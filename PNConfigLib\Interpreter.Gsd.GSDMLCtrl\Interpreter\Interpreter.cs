/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Interpreter.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Globalization;
using System.Collections;
using System.Xml;
using System.IO;
using GSDI;

using C = PNConfigLib.Gsd.Interpreter.Common;
using S = PNConfigLib.Gsd.Interpreter.Structure;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{

    //########################################################################
    #region Enums

    /// <summary>
    /// Specifies the possible assignment options for the
    /// Interpreter.
    /// </summary>
    internal enum AssignmentOptions
    {
        /// <summary>
        /// Is used, if a GSD file is newly assigned to the Interpreter.
        /// </summary>
        New = 0,
        /// <summary>
        /// Is used, if the language of the entries within the data object
        /// model(s) of a assigned GSD file should be changed to another
        /// language.
        /// </summary>
        LanguageChange = 1,
        /// <summary>
        /// Is used, if an other model is wanted for a assigned GSD file.
        /// </summary>
        ModelChange = 2
    }

    #endregion

    /// <summary>
    /// The Interpreter is the class which handels the functionality for 
    /// interpreting a specified GSD(ML) file. This means, that a simple
    /// data object model is created for the specific information within
    /// the GSD file, which can be used, to provide any other with data
    /// from the GSD in a simple manner.
    /// To keep the object model as simple as possible for the different
    /// requirements from the engineering tools, two different data object
    /// models can be created by the assignment of a GSD. The structure
    /// data object model is mainly used to get an overview of the contained
    /// device, the access points, the modules and submodules. The common
    /// data object model is used for the structural information and 
    /// additionally all the specific information contained in the GSD file.
    /// </summary>
    public class Interpreter :
        GSDI.INterpreter,
        GSDI.IInterpreterCommon,
        GSDI.IInterpreterStructure,
        GSDI.IInterpreterStructure2,
        GSDI.IInterpreterStructure3,
        GSDI.IInterpreterInfo
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Interpreter if it is instantiated. 
        /// </summary>
        public Interpreter()
        {
            // Initialize the properties
            SetInterpreterVersion(Constants.s_CurrentInterpreterVersion);
            SetSupportedGsdmlVersion(Constants.s_CurrentGsdmlVersion);
            SetDefaultLanguage(Constants.s_En);

            m_ActualGsdVersion = String.Empty;
            m_ActualGsdPathName = String.Empty;
            m_ActualGsdName = String.Empty;
            m_ActualLanguage = String.Empty;

            m_Model = GSDI.ModelOptions.GSDNoModel;
            m_StructureStore = null;
            m_CommonStore = null;
            m_LanguageStore = null;
        }

        #endregion

        //########################################################################################
        #region Fields

        private string m_InterpreterVersion;
        private string m_SupportedGsdmlVersion;
        private string m_DefaultLanguage;

        private string m_ActualGsdVersion;
        private string m_ActualGsdPathName;
        private string m_ActualGsdName;
        private string m_ActualLanguage;

        private GSDI.ModelOptions m_Model;
        private ModelStore m_StructureStore;
        private ModelStore m_CommonStore;

        private ArrayList m_LanguageStore;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the version of the actual GSD (version of GSDML specification), 
        /// which is assigned to the Interpreter.
        /// </summary>
        /// <value>Version of the GSDML specification used from the actual GSD.</value>
        /// <remarks>If no model is assigned to the Interpreter, an empty string is 
        /// given.</remarks>
        internal virtual string ActualGsdVersion
        {
            get => this.m_ActualGsdVersion;
            set => this.m_ActualGsdVersion = value;
        }
        /// <summary>
        /// Accesses the name of the actual assigned GSD file with complete path.
        /// </summary>
        /// <value>Name of the actual assigned GSD with complete path.</value>
        /// <remarks>If no model is assigned to the Interpreter, an empty string is 
        /// given.</remarks>
        internal virtual string ActualGsdPathName
        {
            get => this.m_ActualGsdPathName;
            set => this.m_ActualGsdPathName = value;
        }
        internal virtual ModelStore StructureStore
        {
            get => this.m_StructureStore;
            set => this.m_StructureStore = value;
        }
        internal virtual ModelStore CommonStore
        {
            get => this.m_CommonStore;
            set => this.m_CommonStore = value;
        }

        internal virtual ArrayList LanguageStore
        {
            get => this.m_LanguageStore;
            set => this.m_LanguageStore = value;
        }

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Sets the version of the Interpreter
        /// </summary>
        /// <param name="version">Version of the Interpreter</param>
        /// <returns></returns>
        protected void SetInterpreterVersion(string version) { m_InterpreterVersion = version; }

        /// <summary>
        /// Sets the latest supported GSDML version
        /// </summary>
        /// <param name="version">Latest supported GSDML version</param>
        /// <returns></returns>
        protected void SetSupportedGsdmlVersion(string version) { m_SupportedGsdmlVersion = version; }

        /// <summary>
        /// Sets the default language
        /// </summary>
        /// <param name="lang">Default language</param>
        /// <returns></returns>
        protected void SetDefaultLanguage(string lang) { m_DefaultLanguage = lang; }

        /// <summary>
        /// Sets the name of the GSD
        /// </summary>
        /// <param name="name">GSD name</param>
        /// <returns></returns>
        protected virtual void SetActualGsdName(string name) { m_ActualGsdName = name; }

        /// <summary>
        /// Sets the current language
        /// </summary>
        /// <param name="lang">current language</param>
        /// <returns></returns>
        protected virtual void SetActualLanguage(string lang) { m_ActualLanguage = lang; }

        /// <summary>
        /// Sets the avaialble model(s)
        /// </summary>
        /// <param name="model">Available models</param>
        /// <returns></returns>
        virtual protected void SetAvailableModel(GSDI.ModelOptions model) { m_Model = model; }

        /// <summary>
        /// Checks whether the specified model is available.
        /// </summary>
        /// <param name="modeloption">Specifies the model, which should be checked for
        /// availability.</param>
        /// <returns>True if the model is available, false if not.</returns>
        protected virtual bool CheckModelAvailability(GSDI.ModelOptions modeloption)
        {
            // Check what model option is given (Structure or Common possible).
            if (modeloption == GSDI.ModelOptions.GSDStructure)
            {
                // Check available interpreter model option.
                if (!(AvailableModel == GSDI.ModelOptions.GSDStructure) &&
                      !(AvailableModel == GSDI.ModelOptions.GSDCommonAndStructure))
                {
                    return false;	// ---------->
                }

                // Check store.
                if (null == StructureStore)
                {
                    return false;	// ---------->
                }

                // Conditions satisfy.
                return true;	// ---------->
            }
            else if (modeloption == GSDI.ModelOptions.GSDCommon)
            {
                // Check available interpreter model option.
                if (!(AvailableModel == GSDI.ModelOptions.GSDCommon) &&
                      !(AvailableModel == GSDI.ModelOptions.GSDCommonAndStructure))
                {
                    return false;	// ---------->
                }

                // Check store.
                if (null == CommonStore)
                {
                    return false;	// ---------->
                }

                // Conditions satisfy.
                return true;	// ---------->
            }

            return false;
        }

        /// <summary>
        /// This method makes the real work for the public export function(s) of the
        /// interpreter.
        /// </summary>
        /// <param name="lModelOption">Specifies, which model(s) should be exported. 
        /// Only the matching of loaded and specified model(s) are exported.</param>
        /// <param name="writer">XML writer, which is used from the underlying
        /// objects to serialize the data.</param>
        /// <returns>True if the export was successful, false if not.</returns>
        private bool InternalExport(ModelOptions lModelOption, ref XmlTextWriter writer)
        {
            bool succeeded = true;

            try
            {
                // Object begin.
                writer.WriteStartElement(Gsd.Interpreter.Export.s_ElementDevice);

                // Serialize the necessary "store" objects.
                if (lModelOption == ModelOptions.GSDCommonAndStructure ||
                    lModelOption == ModelOptions.GSDStructure)
                {
                    // Check whether structure model is available.
                    if (null != StructureStore)
                    {
                        // Try to serialize structure object model.
                        succeeded = StructureStore.Serialize(ref writer);
                        if (!succeeded)
                            throw new SerializationException("Serialization of Structure data object model fails!");
                    }
                }
                //
                if (lModelOption == ModelOptions.GSDCommonAndStructure ||
                    lModelOption == ModelOptions.GSDCommon)
                {
                    // Check whether common model is available.
                    if (null != CommonStore)
                    {
                        // Try to serialize common object model.
                        succeeded = CommonStore.Serialize(ref writer);
                        if (!succeeded)
                            throw new SerializationException("Serialization of Common data object model fails!");
                    }
                }

                // Object end.
                writer.WriteEndElement();
            }
            catch (SerializationException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        /// <summary>
        /// This method makes the real work for the public assign function(s) of the
        /// interpreter.
        /// </summary>
        /// <param name="bstrPathName">The name of the GSD, which should be assigned.</param>
        /// <param name="lModelOption">Specifies, which model(s) should be created. 
        /// Only the matching of loaded and specified model(s) are created.</param>
        /// <param name="bstrLanguage">Specifies the language, which should be extracted
        /// from the GSD. If this language isn't available from the given GSD, the default
        /// language is used instead.</param>
        /// <param name="lAssOption">Specifies whether the assignment is new, or there is
        /// a model change or a language change.</param>
        /// <returns>True if the assignment was successful, false if not.</returns>
        private bool InternalAssignment(string bstrPathName, GSDI.ModelOptions lModelOption, string bstrLanguage, bool bCheckGsd, AssignmentOptions lAssOption)
        {
            bool succeeded = true;

            string pathname = bstrPathName;
            string language = bstrLanguage;
            bool check = bCheckGsd;
            ModelOptions model = lModelOption;
            ModelOptions creationmodel = lModelOption;
            AssignmentOptions option = lAssOption;
            ModelStore structurestore = null;
            ModelStore commonstore = null;
            FileStream filestream = null;

            try
            {
                // Check model option.
                if (model == ModelOptions.GSDNoModel)
                    throw new ArgumentException("Can't create model: '" + model + "'");

                // Check language string.
                string lang = string.Empty;
                if (!String.IsNullOrEmpty(language))
                    lang = language.Substring(0, 2).ToUpperInvariant().ToLower(CultureInfo.CurrentCulture);
                if (!(lang == Constants.s_De || lang == Constants.s_En || lang == Constants.s_Fr ||
                    lang == Constants.s_It || lang == Constants.s_Es || lang == Constants.s_Ja || lang == Constants.s_Ch))
                {
                }

                language = lang;

                // Search and get the GSD(ML) file!
                string gsdname;
                string gsdpath;
                if (File.Exists(pathname))
                {
                    FileInfo fileinfo = new FileInfo(pathname);
                    gsdname = fileinfo.Name;
                    gsdpath = fileinfo.DirectoryName;

                    filestream = new FileStream(pathname, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                }
                else
                    throw new ArgumentException("Can't find Gsd file with the given path: '" + pathname + "'!");

                // Check for GSDML file name (e.g. "GSDML-V1.0-Siemens-ET200S-20040128.xml")
                if (!Help.s_RegularExpressionGsdmlName.IsMatch(gsdname))
                    throw new ArgumentException("Name of GSDML does not match the claimed naming convention. E.g. 'GSDML-Vx.y-vendorname-modelname-jjjjmmdd.xml'");

                // Check whether Gsd(Version) of actual GSD file is supported!
                // NOTE: Example Gsd name = 'GSDML-V1.0-Siemens-PNIO_Example-20030708.xml'
                string s = gsdname.Substring(gsdname.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal) + 1);
                string version = (s.Substring(0, s.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal))).ToUpperInvariant();

                Stream docstream = filestream;

                // If specified check GSD.
                if (check)
                {
                    // Instantiate checker and check gsd!
                    Checker.Checker checker = new();

                    //succeeded = checker.CheckGsd(pathname);
                    FileStream fs = new(pathname, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    succeeded = checker.CheckGsd(fs, gsdname, gsdpath, version);
                    if (!succeeded)
                        throw new InterpreterException("Check of Gsd '" + pathname + "' didn't succeed!");
                    //docstream.Position = 0;
                }

                // Get needed internal builder object.
                BuilderObject builder = GetInternalBuilder(version);
                if (null == builder)
                    throw new InterpreterException("Couldn't get needed internal builder object for Gsd version: '" + version + "'");

                ChangeModelStore(option, model, ref structurestore, ref creationmodel, ref commonstore);

                ArrayList languageStore = this.LanguageStore;

                // Build specified model.
                if (creationmodel != ModelOptions.GSDNoModel)
                {
                    //					builder.SetCompatibilityCache(compatibilitycache);
                    succeeded = builder.BuildModels(docstream, pathname, creationmodel, language, ref structurestore, ref commonstore, ref languageStore);
                }

                // Check result.
                if (!succeeded)
                    throw new InterpreterException("Interpreter couldn't create model(s): '" + creationmodel + "'!");


                // If building succeeds, assign internal variables.
                this.SetActualGsdName(gsdname);
                this.SetActualLanguage(language);
                this.SetAvailableModel(model);

                this.ActualGsdVersion = version;
                this.ActualGsdPathName = pathname;

                this.StructureStore = structurestore;
                this.CommonStore = commonstore;
                this.LanguageStore = languageStore;

            }
            catch (Exception e)
            {
                throw new InterpreterException("Could not interprete GSD file", e);
            }
            finally
            {
                if (null != filestream)
                    filestream.Close();
            }
            return true;
        }

        private void ChangeModelStore(
            AssignmentOptions option,
            ModelOptions model,
            ref ModelStore structurestore,
            ref ModelOptions creationmodel,
            ref ModelStore commonstore)
        {
            // ------------------------------------------------------------------------
            // NOTE: Special handling for model change and just available models.
            //       (S = Structure, C = Common)
            //
            // Origin (S): -------------------------------------------
            // Needed (S)         - return imediately without doing something!
            // Needed (C)         - SStore = null
            //                    - CreationModel = Common
            // Needed (C + S)     - SStore = Origin SStore
            //                    - CreationModel = Common
            //
            // Origin (C): -------------------------------------------
            // Needed (S)         - CStore = null
            //                    - CreationModel = Structure
            // Needed (C)         - return imediately without doing something!
            // Needed (C + S)     - CStore = Origin CStore
            //                    - CreationModel = Structure
            //
            // Origin (C + S): ---------------------------------------
            // Needed (S)         - CStore = null
            //                    - CreationModel = NoModel
            //                    - SStore = Origin SStore
            // Needed (C)         - SStore = null
            //                    - CreationModel = NoModel
            //                    - CStore = Origin CStore
            // Needed (C + S)     - return imediately without doing something!
            //
            // ------------------------------------------------------------------------
            if (option == AssignmentOptions.ModelChange)
                {
                    switch (AvailableModel)
                    {
                        case ModelOptions.GSDStructure:
                            {
                                // Settings for CommonAndStructure model.
                                if (model == GSDI.ModelOptions.GSDCommonAndStructure)
                                {
                                    structurestore = StructureStore;
                                    creationmodel = GSDI.ModelOptions.GSDCommon;
                                }

                                break;
                            }
                        case ModelOptions.GSDCommon:
                            {
                                // Settings for CommonAndStructure model.
                                if (model == GSDI.ModelOptions.GSDCommonAndStructure)
                                {
                                    commonstore = CommonStore;
                                    creationmodel = GSDI.ModelOptions.GSDStructure;
                                }

                                break;
                            }
                        case ModelOptions.GSDCommonAndStructure:
                            {
                                // Settings for Structure model.
                                if (model == GSDI.ModelOptions.GSDStructure)
                                {
                                    structurestore = StructureStore;
                                    creationmodel = GSDI.ModelOptions.GSDNoModel;
                                }
                                // Settings for Common model.
                                if (model == GSDI.ModelOptions.GSDCommon)
                                {
                                    commonstore = CommonStore;
                                    creationmodel = GSDI.ModelOptions.GSDNoModel;
                                }

                                break;
                            }
                    }
                }
                
        }

        /// <summary>
        /// Accesses the correct internal builder object for the specified version.
        /// </summary>
        /// <param name="version">The version for which the builder should be getted.</param>
        /// <returns>The internal builder object, which is the correct for the
        /// specified version.</returns>
        private BuilderObject GetInternalBuilder(string version)
        {

            try
            {

                // BUILDER VERSION "Vx.x" -----------------------------
                //builderversion = "Vx.x";
                // NOTE: Place for adding queries for newer versions!!!
                // BUILDER VERSION "V2.45" -----------------------------
                string builderversion = Constants.s_Version245;
                BuilderObject builder;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02045();

                    return builder; // ------------>
                }
                // BUILDER VERSION "V2.44" -----------------------------
                builderversion = Constants.s_Version244;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02044();

                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.43" -----------------------------
                builderversion = Constants.s_Version243;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02043();

                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.42" -----------------------------
                builderversion = Constants.s_Version242;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02042();

                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.41" -----------------------------
                builderversion = Constants.s_Version241;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02041();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.4" -----------------------------
                builderversion = Constants.s_Version24;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0204();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.35" -----------------------------
                builderversion = Constants.s_Version235;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02035();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.34" -----------------------------
                builderversion = Constants.s_Version234;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02034();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.33" -----------------------------
                builderversion = Constants.s_Version233;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02033();
                    return builder; // ------------>
                }


                // BUILDER VERSION "V2.32" -----------------------------
                builderversion = Constants.s_Version232;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02032();
                    return builder; // ------------>
                }


                // BUILDER VERSION "V2.31" -----------------------------
                builderversion = Constants.s_Version231;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02031();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.3" -----------------------------
                builderversion = Constants.s_Version23;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0203();
                    return builder; // ------------>
                }


                // BUILDER VERSION "V2.25" -----------------------------
                builderversion = Constants.s_Version225;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV02025();
                    return builder; // ------------>
                }


                // BUILDER VERSION "V2.2" -----------------------------
                builderversion = Constants.s_Version22;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0202();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.1" -----------------------------
                builderversion = Constants.s_Version21;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0201();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V2.0" -----------------------------
                builderversion = Constants.s_Version20;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0200();
                    return builder; // ------------>
                }

                // BUILDER VERSION "V1.0" -----------------------------
                builderversion = Constants.s_Version10;
                if (Help.IsHigherOrEqualVersion(version, builderversion))
                {
                    builder = new BuilderV0100();
                    return builder; // ------------>
                }

            }
            catch (ArgumentException)
            {
                return null;
            }

            return null;

        }


        #endregion


        //########################################################################################
        #region IInterpreter Members

        /// <summary>
        /// Accesses the name of the actual assigned GSD file. Only the name without 
        /// path is given.
        /// </summary>
        /// <value>Name of the actual assigned GSD.</value>
        /// <remarks>If no model is assigned to the Interpreter, an empty string is 
        /// given.</remarks>
        public string ActualGsdName => m_ActualGsdName;

        /// <summary>
        /// Accesses the actual setted language of the Interpreter object model(s).
        /// </summary>
        /// <value>Language actually used for the GSD. </value>
        /// <remarks>If no model is assigned to the Interpreter, an empty string is 
        /// given.</remarks>
        public string ActualLanguage => m_ActualLanguage;

        /// <summary>
        /// Accesses the default language adjusted by the Interpreter.
        /// </summary>
        /// <value>Language used from the Interpreter if needed language is
        /// unavailable.</value>
        /// <remarks>The default language is fixed set to english, because this
        /// language is always available from the GSD (required primary language
        /// of the GSD). If the Interpreter is invited to create the data object
        /// model(s) with a language, which is not available, the default language
        /// is used instead. The default language entry for a text is also used,
        /// if this text isn't available for the required language.</remarks>
        public string DefaultLanguage => m_DefaultLanguage;

        /// <summary>
        /// Accesses the actual available model, loaded by the Interpreter.
        /// </summary>
        /// <value>Model actually created for the GSD.</value>
        public GSDI.ModelOptions AvailableModel => m_Model;


        /// <summary>
        /// Assigns the specified GSD to the Interpreter. This means, that it tries to
        /// ctreat the data object model(s) for the GSD.
        /// It is possible to create the common or(and) the structure data object model.
        /// </summary>
        /// <remarks>The structure model is a simple object model, which contains
        /// general information about the access points, modules and submoduls.
        /// Whereas the common model contains objects for each facet of information
        /// available from a GSD file.</remarks>
        /// <param name="bstrPathName">The name of the GSD, which should be assigned.</param>
        /// <param name="lModelOption">Specifies which model(s) should be created.</param>
        /// <param name="bstrLanguage">Specifies the language, which should be extracted
        /// from the GSD. If this language isn't available from the given GSD, the default
        /// language is used instead.</param>
        /// <returns>True if the assignment was successful, false if not.</returns>
        public bool AssignGsd(string bstrPathName, GSDI.ModelOptions lModelOption, string bstrLanguage, bool bCheckGsd)
        {
            bool succeeded;

            try
            {
                // Call internal assignment method.
                succeeded = InternalAssignment(bstrPathName, lModelOption, bstrLanguage, bCheckGsd, AssignmentOptions.New);
            }
            catch (InterpreterException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Deletes the actual object model(s) and create it new with the specified 
        /// language.
        /// </summary>
        /// <param name="bstrLanguage">Specifies the language, which should be extracted
        /// from the GSD. If this language isn't available from the given GSD, the default
        /// language is used instead.</param>
        /// <returns>True if the assignment with the new language was successful,
        /// false if not.</returns>
        public bool ChangeLanguage(string bstrLanguage)
        {
            bool succeeded;

            try
            {
                // Call internal assignment method.
                succeeded = this.InternalAssignment(this.ActualGsdPathName, this.AvailableModel, bstrLanguage, false, AssignmentOptions.LanguageChange);
           }
            catch (InterpreterException)
            {
                succeeded = false;
            }
            catch (ArgumentException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Changes the current model of the Interpreter to the given model. This means,
        /// that if a model is spcified and not loaded at the moment, it will be loaded
        /// and if a model is not specifed and loaded, it will be deleted.
        /// </summary>
        /// <param name="lModelOption">The model to which the Interpreter should be changed.</param>
        /// <returns>True if the assignment to the specified model(s) was successful,
        /// false if not.</returns>
        public bool ChangeModel(GSDI.ModelOptions lModelOption)
        {
            bool succeeded;

            try
            {
                // If model is the same as available, return with true.
                if (this.AvailableModel == lModelOption)
                    return true;

                // Call internal assignment method.
                succeeded = this.InternalAssignment(this.ActualGsdPathName, lModelOption, this.ActualLanguage, false, AssignmentOptions.ModelChange);
                
            }
            catch (InterpreterException)
            {
                succeeded = false;
            }
            
            return succeeded;
        }

        /// <summary>
		/// Serializes the specified data object model(s) to a defined XML format and
		/// save the serialization to the file.
		/// </summary>
		/// <param name="bstrPathName">The file writing the export result to.</param>
		/// <param name="lModelOption">Specifies, which model(s) should be exported. 
		/// Only the matching of loaded and specified model(s) are exported.</param>
		/// <returns>True if the export was successful, false if not.</returns>
		public bool ExportToFile(string bstrPathName, ModelOptions lModelOption)
        {
            bool succeeded = true;
            XmlTextWriter writer = null;

            try
            {
                // Check whether file exists.
                //if (System.IO.File.Exists(bstrPathName))
                //throw new ArgumentException("Couldn't export to existing file '" + bstrPathName + "'!", "bstrPathName");

                // Create xml writer object and serialize the stores to file.
                // TODO: evtl. other encoding as 'UTF-8'
                writer = new XmlTextWriter(bstrPathName, null);

                // Set formatting options.
                if (Settings.Indentation)
                {
                    writer.Formatting = Formatting.Indented;
                    writer.Indentation = Gsd.Interpreter.Export.s_Indentation;
                    writer.IndentChar = Gsd.Interpreter.Export.s_IndentationCharacter;
                }

                // Start a new xml document.
                writer.WriteStartDocument();
                writer.WriteStartElement(Gsd.Interpreter.Export.s_ElementExport);

                // Call internal serialize.
                succeeded = this.InternalExport(lModelOption, ref writer);
                if (!succeeded)
                    throw new SerializationException("Couldn't export model: '" + lModelOption + "'!");

                writer.WriteEndElement();
                writer.WriteEndDocument();

                writer.Flush();
            }
            catch (ArgumentException e)
            {
                succeeded = false;
            }
            catch (SerializationException e)
            {
                succeeded = false;
            }
            catch (IOException e)
            {
                succeeded = false;
            }
            catch (InvalidOperationException e)
            {
                succeeded = false;
            }
            catch (System.Security.SecurityException e)
            {
                succeeded = false;
            }
            catch (UnauthorizedAccessException e)
            {
                succeeded = false;
            }
            finally
            {
                // Close assigned file.
                if (null != writer)
                {
                    writer.Close();
                    if (!succeeded)
                        File.Delete(bstrPathName);
                }
            }

            return succeeded;
        }


        /// <summary>
        /// Serializes the specified data object model(s) to a defined XML format and
        /// save the serialization to the file.
        /// </summary>
        /// <param name="lModelOption">Specifies, which model(s) should be exported. 
        /// Only the matching of loaded and specified model(s) are exported.</param>
        /// <returns>The serialized XML string if export was successful, an empty
        /// string if not.</returns>
        public string Export(GSDI.ModelOptions lModelOption)
        {
            XmlTextWriter writer = null;
            System.IO.StreamReader reader = null;

            try
            {
                // Create xml writer object and serialize the stores to file.
                // TODO: evtl. other encoding as 'UTF-8'
                MemoryStream mstream = new MemoryStream();
                BufferedStream bstream = new BufferedStream(mstream, 40000);
                writer = new XmlTextWriter(bstream, null);

                // Set formatting options.
                if (Settings.Indentation)
                {
                    writer.Formatting = Formatting.Indented;
                    writer.Indentation = Gsd.Interpreter.Export.s_Indentation;
                    writer.IndentChar = Gsd.Interpreter.Export.s_IndentationCharacter;
                }

                // Start a new xml document.
                writer.WriteStartDocument();
                writer.WriteStartElement(Gsd.Interpreter.Export.s_ElementExport);

                // Call internal serialize.
                bool succeeded = this.InternalExport(lModelOption, ref writer);
                if (!succeeded)
                    throw new SerializationException("Couldn't export model: '" + lModelOption + "'!");

                writer.WriteEndElement();
                writer.WriteEndDocument();

                writer.Flush();

                // Read stream and write to string.
                reader = new StreamReader(writer.BaseStream);
                reader.BaseStream.Position = 0;
                return reader.ReadToEnd();

            }
            catch (InvalidOperationException)
            {
            }
            catch (IOException)
            {
            }
            catch (OutOfMemoryException)
            {
            }
            finally
            {
                // Close assigned memory.
                if (null != writer)
                    writer.Close();
                writer = null;

                if (null != reader)
                    reader.Close();
                reader = null;
            }

            return String.Empty;
        }


        #endregion

        //########################################################################################
        #region IInterpreterCommon Members

        /// <summary>
        /// Gets the Device object, which is the root of the common data object model.
        /// </summary>
        /// <returns>The Device object of the common data object model, null if
        /// not available.</returns>
        /// <remarks>The methods of the IInterpreterCommon interface are only
        /// useful if a GSD is assigned to the Interpreter and the common data
        /// object model is wanted by the assignment.</remarks>
        public C.GsdDevice GetDevice()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
                return (C.GsdDevice)this.CommonStore.Device;

            return null;

        }

        /// <summary>
        /// Gets the DeviceInfo object of the common data object model, which
        /// contains information about the device described with this GSD file.
        /// </summary>
        /// <returns>The DeviceInfo object of the common data object model, null 
        /// if not available.</returns>
        public C.DeviceInfo GetDeviceInfo()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == this.CommonStore.Device)
                {
                    return null;
                }

                return ((C.GsdDevice)this.CommonStore.Device).Info;
            }

            return null;
        }


        /// <summary>
        /// Gets a list of device access points, available from the assigned
        /// GSD file.
        /// At least one device access point is a must for each GSD file.
        /// </summary>
        /// <returns>A list of DeviceAccessPoint objects, null if no DAP is
        /// available.</returns>
        public Array GetDeviceAccessPoints()
        {
            if (CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)(CommonStore.Device)).DeviceAccessPoints;
            }

            return null;
        }

        /// <summary>
        /// Gets the device access point specified with the given GSD ID.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the wanted device access point.</param>
        /// <returns>The DeviceAccessPoint object with the specified GSD ID, null
        /// if it isn't available.</returns>
        public C.DeviceAccessPoint GetDeviceAccessPoint(string sGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.DeviceAccessPoints.ContainsKey(sGsdID))
                {
                    return null;
                }

                return (C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[sGsdID];
            }

            //SITrace.Write(TraceCategories.Warning, "DeviceInterfaceModule is unavailable!", SITrace.tgcc);
            return null;
        }


        /// <summary>
        /// Gets a list of all modules, available from the whole GSD.
        /// </summary>
        /// <returns>A list of Module objects, null if no Module is available.</returns>
        public Array GetAllModules()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == this.CommonStore.Modules)
                {
                    return null;
                }

                var array = new C.GsdObject[this.CommonStore.Modules.Values.Count];
                this.CommonStore.Modules.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of modules, supported from the specified access point.
        /// </summary>
        /// <param name="bstrAPGsdID">The GSD ID of the access point, for which the
        /// modules are wanted.</param>
        /// <returns>A list of Module objects, null if no Module is available.</returns>
        public Array GetModules(string bstrAPGsdID)
        {
            if (CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.DeviceAccessPoints.ContainsKey(bstrAPGsdID))
                {
                    return null;
                }

                return ((C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[bstrAPGsdID]).Modules;
            }

            //SITrace.Write(TraceCategories.Warning, "Modules are unavailable!", SITrace.tgcc);
            return null;
        }

        /// <summary>
        /// Gets the module specified with the given GSD ID.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the wanted module.</param>
        /// <returns>The Module object with the specified GSD ID, null if it
        /// isn't available.</returns>
        public C.Module GetModule(string sGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.Modules.ContainsKey(sGsdID))
                {
                    return null;
                }

                return (C.Module)this.CommonStore.Modules[sGsdID];
            }

            //SITrace.Write(TraceCategories.Warning, "Module is unavailable!", SITrace.tgcc);
            return null;
        }

        /// <summary>
        /// Gets the communication interfaces of the specified device access point.
        /// </summary>
        /// <param name="bstrDAGsdID">The GSD ID of the device access point for which the communication 
        /// interface should be returned.</param>
        /// <returns>A list of CommunicationInterface objects. Null if no parameter
        /// record data is available.</returns>
        public Array GetCommunicationInterfaces(string bstrDAGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                // CommunicationInterfaces from DeviceAccessPoint.
                if (this.CommonStore.DeviceAccessPoints.ContainsKey(bstrDAGsdID))
                {
                    return ((C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[bstrDAGsdID]).CommunicationInterfaces;
                }
                else
                {
                    return null;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the communication interface with the specified GSD ID.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the communication interface for which 
        /// should be returned.</param>
        /// <returns>A CommunicationInterface object. Null if no
        /// communication interface is available.</returns>
        public C.CommunicationInterface GetCommunicationInterfaceItem(string bstrGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.CommunicationInterfaceItems.ContainsKey(bstrGsdID))
                {
                    return null;
                }
                return ((C.CommunicationInterface)this.CommonStore.CommunicationInterfaceItems[bstrGsdID]);
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all communication interfaces, available from the whole GSD.
        /// </summary>
        /// <returns>A list of CommunicationInterface objects, null if no CIM is available.</returns>
        public Array GetAllCommunicationInterfaces()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == this.CommonStore.CommunicationInterfaceItems)
                {
                    return null;
                }

                var array = new C.GsdObject[this.CommonStore.CommunicationInterfaceItems.Values.Count];
                this.CommonStore.CommunicationInterfaceItems.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all virtual submodules, available from the whole GSD.
        /// </summary>
        /// <returns>A list of VirtualSubmodule objects, null if no VSM is available.</returns>
        public Array GetAllVirtualSubmodules()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == this.CommonStore.VirtualSubmodules)
                {
                    return null;
                }

                var array = new C.GsdObject[CommonStore.VirtualSubmodules.Values.Count];
                this.CommonStore.VirtualSubmodules.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of virtual submodules, supported from the specified module with
        /// the given type.
        /// Because the device access point is in principle also a module, it can also
        /// contain virtual submodules and must be considered from this method.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the module (normal module or device access
        /// point), for which the virtual submodules are wanted.</param>
        /// <param name="lModuleType">The type of the module, which is qualified with
        /// the GSD ID. Only device access point or module is allowed as type.</param>
        /// <returns>A list of VirtualSubmodule objects, null if no VSM is available.</returns>
        public Array GetVirtualSubmodules(string bstrGsdID, ModuleTypes lModuleType)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                // NOTE: Only DeviceInterfaceModule or Module GsdID is allowed!!!
                switch (lModuleType)
                {
                    case ModuleTypes.GSDDeviceAccessPoint:
                        {
                            if (this.CommonStore.DeviceAccessPoints.ContainsKey(bstrGsdID))
                                return ((C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[bstrGsdID]).VirtualSubmodules;
                            break;
                        }
                    case ModuleTypes.GSDModule:
                        {
                            if (this.CommonStore.Modules.ContainsKey(bstrGsdID))
                                return ((C.Module)this.CommonStore.Modules[bstrGsdID]).VirtualSubmodules;
                            break;
                        }
                    case ModuleTypes.GSDVirtualSubmodule:
                    default:
                        {
                            break;
                        }
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the virtual submodule specified with the given GSD ID.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the wanted virtual submodule.</param>
        /// <returns>The VirtualSubmodule object with the specified GSD ID, null if 
        /// it isn't available.</returns>
        public C.VirtualSubmodule GetVirtualSubmodule(string sGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.VirtualSubmodules.ContainsKey(sGsdID))
                {
                    return null;
                }

                return (C.VirtualSubmodule)this.CommonStore.VirtualSubmodules[sGsdID];
            }

            //SITrace.Write(TraceCategories.Warning, "VirtualSubmodule is unavailable!", SITrace.tgcc);
            return null;
        }
 
        /// <summary>
        /// Gets a list of all virtual submodules, available from the whole GSD.
        /// </summary>
        /// <returns>A list of VirtualSubmodule objects, null if no VSM is available.</returns>
        public Array GetAllPluggablePorts()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == this.CommonStore.PluggablePortsubmodules)
                {
                    return null;
                }

                var array = new C.GsdObject[this.CommonStore.PluggablePortsubmodules.Values.Count];
                this.CommonStore.PluggablePortsubmodules.Values.CopyTo(array, 0);

                return array;
            }

            return null;

        }

        /// <summary>
        /// Gets the virtual submodule specified with the given GSD ID.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the wanted virtual submodule.</param>
        /// <returns>The VirtualSubmodule object with the specified GSD ID, null if 
        /// it isn't available.</returns>
        public C.PortSubmodule GetPluggablePort(string sGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.PluggablePortsubmodules.ContainsKey(sGsdID))
                {
                    return null;
                }

                return (C.PortSubmodule)this.CommonStore.PluggablePortsubmodules[sGsdID];
            }

            return null;
        }

        /// <summary>
        /// Gets the module info, which contains general information about a specific
        /// module.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the module (normal module, device access
        /// point or virtual submodule), for which the module info is wanted.</param>
        /// <param name="lModuleType">The type of the module, which is qualified with
        /// the GSD ID.</param>
        /// <returns>The ModuleInfo object with the specified GSD ID, null if 
        /// it isn't available.</returns>
        public C.ModuleInfo GetModuleInfo(string sGsdID, GSDI.ModuleTypes lModuleType)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                // NOTE: Only DeviceInterfaceModule or Module GsdID is allowed!!!
                switch (lModuleType)
                {
                    case ModuleTypes.GSDDeviceAccessPoint:
                        {
                            if (this.CommonStore.DeviceAccessPoints.ContainsKey(sGsdID))
                                return ((C.ModuleObject)this.CommonStore.DeviceAccessPoints[sGsdID]).Info;
                            break;
                        }
                    case ModuleTypes.GSDModule:
                        {
                            if (this.CommonStore.Modules.ContainsKey(sGsdID))
                                return ((C.ModuleObject)this.CommonStore.Modules[sGsdID]).Info;
                            break;
                        }
                    case ModuleTypes.GSDVirtualSubmodule:
                        {
                            if (this.CommonStore.VirtualSubmodules.ContainsKey(sGsdID))
                                return ((C.ModuleObject)this.CommonStore.VirtualSubmodules[sGsdID]).Info;
                            break;
                        }
                    default:
                        {
                            break;
                        }
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the module plug data for the specified module. Because module plug
        /// data is available for the device access point itself and also for the 
        /// modules contained in the device access point, the GSD ID of the device
        /// access point must be always specified, whereas the GSD ID of the module
        /// can be empty.
        /// If the GSD ID of the module parameter is empty, the plug data of the 
        /// specified device access point itself is returned. If module GSD ID
        /// is given, the plug data for the specified module by the specified device
        /// access point is returned.
        /// </summary>
        /// <param name="sDAPGsdID">The GSD ID of the access point looking for module 
        /// plug data.</param>
        /// <param name="sMGsdID">The GSD ID of the module, for which the module plug 
        /// data should be returned.</param>
        /// <returns>The ModulePlugData object, which specifies the plug rules for a
        /// module or device access point, null if no plug rules are available.</returns>
        public C.ModulePlugData GetModulePlugData(string sDAPGsdID, string sMGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.DeviceAccessPoints.ContainsKey(sDAPGsdID))
                {
                    return null;
                }

                // If MGsdID is empty --> ModulePlugData from AccessPoint must be returned
                object o = this.CommonStore.DeviceAccessPoints[sDAPGsdID];
                if (String.IsNullOrEmpty(sMGsdID))
                {
                    return ((C.DeviceAccessPoint)o).PlugData;
                }
                // Else --> ModulePlugData from Module with MGsdID must be returned
                else
                {
                    if (!((C.DeviceAccessPoint)o).ModulePlugDataDictionary.Contains(sMGsdID))
                    {
                        return null;
                    }

                    return (C.ModulePlugData)((C.DeviceAccessPoint)o).ModulePlugDataDictionary[sMGsdID];
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the input and output configuration data of the specified device access 
        /// point.
        /// </summary>
        /// <param name="sDAPGsdID">The GSD ID of the access point for which the 
        /// input and output configuration data object should be returned.</param>
        /// <returns>The IOConfigData object of the device access point with the 
        /// specified GSD ID, null if it isn't available.</returns>
        public C.IOConfigData GetIOConfigData(string sDAPGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.DeviceAccessPoints.ContainsKey(sDAPGsdID))
                {
                    return null;
                }

                return ((C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[sDAPGsdID]).IOConfigData;
            }

            return null;
        }

        /// <summary>
        /// Gets the parameter record data of the specified submodule.
        /// </summary>
        /// <param name="bstrSMGsdID">The GSD ID of the submodule for which the record 
        /// data should be returned.</param>
        /// <returns>A list of ParameterRecordData objects. Null if no parameter
        /// record data is available.</returns>
        public Array GetParameterRecordData(string bstrSMGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                // ParameterRecordData from VirtualSubmodule.
                if (!this.CommonStore.VirtualSubmodules.ContainsKey(bstrSMGsdID))
                {
                    return null;
                }
                return ((C.VirtualSubmodule)this.CommonStore.VirtualSubmodules[bstrSMGsdID]).ParameterRecordData;
            }

            return null;
        }

        /// <summary>
        /// Gets the parameter record data for the specified GSD ID.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the parameter record data which
        /// should be returned.</param>
        /// <returns>A ParameterRecordData object. Null if no parameter
        /// record data is available.</returns>
        public C.ParameterRecordData GetParameterRecordDataItem(string bstrGsdID)
        {
            if (CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!CommonStore.RecordDataItems.ContainsKey(bstrGsdID))
                {
                    return null;
                }
                return ((C.ParameterRecordData)CommonStore.RecordDataItems[bstrGsdID]);
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all RecordDataItems (ApplicationProcess), available from the whole GSD.
        /// </summary>
        /// <returns>A list of RecordDataItems objects, null if no RecordDataItem is available.</returns>
        public Array GetAllParameterRecordDataItems()
        {
            if (CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (null == CommonStore.RecordDataItems)
                {
                    return null;
                }

                Array array = Array.CreateInstance(typeof(object), this.CommonStore.RecordDataItems.Count);
                this.CommonStore.RecordDataItems.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets the input and output data of the specified submodule.
        /// </summary>
        /// <param name="sSMGsdID">The GSD ID of the submodule for which the 
        /// input and output data should be returned.</param>
        /// <returns>The IOData object of the submodule with the given GSD ID. 
        /// Null if no IO data is available.</returns>
        public C.IOData GetIOData(string sSMGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (!this.CommonStore.VirtualSubmodules.ContainsKey(sSMGsdID))
                {
                    return null;
                }
                return ((C.VirtualSubmodule)this.CommonStore.VirtualSubmodules[sSMGsdID]).IOData;
            }

            return null;
        }

        /// <summary>
        /// Gets the channel diagnostic information, available for a special error type.
        /// </summary>
        /// <param name="vErrorType">The error type number for which the channel diagnostic 
        /// should be returned.</param>
        /// <returns>The ChannelDiagnostic object available with this error type.
        /// Null if no channel diagnostic information is available.</returns>
        public C.ChannelDiagnostic GetChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.ChannelDiagnostic CD in ((C.GsdDevice)this.CommonStore.Device).ChannelDiagnostics)
                {
                    if (CD.ErrorType == vErrorType)
                        return CD;
                }
             }

            return null;
        }

        /// <summary>
        /// Gets the available channel diagnostic entries.
        /// </summary>
        /// <returns>A list of all available ChannelDiagnostic objects.
        /// Null if no channel diagnostic information are available.</returns>
        public Array GetChannelDiagnostics()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).ChannelDiagnostics;
            }

            return null;
        }

        /// <summary>
        /// Gets the profile channel diagnostic information, available for a special error type.
        /// </summary>
        /// <param name="vErrorType">The error type number for which the profile channel diagnostic 
        /// should be returned.</param>
        /// <returns>The ProfileChannelDiagnostic object available with this error type.
        /// Null if no profile channel diagnostic information is available.</returns>
        public C.ProfileChannelDiagnostic GetProfileChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.ProfileChannelDiagnostic CD in ((C.GsdDevice)this.CommonStore.Device).ProfileChannelDiagnostics)
                {
                    if (CD.ErrorType == vErrorType)
                        return CD;
                }
            }
            return null;
        }

        /// <summary>
        /// Gets the available profile channel diagnostic entries.
        /// </summary>
        /// <returns>A list of all available ProfileChannelDiagnostic objects.
        /// Null if no profile channel diagnostic information are available.</returns>
        public Array GetProfileChannelDiagnostics()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).ProfileChannelDiagnostics;
            }
            return null;
        }

        /// <summary>
        /// Gets the unit diagnostic type information available for this user structure
        /// identifier.
        /// </summary>
        /// <param name="vUserStructureIdentifier">The diagnostic type number for which 
        /// the unit diagnostic type should be returned.</param>
        /// <returns>The UnitDiagnosticType object available with this diagnostic type.
        /// Null if no unit diagnostic type is available.</returns>
        public C.UnitDiagnosticType GetUnitDiagnosticType(System.UInt32 vUserStructureIdentifier)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).GetUnitDiagnosticType(vUserStructureIdentifier);
            }

            return null;
        }

        /// <summary>
        /// Gets the available unit diagnostic type entries.
        /// </summary>
        /// <returns>A list of all available UnitDiagnosticType objects.
        /// Null if no unit diagnostic types are available.</returns>
        public Array GetUnitDiagnosticTypes()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).UnitDiagnosticTypes;
            }

            return null;
        }

        /// <summary>
        /// Gets the profile channel diagnostic information, available for a special error type.
        /// </summary>
        /// <param name="vErrorType">The error type number for which the profile channel diagnostic 
        /// should be returned.</param>
        /// <returns>The ProfileChannelDiagnostic object available with this error type.
        /// Null if no profile channel diagnostic information is available.</returns>
        public C.SystemDefinedChannelDiagnostic GetSystemDefinedChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.SystemDefinedChannelDiagnostic CD in ((C.GsdDevice)this.CommonStore.Device).SystemDefinedChannelDiagnostics)
                {
                    if (CD.ErrorType == vErrorType)
                        return CD;
                }
            }
            return null;
        }


        /// <summary>
        /// Gets the available profile channel diagnostic entries.
        /// </summary>
        /// <returns>A list of all available ProfileChannelDiagnostic objects.
        /// Null if no profile channel diagnostic information are available.</returns>
        public Array GetSystemDefinedChannelDiagnostics()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).SystemDefinedChannelDiagnostics;
            }
            return null;
        }


        /// <summary>
        /// Gets the application relations available for the specified device access
        /// point.
        /// </summary>
        /// <param name="sDAPGsdID">The GSD ID of the access point for which the 
        /// application relation object should be returned.</param>
        /// <returns>The ApplicationRelations object of the access point with the given 
        /// GSD ID. Null if no application relations are available.</returns>
        public C.ApplicationRelations GetApplicationRelations(string sDAPGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                if (this.CommonStore.DeviceAccessPoints.ContainsKey(sDAPGsdID))
                {
                    return ((C.DeviceAccessPoint)this.CommonStore.DeviceAccessPoints[sDAPGsdID]).ApplicationRelations;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the channel process alarm information, available for a special reason.
        /// </summary>
        /// <param name="vReason">The reason of the channel process alarm.
        /// <returns>The ChannelProcessAlarm object available with this error type.
        /// Null if no channel process alarm information is available.</returns>
        public C.ChannelProcessAlarm GetChannelProcessAlarm(System.UInt32 vReason)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.ChannelProcessAlarm CD in ((C.GsdDevice)this.CommonStore.Device).ChannelProcessAlarms)
                {
                    if (CD.Reason == vReason)
                        return CD;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the available channel process alarm entries.
        /// </summary>
        /// <returns>A list of all available ChannelProcessAlarm objects.
        /// Null if no channel process alarm information are available.</returns>
        public Array GetChannelProcessAlarms()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).ChannelProcessAlarms;
            }

            return null;
        }

        /// <summary>
        /// Gets the system defined channel process alarm information, available for a special reason.
        /// </summary>
        /// <param name="vReason">The reason of the system defined channel process alarm.
        /// </param>
        /// <returns>The SystemDefinedChannelProcessAlarm object available with this error type.
        /// Null if no system defined channel process alarm information is available.</returns>
        public C.SystemDefinedChannelProcessAlarm GetSystemDefinedChannelProcessAlarm(System.UInt32 vReason)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.SystemDefinedChannelProcessAlarm CD in ((C.GsdDevice)this.CommonStore.Device).SystemDefinedChannelProcessAlarms)
                {
                    if (CD.Reason == vReason)
                        return CD;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the available system defined channel process alarm entries.
        /// </summary>
        /// <returns>A list of all available SystemDefinedChannelProcessAlarm objects.
        /// Null if no system defined channel process alarm information are available.</returns>
        public Array GetSystemDefinedChannelProcessAlarms()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).SystemDefinedChannelProcessAlarms;
            }

            return null;
        }

        /// <summary>
        /// Gets the profile channel process alarm information, available for a special reason.
        /// </summary>
        /// <param name="vReason">The reason of the system defined channel process alarm.
        /// </param>
        /// <returns>The ProfileChannelProcessAlarm object available with this error type.
        /// Null if no profile channel process alarm information is available.</returns>
        public C.ProfileChannelProcessAlarm GetProfileChannelProcessAlarm(System.UInt32 vReason)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                foreach (C.ProfileChannelProcessAlarm CD in ((C.GsdDevice)this.CommonStore.Device).ProfileChannelProcessAlarms)
                {
                    if (CD.Reason == vReason)
                        return CD;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets the available profile channel process alarm entries.
        /// </summary>
        /// <returns>A list of all available ProfileChannelProcessAlarm objects.
        /// Null if no profile channel process alarm information are available.</returns>
        public Array GetProfileChannelProcessAlarms()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDCommon))
            {
                return ((C.GsdDevice)this.CommonStore.Device).ProfileChannelProcessAlarms;
            }

            return null;
        }


        #endregion

        //########################################################################################
        #region IInterpreterStructure Members

        /// <summary>
        /// Gets the device structure element, which is the root of the structure
        /// data object model.
        /// </summary>
        /// <returns>The DeviceStructureElement object of the structure data object model.
        /// Null if not available.</returns>
        /// <remarks>The methods of the IInterpreterStructure interface are only
        /// useful if a GSD is assigned to the Interpreter and the structure data
        /// object model is wanted by the assignment.</remarks>
        public virtual S.DeviceStructureElement GetDeviceStructureElement()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
                return (S.DeviceStructureElement)this.StructureStore.Device;

            return null;
        }

        /// <summary>
        /// Gets a list of all module structure elements, available from the specified 
        /// access point.
        /// </summary>
        /// <param name="bstrAPGsdID">The GSD ID of the access point for which the modules 
        /// should be returned.</param>
        /// <returns>The list of ModuleStructureElement objects, which are available from the
        /// access point with the given GSD ID.</returns>
        public Array GetModuleStructureElements(string bstrAPGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.DeviceAccessPoints.ContainsKey(bstrAPGsdID))
                {
                    return null;
                }

                return ((S.AccessPointStructureElement)this.StructureStore.DeviceAccessPoints[bstrAPGsdID]).Modules;
            }

            return null;
        }

        /// <summary>
        /// Gets the specified module structure element.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the module structure element.</param>
        /// <returns>The ModuleStructureElement object with the specified GSD ID.
        /// Null if it isn't available.</returns>
        public S.ModuleStructureElement GetModuleStructureElement(string bstrGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.Modules.ContainsKey(bstrGsdID))
                {
                    return null;
                }

                return (S.ModuleStructureElement)this.StructureStore.Modules[bstrGsdID];
            }

            return null;
        }


        /// <summary>
        /// Gets the specified access point structure element.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the access point structure element.</param>
        /// <returns>The AccessPointStructureElement object with the specified GSD ID.
        /// Null if it isn't available.</returns>
        public S.AccessPointStructureElement GetDeviceAccessPointStructureElement(string bstrGsdID)
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.DeviceAccessPoints.ContainsKey(bstrGsdID))
                {
                    return null;
                }

                return (S.AccessPointStructureElement)this.StructureStore.DeviceAccessPoints[bstrGsdID];
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all available device access point structure elements from the 
        /// whole GSD, belonging to a device.
        /// </summary>
        /// <returns>The list of AccessPointStructureElement objects.</returns>
        public Array GetDeviceAccessPointStructureElements()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                return ((S.DeviceStructureElement)(this.StructureStore.Device)).DeviceAccessPoints;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all available module structure elements from the 
        /// whole GSD.
        /// </summary>
        /// <returns>The list of ModuleStructureElement objects.</returns>
        public Array GetAllModuleStructureElements()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (null == this.StructureStore.Modules)
                {
                    return null;
                }

                var array = new C.GsdObject[this.StructureStore.Modules.Values.Count];
                this.StructureStore.Modules.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all available submodule structure elements from the 
        /// whole GSD.
        /// </summary>
        /// <returns>The list of virtual SubmoduleStructureElement objects.</returns>
        public Array GetAllSubmoduleStructureElements()
        {
            // NOTE: At the moment there are no physical submodules available!
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (null == this.StructureStore.PhysicalSubmodules)
                {
                    return null;
                }

                var array = new C.GsdObject[this.StructureStore.PhysicalSubmodules.Values.Count];
                this.StructureStore.PhysicalSubmodules.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets the specified submodule structure element.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the submodule structure element.</param>
        /// <returns>The SubmoduleStructureElement object with the specified GSD ID.
        /// Null if it isn't available.</returns>
        public S.SubmoduleStructureElement GetSubmoduleStructureElement(string bstrGsdID)
        {
            // NOTE: At the moment there are no physical submodules available!
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.PhysicalSubmodules.ContainsKey(bstrGsdID))
                {
                    return null;
                }

                return (S.SubmoduleStructureElement)this.StructureStore.PhysicalSubmodules[bstrGsdID];
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all submodule structure elements, available from the 
        /// specified module or access point.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the module or access point for which the 
        /// submodule structure elements should be returned.</param>
        /// <param name="lModuleType">The type of the module, which is qualified with
        /// the GSD ID. Only device access point or module is allowed as type.</param>
        /// <returns>The list of SubmoduleStructureElement objects, which are available 
        /// from the module or access point specified with the given GSD ID.</returns>
        public Array GetSubmoduleStructureElements(string bstrGsdID, GSDI.ModuleTypes lModuleType)
        {
            // NOTE: At the moment there are no physical submodules available!
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                // NOTE: Only DeviceInterfaceModule or Module GsdID is allowed!!!
                switch (lModuleType)
                {
                    case ModuleTypes.GSDDeviceAccessPoint:
                        {
                            if (this.StructureStore.DeviceAccessPoints.ContainsKey(bstrGsdID))
                                return ((S.AccessPointStructureElement)this.StructureStore.DeviceAccessPoints[bstrGsdID]).Submodules;
                            break;
                        }
                    case ModuleTypes.GSDModule:
                        {
                            if (this.StructureStore.Modules.ContainsKey(bstrGsdID))
                                return ((S.ModuleStructureElement)this.StructureStore.Modules[bstrGsdID]).Submodules;
                            break;
                        }
                    case ModuleTypes.GSDVirtualSubmodule:
                    default:
                        {
                            break;
                        }
                }
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all available record data structure elements from the 
        /// whole GSD.
        /// </summary>
        /// <returns>The list of virtual RecordDataStructureElement objects.</returns>
        public Array GetAllRecordDataStructureElements()
        {
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (null == this.StructureStore.RecordDataItems)
                {
                    return null;
                }

                Array array = Array.CreateInstance(typeof(object), this.StructureStore.RecordDataItems.Count);
                this.StructureStore.RecordDataItems.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets the specified record data structure element.
        /// </summary>
        /// <param name="sGsdID">The GSD ID of the record data structure element.</param>
        /// <returns>The RecordDataStructureElement object with the specified GSD ID.
        /// Null if it isn't available.</returns>
        public S.RecordDataStructureElement GetRecordDataStructureElement(string sGsdID)
        {
            // NOTE: At the moment there are no physical submodules available!
            if (this.CheckModelAvailability(GSDI.ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.RecordDataItems.ContainsKey(sGsdID))
                {
                    return null;
                }

                return (S.RecordDataStructureElement)this.StructureStore.RecordDataItems[sGsdID];
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all record data structure elements, available from the 
        /// specified submodule.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the submodule for which the 
        /// record data structure elements should be returned.</param>
        /// <returns>The list of RecordDataStructureElement objects, which are available 
        /// from the submodule specified with the given GSD ID.</returns>
        public Array GetRecordDataStructureElements(string bstrGsdID)
        {
            if (this.CheckModelAvailability(ModelOptions.GSDStructure))
            {
                if (this.StructureStore.PhysicalSubmodules.ContainsKey(bstrGsdID))
                    return ((S.SubmoduleStructureElement)this.StructureStore.PhysicalSubmodules[bstrGsdID]).RecordDataItems;
                if (this.StructureStore.VirtualSubmodules.ContainsKey(bstrGsdID))
                    return ((S.SubmoduleStructureElement)this.StructureStore.VirtualSubmodules[bstrGsdID]).RecordDataItems;
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all available CommunicationInterface structure elements from the 
        /// whole GSD.
        /// </summary>
        /// <returns>The list of virtual CommunicationInterfaceStructureElement objects.</returns>
        public Array GetAllCommunicationInterfaceStructureElements()
        {
            if (this.CheckModelAvailability(ModelOptions.GSDStructure))
            {
                if (null == this.StructureStore.CommunicationInterfaceItems)
                {
                    return null;
                }

                var array = new C.GsdObject[StructureStore.CommunicationInterfaceItems.Values.Count];
                this.StructureStore.CommunicationInterfaceItems.Values.CopyTo(array, 0);

                return array;
            }

            return null;
        }

        /// <summary>
        /// Gets the specified CommunicationInterface structure element.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the record data structure element.</param>
        /// <returns>The CommunicationInterfaceStructureElement object with the specified GSD ID.
        /// Null if it isn't available.</returns>
        public S.CommunicationInterfaceStructureElement GetCommunicationInterfaceStructureElement(string bstrGsdID)
        {
            // NOTE: At the moment there are no physical submodules available!
            if (this.CheckModelAvailability(ModelOptions.GSDStructure))
            {
                if (!this.StructureStore.CommunicationInterfaceItems.ContainsKey(bstrGsdID))
                {
                    return null;
                }

                return (S.CommunicationInterfaceStructureElement)this.StructureStore.CommunicationInterfaceItems[bstrGsdID];
            }

            return null;
        }

        /// <summary>
        /// Gets a list of all CommunicationInterface structure elements, available from the 
        /// specified access point.
        /// </summary>
        /// <param name="bstrGsdID">The GSD ID of the access point for which the 
        /// record CommunicationInterface elements should be returned.</param>
        /// <returns>The list of CommunicationInterfaceStructureElement objects, which are available 
        /// from the access point specified with the given GSD ID.</returns>
        public Array GetCommunicationInterfaceStructureElements(string bstrGsdID)
        {
            if (this.CheckModelAvailability(ModelOptions.GSDStructure))
            {
                if (this.StructureStore.DeviceAccessPoints.ContainsKey(bstrGsdID))
                    return ((S.AccessPointStructureElement)this.StructureStore.DeviceAccessPoints[bstrGsdID]).CommunicationInterfaces;
            }

            return null;
        }

        #endregion

        //########################################################################################
        #region IInterpreterInfo Members

        /// <summary>
        /// Accesses the version of the GSDML specification, which is supported
        /// by the Interpreter.
        /// </summary>
        /// <value>Version of the GSDML specification supported from the Interpreter.</value>
        public string SupportedGsdmlVersion => this.m_SupportedGsdmlVersion;

        /// <summary>
        /// Accesses the version of the Interpreter itself.
        /// </summary>
        /// <value>Version of the Interpreter.</value>
        public string Version => this.m_InterpreterVersion;


        #endregion

        #region language support

        /// <summary>
        /// Returns the supported languages of a GSD file
        /// by the Interpreter.
        /// </summary>
        /// <returns>supported languages</returns>
        public Array GetSupportedLanguages()
        {
            Array array = Array.CreateInstance(typeof(object), this.LanguageStore.Count);
            this.LanguageStore.CopyTo(array, 0);

            return array;
        }

        #endregion
    }
}
