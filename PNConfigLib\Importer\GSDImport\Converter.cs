/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: Converter.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using GSDI;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.GSDImport.ComponentConverters;

using DeviceAccessPoint = PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint;
using InterfaceSubmodule = PNConfigLib.Gsd.Interpreter.Common.InterfaceSubmodule;
using Module = PNConfigLib.Gsd.Interpreter.Common.Module;
using ModulePlugData = PNConfigLib.Gsd.Interpreter.Common.ModulePlugData;
using PortSubmodule = PNConfigLib.Gsd.Interpreter.Common.PortSubmodule;
using VirtualSubmodule = PNConfigLib.Gsd.Interpreter.Common.VirtualSubmodule;
using PNConfigLib.BusinessLogic.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.GSDImport
{
    public static class Converter
    {
        internal static string XmlFilePath { get; set; }

        private static Gsd.Interpreter.Interpreter m_Interpreter;
        
        public static string GetTextFromTextId(string textId)
        {
            return textId;
        }
        internal static bool ImportGSDMLs(ListOfNodes listOfNodes, ConsistencyManager consistencyManager, string listOfNodesPath)
        {
            // There is no GSDML for central devices; therefore we only import decentral devices.
            foreach (DecentralDeviceType lonDecentralDevice in listOfNodes.DecentralDevice)
            {
                string gsdPath = FileOperations.GetFullDirectoryPath(lonDecentralDevice.GSDPath, listOfNodesPath);
                string fileName = Path.GetFileName(gsdPath);
                if (fileName != null && !Catalog.ImportedGSDMLList.Contains(fileName.ToUpperInvariant()))
                {
                    if (!ImportGSDML(gsdPath, consistencyManager))
                    {
                        return false;
                    }
                }
            }
            return true;
        }
        public static bool ImportGSDML(string xmlPath, ConsistencyManager consistencyManager)
        {
            var checkGsd = true;
            var buildObjectModel = true;
            var success = true;
            XmlFilePath = xmlPath;
            string name = Path.GetFileName(xmlPath);
            if (name != null)
            {
                string fileName = name.ToLowerInvariant();
                GsdmlIntegrationTarget target = new GsdmlIntegrationTarget(new FileInfo(fileName));
                var langFileRegex = new Regex("^[a-z0-9\\-\\.]*-text-[a-z]{2}.xml$", RegexOptions.IgnoreCase);
                if (langFileRegex.IsMatch(fileName))
                {
                    // No check for language files
                    checkGsd = false;
                    buildObjectModel = false;
                }
                if (buildObjectModel)
                {
                    if (checkGsd)
                        {
                        success = consistencyManager.ValidateGSDML(target.Revision, xmlPath);
                        }

                        if (success)
                        {
                        BuildObjectModel(xmlPath, fileName);
                    }
                }
                Catalog.ImportedGSDMLList.Add(name.ToUpperInvariant());
            }
            return success;
        }

        private static void BuildObjectModel(string xmlPath, string fileName)
        {
            m_Interpreter = new Gsd.Interpreter.Interpreter();
            m_Interpreter.AssignGsd(xmlPath, ModelOptions.GSDCommon, m_Interpreter.DefaultLanguage, false);

            //Get not only virtual submodules but all submodules
            foreach (VirtualSubmodule submoduleItem in m_Interpreter.GetAllVirtualSubmodules())
            {
                SubmoduleConverter submoduleConverter = new SubmoduleConverter(submoduleItem);
                SubmoduleCatalog submoduleCatalog = submoduleConverter.Convert(fileName);
                Catalog.SubmoduleList.Add(
                    m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID,
                    submoduleCatalog);
            }

            foreach (Module module in m_Interpreter.GetAllModules())
            {
                ModuleConverter moduleConverter = new ModuleConverter(module);
                ModuleCatalog moduleCatalog = moduleConverter.Convert(fileName);
                Catalog.ModuleList.Add(m_Interpreter.ActualGsdName + "\\" + module.GsdID, moduleCatalog);

                if (module.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort))
                {
                    Array arrPorts = module.GetSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort);
                    foreach (PortSubmodule port in arrPorts)
                    {
                        PortSubmoduleConverter portSubmoduleConverter = new PortSubmoduleConverter(port);
                        PortCatalog portCatalog = portSubmoduleConverter.Convert(fileName);
                        
                        if (!string.IsNullOrEmpty(port.GsdID))
                        {
                            Catalog.PortList.Add(m_Interpreter.ActualGsdName + "\\" + port.GsdID, portCatalog);
                        }
                        moduleCatalog.SystemDefinedPorts.Add((int)port.SubslotNumber, portCatalog);
                    }
                }

                System.Collections.IDictionary SubmodulePlugData;

                try
                {
                    SubmodulePlugData = module.SubmodulePlugData;
                }
                catch (NullReferenceException)
                {
                    SubmodulePlugData = null;
                }

                //Create SlotRelation List
                if (SubmodulePlugData != null)
                {
                    foreach (string submoduleGSD in SubmodulePlugData.Keys)
                    {
                        ModulePlugData mpd = module.GetSubmodulePlugData(submoduleGSD);
                        SlotRelation slotRelation = new SlotRelation();

                        slotRelation.AllowedInSlots = mpd.AllowedInSlots;
                        slotRelation.FixedInSlots = mpd.FixedInSlots;
                        slotRelation.UsedInSlots = mpd.UsedInSlots;

                        moduleCatalog.PluggableSubmoduleList.Add(submoduleGSD, slotRelation);
                    }
                }


                if (module.VirtualSubmodules != null)
                {
                    foreach (VirtualSubmodule submoduleItem in module.VirtualSubmodules)
                    {
                        SubmoduleCatalog smCatalog = Catalog
                            .SubmoduleList[m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID];
                        if (submoduleItem.PlugData != null)
                        {
                            if (submoduleItem.PlugData.FixedInSlots != null)
                            {
                                foreach (uint fixedInSlot in submoduleItem.PlugData.FixedInSlots)
                                {
                                    smCatalog.AttributeAccess.AddAnyAttribute<uint>(
                                        InternalAttributeNames.PnSubslotNumber,
                                        fixedInSlot);
                                    break;
                                }
                            }
                            else
                            {
                                uint defaultFixedInSubslotValueOfVirtualSubmodule = 1; // GSDML spec : default value 1 if the attribute "FixedInSubslots" is omitted, which is only allowed when there is only one VirtualSubmoduleItem)
                                smCatalog.AttributeAccess.AddAnyAttribute<uint>(
                                    InternalAttributeNames.PnSubslotNumber, defaultFixedInSubslotValueOfVirtualSubmodule);
                            }
                        }
                        moduleCatalog.VirtualSubmoduleList.Add(
                            Catalog.SubmoduleList[m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID]);
                    }
                }
            }

            foreach (PortSubmodule submoduleItem in m_Interpreter.GetAllPluggablePorts())
            {
                PortSubmoduleConverter portSubmoduleConverter = new PortSubmoduleConverter(submoduleItem);
                PortCatalog portCatalog = portSubmoduleConverter.Convert(fileName);
                Catalog.PortList.Add(m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID, portCatalog);
            }

            foreach (DeviceAccessPoint deviceAccessPointItem in m_Interpreter.GetDeviceAccessPoints())
            {
                DeviceConverter deviceConverter = new DeviceConverter(deviceAccessPointItem);
                RackConverter rackConverter = new RackConverter(deviceAccessPointItem);
                HeadModuleConverter headModuleConverter = new HeadModuleConverter(
                    deviceAccessPointItem,
                    m_Interpreter.GetDevice());
                DecentralDeviceCatalog decentralDeviceCatalog = deviceConverter.Convert(fileName);
                decentralDeviceCatalog = rackConverter.Convert(decentralDeviceCatalog);
                decentralDeviceCatalog = headModuleConverter.Convert(decentralDeviceCatalog);
                Catalog.DeviceList.Add(
                    m_Interpreter.ActualGsdName + "\\" + deviceAccessPointItem.GsdID,
                    decentralDeviceCatalog);

                if (deviceAccessPointItem.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDInterface))
                {
                    Array arrInterfaces =
                        deviceAccessPointItem.GetSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDInterface);

                    foreach (InterfaceSubmodule interfaceSubmoduleItem in
                        arrInterfaces)
                    {
                        InterfaceSubmoduleConverter interfaceConverter =
                            new InterfaceSubmoduleConverter(
                                deviceAccessPointItem,
                                interfaceSubmoduleItem,
                                m_Interpreter.GetDevice());
                        InterfaceCatalog interfaceCatalog = interfaceConverter.Convert(fileName);

                        if (!string.IsNullOrEmpty(interfaceSubmoduleItem.GsdID))
                        {
                            Catalog.InterfaceList.Add(
                                m_Interpreter.ActualGsdName + "\\" + interfaceSubmoduleItem.GsdID,
                                interfaceCatalog);
                        }
                        //GSDML spesification does not allow multiple interfacesubmodules
                        decentralDeviceCatalog.Interface = interfaceCatalog;

                        if (deviceAccessPointItem.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort))
                        {
                            Array arrPorts =
                                deviceAccessPointItem.GetSystemDefinedSubmodules(
                                    SystemDefinedSubmoduleTypes.GSDPort);

                            foreach (PortSubmodule port in arrPorts)
                            {
                                PortSubmoduleConverter portSubmoduleConverter = new PortSubmoduleConverter(port);
                                PortCatalog portCatalog = portSubmoduleConverter.Convert(fileName);

                                if (!string.IsNullOrEmpty(port.GsdID))
                                {
                                    Catalog.PortList.Add(
                                        m_Interpreter.ActualGsdName + "\\" + port.GsdID,
                                        portCatalog);
                                 }
                                interfaceCatalog.PortList.Add(port.SubslotNumber, portCatalog);
                            }
                        }
                    }
                }
                else
                {
                    // Create dummy interface submodule
                    InterfaceSubmoduleConverter interfaceConverter =
                        new InterfaceSubmoduleConverter(deviceAccessPointItem, null, m_Interpreter.GetDevice());
                    InterfaceCatalog interfaceCatalog = interfaceConverter.Convert(fileName);

                    interfaceCatalog.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        1);

                    interfaceCatalog.AttributeAccess.AddAnyAttribute<int>(
                        InternalAttributeNames.PnSubslotNumber,
                        32768);

                    Catalog.InterfaceList.Add(
                        m_Interpreter.ActualGsdName + "\\" + deviceAccessPointItem.GsdID + "_Interface",
                        interfaceCatalog);
                    decentralDeviceCatalog.Interface = interfaceCatalog;

                    // Create dummy port submodule (only one for whole GSD file)
                    // Port's PNSubslotNumber: PositionNumber of the Ports should be 32769 (0x8001) or 32770 (0x8002). For dummy ports it is set to 32769.
                    // Port's PNSubmoduleIdentNumber: If the submodule is PNFiberOptic it is 49168, ow it is 49152. For dummy ports it is set to 49152.
                    PortSubmodule port = new PortSubmodule();
                    PortSubmoduleConverter portSubmoduleConverter = new PortSubmoduleConverter(port);
                    PortCatalog portCatalog = portSubmoduleConverter.Convert(fileName);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.IoType);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnPTPBoundary);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnPortDeactivated);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnLinkStateDiagnosisCapability);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnFiberOptic);
                    portCatalog.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnCheckMAUTypeRecordSupported);

                    portCatalog.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnParameterizationDisallowed, 1);
                    portCatalog.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnSubmoduleIdentNumber, 49152);

                    portCatalog.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnSubslotNumber, 32769);
                    portCatalog.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnIOCSLength, 0);
                    portCatalog.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnIOPSLength, 0);
                    Catalog.PortList.Add(
                        m_Interpreter.ActualGsdName + "\\" + deviceAccessPointItem.GsdID + "_Port_0",
                        portCatalog);
                    interfaceCatalog.PortList.Add(32769, portCatalog);
                }

                if (deviceAccessPointItem.VirtualSubmodules != null)
                {
                    foreach (VirtualSubmodule submoduleItem in deviceAccessPointItem.VirtualSubmodules)
                    {
                        SubmoduleCatalog smCatalog = Catalog
                            .SubmoduleList[m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID];
                        if (submoduleItem.PlugData != null && submoduleItem.PlugData.FixedInSlots != null)
                        {
                            foreach (uint fixedInSlot in submoduleItem.PlugData.FixedInSlots)
                            {
                                smCatalog.AttributeAccess
                                    .AddAnyAttribute<uint>(InternalAttributeNames.PnSubslotNumber, fixedInSlot);
                                break;
                            }
                        }
                        decentralDeviceCatalog.VirtualSubmoduleList.Add(
                            Catalog.SubmoduleList[m_Interpreter.ActualGsdName + "\\" + submoduleItem.GsdID]);
                    }
                }

                if (deviceAccessPointItem.SystemDefinedSubmodulesArray != null)
                {
                    foreach (SystemDefinedSubmoduleObject systemDefinedSubmodule in deviceAccessPointItem.SystemDefinedSubmodulesArray)
                    {
                        decentralDeviceCatalog.SystemDefinedSubmoduleList.Add(systemDefinedSubmodule);
                    }
                }

                //Create SlotRelation List
                if (deviceAccessPointItem.ModulePlugDataDictionary != null)
                {
                    foreach (string moduleGSD in deviceAccessPointItem.ModulePlugDataDictionary.Keys)
                    {
                        ModulePlugData mpd = deviceAccessPointItem.GetModulePlugData(moduleGSD);
                        SlotRelation slotRelation = new SlotRelation();

                        slotRelation.AllowedInSlots = mpd.AllowedInSlots;
                        slotRelation.FixedInSlots = mpd.FixedInSlots;
                        slotRelation.UsedInSlots = mpd.UsedInSlots;

                        decentralDeviceCatalog.PluggableModuleList.Add(moduleGSD, slotRelation);
                    }
                }
                decentralDeviceCatalog.PhysicalSubslotList = (deviceAccessPointItem.PhysicalSubslots == null) ? new List<uint>() : deviceAccessPointItem.PhysicalSubslots.OfType<uint>().ToList();

                System.Collections.IDictionary SubmodulePlugDataDictionary;

                try
                {
                    SubmodulePlugDataDictionary = deviceAccessPointItem.SubmodulePlugDataDictionary;
                }
                catch (NullReferenceException)
                {
                    SubmodulePlugDataDictionary = null;
                }

                if (SubmodulePlugDataDictionary != null)
                {
                    foreach (string submoduleGSD in SubmodulePlugDataDictionary.Keys)
                    {
                        ModulePlugData mpd = deviceAccessPointItem.GetSubmodulePlugData(submoduleGSD);
                        SlotRelation slotRelation = new SlotRelation();

                        slotRelation.AllowedInSlots = mpd.AllowedInSlots;
                        slotRelation.FixedInSlots = mpd.FixedInSlots;
                        slotRelation.UsedInSlots = mpd.UsedInSlots;

                        decentralDeviceCatalog.PluggableSubmoduleList.Add(submoduleGSD, slotRelation);
                    }
                }
            }
        }
    }
}