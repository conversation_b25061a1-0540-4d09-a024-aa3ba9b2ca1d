﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IOsysParamConfigHelper.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using PNConfigLib.DataModel.AttributeUtilities;
using System;
using System.Collections.Generic;
using System.Text;

namespace PNConfigLib.BusinessLogic.HWCNBL
{
    internal class IosysParamConfigHelper : IIosysParamConfigHelper
    {
        #region Constants
        private const ushort s_DefaultPropertyValue = 0;
        private const ushort s_DefaultBlockVersion = 256;
        private const ushort s_HighNetLoadEnabledBitmask = 1;
        #endregion

        #region Fields
        private bool m_PnIoKeepARAtErrorActivated;
        private byte m_BlockVersionLow;
        private byte m_BlockVersionHigh;
        #endregion

        #region Properties
        public byte GetBlockVersionHigh
        {
            get
            {
                return m_BlockVersionHigh;
            }
        }

        public byte GetBlockVersionLow
        {
            get
            {
                return m_BlockVersionLow;
            }
        }

        public ushort GetProperties
        {
            get
            {
                if (m_PnIoKeepARAtErrorActivated)
                {
                    return s_HighNetLoadEnabledBitmask;
                }

                return s_DefaultPropertyValue;
            }
        }
        #endregion

        #region Construction
        public IosysParamConfigHelper(AttributeAccess attributeAccess)
        {
            if (attributeAccess == null)
            {
                throw new ArgumentNullException(nameof(attributeAccess));
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            m_PnIoKeepARAtErrorActivated = attributeAccess.GetAnyAttribute(InternalAttributeNames.PnIoKeepARAtErrorActivated,
                                                                       ac,
                                                                       false);

            ushort blockVersion = (ushort)attributeAccess.
                                    GetAnyAttribute<uint>(InternalAttributeNames.PnIoControllerPropertiesBlockVersion,
                                                          ac.GetNew(),
                                                          s_DefaultBlockVersion);

            m_BlockVersionHigh = (byte)(blockVersion / 256);
            m_BlockVersionLow = (byte)(blockVersion % 256);
        }
        #endregion
    }
}
