﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ProfileProcessAutomation.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Runtime.InteropServices;
using System.Collections;
using GSDI;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.Gsd.Interpreter;
using System.Diagnostics;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// This element contains the definition of the netload class according to the link speed.
    /// </summary>
    [ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C21")]
    public class ProfileProcessAutomation :
        GsdObject,
        GSDI.IProfileProcessAutomation

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ProfileProcessAutomation if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ProfileProcessAutomation()
        {
            m_PAProfileVersion = String.Empty;
            m_PAProfileDeviceID = 0;
            m_PAProfileDeviceDAP_ID = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private GSDI.PADeviceClasses m_PADeviceClass;
        private string m_PAProfileVersion;
        private uint m_PAProfileDeviceID;
        private string m_PAProfileDeviceDAP_ID;

        #endregion

        //########################################################################################
        #region Properties

        public GSDI.PADeviceClasses PADeviceClass => this.m_PADeviceClass;

        public string PAProfileVersion => this.m_PAProfileVersion;

        public UInt32 PAProfileDeviceID => this.m_PAProfileDeviceID;

        public string PAProfileDeviceDAP_ID => this.m_PAProfileDeviceDAP_ID;


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.

                string member = Models.s_FieldPADeviceClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.PADeviceClasses)
                    m_PADeviceClass = (GSDI.PADeviceClasses)hash[member];

                member = Models.s_FieldPAProfileVersion;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_PAProfileVersion = (string)hash[member];

                member = Models.s_FieldPAProfileDeviceID;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_PAProfileDeviceID = (uint)hash[member];

                member = Models.s_FieldPAProfileDeviceDAP_ID;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_PAProfileDeviceDAP_ID = (string)hash[member];

                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectProfileProcessAutomation);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldPADeviceClass, this.m_PADeviceClass.ToString(), Export.s_SubtypeLinkSpeeds);
            Export.WriteStringProperty(ref writer, Models.s_FieldPAProfileVersion, this.m_PAProfileVersion);
            Export.WriteUint32Property(ref writer, Models.s_FieldPAProfileDeviceID, this.m_PAProfileDeviceID);
            Export.WriteStringProperty(ref writer, Models.s_FieldPAProfileDeviceDAP_ID, this.m_PAProfileDeviceDAP_ID);

            return true;
        }


        #endregion
    }
}
