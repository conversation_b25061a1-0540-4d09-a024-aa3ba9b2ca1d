/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_02.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Globalization;
using System.Xml;
using System.Collections;
using System.Xml.XPath;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Linq;

using GSDI;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.2 and is based on GSD(ML) versions 2.1 and lower.
    ///		
    /// </summary>
    internal class CheckerV0202 : CheckerV0201
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.2.
        /// </summary>
        public CheckerV0202()
        {
            SetSupportedGsdmlVersion(Constants.s_Version22);
        }

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV0202;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV0202;
        }

        #endregion

        #region Properties

        protected virtual string Msg_0X000121052 => "0x00012105_2";

        protected virtual string Msg_0X000121132 => "0x00012113_2";

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:InterfaceSubmoduleItem/@IsochroneModeInRT_Classes" +
                            " | .//gsddef:SynchronisationMode/@SupportedSyncProtocols";
                return (xp);
            }
        }

        #endregion

        #region Methods

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();


            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00012101);
            Checks.Add(Constants.s_Cn_0X00012104);
            Checks.Add(Constants.s_Cn_0X00012105);
            Checks.Add(Constants.s_Cn_0X00012105_2);
            Checks.Add(Constants.s_Cn_0X00012107);
            Checks.Add(Constants.s_Cn_0X00012108);
            Checks.Add(Constants.s_Cn_0X00012109);
            Checks.Add(Constants.s_Cn_0X00012110);
            Checks.Add(Constants.s_Cn_0X0001211D);
            Checks.Add(Constants.s_Cn_0X00012111);
            Checks.Add(Constants.s_Cn_0X00012112);
            Checks.Add(Constants.s_Cn_0X00012113);
            Checks.Add(Constants.s_Cn_0X00012114);
            Checks.Add(Constants.s_Cn_0X00012115);
            Checks.Add(Constants.s_Cn_0X00012116);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();
            try
            {
                Checks.Remove(Constants.s_Cn_0X00010007);
                Checks.Remove(Constants.s_Cn_0X0001000F);
                Checks.Remove(Constants.s_Cn_0X00010010);
                Checks.Remove(Constants.s_Cn_0X00010018);
                Checks.Remove(Constants.s_Cn_0X00010019);
                Checks.Remove(Constants.s_Cn_0X00010024);
                Checks.Remove(Constants.s_Cn_0X00010028);
                Checks.Remove(Constants.s_Cn_0X00010029);
                Checks.Remove(Constants.s_Cn_0X0001002A);
                Checks.Remove(Constants.s_Cn_0X0001002B);
                Checks.Remove(Constants.s_Cn_0X00010044);
                Checks.Remove(Constants.s_Cn_0X00010045);
                Checks.Remove(Constants.s_Cn_0X00010046);
                Checks.Remove(Constants.s_Cn_0X00010047);
                Checks.Remove(Constants.s_Cn_0X00010049);
                Checks.Remove(Constants.s_Cn_0X0001004A);
                Checks.Remove(Constants.s_Cn_0X00010112);
                Checks.Remove(Constants.s_Cn_0X0001011D);
                Checks.Remove(Constants.s_Cn_0X00011107);
                Checks.Remove(Constants.s_Cn_0X0001119D);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }
            return succeeded;
        }

        private bool IsVersion2Needed(XContainer submodItem)
        {
            var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
            if (ioData == null)
            {
                return false;
            }
            var input = ioData.Element(NamespaceGsdDef + Elements.s_Input);
            if (input != null)
            {
                var dataItems = input.Elements(NamespaceGsdDef + Elements.s_DataItem);
                foreach (var dataItem in dataItems)
                {
                    string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                    if (dataType == Enums.s_Integer32 ||
                        dataType == Enums.s_Unsigned8Unsigned8)
                    {
                        return true;
                    }

                }
            }

            var output = ioData.Element(NamespaceGsdDef + Elements.s_Output);
            if (output == null)
            {
                return false;
            }

            {
                // Output is available only once
                var dataItems = output.Elements(NamespaceGsdDef + Elements.s_DataItem);
                foreach (var dataItem in dataItems)
                {
                    string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                    if (dataType == Enums.s_Integer32 ||
                        dataType == Enums.s_Unsigned8Unsigned8)
                    {
                        return true;
                    }
                }

            }
            return false;
        }

        private bool IsBit1Set(IReadOnlyList<ValueListHelper.ValueRangeT> values)
        {
            bool isBit1Set = false;

            for (uint i = values[0].From; i <= values[values.Count - 1].To; i++)
            {
                if (i % 2 > 0)
                {
                    isBit1Set = true;
                    break;
                }
            }

            return isBit1Set;
        }

        protected virtual void FParamDescCrcForFBlockIDFaked(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Block_ID, if visible
            var fBlockID = fparamRecord.Element(NamespaceGsdDef + Elements.s_FBlockID);
            if (fBlockID != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fBlockID, Attributes.s_Visible);
                bool bVisible = true;    // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFBlockID = 0;  // Default: "0"
                    string strDFBlockID = Help.GetAttributeValueFromXElement(fBlockID, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strDFBlockID))
                        dFBlockID = (UInt16)XmlConvert.ToInt16(strDFBlockID); // This (double)cast is important to convert -0

                    // Allowed values - Fake it
                    ValueListHelper.ValueRangeT allowedValues;
                    allowedValues.From = 0;
                    allowedValues.To = 7;

                    byte[] data = new byte[]
                        {
                        (byte)'F',(byte)'_',(byte)'B',(byte)'l',(byte)'o',(byte)'c',
                        (byte)'k',(byte)'_',(byte)'I',(byte)'D',
                        0x00, 0x03,  // Type=BitArea, Offset=3
                        (byte)(dFBlockID), (byte)(dFBlockID >> 8),  // Default value
                        (byte)(allowedValues.From), (byte)(allowedValues.From >> 8),  // Min value
                        (byte)(allowedValues.To), (byte)(allowedValues.To >> 8)  // Max value
                        };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Default:"true"'
                byte[] data = new byte[]
                {
                (byte)'F',(byte)'_',(byte)'B',(byte)'l',(byte)'o',(byte)'c',
                (byte)'k',(byte)'_',(byte)'I',(byte)'D',
                0x00, 0x03,  // Type=BitArea, Offset=3
                0x00, 0x00,  // Default value
                0x00, 0x00,  // Min value
                0x07, 0x00   // Max value
                };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void CollectBytesFParamDescCrcFaked(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // F_Check_iPar
            FParamDescCrcForFCheckIPar(fparamRecord, calcFParamDescCrc);

            // F_SIL
            FParamDescCrcForFSil(fparamRecord, calcFParamDescCrc);

            // F_CRC_Length
            FParamDescCrcForFCrcLength(fparamRecord, calcFParamDescCrc);

            // F_Block_ID
            FParamDescCrcForFBlockIDFaked(fparamRecord, calcFParamDescCrc);

            // F_Par_Version
            FParamDescCrcForFParVersion(fparamRecord, calcFParamDescCrc);

            // F_Source_Add
            FParamDescCrcForFSourceAdd(fparamRecord, calcFParamDescCrc);

            // F_Dest_Add
            FParamDescCrcForFDestAdd(fparamRecord, calcFParamDescCrc);

            // F_WD_Time
            FParamDescCrcForFWdTime(fparamRecord, calcFParamDescCrc);

            // F_iPar_CRC
            FParamDescCrcForFIParCrc(fparamRecord, calcFParamDescCrc);

            // F_Par_CRC
            FParamDescCrcForFParCrc(fparamRecord, calcFParamDescCrc);
        }

        #endregion

        #region All Checks

        #region Category : Validation

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("RT_CLASS_UDP");
            tokens1.Add("RT_CLASS_1");
            tokens1.Add("RT_CLASS_2");
            tokens1.Add("RT_CLASS_3");
            AttributeTokenDictionary.Add("IsochroneModeInRT_Classes", tokens1);

            IList<string> tokens2 = new List<string>();
            tokens2.Add("PTCP");
            AttributeTokenDictionary.Add("SupportedSyncProtocols", tokens2);

        }

        #endregion

        protected override bool DoSchemaCheck_0X00010048(XAttribute an)
        {
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010009
        /// The "@TextId" attribute must be unique across all texts within the "PrimaryLanguage". 
        /// Furthermore, the "@TextId" references must point to an existing text within the "PrimaryLanguage".
        /// The "@TextId" attribute must also be unique for each additional language entered within the respective 
        /// language. The "@TextId" references should also refer to an existing text for each language entered.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010009()
        {
            // Keys -----
            var primarykeys = new List<string>();

            var nl =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PrimaryLanguage)
                    .Elements(NamespaceGsdDef + Elements.s_Text)
                    .Attributes(Attributes.s_TextId);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                primarykeys.Add(Help.CollapseWhitespace(an.Value));
            }


            IDictionary<string, IList<string>> languages = new Dictionary<string, IList<string>>();
            IList<string> keys = null;

            var nlLanguages = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Language);
            nlLanguages = Help.TryRemoveXElementsUnderXsAny(nlLanguages, Nsmgr, Gsd);
            foreach (var en in nlLanguages)
            {
                var lineInfo = (IXmlLineInfo)en;


                string langcode = Help.GetAttributeValueFromXElement(en, Attributes.s_XMLLang);

                if (!string.IsNullOrEmpty(langcode))
                {
                    continue;
                }
                string language = Iso6391.LangCodeToString(langcode);
                if (langcode.Equals("en", StringComparison.Ordinal))
                {
                    // "The language code 'en' is not valid, because English is defined to be the default value."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010009_3"));
                    string xpath = Help.GetXPath(en.Attribute("{http://www.w3.org/XML/1998/namespace}" + Attributes.s_XMLLang.Remove(0, 4)));
                    xpath = xpath.Substring(0, xpath.LastIndexOf('@') - 1);
                    Store.CreateAndAnnounceReport(ReportType_0X000100093, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.KeyKeyref, "0x00010009_3");
                }

                if (language.Contains("??"))
                {
                    // "The language code is not a valid value as defined in ISO 639-1."
                    string msg = Help.GetMessageString("M_0x00010009_4");
                    string xpath = Help.GetXPath(en.Attribute("{http://www.w3.org/XML/1998/namespace}" + Attributes.s_XMLLang.Remove(0, 4)));
                    xpath = xpath.Substring(0, xpath.LastIndexOf('@') - 1);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.KeyKeyref, "0x00010009_4");
                }

                var nltemp = en.Descendants(NamespaceGsdDef + Elements.s_Text).Attributes(Attributes.s_TextId);
                foreach (XAttribute an in nltemp)
                {
                    string normalizedId = Help.CollapseWhitespace(an.Value);

                    if (keys == null)
                    {
                        keys = new List<string>();
                    }

                    keys.Add(normalizedId);
                }

                languages.Add(langcode, keys);
                keys = null;

            }

            // KeyRefs -----
            nl = from attribute in GsdProfileBody.Descendants().Attributes(Attributes.s_TextId)
                 where attribute.Parent != null && attribute.Parent.Name.LocalName != Elements.s_Text
                 select attribute;
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string normalizedId = Help.CollapseWhitespace(an.Value);
                foreach (string langcode in languages.Keys)
                {
                    if (((languages[langcode])).Contains(normalizedId))
                    {
                        continue;
                    }
                    // "The '@TextId' reference attribute must reference an existing text of the language '{0}'."
                    string language = Iso6391.LangCodeToString(langcode);
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010009_7"), language);
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00010009_7");
                }
            }


            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010013
        /// The 'Ref/@BitLength' attribute can only be used in conjunction with data type "BitArea".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010013()
        {
            string xp = ".//gsddef:Ref[not(@DataType='BitArea') and @BitLength]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The 'Ref/@BitLength' attribute can only be used in conjunction with data type "BitArea"."
                    string msg = Help.GetMessageString("M_0x00010013_2");
                    string xpath = Help.GetXPath(en);
                    IXmlLineInfo xli = en;
                    Store.CreateAndAnnounceReport(ReportType_0X000100132, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010013_2");
                }
            }

            return true;
        }


        /// <summary>
        /// The schemaLocation attribute may only consist of exactly two entries separated by white space;
        /// the first must be the GSDML namespace "http://www.profibus.com/GSDML/2003/11/DeviceProfile",
        /// the second must be "..\xsd\GSDML-DeviceProfile-vX.Y.xsd" (case insensitive).
        /// For external language file, the namespace must be as of GSDML version 2.2
        /// "http://www.profibus.com/GSDML/2003/11/Primitives" and
        /// the schema reference must be "..\..\xsd\GSDML-Primitives-vX.Y.xsd".
        /// </summary>
        /// <returns></returns>
        protected override bool CheckCn_0X00010026()
        {
            XNamespace ns = Nsmgr.LookupNamespace("gsdxsi");
            if (Gsd.Root == null)
            {
                return true;
            }

            var schemaLocation = Gsd.Root.Attribute(ns + "schemaLocation");
            if (schemaLocation == null)
            {
                CreateReport0X00010026_1();
                return true;
            }
            var lineInfo = (IXmlLineInfo)schemaLocation;

            if (!CheckParameter.ContainsKey("GsdName") || CheckParameter["GsdName"] == null)
            {
                return true;
            }
            const bool IgnoreCase = true;
            bool deviceProfileUsed = false;
            int index1 = schemaLocation.Value.IndexOf("http", StringComparison.CurrentCultureIgnoreCase);
            if (CreateReport0X00010026_4(index1, schemaLocation, lineInfo))
            {
                return true;
            }

            const string RequiredNamespace1 = "http://www.profibus.com/GSDML/2003/11/DeviceProfile";
            const string RequiredNamespace2 = "http://www.profibus.com/GSDML/2003/11/Primitives";
            bool xsdGiven = (schemaLocation.Value.Length > index1 + RequiredNamespace2.Length)
           && (-1 != schemaLocation.Value.IndexOf(
                   "xsd",
                   index1 + RequiredNamespace2.Length,
                   StringComparison.CurrentCultureIgnoreCase));
            if (CreateReport0X00010026_4(
                     schemaLocation,
                     index1,
                     RequiredNamespace1,
                     IgnoreCase,
                     RequiredNamespace2,
                     lineInfo,
                     ref deviceProfileUsed,
                     out int index2))
            {
                return true;
            }

            if (CreateReport0X00010026_2(index2, xsdGiven, schemaLocation, lineInfo))
            {
                return true;
            }



            if (-1 == index2)
                index2 = 0;

            if (deviceProfileUsed)
            {
                string deviceProfileSchema = schemaLocation.Value.Remove(0, index2);
                CreateReport0X00010026_5(deviceProfileSchema, IgnoreCase, schemaLocation, lineInfo);
                CreateReport0X00010026_8(deviceProfileSchema, schemaLocation, lineInfo);


            }
            else
            {
                string primitivesSchema = schemaLocation.Value.Remove(0, index2);
                CreateReport0X00010026_7(primitivesSchema, IgnoreCase, schemaLocation, lineInfo);


                // get version from schemaLocation attribute, DeviceProfile entry
                const String PrimitivesPattern = @"primitives-(v[0-9]+\.[0-9]*).xsd\s*$";
                Match primitivesMatch = Regex.Match(primitivesSchema.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture), PrimitivesPattern);

                // get all versions as string
                string primitivesVersion = primitivesMatch.Groups[1].ToString();

                if (primitivesVersion == FileNameGsdmlVersion)
                {
                    return true;
                }

                // "The 'schemaLocation' attribute does not reference the appropriate schema file."
                string msg = Help.GetMessageString("M_0x00010026_8");
                string xpath = Help.GetXPath(schemaLocation);
                Store.CreateAndAnnounceReport(
                    ReportType_0X00010026,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010026_8");
            }

            return true;
        }

        private void CreateReport0X00010026_1()
        {
            int lineNumber = 0;
            int linePosition = 0;
            if (Gsd.Root != null)
            {
                IXmlLineInfo xli = Gsd.Root;
                lineNumber = xli.LineNumber;
                linePosition = xli.LinePosition;
            }

            // "The 'schemaLocation' attribute must be given."
            string msg = Help.GetMessageString("M_0x00010026_1");
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineNumber,
                linePosition,
                msg,
                null,
                ReportCategories.TypeSpecific,
                "0x00010026_1");
        }

        private void CreateReport0X00010026_7(
            string primitivesSchema,
            bool IgnoreCase,
            XObject schemaLocation,
            IXmlLineInfo lineInfo)
        {
            const string RequiredPrimitives = "..\\..\\xsd\\GSDML-Primitives";

            if (0 == string.Compare(primitivesSchema, 0, RequiredPrimitives, 0, RequiredPrimitives.Length, IgnoreCase, CultureInfo.InvariantCulture))
            {
                return;
            }
            // "The second entry of the 'schemaLocation' attribute must contain the primitives schema: "..\..\xsd\GSDML-Primitives-vX.Y.xsd"."
            string msg = Help.GetMessageString("M_0x00010026_7");
            string xpath = Help.GetXPath(schemaLocation);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_7");
        }

        private void CreateReport0X00010026_8(string deviceProfileSchema, XObject schemaLocation, IXmlLineInfo lineInfo)
        {
            // get version from schemaLocation attribute, DeviceProfile entry
            const String DeviceProfilePattern = @"deviceprofile-(v[0-9]+\.[0-9]*).xsd\s*$";
            Match deviceProfileMatch = Regex.Match(deviceProfileSchema.ToUpperInvariant().ToLower(CultureInfo.InvariantCulture), DeviceProfilePattern);

            // get all versions as string
            string deviceProfileVersion = deviceProfileMatch.Groups[1].ToString();

            if (deviceProfileVersion == FileNameGsdmlVersion)
            {
                return;
            }
            // "The 'schemaLocation' attribute does not reference the appropriate schema file."
            string msg = Help.GetMessageString("M_0x00010026_8");
            string xpath = Help.GetXPath(schemaLocation);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_8");
        }
        private void CreateReport0X00010026_5(
          string deviceProfileSchema,
          bool IgnoreCase,
          XObject schemaLocation,
          IXmlLineInfo lineInfo)
        {
            string requiredProfileSchema = "..\\XSD\\GSDML-DeviceProfile";
            if (0 == string.Compare(deviceProfileSchema, 0, requiredProfileSchema, 0, requiredProfileSchema.Length, IgnoreCase, CultureInfo.InvariantCulture))
            {
                return;
            }
            // "The second entry of the 'schemaLocation' attribute must contain the device profile schema: "..\xsd\GSDML-DeviceProfile-vX.Y.xsd"."
            string msg = Help.GetMessageString("M_0x00010026_5");
            string xpath = Help.GetXPath(schemaLocation);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_5");
        }
        private bool CreateReport0X00010026_2(int index2, bool xsdGiven, XObject schemaLocation, IXmlLineInfo lineInfo)
        {

            if (-1 != index2 || !xsdGiven)
            {
                return false;
            }
            // "The 'schemaLocation' attribute does not contain the appropriate number of two entries."
            string msg = Help.GetMessageString("M_0x00010026_2");
            string xpath = Help.GetXPath(schemaLocation);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_2");
            return true;
        }

        

        private bool CreateReport0X00010026_4(
            XAttribute schemaLocation,
            int index1,
            string requiredNamespace1,
            bool ignoreCase,
            string requiredNamespace2,
            IXmlLineInfo lineInfo,
            ref bool deviceProfileUsed,
            out int index2)
        {
            index2 = 0;

            if (0 == string.Compare(
                    schemaLocation.Value,
                    index1,
                    requiredNamespace1,
                    0,
                    requiredNamespace1.Length,
                    ignoreCase,
                    CultureInfo.InvariantCulture))
            {
                deviceProfileUsed = true;
                index2 = schemaLocation.Value.IndexOf(
                    "..",
                    index1 + requiredNamespace1.Length,
                    StringComparison.CurrentCultureIgnoreCase);
            }
            else if (0 == string.Compare(
                         schemaLocation.Value,
                         index1,
                         requiredNamespace2,
                         0,
                         requiredNamespace2.Length,
                         ignoreCase,
                         CultureInfo.InvariantCulture))
            {
                deviceProfileUsed = false;
                index2 = schemaLocation.Value.IndexOf(
                    "..",
                    index1 + requiredNamespace2.Length,
                    StringComparison.CurrentCultureIgnoreCase);
            }
            else
            {
                // "The first entry of the 'schemaLocation' attribute must contain either the namespace:
                // "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/GSDML/2003/11/Primitives"."
                string msg = Help.GetMessageString("M_0x00010026_4");
                string xpath = Help.GetXPath(schemaLocation);
                Store.CreateAndAnnounceReport(
                    ReportType_0X00010026,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010026_4");
                return true;
            }

            return false;
        }

        private bool CreateReport0X00010026_4(int index1, XObject schemaLocation, IXmlLineInfo lineInfo)
        {
            if (-1 != index1)
            {
                return false;
            }
            // "The first entry of the 'schemaLocation' attribute must contain either the namespace:
            // "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/GSDML/2003/11/Primitives"."
            string msg = Help.GetMessageString("M_0x00010026_4");
            string xpath = Help.GetXPath(schemaLocation);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_4");
            return true;
        }

        protected virtual bool DoesDataTypeRequiresLength(string dataType)
        {
            if (dataType != Enums.s_OctetString && dataType != Enums.s_VisibleString)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001119B
        /// Check "DataItem/@DataType" attribute
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X0001119B()
        {
            // Find all 'VisibleString' or 'OctetString' without '@Length' given.
            string xp = "(.//gsddef:ProfileExtChannelDiagItem | .//gsddef:ExtChannelDiagItem)/gsddef:ExtChannelAddValue/" +
                        "gsddef:DataItem[not(@Length) and " + DataTypesRequiresLength + "]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The attribute 'DataItem/@Length' must be used, if 'DataItem/@DataType'="{0}"."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001119B_2"), Help.GetAttributeValueFromXElement(en, Attributes.s_DataType));
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119B_2");
            }

            // Check, if the data type match with the data length.
            xp = "(.//gsddef:ProfileExtChannelDiagItem | .//gsddef:ExtChannelDiagItem)/gsddef:ExtChannelAddValue/gsddef:DataItem";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                string sLength = Help.GetAttributeValueFromXElement(en, Attributes.s_Length);
                if (!String.IsNullOrEmpty(sLength))
                {
                    UInt16 length = XmlConvert.ToUInt16(sLength);
                    string dataType = Help.GetAttributeValueFromXElement(en, Attributes.s_DataType);
                    if (!DoesDataTypeRequiresLength(dataType))
                    {
                        UInt16 dataTypeLength = (UInt16)(Help.GetBitLengthFromDataItemType(dataType) / 8);
                        if (dataTypeLength != length)
                        {
                            // "The attribute 'DataItem/@Length'(={0}) must match to 'DataItem/@DataType' (length={1})."
                            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001119B_3"), length, dataTypeLength);
                            string xpath = Help.GetXPath(en);
                            IXmlLineInfo xli = en;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119B_3");
                        }
                    }
                }
            }

            return true;
        }

        
        protected override bool CheckSubslotFor0()
        {
            // prepare Module dictionary
            string xp = ".//gsddef:ModuleItem";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            var modules = nl1.ToList();
            Dictionary<string, ModuleSubslot0T> allModules = new(modules.Count);
            ModuleSubslot0T ms0;
            ms0.State = Subslot0StateT.UnTested;
            ms0.Attr = null;
            foreach (var module in modules)
            {
                string id = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(module, Attributes.ID));
                if (allModules.ContainsKey(id))
                {
                    // this is an error which is detected elsewhere; ignore it here
                }
                else
                {
                    ms0.Module = module;
                    allModules.Add(id, ms0);
                }
            }



            // check
            xp = ".//gsddef:DeviceAccessPointItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                if (!SubmoduleWithSubslot0(dap, allModules, out XAttribute subslot))
                {
                    continue;
                }
                bool pullModuleAlarmSupported = false;
                XAttribute pullModuleAlarmSupportedNode = ((IEnumerable)dap.XPathEvaluate(
                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations/@PullModuleAlarmSupported", Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (pullModuleAlarmSupportedNode != null)
                    pullModuleAlarmSupported = XmlConvert.ToBoolean(pullModuleAlarmSupportedNode.Value);

                if (!pullModuleAlarmSupported)
                {
                    if (!Help.CheckSchemaVersion(subslot, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "The subslot 0 is only allowed if the pull module alarm is supported by the interface."
                    string msg = Help.GetMessageString("M_0x00010043_2");
                    string xpath = Help.GetXPath(subslot);
                    IXmlLineInfo xli = subslot;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010043_2");
                }

                else
                {
                    if (!Help.CheckSchemaVersion(subslot, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "Subslot 0 in combination with PullModuleAlarmSupported="true" is allowed, but might not work with all controllers."
                    string msg = Help.GetMessageString("M_0x00010043_7");
                    string xpath = Help.GetXPath(subslot);
                    IXmlLineInfo xli = subslot;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010043_7");
                }


            }

            return true;
        }

        /// <summary>
        /// Check for F_IO_StructureDescCRC.
        /// </summary>
        protected virtual bool CheckCn_0X00012101()
        {
            string xp = "(.//gsddef:VirtualSubmoduleItem | .//gsddef:SubmoduleItem)[./gsddef:IOData/@F_IO_StructureDescCRC > 65535 and " +
                        "(./gsddef:IOData/@F_IO_StructureDescVersion < 2 or not(./gsddef:IOData/@F_IO_StructureDescVersion))]";
            var nl = GsdProfileBody.XPathSelectElements(xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The value of 'F_IO_StructureDescCRC' requires a 'F_IO_StructureDescVersion' with a value greater or equal than 2."
                    string msg = Help.GetMessageString("M_0x00012101_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012101_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check the attribute MaxSupportedRecordSize.
        /// Min Value must be at least 4068.
        /// </summary>
        protected virtual bool CheckCn_0X00012104()
        {
            const UInt32 Min = 4068;
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem).Attributes(Attributes.s_MaxSupportedRecordSize);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                UInt32 value = XmlConvert.ToUInt32(an.Value);
                if (value < Min)
                {
                    // "Value contained within the 'DeviceAccessPointItem/@MaxSupportedRecordSize' attribute must be at least {0}."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012104_1"), Min.ToString(CultureInfo.CurrentCulture));
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012104_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check the attribute //DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes.
        /// Each class shall be separated by a semicolon. One or more of the following class types can be used:
        /// "RT_CLASS_2"
        /// "RT_CLASS_3"
        /// These classes have to be supported @SupportedRT_Classes or @SupportedRT_Class.
        /// In this Function will be checked if there is a Node, where the attribute IsochroneModeInRT_Classes will be set while Supported RT_Classes is not present.
        /// </summary>
        protected virtual bool CheckCn_0X00012105()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/" +
                        "gsddef:InterfaceSubmoduleItem[@IsochroneModeInRT_Classes and not(@SupportedRT_Classes or @SupportedRT_Class)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var an in nl)
            {
                // "Attribute 'InterfaceSubmoduleItem/@SupportedRT_Classes' is not present,
                // while using 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'."
                string msg = Help.GetMessageString("M_0x00012105_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012105_1");
            }

            return true;
        }

        /// <summary>
        /// Check the attribute //DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes.
        /// Each class shall be separated by a semicolon. One or more of the following class types can be used:
        /// "RT_CLASS_2"
        /// "RT_CLASS_3"
        /// These classes have to be supported by @SupportedRT_Classes or @SupportedRT_Class.
        /// </summary>
        protected virtual bool CheckCn_0X00012105_2()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/" +
                        "gsddef:InterfaceSubmoduleItem[@IsochroneModeInRT_Classes and (@SupportedRT_Classes or @SupportedRT_Class)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var elem in nl)
            {
                XAttribute iochroneModeInRTClasses = elem.Attribute(Attributes.s_IsochroneModeInRTClasses);
                if (iochroneModeInRTClasses == null)
                {
                    continue;
                }

                var lineInfo = (IXmlLineInfo)iochroneModeInRTClasses;

                string isRTCl = Help.GetAttributeValueFromXElement(elem, Attributes.s_IsochroneModeInRTClasses);
                string suRTCl = Help.GetAttributeValueFromXElement(elem, Attributes.s_SupportedRTClasses);
                string rtClass = Help.GetAttributeValueFromXElement(elem, Attributes.s_SupportedRTClass);
                string[] arrIsRTCl = isRTCl.Split(';');
                foreach (string val in arrIsRTCl)
                {
                    if ((val == "RT_CLASS_UDP") || (val == "RT_CLASS_1"))
                    {
                        CreateReportMsg_0x00012105_2(val, elem, lineInfo);

                    }
                    else if (!String.IsNullOrEmpty(suRTCl))
                    {
                        CreateReport0x00012105_3(suRTCl, val, elem, lineInfo);

                    }
                    else
                    {
                        CreateReport0x00012105_4(val, rtClass, elem, lineInfo);

                    }
                }
            }
            return true;

        }

        private void CreateReport0x00012105_4(string val, string rtClass, XObject elem, IXmlLineInfo lineInfo)
        {
            if (((val == "RT_CLASS_2") && (rtClass == "Class2")) || ((val == "RT_CLASS_3") && (rtClass == "Class3")))
            {
                return;
            }

            // "Class type within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' attribute
            // is not supported in 'InterfaceSubmoduleItem/@SupportedRT_Class'."
            string msg = Help.GetMessageString("M_0x00012105_4");
            string xpath = Help.GetXPath(elem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012105_4");
        }

        private void CreateReport0x00012105_3(string su_rt_cl, string val, XObject elem, IXmlLineInfo lineInfo)
        {
            if (su_rt_cl.IndexOf(val, StringComparison.InvariantCultureIgnoreCase) >= 0)
            {
                return;
            }
            // "Class type within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' attribute
            // is not supported in 'InterfaceSubmoduleItem/@SupportedRT_Classes'."
            string msg = Help.GetMessageString("M_0x00012105_3");
            string xpath = Help.GetXPath(elem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012105_3");
        }
        private void CreateReportMsg_0x00012105_2(string val, XObject elem, IXmlLineInfo lineInfo)
        {

            // "Class type "{0}" contained within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'
            // attribute is not supported. Each class shall be separated by a semicolon. One or more of
            // the following class types can be used: "RT_CLASS_2" and or "RT_CLASS_3"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_" + Msg_0X000121052), val);
            string xpath = Help.GetXPath(elem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "Msg_0x00012105_2");
        }
        /// <summary>
        /// Check for F_IO_StructureDescVersion.
        /// </summary>
        protected virtual bool CheckCn_0X00012107()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodItem in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (pRofIsafeSupported)
                {
                    bool isVersion2Needed = IsVersion2Needed(submodItem);

                    var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
                    string fIOStructureDescVersion = Help.GetAttributeValueFromXElement(ioData, Attributes.s_FIOStructureDescVersion);

                    if (string.IsNullOrEmpty(fIOStructureDescVersion))
                        fIOStructureDescVersion = Constants.s_Number1;

                    if (isVersion2Needed && fIOStructureDescVersion != Constants.s_Number2)
                    {
                        if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                        {
                            string msg = Help.GetMessageString("M_0x00012107_1");
                            string xpath = Help.GetXPath(submodItem);
                            var xli = (IXmlLineInfo)submodItem;
                            // "A 'DataItem' with the 'DataType' "Integer32" or "Unsigned8+Unsigned8" is used.
                            // That requires a 'F_IO_StructureDescVersion' with a value greater or equal than 2."
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012107_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_Check_iPar.
        /// </summary>
        protected virtual bool CheckCn_0X00012109()
        {
            const string Xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_Check_iPar";
            var nl = GsdProfileBody.XPathSelectElements(Xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fCheckiPar in nl)
            {
                // Get the attribute 'Visible'
                bool visible = false; // Default value
                string strVisible = Help.GetAttributeValueFromXElement(fCheckiPar, Attributes.s_Visible);
                if (!String.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                // If 'Visible' is not set, the default works for DefaultValue and AllowedValues.
                // Therefore in this case a warning is reported, otherwise error.
                ReportTypes reportType = ReportTypes.GSD_RT_Error;
                if (!visible)
                    reportType = ReportTypes.GSD_RT_Warning;

                var aAllowedValues = fCheckiPar.Attribute(Attributes.s_AllowedValues);
                if (aAllowedValues == null)
                    continue;
                string allowedValues = aAllowedValues.Value;

                string defaultValue = Help.GetAttributeValueFromXElement(fCheckiPar, Attributes.s_DefaultValue);
                if (String.IsNullOrEmpty(defaultValue))
                    defaultValue = Enums.s_NoCheck;

                if (!allowedValues.Contains(defaultValue))
                {
                    // "'F_ParameterRecordDataItem/F_Check_iPar': The default value (= {0}) is not contained in allowed values (= {1})."
                    if (Help.CheckSchemaVersion(fCheckiPar, SupportedGsdmlVersion))
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012109_1"), defaultValue, allowedValues);
                        string xpath = Help.GetXPath(fCheckiPar);
                        var xli = (IXmlLineInfo)fCheckiPar;
                        Store.CreateAndAnnounceReport(reportType, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012109_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_Block_ID.
        /// </summary>
        protected virtual bool CheckCn_0X00012108()
        {
            const string Xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_Block_ID";
            var nl = GsdProfileBody.XPathSelectElements(Xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fBlockID in nl)
            {
                // Get the attribute 'Visible'
                bool visible = true; // Default value
                string strVisible = Help.GetAttributeValueFromXElement(fBlockID, Attributes.s_Visible);
                if (!String.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                // If 'Visible' is not set, the default works for DefaultValue and AllowedValues.
                // Therefore in this case a warning is reported, otherwise error.
                ReportTypes reportType = ReportTypes.GSD_RT_Error;
                if (!visible)
                    reportType = ReportTypes.GSD_RT_Warning;

                string strDefaultValue = Help.GetAttributeValueFromXElement(fBlockID, Attributes.s_DefaultValue);

                UInt32 defaultValue = 0;
                if (!string.IsNullOrEmpty(strDefaultValue))
                    defaultValue = (UInt32)XmlConvert.ToInt32(strDefaultValue); // This (double)cast is important to convert -0

                bool valid = FindAllowedValues(fBlockID, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                var lineInfo = (IXmlLineInfo)fBlockID;
                CreateReport0x00012108_1(defaultValue, allowedValuesFrom, allowedValuesTo, fBlockID, reportType, lineInfo);
                CreateReport0x00012108_2(fBlockID, allowedValues, visible, lineInfo);

            }

            return true;
        }

        private void CreateReport0x00012108_2(XObject fBlockID, IReadOnlyList<ValueListHelper.ValueRangeT> allowedValues, bool visible, IXmlLineInfo lineInfo)
        {
            XElement fparamRecordDataItem = fBlockID.Parent;
            if (fparamRecordDataItem != null && ((fparamRecordDataItem.Element(NamespaceGsdDef + Elements.s_FIParCrc)) == null
                || IsBit1Set(allowedValues)))
            {
                return;
            }
            // If 'Visible' is not set, the default works for AllowedValues (=0..7) and no error must be reported.
            if (!visible)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(fBlockID, SupportedGsdmlVersion))
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_Block_ID': If none of the values 1, 3, 5 or 7
            //  is contained in 'AllowedValues', 'F_iPar_CRC' must not be given."
            string msg = Help.GetMessageString("M_0x00012108_2");
            string xpath = Help.GetXPath(fBlockID);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012108_2");
        }

        private void CreateReport0x00012108_1(
         uint defaultValue,
         uint allowedValuesFrom,
         uint allowedValuesTo,
         XObject fBlockID,
         ReportTypes reportType,
         IXmlLineInfo lineInfo)
        {
            if (defaultValue >= allowedValuesFrom && defaultValue <= allowedValuesTo)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(fBlockID, SupportedGsdmlVersion))
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_Block_ID': The default value (= {0}) is not contained in allowed values (= {1}..{2})."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00012108_1"),
                defaultValue,
                allowedValuesFrom,
                allowedValuesTo);
            string xpath = Help.GetXPath(fBlockID);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012108_1");


        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_Source_Add.
        /// </summary>
        protected virtual bool CheckCn_0X00012110()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_Source_Add";
            var nl = GsdProfileBody.XPathSelectElements(xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fsourceAdd in nl)
            {
                bool valid = FindAllowedValues(fsourceAdd, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                string strDefaultValue = Help.GetAttributeValueFromXElement(fsourceAdd, Attributes.s_DefaultValue);

                UInt32 defaultValue = 1;
                if (!string.IsNullOrEmpty(strDefaultValue))
                    defaultValue = XmlConvert.ToUInt32(strDefaultValue);

                if (defaultValue < allowedValuesFrom || defaultValue > allowedValuesTo)
                {
                    if (Help.CheckSchemaVersion(fsourceAdd, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/F_Source_Add': The default value (= {0}) is not contained in allowed values (= {1}..{2})."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012110_1"), defaultValue, allowedValuesFrom, allowedValuesTo);
                        string xpath = Help.GetXPath(fsourceAdd);
                        var xli = (IXmlLineInfo)fsourceAdd;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012110_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// A F_ParameterRecordDataItem element must have the attribute
        /// F_ParamDescCRC. Due to an error in the GSDML schema this has
        /// to checked here.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X0001211D()
        {
            string xp = ".//gsddef:F_ParameterRecordDataItem[not(@F_ParamDescCRC)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var el in nl)
            {
                // "The attribute 'F_ParamDescCRC' is missing."
                string msg = Help.GetMessageString("M_0x0001211D_1");
                string xpath = Help.GetXPath(el);
                var xli = (IXmlLineInfo)el;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001211D_1");
            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_Dest_Add.
        /// </summary>
        protected virtual bool CheckCn_0X00012111()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_Dest_Add";
            var nl = GsdProfileBody.XPathSelectElements(xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fdestAdd in nl)
            {
                bool valid = FindAllowedValues(fdestAdd, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                string strDefaultValue = Help.GetAttributeValueFromXElement(fdestAdd, Attributes.s_DefaultValue);

                UInt32 defaultValue = 1;
                if (!string.IsNullOrEmpty(strDefaultValue))
                    defaultValue = XmlConvert.ToUInt32(strDefaultValue);

                if (defaultValue < allowedValuesFrom || defaultValue > allowedValuesTo)
                {
                    if (Help.CheckSchemaVersion(fdestAdd, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/F_Dest_Add': The default value (= {0}) is not contained in allowed values (= {1}..{2})."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012111_1"), defaultValue, allowedValuesFrom, allowedValuesTo);
                        string xpath = Help.GetXPath(fdestAdd);
                        var xli = (IXmlLineInfo)fdestAdd;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012111_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_Par_CRC.
        /// </summary>
        protected virtual bool CheckCn_0X00012112()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_Par_CRC";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fparCrc in nl)
            {
                
                bool valid = FindAllowedValues(fparCrc, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                string strDefaultValue = Help.GetAttributeValueFromXElement(fparCrc, Attributes.s_DefaultValue);

                UInt32 defaultValue = 53356;
                if (!string.IsNullOrEmpty(strDefaultValue))
                    defaultValue = XmlConvert.ToUInt32(strDefaultValue);

                if (defaultValue < allowedValuesFrom || defaultValue > allowedValuesTo)
                {
                    if (Help.CheckSchemaVersion(fparCrc, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/F_Par_CRC': The default value (= {0}) is not contained in allowed values (= {1}..{2})."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012112_1"), defaultValue, allowedValuesFrom, allowedValuesTo);
                        string xpath = Help.GetXPath(fparCrc);
                        var xli = (IXmlLineInfo)fparCrc;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00012112_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Validate the F_ParamDescCRC.
        /// </summary>
        protected override bool CheckCn_0X00010128_2()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            Crc16 calcFParamDescCrc = new Crc16();

            foreach (var fParamRecord in nl)
            {
                // Init the F_ParamDescCRC
                calcFParamDescCrc.InitChecksum();

                // Collect the bytes for checksum
                CollectBytesFParamDescCrc(fParamRecord, calcFParamDescCrc);

                // Finish the checksum process, returning the CRC
                UInt16 calculatedFParamDescCrc = calcFParamDescCrc.FinishChecksum(true);

                // Get the F_ParamDescCRC from Xml file
                UInt16 fparamDescCrc = 0;
                XAttribute nodeFParamDescCrc = fParamRecord.Attribute(Attributes.s_FParamDescCrc);
                XObject node = fParamRecord;
                if (nodeFParamDescCrc != null)
                {
                    fparamDescCrc = XmlConvert.ToUInt16(nodeFParamDescCrc.Value);
                    node = nodeFParamDescCrc;
                }

                if (calculatedFParamDescCrc == fparamDescCrc)
                {
                    continue;
                }
                var xli = (IXmlLineInfo)node;

                // For GSDML V2.2 STEP7/ComPROFIsafe calculates wrong values for F_Block_ID/@AllowedValues != 0..7
                if (SupportedGsdmlVersion == Constants.s_Version22)
                {
                    // Init the F_ParamDescCRC
                    calcFParamDescCrc.InitChecksum();

                    // Collect the bytes for checksum - faked version
                    CollectBytesFParamDescCrcFaked(fParamRecord, calcFParamDescCrc);

                    // Finish the checksum process, returning the CRC
                    UInt16 calculatedFParamDescCrcFaked = calcFParamDescCrc.FinishChecksum(true);

                    if (calculatedFParamDescCrcFaked == fparamDescCrc)
                    {
                        continue;
                    }
                    if (!Help.CheckSchemaVersion(fParamRecord, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "'F_ParameterRecordDataItem/@F_ParamDescCRC': The CRC over all F-parameters is {0} but should be {1}."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010128_2"), fparamDescCrc, calculatedFParamDescCrc);
                    string xpath = Help.GetXPath(node);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010128_2");


                }
                else
                {
                    if (!Help.CheckSchemaVersion(fParamRecord, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "'F_ParameterRecordDataItem/@F_ParamDescCRC': The CRC over all F-parameters is {0} but should be {1}."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010128_2"), fparamDescCrc, calculatedFParamDescCrc);
                    string xpath = Help.GetXPath(node);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010128_2");

                }

            }

            return true;
        }

        /// <summary>
        /// IsAdjustMAUTypeSupported
        /// Checks for a 'PortSubmoduleItem' if it has a 'MAUTypes' element.
        /// 
        /// </summary>
        /// <returns>True, if 'MAUTypes' is present.</returns>
        protected virtual bool IsAdjustMauTypeSupported(XElement portSubmoduleItem)
        {
            XAttribute mauTypes = portSubmoduleItem.Attribute(Attributes.s_MauTypes);

            if (null == mauTypes)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// When 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", 'PowerOnToCommReady' on the DAP must be >0.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00012113()
        {
            // Find all interface submodules with DCP_HelloSupported = "true"
            var submodules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            submodules = Help.TryRemoveXElementsUnderXsAny(submodules, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in submodules)
            {
                string strDcpHelloSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_DcpHelloSupported);
                bool bDcpHelloSupported = false;
                if (!string.IsNullOrEmpty(strDcpHelloSupported))
                    bDcpHelloSupported = XmlConvert.ToBoolean(strDcpHelloSupported);
                if (!bDcpHelloSupported)
                {
                    continue;
                }

                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }
                var dap = interfaceSubmoduleItem.Parent.Parent;
                double version = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                ReportTypes reportType_0X00012113 = ReportTypes.GSD_RT_Warning;
                if (version >= 2.31)
                    reportType_0X00012113 = ReportTypes.GSD_RT_Error;

                string powerOnToCommReadyStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_PowerOnToCommReady);
                if (!UInt32.TryParse(powerOnToCommReadyStr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 powerOnToCommReady))
                    powerOnToCommReady = 0;
                CreateReport0x00012113_1(powerOnToCommReady, interfaceSubmoduleItem, reportType_0X00012113);

                CreateReport0x00012113_2(dap, reportType_0X00012113);

            }

            return true;
        }
        private void CreateReport0x00012113_2(XElement dap, ReportTypes reportType_0X00012113)
        {
            if (!DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
            {
                return;
            }
            foreach (var portSubmoduleItem in portSubmoduleItems)
            {
                if (IsAdjustMauTypeSupported(portSubmoduleItem))
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(portSubmoduleItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "FSU (Fast Startup) can only work if autonegotiation can be turned off. That means, when
                // 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", for all 'PortSubmoduleItem' elements
                // pluggable on the DAP (ID = "{0}") 'MAUTypes' must be given, so that PDPortDataAdjust.AdjustMAUType
                // is supported and autonegotiation can be switched off."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_" + Msg_0X000121132),
                    Help.GetAttributeValueFromXElement(dap, Attributes.ID));
                string xpath = Help.GetXPath(portSubmoduleItem);
                var xli = (IXmlLineInfo)portSubmoduleItem;
                Store.CreateAndAnnounceReport(
                    reportType_0X00012113,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    Msg_0X000121132);

            }

        }

        private void CreateReport0x00012113_1(
         uint powerOnToCommReady,
         XObject interfaceSubmoduleItem,
         ReportTypes reportType_0X00012113)
        {
            if (powerOnToCommReady != 0)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(interfaceSubmoduleItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "When 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", 'PowerOnToCommReady' on the DAP must be >0."
            string msg = Help.GetMessageString("M_0x00012113_1");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            var xli = (IXmlLineInfo)interfaceSubmoduleItem;
            Store.CreateAndAnnounceReport(
                reportType_0X00012113,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012113_1");
        }

        /// <summary>
        /// The DefaultValue and AllowedValues attributes of the F_ParameterRecordDataItem/F_Par_Version element are to be checked.
        /// Check as of GSDML V2.2 (the element did not exist before).
        /// 
        /// Both DefaultValue and AllowedValues must have the value "1", otherwise error.
        /// 
        /// Background:
        /// F_Par_Version contains the PROFIsafe mode.
        /// The value 0 stands for v1-mode, the value 1 stands for v2-mode. The possible values 2 and 3 are undefined.
        /// v1-mode was only available with PROFIBUS. v2-mode is available with PROFIBUS and PROFINET.
        /// I.e. in a PROFINET GSD today only the value 1 can stand.
        ///  
        /// The check shall be effective until GSDML V2.31, after that the scheme is more strict and
        /// then this check is no longer necessary. 
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00012114()
        {
            const string Xp = ".//gsddef:F_ParameterRecordDataItem/gsddef:F_Par_Version[@DefaultValue or @AllowedValues]";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fParVersion in nl)
            {
                var lineInfo = (IXmlLineInfo)fParVersion;

                // Default value
                string strFParVersion = Help.GetAttributeValueFromXElement(fParVersion, Attributes.s_DefaultValue);
                if (!String.IsNullOrEmpty(strFParVersion))
                {
                    UInt16 dFParVersion = (UInt16)XmlConvert.ToInt16(strFParVersion); // This (double)cast is important to convert -0
                    if (dFParVersion != 1)
                    {
                        if (Help.CheckSchemaVersion(fParVersion, SupportedGsdmlVersion))
                        {
                            // "'F_ParameterRecordDataItem/F_Par_Version/@DefaultValue' must be 1 for v2-mode (PROFINET)."
                            string msg = Help.GetMessageString("M_0x00012114_1");
                            string xpath = Help.GetXPath(fParVersion);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x00012114_1");
                        }
                    }
                }

                // Allowed values
                bool valid = FindAllowedValues(fParVersion, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                if (allowedValuesFrom != 1 || allowedValuesTo != 1)
                {
                    if (Help.CheckSchemaVersion(fParVersion, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/F_Par_Version/@AllowedValues' must be 1 for v2-mode (PROFINET)."
                        string msg = Help.GetMessageString("M_0x00012114_2");
                        string xpath = Help.GetXPath(fParVersion);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00012114_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00012115
        /// Values for 'F_Block_ID/@AllowedValues' attribute must be in the range of 0 to 7.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012115()
        {
            // Range
            const uint Max = 7;
            const uint Min = 0;


            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FBlockID).Attributes(Attributes.s_AllowedValues);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

            foreach (XAttribute an in nl)
            {
                // Get the attribute 'Visible' for F_Block_ID
                var fBlockId = an.Parent;
                bool visible = true;   // Default value
                string strVisible = Help.GetAttributeValueFromXElement(fBlockId, Attributes.s_Visible);
                if (!String.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                // If 'Visible' is not set, the default works for AllowedValues.
                // Therefore in this case a warning is reported, otherwise error.
                GSDI.ReportTypes reportType = GSDI.ReportTypes.GSD_RT_Error;
                if (!visible)
                    reportType = GSDI.ReportTypes.GSD_RT_Warning;


                // Split incoming string to individual numbers and areas in a list.
                List<ValueListHelper.ValueRangeT> splitlist = ValueListHelper.NormalizeValueList(an, Store);

                for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
                {
                    if (splitlist[currentRange].From < Min || splitlist[currentRange].To > Max)
                    {
                        if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                        {
                            // "Values for 'F_Block_ID/@AllowedValues' attribute must be in the range of 0 to 7."
                            string msg = Help.GetMessageString("M_0x00012115_1");
                            string xpath = Help.GetXPath(an);
                            var xli = (IXmlLineInfo)an;
                            Store.CreateAndAnnounceReport(reportType, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x00012115_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00012116
        /// Check MaxSupportedRecordSize against RecordSize of each single Submodule:
        /// For each Submodule, iterate over all elements ParameterRecordDataItems in the
        /// RecordDataList and check it against MaxSupportedRecordSize (or its default value 4068).
        /// For pluggable (Port)Submodules the minimum of MaxSupportedRecordSize over all DeviceAccessPoints
        /// must be calculated.
        /// Severity is ERROR.
        ///
        /// Note: The F-ParameterRecord is always smaller and therefore does not need to be checked.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00012116()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                UInt32 maxSupportedRecordSize = 4068;
                string strMaxSupportedRecordSize = Help.GetAttributeValueFromXElement(dap, Attributes.s_MaxSupportedRecordSize);
                if (!string.IsNullOrEmpty(strMaxSupportedRecordSize))
                    maxSupportedRecordSize = XmlConvert.ToUInt32(strMaxSupportedRecordSize);
                

                // (1) Check the InterfaceSubmoduleItem at the DAP
                var interfaceSubmoduleItem = dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();
                if (interfaceSubmoduleItem != null)
                {
                    CheckEachParameterRecordInSubmodule(maxSupportedRecordSize, interfaceSubmoduleItem);
                }

                // (2) Check all VirtualSubmoduleItems at the DAP
                var virtualSubmoduleItems = dap.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
                {
                    CheckEachParameterRecordInSubmodule(maxSupportedRecordSize, virtualSubmoduleItem);
                }
            }

            // (3) Check all PortSubmoduleItems
            CheckAllPortSubmoduleItems();


            // (4) Check all SubmoduleItems
            CheckAllSubmoduleItems();


            return true;
        }
        private void CheckAllSubmoduleItems()
        {
            foreach (var entry in SubmoduleToDapDictionary)
            {
                UInt32 minOfMaxSupportedRecordSize = 0;
                XElement submodule = entry.Key;
                IList<XElement> dapsOfSubmodule = entry.Value;
                foreach (XElement dap in dapsOfSubmodule)
                {
                    UInt32 maxSupportedRecordSize = 4068;
                    string strMaxSupportedRecordSize = Help.GetAttributeValueFromXElement(dap, Attributes.s_MaxSupportedRecordSize);
                    if (!string.IsNullOrEmpty(strMaxSupportedRecordSize))
                        maxSupportedRecordSize = XmlConvert.ToUInt32(strMaxSupportedRecordSize);
                    

                    if (0 == minOfMaxSupportedRecordSize)
                        minOfMaxSupportedRecordSize = maxSupportedRecordSize;
                    

                    if (maxSupportedRecordSize < minOfMaxSupportedRecordSize)
                        minOfMaxSupportedRecordSize = maxSupportedRecordSize;
                    
                }
                CheckEachParameterRecordInSubmodule(minOfMaxSupportedRecordSize, submodule);
            }
        }
        private void CheckAllPortSubmoduleItems()
        {

            foreach (var entry in PortToDapDictionary)
            {
                UInt32 minOfMaxSupportedRecordSize = 0;
                XElement port = entry.Key;
                IList<XElement> dapsOfPort = entry.Value;
                foreach (XElement dap in dapsOfPort)
                {
                    UInt32 maxSupportedRecordSize = 4068;
                    string strMaxSupportedRecordSize = Help.GetAttributeValueFromXElement(dap, Attributes.s_MaxSupportedRecordSize);
                    if (!string.IsNullOrEmpty(strMaxSupportedRecordSize))
                        maxSupportedRecordSize = XmlConvert.ToUInt32(strMaxSupportedRecordSize);
                    

                    if (0 == minOfMaxSupportedRecordSize)
                        minOfMaxSupportedRecordSize = maxSupportedRecordSize;
                    

                    if (maxSupportedRecordSize < minOfMaxSupportedRecordSize)
                        minOfMaxSupportedRecordSize = maxSupportedRecordSize;
                    
                }

                CheckEachParameterRecordInSubmodule(minOfMaxSupportedRecordSize, port);
            }
        }
        void CheckEachParameterRecordInSubmodule(UInt32 maxSupportedRecordSize, XContainer submoduleItem)
        {
            var parameterRecordDataItems = submoduleItem.Descendants(NamespaceGsdDef + Elements.s_ParameterRecordDataItem);
            foreach (var parameterRecordDataItem in parameterRecordDataItems)
            {
                UInt32 recordDataLength = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(parameterRecordDataItem, Attributes.s_Length));
                if (maxSupportedRecordSize < recordDataLength)
                {
                    if (Help.CheckSchemaVersion(parameterRecordDataItem, SupportedGsdmlVersion))
                    {
                        // "The length of the ParameterRecordDataItem must not exceed the MaxSupportedRecordSize of {0}."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012116_1"), maxSupportedRecordSize);
                        string xpath = Help.GetXPath(parameterRecordDataItem);
                        var xli = (IXmlLineInfo)parameterRecordDataItem;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00012116_1");
                    }
                }
            }
        }
        #endregion
    }
}


