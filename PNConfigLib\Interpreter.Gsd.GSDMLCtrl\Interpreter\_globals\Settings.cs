/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Settings.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all Settings for the Interpreter component.
    /// </summary>
    internal static class Settings
    {
        #region Config File Settings
        // Contains all config file settings as constants

        #region Check Settings

        #region Default Entries

        /// <summary>default value for the SchemaFile entry</summary>
        public const string DefaultSchemaFile = "gsdml-deviceprofile-v1.0.xsd";
        /// <summary>default value for the SchemaPath entry</summary>
        public const string DefaultSchemaPath = @".\xsd";
        /// <summary>default value for the Validate entry</summary>
        public const bool DefaultValidate = true;
        /// <summary>default value for the MaxErrorCount entry</summary>
        public const uint DefaultMaxErrorCount = 500;

        #endregion

        #region Concrete Settings
        /// <summary>Concrete value, getted from the config file, for the SchemaFile entry. 
        /// It specifies the name of the schema file, which should be used for validation.</summary>
        public static readonly string SchemaFile = GetSchemaFile();
        /// <summary>Concrete value, getted from the config file, for the SchemaPath entry. 
        /// It specifies the path from which the schema files would be loaded, to validate GSD files.</summary>
        public static readonly string SchemaPath = GetSchemaPath();
        /// <summary>Concrete value, getted from the config file, for the Validate entry. 
        /// It specifies whether the schema validation should be done (true), or not (false).</summary>
        public static readonly bool Validate = GetValidate();
        /// <summary>Concrete value, getted from the config file, for the MaxErrorCount entry.  
        /// It specifies the maximal number of errors, which will be accepted from the checker, 
        /// before the check is aborted.</summary>
        public static readonly uint MaxErrorCount = GetMaxErrorCount();

        #endregion

        #region Getter Methods

        /// <summary>
        /// Accesses the SchemaFile entry of the config file. If no config file
        /// entry is available, the default value for it is returned.
        /// </summary>
        /// <returns>The name of the schema file which should be used for validation.</returns>
        private static string GetSchemaFile()
        {
            return DefaultSchemaFile;
        }

        /// <summary>
        /// Accesses the SchemaPath entry of the config file. If no config file
        /// entry is available, the default value for it is returned.
        /// </summary>
        /// <returns>The path where the schema file could be found.</returns>
        private static string GetSchemaPath()
        {
            return DefaultSchemaPath;
        }

        /// <summary>
        /// Accesses the Validate entry of the config file. If no config file
        /// entry is available, the default value for it is returned.
        /// </summary>
        /// <returns>True if GSDML files should be validated, else false.</returns>
        private static bool GetValidate()
        {
            return DefaultValidate;
        }

        /// <summary>
        /// Accesses the MaxErrorCount entry of the config file. If no config file
        /// entry is available, the default value for it is returned.
        /// </summary>
        /// <returns>Maximal number of errors, which should be stored as report.</returns>
        private static uint GetMaxErrorCount()
        {
            return DefaultMaxErrorCount;
        }

        #endregion

        #endregion

        #region Export Settings

        #region Default Entries

        /// <summary>default value for the Indentation entry</summary>
        public const bool DefaultIndentation = true;

        #endregion

        #region Concrete Settings

        /// <summary>Concrete value, getted from the config file, for the Indentation entry. 
        /// It specifies whether the export result XML file would be indented (true), or not (false).</summary>
        public static readonly bool Indentation = GetIndentation();

        #endregion

        #region Getter Methods

        /// <summary>
        /// Accesses the Indentation entry of the config file. If no config file
        /// entry is available, the default value for it is returned.
        /// </summary>
        /// <returns>True if export files should be indented, else false.</returns>
        private static bool GetIndentation()
        {
            return DefaultIndentation;
        }

        #endregion

        #endregion

        #endregion
    }
}


