/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SharedDeviceUtility.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Contains methods and utilities used for accessing attributes related to shared device feature and relevant values.
    /// </summary>
    internal static class SharedDeviceUtility
    {
        /// <summary>
        /// Get the first not shared module
        /// Auxiliary method exclusively for Utility.GetSlotNumber method because Shared device functional expansion
        /// </summary>
        /// <param name="modules">ConfigObject list of modules cast to object (caller method input parameter)</param>
        /// <returns>CoreObject of the module where not shared submodule found</returns>
        public static PclObject GetFirstNotSharedModule(IList<PclObject> modules)
        {
            if (modules == null)
            {
                throw new ArgumentNullException(nameof(modules));
            }

            PclObject firstNotSharedModule = null;
            bool notSharedModulFound = false;

            foreach (PclObject module in modules)
            {
                if (module is DecentralDevice)
                {
                    AttributeAccessCode ac = new AttributeAccessCode();
                    bool isSharedDeviceSupported = module.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoSharedDeviceSupported,
                        ac,
                        false);
                    if (isSharedDeviceSupported)
                    {
                        notSharedModulFound = FindSubModule(module, SharedIoAssignment.None, out firstNotSharedModule);
                    }
                    else
                    {
                        firstNotSharedModule = module;
                        break;
                    }
                }
                else if (module is Module)
                {
                    notSharedModulFound = FindSubModule(module, SharedIoAssignment.None, out firstNotSharedModule);
                }

                if (notSharedModulFound)
                {
                    return module;
                }
            }
            return firstNotSharedModule;
        }

        /// <summary>
        /// Check head module assignment (head module shared or not)
        /// </summary>
        /// <param name="interfaceSubmodule">interface submodule of the headmodule</param>
        /// <returns>With true if head module shared</returns>
        public static bool IsHeadModuleShared(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return false;
            }

            DecentralDevice headModule = NavigationUtilities.GetHeadmodule(interfaceSubmodule);

            if (headModule == null)
            {
                return false;
            }

            SharedIoAssignment assignment = GetSharedAccess(headModule);

            return assignment == SharedIoAssignment.NotAssigned;
        }

        /// <summary>
        /// Get the user adjusted number of external controllers which are assigned to the shared capable IODevice
        /// if the particular IOController has full access to the PDEV submodules of the IODevice.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The controller interface submodule that will be checked whether the PDEV
        /// device interface is assigned to.
        /// </param>
        /// <param name="deviceInterfaceSubmodule">
        /// The device interface submodule whose number of external controllers will be
        /// retrieved.
        /// </param>
        /// <exception cref="ArgumentNullException">if controllerInterfaceSubmodule or deviceInterfaceSubmodule is null.</exception>
        /// <returns>
        /// The number of external controllers that the device interface is assigned to.
        /// 0 if PNIoNumberOfAR attribute can not be retrieved, the controller or device is not shared device capable
        /// or PDEV device interface is not assigned to the given controller interface.
        /// </returns>
        internal static uint GetNumberOfARs(PclObject controllerInterfaceSubmodule, PclObject deviceInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(controllerInterfaceSubmodule));
            }

            if (deviceInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(deviceInterfaceSubmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            uint numberOfARs =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoNumberOfAR,
                    ac,
                    0);

            // Check the numberOfARs first, because it will be null most of the time
            // when the configuration is not shared capable -> minimal get operation and navigation 
            if (numberOfARs > 0)
            {
                bool isControllerSharedCapable =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoSharedDeviceAssignmentSupp,
                        ac.GetNew(),
                        false);

                if (isControllerSharedCapable)
                {
                    // Get the interface assignment only, because the assignment of PDEV submodules should be always the same 
                    // (Ports, Interface)
                    SharedIoAssignment sharedIoAssignment =
                        (SharedIoAssignment)
                        deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.SharedIoAssignment,
                            ac.GetNew(),
                            (uint)SharedIoAssignment.None);

                    // PDEV device interface is assigned to the controller
                    if (sharedIoAssignment == SharedIoAssignment.None)
                    {
                        bool isDeviceSharedCapable = false;
                        // Get the headmodule
                        PclObject deviceHeadModule = deviceInterfaceSubmodule.GetDevice();
                        if (deviceHeadModule != null)
                        {
                            isDeviceSharedCapable =
                                deviceHeadModule.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.PnIoSharedDeviceSupported,
                                    ac.GetNew(),
                                    false);
                        }
                        // IODevice is shared capable
                        if (isDeviceSharedCapable)
                        {
                            return numberOfARs;
                        }
                        return 0;
                    }
                    return 0;
                }
                return 0;
            }
            return 0;
        }

        /// <summary>
        /// Gets the shared assignment value of a given module/submodule.
        /// </summary>
        /// <param name="sharedObject">The module/submodule whose shared assignment value will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if sharedObject is null.</exception>
        /// <returns>
        /// SharedIoAssignment.None when "assigned to controller" is set;
        /// SharedIoAssigment.NotAssigned when "not assigned to controller" is set.
        /// </returns>
        internal static SharedIoAssignment GetSharedAccess(PclObject sharedObject)
        {
            if (sharedObject == null)
            {
                throw new ArgumentNullException(nameof(sharedObject));
            }

            uint assignmentValue = (uint)SharedIoAssignment.None;
            IEnumerable<PclObject> submodules = sharedObject.GetElements();
            bool hasInterfaceSubmodule;
            bool hasAssignedSubmodule = HasAssignedSubmodule(sharedObject, submodules, out hasInterfaceSubmodule);
            AttributeAccessCode ac = new AttributeAccessCode();
            if (sharedObject is DecentralDevice)
            {
                bool isSupported =
                    sharedObject.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoSharedDeviceSupported,
                        ac,
                        false);

                if (!ac.IsOkay
                    || !isSupported)
                {
                    return (SharedIoAssignment)assignmentValue;
                }
                assignmentValue =
                    sharedObject.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        ac.GetNew(),
                        (uint)SharedIoAssignment.None);
            }
            // If it is a module with one submodule, then return with the parameter of the submodule.
            else if (sharedObject is Module
                     && submodules.Count() == 1)
            {
                assignmentValue =
                    submodules.First()
                        .AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.SharedIoAssignment,
                            ac,
                            (uint)SharedIoAssignment.None);
            }
            // If it is a module with more than 1 submodule, then if all submodules are not assigned,
            // returns with not assigned, otherwise returns assigned.
            else if (sharedObject is Module
                     && (submodules.Count() > 1)
                     && !hasAssignedSubmodule)
            {
                assignmentValue = (uint)SharedIoAssignment.NotAssigned;
            }

            else if (sharedObject is Module
                     || sharedObject is Submodule
                     || sharedObject is Interface)
            {
                assignmentValue =
                    sharedObject.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        ac.GetNew(),
                        (uint)SharedIoAssignment.None);
            }

            return (SharedIoAssignment)assignmentValue;
        }

        /// <summary>
        /// Returns the list of the shared submodules on a device.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The device interface whose shared submodules will be retrieved.</param>
        /// <returns>
        /// List of shared submodules on the given device interface.
        /// Empty list if deviceInterfaceSubmodule is null, device does not support shared device feature
        /// or PNIoSharedDeviceSupported attribute can not be retrieved.
        /// </returns>
        internal static List<PclObject> GetSharedSubmodules(Interface deviceInterfaceSubmodule)
        {
            List<PclObject> sharedSubmodules = new List<PclObject>();

            DecentralDevice device = deviceInterfaceSubmodule.GetDevice() as DecentralDevice;

            if (device == null)
            {
                return sharedSubmodules;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            bool isSharedDeviceSupported =
                device.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnIoSharedDeviceSupported, ac, false);

            if (!ac.IsOkay
                || !isSharedDeviceSupported)
            {
                return sharedSubmodules;
            }

            foreach (Module module in device.GetModules())
            {
                SharedIoAssignment sharedIoAssigment =
                    (SharedIoAssignment)
                    module.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        ac.GetNew(),
                        (uint)SharedIoAssignment.None);

                if (sharedIoAssigment != SharedIoAssignment.NotAssigned)
                {
                    continue;
                }

                sharedSubmodules.Add(module);
            }

            return sharedSubmodules;
        }

        /// <summary>
        /// Gets whether there is a shared submodule that is set to not assigned.
        /// </summary>
        /// <param name="headmodule">The head module whose submodules will be checked.</param>
        /// <exception cref="ArgumentNullException">if headmodule is null.</exception>
        /// <returns>
        /// True if the device has a submodule that is not assigned.
        /// False if device does not support shared device or does not have a submodule that is not assigned.
        /// </returns>
        internal static bool HasNotAssignedSubmodule(DecentralDevice headmodule)
        {
            if (headmodule == null)
            {
                throw new ArgumentNullException(nameof(headmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            bool hasNotAssignedSubmodule = false;
            bool isSharedDeviceSupported =
                headmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceSupported,
                    ac,
                    false);

            if (!isSharedDeviceSupported)
            {
                return false;
            }

            foreach (PclObject deviceItem in headmodule.GetDeviceItems())
            {
                uint assignmentValue =
                    deviceItem.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        ac.GetNew(),
                        (uint)SharedIoAssignment.None);

                if ((SharedIoAssignment)assignmentValue == SharedIoAssignment.NotAssigned)
                {
                    hasNotAssignedSubmodule = true;
                    break;
                }
            }

            return hasNotAssignedSubmodule;
        }

        /// <summary>
        /// Gets the PNParameterizationDisallowed attribute value of a given shared object.
        /// </summary>
        /// <param name="sharedObject">The object whose PNParameterizationDisallowed attribute value will be retrieved.</param>
        /// <returns>
        /// PNParameterizationDisallowed attribute value of sharedObject;
        /// false if the attribute can not be retrieved or sharedObject is not a submodule or PDEV.
        /// </returns>
        internal static bool IsParameterizationDisallowed(PclObject sharedObject)
        {
            if (IsPDEVSubmodule(sharedObject))
            {
                return
                    sharedObject.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnParameterizationDisallowed,
                        new AttributeAccessCode(),
                        false);
            }

            return false;
        }

        /// <summary>
        /// Gets whether a given object is a submodule and PDEV.
        /// </summary>
        /// <param name="submodule">The object to be checked.</param>
        /// <exception cref="ArgumentNullException">if submodule is null.</exception>
        /// <returns>True if object is an Interface, Port or Submodule and is a PDEV; false otherwise.</returns>
        private static bool IsPDEVSubmodule(PclObject submodule)
        {
            if (submodule == null)
            {
                throw new ArgumentNullException(nameof(submodule));
            }

            bool isPDEV = false;

            if (submodule is Interface
                || submodule is DataModel.PCLObjects.Port
                || submodule is Submodule)
            {
                int subslotNumber =
                    submodule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnSubslotNumber,
                        new AttributeAccessCode(),
                        0);

                if ((subslotNumber >= PNConstants.PDEVSubmoduleSubslotNumberMinimum)
                    && (subslotNumber <= PNConstants.PDEVSubmoduleSubslotNumberMaximum))
                {
                    isPDEV = true;
                }
            }

            return isPDEV;
        }

        private static bool HasAssignedSubmodule(
            PclObject sharedObject,
            IEnumerable<PclObject> submodules,
            out bool hasInterfaceSubmodule)
        {
            bool hasInterface = false;
            bool hasAssignedSubmodule = submodules.Any(
                submodule =>
                    {
                        // Ignore interface submodule when getting shared access of the headmodule.
                        if (sharedObject is DecentralDevice
                            && submodule is Interface)
                        {
                            hasInterface = true;
                            return false;
                        }

                        AttributeAccessCode ac = new AttributeAccessCode();
                        uint elementAssignmentValue =
                            submodule.AttributeAccess.GetAnyAttribute<uint>(
                                InternalAttributeNames.SharedIoAssignment,
                                ac,
                                (uint)SharedIoAssignment.None);

                        if (!ac.IsOkay)
                        {
                            return false;
                        }

                        return (SharedIoAssignment)elementAssignmentValue == SharedIoAssignment.None;
                    });

            hasInterfaceSubmodule = hasInterface;

            return hasAssignedSubmodule;
        }

        /// <summary>
        /// Return true if any shared submodule exists
        /// </summary>
        /// <param name="module"></param>
        /// <returns></returns>
        public static bool IsSharedSubmoduleExist(PclObject module)
        {
            if (module == null)
            {
                throw new ArgumentNullException(nameof(module));
            }

            return FindSharedModule(module);
        }

        /// <summary>
        /// Recursive helper method to IsSharedIoDevice method
        /// </summary>
        /// <param name="module">Head/Modul/Submodul</param>
        /// <returns>Return true when not assigned submodule exists</returns>
        private static bool FindSharedModule(PclObject module)
        {
            PclObject firstSubModuleFound;

            return FindSubModule(module, SharedIoAssignment.NotAssigned, out firstSubModuleFound);
        }

        /// <summary>
        /// Recursive submodule searcher method
        /// </summary>
        /// <param name="module"></param>
        /// <param name="sharedIoAssignment"></param>
        /// <param name="firstSubModuleFound"></param>
        /// <returns></returns>
        internal static bool FindSubModule(PclObject module, SharedIoAssignment sharedIoAssignment,
            out PclObject firstSubModuleFound)
        {
            firstSubModuleFound = null;
            bool isSubModuleFound = false;
            UInt32 assignment;

            if (module is Submodule || module is Interface)
            {
                assignment = module.AttributeAccess.
                    GetAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment, new AttributeAccessCode(), 0);
                if ((SharedIoAssignment)assignment == sharedIoAssignment)
                {
                    if (!IsParametrizationDisallowed(module) ||
                        assignment != (UInt32)SharedIoAssignment.NotAssigned)
                    {
                        firstSubModuleFound = module;
                        return true;
                    }
                }
            }

            List<PclObject> submodules = GetSubmodules(module);

            if (submodules.Count == 0 && module is Module)
            {
                //If the module has not got any submodule, then the module holds the shared access info,
                //so the shared access setting of the module must be checked
                assignment = module.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.SharedIoAssignment,
                    new AttributeAccessCode(),
                    0);

                if ((SharedIoAssignment)assignment == sharedIoAssignment)
                {
                    firstSubModuleFound = module;
                    return true;
                }
            }

            foreach (PclObject submodule in submodules)
            {
                isSubModuleFound = FindSubModule(submodule, sharedIoAssignment, out firstSubModuleFound);

                if (isSubModuleFound)
                {
                    return true;
                }
            }

            return false;
        }

        private static bool IsParametrizationDisallowed(PclObject sharedObject)
        {
            if (IsPDEVSubmodule(sharedObject))
            {
                return sharedObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnParameterizationDisallowed
                    , new AttributeAccessCode(), false);
            }

            return false;
        }

        private static List<PclObject> GetSubmodules(PclObject module, bool isSorted = false)
        {
            List<PclObject> submodules = (List<PclObject>)module.GetElements();
            if (isSorted)
            {
                submodules.Sort(
                    (submodule1, submodule2)
                        => AttributeUtilities.GetPositionNumber(submodule1).CompareTo(AttributeUtilities.GetPositionNumber(submodule2)));
            }
            return submodules;
        }

        #region Consistency checks

        /// <summary>
        /// At least one sub module of an IO Device must be assigned to an IO-Controller
        /// </summary>
        /// /// <param name="headmodule"></param>
        internal static void ConsistencyCheckAllModulesShared(DecentralDevice headmodule)
        {
            bool isAnyModuleAssignedToIoController = HasNotSharedSubModule(headmodule);

            if (!isAnyModuleAssignedToIoController)
            {
                Interface interfaceSubmodule = headmodule.GetInterface();
                DataModel.PCLObjects.IOSystem ioSystem = interfaceSubmodule.PNIOD.AssignedController.IOSystem;
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, headmodule, ConsistencyConstants.SharedDeviceAllSubmodulesShared,
                        Utility.GetNameWithContainer(headmodule), AttributeUtilities.GetName(ioSystem));
            }
        }

        /// <summary>
        /// Returns with true, when not shared submodule found
        /// </summary>
        /// <param name="headModule">Head module ConfigObject</param>
        /// <returns></returns>
        private static bool HasNotSharedSubModule(DecentralDevice headModule)
        {
            if (headModule == null)
            {
                throw new ArgumentNullException(nameof(headModule));
            }
            return HasSharedAssignment(headModule, true, SharedIoAssignment.None);
        }

        /// <summary>
        /// Checks weather the input module contains any shared assignments either on module or submodule level.
        /// </summary>
        /// <param name="moduleInput">Input modules</param>
        /// <param name="defaulValue"></param>
        /// /// <param name="assignmentType"></param>
        /// <returns>True is shared assignement is found</returns>
        private static bool HasSharedAssignment(DecentralDevice moduleInput, bool defaulValue, SharedIoAssignment assignmentType)
        {
            bool sharedAssignmentFound = defaulValue;

            IEnumerable<PclObject> modules = PNNavigationUtility.GetModulesSorted(moduleInput);
            PclObject sharedModule;
            foreach (PclObject module in modules)
            {
                if (module is DecentralDevice)
                {
                    bool isSharedDeviceSupported = module.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoSharedDeviceSupported,
                        new AttributeAccessCode(),
                        false);
                    if (isSharedDeviceSupported)
                    {
                        FindSubModule(module, assignmentType, out sharedModule);
                        sharedAssignmentFound = sharedModule != null;
                    }
                    else
                    {
                        break;
                    }
                }
                else if (module is Module)
                {
                    FindSubModule(module, assignmentType, out sharedModule);
                    sharedAssignmentFound = sharedModule != null;
                }

                if (sharedAssignmentFound)
                {
                    return true;
                }
            }
            return sharedAssignmentFound;
        }

        /// <summary>
        /// Priorized startup is only possible in context of the IO Controller which has full access 
        /// of the PROFINET interface.
        /// </summary>
        /// <param name="headmodule"></param>
        internal static void ConsistencyCheckFastStartupWithNotAssignedPDEVModules(DecentralDevice headmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            Interface deviceInterfaceSubmodule = headmodule.GetInterface();
            if (deviceInterfaceSubmodule == null) { return; }

            bool hasDeviceFSUPriority = deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoDeviceFSUPriority, ac, false);

            if (hasDeviceFSUPriority)
            {
                // Get the interface assignment only, because the assignment of PDEV submodules should 
                // be always the same (Ports, Interface)
                SharedIoAssignment assignment =
                    (SharedIoAssignment)deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.SharedIoAssignment, ac.GetNew(), 0);

                // IO Controller has not full access of the PROFINET interface
                if (assignment == SharedIoAssignment.NotAssigned)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, headmodule, ConsistencyConstants.SharedDevicePrioritizedStartupNotPossible,
                            Utility.GetNameWithContainer(headmodule),
                            AttributeUtilities.GetName(headmodule.GetInterface().PNIOD.AssignedController.GetDevice()));
                }
            }
        }

        /// <summary>
        /// Check if IRT is set when IO-Device interface is set to Not assigned
        /// If the Interface of an IO-Device is �not assigned� IRT is not possible
        /// </summary>
        /// <param name="deviceInterfaceSubmodule"></param>
        internal static void ConsistencyCheckIRTAndDeviceWithSharedInterface(
            Interface deviceInterfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            PNIRTSyncRole syncRole =
                (PNIRTSyncRole)
                deviceInterfaceSubmodule.PNIOD.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtSyncRole, ac, 0);
            
            if (syncRole != PNIRTSyncRole.NotSynchronized 
                && (SharedIoAssignment)deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.SharedIoAssignment,
                    ac.GetNew(),
                    (uint)SharedIoAssignment.None) == SharedIoAssignment.NotAssigned
                )
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, deviceInterfaceSubmodule.ParentObject,
                    ConsistencyConstants.SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned,
                        Utility.GetNameWithContainer(deviceInterfaceSubmodule.ParentObject),
                        AttributeUtilities.GetName(deviceInterfaceSubmodule));
            }
        }

        /// <summary>
        /// Check if isochronous is set when IO-Device interface is set to Not assigned
        /// If the Interface of an IO-Device is �not assigned� isochronous is not possible
        /// </summary>
        /// /// <param name="deviceInterfaceSubmodule"></param>
        internal static void ConsistencyCheckIsochronAndDeviceWithSharedInterface(Interface deviceInterfaceSubmodule)
        {
            if ((SharedIoAssignment)deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.SharedIoAssignment,
                    new AttributeAccessCode(), 
                    (uint)SharedIoAssignment.None) == SharedIoAssignment.NotAssigned)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, deviceInterfaceSubmodule.ParentObject, 
                    ConsistencyConstants.SharedDeviceIsoNotPossibleWithSharedIF,
                        Utility.GetNameWithContainer(deviceInterfaceSubmodule.ParentObject),
                        AttributeUtilities.GetName(deviceInterfaceSubmodule));
            }
        }

        /// <summary>
        /// Indicates consistency error if IODevice: 
        /// synchronised, IRT, uneven sendclock adjusted and not assigned module/submodule exist
        /// </summary>
        /// <param name="hasNotAssignedModule"></param>
        /// <param name="deviceInterfaceSubmodule"></param>
        public static void ConsistencyCheckIRTUnevenSendClockWithNotAssignedModules(
            bool hasNotAssignedModule, 
            Interface deviceInterfaceSubmodule)
        {
            // If the IODevice does not have "not assigned" modules/submodules
            if (!hasNotAssignedModule)
            {
                return;
            }

            PNIOD ioDevice = deviceInterfaceSubmodule.PNIOD;
            if (ioDevice == null) { return; }

            AttributeAccessCode ac = new AttributeAccessCode();

            // Get two attributes to decide that the IODevice is synchronised or not
            PNIRTSyncRole pnIrtSyncRole =
                (PNIRTSyncRole)ioDevice.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtSyncRole, ac, (byte)PNIRTSyncRole.NotSynchronized);

            PNIOFrameClass pnIoFrameClass =
                (PNIOFrameClass)ioDevice.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoFrameClass, ac.GetNew(), (long)PNIOFrameClass.Class1Frame);

            // If synchronised and IRT
            if (pnIrtSyncRole != PNIRTSyncRole.NotSynchronized && pnIoFrameClass == PNIOFrameClass.Class3Frame)
            {
                SyncDomain syncDomain = deviceInterfaceSubmodule.SyncDomain;

                if (syncDomain != null)
                {
                    // SyncDomain PNIoSendClockFactor is Int32 not Int64
                    int sendClockFactor = syncDomain.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIoSendClockFactor, ac.GetNew(), 32);

                    Interface ioControllerInterface =
                        NavigationUtilities.GetControllerInterfaceOfDeviceInterface(deviceInterfaceSubmodule);
                    if (ioControllerInterface == null) { return; }

                    if (Utility.IsUnevenSendclock(ioControllerInterface, sendClockFactor))
                    {
                        //Calculate send clock
                        string sendClock = string.Format(CultureInfo.InvariantCulture, "{0}", (float)(sendClockFactor * PNConstants.PNTimeBase));
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, deviceInterfaceSubmodule.ParentObject,
                            ConsistencyConstants.SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules,
                                Utility.GetNameWithContainer(deviceInterfaceSubmodule.ParentObject),
                                sendClock);
                    }
                }
            }
        }

        #endregion

        /// <summary>
        /// Returns with true when the IO device not assigned submodul has
        /// </summary>
        /// <param name="interfaceSubmodule">Interface submodule</param>
        /// <returns></returns>
        public static bool IsSharedIoDevice(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            return HasSharedAssignment(interfaceSubmodule.GetDevice() as DecentralDevice, false, SharedIoAssignment.NotAssigned);
        }
    }
}