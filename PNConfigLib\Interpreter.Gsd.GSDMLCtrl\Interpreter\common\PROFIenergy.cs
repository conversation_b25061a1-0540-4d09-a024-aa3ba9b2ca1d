/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: PROFIenergy.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The PROFIenergy object describes port specific MAU types.
    /// </summary>
    public class ProfIenergy :
        GsdObject,
        GSDI.IProfIenergy
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the PROFIenergy if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ProfIenergy()
        {
            m_EntityClass = GSDI.EntityClasses.GSDEntityClassNone;
            m_EntitySubclass = GSDI.EntitySubclasses.GSDEntitySubclassNone;
            m_IsDynamicTimeAndEnergyValues = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_ProfileVersion;
        private GSDI.EntityClasses m_EntityClass;
        private GSDI.EntitySubclasses m_EntitySubclass;
        private bool m_IsDynamicTimeAndEnergyValues;
        private EnergySavingModeList m_EnergySavingModeList;
        private MeasurementList m_MeasurementList;

        #endregion

        //########################################################################################
        #region Properties


        /// <summary>
        /// Accesses the ProfileVersion property.
        /// </summary>
        public virtual string ProfileVersion => this.m_ProfileVersion;

        /// <summary>
        /// Accesses the EntityClass property.
        /// </summary>
        public virtual GSDI.EntityClasses EntityClass => this.m_EntityClass;

        /// <summary>
        /// Accesses the EntityClass property.
        /// </summary>
        public virtual GSDI.EntitySubclasses EntitySubclass => this.m_EntitySubclass;

        /// <summary>
        /// Accesses the DynamicTimeAndEnergyValues property.
        /// </summary>
        public virtual bool IsDynamicTimeAndEnergyValues => this.m_IsDynamicTimeAndEnergyValues;

        /// <summary>
        /// Accesses the list of EnergySavingMode objects.
        /// </summary>
        public virtual EnergySavingModeList EnergySavingModeList =>
            m_EnergySavingModeList;

        /// <summary>
        /// Accesses the list of Measurement objects.
        /// </summary>
        public virtual MeasurementList MeasurementList =>
            m_MeasurementList;

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hash table parameter must not be 'null'!");

                // Own data.
                string member = Models.s_FieldProfileVersion;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_ProfileVersion = (string)hash[member];

                member = Models.s_FieldEntityClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.EntityClasses)
                    m_EntityClass = (GSDI.EntityClasses)hash[member];

                member = Models.s_FieldEntitySubclass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.EntitySubclasses)
                    m_EntitySubclass = (GSDI.EntitySubclasses)hash[member];

                member = Models.s_FieldIsDynamicTimeAndEnergyValues;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    m_IsDynamicTimeAndEnergyValues = (bool)hash[member];

                member = Models.s_FieldEnergySavingModeList;
                if (hash.ContainsKey(member) && hash[member] is EnergySavingModeList)
                    this.m_EnergySavingModeList = hash[member] as EnergySavingModeList;

                member = Models.s_FieldMeasurementList;
                if (hash.ContainsKey(member) && hash[member] is MeasurementList)
                    this.m_MeasurementList = hash[member] as MeasurementList;
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectProfIenergy);

            // ----------------------------------------------
            SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            Export.WriteStringProperty(ref writer, Models.s_FieldProfileVersion, this.m_ProfileVersion);
            Export.WriteEnumProperty(ref writer, Models.s_FieldEntityClass, this.m_EntityClass.ToString(), Export.s_SubtypeEntityClasses);
            Export.WriteEnumProperty(ref writer, Models.s_FieldEntitySubclass, this.m_EntitySubclass.ToString(), Export.s_SubtypeEntityClasses);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDynamicTimeAndEnergyValues, this.m_IsDynamicTimeAndEnergyValues);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldEnergySavingModeList, this.m_EnergySavingModeList);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldMeasurementList, this.m_MeasurementList);

            return true;
        }

        #endregion
    }
}
