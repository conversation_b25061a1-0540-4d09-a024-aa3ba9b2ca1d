/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: DeviceAccessPoint.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

using GSDI;
using System.Collections.Generic;
using System.Linq;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The DeviceAccessPoint extends the abstract AccessPoint object. It 
    /// describes the characterisitic of an AccessPoint (interface module)
    /// of a PROFINET Device.
    /// </summary>
    public class DeviceAccessPoint :
        ModuleObject,
        GSDI.IDeviceAccessPoint,	// Interface
        GSDI.IDeviceAccessPoint2,
        GSDI.IDeviceAccessPoint3,
        GSDI.IDeviceAccessPoint4,
        GSDI.IDeviceAccessPoint5,
        IDeviceAccessPoint6,

        GSDI.ICompatibility
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the DeviceAccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public DeviceAccessPoint()
        {
            m_ImplementationType = String.Empty;
            m_MinDeviceInterval = 0;
            m_DnsCompatibleName = String.Empty;
            m_ObjectUUIDLocalIndex = 0;
            m_ExtendedAddressAssignmentSupported = false;
            m_RequiredSchemaVersion = String.Empty;
            m_MultipleWriteSupported = false;
            m_IOXSRequired = true;
            m_DeviceSpecificToolName = String.Empty;
            m_DeviceSpecificToolDescription = String.Empty;
            m_AddressAssignment = null;
            m_RemoteApplicationTimeout = 0;

            m_ParameterizationSpeedupSupported = false;
            m_PowerOnToCommReady = 0;
            m_MaxSupportedRecordSize = 0;
            m_NameOfStationNotTransferable = false;

            m_SharedDeviceSupported = false;
            m_SharedInputSupported = false;
            m_DeviceAccessSupported = false;
            m_WebServer = String.Empty;

            m_CirSupported = Attributes.s_DefaultCirSupported;
            m_LldpNoDSupported = Attributes.s_DefaultLldpnoDSupported;
            m_AutoConfigurationSupported = Attributes.s_DefaultAutoConfigurationSupported;
            m_CheckDeviceIDAllowed = false;
            m_PrmBeginPrmEndSequenceSupported = Attributes.s_DefaultPrmBeginPrmEndSequenceSupported;

            // GSDML V2.32
            m_IsProfIenergySupported = false;

            // GSDML V2.33
            m_NumberOfDeviceAccessAr = 0;
            m_IsAdaptsRealIdentification = false;
            m_IsAssetManagement = false;

            // GSDML V2.34
            m_NumberOfSubmodules = 0;

            // GSDML V2.4
            m_SFPDiagnosisSupported = null;
        }

        #endregion

        //########################################################################################
        #region Fields

        // V1.0
        private ArrayList m_VirtualSubmodules;
        private List<uint> m_PhysicalSlots;
        private ArrayList m_Modules;
        private IGsdObjectDictionary m_ModuleQuickFinder;	// Only private!
        private IGsdObjectDictionary m_SubmoduleQuickFinder;	// Only private!
        private ArrayList m_DefaultModules;	// Only private!
        private IGsdObjectDictionary m_ModulePlugData;
        private string m_ImplementationType;
        private uint m_MinDeviceInterval;
        private IOConfigData m_IOConfigData;
        private ApplicationRelations m_ApplicationRelations;
        private string m_DnsCompatibleName;
        private uint m_ObjectUUIDLocalIndex;
        private bool m_ExtendedAddressAssignmentSupported;
        private ModulePlugData m_PlugData;

        private ArrayList m_SystemDefinedSubmodules;
        private ArrayList m_Slots;
        private ArrayList m_Subslots;
        private string m_RequiredSchemaVersion;
        private bool m_MultipleWriteSupported;
        private bool m_IOXSRequired;
        private string m_DeviceSpecificToolName;
        private string m_DeviceSpecificToolDescription;
        private ArrayList m_AddressAssignment;

        private List<uint> m_PhysicalSubslots;
        private ArrayList m_PhysicalSubmodules;
        private IGsdObjectDictionary m_SubmodulePlugData;
        private uint m_RemoteApplicationTimeout;

        private bool m_ParameterizationSpeedupSupported;
        private uint m_PowerOnToCommReady;
        private uint m_MaxSupportedRecordSize;
        private bool m_NameOfStationNotTransferable;

        private bool m_SharedDeviceSupported;
        private bool m_SharedInputSupported;

        private bool m_DeviceAccessSupported;
        private string m_WebServer;

        private SystemRedundancy m_SystemRedundancy;
        private bool m_CirSupported;
        private bool m_LldpNoDSupported;
        private bool m_AutoConfigurationSupported;

        private bool m_CheckDeviceIDAllowed;
        private bool m_PrmBeginPrmEndSequenceSupported;

        // GSDML V2.32
        private bool m_IsProfIenergySupported;

        // GSDML V2.33
        private uint m_NumberOfDeviceAccessAr;
        private bool m_IsAdaptsRealIdentification;
        private bool m_IsAssetManagement;

        // GSDML V2.34
        private uint m_NumberOfSubmodules;

        // GSDML V2.4
        private ArrayList m_SFPDiagnosisSupported;

        // GSDML V2.41
        private CertificationInfo m_CertificationInfo;

        // GSDML V2.43
        private ArrayList m_CommunicationInterfaces;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the list of slot numbers for the physical slots 
        /// supported with this AccessPoint.
        /// </summary>
        public virtual Array PhysicalSlots =>
            (null != m_PhysicalSlots) ?
                new ArrayList(m_PhysicalSlots).ToArray() :
                null;

        /// <summary>
        /// Accesses the list of slot numbers for the physical slots 
        /// supported with this AccessPoint.
        /// </summary>
        public virtual Array PhysicalSubslots =>
            (null != m_PhysicalSubslots) ?
                new ArrayList(m_PhysicalSubslots).ToArray() :
                null;

        /// <summary>
        /// Accesses the list of Modules supported with this AccessPoint.
        /// </summary>
        public virtual Array Modules =>
            m_Modules?.ToArray();

        /// <summary>
        /// Accesses the key value list of ModulePlugData, whereby each 
        /// plug data corresponds to a Module available from the Modules list.
        /// </summary>
        /// <remarks>Key is the GsdID of the Module and the corresponding value
        /// is the ModulePlugData object.</remarks>
        public virtual IDictionary ModulePlugDataDictionary => m_ModulePlugData.CastToNonGenericDictionary();

        /// <summary>
        /// Accesses the key value list of ModulePlugData, whereby each 
        /// plug data corresponds to a Submodule available from the Submodules list.
        /// </summary>
        /// <remarks>Key is the GsdID of the Module and the corresponding value
        /// is the ModulePlugData object.</remarks>
        public virtual IDictionary SubmodulePlugDataDictionary => m_SubmodulePlugData.CastToNonGenericDictionary();

        /// <summary>
        /// Accesses the ImplementationType, which is a description of the 
        /// standard implementation in the AccessPoint. For example, standard 
        /// software, controller or ASIC (Application Specific Integrated 
        /// Circuit) solution.
        /// </summary>
        public string ImplementationType => m_ImplementationType;

        /// <summary>
        /// Accesses the MinDeviceInterval, which specifies the minimum 
        /// interval time for sending cyclic IO data.
        /// Basic clock tick is 31,25 microseconds. The value of this element 
        /// contains the multiplier of the basic clock tick.
        /// </summary>
        public UInt32 MinDeviceInterval => m_MinDeviceInterval;

        /// <summary>
        /// Accesses the IOConfigData object, which contains information 
        /// about the quantity of IO data.
        /// </summary>
        public virtual IOConfigData IOConfigData => m_IOConfigData;

        /// <summary>
        /// Accesses the ObjectUUIDLocalIndex, which specifies the instance field 
        /// of the Object UUID.
        /// </summary>
        public UInt32 ObjectUUIDLocalIndex => m_ObjectUUIDLocalIndex;

        /// <summary>
        /// Accesses, whether another way of IP address assignment is supported or not.
        /// </summary>
        /// <remarks>In PROFINET IO each IO Device shall implement the DCP protocol for 
        /// assignment of the IP addresses. If the DeviceAccessPoint supports another way 
        /// of IP address assignment (e.g. DHCP) this attribute has to be set to true.</remarks>
        public bool IsExtendedAddressAssignmentSupported => m_ExtendedAddressAssignmentSupported;

        /// <summary>
        /// Accesses the ApplicationRelations object, which contains information about the 
        /// application relations implemented in an IO Device.
        /// </summary>
        public virtual ApplicationRelations ApplicationRelations => m_ApplicationRelations;

        /// <summary>
        /// Accesses the DNS Name, which describes the default name of a device,
        /// compliant with the DNS rules according to RFC 1101:1989.
        /// </summary>
        public string DNSCompatibleName => m_DnsCompatibleName;

        /// <summary>
        /// Accesses the ModulePlugData object, which contains special information about
        /// the slots where the DeviceAccessPoint can be plugged.
        /// </summary>
        public virtual ModulePlugData PlugData => m_PlugData;

        /// <summary>
        /// Accesses the list of VirtualSubmodules available from this Module.
        /// </summary>
        public virtual Array VirtualSubmodules =>
            m_VirtualSubmodules?.ToArray();

        /// <summary>
        /// Accesses the list of physical submodules available from this DAP.
        /// </summary>
        public virtual Array PhysicalSubmodules =>
            m_PhysicalSubmodules?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array SystemDefinedSubmodulesArray =>
            m_SystemDefinedSubmodules?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array Slots =>
            m_Slots?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array Subslots =>
            m_Subslots?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public string RequiredSchemaVersion => m_RequiredSchemaVersion;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsMultipleWriteSupported => m_MultipleWriteSupported;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsIOXSRequired => m_IOXSRequired;

        /// <summary>
        /// ...
        /// </summary>
        public string DeviceSpecificToolName => m_DeviceSpecificToolName;

        /// <summary>
        /// ...
        /// </summary>
        public string DeviceSpecificToolDescription => m_DeviceSpecificToolDescription;

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array AddressAssignment =>
            m_AddressAssignment?.ToArray();

        public UInt32 RemoteApplicationTimeout => m_RemoteApplicationTimeout;

        public bool IsParameterizationSpeedupSupported => m_ParameterizationSpeedupSupported;

        public bool IsNameOfStationNotTransferable => m_NameOfStationNotTransferable;

        public UInt32 PowerOnToCommReady => m_PowerOnToCommReady;

        public UInt32 MaxSupportedRecordSize => m_MaxSupportedRecordSize;

        protected new object IdentNumber => ((ModuleObject)this).IdentNumber;

        protected new GSDI.IModuleInfo Info => ((ModuleObject)this).Info;

        protected IModuleObject GetSubmoduleExt(string bstrGsdId)
        {
            return GetSubmodule(bstrGsdId);
        }

        protected new GSDI.IGraphic GetGraphic(GraphicTypes lType)
        {
            return ((ModuleObject)this).GetGraphic(lType);
        }

        #region IDeviceAccessPoint5 Members

        public bool IsSharedDeviceSupported => m_SharedDeviceSupported;

        public bool IsSharedInputSupported => m_SharedInputSupported;

        public bool IsDeviceAccessSupported => m_DeviceAccessSupported;

        public string WebServer => m_WebServer;

        #endregion

        #region IDeviceAccessPoint6 Members

        public SystemRedundancy SystemRedundancy => m_SystemRedundancy;

        public bool IsCIR_Supported => m_CirSupported;

        public bool IsLldpnoDSupported => m_LldpNoDSupported;

        public bool IsAutoConfigurationSupported => m_AutoConfigurationSupported;

        public bool IsCheckDeviceIDAllowed => m_CheckDeviceIDAllowed;

        public bool IsPROFIenergySupported => this.m_IsProfIenergySupported;

        public uint NumberOfDeviceAccessAR => this.m_NumberOfDeviceAccessAr;

        public bool IsAdaptsRealIdentification => this.m_IsAdaptsRealIdentification;

        public bool IsAssetManagement => this.m_IsAssetManagement;

        public bool IsPrmBeginPrmEndSequenceSupported => this.m_PrmBeginPrmEndSequenceSupported;

        public uint NumberOfSubmodules => this.m_NumberOfSubmodules;

        public CertificationInfo CertificationInfo => m_CertificationInfo;

        public virtual Array SFPDiagnosisSupported =>
            (null != this.m_SFPDiagnosisSupported) ?
                m_SFPDiagnosisSupported.ToArray() :
                null;

        public virtual Array CommunicationInterfaces =>
            (null != this.m_CommunicationInterfaces) ?
                m_CommunicationInterfaces.ToArray() :
                null;

        #endregion

        #endregion
        //########################################################################################
        #region Methods

        /// <summary>
        /// Returns the list of available default Modules. 
        /// Default Modules are Modules from the Modules list, which have 
        /// plug data entries within the UsedInSlots or FixedInSlots listings.
        /// </summary>
        public virtual Array GetDefaultModules()
        {
            return m_DefaultModules?.ToArray();
        }

        /// <summary>
        /// Returns the ModulePlugData corresponding to the Module specified 
        /// with the given GsdID.
        /// </summary>
        /// <param name="bstrModuleGsdID">Is the GsdID of the Module for
        /// which the ModulePlugData should be accessed.</param>
        public virtual ModulePlugData GetModulePlugData(string bstrModuleGsdID)
        {
            if (null != m_ModulePlugData)
            {
                if (m_ModulePlugData.ContainsKey(bstrModuleGsdID))
                    return m_ModulePlugData[bstrModuleGsdID] as ModulePlugData;
            }

            // Return null if no ModulePlugData is available for this GsdID.
            return null;
        }

        /// <summary>
        /// Returns the ModulePlugData corresponding to the Module specified 
        /// with the given GsdID.
        /// </summary>
        /// <param name="bstrModuleGsdID">Is the GsdID of the Module for
        /// which the ModulePlugData should be accessed.</param>
        public virtual ModulePlugData GetSubmodulePlugData(string bstrModuleGsdID)
        {
            if (null != m_SubmodulePlugData)
            {
                if (m_SubmodulePlugData.ContainsKey(bstrModuleGsdID))
                    return m_SubmodulePlugData[bstrModuleGsdID] as ModulePlugData;
            }

            // Return null if no ModulePlugData is available for this GsdID.
            return null;
        }

        /// <summary>
        /// Returns the Submodule object from the Submodules list having the 
        /// specified GsdID.
        /// </summary>
        /// <param name="bstrGsdID">Is the GsdID of the Module, which should
        /// be accessed.</param>
        public virtual ModuleObject GetSubmodule(string bstrGsdID)
        {
            if (null != m_SubmoduleQuickFinder)
            {
                if (m_SubmoduleQuickFinder.ContainsKey(bstrGsdID))
                    return m_SubmoduleQuickFinder[bstrGsdID] as ModuleObject;
            }

            // If no Module or this Module didn't exist, return nothing.
            return null;
        }


        /// <summary>
        /// Returns the Module object from the Modules list having the 
        /// specified GsdID.
        /// </summary>
        /// <param name="bstrGsdID">Is the GsdID of the Module, which should
        /// be accessed.</param>
        public virtual Module GetModule(string bstrGsdID)
        {
            if (null != m_ModuleQuickFinder)
            {
                if (m_ModuleQuickFinder.ContainsKey(bstrGsdID))
                    return m_ModuleQuickFinder[bstrGsdID] as Module;
            }

            // If no Module or this Module didn't exist, return nothing.
            return null;
        }


        //
        /// <summary>
        /// ...
        /// </summary>
        public virtual bool HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes lType)
        {
            // Check whether system defined submodules exist.
            if (null == this.SystemDefinedSubmodulesArray)
                return false;	// ---------->

            foreach (SystemDefinedSubmoduleObject pso in this.SystemDefinedSubmodulesArray)
            {
                // Check for Interface system defined submodule.
                if ((lType == GSDI.SystemDefinedSubmoduleTypes.GSDAll ||
                    lType == GSDI.SystemDefinedSubmoduleTypes.GSDInterface) && pso is InterfaceSubmodule)
                    return true;	// ---------->

                // Check for Port system defined submodule.
                if ((lType == GSDI.SystemDefinedSubmoduleTypes.GSDAll ||
                    lType == GSDI.SystemDefinedSubmoduleTypes.GSDPort) && pso is PortSubmodule)
                    return true;	// ---------->
            }

            return false;
        }

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes lType)
        {
            // Check whether system defined submodules exist.
            if (null == this.SystemDefinedSubmodulesArray)
                return null;	// ---------->

            // Check for all type.
            if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDAll)
                return this.SystemDefinedSubmodulesArray;	// ---------->

            // Fill array for returning.
            ArrayList list = new ArrayList();
            foreach (SystemDefinedSubmoduleObject pso in this.SystemDefinedSubmodulesArray)
            {
                // Check for Interface system defined submodule.
                if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDInterface && pso is InterfaceSubmodule)
                    list.Add(pso);

                // Check for Port system defined submodule.
                if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDPort && pso is PortSubmodule)
                    list.Add(pso);
            }

            if (list.Count > 0)
                return list.ToArray();	// ---------->

            return null;
        }

        /// <summary>
        /// ...
        /// </summary>
        public virtual SystemDefinedSubmoduleObject GetSystemDefinedSubmodule(System.UInt32 lSubslot)
        {
            // Check whether system defined submodules exist.
            if (null == SystemDefinedSubmodulesArray)
                return null;	// ---------->

            // Search for system defined submodule with given slot number.
            foreach (SystemDefinedSubmoduleObject pso in SystemDefinedSubmodulesArray)
            {

                // Check slot number.
                if (pso.SubslotNumber == lSubslot)
                    return pso;	// ---------->
            }
            return null;
        }
        /// <summary>
        /// ...
        /// </summary>
        public virtual Slot GetSlot(UInt32 lSlot)
        {
            // Check whether slots exist.
            if (null == Slots)
                return null;	// ---------->

            // Search for slot with given slot number.
            foreach (Slot ssl in Slots)
            {

                // Check slot number.
                if (ssl.SlotNumber == lSlot)
                    return ssl;	// ---------->
            }
            return null;
        }
        /// <summary>
        /// ...
        /// </summary>
        public virtual Subslot GetSubslot(UInt32 lSubslot)
        {
            // Check whether subslots exist.
            if (null == Subslots)
                return null;	// ---------->

            // Search for subslot with given slot number.
            foreach (Subslot ssl in Subslots)
            {

                // Check slot number.
                if (ssl.SubslotNumber == lSubslot)
                    return ssl;	// ---------->
            }
            return null;
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");
                FillFieldVirtualSubmodules(hash);

                FillFieldPhysicalSlots(hash);

                FillFieldPhysicalSubslots(hash);

                FillFieldModules(hash);

                FillFieldModulePlugData(hash);

                FillFieldImplementationType(hash);

                FillFieldMinDeviceInterval(hash);

                FillFieldIOConfigData(hash);

                FillFieldApplicationRelations(hash);

                FillFieldDNSCompatibleName(hash);

                FillFieldObjectUUIDLocalIndex(hash);

                FillFieldIsExtendedAddressAssignmentSupported(hash);

                FillFieldPlugData(hash);

                FillFieldSystemDefinedSubmodules(hash);

                FillFieldSlots(hash);

                FillFieldSubslots(hash);

                FillFieldRequiredSchemaVersion(hash);

                FillFieldIsMultipleWriteSupported(hash);

                FillFieldIsIOXSRequired(hash);

                FillFieldDeviceSpecificToolName(hash);

                FillFieldDeviceSpecificToolDescription(hash);

                FillFieldAddressAssignment(hash);

                FillFieldIsParameterizationSpeedupSupported(hash);

                FillFieldMaxSupportedRecordSize(hash);

                FillFieldPowerOnToCommReady(hash);

                FillFieldRemoteApplicationTimeout(hash);

                FillFieldIsNameOfStationNotTransferable(hash);

                FillFieldSubmodules(hash);

                FillFieldSubmodulePlugData(hash);

                FillFieldIsSharedDeviceSupported(hash);

                FillFieldIsSharedInputSupported(hash);

                FillFieldIsDeviceAccessSupported(hash);

                FillFieldWebServer(hash);

                FillFieldSystemRedundancy(hash);

                FillFieldIsCirSupported(hash);

                FillFieldIsLLDPNoDSupported(hash);

                FillFieldIsAutoConfigurationSupported(hash);

                FillFieldIsCheckDeviceIDAllowed(hash);

                FillFieldIsPROFIenergySupported(hash);

                FillFieldNumberOfDeviceAccessAR(hash);

                FillFieldIsAdaptsRealIdentification(hash);

                FillFieldIsAssetManagement(hash);

                FillFieldIsPrmBeginPrmEndSequenceSupported(hash);

                FillFieldNumberOfSubmodules(hash);

                FillFieldSFPDiagnosisSupported(hash);

                FillFieldCertificationInfo(hash);

                FillFieldCommunicationInterfaces(hash);


                

                // Base data.
                succeeded = base.Fill(hash);

                CreateModulesHashTable();

                CreatePhysicalSubmodulesHashTable();

                CreateDefaultModulesList();

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        private void CreateDefaultModulesList()
        {
            // For better handling of default modules create own list.
            if (null == m_Modules)
            {
                return;
            }

            foreach (Module m in m_Modules)
            {
                // Check in module plug data whether it is a default module.
                if (((ModulePlugData)m_ModulePlugData[m.GsdID]).FixedInSlots != null)
                {
                    if (((ModulePlugData)m_ModulePlugData[m.GsdID]).FixedInSlots.Length != 0)
                    {
                        // If default modules exist, create list.
                        if (null == m_DefaultModules)
                            m_DefaultModules = new ArrayList();

                        // Add module to default modules list.
                        m_DefaultModules.Add(m);
                    }
                }

                //
                if (((ModulePlugData)m_ModulePlugData[m.GsdID]).UsedInSlots == null)
                {
                    continue;
                }

                if (((ModulePlugData)m_ModulePlugData[m.GsdID]).UsedInSlots.Length == 0)
                {
                    continue;
                }

                // If default modules exist, create list.
                if (m_DefaultModules == null)
                {
                    m_DefaultModules = new ArrayList();
                }

                // Add module to default modules list.
                if (!m_DefaultModules.Contains(m))
                    m_DefaultModules.Add(m);
            }

            if (null != m_DefaultModules)
                m_DefaultModules.TrimToSize();
        }

        private void CreatePhysicalSubmodulesHashTable()
        {
            // For better handling of Submodules in relation to the GsdID create private hashtable.
            if (null == m_PhysicalSubmodules)
            {
                return;
            }

            foreach (ModuleObject m in m_PhysicalSubmodules)
            {
                // If graphics exist, create hashtable.
                if (m_SubmoduleQuickFinder == null)
                {
                    m_SubmoduleQuickFinder = GsdObjectDictionaryFactory.CreateDictionary(true);
                }

                // Add graphic to quick finder hashtable.
                m_SubmoduleQuickFinder.Add(m.GsdID, m);
            }

            if (null == m_SubmoduleQuickFinder)
            {
                return;
            }

            m_PhysicalSubmodules = new ArrayList(m_SubmoduleQuickFinder.Values.ToArray());
        }

        private void CreateModulesHashTable()
        {
            // For better handling of Modules in relation to the GsdID create private hashtable.
            if (null == m_Modules)
            {
                return;
            }

            foreach (Module m in m_Modules)
            {
                // If graphics exist, create hashtable.
                if (m_ModuleQuickFinder == null)
                {
                    m_ModuleQuickFinder = GsdObjectDictionaryFactory.CreateDictionary(true);
                }

                // Add graphic to quick finder hashtable.
                m_ModuleQuickFinder.Add(m.GsdID, m);
            }

            if (null == m_ModuleQuickFinder)
            {
                return;
            }

            m_Modules = new ArrayList(m_ModuleQuickFinder.Values.ToArray());
        }

        private void FillFieldCommunicationInterfaces(Hashtable hash)
        {
            string member = Models.s_FieldCommunicationInterfaces;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_CommunicationInterfaces = hash[member] as ArrayList;
        }

        private void FillFieldCertificationInfo(Hashtable hash)
        {
            string member = Models.s_FieldCertificationInfo;
            if (hash.ContainsKey(member)
                && hash[member] is CertificationInfo)
                m_CertificationInfo = hash[member] as CertificationInfo;
        }

        private void FillFieldSFPDiagnosisSupported(Hashtable hash)
        {
            string member = Models.s_FieldSFPDiagnosisSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SFPDiagnosisSupported = hash[member] as ArrayList;
        }

        private void FillFieldNumberOfSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_NumberOfSubmodules = (uint)hash[member];
        }

        private void FillFieldIsPrmBeginPrmEndSequenceSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsPrmBeginPrmEndSequenceSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_PrmBeginPrmEndSequenceSupported = (bool)hash[member];
        }

        private void FillFieldIsAssetManagement(Hashtable hash)
        {
            string member = Models.s_FieldIsAssetManagement;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsAssetManagement = (bool)hash[member];
        }

        private void FillFieldIsAdaptsRealIdentification(Hashtable hash)
        {
            string member = Models.s_FieldIsAdaptsRealIdentification;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsAdaptsRealIdentification = (bool)hash[member];
        }

        private void FillFieldNumberOfDeviceAccessAR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfDeviceAccessAr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_NumberOfDeviceAccessAr = (uint)hash[member];
        }

        private void FillFieldIsPROFIenergySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsProfIenergySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfIenergySupported = (bool)hash[member];
        }

        private void FillFieldIsCheckDeviceIDAllowed(Hashtable hash)
        {
            string member = Models.s_FieldIsCheckDeviceIdAllowed;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_CheckDeviceIDAllowed = (bool)hash[member];
        }

        private void FillFieldIsAutoConfigurationSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsAutoConfigurationSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_AutoConfigurationSupported = (bool)hash[member];
        }

        private void FillFieldIsLLDPNoDSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsLldpnoDSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_LldpNoDSupported = (bool)hash[member];
        }

        private void FillFieldIsCirSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsCirSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_CirSupported = (bool)hash[member];
        }

        private void FillFieldSystemRedundancy(Hashtable hash)
        {
            string member = Models.s_FieldSystemRedundancy;
            if (hash.ContainsKey(member)
                && hash[member] is SystemRedundancy)
                m_SystemRedundancy = hash[member] as SystemRedundancy;
        }

        private void FillFieldWebServer(Hashtable hash)
        {
            string member = Models.s_FieldWebServer;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_WebServer = (string)hash[member];
        }

        private void FillFieldIsDeviceAccessSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsDeviceAccessSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_DeviceAccessSupported = (bool)hash[member];
        }

        private void FillFieldIsSharedInputSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsSharedInputSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_SharedInputSupported = (bool)hash[member];
        }

        private void FillFieldIsSharedDeviceSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsSharedDeviceSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_SharedDeviceSupported = (bool)hash[member];
        }

        private void FillFieldSubmodulePlugData(Hashtable hash)
        {
            string member = Models.s_FieldSubmodulePlugData;
            if (hash.ContainsKey(member)
                && hash[member] is IGsdObjectDictionary)
                m_SubmodulePlugData = hash[member] as IGsdObjectDictionary;
        }

        private void FillFieldSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_PhysicalSubmodules = hash[member] as ArrayList;
        }

        private void FillFieldIsNameOfStationNotTransferable(Hashtable hash)
        {
            string member = Models.s_FieldIsNameOfStationNotTransferable;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_NameOfStationNotTransferable = (bool)hash[member];
        }

        private void FillFieldRemoteApplicationTimeout(Hashtable hash)
        {
            string member = Models.s_FieldRemoteApplicationTimeout;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_RemoteApplicationTimeout = (uint)hash[member];
        }

        private void FillFieldPowerOnToCommReady(Hashtable hash)
        {
            string member = Models.s_FieldPowerOnToCommReady;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_PowerOnToCommReady = (uint)hash[member];
        }

        private void FillFieldMaxSupportedRecordSize(Hashtable hash)
        {
            string member = Models.s_FieldMaxSupportedRecordSize;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxSupportedRecordSize = (uint)hash[member];
        }

        private void FillFieldIsParameterizationSpeedupSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsParameterizationSpeedupSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ParameterizationSpeedupSupported = (bool)hash[member];
        }

        private void FillFieldAddressAssignment(Hashtable hash)
        {
            string member = Models.s_FieldAddressAssignment;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_AddressAssignment = hash[member] as ArrayList;
        }

        private void FillFieldDeviceSpecificToolDescription(Hashtable hash)
        {
            string member = Models.s_FieldDeviceSpecificToolDescription;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_DeviceSpecificToolDescription = hash[member] as string;
        }

        private void FillFieldDeviceSpecificToolName(Hashtable hash)
        {
            string member = Models.s_FieldDeviceSpecificToolName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_DeviceSpecificToolName = hash[member] as string;
        }

        private void FillFieldIsIOXSRequired(Hashtable hash)
        {
            string member = Models.s_FieldIsIoxsRequired;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IOXSRequired = (bool)hash[member];
        }

        private void FillFieldIsMultipleWriteSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsMultipleWriteSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_MultipleWriteSupported = (bool)hash[member];
        }

        private void FillFieldRequiredSchemaVersion(Hashtable hash)
        {
            string member = Models.s_FieldRequiredSchemaVersion;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_RequiredSchemaVersion = hash[member] as string;
        }

        private void FillFieldSubslots(Hashtable hash)
        {
            string member = Models.s_FieldSubslots;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_Subslots = hash[member] as ArrayList;
        }

        private void FillFieldSlots(Hashtable hash)
        {
            string member = Models.s_FieldSlots;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_Slots = hash[member] as ArrayList;
        }

        private void FillFieldSystemDefinedSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldSystemDefinedSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SystemDefinedSubmodules = hash[member] as ArrayList;
        }

        private void FillFieldPlugData(Hashtable hash)
        {
            string member = Models.s_FieldPlugData;
            if (hash.ContainsKey(member)
                && hash[member] is ModulePlugData)
                m_PlugData = hash[member] as ModulePlugData;
        }

        private void FillFieldIsExtendedAddressAssignmentSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsExtendedAddressAssignmentSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ExtendedAddressAssignmentSupported = (bool)hash[member];
        }

        private void FillFieldObjectUUIDLocalIndex(Hashtable hash)
        {
            string member = Models.s_FieldObjectUuidLocalIndex;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_ObjectUUIDLocalIndex = (uint)hash[member];
        }

        private void FillFieldDNSCompatibleName(Hashtable hash)
        {
            string member = Models.s_FieldDnsCompatibleName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_DnsCompatibleName = hash[member] as string;
        }

        private void FillFieldApplicationRelations(Hashtable hash)
        {
            string member = Models.s_FieldApplicationRelations;
            if (hash.ContainsKey(member)
                && hash[member] is ApplicationRelations)
                m_ApplicationRelations = hash[member] as ApplicationRelations;
        }

        private void FillFieldIOConfigData(Hashtable hash)
        {
            string member = Models.s_FieldIoConfigData;
            if (hash.ContainsKey(member)
                && hash[member] is IOConfigData)
                m_IOConfigData = hash[member] as IOConfigData;
        }

        private void FillFieldMinDeviceInterval(Hashtable hash)
        {
            string member = Models.s_FieldMinDeviceInterval;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MinDeviceInterval = (uint)hash[member];
        }

        private void FillFieldImplementationType(Hashtable hash)
        {
            string member = Models.s_FieldImplementationType;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_ImplementationType = hash[member] as string;
        }

        private void FillFieldModulePlugData(Hashtable hash)
        {
            string member = Models.s_FieldModulePlugData;
            if (hash.ContainsKey(member)
                && hash[member] is IGsdObjectDictionary)
                m_ModulePlugData = hash[member] as IGsdObjectDictionary;
        }

        private void FillFieldModules(Hashtable hash)
        {
            string member = Models.s_FieldModules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_Modules = hash[member] as ArrayList;
        }

        private void FillFieldPhysicalSubslots(Hashtable hash)
        {
            string member = Models.s_FieldPhysicalSubslots;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_PhysicalSubslots = hash[member] as List<uint>;
        }

        private void FillFieldPhysicalSlots(Hashtable hash)
        {
            string member = Models.s_FieldPhysicalSlots;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_PhysicalSlots = hash[member] as List<uint>;
        }

        private void FillFieldVirtualSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldVirtualSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_VirtualSubmodules = hash[member] as ArrayList;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectDeviceAccessPoint);

            // ----------------------------------------------
            SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }
        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            // From base class
            base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            // V1.0
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldVirtualSubmodules, this.m_VirtualSubmodules);
            Export.WriteStringProperty(ref writer, Models.s_FieldImplementationType, this.m_ImplementationType);
            Export.WriteUint32Property(ref writer, Models.s_FieldMinDeviceInterval, this.m_MinDeviceInterval);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldIoConfigData, this.m_IOConfigData);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldPhysicalSlots, new ArrayList(this.m_PhysicalSlots), true);
            Export.WriteDictionaryListProperty(option, ref writer, Models.s_FieldModulePlugData, m_ModulePlugData.CastToNonGenericDictionary());
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldModules, this.m_Modules);
            Export.WriteStringProperty(ref writer, Models.s_FieldDnsCompatibleName, this.m_DnsCompatibleName);
            Export.WriteUint32Property(ref writer, Models.s_FieldObjectUuidLocalIndex, this.m_ObjectUUIDLocalIndex);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsExtendedAddressAssignmentSupported, this.m_ExtendedAddressAssignmentSupported);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldApplicationRelations, this.m_ApplicationRelations);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldPlugData, this.m_PlugData);

            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSystemDefinedSubmodules, this.m_SystemDefinedSubmodules);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSlots, this.m_Slots);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSubslots, this.m_Subslots);
            Export.WriteStringProperty(ref writer, Models.s_FieldRequiredSchemaVersion, this.m_RequiredSchemaVersion);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMultipleWriteSupported, this.m_MultipleWriteSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsIoxsRequired, this.m_IOXSRequired);
            Export.WriteStringProperty(ref writer, Models.s_FieldDeviceSpecificToolName, this.m_DeviceSpecificToolName);
            Export.WriteStringProperty(ref writer, Models.s_FieldDeviceSpecificToolDescription, this.m_DeviceSpecificToolDescription);

            // V2.1
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldAddressAssignment, this.m_AddressAssignment, true);

            // V2.2
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsParameterizationSpeedupSupported, this.m_ParameterizationSpeedupSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsNameOfStationNotTransferable, this.m_NameOfStationNotTransferable);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxSupportedRecordSize, this.m_MaxSupportedRecordSize);
            Export.WriteUint32Property(ref writer, Models.s_FieldPowerOnToCommReady, this.m_PowerOnToCommReady);

            // V2.25
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsSharedDeviceSupported, this.m_SharedDeviceSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsSharedInputSupported, this.m_SharedInputSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDeviceAccessSupported, this.m_DeviceAccessSupported);

            // V2.3
            Export.WriteStringProperty(ref writer, Models.s_FieldWebServer, this.m_WebServer);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldSystemRedundancy, this.m_SystemRedundancy);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsCirSupported, this.m_CirSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsLldpnoDSupported, this.m_LldpNoDSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsAutoConfigurationSupported, this.m_AutoConfigurationSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsPrmBeginPrmEndSequenceSupported, this.m_PrmBeginPrmEndSequenceSupported);

            // V2.31
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsCheckDeviceIdAllowed, this.m_CheckDeviceIDAllowed);

            // V2.32
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsProfIenergySupported, this.m_IsProfIenergySupported);

            // V2.33
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfDeviceAccessAr, this.m_NumberOfDeviceAccessAr);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsAdaptsRealIdentification, this.m_IsAdaptsRealIdentification);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsAssetManagement, this.m_IsAssetManagement);

            // V2.34
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfSubmodules, this.m_NumberOfSubmodules);

            // V2.4
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSFPDiagnosisSupported, m_SFPDiagnosisSupported, true);

            // V2.41
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldCertificationInfo, this.m_CertificationInfo);

            // V2.43
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldCommunicationInterfaces, this.m_CommunicationInterfaces);
            return true;
        }

        #endregion
    }
}
