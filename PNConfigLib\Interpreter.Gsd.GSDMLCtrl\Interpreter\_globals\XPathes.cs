/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: XPathes.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all xpath expressions for the GSD(ML) document.
    /// </summary>
    internal static class XPathes
    {
        //########################################################################################
        #region Common XPathes for V1.0 Elements and Attributes
        // Contains all element names as constants

        /// <summary>//ISO15745Profile/ProfileBody</summary>
        public const string ProfileBody =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_Iso15745Profile +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ProfileBody;


        /// <summary>//Family</summary>
        public const string Family =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_Family;


        /// <summary>//VirtualSubmoduleList/VirtualSubmoduleItem</summary>
        public const string AllVirtualSubmoduleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_VirtualSubmoduleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_VirtualSubmoduleItem;

        /// <summary>//InterfaceSubmoduleList/InterfaceSubmoduleItem</summary>
        public const string AllInterfaceSubmoduleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SystemDefinedSubmoduleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_InterfaceSubmoduleItem;

        /// <summary>//InterfaceSubmoduleList/InterfaceSubmoduleItem</summary>
        public const string AllInterfacePortSubmoduleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SystemDefinedSubmoduleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_PortSubmoduleItem;

        /// <summary>//ModuleList/ModuleItem</summary>
        public const string AllModuleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ModuleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ModuleItem;

        /// <summary>//SubmoduleList/SubmoduleItem</summary>
        public const string AllSubmoduleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SubmoduleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SubmoduleItem;

        /// <summary>//SubmoduleList/PortsubmoduleItem</summary>
        public const string AllPluggablePortsubmoduleItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SubmoduleList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_PortSubmoduleItem;

        /// <summary>//ApplicationProcess/RecordDataList/ParameterRecordDataItem</summary>
        public const string AllRecordDataItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ApplicationProcess +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_RecordDataList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ParameterRecordDataItem;


        /// <summary>//ApplicationProcess/CommunicationInterfaceList/CommunicationInterfaceItem</summary>
        public const string AllCommunicationInterfaces =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ApplicationProcess +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_CommunicationInterfaceList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_CommunicationInterfaceItem;

        /// <summary>//DeviceAccessPointList/DeviceAccessPointItem</summary>
        public const string AllDeviceAccessPointItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_DeviceAccessPointList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_DeviceAccessPointItem;

        /// <summary>//CategoryList/CategoryItem</summary>
        public const string AllCategoryItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_CategoryList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_CategoryItem;

        /// <summary>//GraphicsList/GraphicItem</summary>
        public const string AllGraphicItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_GraphicsList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_GraphicItem;

        /// <summary>//ValueList/ValueItem</summary>
        public const string AllValueItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ValueList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ValueItem;

        /// <summary>//ChannelDiagList/ChannelDiagItem</summary>
        public const string AllChannelDiagItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelDiagList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelDiagItem;

        /// <summary>//ChannelDiagList/ProfileChannelDiagItem</summary>
        public const string AllProfileChannelDiagItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelDiagList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ProfileChannelDiagItem;

        /// <summary>//UnitDiagTypeList/UnitDiagTypeItem</summary>
        public const string AllUnitDiagTypeItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_UnitDiagTypeList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_UnitDiagTypeItem;

        /// <summary>//ExternalTextList/PrimaryLanguage</summary>
        public const string PrimaryLanguage =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ExternalTextList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_PrimaryLanguage;

        
        /// <summary>//ExternalTextList/Language</summary>
        public const string AllLanguages =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ExternalTextList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_Language;

        #endregion

        #region XPathes for V2.31 Elements and Attributes

        /// <summary>//ChannelDiagList/ProfileChannelDiagItem</summary>
        public const string AllSystemDefinedChannelDiagItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelDiagList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SystemDefinedChannelDiagItem;

        #endregion


        //########################################################################################
        #region XPathes for V2.35 Elements and Attributes

        /// <summary>//ChannelProcessAlarmList/ChannelProcessAlarmItem</summary>
        public const string AllChannelProcessAlarmItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelProcessAlarmList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelProcessAlarmItem;

        /// <summary>//ChannelProcessAlarmList/SystemDefinedChannelProcessAlarmItem</summary>
        public const string AllSystemDefinedChannelProcessAlarmItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelProcessAlarmList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_SystemDefinedChannelProcessAlarmItem;

        /// <summary>//ChannelProcessAlarmList/ProfileChannelProcessAlarmItem</summary>
        public const string AllProfileChannelProcessAlarmItems =
            Constants.s_DoubleSlash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ChannelProcessAlarmList +
            Constants.s_Slash +
            Namespaces.s_PrefixGsdmlDeviceProfile +
            Constants.s_Colon +
            Elements.s_ProfileChannelProcessAlarmItem;

        #endregion


        //########################################################################################

    }
}


