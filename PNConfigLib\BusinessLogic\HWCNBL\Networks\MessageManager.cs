/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MessageManager.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Networks
{
    internal static class MessagesIe
    {
        public static Dictionary<IPAddressErrorCodes, string> IpAddressCompile { get; } = new Dictionary<IPAddressErrorCodes, string>
        {
            // Controller
            { IPAddressErrorCodes.WrongSubnetMask,         ConsistencyConstants.IPWrongSubnetMask},
            { IPAddressErrorCodes.AddressDifferentSubnet,   ConsistencyConstants.IPAddressDifferentSubnet},
            { IPAddressErrorCodes.NetAddressNull,           ConsistencyConstants.IPWrongSubnetMask},
            { IPAddressErrorCodes.NotAllowedAddressRange,   ConsistencyConstants.IPAddressRestrictedSubnet},
            { IPAddressErrorCodes.HostAdresseNull,          ConsistencyConstants.IPHostAddressNull},
            // Compile
            { IPAddressErrorCodes.AddressNotUnique,         ConsistencyConstants.IPMultipleAddress},
            { IPAddressErrorCodes.AddressClassInvalid,      ConsistencyConstants.IPUnrestrictedSubnetMaskNotSupported},
            { IPAddressErrorCodes.AddressTooLong,           ConsistencyConstants.IPTooLongaddress},
            { IPAddressErrorCodes.BroadcastAddress,         ConsistencyConstants.IPBroadcastAddress},
            { IPAddressErrorCodes.DefaultRouteAddress,      ConsistencyConstants.IPDefaultRouteAddress},
            { IPAddressErrorCodes.EmptyAddress,             ConsistencyConstants.IPEmptyAddress},
            { IPAddressErrorCodes.WrongAddress,             ConsistencyConstants.IPWrongAddress},
            { IPAddressErrorCodes.LoopbackAddress,          ConsistencyConstants.IPLoopbackAddress},

        };


        public static Dictionary<IPAddressErrorCodes, string> IpRouterCompile { get; } = new Dictionary<IPAddressErrorCodes, string>
        {
            // Compile
            { IPAddressErrorCodes.AddressClassInvalid,      ConsistencyConstants.IPRouterAddressClassInvalid},
            { IPAddressErrorCodes.AddressTooLong,           ConsistencyConstants.IPRouterAddressTooLong},
            { IPAddressErrorCodes.BroadcastAddress,         ConsistencyConstants.IPRouterBroadcastAddress},
            { IPAddressErrorCodes.DefaultRouteAddress,      ConsistencyConstants.IPRouterDefaultRouteAddress},
            { IPAddressErrorCodes.EmptyAddress,             ConsistencyConstants.IPRouterEmptyAddress},
            { IPAddressErrorCodes.WrongAddress,             ConsistencyConstants.IPRouterWrongAddress},
            { IPAddressErrorCodes.LoopbackAddress,          ConsistencyConstants.IPRouterLoopbackAddress},
            { IPAddressErrorCodes.WrongSubnetMask,         ConsistencyConstants.IPWrongSubnetMask}
        };


        public static Dictionary<IPAddressErrorCodes, string> IpSubnetMaskCompile { get; } = new Dictionary<IPAddressErrorCodes, string>
        {
             // Compile
             { IPAddressErrorCodes.WrongAddress,            ConsistencyConstants.IPWrongSubnetMask},
             { IPAddressErrorCodes.WrongSubnetMask,         ConsistencyConstants.IPWrongSubnetMask}
        };

    }
}