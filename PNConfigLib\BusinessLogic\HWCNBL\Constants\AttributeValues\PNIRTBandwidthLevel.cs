/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIRTBandwidthLevel.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.AttributeValues
{
    /// <summary>
    /// Contains all possible values of the PNIrtBandwidthLevel attribute.
    /// </summary>
    public enum PNIRTBandwidthLevel
    {
        /// <summary>
        /// Bandwidth level is not supported.
        /// </summary>
        None = 0,

        /// <summary>
        /// Maximum bandwidth for non realtime.
        /// </summary>
        MaximumNRT = 1,

        /// <summary>
        /// More bandwith for non real time.
        /// </summary>
        MoreNRT = 2,

        /// <summary>
        /// Fairly distributed badwith.
        /// </summary>
        Fair = 3,

        /// <summary>
        /// More Bandwith for cyclic IO
        /// </summary>
        MoreIO = 4,

        /// <summary>
        /// Maximum Bandwith for cyclic IO
        /// </summary>
        MaximumIO = 5,


    }
}