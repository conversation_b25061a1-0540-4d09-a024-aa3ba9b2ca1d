/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: IPConfigurationChecker.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.HWCNBL.Constants;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    class IPConfigurationChecker : IConsistencyChecker
    {

        private readonly Configuration m_Configuration;

        public IPConfigurationChecker(Configuration cfg)
        {
            m_Configuration = cfg;
        }

        public void Check()
        {
            IsIPConfiguredCorrectlyWithMultipleUseIOSystem();
        }

        private void IsIPConfiguredCorrectlyWithMultipleUseIOSystem()
        {
            IEnumerable<SubnetIOSystem> ioSystems = m_Configuration.Subnet?.SelectMany(s => s.IOSystem);

            IsIPConfiguredCorrectlyWithMultipleUseIOSystemCentral(ioSystems);
            IsIPConfiguredCorrectlyWithMultipleUseIOSystemDecentral(ioSystems);
        }

        private void IsIPConfiguredCorrectlyWithMultipleUseIOSystemCentral(IEnumerable<SubnetIOSystem> ioSystems)
        {
            foreach (SubnetIOSystem ioSys in ioSystems)
            {
                if (!ioSys.General.MultipleUseIOSystem)
                {
                    return;
                }

                CentralDeviceType centralDevice = m_Configuration.Devices.CentralDevice.FirstOrDefault(
                    cd => cd.CentralDeviceInterface?.EthernetAddresses?.IOSystemRefID == ioSys.IOSystemID);

                if (centralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.Item is CentralIPProtocolTypeSetInTheProject)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IPAddressSetWithAddressTailoring,
                        centralDevice.CentralDeviceInterface.InterfaceRefID,
                        ioSys.IOSystemID
                        );
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsIPConfiguredCorrectlyWithMultipleUseIOSystemDecentral(IEnumerable<SubnetIOSystem> ioSystems)
        {
            foreach (SubnetIOSystem ioSys in ioSystems)
            {
                if (!ioSys.General.MultipleUseIOSystem)
                {
                    return;
                }

                IEnumerable<DecentralDeviceType> decentralDevices = m_Configuration.Devices.DecentralDevice.Where(
                    cd => cd.DecentralDeviceInterface?.EthernetAddresses?.IOSystemRefID == ioSys.IOSystemID);

                if (decentralDevices.Any(d => d.DecentralDeviceInterface.EthernetAddresses.IPProtocol.Item is DecentralIPProtocolTypeSetInTheProject))
                {
                    List<DecentralDeviceType> ipConfiguredDevices = decentralDevices.Where(d => d.DecentralDeviceInterface.EthernetAddresses.IPProtocol.Item is DecentralIPProtocolTypeSetInTheProject).ToList();

                    foreach (DecentralDeviceType d in ipConfiguredDevices)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_IPAddressSetWithAddressTailoring,
                            d.DecentralDeviceInterface.InterfaceRefID,
                            ioSys.IOSystemID);
                    }
                    throw new ConsistencyCheckException();
                }
            }
        }
    }
}
