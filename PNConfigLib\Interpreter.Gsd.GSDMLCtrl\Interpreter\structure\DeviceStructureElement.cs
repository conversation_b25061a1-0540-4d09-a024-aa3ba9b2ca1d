/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: DeviceStructureElement.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

using C = PNConfigLib.Gsd.Interpreter.Common;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Structure
{
    /// <summary>
    /// The DeviceStructureElement object is the root object of the structural
    /// data object model for the GSD(ML) data.
    /// It contains the device specific properties, like identifikation, families 
    /// and a short information about the device. Additionally to that common 
    /// information, there is only the list of device access points available.
    /// </summary>
    /// <remarks>With the structure data object model, it is possible to get
    /// a quick and simple overview of the available access points, there
    /// contained modules and the available submodules.
    /// For all the structural objects, like device, access point, module
    /// and submodule, there is additionally some common information available.</remarks>
    public class DeviceStructureElement :
        C.GsdObject,
        GSDI.IDeviceStructureElement
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the DeviceStructureElement if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public DeviceStructureElement()
        {
            // Initialize the properties
            m_MainFamily = String.Empty;
            m_MainFamilyAsEnum = GSDI.MainFamilies.GSDMfGeneral;
            m_ProductFamily = String.Empty;
            m_InfoText = String.Empty;
            m_VendorName = String.Empty;
            m_VendorIdentNumber = 0;
            m_DeviceIdentNumber = 0;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private ArrayList m_DaPs;

        private string m_MainFamily;
        private string m_ProductFamily;
        private string m_InfoText;
        private string m_VendorName;
        private uint m_VendorIdentNumber;
        private uint m_DeviceIdentNumber;

        private GSDI.MainFamilies m_MainFamilyAsEnum;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the list of AccessPointStructureElement objects, which are
        /// available for this device.
        /// </summary>
        public virtual Array DeviceAccessPoints => this.m_DaPs?.ToArray();

        /// <summary>
        /// Accesses the MainFamily of the Device, which specifies the 
        /// assignment to a function class.
        /// </summary>
        public string MainFamily => this.m_MainFamily;

        /// <summary>
        /// Accesses the ProductFamily of the Device, which contains the 
        /// vendor specific assignment of the Device to a product family.
        /// </summary>
        public string ProductFamily => this.m_ProductFamily;

        /// <summary>
		/// Accesses the info text for the Device, which contains human 
		/// readable additional text information about a device.
		/// </summary>
		public string InfoText => this.m_InfoText;

        /// <summary>
        /// Accesses the name of the vendor of the Device.
        /// </summary>
        public string VendorName => this.m_VendorName;

        /// <summary>
		/// Accesses the vendor specific part of the DeviceIdentNumber.
		/// It is assigned by vendor association. Manufacturers of devices 
		/// have to apply for the VendorIdentNumber at the appropriate vendor 
		/// association.
		/// </summary>
		/// <remarks>For PROFINET the responsible association to assign a 
		/// unique VendorIdentNumber is the tradename owner.</remarks>
		public UInt32 VendorIdentNumber => this.m_VendorIdentNumber;

        /// <summary>
        /// Accesses the device specific part of the DeviceIdentNumber. 
        /// It is a unique ID for all devices of a vendor.
        /// </summary>
        /// <remarks>The vendor has to keep this ID unique.</remarks>
        public UInt32 DeviceIdentNumber => this.m_DeviceIdentNumber;

        #endregion

        //########################################################################################
        #region Methods
        /// <summary>
        /// Accesses the string and the enumeration representation of the MainFamily 
        /// of the Device.
        /// </summary>
        /// <param name="plMainFamily">Outparameter to which is written the 
        /// enumeration representation of the MainFamily.</param>
        /// <returns>The string representation of the MainFamily.</returns>
        public virtual string GetMainFamily(out GSDI.MainFamilies plMainFamily)
        {
            plMainFamily = this.m_MainFamilyAsEnum;
            return this.m_MainFamily;
        }
        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldDeviceAccessPoints;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_DaPs = hash[member] as ArrayList;

                member = Models.s_FieldMainFamily;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_MainFamily = hash[member] as string;

                member = Models.s_FieldProductFamily;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_ProductFamily = hash[member] as string;

                member = Models.s_FieldInfoText;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_InfoText = hash[member] as string;

                member = Models.s_FieldVendorName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_VendorName = hash[member] as string;

                member = Models.s_FieldVendorIdentNumber;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_VendorIdentNumber = (uint)hash[member];

                member = Models.s_FieldDeviceIdentNumber;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_DeviceIdentNumber = (uint)hash[member];


                member = Models.s_FieldMainFamilyAsEnum;
                if (hash.ContainsKey(member) && hash[member] is GSDI.MainFamilies)
                    this.m_MainFamilyAsEnum = (GSDI.MainFamilies)hash[member];

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {

            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectDeviceStructureElement);

            // ----------------------------------------------
            bool succeeded = this.SerializeMembers(option, ref writer);
            if (!succeeded)
                throw new SerializationException("Couldn't serialize members!");

            // Object end.
            writer.WriteEndElement();


            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldMainFamily, this.m_MainFamily);
            Export.WriteEnumProperty(ref writer, Models.s_FieldMainFamilyAsEnum, this.m_MainFamilyAsEnum.ToString(), Export.s_SubtypeMainFamilies);
            Export.WriteStringProperty(ref writer, Models.s_FieldProductFamily, this.m_ProductFamily);
            Export.WriteStringProperty(ref writer, Models.s_FieldInfoText, this.m_InfoText);
            Export.WriteStringProperty(ref writer, Models.s_FieldVendorName, this.m_VendorName);
            Export.WriteUint32Property(ref writer, Models.s_FieldVendorIdentNumber, this.m_VendorIdentNumber);
            Export.WriteUint32Property(ref writer, Models.s_FieldDeviceIdentNumber, this.m_DeviceIdentNumber);
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldDeviceAccessPoints, this.m_DaPs);

            return true;
        }

        #endregion

    }
}


