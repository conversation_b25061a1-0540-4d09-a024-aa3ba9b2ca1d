﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralSubmoduleConfigureManager.cs     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class DecentralSubmoduleConfigureManager
    {
        private Project m_Project;

        internal DecentralSubmoduleConfigureManager(Project project) 
        {
            m_Project = project;
        }

        internal void Configure(DecentralDeviceType xmlDecentralDevice,
            ModuleType xmlModule, 
            ModuleCatalog moduleCatalog,
            Module module,
            string gsdPath, 
            IOAddressManager ioAddressManager)
        {
            FillSubmoduleToModule(xmlModule, moduleCatalog, gsdPath, ioAddressManager);

            foreach (ModuleTypeSubmodule xmlSubmodule in xmlModule.Submodule)
            {
                Submodule submodule =
                    new Submodule(
                        Catalog.SubmoduleList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", gsdPath, xmlSubmodule.GSDRefID)]);
                bool isVirtual = moduleCatalog.VirtualSubmoduleList.Exists(
                    vsm => vsm.AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.GsdId)
                           && (string)vsm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]
                           == xmlSubmodule.GSDRefID);
                module.AddSubmodule(submodule, isVirtual);

                SubmoduleBL submoduleBL = new SubmoduleBL(submodule);
                bool isochronousSubmodule =
                    xmlDecentralDevice.DecentralDeviceInterface.IsochronousMode.IsochronousSubmodule
                        .Any(
                            sm => (sm.ModuleRefID == xmlModule.ModuleID)
                                  && (sm.SubmoduleRefID == xmlSubmodule.SubmoduleID));
                submoduleBL.Configure(xmlSubmodule, ioAddressManager, isochronousSubmodule);
                m_Project.BusinessLogicList.Add(submoduleBL);
            }
        }

        /// <summary>
        /// Gets free IO address considering IO type
        /// </summary>
        /// <param name="catalogObj"></param>
        /// <param name="ioType"></param>
        /// <param name="ioAddressManager"></param>
        /// <returns></returns>
        private static object GetIOAddress(
            PclCatalogObject catalogObj,
            IoTypes ioType,
            ref IOAddressManager ioAddressManager)
        {
            if (ioType != IoTypes.Input
                && ioType != IoTypes.Output)
            {
                return null;
            }

            string addressRangeAttributeName = ioType == IoTypes.Input
                                                   ? InternalAttributeNames.InAddressRange
                                                   : InternalAttributeNames.OutAddressRange;

            int addressRange =
                catalogObj.AttributeAccess.GetAnyAttribute<int>(
                    addressRangeAttributeName,
                    new AttributeAccessCode(),
                    -1);

            AttributeAccessCode aac = new AttributeAccessCode();
            if (!aac.IsOkay
                || addressRange == -1)
            {
                return null;
            }

            List<int> availableAddresses = ioAddressManager.GetFreeAddresses(addressRange, ioType, false);

            object ioAddress;
            if (ioType == IoTypes.Input)
            {
                ioAddress = new ModuleTypeInputAddresses { StartAddress = (uint)availableAddresses.First() };
            }
            else
            {
                ioAddress = new ModuleTypeOutputAddresses { StartAddress = (uint)availableAddresses.First() };
            }

            return ioAddress;
        }

        /// <summary>
        /// Fills IO addresses of the catalog object
        /// </summary>
        /// <param name="catalogObj"></param>
        /// <param name="ioAddressManager"></param>
        /// <returns></returns>
        private static List<object> FillIOAddresses(PclCatalogObject catalogObj, ref IOAddressManager ioAddressManager)
        {
            List<object> retval = new List<object>();

            object inAddress = GetIOAddress(catalogObj, IoTypes.Input, ref ioAddressManager);
            if (inAddress != null)
            {
                retval.Add(inAddress);
            }

            object outAddress = GetIOAddress(catalogObj, IoTypes.Output, ref ioAddressManager);
            if (outAddress != null)
            {
                retval.Add(outAddress);
            }

            return retval;
        }

        private static List<ModuleTypeSubmodule> GetSubmodulesBySlotRelation(
            ModuleCatalog moduleCatalog,
            ModuleType xmlModule,
            string gsdPath,
            SlotRelationType slotRelType,
            ref IOAddressManager ioAddressManager)
        {
            List<ModuleTypeSubmodule> retval = new List<ModuleTypeSubmodule>();

            Dictionary<KeyValuePair<string, int>, SubmoduleCatalog> submoduleLookup =
                ProjectManagerUtilities.GetSubmoduleLookupBySlotRelation(moduleCatalog, gsdPath, slotRelType);
            int fixedSubmoduleCounter = 0;
            foreach (KeyValuePair<string, int> submoduleInfo in submoduleLookup.Keys)
            {
                if ((xmlModule.Submodule != null)
                    && ((xmlModule.Submodule.Count == 0)
                        || ((xmlModule.Submodule.Count > 0)
                            && xmlModule.Submodule.All(x => x.GSDRefID != submoduleInfo.Key))))
                {
                    ModuleTypeSubmodule xmlSubmodule = new ModuleTypeSubmodule
                    {
                        GSDRefID = submoduleInfo.Key,
                        SubslotNumber =
                            (ushort)submoduleInfo.Value,
                        SubmoduleID =
                            string.Format(CultureInfo.InvariantCulture,
                                "{0}_{1}",
                                submoduleInfo.Key,
                                ++fixedSubmoduleCounter)
                    };
                    xmlSubmodule.IOAddresses = FillIOAddresses(
                        submoduleLookup[submoduleInfo],
                        ref ioAddressManager);
                    retval.Add(xmlSubmodule);
                }
            }
            return retval;
        }

        public static void FillSubmoduleToModule(
          ModuleType xmlModule,
          ModuleCatalog moduleCatalog,
          string gsdPath,
          IOAddressManager ioAddressManager)
        {
            List<ModuleTypeSubmodule> fixedSubmodules = GetSubmodulesBySlotRelation(
                moduleCatalog,
                xmlModule,
                gsdPath,
                SlotRelationType.FixedInSlots,
                ref ioAddressManager);
            List<ModuleTypeSubmodule> usedInSlotSubmodules = GetSubmodulesBySlotRelation(
                moduleCatalog,
                xmlModule,
                gsdPath,
                SlotRelationType.UsedInSlots,
                ref ioAddressManager);

            if (usedInSlotSubmodules != null)
            {
                foreach (ModuleTypeSubmodule submodule in usedInSlotSubmodules)
                {
                    if (xmlModule.Submodule.Any(x => x.GSDRefID == submodule.GSDRefID))
                    {
                        continue;
                    }

                    if (
                        xmlModule.Submodule.Any(
                            x =>
                                (x.SubslotNumber == submodule.SubslotNumber) && string.IsNullOrEmpty(x.GSDRefID)))
                    {
                        ModuleTypeSubmodule currentSubmodule =
                            xmlModule.Submodule.FirstOrDefault(
                                x =>
                                    (x.SubslotNumber == submodule.SubslotNumber) && string.IsNullOrEmpty(x.GSDRefID));
                        xmlModule.Submodule.Remove(currentSubmodule);
                        continue;
                    }

                    if (
                        xmlModule.Submodule.Any(
                            x =>
                                (x.SubslotNumber == submodule.SubslotNumber) && (x.GSDRefID != submodule.GSDRefID)))
                    {
                        continue;
                    }
                    xmlModule.Submodule.Add(submodule);
                }
            }

            xmlModule.Submodule.AddRange(fixedSubmodules);
        }
    }
}
