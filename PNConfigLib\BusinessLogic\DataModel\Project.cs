/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Project.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;

#endregion

namespace PNConfigLib.DataModel
{
    /// <summary>
    /// This class represents a PNConfigLib project.
    /// </summary>
    internal class Project
    {
        /// <summary>
        /// The list of Subnet objects in this Project.
        /// </summary>
        /// <remarks>
        /// All elements can be reached in the project hierarchy beginning from the subnet.
        /// </remarks>
        public List<Subnet> SubnetList = new List<Subnet>();

        public List<IBusinessLogic> BusinessLogicList = new List<IBusinessLogic>();

        /// <summary>
        /// Default constructor of the Project object.
        /// </summary>
        /// <param name="projectName">Name of the project.</param>
        public Project(string projectName)
        {
            Name = projectName;
        }

        /// <summary>
        /// Name of the project.
        /// </summary>
        private string Name { get; }
    }
}