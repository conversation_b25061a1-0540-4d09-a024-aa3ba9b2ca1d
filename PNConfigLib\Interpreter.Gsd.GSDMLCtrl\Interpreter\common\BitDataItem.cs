/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BitDataItem.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The BitDataItem element contains information about ...
    /// </summary>
    public class BitDataItem :
        GsdObject,
        GSDI.IBitDataItem
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the BitDataItem if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public BitDataItem()
        {
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
            m_BitOffset = 0;
            m_BitDataItemId = String.Empty;
            m_BitLength = 0;
            m_Format = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_Name;
        private string m_NameTextID;    // Only private!
        private uint m_BitOffset;
        private string m_BitDataItemId;
        private uint m_BitLength;
        private string m_Format;
        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the language specific name of the bit data item.
        /// </summary>
        public string Name => this.m_Name;

        /// <summary>
        /// Accesses the language specific name of the bit data item.
        /// </summary>
        public string NameTextId => this.m_NameTextID;

        /// <summary>
        /// Accesses the ...
        /// </summary>
        public UInt32 BitOffset => this.m_BitOffset;

        /// <summary>
        /// Accesses the ID of the bit data item.
        /// </summary>
        public string BitDataItemId => this.m_BitDataItemId;

        /// <summary>
        /// Accesses the bit length of the bit data item.
        /// </summary>
        public UInt32 BitLength => this.m_BitLength;

        /// <summary>
        /// Accesses the format of the bit data item.
        /// </summary>
        public string Format => this.m_Format;

        #region COM Interface Members Only

        #endregion

        #endregion


        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Name = hash[member] as string;

                member = Models.s_FieldNameTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_NameTextID = hash[member] as string;

                member = Models.s_FieldBitOffset;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_BitOffset = (uint)hash[member];

                member = Models.s_FieldBitDataItemId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_BitDataItemId = (string)hash[member];

                member = Models.s_FieldBitLength;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_BitLength = (uint)hash[member];

                member = Models.s_FieldFormat;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Format = hash[member] as string;
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectBitDataItem);

            // ----------------------------------------------
            bool succeeded = this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return succeeded;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, this.m_NameTextID);
            Export.WriteUint32Property(ref writer, Models.s_FieldBitOffset, this.m_BitOffset);
            Export.WriteStringProperty(ref writer, Models.s_FieldBitDataItemId, this.m_BitDataItemId);
            Export.WriteUint32Property(ref writer, Models.s_FieldBitLength, this.m_BitLength);
            Export.WriteStringProperty(ref writer, Models.s_FieldFormat, this.m_Format);

            return true;
        }

        #endregion
    }
}


