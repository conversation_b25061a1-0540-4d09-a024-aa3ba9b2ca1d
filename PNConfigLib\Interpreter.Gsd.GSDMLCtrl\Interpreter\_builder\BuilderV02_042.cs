/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_042.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections;
using System.Xml.XPath;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02042 :
        BuilderV02041
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02042()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version242);
        }

        #endregion


        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            try
            {

                return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
            }
            catch (PreparationException)
            {
            }

            return null;
        }

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for InterfaceSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSupportedDelayMeasurements, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // --------------------------------------------

            // Get SupportedDelayMeasurements attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_SupportedDelayMeasurements, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSupportedDelayMeasurements] = Help.SeparateTokenList(attr);
            }
        }

        protected override void PrepareMauTypeItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMaxTransferTimeTX, null);
            hash.Add(Models.s_FieldMaxTransferTimeRX, null);

            // Call base class method first.
            base.PrepareMauTypeItem(nav, ref hash);

            // Get MaxTransferTimeTX attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_MaxTransferTimeTx, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxTransferTimeTX] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // Get MaxTransferTimeRX attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MaxTransferTimeRx, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxTransferTimeRX] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        protected override void PrepareIOConfigData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOConfigData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMaxApplicationARs, null);

            // Call base class method first.
            base.PrepareIOConfigData(nav, ref hash);

            // Attribute ApplicationLengthIncludesIOxS
            string attr = nav.GetAttribute(Attributes.s_MaxApplicationARs, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxApplicationARs] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        #endregion

        #endregion

    }
}