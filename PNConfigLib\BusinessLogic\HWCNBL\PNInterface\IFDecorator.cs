/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IFDecorator.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.IODevice.ConfigStrategy;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    public abstract class IFDecorator : HwcnBusinessLogic ,IInterfaceBusinessLogic
    {
        private IInterfaceBusinessLogic decoratedInterfaceBL;

        private PNDeviceConfigStrategy m_Strategy;

        protected IFDecorator(IInterfaceBusinessLogic decoratedIFBL)
        {
            if (decoratedIFBL == null)
            {
                throw new ArgumentNullException(nameof(decoratedIFBL));
            }

            Interface = decoratedIFBL.Interface;
            Interface.InterfaceBL = this;
            decoratedInterfaceBL = decoratedIFBL;
        }

        public Interface Interface { get; }

        internal PNDeviceConfigStrategy Strategy
        {
            get
            {
                if (!(decoratedInterfaceBL is PNBaseInterfaceBL))
                {
                    while (!(decoratedInterfaceBL is PNDeviceConfigStrategy))
                    {
                        decoratedInterfaceBL = ((IFDecorator)decoratedInterfaceBL).decoratedInterfaceBL;
                    }

                    m_Strategy = (PNDeviceConfigStrategy)decoratedInterfaceBL;
                }
                return m_Strategy;
            }
        }

        public void Configure(IConfigInterface xmlDeviceInterface)
        {
            Configure(xmlDeviceInterface, null);
        }

        public virtual void Configure(
            IConfigInterface xmlDeviceInterface,
            SyncDomainType syncDomainType)
        {
            decoratedInterfaceBL.Configure(xmlDeviceInterface, syncDomainType);
        }
        
        public virtual void InitBL()
        {
            decoratedInterfaceBL.InitBL();
        }
    }
}