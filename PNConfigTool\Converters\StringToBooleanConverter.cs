using System;
using System.Globalization;
using System.Windows.Data;

namespace PNConfigTool.Converters
{
    /// <summary>
    /// Converts a string to a boolean value based on whether the string is null or empty.
    /// </summary>
    public class StringToBooleanConverter : IValueConverter
    {
        /// <summary>
        /// Converts a string to a boolean.
        /// </summary>
        /// <param name="value">The string value to check.</param>
        /// <param name="targetType">The target type (Boolean).</param>
        /// <param name="parameter">If "false" or False, inverts the result.</param>
        /// <param name="culture">The culture information.</param>
        /// <returns>True if the string is not null or empty, False otherwise (or inverted if parameter is "false").</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool result = !string.IsNullOrEmpty(value as string);
            
            // Check if we need to invert the result
            if (parameter != null)
            {
                if (parameter is bool boolParam && !boolParam)
                    result = !result;
                else if (parameter is string strParam && 
                        (strParam.Equals("false", StringComparison.OrdinalIgnoreCase) ||
                         strParam.Equals("0", StringComparison.OrdinalIgnoreCase)))
                    result = !result;
            }
            
            return result;
        }

        /// <summary>
        /// Converts a boolean back to a string (not implemented).
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("StringToBooleanConverter.ConvertBack is not implemented.");
        }
    }
} 