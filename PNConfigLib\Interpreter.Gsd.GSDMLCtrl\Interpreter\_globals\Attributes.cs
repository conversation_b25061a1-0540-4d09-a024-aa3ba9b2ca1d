/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Attributes.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all attribute names of the GSDML specification and default value settings for
    /// optional attributes.
    /// </summary>
    internal static class Attributes
    {
        //########################################################################################
        #region General XML Attributes
        // Contains general xml attribute names as constants

        /// <summary>name of the xml language attribute</summary>
        public const string s_XMLLang = @"xml:lang";

        #endregion

        //########################################################################################
        #region GSDML V1.0 Attributes
        // Contains all attribute names as constants

        /// <summary>name of the VendorID attribute</summary>
        public const string s_VendorId = @"VendorID";
        /// <summary>name of the DeviceID attribute</summary>
        public const string s_DeviceId = @"DeviceID";
        /// <summary>name of the TextId attribute</summary>
        public const string s_TextId = @"TextId";
        /// <summary>name of the Value attribute</summary>
        public const string s_Value = @"Value";
        /// <summary>name of the MainFamily attribute</summary>
        public const string s_MainFamily = @"MainFamily";
        /// <summary>name of the ProductFamily attribute</summary>
        public const string s_ProductFamily = @"ProductFamily";
        /// <summary>name of the ID attribute</summary>
		public const string ID = @"ID";
        /// <summary>name of the PhysicalSlots attribute</summary>
        public const string s_PhysicalSlots = @"PhysicalSlots";
        /// <summary>name of the ModuleIdentNumber attribute</summary>
        public const string s_ModuleIdentNumber = @"ModuleIdentNumber";
        /// <summary>name of the MinDeviceInterval attribute</summary>
        public const string s_MinDeviceInterval = @"MinDeviceInterval";
        /// <summary>name of the ImplementationType attribute</summary>
        public const string s_ImplementationType = @"ImplementationType";
        /// <summary>name of the DNS_CompatibleName attribute</summary>
        public const string s_DnsCompatibleName = @"DNS_CompatibleName";
        /// <summary>name of the FixedInSlots attribute</summary>
        public const string s_FixedInSlots = @"FixedInSlots";
        /// <summary>name of the UsedInSlots attribute</summary>
        public const string s_UsedInSlots = @"UsedInSlots";
        /// <summary>name of the AllowedInSlots attribute</summary>
        public const string s_AllowedInSlots = @"AllowedInSlots";
        /// <summary>name of the ObjectUUID_LocalIndex attribute</summary>
        public const string s_ObjectUUIDLocalIndex = @"ObjectUUID_LocalIndex";
        /// <summary>name of the ExtendedAddressAssignmentSupported attribute</summary>
		public const string s_ExtendedAddressAssignmentSupported = @"ExtendedAddressAssignmentSupported";
        /// <summary>name of the CategoryRef attribute</summary>
        public const string s_CategoryRef = @"CategoryRef";
        /// <summary>name of the SubCategory1Ref attribute</summary>
        public const string s_SubCategory1Ref = @"SubCategory1Ref";
        /// <summary>name of the MaxInputLength attribute</summary>
        public const string s_MaxInputLength = @"MaxInputLength";
        /// <summary>name of the MaxOutputLength attribute</summary>
        public const string s_MaxOutputLength = @"MaxOutputLength";
        /// <summary>name of the MaxDataLength attribute</summary>
        public const string s_MaxDataLength = @"MaxDataLength";
        /// <summary>name of the ModuleItemTarget attribute</summary>
        public const string s_ModuleItemTarget = @"ModuleItemTarget";
        /// <summary>name of the SubmoduleIdentNumber attribute</summary>
        public const string s_SubmoduleIdentNumber = @"SubmoduleIdentNumber";
        /// <summary>name of the GraphicItemTarget attribute</summary>
        public const string s_GraphicItemTarget = @"GraphicItemTarget";
        /// <summary>name of the Type attribute</summary>
        public const string s_Type = @"Type";
        /// <summary>name of the AR_BlockVersion attribute</summary>
        public const string s_ArBlockVersion = @"AR_BlockVersion";
        /// <summary>name of the IOCR_BlockVersion attribute</summary>
        public const string s_IocrBlockVersion = @"IOCR_BlockVersion";
        /// <summary>name of the AlarmCR_BlockVersion attribute</summary>
        public const string s_AlarmCrBlockVersion = @"AlarmCR_BlockVersion";
        /// <summary>name of the SubmoduleDataBlockVersion attribute</summary>
        public const string s_SubmoduleDataBlockVersion = @"SubmoduleDataBlockVersion";
        /// <summary>name of the SendClock attribute</summary>
        public const string s_SendClock = @"SendClock";
        /// <summary>name of the ReductionRatio attribute</summary>
        public const string s_ReductionRatio = @"ReductionRatio";
        /// <summary>name of the Content attribute</summary>
        public const string s_Content = @"Content";
        /// <summary>name of the ErrorType attribute</summary>
        public const string s_ErrorType = @"ErrorType";
        /// <summary>name of the UserStructureIdentifier attribute</summary>
        public const string s_UserStructureIdentifier = @"UserStructureIdentifier";
        /// <summary>name of the IOPS_Length attribute</summary>
        public const string s_IopsLength = @"IOPS_Length";
        /// <summary>name of the IOCS_Length attribute</summary>
        public const string s_IocsLength = @"IOCS_Length";
        /// <summary>name of the Consistency attribute</summary>
        public const string s_Consistency = @"Consistency";
        /// <summary>name of the DataType attribute</summary>
        public const string s_DataType = @"DataType";
        /// <summary>name of the Length attribute</summary>
        public const string s_Length = @"Length";
        /// <summary>name of the UseAsBits attribute</summary>
        public const string s_UseAsBits = @"UseAsBits";
        /// <summary>name of the Index attribute</summary>
        public const string s_Index = @"Index";
        /// <summary>name of the TransferSequence attribute</summary>
        public const string s_TransferSequence = @"TransferSequence";
        /// <summary>name of the GraphicFile attribute</summary>
        public const string s_GraphicFile = @"GraphicFile";
        /// <summary>name of the Data attribute</summary>
        public const string s_Data = @"Data";
        /// <summary>name of the ByteOffset attribute</summary>
        public const string s_ByteOffset = @"ByteOffset";
        /// <summary>name of the BitOffset attribute</summary>
        public const string s_BitOffset = @"BitOffset";
        /// <summary>name of the DefaultValue attribute</summary>
        public const string s_DefaultValue = @"DefaultValue";
        /// <summary>name of the AllowedValues attribute</summary>
        public const string s_AllowedValues = @"AllowedValues";
        /// <summary>name of the ValueItemTarget attribute</summary>
        public const string s_ValueItemTarget = @"ValueItemTarget";
        /// <summary>name of the BitLength attribute</summary>
        public const string s_BitLength = @"BitLength";
        /// <summary>name of the Changeable attribute</summary>
        public const string s_Changeable = @"Changeable";
        /// <summary>name of the Visible attribute</summary>
        public const string s_Visible = @"Visible";

        #endregion

        //########################################################################################
        #region GSDML V2.0 Attributes
        // Contains all attribute names as constants

        /// <summary>name of the RequiredSchemaVersion attribute</summary>
        public const string s_RequiredSchemaVersion = @"RequiredSchemaVersion";
        /// <summary>name of the M_ProviderModeSupported attribute</summary>
        public const string s_MProviderModeSupported = @"M_ProviderModeSupported";
        /// <summary>name of the MaxM_ProviderLinks attribute</summary>
        public const string s_MaxMProviderLinks = @"MaxM_ProviderLinks";
        /// <summary>name of the SupportedRT_Class attribute</summary>
        public const string s_SupportedRTClass = @"SupportedRT_Class";
        /// <summary>name of the MaxPortTxDelay attribute</summary>
        public const string s_MaxPortTxDelay = @"MaxPortTxDelay";
        /// <summary>name of the MaxPortRxDelay attribute</summary>
        public const string s_MaxPortRxDelay = @"MaxPortRxDelay";
        /// <summary>name of the MaxBridgeDelay attribute</summary>
        public const string s_MaxBridgeDelay = @"MaxBridgeDelay";
        /// <summary>name of the IsochroneModeRequired attribute</summary>
        public const string s_IsochroneModeRequired = @"IsochroneModeRequired";
        /// <summary>name of the T_PLL_MAX attribute</summary>
        public const string s_PllMax = @"T_PLL_MAX";
        /// <summary>name of the SupportedRole attribute</summary>
        public const string s_SupportedRole = @"SupportedRole";
        /// <summary>name of the MaxLocalJitter attribute</summary>
        public const string s_MaxLocalJitter = @"MaxLocalJitter";
        // <summary>name of the F_Parameter attribute</summary>
        //public const string F_Parameter = @"F_Parameter";
        /// <summary>name of the SubslotNumber attribute</summary>
        public const string s_SubslotNumber = @"SubslotNumber";
        /// <summary>name of the FixedInSubslots attribute</summary>
        public const string s_FixedInSubslots = @"FixedInSubslots";
        /// <summary>name of the Version attribute</summary>
        public const string s_Version = @"Version";
        /// <summary>name of the Label attribute</summary>
        public const string s_Label = @"Label";
        /// <summary>name of the MAUType attribute</summary>
        public const string s_MauType = @"MAUType";
        /// <summary>name of the T_DC_Base attribute</summary>
        public const string s_DcBase = @"T_DC_Base";
        /// <summary>name of the T_DC_Min attribute</summary>
        public const string s_DcMin = @"T_DC_Min";
        /// <summary>name of the T_DC_Max attribute</summary>
        public const string s_DcMax = @"T_DC_Max";
        /// <summary>name of the T_IO_Base attribute</summary>
        public const string s_IOBase = @"T_IO_Base";
        /// <summary>name of the T_IO_InputMin attribute</summary>
        public const string s_IOInputMin = @"T_IO_InputMin";
        /// <summary>name of the T_IO_OutputMin attribute</summary>
        public const string s_IOOutputMin = @"T_IO_OutputMin";
        /// <summary>name of the PROFIsafeSupported attribute</summary>
        public const string s_ProfIsafeSupported = @"PROFIsafeSupported";
        /// <summary>name of the F_ParamDescCRC attribute</summary>
        public const string s_FParamDescCrc = @"F_ParamDescCRC";
        /// <summary>name of the API attribute</summary>
        public const string s_Api = @"API";
        /// <summary>name of the MaxNumberIR_FrameData attribute</summary>
        public const string s_MaxNumberIRFrameData = @"MaxNumberIR_FrameData";
        /// <summary>name of the NumberOfAdditionalInputCR attribute</summary>
        public const string s_NumberOfAdditionalInputCr = @"NumberOfAdditionalInputCR";
        /// <summary>name of the NumberOfAdditionalOutputCR attribute</summary>
        public const string s_NumberOfAdditionalOutputCr = @"NumberOfAdditionalOutputCR";
        /// <summary>name of the NumberOfAdditionalMulticastProviderCR attribute</summary>
        public const string s_NumberOfAdditionalMulticastProviderCr = @"NumberOfAdditionalMulticastProviderCR";
        /// <summary>name of the NumberOfMulticastConsumerCR attribute</summary>
        public const string s_NumberOfMulticastConsumerCr = @"NumberOfMulticastConsumerCR";
        /// <summary>name of the MultipleWriteSupported attribute</summary>
        public const string s_MultipleWriteSupported = @"MultipleWriteSupported";
        /// <summary>name of the IOXS_Required attribute</summary>
        public const string s_IOXSRequired = @"IOXS_Required";
        /// <summary>name of the F_IO_StructureDescCRC attribute</summary>
        public const string s_FIOStructureDescCrc = @"F_IO_StructureDescCRC";
        /// <summary>name of the IsochroneModeSupported attribute</summary>
        public const string s_IsochroneModeSupported = @"IsochroneModeSupported";
        /// <summary>name of the Name attribute</summary>
        public const string s_Name = @"Name";

        #endregion

        //########################################################################################
        #region GSDML V2.1 Attributes
        // Contains all attribute names as constants

        /// <summary>name of the FiberOpticTypes attribute</summary>
        public const string s_FiberOpticTypes = @"FiberOpticTypes";
        /// <summary>name of the PowerBudgetControlSupported attribute</summary>
        public const string s_PowerBudgetControlSupported = @"PowerBudgetControlSupported";
        /// <summary>name of the NetworkComponentDiagnosisSupported attribute</summary>
        public const string s_NetworkComponentDiagnosisSupported = @"NetworkComponentDiagnosisSupported";
        /// <summary>name of the MAUTypes attribute</summary>
        public const string s_MauTypes = @"MAUTypes";
        /// <summary>name of the LinkStateDiagnosisCapability attribute</summary>
        public const string s_LinkStateDiagnosisCapability = @"LinkStateDiagnosisCapability";
        /// <summary>name of the SupportedRT_Classes attribute</summary>
        public const string s_SupportedRTClasses = @"SupportedRT_Classes";
        /// <summary>name of the PortDeactivationSupported attribute</summary>
        public const string s_PortDeactivationSupported = @"PortDeactivationSupported";
        /// <summary>name of the AddressAssignment attribute</summary>
        public const string s_AddressAssignment = @"AddressAssignment";
        /// <summary>name of the Writeable_IM_Records attribute</summary>
        public const string s_WriteableImRecords = @"Writeable_IM_Records";

        /// <summary>name of the RemoteApplicationTimeout attribute</summary>
        public const string s_RemoteApplicationTimeout = @"RemoteApplicationTimeout";

        /// <summary>name of the SubSlotNumber attribute</summary>
        public const string s_SlotNumber = @"SlotNumber";

        /// <summary>name of the PhysicalSubslots attribute</summary>
        public const string s_PhysicalSubslots = @"PhysicalSubslots";

        /// <summary>name of the UsedInSubslots attribute</summary>
        public const string s_UsedInSubslots = @"UsedInSubslots";

        /// <summary>name of the AllowedInSubslots attribute</summary>
        public const string s_AllowedInSubslots = @"AllowedInSubslots";

        /// <summary>name of the SubmoduleItemTarget attribute</summary>
        public const string s_SubmoduleItemTarget = @"SubmoduleItemTarget";

        /// <summary>name of the SupportedProtocols attribute</summary>
        public const string s_SupportedProtocols = @"SupportedProtocols";

        /// <summary>name of the SupportedMibs attribute</summary>
        public const string s_SupportedMibs = @"SupportedMibs";

        /// <summary>name of the  //InterfaceSubmoduleItem/MediaRedundancy/@RT_MediaRedundancySupported attribute</summary>
        public const string s_RTMediaRedundancySupported = @"RT_MediaRedundancySupported";

        /// <summary>name of the  //InterfaceSubmoduleItem/MediaRedundancy/@SupportedRole attribute</summary>
        public const string s_MrSupportedRoles = @"SupportedRole";

        /// <summary>name of the IsDefaultRingport attribute</summary>
        public const string s_IsDefaultRingport = @"IsDefaultRingport";

        /// <summary>name of the SupportsRingportConfig attribute</summary>
        public const string s_SupportsRingportConfig = @"SupportsRingportConfig";

        /// <summary>name of the Id attribute</summary>
        public const string Id = @"Id";

        /// <summary>name of the MaintenanceAlarmState attribute</summary>
        public const string s_MaintenanceAlarmState = @"MaintenanceAlarmState";

        /// <summary>name of the SlotList attribute</summary>
        public const string s_SlotList = @"SlotList";

        #endregion

        //########################################################################################
        #region GSDML V2.2 Attributes

        /// <summary>name of the SupportedSyncProtocols attribute</summary>
        public const string s_SupportedSyncProtocols = @"SupportedSyncProtocols";
        /// <summary>name of the DCPBoundarySupported attribute</summary>
        public const string s_DcpBoundarySupported = @"DCP_BoundarySupported";
        /// <summary>name of the PTPBoundarySupported attribute</summary>
        public const string s_PtpBoundarySupported = @"PTP_BoundarySupported";
        /// <summary>name of the DCP_HelloSupported attribute</summary>
        public const string s_DcpHelloSupported = @"DCP_HelloSupported";
        /// <summary>name of the ParameterizationSpeedupSupported attribute</summary>
        public const string s_ParameterizationSpeedupSupported = @"ParameterizationSpeedupSupported";
        /// <summary>name of the NameOfStationNotTransferable attribute</summary>
        public const string s_NameOfStationNotTransferable = @"NameOfStationNotTransferable";
        /// <summary>name of the MaxSupportedRecordSize attribute</summary>
        public const string s_MaxSupportedRecordSize = @"MaxSupportedRecordSize";
        /// <summary>name of the PowerOnToCommReady attribute</summary>
        public const string s_PowerOnToCommReady = @"PowerOnToCommReady";
        /// <summary>name of the F_IO_StructureDescVersion attribute</summary>
        public const string s_FIOStructureDescVersion = @"F_IO_StructureDescVersion";

        public const string s_IsochroneModeInRTClasses = @"IsochroneModeInRT_Classes";

        public const string s_ReductionRatioPow2 = @"ReductionRatioPow2";

        public const string s_ReductionRatioNonPow2 = @"ReductionRatioNonPow2";

        public const string s_BlockVersionHigh = @"High";
        public const string s_BlockVersionLow = @"Low";
        public const string s_PullModuleAlarmSupported = @"PullModuleAlarmSupported";

        #endregion

        //########################################################################################
        #region GSDML V2.25 Attributes

        /// <summary>name of the ParameterizationDisallowed attribute</summary>
        public const string s_ParameterizationDisallowed = @"ParameterizationDisallowed";

        /// <summary>name of the PreferredSendclock attribute</summary>
        public const string s_PreferredSendClock = @"PreferredSendClock";

        /// <summary>name of the NumberOfAR attribute</summary>
        public const string s_NumberOfAr = @"NumberOfAR";

        /// <summary>name of the SharedDeviceSupported attribute</summary>
        public const string s_SharedDeviceSupported = @"SharedDeviceSupported";

        /// <summary>name of the SharedInputSupported attribute</summary>
        public const string s_SharedInputSupported = @"SharedInputSupported";

        /// <summary>name of the MaxTotalDataLength attribute</summary>
        public const string s_MaxTotalDataLength = @"MaxTotalDataLength";

        /// <summary>name of the DeviceAccessSupported attribute</summary>
        public const string s_DeviceAccessSupported = @"DeviceAccessSupported";

        /// <summary>name of the MulticastBoundarySupported attribute</summary>
        public const string s_MulticastBoundarySupported = @"MulticastBoundarySupported";

        /// <summary>name of the CheckMAUTypeSupported attribute</summary>
        public const string s_CheckMauTypeSupported = @"CheckMAUTypeSupported";

        /// <summary>name of the DelayMeasurementSupported attribute</summary>
        public const string s_DelayMeasurementSupported = @"DelayMeasurementSupported";

        /// <summary>name of the AdditionalProtocolsSupported attribute</summary>
        public const string s_AdditionalProtocolsSupported = @"AdditionalProtocolsSupported";

        /// <summary>name of the WebServer attribute</summary>
        public const string s_WebServer = @"WebServer";

        /// <summary>name of the SupportedSubstitutionModes attribute</summary>
        public const string s_SupportedSubstitutionModes = @"SupportedSubstitutionModes";

        /// <summary>name of the Count attribute</summary>
        public const string s_Count = @"Count";

        /// <summary>name of the MaxSupported attribute</summary>
        public const string s_MaxSupported = @"MaxSupported";

        /// <summary>name of the Range attribute</summary>
        public const string s_Range = @"Range";

        #endregion

        //########################################################################################
        #region GSDML V2.3 Attributes

        public const string s_ForwardingMode = @"ForwardingMode";

        /// <summary> name of the FragmentationType attribute </summary>
        public const string s_FragmentationType = @"FragmentationType";

        public const string s_MrpdSupported = @"MRPD_Supported";
        public const string s_MrtSupported = @"MRT_Supported";
        public const string s_StartupMode = @"StartupMode";

        /// <summary>name of the MaxBridgeDelayFFW attribute</summary>
        public const string s_MaxBridgeDelayFfw = @"MaxBridgeDelayFFW";

        /// <summary> name of the AlignDFP_Subframes attribute </summary>
        public const string s_AlignDfpSubframes = @"AlignDFP_Subframes";

        /// <summary> name of the MaxDFP_Feed attribute </summary>
        public const string s_MaxDfpFeed = @"MaxDFP_Feed";

        public const string s_MaxDfpFrames = @"MaxDFP_Frames";

        /// <summary> name of the MaxRedPeriodLength attribute </summary>
        public const string s_MaxRedPeriodLength = @"MaxRedPeriodLength";

        public const string s_MaxRangeIrFrameId = @"MaxRangeIR_FrameID";

        public const string s_MinFso = @"MinFSO";

        public const string s_PeerToPeerJitter = @"PeerToPeerJitter";

        public const string s_DeviceType = @"DeviceType";

        public const string s_MaxSwitchOverTime = @"MaxSwitchOverTime";

        public const string s_NumberOfSrArSets = @"NumberOfSR_AR_Sets";

        public const string s_PrimaryArOnBothNapsSupported = @"PrimaryAR_OnBothNAPsSupported";

        public const string s_ShortPreamble100MbitSupported = @"ShortPreamble100MBitSupported";

        public const string s_CirSupported = @"CIR_Supported";

        public const string s_LldpnoDSupported = @"LLDP_NoD_Supported";

        public const string s_ResetToFactoryModes = @"ResetToFactoryModes";

        public const string s_PNioVersion = @"PNIO_Version";
        public const string s_ConformanceClass = @"ConformanceClass";
        public const string s_ApplicationClass = @"ApplicationClass";
        public const string s_NetloadClass = @"NetloadClass";

        public const string s_AutoConfigurationSupported = @"AutoconfigurationSupported";

        public const string s_MenuTarget = @"MenuTarget";

        public const string s_ParameterTarget = @"ParameterTarget";

        public const string s_FieldbusType = @"FieldbusType";

        public const string s_MaxApplicationDataLength = @"MaxApplicationDataLength";

        public const string s_MaxApplicationInputLength = @"MaxApplicationInputLength";

        public const string s_MaxApplicationOutputLength = @"MaxApplicationOutputLength";

        public const string s_Status = @"Status";
        public const string s_ErrorCode2 = @"ErrorCode2";
        public const string s_FSupportedParameters = @"F_SupportedParameters";

        public const string s_PrmBeginPrmEndSequenceSupported = @"PrmBeginPrmEndSequenceSupported";

        public const string s_RTInputOnBackupArSupported = @"RT_InputOnBackupAR_Supported";

        public const string s_NumberOfArSets = @"NumberOfAR_Sets";
        public const string s_IOSupervisorSupported = @"IO_SupervisorSupported";

        public const string s_ChangeableWithBump = @"ChangeableWithBump";

        #endregion

        //########################################################################################
        #region GSDML V2.31 Attributes

        public const string s_SupportedMultipleRoles = @"SupportedMultipleRole";
        public const string s_MaxMrpInstances = @"MaxMRP_Instances";

        public const string s_MinRtc3Gap = @"MinRTC3_Gap";
        public const string s_MaxFrameStartTime = @"MaxFrameStartTime";
        public const string s_MinNrtGap = @"MinNRT_Gap";
        public const string s_MinYellowTime = @"MinYellowTime";
        public const string s_YellowSafetyMargin = @"YellowSafetyMargin";
        public const string s_PdevCombinedObjectSupported = @"PDEV_CombinedObjectSupported";
        public const string s_MinRdht = @"MinRDHT";
        public const string s_MaxReductionRatioIsochroneMode = @"MaxReductionRatioIsochroneMode";
        public const string s_CheckDeviceIdAllowed = @"CheckDeviceID_Allowed";

        public const string s_Subordinate = @"Subordinate";
        public const string s_ApStructureIdentifier = @"APStructureIdentifier";

        #endregion

        //########################################################################################
        #region GSDML V2.32 Attributes

        public const string s_ProfIenergyAseSupported = @"PROFIenergyASE_Supported";
        public const string s_PesaPusesProfIenergyAse = @"PESAP_uses_PROFIenergyASE";
        public const string s_Number = @"Number";
        public const string s_OppositeDirection = @"OppositeDirection";
        public const string s_Format = @"Format";
        public const string s_DataInvalidOnBackupArSupported = @"DataInvalidOnBackupAR_Supported";
        public const string s_S2MaxInputOnBackupDelay = @"S2MaxInputOnBackupDelay";
        public const string s_R2MaxInputOnBackupDelay = @"R2MaxInputOnBackupDelay";
        public const string s_AdjustSupported = @"AdjustSupported";
        public const string s_ExtensionSupported = @"ExtensionSupported";
        public const string s_Extension = @"Extension";
        public const string s_CheckMauTypeDifferenceSupported = @"CheckMAUTypeDifferenceSupported";
        public const string s_Im5Supported = @"IM5_Supported";
        public const string s_UsesStaticArpCacheEntries = @"UsesStaticARP_CacheEntries";
        public const string s_DFPOutboundTruncationSupported = @"DFP_OutboundTruncationSupported";
        public const string s_MaxRetentionTime = @"MaxRetentionTime";
        public const string s_MayIssueProcessAlarm = @"MayIssueProcessAlarm";

        #endregion

        //########################################################################################
        #region GSDML V2.33 Attributes

        public const string s_NumberOfDeviceAccessAr = @"NumberOfDeviceAccessAR";
        public const string s_AdaptsRealIdentification = @"AdaptsRealIdentification";
        public const string s_OwnIPSetsStandardGateway = @"OwnIP_SetsStandardGateway";
        public const string s_DFPRedundantPathLayoutSupported = @"DFP_RedundantPathLayoutSupported";
        public const string s_AdditionalForwardingRulesSupported = @"AdditionalForwardingRulesSupported";
        public const string s_ProfileVersion = @"ProfileVersion";
        public const string s_EntityClass = @"EntityClass";
        public const string s_EntitySubclass = @"EntitySubclass";
        public const string s_DynamicTimeAndEnergyValues = @"DynamicTimeAndEnergyValues";
        public const string s_TimeToPause = @"TimeToPause";
        public const string s_Rtto = @"RTTO";
        public const string s_TimeMinLengthOfStay = @"TimeMinLengthOfStay";
        public const string s_PowerConsumption = @"PowerConsumption";
        public const string s_EnergyConsumptionToPause = @"EnergyConsumptionToPause";
        public const string s_EnergyConsumptionToOperation = @"EnergyConsumptionToOperation";
        public const string s_AccuracyDomain = @"AccuracyDomain";
        public const string s_AccuracyClass = @"AccuracyClass";

        #endregion

        //########################################################################################
        #region GSDML V2.34 Attributes

        public const string s_NumberOfSubmodules = @"NumberOfSubmodules";
        public const string s_ApplicationLengthIncludesIoxS = @"ApplicationLengthIncludesIOxS";

        #endregion

        //########################################################################################
        #region GSDML V2.35 Attributes

        public const string s_NumberOfImplicitAr = @"NumberOfImplicitAR";
        public const string s_Reason = @"Reason";
        public const string s_SupportedMrpInterconnRole = @"SupportedMRP_InterconnRole";
        public const string s_MaxMrpInterconnInstances = @"MaxMRP_InterconnInstances";
        public const string s_SupportsMrpInterconnPortConfig = @"SupportsMRP_InterconnPortConfig";

        #endregion

        //########################################################################################
        #region GSDML V2.4 Attributes

        public const string s_DeviceTypes = @"DeviceTypes";
        public const string s_Role = @"Role";
        public const string s_SupportedFeatures = @"SupportedFeatures";
        public const string s_SfpDiagnosisSupported = @"SFPDiagnosisSupported";
        public const string s_SfpDiagnosisMonitoring = @"SFPDiagnosisMonitoring";
        public const string s_SupportedServiceProtocols = @"SupportedServiceProtocols";
        public const string s_ProfIsafePirSupported = @"PROFIsafePIR_Supported";

        #endregion

        //########################################################################################
        #region GSDML V2.41 Attributes

        public const string s_SecurityClass = @"SecurityClass";
        public const string s_PaProfileVersion = @"PAProfileVersion";
        public const string s_PaProfileDeviceId = @"PAProfileDeviceID";
        public const string s_PaProfileDeviceDapId = @"PAProfileDeviceDAP_ID";

        #region Signature

        public const string s_Algorithm = @"Algorithm";

        #endregion

        #endregion

        //########################################################################################
        #region GSDML V2.42 Attributes

        public const string s_MaxTransferTimeTx = @"MaxTransferTimeTX";
        public const string s_MaxTransferTimeRx = @"MaxTransferTimeRX";
        public const string s_PaDeviceClass = @"PADeviceClass";
        public const string s_MaxApplicationARs = @"MaxApplicationARs";
        public const string s_SupportedDelayMeasurements = @"SupportedDelayMeasurements";

        #endregion
        //########################################################################################
        #region GSDML V2.43 Attributes

        public const string s_DcpFeaturesSupported = @"DCP_FeaturesSupported";
        public const string s_SnmpFeaturesSupported = @"SNMP_FeaturesSupported";
        public const string s_AplFeaturesSupported = @"APL_FeaturesSupported";

        #region Communication Interface Modules

        public const string s_CimTarget = @"CIM_Target";
        public const string s_Instance = @"Instance";
        public const string s_SupportedRecords = @"SupportedRecords";

        #region Protection

        public const string s_Supported = @"Supported";
        public const string s_Algorithms = @"Algorithms";
        public const string s_AuthnOnly = @"AuthnOnly";
        public const string s_AuthnEnc = @"AuthnEnc";

        #endregion

        #region APLPortClassification

        public const string s_SegmentClass = @"SegmentClass";
        public const string s_PortClass = @"PortClass";
        public const string s_PowerClass = @"PowerClass";
        public const string s_ProtectionClass = @"IS_ProtectionClass";

        #endregion

        #region ParameterRecordData

        public const string s_RecordDataTarget = @"RecordDataTarget";

        #endregion

        #region CertificationInfo

        public const string s_LinkSpeed = @"LinkSpeed";

        #endregion

        #endregion

        #endregion
        #region GSDML V2.44 Attributes

        public const string s_Bridge_FeaturesSupported = @"Bridge_FeaturesSupported";
        public const string s_TSN_ConfigurationsSupported = @"TSN_ConfigurationsSupported";
        public const string s_Access = @"Access";

        #endregion

        #region GSDML V2.45 Attributes

        public const string s_ProfIsafeFscpTestModeSupported = @"PROFIsafeFSCP_TestMode_Supported";
        public const string s_DataTarget = @"DataTarget";

        #endregion
        #region Default Value Settings
        // Default values for some GSDML values

        #region V1.0

        /// <summary>default value for ExtendedAddressAssignmentSupported attribute</summary>
        public const bool s_DefaultExtendedAddressAssignmentSupported = false;
        /// <summary>default value for IOPS_Length attribute</summary>
        public const uint s_DefaultIopsLength = 1;
        /// <summary>default value for IOCS_Length attribute</summary>
        public const uint s_DefaultIocsLength = 1;
        /// <summary>default value for Consistency attribute</summary>
        public const GSDI.IOConsistencies s_DefaultConsistency = GSDI.IOConsistencies.GSDType;
        /// <summary>default value for UseAsBits attribute</summary>
        public const bool s_DefaultUseAsBits = false;
        /// <summary>default value for TransferSequence attribute</summary>
        public const uint s_DefaultTransferSequence = 0;
        /// <summary>default value for ByteOffset attribute</summary>
        public const uint s_DefaultByteOffset = 0;
        /// <summary>default value for BitOffset attribute</summary>
        public const uint s_DefaultBitOffset = 0;
        /// <summary>default value for BitLength attribute</summary>
        public const uint s_DefaultBitLength = 1;
        /// <summary>default value for Changeable attribute</summary>
        public const bool s_DefaultChangeable = true;
        /// <summary>default value for Visible attribute</summary>
        public const bool s_DefaultVisible = true;

        public static readonly uint[] DefaultSendClock = { 32 };

        /// <summary>list of default values for ReductionRatio attribute</summary>
        public static readonly uint[] DefaultReductionRatio = { 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 };

        #endregion

        #region V2.0

        /// <summary>default value for SynchronisationModeSupported</summary>
        public const bool s_DefaultSynchronisationModeSupported = false;
        /// <summary>default value for IsochroneModeSupported</summary>
        public const bool s_DefaultIsochroneModeSupported = false;
        /// <summary>default value for IsochroneModeRequired attribute</summary>
        public const bool s_DefaultIsochroneModeRequired = false;
        /// <summary>default value for PROFIsafeSupported attribute</summary>
        public const bool s_DefaultProfIsafeSupported = false;
        /// <summary>default value for API attribute</summary>
        public const uint s_DefaultApi = 0;
        /// <summary>default value for AR_BlockVersion attribute</summary>
        public const uint s_DefaultArBlockVersion = 1;
        /// <summary>default value for IOCR_BlockVersion attribute</summary>
        public const uint s_DefaultIocrBlockVersion = 1;
        /// <summary>default value for AlarmCR_BlockVersion attribute</summary>
        public const uint s_DefaultAlarmCrBlockVersion = 1;
        /// <summary>default value for SubmoduleDataBlockVersion attribute</summary>
        public const uint s_DefaultSubmoduleDataBlockVersion = 1;
        /// <summary>default value for NumberOfAdditionalInputCR attribute</summary>
        public const uint s_DefaultNumberOfAdditionalInputCr = 0;
        /// <summary>default value for NumberOfAdditionalOutputCR attribute</summary>
        public const uint s_DefaultNumberOfAdditionalOutputCr = 0;
        /// <summary>default value for NumberOfAdditionalMulticastProviderCR attribute</summary>
        public const uint s_DefaultNumberOfAdditionalMulticastProviderCr = 0;
        /// <summary>default value for NumberOfMulticastConsumerCR attribute</summary>
        public const uint s_DefaultNumberOfMulticastConsumerCr = 0;
        /// <summary>default value for Consistency attribute</summary>
        public const GSDI.RTClasses s_DefaultSupportedRTClass = GSDI.RTClasses.GSDClass1;
        /// <summary>default value for MauType attribute</summary>
        public const GSDI.MauTypes s_DefaultMauType = GSDI.MauTypes.GSDMau100Basetxfd;
        /// <summary>default value for MaxPortRxDelay attribute</summary>
        public const uint s_DefaultMaxPortRxDelay = 0;
        /// <summary>default value for MaxPortTxDelay attribute</summary>
        public const uint s_DefaultMaxPortTxDelay = 0;
        /// <summary>default value for MultipleWriteSupported attribute</summary>
        public const bool s_DefaultMultipleWriteSupported = false;
        /// <summary>default value for IOXS_Required attribute</summary>
        public const bool s_DefaultIOXSRequired = true;
        /// <summary>default value for F_IO_StructureDescCRC attribute</summary>
        public const uint s_DefaultFioStructureDescCrc = 0;
        /// <summary>default value for SupportedRole attribute</summary>
        public const GSDI.SynchronisationRoles s_DefaultSupportedSynchronisationRole = GSDI.SynchronisationRoles.GSDSyncSlave;
        /// <summary>default value for T_PLL_Max attribute</summary>
        public const uint s_DefaultTPllMax = 1000;

        /// <summary>fixed value for SubslotNumber attribute of InterfaceSubmodule (0x8000 hexadecimal)</summary>
        public const uint s_FixedSubslotNumberOfInterfaceSubmodule = 32768;
        // <summary>fixed value for SubslotNumber attribute of InterfaceSubmodule (0x8000 hexadecimal) as string</summary>
        //public const string FixedSubslotNumberOfInterfaceSubmoduleAsString = @"0x8000";
        /// <summary>fixed value for Length attribute of F_ParameterRecordData (10 byte)</summary>
        public const uint s_FixedFParameterRecordDataLength = 10;

        /// <summary>list of default values for SendClock attribute</summary>
        public static readonly uint[] DefaultRTClass3SendClock  = { 32 };
        /// <summary>list of default values for ReductionRatio attribute</summary>
        public static readonly uint[] DefaultRTClass3ReductionRatio  = { 1, 2, 4, 8, 16 };

        #endregion

        #region V2.1

        /// <summary>default value for PowerBudgetControlSupported</summary>
        public const bool s_DefaultPowerBudgetControlSupported = false;
        /// <summary>default value for NetworkComponentDiagnosisSupported</summary>
        public const bool s_DefaultNetworkComponentDiagnosisSupported = false;
        /// <summary>default value for LinkStateDiagnosisCapability attribute</summary>
        public const GSDI.LinkStateDiagnosisCapabilities s_DefaultLinkStateDiagnosisCapability = GSDI.LinkStateDiagnosisCapabilities.GSDLsdcNone;
        /// <summary>default value for SupportedRT_Classes attribute</summary>
        public const string s_DefaultSupportedRtClasses = @"RT_CLASS_1";
        /// <summary>value  RT_CLASS_1 for SupportedRT_Classes attribute</summary>
        public const string s_RtClass1 = @"RT_CLASS_1";
        /// <summary>value  RT_CLASS_2 for SupportedRT_Classes attribute</summary>
        public const string s_RtClass2 = @"RT_CLASS_2";
        /// <summary>value  RT_CLASS_3 for SupportedRT_Classes attribute</summary>
        public const string s_RtClass3 = @"RT_CLASS_3";
        /// <summary>fixed value for Length attribute of F_ParameterRecordData with F_iPar_CRC (14 byte)</summary>
        public const uint s_ExtFixedFParameterRecordDataLength = 14;
        /// <summary>default value for PortDeactivationSupported</summary>
        public const bool s_DefaultPortDeactivationSupported = false;
        /// <summary>value  Default value for AddressAssignemnt attribute</summary>
        public const GSDI.AddressAssignment s_DefaultAddressAssignment = GSDI.AddressAssignment.GSDAddressassignmentDcp;

        /// <summary>default value for RT_MediaRedundancySupported</summary>
        public const bool s_DefaultRTMediaRedundancySupported = true;

        /// <summary>default value for IsDefaultRingport attribute</summary>
		public const bool s_DefaultIsDefaultRingport = false;

        /// <summary>default value for SupportsRingportConfi attribute</summary>
		public const bool s_DefaultSupportsRingportConfig = false;

        /// <summary>default value for RemoteApplicationTimeout</summary>
        public const uint s_DefaultRemoteApplicationTimeout = 300;

        #endregion

        #region V2.2
        public const bool s_DefaultDcpBoundarySupported = false;
        public const bool s_DefaultPtpBoundarySupported = false;
        public const bool s_DefaultDcpHelloSupported = false;

        public const bool s_DefaultParameterizationSpeedupSupported = false;
        public const bool s_DefaultNameOfStationNotTransferable = false;

        /// <summary>default value for F_IO_StructureDescVersion</summary>
        public const uint s_DefaultFioStructureDescVersion = 1;

        /// <summary>default value for MaxSupportedRecordSiz</summary>
        public const uint s_DefaultMaxSupportedRecordSize = 4068;

        /// <summary>default value for PowerOnToCommReady</summary>
        public const uint s_DefaultPowerOnToCommReady = 0;

        /// <summary>default value for PullModuleAlarmSupported</summary>
        public const bool s_DefaultPullModuleAlarmSupported = false;

        #endregion

        #region V2.25
        public const bool s_DefaultParameterizationDisallowed = false;
        public const bool s_DefaultSharedDeviceSupported = false;
        public const bool s_DefaultSharedInputSupported = false;

        public const uint s_DefaultNumberOfAr = 1;

        public const bool s_DefaultDeviceAccessSupported = false;
        public const bool s_DefaultCheckMauTypeSupported = false;
        public const bool s_DefaultMulticastBoundarySupported = false;
        public const bool s_DefaultDelayMeasurementSupported = false;
        public const bool s_DefaultAdditionalProtocolsSupported = false;
        public const bool s_DefaultPROFIenergySupported = false;

        #endregion

        #region V2.3

        public const uint s_DefaultMaxBridgeDelayFfw = 0;
        public const uint s_DefaultMaxDfpFeed = 0;
        public const bool s_DefaultAlignDfpSubframes = false;
        public const uint s_DefaultMaxDfpFrames = 0;
        public const string s_DefaultForwardingMode = "";
        public const string s_DefaultFragmentationType = "";
        public const bool s_DefaultMrtSupported = false;
        public const bool s_DefaultMrpdSupported = false;
        public const uint s_DefaultMaxRangeIrFrameId = 1024;
        public const uint s_DefaultMaxRedPeriodLength = 3875;
        public const string s_DefaultStartupMode = "Legacy";
        public const uint s_DefaultMinFso = 5000;
        public const uint s_DefaultPeerToPeerJitter = 0;
        public const int s_DefaultNumberOfSrArSets = 1;
        public const bool s_DefaultPrimaryArOnBothNapsSupported = false;
        public const bool s_DefaultChangeableWithBump = false;
        public const bool s_DefaultShortPreamble100MbitSupported = false;
        public const bool s_DefaultCirSupported = false;
        public const bool s_DefaultLldpnoDSupported = false;
        public const bool s_DefaultPrmBeginPrmEndSequenceSupported = false;
        public const bool s_DefaultAutoConfigurationSupported = false;
        public const uint s_DefaultMaxApplicationInputLength = 0;
        public const uint s_DefaultMaxApplicationOutputLength = 0;
        public const uint s_DefaultMaxApplicationDataLength = 0;
        public const uint s_DefaultNumberOfArSets = 1;
        public const bool s_DefaultIOSupervisorSupported = false;

        #endregion

        #region V2.31

        public const GSDI.MediaRedundancyRoles DefaultMRSupportedRole = GSDI.MediaRedundancyRoles.GSDClient;
        public const uint s_DefaultMaxMrpInstances = 0;

        public const uint s_DefaultMinRtc3Gap = 1120;
        public const uint s_DefaultMaxFrameStartTime = 1600;
        public const uint s_DefaultMinNrtGap = 960;
        public const uint s_DefaultMinYellowTime = 9600;
        public const uint s_DefaultYellowSafetyMargin = 160;
        public const bool s_DefaultPdevCombinedObjectSupported = false;
        public const uint s_DefaultMaxReductionRatioIsochroneMode = 1;
        public const bool s_DefaultCheckDeviceIdAllowed = false;
        public const bool s_DefaultSubordinate = false;

        #endregion

        #region V2.32

        public const bool s_DefaultProfIenergyAseSupported = false;
        public const bool s_DefaultPesaPusesProfIenergyAse = false;
        public const bool s_DefaultDataInvalidOnBackupArSupported = false;
        public const bool s_DefaultExtensionSupported = false;
        public const bool s_DefaultAdjustSupported = false;
        public const bool s_DefaultOppositeDirection = false;
        public const bool s_DefaultIm5Supported = false;
        public const bool s_DefaultUsesStaticArpCacheEntries = false;
        public const bool s_DefaultDFPOutboundTruncationSupported = false;

        #endregion

        #region V2.33

        public const bool s_DefaultAdaptsRealIdentification = false;
        public const bool s_DefaultOwnIPSetsStandardGateway = false;
        public const bool s_DefaultDFPRedundantPathLayoutSupported = false;
        public const bool s_DefaultAdditionalForwardingRulesSupported = false;
        public const bool s_DefaultDynamicTimeAndEnergyValues = false;

        #endregion

        #region V2.34

        public const bool s_DefaultApplicationLengthIncludesIoxS = false;

        #endregion

        #region V2.35

        public const bool s_DefaultSupportsMrpInterconnPortConfig = false;

        #endregion

        #region V2.4

        public const bool s_DefaultProfIsafePirSupported = false;

        #endregion

        #region V2.43

        /// <summary>fixed value for Length attribute of F_ParameterRecordData with F_WD_TIME_2 but no F_iPar_CRC (12 byte)</summary>
        public const uint s_Ext1FixedFParameterRecordDataLength = 12;
        /// <summary>fixed value for Length attribute of F_ParameterRecordData with F_iPar_CRC and F_WD_TIME_2 (16 byte)</summary>
        public const uint s_Ext2FixedFParameterRecordDataLength = 16;

        #endregion
        #region V2.44

        /// <summary>fixed value for Length attribute of F_ParameterRecordData (10 byte)</summary>
        public const uint s_FixedFBaseIDRecordDataLength = 12;

        #endregion
        #region V2.45

        public const bool s_DefaultProfIsafeFscpTestModeSupported = false;

        #endregion
        #endregion
    }
}
