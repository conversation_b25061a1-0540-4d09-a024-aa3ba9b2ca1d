/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: MeasurementValue.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The MeasurementValue element contains information about a single MeasurementValue.
    /// </summary>
    public class MeasurementValue :
        GsdObject,
        GSDI.IMeasurementValue

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the MeasurementValue if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public MeasurementValue()
        {
            m_ID = 0;
            m_AccuracyDomain = 0;
            m_AccuracyClass = 0;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private UInt32 m_ID;
        private UInt32 m_AccuracyDomain;
        private UInt32 m_AccuracyClass;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the ID property.
        /// </summary>
        public UInt32 ID => this.m_ID;

        /// <summary>
        /// Accesses the AccuracyDomain property.
        /// </summary>
        public UInt32 AccuracyDomain => this.m_AccuracyDomain;

        /// <summary>
        /// Accesses the AccuracyClass property.
        /// </summary>
        public UInt32 AccuracyClass => this.m_AccuracyClass;
        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.FieldID;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_ID = (UInt32)hash[member];

                member = Models.s_FieldAccuracyDomain;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_AccuracyDomain = (UInt32)hash[member];

                member = Models.s_FieldAccuracyClass;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_AccuracyClass = (UInt32)hash[member];
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectMeasurementValue);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.FieldID, this.m_ID);
            Export.WriteUint32Property(ref writer, Models.s_FieldAccuracyDomain, this.m_AccuracyDomain);
            Export.WriteUint32Property(ref writer, Models.s_FieldAccuracyClass, this.m_AccuracyClass);

            return true;
        }


        #endregion
    }
}
