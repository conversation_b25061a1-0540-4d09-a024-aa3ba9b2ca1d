/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigBlock.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;

#endregion

namespace PNConfigLib.HWCNBL.ConfigParser
{
    internal class ConfigBlock
    {
        private int m_Length;
        
        public ConfigBlock(byte[] data, int offset, int length)
        {
            if (data == null)
            {
                throw new ArgumentNullException(nameof(data));
            }

            Debug.Assert(offset < data.Length);
            Debug.Assert(offset + length <= data.Length);

            Data = data;
            BlockOffset = offset;
            m_Length = length;
        }

        public ConfigBlock()
        {
        }

        public int BlockLength
        {
            get { return m_Length; }
            set { m_Length = value; }
        }
        
        public int BlockOffset { get; set; }

        public byte[] Data { get; set; }

        protected ushort Read16(int offset)
        {
            return Transformator.Read16(Data, BlockOffset + offset);
        }

        protected uint Read32(int offset)
        {
            return Transformator.Read32(Data, BlockOffset + offset);
        }

        protected byte Read8(int offset)
        {
            return Transformator.Read8(Data, BlockOffset + offset);
        }

        protected int ReadBitfieldBased16(int ByteOffset, int BitOffset, int BitLength)
        {
            return Transformator.ReadBitfieldBased16(Data, BlockOffset + ByteOffset, BitOffset, BitLength);
        }

        protected uint ReadBitfieldBased32(int ByteOffset, int BitOffset, int BitLength)
        {
            return Transformator.ReadBitfieldBased32(Data, BlockOffset + ByteOffset, BitOffset, BitLength);
        }

        protected int ReadBitfieldBased8(int ByteOffset, int BitOffset, int BitLength)
        {
            return Transformator.ReadBitfieldBased8(Data, BlockOffset + ByteOffset, BitOffset, BitLength);
        }

        protected byte[] ReadByteblock(int offset, int length)
        {
            byte[] result = new byte[length];
            Array.Copy(Data, BlockOffset + offset, result, 0, length);
            return result;
        }
    }
}