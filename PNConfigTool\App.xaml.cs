using PNConfigTool.Services;
using PNConfigTool.Models;
using PNConfigTool.Views.Windows;
using System;
using System.Windows;
using System.Threading.Tasks;

namespace PNConfigTool
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 设置全局异常处理
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            // 初始化全局地址管理器
            try
            {
                var globalAddressManager = GlobalAddressManager.Instance;
                var projectManager = ProjectManager.Instance;
                globalAddressManager.Initialize(projectManager);
                System.Diagnostics.Debug.WriteLine("GlobalAddressManager已初始化");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化GlobalAddressManager时出错: {ex.Message}");
            }

            // 创建并显示MainWindow
            MainWindow = new MainWindow();
            //MainWindow.WindowState = WindowState.Maximized; // 设置窗口最大化
            MainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // 清理GlobalAddressManager资源
            try
            {
                var globalAddressManager = GlobalAddressManager.Instance;
                globalAddressManager.ClearAllAddresses();
                System.Diagnostics.Debug.WriteLine("GlobalAddressManager地址已清理");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理GlobalAddressManager资源时出错: {ex.Message}");
            }

            // 清理GSDMLService资源
            try
            {
                var gsdmlService = ServiceLocator.GetService<IGSDMLService>();
                if (gsdmlService is IDisposable disposable)
                {
                    disposable.Dispose();
                    System.Diagnostics.Debug.WriteLine("GSDMLService资源已清理");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理GSDMLService资源时出错: {ex.Message}");
            }

            base.OnExit(e);
        }
        
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception? ex = e.ExceptionObject as Exception;
            HandleException(ex, "未处理的应用程序异常");
        }

        private void TaskScheduler_UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            HandleException(e.Exception, "未观察到的任务异常");
            e.SetObserved(); // 标记为已观察，防止程序崩溃
        }
        
        private void HandleException(Exception? ex, string title)
        {
            if (ex != null)
            {
                string errorMessage = $"发生错误: {ex.Message}\n\n调用堆栈: {ex.StackTrace}";
                MessageBox.Show(errorMessage, title, MessageBoxButton.OK, MessageBoxImage.Error);
                
                // 记录异常
                System.Diagnostics.Debug.WriteLine($"{title}: {ex}");
            }
        }
    }
}