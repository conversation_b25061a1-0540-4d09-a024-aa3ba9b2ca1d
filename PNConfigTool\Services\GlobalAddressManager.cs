using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using PNConfigTool.Models;

namespace PNConfigTool.Services
{
    /// <summary>
    /// 全局地址管理器 - 管理整个项目中所有设备的模块地址分配
    /// 确保不同设备间的模块地址不会冲突，实现统一的地址空间管理
    /// </summary>
    public class GlobalAddressManager
    {
        #region 常量定义
        
        /// <summary>
        /// 输入地址池大小 (0-8191)
        /// </summary>
        public const int INPUT_ADDRESS_POOL_SIZE = 8192;
        
        /// <summary>
        /// 输出地址池大小 (0-8191)
        /// </summary>
        public const int OUTPUT_ADDRESS_POOL_SIZE = 8192;
        
        #endregion

        #region 私有字段

        private static GlobalAddressManager _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// 已分配的输入地址段列表 (起始地址, 结束地址, 设备ID, 模块ID)
        /// </summary>
        private readonly List<AddressSegment> _allocatedInputSegments;
        
        /// <summary>
        /// 已分配的输出地址段列表 (起始地址, 结束地址, 设备ID, 模块ID)
        /// </summary>
        private readonly List<AddressSegment> _allocatedOutputSegments;
        
        /// <summary>
        /// 项目管理器引用，用于持久化地址分配状态
        /// </summary>
        private ProjectManager _projectManager;

        #endregion

        #region 构造函数

        private GlobalAddressManager()
        {
            _allocatedInputSegments = new List<AddressSegment>();
            _allocatedOutputSegments = new List<AddressSegment>();
            Debug.WriteLine("GlobalAddressManager 实例已创建");
        }

        #endregion

        #region 单例模式

        /// <summary>
        /// 获取全局地址管理器实例
        /// </summary>
        public static GlobalAddressManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new GlobalAddressManager();
                        }
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化地址管理器，设置项目管理器引用
        /// </summary>
        /// <param name="projectManager">项目管理器实例</param>
        public void Initialize(ProjectManager projectManager)
        {
            _projectManager = projectManager;
            Debug.WriteLine("GlobalAddressManager 已初始化");
        }

        /// <summary>
        /// 分配输入地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="length">需要的地址长度</param>
        /// <param name="forceNew">是否强制分配新地址，忽略现有分配</param>
        /// <returns>分配的起始地址，如果分配失败返回-1</returns>
        public int AllocateInputAddress(string deviceId, string moduleId, int length, bool forceNew = false)
        {
            if (length <= 0)
            {
                Debug.WriteLine($"输入地址分配失败：长度无效 ({length})");
                return -1;
            }

            lock (_lock)
            {
                // 如果不强制分配新地址，检查是否已经为该模块分配了地址
                if (!forceNew)
                {
                    var existingSegment = _allocatedInputSegments.FirstOrDefault(s =>
                        s.DeviceId == deviceId && s.ModuleId == moduleId);

                    if (existingSegment != null)
                    {
                        Debug.WriteLine($"模块 {deviceId}.{moduleId} 已有输入地址分配: {existingSegment.StartAddress}");
                        return existingSegment.StartAddress;
                    }
                }
                else
                {
                    // 强制分配新地址时，先释放现有分配
                    ReleaseModuleAddresses(deviceId, moduleId);
                }

                // 查找可用的连续地址空间
                int startAddress = FindAvailableInputAddress(length);

                if (startAddress == -1)
                {
                    Debug.WriteLine($"输入地址分配失败：无足够连续空间 (需要 {length} 字节)");
                    return -1;
                }

                // 分配地址段
                var segment = new AddressSegment
                {
                    StartAddress = startAddress,
                    EndAddress = startAddress + length - 1,
                    Length = length,
                    DeviceId = deviceId,
                    ModuleId = moduleId,
                    AddressType = AddressType.Input
                };

                _allocatedInputSegments.Add(segment);
                _allocatedInputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                Debug.WriteLine($"输入地址分配成功：设备 {deviceId}, 模块 {moduleId}, 地址 {startAddress}-{startAddress + length - 1} (长度: {length})");

                return startAddress;
            }
        }

        /// <summary>
        /// 分配输出地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="length">需要的地址长度</param>
        /// <param name="forceNew">是否强制分配新地址，忽略现有分配</param>
        /// <returns>分配的起始地址，如果分配失败返回-1</returns>
        public int AllocateOutputAddress(string deviceId, string moduleId, int length, bool forceNew = false)
        {
            if (length <= 0)
            {
                Debug.WriteLine($"输出地址分配失败：长度无效 ({length})");
                return -1;
            }

            lock (_lock)
            {
                // 如果不强制分配新地址，检查是否已经为该模块分配了地址
                if (!forceNew)
                {
                    var existingSegment = _allocatedOutputSegments.FirstOrDefault(s =>
                        s.DeviceId == deviceId && s.ModuleId == moduleId);

                    if (existingSegment != null)
                    {
                        Debug.WriteLine($"模块 {deviceId}.{moduleId} 已有输出地址分配: {existingSegment.StartAddress}");
                        return existingSegment.StartAddress;
                    }
                }
                else
                {
                    // 强制分配新地址时，先释放现有分配
                    ReleaseModuleAddresses(deviceId, moduleId);
                }

                // 查找可用的连续地址空间
                int startAddress = FindAvailableOutputAddress(length);

                if (startAddress == -1)
                {
                    Debug.WriteLine($"输出地址分配失败：无足够连续空间 (需要 {length} 字节)");
                    return -1;
                }

                // 分配地址段
                var segment = new AddressSegment
                {
                    StartAddress = startAddress,
                    EndAddress = startAddress + length - 1,
                    Length = length,
                    DeviceId = deviceId,
                    ModuleId = moduleId,
                    AddressType = AddressType.Output
                };

                _allocatedOutputSegments.Add(segment);
                _allocatedOutputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                Debug.WriteLine($"输出地址分配成功：设备 {deviceId}, 模块 {moduleId}, 地址 {startAddress}-{startAddress + length - 1} (长度: {length})");

                return startAddress;
            }
        }

        /// <summary>
        /// 释放模块的地址分配
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        public void ReleaseModuleAddresses(string deviceId, string moduleId)
        {
            lock (_lock)
            {
                // 释放输入地址
                var inputSegment = _allocatedInputSegments.FirstOrDefault(s => 
                    s.DeviceId == deviceId && s.ModuleId == moduleId);
                if (inputSegment != null)
                {
                    _allocatedInputSegments.Remove(inputSegment);
                    Debug.WriteLine($"释放输入地址：设备 {deviceId}, 模块 {moduleId}, 地址 {inputSegment.StartAddress}-{inputSegment.EndAddress}");
                }

                // 释放输出地址
                var outputSegment = _allocatedOutputSegments.FirstOrDefault(s => 
                    s.DeviceId == deviceId && s.ModuleId == moduleId);
                if (outputSegment != null)
                {
                    _allocatedOutputSegments.Remove(outputSegment);
                    Debug.WriteLine($"释放输出地址：设备 {deviceId}, 模块 {moduleId}, 地址 {outputSegment.StartAddress}-{outputSegment.EndAddress}");
                }
            }
        }

        /// <summary>
        /// 释放设备的所有地址分配
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        public void ReleaseDeviceAddresses(string deviceId)
        {
            lock (_lock)
            {
                // 释放设备的所有输入地址
                var inputSegments = _allocatedInputSegments.Where(s => s.DeviceId == deviceId).ToList();
                foreach (var segment in inputSegments)
                {
                    _allocatedInputSegments.Remove(segment);
                    Debug.WriteLine($"释放设备输入地址：设备 {deviceId}, 模块 {segment.ModuleId}, 地址 {segment.StartAddress}-{segment.EndAddress}");
                }

                // 释放设备的所有输出地址
                var outputSegments = _allocatedOutputSegments.Where(s => s.DeviceId == deviceId).ToList();
                foreach (var segment in outputSegments)
                {
                    _allocatedOutputSegments.Remove(segment);
                    Debug.WriteLine($"释放设备输出地址：设备 {deviceId}, 模块 {segment.ModuleId}, 地址 {segment.StartAddress}-{segment.EndAddress}");
                }
            }
        }

        /// <summary>
        /// 清空所有地址分配
        /// </summary>
        public void ClearAllAddresses()
        {
            lock (_lock)
            {
                _allocatedInputSegments.Clear();
                _allocatedOutputSegments.Clear();
                Debug.WriteLine("已清空所有地址分配");
            }
        }

        /// <summary>
        /// 直接分配指定的输入地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="startAddress">指定的起始地址</param>
        /// <param name="length">地址长度</param>
        /// <returns>分配是否成功</returns>
        public bool AllocateSpecificInputAddress(string deviceId, string moduleId, int startAddress, int length)
        {
            if (length <= 0)
            {
                Debug.WriteLine($"输入地址分配失败：长度无效 ({length})");
                return false;
            }

            lock (_lock)
            {
                // 检查地址范围是否有效
                if (startAddress < 0 || startAddress >= INPUT_ADDRESS_POOL_SIZE)
                {
                    Debug.WriteLine($"输入地址分配失败：起始地址超出边界 ({startAddress})，有效范围: 0-{INPUT_ADDRESS_POOL_SIZE - 1}");
                    return false;
                }

                // 检查模块占用的完整地址范围
                int endAddress = startAddress + length - 1;
                if (endAddress >= INPUT_ADDRESS_POOL_SIZE)
                {
                    Debug.WriteLine($"输入地址分配失败：模块地址范围超出边界 ({startAddress}-{endAddress})，有效范围: 0-{INPUT_ADDRESS_POOL_SIZE - 1}，建议起始地址不超过: {INPUT_ADDRESS_POOL_SIZE - length}");
                    return false;
                }

                // 检查是否与现有分配冲突（排除当前模块）
                if (HasInputAddressConflict(startAddress, length, deviceId, moduleId))
                {
                    Debug.WriteLine($"输入地址分配失败：地址冲突 ({startAddress}-{startAddress + length - 1})");
                    return false;
                }

                // 先释放该模块的现有分配
                ReleaseModuleAddresses(deviceId, moduleId);

                // 分配新的地址段
                var segment = new AddressSegment
                {
                    StartAddress = startAddress,
                    EndAddress = startAddress + length - 1,
                    Length = length,
                    DeviceId = deviceId,
                    ModuleId = moduleId,
                    AddressType = AddressType.Input
                };

                _allocatedInputSegments.Add(segment);
                _allocatedInputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                Debug.WriteLine($"直接分配输入地址成功：设备 {deviceId}, 模块 {moduleId}, 地址 {startAddress}-{startAddress + length - 1}");
                return true;
            }
        }

        /// <summary>
        /// 直接分配指定的输出地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <param name="startAddress">指定的起始地址</param>
        /// <param name="length">地址长度</param>
        /// <returns>分配是否成功</returns>
        public bool AllocateSpecificOutputAddress(string deviceId, string moduleId, int startAddress, int length)
        {
            if (length <= 0)
            {
                Debug.WriteLine($"输出地址分配失败：长度无效 ({length})");
                return false;
            }

            lock (_lock)
            {
                // 检查地址范围是否有效
                if (startAddress < 0 || startAddress >= OUTPUT_ADDRESS_POOL_SIZE)
                {
                    Debug.WriteLine($"输出地址分配失败：起始地址超出边界 ({startAddress})，有效范围: 0-{OUTPUT_ADDRESS_POOL_SIZE - 1}");
                    return false;
                }

                // 检查模块占用的完整地址范围
                int endAddress = startAddress + length - 1;
                if (endAddress >= OUTPUT_ADDRESS_POOL_SIZE)
                {
                    Debug.WriteLine($"输出地址分配失败：模块地址范围超出边界 ({startAddress}-{endAddress})，有效范围: 0-{OUTPUT_ADDRESS_POOL_SIZE - 1}，建议起始地址不超过: {OUTPUT_ADDRESS_POOL_SIZE - length}");
                    return false;
                }

                // 检查是否与现有分配冲突（排除当前模块）
                if (HasOutputAddressConflict(startAddress, length, deviceId, moduleId))
                {
                    Debug.WriteLine($"输出地址分配失败：地址冲突 ({startAddress}-{startAddress + length - 1})");
                    return false;
                }

                // 先释放该模块的现有分配
                ReleaseModuleAddresses(deviceId, moduleId);

                // 分配新的地址段
                var segment = new AddressSegment
                {
                    StartAddress = startAddress,
                    EndAddress = startAddress + length - 1,
                    Length = length,
                    DeviceId = deviceId,
                    ModuleId = moduleId,
                    AddressType = AddressType.Output
                };

                _allocatedOutputSegments.Add(segment);
                _allocatedOutputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                Debug.WriteLine($"直接分配输出地址成功：设备 {deviceId}, 模块 {moduleId}, 地址 {startAddress}-{startAddress + length - 1}");
                return true;
            }
        }

        /// <summary>
        /// 获取模块的输入地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <returns>输入地址，如果未分配返回-1</returns>
        public int GetModuleInputAddress(string deviceId, string moduleId)
        {
            lock (_lock)
            {
                var segment = _allocatedInputSegments.FirstOrDefault(s => 
                    s.DeviceId == deviceId && s.ModuleId == moduleId);
                return segment?.StartAddress ?? -1;
            }
        }

        /// <summary>
        /// 获取模块的输出地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleId">模块ID</param>
        /// <returns>输出地址，如果未分配返回-1</returns>
        public int GetModuleOutputAddress(string deviceId, string moduleId)
        {
            lock (_lock)
            {
                var segment = _allocatedOutputSegments.FirstOrDefault(s => 
                    s.DeviceId == deviceId && s.ModuleId == moduleId);
                return segment?.StartAddress ?? -1;
            }
        }

        /// <summary>
        /// 获取地址分配统计信息
        /// </summary>
        /// <returns>地址分配统计信息</returns>
        public AddressAllocationStats GetAllocationStats()
        {
            lock (_lock)
            {
                var stats = new AddressAllocationStats();

                // 计算输入地址使用情况
                stats.InputAddressesUsed = _allocatedInputSegments.Sum(s => s.Length);
                stats.InputAddressesAvailable = INPUT_ADDRESS_POOL_SIZE - stats.InputAddressesUsed;
                stats.InputUtilizationPercentage = (double)stats.InputAddressesUsed / INPUT_ADDRESS_POOL_SIZE * 100;

                // 计算输出地址使用情况
                stats.OutputAddressesUsed = _allocatedOutputSegments.Sum(s => s.Length);
                stats.OutputAddressesAvailable = OUTPUT_ADDRESS_POOL_SIZE - stats.OutputAddressesUsed;
                stats.OutputUtilizationPercentage = (double)stats.OutputAddressesUsed / OUTPUT_ADDRESS_POOL_SIZE * 100;

                // 设备和模块统计
                stats.DeviceCount = _allocatedInputSegments.Select(s => s.DeviceId)
                    .Union(_allocatedOutputSegments.Select(s => s.DeviceId))
                    .Distinct().Count();

                stats.ModuleCount = _allocatedInputSegments.Select(s => $"{s.DeviceId}.{s.ModuleId}")
                    .Union(_allocatedOutputSegments.Select(s => $"{s.DeviceId}.{s.ModuleId}"))
                    .Distinct().Count();

                return stats;
            }
        }

        /// <summary>
        /// 验证地址分配的一致性
        /// </summary>
        /// <returns>验证结果</returns>
        public AddressValidationResult ValidateAddressAllocation()
        {
            lock (_lock)
            {
                var result = new AddressValidationResult { IsValid = true };

                // 验证输入地址
                result.InputValidation = ValidateAddressSegments(_allocatedInputSegments, INPUT_ADDRESS_POOL_SIZE, "输入");

                // 验证输出地址
                result.OutputValidation = ValidateAddressSegments(_allocatedOutputSegments, OUTPUT_ADDRESS_POOL_SIZE, "输出");

                result.IsValid = result.InputValidation.IsValid && result.OutputValidation.IsValid;

                // 额外的完整性检查
                if (result.IsValid)
                {
                    result.IsValid = PerformIntegrityCheck();
                }

                return result;
            }
        }

        /// <summary>
        /// 执行地址分配完整性检查
        /// </summary>
        /// <returns>如果通过完整性检查返回true</returns>
        private bool PerformIntegrityCheck()
        {
            bool isValid = true;

            try
            {
                Debug.WriteLine("=== 开始地址分配完整性检查 ===");

                // 检查输入地址重叠
                var inputOverlaps = DetectAddressOverlaps(_allocatedInputSegments, "输入");
                if (inputOverlaps.Any())
                {
                    isValid = false;
                    foreach (var overlap in inputOverlaps)
                    {
                        Debug.WriteLine($"输入地址重叠: {overlap}");
                    }
                }

                // 检查输出地址重叠
                var outputOverlaps = DetectAddressOverlaps(_allocatedOutputSegments, "输出");
                if (outputOverlaps.Any())
                {
                    isValid = false;
                    foreach (var overlap in outputOverlaps)
                    {
                        Debug.WriteLine($"输出地址重叠: {overlap}");
                    }
                }

                // 检查地址边界
                var boundaryViolations = CheckAddressBoundaries();
                if (boundaryViolations.Any())
                {
                    isValid = false;
                    foreach (var violation in boundaryViolations)
                    {
                        Debug.WriteLine($"地址边界违规: {violation}");
                    }
                }

                Debug.WriteLine($"完整性检查结果: {(isValid ? "通过" : "失败")}");
                Debug.WriteLine("=== 地址分配完整性检查完成 ===");

                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"完整性检查时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测地址段重叠
        /// </summary>
        /// <param name="segments">地址段列表</param>
        /// <param name="addressType">地址类型名称</param>
        /// <returns>重叠描述列表</returns>
        private List<string> DetectAddressOverlaps(List<AddressSegment> segments, string addressType)
        {
            var overlaps = new List<string>();
            var sortedSegments = segments.OrderBy(s => s.StartAddress).ToList();

            for (int i = 0; i < sortedSegments.Count - 1; i++)
            {
                var current = sortedSegments[i];
                var next = sortedSegments[i + 1];

                if (current.EndAddress >= next.StartAddress)
                {
                    overlaps.Add($"{addressType}地址重叠: {current.DeviceId}.{current.ModuleId} ({current.StartAddress}-{current.EndAddress}) 与 {next.DeviceId}.{next.ModuleId} ({next.StartAddress}-{next.EndAddress})");
                }
            }

            return overlaps;
        }

        /// <summary>
        /// 检查地址边界违规
        /// </summary>
        /// <returns>边界违规描述列表</returns>
        private List<string> CheckAddressBoundaries()
        {
            var violations = new List<string>();

            // 检查输入地址边界
            foreach (var segment in _allocatedInputSegments)
            {
                if (segment.StartAddress < 0 || segment.EndAddress >= INPUT_ADDRESS_POOL_SIZE)
                {
                    violations.Add($"输入地址超出边界: {segment.DeviceId}.{segment.ModuleId} ({segment.StartAddress}-{segment.EndAddress}), 有效范围: 0-{INPUT_ADDRESS_POOL_SIZE - 1}");
                }
            }

            // 检查输出地址边界
            foreach (var segment in _allocatedOutputSegments)
            {
                if (segment.StartAddress < 0 || segment.EndAddress >= OUTPUT_ADDRESS_POOL_SIZE)
                {
                    violations.Add($"输出地址超出边界: {segment.DeviceId}.{segment.ModuleId} ({segment.StartAddress}-{segment.EndAddress}), 有效范围: 0-{OUTPUT_ADDRESS_POOL_SIZE - 1}");
                }
            }

            return violations;
        }



        /// <summary>
        /// 获取所有已分配的地址段信息
        /// </summary>
        /// <returns>地址段信息列表</returns>
        public List<AddressSegment> GetAllAllocatedSegments()
        {
            lock (_lock)
            {
                var allSegments = new List<AddressSegment>();
                allSegments.AddRange(_allocatedInputSegments);
                allSegments.AddRange(_allocatedOutputSegments);
                return allSegments.OrderBy(s => s.AddressType).ThenBy(s => s.StartAddress).ToList();
            }
        }

        /// <summary>
        /// 调试方法：打印当前所有地址分配状态
        /// </summary>
        public void DebugPrintAllAllocations()
        {
            lock (_lock)
            {
                Debug.WriteLine("=== 当前全局地址分配状态 ===");

                Debug.WriteLine($"输入地址段 ({_allocatedInputSegments.Count} 个):");
                foreach (var segment in _allocatedInputSegments.OrderBy(s => s.StartAddress))
                {
                    Debug.WriteLine($"  {segment.DeviceId}.{segment.ModuleId}: {segment.StartAddress}-{segment.EndAddress} (长度:{segment.Length})");
                }

                Debug.WriteLine($"输出地址段 ({_allocatedOutputSegments.Count} 个):");
                foreach (var segment in _allocatedOutputSegments.OrderBy(s => s.StartAddress))
                {
                    Debug.WriteLine($"  {segment.DeviceId}.{segment.ModuleId}: {segment.StartAddress}-{segment.EndAddress} (长度:{segment.Length})");
                }

                var stats = GetAllocationStats();
                Debug.WriteLine($"统计信息: {stats}");

                // 显示地址空间使用信息
                AnalyzeAddressUsage();

                Debug.WriteLine("=== 地址分配状态结束 ===");
            }
        }

        /// <summary>
        /// 分析地址空间使用情况
        /// </summary>
        private void AnalyzeAddressUsage()
        {
            Debug.WriteLine("--- 地址空间使用分析 ---");

            // 分析输入地址空间
            var inputGaps = GetAddressGaps(_allocatedInputSegments, INPUT_ADDRESS_POOL_SIZE);
            Debug.WriteLine($"输入地址空隙数量: {inputGaps.Count}");
            foreach (var gap in inputGaps)
            {
                Debug.WriteLine($"  空隙: {gap.start}-{gap.end} (大小: {gap.size})");
            }

            // 分析输出地址空间
            var outputGaps = GetAddressGaps(_allocatedOutputSegments, OUTPUT_ADDRESS_POOL_SIZE);
            Debug.WriteLine($"输出地址空隙数量: {outputGaps.Count}");
            foreach (var gap in outputGaps)
            {
                Debug.WriteLine($"  空隙: {gap.start}-{gap.end} (大小: {gap.size})");
            }
        }

        /// <summary>
        /// 获取地址空隙列表
        /// </summary>
        /// <param name="segments">已分配的地址段</param>
        /// <param name="poolSize">地址池大小</param>
        /// <returns>空隙列表</returns>
        private List<(int start, int end, int size)> GetAddressGaps(List<AddressSegment> segments, int poolSize)
        {
            var gaps = new List<(int start, int end, int size)>();
            var sortedSegments = segments.OrderBy(s => s.StartAddress).ToList();

            if (sortedSegments.Count == 0)
            {
                // 整个地址空间都是空隙
                gaps.Add((0, poolSize - 1, poolSize));
                return gaps;
            }

            // 检查开头的空隙
            if (sortedSegments[0].StartAddress > 0)
            {
                int gapSize = sortedSegments[0].StartAddress;
                gaps.Add((0, sortedSegments[0].StartAddress - 1, gapSize));
            }

            // 检查中间的空隙
            for (int i = 0; i < sortedSegments.Count - 1; i++)
            {
                int gapStart = sortedSegments[i].EndAddress + 1;
                int gapEnd = sortedSegments[i + 1].StartAddress - 1;

                if (gapStart <= gapEnd)
                {
                    int gapSize = gapEnd - gapStart + 1;
                    gaps.Add((gapStart, gapEnd, gapSize));
                }
            }

            // 检查结尾的空隙
            int lastEnd = sortedSegments.Last().EndAddress;
            if (lastEnd < poolSize - 1)
            {
                int gapStart = lastEnd + 1;
                int gapEnd = poolSize - 1;
                int gapSize = gapEnd - gapStart + 1;
                gaps.Add((gapStart, gapEnd, gapSize));
            }

            return gaps;
        }

        /// <summary>
        /// 批量重新分配设备的所有模块地址
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="moduleRequests">模块地址需求列表</param>
        /// <returns>分配结果字典，键为模块ID，值为(输入地址, 输出地址)</returns>
        public Dictionary<string, (int inputAddress, int outputAddress)> BatchReallocateDeviceAddresses(
            string deviceId,
            List<(string moduleId, int inputLength, int outputLength)> moduleRequests)
        {
            lock (_lock)
            {
                var results = new Dictionary<string, (int inputAddress, int outputAddress)>();

                try
                {
                    Debug.WriteLine($"=== 开始批量重新分配设备 {deviceId} 的地址 ===");

                    // 1. 释放设备的所有现有地址分配
                    ReleaseDeviceAddresses(deviceId);

                    // 2. 优化分配策略：优先使用小空隙分配小模块
                    var sortedRequests = OptimizeAllocationOrder(moduleRequests);

                    // 3. 按优化后的顺序重新分配地址
                    foreach (var (moduleId, inputLength, outputLength) in sortedRequests)
                    {
                        int inputAddress = -1;
                        int outputAddress = -1;

                        // 分配输入地址
                        if (inputLength > 0)
                        {
                            inputAddress = FindAvailableInputAddress(inputLength);
                            if (inputAddress >= 0)
                            {
                                var inputSegment = new AddressSegment
                                {
                                    StartAddress = inputAddress,
                                    EndAddress = inputAddress + inputLength - 1,
                                    Length = inputLength,
                                    DeviceId = deviceId,
                                    ModuleId = moduleId,
                                    AddressType = AddressType.Input
                                };
                                _allocatedInputSegments.Add(inputSegment);
                                Debug.WriteLine($"  批量分配输入地址: {deviceId}.{moduleId} -> {inputAddress}-{inputAddress + inputLength - 1}");
                            }
                            else
                            {
                                Debug.WriteLine($"  输入地址分配失败: {deviceId}.{moduleId}, 需要长度 {inputLength}");
                            }
                        }

                        // 分配输出地址
                        if (outputLength > 0)
                        {
                            outputAddress = FindAvailableOutputAddress(outputLength);
                            if (outputAddress >= 0)
                            {
                                var outputSegment = new AddressSegment
                                {
                                    StartAddress = outputAddress,
                                    EndAddress = outputAddress + outputLength - 1,
                                    Length = outputLength,
                                    DeviceId = deviceId,
                                    ModuleId = moduleId,
                                    AddressType = AddressType.Output
                                };
                                _allocatedOutputSegments.Add(outputSegment);
                                Debug.WriteLine($"  批量分配输出地址: {deviceId}.{moduleId} -> {outputAddress}-{outputAddress + outputLength - 1}");
                            }
                            else
                            {
                                Debug.WriteLine($"  输出地址分配失败: {deviceId}.{moduleId}, 需要长度 {outputLength}");
                            }
                        }

                        results[moduleId] = (inputAddress, outputAddress);
                    }

                    // 4. 重新排序地址段
                    _allocatedInputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));
                    _allocatedOutputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                    Debug.WriteLine($"=== 设备 {deviceId} 地址批量重新分配完成 ===");

                    return results;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"批量重新分配设备地址时出错: {ex.Message}");
                    return results;
                }
            }
        }

        /// <summary>
        /// 保持原始分配顺序，不进行特殊优化
        /// </summary>
        /// <param name="moduleRequests">原始模块请求列表</param>
        /// <returns>原始分配顺序</returns>
        private List<(string moduleId, int inputLength, int outputLength)> OptimizeAllocationOrder(
            List<(string moduleId, int inputLength, int outputLength)> moduleRequests)
        {
            // 保持原始顺序，不进行特殊优化
            return moduleRequests.ToList();
        }

        /// <summary>
        /// 保存地址分配状态到项目配置
        /// </summary>
        public void SaveAddressAllocationToProject()
        {
            if (_projectManager?.CurrentProject == null)
            {
                Debug.WriteLine("无法保存地址分配状态：项目管理器或当前项目为空");
                return;
            }

            lock (_lock)
            {
                try
                {
                    var allocation = _projectManager.CurrentProject.ProjectSpecificExtensions.GlobalAddressAllocation;

                    // 清空现有数据
                    allocation.InputSegments.Clear();
                    allocation.OutputSegments.Clear();

                    // 保存输入地址段
                    foreach (var segment in _allocatedInputSegments)
                    {
                        allocation.InputSegments.Add(new Models.AddressSegmentInfo
                        {
                            StartAddress = segment.StartAddress,
                            EndAddress = segment.EndAddress,
                            Length = segment.Length,
                            DeviceId = segment.DeviceId,
                            ModuleId = segment.ModuleId,
                            AddressType = segment.AddressType.ToString()
                        });
                    }

                    // 保存输出地址段
                    foreach (var segment in _allocatedOutputSegments)
                    {
                        allocation.OutputSegments.Add(new Models.AddressSegmentInfo
                        {
                            StartAddress = segment.StartAddress,
                            EndAddress = segment.EndAddress,
                            Length = segment.Length,
                            DeviceId = segment.DeviceId,
                            ModuleId = segment.ModuleId,
                            AddressType = segment.AddressType.ToString()
                        });
                    }

                    allocation.LastUpdated = DateTime.Now;

                    Debug.WriteLine($"已保存地址分配状态：输入段 {allocation.InputSegments.Count} 个，输出段 {allocation.OutputSegments.Count} 个");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"保存地址分配状态时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从项目配置加载地址分配状态
        /// </summary>
        public void LoadAddressAllocationFromProject()
        {
            if (_projectManager?.CurrentProject == null)
            {
                Debug.WriteLine("无法加载地址分配状态：项目管理器或当前项目为空");
                return;
            }

            lock (_lock)
            {
                try
                {
                    var allocation = _projectManager.CurrentProject.ProjectSpecificExtensions.GlobalAddressAllocation;

                    // 清空当前分配
                    _allocatedInputSegments.Clear();
                    _allocatedOutputSegments.Clear();

                    // 加载输入地址段
                    foreach (var segmentInfo in allocation.InputSegments)
                    {
                        var segment = new AddressSegment
                        {
                            StartAddress = segmentInfo.StartAddress,
                            EndAddress = segmentInfo.EndAddress,
                            Length = segmentInfo.Length,
                            DeviceId = segmentInfo.DeviceId,
                            ModuleId = segmentInfo.ModuleId,
                            AddressType = Enum.TryParse<AddressType>(segmentInfo.AddressType, out var type) ? type : AddressType.Input
                        };
                        _allocatedInputSegments.Add(segment);
                    }

                    // 加载输出地址段
                    foreach (var segmentInfo in allocation.OutputSegments)
                    {
                        var segment = new AddressSegment
                        {
                            StartAddress = segmentInfo.StartAddress,
                            EndAddress = segmentInfo.EndAddress,
                            Length = segmentInfo.Length,
                            DeviceId = segmentInfo.DeviceId,
                            ModuleId = segmentInfo.ModuleId,
                            AddressType = Enum.TryParse<AddressType>(segmentInfo.AddressType, out var type) ? type : AddressType.Output
                        };
                        _allocatedOutputSegments.Add(segment);
                    }

                    // 按地址排序
                    _allocatedInputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));
                    _allocatedOutputSegments.Sort((a, b) => a.StartAddress.CompareTo(b.StartAddress));

                    Debug.WriteLine($"已加载地址分配状态：输入段 {_allocatedInputSegments.Count} 个，输出段 {_allocatedOutputSegments.Count} 个");

                    // 验证加载的地址分配
                    var validationResult = ValidateAddressAllocation();
                    if (!validationResult.IsValid)
                    {
                        Debug.WriteLine("警告：加载的地址分配存在问题");
                        foreach (var error in validationResult.GetAllErrors())
                        {
                            Debug.WriteLine($"  - {error}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"加载地址分配状态时出错: {ex.Message}");
                    // 出错时清空分配，避免不一致状态
                    _allocatedInputSegments.Clear();
                    _allocatedOutputSegments.Clear();
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 查找可用的输入地址空间
        /// </summary>
        /// <param name="length">需要的地址长度</param>
        /// <returns>可用的起始地址，如果没有足够空间返回-1</returns>
        private int FindAvailableInputAddress(int length)
        {
            Debug.WriteLine($"查找可用输入地址空间，需要长度: {length}");

            // 按起始地址排序
            var sortedSegments = _allocatedInputSegments.OrderBy(s => s.StartAddress).ToList();

            Debug.WriteLine($"当前已分配的输入地址段数量: {sortedSegments.Count}");
            foreach (var seg in sortedSegments)
            {
                Debug.WriteLine($"  已分配段: {seg.StartAddress}-{seg.EndAddress} ({seg.DeviceId}.{seg.ModuleId})");
            }

            // 检查从0开始是否有足够空间
            if (sortedSegments.Count == 0)
            {
                Debug.WriteLine("没有已分配段，从地址0开始分配");
                if (!HasInputAddressConflict(0, length))
                {
                    return 0;
                }
            }

            if (sortedSegments[0].StartAddress >= length)
            {
                Debug.WriteLine($"地址0到{length-1}可用，第一个已分配段从{sortedSegments[0].StartAddress}开始");
                if (!HasInputAddressConflict(0, length))
                {
                    return 0;
                }
            }

            // 检查已分配段之间的空隙
            for (int i = 0; i < sortedSegments.Count - 1; i++)
            {
                int gapStart = sortedSegments[i].EndAddress + 1;
                int gapEnd = sortedSegments[i + 1].StartAddress - 1;
                int gapSize = gapEnd - gapStart + 1;

                Debug.WriteLine($"检查空隙: {gapStart}-{gapEnd} (大小: {gapSize})");

                if (gapSize >= length)
                {
                    if (!HasInputAddressConflict(gapStart, length))
                    {
                        Debug.WriteLine($"找到可用空隙: 从地址{gapStart}开始，长度{length}");
                        return gapStart;
                    }
                    else
                    {
                        Debug.WriteLine($"空隙 {gapStart}-{gapStart + length - 1} 存在冲突，跳过");
                    }
                }
            }

            // 检查最后一个段之后是否有足够空间
            if (sortedSegments.Count > 0)
            {
                int lastEnd = sortedSegments.Last().EndAddress;
                int nextStart = lastEnd + 1;
                int remainingSpace = INPUT_ADDRESS_POOL_SIZE - nextStart;

                Debug.WriteLine($"检查末尾空间: 最后段结束于{lastEnd}，下一个可用地址{nextStart}，剩余空间{remainingSpace}");

                if (remainingSpace >= length)
                {
                    if (!HasInputAddressConflict(nextStart, length))
                    {
                        Debug.WriteLine($"找到末尾可用空间: 从地址{nextStart}开始，长度{length}");
                        return nextStart;
                    }
                    else
                    {
                        Debug.WriteLine($"末尾空间 {nextStart}-{nextStart + length - 1} 存在冲突，跳过");
                    }
                }
            }

            Debug.WriteLine("没有找到足够的连续空间");
            return -1; // 没有足够的连续空间
        }

        /// <summary>
        /// 查找可用的输出地址空间
        /// </summary>
        /// <param name="length">需要的地址长度</param>
        /// <returns>可用的起始地址，如果没有足够空间返回-1</returns>
        private int FindAvailableOutputAddress(int length)
        {
            Debug.WriteLine($"查找可用输出地址空间，需要长度: {length}");

            // 按起始地址排序
            var sortedSegments = _allocatedOutputSegments.OrderBy(s => s.StartAddress).ToList();

            Debug.WriteLine($"当前已分配的输出地址段数量: {sortedSegments.Count}");
            foreach (var seg in sortedSegments)
            {
                Debug.WriteLine($"  已分配段: {seg.StartAddress}-{seg.EndAddress} ({seg.DeviceId}.{seg.ModuleId})");
            }

            // 检查从0开始是否有足够空间
            if (sortedSegments.Count == 0)
            {
                Debug.WriteLine("没有已分配段，从地址0开始分配");
                if (!HasOutputAddressConflict(0, length))
                {
                    return 0;
                }
            }

            if (sortedSegments[0].StartAddress >= length)
            {
                Debug.WriteLine($"地址0到{length-1}可用，第一个已分配段从{sortedSegments[0].StartAddress}开始");
                if (!HasOutputAddressConflict(0, length))
                {
                    return 0;
                }
            }

            // 检查已分配段之间的空隙
            for (int i = 0; i < sortedSegments.Count - 1; i++)
            {
                int gapStart = sortedSegments[i].EndAddress + 1;
                int gapEnd = sortedSegments[i + 1].StartAddress - 1;
                int gapSize = gapEnd - gapStart + 1;

                Debug.WriteLine($"检查空隙: {gapStart}-{gapEnd} (大小: {gapSize})");

                if (gapSize >= length)
                {
                    if (!HasOutputAddressConflict(gapStart, length))
                    {
                        Debug.WriteLine($"找到可用空隙: 从地址{gapStart}开始，长度{length}");
                        return gapStart;
                    }
                    else
                    {
                        Debug.WriteLine($"空隙 {gapStart}-{gapStart + length - 1} 存在冲突，跳过");
                    }
                }
            }

            // 检查最后一个段之后是否有足够空间
            if (sortedSegments.Count > 0)
            {
                int lastEnd = sortedSegments.Last().EndAddress;
                int nextStart = lastEnd + 1;
                int remainingSpace = OUTPUT_ADDRESS_POOL_SIZE - nextStart;

                Debug.WriteLine($"检查末尾空间: 最后段结束于{lastEnd}，下一个可用地址{nextStart}，剩余空间{remainingSpace}");

                if (remainingSpace >= length)
                {
                    if (!HasOutputAddressConflict(nextStart, length))
                    {
                        Debug.WriteLine($"找到末尾可用空间: 从地址{nextStart}开始，长度{length}");
                        return nextStart;
                    }
                    else
                    {
                        Debug.WriteLine($"末尾空间 {nextStart}-{nextStart + length - 1} 存在冲突，跳过");
                    }
                }
            }

            Debug.WriteLine("没有找到足够的连续空间");
            return -1; // 没有足够的连续空间
        }

        /// <summary>
        /// 检查指定地址范围是否与现有输入地址分配冲突
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="length">地址长度</param>
        /// <param name="excludeDeviceId">排除的设备ID（用于检查同设备内的冲突）</param>
        /// <param name="excludeModuleId">排除的模块ID</param>
        /// <returns>如果有冲突返回true</returns>
        public bool HasInputAddressConflict(int startAddress, int length, string excludeDeviceId = "", string excludeModuleId = "")
        {
            int endAddress = startAddress + length - 1;

            foreach (var segment in _allocatedInputSegments)
            {
                // 跳过指定的设备和模块
                if (!string.IsNullOrEmpty(excludeDeviceId) && !string.IsNullOrEmpty(excludeModuleId) &&
                    segment.DeviceId == excludeDeviceId && segment.ModuleId == excludeModuleId)
                {
                    continue;
                }

                // 检查地址范围是否重叠
                bool hasOverlap = !(endAddress < segment.StartAddress || startAddress > segment.EndAddress);
                if (hasOverlap)
                {
                    Debug.WriteLine($"输入地址冲突检测: 地址范围 {startAddress}-{endAddress} 与现有分配 {segment.StartAddress}-{segment.EndAddress} ({segment.DeviceId}.{segment.ModuleId}) 冲突");
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查指定地址范围是否与现有输出地址分配冲突
        /// </summary>
        /// <param name="startAddress">起始地址</param>
        /// <param name="length">地址长度</param>
        /// <param name="excludeDeviceId">排除的设备ID（用于检查同设备内的冲突）</param>
        /// <param name="excludeModuleId">排除的模块ID</param>
        /// <returns>如果有冲突返回true</returns>
        public bool HasOutputAddressConflict(int startAddress, int length, string excludeDeviceId = "", string excludeModuleId = "")
        {
            int endAddress = startAddress + length - 1;

            foreach (var segment in _allocatedOutputSegments)
            {
                // 跳过指定的设备和模块
                if (!string.IsNullOrEmpty(excludeDeviceId) && !string.IsNullOrEmpty(excludeModuleId) &&
                    segment.DeviceId == excludeDeviceId && segment.ModuleId == excludeModuleId)
                {
                    continue;
                }

                // 检查地址范围是否重叠
                bool hasOverlap = !(endAddress < segment.StartAddress || startAddress > segment.EndAddress);
                if (hasOverlap)
                {
                    Debug.WriteLine($"输出地址冲突检测: 地址范围 {startAddress}-{endAddress} 与现有分配 {segment.StartAddress}-{segment.EndAddress} ({segment.DeviceId}.{segment.ModuleId}) 冲突");
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 验证地址段的一致性
        /// </summary>
        /// <param name="segments">地址段列表</param>
        /// <param name="poolSize">地址池大小</param>
        /// <param name="addressTypeName">地址类型名称</param>
        /// <returns>验证结果</returns>
        private SegmentValidationResult ValidateAddressSegments(List<AddressSegment> segments, int poolSize, string addressTypeName)
        {
            var result = new SegmentValidationResult { IsValid = true, AddressType = addressTypeName };

            // 检查地址范围
            foreach (var segment in segments)
            {
                if (segment.StartAddress < 0 || segment.EndAddress >= poolSize)
                {
                    result.IsValid = false;
                    result.Errors.Add($"{addressTypeName}地址段 {segment.DeviceId}.{segment.ModuleId} 超出有效范围: {segment.StartAddress}-{segment.EndAddress}");
                }

                if (segment.StartAddress > segment.EndAddress)
                {
                    result.IsValid = false;
                    result.Errors.Add($"{addressTypeName}地址段 {segment.DeviceId}.{segment.ModuleId} 起始地址大于结束地址: {segment.StartAddress}-{segment.EndAddress}");
                }

                if (segment.Length != (segment.EndAddress - segment.StartAddress + 1))
                {
                    result.IsValid = false;
                    result.Errors.Add($"{addressTypeName}地址段 {segment.DeviceId}.{segment.ModuleId} 长度不一致: 声明长度={segment.Length}, 实际长度={segment.EndAddress - segment.StartAddress + 1}");
                }
            }

            // 检查地址重叠
            var sortedSegments = segments.OrderBy(s => s.StartAddress).ToList();
            for (int i = 0; i < sortedSegments.Count - 1; i++)
            {
                var current = sortedSegments[i];
                var next = sortedSegments[i + 1];

                if (current.EndAddress >= next.StartAddress)
                {
                    result.IsValid = false;
                    result.Errors.Add($"{addressTypeName}地址重叠: {current.DeviceId}.{current.ModuleId} ({current.StartAddress}-{current.EndAddress}) 与 {next.DeviceId}.{next.ModuleId} ({next.StartAddress}-{next.EndAddress})");
                }
            }

            // 检查重复分配
            var duplicates = segments.GroupBy(s => $"{s.DeviceId}.{s.ModuleId}")
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var duplicate in duplicates)
            {
                result.IsValid = false;
                result.Errors.Add($"{addressTypeName}地址重复分配给模块: {duplicate}");
            }

            return result;
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// 地址段信息
    /// </summary>
    public class AddressSegment
    {
        public int StartAddress { get; set; }
        public int EndAddress { get; set; }
        public int Length { get; set; }
        public string DeviceId { get; set; }
        public string ModuleId { get; set; }
        public AddressType AddressType { get; set; }
    }

    /// <summary>
    /// 地址类型枚举
    /// </summary>
    public enum AddressType
    {
        Input,
        Output
    }

    /// <summary>
    /// 地址分配统计信息
    /// </summary>
    public class AddressAllocationStats
    {
        public int InputAddressesUsed { get; set; }
        public int InputAddressesAvailable { get; set; }
        public double InputUtilizationPercentage { get; set; }

        public int OutputAddressesUsed { get; set; }
        public int OutputAddressesAvailable { get; set; }
        public double OutputUtilizationPercentage { get; set; }

        public int DeviceCount { get; set; }
        public int ModuleCount { get; set; }

        public override string ToString()
        {
            return $"输入地址: {InputAddressesUsed}/{GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE} ({InputUtilizationPercentage:F1}%), " +
                   $"输出地址: {OutputAddressesUsed}/{GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE} ({OutputUtilizationPercentage:F1}%), " +
                   $"设备数: {DeviceCount}, 模块数: {ModuleCount}";
        }
    }

    /// <summary>
    /// 地址验证结果
    /// </summary>
    public class AddressValidationResult
    {
        public bool IsValid { get; set; }
        public SegmentValidationResult InputValidation { get; set; }
        public SegmentValidationResult OutputValidation { get; set; }

        public List<string> GetAllErrors()
        {
            var errors = new List<string>();
            if (InputValidation?.Errors != null)
                errors.AddRange(InputValidation.Errors);
            if (OutputValidation?.Errors != null)
                errors.AddRange(OutputValidation.Errors);
            return errors;
        }
    }

    /// <summary>
    /// 地址段验证结果
    /// </summary>
    public class SegmentValidationResult
    {
        public bool IsValid { get; set; }
        public string AddressType { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    #endregion
}
