using System;
using System.Collections.Generic;

namespace PNConfigTool.Models
{
    /// <summary>
    /// 设备配置工厂类，用于创建标准化的设备配置
    /// </summary>
    public static class DeviceConfigFactory
    {
        /// <summary>
        /// 创建分布式设备节点配置（用于ListOfNodes）
        /// </summary>
        /// <param name="deviceID">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="gsdmlPath">GSDML文件路径</param>
        /// <param name="gsdRefID">GSDML引用ID</param>
        /// <param name="deviceType">设备类型（DAP节点名称）</param>
        /// <param name="projectDirectory">项目目录（用于转换为相对路径）</param>
        /// <returns>分布式设备节点配置</returns>
        public static DecentralDeviceNode CreateDecentralDeviceNode(
            string deviceID,
            string deviceName,
            string gsdmlPath,
            string gsdRefID,
            string deviceType = "",
            string projectDirectory = "")
        {
            // 将GSDML路径转换为相对路径
            string relativePath = !string.IsNullOrEmpty(projectDirectory) && !string.IsNullOrEmpty(gsdmlPath)
                ? PNConfigTool.Utilities.PathHelper.ToRelativePath(gsdmlPath, projectDirectory)
                : gsdmlPath;

            return new DecentralDeviceNode
            {
                DeviceID = deviceID,
                DeviceName = deviceName,
                DeviceType = deviceType,
                GSDPath = relativePath,
                GSDRefID = gsdRefID,
                Interface = new DecentralDeviceInterface
                {
                    InterfaceID = $"{deviceID}_Interface",
                    InterfaceName = $"{deviceID}_Interface"
                }
            };
        }

        /// <summary>
        /// 创建分布式设备配置（用于Configuration）
        /// </summary>
        /// <param name="deviceRefID">设备引用ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="subnetMask">子网掩码</param>
        /// <param name="deviceNumber">设备编号</param>
        /// <param name="pnDeviceName">PROFINET设备名称</param>
        /// <param name="gsdmlFilePath">GSDML文件路径</param>
        /// <param name="deviceType">设备类型（DAP节点名称）</param>
        /// <param name="subnetRefID">子网引用ID（可选，仅在使用显式子网配置时设置）</param>
        /// <param name="ioSystemRefID">IO系统引用ID（可选，仅在使用显式子网配置时设置）</param>
        /// <param name="projectDirectory">项目目录（用于转换为相对路径）</param>
        /// <returns>分布式设备配置</returns>
        public static DecentralDeviceConfig CreateDecentralDeviceConfig(
            string deviceRefID,
            string deviceName,
            string ipAddress,
            string subnetMask,
            int deviceNumber,
            string pnDeviceName,
            string gsdmlFilePath = "",
            string deviceType = "",
            string? subnetRefID = null,
            string? ioSystemRefID = null,
            string projectDirectory = "")
        {
            // 将GSDML路径转换为相对路径
            string relativePath = !string.IsNullOrEmpty(projectDirectory) && !string.IsNullOrEmpty(gsdmlFilePath)
                ? PNConfigTool.Utilities.PathHelper.ToRelativePath(gsdmlFilePath, projectDirectory)
                : gsdmlFilePath;

            return new DecentralDeviceConfig
            {
                DeviceRefID = deviceRefID,
                DeviceType = deviceType,
                GSDMLFilePath = relativePath,
                DecentralDeviceInterface = new DecentralDeviceInterfaceConfig
                {
                    InterfaceRefID = $"{deviceRefID}_Interface",
                    EthernetAddresses = new EthernetAddressesConfig
                    {
                        IPProtocol = new IPProtocolConfig
                        {
                            SetInTheProject = new SetInTheProjectConfig
                            {
                                IPAddress = ipAddress,
                                SubnetMask = subnetMask,
                                SynchronizeRouterSettingsWithIOController = true
                            }
                        },
                        PROFINETDeviceName = new PROFINETDeviceNameConfig
                        {
                            PNDeviceName = pnDeviceName,
                            DeviceNumber = deviceNumber
                        },
                        SubnetRefID = subnetRefID,
                        IOSystemRefID = ioSystemRefID
                    }
                },
                Modules = new List<ModuleConfig>()
            };
        }

        /// <summary>
        /// 创建模块配置
        /// </summary>
        /// <param name="moduleRefID">模块引用ID</param>
        /// <param name="moduleName">模块名称</param>
        /// <param name="slotNumber">槽位号</param>
        /// <param name="comment">注释</param>
        /// <returns>模块配置</returns>
        public static ModuleConfig CreateModuleConfig(
            string moduleRefID,
            string moduleName,
            int slotNumber,
            string comment = "")
        {
            return new ModuleConfig
            {
                ModuleRefID = moduleRefID,
                SlotNumber = slotNumber,

                Submodules = new List<SubmoduleConfig>()
            };
        }

        /// <summary>
        /// 创建子模块配置
        /// </summary>
        /// <param name="submoduleRefID">子模块引用ID</param>
        /// <param name="submoduleName">子模块名称</param>
        /// <param name="index">UI索引</param>
        /// <param name="position">位置</param>
        /// <param name="inputStartAddress">输入起始地址</param>
        /// <param name="outputStartAddress">输出起始地址</param>
        /// <param name="comment">注释</param>
        /// <returns>子模块配置</returns>
        public static SubmoduleConfig CreateSubmoduleConfig(
            string submoduleRefID,
            string submoduleName,
            int index = 0,
            string position = "0",
            string inputStartAddress = "0",
            string outputStartAddress = "0",
            string comment = "")
        {
            return new SubmoduleConfig
            {
                SubmoduleRefID = submoduleRefID,

                UIProperties = new UIPropertiesConfig
                {
                    Index = index,
                    IsSelected = false,
                    Position = position
                },
                AddressConfiguration = new AddressConfigurationConfig
                {
                    InputStartAddress = inputStartAddress,
                    OutputStartAddress = outputStartAddress,
                    InputAddress = int.TryParse(inputStartAddress, out int inputAddr) ? inputAddr : 0,
                    OutputAddress = int.TryParse(outputStartAddress, out int outputAddr) ? outputAddr : 0
                }
            };
        }

        /// <summary>
        /// 从旧的设备配置创建新的设备配置对
        /// </summary>
        /// <param name="legacyConfig">旧的设备配置</param>
        /// <param name="deviceNumber">设备编号</param>
        /// <param name="subnetRefID">子网引用ID（可选）</param>
        /// <param name="ioSystemRefID">IO系统引用ID（可选）</param>
        /// <returns>设备节点和设备配置的元组</returns>
        public static (DecentralDeviceNode deviceNode, DecentralDeviceConfig deviceConfig) CreateFromLegacyConfig(
            LegacyDeviceConfig legacyConfig,
            int deviceNumber,
            string? subnetRefID = null,
            string? ioSystemRefID = null)
        {
            string deviceID = string.IsNullOrEmpty(legacyConfig.DeviceIdentifier)
                ? legacyConfig.DeviceName.Replace(" ", "_")
                : legacyConfig.DeviceIdentifier;

            var deviceNode = CreateDecentralDeviceNode(
                deviceID,
                legacyConfig.DeviceName,
                legacyConfig.GSDMLFile ?? "",
                "DAP1", // 默认值，可能需要从GSDML文件中提取
                legacyConfig.DeviceType ?? ""
            );

            var deviceConfig = CreateDecentralDeviceConfig(
                deviceID,
                legacyConfig.DeviceName,
                legacyConfig.IPAddress,
                legacyConfig.SubnetMask,
                deviceNumber,
                legacyConfig.DeviceName.ToLower().Replace(" ", ""),
                legacyConfig.GSDMLFile ?? "",
                legacyConfig.DeviceType ?? "",
                subnetRefID,
                ioSystemRefID
            );

            return (deviceNode, deviceConfig);
        }
    }

    /// <summary>
    /// 旧版设备配置类（用于兼容性）
    /// </summary>
    public class LegacyDeviceConfig
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型
        /// </summary>
        public string? DeviceType { get; set; }

        /// <summary>
        /// 设备唯一标识符
        /// </summary>
        public string DeviceIdentifier { get; set; } = string.Empty;

        /// <summary>
        /// GSDML文件路径
        /// </summary>
        public string GSDMLFile { get; set; } = string.Empty;

        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; } = "0.0.0.0";

        /// <summary>
        /// 子网掩码
        /// </summary>
        public string SubnetMask { get; set; } = "*************";

        /// <summary>
        /// 设备参数配置
        /// </summary>
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// 设备配置助手类
    /// </summary>
    public static class DeviceConfigHelper
    {
        /// <summary>
        /// 生成下一个可用的设备编号
        /// </summary>
        /// <param name="existingDevices">现有设备列表</param>
        /// <returns>下一个可用的设备编号</returns>
        public static int GetNextDeviceNumber(List<DecentralDeviceConfig> existingDevices)
        {
            if (existingDevices == null || existingDevices.Count == 0)
                return 1;

            int maxNumber = 0;
            foreach (var device in existingDevices)
            {
                if (device.DecentralDeviceInterface?.EthernetAddresses?.PROFINETDeviceName?.DeviceNumber > maxNumber)
                {
                    maxNumber = device.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.DeviceNumber;
                }
            }

            return maxNumber + 1;
        }

        /// <summary>
        /// 生成下一个可用的IP地址
        /// </summary>
        /// <param name="baseIP">基础IP地址（如***********）</param>
        /// <param name="existingDevices">现有设备列表</param>
        /// <returns>下一个可用的IP地址</returns>
        public static string GetNextIPAddress(string baseIP, List<DecentralDeviceConfig> existingDevices)
        {
            if (existingDevices == null || existingDevices.Count == 0)
                return IncrementIPAddress(baseIP);

            var usedIPs = new HashSet<string>();
            foreach (var device in existingDevices)
            {
                var ip = device.DecentralDeviceInterface?.EthernetAddresses?.IPProtocol?.SetInTheProject?.IPAddress;
                if (!string.IsNullOrEmpty(ip))
                {
                    usedIPs.Add(ip);
                }
            }

            string nextIP = IncrementIPAddress(baseIP);
            while (usedIPs.Contains(nextIP))
            {
                nextIP = IncrementIPAddress(nextIP);
            }

            return nextIP;
        }

        /// <summary>
        /// 递增IP地址的最后一个八位组
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>递增后的IP地址</returns>
        private static string IncrementIPAddress(string ipAddress)
        {
            var parts = ipAddress.Split('.');
            if (parts.Length == 4 && int.TryParse(parts[3], out int lastOctet))
            {
                lastOctet++;
                if (lastOctet > 254) lastOctet = 2; // 避免使用255和1
                parts[3] = lastOctet.ToString();
                return string.Join(".", parts);
            }
            return ipAddress;
        }
    }
}