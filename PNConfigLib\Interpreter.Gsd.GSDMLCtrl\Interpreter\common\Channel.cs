/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Channel.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The Channel element contains information about data and quality.
    /// It could be used to specify the characteristic of channels
    /// of a IO Data.
    /// </summary>
    public class Channel :
        GsdObject,
        GSDI.IChannel
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Channel if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public Channel()
        {
            m_ChannelNumber = 0;
        }

        #endregion

        //########################################################################################
        #region Fields

        private UInt32 m_ChannelNumber;
        private GsdData m_Data;
        private Quality m_Quality;
        private DataRef m_DataRef;
        #endregion

        //########################################################################################
        #region Properties


        /// <summary>
        /// Accesses the bit offset.
        /// </summary>
        public UInt32 ChannelNumber => this.m_ChannelNumber;

        public GsdData Data => this.m_Data;

        public Quality Quality => this.m_Quality;

        public DataRef DataRef => this.m_DataRef;

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldChannelNumber;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_ChannelNumber = (UInt32)hash[member];

                member = Models.s_FieldData;
                if (hash.ContainsKey(member) && hash[member] is GsdData)
                    this.m_Data = hash[member] as GsdData;

                member = Models.s_FieldQuality;
                if (hash.ContainsKey(member) && hash[member] is Quality)
                    this.m_Quality = hash[member] as Quality;

                member = Models.s_FieldDataRef;
                if (hash.ContainsKey(member) && hash[member] is DataRef)
                    this.m_DataRef = hash[member] as DataRef;
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectPortSubmodule);

            // ----------------------------------------------
            bool succeeded = SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return succeeded;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            // From base class
            bool succeeded = base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldChannelNumber, this.m_ChannelNumber);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInfo, this.m_Data);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInfo, this.m_Quality);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldDataRef, this.m_DataRef);

            return succeeded;
        }

        #endregion
    }
}
