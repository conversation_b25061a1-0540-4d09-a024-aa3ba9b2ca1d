/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SyncDomainConsistencyChecker.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Isochrone;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.PNPlannerAdapter;

#endregion

namespace PNConfigLib.HWCNBL.DomainManagement
{
    /// <summary>
    /// Contains consistency check methods for sync domains.
    /// </summary>
    internal static class SyncDomainConsistencyChecker
    {
        /// <summary>
        /// General consistency checker of the sync-domain. Checks the consistency of the sync-domain after
        /// PNPlanner calls.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        public static void CheckConsistencyAfterPNPlanner1(
            SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            CheckSynchronisationRole(syncDomainBusinessLogic);
            CheckFragmentationConsistency(syncDomainBusinessLogic);
            CheckIrtSettings(syncDomainBusinessLogic);
            if (syncDomainBusinessLogic.SynchronizedParticipants.Any())
            {
                // Check the count of Sync-Masters and Secondary Sync-Masters in the Subnet.
                CheckSyncMasterCounts(syncDomainBusinessLogic);
            }

            if (syncDomainBusinessLogic.HasDevicesUsingIrtTop)
            {
                // Check the supported startup modes of irttop.
                CheckSupportedArStartupModes(syncDomainBusinessLogic);

                // Check the stations between primary and secondary sync-master.
                CheckSyncMasterDistances(syncDomainBusinessLogic);
            }

            // Check if IRT is being used for the user interface consistency
            if (syncDomainBusinessLogic.SynchronizedParticipants.Any())
            {
                CheckExpertModeConsistency(syncDomainBusinessLogic);
                CheckBandwidthLevelConsistency(syncDomainBusinessLogic);
                CheckReductionRatioConsistency(syncDomainBusinessLogic);
            }

            CheckConsistencySyncDomainName(syncDomainBusinessLogic);
        }

        /// <summary>
        /// General consistency checker of the sync-domain. Checks the consistency of the sync-domain after
        /// PNPlanner calls (these calls must be done externally).
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        public static void CheckConsistencyAfterPNPlanner2(
            SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            bool someParticipantsSynchronized = syncDomainBusinessLogic.SynchronizedParticipants.Any();
            if (someParticipantsSynchronized)
            {
                if (syncDomainBusinessLogic.SendClockFactor == -1)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, syncDomainBusinessLogic.SyncDomain, ConsistencyConstants.SendclockInvalidInSyncDomain);
                }
                else
                {
                    // Check if small send clocks are available
                    if ((syncDomainBusinessLogic.SendClock < 250000)
                        && !syncDomainBusinessLogic.IsFragmentationPossible)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, syncDomainBusinessLogic.SyncDomain, ConsistencyConstants.SyncDomainSmallSendClocksNotSupported);
                    }
                }
            }

            if (someParticipantsSynchronized)
            {
                //Check if exist Slave device which is unreachable from the Master
                CheckConsistencyPortTopology(syncDomainBusinessLogic);
            }
            if (syncDomainBusinessLogic.SyncMaster != null)
            {
                if (syncDomainBusinessLogic.HasDevicesUsingIrtFlex
                    && (syncDomainBusinessLogic.PNPlannerResults.CalculatedPartIrt > syncDomainBusinessLogic.ReservedIrtBandwidth))
                {
                    // Create the warning message.
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, syncDomainBusinessLogic.SyncDomain, ConsistencyConstants.SyncDomainResBandwidthExceeded);
                }
                if (syncDomainBusinessLogic.HasDevicesUsingIrtTop)
                {
                    // Check the maximum red period
                    CheckMaxSizeRedPeriod(syncDomainBusinessLogic);

                    // the related attribute
                    CheckMaxRangeIRFrameID(syncDomainBusinessLogic);
                }
            }
        }

        /// <summary>
        /// Consistency checks for the created irttop island are made here.
        /// </summary>
        /// <param name="creationResult">Result of the CreateIrtTopIsland method</param>
        /// <param name="irtTopIsland">IrtTop island to be checked</param>
        /// <param name="syncDomainBl"></param>
        /// <returns></returns>
        public static bool CheckIrtTopIsland(PNPlannerResult creationResult, InterfaceFrameDataList irtTopIsland,
            SyncDomainBusinessLogic syncDomainBl)
        {
            if (creationResult.ResultType != PNPlannerResultType.Successful)
            {
                if (creationResult.MessageParameters != null)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        syncDomainBl,
                        creationResult.MsgId,
                        creationResult.MessageParameters);
                }
                return false;
            }

            if ((irtTopIsland != null)
                && (irtTopIsland.AllPNFrames.Count > 0))
            {
                // Go through all participants in the IrtTop island.
                foreach (Interface interfaceSubmodule in irtTopIsland.AllPNFrames.Keys.Where(
                    pf => PNAttributeUtility.GetAdjustedSyncRole(pf.GetIOConnector()) != PNIRTSyncRole.NotSynchronized))
                {
                    bool isDecentralPdev = (interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.PnPdevParametrizationDecentral, new AttributeAccessCode(), 0) > 0);

                    IList<DataModel.PCLObjects.Port> lPorts = interfaceSubmodule.GetPortModulesSorted();

                    foreach (DataModel.PCLObjects.Port localPort in lPorts)
                    {
                        IList<DataModel.PCLObjects.Port> pPorts = localPort.GetPartnerPorts();
                        foreach (DataModel.PCLObjects.Port partnerPort in pPorts)
                        {
                            if (irtTopIsland.AllPNFrames.ContainsKey(NavigationUtilities.GetInterfaceOfPort(partnerPort)))
                            {
                                AttributeAccessCode ac = new AttributeAccessCode();

                                // a deactivated port is prohibited in an IRT-Line
                                if (localPort.AttributeAccess.GetAnyAttribute<Boolean>(InternalAttributeNames.PnPortDeactivated, ac, false))
                                {
                                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                                        syncDomainBl.SyncDomain,
                                        ConsistencyConstants.Irt_With_Deactivated_Port);
                                    return false;
                                }
                            }
                        }
                    }

                    // Ignore the devices (and the idevices if the PDev is true)
                    if (!Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule) || isDecentralPdev)
                    {
                        continue;
                    }
                    // If the Controller takes place in an island, it should be synchronized.
                    PNIOC ioController = interfaceSubmodule.PNIOC;
                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioController);
                    if (syncRole != PNIRTSyncRole.NotSynchronized)
                    {
                        continue;
                    }
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Checks the validity of the name of the sync domain:
        /// 1) It cannot be longer then 64 characters.
        /// 2) The converted name cannot be longer then 64 charactersç
        /// 3) The name must be unique in the scope of sync domains.
        /// </summary>
        /// <param name="syncDomainBusinessLogic">Current sync domain business logic object.</param>
        internal static void CheckConsistencySyncDomainName(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            string syncDomainName = syncDomainBusinessLogic.DomainName;
            if (string.IsNullOrEmpty(syncDomainName))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainEmptyName);
            }

            if ((syncDomainName != null)
                && (syncDomainName.Length > 64)) // 64 is the max length of the sync domain name
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainNameTooLong);
            }
            else
            {
                string tempSyncDomainName;
                PNNameOfStationConverter.DnsNameConvertStatus result =
                    PNNameOfStationConverter.EncodePNNameOfStation(syncDomainName, null, null, out tempSyncDomainName);
                if (result == PNNameOfStationConverter.DnsNameConvertStatus.CpuNameLengthExceeded)
                {
                    // The converted name also shouldn't be too long
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                      syncDomainBusinessLogic.SyncDomain,
                      ConsistencyConstants.SyncDomainNameTooLong);
                }
            }
        }

        /// <summary>
        /// Checks the count of the sync-masters and secondary sync-masters in the sync-domain.
        /// The method assumes that there are synchronized participants in the domain. In this case exactly 1
        /// sync-master (or primary sync-master) should exist in the domain. Sync-domain can additionaly have at most
        /// 1 secondary sync-master.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        private static void CheckSyncMasterCounts(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            int masterCount = 0;
            int secondaryMasterCount = 0;
            foreach (Interface participant in syncDomainBusinessLogic.AllParticipants)
            {
                PclObject ioConnector = NavigationUtilities.GetIOConnector(participant);
                if (null == ioConnector)
                {
                    continue;
                }

                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);

                switch (syncRole)
                {
                    case PNIRTSyncRole.PrimarySyncMaster:
                    case PNIRTSyncRole.SyncMaster:
                        masterCount++;
                        break;

                    case PNIRTSyncRole.SecondarySyncMaster:
                        secondaryMasterCount++;
                        break;
                }
            }
            if (masterCount == 0)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainNoSyncMaster,
                    AttributeUtilities.GetName(syncDomainBusinessLogic.SyncDomain));

            }
            else if (masterCount > 1)
            {
                // Create the error message.
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainSyncMasterCount,
                    AttributeUtilities.GetName(syncDomainBusinessLogic.SyncDomain));
            }
            else if (secondaryMasterCount > 1)
            {
                // Create the error message.
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainSecondarySyncMasterCount,
                    AttributeUtilities.GetName(syncDomainBusinessLogic.SyncDomain));
            }
        }

        /// <summary>
        /// Checks IO-Controller fragmentation mode related consistencies if fragmentation is active in the 
        /// sync-domain.
        /// 1- If IO-Controller fragmentation mode is NoRTCy or OnePortNoRTCy, sync-domain is not allowed to have RTCy 
        /// devices.
        /// 2- If IO-Controller fragmentation mode is OnePort or OnePortNoRTCy, only one port of the IO-Controller
        /// must be activated.
        /// </summary>
        /// <param name="syncDomainBl"></param>
        private static void CheckFragmentationConsistency(SyncDomainBusinessLogic syncDomainBl)
        {
            string noRtcyIocName = null;
            bool checkPortsOfInterface = true;
            if (!syncDomainBl.IsFragmentationActive)
            {
                return;
            }
            foreach (Interface ioControllerInterface in syncDomainBl.IOControllerInterfaces)
            {
                if (Utility.IsInterfaceSynchronized(ioControllerInterface))
                {
                    continue;
                }

                PNIRTFragmentationMode fragmentationMode =
                    (PNIRTFragmentationMode)ioControllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtFragmentationMode,
                        new AttributeAccessCode(),
                        (uint)PNIRTFragmentationMode.None);

                if (fragmentationMode == PNIRTFragmentationMode.All)
                {
                    continue;
                }

                if ((fragmentationMode != PNIRTFragmentationMode.OnePortNoRtcy)
                    && (fragmentationMode != PNIRTFragmentationMode.OnePort))
                {
                    checkPortsOfInterface = false;
                }

                if (fragmentationMode == PNIRTFragmentationMode.NoRtcy
                    || fragmentationMode == PNIRTFragmentationMode.OnePortNoRtcy)
                {
                    noRtcyIocName = Utility.GetNameWithContainer(ioControllerInterface);
                }

                if (!checkPortsOfInterface)
                {
                    continue;
                }

                if (CheckOnePortFragModeWithMultiplePortsActive(syncDomainBl, ioControllerInterface))
                {
                    return;
                }
            }
            if (noRtcyIocName == null)
            {
                return;
            }
            CheckNoRtcyFragModeWithRtcyDeviceExists(syncDomainBl, noRtcyIocName);
        }

        // Checks whether a RTCy device exits when fragmentation mode is activated in NoRTCy mode
        private static void CheckNoRtcyFragModeWithRtcyDeviceExists(
            SyncDomainBusinessLogic syncDomainBl,
            string noRtcyIocName)
        {
            foreach (Interface typedIfSubmodule in syncDomainBl.IODeviceInterfaces)
            {
                // If the IODevice not assigned to the controller, we don't have to check it
                if (typedIfSubmodule.PNIOD.AssignedController == null ||
                    Utility.IsIrtTopDevice(typedIfSubmodule))
                {
                    continue;
                }
                string iodName = Utility.GetNameWithContainer(typedIfSubmodule);
                // A Controller that supports NoRTCy fragmentation has a RTCy device. Display an error.
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    syncDomainBl.SyncDomain,
                    ConsistencyConstants.SyncDomainNoRtcyFragModeWithRtcyDeviceExists,
                    noRtcyIocName,
                    syncDomainBl.DomainName,
                    iodName);
                return;
            }
        }

        // Checks whether multiple ports are activated on a oneport fragmentation controller
        private static bool CheckOnePortFragModeWithMultiplePortsActive(
            SyncDomainBusinessLogic syncDomainBl,
            Interface ioControllerInterface)
        {
            IList<DataModel.PCLObjects.Port> iocPorts = ioControllerInterface.GetPortModulesSorted();
            int activePorts = 0;
            foreach (DataModel.PCLObjects.Port port in iocPorts)
            {
                if (PortUtility.IsPortDeactivated(port))
                {
                    continue;
                }
                if (++activePorts <= 1)
                {
                    continue;
                }
                // Multiple active ports detected on a OnePort fragmentation controller. Display an error.
                object[] parameters = new object[2];
                parameters[0] = syncDomainBl.DomainName;
                parameters[1] = Utility.GetNameWithContainer(ioControllerInterface);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    syncDomainBl.SyncDomain,
                    ConsistencyConstants.SyncDomainOnePortFragModeWithMultiplePortsActive,
                    parameters);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Checks whether the Sync-Domain badwidth level is valid.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        private static void CheckBandwidthLevelConsistency(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            if (syncDomainBusinessLogic == null)
            {
                throw new ArgumentNullException(nameof(syncDomainBusinessLogic));
            }

            // Get the value from PNIrtBandwidthLevel attribute of the Sync-Domain
            AttributeAccessCode ac = new AttributeAccessCode();
            PNIRTBandwidthLevel currentLevel =
                (PNIRTBandwidthLevel)
                syncDomainBusinessLogic.SyncDomain.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtBandwidthLevel,
                    ac,
                    (byte)PNIRTBandwidthLevel.Fair);

            if (currentLevel > syncDomainBusinessLogic.GetMaxAvailableBandwidthLevel())
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainBwLevelExceeded, 
                    currentLevel, 
                    syncDomainBusinessLogic.GetMaxAvailableBandwidthLevel(),
                    syncDomainBusinessLogic.DomainName);
            }
        }

        /// <summary>
        /// Checks whether the Sync-Domain badwidth level is valid.
        /// </summary>
        /// <param name="syncDomainBl"></param>
        private static void CheckExpertModeConsistency(SyncDomainBusinessLogic syncDomainBl)
        {
            if (syncDomainBl == null)
            {
                throw new ArgumentNullException(nameof(syncDomainBl));
            }
            // Get the value from PNIrtSyncDomainExpertMode attribute of the Sync-Domain
            bool expertModeEnabled = syncDomainBl.SyncDomain.AttributeAccess.GetAnyAttribute<Boolean>(
                InternalAttributeNames.PnIrtSyncDomainExpertMode, new AttributeAccessCode(), false);

            if (expertModeEnabled)
            {
                return;
            }

            // Get the value from PNIrtBandwidthLevel attribute of the Sync-Domain
            AttributeAccessCode ac = new AttributeAccessCode();
            PNIRTBandwidthLevel currentLevel =
                (PNIRTBandwidthLevel)syncDomainBl.SyncDomain.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtBandwidthLevel, ac, (byte)PNIRTBandwidthLevel.Fair);

            if (currentLevel > PNIRTBandwidthLevel.Fair)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBl.SyncDomain,
                  ConsistencyConstants.ValidationExpertModeSupportedBwLevel, currentLevel, syncDomainBl.DomainName);
            }
            if ((syncDomainBl.SendClockFactor != -1) && (syncDomainBl.SendClock < 250000))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBl.SyncDomain,
                    ConsistencyConstants.ValidationExpertModeSupportedSC);
            }
        }

        /// <summary>
        /// Check if fast forwarding is permitted on the sync domain
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        internal static void CheckIfFastForwardingPermitted(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            bool isFfwPermittedOnSyncDomain = syncDomainBusinessLogic.SyncDomain.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnIrtSyncDomainFFW, new AttributeAccessCode(), false);

            if (isFfwPermittedOnSyncDomain)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Warning,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.FastForwardingWithIpv6NotSupported);
            }
        }

        /// <summary>
        /// Consistency check of PNIrtMaxRangeIRFrameID attribute for syncronized IRT interfaces
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        /// <returns></returns>
        private static void CheckMaxRangeIRFrameID(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            if (syncDomainBusinessLogic == null)
            {
                throw new ArgumentNullException(nameof(syncDomainBusinessLogic));
            }

            foreach (Interface interfaceSubmodule in syncDomainBusinessLogic.SynchronizedParticipants)
            {
                uint pnIrtMaxRangeIRFrameID =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxRangeIRFrameID,
                        new AttributeAccessCode(),
                        PNFunctionsDefaultAttributeValues.DefaultPNIRTMaxRangeIRFrameID);

                // Zero means infinite
                if (pnIrtMaxRangeIRFrameID == 0)
                {
                    continue;
                }

                int startOfReqFrameID;
                int endOfReqFrameID;
                ConfigUtility.GetStartEndOfReqFrameIDsList(
                    interfaceSubmodule,
                    out startOfReqFrameID,
                    out endOfReqFrameID);

                if (endOfReqFrameID - startOfReqFrameID <= pnIrtMaxRangeIRFrameID)
                {
                    continue;
                }

                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.MaxRangeIRFrameIDOutOfRange,
                        AttributeUtilities.GetName(interfaceSubmodule));
            }
        }

        /// <summary>
        /// Checks the calculated irt bandwidth against maximum allowed irt bandwidth of the sync-domain. The check
        /// is only irttop related.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        /// <returns>Maximum size of the red period.</returns>
        private static void CheckMaxSizeRedPeriod(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            long maxSizeRed = DomainManagementUtility.GetMaxSizeRed(syncDomainBusinessLogic);

            // Compare the maxSizeRed with the calculated irt)
            if (syncDomainBusinessLogic.PNPlannerResults.CalculatedPartIrt > maxSizeRed)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainMaxRedBwExceeded);
            }
        }

        /// <summary>
        /// Checks the Reduction Ration if the send clock is smaller than 250µs.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        private static void CheckReductionRatioConsistency(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            if (syncDomainBusinessLogic == null)
            {
                throw new ArgumentNullException(nameof(syncDomainBusinessLogic));
            }

            // Get the value from PNIrtSyncDomainExpertMode attribute of the Sync-Domain
            bool expertModeEnabled =
                syncDomainBusinessLogic.SyncDomain.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtSyncDomainExpertMode,
                    new AttributeAccessCode(),
                    false);

            if (!expertModeEnabled)
            {
                return;
            }

            if ((syncDomainBusinessLogic.SendClockFactor == -1)
                || (syncDomainBusinessLogic.SendClock >= 250000))
            {
                return;
            }
            foreach (Interface controllerInterfaceSubmodule in syncDomainBusinessLogic.IOControllerInterfaces)
            {
                List<IPNFrameData> frameList = (List<IPNFrameData>)syncDomainBusinessLogic.GetFramesOfInterface(controllerInterfaceSubmodule);

                if (frameList == null)
                {
                    continue;
                }
                foreach (IPNFrameData frame in frameList)
                {
                    if (frame.DeviceLocalReductionRatio == 1)
                    {
                        continue;
                    }

                    if ((frame.FrameClass != 3)
                        || (frame.SendClockFactor >= 8))
                    {
                        continue;
                    }
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        syncDomainBusinessLogic.SyncDomain,
                        ConsistencyConstants.IncorrectReductionRatioWithSmallSC);
                }
            }
        }

        /// <summary>
        /// Checks if a common startup mode can be found for the synchronized interface submodules of the sync-domain.
        /// This check is only related to irttop.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        private static void CheckSupportedArStartupModes(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            bool onlyLegacySupport = false;
            bool onlyAdvancedSupport = false;
            foreach (Interface interfaceSubmodule in syncDomainBusinessLogic.SynchronizedParticipants)
            {
                List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(interfaceSubmodule);
                bool legacySupportFound = false;
                bool advancedSupportFound = false;
                foreach (PNIrtArStartupMode startupMode in startupModes)
                {
                    switch (startupMode)
                    {
                        case PNIrtArStartupMode.None:
                            break;
                        case PNIrtArStartupMode.Advanced:
                            advancedSupportFound = true;
                            break;
                        case PNIrtArStartupMode.Legacy:
                            legacySupportFound = true;
                            break;
                    }
                }

                if (!legacySupportFound
                    && !advancedSupportFound)
                {
                    // Default is only legacy support
                    onlyLegacySupport = true;
                }
                else if (!advancedSupportFound)
                {
                    onlyLegacySupport = true;
                }
                else if (!legacySupportFound)
                {
                    onlyAdvancedSupport = true;
                }

                if (onlyAdvancedSupport && onlyLegacySupport)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        syncDomainBusinessLogic.SyncDomain,
                        ConsistencyConstants.SyncDomainNoCommonStartupMode,
                            AttributeUtilities.GetName(syncDomainBusinessLogic.SyncDomain));
                }
            }
        }

        /// <summary>
        /// Checks the distance between primary and secondary sync-masters of the sync-domain.
        /// There can be at most 2 sync-slaves between a primary and secondary sync-master.
        /// The shortest path between the two sync-masters are used determining the distance.
        /// </summary>
        /// <param name="syncDomainBusinessLogic"></param>
        private static void CheckSyncMasterDistances(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            Interface primarySyncMaster = syncDomainBusinessLogic.SyncMaster;
            if (primarySyncMaster == null)
            {
                return;
            }
            Interface secondarySyncMaster = syncDomainBusinessLogic.SecondarySyncMaster;
            if (secondarySyncMaster == null)
            {
                return;
            }

            const int MaxDepth = 3;

            // Key = ie submodule, value = hashset containing the visited status of ports
            Dictionary<Interface, HashSet<DataModel.PCLObjects.Port>> visitedPorts =
                new Dictionary<Interface, HashSet<DataModel.PCLObjects.Port>>();

            // Initialization for the search
            List<DataModel.PCLObjects.Port> primaryMasterPorts = NavigationUtilities.GetPortModules(primarySyncMaster);
            Queue<DataModel.PCLObjects.Port> portQueue = new Queue<DataModel.PCLObjects.Port>();
            Dictionary<Interface, int> interfaceDepths = new Dictionary<Interface, int>();

            interfaceDepths.Add(primarySyncMaster, 0);
            foreach (DataModel.PCLObjects.Port port in primaryMasterPorts)
            {
                portQueue.Enqueue(port);
            }

            visitedPorts.Add(primarySyncMaster, new HashSet<DataModel.PCLObjects.Port>());

            bool secondaryMasterInterfaceFound = false;
            while (portQueue.Count > 0)
            {
                DataModel.PCLObjects.Port currPort = portQueue.Dequeue();
                Interface currInterface = NavigationUtilities.GetInterfaceOfPort(currPort);

                if (currInterface == secondarySyncMaster)
                {
                    secondaryMasterInterfaceFound = true;
                    break;
                }

                // Mark the current port as visited
                if (!visitedPorts.ContainsKey(currInterface))
                {
                    visitedPorts.Add(currInterface, new HashSet<DataModel.PCLObjects.Port>());
                }

                if (!visitedPorts[currInterface].Contains(currPort))
                {
                    visitedPorts[currInterface].Add(currPort);
                }
                else
                {
                    // Port is already visited before, skip to the next port
                    continue;
                }

                if (!interfaceDepths.ContainsKey(currInterface))
                {
                    Debug.Fail("Algorithm error in CheckSyncMasterDistances");
                    return;
                }

                int currDepth = interfaceDepths[currInterface];

                // Get partner ports
                IList<DataModel.PCLObjects.Port> partnerPorts = currPort.GetPartnerPorts();
                foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                {
                    Interface partnerInterface = NavigationUtilities.GetInterfaceOfPort(partnerPort);

                    // Mark the partner port as visited
                    if (!visitedPorts.ContainsKey(partnerInterface))
                    {
                        visitedPorts.Add(partnerInterface, new HashSet<DataModel.PCLObjects.Port>());
                    }
                    visitedPorts[partnerInterface].Add(partnerPort);

                    // Get/set the partner ie depth
                    int partnerIeDepth;
                    if (interfaceDepths.ContainsKey(partnerInterface))
                    {
                        partnerIeDepth = interfaceDepths[partnerInterface];
                    }
                    else
                    {
                        partnerIeDepth = currDepth + 1;
                        interfaceDepths.Add(partnerInterface, partnerIeDepth);
                    }

                    // Check the partner depth
                    if (partnerIeDepth > MaxDepth)
                    {
                        continue;
                    }

                    // Get all ports of the interface and add them to queue
                    List<DataModel.PCLObjects.Port> partnerInterfacePorts =
                        NavigationUtilities.GetPortModules(partnerInterface);
                    foreach (DataModel.PCLObjects.Port partnerInterfacePort in partnerInterfacePorts)
                    {
                        portQueue.Enqueue(partnerInterfacePort);
                    }
                }
            }

            if (!secondaryMasterInterfaceFound)
            {
                string primarySyncMasterName = AttributeUtilities.GetSubmoduleNameWithContainer(primarySyncMaster);
                string secondarySyncMasterName = AttributeUtilities.GetSubmoduleNameWithContainer(secondarySyncMaster);
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncMasterInvalidDistance,
                    primarySyncMasterName,
                    secondarySyncMasterName);
            }
        }

        /// <summary>
        /// If the sync domain is in IRT flex mode, then no secondary syncmaster is allowed.
        /// </summary>
        private static void CheckSynchronisationRole(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            if (syncDomainBusinessLogic.HasDevicesUsingIrtFlexWithSecondarySyncMaster)
            {
                ConsistencyLogger.Log(
                            ConsistencyType.PN,
                    LogSeverity.Error,
                    syncDomainBusinessLogic.SyncDomain,
                    ConsistencyConstants.SyncDomainNoSecSyncMasterWithIrtFlex);
            }
        }

        /// <summary>
        /// Check if IRT Top and IRT Flex is mixed at sync domain
        /// </summary>
        private static void CheckIrtSettings(SyncDomainBusinessLogic syncDomainBl)
        {
            Interface syncMaster = syncDomainBl.SyncMaster;

            PclObject ioController = syncMaster?.PNIOC;

            if (ioController == null)
            {
                return;
            }

            PNIOFrameClass ioControllerAdjusted = PNIOFrameClass.None;
            PclObject ioControllerInterface = ioController.GetInterface();

            AttributeAccessCode ac = new AttributeAccessCode();
            if (ioControllerInterface != null)
            {
                Enumerated pnIoFrameClasses =
                ioControllerInterface.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    ac,
                    null);

                byte bValue = (byte?)pnIoFrameClasses?.DefaultValue ?? 0;
                ioControllerAdjusted = (PNIOFrameClass)bValue;
            }
            switch (ioControllerAdjusted)
            {
                case PNIOFrameClass.Class2Frame:
                    if (syncDomainBl.HasDevicesUsingIrtTop)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            syncDomainBl.SyncDomain,
                            ConsistencyConstants.MixedOperation_IRTTop_IRTFlex_at_SyncDomain,
                            syncDomainBl.DomainName,
                            AttributeUtilities.GetName(ioController.GetInterface().Node.Subnet));
                    }
                    break;
                case PNIOFrameClass.Class3Frame:
                    if (syncDomainBl.HasDevicesUsingIrtFlex)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN,
                            LogSeverity.Error,
                            syncDomainBl.SyncDomain,
                            ConsistencyConstants.MixedOperation_IRTTop_IRTFlex_at_SyncDomain,
                            syncDomainBl.DomainName,
                            AttributeUtilities.GetName(ioController.GetInterface().Node.Subnet));
                    }
                    break;
            }
        }

        /// <summary>
        /// This method check if in the sync domain exist a Slave whick unreachable from the Master
        /// </summary>
        /// <param name="syncDomainBl"></param>
        private static void CheckConsistencyPortTopology(SyncDomainBusinessLogic syncDomainBl)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            List<DataModel.PCLObjects.Port> visitedPorts = new List<DataModel.PCLObjects.Port>();

            Interface syncMaster = syncDomainBl.SyncMaster;

            if (syncMaster == null)
            {
                // no sync master
                return;
            }

            bool synchronized = true;

            foreach (DataModel.PCLObjects.Port ports in syncMaster.GetPortModulesSorted())
            {
                if (ports.GetPartnerPorts().Count != 0)
                {
                    DeepSearch(syncDomainBl, synchronized, ports, ports, visitedPorts);
                }
            }

            foreach (Interface interfaceSubmodule in syncDomainBl.AllParticipants)
            {
                bool isNotConnected = false;
                List<PclObject> ioConnectors = NavigationUtilities.GetIOConnectors(interfaceSubmodule);
                if (null != ioConnectors)
                {
                    foreach (PclObject ioConnector in ioConnectors)
                    {
                        bool isIrtTopSupported = false;
                        PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);

                        if (syncRole == PNIRTSyncRole.SyncSlave)
                        {
                            PclObject ioConnectorObject = interfaceSubmodule.GetIOConnector();

                            if (
                                ioConnectorObject.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.IsMasterType, ac.GetNew(), false))
                            {
                                isIrtTopSupported = ConfigUtility.IsControllerIrtTop(interfaceSubmodule);
                            }
                            else
                            {
                                DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(ioConnectorObject.GetInterface());
                                if (ioSystem != null)
                                {
                                    isIrtTopSupported =
                                        ConfigUtility.GetRtClassfromFrameDataList(interfaceSubmodule) ==
                                        PNRTClass.IrtTop;
                                }
                            }

                            if (isIrtTopSupported)
                            {
                                List<DataModel.PCLObjects.Port> lPorts =
                                    NavigationUtilities.GetPortModules(interfaceSubmodule);

                                foreach (DataModel.PCLObjects.Port localPort in lPorts)
                                {
                                    if (!visitedPorts.Contains(localPort))
                                    {
                                        isNotConnected = true;
                                    }
                                }
                            }
                        }
                    }

                    // check whether a port is in the syncdomain but is not connected at all
                    if (isNotConnected)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            syncDomainBl.SyncDomain,
                            ConsistencyConstants.SyncDomainNoConnection,
                            AttributeUtilities.GetName(syncMaster),
                            AttributeUtilities.GetName(interfaceSubmodule));
                    }
                }
            }
        }

        /// <summary>
        /// Run deep search from the current port to find all connected (reachable) and synchronized salves in this syncdomain.
        /// Check: if a synchronized device is reached from an unsynchronized device --> forbidden port interconnection
        /// A device in an other synchdomain is also considered as unsynchronized (from the perspective of the checked syncdomain).
        /// </summary>
        /// <param name="syncDomainBl">used to indentify syncdomain</param>
        /// <param name="synchronized">indicates whether the *previous* port is on a synchronized (same domain!) device</param>
        /// <param name="currentPort">current port</param>
        /// <param name="previousPort">previous port from which we got here (used for error message info)</param>
        /// <param name="visitedPort">all visited ports (also the not synchronized!)</param>
        private static void DeepSearch(SyncDomainBusinessLogic syncDomainBl,
            bool synchronized,
            DataModel.PCLObjects.Port currentPort,
            DataModel.PCLObjects.Port previousPort,
            ICollection<DataModel.PCLObjects.Port> visitedPort)
        {
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(currentPort);
            SyncDomain syncDomainOfinterfaceSubmodule = interfaceSubmodule.SyncDomain;

            if ((syncDomainOfinterfaceSubmodule != null) && (syncDomainOfinterfaceSubmodule.Equals(syncDomainBl.SyncDomain)))
            {
                bool isSyncSlave = false;
                // partner ioconnectors
                List<PclObject> newIoConnectorsPort = NavigationUtilities.GetIOConnectors(interfaceSubmodule);
                List<IPNFrameData> fullFrameList = new List<IPNFrameData>();

                if (interfaceSubmodule.PNIOC != null)
                {
                    fullFrameList = (List<IPNFrameData>)syncDomainBl.GetFramesOfInterface(interfaceSubmodule);
                }


                foreach (PclObject ioConnectorPort in newIoConnectorsPort)
                {
                    bool isMaster =
                        ioConnectorPort.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.IsMasterType,
                            new AttributeAccessCode(),
                            false);
                    bool filterConnector = false;

                    if (isMaster)
                    {
                        if (fullFrameList.Count == 0)
                        {
                            filterConnector = true;
                        }
                    }
                    else
                    {
                        if (NavigationUtilities.GetIoSystem(ioConnectorPort.GetInterface()) == null)
                        {
                            filterConnector = true;
                        }
                    }

                    if ((PNAttributeUtility.GetAdjustedSyncRole(ioConnectorPort) == PNIRTSyncRole.SyncSlave) && !filterConnector)
                    {
                        isSyncSlave = true;
                    }
                }

                if (!synchronized && isSyncSlave)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN,
                        LogSeverity.Error,
                        syncDomainBl.SyncDomain,
                        ConsistencyConstants.SyncDomainNotAllowedPortInterconnection,
                        syncDomainBl.DomainName,
                        AttributeUtilities.GetSubmoduleNameWithContainer(NavigationUtilities.GetInterfaceOfPort(previousPort)));
                }
            }

            List<DataModel.PCLObjects.Port> lPorts = NavigationUtilities.GetPortModules(interfaceSubmodule);

            foreach (DataModel.PCLObjects.Port localPort in lPorts)
            {
                IList<DataModel.PCLObjects.Port> pPorts = localPort.GetPartnerPorts();

                if (visitedPort.Contains(localPort))
                {
                    continue;
                }

                visitedPort.Add(localPort);

                if (pPorts.Count == 0)
                {
                    // no interconnection going out
                    continue;
                }

                List<PclObject> ioConnectorsPort = NavigationUtilities.GetIOConnectors(localPort.GetInterface());

                synchronized = false;

                foreach (PclObject ioConnectorPort in ioConnectorsPort)
                {
                    bool ioSystemExistsOnInterface = false;

                    PclObject ioConnectorObject = interfaceSubmodule.GetIOConnector();
                    if (ioConnectorObject != null)
                    {
                        DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(ioConnectorObject.GetInterface());
                        if (ioSystem != null)
                        {
                            ioSystemExistsOnInterface = true;
                        }
                    }

                    // synchronized if: ((IODevice on IOSystem) or Controller) AND synchronized in the same syncdomain
                    if (ioSystemExistsOnInterface &&
                        (PNAttributeUtility.GetAdjustedSyncRole(ioConnectorPort) != PNIRTSyncRole.NotSynchronized) &&
                        (syncDomainOfinterfaceSubmodule != null) && (syncDomainOfinterfaceSubmodule.Equals(syncDomainBl.SyncDomain)))
                    {
                        synchronized = true;
                    }
                }

                // recursive search from all local ports --> partner ports
                foreach (DataModel.PCLObjects.Port partnerPort in pPorts)
                {
                    if (visitedPort.Contains(partnerPort))
                    {
                        continue;
                    }

                    DeepSearch(syncDomainBl, synchronized, partnerPort, localPort, visitedPort);
                }
            }
        }

        public static bool ProcessPNPlannerResult(SyncDomain syncDomain, PNPlannerResult result)
        {
            bool executionSuccessful = true;
            switch (result.ResultType)
            {
                case PNPlannerResultType.SystemError:
                    Debug.Fail(result.Description);
                    break;
                case PNPlannerResultType.Successful:
                case PNPlannerResultType.NoMessagesToSend:
                case PNPlannerResultType.RedundantMessageFound:
                case PNPlannerResultType.PackgroupsFound:
                    // First run is not required if there are no frames (messages) to send
                    // The algorithm is not executed in this case.
                    // This is not an error, just continue.
                    // Redundant message found and packgroup found types are not errors, either.
                    break;
                case PNPlannerResultType.MoreThanOneIsland:
                case PNPlannerResultType.ToolChangerError:
                case PNPlannerResultType.AlgorithmError:
                    ConsistencyLogger.Log(ConsistencyType.PN,
                        LogSeverity.Error,
                        syncDomain,
                        result.MsgId, result.MessageParameters);
                    break;
                default:
                    Debug.Fail("Define what to do for the new PNPlanner result type");
                    break;
            }

            // Check whether the message is an internal error message.
            if ((result.ResultType == PNPlannerResultType.SystemError)
                || (result.ResultType == PNPlannerResultType.AlgorithmError))
            {
                executionSuccessful = false;
            }
            return executionSuccessful;
        }
    }
}