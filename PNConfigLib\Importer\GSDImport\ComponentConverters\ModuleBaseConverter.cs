/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleBaseConverter.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.GSDImport.Helper;

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal abstract class ModuleBaseConverter
    {
        protected abstract string GsdId
        {
            get;
        }

        protected SubslotHelper SubslotHelper
        {
            get; set;
        }

        public void DefineInfoVariables(ModuleInfo info, PclCatalogObject catalogObject)
        {
            string fwVersion;
            String typeName = String.Empty;
            string invariantTypeName = String.Empty;
            if (info != null)
            {
                // Description
                string description = Converter.GetTextFromTextId(info.InfoTextID);
                catalogObject.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Description, description);

                // OrderNumber
                catalogObject.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.OrderNumber,
                    info.OrderNumber);

                // FwVersion
                fwVersion = info.SoftwareRelease;
                catalogObject.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.FwVersion, fwVersion);

                // HwVersion
                catalogObject.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.HwVersion,
                    info.HardwareRelease);

                // TypeName
                typeName = Converter.GetTextFromTextId(info.NameTextID);
                if (string.IsNullOrEmpty(typeName))
                {
                    typeName = info.OrderNumber;
                }
                if (!string.IsNullOrEmpty(typeName))
                {
                    catalogObject.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.TypeName, typeName);
                }

                invariantTypeName = typeName;
            }
            if (!string.IsNullOrEmpty(typeName))
            {
                catalogObject.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.TypeName, GsdId);
            }

            // InvariantTypeName
            if (string.IsNullOrEmpty(invariantTypeName))
            {
                invariantTypeName = GsdId;
            }
            catalogObject.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.InvariantTypeName,
                invariantTypeName);
        }
    }
}
