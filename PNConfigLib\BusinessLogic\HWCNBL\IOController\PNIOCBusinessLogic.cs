/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIOCBusinessLogic.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.PNFrameGeneration;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.IOController
{
    /// <summary>
    /// The business logic class for PNIOc.
    /// </summary>
    internal class PNIOCBusinessLogic : PNIOConnectorBL
    {
        //########################################################################################

        #region Constants and Enums

        /// <summary>
        /// The station number constant for controllers.
        /// </summary>
        private const int m_PNStationNumberOfController = 0;

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// The constructor for PNIOcBusinessLogic.
        /// </summary>
        /// <param name="PNIOC">The IO controller.</param>
        public PNIOCBusinessLogic(PNIOC pnIOC)
        {
            PNIOC = pnIOC;
            InitBL();
        }

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// The IO controller data model object of this business logic object.
        /// </summary>
        public PNIOC PNIOC { get; }

        #endregion

        //########################################################################################

        #region Nested Clases

        #endregion

        //########################################################################################

        #region Fields

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Public Methods

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        /// <summary>
        /// Initialize the BL.
        /// Add event some handlers.
        /// </summary>
        /// <summary>
        /// Initialization method.
        /// </summary>
        private void InitBL()
        {
            //Add IsSlaveType Attribute
            PNIOC.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.IsMasterType, true);

            //Add attribute PNStationNumber
            PNIOC.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnStationNumber,
                m_PNStationNumberOfController);

            //Add PNIODeviceLocalReductionRatio, Default=1
            PNIOC.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoDeviceLocalReductionRatio, 1);

            //Add PNIrtSyncRole, Default=0(UnSync)
            PNIOC.AttributeAccess.AddAnyAttribute<byte>(
                InternalAttributeNames.PnIrtSyncRole,
                (byte)PNIRTSyncRole.NotSynchronized);

            //Add PNUpdateTimeMode, Default=0(Automatic)
            PNIOC.AttributeAccess.AddAnyAttribute<byte>(
                InternalAttributeNames.PnUpdateTimeMode,
                (byte)PNUpdateTimeMode.Automatic);

            //Add PNIOWatchdogFactor, Default=3
            PNIOC.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoWatchdogFactor, 3);

            //Add PNIOFrameClass, Default=1(RT)
            PNIOC.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoFrameClass, 1);

            //Add IoType, Default=0()
            PNIOC.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.IoType, 0);

            SetIOTypeAttribute();
        }

        /// <summary>
        /// Sets the IO type based on the operating mode of this IO controller.
        /// </summary>
        private void SetIOTypeAttribute()
        {
            DataModel.PCLObjects.IOSystem masterSystem = PNIOC.IOSystem;

            Interface controllerInterfaceSubmodule = PNIOC.ParentObject as Interface;

            PNIOOperatingModes pnIoOperatingMode = AttributeUtilities.GetOperatingMode(controllerInterfaceSubmodule);

            int ioType = (masterSystem != null) || (pnIoOperatingMode == PNIOOperatingModes.IOControllerAndIODevice)
                             ? 0x40
                             : 0x0;

            PNIOC.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.IoType, ioType);
        }

        #endregion
    }
}