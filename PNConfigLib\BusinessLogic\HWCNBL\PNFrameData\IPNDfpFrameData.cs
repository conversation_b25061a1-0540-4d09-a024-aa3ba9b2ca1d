/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPNDfpFrameData.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL
{
    /// <summary>
    /// Summary description for IPNDfpFrameData.
    /// </summary>
    public interface IPNDfpFrameData : IPNFrameData
    {
        //########################################################################################

        #region Properties

        /// <summary>
        /// Gets if subframe checksums are supported.
        /// </summary>
        bool SfCrc16 { get; }

        /// <summary>
        /// Distributed watchdog factor of the dfp frame.
        /// </summary>
        uint DistributedWdFactor { get; }

        /// <summary>
        /// Distributed restart factor of the dfp frame.
        /// </summary>
        uint DistributedRestartFactor { get; }

        /// <summary>
        /// Must be set to true for Dfp frames which have redundant frames having the reverse subframe order.
        /// This is the case for DFP + MRPD in ring combinations (with the exception of a group which contains only one
        /// device in a ring and others in a line).
        /// </summary>
        bool DfpRedundantPathLayout { get; }

        /// <summary>
        /// List of subframes sorted by subframe id.
        /// </summary>
        SortedList<int, IPNSubframeData> Subframes { get; }

        #endregion

        //########################################################################################

        #region Methods

        #endregion
    }
}