/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NodeControllerIE.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
#region Used Namespaces

using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities.Network;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Networks.Ethernet
{
    /// <summary> Node controller for Industrial Ethernet nodes. </summary>
    internal class NodeControllerIe
    {

        //########################################################################################
        #region Constants and Enums
        // Contains all constants and enums

        #endregion

        //########################################################################################
        #region Fields
        // Contains all private fields (and also public fields in case there is a really good reason to have some)
        private readonly NodeIeBusinessLogic m_NodeIeBusinessLogic;

        //#############################################################################################

        #endregion

        //########################################################################################
        #region Properties
        // Contains all properties, that are not part of an interface implementation those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################
        #region Construction/Destruction/Initialisation
        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        /// <summary> This constructor makes the node accessible </summary>
        /// <param name="bl">the IE node business logic</param>
        public NodeControllerIe(NodeIeBusinessLogic bl)
        {
            m_NodeIeBusinessLogic = bl;
        }

        #endregion

        //########################################################################################
        #region Private Implementation

        #region VALIDATION METHODS
        internal void SecondLevelValidation()
        {
            SecondLevelValidationIPAddress(m_NodeIeBusinessLogic.IPAddress);
        }

        private void SecondLevelValidationIPAddress(IPAddress ipAddress)
        {
            if (m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive 
                || m_NodeIeBusinessLogic.IsPNIoIpSuiteViaOtherPathActive 
                || !ipAddress.IsInitialized)
                return;

            IPAddressErrorCodes errorCode = IPv4Helper.CheckIPAddressRfcValidation(ipAddress, m_NodeIeBusinessLogic.IPSubnetMask);
            if ((errorCode != IPAddressErrorCodes.None) && ipAddress.IsNetworkAddress(m_NodeIeBusinessLogic.IPSubnetMask.AsInt64))
            {
                // net address as IP address is not allowed
                errorCode = IPAddressErrorCodes.HostAdresseNull;
            }
            UtilityNodeIe utility = new UtilityNodeIe(m_NodeIeBusinessLogic);
            utility.SetCheckConsistencyMessage(ipAddress, errorCode);
        }


        #endregion

        #endregion

    }
}
