/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PortElement.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;

namespace PNConfigLib.HWCNBL
{
    public struct PortElement: IEquatable<PortElement>
    {
        private int m_Nr;

        private bool m_UsingShortPreamble;

        public PortElement(int number) : this()
        {
            m_Nr = number;
        }

        public int Nr =>  m_Nr; 

        public bool UsingShortPreamble
        {
            get { return m_UsingShortPreamble; }
            set { m_UsingShortPreamble = value; }
        }

        public static bool operator ==(PortElement a, PortElement b)
        {
            return a.Nr == b.Nr;
        }

        public static bool operator !=(PortElement a, PortElement b)
        {
            return !(a == b);
        }
        #region Equality members

        public bool Equals(PortElement other)
        {
            return this == other;
        }

        /// <summary>
        /// Indicates whether this instance and a specified object are equal.
        /// </summary>
        /// <returns>
        /// true if <paramref name="obj"/> and this instance are the same type and represent the same value; otherwise, false. 
        /// </returns>
        /// <param name="obj">The object to compare with the current instance. </param>
        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj))
            {
                return false;
            }
            return obj is PortElement && Equals((PortElement)obj);
        }
        #endregion

        public override int GetHashCode()
        {
            return Nr.GetHashCode();
        }
    }
}