/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Port.cs                                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a Port submodule.
    /// </summary>
    public class Port : PclObject
    {
        /// <summary>
        /// The list containing ports that this Port is connected to.
        /// </summary>
        /// <remarks>
        /// Normally there is either no partner ports (in the case of no topology, or machine tailoring)
        /// or a single partner port. However, multiple partner ports can be set when tool changer is used.
        /// </remarks>
        private List<Port> m_PartnerPorts;

        /// <summary>
        /// The constructor of Port.
        /// </summary>
        /// <param name="portNumber">The port number of this Port.</param>
        public Port(uint portNumber)
        {
            m_PartnerPorts = new List<Port>();
            PortNumber = portNumber;
        }

        /// <summary>
        /// The constructor of Port.
        /// </summary>
        /// <param name="portNumber">The port number of this Port.</param>
        /// <param name="subSlotNumber"></param>
        public Port(uint portNumber, int subSlotNumber) : this(portNumber)
        {
            AttributeAccess.AddAnyAttribute(InternalAttributeNames.PnSubslotNumber, subSlotNumber);
        }

        /// <summary>
        /// The port number of this Port.
        /// </summary>
        public uint PortNumber { get; }

        /// <summary>
        /// Gets the list of Interface objects connected to this Port.
        /// </summary>
        /// <returns>The list of interfaces connected to this Port.</returns>
        internal override Interface GetInterface()
        {
            if (ParentObject is Interface)
            {
                return ParentObject as Interface;
            }
            return GetDevice().GetInterface();
        }

        /// <summary>
        /// Getter for partner ports.
        /// </summary>
        /// <returns>A list containing partner ports.</returns>
        public IList<Port> GetPartnerPorts()
        {
            List<Port> partners;
            if (m_PartnerPorts != null && m_PartnerPorts.Count > 0)
            {
                // Group partners by interface(parent) id and get uniques to select one of the shared device parts
                IEnumerable<IGrouping<string, Port>> groupedPartners = m_PartnerPorts.GroupBy(pp => pp.ParentObject.Id);
                partners = groupedPartners.Select(grp => grp.First()).ToList();
            }
            else
            {
                partners = m_PartnerPorts;
            }
            return partners;
        }

        /// <summary>
        /// Adds a partner port to this Port.
        /// </summary>
        /// <param name="partnerPort">The partner port to be added.</param>
        public void SetPartnerPort(Port partnerPort)
        {
            if (partnerPort == null)
            {
                throw new ArgumentNullException(nameof(partnerPort));
            }

            if (m_PartnerPorts == null)
            {
                m_PartnerPorts = new List<Port>();
            }
            m_PartnerPorts.Add(partnerPort);

            if (partnerPort.m_PartnerPorts == null)
            {
                partnerPort.m_PartnerPorts = new List<Port>();
            }
            partnerPort.m_PartnerPorts.Add(this);
        }
    }
}