/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNConstants.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;

namespace PNConfigLib.HWCNBL
{
    internal static class PNConstants
    {
        public const uint CustomInterfaceSubmoduleIdentNumber = 4294967294; // 0xFFFFFFFE

        public const uint CustomPortSubmoduleIdentNumber = 4294967295; // 0xFFFFFFFF

        public const uint CustomInterfaceSubmoduleIdentNumberForV31orUpper = 4294967278; // 0xFFFFFFEE

        public const uint CustomPortSubmoduleIdentNumberForV31orUpper = 4294967279; // 0xFFFFFFEF

        public const uint DefaultMaxFrameStartTime = 1600;

        public const uint DefaultMaxRedPeriodLength = 3875;

        public const uint DefaultMaxTimeLag = 20000;

        public const uint DefaultMinYellowTime = 9600;

        public const uint DefaultPDIRDataYellowTime = 125000;

        // Skip consistency check if MaxDeviceNettoDataLength attributes are not defined
        public const int DefaultPNIOMaxDeviceNettoDataNotDefined = 0;

        // PNIrtSignalDelayTime default values for different media (see DD-Spec. PN Base)
        public const uint DefaultPNIrtSignalDelayTimeCU = 600;

        public const uint DefaultPNIrtSignalDelayTimeFO = 18000;

        public const uint DefaultPNIrtSignalDelayTimePOF = 300;

        public const int DefaultPNMaxDevices = 4096;

        public const long DefaultSendClockFactor = 32;

        public const uint DefaultYellowSafetyMargin = 160;

        public const string IsCompile = "IsCompile";

        public const long MaxAllowedWatchdogFactor = 255;

        public const int MaxAllowedWatchdogTime = 1920; //In milliSekunden

        public const int MaxIODataLengthDeviceindependent = 1440;

        public const uint MaxPackgroupSwitches = 63;

        public const int MinFramePayloadLength = 40;

        public const int PDEVSubmoduleSubslotNumberMaximum = 0x8FFF;

        public const int PDEVSubmoduleSubslotNumberMinimum = 0x8000;

        public const uint PNIrtMaxTimeLag = 4000000;

        public const float PNTimeBase = 0.03125f;

        /// <summary>
        /// Maximum Signal Delay for relative forwarder interfaces.
        /// </summary>
        public const int RelativeForwarderMaxSignalDelay = 18000;

        // They are defined in MDD_PNIO-IRT-Basic-Port-Submodule.
        public const int PNIrtSignalMaxDelayTime = 999990;

        public const int PNIrtSignalMinDelayTime = 0;

        public const string PNIOMaxSharedRTC3Consumer = "PnIoMaxSharedRTC3Consumer";

        public const string PNIOMaxSharedRTC3Provider = "PnIoMaxSharedRTC3Provider";


        #region Isochronous OB
        public static readonly IReadOnlyList<string> IsochronousOBs = new string[] { "OB61", "OB62", "OB63", "OB64" };

        public const ushort Config2003BlockVersion = 0x102;

        public const int MaxControllerAppCycleTime = 32000;
        #endregion

        #region Generic method names and arguments

        internal const string s_InitializeDiagnosticAlarmBlock = "InitializeDiagnosticAlarmBlock";

        internal const string s_ParameterBlock = "ParameterBlock";
        internal const string PNIOMaxSharedRTC1Provider = "PnIoMaxSharedRTC1Provider";
        internal const string PNIOMaxSharedRTC1Consumer = "PnIoMaxSharedRTC1Consumer";
        internal const string PNIOMaxSharedRTCxProvider = "PnIoMaxSharedRTCxProvider";
        internal const string PNIOMaxSharedRTCxConsumer = "PnIoMaxSharedRTCxConsumer";

        #endregion

        #region IP Adresses

        public const string DefaultRouterIP = "0.0.0.0";
        public const string DefaultIPAddress = "***********";
        public const string DefaultSubnetMask = "*************";


        #endregion
    }
}