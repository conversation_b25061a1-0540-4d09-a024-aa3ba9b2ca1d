/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PDInterfaceMrpDataAdjustStruct.cs         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Text;

using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Mrp
{
    /// <summary>
    /// The data record object for PDInterfaceMrpDataAdjustStruct.
    /// </summary>
    internal class PDInterfaceMrpDataAdjustStruct : ParameterDSSubBlockStruct
    {
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// Constructor for PDInterfaceMrpDataAdjustStruct.
        /// </summary>
        public PDInterfaceMrpDataAdjustStruct()
        {
            m_Data = new byte[23];
            ParaBlockType = 0x0231;
            ParaBlockVersion = 0x0100;
            base.AddSubblock(m_Data);
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Overrides and overridables

        // Contains all public and protected overrides as well as overridables of the class
        /// <summary>
        /// Returns the contents of the data record as a byte array.
        /// </summary>
        public override byte[] ToByteArray
        {
            get
            {
                List<byte> tmpData = new List<byte>();

                tmpData.AddRange(header);
                tmpData.AddRange(m_Data);
                tmpData.AddRange(Encoding.ASCII.GetBytes(m_DomainName));
                tmpData.AddRange(new byte[m_DomainNameNumBytesAlign]);
                foreach (byte[] subblock in m_ListSubBlocks)
                {
                    tmpData.AddRange(subblock);
                }

                return tmpData.ToArray();
            }
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class
        /// <summary>
        /// Adds a subblock to this data record.
        /// </summary>
        /// <param name="param">Contents of the subblock.</param>
        public new void AddSubblock(byte[] param)
        {
            m_ListSubBlocks.Add(param);
            ParaBlockLength += param.Length;
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Nested classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Constants and enumerations

        // Contains all constants and enumerations

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        /// <summary>
        /// Contents of the data record.
        /// </summary>
        private byte[] m_Data;

        /// <summary>
        /// MRP domain name.
        /// </summary>
        private string m_DomainName = "";

        /// <summary>
        /// The list containing subblocks.
        /// </summary>
        private readonly IList<byte[]> m_ListSubBlocks = new List<byte[]>();

        /// <summary>
        /// The number of bytes used to align MRP domain name within the data block.
        /// </summary>
        private int m_DomainNameNumBytesAlign;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// MrpInstance part of the data record.
        /// </summary>
        public byte MrpInstance
        {
            set { BufferManager.Write8(m_Data, 1, value); }
            get { return (byte)BufferManager.Read8(m_Data, 1); }
        }

        /// <summary>
        /// MrpDomainUUID part of the data record.
        /// </summary>
        public byte[] MrpDomainUUID
        {
            set { BufferManager.WriteBuffer(m_Data, 2, value); }
            get { return BufferManager.ReadBuffer(m_Data, 2, 16); }
        }

        /// <summary>
        /// MrpRole part of the data record.
        /// </summary>
        public int MrpRole
        {
            set { BufferManager.Write16(m_Data, 18, value); }
            get { return BufferManager.Read16(m_Data, 18); }
        }

        /// <summary>
        /// MrpLengthDomainName part of the data record.
        /// </summary>
        private byte MrpLengthDomainName
        {
            set { BufferManager.Write8(m_Data, 22, value); }
            get { return (byte)BufferManager.Read8(m_Data, 22); }
        }

        /// <summary>
        /// MrpDomainName part of the data record.
        /// </summary>
        public string MrpDomainName
        {
            set
            {
                m_DomainName = value;
                MrpLengthDomainName = (byte)m_DomainName.Length;
                m_DomainNameNumBytesAlign = BufferManager.Alignment(ParaBlockLength + MrpLengthDomainName, 4);
                ParaBlockLength += MrpLengthDomainName + m_DomainNameNumBytesAlign;
            }
            get { return m_DomainName; }
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Delegates and events

        // Contains all delegate and events

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region I... members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Protected methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Private implementation

        // Contains the private implementation of the class

        #endregion
    }
}