﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigureManager.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ConfigureManagers;
using PNConfigLib.ConfigReader.ConfigureManagers.CentralDeviceConfigureManagers;
using PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.IOSystem;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.HWCNBL.Utilities;

using CableLength = PNConfigLib.ConfigReader.Topology.CableLength;
using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;
using PartnerPortType = PNConfigLib.ConfigReader.Topology.PartnerPortType;

#endregion

namespace PNConfigLib.PNProjectManager
{
    internal class ConfigureManager
    {
        private Project Project;

        private Configuration Configuration
        {
            get;
        }

        private ListOfNodes ListOfNodes
        {
            get;
        }

        private Topology Topology
        {
            get;
        }

        private CentralDeviceConfigureManager m_CentralDeviceConfigureManager;

        private DecentralDeviceConfigureManager m_DecentralDeviceConfigureManager;

        public ConfigureManager(
           Project project,
           Configuration configuration,
           ListOfNodes listOfNodes,
           Topology topology)
        {
            Project = project;
            Configuration = configuration;
            ListOfNodes = listOfNodes;
            Topology = topology;

            m_CentralDeviceConfigureManager = new CentralDeviceConfigureManager(Project, Configuration, ListOfNodes, Topology);
            m_DecentralDeviceConfigureManager = new DecentralDeviceConfigureManager(Project, Configuration, ListOfNodes, Topology);
        }

        public void Configure(bool topologyExists, string listOfNodesPath)
        {
            SubnetConfigureManager subnetConfigureManager = new SubnetConfigureManager(Project);
            subnetConfigureManager.Configure(Configuration.Subnet);

            m_CentralDeviceConfigureManager.Configure(Configuration.Devices.CentralDevice, listOfNodesPath);

            List<DecentralDeviceType> notAssignedDecentralDevices =
                            Configuration.Devices.DecentralDevice.Where(dd => string.IsNullOrEmpty(
                                                                  dd.DecentralDeviceInterface
                                                                      .EthernetAddresses.IOSystemRefID)
                                                              && ((dd.SharedDevice == null)
                                                                  || (dd.SharedDevice.Count == 0)
                                                                  || dd.SharedDevice.Any(
                                                                      sd => !Configuration.Devices.CentralDevice.Any(
                                                                          cd => sd.DeviceRefID == cd.DeviceRefID)))).ToList();

            if ((notAssignedDecentralDevices != null) && (notAssignedDecentralDevices.Count > 0))
            {
                IOAddressManager addressManager = new IOAddressManager();
                m_DecentralDeviceConfigureManager.Configure(notAssignedDecentralDevices.ToList(), null, ref addressManager);
            }

            if (topologyExists)
            {
                ConnectTopology();
            }
        }

        /// <summary>
        /// Sets the partner ports and other related attributes as specified in the topology.
        /// </summary>
        private void ConnectTopology()
        {
            List<IOSystem> ioSystems = Project.BusinessLogicList.Where(bl => bl is IoSystemBusinessLogic)
                .Cast<IoSystemBusinessLogic>().Select(s => s.IOSystem).ToList();

            List<Interface> allInterfaces = Project.BusinessLogicList.Where(bl => bl is IInterfaceBusinessLogic)
                .Cast<IInterfaceBusinessLogic>().Select(s => s.Interface).ToList();
            foreach (IOSystem ioSystem in ioSystems)
            {
                List<Interface> allParticipants = new List<Interface>();
                FillAllParticipants(allParticipants, ioSystem);

                foreach (TopologyTypePortInterconnection portInterconnection in Topology.PortInterconnection)
                {
                    Interface localInterface = GetLocalInterface(allParticipants, allInterfaces, portInterconnection);

                    if (localInterface == null)
                    {
                        continue;
                    }

                    TopologyTypePortInterconnectionPartnerPort topologyPartnerPort = portInterconnection.PartnerPort;
                    // Machine tailoring
                    if ((topologyPartnerPort.DeviceRefID == null)
                        && (topologyPartnerPort.InterfaceRefID == null))
                    {
                        if (portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                        {
                            ConsistencyLogger.Log(
                                ConsistencyType.XML,
                                LogSeverity.Error,
                                portInterconnection.LocalPort,
                                ConsistencyConstants.XML_NoPartnerPortAndItIsNotSetByUserProgram,
                                portInterconnection.LocalPort.InterfaceRefID,
                                portInterconnection.LocalPort.PortNumber);
                            return;
                        }
                        Port localPort = localInterface.GetPortByPortNumber(portInterconnection.LocalPort.PortNumber);
                        localPort.AttributeAccess.SetAnyAttribute<bool>(
                            InternalAttributeNames.PnIoProgrammablePeer,
                            true);
                    }
                    else
                    {
                        if (!GetPartnerPortAndLocalPort(allParticipants, allInterfaces,
                            localInterface, portInterconnection, topologyPartnerPort, out Port partnerPort, out Port localPort))
                        {
                            continue;
                        }

                        MethodData methodData = new MethodData();
                        ConfigureMethodData(methodData, partnerPort, portInterconnection);

                        localPort.BaseActions.CallMethod(methodData);
                    }
                }
            }
        }

        private void FillAllParticipants(List<Interface> allParticipants, IOSystem ioSystem)
        {
            allParticipants.Add(ioSystem.PNIOC.ParentObject.GetInterface());

            foreach (PNIOD iod in ioSystem.GetParticipants())
            {
                allParticipants.Add(iod.GetInterface());
            }
        }

        private Interface GetLocalInterface(List<Interface> allParticipants, List<Interface> allInterfaces,
                                       TopologyTypePortInterconnection portInterconnection)
        {
            Interface localInterface =
                    allParticipants.FirstOrDefault(l => l.Id == portInterconnection.LocalPort.InterfaceRefID);
            if (localInterface == null)
            {
                localInterface =
                    allInterfaces.FirstOrDefault(i => i.Id == portInterconnection.LocalPort.InterfaceRefID);
            }

            return localInterface;
        }

        /// <summary>
        /// Gets the PartnerPort and LocalPort
        /// </summary>
        /// <param name="allParticipants"></param>
        /// <param name="allInterfaces"></param>
        /// <param name="localInterface"></param>
        /// <param name="portInterconnection"></param>
        /// <param name="topologyPartnerPort"></param>
        /// <param name="partnerPort"></param>
        /// <param name="localPort"></param>
        /// <returns>Returns false if one of the out params will be set to null</returns>
        private bool GetPartnerPortAndLocalPort(List<Interface> allParticipants, List<Interface> allInterfaces,
            Interface localInterface, TopologyTypePortInterconnection portInterconnection, 
            TopologyTypePortInterconnectionPartnerPort topologyPartnerPort,
            out Port partnerPort, out Port localPort)
        {
            bool result = true;

            Interface partnerInterface =
            allParticipants.FirstOrDefault(p => p.Id == topologyPartnerPort.InterfaceRefID)
            ?? allInterfaces.FirstOrDefault(i => i.Id == topologyPartnerPort.InterfaceRefID);

            if (partnerInterface == null)
            {
                result = false;
            }

            localPort = NavigationUtilities.GetPort(
                localInterface,
                portInterconnection.LocalPort.PortNumber,
                portInterconnection.LocalPort.SlotNumberSpecified,
                portInterconnection.LocalPort.SlotNumberSpecified
                    ? (uint)portInterconnection.LocalPort.SlotNumber
                    : 0);

            if (localPort == null)
            {
                result = false;
            }

            partnerPort = NavigationUtilities.GetPort(
                partnerInterface,
                topologyPartnerPort.PortNumber,
                topologyPartnerPort.SlotNumberSpecified,
                topologyPartnerPort.SlotNumberSpecified ? (uint)topologyPartnerPort.SlotNumber : 0);

            if (partnerPort == null)
            {
                result = false;
            }

            return result;
        }

        private void ConfigureMethodData(MethodData methodData, Port partnerPort,
            TopologyTypePortInterconnection portInterconnection)
        {
            methodData.Name = CreatePortInterconnection.Name;
            methodData.Arguments.Add(CreatePortInterconnection.PartnerPort, partnerPort);

            if (portInterconnection.Properties.Item != null
                && portInterconnection.Properties.Item.GetType() == typeof(CableLength))
            {
                CableLength cableLength = (CableLength)portInterconnection.Properties.Item;
                methodData.Arguments.Add(CreatePortInterconnection.CableData, cableLength);
            }
        }
    }
}