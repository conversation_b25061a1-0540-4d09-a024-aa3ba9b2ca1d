/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: BlobCreator.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Text;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Summary description for BlobCreator.
    /// </summary>
    public class BlobCreator
    {
        private byte[] m_Data;

        private int m_Delta = 500;

        public BlobCreator()
        {
            m_Data = new byte[m_Delta];
        }

        public BlobCreator(int size)
        {
            m_Data = new byte[size];
        }

        public BlobCreator(byte[] blob)
        {
            if (blob != null)
            {
                m_Data = blob;
            }
        }

        private enum SerializationTypes
        {
            Byte = 1,

            SByte = 2,

            Int = 3,

            UInt = 4,

            Long = 5,

            Bool = 6,

            String = 32,

            List = 128,

            SByteArray = 129,

            ByteArray = 130,

            UIntArray = 131,

            IntArray = 132
        }

        public bool BigEndian { get; set; }

        private Encoding Encoder { get; }

        public int Index { get; set; }

        private bool DeSerializeBool()
        {
            return Convert.ToBoolean(DeSerializeIntExt(1), CultureInfo.InvariantCulture);
        }

        private byte DeSerializeByte()
        {
            return (byte)DeSerializeIntExt(1);
        }

        public byte[] DeSerializeByteArrayOfKnownSize(int size)
        {
            byte[] array = new byte[size];
            Array.Copy(m_Data, Index, array, 0, size);
            Index += size;

            return array;
        }

        public int DeSerializeInt()
        {
            return DeSerializeIntExt(4);
        }

        private int DeSerializeIntExt(int anzBytes)
        {
            int retVal = 0;
            if (BigEndian)
            {
                for (int i = 0; i < anzBytes; i++)
                {
                    retVal <<= 8;
                    retVal += m_Data[Index + i];
                }
            }
            else
            {
                for (int i = anzBytes - 1; i >= 0; i--)
                {
                    retVal <<= 8;
                    retVal += m_Data[Index + i];
                }
            }
            Index += anzBytes;
            return retVal;
        }

        public long DeSerializeLong()
        {
            return DeSerializeLongExt(8);
        }

        private long DeSerializeLongExt(int anzBytes)
        {
            long retVal = 0;
            if (BigEndian)
            {
                for (int i = 0; i < anzBytes; i++)
                {
                    retVal <<= 8;
                    retVal += m_Data[Index + i];
                }
            }
            else
            {
                for (int i = anzBytes - 1; i >= 0; i--)
                {
                    retVal <<= 8;
                    retVal += m_Data[Index + i];
                }
            }
            Index += anzBytes;
            return retVal;
        }

        /// <summary>
        /// Try to serialize an object with its type and its value
        /// </summary>
        public object DeSerializeObject()
        {
            byte myType = DeSerializeByte();
            switch (myType)
            {
                case (byte)SerializationTypes.List:
                    {
                        int cnt = DeSerializeInt();
                        List<object> retVal = new List<object>(cnt);
                        for (int i = 0; i < cnt; i++)
                        {
                            retVal.Add(DeSerializeObject());
                        }
                        return retVal;
                    }
                case (byte)SerializationTypes.String:
                    return DeSerializeString();
                case (byte)SerializationTypes.SByteArray:
                    {
                        int cnt = DeSerializeInt();
                        sbyte[] retVal = new sbyte[cnt];
                        for (int i = 0; i < cnt; i++)
                        {
                            retVal[i] = (sbyte)DeSerializeByte();
                        }
                        return retVal;
                    }
                case (byte)SerializationTypes.ByteArray:
                    {
                        int cnt = DeSerializeInt();
                        byte[] retVal = new byte[cnt];
                        for (int i = 0; i < cnt; i++)
                        {
                            retVal[i] = DeSerializeByte();
                        }
                        return retVal;
                    }
                case (byte)SerializationTypes.IntArray:
                    {
                        int cnt = DeSerializeInt();
                        int[] retVal = new int[cnt];
                        for (int i = 0; i < cnt; i++)
                        {
                            retVal[i] = DeSerializeInt();
                        }
                        return retVal;
                    }
                case (byte)SerializationTypes.UIntArray:
                    {
                        int cnt = DeSerializeInt();
                        uint[] retVal = new uint[cnt];
                        for (int i = 0; i < cnt; i++)
                        {
                            retVal[i] = (uint)DeSerializeInt();
                        }
                        return retVal;
                    }
                case (byte)SerializationTypes.Bool:
                    return DeSerializeBool();
                case (byte)SerializationTypes.Byte:
                    return DeSerializeByte();
                case (byte)SerializationTypes.SByte:
                    return (sbyte)DeSerializeByte();
                case (byte)SerializationTypes.Int:
                    return DeSerializeInt();
                case (byte)SerializationTypes.UInt:
                    return (uint)DeSerializeInt();
                case (byte)SerializationTypes.Long:
                    return DeSerializeLong();
                default:
                    {
                        int length = DeSerializeInt();
                        DeSerializeByteArrayOfKnownSize(length);
                        return null;
                    }
            }
        }

        public short DeSerializeShort()
        {
            return (short)DeSerializeIntExt(2);
        }

        public string DeSerializeString()
        {
            if (Encoder != null)
            {
                int length = DeSerializeInt();

                if (length == -1)
                {
                    return null;
                }

                byte[] stringEncoded = DeSerializeByteArrayOfKnownSize(length);
                return Encoder.GetString(stringEncoded);
            }
            return DeSerializeStringExt(2);
        }

        private string DeSerializeStringExt(int sizeOfLengthField)
        {
            int strLength = DeSerializeIntExt(sizeOfLengthField);

            return DeSerializeStringOfKnownSize(strLength);
        }

        private void SerializeBool(bool val)
        {
            SerializeIntExt(Convert.ToInt32(val, CultureInfo.InvariantCulture), 1);
        }

        public void SerializeByte(byte val)
        {
            SerializeIntExt(val, 1);
        }

        public void SerializeByte(byte val, int pos)
        {
            SerializeIntExt(val, 1, pos);
        }

        public void SerializeByteArray(byte[] array)
        {
            SerializeByteArrayExt(0, array);
        }

        private void SerializeByteArrayExt(int size, byte[] array)
        {
            if (array == null)
            {
                Debug.Assert(false, "BlobCreator::SerializeByteArrayExt", @"param (byte[] array) is null reference");
                return;
            }
            SerializeByteArrayExt(size, array, 0, array.Length);
        }

        private void SerializeByteArrayExt(int size, byte[] array, int begin, int count)
        {
            if (array == null)
            {
                Debug.Assert(false, "BlobCreator::SerializeByteArrayExt", @"param (byte[] array) is null reference");
                return;
            }

            if (size > 0)
            {
                SerializeIntExt(count, size);
            }

            AssertSize(count);
            Array.Copy(array, begin, m_Data, Index, count);
            Index += count;
        }

        private void SerializeInt(int val)
        {
            SerializeIntExt(val, 4);
        }

        public void SerializeIntExt(int val, int anzBytes, int pos)
        {
            AssertSize(anzBytes);

            if (BigEndian)
            {
                for (int i = anzBytes - 1; i >= 0; i--)
                {
                    m_Data[pos + i] = (byte)(val & 0xFF);
                    val >>= 8;
                }
            }
            else
            {
                for (int i = 0; i < anzBytes; i++)
                {
                    m_Data[pos++] = (byte)(val & 0xFF);
                    val >>= 8;
                }
            }
        }

        public void SerializeIntExt(int val, int anzBytes)
        {
            AssertSize(anzBytes);

            SerializeIntExt(val, anzBytes, Index);

            Index += anzBytes;
        }

        private void SerializeLong(long val)
        {
            AssertSize(8);

            SerializeLongExt(val, 8, Index);

            Index += 8;
        }

        private void SerializeLongExt(long val, int anz, int pos)
        {
            AssertSize(anz);

            if (BigEndian)
            {
                for (int i = anz - 1; i >= 0; i--)
                {
                    m_Data[pos + i] = (byte)(val & 0xFF);
                    val >>= 8;
                }
            }
            else
            {
                for (int i = 0; i < anz; i++)
                {
                    m_Data[pos++] = (byte)(val & 0xFF);
                    val >>= 8;
                }
            }
        }

        /// <summary>
        /// Try to serialize an object with its type and its value
        /// </summary>
        /// <param name="obj"></param>
        public void SerializeObject(object obj)
        {
            if (obj == null)
            {
                return;
            }

            List<object> listOfObjects = obj as List<object>;
            if (listOfObjects != null)
            {
                SerializeByte((byte)SerializationTypes.List);
                int cnt = listOfObjects.Count;
                SerializeInt(cnt);

                for (int i = 0; i < cnt; i++)
                {
                    SerializeObject(listOfObjects[i]);
                }
                return;
            }

            Type objType = obj.GetType();
            if (objType == typeof (string))
            {
                SerializeByte((byte)SerializationTypes.String);
                SerializeString(obj as string);
            }
            else if (objType == typeof (bool))
            {
                SerializeByte((byte)SerializationTypes.Bool);
                SerializeBool((bool)obj);
            }
            else if (objType == typeof (byte))
            {
                SerializeByte((byte)SerializationTypes.Byte);
                SerializeByte((byte)obj);
            }
            else if (objType == typeof (sbyte))
            {
                SerializeByte((byte)SerializationTypes.SByte);
                SerializeByte((byte)(sbyte)obj);
            }
            else if (objType == typeof (int))
            {
                SerializeByte((byte)SerializationTypes.Int);
                SerializeInt((int)obj);
            }
            else if (objType == typeof (long))
            {
                SerializeByte((byte)SerializationTypes.Long);
                SerializeLong((long)obj);
            }
            else if (objType == typeof (uint))
            {
                SerializeByte((byte)SerializationTypes.UInt);
                SerializeInt((int)(uint)obj);
            }
            else if (objType == typeof (sbyte[]))
            {
                sbyte[] objVal = obj as sbyte[];
                SerializeByte((byte)SerializationTypes.SByteArray);
                if (objVal != null)
                {
                    SerializeInt(objVal.Length);
                    for (int i = 0; i < objVal.Length; i++)
                    {
                        SerializeByte((byte)objVal[i]);
                    }
                }
            }
            else if (objType == typeof (byte[]))
            {
                byte[] objVal = obj as byte[];
                SerializeByte((byte)SerializationTypes.ByteArray);
                if (objVal != null)
                {
                    SerializeInt(objVal.Length);
                    for (int i = 0; i < objVal.Length; i++)
                    {
                        SerializeByte(objVal[i]);
                    }
                }
            }
            else if (objType == typeof (int[]))
            {
                int[] objVal = obj as int[];
                SerializeByte((byte)SerializationTypes.IntArray);
                if (objVal != null)
                {
                    SerializeInt(objVal.Length);
                    for (int i = 0; i < objVal.Length; i++)
                    {
                        SerializeInt(objVal[i]);
                    }
                }
            }
            else if (objType == typeof (uint[]))
            {
                uint[] objVal = obj as uint[];
                SerializeByte((byte)SerializationTypes.UIntArray);
                if (objVal != null)
                {
                    SerializeInt(objVal.Length);
                    for (int i = 0; i < objVal.Length; i++)
                    {
                        SerializeInt((int)objVal[i]);
                    }
                }
            }
            else
            {
                Debug.Fail("Tried to serialize unsupported type !");
            }
        }

        private void SerializeString(string str)
        {
            if (Encoder != null)
            {
                if (str == null)
                {
                    SerializeInt(-1);
                }
                else
                {
                    SerializeStringExt(str, 4);
                }
            }
            else
            {
                SerializeStringExt(str, 2);
            }
        }

        /// <summary>
        /// Serialize a string with
        /// </summary>
        /// <param name="str">String to be serialized</param>
        /// <param name="sizeOfLengthField">number of bytes used for the length preceding the string</param>
        public void SerializeStringExt(string str, int sizeOfLengthField)
        {
            // the shift left operator masks the value with max number of bits that can be shifted
            // without rotating (32 can be shifted max 31 bits)
            // Sample:
            // 126 >> 32 = 126, cause 32 & 31 == 0
            if (sizeOfLengthField < int.MaxValue)
            {
                Debug.Assert(
                    (str != null)
                    && ((sizeOfLengthField >= 4) || (sizeOfLengthField == 0)
                        || (str.Length >> (8 * sizeOfLengthField) == 0))); // Otherwise have a closer look

                if (str == null)
                {
                    return;
                }

                if (Encoder == null)
                {
                    if (sizeOfLengthField > 0)
                    {
                        SerializeIntExt(str.Length, sizeOfLengthField);
                    }

                    AssertSize(str.Length);
                    for (int i = 0; i < str.Length; i++)
                    {
                        m_Data[Index++] = (byte)str[i];
                    }
                }
                else
                {
                    byte[] stringEncoded = Encoder.GetBytes(str);

                    if (sizeOfLengthField > 0)
                    {
                        SerializeIntExt(stringEncoded.Length, sizeOfLengthField);
                    }

                    SerializeByteArray(stringEncoded);
                }
            }
            else
            {
                Debug.Fail("BlobCreator::SerializeStringExt - soizeOfLengthField was MaxInt, can this be serious?");
            }
        }

        public byte[] ToByteArray()
        {
            Array.Resize<byte>(ref m_Data, Index);
            return m_Data;
        }

        private void AssertSize(int sizeNeeded)
        {
            if (Index + sizeNeeded > m_Data.Length)
            {
                int newSize = m_Data.Length + m_Delta;
                m_Delta *= 2;
                while (Index + sizeNeeded > newSize)
                {
                    newSize += m_Delta;
                    m_Delta *= 2;
                }

                Array.Resize<byte>(ref m_Data, newSize);
            }
        }

        private string DeSerializeStringOfKnownSize(int size)
        {
            if (Encoder == null)
            {
                StringBuilder str = new StringBuilder(size);
                for (int i = 0; i < size; i++)
                {
                    str.Append((char)m_Data[Index++]);
                }
                return str.ToString();
            }
            byte[] strRaw = DeSerializeByteArrayOfKnownSize(size);
            return Encoder.GetString(strRaw);
        }
    }
}