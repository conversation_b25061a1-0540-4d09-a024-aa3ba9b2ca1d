﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: GSDI                                      :P&  */
/*                                                                           */
/*  P a c k a g e         &W: Interpreter                               :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: GsdObjectDictionary.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections;
using System.Collections.Generic;
using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class GsdObjectDictionary : IGsdObjectDictionary
    {

        private readonly IDictionary<string, GsdObject> m_Dictionary;

        public int Count => m_Dictionary.Count;

        public bool IsReadOnly => m_Dictionary.IsReadOnly;

        public ICollection<string> Keys => m_Dictionary.Keys;

        public ICollection<GsdObject> Values => m_Dictionary.Values;

        public GsdObjectDictionary(IDictionary<string, GsdObject> dictionary)
        {
            m_Dictionary = dictionary;
        }

        public IDictionary CastToNonGenericDictionary()
        {
            // this only works when the underlying dictionary also implements IDictionary
            return m_Dictionary as IDictionary;
        }

        public IEnumerator<KeyValuePair<string, GsdObject>> GetEnumerator()
        {
            return m_Dictionary.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return m_Dictionary.GetEnumerator();
        }

        public void Clear()
        {
            m_Dictionary.Clear();
        }

        public bool ContainsKey(string key)
        {
            return m_Dictionary.ContainsKey(Help.CollapseWhitespace(key));
        }

        public void Add(string key, GsdObject value)
        {
            m_Dictionary.Add(Help.CollapseWhitespace(key), value);
        }

        public bool Remove(string key)
        {
            return m_Dictionary.Remove(Help.CollapseWhitespace(key));
        }

        public bool TryGetValue(string key, out GsdObject value)
        {
            return m_Dictionary.TryGetValue(Help.CollapseWhitespace(key), out value);
        }

        public GsdObject this[string key]
        {
            get => m_Dictionary[Help.CollapseWhitespace(key)];
            set => m_Dictionary[Help.CollapseWhitespace(key)] = value;
        }

        public bool Contains(KeyValuePair<string, GsdObject> item)
        {
            throw new NotSupportedException();
        }

        public void CopyTo(KeyValuePair<string, GsdObject>[] array, int arrayIndex)
        {
            throw new NotSupportedException();
        }

        public void Add(KeyValuePair<string, GsdObject> item)
        {
            throw new NotSupportedException();
        }

        public bool Remove(KeyValuePair<string, GsdObject> item)
        {
            throw new NotSupportedException();
        }
    }
}
