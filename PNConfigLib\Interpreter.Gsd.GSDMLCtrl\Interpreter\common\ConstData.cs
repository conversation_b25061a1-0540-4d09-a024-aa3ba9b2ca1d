/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ConstData.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ConstData object is used to initialize the content of a record 
    /// data object.
    /// If the const definition does not describe the complete content of 
    /// the record data object, the undefined fields shall be set to zero. 
    /// If the ConstData object is missing, the record data object is initialized 
    /// with octets set to zero. 
    /// If more than one Const element is defined no overlapping between 
    /// the definitions are allowed. 
    /// The length of the const definition shall be less or equal to the length 
    /// of the RecordData object.
    /// </summary>
    public class ConstData :
        GsdObject,
        GSDI.IConstData
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ConstData if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ConstData()
        {
            m_ByteOffset = 0;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private uint m_ByteOffset;
        private ArrayList m_Values;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the offset in octets of the referenced data object from the 
        /// beginning of the record data object.
        /// </summary>
        public UInt32 ByteOffset => this.m_ByteOffset;

        /// <summary>
        /// Accesses the data content list of the record data object starting at 
        /// ByteOffset. Each octet is supplied as separate number value.
        /// </summary>
        public virtual Array Values =>
            this.m_Values?.ToArray();

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldByteOffset;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_ByteOffset = (uint)hash[member];

                member = Models.s_FieldValues;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Values = hash[member] as ArrayList;
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectConstData);

            // ----------------------------------------------
            bool succeeded = this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return succeeded;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldByteOffset, this.m_ByteOffset);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldValues, this.m_Values, false);

            return true;
        }

        #endregion

        //########################################################################################
        #region Object members
        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(ConstData))
                return false;

            ConstData const1 = this;
            ConstData const2 = obj as ConstData;
            if (const2 == null)
            {
                return false;
            }
            if (const1.ByteOffset != const2.ByteOffset)
                return false;

            if (const1.Values.Length != const2.Values.Length)
                return false;

            for (int i = 0; i < Values.Length; i++)
            {
                if (!const1.Values.GetValue(i).Equals(const2.Values.GetValue(i)))
                    return false;
            }

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 21;

            hash = hash * 17 + ByteOffset.GetHashCode();
            foreach (Object obj in Values)
            {
                hash = hash * 17 + obj.GetHashCode();
            }

            return hash;
        }
        #endregion
    }
}
