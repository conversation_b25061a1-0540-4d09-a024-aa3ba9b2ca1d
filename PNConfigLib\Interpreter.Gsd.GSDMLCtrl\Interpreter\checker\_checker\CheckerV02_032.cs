/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_032.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;

using GSDI;
using System.IO;
using PNConfigLib.Gsd.Interpreter;
using PNConfigLib.Gsd.Interpreter.Checker;
using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.32 and is based on GSD(ML) versions 2.31 and lower.
    ///		
    /// </summary>
    internal class CheckerV02032 : CheckerV02031
    {
        #region Properties

        protected override GSDI.ReportTypes ReportType_0X000100132 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000100161 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00010022 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00010034 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00010117 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0x00010119_2 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101221 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101318 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101319 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00011109 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000111A5 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00012205 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000122062 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0x00020008 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0x0002002E => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X0003002C => GSDI.ReportTypes.GSD_RT_Error;

        protected virtual GSDI.ReportTypes ReportType_0X0003200A => GSDI.ReportTypes.GSD_RT_Warning;

        protected override string Msg_0X000121132 => "0x00012113_3";

        protected override string Msg_0x00020025_2 => "0x00020025_3";

        protected override string Msg_0x00020026_1 => "0x00020026_8";

        protected override string Msg_0x00020026_2 => "0x00020026_9";

        protected override string DataTypesRequiresLength =>
            "(@DataType='VisibleString' or @DataType='OctetString' or @DataType='UnicodeString8' or "
            + "@DataType='61131_STRING' or @DataType='61131_WSTRING' or @DataType='OctetString_S')";

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02032;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02032;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00032000);
            Checks.Add(Constants.s_Cn_0X00032001);
            Checks.Add(Constants.s_Cn_0X00032002);
            Checks.Add(Constants.s_Cn_0X00032003);
            Checks.Add(Constants.s_Cn_0X00032004);
            Checks.Add(Constants.s_Cn_0X00032005);
            Checks.Add(Constants.s_Cn_0X00032006);
            Checks.Add(Constants.s_Cn_0X00032007);
            Checks.Add(Constants.s_Cn_0X00032008);
            Checks.Add(Constants.s_Cn_0X00032009);
            Checks.Add(Constants.s_Cn_0X0003200A);
            Checks.Add(Constants.s_Cn_0X0003200B);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                Checks.Remove(Constants.s_Cn_0X00010025);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.32.
        /// </summary>
        public CheckerV02032()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version232);
        }

        #endregion


        #region Methods

        /// <summary>
        /// Checks the MAUType against MAUTypeExtension for allowed combinations.
        /// 
        /// Note: Until GSDML V2.41 the value of attribute 'Extension' is not checked.
        /// </summary>
        /// <param></param>
        /// <returns></returns>
        protected virtual void CheckMAUTypeExtensionCombinations(XElement mauTypeItem)
        {
        }

        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("Channel", "Number");
            ElementDescriptions.Add("MAUTypeItem", "Value,+Extension");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = AttributeTokenDictionary["ApplicationClass"];
            tokens1.Add("EnergySaving");
        }

        #endregion

        protected override bool DoesDataTypeRequiresLength(string dataType)
        {
            if (dataType != Enums.s_OctetString && dataType != Enums.s_VisibleString &&
                dataType != Enums.s_UnicodeString8 && dataType != Enums.s_String61131 &&
                dataType != Enums.s_Wstring61131 && dataType != Enums.s_OctetStringS)
                return false;

            return true;
        }

        /// <summary>
        /// Checks if the DAP supports system redundancy.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>true, if @DeviceType is R2.</returns>
        protected override bool DapRedundancySupported(XElement dap)
        {
            IList<string> deviceTypeList = GetDeviceTypes(dap);

            // Find the most important redundancy type
            if (deviceTypeList.Contains(Enums.s_R1) || deviceTypeList.Contains(Enums.s_R2))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// IsPortWireless
        /// Checks for a 'PortSubmoduleItem' if it has 'MAUTypes' = 0 or
        /// if it has a single element 'MAUTypeList/MAUTypeItem'
        /// and this element has attribute 'Value' = 0 (wireless).
        /// 
        /// </summary>
        /// <returns>True, if the port is wireless.</returns>
        protected override bool IsPortWireless(XElement portSubmoduleItem)
        {
            var nl = Enumerable.ToList(portSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem).Attributes(Attributes.s_Value));

            if (nl.Count != 1)   // one or more
            {
                return false;
            }

            var valueNode = nl[0];   // required
            UInt16 value = XmlConvert.ToUInt16(valueNode.Value);
            if (value != 0)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// IsAdjustMAUTypeSupported
        /// Checks for a 'PortSubmoduleItem' if at least one of the 'MAUTypeItem' elements has the attribute AdjustSupported present and "true".
        /// 
        /// </summary>
        /// <returns>True, if a 'MAUTypeItem' with 'AdjustSupported' present found.</returns>
        protected override bool IsAdjustMauTypeSupported(XElement portSubmoduleItem)
        {
            var nl =
                portSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_AdjustSupported) != null);

            foreach (var mauTypeItem in nl)
            {
                string strAdjustSupported = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported);
                bool adjustSupported = XmlConvert.ToBoolean(strAdjustSupported);
                if (adjustSupported)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Find the error msg for a length - data type, which requires length combination.
        /// 
        /// Remark:
        /// Length != 0 is checked by schema now (VisibleString, OctetString)
        /// </summary>
        /// <param name="en"></param>
        /// <returns>The error message or empty string.</returns>
        protected override void FindMsgForDataTypesRequiresLength(XElement en)
        {
            string dataType = String.Empty;
            string strLength = String.Empty;

            var lineInfo = (IXmlLineInfo)en;

            XAttribute dataTypeAtt = en.Attribute(Attributes.s_DataType);
            if (dataTypeAtt != null)
            {
                dataType = dataTypeAtt.Value;
            }

            XAttribute lengthAtt = en.Attribute(Attributes.s_Length);
            if (lengthAtt != null)
            {
                strLength = lengthAtt.Value;
            }

            if (string.IsNullOrEmpty(strLength))
            {
                // "The attribute '@Length' must be used, if the data type is "{0}"."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001000C_1"), dataType);
                string xpath = Help.GetXPath(en);
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001000C_1");
                return;
            }

            UInt16 length = XmlConvert.ToUInt16(strLength);

            switch (dataType)
            {
                case Enums.s_String61131:
                    {
                        if (length < 3)
                        {
                            // "The attribute '@Length' must be greater or equal 3, if the data type is "61131_STRING"."
                            string msg = Help.GetMessageString("M_0x0001000C_4");
                            string xpath = Help.GetXPath(en);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001000C_4");
                        }
                    }
                    break;
                case Enums.s_Wstring61131:
                    {
                        if (length < 6 || length % 2 == 1)
                        {
                            // "The attribute '@Length' must be greater or equal 6 and an even number, if the data type is "61131_WSTRING"."
                            string msg = Help.GetMessageString("M_0x0001000C_5");
                            string xpath = Help.GetXPath(en);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001000C_5");
                        }
                    }
                    break;
                case Enums.s_OctetStringS:
                    {
                        if (length < 3 || length % 3 != 0)
                        {
                            // "The attribute '@Length' must be greater or equal 3 and '@Length' modulo 3 equal 0, if the data type is "OctetString_S"."
                            string msg = Help.GetMessageString("M_0x0001000C_6");
                            string xpath = Help.GetXPath(en);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001000C_6");
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// Find the error msg for a length - data type, which not requires length combination.
        /// </summary>
        /// <param name="en"></param>
        /// <returns>The error message or empty string.</returns>
        protected override void FindMsgForDataTypesNotRequiresLength(XElement en)
        {
            var attLength = en.Attribute(Attributes.s_Length);
            if (attLength != null)
            {
                string sLength = attLength.Value;
                if (String.IsNullOrEmpty(sLength))
                    return;

                // "The attribute 'DataItem/@Length' is only allowed on (Octet)String data types with variable size."
                string msg = Help.GetMessageString("M_0x0001000C_7");
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001000C_7");
            }
        }

        /// <summary>
        /// Check number: CN_0x00032000
        /// 
        /// If DeviceAccessPointItem/CertificationInfo/@ApplicationClass is present and contains the token "EnergySaving":
        /// (1) The attribute PROFIenergyASE_Supported shall be present and "true" at the DAP.
        /// (2) There shall be at least one Submodule with element PROFIenergy and attribute PESAP_uses_PROFIenergyASE
        ///     present and "true" which can be configured with this DAP.
        ///     
        /// This check is changed with GSDML V2.4 in the following way:
        /// 
        /// The PROFIenergy profile V1.3 relaxes the rules: The PROFIenergy ASE is not mandatory anymore.
        /// 
        /// This change of the rules is not connected with the GSDML Version, PNIO_Version or PE Profile version,
        /// because we want this relaxation in general (also affecting older versions).
        /// 
        /// So the application class "EnergySaving" is not connected with the PROFIenergy ASE anymore,
        /// but if the PROFIenergy ASE is supported, it still must be used somewhere.
        /// 
        /// The following changes are applied:
        /// (1) ApplicationClass "EnergySaving" does not require DAP / @PROFIenergyASE_Supported="true"
        /// (2) ApplicationClass "EnergySaving" does not require at least one (Virtual)SubmoduleItem
        ///     having PROFIenery with @PESAP_uses_PROFIenergyASE="true"
        /// 
        /// - but -
        /// 
        /// (3) When DAP / @PROFIenergyASE_Supported="true" then at least one (Virtual)SubmoduleItem
        ///     having PROFIenery with @PESAP_uses_PROFIenergyASE="true"
        ///  
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032000()
        {
            var nl =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            x.Attributes(Attributes.s_ProfIenergyAseSupported)
                             .FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                var xliDap = (IXmlLineInfo)dap;

                // (1)
                // Check 0x00032000_1 is removed:
                // "If 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' is present and contains the token "EnergySaving"
                // the attribute 'PROFIenergyASE_Supported' shall be present and "true" at the DAP."

                // (2) -> (3)
                // Check 0x00032000_2 is removed:
                // "If 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' is present and contains the value "EnergySaving"
                // there shall be at least one submodule with element 'PROFIenergy' and attribute 'PESAP_uses_PROFIenergyASE'
                // present and "true" which can be configured with this DAP."
                //
                // respectively changed to
                // "If 'DeviceAccessPointItem/PROFIenergyASE_Supported' is present and true
                // there shall be at least one submodule with element 'PROFIenergy' and attribute 'PESAP_uses_PROFIenergyASE'
                // present and "true" which can be configured with this DAP."


                var pEnergyAseSupported = dap.Attribute(Attributes.s_ProfIenergyAseSupported);
                bool bProfIenergyAseSupported = pEnergyAseSupported != null && XmlConvert.ToBoolean(pEnergyAseSupported.Value);

                if (!bProfIenergyAseSupported)
                    continue;

                // Check all VirtualSubmodules at the DAP
                var virtualSubmodules = dap.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                bool profIenergyAseFound = CheckAllVirtualSubmodulesAtTheDap(virtualSubmodules, false);

                profIenergyAseFound = CheckAllUsableSubmodulesAtTheDap(profIenergyAseFound, dap);



                if (!profIenergyAseFound)
                {
                    // Check all VirtualSubmodules and UseableSubmodules at the UseableModules of the DAP
                    var moduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_ModuleItemRef);
                    profIenergyAseFound = CheckAllModulesAtTheUsableModulesOfTheDap(moduleItemRefs, false);

                }


                if (profIenergyAseFound)
                {
                    continue;
                }

                GSDI.ReportTypes ReportType_0x00032000_2 = GSDI.ReportTypes.GSD_RT_MinorError;

                // Get all ApplicationClasses on DAP
                IList<string> applicationClasses = GetCombinedApplicationClasses(dap);
                // TFS #5012346: ReportType "Error" only for token "EnergySaving", else "MinorError"
                if (applicationClasses.Contains("EnergySaving"))
                    ReportType_0x00032000_2 = GSDI.ReportTypes.GSD_RT_Error;

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "If 'DeviceAccessPointItem/@PROFIenergyASE_Supported' is present and "true"
                // there shall be at least one submodule with element 'PROFIenergy' and attribute 'PESAP_uses_PROFIenergyASE'
                // present and "true" which can be configured with this DAP."
                string msg = Help.GetMessageString("M_0x00032000_2");
                string xpath = Help.GetXPath(dap);
                Store.CreateAndAnnounceReport(ReportType_0x00032000_2, xliDap.LineNumber, xliDap.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00032000_2");
            }




            return true;
        }

        private bool CheckAllModulesAtTheUsableModulesOfTheDap(IEnumerable<XElement> moduleItemRefs, bool profIenergyAseFound)
        {
            foreach (var moduleItemRef in moduleItemRefs)
            {
                string moduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                if (null == module)
                    continue;

                // Check all VirtualSubmodules at the Module usable at the DAP
                IEnumerable<XElement> virtualSubmodules = module.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                profIenergyAseFound =
                    CheckAllVirtualSubmodulesAtTheModuleUsableAtTheDap(virtualSubmodules, profIenergyAseFound);

                if (profIenergyAseFound)
                {
                    continue;
                }

                {
                    // Check all UseableSubmodules at the Module usable at the DAP
                    if (!ModuleRefToSubmoduleDictionary.TryGetValue(moduleItemRef, out IList<XElement> submodules))
                    {
                        continue;
                    }

                    profIenergyAseFound = CheckAllUsableSubmodulesAtTheModuleUsableAtTheDap(submodules, false);
                }
            }
            return profIenergyAseFound;
        }

        private bool CheckAllUsableSubmodulesAtTheModuleUsableAtTheDap(IList<XElement> submodules, bool profIenergyAseFound)
        {
            foreach (XElement submodule in submodules)
            {
                var usesProfIenergyAse = submodule.Elements(NamespaceGsdDef + Elements.s_ProfIenergy)
                    .Attributes(Attributes.s_PesaPusesProfIenergyAse).FirstOrDefault();
                if (usesProfIenergyAse == null)
                {
                    continue;
                }

                if (XmlConvert.ToBoolean(usesProfIenergyAse.Value))
                    profIenergyAseFound = true;
                break;
            }
            return profIenergyAseFound;
        }

        private bool CheckAllVirtualSubmodulesAtTheModuleUsableAtTheDap(IEnumerable<XElement> virtualSubmodules, bool profIenergyAseFound)
        {
            foreach (var virtualSubmodule in virtualSubmodules)
            {
                var usesProfIenergyAse = virtualSubmodule.Elements(NamespaceGsdDef + Elements.s_ProfIenergy)
                    .Attributes(Attributes.s_PesaPusesProfIenergyAse).FirstOrDefault();
                if (usesProfIenergyAse == null)
                {
                    continue;
                }

                if (XmlConvert.ToBoolean(usesProfIenergyAse.Value))
                    profIenergyAseFound = true;
                break;
            }

            return profIenergyAseFound;
        }

        private bool CheckAllUsableSubmodulesAtTheDap(bool profIenergyAseFound, XContainer dap)
        {
            if (profIenergyAseFound)
            {
                return true;
            }

            // Check all UseableSubmodules at the DAP
            var submoduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                string submoduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                if (null == submodule)
                    continue;

                var usesProfIenergyAse = submodule.Elements(NamespaceGsdDef + Elements.s_ProfIenergy)
                    .Attributes(Attributes.s_PesaPusesProfIenergyAse).FirstOrDefault();
                if (usesProfIenergyAse == null)
                {
                    continue;
                }

                if (XmlConvert.ToBoolean(usesProfIenergyAse.Value))
                    profIenergyAseFound = true;
                break;
            }

            return profIenergyAseFound;
        }

        private bool CheckAllVirtualSubmodulesAtTheDap(IEnumerable<XElement> virtualSubmodules, bool profIenergyAseFound)
        {
            foreach (var virtualSubmodule in virtualSubmodules)
            {
                var usesProfIenergyAse = virtualSubmodule.Elements(NamespaceGsdDef + Elements.s_ProfIenergy)
                    .Attributes(Attributes.s_PesaPusesProfIenergyAse).FirstOrDefault();
                if (usesProfIenergyAse == null)
                {
                    continue;
                }

                if (XmlConvert.ToBoolean(usesProfIenergyAse.Value))
                    profIenergyAseFound = true;
                break;
            }

            return profIenergyAseFound;
        }

        /// <summary>
        /// FindDataItemBits
        /// Here lists with all available DataItem and all available BitDataItem elements for Input and Output are generated.
        ///
        /// </summary>
        /// <returns>List with IOData/Input/DataItem or IOData/Output/DataItem elements divided in single bits.
        ///          For each bit the corresponding DataItem or BitDataItem is contained.</returns>
        protected virtual IList<XElement> FindDataItemBits(XElement inOrOut, out IList<XElement> availableDataItems, out SortedList<UInt16, XElement> availableBitDataItems)
        {
            IList<XElement> bitList = new List<XElement>();
            availableDataItems = new List<XElement>();
            availableBitDataItems = new SortedList<UInt16, XElement>();

            if (inOrOut == null)
            {
                return bitList;
            }
            var nl = inOrOut.Elements(NamespaceGsdDef + Elements.s_DataItem);
            UInt16 dataTypeBitLengthSum = 0;
            foreach (var dataItem in nl)
            {
                // All BitDataItems under DataItem, if available, must be placed according to its BitOffset.
                // For that the sorted list "bitDataItems" will be provided.
                uint dataTypeBitLength = GetDataTypeBitLength(dataItem);



                // The PROFINET specification mandates, that the Input / Output data of each submodule cannot be split across multiple transport data units.
                // This limits both Input and Output of each submodule to the payload size of a IOCR, which is 1440 byte (including the IOxS byte).
                // So, when dataTypeBitLength (which is an uint) exceeds 1440 * 8 (which is well below 64 KiB), this check can just be aborted without additional message.
                // The exceeded length is already checked and reported elsewhere.
                if (dataTypeBitLength > 1440 * 8)
                    return bitList;

                var nl1 = dataItem.Elements(NamespaceGsdDef + Elements.s_BitDataItem);
                SortedList<UInt32, XElement> bitDataItems = new();
                foreach (var bitDataItem in nl1)
                {
                    UInt16 bitOffset = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(bitDataItem, Attributes.s_BitOffset));
                    if (bitOffset >= dataTypeBitLength) // The error for bitOffset>=dataTypeBitLength will be raised at another check.
                    {
                        continue;
                    }

                    // Transform Big Endian in a lineare list
                    UInt16 byteOffset = (UInt16)((UInt16)(dataTypeBitLength / 8) - (UInt16)(bitOffset / 8) - 1);
                    bitOffset = (UInt16)((UInt16)(byteOffset * 8) + (UInt16)(bitOffset % 8));
                    bitDataItems.Add(bitOffset, bitDataItem);
                }

                // "bitList" shows a list for all bits under IOData/Input/DataItem.
                // For each bit the corresponding BitDataItem or if not available, the corresponding DataItem, must be referenced.
                for (UInt32 i = 0; i < dataTypeBitLength; i++)
                {
                    if (bitDataItems.TryGetValue(i, out XElement bitDataItem))
                    {
                        bitList.Add(bitDataItem);
                        UInt16 bitOffset = (UInt16)i;
                        availableBitDataItems.Add((UInt16)(dataTypeBitLengthSum + bitOffset), bitDataItem);
                    }
                    else
                    {
                        bitList.Add(dataItem);
                    }
                }

                if (!availableDataItems.Contains(dataItem))
                    availableDataItems.Add(dataItem);

                dataTypeBitLengthSum += (UInt16)dataTypeBitLength;
            }


            return bitList;
        }

        private static uint GetDataTypeBitLength(XElement dataItem)
        {
            uint dataTypeBitLength = Help.GetBitLengthFromDataItemType(Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType));
            var attLength = dataItem.Attribute(Attributes.s_Length);
            if (attLength == null)
            {
                return dataTypeBitLength;
            }
            string sLength = attLength.Value;
            if (!String.IsNullOrEmpty(sLength))
            {
                dataTypeBitLength = XmlConvert.ToUInt16(sLength) * (uint)8;
            }
            return dataTypeBitLength;
        }

        /// <summary>
        /// FindChannels
        /// Here sorted lists with [@BitOffset, @BitLength] for Input and Output are generated.
        ///
        /// The following has to be checked:
        /// 
        /// (1) The parts of the IO data referenced by Data / @BitOffset + @BitLength and Quality / @BitOffset + length
        ///     deduced from @Format shall not overlap (else error).
        ///     Be careful: When attribute OppositeDirection is "true", @BitOffset points to the other side!
        ///     That means it points from Input to Output or the other way round.
        /// 
        /// </summary>
        /// <returns>Void.</returns>
        protected virtual void FindChannels(XElement inOrOut, ref SortedList<UInt16, UInt16> channels1, ref SortedList<UInt16, UInt16> channels2)
        {
            //XmlNodeList nl1 = inOrOut.SelectNodes("./gsddef:Channel/gsddef:Data", Nsmgr);
            var nl1 = inOrOut.Descendants(NamespaceGsdDef + Elements.s_Data).Where(x => x.Parent.Name.LocalName == Elements.s_Channel);
            foreach (var data in nl1)
            {
                UInt16 bitOffset = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(data, Attributes.s_BitOffset));
                UInt16 bitLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(data, Attributes.s_BitLength));

                var xliData = (IXmlLineInfo)data;

                // (1)
                // Channels shall not overlap.
                // The BitOffset must be not already contained.
                if (channels1.ContainsKey(bitOffset))
                {
                    CreateReport0x00032001_1(data, bitOffset, xliData);
                }
                else
                {
                    channels1.Add(bitOffset, bitLength);
                    int index = channels1.IndexOfKey(bitOffset);
                    // (1)
                    // Channels shall not overlap.
                    // The referenced IO data before must not reach the area of BitOffset.
                    CreateReport0x00032001_2(channels1, index, bitOffset, data, xliData);
                    // (1)
                    // Channels shall not overlap.
                    // The referenced IO data area after must not be reached by BitOffset + BitLength.
                    CreateReport0x00032001_3(channels1, index, bitOffset, bitLength, data, xliData);

                }
            }

            var nl2 = inOrOut.Descendants(NamespaceGsdDef + Elements.s_Quality);
            foreach (var quality in nl2)
            {
                UInt16 bitOffset = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(quality, Attributes.s_BitOffset));
                UInt16 bitLength = Help.GetBitLengthFromQualityFormat(Help.GetAttributeValueFromXElement(quality, Attributes.s_Format));

                var xliQuality = (IXmlLineInfo)quality;

                bool oppositeDirection = false;
                string strOppositeDirection = Help.GetAttributeValueFromXElement(quality, Attributes.s_OppositeDirection);
                if (!string.IsNullOrEmpty(strOppositeDirection))
                    oppositeDirection = XmlConvert.ToBoolean(strOppositeDirection);

                if (!oppositeDirection)
                {
                    // (1)
                    // Channels shall not overlap.
                    // The BitOffset must be not already contained.
                    if (channels1.ContainsKey(bitOffset))
                    {
                        CreateReport0x00032001_4(quality, bitOffset, xliQuality);
                    }
                    else
                    {
                        channels1.Add(bitOffset, bitLength);
                        int index = channels1.IndexOfKey(bitOffset);
                        // (1)
                        // Channels shall not overlap.
                        // The referenced IO data before must not reach the area of BitOffset.
                        CreateReport0x00032001_5(channels1, index, bitOffset, quality, xliQuality);
                        // (1)
                        // Channels shall not overlap.
                        // The referenced IO data area after must not be reached by BitOffset + BitLength.
                        CreateReport0x00032001_6(channels1, index, bitOffset, bitLength, quality, xliQuality);

                    }
                }
                else
                {
                    // (1)
                    // Channels shall not overlap.
                    // The BitOffset must be not already contained.
                    if (channels2.ContainsKey(bitOffset))
                    {
                        CreateReport0x00032001_4(quality, bitOffset, xliQuality);
                    }
                    else
                    {
                        channels2.Add(bitOffset, bitLength);
                        int index = channels2.IndexOfKey(bitOffset);
                        // (1)
                        // Channels shall not overlap.
                        // The referenced IO data before must not reach the area of BitOffset.
                        CreateReport0x00032001_5(channels2, index, bitOffset, quality, xliQuality);
                        // (1)
                        // Channels shall not overlap.
                        // The referenced IO data area after must not be reached by BitOffset + BitLength.
                        CreateReport0x00032001_6(channels2, index, bitOffset, bitLength, quality, xliQuality);
                    }
                }
            }
        }

        private void CreateReport0x00032001_6(
            SortedList<ushort, ushort> channels1,
            int index,
            ushort bitOffset,
            ushort bitLength,
            XObject quality,
            IXmlLineInfo xliQuality)
        {
            if (index >= channels1.Count - 1
                || bitOffset + bitLength <= channels1.Keys[index + 1])
            {
                return;
            }

            if (!Help.CheckSchemaVersion(quality, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap.
            //  The referenced IO data area after must not be reached by the area starting with 'BitOffset' = {0}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_6"), bitOffset);
            string xpath = Help.GetXPath(quality);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliQuality.LineNumber,
                xliQuality.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_6");
        }

        private void CreateReport0x00032001_5(
            SortedList<ushort, ushort> channels1,
            int index,
            ushort bitOffset,
            XObject quality,
            IXmlLineInfo xliQuality)
        {
            if (index <= 0
                || channels1.Keys[index - 1] + channels1.Values[index - 1] <= bitOffset)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(quality, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap.
            //  The referenced IO data area before must not reach the area starting with 'BitOffset' = {0}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_5"), bitOffset);
            string xpath = Help.GetXPath(quality);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliQuality.LineNumber,
                xliQuality.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_5");
        }


        private void CreateReport0x00032001_4(XObject quality, ushort bitOffset, IXmlLineInfo xliQuality)
        {
            if (!Help.CheckSchemaVersion(quality, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap. 'BitOffset' = {0} is defined twice."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_4"), bitOffset);
            string xpath = Help.GetXPath(quality);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliQuality.LineNumber,
                xliQuality.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_4");
        }

        private void CreateReport0x00032001_3(
            SortedList<ushort, ushort> channels1,
            int index,
            ushort bitOffset,
            ushort bitLength,
            XObject data,
            IXmlLineInfo xliData)
        {
            if (index >= channels1.Count - 1
                || bitOffset + bitLength <= channels1.Keys[index + 1])
            {
                return;
            }

            if (!Help.CheckSchemaVersion(data, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap.
            //  The referenced IO data area after must not be reached by the area starting with 'BitOffset' = {0}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_3"), bitOffset);
            string xpath = Help.GetXPath(data);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliData.LineNumber,
                xliData.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_3");
        }

        private void CreateReport0x00032001_2(
            SortedList<ushort, ushort> channels1,
            int index,
            ushort bitOffset,
            XObject data,
            IXmlLineInfo xliData)
        {
            if (index <= 0
                || channels1.Keys[index - 1] + channels1.Values[index - 1] <= bitOffset)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(data, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap.
            //  The referenced IO data area before must not reach the area starting with 'BitOffset' = {0}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_2"), bitOffset);
            string xpath = Help.GetXPath(data);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliData.LineNumber,
                xliData.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_2");
        }

        private void CreateReport0x00032001_1(XObject data, ushort bitOffset, IXmlLineInfo xliData)
        {
            if (!Help.CheckSchemaVersion(data, SupportedGsdmlVersion))
            {
                return;
            }

            // "Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap. 'BitOffset' = {0} is defined twice."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032001_1"), bitOffset);
            string xpath = Help.GetXPath(data);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xliData.LineNumber,
                xliData.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_1");
        }
        /// <summary>
        /// Check number: CN_0x00032001_1
        /// Here the check is done for 'IOData/Input' and 'IOData/Output'.
        ///
        /// The following has to be checked:
        /// 
        /// (1) Channel data and/or channel quality must reference existing IO data.
        /// (2) A reference which overlaps from one DataItem to next DataItems is only allowed when the width covers
        ///     the complete DataItems (else error). Either a part of one DataItem is referenced or
        ///     one or more complete DataItems.
        /// (3) When Channel is used, all IO data must be referenced (else error):
        ///     All DataItems except for those with DataType F_MessageTrailer4Byte or F_MessageTrailer5Byte
        ///     must be at least partly used.
        /// (4) On DataItems with BitDataItems, all BitDataItems must be used.
        /// (5) Channel data and/or channel quality which is only one bit wide and which is in a DataItem of
        ///     DataType UnsignedXX or OctetString must be described by a BitDataItem (else warning).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000320011(XElement inOrOut, SortedList<UInt16, UInt16> channels, IList<XElement> refToDataItems,
                                                   IList<XElement> availableDataItems, SortedList<UInt16, XElement> availableBitDataItems, IList<XElement> dataRefs)
        {
            IList<XElement> usedDataItems = new List<XElement>();

            // Check each entry.
            UInt16 dataItemsCount = (UInt16)refToDataItems.Count;
            foreach (UInt16 bitOffset in channels.Keys)
            {
                // (1)
                // Check, that channel data and/or channel quality does not point outside the available IO data
                var dataOrQualityList = inOrOut.Descendants()
                        .Where(x => x.Attribute(Attributes.s_BitOffset) != null);
                if (CreateReport0x00032001_7(inOrOut, channels, bitOffset, dataItemsCount, dataOrQualityList, out ushort bitLength))
                {
                    continue;
                }



                // (2)
                // Check if either a part of one DataItem is referenced or
                // one or more complete DataItems.
                XElement dataItemStart = GetDataItemStart(refToDataItems, bitOffset);
                XElement dataItemEnd = GetDataItemEnd(refToDataItems, bitOffset, bitLength);
                CreateReport0x00032001_8(inOrOut, refToDataItems, dataItemStart, dataItemEnd, bitOffset, dataOrQualityList, dataItemsCount, bitLength);




                // (5)
                // Channel data and/or channel quality which is only one bit wide and which is in a DataItem of
                // DataType UnsignedXX, OctetString or V2 must be described by a BitDataItem (else warning).
                CreateReport0x00032001_9(refToDataItems, bitLength, bitOffset);




                if (!usedDataItems.Contains(dataItemStart))
                    usedDataItems.Add(dataItemStart);
                if (!usedDataItems.Contains(dataItemEnd))
                    usedDataItems.Add(dataItemEnd);
            }
            // (3) When Channel is used
            ProcessUnusedDataItems(channels, availableDataItems, dataRefs, usedDataItems);

            // (4)
            // On DataItems with BitDataItems, all BitDataItems must be used.
            CreateReport0x00032001_c(channels, availableBitDataItems, dataRefs);

            return true;

        }

        private void ProcessUnusedDataItems(SortedList<ushort, ushort> channels, IList<XElement> availableDataItems, IList<XElement> dataRefs, IList<XElement> usedDataItems)
        {
            if (channels.Count == 0)
            {
                return;
            }

            foreach (var availableDataItem in availableDataItems)
            {
                var xli = (IXmlLineInfo)availableDataItem;

                string availableDataItemId = availableDataItem.Attribute(Attributes.ID)?.Value;
                if (!string.IsNullOrEmpty(availableDataItemId) &&
                    dataRefs.Any(x => x.Attribute(Attributes.s_DataTarget)?.Value == availableDataItemId))
                {
                    continue;
                }


                // (3)
                // All DataItems except for those with DataType F_MessageTrailer4Byte or F_MessageTrailer5Byte
                // must be at least partly used by channel data and/or channel quality.
                if (Help.GetAttributeValueFromXElement(availableDataItem, Attributes.s_DataType) != Enums.s_FMessageTrailer4Byte &&
                    Help.GetAttributeValueFromXElement(availableDataItem, Attributes.s_DataType) != Enums.s_FMessageTrailer5Byte &&
                    !usedDataItems.Contains(availableDataItem))
                {
                    CreateReport0x00032001_a(availableDataItem, xli);
                }
                // (3)
                // All DataItems with DataType F_MessageTrailer4Byte or F_MessageTrailer5Byte
                // must not be referenced by channel data and/or channel quality.
                else if ((Help.GetAttributeValueFromXElement(availableDataItem, Attributes.s_DataType) == Enums.s_FMessageTrailer4Byte ||
                          Help.GetAttributeValueFromXElement(availableDataItem, Attributes.s_DataType) == Enums.s_FMessageTrailer5Byte) &&
                         usedDataItems.Contains(availableDataItem))
                {
                    CreateReport0x00032001_b(availableDataItem, xli);
                }
            }
        }

        private static XElement GetDataItemEnd(IList<XElement> refToDataItems, ushort bitOffset, ushort bitLength)
        {
            var dataItemEnd = refToDataItems[bitOffset + bitLength - 1];
            if (dataItemEnd.Name.LocalName == Elements.s_BitDataItem)
                dataItemEnd = dataItemEnd.Parent;
            return dataItemEnd;
        }

        private static XElement GetDataItemStart(IList<XElement> refToDataItems, ushort bitOffset)
        {
            var dataItemStart = refToDataItems[bitOffset];
            if (dataItemStart.Name.LocalName == Elements.s_BitDataItem)
                dataItemStart = dataItemStart.Parent;
            return dataItemStart;
        }

        private void CreateReport0x00032001_c(IReadOnlyDictionary<ushort, ushort> channels, SortedList<ushort, XElement> availableBitDataItems, IList<XElement> dataRefs)
        {
            foreach (var availableBitDataItem in availableBitDataItems)
            {
                UInt16 bitOffset = availableBitDataItem.Key;
                if (channels.ContainsKey(bitOffset))
                {
                    continue;
                }
                string availableBitDataItemId = availableBitDataItem.Value.Attribute(Attributes.ID)?.Value;
                if (!string.IsNullOrEmpty(availableBitDataItemId) &&
                    dataRefs.Any(x => x.Attribute(Attributes.s_DataTarget)?.Value == availableBitDataItemId))
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(availableBitDataItem.Value, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "On 'DataItem' elements with 'BitDataItem' elements, all 'BitDataItem' elements must be used."
                string msg = Help.GetMessageString("M_0x00032001_c");
                string xpath = Help.GetXPath(availableBitDataItem.Value);
                var xli = (IXmlLineInfo)availableBitDataItem.Value;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00032001_c");
            }
        }

        private void CreateReport0x00032001_b(XObject availableDataItem, IXmlLineInfo xli)
        {
            if (!Help.CheckSchemaVersion(availableDataItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "All 'DataItem' elements with 'DataType' "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte"
            // must not be referenced by 'Channel/Data' and/or 'Channel/Quality'."
            string msg = Help.GetMessageString("M_0x00032001_b");
            string xpath = Help.GetXPath(availableDataItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_b");
        }

        private void CreateReport0x00032001_a(XObject availableDataItem, IXmlLineInfo xli)
        {
            if (!Help.CheckSchemaVersion(availableDataItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "All 'DataItem' elements except for those with 'DataType' "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte"
            // must be at least partly used by 'Channel/Data' and/or 'Channel/Quality'."
            string msg = Help.GetMessageString("M_0x00032001_a");
            string xpath = Help.GetXPath(availableDataItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_a");
        }
        private void CreateReport0x00032001_9(IList<XElement> refToDataItems, ushort bitLength, ushort bitOffset)
        {
            if (bitLength != 1)
            {
                return;
            }

            var bitDataItem = refToDataItems[bitOffset];
            // refToDataItems contains a reference to a DataItem or a BitDataItem for each available bit.
            // When it is not a BitDataItem, it must be a DataItem and has a DataType.
            if (bitDataItem.Name.LocalName == Elements.s_BitDataItem
        || !Help.IsDataTypeUsableAsBitDataItem(
           Help.GetAttributeValueFromXElement(bitDataItem, Attributes.s_DataType)))
            {
                return;
            }
            if (!Help.CheckSchemaVersion(bitDataItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "'Channel/Data' or 'Channel/Quality' which is only one bit wide and which is in a 'DataItem' of 'DataType'
            //  "UnsignedXX", "OctetString" or "V2" must be described by a 'BitDataItem'."
            string msg = Help.GetMessageString("M_0x00032001_9");
            string xpath = Help.GetXPath(bitDataItem);
            var xli = (IXmlLineInfo)bitDataItem;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032001_9");
        }
        private void CreateReport0x00032001_8(
                   XElement inOrOut,
                   IList<XElement> refToDataItems,
                   XElement dataItemStart,
                   XElement dataItemEnd,
                   ushort bitOffset,
                   IEnumerable<XElement> dataOrQualityList,
                   ushort dataItemsCount,
                   ushort bitLength)
        {
            if (dataItemStart == dataItemEnd)
            {
                return;
            }
            // The referenced area applies for more than one DataItem.
            if (bitOffset > 0)
            {
                // A complete DataItem must be referenced.
                // That means, that the bit before this reference must point to another DataItem.
                var dataItemBeforeStart = refToDataItems[bitOffset - 1];
                if (dataItemBeforeStart.Name.LocalName == Elements.s_BitDataItem)
                    dataItemBeforeStart = dataItemBeforeStart.Parent;
                if (dataItemBeforeStart == dataItemStart)
                {
                    XElement dataOrQuality = GetDataOrQuality(inOrOut, bitOffset, dataOrQualityList);


                    if (Help.CheckSchemaVersion(dataOrQuality, SupportedGsdmlVersion))
                    {
                        // "'Channel/Data' or 'Channel/Quality' must point either to a part of one DataItem or to one or more complete DataItems."
                        string msg = Help.GetMessageString("M_0x00032001_8");
                        string xpath = Help.GetXPath(dataOrQuality);
                        var xliDataOrQuality = (IXmlLineInfo)dataOrQuality;
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xliDataOrQuality.LineNumber,
                            xliDataOrQuality.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.KeyKeyref,
                            "0x00032001_8");
                    }
                }
            }

            if (bitOffset >= dataItemsCount - 1
          || (bitOffset + bitLength) >= refToDataItems.Count)
            {
                return;
            }
            {
                // A complete DataItem must be referenced.
                // That means, that the bit after this reference must point to another DataItem.
                XElement dataItemAfterEnd = refToDataItems[bitOffset + bitLength];
                if (dataItemAfterEnd.Name.LocalName == Elements.s_BitDataItem)
                    dataItemAfterEnd = dataItemAfterEnd.Parent;
                if (dataItemAfterEnd != dataItemEnd)
                {
                    return;
                }
                XElement dataOrQuality = GetDataOrQuality(inOrOut, bitOffset, dataOrQualityList);


                if (!Help.CheckSchemaVersion(dataOrQuality, SupportedGsdmlVersion))
                {
                    return;
                }
                // "'Channel/Data' or 'Channel/Quality' must point either to a part of one DataItem or to one or more complete DataItems."
                string msg = Help.GetMessageString("M_0x00032001_8");
                string xpath = Help.GetXPath(dataOrQuality);
                var xliDataOrQuality = (IXmlLineInfo)dataOrQuality;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xliDataOrQuality.LineNumber,
                    xliDataOrQuality.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00032001_8");
            }
        }
        private static XElement GetDataOrQuality(XElement inOrOut, ushort bitOffset, IEnumerable<XElement> dataOrQualityList)
        {
            var dataOrQuality = inOrOut;
            foreach (var dataOrQual in dataOrQualityList)
            {
                if (XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(dataOrQual, Attributes.s_BitOffset)) == bitOffset)
                {
                    dataOrQuality = dataOrQual;
                }
            }
            return dataOrQuality;
        }

        private bool CreateReport0x00032001_7(
             XElement inOrOut,
             IReadOnlyDictionary<ushort, ushort> channels,
             ushort bitOffset,
             ushort dataItemsCount,
             IEnumerable<XElement> dataOrQualityList,
             out ushort bitLength)
        {
            bitLength = 0;
            if (bitOffset >= dataItemsCount)
            {
                var dataOrQuality = inOrOut;
                foreach (var dataOrQual in dataOrQualityList)
                {
                    if (XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(dataOrQual, Attributes.s_BitOffset)) == bitOffset)
                        dataOrQuality = dataOrQual;
                }

                if (!Help.CheckSchemaVersion(dataOrQuality, SupportedGsdmlVersion))
                {
                    return true;
                }

                // "'Channel/Data' or 'Channel/Quality' points outside the available IO data."
                string msg = Help.GetMessageString("M_0x00032001_7");
                string xpath = Help.GetXPath(dataOrQuality);
                var xliDataOrQuality = (IXmlLineInfo)dataOrQuality;

                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xliDataOrQuality.LineNumber,
                    xliDataOrQuality.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00032001_7");

                return true;
            }

            bitLength = channels[bitOffset];

            // (1)
            // Check, that channel data and/or channel quality does not point outside the available IO data
            if (bitOffset + bitLength <= dataItemsCount)
            {
                return false;
            }

            {
                var dataOrQuality = inOrOut;
                foreach (var dataOrQual in dataOrQualityList)
                {
                    if (XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(dataOrQual, Attributes.s_BitOffset)) == bitOffset)
                        dataOrQuality = dataOrQual;
                }

                if (!Help.CheckSchemaVersion(dataOrQuality, SupportedGsdmlVersion))
                {
                    return true;
                }

                // "'Channel/Data' or 'Channel/Quality' points outside the available IO data."
                string msg = Help.GetMessageString("M_0x00032001_7");
                string xpath = Help.GetXPath(dataOrQuality);
                var xliDataOrQuality = (IXmlLineInfo)dataOrQuality;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xliDataOrQuality.LineNumber,
                    xliDataOrQuality.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00032001_7");

                return true;
            }

        }

        /// <summary>
        /// Check number: CN_0x00032001
        /// The IOData consists of DataItems with optional BitDataItems.
        /// There is the option to define how the IO data are distributed to Channels.
        ///
        /// All checks must be done for 'IOData/Input' and 'IOData/Output' and are called from here
        /// together with some preparation work. The real check is done in FindChannels and CheckCn_0X00032001_1().
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032001()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoData);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var ioData in nl)
            {
                SortedList<UInt16, UInt16> inputChannels = new SortedList<UInt16, UInt16>();
                SortedList<UInt16, UInt16> outputChannels = new SortedList<UInt16, UInt16>();

                // Check if IOData is distributed to channels
                var channelInput =
                    ioData
                        .Descendants(NamespaceGsdDef + Elements.s_Channel).FirstOrDefault(x => x.Parent.Name.LocalName == Elements.s_Input);
                var channelOutput =
                    ioData
                        .Descendants(NamespaceGsdDef + Elements.s_Channel).FirstOrDefault(x => x.Parent.Name.LocalName == Elements.s_Output);

                if (channelInput == null && channelOutput == null)
                {
                    continue;
                }

                var input = ioData.Element(NamespaceGsdDef + Elements.s_Input);
                IList<XElement> inputList = FindDataItemBits(input, out IList<XElement> availableDataItemsInput, out SortedList<UInt16, XElement> availableBitDataItemsInput);

                var output = ioData.Element(NamespaceGsdDef + Elements.s_Output);
                IList<XElement> outputList = FindDataItemBits(output, out IList<XElement> availableDataItemsOutput, out SortedList<UInt16, XElement> availableBitDataItemsOutput);

                if (input != null)
                    FindChannels(input, ref inputChannels, ref outputChannels);

                if (output != null)
                    FindChannels(output, ref outputChannels, ref inputChannels);

                IList<XElement> dataRefs = ioData.Descendants(NamespaceGsdDef + Elements.s_DataRef)
                    .Where(x => x.Parent != null && x.Parent.Name.LocalName == Elements.s_Channel)
                    .ToList();

                if (input != null)
                    CheckCn_0X000320011(input, inputChannels, inputList, availableDataItemsInput, availableBitDataItemsInput, dataRefs);

                if (output != null)
                    CheckCn_0X000320011(output, outputChannels, outputList, availableDataItemsOutput, availableBitDataItemsOutput, dataRefs);

            }

            return true;
        }

        /// <summary>
        /// Check number: CheckInputOnBackupDelay
        /// Here the check is done for 'S2MaxInputOnBackupDelay'/'R2MaxInputOnBackupDelay' in conjunction to 'RT_InputOnBackupAR_Supported' and 'DeviceType'.
        ///
        /// The following has to be checked:
        /// 
        /// (1) 'S2MaxInputOnBackupDelay' shall only be present if 'DeviceType' is "S2" or "R2".
        ///     'R2MaxInputOnBackupDelay' shall only be present if 'DeviceType' is "R1" or "R2".
        /// (2) 'S2MaxInputOnBackupDelay' shall only be present if 'RT_InputOnBackupAR_Supported' is present and "true" and 'DeviceType' is "S2" or "R2".
        ///     'R2MaxInputOnBackupDelay' shall only be present if 'RT_InputOnBackupAR_Supported' is present and "true" and 'DeviceType' is "R1" or "R2".
        /// (3) If 'RT_InputOnBackupAR_Supported' is present and "true" 'S2MaxInputOnBackupDelay' shall also be present if 'DeviceType' is is "S2" or "R2".
        ///     If 'RT_InputOnBackupAR_Supported' is present and "true" 'R2MaxInputOnBackupDelay' shall also be present if 'DeviceType' is is "R1" or "R2".
        /// 
        /// </summary>
        /// <returns>void</returns>
        protected virtual void CheckInputOnBackupDelay(string delayToBeChecked, XAttribute backupDelayNode, IList<string> demandedDeviceTypes, IList<string> supportedDeviceTypes, XAttribute rTInputOnBackupArSupportedNode)
        {
            bool rTInputOnBackupArSupported = false;
            if (rTInputOnBackupArSupportedNode != null)
                rTInputOnBackupArSupported = XmlConvert.ToBoolean(rTInputOnBackupArSupportedNode.Value);

            var xliBackupDelay = (IXmlLineInfo)backupDelayNode;
            var xliRTInputOnBackupArSupported = (IXmlLineInfo)rTInputOnBackupArSupportedNode;

            bool neededDeviceTypeFound = false;
            foreach (string supportedDeviceType in supportedDeviceTypes)
            {
                if (demandedDeviceTypes.Contains(supportedDeviceType))
                {
                    neededDeviceTypeFound = true;
                    break;
                }
            }

            if (backupDelayNode != null && (!neededDeviceTypeFound || !rTInputOnBackupArSupported))
            {
                if (Help.CheckSchemaVersion(backupDelayNode, SupportedGsdmlVersion))
                {
                    // "'SystemRedundancy/@{0}' shall only be present if 'RT_InputOnBackupAR_Supported' is present and "true" and 'DeviceType' is "{1}" or "{2}"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032002_2"), delayToBeChecked, demandedDeviceTypes[0], demandedDeviceTypes[1]);
                    string xpath = Help.GetXPath(backupDelayNode);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xliBackupDelay.LineNumber, xliBackupDelay.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00032002_2");
                }
            }
            else if (backupDelayNode == null && rTInputOnBackupArSupported && neededDeviceTypeFound)
            {
                if (Help.CheckSchemaVersion(rTInputOnBackupArSupportedNode, SupportedGsdmlVersion))
                {
                    // "If 'RT_InputOnBackupAR_Supported' is present and "true" 'SystemRedundancy/@{0}' shall also be present if 'DeviceType' is "{1}" or "{2}"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032002_3"), delayToBeChecked, demandedDeviceTypes[0], demandedDeviceTypes[1]);
                    string xpath = Help.GetXPath(rTInputOnBackupArSupportedNode);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xliRTInputOnBackupArSupported.LineNumber, xliRTInputOnBackupArSupported.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00032002_3");
                }
            }
        }

        /// <summary>
        /// Check number: CN_0x00032002
        /// Some checks on the attributes of 'SystemRedundancy':
        /// (1) The attribute 'S2MaxInputOnBackupDelay' shall be present if and only if the
        ///     attribute 'RT_InputOnBackupAR_Supported' is present and "true"
        ///     and if attribute 'DeviceType' is S2 or R2, but not R1 (because R1 can't be used as S2). 
        /// (2) The attribute 'R2MaxInputOnBackupDelay' shall be present if and only if the
        ///     attribute 'RT_InputOnBackupAR_Supported' is present and "true"
        ///     and if attribute 'DeviceType' is R1 or R2. 
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032002()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var systemRedundancy in nl)
            {
                IList<string> supportedDeviceTypes = GetDeviceTypes(systemRedundancy.Parent);
                var s2MaxInputOnBackupDelay = systemRedundancy.Attribute(Attributes.s_S2MaxInputOnBackupDelay);
                var r2MaxInputOnBackupDelay = systemRedundancy.Attribute(Attributes.s_R2MaxInputOnBackupDelay);
                var rTInputOnBackupArSupported = systemRedundancy.Attribute(Attributes.s_RTInputOnBackupArSupported);

                // (1)
                IList<string> demandedDeviceTypesS2R2 = new List<string>();
                demandedDeviceTypesS2R2.Add(Enums.s_S2);
                demandedDeviceTypesS2R2.Add(Enums.s_R2);
                CheckInputOnBackupDelay(Attributes.s_S2MaxInputOnBackupDelay, s2MaxInputOnBackupDelay, demandedDeviceTypesS2R2, supportedDeviceTypes, rTInputOnBackupArSupported);

                // (2)
                IList<string> demandedDeviceTypesR1R2 = new List<string>();
                demandedDeviceTypesR1R2.Add(Enums.s_R1);
                demandedDeviceTypesR1R2.Add(Enums.s_R2);
                CheckInputOnBackupDelay(Attributes.s_R2MaxInputOnBackupDelay, r2MaxInputOnBackupDelay, demandedDeviceTypesR1R2, supportedDeviceTypes, rTInputOnBackupArSupported);
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00032003
        /// (1) If the attribute MAUTypeList/MAUTypeItem/@Value = 0 is present
        ///     this must be the only one in the MAUTypeList.
        /// (2) A MAUTypeItem with Value 0 shall have AdjustSupported missing or "false".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032003()
        {
            string xp = ".//gsddef:MAUTypeList[gsddef:MAUTypeItem/@Value = 0]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var mauTypeList in nl)
            {
                var mauTypeItems = mauTypeList.Elements(NamespaceGsdDef + Elements.s_MauTypeItem).ToList();
                if (mauTypeItems.Count > 1)
                {
                    if (Help.CheckSchemaVersion(mauTypeList, SupportedGsdmlVersion))
                    {
                        // "If 'MAUTypeItem/@Value=0' is present it must be the only one in the 'MAUTypeList'."
                        string msg = Help.GetMessageString("M_0x00032003_1");
                        string xpath = Help.GetXPath(mauTypeList);
                        var xli = (IXmlLineInfo)mauTypeList;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00032003_1");
                    }
                }

                var mauTypeItemsWith0 =
                    mauTypeList.Elements(NamespaceGsdDef + Elements.s_MauTypeItem)
                        .Where(x => x.Attribute(Attributes.s_Value) != null);
                foreach (var mauTypeItem in mauTypeItemsWith0)
                {
                    string strValue = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value);
                    UInt16 value = XmlConvert.ToUInt16(strValue);
                    if (value != 0)
                    {
                        continue;
                    }
                    bool adjustSupported = false;
                    if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported)))
                    {
                        adjustSupported = XmlConvert.ToBoolean(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported));
                    }

                    if (!adjustSupported)
                    {
                        continue;
                    }
                    if (!Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                    // "If 'MAUTypeItem/@Value=0' is present the attribute 'AdjustSupported' shall be missing or "false"."
                    string msg = Help.GetMessageString("M_0x00032003_2");
                    string xpath = Help.GetXPath(mauTypeItem);
                    var xli = (IXmlLineInfo)mauTypeItem;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00032003_2");

                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00032004
        /// If there is any MAUTypeItem with Extension present and <>0,
        /// the attribute MAUTypeList/@ExtensionSupported shall be present and "true". 
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032004()
        {
            string xp = ".//gsddef:MAUTypeList[gsddef:MAUTypeItem/@Extension != 0]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var mauTypeList in nl)
            {
                bool extensionSupported = false;
                if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(mauTypeList, Attributes.s_ExtensionSupported)))
                {
                    extensionSupported = XmlConvert.ToBoolean(Help.GetAttributeValueFromXElement(mauTypeList, Attributes.s_ExtensionSupported));
                }

                if (!extensionSupported)
                {
                    if (Help.CheckSchemaVersion(mauTypeList, SupportedGsdmlVersion))
                    {
                        // "If 'MAUTypeItem/@Extension!=0' is present the attribute 'MAUTypeList/@ExtensionSupported' shall be present and "true"."
                        string msg = Help.GetMessageString("M_0x00032004_1");
                        string xpath = Help.GetXPath(mauTypeList);
                        var xli = (IXmlLineInfo)mauTypeList;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00032004_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00032005
        /// If the attribute MAUTypes is present on the port submodule,
        /// (1) there shall be a MAUTypeItem for each of its values in attribute Value,
        /// (2) with attribute Extension missing or set to 0,
        /// (3) and with attribute AdjustSupported set to "true"
        ///     (except for the MAUType value 0 (radio communication) where the attribute
        ///     AdjustSupported shall be missing or "false").
        /// (4) From GSDML V2.41 on, MauType - Extension combinations must be checked.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032005()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem).Where(x => x.Attribute(Attributes.s_MauTypes) != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var portSubmoduleItem in nl)
            {
                XAttribute mauTypes = portSubmoduleItem.Attribute(Attributes.s_MauTypes);
                List<ValueListHelper.ValueRangeT> mauTypesList = ValueListHelper.NormalizeValueList(mauTypes, Store);

                var mauTypeItems = portSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
                Dictionary<UInt16, XElement> mauTypeItemValuesWithEx = new Dictionary<UInt16, XElement>();
                Dictionary<UInt16, XElement> mauTypeItemValuesWoutEx = new Dictionary<UInt16, XElement>();
                FillMauTypeItemValues(mauTypeItems, mauTypeItemValuesWoutEx, mauTypeItemValuesWithEx);

                uint numberErrorMessages = 0;

                for (int currentRange = 0; currentRange < mauTypesList.Count; currentRange++)
                {
                    if (numberErrorMessages == Constants.s_MaxNumberErrorMessages)
                        break;

                    numberErrorMessages = CheckMauTypesList(mauTypesList, currentRange, mauTypeItemValuesWoutEx, mauTypeItemValuesWithEx, portSubmoduleItem, numberErrorMessages);
                }

            }

            // (4)
            CheckAllMauTypeExtensionCominations();

            return true;
        }

        private uint CheckMauTypesList(
            IReadOnlyList<ValueListHelper.ValueRangeT> mauTypesList,
            int currentRange,
            IReadOnlyDictionary<ushort, XElement> mauTypeItemValuesWoutEx,
            IReadOnlyDictionary<ushort, XElement> mauTypeItemValuesWithEx,
            XObject portSubmoduleItem,
            uint numberErrorMessages)
        {
            UInt16 mauType = (UInt16)mauTypesList[currentRange].From;
            for (uint k = mauTypesList[currentRange].From; k <= mauTypesList[currentRange].To; k++)
            {
                // (1)
                if (!mauTypeItemValuesWoutEx.ContainsKey(mauType)
                    && mauTypeItemValuesWithEx.TryGetValue(mauType, out XElement mauTypeItem))
                {
                    // (2)
                    CreateReport0x00032005_2(mauTypeItem, mauType);
                }
                else if (mauTypeItemValuesWoutEx.TryGetValue(mauType, out mauTypeItem))
                {
                    CreateReport0x00032005_3(mauType, mauTypeItem);
                }
                else
                {
                    // (1)
                    CreateReport0x00032005_1(portSubmoduleItem, mauType);

                    numberErrorMessages++;
                    if (numberErrorMessages == Constants.s_MaxNumberErrorMessages)
                        break;
                }

                mauType++;
            }

            return numberErrorMessages;
        }

        private void CheckAllMauTypeExtensionCominations()
        {
            var nl1 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeList);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var mauTypeList in nl1)
            {
                var nl2 = mauTypeList.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
                foreach (var mauTypeItem in nl2)
                {
                    CheckMAUTypeExtensionCombinations(mauTypeItem);
                }
            }
        }

        private static void FillMauTypeItemValues(
            IEnumerable<XElement> mauTypeItems,
            IDictionary<ushort, XElement> mauTypeItemValuesWoutEx,
            IDictionary<ushort, XElement> mauTypeItemValuesWithEx)
        {
            foreach (var mauTypeItem in mauTypeItems)
            {
                string valueStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value); // required
                UInt16 value = XmlConvert.ToUInt16(valueStr);
                bool extensionIs0 = true; // default
                string extensionStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension);
                if (!string.IsNullOrEmpty(extensionStr)
                    && XmlConvert.ToUInt16(extensionStr) != 0)
                    extensionIs0 = false;

                if (extensionIs0)
                {
                    mauTypeItemValuesWoutEx.Add(value, mauTypeItem);
                }
                else
                {
                    if (!mauTypeItemValuesWithEx.ContainsKey(value))
                        mauTypeItemValuesWithEx.Add(value, mauTypeItem);
                }
            }
        }

        private void CreateReport0x00032005_1(XObject portSubmoduleItem, ushort mauType)
        {
            if (!Help.CheckSchemaVersion(portSubmoduleItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUTypes' value = {0} no 'MAUTypeItem' with this value in attribute 'Value' found."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032005_1"), mauType);
            string xpath = Help.GetXPath(portSubmoduleItem);
            var xli = (IXmlLineInfo)portSubmoduleItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00032005_1");
        }

        private void CreateReport0x00032005_3(ushort mauType, XElement mauTypeItem)
        {
            if (mauType == 0)
            {
                return;
            }

            bool adjustSupported = false;
            if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported)))
            {
                adjustSupported = XmlConvert.ToBoolean(
                    Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported));
            }

            // (3)
            if (adjustSupported)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUTypes' value = {0} a 'MAUTypeItem' with this value in attribute 'Value' found, but attribute 'AdjustSupported' is not set to "true"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032005_3"), mauType);
            string xpath = Help.GetXPath(mauTypeItem);
            var xli = (IXmlLineInfo)mauTypeItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00032005_3");
        }

        private void CreateReport0x00032005_2(XObject mauTypeItem, ushort mauType)
        {
            if (!Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUTypes' value = {0} a 'MAUTypeItem' with this value in attribute 'Value' found, but attribute 'Extension' is not missing or set to 0."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032005_2"), mauType);
            string xpath = Help.GetXPath(mauTypeItem);
            var xli = (IXmlLineInfo)mauTypeItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00032005_2");
        }
        /// <summary>
        /// Check number: CN_0x00032006
        /// If the attribute MAUType is present, but MAUTypes is not present
        /// on the port submodule, and the interpretation of the attribute
        /// MAUType by older engineering tools is not blocked by the use
        /// of the attribute RequiredSchemaVersion with a value >= "V2.32",
        /// (1) there shall be a MAUTypeItem for the value of the attribute MAUType
        ///     (or its default if missing) in attribute Value,
        /// (2) with attribute Extension missing or set to 0,
        /// (3) and with attribute AdjustSupported missing or set to "false".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032006()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem).Where(x => x.Attribute(Attributes.s_MauTypes) == null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var portSubmoduleItem in nl)
            {
                double requiredSchemaVersion = GEtRequiredSchemaVersion(portSubmoduleItem);

                if (!(requiredSchemaVersion < 2.32))
                {
                    continue;
                }

                string mauTypeStr = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_MauType);
                if (string.IsNullOrEmpty(mauTypeStr))
                    mauTypeStr = "100BASETXFD";
                UInt16 mauType = MapMauTypeStringToValue(mauTypeStr);

                // (1)
                bool bMauTypeFound = false;
                var mauTypeItems = portSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
                foreach (var mauTypeItem in mauTypeItems)
                {
                    var xli = (IXmlLineInfo)mauTypeItem;

                    string valueStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value);  // required
                    UInt16 value = XmlConvert.ToUInt16(valueStr);
                    if (value != mauType)
                    {
                        continue;
                    }

                    UInt16 extension = 0;
                    if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension)))
                    {
                        extension = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension));
                    }
                    // (2)
                    CreateReport0x00032006_2(extension, mauTypeItem, mauTypeStr, xli);

                    bool adjustSupported = false;
                    if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported)))
                    {
                        adjustSupported = XmlConvert.ToBoolean(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_AdjustSupported));
                    }
                    // (3)
                    CreateReport0x00032006_3(adjustSupported, mauTypeItem, mauTypeStr, xli);

                    bMauTypeFound = true;
                    break;
                }

                // (1)
                CreateReport0x00032006_1(bMauTypeFound, portSubmoduleItem, mauTypeStr);
            }

            return true;
        }

        private double GEtRequiredSchemaVersion(XElement portSubmoduleItem)
        {
            double requiredSchemaVersion;
            XElement submoduleList = portSubmoduleItem.Parent;
            if (submoduleList != null && submoduleList.Name.LocalName == Elements.s_SubmoduleList) // Can be SubmoduleList or SystemDefinedSubmoduleList
            {
                // For a pluggable PortSubmoduleItem the RequiredSchemaVersion can be found directly as attribute of PortSubmoduleItem
                requiredSchemaVersion = GetPNioVersion(
                    Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_RequiredSchemaVersion));
            }
            else // submoduleList.LocalName == Elements.SystemDefinedSubmoduleList
            {
                // For a PortSubmoduleItem which is fixed plugged in a DAP or Module the RequiredSchemaVersion can be found as attribute of DAP or Module
                XElement dapOrMod = submoduleList.Parent;
                requiredSchemaVersion =
                    GetPNioVersion(Help.GetAttributeValueFromXElement(dapOrMod, Attributes.s_RequiredSchemaVersion));
            }

            if (requiredSchemaVersion == 0)
                requiredSchemaVersion = 2.25;
            return requiredSchemaVersion;
        }

        private void CreateReport0x00032006_1(bool bMauTypeFound, XObject portSubmoduleItem, string mauTypeStr)
        {
            if (bMauTypeFound)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(portSubmoduleItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUType' value = {0} no 'MAUTypeItem' with the mapped value in attribute 'Value' found."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032006_1"), mauTypeStr);
            string xpath = Help.GetXPath(portSubmoduleItem);
            var xli = (IXmlLineInfo)portSubmoduleItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00032006_1");
        }

        private void CreateReport0x00032006_3(bool adjustSupported, XObject mauTypeItem, string mauTypeStr, IXmlLineInfo xli)
        {
            if (!adjustSupported)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUType' value = {0} a 'MAUTypeItem' with the mapped value in attribute 'Value' found, but attribute 'AdjustSupported' is not missing or set to "false"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032006_3"), mauTypeStr);
            string xpath = Help.GetXPath(mauTypeItem);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00032006_3");
        }

        private void CreateReport0x00032006_2(ushort extension, XObject mauTypeItem, string mauTypeStr, IXmlLineInfo xli)
        {
            if (extension == 0)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'PortSubmoduleItem/@MAUType' value = {0} a 'MAUTypeItem' with the mapped value in attribute 'Value' found, but attribute 'Extension' is not missing or set to 0."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032006_2"), mauTypeStr);
            string xpath = Help.GetXPath(mauTypeItem);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00032006_2");
        }
        /// <summary>
        /// Check number: CN_0x00032007
        /// RT_Class3Properties/@MaxRetentionTime shall not be present if @ForwardingMode only contains the token "Absolute".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032007()
        {
            string xp = ".//gsddef:RT_Class3Properties[@MaxRetentionTime and @ForwardingMode = 'Absolute']";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var rtClass3Properties in nl)
            {
                if (Help.CheckSchemaVersion(rtClass3Properties, SupportedGsdmlVersion))
                {
                    // "'RT_Class3Properties/@MaxRetentionTime' shall not be present if '@ForwardingMode' only contains the token "Absolute"."
                    string msg = Help.GetMessageString("M_0x00032007_1");
                    string xpath = Help.GetXPath(rtClass3Properties);
                    var xli = (IXmlLineInfo)rtClass3Properties;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00032007_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00032008
        /// PortSubmoduleItem/@CheckMAUTypeDifferenceSupported shall be present and "true"
        /// if DeviceAccessPointItem/@PNIO_Version >= "V2.31" and
        /// the attribute DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032008()
        {
            string xp = ".//gsddef:DeviceAccessPointItem[gsddef:CertificationInfo/@ConformanceClass = 'C']";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                if (pnioVersion < 2.31)
                    continue;

                if (!DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
                {
                    continue;
                }

                foreach (var portSubmoduleItem in portSubmoduleItems)
                {
                    bool checkMauTypeDifferenceSupported = false;
                    string checkMauTypeDifferenceSupportedStr = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_CheckMauTypeDifferenceSupported);
                    if (!string.IsNullOrEmpty(checkMauTypeDifferenceSupportedStr))
                        checkMauTypeDifferenceSupported = XmlConvert.ToBoolean(checkMauTypeDifferenceSupportedStr);

                    if (checkMauTypeDifferenceSupported)
                    {
                        continue;
                    }

                    if (!Help.CheckSchemaVersion(portSubmoduleItem, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                    // "The 'PortSubmoduleItem' is configurable with 'DeviceAccessPointItem' ('ID' = "{0}"),
                    // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' "C",
                    // but the attribute 'PortSubmoduleItem/@CheckMAUTypeDifferenceSupported' is not present or not "true"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00032008_1"), Help.GetAttributeValueFromXElement(dap, Attributes.ID));
                    string xpath = Help.GetXPath(portSubmoduleItem);
                    var xli = (IXmlLineInfo)portSubmoduleItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition,
                        msg, xpath, ReportCategories.TypeSpecific, "0x00032008_1");
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00032009
        /// (1) The length of the string in the attribute DefaultValue must be compared
        ///     against the Length attribute of the DataType, considering the rules:
        ///     61131_STRING: 1+1 octets + number of characters (Octet = byte)
        ///     61131_WSTRING: 2+2 octets + 2 * number of characters
        ///     UnicodeString8: number of octets according to UTF-8 encoding of the characters (1-4 octets per character)
        ///     OctetString_S: number of octets with status must be dividable by 3
        /// (2) For the DataTypes OctetString, OctetString_S, 61131_STRING and 61131_WSTRING:
        ///     The attribute DefaultValue must be checked for correct syntax.
        ///     61131_STRING: Characters must be 7-Bit ASCII except the DEL char (code 127)
        ///     61131_WSTRING: Characters must fit to UCS-2 coding, i.e. the Unicode code point must be in
        ///     the BMP (Basic Multilingual Page), i.e. code point must be less than 65536.
        ///     OctetString: Pattern is (0x[0-9a-fA-F][0-9a-fA-F],)*0x[0-9afA-F][0-9a-fA-F]               
        ///     OctetString_S: Pattern is (0x[0-9a-fA-F][0-9a-fA-F][BSUG]{8},)*0x[0-9a-fA-F][0-9a-fA-F][BSUG]{8}
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00032009()
        {
            var nl = GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_DataType) != null &&
                            x.Attribute(Attributes.s_Length) != null &&
                            x.Attribute(Attributes.s_DefaultValue) != null
                            );
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var en in nl)
            {

                UInt16 length = XmlConvert.ToUInt16(en.Attribute(Attributes.s_Length).Value);
                string dataType = en.Attribute(Attributes.s_DataType).Value;
                string defaultValue = en.Attribute(Attributes.s_DefaultValue).Value;

                var lineInfo = (IXmlLineInfo)en.Attribute(Attributes.s_Length);

                bool dontMatchToSyntax = false;
                bool dontMatchToLength = DontMatchToLengthOrSyntax(dataType, length, defaultValue, false, ref dontMatchToSyntax);

                // (1)
                if (dontMatchToLength)
                {
                    // "The length of the string in the attribute DefaultValue according to its DataType does not match
                    // to the Length attribute."
                    string msg = Help.GetMessageString("M_0x00032009_1");
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_Length));
                    if (lineInfo != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00032009_1");
                    }
                }

                // (2)
                if (dontMatchToSyntax)
                {
                    // "The string in the attribute DefaultValue does not match its DataType."
                    string msg = Help.GetMessageString("M_0x00032009_2");
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_Length));
                    if (lineInfo != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00032009_2");
                    }
                }
            }
            return true;
        }

        private static bool DontMatchToLengthOrSyntax(
            string dataType,
            ushort length,
            string defaultValue,
            bool dontMatchToLength,
            ref bool dontMatchToSyntax)
        {
            switch (dataType)
            {
                case Enums.s_String61131:
                    {
                        // (1)
                        dontMatchToLength = DontMatchToLength_STRING_61131(length, defaultValue);

                        // (2)
                        try
                        {
                            Encoding enc = new ASCIIEncoding();
                            byte[] bytes = enc.GetBytes(defaultValue);
                            for (int i = 0; i < bytes.Length; i++)
                                if (bytes[i] > 126)
                                    dontMatchToSyntax = true;
                        }
                        catch (Exception e) when (e is ArgumentNullException || e is EncoderFallbackException
                                                                             || e is OverflowException)
                        {
                            dontMatchToSyntax = true;
                        }
                    }
                    break;
                case Enums.s_Wstring61131:
                    {
                        // (1)
                        dontMatchToLength = DontMatchToLength_WSTRING_61131(length, defaultValue);

                        // (2)
                        try
                        {
                            Encoding enc = Encoding.GetEncoding("ucs-2");
                            enc.GetBytes(defaultValue);
                        }
                        catch (Exception e) when (e is ArgumentNullException || e is EncoderFallbackException
                                                                             || e is ArgumentException)
                        {
                            dontMatchToSyntax = true;
                        }
                    }
                    break;
                case Enums.s_UnicodeString8:
                    {
                        // (1)
                        try
                        {
                            dontMatchToLength = DontMatchToLength_UnicodeString8(length, defaultValue);
                        }
                        catch (Exception e) when (e is ArgumentNullException || e is EncoderFallbackException)
                        {
                            dontMatchToSyntax = true;
                        }
                    }
                    break;
                case Enums.s_OctetStringS:
                    {
                        // (1)
                        dontMatchToLength = DontMatchToLength_OctetString_S(length, defaultValue);
                    }
                    break;
            }

            return dontMatchToLength;
        }

        private static bool DontMatchToLength_OctetString_S(ushort length, string defaultValue)
        {
            bool dontMatchToLength = false;
            int defaultValueLength = (Regex.Matches(defaultValue, Regex.Escape(",")).Count + 1) * 3;
            if (length != defaultValueLength)
            {
                dontMatchToLength = true;
            }

            return dontMatchToLength;
        }

        private static bool DontMatchToLength_UnicodeString8(ushort length, string defaultValue)
        {
            bool dontMatchToLength = false;
            Encoding enc = new UTF8Encoding(true, true);
            int defaultValueLength = enc.GetByteCount(defaultValue);
            if (length < defaultValueLength)
            {
                dontMatchToLength = true;
            }

            return dontMatchToLength;
        }

        private static bool DontMatchToLength_WSTRING_61131(ushort length, string defaultValue)
        {
            bool dontMatchToLength = false;
            if (length < (4 + 2 * defaultValue.Length))
            {
                dontMatchToLength = true;
            }

            return dontMatchToLength;
        }

        private static bool DontMatchToLength_STRING_61131(ushort length, string defaultValue)
        {
            bool dontMatchToLength = false;
            if (length < (2 + defaultValue.Length))
            {
                dontMatchToLength = true;
            }

            return dontMatchToLength;
        }

        /// <summary>
        /// Check number: CN_0x0003200A
        /// 
        /// <Description>
        /// For all (Virtual)SubmoduleItem elements:
        /// On each Submodule that follows the RIOforFA profile (i.e. where IOData/Input/DataItem/ @Subordinate="true" exist),
        /// there must be channel description (i.e. IOData/Input or Output/Channel must be present).
        /// 
        /// The Check must start with GSDML V2.32 (since the Channel element was introduced in this version).
        /// The reaction shall be Warning starting with GSDML V2.32 and Error starting with GSDML V2.34.
        /// The PNIO_Version has no influence on the check.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003200A()
        {
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);

            foreach (var submodule in allSubmodules)
            {
                var inputDataItems = GetInputDataItems(submodule);
                if (IsRioForFaSupported(inputDataItems))
                {
                    // Check if IOData is distributed to channels
                    var ioInputChannels = submodule.Elements(NamespaceGsdDef + Elements.s_IoData)
                        .Elements(NamespaceGsdDef + Elements.s_Input)
                        .Elements(NamespaceGsdDef + Elements.s_Channel);
                    var ioOutputChannels = submodule.Elements(NamespaceGsdDef + Elements.s_IoData)
                        .Elements(NamespaceGsdDef + Elements.s_Output)
                        .Elements(NamespaceGsdDef + Elements.s_Channel);

                    if (ioInputChannels.Count() == 0 && ioOutputChannels.Count() == 0)
                    {
                        if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                        {
                            var lineInfo = (IXmlLineInfo)submodule;
                            // "Channel description is mandatory when the submodule follows the RIOforFA profile."
                            string msg = Help.GetMessageString("M_0x0003200A_1");
                            string xpath = Help.GetXPath(submodule);
                            Store.CreateAndAnnounceReport(ReportType_0X0003200A, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                ReportCategories.TypeSpecific, "0x0003200A_1");
                        }
                    }
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x0003200B
        /// 
        /// <Description>
        /// Check the single values in attribute 'MAUTypeItem/@Value' under 'MAUTypeList' for allowed values. 
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x0003200B()
        {
            var mauTypeListNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeList);
            mauTypeListNodes = Help.TryRemoveXElementsUnderXsAny(mauTypeListNodes, Nsmgr, Gsd);

            foreach (var mauTypeList in mauTypeListNodes)
            {
                var mauTypeItems = mauTypeList.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
                foreach (var mauTypeItem in mauTypeItems)
                {
                    XAttribute an = mauTypeItem.Attribute(Attributes.s_Value);
                    List<ValueListHelper.ValueRangeT> values = new List<ValueListHelper.ValueRangeT>();
                    ValueListHelper.ValueRangeT vr = new ValueListHelper.ValueRangeT();
                    if (an == null)
                    {
                        continue;
                    }
                    vr.From = vr.To = XmlConvert.ToUInt16(an.Value);
                    values.Add(vr);
                    XElement port = mauTypeList.Parent;
                    CheckOneAttributeWithValueList(an, values, port, Attributes.s_MauTypes);
                }
            }

            return true;
        }


        #endregion
    }
}


