/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDevice.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.LAddresses;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a central device.
    /// </summary>
    internal class CentralDevice : PclObject
    {
        /// <summary>
        /// CentralAddressManager controls the address assignment for the CentralDevice and all DecentralDevice objects assigned to
        /// it.
        /// </summary>
        private LAddressManager m_CentralLAddressManager;

        /// <summary>
        /// The list of Interface objects connected to the CentralDevice.
        /// </summary>
        private Interface m_Interface;

        /// <summary>
        /// The constructor of CentralDevice; instantiates the CentralAddressManager and the empty list for
        /// Module objects as well as registering itself with the address manager.
        /// </summary>
        /// <param name="centralDeviceId">The ID of the central device.</param>
        public CentralDevice(string centralDeviceId)
        {
            m_CentralLAddressManager = new LAddressManager();

            Id = centralDeviceId;

            m_CentralLAddressManager.RegisterPCLObject(this, LAddressType.Station);
            m_CentralLAddressManager.RegisterPCLObject(this, LAddressType.Rack);
            m_CentralLAddressManager.RegisterPCLObject(this, LAddressType.PCLObject);
        }

        /// <summary>
        /// The rack address of the CentralDevice.
        /// </summary>
        public LAddress RackLAddress { get; set; }

        /// <summary>
        /// The station address of the CentralDevice.
        /// </summary>
        public LAddress StationLAddress { get; set; }

        /// <summary>
        /// Gets Mapped Firmware Version of this device
        /// </summary>
        /// <returns></returns>
        internal FwVersion GetCentralDeviceFwVersion()
        {
            string pndVersion = AttributeAccess.GetAnyAttribute<string>(
                InternalAttributeNames.FwVersion,
                new AttributeAccessCode(),
                null);

            return HWCNBL.Utilities.AttributeUtilities.MapVersion(pndVersion);
        }

        /// <summary>
        /// Adds an interface and registers it with the CentralAddressManager.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule to be added.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        public void AddInterface(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            m_Interface = interfaceSubmodule;
            interfaceSubmodule.ParentObject = this;
            m_CentralLAddressManager.RegisterPCLObject(interfaceSubmodule, LAddressType.PCLObject);
        }

        /// <summary>
        /// Gets the CentralAddressManager of this CentralDevice.
        /// </summary>
        /// <returns>The CentralAddressManager of this CentralDevice.</returns>
        public LAddressManager GetCentralAddressManager()
        {
            return m_CentralLAddressManager;
        }

        /// <summary>
        /// Gets the Interface object connected to this CentralDevice.
        /// </summary>
        /// <returns>The list of interfaces connected to this CentralDevice.</returns>
        internal override Interface GetInterface()
        {
            return m_Interface;
        }
    }
}