<?xml version="1.0" encoding="utf-8"?>
<CentralDeviceCatalogObject xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                            xmlns="http://www.siemens.com/Automation/PNConfigLib/ControllerVariant"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                            xsi:schemaLocation="http://www.siemens.com/Automation/PNConfigLib/ControllerVariant ControllerVariant.xsd">
  <AttributeLookup>
    <CatalogAttribute Key="FwVersion">
      <Value xsi:type="xsd:string">v2.2</Value>
    </CatalogAttribute>
    <CatalogAttribute Key="PnModuleIdentNumber">
      <Value xsi:type="xsd:int">45569</Value>
    </CatalogAttribute>
  </AttributeLookup>
  <InterfaceCatalogItems>
    <InterfaceCatalogItem>
      <AttributeLookup>
        <CatalogAttribute Key="PnIoIODDefaultRouterSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeIECheckProtocolPresence">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeIPSubnetMaskUnrestricted">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeDefaultIPAddress">
          <Value xsi:type="xsd:string">***********</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnAllowOverwriteNoISupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnBasicPackageVersion">
          <Value xsi:type="xsd:unsignedInt">1537</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnDCPBoundarySupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnDelayMeasurementSupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAddressTailoring">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAddressTailoringEnabled">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAllowSlot0WithoutSubmodule1">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoArRecordSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoArStartupMode">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">1</DefaultValue>
            <Value xsi:type="xsd:int">2</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoConfigAlarmCrDataSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoCheckDeviceIDSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoControllerPdevCombinedObjectSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoDataRecordsTransferSeqSupp">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoFastStartupMaxCount">
          <Value xsi:type="xsd:unsignedInt">32</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoIdentificationBlockRequired">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoIOXSRequired">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMachineTailoring">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMachineTailoringTopologyRequired">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxControllerInputDataLength">
          <Value xsi:type="xsd:long">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxControllerOutputDataLength">
          <Value xsi:type="xsd:long">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDevices">
          <Value xsi:type="xsd:int">128</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDeviceSubmoduleDataLength">
          <Value xsi:type="xsd:int">1024</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDeviceSubmodules">
          <Value xsi:type="xsd:int">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxFrameStartTime">
          <Value xsi:type="xsd:unsignedInt">1600</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxIrtDevices">
          <Value xsi:type="xsd:int">32</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxStationNumber">
          <Value xsi:type="xsd:int">2047</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMinMaxWatchdogFactor">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">3</anyType>
              <anyType xsi:type="xsd:int">255</anyType>
            </List>
            <Value xsi:type="xsd:int">3</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMinNRTGap">
          <Value xsi:type="xsd:unsignedInt">960</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMultipleWriteSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoObjectUuidV23Supported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoOperatingMode">
          <Value xsi:type="xsd:unsignedInt">1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoScfAdaptionNonPow2Supported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoScfAdaptionSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSharedDeviceAssignmentSupp">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerDistributionMode">
          <Value xsi:type="xsd:unsignedInt">2</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxBytesPerMs">
          <Value xsi:type="xsd:long">-1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxFramesPerMs">
          <Value xsi:type="xsd:long">64</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxSharedRTC3Consumer">
          <Value xsi:type="xsd:int">64</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxSharedRTC3Provider">
          <Value xsi:type="xsd:int">64</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxRTC12BytesPerMs">
          <Value xsi:type="xsd:long">5952</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppFrameClass">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">3</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppSCF12">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">32</anyType>
            </List>
            <Value xsi:type="xsd:int">32</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppSCF3">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">16</anyType>
              <anyType xsi:type="xsd:int">20</anyType>
              <anyType xsi:type="xsd:int">24</anyType>
              <anyType xsi:type="xsd:int">28</anyType>
              <anyType xsi:type="xsd:int">32</anyType>
              <anyType xsi:type="xsd:int">64</anyType>
              <anyType xsi:type="xsd:int">128</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">32</DefaultValue>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR12Pow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">4</anyType>
              <anyType xsi:type="xsd:int">8</anyType>
              <anyType xsi:type="xsd:int">16</anyType>
              <anyType xsi:type="xsd:int">32</anyType>
              <anyType xsi:type="xsd:int">64</anyType>
              <anyType xsi:type="xsd:int">128</anyType>
              <anyType xsi:type="xsd:int">256</anyType>
              <anyType xsi:type="xsd:int">512</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR12NonPow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR3Pow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">4</anyType>
              <anyType xsi:type="xsd:int">8</anyType>
              <anyType xsi:type="xsd:int">16</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR3NonPow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppStartupModes">
          <Value xsi:type="xsd:unsignedInt">2</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtArStartupMode">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtFFWMode">
          <Value xsi:type="xsd:unsignedInt">1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtForwardingMode">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">1</DefaultValue>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtFragmentationMode">
          <Value xsi:type="xsd:unsignedInt">4</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxDfpFramesIOC">
          <Value xsi:type="xsd:unsignedInt">8</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxNoIrFrameData">
          <Value xsi:type="xsd:int">128</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxRangeIRFrameID">
          <Value xsi:type="xsd:unsignedInt">1024</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxRedPeriodLength">
          <Value xsi:type="xsd:unsignedInt">3875</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxRetentionTime">
          <Value xsi:type="xsd:unsignedInt">4000000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMaxSuppBwLevel">
          <Value xsi:type="xsd:unsignedInt">4</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMinFrameSendOffset">
          <Value xsi:type="xsd:unsignedInt">5000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMinRTC3Gap">
          <Value xsi:type="xsd:unsignedInt">1120</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtMinYellowTimeIOC">
          <Value xsi:type="xsd:unsignedInt">9600</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtPllWindow">
          <Value xsi:type="xsd:int">1000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtRestartFactorForDistributedWD">
          <Value xsi:type="xsd:unsignedInt">10</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSubframeCRCSupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSupportedSyncProtocols">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">3</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSwitchBridgingDelay">
          <Value xsi:type="xsd:int">2920</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSwitchBridgingDelayFFWIOC">
          <Value xsi:type="xsd:unsignedInt">2920</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSwitchMaxBufferTime">
          <Value xsi:type="xsd:int">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtSyncRoleSupp">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">0</anyType>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">0</DefaultValue>
            <Value xsi:type="xsd:int">0</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIrtYellowSafetyMargin">
          <Value xsi:type="xsd:unsignedInt">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="IsoModeDecentralSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCacfSupported">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">3</anyType>
              <anyType xsi:type="xsd:int">4</anyType>
              <anyType xsi:type="xsd:int">5</anyType>
              <anyType xsi:type="xsd:int">6</anyType>
              <anyType xsi:type="xsd:int">7</anyType>
              <anyType xsi:type="xsd:int">8</anyType>
              <anyType xsi:type="xsd:int">9</anyType>
              <anyType xsi:type="xsd:int">10</anyType>
              <anyType xsi:type="xsd:int">11</anyType>
              <anyType xsi:type="xsd:int">12</anyType>
              <anyType xsi:type="xsd:int">13</anyType>
              <anyType xsi:type="xsd:int">14</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoInterruptLatency">
          <Value xsi:type="xsd:unsignedInt">144000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeInputBase">
          <Value xsi:type="xsd:unsignedInt">41000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeInputByte">
          <Value xsi:type="xsd:unsignedInt">59</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeOutputBase">
          <Value xsi:type="xsd:unsignedInt">21000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeOutputByte">
          <Value xsi:type="xsd:unsignedInt">30</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeInputSubmodule">
          <Value xsi:type="xsd:unsignedInt">100</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoCopyTimeOutputSubmodule">
          <Value xsi:type="xsd:unsignedInt">49</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoProvisionTime">
          <Value xsi:type="xsd:unsignedInt">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoTpaMaxInCopyLen">
          <Value xsi:type="xsd:int">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoTpaMaxOutCopyLen">
          <Value xsi:type="xsd:int">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIsoUserAdjustedAppCycle">
          <Value xsi:type="xsd:long">1000</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnLLDPNoDSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnLLDPSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnNetworkComponentDiagnosisSupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPdevSupportedModel">
          <Value xsi:type="xsd:unsignedInt">7</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPnIpConfigModeSupported">
          <Value xsi:type="xsd:int">3</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPTPBoundarySupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnSubmoduleIdentNumber">
          <Value xsi:type="xsd:unsignedInt">176</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnSubslotNumber">
          <Value xsi:type="xsd:int">32768</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPrioritizedStartupConsistencyCheckRequired">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
      </AttributeLookup>
      <PortCatalogItems>
        <PortCatalogItem>
          <AttributeLookup>
            <CatalogAttribute Key="PnPortDeactivationSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnCheckMAUTypeDifferenceSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnCheckMAUTypeRecordSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnDCPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnDontGenerateAdjustMAUTypeBlock">
              <Value xsi:type="Enumerated">
                <List>
                  <anyType xsi:type="xsd:unsignedInt">8</anyType>
                </List>
                <Value xsi:type="xsd:unsignedInt">8</Value>
              </Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnEthernetMediumDuplex">
              <Value xsi:type="Enumerated">
                <List>
                  <anyType xsi:type="xsd:unsignedInt">8</anyType>
                  <anyType xsi:type="xsd:unsignedInt">16</anyType>
                </List>
                <Value xsi:type="xsd:unsignedInt">8</Value>
              </Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIoProgrammablePeer">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtGenerateAdjustPreambleLengthV14">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtLineDelaySelection">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtLineLength">
              <Value xsi:type="xsd:unsignedInt">600</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortRxDelay">
              <Value xsi:type="xsd:unsignedInt">330</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortSyncDomainBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortTxDelay">
              <Value xsi:type="xsd:unsignedInt">58</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtShortPreamble100MBitSupported">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtSignalDelayTime">
              <Value xsi:type="xsd:unsignedInt">600</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosis">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosisCapability">
              <Value xsi:type="xsd:unsignedInt">1</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortAutoNegotiation">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortNumber">
              <Value xsi:type="xsd:int">1</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPTPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubmoduleIdentNumber">
              <Value xsi:type="xsd:unsignedInt">177</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubslotNumber">
              <Value xsi:type="xsd:int">32769</Value>
            </CatalogAttribute>
          </AttributeLookup>
        </PortCatalogItem>
        <PortCatalogItem>
          <AttributeLookup>
            <CatalogAttribute Key="IsFiberOptic">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="IsPCF">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="IsPof">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortDeactivationSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnCheckMAUTypeDifferenceSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnCheckMAUTypeRecordSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnDCPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnEthernetMediumDuplex">
              <Value xsi:type="Enumerated">
                <List>
                  <anyType xsi:type="xsd:unsignedInt">8</anyType>
                  <anyType xsi:type="xsd:unsignedInt">16</anyType>
                </List>
                <Value xsi:type="xsd:unsignedInt">8</Value>
              </Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIoProgrammablePeer">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtGenerateAdjustPreambleLengthV14">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtLineDelaySelection">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtLineLength">
              <Value xsi:type="xsd:unsignedInt">600</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortRxDelay">
              <Value xsi:type="xsd:unsignedInt">330</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortSyncDomainBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtPortTxDelay">
              <Value xsi:type="xsd:unsignedInt">58</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtShortPreamble100MBitSupported">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIrtSignalDelayTime">
              <Value xsi:type="xsd:unsignedInt">600</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosis">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosisCapability">
              <Value xsi:type="xsd:unsignedInt">1</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortAutoNegotiation">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortNumber">
              <Value xsi:type="xsd:int">2</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPTPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubmoduleIdentNumber">
              <Value xsi:type="xsd:unsignedInt">177</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubslotNumber">
              <Value xsi:type="xsd:int">32770</Value>
            </CatalogAttribute>
          </AttributeLookup>
        </PortCatalogItem>
      </PortCatalogItems>
    </InterfaceCatalogItem>
  </InterfaceCatalogItems>
</CentralDeviceCatalogObject>