<Window x:Class="PNConfigTool.Views.Windows.AboutWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PNConfigTool.Views.Windows"
        mc:Ignorable="d"
        Title="关于 PNConfigTool" Height="300" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F0F0F0"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <!-- 定义样式 -->
        <Style x:Key="TitleTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#0078D7"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>

        <Style x:Key="VersionTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
        
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="80"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="#E6E6E6"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#0078D7"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid>
        <!-- 主要信息区域 -->
        <Border Background="#E6E6E6" Padding="30" Margin="20"
                BorderBrush="#CCCCCC" BorderThickness="1">
            <StackPanel>
                <!-- 应用程序名称 -->
                <TextBlock Text="PNConfigTool" Style="{StaticResource TitleTextStyle}" Margin="0,10"/>

                <!-- 版本号 -->
                <TextBlock Text="版本 072714" Style="{StaticResource VersionTextStyle}" Margin="0,5"/>

                <!-- 应用程序描述 -->
                <TextBlock Text="PROFINET 网络配置工具" Style="{StaticResource InfoTextStyle}" Margin="0,5"/>
                <TextBlock Text="用于配置和管理 PROFINET 设备网络" Style="{StaticResource InfoTextStyle}" Margin="0,5"/>

                <!-- 版权信息 -->
                <TextBlock Text="© 2025 Siemens" Style="{StaticResource InfoTextStyle}" Margin="0,15,0,10"/>

            </StackPanel>
        </Border>
    </Grid>
</Window>
