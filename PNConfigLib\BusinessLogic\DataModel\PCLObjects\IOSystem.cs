/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IOSystem.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.HWCNBL;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents an IO system.
    /// </summary>
    public class IOSystem : PclObject
    {
        /// <summary>
        /// IO devices that are connected to this IO system.
        /// </summary>
        private List<PNIOD> m_Participants = new List<PNIOD>();

        /// <summary>
        /// The IO Controller of the IO system.
        /// </summary>
        private PNIOC m_PNIOc;

        /// <summary>
        /// The constructor of IO system.
        /// </summary>
        /// <param name="ioSystemID">The ID of this IO system.</param>
        public IOSystem(string ioSystemID)
        {
            Id = ioSystemID;
        }

        /// <summary>
        /// Getter for the IO controller.
        /// </summary>
        public PNIOC PNIOC
        {
            get { return m_PNIOc; }
        }

        /// <summary>
        /// The Subnet that this PNIOd is connected to.
        /// </summary>
        public Subnet Subnet { get; set; }

        /// <summary>
        /// Adds an IO device to the IO system.
        /// </summary>
        /// <param name="PNIOD">The IO device to be added.</param>
        public void AddParticipant(PNIOD PNIOD)
        {
            if (PNIOD == null)
            {
                throw new ArgumentNullException(nameof(PNIOD));
            }

            m_Participants.Add(PNIOD);
            PNIOD.IOSystem = this;
        }

        /// <summary>
        /// Gets the list of IO devices connected to this IO system.
        /// </summary>
        /// <returns>A list containing IO devices connected to this IO system.</returns>
        public IList<PNIOD> GetParticipants()
        {
            return m_Participants;
        }

        /// <summary>
        /// Setter method for the IO controller.
        /// </summary>
        /// <exception cref="PNFunctionsException">if a pnIOC is already set.</exception>
        /// <param name="PNIOC">The IO controller to be set.</param>
        public void SetPNIOC(PNIOC pnIOC)
        {
            // We should not be setting this twice.
            if ((m_PNIOc == null)
                && (pnIOC != null))
            {
                m_PNIOc = pnIOC;
                pnIOC.IOSystem = this;
            }
            else
            {
                throw new PNFunctionsException("PNIOc of IOSystem can not be changed.");
            }

            RegisterWithAddressManager(this);
        }
    }
}