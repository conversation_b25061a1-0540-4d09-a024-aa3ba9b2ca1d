/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV01_00.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Xml;
using System.Xml.XPath;
using PNConfigLib.Gsd.Interpreter.Common;
using S = PNConfigLib.Gsd.Interpreter.Structure;
using GSDI;
using System.Globalization;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal class BuilderV0100 :
        BuilderObject
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0100()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version10);
            SetDefaultLanguage(Constants.s_En);

            m_Gsd = null;

            m_SStore = null;
            m_CStore = null;
            m_LanguageStore = null;

            m_Expressions = null;

            m_TextCache = null;
            m_PrimaryTextCache = null;

            m_GraphicItemCache = null;
            m_CategoryItemCache = null;
            m_CategoryItemInfoTextCache = null;
            m_ValueItemCache = null;


            m_ValueItemObjectCache = null;
            m_ValueItemObjectListCache = null;

            ValueListHelper = new ValueListHelper();

        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_SupportedGsdmlVersion;

        /// <summary>
        /// Accesses the default language, which is used for the actual
        /// internal builder.
        /// </summary>
        /// <permission cref="m_DefaultLanguage">Private member.</permission>
        private string m_DefaultLanguage;

        /// <summary>
        /// Accesses the XML document created from the given GSD file.
        /// </summary>
        /// <remarks>Ideally, we should use an instance of XPathDocument 
        /// to hold the document being transformed. It is optimized for 
        /// XPath processing. It doesn't contain all the validation and 
        /// structure overhead of the other document classes, and will 
        /// provide the best performance.</remarks>
        /// <permission cref="m_Gsd">Private member.</permission>
        private IXPathNavigable m_Gsd;

        /// <summary>
        /// Accesses the ModelStore object, which is used to hold the
        /// objects for the structure data object model.
        /// </summary>
        /// <permission cref="m_SStore">Private member.</permission>
        private ModelStore m_SStore;
        /// <summary>
        /// Accesses the ModelStore object, which is used to hold the
        /// objects for the common data object model.
        /// </summary>
        /// <permission cref="m_CStore">Private member.</permission>
        private ModelStore m_CStore;

        /// <summary>
        /// Accesses the compiled xpath expressions, which are prefabricated
        /// to get the relevant XML elements later at the specific creator
        /// methods.
        /// For the keys, there are the names of the elements from the GSDML 
        /// specification used.
        /// </summary>
        private Hashtable m_Expressions;

        /// <summary>
        /// Accesses the cache for the XML Text elements especially for the
        /// setted language.
        /// For the keys, there are the text IDs used and the values are the 
        /// language specific texts.
        /// </summary>
        /// <remarks>If primary language and needed language is equal, the
        /// text cache and the primary text cache are also equal.</remarks>
        private Hashtable m_TextCache;
        /// <summary>
        /// Accesses the cache for the XML Text elements especially for the
        /// primary language. The primary language is always english.
        /// For the keys, there are the text IDs used and the values are the 
        /// language specific texts.
        /// </summary>
        /// <remarks>If primary language and needed language is equal, the
        /// text cache and the primary text cache are also equal.</remarks>
        private Hashtable m_PrimaryTextCache;

        /// <summary>
        /// Accesses the cache for the XML graphic item elements from the 
        /// GSD file.
        /// Keys are the GSD IDs of the graphic item elements and the values
        /// are XPathNavigator objects, positioned on the specified XML
        /// element.
        /// </summary>
        private Hashtable m_GraphicItemCache;
        /// <summary>
        /// Accesses the cache for the XML category item elements from the 
        /// GSD file.
        /// Keys are the GSD IDs of the category item elements and the 
        /// values are XPathNavigator objects, positioned on the specified 
        /// XML element.
        /// </summary>
        private Hashtable m_CategoryItemCache;

        // Cache for category info texts
        private Hashtable m_CategoryItemInfoTextCache;

        /// <summary>
        /// Accesses the cache for the XML value item elements from the GSD
        /// file.
        /// Keys are the GSD IDs of the value item elements and the values
        /// are XPathNavigator objects, positioned on the specified XML
        /// element.
        /// </summary>
        private Hashtable m_ValueItemCache;

        /// <summary>
        /// Accesses the cache for the common data object model ValueItem
        /// objects. The objects are created during the build process at
        /// the first time they are needed. 
        /// Special is, that the related objects must be created and cached 
        /// not only for each available element, but also for each needed 
        /// data type. 
        /// Keys are the GSD IDs of the value item elements plus the specific
        /// data type. Values are hashtables, which contains values as keys 
        /// with the corresponding ValueItem object.
        /// </summary>
        /// <remarks>The XML value item, which is marked with a GSD ID,
        /// contains a list of assignments. Each assignment contains
        /// content values and corresponding texts and is mapped to a
        /// common data object model ValueItem object. 
        /// The problem, whether the cache must be separated for GSD ID 
        /// and data type is, that the XML value item element can be 
        /// referenced from elements needing different data types for 
        /// the content value. Therefore the assignments are created for 
        /// each needed data type.</remarks>
        private Hashtable m_ValueItemObjectCache;
        /// <summary>
        /// Accesses the cache for the common data object model ValueItem
        /// objects. The objects are created during the build process at
        /// the first time they are needed. 
        /// Special is, that the related objects must be created and cached 
        /// not only for each available element, but also for each needed 
        /// data type.
        /// Keys are the GSD IDs of the value item elements plus the specific
        /// data type. Unlike the normal value item object cache, the values 
        /// are array lists, which contains the related ValueItem objects.
        /// </summary>
        /// <remarks>The XML value item, which is marked with a GSD ID,
        /// contains a list of assignments. Each assignment contains
        /// content values and corresponding texts and is mapped to a
        /// common data object model ValueItem object. 
        /// The problem, whether the cache must be separated for GSD ID 
        /// and data type is, that the XML value item element can be 
        /// referenced from elements needing different data types for 
        /// the content value. Therefore the assignments are created for 
        /// each needed data type.</remarks>
        private Hashtable m_ValueItemObjectListCache;

        private ArrayList m_LanguageStore;

        private string m_Filename;

        #endregion

        //########################################################################################
        #region Properties

        protected virtual string DefaultLanguage => this.m_DefaultLanguage;

        protected virtual IXPathNavigable Gsd
        {
            get => this.m_Gsd;
            set => this.m_Gsd = value;
        }
        protected virtual ModelStore SStore
        {
            get => this.m_SStore;
            set => this.m_SStore = value;
        }
        protected virtual ModelStore CStore
        {
            get => this.m_CStore;
            set => this.m_CStore = value;
        }

        protected virtual ArrayList LanguageStore
        {
            get => this.m_LanguageStore;
            set => this.m_LanguageStore = value;
        }

        protected virtual Hashtable Expressions
        {
            get => this.m_Expressions;
            set => this.m_Expressions = value;
        }

        protected virtual Hashtable TextCache
        {
            get => this.m_TextCache;
            set => this.m_TextCache = value;
        }
        protected virtual Hashtable PrimaryTextCache
        {
            get => this.m_PrimaryTextCache;
            set => this.m_PrimaryTextCache = value;
        }

        protected virtual Hashtable GraphicItemCache
        {
            get => this.m_GraphicItemCache;
            set => this.m_GraphicItemCache = value;
        }
        protected virtual Hashtable CategoryItemCache
        {
            get => this.m_CategoryItemCache;
            set => this.m_CategoryItemCache = value;
        }
        protected virtual Hashtable CategoryItemInfoTextCache
        {
            get => this.m_CategoryItemInfoTextCache;
            set => this.m_CategoryItemInfoTextCache = value;
        }
        protected virtual Hashtable ValueItemCache
        {
            get => this.m_ValueItemCache;
            set => this.m_ValueItemCache = value;
        }

        protected virtual Hashtable ValueItemObjectCache
        {
            get => this.m_ValueItemObjectCache;
            set => this.m_ValueItemObjectCache = value;
        }
        protected virtual Hashtable ValueItemObjectListCache
        {
            get => this.m_ValueItemObjectListCache;
            set => this.m_ValueItemObjectListCache = value;
        }

        protected virtual string Filename
        {
            get => m_Filename;
            set => m_Filename = value;
        }

        protected ValueListHelper ValueListHelper { get; set; }

        #endregion

        //########################################################################################
        #region Methods

        protected void SetSupportedGsdmlVersion(string version)
        {
            m_SupportedGsdmlVersion = version;
        }
        protected void SetDefaultLanguage(string lang)
        {
            m_DefaultLanguage = lang;
        }

        #endregion


        //########################################################################################
        #region Build Models

        /// <summary>
        /// Accesses the GSDML version supported from this internal builder.
        /// </summary>
        public override string SupportedGsdmlVersion => this.m_SupportedGsdmlVersion;

        /// <summary>
        /// Builds the data object model(s) for the given file and language. The resulting
        /// data object model(s) are returned from the respective model store.
        /// </summary>
        /// <param name="gsddocstream">Stream of the GSD document to check.</param>
        /// <param name="pathname">Name of the GSD file, for which the data object
        /// model(s) should be created.</param>
        /// <param name="lModelOption">Specifies the model, which should be created.
        /// Only structure, only common, or both.</param>
        /// <param name="language">The language, which should be used for the texts
        /// from the GSD file.</param>
        /// <param name="sstore">Store for the resulting structure model.</param>
        /// <param name="cstore">Store for the resulting common model.</param>
        /// <param name="languageStore"></param>
        /// <returns>True if creation of the data object model(s) succeeds,
        /// else false. If an error occurres during creation, false is returned
        /// and the stores are set to null.</returns>
        public override bool BuildModels(Stream gsddocstream, string pathname, ModelOptions lModelOption,
            string language, ref ModelStore sstore, ref ModelStore cstore, ref ArrayList languageStore)
        {
            // Set local variables.
            bool succeeded = true;
            Filename = Path.GetFileName(pathname);

            try
            {
                // Init complete build process.
                succeeded = this.InitBuildProcess(gsddocstream, pathname, language);
                if (!succeeded)
                {
                    return false;
                }

                // Create structure data object model.
                if ((lModelOption == ModelOptions.GSDStructure) ||
                    (lModelOption == ModelOptions.GSDCommonAndStructure))
                {
                    sstore = null;
                    sstore = new ModelStore();
                    sstore.Model = StoreModels.Structure;
                    this.SStore = sstore;

                    this.CreateStructureModel();
                }

                // Create common data object model.
                if ((lModelOption == ModelOptions.GSDCommon) ||
                    (lModelOption == ModelOptions.GSDCommonAndStructure))
                {
                    cstore = null;
                    cstore = new ModelStore();
                    cstore.Model = StoreModels.Common;
                    this.CStore = cstore;

                    this.CreateCommonModel();
                }

                languageStore = new ArrayList();
                this.LanguageStore = languageStore;
                FillLanguageStore();

                // After build actions.
                succeeded = this.AfterBuildActions(ref sstore, ref cstore);
            }
            finally
            {
                // Clear internal variables.
                this.ClearMemberVariables();
            }

            return succeeded;
        }

        #region Helper Methods

        /// <summary>
        /// Creates the structure data object model. First, the submodules are created,
        /// than the modules, after that the access points and finally the device.
        /// </summary>
        protected virtual void CreateStructureModel()
        {

            try
            {
                this.CreateRecordDataStructureElements();

                // Create all physical submodules and add them to the store.
                // NOTE: At the moment, there are no submodules available, which are relevant for
                //       structure object model!
                this.CreateSubmoduleStructureElements();

                // Create all modules and add them to the store.
                this.CreateModuleStructureElements();

                // Create the communication interfaces and add it to the store.
                this.CreateCommunicationInterfaceStructureElements();

                // Create all Device AccessPointStructureElement objects and add them to the store.
                this.CreateDeviceAccessPointStructureElements();

                // Create the device and add it to the store.
                this.CreateDeviceStructureElement();

            }
            catch (CreationException e)
            {
                throw new CreationException("Couldn't create 'Structure' model!", e);
            }
        }
        /// <summary>
        /// Creates the common data object model. First, the submodules are created,
        /// than the modules, after that the access points and finally the device.
        /// </summary>
        protected virtual void CreateCommonModel()
        {
            try
            {

                // Create all virtual submodules and add them to the store.
                this.CreateRecordDataItems();

                // Create all virtual submodules and add them to the store.
                this.CreateVirtualSubmodules();

                // Create all modules and add them to the store.
                this.CreateModules();

                // Create all communication interfaces and add them to the store.
                this.CreateCommunicationInterfaces();

                // Create all DeviceAccessPoint objects and add them to the store.
                this.CreateDeviceAccessPoints();

                // Create the device and add it to the store.
                this.CreateDevice();
            }
            catch (CreationException)
            {
                throw;
            }

        }

        #region AfterBuildActions

        /// <summary>
        /// Activities needed after the creation of the data object model(s) succeeds.
        /// Entries are made, which needs the access to the complete model. For example,
        /// the vendor name of some modules (submodules, modules, access points) is set,
        /// if it isn't done from the GSD file. The rule is, that if an module has no
        /// vendor name entry, the entry of the parent is used.
        /// </summary>
        /// <param name="sstore">Store for the complete structure data object model. Null
        /// if structure model isn't created for the actual GSD.</param>
        /// <param name="cstore">Store for the complete common data object model. Null
        /// if common model isn't created for the actual GSD.</param>
        /// <returns>True if actions are normally done, else false.</returns>
        protected virtual bool AfterBuildActions(ref ModelStore sstore, ref ModelStore cstore)
        {
            // Set local variables.
            Hashtable hash = new();
            hash.Add(Models.s_FieldVendorName, null);
            hash.Add(Models.s_FieldMainFamily, null);
            hash.Add(Models.s_FieldMainFamilyAsEnum, null);
            hash.Add(Models.s_FieldProductFamily, null);

            // Set correct VendorName on each ModuleInfo.
            AfterBuildActionsStructureDataObjectModel(sstore, hash);

            AfterBuildActionsCommonDataObjectModel(cstore, hash);


            return true;

        }
        private static void AfterBuildActionsCommonDataObjectModel(ModelStore cstore, Hashtable hash)
        {
            if (null == cstore)
            {
                return;
            }

            Stack st = new Stack();
            Stack stMf = new Stack();
            Stack stPf = new Stack();

            st.Push(((GsdDevice)cstore.Device).Info.VendorName);
            stMf.Push(((GsdDevice)cstore.Device).Info.MainFamily);
            stPf.Push(((GsdDevice)cstore.Device).Info.ProductFamily);

            foreach (DeviceAccessPoint im in ((GsdDevice)cstore.Device).DeviceAccessPoints)
            {
                // Prepare (new) values.
                hash[Models.s_FieldVendorName] = im.Info.VendorName;
                if (String.IsNullOrEmpty(im.Info.VendorName))	// Info is a must here!
                    hash[Models.s_FieldVendorName] = st.Peek();

                hash[Models.s_FieldMainFamily] = im.Info.MainFamily;
                if (String.IsNullOrEmpty(im.Info.MainFamily))
                {
                    hash[Models.s_FieldMainFamily] = stMf.Peek();
                    if (Enums.IsFamilyEnumValueConvertable((string)stMf.Peek()))
                        hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum((string)stMf.Peek());
                }
                else if (Enums.IsFamilyEnumValueConvertable(im.Info.MainFamily))
                    hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(im.Info.MainFamily);

                hash[Models.s_FieldProductFamily] = im.Info.ProductFamily;
                if (String.IsNullOrEmpty(im.Info.ProductFamily))	// Info is a must here!
                    hash[Models.s_FieldProductFamily] = stPf.Peek();

                // Set (new) values.
                (im.Info).Fill(hash);

                // Write to stacks.
                st.Push(im.Info.VendorName);
                stMf.Push(im.Info.MainFamily);
                stPf.Push(im.Info.ProductFamily);

                // Set on Submodules and Modules.
                FillVirtualSubmodulesForCommonDataObjectModel(hash, im, st, stMf, stPf);
                FillModulesForCommonDataObjectModel(hash, im, st, stMf, stPf);

                st.Pop();
                stMf.Pop();
                stPf.Pop();
            }
            st.Pop();
            stMf.Pop();
            stPf.Pop();
        }


        private static void FillModulesForCommonDataObjectModel(
           Hashtable hash,
           DeviceAccessPoint im,
           Stack st,
           Stack stMf,
           Stack stPf)
        {
            if (im.Modules == null)
            {
                return;
            }

            foreach (Module m in im.Modules)
            {
                // Cast IModuleInfo to concrete ModuleInfo type
                if (m.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo moduleInfo)
                {
                    // Prepare (new) values.
                    hash[Models.s_FieldVendorName] = moduleInfo.VendorName;
                    if (string.IsNullOrEmpty(moduleInfo.VendorName))    // Info is a must here!
                        hash[Models.s_FieldVendorName] = st.Peek();

                    hash[Models.s_FieldMainFamily] = moduleInfo.MainFamily;
                    if (string.IsNullOrEmpty(moduleInfo.MainFamily))
                    {
                        hash[Models.s_FieldMainFamily] = stMf.Peek();
                        if (Enums.IsFamilyEnumValueConvertable((string)stMf.Peek()))
                            hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum((string)stMf.Peek());
                    }
                    else if (Enums.IsFamilyEnumValueConvertable(moduleInfo.MainFamily))
                        hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(moduleInfo.MainFamily);

                    hash[Models.s_FieldProductFamily] = moduleInfo.ProductFamily;
                    if (string.IsNullOrEmpty(moduleInfo.ProductFamily)) // Info is a must here!
                        hash[Models.s_FieldProductFamily] = stPf.Peek();

                    // Set (new) values.
                    moduleInfo.Fill(hash);

                    // Write to stacks.
                    st.Push(moduleInfo.VendorName);
                    stMf.Push(moduleInfo.MainFamily);
                    stPf.Push(moduleInfo.ProductFamily);
                }

                // Set on Submodules.
                FillVirtualSubmodulesForCommonDataObjectModel(hash, im, st, stMf, stPf);

                st.Pop();
                stMf.Pop();
                stPf.Pop();
            }
        }


        private static void FillVirtualSubmodulesForCommonDataObjectModel(
             Hashtable hash,
             DeviceAccessPoint im,
             Stack st,
             Stack stMf,
             Stack stPf)
        {
            if (im.VirtualSubmodules == null)
            {
                return;
            }

            foreach (VirtualSubmodule sm in im.VirtualSubmodules)
            {
                if (null == sm.Info)
                {
                    continue;
                }

                // Cast IModuleInfo to concrete ModuleInfo type
                if (sm.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo submoduleInfo)
                {
                    // Prepare (new) values.
                    hash[Models.s_FieldVendorName] = submoduleInfo.VendorName;
                    if (String.IsNullOrEmpty(submoduleInfo.VendorName))
                        hash[Models.s_FieldVendorName] = st.Peek();

                    hash[Models.s_FieldMainFamily] = submoduleInfo.MainFamily;
                    if (String.IsNullOrEmpty(submoduleInfo.MainFamily))
                    {
                        hash[Models.s_FieldMainFamily] = stMf.Peek();
                        if (Enums.IsFamilyEnumValueConvertable((string)stMf.Peek()))
                            hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum((string)stMf.Peek());
                    }
                    else if (Enums.IsFamilyEnumValueConvertable(submoduleInfo.MainFamily))
                        hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(submoduleInfo.MainFamily);

                    hash[Models.s_FieldProductFamily] = submoduleInfo.ProductFamily;
                    if (String.IsNullOrEmpty(submoduleInfo.ProductFamily))
                        hash[Models.s_FieldProductFamily] = stPf.Peek();

                    // Set (new) values.
                    submoduleInfo.Fill(hash);
                }
            }
        }

        private static void AfterBuildActionsStructureDataObjectModel(ModelStore sstore, Hashtable hash)
        {
            if (null == sstore)
            {
                return;
            }

            Stack st = new Stack();
            Stack stMf = new Stack();
            Stack stPf = new Stack();

            st.Push(((S.DeviceStructureElement)sstore.Device).VendorName);
            stMf.Push(((S.DeviceStructureElement)sstore.Device).MainFamily);
            stPf.Push(((S.DeviceStructureElement)sstore.Device).ProductFamily);

            foreach (S.AccessPointStructureElement im in ((S.DeviceStructureElement)sstore.Device).DeviceAccessPoints)
            {
                // Prepare (new) values.
                hash[Models.s_FieldVendorName] = im.VendorName;
                if (String.IsNullOrEmpty(im.VendorName))
                    hash[Models.s_FieldVendorName] = st.Peek();

                hash[Models.s_FieldMainFamily] = im.MainFamily;
                if (String.IsNullOrEmpty(im.MainFamily))
                {
                    hash[Models.s_FieldMainFamily] = stMf.Peek();
                    if (Enums.IsFamilyEnumValueConvertable((string)stMf.Peek()))
                        hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum((string)stMf.Peek());
                }
                else if (Enums.IsFamilyEnumValueConvertable(im.MainFamily))
                    hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(im.MainFamily);

                hash[Models.s_FieldProductFamily] = im.ProductFamily;
                if (String.IsNullOrEmpty(im.ProductFamily))
                    hash[Models.s_FieldProductFamily] = stPf.Peek();

                // Set (new) values.
                (im).Fill(hash);

                // Write to stacks.
                st.Push(im.VendorName);
                stMf.Push(im.MainFamily);
                stPf.Push(im.ProductFamily);

                FillModuleStructureElementForStructureDataObjectModel(hash, im, st, stMf, stPf);
                st.Pop();
                stMf.Pop();
                stPf.Pop();
            }

            st.Pop();
            stMf.Pop();
            stPf.Pop();
        }


        private static void FillModuleStructureElementForStructureDataObjectModel(
            Hashtable hash,
            S.AccessPointStructureElement im,
            Stack st,
            Stack stMf,
            Stack stPf)
        {

            if (im.Modules == null)
            {
                return;
            }
            // Set on Modules.
            foreach (S.ModuleStructureElement m in im.Modules)
            {
                // Prepare (new) values.
                hash[Models.s_FieldVendorName] = m.VendorName;
                if (String.IsNullOrEmpty(m.VendorName))
                    hash[Models.s_FieldVendorName] = st.Peek();

                hash[Models.s_FieldMainFamily] = m.MainFamily;
                if (String.IsNullOrEmpty(m.MainFamily))
                {
                    hash[Models.s_FieldMainFamily] = stMf.Peek();
                    if (Enums.IsFamilyEnumValueConvertable((string)stMf.Peek()))
                        hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum((string)stMf.Peek());
                }
                else if (Enums.IsFamilyEnumValueConvertable(im.MainFamily))
                    hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(m.MainFamily);

                hash[Models.s_FieldProductFamily] = m.ProductFamily;
                if (String.IsNullOrEmpty(m.ProductFamily))
                    hash[Models.s_FieldProductFamily] = stPf.Peek();

                // Set (new) values.
                ((GsdObject)(m)).Fill(hash);

            }
        }


        protected virtual void FillLanguageStore()
        {
            // Select all DeviceAccessPoints
            XPathNavigator nav = Gsd.CreateNavigator();

            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_Language]);

            LanguageStore.Add(DefaultLanguage);
            while (nodes.MoveNext())
            {
                // Get specified language.
                if (nodes.Current == null)
                {
                    continue;
                }

                string lang = nodes.Current.XmlLang;
                lang = lang.Substring(0, 2).ToUpperInvariant().ToLower(CultureInfo.CurrentCulture);
                LanguageStore.Add(lang);
            }
        }


        /// <summary>
        /// Clears needed internal member variables.
        /// </summary>
        protected virtual void ClearMemberVariables()
        {
            Gsd = null;

            SStore = null;
            CStore = null;

            Expressions = null;

            TextCache = null;
            PrimaryTextCache = null;

            GraphicItemCache = null;
            CategoryItemCache = null;
            ValueItemCache = null;

            ValueItemObjectCache = null;
            ValueItemObjectListCache = null;

        }


        #endregion

        #endregion

        //########################################################################################
        #region Inititialization of the build process

        /// <summary>
        /// Initializes the hashtable for collecting compiled xpath expressions,
        /// which are used during the build process to find the relevant elements.
        /// Compiled xpath expressions are only used for global and major elements
        /// or elements, which are needed oftener.
        /// </summary>
        /// <returns>True if the initialization and compile of the xpath
        /// expressions succeeds, else false.</returns>
        protected virtual bool InitExpressions()
        {
            bool succeeded = true;

            try
            {
                // --------------------------------------------------------------
                // Check needed xml document.
                if (null == Gsd)
                {
                    return false;
                }

                if (null == Expressions)
                {
                    // Create document navigator.
                    XPathNavigator nav = Gsd.CreateNavigator();

                    if (nav == null)
                    {
                        return false;
                    }

                    // Create the NamespaceManager and add all XML Namespaces to it.
                    XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                    nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                    nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                    nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                    nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                    nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                    // Create compiled XPathExpressions.
                    if (null == Expressions)
                        Expressions = new Hashtable();

                    // Virtual Submodules
                    XPathExpression expr = nav.Compile(XPathes.AllVirtualSubmoduleItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_VirtualSubmoduleItem, expr);
                    // Modules
                    expr = nav.Compile(XPathes.AllModuleItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_ModuleItem, expr);
                    // DeviceAccessPoints
                    expr = nav.Compile(XPathes.AllDeviceAccessPointItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_DeviceAccessPointItem, expr);
                    // ProfileBody - Device, DeviceInfo, DeviceStructureElement
                    expr = nav.Compile(XPathes.ProfileBody);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_ProfileBody, expr);

                    // Categories
                    expr = nav.Compile(XPathes.AllCategoryItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_CategoryItem, expr);
                    // Graphics
                    expr = nav.Compile(XPathes.AllGraphicItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_GraphicItem, expr);
                    // ValueItem
                    expr = nav.Compile(XPathes.AllValueItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_ValueItem, expr);

                    // ChannelDiagItem
                    expr = nav.Compile(XPathes.AllChannelDiagItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_ChannelDiagItem, expr);
                    // UnitDiagTypeItem
                    expr = nav.Compile(XPathes.AllUnitDiagTypeItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_UnitDiagTypeItem, expr);

                    // Language
                    expr = nav.Compile(XPathes.AllLanguages);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_Language, expr);

                    // PrimaryLanguage
                    expr = nav.Compile(XPathes.PrimaryLanguage);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_PrimaryLanguage, expr);
                }
            }
            catch (XPathException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Initializes the caches (properties m...Cache), which are needed
        /// during the build process, for better access to special, frequently
        /// used XML elements and other objects.
        /// </summary>
        /// <param name="pathname">Name of the GSD (with path) for which the 
        /// data object models should be built. It is needed for the text cache, 
        /// searching for separate external text files for the actual needed 
        /// language, if it isn't contained internally.</param>
        /// <param name="language">Language, which should be used to create the
        /// text entries within the data object model(s).</param>
        /// <returns>True if caches could be initialized correctly, else false.</returns>
        protected virtual bool InitCaches(string pathname, string language)
        {
            try
            {
                // Create needed navigator.
                XPathNavigator nav = Gsd.CreateNavigator();

                // Make CategoryItem cache.
                InitCategoryItemCache(nav);

                // Make GraphicItem cache.
                InitGraphicItemCache(nav);

                // Make ValueItem cache.
                InitValueItemCache(nav);

                // ------------------------------------------------------------------
                // Text caches.

                // Make primary text cache (english language).
                if (!InitPrimaryTextCache(nav))
                {
                    return false;
                }

                // Make text cache (given language).
                InitTextCache(pathname, language, nav);
            }
            catch (Exception e)
            {
                throw new InterpreterException("Exception in InitChaches", e);
            }


            return true;
        }

        private void InitTextCache(string pathname, string language, XPathNavigator nav)
        {
            if (null != this.TextCache)
            {
                return;
            }

            // Check whether needed language is primary language.
            if (language == this.DefaultLanguage)
            {
                // Use primary cache.
                this.TextCache = this.PrimaryTextCache;
            }
            else
            {

                bool found = false;
                XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_Language]);

                while (nodes.MoveNext())
                {
                    // Get specified language.
                    if (nodes.Current != null)
                    {
                        string lang = nodes.Current.XmlLang;
                        lang = lang.Substring(0, 2).ToUpperInvariant().ToLower(CultureInfo.CurrentCulture);

                        // Check language specifier.
                        if (lang != language)
                        {
                            continue;
                        }
                    }

                    // Make text cache.
                    TextCache = null;
                    TextCache = new Hashtable();

                    BuildTextCache(nodes);

                    found = true;
                    // Break while loop, because we are ready and nodes object is no longer correct!
                    break;  // ------------------------->

                }

                // Reader -------------------------------------------------------
                // Check whether needed language was internally supported.
                found = InitTextCacheFromFile(pathname, language, found);

                // Check whether needed language was externally supported.
                if (found)
                {
                    return;
                }

                // If needed language is whether internally or externally supported, use PrimaryLnaguage!
                this.TextCache = this.PrimaryTextCache;
            }
        }


        private void BuildTextCache(XPathNodeIterator nodes)
        {
            XPathNavigator tempnav = nodes.Current;
            if (tempnav != null)
            {
                nodes = tempnav.SelectChildren(Elements.s_Text, Namespaces.s_GsdmlDeviceProfile);
            }

            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sT = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                string sV = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);
                TextCache.Add(sT, sV);
            }
        }
        private bool InitTextCacheFromFile(string pathname, string language, bool found)
        {
            if (found)
            {
                return true;
            }

            // Check whether needed language is externally supported.
            FileInfo fileinfo = new FileInfo(pathname);

            // Check whether language is externally supported.
            string extdir = fileinfo.DirectoryName + Constants.s_DoubleBackSlash + language;
            if (!Directory.Exists(extdir))
            {
                return false;
            }

            string extpathname = extdir + Constants.s_DoubleBackSlash + fileinfo.Name.Substring(0,
                                                                                                          fileinfo.Name.LastIndexOf(Constants.s_XmlFileType, StringComparison.Ordinal)) +
                                             Constants.s_ExternalLanguageFileExtension + language + Constants.s_XmlFileType;
            if (!File.Exists(extpathname))
            {
                return false;
            }
            // Make text cache.
            TextCache = null;
            TextCache = new Hashtable();

            // Load XML document.
            FileStream filestream = null;
            try
            {
                // Create filestream.
                filestream = new FileStream(extpathname, FileMode.Open, FileAccess.Read);

                // Load Gsd from file stream.
                XmlTextReader tr = new XmlTextReader(filestream);
                // Set Dtd processing to ignore 
                tr.DtdProcessing = DtdProcessing.Ignore;

                while (tr.Read())
                {
                    if (tr.NodeType != XmlNodeType.Element
                    || tr.LocalName != "Text")
                    {
                        continue;
                    }
                    string sT = tr.GetAttribute(Attributes.s_TextId);
                    string sV = tr.GetAttribute(Attributes.s_Value);
                    this.TextCache.Add(sT, sV);
                }
            }
            catch (Exception e)
            {
                TextCache = null;
                throw new InterpreterException("The file '" + extpathname + "' could not be read.", e);
            }
            finally
            {
                // Close file if it is opened.
                if (null != filestream)
                    filestream.Close();
                filestream = null;
            }

            return true;

        }


        private bool InitPrimaryTextCache(XPathNavigator nav)
        {
            if (null != this.PrimaryTextCache)
            {
                return true;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_PrimaryLanguage]);

            // Check whether elements are available.
            if (nodes.Count == 0)
            {
                return false;
            }

            // Make text cache.
            this.PrimaryTextCache = new Hashtable();

            if (!nodes.MoveNext())
            {
                return true;
            }

            XPathNavigator tempnav = nodes.Current;
            if (tempnav != null)
            {
                nodes = tempnav.SelectChildren(Elements.s_Text, Namespaces.s_GsdmlDeviceProfile);
            }

            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sT = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                string sV = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);
                PrimaryTextCache.Add(sT, sV);
            }


            return true;
        }

        private void InitValueItemCache(XPathNavigator nav)
        {
            if (null != this.ValueItemCache)
            {
                return;
            }

            ValueItemCache = new Hashtable();

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ValueItem]);
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string attr = nodes.Current.GetAttribute(Attributes.ID, String.Empty);
                ValueItemCache.Add(attr, nodes.Current.Clone());
            }

        }

        private void InitGraphicItemCache(XPathNavigator nav)
        {
            if (null != this.GraphicItemCache)
            {
                return;
            }

            GraphicItemCache = new Hashtable();

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_GraphicItem]);
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }
                string attr = nodes.Current.GetAttribute(Attributes.ID, String.Empty);
                GraphicItemCache.Add(attr, nodes.Current.Clone());
            }
        }

        private void InitCategoryItemCache(XPathNavigator nav)
        {
            if (null != this.CategoryItemCache)
            {
                return;
            }

            CategoryItemCache = new Hashtable();
            CategoryItemInfoTextCache = new Hashtable();

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_CategoryItem]);
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string attr = nodes.Current.GetAttribute(Attributes.ID, String.Empty);
                CategoryItemCache.Add(attr, nodes.Current.Clone());

                XPathNodeIterator infotextIterator = nodes.Current.SelectChildren(
                    Elements.s_InfoText,
                    Namespaces.s_GsdmlDeviceProfile);
                if (!infotextIterator.MoveNext())
                {
                    continue;
                }
                if (infotextIterator.Current != null)
                {
                    CategoryItemInfoTextCache.Add(attr, infotextIterator.Current.Clone());
                }
            }

        }

        /// <summary>
        /// Initializes the complete build process, which includes also the 
        /// initialization of the namespaces, converters, xpath expressions
        /// and caches.
        /// </summary>
        /// <param name="gsddocstream">Stream of the GSD document to check.</param>
        /// <param name="pathname">Name of the GSD (with path) for which the 
        /// data object models should be built.</param>
        /// <param name="language">Language, which should be used to create the
        /// text entries within the data object model(s).</param>
        /// <returns>True if intializations are successful, else false.</returns>
        protected virtual bool InitBuildProcess(Stream gsddocstream, string pathname, string language)
        {
            FileStream filestream = null;
            Stream docstream;

            try
            {
                // If document stream isn't given, try to get stream with pathname.
                if (null == gsddocstream)
                {
                    // Check whether file exists and open it if available.
                    if (!File.Exists(pathname))
                        throw new IOException("Can't find Gsd file with the given path: '" + pathname + "'!");

                    // Create filestream.
                    filestream = new FileStream(pathname, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    docstream = filestream;
                }
                else
                    docstream = gsddocstream;

                // Load Gsd from file stream.
                this.Gsd = new XmlDocument();
                ((XmlDocument)this.Gsd).Load(docstream);


                // Init needed compiled XPath expressions.
                bool succeeded = this.InitExpressions();
                if (!succeeded)
                {
                    return false;	// ---------->
                }

                // Init caches.
                succeeded = this.InitCaches(pathname, language);
                if (!succeeded)
                {
                    return false;	// ---------->
                }

            }
            catch (XmlException e)
            {
                throw new InterpreterException("Error in InitBuildProcess", e);
            }
            finally
            {
                // Close file if it is opened.
                if (null != filestream)
                    filestream.Close();
                filestream = null;
            }

            return true;
        }


        #endregion

        //########################################################################################
        #region Model Helper Methods

        protected virtual object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            GsdObject obj;

            try
            {
                switch (name)
                {
                    // ------------- STRUCTURE ELEMENTS -----------------------
                    case Models.s_ObjectDeviceStructureElement:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            PrepareDeviceStructureElement(nav, ref hash);
                            obj = new S.DeviceStructureElement();

                            break;
                        }
                    case Models.s_ObjectAccessPointStructureElement:
                        {
                            // NOTE: Navigator must point to ...AccessPointItem.
                            PrepareAccessPointStructureElement(nav, ref hash);
                            obj = new S.AccessPointStructureElement();

                            break;
                        }
                    case Models.s_ObjectModuleStructureElement:
                        {
                            // NOTE: Navigator must point to ModuleItem.
                            PrepareModuleStructureElement(nav, ref hash);
                            obj = new S.ModuleStructureElement();

                            break;
                        }
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectDevice:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            PrepareDevice(nav, ref hash);
                            obj = new GsdDevice();

                            break;
                        }
                    case Models.s_ObjectDeviceAccessPoint:
                        {
                            // NOTE: Navigator must point to DeviceAccessPointItem.
                            PrepareDeviceAccessPoint(nav, ref hash);
                            obj = new DeviceAccessPoint();

                            break;
                        }
                    case Models.s_ObjectModule:
                        {
                            // NOTE: Navigator must point to ModuleItem.
                            PrepareModule(nav, ref hash);
                            obj = new Module();

                            break;
                        }
                    case Models.s_ObjectVirtualSubmodule:
                        {
                            // NOTE: Navigator must point to VirtualSubmoduleItem.
                            PrepareVirtualSubmodule(nav, ref hash);
                            obj = new VirtualSubmodule();

                            break;
                        }
                    case Models.s_ObjectChannelDiagnostic:
                        {
                            // NOTE: Navigator must point to ChannelDiagItem.
                            PrepareChannelDiagnostic(nav, ref hash);
                            obj = new ChannelDiagnostic();

                            break;
                        }
                    case Models.s_ObjectUnitDiagnosticType:
                        {
                            // NOTE: Navigator must point to UnitDiagTypeItem.
                            PrepareUnitDiagnosticType(nav, ref hash);
                            obj = new UnitDiagnosticType();

                            break;
                        }
                    case Models.s_ObjectDeviceInfo:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            PrepareDeviceInfo(nav, ref hash);
                            obj = new DeviceInfo();

                            break;
                        }
                    case Models.s_ObjectModuleInfo:
                        {
                            // NOTE: Navigator must point to ModuleInfo.
                            PrepareModuleInfo(nav, ref hash);
                            obj = new ModuleInfo();

                            break;
                        }
                    case Models.s_ObjectGraphic:
                        {
                            // NOTE: Navigator must point to GraphicItemRef.
                            PrepareGraphic(nav, ref hash);
                            obj = new Graphic();

                            break;
                        }
                    case Models.s_ObjectIoData:
                        {
                            // NOTE: Navigator must point to IOData.
                            PrepareIOData(nav, ref hash);
                            obj = new IOData();

                            break;
                        }
                    case Models.s_ObjectDataItem:
                        {
                            // NOTE: Navigator must point to DataItem.
                            PrepareDataItem(nav, ref hash);
                            obj = new DataItem();

                            break;
                        }
                    case Models.s_ObjectParameterRecordData:
                        {
                            // NOTE: Navigator must point to ParameterRecordDataItem.
                            PrepareParameterRecordData(nav, ref hash);
                            obj = new ParameterRecordData();

                            break;
                        }
                    case Models.s_ObjectConstData:
                        {
                            // NOTE: Navigator must point to Const.
                            PrepareConstData(nav, ref hash);
                            obj = new ConstData();

                            break;
                        }
                    case Models.s_ObjectRefData:
                        {
                            // NOTE: Navigator must point to Ref.
                            PrepareRefData(nav, ref hash);
                            obj = new RefData();

                            break;
                        }
                    case Models.s_ObjectIoConfigData:
                        {
                            // NOTE: Navigator must point to IOConfigData.
                            PrepareIOConfigData(nav, ref hash);
                            obj = new IOConfigData();

                            break;
                        }
                    case Models.s_ObjectTimingProperties:
                        {
                            // NOTE: Navigator must point to TimingProperties.
                            PrepareTimingProperties(nav, ref hash);
                            obj = new TimingProperties();

                            break;
                        }
                    case Models.s_ObjectApplicationRelations:
                        {
                            // NOTE: Navigator must point to ApplicationRelations.
                            PrepareApplicationRelations(nav, ref hash);
                            obj = new ApplicationRelations();

                            break;
                        }
                    case Models.s_ObjectModulePlugData:
                        {
                            // NOTE: Navigator must point to ModuleItemRef.
                            PrepareModulePlugData(nav, ref hash);
                            obj = new ModulePlugData();

                            break;
                        }
                    // --------------------------------------------------------
                    default:
                        {
                            throw new ArgumentException("No Gsd object with name '" + name + "' found!");
                        }
                }

                // Fill object with data.
                bool succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(name + " couldn't be filled with data!");
            }
            catch (PreparationException)
            {
                System.Diagnostics.Debug.Assert(false);
                obj = null;
            }

            return obj;
        }

        protected virtual string GetText(XPathNavigator nav)
        {
            try
            {
                // Get attribute TextId and assigned text.
                string attr = nav.GetAttribute(Attributes.s_TextId, String.Empty);
                if (!TextCache.ContainsKey(attr))
                {
                    if (PrimaryTextCache.ContainsKey(attr))
                        return (string)PrimaryTextCache[attr];
                }
                else
                    return (string)TextCache[attr];
            }
            catch (XmlException)
            {
                return String.Empty;
            }
            catch (XPathException)
            {
                return String.Empty;
            }


            return String.Empty;
        }

        protected virtual Hashtable GetValueItemObjectCache(string valuegsdid, DataTypes type)
        {
            Hashtable cache = new Hashtable();

            try
            {
                // At the first time, create cache hashtable.
                if (null == ValueItemObjectCache)
                    ValueItemObjectCache = new Hashtable();
                if (null == ValueItemObjectListCache)
                    ValueItemObjectListCache = new Hashtable();

                // If the value items with this type exist, only return it.
                string key = valuegsdid + type;
                if (ValueItemObjectCache.ContainsKey(key))
                    return (Hashtable)ValueItemObjectCache[key];

                // Else create needed cache.
                Hashtable hash = new Hashtable();
                // Add all needed entries as null references to avoid later exceptions.
                hash.Add(Constants.s_Value, null);
                hash.Add(Constants.s_Text, null);
                hash.Add(Constants.s_TextId, null);

                XPathNavigator nav = (XPathNavigator)ValueItemCache[valuegsdid];
                if (nav == null)
                {
                    return cache;
                }

                XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_Assign, Namespaces.s_GsdmlDeviceProfile, false);

                ArrayList list = new ArrayList();

                while (nodes.MoveNext())
                {
                    // Create value item.
                    hash[Constants.s_Text] = GetText(nodes.Current);
                    if (nodes.Current == null)
                    {
                        continue;
                    }

                    hash[Constants.s_TextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

                    string attr = nodes.Current.GetAttribute(Attributes.s_Content, String.Empty);
                    hash[Constants.s_Value] = Enums.GetDataTypeObject(attr, type);
                    if (null != hash[Constants.s_Value])
                    {
                        // Create object and fill with data
                        ValueItem oValueItem = new ValueItem();
                        bool succeeded = oValueItem.Fill(hash);
                        if (!succeeded)
                        {
                            return null;
                        }

                        // Add created object to cache.
                        if (!cache.ContainsKey(oValueItem.Value))
                        {
                            cache.Add(oValueItem.Value, oValueItem);
                            list.Add(oValueItem);
                        }
                    }
                }

                // Add cache to ValueItemObjectCache.
                ValueItemObjectCache.Add(key, cache);
                ValueItemObjectListCache.Add(key, list);

            }
            catch (Exception e)
            {
                throw new InterpreterException("Cannot get ValueItem for ID '" + valuegsdid + "' and type '" + type + "'", e);
            }

            return cache;
        }


        #endregion

        //########################################################################################
        #region Structure Model Methods
        #endregion
        #region Preparation

        protected virtual void PrepareStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare StructureElement data with all sub components.
            // NOTE: At the moment, there are only Modules and AccessPoints available for the
            //       Structure data object model.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldGsdId, null);
            hash.Add(Models.s_FieldIdentNumber, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldInfoText, null);
            hash.Add(Models.s_FieldCategory, null);
            hash.Add(Models.s_FieldCategoryGsdId, null);
            hash.Add(Models.s_FieldCategoryInfoText, null);
            hash.Add(Models.s_FieldCategoryInfoTextId, null);
            hash.Add(Models.s_FieldSubCategory1, null);
            hash.Add(Models.s_FieldSubCategory1GsdId, null);
            hash.Add(Models.s_FieldSubCategory1InfoText, null);
            hash.Add(Models.s_FieldSubCategory1InfoTextId, null);
            hash.Add(Models.s_FieldVendorName, null);
            hash.Add(Models.s_FieldOrderNumber, null);
            hash.Add(Models.s_FieldHardwareRelease, null);
            hash.Add(Models.s_FieldSoftwareRelease, null);
            hash.Add(Models.s_FieldMainFamily, null);
            hash.Add(Models.s_FieldMainFamilyAsEnum, null);
            hash.Add(Models.s_FieldProductFamily, null);

            hash.Add(Models.s_FieldCompatibilityVersion, null);
            hash.Add(Models.s_FieldIsCompatible, null);

            AddFieldGsdIDToHash(nav, hash);

            AddFieldIdentNumberToHash(nav, hash);

            // --------------------------------------------------------------
            // Get data of module info element.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ModuleInfo, Namespaces.s_GsdmlDeviceProfile);

            // ModuleInfo is required for Module and AccessPoint.
            if (nodes.MoveNext())
            {
                XPathNavigator tempnav = nodes.Current;

                // -------------------------
                // Get Name. Required.
                nodes = tempnav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                {
                    hash[Models.s_FieldName] = GetText(nodes.Current);
                    hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }

                // -------------------------
                // Get InfoText. Required.
                nodes = tempnav.SelectChildren(Elements.s_InfoText, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                {
                    hash[Models.s_FieldInfoText] = GetText(nodes.Current);
                }

                // -------------------------
                // Get VendorName. Optional.
                nodes = tempnav.SelectChildren(Elements.s_VendorName, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                    hash[Models.s_FieldVendorName] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

                // -------------------------
                // Get OrderNumber. Optional.
                nodes = tempnav.SelectChildren(Elements.s_OrderNumber, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                    hash[Models.s_FieldOrderNumber] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

                // -------------------------
                // Get HardwareRelease. Optional.
                nodes = tempnav.SelectChildren(Elements.s_HardwareRelease, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                    hash[Models.s_FieldHardwareRelease] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

                // -------------------------
                // Get SoftwareRelease. Optional.
                nodes = tempnav.SelectChildren(Elements.s_SoftwareRelease, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                    hash[Models.s_FieldSoftwareRelease] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

                // -------------------------
                // Get Category if available. Optional.
                AddCategoryRefToHash(hash, tempnav);

                // -------------------------
                // Get SubCategory1 if available. Optional.
                AddSubCategory1RefToHash(hash, tempnav);

                // -------------------------
                // Get family attributes. (Optional)
                AddFamilyAttributsToHash(hash, tempnav);



            }
            else
            {
                XPathNavigator nav1 = nav;
                nav1.MoveToParent();
                if (nav.LocalName != Elements.s_SubmoduleItem && nav1.LocalName != Elements.s_SystemDefinedSubmoduleList)
                    throw new PreparationException("Element '" + Elements.s_ModuleInfo + "' isn't available from the '" + nav.LocalName + "' element!");
            }

            // --------------------------------------------------------------
            // Get compatibility info.
            bool succeeded = Help.TryGetCompatibilityInfo(nav, Constants.s_Version10, SupportedGsdmlVersion,
                out string cv, out bool isc);
            if (!succeeded)
                throw new PreparationException("Couldn't get compatibility info for '" + nav.LocalName + "' with ID '" + hash[Models.s_FieldGsdId] + "'!");
            hash[Models.s_FieldCompatibilityVersion] = cv;
            hash[Models.s_FieldIsCompatible] = isc;
        }

        private static void AddFamilyAttributsToHash(IDictionary hash, XPathNavigator tempnav)
        {
            XPathNodeIterator nodes = tempnav.SelectChildren(Elements.s_Family, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            if (nodes.Current == null)
            {
                return;
            }

            string attr = nodes.Current.GetAttribute(Attributes.s_MainFamily, String.Empty);   // Required.
            hash[Models.s_FieldMainFamily] = attr;
            if (Enums.IsFamilyEnumValueConvertable(attr))
                hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(attr);
            hash[Models.s_FieldProductFamily] = nodes.Current.GetAttribute(Attributes.s_ProductFamily, String.Empty);   // Optional.
        }

        private void AddSubCategory1RefToHash(IDictionary hash, XPathNavigator tempnav)
        {
            string attr = tempnav.GetAttribute(Attributes.s_SubCategory1Ref, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                return;
            }

            if (!CategoryItemCache.ContainsKey(attr))
                throw new PreparationException("Couldn't get CategoryItem with ID '" + attr + "'!");

            hash[Models.s_FieldSubCategory1GsdId] = attr;
            hash[Models.s_FieldSubCategory1] = GetText((XPathNavigator)CategoryItemCache[attr]);

            if (CategoryItemInfoTextCache.ContainsKey(attr))
            {
                hash[Models.s_FieldSubCategory1InfoText] = GetText((XPathNavigator)CategoryItemInfoTextCache[attr]);
            }

            XPathNavigator categoryInfoTextNav = (XPathNavigator)CategoryItemInfoTextCache[attr];
            if (categoryInfoTextNav == null)
            {
                return;
            }

            attr = categoryInfoTextNav.GetAttribute(Attributes.s_TextId, String.Empty);
            hash[Models.s_FieldSubCategory1InfoTextId] = attr;
        }

        private void AddCategoryRefToHash(IDictionary hash, XPathNavigator tempnav)
        {
            string attr = tempnav.GetAttribute(Attributes.s_CategoryRef, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                return;
            }

            if (!CategoryItemCache.ContainsKey(attr))
                throw new PreparationException("Couldn't get CategoryItem with ID '" + attr + "'!");

            hash[Models.s_FieldCategoryGsdId] = attr;
            hash[Models.s_FieldCategory] = GetText((XPathNavigator)CategoryItemCache[attr]);

            if (CategoryItemInfoTextCache.ContainsKey(attr))
            {
                hash[Models.s_FieldCategoryInfoText] = GetText((XPathNavigator)CategoryItemInfoTextCache[attr]);
            }

            XPathNavigator categoryInfoTextNav = (XPathNavigator)CategoryItemInfoTextCache[attr];
            if (categoryInfoTextNav == null)
            {
                return;
            }

            attr = categoryInfoTextNav.GetAttribute(Attributes.s_TextId, String.Empty);
            hash[Models.s_FieldCategoryInfoTextId] = attr;

        }

        private static void AddFieldIdentNumberToHash(XPathNavigator nav, IDictionary hash)
        {
            string attr;
            // Submodules need a SubmoduleIdentNumber, Modules and DAPs need a ModuleIdentNumber
            if (nav.LocalName == Elements.s_SubmoduleItem || nav.LocalName == Elements.s_PortSubmoduleItem ||
                nav.LocalName == Elements.s_VirtualSubmoduleItem || nav.LocalName == Elements.s_InterfaceSubmoduleItem)
            {
                attr = nav.GetAttribute(Attributes.s_SubmoduleIdentNumber, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    hash[Models.s_FieldIdentNumber] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
                }
                else
                    throw new PreparationException("Attribute '" + Attributes.s_SubmoduleIdentNumber + "' isn't available from the '" + nav.LocalName + "' element!");

            }
            else
            {
                // ModuleIdentNumber Attribute (specified as hex 0x...). Required for Modules and AccessPoints.
                attr = nav.GetAttribute(Attributes.s_ModuleIdentNumber, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    hash[Models.s_FieldIdentNumber] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
                }
                else
                    throw new PreparationException("Attribute '" + Attributes.s_ModuleIdentNumber + "' isn't available from the '" + nav.LocalName + "' element!");


            }
        }

        private static void AddFieldGsdIDToHash(XPathNavigator nav, IDictionary hash)
        {
            // Attribute ID. Required.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldGsdId] = attr;
            }
            else
                throw new PreparationException("Attribute '" + Attributes.ID + "' isn't available from the '" + nav.LocalName + "' element!");
        }
        protected virtual void PrepareModuleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Module data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmodules, null);

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);

            // NOTE: Physical submodules aren't available at the moment and therefore, the
            //       Submodules collection must be empty!

        }

        protected virtual void PrepareAccessPointStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare AccessPoint data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmodules, null);
            hash.Add(Models.s_FieldModules, null);

            // NOTE: Physical submodules aren't available at the moment and therefore, the
            //       Submodules collection must be empty!

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);

            // Check whether (Sub)Modules are available.
            // NOTE: The following logic will be implemented:
            // 
            //       Notation: A = AllowedIn(Sub)Slots
            //                 U = UsedIn(Sub)Slots
            //                 F = FixedIn(Sub)Slots
            //                 P = Physical(Sub)Slot
            //                 
            //       Only F:              (Sub)Module must not be available from the catalog,
            //                            because it is in each F and not allowed to be inserted elsewhere.
            //       A and F:             (Sub)Module must be available from the catalog, only if A contains additional (sub)slots,
            //                            which are not in F. The (Sub)Module is fixed in (sub)slots and can be inserted into each A,
            //                            which is not also a F. If all (sub)slots from A are also in F there is no possibility for insertion.
            //       Not A, not U, not F: (Sub)Module must be available from the catalog and can be inserted into each P.
            //       Only A:              (Sub)Module must be available from the catalog and can be inserted into each A.
            //       Only U:              (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be deleted and inserted again.
            //       A and U:             (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be additional inserted in each A.
            //       U and F:             (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and inserted again.
            //       A and U and F:       (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and additional inserted in each A (U must be contained in A).

            // Select compatible modules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ModuleItemRef, Namespaces.s_GsdmlDeviceProfile, false);

            if (nodes.Count <= 0)
            {
                return;
            }
            // Add Module list to hash.
            ArrayList list = new ArrayList();
            while (nodes.MoveNext())
            {
                string sfixed = nodes.Current.GetAttribute(Attributes.s_FixedInSlots, String.Empty);
                string sused = nodes.Current.GetAttribute(Attributes.s_UsedInSlots, String.Empty);
                string sallowed = nodes.Current.GetAttribute(Attributes.s_AllowedInSlots, String.Empty);

                bool moduleInCatalog = true;

                if ((sfixed.Length != 0) && (sused.Length == 0) && (sallowed.Length == 0))
                {
                    // only F
                    moduleInCatalog = false;
                }
                else if ((sfixed.Length != 0) && (sused.Length == 0) && (sallowed.Length != 0))
                {
                    // A and F
                    List<uint> listAllowed = ValueListHelper.SeparateUnsignedValueList(sallowed);
                    List<uint> listFixed = ValueListHelper.SeparateUnsignedValueList(sfixed);

                    // In principle the check for length is sufficient here, because all F must be contained in A.
                    // But because it is not ensured that the checker run every time, the list contents must be checked.

                    moduleInCatalog = ModuleInCatalog(listAllowed, listFixed);
                }

                if (moduleInCatalog)
                {
                    list.Add(this.SStore.Modules[nodes.Current.GetAttribute(Attributes.s_ModuleItemTarget, String.Empty)]);
                }

            }
            list.TrimToSize();

            hash[Models.s_FieldModules] = list;
        }

        private static bool ModuleInCatalog(IReadOnlyList<uint> listAllowed, IReadOnlyList<uint> listFixed)
        {
            bool moduleInCatalog = true;
            if (listAllowed.Count != listFixed.Count)
            {
                return true;
            }

            for (int i = 0; i < listAllowed.Count; i++)
            {
                if (listAllowed[i] == listFixed[i])
                {
                    moduleInCatalog = false;
                }
                else
                {
                    moduleInCatalog = true;
                    break;
                }
            }

            return moduleInCatalog;

        }


        protected virtual void PrepareDeviceStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare DeviceStructureElement data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldInfoText, null);
            hash.Add(Models.s_FieldVendorName, null);
            hash.Add(Models.s_FieldMainFamily, null);
            hash.Add(Models.s_FieldMainFamilyAsEnum, null);
            hash.Add(Models.s_FieldProductFamily, null);
            hash.Add(Models.s_FieldVendorIdentNumber, null);
            hash.Add(Models.s_FieldDeviceIdentNumber, null);
            hash.Add(Models.s_FieldDeviceAccessPoints, null);


            // --------------------------------------------------------------
            // ...IdentNumber Attributes (specified as hex 0x...). Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_DeviceIdentity, Namespaces.s_GsdmlDeviceProfile);

            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_DeviceIdentity + "' isn't available from the '" + nav.LocalName + "' element!");

            XPathNavigator tempnav = nodes.Current;

            string attr = tempnav.GetAttribute(Attributes.s_VendorId, String.Empty);
            hash[Models.s_FieldVendorIdentNumber] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);

            attr = tempnav.GetAttribute(Attributes.s_DeviceId, String.Empty);
            hash[Models.s_FieldDeviceIdentNumber] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);

            // --------------------------------------------------------------
            // Get InfoText. Required.
            nodes = tempnav.SelectChildren(Elements.s_InfoText, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldInfoText] = this.GetText(nodes.Current);
            }

            // --------------------------------------------------------------
            // Get VendorName. Required.
            nodes = tempnav.SelectChildren(Elements.s_VendorName, Namespaces.s_GsdmlDeviceProfile);

            // Get value.
            if (nodes.MoveNext())
                hash[Models.s_FieldVendorName] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

            // --------------------------------------------------------------
            // Get ...family attributes.
            nodes = nav.SelectChildren(Elements.s_DeviceFunction, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_DeviceFunction + "' isn't available from the '" + nav.LocalName + "' element!");

            tempnav = nodes.Current;

            nodes = tempnav.SelectChildren(Elements.s_Family, Namespaces.s_GsdmlDeviceProfile);

            // Get family attributes.
            if (nodes.MoveNext())
            {
                attr = nodes.Current.GetAttribute(Attributes.s_MainFamily, String.Empty);	// Required.
                hash[Models.s_FieldMainFamily] = attr;
                if (Enums.IsFamilyEnumValueConvertable(attr))
                    hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(attr);
                hash[Models.s_FieldProductFamily] = nodes.Current.GetAttribute(Attributes.s_ProductFamily, String.Empty);	// Optional.
            }

            // --------------------------------------------------------------
            // Add all DeviceAccessPoints to list and add list to hash.
            ArrayList list = new ArrayList(this.SStore.DeviceAccessPoints.Count);
            foreach (var gsdObject in SStore.DeviceAccessPoints.Values)
            {
                list.Add(gsdObject);
            }

            hash[Models.s_FieldDeviceAccessPoints] = list;
        }

        #endregion

        #region Creation

        protected virtual void CreateRecordDataStructureElements()
        {
            // NOTE: At the moment there are no record data (ApplicationProcess) available from the GSD(ML)!!!
        }

        protected virtual void CreateSubmoduleStructureElements()
        {
            // NOTE: At the moment there are no physical submodules available from the GSD(ML)!!!
        }

        protected virtual void CreateModuleStructureElements()
        {
            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ModuleItem]);

            // Prepare data for each Module, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectModuleStructureElement;
                S.ModuleStructureElement obj = (S.ModuleStructureElement)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModuleStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.Modules.Add(obj.GsdID, obj);
            }

        }

        protected virtual void CreateDeviceAccessPointStructureElements()
        {
            // Select all 'DeviceAccessPointItem' elements.
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_DeviceAccessPointItem]);

            // Check whether AccessPoints are available.
            if (nodes.Count == 0)
                throw new CreationException("Elements '" + Elements.s_DeviceAccessPointItem + "' aren't available from the actual GSDML file!");

            // Prepare data for each AccessPoint, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectAccessPointStructureElement;
                S.AccessPointStructureElement obj = (S.AccessPointStructureElement)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAccessPointStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.DeviceAccessPoints.Add(obj.GsdID, obj);
            }
        }

        protected virtual void CreateDeviceStructureElement()
        {
            // Select profile body.
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ProfileBody]);

            // Check whether ProfileBody is available.
            if (nodes.Count == 0)
                throw new CreationException("Element '" + Elements.s_ProfileBody + "' isn't available from the actual GSDML file!");

            // Prepare data for the Device, create it and add it to store.
            if (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectDeviceStructureElement;
                object obj = CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectDeviceStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.Device = obj;
            }
        }

        protected virtual void CreateCommunicationInterfaceStructureElements()
        {
            // NOTE: At the moment there are no communication interfaces (ApplicationProcess) available from the GSD(ML)!!!
        }

        #endregion

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region  Preparation

        protected virtual void PrepareModuleObject(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ModuleObject data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldGsdId, null);
            hash.Add(Models.s_FieldIdentNumber, null);
            hash.Add(Models.s_FieldGraphics, null);
            hash.Add(Models.s_FieldInfo, null);

            hash.Add(Models.s_FieldCompatibilityVersion, null);
            hash.Add(Models.s_FieldIsCompatible, null);

            // Attribute ID. Required.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldGsdId] = attr.Trim();
                hash[Models.s_FieldGsdIdRaw] = attr;
            }

            // Attribute SubmoduleIdentNumber or ModuleIdentNumber (specified as hex 0x...). Required.
            attr = nav.GetAttribute(Attributes.s_SubmoduleIdentNumber, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                attr = nav.GetAttribute(Attributes.s_ModuleIdentNumber, String.Empty);
                if (String.IsNullOrEmpty(attr))
                    throw new PreparationException("Attribute '" + Attributes.s_SubmoduleIdentNumber + "' or '" + Attributes.s_ModuleIdentNumber + "' isn't available from the '" + nav.LocalName + "' element!");

            }
            hash[Models.s_FieldIdentNumber] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);

            // Navigate to ModuleInfo and create it. Partly optional, partly required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ModuleInfo, Namespaces.s_GsdmlDeviceProfile);
            // It is optional, therefore no ModuleInfo is also valid.
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectModuleInfo, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModuleInfo + "' couldn't be created!");
            }
            hash[Models.s_FieldInfo] = obj;

            // Navigate to Graphics and create it. Optional.
            ArrayList list = null;
            nodes = nav.SelectChildren(Elements.s_Graphics, Namespaces.s_GsdmlDeviceProfile);

            // Check whether Graphic(s) are available.
            if (nodes.MoveNext())
            {
                // Set navigator to the correct position.
                XPathNavigator tempnav = nodes.Current;
                if (tempnav != null)
                {
                    nodes = tempnav.SelectChildren(Elements.s_GraphicItemRef, Namespaces.s_GsdmlDeviceProfile);
                }

                // Create each found graphic.
                while (nodes.MoveNext())
                {
                    // Create list.
                    if (null == list)
                        list = new ArrayList(nodes.Count);

                    // Create graphic itself.
                    obj = CreateGsdObject(Models.s_ObjectGraphic, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectGraphic + "' couldn't be created!");

                    // Add it to the list.
                    list.Add(obj);
                }
                hash[Models.s_FieldGraphics] = list;
            }

            // --------------------------------------------------------------
            // Get compatibility info.
            string cv = String.Empty;
            bool isc = true;
            bool succeeded = Help.TryGetCompatibilityInfo(nav, Constants.s_Version10, SupportedGsdmlVersion,
                out cv, out isc);
            if (!succeeded)
                throw new PreparationException("Couldn't get compatibility info for '" + nav.LocalName + "' with ID '" + hash[Models.s_FieldGsdId] + "'!");
            hash[Models.s_FieldCompatibilityVersion] = cv;
            hash[Models.s_FieldIsCompatible] = isc;

        }

        protected virtual void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare VirtualSubmodule data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIoData, null);
            hash.Add(Models.s_FieldParameterRecordData, null);

            // Prepare data of base class.
            PrepareModuleObject(nav, ref hash);

            // Navigate to IOData and create it. Required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_IoData, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
                obj = CreateGsdObject(Models.s_ObjectIoData, nodes.Current);

            if (null == obj)	// Its required, therefore no element is not valid.
                throw new CreationException("Object '" + Models.s_ObjectIoData + "' couldn't be created!");
            hash[Models.s_FieldIoData] = obj;

            // Navigate to all ParameterRecordData and create it. Optional.
            ArrayList list = null;
            nodes = nav.SelectDescendants(Elements.s_ParameterRecordDataItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Create each found data record.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data record itself.
                obj = CreateGsdObject(Models.s_ObjectParameterRecordData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectParameterRecordData + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            hash[Models.s_FieldParameterRecordData] = list;

        }

        protected virtual void PrepareModule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare VirtualSubmodule data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldVirtualSubmodules, null);

            // Prepare data of base class.
            PrepareModuleObject(nav, ref hash);

            // Select virtual submodule(s).
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_VirtualSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Add Submodule to hash.
            // NOTE: For the first version of the GSDML, use only first getted virtual submodule.
            ArrayList list = new ArrayList();
            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string id = nodes.Current.GetAttribute(Attributes.ID, String.Empty);
                    list.Add(CStore.VirtualSubmodules[id]);
                    // Check whether VirtualSubmodule is available.
                    if (list.Count == 0)
                        throw new PreparationException(
                            "VirtualSubmoduleItem node with ID '" + id
                                                                  + "' couldn't be found in the Gsd file for the '"
                                                                  + nav.LocalName + "' element!");
                }
            }
            hash[Models.s_FieldVirtualSubmodules] = list;

        }

        protected virtual void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare DeviceAccessPoint data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldPhysicalSlots, null);
            hash.Add(Models.s_FieldMinDeviceInterval, null);
            hash.Add(Models.s_FieldImplementationType, null);
            hash.Add(Models.s_FieldDnsCompatibleName, null);
            hash.Add(Models.s_FieldObjectUuidLocalIndex, null);
            hash.Add(Models.s_FieldIsExtendedAddressAssignmentSupported, null);
            hash.Add(Models.s_FieldVirtualSubmodules, null);
            hash.Add(Models.s_FieldModules, null);
            hash.Add(Models.s_FieldModulePlugData, null);
            hash.Add(Models.s_FieldIoConfigData, null);
            hash.Add(Models.s_FieldApplicationRelations, null);
            hash.Add(Models.s_FieldPlugData, null);

            hash[Models.s_FieldIsDeviceAccessSupported] = true;

            // Get data of base class.
            PrepareModuleObject(nav, ref hash);


            // Get PhysicalSlots attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_PhysicalSlots, String.Empty);
            hash[Models.s_FieldPhysicalSlots] = ValueListHelper.SeparateUnsignedValueList(attr);

            // Get min device intervall attribute. Required.
            attr = nav.GetAttribute(Attributes.s_MinDeviceInterval, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldMinDeviceInterval] = value;

            // Get implementation type attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ImplementationType, String.Empty);
            hash[Models.s_FieldImplementationType] = attr;

            // Get dns compatible name attribute. Required.
            attr = nav.GetAttribute(Attributes.s_DnsCompatibleName, String.Empty);
            hash[Models.s_FieldDnsCompatibleName] = attr;

            // Get ObjectUUIDLocalIndex attribute. Required.
            attr = nav.GetAttribute(Attributes.s_ObjectUUIDLocalIndex, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldObjectUuidLocalIndex] = value;

            // Get ExtendedAddressAssignmentSupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ExtendedAddressAssignmentSupported, String.Empty);
            hash[Models.s_FieldIsExtendedAddressAssignmentSupported] = Help.GetBool(attr, Attributes.s_DefaultExtendedAddressAssignmentSupported);

            // --------------------------------------------------
            AddSubmodules2DAP(nav, ref hash);

            PrepareDAPModulePlugData(nav, ref hash);

            // --------------------------------------------------
            // Navigate to IOConfigData and create it. Required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_IoConfigData, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
                obj = CreateGsdObject(Models.s_ObjectIoConfigData, nodes.Current);
            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectIoConfigData + "' couldn't be created!");
            hash[Models.s_FieldIoConfigData] = obj;

            // --------------------------------------------------
            // Create ModulePlugData for the DeviceAccessPoint.
            obj = CreateGsdObject(Models.s_ObjectModulePlugData, nav);
            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectModulePlugData + "' couldn't be created!");
            hash[Models.s_FieldPlugData] = obj;

            // --------------------------------------------------
            // Navigate to ApplicationRelations. Optional.
            nodes = nav.SelectChildren(Elements.s_ApplicationRelations, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            obj = null;
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectApplicationRelations, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectApplicationRelations + "' couldn't be created!");
            }
            hash[Models.s_FieldApplicationRelations] = obj;
        }

        protected virtual void AddSubmodules2DAP(XPathNavigator nav, ref Hashtable hash)
        {
            // Select virtual submodule.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_VirtualSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Check whether VirtualSubmodule is available.
            if (nodes.Count == 0)
                throw new PreparationException("VirtualSubmoduleItem node couldn't be found in the Gsd file for the '" + nav.LocalName + "' element!");

            // Add Submodule(s) to hash.
            // NOTE: For the first version of the GSDML, use only first found virtual submodule.
            ArrayList list = new ArrayList();
            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string id = nodes.Current.GetAttribute(Attributes.ID, String.Empty);
                    list.Add(CStore.VirtualSubmodules[id]);

                    // Check whether VirtualSubmodule is available.
                    if (list.Count == 0)
                        throw new PreparationException(
                            "VirtualSubmoduleItem node with ID '" + id + "' couldn't be found in the Gsd file for the '"
                            + nav.LocalName + "' element!");
                }
            }
            hash[Models.s_FieldVirtualSubmodules] = list;
        }

        protected virtual void PrepareDAPModulePlugData(XPathNavigator nav, ref Hashtable hash)
        {
            // Select modules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ModuleItemRef, Namespaces.s_GsdmlDeviceProfile, false);

            // Check whether modules are available. Required.
            if (nodes.Count == 0)
                throw new PreparationException("ModuleItemRef node couldn't be found in the Gsd file for the '" + nav.LocalName + "' element!");

            // Add found modules to hash and create module plug data.

            ArrayList list = new ArrayList(nodes.Count);
            var slist = GsdObjectDictionaryFactory.CreateDictionary(true);

            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sID = nodes.Current.GetAttribute(Attributes.s_ModuleItemTarget, String.Empty);
                if (!CStore.Modules.ContainsKey(sID))
                    throw new PreparationException("Couldn't get Module from store with ID '" + sID + "'!");

                // Add module to list.
                list.Add(CStore.Modules[sID]);

                // Create module plug data.
                var obj = CreateGsdObject(Models.s_ObjectModulePlugData, nodes.Current) as GsdObject;
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModulePlugData + "' couldn't be created!");

                // Add it to the hash.
                slist.Add(sID, obj);
            }
            hash[Models.s_FieldModules] = list;
            hash[Models.s_FieldModulePlugData] = slist;
        }

        protected virtual void PrepareDevice(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Device data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldInfo, null);
            hash.Add(Models.s_FieldDeviceAccessPoints, null);
            hash.Add(Models.s_FieldChannelDiagnostics, null);
            hash.Add(Models.s_FieldUnitDiagnosticTypes, null);

            // Navigate to DeviceIdentity and create it.
            object obj = CreateGsdObject(Models.s_ObjectDeviceInfo, nav);

            // Its required, therefore no element is not valid.
            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectDeviceInfo + "' couldn't be created!");

            // Set hash variable.
            hash[Models.s_FieldInfo] = obj;

            // Add all InterfaceModules to list and add list to hash.
            ArrayList list = new ArrayList(this.CStore.DeviceAccessPoints.Count);
            foreach (var gsdObject in CStore.DeviceAccessPoints.Values)
            {
                list.Add(gsdObject);
            }
            hash[Models.s_FieldDeviceAccessPoints] = list;

            // Navigate to channel diagnostics and create it. Optional.
            list = null;
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ChannelDiagItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectChannelDiagnostic, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectChannelDiagnostic + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldChannelDiagnostics] = list;

            // Navigate to unit diagnostic types and create it. Optional.
            list = null;
            nodes = nav.Select((XPathExpression)Expressions[Elements.s_UnitDiagTypeItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectUnitDiagnosticType, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectUnitDiagnosticType + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldUnitDiagnosticTypes] = list;

        }

        protected virtual void PrepareDeviceInfo(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare DeviceInfo data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldInfoText, null);
            hash.Add(Models.s_FieldInfoTextId, null);
            hash.Add(Models.s_FieldVendorName, null);
            hash.Add(Models.s_FieldMainFamily, null);
            hash.Add(Models.s_FieldMainFamilyAsEnum, null);
            hash.Add(Models.s_FieldProductFamily, null);
            hash.Add(Models.s_FieldVendorIdentNumber, null);
            hash.Add(Models.s_FieldDeviceIdentNumber, null);

            // --------------------------------------------------------------
            // ...IdentNumber Attributes (specified as hex 0x...). Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_DeviceIdentity, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException(
                    "Element '" + Elements.s_DeviceIdentity + "' isn't available from the '" + nav.LocalName
                    + "' element!");

            XPathNavigator tempnav = nodes.Current;

            if (tempnav == null)
            {
                return;
            }

            string attr = tempnav.GetAttribute(Attributes.s_VendorId, String.Empty);
            hash[Models.s_FieldVendorIdentNumber] = UInt32.Parse(
                attr.Substring(2),
                NumberStyles.AllowHexSpecifier,
                CultureInfo.InvariantCulture);

            attr = tempnav.GetAttribute(Attributes.s_DeviceId, String.Empty);
            hash[Models.s_FieldDeviceIdentNumber] = UInt32.Parse(
                attr.Substring(2),
                NumberStyles.AllowHexSpecifier,
                CultureInfo.InvariantCulture);

            // --------------------------------------------------------------
            // Get InfoText. Required.
            nodes = tempnav.SelectChildren(Elements.s_InfoText, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException(
                    "Element '" + Elements.s_InfoText + "' isn't available from the '" + tempnav.LocalName
                    + "' element!");
            hash[Models.s_FieldInfoText] = this.GetText(nodes.Current);
            hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // --------------------------------------------------------------
            // Get VendorName. Required.
            nodes = tempnav.SelectChildren(Elements.s_VendorName, Namespaces.s_GsdmlDeviceProfile);

            // Get value.
            if (nodes.MoveNext())
                hash[Models.s_FieldVendorName] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);


            // --------------------------------------------------------------
            // Get ...family attributes.
            nodes = nav.SelectChildren(Elements.s_DeviceFunction, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException(
                    "Element '" + Elements.s_DeviceFunction + "' isn't available from the '" + nav.LocalName
                    + "' element!");

            tempnav = nodes.Current;

            nodes = tempnav.SelectChildren(Elements.s_Family, Namespaces.s_GsdmlDeviceProfile);

            // --------------------------------------------------------------
            // Get family attributes. (Optional)
            if (!nodes.MoveNext())
            {
                return;
            }

            attr = nodes.Current.GetAttribute(Attributes.s_MainFamily, String.Empty); // Required.
            hash[Models.s_FieldMainFamily] = attr;
            if (Enums.IsFamilyEnumValueConvertable(attr))
                hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(attr);
            hash[Models.s_FieldProductFamily] =
                nodes.Current.GetAttribute(Attributes.s_ProductFamily, String.Empty); // Optional.


        }

        protected virtual void PrepareChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldErrorType, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);

            // Get error type attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_ErrorType, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldErrorType] = value;

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = this.GetText(nodes.Current);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }
            hash[Models.s_FieldHelp] = this.GetText(nodes.Current);
            if (nodes.Current != null)
            {

                hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
            }

        }

        protected virtual void PrepareUnitDiagnosticType(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare UnitDiagnosticType data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldUserStructureIdentifier, null);
            hash.Add(Models.s_FieldRefs, null);

            // Get UserStructureIdentifier if available. Required.
            string attr = nav.GetAttribute(Attributes.s_UserStructureIdentifier, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldUserStructureIdentifier] = value;

            // Navigate to ref elements and create them.
            ArrayList list = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Ref, Namespaces.s_GsdmlDeviceProfile);

            // Create each found ref element.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create ref data itself.
                object obj = CreateGsdObject(Models.s_ObjectRefData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectRefData + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldRefs] = list;

        }

        protected virtual void PrepareModuleInfo(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ModuleInfo data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldInfoText, null);
            hash.Add(Models.s_FieldInfoTextId, null);
            hash.Add(Models.s_FieldCategory, null);
            hash.Add(Models.s_FieldCategoryGsdId, null);
            hash.Add(Models.s_FieldCategoryTextId, null);
            hash.Add(Models.s_FieldCategoryInfoText, null);
            hash.Add(Models.s_FieldCategoryInfoTextId, null);
            hash.Add(Models.s_FieldSubCategory1, null);
            hash.Add(Models.s_FieldSubCategory1TextId, null);
            hash.Add(Models.s_FieldSubCategory1GsdId, null);
            hash.Add(Models.s_FieldSubCategory1InfoText, null);
            hash.Add(Models.s_FieldSubCategory1InfoTextId, null);
            hash.Add(Models.s_FieldVendorName, null);
            hash.Add(Models.s_FieldOrderNumber, null);
            hash.Add(Models.s_FieldHardwareRelease, null);
            hash.Add(Models.s_FieldSoftwareRelease, null);
            hash.Add(Models.s_FieldMainFamily, null);
            hash.Add(Models.s_FieldMainFamilyAsEnum, null);
            hash.Add(Models.s_FieldProductFamily, null);

            // --------------------------------------------------------------
            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = GetText(nodes.Current);
            hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // --------------------------------------------------------------
            // Get InfoText. Required.
            nodes = nav.SelectChildren(Elements.s_InfoText, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_InfoText + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldInfoText] = this.GetText(nodes.Current);
            hash[Models.s_FieldInfoTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // --------------------------------------------------------------
            // Get VendorName. Optional.
            nodes = nav.SelectChildren(Elements.s_VendorName, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
                hash[Models.s_FieldVendorName] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

            // --------------------------------------------------------------
            // Get OrderNumber. Optional.
            nodes = nav.SelectChildren(Elements.s_OrderNumber, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
                hash[Models.s_FieldOrderNumber] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

            // --------------------------------------------------------------
            // Get HardwareRelease. Optional.
            nodes = nav.SelectChildren(Elements.s_HardwareRelease, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
                hash[Models.s_FieldHardwareRelease] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

            // --------------------------------------------------------------
            // Get SoftwareRelease. Optional.
            nodes = nav.SelectChildren(Elements.s_SoftwareRelease, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
                hash[Models.s_FieldSoftwareRelease] = nodes.Current.GetAttribute(Attributes.s_Value, String.Empty);

            // --------------------------------------------------------------
            // Get Category if available. Optional.
            GetCategoryRefForModuleInfo(nav, hash);

            // --------------------------------------------------------------
            // Get SubCategory1 if available. Optional.
            GetSubCategory1RefForModuleInfo(nav, hash);




            nodes = nav.SelectChildren(Elements.s_Family, Namespaces.s_GsdmlDeviceProfile);

            // --------------------------------------------------------------
            // Get family attributes. (Optional)
            if (!nodes.MoveNext())
            {
                return;
            }
            string attr = nodes.Current.GetAttribute(Attributes.s_MainFamily, String.Empty);    // Required.
            hash[Models.s_FieldMainFamily] = attr;
            if (Enums.IsFamilyEnumValueConvertable(attr))
                hash[Models.s_FieldMainFamilyAsEnum] = Enums.ConvertFamilyEnum(attr);
            hash[Models.s_FieldProductFamily] = nodes.Current.GetAttribute(Attributes.s_ProductFamily, String.Empty);   // Optional.


        }


        private void GetSubCategory1RefForModuleInfo(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_SubCategory1Ref, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                return;
            }
            if (!CategoryItemCache.ContainsKey(attr))
                throw new PreparationException("Couldn't get CategoryItem with ID: '" + attr + "'!");
            hash[Models.s_FieldSubCategory1GsdId] = attr;

            // Get SubCategory1 text.
            hash[Models.s_FieldSubCategory1] = GetText(CategoryItemCache[attr] as XPathNavigator);

            XPathNavigator navSubCat1 = CategoryItemCache[attr] as XPathNavigator;
            if (navSubCat1 != null)
            {
                string catTextId = navSubCat1.GetAttribute("TextId", "");
                hash[Models.s_FieldSubCategory1TextId] = catTextId;
            }

            if (CategoryItemInfoTextCache.ContainsKey(attr))
            {
                hash[Models.s_FieldSubCategory1InfoText] = GetText((XPathNavigator)this.CategoryItemInfoTextCache[attr]);
            }

            XPathNavigator categoryInfoTextNav = (XPathNavigator)CategoryItemInfoTextCache[attr];
            if (categoryInfoTextNav == null)
            {
                return;
            }
            attr = categoryInfoTextNav.GetAttribute(Attributes.s_TextId, String.Empty);
            hash[Models.s_FieldSubCategory1InfoTextId] = attr;


        }

        private void GetCategoryRefForModuleInfo(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_CategoryRef, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                return;
            }
            if (!CategoryItemCache.ContainsKey(attr))
                throw new PreparationException("Couldn't get CategoryItem with ID: '" + attr + "'!");
            hash[Models.s_FieldCategoryGsdId] = attr;

            // Get category name.
            hash[Models.s_FieldCategory] = GetText(CategoryItemCache[attr] as XPathNavigator);

            XPathNavigator navCat = CategoryItemCache[attr] as XPathNavigator;
            if (navCat != null)
            {
                string catTextId = navCat.GetAttribute("TextId", "");
                hash[Models.s_FieldCategoryTextId] = catTextId;
            }


            // Get category info text
            if (CategoryItemInfoTextCache.ContainsKey(attr))
            {
                hash[Models.s_FieldCategoryInfoText] = GetText((XPathNavigator)this.CategoryItemInfoTextCache[attr]);
            }

            XPathNavigator categoryInfoTextNav = (XPathNavigator)CategoryItemInfoTextCache[attr];
            if (categoryInfoTextNav == null)
            {
                return;
            }
            attr = categoryInfoTextNav.GetAttribute(Attributes.s_TextId, String.Empty);
            hash[Models.s_FieldCategoryInfoTextId] = attr;


        }

        protected virtual void PrepareGraphic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ModuleInfo data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldGraphicGsdId, null);
            hash.Add(Models.s_FieldFile, null);
            hash.Add(Models.s_FieldSvg, null);
            hash.Add(Models.s_FieldType, null);

            // Get Graphic.
            string attr = nav.GetAttribute(Attributes.s_GraphicItemTarget, String.Empty);
            if (!GraphicItemCache.ContainsKey(attr))
                throw new PreparationException("Couldn't get GraphicItem with ID: '" + attr + "'!");
            hash[Models.s_FieldGraphicGsdId] = attr;

            // Get GraphicFile attribute from item element.
            XPathNavigator tempnav = GraphicItemCache[attr] as XPathNavigator;
            attr = tempnav.GetAttribute(Attributes.s_GraphicFile, String.Empty);

            hash[Models.s_FieldFile] = attr;

            // Get Type attribute from reference element.
            attr = nav.GetAttribute(Attributes.s_Type, String.Empty);
            if (Enums.IsGraphicsTypeEnumValueConvertable(attr))
                hash[Models.s_FieldType] = Enums.ConvertGraphicsTypeEnum(attr);


            // Check whether embedded graphic exists.
            XPathNodeIterator nodes = tempnav.SelectDescendants(Elements.s_Embedded, Namespaces.s_GsdmlDeviceProfile, false);

            if (nodes.MoveNext())
            {
                // Embedded graphic available.
                IHasXmlNode node = nodes.Current as IHasXmlNode;
                if (node != null)
                {
                    hash[Models.s_FieldSvg] = node.GetNode().InnerXml;
                }
            }
        }


        protected virtual void PrepareIOConfigData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOConfigData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMaxInputLength, null);
            hash.Add(Models.s_FieldMaxOutputLength, null);
            hash.Add(Models.s_FieldMaxDataLength, null);

            // Get maximal input length attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_MaxInputLength, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldMaxInputLength] = value;

            // Get maximal output length attribute. Required.
            attr = nav.GetAttribute(Attributes.s_MaxOutputLength, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldMaxOutputLength] = value;

            // Get maximal data length attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MaxDataLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldMaxDataLength] =
                    (UInt32)hash[Models.s_FieldMaxInputLength] +
                    (UInt32)hash[Models.s_FieldMaxOutputLength];
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldMaxDataLength] = value;
            }

        }
        protected virtual void PrepareIOData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIopsLength, null);
            hash.Add(Models.s_FieldIocsLength, null);
            hash.Add(Models.s_FieldInputDataItems, null);
            hash.Add(Models.s_FieldOutputDataItems, null);
            hash.Add(Models.s_FieldInputConsistency, null);
            hash.Add(Models.s_FieldOutputConsistency, null);

            // Get IOPS length attribute. Optional.
            GetFieldIOPSLengthForIOData(nav, hash);

            // Get IOCS length attribute. Optional.
            GetFieldIOCSLengthForIOData(nav, hash);

            // Navigate to data items and create them.
            // Select input data items.
            object obj = null;
            ArrayList list = null;
            XPathNavigator tempnav = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Input, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                tempnav = nodes.Current;

                // Get input consistency if available. Optional.
                GetFieldInputConsistencyForIOData(hash, tempnav);

                nodes = tempnav.SelectChildren(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile);

                // Create each found data item. Optional.
                while (nodes.MoveNext())
                {
                    // Create list.
                    if (null == list)
                        list = new ArrayList(nodes.Count);

                    // Create data record itself.
                    obj = CreateGsdObject(Models.s_ObjectDataItem, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectDataItem + "' couldn't be created!");

                    // Add it to the list.
                    list.Add(obj);
                }
                hash[Models.s_FieldInputDataItems] = list;
            }

            // Select output data items.
            list = null;
            nodes = nav.SelectChildren(Elements.s_Output, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }
            tempnav = nodes.Current;

            // Get output consistency if available. Optional.
            string attr = tempnav.GetAttribute(Attributes.s_Consistency, String.Empty);
            if (!Enums.IsIODataConsistencyEnumValueConvertable(attr))
            {
                // Set default.
                hash[Models.s_FieldOutputConsistency] = Attributes.s_DefaultConsistency;
            }
            else
                hash[Models.s_FieldOutputConsistency] = Enums.ConvertIODataConsistencyEnum(attr);

            nodes = tempnav.SelectChildren(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile);

            // Create each found data item. Optional.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data record itself.
                obj = CreateGsdObject(Models.s_ObjectDataItem, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectDataItem + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            hash[Models.s_FieldOutputDataItems] = list;
        }

        private static void GetFieldInputConsistencyForIOData(IDictionary hash, XPathNavigator tempnav)
        {
            string attr = tempnav.GetAttribute(Attributes.s_Consistency, String.Empty);
            if (!Enums.IsIODataConsistencyEnumValueConvertable(attr))
            {
                // Set default.
                hash[Models.s_FieldInputConsistency] = Attributes.s_DefaultConsistency;
            }
            else
                hash[Models.s_FieldInputConsistency] = Enums.ConvertIODataConsistencyEnum(attr);
        }

        private static void GetFieldIOCSLengthForIOData(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_IocsLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldIocsLength] = Attributes.s_DefaultIocsLength;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldIocsLength] = value;
            }
        }
        private static void GetFieldIOPSLengthForIOData(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_IopsLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldIopsLength] = Attributes.s_DefaultIopsLength;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldIopsLength] = value;
            }
        }

        protected virtual void PrepareDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare DataItem data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldDataType, null);
            hash.Add(Models.s_FieldUseAsBits, null);
            hash.Add(Models.s_FieldDataLength, null);

            // Get Name. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get data type attribute of data item. Required.
            string attr = nav.GetAttribute(Attributes.s_DataType, String.Empty);
            if (Enums.IsDataTypeEnumValueConvertable(attr))
                hash[Models.s_FieldDataType] = Enums.ConvertDataItemTypeEnum(attr);

            // Get length attribute of data item. Optional.
            uint length = Enums.GetDataItemTypeBitLength(attr);
            if (length == 0)
            {
                attr = nav.GetAttribute(Attributes.s_Length, String.Empty);
                if (String.IsNullOrEmpty(attr))
                    throw new PreparationException("If type of DataItem is 'OctetString' or 'VisibleString', the length attribute must be specified!");
                length = XmlConvert.ToUInt32(attr) * 8;
            }
            hash[Models.s_FieldDataLength] = length;

            // Get UseAsBits attribute of data item. Optional.
            attr = nav.GetAttribute(Attributes.s_UseAsBits, String.Empty);
            hash[Models.s_FieldUseAsBits] = Help.GetBool(attr, Attributes.s_DefaultUseAsBits);
        }

        protected virtual void PrepareParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ParameterRecordData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldTransferSequence, null);
            hash.Add(Models.s_FieldRefs, null);

            // Prepare data of base class.
            PrepareRecordData(nav, ref hash);

            // Get TransferSequence attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTransferSequence] = Attributes.s_DefaultTransferSequence;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldTransferSequence] = value;
            }

            // Navigate to ref data and create it. Optional.
            ArrayList list = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Ref, Namespaces.s_GsdmlDeviceProfile);

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectRefData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectRefData + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            hash[Models.s_FieldRefs] = list;
        }


        protected virtual void PrepareRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare (abstract) RecordData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIndex, null);
            hash.Add(Models.s_FieldLength, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldConsts, null);

            // Get Index attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Index, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldIndex] = value;

            // Get Length attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Length, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldLength] = value;

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldName] = this.GetText(nodes.Current);
                hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
            }

            // Navigate to const data and create it. Optional.
            ArrayList list = null;
            nodes = nav.SelectChildren(Elements.s_Const, Namespaces.s_GsdmlDeviceProfile);

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectConstData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectConstData + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            hash[Models.s_FieldConsts] = list;
        }



        protected virtual void PrepareConstData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ConstData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldValues, null);
            hash.Add(Models.s_FieldByteOffset, null);

            // Get Data attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Data, String.Empty);
            ArrayList stringlist = new ArrayList(attr.Split(Constants.s_Comma.ToCharArray()));
            ArrayList list = new ArrayList(stringlist.Count);
            // Convert each hex value to a normal number.
            foreach (string s in stringlist)
            {
                list.Add(UInt32.Parse(s.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture));
            }
            hash[Models.s_FieldValues] = list;

            // Get ByteOffset attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ByteOffset, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldByteOffset] = Attributes.s_DefaultByteOffset;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldByteOffset] = value;
            }

        }

        protected virtual void PrepareRefData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare RefData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldDataType, null);
            hash.Add(Models.s_FieldByteOffset, null);
            hash.Add(Models.s_FieldBitOffset, null);
            hash.Add(Models.s_FieldBitLength, null);
            hash.Add(Models.s_FieldIsChangeable, null);
            hash.Add(Models.s_FieldIsVisible, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldValueGsdId, null);
            hash.Add(Models.s_FieldDefaultValue, null);
            hash.Add(Models.s_FieldValues, null);
            hash.Add(Models.s_FieldValueType, null);

            // Get DataType attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_DataType, String.Empty);
            if (Enums.IsDataTypeEnumValueConvertable(attr))
                hash[Models.s_FieldDataType] = Enums.ConvertDataTypeEnum(attr);

            bool isNumerical = Enums.IsDataTypeNumerical(attr);

            // Get BitLength attribute. Optional.
            GetFieldBitLengthForRefData(nav, hash, attr);

            // Get ByteOffset attribute. Required.
            GetFieldByteOffsetForRefData(nav, hash);


            // Get Name. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get BitOffset attribute. Optional.
            GetFieldBitOffsetForRefData(nav, hash);


            // Get Changeable and Visible attributes. Optional.
            attr = nav.GetAttribute(Attributes.s_Changeable, String.Empty);
            hash[Models.s_FieldIsChangeable] = Help.GetBool(attr, Attributes.s_DefaultChangeable);

            attr = nav.GetAttribute(Attributes.s_Visible, String.Empty);
            hash[Models.s_FieldIsVisible] = Help.GetBool(attr, Attributes.s_DefaultVisible);


            // Get DefaultValue attribute. Required.
            DataTypes type = (DataTypes)hash[Models.s_FieldDataType];
            attr = nav.GetAttribute(Attributes.s_DefaultValue, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldDefaultValue] = Enums.GetDataTypeObject(attr, type);

            // Get AllowedValues attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_AllowedValues, String.Empty);
            string sAllowedValues = attr;

            // Get target for value item. Optional.
            attr = nav.GetAttribute(Attributes.s_ValueItemTarget, String.Empty);
            hash[Models.s_FieldValueGsdId] = attr;
            string sValueGsdID = attr;

            // Check whether there are discrete values or it is an area.
            bool area = false;

            if (String.IsNullOrEmpty(sValueGsdID) && isNumerical)
                area = true;

            area = GetFieldHelpForRefData(hash, sValueGsdID, area);


            if (area)
            {
                // ####### AREA ##################################################
                // Area(s) must be initialized!!!
                GetGSD_AreaTypesForRefData(hash, type, sAllowedValues);
            }
            else if (isNumerical)
            {
                // ####### VALUE #################################################
                // Value(s) must be initialized!!!
                GetGSD_ConcreteTypesForRefData(hash, sValueGsdID, type, sAllowedValues);
            }
        }

        private void GetGSD_ConcreteTypesForRefData(IDictionary hash, string sValueGsdID, DataTypes type, string sAllowedValues)
        {
            hash[Models.s_FieldValueType] = ValueTypes.GSDConcrete;

            ArrayList list = null;
            Hashtable cache = GetValueItemObjectCache(sValueGsdID, type);
            if (null == cache)
                throw new PreparationException("Couldn't get ValueItem with ID '" + sValueGsdID + "' and type '" + type + "'!");

            if (string.IsNullOrEmpty(sAllowedValues))
            {
                // All Assign nodes must be used!!!
                string key = sValueGsdID + type;
                if (ValueItemObjectListCache.Contains(key))
                {
                    list = (ArrayList)ValueItemObjectListCache[key];
                }
                else
                    list = new ArrayList(cache.Values);
            }
            else
            {
                // Only this Assign nodes must be used, which are also contained in AllowedValues!!!
                // Separate allowed values.
                ArrayList av = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));
                ArrayList av2 = new ArrayList();
                SeparateAllowedValues(type, av, av2);


                // Take matching value items to list.
                foreach (object o in av2)
                {
                    // Create list.
                    if (null == list)
                        list = new ArrayList();

                    // Check whether cache contains value.
                    if (!cache.ContainsKey(o))
                        continue;

                    list.Add(cache[o]);	// Add to list.
                }
            }
            hash[Models.s_FieldValues] = list;
        }

        private static void SeparateAllowedValues(DataTypes type, ArrayList av, IList av2)
        {
            foreach (string s in av)
            {
                int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                if (index != -1)
                {
                    // Area, separate area and add values.
                    string ss1 = s.Substring(0, index);
                    string ss2 = s.Substring(index + 2);

                    // Separate doings for signed and unsigned data types.
                    if ((type == DataTypes.GSDInteger8) || (type == DataTypes.GSDInteger16) ||
                        (type == DataTypes.GSDInteger32) || (type == DataTypes.GSDInteger64))
                    {
                        Int64 v1 = XmlConvert.ToInt64(ss1);
                        Int64 v2 = XmlConvert.ToInt64(ss2);


                        for (Int64 i = v1; i <= v2; i++)
                        {
                            av2.Add(Enums.GetDataTypeObject(i.ToString(CultureInfo.CurrentCulture), type));
                        }
                    }
                    else
                    {
                        UInt64 v1 = XmlConvert.ToUInt64(ss1);
                        UInt64 v2 = XmlConvert.ToUInt64(ss2);

                        for (UInt64 i = v1; i <= v2; i++)
                        {
                            av2.Add(Enums.GetDataTypeObject(i.ToString(CultureInfo.CurrentCulture), type));
                        }
                    }
                }
                else
                    // Add value directly.
                    av2.Add(Enums.GetDataTypeObject(s, type));
            }
        }
        private static void GetGSD_AreaTypesForRefData(IDictionary hash, DataTypes type, string sAllowedValues)
        {
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

            ArrayList av = null;

            // If data type is bit or AllowedValues aren't specified, create needed area.
            if ((DataTypes.GSDBit == type) || String.IsNullOrEmpty(sAllowedValues))
            {
                UInt32 bitlength = 0;
                if ((DataTypes.GSDBitArea == type) && (null != hash[Models.s_FieldBitLength]))
                    bitlength = (UInt32)hash[Models.s_FieldBitLength];
                string sarea = Enums.GetAreaByDataType(type, bitlength);

                av = new ArrayList();
                av.Add(sarea);
            }

            // Separate allowed values.
            if (null == av)
                av = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

            //ArrayList av2 = new ArrayList();

            ArrayList list = null;
            Hashtable hash2 = new Hashtable();

            // Add all needed entries as null references to avoid later exceptions.
            hash2.Add(Models.s_FieldMinValue, null);
            hash2.Add(Models.s_FieldMaxValue, null);

            // Iterate to all separated areas and get min and max values to create area item.
            foreach (string s in av)
            {
                // Create list.
                if (null == list)
                    list = new ArrayList();

                // Get min and max value.
                string ssMin = String.Empty;
                string ssMax = String.Empty;
                int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                if (index != -1)
                {
                    // Area, separate area and add values (44..211).
                    ssMin = s.Substring(0, index);	// 44
                    ssMax = s.Substring(index + 2);	// 211
                }
                else
                {
                    // Value, create area with same min and max value.
                    ssMin = s;
                    ssMax = s;
                }

                // Create AreaItem(s).
                hash2[Models.s_FieldMinValue] = Enums.GetDataTypeObject(ssMin, type);
                hash2[Models.s_FieldMaxValue] = Enums.GetDataTypeObject(ssMax, type);

                if ((null == hash2[Models.s_FieldMinValue]) || (null == hash2[Models.s_FieldMaxValue]))
                    throw new PreparationException("Min- or MaxValue unavailable!");

                // Create object and fill with data
                AreaItem oAreaItem = new AreaItem();
                bool succeeded = oAreaItem.Fill(hash2);
                if (!succeeded)
                    throw new PreparationException("AreaItem couldn't be filled with data!");

                list.Add(oAreaItem);    // Add object to list.

            }

            // Set hash variable.
            hash[Models.s_FieldValues] = list;
        }


        private bool GetFieldHelpForRefData(IDictionary hash, string sValueGsdID, bool area)
        {
            if (!String.IsNullOrEmpty(sValueGsdID))
            {
                // Get Help. Optional.
                if (!ValueItemCache.ContainsKey(sValueGsdID))
                    throw new PreparationException("Couldn't get ValueItem with ID: '" + sValueGsdID + "'!");

                XPathNavigator tempnav = (XPathNavigator)ValueItemCache[sValueGsdID];
                XPathNodeIterator nodes = tempnav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
                if (nodes.MoveNext())
                {
                    hash[Models.s_FieldHelp] = GetText(nodes.Current);
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

                }

                // Check whether there are discrete values or it is an area.
                nodes = tempnav.SelectDescendants(Elements.s_Assign, Namespaces.s_GsdmlDeviceProfile, false);
                if (nodes.Count == 0)
                    area = true;
            }
            return area;

        }

        private static void GetFieldBitOffsetForRefData(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_BitOffset, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldBitOffset] = Attributes.s_DefaultBitOffset;            }
            else
            {

                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldBitOffset] = value;
            }
        }

        private static void GetFieldByteOffsetForRefData(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_ByteOffset, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldByteOffset] = Attributes.s_DefaultByteOffset;
            }
            else
            {
                
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldByteOffset] = value;
            }
        }

        private static void GetFieldBitLengthForRefData(XPathNavigator nav, IDictionary hash, string attr)
        {
            if (attr == Enums.s_BitArea)
            {
                attr = nav.GetAttribute(Attributes.s_BitLength, String.Empty);
                if (String.IsNullOrEmpty(attr))
                {
                    hash[Models.s_FieldBitLength] = Attributes.s_DefaultBitLength;
                }
                else
                {

                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                    hash[Models.s_FieldBitLength] = value;
                }
            }
            else
                hash[Models.s_FieldBitLength] = Enums.GetDataTypeBitLength(attr);
        }

        protected virtual void PrepareModulePlugData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ModulePlugData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFixedInSlots, null);
            hash.Add(Models.s_FieldUsedInSlots, null);
            hash.Add(Models.s_FieldAllowedInSlots, null);
            hash.Add(Models.s_FieldFixedInSlotsString, null);
            hash.Add(Models.s_FieldUsedInSlotsString, null);
            hash.Add(Models.s_FieldAllowedInSlotsString, null);

            // Get FixedInSlots attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FixedInSlots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldFixedInSlotsString] = attr;

            // Get UsedInSlots attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_UsedInSlots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldUsedInSlotsString] = attr;

            // Get AllowedInSlots attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_AllowedInSlots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldAllowedInSlotsString] = attr;
        }

        protected virtual void PrepareApplicationRelations(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ApplicationRelations data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldArBlockVersion, null);
            hash.Add(Models.s_FieldIocrBlockVersion, null);
            hash.Add(Models.s_FieldAlarmCrBlockVersion, null);
            hash.Add(Models.s_FieldSubmoduleDataBlockVersion, null);
            hash.Add(Models.s_FieldTimingProperties, null);

            try
            {
                // Get ARBlockVersion attribute. Required.
                string attr = nav.GetAttribute(Attributes.s_ArBlockVersion, String.Empty);

                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
                hash[Models.s_FieldArBlockVersion] = value;

                // Get IOCRBlockVersion attribute. Required.
                attr = nav.GetAttribute(Attributes.s_IocrBlockVersion, String.Empty);
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldIocrBlockVersion] = value;

                // Get AlarmCRBlockVersion attribute. Required.
                attr = nav.GetAttribute(Attributes.s_AlarmCrBlockVersion, String.Empty);
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldAlarmCrBlockVersion] = value;

                // Get SubmoduleDataBlockVersion attribute. Required.
                attr = nav.GetAttribute(Attributes.s_SubmoduleDataBlockVersion, String.Empty);
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldSubmoduleDataBlockVersion] = value;

                // Navigate to TimingProperties. Optional.
                object obj = null;
                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_TimingProperties, Namespaces.s_GsdmlDeviceProfile);
                // Create element if available.
                if (nodes.MoveNext())
                {
                    obj = this.CreateGsdObject(Models.s_ObjectTimingProperties, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectTimingProperties + "' couldn't be created!");
                }
                hash[Models.s_FieldTimingProperties] = obj;

            }
            catch (CreationException e)
            {
                throw new PreparationException("Couldn't prepare ApplicationRelations!", e);
            }
            catch (PreparationException e)
            {
                throw new PreparationException("Couldn't prepare ApplicationRelations!", e);
            }

        }


        protected virtual void PrepareTimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare TimingProperties data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSendClock, null);
            hash.Add(Models.s_FieldReductionRatio, null);

            try
            {

                // Get SendClock attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_SendClock, String.Empty);
                if (attr.Length == 0)
                    attr = Attributes.DefaultSendClock.GetValue(0).ToString();
                hash[Models.s_FieldSendClock] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultSendClock);

                // Get ReductionRatio attribute. Optional.
                attr = nav.GetAttribute(Attributes.s_ReductionRatio, String.Empty);
                if (attr.Length == 0)
                    attr = Attributes.DefaultReductionRatio.GetValue(0).ToString();
                hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultReductionRatio);

            }
            catch (CreationException e)
            {
                throw new PreparationException("Couldn't prepare TimingProperties!", e);
            }
            catch (PreparationException e)
            {
                throw new PreparationException("Couldn't prepare TimingProperties!", e);
            }

        }

        #endregion

        #region Creation

        protected virtual void CreateRecordDataItems()
        {
            // NOTE: At the moment there are no record data items (ApplicationProcess) available from the GSD(ML)!!!
           
        }

        protected virtual void CreateVirtualSubmodules()
        {
            // Select all VirtualSubmodules
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_VirtualSubmoduleItem]);

            // Check whether VirtualSubmodules are available.
            if (nodes.Count == 0)
                return;
            //throw new CreationException("Elements '" + Elements.VirtualSubmoduleItem + "' aren't available from the actual GSDML file!");

            // Prepare data for each VirtualSubmodule, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectVirtualSubmodule;
                ModuleObject obj = (ModuleObject)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectVirtualSubmodule + "' couldn't be created!");

                // Add object to store.
                CStore.VirtualSubmodules.Add(obj.GsdID, obj);
            }
        }

        protected virtual void CreateModules()
        {
            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ModuleItem]);

            
                // Prepare data for each Module, create it and add it to store.
                while (nodes.MoveNext())
                {
                    // Create object.
                    string name = Models.s_ObjectModule;
                    ModuleObject obj = (ModuleObject)CreateGsdObject(name, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectModule + "' couldn't be created!");

                    // Add object to store.
                    this.CStore.Modules.Add(obj.GsdID, obj);
                            }
        }

        protected virtual void CreateCommunicationInterfaces()
        {
            // NOTE: At the moment there are no communication interfaces available from the GSD(ML)!!!

        }

        protected virtual void CreateDeviceAccessPoints()
        {
            // Select all DeviceAccessPoints
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_DeviceAccessPointItem]);

            // Check whether DeviceAccessPoints are available.
            if (nodes.Count == 0)
                throw new CreationException("Elements '" + Elements.s_DeviceAccessPointItem + "' aren't available from the actual GSDML file!");

            // Prepare data for each DeviceAccessPoint, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectDeviceAccessPoint;
                ModuleObject obj = (ModuleObject)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectDeviceAccessPoint + "' couldn't be created!");

                // Add object to store.
                CStore.DeviceAccessPoints.Add(obj.GsdID, obj);
            }
        }

        protected virtual void CreateDevice()
        {
            // Select profile body.
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ProfileBody]);

            // Check whether ProfileBody is available.
            if (nodes.Count == 0)
                throw new CreationException(
                    "Element '" + Elements.s_ProfileBody + "' isn't available from the actual GSDML file!");

            // Prepare data for the Device, create it and add it to store.
            if (!nodes.MoveNext())
            {
                return;
            }

            // Create object.
            string name = Models.s_ObjectDevice;
            object obj = CreateGsdObject(name, nodes.Current);
            if (null == obj)
                throw new CreationException(
                    "Object '" + Models.s_ObjectDeviceStructureElement + "' couldn't be created!");

            // Add object to store.
            CStore.Device = obj;

        }

        #endregion

        #endregion

    }
}


