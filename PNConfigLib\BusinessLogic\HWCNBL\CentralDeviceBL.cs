/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDeviceBL.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Constants.Methods;
using System.Collections.Generic;
using System.Globalization;

#endregion

namespace PNConfigLib.HWCNBL
{
    internal class CentralDeviceBL : HwcnBusinessLogic
    {
        internal CentralDeviceBL(CentralDevice centralDevice)
        {
            CentralDevice = centralDevice;
            CentralDevice.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PositionNumber, 1);
        }

        internal CentralDevice CentralDevice { get; }

        internal virtual void Configure(CentralDeviceType xmlCentralDevice, PNDriverType lonCentralDevice)
        {
            InitBL(xmlCentralDevice, lonCentralDevice);
        }

        private void InitActions()
        {
            CentralDevice.BaseActions.RegisterMethod(GetDataRecordsConfCpu.Name, CreateExpectedConfigDataRecordBlock);
        }

        private void InitBL(CentralDeviceType xmlCentralDevice, PNDriverType lonCentralDevice)
        {
            InitAttributes(xmlCentralDevice, lonCentralDevice);
            InitActions();
        }

        #region Attributes
        private void InitAttributes(CentralDeviceType xmlCentralDevice, PNDriverType lonCentralDevice)
        {
            FillGeneralAttributes(CentralDevice, xmlCentralDevice.General
                , xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item as string
                , lonCentralDevice.DeviceName
                , lonCentralDevice.DeviceID);
            bool isCustomizationEnabled = false;
            if (xmlCentralDevice.Customization != null)
            {
                isCustomizationEnabled = true;
                CentralDevice.AttributeAccess.AddAnyAttribute<int>(
                    InternalAttributeNames.PnVendorIdCustomized,
                    xmlCentralDevice.Customization.PNVendorID);
                CentralDevice.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnDeviceIdCustomized, xmlCentralDevice.Customization.PNDeviceID);
            }
            CentralDevice.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnCustomizationEnabled, isCustomizationEnabled);
            CentralDevice.AttributeAccess.AddAnyAttribute(InternalAttributeNames.InterfaceType, lonCentralDevice.Interface.InterfaceType);
        }
        #endregion

        private void CreateExpectedConfigDataRecordBlock(IMethodData methoddata)
        {
            int PNModuleIdentNumber = CentralDevice.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnModuleIdentNumber,
                new AttributeAccessCode(),
                0);
            Interface controllerInterface = CentralDevice.GetInterface();

            List<PclObject> subModules = new List<PclObject>();
            subModules.Add(controllerInterface);
            subModules.AddRange(controllerInterface.GetPortModulesSorted());

            short numberOfSubModules = Convert.ToInt16(subModules.Count, CultureInfo.InvariantCulture);
            int sizeOfBlob = 16 + numberOfSubModules * 16; // 16 byte header + n * 16 byte submodule entry

            BlobCreator creator = new BlobCreator(sizeOfBlob);
            creator.BigEndian = true;

            // 16 bytes fix
            creator.SerializeByte(0x02);    // (U8)  ExpectedConfigVersionHigh
            creator.SerializeByte(0x00);    // (U8)  ExpectedConfigVersionLow
            creator.SerializeIntExt(0x0001, 2); // (U16) Number of APIs
            creator.SerializeIntExt(0, 4);        // (U32) API
            creator.SerializeIntExt(PNModuleIdentNumber, 4);    // (U32) PNModuleIdentNumber
            creator.SerializeIntExt(0x0000, 2); // (U16) ExpModuleProperties (0 = no action)
            creator.SerializeIntExt(numberOfSubModules, 2);      // (U16) Number of subslots

            // 16 bytes each submodule entry
            for (int i = 0; i < numberOfSubModules; i++)
            {
                int positionNumber = subModules[i].AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    0);
                uint subIdentNumber = subModules[i].AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnSubmoduleIdentNumber,
                    new AttributeAccessCode(),
                    0);

                creator.SerializeIntExt(positionNumber, 2);  // SubslotNumber 
                creator.SerializeIntExt(0xFFFF, 2);  // SubslotInputOffset
                creator.SerializeIntExt(0xFFFF, 2);  // SubslotOutputOffset
                creator.SerializeIntExt(0, 2);  // ExpSubmoduleProperties 
                creator.SerializeIntExt((int)subIdentNumber, 4);    // PNSubmoduleIdentNumber
                creator.SerializeIntExt(0, 2);  // SubmoduleInput-DataDescription
                creator.SerializeIntExt(0, 2);  // SubmoduleOutput-DataDescription
            }

            methoddata.ReturnValue = creator.ToByteArray();
        }
    }
}