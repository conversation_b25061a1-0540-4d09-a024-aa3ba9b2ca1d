/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IOAddressManager.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.HWCNBL.Constants.AttributeValues;

namespace PNConfigLib.PNProjectManager
{
    using PNConfigLib.Consistency;
    using PNConfigLib.HWCNBL.Constants;

    internal class IOAddressManager
    {
        private List<int> availableInputAddresses = Enumerable.Range(0, 32766).ToList();
        private List<int> availableOutputAddresses = Enumerable.Range(0, 32766).ToList();

        internal List<int> GetFreeAddresses(int addressLength, IoTypes ioType, bool hasStartAddress, int startAddress = 0)
        {
            List<int> retval = new List<int>();
            List<int> freeAddreses = new List<int>();
            List<int> availableAddresses = ioType == IoTypes.Input ? availableInputAddresses : availableOutputAddresses;

            if (!hasStartAddress)
            {
                freeAddreses = GetFreeRange(addressLength, availableAddresses);
            }
            else
            {
                int endAddress = startAddress + addressLength;
                for (; startAddress < endAddress; startAddress++)
                {
                    freeAddreses.Add(startAddress);
                }
            }
            freeAddreses.ForEach(addr => availableAddresses.Remove(addr));

            if (freeAddreses.Count == 0)
            {
                retval.Add(0);
                retval.Add(0);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.IoAddressScheduleIsNotPossible,
                    ioType.ToString());
            }
            else
            {
                retval.Add(freeAddreses.First());
                retval.Add(freeAddreses.Last());
            }
            
            return retval;
        }

        /* Explanation for the logic
        We need an available IOAddress block to assign new IOData module. 
        If the difference is equal to IO data length, it means available address contains all the element for the range.
        For instance we have 8 byte length input module and we have available address list like that;
        ####XX######X########## which represents a list (0,1,2,3,6,7,8,9,10,11,13,14,15,16,17,18,19,20,21,22)
        # : represents containing element, X : available list does not contains this element
        It means that this list does not contain 4,5 and 12. 
        From the point 0 which is also 0 check the difference. Next element is 1, then, 2,3,6,7,8,9,10,11,13.
        For start point 0, Available[7] - Available[0] is 9 (because 4,5 is missing) and for 1 Available[8] - Available[1] is also 9.
        When the start point is 10, Available[17] - Available[10] is 8, which means all the numbers in the range exist in the list.
        Available[17] -> 20, Available[10] -> 13, Available[8] -> 10, Available[7] -> 9, Available[1] -> 1, Available[0] -> 0
        For start point 
        13 is the first point which is suitable, continuous for IO data and 13-20 block is reserved for this IO data.*/
        private List<int> GetFreeRange(int addressLength, List<int> availableAddresses)
        {
            List<int> freeRange = new List<int>();
            int startPoint = 0;

            while (startPoint + addressLength - 1 < availableAddresses.Count)
            {
                int difference = availableAddresses[startPoint + addressLength - 1] - availableAddresses[startPoint];
                if (difference == addressLength - 1)
                {
                    for (int i = 0; i < addressLength; i++)
                    {
                        freeRange.Add(availableAddresses[startPoint + i]);
                    }

                    break;
                }

                startPoint++;
            }
            
            return freeRange;
        }
    }
}
