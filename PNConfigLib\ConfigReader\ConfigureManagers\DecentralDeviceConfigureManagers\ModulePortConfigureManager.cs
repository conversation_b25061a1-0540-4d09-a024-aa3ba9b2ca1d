﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModulePortConfigureManager.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class ModulePortConfigureManager
    {
        private Project m_Project;

        private Topology.Topology m_Topology
        {
            get;
        }

        internal ModulePortConfigureManager(Project project, Topology.Topology topology) 
        {
            m_Topology = topology;
            m_Project = project;
        }

        internal void Configure(ModuleCatalog moduleCatalog, 
            List<PortType> xmlPorts, 
            Module module, 
            string gsdPath) 
        {
            FillSystemDefinedPorts(
                    moduleCatalog.SystemDefinedPorts,
                    moduleCatalog.PluggableSubmoduleList,
                    xmlPorts,
                    module,
                    gsdPath);
        }

        private void FillSystemDefinedPorts(
            Dictionary<int, PortCatalog> systemDefinedPortLookup,
            Dictionary<string, SlotRelation> pluggableModules,
            List<PortType> xmlPorts,
            Module portContainer,
            string gsdPath)
        {
            byte iPortNumberCursor = 0;
            List<PortType> allPorts = new List<PortType>(xmlPorts);
            List<PortType> sysDefPorts = FillXmlPortsBySystemDefinedPorts(systemDefinedPortLookup, xmlPorts);
            allPorts.AddRange(sysDefPorts);
            foreach (PortType xmlPort in allPorts.OrderBy(x => x.SubslotNumber))
            {
                Port port = new Port(xmlPort.PortNumber, (int)xmlPort.SubslotNumber);
                xmlPort.PortNumber = ++iPortNumberCursor;
                IPortBL portBL = null;
                portContainer.AddPort(port);
                if (systemDefinedPortLookup.ContainsKey((int)xmlPort.SubslotNumber))
                {
                    port.PCLCatalogObject = systemDefinedPortLookup[(int)xmlPort.SubslotNumber];
                }
                else if (pluggableModules.ContainsKey(xmlPort.GSDRefID))
                {
                    port.PCLCatalogObject = Catalog.PortList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", gsdPath, xmlPort.GSDRefID)];
                }
                else
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        port,
                        ConsistencyConstants.XML_PortWithGivenSubslotNumberDoesNotExist,
                        xmlPort.SubslotNumber,
                        xmlPort.PortNumber,
                        portContainer.Id,
                        portContainer.ParentObject.Id);
                }


                if ((port.PCLCatalogObject != null))
                {
                    portBL = new PNIoPortBusinessLogic(new PNBasePortBL(port));
                }

                if (portBL != null)
                {
                    bool isLocal;
                    portBL.Configure(
                        xmlPort,
                        ProjectManagerUtilities.FindPortInterconnectionByInterfaceAndPortNumber(
                            xmlPort.GSDRefID,
                            xmlPort.PortNumber,
                            m_Topology,
                            out isLocal),
                            allPorts.OrderBy(x => x.SubslotNumber).Count(),
                        isLocal);
                    m_Project.BusinessLogicList.Add(portBL);
                }
            }
        }

        private List<PortType> FillXmlPortsBySystemDefinedPorts(
           Dictionary<int, PortCatalog> systemDefinedPortLookup,
           List<PortType> xmlPorts,
           SortedList<uint, PortCatalog> portCatalog = null)
        {
            List<PortType> retval = new List<PortType>();
            Dictionary<uint, PortCatalog> catalogLookup = new Dictionary<uint, PortCatalog>();
            FillSystemDefinedValuesToPort(systemDefinedPortLookup, portCatalog, catalogLookup);
            byte iPortNumberCursor = 0;
            foreach (uint slotNumber in catalogLookup.Keys)
            {
                ++iPortNumberCursor;
                if (xmlPorts.All(p => p.PortNumber != iPortNumberCursor))
                {
                    PortType xmlPort = new PortType { SubslotNumber = (ushort)slotNumber, PortNumber = iPortNumberCursor };

                    if (catalogLookup[slotNumber].AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.GsdId))
                    {
                        xmlPort.GSDRefID = catalogLookup[slotNumber].AttributeAccess.AttributeList[InternalAttributeNames.GsdId] as string;
                    }
                    retval.Add(xmlPort);
                }
                else
                {
                    // set subslot number instead of 0
                    if (xmlPorts.Find(p => p.PortNumber == iPortNumberCursor).SubslotNumber == 0)
                    {
                        xmlPorts.Find(p => p.PortNumber == iPortNumberCursor).SubslotNumber = (ushort)slotNumber;
                    }
                }
            }
            return retval;
        }

        internal static void FillSystemDefinedValuesToPort(Dictionary<int, PortCatalog> systemDefinedPortLookup, SortedList<uint, PortCatalog> portCatalog, Dictionary<uint, PortCatalog> catalogLookup)
        {
            foreach (int subslotNumber in systemDefinedPortLookup.Keys)
            {
                if (!catalogLookup.ContainsKey((uint)subslotNumber))
                {
                    catalogLookup.Add((uint)subslotNumber, systemDefinedPortLookup[subslotNumber]);
                }
            }
            if (portCatalog != null)
            {
                foreach (uint subslotNumber in portCatalog.Keys)
                {
                    if (!catalogLookup.ContainsKey(subslotNumber))
                    {
                        catalogLookup.Add(subslotNumber, portCatalog[subslotNumber]);
                    }
                }
            }
        }
    }
}
