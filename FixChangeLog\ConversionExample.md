# 配置文件转换示例

## 原有配置文件 (0616.json) 到新架构的转换

### 1. 项目元数据转换

**原有结构：**
```json
{
  "ProjectName": "0616",
  "CreationDate": "2025-06-16T20:21:09.2351524+08:00",
  "LastModifiedDate": "2025-06-16T20:21:39.5575067+08:00",
  "Version": "1.0",
  "CurrentStepStatus": 5
}
```

**新架构：**
```json
{
  "ProjectMetadata": {
    "ProjectName": "0616",
    "CreationDate": "2025-06-16T20:21:09.2351524+08:00",
    "LastModifiedDate": "2025-06-16T20:21:39.5575067+08:00",
    "Version": "1.0",
    "CurrentStepStatus": 5,
    "Description": ""
  }
}
```

### 2. ListOfNodes部分转换

**原有结构（混合在根级别）：**
```json
{
  "MasterDeviceID": "PN_Driver_1",
  "MasterName": "PROFINET Driver",
  "MasterDeviceVersion": "v3.1",
  "MasterInterfaceID": "PN_Driver_1_Interface",
  "MasterInterfaceType": "Linux Native",
  "MasterInterfaceName": "PN_Driver_1_Interface",
  "Devices": [
    {
      "DeviceID": "IM60 V1.0 ",
      "DeviceName": "ET200SMART_1",
      "GSDMLPath": "E:\\Data\\TEST\\0616\\GSDMLs\\gsdml-v2.34-#siemens-et200smart-im60-20240325.xml",
      "GSDRefID": "DAP1",
      "InterfaceID": "ET200SMART_1_Interface",
      "InterfaceName": "ET200SMART_1_Interface"
    }
  ]
}
```

**新架构（清晰分离）：**
```json
{
  "ListOfNodesConfiguration": {
    "ListOfNodesID": "0616_ListOfNodes",
    "SchemaVersion": "1.0",
    
    "PNDriver": {
      "DeviceID": "PN_Driver_1",
      "DeviceName": "PROFINET Driver",
      "DeviceVersion": "v3.1",
      "Interface": {
        "InterfaceID": "PN_Driver_1_Interface",
        "InterfaceName": "PN_Driver_1_Interface",
        "InterfaceType": "Linux Native",
        "CustomInterfacePath": ""
      }
    },
    
    "DecentralDevices": [
      {
        "DeviceID": "ET200SMART_1",
        "DeviceName": "ET200SMART_1", 
        "GSDPath": "E:\\Data\\TEST\\0616\\GSDMLs\\gsdml-v2.34-#siemens-et200smart-im60-20240325.xml",
        "GSDRefID": "DAP1",
        "Interface": {
          "InterfaceID": "ET200SMART_1_Interface",
          "InterfaceName": "ET200SMART_1_Interface"
        }
      }
    ]
  }
}
```

### 3. Configuration部分转换

**原有结构（网络配置混合）：**
```json
{
  "MasterIPAddress": "***********",
  "MasterSubnetMask": "*************",
  "MasterRouterAddress": "***********",
  "MasterPNDeviceName": "profinetdriver",
  "MasterSendClock": "1",
  "Devices": [
    {
      "IPAddress": "***********",
      "SubnetMask": "*************",
      "DeviceNumber": 1,
      "PNDeviceName": "ET200SMART_1"
    }
  ]
}
```

**新架构（标准化结构）：**
```json
{
  "ConfigurationSettings": {
    "ConfigurationID": "0616_Config",
    "ConfigurationName": "0616",
    "ListOfNodesRefID": "0616_ListOfNodes",
    "SchemaVersion": "1.0",
    
    "CentralDevice": {
      "DeviceRefID": "PN_Driver_1",
      "CentralDeviceInterface": {
        "InterfaceRefID": "PN_Driver_1_Interface",
        "EthernetAddresses": {
          "IPProtocol": {
            "SetInTheProject": {
              "IPAddress": "***********",
              "SubnetMask": "*************",
              "RouterAddress": "***********"
            }
          },
          "PROFINETDeviceName": {
            "PNDeviceName": "profinetdriver"
          }
        }
      },
      "AdvancedConfiguration": {
        "RealTimeSettings": {
          "IOCommunication": {
            "SendClock": 1.0
          }
        }
      }
    },
    
    "DecentralDevices": [
      {
        "DeviceRefID": "ET200SMART_1",
        "DecentralDeviceInterface": {
          "InterfaceRefID": "ET200SMART_1_Interface",
          "EthernetAddresses": {
            "IPProtocol": {
              "SetInTheProject": {
                "IPAddress": "***********",
                "SubnetMask": "*************"
              }
            },
            "PROFINETDeviceName": {
              "PNDeviceName": "ET200SMART_1",
              "DeviceNumber": 1
            }
          }
        }
      }
    ]
  }
}
```

### 4. 模块配置转换

**原有结构（扁平化）：**
```json
{
  "Modules": [
    {
      "Index": 0,
      "IsSelected": false,
      "Position": "0",
      "ModuleID": "",
      "ModuleName": "IM60 V1.0 ",
      "SubmoduleID": "",
      "SubmoduleName": "主模块",
      "InterfaceSlot": "",
      "InputStartAddress": "0",
      "OutputStartAddress": "0",
      "Slot": 0,
      "InputAddress": 0,
      "OutputAddress": 0
    }
  ]
}
```

**新架构（层次化+保留UI字段）：**
```json
{
  "Modules": [
    {
      "ModuleRefID": "IM60_Module_1",
      "SlotNumber": 0,
      "General": {
        "Name": "IM60 V1.0",
        "Comment": "主模块"
      },
      "Submodules": [
        {
          "SubmoduleRefID": "IM60_Submodule_1",
          "General": {
            "Name": "主模块",
            "Comment": "IM60主模块"
          },
          "UIProperties": {
            "Index": 0,
            "IsSelected": false,
            "Position": "0"
          },
          "AddressConfiguration": {
            "InputStartAddress": "0",
            "OutputStartAddress": "0",
            "InputAddress": 0,
            "OutputAddress": 0
          }
        }
      ]
    }
  ]
}
```

## 转换规则总结

### 1. 命名转换规则
- `MasterDeviceID` → `PNDriver.DeviceID`
- `MasterName` → `PNDriver.DeviceName`
- `MasterInterfaceID` → `PNDriver.Interface.InterfaceID`
- `MasterIPAddress` → `CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress`
- `DeviceID` (设备) → `DecentralDevices[].DeviceID`
- `GSDMLPath` → `GSDPath`

### 2. 结构转换规则
- 根级别字段 → 分组到对应的配置节
- 扁平化模块 → 层次化Module/Submodule结构
- 混合网络配置 → 标准化EthernetAddresses结构
- UI字段 → 保留在UIProperties节
- 地址字段 → 保留在AddressConfiguration节

### 3. 引用关系建立
- `ListOfNodesRefID` 建立Configuration到ListOfNodes的引用
- `DeviceRefID` 建立Configuration中设备到ListOfNodes中设备的引用
- `InterfaceRefID` 建立接口引用关系
- `ModuleRefID`/`SubmoduleRefID` 建立模块引用关系
