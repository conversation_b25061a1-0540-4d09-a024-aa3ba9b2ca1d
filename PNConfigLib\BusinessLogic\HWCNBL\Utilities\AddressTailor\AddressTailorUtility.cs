/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AddressTailorUtility.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.AddressTailor
{
    internal static class AddressTailorUtility
    {
        //########################################################################################

        #region Public Methods

        private static bool IsAddressTailoringSupportedIoControllerInterfaceStartObject(Interface ioControllerInterface)
        {
            bool retValue = false;
            if (ioControllerInterface != null)
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool addressTailoringSupported =
                    ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoAddressTailoring,
                        attributeAccessCode,
                        false);
                retValue = attributeAccessCode.IsOkay && addressTailoringSupported;
            }
            return retValue;
        }

        public static bool IsAddressTailoringEnabledIoControllerInterfaceStartObject(Interface ioControllerInterface)
        {
            bool retValue = false;

            if ((ioControllerInterface != null)
                && IsAddressTailoringSupportedIoControllerInterfaceStartObject(ioControllerInterface))
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool isAddresTailoringEnabled =
                    ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoAddressTailoringEnabled,
                        attributeAccessCode,
                        false);

                retValue = attributeAccessCode.IsOkay && isAddresTailoringEnabled;
            }
            return retValue;
        }

        public static bool IsAddressTailoringEnabledforInterface(Interface startObject)
        {
            PNIOOperatingModes operatingMode =
                (PNIOOperatingModes)
                startObject.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    (uint)PNIOOperatingModes.None);
            if (operatingMode == PNIOOperatingModes.IOController)
            {
                return IsAddressTailoringEnabledIoControllerInterfaceStartObject(startObject);
            }
            return
                IsAddressTailoringEnabledIoControllerInterfaceStartObject(
                    GetIoControllerInterfaceFromInterface(startObject));
        }

        public static void SetAddressTailoringState(Interface ioControllerInterface, bool addressTailoringState)
        {
            ioControllerInterface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnIoAddressTailoringEnabled,
                addressTailoringState);

            //The IO controller’s IPv4 suite within the project is set to “Set IP address using a different method”;
            //i.e. the IO controller’s interface does not have an IPv4 suite within the project.  
            DataModel.PCLObjects.Node node = ioControllerInterface.Node;
            int pnPNIpConfigModeSupported =
                ioControllerInterface.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnPnIpConfigModeSupported,
                    new AttributeAccessCode(),
                    0);

            if (pnPNIpConfigModeSupported != 0)
            {
                node.AttributeAccess.SetAnyAttribute(
                    InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                    addressTailoringState);
            }

            int pnIoIpConfigModeSupported =
                ioControllerInterface.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoIpConfigModeSupported,
                    new AttributeAccessCode(),
                    0);
            if (pnIoIpConfigModeSupported != 0)
            {
                node.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                    addressTailoringState);
                node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Other);
            }
            if (!addressTailoringState)
            {
                IMethodData methodData = new MethodData { Name = NodeIPSuiteConfiguration.Name };
                node.BaseActions.CallMethod(methodData);
            }

            node.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnPnNoSAutoGenerate,
                !addressTailoringState);

            //The IO controller’s NameOfStation within the project is set to “Set PROFINET device name using a different method”;
            //i.e. the IO controller’s interface does not have a name within the project
            node.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnPnNoSViaOtherPath,
                addressTailoringState);

            List<Interface> deviceInterfaces = NavigationUtilities.GetDevicesOfController(ioControllerInterface);
            foreach (Interface deviceInterface in deviceInterfaces)
            {
                SetAddressTailoringStateOfDevice(addressTailoringState, deviceInterface);
            }
        }

        /// <summary>
        /// Sets the address tailoring related attributes to devices assigned to IO Controller
        /// </summary>
        /// <param name="addressTailoringState"></param>
        /// <param name="deviceInterface"></param>
        private static void SetAddressTailoringStateOfDevice(bool addressTailoringState, Interface deviceInterface)
        {
            DataModel.PCLObjects.Node deviceNode = deviceInterface.Node;
            if (AttributeUtilities.IsIDevice(deviceInterface))
            {
                PNIOOperatingModes currentOperatingMode = AttributeUtilities.GetOperatingMode(deviceInterface);
                if (currentOperatingMode == PNIOOperatingModes.IOControllerAndIODevice)
                {
                    deviceNode.AttributeAccess.SetAnyAttribute<bool>(
                        InternalAttributeNames.PnPnNoSViaOtherPath,
                        addressTailoringState);
                }
            }

            // PNConfigLib comment: GSDML based IO-Device's DeviceItemType is no CP although it is CP. 
            // Therefore no need to have IsCP check. One option is if IO-Device is Central device (i-Device).
            PclObject containerOfInterface = NavigationUtilities.GetOwnerOfInterface(deviceInterface);
            if (containerOfInterface.GetDevice() is CentralDevice)
            {
                SetAddressTailoringStateOnCP(deviceInterface, addressTailoringState);
            }

            // When we turn off the "Multiple use IO system" we need to set the PNIoIpSuiteViaOtherPath to false,
            // because the user has not able to modify ip address after it, if "IP address is set directly at the device" was checked
            // also the NodeIPConfiguration attribute has to be set, to control the radio buttons correctly
            if (AttributeUtilities.GetOperatingMode(deviceInterface) != PNIOOperatingModes.IODevice)
            {
                return;
            }

            if (addressTailoringState)
            {
                deviceNode.AttributeAccess.SetAnyAttribute<int>(
                    InternalAttributeNames.NodeIPConfiguration,
                    (int)NodeIPConfiguration.ViaIoController);
            }
            else
            {
                if (IsIoDeviceConfiguredAsLOCAL(deviceNode))
                {
                    deviceNode.AttributeAccess.SetAnyAttribute<int>(
                        InternalAttributeNames.NodeIPConfiguration,
                        (int)NodeIPConfiguration.Other);
                }
                else
                {
                    deviceNode.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnIoIpSuiteViaOtherPath, false);
                    deviceNode.AttributeAccess.SetAnyAttribute<int>(
                        InternalAttributeNames.NodeIPConfiguration,
                        (int)NodeIPConfiguration.Project);
                }
            }
        }

        /// <summary>
        /// Sets the NodeIPConfiguration attribute on the node according to the value
        /// </summary>
        /// <param name="interfaceSubmodule">The interfacesubmodule to set</param>
        /// <param name="addressTailoringEnabled">The boolean value of addresstailoring enabled</param>
        private static void SetAddressTailoringStateOnCP(Interface interfaceSubmodule, bool addressTailoringEnabled)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            PNIOOperatingModes operatingMode =
                (PNIOOperatingModes)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    (uint)PNIOOperatingModes.None);
            if (operatingMode == PNIOOperatingModes.IODevice)
            {
                PclObject deviceNode = interfaceSubmodule.Node;
                deviceNode.AttributeAccess.SetAnyAttribute<int>(
                    InternalAttributeNames.NodeIPConfiguration,
                    addressTailoringEnabled
                        ? (int)NodeIPConfiguration.ViaIoController
                        : (int)NodeIPConfiguration.Project);
            }
        }

        #endregion

        #region Private Methods

        private static Interface GetIoControllerInterfaceFromInterface(Interface startObject)
        {
            return NavigationUtilities.GetControllerOfDevice(startObject);
        }

        /// <summary> CheckIfAssignedIODevice </summary>
        /// <param name="ioDevNode"></param>
        /// <returns></returns>
        private static bool IsIoDeviceConfiguredAsLOCAL(DataModel.PCLObjects.Node ioDevNode)
        {

            Interface itfSubmodule = ioDevNode.ParentObject as Interface;
            if (itfSubmodule == null)
                return false;
            AttributeAccessCode acode = new AttributeAccessCode();
            Int32 pnPNIpConfigModeSupported = itfSubmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnPnIpConfigModeSupported, acode, 0);
            acode.Reset();
            int pnIoIpConfigModeSupported = itfSubmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnIoIpConfigModeSupported, acode, 0);

            return (pnPNIpConfigModeSupported == 0) && (pnIoIpConfigModeSupported == 4);
        }

        #endregion
    }
}