/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MrpDomainBusinessLogic.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.MultipleMrp;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.DomainManagement
{
    internal class MrpDomainBusinessLogic : DomainBusinessLogic
    {
        private const int s_MaxAllowedMembersInRing = 50;
        /// <summary>
        /// Getter for the MrpDomain data model object of this MrpDomainBusinessLogic class.
        /// </summary>
        private MrpDomain MrpDomain => PCLObject as MrpDomain;

        internal MrpDomainBusinessLogic(MrpDomain mrpDomain)
        {
            PCLObject = mrpDomain;
            mrpDomain.MrpDomainBusinessLogic = this;
            ConsistencyManager.RegisterConsistencyCheck(MrpDomain, CheckConsistency);
        }

        /// <summary>
        /// Fills the corresponding PROFINET attributes from the PNConfigLib configuration XML.
        /// </summary>
        /// <param name="xmlMrpDomain">The XML object of the mrp domain.</param>
        internal void Configure(MrpDomainType xmlMrpDomain)
        {
            if (string.IsNullOrEmpty(xmlMrpDomain.MrpDomainName))
            {
                MrpDomain.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlMrpDomain.MrpDomainID);
            }
            else
            {
                MrpDomain.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlMrpDomain.MrpDomainName);
            }
        }

        #region Consistency Checks

        private void CheckConsistency()
        {
            CheckManager();
            CheckMrpRings();
        }

        /// <summary>
        /// This method checks:
        /// 1- If there exists exactly one device with the role "manager".
        /// It is also not allowed to have a device with the role "manager(auto) 
        /// if a manager is defined in the media redundancy domain. 
        /// 2- If the mrp domain has at least one interface submodule with the role "client". 
        /// In this case it must also have at least one manager or manager(auto).
        /// </summary>
        private void CheckManager()
        {
            List<PNMrpRole> mrpRoleList = new List<PNMrpRole>();

            foreach (Interface interfaceSubmodule in MrpDomain.Participants)
            {
                if (IDeviceUtility.IsStandaloneDecPDEV(interfaceSubmodule))
                {
                    continue;
                }
                List<int> instanceNumbers =
                    MultipleMrpUtilities.GetParticipantInstancesFromMrpDomain(MrpDomain, interfaceSubmodule.Id);

                foreach (int instanceNum in instanceNumbers)
                {
                    // GetMultipleRole() handles non- multiple MRP cases too
                    mrpRoleList.Add(MultipleMrpUtilities.GetMultipleRole(interfaceSubmodule, instanceNum));
                }
            }
            int managerCount = mrpRoleList.Count(role => role.Equals(PNMrpRole.Manager));
            int managerAutoCount = mrpRoleList.Count(role => role.Equals(PNMrpRole.NormManagerAuto));
            int clientCount = mrpRoleList.Count(role => role.Equals(PNMrpRole.Client));

            if (managerCount > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    MrpDomain,
                    ConsistencyConstants.MrpMoreThanOneManager);
            }
            if ((managerCount == 1)
                && (managerAutoCount != 0))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    MrpDomain,
                    ConsistencyConstants.MrpManagerAndManagerAuto);
            }
            if ((clientCount > 0)
                && (managerCount == 0)
                && (managerAutoCount == 0))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    MrpDomain,
             ConsistencyConstants.MrpNoManager);
            }
        }

        /// <summary>
        /// This method checks if:
        /// 1- There are more than one mrp rings in the domain. This is an error. Normally, user
        /// must create a mrp-domain for each mrp-ring
        /// 2- There are more than maximum allowed interfaces (50) in an mrp-ring. 
        /// This is an error.
        /// 3- There is at least one mrp ring in the domain. A warning is displayed if a ring cannot be found in 
        /// the domain (user doesn't have to interconnect the ports and create a ring, but some of the 
        /// consistency checks can work only if mrp is configured with port interconnection). 
        /// </summary>
        private void CheckMrpRings()
        {
            List<List<Interface>> allRingsInDomain = PNTopologyUtilities.GetRings(MrpDomain.Participants);

            AttributeAccessCode ac = new AttributeAccessCode();
            IList<Interface> activeMrpInterfaces = new List<Interface>();

            foreach (Interface interfaceSubmodule in MrpDomain.Participants)
            {
                List<PNMrpRole> mrpRoleList = new List<PNMrpRole>();
                bool mrpSupported = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnMrpSupported,
                    ac.GetNew(),
                    false);

                List<int> instanceNumbers =
                    MultipleMrpUtilities.GetParticipantInstancesFromMrpDomain(MrpDomain, interfaceSubmodule.Id);
                foreach (int instanceNum in instanceNumbers)
                {
                    // GetMultipleRole() handles non- multiple MRP cases too
                    mrpRoleList.Add(MultipleMrpUtilities.GetMultipleRole(interfaceSubmodule, instanceNum));
                }
                if (mrpRoleList.Any<PNMrpRole>(r => r != PNMrpRole.NotInRing) && mrpSupported)
                {
                    activeMrpInterfaces.Add(interfaceSubmodule);
                }
            }

            if (activeMrpInterfaces.Count > s_MaxAllowedMembersInRing)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    MrpDomain,
                    ConsistencyConstants.MrpMaxSizeExceeded);
            }

            if (allRingsInDomain != null)
            {
                List<List<Interface>> activeRingsInDomain = new List<List<Interface>>();
                foreach (List<Interface> ring in allRingsInDomain)
                {
                    bool activeMrpIeFound = false;
                    bool passiveOrNonMrpIeFound = false;

                    foreach (Interface interfaceSubmodule in ring)
                    {
                        bool mrpSupported = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnMrpSupported,
                            ac.GetNew(),
                            false);

                        int instanceNumber =
                            MultipleMrpUtilities.GetParticipantInstancesFromMrpDomain(MrpDomain, interfaceSubmodule.Id)
                                [0];

                        PNMrpRole mrpRole = MultipleMrpUtilities.GetMultipleRole(interfaceSubmodule, instanceNumber);
                        if (mrpSupported && (mrpRole != PNMrpRole.NotInRing))
                        {
                            activeMrpIeFound = true;
                        }
                        else
                        {
                            passiveOrNonMrpIeFound = true;
                        }
                    }
                    if (activeMrpIeFound && !passiveOrNonMrpIeFound)
                    {
                        activeRingsInDomain.Add(ring);
                    }
                }

                if (activeRingsInDomain.Count > 1)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        MrpDomain,
                        ConsistencyConstants.MrpMoreThanOneRing);
                }
                else if (activeRingsInDomain.Count == 1)
                {
                    IList<Interface> interfaceInRing =
                        (from Interface interfaceSubmodule in activeRingsInDomain[0] select interfaceSubmodule)
                        .ToList();

                    // interfaces which are not in a ring, but have a role
                    IList<Interface> interfaceWithWrongRole =
                        (from Interface interfaceSubmodule in activeMrpInterfaces
                         where !interfaceInRing.Contains(interfaceSubmodule)
                         select interfaceSubmodule).ToList();

                    foreach (Interface wrongInterface in interfaceWithWrongRole)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            MrpDomain,
                            ConsistencyConstants.MrpRoleOutsideTheRing,
                            AttributeUtilities.GetSubmoduleNameWithContainer(wrongInterface));
                    }
                }
            }
        }

        #endregion
    }
}
