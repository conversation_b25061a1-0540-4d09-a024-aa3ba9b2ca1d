/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: APLPortClassification.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Runtime.InteropServices;
using System.Collections;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// This element adds more classification to the APL MAU-type extension.
    /// </summary>
    //[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C9B")] 
    public class AplPortClassification : 
		GsdObject,
		IAplPortClassification

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the APLPortClassification if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public AplPortClassification()
		{

        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private SegmentClasses m_SegmentClass;
        private PortClasses m_PortClass;
        private PowerClasses m_PowerClass;
        private ProtectionClasses m_ProtectionClass;

        private string m_SegmentClassAsString;
        private string m_PortClassAsString;
        private string m_PowerClassAsString;
        private string m_ProtectionClassAsString;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the SegmentClass.
        /// </summary>
        public GSDI.SegmentClasses SegmentClass => this.m_SegmentClass;

        /// <summary>
        /// Accesses the SegmentClass.
        /// </summary>
        public string SegmentClassAsString => this.m_SegmentClassAsString;

        /// <summary>
        /// Accesses the PortClass.
        /// </summary>
        public GSDI.PortClasses PortClass => this.m_PortClass;

        /// <summary>
        /// Accesses the PortClass.
        /// </summary>
        public string PortClassAsString => this.m_PortClassAsString;

        /// <summary>
        /// Accesses the PowerClass.
        /// </summary>
        public GSDI.PowerClasses PowerClass => this.m_PowerClass;

        /// <summary>
        /// Accesses the PowerClass.
        /// </summary>
        public string PowerClassAsString => this.m_PowerClassAsString;

        /// <summary>
        /// Accesses the ProtectionClass.
        /// </summary>
        public GSDI.ProtectionClasses IS_ProtectionClass => this.m_ProtectionClass;

        /// <summary>
        /// Accesses the ProtectionClass.
        /// </summary>
        public string ProtectionClassAsString => this.m_ProtectionClassAsString;

#if !S7PLUS
        #region COM Interface Members Only


        #endregion
#endif

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter
				if (null == hash)
					throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldSegmentClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.SegmentClasses)
                    m_SegmentClass = (GSDI.SegmentClasses)hash[member];

                member = Models.s_FieldSegmentClassAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_SegmentClassAsString = (string)hash[member];

                member = Models.s_FieldPortClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.PortClasses)
                    m_PortClass = (GSDI.PortClasses)hash[member];

                member = Models.s_FieldPortClassAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_PortClassAsString = (string)hash[member];

                member = Models.s_FieldPowerClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.PowerClasses)
                    m_PowerClass = (GSDI.PowerClasses)hash[member];

                member = Models.s_FieldPowerClassAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_PowerClassAsString = (string)hash[member];

                member = Models.s_FieldProtectionClass;
                if (hash.ContainsKey(member) && hash[member] is GSDI.ProtectionClasses)
                    m_ProtectionClass = (GSDI.ProtectionClasses)hash[member];

                member = Models.s_FieldProtectionClassAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_ProtectionClassAsString = (string)hash[member];

                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectAPLPortClassification);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successful, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{
            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldSegmentClass, this.m_SegmentClass.ToString(), Export.s_SubtypeSegmentClasses);
            Export.WriteEnumProperty(ref writer, Models.s_FieldPortClass, this.m_PortClass.ToString(), Export.s_SubtypePortClasses);
            Export.WriteEnumProperty(ref writer, Models.s_FieldPowerClass, this.m_PowerClass.ToString(), Export.s_SubtypePowerClasses);
            Export.WriteEnumProperty(ref writer, Models.s_FieldProtectionClass, this.m_ProtectionClass.ToString(), Export.s_SubtypeProtectionClasses);

            return true; 
		}


		#endregion
	}
}
