/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIsochronIFDecentralBL.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Isochrone;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIsochronIFDecentralBL : IFDecorator
    {
        public PNIsochronIFDecentralBL(IInterfaceBusinessLogic decoratedIFBL) : base(decoratedIFBL)
        {
            InitBL();
        }

        public override void InitBL()
        {
            InitActions();
        }

        //########################################################################################

        #region Generic Methods

        private void GenericMethodGetIsochronousModeData(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            try
            {
                // init return value
                methodData.ReturnValue = false;
                PclObject subModule = (PclObject)methodData.Arguments[GetIsochronousModeData.ModuleParameter];
                PNIsoDataModel isoDataModel = PNIsoDataModel.Model1;

                if (methodData.Arguments.AllKeys.Contains(GetIsochronousModeData.PNIsoDataModel))
                {
                    isoDataModel = (PNIsoDataModel)methodData.Arguments[GetIsochronousModeData.PNIsoDataModel];
                }

                AttributeAccessCode ac = new AttributeAccessCode();
                if (!Interface.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsochron, ac, false))
                {
                    return;
                }

                bool returnVal;
                byte[] block = IsochronConfigUtility.GetIsochronousModeData(
                    Interface,
                    subModule,
                    out returnVal,
                    isoDataModel);
                methodData.ReturnValue = returnVal;

                if (returnVal)
                {
                    ParameterDatasetStruct paramDSIsochronModeData = new ParameterDatasetStruct();
                    paramDSIsochronModeData.ParaDSNumber = 0x8030;
                    paramDSIsochronModeData.ParaDSIdentifier = 0;
                    paramDSIsochronModeData.AddParaBlock(block);

                    methodData.Arguments[GetIsochronousModeData.IsochronousModeDataBlockEntry] = paramDSIsochronModeData;
                }
            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetIsochronousModeData", e);
            }
        }

        #endregion

        private void InitActions()
        {
            Interface.BaseActions.RegisterMethod(CalculateIsochronParametersForGsdml.Name, GenericMethodCalculateIsochronParametersForGsdml);
            Interface.BaseActions.RegisterMethod(GetIsochronousModeData.Name, GenericMethodGetIsochronousModeData);
            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodConsistencyCheck);
        }

        #region consistency checking

        /// <summary>
        /// Consistency-Check method
        /// </summary>
        private void MethodConsistencyCheck()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool isIsochron = Interface.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsochron, ac, false);

            if (isIsochron)
            {
                SharedDeviceUtility.ConsistencyCheckIsochronAndDeviceWithSharedInterface(Interface);

                bool isValidSendclock = PNIsochronUtilities.CheckUpdateTimeGreaterThanTiToValue(Interface);

                if (isValidSendclock)
                {
                    PNIsochronUtilities.CheckIsochronTiToValueRange(Interface);
                }

                PNIsochronUtilities.CheckIfIsoCouplingSupported(Interface);

                PNIsochronUtilities.CheckIRTtopAndIsochronMode(Interface);
            }

            PNIsochronUtilities.CheckIsochronSubmodulesInIoDevice(Interface);
        }
        
        #endregion

        //########################################################################################
        #region Nested Clases
        private class TiToBoundaries
        {
            public int TiMin;
            public int ToMin;
            public int TiMax;
            public int ToMax;
            public uint T_DC_Min;
            public uint T_DC_Max;
        }

        internal enum IsochronParameter
        {
            PNIsoTiMinCalculated = 1,
            PNIsoTiMaxCalculated = 2,
            PNIsoToMinCalculated = 3,
            PNIsoToMaxCalculated = 4,
            PNIsoT_DC_BASE = 5,
            PNIsoT_DC_MIN = 6,
            PNIsoT_DC_MAX = 7,
            PNIsoT_IO_BASE = 8
        }
        #endregion

        public override void Configure(IConfigInterface xmlDeviceInterface, SyncDomainType syncDomainType = null)
        {
            DecentralDeviceTypeDecentralDeviceInterface xmlDecentralDeviceInterface =
                xmlDeviceInterface as DecentralDeviceTypeDecentralDeviceInterface;

            base.Configure(xmlDecentralDeviceInterface, syncDomainType);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIsochron
                , xmlDecentralDeviceInterface.IsochronousMode.IsoModeEnabled);

            object calculationMode = xmlDecentralDeviceInterface.IsochronousMode.TiToValues.Item;

            IsochronousModeTypeTiToValuesManual calculationTypeManual = calculationMode as IsochronousModeTypeTiToValuesManual;

            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIsoTiToCalcMode
                , (uint)PNIsoTiToCalcMode.AutomaticMinimum);

            if (xmlDecentralDeviceInterface.IsochronousMode.IsoModeEnabled)
            {
                if (calculationTypeManual != null)
                {
                    Interface.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnIsoTiToCalcMode,
                        (uint)PNIsoTiToCalcMode.Manual);
                    Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTi,
                        (long)(calculationTypeManual.TimeTi * PNIsochronUtilities.MillisecToNanosecInt));

                    Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTo,
                        (long)(calculationTypeManual.TimeTo * PNIsochronUtilities.MillisecToNanosecInt));
                }
                Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIsoUseDeviceLocalOutputValid,
                    false);
                Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIsoDataModel, 1);
            }
        }

        private void GenericMethodCalculateIsochronParametersForGsdml(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            long tiMin = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoTiMinCalculated);
            long tiMax = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoTiMaxCalculated);
            long toMin = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoToMinCalculated);
            long toMax = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoToMaxCalculated);
            long dcMin = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoT_DC_MIN);
            long dcMax = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoT_DC_MAX);

            long tioBase = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoT_IO_BASE);
            long tdcBase = CalculateIsochronParameters(Interface, IsochronParameter.PNIsoT_DC_BASE);

            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTiMinCalculated, tiMin);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTiRanges, tiMax);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoToMinCalculated, toMin);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoToRanges, toMax);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_MIN, dcMin);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_MAX, dcMax);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoT_IO_BASE, tioBase);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_BASE, tdcBase);

            AttributeAccessCode aac = new AttributeAccessCode();
            uint calcMode = Interface.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnIsoTiToCalcMode
                , aac, 0);
            PNIsoTiToCalcMode tiToVal;
            Enum.TryParse(calcMode.ToString(CultureInfo.InvariantCulture), out tiToVal);
            if (aac.IsOkay && (tiToVal == PNIsoTiToCalcMode.AutomaticMinimum))
            {
                Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTi, tiMin);

                Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTo, toMin);
            }
        }

        public static long CalculateIsochronParameters(Interface interfaceSubmodule, IsochronParameter calcType)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            if (interfaceSubmodule == null
                || !interfaceSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(InternalAttributeNames.PnIsochron, ac, false))
            {
                return 0;
            }

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerInterfaceOfDeviceInterface(interfaceSubmodule);
            long tdc = Utility.GetTdc(controllerInterfaceSubmodule);

            List<TiToBoundaries> titoValuesList = new List<TiToBoundaries>();
            HashSet<int> tiobaseList = new HashSet<int>();
            HashSet<int> t_DC_BaseList = new HashSet<int>();
            IoTypes deviceIoType = IoTypes.None;


            //iterate through all deviceitems of the decentral device
            List<PclObject> modules = PNNavigationUtility.GetModulesFromInterfaceSubmodule(interfaceSubmodule).ToList();
            List<Submodule> submodules = modules.Where(m => m is Module).SelectMany(m => ((Module)m).GetSubmodules())
                .ToList();
            List<Submodule> virtualSubmodules = modules.Where(m => m is Module)
                .SelectMany(m => ((Module)m).GetVirtualSubmodules()).ToList();
            IList<DataModel.PCLObjects.Port> ports = interfaceSubmodule.GetPorts();
            List<PclObject> allModules = new List<PclObject>();
            allModules.Add(interfaceSubmodule);
            allModules.AddRange(ports);
            allModules.AddRange(modules);
            allModules.AddRange(submodules);
            allModules.AddRange(virtualSubmodules);
            
            foreach (PclObject submodule in allModules)
            {
                IoTypes itemIoType = (IoTypes)submodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.IoType,
                    ac.GetNew(),
                    (int)IoTypes.None);
                if (ac.IsOkay)
                {
                    deviceIoType |= itemIoType;
                }

                if (!submodule.AttributeAccess.GetAnyAttribute<Boolean>(
                        InternalAttributeNames.ClockSyncMode,
                        ac.GetNew(),
                        false))
                {
                    continue;
                }

                //calculate Ti/To min/max for this submodule
                int tiobase =
                    (int)submodule.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.PnIsoT_IO_BASE,
                        ac.GetNew(),
                        1);
                if (ac.IsOkay)
                {
                    tiobaseList.Add(tiobase);
                }

                int t_DC_Base =
                    (int)submodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIsoT_DC_BASE,
                        ac.GetNew(),
                        1);
                if (ac.IsOkay)
                {
                    t_DC_BaseList.Add(t_DC_Base);
                }

                titoValuesList.Add(
                    CalculateTiToValues(calcType, tdc, tiobase, t_DC_Base, submodule, interfaceSubmodule));
            }

            //calculate LCM for tiobase variables to get the device level tiobase
            int tiobaseDevice = Math.Abs(GeneralUtilities.GetLCMM(tiobaseList.ToList()));
            uint t_DC_BaseDevice = (uint)Math.Abs(GeneralUtilities.GetLCMM(t_DC_BaseList.ToList()));

            return CalculateReturnValue(calcType, deviceIoType, tiobaseDevice, t_DC_BaseDevice, titoValuesList);
        }

        private static TiToBoundaries CalculateTiToValues(
            IsochronParameter calcType,
            long tdc,
            int tiobase,
            int t_DC_Base,
            PclObject deviceItemObject,
            Interface interfaceSubmodule)
        {
            TiToBoundaries titovalues = new TiToBoundaries();
            AttributeAccessCode ac = new AttributeAccessCode();

            switch (calcType)
            {
                case IsochronParameter.PNIsoTiMinCalculated:
                    {
                        titovalues.TiMin = (int)deviceItemObject.AttributeAccess.GetAnyAttribute<UInt32>(
                                               InternalAttributeNames.PnIsoT_IO_InputMin,
                                               ac.GetNew(),
                                               0) * tiobase;
                        break;
                    }
                case IsochronParameter.PNIsoTiMaxCalculated:
                case IsochronParameter.PNIsoToMaxCalculated:
                    {
                        //according to "detailed design isochron" this is valid for GSD based devices
                        //because Tpre and Tpost are both always 0
                        titovalues.ToMax = titovalues.TiMax = (int)Math.Floor(tdc / (decimal)tiobase) * tiobase;
                        break;
                    }
                case IsochronParameter.PNIsoToMinCalculated:
                    {
                        int tomin = (int)deviceItemObject.AttributeAccess.GetAnyAttribute<UInt32>(
                                        InternalAttributeNames.PnIsoT_IO_OutputMin,
                                        ac.GetNew(),
                                        0) * tiobase;
                        titovalues.ToMin =
                            (int)Math.Ceiling((GetToValid(interfaceSubmodule) + tomin) / (decimal)tiobase) * tiobase;
                        break;
                    }
                case IsochronParameter.PNIsoT_DC_MIN:
                    {
                        uint t_DC_Min = deviceItemObject.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIsoT_DC_MIN,
                            ac.GetNew(),
                            1);
                        titovalues.T_DC_Min = (uint)t_DC_Base * t_DC_Min;
                        break;
                    }
                case IsochronParameter.PNIsoT_DC_MAX:
                    {
                        uint t_DC_Max = deviceItemObject.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIsoT_DC_MAX,
                            ac.GetNew(),
                            1);
                        titovalues.T_DC_Max = (uint)t_DC_Base * t_DC_Max;
                        break;
                    }
            }
            return titovalues;
        }

        /// <summary>
        /// Calculates ToValid value
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <returns></returns>
        private static int GetToValid(Interface interfaceSubmodule)
        {
            long globalEndOfRed = 0;
            int pnisoTioOutputValid = 0;
            if (interfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.PNPlannerResults
                .LocalRxPeriodNoSyncValues.ContainsKey(interfaceSubmodule))
            {
                pnisoTioOutputValid = (int)interfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.PNPlannerResults
                    .LocalRxPeriodNoSyncValues[interfaceSubmodule];
            }

            if (interfaceSubmodule.SyncDomain != null)
            {
                globalEndOfRed = interfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.PNPlannerResults
                    .CalculatedPartIrt;
            }

            return Math.Max(Convert.ToInt32(globalEndOfRed), pnisoTioOutputValid);
        }

        private static long CalculateReturnValue(
            IsochronParameter calcType,
            IoTypes deviceIoType,
            int tiobaseDevice,
            uint t_DC_BaseDevice,
            List<TiToBoundaries> titoValuesList)
        {
            long returnValue = 0;
            Func<int, long> roundUpValue = val => (long)Math.Ceiling(val / (decimal)tiobaseDevice) * tiobaseDevice;

            switch (calcType)
            {
                case IsochronParameter.PNIsoT_IO_BASE:
                    {
                        returnValue = tiobaseDevice;
                        break;
                    }
                case IsochronParameter.PNIsoTiMinCalculated:
                    {
                        if ((deviceIoType & IoTypes.Input) == IoTypes.Input
                            && titoValuesList.Count > 0)
                        {
                            int tiMinMax = titoValuesList.Max(a => a.TiMin);
                            returnValue = roundUpValue(tiMinMax);
                        }
                        break;
                    }
                case IsochronParameter.PNIsoTiMaxCalculated:
                    {
                        if ((deviceIoType & IoTypes.Input) == IoTypes.Input
                            && titoValuesList.Count > 0)
                        {
                            int tiMaxMin = titoValuesList.Min(a => a.TiMax);
                            returnValue = roundUpValue(tiMaxMin);
                        }
                        break;
                    }
                case IsochronParameter.PNIsoToMinCalculated:
                    {
                        if ((deviceIoType & IoTypes.Input) == IoTypes.Input
                            && titoValuesList.Count > 0)
                        {
                            int toMinMax = titoValuesList.Max(a => a.ToMin);
                            returnValue = roundUpValue(toMinMax);
                        }
                        break;
                    }
                case IsochronParameter.PNIsoToMaxCalculated:
                    {
                        if ((deviceIoType & IoTypes.Input) == IoTypes.Input
                            && titoValuesList.Count > 0)
                        {
                            int toMaxMin = titoValuesList.Min(a => a.ToMax);
                            returnValue = roundUpValue(toMaxMin);
                        }
                        break;
                    }
                case IsochronParameter.PNIsoT_DC_BASE:
                    {
                        returnValue = t_DC_BaseDevice;

                        break;
                    }
                case IsochronParameter.PNIsoT_DC_MIN:
                    {
                        if (titoValuesList.Count > 0)
                        {
                            uint t_DC_Min = titoValuesList.Max(a => a.T_DC_Min);

                            if (t_DC_Min % t_DC_BaseDevice != 0)
                            {
                                t_DC_Min /= t_DC_BaseDevice;
                                t_DC_Min++;
                            }
                            else
                            {
                                t_DC_Min /= t_DC_BaseDevice;
                            }

                            returnValue = t_DC_Min;
                        }

                        break;
                    }
                case IsochronParameter.PNIsoT_DC_MAX:
                    {
                        if (titoValuesList.Count > 0)
                        {
                            uint t_DC_Max = titoValuesList.Min(a => a.T_DC_Max);

                            if (t_DC_Max % t_DC_BaseDevice != 0)
                            {
                                t_DC_Max /= t_DC_BaseDevice;
                                t_DC_Max--;
                            }
                            else
                            {
                                t_DC_Max /= t_DC_BaseDevice;
                            }

                            returnValue = t_DC_Max;
                        }

                        break;
                    }
            }
            return returnValue;
        }

        #region Definition of enumeration types for the validation process

        /// <summary>
        /// The possible results of the Ti BL property validation process
        /// </summary>
        public enum TiBlpCheck { Valid, TiToCannotBeCalculated, InvalidTi, NotSupportedTi, NotInRaster, TitoGreaterThanAppCycle };

        /// <summary>
        /// The possible results of the To BL property validation process
        /// </summary>
        public enum ToBlpCheck { Valid, TiToCannotBeCalculated, InvalidTo, NotSupportedTo, NotInRaster, TitoGreaterThanAppCycle };

        /// <summary>
        /// The possible results of the IsochronActivated BL property validation process
        /// </summary>
        public enum IsochronActivatedBlpCheck { Valid, IsochronousCouplingNotSupported, SharedIsochronousInterface };
        /// <summary>
        /// The possible results of the ClockSyncMode BL property validation process
        /// </summary>
        public enum ClockSyncModeBlpCheck { Valid, SharedModule, SharedIsochronousInterface };
        #endregion
    }
}