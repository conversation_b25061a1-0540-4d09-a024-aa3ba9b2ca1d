/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_00.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Globalization;
using System.Xml;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Xml.XPath;

using GSDI;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.Gsd.Interpreter.Checker;
using PNConfigLib.Gsd.Interpreter;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.1 and is based on GSD(ML) version 1.0.
    ///		
    /// </summary>
    internal class CheckerV0200 :
        CheckerV0100
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.0.
        /// </summary>
        public CheckerV0200()
        {
            SetSupportedGsdmlVersion(Constants.s_Version20);
        }

        #endregion

        //########################################################################################
        #region Fields

        protected const uint s_MaxIoDataFor3ByteCrc = 12;
        protected const uint s_MaxIoDataFor4ByteCrc = 123;
        protected const uint s_GuaranteedIoDataFor4ByteCrc = 12;

        #endregion

        //########################################################################################
        #region Properties

        protected virtual ReportTypes ReportType_0X000101041 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000101042 => ReportTypes.GSD_RT_Warning;

        protected virtual ReportTypes ReportType_0X000101052 => ReportTypes.GSD_RT_Warning;

        protected virtual ReportTypes ReportType_0X00010117 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0x00010119_2 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000101221 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000101318 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000101319 => ReportTypes.GSD_RT_MinorError;

        protected virtual string FMessageTrailer5Byte => Enums.s_FMessageTrailer5ByteTypingError;

        #endregion

        //########################################################################################
        #region Methods

        protected override bool RegisterChecks()
        {
            bool succeeded = true;

            try
            {
                succeeded = base.RegisterChecks();

                if (Checks == null)
                    Checks = new List<string>();

                Checks.Add(Constants.s_Cn_0X00010102);
                Checks.Add(Constants.s_Cn_0X00010103);
                Checks.Add(Constants.s_Cn_0X00010104);
                Checks.Add(Constants.s_Cn_0X00010105);
                Checks.Add(Constants.s_Cn_0X00010106);
                Checks.Add(Constants.s_Cn_0X00010107);
                Checks.Add(Constants.s_Cn_0X00010108);
                Checks.Add(Constants.s_Cn_0X00010109);
                Checks.Add(Constants.s_Cn_0X0001010A);
                Checks.Add(Constants.s_Cn_0X0001010B);
                Checks.Add(Constants.s_Cn_0X0001010D);
                Checks.Add(Constants.s_Cn_0X0001010E);
                Checks.Add(Constants.s_Cn_0X0001010F);
                Checks.Add(Constants.s_Cn_0X00010110);
                Checks.Add(Constants.s_Cn_0X000101101);
                Checks.Add(Constants.s_Cn_0X00010112);
                Checks.Add(Constants.s_Cn_0X00010113);
                Checks.Add(Constants.s_Cn_0X00010114);
                Checks.Add(Constants.s_Cn_0X00010116);
                Checks.Add(Constants.s_Cn_0X00010117);
                Checks.Add(Constants.s_Cn_0X00010118);
                Checks.Add(Constants.s_Cn_0X00010119);
                Checks.Add(Constants.s_Cn_0X0001011B);
                Checks.Add(Constants.s_Cn_0X0001011C);
                Checks.Add(Constants.s_Cn_0X0001011D);
                Checks.Add(Constants.s_Cn_0X0001011F);
                Checks.Add(Constants.s_Cn_0X00010120);
                Checks.Add(Constants.s_Cn_0X00010121);
                Checks.Add(Constants.s_Cn_0X00010122);
                Checks.Add(Constants.s_Cn_0X00010123);
                Checks.Add(Constants.s_Cn_0X00010124);
                Checks.Add(Constants.s_Cn_0X00010125);
                Checks.Add(Constants.s_Cn_0X00010126);
                Checks.Add(Constants.s_Cn_0X00010127);
                Checks.Add(Constants.s_Cn_0X000101281);
                Checks.Add(Constants.s_Cn_0X000101282);
                Checks.Add(Constants.s_Cn_0X00010129);
                Checks.Add(Constants.s_Cn_0X00010131);
                Checks.Add(Constants.s_Cn_0X00010132);
                Checks.Add(Constants.s_Cn_0X00010133);
                Checks.Add(Constants.s_Cn_0X00010134);
                Checks.Add(Constants.s_Cn_0X00010135);
                Checks.Add(Constants.s_Cn_0X00010136);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }
            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                Checks.Remove(Constants.s_Cn_0X00010003);
                Checks.Remove(Constants.s_Cn_0X00010004);
                Checks.Remove(Constants.s_Cn_0X00010005);
                Checks.Remove(Constants.s_Cn_0X00010006);
                Checks.Remove(Constants.s_Cn_0X00010008);
                Checks.Remove(Constants.s_Cn_0X00010023);
                Checks.Remove(Constants.s_Cn_0X00010031);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        // Collects all input DataItems in a node list
        protected virtual IList<XElement> GetInputDataItems(XElement submodItem)
        {
            if (submodItem == null)
                return null;

            IList<XElement> dataItems = null;

            var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
            if (ioData != null)
            {
                var input = ioData.Element(NamespaceGsdDef + Elements.s_Input);
                if (input != null)
                {
                    // Input is available only once
                    dataItems = input.Elements(NamespaceGsdDef + Elements.s_DataItem).ToList();
                }
            }
            return dataItems;
        }


        // Collects all output DataItems in a node list
        protected virtual IList<XElement> GetOutputDataItems(XElement submodItem)
        {
            if (submodItem == null)
                return null;

            IList<XElement> dataItems = null;

            var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
            if (ioData != null)
            {
                var output = ioData.Element(NamespaceGsdDef + Elements.s_Output);
                if (output != null)
                {
                    // Input is available only once
                    dataItems = output.Elements(NamespaceGsdDef + Elements.s_DataItem).ToList();
                }
            }
            return dataItems;
        }


        // Some checks for UseAsBits in dependency to DataType for PROFIsafe submodules:
        // For DataType equal UnsignedX UseAsBits must be true.
        // For DataType not equal UnsignedX UseAsBits must be false or not existing.
        private void TestUseAsBitsProfIsafe(IList<XElement> dataItems)
        {
            if (dataItems == null)
                return;

            bool warningRaised = false;

            foreach (var dataItem in dataItems)
            {
                bool valueUseAsBits = false;
                string useAsBitsStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                if (!string.IsNullOrEmpty(useAsBitsStr))
                    valueUseAsBits = XmlConvert.ToBoolean(useAsBitsStr);
                string valueDataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                var lineInfo = (IXmlLineInfo)dataItem;

                if (valueUseAsBits && (valueDataType != Enums.s_Unsigned8 && valueDataType != Enums.s_Unsigned16 && valueDataType != Enums.s_Unsigned32))
                {
                    CreateReport0x00010122_1(dataItem, lineInfo);

                }
                else if (!valueUseAsBits && (valueDataType == Enums.s_Unsigned8 || valueDataType == Enums.s_Unsigned16 || valueDataType == Enums.s_Unsigned32))
                {
                    CreateReport0x00010122_2(dataItem, lineInfo);

                }
                else if (valueUseAsBits && (valueDataType == Enums.s_Unsigned16 || valueDataType == Enums.s_Unsigned32))
                {
                    warningRaised = CreateReport0x00010122_6(dataItem, warningRaised, lineInfo);

                }
            }

        }

        private bool CreateReport0x00010122_6(XNode dataItem, bool warningRaised, IXmlLineInfo lineInfo)
        {
            var bitDataItems = dataItem.XPathSelectElements("./gsddef:BitDataItem", Nsmgr).ToList();
            if (bitDataItems.Count <= 0)
            {
                return warningRaised;
            }
            if (warningRaised || !Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
            {
                return warningRaised;
            }
            // "For packing boolean channels, it is strongly advised to use only DataItems with DataType="Unsigned8".
            // This warning points to the first occurrence, but it might be other ones under this PROFIsafe submodule."
            string msg = Help.GetMessageString("M_0x00010122_6");
            string xpath = Help.GetXPath(dataItem);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010122_6");
            return true;

        }

        private void CreateReport0x00010122_2(XObject dataItem, IXmlLineInfo lineInfo)
        {
            if (!Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules: The DataType "Unsigned8", "Unsigned16" or "Unsigned32" requires 'UseAsBits' = "true"."
            string msg = Help.GetMessageString("M_0x00010122_2");
            string xpath = Help.GetXPath(dataItem);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010122_2");

        }

        private void CreateReport0x00010122_1(XObject dataItem, IXmlLineInfo lineInfo)
        {
            if (!Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules: For a 'DataType' other than "Unsigned8", "Unsigned16" or "Unsigned32" 'UseAsBits' shall not be set."
            string msg = Help.GetMessageString("M_0x00010122_1");
            string xpath = Help.GetXPath(dataItem);
            Store.CreateAndAnnounceReport(
                ReportType_0X000101221,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010122_1");

        }


        // Check if Input and Output is given for each (Virtual)SubmoduleItem/IOData
        private void CheckInputOutputIsGiven(XContainer submodItem)
        {
            if (submodItem == null)
                return;

            var lineInfo = (IXmlLineInfo)submodItem;

            var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
            if (ioData != null)
            {
                // Input available?
                var input = ioData.Element(NamespaceGsdDef + Elements.s_Input);
                if (input == null)
                {
                    if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                    {
                        // "For F-Submodules 'IOData/Input' must be available."
                        string msg = Help.GetMessageString("M_0x00010122_3");
                        string xpath = Help.GetXPath(submodItem);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00010122_3");
                    }
                }

                // Output available?
                var output = ioData.Element(NamespaceGsdDef + Elements.s_Output);
                if (output == null)
                {
                    if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                    {
                        // "For F-Submodules 'IOData/Output' must be available."
                        string msg = Help.GetMessageString("M_0x00010122_4");
                        string xpath = Help.GetXPath(submodItem);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00010122_4");
                    }
                }
            }
        }


        // Here it is checked if F_MessageTrailerXByte is correctly placed.
        // F_MessageTrailerXByte must be given as DataType for exactly the last DataItem
        // and must be the same for Input and Output.
        private void TestMessageTrailerXByteCorrectlyPlaced(XObject submodItem, IList<XElement> dataItems)
        {
            if (submodItem == null)
                return;

            var lineInfo = (IXmlLineInfo)submodItem;

            // Check if at least one DataItem is available
            if (dataItems == null || dataItems.Count == 0)
            {
                if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                {
                    // "For F-Submodules 'IOData' at least one 'DataItem' with 'DataType' equal "F_MessageTrailer4Byte"
                    // or "F_MessageTrailer5Byte" must be available for 'Input' and 'Output'."
                    string msg = Help.GetMessageString("M_0x00010122_5");
                    string xpath = Help.GetXPath(submodItem);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00010122_5");
                }
                return;
            }

            string messageTrailer = GetMessageTrailerXByte(dataItems);

            // F_MessageTrailerXByte must be defined for Input and Output
            if (String.IsNullOrEmpty(messageTrailer))
            {
                if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                {
                    // "For F-Submodules in 'IOData' a message trailer ("F_MessageTrailer4Byte"
                    // or "F_MessageTrailer5Byte") must be defined for 'Input' and 'Output'."
                    string msg = Help.GetMessageString("M_0x00010122_8");
                    string xpath = Help.GetXPath(submodItem);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00010122_8");
                }
            }
        }


        // Gets the F_MessageTrailerXByte definition
        private string GetMessageTrailerXByte(IList<XElement> dataItems)
        {
            if (dataItems == null || dataItems.Count == 0)
                return string.Empty; // Should not be, but for safety reasons

            // The definition for F_MessageTrailerXByte must be in the last element
            var dataItem = dataItems[dataItems.Count - 1];
            string messageTrailer = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
            if (messageTrailer != Enums.s_FMessageTrailer4Byte && messageTrailer != FMessageTrailer5Byte)
                messageTrailer = string.Empty; // Should not be, but the error message will be generated at another place

            return messageTrailer;

        }


        // Here the usage of F_MessageTrailer4Byte and F_MessageTrailer5Byte is checked.
        protected virtual void TestMessageTrailerXByteIsSufficient(XElement submodItem, IList<XElement> inputDataItems, IList<XElement> outputDataItems, int ioDataChannelSize)
        {
            if (inputDataItems == null || inputDataItems.Count <= 0)
                return; // Should not be, but the error message will be generated at another place

            // Get F_MessageTrailer4Byte or F_MessageTrailer5Byte.
            // It must be given always in the last DataItem and
            // must be the same for input and output.
            var inDataItem = inputDataItems[inputDataItems.Count - 1];
            string inMessageTrailerXByte = Help.GetAttributeValueFromXElement(inDataItem, Attributes.s_DataType);
            var outDataItem = outputDataItems[outputDataItems.Count - 1];
            string outMessageTrailerXByte = Help.GetAttributeValueFromXElement(outDataItem, Attributes.s_DataType);

            if (inMessageTrailerXByte != Enums.s_FMessageTrailer4Byte && inMessageTrailerXByte != FMessageTrailer5Byte)
                return; // Error, will be handled at another place

            if (outMessageTrailerXByte != Enums.s_FMessageTrailer4Byte && outMessageTrailerXByte != FMessageTrailer5Byte)
                return; // Error, will be handled at another place

            if (outMessageTrailerXByte != inMessageTrailerXByte)
            {
                // "The same message trailer must be used for 'IOData/Input' and 'IOData/Output'."
                string msg = Help.GetMessageString("M_0x00010122_9");
                string xpath = Help.GetXPath(submodItem);
                IXmlLineInfo xli = submodItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00010122_9");
            }
        }


        // Get the sum of all channel (DataItem) sizes separately for input and output
        // and return the value of the greater one
        protected virtual int GetIODataChannelSize(XElement submodItem)
        {
            if (submodItem == null)
                return 0;

            var inputDataItems = GetInputDataItems(submodItem);
            int inputIODataChannelSize = GetDataItemsChannelSize(inputDataItems);

            var outputDataItems = GetOutputDataItems(submodItem);
            int outputIODataChannelSize = GetDataItemsChannelSize(outputDataItems);

            if (inputIODataChannelSize > outputIODataChannelSize)
                return inputIODataChannelSize;

            return outputIODataChannelSize;
        }


        // Get the sum of all channel sizes
        protected virtual int GetDataItemsChannelSize(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                string strDataTypeLen = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Length);

                int dataTypeLen = GetDataTypeChannelSizeWithOutMsgTrailerByte(dataType);

                if (!String.IsNullOrEmpty(strDataTypeLen))
                {
                    if (dataTypeLen != XmlConvert.ToInt32(strDataTypeLen) &&
                        (dataType != Enums.s_VisibleString && dataType != Enums.s_OctetString &&
                        dataType != Enums.s_UnicodeString8 && dataType != Enums.s_String61131 &&
                        dataType != Enums.s_Wstring61131 && dataType != Enums.s_OctetStringS &&
                        dataType != Enums.s_FMessageTrailer4Byte && dataType != FMessageTrailer5Byte))
                    {
                        // "The given 'Length' = {0} doesn't match with 'DataType' = "{1}"."
                        if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                        {
                            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010122_b"), strDataTypeLen, dataType);
                            string xpath = Help.GetXPath(dataItem);
                            IXmlLineInfo xli = dataItem;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010122_b");
                        }
                    }
                }

                allDataItemsLen += dataTypeLen;
            }

            return allDataItemsLen;
        }

        // Get the channel size for one specific DataType
        protected virtual int GetDataTypeChannelSizeWithOutMsgTrailerByte(string dataType)
        {
            int dataTypeLen = 0;

            if (String.IsNullOrEmpty(dataType))
                return dataTypeLen;

            if (dataType == Enums.s_Integer8
                || dataType == Enums.s_Unsigned8
                || dataType == Enums.s_Boolean
                || dataType == Enums.s_Unsigned8S)
            {
                dataTypeLen = 1;
            }
            else if (dataType == Enums.s_Integer16
                     || dataType == Enums.s_Unsigned16
                     || dataType == Enums.s_Unsigned8Unsigned8
                     || dataType == Enums.s_Unsigned16S
                     || dataType == Enums.s_Integer16S
                     || dataType == Enums.s_N2
                     || dataType == Enums.s_V2
                     || dataType == Enums.s_L2
                     || dataType == Enums.s_R2
                     || dataType == Enums.s_T2
                     || dataType == Enums.s_D2
                     || dataType == Enums.s_E2
                     || dataType == Enums.s_X2
                     || dataType == Enums.s_Unipolar216)
            {
                dataTypeLen = 2;
            }
            else if (dataType == Enums.s_OctetString2Unsigned8)
            {
                dataTypeLen = 3;
            }
            else if (dataType == Enums.s_Integer32
                     || dataType == Enums.s_Unsigned32
                     || dataType == Enums.s_Float32
                     || dataType == Enums.s_TimeDifferenceWithoutDateIndication
                     || dataType == Enums.s_TimeOfDayWithoutDateIndication
                     || dataType == Enums.s_N4
                     || dataType == Enums.s_T4
                     || dataType == Enums.s_C4
                     || dataType == Enums.s_X4)
            {
                dataTypeLen = 4;
            }
            else if (dataType == Enums.s_Float32Unsigned8
                     || dataType == Enums.s_Float32Status8)
            {
                dataTypeLen = 5;
            }
            else if (dataType == Enums.s_TimeDifferenceWithDateIndication
                     || dataType == Enums.s_TimeOfDayWithDateIndication)
            {
                dataTypeLen = 6;
            }
            else if (dataType == Enums.s_Date)
            {
                dataTypeLen = 7;
            }
            else if (dataType == Enums.s_Integer64
                     || dataType == Enums.s_Unsigned64
                     || dataType == Enums.s_Float64
                     || dataType == Enums.s_NetworkTime
                     || dataType == Enums.s_NetworkTimeDifference
                     || dataType == Enums.s_TimeStampDifferenceShort)
            {
                dataTypeLen = 8;
            }
            else if (dataType == Enums.s_TimeStamp
                     || dataType == Enums.s_TimeStampDifference)
            {
                dataTypeLen = 12;
            }

            return dataTypeLen;
        }

        // Get the channel size for one specific DataType
        protected virtual int GetDataTypeChannelSize(string dataType)
        {
            int dataTypeLen = GetDataTypeChannelSizeWithOutMsgTrailerByte(dataType);

            if (dataType == Enums.s_FMessageTrailer4Byte)
            {
                dataTypeLen = 4;
            }
            else if (dataType == FMessageTrailer5Byte)
            {
                dataTypeLen = 5;
            }

            return dataTypeLen;
        }

        // Get the length in octets of the whole DataItems section
        protected virtual int GetAddressRange(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                int dataTypeLen = GetDataTypeChannelSize(dataType);
                allDataItemsLen += dataTypeLen;
            }

            return allDataItemsLen;
        }

        // Get the sum of all composite channel sizes
        protected virtual int GetBytesComposite(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                if (dataType == Enums.s_Float32Unsigned8 || dataType == Enums.s_Float32Status8)
                {
                    int dataTypeLen = GetDataTypeChannelSize(dataType);
                    allDataItemsLen += dataTypeLen;
                }
            }

            return allDataItemsLen;
        }

        // Get the sum of all Unsigned8+Unsigned8
        protected virtual int GetBytesU8U8(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                if (dataType == Enums.s_Unsigned8Unsigned8)
                {
                    int dataTypeLen = GetDataTypeChannelSize(dataType);
                    allDataItemsLen += dataTypeLen;
                }
            }

            return allDataItemsLen;
        }

        /// <summary>
        /// Get the sum of all Unsigned channel sizes in bits
        /// </summary>
        /// <param name="dataItems"></param>
        /// <returns></returns>
        protected virtual int GetChannelsBoolMax(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;
            List<XElement> dataItemsUnsignedXx = GetDataItemsUnsignedXx(dataItems);


            int noOfItem = 0;
            foreach (XElement dataItem in dataItemsUnsignedXx)
            {
                noOfItem++;

                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                int dataTypeLen = GetDataTypeChannelSize(dataType);

                // If the data item is not the last one, always sum up 8 channels per byte
                if (noOfItem < dataItemsUnsignedXx.Count)
                {
                    allDataItemsLen += dataTypeLen * 8;
                    continue;
                }

                var bitDataItems = dataItem.Elements(NamespaceGsdDef + Elements.s_BitDataItem).ToList();
                List<UInt16> bitOffsets = new();
                foreach (var bitDataItem in bitDataItems)
                    bitOffsets.Add(XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(bitDataItem, Attributes.s_BitOffset)));

                // Also for the last data item always sum up 8 channels per byte except for the last byte
                if (dataTypeLen == 2)
                {
                    allDataItemsLen += 8;
                }
                else if (dataTypeLen == 4)
                {
                    allDataItemsLen += 24;
                }

                // For the last byte the most significant used bits count

                // If no bit data items given, count the whole byte
                if (bitDataItems.Count == 0)
                {
                    allDataItemsLen += 8;
                    continue;
                }

                // Count the used bits
                allDataItemsLen = CountUsedBits(bitOffsets, allDataItemsLen);

            }

            return allDataItemsLen;
        }

        private static int CountUsedBits(ICollection<ushort> bitOffsets, int allDataItemsLen)
        {
            UInt16 j = 7;
            for (; j >= 0; j--)
            {
                if (bitOffsets.Contains(j))
                    break;
                if (j == 0)
                    break;
            }
            if (j != 0 || bitOffsets.Contains(j))
            {
                allDataItemsLen += j + 1;
            }
            return allDataItemsLen;
        }

        private static List<XElement> GetDataItemsUnsignedXx(IList<XElement> dataItems)
        {
            List<XElement> dataItemsUnsignedXx = new();

            // Only Usigned8, Unsigned16, Unsigned32 can be used as boolean channels.
            // Save the relevant data items to a list
            foreach (var dataItem in dataItems)
            {
                // Get the data type of item
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // Get if used as bits
                string strUseAsBits = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                bool useAsBits = false;
                if (!string.IsNullOrEmpty(strUseAsBits))
                    useAsBits = XmlConvert.ToBoolean(strUseAsBits);

                // Save items with DataType "UnsignedXX"
                if ((dataType == Enums.s_Unsigned8 || dataType == Enums.s_Unsigned16 || dataType == Enums.s_Unsigned32
                     || dataType == Enums.s_Integer8 || dataType == Enums.s_Integer16 || dataType == Enums.s_Integer32)
                    && useAsBits)
                    dataItemsUnsignedXx.Add(dataItem);
            }
            return dataItemsUnsignedXx;
        }

        /// <summary>
        /// Get the sum of all Unsigned channel sizes in bytes
        /// </summary>
        /// <param name="dataItems"></param>
        /// <returns></returns>
        protected virtual int GetBytesBoolMax(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // Get if used as bits
                string strUseAsBits = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                bool useAsBits = false;
                if (!string.IsNullOrEmpty(strUseAsBits))
                    useAsBits = XmlConvert.ToBoolean(strUseAsBits);

                if ((dataType == Enums.s_Unsigned8 || dataType == Enums.s_Unsigned16 || dataType == Enums.s_Unsigned32 ||
                     dataType == Enums.s_Integer8 || dataType == Enums.s_Integer16 || dataType == Enums.s_Integer32) &&
                    useAsBits)
                {
                    int dataTypeLen = GetDataTypeChannelSize(dataType);
                    allDataItemsLen += dataTypeLen;
                }
            }

            return allDataItemsLen;
        }

        /// <summary>
        /// Calculates the sum of channels of a list of DataItems with a specific data type.
        /// </summary>
        /// <param name="dataItems"></param>
        /// <param name="dataType"></param>
        /// <returns>sum of channels</returns>
        protected virtual int GetChannelsAnalog(IList<XElement> dataItems, string dataType)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string dt = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // Get if used as bits
                string strUseAsBits = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                bool useAsBits = false;
                if (!string.IsNullOrEmpty(strUseAsBits))
                    useAsBits = XmlConvert.ToBoolean(strUseAsBits);

                if (dt == dataType && !useAsBits)
                {
                    allDataItemsLen++;
                }
            }

            return allDataItemsLen;
        }

        protected virtual bool FindAllowedValues(XElement elem, out List<ValueListHelper.ValueRangeT> allowedValues)
        {
            bool valid = false;

            XAttribute allowedValuesNode = elem.Attribute(Attributes.s_AllowedValues);
            allowedValues = ValueListHelper.NormalizeValueList(allowedValuesNode, Store);

            if (allowedValues != null && allowedValues.Count != 0)
            {
                valid = true;
            }

            return valid;
        }

        protected virtual void FParamDescCrcForFCheckIPar(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Check_iPar, if visible
            var checkiPar = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCheckIPar);
            if (checkiPar == null)
            {
                return;
            }
            string strVisible = Help.GetAttributeValueFromXElement(checkiPar, Attributes.s_Visible);
            bool bVisible = false; // Default
            if (!string.IsNullOrEmpty(strVisible))
                bVisible = XmlConvert.ToBoolean(strVisible);
            if (!bVisible)
            {
                return;
            }
            // Default value
            UInt16 dFCheckiPar = Enums.s_NoCheckValue; // Default: "NoCheck"
            string strDFCheckiPar = Help.GetAttributeValueFromXElement(checkiPar, Attributes.s_DefaultValue);
            if (strDFCheckiPar == Enums.s_Check)
                dFCheckiPar = Enums.s_CheckValue;

            byte[] data = new byte[]
                              {
                                          (byte)'F', (byte)'_', (byte)'C', (byte)'h', (byte)'e', (byte)'c', (byte)'k',
                                          (byte)'_', (byte)'i', (byte)'P', (byte)'a', (byte)'r',
                                          0x00, 0x01, // Type=BitArea, Offset=1
                                          (byte)(dFCheckiPar), (byte)(dFCheckiPar >> 8) // Default value
                              };

            // Add the byte array to the CRC
            calcFParamDescCrc.UpdateChecksum(data);

            // Allowed values
            string strAfCheckiPar = Help.GetAttributeValueFromXElement(checkiPar, Attributes.s_AllowedValues);
            if (String.IsNullOrEmpty(strAfCheckiPar))
                strAfCheckiPar = "Check NoCheck";

            if (!strAfCheckiPar.Contains(Constants.s_Space))
            {
                UInt16 aFCheckiPar = Enums.s_NoCheckValue;
                if (strAfCheckiPar == Enums.s_Check)
                    aFCheckiPar = Enums.s_CheckValue;
                byte[] data1 = new byte[]
                                   {
                                               (byte)(aFCheckiPar), (byte)(aFCheckiPar >> 8), // Min value
                                               (byte)(aFCheckiPar), (byte)(aFCheckiPar >> 8) // Max value
                                   };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data1);
            }
            else
            {
                if (strAfCheckiPar.Contains(Enums.s_NoCheck))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'N', (byte)'o', (byte)'C', (byte)'h', (byte)'e', (byte)'c', (byte)'k',
                                                   Enums.s_NoCheckValue, Enums.s_NoCheckValue >> 8 // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (!strAfCheckiPar.Contains(Enums.s_Check))
                {
                    return;

                }
                {
                    byte[] data1 = new byte[]
                                               {
                                                   (byte)'C', (byte)'h', (byte)'e', (byte)'c', (byte)'k',
                                                   Enums.s_CheckValue, Enums.s_CheckValue >> 8 // Value
                                               };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }
            }


        }

        protected virtual void FParamDescCrcForFSil(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_SIL, if visible
            var fsil = fparamRecord.Element(NamespaceGsdDef + Elements.s_FSil);
            byte[] data;
            if (fsil == null)
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                data = new byte[]
                           {
                               (byte)'F', (byte)'_', (byte)'S', (byte)'I', (byte)'L', 0x00,
                               0x02, // Type=BitArea, Offset=2
                               0x02, 0x00, // Default value
                               (byte)'S', (byte)'I', (byte)'L', (byte)'1', 0x00, 0x00, // Enumeration
                               (byte)'S', (byte)'I', (byte)'L', (byte)'2', 0x01, 0x00, (byte)'S', (byte)'I',
                               (byte)'L', (byte)'3', 0x02, 0x00, (byte)'N', (byte)'o', (byte)'S', (byte)'I',
                               (byte)'L', 0x03, 0x00,
                           };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);

                return;
            }
            string strVisible = Help.GetAttributeValueFromXElement(fsil, Attributes.s_Visible);
            bool bVisible = true; // Default
            if (!string.IsNullOrEmpty(strVisible))
                bVisible = XmlConvert.ToBoolean(strVisible);
            if (!bVisible)
            {
                return;
            }

            // Default value
            ushort dFsil = FParamDescCRCForFSilDefaultValue(fsil);

            data = new byte[]
                                  {
                                          (byte)'F', (byte)'_', (byte)'S', (byte)'I', (byte)'L', 0x00,
                                          0x02, // Type=BitArea, Offset=2
                                          (byte)(dFsil), (byte)(dFsil >> 8) // Default value
                                  };

            // Add the byte array to the CRC
            calcFParamDescCrc.UpdateChecksum(data);

            // Allowed values
            var aAFSIL = fsil.Attribute(Attributes.s_AllowedValues);
            string strAfsil = aAFSIL != null ? aAFSIL.Value : "SIL1 SIL2 SIL3 NoSIL";


            if (!strAfsil.Contains(Constants.s_Space))
            {
                ushort aFsil = FParamDescCRCForFSilAllowedValues(strAfsil);


                byte[] data1 = new byte[]
                                               {
                                               (byte)(aFsil), (byte)(aFsil >> 8), // Min value
                                               (byte)(aFsil), (byte)(aFsil >> 8) // Max value
                                               };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data1);
            }
            else
            {
                if (strAfsil.Contains(Enums.s_Sil1))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'S', (byte)'I', (byte)'L', (byte)'1', (byte)(Enums.s_Sil1Value),
                                                   Enums.s_Sil1Value >> 8 // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (strAfsil.Contains(Enums.s_Sil2))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'S', (byte)'I', (byte)'L', (byte)'2', (byte)(Enums.s_Sil2Value),
                                                   Enums.s_Sil2Value >> 8 // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (strAfsil.Contains(Enums.s_Sil3))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'S', (byte)'I', (byte)'L', (byte)'3', (byte)(Enums.s_Sil3Value),
                                                   (Enums.s_Sil3Value >> 8) // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (!strAfsil.Contains(Enums.s_NoSil))
                {
                    return;
                }


                {

                    byte[] data1 = new byte[]
                                          {
                                           (byte)'N', (byte)'o', (byte)'S', (byte)'I', (byte)'L',
                                           (byte)(Enums.s_NoSilValue), (Enums.s_NoSilValue >> 8) // Value
                                          };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }
            }
        }

        private static ushort FParamDescCRCForFSilAllowedValues(string strAfsil)
        {
            UInt16 aFsil = Enums.s_Sil3Value;
            if (strAfsil == Enums.s_Sil1)
                aFsil = Enums.s_Sil1Value;
            if (strAfsil == Enums.s_Sil2)
                aFsil = Enums.s_Sil2Value;
            if (strAfsil == Enums.s_NoSil)
                aFsil = Enums.s_NoSilValue;
            return aFsil;
        }

        private static ushort FParamDescCRCForFSilDefaultValue(XElement fsil)
        {
            UInt16 dFsil = Enums.s_Sil3Value; // Default: "SIL3"
            string strDfsil = Help.GetAttributeValueFromXElement(fsil, Attributes.s_DefaultValue);
            if (strDfsil == Enums.s_Sil1)
                dFsil = Enums.s_Sil1Value;
            if (strDfsil == Enums.s_Sil2)
                dFsil = Enums.s_Sil2Value;
            if (strDfsil == Enums.s_NoSil)
                dFsil = Enums.s_NoSilValue;
            return dFsil;
        }

        protected virtual void FParamDescCrcForFCrcLength(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_CRC_Length, if visible
            var fcrcLength = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCrcLength);
            if (fcrcLength == null)
            {
                return;
            }
            string strVisible = Help.GetAttributeValueFromXElement(fcrcLength, Attributes.s_Visible);
            bool bVisible = false; // Default
            if (!string.IsNullOrEmpty(strVisible))
                bVisible = XmlConvert.ToBoolean(strVisible);
            if (!bVisible)
            {
                return;
            }
            // Default value
            UInt16 dFcrcLength = Enums.s_ByteCrc3Value; // Default: "3-Byte-CRC"
            string strDfcrcLength = Help.GetAttributeValueFromXElement(fcrcLength, Attributes.s_DefaultValue);
            if (strDfcrcLength == Enums.s_ByteCrc2)
                dFcrcLength = Enums.s_ByteCrc2Value;
            if (strDfcrcLength == Enums.s_ByteCrc4)
                dFcrcLength = Enums.s_ByteCrc4Value;

            byte[] data = new byte[]
                              {
                                          (byte)'F', (byte)'_', (byte)'C', (byte)'R', (byte)'C', (byte)'_',
                                          (byte)'L', (byte)'e', (byte)'n', (byte)'g', (byte)'t', (byte)'h',
                                          0x00, 0x04, // Type=BitArea, Offset=4
                                          (byte)(dFcrcLength), (byte)(dFcrcLength >> 8) // Default value
                              };

            // Add the byte array to the CRC
            calcFParamDescCrc.UpdateChecksum(data);

            // Allowed values
            var aAFCRCLength = fcrcLength.Attribute(Attributes.s_AllowedValues);
            string strAfcrcLength = null;
            if (aAFCRCLength != null)
                strAfcrcLength = aAFCRCLength.Value;
            else
                strAfcrcLength = "3-Byte-CRC"; // Default: "3-Byte-CRC"

            if (!strAfcrcLength.Contains(Constants.s_Space))
            {
                UInt16 aFcrcLength = Enums.s_ByteCrc3Value;
                if (strAfcrcLength == Enums.s_ByteCrc2)
                    aFcrcLength = Enums.s_ByteCrc2Value;
                if (strAfcrcLength == Enums.s_ByteCrc4)
                    aFcrcLength = Enums.s_ByteCrc4Value;

                byte[] data1 = new byte[]
                                   {
                                               (byte)(aFcrcLength), (byte)(aFcrcLength >> 8), // Min value
                                               (byte)(aFcrcLength), (byte)(aFcrcLength >> 8) // Max value
                                   };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data1);
            }
            else
            {
                if (strAfcrcLength.Contains(Enums.s_ByteCrc3))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'3', (byte)'-', (byte)'B', (byte)'y', (byte)'t', (byte)'e',
                                                   (byte)'-', (byte)'C', (byte)'R', (byte)'C',
                                                   (byte)(Enums.s_ByteCrc3Value), (Enums.s_ByteCrc3Value >> 8) // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (strAfcrcLength.Contains(Enums.s_ByteCrc2))
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'2', (byte)'-', (byte)'B', (byte)'y', (byte)'t', (byte)'e',
                                                   (byte)'-', (byte)'C', (byte)'R', (byte)'C',
                                                   (byte)(Enums.s_ByteCrc2Value), (Enums.s_ByteCrc2Value >> 8) // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }

                if (!strAfcrcLength.Contains(Enums.s_ByteCrc4))
                {
                    return;
                }
                {
                    byte[] data1 = new byte[]
                                       {
                                                   (byte)'4', (byte)'-', (byte)'B', (byte)'y', (byte)'t', (byte)'e',
                                                   (byte)'-', (byte)'C', (byte)'R', (byte)'C',
                                                   (byte)(Enums.s_ByteCrc4Value), (Enums.s_ByteCrc4Value >> 8) // Value
                                       };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }
            }
        }

        protected virtual void FParamDescCrcForFBlockID(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Block_ID, if visible
            var fblockIDs = fparamRecord.Element(NamespaceGsdDef + Elements.s_FBlockID);
            if (fblockIDs != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fblockIDs, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFBlockID = 0; // Default: "0"
                    string strDFBlockID = Help.GetAttributeValueFromXElement(fblockIDs, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strDFBlockID))
                        dFBlockID = (UInt16)XmlConvert.ToInt16(strDFBlockID); // This (double)cast is important to convert -0

                    // Allowed values
                    uint allowedValuesFrom = 0;
                    uint allowedValuesTo = 7;
                    bool valid = FindAllowedValues(fblockIDs, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'B', (byte)'l', (byte)'o', (byte)'c',
                                          (byte)'k', (byte)'_', (byte)'I', (byte)'D',
                                          0x00, 0x03, // Type=BitArea, Offset=3
                                          (byte)(dFBlockID), (byte)(dFBlockID >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Default:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'B', (byte)'l', (byte)'o', (byte)'c',
                                      (byte)'k', (byte)'_', (byte)'I', (byte)'D',
                                      0x00, 0x03, // Type=BitArea, Offset=3
                                      0x00, 0x00, // Default value
                                      0x00, 0x00, // Min value
                                      0x07, 0x00 // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void FParamDescCrcForFParVersion(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Par_Version, if visible
            var fparVersions = fparamRecord.Element(NamespaceGsdDef + Elements.s_FParVersion);
            if (fparVersions != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fparVersions, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFParVersion = 1; // Default: "1" (V2-mode)
                    string strFParVersion = Help.GetAttributeValueFromXElement(fparVersions, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFParVersion))
                        dFParVersion = (UInt16)XmlConvert.ToInt16(strFParVersion);

                    // Allowed values
                    uint allowedValuesFrom = 1;
                    uint allowedValuesTo = 1;
                    bool valid = FindAllowedValues(fparVersions, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'P', (byte)'a', (byte)'r', (byte)'_',
                                          (byte)'V', (byte)'e', (byte)'r', (byte)'s', (byte)'i', (byte)'o', (byte)'n',
                                          0x00, 0x06, // Type=BitArea, Offset=6
                                          (byte)(dFParVersion), (byte)(dFParVersion >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'P', (byte)'a', (byte)'r', (byte)'_',
                                      (byte)'V', (byte)'e', (byte)'r', (byte)'s', (byte)'i', (byte)'o', (byte)'n',
                                      0x00, 0x06, // Type=BitArea, Offset=6
                                      0x01, 0x00, // Default value
                                      0x01, 0x00, // Min value
                                      0x01, 0x00 // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void FParamDescCrcForFSourceAdd(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Source_Add, if visible
            var fsourceAdd = fparamRecord.Element(NamespaceGsdDef + Elements.s_FSourceAdd);
            if (fsourceAdd != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fsourceAdd, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFSourceAdd = 1; // Default: "1"
                    string strFSourceAdd = Help.GetAttributeValueFromXElement(fsourceAdd, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFSourceAdd))
                        dFSourceAdd = XmlConvert.ToUInt16(strFSourceAdd);

                    // Allowed values
                    uint allowedValuesFrom = 1;
                    uint allowedValuesTo = 65534;
                    bool valid = FindAllowedValues(fsourceAdd, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'S', (byte)'o', (byte)'u', (byte)'r',
                                          (byte)'c', (byte)'e', (byte)'_', (byte)'A', (byte)'d', (byte)'d',
                                          0x02, 0x00, // Type=Unsigned16
                                          (byte)(dFSourceAdd), (byte)(dFSourceAdd >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'S', (byte)'o', (byte)'u', (byte)'r',
                                      (byte)'c', (byte)'e', (byte)'_', (byte)'A', (byte)'d', (byte)'d',
                                      0x02, 0x00, // Type=Unsigned16
                                      0x01, 0x00, // Default value
                                      0x01, 0x00, // Min value
                                      0xfe, 0xff // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void FParamDescCrcForFDestAdd(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Dest_Add, if visible
            var fdestAdds = fparamRecord.Element(NamespaceGsdDef + Elements.s_FDestAdd);
            if (fdestAdds != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fdestAdds, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFDestAdd = 1; // Default: "1"
                    string strFDestAdd = Help.GetAttributeValueFromXElement(fdestAdds, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFDestAdd))
                        dFDestAdd = XmlConvert.ToUInt16(strFDestAdd);

                    // Allowed values
                    uint allowedValuesFrom = 1;
                    uint allowedValuesTo = 65534;
                    bool valid = FindAllowedValues(fdestAdds, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'D', (byte)'e', (byte)'s', (byte)'t',
                                          (byte)'_', (byte)'A', (byte)'d', (byte)'d',
                                          0x02, 0x00, // Type=Unsigned16
                                          (byte)(dFDestAdd), (byte)(dFDestAdd >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'D', (byte)'e', (byte)'s', (byte)'t',
                                      (byte)'_', (byte)'A', (byte)'d', (byte)'d',
                                      0x02, 0x00, // Type=Unsigned16
                                      0x01, 0x00, // Default value
                                      0x01, 0x00, // Min value
                                      0xfe, 0xff // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void FParamDescCrcForFWdTime(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_WD_Time, if visible
            var fwdTime = fparamRecord.Element(NamespaceGsdDef + Elements.s_FWdTime);
            if (fwdTime != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fwdTime, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFwdTime = 150; // Default: "150" (150 ms)
                    string strFwdTime = Help.GetAttributeValueFromXElement(fwdTime, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFwdTime))
                        dFwdTime = XmlConvert.ToUInt16(strFwdTime);

                    // Allowed values
                    uint allowedValuesFrom = 1;
                    uint allowedValuesTo = 65535;
                    bool valid = FindAllowedValues(fwdTime, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'W', (byte)'D', (byte)'_',
                                          (byte)'T', (byte)'i', (byte)'m', (byte)'e',
                                          0x02, 0x00, // Type=Unsigned16
                                          (byte)(dFwdTime), (byte)(dFwdTime >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'W', (byte)'D', (byte)'_',
                                      (byte)'T', (byte)'i', (byte)'m', (byte)'e',
                                      0x02, 0x00, // Type=Unsigned16
                                      0x96, 0x00, // Default value
                                      0x01, 0x00, // Min value
                                      0xff, 0xff // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void FParamDescCrcForFIParCrc(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_iPar_CRC, if visible
            var fiParCrc = fparamRecord.Element(NamespaceGsdDef + Elements.s_FIParCrc);
            if (fiParCrc != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fiParCrc, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt32 dFiParCrc = 0; // Default: "0"
                    string strFiParCrc = Help.GetAttributeValueFromXElement(fiParCrc, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFiParCrc))
                        dFiParCrc = XmlConvert.ToUInt32(strFiParCrc);

                    // Allowed values
                    uint allowedValuesFrom = 0;
                    uint allowedValuesTo = 4294967295;
                    bool valid = FindAllowedValues(fiParCrc, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'i', (byte)'P', (byte)'a', (byte)'r',
                                          (byte)'_', (byte)'C', (byte)'R', (byte)'C',
                                          0x03, 0x00, // Type=Unsigned32
                                          (byte)(dFiParCrc), (byte)(dFiParCrc >> 8),
                                          (byte)(dFiParCrc >> 16), (byte)(dFiParCrc >> 24), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8),
                                          (byte)(allowedValuesFrom >> 16), (byte)(allowedValuesFrom >> 24), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8),
                                          (byte)(allowedValuesTo >> 16), (byte)(allowedValuesTo >> 24) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
        }

        protected virtual void FParamDescCrcForFParCrc(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Par_CRC, if visible
            var fParCrc = fparamRecord.Element(NamespaceGsdDef + Elements.s_FParCrc);
            if (fParCrc != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fParCrc, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFParCrc = 53356; // Default: "53356"
                    string strFParCrc = Help.GetAttributeValueFromXElement(fParCrc, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFParCrc))
                        dFParCrc = XmlConvert.ToUInt16(strFParCrc);

                    // Allowed values
                    uint allowedValuesFrom = 0;
                    uint allowedValuesTo = 65535;
                    bool valid = FindAllowedValues(fParCrc, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }

                    byte[] data = new byte[]
                                      {
                                          (byte)'F', (byte)'_', (byte)'P', (byte)'a', (byte)'r', (byte)'_',
                                          (byte)'C', (byte)'R', (byte)'C',
                                          0x02, 0x00, // Type=Unsigned16
                                          (byte)(dFParCrc), (byte)(dFParCrc >> 8), // Default value
                                          (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8), // Min value
                                          (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8) // Max value
                                      };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data);
                }
            }
            else
            {
                // Set the default values, because the F-Parameter is required and "Visible" is equal 'Fixed:"true"'
                byte[] data = new byte[]
                                  {
                                      (byte)'F', (byte)'_', (byte)'P', (byte)'a', (byte)'r', (byte)'_',
                                      (byte)'C', (byte)'R', (byte)'C',
                                      0x02, 0x00, // Type=Unsigned16
                                      0x6c, 0xd0, // Default value
                                      0x00, 0x00, // Min value
                                      0xff, 0xff // Max value
                                  };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);
            }
        }

        protected virtual void CollectBytesFParamDescCrc(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // F_Check_iPar
            FParamDescCrcForFCheckIPar(fparamRecord, calcFParamDescCrc);

            // F_SIL
            FParamDescCrcForFSil(fparamRecord, calcFParamDescCrc);

            // F_CRC_Length
            FParamDescCrcForFCrcLength(fparamRecord, calcFParamDescCrc);

            // F_Block_ID
            FParamDescCrcForFBlockID(fparamRecord, calcFParamDescCrc);

            // F_Par_Version
            FParamDescCrcForFParVersion(fparamRecord, calcFParamDescCrc);

            // F_Source_Add
            FParamDescCrcForFSourceAdd(fparamRecord, calcFParamDescCrc);

            // F_Dest_Add
            FParamDescCrcForFDestAdd(fparamRecord, calcFParamDescCrc);

            // F_WD_Time
            FParamDescCrcForFWdTime(fparamRecord, calcFParamDescCrc);

            // F_iPar_CRC
            FParamDescCrcForFIParCrc(fparamRecord, calcFParamDescCrc);

            // F_Par_CRC
            FParamDescCrcForFParCrc(fparamRecord, calcFParamDescCrc);
        }

        protected double GetPNioVersion(string pnioVersion)
        {
            double version = 1.0;
            Regex rxVersion = new Regex(@"^V[0-9]+\.[0-9]+$", RegexOptions.IgnoreCase);
            Match mVersion = rxVersion.Match(pnioVersion);
            if (!mVersion.Success)
            {
                return version;
            }

            pnioVersion = pnioVersion.ToUpperInvariant();
            int index = pnioVersion.IndexOf('V');
            if (index != -1)
            {
                // Calculate the version as double value
                string pv = pnioVersion.Substring(index + 1);
                version = XmlConvert.ToDouble(pv);
            }

            return version;
        }

        protected IList<string> GetRTClasses(XElement interfaceSubmoduleItem)
        {
            string supportedRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
            IList<string> rtClasses;
            if (!string.IsNullOrEmpty(supportedRTClasses))
            {
                rtClasses = new List<string>(supportedRTClasses.Split(Constants.s_Semicolon.ToCharArray()));
            }
            else
            {
                string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
                if (string.IsNullOrEmpty(supportedRTClass))
                {
                    supportedRTClass = "Class1"; // Default value
                }

                rtClasses = new List<string>();
                if (supportedRTClass == "Class1")
                {
                    rtClasses.Add("RT_CLASS_1");
                }

                if (supportedRTClass == "Class2")
                {
                    rtClasses.Add("RT_CLASS_1");
                    rtClasses.Add("RT_CLASS_2");
                }

                if (supportedRTClass == "Class3")
                {
                    rtClasses.Add("RT_CLASS_1");
                    rtClasses.Add("RT_CLASS_3");
                }
            }

            return rtClasses;
        }

        #endregion

        //########################################################################################

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV0200;
        }
        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV0200;
        }

        #endregion

        //########################################################################################

        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("ExtChannelDiagItem", "ErrorType");
            ElementDescriptions.Add("SubslotItem", "SubslotNumber");
            ElementDescriptions.Add("BitDataItem", "BitOffset");
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00010007
        /// The 'CategoryItem/@ID' must be unique over all 'CategoryItem' elements.
        /// The 'ModuleInfo/@SubCategory1Ref' attribute must reference an existing CategoryItem 'ID'.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010007()
        {
            // Keys -----
            var keys = new List<string>();
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CategoryItem).Attributes(Attributes.ID);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                keys.Add(Help.CollapseWhitespace(an.Value));
            }

            // KeyRefs -----
            nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleInfo).Attributes(Attributes.s_SubCategory1Ref);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                if (!keys.Contains(Help.CollapseWhitespace(an.Value)))
                {
                    // "The 'ModuleInfo/@SubCategory1Ref' attribute must reference an existing CategoryItem 'ID'."
                    string msg = Help.GetMessageString("M_0x00010007_3");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00010007_3");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010018
        /// For ChannelDiagList/ChannelDiagItem/ExtChannelDiagList/ExtChannelDiagItem is to check:
        /// The 'ErrorType' attribute must be between 1 and 32767 and
        /// unique for all 'ExtChannelDiagItem' entries under one ChannelDiagItem.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010018()
        {
            base.CheckCn_0X00010018();

            var lerrortypes = new List<uint>();

            // For ChannelDiagList/ChannelDiagItem/ExtChannelDiagList/ExtChannelDiagItem is to check:
            // (1) The 'ErrorType' attribute must be between 1 and 32767 and 
            // (2) unique for all 'ExtChannelDiagItem' entries under one ChannelDiagItem.

            var nl1 = GsdProfileBody.XPathSelectElements(".//gsddef:ChannelDiagList/gsddef:ChannelDiagItem", Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                var nl2 = (IEnumerable)en.XPathEvaluate("./gsddef:ExtChannelDiagList/gsddef:ExtChannelDiagItem/@ErrorType", Nsmgr);

                if (lerrortypes.Count > 0)
                {
                    lerrortypes.RemoveRange(0, lerrortypes.Count - 1);
                }

                foreach (XAttribute an in nl2)
                {
                    var xli = (IXmlLineInfo)an;

                    uint errortype = XmlConvert.ToUInt32(an.Value);

                    // (1)
                    if (errortype < 1 || errortype > 32767)
                    {
                        // "The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010018_1"), errortype, 1, 32767);
                        string xpath = Help.GetXPath(an);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010018_1");
                    }

                    // (2)
                    if (lerrortypes.Contains(errortype))
                    {
                        // "The 'ErrorType' ({0}) is a duplicate."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010018_2"), errortype);
                        string xpath = Help.GetXPath(an);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010018_2");
                    }
                    else
                        lerrortypes.Add(errortype);
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010102
        /// (1) Check VirtualSubmoduleItem/IsochroneMode/@IsochroneModeRequired 
        /// and InterfaceSubmoduleItem/@IsochroneModeSupported elements
        /// (2) Check VirtualSubmoduleItem/IsochroneMode and InterfaceSubmoduleItem/@IsochroneModeSupported elements
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010102()
        {
            // Find all ModuleItem IDs, which requires IsochroneMode.
            var moduleids = new List<string>();
            var isochroneModesOfModules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem)
                                          .Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleList)
                                          .Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem)
                                          .Elements(NamespaceGsdDef + Elements.s_IsochroneMode);
            isochroneModesOfModules = Help.TryRemoveXElementsUnderXsAny(isochroneModesOfModules, Nsmgr, Gsd);
            CheckCn_0X00010102_CollectModuleIds(isochroneModesOfModules, moduleids);



            // Find all DeviceAccessPointItem IDs, which requires IsochroneMode.
            var dapids = new List<string>();
            var isochroneModesOfDaps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                                          .Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleList)
                                          .Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem)
                                          .Elements(NamespaceGsdDef + Elements.s_IsochroneMode);
            isochroneModesOfDaps = Help.TryRemoveXElementsUnderXsAny(isochroneModesOfDaps, Nsmgr, Gsd);
            CheckCn_0X00010102_GetIsochroneModesOfDaps(isochroneModesOfDaps, dapids);


            // Find all ModuleItem IDs, which support (not require!) IsochroneMode.
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found Module IDs.
            var moduleSupportIds = new List<string>();
            XAttribute an;
            foreach (XElement en in nl)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && (!moduleSupportIds.Contains(Help.CollapseWhitespace(an.Value))) && (!moduleids.Contains(Help.CollapseWhitespace(an.Value))))
                    moduleSupportIds.Add(Help.CollapseWhitespace(an.Value));
            }

            // Find all DeviceAccessPointItem IDs, which have a submodule which support (not require!) IsochroneMode.
            nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found DAP IDs.
            var dapSupportIds = new List<string>();
            foreach (XElement en in nl)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && (!dapSupportIds.Contains(Help.CollapseWhitespace(an.Value))) && (!dapids.Contains(Help.CollapseWhitespace(an.Value))))
                {
                    dapSupportIds.Add(Help.CollapseWhitespace(an.Value));
                }
            }

            // If no module or DAP is found, which requires isochrone mode, further on test isn't necessary!
            if (dapids.Count <= 0 && moduleids.Count <= 0 && dapSupportIds.Count <= 0 && moduleSupportIds.Count <= 0)
            {
                return true; // ---------->
            }

            // Find all DeviceAccessPoints, which don't support IsochroneMode.
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                var interfaceSubmoduleItem = dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();
                bool isochroneModeSupported = false;
                string strIsochroneModeInRT_Classes = string.Empty;
                if (interfaceSubmoduleItem != null)
                {
                    string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
                    if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                        isochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                    strIsochroneModeInRT_Classes = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);
                }

                if (isochroneModeSupported || strIsochroneModeInRT_Classes != string.Empty)
                {
                    continue;
                }
                CheckCn_0X000101021(dap, dapids, moduleids);                 // (1)
                CheckCn_0X000101022(dap, dapSupportIds, moduleSupportIds);   // (2)
            }

            return true;
        }

        private static void CheckCn_0X00010102_CollectModuleIds(IEnumerable<XElement> isochroneModesOfModules, ICollection<string> moduleids)
        {
            foreach (var isochroneModeOfModules in isochroneModesOfModules)
            {
                string strIsochroneModeRequired = Help.GetAttributeValueFromXElement(isochroneModeOfModules, Attributes.s_IsochroneModeRequired);
                bool isochroneModeRequired = false;
                if (!string.IsNullOrEmpty(strIsochroneModeRequired))
                    isochroneModeRequired = XmlConvert.ToBoolean(strIsochroneModeRequired);
                if (!isochroneModeRequired)
                {
                    continue;
                }
                // Collect found Module ID.
                if (isochroneModeOfModules.Parent == null)
                {
                    continue;
                }

                if (isochroneModeOfModules.Parent.Parent == null)
                {
                    continue;
                }
                var moduleItem = isochroneModeOfModules.Parent.Parent.Parent;
                if (moduleItem == null)
                {
                    continue;
                }
                var id = moduleItem.Attribute(Attributes.ID);
                if (!moduleids.Contains(Help.CollapseWhitespace(id.Value)))
                    moduleids.Add(Help.CollapseWhitespace(id.Value));

            }
        }

        private static void CheckCn_0X00010102_GetIsochroneModesOfDaps(IEnumerable<XElement> isochroneModesOfDaps, ICollection<string> dapids)
        {
            foreach (var isochroneModeOfDaps in isochroneModesOfDaps)
            {
                string strIsochroneModeRequired = Help.GetAttributeValueFromXElement(isochroneModeOfDaps, Attributes.s_IsochroneModeRequired);
                bool isochroneModeRequired = false;
                if (!string.IsNullOrEmpty(strIsochroneModeRequired))
                    isochroneModeRequired = XmlConvert.ToBoolean(strIsochroneModeRequired);
                if (!isochroneModeRequired)
                {
                    continue;
                }
                // Collect found Dap ID.
                if (isochroneModeOfDaps.Parent == null)
                {
                    continue;
                }

                if (isochroneModeOfDaps.Parent.Parent == null)
                {
                    continue;
                }
                var dap = isochroneModeOfDaps.Parent.Parent.Parent;
                if (dap == null)
                {
                    continue;
                }
                var id = dap.Attribute(Attributes.ID);
                if (id != null && !dapids.Contains(Help.CollapseWhitespace(id.Value)))
                    dapids.Add(Help.CollapseWhitespace(id.Value));

            }
        }


        /// <summary>
        /// Checks if a DeviceAccessPoint which does not support isochrone mode has a submodule which REQUIRES
        /// isochrone mode or can be used together with a module which REQUIRES isochrone mode
        /// </summary>
        /// <param name="en"></param>
        /// <param name="dapids"></param>
        /// <param name="moduleids"></param>
        private void CheckCn_0X000101021(XElement en, ICollection<string> dapids, ICollection<string> moduleids)
        {
            if (dapids.Count > 0)
            {
                string dapid = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(en, Attributes.ID));
                if (dapids.Contains(Help.CollapseWhitespace(dapid)))
                {
                    // "The 'DeviceAccessPointItem' uses at least one '(Virtual)SubmoduleItem' which requires IsochroneMode."
                    string msg = Help.GetMessageString("M_0x00010102_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_1");
                }
            }

            if (moduleids.Count > 0)
            {
                // Find all Module references starting from current DAP.
                string xp = "./gsddef:UseableModules/gsddef:ModuleItemRef";
                var nltemp = en.XPathSelectElements(xp, Nsmgr);

                foreach (var en_ in nltemp)
                {
                    string moduleref = Help.GetAttributeValueFromXElement(en_, Attributes.s_ModuleItemTarget);
                    if (moduleids.Contains(Help.CollapseWhitespace(moduleref)))
                    {
                        //string msg = "The 'DeviceAccessPointItem' uses a 'ModuleItem' which 'VirtualSubmoduleItem(s)' requires IsochroneMode!";
                        if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                        {
                            // "The 'DeviceAccessPointItem' uses a 'ModuleItem' which has at least one 'VirtualSubmoduleItem' which requires IsochroneMode."
                            string msg = Help.GetMessageString("M_0x00010102_2");
                            string xpath = Help.GetXPath(en_);
                            var xli = (IXmlLineInfo)en_;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_2");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Checks if a DeviceAccessPoint which does not support isochrone mode has a submodule which SUPPORTS
        /// isochrone mode or can be used together with a module which SUPPORTS isochrone mode
        /// </summary>
        /// <param name="en"></param>
        /// <param name="dapids"></param>
        /// <param name="moduleids"></param>
        private void CheckCn_0X000101022(XElement en, ICollection<string> dapids, ICollection<string> moduleids)
        {
            if (dapids.Count > 0)
            {
                string dapid = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(en, Attributes.ID));
                if (dapids.Contains(Help.CollapseWhitespace(dapid)))
                {
                    // "'DeviceAccessPointItem' does not support IsochroneMode but has a submodule which supports IsochroneMode."
                    string msg = Help.GetMessageString("M_0x00010102_3");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_3");
                }
            }

            if (moduleids.Count > 0)
            {
                // Find all Module references starting from actual DAP.
                var nltemp =
                    en.Elements(NamespaceGsdDef + Elements.s_UseableModules)
                        .Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);

                foreach (var en_ in nltemp)
                {
                    string moduleref = Help.GetAttributeValueFromXElement(en_, Attributes.s_ModuleItemTarget);
                    if (moduleids.Contains(Help.CollapseWhitespace(moduleref)))
                    {
                        // "'DeviceAccessPointItem' does not support IsochroneMode but can be used together
                        // with modules which support IsochroneMode."
                        string msg = Help.GetMessageString("M_0x00010102_4");
                        string xpath = Help.GetXPath(en);
                        var xli = (IXmlLineInfo)en;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_4");
                    }
                }
            }
        }

        /// <summary>
        /// Checks if there are DeviceAccessPointItem elements which support IsochroneMode but have no ModuleItem element which uses isochrone mode
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00010121()
        {
            // Find all ModuleItem IDs, which support IsochroneMode.
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found Module IDs.
            var moduleids = new List<string>();
            XAttribute an;
            foreach (XElement en in nl)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && !moduleids.Contains(Help.CollapseWhitespace(an.Value)))
                    moduleids.Add(Help.CollapseWhitespace(an.Value));
            }

            // Find all DeviceAccessPointItem IDs, which have a submodule which support IsochroneMode.
            nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found DAP IDs.
            var dapids = new List<string>();
            foreach (XElement en in nl)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && !dapids.Contains(Help.CollapseWhitespace(an.Value)))
                    dapids.Add(Help.CollapseWhitespace(an.Value));
            }

            // Find all DeviceAccessPoints, which support IsochroneMode.
            var nlDaps = new List<XElement>();
            var interfaceSubmoduleItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            interfaceSubmoduleItems = Help.TryRemoveXElementsUnderXsAny(interfaceSubmoduleItems, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in interfaceSubmoduleItems)
            {
                string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
                bool isochroneModeSupported = false;
                if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                    isochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                string strIsochroneModeInRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);

                if (!isochroneModeSupported
                && strIsochroneModeInRTClasses == string.Empty)
                {
                    continue;
                }
                if (interfaceSubmoduleItem.Parent != null && !nlDaps.Contains(interfaceSubmoduleItem.Parent.Parent))
                    nlDaps.Add(interfaceSubmoduleItem.Parent.Parent);

            }
            CreateReport0x00010121_1(nlDaps, dapids, moduleids);

            return true;
        }

        private void CreateReport0x00010121_1(List<XElement> nlDaps, ICollection<string> dapids, ICollection<string> moduleids)
        {
            foreach (var en in nlDaps)
            {
                bool moduleFound = false;

                if (dapids.Count > 0)
                {
                    string dapid = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(en, Attributes.ID));
                    if (dapids.Contains(Help.CollapseWhitespace(dapid)))
                    {
                        break;
                    }
                }

                if (moduleids.Count > 0)
                {
                    // Find all Module references starting from current DAP.
                    var nltemp =
                        en.Elements(NamespaceGsdDef + Elements.s_UseableModules)
                            .Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);

                    foreach (var en_ in nltemp)
                    {
                        string moduleref = Help.GetAttributeValueFromXElement(en_, Attributes.s_ModuleItemTarget);
                        if (moduleids.Contains(Help.CollapseWhitespace(moduleref)))
                        {
                            moduleFound = true;
                            break;
                        }
                    }
                }

                if (moduleFound)
                {
                    continue;
                }
                // "The 'DeviceAccessPointItem' supports IsochroneMode, but none of its modules can use IsochroneMode."
                string msg = Help.GetMessageString("M_0x00010121_1");
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010121_1");

            }


        }
        /// <summary>
        /// Check number: CN_0x00010103
        /// If "InterfaceSubmoduleItem/@IsochroneModeSupported" is set to "true",
        /// then "InterfaceSubmoduleItem/@SupportedRT_Class" should be set to
        /// "Class2" or "Class3".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010103()
        {
            // Over all DeviceAccessPoints:
            var interfaceSubmoduleItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            interfaceSubmoduleItems = Help.TryRemoveXElementsUnderXsAny(interfaceSubmoduleItems, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in interfaceSubmoduleItems)
            {
                // Find all DeviceAccessPoints, which support IsochroneMode...
                string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
                bool isochroneModeSupported = false;
                if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                    isochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                string strIsochroneModeInRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);

                // ...and does not has 'SupportedRT_Class' is set to "Class2" or "Class3"
                string strSupportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);

                if ((isochroneModeSupported || strIsochroneModeInRTClasses != string.Empty)
                    && (strSupportedRTClass != "Class2" && strSupportedRTClass != "Class3"))
                {
                    if (Help.CheckSchemaVersion(interfaceSubmoduleItem, SupportedGsdmlVersion))
                    {
                        // "The 'InterfaceSubmoduleItem' supports IsochroneMode, but the attribute 'SupportedRT_Class' is not set to "Class2" respectively "Class3".";
                        string msg = Help.GetMessageString("M_0x00010103_1");
                        string xpath = Help.GetXPath(interfaceSubmoduleItem);
                        var xli = (IXmlLineInfo)interfaceSubmoduleItem;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010103_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010104
        /// If "InterfaceSubmoduleItem/@SupportedRT_Class" is set to "Class2" or "Class3",
        /// then "InterfaceSubmoduleItem/SynchronisationMode" should exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010104()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem" +
                        "[(@SupportedRT_Class='Class2' or @SupportedRT_Class='Class3') and not(./gsddef:SynchronisationMode)]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_2" or "RT_CLASS_3", but the element 'SynchronisationMode' is not set.";
                string msg = Help.GetMessageString("M_0x00010104_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportType_0X000101041, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010104_1");
            }

            var nl2 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SynchronisationMode);
            nl2 = Help.TryRemoveXElementsUnderXsAny(nl2, Nsmgr, Gsd);
            foreach (var en in nl2)
            {
                var interfaceSubmoduleItem = en.Parent;
                string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
                if (0 != string.Compare(supportedRTClass, "Class2", StringComparison.InvariantCulture)
                    && 0 != string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
                {
                    // "The element 'SynchronisationMode' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_2" or "RT_CLASS_3"."
                    string msg = Help.GetMessageString("M_0x00010104_2");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportType_0X000101042, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010104_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010105
        /// If "InterfaceSubmoduleItem/@SupportedRT_Class" is set to "Class3",
        /// "InterfaceSubmoduleItem/RT_Class3Properties" should also exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010105()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/" +
                        "gsddef:InterfaceSubmoduleItem[@SupportedRT_Class='Class3' and not(./gsddef:RT_Class3Properties)]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the element 'RT_Class3Properties' is not set."
                string msg = Help.GetMessageString("M_0x00010105_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010105_1");
            }

            var nl2 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties);
            nl2 = Help.TryRemoveXElementsUnderXsAny(nl2, Nsmgr, Gsd);
            foreach (var en in nl2)
            {
                var interfaceSubmoduleItem = en.Parent;
                string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
                if (0 != string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
                {
                    // "The element 'RT_Class3Properties' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_3"."
                    string msg = Help.GetMessageString("M_0x00010105_2");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportType_0X000101052, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010105_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010106
        /// If the 'InterfaceSubmoduleItem' supports "RT_CLASS_3", the related 'PortSubmoduleItem'
        /// element should have the attributes 'MaxPortRxDelay' and 'MaxPortTxDelay'.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010106()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem[" +
                        "../gsddef:InterfaceSubmoduleItem/@SupportedRT_Class='Class3' and not(@MaxPortRxDelay and @MaxPortTxDelay)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the related 'PortSubmoduleItem'
                // element does not have the needed attributes 'MaxPortRxDelay' respectively 'MaxPortTxDelay'."
                string msg = Help.GetMessageString("M_0x00010106_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010106_1");
            }

            // ------------------------------------------------------------

            // Find all Modules (IDs) with Ports without MaxPortRxDelay or MaxPortTxDelay.
            var nlPorts = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem).Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem)
                         .Where(
                             x =>
                                 x.Attribute(Attributes.s_MaxPortRxDelay) == null ||
                                 x.Attribute(Attributes.s_MaxPortTxDelay) == null);
            nlPorts = Help.TryRemoveXElementsUnderXsAny(nlPorts, Nsmgr, Gsd);

            // Collect found Module IDs.
            var moduleids = new List<string>();
            foreach (XElement port in nlPorts)
            {
                XAttribute an = port.Parent.Parent.Attribute(Attributes.ID);
                if (!moduleids.Contains(Help.CollapseWhitespace(an.Value)))
                    moduleids.Add(Help.CollapseWhitespace(an.Value));
            }

            // If no module is found which has ports with no MaxPortRxDelay or MaxPortTxDelay attributes!
            if (moduleids.Count <= 0)
                return true; // ---------->

            // Find all InterfaceSubmoduleItem elements, which supports RT_CLASS_3 (IRT).
            xp = ".//gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem[./@SupportedRT_Class='Class3']";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // Check PortSubmoduleItem parameters (MaxPortRxDelay and MaxPortTxDelay must exist) on
                // used ModuleItems.
                xp = "../../gsddef:UseableModules/gsddef:ModuleItemRef";
                var nltemp = en.XPathSelectElements(xp, Nsmgr);

                foreach (var en_ in nltemp)
                {
                    string moduleref = Help.GetAttributeValueFromXElement(en_, Attributes.s_ModuleItemTarget);
                    if (moduleids.Contains(moduleref))
                    {
                        // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the 'PortSubmoduleItem' element
                        // from the referenced 'ModuleItem' does not have the needed attributes 'MaxPortRxDelay'
                        // respectively 'MaxPortTxDelay'."
                        string msg = Help.GetMessageString("M_0x00010106_2");
                        string xpath = Help.GetXPath(en_);
                        var xli = (IXmlLineInfo)en_;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010106_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010107
        /// "VirtualSubmoduleItem/IsochroneMode/@T_DC_Min" must be 
        /// lower or equal than "VirtualSubmoduleItem/IsochroneMode/@T_DC_Max".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010107()
        {
            string xp = "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IsochroneMode[number(@T_DC_Min) > number(@T_DC_Max)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The attribute 'T_DC_Min' must be lower or equal than 'T_DC_Max'.";
                    string msg = Help.GetMessageString("M_0x00010107_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010107_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010108
        /// The value of "VirtualSubmoduleItem/IsochroneMode/@T_DC_Min" * the value of "VirtualSubmoduleItem/IsochroneMode/@T_DC_Base"
        /// must be lower or equal than 1024.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010108()
        {
            string xp = "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IsochroneMode[number(@T_DC_Min) * number(@T_DC_Base) > 1024]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The value of 'T_DC_Min' * 'T_DC_Base' must be lower or equal than 1024.";
                    string msg = Help.GetMessageString("M_0x00010108_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010108_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010109
        /// The value of "VirtualSubmoduleItem/IsochroneMode/@T_DC_Max" * the value of "VirtualSubmoduleItem/IsochroneMode/@T_DC_Base"
        /// must be lower or equal than 1024.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010109()
        {
            string xp = "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IsochroneMode[number(@T_DC_Max) * number(@T_DC_Base) > 1024]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The value of 'T_DC_Max' * 'T_DC_Base' must be lower or equal than 1024.";
                    string msg = Help.GetMessageString("M_0x00010109_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010109_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001010A
        /// The value of "VirtualSubmoduleItem/IsochroneMode/@T_IO_InputMin" * the value of "VirtualSubmoduleItem/IsochroneMode/@T_IO_Base"
        /// must be lower or equal than 32000000.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001010A()
        {
            string xp = "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IsochroneMode[number(@T_IO_InputMin) * number(@T_IO_Base) > 32000000]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The 'T_IO_Base' * 'T_IO_InputMin' must be lower or equal than 32000000."
                    string msg = Help.GetMessageString("M_0x0001010A_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001010A_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001010B
        /// The value of "VirtualSubmoduleItem/IsochroneMode/@T_IO_OutputMin" * the value of "VirtualSubmoduleItem/IsochroneMode/@T_IO_Base"
        /// must be lower or equal than 32000000.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001010B()
        {
            string xp = "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IsochroneMode[number(@T_IO_OutputMin) * number(@T_IO_Base) > 32000000]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The 'T_IO_Base' * 'T_IO_OutputMin' must be lower or equal than 32000000."
                    string msg = Help.GetMessageString("M_0x0001010B_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001010B_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001010D
        /// The 'DeviceAccessPointItem/ApplicationRelations' is ignored,
        /// if DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/ApplicationRelations' is defined.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001010D()
        {
            string xp = ".//gsddef:DeviceAccessPointItem[./gsddef:ApplicationRelations and ./gsddef:SystemDefinedSubmoduleList]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var en in nl)
            {
                // "The 'DeviceAccessPointItem/ApplicationRelations' is ignored because the DAP is not built according to PROFINET IO V1.0.
                // 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/ApplicationRelations' is evaluated instead."
                string msg = Help.GetMessageString("M_0x0001010D_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001010D_1");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001010E
        /// The 'DataItem/@UseAsBits' attribute must be set to "true", if the child element 'BitDataItem' is specified.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001010E()
        {
            // Find all DataItems, which don't support UseAsBits.
            var dataItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DataItem);
            dataItems = Help.TryRemoveXElementsUnderXsAny(dataItems, Nsmgr, Gsd);
            foreach (var dataItem in dataItems)
            {
                if (dataItem.Element(NamespaceGsdDef + Elements.s_BitDataItem) != null)
                {
                    string strUseAsBits = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                    bool useAsBits = false;
                    if (!string.IsNullOrEmpty(strUseAsBits))
                        useAsBits = XmlConvert.ToBoolean(strUseAsBits);

                    if (!useAsBits)
                    {
                        if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                        {
                            // "The 'DataItem/@UseAsBits' attribute must be set to "true", if the child element 'BitDataItem' is specified."
                            string msg = Help.GetMessageString("M_0x0001010E_1");
                            string xpath = Help.GetXPath(dataItem);
                            IXmlLineInfo xli = dataItem;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001010E_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001010F
        /// The 'BitDataItem/@BitOffset' attribute value is higher than allowed with the
        /// specified 'DataItem/@DataType' and its bit length.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001010F()
        {
            var xps = new List<string>();
            xps.Add(".//gsddef:DataItem[@DataType='Integer8' or @DataType='Unsigned8' or @DataType='Boolean' or @DataType='Unsigned8_S']/gsddef:BitDataItem[not(@BitOffset < 8)]");
            xps.Add(".//gsddef:DataItem[@DataType='Integer16' or @DataType='Unsigned16' or @DataType='Unsigned8+Unsigned8' or "
                    + "@DataType='Unsigned16_S' or @DataType='Integer16_S' or @DataType='N2' or @DataType='V2' or @DataType='L2' or @DataType='R2' "
                    + "or @DataType='T2' or @DataType='D2' or @DataType='E2' or @DataType='X' or @DataType='Unipolar2.16']"
                    + "/gsddef:BitDataItem[not(@BitOffset < 16)]");
            xps.Add(".//gsddef:DataItem[@DataType='OctetString2+Unsigned8']/gsddef:BitDataItem[not(@BitOffset < 24)]");
            xps.Add(".//gsddef:DataItem[@DataType='Integer32' or @DataType='Unsigned32' or @DataType='Float32' or @DataType='TimeOfDay without date indication' or "
                    + "@DataType='TimeDifference without date indication' or @DataType='F_MessageTrailer4Byte' or @DataType='N4' or @DataType='T4' "
                    + "or @DataType='C4' or @DataType='X4']/gsddef:BitDataItem[not(@BitOffset < 32)]");
            xps.Add(".//gsddef:DataItem[@DataType='Float32+Status8' or @DataType='Float32+Unsigned8' ]/gsddef:BitDataItem[not(@BitOffset < 40)]");
            string XPathFMessageTrailer5Byte = string.Format(CultureInfo.CurrentCulture, ".//gsddef:DataItem[@DataType='{0}']/gsddef:BitDataItem[not(@BitOffset < 40)]", FMessageTrailer5Byte);
            xps.Add(XPathFMessageTrailer5Byte);
            xps.Add(".//gsddef:DataItem[@DataType='TimeOfDay with date indication' or @DataType='TimeDifference with date indication']/gsddef:BitDataItem["
                    + "not(@BitOffset < 48)]");
            xps.Add(".//gsddef:DataItem[@DataType='Date']/gsddef:BitDataItem[not(@BitOffset < 56)]");
            xps.Add(".//gsddef:DataItem[@DataType='Integer64' or @DataType='Unsigned64' or @DataType='Float64' or @DataType='NetworkTime' or "
                    + "@DataType='NetworkTimeDifference' or @DataType='TimeStampDifferenceShort']/gsddef:BitDataItem[not(@BitOffset < 64)]");
            xps.Add(".//gsddef:DataItem[@DataType='TimeStamp' or @DataType='TimeStampDifference']/gsddef:BitDataItem[not(@BitOffset < 96)]");
            xps.Add(".//gsddef:DataItem[@DataType='VisibleString' or @DataType='OctetString' or @DataType='UnicodeString8' or @DataType='61131_STRING' "
                    + "or @DataType='61131_WSTRING' or @DataType='OctetString_S']/gsddef:BitDataItem[not(@BitOffset < number(../@Length) * 8)]");

            foreach (string xp in xps)
            {
                var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
                nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
                foreach (var en in nl)
                {
                    // "The 'BitDataItem/@BitOffset' attribute value is higher than allowed with the
                    // specified 'DataItem/@DataType' and its bit length."
                    string msg = Help.GetMessageString("M_0x0001010F_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001010F_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010110
        /// The 'DataItem/@DataType' attribute values "F_MessageTrailer4Byte" and "F_MessageTrailer5Byte"
        /// can only be used, if the attribute 'PROFIsafeSupported' of the '(Virtual)SubmoduleItem'
        /// element is set to "true".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010110()
        {
            // Find all IOData/DataItems, which support DataType "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte".
            var dataItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoData)
                            .Descendants(NamespaceGsdDef + Elements.s_DataItem);
            dataItems = Help.TryRemoveXElementsUnderXsAny(dataItems, Nsmgr, Gsd);
            foreach (var dataItem in dataItems)
            {
                string strDataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                if (strDataType != "F_MessageTrailer4Byte"
                    && strDataType != FMessageTrailer5Byte)
                {
                    continue;
                }

                // Check, if PROFIsafeSupported
                if (dataItem.Parent == null)
                {
                    continue;
                }

                if (dataItem.Parent.Parent != null)
                {
                    var submoduleItem = dataItem.Parent.Parent.Parent;
                    string strProfIsafeSupported = Help.GetAttributeValueFromXElement(
                        submoduleItem,
                        Attributes.s_ProfIsafeSupported);
                    bool pRofIsafeSupported = false;
                    if (!string.IsNullOrEmpty(strProfIsafeSupported))
                        pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);

                    if (pRofIsafeSupported)
                    {
                        continue;
                    }

                    if (!Help.CheckSchemaVersion(submoduleItem, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                }

                // "The 'DataItem/@DataType' attribute values "F_MessageTrailer4Byte" and "F_MessageTrailer5Byte"
                // can only be used, if the attribute 'PROFIsafeSupported' of the '(Virtual)SubmoduleItem'
                // element is set to "true"."
                string msg = Help.GetMessageString("M_0x00010110_1");
                string xpath = Help.GetXPath(dataItem);
                var xli = (IXmlLineInfo)dataItem;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010110_1");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010110_1
        /// The IO data types 'F_IO_StructureDescVersion' and 'F_IO_StructureDescCRC' may only be used
        /// if 'PROFIsafeSupported' is set to "true" for 'VirtualSubmoduleItem'.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010110_1()
        {
            // Find all IOData with attributes "F_IO_StructureDescVersion" or "F_IO_StructureDescCRC".
            var ioDataList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoData);
            ioDataList = Help.TryRemoveXElementsUnderXsAny(ioDataList, Nsmgr, Gsd);
            foreach (var ioData in ioDataList)
            {
                string strFIOStructureDescVersion = Help.GetAttributeValueFromXElement(ioData, Attributes.s_FIOStructureDescVersion);
                string strFIOStructureDescCrc = Help.GetAttributeValueFromXElement(ioData, Attributes.s_FIOStructureDescCrc);
                if (string.IsNullOrEmpty(strFIOStructureDescVersion)
                    && string.IsNullOrEmpty(strFIOStructureDescCrc))
                {
                    continue;
                }

                // Check, if PROFIsafeSupported
                var submoduleItem = ioData.Parent;
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submoduleItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (pRofIsafeSupported)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(submoduleItem, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "The 'IOData' attributes 'F_IO_StructureDescVersion' and 'F_IO_StructureDescCRC'
                // can only be used, if the attribute 'PROFIsafeSupported' of the '(Virtual)SubmoduleItem'
                // element is set to "true"."
                string msg = Help.GetMessageString("M_0x00010110_2");
                string xpath = Help.GetXPath(submoduleItem);
                var xli = (IXmlLineInfo)submoduleItem;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010110_2");
                }


            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010112
        /// The attribute "PortSubmoduleItem/@SubslotNumber" must be unique within a "ModuleItem"
        /// or a "DeviceAccessPointItem" element.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010112()
        {
            string xp = ".//gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem[@SubslotNumber = ../gsddef:InterfaceSubmoduleItem/" +
                        "@SubslotNumber or @SubslotNumber = ./following-sibling::gsddef:PortSubmoduleItem/@SubslotNumber]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The 'SubslotNumber' attribute value must be unique for each 'PortSubmoduleItem'
                // and 'InterfaceSubmoduleItem' element within a 'ModuleItem' or 'DeviceAccessPointItem'."
                string msg = Help.GetMessageString("M_0x00010112_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010112_1");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010113
        /// If the attribute "VirtualSubmoduleItem/@PROFIsafeSupported" is set to "true" ("1"),
        /// a "RecordDataList/F_ParameterRecordDataItem" item must be specified
        /// and the attribute "IOData/@F_IO_StructureDescCRC" must be present.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010113()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (pRofIsafeSupported)
                {
                    XElement fParameterRecordDataItem = null;
                    XElement recordDataList = submodule.Element(NamespaceGsdDef + Elements.s_RecordDataList);
                    if (recordDataList != null)
                        fParameterRecordDataItem = recordDataList.Element(NamespaceGsdDef + Elements.s_FParameterRecordDataItem);
                    var ioData = submodule.Element(NamespaceGsdDef + Elements.s_IoData);
                    string strFIOStructureDescCrc = Help.GetAttributeValueFromXElement(ioData, Attributes.s_FIOStructureDescCrc);
                    if (fParameterRecordDataItem == null || string.IsNullOrEmpty(strFIOStructureDescCrc))
                    {
                        if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                        {
                            // "If the '(Virtual)SubmoduleItem/@PROFIsafeSupported' attribute is set to "true", the element
                            // 'RecordDataList/F_ParameterRecordDataItem' must be specified and the
                            // 'IOData/@F_IO_StructureDescCRC' attribute must be available."
                            string msg = Help.GetMessageString("M_0x00010113_1");
                            string xpath = Help.GetXPath(submodule);
                            var xli = (IXmlLineInfo)submodule;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010113_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010114
        /// If the "RecordDataList/F_ParameterRecordDataItem" element is present for a "VirtualSubmoduleItem" element,
        /// the "PROFIsafeSupported" attribute must be set to "true" ("1").
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010114()
        {
            // Find all submodules with PROFIsafeSupported = "false"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);

                if (pRofIsafeSupported)
                    continue;

                var recordDataList = submodule.Element(NamespaceGsdDef + Elements.s_RecordDataList);
                if (recordDataList == null)
                    continue;

                var fParameterRecordDataItem = recordDataList.Element(NamespaceGsdDef + Elements.s_FParameterRecordDataItem);
                if (fParameterRecordDataItem != null)
                {
                    // "If the '(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem' element
                    // is available, the attribute 'PROFIsafeSupported' must be set to "true"."
                    string msg = Help.GetMessageString("M_0x00010114_1");
                    string xpath = Help.GetXPath(submodule);
                    var xli = (IXmlLineInfo)submodule;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010114_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010116
        /// If there is more than one VirtualSubmoduleItem in a VirtualSubmoduleList,
        /// all of them must use the FixedInSubslots attribute.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010116()
        {
            string xp = ".//gsddef:VirtualSubmoduleItem[count(../gsddef:VirtualSubmoduleItem) > 1 and not(@FixedInSubslots)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "If more than one 'VirtualSubmoduleItem' element within a 'VirtualSubmoduleList'
                // is specified, the attribute 'FixedInSubslots' must be used for all these elements."
                string msg = Help.GetMessageString("M_0x00010116_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010116_1");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010117
        /// A subslot number which is given in "SubslotList/SubslotItem/@SubslotNumber", must actually be used.
        /// (1) If >= 32768, it must be present in "InterfaceSubmoduleItem/@SubslotNumber" or
        ///     "PortSubmoduleItem/@SubslotNumber" or "@PhysicalSubslots".
        ///     Remark: pluggable PortSubmodules are supported from GSDML V2.25 on, but are considered already here.
        ///             Therefore also "@PhysicalSubslots" are checked for pluggable PortSubmodules.
        /// (2) Else it must be present in "VirtualSubmoduleItem/@FixedInSubslots" or "@PhysicalSubslots".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010117()
        {
            // (1)
            string xp = ".//*[./gsddef:SubslotList/gsddef:SubslotItem/@SubslotNumber >= 32768]";
            var nlElements = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nlElements = Help.TryRemoveXElementsUnderXsAny(nlElements, Nsmgr, Gsd);
            CreateReport0X00010117_1(nlElements);



            // (2)
            xp = ".//*[./gsddef:SubslotList/gsddef:SubslotItem/@SubslotNumber < 32768]";
            nlElements = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nlElements = Help.TryRemoveXElementsUnderXsAny(nlElements, Nsmgr, Gsd);
            CreateReport0X00010117_2(nlElements);


            return true;
        }

        private void CreateReport0X00010117_2(IEnumerable<XElement> nlElements)
        {
            foreach (var en in nlElements)
            {
                // --------------------------------------------------------
                string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem/@FixedInSubslots | ./@PhysicalSubslots";
                var nltemp = (IEnumerable)en.XPathEvaluate(xp, Nsmgr);
                List<ValueListHelper.ValueRangeT> realSubslots = CreateReport0X00010117_2_GetRealSubslots(nltemp);


                xp = "./gsddef:SubslotList/gsddef:SubslotItem/@SubslotNumber[. < 32768]";
                var nlSubslotNumber = (IEnumerable)en.XPathEvaluate(xp, Nsmgr);

                foreach (XAttribute antemp in nlSubslotNumber)
                {
                    if (ValueListHelper.IsValueInValueList(XmlConvert.ToUInt32(antemp.Value), realSubslots))
                    {
                        continue;
                    }
                    if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "The specified subslot number ('SubslotItem/@SubslotNumber') must be also available
                    // as a real subslot number of a Submodule ('VirtualSubmoduleItem/@FixedInSubslots',
                    // '@PhysicalSubslots')."
                    string msg = Help.GetMessageString("M_0x00010117_2");
                    string xpath = Help.GetXPath(antemp.Parent);
                    var xli = (IXmlLineInfo)antemp.Parent;
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportType_0X00010117,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010117_2");

                    }

                }
            }
        }

        private List<ValueListHelper.ValueRangeT> CreateReport0X00010117_2_GetRealSubslots(IEnumerable nltemp)
        {
            List<ValueListHelper.ValueRangeT> realSubslots = new();
            bool bFixedInSubslotsFound = false;
            foreach (XAttribute antemp in nltemp)
            {
                if (antemp.Name.LocalName == Attributes.s_FixedInSubslots)
                    bFixedInSubslotsFound = true;
                List<ValueListHelper.ValueRangeT> list = ValueListHelper.NormalizeValueList(antemp, Store);

                if (list == null || list.Count == 0)
                    continue;   // -----

                for (int i = 0; i < list.Count; i++)
                    if (!realSubslots.Contains(list[i]))
                        realSubslots.Add(list[i]);
            }
            // Append default value for FixedInSubslots if attribute is not available
            if (bFixedInSubslotsFound)
            {
                return realSubslots;
            }
            ValueListHelper.ValueRangeT newRange = new();
            newRange.From = 1;
            newRange.To = newRange.From;
            if (!realSubslots.Contains(newRange))
                realSubslots.Add(newRange);
            return realSubslots;
        }

        private void CreateReport0X00010117_1(IEnumerable<XElement> nlElements)
        {
            foreach (var en in nlElements)
            {
                string xp = "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/@SubslotNumber | " +
                     "./gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem/@SubslotNumber";
                var nltemp = (IEnumerable)en.XPathEvaluate(xp, Nsmgr);
                List<ValueListHelper.ValueRangeT> realSubslots = CreateReport0x00010117_1_GetRealSubslots(en, nltemp);




                xp = "./gsddef:SubslotList/gsddef:SubslotItem/@SubslotNumber[. >= 32768]";
                var nlSubslotNumber = (IEnumerable)en.XPathEvaluate(xp, Nsmgr);

                foreach (XAttribute antemp in nlSubslotNumber)
                {
                    if (ValueListHelper.IsValueInValueList(XmlConvert.ToUInt32(antemp.Value), realSubslots))
                    {
                        continue;
                    }
                    if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                    // "The specified subslot number ('SubslotItem/@SubslotNumber') must be also available
                    // as a real subslot number of a 'InterfaceSubmoduleItem/@SubslotNumber' or
                    // 'PortSubmoduleItem/@SubslotNumber' or '@PhysicalSubslots'."
                    string msg = Help.GetMessageString("M_0x00010117_1");
                    string xpath = Help.GetXPath(antemp.Parent);
                    var xli = (IXmlLineInfo)antemp.Parent;
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportType_0X00010117,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010117_1");

                    }
                }
            }
        }

        private List<ValueListHelper.ValueRangeT> CreateReport0x00010117_1_GetRealSubslots(XElement en, IEnumerable nltemp)
        {
            List<ValueListHelper.ValueRangeT> realSubslots = new();

            var physicalSubslots = en.Attribute(Attributes.s_PhysicalSubslots);
            if (physicalSubslots != null)
                // Split incoming attribute value string to individual numbers and areas in a list.
                realSubslots = ValueListHelper.NormalizeValueList(physicalSubslots, Store);

            foreach (XAttribute antemp in nltemp)
            {
                ValueListHelper.ValueRangeT newRange = new();
                newRange.From = XmlConvert.ToUInt32(antemp.Value);
                newRange.To = newRange.From;
                if (!realSubslots.Contains(newRange))
                    realSubslots.Add(newRange);
            }
            // Append default value for InterfaceSubmoduleItem/@SubslotNumber if attribute is not available
            var systemDefinedSubmoduleList = en.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList);
            if (systemDefinedSubmoduleList == null)
            {
                return realSubslots;
            }
            {
                var interfaceSubmoduleItem = systemDefinedSubmoduleList.Element(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
                if (interfaceSubmoduleItem == null || interfaceSubmoduleItem.Attribute(Attributes.s_SubslotNumber) != null)
                {
                    return realSubslots;
                }
                ValueListHelper.ValueRangeT newRange = new();
                newRange.From = 32768;
                newRange.To = newRange.From;
                if (!realSubslots.Contains(newRange))
                    realSubslots.Add(newRange);

            }
            return realSubslots;
        }

        /// <summary>
        /// Check number: CN_0x00010118
        /// Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must not be duplicates.
        /// Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must be lower than 32768 (0x8000).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010118()
        {
            string xp = ".//gsddef:VirtualSubmoduleItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            string fixedInSubslotListStr = string.Empty;
            XElement currentVirtualSubmoduleList = null;

            foreach (var an in nl)
            {
                var xli = (IXmlLineInfo)an;

                // Only values from the same VirtualSubmoduleList shall be collected
                XElement newVirtualSubmoduleList = an.Parent;
                if (newVirtualSubmoduleList != currentVirtualSubmoduleList)
                {
                    currentVirtualSubmoduleList = newVirtualSubmoduleList;
                    fixedInSubslotListStr = string.Empty;
                }

                if (string.IsNullOrEmpty(fixedInSubslotListStr))
                {
                    fixedInSubslotListStr = Help.GetAttributeValueFromXElement(an, Attributes.s_FixedInSubslots);
                }
                else
                {
                    fixedInSubslotListStr += " ";
                    fixedInSubslotListStr += Help.GetAttributeValueFromXElement(an, Attributes.s_FixedInSubslots);
                }

                List<ValueListHelper.ValueRangeT> fixedInSubslotList = null;
                ValueListHelper.NormalizeResult res = ValueListHelper.NormalizeResult.OK;
                if (!string.IsNullOrEmpty(fixedInSubslotListStr))
                    res = ValueListHelper.NormalizeValueList(fixedInSubslotListStr, out fixedInSubslotList);

                // Other errors are raised with check of single FixedInSubslots entries
                if (res == ValueListHelper.NormalizeResult.Overlap)
                {
                    // "Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must not be duplicates."
                    string msg = Help.GetMessageString("M_0x00010118_1");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00010118_1");
                    res = ValueListHelper.NormalizeValueList(Help.GetAttributeValueFromXElement(an, Attributes.s_FixedInSubslots), out fixedInSubslotList);
                }

                if (res == ValueListHelper.NormalizeResult.OK && fixedInSubslotList != null && fixedInSubslotList.Count > 0)
                {
                    uint highestValue = fixedInSubslotList[fixedInSubslotList.Count - 1].To;
                    if (highestValue >= Constants.s_SystemDefinedSubmoduleSubslotNumberMin)
                    {
                        // "Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must be lower than 32768 (0x8000)."
                        string msg = Help.GetMessageString("M_0x00010118_2");
                        string xpath = Help.GetXPath(an);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00010118_2");
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010119
        /// Values contained within the '(RT_Class3)TimingProperties/@SendClock' attribute must
        /// be higher or equal than {0} and lower or equal than {1}.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010119()
        {
            // Range
            const uint Max = 128;
            //            uint min = 1;
            const uint MinRT = 8;
            const uint MinIrt = 1;

            var nl =
                GsdProfileBody.Descendants()
                    .Attributes(Attributes.s_SendClock)
                    .Where(
                        attribute =>
                            attribute.Parent != null && (attribute.Parent.Name.LocalName == Elements.s_TimingProperties ||
                            attribute.Parent.Name.LocalName == Elements.s_RTClass3TimingProperties));
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                uint min = MinRT;
                string xpathToNode = Help.GetXPath(an);
                if (xpathToNode.Contains("RT_Class3TimingProperties"))
                {
                    min = MinIrt;
                }

                var lineInfo = (IXmlLineInfo)an;

                // Split incoming string to individual numbers and areas in a list.
                List<ValueListHelper.ValueRangeT> splitlist = ValueListHelper.NormalizeValueList(an, Store);

                // splitlist is empty if error occured in NormalieValueList
                if (splitlist.Count <= 0)
                {
                    continue;
                }
                bool value32Found = CreateReport0X00010119_1(splitlist, min, Max, an, lineInfo, false);

                CreateReport0x00010119_2(value32Found, an, lineInfo);

            }

            return true;
        }

        private void CreateReport0x00010119_2(bool value32Found, XObject an, IXmlLineInfo lineInfo)
        {

            if (value32Found || Help.IsGeneratedGSDMLFile(CheckParameter["GsdName"]))
            {
                return;
            }
            // "The 'SendClock' attribute must contain the mandatory value 32."
            string msg = Help.GetMessageString("M_0x00010119_2");
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportType_0x00010119_2,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010119_2");
        }

        private bool CreateReport0X00010119_1(
            IReadOnlyList<ValueListHelper.ValueRangeT> splitlist,
            uint min,
            uint Max,
            XObject an,
            IXmlLineInfo lineInfo,
            bool value32Found)
        {
            for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
            {
                if (splitlist[currentRange].From < min
                    || splitlist[currentRange].To > Max)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "Values contained within the '(RT_Class3)TimingProperties/@SendClock' attribute must
                        // be higher or equal than {0} and lower or equal than {1}."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010119_1"), min, Max);
                        string xpath = Help.GetXPath(an);
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010119_1");
                    }
                }
                if (splitlist[currentRange].From <= 32
                    && splitlist[currentRange].To >= 32)
                {
                    value32Found = true;
                }
            }
            return value32Found;
        }

        /// <summary>
        /// Check number: CN_0x0001011B
        /// Values for 'F_Source_Add/@AllowedValues' or 'F_Dest_Add/@AllowedValues' 
        /// attribute must be in the range of 1 to 65534.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001011B()
        {
            // Range
            uint max = 65534;
            uint min = 1;

            var nl =
                GsdProfileBody.Descendants()
                    .Attributes(Attributes.s_AllowedValues)
                    .Where(
                        attribute =>
                            attribute.Parent != null && (attribute.Parent.Name.LocalName == Elements.s_FSourceAdd ||
                                                         attribute.Parent.Name.LocalName == Elements.s_FDestAdd));
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                // Split incoming string to individual numbers and areas in a list.
                List<ValueListHelper.ValueRangeT> splitlist = ValueListHelper.NormalizeValueList(an, Store);

                for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
                {
                    if (splitlist[currentRange].From >= min
                        && splitlist[currentRange].To <= max)
                    {
                        continue;
                    }

                    if (!Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                    // "Values for 'F_Source_Add/@AllowedValues' or 'F_Dest_Add/@AllowedValues'
                    // attribute must be in the range of 1 to 65534."
                    string msg = Help.GetMessageString("M_0x0001011B_1");
                    string xpath = Help.GetXPath(an);
                    IXmlLineInfo xli = an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011B_1");


                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001011C
        /// Values for 'F_WD_Time/@AllowedValues' attribute must be in the range of 1 to 65535.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001011C()
        {
            // Range
            const uint Max = 65535;
            const uint Min = 1;

            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FParameterRecordDataItem)
                        .Elements(NamespaceGsdDef + Elements.s_FWdTime)
                        .Attributes(Attributes.s_AllowedValues);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                // Split incoming string to individual numbers and areas in a list.
                List<ValueListHelper.ValueRangeT> splitlist = ValueListHelper.NormalizeValueList(an, Store);

                for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
                {
                    if (splitlist[currentRange].From < Min || splitlist[currentRange].To > Max)
                    {
                        if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                        {
                            // "Values for 'F_WD_Time/@AllowedValues' attribute must be in the range of 1 to 65535."
                            string msg = Help.GetMessageString("M_0x0001011C_1");
                            string xpath = Help.GetXPath(an);
                            IXmlLineInfo xli = an;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011C_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001011D
        /// The value for 'F_WD_Time/@DefaultValue' attribute must be in the range of '1' to '65535'.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001011D()
        {
            // Range
            uint max = 65535;
            uint min = 1;

            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FParameterRecordDataItem)
                        .Elements(NamespaceGsdDef + Elements.s_FWdTime)
                        .Attributes(Attributes.s_DefaultValue);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                if (XmlConvert.ToUInt32(an.Value) < min || XmlConvert.ToUInt32(an.Value) > max)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "The value for 'F_WD_Time/@DefaultValue' attribute must be in the range of '1' to '65535'."
                        string msg = Help.GetMessageString("M_0x0001011D_1");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011D_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check RequiredSchemaVersion attribute
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X0001011F()
        {
            //var nl = (IEnumerable)Gsd.XPathEvaluate("//@RequiredSchemaVersion", Nsmgr);
            var nl = GsdProfileBody.Descendants().Attributes(Attributes.s_RequiredSchemaVersion);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute attribute in nl)
            {
                var lineInfo = (IXmlLineInfo)attribute;

                if (!Help.s_RegularExpressionGsdmlVersion.IsMatch(attribute.Value))
                {
                    // "The 'RequiredSchemaVersion' is not given in a valid format."
                    string msg = Help.GetMessageString("M_0x0001011F_4");
                    string xpath = Help.GetXPath(attribute);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011F_4");
                    return true;
                }
                else if (String.Compare(attribute.Value, FileNameGsdmlVersion, true, CultureInfo.InvariantCulture) > 0)
                {
                    // "The 'RequiredSchemaVersion' is higher than the GSDML version of this GSD file."
                    string msg = Help.GetMessageString("M_0x0001011F_1");
                    string xpath = Help.GetXPath(attribute);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011F_1");
                }
                else if (String.Compare(attribute.Value, SupportedGsdmlVersion, true, CultureInfo.InvariantCulture) <= 0)
                {
                    if (!m_AllExistingGsdmlVersions.Contains(attribute.Value.ToUpperInvariant()))
                    {
                        // "The 'RequiredSchemaVersion' attribute references a non-existing GSDML version."
                        string msg = Help.GetMessageString("M_0x0001011F_2");
                        string xpath = Help.GetXPath(attribute);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011F_2");
                    }
                }

                var item = attribute.Parent;
                if (item == null)
                {
                    return false;
                }
                double version = GetPNioVersion(attribute.Value);
                double requiredVersion = 0.0;
                if (item.Name.LocalName == Elements.s_SubmoduleItem)
                    requiredVersion = 2.1;
                if (item.Name.LocalName == Elements.s_PortSubmoduleItem)
                    requiredVersion = 2.25;
                if (version < requiredVersion)
                {
                    // "The attribute 'ApplicationProcess/SubmoduleList/{0}/@RequiredSchemaVersion' must be >= {1},
                    //  because prior to GSDML version V{1} no pluggable (port)submodules existed."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001011F_3"), item.Name, XmlConvert.ToString(requiredVersion));
                    string xpath = Help.GetXPath(attribute);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001011F_3");
                }
            }

            return true;
        }

        /// <summary>
        /// Check AllowedValues of F-Params for gaps
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00010120()
        {
            var nl =
                GsdProfileBody.Descendants()
                    .Attributes(Attributes.s_AllowedValues)
                    .Where(
                        attribute =>
                            attribute.Parent != null && attribute.Parent.Parent != null && attribute.Parent != null && attribute.Parent.Parent.Name.LocalName == Elements.s_FParameterRecordDataItem);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute att in nl)
            {
                if (!Regex.IsMatch(att.Value, @"^(([0-9]+\.\.[0-9]+)|([0-9]+))(( [0-9]+\.\.[0-9]+)|( [0-9]+))*$"))
                {
                    continue;
                }

                bool check = Help.ValueListHasGaps(att.Value);

                if (!check)
                {
                    continue;
                }
                // "No gaps allowed in 'AllowedValues' attribute."
                string msg = Help.GetMessageString("M_0x00010120_1");
                string xpath = Help.GetXPath(att);
                var xli = (IXmlLineInfo)att;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010120_1");

            }

            return true;
        }

        /// <summary>
        /// Some checks for F-Submodules.
        /// </summary>
        protected virtual bool CheckCn_0X00010122()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodItem in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (pRofIsafeSupported)
                {
                    var inputDataItems = GetInputDataItems(submodItem);
                    var outputDataItems = GetOutputDataItems(submodItem);

                    // Check if Input and Output is available
                    CheckInputOutputIsGiven(submodItem);

                    // Check if the F_MessageTrailerXByte is at the correct place
                    TestMessageTrailerXByteCorrectlyPlaced(submodItem, inputDataItems);
                    TestMessageTrailerXByteCorrectlyPlaced(submodItem, outputDataItems);

                    // F_MessageTrailerXByte must be identical for Input and Output
                    string inputMessageTrailer = GetMessageTrailerXByte(inputDataItems);
                    string outputMessageTrailer = GetMessageTrailerXByte(outputDataItems);
                    if (!String.IsNullOrEmpty(inputMessageTrailer)
                        && !String.IsNullOrEmpty(outputMessageTrailer)
                        && inputMessageTrailer != outputMessageTrailer)
                    {
                        if (Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                        {
                            // "For F-Submodules 'IOData' the message trailer bytes for 'Input' ("{0}") and 'Output' ("{1}") must be identical."
                            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010122_c"), inputMessageTrailer, outputMessageTrailer);
                            string xpath = Help.GetXPath(submodItem);
                            var xli = (IXmlLineInfo)submodItem;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010122_c");
                        }
                    }

                    // Is the F_MessageTrailerXByte valid?
                    if (!String.IsNullOrEmpty(inputMessageTrailer)
                        && inputMessageTrailer == outputMessageTrailer)
                    {
                        // Check if the given F_MessageTrailerXByte is sufficient
                        int ioDataChannelSize = GetIODataChannelSize(submodItem);
                        TestMessageTrailerXByteIsSufficient(submodItem, inputDataItems, outputDataItems, ioDataChannelSize);
                    }

                    // Check the usage of UseAsBits in dependency with the DataType for all DataItems
                    TestUseAsBitsProfIsafe(inputDataItems);
                    TestUseAsBitsProfIsafe(outputDataItems);
                }
            }

            return true;
        }

        /// <summary>
        /// Check if for F-Submodules 'Consistency="All items consistency"' is given for Input and Output.
        /// </summary>
        protected virtual bool CheckCn_0X00010123()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (!pRofIsafeSupported)
                {
                    continue;
                }
                var ioData = submodule.Element(NamespaceGsdDef + Elements.s_IoData); // required
                if (ioData == null)
                {
                    continue;
                }
                var input = ioData.Element(NamespaceGsdDef + Elements.s_Input); // optional
                bool bAllItemsConsistencyGiven = false;
                if (input != null)
                {
                    string strConsistency = Help.GetAttributeValueFromXElement(input, Attributes.s_Consistency);
                    if (strConsistency == Enums.s_AllItemsConsistency)
                        bAllItemsConsistencyGiven = true;
                }

                if (bAllItemsConsistencyGiven)
                {
                    continue;
                }

                // "For F-Submodules 'Consistency'="All items consistency" must be given for Input."
                if (!Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                {
                    continue;
                }

                string msg = Help.GetMessageString("M_0x00010123_1");
                string xpath = Help.GetXPath(submodule);
                var xli = (IXmlLineInfo)submodule;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010123_1");

            }

            return true;
        }

        /// <summary>
        /// Check if for F-Submodules 'Consistency="All items consistency"' is given for Output.
        /// </summary>
        protected virtual bool CheckCn_0X00010124()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (!pRofIsafeSupported)
                {
                    continue;
                }
                var ioData = submodule.Element(NamespaceGsdDef + Elements.s_IoData); // required
                if (ioData == null)
                {
                    continue;
                }

                var output = ioData.Element(NamespaceGsdDef + Elements.s_Output); // optional
                bool bAllItemsConsistencyGiven = false;
                if (output != null)
                {
                    string strConsistency = Help.GetAttributeValueFromXElement(output, Attributes.s_Consistency);
                    if (strConsistency == Enums.s_AllItemsConsistency)
                        bAllItemsConsistencyGiven = true;
                }
                if (bAllItemsConsistencyGiven)
                {
                    continue;
                }

                // "For F-Submodules 'Consistency'="All items consistency" must be given for 'Output'.";
                if (!Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                {
                    continue;
                }

                string msg = Help.GetMessageString("M_0x00010124_1");
                string xpath = Help.GetXPath(submodule);
                var xli = (IXmlLineInfo)submodule;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010124_1");

            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_CRC_Length.
        /// </summary>
        protected virtual bool CheckCn_0X00010125()
        {
            const string Xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_CRC_Length";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                var lineInfo = (IXmlLineInfo)en;

                // Get the attribute 'Visible'
                bool visible = false; // Default value
                string strVisible = Help.GetAttributeValueFromXElement(en, Attributes.s_Visible);
                if (!String.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                uint maxIoSizeFor3ByteCrc = GetMaxIoSizeFor3ByteCrc(en.Parent);
                uint maxIoSizeFor4ByteCrc = GetMaxIoSizeFor4ByteCrc(en.Parent);
                uint guaranteedIoSizeFor4ByteCrc = GetGuaranteedIoSizeFor4ByteCrc(en.Parent);

                string defaultValue = Help.GetAttributeValueFromXElement(en, Attributes.s_DefaultValue);
                if (String.IsNullOrEmpty(defaultValue))
                    defaultValue = Enums.s_ByteCrc3;

                var aAllowedValues = en.Attribute(Attributes.s_AllowedValues);
                string allowedValues;
                if (aAllowedValues != null)
                    allowedValues = aAllowedValues.Value;
                else
                    allowedValues = Enums.s_ByteCrc3;

                // If 'Visible' is not set, the default works for DefaultValue and AllowedValues.
                // Therefore in this case a warning is reported, otherwise error.
                ReportTypes reportType = ReportTypes.GSD_RT_Error;
                if (!visible)
                    reportType = ReportTypes.GSD_RT_Warning;

                CreateReports0x00010125(defaultValue, en, reportType, lineInfo, allowedValues, visible, maxIoSizeFor3ByteCrc, maxIoSizeFor4ByteCrc, guaranteedIoSizeFor4ByteCrc);
            }
            return true;
        }
        private void CreateReports0x00010125(
            string defaultValue,
            XObject en,
            ReportTypes reportType,
            IXmlLineInfo lineInfo,
            string allowedValues,
            bool visible,
            uint maxIoSizeFor3ByteCrc,
            uint maxIoSizeFor4ByteCrc,
            uint guaranteedIoSizeFor4ByteCrc)
        {

            CreateReport0X00010125_1(defaultValue, en, reportType, lineInfo);

            CreateReport0X00010125_2(allowedValues, en, reportType, lineInfo);



            if (IsProfiSafeV26(en.Parent))
            {
                CreateReport0X00010125_7(defaultValue, en, reportType, lineInfo);

                CreateReport0X00010125_8(allowedValues, en, reportType, lineInfo);

            }
            else
            {
                CreateReport0X00010125_b(defaultValue, en, lineInfo);

            }
            if (en.Parent == null)
            {
                return;
            }

            if (en.Parent.Parent == null)
            {
                return;
            }
            var submodItem = en.Parent.Parent.Parent;
            var inputDataItems = GetInputDataItems(submodItem);

            // F_MessageTrailerXByte must be identical for Input and Output
            string messageTrailer = GetMessageTrailerXByte(inputDataItems);

            int ioDataChannelSize = GetIODataChannelSize(submodItem);
            CreateReport0X00010125_3(allowedValues, defaultValue, en, reportType, lineInfo);


            // If 'Visible' is not set, for cross-checks between F_CRC_Length and data length as well as F_MessageTrailerXByte
            // the default works for DefaultValue and AllowedValues
            if (!visible)
            {
                defaultValue = Enums.s_ByteCrc3;
            }

            CreateReport0X00010125_4(ioDataChannelSize, maxIoSizeFor3ByteCrc, defaultValue, en, lineInfo);



            if (defaultValue == Enums.s_ByteCrc4 && messageTrailer != FMessageTrailer5Byte)
            {
                CreateReport0X00010125_5(en, lineInfo);

            }
            else if (defaultValue == Enums.s_ByteCrc3 && messageTrailer != Enums.s_FMessageTrailer4Byte)
            {
                CreateReport0X00010125_6(en, lineInfo);

            }

            if (defaultValue != Enums.s_ByteCrc4)
            {
                return;
            }
            if (ioDataChannelSize > maxIoSizeFor4ByteCrc)
            {
                CreateReport0X00010125_9(en, maxIoSizeFor4ByteCrc, ioDataChannelSize, lineInfo);


            }
            else if (ioDataChannelSize > guaranteedIoSizeFor4ByteCrc)
            {
                CreateReport0X00010125_a(en, guaranteedIoSizeFor4ByteCrc, ioDataChannelSize, lineInfo);

            }


        }

        private void CreateReport0X00010125_a(
            XObject en,
           uint guaranteedIoSizeFor4ByteCrc,
           int ioDataChannelSize,
           IXmlLineInfo lineInfo)
        {
            // "For "4-Byte-CRC" {0} bytes 'Input' or 'Output' are guaranteed. You use {1} bytes.
            // This may not work with all PROFIsafe hosts."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }

            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010125_a"),
                guaranteedIoSizeFor4ByteCrc,
                ioDataChannelSize);
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_a");
        }
        private void CreateReport0X00010125_9(
             XObject en,
             uint maxIoSizeFor4ByteCrc,
             int ioDataChannelSize,
             IXmlLineInfo lineInfo)
        {

            // "For "4-Byte-CRC" a maximum of {0} bytes 'Input' or 'Output' is defined. You use {1} bytes."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010125_9"), maxIoSizeFor4ByteCrc, ioDataChannelSize);
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_9");
        }

        private void CreateReport0X00010125_6(XObject en, IXmlLineInfo lineInfo)
        {
            // ""F_MessageTrailer4Byte" is necessary when using a "3-Byte-CRC" as default value.";
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }

            string msg = Help.GetMessageString("M_0x00010125_6");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_6");
        }
        private void CreateReport0X00010125_5(XObject en, IXmlLineInfo lineInfo)
        {
            // ""F_MessageTrailer5Byte" is necessary when using a "4-Byte-CRC" as default value.";
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_5");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_5");
        }
        private void CreateReport0X00010125_4(
           int ioDataChannelSize,
           uint maxIoSizeFor3ByteCrc,
           string defaultValue,
           XObject en,
           IXmlLineInfo lineInfo)
        {
            if (ioDataChannelSize <= maxIoSizeFor3ByteCrc || defaultValue == Enums.s_ByteCrc4)
            {
                return;
            }
            // ""4-Byte-CRC" must be used when using 'Input' or 'Output' data with more than {0} bytes."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010125_4"), maxIoSizeFor3ByteCrc);
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_4");
        }
        private void CreateReport0X00010125_3(
           string allowedValues,
           string defaultValue,
           XObject en,
           ReportTypes reportType,
           IXmlLineInfo lineInfo)
        {
            if (allowedValues.Contains(defaultValue))
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_CRC_Length': The default value (= {0}) is not contained in allowed values (= {1}).";
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010125_3"), defaultValue, allowedValues);
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_3");

        }


        private void CreateReport0X00010125_b(string defaultValue, XObject en, IXmlLineInfo lineInfo)
        {
            if (defaultValue != Enums.s_ByteCrc4)
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue': "4-Byte-CRC" should not be used for PROFIsafe V2.4."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_b");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_b");


        }
        private void CreateReport0X00010125_8(string allowedValues, XObject en, ReportTypes reportType, IXmlLineInfo lineInfo)
        {
            if (allowedValues == Enums.s_ByteCrc4)
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_CRC_Length/@AllowedValues': "4-Byte-CRC" is mandatory for PROFIsafe V2.6.1."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_8");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_8");

        }

        private void CreateReport0X00010125_7(string defaultValue, XObject en, ReportTypes reportType, IXmlLineInfo lineInfo)
        {
            if (defaultValue == Enums.s_ByteCrc4)
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue': "4-Byte-CRC" is mandatory for PROFIsafe V2.6.1."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_7");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_7");

        }

        private void CreateReport0X00010125_2(string allowedValues, XObject en, ReportTypes reportType, IXmlLineInfo lineInfo)
        {

            if (!allowedValues.Contains(Enums.s_ByteCrc2))
            {
                return;
            }
            // "'F_ParameterRecordDataItem/F_CRC_Length': "2-Byte-CRC" is allowed in
            // V1-mode only, which is not applicable with PROFINET and must not be used for 'AllowedValues'."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_2");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_2");
        }

        private void CreateReport0X00010125_1(string defaultValue, XObject en, ReportTypes reportType, IXmlLineInfo lineInfo)
        {
            if (defaultValue != Enums.s_ByteCrc2)
            {
                return;
            }
            // "F_ParameterRecordDataItem/F_CRC_Length: 2-Byte-CRC is allowed in
            // V1-mode only, which is not applicable with PROFINET and must not be used for 'DefaultValue'."
            if (!Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x00010125_1");
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                reportType,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010125_1");

        }

        protected virtual uint GetMaxIoSizeFor3ByteCrc(XElement fParameterRecordData)
        {
            return s_MaxIoDataFor3ByteCrc;
        }

        protected virtual uint GetMaxIoSizeFor4ByteCrc(XElement fParameterRecordData)
        {
            return s_MaxIoDataFor4ByteCrc;
        }

        protected virtual uint GetGuaranteedIoSizeFor4ByteCrc(XElement fparameterRecordData)
        {
            return s_GuaranteedIoDataFor4ByteCrc;
        }

        protected virtual bool IsProfiSafeV26(XElement fparameterRecordData)
        {
            return false;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_SIL.
        /// </summary>
        protected virtual bool CheckCn_0X00010126()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_SIL";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fsil in nl)
            {
                var aAllowedValues = fsil.Attribute(Attributes.s_AllowedValues);
                if (aAllowedValues == null)
                    continue;
                string allowedValues = aAllowedValues.Value;

                string defaultValue = Help.GetAttributeValueFromXElement(fsil, Attributes.s_DefaultValue);
                if (String.IsNullOrEmpty(defaultValue))
                    defaultValue = Enums.s_Sil3;

                if (!allowedValues.Contains(defaultValue))
                {
                    // "'F_ParameterRecordDataItem/F_SIL': The default value (= {0}) is not contained in allowed values (= {1})."
                    if (Help.CheckSchemaVersion(fsil, SupportedGsdmlVersion))
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010126_1"), defaultValue, allowedValues);
                        string xpath = Help.GetXPath(fsil);
                        var xli = (IXmlLineInfo)fsil;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010126_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Some checks for F_ParameterRecordDataItem/F_WD_Time.
        /// </summary>
        protected virtual bool CheckCn_0X00010127()
        {
            const string Xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem/gsddef:F_WD_Time";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fwdTime in nl)
            {
                bool valid = FindAllowedValues(fwdTime, out List<ValueListHelper.ValueRangeT> allowedValues);

                if (!valid)
                    continue;

                uint allowedValuesFrom = allowedValues[0].From;
                uint allowedValuesTo = allowedValues[allowedValues.Count - 1].To;

                string strDefaultValue = Help.GetAttributeValueFromXElement(fwdTime, Attributes.s_DefaultValue);

                UInt32 defaultValue = 150;
                if (!string.IsNullOrEmpty(strDefaultValue))
                    defaultValue = XmlConvert.ToUInt32(strDefaultValue);

                if (defaultValue < allowedValuesFrom || defaultValue > allowedValuesTo)
                {
                    if (Help.CheckSchemaVersion(fwdTime, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/F_WD_Time': The default value (= {0}) is not contained in allowed values (= {1}..{2})." 
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010127_1"), defaultValue, allowedValuesFrom, allowedValuesTo);
                        string xpath = Help.GetXPath(fwdTime);
                        var xli = (IXmlLineInfo)fwdTime;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010127_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Validate the F_Par_CRC.
        /// </summary>
        protected virtual bool CheckCn_0X00010128_1()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            Crc16 calcFParCrc = new Crc16();

            foreach (var fparamRecord in nl)
            {
                calculateFParCRC(fparamRecord);
            }

            return true;
        }

        /// <summary>
        /// Calculate the F_Par_CRC.
        /// </summary>
        protected virtual void calculateFParCRC(XElement fparamRecord)
        {
            Crc16 calcFParCrc = new Crc16();

            // Build the F_Par_CRC
            calcFParCrc.InitChecksum();

            // The first four bytes are occupied with F_iPar_CRC
            // Add F_iPar_CRC, if visible
            byte[] data1 = GetFiParBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data1);

            // The fifth byte is occupied with F_Prm_Flag1 = F_Check_SeqNr, F_Check_iPar, F_SIL, F_CRC_Length
            byte fPrmFlag1 = GetFPrmFlag1(fparamRecord);

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag1);

            byte fPrmFlag2 = GetFPrmFlag2(fparamRecord);

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag2);

            // The seventh and eighth byte is occupied with F_Source_Add
            // Add F_Source_Add, if visible
            byte[] data2 = GetFSourceAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data2);

            // The ninth and tenth byte is occupied with F_Dest_Add
            // Add F_Dest_Add, if visible
            byte[] data3 = GetFDestAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data3);

            // The eleventh and twelfth byte is occupied with F_WD_Time
            // Add F_WD_Time, if visible
            byte[] data4 = GetFWdTimeBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data4);

            // Finish the checksum process, returning the CRC
            UInt16 calculatedFParCrc = calcFParCrc.FinishChecksum(false);

            // Get the F_Par_CRC from Xml file
            UInt16 fparCrc = 53356;  // Default: "53356"
            var fparCrCs = fparamRecord.Element(NamespaceGsdDef + Elements.s_FParCrc);
            XObject node = fparamRecord;
            if (fparCrCs != null)
            {
                node = fparCrCs;
                XAttribute nodeFParCrc = fparCrCs.Attribute(Attributes.s_DefaultValue);
                if (nodeFParCrc != null)
                {
                    fparCrc = XmlConvert.ToUInt16(nodeFParCrc.Value);
                    node = nodeFParCrc;
                }
            }

            CreateReport0X00010128_1(calculatedFParCrc, fparCrc, fparamRecord, node);
        }
        protected void CreateReport0X00010128_1(ushort calculatedFParCrc, ushort fparCrc, XObject fparamRecord, XObject node)
        {

            if (calculatedFParCrc == fparCrc)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(fparamRecord, SupportedGsdmlVersion))
            {
                return;
            }
            // "'F_ParameterRecordDataItem/@F_Par_CRC': The CRC over all default values is {0}, but should be {1}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010128_1"), fparCrc, calculatedFParCrc);
            string xpath = Help.GetXPath(node);
            IXmlLineInfo xli = node;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010128_1");

        }

        protected virtual byte GetFPrmFlag2(XElement fParamRecord)
        {
            // The sixt byte is occupied with F_Prm_Flag2 = F_Block_ID, F_Par_Version
            byte fprmFlag2 = 0;

            // F_Block_ID (bit 3-5):
            // Add F_Block_ID, if visible
            var fblockIDs = fParamRecord.Element(NamespaceGsdDef + Elements.s_FBlockID);
            if (fblockIDs != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fblockIDs, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    byte fBlockID = 0; // Default: "0"
                    string strFBlockID = Help.GetAttributeValueFromXElement(fblockIDs, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFBlockID))
                        fBlockID = (byte)Int16.Parse(strFBlockID, CultureInfo.InvariantCulture);
                    fprmFlag2 |= (byte)(fBlockID << 3);
                }
            }

            // F_Par_Version (bit 6+7):
            // Add F_Par_Version, if visible
            var fparVersions = fParamRecord.Element(NamespaceGsdDef + Elements.s_FParVersion);
            if (fparVersions != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fparVersions, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    byte fparVersion = 1; // Default: "1" (V2-mode)
                    string strFParVersion = Help.GetAttributeValueFromXElement(fparVersions, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFParVersion))
                        fparVersion = (byte)Int16.Parse(strFParVersion, CultureInfo.InvariantCulture);
                    fprmFlag2 |= (byte)(fparVersion << 6);
                }
            }
            else
            {
                // F_Par_Version is not given, but because it is always visible the default value must be added
                byte fParVersion = 1; // Default: "1" (V2-mode)
                fprmFlag2 |= (byte)(fParVersion << 6);
            }

            return fprmFlag2;
        }

        protected virtual byte GetFPrmFlag1(XElement fparamRecord)
        {
            byte fprmFlag1 = 0;

            // F_Check_SeqNr (bit 0): don't care in V2-mode

            // F_Check_iPar (bit 1):
            // Add F_Check_iPar, if visible
            fprmFlag1 = GetFprmFlag1AddFCheckiPar(fparamRecord, fprmFlag1);


            // F_SIL (bit 2+3):
            // Add F_SIL, if visible
            fprmFlag1 = GetFprmFlag1_AddF_SIL(fparamRecord, fprmFlag1);



            // F_CRC_Length (bit 4+5):
            // Add F_CRC_Length, if visible
            var fcrcLengths = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCrcLength);
            if (fcrcLengths == null)
            {
                return fprmFlag1;
            }

            {
                string strVisible = Help.GetAttributeValueFromXElement(fcrcLengths, Attributes.s_Visible);
                bool bVisible = false; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (!bVisible)
                {
                    return fprmFlag1;
                }


                byte fcrcLength = 0; // Default: "3-Byte-CRC"
                string strFcrcLength = Help.GetAttributeValueFromXElement(fcrcLengths, Attributes.s_DefaultValue);
                if (strFcrcLength == Enums.s_ByteCrc2)
                    fcrcLength = 1;
                if (strFcrcLength == Enums.s_ByteCrc4)
                    fcrcLength = 2;
                fprmFlag1 |= (byte)(fcrcLength << 4);

            }

            return fprmFlag1;
        }

        private byte GetFprmFlag1_AddF_SIL(XContainer fparamRecord, byte fprmFlag1)
        {
            var fsiLs = fparamRecord.Element(NamespaceGsdDef + Elements.s_FSil);
            if (fsiLs != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fsiLs, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (!bVisible)
                {
                    return fprmFlag1;
                }
                byte fsil = 2; // Default: "SIL3"
                string strFsil = Help.GetAttributeValueFromXElement(fsiLs, Attributes.s_DefaultValue);
                if (strFsil == Enums.s_Sil1)
                    fsil = 0;
                if (strFsil == Enums.s_Sil2)
                    fsil = 1;
                if (strFsil == Enums.s_NoSil)
                    fsil = 3;
                fprmFlag1 |= (byte)(fsil << 2);

            }
            else
            {
                // F_SIL is not given, but because it is always visible the default value must be added
                byte fsil = 2; // Default: "SIL3"
                fprmFlag1 |= (byte)(fsil << 2);
            }
            return fprmFlag1;
        }

        private byte GetFprmFlag1AddFCheckiPar(XContainer fparamRecord, byte fprmFlag1)
        {
            var fcheckiPars = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCheckIPar);
            if (fcheckiPars == null)
            {
                return fprmFlag1;
            }
            string strVisible = Help.GetAttributeValueFromXElement(fcheckiPars, Attributes.s_Visible);
            bool bVisible = true; // Default
            if (!string.IsNullOrEmpty(strVisible))
                bVisible = XmlConvert.ToBoolean(strVisible);
            if (!bVisible)
            {
                return fprmFlag1;
            }
            byte fCheckiPar = 0; // Default: "NoCheck"
            string strFCheckiPar = Help.GetAttributeValueFromXElement(fcheckiPars, Attributes.s_DefaultValue);
            if (strFCheckiPar == Enums.s_Check)
                fCheckiPar = 1;
            fprmFlag1 |= (byte)(fCheckiPar << 1);

            return fprmFlag1;
        }

        protected virtual byte[] GetFWdTimeBytes(XElement fparamRecord)
        {
            byte[] data4 = new byte[2];
            data4[0] = 0;
            data4[1] = 0;

            var fwdTimes = fparamRecord.Element(NamespaceGsdDef + Elements.s_FWdTime);
            if (fwdTimes != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fwdTimes, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    UInt16 fwdTime = 150; // Default: "150" (150 ms)
                    string strFwdTime = Help.GetAttributeValueFromXElement(fwdTimes, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFwdTime))
                        fwdTime = UInt16.Parse(strFwdTime, CultureInfo.InvariantCulture);

                    data4[0] = (byte)(fwdTime >> 8);
                    data4[1] = (byte)fwdTime;
                }
            }
            else
            {
                // F_WD_Time is not given, but because it is always visible the default value must be added
                UInt16 fwdTime = 150; // Default: "150" (150 ms)
                data4[0] = (byte)(fwdTime >> 8);
                data4[1] = (byte)fwdTime;
            }

            return data4;
        }

        protected virtual byte[] GetFDestAddBytes(XElement fparamRecord)
        {
            byte[] data3 = new byte[2];
            data3[0] = 0;
            data3[1] = 0;

            var fdestAdds = fparamRecord.Element(NamespaceGsdDef + Elements.s_FDestAdd);
            if (fdestAdds != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fdestAdds, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    UInt16 fdestAdd = 1; // Default: "1"
                    string strFDestAdd = Help.GetAttributeValueFromXElement(fdestAdds, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFDestAdd))
                        fdestAdd = UInt16.Parse(strFDestAdd, CultureInfo.InvariantCulture);

                    data3[0] = (byte)(fdestAdd >> 8);
                    data3[1] = (byte)fdestAdd;
                }
            }
            else
            {
                // F_Dest_Add is not given, but because it is always visible the default value must be added
                UInt16 fdestAdd = 1; // Default: "1"
                data3[0] = (byte)(fdestAdd >> 8);
                data3[1] = (byte)fdestAdd;
            }

            return data3;
        }

        protected virtual byte[] GetFSourceAddBytes(XElement fparamRecord)
        {
            byte[] data2 = new byte[2];
            data2[0] = 0;
            data2[1] = 0;
            var fsourceAdds = fparamRecord.Element(NamespaceGsdDef + Elements.s_FSourceAdd);
            if (fsourceAdds != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fsourceAdds, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    UInt16 fsourceAdd = 1; // Default: "1"
                    string strFSourceAdd = Help.GetAttributeValueFromXElement(fsourceAdds, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFSourceAdd))
                        fsourceAdd = UInt16.Parse(strFSourceAdd, CultureInfo.InvariantCulture);

                    data2[0] = (byte)(fsourceAdd >> 8);
                    data2[1] = (byte)fsourceAdd;
                }
            }
            else
            {
                // F_Source_Add is not given, but because it is always visible the default value must be added
                UInt16 fsourceAdd = 1; // Default: "1"
                data2[0] = (byte)(fsourceAdd >> 8);
                data2[1] = (byte)fsourceAdd;
            }

            return data2;
        }

        protected virtual byte[] GetFiParBytes(XElement fparamRecord)
        {
            byte[] data1 = new byte[4];
            data1[0] = 0;
            data1[1] = 0;
            data1[2] = 0;
            data1[3] = 0;
            var fiParCrc = fparamRecord.Element(NamespaceGsdDef + Elements.s_FIParCrc);
            if (fiParCrc != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fiParCrc, Attributes.s_Visible);
                bool bVisible = true; // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    UInt32 uintFiParCrc = 0;
                    string strFiParCrc = Help.GetAttributeValueFromXElement(fiParCrc, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFiParCrc))
                        uintFiParCrc = UInt32.Parse(strFiParCrc, CultureInfo.InvariantCulture);

                    data1[0] = (byte)(uintFiParCrc >> 24);
                    data1[1] = (byte)(uintFiParCrc >> 16);
                    data1[2] = (byte)(uintFiParCrc >> 8);
                    data1[3] = (byte)uintFiParCrc;
                }
            }

            return data1;
        }

        /// <summary>
        /// Validate the F_ParamDescCRC.
        /// </summary>
        protected virtual bool CheckCn_0X00010128_2()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, this.Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            Crc16 calcFParamDescCrc = new();

            foreach (XElement fParamRecord in nl)
            {
                // Init the F_ParamDescCRC
                calcFParamDescCrc.InitChecksum();

                // Collect the bytes for checksum
                CollectBytesFParamDescCrc(fParamRecord, calcFParamDescCrc);

                // Finish the checksum process, returning the CRC
                UInt16 calculatedFParamDescCrc = calcFParamDescCrc.FinishChecksum(true);

                // Get the F_ParamDescCRC from Xml file
                UInt16 fparamDescCrc = 0;
                var nodeFParamDescCrc = fParamRecord.Attribute(Attributes.s_FParamDescCrc);
                XObject node = fParamRecord;
                if (nodeFParamDescCrc != null)
                {
                    fparamDescCrc = XmlConvert.ToUInt16(nodeFParamDescCrc.Value);
                    node = nodeFParamDescCrc;
                }

                if (calculatedFParamDescCrc != fparamDescCrc)
                {
                    if (Help.CheckSchemaVersion(fParamRecord, SupportedGsdmlVersion))
                    {
                        // "'F_ParameterRecordDataItem/@F_ParamDescCRC': The CRC over all F-parameters is {0}, but should be {1}."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010128_2"), fparamDescCrc, calculatedFParamDescCrc);
                        string xpath = Help.GetXPath(node);
                        var xli = (IXmlLineInfo)node;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010128_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Validate the F_IO_StructureDescCRC.
        /// </summary>
        protected virtual bool CheckCn_0X00010129()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodItem in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (!pRofIsafeSupported)
                {
                    continue;
                }
                // Get the F_IO_StructureDescCRC from Xml file, if available
                var nodeFioStructureDescCrc = submodItem.Element(NamespaceGsdDef + Elements.s_IoData)?.Attribute(Attributes.s_FIOStructureDescCrc);

                // If the CRC is not given in Xml file, no need to calculate the CRC
                if (nodeFioStructureDescCrc == null)
                    continue;

                UInt32 fioStructureDescCrc = XmlConvert.ToUInt32(nodeFioStructureDescCrc.Value);

                // Find the F_IO_StructureDescVersion
                byte fioStrucDescVer = 1;
                var ioData = submodItem.Element(NamespaceGsdDef + Elements.s_IoData);
                string strFioStrucDescVer = Help.GetAttributeValueFromXElement(ioData, Attributes.s_FIOStructureDescVersion);
                if (!string.IsNullOrEmpty(strFioStrucDescVer))
                    fioStrucDescVer = XmlConvert.ToByte(strFioStrucDescVer);

                // Get input and output data items
                var inputDataItems = GetInputDataItems(submodItem);
                var outputDataItems = GetOutputDataItems(submodItem);

                // Input:

                // Find IN_ADDRESS_RANGE
                ushort inAddressRange = (ushort)GetAddressRange(inputDataItems);

                // Find COUNT_PS_INPUT_BYTES_COMPOSITE
                ushort inBytesComposite = (ushort)GetBytesComposite(inputDataItems);

                // Find COUNT_PS_INPUT_BYTES_U8_U8
                ushort inBytesU8U8 = (ushort)GetBytesU8U8(inputDataItems);

                // Find COUNT_PS_INPUT_CHANNELS_BOOL_MAX
                ushort inChannelsBoolMax = (ushort)GetChannelsBoolMax(inputDataItems);

                // Find COUNT_PS_INPUT_BYTES_BOOL_MAX
                ushort inBytesBoolMax = (ushort)GetBytesBoolMax(inputDataItems);

                // Find COUNT_PS_INPUT_CHANNELS_INT
                ushort inChannelsInt = (ushort)GetChannelsAnalog(inputDataItems, Enums.s_Integer16);

                // Find COUNT_PS_INPUT_CHANNELS_DINT
                ushort inChannelsDInt = (ushort)GetChannelsAnalog(inputDataItems, Enums.s_Integer32);

                // Find COUNT_PS_INPUT_CHANNELS_REAL
                ushort inChannelsReal = (ushort)GetChannelsAnalog(inputDataItems, Enums.s_Float32);

                // Output:

                // Find OUT_ADDRESS_RANGE
                ushort outAddressRange = (ushort)GetAddressRange(outputDataItems);

                // Find COUNT_PS_OUTPUT_BYTES_COMPOSITE
                ushort outBytesComposite = (ushort)GetBytesComposite(outputDataItems);

                // Find COUNT_PS_OUTPUT_BYTES_U8_U8
                ushort outBytesU8U8 = (ushort)GetBytesU8U8(outputDataItems);

                // Find COUNT_PS_OUTPUT_CHANNELS_BOOL_MAX
                ushort outChannelsBoolMax = (ushort)GetChannelsBoolMax(outputDataItems);

                // Find COUNT_PS_OUTPUT_BYTES_BOOL_MAX
                ushort outBytesBoolMax = (ushort)GetBytesBoolMax(outputDataItems);

                // Find COUNT_PS_OUTPUT_CHANNELS_INT
                ushort outChannelsInt = (ushort)GetChannelsAnalog(outputDataItems, Enums.s_Integer16);

                // Find COUNT_PS_OUTPUT_CHANNELS_DINT
                ushort outChannelsDInt = (ushort)GetChannelsAnalog(outputDataItems, Enums.s_Integer32);

                // Find COUNT_PS_OUTPUT_CHANNELS_REAL
                ushort outChannelsReal = (ushort)GetChannelsAnalog(outputDataItems, Enums.s_Float32);

                uint calculatedFioStructureDescCrc;

                if (fioStrucDescVer == 1)
                {
                    Crc16 calcFioStrucDescCrc = new();

                    // Init the F_ParamDescCRC
                    calcFioStrucDescCrc.InitChecksum();

                    // Collect the bytes for checksum
                    byte[] data = new byte[]
                                      {
                                              (byte)(inAddressRange >> 8), (byte)(inAddressRange),
                                              (byte)(inBytesComposite >> 8), (byte)(inBytesComposite),
                                              (byte)(inChannelsBoolMax >> 8), (byte)(inChannelsBoolMax),
                                              (byte)(inBytesBoolMax >> 8), (byte)(inBytesBoolMax),
                                              (byte)(inChannelsInt >> 8), (byte)(inChannelsInt),
                                              (byte)(inChannelsReal >> 8), (byte)(inChannelsReal),
                                              (byte)(outAddressRange >> 8), (byte)(outAddressRange),
                                              (byte)(outBytesComposite >> 8), (byte)(outBytesComposite),
                                              (byte)(outChannelsBoolMax >> 8), (byte)(outChannelsBoolMax),
                                              (byte)(outBytesBoolMax >> 8), (byte)(outBytesBoolMax),
                                              (byte)(outChannelsInt >> 8), (byte)(outChannelsInt),
                                              (byte)(outChannelsReal >> 8), (byte)(outChannelsReal),
                                      };
                    calcFioStrucDescCrc.UpdateChecksum(data);

                    // Finish the checksum process, returning the CRC
                    calculatedFioStructureDescCrc = calcFioStrucDescCrc.FinishChecksum(true);
                }
                else
                {
                    Crc32 calcFioStrucDescCrc = new();

                    // Init the F_ParamDescCRC
                    calcFioStrucDescCrc.InitChecksum();

                    // Collect the bytes for checksum
                    byte[] data = new byte[]
                    {
                        fioStrucDescVer,

                        (byte)(inAddressRange >> 8), (byte)(inAddressRange),
                        (byte)(inBytesComposite >> 8), (byte)(inBytesComposite),
                        (byte)(inBytesU8U8 >> 8), (byte)(inBytesU8U8),
                        (byte)(inChannelsBoolMax >> 8), (byte)(inChannelsBoolMax),
                        (byte)(inBytesBoolMax >> 8), (byte)(inBytesBoolMax),
                        (byte)(inChannelsInt >> 8), (byte)(inChannelsInt),
                        (byte)(inChannelsDInt >> 8), (byte)(inChannelsDInt),
                        (byte)(inChannelsReal >> 8), (byte)(inChannelsReal),

                        (byte)(outAddressRange >> 8), (byte)(outAddressRange),
                        (byte)(outBytesComposite >> 8), (byte)(outBytesComposite),
                        (byte)(outBytesU8U8 >> 8), (byte)(outBytesU8U8),
                        (byte)(outChannelsBoolMax >> 8), (byte)(outChannelsBoolMax),
                        (byte)(outBytesBoolMax >> 8), (byte)(outBytesBoolMax),
                        (byte)(outChannelsInt >> 8), (byte)(outChannelsInt),
                        (byte)(outChannelsDInt >> 8), (byte)(outChannelsDInt),
                        (byte)(outChannelsReal >> 8), (byte)(outChannelsReal),
                    };

                    calcFioStrucDescCrc.UpdateChecksum(data);

                    // Finish the checksum process, returning the CRC
                    calculatedFioStructureDescCrc = calcFioStrucDescCrc.FinishChecksum();
                }

                // Check the F_IO_StructureDescCRC from Xml file against the calculated CRC
                if (calculatedFioStructureDescCrc == fioStructureDescCrc)
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "'(Virtual)SubmoduleItem/IOData/@F_IO_StructureDescCRC': The CRC over all IOData is {0}, but should be {1}."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010129_1"), fioStructureDescCrc, calculatedFioStructureDescCrc);
                string xpath = Help.GetXPath(nodeFioStructureDescCrc);
                IXmlLineInfo xli = nodeFioStructureDescCrc;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010129_1");


            }

            return true;
        }

        /// <summary>
        /// Check IsRIOforFAsupported 
        /// 
        /// </summary>
        /// <returns>True, if RIOforFA supported.</returns>
        protected virtual bool IsRioForFaSupported(IList<XElement> dataItems)
        {
            return false;
        }

        /// <summary>
        /// IsWrongOrder
        /// 
        /// </summary>
        /// <returns>True, if datatypes with wrong order found.</returns>
        protected virtual bool IsWrongOrder(IList<XElement> dataItems)
        {
            if (null == dataItems)
                return false;

            const uint StepFloat32Unsigned8 = 0;
            const uint StepUnsigned8Unsigned8 = 1;
            const uint StepUnsigned81632 = 2;
            const uint StepInteger16 = 3;
            const uint StepInteger32 = 4;
            const uint StepFloat32 = 5;
            const uint StepFMessageTrailerxByte = 6;

            uint currentStep = StepFloat32Unsigned8;
            bool wrongOrder = false;
            bool fMessageTrailerxByteFound = false;

            foreach (var dataItem in dataItems)
            {
                if (wrongOrder)
                    break;

                var lineInfo = (IXmlLineInfo)dataItem;

                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                CreateReport0X00010131_5(currentStep, StepFMessageTrailerxByte, dataType, dataItem, lineInfo);
                fMessageTrailerxByteFound = CreateReport0x00010131_6(dataType, fMessageTrailerxByteFound, dataItem, lineInfo, StepFMessageTrailerxByte, ref currentStep);

                currentStep = CreateReport0x00010131_7(dataType, currentStep, StepFloat32Unsigned8, StepUnsigned8Unsigned8, StepUnsigned81632, StepInteger16, StepInteger32, StepFloat32, dataItem, lineInfo, ref wrongOrder);

            }
            return wrongOrder;
        }

        private uint CreateReport0x00010131_7(
       string dataType,
       uint currentStep,
       uint StepFloat32Unsigned8,
       uint StepUnsigned8Unsigned8,
       uint StepUnsigned81632,
       uint StepInteger16,
       uint StepInteger32,
       uint StepFloat32,
       XObject dataItem,
       IXmlLineInfo lineInfo,
       ref bool wrongOrder)
        {
            switch (dataType)
            {
                case Enums.s_Float32Unsigned8:
                case Enums.s_Float32Status8:
                    {
                        if (currentStep > StepFloat32Unsigned8)
                            wrongOrder = true;
                        currentStep = StepFloat32Unsigned8;
                        break;
                    }
                case Enums.s_Unsigned8Unsigned8:
                    {
                        if (currentStep > StepUnsigned8Unsigned8)
                            wrongOrder = true;
                        currentStep = StepUnsigned8Unsigned8;
                        break;
                    }
                case Enums.s_Unsigned8:
                case Enums.s_Unsigned16:
                case Enums.s_Unsigned32:
                    {
                        if (currentStep > StepUnsigned81632)
                            wrongOrder = true;
                        currentStep = StepUnsigned81632;
                        break;
                    }
                case Enums.s_Integer16:
                    {
                        if (currentStep > StepInteger16)
                            wrongOrder = true;
                        currentStep = StepInteger16;
                        break;
                    }
                case Enums.s_Integer32:
                    {
                        if (currentStep > StepInteger32)
                            wrongOrder = true;
                        currentStep = StepInteger32;
                        break;
                    }
                case Enums.s_Float32:
                    {
                        if (currentStep > StepFloat32)
                            wrongOrder = true;
                        currentStep = StepFloat32;
                        break;
                    }
                default:
                    {
                        if (dataType != Enums.s_FMessageTrailer4Byte && dataType != FMessageTrailer5Byte)
                        {
                            if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                            {
                                // "The 'DataType' "{0}" is not allowed for PROFIsafe."
                                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010131_7"), dataType);
                                string xpath = Help.GetXPath(dataItem);
                                Store.CreateAndAnnounceReport(
                                    ReportTypes.GSD_RT_Error,
                                    lineInfo.LineNumber,
                                    lineInfo.LinePosition,
                                    msg,
                                    xpath,
                                    ReportCategories.TypeSpecific,
                                    "0x00010131_7");
                            }
                        }

                        break;
                    }

            }
            return currentStep;
        }





        private bool CreateReport0x00010131_6(
            string dataType,
            bool fMessageTrailerxByteFound,
            XObject dataItem,
            IXmlLineInfo lineInfo,
            uint StepFMessageTrailerxByte,
            ref uint currentStep)
        {
            if (dataType != Enums.s_FMessageTrailer4Byte
            && dataType != FMessageTrailer5Byte)
            {
                return fMessageTrailerxByteFound;
            }
            // Search for double definition of F_MessageTrailerXByte in DataItems
            if (fMessageTrailerxByteFound)
            {
                if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                {
                    // "For F-Submodules in 'IOData' the 'DataType' "F_MessageTrailer4Byte" or
                    // "F_MessageTrailer5Byte" must not be double defined."
                    string msg = Help.GetMessageString("M_0x00010131_6");
                    string xpath = Help.GetXPath(dataItem);
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        lineInfo.LineNumber,
                        lineInfo.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010131_6");
                }
            }

            currentStep = StepFMessageTrailerxByte;
            return true;
        }

        private void CreateReport0X00010131_5(
           uint currentStep,
           uint StepFMessageTrailerxByte,
           string dataType,
           XObject dataItem,
           IXmlLineInfo lineInfo)
        {
            if (currentStep != StepFMessageTrailerxByte
            || dataType == Enums.s_FMessageTrailer4Byte
            || dataType == FMessageTrailer5Byte)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
            {
                return;

            }
            // "For F-Submodules in 'IOData' a 'DataItem' with 'DataType' equal "F_MessageTrailer4Byte" or
            // "F_MessageTrailer5Byte" must be the last one, and not "{0}"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010131_5"), dataType);
            string xpath = Help.GetXPath(dataItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_5");

        }

        /// <summary>
        /// IsHoleInBooleanChannels
        /// Checks on PROFIsafe submodules, the list of boolean channels,
        /// which is packed into DataItems of DataType="UnsignedXX" with
        /// UseAsBits="true", may not have holes in it.
        /// 
        /// UseAsBits="true" is not checked here, but this is checked already elsewhere.
        /// 
        /// </summary>
        /// <returns>True, if hole in boolean channels found.</returns>
        protected virtual bool IsHoleInBooleanChannels(IList<XElement> dataItems)
        {
            if (null == dataItems)
                return false;

            bool holeFound = false;
            List<XElement> dataItemsUnsignedXx = new();

            // Only Unsigned8, Unsigned16, Unsigned32 can be used as boolean channels.
            // Save the relevant data items to a list
            IsHoleInBooleanChannels_GetDataItemsUnsignedXX(dataItems, dataItemsUnsignedXx);


            int noOfItem = 0;
            foreach (var dataItem in dataItemsUnsignedXx)
            {
                noOfItem++;

                if (holeFound)
                    break;


                // Only boolean channels with BitDataItems given are relevant
                var bitDataItems = dataItem.XPathSelectElements("./gsddef:BitDataItem", Nsmgr).ToList();
                if (bitDataItems.Count == 0)
                    continue;

                List<UInt16> bitOffsets = new();
                foreach (var bitDataItem in bitDataItems)
                    bitOffsets.Add(XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(bitDataItem, Attributes.s_BitOffset)));

                // Get the data type of item
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // Get the number of bits in the data type
                int dataTypeLen = GetDataTypeChannelSize(dataType) * 8;

                // Are as many BitDataItems given as the number of bits in the data type?
                if (dataTypeLen == bitDataItems.Count)
                    continue;

                // Now we found a hole
                if (FindHoleinDataItemsUnsignedXX(noOfItem, dataItemsUnsignedXx, dataTypeLen, bitOffsets, ref holeFound))
                {
                    break;
                }
            }

            return holeFound;
        }

        private static bool FindHoleinDataItemsUnsignedXX(
             int noOfItem,
             ICollection dataItemsUnsignedXx,
             int dataTypeLen,
             ICollection<ushort> bitOffsets,
             ref bool holeFound)
        {
            // This is not OK if it is not at the very end
            if (noOfItem != dataItemsUnsignedXx.Count)
            {
                holeFound = true;
                return true;
            }

            // This is OK if it has less channels in its last byte, in the most significant bits

            // If it is not last byte, all bits must be filled
            if (dataTypeLen > 8)
            {
                for (UInt16 i = 8; i < dataTypeLen; i++)
                {
                    if (bitOffsets.Contains(i))
                    {
                        continue;
                    }
                    holeFound = true;
                    break;

                }
            }

            // If it is the last byte, all bits must be filled from 0..somewhere
            UInt16 j;
            for (j = 0; j < 8; j++)
            {
                if (!bitOffsets.Contains(j))
                    break;
            }

            for (; j < 8; j++)
            {
                if (!bitOffsets.Contains(j))
                {
                    continue;
                }
                holeFound = true;
                break;


            }
            return false;
        }

        private static void IsHoleInBooleanChannels_GetDataItemsUnsignedXX(IList<XElement> dataItems, ICollection<XElement> dataItemsUnsignedXx)
        {
            foreach (var dataItem in dataItems)
            {
                // Get the data type of item
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // It is allowed to have holes in the BitDataItems that describe the Q bits.
                // Therefore these DataItems must not be checked and must be excluded from list
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                // Save items with DataType "UnsignedXX"

                if ((dataType == Enums.s_Unsigned8
                    || dataType == Enums.s_Unsigned16
                    || dataType == Enums.s_Unsigned32) && !subordinate)
                    dataItemsUnsignedXx.Add(dataItem);
            }
        }

        /// <summary>
        /// Check number: CN_0x00010131
        /// For F-Submodules, the 'Output/DataItem' elements have to be ordered according to their 'DataType'.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010131()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodItem in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (!pRofIsafeSupported)
                {
                    continue;
                }
                var lineInfo = (IXmlLineInfo)submodItem;

                // Get input and output data items
                var inputDataItems = GetInputDataItems(submodItem);
                var outputDataItems = GetOutputDataItems(submodItem);

                // For RIOforFA other rules apply concerning the order of the data types.
                if (!IsRioForFaSupported(inputDataItems))
                {
                    CreateReport0X00010131_1(inputDataItems, submodItem, lineInfo);
                    CreateReport0X00010131_2(outputDataItems, submodItem, lineInfo);


                }

                // Find address ranges
                int inAddressRange = GetChannelsBoolMax(inputDataItems);
                int outAddressRange = GetChannelsBoolMax(outputDataItems);
                CreateReport0X00010131_3(inAddressRange, submodItem, lineInfo);

                CreateReport0X00010131_4(outAddressRange, submodItem, lineInfo);

                CreateReport0X00010131_8(inputDataItems, submodItem, lineInfo);

                CreateReport0X00010131_9(outputDataItems, submodItem, lineInfo);



            }
            return true;
        }
        private void CreateReport0X00010131_9(IList<XElement> outputDataItems, XObject submodItem, IXmlLineInfo lineInfo)
        {

            if (!IsHoleInBooleanChannels(outputDataItems))
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules, the list of boolean channels on 'Output/DataItem' elements with
            // DataType="UnsignedXX" and UseAsBits="true", may not have holes in it."
            string msg = Help.GetMessageString("M_0x00010131_9");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportType_0X000101319,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_9");
        }
        private void CreateReport0X00010131_8(IList<XElement> inputDataItems, XObject submodItem, IXmlLineInfo lineInfo)
        {
            if (!IsHoleInBooleanChannels(inputDataItems))
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules, the list of boolean channels on 'Input/DataItem' elements with
            // DataType="UnsignedXX" and UseAsBits="true", may not have holes in it."
            string msg = Help.GetMessageString("M_0x00010131_8");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportType_0X000101318,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_8");
        }
        private void CreateReport0X00010131_4(int outAddressRange, XObject submodItem, IXmlLineInfo lineInfo)
        {

            if (outAddressRange <= 64)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "The PROFIsafe specification requires hosts to support at least 64 boolean output channels.
            // This submodule exceeds this number and thus may not work with all PROFIsafe hosts."
            string msg = Help.GetMessageString("M_0x00010131_4");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_4");
        }

        private void CreateReport0X00010131_3(int inAddressRange, XObject submodItem, IXmlLineInfo lineInfo)
        {
            if (inAddressRange <= 64)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "The PROFIsafe specification requires hosts to support at least 64 boolean input channels.
            // This submodule exceeds this number and thus may not work with all PROFIsafe hosts."
            string msg = Help.GetMessageString("M_0x00010131_3");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_3");
        }



        private void CreateReport0X00010131_2(IList<XElement> outputDataItems, XObject submodItem, IXmlLineInfo lineInfo)
        {

            if (!IsWrongOrder(outputDataItems))
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules, the 'Output/DataItem' elements have to be ordered according to their 'DataType'."
            string msg = Help.GetMessageString("M_0x00010131_2");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_2");
        }
        private void CreateReport0X00010131_1(IList<XElement> inputDataItems, XObject submodItem, IXmlLineInfo lineInfo)
        {
            if (!IsWrongOrder(inputDataItems))
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submodItem, SupportedGsdmlVersion))
            {
                return;
            }
            // "For F-Submodules, the 'Input/DataItem' elements have to be ordered according to their 'DataType'."
            string msg = Help.GetMessageString("M_0x00010131_1");
            string xpath = Help.GetXPath(submodItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010131_1");

        }

        /// <summary>
        /// Check number: CN_0x00010132
        /// 'Visible'="false" does not only hide the F-parameter 'F_Check_iPar', but also sets its value to "NoCheck"
        /// 'Visible'="false" does not only hide the F-parameter 'F_CRC_Length', but also sets its value to "3-Byte-CRC"
        /// 'Visible'="false" does not only hide the F-parameter 'F_Block_ID', but also sets its value to "0"
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010132()
        {
            // Find all F_Parameter with Visible = "false"
            var allFPars =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_FCheckIPar ||
                            x.Name.LocalName == Elements.s_FCrcLength ||
                            x.Name.LocalName == Elements.s_FBlockID);
            allFPars = Help.TryRemoveXElementsUnderXsAny(allFPars, Nsmgr, Gsd);

            foreach (var fPar in allFPars)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fPar, Attributes.s_Visible);
                bool visible = false;
                if (!string.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                if (visible)
                    continue;

                var xli = (IXmlLineInfo)fPar;
                CreateReport0x00010132_1(fPar, xli, strVisible);
            }
            return true;
        }
        private void CreateReport0x00010132_1(XElement fPar,
            IXmlLineInfo xli, string strVisible)
        {
            if (fPar.Name.LocalName == Elements.s_FCheckIPar)
            {
                string defaultValue = Help.GetAttributeValueFromXElement(fPar, Attributes.s_DefaultValue);
                if (string.IsNullOrEmpty(defaultValue) || defaultValue == Enums.s_NoCheck)
                {
                    return;
                }
                if (!Help.CheckSchemaVersion(fPar, SupportedGsdmlVersion))
                {
                    return;
                }
                // "'Visible'="false" does not only hide the F-parameter 'F_Check_iPar', but also sets its value to "NoCheck",
                //  which is not the same as the DefaultValue. This is probably not intended."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010132_1"),
                    Elements.s_FCheckIPar,
                    Enums.s_NoCheck);
                string xpath = Help.GetXPath(fPar);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010132_1");


            }
            else if (fPar.Name.LocalName == Elements.s_FCrcLength)
            {
                string defaultValue = Help.GetAttributeValueFromXElement(fPar, Attributes.s_DefaultValue);
                if (string.IsNullOrEmpty(defaultValue) || defaultValue == Enums.s_ByteCrc3)
                {
                    return;
                }
                if (!Help.CheckSchemaVersion(fPar, SupportedGsdmlVersion))
                {
                    return;
                }
                // "'Visible'="false" does not only hide the F-parameter 'F_CRC_Length', but also sets its value to "3-Byte-CRC",
                //  which is not the same as the DefaultValue. This is probably not intended."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010132_1"),
                    Elements.s_FCrcLength,
                    Enums.s_ByteCrc3);
                string xpath = Help.GetXPath(fPar);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010132_1");


            }
            else if (fPar.Name.LocalName == Elements.s_FBlockID)
            {
                string strDefaultValue = Help.GetAttributeValueFromXElement(fPar, Attributes.s_DefaultValue);
                if (string.IsNullOrEmpty(strVisible) || string.IsNullOrEmpty(strDefaultValue))
                {
                    return;
                }

                int defaultValue = XmlConvert.ToInt16(strDefaultValue);
                if (defaultValue == 0)
                {
                    return;
                }
                if (!Help.CheckSchemaVersion(fPar, SupportedGsdmlVersion))
                {
                    return;
                }
                // "'Visible'="false" does not only hide the F-parameter 'F_Block_ID', but also sets its value to "0",
                //  which is not the same as the DefaultValue. This is probably not intended."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010132_1"), Elements.s_FBlockID, "0");
                string xpath = Help.GetXPath(fPar);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010132_1");

            }

        }



        /// <summary>
        /// Check number: CN_0x00010133
        /// If 'SupportedRT_Class' is "Class3" and/or 'SupportedRT_Classes' contains "RT_CLASS_3", 
        /// the modules pluggable with this DAP shall not carry PortSubmodules.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010133()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in nl)
            {
                var supportedRTClasses = GetRTClasses(interfaceSubmoduleItem);

                if (!supportedRTClasses.Contains("RT_CLASS_3"))
                    continue;

                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }

                var dap = interfaceSubmoduleItem.Parent.Parent;
                if (dap == null)
                {
                    continue;
                }
                var moduleItemRefs =
                    dap.Elements(NamespaceGsdDef + Elements.s_UseableModules)
                        .Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);
                foreach (var moduleItemRef in moduleItemRefs)
                {
                    string moduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                    PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                    if (null == module)
                        continue;

                    // Check for SystemDefinedSubmoduleList of modules at the DAP
                    ModuleRefToFixedPortDictionary.TryGetValue(moduleItemRef, out IList<XElement> fixedPortsOfModule);
                    if (fixedPortsOfModule != null && fixedPortsOfModule.Count != 0)
                    {
                        // "If 'SupportedRT_Class' is "Class3" and/or 'SupportedRT_Classes' contains "RT_CLASS_3", the modules pluggable with this DAP (ID = "{0}") shall not carry PortSubmodules."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010133_1"), Help.GetAttributeValueFromXElement(dap, Attributes.ID));
                        string xpath = Help.GetXPath(module);
                        IXmlLineInfo xli = module;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010133_1");
                    }

                    // Check all UseableSubmodules of modules at the DAP
                    ModuleRefToPluggablePortDictionary.TryGetValue(moduleItemRef, out IList<XElement> pluggablePortsOfModule);
                    if (pluggablePortsOfModule.Count != 0)
                    {
                        // "If 'SupportedRT_Class' is "Class3" and/or 'SupportedRT_Classes' contains "RT_CLASS_3", the modules pluggable with this DAP (ID = "{0}") shall not carry PortSubmodules."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010133_1"), Help.GetAttributeValueFromXElement(dap, Attributes.ID));
                        string xpath = Help.GetXPath(module);
                        var xli = (IXmlLineInfo)module;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010133_1");
                    }
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00010134
        /// Check the usage of @UseAsBits for submodules without PROFIsafe.
        /// PROFIsafe submodules are checked in CN_0x00010122.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010134()
        {
            // Find all submodules with PROFIsafeSupported = "false"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodItem in allSubmodules)
            {
                string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);

                if (pRofIsafeSupported)
                    continue;

                var inputDataItems = GetInputDataItems(submodItem);
                var outputDataItems = GetOutputDataItems(submodItem);

                // Check the usage of UseAsBits in dependency with the DataType for all DataItems
                TestUseAsBits(inputDataItems);
                TestUseAsBits(outputDataItems);
            }

            return true;
        }


        // Some checks for UseAsBits in dependency to DataType:
        // For DataType not equal UnsignedX, OctetString, V2 UseAsBits must be false or not existing.
        private void TestUseAsBits(IList<XElement> dataItems)
        {
            if (dataItems == null)
                return;

            foreach (var dataItem in dataItems)
            {
                bool valueUseAsBits = false;
                string useAsBitsStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_UseAsBits);
                if (!string.IsNullOrEmpty(useAsBitsStr))
                    valueUseAsBits = XmlConvert.ToBoolean(useAsBitsStr);
                string valueDataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                var lineInfo = (IXmlLineInfo)dataItem;

                if (valueUseAsBits && (valueDataType != Enums.s_Unsigned8 && valueDataType != Enums.s_Unsigned16 && valueDataType != Enums.s_Unsigned32 &&
                                       valueDataType != Enums.s_Unsigned64 && valueDataType != Enums.s_OctetString && valueDataType != Enums.s_V2))
                {
                    if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                    {
                        // "For a 'DataType' other than "Unsigned8", "Unsigned16" or "Unsigned32" or "Unsigned64" or "OctetString" or "V2" 'UseAsBits' shall not be set."
                        string msg = Help.GetMessageString("M_0x00010134_1");
                        string xpath = Help.GetXPath(dataItem);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_MinorError, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00010134_1");
                    }
                }
            }

        }


        /// <summary>
        /// Check number: CN_0x00010135
        /// PROFINET Device needs at least one port. This means there must be at least one PortSubmoduleItem configurable with each DAP.
        /// To be checked:
        /// For each DAP which is PROFINET Version > 1.0 (i.e. where SystemDefinedSubmoduleList is present), there must be either
        /// - a fixed PortSubmoduleItem at the DAP
        /// - a PortSubmoduleItem pluggable with the DAP
        /// - a Module pluggable with the DAP with a fixed PortSubmoduleItem
        /// - a Module pluggable with the DAP with a PortSubmoduleItem pluggable with the Module
        /// => All these ports are collected in the DapToPortDictionary, if available
        /// 
        /// If no port is available from the DapToPortDictionary, an error is raised.
        /// 
        /// TFS #2567698:
        /// The same is also valid vice versa: For each DAP with PROFINET Version = 1.0 no PortSubmoduleItem must be configurable with this DAP.
        /// 
        /// Note: Pluggable PortSubmoduleItems were introduced with GSDML V2.25.
        ///       This check look already for pluggable PortSubmoduleItems to make it simpler,
        ///       but in GSDML V2.0 no pluggable PortSubmoduleItems will be found.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010135()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems);

                // Check, if DAP has SystemDefinedSubmoduleList => PROFINET Version > 1.0
                var systemDefinedSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList);
                if (systemDefinedSubmoduleList != null)
                {
                    if (portSubmoduleItems == null || portSubmoduleItems.Count == 0)
                    {
                        if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                        {
                            // "No PortSubmoduleItem is configurable with this DAP."
                            string msg = Help.GetMessageString("M_0x00010135_1");
                            string xpath = Help.GetXPath(dap);
                            var lineInfo = (IXmlLineInfo)dap;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber,
                                lineInfo.LinePosition, msg, xpath,
                                ReportCategories.TypeSpecific, "0x00010135_1");
                        }
                    }
                }
                else     // PROFINET Version == 1.0
                {
                    if (portSubmoduleItems != null && portSubmoduleItems.Count != 0)
                    {
                        // "A DAP with PROFINET Version = 1.0 must not have any configurable PortSubmoduleItem."
                        string msg = Help.GetMessageString("M_0x00010135_2");
                        string xpath = Help.GetXPath(dap);
                        var lineInfo = (IXmlLineInfo)dap;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_MinorError, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00010135_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010136
        /// 
        /// TFS #4051208:
        /// The InterfaceSubmoduleItem attribute SubslotNumber was added in GSDML V2.0 with a
        /// fixed (correct) value of 32768.
        /// 
        /// Other values (0x8000, 0x8100, 0x8200, .. 0x8F00) were allowed starting with GSDML V2.1.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010136()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                // InterfaceSubmoduleItem
                UInt16 interfaceSubslot = 0x8000;    // Default value
                XElement ism = dap.XPathSelectElement(".//gsddef:InterfaceSubmoduleItem", Nsmgr);
                if (ism != null)
                {
                    string strInterfaceSubslot = Help.GetAttributeValueFromXElement(ism, "SubslotNumber");
                    if (!string.IsNullOrEmpty(strInterfaceSubslot))
                    {
                        interfaceSubslot = XmlConvert.ToUInt16(strInterfaceSubslot);
                    }
                    if (interfaceSubslot != 0x8000)
                    {
                        if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                        {
                            // "For GSDML V2.0 'InterfaceSubmoduleItem/@SubslotNumber' must have a fixed (correct) value of 32768."
                            string msg = Help.GetMessageString("M_0x00010136_1");
                            string xpath = Help.GetXPath(ism);
                            var lineInfo = (IXmlLineInfo)ism;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x00010136_1");
                        }
                    }
                }
            }

            return true;
        }
        #endregion
    }
}

