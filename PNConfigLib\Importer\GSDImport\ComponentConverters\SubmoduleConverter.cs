/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: SubmoduleConverter.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;

using GSDI;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.GSDImport.Helper;

using DataItem = PNConfigLib.Gsd.Interpreter.Common.DataItem;
using ParameterRecordData = PNConfigLib.Gsd.Interpreter.Common.ParameterRecordData;
using VirtualSubmodule = PNConfigLib.Gsd.Interpreter.Common.VirtualSubmodule;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal sealed class SubmoduleConverter
    {
        private SubmoduleCatalog m_Submodule;

        private VirtualSubmodule m_SubmoduleItem;

        public SubmoduleConverter(VirtualSubmodule submoduleItem)
        {
            m_SubmoduleItem = submoduleItem;
        }

        public SubmoduleCatalog Convert(string fileName)
        {
            m_Submodule = new SubmoduleCatalog();
            m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GSDFileName, fileName);

            AddAttributes();
            AddDataRecordTransferSequenceAttribute();

            m_Submodule.ParameterRecordDataList = m_SubmoduleItem.ParameterRecordData;
            m_Submodule.IOData = m_SubmoduleItem.IOData;
            return m_Submodule;
        }

        private static uint SizeInByte(uint value)
        {
            uint nLenInBit = value;

            if (nLenInBit == 0)
            {
                return value;
            }

            if (nLenInBit % 8 != 0)
            {
                nLenInBit += 8 - nLenInBit % 8;
            }

            return nLenInBit / 8;
        }

        /// <summary>
        /// Adds the attribute DataRecordTransferSequenceAttribute. This attribute
        /// is only neccessary if there is a transfer sequence defined for the data records
        /// which is different from the order of the indexes.
        /// </summary>
        private void AddDataRecordTransferSequenceAttribute()
        {
            bool attributeNeccessary = false;

            IList<ParameterRecordData> prmRecordList = null;

            if (m_SubmoduleItem.ParameterRecordData != null)
            {
                prmRecordList = new List<ParameterRecordData>();
                foreach (ParameterRecordData parameterRecordData in m_SubmoduleItem.ParameterRecordData)
                {
                    prmRecordList.Add(parameterRecordData);
                }
            }

            if (prmRecordList != null)
            {
                if (prmRecordList[0].TransferSequence != 0)
                {
                    attributeNeccessary = true;
                }
            }
            else if (m_SubmoduleItem.FParameterRecordData != null)
            {
                if (m_SubmoduleItem.FParameterRecordData.TransferSequence != 0)
                {
                    attributeNeccessary = true;
                }
            }

            if (attributeNeccessary)
            {
                StringBuilder attributeValue = new StringBuilder(50);

                ICollection<int> indexList = GetPrmTransferSequence(prmRecordList);
                if (indexList.Count > 1)
                {
                    // attribute does only make sense if there is more than one data record
                    foreach (int index in indexList)
                    {
                        attributeValue.Append(System.Convert.ToString(index, CultureInfo.InvariantCulture));
                        attributeValue.Append(";");
                    }

                    // remove last ';'
                    if (attributeValue.Length > 0)
                    {
                        attributeValue.Remove(attributeValue.Length - 1, 1);
                    }

                    m_Submodule.AttributeAccess.AddAnyAttribute<string>(
                        InternalAttributeNames.DataRecordTransferSequence,
                        attributeValue.ToString());
                }
            }
        }

        private void AddAttributes()
        {
            AddGsdIdAttribute();
            AddAlternativeTypeIdAttribute();
            AddPNSubmoduleIdentNumberAttribute();
            AddModelNameAttribute();
            AddIoTypeAttribute();
            AddSignalTypeAttribute();
            AddInAddressRangeAttribute();
            AddOutAddressRangeAttribute();

            AddPNApiAttribute();
            AddCatalogInfoVariables();

            ConvertIsochroneAttributes();
        }

        private void ConvertIsochroneAttributes()
        {
            if (m_SubmoduleItem.IsochroneMode != null)
            {
                m_Submodule.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.PnIsoT_IO_BASE, m_SubmoduleItem.IsochroneMode.T_IO_Base);
                m_Submodule.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIsoT_DC_BASE, m_SubmoduleItem.IsochroneMode.T_DC_Base);
                m_Submodule.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIsoT_DC_MAX, m_SubmoduleItem.IsochroneMode.T_DC_Max);
                m_Submodule.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIsoT_DC_MIN, m_SubmoduleItem.IsochroneMode.T_DC_Min);
                m_Submodule.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.PnIsoT_IO_InputMin, m_SubmoduleItem.IsochroneMode.T_IO_InputMin);
                m_Submodule.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.PnIsoT_IO_OutputMin, m_SubmoduleItem.IsochroneMode.T_IO_OutputMin);
                m_Submodule.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIsochroneModeRequired, m_SubmoduleItem.IsochroneMode.IsIsochroneModeRequired);
                m_Submodule.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.ClockSyncMode, m_SubmoduleItem.IsochroneMode.IsIsochroneModeRequired);
            }
        }

        private void AddCatalogInfoVariables()
        {
            string typeName = string.Empty;
            string invariantTypeName = string.Empty;

            if (m_SubmoduleItem.Info != null && m_SubmoduleItem.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo moduleInfo)
            {
                // Description
                string description = Converter.GetTextFromTextId(moduleInfo.InfoTextID);
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Description, description);

                // OrderNumber
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.OrderNumber, moduleInfo.OrderNumber);

                // FwVersion
                string fwVersion = moduleInfo.SoftwareRelease;
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.FwVersion, fwVersion);

                // HwVersion
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.HwVersion, moduleInfo.HardwareRelease);

                // TypeName
                typeName = Converter.GetTextFromTextId(moduleInfo.NameTextID);
                if (string.IsNullOrEmpty(typeName))
                {
                    typeName = moduleInfo.OrderNumber;
                }
                if (!string.IsNullOrEmpty(typeName))
                {
                    m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.TypeName, typeName);
                }

                invariantTypeName = typeName;
            }

            if (!string.IsNullOrEmpty(typeName))
            {
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.TypeName,
                    m_SubmoduleItem.GsdID);
            }

            // InvariantTypeName
            if (string.IsNullOrEmpty(invariantTypeName))
            {
                invariantTypeName = m_SubmoduleItem.GsdID;
            }
            m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.InvariantTypeName, invariantTypeName);
        }

        private void AddPNApiAttribute()
        {
            m_Submodule.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnAPI, m_SubmoduleItem.API);

        }

        private void AddOutAddressRangeAttribute()
        {
            uint submoduleOutputLength = m_SubmoduleItem.IOData.OutputLength;
            int outAddressRange = (int)SizeInByte(submoduleOutputLength);
            if ((outAddressRange > 0)
                || m_SubmoduleItem.IsPROFIsafeSupported)
            {
                m_Submodule.AttributeAccess.AddAnyAttribute<int>(
                    InternalAttributeNames.OutAddressRange,
                    outAddressRange);
            }
        }

        private void AddInAddressRangeAttribute()
        {
            uint submoduleInputLength = m_SubmoduleItem.IOData.InputLength;
            int inAddressRange = (int)SizeInByte(submoduleInputLength);
            if ((inAddressRange > 0)
                || m_SubmoduleItem.IsPROFIsafeSupported)
            {
                m_Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.InAddressRange, inAddressRange);
            }
        }

        private void AddSignalTypeAttribute()
        {
            // SignalType
            int signalType = 2;
            if (m_SubmoduleItem.IOData.InputLength > 0)
            {
                if (m_SubmoduleItem.IOData.InputDataItems.Length > 0)
                {
                    DataItem dataItem = (DataItem)m_SubmoduleItem.IOData.InputDataItems.GetValue(0);
                    if (dataItem.UseAsBits)
                    {
                        signalType = 1;
                    }
                }
            }
            else if (m_SubmoduleItem.IOData.OutputLength > 0)
            {
                if (m_SubmoduleItem.IOData.OutputDataItems.Length > 0)
                {
                    DataItem dataItem = (DataItem)m_SubmoduleItem.IOData.OutputDataItems.GetValue(0);
                    if (dataItem.UseAsBits)
                    {
                        signalType = 1;
                    }
                }
            }
            m_Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.SignalType, signalType);
        }

        private void AddIoTypeAttribute()
        {
            uint submoduleInputLength = m_SubmoduleItem.IOData.InputLength;
            uint submoduleOutputLength = m_SubmoduleItem.IOData.OutputLength;
            int ioType = 0;
            if (submoduleInputLength > 0)
            {
                ioType = ioType | 0x0001;
            }
            if (submoduleOutputLength > 0)
            {
                ioType = ioType | 0x0002;
            }
            if (ioType == 0)
            {
                ioType = 0x0040;
            }
            m_Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.IoType, ioType);
        }

        private void AddModelNameAttribute()
        {
            if (m_SubmoduleItem.Info != null && m_SubmoduleItem.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo moduleInfo)
            {
                if (!string.IsNullOrEmpty(moduleInfo.OrderNumber))
                {
                    m_Submodule.AttributeAccess.AddAnyAttribute<string>(
                        InternalAttributeNames.ModelName,
                        moduleInfo.OrderNumber);
                }
            }
        }

        private void AddPNSubmoduleIdentNumberAttribute()
        {
            // PNSubmoduleIdentNumber
            m_Submodule.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnSubmoduleIdentNumber,
                m_SubmoduleItem.IdentNumber);
        }
        
        private void AddGsdIdAttribute()
        {
            m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GsdId, m_SubmoduleItem.GsdID);
        }

          
        private void AddAlternativeTypeIdAttribute()
        {
            // AlternativeTypeId
            string submoduleComponentID = string.Format( 
                CultureInfo.InvariantCulture,
                "Submodule_{0}",
                m_SubmoduleItem.GsdID);
            string fileName = Path.GetFileName(Converter.XmlFilePath);
            if (fileName != null)
            {
                string alternativeTypeId = string.Format(
                    CultureInfo.InvariantCulture,
                    "GSD\\\\{0}\\\\{1}",
                    fileName.ToUpperInvariant(),
                    submoduleComponentID);
                m_Submodule.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.AlternativeTypeId, alternativeTypeId);
            }
        }

        private ICollection<int> GetPrmTransferSequence(IList<ParameterRecordData> parameterRecordDataList)
        {
            SortedList<int, int> transferSequence = new SortedList<int, int>();

            if (parameterRecordDataList == null)
            {
                return transferSequence.Values;
            }

            foreach (ParameterRecordData rec in parameterRecordDataList)
            {
                // the TransferSequence always starts with value "1". If the GSDML provider delivers
                // the value "0" there is no TransferSequence defined in the GSD file.
                if (rec.TransferSequence == 0)
                {
                    transferSequence.Add((int)rec.Index, (int)rec.Index);
                }
                else
                {
                    transferSequence.Add((int)rec.TransferSequence, (int)rec.Index);
                }
            }

            if (m_SubmoduleItem.FParameterRecordData != null)
            {
                if (m_SubmoduleItem.FParameterRecordData.TransferSequence == 0)
                {
                    transferSequence.Add(
                        (int)m_SubmoduleItem.FParameterRecordData.Index,
                        (int)m_SubmoduleItem.FParameterRecordData.Index);
                }
                else
                {
                    transferSequence.Add(
                        (int)m_SubmoduleItem.FParameterRecordData.TransferSequence,
                        (int)m_SubmoduleItem.FParameterRecordData.Index);
                }
            }
            return transferSequence.Values;
        }
    }
}