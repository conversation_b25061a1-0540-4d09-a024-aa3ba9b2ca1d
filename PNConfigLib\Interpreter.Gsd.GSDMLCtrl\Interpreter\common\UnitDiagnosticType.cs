/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: UnitDiagnosticType.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
using System.Xml.Linq;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The UnitDiagnosticType assigns diagnostic values to manufacturer specific 
    /// status and error messages.
    /// </summary>
    public class UnitDiagnosticType :
        GsdObject,
        GSDI.IUnitDiagnosticType
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public UnitDiagnosticType()
        {
            m_UserStructureIdentifier = 0;
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
            m_Help = String.Empty;
            m_HelpTextID = String.Empty;

        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private ArrayList m_Refs;
        private uint m_UserStructureIdentifier;
        private string m_Name;
        private string m_NameTextID;
        private string m_Help;
        private string m_HelpTextID;


        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of RefData objects, which contains information about 
        /// a diagnostic data item within an alarm data.
        /// </summary>
        /// <remarks>The byte offset of RefData object refers to the additional 
        /// alarm info block of an alarm request PDU (the header information is 
        /// not included).</remarks>
        public virtual Array Refs =>
            this.m_Refs?.ToArray();

        /// <summary>
        /// Accesses the user structure identifier of the alarm request block.
        /// </summary>
        /// <remarks>The user structure identifier shall be in the range from 0 
        /// to 32767. 
        /// Within the list of unit diagnostic types, the user structure identifier
        /// shall be unique.</remarks>
        public UInt32 UserStructureIdentifier => this.m_UserStructureIdentifier;

        /// <summary>
        /// Accesses the language dependent name of a (Profile)UnitDiagTypeItem.
        /// </summary>
        public string Name => this.m_Name;

        /// <summary>
        /// Accesses the language independent text id of the name of a
        /// (Profile)UnitDiagTypeItem.
        /// </summary>
        public string NameTextID => this.m_NameTextID;

        /// <summary>
        /// Accesses the language dependent name of a (Profile)UnitDiagTypeItem.
        /// </summary>
        public string Help => this.m_Help;

        /// <summary>
        /// Accesses the language independent text id of the name of a
        /// (Profile)UnitDiagTypeItem.
        /// </summary>
        public string HelpTextID => this.m_HelpTextID;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldUserStructureIdentifier;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_UserStructureIdentifier = (uint)hash[member];

                member = Models.s_FieldRefs;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Refs = hash[member] as ArrayList;

                member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Name = hash[member] as string;

                member = Models.s_FieldNameTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_NameTextID = hash[member] as string;

                member = Models.s_FieldHelp;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Help = hash[member] as string;

                member = Models.s_FieldHelpTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_HelpTextID = hash[member] as string;

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectUnitDiagnosticType);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldUserStructureIdentifier, this.m_UserStructureIdentifier);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldRefs, this.m_Refs);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, this.m_NameTextID);
            Export.WriteStringProperty(ref writer, Models.s_FieldHelp, this.m_Help);
            Export.WriteStringProperty(ref writer, Models.s_FieldHelpTextId, this.m_HelpTextID);

            return true;
        }

        #endregion
    }
}


