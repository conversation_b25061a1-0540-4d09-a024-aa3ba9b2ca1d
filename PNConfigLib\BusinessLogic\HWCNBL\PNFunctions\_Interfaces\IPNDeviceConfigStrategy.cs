/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPNDeviceConfigStrategy.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.HWCNBL.PNFunctions._Interfaces
{
    /// <summary>
    /// Interface for PNDeviceConfigStrategy.
    /// </summary>
    public interface IPNDeviceConfigStrategy
    {
        /// <summary>
        /// Gets the APIs.
        /// </summary>
        /// <returns>APIs.</returns>
        uint[] GetAPIs();

        /// <summary>
        /// Gets the AR properties.
        /// </summary>
        /// <returns>AR properties.</returns>
        uint GetArProperties();

        /// <summary>
        /// Gets the AR record data.
        /// </summary>
        /// <returns>List of AR Record data blocks.</returns>
        IList<byte[]> GetARRecordData();

        /// <summary>
        /// Gets the AR type.
        /// </summary>
        /// <returns>The AR type.</returns>
        ushort GetArType();

        /// <summary>
        /// Gets the AR UUID.
        /// </summary>
        /// <returns>The AR UUID.</returns>
        Guid GetArUuid();

        /// <summary>
        /// Gets the value of a specified attribute.
        /// </summary>
        /// <typeparam name="T">Type of the attribute.</typeparam>
        /// <param name="attributeName">Name of the attribute.</param>
        /// <param name="defaultValue">Default value.</param>
        /// <returns>
        /// Value of the attribute if the attribute is accessed successfully; default value otherwise.
        /// </returns>
        T GetAttributeValue<T>(string attributeName, T defaultValue);

        /// <summary>
        /// Gets the block version.
        /// </summary>
        /// <param name="blocktype">Block type.</param>
        /// <returns>The block version.</returns>
        ushort GetBlockVersion(int blocktype);

        /// <summary>
        /// Gets CheckDeviceID attribute.
        /// </summary>
        /// <returns>CheckDeviceID attribute.</returns>
        bool GetCheckDeviceId();

        /// <summary>
        /// Gets CMI activity timeout.
        /// </summary>
        /// <returns>The CMI activity timeout.</returns>
        ushort GetCmiActivityTimeout();

        /// <summary>
        /// Gets the configuration subslot data.
        /// </summary>
        /// <param name="module">The module whose data will be retrieved.</param>
        /// <param name="submodule">The submodule whose data will be retrieved</param>
        /// <param name="api">The API number.</param>
        /// <returns>Byte array containing the data block.</returns>
        byte[] GetConfigurationSubslotData(PclObject module, PclObject submodule, long api);

        /// <summary>
        /// Gets the subslotnumber from configuration subslot data
        /// </summary>
        /// <param name="module">The module whose data will be retrieved.</param>
        /// <param name="submodule">The submodule whose data will be retrieved</param>
        /// <param name="api">The API number.</param>
        /// <returns></returns>
        int GetSubslotNumber(PclObject module, PclObject submodule, long api);

        /// <summary>
        /// Gets the input and output IOCR entry data blocks.
        /// </summary>
        /// <param name="inputCrEntry">Input CR entry blocks.</param>
        /// <param name="outputCrEntry">Output CR entry blocks.</param>
        /// <param name="withApi">Whether API struct should be used.</param>
        void GetCREntry(IList<byte[]> inputCrEntry, IList<byte[]> outputCrEntry, bool withApi);

        /// <summary>
        /// Gets the data records of an object.
        /// </summary>
        /// <param name="module">The object whose data records will be retrieved.</param>
        /// <returns>The data records of an object.</returns>
        byte[] GetDataRecords(PclObject module);

        /// <summary>
        /// Gets the address mode of the device.
        /// </summary>
        /// <returns>The address mode.</returns>
        uint GetDeviceAddressMode();

        /// <summary>
        /// Gets the device ID.
        /// </summary>
        /// <returns>The device ID.</returns>
        ushort GetDeviceId();

        /// <summary>
        /// Gets the InstanceID.
        /// </summary>
        /// <returns>InstanceID of the device.</returns>
        ushort GetDeviceInstance();

        /// <summary>
        /// Gets the IRT mode.
        /// </summary>
        /// <returns>The IRT mode.</returns>
        ushort GetIrtMode();

        /// <summary>
        /// Gets the modules of the device.
        /// </summary>
        /// <returns>List of modules.</returns>
        IList<PclObject> GetModules();

        /// <summary>
        /// Gets PDIR data block.
        /// </summary>
        /// <param name="slotNumber">Slot number that will be used in the data block.</param>
        /// <param name="subSlotNumber">Subslot number that will be used in the data block.</param>
        /// <returns></returns>
        byte[] GetPDIRData(int slotNumber, int subSlotNumber);

        /// <summary>
        /// Gets RTA retry count.
        /// </summary>
        /// <returns>RTA retry count.</returns>
        ushort GetRTARetries();

        /// <summary>
        /// Gets RTA timeout factor.
        /// </summary>
        /// <returns>RTA timeout factor.</returns>
        ushort GetRTATimeoutFactor();

        /// <summary>
        /// Gets the startup mode.
        /// </summary>
        /// <returns>The startup mode.</returns>
        ushort GetStartupMode();

        /// <summary>
        /// Gets station name alias data block.
        /// </summary>
        /// <returns>Station name alias data block.</returns>
        IList<byte[]> GetStationNameAliases();

        /// <summary>
        /// Gets the submodule properties subslot data.
        /// </summary>
        /// <param name="module">The module whose data will be retrieved.</param>
        /// <param name="submodule">The submodule whose data will be retrieved.</param>
        /// <param name="api">The API number.</param>
        /// <returns>Byte array containing the data block.</returns>
        byte[] GetSubmodulePropertiesSubslotData(PclObject module, PclObject submodule, long api);

        /// <summary>
        /// Gets the submodules of the device.
        /// </summary>
        /// <param name="module">The object whose submodules will be retrieved.</param>
        /// <returns>List of submodules.</returns>
        IList<PclObject> GetSubmodules(PclObject module);

        /// <summary>
        /// Gets VendorID attribute.
        /// </summary>
        /// <returns>VendorID attribute.</returns>
        ushort GetVendorId();

        /// <summary>
        /// Initializes the application relation data.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">The controller with the AR that will be initialized.</param>
        void InitializeArData(Interface controllerInterfaceSubmodule);

        /// <summary>
        /// Gets whether name of station overwrite is supported.
        /// </summary>
        /// <returns>Whether name of station overwrite is supported.</returns>
        bool IsAllowNameOfStationOverwriteSupported();

        /// <summary>
        /// Gets whether name of station overwrite is active
        /// </summary>
        /// <returns>Returns true if name of station overwrite is active, otherwise false</returns>
        bool IsAllowNameOfStationOverwriteActive();

        /// <summary>
        /// Gets whether AR record is supported.
        /// </summary>
        /// <returns>Whether AR record is supported.</returns>
        bool IsARRecordSupported();

        /// <summary>
        /// Gets whether AR record is active.
        /// </summary>
        /// <returns>Whether AR record is active.</returns>
        bool IsArRecordActive();

        /// <summary>
        /// Gets whether exchange without MMC is supported.
        /// </summary>
        /// <returns>Whether exchange without MMC is supported.</returns>
        bool IsExchangeWithoutMMCSupported();

        /// <summary>
        /// Gets whether IR info block is required.
        /// </summary>
        /// <returns>Whether IR info block is required.</returns>
        bool IsIRInfoBlockRequired();

        /// <summary>
        /// Gets whether device supports multiple write.
        /// </summary>
        /// <returns>Whether multiple write is supported.</returns>
        bool IsMultipleWriteSupported();

        /// <summary>
        /// Gets whether parameterizing speedup is supported.
        /// </summary>
        /// <returns>Whether parameterizing speedup is supported.</returns>
        bool IsParamSpeedupSupported();

        /// <summary>
        /// Gets whether submodule properties block is required.
        /// </summary>
        /// <returns>Wheter submodule properties is required.</returns>
        bool IsSubmodulePropertiesRequired();

        bool IsDeviceDefaultRouterIndividual();

        /// <summary>
        /// if dcp is supported, it returns true.
        /// </summary>
        /// <returns></returns>
        bool IsDcpReadOnlyEnabled();
    }
}