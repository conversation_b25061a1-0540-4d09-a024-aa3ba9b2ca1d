/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AttributeUtilities.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities.Enums;

using BandwidthUse = PNConfigLib.ConfigReader.Configuration.BandwidthUse;
using CableLength = PNConfigLib.ConfigReader.Topology.CableLength;
using SyncRole = PNConfigLib.ConfigReader.Configuration.SyncRole;
using TransmissionRate = PNConfigLib.ConfigReader.Configuration.TransmissionRate; 

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Contains methods and utilities used for accessing
    /// commonly used attributes and relevant values.
    /// </summary>
    internal static class AttributeUtilities
    {
        internal static PNIRTSyncRole MapSyncRoleEnum(SyncRole syncRole)
        {
            switch (syncRole)
            {
                case SyncRole.RedundantSyncMaster: return PNIRTSyncRole.SecondarySyncMaster;
                case SyncRole.SyncMaster: return PNIRTSyncRole.SyncMaster;
                case SyncRole.SyncSlave: return PNIRTSyncRole.SyncSlave;
                case SyncRole.Unsynchronized: return PNIRTSyncRole.NotSynchronized;
                default: return PNIRTSyncRole.SyncMaster;
            }
        }

        internal static bool IsCentralPDEV(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            UInt32 pnPdevParametrizationDecentral = interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnPdevParametrizationDecentral,
                new AttributeAccessCode(),
                default(UInt32));
            if (pnPdevParametrizationDecentral == 0)
            {
                return true;
            }
            return false;
        }

        internal static PNTransmissionRate MapTransmissionRateEnum(TransmissionRate transmissionRate)
        {
            switch (transmissionRate)
            {
                case TransmissionRate.TP10MbpsHalfDuplex: return PNTransmissionRate.TP10MbpsHalfDuplex;
                case TransmissionRate.TP10MbpsFullDuplex: return PNTransmissionRate.TP10MbpsFullDuplex;
                case TransmissionRate.AsyncFiber10MbpsHalfDuplex: return PNTransmissionRate.AsyncFiber10MbpsHalfDuplex;
                case TransmissionRate.AsyncFiber10MbpsFullDuplex: return PNTransmissionRate.AsyncFiber10MbpsFullDuplex;
                case TransmissionRate.TP100MbpsHalfDuplex: return PNTransmissionRate.TP100MbpsHalfDuplex;
                case TransmissionRate.TP100MbpsFullDuplex: return PNTransmissionRate.TP100MbpsFullDuplex;
                case TransmissionRate.POFPCF100MbpsFullDuplex: return PNTransmissionRate.POFPCF100MbpsFullDuplex;
                case TransmissionRate.FO100MbpsFullDuplex: return PNTransmissionRate.FO100MbpsFullDuplex;
                case TransmissionRate.X1000MbpsFullDuplex: return PNTransmissionRate.X1000MbpsFullDuplex;
                case TransmissionRate.FO1000MbpsFullDuplex: return PNTransmissionRate.FO1000MbpsFullDuplex;
                case TransmissionRate.FO1000MbpsFullDuplexLD: return PNTransmissionRate.FO1000MbpsFullDuplexLD;
                case TransmissionRate.TP1000MbpsFullDuplex: return PNTransmissionRate.TP1000MbpsFullDuplex;
                case TransmissionRate.FO10000MbpsFullDuplex: return PNTransmissionRate.FO10000MbpsFullDuplex;
                case TransmissionRate.FO100MbpsFullDuplexLD: return PNTransmissionRate.FO100MbpsFullDuplexLD;
                case TransmissionRate.Automatic: return PNTransmissionRate.Automatic;
                default: throw new ArgumentOutOfRangeException(nameof(transmissionRate), transmissionRate, null);
            }
        }

        internal static PNBandwidthUse MapBandwidthUse(BandwidthUse bandwidthUse)
        {
            switch (bandwidthUse)
            {
                case BandwidthUse.Maximum25cyclicIOdataFocusonnonecyclicdata:
                    return PNBandwidthUse.Maximum25cyclicIOdataFocusonnonecyclicdata;
                case BandwidthUse.Maximum375cyclicIOdataFocusonnonecyclicdata:   
                    return PNBandwidthUse.Maximum375cyclicdataFocusonnonecyclicdata;
                case BandwidthUse.Maximum50cyclicIOdataBalancedproportion:
                    return PNBandwidthUse.Maximum50cyclicIOdataBalancedproportion;
                case BandwidthUse.Maximum90cyclicIOdataFocusoncyclicdata:
                    return PNBandwidthUse.Maximum90cyclicdataFocusoncyclicdata;
                case BandwidthUse.Always90cyclicdataFocusoncyclicdata:
                    return PNBandwidthUse.Always90cyclicdataFocusoncyclicdata;


                default: throw new ArgumentOutOfRangeException(nameof(bandwidthUse), bandwidthUse, null);
            }
        }

        internal static FwVersion MapVersion(string version)
        {
            switch (version)
            {
                case "v2.1":
                    return FwVersion.V2_1;
                case "v2.2":
                    return FwVersion.V2_2;
                case "v3.1":
                    return FwVersion.V3_1;
                default: 
                    return FwVersion.Undefined;
            }
        }

        internal static PNCableLength MapCableLength(CableLength cableLength)
        {
            switch (cableLength)
            {
                case CableLength.max20m: return PNCableLength.Item20m;
                case CableLength.max50m: return PNCableLength.Item50m;
                case CableLength.max100m: return PNCableLength.Item100m;
                case CableLength.max500m: return PNCableLength.Item500m;
                case CableLength.max1000m: return PNCableLength.Item1000m;
                case CableLength.max3000m: return PNCableLength.Item3000m;
                case CableLength.NotSpecified: return PNCableLength.NotSpecified;
                default: throw new ArgumentOutOfRangeException(nameof(cableLength), cableLength, null);
            }
        }

        /// <summary>
        /// Generates an AttributeAccess Error Message
        /// </summary>
        /// <param name="attributeName">The Attribute which was accessed via AttributeAccess</param>
        /// <param name="isReadAccess">specifies the access type</param>
        /// <param name="acode">the AttributeAccessCode which was used for the Attribute Access</param>
        /// <param name="configObject">The Object which was accessed via AttributeAccess</param>
        /// <returns>The String With the Error Message</returns>
        private static string GetAttributeAccessMessage(
            string attributeName,
            bool isReadAccess,
            AttributeAccessCode acode,
            PclObject configObject)
        {
            Debug.Assert(attributeName != null);
            Debug.Assert(configObject != null);
            if (attributeName == null)
            {
                return string.Empty;
            }
            if (configObject == null)
            {
                return string.Empty;
            }

            string result = string.Empty;

            if (attributeName != null)
            {
                if (acode != null)
                {
                    if (configObject != null)
                    {
                        if (isReadAccess)
                        {
                            result = string.Format(
                                CultureInfo.InvariantCulture,
                                "Reading access on attribute '{0}' returned with AttributeAccessCode '{1}' "
                                + "for ConfigObject '{2}'.",
                                attributeName,
                                acode.Code,
                                configObject);
                        }
                        else
                        {
                            result = string.Format(
                                CultureInfo.InvariantCulture,
                                "Writing access on attribute '{0}' returned with AttributeAccessCode '{1}' "
                                + "for ConfigObject '{2}'.",
                                attributeName,
                                acode.Code,
                                configObject);
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// This function gets the supported reduction ratios for RT Class 1,2
        /// of an interface submodule of a profinet controller or profinet device.
        /// </summary>
        /// <param name="PNInterfaceSubmodule">
        /// The interface submodule whose supported RT Class 1,2 reduction ratios will be
        /// retrieved.
        /// </param>
        /// <exception cref="ArgumentNullException">if PNInterfaceSubmodule is null.</exception>
        /// <returns>Supported RT Class 1,2 reduction ratios of the interface submodule.</returns>
        public static List<int> GetCurrentSuppRR12(Interface pnInterfaceSubmodule)
        {
            if (pnInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(pnInterfaceSubmodule));
            }
            List<int> currentSuppRR12 = null;
            //Get sendclockfactor of the interface submodule.
            long sendClockFactorOfInterfaceSubmodule = PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor;
            AttributeAccessCode ac = new AttributeAccessCode();
            if (IsMaster(pnInterfaceSubmodule))
            {
                //Controller Interfacesubmodule
                GetTransientPNSendClockFactor(
                    pnInterfaceSubmodule,
                    PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor);
            }

            ac.Reset();
            if (IsPowerOfTwo(sendClockFactorOfInterfaceSubmodule))
            {
                Enumerated pnIoSuppRr12Pow2Enumerated =
                    pnInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR12Pow2,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    if ((pnIoSuppRr12Pow2Enumerated != null)
                        && (pnIoSuppRr12Pow2Enumerated.List != null))
                    {
                        currentSuppRR12 = new List<int>();
                        foreach (object pnIoSuppRr12Pow2 in pnIoSuppRr12Pow2Enumerated.List)
                        {
                            currentSuppRR12.Add(int.Parse(pnIoSuppRr12Pow2.ToString(), CultureInfo.InvariantCulture));
                        }
                    }
                }
                else
                {
                    currentSuppRR12 = new List<int>(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow12);
                }
            }
            else
            {
                Enumerated pnIoSuppRr12NonPow2Enumerated =
                    pnInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR12NonPow2,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    if ((pnIoSuppRr12NonPow2Enumerated != null)
                        && (pnIoSuppRr12NonPow2Enumerated.List != null)
                        && (pnIoSuppRr12NonPow2Enumerated.List.Count > 0))
                    {
                        currentSuppRR12 = new List<int>();
                        foreach (object pnIoSuppRr12NonPow2 in pnIoSuppRr12NonPow2Enumerated.List)
                        {
                            currentSuppRR12.Add(int.Parse((string)pnIoSuppRr12NonPow2, CultureInfo.InvariantCulture));
                        }
                    }
                }
                else
                {
                    currentSuppRR12 = new List<int>(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow12);
                }
            }

            return currentSuppRR12;
        }

        /// <summary>
        /// get attribute 'NodeIPConfiguration', !no checks concerning ObjectType will be performed!
        /// </summary>
        /// <param name="node">the node to query the attribute</param>
        /// <returns>NodeIPConfiguration value</returns>
        public static NodeIPConfiguration GetNodeIPConfiguration(DataModel.PCLObjects.Node node)
        {
            NodeIPConfiguration result = NodeIPConfiguration.Project;

            if (node != null)
            {
                AttributeAccessCode acode = new AttributeAccessCode();

                result = (NodeIPConfiguration)node.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.NodeIPConfiguration,
                    acode,
                    (int)NodeIPConfiguration.Project);

                Debug.Assert(
                    acode.IsOkay,
                    GetAttributeAccessMessage(InternalAttributeNames.NodeIPConfiguration, true, acode, node));
            }

            return result;
        }

        public static int GetPositionNumber(PclObject pclObject)
        {
            if (pclObject == null)
            {
                throw new ArgumentNullException(nameof(pclObject));
            }

            int positionNumber = pclObject.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                new AttributeAccessCode(),
                -1);

            return positionNumber;
        }

        /// <summary>
        /// Builds a string for the interface submodule with container and station names.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface for which the name will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>The name of the interface combined with the name of the container.</returns>
        public static string GetSubmoduleNameWithContainer(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            string nameOfContainer = string.Empty;

            // Get the container device item.
            PclObject deviceItem = NavigationUtilities.GetOwnerOfInterface(interfaceSubmodule);

            if (deviceItem != null)
            {
                nameOfContainer = GetName(deviceItem);
            }

            return nameOfContainer + "\\" + GetName(interfaceSubmodule);
        }

        /// <summary>
        /// Builds a string for the interface submodule with container and station names.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface for which the name will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <exception cref="InvalidOperationException">if interfaceSubmodule is not connected to a device.</exception>
        /// <returns>The name of the interface combined with the name of the container and station.</returns>
        public static string GetSubmoduleNameWithContainerAndStation(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            string nameOfSubmoduleWithContainer = GetSubmoduleNameWithContainer(interfaceSubmodule);

            // Get the container device item.
            PclObject station = interfaceSubmodule.GetDevice();

            if (station == null)
            {
                throw new InvalidOperationException("Station of interfaceSubmodule is null.");
            }
            string nameOfStation = GetName(station);

            return string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", nameOfSubmoduleWithContainer, nameOfStation);
        }

        public static bool HasInputSignals(PclObject configObject)
        {
            bool result = false;

            if (configObject != null)
            {
                AttributeAccessCode acode = new AttributeAccessCode();

                int ioType = configObject.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.IoType, acode, 0);

                result = (ioType & (int)IoTypes.Input) == (int)IoTypes.Input;
            }

            return result;
        }

        public static bool HasOutputSignals(PclObject configObject)
        {
            bool result = false;

            if (configObject != null)
            {
                AttributeAccessCode acode = new AttributeAccessCode();

                int ioType = configObject.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.IoType, acode, 0);

                result = (ioType & (int)IoTypes.Output) == (int)IoTypes.Output;
            }

            return result;
        }

        /// <summary>
        /// Checks whether the given Interface Submodule is currently in Profinet (IO-C, IO-D or IO-C and IO-D)
        /// operating mode.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <returns></returns>
        public static bool IsActiveProfinetInterfaceSubmodule(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            if (interfaceSubmodule is Interface)
            {
                // Check the actual operating mode of the Interface Submodule.
                AttributeAccessCode ac = new AttributeAccessCode();
                PNIOOperatingModes operatingMode =
                    (PNIOOperatingModes)interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoOperatingMode,
                        ac,
                        (uint)PNIOOperatingModes.None);

                if (operatingMode != PNIOOperatingModes.None)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if a given interfaceSubmodule is a decentral IDevice, or a normal decentral PDEV device.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule to be checked.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>True if decentral PDEV, false otherwise.</returns>
        public static bool IsDecentralPDEV(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            uint pnPdevParametrizationDecentral = interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnPdevParametrizationDecentral,
                new AttributeAccessCode(),
                default(uint));
            if ((pnPdevParametrizationDecentral == 1)
                || ((PNIOOperatingModes)interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoOperatingMode,
                        new AttributeAccessCode(),
                        0) == PNIOOperatingModes.IODevice))
            {
                return true; // decentral PDEV
            }

            return false; // central PDEV
        }

        /// <summary>
        /// Sets the transient value of SendClockFactor of the specified controller Interface Submodule
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">The controller interface submodule</param>
        /// <param name="newValue">The new value</param>
        /// <returns>The attribute access code</returns>
        public static AttributeAccessCode SetTransientPNSendClockFactor(
            Interface controllerInterfaceSubmodule,
            long newValue)
        {
            if (controllerInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(controllerInterfaceSubmodule));
            }
            return controllerInterfaceSubmodule.AttributeAccess.SetAnyAttribute<long>(
                InternalAttributeNames.PnSendClockFactorTransient,
                newValue);
        }

        /// <summary>
        /// This function gets the supported reduction ratios for RT Class 3
        /// of an interface submodule of a profinet controller or profinet device.
        /// </summary>
        /// <param name="PNInterfaceSubmodule">
        /// The interface submodule whose supported RT Class 1,2 reduction ratios will be
        /// retrieved.
        /// </param>
        /// <exception cref="ArgumentNullException">if PNInterfaceSubmodule is null.</exception>
        /// <returns>Supported RT Class 3 reduction ratios of the interface submodule.</returns>
        internal static List<int> GetCurrentSuppRR3(Interface pnInterfaceSubmodule)
        {
            if (pnInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(pnInterfaceSubmodule));
            }

            List<int> currentSuppRR3 = null;
            //Get sendclockfactor of the interface submodule.
            long sendClockFactorOfInterfaceSubmodule = PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor;
            AttributeAccessCode ac = new AttributeAccessCode();
            if (IsMaster(pnInterfaceSubmodule))
            {
                //Controller Interfacesubmodule
                sendClockFactorOfInterfaceSubmodule = GetTransientPNSendClockFactor(
                    pnInterfaceSubmodule,
                    PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor);
            }

            if (IsPowerOfTwo(sendClockFactorOfInterfaceSubmodule))
            {
                Enumerated pnIoSuppRr3Pow2Enumerated =
                    pnInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR3Pow2,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    if ((pnIoSuppRr3Pow2Enumerated != null)
                        && (pnIoSuppRr3Pow2Enumerated.List != null))
                    {
                        currentSuppRR3 = new List<int>();
                        foreach (object pnIoSuppRr3Pow2 in pnIoSuppRr3Pow2Enumerated.List)
                        {
                            currentSuppRR3.Add(int.Parse(pnIoSuppRr3Pow2.ToString(), CultureInfo.InvariantCulture));
                        }
                    }
                }
                else
                {
                    currentSuppRR3 = new List<int>(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow3);
                }
            }
            else
            {
                Enumerated PNIoSuppRR3NonPow2Enumerated =
                    pnInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR3NonPow2,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    if ((PNIoSuppRR3NonPow2Enumerated != null)
                        && (PNIoSuppRR3NonPow2Enumerated.List != null))
                    {
                        currentSuppRR3 = new List<int>();
                        foreach (int PNIoSuppRR3NonPow2 in PNIoSuppRR3NonPow2Enumerated.List)
                        {
                            currentSuppRR3.Add(PNIoSuppRR3NonPow2);
                        }
                    }
                }
                else
                {
                    currentSuppRR3 = new List<int>(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow3);
                }
            }
            return currentSuppRR3;
        }

        /// <summary>
        /// Gets the default operating mode of a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose default operating mode will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>Default operating mode of the interface.</returns>
        private static PNIOOperatingModes GetDefaultOperatingMode(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            PclObject device = interfaceSubmodule.GetDevice();
            if (device is CentralDevice)
            {
                return PNIOOperatingModes.IOController;
            }
            if (device is DecentralDevice)
            {
                return PNIOOperatingModes.IODevice;
            }
            return PNIOOperatingModes.None;
        }

        /// <summary>
        /// Gets the PNStationNumber attribute of an IO connector.
        /// </summary>
        /// <param name="ioConnector">The IO connector whose PNStationNumber attribute will be retrieved.</param>
        /// <returns>
        /// The PNStationNumber attribute value of the IO connector,
        /// or 0 if the attribute can not be retrieved.
        /// </returns>
        internal static int GetFirstPNStationNumber(PclObject ioConnector)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            int stationNumber =
                ioConnector.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnStationNumber, ac, 0);

            return stationNumber;
        }

        /// <summary>
        /// Gets the name of a given PclObject.
        /// </summary>
        /// <param name="pclObject">The PclObject whose name will be retrieved.</param>
        /// <returns>Name of the PclObject; or String.Empty if "Name" attribute can not be retrieved.</returns>
        internal static string GetName(PclObject pclObject)
        {
            if (pclObject == null)
            {
                return string.Empty;
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            string name =
                pclObject.AttributeAccess.GetAnyAttribute<string>(InternalAttributeNames.Name, ac, string.Empty);

            if (string.IsNullOrEmpty(name)
                || !ac.IsOkay)
            {
                return string.Empty;
            }

            return name;
        }

        /// <summary>
        /// Gets the current operating mode of a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">Interface submodule</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>Current operating mode of the interface submodule.</returns>
        internal static PNIOOperatingModes GetOperatingMode(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            return (PNIOOperatingModes)interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIoOperatingMode,
                ac,
                (uint)PNIOOperatingModes.None);
        }

        /// <summary>
        /// Gets the transient send clock factor of a given controller interface.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The controller interface whose transient send clock factor will be
        /// retrieved.
        /// </param>
        /// <param name="defaultValue">The default value to use in case the attribute can not be retrieved.</param>
        /// <exception cref="ArgumentNullException">if controllerInterfaceSubmodule is null.</exception>
        /// <returns>
        /// The PNSendClockFactorTransient attribute value of the controller interface,
        /// or the default value if that attribute can not be retrieved.
        /// </returns>
        internal static long GetTransientPNSendClockFactor(PclObject controllerInterfaceSubmodule, long defaultValue)
        {
            if (controllerInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(controllerInterfaceSubmodule));
            }

            long sendClockFactor = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnSendClockFactorTransient,
                new AttributeAccessCode(),
                defaultValue);

            return sendClockFactor;
        }

        /// <summary>
        /// Checks if the given interface submodule is iDevice.
        /// </summary>
        /// <remarks>
        /// This method first checks if the current operating mode is equal to the default operating mode.
        /// If not, then checks whether the current operating mode is IODevice or IOControllerAndIODevice.
        /// If it this, then the interface submodule is iDevice.
        /// </remarks>
        /// <param name="interfaceSubmodule">The interface submodule whose iDevice status will be checked.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>True if interfaceSubmodule is iDevice, false otherwise.</returns>
        internal static bool IsIDevice(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            bool isIDevice = false;
            PNIOOperatingModes defaultOperatingMode = GetDefaultOperatingMode(interfaceSubmodule);
            PNIOOperatingModes currentOperatingMode = GetOperatingMode(interfaceSubmodule);
            if (defaultOperatingMode != currentOperatingMode)
            {
                if ((currentOperatingMode == PNIOOperatingModes.IODevice)
                    || (currentOperatingMode == PNIOOperatingModes.IOControllerAndIODevice))
                {
                    isIDevice = true;
                }
            }
            return isIDevice;
        }

        /// <summary>
        /// Checks if a given Interface is a Profinet master.
        /// </summary>
        /// <param name="interfaceSubmodule">Interface to be checked.</param>
        /// <returns>True if the interfaceSubmodule is a Profinet master, false otherwise.</returns>
        private static bool IsMaster(Interface interfaceSubmodule)
        {
            PclObject connector = interfaceSubmodule.GetIOConnector();
            return connector.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.IsMasterType,
                new AttributeAccessCode(),
                false);
        }

        /// <summary>
        /// Checks whether a given number is power of two.
        /// </summary>
        /// <param name="number">The number to be checked.</param>
        /// <returns>True if the number is power of two, false otherwise.</returns>
        private static bool IsPowerOfTwo(long number)
        {
            return !((number & (number - 1)) > 0);
        }

        internal static long MapFiberOpticType(FiberOpticType specifiedCableType)
        {
            long mappedType = 0;
            // Check if the specified type is supported.
            switch (specifiedCableType)
            {
                // Type is not specified, used the first supported type.
                case FiberOpticType.FOgroundcable:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypeFoGroundCable, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.FOstandardcable625µm:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypeFoStandardCable625, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.FOstandardcableGP50µm:
                    mappedType = long.Parse(AttributeValues.CableTypes.CabeTypeFoStandardCableGp50, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.FOtrailingcableGP:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypeFoTrailingCableGp, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.FlexibleFOcable:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypeFlexibleFoCable, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.PCFstandardcableGP:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypePcfStandardCableGp, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.PCFtrailingcableGP:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypePcfTrailingCableGp, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.POFtrailingcable:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypePofTrailingCable, CultureInfo.InvariantCulture);
                    break;
                case FiberOpticType.POFstandardcableGP:
                    mappedType = long.Parse(AttributeValues.CableTypes.CableTypePofStandardCableGp, CultureInfo.InvariantCulture);
                    break;
            }
            return mappedType;
        }

        internal static void SetSyncRole(Interface interfaceSubmodule, PNIRTSyncRole syncRole)
        {
            interfaceSubmodule.PNIOD.AttributeAccess.SetAnyAttribute<byte>(
                InternalAttributeNames.PnIrtSyncRole,
                (byte)syncRole);
            if (syncRole == PNIRTSyncRole.NotSynchronized)
            {
                Enumerated suppFrameClass = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    new AttributeAccessCode(),
                    null);

                if ((suppFrameClass == null)
                    || !suppFrameClass.List.Contains((uint)PNIOFrameClass.Class2Frame))
                {
                    interfaceSubmodule.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.PnIoFrameClass,
                        (long)PNIOFrameClass.Class1Frame);
                }
                else
                {
                    interfaceSubmodule.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.PnIoFrameClass,
                        (long)PNIOFrameClass.Class2Frame);
                }
            }
            else
            {
                interfaceSubmodule.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                    InternalAttributeNames.PnIoFrameClass,
                    (long)PNIOFrameClass.Class3Frame);
            }
        }

        internal static void SetCustomVariantSubmoduleIdentNumber(PclObject submodule, string deviceVersion)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            uint identNumber = 0;
            bool isDeviceVersion31orUpper = CheckDeviceVersion(deviceVersion);

            Interface interfaceSubmodule = submodule as Interface;

            DataModel.PCLObjects.Port portSubmodule = submodule as DataModel.PCLObjects.Port;

            if (interfaceSubmodule != null)
            {
                identNumber = isDeviceVersion31orUpper ? PNConstants.CustomInterfaceSubmoduleIdentNumberForV31orUpper : PNConstants.CustomInterfaceSubmoduleIdentNumber;
            }
            else if (portSubmodule != null)
            {
                identNumber = isDeviceVersion31orUpper ? PNConstants.CustomPortSubmoduleIdentNumberForV31orUpper : PNConstants.CustomPortSubmoduleIdentNumber;
            }

            submodule.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnSubmoduleIdentNumber, ac, 0);
            if (!ac.IsOkay)
            {
                submodule.PCLCatalogObject.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnSubmoduleIdentNumber, identNumber);
            }
            else
            {
                submodule.PCLCatalogObject.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnSubmoduleIdentNumber, identNumber);
            }
        }

        internal static void SetCustomVariantPositionNumber(CentralDevice centralDevice, string deviceVersion)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            int slotNumFor31orUpper = 0;
            int slotNumFor22orLower = 1;
            bool isDeviceVersion31orUpper = CheckDeviceVersion(deviceVersion);
            int positionNumber = isDeviceVersion31orUpper ? slotNumFor31orUpper : slotNumFor22orLower;

            centralDevice.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PositionNumber, ac, -1);
            if (!ac.IsOkay)
            {
                centralDevice.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PositionNumber, positionNumber);
            }
            else
            {
                centralDevice.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PositionNumber, positionNumber);
            }
        }

        internal static bool CheckDeviceVersion(string deviceVersion)
        {
            FwVersion fwVersion = HWCNBL.Utilities.AttributeUtilities.MapVersion(deviceVersion);
            return fwVersion.CompareTo(FwVersion.V3_1) >= 0 || fwVersion.CompareTo(FwVersion.Undefined) == 0;
        }
    }
}