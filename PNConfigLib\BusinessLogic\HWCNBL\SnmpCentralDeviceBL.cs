﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SnmpCentralDeviceBL.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Networks.SNMP;
using PNConfigLib.HWCNBL.Networks.SNMP._Interfaces;
using PNConfigLib.HWCNBL.Networks.SNMP.Consistency;

#endregion

namespace PNConfigLib.BusinessLogic.HWCNBL
{
    internal class SnmpCentralDeviceBL : CentralDeviceBL
    {
        private const string s_DefaultSnmpReadOnlyCommunityName = "public";

        private const string s_DefaultSnmpReadWriteCommunityName = "private";

        internal SnmpCentralDeviceBL(CentralDevice centralDevice) : base(centralDevice)
        {
        }

        internal override void Configure(CentralDeviceType xmlCentralDevice, PNDriverType lonCentralDevice)
        {
            base.Configure(xmlCentralDevice, lonCentralDevice);
            InitBL(xmlCentralDevice, lonCentralDevice);
        }

        private void CheckSnmpConsistency()
        {
            //Following consistency must be added after idevice implementation is done.
            //SnmpConfigurationSourceConsistencyChecker snmpConfigurationSourceCC =
            //    new SnmpConfigurationSourceConsistencyChecker(PnInterfaceSubmoduleNavigation,
            //                                                    IDeviceConfigurationChecker,
            //                                                    new SnmpErrorMessageProvider());
            //snmpConfigurationSourceCC.CheckConsistency(data);

            CommunityNameConsistencyChecker checker = new CommunityNameConsistencyChecker();

            checker.CheckConsistency(CentralDevice);

            SnmpEnableReadOnlyConsistencyChecker enableReadonlyCC = new SnmpEnableReadOnlyConsistencyChecker();
            enableReadonlyCC.CheckConsistency(CentralDevice);
        }

        private void GenericMethodGetSnmpControlRecord(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            methodData.ReturnValue = null;

            ISnmpControlRecordGenerator snmpRuntimeDataGenerator = new SnmpControlRecordV2Generator();

            byte[] record = snmpRuntimeDataGenerator?.GenerateSnmpControlRecord(CentralDevice);

            methodData.ReturnValue = record;

        }

        private bool HasDefaultValues(CentralDeviceType xmlCentralDevice)
        {
            if (!xmlCentralDevice.Snmp.SNMPEnabled
                && !xmlCentralDevice.Snmp.SNMPEnableReadOnly
                && xmlCentralDevice.Snmp.SNMPReadOnlyCommunityName.Equals(s_DefaultSnmpReadOnlyCommunityName, StringComparison.Ordinal)
                && xmlCentralDevice.Snmp.SNMPReadWriteCommunityName.Equals(s_DefaultSnmpReadWriteCommunityName, StringComparison.Ordinal))
            {
                return true;
            }

            return false;
        }

        private void InitActions()
        {
            CentralDevice.BaseActions.RegisterMethod(GetSnmpControlRecordName.Name, GenericMethodGetSnmpControlRecord);

            ConsistencyManager.RegisterConsistencyCheck(CentralDevice, CheckSnmpConsistency);
        }

        #region Attributes

        private void InitAttributes(CentralDeviceType xmlCentralDevice)
        {
            if (HasDefaultValues(xmlCentralDevice))
            {
                bool snmpEnabledCatalog = CentralDevice.PCLCatalogObject.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.SnmpEnabled,
                    new AttributeAccessCode(),
                    false);
                bool snmpEnableReadOnlyCatalog = CentralDevice.PCLCatalogObject.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.SnmpEnableReadOnly,
                    new AttributeAccessCode(),
                    false);
                string snmpReadOnlyCommunityNameCatalog =
                    CentralDevice.PCLCatalogObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.SnmpReadOnlyCommunityName,
                        new AttributeAccessCode(),
                        s_DefaultSnmpReadOnlyCommunityName);
                string snmpReadWriteCommunityNameCatalog =
                    CentralDevice.PCLCatalogObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.SnmpReadWriteCommunityName,
                        new AttributeAccessCode(),
                        s_DefaultSnmpReadWriteCommunityName);

                CentralDevice.AttributeAccess.AddAnyAttribute(InternalAttributeNames.SnmpEnabled, snmpEnabledCatalog);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpEnableReadOnly,
                    snmpEnableReadOnlyCatalog);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpReadOnlyCommunityName,
                    snmpReadOnlyCommunityNameCatalog);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpReadWriteCommunityName,
                    snmpReadWriteCommunityNameCatalog);
            }
            else
            {
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpEnabled,
                    xmlCentralDevice.Snmp.SNMPEnabled);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpEnableReadOnly,
                    xmlCentralDevice.Snmp.SNMPEnableReadOnly);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpReadOnlyCommunityName,
                    xmlCentralDevice.Snmp.SNMPReadOnlyCommunityName);
                CentralDevice.AttributeAccess.AddAnyAttribute(
                    InternalAttributeNames.SnmpReadWriteCommunityName,
                    xmlCentralDevice.Snmp.SNMPReadWriteCommunityName);
            }
        }

        #endregion

        private void InitBL(CentralDeviceType xmlCentralDevice, PNDriverType lonCentralDevice)
        {
            InitAttributes(xmlCentralDevice);
            InitActions();
        }
    }
}