/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: VirtualSubmodule.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The VirtualSubmodule itself is a concrete submodule. It 
    /// inherits some basic functionality from the ModuleObject class.
    /// In principle, the Submodule holds the IO and parameter data of 
    /// the modules.
    /// </summary>
    /// <remarks>At the moment, it doesn't implement any special
    /// functionality, beyond the basic submodule functionality.</remarks>
    public class VirtualSubmodule :
        ModuleObject,
        GSDI.IVirtualSubmodule,
        GSDI.IVirtualSubmodule2,
        GSDI.IVirtualSubmodule3,
        GSDI.IVirtualSubmodule4,
        GSDI.IProfiSafe,
        GSDI.IProfiSafe2,
        GSDI.ICompatibility
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Submodule if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public VirtualSubmodule()
        {
            m_IsTransferSequenceDefined = false;
            m_Api = 0;
            m_IsIsochroneModeSupported = false;
            m_IsProfIsafeSupported = false;

            m_WriteableImRecords = null;

            m_IsProfIenergySupported = false;
            m_SupportedSubstitutionModes = null;

            m_IsIm5Supported = false;
            m_IsProfIsafePirSupported = false;
            m_IsProfisafeFscpTestModeSupported = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        private IOData m_IOData;
        private ArrayList m_ParameterRecordData;
        private bool m_IsTransferSequenceDefined;

        private uint m_Api;
        private bool m_IsIsochroneModeSupported;
        private IsochroneMode m_IsochroneMode;
        private FParameterRecordData m_FParameterRecordData;
        private ModulePlugData m_PlugData;
        private bool m_IsProfIsafeSupported;

        private List<uint> m_WriteableImRecords;

        private bool m_IsProfIenergySupported;
        private List<uint> m_SupportedSubstitutionModes;

        private bool m_IsIm5Supported;
        private bool m_MayIssueProcessAlarm;
        private bool m_ExistsMayIssueProcessAlarm;

        // GSDML V2.33
        private ProfIenergy m_ProfIenergy;
        private ReportingSystemEvents m_ReportingSystemEvents;

        // GSDML V2.4
        private bool m_IsProfIsafePirSupported;
        // GSDML V2.44
        private FBaseIDRecordData m_FBaseIDRecordData;

        // GSDML V2.45
        private bool m_IsProfisafeFscpTestModeSupported;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the IOData object, which contains information about the
        /// input and output data of the submodule.
        /// </summary>
        public virtual IOData IOData => this.m_IOData;

        /// <summary>
		/// Accesses a list of parameter record data objects, which contains
		/// information about the possible parametrization of the submodule.
		/// </summary>
		public virtual Array ParameterRecordData =>
            this.m_ParameterRecordData?.ToArray();

        /// <summary>
		/// Accesses whether the transfer sequence for the parameter record
		/// data objects is defined.
		/// </summary>
		/// <remarks>There is so, if the transfer sequence of any parameter
		/// record data object is unequal to 0.</remarks>
		public virtual bool IsTransferSequenceDefined()
        {
            return this.m_IsTransferSequenceDefined;
        }

        public virtual UInt32 API => this.m_Api;

        public virtual bool IsIsochroneModeSupported => this.m_IsIsochroneModeSupported;

        public virtual IsochroneMode IsochroneMode => this.m_IsochroneMode;

        public virtual FParameterRecordData FParameterRecordData => this.m_FParameterRecordData;

        public virtual FBaseIDRecordData FBaseIDRecordData => this.m_FBaseIDRecordData;

        public virtual ModulePlugData PlugData => this.m_PlugData;

        public virtual bool IsPROFIsafeSupported => this.m_IsProfIsafeSupported;

        public virtual Array Writeable_IM_Records =>
            (null != this.m_WriteableImRecords) ?
                new ArrayList(this.m_WriteableImRecords).ToArray() :

                null;

        public virtual Array SupportedSubstitutionModes =>
            (null != this.m_SupportedSubstitutionModes) ?
                new ArrayList(this.m_SupportedSubstitutionModes).ToArray() :

                null;

        public virtual bool IsPROFIenergySupported => this.m_IsProfIenergySupported;

        public virtual bool IsIM5_Supported => this.m_IsIm5Supported;

        public ProfIenergy PROFIenergy => this.m_ProfIenergy;

        public ReportingSystemEvents ReportingSystemEvents => this.m_ReportingSystemEvents;

        public virtual bool IsPROFIsafePIR_Supported => this.m_IsProfIsafePirSupported;

        public virtual bool IsPROFIsafeFSCP_TestMode_Supported => this.m_IsProfisafeFscpTestModeSupported;

        protected new object IdentNumber => ((ModuleObject)this).IdentNumber;

        public new GSDI.IModuleInfo Info => ((ModuleObject)this).Info;

        protected new GSDI.IGraphic GetGraphic(GSDI.GraphicTypes lType)
        {
            return ((ModuleObject)this).GetGraphic(lType);
        }
        protected object FIOStructureDescCRC =>
            this.IOData?.FIOStructureDescCRC ?? 0;

        protected object FIOStructureDescVersion =>
            this.IOData?.FIOStructureDescVersion ?? 0;

        public bool MayIssueProcessAlarm => this.m_MayIssueProcessAlarm;

        public bool ExistsMayIssueProcessAlarm => m_ExistsMayIssueProcessAlarm;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                FillFieldIOData(hash);

                FillFieldParameterRecordData(hash);

                FillFieldAPI(hash);

                FillFieldIsIsochroneModeSupported(hash);

                FillFieldIsochroneMode(hash);

                FillFieldPlugData(hash);

                FillFieldIsPROFIsafeSupported(hash);

                FillFieldFParameterRecordData(hash);

                FillFieldFBaseIdRecordData(hash);

                FillFieldWriteable_IM_Records(hash);

                FillFieldSupportedSubstitutionModes(hash);

                FillFieldIsPROFIenergySupported(hash);

                FillFieldIM5_Supported(hash);

                FillFieldPROFIenergy(hash);

                FillFieldReportingSystemEvents(hash);

                FillFieldPROFIsafePIR_Supported(hash);

                FillFieldMayIssueProcessAlarm(hash);

                FillFieldPROFIsafeFSCP_TestMode_supported(hash);

                // Base data.
                succeeded = base.Fill(hash);

                if (null != this.m_ParameterRecordData)
                {
                    if (this.m_ParameterRecordData.Count != 0)
                    {
                        this.m_IsTransferSequenceDefined = (((ParameterRecordData)this.m_ParameterRecordData[0]).TransferSequence != 0);

                        // Sort parameter records by index.
                        SortedList list = new SortedList();
                        foreach (RecordData O in this.m_ParameterRecordData)
                            list.Add(O.Index, O);
                        this.m_ParameterRecordData = new ArrayList(list.Values);
                    }
                }

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }


        private void FillFieldPROFIsafePIR_Supported(Hashtable hash)
        {
            string member = Models.s_FieldProfIsafePirSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfIsafePirSupported = (bool)hash[member];
        }

        private void FillFieldReportingSystemEvents(Hashtable hash)
        {
            string member = Models.s_FieldReportingSystemEvents;
            if (hash.ContainsKey(member)
                && hash[member] is ReportingSystemEvents)
                this.m_ReportingSystemEvents = hash[member] as ReportingSystemEvents;
        }

        private void FillFieldPROFIenergy(Hashtable hash)
        {
            string member = Models.s_FieldProfIenergy;
            if (hash.ContainsKey(member)
                && hash[member] is ProfIenergy)
                this.m_ProfIenergy = hash[member] as ProfIenergy;
        }

        private void FillFieldIM5_Supported(Hashtable hash)
        {
            string member = Models.s_FieldIm5Supported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsIm5Supported = (bool)hash[member];
        }

        private void FillFieldIsPROFIenergySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsProfIenergySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfIenergySupported = (bool)hash[member];
        }

        private void FillFieldSupportedSubstitutionModes(Hashtable hash)
        {
            string member = Models.s_FieldSupportedSubstitutionModes;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                this.m_SupportedSubstitutionModes = hash[member] as List<uint>;
        }

        private void FillFieldWriteable_IM_Records(Hashtable hash)
        {
            string member = Models.s_FieldWriteableImRecords;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                this.m_WriteableImRecords = hash[member] as List<uint>;
        }

        private void FillFieldFParameterRecordData(Hashtable hash)
        {
            string member = Models.s_FieldFParameterRecordData;
            if (hash.ContainsKey(member)
                && hash[member] is FParameterRecordData)
                this.m_FParameterRecordData = hash[member] as FParameterRecordData;
        }

        private void FillFieldFBaseIdRecordData(Hashtable hash)
        {
            string member = Models.s_FieldFBaseIDRecordData;
            if (hash.ContainsKey(member)
                && hash[member] is FBaseIDRecordData)
                this.m_FBaseIDRecordData = hash[member] as FBaseIDRecordData;
        }

        private void FillFieldIsPROFIsafeSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsProfIsafeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfIsafeSupported = (bool)hash[member];
        }

        private void FillFieldPlugData(Hashtable hash)
        {
            string member = Models.s_FieldPlugData;
            if (hash.ContainsKey(member)
                && hash[member] is ModulePlugData)
                this.m_PlugData = hash[member] as ModulePlugData;
        }

        private void FillFieldIsochroneMode(Hashtable hash)
        {
            string member = Models.s_FieldIsochroneMode;
            if (hash.ContainsKey(member)
                && hash[member] is IsochroneMode)
                this.m_IsochroneMode = hash[member] as IsochroneMode;
        }

        private void FillFieldIsIsochroneModeSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsIsochroneModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsIsochroneModeSupported = (bool)hash[member];
        }

        private void FillFieldAPI(Hashtable hash)
        {
            string member = Models.s_FieldApi;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_Api = (uint)hash[member];
        }

        private void FillFieldParameterRecordData(Hashtable hash)
        {
            string member = Models.s_FieldParameterRecordData;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_ParameterRecordData = hash[member] as ArrayList;
        }

        private void FillFieldIOData(Hashtable hash)
        {
            string member = Models.s_FieldIoData;
            if (hash.ContainsKey(member)
                && hash[member] is IOData)
                this.m_IOData = hash[member] as IOData;
        }

        private void FillFieldMayIssueProcessAlarm(Hashtable hash)
        {
            string member = Models.s_FieldMayIssueProcessAlarm;
            if (!hash.ContainsKey(member)
                || hash[member] is not bool)
            {
                return;
            }

            this.m_MayIssueProcessAlarm = (bool)hash[member];
            m_ExistsMayIssueProcessAlarm = true;

        }

        private void FillFieldPROFIsafeFSCP_TestMode_supported(Hashtable hash)
        {
            string member = Models.s_FieldProfIsafeFscpTestModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfisafeFscpTestModeSupported = (bool)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectVirtualSubmodule);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            // From base class
            base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldIoData, this.m_IOData);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldParameterRecordData, this.m_ParameterRecordData);

            // 
            Export.WriteUint32Property(ref writer, Models.s_FieldApi, this.m_Api);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsIsochroneModeSupported, this.m_IsIsochroneModeSupported);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldIsochroneMode, this.m_IsochroneMode);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldPlugData, this.m_PlugData);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsProfIsafeSupported, this.m_IsProfIsafeSupported);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldFParameterRecordData, this.m_FParameterRecordData);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldFBaseIDRecordData, this.m_FBaseIDRecordData);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldWriteableImRecords, this.m_WriteableImRecords, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsProfIenergySupported, m_IsProfIenergySupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIm5Supported, m_IsIm5Supported);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldProfIenergy, this.m_ProfIenergy);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldReportingSystemEvents, this.m_ReportingSystemEvents);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafePirSupported, m_IsProfIsafePirSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafeFscpTestModeSupported, m_IsProfisafeFscpTestModeSupported);

            return true;
        }


        #endregion

    }
}


