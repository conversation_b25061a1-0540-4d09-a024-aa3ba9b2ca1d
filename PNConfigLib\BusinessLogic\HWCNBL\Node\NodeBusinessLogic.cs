/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NodeBusinessLogic.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Network;

using Subnet = PNConfigLib.DataModel.PCLObjects.Subnet;
#endregion

namespace PNConfigLib.HWCNBL.Node
{
    public abstract class NodeBusinessLogic : INodeBusinessLogic
    {
        protected NodeBusinessLogic(DataModel.PCLObjects.Node node)
        {
            Node = node;
            Initialize();
        }

        public DataModel.PCLObjects.Node Node
        {
            get;
        }

        public void Configure(DecentralDeviceTypeDecentralDeviceInterface xmlDecentralDeviceInterface)
        {
            if (xmlDecentralDeviceInterface == null)
            {
                throw new ArgumentNullException(nameof(xmlDecentralDeviceInterface));
            }

            string encodedName;
            var nameOfStationConvertResult = PNNameOfStationConverter.EncodePNNameOfStation(
                AttributeUtilities.GetName(Node.GetDevice()),
                null,
                null,
                out encodedName);
            Node.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.PnNameOfStation,
                encodedName);

            PNConfigLib.Consistency.Checker.ConsistencyCheckerUtilities.CheckInvalidDeviceName(
                xmlDecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item,
                xmlDecentralDeviceInterface,
                nameOfStationConvertResult);
            Node.AttributeAccess.SetAnyAttribute<string>(
                InternalAttributeNames.PnNameOfStationVirtual,
                xmlDecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item);

            NodeIeBusinessLogic nodeIeBusinessLogic = (NodeIeBusinessLogic)this;
            PNConfigLib.Consistency.Checker.ConfigurationFileChecker.DecentralDeviceChecker.CanIpAddressSetDirectlyAtTheDevice(
                xmlDecentralDeviceInterface,
                nodeIeBusinessLogic.IsPNIoIpConfigModeSupported,
                nodeIeBusinessLogic.AddressTailoringEnabled);

            object ipProtocolItem = xmlDecentralDeviceInterface.EthernetAddresses.IPProtocol.Item;
            var elementName = xmlDecentralDeviceInterface.EthernetAddresses.IPProtocol.ItemElementName;
            if (ipProtocolItem is string)
            {
                Node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Other);
                ApplySetDirectlyAtDeviceEnabled(elementName);
            }
            else
            {
                Node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Project);

                DecentralIPProtocolTypeSetInTheProject setIpAddressInProject = ipProtocolItem as DecentralIPProtocolTypeSetInTheProject;

                Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.NodeIPAddresseSetByUser, true);

                if (setIpAddressInProject != null)
                {
                    long ipAddress = new IPAddress(setIpAddressInProject.IPAddress, Node).AsInt64;
                    long subnetMask = new IPSubnetMask(setIpAddressInProject.SubnetMask, Node).AsInt64;

                    Node.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.NodeIPAddress,
                        ipAddress);

                    Node.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.NodeIPSubnetMask,
                       subnetMask);

                    long routerAddress;
                    bool isDefaultRouterUsed;
                    bool isRouterSyncronizedWithController = setIpAddressInProject.SynchronizeRouterSettingsWithIOController;

                    AttributeAccessCode ac = new AttributeAccessCode();
                    Interface deviceInterface = Node.ParentObject as Interface;
                    Interface ioControllerInterface = deviceInterface?.PNIOD?.AssignedController?.GetInterface();
                    if (ioControllerInterface == null)
                    {
                        return;
                    }

                    if (!ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIoIODDefaultRouterSupported, ac, false))
                    {
                        isRouterSyncronizedWithController = true;
                    }
                    if (isRouterSyncronizedWithController) // use IOC's router address definitions
                    {
                        routerAddress = ioControllerInterface.Node.AttributeAccess.GetAnyAttribute<long>(
                             InternalAttributeNames.NodeIPDefaultRouterAddress, ac, 0);
                        isDefaultRouterUsed = ioControllerInterface.Node.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.NodeIPDefaultRouterAddressUsed, ac, false);
                    }
                    else if (setIpAddressInProject.RouterAddress != PNConstants.DefaultRouterIP)  // use its own router address
                    {
                        routerAddress = new IPDefaultRouterAddress(setIpAddressInProject.RouterAddress, Node).AsInt64;
                        isDefaultRouterUsed = true;
                    }
                    else
                    {
                        routerAddress = ipAddress;
                        isDefaultRouterUsed = false;
                    }
                    Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.NodeIPDefaultRouterIODSync, isRouterSyncronizedWithController);
                    Node.AttributeAccess.SetAnyAttribute<long>(InternalAttributeNames.NodeIPDefaultRouterAddress, routerAddress);
                    Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.NodeIPDefaultRouterAddressUsed, isDefaultRouterUsed);
                }
            }
        }

        public void Configure(CentralDeviceTypeCentralDeviceInterface xmlCentralDeviceInterface)
        {
            if (xmlCentralDeviceInterface == null)
            {
                throw new ArgumentNullException(nameof(xmlCentralDeviceInterface));
            }
            string encodedName;
            var nameOfStationConvertResult = PNNameOfStationConverter.EncodePNNameOfStation(
                AttributeUtilities.GetName(Node.GetDevice()),
                null,
                null,
                out encodedName);

            Node.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.PnNameOfStation,
                encodedName);
            object pnDeviceNameItem = xmlCentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item;
            if (pnDeviceNameItem is bool)
            {
                Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPnNoSViaOtherPath, true);
            }
            else
            {
                PNConfigLib.Consistency.Checker.ConsistencyCheckerUtilities.CheckInvalidDeviceName(
                    (string)xmlCentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item,
                    xmlCentralDeviceInterface,
                    nameOfStationConvertResult);
                Node.AttributeAccess.SetAnyAttribute<string>(
                    InternalAttributeNames.PnNameOfStationVirtual,
                    pnDeviceNameItem as string);
            }

            object ipProtocolItem = xmlCentralDeviceInterface.EthernetAddresses.IPProtocol.Item;
            if (ipProtocolItem is string)
            {
                Node.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnPnIpSuiteViaOtherPath, true);
                Node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Other);
            }
            else
            {
                Node.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnPnIpSuiteViaOtherPath, false);
                Node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Project);

                CentralIPProtocolTypeSetInTheProject setIpAddressInProject = ipProtocolItem as CentralIPProtocolTypeSetInTheProject;

                if (setIpAddressInProject != null)
                {
                    long ipAddress = new IPAddress(setIpAddressInProject.IPAddress, Node).AsInt64;
                    Node.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.NodeIPAddress, ipAddress);
                }
                Node.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.NodeIPAddresseSetByUser,
                    true);

                if (setIpAddressInProject != null)
                {
                    long subnetMask = new IPSubnetMask(setIpAddressInProject.SubnetMask, Node).AsInt64;
                    Node.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.NodeIPSubnetMask,
                        subnetMask);
                    if (setIpAddressInProject.RouterAddress != PNConstants.DefaultRouterIP)
                    {
                        long routerAddress = new IPDefaultRouterAddress(setIpAddressInProject.RouterAddress, Node).AsInt64;
                        Node.AttributeAccess.SetAnyAttribute<long>(
                            InternalAttributeNames.NodeIPDefaultRouterAddress, routerAddress);
                        Node.AttributeAccess.SetAnyAttribute<bool>(
                            InternalAttributeNames.NodeIPDefaultRouterAddressUsed, true);
                    }
                }
            }
        }

        /// <remarks>
        /// Remember that every attribute which value is here set from meta knowledge has to be added
        /// to RemoveAttributesFilledFromMetaKnowledge()
        /// </remarks>
        private void AddAttributes()
        {
            Node.AttributeAccess.AddAnyAttribute(InternalAttributeNames.NodeGetsAddressAutomatically, true);
            Node.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.NodeFeatures, 0);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.NodeIPProtocolUsed, true);
            Node.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.PnNameOfStation, String.Empty);
            Node.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.PnNameOfStationVirtual, String.Empty);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPnNoSViaOtherPath, false);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPnNoSAutoGenerate, false);
            Node.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.NodeIPAddress, 0);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.NodeIPAddresseSetByUser, true);
            Node.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.NodeIPSubnetMask, 0);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.NodeIPDefaultRouterAddressUsed,
                false);
            Node.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.NodeIPDefaultRouterAddress, 0);
            Node.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)NodeIPConfiguration.Project);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPnIpSuiteViaOtherPath, false);
            Node.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoIpSuiteViaOtherPath, false);
        }

        private void Initialize()
        {
            AddAttributes();
        }

        /// <summary>
        /// This method does necessary actions if SetDirectlyAtDevice is enabled
        /// </summary>
        /// <param name="elementName">ItemChoiceType enum</param>
        private void ApplySetDirectlyAtDeviceEnabled(ItemChoiceType elementName)
        {
            if (elementName == ItemChoiceType.SetDirectlyAtTheDevice)
            {
                NodeIeBusinessLogic nodeIeBusinessLogic = (NodeIeBusinessLogic)this;

                var nodeIpConfiguration = (NodeIPConfiguration)Node.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.NodeIPConfiguration,
                    new AttributeAccessCode(),
                    (int)NodeIPConfiguration.Other);
                bool viaOtherPath = nodeIpConfiguration == NodeIPConfiguration.Other;

                if (nodeIeBusinessLogic.IsPNPNIpConfigModeSupported)
                {
                    nodeIeBusinessLogic.PNPNIpSuiteViaOtherPath = viaOtherPath;
                }

                if ((nodeIeBusinessLogic.IsPNIoIpConfigModeSupported || nodeIeBusinessLogic.AddressTailoringEnabled)
                    && !nodeIeBusinessLogic.IsAddressTailoredIDevice)
                {
                    nodeIeBusinessLogic.PNIoIpSuiteViaOtherPath = viaOtherPath;
                }

                nodeIeBusinessLogic.IPConfiguration = nodeIpConfiguration;
            }
        }

        #region Public Methods

        /// <summary>
        /// </summary>
        /// <param name="subnet"> subnet to which this node will be connected</param>
        public abstract bool SetDefaultAddress(Subnet subnet);

        #endregion
    }
}