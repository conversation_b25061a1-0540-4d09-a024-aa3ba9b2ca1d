/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PdMasterTailorDataStruct.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.Config
{
    /// <summary>
    /// Summary description for PdMasterTailorDataStruct.
    /// </summary>
    internal class PdMasterTailorDataStruct : ParameterDSSubBlockStruct
    {
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Nested classes
        // Contains all non-public nested classes and locally scoped interface definitions
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Constants and enumerations
        // Contains all constants and enumerations

        private const int s_Index = 0x00017081;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private BlockDataWrapper<PdMasterTailorDataStructEnum, ushort> m_BlockDataWrapper;

        private List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> m_PortMasterTailorData
            = new List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>>();

        private bool m_SubblockAdded = false;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        public int IndexLow => s_Index & 0xFFFF;

        public int IndexHigh => s_Index >> 16;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Delegates and events
        // Contains all delegate and events
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Construction/destruction/initialization
        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        public PdMasterTailorDataStruct()
        {
            m_BlockDataWrapper = new BlockDataWrapper<PdMasterTailorDataStructEnum, ushort>(3);

            ParaBlockType = 0x7081;

            ParaBlockVersion = 256;

            //Lenght of block without BlockType and BlockLength. -> 6 - 4 = 2
            ParaBlockLength = 2;
        }
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Public methods
        // Contains all public methods of the class
        public void SetBlockElement(PdMasterTailorDataStructEnum portMasterTailorDataElement, ushort valueToSet)
        {
            m_BlockDataWrapper.SetBlockElement(portMasterTailorDataElement, valueToSet);
        }
        public void AddPortMasterTailorData(BlockDataWrapper<PortMasterTailorDataEnum, ushort> portMasterTailorData)
        {
            m_PortMasterTailorData.Add(portMasterTailorData);
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region I... members
        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Overrides and overridables
        // Contains all public and protected overrides as well as overridables of the class

        public override byte[] ToByteArray
        {
            get
            {
                if (!m_SubblockAdded)
                {
                    AddSubblock(m_BlockDataWrapper.ToByteArray);
                    foreach (var item in m_PortMasterTailorData)
                    {
                        AddSubblock(item.ToByteArray);
                    }
                    m_SubblockAdded = true;
                }
                return base.ToByteArray;
            }
        }
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Protected methods
        // Contains all protected (non overridables) methods of the class
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Private implementation
        // Contains the private implementation of the class
        #endregion
    }
}