/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIoPortBusinessLogic.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options;

#endregion

namespace PNConfigLib.HWCNBL.Port
{
    internal class PNIoPortBusinessLogic : PortDecorator
    {
        public PNIoPortBusinessLogic(IPortBL decoratedPort) : base(decoratedPort)
        {
            InitBL();
        }
        public override void InitBL()
        {
            InitAttributes();
            InitActions();
        }
        protected void InitAttributes()
        {
            Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoProgrammablePeer, false);
        }
        private void InitActions()
        {
           ConsistencyManager.RegisterConsistencyCheck(Port, ConsistencyCheckPortProgrammablePeer);
        }

        /// <summary>
        /// Check Programmable Peer PDEV consistency for this port.
        /// </summary>
        private void ConsistencyCheckPortProgrammablePeer()
        {
            ProgrammablePeerConsistencyChecker.CheckPdevConsistency(Port);
        }
    }
}