/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AttributeAccessCode.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// The return code indicating the result of a get or set operation on attributes.
    /// </summary>
    public class AttributeAccessCode
    {
        //########################################################################################

        #region Fields

        /// <summary>
        /// The field for storing the access code.
        /// </summary>
        private AccessCode m_Code = AccessCode.Okay;

        #endregion

        //########################################################################################

        #region Constants and Enums

        /// <summary>
        /// Enumeration of attribute access code values.
        /// </summary>
        public enum AccessCode
        {
            /// <summary>
            /// Attribute is accessed.
            /// </summary>
            Okay = 0,

            /// <summary>
            /// Attribute value is invalid.
            /// </summary>
            InvalidValue,

            /// <summary>
            /// Attribute is not of requested type.
            /// </summary>
            InvalidType
        }

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// Access code.
        /// </summary>
        public AccessCode Code
        {
            get { return m_Code; }
        }

        /// <summary>
        /// Whether the access code is okay.
        /// </summary>
        public bool IsOkay
        {
            get { return m_Code == AccessCode.Okay; }
        }

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        #endregion

        //########################################################################################

        #region Public Methods

        /// <summary>
        /// Get the attribute access code object as new.
        /// </summary>
        /// <remarks>Calls Reset() and returns this object.</remarks>
        /// <returns>This AttributeAccessCode object.</returns>
        public AttributeAccessCode GetNew()
        {
            Reset();
            return this;
        }

        /// <summary>
        /// Resets the access code to Okay.
        /// </summary>
        public void Reset()
        {
            m_Code = AccessCode.Okay;
        }

        /// <summary>
        /// Set the access code, suppressing lower access codes.
        /// In other words, the method accepts worse codes only.
        /// </summary>
        /// <param name="code">New access code to set.</param>
        public void Set(AccessCode code)
        {
            if (m_Code < code)
            {
                m_Code = code;
            }
        }

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion
    }
}