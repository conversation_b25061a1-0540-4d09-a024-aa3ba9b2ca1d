<?xml version="1.0" encoding="utf-8"?>
<ISO15745Profile xsi:schemaLocation="http://www.profibus.com/GSDML/2003/11/DeviceProfile ..\XSD\GSDML-DeviceProfile-V2.34.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.profibus.com/GSDML/2003/11/DeviceProfile">
  <ProfileHeader>
    <ProfileIdentification>PROFINET Device Profile</ProfileIdentification>
    <ProfileRevision>1.00</ProfileRevision>
    <ProfileName>Device Profile for PROFINET Devices</ProfileName>
    <ProfileSource>PROFIBUS Nutzerorganisation e. V. (PNO)</ProfileSource>
    <ProfileClassID>Device</ProfileClassID>
    <ISO15745Reference>
      <ISO15745Part>4</ISO15745Part>
      <ISO15745Edition>1</ISO15745Edition>
      <ProfileTechnology>GSDML</ProfileTechnology>
    </ISO15745Reference>
  </ProfileHeader>
  <ProfileBody>
    <DeviceIdentity VendorID="0x002A" DeviceID="0x031B">
      <!-- 设备标识:供应商ID-U16H PI分配，设备ID-U16H 供应商分配-->
      <InfoText TextId="T_DAP_DEVICE_DESC"/>
      <!--设备描述 Infomation 引用：外部文本列表=PROFINET IO Device Adapter-->
      <VendorName Value="SIEMENS"/>
      <!--供应商名称-->
    </DeviceIdentity>
    <DeviceFunction>
      <!-- 设备功能 -->
      <Family MainFamily="I/O" ProductFamily="ET 200 SMART"/>
    </DeviceFunction>
    <ApplicationProcess>
      <!-- 应用进程 -->
      <DeviceAccessPointList>
        <!--设备访问点列表-->

        <DeviceAccessPointItem CheckDeviceID_Allowed="true"
                       ID="DAP1"
                       ModuleIdentNumber="0xababab60"
                       DNS_CompatibleName="ET200SMART"
                       PhysicalSlots="0..33"
                       FixedInSlots="0"
                       DeviceAccessSupported="true"
                       NumberOfDeviceAccessAR="1"
                       ObjectUUID_LocalIndex="100"
                       RequiredSchemaVersion="V2.32"
                       MinDeviceInterval="128"
                       MaxSupportedRecordSize="4068"
                       NameOfStationNotTransferable="false"
                       AddressAssignment="DCP"
                       MultipleWriteSupported="true"
                       LLDP_NoD_Supported="true"
                       SharedDeviceSupported="false"
                       ResetToFactoryModes="2"
                       PNIO_Version="V2.33"
                       PowerOnToCommReady="2000">
          <ModuleInfo>
            <Name TextId="DAP_Name_ID"/>
            <InfoText TextId="T_DAP_DEVICE_DESC"/>
            <VendorName Value ="SIEMENS" />
            <OrderNumber Value="6ES7 288-4RM60-0AA0"/>
            <HardwareRelease Value="1"/>
            <SoftwareRelease Value="V1.0"/>
          </ModuleInfo>
          <CertificationInfo ConformanceClass="A" ApplicationClass="" NetloadClass="I"/>
          <!--认证信息-->
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="Interface_SubslotLabel"/>
            <SubslotItem SubslotNumber="32769" TextId="Port32769_SubslotLabel"/>
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440"/>

          <UseableModules>
            <!--可使用的模块列表 -->
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AM06"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DT32"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AE08"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AQ04"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DE16"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_QR16"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AR02"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AR04"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AT04"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DE08"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DT08"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DR08"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DT16"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DR16"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_DR32"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AE04 V1.0"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AE04 V1.1"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AQ02"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_AM03"/>
            <ModuleItemRef AllowedInSlots="1..6" ModuleItemTarget ="EM_QT16"/>

          </UseableModules>

          <VirtualSubmoduleList>
            <!--虚拟子模块列表-->
            <VirtualSubmoduleItem ID="DAP1" SubmoduleIdentNumber="0x10000001" FixedInSubslots="1" MayIssueProcessAlarm="false" Writeable_IM_Records="1 2 3">
              <IOData/>
              <ModuleInfo>
                <Name TextId="DAP1_Name"/>
                <InfoText TextId="DAP1_Comment"/>
              </ModuleInfo>
            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="Onboard_Inputs" SubmoduleIdentNumber="0x00000001" FixedInSubslots="1001" MayIssueProcessAlarm="false">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_Inputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_Inputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_Inputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_Inputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_Inputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_Inputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_Inputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_Inputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_Inputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_Inputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_Inputs"/>
                  </DataItem>

                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="36">
                  <Name TextId ="TextId_OnboardDigitalInputs"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_0"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_1"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_2"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_3"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_4"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_5"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_6"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_0_7"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="8"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_0"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="9"  DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_1"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="10" DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_2"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="11" DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_3"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="12" DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_4"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="13" DefaultValue="12" AllowedValues="0..13" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_5"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="14" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_6"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="15" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_1_7"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="16" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_0"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="17" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_1"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="18" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_2"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="19" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_3"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="20" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_4"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="21" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_5"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="22" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_6"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="23" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_2_7"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="24" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_0"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="25" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_1"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="26" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_2"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="27" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_3"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="28" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_4"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="29" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_5"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="30" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_6"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="31" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_3_7"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="32" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_4_0"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="33" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_4_1"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="34" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_4_2"/>
                  <Ref  ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset ="35" DefaultValue="12" AllowedValues="12..14" Changeable="true" Visible="true" TextId="OnboardDigitalInput_Filter_I_4_3"/>

                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="Onboard_Inputs_Name"/>
                <InfoText TextId="Onboard_Inputs_Comment"/>
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="Onboard_outputs" SubmoduleIdentNumber="0x00000002" FixedInSubslots="1002" MayIssueProcessAlarm="false">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_outputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_outputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_outputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_outputs"/>
                  </DataItem>

                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="Onboard_outputs">
                    <BitDataItem BitOffset="0" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="1" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="2" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="3" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="4" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="5" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="6" TextId="Onboard_outputs"/>
                    <BitDataItem BitOffset="7" TextId="Onboard_outputs"/>
                  </DataItem>

                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="24">
                  <Name TextId="TextId_OnboardDigitaloutputs" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_0"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_1"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_2"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_3"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_4"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_5"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_6"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_7"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="8"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_0"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="9"   DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_1"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="10"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_2"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="11"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_3"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="12"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_4"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="13"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_5"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="14"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_6"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="15"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_7"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="16"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_0"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="17"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_1"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="18"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_2"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="19"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_3"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="20"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_4"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="21"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_5"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="22"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_6"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="23"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_7"/>

                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="Onboard_outputs_Name"/>
                <InfoText TextId="Onboard_outputs_Comment"/>
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="Interface" SubslotNumber="32768" SubmoduleIdentNumber="0xf00" TextId="Interface" ParameterizationDisallowed="true" SupportedProtocols="LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" SupportedRT_Classes="RT_CLASS_1">
              <ApplicationRelations StartupMode="Advanced;Legacy" NumberOfAR="1">
                <TimingProperties SendClock="32" ReductionRatio="1 2 4 8 16 32 64 128 256 512" ReductionRatioPow2="1 2 4 8 16 32 64 128 256 512"/>
              </ApplicationRelations>
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="Port_1" TextId="Port_1" SubslotNumber="32769" SubmoduleIdentNumber="0xf01" ParameterizationDisallowed="true" PortDeactivationSupported="false" MAUTypes="16" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" LinkStateDiagnosisCapability="Up+Down">
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true"/>
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="1"/>
          </Graphics>
        </DeviceAccessPointItem>
      </DeviceAccessPointList>

      <ModuleList>
        <ModuleItem ID="EM_AM06" ModuleIdentNumber="0x80003009">
          <ModuleInfo CategoryRef="EM_CAT_AI_AQ">
            <Name TextId="EM_AM06_Name"/>
            <InfoText TextId="EM_AM06_Info"/>
            <OrderNumber Value="6ES7 288-3AM06-0AA0" />
            <HardwareRelease Value="0x08" />
            <SoftwareRelease Value="V1.0" />

          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AM06_Module_Parameters" MayIssueProcessAlarm="false" FixedInSubslots="1000" SubmoduleIdentNumber="0x00000003">
              <IOData />

              <RecordDataList>
                <ParameterRecordDataItem Length="1" Index="1">
                  <Name TextId="EM_AM06_Module_Parameters" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM06_Module_Parameters" />
                <InfoText TextId="EM_AM06_Param_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_AM06_AI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AI_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AI_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AI_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AI_CH_3" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="11" Index="1">
                  <Name TextId="EM_AM06_AI_Name" />
                  <Ref ValueItemTarget="EM_AI_Combined_4Channel_Rejection" DataType="Unsigned8" ByteOffset="0" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Combined_4Channel_Rejection_Cfg" ID="EM_AI_Combined_4Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="1" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg"/>

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="3" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" ID="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="5" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" ID="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="6" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="6" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="7" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" ID="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="9" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" ID="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="10" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="10" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AM06_AI_Name" />
                      <ParameterRef ParameterTarget="EM_AI_Combined_4Channel_Rejection_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" />

                      <MenuRef MenuTarget="EM_AM_AI_Channel_0" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_1" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_2" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_3" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_0">
                      <Name TextId="EM_AM06_AI_CH_0" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_1">
                      <Name TextId="EM_AM06_AI_CH_1" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_2">
                      <Name TextId="EM_AM06_AI_CH_2" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_3">
                      <Name TextId="EM_AM06_AI_CH_3" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM06_AI_Name" />
                <InfoText TextId="EM_AM06_Input_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_AM06_AQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AQ_CH_0"/>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM06_AQ_CH_1"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="9" Index="1">
                  <Name TextId="EM_AM06_AQ_Name" />
                  <Ref ValueItemTarget="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior" DataType="Unsigned8" ByteOffset="0" DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" ID="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="1" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch0_Range_Cfg" ID="EM_AQ_Mesurement_ch0_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="2" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch0_Value_Cfg" ID="EM_AQ_Substitute_ch0_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="5" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch1_Range_Cfg" ID="EM_AQ_Mesurement_ch1_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="6" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch1_Value_Cfg" ID="EM_AQ_Substitute_ch1_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="8" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />
                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AM06_AQ_Name" />
                      <ParameterRef ParameterTarget="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" />
                      <MenuRef MenuTarget="EM_AM06_AQ_Channel_0" />
                      <MenuRef MenuTarget="EM_AM06_AQ_Channel_1" />
                    </MenuItem>

                    <MenuItem ID="EM_AM06_AQ_Channel_0">
                      <Name TextId="EM_AM06_AQ_CH_0" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch0_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch0_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM06_AQ_Channel_1">
                      <Name TextId="EM_AM06_AQ_CH_1" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch1_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch1_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM06_AQ_Name" />
                <InfoText TextId="EM_AM06_Output_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DT32" ModuleIdentNumber="0x80003005">
          <ModuleInfo CategoryRef="EM_CAT_DI_DQ">
            <Name TextId="EM_DT32_Name"/>
            <InfoText TextId="EM_DT32_Info"/>
            <OrderNumber Value="6ES7 288-2DT32-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DT32_DI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_DT32_CH_DI_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="4" Index="1">
                  <Name TextId="EM_DT32_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_1_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="2" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_2_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="3" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_3_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DT32_DI_Name" />
                <InfoText TextId="EM_DT32_Input_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_DT32_DQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_DT32_CH_DQ_ALL"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="16" Index="1">
                  <Name TextId="EM_DT32_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"/>

                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="8"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch8_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="9"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch9_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="10"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch10_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="11"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch11_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="12"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch12_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="13"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch13_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="14"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch14_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="15"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch15_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DT32_DQ_Name" />
                <InfoText TextId="EM_DT32_Output_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_AE08" ModuleIdentNumber="0x80003013">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AE08_Name"/>
            <InfoText TextId="EM_AE08_Info"/>
            <OrderNumber Value="6ES7 288-3AE08-0AA0" />
            <HardwareRelease Value="0x03" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AE08_AI" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_3" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_4" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_5" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_6" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE08_CH_7" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="22" Index="1">
                  <Name TextId="EM_AE08_AI_Name" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AI_Combined_4Channel_Rejection" DataType="Unsigned8" ByteOffset="1" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Combined_8Channel_Rejection_Cfg" ID="EM_AI_Combined_8Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="3" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_4_5_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="4" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_4_5_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_4_5_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_6_7_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="5" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_6_7_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_6_7_Mesurement_Type_Range_Cfg"/>

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="6" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" ID="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="8" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" ID="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="10" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" ID="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="12" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" ID="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="14" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch4_Smoothing_Cfg" ID="EM_AI_Mesurement_ch4_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="15" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch4_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch4_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="15" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch4_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch4_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="16" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch5_Smoothing_Cfg" ID="EM_AI_Mesurement_ch5_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch5_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch5_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch5_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch5_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="18" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch6_Smoothing_Cfg" ID="EM_AI_Mesurement_ch6_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="19" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch6_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch6_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="19" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch6_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch6_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="20" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch7_Smoothing_Cfg" ID="EM_AI_Mesurement_ch7_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="21" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch7_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch7_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="21" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch7_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch7_En_Lower_Limit_Alarm_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AE_Configuration">
                      <Name TextId="EM_AE08_AI_Name" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile" />
                      <ParameterRef ParameterTarget="EM_AI_Combined_8Channel_Rejection_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_4_5_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_6_7_Mesurement_Type_Range_Cfg" />

                      <MenuRef MenuTarget="EM_AM_AI_Channel_0" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_1" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_2" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_3" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_4" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_5" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_6" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_7" />

                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_0">
                      <Name TextId="EM_AE08_CH_0" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_1">
                      <Name TextId="EM_AE08_CH_1" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_2">
                      <Name TextId="EM_AE08_CH_2" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_3">
                      <Name TextId="EM_AE08_CH_3" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_4">
                      <Name TextId="EM_AE08_CH_4" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch4_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch4_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch4_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_5">
                      <Name TextId="EM_AE08_CH_5" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch5_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch5_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch5_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_6">
                      <Name TextId="EM_AE08_CH_6" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch6_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch6_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch6_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_7">
                      <Name TextId="EM_AE08_CH_7" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch7_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch7_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch7_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AE08_Name" />
                <InfoText TextId="EM_AE08_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_AQ04" ModuleIdentNumber="0x80003015">
          <ModuleInfo CategoryRef="EM_CAT_AQ">
            <Name TextId="EM_AQ04_Name"/>
            <InfoText TextId="EM_AQ04_Info"/>
            <OrderNumber Value="6ES7 288-3AQ04-0AA0" />
            <HardwareRelease Value="0x06" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AQ04" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ04_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ04_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ04_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ04_CH_3" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="18" Index="1">
                  <Name TextId="EM_AQ04_AQ_Name" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior" DataType="Unsigned8" ByteOffset="1" DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_Cfg" ID="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch0_Range_Cfg" ID="EM_AQ_Mesurement_ch0_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="3" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch0_Value_Cfg" ID="EM_AQ_Substitute_ch0_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="6" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch1_Range_Cfg" ID="EM_AQ_Mesurement_ch1_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="7" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch1_Value_Cfg" ID="EM_AQ_Substitute_ch1_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="10" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch2_Range_Cfg" ID="EM_AQ_Mesurement_ch2_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="11" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch2_Value_Cfg" ID="EM_AQ_Substitute_ch2_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch2_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch2_Cfg" />
                  <Ref DataType="Bit" ByteOffset="13" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch2_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch2_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="14" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch3_Range_Cfg" ID="EM_AQ_Mesurement_ch3_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="15" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch3_Value_Cfg" ID="EM_AQ_Substitute_ch3_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch3_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch3_Cfg" />
                  <Ref DataType="Bit" ByteOffset="17" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch3_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch3_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AQ_Configuration">
                      <Name TextId="EM_AQ04_AQ_Name" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile" />
                      <ParameterRef ParameterTarget="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_Cfg" />
                      <MenuRef MenuTarget="EM_AQ04_AQ_Channel_0" />
                      <MenuRef MenuTarget="EM_AQ04_AQ_Channel_1" />
                      <MenuRef MenuTarget="EM_AQ04_AQ_Channel_2" />
                      <MenuRef MenuTarget="EM_AQ04_AQ_Channel_3" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ04_AQ_Channel_0">
                      <Name TextId="EM_AQ04_CH_0" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch0_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch0_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ04_AQ_Channel_1">
                      <Name TextId="EM_AQ04_CH_1" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch1_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch1_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ04_AQ_Channel_2">
                      <Name TextId="EM_AQ04_CH_2" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch2_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch2_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch2_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch2_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ04_AQ_Channel_3">
                      <Name TextId="EM_AQ04_CH_3" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch3_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch3_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch3_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch3_Cfg" />
                    </MenuItem>


                  </MenuList>

                </ParameterRecordDataItem>
              </RecordDataList>


              <ModuleInfo>
                <Name TextId="EM_AQ04_AQ_Name" />
                <InfoText TextId="EM_AQ04_Info" />
              </ModuleInfo>


            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DE16" ModuleIdentNumber="0x80003016">
          <ModuleInfo CategoryRef="EM_CAT_DI">
            <Name TextId="EM_DE16_Name"/>
            <InfoText TextId="EM_DE16_Info"/>
            <OrderNumber Value="6ES7 288-2DE16-0AA0" />
            <HardwareRelease Value="0x03" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DE16" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_DE16_CH_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="4" Index="1">
                  <Name TextId="EM_DE16_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_1_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="2" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_2_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="3" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_3_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DE16_DI_Name" />
                <InfoText TextId="EM_DE16_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_QR16" ModuleIdentNumber="0x80003018">
          <ModuleInfo CategoryRef="EM_CAT_DQ">
            <Name TextId="EM_QR16_Name"/>
            <InfoText TextId="EM_QR16_Info"/>
            <OrderNumber Value="6ES7 288-2QR16-0AA0" />
            <HardwareRelease Value="0x02" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_QR16" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_QR16_CH_ALL" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="16" Index="1">
                  <Name TextId="EM_QR16_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"/>

                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="8"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch8_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="9"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch9_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="10"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch10_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="11"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch11_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="12"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch12_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="13"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch13_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="14"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch14_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="15"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch15_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_QR16_DQ_Name" />
                <InfoText TextId="EM_QR16_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_AR02" ModuleIdentNumber="0x80003010">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AR02_Name"/>
            <InfoText TextId="EM_AR02_Info"/>
            <OrderNumber Value="6ES7 288-3AR02-0AA0" />
            <HardwareRelease Value="0x05" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AR02" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR02_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR02_CH_1" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Length="14" Index="1">
                  <Name TextId="VM_EM_AR_Cfg_Title" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AR_Combined_2Channel_Rejection" DataType="Unsigned8" DefaultValue="1" ByteOffset="1" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Combined_2Channel_Rejection_Cfg" ID="EM_AR_Combined_2Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="2" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="6" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch0_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="8" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="12" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch1_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg" />

                  <MenuList>
                    <MenuItem ID="VM_EM_AR02_Cfg">
                      <Name TextId="VM_EM_AR_Cfg_Title" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile"/>
                      <ParameterRef ParameterTarget="EM_AR_Combined_2Channel_Rejection_Cfg"/>

                      <MenuRef MenuTarget="VM_EM_AR02_Ch0_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AR02_Ch1_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR02_Ch0_Cfg">
                      <Name TextId="EM_AR02_CH_0" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR02_Ch1_Cfg">
                      <Name TextId="EM_AR02_CH_1" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg"/>
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>
              <ModuleInfo>
                <Name TextId="EM_AR02_Name" />
                <InfoText TextId="EM_AR02_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_AR04" ModuleIdentNumber="0x80003014">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AR04_Name"/>
            <InfoText TextId="EM_AR04_Info"/>
            <OrderNumber Value="6ES7 288-3AR04-0AA0" />
            <HardwareRelease Value="0x03" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AR04" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR04_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR04_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR04_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AR04_CH_3" />
                </Input>
              </IOData>


              <RecordDataList>
                <ParameterRecordDataItem Length="26" Index="1">
                  <Name TextId="VM_EM_AR_Cfg_Title" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AR_Combined_4Channel_Rejection" DataType="Unsigned8" DefaultValue="1" ByteOffset="1" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Combined_4Channel_Rejection_Cfg" ID="EM_AR_Combined_4Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="2" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="6" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch0_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="8" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="12" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch1_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg" />

                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="14" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_2_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="18" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch2_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch2_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch2_Cfg" />

                  <Ref ValueItemTarget="EM_AR_Type_Resistor_Coef_Scale" DataType="Unsigned32" DefaultValue="1048575" ByteOffset="20" Changeable="true" Visible="true" AllowedValues="1048575 1114111 1179647 1245183 1310719 17825791 17891327 17956863 18022399 18087935 34603007 34668543 34734079 34799615 34865151 50331648 50331649 50331904 50331905 50397184 50397185 50397440 50397441 50462720 50462721 50463232 50463233 50463488 50463489 50463744 50463745 50462976 50462977 50528256 50528257 50528768 50528769 50529024 50529025 50529280 50529281 50593792 50593793 50594304 50594305 50594560 50594561 50594816 50594817 50594048 50594049 50659328 50659329 50659840 50659841 50660096 50660097 50660352 50660353 50726144 50726145 50726400 50726401 50726656 50726657 50791936 50791937 50792192 50792193 50857472 50857473 50857728 50857729 50923008 50923009 50923264 50923265 50988544 50988545 50988800 50988801 51054592 51054593 51054848 51054849 51055104 51055105 51120128 51120129 51120384 51120385 51185664 51185665 51185920 51185921 51251968 51251969 67108864 67108865 67109120 67109121 67174400 67174401 67174656 67174657 67239936 67239937 67240448 67240449 67240704 67240705 67240960 67240961 67240192 67240193 67305472 67305473 67305984 67305985 67306240 67306241 67306496 67306497 67371008 67371009 67371520 67371521 67371776 67371777 67372032 67372033 67371264 67371265 67436544 67436545 67437056 67437057 67437312 67437313 67437568 67437569 67503360 67503361 67503616 67503617 67503872 67503873 67569152 67569153 67569408 67569409 67634688 67634689 67634944 67634945 67700224 67700225 67700480 67700481 67765760 67765761 67766016 67766017 67831808 67831809 67832064 67832065 67832320 67832321 67897344 67897345 67897600 67897601 67962880 67962881 67963136 67963137 68029184 68029185 83886080 83886081 83886336 83886337 83951616 83951617 83951872 83951873 84017152 84017153 84017664 84017665 84017920 84017921 84018176 84018177 84017408 84017409 84082688 84082689 84083200 84083201 84083456 84083457 84083712 84083713 84148224 84148225 84148736 84148737 84148992 84148993 84149248 84149249 84148480 84148481 84213760 84213761 84214272 84214273 84214528 84214529 84214784 84214785 84280576 84280577 84280832 84280833 84281088 84281089 84346368 84346369 84346624 84346625 84411904 84411905 84412160 84412161 84477440 84477441 84477696 84477697 84542976 84542977 84543232 84543233 84609024 84609025 84609280 84609281 84609536 84609537 84674560 84674561 84674816 84674817 84740096 84740097 84740352 84740353 84806400 84806401" TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" ID="EM_AR_Type_Resistor_Coef_Scale_Ch_3_Cfg" />
                  <Ref ValueItemTarget="EM_AR_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="24" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AR_Mesurement_Smoothing_Ch3_Cfg" ID="EM_AR_Mesurement_Smoothing_Ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AR_Enable_WireBreak_Alarm_ch3_Cfg" ID="EM_AR_Enable_WireBreak_Alarm_ch3_Cfg" />

                  <MenuList>
                    <MenuItem ID="VM_EM_AR04_Cfg">
                      <Name TextId="VM_EM_AR_Cfg_Title" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile"/>
                      <ParameterRef ParameterTarget="EM_AR_Combined_4Channel_Rejection_Cfg"/>

                      <MenuRef MenuTarget="VM_EM_AR04_Ch0_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AR04_Ch1_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AR04_Ch2_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AR04_Ch3_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR04_Ch0_Cfg">
                      <Name TextId="EM_AR04_CH_0" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR04_Ch1_Cfg">
                      <Name TextId="EM_AR04_CH_1" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR04_Ch2_Cfg">
                      <Name TextId="EM_AR04_CH_2" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch2_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AR04_Ch3_Cfg">
                      <Name TextId="EM_AR04_CH_3" />
                      <ParameterRef ParameterTarget="EM_AR_Type_Resistor_Coef_Scale_Ch_3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Mesurement_Smoothing_Ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AR_Enable_WireBreak_Alarm_ch3_Cfg"/>
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>


              <ModuleInfo>
                <Name TextId="EM_AR04_Name" />
                <InfoText TextId="EM_AR04_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_AT04" ModuleIdentNumber="0x80003011">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AT04_Name"/>
            <InfoText TextId="EM_AT04_Info"/>
            <OrderNumber Value="6ES7 288-3AT04-0AA0" />
            <HardwareRelease Value="0x05" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AT04" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AT04_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AT04_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AT04_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AT04_CH_3" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="26" Index="1">
                  <Name TextId="VM_EM_AT_Cfg_Title" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AT_Combined_4Channel_Rejection" DataType="Unsigned8" DefaultValue="1" ByteOffset="1" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AT_Combined_4Channel_Rejection_Cfg" ID="EM_AT_Combined_4Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AT_Type_Therm_Scale_Src" DataType="Unsigned32" DefaultValue="0" ByteOffset="2" Changeable="true" Visible="true" AllowedValues="0 1 2 256 257 258 65536 65537 65538 65792 65793 65794 131072 131073 131074 131328 131329 131330 196608 196609 196610 196864 196865 196866 262144 262145 262146 262400 262401 262402 327680 327681 327682 327936 327937 327938 393216 393217 393218 393472 393473 393474 458752 458753 458754 459008 459009 459010 524288 524289 524290 524544 524545 524546 589824 589825 589826 590080 590081 590082 17498111" TextId="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg" ID="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg" />
                  <Ref ValueItemTarget="EM_AT_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="6" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AT_Mesurement_Smoothing_Ch0_Cfg" ID="EM_AT_Mesurement_Smoothing_Ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref ByteOffset="7" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AT_Type_Therm_Scale_Src" DataType="Unsigned32" DefaultValue="0" ByteOffset="8" Changeable="true" Visible="true" AllowedValues="0 1 2 256 257 258 65536 65537 65538 65792 65793 65794 131072 131073 131074 131328 131329 131330 196608 196609 196610 196864 196865 196866 262144 262145 262146 262400 262401 262402 327680 327681 327682 327936 327937 327938 393216 393217 393218 393472 393473 393474 458752 458753 458754 459008 459009 459010 524288 524289 524290 524544 524545 524546 589824 589825 589826 590080 590081 590082 17498111" TextId="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg" ID="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg" />
                  <Ref ValueItemTarget="EM_AT_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="12" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AT_Mesurement_Smoothing_Ch1_Cfg" ID="EM_AT_Mesurement_Smoothing_Ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref ByteOffset="13" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AT_Enable_WireBreak_Alarm_ch1_Cfg" />

                  <Ref ValueItemTarget="EM_AT_Type_Therm_Scale_Src" DataType="Unsigned32" DefaultValue="0" ByteOffset="14" Changeable="true" Visible="true" AllowedValues="0 1 2 256 257 258 65536 65537 65538 65792 65793 65794 131072 131073 131074 131328 131329 131330 196608 196609 196610 196864 196865 196866 262144 262145 262146 262400 262401 262402 327680 327681 327682 327936 327937 327938 393216 393217 393218 393472 393473 393474 458752 458753 458754 459008 459009 459010 524288 524289 524290 524544 524545 524546 589824 589825 589826 590080 590081 590082 17498111" TextId="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg" ID="EM_AT_Type_Therm_Scale_Src_Ch_2_Cfg" />
                  <Ref ValueItemTarget="EM_AT_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="18" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AT_Mesurement_Smoothing_Ch2_Cfg" ID="EM_AT_Mesurement_Smoothing_Ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" ID="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" />
                  <Ref ByteOffset="19" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AT_Enable_WireBreak_Alarm_ch2_Cfg" ID="EM_AT_Enable_WireBreak_Alarm_ch2_Cfg" />

                  <Ref ValueItemTarget="EM_AT_Type_Therm_Scale_Src" DataType="Unsigned32" DefaultValue="0" ByteOffset="20" Changeable="true" Visible="true" AllowedValues="0 1 2 256 257 258 65536 65537 65538 65792 65793 65794 131072 131073 131074 131328 131329 131330 196608 196609 196610 196864 196865 196866 262144 262145 262146 262400 262401 262402 327680 327681 327682 327936 327937 327938 393216 393217 393218 393472 393473 393474 458752 458753 458754 459008 459009 459010 524288 524289 524290 524544 524545 524546 589824 589825 589826 590080 590081 590082 17498111" TextId="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg" ID="EM_AT_Type_Therm_Scale_Src_Ch_3_Cfg" />
                  <Ref ValueItemTarget="EM_AT_Mesurement_Smoothing" DataType="Unsigned8" DefaultValue="1" ByteOffset="24" Changeable="true" Visible="true" AllowedValues="0..3" TextId="EM_AT_Mesurement_Smoothing_Ch3_Cfg" ID="EM_AT_Mesurement_Smoothing_Ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="0" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="1" DataType="Bit" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" ID="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" />
                  <Ref ByteOffset="25" BitOffset="2" DataType="Bit" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AT_Enable_WireBreak_Alarm_ch3_Cfg" ID="EM_AT_Enable_WireBreak_Alarm_ch3_Cfg" />

                  <MenuList>
                    <MenuItem ID="VM_EM_AT04_Cfg">
                      <Name TextId="VM_EM_AT_Cfg_Title" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile"/>
                      <ParameterRef ParameterTarget="EM_AT_Combined_4Channel_Rejection_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AT04_Ch0_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AT04_Ch1_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AT04_Ch2_Cfg"/>
                      <MenuRef MenuTarget="VM_EM_AT04_Ch3_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AT04_Ch0_Cfg">
                      <Name TextId="EM_AT04_CH_0" />
                      <ParameterRef ParameterTarget="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Mesurement_Smoothing_Ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AT04_Ch1_Cfg">
                      <Name TextId="EM_AT04_CH_1" />
                      <ParameterRef ParameterTarget="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Mesurement_Smoothing_Ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_WireBreak_Alarm_ch1_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AT04_Ch2_Cfg">
                      <Name TextId="EM_AT04_CH_2" />
                      <ParameterRef ParameterTarget="EM_AT_Type_Therm_Scale_Src_Ch_2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Mesurement_Smoothing_Ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_WireBreak_Alarm_ch2_Cfg"/>
                    </MenuItem>

                    <MenuItem ID="VM_EM_AT04_Ch3_Cfg">
                      <Name TextId="EM_AT04_CH_3" />
                      <ParameterRef ParameterTarget="EM_AT_Type_Therm_Scale_Src_Ch_3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Mesurement_Smoothing_Ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg"/>
                      <ParameterRef ParameterTarget="EM_AT_Enable_WireBreak_Alarm_ch3_Cfg"/>
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="EM_AT04_Name" />
                <InfoText TextId="EM_AT04_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DE08" ModuleIdentNumber="0x80003000">
          <ModuleInfo CategoryRef="EM_CAT_DI">
            <Name TextId="EM_DE08_Name"/>
            <InfoText TextId="EM_DE08_Info"/>
            <OrderNumber Value="6ES7 288-2DE08-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DE08" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DE08_CH_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="2" Index="1">
                  <Name TextId="EM_DE08_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_1_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>


              <ModuleInfo>
                <Name TextId="EM_DE08_DI_Name" />
                <InfoText TextId="EM_DE08_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DT08" ModuleIdentNumber="0x80003001">
          <ModuleInfo CategoryRef="EM_CAT_DQ">
            <Name TextId="EM_DT08_Name"/>
            <InfoText TextId="EM_DT08_Info"/>
            <OrderNumber Value="6ES7 288-2DT08-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DT08" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DT08_CH_ALL" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="8" Index="1">
                  <Name TextId="EM_DT08_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DT08_DQ_Name" />
                <InfoText TextId="EM_DT08_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DR08" ModuleIdentNumber="0x80003002">
          <ModuleInfo CategoryRef="EM_CAT_DQ">
            <Name TextId="EM_DR08_Name"/>
            <InfoText TextId="EM_DR08_Info"/>
            <OrderNumber Value="6ES7 288-2DR08-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DR08" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DR08_CH_ALL" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="8" Index="1">
                  <Name TextId="EM_DR08_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DR08_DQ_Name" />
                <InfoText TextId="EM_DR08_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_QT16" ModuleIdentNumber="0x80003017">
          <ModuleInfo CategoryRef="EM_CAT_DQ">
            <Name TextId="EM_QT16_Name"/>
            <InfoText TextId="EM_QT16_Info"/>
            <OrderNumber Value="6ES7 288-2QT16-0AA0" />
            <HardwareRelease Value="0x02" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_QT16_DQ" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_QT16_CH_ALL" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="16" Index="1">
                  <Name TextId="EM_QT16_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"/>

                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="8"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch8_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="9"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch9_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="10"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch10_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="11"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch11_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="12"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch12_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="13"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch13_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="14"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch14_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="15"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch15_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_QT16_DQ_Name" />
                <InfoText TextId="EM_QT16_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_DT16" ModuleIdentNumber="0x80003003">
          <ModuleInfo CategoryRef="EM_CAT_DI_DQ">
            <Name TextId="EM_DT16_Name"/>
            <InfoText TextId="EM_DT16_Info"/>
            <OrderNumber Value="6ES7 288-2DT16-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DT16_DI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DT16_CH_DI_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="2" Index="1">
                  <Name TextId="EM_DT16_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DT_DI_Ch_Grp_1_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DT16_DI_Name" />
                <InfoText TextId="EM_DT16_Input_Info" />
              </ModuleInfo>


            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_DT16_DQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DT16_CH_DQ_ALL"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="8" Index="1">
                  <Name TextId="EM_DT16_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DT16_DQ_Name" />
                <InfoText TextId="EM_DT16_Output_Info" />
              </ModuleInfo>


            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_DR16" ModuleIdentNumber="0x80003004">
          <ModuleInfo CategoryRef="EM_CAT_DI_DQ">
            <Name TextId="EM_DR16_Name"/>
            <InfoText TextId="EM_DR16_Info"/>
            <OrderNumber Value="6ES7 288-2DR16-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DR16_DI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DR16_CH_DI_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="2" Index="1">
                  <Name TextId="EM_DR16_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_1_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DR16_DI_Name" />
                <InfoText TextId="EM_DR16_Input_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_DR16_DQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned8" UseAsBits="true" TextId="EM_DR16_CH_DQ_ALL"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="8" Index="1">
                  <Name TextId="EM_DR16_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DR16_DQ_Name" />
                <InfoText TextId="EM_DR16_Output_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_DR32" ModuleIdentNumber="0x80003006">
          <ModuleInfo CategoryRef="EM_CAT_DI_DQ">
            <Name TextId="EM_DR32_Name"/>
            <InfoText TextId="EM_DR32_Info"/>
            <OrderNumber Value="6ES7 288-2DR32-0AA0" />
            <HardwareRelease Value="0x04" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_DR32_DI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_DR32_CH_DI_ALL" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="4" Index="1">
                  <Name TextId="EM_DR32_DI_Name" />
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="0" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_0_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="1" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_1_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="2" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_2_Input_Filter"/>
                  <Ref ValueItemTarget="IDV_Input_Filter_Enum" DataType="Unsigned8" ByteOffset="3" DefaultValue="12" AllowedValues="7..13" Changeable="true" Visible="true" TextId="EM_DR_DI_Ch_Grp_3_Input_Filter"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DR32_DI_Name" />
                <InfoText TextId="EM_DR32_Input_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_DR32_DQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="true" TextId="EM_DR32_CH_DQ_ALL"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="16" Index="1">
                  <Name TextId="EM_DR32_DQ_Name" />
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="0"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="1"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="2"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="3"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="4"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="5"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="6"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="7"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"/>

                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="8"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch8_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="9"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch9_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="10"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch10_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="11"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch11_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="12"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch12_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="13"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch13_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="14"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch14_Reaction_TO_CPUSTOP"/>
                  <Ref ValueItemTarget="IDV_OutPut_CPU_STOP_Substitue_Value" DataType="Unsigned8" ByteOffset ="15"  DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_DR_DQ_Ch15_Reaction_TO_CPUSTOP"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_DR32_DQ_Name" />
                <InfoText TextId="EM_DR32_Output_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>


        <ModuleItem ID="EM_AE04 V1.0" ModuleIdentNumber="0x80003007">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AE04_Name V1.0"/>
            <InfoText TextId="EM_AE04_Info V1.0"/>
            <OrderNumber Value="6ES7 288-3AE04-0AA0" />
            <HardwareRelease Value="0x06" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AE04_AI V1.0" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_3" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="12" Index="1">
                  <Name TextId="EM_AE04_AI_Name V1.0" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AI_Combined_4Channel_Rejection" DataType="Unsigned8" ByteOffset="1" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Combined_4Channel_Rejection_Cfg" ID="EM_AI_Combined_4Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="3" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg"/>

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="4" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" ID="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="6" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" ID="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="8" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" ID="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="10" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" ID="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AE04_AI_Name V1.0" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile" />
                      <ParameterRef ParameterTarget="EM_AI_Combined_4Channel_Rejection_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" />

                      <MenuRef MenuTarget="EM_AM_AI_Channel_0" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_1" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_2" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_3" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_0">
                      <Name TextId="EM_AE04_CH_0" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_1">
                      <Name TextId="EM_AE04_CH_1" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_2">
                      <Name TextId="EM_AE04_CH_2" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_3">
                      <Name TextId="EM_AE04_CH_3" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>


              <ModuleInfo>
                <Name TextId="EM_AE04_AI_Name V1.0" />
                <InfoText TextId="EM_AE04_Info V1.0" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_AE04 V1.1" ModuleIdentNumber="0x80003007">
          <ModuleInfo CategoryRef="EM_CAT_AI">
            <Name TextId="EM_AE04_Name V1.1"/>
            <InfoText TextId="EM_AE04_Info V1.1"/>
            <OrderNumber Value="6ES7 288-3AE04-0AA0" />
            <HardwareRelease Value="0x06" />
            <SoftwareRelease Value="V1.1" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AE04_AI V1.1" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_1" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_2" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AE04_CH_3" />
                </Input>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="12" Index="1">
                  <Name TextId="EM_AE04_AI_Name V1.1" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AI_Combined_4Channel_Rejection" DataType="Unsigned8" ByteOffset="1" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Combined_4Channel_Rejection_Cfg" ID="EM_AI_Combined_4Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="3" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg"/>

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="4" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" ID="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="6" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" ID="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="7" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="8" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" ID="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="10" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" ID="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="11" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AE04_AI_Name V1.1" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile" />
                      <ParameterRef ParameterTarget="EM_AI_Combined_4Channel_Rejection_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" />

                      <MenuRef MenuTarget="EM_AM_AI_Channel_0" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_1" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_2" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_3" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_0">
                      <Name TextId="EM_AE04_CH_0" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_1">
                      <Name TextId="EM_AE04_CH_1" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_2">
                      <Name TextId="EM_AE04_CH_2" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_3">
                      <Name TextId="EM_AE04_CH_3" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>


              <ModuleInfo>
                <Name TextId="EM_AE04_AI_Name V1.1" />
                <InfoText TextId="EM_AE04_Info V1.1" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_AQ02" ModuleIdentNumber="0x80003008">
          <ModuleInfo CategoryRef="EM_CAT_AQ">
            <Name TextId="EM_AQ02_Name"/>
            <InfoText TextId="EM_AQ02_Info"/>
            <OrderNumber Value="6ES7 288-3AQ02-0AA0" />
            <HardwareRelease Value="0x05" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>

            <VirtualSubmoduleItem ID="VM_EM_AQ02" MayIssueProcessAlarm="false" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ02_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AQ02_CH_1" />
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="10" Index="1">
                  <Name TextId="EM_AQ02_AQ_Name" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                  <Ref ValueItemTarget="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior" DataType="Unsigned8" ByteOffset="1" DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" ID="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="2" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch0_Range_Cfg" ID="EM_AQ_Mesurement_ch0_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="3" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch0_Value_Cfg" ID="EM_AQ_Substitute_ch0_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="6" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch1_Range_Cfg" ID="EM_AQ_Mesurement_ch1_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="7" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch1_Value_Cfg" ID="EM_AQ_Substitute_ch1_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                  <Ref DataType="Bit" ByteOffset="9" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AQ02_AQ_Name" />
                      <ParameterRef ParameterTarget="EM_Module_Parameters_Alarm_Profile" />
                      <ParameterRef ParameterTarget="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" />
                      <MenuRef MenuTarget="EM_AQ02_AQ_Channel_0" />
                      <MenuRef MenuTarget="EM_AQ02_AQ_Channel_1" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ02_AQ_Channel_0">
                      <Name TextId="EM_AQ02_CH_0" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch0_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch0_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AQ02_AQ_Channel_1">
                      <Name TextId="EM_AQ02_CH_1" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch1_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch1_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AQ02_AQ_Name" />
                <InfoText TextId="EM_AQ02_Info" />
              </ModuleInfo>

            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>

        </ModuleItem>

        <ModuleItem ID="EM_AM03" ModuleIdentNumber="0x80003012">
          <ModuleInfo CategoryRef="EM_CAT_AI_AQ">
            <Name TextId="EM_AM03_Name"/>
            <InfoText TextId="EM_AM03_Info"/>
            <OrderNumber Value="6ES7 288-3AM03-0AA0" />
            <HardwareRelease Value="0x06" />
            <SoftwareRelease Value="V1.0" />
          </ModuleInfo>

          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="VM_EM_AM03_Module_Parameters" MayIssueProcessAlarm="false" FixedInSubslots="1000" SubmoduleIdentNumber="0x00000003">
              <IOData />

              <RecordDataList>
                <ParameterRecordDataItem Length="1" Index="1">
                  <Name TextId="EM_AM03_Module_Parameters" />
                  <Ref DataType="Bit"  DefaultValue="1" BitOffset="0" ByteOffset="0" Changeable="true" Visible="true" TextId="EM_Module_Parameters_Alarm_Profile" ID="EM_Module_Parameters_Alarm_Profile"/>
                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM03_Module_Parameters" />
                <InfoText TextId="EM_AM03_Param_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>


            <VirtualSubmoduleItem ID="VM_EM_AM03_AI" MayIssueProcessAlarm="false" FixedInSubslots="1001" SubmoduleIdentNumber="0x00000001">
              <IOData>
                <Input>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM03_AI_CH_0" />
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM03_AI_CH_1" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Length="6" Index="1">
                  <Name TextId="EM_AM03_AI_Name" />
                  <Ref ValueItemTarget="EM_AI_Combined_2Channel_Rejection" DataType="Unsigned8" ByteOffset="0" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Combined_2Channel_Rejection_Cfg" ID="EM_AI_Combined_2Channel_Rejection_Cfg"/>
                  <Ref ValueItemTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range" DataType="Unsigned8" ByteOffset="1" DefaultValue="2" AllowedValues="0 1 2 19" Changeable="true" Visible="true" TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" ID="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg"/>

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="2" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" ID="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="3" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="3" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />

                  <Ref ValueItemTarget="EM_AI_Mesurement_Smoothing" DataType="Unsigned8" ByteOffset="4" DefaultValue="1" AllowedValues="0..3" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" ID="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                  <Ref DataType="Bit" ByteOffset="5" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" ID="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AM03_AI_Name" />
                      <ParameterRef ParameterTarget="EM_AI_Combined_2Channel_Rejection_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" />

                      <MenuRef MenuTarget="EM_AM_AI_Channel_0" />
                      <MenuRef MenuTarget="EM_AM_AI_Channel_1" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_0">
                      <Name TextId="EM_AM03_AI_CH_0" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                    <MenuItem ID="EM_AM_AI_Channel_1">
                      <Name TextId="EM_AM03_AI_CH_1" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_Smoothing_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" />
                      <ParameterRef ParameterTarget="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>

              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM03_AI_Name" />
                <InfoText TextId="EM_AM03_Input_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>

            <VirtualSubmoduleItem ID="VM_EM_AM03_AQ" MayIssueProcessAlarm="false" FixedInSubslots="1002" SubmoduleIdentNumber="0x00000002">
              <IOData>
                <Output>
                  <DataItem DataType="Unsigned16" UseAsBits="false" TextId="EM_AM03_AQ_CH_0"/>
                </Output>
              </IOData>

              <RecordDataList>
                <ParameterRecordDataItem Length="5" Index="1">
                  <Name TextId="EM_AM03_AQ_Name" />
                  <Ref ValueItemTarget="EM_AQ_Shutdown_Behavior" DataType="Unsigned8" ByteOffset="0" DefaultValue="0" AllowedValues="0..2" Changeable="true" Visible="true" TextId="EM_AQ_Shutdown_Behavior_Cfg" ID="EM_AQ_Shutdown_Behavior_Cfg" />

                  <Ref ValueItemTarget="EM_AQ_Mesurement_Range" DataType="Unsigned8" ByteOffset="1" DefaultValue="2" AllowedValues="2 19" Changeable="true" Visible="true" TextId="EM_AQ_Mesurement_ch0_Range_Cfg" ID="EM_AQ_Mesurement_ch0_Range_Cfg" />
                  <Ref DataType="Integer16" ByteOffset="2" DefaultValue="0" AllowedValues="-32512..32511" Changeable="true" Visible="true" TextId="EM_AQ_Substitute_ch0_Value_Cfg" ID="EM_AQ_Substitute_ch0_Value_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="0" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="1" DefaultValue="1" Changeable="true" Visible="true" TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" ID="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="2" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" ID="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                  <Ref DataType="Bit" ByteOffset="4" BitOffset="3" DefaultValue="0" Changeable="true" Visible="true" TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" ID="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />

                  <MenuList>
                    <MenuItem ID="EM_AM_Configuration">
                      <Name TextId="EM_AM03_AQ_Name" />
                      <ParameterRef ParameterTarget="EM_AQ_Shutdown_Behavior_Cfg" />
                      <MenuRef MenuTarget="EM_AM03_AQ_Channel_0" />
                    </MenuItem>

                    <MenuItem ID="EM_AM03_AQ_Channel_0">
                      <Name TextId="EM_AM03_AQ_CH_0" />
                      <ParameterRef ParameterTarget="EM_AQ_Mesurement_ch0_Range_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Substitute_ch0_Value_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" />
                      <ParameterRef ParameterTarget="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" />
                    </MenuItem>

                  </MenuList>

                </ParameterRecordDataItem>
              </RecordDataList>

              <ModuleInfo>
                <Name TextId="EM_AM03_AQ_Name" />
                <InfoText TextId="EM_AM03_Output_Info" />
              </ModuleInfo>
            </VirtualSubmoduleItem>

          </VirtualSubmoduleList>

        </ModuleItem>

      </ModuleList>


      <ValueList>
        <ValueItem ID="IDV_UDIAG1">
          <Assignments>
            <Assign Content="1" TextId="IDT_UDIAG1_1"/>
          </Assignments>
        </ValueItem>

        <ValueItem ID="IDV_Input_Filter_Enum">
          <Assignments>
            <Assign Content="0"  TextId="OnboardInput_Filter_0"/>
            <Assign Content="1"  TextId="OnboardInput_Filter_1"/>
            <Assign Content="2"  TextId="OnboardInput_Filter_2"/>
            <Assign Content="3"  TextId="OnboardInput_Filter_3"/>
            <Assign Content="4"  TextId="OnboardInput_Filter_4"/>
            <Assign Content="5"  TextId="OnboardInput_Filter_5"/>
            <Assign Content="6"  TextId="OnboardInput_Filter_6"/>
            <Assign Content="7"  TextId="OnboardInput_Filter_7"/>
            <Assign Content="8"  TextId="OnboardInput_Filter_8"/>
            <Assign Content="9"  TextId="OnboardInput_Filter_9"/>
            <Assign Content="10" TextId="OnboardInput_Filter_10"/>
            <Assign Content="11" TextId="OnboardInput_Filter_11"/>
            <Assign Content="12" TextId="OnboardInput_Filter_12"/>
            <Assign Content="13" TextId="OnboardInput_Filter_13"/>
            <Assign Content="14" TextId="OnboardInput_Filter_14"/>
          </Assignments>
        </ValueItem>

        <ValueItem ID="IDV_Input_pulse_catch_Enum">
          <Assignments>
            <Assign Content="0" TextId="Input_pulse_catch_Disable"/>
            <Assign Content="1" TextId="Input_pulse_catch_Enable"/>
          </Assignments>
        </ValueItem>

        <ValueItem ID="IDV_OutPut_CPU_STOP_Substitue_Value">
          <Assignments>
            <Assign Content="0" TextId="OnboardOutput_CPU_STOP_Reaction_0" />
            <Assign Content="1" TextId="OnboardOutput_CPU_STOP_Reaction_1" />
            <Assign Content="2" TextId="OnboardOutput_CPU_STOP_Reaction_2" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Mesurement_Type">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Type_Voltage" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Type_Current" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Mesurement_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AI_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Chanel_0_1_Mesurement_Type_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AI_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Chanel_2_3_Mesurement_Type_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AI_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Chanel_4_5_Mesurement_Type_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AI_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Chanel_6_7_Mesurement_Type_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AI_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Mesurement_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Combined_4Channel_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Combined_2Channel_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AR_Combined_2Channel_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AR_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AR_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AR_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AR_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AR_Combined_4Channel_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AR_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AR_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AR_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AR_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AT_Combined_4Channel_Rejection">
          <Assignments>
            <Assign Content="0" TextId="EM_AT_Mesurement_Rejection_0" />
            <Assign Content="1" TextId="EM_AT_Mesurement_Rejection_1" />
            <Assign Content="2" TextId="EM_AT_Mesurement_Rejection_2" />
            <Assign Content="3" TextId="EM_AT_Mesurement_Rejection_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AI_Mesurement_Smoothing">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Smoothing_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Smoothing_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Smoothing_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Smoothing_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AR_Mesurement_Smoothing">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Smoothing_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Smoothing_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Smoothing_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Smoothing_3" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AT_Mesurement_Smoothing">
          <Assignments>
            <Assign Content="0" TextId="EM_AI_Mesurement_Smoothing_0" />
            <Assign Content="1" TextId="EM_AI_Mesurement_Smoothing_1" />
            <Assign Content="2" TextId="EM_AI_Mesurement_Smoothing_2" />
            <Assign Content="3" TextId="EM_AI_Mesurement_Smoothing_3" />
          </Assignments>
        </ValueItem>


        <ValueItem ID="EM_AR_Type_Resistor_Coef_Scale">
          <Assignments>
            <Assign Content="1048575" TextId="Resistance 4-wire 48 ohms" />
            <Assign Content="1114111" TextId="Resistance 4-wire 150 ohms" />
            <Assign Content="1179647" TextId="Resistance 4-wire 300 ohms" />
            <Assign Content="1245183" TextId="Resistance 4-wire 600 ohms" />
            <Assign Content="1310719" TextId="Resistance 4-wire 3000 ohms" />

            <Assign Content="17825791" TextId="Resistance 3-wire 48 ohms" />
            <Assign Content="17891327" TextId="Resistance 3-wire 150 ohms" />
            <Assign Content="17956863" TextId="Resistance 3-wire 300 ohms" />
            <Assign Content="18022399" TextId="Resistance 3-wire 600 ohms" />
            <Assign Content="18087935" TextId="Resistance 3-wire 3000 ohms" />

            <Assign Content="34603007" TextId="Resistance 2-wire 48 ohms" />
            <Assign Content="34668543" TextId="Resistance 2-wire 150 ohms" />
            <Assign Content="34734079" TextId="Resistance 2-wire 300 ohms" />
            <Assign Content="34799615" TextId="Resistance 2-wire 600 ohms" />
            <Assign Content="34865151" TextId="Resistance 2-wire 3000 ohms" />

            <Assign Content="50331648" TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50331649" TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50331904" TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="50331905" TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="50397184" TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50397185" TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50397440" TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="50397441" TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="50462720" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50462721" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50463232" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="50463233" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="50463488" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="50463489" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="50463744" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="50463745" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="50462976" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="50462977" TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="50528256" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50528257" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50528768" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="50528769" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="50529024" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="50529025" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="50529280" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="50529281" TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="50593792" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50593793" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50594304" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="50594305" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="50594560" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="50594561" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="50594816" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="50594817" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="50594048" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="50594049" TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="50659328" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="50659329" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="50659840" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="50659841" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="50660096" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="50660097" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="50660352" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="50660353" TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="50726144" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
            <Assign Content="50726145" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
            <Assign Content="50726400" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="50726401" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="50726656" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="50726657" TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="50791936" TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="50791937" TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="50792192" TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="50792193" TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="50857472" TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="50857473" TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="50857728" TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="50857729" TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="50923008" TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="50923009" TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="50923264" TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="50923265" TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="50988544" TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="50988545" TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="50988800" TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="50988801" TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="51054592" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="51054593" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="51054848" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="51054849" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
            <Assign Content="51055104" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
            <Assign Content="51055105" TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

            <Assign Content="51120128" TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="51120129" TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="51120384" TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="51120385" TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="51185664" TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="51185665" TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="51185920" TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="51185921" TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="51251968" TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
            <Assign Content="51251969" TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

            <Assign Content="67108864" TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67108865" TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67109120" TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="67109121" TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="67174400" TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67174401" TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67174656" TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="67174657" TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="67239936" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67239937" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67240448" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="67240449" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="67240704" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="67240705" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="67240960" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="67240961" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="67240192" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="67240193" TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="67305472" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67305473" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67305984" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="67305985" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="67306240" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="67306241" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="67306496" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="67306497" TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="67371008" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67371009" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67371520" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="67371521" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="67371776" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="67371777" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="67372032" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="67372033" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="67371264" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="67371265" TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="67436544" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="67436545" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="67437056" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="67437057" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="67437312" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="67437313" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="67437568" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="67437569" TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="67503360" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
            <Assign Content="67503361" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
            <Assign Content="67503616" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="67503617" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="67503872" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="67503873" TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="67569152" TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="67569153" TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="67569408" TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="67569409" TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="67634688" TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="67634689" TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="67634944" TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="67634945" TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="67700224" TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="67700225" TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="67700480" TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="67700481" TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="67765760" TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="67765761" TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="67766016" TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="67766017" TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="67831808" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="67831809" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="67832064" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="67832065" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
            <Assign Content="67832320" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
            <Assign Content="67832321" TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

            <Assign Content="67897344" TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="67897345" TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="67897600" TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="67897601" TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="67962880" TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="67962881" TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="67963136" TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="67963137" TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="68029184" TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
            <Assign Content="68029185" TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

            <Assign Content="83886080" TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="83886081" TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="83886336" TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="83886337" TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="83951616" TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="83951617" TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="83951872" TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="83951873" TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="84017152" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="84017153" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="84017664" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="84017665" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="84017920" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="84017921" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="84018176" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="84018177" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="84017408" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="84017409" TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="84082688" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="84082689" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="84083200" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="84083201" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="84083456" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="84083457" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="84083712" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="84083713" TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="84148224" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="84148225" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="84148736" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="84148737" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="84148992" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="84148993" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="84149248" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="84149249" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
            <Assign Content="84148480" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
            <Assign Content="84148481" TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

            <Assign Content="84213760" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
            <Assign Content="84213761" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
            <Assign Content="84214272" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
            <Assign Content="84214273" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
            <Assign Content="84214528" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
            <Assign Content="84214529" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
            <Assign Content="84214784" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
            <Assign Content="84214785" TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

            <Assign Content="84280576" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
            <Assign Content="84280577" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
            <Assign Content="84280832" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="84280833" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="84281088" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="84281089" TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="84346368" TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="84346369" TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="84346624" TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="84346625" TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="84411904" TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="84411905" TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="84412160" TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="84412161" TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="84477440" TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="84477441" TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="84477696" TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="84477697" TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="84542976" TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
            <Assign Content="84542977" TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
            <Assign Content="84543232" TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
            <Assign Content="84543233" TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

            <Assign Content="84609024" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="84609025" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="84609280" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="84609281" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
            <Assign Content="84609536" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
            <Assign Content="84609537" TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

            <Assign Content="84674560" TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="84674561" TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="84674816" TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="84674817" TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="84740096" TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
            <Assign Content="84740097" TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
            <Assign Content="84740352" TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
            <Assign Content="84740353" TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

            <Assign Content="84806400" TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
            <Assign Content="84806401" TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AT_Type_Therm_Scale_Src">
          <Assignments>
            <Assign Content="0" TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="1" TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="2" TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="256" TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="257" TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="258" TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="65536" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="65537" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="65538" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="65792" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="65793" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="65794" TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="131072" TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="131073" TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="131074" TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="131328" TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="131329" TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="131330" TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="196608" TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="196609" TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="196610" TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="196864" TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="196865" TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="196866" TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="262144" TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="262145" TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="262146" TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="262400" TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="262401" TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="262402" TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="327680" TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="327681" TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="327682" TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="327936" TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="327937" TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="327938" TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="393216" TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="393217" TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="393218" TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="393472" TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="393473" TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="393474" TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="458752" TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="458753" TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="458754" TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="459008" TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="459009" TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="459010" TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="524288" TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="524289" TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="524290" TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: Internal reference）" />
            <Assign Content="524544" TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="524545" TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="524546" TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="589824" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 0 Celsius）" />
            <Assign Content="589825" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 50 Celsius）" />
            <Assign Content="589826" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: Internal reference）" />
            <Assign Content="590080" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 0 Celsius）" />
            <Assign Content="590081" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 50 Celsius）" />
            <Assign Content="590082" TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: Internal reference）" />

            <Assign Content="17498111" TextId="Voltage-(±80 mV)" />


          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AQ_Mesurement_Range">
          <Assignments>
            <Assign Content="0" TextId="EM_AQ_Mesurement_Range_Voltage_0" />
            <Assign Content="1" TextId="EM_AQ_Mesurement_Range_Voltage_1" />
            <Assign Content="2" TextId="EM_AQ_Mesurement_Range_Voltage_2" />
            <Assign Content="19" TextId="EM_AQ_Mesurement_Range_Current_0" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AQ_Shutdown_Behavior">
          <Assignments>
            <Assign Content="0" TextId="EM_AQ_Shutdown_Behavior_0" />
            <Assign Content="1" TextId="EM_AQ_Shutdown_Behavior_1" />
            <Assign Content="2" TextId="EM_AQ_Shutdown_Behavior_2" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior">
          <Assignments>
            <Assign Content="0" TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_0" />
            <Assign Content="1" TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_1" />
            <Assign Content="2" TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_2" />
          </Assignments>
        </ValueItem>

        <ValueItem ID="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior">
          <Assignments>
            <Assign Content="0" TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_0" />
            <Assign Content="1" TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_1" />
            <Assign Content="2" TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_2" />
          </Assignments>
        </ValueItem>

      </ValueList>

      <GraphicsList>
        <GraphicItem ID="1" GraphicFile="GSDML-002A-0119-200SMART"/>
      </GraphicsList>

      <CategoryList>
        <CategoryItem ID="EM_CAT_AI_AQ" TextId="EM_CAT_AI_AQ_Name" />
        <CategoryItem ID="EM_CAT_DI_DQ" TextId="EM_CAT_DI_DQ_Name" />
        <CategoryItem ID="EM_CAT_AI" TextId="EM_CAT_AI_Name" />
        <CategoryItem ID="EM_CAT_AQ" TextId="EM_CAT_AQ_Name" />
        <CategoryItem ID="EM_CAT_DI" TextId="EM_CAT_DI_Name" />
        <CategoryItem ID="EM_CAT_DQ" TextId="EM_CAT_DQ_Name" />

      </CategoryList>
      <ExternalTextList>
        <PrimaryLanguage>
          <Text TextId="T_DAP_DEVICE_DESC" Value="Interface module with PROFINET RT interface; Support cycle time starting at 4ms; Support maximum 6 I/O extension modules."/>
          <Text TextId="Interface_SubslotLabel" Value="X1"/>
          <Text TextId="Port32769_SubslotLabel" Value="Port1"/>
          <Text TextId="DAP_Name_ID" Value="IM60 V1.0 "/>
          <Text TextId="Interface" Value="Interface"/>
          <Text TextId="Port_1" Value="Port 1"/>
          <Text TextId="IDT_UDIAG1" Value="Running status:"/>
          <Text TextId="IDT_RunningStatus" Value="Device is running"/>
          <Text TextId="IDT_UDIAG1_1" Value="with error"/>
          <Text TextId="DAP1_Name" Value="IM60 Virtual submodule"/>
          <Text TextId="DAP1_Comment" Value="IM60, onboard 36 inputs,24 outputs.Support up to 6 EMs. "/>
          <Text TextId="Onboard_Inputs_Name" Value="Onboard inputs"/>
          <Text TextId="Onboard_Inputs_Comment" Value=""/>
          <Text TextId="Onboard_Inputs" Value="Onboard inputs"/>
          <Text TextId="Onboard_outputs_Name" Value="Onboard outputs"/>
          <Text TextId="Onboard_outputs_Comment" Value=""/>
          <Text TextId="Onboard_outputs" Value="Onboard outputs"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_0" Value=" Input  Filter (channel  0):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_1" Value=" Input  Filter (channel  1):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_2" Value=" Input  Filter (channel  2):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_3" Value=" Input  Filter (channel  3):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_4" Value=" Input  Filter (channel  4):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_5" Value=" Input  Filter (channel  5):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_6" Value=" Input  Filter (channel  6):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_7" Value=" Input  Filter (channel  7):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_0" Value=" Input  Filter (channel  8):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_1" Value=" Input  Filter (channel  9):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_2" Value=" Input  Filter (channel 10):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_3" Value=" Input  Filter (channel 11):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_4" Value=" Input  Filter (channel 12):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_5" Value=" Input  Filter (channel 13):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_6" Value=" Input  Filter (channel 14):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_7" Value=" Input  Filter (channel 15):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_0" Value=" Input  Filter (channel 16):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_1" Value=" Input  Filter (channel 17):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_2" Value=" Input  Filter (channel 18):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_3" Value=" Input  Filter (channel 19):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_4" Value=" Input  Filter (channel 20):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_5" Value=" Input  Filter (channel 21):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_6" Value=" Input  Filter (channel 22):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_7" Value=" Input  Filter (channel 23):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_0" Value=" Input  Filter (channel 24):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_1" Value=" Input  Filter (channel 25):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_2" Value=" Input  Filter (channel 26):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_3" Value=" Input  Filter (channel 27):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_4" Value=" Input  Filter (channel 28):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_5" Value=" Input  Filter (channel 29):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_6" Value=" Input  Filter (channel 30):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_7" Value=" Input  Filter (channel 31):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_0" Value=" Input  Filter (channel 32):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_1" Value=" Input  Filter (channel 33):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_2" Value=" Input  Filter (channel 34):"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_3" Value=" Input  Filter (channel 35):"/>

          <Text TextId="EM_DT_DI_Ch_Grp_0_Input_Filter" Value="Input Filter (channel 0,1,2,3):" />
          <Text TextId="EM_DT_DI_Ch_Grp_1_Input_Filter" Value="Input Filter (channel 4,5,6,7):" />
          <Text TextId="EM_DT_DI_Ch_Grp_2_Input_Filter" Value="Input Filter (channel 8,9,10,11):" />
          <Text TextId="EM_DT_DI_Ch_Grp_3_Input_Filter" Value="Input Filter (channel 12,13,14,15):" />

          <Text TextId="EM_DR_DI_Ch_Grp_0_Input_Filter" Value="Input Filter (channel 0,1,2,3):" />
          <Text TextId="EM_DR_DI_Ch_Grp_1_Input_Filter" Value="Input Filter (channel 4,5,6,7):" />
          <Text TextId="EM_DR_DI_Ch_Grp_2_Input_Filter" Value="Input Filter (channel 8,9,10,11):" />
          <Text TextId="EM_DR_DI_Ch_Grp_3_Input_Filter" Value="Input Filter (channel 12,13,14,15):" />

          <Text TextId="TextId_OnboardDigitalInputs" Value="Onboard Digital Inputs:" />


          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_0" Value="Pulse Catch(channel  0):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_1" Value="Pulse Catch(channel  1):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_2" Value="Pulse Catch(channel  2):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_3" Value="Pulse Catch(channel  3):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_4" Value="Pulse Catch(channel  4):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_5" Value="Pulse Catch(channel  5):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_6" Value="Pulse Catch(channel  6):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_7" Value="Pulse Catch(channel  7):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_0" Value="Pulse Catch(channel  8):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_1" Value="Pulse Catch(channel  9):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_2" Value="Pulse Catch(channel 10):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_3" Value="Pulse Catch(channel 11):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_4" Value="Pulse Catch(channel 12):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_5" Value="Pulse Catch(channel 13):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_6" Value="Pulse Catch(channel 14):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_7" Value="Pulse Catch(channel 15):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_0" Value="Pulse Catch(channel 16):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_1" Value="Pulse Catch(channel 17):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_2" Value="Pulse Catch(channel 18):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_3" Value="Pulse Catch(channel 19):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_4" Value="Pulse Catch(channel 20):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_5" Value="Pulse Catch(channel 21):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_6" Value="Pulse Catch(channel 22):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_7" Value="Pulse Catch(channel 23):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_0" Value="Pulse Catch(channel 24):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_1" Value="Pulse Catch(channel 25):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_2" Value="Pulse Catch(channel 26):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_3" Value="Pulse Catch(channel 27):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_4" Value="Pulse Catch(channel 28):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_5" Value="Pulse Catch(channel 29):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_6" Value="Pulse Catch(channel 30):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_7" Value="Pulse Catch(channel 31):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_0" Value="Pulse Catch(channel 32):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_1" Value="Pulse Catch(channel 33):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_2" Value="Pulse Catch(channel 34):"/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_3" Value="Pulse Catch(channel 35):"/>

          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_0" Value="Reaction to CPU Stop(channel  0):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_1" Value="Reaction to CPU Stop(channel  1):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_2" Value="Reaction to CPU Stop(channel  2):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_3" Value="Reaction to CPU Stop(channel  3):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_4" Value="Reaction to CPU Stop(channel  4):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_5" Value="Reaction to CPU Stop(channel  5):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_6" Value="Reaction to CPU Stop(channel  6):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_7" Value="Reaction to CPU Stop(channel  7):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_0" Value="Reaction to CPU Stop(channel  8):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_1" Value="Reaction to CPU Stop(channel  9):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_2" Value="Reaction to CPU Stop(channel 10):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_3" Value="Reaction to CPU Stop(channel 11):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_4" Value="Reaction to CPU Stop(channel 12):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_5" Value="Reaction to CPU Stop(channel 13):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_6" Value="Reaction to CPU Stop(channel 14):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_7" Value="Reaction to CPU Stop(channel 15):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_0" Value="Reaction to CPU Stop(channel 16):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_1" Value="Reaction to CPU Stop(channel 17):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_2" Value="Reaction to CPU Stop(channel 18):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_3" Value="Reaction to CPU Stop(channel 19):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_4" Value="Reaction to CPU Stop(channel 20):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_5" Value="Reaction to CPU Stop(channel 21):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_6" Value="Reaction to CPU Stop(channel 22):"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_7" Value="Reaction to CPU Stop(channel 23):"/>

          <Text TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  0):" />
          <Text TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  1):" />
          <Text TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  2):" />
          <Text TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  3):" />
          <Text TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  4):" />
          <Text TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  5):" />
          <Text TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  6):" />
          <Text TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  7):" />
          <Text TextId="EM_DT_DQ_Ch8_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  8):" />
          <Text TextId="EM_DT_DQ_Ch9_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  9):" />
          <Text TextId="EM_DT_DQ_Ch10_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 10):" />
          <Text TextId="EM_DT_DQ_Ch11_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 11):" />
          <Text TextId="EM_DT_DQ_Ch12_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 12):" />
          <Text TextId="EM_DT_DQ_Ch13_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 13):" />
          <Text TextId="EM_DT_DQ_Ch14_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 14):" />
          <Text TextId="EM_DT_DQ_Ch15_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 15):" />

          <Text TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  0):" />
          <Text TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  1):" />
          <Text TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  2):" />
          <Text TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  3):" />
          <Text TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  4):" />
          <Text TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  5):" />
          <Text TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  6):" />
          <Text TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  7):" />
          <Text TextId="EM_DR_DQ_Ch8_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  8):" />
          <Text TextId="EM_DR_DQ_Ch9_Reaction_TO_CPUSTOP"  Value="Reaction to CPU Stop(channel  9):" />
          <Text TextId="EM_DR_DQ_Ch10_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 10):" />
          <Text TextId="EM_DR_DQ_Ch11_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 11):" />
          <Text TextId="EM_DR_DQ_Ch12_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 12):" />
          <Text TextId="EM_DR_DQ_Ch13_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 13):" />
          <Text TextId="EM_DR_DQ_Ch14_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 14):" />
          <Text TextId="EM_DR_DQ_Ch15_Reaction_TO_CPUSTOP" Value="Reaction to CPU Stop(channel 15):" />

          <Text TextId="OnboardInput_Filter_0" Value="0.2us" />
          <Text TextId="OnboardInput_Filter_1" Value="0.4us" />
          <Text TextId="OnboardInput_Filter_2" Value="0.8us" />
          <Text TextId="OnboardInput_Filter_3" Value="1.6us" />
          <Text TextId="OnboardInput_Filter_4" Value="3.2us" />
          <Text TextId="OnboardInput_Filter_5" Value="6.4us" />
          <Text TextId="OnboardInput_Filter_6" Value="12.8us" />
          <Text TextId="OnboardInput_Filter_7" Value="0.2ms" />
          <Text TextId="OnboardInput_Filter_8" Value="0.4ms" />
          <Text TextId="OnboardInput_Filter_9" Value="0.8ms" />
          <Text TextId="OnboardInput_Filter_10" Value="1.6ms" />
          <Text TextId="OnboardInput_Filter_11" Value="3.2ms" />
          <Text TextId="OnboardInput_Filter_12" Value="6.4ms" />
          <Text TextId="OnboardInput_Filter_13" Value="12.8ms" />
          <Text TextId="OnboardInput_Filter_14" Value="None" />

          <Text TextId="Input_pulse_catch_Enable" Value="Enabled" />
          <Text TextId="Input_pulse_catch_Disable" Value="Disabled" />

          <Text TextId="TextId_OnboardDigitaloutputs" Value="Onboard Digital Outputs:" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_0" Value="Shutdown" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_1" Value="Keep Last Value" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_2" Value="Output Value:1" />

          <Text TextId="EM_AM06_AI_Name" Value="Analog Input" />
          <Text TextId="EM_AM06_AQ_Name" Value="Analog Output" />
          <Text TextId="EM_AM03_AI_Name" Value="Analog Input" />
          <Text TextId="EM_AM03_AQ_Name" Value="Analog Output" />

          <Text TextId="EM_AI_Mesurement_Type_Voltage" Value="Voltage" />
          <Text TextId="EM_AI_Mesurement_Type_Current" Value="Current" />

          <Text TextId="EM_AI_Cfg_Title" Value="Analog Input Configuration" />
          <Text TextId="EM_AQ_Cfg_Title" Value="Analog Output Configuration" />
          <Text TextId="VM_EM_AR_Cfg_Title" Value="Analog Input(RTD) Configuration" />
          <Text TextId="VM_EM_AT_Cfg_Title" Value="Analog Input(TC) Configuration" />

          <Text TextId="EM_AI_Mesurement_Type_Cfg" Value="Type" />
          <Text TextId="EM_AI_Mesurement_Range_Cfg" Value="Range"/>
          <Text TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" Value="Channel 0,1 mesurement type and range" />
          <Text TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" Value="Channel 2,3 mesurement type and range" />
          <Text TextId="EM_AI_Chanel_4_5_Mesurement_Type_Range_Cfg" Value="Channel 4,5 mesurement type and range" />
          <Text TextId="EM_AI_Chanel_6_7_Mesurement_Type_Range_Cfg" Value="Channel 6,7 mesurement type and range" />
          <Text TextId="EM_AI_Mesurement_Rejection_Cfg" Value="Rejection" />
          <Text TextId="EM_AI_Mesurement_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch4_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch5_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch6_Smoothing_Cfg" Value="Smoothing" />
          <Text TextId="EM_AI_Mesurement_ch7_Smoothing_Cfg" Value="Smoothing" />

          <Text TextId="EM_AI_Combined_8Channel_Rejection_Cfg" Value="Rejection" />
          <Text TextId="EM_AI_Combined_4Channel_Rejection_Cfg" Value="Rejection" />
          <Text TextId="EM_AI_Combined_2Channel_Rejection_Cfg" Value="Rejection" />

          <Text TextId="EM_AR_Combined_2Channel_Rejection_Cfg" Value="Rejection" />
          <Text TextId="EM_AR_Combined_4Channel_Rejection_Cfg" Value="Rejection" />
          <Text TextId="EM_AT_Combined_4Channel_Rejection_Cfg" Value="Rejection" />

          <Text TextId="EM_AI_Mesurement_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch4_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch5_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch6_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch7_En_Upper_Limit_Alarm_Cfg" Value="Enable upper limit exceeded alarm" />

          <Text TextId="EM_AI_Mesurement_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch4_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch5_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch6_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />
          <Text TextId="EM_AI_Mesurement_ch7_En_Lower_Limit_Alarm_Cfg" Value="Enable lower limit exceeded alarm" />

          <Text TextId="EM_AI_Mesurement_Range_Voltage_0" Value="Voltage +/- 2.5v" />
          <Text TextId="EM_AI_Mesurement_Range_Voltage_1" Value="Voltage +/- 5v" />
          <Text TextId="EM_AI_Mesurement_Range_Voltage_2" Value="Voltage +/- 10v" />
          <Text TextId="EM_AI_Mesurement_Range_Current_0" Value="Current 0 - 20ma" />

          <Text TextId="EM_AI_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_3" Value="400Hz" />

          <Text TextId="EM_AR_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_3" Value="400Hz" />

          <Text TextId="EM_AT_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_3" Value="400Hz" />


          <Text TextId="Resistance 4-wire 48 ohms" Value ="Resistance 4-wire 48 ohms" />
          <Text TextId="Resistance 4-wire 150 ohms" Value ="Resistance 4-wire 150 ohms" />
          <Text TextId="Resistance 4-wire 300 ohms" Value ="Resistance 4-wire 300 ohms" />
          <Text TextId="Resistance 4-wire 600 ohms" Value ="Resistance 4-wire 600 ohms" />
          <Text TextId="Resistance 4-wire 3000 ohms" Value ="Resistance 4-wire 3000 ohms" />

          <Text TextId="Resistance 3-wire 48 ohms" Value ="Resistance 3-wire 48 ohms" />
          <Text TextId="Resistance 3-wire 150 ohms" Value ="Resistance 3-wire 150 ohms" />
          <Text TextId="Resistance 3-wire 300 ohms" Value ="Resistance 3-wire 300 ohms" />
          <Text TextId="Resistance 3-wire 600 ohms" Value ="Resistance 3-wire 600 ohms" />
          <Text TextId="Resistance 3-wire 3000 ohms" Value ="Resistance 3-wire 3000 ohms" />

          <Text TextId="Resistance 2-wire 48 ohms" Value ="Resistance 2-wire 48 ohms" />
          <Text TextId="Resistance 2-wire 150 ohms" Value ="Resistance 2-wire 150 ohms" />
          <Text TextId="Resistance 2-wire 300 ohms" Value ="Resistance 2-wire 300 ohms" />
          <Text TextId="Resistance 2-wire 600 ohms" Value ="Resistance 2-wire 600 ohms" />
          <Text TextId="Resistance 2-wire 3000 ohms" Value ="Resistance 2-wire 3000 ohms" />

          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" />

          <Text TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" />
          <Text TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" />

          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: Internal reference）" Value="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 0 Celsius）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 50 Celsius）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: Internal reference）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: Internal reference）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 0 Celsius）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 0 Celsius）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 50 Celsius）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 50 Celsius）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: Internal reference）" Value="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: Internal reference）" />

          <Text TextId="Voltage-(±80 mV)" Value="Voltage-(±80 mV)" />



          <Text TextId="EM_AI_Mesurement_Smoothing_0" Value="None(1 Cycle)" />
          <Text TextId="EM_AI_Mesurement_Smoothing_1" Value="Weak(4 Cycle)" />
          <Text TextId="EM_AI_Mesurement_Smoothing_2" Value="Medium(16 Clcle)" />
          <Text TextId="EM_AI_Mesurement_Smoothing_3" Value="Strong(32 Cycle)" />
          <Text TextId="EM_AQ_Mesurement_Type_Cfg" Value="Measurement Type" />

          <Text TextId="EM_AQ_Mesurement_Range_Cfg" Value="Measurement Range" />
          <Text TextId="EM_AQ_Mesurement_ch0_Range_Cfg" Value="Measurement Range" />
          <Text TextId="EM_AQ_Mesurement_ch1_Range_Cfg" Value="Measurement Range" />
          <Text TextId="EM_AQ_Mesurement_ch2_Range_Cfg" Value="Measurement Range" />
          <Text TextId="EM_AQ_Mesurement_ch3_Range_Cfg" Value="Measurement Range" />

          <Text TextId="EM_AQ_Shutdown_Behavior_Cfg" Value="Reaction to CPU Stop" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" Value="Reaction to CPU Stop" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_Cfg" Value="Reaction to CPU Stop" />
          <Text TextId="EM_AQ_Substitute_Value_Cfg" Value="Substitute value(Only valid if substitute configuration is enabled for this channel.)" />

          <Text TextId="EM_AQ_Substitute_ch0_Value_Cfg" Value="Substitute value(Only valid if substitute configuration is enabled for this channel.)" />
          <Text TextId="EM_AQ_Substitute_ch1_Value_Cfg" Value="Substitute value(Only valid if substitute configuration is enabled for this channel.)" />
          <Text TextId="EM_AQ_Substitute_ch2_Value_Cfg" Value="Substitute value(Only valid if substitute configuration is enabled for this channel.)" />
          <Text TextId="EM_AQ_Substitute_ch3_Value_Cfg" Value="Substitute value(Only valid if substitute configuration is enabled for this channel.)" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable upper limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable lower limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" Value="Enable wire break alarm(Only valid for current)" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" Value="Enable short circuit alarm(Only valid for voltage)" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable upper limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable lower limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" Value="Enable wire break alarm(Only valid for current)" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" Value="Enable short circuit alarm(Only valid for voltage)" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable upper limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable lower limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch2_Cfg" Value="Enable wire break alarm(Only valid for current)" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch2_Cfg" Value="Enable short circuit alarm(Only valid for voltage)" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable upper limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable lower limit exceed alarm" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch3_Cfg" Value="Enable wire break alarm(Only valid for current)" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch3_Cfg" Value="Enable short circuit alarm(Only valid for voltage)" />

          <Text TextId="EM_AR_Mesurement_Smoothing_Ch0_Cfg" Value="Smoothing" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch1_Cfg" Value="Smoothing" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch2_Cfg" Value="Smoothing" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch3_Cfg" Value="Smoothing" />

          <Text TextId="EM_AT_Mesurement_Smoothing_Ch0_Cfg" Value="Smoothing" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch1_Cfg" Value="Smoothing" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch2_Cfg" Value="Smoothing" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch3_Cfg" Value="Smoothing" />


          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable upper limit exceeded alarm"/>

          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable upper limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable upper limit exceeded alarm"/>

          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable lower limit exceeded alarm"/>

          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="Enable lower limit exceeded alarm"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="Enable lower limit exceeded alarm"/>

          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch2_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch3_Cfg" Value="Enable wire break alarm" />

          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch1_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch2_Cfg" Value="Enable wire break alarm" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch3_Cfg" Value="Enable wire break alarm" />

          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" Value="Type" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" Value="Type" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_2_Cfg" Value="Type" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_3_Cfg" Value="Type" />

          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg" Value="Type" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg" Value="Type" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_2_Cfg" Value="Type" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_3_Cfg" Value="Type" />

          <Text TextId="EM_AM06_Name" Value="AM06(4AI/2AQ) V1.0" />
          <Text TextId="EM_AM06_Info" Value="AM06 V1.0： with 4 analog inputs and 2 analog outputs." />
          <Text TextId="EM_AM06_Param_Info" Value="Parameters for the entire module" />
          <Text TextId="EM_AM06_Input_Info" Value="With 4 analog inputs." />
          <Text TextId="EM_AM06_Output_Info" Value="With 2 analog outputs." />
          <Text TextId="EM_AM06_Module_Parameters" Value="Module Parameters" />
          <Text TextId="EM_AM06_AI_CH_0" Value="AM06,analog input,channal 0" />
          <Text TextId="EM_AM06_AI_CH_1" Value="AM06,analog input,channal 1" />
          <Text TextId="EM_AM06_AI_CH_2" Value="AM06,analog input,channal 2" />
          <Text TextId="EM_AM06_AI_CH_3" Value="AM06,analog input,channal 3" />
          <Text TextId="EM_AM06_AQ_CH_0" Value="AM06,analog output,channel 0" />
          <Text TextId="EM_AM06_AQ_CH_1" Value="AM06,analog output,channel 1" />

          <Text TextId="EM_Module_Parameters_Alarm_Profile" Value="Enable user power alarm for this module" />

          <Text TextId="EM_AQ_Mesurement_Range_Voltage_0" Value="Voltage +/- 2.5v"/>
          <Text TextId="EM_AQ_Mesurement_Range_Voltage_1" Value="Voltage +/- 5v" />
          <Text TextId="EM_AQ_Mesurement_Range_Voltage_2" Value="Voltage +/- 10v" />
          <Text TextId="EM_AQ_Mesurement_Range_Current_0" Value="Current 0 - 20ma" />
          <Text TextId="EM_AQ_Shutdown_Behavior_0" Value="Shutdown" />
          <Text TextId="EM_AQ_Shutdown_Behavior_1" Value="Keep last value" />
          <Text TextId="EM_AQ_Shutdown_Behavior_2" Value="Output substitute value" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_0" Value="Shutdown" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_1" Value="Keep last value" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_2" Value="Output substitute value" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_0" Value="Shutdown" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_1" Value="Keep last value" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_2" Value="Output substitute value" />

          <Text TextId="EM_DT32_Name" Value="DT32(16DI/16DQ Transistor) V1.0" />
          <Text TextId="EM_DT32_DI_Name" Value="Digital Inputs" />
          <Text TextId="EM_DT32_DQ_Name" Value="Digital Outputs" />
          <Text TextId="EM_DT32_Info" Value="DT32 V1.0: with 16 digital inputs and 16 digital outputs(Transistor)." />
          <Text TextId="EM_DT32_Input_Info" Value="With 16 digital inputs." />
          <Text TextId="EM_DT32_Output_Info" Value="With 16 digital outputs(Transistor)." />
          <Text TextId="EM_DT32_CH_DI_ALL" Value="EM DT32, digital inputs." />
          <Text TextId="EM_DT32_CH_DQ_ALL" Value="EM DT32,digital outputs." />

          <Text TextId="EM_CAT_AI_AQ_Name" Value="AI,AQ" />
          <Text TextId="EM_CAT_DI_DQ_Name" Value="DI,DQ" />
          <Text TextId="EM_CAT_AI_Name" Value="AI" />
          <Text TextId="EM_CAT_AQ_Name" Value="AQ" />
          <Text TextId="EM_CAT_DI_Name" Value="DI" />
          <Text TextId="EM_CAT_DQ_Name" Value="DQ" />

          <Text TextId="EM_AE08_Name" Value="AE08(8AI) V1.0" />
          <Text TextId="EM_AE08_Info" Value="AE08 V1.0： with 8 analog inputs." />
          <Text TextId="EM_AE08_AI_Name" Value="Analog Input" />
          <Text TextId="EM_AE08_CH_0" Value="Channel 0" />
          <Text TextId="EM_AE08_CH_1" Value="Channel 1" />
          <Text TextId="EM_AE08_CH_2" Value="Channel 2" />
          <Text TextId="EM_AE08_CH_3" Value="Channel 3" />
          <Text TextId="EM_AE08_CH_4" Value="Channel 4" />
          <Text TextId="EM_AE08_CH_5" Value="Channel 5" />
          <Text TextId="EM_AE08_CH_6" Value="Channel 6" />
          <Text TextId="EM_AE08_CH_7" Value="Channel 7" />

          <Text TextId="EM_AQ04_Name" Value="AQ04(4AQ) V1.0" />
          <Text TextId="EM_AQ04_Info" Value="AQ04 V1.0： with 4 analog outputs." />
          <Text TextId="EM_AQ04_AQ_Name" Value="Analog Output" />

          <Text TextId="EM_AQ04_CH_0" Value="Channel 0" />
          <Text TextId="EM_AQ04_CH_1" Value="Channel 1" />
          <Text TextId="EM_AQ04_CH_2" Value="Channel 2" />
          <Text TextId="EM_AQ04_CH_3" Value="Channel 3" />


          <Text TextId="EM_DE16_Name" Value="DE16(16DI) V1.0" />
          <Text TextId="EM_DE16_Info" Value="DE16 V1.0： with 16 digital inputs." />
          <Text TextId="EM_DE16_CH_ALL" Value="Digital Inputs" />
          <Text TextId="EM_DE16_DI_Name" Value="Digital Inputs" />

          <Text TextId="EM_QR16_Name" Value="QR16(16DQ Relay) V1.0" />
          <Text TextId="EM_QR16_Info" Value="QR16 V1.0： with 16 digital outputs(Relay)." />
          <Text TextId="EM_QR16_CH_ALL" Value="Digital Outputs" />
          <Text TextId="EM_QR16_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_AR02_Name" Value="AR02(2AI RTD) V1.0" />
          <Text TextId="EM_AR02_Info" Value="AR02 V1.0： with 2 analog inputs(RTD)." />
          <Text TextId="EM_AR02_CH_0" Value="RTD Channel 0" />
          <Text TextId="EM_AR02_CH_1" Value="RTD Channel 1" />

          <Text TextId="EM_AR04_Name" Value="AR04(4AI RTD) V1.0" />
          <Text TextId="EM_AR04_Info" Value="AR04 V1.0： with 4 analog inputs(RTD)." />
          <Text TextId="EM_AR04_CH_0" Value="RTD Channel 0" />
          <Text TextId="EM_AR04_CH_1" Value="RTD Channel 1" />
          <Text TextId="EM_AR04_CH_2" Value="RTD Channel 2" />
          <Text TextId="EM_AR04_CH_3" Value="RTD Channel 3" />

          <Text TextId="EM_AT04_Name" Value="AT04(4AI TC) V1.0" />
          <Text TextId="EM_AT04_Info" Value="AT04 V1.0： with 4 analog inputs(TC)." />
          <Text TextId="EM_AT04_CH_0" Value="Thermocouple Channel 0" />
          <Text TextId="EM_AT04_CH_1" Value="Thermocouple Channel 1" />
          <Text TextId="EM_AT04_CH_2" Value="Thermocouple Channel 2" />
          <Text TextId="EM_AT04_CH_3" Value="Thermocouple Channel 3" />

          <Text TextId="EM_DE08_Name" Value="DE08(8DI) V1.0" />
          <Text TextId="EM_DE08_Info" Value="DE08 V1.0： with 8 digital inputs." />
          <Text TextId="EM_DE08_CH_ALL" Value="Digital Inputs" />
          <Text TextId="EM_DE08_DI_Name" Value="Digital Inputs" />

          <Text TextId="EM_DT08_Name" Value="DT08(8DQ Transistor) V1.0" />
          <Text TextId="EM_DT08_Info" Value="DT08 V1.0： with 8 digital outputs(Transistor)." />
          <Text TextId="EM_DT08_CH_ALL" Value="Digital Outputs" />
          <Text TextId="EM_DT08_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_DR08_Name" Value="DR08(8DQ Relay) V1.0" />
          <Text TextId="EM_DR08_Info" Value="DR08 V1.0： with 8 digital outputs(Relay)." />
          <Text TextId="EM_DR08_CH_ALL" Value="Digital Outputs" />
          <Text TextId="EM_DR08_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_QT16_Name" Value="QT16(16DQ Transistor) V1.0" />
          <Text TextId="EM_QT16_Info" Value="QT16 V1.0： with 16 digital outputs(Transistor)." />
          <Text TextId="EM_QT16_CH_ALL" Value="Digital Outputs" />
          <Text TextId="EM_QT16_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_DT16_Name" Value="DT16(8DI/8DQ Transistor) V1.0" />
          <Text TextId="EM_DT16_Info" Value="DT16 V1.0： with 8 digital inputs and 8 digital outputs(Transistor)." />
          <Text TextId="EM_DT16_Input_Info" Value="With 8 digital inputs." />
          <Text TextId="EM_DT16_Output_Info" Value="With 8 digital outputs(Transistor)." />
          <Text TextId="EM_DT16_CH_DI_ALL" Value="Digital Inputs" />
          <Text TextId="EM_DT16_CH_DQ_ALL" Value="Digital Outputs" />
          <Text TextId="EM_DT16_DI_Name" Value="Digital Inputs" />
          <Text TextId="EM_DT16_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_DR16_Name" Value="DR16(8DI/8DQ Relay) V1.0" />
          <Text TextId="EM_DR16_Info" Value="DR16 V1.0： with 8 digital inputs and 8 digital outputs(Relay)." />
          <Text TextId="EM_DR16_Input_Info" Value="With 8 digital inputs." />
          <Text TextId="EM_DR16_Output_Info" Value="With 8 digital outputs(Relay)." />
          <Text TextId="EM_DR16_CH_DI_ALL" Value="Digital Inputs" />
          <Text TextId="EM_DR16_CH_DQ_ALL" Value="Digital Outputs" />

          <Text TextId="EM_DR16_DI_Name" Value="Digital Inputs" />
          <Text TextId="EM_DR16_DQ_Name" Value="Digital Outputs" />

          <Text TextId="EM_DR32_Name" Value="DR32(16DI/16DQ Relay) V1.0" />
          <Text TextId="EM_DR32_Info" Value="DR32 V1.0： with 16 digital inputs and 16 digital outputs(Relay)." />
          <Text TextId="EM_DR32_Input_Info" Value="With 16 digital inputs." />
          <Text TextId="EM_DR32_Output_Info" Value="With 16 digital outputs(Relay)." />
          <Text TextId="EM_DR32_CH_DI_ALL" Value="Digital Inputs" />
          <Text TextId="EM_DR32_CH_DQ_ALL" Value="Digital Outputs" />
          <Text TextId="EM_DR32_DI_Name" Value="Digital Inputs" />
          <Text TextId="EM_DR32_DQ_Name" Value="Digital Outputs" />


          <Text TextId="EM_AE04_Name V1.0" Value="AE04(4AI) V1.0" />
          <Text TextId="EM_AE04_Info V1.0" Value="AE04 V1.0： with 4 analog inputs." />
          <Text TextId="EM_AE04_Name V1.1" Value="AE04(4AI) V1.1" />
          <Text TextId="EM_AE04_Info V1.1" Value="AE04 V1.1： with 4 analog inputs." />
          <Text TextId="EM_AE04_AI_Name V1.0" Value="Analog Input" />
          <Text TextId="EM_AE04_AI_Name V1.1" Value="Analog Input" />
          <Text TextId="EM_AE04_CH_0" Value="Analog Input Channel 0" />
          <Text TextId="EM_AE04_CH_1" Value="Analog Input Channel 1" />
          <Text TextId="EM_AE04_CH_2" Value="Analog Input Channel 2" />
          <Text TextId="EM_AE04_CH_3" Value="Analog Input Channel 3" />

          <Text TextId="EM_AQ02_Name" Value="AQ02(2AQ) V1.0" />
          <Text TextId="EM_AQ02_Info" Value="AQ02 V1.0： with 2 analog outputs." />
          <Text TextId="EM_AQ02_AQ_Name" Value="Analog Output" />
          <Text TextId="EM_AQ02_CH_0" Value="Analog Output Channel 0" />
          <Text TextId="EM_AQ02_CH_1" Value="Analog Output Channel 1" />

          <Text TextId="EM_AM03_Module_Parameters" Value="Module Parameters" />
          <Text TextId="EM_AM03_Name" Value="AM03(2AI/1AQ) V1.0" />
          <Text TextId="EM_AM03_Info" Value="AM03 V1.0： with 2 analog inputs and 1 analog output." />
          <Text TextId="EM_AM03_Param_Info" Value="Parameters for the entire module" />
          <Text TextId="EM_AM03_Input_Info" Value="With 2 analog inputs." />
          <Text TextId="EM_AM03_Output_Info" Value="With 1 analog output." />
          <Text TextId="EM_AM03_AI_CH_0" Value="Analog Input Channel 0" />
          <Text TextId="EM_AM03_AI_CH_1" Value="Analog Input Channel 1" />
          <Text TextId="EM_AM03_AQ_CH_0" Value="Analog Output Channel 0" />
        </PrimaryLanguage>
        
        <Language xml:lang="zh">
          <Text TextId="T_DAP_DEVICE_DESC" Value="该接口模块支持 Profinet RT, 最小的周期时间是 4 毫秒; 最多支持6个扩展模块。"/>
          <Text TextId="Interface_SubslotLabel" Value="X1"/>
          <Text TextId="Port32769_SubslotLabel" Value="端口1"/>
          <Text TextId="DAP_Name_ID" Value="IM60 V1.0 "/>
          <Text TextId="Interface" Value="接口"/>
          <Text TextId="Port_1" Value="端口1"/>
          <Text TextId="IDT_UDIAG1" Value="运行状态："/>
          <Text TextId="IDT_RunningStatus" Value="设备正在运行中"/>
          <Text TextId="IDT_UDIAG1_1" Value="出现错误"/>
          <Text TextId="DAP1_Name" Value="IM60 虚拟子模块"/>
          <Text TextId="DAP1_Comment" Value="IM60, 板载36路输入, 24路输出。最多支持6个扩展模块。"/>
          <Text TextId="Onboard_Inputs_Name" Value="板载输入"/>
          <Text TextId="Onboard_Inputs_Comment" Value=""/>
          <Text TextId="Onboard_Inputs" Value="板载输入"/>
          <Text TextId="Onboard_outputs_Name" Value="板载输出"/>
          <Text TextId="Onboard_outputs_Comment" Value=""/>
          <Text TextId="Onboard_outputs" Value="板载输出"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_0" Value="输入过滤器 (通道  0) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_1" Value="输入过滤器 (通道  1) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_2" Value="输入过滤器 (通道  2) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_3" Value="输入过滤器 (通道  3) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_4" Value="输入过滤器 (通道  4) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_5" Value="输入过滤器 (通道  5) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_6" Value="输入过滤器 (通道  6) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_0_7" Value="输入过滤器 (通道  7) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_0" Value="输入过滤器 (通道  8) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_1" Value="输入过滤器 (通道  9) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_2" Value="输入过滤器 (通道 10) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_3" Value="输入过滤器 (通道 11) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_4" Value="输入过滤器 (通道 12) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_5" Value="输入过滤器 (通道 13) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_6" Value="输入过滤器 (通道 14) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_1_7" Value="输入过滤器 (通道 15) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_0" Value="输入过滤器 (通道 16) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_1" Value="输入过滤器 (通道 17) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_2" Value="输入过滤器 (通道 18) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_3" Value="输入过滤器 (通道 19) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_4" Value="输入过滤器 (通道 20) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_5" Value="输入过滤器 (通道 21) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_6" Value="输入过滤器 (通道 22) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_2_7" Value="输入过滤器 (通道 23) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_0" Value="输入过滤器 (通道 24) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_1" Value="输入过滤器 (通道 25) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_2" Value="输入过滤器 (通道 26) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_3" Value="输入过滤器 (通道 27) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_4" Value="输入过滤器 (通道 28) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_5" Value="输入过滤器 (通道 29) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_6" Value="输入过滤器 (通道 30) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_3_7" Value="输入过滤器 (通道 31) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_0" Value="输入过滤器 (通道 32) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_1" Value="输入过滤器 (通道 33) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_2" Value="输入过滤器 (通道 34) :"/>
          <Text TextId="OnboardDigitalInput_Filter_I_4_3" Value="输入过滤器 (通道 35) :"/>

          <Text TextId="EM_DT_DI_Ch_Grp_0_Input_Filter" Value="输入过滤器 (通道 0, 1, 2, 3) :" />
          <Text TextId="EM_DT_DI_Ch_Grp_1_Input_Filter" Value="输入过滤器 (通道 4, 5, 6, 7) :" />
          <Text TextId="EM_DT_DI_Ch_Grp_2_Input_Filter" Value="输入过滤器 (通道 8, 9, 10, 11) :" />
          <Text TextId="EM_DT_DI_Ch_Grp_3_Input_Filter" Value="输入过滤器 (通道 12, 13, 14, 15) :" />

          <Text TextId="EM_DR_DI_Ch_Grp_0_Input_Filter" Value="输入过滤器 (通道 0, 1, 2, 3) :" />
          <Text TextId="EM_DR_DI_Ch_Grp_1_Input_Filter" Value="输入过滤器 (通道 4, 5, 6, 7) :" />
          <Text TextId="EM_DR_DI_Ch_Grp_2_Input_Filter" Value="输入过滤器 (通道 8, 9, 10, 11) :" />
          <Text TextId="EM_DR_DI_Ch_Grp_3_Input_Filter" Value="输入过滤器 (通道 12, 13, 14, 15) :" />

          <Text TextId="TextId_OnboardDigitalInputs" Value="板载数字量输入" />


          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_0" Value="脉冲捕捉（通道  0）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_1" Value="脉冲捕捉（通道  1）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_2" Value="脉冲捕捉（通道  2）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_3" Value="脉冲捕捉（通道  3）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_4" Value="脉冲捕捉（通道  4）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_5" Value="脉冲捕捉（通道  5）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_6" Value="脉冲捕捉（通道  6）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_0_7" Value="脉冲捕捉（通道  7）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_0" Value="脉冲捕捉（通道  8）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_1" Value="脉冲捕捉（通道  9）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_2" Value="脉冲捕捉（通道 10）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_3" Value="脉冲捕捉（通道 11）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_4" Value="脉冲捕捉（通道 12）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_5" Value="脉冲捕捉（通道 13）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_6" Value="脉冲捕捉（通道 14）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_1_7" Value="脉冲捕捉（通道 15）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_0" Value="脉冲捕捉（通道 16）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_1" Value="脉冲捕捉（通道 17）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_2" Value="脉冲捕捉（通道 18）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_3" Value="脉冲捕捉（通道 19）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_4" Value="脉冲捕捉（通道 20）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_5" Value="脉冲捕捉（通道 21）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_6" Value="脉冲捕捉（通道 22）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_2_7" Value="脉冲捕捉（通道 23）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_0" Value="脉冲捕捉（通道 24）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_1" Value="脉冲捕捉（通道 25）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_2" Value="脉冲捕捉（通道 26）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_3" Value="脉冲捕捉（通道 27）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_4" Value="脉冲捕捉（通道 28）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_5" Value="脉冲捕捉（通道 29）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_6" Value="脉冲捕捉（通道 30）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_3_7" Value="脉冲捕捉（通道 31）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_0" Value="脉冲捕捉（通道 32）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_1" Value="脉冲捕捉（通道 33）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_2" Value="脉冲捕捉（通道 34）："/>
          <Text TextId="OnboardDigitalInput_pulse_catch_I_4_3" Value="脉冲捕捉（通道 35）："/>

          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_0" Value="对 CPU STOP 的响应 (通道  0) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_1" Value="对 CPU STOP 的响应 (通道  1) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_2" Value="对 CPU STOP 的响应 (通道  2) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_3" Value="对 CPU STOP 的响应 (通道  3) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_4" Value="对 CPU STOP 的响应 (通道  4) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_5" Value="对 CPU STOP 的响应 (通道  5) :"/> 
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_6" Value="对 CPU STOP 的响应 (通道  6) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_0_7" Value="对 CPU STOP 的响应 (通道  7) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_0" Value="对 CPU STOP 的响应 (通道  8) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_1" Value="对 CPU STOP 的响应 (通道  9) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_2" Value="对 CPU STOP 的响应 (通道 10) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_3" Value="对 CPU STOP 的响应 (通道 11) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_4" Value="对 CPU STOP 的响应 (通道 12) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_5" Value="对 CPU STOP 的响应 (通道 13) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_6" Value="对 CPU STOP 的响应 (通道 14) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_1_7" Value="对 CPU STOP 的响应 (通道 15) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_0" Value="对 CPU STOP 的响应 (通道 16) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_1" Value="对 CPU STOP 的响应 (通道 17) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_2" Value="对 CPU STOP 的响应 (通道 18) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_3" Value="对 CPU STOP 的响应 (通道 19) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_4" Value="对 CPU STOP 的响应 (通道 20) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_5" Value="对 CPU STOP 的响应 (通道 21) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_6" Value="对 CPU STOP 的响应 (通道 22) :"/>
          <Text TextId="OnboardDigitalOutput_Reaction_TO_CPUSTOP_Q_2_7" Value="对 CPU STOP 的响应 (通道 23) :"/>

          <Text TextId="EM_DT_DQ_Ch0_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  0) :" />
          <Text TextId="EM_DT_DQ_Ch1_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  1) :" />
          <Text TextId="EM_DT_DQ_Ch2_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  2) :" />
          <Text TextId="EM_DT_DQ_Ch3_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  3) :" />
          <Text TextId="EM_DT_DQ_Ch4_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  4) :" />
          <Text TextId="EM_DT_DQ_Ch5_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  5) :" />
          <Text TextId="EM_DT_DQ_Ch6_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  6) :" />
          <Text TextId="EM_DT_DQ_Ch7_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  7) :" />
          <Text TextId="EM_DT_DQ_Ch8_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  8) :" />
          <Text TextId="EM_DT_DQ_Ch9_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  9) :" />
          <Text TextId="EM_DT_DQ_Ch10_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 10) :" />
          <Text TextId="EM_DT_DQ_Ch11_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 11) :" />
          <Text TextId="EM_DT_DQ_Ch12_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 12) :" />
          <Text TextId="EM_DT_DQ_Ch13_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 13) :" />
          <Text TextId="EM_DT_DQ_Ch14_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 14) :" />
          <Text TextId="EM_DT_DQ_Ch15_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 15) :" />

          <Text TextId="EM_DR_DQ_Ch0_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  0) :" />
          <Text TextId="EM_DR_DQ_Ch1_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  1) :" />
          <Text TextId="EM_DR_DQ_Ch2_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  2) :" />
          <Text TextId="EM_DR_DQ_Ch3_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  3) :" />
          <Text TextId="EM_DR_DQ_Ch4_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  4) :" />
          <Text TextId="EM_DR_DQ_Ch5_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  5) :" />
          <Text TextId="EM_DR_DQ_Ch6_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  6) :" />
          <Text TextId="EM_DR_DQ_Ch7_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  7) :" />
          <Text TextId="EM_DR_DQ_Ch8_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  8) :" />
          <Text TextId="EM_DR_DQ_Ch9_Reaction_TO_CPUSTOP"  Value="对 CPU STOP 的响应 (通道  9) :" />
          <Text TextId="EM_DR_DQ_Ch10_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 10) :" />
          <Text TextId="EM_DR_DQ_Ch11_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 11) :" />
          <Text TextId="EM_DR_DQ_Ch12_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 12) :" />
          <Text TextId="EM_DR_DQ_Ch13_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 13) :" />
          <Text TextId="EM_DR_DQ_Ch14_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 14) :" />
          <Text TextId="EM_DR_DQ_Ch15_Reaction_TO_CPUSTOP" Value="对 CPU STOP 的响应 (通道 15) :" />

          <Text TextId="OnboardInput_Filter_0" Value="0.2微秒"/>
          <Text TextId="OnboardInput_Filter_1" Value="0.4微秒" />
          <Text TextId="OnboardInput_Filter_2" Value="0.8微秒" />
          <Text TextId="OnboardInput_Filter_3" Value="1.6微秒" />
          <Text TextId="OnboardInput_Filter_4" Value="3.2微秒" />
          <Text TextId="OnboardInput_Filter_5" Value="6.4微秒" />
          <Text TextId="OnboardInput_Filter_6" Value="12.8微秒" />
          <Text TextId="OnboardInput_Filter_7" Value="0.2毫秒" />
          <Text TextId="OnboardInput_Filter_8" Value="0.4毫秒" />
          <Text TextId="OnboardInput_Filter_9" Value="0.8毫秒" />
          <Text TextId="OnboardInput_Filter_10" Value="1.6毫秒" />
          <Text TextId="OnboardInput_Filter_11" Value="3.2毫秒" />
          <Text TextId="OnboardInput_Filter_12" Value="6.4毫秒" />
          <Text TextId="OnboardInput_Filter_13" Value="12.8毫秒" />
          <Text TextId="OnboardInput_Filter_14" Value="无" />

          <Text TextId="Input_pulse_catch_Enable" Value="已启用"/>
          <Text TextId="Input_pulse_catch_Disable" Value="已禁用"/>

          <Text TextId="TextId_OnboardDigitaloutputs" Value="板载数字量输出：" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_0" Value="关闭输出" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_1" Value="保持上一个值" />
          <Text TextId="OnboardOutput_CPU_STOP_Reaction_2" Value="输出值为 1" />

          <Text TextId="EM_AM06_AI_Name" Value="模拟量输入" />
          <Text TextId="EM_AM06_AQ_Name" Value="模拟量输出" />
          <Text TextId="EM_AM03_AI_Name" Value="模拟量输入" />
          <Text TextId="EM_AM03_AQ_Name" Value="模拟量输出" />

          <Text TextId="EM_AI_Mesurement_Type_Voltage" Value="电压" />
          <Text TextId="EM_AI_Mesurement_Type_Current" Value="电流" />

          <Text TextId="EM_AI_Cfg_Title" Value="模拟量输入配置" />
          <Text TextId="EM_AQ_Cfg_Title" Value="模拟量输出配置" />
          <Text TextId="VM_EM_AR_Cfg_Title" Value="模拟量输入（RTD）配置" />
          <Text TextId="VM_EM_AT_Cfg_Title" Value="模拟量输入（TC）配置" />

          <Text TextId="EM_AI_Mesurement_Type_Cfg" Value="类型" />
          <Text TextId="EM_AI_Mesurement_Range_Cfg" Value="范围"/>
          <Text TextId="EM_AI_Chanel_0_1_Mesurement_Type_Range_Cfg" Value="通道0和通道1测量类型和范围" />
          <Text TextId="EM_AI_Chanel_2_3_Mesurement_Type_Range_Cfg" Value="通道2和通道3测量类型和范围" />
          <Text TextId="EM_AI_Chanel_4_5_Mesurement_Type_Range_Cfg" Value="通道4和通道5测量类型和范围" />
          <Text TextId="EM_AI_Chanel_6_7_Mesurement_Type_Range_Cfg" Value="通道6和通道7测量类型和范围" />
          <Text TextId="EM_AI_Mesurement_Rejection_Cfg" Value="抑制" />
          <Text TextId="EM_AI_Mesurement_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch0_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch1_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch2_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch3_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch4_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch5_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch6_Smoothing_Cfg" Value="滤波" />
          <Text TextId="EM_AI_Mesurement_ch7_Smoothing_Cfg" Value="滤波" />

          <Text TextId="EM_AI_Combined_8Channel_Rejection_Cfg" Value="抑制" />
          <Text TextId="EM_AI_Combined_4Channel_Rejection_Cfg" Value="抑制" />
          <Text TextId="EM_AI_Combined_2Channel_Rejection_Cfg" Value="抑制" />

          <Text TextId="EM_AR_Combined_2Channel_Rejection_Cfg" Value="抑制" />
          <Text TextId="EM_AR_Combined_4Channel_Rejection_Cfg" Value="抑制" />
          <Text TextId="EM_AT_Combined_4Channel_Rejection_Cfg" Value="抑制" />

          <Text TextId="EM_AI_Mesurement_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch0_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch1_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch2_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch3_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch4_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch5_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch6_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch7_En_Upper_Limit_Alarm_Cfg" Value="启用“超出上限”的警报" />

          <Text TextId="EM_AI_Mesurement_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch0_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch1_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch2_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch3_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch4_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch5_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch6_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />
          <Text TextId="EM_AI_Mesurement_ch7_En_Lower_Limit_Alarm_Cfg" Value="启用“超出下限”的警报" />

          <Text TextId="EM_AI_Mesurement_Range_Voltage_0" Value="电压 +/- 2.5v" />
          <Text TextId="EM_AI_Mesurement_Range_Voltage_1" Value="电压 +/- 5v" />
          <Text TextId="EM_AI_Mesurement_Range_Voltage_2" Value="电压 +/- 10v" />
          <Text TextId="EM_AI_Mesurement_Range_Current_0" Value="电流 0 - 20ma" />

          <Text TextId="EM_AI_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AI_Mesurement_Rejection_3" Value="400Hz" />

          <Text TextId="EM_AR_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AR_Mesurement_Rejection_3" Value="400Hz" />

          <Text TextId="EM_AT_Mesurement_Rejection_0" Value="10Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_1" Value="50Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_2" Value="60Hz" />
          <Text TextId="EM_AT_Mesurement_Rejection_3" Value="400Hz" />

          <Text TextId="Resistance 4-wire 48 ohms" Value ="4线制电阻 48 ohms" />
          <Text TextId="Resistance 4-wire 150 ohms" Value ="4线制电阻 150 ohms" />
          <Text TextId="Resistance 4-wire 300 ohms" Value ="4线制电阻 300 ohms" />
          <Text TextId="Resistance 4-wire 600 ohms" Value ="4线制电阻 600 ohms" />
          <Text TextId="Resistance 4-wire 3000 ohms" Value ="4线制电阻 3000 ohms" />

          <Text TextId="Resistance 3-wire 48 ohms" Value ="3线制电阻 48 ohms" />
          <Text TextId="Resistance 3-wire 150 ohms" Value ="3线制电阻 150 ohms" />
          <Text TextId="Resistance 3-wire 300 ohms" Value ="3线制电阻 300 ohms" />
          <Text TextId="Resistance 3-wire 600 ohms" Value ="3线制电阻 600 ohms" />
          <Text TextId="Resistance 3-wire 3000 ohms" Value ="3线制电阻 3000 ohms" />

          <Text TextId="Resistance 2-wire 48 ohms" Value ="2线制电阻 48 ohms" />
          <Text TextId="Resistance 2-wire 150 ohms" Value ="2线制电阻 150 ohms" />
          <Text TextId="Resistance 2-wire 300 ohms" Value ="2线制电阻 300 ohms" />
          <Text TextId="Resistance 2-wire 600 ohms" Value ="2线制电阻 600 ohms" />
          <Text TextId="Resistance 2-wire 3000 ohms" Value ="2线制电阻 3000 ohms" />

          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="4线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="4线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt100(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt200(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt500(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="4线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="4线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="4线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="4线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="4线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="4线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="4线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="4线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="4线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="4线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="4线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="4线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="4线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="4线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="4线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="4线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 4-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="4线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="3线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="3线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt100(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt200(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt500(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="3线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="3线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="3线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="3线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="3线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="3线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="3线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="3线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="3线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="3线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="3线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="3线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="3线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="3线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="3线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="3线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 3-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="3线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt10 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Celsius)" Value ="2线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt10 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt10 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt50 (系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Celsius)" Value ="2线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt50 (Coef:Pt0.003910, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt50 (系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt100(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Celsius)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Celsius)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Celsius)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Celsius)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt100(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt100(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt200(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Celsius)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Celsius)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Celsius)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt200(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt200(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt500(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Celsius)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Celsius)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Celsius)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003920，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Celsius)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003910，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt500(Coef:Pt0.003910, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt500(系数：Pt0.003910，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Celsius)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.00385055, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.00385055，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Celsius)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003916, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003916，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Celsius)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003902, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003902，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Celsius)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Pt1000(Coef:Pt0.003920, Scale: Fahrenheit)" Value ="2线制热敏电阻 Pt1000(系数：Pt0.003920，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Celsius)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006170, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006170，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Celsius)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Celsius)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni100(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni100(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Celsius)" Value ="2线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni120(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Celsius)" Value ="2线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni120(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni120(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Celsius)" Value ="2线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni200(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Celsius)" Value ="2线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni200(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni200(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Celsius)" Value ="2线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni500(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Celsius)" Value ="2线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni500(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni500(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Celsius)" Value ="2线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006180, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni1000(系数：Ni 0.006180，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Celsius)" Value ="2线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Ni1000(Coef:Ni 0.006720, Scale: Fahrenheit)" Value ="2线制热敏电阻 Ni1000(系数：Ni 0.006720，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Celsius)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Celsius)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00428，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Celsius)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu10(Coef:Cu 0.00427, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu10(系数：Cu 0.00427，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Celsius)" Value ="2线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu50(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Celsius)" Value ="2线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu50(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu50(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Celsius)" Value ="2线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00426, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu100(系数：Cu 0.00426，标尺：华氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Celsius)" Value ="2线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire Cu100(Coef:Cu 0.00428, Scale:Fahrenheit)" Value ="2线制热敏电阻 Cu100(系数：Cu 0.00428，标尺：华氏)" />

          <Text TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Celsius)" Value ="2线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：摄氏)" />
          <Text TextId="Thermal Resistance 2-wire LG-Ni1000(Coef:LG-Ni 0.005000, Scale:Fahrenheit)" Value ="2线制热敏电阻 LG-Ni1000(系数：LG-Ni 0.005000，标尺：华氏)" />


          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型B(PtRh-PtRh)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型B(PtRh-PtRh)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型B(PtRh-PtRh)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型B(PtRh-PtRh)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型B(PtRh-PtRh)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type B(PtRh-PtRh)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型B(PtRh-PtRh)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型N(NiCrSi-NiSi)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型N(NiCrSi-NiSi)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型N(NiCrSi-NiSi)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型N(NiCrSi-NiSi)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型N(NiCrSi-NiSi)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type N(NiCrSi-NiSi)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型N(NiCrSi-NiSi)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型E(NiCr-CuNi)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型E(NiCr-CuNi)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型E(NiCr-CuNi)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型E(NiCr-CuNi)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型E(NiCr-CuNi)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type E(NiCr-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型E(NiCr-CuNi)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型R(PtRh-Pt)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型R(PtRh-Pt)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型R(PtRh-Pt)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型R(PtRh-Pt)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型R(PtRh-Pt)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type R(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型R(PtRh-Pt)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型S(PtRh-Pt)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型S(PtRh-Pt)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型S(PtRh-Pt)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型S(PtRh-Pt)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型S(PtRh-Pt)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type S(PtRh-Pt)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型S(PtRh-Pt)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型J(Fe-CuNi)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型J(Fe-CuNi)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型J(Fe-CuNi)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型J(Fe-CuNi)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型J(Fe-CuNi)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type J(Fe-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型J(Fe-CuNi)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型T(Cu-CuNi)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型T(Cu-CuNi)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型T(Cu-CuNi)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型T(Cu-CuNi)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型T(Cu-CuNi)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type T(Cu-CuNi)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型T(Cu-CuNi)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型K(NiCr-Ni)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型K(NiCr-Ni)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型K(NiCr-Ni)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型K(NiCr-Ni)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型K(NiCr-Ni)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type K(NiCr-Ni)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型K(NiCr-Ni)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 0 Celsius）" Value="热电偶-类型C(W5Re-W26Re)-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: 50 Celsius）" Value="热电偶-类型C(W5Re-W26Re)-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Celsius （Source Reference: Internal reference）" Value="热电偶-类型C(W5Re-W26Re)-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-类型C(W5Re-W26Re)-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-类型C(W5Re-W26Re)-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-Type C(W5Re-W26Re)-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-类型C(W5Re-W26Re)-华氏（源参考：内部参考）" />

          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 0 Celsius）" Value="热电偶-TXK/XK (TXK/XK(L))-摄氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: 50 Celsius）" Value="热电偶-TXK/XK (TXK/XK(L))-摄氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Celsius （Source Reference: Internal reference）" Value="热电偶-TXK/XK (TXK/XK(L))-摄氏（源参考：内部参考）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 0 Celsius）" Value="热电偶-TXK/XK (TXK/XK(L))-华氏（源参考：0摄氏）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: 50 Celsius）" Value="热电偶-TXK/XK (TXK/XK(L))-华氏（源参考：50摄氏）" />
          <Text TextId="Thermocouple-TXK/XK (TXK/XK(L))-Fahrenheit（Source Reference: Internal reference）" Value="热电偶-TXK/XK (TXK/XK(L))-华氏（源参考：内部参考））" />

          <Text TextId="Voltage-(±80 mV)" Value="电压-(±80 mV)" />


          <Text TextId="EM_AI_Mesurement_Smoothing_0" Value="无（1个周期）" />
          <Text TextId="EM_AI_Mesurement_Smoothing_1" Value="弱（4个周期）" />
          <Text TextId="EM_AI_Mesurement_Smoothing_2" Value="中（16个周期）" />
          <Text TextId="EM_AI_Mesurement_Smoothing_3" Value="强（32个周期）" />
          <Text TextId="EM_AQ_Mesurement_Type_Cfg" Value="类型" />

          <Text TextId="EM_AQ_Mesurement_Range_Cfg" Value="测量范围" />
          <Text TextId="EM_AQ_Mesurement_ch0_Range_Cfg" Value="测量范围" />
          <Text TextId="EM_AQ_Mesurement_ch1_Range_Cfg" Value="测量范围" />
          <Text TextId="EM_AQ_Mesurement_ch2_Range_Cfg" Value="测量范围" />
          <Text TextId="EM_AQ_Mesurement_ch3_Range_Cfg" Value="测量范围" />

          <Text TextId="EM_AQ_Shutdown_Behavior_Cfg" Value="对 CPU STOP 的响应" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_Cfg" Value="对 CPU STOP 的响应" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_Cfg" Value="对 CPU STOP 的响应" />
          <Text TextId="EM_AQ_Substitute_Value_Cfg" Value="替换值（仅在该通道启用替换值配置时才有效）" />

          <Text TextId="EM_AQ_Substitute_ch0_Value_Cfg" Value="替换值（仅在该通道启用替换值配置时才有效）" />
          <Text TextId="EM_AQ_Substitute_ch1_Value_Cfg" Value="替换值（仅在该通道启用替换值配置时才有效）" />
          <Text TextId="EM_AQ_Substitute_ch2_Value_Cfg" Value="替换值（仅在该通道启用替换值配置时才有效）" />
          <Text TextId="EM_AQ_Substitute_ch3_Value_Cfg" Value="替换值（仅在该通道启用替换值配置时才有效）" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出上限”警报" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出下限”警报" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch0_Cfg" Value="启用“断线”警报（仅对电流有效）" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch0_Cfg" Value="启用“短路”警报（仅对电压有效）" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出上限”警报" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出下限”警报" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch1_Cfg" Value="启用“断线”警报（仅对电流有效）" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch1_Cfg" Value="启用“短路”警报（仅对电压有效）" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出上限”警报" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出下限”警报" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch2_Cfg" Value="启用“断线”警报（仅对电流有效）" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch2_Cfg" Value="启用“短路”警报（仅对电压有效）" />

          <Text TextId="EM_AQ_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出上限”警报" />
          <Text TextId="EM_AQ_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出下限”警报" />
          <Text TextId="EM_AQ_Enable_WireBreak_Alarm_ch3_Cfg" Value="启用“断线”警报（仅对电流有效）" />
          <Text TextId="EM_AQ_Enable_ShortCircuit_Alarm_ch3_Cfg" Value="启用“短路”警报（仅对电压有效）" />

          <Text TextId="EM_AR_Mesurement_Smoothing_Ch0_Cfg" Value="滤波" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch1_Cfg" Value="滤波" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch2_Cfg" Value="滤波" />
          <Text TextId="EM_AR_Mesurement_Smoothing_Ch3_Cfg" Value="滤波" />

          <Text TextId="EM_AT_Mesurement_Smoothing_Ch0_Cfg" Value="滤波" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch1_Cfg" Value="滤波" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch2_Cfg" Value="滤波" />
          <Text TextId="EM_AT_Mesurement_Smoothing_Ch3_Cfg" Value="滤波" />


          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AR_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出上限”警报"/>

          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出上限”警报"/>
          <Text TextId="EM_AT_Enable_Upper_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出上限”警报"/>

          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AR_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出下限”警报"/>

          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch0_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch1_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch2_Cfg" Value="启用“超出下限”警报"/>
          <Text TextId="EM_AT_Enable_Lower_Limit_Exceed_Alarm_ch3_Cfg" Value="启用“超出下限”警报"/>

          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch0_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch1_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch2_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AR_Enable_WireBreak_Alarm_ch3_Cfg" Value="启用“断线”警报" />

          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch0_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch1_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch2_Cfg" Value="启用“断线”警报" />
          <Text TextId="EM_AT_Enable_WireBreak_Alarm_ch3_Cfg" Value="启用“断线”警报" />

          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_0_Cfg" Value="类型" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_1_Cfg" Value="类型" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_2_Cfg" Value="类型" />
          <Text TextId="EM_AR_Type_Resistor_Coef_Scale_Ch_3_Cfg" Value="类型" />

          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_0_Cfg" Value="类型" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_1_Cfg" Value="类型" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_2_Cfg" Value="类型" />
          <Text TextId="EM_AT_Type_Therm_Scale_Src_Ch_3_Cfg" Value="类型" />

          <Text TextId="EM_AM06_Name" Value="AM06(4AI/2AQ) V1.0" />
          <Text TextId="EM_AM06_Info" Value="AM06 V1.0：4点模拟量输入和2点模拟量输出" />
          <Text TextId="EM_AM06_Param_Info" Value="模块参数" />
          <Text TextId="EM_AM06_Input_Info" Value="4点模拟量输入。" />
          <Text TextId="EM_AM06_Output_Info" Value="2点模拟量输出。" />
          <Text TextId="EM_AM06_Module_Parameters" Value="模块参数" />
          <Text TextId="EM_AM06_AI_CH_0" Value="AM06，模拟量输入，通道0" />
          <Text TextId="EM_AM06_AI_CH_1" Value="AM06，模拟量输入，通道1" />
          <Text TextId="EM_AM06_AI_CH_2" Value="AM06，模拟量输入，通道2" />
          <Text TextId="EM_AM06_AI_CH_3" Value="AM06，模拟量输入，通道3" />
          <Text TextId="EM_AM06_AQ_CH_0" Value="AM06，模拟量输出，通道0" />
          <Text TextId="EM_AM06_AQ_CH_1" Value="AM06，模拟量输出，通道1" />

          <Text TextId="EM_Module_Parameters_Alarm_Profile" Value="启用“用户电源”警报" />

          <Text TextId="EM_AQ_Mesurement_Range_Voltage_0" Value="电压 +/- 2.5v"/>
          <Text TextId="EM_AQ_Mesurement_Range_Voltage_1" Value="电压 +/- 5v" />
          <Text TextId="EM_AQ_Mesurement_Range_Voltage_2" Value="电压 +/- 10v" />
          <Text TextId="EM_AQ_Mesurement_Range_Current_0" Value="电流 0 - 20ma" />
          <Text TextId="EM_AQ_Shutdown_Behavior_0" Value="关闭输出" />
          <Text TextId="EM_AQ_Shutdown_Behavior_1" Value="保持上一个值" />
          <Text TextId="EM_AQ_Shutdown_Behavior_2" Value="输出替换值" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_0" Value="关闭输出" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_1" Value="保持上一个值" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_Shutdown_Behavior_2" Value="输出替换值" />

          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_0" Value="关闭输出" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_1" Value="保持上一个值" />
          <Text TextId="EM_AQ_Combined_Channel_0_1_2_3_Shutdown_Behavior_2" Value="输出替换值" />

          <Text TextId="EM_DT32_Name" Value="DT32(16DI/16DQ 晶体管) V1.0" />
          <Text TextId="EM_DT32_DI_Name" Value="数字量输入" />
          <Text TextId="EM_DT32_DQ_Name" Value="数字量输出" />
          <Text TextId="EM_DT32_Info" Value="DT32 V1.0：16点数字量输入和16点数字量输出（晶体管）。" />
          <Text TextId="EM_DT32_Input_Info" Value="16点数字量输入。" />
          <Text TextId="EM_DT32_Output_Info" Value="16点数字量输出（晶体管）。" />
          <Text TextId="EM_DT32_CH_DI_ALL" Value="EM DT32，数字量输入。" />
          <Text TextId="EM_DT32_CH_DQ_ALL" Value="EM DT32，数字量输出。" />

          <Text TextId="EM_CAT_AI_AQ_Name" Value="AI,AQ" />
          <Text TextId="EM_CAT_DI_DQ_Name" Value="DI,DQ" />
          <Text TextId="EM_CAT_AI_Name" Value="AI" />
          <Text TextId="EM_CAT_AQ_Name" Value="AQ" />
          <Text TextId="EM_CAT_DI_Name" Value="DI" />
          <Text TextId="EM_CAT_DQ_Name" Value="DQ" />

          <Text TextId="EM_AE08_Name" Value="AE08(8AI) V1.0" />
          <Text TextId="EM_AE08_Info" Value="AE08 V1.0：8点模拟量输入。" />
          <Text TextId="EM_AE08_AI_Name" Value="模拟量输入" />
          <Text TextId="EM_AE08_CH_0" Value="通道 0" />
          <Text TextId="EM_AE08_CH_1" Value="通道 1" />
          <Text TextId="EM_AE08_CH_2" Value="通道 2" />
          <Text TextId="EM_AE08_CH_3" Value="通道 3" />
          <Text TextId="EM_AE08_CH_4" Value="通道 4" />
          <Text TextId="EM_AE08_CH_5" Value="通道 5" />
          <Text TextId="EM_AE08_CH_6" Value="通道 6" />
          <Text TextId="EM_AE08_CH_7" Value="通道 7" />

          <Text TextId="EM_AQ04_Name" Value="AQ04(4AQ) V1.0" />
          <Text TextId="EM_AQ04_Info" Value="AQ04 V1.0：4点模拟量输出。" />
          <Text TextId="EM_AQ04_AQ_Name" Value="模拟量输出" />

          <Text TextId="EM_AQ04_CH_0" Value="通道 0" />
          <Text TextId="EM_AQ04_CH_1" Value="通道 1" />
          <Text TextId="EM_AQ04_CH_2" Value="通道 2" />
          <Text TextId="EM_AQ04_CH_3" Value="通道 3" />


          <Text TextId="EM_DE16_Name" Value="DE16(16DI) V1.0" />
          <Text TextId="EM_DE16_Info" Value="DE16 V1.0：16点数字量输入。" />
          <Text TextId="EM_DE16_CH_ALL" Value="数字量输入" />
          <Text TextId="EM_DE16_DI_Name" Value="数字量输入" />

          <Text TextId="EM_QR16_Name" Value="QR16(16DQ 继电器) V1.0" />
          <Text TextId="EM_QR16_Info" Value="QR16 V1.0：16点数字量输出（继电器）。" />
          <Text TextId="EM_QR16_CH_ALL" Value="数字量输出" />
          <Text TextId="EM_QR16_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_AR02_Name" Value="AR02(2AI RTD) V1.0" />
          <Text TextId="EM_AR02_Info" Value="AR02 V1.0：2点模拟量输入(RTD)。" />
          <Text TextId="EM_AR02_CH_0" Value="RTD 通道 0" />
          <Text TextId="EM_AR02_CH_1" Value="RTD 通道 1" />

          <Text TextId="EM_AR04_Name" Value="AR04(4AI RTD) V1.0" />
          <Text TextId="EM_AR04_Info" Value="AR04 V1.0：4点模拟量输入(RTD)。" />
          <Text TextId="EM_AR04_CH_0" Value="RTD 通道 0" />
          <Text TextId="EM_AR04_CH_1" Value="RTD 通道 1" />
          <Text TextId="EM_AR04_CH_2" Value="RTD 通道 2" />
          <Text TextId="EM_AR04_CH_3" Value="RTD 通道 3" />

          <Text TextId="EM_AT04_Name" Value="AT04(4AI TC) V1.0" />
          <Text TextId="EM_AT04_Info" Value="AT04 V1.0：4点模拟量输入（TC）。" />
          <Text TextId="EM_AT04_CH_0" Value="热电偶 通道 0" />
          <Text TextId="EM_AT04_CH_1" Value="热电偶 通道 1" />
          <Text TextId="EM_AT04_CH_2" Value="热电偶 通道 2" />
          <Text TextId="EM_AT04_CH_3" Value="热电偶 通道 3" />

          <Text TextId="EM_DE08_Name" Value="DE08(8DI) V1.0" />
          <Text TextId="EM_DE08_Info" Value="DE08 V1.0：8点数字量输入。" />
          <Text TextId="EM_DE08_CH_ALL" Value="数字量输入" />
          <Text TextId="EM_DE08_DI_Name" Value="数字量输入" />

          <Text TextId="EM_DT08_Name" Value="DT08(8DQ 晶体管) V1.0" />
          <Text TextId="EM_DT08_Info" Value="DT08 V1.0：8点数字量输出（晶体管）。" />
          <Text TextId="EM_DT08_CH_ALL" Value="数字量输出" />
          <Text TextId="EM_DT08_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_DR08_Name" Value="DR08(8DQ 继电器) V1.0" />
          <Text TextId="EM_DR08_Info" Value="DR08 V1.0：8点数字量输出（继电器）。" />
          <Text TextId="EM_DR08_CH_ALL" Value="数字量输出" />
          <Text TextId="EM_DR08_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_QT16_Name" Value="QT16(16DQ 晶体管) V1.0" />
          <Text TextId="EM_QT16_Info" Value="QT16 V1.0：16点数字量输出（晶体管）。" />
          <Text TextId="EM_QT16_CH_ALL" Value="数字量输出" />
          <Text TextId="EM_QT16_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_DT16_Name" Value="DT16(8DI/8DQ 晶体管) V1.0" />
          <Text TextId="EM_DT16_Info" Value="DT16 V1.0：8点数字量输入和8点数字量输出（晶体管）。" />
          <Text TextId="EM_DT16_Input_Info" Value="8点数字量输入。" />
          <Text TextId="EM_DT16_Output_Info" Value="8点数字量输出（晶体管）。" />
          <Text TextId="EM_DT16_CH_DI_ALL" Value="数字量输入" />
          <Text TextId="EM_DT16_CH_DQ_ALL" Value="数字量输出" />
          <Text TextId="EM_DT16_DI_Name" Value="数字量输入" />
          <Text TextId="EM_DT16_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_DR16_Name" Value="DR16(8DI/8DQ 继电器) V1.0" />
          <Text TextId="EM_DR16_Info" Value="DR16 V1.0：8点数字量输入和8点数字量输出（继电器）。" />
          <Text TextId="EM_DR16_Input_Info" Value="8点数字量输入。" />
          <Text TextId="EM_DR16_Output_Info" Value="8点数字量输出（继电器）。" />
          <Text TextId="EM_DR16_CH_DI_ALL" Value="数字量输入" />
          <Text TextId="EM_DR16_CH_DQ_ALL" Value="数字量输出" />

          <Text TextId="EM_DR16_DI_Name" Value="数字量输入" />
          <Text TextId="EM_DR16_DQ_Name" Value="数字量输出" />

          <Text TextId="EM_DR32_Name" Value="DR32(16DI/16DQ 继电器) V1.0" />
          <Text TextId="EM_DR32_Info" Value="DR32 V1.0：16点数字量输入和16点数字量输出（继电器）。" />
          <Text TextId="EM_DR32_Input_Info" Value="16点数字量输入。" />
          <Text TextId="EM_DR32_Output_Info" Value="16点数字量输出（继电器）。" />
          <Text TextId="EM_DR32_CH_DI_ALL" Value="数字量输入" />
          <Text TextId="EM_DR32_CH_DQ_ALL" Value="数字量输出" />
          <Text TextId="EM_DR32_DI_Name" Value="数字量输入" />
          <Text TextId="EM_DR32_DQ_Name" Value="数字量输出" />


          <Text TextId="EM_AE04_Name V1.0" Value="AE04(4AI) V1.0" />
          <Text TextId="EM_AE04_Info V1.0" Value="AE04 V1.0：4点模拟量输入。" />
          <Text TextId="EM_AE04_Name V1.1" Value="AE04(4AI) V1.1" />
          <Text TextId="EM_AE04_Info V1.1" Value="AE04 V1.1：4点模拟量输入。" />
          <Text TextId="EM_AE04_AI_Name V1.0" Value="模拟量输入" />
          <Text TextId="EM_AE04_AI_Name V1.1" Value="模拟量输入" />
          <Text TextId="EM_AE04_CH_0" Value="模拟量输入通道 0" />
          <Text TextId="EM_AE04_CH_1" Value="模拟量输入通道 1" />
          <Text TextId="EM_AE04_CH_2" Value="模拟量输入通道 2" />
          <Text TextId="EM_AE04_CH_3" Value="模拟量输入通道 3" />

          <Text TextId="EM_AQ02_Name" Value="AQ02(2AQ) V1.0" />
          <Text TextId="EM_AQ02_Info" Value="AQ02 V1.0：2点模拟量输出。" />
          <Text TextId="EM_AQ02_AQ_Name" Value="模拟量输出" />
          <Text TextId="EM_AQ02_CH_0" Value="模拟量输出通道 0" />
          <Text TextId="EM_AQ02_CH_1" Value="模拟量输出通道 1" />

          <Text TextId="EM_AM03_Module_Parameters" Value="模块参数" />
          <Text TextId="EM_AM03_Name" Value="AM03(2AI/1AQ) V1.0" />
          <Text TextId="EM_AM03_Info" Value="AM03 V1.0：2点模拟量输入和1点模拟量输出。" />
          <Text TextId="EM_AM03_Param_Info" Value="模块参数" />
          <Text TextId="EM_AM03_Input_Info" Value="2点模拟量输入。" />
          <Text TextId="EM_AM03_Output_Info" Value="1点模拟量输出。" />
          <Text TextId="EM_AM03_AI_CH_0" Value="模拟量输入通道 0" />
          <Text TextId="EM_AM03_AI_CH_1" Value="模拟量输入通道 1 " />
          <Text TextId="EM_AM03_AQ_CH_0" Value="模拟量输出通道 0" />
        </Language>
      </ExternalTextList>
    </ApplicationProcess>
  </ProfileBody>
</ISO15745Profile>