/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: InputCheckerHelper.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker
{
    internal static class InputCheckerHelper
    {
        internal static List<string> GetAllVirtualSubmoduleCatalogIds(
            DecentralDeviceType decentralDevice,
            string gsdFileName,
            string moduleId,
            ConfigReader.ListOfNodes.ListOfNodes listOfNodes)
        {
            List<string> retval = new List<string>();

            if (decentralDevice.Module.Exists(m => m.ModuleID == moduleId))
            {
                ModuleCatalog moduleCatalog = ModuleCatalogHelper.GetModuleCatalogWithGsdName(
                    gsdFileName,
                    decentralDevice.Module.FirstOrDefault(m => m.ModuleID == moduleId)?.GSDRefID);

                if (moduleCatalog != null
                    && moduleCatalog.VirtualSubmoduleList != null)
                {
                    retval = moduleCatalog.VirtualSubmoduleList.Select(
                        vsm => (string)vsm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]).ToList();
                }
            }

            if (decentralDevice.DeviceRefID == moduleId)
            {
                ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(decentralDevice.DeviceRefID, listOfNodes);
                DecentralDeviceCatalog deviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDecentralDevice?.GSDRefID);
                if (deviceCatalog != null
                    && deviceCatalog.VirtualSubmoduleList != null)
                {
                    retval = deviceCatalog.VirtualSubmoduleList.Select(
                        vsm => (string)vsm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]).ToList();
                }
            }

            return retval;
        }
    }
}
