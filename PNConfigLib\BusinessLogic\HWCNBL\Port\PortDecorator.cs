/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PortDecorator.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.Topology;

#endregion

namespace PNConfigLib.HWCNBL.Port
{
    public abstract class PortDecorator : IPortBL
    {
        private IPortBL PortBL;

        protected PortDecorator(IPortBL decoratedPort)
        {
            PortBL = decoratedPort;
        }

        public DataModel.PCLObjects.Port Port
        {
            get { return PortBL.Port; }
        }

        public void Configure(PortType xmlPort, TopologyTypePortInterconnection xmlPortInterconnection, int numberOfPorts, bool isLocal)
        {
            PortBL.Configure(xmlPort, xmlPortInterconnection, numberOfPorts, isLocal);
        }

        public virtual void InitBL()
        {
            PortBL.InitBL();
        }
    }
}