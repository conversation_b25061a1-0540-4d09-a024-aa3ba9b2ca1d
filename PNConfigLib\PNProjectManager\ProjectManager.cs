/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnProjectManager                          :C&  */
/*                                                                           */
/*  F i l e               &F: ProjectManager.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.LAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.GSDImport;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.Compiler;
using PNConfigLib.Compiler.DataTypes;
using PNConfigLib.Consistency.Checker.ConfigurationFileChecker;

using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

#endregion

namespace PNConfigLib.PNProjectManager
{
    /// <summary>
    /// This singleton class is the entry point for the user which orchestrates the execution of PNConfigLib.
    /// </summary>
    /// <remarks>
    /// All the actions needed to create a PNConfigLib project, from receiving the input files to compiling
    /// the project to creating and returning the output, are arranged through this class.
    /// </remarks>
    internal class ProjectManager
    {
        /// <summary>
        /// The list that contains IO Systems for this project.
        /// </summary>
        internal object IOSystemList = new List<HwConfiguration>();

        /// <summary>
        /// The object that contains data about the Project Manager.
        /// </summary>
        private Project m_Project;

        internal PNConfigLibResult RunPNConfigLib(
            string configXmlPath,
            string listOfNodesXmlPath,
            string topologyXmlPath = null)
        {
            #region Initialization

            LAddressContainer.UsedAddresses = new Dictionary<LAddress, PclObject>();
            ConsistencyManager consistencyManager = new ConsistencyManager();
            ConfigReader.ConfigReader configReader = new ConfigReader.ConfigReader();
            PNConfigLibCompiler compiler = new PNConfigLibCompiler();
            bool topologyExists;
            PNConfigLibResult result = new PNConfigLibResult();
            Reset();

            #endregion Initialization

            if (consistencyManager.ValidateInputPaths(
                    configXmlPath,
                    listOfNodesXmlPath,
                    topologyXmlPath,
                    out topologyExists)
                && consistencyManager.ValidateXsd(configXmlPath, listOfNodesXmlPath, topologyXmlPath, topologyExists))
            {
                Configuration configuration;
                ListOfNodes listOfNodes;
                Topology topology;
                configReader.DeserializeXMLFiles(
                    configXmlPath,
                    out configuration,
                    listOfNodesXmlPath,
                    out listOfNodes,
                    topologyXmlPath,
                    topologyExists,
                    out topology);

                m_Project = new Project(configuration.ConfigurationName);

                if (Consistency.Checker.ListOfNodesChecker.AreGsdmlFilesExist(listOfNodes.DecentralDevice, listOfNodesXmlPath)
                    && Converter.ImportGSDMLs(listOfNodes, consistencyManager, listOfNodesXmlPath)
                    && consistencyManager.AreInputsValid(configuration, listOfNodes, topology, listOfNodesXmlPath))
                {
                    ConfigureManager configureManager =
                        new ConfigureManager(m_Project, configuration, listOfNodes, topology);
                    configureManager.Configure(topologyExists, listOfNodesXmlPath);

                    List<Interface> interfaces = GetAllIODInterfaces();

                    IEnumerable<DecentralDeviceTypeDecentralDeviceInterface> xmlInterfaces =
                        configuration.Devices.DecentralDevice.Select(d => d.DecentralDeviceInterface);

                    if (PortChecker.IsPortParameterizationValid(xmlInterfaces, interfaces))
                    {
                        List<HwConfiguration> ioSystems = GenerateIOSystems();

                        if (consistencyManager.ValidatePN())
                        {
                            var returnObject = compiler.Run(ioSystems);
                            result.SetReturnObject(returnObject);
                        }
                    }
                }
            }

            var messages = consistencyManager.GetAllMessages();
            result.SetMessages(messages);
            return result;
        }

        private List<Interface> GetAllIODInterfaces()
        {
            List<Interface> retval = new List<Interface>();

            IEnumerable<IBusinessLogic> iodInterfaceBlList =
                m_Project.BusinessLogicList.Where(
                    bl => (bl as IInterfaceBusinessLogic)?.Interface?.PNIOD != null);

            foreach (var businessLogic in iodInterfaceBlList)
            {
                var ifBusinessLogic = (IInterfaceBusinessLogic)businessLogic;
                retval.Add(ifBusinessLogic.Interface);
            }

            return retval;
        }

        private List<HwConfiguration> GenerateIOSystems()
        {
            List<CentralDeviceBL> centralDevices = new List<CentralDeviceBL>();
            foreach (IBusinessLogic bl in m_Project.BusinessLogicList)
            {
                CentralDeviceBL centralDeviceBl = bl as CentralDeviceBL;
                if (centralDeviceBl != null)
                {
                    centralDevices.Add(centralDeviceBl);
                }
            }
            List<HwConfiguration> ioSystems = new List<HwConfiguration>();
            foreach (CentralDeviceBL centralDevice in centralDevices)
            {
                HwConfiguration hwConfiguration = new HwConfiguration(centralDevice);
                hwConfiguration.GenerateChildren();
                ioSystems.Add(hwConfiguration);
            }
            IOSystemList = ioSystems;
            return ioSystems;
        }

        private void Reset()
        {
            Catalog.Reset();
            ConsistencyManager.Reset();
        }
    }
}