<UserControl x:Class="PNConfigTool.Views.Controls.IPAddressInput"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:PNConfigTool.Views.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="200">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        
        <TextBox x:Name="Part1" Grid.Column="0" Width="40" MaxLength="3" 
                 PreviewTextInput="NumberValidationTextBox" 
                 TextChanged="IPTextBox_TextChanged"
                 GotFocus="TextBox_GotFocus"
                 LostFocus="TextBox_LostFocus"/>
        <TextBlock Grid.Column="1" Text="." Margin="2,0"/>
        <TextBox x:Name="Part2" Grid.Column="2" Width="40" MaxLength="3" 
                 PreviewTextInput="NumberValidationTextBox" 
                 TextChanged="IPTextBox_TextChanged"
                 GotFocus="TextBox_GotFocus"
                 LostFocus="TextBox_LostFocus"/>
        <TextBlock Grid.Column="3" Text="." Margin="2,0"/>
        <TextBox x:Name="Part3" Grid.Column="4" Width="40" MaxLength="3" 
                 PreviewTextInput="NumberValidationTextBox" 
                 TextChanged="IPTextBox_TextChanged"
                 GotFocus="TextBox_GotFocus"
                 LostFocus="TextBox_LostFocus"/>
        <TextBlock Grid.Column="5" Text="." Margin="2,0"/>
        <TextBox x:Name="Part4" Grid.Column="6" Width="40" MaxLength="3" 
                 PreviewTextInput="NumberValidationTextBox" 
                 TextChanged="IPTextBox_TextChanged"
                 GotFocus="TextBox_GotFocus"
                 LostFocus="TextBox_LostFocus"/>
    </Grid>
</UserControl> 