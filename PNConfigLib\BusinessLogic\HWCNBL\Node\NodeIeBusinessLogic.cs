/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NodeIeBusinessLogic.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Net;
using PNConfigLib.HWCNBL.Networks.Ethernet;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;
using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.Node
{
    internal class NodeIeBusinessLogic : NodeBusinessLogic, INodeIeBusinessLogic
    {
        public NodeIeBusinessLogic(DataModel.PCLObjects.Node node) : base(node)
        {
            m_Utility = new UtilityNodeIe(this);
            m_NodeIeController = new NodeControllerIe(this);
            Initialize();
        }

        #region Overrides and Overridables

        /// <summary> Add attributes when PCL object is created. </summary>
        /// <remarks>
        /// Take a look at every attribute which value is here set from meta knowledge
        /// if it is useful to add it to RemoveAttributesFilledFromMetaKnowledge() and let it be
        /// re-initialized after exchange.
        /// </remarks>
        private void AddAttributes()
        {
            //read default addresses
            //first look for them in Device Item metadata, then in Ethernet Node metadata.
            //if they can't be found use hardcoded addresses as default
            AttributeAccessCode aac = new AttributeAccessCode();
            string defaultIPAddress = Node.AttributeAccess.GetAnyAttribute<string>(
                InternalAttributeNames.NodeDefaultIPAddress,
                aac,
                PNConstants.DefaultIPAddress);
            aac.Reset();
            string defaultIPSubnetMask = Node.AttributeAccess.GetAnyAttribute<string>(
                InternalAttributeNames.NodeIPSubnetMaskDefault,
                aac,
                PNConstants.DefaultSubnetMask);
            aac.Reset();
            string defaultIPDefaultRouterAddress =
                Node.AttributeAccess.GetAnyAttribute<string>(
                   InternalAttributeNames.NodeIPDefaultRouterAddressDefault,
                    aac,
                    PNConstants.DefaultIPAddress);
            aac.Reset();
            string defaultIPConfiguration =
                Node.AttributeAccess.GetAnyAttribute<string>(InternalAttributeNames.NodeIPConfigurationDefault, aac, "0");
            Node.AttributeAccess.SetAnyAttribute<long>(
                InternalAttributeNames.NodeIPAddress,
                new IPAddress(defaultIPAddress, Node).AsInt64);
            Node.AttributeAccess.SetAnyAttribute<int>(
                InternalAttributeNames.NodeIPConfiguration,
                Convert.ToInt32(defaultIPConfiguration, CultureInfo.InvariantCulture));
            Node.AttributeAccess.SetAnyAttribute<long>(
                InternalAttributeNames.NodeIPDefaultRouterAddress,
                new IPDefaultRouterAddress(defaultIPDefaultRouterAddress, Node).AsInt64);
            Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.NodeIPDefaultRouterAddressUsed, false);

            aac.Reset();
            Node.AttributeAccess.SetAnyAttribute<long>(
                InternalAttributeNames.NodeIPSubnetMask,
                new IPSubnetMask(defaultIPSubnetMask, Node).AsInt64);
            Node.AttributeAccess.AddAnyAttribute(InternalAttributeNames.IsAssignDeviceActivated, false);
            Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.NodeIPAddresseSetByUser, false);

            aac.Reset();
            Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPnIpSuiteViaOtherPath, false);
            Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPnNoSViaOtherPath, false);
            Node.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPnNoSAutoGenerate, true);

            // Decentral side of the "IP suite via other path" 
            bool pnIoIpSuiteViaOtherPath = PNAttributeUtility.GetPNIoIpConfigModeSupported((Interface)Node.ParentObject)
                                           == PNIoIpConfigModeSupported.Local;
            Node.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                pnIoIpSuiteViaOtherPath);

            if (!IsPNPNIpConfigModeSupported)
            {
                PNPNIpSuiteViaOtherPath = false;
            }

            if (!IsPNIoIpConfigModeSupported)
            {
                PNIoIpSuiteViaOtherPath = false;
            }

            if (IsIPBackplaneRoutingSupported)
            {
                Node.AttributeAccess.AddAnyAttribute(InternalAttributeNames.NodeIPBackplaneRouting, false);
            }
            Node.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.NodeIPProtocolSupported, true);
        }

        #endregion

        #region Private Implementation

        #endregion

        private void Initialize()
        {
            AddAttributes();

            Node.BaseActions.RegisterMethod(
                NodeGetDeviceNameOfStationBlock.Name,
                GenericMethodGetDeviceNameOfStationBlock);
            Node.BaseActions.RegisterMethod(GetNetworkParameter.Name, GenericMethodGetNetworkParameters);
            Node.BaseActions.RegisterMethod(NodeIPSuiteConfiguration.Name, GenericMethodDeactivateAddressTailoring);

            ConsistencyManager.RegisterConsistencyCheck(Node, CheckConsistency);
        }

        #region Constants and Enums

        private readonly UtilityNodeIe m_Utility;

        private readonly NodeControllerIe m_NodeIeController;
        public enum PNIoIpConfigModeSupported
        {
            NotSupported = 0,

            Local = 4
        }

        internal enum Supernet
        {
            Supported = 1,
            NotSupported = 0,
            Unknown = -1
        }

        #endregion

        #region Properties


        private bool IsIPBackplaneRoutingSupported => Node.AttributeAccess.GetAnyAttribute<bool>(
            InternalAttributeNames.NodeIPBackplaneRoutingSupported,
            new AttributeAccessCode(),
            false);

        private bool NodeIeCheckProtocolPresence => Node.AttributeAccess.GetAnyAttribute<bool>(
            InternalAttributeNames.NodeIECheckProtocolPresence,
            new AttributeAccessCode(),
            false);

        internal bool NodeGetsAddressAutomatically
        {
            get
            {
                AttributeAccessCode code = new AttributeAccessCode();
                bool result = Node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.NodeGetsAddressAutomatically,
                    code,
                    false);

                Debug.Assert(code.IsOkay, "Could not read attribute NodeGetsAddressAutomatically");

                return result;
            }
            set
            {
                AttributeAccessCode code =
                    Node.AttributeAccess.SetAnyAttribute(InternalAttributeNames.NodeGetsAddressAutomatically, value);

                Debug.Assert(code.IsOkay, "Could not write attribute NodeGetsAddressAutomatically");
            }
        }

        /// <summary> Returns AddressTailoring state for an IOSystem controller node. </summary>
        internal bool AddressTailoringEnabled
        {
            get
            {
                if (IOControllerInterface == null)
                {
                    return false;
                }

                return
                    AddressTailorUtility.IsAddressTailoringEnabledIoControllerInterfaceStartObject(
                        IOControllerInterface);
            }
        }

        internal bool PNPNIpSuiteViaOtherPath
        {
            get
            {
                return Node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                    new AttributeAccessCode(),
                    false);
            }
            set
            {
                Node.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnPnIpSuiteViaOtherPath, value);
            }
        }

        /// <summary> Is PN Name set directly at the device </summary>
        private bool PNPNNoSViaOtherPath
        {
            get
            {
                return Node.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPnNoSViaOtherPath,
                    new AttributeAccessCode(),
                    false);
            }
        }

        internal bool PNIoIpSuiteViaOtherPath
        {
            get
            {
                return Node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                    new AttributeAccessCode(),
                    false);
            }
            set
            {
                Node.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnIoIpSuiteViaOtherPath, value);
            }
        }

        /// <summary> indicate if the device use IP Protocol </summary>
        public bool IPProtocolUsed => Node.AttributeAccess.GetAnyAttribute<bool>(
            InternalAttributeNames.NodeIPProtocolUsed,
            new AttributeAccessCode(),
            false);

        /// <summary>
        /// Indicates which operating mode is currently used by a device. It does NOT indicates which operating modes are supported -> MDD Enum
        /// </summary>
        private PNIOOperatingModes PNIoOperatingModeAdjusted
        {
            get
            {
                if (Node.GetInterface() == null)
                {
                    return (UInt32)PNIOOperatingModes.None;
                }
                return (PNIOOperatingModes)Node.GetInterface().AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    (UInt32)PNIOOperatingModes.None);
            }
        }
        private bool IsPureIODevice => (PNIoOperatingModeAdjusted != PNIOOperatingModes.IOControllerAndIODevice) && (PNIoOperatingModeAdjusted == PNIOOperatingModes.IODevice);
        public IPAddress IPAddress
        {
            get
            {
                return new IPAddress(Node);
            }
            set
            {
                if (!value.IsEqual(Node))
                {
                    m_Utility.InternalSetIPAddress(value, true);
                }
            }
        }

        public IPSubnetMask IPSubnetMask
        {
            get
            {
                return new IPSubnetMask(Node);
            }
            set
            {
                m_Utility.InternalSetIPSubnetMask(value, true);
            }
        }

        /// <summary> indicate if the default router is in use </summary>
        public bool IPDefaultRouterAddressUsed
        {
            get
            {
                return IsIPConfiguredInProject
                       && Node.AttributeAccess.GetAnyAttribute(
                           InternalAttributeNames.NodeIPDefaultRouterAddressUsed,
                           new AttributeAccessCode(),
                           false);
            }
            set
            {
                Debug.Assert(IPProtocolUsed);
                Node.AttributeAccess.SetAnyAttribute(InternalAttributeNames.NodeIPDefaultRouterAddressUsed, value);
            }
        }

        /// <summary> IP Address of the default router </summary>
        public IPDefaultRouterAddress IPDefaultRouterAddress
        {
            get
            {
                return new IPDefaultRouterAddress(Node);
            }
            set
            {
                m_Utility.InternalSetIPDefaultRouterAddress(value);
            }
        }

        /// <summary> the way the node get its IP address </summary>
        public NodeIPConfiguration IPConfiguration
        {
            get
            {
                if (IPProtocolUsed)
                {
                    return
                        (NodeIPConfiguration)
                        Node.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.NodeIPConfiguration,
                            new AttributeAccessCode(),
                            (int)NodeIPConfiguration.None);
                }
                return NodeIPConfiguration.None;
            }
            set
            {
                Debug.Assert(IPProtocolUsed);
                Node.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, (int)value);
            }
        }

        public Supernet CanSuperneting
        {
            get
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                var attributeValue = Node.GetInterface().AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.NodeIPSubnetMaskUnrestricted,
                    ac,
                    false);

                var exist = ac.IsOkay;

                Supernet canSupernet = attributeValue ? Supernet.Supported : Supernet.NotSupported;

                return !exist ? Supernet.Unknown : canSupernet;
            }
        }

        /// <summary> Check if PNIpConfigMode Supported. </summary>
        internal bool IsPNPNIpConfigModeSupported
        {
            get
            {
                if (Node.ParentObject == null)
                {
                    return false;
                }

                AttributeAccessCode acode = new AttributeAccessCode();
                int pnPNIpConfigModeSupported =
                    Node.ParentObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnPnIpConfigModeSupported,
                        acode,
                        0);

                return pnPNIpConfigModeSupported != 0;
            }
        }

        /// <summary> Check if PNIpConfigMode Supported. </summary>
        internal bool IsPNIoIpConfigModeSupported
        {
            get
            {
                AttributeAccessCode acode = new AttributeAccessCode();
                int pnIoIpConfigModeSupported = 0;

                if (Node.ParentObject != null)
                {
                    pnIoIpConfigModeSupported =
                        Node.ParentObject.AttributeAccess.GetAnyAttribute(
                            InternalAttributeNames.PnIoIpConfigModeSupported,
                            acode,
                            0);
                }
                return acode.IsOkay && (pnIoIpConfigModeSupported != 0);
            }
        }

        /// <summary> Check if PNPNIpSuiteViaOtherPath attribute is active. </summary>
        internal bool IsPNPNIpSuiteViaOtherPathActive => IsPNPNIpConfigModeSupported && PNPNIpSuiteViaOtherPath;

        internal bool IsAddressTailoredIDevice
        {
            get
            {
                Interface ifSubmodule = Node.GetInterface();
                if (ifSubmodule == null)
                    return false;

                return IsControllerNode
                    && AttributeUtilities.IsIDevice(ifSubmodule)
                    && AddressTailoringEnabled
                    && IsPNPNIpSuiteViaOtherPathActive;
            }
        }

        /// <summary> Check if PNPNNoSViaOtherPath attribute is active. </summary>
        internal bool IsPNPNNoSViaOtherPathActive => IsPNPNIpConfigModeSupported && PNPNNoSViaOtherPath;

        /// <summary> Check if PNIoIpSuiteViaOtherPath attribute is active. </summary>
        internal bool IsPNIoIpSuiteViaOtherPathActive
        {
            get
            {
                //We need to skip the supported attribute for a moment here, because of an incomplete (IP via other path for IODs) feature
                if ((IsPNIoIpConfigModeSupported == false)
                    && !AddressTailoringEnabled)
                {
                    return false;
                }

                return PNIoIpSuiteViaOtherPath;
            }
        }

        internal PNIOOperatingModes PNIoOperatingMode => (PNIOOperatingModes)
            Node.ParentObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnIoOperatingMode,
                new AttributeAccessCode(),
                (uint)PNIOOperatingModes.None);

        #endregion

        #region Public Methods

        #region GET Functions

        public string GetPNNameOfStation()
        {
            bool isPNPNNoSAutoGenerate = Node.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnPnNoSAutoGenerate,
                new AttributeAccessCode(),
                true);
            string nameOfStation;

            if (isPNPNNoSAutoGenerate || (IsIOSystemController && AddressTailoringEnabled))
            {
                nameOfStation = Node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnNameOfStationVirtual,
                    new AttributeAccessCode(),
                    String.Empty);
            }
            else
            {
                nameOfStation = Node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnNameOfStation,
                    new AttributeAccessCode(),
                    String.Empty);
            }
            return nameOfStation;
        }

        #endregion

        /// <summary>
        /// Set default address. If the node is for the first time connected to subnet this function is called. It
        /// generates new unique ( within the subnet ) address
        /// </summary>
        /// <param name="subnet">subnet to which the node connects</param>
        /// <returns>true if succeeded</returns>
        public override bool SetDefaultAddress(Subnet subnet)
        {
            if (subnet != null)
            {
                INetIeBusinessLogic subnetIeBusinessLogic = subnet.NetBL as NetIeBusinessLogic;
                if (subnetIeBusinessLogic != null)
                {
                    bool retValue = true;
                    if (IPProtocolUsed)
                    {
                        retValue &= m_Utility.SetDefaultIPAddress(subnetIeBusinessLogic);
                    }
                    return retValue;
                }
                Debug.Fail("NetIEBL not found.");
                return false;
            }
            Debug.Fail("Subnet not found.");
            return false;
        }

        #endregion

        #region INodeIEBL Members

        public bool IsIPConfiguredInProject
            =>
                IPProtocolUsed && (IPConfiguration == NodeIPConfiguration.Project) && !PNPNIpSuiteViaOtherPath
                && !PNIoIpSuiteViaOtherPath;

        /// <summary> Returns true, if the node belongs to a controller; otherwise, false. </summary>
        public bool IsControllerNode
        {
            get
            {
                // Go to Controller
                Interface ifSubmodule = Node.ParentObject as Interface;
                return (ifSubmodule != null) && (ifSubmodule.PNIOC != null);
            }
        }

        /// <summary> Returns true, if the node belongs to a device; otherwise, false. </summary>
        public bool IsDeviceNode
        {
            get
            {
                Interface ifSubmodule = Node.ParentObject as Interface;
                return ifSubmodule?.PNIOD != null;
            }
        }

        private bool IsIOSystemController
        {
            get
            {
                Interface ifSubmodule = Node.ParentObject as Interface;
                if (ifSubmodule != null)
                {
                    return IsControllerNode && !AttributeUtilities.IsIDevice(ifSubmodule);
                }
                return false;
            }
        }

        internal Interface IOControllerInterface
        {
            get
            {
                Interface ioController = null;
                if (IsIOSystemController)
                {
                    ioController = Node.ParentObject as Interface;
                }
                return ioController ?? NavigationUtilities.GetControllerOfDevice(Node.ParentObject as Interface);
            }
        }

        public bool IPDefaultRouterIodSync
        {
            get
            {
                if (!IsPureIODevice)
                {
                    return false;
                }
                return Node.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.NodeIPDefaultRouterIODSync,
                    new AttributeAccessCode(),
                    true);
            }
        }

        #endregion
 
        #region Generic Methods

        /// <summary> React on the deactivation of address tailoring and sets the IP configuration state </summary>
        private void GenericMethodDeactivateAddressTailoring(IMethodData methodData)
        {
            if (IsPNPNIpConfigModeSupported)
            {
                IPConfiguration = IsPNPNIpSuiteViaOtherPathActive
                                                    ? NodeIPConfiguration.Other
                                                    : NodeIPConfiguration.Project;
            }
        }

        /// <summary> Creates and block DeviceNameOfStation to a byte buffer. </summary>
        /// <returns>a byte buffer containing the DeviceNameOfStation block</returns>
        private void GenericMethodGetDeviceNameOfStationBlock(IMethodData methodData)
        {
            ConfigHelperIe configHelperIe = new ConfigHelperIe(this);
            configHelperIe.GetDeviceNameOfStationBlock(methodData);
        }

        /// <summary> Creates all relevant reocrds of Networkparameter </summary>
        /// <param name="methodData"></param>
        /// <returns>a byte buffer containing the record</returns>
        private void GenericMethodGetNetworkParameters(IMethodData methodData)
        {
            ConfigHelperIe configHelperIe = new ConfigHelperIe(this);

            bool isDecentral = (methodData.Arguments[GetNetworkParameter.IsDecentral] != null)
                               && Convert.ToBoolean(methodData.Arguments[GetNetworkParameter.IsDecentral], CultureInfo.InvariantCulture);
            bool alignment = (methodData.Arguments[GetNetworkParameter.Alignment] != null) && Convert.ToBoolean(methodData.Arguments[GetNetworkParameter.Alignment], CultureInfo.InvariantCulture);
            bool isControllerSupportBv0X0100 = (methodData.Arguments[GetNetworkParameter.IsControllerSupportBv0X0100] != null) && Convert.ToBoolean(methodData.Arguments[GetNetworkParameter.IsControllerSupportBv0X0100], CultureInfo.InvariantCulture);
            int blockversion = methodData.Arguments[GetNetworkParameter.Blockversion] != null
                                   ? Convert.ToInt32(methodData.Arguments[GetNetworkParameter.Blockversion], CultureInfo.InvariantCulture)
                                   : 0x0100;

            if (methodData.Arguments[GetNetworkParameter.SelectedDataset] != null)
            {
                int index = Convert.ToInt32(methodData.Arguments[GetNetworkParameter.SelectedDataset], CultureInfo.InvariantCulture);
                methodData.ReturnValue = configHelperIe.GetNetworkParameters(
                    alignment,
                    isDecentral,
                    blockversion,
                    index,
                    false);
                return;
            }

            Dictionary<uint, byte[]> dictNetworkparam = new Dictionary<uint, byte[]>();
            //Decentral return set of records 
            byte[] nameOfStation = configHelperIe.GetNetworkParameters(
                alignment,
                isDecentral,
                blockversion,
                (int)DataRecords.Indexes.NameOfStation,
                false);
            if (nameOfStation != null)
            {
                dictNetworkparam.Add((int)DataRecords.Indexes.NameOfStation, nameOfStation);
            }

            byte[] ipV4Suite = configHelperIe.GetNetworkParameters(
                alignment,
                isDecentral,
                blockversion,
                (int)DataRecords.Indexes.IpV4Suite,
                false);
            if (ipV4Suite != null)
            {
                dictNetworkparam.Add((int)DataRecords.Indexes.IpV4Suite, ipV4Suite);
            }

            byte[] stationNameAlias = configHelperIe.GetNetworkParameters(
                alignment,
                isDecentral,
                blockversion,
                (int)DataRecords.Indexes.StationNameAlias,
                false);
            if (stationNameAlias != null)
            {
                dictNetworkparam.Add((int)DataRecords.Indexes.StationNameAlias, stationNameAlias);
            }

            byte[] ipAddressValidationRemote = configHelperIe.GetNetworkParameters(
                alignment,
                isDecentral,
                blockversion,
                (int)DataRecords.Indexes.IpAddressValidationRemote,
                isControllerSupportBv0X0100);
            if (ipAddressValidationRemote != null)
            {
                dictNetworkparam.Add((int)DataRecords.Indexes.IpAddressValidationRemote, ipAddressValidationRemote);
            }

            methodData.ReturnValue = dictNetworkparam;
        }

        private void CheckConsistency()
        {
            //----CHECK 1
            CheckProtocolPresence();

            //----CHECK 2
            CheckAddressValidation();

            Subnet subnet = Node.Subnet;

            if (subnet == null)
            {
                return;
            }
            //----CHECK 3
            //check if the node addresses are unique within its subnet, the subnet has created the ConsistencyCheckDataNodesIE object before
            ConsistencyCheckDataNodesIE nodesData = ConsistencyManager.s_NodeInfos[Node.Subnet.Id];
            if (nodesData != null)
            {
                CheckAddressWithinNet(nodesData);
            }

            //----CHECK 4
            // Check the PNNameOfStation

            Interface pnInterface = (Interface)Node.ParentObject;
            bool isPNInterfaceSubmodule = pnInterface != null;

            if (isPNInterfaceSubmodule)
            {
                nodesData = ConsistencyManager.s_NodeInfos[Node.Subnet.Id];

                if ((nodesData != null)
                    && !IsPNPNNoSViaOtherPathActive)
                {
                    m_Utility.CheckPNNameOfStation(nodesData);
                }
            }

            //----CHECK 5
            // Check the names of interfaces the subnet has created the ConsistencyCheckDataNodesIE object before
            nodesData = ConsistencyManager.s_NodeInfos[Node.Subnet.Id];
            if (nodesData != null)
            {
                CheckNameOfInterfaceWithinNet(nodesData);
            }
            if ((pnInterface != null) && pnInterface.ParentObject is DecentralDevice)
            {
                CheckIODeviceDefaultRouterSupported(pnInterface);
            }
        }

        private void CheckIODeviceDefaultRouterSupported(Interface pnInterface)
        {
            Interface ioControllerInterface = pnInterface.PNIOD.AssignedController.GetInterface();

            bool isDifferentDefaultRouterSupported = ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoIODDefaultRouterSupported,
                new AttributeAccessCode(),
                false);
            AttributeAccessCode ac = new AttributeAccessCode();
            long ioDRouterAddress =
                Node.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.NodeIPDefaultRouterAddress, ac, 0);
            long ioCRouterAddress = ioControllerInterface.Node.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.NodeIPDefaultRouterAddress,
                ac,
                0);

            if (!isDifferentDefaultRouterSupported
                && IPDefaultRouterAddressUsed
                && (ioDRouterAddress != ioCRouterAddress))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    pnInterface,
                    ConsistencyConstants.ErrorIE_IPRouteAddressDifferent,
                    AttributeUtilities.GetName(ioControllerInterface));
            }
        }

        /// <summary>Check for consistency that proofs if a protocol is available </summary>
        private void CheckProtocolPresence()
        {
            if (!NodeIeCheckProtocolPresence || IPProtocolUsed)
            {
                return;
            }
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                Node.ParentObject,
                ConsistencyConstants.NodeIeNoProtocol,
                AttributeUtilities.GetName(Node.ParentObject));
        }

        private void CheckAddressValidation()
        {
            //check if the node addresses are valid
            if (IPProtocolUsed)
            {
                m_Utility.CheckIPProtocol();
                m_NodeIeController.SecondLevelValidation();
            }
        }

        private void CheckAddressWithinNet(ConsistencyCheckDataNodesIE nodesData)
        {
            if (IPProtocolUsed)
            {
                m_Utility.CheckIPAddressWithinNet(nodesData);
            }
        }

        /// <summary> Checks the uniqueness of names of interfaces on PROFINET submodules. </summary>
        /// <param name="nodesData">The data of the consistency check for all Ethernet nodes.</param>
        private void CheckNameOfInterfaceWithinNet(ConsistencyCheckDataNodesIE nodesData)
        {
            Interface pnInterface = (Interface)Node.ParentObject;
            if ((Node == null)
                || (pnInterface == null))
            {
                return;
            }

            string nameOfInterface = AttributeUtilities.GetName(pnInterface);
            if (!nodesData.MultipleInterfaceNames.ContainsKey(nameOfInterface))
            {
                return;
            }

            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                Node.ParentObject,
                ConsistencyConstants.NodePNNoSNotUnique,
                AttributeUtilities.GetName(pnInterface));
        }

        #endregion
    }
}