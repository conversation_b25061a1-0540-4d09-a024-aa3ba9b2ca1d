/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: TailoredIoSystem.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Tailor.AddressTailoring.Options;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.IOSystem
{
    /// <summary>
    /// Summary description for TailoredIoSystem.
    /// </summary>
    public class TailoredIoSystem : ITailoringConsistency
    {
        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private DataModel.PCLObjects.IOSystem m_IOSystem;

        private DataModel.PCLObjects.Interface m_IOControllerInterface;

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        /// <summary>
        /// Constructs a TailoredIoSystem instance.
        /// </summary>
        /// <param name="ioSystem">The IoSystem for which the AddressTailoring or MachineTailoring is enabled.</param>
        /// <param name="ioControllerInterface">The Interface of the controller of the current IOSystem</param>
        public TailoredIoSystem(
            DataModel.PCLObjects.IOSystem ioSystem,
            DataModel.PCLObjects.Interface ioControllerInterface)
        {
            m_IOSystem = ioSystem;
            m_IOControllerInterface = ioControllerInterface;
        }

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class


        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        public void CheckConsistency(bool omitTopologyCheck)
        {
            List<PNIOD> ioDevicesOfIOSystem = (List<PNIOD>)m_IOSystem.GetParticipants();

            if (!omitTopologyCheck)
            {
                CheckTopologyConsistency(ioDevicesOfIOSystem);
            }

            CheckIOControllerCapability();

            CheckIDeviceControllerCentralPdevConsistency();

            CheckCoupledIDeviceBothIOSystemsTailoredConsistency();

            //Check the sync domain consistency
            SyncDomain syncDomain = m_IOControllerInterface.SyncDomain;
            CheckDomainConsistency(
                syncDomain,
                m_IOControllerInterface,
                ConsistencyConstants.DomainManagement_OtherIOSystemMemberInSyncDomain);

            //Check the mrp domain consistency
            MrpDomain mrpDomain = NavigationUtilities.GetMediaRedundancyDomainOfInterface(m_IOControllerInterface);
            CheckDomainConsistency(
                mrpDomain,
                m_IOControllerInterface,
                ConsistencyConstants.DomainManagement_OtherIOSystemMemberInMrpDomain);

            //Check that fixed peer ports do not have partners outside the IOSystem
            CheckPortConsistency(m_IOControllerInterface, ioDevicesOfIOSystem);

            //Call consistency for the controller interface
            CheckSharedDeviceConsistency(m_IOControllerInterface);
            CheckActivatedProgrammablePeerConsistency(m_IOControllerInterface);

            //Call consistency check for every ioDevice
            foreach (PNIOD ioDevice in ioDevicesOfIOSystem)
            {
                DataModel.PCLObjects.Interface interfaceSubmodule = ioDevice.GetInterface();
                CheckPdevConsistency(interfaceSubmodule);
                CheckSharedDeviceConsistency(interfaceSubmodule);
                CheckActivatedProgrammablePeerConsistency(interfaceSubmodule);
            }
        }

        public void CheckConsistency()
        {

        }

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        /// <summary>
        /// Consistency Check for 'Each IO device in the IO system is topologically reachable from its IO controller'.
        /// </summary>
        /// <param name="ioDevicesOfIOSystem"></param>
        private void CheckTopologyConsistency(List<PNIOD> ioDevicesOfIOSystem)
        {
            bool errorDisplayed = false;
            bool controllerIslandHasProgrammablePeer = true;
            object[] parameters = new object[2];
            DataModel.PCLObjects.Interface interfaceToReport = null;

            List<DataModel.PCLObjects.Interface> interfacesWithoutPort;
            List<TailoredTopologicalIsland> topologicalIslands =
                GetAllTopologicalIslands(ioDevicesOfIOSystem, out interfacesWithoutPort);

            // Devices without ports needs to be checked
            if ((interfacesWithoutPort != null)
                && (interfacesWithoutPort.Count > 0))
            {
                foreach (DataModel.PCLObjects.Interface interfaceSubmodule in interfacesWithoutPort)
                {
                    parameters = new object[2];
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(interfaceSubmodule);
                    parameters[1] = AttributeUtilities.GetSubmoduleNameWithContainer(m_IOControllerInterface);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        m_IOSystem,
                        ConsistencyConstants.PortInterconnection_TopologyReachableFromController,
                        parameters);
                }
            }

            //No need to check if there are no multiple topological islands.
            if (topologicalIslands.Count <= 1)
            {
                return;
            }

            foreach (TailoredTopologicalIsland topologicalIsland in topologicalIslands)
            {
                //If this island has no programmable peer we should display a consistency error.
                if (!topologicalIsland.HasProgrammablePeer)
                {
                    //Do NOT show the error for this island if controller is in it.
                    if (topologicalIsland.HasController)
                    {
                        controllerIslandHasProgrammablePeer = false;
                        continue;
                    }

                    // Wireless devices and some switches does not have ports.The necessary cons - check is sent by the upper code.
                    if (topologicalIsland.PortsOfIsland.First == null)
                    {
                        errorDisplayed = true;
                        break;
                    }

                    //Display the error otherwise
                    parameters = new object[2];
                    DataModel.PCLObjects.Interface interfaceSubmodule =
                        NavigationUtilities.GetInterfaceOfPort(topologicalIsland.PortsOfIsland.First.Value);
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(interfaceSubmodule);
                    parameters[1] = AttributeUtilities.GetSubmoduleNameWithContainer(m_IOControllerInterface);

                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        m_IOSystem,
                        ConsistencyConstants.PortInterconnection_TopologyReachableFromController,
                        parameters);

                    errorDisplayed = true;
                    break;
                }
                //We hold this interface for reporting if controller has no programmable peer.
                interfaceToReport = NavigationUtilities.GetInterfaceOfPort(topologicalIsland.PortsOfIsland.First.Value);
            }

            //Controller's island has no Programmable Peer. At least 1 island cannot reach Controller. We use the interface
            //that we held for reporting.
            if (!controllerIslandHasProgrammablePeer
                && !errorDisplayed)
            {
                parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(interfaceToReport);
                parameters[1] = AttributeUtilities.GetSubmoduleNameWithContainer(m_IOControllerInterface);

                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOSystem,
                    ConsistencyConstants.PortInterconnection_TopologyReachableFromController,
                    parameters);
            }
        }

        /// <summary>
        /// Consistency Check for 'All IO devices in the IO system have a PDEV and have it assigned to the IO system's IO controller'.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        private void CheckPdevConsistency(DataModel.PCLObjects.Interface interfaceSubmodule)
        {
            if (AttributeUtilities.IsIDevice(interfaceSubmodule))
            {
                if (AttributeUtilities.IsCentralPDEV(interfaceSubmodule))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        m_IOSystem,
                        ConsistencyConstants.PdevAssignedToController);
                }
            }
            else if (!GeneralUtilities.IsPDEVDevice(interfaceSubmodule))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOSystem,
                    ConsistencyConstants.PdevAssignedToController);
            }
        }

        /// <summary>
        /// Consistency Check for 'Shared devices are not allowed at multi-deployable or machine tailorable IO-Systems'
        /// </summary>
        private void CheckSharedDeviceConsistency(DataModel.PCLObjects.Interface interfaceSubmodule)
        {
            if (SharedDeviceUtility.IsSharedIoDevice(interfaceSubmodule))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    interfaceSubmodule,
                    ConsistencyConstants.MultiDeployable_NoSharedDevice,
                    new object[] { Utility.GetNameWithContainer(interfaceSubmodule) });
            }
        }

        /// <summary>
        /// Consistency Check for 'If the IO controller acts as iDevice on the same interface, it shall have the PDEV centrally assigned'.
        /// </summary>
        private void CheckIDeviceControllerCentralPdevConsistency()
        {
            if (!AttributeUtilities.IsIDevice(m_IOControllerInterface))
            {
                return;
            }
            if (!AttributeUtilities.IsDecentralPDEV(m_IOControllerInterface))
            {
                return;
            }
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                m_IOControllerInterface,
                ConsistencyConstants.IDeviceControllerCentralPdevAssigned);
        }

        private void CheckCoupledIDeviceBothIOSystemsTailoredConsistency()
        {
            PNIOOperatingModes currOperatingMode =
                (PNIOOperatingModes)m_IOControllerInterface.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    (UInt32)PNIOOperatingModes.None);

            // If controller is a Coupled IDevice, then this is a Subordinate IO-System.
            if (currOperatingMode != PNIOOperatingModes.IOControllerAndIODevice)
            {
                return;
            }
            // Get Superordinate IO-System Info, if exists.
            DataModel.PCLObjects.IOSystem superOrdinatedIOSystem =
                NavigationUtilities.GetIoSystem(m_IOControllerInterface);
            if (superOrdinatedIOSystem == null)
            {
                return;
            }

            AddressTailorOptionsCentral addressTailoringUtility =
                new AddressTailorOptionsCentral(superOrdinatedIOSystem, null);
            TailorIOSystemOptions tailoringUtility = new TailorIOSystemOptions(superOrdinatedIOSystem, null);

            // If Superordinate IO-System is Tailored, return error. It is known that this case is not 
            // applicable for now. This implementation is for future where Decentral PDEV is allowed to
            // have  an underlying IO-System.
            if (addressTailoringUtility.AddressTailoringEnabled
                || tailoringUtility.MachineTailorable)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOControllerInterface,
                    ConsistencyConstants.CoupledIDeviceOnlyOneIOSystemShouldBeTailored);
            }
        }

        private List<TailoredTopologicalIsland> GetAllTopologicalIslands(List<PNIOD> ioDevicesOfIOSystem, out List<DataModel.PCLObjects.Interface> interfacesWithoutPort)
        {
            List<DataModel.PCLObjects.Interface> visitedIoDevices = new List<DataModel.PCLObjects.Interface>();
            List<TailoredTopologicalIsland> topologicalIslands = new List<TailoredTopologicalIsland>();
            interfacesWithoutPort = new List<DataModel.PCLObjects.Interface>();
            bool interfaceWithoutPort;

            foreach (PNIOD ioDevice in ioDevicesOfIOSystem)
            {
                DataModel.PCLObjects.Interface itfSubmodule = ioDevice.GetInterface();
                if (!visitedIoDevices.Contains(itfSubmodule))
                {
                    topologicalIslands.Add(
                        GetTopologicalIsland(itfSubmodule, visitedIoDevices, out interfaceWithoutPort));

                    if (interfaceWithoutPort)
                    {
                        interfacesWithoutPort.Add(itfSubmodule);
                    }
                }
            }
            //Add Controller if it is not already added.
            if (!visitedIoDevices.Contains(m_IOControllerInterface))
            {
                topologicalIslands.Add(
                    GetTopologicalIsland(m_IOControllerInterface, visitedIoDevices, out interfaceWithoutPort));

                if (interfaceWithoutPort)
                {
                    interfacesWithoutPort.Add(m_IOControllerInterface);
                }
            }
            return topologicalIslands;
        }

        private TailoredTopologicalIsland GetTopologicalIsland(
            DataModel.PCLObjects.Interface interfaceSubmoduleBase,
            List<DataModel.PCLObjects.Interface> visitedIoDevices,
            out bool interfaceWithoutPort)
        {
            TailoredTopologicalIsland topologicalIsland = new TailoredTopologicalIsland();
            interfaceWithoutPort = false;

            if (interfaceSubmoduleBase != null)
            {
                //get the Ports
                List<DataModel.PCLObjects.Port> originalPorts =
                    NavigationUtilities.GetPortModules(interfaceSubmoduleBase);
                List<DataModel.PCLObjects.Port> copiedPorts = new List<DataModel.PCLObjects.Port>();
                if (originalPorts != null)
                {
                    copiedPorts = originalPorts.ToList();
                    if (copiedPorts.Count == 0)
                    {
                        interfaceWithoutPort = true;
                    }

                    foreach (DataModel.PCLObjects.Port port in copiedPorts)
                    {
                        //Check if the current port is programmable peer.
                        bool hasProgrammablePeer = Utilities.MachineTailor.MachineTailorUtility
                            .IsProgrammablePeerEnabled(port);
                        if (hasProgrammablePeer)
                        {
                            topologicalIsland.HasProgrammablePeer = true;
                        }
                        topologicalIsland.AddPort(port);
                    }
                }

                //Check if current interface is this IO-System's Controller.
                if (interfaceSubmoduleBase == m_IOControllerInterface)
                {
                    topologicalIsland.HasController = true;
                }
                //Add the currently investigated IODevice, we don't want to come back to that either.
                visitedIoDevices.Add(interfaceSubmoduleBase);

                LinkedListNode<DataModel.PCLObjects.Port> currentPort = topologicalIsland.PortsOfIsland.First;
                //Iterate through the ports of the IoDevice
                while (currentPort != null)
                {
                    IList<DataModel.PCLObjects.Port> partnerPorts = currentPort.Value.GetPartnerPorts();
                    if ((partnerPorts == null) || (partnerPorts.Count == 0))
                    {
                        currentPort = currentPort.Next;
                        continue;
                    }

                    //Iterate through each partner port of a port of the IoDevice
                    foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                    {
                        DataModel.PCLObjects.Interface partnerPortInterface =
                            NavigationUtilities.GetInterfaceOfPort(partnerPort);
                        //Add all the ports of the IODevice of the partner port so that we can step further.
                        if (!visitedIoDevices.Contains(partnerPortInterface))
                        {
                            visitedIoDevices.Add(partnerPortInterface);
                        }

                        //Check if current interface is Controller.
                        if (partnerPortInterface == m_IOControllerInterface)
                        {
                            topologicalIsland.HasController = true;
                        }

                        List<DataModel.PCLObjects.Port> currentIODevicePorts =
                            NavigationUtilities.GetPortModules(partnerPortInterface);
                        foreach (DataModel.PCLObjects.Port currentIODevicePort in currentIODevicePorts)
                        {
                            bool hasProgrammablePeer = Utilities.MachineTailor.MachineTailorUtility
                                .IsProgrammablePeerEnabled(currentIODevicePort);
                            if (hasProgrammablePeer)
                            {
                                topologicalIsland.HasProgrammablePeer = true;
                            }

                            if (!copiedPorts.Contains(currentIODevicePort))
                            {
                                copiedPorts.Add(currentIODevicePort);
                                topologicalIsland.AddPort(currentIODevicePort);
                            }
                        }
                    }
                    currentPort = currentPort.Next;
                }
            }
            return topologicalIsland;
        }

        /// <summary>
        /// Consistency Check for 'If an IO system is machine tailorable, PNIoMachineTailoring of its IOC shall be set to "true".'
        /// </summary>
        private void CheckIOControllerCapability()
        {
            object[] parameters = new object[1];
            parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(m_IOControllerInterface);
            if (Utilities.MachineTailor.MachineTailorUtility.IsIoSystemMachineTailorable(m_IOControllerInterface)
                && !Utilities.MachineTailor.MachineTailorUtility
                    .IsMachineTailoringSupportedIoControllerInterfaceStartObject(m_IOControllerInterface))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOSystem,
                    ConsistencyConstants.IOSystem_MachineTailoringEnabled_IOC,
                    parameters);
            }
        }

        /// <summary>
        /// consistency check for 'If IO devices within the IO system are assigned to a sync domain, the sync domain
        /// shall contain IO devices of this IO system only' and 'If IO devices within the IO system are assigned to
        /// a media redundancy domain, the media redundancy domain shall contain IO devices of this IO system only'.
        /// </summary>
        /// <param name="domain"></param>
        /// <param name="ioControllerInterface"></param>
        /// <param name="errorMsg"></param>
        private void CheckDomainConsistency(
            Domain domain,
            DataModel.PCLObjects.Interface ioControllerInterface,
            string errorMsg)
        {
            if (domain != null)
            {
                string ioSystemControllerName =
                    AttributeUtilities.GetName(NavigationUtilities.GetOwnerOfInterface(ioControllerInterface));
                List<DataModel.PCLObjects.Interface> domainInterfaces = domain.Participants;

                foreach (DataModel.PCLObjects.Interface domainInterface in domainInterfaces)
                {
                    if ((domainInterface == ioControllerInterface)
                        || !AttributeUtilities.IsActiveProfinetInterfaceSubmodule(domainInterface))
                    {
                        continue;
                    }
                    if (Utility.IsProfinetControllerInterfaceSubmodule(domainInterface))
                    {
                        //Handle a controller interface
                        string ioSystemParticipantControllerName = null;

                        //Handle IDevice, as it behaves differently
                        if (AttributeUtilities.IsIDevice(domainInterface))
                        {
                            //get controller, of this this IDevice
                            DataModel.PCLObjects.Interface controllerOfIDevice =
                                NavigationUtilities.GetControllerOfDevice(domainInterface);
                            if (controllerOfIDevice != null)
                            {
                                ioSystemParticipantControllerName =
                                    AttributeUtilities.GetName(
                                        NavigationUtilities.GetOwnerOfInterface(controllerOfIDevice));
                            }
                        }
                        else
                        {
                            ioSystemParticipantControllerName =
                                AttributeUtilities.GetName(NavigationUtilities.GetOwnerOfInterface(domainInterface));
                        }

                        //If the(controller) domain participant part of another IO System, we have an error.
                        if (ioSystemControllerName != ioSystemParticipantControllerName)
                        {
                            PNIOC ioControllerObjectOfInterface = domainInterface.PNIOC;

                            if (ioControllerObjectOfInterface != null)
                            {
                                if (NavigationUtilities.GetIoDevicesOfIoController(ioControllerObjectOfInterface)
                                    != null)
                                {
                                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, domain, errorMsg);
                                }
                            }
                            return;
                        }
                    }
                    else
                    {
                        //Handle a device interface
                        PclObject ioSystemOfDomainParticipant = NavigationUtilities.GetIoSystem(domainInterface);
                        //The device is not part of an IOSystem, error
                        if (ioSystemOfDomainParticipant == null)
                        {
                            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_IOSystem, errorMsg);
                            return;
                        }
                        DataModel.PCLObjects.Interface controllerOfDomainParticipantInterface =
                            NavigationUtilities.GetControllerInterfaceOfDeviceInterface(domainInterface);
                        string ioSystemParticipantControllerName = AttributeUtilities.GetName(
                            NavigationUtilities.GetOwnerOfInterface(controllerOfDomainParticipantInterface));
                        //The device is part of another IOSystem, error
                        if (ioSystemControllerName != ioSystemParticipantControllerName)
                        {
                            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_IOSystem, errorMsg);
                            return;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Consistency Check for 'Port connections with fixed peers are restricted to have partner in the same IO system'.
        /// </summary>
        /// <param name="ioControllerInterface"></param>
        /// <param name="ioDevicesOfIOSystem"></param>
        private void CheckPortConsistency(DataModel.PCLObjects.Interface ioControllerInterface,
                                          List<PNIOD> ioDevicesOfIOSystem)
        {
            string ioSystemControllerName =
                AttributeUtilities.GetName(NavigationUtilities.GetOwnerOfInterface(ioControllerInterface));
            //check the interfaces of the devices
            foreach (PNIOD ioDevice in ioDevicesOfIOSystem)
            {
                CheckPortsOfInterface(ioDevice.GetInterface(), ioSystemControllerName);
            }

            //Check the ports of the current IOSystem controller as well.
            CheckPortsOfInterface(ioControllerInterface, ioSystemControllerName);
        }

        /// <summary>
        /// Checks weather a port set to Programmable peer is activated.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        private void CheckActivatedProgrammablePeerConsistency(DataModel.PCLObjects.Interface interfaceSubmodule)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(interfaceSubmodule);
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                if (Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(port)
                    && IsPortDeactivated(port))
                {
                    string portName = AttributeUtilities.GetName(port);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        port,
                        ConsistencyConstants.ProgrammablePeerPortDeactivated,
                        portName);
                }
            }
        }

        private void CheckPortsOfInterface(
            DataModel.PCLObjects.Interface interfaceSubmodule,
            string ioSystemControllerName)
        {
            if (interfaceSubmodule == null)
            {
                return;
            }

            bool partnerInSameIOSystem = true;
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(interfaceSubmodule);
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                // If Programmable Peer is enabled for this port, no need to check port links.
                if (Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(port))
                {
                    continue;
                }
                //check if it has partners, and their IOSystem
                foreach (DataModel.PCLObjects.Port partnerPort in port.GetPartnerPorts())
                {
                    //get the interface of partner port
                    DataModel.PCLObjects.Interface partnerPortInterface =
                        NavigationUtilities.GetInterfaceOfPort(partnerPort);
                    // If we have a coupled IDevice as partner, then we should check both IO-Systems of it, if they exist.
                    if (AttributeUtilities.IsIDevice(partnerPortInterface))
                    {
                        if (CheckIDeviceInterfaceInSameIOSystem(ioSystemControllerName, partnerPortInterface))
                        {
                            continue;
                        }
                        partnerInSameIOSystem = false;
                        break;
                    }

                    // If the interface whose ports are to be checked is an IDevice then 
                    // the partner should be checked in both IO-Systems of it, if they exist.
                    if (AttributeUtilities.IsIDevice(interfaceSubmodule))
                    {
                        string controllerNameChecker = string.Empty;
                        // We should be taking partner's controller info into consideration
                        if (Utility.IsProfinetDeviceInterfaceSubmodule(partnerPortInterface))
                        {
                            DataModel.PCLObjects.Interface controllerInterfaceOfPartnerPort =
                                NavigationUtilities.GetControllerInterfaceOfDeviceInterface(partnerPortInterface);
                            if (controllerInterfaceOfPartnerPort != null)
                            {
                                controllerNameChecker =
                                    AttributeUtilities.GetName(
                                        NavigationUtilities.GetOwnerOfInterface(controllerInterfaceOfPartnerPort));
                            }
                        }
                        else
                        {
                            controllerNameChecker = AttributeUtilities.GetName(partnerPortInterface);
                        }

                        // Check whether IDevice's partner port remains in one of the IDevice's IO-Systems
                        if (!string.IsNullOrEmpty(controllerNameChecker)
                            && CheckIDeviceInterfaceInSameIOSystem(controllerNameChecker, interfaceSubmodule))
                        {
                            continue;
                        }
                        partnerInSameIOSystem = false;
                        break;
                    }

                    //if we have an iodevice we should take that, because the if there is an iocontroller too, than it we be filtered out by other checks  
                    PclObject ioSystemOfPartnerPort = NavigationUtilities.GetIoSystem(partnerPortInterface);

                    //We have a partner that is not even a part of an IOSystem, we don't like that!
                    if (CheckControllerNamesMatch(ioSystemControllerName, ioSystemOfPartnerPort))
                    {
                        continue;
                    }
                    partnerInSameIOSystem = false;
                    break;
                }
                if (partnerInSameIOSystem)
                {
                    continue;
                }
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOSystem,
                    ConsistencyConstants.PortInterconnection_PartnerPortFromSameIoSystem);

                return;
            }
        }

        /// <summary>
        /// Check whether the IDevice is in the same IO-System. A seperate method is needed for IDevice as there can be several Operating Modes.
        /// </summary>
        /// <param name="ioSystemControllerName"></param>
        /// <param name="partnerPortInterfaceConfigObject"></param>
        /// <returns>True if IDevice is in the same IO-System, False otherwise</returns>
        private bool CheckIDeviceInterfaceInSameIOSystem(
            string ioSystemControllerName,
            DataModel.PCLObjects.Interface partnerPortInterfaceConfigObject)
        {
            PNIOOperatingModes currOperatingMode =
                (PNIOOperatingModes)
                partnerPortInterfaceConfigObject.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnIoOperatingMode, new AttributeAccessCode(),
                    (UInt32)PNIOOperatingModes.None);
            switch (currOperatingMode)
            {
                case PNIOOperatingModes.IOControllerAndIODevice:
                    {
                        // Get IO-Systems of current Interface for IO-Device and IO-Controller modes
                        PclObject ioSystemOfIDeviceIODevice =
                            NavigationUtilities.GetIoSystem(
                                partnerPortInterfaceConfigObject);
                        PclObject ioSystemOfIDeviceIOController =
                            NavigationUtilities.GetIoSystem(
                                partnerPortInterfaceConfigObject);
                        if ((ioSystemOfIDeviceIOController == null) && (ioSystemOfIDeviceIODevice == null))
                        {
                            return false;
                        }

                        // Check whether the current Interface's IO-Controller matches tailored IO-System's controller.
                        return CheckControllerNamesMatch(ioSystemControllerName, ioSystemOfIDeviceIOController)
                               || CheckControllerNamesMatch(ioSystemControllerName, ioSystemOfIDeviceIODevice);
                    }
                case PNIOOperatingModes.IOController:
                    {
                        // Get IO-System of current Interface for IO-Controller mode
                        PclObject ioSystemOfIDeviceIOController =
                            NavigationUtilities.GetIoSystem(
                                partnerPortInterfaceConfigObject);

                        // Check whether the current Interface's IO-Controller matches tailored IO-System's controller.
                        return CheckControllerNamesMatch(ioSystemControllerName, ioSystemOfIDeviceIOController);
                    }
                case PNIOOperatingModes.None:
                case PNIOOperatingModes.IODevice:
                    {
                        // Get IO-System of current Interface for IO-Device mode
                        PclObject ioSystemOfIDeviceIODevice =
                            NavigationUtilities.GetIoSystem(
                                partnerPortInterfaceConfigObject);

                        // Check whether the current Interface's IO-Controller matches tailored IO-System's controller.
                        return CheckControllerNamesMatch(ioSystemControllerName, ioSystemOfIDeviceIODevice);
                    }
            }
            return true;
        }

        /// <summary>
        /// Check whether the given Interface's IO-Controller matches tailored IO-System's controller.
        /// </summary>
        /// <param name="ioSystemControllerName"></param>
        /// <param name="ioSystemOfIInterface"></param>
        /// <returns>True if matches, False otherwise</returns>
        private bool CheckControllerNamesMatch(string ioSystemControllerName, PclObject ioSystemOfIInterface)
        {
            if (ioSystemOfIInterface == null)
            {
                return false;
            }
            if (!(ioSystemOfIInterface is DataModel.PCLObjects.IOSystem))
            {
                return false;
            }

            DataModel.PCLObjects.Interface ioControllerForPartnerPortIoSystem =
                PNNavigationUtility.GetControllerOfIOSystem(ioSystemOfIInterface as DataModel.PCLObjects.IOSystem);
            if (ioControllerForPartnerPortIoSystem == null)
            {
                return false;
            }

            return ioSystemControllerName
                   == AttributeUtilities.GetName(
                       NavigationUtilities.GetOwnerOfInterface(ioControllerForPartnerPortIoSystem));
        }

        private bool IsPortDeactivated(PclObject port)
        {
            AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
            return attributeAccessCode.IsOkay && port.AttributeAccess.GetAnyAttribute<bool>(
                       InternalAttributeNames.PnPortDeactivated,
                       attributeAccessCode,
                       false);
        }

        #endregion

    }
}