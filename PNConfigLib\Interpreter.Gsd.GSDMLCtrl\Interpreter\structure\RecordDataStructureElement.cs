/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: RecordDataStructureElement.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using C = PNConfigLib.Gsd.Interpreter.Common;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Structure
{
    /// <summary>
    /// Summary description for RecordDataStructureElement.
    /// </summary>
    //[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C8A")] 
    public class RecordDataStructureElement :
		StructureElement,
		GSDI.IRecordDataStructureElement // Interface
    {

		#region Initialization & Termination

		/// <summary>
		/// Creates an instance of this class. 
		/// </summary>
		public RecordDataStructureElement()
		{
            m_RecordDataID = String.Empty;
		}
		#endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_RecordDataID;

        #endregion

        #region Properties

        /// <summary>
        /// Returns the submodule type ("Submodule", "Port").
        /// </summary>
        public string RecordDataID => this.m_RecordDataID;

        #region COM Interface Members Only


		#endregion

		#endregion


		#region GsdObject Members


        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldRecordDataId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_RecordDataID = hash[member] as string;

                //// Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }


		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
			writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectRecordDataStructureElement);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        override internal bool SerializeMembers(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldRecordDataId, this.m_RecordDataID);

            return true;
        }



        #endregion

    }
}
