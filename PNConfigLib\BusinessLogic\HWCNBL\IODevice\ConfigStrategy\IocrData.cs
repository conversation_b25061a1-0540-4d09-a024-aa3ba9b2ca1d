/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IocrData.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    /// <summary>
    /// IOCRData block related part of the PNDeviceConfigStratedy class.
    /// </summary>
    internal partial class PNDeviceConfigStrategy
    {
        //########################################################################################

        #region Public Methods
        /// <summary>
        /// This method initializes IOCR structs & Frame Offset values for each IOCR Entry pair.
        /// </summary>
        /// <param name="inputCrEntry">Input CR entry data block.</param>
        /// <param name="outputCrEntry">Output CR entry data block.</param>
        /// <param name="withApi">Whether API struct should be initialized.</param>
        public void InitializeIOCR(IList<byte[]> inputCrEntry, IList<byte[]> outputCrEntry, List<IPNFrameData> inputPNFrameDatas, List<IPNFrameData> outputPNFrameDatas, bool withApi)
        {
            for (int i = 0; i < inputPNFrameDatas.Count; i++)
            {
                //initialize IOCR structs & Frame Offset values for each IOCR Entry pair.
                IocrStruct inputIocr = new IocrStruct();
                IocrStruct outputIocr = new IocrStruct();
                OffsetIocr = 0;
                OffsetIOData = 0;

                IMethodData md = new MethodData();
                md.Name = GetIocrEntry.Name;

                md.Arguments.Add(GetIocrEntry.PNFrameData, inputPNFrameDatas[i]);
                md.Arguments.Add(GetIocrEntry.PNFrameData2, outputPNFrameDatas[i]);
                this.Interface.BaseActions.CallMethod(md);

                if (!(bool)md.ReturnValue)
                {
                    throw new PNFunctionsException("Could not get attribute values from PNFrameData object.");
                }

                bool isApduController = IsApduController();

                // append PNPlanner data to IOCR struct
                inputIocr.SetIOCREntry(md.Arguments[GetIocrEntry.IOCREntry] as byte[]);
                outputIocr.SetIOCREntry(md.Arguments[GetIocrEntry.IOCREntry2] as byte[]);

                inputIocr.Version = outputIocr.Version = 0x0100;
                inputIocr.IOCRReference = (int)CrReference.Input;
                outputIocr.IOCRReference = (int)CrReference.Output;

                inputIocr.Ethertype = outputIocr.Ethertype = 0x8892;
                //IOCR.FrameSendOffset = 0xFFFFFFFF;
                inputIocr.SetIOCRUserPriority(6);
                outputIocr.SetIOCRUserPriority(6);

                byte[] inputRelatedObjects;
                byte[] outputRelatedObjects;

                // initialise API struct
                if (withApi)
                {
                    IocrSubblockStruct inputApi = new IocrSubblockStruct();
                    IocrSubblockStruct outputApi = new IocrSubblockStruct();
                    inputApi.APICount = outputApi.APICount = Apis.Count;

                    // create related IO objects
                    foreach (uint api in Apis)
                    {
                        CreateRelatedIOEntries(
                            inputPNFrameDatas[i],
                            outputPNFrameDatas[i],
                            out inputRelatedObjects,
                            out outputRelatedObjects,
                            api,
                            isApduController);

                        // append to API struct
                        inputApi.AddAPI(inputRelatedObjects);
                        outputApi.AddAPI(outputRelatedObjects);
                    }

                    // append data to IOCR struct
                    inputIocr.AddSubblock(inputApi.ToByteArray);
                    outputIocr.AddSubblock(outputApi.ToByteArray);
                }
                else
                {
                    CreateRelatedIOEntriesNoApi(
                        inputPNFrameDatas[i],
                        outputPNFrameDatas[i],
                        out inputRelatedObjects,
                        out outputRelatedObjects,
                        isApduController);

                    inputIocr.AddSubblock(inputRelatedObjects);
                    outputIocr.AddSubblock(outputRelatedObjects);
                }
                if (inputCrEntry != null)
                {
                    inputCrEntry.Add(inputIocr.ToByteArray);
                }
                if (outputCrEntry != null)
                {
                    outputCrEntry.Add(outputIocr.ToByteArray);
                }
            }
        }
        /// <summary>
        /// This method returns the IOCREntry part for an input and output frame.
        /// Returns null if the device interface submodule does not have any PNPlannerDataObjects.
        /// </summary>
        /// <param name="inputCrEntry">Input CR entry data block.</param>
        /// <param name="outputCrEntry">Output CR entry data block.</param>
        /// <param name="withApi">Whether API struct should be initialized.</param>
        public void GetCREntry(IList<byte[]> inputCrEntry, IList<byte[]> outputCrEntry, bool withApi)
        {
            // get IOCR bock from SLC object
            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(IODevice, false);

            if ((null == pnFrameDataList)
                || (pnFrameDataList.Count != 2))
            {
                throw new PNFunctionsException(
                    "Could not find any PNFrameData objects aggregated on the device interface submodule.");
            }

            List<IPNFrameData> inputPNFrameDatas = new List<IPNFrameData>();
            List<IPNFrameData> outputPNFrameDatas = new List<IPNFrameData>();
            foreach (IPNFrameData frame in pnFrameDataList)
            {
                if (frame.FrameDirection == (long)PNPlannerFrameDirection.InputFrame)
                {
                    inputPNFrameDatas.Add(frame);
                }
                else if (frame.FrameDirection == (long)PNPlannerFrameDirection.OutputFrame)
                {
                    outputPNFrameDatas.Add(frame);
                }
            }
            if ((inputPNFrameDatas == null)
                || (outputPNFrameDatas == null))
            {
                throw new PNFunctionsException(
                    "Could not find the PNFrameData object with input or output frame direction.");
            }
            InitializeIOCR(inputCrEntry, outputCrEntry, inputPNFrameDatas, outputPNFrameDatas, withApi);
        }

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Initializes the IO entries.
        /// </summary>
        private void InitializeIOEntries()
        {
            OffsetIocr = 0;
            OffsetIOData = 0;
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        #endregion

        //########################################################################################

        #region Constants and Enums

        /// <summary>
        /// Values for IOCR reference.
        /// </summary>
        private enum CrReference
        {
            Input = 1,

            Output = 2
        }

        /// <summary>
        /// IOPS length constant.
        /// </summary>
        public const int s_LengthIops = 1;

        #endregion

        //########################################################################################

        #region Fields

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// IOCR offset.
        /// </summary>
        private int OffsetIocr { get; set; }

        /// <summary>
        /// IOData offset.
        /// </summary>
        private int OffsetIOData { get; set; }

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion

        //########################################################################################

        #region Private Implementation

        /// <summary>
        /// Creates related IO entries for given input and output frames.
        /// </summary>
        /// <param name="inputPNFrameData">Input frame.</param>
        /// <param name="outputPNFrameData">Output frame.</param>
        /// <param name="inputRelatedObjects">Input IO entry.</param>
        /// <param name="outputRelatedObjects">Output IO entry.</param>
        /// <param name="api">API number.</param>
        /// <param name="isApdu">Whether controller is APDU.</param>
        protected virtual void CreateRelatedIOEntries(
            IPNFrameData inputPNFrameData,
            IPNFrameData outputPNFrameData,
            out byte[] inputRelatedObjects,
            out byte[] outputRelatedObjects,
            uint api,
            bool isApdu)
        {
            IocrApiSubblockStruct inputIocrApi = new IocrApiSubblockStruct();
            IocrApiSubblockStruct outputIocrApi = new IocrApiSubblockStruct();
            inputIocrApi.API = outputIocrApi.API = api;

            //get slots of module
            foreach (PclObject module in Modules)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                int slotNumber = GetSlotNumberForRelatedIOEntries(module);

                // check whether the module has Input, Output or Diagnostic address
                bool hasInOrOutOrDiagAddr = ConfigUtility.HasModuleInOutOrDiagAddress(module);

                // if a physical module has Input, Output or Diagnostic address(es) it must have IO entry(ies)
                if (hasInOrOutOrDiagAddr)
                {
                    const int SubslotNumber = 1;
                    if (module.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnAPI, ac.GetNew(), 0)
                        != api)
                    {
                        continue;
                    }

                    CreateRelatedEntries(
                        module,
                        slotNumber,
                        SubslotNumber,
                        inputPNFrameData,
                        outputPNFrameData,
                        inputIocrApi,
                        outputIocrApi,
                        isApdu);
                }

                //get available submodules
                IList<PclObject> submodules = GetSubmodulesInternal(module);

                //if no submodule available then get data from module, but only if module has not got address
                if ((submodules.Count == 0)
                    && !hasInOrOutOrDiagAddr)
                {
                    const int SubslotNumber = 1;
                    if (module.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnAPI, ac.GetNew(), 0)
                        != api)
                    {
                        continue;
                    }

                    CreateRelatedEntries(
                        module,
                        slotNumber,
                        SubslotNumber,
                        inputPNFrameData,
                        outputPNFrameData,
                        inputIocrApi,
                        outputIocrApi,
                        isApdu);
                }
                else
                {
                    //else each submodule provides data
                    foreach (PclObject submodule in submodules)
                    {
                        int subslotNumber = GetSubmodulesPNSubslotNumber(submodule);
                        if (submodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnAPI,
                            ac.GetNew(),
                            0) != api)
                        {
                            continue;
                        }

                        CreateRelatedEntries(
                            submodule,
                            slotNumber,
                            subslotNumber,
                            inputPNFrameData,
                            outputPNFrameData,
                            inputIocrApi,
                            outputIocrApi,
                            isApdu);
                    }
                }
            }

            inputRelatedObjects = inputIocrApi.ToByteArray;
            outputRelatedObjects = outputIocrApi.ToByteArray;
        }

        /// <summary>
        /// Gets the slot number of a module.
        /// </summary>
        /// <param name="module">Module whose slot number will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if module is null.</exception>
        /// <returns>Slot number of the module.</returns>
        protected virtual int GetSlotNumberForRelatedIOEntries(PclObject module)
        {
            int slotNumber = Utility.GetSlotNumber(module);
            return slotNumber;
        }

        /// <summary>
        /// Creates related IO entries for given input and output frames, without api.
        /// </summary>
        /// <param name="inputPNFrameData">Input frame.</param>
        /// <param name="outputPNFrameData">Output frame.</param>
        /// <param name="inputRelatedObjects">Input IO entry.</param>
        /// <param name="outputRelatedObjects">Output IO entry.</param>
        /// <param name="isApdu">Whether controller is APDU.</param>
        protected virtual void CreateRelatedIOEntriesNoApi(
            IPNFrameData inputPNFrameData,
            IPNFrameData outputPNFrameData,
            out byte[] inputRelatedObjects,
            out byte[] outputRelatedObjects,
            bool isApdu)
        {
            IocrRelatedSubblockStructV10 inputIocrApi = new IocrRelatedSubblockStructV10();
            IocrRelatedSubblockStructV10 outputIocrApi = new IocrRelatedSubblockStructV10();

            //get slots of module
            foreach (PclObject moduleVar in Modules)
            {
                Module module = (Module)moduleVar;
                AttributeAccessCode ac = new AttributeAccessCode();
                int slotNumber = module.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PositionNumber, ac, 0);

                // check whether the module has Input, Output or Diagnostic address
                bool hasInOrOutOrDiagAddr = ConfigUtility.HasModuleInOutOrDiagAddress(module);

                // if a physical module has Input, Output or Diagnostic address(es) it must have IO entry(ies)
                if (hasInOrOutOrDiagAddr)
                {
                    const int SubslotNumber = 1;
                    CreateRelatedEntriesNoApi(
                        module,
                        slotNumber,
                        SubslotNumber,
                        inputPNFrameData,
                        outputPNFrameData,
                        inputIocrApi,
                        outputIocrApi,
                        isApdu);
                }

                //get available submodules
                IList<PclObject> submodules = GetSubmodulesInternal(module);

                //if no submodule available then get data from module, but only if module has not got address
                if ((submodules.Count == 0)
                    && !hasInOrOutOrDiagAddr)
                {
                    const int SubslotNumber = 1;
                    CreateRelatedEntriesNoApi(
                        module,
                        slotNumber,
                        SubslotNumber,
                        inputPNFrameData,
                        outputPNFrameData,
                        inputIocrApi,
                        outputIocrApi,
                        isApdu);
                }
                else
                {
                    //else each submodule provides data
                    foreach (PclObject submoduleVar in submodules)
                    {
                        Submodule submodule = (Submodule)submoduleVar;
                        int subslotNumber =
                            submodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnSubslotNumber,
                                ac.GetNew(),
                                1);
                        CreateRelatedEntriesNoApi(
                            submodule,
                            slotNumber,
                            subslotNumber,
                            inputPNFrameData,
                            outputPNFrameData,
                            inputIocrApi,
                            outputIocrApi,
                            isApdu);
                    }
                }
            }

            inputRelatedObjects = inputIocrApi.ToByteArray;
            outputRelatedObjects = outputIocrApi.ToByteArray;
        }

        /// <summary>
        /// Creates related entries for input and output frames.
        /// </summary>
        /// <param name="module">The module whose entries are created.</param>
        /// <param name="slotNumber">Slot number of the module.</param>
        /// <param name="subslotNumber">Subslot number.</param>
        /// <param name="inputPNFrameData">Input frame.</param>
        /// <param name="outputPNFrameData">Output frame.</param>
        /// <param name="inputIocrApi">IOCRAPISubblockStruct for input.</param>
        /// <param name="outputIocrApi">IOCRAPISubblockStruct for output.</param>
        /// <param name="isApduController">Whether controller is APDU.</param>
        public virtual void CreateRelatedEntries(
            PclObject module,
            int slotNumber,
            int subslotNumber,
            IPNFrameData inputPNFrameData,
            IPNFrameData outputPNFrameData,
            IocrApiSubblockStruct inputIocrApi,
            IocrApiSubblockStruct outputIocrApi,
            bool isApduController)
        {
            SortOrder sortOrder = isApduController ? SortOrder.APDU : SortOrder.IO;
            List<DataAddress> addressObjects;

            addressObjects = ConfigUtility.GetAddressObjectsSorted(module, sortOrder);
            IoTypes ioType;
            int iopsLength;
            foreach (DataAddress addressObj in addressObjects)
            {
                ioType = addressObj.IoType;
                int length = addressObj.Length;
                iopsLength = Utility.GetIOXSLength(this.Interface, ioType, s_LengthIops);

                #region (pnPlannerFrameDirection == PNPlannerFrameDirection.InputFrame)

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis)
                    || (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(
                        slotNumber,
                        subslotNumber,
                        length + iopsLength,
                        inputIocrApi,
                        ioType,
                        inputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    // Wenn InputFrame und AddressObject mit IOtype --> Output ==> length = 0
                    CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, inputIocrApi, ioType, inputPNFrameData);
                }
                else
                {
                    Debug.Assert(false, "CreateRelatedIOEntry: Invalid IoType: " + ioType);
                }

                #endregion

                #region (pnPlannerFrameDirection == PNPlannerFrameDirection.OutputFrame)

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis)
                    || (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(
                        slotNumber,
                        subslotNumber,
                        iopsLength,
                        outputIocrApi,
                        ioType,
                        outputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    CreateRelatedIOEntry(
                        slotNumber,
                        subslotNumber,
                        length + iopsLength,
                        outputIocrApi,
                        ioType,
                        outputPNFrameData);
                }
                else
                {
                    Debug.Assert(false, "CreateRelatedIOEntry: Invalid IoType: " + ioType);
                }

                #endregion
            }

            if (addressObjects.Count != 0)
            {
                return;
            }

            ioType = (IoTypes)module.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.IoType,
                new AttributeAccessCode(),
                0);
            iopsLength = Utility.GetIOXSLength(this.Interface, ioType, s_LengthIops);
            CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, inputIocrApi, IoTypes.Input, inputPNFrameData);
            CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, outputIocrApi, IoTypes.Input, outputPNFrameData);
        }

        /// <summary>
        /// Creates related entries for input and output frames without api.
        /// </summary>
        /// <param name="module">The module whose entries are created.</param>
        /// <param name="slotNumber">Slot number of the module.</param>
        /// <param name="subslotNumber">Subslot number.</param>
        /// <param name="inputPNFrameData">Input frame.</param>
        /// <param name="outputPNFrameData">Output frame.</param>
        /// <param name="inputIocrApi">IOCRAPISubblockStruct for input.</param>
        /// <param name="outputIocrApi">IOCRAPISubblockStruct for output.</param>
        /// <param name="isApduController">Whether controller is APDU.</param>
        public virtual void CreateRelatedEntriesNoApi(
            PclObject module,
            int slotNumber,
            int subslotNumber,
            IPNFrameData inputPNFrameData,
            IPNFrameData outputPNFrameData,
            IocrRelatedSubblockStructV10 inputIocrApi,
            IocrRelatedSubblockStructV10 outputIocrApi,
            bool isApduController)
        {
            SortOrder sortOrder = isApduController ? SortOrder.APDU : SortOrder.IO;
            List<DataAddress> addressObjects;

            addressObjects = ConfigUtility.GetAddressObjectsSorted(module, sortOrder);
            foreach (DataAddress addressObj in addressObjects)
            {
                IoTypes ioType = addressObj.IoType;

                int length = addressObj.Length;

                #region IOCR_Type = 1 InputFrame

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis)
                    || (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input)
                    || (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output))
                {
                    int lengthC = ((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output
                                       ? s_LengthIops
                                       : length + s_LengthIops;
                    CreateRelatedIOEntry(slotNumber, subslotNumber, lengthC, inputIocrApi, ioType, inputPNFrameData);
                }
                else
                {
                    Debug.Assert(false, "CreateRelatedIOEntry: Invalid IoType: " + ioType);
                }

                #endregion

                #region IOCR_Type = 2 OutputFrame

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis)
                    || (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input)
                    || (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output))
                {
                    int lengthC = ((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output
                                       ? s_LengthIops + length
                                       : s_LengthIops;
                    CreateRelatedIOEntry(slotNumber, subslotNumber, lengthC, outputIocrApi, ioType, outputPNFrameData);
                }
                else
                {
                    Debug.Assert(false, "CreateRelatedIOEntry: Invalid IoType: " + ioType);
                }

                #endregion
            }

            if (addressObjects.Count == 0)
            {
                CreateRelatedIOEntry(
                    slotNumber,
                    subslotNumber,
                    s_LengthIops,
                    inputIocrApi,
                    IoTypes.Input,
                    inputPNFrameData);
                CreateRelatedIOEntry(
                    slotNumber,
                    subslotNumber,
                    s_LengthIops,
                    outputIocrApi,
                    IoTypes.Input,
                    outputPNFrameData);
            }
        }

        /// <summary>
        /// Creates related IO entries.
        /// </summary>
        /// <param name="slotNumber">Slot number.</param>
        /// <param name="subslotNumber">Subslot number.</param>
        /// <param name="length">Length.</param>
        /// <param name="api">IOCR related subblock struct.</param>
        /// <param name="ioType">IO type.</param>
        /// <param name="PNFrameData">Frame data.</param>
        protected void CreateRelatedIOEntry(
            int slotNumber,
            int subslotNumber,
            int length,
            IocrRelatedSubblockStruct api,
            IoTypes ioType,
            IPNFrameData pnFrameData)
        {
            RelatedIOBlockStruct relatedEntry = new RelatedIOBlockStruct();
            relatedEntry.SetSlotNumber(slotNumber);
            relatedEntry.SetSubslotNumber(subslotNumber);

            PNPlannerFrameDirection frameDirection = (PNPlannerFrameDirection)pnFrameData.FrameDirection;

            // Check the global start offset of the dfp frames
            IPNDfpFrameData dfpFrameData = pnFrameData as IPNDfpFrameData;

            // If both offsets are greater than 0, this means the method is called before, we mustn't set
            // the offsets again.
            if ((dfpFrameData != null) && ((OffsetIocr == 0) || (OffsetIOData == 0)))
            {
                // Find the related subframe
                IPNSubframeData subframeData = Utility.GetSubframeOfInterface(Interface, dfpFrameData);
                int subframeOffset = 0;
                if (subframeData != null)
                {
                    // Find the start point of this subframe (the offset changes in inbound and outbound direction).
                    subframeOffset = GetSubframeOffset(dfpFrameData, subframeData.SubframeId);
                }

                if ((dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame) && (OffsetIOData == 0))
                {
                    OffsetIOData += subframeOffset;
                }
                else if ((dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame) && (OffsetIocr == 0))
                {
                    OffsetIocr += subframeOffset;
                }
            }

            if (frameDirection == PNPlannerFrameDirection.InputFrame)
            {
                relatedEntry.SetFrameOffset(OffsetIOData);
                OffsetIOData += length;

                if (ioType == IoTypes.Output)
                {
                    api.SetRelatedIOCS(relatedEntry);
                }
                else
                {
                    api.SetRelatedIOData(relatedEntry);
                }
            }
            else if (frameDirection == PNPlannerFrameDirection.OutputFrame)
            {
                relatedEntry.SetFrameOffset(OffsetIocr);
                OffsetIocr += length;

                if (ioType == IoTypes.Output)
                {
                    api.SetRelatedIOData(relatedEntry);
                }
                else
                {
                    api.SetRelatedIOCS(relatedEntry);
                }
            }
        }

        #endregion
    }
}