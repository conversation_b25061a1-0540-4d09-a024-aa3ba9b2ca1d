using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace PNConfigTool.Converters
{
    /// <summary>
    /// 合并多个集合为一个集合的转换器
    /// </summary>
    public class MultiCollectionConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            // 创建一个临时集合来存储所有的项目
            List<object> result = new List<object>();
            
            // 遍历所有的集合
            foreach (object value in values)
            {
                if (value is IEnumerable collection)
                {
                    foreach (object item in collection)
                    {
                        result.Add(item);
                    }
                }
            }
            
            return result;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("MultiCollectionConverter不支持反向转换");
        }
    }
} 