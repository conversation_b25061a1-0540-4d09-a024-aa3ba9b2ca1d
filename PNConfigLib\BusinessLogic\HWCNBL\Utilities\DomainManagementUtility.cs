/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DomainManagementUtility.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Contains methods and utilities used for accessing sync domain attributes and relevant values.
    /// </summary>
    internal static class DomainManagementUtility
    {
        /// <summary>
        /// Calculate the allowed maximum irt bandwidth of the sync-domain.
        /// </summary>
        /// <remarks>
        /// Allowed irt bandwidth is calculated using the following equation:
        /// MaxSizeRed = MIN(MaxRTCBw, Sendclock – PDIRGlobalData.YellowTime,
        /// Sendclock – MinFragTime, MaxRedPeriodLength)
        /// MaxRedPeriodLength is the minimum value of the PNIrtMaxRedPeriodLength of all synchronized interfaces
        /// within the sync-domain.
        /// </remarks>
        /// <param name="syncDomainBl">The sync domain BL object whose maximum irt bandwidth will be retrieved.</param>
        /// <returns>Maximum size of the red period.</returns>
        public static long GetMaxSizeRed(SyncDomainBusinessLogic syncDomainBl)
        {
            uint yellowTime = GetYellowTime(syncDomainBl);
            uint minFragTime = GetMinFragTime(syncDomainBl);
            // Check the max. available red period and compare it with MaxRedPeriodLength
            long maxSizeRed = Math.Min(
                syncDomainBl.MaxBandwidthForCyclic,
                Math.Min(
                    syncDomainBl.MaxRedPeriodLength,
                    Math.Min(syncDomainBl.SendClock - yellowTime, syncDomainBl.SendClock - minFragTime)));
            return maxSizeRed;
        }

        /// <summary>
        /// Calculates the minimum fragmentation time in the sync domain.
        /// </summary>
        /// <param name="syncDomainBl">The sync domain BL object whose minimum fragmentation time will be retrieved.</param>
        /// <returns>The minimum fragmentation time in the sync domain.</returns>
        public static uint GetMinFragTime(SyncDomainBusinessLogic syncDomainBl)
        {
            uint minFragTime;
            if (!syncDomainBl.IsFragmentationActive)
            {
                // 121760 = 1522 (Max Frame size in bytes) * 80
                minFragTime = Math.Max(
                    125000,
                    syncDomainBl.MaxFrameStartTime + 121760 + syncDomainBl.MaxYellowSafetyMargin);
            }
            else
            {
                uint fragSize = (uint)(syncDomainBl.SendClock > 62500 ? 256 : 128);
                minFragTime = syncDomainBl.MaxFrameStartTime + fragSize * 80 + syncDomainBl.MaxYellowSafetyMargin;
            }

            return minFragTime;
        }

        /// <summary>
        /// Gets the PDIRGlobalData.YellowValue.
        /// </summary>
        /// <remarks>
        /// The equation to use is given below:
        /// If fragmentation is not active throughout the sync-domain: 125000
        /// If fragmentation is active throughout the sync-domain:
        /// Max(MaxMinYellowTime, ((MaxNonFragFrame + 20) * 80 + MaxYellowSafetyMargin)
        /// MaxNonFragFrame: Max(98, MaxRTC1Frame)
        /// MaxRTC1Frame: Largest Real time class 1 frame (RT Frame) of the sync-domain.
        /// MaxYellowSafetyMargin: Largest YellowSafetyMargin of the sync-domain
        /// MaxMinYellowTime: Largest MinYellowTime of the sync-domain, to be returned if the calculated value for
        /// YellowTime is lower than this value.
        /// </remarks>
        /// <returns>Time of the yellow period.</returns>
        public static uint GetYellowTime(SyncDomainBusinessLogic syncDomainBl)
        {
            if (!syncDomainBl.IsFragmentationActive)
            {
                return PNConstants.DefaultPDIRDataYellowTime;
            }

            // Max RTC 1 & 2 can be used instead of just RTC1 since IRTFlex & IRTTop combination is not allowed.
            uint yellowVal = (20 + Math.Max(98, syncDomainBl.PNPlannerResults.MaxRTC12FrameLength)) * 80
                             + syncDomainBl.MaxYellowSafetyMargin;

            // Check against the Maximum of all MinYellowTime within the sync domain
            return Math.Max(yellowVal, syncDomainBl.MaxMinYellowTime);
        }

        /// <summary>
        /// Function checks whether the actual interface represents a sync master.
        /// if yes, it checks the sync slaves of the sync domain whether
        /// they all lie inside the boundaries.
        /// </summary>
        /// <param name="deviceInterface">The PN interface of the object</param>
        /// <returns>Returns those ports which have problems</returns>
        internal static IEnumerable<DataModel.PCLObjects.Port> CheckSlavesOutsideOfTheBoundariesOfSyncDomain(
            Interface deviceInterface)
        {
            if (deviceInterface == null)
            {
                yield break;
            }

            PclObject connector = deviceInterface.GetIOConnector();
            if (connector == null)
            {
                yield break;
            }

            PNIRTSyncRole syncrole = PNAttributeUtility.GetAdjustedSyncRole(connector);
            SyncDomain syncDomain = deviceInterface.SyncDomain;

            //if it's the sync master and we have its synchDomain, then we have to call our check method
            if ((syncDomain != null)
                && ((syncrole == PNIRTSyncRole.SyncMaster)
                    || (syncrole == PNIRTSyncRole.PrimarySyncMaster)))
            {
                List<Interface> notAccessibleInterfaces;
                List<DataModel.PCLObjects.Port> boundaryPorts;
                GetInterfacesOutsideOfTheBoundariesOfSyncDomain(
                    deviceInterface,
                    out notAccessibleInterfaces,
                    out boundaryPorts,
                    true);

                foreach (DataModel.PCLObjects.Port port in boundaryPorts)
                {
                    yield return port;
                }
            }
        }

        /// <summary>
        /// Gets the total number of secondary sync masters of the sync domain.
        /// </summary>
        /// <param name="syncDomain">The sync domain whose number of secondary sync masters will be retrieved.</param>
        /// <returns>Total number of secondary sync masters of syncDomain.</returns>
        internal static int GetNumberOfSecondarySyncMastersInSyncDomain(PclObject syncDomain)
        {
            return GetNumberOfSyncMastersInSyncDomain(syncDomain as SyncDomain, true);
        }

        /// <summary>
        /// Gets the total number of SyncMasters and Primary-SyncMasters of the domain.
        /// Secondary sync masters are not included.
        /// </summary>
        /// <param name="syncDomain">The sync domain whose number of sync masters will be retrieved.</param>
        /// <returns>Total number of sync masters and primary sync masters of syncDomain.</returns>
        internal static int GetNumberOfSyncMastersInSyncDomain(PclObject syncDomain)
        {
            return GetNumberOfSyncMastersInSyncDomain(syncDomain as SyncDomain, false);
        }

        /// <summary>
        /// function checks the syncdomain for interfaces which lie outside of the sync boundary
        /// </summary>
        /// <param name="interfaceOfSyncMaster">reference to the sync master's interface</param>
        /// <param name="notAccessibleInterfaces">the result list</param>
        /// <param name="boundaryPorts"></param>
        /// <param name="onlySlaves">whether only the slaves have to be returned</param>
        private static void GetInterfacesOutsideOfTheBoundariesOfSyncDomain(
            Interface interfaceOfSyncMaster,
            out List<Interface> notAccessibleInterfaces,
            out List<DataModel.PCLObjects.Port> boundaryPorts,
            bool onlySlaves)
        {
            //we get the interfaces which are accessible from the sync master
            List<Interface> portInterconnectedInterfaces =
                PNNavigationUtility.GetAccessiblePortInterconnectedInterfaces(interfaceOfSyncMaster);

            ////we loop through the interfaces of the sync domain which are accessible from the sync master via port interconnection
            //and see, whether the list of not accesible interfaces contains a synchronized interface
            notAccessibleInterfaces = new List<Interface>();
            boundaryPorts = new List<DataModel.PCLObjects.Port>();
            foreach (Interface pnInterface in portInterconnectedInterfaces)
            {
                PclObject connector = pnInterface.GetIOConnector();
                if (connector == null)
                {
                    continue;
                }

                PNIRTSyncRole syncrole = PNAttributeUtility.GetAdjustedSyncRole(connector);
                if (syncrole == PNIRTSyncRole.NotSynchronized)
                {
                    continue;
                }

                List<DataModel.PCLObjects.Port> portsOfInterface = NavigationUtilities.GetPortModules(pnInterface);

                foreach (DataModel.PCLObjects.Port portObject in portsOfInterface)
                {
                    List<DataModel.PCLObjects.Port> partnerPorts = (List<DataModel.PCLObjects.Port>)portObject.GetPartnerPorts();
                    if (partnerPorts.Count == 0)
                    {
                        continue;
                    }
                    foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                    {
                        Interface interfaceOfPartner = NavigationUtilities.GetInterfaceOfPort(partnerPort);

                        if (interfaceOfPartner.SyncDomain == pnInterface.SyncDomain)
                        {
                            AttributeAccessCode ac = new AttributeAccessCode();
                            uint byteValue =
                                portObject.AttributeAccess.GetAnyAttribute<uint>(
                                    InternalAttributeNames.PnIrtPortSyncDomainBoundary,
                                    ac,
                                    0);

                            if ((byteValue == 1)
                                && ac.IsOkay)
                            {
                                connector = interfaceOfPartner.GetIOConnector();
                                if (connector == null)
                                {
                                    continue;
                                }

                                syncrole = PNAttributeUtility.GetAdjustedSyncRole(connector);

                                if ((syncrole != PNIRTSyncRole.NotSynchronized)
                                    || !onlySlaves)
                                {
                                    //this interface is synchronized and has a boundary port -> sync frame are not forwarded within the sync domain -> prohibited
                                    notAccessibleInterfaces.Add(interfaceOfPartner);

                                    if (!boundaryPorts.Contains(portObject))
                                    {
                                        boundaryPorts.Add(portObject);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Private method used for getting the number of sync masters in a given syncDomain.
        /// </summary>
        /// <param name="syncDomain">The sync domain whose number of sync masters will be retrieved.</param>
        /// <param name="secondaryMasterNeeded">Whether the number of secondary sync masters should be included.</param>
        /// <returns>Total number of synfc masters of syncDomain; or 0 if syncDomain is null.</returns>
        private static int GetNumberOfSyncMastersInSyncDomain(SyncDomain syncDomain, bool secondaryMasterNeeded)
        {
            if (syncDomain == null)
            {
                return 0;
            }

            int numberOfSyncMastersInSyncDomain = 0;
            IList<Interface> participants = syncDomain.GetInterfaces();
            AttributeAccessCode ac = new AttributeAccessCode();
            foreach (Interface interfaceSubmodule in participants)
            {
                PclObject ioConnector = interfaceSubmodule.GetIOConnector();

                if (ioConnector != null)
                {
                    ac.Reset();
                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);
                    // Check whether the ioConnector is a master.
                    if (!secondaryMasterNeeded
                        && ((syncRole == PNIRTSyncRole.SyncMaster) || (syncRole == PNIRTSyncRole.PrimarySyncMaster)))
                    {
                        numberOfSyncMastersInSyncDomain++;
                    }
                    else if (secondaryMasterNeeded && (syncRole == PNIRTSyncRole.SecondarySyncMaster))
                    {
                        numberOfSyncMastersInSyncDomain++;
                    }
                }
            }
            return numberOfSyncMastersInSyncDomain;
        }
    }
}