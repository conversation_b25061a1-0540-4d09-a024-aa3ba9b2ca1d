/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SubmoduleBL.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.PNProjectManager;
using PNConfigLib.Gsd.Interpreter.Common;

#endregion

namespace PNConfigLib.HWCNBL
{
    using System.Linq;

    using PNConfigLib.BusinessLogic.HWCNBL;

    internal class SubmoduleBL : HwcnBusinessLogic
    {
        internal SubmoduleBL(Submodule submodule, List<object> ioAddresses, IOAddressManager ioAddressManager)
        {
            Submodule = submodule;
            InitBL();
            FillIOData(ioAddresses, ioAddressManager);
        }

        internal SubmoduleBL(Submodule submodule)
        {
            Submodule = submodule;
            InitBL();
        }

        private Submodule Submodule { get; }

        internal void Configure(
            ModuleTypeSubmodule xmlSubmodule,
            IOAddressManager ioAddressManager,
            bool isochronousSubmodule = false)
        {
            FillGeneralAttributes(Submodule, xmlSubmodule.General, xmlSubmodule.SubmoduleID);

            Submodule.AttributeAccess.SetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                (int)xmlSubmodule.SubslotNumber - 1);
            Submodule.AttributeAccess.SetAnyAttribute<int>(
                InternalAttributeNames.PnSubslotNumber,
                (int)xmlSubmodule.SubslotNumber);

            AttributeAccessCode ac = new AttributeAccessCode();
            IOData smCatalogIOData = ((SubmoduleCatalog)Submodule.PCLCatalogObject).IOData;

            if (smCatalogIOData != null)
            {
                IoDataHelper ioDataHelper = new IoDataHelper();
                IoDataHelper.IoAddressImplementationType ioAddressImplementationType;
                int startAddress = 0;
                if (smCatalogIOData.InputDataItems != null && smCatalogIOData.InputDataItems.Length > 0)
                {
                    if (xmlSubmodule.IOAddresses.Exists(w => w is ModuleTypeInputAddresses))
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromXml;
                        startAddress = (int)xmlSubmodule.IOAddresses.OfType<ModuleTypeInputAddresses>()
                            .FirstOrDefault().StartAddress;
                    }
                    else if (!this.Submodule.IsVirtual)
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromVirtualSubmodule;
                    }
                    else
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.AlreadySet;
                    }

                    ioDataHelper.SetIOData(
                        xmlSubmodule.SubmoduleID,
                        startAddress,
                        IoTypes.Input,
                        ioAddressManager,
                        this.Submodule,
                        ac,
                        InternalAttributeNames.InAddressRange,
                        InternalAttributeNames.InputStartAddress,
                        ioAddressImplementationType);
                }

                if (smCatalogIOData.OutputDataItems != null && smCatalogIOData.OutputDataItems.Length > 0)
                {
                    if (xmlSubmodule.IOAddresses.Exists(w => w is ModuleTypeOutputAddresses))
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromXml;
                        startAddress = (int)xmlSubmodule.IOAddresses.OfType<ModuleTypeOutputAddresses>()
                            .FirstOrDefault().StartAddress;
                    }
                    else if (!this.Submodule.IsVirtual)
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromVirtualSubmodule;
                    }
                    else
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.AlreadySet;
                    }

                    ioDataHelper.SetIOData(
                        xmlSubmodule.SubmoduleID,
                        startAddress,
                        IoTypes.Output,
                        ioAddressManager,
                        this.Submodule,
                        ac,
                        InternalAttributeNames.OutAddressRange,
                        InternalAttributeNames.OutputStartAddress,
                        ioAddressImplementationType);
                }
            }

            Dictionary<uint, byte[]> parameterRecordDataItems = FillParameterRecordDataItems(
                Submodule,
                xmlSubmodule.ParameterRecordDataItems,
                xmlSubmodule.SubmoduleID);
            Submodule.SetParameterRecordDataItems(parameterRecordDataItems);

            if (isochronousSubmodule)
            {
                Submodule.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.ClockSyncMode, true);
                Submodule.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.IsClockSyncModeSelected, true);
            }
        }

        private void InitBL()
        {
            InitAttributes();
        }

        private void InitAttributes()
        {
            Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnSubslotNumber, 0);
            Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.InputStartAddress, 0);
            Submodule.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.OutputStartAddress, 0);
            Submodule.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.ClockSyncMode, false);
            Submodule.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.IsClockSyncModeSelected, false);
            Submodule.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)SharedIoAssignment.None);
        }

        private void FillIOData(List<object> ioAddresses, IOAddressManager ioAddressManager)
        {
            if (Submodule.PCLCatalogObject != null)
            {
                SubmoduleCatalog smCatalog = Submodule.PCLCatalogObject as SubmoduleCatalog;
                if (smCatalog != null && smCatalog.IOData != null)
                {
                    if (smCatalog.IOData.InputDataItems != null)
                    {
                        int inAddressRange = smCatalog.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.InAddressRange,
                            new AttributeAccessCode(),
                            0);
                        FillIODataItem(
                            ioAddresses,
                            IoTypes.Input,
                            inAddressRange,
                            ioAddressManager);
                    }
                    if (smCatalog.IOData.OutputDataItems != null)
                    {
                        int outAddressRange = smCatalog.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.OutAddressRange,
                            new AttributeAccessCode(),
                            0);
                        FillIODataItem(
                            ioAddresses,
                            IoTypes.Output,
                            outAddressRange,
                            ioAddressManager);
                    }
                }
            }
        }

        private void FillIODataItem(
            List<object> ioAddresses,
            IoTypes ioType,
            int addressRange,
            IOAddressManager ioAddressManager)
        {
            string startAddressName = String.Empty;
            uint startAddress = 0;
            int dataLength = 0;


            switch (ioType)
            {
                case IoTypes.Input:
                    startAddressName = InternalAttributeNames.InputStartAddress;
                    dataLength = Submodule.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.InAddressRange,
                            new AttributeAccessCode(),
                            0);
                    break;

                case IoTypes.Output:
                    startAddressName = InternalAttributeNames.OutputStartAddress;
                    dataLength = Submodule.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.OutAddressRange,
                            new AttributeAccessCode(),
                            0);
                    break;
            }

            startAddress = GetStartAddress(ioAddresses, ioType, addressRange, ioAddressManager, startAddress);
            Submodule.DataAddresses.Add(new DataAddress(ioType, (int)startAddress, dataLength));
            Submodule.AttributeAccess.SetAnyAttribute<int>(startAddressName, (int)startAddress);
        }

        private static uint GetStartAddress(
            List<object> ioAddresses,
            IoTypes ioType,
            int addressRange,
            IOAddressManager ioAddressManager,
            uint startAddress)
        {
            if (ioAddresses != null && ioAddresses.Count > 0)
            {
                foreach (object ioAddress in ioAddresses)
                {
                    if (ioType == IoTypes.Input)
                    {
                        ModuleTypeInputAddresses inputAddress = ioAddress as ModuleTypeInputAddresses;
                        if (inputAddress != null)
                        {
                            startAddress = inputAddress.StartAddress;
                        }
                    }
                    else if (ioType == IoTypes.Output)
                    {
                        ModuleTypeOutputAddresses outputAddress = ioAddress as ModuleTypeOutputAddresses;
                        if (outputAddress != null)
                        {
                            startAddress = outputAddress.StartAddress;
                        }
                    }
                }
            }
            else
            {
                List<int> freeAddress = ioAddressManager.GetFreeAddresses(addressRange, ioType, false);
                startAddress = (uint)freeAddress[0];
            }

            return startAddress;
        }
    }
}