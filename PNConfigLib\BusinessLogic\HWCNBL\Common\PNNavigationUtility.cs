/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNNavigationUtility.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common
{
    /// <summary>
    /// Contains methods for navigating across the objects in the PNConfigLib project.
    /// </summary>
    internal static class PNNavigationUtility
    {
        /// <summary>
        /// Gets the interfaces who have port interconnections with the given interface.
        /// </summary>
        /// <remarks>
        /// This method returns all interfaces that can be reached with the port interconnections
        /// of the given interface; i.e. if Interface1 is connected to Interface2, and Interface2 is connected
        /// to Interface3, this method returns Interfaces 1,2 and 3 for any of the interfaces.
        /// </remarks>
        /// <param name="deviceInterface">The interface whose connected interface submodules will be returned.</param>
        /// <returns>A list containing all accessible interfaces from the given interface submodule.</returns>
        public static List<Interface> GetAccessiblePortInterconnectedInterfaces(Interface deviceInterface)
        {
            Dictionary<Interface, BoolValue> portInterconnectedInterfaces = new Dictionary<Interface, BoolValue>();
            portInterconnectedInterfaces[deviceInterface] = new BoolValue(false);

            bool finished;
            do
            {
                List<Interface> newInterfaces = new List<Interface>();
                foreach (KeyValuePair<Interface, BoolValue> node in portInterconnectedInterfaces)
                {
                    if (!node.Value.Value)
                    {
                        List<Interface> directConnectedInterfaces = new List<Interface>();
                        node.Value.Value = true;

                        List<DataModel.PCLObjects.Port> portsOfInterface = (List<DataModel.PCLObjects.Port>)node.Key.GetPorts();

                        foreach (DataModel.PCLObjects.Port port in portsOfInterface)
                        {
                            IList<DataModel.PCLObjects.Port> partnerPorts = port.GetPartnerPorts();
                            if (partnerPorts.Count == 0)
                            {
                                continue;
                            }
                            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                            {
                                Interface partnerInterface = partnerPort.GetInterface();
                                if ((deviceInterface.SyncDomain != null)
                                    && (partnerInterface.SyncDomain != null)
                                    && (deviceInterface.SyncDomain == partnerInterface.SyncDomain))
                                {
                                    directConnectedInterfaces.Add(partnerInterface);
                                }
                            }
                        }

                        foreach (Interface directConnectedInterface in directConnectedInterfaces)
                        {
                            if (!newInterfaces.Contains(directConnectedInterface)
                                && (deviceInterface != directConnectedInterface))
                            {
                                newInterfaces.Add(directConnectedInterface);
                            }
                        }
                    }
                }

                finished = true;
                // IF there aren't any new interface to navigate, we're done.
                if (newInterfaces.Count > 0)
                {
                    // Otherwise, we add the new interfaces to the list of the accessible interfaces,
                    // if they aren't already added.
                    foreach (Interface newInterface in newInterfaces)
                    {
                        if (!portInterconnectedInterfaces.ContainsKey(newInterface))
                        {
                            finished = false;
                            portInterconnectedInterfaces[newInterface] = new BoolValue(false);
                        }
                    }
                }
            }
            while (!finished);

            return portInterconnectedInterfaces.Keys.ToList();
        }

        /// <summary>
        /// Gets head, interface and port submodules of the headmodule.
        /// </summary>
        /// <returns>A list of head, interface and port submodules of the headmodule.</returns>
        internal static IList<PclObject> GetHeadSubmodulesSorted(DecentralDevice module)
        {
            List<PclObject> list = new List<PclObject>();
            List<PclObject> elements = new List<PclObject>();
            elements.AddRange(module.GetVirtualSubmodules());
            elements.Add(module.GetInterface());
            List<PclObject> interfaceSubmodules = new List<PclObject>();
            foreach (PclObject element in elements)
            {
                if (element == null)
                {
                    continue;
                }
                if (element is Interface)
                {
                    if (!GeneralUtilities.IsPDEVDevice(element))
                    {
                        continue;
  
                    }
                    list.Add(element);
                    List<PclObject> ports = (List<PclObject>)element.GetElements();
                    foreach (PclObject port in ports)
                    {
                        if (!(port is DataModel.PCLObjects.Port))
                        {
                            continue;
                        }
                        interfaceSubmodules.Add(port);
                    }
                    continue;
                }
                if (element is Submodule)
                {
                    list.Add(element);
                }
            }

            list.AddRange(interfaceSubmodules);

            return list;
        }

        /// <summary>
        /// Gets the media module object of port, if exists.
        /// </summary>
        /// <returns>Media module or null.</returns>
        internal static PclObject GetMediaModuleOfPort(DataModel.PCLObjects.Port port)
        {
            if (port == null)
            {
                return null;
            }
            PclObject module = NavigationUtilities.GetContainer(port);
            if ((module == null)
                || !(module is Module))
            {
                return null;
            }

            return module;
        }

        /// <summary>
        /// Gets the head module and the modules connected to the device of a given interface.
        /// </summary>
        /// <param name="decentralDeviceInterfaceSubmodule">The interface whose device's modules will be retrieved.</param>
        /// <returns>A list containing the headmodule and modules of the device of the given interface submodule.</returns>
        internal static List<PclObject> GetModulesFromInterfaceSubmodule(Interface decentralDeviceInterfaceSubmodule)
        {
            DecentralDevice decentralDevice = decentralDeviceInterfaceSubmodule.GetDevice() as DecentralDevice;

            if (decentralDevice == null)
            {
                throw new PNFunctionsException(
                    "Decentral device interface submodule not connected to a decentral device.");
            }

            List<PclObject> modules = new List<PclObject>();

            modules.Add(decentralDevice);
            modules.AddRange(decentralDevice.GetModules());

            return modules;
        }

        /// <summary>
        /// This method returns all modules in the rack of the input device interface sorted by their position number.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface submodule.</param>
        /// <returns>A list of the modules in the rack sorted by their position number.</returns>
        internal static IList<PclObject> GetModulesSorted(Interface deviceInterfaceSubmodule)
        {
            List<PclObject> allModules = new List<PclObject>();

            DecentralDevice device = deviceInterfaceSubmodule.ParentObject as DecentralDevice;
            if (device != null)
            {
                allModules.AddRange(device.GetModules());
                allModules.Add(device);
            }
            allModules =
                allModules.OrderBy(
                    x =>
                    x.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PositionNumber,
                        new AttributeAccessCode(),
                        0)).ToList();
            return allModules;
        }

        /// <summary>
        /// This method returns all modules in the rack of the input device interface sorted by their position number.
        /// </summary>
        /// <param name="device"></param>
        /// <returns>A list of the modules in the rack sorted by their position number.</returns>
        internal static IList<PclObject> GetModulesSorted(DecentralDevice device)
        {
            List<PclObject> allModules = new List<PclObject>();
            if (device != null)
            {
                allModules.AddRange(device.GetModules());
                allModules.Add(device);
            }
            allModules =
                allModules.OrderBy(
                    x =>
                        x.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.PositionNumber,
                            new AttributeAccessCode(),
                            0)).ToList();
            return allModules;
        }

        /// <summary>
        /// This method returns the CoreObjects of all modules in the rack of the input device interface sorted by their Position
        /// Number
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface submodule</param>
        /// <returns>A list of CoreObjects sorted by their module's Possition Number (default is an empty list)</returns>
        internal static IList<PclObject> GetModulesSortedCore(Interface deviceInterfaceSubmodule)
        {
            List<PclObject> modulesUnsorted = GetModulesFromInterfaceSubmodule(deviceInterfaceSubmodule);
            SortedDictionary<int, PclObject> dict = new SortedDictionary<int, PclObject>();
            foreach (PclObject module in modulesUnsorted)
            {
                int pos = AttributeUtilities.GetPositionNumber(module);
                dict.Add(pos, module);
            }
            List<PclObject> result = dict.Values.ToList();
            return result;
        }

        /// <summary>
        /// This method returns all submodules of a module sorted by their position number.
        /// </summary>
        /// <returns>A list of the submodules sorted by their position number.</returns>
        internal static IList<PclObject> GetSubmodulesSorted(PclObject module)
        {
            List<PclObject> retval = new List<PclObject>();
            List<PclObject> allSubmodules = (List<PclObject>)module.GetElements();
            if (allSubmodules != null)
            {
                retval = allSubmodules.OrderBy(
                m => m.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(), 0)).ToList();
            }
            return retval;
        }

        /// <summary>
        /// Helper class for the GetInterfacesOutsideOfTheBoundariesOfSyncDomain method; used
        /// as a workaround for modifying the contents of a dictionary while iterating it.
        /// </summary>
        internal class BoolValue
        {
            /// <summary>
            /// The content of the value.
            /// </summary>
            public bool Value;

            /// <summary>
            /// Constructor for BoolValue.
            /// </summary>
            /// <param name="value">Initial value.</param>
            public BoolValue(bool value)
            {
                Value = value;
            }
        }

        /// <summary>
        /// returns a PN head submodule
        /// </summary>
        /// <param name="headmodule">the head module</param>
        /// <param name="checkForSharedAssignment"></param>
        /// <param name="notAssignedSubmoduleIDs"></param>
        /// <returns>the PN head submodule</returns>
        internal static IList<PclObject> GetHeadSubmodulesPN(PclObject headmodule, bool checkForSharedAssignment, ref List<string> notAssignedSubmoduleIDs)
        {
            if (headmodule == null)
            {
                return null;
            }
            IList<PclObject> elements = GetSubmodulesSorted(headmodule);
            IList<PclObject> headSubmodules = new List<PclObject>();
            foreach (PclObject element in elements)
            {
                if (!(element is Submodule) || element is Interface)
                {
                    continue;
                }
                if (checkForSharedAssignment 
                    && (element.AttributeAccess
                            .GetAnyAttribute<uint>(InternalAttributeNames.SharedIoAssignment
                            , new AttributeAccessCode(), 0) == 1))
                {
                    notAssignedSubmoduleIDs.Add(element.Id);
                }
                headSubmodules.Add(element);
            }
            return headSubmodules;
        }

        public static Interface GetControllerOfIOSystem(DataModel.PCLObjects.IOSystem ioSystem)
        {
            if ((ioSystem == null) || (ioSystem.PNIOC == null))
            {
                return null;
            }
            return ioSystem.PNIOC.GetInterface();
        }
    }
}