/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AttributeAccess.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// Allows access to the attributes of PclObject and PclCatalogObject classes.
    /// </summary>
    public class AttributeAccess
    {
        /// <summary>
        /// The object whose attributes are kept.
        /// </summary>
        private IPclObject m_DataObject;

        public Dictionary<string, object> AttributeList { get; } = new Dictionary<string, object>();

        /// <summary>
        /// The constructor for using AttributeAccess with PclObject.
        /// </summary>
        /// <param name="obj">PCLObject to be used.</param>
        public AttributeAccess(IPclObject obj)
        {
            m_DataObject = obj;
        }

        /// <summary>
        /// Adds an attribute to the data object.
        /// </summary>
        /// <typeparam name="T">Type of the attribute.</typeparam>
        /// <param name="attributeName">Name of the attribute.</param>
        /// <param name="initialValue">Initial value of the attribute.</param>
        /// <returns>An AddAttributeReturnCode instance with the result of the add operation.</returns>
        public AddAttributeReturnCode AddAnyAttribute<T>(string attributeName, T initialValue)
        {
            AddAttributeReturnCode code = AddAttributeReturnCode.Added;

            if (!AttributeList.ContainsKey(attributeName))
            {
                AttributeList[attributeName] = initialValue;
            }
            else
            {
                code = AddAttributeReturnCode.ExistsDynamic;
            }
            return code;
        }

        /// <summary>
        /// Adds an attribute to the data object with given get and set handlers,
        /// which are triggered automatically when getting and setting the attribute.
        /// </summary>
        /// <remarks>
        /// This method is used for attributes that are calculated using other attributes or properties.
        /// This is also the reason why there isn't an initial value parameter.
        /// </remarks>
        /// <typeparam name="T">Type of the attribute.</typeparam>
        /// <param name="attributeName">Name of the attribute.</param>
        /// <param name="getHandler">Get handler of the attribute.</param>
        /// <param name="setHandler">Set handler of the attribute.</param>
        /// <returns>An AddAttributeReturnCode instance with the result of the add operation.</returns>
        internal AddAttributeReturnCode AddAnyAttribute<T>(
            string attributeName,
            AttributeHandler<T>.GetHandler getHandler,
            AttributeHandler<T>.SetHandler setHandler)
        {
            AddAttributeReturnCode code = AddAttributeReturnCode.Added;

            if (!AttributeList.ContainsKey(attributeName))
            {
                AttributeHandler<T> h = new AttributeHandler<T>();
                h.AttributeGetter = getHandler;
                h.AttributeSetter = setHandler;
                AttributeList[attributeName] = h;
            }
            else
            {
                code = AddAttributeReturnCode.ExistsDynamic;
            }
            return code;
        }

        /// <summary>
        /// Gets an attribute from the data object.
        /// </summary>
        /// <typeparam name="T">Type of the attribute.</typeparam>
        /// <param name="attributeName">Name of the attribute.</param>
        /// <param name="accessCode">Keeps the result of the get operation.</param>
        /// <param name="defaultValue">The value to be returned if the attribute could not be retrieved.</param>
        /// <returns>
        /// The value of the attribute if the attribute exists on the data object and the correct
        /// type is specified; default value otherwise.
        /// </returns>
        public T GetAnyAttribute<T>(string attributeName, AttributeAccessCode accessCode, T defaultValue)
        {
            if (accessCode == null)
            {
                accessCode = new AttributeAccessCode();
            }

            // Check whether attribute is in the attribute list of PclObject. If it is, retrieve it from there.
            // If not, check whether PclObject has a PclCatalogObject and retrieve it from the attribute list of
            // PclCatalogObject if it exists.
            Dictionary<string, object> attributeList = m_DataObject is PclObject
                                                       && !AttributeList.ContainsKey(attributeName)
                                                       && ((PclObject)m_DataObject).PCLCatalogObject != null
                                                           ? ((PclObject)m_DataObject).PCLCatalogObject.AttributeAccess.AttributeList
                                                           : m_DataObject.AttributeAccess.AttributeList;

            if (!attributeList.ContainsKey(attributeName) || (attributeList[attributeName] == null))
            {
                accessCode.Set(AttributeAccessCode.AccessCode.InvalidValue);
            }
            else
            {
                AttributeHandler<T> attribute = attributeList[attributeName] as AttributeHandler<T>;
                if (attribute != null)
                {
                    if (attribute.AttributeGetter != null)
                    {
                        return attribute.AttributeGetter();
                    }
                    return attribute.Value;
                }

                if (typeof(T) == attributeList[attributeName].GetType())
                {
                    return (T)attributeList[attributeName];
                }

                accessCode.Set(AttributeAccessCode.AccessCode.InvalidType);
            }

            return defaultValue;
        }

        /// <summary>
        /// Removes the attribute with this name. If the attribute does not exist, the call is ignored.
        /// </summary>
        /// <param name="attributeName">Name of the attribute.</param>
        public void RemoveAttribute(string attributeName)
        {
            AttributeList.Remove(attributeName);
        }

        /// <summary>
        /// Sets an attribute on the data object.
        /// </summary>
        /// <typeparam name="T">Type of the attribute.</typeparam>
        /// <param name="attributeName">Name of the attribute.</param>
        /// <param name="newValue">The value to be set.</param>
        /// <returns>An AttributeAccessCode instance with the result of the set operation.</returns>
        public AttributeAccessCode SetAnyAttribute<T>(string attributeName, T newValue)
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();

            if (AttributeList.ContainsKey(attributeName))
            {
                AttributeHandler<T> attribute = AttributeList[attributeName] as AttributeHandler<T>;
                if (attribute != null)
                {
                    if (attribute.AttributeSetter != null)
                    {
                        attribute.AttributeSetter(newValue);
                    }
                    else
                    {
                        attribute.Value = newValue;
                    }
                }
                else
                {
                    AttributeList[attributeName] = newValue;
                }
            }
            else
            {
                accessCode.Set(AttributeAccessCode.AccessCode.InvalidValue);
            }

            return accessCode;
        }
    }
}