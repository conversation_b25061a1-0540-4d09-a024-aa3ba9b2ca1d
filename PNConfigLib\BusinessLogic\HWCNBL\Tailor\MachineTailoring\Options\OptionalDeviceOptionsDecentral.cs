/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: OptionalDeviceOptionsDecentral.cs         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.ComponentModel;
using System.Globalization;
using System.Xml;

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options
{
    /// <summary>
    /// Summary description for MachineTailorGUIDecentral.
    /// </summary>
    internal class OptionalDeviceOptionsDecentral : TailorLogicBase
    {
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Nested classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Constants and enumerations

        // Contains all constants and enumerations

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private readonly DataModel.PCLObjects.Interface m_IODevice;
        private DataModel.PCLObjects.Interface m_IOControllerInterface;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        public ITailoringConsistency OptionalDevice => new OptionalDevice(m_IODevice);

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Delegates and events

        // Contains all delegate and events

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        private OptionalDeviceOptionsDecentral(DataModel.PCLObjects.Interface ioDeviceIF, DataModel.PCLObjects.Interface ioControllerIF)
        {
            m_IODevice = ioDeviceIF;
            m_IOControllerInterface = ioControllerIF;
        }

        public OptionalDeviceOptionsDecentral(DataModel.PCLObjects.Interface ioDeviceIF)
            : this(ioDeviceIF, null)
        {
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class


        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region I... members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Overrides and overridables

        // Contains all public and protected overrides as well as overridables of the class

        protected override DataModel.PCLObjects.Interface IoControllerInterface => m_IOControllerInterface ??
                       (m_IOControllerInterface = NavigationUtilities.GetControllerOfDevice(m_IODevice));

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Protected methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Private implementation

        // Contains the private implementation of the class

        #endregion
    }
}