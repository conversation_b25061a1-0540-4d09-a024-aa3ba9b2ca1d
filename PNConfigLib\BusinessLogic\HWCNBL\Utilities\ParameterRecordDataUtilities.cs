/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ParameterRecordDataUtilities.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Text;

using GSDI;

using PNConfigLib.ConfigReader.Configuration;
using ConstData = PNConfigLib.Gsd.Interpreter.Common.ConstData;
using ParameterRecordData = PNConfigLib.Gsd.Interpreter.Common.ParameterRecordData;
using RefData = PNConfigLib.Gsd.Interpreter.Common.RefData;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class ParameterRecordDataUtilities
    {
        private static readonly int s_HexadecimalValue = 16;

        private static readonly string s_TrueString = "true";

        private static readonly uint s_GSDUnsigned8Length = 1;

        private static readonly uint s_GSDFloat32Length = 4;

        private static readonly int s_HexadecimalPartLength = 2;


        public static void FillBytes(ParameterRecordData parameterRecordData, byte[] data)
        {
            if (parameterRecordData == null)
            {
                return;
            }

            if (parameterRecordData.Consts != null)
            {
                foreach (ConstData constData in parameterRecordData.Consts)
                {
                    if (constData == null)
                    {
                        continue;
                    }

                    uint byteOffset = constData.ByteOffset;
                    foreach (object singleData in constData.Values)
                    {
                        byte singleByte = byte.Parse(singleData.ToString(), CultureInfo.InvariantCulture);
                        data[byteOffset] = singleByte;
                        byteOffset++;
                    }
                }
            }

            if (parameterRecordData.Refs == null)
            {
                return;
            }

            foreach (RefData refData in parameterRecordData.Refs)
            {
                if (refData == null)
                {
                    continue;
                }

                switch (refData.DataType)
                {
                    case DataTypes.GSDBit:
                        WriteBit(data, refData.ByteOffset, refData.BitOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDBitArea:
                        WriteBitArea(
                            data,
                            refData.ByteOffset,
                            refData.BitOffset,
                            refData.BitLength,
                            refData.DefaultValue);
                        break;
                    case DataTypes.GSDInteger8:
                    case DataTypes.GSDUnsigned8:
                    case DataTypes.GSDInteger16:
                    case DataTypes.GSDUnsigned16:
                    case DataTypes.GSDInteger32:
                    case DataTypes.GSDUnsigned32:
                    case DataTypes.GSDFloat32:
                    case DataTypes.GSDInteger64:
                    case DataTypes.GSDUnsigned64:
                    case DataTypes.GSDFloat64:
                    case DataTypes.Gsdn2:
                    case DataTypes.Gsdn4:
                    case DataTypes.Gsdr2:
                    case DataTypes.Gsdt2:
                    case DataTypes.Gsdt4:
                    case DataTypes.Gsdd2:
                    case DataTypes.Gsdx2:
                    case DataTypes.Gsdx4:
                        WriteBytes(data, refData.ByteOffset, refData.DefaultValue, refData.DataType);
                        break;
                    case DataTypes.GSDVisibleString:
                        WriteBytesOfVisibleString(data, refData.ByteOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDOctetString:
                        WriteBytesOfOctetString(data, refData.ByteOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDUnsigned8Unsigned8:
                        DivideAndWriteUnsigned8Unsigned8(data, refData.ByteOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDFloat32Unsigned8:
                        DivideAndWriteFloat32Unsigned8(data, refData.ByteOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDBoolean:
                        WriteBitArea(
                            data,
                            refData.ByteOffset,
                            refData.BitOffset,
                            refData.BitLength,
                            (string)refData.DefaultValue == s_TrueString ? 1 : 0);
                        break;

                    case DataTypes.GSDDate:
                        WriteBytesOfDate(data, refData.ByteOffset, refData.DefaultValue);
                        break;
                    case DataTypes.GSDOctetString2Unsigned8:
                        DivideAndWriteOctetString2Unsigned8(data, refData.ByteOffset, refData.DefaultValue);
                        break;


                    default:
                        throw new InvalidOperationException(
                            string.Format(
                                CultureInfo.InvariantCulture,
                                "Not implemented data type: {0}",
                                refData.DataType));
                }
            }
        }

        public static void FillCustomRefs(
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordDataItem,
            ParameterRecordData parameterRecordData,
            byte[] parameterRecordDataBytes)
        {
            foreach (
                ParameterRecordDataItemsTypeParameterRecordDataItemRef customRef in customParameterRecordDataItem.Ref)
            {
                RefData refData = null;
                foreach (RefData refItem in parameterRecordData.Refs)
                {
                    if (customRef.ByteOffset == refItem.ByteOffset && (!customRef.BitOffsetSpecified || customRef.BitOffset == refItem.BitOffset))
                    {
                        refData = refItem;
                        break;
                    }
                }

                if (refData == null)
                {
                    throw new InvalidOperationException(
                        string.Format(
                            CultureInfo.InvariantCulture,
                            "Can not find ref with the given id: {0}",
                            customRef.ByteOffset));
                }

                switch (refData.DataType)
                {
                    case DataTypes.GSDBit:
                        WriteBit(parameterRecordDataBytes, refData.ByteOffset, refData.BitOffset, customRef.Value);
                        break;
                    case DataTypes.GSDBitArea:
                        WriteBitArea(
                            parameterRecordDataBytes,
                            refData.ByteOffset,
                            refData.BitOffset,
                            refData.BitLength,
                            customRef.Value);
                        break;
                    case DataTypes.GSDInteger8:
                    case DataTypes.GSDUnsigned8:
                    case DataTypes.GSDInteger16:
                    case DataTypes.GSDUnsigned16:
                    case DataTypes.GSDInteger32:
                    case DataTypes.GSDUnsigned32:
                    case DataTypes.GSDFloat32:
                    case DataTypes.GSDInteger64:
                    case DataTypes.GSDUnsigned64:
                    case DataTypes.GSDFloat64:
                    case DataTypes.Gsdn2:
                    case DataTypes.Gsdn4:
                    case DataTypes.Gsdr2:
                    case DataTypes.Gsdt2:
                    case DataTypes.Gsdt4:
                    case DataTypes.Gsdd2:
                    case DataTypes.Gsdx2:
                    case DataTypes.Gsdx4:
                        WriteBytes(parameterRecordDataBytes, refData.ByteOffset, customRef.Value, refData.DataType);
                        break;
                    case DataTypes.GSDVisibleString:
                        WriteBytesOfVisibleString(parameterRecordDataBytes, refData.ByteOffset, customRef.Value);
                        break;
                    case DataTypes.GSDOctetString:
                        WriteBytesOfOctetString(parameterRecordDataBytes, refData.ByteOffset, customRef.Value);
                        break;
                    case DataTypes.GSDUnsigned8Unsigned8:
                        DivideAndWriteUnsigned8Unsigned8(parameterRecordDataBytes, refData.ByteOffset, customRef.Value);
                        break;
                    case DataTypes.GSDFloat32Unsigned8:
                        DivideAndWriteFloat32Unsigned8(parameterRecordDataBytes, refData.ByteOffset, customRef.Value);
                        break;
                    case DataTypes.GSDBoolean:
                        WriteBitArea(
                            parameterRecordDataBytes,
                            refData.ByteOffset,
                            refData.BitOffset,
                            refData.BitLength,
                            customRef.Value == s_TrueString ? 1 : 0);
                        break;
                    case DataTypes.GSDDate:
                        WriteBytesOfDate(parameterRecordDataBytes, refData.ByteOffset, customRef.Value);
                        break;
                    case DataTypes.GSDOctetString2Unsigned8:
                        DivideAndWriteOctetString2Unsigned8(
                            parameterRecordDataBytes,
                            refData.ByteOffset,
                            customRef.Value);
                        break;
                    default:
                        throw new InvalidOperationException(
                            string.Format(
                                CultureInfo.InvariantCulture,
                                "Not implemented data type: {0}",
                                refData.DataType));
                }
            }
        }

        private static uint GetBitAreaMaximumValue(uint bitLength)
        {
            if (bitLength == 0)
            {
                throw new InvalidOperationException(
                    string.Format(CultureInfo.InvariantCulture, "Incorrect bitLength: {0}", bitLength));
            }

            if (bitLength > 0 && bitLength < 16)
            {
                return (uint)Math.Pow(2, bitLength) - 1;
            }

            throw new InvalidOperationException(
                string.Format(CultureInfo.InvariantCulture, "Incorrect bit length: {0}", bitLength));

        }

        private static void WriteBit(byte[] data, uint byteOffset, uint bitOffset, object value)
        {
            if (bitOffset > 7)
            {
                throw new InvalidOperationException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "Incorrect value for BitOffset while writing Bit: {0}",
                        bitOffset));
            }

            uint bitValue = uint.Parse(value.ToString(), CultureInfo.InvariantCulture);

            if (bitValue > 1)
            {
                throw new InvalidOperationException(
                    string.Format(CultureInfo.InvariantCulture, "Incorrect value for bit: {0}", bitValue));
            }

            byte writtenByte = data[byteOffset];

            if (bitValue == 0)
            {
                writtenByte &= (byte)~(1 << (int)bitOffset);
            }
            else
            {
                writtenByte |= (byte)(1 << (int)bitOffset);
            }

            data[byteOffset] = writtenByte;
        }

        private static void WriteBitArea(byte[] data, uint byteOffset, uint bitOffset, uint bitLength, object value)
        {
            if (bitLength > 15)
            {
                throw new InvalidOperationException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "Incorrect value for BitLength while writing BitArea: {0}",
                        bitLength));
            }

            if (bitOffset > 15)
            {
                throw new InvalidOperationException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "Incorrect value for BitOffset while writing BitArea: {0}",
                        bitOffset));
            }

            if (bitLength + bitOffset > 16)
            {
                throw new InvalidOperationException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "Incorrect BitLength and BitOffset value combination; BitLength: {0} - BitOffset: {1}",
                        bitLength,
                        bitOffset));
            }

            uint bitAreaValue = uint.Parse(value.ToString(), CultureInfo.InvariantCulture);

            uint maxBitAreaValue = GetBitAreaMaximumValue(bitLength);

            if (bitAreaValue > maxBitAreaValue)
            {
                throw new InvalidOperationException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "Incorrect value for BitArea; BitLength: {0} - MaxValue: {1} - Value: {2}",
                        bitLength,
                        maxBitAreaValue,
                        bitAreaValue));
            }

            BitArray dataBitArray = new BitArray(data);
            BitArray valueBitArray = new BitArray(new[] { (int)bitAreaValue });

            int startingBitIndex = (int)(byteOffset * 8 + bitOffset);

            for (int i = 0; i < bitLength; i++)
            {
                dataBitArray[startingBitIndex] = valueBitArray[i];
                startingBitIndex++;
            }

            dataBitArray.CopyTo(data, 0);
        }

        private static void WriteBytes(byte[] data, uint byteOffset, object value, DataTypes dataType)
        {
            byte[] byteValues;
            switch (dataType)
            {
                case DataTypes.GSDInteger8:
                case DataTypes.GSDUnsigned8:
                    byteValues = new[] { byte.Parse(value.ToString(), CultureInfo.InvariantCulture) };
                    break;
                case DataTypes.GSDInteger16:
                case DataTypes.Gsdn2:
                case DataTypes.Gsdx2:
                    byteValues = BitConverter.GetBytes(short.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDUnsigned16:
                case DataTypes.Gsdr2:
                case DataTypes.Gsdt2:
                case DataTypes.Gsdd2:
                    byteValues = BitConverter.GetBytes(ushort.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDInteger32:
                case DataTypes.Gsdn4:
                case DataTypes.Gsdx4:
                    byteValues = BitConverter.GetBytes(int.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDUnsigned32:
                case DataTypes.Gsdt4:
                    byteValues = BitConverter.GetBytes(uint.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDInteger64:
                    byteValues = BitConverter.GetBytes(long.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDUnsigned64:
                    byteValues = BitConverter.GetBytes(ulong.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDFloat32:
                    byteValues = BitConverter.GetBytes(float.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                case DataTypes.GSDFloat64:
                    byteValues = BitConverter.GetBytes(double.Parse(value.ToString(), CultureInfo.InvariantCulture));
                    break;
                default:
                    throw new InvalidOperationException(
                        string.Format(
                            CultureInfo.InvariantCulture,
                            "Unsupported type while writing bytes: {0}",
                            dataType));
            }

            byteValues = byteValues.Reverse().ToArray();

            foreach (byte byteValue in byteValues)
            {
                data[byteOffset] = byteValue;
                byteOffset++;
            }
        }

        private static void WriteBytesOfVisibleString(byte[] data, uint byteOffset, object value)
        {
            byte[] byteValues = Encoding.ASCII.GetBytes(value.ToString());
            foreach (byte byteValue in byteValues)
            {
                data[byteOffset] = byteValue;
                byteOffset++;
            }
        }

        private static void WriteBytesOfOctetString(byte[] data, uint byteOffset, object value)
        {
            string[] parts = value.ToString().Split(',');
            foreach (string element in parts)
            {
                data[byteOffset] = (byte)Convert.ToInt32(element, s_HexadecimalValue);
                byteOffset++;
            }
        }

        private static void DivideAndWriteUnsigned8Unsigned8(byte[] data, uint byteOffset, object value)
        {
            string[] parts = value.ToString().Split(',');
            WriteBytes(data, byteOffset, parts[0], DataTypes.GSDUnsigned8);
            WriteBytes(
                data,
                byteOffset + s_GSDUnsigned8Length,
                int.Parse(
                    parts[1].Substring(s_HexadecimalPartLength),
                    NumberStyles.HexNumber,
                    CultureInfo.InvariantCulture),
                DataTypes.GSDUnsigned8);
        }

        private static void DivideAndWriteFloat32Unsigned8(byte[] data, uint byteOffset, object value)
        {
            string[] parts = value.ToString().Split(',');
            WriteBytes(data, byteOffset, parts[0], DataTypes.GSDFloat32);
            WriteBytes(
                data,
                byteOffset + s_GSDFloat32Length,
                int.Parse(
                    parts[1].Substring(s_HexadecimalPartLength),
                    NumberStyles.HexNumber,
                    CultureInfo.InvariantCulture),
                DataTypes.GSDUnsigned8);
        }

        private static void DivideAndWriteOctetString2Unsigned8(byte[] data, uint byteOffset, object value)
        {
            value = value.ToString().Replace(" ", "");
            string[] octetParts = value.ToString().Split(',');
            uint indexForOctet = 0;
            foreach (string octetPart in octetParts)
            {
                WriteBytes(
                    data,
                    byteOffset + indexForOctet,
                    int.Parse(
                        octetPart.Substring(s_HexadecimalPartLength),
                        NumberStyles.HexNumber,
                        CultureInfo.InvariantCulture),
                    DataTypes.GSDUnsigned8);
                indexForOctet += s_GSDUnsigned8Length;
            }
        }

        private static void WriteBytesOfDate(byte[] data, uint byteOffset, object value)
        {
            value = value.ToString().Replace('T', ' ');
            string[] dateParts = value.ToString().Split(' ');
            CheckDateFormat(dateParts, 1, 2, value.ToString());
            string yearMonthDay = dateParts[0];
            string[] yearMonthDayParts = yearMonthDay.Split('-');
            CheckDateFormat(yearMonthDayParts, 3, 3, value.ToString());
            int year = Convert.ToInt16(yearMonthDayParts[0], CultureInfo.InvariantCulture);
            int month = Convert.ToInt16(yearMonthDayParts[1], CultureInfo.InvariantCulture);
            int day = Convert.ToInt16(yearMonthDayParts[2], CultureInfo.InvariantCulture);
            string hourMinuteSecond;
            if (dateParts.Length == 2)
            {
                hourMinuteSecond = dateParts[1];
            }
            else
            {
                hourMinuteSecond = "00:00:00.000";
            }

            string[] hourMinuteSecondParts = hourMinuteSecond.Split(':');
            CheckDateFormat(hourMinuteSecondParts, 3, 3, value.ToString());
            int hour = Convert.ToInt16(hourMinuteSecondParts[0], CultureInfo.InvariantCulture);
            int minute = Convert.ToInt16(hourMinuteSecondParts[1], CultureInfo.InvariantCulture);
            string[] secondMillisecondParts = hourMinuteSecondParts[2].Split('.');
            CheckDateFormat(secondMillisecondParts, 1, 2, value.ToString());
            int second = Convert.ToInt16(secondMillisecondParts[0], CultureInfo.InvariantCulture);
            int millisec = 0;
            if (secondMillisecondParts.Length == 2)
            {
                millisec = Convert.ToInt16(secondMillisecondParts[1], CultureInfo.InvariantCulture)
                           * (int)(Math.Pow(10, 3 - secondMillisecondParts[1].Length));
            }



            DateTime dateTimeTmp = new DateTime(year, month, day, hour, minute, second);
            DateTime dateTime = dateTimeTmp.AddMilliseconds(millisec);

            const int dataTypeLength = 7;
            byte[] binary = new byte[dataTypeLength];


            int milliseconds = dateTime.Millisecond + second * 1000;
            int dayOfWeek = (int)dateTime.DayOfWeek;
            int dayOfMonth = dateTime.Day;

            bool dst = dateTime.IsDaylightSavingTime();
            if (dst)
            {
                hour += 1 << 7;
            }

            int convertedYear = (int)((decimal)year % 100);

            byte[] millisecondsBytes = BitConverter.GetBytes((ushort)milliseconds);

            binary[0] = millisecondsBytes[0];
            binary[1] = millisecondsBytes[1];
            binary[2] = (byte)minute;
            binary[3] = (byte)hour;
            binary[4] = (byte)dayOfMonth;
            binary[4] += (byte)(dayOfWeek << 5);
            binary[5] = (byte)month;
            binary[6] = (byte)convertedYear;

            foreach (byte part in binary)
            {
                data[byteOffset] = part;
                byteOffset++;
            }
        }

        private static void CheckDateFormat(string[] datePart, int minLength, int maxLength, string dateValue)
        {
            if (datePart.Length < minLength || datePart.Length > maxLength)
            {
                throw new InvalidOperationException(
                    string.Format(CultureInfo.InvariantCulture, "Incorrect date value for : {0}", dateValue));
            }
        }
    }
}