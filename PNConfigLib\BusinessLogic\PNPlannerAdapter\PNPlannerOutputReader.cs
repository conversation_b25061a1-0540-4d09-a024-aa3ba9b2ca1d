/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerOutputReader.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

using PNConfigLib.BusinessLogic.PNPlannerAdapter.DataClasses;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.PNPlannerAdapter.DataClasses;
using PNConfigLib.Gsd.Interpreter.Common;

#endregion

namespace PNConfigLib.PNPlannerAdapter
{
    internal class BeginEndAssignments
    {
        public BeginEndAssignments(Dictionary<int, Dictionary<int, IPNPlannerOutputBeginEndAssignment>> beginEndAssignmentsOfAllPorts)
        {
            BeginEndAssignmentsOfAllPorts = beginEndAssignmentsOfAllPorts;
        }

        public Dictionary<int, Dictionary<int, IPNPlannerOutputBeginEndAssignment>> BeginEndAssignmentsOfAllPorts { get; }
    }

    internal class PhaseAssignments
    {
        public PhaseAssignments(Dictionary<int, Dictionary<int, IPNPlannerOutputPhaseAssignment>> phaseAssignmentsOfAllPorts)
        {
            PhaseAssignmentsOfAllPorts = phaseAssignmentsOfAllPorts;
        }

        public Dictionary<int, Dictionary<int, IPNPlannerOutputPhaseAssignment>> PhaseAssignmentsOfAllPorts { get; }
    }

    internal class PNPlannerOutputReader
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public PNPlannerOutputReader(SyncDomainBusinessLogic syncDomainBusinessLogic, PNPlannerHelper pnPlannerHelper)
        {
            m_SyncDomainBusinessLogic = syncDomainBusinessLogic;
            m_PlannerHelper = pnPlannerHelper;
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions
        private class PNPlannerException
        {
            #region Fields

            private long m_FrameId;

            private long m_StationNo;

            private string m_SlotNo;

            private string m_SubSlotNo;

            private string m_AddInfo;

            private long m_No;

            private string m_Type;

            private string m_Message;

            #endregion

            #region Properties

            public long No
            {
                get { return m_No; }
                set { m_No = value; }
            }

            public string Type
            {
                get { return m_Type; }
                set { m_Type = value; }
            }

            public long FrameId
            {
                get { return m_FrameId; }
                set { m_FrameId = value; }
            }

            public long StationNo
            {
                get { return m_StationNo; }
                set { m_StationNo = value; }
            }

            public string SlotNo
            {
                get { return m_SlotNo; }
                set { m_SlotNo = value; }
            }

            public string SubSlotNo
            {
                get { return m_SubSlotNo; }
                set { m_SubSlotNo = value; }
            }

            public string AddInfo
            {
                get { return m_AddInfo; }
                set { m_AddInfo = value; }
            }

            public string Message
            {
                get { return m_Message; }
                set { m_Message = value; }
            }

            #endregion

            #region Overrides of Object

            public override string ToString()
            {
                string message = string.Format(
                    CultureInfo.InvariantCulture,
                    "PNPlanner: {0}. No: {1}. SlotNo: {2}. StationNo: {3}. SubSlotNo: {4}. Type: {5}. FrameId: {6}. AddInfo: {7}",
                    Message.Replace("Error: ", string.Empty),
                    No,
                    SlotNo,
                    StationNo,
                    SubSlotNo,
                    Type,
                    FrameId,
                    AddInfo);
                return message;
            }

            #endregion
        }

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private const int ProfinetEthertype = 34962;

        private readonly SyncDomainBusinessLogic m_SyncDomainBusinessLogic;

        private readonly PNPlannerHelper m_PlannerHelper;

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        /// The main method responsible for reading the PNPlanner output XML file.
        /// Reads the XML file passed as a byte array and updates the corresponding Frame references and results such
        /// as bandwidth usage.
        /// </summary>
        /// <param name="outputXmlBuffer">Output XML passed as a byte array</param>
        /// <param name="bufferSize">Size of the output buffer</param>
        /// <param name="controllersWithFrames">
        /// A dictionary that holds the controller interfaces and their
        /// corresponding Frames.
        /// </param>
        /// <param name="interfacesOfSwitchNames"></param>
        public void Read(
            byte[] outputXmlBuffer,
            int bufferSize,
            InterfaceFrameDataList controllersWithFrames,
            Dictionary<string, Interface> interfacesOfSwitchNames)
        {
            List<PNPlannerOutputFrame> outputFrames = new List<PNPlannerOutputFrame>();

            // Uncomment the following using statement if you want to have PNPlanner output written to a temp file
            //using (var strReader = new StreamReader(new MemoryStream(outputXmlBuffer, 0, bufferSize)))
            //{
            //    string fileContents = strReader.ReadToEnd();
            //    string tempPath = Path.GetTempFileName();
            //    Console.WriteLine($"PNPlanner output path: {tempPath}");
            //    File.WriteAllText(tempPath, fileContents, System.Text.Encoding.UTF8);
            //}

            using (XmlTextReader xmlReader = new SecureXmlTextReader(new MemoryStream(outputXmlBuffer, 0, bufferSize)))
            {
                while (xmlReader.Read())
                {
                    switch (xmlReader.Name)
                    {
                        case PNPlannerConstants.m_XmlControllersCaption:
                            ReadControllers(xmlReader, controllersWithFrames);
                            break;
                        case PNPlannerConstants.m_XmlFramesCaption:
                            outputFrames = ReadFrames(xmlReader);
                            UpdateFrameReferences(controllersWithFrames, outputFrames);
                            break;
                        case PNPlannerConstants.m_XmlIRTCommonValues:
                            ReadIRTCommonValues(xmlReader);
                            break;
                        case PNPlannerConstants.m_XmlIrtSwitchesCaption:
                            ReadIrtSwitches(xmlReader, outputFrames, interfacesOfSwitchNames);
                            break;
                        case PNPlannerConstants.m_XmlTxRxPeriodsByPhaseCaption:
                            ReadTxRxPeriodsByPhase(xmlReader, controllersWithFrames);
                            break;
                        case PNPlannerConstants.m_XmlOutputFingerprintCaption:
                            ReadOutputFingerPrint(xmlReader);
                            break;
                    }
                }
            }
        }

        public bool ReadPNPlannerErrorXml(byte[] errorXmlBuffer, int bufferSize)
        {
            // Uncomment the following using statement if you want to have PNPlanner error written to a temp file
            //using (var strReader = new StreamReader(new MemoryStream(errorXmlBuffer, 0, bufferSize)))
            //{
            //    string fileContents = strReader.ReadToEnd();
            //    string tempPath = Path.GetTempFileName();
            //    Console.WriteLine($"PNPlanner error path: {tempPath}");
            //    File.WriteAllText(tempPath, fileContents, System.Text.Encoding.UTF8);
            //}

            bool isSuccess = true;
            using (XmlTextReader xmlReader = new SecureXmlTextReader(new MemoryStream(errorXmlBuffer, 0, bufferSize)))
            {
                while (xmlReader.Read())
                {
                    if ((xmlReader.Name == PNPlannerConstants.m_XmlResultLogCaption)
                        && (xmlReader.NodeType == XmlNodeType.Element))
                    {
                        if (!ReadLog(xmlReader))
                        {
                            isSuccess = false;
                        }
                    }
                }
            }
            return isSuccess;
        }

        #endregion

        //########################################################################################

        #region  Private Implementations 

        /// <summary>
        /// Reads the "Controllers" section of the XML Output file which reads each "Controller" element consecutively.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <param name="controllersWithFrames">
        /// >A dictionary that holds the controller interfaces and their
        /// corresponding Frames.
        /// </param>
        private void ReadControllers(
            XmlTextReader xmlReader,
            InterfaceFrameDataList controllersWithFrames)
        {
            while (xmlReader.Read())
            {
                if ((xmlReader.NodeType == XmlNodeType.Element)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlControllerCaption))
                {
                    ReadController(xmlReader, controllersWithFrames);
                }

                if ((xmlReader.NodeType == XmlNodeType.EndElement)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlControllersCaption))
                {
                    break;
                }
            }
        }

        /// <summary>
        /// Reads the "Controller" element under the "Controllers" section. Updates the RT Bandwidth and Sendclock factor
        /// associated with this controller in BL.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <param name="controllersWithFrames">
        /// >A dictionary that holds the controller interfaces and their
        /// corresponding Frames.
        /// </param>
        private void ReadController(
            XmlTextReader xmlReader,
            InterfaceFrameDataList controllersWithFrames)
        {
            string controllerName = xmlReader.GetAttribute(PNPlannerConstants.m_XmlControllerNameCaption);
            Interface controllerInterface = FindController(controllerName, controllersWithFrames);

            string scfString = xmlReader.GetAttribute(PNPlannerConstants.m_XmlSendClockFactorCaption);
            if (scfString != null)
            {
                long sendClockFactor = long.Parse(scfString, CultureInfo.InvariantCulture);
                AttributeUtilities.SetTransientPNSendClockFactor(controllerInterface, sendClockFactor);
            }

            string rtBwString = xmlReader.GetAttribute(PNPlannerConstants.m_XmlRtBandwidthCaption);
            if (rtBwString == null)
            {
                return;
            }
            long pnPlannerRtBandwidth = long.Parse(rtBwString, CultureInfo.InvariantCulture);
            m_SyncDomainBusinessLogic.PNPlannerResults.CalculatedPartRt = pnPlannerRtBandwidth;

            string possibleSendclockFactors = xmlReader.GetAttribute(PNPlannerConstants.PossibleSendClockFactorsText);
            if (possibleSendclockFactors != null)
            {
                string[] possibleScfArray = possibleSendclockFactors.Split(' ');
                List<long> possibleSendClockFactorsLong = new List<long>();

                foreach (string possibleSendClockFactor in possibleScfArray)
                {
                    possibleSendClockFactorsLong.Add(Convert.ToInt64(possibleSendClockFactor, CultureInfo.InvariantCulture));
                }

                m_SyncDomainBusinessLogic.PossibleSendClockFactors.Add(controllerInterface, possibleSendClockFactorsLong);
            }

            //Possible sendclock factors are not read since they are not used for PNConfigLib.
        }

        /// <summary>
        /// Reads the "Frames" section of the XML Output file which reads each "Frame" element consecutively.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <returns>Frame objects read from the output XML along with DFP subframes if present.</returns>
        private List<PNPlannerOutputFrame> ReadFrames(XmlTextReader xmlReader)
        {
            List<PNPlannerOutputFrame> outputFrames = new List<PNPlannerOutputFrame>();
            HashSet<int> msgIds = new HashSet<int>();
            while (xmlReader.Read())
            {
                if ((xmlReader.NodeType == XmlNodeType.EndElement)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlFramesCaption))
                {
                    break;
                }

                if ((xmlReader.NodeType != XmlNodeType.Element)
                    || (xmlReader.Name != PNPlannerConstants.m_XmlFrameCaption))
                {
                    continue;
                }

                PNPlannerOutputFrame frame = ReadFrame(xmlReader);
                if (msgIds.Contains(frame.FrameID))
                {
                    // Each frame should occur only once. Silently skip if it is read more than once
                    continue;
                }

                msgIds.Add(frame.FrameID);

                outputFrames.Add(frame);
            }

            return outputFrames;
        }

        /// <summary>
        /// Reads the "Frame" element under the "Frames" section and then fills the Frame properties such as new FrameID,
        /// Controller/Device Phase and Reduction ratios and SendClockFactor associated with this frame for a
        /// PNPlannerOutputFrame object.
        /// Notes:
        /// Length and Padding information are used for IRT related frames but are being filled for future usage.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <returns>Frame object</returns>
        private PNPlannerOutputFrame ReadFrame(XmlTextReader xmlReader)
        {
            PNPlannerOutputFrame outputFrame = new PNPlannerOutputFrame();
            bool hasChildrenElements = !xmlReader.IsEmptyElement;
            while (xmlReader.MoveToNextAttribute())
            {
                switch (xmlReader.Name)
                {
                    case PNPlannerConstants.m_XmlFrameIdCaption:
                        outputFrame.FrameID = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_InputMsgId:
                    case PNPlannerConstants.m_InputFrameId:
                        outputFrame.OriginalFrameID = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlRedundantFrameIdCaption:
                        outputFrame.RedundantFrameID = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlControllerLocalReductionRatioCaption:
                        outputFrame.ControllerLocalReductionRatio = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlDeviceLocalReductionRatioCaption:
                        outputFrame.DeviceLocalReductionRatio = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlControllerLocalPhaseCaption:
                        outputFrame.ControllerLocalPhase = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        outputFrame.Phase = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlDeviceLocalPhaseCaption:
                        outputFrame.DeviceLocalPhase = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlLengthCaption:
                        outputFrame.Length = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlPaddingCaption:
                        outputFrame.Padding = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlPossibleReductionRatiosCaption:
                        string possibleReductionRatios = xmlReader.Value;
                        SetPossibleReductionRatios(possibleReductionRatios, outputFrame);

                        break;
                    case PNPlannerConstants.m_XmlSendClockFactorCaption:
                        outputFrame.SendclockFactor = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                        break;
                    case PNPlannerConstants.m_XmlSFCRC16Caption:
                        outputFrame.SfCrc16 = bool.Parse(xmlReader.Value);
                        break;
                }
            }

            // Attributes are read.
            if (!hasChildrenElements)
            {
                return outputFrame;
            }

            // Start reading DfpFrame elements
            while (xmlReader.Read())
            {
                // End of the frame. Return.
                if (xmlReader.NodeType == XmlNodeType.EndElement && xmlReader.Name == PNPlannerConstants.m_XmlFrameCaption)
                {
                    break;
                }

                if (xmlReader.NodeType != XmlNodeType.Element
                    || xmlReader.Name != PNPlannerConstants.m_XmlDfpFrameCaption)
                {
                    continue;
                }

                IPNPlannerOutputSubframe subframe = ReadDfpFrame(xmlReader);
                if (outputFrame.Subframes == null)
                {
                    outputFrame.Subframes = new List<IPNPlannerOutputSubframe>();
                }
                outputFrame.Subframes.Add(subframe);
            }

            return outputFrame;
        }

        private static void SetPossibleReductionRatios(
            string possibleReductionRatios,
            IPNPlannerOutputFrame outputFrame)
        {
            if (string.IsNullOrEmpty(possibleReductionRatios))
            {
                return;
            }

            string[] possibleReductionRatioStrings = possibleReductionRatios.Split(' ');
            if (possibleReductionRatios == "")
            {
                return;
            }

            foreach (string possibleReductionRatioString in possibleReductionRatioStrings)
            {
                outputFrame.PossibleReductionRatios.Add(
                    long.Parse(possibleReductionRatioString, CultureInfo.InvariantCulture));
            }
        }

        /// <summary>
        /// Reads DfpFrame element (/IRT_PlanningOutput/frames/frame/DfpFrame)
        /// </summary>
        /// <param name="xmlReader"></param>
        /// <returns>PNPlannerOutputSubframe instance containing the required information of DfpFrame element</returns>
        private static PNPlannerOutputSubframe ReadDfpFrame(XmlReader xmlReader)
        {
            PNPlannerOutputSubframe subframe = new PNPlannerOutputSubframe();
            string attr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlSFIdCaption);
            Debug.Assert(!string.IsNullOrEmpty(attr), "DfpFrame SFId cannot be retrieved");
            // ReSharper disable AssignNullToNotNullAttribute
            subframe.SubframeId = int.Parse(attr, CultureInfo.InvariantCulture);

            attr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlPackSize);
            Debug.Assert(!string.IsNullOrEmpty(attr), "DfpFrame Length cannot be retrieved");
            subframe.Length = int.Parse(attr, CultureInfo.InvariantCulture);

            attr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlSFLengthCaption);
            Debug.Assert(!string.IsNullOrEmpty(attr), "DfpFrame SFLength cannot be retrieved");
            subframe.SFLength = int.Parse(attr, CultureInfo.InvariantCulture);

            attr = xmlReader.GetAttribute(PNPlannerConstants.m_InputMsgId);
            Debug.Assert(!string.IsNullOrEmpty(attr), "DfpFrame InputMsgId cannot be retrieved");
            subframe.MsgId = int.Parse(attr, CultureInfo.InvariantCulture);

            attr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlPaddingCaption);
            Debug.Assert(!string.IsNullOrEmpty(attr), "DfpFrame Padding cannot be retrieved");
            subframe.Padding = int.Parse(attr, CultureInfo.InvariantCulture);
            // ReSharper restore AssignNullToNotNullAttribute
            return subframe;
        }

        /// <summary>
        /// General IRT related results are being read in CalculatedValues  here. These results are:
        ///     - IRT Startup Mode
        ///     - Fast Forwarding
        ///     - Frame Preamble Length
        ///     - IRT Bandwidth
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        private void ReadIRTCommonValues(XmlTextReader xmlReader)
        {
            m_PlannerHelper.StartupMode = xmlReader.GetAttribute(PNPlannerConstants.m_XmlIrtStartupModeCaption);
            if (string.IsNullOrEmpty(m_PlannerHelper.StartupMode))
            {
                Debug.Fail("IRT Startup mode cannot be retrieved from PNPlanner");
            }

            string attrVal = xmlReader.GetAttribute(PNPlannerConstants.m_XmlFastForwardingCaption);
            if (!bool.TryParse(attrVal, out m_PlannerHelper.FastForwarding))
            {
                Debug.Fail("Calculated value for FastForwarding cannot be retrieved.");
            }

            attrVal = xmlReader.GetAttribute(PNPlannerConstants.m_XmlFramePreambleCaption);
            if (!uint.TryParse(
                    attrVal,
                    NumberStyles.Integer,
                    CultureInfo.InvariantCulture,
                    out m_PlannerHelper.FramePreamble))
            {
                Debug.Fail("Calculated value for FastForwarding cannot be retrieved.");
            }

            attrVal = xmlReader.GetAttribute(PNPlannerConstants.m_XmlRedundancyExistsCaption);
            if (!bool.TryParse(attrVal, out m_PlannerHelper.RedundancyExists))
            {
                Debug.Fail("Calculated value for RedundancyExists cannot be retrieved.");
            }

            attrVal = xmlReader.GetAttribute(PNPlannerConstants.m_XmlIrtBandwidthCaption);
            long irtBandwidth;
            if (!long.TryParse(attrVal, NumberStyles.Integer, CultureInfo.InvariantCulture, out irtBandwidth))
            {
                Debug.Fail("Calculated value for IRTBandwidth cannot be retrieved.");
            }

            m_SyncDomainBusinessLogic.PNPlannerResults.CalculatedPartIrt = irtBandwidth;
        }

        /// <summary>
        /// Reads the "IRTSwitches" section of the PNPlanner Output XML file. This section contains the RED port
        /// activity for each IRT Switch of the Sync Domain.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <param name="outputFrames">Messages read from PNPlanner output</param>
        /// <param name="interfacesOfSwitchNames"></param>
        private void ReadIrtSwitches(XmlTextReader xmlReader, List<PNPlannerOutputFrame> outputFrames, Dictionary<string, Interface> interfacesOfSwitchNames)
        {
            Interface interfaceSubmodule;

            while (xmlReader.Read())
            {
                if ((xmlReader.NodeType == XmlNodeType.EndElement)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlIrtSwitchesCaption))
                {
                    break;
                }

                if ((xmlReader.NodeType != XmlNodeType.Element)
                    && (xmlReader.Name != PNPlannerConstants.m_XmlIrtSwitchCaption))
                {
                    continue;
                }

                HashSet<PortElement> irtTopPortsOfInterface = new HashSet<PortElement>();
                interfaceSubmodule = ReadIrtSwitch(xmlReader, outputFrames, interfacesOfSwitchNames, irtTopPortsOfInterface);

                if (interfaceSubmodule != null
                    && irtTopPortsOfInterface.Count > 0)
                {
                    foreach (PortElement pe in irtTopPortsOfInterface)
                    {
                        DataModel.PCLObjects.Port currentPort = NavigationUtilities.GetPortModules(interfaceSubmodule).FirstOrDefault(p => p.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.UniquePortNumber, new AttributeAccessCode(), 0) + 1 == pe.Nr);
                        if (currentPort != null)
                        {
                            currentPort.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnIrtUsingShortPreamble, pe.UsingShortPreamble);
                        }
                    }

                    m_SyncDomainBusinessLogic.PNPlannerResults.AllIrtTopPorts.Add(interfaceSubmodule.GetHashCode(), irtTopPortsOfInterface);
                }
            }
        }

        /// <summary>
        /// Reads the IRT Switch
        /// </summary>
        /// <param name="xmlReader"></param>
        /// <param name="outputFrames"></param>
        /// <param name="interfacesOfSwitchNames"></param>
        /// <param name="irtTopPortsOfInterface"></param>
        private Interface ReadIrtSwitch(XmlTextReader xmlReader, List<PNPlannerOutputFrame> outputFrames, Dictionary<string, Interface> interfacesOfSwitchNames, HashSet<PortElement> irtTopPortsOfInterface)
        {
            Interface interfaceSubmodule;
            // Key = port nr (0-based), value = LineRxDelay for that port
            Dictionary<int, int> lineRxDelays = new Dictionary<int, int>();

            //Get Switch Name
            string switchName = xmlReader.GetAttribute(PNPlannerConstants.m_XmlNameCaption);
            if (string.IsNullOrEmpty(switchName)
                || !interfacesOfSwitchNames.ContainsKey(switchName))
            {
                return null;
            }

            interfaceSubmodule = interfacesOfSwitchNames[switchName];

            int bridgeDelay;
            string actualBridgingDelay = xmlReader.GetAttribute(PNPlannerConstants.m_XmlCalculatedBridgeDelayCaption);
            if (string.IsNullOrEmpty(actualBridgingDelay))
            {
                bridgeDelay = interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnIrtSwitchBridgingDelay, new AttributeAccessCode(), 0);
            }
            else
            {
                bridgeDelay = int.Parse(actualBridgingDelay, CultureInfo.InvariantCulture);
            }

            interfaceSubmodule.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnIrtSwitchActualBridgingDelay, (uint)bridgeDelay);

            Dictionary<int, PNPlannerOutputFrame> outputFrameDict = FormFrameDictionary(outputFrames);
            m_SyncDomainBusinessLogic.PNPlannerResults.Config2008FramesBlocks[switchName] = new List<IPNPlannerOutputFrame>();
            m_SyncDomainBusinessLogic.PNPlannerResults.Config2008BeginEndAssigmentBlocks[switchName] = new Dictionary<int, List<IPNPlannerOutputBeginEndAssignment>>();
            m_SyncDomainBusinessLogic.PNPlannerResults.Config2008PhaseAssigmentBlocks[switchName] = new Dictionary<int, List<IPNPlannerOutputPhaseAssignment>>();

            int rxPortNumber = 0;

            while (xmlReader.Read())
            {
                if ((xmlReader.NodeType == XmlNodeType.EndElement)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlIrtSwitchCaption))
                {
                    m_SyncDomainBusinessLogic.PNPlannerResults.Config2008MaxLineRxDelays.Add(switchName, lineRxDelays);
                    break;
                }

                if (xmlReader.NodeType != XmlNodeType.Element)
                {
                    continue;
                }

                switch (xmlReader.Name)
                {
                    case PNPlannerConstants.m_XmlPortCaption:
                        string portNr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlNumberCaption);
                        if (string.IsNullOrEmpty(portNr))
                        {
                            return interfaceSubmodule;
                        }

                        PortElement irtPort = new PortElement(int.Parse(portNr, CultureInfo.InvariantCulture) + 1);
                        string usingShortPreambleStr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlUsingShortPreambleCaption);
                        bool usingShortPreamble;
                        if (!string.IsNullOrEmpty(usingShortPreambleStr)
                            && bool.TryParse(usingShortPreambleStr, out usingShortPreamble))
                        {
                            irtPort.UsingShortPreamble = usingShortPreamble;
                        }

                        irtTopPortsOfInterface.Add(irtPort);
                        break;
                    case PNPlannerConstants.m_XmlRxPortCaption:
                        string rxPortNumStr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlNumberCaption);
                        if (!int.TryParse(
                                rxPortNumStr,
                                NumberStyles.Integer,
                                CultureInfo.InvariantCulture,
                                out rxPortNumber))
                        {
                            return interfaceSubmodule;
                        }

                        break;
                    case PNPlannerConstants.m_XmlRxPortMsgCaption:
                        ReadMsg(xmlReader, switchName, rxPortNumber, outputFrameDict);
                        break;
                    case PNPlannerConstants.m_XmlTxPortMsgCaption:
                        ReadMsg(xmlReader, switchName, -1, outputFrameDict);
                        break;
                    case PNPlannerConstants.m_XmlRedBeginEndGroupCaption:
                        ReadRedBeginEndAssignment(xmlReader, switchName);
                        break;
                    case PNPlannerConstants.m_XmlLineRxDelayCaption:
                        string delayStr = xmlReader.GetAttribute(PNPlannerConstants.m_XmlDelayCaption);
                        int delay;
                        if (!int.TryParse(delayStr, NumberStyles.Integer, CultureInfo.InvariantCulture, out delay))
                        {
                            return interfaceSubmodule;
                        }

                        lineRxDelays.Add(rxPortNumber + 1, delay);
                        break;
                    case PNPlannerConstants.m_XmlLocalRTC3PeriodCaption:
                        string end = xmlReader.GetAttribute(PNPlannerConstants.m_XmlEndCaption);
                        long endLong;
                        if (!long.TryParse(end, NumberStyles.Integer, CultureInfo.InvariantCulture, out endLong))
                        {
                            return interfaceSubmodule;
                        }

                        m_SyncDomainBusinessLogic.PNPlannerResults.LocalRxPeriodNoSyncValues.Add(interfaceSubmodule, endLong);
                        break;
                }
            }

            return interfaceSubmodule;
        }

        /// <summary>
        /// Forms a dictionary of output frames as FrameID is the key and the Frame itself is the value using the List
        /// of frames
        /// </summary>
        /// <param name="outputFrames">List of frames</param>
        /// <returns>Dictionary where key is frame id and value is the frame itself.</returns>
        private Dictionary<int, PNPlannerOutputFrame> FormFrameDictionary(List<PNPlannerOutputFrame> outputFrames)
        {
            Dictionary<int, PNPlannerOutputFrame> frameDictionary = new Dictionary<int, PNPlannerOutputFrame>();
            foreach (PNPlannerOutputFrame outputFrame in outputFrames)
            {
                if (!frameDictionary.ContainsKey(outputFrame.FrameID))
                {
                    frameDictionary.Add(outputFrame.FrameID, outputFrame);
                }
            }

            return frameDictionary;
        }

        private void ReadMsg(
            XmlReader pnPlannerReader,
            string switchName,
            int rxPortNumber,
            IDictionary<int, PNPlannerOutputFrame> messages)
        {
            int frameId = 0;
            PNPlannerOutputFrame outputFrame = new PNPlannerOutputFrame();

            string attrVal = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlFrameIdCaption);
            if (!string.IsNullOrEmpty(attrVal))
            {
                frameId = int.Parse(attrVal, CultureInfo.InvariantCulture);
                outputFrame.FrameID = frameId;
            }

            int sfId = 0;
            attrVal = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlSFIdCaption);
            if (!string.IsNullOrEmpty(attrVal))
            {
                sfId = int.Parse(attrVal, CultureInfo.InvariantCulture);
            }

            attrVal = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlTimeCaption);
            if (!string.IsNullOrEmpty(attrVal))
            {
                outputFrame.FrameSendOffset = uint.Parse(attrVal, CultureInfo.InvariantCulture);
            }

            attrVal = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlLocalCaption);
            if (!string.IsNullOrEmpty(attrVal)
                && bool.Parse(attrVal))
            {
                outputFrame.Local = 1;
            }

            attrVal = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlTxPortsCaption);
            if (!string.IsNullOrEmpty(attrVal))
            {
                IEnumerable<int> ports = GetIntList(attrVal);
                foreach (int port in ports)
                {
                    outputFrame.Ports.Add(port + 1);
                }
            }

            outputFrame.EtherType = ProfinetEthertype;
            outputFrame.RxPort = rxPortNumber + 1;

            // Get the other attributes from the original frame
            if (messages.ContainsKey(frameId))
            {
                SetOtherFrameAttributes(messages, frameId, sfId, outputFrame);
            }

            m_SyncDomainBusinessLogic.PNPlannerResults.Config2008FramesBlocks[switchName].Add(outputFrame);
        }

        private static void SetOtherFrameAttributes(
            IDictionary<int, PNPlannerOutputFrame> messages,
            int frameId,
            int sfId,
            PNPlannerOutputFrame outputFrame)
        {
            PNPlannerOutputFrame originalFrame = messages[frameId];

            if (originalFrame.Subframes != null
                && sfId != 0)
            {
                // Find the subframe to get the length and padding
                bool subframeFound = false;
                foreach (IPNPlannerOutputSubframe subframe in originalFrame.Subframes)
                {
                    if (subframe.SubframeId != sfId)
                    {
                        continue;
                    }

                    subframeFound = true;
                    outputFrame.Length = subframe.Length;
                    outputFrame.Padding = subframe.Padding;
                    break;
                }

                if (!subframeFound)
                {
                    Debug.Fail("Original subframe cannot be retrieved.");
                    // ReSharper disable HeuristicUnreachableCode
                    outputFrame.Length = originalFrame.Length;
                    outputFrame.Padding = originalFrame.Padding;
                    // ReSharper restore HeuristicUnreachableCode
                }
            }
            else
            {
                outputFrame.Length = originalFrame.Length;
                outputFrame.Padding = originalFrame.Padding;
            }

            outputFrame.Phase = originalFrame.Phase;
            outputFrame.ReductionRatio = originalFrame.ControllerLocalReductionRatio;
            outputFrame.SyncFrame = originalFrame.SyncFrame;
            outputFrame.RedundantFrameID = originalFrame.RedundantFrameID;
        }

        private static IEnumerable<int> GetIntList(string intListString)
        {
            List<int> intList = new List<int>(16);
            string[] intStrings = intListString.Split(',');
            foreach (string intString in intStrings)
            {
                intList.Add(int.Parse(intString, CultureInfo.InvariantCulture));
            }

            return intList;
        }

        private void ReadRedBeginEndAssignment(XmlTextReader pnPlannerReader, string deviceName)
        {
            // Outer hashtable: key = portNumber, value = inner hashtable.
            // Inner hashtable: key = AssignmentNumber, value = BeginEndAssignment
            BeginEndAssignments beginEndAssignmentsOfAllPorts =
                new BeginEndAssignments(new Dictionary<int, Dictionary<int, IPNPlannerOutputBeginEndAssignment>>());

            // Outer hashtable: key = portNumber, value = inner hashtable.
            // Inner hashtable: key = phase number, value = PhaseAssignment
            PhaseAssignments phaseAssignmentsOfAllPorts =
                new PhaseAssignments(new Dictionary<int, Dictionary<int, IPNPlannerOutputPhaseAssignment>>());

            int portNumber = 0;

            while (pnPlannerReader.Read())
            {
                if ((pnPlannerReader.NodeType == XmlNodeType.EndElement)
                    && (pnPlannerReader.Name == PNPlannerConstants.m_XmlRedBeginEndGroupCaption))
                {
                    break;
                }

                if (pnPlannerReader.NodeType != XmlNodeType.Element)
                {
                    continue;
                }

                switch (pnPlannerReader.Name)
                {
                    case PNPlannerConstants.m_XmlPortCaption:
                        string portNumStr = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlNumberCaption);
                        if (!int.TryParse(
                                portNumStr,
                                NumberStyles.Integer,
                                CultureInfo.InvariantCulture,
                                out portNumber))
                        {
                            return;
                        }

                        break;

                    case PNPlannerConstants.m_XmlRxPeriodGroupCaption:
                        ReadPeriodGroup(
                            false,
                            pnPlannerReader,
                            beginEndAssignmentsOfAllPorts,
                            portNumber,
                            phaseAssignmentsOfAllPorts);
                        break;

                    case PNPlannerConstants.m_XmlTxPeriodGroupCaption:
                        ReadPeriodGroup(
                            true,
                            pnPlannerReader,
                            beginEndAssignmentsOfAllPorts,
                            portNumber,
                            phaseAssignmentsOfAllPorts);
                        break;
                }
            }

            //Set BeginEndAssignmentBlocks
            foreach (KeyValuePair<int, Dictionary<int, IPNPlannerOutputBeginEndAssignment>> beginEndAssignmentPair in
                beginEndAssignmentsOfAllPorts.BeginEndAssignmentsOfAllPorts)
            {
                // Key is the portnumber starting from 0, value is the beginEndAssignments of that port
                m_SyncDomainBusinessLogic.PNPlannerResults.Config2008BeginEndAssigmentBlocks[deviceName].Add(
                    beginEndAssignmentPair.Key + 1,
                    new List<IPNPlannerOutputBeginEndAssignment>());
                foreach (IPNPlannerOutputBeginEndAssignment beginEndAssignmentValue in beginEndAssignmentPair.Value
                    .Values)
                {
                    m_SyncDomainBusinessLogic.PNPlannerResults.Config2008BeginEndAssigmentBlocks[deviceName][
                        beginEndAssignmentPair.Key + 1].Add(beginEndAssignmentValue);
                }
            }

            ReferenceDummyAssignments(new PhaseAssignments(phaseAssignmentsOfAllPorts.PhaseAssignmentsOfAllPorts));

            //Set PhaseAssignmentBlocks
            foreach (KeyValuePair<int, Dictionary<int, IPNPlannerOutputPhaseAssignment>> phaseAssignmentPair in
                phaseAssignmentsOfAllPorts.PhaseAssignmentsOfAllPorts)
            {
                // Key is the portnumber starting from 0, value is the phaseAssignments of that port
                m_SyncDomainBusinessLogic.PNPlannerResults.Config2008PhaseAssigmentBlocks[deviceName].Add(
                    phaseAssignmentPair.Key + 1,
                    new List<IPNPlannerOutputPhaseAssignment>());
                foreach (IPNPlannerOutputPhaseAssignment phaseAssignmentValue in phaseAssignmentPair.Value.Values)
                {
                    m_SyncDomainBusinessLogic.PNPlannerResults.Config2008PhaseAssigmentBlocks[deviceName][
                        phaseAssignmentPair.Key + 1].Add(phaseAssignmentValue);
                }
            }
        }

        private static void ReadPeriodGroup(
            bool isTxPeriod,
            XmlTextReader pnPlannerReader,
            BeginEndAssignments beginEndAssignments,
            int portNumber,
            PhaseAssignments phaseAssignments)
        {
            Dictionary<int, IPNPlannerOutputBeginEndAssignment> beginEndAssignmentsOfPort;
            Dictionary<int, IPNPlannerOutputPhaseAssignment> phaseAssignmentsOfPort;
            PNPlannerOutputBeginEndAssignment beginEndAssignment;
            if (beginEndAssignments.BeginEndAssignmentsOfAllPorts.ContainsKey(portNumber))
            {
                beginEndAssignmentsOfPort = beginEndAssignments.BeginEndAssignmentsOfAllPorts[portNumber];
                phaseAssignmentsOfPort = phaseAssignments.PhaseAssignmentsOfAllPorts[portNumber];
            }
            else
            {
                beginEndAssignmentsOfPort = new Dictionary<int, IPNPlannerOutputBeginEndAssignment>();
                beginEndAssignments.BeginEndAssignmentsOfAllPorts.Add(portNumber, beginEndAssignmentsOfPort);
                phaseAssignmentsOfPort = new Dictionary<int, IPNPlannerOutputPhaseAssignment>();
                phaseAssignments.PhaseAssignmentsOfAllPorts.Add(portNumber, phaseAssignmentsOfPort);
            }

            string attrStr = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlNumberCaption);
            Debug.Assert(!string.IsNullOrEmpty(attrStr), "PNPlanner assignment number cannot be retrieved");
            int assignmentNumber = int.Parse(attrStr, CultureInfo.InvariantCulture);

            if (!beginEndAssignmentsOfPort.ContainsKey(assignmentNumber))
            {
                beginEndAssignment = new PNPlannerOutputBeginEndAssignment();
                beginEndAssignmentsOfPort.Add(assignmentNumber, beginEndAssignment);
            }
            else
            {
                beginEndAssignment = beginEndAssignmentsOfPort[assignmentNumber] as PNPlannerOutputBeginEndAssignment;
            }

            Debug.Assert(beginEndAssignment != null, "BeginEndAssignment cannot be retrieved");

            attrStr = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlStartCaption);
            if (!string.IsNullOrEmpty(attrStr))
            {
                uint start = uint.Parse(attrStr, CultureInfo.InvariantCulture);
                if (isTxPeriod)
                {
                    beginEndAssignment.TX_RedPeriodBegin = start;
                }
                else
                {
                    beginEndAssignment.RX_RedPeriodBegin = start;
                }
            }

            attrStr = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlEndCaption);
            if (!string.IsNullOrEmpty(attrStr))
            {
                uint end = uint.Parse(attrStr, CultureInfo.InvariantCulture);
                if (isTxPeriod)
                {
                    beginEndAssignment.TX_OrangePeriodBegin = end;
                    beginEndAssignment.TX_RedOrangePeriodEnd = end;
                }
                else
                {
                    beginEndAssignment.RX_OrangePeriodBegin = end;
                    beginEndAssignment.RX_RedOrangePeriodEnd = end;
                }
            }
            attrStr = pnPlannerReader.GetAttribute(PNPlannerConstants.m_XmlPhaseCaption);
            if (string.IsNullOrEmpty(attrStr))
            {
                return;
            }

            IEnumerable<int> phases = GetIntList(attrStr);
            foreach (int phase in phases)
            {
                PNPlannerOutputPhaseAssignment phaseAssignment;
                if (!phaseAssignmentsOfPort.ContainsKey(phase))
                {
                    phaseAssignment = new PNPlannerOutputPhaseAssignment();
                    phaseAssignment.Phase = phase;
                    phaseAssignmentsOfPort.Add(phase, phaseAssignment);
                }
                else
                {
                    phaseAssignment = phaseAssignmentsOfPort[phase] as PNPlannerOutputPhaseAssignment;
                }

                Debug.Assert(phaseAssignment != null, "Phase assignment cannot be retrieved");
                if (isTxPeriod)
                {
                    phaseAssignment.TX_OrangeBegin = assignmentNumber;
                    phaseAssignment.TX_ReservedBegin = assignmentNumber;
                    phaseAssignment.TX_ReservedEnd = assignmentNumber;
                }
                else
                {
                    phaseAssignment.RX_OrangeBegin = assignmentNumber;
                    phaseAssignment.RX_ReservedBegin = assignmentNumber;
                    phaseAssignment.RX_ReservedEnd = assignmentNumber;
                }
            }
        }

        /// <summary>
        /// Finds the phase blocks which have unassigned phase values either at Tx or Rx part.
        /// All unassigned phase blocks should reference the first dummy beginend assignment, i.e. the assignment
        /// which has 0 set to all its tx and rx values.
        /// </summary>
        /// <param name="phaseAssignmentsOfAllPorts"></param>
        private static void ReferenceDummyAssignments(PhaseAssignments phaseAssignments)
        {
            // Values of unassigned phase blocks are -1 either at Tx or Rx part.
            // The number of the first dummy beginend assignments are equal to
            // //Port/TxPeriodGroup/@Nr + 1 and //Port/RxPeriodGroup/@Nr + 1
            foreach (KeyValuePair<int, Dictionary<int, IPNPlannerOutputPhaseAssignment>> phaseAssignmentPair in phaseAssignments.PhaseAssignmentsOfAllPorts)
            {
                int maxPeriodRxGroupNo = -1;
                int maxPeriodTxGroupNo = -1;
                List<int> unAssignedRxOfPort = new List<int>();
                List<int> unAssignedTxOfPort = new List<int>();

                foreach (KeyValuePair<int, IPNPlannerOutputPhaseAssignment> phaseAssignmentPairOfPort in phaseAssignmentPair.Value)
                {
                    int phaseNo = phaseAssignmentPairOfPort.Key;
                    IPNPlannerOutputPhaseAssignment currPhaseAssignment = phaseAssignmentPairOfPort.Value;

                    if (currPhaseAssignment.RX_OrangeBegin > maxPeriodRxGroupNo)
                    {
                        maxPeriodRxGroupNo = currPhaseAssignment.RX_OrangeBegin;
                    }

                    if (currPhaseAssignment.TX_OrangeBegin > maxPeriodTxGroupNo)
                    {
                        maxPeriodTxGroupNo = currPhaseAssignment.TX_OrangeBegin;
                    }

                    if (currPhaseAssignment.RX_OrangeBegin == -1)
                    {
                        unAssignedRxOfPort.Add(phaseNo);
                    }

                    if (currPhaseAssignment.TX_OrangeBegin == -1)
                    {
                        unAssignedTxOfPort.Add(phaseNo);
                    }
                }

                maxPeriodTxGroupNo += 1;
                maxPeriodRxGroupNo += 1;
                for (int i = 0; i < unAssignedRxOfPort.Count; i++)
                {
                    PNPlannerOutputPhaseAssignment currAssignment = (PNPlannerOutputPhaseAssignment)phaseAssignmentPair.Value[unAssignedRxOfPort[i]];
                    currAssignment.RX_OrangeBegin = maxPeriodRxGroupNo;
                    currAssignment.RX_ReservedBegin = maxPeriodRxGroupNo;
                    currAssignment.RX_ReservedEnd = maxPeriodRxGroupNo;
                }

                for (int i = 0; i < unAssignedTxOfPort.Count; i++)
                {
                    PNPlannerOutputPhaseAssignment currAssignment = (PNPlannerOutputPhaseAssignment)phaseAssignmentPair.Value[unAssignedTxOfPort[i]];
                    currAssignment.TX_OrangeBegin = maxPeriodTxGroupNo;
                    currAssignment.TX_ReservedBegin = maxPeriodTxGroupNo;
                    currAssignment.TX_ReservedEnd = maxPeriodTxGroupNo;
                }
            }
        }

        /// <summary>
        /// Reads the  "OutputFingerPrint" section of PNPlanner Output XML File.
        /// FingerPrint is used in IRDataID of interface
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        private void ReadOutputFingerPrint(XmlReader xmlReader)
        {
            string fingerprint = string.Empty;
            while (xmlReader.MoveToNextAttribute())
            {
                switch (xmlReader.Name)
                {
                    case PNPlannerConstants.m_XmlValue:
                        fingerprint = xmlReader.Value;
                        break;
                }
            }

            m_SyncDomainBusinessLogic.PNPlannerResults.PNPlannerOutputFingerprint = fingerprint;
        }

        /// <summary>
        /// Reads the "TxRxPeriodsByPhase" section of the PNPlanner Output XML File.
        /// Values read corresponds to IRT bandwidth usage for each phase.
        /// </summary>
        /// <param name="xmlReader">Object responsible for reading the XML file.</param>
        /// <param name="controllersWithFrames"></param>
        private void ReadTxRxPeriodsByPhase(XmlTextReader xmlReader, InterfaceFrameDataList controllersWithFrames)
        {
            List<IPNPlannerOutputBandwidthsForPhases> bandwidthsForPhasesOfIrtTopIsland = new List<IPNPlannerOutputBandwidthsForPhases>();
            PNPlannerOutputBandwidthsForPhases bandwidthsForPhases = null;

            HashSet<long> inputFramePhases, outputFramePhases;
            GetUsedPhases(controllersWithFrames, out inputFramePhases, out outputFramePhases);

            while (xmlReader.Read())
            {
                if ((xmlReader.NodeType == XmlNodeType.EndElement)
                    && (xmlReader.Name == PNPlannerConstants.m_XmlTxRxPeriodsByPhaseCaption))
                {
                    break;
                }

                if (xmlReader.NodeType != XmlNodeType.Element)
                {
                    continue;
                }

                if (xmlReader.Name != PNPlannerConstants.m_XmlTxRxPeriodCaption)
                {
                    continue;
                }

                // We are under TxRxPeriod element
                while (xmlReader.MoveToNextAttribute())
                {
                    switch (xmlReader.Name)
                    {
                        case PNPlannerConstants.m_XmlPhaseCaption:
                            bandwidthsForPhases = new PNPlannerOutputBandwidthsForPhases { Phase = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture) };
                            bandwidthsForPhasesOfIrtTopIsland.Add(bandwidthsForPhases);
                            break;

                        case PNPlannerConstants.m_XmlMaxPortActivityCaption:
                            if (null != bandwidthsForPhases)
                            {
                                int bandwidth = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                                // Only add the bandwidth to input and/or output if this phase contains 
                                // input/output frames
                                bandwidthsForPhases.InputBandwidth = inputFramePhases.Contains(bandwidthsForPhases.Phase) ? bandwidth : 0;
                                bandwidthsForPhases.OutputBandwidth = outputFramePhases.Contains(bandwidthsForPhases.Phase) ? bandwidth : 0;
                            }

                            break;
                    }
                }
            }

            //Set BandwidthsForPhases Pro Controller
            if (null == controllersWithFrames)
            {
                return;
            }

            foreach (Interface interfaceSubmodule in controllersWithFrames.AllPNFrames.Keys)
            {
                if (!Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
                {
                    continue;
                }

                string deviceName = AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
                m_SyncDomainBusinessLogic.PNPlannerResults.BandwidthsForPhases[deviceName] = bandwidthsForPhasesOfIrtTopIsland;
            }
        }

        /// <summary>
        /// Gets which phases are used for input and output frames by iterating through m_PNFrameDataMessageIds.
        /// </summary>
        /// <param name="controllersWithFrames"></param>
        /// <param name="inputFramePhases"></param>
        /// <param name="outputFramePhases"></param>
        private void GetUsedPhases(InterfaceFrameDataList controllersWithFrames, out HashSet<long> inputFramePhases, out HashSet<long> outputFramePhases)
        {
            Dictionary<long, IPNFrameData> frameDataDict = new Dictionary<long, IPNFrameData>();
            foreach (KeyValuePair<Interface, List<IPNFrameData>> controllerFrames in controllersWithFrames.AllPNFrames)
            {
                if (controllerFrames.Value == null)
                {
                    continue;
                }

                foreach (IPNFrameData frame in controllerFrames.Value)
                {
                    frameDataDict.Add(frame.FrameID, frame);
                }
            }

            // Find out the maximum reduction ratio. 
            long maxInputReduction = 1;
            long maxOutputReduction = 1;
            foreach (KeyValuePair<long, IPNFrameData> pair in frameDataDict)
            {
                IPNFrameData frameData = pair.Value;
                if ((frameData.RedundantFrameID != 0)
                    && (frameData.FrameID % 2 == 1))
                {
                    // Don't iterate through redundant frames. 
                    // They always have the same reduction ratio with their couples.
                    continue;
                }

                switch (frameData.FrameDirection)
                {
                    case (byte)PNPlannerFrameDirection.InputFrame:
                        if (maxInputReduction < frameData.DeviceLocalReductionRatio)
                        {
                            maxInputReduction = frameData.DeviceLocalReductionRatio;
                        }

                        break;
                    case (byte)PNPlannerFrameDirection.OutputFrame:
                        if (maxOutputReduction < frameData.DeviceLocalReductionRatio)
                        {
                            maxOutputReduction = frameData.DeviceLocalReductionRatio;
                        }

                        break;
                }
            }

            // Using maximum RR, find out which phases are used for input and output frames
            inputFramePhases = new HashSet<long>();
            outputFramePhases = new HashSet<long>();
            foreach (KeyValuePair<long, IPNFrameData> pair in frameDataDict)
            {
                IPNFrameData frameData = pair.Value;
                if ((frameData.RedundantFrameID != 0)
                    && (frameData.FrameID % 2 == 1))
                {
                    // Don't iterate through redundant frames. 
                    // They are always planned at the same phase with their couples.
                    continue;
                }

                switch (frameData.FrameDirection)
                {
                    case (byte)PNPlannerFrameDirection.InputFrame:
                        FillPhasesOfFrame(inputFramePhases, frameData, maxInputReduction);
                        break;
                    case (byte)PNPlannerFrameDirection.OutputFrame:
                        FillPhasesOfFrame(outputFramePhases, frameData, maxOutputReduction);
                        break;
                }
            }
        }

        /// <summary>
        /// Finds out which phases are filled based on a particular frame, and updates the framePhases set according
        /// to this info.
        /// </summary>
        /// <param name="framePhases"></param>
        /// <param name="frameData"></param>
        /// <param name="maxRR"></param>
        private static void FillPhasesOfFrame(HashSet<long> framePhases, IPNFrameData frameData, long maxRR)
        {
            // The first phase to be filled is its first planned phase.
            framePhases.Add(frameData.DeviceLocalPhase);
            long nextPhase = frameData.DeviceLocalPhase + frameData.DeviceLocalReductionRatio;
            while (nextPhase <= maxRR)
            {
                // Fill all remaining phases in a cyclic manner until the max RR is reached.
                framePhases.Add(nextPhase);
                nextPhase += frameData.DeviceLocalReductionRatio;
            }
        }

        private static bool ReadLog(XmlTextReader xmlReader)
        {
            LogSeverity severity = LogSeverity.Unknown;
            PNPlannerException pnPlannerException = new PNPlannerException();
            bool success = true;
            while (xmlReader.Read())
            {
                if ((xmlReader.Name == PNPlannerConstants.m_XmlResultLogCaption)
                    && (xmlReader.NodeType == XmlNodeType.EndElement))
                {
                    break;
                }

                if ((xmlReader.Name == PNPlannerConstants.m_XmlResultNoCaption)
                    && (xmlReader.NodeType == XmlNodeType.Element))
                {
                    xmlReader.Read();
                    pnPlannerException.No = int.Parse(xmlReader.Value, CultureInfo.InvariantCulture);
                }

                if ((xmlReader.Name == PNPlannerConstants.m_XmlResultTypeCaption)
                    && (xmlReader.NodeType == XmlNodeType.Element))
                {
                    xmlReader.Read();
                    pnPlannerException.Type = xmlReader.Value;
                    severity = (LogSeverity)Enum.Parse(typeof(LogSeverity), xmlReader.Value, true);
                }

                if ((xmlReader.Name == PNPlannerConstants.m_XmlResultMessageCaption)
                    && (xmlReader.NodeType == XmlNodeType.Element))
                {
                    xmlReader.Read();
                    pnPlannerException.Message = xmlReader.Value;
                }

                if ((xmlReader.Name == PNPlannerConstants.m_XmlResultAdditionalInfoCaption)
                    && (xmlReader.NodeType == XmlNodeType.Element))
                {
                    AppendAdditionalInfo(xmlReader, pnPlannerException);
                }
            }

            if (severity == LogSeverity.Error)
            {
                success = false;
            }

            ConsistencyLogger.Log(ConsistencyType.PN, severity, "Schedule Algortihm", string.Format(CultureInfo.InvariantCulture, "PNPlanner: {0}.", pnPlannerException.Message.Replace("Error: ", string.Empty)));

            return success;
        }

        #region Utility Functions

        /// <summary>
        /// Finds the controller whose name is passed as a parameter in the Dictionary where controller interfaces
        /// are the keys.
        /// </summary>
        /// <param name="controllerName">Name of the controller to be queried</param>
        /// <param name="controllersWithFrames">Dictionary in which the controllers to be searched are the keys.</param>
        /// <returns></returns>
        private Interface FindController(string controllerName, InterfaceFrameDataList controllersWithFrames)
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();
            foreach (Interface interfaceSubmodule in controllersWithFrames.AllPNFrames.Keys)
            {
                string switchName = PNPlannerHelper.GetSwitchName(interfaceSubmodule);
                if (!accessCode.IsOkay
                    || string.IsNullOrEmpty(switchName))
                {
                    continue;
                }

                if (controllerName == switchName)
                {
                    return interfaceSubmodule;
                }
            }

            return null;
        }

        /// <summary>
        /// Updates the original frame list properties of each controller using the frame objects read from the output.
        /// </summary>
        /// <param name="controllersWithFrames">
        /// Dictionary in which the controllers are paired with their corresponding
        /// frames.
        /// </param>
        /// <param name="outputFrames">Frame objects that are read from the output.</param>
        private void UpdateFrameReferences(InterfaceFrameDataList controllersWithFrames, List<PNPlannerOutputFrame> outputFrames)
        {
            foreach (PNPlannerOutputFrame outputFrame in outputFrames)
            {
                foreach (KeyValuePair<Interface, List<IPNFrameData>> controllerWithFrames in controllersWithFrames.AllPNFrames)
                {
                    UpdateFrameReferenceForController(controllerWithFrames.Value, outputFrame);
                }
            }
            List<DfpFrameModification> dfpFrameModifications = CreateDFPFrameModifications(outputFrames, controllersWithFrames);
            UpdateDFPFrames(dfpFrameModifications, controllersWithFrames);
        }

        private void UpdateDFPFrames(
            List<DfpFrameModification> dfpFrameModifications,
            InterfaceFrameDataList controllersWithFrames)
        {
            if (dfpFrameModifications == null)
            {
                return;
            }

            foreach (DfpFrameModification dfpFrameModification in dfpFrameModifications)
            {
                // Created dfp frame which must be added to the lists.
                IPNDfpFrameData dfpFrame = dfpFrameModification.DfpFrame;
                // Frames which were originally created but will be deleted because 
                // the interface is part of a pack group.
                List<IPNFrameData> originalFrames = dfpFrameModification.OriginalFrames;


                // Update allPNFrames
                foreach (KeyValuePair<Interface, List<IPNFrameData>> keyValuePair in m_SyncDomainBusinessLogic.m_InterfaceFrameDataList.AllPNFrames)
                {
                    Interface currController = keyValuePair.Key;
                    List<IPNFrameData> pnFrameDatasOfController = keyValuePair.Value;

                    if (!pnFrameDatasOfController.Contains(originalFrames[0]))
                    {
                        continue;
                    }

                    // The controller is found. The other original frames must 
                    // also be located under this controller.

                    foreach (IPNFrameData originalFrame in originalFrames)
                    {
                        pnFrameDatasOfController.Remove(originalFrame);
                    }
                    // Now add the new frame
                    pnFrameDatasOfController.Add(dfpFrame);

                    // Update irtTopIsland
                    List<IPNFrameData> pnFrameDatas = controllersWithFrames.AllPNFrames[currController];
                    foreach (IPNFrameData originalFrame in originalFrames)
                    {
                        pnFrameDatas.Remove(originalFrame);
                    }
                    pnFrameDatas.Add(dfpFrame);
                }
            }
        }

        private List<DfpFrameModification> CreateDFPFrameModifications(List<PNPlannerOutputFrame> outputFrames, InterfaceFrameDataList controllersWithFrames)
        {
            List<DfpFrameModification> dfpFrameModifications = new List<DfpFrameModification>();
            Interface controllerInterface =
                controllersWithFrames.AllPNFrames.FirstOrDefault(p => p.Key.PNIOC != null).Key;
            foreach (PNPlannerOutputFrame outputFrame in outputFrames)
            {
                if (outputFrame.Subframes == null
                    || outputFrame.Subframes.Count <= 0)
                {
                    continue;
                }

                // Create a new Frame and store the info in it.
                List<int> ioDeviceCoreIds = GetIODeviceCoreIdList(controllersWithFrames, controllerInterface, outputFrame);

                PNDfpFrameData dfpFrameData = new PNDfpFrameData(ioDeviceCoreIds);
                dfpFrameData.FrameID = outputFrame.FrameID;
                dfpFrameData.FrameIdSet = true;

                dfpFrameData.SfCrc16 = outputFrame.SfCrc16;

                uint dwdRestartFactor = controllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtRestartFactorForDistributedWD,
                    new AttributeAccessCode(),
                    0);
                dfpFrameData.DistributedRestartFactor = dwdRestartFactor;

                dfpFrameData.ControllerLocalReductionRatio = outputFrame.ControllerLocalReductionRatio;
                dfpFrameData.DeviceLocalPhase = outputFrame.DeviceLocalPhase;

                // dfp frame is also an rtc3 frame
                dfpFrameData.FrameClass = 3;
                if (dfpFrameData.FrameClass == (long)PNIOFrameClass.Class3Frame)
                {
                    dfpFrameData.DataLength = outputFrame.Length - outputFrame.Padding;
                }

                dfpFrameData.SetPossibleReductionRatios(outputFrame.PossibleReductionRatios);

                Dictionary<long, IPNFrameData> pnFrameDataMessageIds = new Dictionary<long, IPNFrameData>();
                foreach (KeyValuePair<Interface, List<IPNFrameData>> controllerFrames in controllersWithFrames.AllPNFrames)
                {
                    if (controllerFrames.Value == null)
                    {
                        continue;
                    }

                    foreach (IPNFrameData frame in controllerFrames.Value)
                    {
                        pnFrameDataMessageIds.Add(frame.FrameID, frame);
                    }
                }

                foreach (var subframe in outputFrame.Subframes)
                {
                    int origFrameId = subframe.MsgId;
                    IPNFrameData origFrame = pnFrameDataMessageIds[origFrameId];
                    PNSubframeData subframeData = new PNSubframeData(origFrame.CoreIds[0]);
                    subframeData.OriginalFrameId = subframe.MsgId;

                    subframeData.SubframeId = subframe.SubframeId;
                    subframeData.SubframeLength = subframe.SFLength;

                    dfpFrameData.Subframes.Add(subframeData.SubframeId, subframeData);

                    if (origFrame.HasNotAssignedSubmodule)
                    {
                        dfpFrameData.HasNotAssignedSubmodule = origFrame.HasNotAssignedSubmodule;
                        subframeData.SharedDataLength = origFrame.SharedDataLength;
                        subframeData.NumberOfARs = origFrame.NumberOfARs;
                        subframeData.StationNumber = origFrame.StationNumber;
                    }
                }

                List<IPNFrameData> originalFrames = m_PlannerHelper.FillDfPFrame(dfpFrameData, pnFrameDataMessageIds);
                DfpFrameModification dfpFrameModification = new DfpFrameModification(originalFrames, dfpFrameData);
                dfpFrameModifications.Add(dfpFrameModification);
            }

            return dfpFrameModifications;
        }

        private List<int> GetIODeviceCoreIdList(InterfaceFrameDataList controllersWithFrames, Interface controllerInterface, PNPlannerOutputFrame outputFrame)
        {
            List<int> ioDeviceCoreIds = new List<int>();
            foreach (var subframe in outputFrame.Subframes)
            {
                IPNFrameData frameData = controllersWithFrames.AllPNFrames
                    .FirstOrDefault(p => p.Key.PNIOC != null).Value
                    .FirstOrDefault(p => p.FrameID == subframe.MsgId);
                Dictionary<int, int> interfaceIdIoConnectorIdMapping =
                    PNPlannerHelper.BuildInterfaceAndIODeviceMapping(
                        m_SyncDomainBusinessLogic.AllParticipants.ToList());
                Interface sender = PNPlannerHelper.GetSenderOfFrame(
                    frameData,
                    m_SyncDomainBusinessLogic,
                    controllerInterface,
                    interfaceIdIoConnectorIdMapping);

                List<Interface> receivers = PNPlannerHelper.GetReceiversOfFrame(
                    frameData,
                    m_SyncDomainBusinessLogic,
                    controllerInterface,
                    interfaceIdIoConnectorIdMapping);

                if (frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame)
                {
                    ioDeviceCoreIds.Add(sender.PNIOD.GetHashCode());
                }
                else if (frameData.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame)
                {
                    foreach (Interface receiver in receivers)
                    {
                        ioDeviceCoreIds.Add(receiver.PNIOD.GetHashCode());
                    }
                }
            }

            return ioDeviceCoreIds;
        }

        /// <summary>
        /// Finds the
        /// </summary>
        /// <param name="originalFrames"></param>
        /// <param name="outputFrame"></param>
        private void UpdateFrameReferenceForController(List<IPNFrameData> originalFrames, PNPlannerOutputFrame outputFrame)
        {
            if (originalFrames == null)
            {
                return;
            }

            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.FrameIdSet)
                {
                    continue;
                }

                if (outputFrame.OriginalFrameID != originalFrame.FrameID)
                {
                    continue;
                }

                //Update the FrameID. FrameID generation is realised in PNPlanner core.
                originalFrame.FrameID = outputFrame.FrameID;
                originalFrame.FrameIdSet = true;
                originalFrame.RedundantFrameID = outputFrame.RedundantFrameID;

                originalFrame.DeviceLocalReductionRatio = outputFrame.DeviceLocalReductionRatio;
                originalFrame.ControllerLocalReductionRatio = outputFrame.ControllerLocalReductionRatio;
                originalFrame.SendClockFactor = outputFrame.SendclockFactor;
                originalFrame.DeviceLocalPhase = outputFrame.DeviceLocalPhase;
                originalFrame.ControllerLocalPhase = outputFrame.ControllerLocalPhase;
                if (originalFrame.FrameClass == (long)PNIOFrameClass.Class3Frame)
                {
                    originalFrame.DataLength = outputFrame.Length - outputFrame.Padding;
                }

                originalFrame.FixedPhaseNumber = outputFrame.Phase;
                originalFrame.SetPossibleReductionRatios(outputFrame.PossibleReductionRatios);
            }
        }

        private static void AppendAdditionalInfo(XmlReader xmlReader, PNPlannerException pnPlannerException)
        {
            if ((xmlReader.Name == PNPlannerConstants.m_XmlResultAdditionalInfoCaption)
                && (xmlReader.NodeType == XmlNodeType.EndElement))
            {
                return;
            }

            pnPlannerException.FrameId = int.Parse(xmlReader.GetAttribute(PNPlannerConstants.m_XmlFrameIDCaption) ?? "0", CultureInfo.InvariantCulture);
            pnPlannerException.StationNo = int.Parse(xmlReader.GetAttribute(PNPlannerConstants.m_XmlStationNoCaption) ?? "0", CultureInfo.InvariantCulture);
            pnPlannerException.SlotNo = xmlReader.GetAttribute(PNPlannerConstants.m_XmlSlotNoCaption) ?? "0";
            pnPlannerException.SubSlotNo = xmlReader.GetAttribute(PNPlannerConstants.m_XmlSubSlotNoCaption) ?? "0";
            pnPlannerException.AddInfo = xmlReader.GetAttribute(PNPlannerConstants.m_XmlAddInfoCaption) ?? "0";
        }

        #endregion

        #endregion
    }
}