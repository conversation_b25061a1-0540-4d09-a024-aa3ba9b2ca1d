/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: HeadModuleConverter.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using GSDI;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.GSDImport.Helper;

using PNConfigLib.Gsd.Interpreter.Common;

using DeviceAccessPoint = PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint;
using Module = PNConfigLib.Gsd.Interpreter.Common.Module;
using ModulePlugData = PNConfigLib.Gsd.Interpreter.Common.ModulePlugData;
using PortSubmodule = PNConfigLib.Gsd.Interpreter.Common.PortSubmodule;
using Subslot = PNConfigLib.Gsd.Interpreter.Common.Subslot;
using VirtualSubmodule = PNConfigLib.Gsd.Interpreter.Common.VirtualSubmodule;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal sealed class HeadModuleConverter : ModuleBaseConverter
    {
        private DeviceAccessPoint m_DAP;

        private DecentralDeviceCatalog m_Device;

        private GsdDevice m_GsdDevice;

        protected override string GsdId => m_DAP.GsdID;
        public HeadModuleConverter(DeviceAccessPoint dap, GsdDevice gsdDevice)
        {
            m_DAP = dap;
            m_GsdDevice = gsdDevice;
        }
        public DecentralDeviceCatalog Convert(DecentralDeviceCatalog device)
        {
            m_Device = device;

            AddHeadModuleGeneralAttributes();

            return m_Device;
        }
        private void AddHeadModuleGeneralAttributes()
        {
            SubslotHelper = new SubslotHelper();
            //Slots
            if (m_DAP.VirtualSubmodules != null)
            {
                foreach (VirtualSubmodule submodule in m_DAP.VirtualSubmodules)
                {
                    ModulePlugData plugData = m_DAP.GetSubmodulePlugData(submodule.GsdID);
                    if (plugData == null)
                    {
                        plugData = submodule.PlugData;
                    }
                    SubslotHelper.AddVirtualSubmodulePlugData(submodule.GsdID, plugData);
                    SubslotHelper.AddVirtualSubmodule(submodule.GsdID, submodule);
                }
            }
            if (m_DAP.Subslots != null)
            {
                foreach (Subslot subslot in m_DAP.Subslots)
                {
                    SubslotHelper.AddSubslotDescription(subslot);
                }
            }
            Array physicalSubslots = m_DAP.PhysicalSubslots;
            List<uint> subslotList = new List<uint>();
            if (physicalSubslots != null)
            {
                foreach (uint subslot in physicalSubslots)
                {
                    if (subslot < 0x8000)
                    {
                        subslotList.Add(subslot);
                    }
                }
            }
            SubslotHelper.AddAllPhysicalSubslots(subslotList);
            if (m_DAP.PhysicalSubmodules != null)
            {
                foreach (ModuleObject submodule in m_DAP.PhysicalSubmodules)
                {
                    if (submodule.GetType() != typeof(PortSubmodule))
                    {
                        ModulePlugData plugData = m_DAP.GetSubmodulePlugData(submodule.GsdID);
                        SubslotHelper.AddPhysicalSubmodulePlugData(submodule.GsdID, plugData);
                        SubslotHelper.AddPhysicalSubmodule(submodule.GsdID, submodule);
                    }
                }
            }

            if (m_DAP.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDInterface))
            {
                Array submoduleList = m_DAP.GetSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDInterface);
                foreach (SystemDefinedSubmoduleObject submodule in submoduleList)
                {
                    SubslotHelper.AddSystemDefinedSubmodule(submodule);
                }
            }

            m_Device.AvailableSubSlots = m_DAP.Subslots; 

            AddRequiredAttributes();
            AddSharedDeviceAttributes();
        }
        private void AddRequiredAttributes()
        {
            AddPNDeviceIdAttribute();
            AddPNVendorIdAttribute();
            AddCatalogInfoVariables();
            AddPNModuleIdentNumberAttribute();
            AddDeviceAccessSupportesAttribute();
            AddScalanceDeviceCapabilitiesAttribute();
        }

        private void AddPNDeviceIdAttribute()
        {
            m_Device.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnDeviceId,
                (int)m_GsdDevice.Info.DeviceIdentNumber);
        }
        private void AddPNVendorIdAttribute()
        {
            m_Device.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnVendorId,
                (int)m_GsdDevice.Info.VendorIdentNumber);
        }
        private void AddCatalogInfoVariables()
        {
            DefineInfoVariables(m_DAP.Info, m_Device);
        }

        private void AddPNModuleIdentNumberAttribute()
        {
            m_Device.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnModuleIdentNumber,
                m_DAP.IdentNumber);
        }

        private void AddDeviceAccessSupportesAttribute()
        {
            m_Device.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.DeviceAccessSupported,
                m_DAP.IsDeviceAccessSupported);
            if (!m_DAP.IsDeviceAccessSupported)
            {
                return;
            }
            m_Device.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoNumberOfDeviceAccessAR,
                m_DAP.NumberOfDeviceAccessAR);
        }

        // Modular switches need the attribute ScalanceDeviceCapabilities
        private void AddScalanceDeviceCapabilitiesAttribute()
        {
            Array modules = m_DAP.Modules;

            bool isModularSwitch = false;
            if (modules != null)
            {
                foreach (Module module in modules)
                {
                    if (module.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort))
                    {
                        isModularSwitch = true;
                        break;
                    }

                    if (module.PhysicalSubmodules != null)
                    {
                        foreach (ModuleObject submodule in module.PhysicalSubmodules)
                        {
                            if (submodule.GetType() == typeof(PortSubmodule))
                            {
                                isModularSwitch = true;
                                break;
                            }
                        }
                    }
                }
            }

            if (isModularSwitch)
            {
                m_Device.AttributeAccess.AddAnyAttribute<ulong>(InternalAttributeNames.ScalanceDeviceCapabilities, 1);
            }
        }

        private void AddSharedDeviceAttributes()
        {
            if (m_DAP.IsSharedDeviceSupported)
            {
                m_Device.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceSupported,
                    m_DAP.IsSharedDeviceSupported);
            }
            if (m_DAP.IsSharedInputSupported)
            {
                m_Device.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.SharedInputSupported,
                    m_DAP.IsSharedInputSupported);
            }
        }
    }
}