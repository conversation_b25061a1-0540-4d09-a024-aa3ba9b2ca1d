/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronChecker.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.IO;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.HWCNBL.Constants;

using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class IsochronChecker : IConsistencyChecker
    {
        private Devices m_Devices;

        private readonly ListOfNodes m_ListOfNodes; 

        internal IsochronChecker(Devices devices, ListOfNodes lon)
        {
            m_Devices = devices;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            CheckIsochronRefIDs();
        }

        private void CheckIsochronRefIDs()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_Devices.DecentralDevice)
            {
                foreach (IsochronousModeTypeIsochronousSubmodule isoMmodule in xmlDecentralDevice.DecentralDeviceInterface
                    .IsochronousMode.IsochronousSubmodule)
                {
                    if (xmlDecentralDevice.DeviceRefID != isoMmodule.ModuleRefID
                        && !xmlDecentralDevice.Module.Exists(m => m.ModuleID == isoMmodule.ModuleRefID))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_ModuleRefIDWithoutModuleUnderIsochronousModule,
                            isoMmodule.ModuleRefID,
                            xmlDecentralDevice.DeviceRefID);
                        throw new ConsistencyCheckException();
                    }

                    if (string.IsNullOrEmpty(isoMmodule.SubmoduleRefID))
                    {
                        continue;
                    }

                    var lonDecentralDevice =
                        ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);
                    string gsdFileName = Path.GetFileName(lonDecentralDevice?.GSDPath);

                    List<string> virtualSubmoduleCatalogIds = 
                        InputCheckerHelper.GetAllVirtualSubmoduleCatalogIds(xmlDecentralDevice, gsdFileName, isoMmodule.ModuleRefID, m_ListOfNodes);

                    ModuleType currentXmlModule =
                        xmlDecentralDevice.Module.FirstOrDefault(m => m.ModuleID == isoMmodule.ModuleRefID);

                    bool xmlSubmoduleNotExistInConfiguration =
                        currentXmlModule != null
                        && !currentXmlModule.Submodule.Exists(sm => sm.SubmoduleID == isoMmodule.SubmoduleRefID);

                    if ((xmlSubmoduleNotExistInConfiguration && virtualSubmoduleCatalogIds == null)
                        || (xmlSubmoduleNotExistInConfiguration
                            && !virtualSubmoduleCatalogIds.Contains(isoMmodule.SubmoduleRefID)))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule,
                            isoMmodule.SubmoduleRefID,
                            xmlDecentralDevice.DeviceRefID,
                            isoMmodule.ModuleRefID);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }
    }
}
