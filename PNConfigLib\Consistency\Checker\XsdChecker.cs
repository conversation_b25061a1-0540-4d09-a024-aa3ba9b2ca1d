/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: XsdChecker.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.IO;
using System.Reflection;
using System.Xml;
using System.Xml.Schema;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Consistency
{
    internal class XsdChecker
    {
        private static readonly string m_XsdDir = Path.Combine(
            Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "XSD");

        internal static string ControllerVariantXsdPath { get; } = Path.Combine(m_XsdDir, "ControllerVariant.xsd");
        internal static string ConfigurationXsdPath { get; } = Path.Combine(m_XsdDir, "Configuration.xsd");
        internal static string ListOfNodesXsdPath { get; } = Path.Combine(m_XsdDir, "ListOfNodes.xsd");
        internal static string TopologyXsdPath { get; } = Path.Combine(m_XsdDir, "Topology.xsd");

        /// <summary>
        /// The event handler used for validating xml files with their XSDs.
        /// </summary>
        /// <param name="sender">The object that triggers the event handler.</param>
        /// <param name="validationEventArgs">Event arguments.</param>
        private void ValidationEventHandler(object sender, ValidationEventArgs validationEventArgs)
        {
            throw validationEventArgs.Exception;
        }

        /// <summary>
        /// Checks whether given paths for xml file is correct and that file exist.
        /// </summary>
        /// <param name="xmlFilePath">The path of xml file.</param>
        /// /// <param name="xsdPath">The path of xsd.</param>
        internal bool ValidateXsd(string xmlFilePath, string xsdPath)
        {
            try
            {
                SecureXmlDocument xmlDoc = new SecureXmlDocument();
                xmlDoc.Load(xmlFilePath);
                ValidateXsd(xmlDoc, xsdPath);
            }
            catch (XmlSchemaException xmlSchemaException)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XSD,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XSD_ValidationError,
                    Path.GetFileName(xmlFilePath),
                    xmlSchemaException.Message);
                return false;
            }
            catch (XmlException exception)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    null,
                    ConsistencyConstants.XML_NotValid,
                    xmlFilePath,
                    exception.Message);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Checks whether given paths for xml file is correct and that file exist.
        /// </summary>
        /// <param name="xmlDoc">xml file.</param>
        /// /// <param name="xsdPath">The path of xsd.</param>
        private void ValidateXsd(SecureXmlDocument xmlDoc, string xsdPath)
        {
            XmlTextReader schemaReader = new SecureXmlTextReader(xsdPath);
            XmlSchema schema = XmlSchema.Read(schemaReader, ValidationEventHandler);
            ValidateNamespace(xmlDoc, schema);

            xmlDoc.Doc.Schemas.Add(schema);
            xmlDoc.Doc.Validate(ValidationEventHandler);
        }

        /// <summary>
        /// Checks xml document's namespace with XSD target namespace.
        /// </summary>
        /// <param name="xmlDoc"></param>
        /// <param name="schema"></param>
        private static void ValidateNamespace(SecureXmlDocument xmlDoc, XmlSchema schema)
        {
            if (xmlDoc.Doc.DocumentElement?.NamespaceURI == schema.TargetNamespace)
            {
                return;
            }

            string errorMessage = ConsistencyLogger.RetrieveConsistencyMessage(
                ConsistencyConstants.XSD_NamespaceNotValid,
                xmlDoc.Doc.DocumentElement?.NamespaceURI,
                schema.TargetNamespace,
                Path.GetFileName(schema.SourceUri));
            throw new XmlSchemaException(errorMessage);
        }

        /// <summary>
        /// Checks whether given paths for xml file is correct and that file exist.
        /// </summary>
        /// <param name="resourceName">Embedded resource file name.</param>
        /// /// <param name="xsdPath">The path of xsd.</param>
        internal bool ValidateResource(string resourceName, string xsdPath)
        {
            try
            {
                SecureXmlDocument xmlDoc = GetXmlFileFromResource(resourceName);
                ValidateXsd(xmlDoc, xsdPath);
            }
            catch (XmlSchemaException xmlSchemaException)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XSD,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XSD_ValidationError,
                    resourceName,
                    xmlSchemaException.Message);
                return false;
            }
            catch (XmlException exception)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    null,
                    ConsistencyConstants.XML_NotValid,
                    resourceName,
                    exception.Message);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="resourceName"></param>
        /// <returns></returns>
        private SecureXmlDocument GetXmlFileFromResource(string resourceName)
        {
            SecureXmlDocument retval = new SecureXmlDocument();

            Assembly assembly = this.GetType().Assembly;
            using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            {
                retval.Load(stream);
            }

            return retval;
        }
    }
}
