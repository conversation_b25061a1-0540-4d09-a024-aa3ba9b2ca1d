/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnProjectManager                          :C&  */
/*                                                                           */
/*  F i l e               &F: PNConfigLibResult.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.PNProjectManager;

#endregion

namespace PNConfigLib
{
    /// <summary>
    /// The class that represents the result of a PNConfigLib execution.
    /// </summary>
    public class PNConfigLibResult
    {
        /// <summary>
        /// The default constructor that initializes the properties to default values.
        /// </summary>
        public PNConfigLibResult()
        {
            Messages = new List<ConsistencyLog>();
            ReturnObject = null;
        }

        /// <summary>
        /// The message that explains the result.
        /// </summary>
        public IList<ConsistencyLog> Messages { get; }

        public void SetMessages(IList<ConsistencyLog> list)
        {
            Messages.Clear();
            ((List<ConsistencyLog>)Messages).AddRange(list);
        }

        /// <summary>
        /// The return code that indicates a specific result.
        /// </summary>
        public PNConfigLibReturnCode ReturnCode {
            get
            {
                if (Messages.Any(consistencyLog => consistencyLog.Severity == LogSeverity.Error))
                {
                    return PNConfigLibReturnCode.Failure;
                }
                if (Messages.Any(consistencyLog => consistencyLog.Severity == LogSeverity.Warning))
                {
                    return PNConfigLibReturnCode.SuccessWithWarning;
                }
                return PNConfigLibReturnCode.Success;
            }
        }

        /// <summary>
        /// The object returned by the result.
        /// </summary>
        public Dictionary<string, XDocument> ReturnObject { get; private set; }

        public void SetReturnObject(IDictionary<string, XDocument> dictionary)
        {
            ReturnObject = (Dictionary<string, XDocument>)dictionary;
        }
    }
}