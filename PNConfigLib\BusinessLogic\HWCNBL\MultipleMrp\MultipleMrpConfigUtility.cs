/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MultipleMrpConfigUtility.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Mrp;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.MultipleMrp
{
    /// <summary>
    /// Contains constants and methods related to multiple MRP data records.
    /// </summary>
    internal static class MultipleMrpConfigUtility
    {
        /// <summary>
        /// Default MRP domain name.
        /// </summary>
        internal const string s_MrpDomainDefaultName = "mrpdomain-1";

        /// <summary>
        /// Generates MRP UUID.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule.</param>
        /// <param name="instanceNumber">MRP instance number.</param>
        /// <returns>The UUID.</returns>
        internal static byte[] GenerateMrpUuid(Interface interfaceSubmodule, bool isMultipleInstanceActive, int instanceNumber = 0)
        {
            // Create the input text for the algorithm.
            MrpDomainInstance mediaRedDomain = null;
            List<MrpDomainInstance> sortedMrpInstances = interfaceSubmodule.GetSortedMrpDomainInstances();
            if (interfaceSubmodule != null && sortedMrpInstances != null &&
                sortedMrpInstances.Count > 0)
            {
                if (!isMultipleInstanceActive)
                {
                    mediaRedDomain = sortedMrpInstances.First();
                }
                else
                {
                    mediaRedDomain = sortedMrpInstances[instanceNumber - 1];
                }
            }
            String mrpDomainUuidText = mediaRedDomain == null ?
                                           s_MrpDomainDefaultName :
                mediaRedDomain.AttributeAccess.GetAnyAttribute<String>(InternalAttributeNames.Name, new AttributeAccessCode(),
                    s_MrpDomainDefaultName);

            string validatedName;

            // Perform the DNS conformity validation.
            if ((PNNameOfStationConverter.EncodePNNameOfStation(mrpDomainUuidText, null, null, out validatedName)
                 != PNNameOfStationConverter.DnsNameConvertStatus.NoError)
                || (validatedName == null))
            {
                Debug.Fail("Unconvertible Domain Name. Using default MRP-Domain UUID.");
                return new byte[]
                           {
                               0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
                               0xFF
                           };
            }

            // The object for converting the text to the byte format.
            UTF8Encoding byteConverter = new UTF8Encoding();
            byte[] byteArrToConvert = byteConverter.GetBytes(validatedName);

            // Run the MD5 algorithm and return the result.
            byte[] uuid;
            if (MD5Encryptor.Encrypt(byteArrToConvert, out uuid))
            {
                return uuid;
            }
            Debug.Fail("MD5 Encryption failed. Using default MRP-Domain UUID.");
            return new byte[]
                       {
                           0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
                       };
        }

        // Contains all public methods of the class
        /// <summary>
        /// Gets the parameter data block PDInterfaceMrpDataAdjust for multiple mrp supported interfaces.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose data record is generated.</param>
        /// <returns>
        /// PDInterfaceMrpDataCheck 0x0213 Parameter Block if it is to be generated,
        /// otherwise an empty byte array.
        /// </returns>
        internal static byte[] GetPDInterfaceMultipleMrpDataAdjust(Interface interfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            uint numberOfInstances =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnMrpMaxInstances,
                    new AttributeAccessCode(),
                    0);

            PNMrpRole PNMrpRole;

            PdInterfaceMrpDataAdjustStructV11 mrpDataAdjustBlock = new PdInterfaceMrpDataAdjustStructV11();

            byte numberOfActiveIsntances = 0;
            if (!MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule))
            {
                //Multiple Instances are not active so only one instance will be generated
                numberOfInstances = 1;
            }

            for (int i = 0; i < numberOfInstances; i++)
            {
                PDInterfaceMrpDataAdjustStruct mrpInstanceDataAdjustBlock = new PDInterfaceMrpDataAdjustStruct();
                mrpInstanceDataAdjustBlock.MrpInstance = numberOfActiveIsntances;

                //handle devices which just require V1.1 data blocks
                if (!MultipleMrpUtilities.IsMultipleInstancesAvailable(interfaceSubmodule))
                {
                    mrpInstanceDataAdjustBlock.MrpDomainUUID = GenerateMrpUuid(interfaceSubmodule, false);
                }
                else
                {
                    mrpInstanceDataAdjustBlock.MrpDomainUUID = GenerateMrpUuid(interfaceSubmodule,
                        MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule), i + 1);
                }

                if (interfaceSubmodule.MrpDomainInstances != null && interfaceSubmodule.MrpDomainInstances.Count > i)
                {
                    List<MrpDomainInstance> sortedMrpDomainInstances = interfaceSubmodule.GetSortedMrpDomainInstances();
                    MrpDomainInstance mediaRedDomain = sortedMrpDomainInstances[i];
                    if (mediaRedDomain == null)
                    {
                        mrpInstanceDataAdjustBlock.MrpDomainName = s_MrpDomainDefaultName;
                    }
                    else
                    {
                        String validatedName = String.Empty;
                        if (PNNameOfStationConverter.EncodePNNameOfStation(
                                AttributeUtilities.GetName(mediaRedDomain), null, null, out validatedName)
                            != PNNameOfStationConverter.DnsNameConvertStatus.NoError)
                        {
                            Debug.Fail("unconvertible Domain Name.");
                        }

                        mrpInstanceDataAdjustBlock.MrpDomainName = (string.IsNullOrEmpty(validatedName))
                            ? s_MrpDomainDefaultName
                            : validatedName;
                    }
                }
                else
                {
                    mrpInstanceDataAdjustBlock.MrpDomainName = s_MrpDomainDefaultName;
                }

                #region SubBlocks

                PNMrpRole = MultipleMrpUtilities.GetMultipleRole(interfaceSubmodule, i + 1);

                switch (PNMrpRole)
                {
                    case PNMrpRole.NotInRing:
                        if (i != 0)
                        {
                            continue;
                        }
                        mrpInstanceDataAdjustBlock.MrpRole = 0;
                        mrpDataAdjustBlock.AddSubblock(mrpInstanceDataAdjustBlock.ToByteArray);
                        numberOfActiveIsntances++;
                        break;

                    case PNMrpRole.Client:
                        mrpInstanceDataAdjustBlock.MrpRole = 1;

                        //generate MrpClientParams-0x0217 SubBlock
                        MrpClientParamsSubBlockStruct clientParams = new MrpClientParamsSubBlockStruct();
                        clientParams.MrpLNKdownT = 20;
                        clientParams.MrpLNKupT = 20;
                        clientParams.MrpLNKNRmax = 4;
                        //append MrpClientParams-0x0217 SubBlock
                        mrpInstanceDataAdjustBlock.AddSubblock(clientParams.ToByteArray);
                        mrpDataAdjustBlock.AddSubblock(mrpInstanceDataAdjustBlock.ToByteArray);
                        numberOfActiveIsntances++;

                        //don't a generate MrpRTModeClientData-0x021D SubBlock (MRRT not supported)
                        break;

                    case PNMrpRole.Manager:
                        mrpInstanceDataAdjustBlock.MrpRole = 2;

                        //generate MrpManagerParams-0x0216 SubBlock
                        MrpManagerParamsSubBlockStruct managerParams = new MrpManagerParamsSubBlockStruct();
                        managerParams.MrpPrio =
                            interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnMrpManagerPriority,
                                ac.GetNew(),
                                0x8000);
                        if (!ac.IsOkay)
                        {
                            int vendorId =
                                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                    InternalAttributeNames.PnVendorId,
                                    new AttributeAccessCode(),
                                    0x2A);
                            if (vendorId == 0x2A)
                            {
                                ac.Reset();
                                uint operatingMode =
                                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                                        InternalAttributeNames.PnIoOperatingMode,
                                        ac,
                                        0);
                                if (ac.IsOkay)
                                {
                                    if (operatingMode == 2)
                                    {
                                        managerParams.MrpPrio = 0xA000;
                                    }
                                    else
                                    {
                                        managerParams.MrpPrio = 0x9000;
                                    }
                                }
                            }
                        }

                        managerParams.MrpTOPchgT = 1;
                        managerParams.MrpTOPNRmax = 3;
                        managerParams.MrpTSTshortT = 10;
                        managerParams.MrpTSTdefaultT = 20;
                        managerParams.MrpTSTNRmax = 3;
                        //append MrpManagerParams-0x0216 SubBlock
                        mrpInstanceDataAdjustBlock.AddSubblock(managerParams.ToByteArray);
                        mrpDataAdjustBlock.AddSubblock(mrpInstanceDataAdjustBlock.ToByteArray);
                        numberOfActiveIsntances++;

                        //Don't generate a MrpRTModeManagerData-0x0218 SubBlock (MRRT not supported)
                        break;

                    case PNMrpRole.NormManagerAuto:
                        mrpInstanceDataAdjustBlock.MrpRole = 3;

                        //generate MrpManagerParams-0x0216 SubBlock
                        MrpManagerParamsSubBlockStruct managerAutoParams = new MrpManagerParamsSubBlockStruct();
                        managerAutoParams.MrpPrio =
                            interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnMrpManagerPriority,
                                ac.GetNew(),
                                0x8000);
                        if (!ac.IsOkay)
                        {
                            int vendorId =
                                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                    InternalAttributeNames.PnVendorId,
                                    new AttributeAccessCode(),
                                    0x2A);
                            if (vendorId == 0x2A)
                            {
                                ac.Reset();
                                uint operatingMode =
                                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                                        InternalAttributeNames.PnIoOperatingMode,
                                        ac,
                                        0);
                                if (ac.IsOkay)
                                {
                                    if (operatingMode == 2)
                                    {
                                        managerAutoParams.MrpPrio = 0xA000;
                                    }
                                    else
                                    {
                                        managerAutoParams.MrpPrio = 0x9000;
                                    }
                                }
                            }
                        }

                        managerAutoParams.MrpTOPchgT = 1;
                        managerAutoParams.MrpTOPNRmax = 3;
                        managerAutoParams.MrpTSTshortT = 10;
                        managerAutoParams.MrpTSTdefaultT = 20;
                        managerAutoParams.MrpTSTNRmax = 3;
                        //append MrpManagerParams-0x0216 SubBlock
                        mrpInstanceDataAdjustBlock.AddSubblock(managerAutoParams.ToByteArray);
                        mrpDataAdjustBlock.AddSubblock(mrpInstanceDataAdjustBlock.ToByteArray);
                        numberOfActiveIsntances++;

                        //Don't generate a MrpRTModeManagerData-0x0218 SubBlock (MRRT not supported)
                        break;
                }

                #endregion
            }

            mrpDataAdjustBlock.NumberOfMrpInstances = numberOfActiveIsntances;
            return mrpDataAdjustBlock.ToByteArray;
        }

        /// <summary>
        /// Gets the parameter data block PDInterfaceMrpDataCheck for multiple Mrp supported interface.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose data record is generated.</param>
        /// <param name="mrpDiagnosis"></param>
        /// <returns>
        /// PDInterfaceMrpDataCheck 0x0213 parameter block if it is to be generated,
        /// otherwise an empty byte array.
        /// </returns>
        internal static byte[] GetPDInterfaceMultipleMrpDataCheck(Interface interfaceSubmodule, bool mrpDiagnosis)
        {
            PdInterfaceMrpDataCheckStructV11 multipleMrpDataCheckBlock = new PdInterfaceMrpDataCheckStructV11();

            PNMrpRole PNMrpRole = PNMrpRole.NotInRing;

            byte numberOfActiveInstances = 0;
            if (!MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule))
            {
                //Multiple Instances are not active so only one instance will be generated
                SetMrpDataCheck(
                    interfaceSubmodule,
                    ref numberOfActiveInstances,
                    PNMrpRole,
                    mrpDiagnosis,
                    multipleMrpDataCheckBlock,
                    1);
            }
            else
            {
                List<MrpDomainInstance> mrpDomainInstancesOfInterface = interfaceSubmodule.GetSortedMrpDomainInstances();
                foreach (MrpDomainInstance mrpDomainInstance in mrpDomainInstancesOfInterface)
                {
                    int instanceNumber = mrpDomainInstance.AttributeAccess
                        .GetAnyAttribute<Int32>(InternalAttributeNames.PnInstanceNumber, new AttributeAccessCode(), 0);
                    mrpDiagnosis = mrpDomainInstance.AttributeAccess
                        .GetAnyAttribute<Boolean>(InternalAttributeNames.PnInstanceDiagnostics, new AttributeAccessCode(), false);
                    SetMrpDataCheck(interfaceSubmodule, ref numberOfActiveInstances, PNMrpRole,
                        mrpDiagnosis, multipleMrpDataCheckBlock, instanceNumber, true);
                }
            }

            multipleMrpDataCheckBlock.NumberOfMrpInstances = numberOfActiveInstances;
            return multipleMrpDataCheckBlock.ToByteArray;
        }

        /// <summary>
        /// Sets the contents of the MrpDataCheck data record.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface whose data record is set.</param>
        /// <param name="numberOfActiveInstances">Number of active MRP instances.</param>
        /// <param name="PNMrpRole">MRP role of the interface.</param>
        /// <param name="mrpDiagnosis">Whether MRP diagnosis is used.</param>
        /// <param name="multipleMrpDataCheckBlock">The contents of the data record.</param>
        /// <param name="instanceNumber">MRP instance number.</param>
        internal static void SetMrpDataCheck(
            Interface interfaceSubmodule,
            ref byte numberOfActiveInstances,
            PNMrpRole PNMrpRole,
            bool mrpDiagnosis,
            PdInterfaceMrpDataCheckStructV11 multipleMrpDataCheckBlock,
            int instanceNumber,
            bool isMultipleInstancesActiveted = false)
        {
            PDInterfaceMrpDataCheckStruct mrpDataCheckBlock = new PDInterfaceMrpDataCheckStruct();

            mrpDataCheckBlock.MrpInstance = (byte)(instanceNumber - 1);

            //handle devices which just require V1.1 data blocks
            mrpDataCheckBlock.MrpDomainUUID = GenerateMrpUuid(interfaceSubmodule, isMultipleInstancesActiveted, instanceNumber);

            PNMrpRole = MultipleMrpUtilities.GetMultipleRole(interfaceSubmodule, instanceNumber);

            // Shift the instance number from 1..n range to 0..n-1 range
            if (instanceNumber > 0)
            {
                instanceNumber--;
            }

            switch (PNMrpRole)
            {
                case PNMrpRole.NotInRing:
                    if (instanceNumber == 0)
                    {
                        mrpDataCheckBlock.MrpCheckMediaRedundancyManager = false;
                        mrpDataCheckBlock.MrpCheckMrpDomainUUID = false;
                        multipleMrpDataCheckBlock.AddSubblock(mrpDataCheckBlock.ToByteArray);
                        numberOfActiveInstances++;
                    }
                    break;
                case PNMrpRole.Client:
                    mrpDataCheckBlock.MrpCheckMediaRedundancyManager = false;
                    mrpDataCheckBlock.MrpCheckMrpDomainUUID = mrpDiagnosis;
                    multipleMrpDataCheckBlock.AddSubblock(mrpDataCheckBlock.ToByteArray);
                    numberOfActiveInstances++;
                    break;
                case PNMrpRole.Manager:
                case PNMrpRole.NormManagerAuto:
                    mrpDataCheckBlock.MrpCheckMediaRedundancyManager = mrpDiagnosis;
                    mrpDataCheckBlock.MrpCheckMrpDomainUUID = mrpDiagnosis;
                    multipleMrpDataCheckBlock.AddSubblock(mrpDataCheckBlock.ToByteArray);
                    numberOfActiveInstances++;
                    break;
            }
        }

        /// <summary>
        /// Fills the PDPortMrpData adjust block multiple mrp supported interfaces ports of Config 2002 and 2003.
        /// Only filled for the ports which are part of the ring. False is returned with
        /// returnValueOk when the method is called with a port which does not support 
        /// mrp or currently is not part of the ring.
        /// </summary>
        /// <param name="portSubmodule"></param>
        /// <param name="returnValueOk">Output parameter. True, if the block can be filled 
        /// successfully.</param>
        /// <returns>Byte array which contains the Config info</returns>
        internal static byte[] GetPDPortMrpDataAdjust(DataModel.PCLObjects.Port portSubmodule, out bool returnValueOk)
        {
            if (portSubmodule == null)
            {
                returnValueOk = false;
                return new byte[0];
            }

            PDPortMrpDataAdjustStruct mrpDataAdjustBlock = new PDPortMrpDataAdjustStruct();
            int mrpInstance = MultipleMrpUtilities.GetInstanceOfPort(portSubmodule);
            returnValueOk = true;

            if (mrpInstance > 0)
            {
                //Instance count in config is started from 0
                mrpDataAdjustBlock.MrpInstance = (byte)(mrpInstance - 1);
            }
            //Norm auto manager devices and devices which maxInstances = 1 also requires V1.1 block
            else
            {
                mrpDataAdjustBlock.MrpInstance = 0;
            }

            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);

            mrpDataAdjustBlock.MrpDomainUUID = GenerateMrpUuid(interfaceSubmodule,
                MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule), mrpInstance);

            return mrpDataAdjustBlock.ToByteArray;
        }
    }
}