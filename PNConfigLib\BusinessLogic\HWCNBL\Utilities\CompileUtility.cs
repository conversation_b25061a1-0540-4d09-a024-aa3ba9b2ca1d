/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CompileUtility.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class CompileUtility
    {
        /// <summary>
        /// The method fills the Data Struct of PDSync with the relevant parameters.
        /// </summary>
        /// <param name="methodData">The corresponding Method Data</param>
        /// <param name="interfaceSubmodule">The owner of the Config</param>
        /// <param name="syncDomain">The Sync-Domain of the Interface Submodule</param>
        /// <param name="interfaceType">IOController or IODevice</param>
        /// <returns>true, if the PdSyncData block is filled</returns>
        internal static bool FillPdSyncDataStruct(
            IMethodData methodData,
            Interface interfaceSubmodule,
            SyncDomain syncDomain,
            PNInterfaceType interfaceType)
        {
            methodData.ReturnValue = false;
            byte[] block = Utility.GetPdSyncData(syncDomain, interfaceType, interfaceSubmodule);

            if ((block != null)
                && (block.Length > 0))
            {
                // Assign the output parameter of the method.
                methodData.Arguments[GetPdSyncDataParameters.PdSyncDataStructEntry] = block;
                return true;
            }

            return false;
        }

        /// <summary>
        /// The method fills the Dataset Struct of PDPortDataAdjust for Config2002 and Config2003.
        /// </summary>
        /// <param name="methodData">The corresponding Method Data</param>
        /// <param name="portModule">The owner of the Dataset</param>
        /// <param name="interfaceType"></param>
        internal static void GetPDPortDataAdjust(
            IMethodData methodData,
            DataModel.PCLObjects.Port portModule,
            PNInterfaceType interfaceType)
        {
            methodData.ReturnValue = false;
            bool isRecordwithoutHeader = false;
            for (int i = 0; i < methodData.Arguments.Count; i++)
            {
                if (methodData.Arguments.AllKeys[i] == RecordwithoutHeader.isRecordwithoutHeader)
                {
                    isRecordwithoutHeader = true;
                }
            }

            //Check if the PDPortDataAdjust parameter block is to be generated
            if (!ConfigUtility.IsPDPortDataAdjustToGenerate(portModule))
            {
                if (isRecordwithoutHeader)
                {
                    methodData.ReturnValue = null;
                }
                return;
            }

            byte[] tlvSubBlockByteArray = GetTlvSubBlockForPortDataAdjust(
                portModule,
                interfaceType);
            if (tlvSubBlockByteArray == null)
            {
                return;
            }

            if (isRecordwithoutHeader)
            {
                // assign the output parameter of the method
                methodData.ReturnValue = tlvSubBlockByteArray;
                return;
            }

            // Create PDPortDataAdjust dataset
            ParameterDatasetStruct paramDs = new ParameterDatasetStruct();
            // fill DSNumber
            paramDs.ParaDSNumber = 0x802F;
            // add SubBlock 0x0200 into DS Struct
            paramDs.AddParaBlock(tlvSubBlockByteArray);
            // assign the output parameter of the method
            methodData.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] = paramDs.ToByteArray;

            methodData.ReturnValue = true;
        }

        /// <summary>
        /// The method fills the Dataset Struct of PDPortDataCheck for Config2002 and Config2003.
        /// </summary>
        /// <param name="methodData">The corresponding Method Data</param>
        /// <param name="portModule">The owner of the Dataset</param>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="interfaceType"></param>
        internal static void GetPDPortDataCheck(
            IMethodData methodData,
            DataModel.PCLObjects.Port portModule,
            Interface interfaceSubmodule,
            PNInterfaceType interfaceType)
        {
            methodData.ReturnValue = false;

            // get InterfaceSubmodule of PortSubmodule
            if (interfaceSubmodule == null)
            {
                Debug.Fail("Interfacesubmodule can not be reached from Port:" + portModule);
                return;
            }

            //Check if PDEV
            if (!ConfigUtility.PdevCheckForPDPortData(interfaceSubmodule))
            {
                return;
            }

            //Check if the PDPortDataCheck parameter block is to be generated
            ConfigUtility.PDPortData pdPortData = new ConfigUtility.PDPortData();
            if (!ConfigUtility.IsPDPortDataCheckToGenerate(portModule, interfaceType, pdPortData))
            {
                methodData.ReturnValue = null;
                return;
            }

            byte[] tlvSubBlockByteArray = GetTlvSubBlockForPortDataCheck(
                portModule,
                interfaceType,
                pdPortData);
            if (tlvSubBlockByteArray == null)
            {
                methodData.ReturnValue = false;
            }
            // assign the output parameter of the method
            methodData.ReturnValue = true;
            methodData.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] = tlvSubBlockByteArray;
        }

        internal static byte[] GetPdPortFoDataAdjust(DataModel.PCLObjects.Port portModule)
        {
            PDPortFODataAdjustStruct pdPortFoDataAdjust = new PDPortFODataAdjustStruct();

            //default values
            pdPortFoDataAdjust.FiberOpticType = 0x00000004;
            pdPortFoDataAdjust.FiberOpticCableType = 0x00000001;

            AttributeAccessCode accessCode = new AttributeAccessCode();
            long value =
                portModule.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnFiberOptic,
                    accessCode,
                    0);

            if (accessCode.IsOkay)
            {
                pdPortFoDataAdjust.FiberOpticType =
                    (uint)((Convert.ToUInt64(value, CultureInfo.InvariantCulture) & 0xFFFFFFFF00000000) >> 32);
                pdPortFoDataAdjust.FiberOpticCableType = (uint)(value & 0x00000000FFFFFFFF);
            }
            return pdPortFoDataAdjust.ToByteArray;
        }

        /// <summary>
        /// The method fills the Dataset Struct of PDPortFODataAdjust for Config2002 and Config2003.
        /// </summary>
        /// <param name="methodData"></param>
        /// <param name="portModule">The owner of the Dataset</param>
        /// <param name="interfaceSubmodule"></param>
        internal static void GetPDPortFoDataAdjust(
            IMethodData methodData,
            DataModel.PCLObjects.Port portModule,
            Interface interfaceSubmodule)
        {
            // Initialize the return value.
            methodData.ReturnValue = false;

            if (ConfigUtility.IsFoPortActivated(portModule))
            {
                // get InterfaceSubmodule to PortSubmodule
                if (interfaceSubmodule == null)
                {
                    Debug.Fail("Interfacesubmodule can not be reached from Port:" + portModule);
                    return;
                }

                // Create PDPortFODataAdjust parameter block Struct
                byte[] pdPortFoDataAdjust = GetPdPortFoDataAdjust(portModule);
                // init PDPortFODataAdjust dataset
                ParameterDatasetStruct paramDs = null;

                // Check if the InterfaceSubmodule of the Port is a Device InterfaceSubmodule
                // create dataset with Motorole head format
                if (interfaceSubmodule.ParentObject is DecentralDevice)
                {
                    paramDs = new ParameterDatasetStruct();
                }

                // fill Dataset header informtation
                if (paramDs != null)
                {
                    paramDs.ParaDSNumber = 0x8062;
                    paramDs.ParaDSIdentifier = 0;

                    // append PDPortFODataAdjust parameter block to dataset
                    paramDs.AddParaBlock(pdPortFoDataAdjust);

                    // Assign the output parameter of the method
                    methodData.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] = paramDs.ToByteArray;
                }

                methodData.ReturnValue = true;
            }
        }

        internal static byte[] GetPdPortFoDataCheck()
        {
            PDPortFODataCheckStruct pdPortFoDataCheck = new PDPortFODataCheckStruct();
            pdPortFoDataCheck.MaintenanceRequiredPowerBudget = 0x80000014;
            pdPortFoDataCheck.MaintenanceDemandedPowerBudget = 0x80000000;
            pdPortFoDataCheck.ErrorPowerBudget = 0;
            return pdPortFoDataCheck.ToByteArray;
        }

        /// <summary>
        /// The method fills the Dataset Struct of PDPortFODataCheck for Config2002 and Config2003.
        /// </summary>
        /// <param name="methodData"></param>
        /// <param name="portModule">The owner of the Dataset</param>
        internal static void GetPDPortFoDataCheck(IMethodData methodData, DataModel.PCLObjects.Port portModule)
        {
            //// Initialize the return value.
            methodData.ReturnValue = false;

            if (ConfigUtility.IsFoPortActivated(portModule))
            {
                // check whether Power Budget Control is supported and 
                // port "Automatic settings (monitor)" mode is selected
                if (ConfigUtility.IsPDPortFoDataCheckToGenerate(portModule))
                {
                    // get InterfaceSubmodule to PortSubmodule
                    Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portModule);
                    if (interfaceSubmodule == null)
                    {
                        Debug.Fail("Interfacesubmodule can not be reached from Port:" + portModule);
                        return;
                    }

                    // Create PDPortFODataCheck parameter block Struct
                    byte[] pdPortFoDataCheck = GetPdPortFoDataCheck();
                    methodData.ReturnValue = pdPortFoDataCheck;
                }
            }
        }

        /// <summary>
        /// The method fills the TLVSubBlock for PDPortDataAdjust
        /// </summary>
        internal static byte[] GetTlvSubBlockForPortDataAdjust(
            DataModel.PCLObjects.Port portModule,
            PNInterfaceType interfaceType)
        {
            //get Module of PortSubmodule
            PclObject module = NavigationUtilities.GetContainer(portModule);

            if (module == null)
            {
                Debug.Fail("Parent can not be reached from Port:" + portModule);
                return null;
            }

            int slotNumber = ConfigUtility.GetSlotNumberOfPort(portModule);

            AttributeAccessCode ac = new AttributeAccessCode();

            bool isPortDeactivated =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortDeactivated,
                    ac.GetNew(),
                    false);

            bool isAdjustMauTypeSubBlockToGenerate = ConfigUtility.IsAdjustMauTypeSubBlockToGenerate(portModule);

            uint valueIrtPortSyncDomainBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortSyncDomainBoundary,
                    new AttributeAccessCode(),
                    0);

            bool isIrtPortSyncDomainBoundarySet = (valueIrtPortSyncDomainBoundary > 0)
                                                  && ConfigUtility.IsAdjustDomainBoundaryToGenerate(portModule);

            uint valuePNDcpBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnDCPBoundary,
                    new AttributeAccessCode(),
                    0);
            bool isDcpBoundarySet = valuePNDcpBoundary > 0;

            uint valuePNPTPBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPTPBoundary,
                    new AttributeAccessCode(),
                    0);
            bool isPeerToPeerBoundarySet = valuePNPTPBoundary > 0;

            //generate TLVSubBlock 0x0202
            TlvParamSubBlockStruct tlvSubBlock = new TlvParamSubBlockStruct();
            tlvSubBlock.ParaBlockType = 0x0202;
            tlvSubBlock.TLVParamSlotNumber = (ushort)slotNumber;
            tlvSubBlock.TLVParamSubSlotNumber =
                (ushort)
                portModule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    0);

            //generate AdjustDomainBoundary-0x0209 SubBlock
            if (isIrtPortSyncDomainBoundarySet)
            {
                AdjustDomainBoundarySubBlockStruct adjustDomainBoundarySubBlock =
                    new AdjustDomainBoundarySubBlockStruct();
                // Filter for incoming and outgoing Multicast MAC addresses 
                // 01-0E-CF-00-04-00, 01-0E-CF-00-04-20, 01-0E-CF-00-04-40 and
                // 01-0E-CF-00-04-80, -> Bit 0 = 1
                adjustDomainBoundarySubBlock.DomainBoundaryIngress = 1;
                adjustDomainBoundarySubBlock.DomainBoundaryEgress = 1;
                adjustDomainBoundarySubBlock.AdjustProperties = 0;

                //append AdjustDomainBoundary-0x0209 SubBlock
                tlvSubBlock.AddSubblock(adjustDomainBoundarySubBlock.ToByteArray);
            }

            //generate AdjustMAUType-0x020E SubBlock
            if (isAdjustMauTypeSubBlockToGenerate)
            {
                AdjustMauTypeSubBlockStruct adjustMauTypeSubBlock = new AdjustMauTypeSubBlockStruct();
                adjustMauTypeSubBlock.ParaBlockType = 0x020E;
                adjustMauTypeSubBlock.MAUType =
                    (int)
                    portModule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnEthernetMediumDuplex,
                        new AttributeAccessCode(),
                        0);
                adjustMauTypeSubBlock.AdjustProperties = 0;
                //append AdjustMAUType-0x020E SubBlock
                tlvSubBlock.AddSubblock(adjustMauTypeSubBlock.ToByteArray);
            }
            //generate AdjustLinkState-0x021B SubBlock
            if (isPortDeactivated)
            {
                AdjustMauTypeSubBlockStruct adjustLinkStateSubBlock = new AdjustMauTypeSubBlockStruct();
                adjustLinkStateSubBlock.ParaBlockType = 0x021B;
                adjustLinkStateSubBlock.MAUType = 2; // LinkState.Link = 0x02 ("Down")
                adjustLinkStateSubBlock.AdjustProperties = 0;
                //append AdjustLinkState-0x021B SubBlock
                tlvSubBlock.AddSubblock(adjustLinkStateSubBlock.ToByteArray);
            }

            //generate AdjustPeerToPeerBoundary-0x0224 SubBlock
            if (!isPortDeactivated && isPeerToPeerBoundarySet)
            {
                AdjustPeerToPeerBoundarySubBlockStruct adjustPeerToPeerBoundarySubBlock =
                    new AdjustPeerToPeerBoundarySubBlockStruct();

                //Bit0: The LLDP agent shall not send LLDP frames (egress filter).
                uint peerToPeerBoundary = 1;

                bool isDelayMeasurementSupported =
                    NavigationUtilities.GetInterfaceOfPort(portModule)
                        .AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnDelayMeasurementSupported,
                            new AttributeAccessCode(),
                            false);

                List<PNIRTSupportedSyncProtocols> supportedSyncProtocols = ConfigUtility.GetSuppSyncProtocols(module);

                if (isDelayMeasurementSupported || supportedSyncProtocols.Contains(PNIRTSupportedSyncProtocols.RTSync))
                {
                    //Bit1: The PTCP ASE shall not send PTCP_DELAY frames (egress filter).
                    peerToPeerBoundary += 2;
                }

                adjustPeerToPeerBoundarySubBlock.PeerToPeerBoundary = peerToPeerBoundary;
                //append AdjustPeerToPeerBoundary-0x0224 SubBlock
                tlvSubBlock.AddSubblock(adjustPeerToPeerBoundarySubBlock.ToByteArray);
            }

            //generate AdjustDCPBoundary-0x0225 SubBlock
            if (!isPortDeactivated && isDcpBoundarySet)
            {
                AdjustDcpBoundarySubBlockStruct adjustDcpBoundarySubBlock = new AdjustDcpBoundarySubBlockStruct();
                adjustDcpBoundarySubBlock.DCPBoundary = 3; // DCP_Identify and DCP_Hello are set!
                adjustDcpBoundarySubBlock.AdjustProperties = 0;
                //append AdjustDCPBoundary-0x0225 SubBlock
                tlvSubBlock.AddSubblock(adjustDcpBoundarySubBlock.ToByteArray);
            }

            bool generateAdjustPreambleLength = true;

            generateAdjustPreambleLength &=
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtGenerateAdjustPreambleLengthV14,
                    ac.GetNew(),
                    false);
            generateAdjustPreambleLength &= !isPortDeactivated;

            if (generateAdjustPreambleLength)
            {
                // Generate adjust preamble length subblock
                AdjustPreambleLengthSubBlockStruct adjustPreambleLengthSubBlock =
                    new AdjustPreambleLengthSubBlockStruct();
                adjustPreambleLengthSubBlock.PreambleLength =
                    portModule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtUsingShortPreamble,
                        ac.GetNew(),
                        false)
                        ? 0x01
                        : 0x00;
                tlvSubBlock.AddSubblock(adjustPreambleLengthSubBlock.ToByteArray);
            }

            return tlvSubBlock.ToByteArray;
        }

        internal static byte[] GetTlvSubBlockForPortDataCheck(
            DataModel.PCLObjects.Port portModule,
            PNInterfaceType interfaceType,
            ConfigUtility.PDPortData pdPortData)
        {
            int slotNumber = ConfigUtility.GetSlotNumberOfPort(portModule);

            //generate TLVSubBlock 0x0200
            TlvParamSubBlockStruct tlvSubBlock = new TlvParamSubBlockStruct();
            tlvSubBlock.ParaBlockType = 0x0200;
            tlvSubBlock.TLVParamSlotNumber = (ushort)slotNumber;
            tlvSubBlock.TLVParamSubSlotNumber =
                (ushort)
                portModule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    0);

            //Getting the partner ports
            List<DataModel.PCLObjects.Port> connectedPorts = (List<DataModel.PCLObjects.Port>)portModule.GetPartnerPorts();

            tlvSubBlock.AddSubblock(ConfigUtility.CreateCheckPeersBlock(portModule, pdPortData, connectedPorts));
            tlvSubBlock.AddSubblock(
                ConfigUtility.CreateLineDelayBlock(ref pdPortData, ref portModule, ref connectedPorts));
            tlvSubBlock.AddSubblock(ConfigUtility.CreateMauTypeBlock(ref pdPortData));
            tlvSubBlock.AddSubblock(ConfigUtility.CreateLinkStateBlock(ref pdPortData));
            tlvSubBlock.AddSubblock(ConfigUtility.CreateSyncDifferenceBlock(ref pdPortData));
            tlvSubBlock.AddSubblock(ConfigUtility.CreateMauTypeDifferenceBlock(ref pdPortData));

            return tlvSubBlock.ToByteArray;
        }
    }
}