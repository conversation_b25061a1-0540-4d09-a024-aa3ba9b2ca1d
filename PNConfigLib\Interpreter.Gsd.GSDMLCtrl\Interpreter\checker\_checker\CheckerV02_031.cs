/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_031.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;

using GSDI;
using PNConfigLib.Gsd.Interpreter;
using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.31 and is based on GSD(ML) versions 2.3 and lower.
    ///		
    /// </summary>
    internal class CheckerV02031 : CheckerV0203
    {
        #region Fields

        protected const uint s_GuaranteedIoDataFor4ByteCrcps26 = 13;

        #endregion

        #region Properties

        protected override GSDI.ReportTypes ReportType_0X000100093 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000100166 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X00010026 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X0001002C => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101041 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101042 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000101052 => GSDI.ReportTypes.GSD_RT_Error;

        protected virtual GSDI.ReportTypes ReportType_0X0003002C => GSDI.ReportTypes.GSD_RT_MinorError;

        protected override GSDI.ReportTypes ReportType_0x00020021_4 => GSDI.ReportTypes.GSD_RT_Error;

        protected virtual GSDI.ReportTypes ReportType_0X00030032 => GSDI.ReportTypes.GSD_RT_MinorError;


        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:MediaRedundancy/@SupportedMultipleRole";
                return (xp);
            }
        }

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02031;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02031;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00030003);
            Checks.Add(Constants.s_Cn_0X00030004);
            Checks.Add(Constants.s_Cn_0X00030005);
            Checks.Add(Constants.s_Cn_0X00030007);
            Checks.Add(Constants.s_Cn_0X00030008);
            Checks.Add(Constants.s_Cn_0X00030009);
            Checks.Add(Constants.s_Cn_0X00030011);
            Checks.Add(Constants.s_Cn_0X00030012);
            Checks.Add(Constants.s_Cn_0X00030013);
            Checks.Add(Constants.s_Cn_0X00030014);
            Checks.Add(Constants.s_Cn_0X00030015);
            Checks.Add(Constants.s_Cn_0X00030020);
            Checks.Add(Constants.s_Cn_0X0003002A);
            Checks.Add(Constants.s_Cn_0X0003002B);
            Checks.Add(Constants.s_Cn_0X0003002C);
            Checks.Add(Constants.s_Cn_0X0003002D);
            Checks.Add(Constants.s_Cn_0X0003002E);
            Checks.Add(Constants.s_Cn_0X0003002F);
            Checks.Add(Constants.s_Cn_0X00030030);
            Checks.Add(Constants.s_Cn_0X00030031);
            Checks.Add(Constants.s_Cn_0X00030032);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                Checks.Remove(Constants.s_Cn_0X0001002F);
                Checks.Remove(Constants.s_Cn_0X00010033);
                Checks.Remove(Constants.s_Cn_0X0001119B);
                Checks.Remove(Constants.s_Cn_0X000111A2);
                Checks.Remove(Constants.s_Cn_0X000111A3);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.31.
        /// </summary>
        public CheckerV02031()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version231);
        }

        #endregion

        #region Methods

        /// <summary>
        /// The minimum input or output data size which can be used with '4-Byte-CRC' depends on the PROFIsafe Version:
        ///   - &lt;  2.6:  12 bytes
        ///   - &gt;= 2.6:  13 bytes
        /// </summary>
        /// <param name="fparameterRecordData"></param>
        /// <returns>min input and output data size</returns>
        protected override uint GetGuaranteedIoSizeFor4ByteCrc(XElement fparameterRecordData)
        {
            uint length = s_GuaranteedIoDataFor4ByteCrc;

            if (IsProfiSafeV26(fparameterRecordData))
            {
                length = s_GuaranteedIoDataFor4ByteCrcps26;
            }

            return length;
        }

        /// <summary>
        /// When using PROFIsafe V2.6.1 the PROFIsafe V2.6.1 elements F_Passivation and F_CRC_Seed must be present.
        /// If only one of the elements is present, the use of PROFIsafe V2.6.1 is assumed.
        /// The error message will be generated in another check.
        /// </summary>
        protected override bool IsProfiSafeV26(XElement fparameterRecordData)
        {

            var node = fparameterRecordData.Element(NamespaceGsdDef + Elements.s_FPassivation);
            if (node != null)
            {
                return true;
            }

            node = fparameterRecordData.Element(NamespaceGsdDef + Elements.s_FCrcSeed);
            if (node != null)
            {
                return true;
            }

            return false;
        }

        protected override void CollectBytesFParamDescCrc(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // F_Check_iPar
            FParamDescCrcForFCheckIPar(fparamRecord, calcFParamDescCrc);

            // F_SIL
            FParamDescCrcForFSil(fparamRecord, calcFParamDescCrc);

            // F_CRC_Length
            FParamDescCrcForFCrcLength(fparamRecord, calcFParamDescCrc);

            // F_CRC_Seed
            FParamDescCrcForFCrcSeed(fparamRecord, calcFParamDescCrc);

            // F_Passivation
            FParamDescCrcForFPassivation(fparamRecord, calcFParamDescCrc);

            // F_Block_ID
            FParamDescCrcForFBlockID(fparamRecord, calcFParamDescCrc);

            // F_Par_Version
            FParamDescCrcForFParVersion(fparamRecord, calcFParamDescCrc);

            // F_Source_Add
            FParamDescCrcForFSourceAdd(fparamRecord, calcFParamDescCrc);

            // F_Dest_Add
            FParamDescCrcForFDestAdd(fparamRecord, calcFParamDescCrc);

            // F_WD_Time
            FParamDescCrcForFWdTime(fparamRecord, calcFParamDescCrc);

            // F_iPar_CRC
            FParamDescCrcForFIParCrc(fparamRecord, calcFParamDescCrc);

            // F_Par_CRC
            FParamDescCrcForFParCrc(fparamRecord, calcFParamDescCrc);
        }

        /// <summary>
        /// Calculates the sum of channels of a list of DataItems with a specific data type. Qualifier bits are
        /// excluded.
        /// </summary>
        /// <param name="dataItems"></param>
        /// <param name="dataType"></param>
        /// <returns>sum of channels</returns>
        protected override int GetChannelsAnalog(IList<XElement> dataItems, string dataType)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                if (subordinate)
                {
                    // Do not count Q-Channels
                    continue;
                }

                string dt = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                if (dt == dataType)
                {
                    allDataItemsLen++;
                }
            }

            return allDataItemsLen;
        }


        /// <summary>
        /// Get the sum of all Unsigned channel sizes in bytes. Qualifier bits are excluded
        /// </summary>
        /// <param name="dataItems"></param>
        /// <returns></returns>
        protected override int GetBytesBoolMax(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            foreach (var dataItem in dataItems)
            {
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                if (subordinate)
                {
                    // Do not count Q-Channels
                    continue;
                }

                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                if (dataType == Enums.s_Unsigned8 || dataType == Enums.s_Unsigned16 || dataType == Enums.s_Unsigned32)
                {
                    int dataTypeLen = GetDataTypeChannelSize(dataType);
                    allDataItemsLen += dataTypeLen;
                }
            }

            return allDataItemsLen;
        }

        /// <summary>
        /// Get the sum of all Unsigned channel sizes in bits
        /// </summary>
        /// <param name="dataItems"></param>
        /// <returns></returns>
        protected override int GetChannelsBoolMax(IList<XElement> dataItems)
        {
            int allDataItemsLen = 0;

            if (dataItems == null)
                return allDataItemsLen;

            List<XElement> dataItemsUnsignedXX = GetDataItemsUnsigned(dataItems);
            allDataItemsLen = CalculateDataItemLength(dataItemsUnsignedXX, allDataItemsLen);

            return allDataItemsLen;

        }

        private int CalculateDataItemLength(List<XElement> dataItemsUnsignedXx, int allDataItemsLen)
        {
            int noOfItem = 0;
            foreach (var dataItem in dataItemsUnsignedXx)
            {
                noOfItem++;

                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                int dataTypeLen = GetDataTypeChannelSize(dataType);

                // If the data item is not the last one, always sum up 8 channels per byte
                if (noOfItem < dataItemsUnsignedXx.Count)
                {
                    allDataItemsLen += dataTypeLen * 8;
                    continue;
                }

                var bitDataItems = dataItem.Elements(NamespaceGsdDef + Elements.s_BitDataItem).ToList();
                List<UInt16> bitOffsets = new();
                foreach (var bitDataItem in bitDataItems)
                    bitOffsets.Add(XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(bitDataItem, Attributes.s_BitOffset)));

                // Also for the last data item always sum up 8 channels per byte except for the last byte
                if (dataTypeLen == 2)
                {
                    allDataItemsLen += 8;
                }
                else if (dataTypeLen == 4)
                {
                    allDataItemsLen += 24;
                }

                // For the last byte the most significant used bits count

                // If no bit data items given, count the whole byte
                if (bitDataItems.Count == 0)
                {
                    allDataItemsLen += 8;
                    continue;
                }

                allDataItemsLen = CountUsedBits(allDataItemsLen, bitOffsets);
            }

            return allDataItemsLen;
        }
        private static int CountUsedBits(int allDataItemsLen, ICollection<ushort> bitOffsets)
        {
            // Count the used bits
            UInt16 j = 7;
            for (; j >= 0; j--)
            {
                if (bitOffsets.Contains(j))
                    break;
                if (j == 0)
                    break;
            }
            if (j != 0 || bitOffsets.Contains(j))
                allDataItemsLen += j + 1;
            return allDataItemsLen;
        }
        private static List<XElement> GetDataItemsUnsigned(IList<XElement> dataItems)
        {
            List<XElement> dataItemsUnsignedXx = new List<XElement>();

            // Only Usigned8, Unsigned16, Unsigned32 can be used as boolean channels.
            // Save the relevant data items to a list
            foreach (var dataItem in dataItems)
            {
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                if (subordinate)
                {
                    // Do not count Q-Channels
                    continue;
                }

                // Get the data type of item
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);

                // Save items with DataType "UnsignedXX"
                if (dataType == Enums.s_Unsigned8
                    || dataType == Enums.s_Unsigned16
                    || dataType == Enums.s_Unsigned32)
                    dataItemsUnsignedXx.Add(dataItem);
            }
            return dataItemsUnsignedXx;
        }

        protected virtual IList<int> GetQBits(IList<XElement> dataItems)
        {
            IList<int> qbits = new List<int>();
            foreach (var dataItem in dataItems)
            {
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                if (subordinate)
                {
                    var bitDataItems = dataItem.Elements(NamespaceGsdDef + Elements.s_BitDataItem).ToList();
                    if (bitDataItems.Count > 0)
                    {
                        qbits.Add(bitDataItems.Count);
                    }
                    else
                    {
                        qbits.Add(GetDataTypeChannelSize(Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType)) * 8);
                    }

                }
            }
            return qbits;
        }


        /// <summary>
        /// Check IsRIOforFAsupported attribute
        /// 
        /// </summary>
        /// <returns>True, if RIOforFA supported.</returns>
        protected override bool IsRioForFaSupported(IList<XElement> dataItems)
        {
            if (dataItems == null)
                return false;

            foreach (var dataItem in dataItems)
            {
                string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                bool subordinate = false;
                if (!string.IsNullOrEmpty(subordinateStr))
                    subordinate = XmlConvert.ToBoolean(subordinateStr);
                if (subordinate)
                    return true;
            }

            return false;
        }

        #endregion

        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("Request", "API,+APStructureIdentifier");
            ElementDescriptions.Add("SystemDefinedChannelDiagItem", "ErrorType");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = AttributeTokenDictionary["SupportedRole"];
            tokens1.Add("Manager (Auto)");

            var tokens2 = AttributeTokenDictionary["ApplicationClass"];
            tokens2.Add("HighPerformance");
            tokens2.Add("FunctionalSafety");

            var tokens3 = new List<string>();
            tokens3.Add("Manager (Auto)");
            tokens3.Add("Manager");
            tokens3.Add("Client");
            AttributeTokenDictionary.Add("SupportedMultipleRole", tokens3);
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x0001002E
        /// From GSDML V2.31 on the GSDML Schema checks, that the index is in a correct range.
        /// Only the dependency to API must be done via check here. 
        /// (2) For ParameterRecordDataItem:
        ///     Checks that if the index is in a range between 0xB000 (45056) and 0xBFFF (49151),
        ///     the corresponding submodule is defined in an API <> 0.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X0001002E()
        {
            var nl =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ParameterRecordDataItem).Attributes(Attributes.s_Index);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var an in nl)
            {
                UInt16 index = XmlConvert.ToUInt16(an.Value);
                var submoduleItem = an.Parent.Parent.Parent;
                string apiStr = Help.GetAttributeValueFromXElement(submoduleItem, Attributes.s_Api);
                UInt32 api = 0;
                if (!string.IsNullOrEmpty(apiStr))
                {
                    api = XmlConvert.ToUInt32(apiStr);
                }
                // (2)
                if (index > 45056 && index < 49151)
                {
                    if (api == 0)
                    {
                        // "Profile specific records (index in a range between 0xB000 (45056) and 0xBFFF (49151))
                        // may only be used if the corresponding submodule is defined in an 'API' <> 0."
                        string msg = Help.GetMessageString("M_0x0001002E_2");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x0001002E_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check for attribute SupportedMultipleRole in dependence to attribute MaxMRP_Instances.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00030003()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MediaRedundancy);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var an in nl)
            {
                string valueSupportedMultipleRoles = string.Empty;
                uint valueMaxMRPInstances = 0;
                var supportedMultipleRoles = an.Attribute("SupportedMultipleRole");
                if (supportedMultipleRoles != null)
                {
                    valueSupportedMultipleRoles = supportedMultipleRoles.Value;
                }
                var maxMRPInstances = an.Attribute("MaxMRP_Instances");
                if (maxMRPInstances != null)
                {
                    string stringValue = maxMRPInstances.Value;
                    valueMaxMRPInstances = XmlConvert.ToUInt16(stringValue);
                }
                if (!String.IsNullOrEmpty(valueSupportedMultipleRoles) && valueMaxMRPInstances < 2)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "The attribute 'SupportedMultipleRole' must only be present if the device supports
                        // more than one MRP-instance (attribute 'MaxMRP_Instances')."
                        string msg = Help.GetMessageString("M_0x00030003_1");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00030003_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// PDEV_CombinedObjectSupported must be present and "true"
        /// if SystemRedundancy is present and DeviceType is 'R1' or 'R2'.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>none</returns>
        protected virtual void CheckPDEVCombinedObjectSupportedAgainstSystemRedundancy(XElement dap)
        {
            if (DapRedundancySupported(dap))
            {
                // "'PDEV_CombinedObjectSupported' shall be set to "true" if 'DeviceAccessPointItem/SystemRedundancy/@DeviceType' is "R2" (or "R1" from V2.32 on)."
                string msg = Help.GetMessageString("M_0x00030004_3");
                string xpath = Help.GetXPath(dap);
                var lineInfoDap = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfoDap.LineNumber, lineInfoDap.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030004_3");
            }
        }

        protected virtual void CheckPDEVCombinedObjectSupportedAgainstApplicationClasses(XElement dap, IList<string> applicationClasses)
        {
            double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

            if (pnioVersion >= 2.31 && applicationClasses.Contains("ProcessAutomation"))
            {
                // "'PDEV_CombinedObjectSupported' shall be set to "true" if 'DeviceAccessPointItem/@PNIO_Version >= "V2.31"
                // and DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token "ProcessAutomation"."
                string msg = Help.GetMessageString("M_0x00030004_1");
                var xli = (IXmlLineInfo)dap;
                string xpath = Help.GetXPath(dap);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030004_1");
            }
        }

        /// <summary>
        /// Some additional checks for InterfaceSubmoduleItem/@PDEV_CombinedObjectSupported:
        /// 
        /// This attribute shall be present and "true" if at least one of the following conditions is met:
        /// · DeviceAccessPointItem/@PNIO_Version >= "V2.31" and DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token "ProcessAutomation"
        /// · DeviceAccessPointItem/@CIR_Supported is present and "true"
        /// · DeviceAccessPointItem/SystemRedundancy is present and DeviceAccessPointItem/SystemRedundancy/@DeviceType is "R1" or "R2"
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00030004()
        {
            // Find all interface submodules with PDEV_CombinedObjectSupported = "false"
            var submodules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            submodules = Help.TryRemoveXElementsUnderXsAny(submodules, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in submodules)
            {
                string strPdevCombinedObjectSupported =
                    Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_PdevCombinedObjectSupported);
                bool bPdevCombinedObjectSupported = false;
                if (!string.IsNullOrEmpty(strPdevCombinedObjectSupported))
                    bPdevCombinedObjectSupported = XmlConvert.ToBoolean(strPdevCombinedObjectSupported);
                if (bPdevCombinedObjectSupported)
                {
                    continue;
                }
                var dap = interfaceSubmoduleItem.Parent.Parent;

                IList<string> applicationClasses = GetCombinedApplicationClasses(dap);

                CheckPDEVCombinedObjectSupportedAgainstApplicationClasses(dap, applicationClasses);

                string strCirSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_CirSupported);
                bool bCirSupported = false;
                if (!string.IsNullOrEmpty(strCirSupported))
                {
                    bCirSupported = XmlConvert.ToBoolean(strCirSupported);
                }

                if (bCirSupported)
                {
                    // "'PDEV_CombinedObjectSupported' shall be set to "true" if 'DeviceAccessPointItem/@CIR_Supported' is present and "true"."
                    string msg = Help.GetMessageString("M_0x00030004_2");
                    string xpath = Help.GetXPath(dap);
                    var xli = (IXmlLineInfo)dap;
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00030004_2");
                    }
                }

                CheckPDEVCombinedObjectSupportedAgainstSystemRedundancy(dap);

            }
            return true;
        }

        /// <summary>
        /// Check InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@MaxReductionRatioIsochroneMode.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00030005()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3TimingProperties);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                List<ValueListHelper.ValueRangeT> glReductionRatioPow2 = null;
                List<ValueListHelper.ValueRangeT> glReductionRatioNonPow2 = null;
                List<ValueListHelper.ValueRangeT> glReductionRatio = null;

                var tempNode = dap.Attribute(Attributes.s_ReductionRatioPow2);
                if (tempNode != null)
                    glReductionRatioPow2 = ValueListHelper.NormalizeValueList(tempNode, Store);

                tempNode = dap.Attribute(Attributes.s_ReductionRatioNonPow2);
                if (tempNode != null)
                    glReductionRatioNonPow2 = ValueListHelper.NormalizeValueList(tempNode, Store);

                tempNode = dap.Attribute(Attributes.s_ReductionRatio);
                if (tempNode != null)
                    glReductionRatio = ValueListHelper.NormalizeValueList(tempNode, Store);
                else if ((glReductionRatioNonPow2 == null || glReductionRatioNonPow2.Count == 0) &&
                         (glReductionRatioPow2 == null || glReductionRatioPow2.Count == 0))
                {
                    string temp = "1 2 4 8 16";
                    ValueListHelper.NormalizeValueList(temp, out glReductionRatio);
                }

                CreateReport0x00030005_1(dap, glReductionRatio, glReductionRatioPow2, glReductionRatioNonPow2);
            }
            return true;
        }

        private void CreateReport0x00030005_1(
            XElement dap,
            IList<ValueListHelper.ValueRangeT> glReductionRatio,
            IList<ValueListHelper.ValueRangeT> glReductionRatioPow2,
            IList<ValueListHelper.ValueRangeT> glReductionRatioNonPow2)
        {
            string sMaxReductionRatioIsochroneMode =
                Help.GetAttributeValueFromXElement(dap, Attributes.s_MaxReductionRatioIsochroneMode);
            uint uMaxReductionRatioIsochroneMode = 1;
            if (!String.IsNullOrEmpty(sMaxReductionRatioIsochroneMode))
                uMaxReductionRatioIsochroneMode = XmlConvert.ToUInt16(sMaxReductionRatioIsochroneMode);

            if (glReductionRatio != null
                && ValueListHelper.IsValueInValueList(uMaxReductionRatioIsochroneMode, glReductionRatio))
                return;
            if (glReductionRatioPow2 != null
                && ValueListHelper.IsValueInValueList(uMaxReductionRatioIsochroneMode, glReductionRatioPow2))
                return;
            if (glReductionRatioNonPow2 != null
                && ValueListHelper.IsValueInValueList(uMaxReductionRatioIsochroneMode, glReductionRatioNonPow2))
                return;

            // "The value 'MaxReductionRatioIsochroneMode' shall be one of the values listed
            // in 'ReductionRatio', 'ReductionRatioPow2' or 'ReductionRatioNonPow2'."
            string msg = Help.GetMessageString("M_0x00030005_1");
            string xpath = Help.GetXPath(dap);
            var xli = (IXmlLineInfo)dap;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00030005_1");
        }

        #region PROFISAFE

        protected virtual void FParamDescCrcForFCrcSeed(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_CRC_Seed
            var fcrcSeed = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCrcSeed);
            if (fcrcSeed != null)
            {
                // Default value
                UInt16 dFcrSeed = Enums.s_CrcSeed2432Value;    // Default: "CRC-Seed24/32"
                string strDfcrcSeed = Help.GetAttributeValueFromXElement(fcrcSeed, Attributes.s_DefaultValue);
                if (strDfcrcSeed == Enums.s_CrcSeed16)
                    dFcrSeed = Enums.s_CrcSeed16Value;
                if (strDfcrcSeed == Enums.s_CrcSeed2432)
                    dFcrSeed = Enums.s_CrcSeed2432Value;

                byte[] data = new byte[]
                {
                    (byte)'F',(byte)'_',(byte)'C',(byte)'R',(byte)'C',(byte)'_',
                    (byte)'S',(byte)'e',(byte)'e',(byte)'d',
                    0x00, 0x06,  // Type=BitArea, Offset=6
                    (byte)(dFcrSeed), (byte)(dFcrSeed >> 8)  // Default value
                };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);

                // Allowed values
                string strAfcrcSeed = Help.GetAttributeValueFromXElement(fcrcSeed, Attributes.s_AllowedValues);
                if (String.IsNullOrEmpty(strAfcrcSeed))
                    strAfcrcSeed = "CRC-Seed24/32";  // Default value

                if (!strAfcrcSeed.Contains(Constants.s_Space))
                {
                    UInt16 aFcrcSeed = Enums.s_CrcSeed16Value;
                    if (strAfcrcSeed == Enums.s_CrcSeed2432)
                        aFcrcSeed = Enums.s_CrcSeed2432Value;

                    byte[] data1 = new byte[]
                    {
                        (byte)(aFcrcSeed), (byte)(aFcrcSeed >> 8),  // Min value
                        (byte)(aFcrcSeed), (byte)(aFcrcSeed >> 8)   // Max value
                    };

                    // Add the byte array to the CRC
                    calcFParamDescCrc.UpdateChecksum(data1);
                }
                else
                {
                    if (strAfcrcSeed.Contains(Enums.s_CrcSeed16))
                    {
                        byte[] data1 = new byte[]
                        {
                            (byte)'C',(byte)'R',(byte)'C',(byte)'-',(byte)'S',(byte)'e',
                            (byte)'e',(byte)'d',(byte)'1',(byte)'6',
                            (Enums.s_CrcSeed16Value), (Enums.s_CrcSeed16Value >> 8)  // Value
                        };

                        // Add the byte array to the CRC
                        calcFParamDescCrc.UpdateChecksum(data1);
                    }

                    if (strAfcrcSeed.Contains(Enums.s_CrcSeed2432))
                    {
                        byte[] data1 = new byte[]
                        {

                            (byte)'C',(byte)'R',(byte)'C',(byte)'-',(byte)'S',(byte)'e',
                            (byte)'e',(byte)'d',(byte)'2',(byte)'4',(byte)'/',(byte)'3',
                            (byte)'2',
                            (Enums.s_CrcSeed2432Value), (Enums.s_CrcSeed2432Value >> 8)  // Value
                        };

                        // Add the byte array to the CRC
                        calcFParamDescCrc.UpdateChecksum(data1);
                    }
                }
            }
        }

        protected virtual void FParamDescCrcForFPassivation(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // Add F_Passivation
            var fpassivation = fparamRecord.Element(NamespaceGsdDef + Elements.s_FPassivation);
            if (fpassivation != null)
            {
                // Default value
                UInt16 dFPassivation = Enums.s_DeviceModuleValue;    // Default: "Device/Module"
                string strDFPassivation = Help.GetAttributeValueFromXElement(fpassivation, Attributes.s_DefaultValue);
                if (strDFPassivation == Enums.s_DeviceModule)
                    dFPassivation = Enums.s_DeviceModuleValue;
                if (strDFPassivation == Enums.s_Channel)
                    dFPassivation = Enums.s_ChannelValue;

                byte[] data = new byte[]
                {
                    (byte)'F',(byte)'_',(byte)'P',(byte)'a',(byte)'s',(byte)'s',
                    (byte)'i',(byte)'v',(byte)'a',(byte)'t',(byte)'i',(byte)'o',
                    (byte)'n',
                    0x00, 0x00,  // Type=BitArea, Offset=0
                    (byte)(dFPassivation), (byte)(dFPassivation >> 8)  // Default value
                };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data);

                // Allowed values
                string strAfPassivation = Help.GetAttributeValueFromXElement(fpassivation, Attributes.s_AllowedValues);
                if (String.IsNullOrEmpty(strAfPassivation))
                    strAfPassivation = "Device/Module";    // Default: "Device/Module"

                UInt16 aFPassivation = Enums.s_DeviceModuleValue;
                if (strAfPassivation == Enums.s_Channel)
                    aFPassivation = Enums.s_ChannelValue;

                byte[] data1 = new byte[]
                {
                    (byte)(aFPassivation), (byte)(aFPassivation >> 8),  // Min value
                    (byte)(aFPassivation), (byte)(aFPassivation >> 8)   // Max value
                };

                // Add the byte array to the CRC
                calcFParamDescCrc.UpdateChecksum(data1);
            }
        }

        /// <summary>
        /// When using PROFIsafe V2.6.1 all PROFIsafe V2.6.1 elements and attributes must be present.
        /// </summary>
        protected virtual bool CheckCn_0X00030007()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FParameterRecordDataItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var fparamRecord in nl)
            {
                List<string> ps26List = new List<string>();

                var node = fparamRecord.Element(NamespaceGsdDef + Elements.s_FPassivation);
                if (node != null)
                {
                    ps26List.Add(Elements.s_FPassivation);
                }

                node = fparamRecord.Element(NamespaceGsdDef + Elements.s_FCrcSeed);
                if (node != null)
                {
                    ps26List.Add(Elements.s_FCrcSeed);
                }

                if (ps26List.Count > 0 && ps26List.Count != 2)
                {
                    if (Help.CheckSchemaVersion(fparamRecord, SupportedGsdmlVersion))
                    {
                        // "PROFIsafe V2.6MU1 (MU = Maintenance Update) is used but not all mandatory PROFIsafe V2.6MU1 elements or attributes are present."
                        string msg = Help.GetMessageString("M_0x00030007_1");
                        string xpath = Help.GetXPath(fparamRecord);
                        var xli = (IXmlLineInfo)fparamRecord;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00030007_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// RIOforFA submodule with PROFIsafe must use PROFIsafe V2.6.1. F_Passivation must be set to 'Channel'.
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00030008()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submoduleItem in allSubmodules)
            {
                string strProfIsafeSupported =
                    Help.GetAttributeValueFromXElement(submoduleItem, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafeSupported))
                    pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
                if (!pRofIsafeSupported)
                {
                    continue;
                }
                var lineInfo = (IXmlLineInfo)submoduleItem;

                bool bPassivationSetToChannel = false;
                var nl1 = submoduleItem.Descendants(NamespaceGsdDef + Elements.s_FPassivation).ToList();
                if (nl1.Count == 1)
                {
                    var passivation = nl1[0];
                    if (Help.GetAttributeValueFromXElement(passivation, Attributes.s_DefaultValue) == Enums.s_Channel)
                        bPassivationSetToChannel = true;
                }


                var inputDataItems = GetInputDataItems(submoduleItem);
                if (IsRioForFaSupported(inputDataItems))
                {
                    if (bPassivationSetToChannel)
                    {
                        continue;
                    }
                    // "Submodules with RIOforFA ('Input/DataItem' with 'Subordinate'="true") and PROFIsafe must use PROFIsafe Version 2.6.1
                    //  'F_Passivation' must be set to "Channel"."
                    string msg = Help.GetMessageString("M_0x00030008_1");
                    string xpath = Help.GetXPath(submoduleItem);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber,
                        lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00030008_1");

                }
                else
                {
                    if (!bPassivationSetToChannel)
                    {
                        continue;
                    }

                    // "'F_Passivation' with value "Channel" is only allowed in combination with RIOforFA ('Input/DataItem' with 'Subordinate'="true")."
                    string msg = Help.GetMessageString("M_0x00030008_2");
                    string xpath = Help.GetXPath(submoduleItem);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber,
                        lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00030008_2");

                }

            }

            return true;
        }

        /// <summary>
        /// F_Passivation: The default value must be contained in allowed values.
        /// </summary>
        /// <returns></returns>

        protected virtual bool CheckCn_0X00030009()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FPassivation);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fpassivation in nl)
            {
                string allowedValues = Help.GetAttributeValueFromXElement(fpassivation, Attributes.s_AllowedValues);
                if (String.IsNullOrEmpty(allowedValues))
                    allowedValues = Enums.s_DeviceModule;

                string defaultValue = Help.GetAttributeValueFromXElement(fpassivation, Attributes.s_DefaultValue);
                if (String.IsNullOrEmpty(defaultValue))
                    defaultValue = Enums.s_DeviceModule;

                if (!allowedValues.Contains(defaultValue))
                {
                    // "'F_ParameterRecordDataItem/F_Passivation': The default value (= {0}) is not contained in allowed values (= {1})."
                    if (Help.CheckSchemaVersion(fpassivation, SupportedGsdmlVersion))
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030009_1"), defaultValue, allowedValues);
                        string xpath = Help.GetXPath(fpassivation);
                        var xli = (IXmlLineInfo)fpassivation;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00030009_1");
                    }
                }
            }

            return true;
        }

        #endregion

        /// <summary>
        /// Checked specifically for RIOforFA: Checks if RIOforFA submodule has the correct number of Q bits.
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00030011()
        {
            var nl =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var submoduleItem in nl)
            {
                var inputDataItems = GetInputDataItems(submoduleItem);
                if (!IsRioForFaSupported(inputDataItems))
                {
                    continue;
                }

                ushort channelsInputAnalog = 0;
                ushort channelsInputDigital = 0;
                ushort channelsOutputAnalog = 0;
                ushort channelsOutputDigital = 0;

                if (inputDataItems != null && inputDataItems.Count > 0)
                {
                    channelsInputAnalog = (ushort)(GetChannelsAnalog(inputDataItems, Enums.s_Integer16) +
                                                   GetChannelsAnalog(inputDataItems, Enums.s_Integer32) + GetChannelsAnalog(inputDataItems, Enums.s_Float32));
                    channelsInputDigital = (ushort)GetChannelsBoolMax(inputDataItems);
                }

                var outputDataItems = GetOutputDataItems(submoduleItem);
                if (outputDataItems != null && outputDataItems.Count > 0)
                {
                    channelsOutputAnalog = (ushort)(GetChannelsAnalog(outputDataItems, Enums.s_Integer16) +
                                                    GetChannelsAnalog(outputDataItems, Enums.s_Integer32) + GetChannelsAnalog(outputDataItems, Enums.s_Float32));
                    channelsOutputDigital = (ushort)GetChannelsBoolMax(outputDataItems);
                }

                IList<int> qbitList = GetQBits(inputDataItems);
                bool wrongNumberOfQbits = IsNumberOfQbitsWrong(qbitList, channelsInputDigital, channelsInputAnalog, channelsOutputDigital, channelsOutputAnalog, false);

                if (qbitList != null
                    && !wrongNumberOfQbits)
                {
                    continue;
                }
                // "Number of Q bits does not match number of IO channels."
                string msg = Help.GetMessageString("M_0x00030011_1");
                string xpath = Help.GetXPath(submoduleItem);
                IXmlLineInfo xli = submoduleItem;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00030011_1");

            }

            return true;
        }

        private static bool IsNumberOfQbitsWrong(
                            IList<int> qbitList,
                            ushort channelsInputDigital,
                            ushort channelsInputAnalog,
                            ushort channelsOutputDigital,
                            ushort channelsOutputAnalog,
                            bool wrongNumberOfQbits)
        {
            if (qbitList == null)
            {
                return wrongNumberOfQbits;
            }
            // every channel must have a Q Bit
            int digitalInWithoutQBit = channelsInputDigital;
            int analogInWithoutQBit = channelsInputAnalog;
            int digitalOutWithoutQBit = channelsOutputDigital;
            int analogOutWithoutQBit = channelsOutputAnalog;

            foreach (int qbits in qbitList)
            {
                if (digitalInWithoutQBit > 0)
                {
                    digitalInWithoutQBit -= qbits;
                }
                else if (analogInWithoutQBit > 0)
                {
                    analogInWithoutQBit -= qbits;
                }
                else if (digitalOutWithoutQBit > 0)
                {
                    digitalOutWithoutQBit -= qbits;
                }
                else if (analogOutWithoutQBit > 0)
                {
                    analogOutWithoutQBit -= qbits;
                }
                else
                {
                    wrongNumberOfQbits = true;
                    break;
                }
            }

            if (digitalInWithoutQBit != 0
                || analogInWithoutQBit != 0
                || digitalOutWithoutQBit != 0
                || analogOutWithoutQBit != 0)
            {
                wrongNumberOfQbits = true;
            }
            return wrongNumberOfQbits;
        }

        /// <summary>
        /// Checked specifically for RIOforFA: DataItems in RIOforFA submodules must be in the correct order.
        /// Only certain data types are allowed.
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00030012()
        {
            var nl =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var submoduleItem in nl)
            {
                var inputDataItems = GetInputDataItems(submoduleItem);
                if (!IsRioForFaSupported(inputDataItems))
                {
                    continue;
                }

                CheckOrderInIOData(submoduleItem);

            }

            return true;
        }

        private void CheckOrderInIOData(XContainer submoduleItem)
        {
            var nl1 = submoduleItem.Descendants().Where(x => x.Parent != null && x.Parent.Name.LocalName == Elements.s_IoData);

            foreach (var element in nl1)
            {
                bool wrongOrder = false;
                var dataItems = element.Elements(NamespaceGsdDef + Elements.s_DataItem);

                const uint StepUnsigned8 = 0;
                const uint StepInteger16 = 1;
                const uint StepInteger32 = 2;
                const uint StepFloat32 = 3;
                const uint StepQualifier = 4;
                const uint StepMessageTrailer = 5;

                uint currentStep = 0;

                foreach (var dataItem in dataItems)
                {
                    if (wrongOrder)
                    {
                        break;
                    }

                    var lineInfoDataItem = (IXmlLineInfo)dataItem;

                    string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                    string subordinateStr = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                    bool subordinate = false;
                    if (!string.IsNullOrEmpty(subordinateStr))
                        subordinate = XmlConvert.ToBoolean(subordinateStr);
                    if (CreateReport0x00030012_3(subordinate, element, dataItem, lineInfoDataItem, ref dataType))
                    {
                        break;
                    }

                    currentStep = GetWrongOrderFromDataType(dataType, currentStep, StepUnsigned8, StepInteger16, StepInteger32, StepFloat32, StepQualifier, StepMessageTrailer, dataItem, lineInfoDataItem, ref wrongOrder);
                }

                if (CreateReport0x00030012_2(wrongOrder, element))
                {
                    break;
                }
            }
        }

        private uint GetWrongOrderFromDataType(
            string dataType,
            uint currentStep,
            uint stepUnsigned8,
            uint stepInteger16,
            uint stepInteger32,
            uint stepFloat32,
            uint stepQualifier,
            uint stepMessageTrailer,
            XObject dataItem,
            IXmlLineInfo lineInfoDataItem,
            ref bool wrongOrder)
        {
            switch (dataType)
            {
                case Enums.s_Unsigned8:
                    if (currentStep > stepUnsigned8)
                    {
                        wrongOrder = true;
                    }

                    break;
                case Enums.s_Integer16:
                    if (currentStep > stepInteger16)
                    {
                        wrongOrder = true;
                    }

                    currentStep = stepInteger16;
                    break;
                case Enums.s_Integer32:
                    if (currentStep > stepInteger32)
                    {
                        wrongOrder = true;
                    }

                    currentStep = stepInteger32;
                    break;
                case Enums.s_Float32:
                    if (currentStep > stepFloat32)
                    {
                        wrongOrder = true;
                    }

                    currentStep = stepFloat32;
                    break;
                case "Qualifier":
                    if (currentStep > stepQualifier)
                    {
                        wrongOrder = true;
                    }

                    currentStep = stepQualifier;
                    break;
                case Enums.s_FMessageTrailer4Byte:
                case Enums.s_FMessageTrailer5Byte:
                    // Normally here wrongOrder must be set to true, if PROFIsafe is not supported.
                    // But the appropriate error message is generated in other checks,
                    // so that here nothing will be done.
                    currentStep = stepMessageTrailer;
                    break;
                default:
                    // invalid data type
                    CreateReport0x00030012_1(dataItem, dataType, lineInfoDataItem);
                    break;
            }

            return currentStep;
        }

        private bool CreateReport0x00030012_2(bool wrongOrder, XObject element)
        {
            if (!wrongOrder)
            {
                return false;
            }

            if (!Help.CheckSchemaVersion(element, SupportedGsdmlVersion))
            {
                return true;
            }

            // "Wrong order of 'DataItem' elements used in combination with RIOforFA."
            string msg = Help.GetMessageString("M_0x00030012_2");
            string xpath = Help.GetXPath(element);
            var xli = (IXmlLineInfo)element;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00030012_2");

            return true;

        }

        private void CreateReport0x00030012_1(XObject dataItem, string dataType, IXmlLineInfo lineInfoDataItem)
        {
            if (!Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "The 'DataType' "{0}" must not be used in combination with RIOforFA."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030012_1"), dataType);
            string xpath = Help.GetXPath(dataItem);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfoDataItem.LineNumber,
                lineInfoDataItem.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00030012_1");
        }

        private bool CreateReport0x00030012_3(
            bool subordinate,
            XElement element,
            XObject dataItem,
            IXmlLineInfo lineInfoDataItem,
            ref string dataType)
        {
            if (!subordinate)
            {
                return false;
            }

            if (element.Name.LocalName == Elements.s_Output)
            {
                if (Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                {
                    // "RIOforFA: For 'IOData/Output' no Qualifier bits must be given."
                    string msg = Help.GetMessageString("M_0x00030012_3");
                    string xpath = Help.GetXPath(dataItem);
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        lineInfoDataItem.LineNumber,
                        lineInfoDataItem.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00030012_3");
                    return true;
                }
            }

            dataType = "Qualifier";

            return false;
        }

        #region Marketing Rules

        /// <summary>
        /// Check number: CN_0x00030013
        /// (1) The attribute InterfaceSubmoduleItem/@MaxFrameStartTime shall be present and its value shall be less or
        ///     equal 3500 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "HighPerformance".
        /// (2) The attribute InterfaceSubmoduleItem/@MinNRT_Gap shall be present and its value shall be less or
        ///     equal 1600 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00030013()
        {
            var itfs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            itfs = Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in itfs)
            {
                var lineInfoInterface = (IXmlLineInfo)interfaceSubmoduleItem;

                if (interfaceSubmoduleItem.Parent != null)
                {
                    var dap = interfaceSubmoduleItem.Parent.Parent;
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (pnioVersion < 2.31)
                        continue;
                    string applicationClass = string.Empty;
                    XAttribute applicationClassNode =
                        dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo)
                            .Attributes("ApplicationClass")
                            .FirstOrDefault();
                    if (applicationClassNode != null)
                        applicationClass = applicationClassNode.Value;
                    var applicationClasses = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));

                    if (!applicationClasses.Contains("HighPerformance"))
                    {
                        continue;
                    }
                }

                // (1)
                CheckMaxFrameStartTime(interfaceSubmoduleItem, lineInfoInterface);

                // (2)
                CheckMinNrtGap(interfaceSubmoduleItem, lineInfoInterface);
            }

            return true;
        }

        private void CheckMaxFrameStartTime(XElement interfaceSubmoduleItem, IXmlLineInfo lineInfoInterface)
        {
            string maxFrameStartTimeStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_MaxFrameStartTime);
            if (string.IsNullOrEmpty(maxFrameStartTimeStr))
            {
                // "'InterfaceSubmoduleItem/@MaxFrameStartTime' is not given. This attribute shall be present and its value shall
                // be <= 3500 if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = Help.GetMessageString("M_0x00030013_1");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfoInterface.LineNumber, lineInfoInterface.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030013_1");
            }
            else
            {
                UInt16 maxFrameStartTime = XmlConvert.ToUInt16(maxFrameStartTimeStr);
                if (maxFrameStartTime > 3500)
                {
                    // "'InterfaceSubmoduleItem/@MaxFrameStartTime' is {0}. This attribute shall be present and its value shall be <= 3500
                    // if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030013_2"), maxFrameStartTime);
                    string xpath = Help.GetXPath(interfaceSubmoduleItem);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfoInterface.LineNumber, lineInfoInterface.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00030013_2");
                }
            }
        }

        private void CheckMinNrtGap(XElement interfaceSubmoduleItem, IXmlLineInfo lineInfoInterface)
        {
            string minNrtGapStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_MinNrtGap);
            if (string.IsNullOrEmpty(minNrtGapStr))
            {
                // "'InterfaceSubmoduleItem/@MinNRT_Gap' is not given. This attribute shall be present and its value shall be <= 1600
                // if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = Help.GetMessageString("M_0x00030013_3");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfoInterface.LineNumber, lineInfoInterface.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030013_3");
            }
            else
            {
                UInt16 minNrtGap = XmlConvert.ToUInt16(minNrtGapStr);
                if (minNrtGap <= 1600)
                {
                    return;
                }

                // "'InterfaceSubmoduleItem/@MinNRT_Gap' is {0}. This attribute shall be present and its value shall be <= 1600
                // if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030013_4"), minNrtGap);
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfoInterface.LineNumber, lineInfoInterface.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030013_4");
            }
        }

        /// <summary>
        /// Check number: CN_0x00030014
        /// Some checks for RT_Class3Properties attributes and ApplicationClass
        /// (1) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap shall be present and its
        ///     value shall be less or equal 1600 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// (2) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin shall be present and its
        ///     value shall be less or equal 1000 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00030014()
        {
            var rtcs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);
            foreach (var rtClass3Properties in rtcs)
            {
                var lineInfo = (IXmlLineInfo)rtClass3Properties;

                if (rtClass3Properties.Parent != null)
                {
                    var dap = rtClass3Properties.Parent.Parent.Parent;
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (pnioVersion < 2.31)
                        continue;
                    string applicationClass = string.Empty;
                    XAttribute applicationClassNode =
                        dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo)
                            .Attributes("ApplicationClass")
                            .FirstOrDefault();
                    if (applicationClassNode != null)
                        applicationClass = applicationClassNode.Value;
                    var applicationClasses = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));

                    if (!applicationClasses.Contains("HighPerformance"))
                    {
                        continue;
                    }
                }

                // (1)
                CheckMinRtc3Gap(rtClass3Properties, lineInfo);

                // (2)
                CheckYellowSafetyMargin(rtClass3Properties, lineInfo);

            }

            return true;
        }

        private void CheckMinRtc3Gap(XElement rtClass3Properties, IXmlLineInfo lineInfo)
        {
            string minRtc3GapStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MinRtc3Gap);
            if (string.IsNullOrEmpty(minRtc3GapStr))
            {
                // "InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap is not given. This attribute shall be present and its value
                // shall be <= 1600 if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = Help.GetMessageString("M_0x00030014_1");
                string xpath = Help.GetXPath(rtClass3Properties);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030014_1");
            }
            else
            {
                UInt16 minRtc3Gap = XmlConvert.ToUInt16(minRtc3GapStr);
                if (minRtc3Gap > 1600)
                {
                    // "InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap is {0}. This attribute shall be present and its value shall
                    // be <= 1600 if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030014_2"), minRtc3Gap);
                    string xpath = Help.GetXPath(rtClass3Properties);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00030014_2");
                }
            }
        }

        private void CheckYellowSafetyMargin(XElement rtClass3Properties, IXmlLineInfo lineInfo)
        {
            string yellowSafetyMarginStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_YellowSafetyMargin);
            if (string.IsNullOrEmpty(yellowSafetyMarginStr))
            {
                // "InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin is not given. This attribute shall be present and its value
                // shall be <= 1000 if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = Help.GetMessageString("M_0x00030014_3");
                string xpath = Help.GetXPath(rtClass3Properties);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030014_3");
            }
            else
            {
                UInt16 yellowSafetyMargin = XmlConvert.ToUInt16(yellowSafetyMarginStr);
                if (yellowSafetyMargin <= 1000)
                {
                    return;
                }

                // "InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin is {0}. This attribute shall be present and its value shall
                // be <= 1000 if 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00030014_4"), yellowSafetyMargin);
                string xpath = Help.GetXPath(rtClass3Properties);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00030014_4");
            }
        }

        #endregion

        /// <summary>
        /// Checked specifically for RIOforFA: DI and DO values and all the Q bits shall be packed into a sequence of
        /// DataItem elements whose attribute DataType shall be "Unsigned8" (checked with 0x00030012_1)
        /// and each Boolean shall be described by a subordinated element BitDataItem.
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00030015()
        {
            var nl =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var submoduleItem in nl)
            {
                var inputDataItems = GetInputDataItems(submoduleItem);
                if (!IsRioForFaSupported(inputDataItems))
                {
                    continue;
                }

                var nl1 = submoduleItem.Descendants().Where(x => x.Parent != null && x.Parent.Name.LocalName == Elements.s_IoData);
                foreach (var element in nl1)
                {
                    CreateReport0x00030015_1(element);
                }
            }

            return true;
        }

        private void CreateReport0x00030015_1(XContainer element)
        {
            var dataItems = element.Elements(NamespaceGsdDef + Elements.s_DataItem);

            foreach (var dataItem in dataItems)
            {
                string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                if (dataType != Enums.s_Unsigned8)
                {
                    continue;
                }

                var bitDataItems = dataItem.Elements(NamespaceGsdDef + Elements.s_BitDataItem).ToList();
                if (bitDataItems.Count != 0)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dataItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "The RIOforFA common profile requires BitDataItems for each of the DI, DO and Q bits."
                string msg = Help.GetMessageString("M_0x00030015_1");
                string xpath = Help.GetXPath(dataItem);
                var xli = (IXmlLineInfo)dataItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00030015_1");
            }
        }

        /// <summary>
        /// Check number: CN_0x00030020
        /// Texts, which are referenced from ModuleInfo/Name/@TextId, are used to create catalog entries.
        /// Texts, which are referenced from CategoryItem/@TextId, are used to create sub folders under catalog entries.
        /// 
        /// In all languages such referenced texts must not
        /// - be an empty string
        /// - only consist of white spaces (blank, tabulator)
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00030020()
        {
            var textRefs = from attribute in GsdProfileBody.Descendants().Attributes(Attributes.s_TextId)
                           where
                               ((attribute.Parent.Name.LocalName == Elements.s_Name &&
                                 attribute.Parent.Parent.Name.LocalName == Elements.s_ModuleInfo) ||
                                attribute.Parent.Name.LocalName == Elements.s_CategoryItem)
                           select attribute;
            textRefs = Help.TryRemoveXAttributesUnderXsAny(textRefs, Nsmgr, Gsd);

            IList<string> referencedTexts = new List<string>();
            foreach (var textRef in textRefs)
            {
                string textRefValue = Help.CollapseWhitespace(textRef.Value);
                if (!referencedTexts.Contains(textRefValue))
                    referencedTexts.Add(textRefValue);
            }

            const String Pattern = @"\S+"; // Search for characters which are not white spaces
            var regex = new Regex(Pattern, RegexOptions.IgnoreCase);
            var textIds = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Text).Attributes(Attributes.s_TextId);
            textIds = Help.TryRemoveXAttributesUnderXsAny(textIds, Nsmgr, Gsd);
            foreach (var textId in textIds)
            {
                if (referencedTexts.Contains(Help.CollapseWhitespace(textId.Value)))
                {
                    string text = Help.GetAttributeValueFromXElement(textId.Parent, Attributes.s_Value);
                    Match match = regex.Match(text.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture));
                    string match1String = match.Groups[0].ToString();
                    if (string.IsNullOrEmpty(match1String))
                    {
                        // "Referenced texts must not be an empty string or only consist of white spaces (blank, tabulator)."
                        string msg = Help.GetMessageString("M_0x00030020_1");
                        string xpath = Help.GetXPath(textId);
                        IXmlLineInfo xli = (IXmlLineInfo)textId;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00030020_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0003002A
        /// If the 'InterfaceSubmoduleItem/@SupportedRT_Class' supports RT_CLASS_3, 
        /// the element 'InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties' shall be present.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002A()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem" +
                "[(@SupportedRT_Class or @SupportedRT_Classes) and not(./gsddef:ApplicationRelations/gsddef:RT_Class3TimingProperties)]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                var supportedRTClasses = GetRTClasses(en);

                if (supportedRTClasses.Contains("RT_CLASS_3"))
                {
                    // "The 'InterfaceSubmoduleItem' supports RT_CLASS_3,
                    // but the element 'RT_Class3TimingProperties' is not present."
                    string msg = Help.GetMessageString("M_0x0003002A_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0003002A_1");
                }
            }

            var nl2 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3TimingProperties);
            nl2 = Help.TryRemoveXElementsUnderXsAny(nl2, Nsmgr, Gsd);
            foreach (var en in nl2)
            {
                XElement interfaceSubmoduleItem = en.Parent.Parent;
                var supportedRTClasses = GetRTClasses(interfaceSubmoduleItem);
                if (!supportedRTClasses.Contains("RT_CLASS_3"))
                {
                    // "The element 'RT_Class3TimingProperties' is present, but the 'InterfaceSubmoduleItem' does not support RT_CLASS_3."
                    string msg = Help.GetMessageString("M_0x0003002A_2");
                    string xpath = Help.GetXPath(en);
                    IXmlLineInfo xli = en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0003002A_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0003002B
        /// If 'API' = 0, 'APStructureIdentifier' must be less than 0x8000 (32768).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002B()
        {
            var nl =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Request);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var request in nl)
            {
                string strApi = Help.GetAttributeValueFromXElement(request, Attributes.s_Api);
                UInt32 api = 0;
                if (!string.IsNullOrEmpty(strApi))
                    api = XmlConvert.ToUInt32(strApi);
                if (api == 0)
                {
                    // APStructureIdentifier must be available, checked by schema
                    string value = Help.GetAttributeValueFromXElement(request, Attributes.s_ApStructureIdentifier);

                    UInt16 apStructureIdentifier = XmlConvert.ToUInt16(value);
                    if (apStructureIdentifier >= 32768)
                    {
                        if (Help.CheckSchemaVersion(request, SupportedGsdmlVersion))
                        {
                            // "If 'API' = 0, 'APStructureIdentifier' must be less than 0x8000 (32768)."
                            string msg = Help.GetMessageString("M_0x0003002B_1");
                            string xpath = Help.GetXPath(request);
                            var xli = (IXmlLineInfo)request;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0003002B_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0003002C
        /// If the attribute RT_Class3Properties/@ForwardingMode contains
        /// the token "Absolute" and the attribute PNIO_Version is >= "V2.31",
        /// the value of the attribute RT_Class3Properties/@MaxNumberIR_FrameData shall be >= 128
        /// 
        /// The message from this check is reported as warning at the moment,
        /// but from GSDML V2.32 on it shall be reported as error.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002C()
        {
            var rtcs =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties)
                    .Where(x => x.Attribute(Attributes.s_ForwardingMode) != null);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);

            foreach (var rTClass3Properties in rtcs)
            {
                var interfaceSubmoduleItem = rTClass3Properties.Parent;
                if (interfaceSubmoduleItem == null)
                {
                    continue;
                }
                var dap = interfaceSubmoduleItem.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                string forwardingModeStr = Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_ForwardingMode);
                if (string.IsNullOrEmpty(forwardingModeStr))
                {
                    continue;
                }
                var forwardingModes = new List<string>(forwardingModeStr.Split(Constants.s_Semicolon.ToCharArray()));
                if (!(pnioVersion >= 2.31)
                    || !forwardingModes.Contains("Absolute"))
                {
                    continue;
                }
                string maxNumberIRFrameDataStr = Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_MaxNumberIRFrameData); // Mandatory
                UInt16 maxNumberIRFrameData = XmlConvert.ToUInt16(maxNumberIRFrameDataStr);
                if (maxNumberIRFrameData >= 128)
                {
                    continue;
                }
                // "If the attribute 'RT_Class3Properties/@ForwardingMode' contains the token "Absolute"
                // and the attribute 'PNIO_Version' is >= "V2.31", the value of the attribute
                // 'RT_Class3Properties/@MaxNumberIR_FrameData' shall be >= 128."
                string msg = Help.GetMessageString("M_0x0003002C_1");

                string xpath = Help.GetXPath(rTClass3Properties);
                var xli = (IXmlLineInfo)rTClass3Properties;
                Store.CreateAndAnnounceReport(ReportType_0X0003002C, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0003002C_1");



            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0003002D
        /// If the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token "FunctionalSafety",
        /// at least one of the submodules configurable with this DAP shall support functional safety
        /// (the attribute PROFIsafeSupported shall be present and "true").
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002D()
        {
            List<XElement> profiSubmodulesInFunctionalSafetyDAP = new List<XElement>();
            var daps =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            x.Element(NamespaceGsdDef + Elements.s_CertificationInfo) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);

            foreach (var dap in daps)
            {
                IList<string> applicationClasses = GetCombinedApplicationClasses(dap);
                if (!applicationClasses.Contains("FunctionalSafety"))
                {
                    continue;
                }

                // Check all VirtualSubmodules at the DAP
                var virtualSubmodules = dap.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem).ToList();
                bool profiSafeSupportedFound = CheckAllVirtualSubmodulesAtTheDap(virtualSubmodules, profiSubmodulesInFunctionalSafetyDAP, false);



                // Check all UseableSubmodules at the DAP
                var submoduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
                profiSafeSupportedFound = CheckAllUseableSubmodulesAtTheDap(submoduleItemRefs, profiSubmodulesInFunctionalSafetyDAP, profiSafeSupportedFound);


                // Check all VirtualSubmodules and UseableSubmodules at the UseableModules of the DAP
                var moduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_ModuleItemRef);

                foreach (var moduleItemRef in moduleItemRefs)
                {
                    string moduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                    PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                    if (null == module)
                        continue;

                    // Check all VirtualSubmodules at the Module usable at the DAP

                    virtualSubmodules = module.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem).ToList();
                    profiSafeSupportedFound = CheckAllVirtualSubmodulesAtTheModuleUsableAtTheDap(virtualSubmodules, profiSubmodulesInFunctionalSafetyDAP, profiSafeSupportedFound);



                    // Check all UseableSubmodules at the Module usable at the DAP
                    if (!ModuleRefToSubmoduleDictionary.TryGetValue(moduleItemRef, out IList<XElement>  submodules))
                    {
                        continue;
                    }
                    profiSafeSupportedFound = CheckAllUseableSubmodulesAtTheModuleUsableAtTheDap(submodules, profiSubmodulesInFunctionalSafetyDAP, profiSafeSupportedFound);
                }



                if (profiSafeSupportedFound)
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "A DAP with 'ApplicationClass' "FunctionalSafety" must have at least one PROFIsafe submodule."
                string msg = Help.GetMessageString("M_0x0003002D_1");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0003002D_1");


            } // foreach (var dap in DAPs)
            CreateReport0x0003002D_2(profiSubmodulesInFunctionalSafetyDAP);


            return true;

        }

        

        private static bool CheckAllUseableSubmodulesAtTheModuleUsableAtTheDap(
             IList<XElement> submodules,
             ICollection<XElement> profiSubmodulesInFunctionalSafetyDAP,
             bool profiSafeSupportedFound)
        {
            foreach (var submodule in submodules)
            {
                string strProfiSafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool profiSafeSupported = false;
                if (!string.IsNullOrEmpty(strProfiSafeSupported))
                {
                    profiSafeSupported = XmlConvert.ToBoolean(strProfiSafeSupported);
                }
                if (!profiSafeSupported)
                {
                    continue;
                }
                if (!profiSubmodulesInFunctionalSafetyDAP.Contains(submodule))
                {
                    profiSubmodulesInFunctionalSafetyDAP.Add(submodule);
                }
                profiSafeSupportedFound = true;
            }
            return profiSafeSupportedFound;

        }

        private static bool CheckAllVirtualSubmodulesAtTheModuleUsableAtTheDap(
           List<XElement> virtualSubmodules,
           ICollection<XElement> profiSubmodulesInFunctionalSafetyDAP,
           bool profiSafeSupportedFound)
        {
            foreach (var virtualSubmodule in virtualSubmodules)
            {
                string strProfiSafeSupported = Help.GetAttributeValueFromXElement(virtualSubmodule, Attributes.s_ProfIsafeSupported);
                bool profiSafeSupported = false;
                if (!string.IsNullOrEmpty(strProfiSafeSupported))
                {
                    profiSafeSupported = XmlConvert.ToBoolean(strProfiSafeSupported);
                }

                if (!profiSafeSupported)
                {
                    continue;
                }
                if (!profiSubmodulesInFunctionalSafetyDAP.Contains(virtualSubmodule))
                {
                    profiSubmodulesInFunctionalSafetyDAP.Add(virtualSubmodule);
                }

                profiSafeSupportedFound = true;
            }
            return profiSafeSupportedFound;
        }

        private bool CheckAllUseableSubmodulesAtTheDap(
         IEnumerable<XElement> submoduleItemRefs,
         ICollection<XElement> profiSubmodulesInFunctionalSafetyDAP,
         bool profiSafeSupportedFound)
        {
            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                string submoduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                if (null == submodule)
                    continue;

                string strProfiSafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool profiSafeSupported = false;
                if (!string.IsNullOrEmpty(strProfiSafeSupported))
                {
                    profiSafeSupported = XmlConvert.ToBoolean(strProfiSafeSupported);
                }

                if (!profiSafeSupported)
                {
                    continue;
                }
                if (!profiSubmodulesInFunctionalSafetyDAP.Contains(submodule))
                {
                    profiSubmodulesInFunctionalSafetyDAP.Add(submodule);
                }

                profiSafeSupportedFound = true;
            }
            return profiSafeSupportedFound;
        }

        private static bool CheckAllVirtualSubmodulesAtTheDap(
           List<XElement> virtualSubmodules,
           ICollection<XElement> profiSubmodulesInFunctionalSafetyDAP,
           bool profiSafeSupportedFound)
        {
            foreach (var virtualSubmodule in virtualSubmodules)
            {
                string strProfiSafeSupported = Help.GetAttributeValueFromXElement(virtualSubmodule, Attributes.s_ProfIsafeSupported);
                bool profiSafeSupported = false;
                if (!string.IsNullOrEmpty(strProfiSafeSupported))
                {
                    profiSafeSupported = XmlConvert.ToBoolean(strProfiSafeSupported);
                }

                if (!profiSafeSupported)
                {
                    continue;
                }
                profiSubmodulesInFunctionalSafetyDAP.Add(virtualSubmodule);
                profiSafeSupportedFound = true;
            }
            return profiSafeSupportedFound;

        }

        private void CreateReport0x0003002D_2(List<XElement> profiSubmodulesInFunctionalSafetyDAP)
        {
            foreach (var profiSubmodule in profiSubmodulesInFunctionalSafetyDAP)
            {
                string strWriteableImRecords = Help.GetAttributeValueFromXElement(profiSubmodule, Attributes.s_WriteableImRecords);
                if (string.IsNullOrEmpty(strWriteableImRecords))
                {
                    continue;
                }
                var writeableImRecords = new List<string>(strWriteableImRecords.Split(Constants.s_Space.ToCharArray()));
                if (!writeableImRecords.Contains(Constants.s_Number4))
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(profiSubmodule, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "The submodule ('ID' = "{0}") with 'PROFIsafeSupported' = "true" is pluggable in a DAP with 'ApplicationClass' "FunctionalSafety".
                //  Here I&M4 must not be writable."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x0003002D_2"),
                    Help.GetAttributeValueFromXElement(profiSubmodule, Attributes.ID));
                string xpath = Help.GetXPath(profiSubmodule);
                var xli = (IXmlLineInfo)profiSubmodule;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0003002D_2");

            }
        }

        /// <summary>
        /// Check number: CN_0x0003002E
        /// Checks on the InterfaceSubmoduleItem:
        /// (1) ApplicationRelations/ @StartupMode shall be present and contain the token "Advanced"
        ///     when PDEV_CombinedObjectSupported is present and "true".
        /// (2) RT_Class3Properties/ @StartupMode shall be present and contain the token "Advanced"
        ///     when PDEV_CombinedObjectSupported is present and "true".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002E()
        {
            var itfs =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_PdevCombinedObjectSupported) != null);
            itfs=Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);

            foreach (var interfaceSubmoduleItem in itfs)
            {
                string strPdevCombinedObjectSupported =
                    Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_PdevCombinedObjectSupported);
                bool pdevCombinedObjectSupported = XmlConvert.ToBoolean(strPdevCombinedObjectSupported);
                if (!pdevCombinedObjectSupported)
                {
                    continue;
                }
                // (1)
                var applicationRelations =
                        interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_ApplicationRelations);
                if (applicationRelations != null)
                {
                    string startupModeRT =
                        Help.GetAttributeValueFromXElement(applicationRelations, Attributes.s_StartupMode);
                    IList<string> startupModesRT =
                        new List<string>(startupModeRT.Split(Constants.s_Semicolon.ToCharArray()));
                    if (!startupModesRT.Contains("Advanced"))
                    {
                        if (Help.CheckSchemaVersion(applicationRelations, SupportedGsdmlVersion))
                        {
                            // "'ApplicationRelations/@StartupMode' shall be present and contain the token "Advanced" when PDEV_CombinedObjectSupported is present and "true"."
                            string msg = Help.GetMessageString("M_0x0003002E_1");
                            string xpath = Help.GetXPath(applicationRelations);
                            var lineInfo = (IXmlLineInfo)applicationRelations;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber,
                                lineInfo.LinePosition, msg, xpath,
                                ReportCategories.TypeSpecific, "0x0003002E_1");
                        }
                    }
                }

                // (2)
                var rTClass3Properties =
                    interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_RTClass3Properties);
                if (rTClass3Properties == null)
                {
                    continue;
                }
                {
                    string startupModeIrt =
                            Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_StartupMode);
                    var startupModesIrt = new List<string>(startupModeIrt.Split(Constants.s_Semicolon.ToCharArray()));
                    if (startupModeIrt.Contains("Advanced"))
                    {
                        continue;
                    }

                    if (!Help.CheckSchemaVersion(rTClass3Properties, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" when PDEV_CombinedObjectSupported is present and "true"."
                    string msg = Help.GetMessageString("M_0x0003002E_2");
                    string xpath = Help.GetXPath(rTClass3Properties);
                    var lineInfo = (IXmlLineInfo)rTClass3Properties;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber,
                        lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x0003002E_2");
                }
            }
            return true;
        }



        /// <summary>
        /// Check number: CN_0x0003002F
        /// Checks on the (Virtual)SubmoduleItem:
        /// Submodules with RIOforFA must have Consistency="All items consistency" on IOData/Input and IOData/Output.
        /// RIOforFA is indicated by Input/DataItem/@Subordinate being present and "true".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0003002F()
        {
            // Find all submodules without Consistency="All items consistency"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                var ioData = submodule.Element(NamespaceGsdDef + Elements.s_IoData);  // required
                if (ioData == null)
                {
                    continue;
                }
                var input = ioData.Element(NamespaceGsdDef + Elements.s_Input);       // optional
                var output = ioData.Element(NamespaceGsdDef + Elements.s_Output);       // optional
                bool bAllItemsConsistencyGivenInput = false;
                bool bAllItemsConsistencyGivenOutput = false;
                if (input != null)
                {
                    string strConsistency = Help.GetAttributeValueFromXElement(input, Attributes.s_Consistency);
                    if (strConsistency == Enums.s_AllItemsConsistency)
                        bAllItemsConsistencyGivenInput = true;
                }
                if (output != null)
                {
                    string strConsistency = Help.GetAttributeValueFromXElement(output, Attributes.s_Consistency);
                    if (strConsistency == Enums.s_AllItemsConsistency)
                        bAllItemsConsistencyGivenOutput = true;
                }
                if (bAllItemsConsistencyGivenInput && bAllItemsConsistencyGivenOutput)
                {
                    continue;
                }
                bool bSubordinate = IsAttributeSubordinate(input);

                CreateReport0x0003002F_1(input, bSubordinate, bAllItemsConsistencyGivenInput);
                CreateReport0x0003002F_2(output, bSubordinate, bAllItemsConsistencyGivenOutput);


            }


            return true;
        }

        private bool IsAttributeSubordinate(XContainer input)
        {
            bool bSubordinate = false;
            if (input == null)
            {
                return false;
            }

            var dataItems = input.Elements(NamespaceGsdDef + Elements.s_DataItem);
            foreach (var dataItem in dataItems)
            {
                string strSubordinate = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Subordinate);
                if (!string.IsNullOrEmpty(strSubordinate))
                    bSubordinate = XmlConvert.ToBoolean(strSubordinate);
                if (bSubordinate)
                    break;
            }

            return bSubordinate;
        }

        private void CreateReport0x0003002F_2(XObject output, bool bSubordinate, bool bAllItemsConsistencyGivenOutput)
        {
            if (output == null
        || !bSubordinate
        || bAllItemsConsistencyGivenOutput)
            {
                return;
            }

            // "For RIOforFA 'Consistency'="All items consistency" must be given for Output."
            if (!Help.CheckSchemaVersion(output, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x0003002F_2");
            string xpath = Help.GetXPath(output);
            var xli = (IXmlLineInfo)output;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0003002F_2");
        }
        private void CreateReport0x0003002F_1(XObject input, bool bSubordinate, bool bAllItemsConsistencyGivenInput)
        {

            if (input == null
        || !bSubordinate
        || bAllItemsConsistencyGivenInput)
            {
                return;
            }
            // "For RIOforFA 'Consistency'="All items consistency" must be given for Input."
            if (!Help.CheckSchemaVersion(input, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = Help.GetMessageString("M_0x0003002F_1");
            string xpath = Help.GetXPath(input);
            var xli = (IXmlLineInfo)input;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0003002F_1");

        }


        /// <summary>
        /// Check number: CN_0x00030030
        /// The channel error types (CHET) in the range 0x000F-0x001F may be used in two ways:
        /// As manufacturer specific diagnosis (ChannelDiagItem) for legacy devices and
        /// as system-defined diagnosis (SystemDefinedChannelDiagItem) for current devices.
        /// 
        /// Therefore, this number range is allowed for ErrorType on both ChannelDiagItem
        /// and SystemDefinedChannelDiagItem.
        /// 
        /// As the channel diagnosis is defined globally within the GSD, each ErrorType value can
        /// appear only once, or an engineering tool does not know which entry to choose for decoding.
        /// 
        /// That means: The ErrorType must be unique across SystemDefinedChannelDiagItems and ChannelDiagItems.
        /// 
        /// ProfileChannelDiagItems do not have to be considered, because their ErrorType is in a different number range.
        /// 
        /// Check must start with GSDML V2.31 (when SystemDefinedChannelDiagItem was introduced).
        /// Starting with GSDML V2.33, the check is not necessary anymore, because the GSDML schema will do the check.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00030030()
        {
            IList<UInt16> errorTypes = new List<UInt16>();

            var allChannelDiagItems =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_ChannelDiagItem ||
                            x.Name.LocalName == Elements.s_SystemDefinedChannelDiagItem);
            allChannelDiagItems = Help.TryRemoveXElementsUnderXsAny(allChannelDiagItems, Nsmgr, Gsd);
            foreach (var channelDiagItem in allChannelDiagItems)
            {
                string value = channelDiagItem.Attribute(Attributes.s_ErrorType)?.Value;
                if (value == null)
                {
                    continue;
                }

                UInt16 errorType = XmlConvert.ToUInt16(value);
                if (errorTypes.Contains(errorType))
                {
                    if (!Help.CheckSchemaVersion(channelDiagItem, SupportedGsdmlVersion))
                    {
                        continue;
                    }

                    // "The ErrorType must be unique across SystemDefinedChannelDiagItems and ChannelDiagItems."
                    string msg = Help.GetMessageString("M_0x00030030_1");
                    string xpath = Help.GetXPath(channelDiagItem);
                    var lineInfo = (IXmlLineInfo)channelDiagItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00030030_1");
                }
                else
                {
                    errorTypes.Add(errorType);
                }
            }

            return true;
        }

        /// <summary>
        /// RIOforFA defines that Subordinate="true" may only appear on the Q bits,
        /// and the Q bits are only present on the input side.
        /// Therefore, Subordinate="true" may not appear on IOData/Output/DataItem!
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00030031()
        {
            var subordinates =
                GsdProfileBody.Descendants().Attributes(Attributes.s_Subordinate)
                    .Where(
                        x =>
                            x.Parent.Parent.Name.LocalName == Elements.s_Output);
            subordinates = Help.TryRemoveXAttributesUnderXsAny(subordinates, Nsmgr, Gsd);

            foreach (XAttribute subordinate in subordinates)
            {
                if (XmlConvert.ToBoolean(subordinate.Value))
                {
                    if (Help.CheckSchemaVersion(subordinate, SupportedGsdmlVersion))
                    {
                        // "@Subordinate="true" must not appear on 'IOData/Output/DataItem'."
                        string msg = Help.GetMessageString("M_0x00030031_1");
                        string xpath = Help.GetXPath(subordinate);
                        var lineInfo = (IXmlLineInfo)subordinate;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00030031_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00030032
        /// If the attribute DeviceAccessPointItem/SystemRedundancy/@MinRDHT is present,
        /// its value shall be greater or equal than the value of the attribute MaxSwitchOverTime.
        /// 
        /// The check is implemented here with ReportType = MinorError and
        /// overwritten in CheckerV02_034.cs with Error.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00030032()
        {
            var systemRedundancys = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy);
            systemRedundancys = Help.TryRemoveXElementsUnderXsAny(systemRedundancys, Nsmgr, Gsd);

            foreach (var systemRedundancy in systemRedundancys)
            {
                string strMinRDHT = Help.GetAttributeValueFromXElement(systemRedundancy, Attributes.s_MinRdht);

                if (string.IsNullOrEmpty(strMinRDHT))
                    continue;

                UInt16 minRDHT = XmlConvert.ToUInt16(strMinRDHT);
                string strMaxSwitchOverTime = Help.GetAttributeValueFromXElement(systemRedundancy, Attributes.s_MaxSwitchOverTime);
                UInt16 maxSwitchOverTime = XmlConvert.ToUInt16(strMaxSwitchOverTime);

                if (minRDHT < maxSwitchOverTime)
                {
                    if (Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
                    {
                        // "'DeviceAccessPointItem/SystemRedundancy/@MinRDHT' must not be lower than '@MaxSwitchOverTime'."
                        string msg = Help.GetMessageString("M_0x00030032_1");
                        string xpath = Help.GetXPath(systemRedundancy);
                        var lineInfo = (IXmlLineInfo)systemRedundancy;
                        Store.CreateAndAnnounceReport(ReportType_0X00030032, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00030032_1");
                    }
                }
            }

            return true;
        }

        #endregion
    }
}


