/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPAddressErrorCodes.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;

namespace PNConfigLib.HWCNBL.Utilities.Network._Enums
{
    [Flags]
    public enum IPAddressErrorCodes
    {
        /// <summary>
        /// No error
        /// </summary>
        None = 0,

        /// <summary>
        /// ip address is wrong
        /// </summary>
        WrongAddress,

        /// <summary>
        /// ip address may not be empty
        /// </summary>
        EmptyAddress,

        /// <summary>
        /// ip address may be only 32 bits long
        /// </summary>
        AddressTooLong,

        /// <summary>
        /// Loopback ip address (*********) is not allowed
        /// </summary>
        LoopbackAddress,

        /// <summary>
        /// Address may not be equal to default router address
        /// </summary>
        DefaultRouterSameAsAddress,

        /// <summary>
        /// Subnet part of Address must differ from subnet part of default router address
        /// </summary>
        DefaultRoutersNetOtherThenAddressesNet,

        /// <summary>
        /// Subnet mask is not correct
        /// </summary>
        WrongSubnetMask,

        /// <summary>
        /// ip address is set in a not allowed addres range
        /// </summary>
        NotAllowedAddressRange,

        /// <summary>
        /// ip address does not fit to the subnet mask
        /// </summary>
        AddressClassInvalid,

        /// <summary>
        /// broadcast address is not allowed (example: mask: *********** address xxx.xxx.255.255)
        /// </summary>
        BroadcastAddress,

        /// <summary>
        /// Default route address is not allowed (example mask: *********** address is xxx.xxx.0.0)
        /// </summary>
        DefaultRouteAddress,

        /// <summary>
        /// Address is not unique within the connected subnet
        /// </summary>
        AddressNotUnique,

        /// <summary>
        /// The host address is 0
        /// </summary>
        HostAdresseNull,

        /// <summary>
        /// Net part of IP address is 0
        /// </summary>
        NetAddressNull,

        /// <summary>
        /// The address of the device is in a different subnet as the subnet of the controller.
        /// </summary>
        AddressDifferentSubnet,
    }
}