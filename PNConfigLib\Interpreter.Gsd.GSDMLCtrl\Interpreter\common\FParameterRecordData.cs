/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: FParameterRecordData.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
	/// <summary>
	/// The FParameterRecordData object describes the data structure of a 
	/// F parameter record.
	/// </summary>
	//[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C38")] 
	public class FParameterRecordData : 
		RecordData,
		GSDI.IFParameterRecordData
	{
		//########################################################################################
		#region Initialization & Termination

		/// <summary>
		/// Initializes the FParameterRecordData if it is instantiated.
		/// The properties of the object are all initialized to empty 
		/// or with abstract default values.
		/// </summary>
		/// <remarks>The real content can only be written to the object by the Fill
		/// method, which is only available within this assembly.</remarks>
		public FParameterRecordData()
		{
			//m_Refs = null;
			m_TransferSequence = 0;
			m_FParamDescCRC = 0;
            m_ChangeableWithBump = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private ArrayList m_Refs;
		private uint m_TransferSequence;
		private uint m_FParamDescCRC;
        private bool m_ChangeableWithBump;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of RefData objects ...
        /// </summary>
        public virtual Array Refs =>
            this.m_Refs?.ToArray();

        /// <summary>
        /// Accesses the transfer sequence of the parameter record data objects, which
        /// controls the transfer sequence during start-up.
        /// </summary>
        /// <remarks>The transfer sequence shall be unique within a submodule. The first 
        /// transferred parameter record data object shall have this attribute set to 1, 
        /// the following objects shall have this attribute incremented in direct sequence 
        /// (without gaps). If this attribute is set to 0, then the transfer sequence is 
        /// undefined and the transfer sequence of all parameter record data objects within
        /// the submodule must be 0.</remarks>
        public UInt32 TransferSequence => this.m_TransferSequence;

        public Boolean ChangeableWithBump => this.m_ChangeableWithBump;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 FParamDescCRC => this.m_FParamDescCRC;

        protected new object Length => ((RecordData)this).Length;

        protected new object Index => ((RecordData)this).Index;

        #region COM Interface Members Only

        #endregion

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// ...
        /// </summary>
        public virtual RefData GetRefData(string bstrName)
		{
			// Check whether Ref list exists.
			if (null == this.Refs)
				return null;	// ---------->

			// Search for RefData with given parameter name.
			foreach (RefData rd in this.Refs)
			{
				// Check FParameter number.
				if (rd.Name == bstrName)
					return rd;	// ---------->
			}
			return null;
		}


		#region COM Interface Methods Only

		#endregion

		#endregion

		//########################################################################################
		#region GsdObject Members

		/// <summary>
		/// Fills the created object with all relevant data, which is needed
		/// for the properties.
		/// </summary>
		/// <param name="hash">Hashtable which contains name value pairs for
		/// all properties available from this object.</param>
		/// <returns>True, if filling was successfull, else false.</returns>
		override internal bool Fill(System.Collections.Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter
				if (null == hash)
					throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldRefs;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Refs = hash[member] as ArrayList;

                member = Models.s_FieldTransferSequence;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TransferSequence = (uint)hash[member];

                member = Models.s_FieldChangeableWithBump;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    this.m_ChangeableWithBump = (bool)hash[member];

                member = Models.s_FieldFParamDescCrc;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_FParamDescCRC = (uint)hash[member];

                // Base data.
                succeeded = base.Fill(hash);
			}
			catch(FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
			writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectFParameterRecordData);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// ----------------------------------------------
			// From base class
			base.SerializeMembers(option, ref writer);

			// ----------------------------------------------
			Export.WriteUint32Property(ref writer, Models.s_FieldTransferSequence, this.m_TransferSequence);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldChangeableWithBump, this.m_ChangeableWithBump);
            Export.WriteUint32Property(ref writer, Models.s_FieldFParamDescCrc, this.m_FParamDescCRC);
			Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldRefs, this.m_Refs);


			return true; 
		}


		#endregion

	}
}
