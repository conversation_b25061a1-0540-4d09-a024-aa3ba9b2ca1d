﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SnmpInterfaceDecentralBL.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.BusinessLogic.HWCNBL.Constants.Methods;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Networks.SNMP;
using PNConfigLib.HWCNBL.Networks.SNMP._Interfaces;
using PNConfigLib.HWCNBL.Networks.SNMP.Consistency;
using PNConfigLib.HWCNBL.PNInterface;

#endregion

namespace PNConfigLib.BusinessLogic.HWCNBL.PNInterface
{
    internal class SnmpInterfaceDecentralBL : IFDecorator
    {
        public SnmpInterfaceDecentralBL(IInterfaceBusinessLogic decoratedIFBL) : base(decoratedIFBL)
        {
            InitBL();
        }

        public override void InitBL()
        {
            InitActions();
        }

        private void CheckSnmpConsistency()
        {
            //Following consistency must be added after idevice implementation is done.
            //SnmpConfigurationSourceConsistencyChecker snmpConfigurationSourceCC =
            //    new SnmpConfigurationSourceConsistencyChecker(PnInterfaceSubmoduleNavigation,
            //                                                    IDeviceConfigurationChecker,
            //                                                    new SnmpErrorMessageProvider());
            //snmpConfigurationSourceCC.CheckConsistency(data);

            CommunityNameConsistencyChecker checker = new CommunityNameConsistencyChecker();

            checker.CheckConsistency(Interface);

            SnmpEnableReadOnlyConsistencyChecker enableReadonlyCC = new SnmpEnableReadOnlyConsistencyChecker();
            enableReadonlyCC.CheckConsistency(Interface);
        }

        private void GenericMethodGetCimSnmpAdjustControlRecord(IMethodData data)
        {
            if (data == null)
            {
                return;
            }

            data.ReturnValue = null;

            ISnmpControlRecordGenerator snmpRuntimeDataGenerator = new CimSnmpAdjustRecordGenerator();

            byte[] record = snmpRuntimeDataGenerator?.GenerateSnmpControlRecord(Interface);

            data.ReturnValue = record;
        }

        private void InitActions()
        {
            Interface.BaseActions.RegisterMethod(
                GetCimSnmpAdjustRecord.Name,
                GenericMethodGetCimSnmpAdjustControlRecord);

            ConsistencyManager.RegisterConsistencyCheck(Interface, CheckSnmpConsistency);
        }
    }
}