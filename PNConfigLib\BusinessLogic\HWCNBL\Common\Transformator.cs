/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Transformator.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Diagnostics;

using PNConfigLib.HWCNBL.Basics;

#endregion

namespace PNConfigLib.HWCNBL.Common
{
    /// <summary>
    /// Contains methods for writing and reading bits, bytes and other types to byte arrays,
    /// with given byte and bit offsets.
    /// </summary>
    internal static class Transformator
    {
        #region Write

        /// <summary>
        /// Writes a short integer to the given byte array with a specified byte offset.
        /// </summary>
        /// <param name="arr">The byte array that will be written to.</param>
        /// <param name="byteOffset">Byte offset.</param>
        /// <param name="val">Value to be written. Needs to be between 0 and 65535.</param>
        /// <exception cref="ConfigException">if value is not between 0 and 65535.</exception>
        public static void Write16(byte[] arr, int byteOffset, int val)
        {
            Debug.Assert((val & 0xFFFF0000) == 0);
            Debug.Assert(byteOffset < arr.Length - 1);

            if ((val & 0xFFFF0000) != 0)
            {
                throw new ConfigException("Transformator.Write16: Invalid value");
            }

            byte LO = (byte)(val & 0x00FF);
            byte HI = (byte)((val & 0x0FF00) >> 8);
            arr[byteOffset] = HI;
            arr[byteOffset + 1] = LO;
        }

        #endregion

        #region Read

        /// <summary>
        /// Reads a short integer from the given byte array with a specified byte offset.
        /// </summary>
        /// <param name="arr">The byte array that will read.</param>
        /// <param name="byteOffset">Byte offset.</param>
        /// <returns>The short integer value at the specified byte offset.</returns>
        public static int Read16(byte[] arr, int byteOffset)
        {
            int HI = arr[byteOffset];
            int LO = arr[byteOffset + 1];
            return (HI << 8) + LO;
        }

        #endregion
    }
}