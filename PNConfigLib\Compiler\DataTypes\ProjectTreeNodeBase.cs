/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: ProjectTreeNodeBase.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.Compiler.DataTypes.Interfaces;

#endregion

namespace PNConfigLib.Compiler.DataTypes
{
    [SerializationProperties(Tag = "Object", IsParent = true)]
    internal class ProjectTreeNodeBase : IProjectTreeNode
    {
        public ProjectTreeNodeBase()
        {
            Name = "";
            PCLObject = null;
            ChildNodes = new List<IProjectTreeNode>();
            Variables = new List<IVariable>();
        }

        [SerializationProperties(IsParent = true)]
        public List<IProjectTreeNode> ChildNodes { get; set; }

        [SerializationProperties(Tag = "ClassRID", IsParent = false)]
        public CompilerConstants.ClassRid ClassRID { get; set; }

        [TagAttribute(TagAttributeName = "Name")]
        public string Name { get; set; }

        public PclObject PCLObject { get; set; }

        [Variable]
        public List<IVariable> Variables { get; set; }

        public virtual void Compile()
        {
            if (ChildNodes.Count <= 0)
            {
                return;
            }
            foreach (IProjectTreeNode childNode in ChildNodes)
            {
                childNode.Compile();
            }
        }

        public virtual void GenerateChildren()
        {
        }
    }
}