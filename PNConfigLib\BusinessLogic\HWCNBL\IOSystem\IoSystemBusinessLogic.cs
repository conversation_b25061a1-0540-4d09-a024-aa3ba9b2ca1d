/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IoSystemBusinessLogic.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;
using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.BusinessLogic.HWCNBL.Constants.AttributeValues;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Isochrone;
using PNConfigLib.HWCNBL.Tailor.AddressTailoring.Options;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.IOSystem
{
    /// <summary>
    /// The business logic class for IOSystem.
    /// </summary>
    internal class IoSystemBusinessLogic : IBusinessLogic
    {
        #region Constants and Enums

        /// <summary>
        /// The constant used for converting send clock factor to actual time.
        /// </summary>
        private const float s_PNTimeBase = 31.25f;

        #endregion

        /// <summary>
        /// The constructor for IoSystemBusinessLogic.
        /// </summary>
        /// <param name="ioSystem">The IO system.</param>
        public IoSystemBusinessLogic(DataModel.PCLObjects.IOSystem ioSystem)
        {
            IOSystem = ioSystem;
            InitAttributes();
            InitActions();
        }

        /// <summary>
        /// The IO system data model object for this business logic object.
        /// </summary>
        public DataModel.PCLObjects.IOSystem IOSystem { get; }

        /// <summary>
        /// Fills the corresponding PROFINET attributes from the PNConfigLib configuration XML.
        /// </summary>
        /// <param name="xmlIOSystem">The XML object of the IO system.</param>
        public void Configure(SubnetIOSystem xmlIOSystem)
        {
            if (xmlIOSystem.General.IOSystemNumberSpecified)
            {
                // IOSystemNumber is set in PositionNumber; PNIoSystemNumber attribute reads from there.
                IOSystem.AttributeAccess.SetAnyAttribute<int>(
                    InternalAttributeNames.PositionNumber,
                    xmlIOSystem.General.IOSystemNumber);
            }

            if (string.IsNullOrEmpty(xmlIOSystem.General.IOSystemName))
            {
                IOSystem.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlIOSystem.IOSystemID);
            }
            else
            {
                IOSystem.AttributeAccess.SetAnyAttribute<string>(
                    InternalAttributeNames.Name,
                    xmlIOSystem.General.IOSystemName);
            }

            if (xmlIOSystem.General.MultipleUseIOSystem)
            {
                IOSystem.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIoAddressTailoringEnabled,
                    true);
            }
        }

        /// <summary>
        /// Get handler for the attribute IsoMaxDelayTime.
        /// </summary>
        /// <returns>The value of the related attribute.</returns>
        private long GetIsoMaxDelayTime()
        {
            long upperLimit = 0;
            if (IOSystem != null)
            {
                PNIOC ioController = IOSystem.PNIOC;
                if (ioController != null)
                {
                    Interface controllerInterfaceSubmodule = ioController.ParentObject as Interface;
                    if (controllerInterfaceSubmodule != null)
                    {
                        uint pnIsoInterruptLatency = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIsoInterruptLatency, new AttributeAccessCode().GetNew(), 0);
                        long sendClockFactor =
                            controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIoSendClockFactor,
                                new AttributeAccessCode(),
                                0);
                        long reductionRatio =
                            ioController.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                                new AttributeAccessCode(),
                                1);
                        long tDcMinDistance =
                            ioController.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.OB6xMinTDPDistance,
                                new AttributeAccessCode(),
                                0);
                        upperLimit = (long)(sendClockFactor * reductionRatio * s_PNTimeBase * 1000) - (tDcMinDistance + pnIsoInterruptLatency);
                    }
                }
            }
            return upperLimit;
        }

        /// <summary>
        /// Get handler for the attribute IsoMinDelayTime.
        /// </summary>
        /// <returns>The value of the related attribute.</returns>
        private long GetIsoMinDelayTime()
        {
            return 0;
        }

        /// <summary>
        /// Get handler for the attribute IsoTcaValid.
        /// </summary>
        /// <returns>The value of the related attribute.</returns>
        private long GetIsoTcaValid()
        {
            long isoTcaValid = 0;
            PNIOC ioController = IOSystem.ParentObject as PNIOC;
            if (ioController != null)
            {
                Interface controllerInterface = ioController.ParentObject as Interface;
                if (controllerInterface != null)
                {
                    SyncDomain syncDomain = controllerInterface.SyncDomain;
                    if (syncDomain != null)
                    {
                        AttributeAccessCode ac = new AttributeAccessCode();
                        isoTcaValid =
                            syncDomain.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIsoTcaValid,
                                ac,
                                0);
                    }
                }
            }
            return isoTcaValid;
        }

        /// <summary>
        /// Get handler for the attribute PNIoSystemNumber.
        /// </summary>
        /// <returns>The value of the related attribute.</returns>
        private int GetPNIoSystemNumber()
        {
            int positionNumber = 0;
            if (IOSystem != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                positionNumber =
                    IOSystem.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PositionNumber, ac, 0);
                if (ac.IsOkay)
                {
                    positionNumber = positionNumber | 0x10000;
                }
            }
            return positionNumber;
        }

        /// <summary>
        /// Initializes the generic methods used in IO system data model object.
        /// </summary>
        private void InitActions()
        {
            IOSystem.BaseActions.RegisterMethod(GetAppCycleTime.Name, GenericMethodGetAppCycleTime);
            ConsistencyManager.RegisterConsistencyCheck(IOSystem, ConsistencyCheck);
        }

        private void InitAttributes()
        {
            IOSystem.AttributeAccess.AddAnyAttribute<long>(
                InternalAttributeNames.PnIsoMaxDelayTime,
                GetIsoMaxDelayTime,
                null);

            IOSystem.AttributeAccess.AddAnyAttribute<long>(
                InternalAttributeNames.PnIsoMinDelayTime,
                GetIsoMinDelayTime,
                null);


            IOSystem.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnIoSystemNumber,
                GetPNIoSystemNumber,
                null);

            IOSystem.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.IsoTcaValid, GetIsoTcaValid, null);

            IOSystem.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PositionNumber, 0);

            IOSystem.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.MaxMastersystemNumber, 115);

            IOSystem.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.MinMastersystemNumber, 100);

            IOSystem.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.UseNameInDeviceController, false);

            IOSystem.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnIsoCacf, 1);
        }

        #region Generic methods

        /// <summary>
        /// Generic method that calculates the application cycle time for the specified CACF.
        /// </summary>
        /// <param name="methodData">Data of the generic method.</param>
        private void GenericMethodGetAppCycleTime(IMethodData methodData)
        {
            if ((methodData == null)
                || (methodData.Arguments[GetAppCycleTime.Cacf] == null))
            {
                throw new PNFunctionsException(nameof(GenericMethodGetAppCycleTime));
            }

            methodData.ReturnValue = 0.0f;

            int cacf = (int)methodData.Arguments[GetAppCycleTime.Cacf];

            if (IOSystem.PNIOC != null)
            {
                Interface controllerInterfaceSubmodule = IOSystem.PNIOC.ParentObject as Interface;
                if (controllerInterfaceSubmodule != null)
                {
                    long sendClockFactor =
                        controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            new AttributeAccessCode(),
                            0);
                    if (sendClockFactor != 0)
                    {
                        float factor = sendClockFactor * s_PNTimeBase;
                        float cycleTime = cacf * factor;

                        //Get the composite attribute PNIsoCacfSupported
                        AttributeAccessCode accessCode = new AttributeAccessCode();
                        PclObject cpu = NavigationUtilities.GetCpu(IOSystem.PNIOC);
                        if (cpu != null)
                        {
                            Enumerated supportedCacfsEnumerated =
                                cpu.AttributeAccess.GetAnyAttribute<Enumerated>(
                                    InternalAttributeNames.PnIsoCacfSupported,
                                    accessCode,
                                    null);

                            bool found = false;

                            if (accessCode.IsOkay
                                && (supportedCacfsEnumerated != null)
                                && (supportedCacfsEnumerated.List != null))
                            {
                                foreach (int child in supportedCacfsEnumerated.List)
                                {
                                    int cacfValue = child;
                                    if (found = (cacfValue * factor).Equals(cycleTime))
                                    {
                                        break;
                                    }
                                }
                            }

                            methodData.ReturnValue = found ? cycleTime : factor;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Makes necessary consistency checks for the IO system.
        /// </summary>
        private void ConsistencyCheck()
        {
            bool isPNPNIPSuiteViaOtherPath = false;

            PNIOC ioController = IOSystem?.PNIOC;
            Interface controllerInterfaceSubmodule = (Interface)ioController?.ParentObject;
            DataModel.PCLObjects.Node nodeIe = controllerInterfaceSubmodule?.Node;

            if (nodeIe != null)
            {
                isPNPNIPSuiteViaOtherPath =
                    nodeIe.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                        new AttributeAccessCode(),
                        false);
            }

            if ((ioController != null)
                && (controllerInterfaceSubmodule != null))
            {
                AddressTailorOptionsCentral addressTailoringUtility = new AddressTailorOptionsCentral(IOSystem, controllerInterfaceSubmodule);
                TailorIOSystemOptions tailoringUtility = new TailorIOSystemOptions(IOSystem, controllerInterfaceSubmodule);
                if (addressTailoringUtility.AddressTailoringEnabled || tailoringUtility.MachineTailorable)
                {
                    bool machineTailoringTopologyReguired =
                            controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>
                            (InternalAttributeNames.PnIoMachineTailoringTopologyRequired, new AttributeAccessCode(), true);
                    bool omitTopologyCheck = tailoringUtility.MachineTailorable
                                         && !addressTailoringUtility.AddressTailoringEnabled
                                         && !machineTailoringTopologyReguired;
                    tailoringUtility.TailorIoSystem.CheckConsistency(omitTopologyCheck);
                }
                else
                {
                    //Consistency-Check for the PNIpConfig.
                    //If “IP address via other method” is set and the interface has an underlying PNIO system,
                    //Then we show an Information Message.                       
                    CheckPNIpConfig(isPNPNIPSuiteViaOtherPath, controllerInterfaceSubmodule, nodeIe);
                }
            }

            IList<PNIOD> ioDevices = IOSystem?.GetParticipants();

            if (controllerInterfaceSubmodule != null)
            {
                int maxStationNumber =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIoMaxStationNumber,
                        new AttributeAccessCode(),
                        0);

                if (ioDevices != null)
                {
                    List<int> allStationNumbers = new List<int>();
                    Dictionary<int, PNIOD> duplicatedStationNumbers = new Dictionary<int, PNIOD>();

                    foreach (PNIOD ioDevice in ioDevices)
                    {

                        // Check duplicated PNStationNumbers.                  
                        CheckDuplicatedStationNumbers(ioDevice, allStationNumbers, duplicatedStationNumbers);

                        //Check max. Station no.
                        CheckMaxStationNo(ioDevice, maxStationNumber);

                        //Check down limit values
                        CheckDownLimitForStationNumbers(ioDevice, (int)StationNumberLimits.DeviceNumberLimit.Min);

                    }

                    IEnumerable<int> availableNumbers = Enumerable.Range((int)StationNumberLimits.DeviceNumberLimit.Min, maxStationNumber)
                        .Except(allStationNumbers);

                    LogDuplicatedStationNumbers(duplicatedStationNumbers, availableNumbers);


                    // Check IP Addresses of the IO Devices and IO Controllers, whether they are under the same Subnet.
                    if (!isPNPNIPSuiteViaOtherPath)
                    {
                        CheckIPAddresses(IOSystem, (List<PNIOD>)ioDevices);
                    }
                }
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            if (IOSystem != null)
            {
                long isoCacf = IOSystem.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnIsoCacf, ac, 0);
                //IsoCheck1 - Check controller application cycle time against max. norm value
                if (ac.IsOkay)
                {
                    PNIsochronUtilities.CheckMaxControllerAppTime(IOSystem, isoCacf, "");
                }
            }

            CheckIoSystemNumber();

            //Checking Isochronous OB-PIP settings
            PNIsochronUtilities.CheckPiPsOfDifferentMastersystems(IOSystem);
        }

        /// <summary>
        /// Makes consistency checks related to IP configuration.
        /// </summary>
        /// <param name="isPNPNIPSuiteViaOtherPath">Whether "IP address is set directly at the device" option is selected.</param>
        /// <param name="controllerInterfaceSubmodule">The controller interface submodule in the IO system.</param>
        /// <param name="nodeIe">Node of the controller interface submodule.</param>
        /// <returns>
        /// A consistency log containing error, warning or information;
        /// null if there are no issues with the consistency check.
        /// </returns>
        private void CheckPNIpConfig(bool isPNPNIPSuiteViaOtherPath,
            IPclObject controllerInterfaceSubmodule,
            IPclObject nodeIe)
        {
            if (nodeIe == null)
            {
                return;
            }
            //relevant in case of CPU (PNIpConfig_Node_Dialog)
            int pnPNIpConfigModeSupported =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnPnIpConfigModeSupported,
                    new AttributeAccessCode(),
                    0);
            //relevant in case of CP (IPConfig1DialogLayoutId)
            NodeIPConfiguration nodeIPConfiguration =
                (NodeIPConfiguration)
                nodeIe.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.NodeIPConfiguration,
                    new AttributeAccessCode(),
                    (int)NodeIPConfiguration.Project);
            if (((pnPNIpConfigModeSupported == 0) || !isPNPNIPSuiteViaOtherPath)
                && (nodeIPConfiguration != NodeIPConfiguration.Other))
            {
                return ;
            }
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Info,
                IOSystem,
                ConsistencyConstants.IoSystemPNIpConfigIPAddressViaOtherPath);
        }

        private void CheckDuplicatedStationNumbers(PNIOD ioDevice, ICollection<int> allStationNumbers, IDictionary<int, PNIOD> duplicatedStationNumbers)
        {

            int stationNumber = ioDevice.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnStationNumber,
                new AttributeAccessCode(),
                0);

            if (allStationNumbers.Contains(stationNumber))
            {
                // Duplicated station number is found, save it!
                if (!duplicatedStationNumbers.ContainsKey(stationNumber))
                {
                    duplicatedStationNumbers.Add(stationNumber, ioDevice);
                }
            }
            else
            {
                allStationNumbers.Add(stationNumber);
            }

        }

        private void LogDuplicatedStationNumbers(
            Dictionary<int, PNIOD> duplicatedStationNumbers,
            IEnumerable<int> availableNumbers)
        {
            foreach (KeyValuePair<int, PNIOD> item in duplicatedStationNumbers)
            {
                string ioSystem = item.Value.IOSystem.Id;
                if (availableNumbers.Any())
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, item.Value.ParentObject,
                        ConsistencyConstants.StationNumberDuplication, item.Key, ioSystem, availableNumbers.First());
                }
                else
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, item.Value.ParentObject,
                        ConsistencyConstants.StationNumberDuplicationAndNotAvailablePosition, item.Key, ioSystem);
                }
            }
        }

        /// <summary>
        /// Makes consistency checks related to maximum station number.
        /// </summary>
        /// <param name="ioDevice">The IO device whose station number is checked.</param>
        /// <param name="maxStationNo">Maximum allowed station number.</param>
        /// <returns>
        /// A consistency log containing error, warning or information;
        /// null if there are no issues with the consistency check.
        /// </returns>
        private void CheckMaxStationNo(PclObject ioDevice, int maxStationNo)
        {
            int stationNumber = ioDevice.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnStationNumber,
                new AttributeAccessCode(),
                0);
            if (stationNumber > maxStationNo)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, 
                    ioDevice.ParentObject, 
                    ConsistencyConstants.MaxStationNumberExceeded,
                        stationNumber,
                        maxStationNo);
            }
        }

        /// <summary>
        /// Makes consistency checks for station number less than or equal to zero.
        /// </summary>
        /// <param name="ioDevice">The IO system of IO device that is checked.</param>
        /// <param name="downLimit">Down Limit.</param>
        /// <returns>
        /// A consistency log containing error, warning or information;
        /// null if there are no issues with the consistency check.
        /// </returns>
        private void CheckDownLimitForStationNumbers(PNIOD ioDevice, int downLimit)
        {
            int stationNumber = ioDevice.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnStationNumber,
                new AttributeAccessCode(),
                0);
            string ioSystem = ioDevice.IOSystem.Id;
            if (stationNumber < downLimit)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    ioDevice.ParentObject,
                    ConsistencyConstants.StationNumberDownLimit, 
                    downLimit,
                    stationNumber,
                    ioSystem);
            }
        }

        /// <summary>
        /// Makes consistency checks related to IP addresses.
        /// </summary>
        /// <param name="ioSystem">The IO system that is checked.</param>
        /// <param name="ioDevices">The list of IO devices in the IO system.</param>
        /// <returns></returns>
        private void CheckIPAddresses(DataModel.PCLObjects.IOSystem ioSystem, List<PNIOD> ioDevices)
        {
            List<long> allSubnetworks = new List<long>();
            List<PclObject> ioDeviceNodesWithIPAddressProblem = new List<PclObject>();
            List<PclObject> devicesPclObjects = new List<PclObject>();

            foreach (PNIOD device in ioDevices)
            {
                devicesPclObjects.Add(device);
            }

            List<PclObject> ioControllers = new List<PclObject>(1);
            ioControllers.Add(ioSystem.PNIOC);

            GetAllSubnetworks(ioControllers, allSubnetworks, null);
            GetAllSubnetworks(devicesPclObjects, allSubnetworks, ioDeviceNodesWithIPAddressProblem);

            if (ioDeviceNodesWithIPAddressProblem.Count <= 0)
            {
                return;
            }

            foreach (PclObject currIODeviceNode in ioDeviceNodesWithIPAddressProblem)
            {
                bool isIpViaOtherPath =
                    currIODeviceNode.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                        new AttributeAccessCode(),
                        false);
                if (!isIpViaOtherPath)
                {
                    //we skip the devices that will get their IP Address on another way.
                    LogIPAddressProblem(currIODeviceNode);
                  
                }
            }
        }
        private void LogIPAddressProblem(PclObject currIODeviceNode)
        {
            IPAddress ipAddress = new IPAddress(currIODeviceNode as DataModel.PCLObjects.Node);
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                currIODeviceNode.ParentObject,
                ConsistencyConstants.IPAddressDifferentSubnet, 
                ipAddress.AsString);
        }
        /// <summary>
        /// Checks the IP addresses of IO devices in the IO system and determines
        /// the IO devices that have IP address problems (such as being on different networks).
        /// </summary>
        /// <param name="ioConnecter">The list of IO devices and controller in the IO system.</param>
        /// <param name="allSubnetworks">All subnets in the IO system.</param>
        /// <param name="ioDeviceNodesWithIpAddressProblem">The list of devices with IP address problems.</param>
        private void GetAllSubnetworks(
            List<PclObject> ioConnecter,
            ICollection<long> allSubnetworks,
            ICollection<PclObject> ioDeviceNodesWithIpAddressProblem)
        {
            foreach (PclObject connector in ioConnecter)
            {
                Interface interfaceSubmodule = (Interface)connector.ParentObject;
                DataModel.PCLObjects.Node curNode = interfaceSubmodule.Node;

                if (curNode == null)
                {
                    continue;
                }

                IPAddress ipAddress = new IPAddress(curNode);
                IPSubnetMask ipSubnetMask = new IPSubnetMask(curNode);

                long subnetAddress = ipSubnetMask.GetNetAddress(ipAddress);

                if ((subnetAddress == 0)
                    || allSubnetworks.Contains(subnetAddress))
                {
                    continue;
                }
                allSubnetworks.Add(subnetAddress);
                ioDeviceNodesWithIpAddressProblem?.Add(curNode);
            }
        }

        /// <summary>
        /// Checks if IO system number is in the range between MinMasterSystemNumber and MaxMasterSystemNumber.
        /// </summary>
        private void CheckIoSystemNumber()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            int minNumber = IOSystem.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.MinMastersystemNumber,
                ac,
                0);
            int maxNumber = IOSystem.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.MaxMastersystemNumber,
                ac.GetNew(),
                0);

            int currentNumber = IOSystem.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                ac.GetNew(),
                0);

            if ((currentNumber >= minNumber)
                && (currentNumber <= maxNumber))
            {
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, IOSystem, ConsistencyConstants.InvalidIOSystemNumber,
                minNumber,
                maxNumber);
        }

        #endregion
    }
}