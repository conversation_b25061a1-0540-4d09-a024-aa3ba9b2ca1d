/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Enumerated.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Xml.Serialization;

#endregion

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// This class represents an attribute value that may contain multiple values,
    /// such as enums, lists or other Enumerated objects.
    /// </summary>
    [XmlType(Namespace = "http://www.siemens.com/Automation/PNConfigLib/ControllerVariant")]
    public class Enumerated
    {
        /// <summary>
        /// The list that contains the values for this Enumerated object.
        /// </summary>
        public List<object> List { get; } = new List<object>();

        /// <summary>
        /// The default value.
        /// </summary>
        public object DefaultValue { get; set; }

        /// <summary>
        /// The first value of the object.
        /// </summary>
        public object Value
        {
            get { return List[0]; }
        }

        public void SetList(IEnumerable<object> list)
        {
            List.Clear();
            List.AddRange(list);
        }
    }
}