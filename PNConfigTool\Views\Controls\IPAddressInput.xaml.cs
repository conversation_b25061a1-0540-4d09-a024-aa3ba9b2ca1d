using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace PNConfigTool.Views.Controls
{
    public partial class IPAddressInput : UserControl
    {
        public static readonly DependencyProperty IPAddressProperty =
            DependencyProperty.Register("IPAddress", typeof(string), typeof(IPAddressInput),
                new PropertyMetadata("0.0.0.0", OnIPAddressChanged));

        public string IPAddress
        {
            get { return (string)GetValue(IPAddressProperty); }
            set 
            { 
                SetValue(IPAddressProperty, value);
                // Force UI update when property is set programmatically
                string[] parts = value.Split('.');
                if (parts.Length == 4)
                {
                    if (Part1 != null) Part1.Text = parts[0];
                    if (Part2 != null) Part2.Text = parts[1];
                    if (Part3 != null) Part3.Text = parts[2];
                    if (Part4 != null) Part4.Text = parts[3];
                }
            }
        }

        public IPAddressInput()
        {
            InitializeComponent();
            Loaded += IPAddressInput_Loaded;
        }

        private void IPAddressInput_Loaded(object sender, RoutedEventArgs e)
        {
            // Initialize with current IP address
            string[] parts = IPAddress.Split('.');
            if (parts.Length == 4)
            {
                Part1.Text = parts[0];
                Part2.Text = parts[1];
                Part3.Text = parts[2];
                Part4.Text = parts[3];
            }
        }

        private static void OnIPAddressChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is IPAddressInput control)
            {
                string[] parts = ((string)e.NewValue).Split('.');
                if (parts.Length == 4)
                {
                    control.Part1.Text = parts[0];
                    control.Part2.Text = parts[1];
                    control.Part3.Text = parts[2];
                    control.Part4.Text = parts[3];
                }
            }
        }

        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void IPTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // Remove non-numeric characters
                string text = new string(textBox.Text.Where(c => char.IsDigit(c)).ToArray());
                if (int.TryParse(text, out int value))
                {
                    // Ensure value is in range 0-255
                    if (value > 255)
                    {
                        textBox.Text = "255";
                        textBox.SelectionStart = textBox.Text.Length;
                    }
                    else
                    {
                        textBox.Text = value.ToString();
                    }

                    // Auto-advance to next box when 3 digits are entered
                    if (textBox.Text.Length == 3)
                    {
                        if (textBox == Part1) Part2.Focus();
                        else if (textBox == Part2) Part3.Focus();
                        else if (textBox == Part3) Part4.Focus();
                    }
                }
                else if (string.IsNullOrEmpty(text))
                {
                    textBox.Text = "";
                }

                // Update the IPAddress property
                UpdateIPAddress();
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // Clear selection when focused
                textBox.SelectionStart = textBox.Text.Length;
                textBox.SelectionLength = 0;
            }
        }

        private void TextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // Ensure empty boxes show 0
                if (string.IsNullOrEmpty(textBox.Text))
                {
                    textBox.Text = "0";
                }
                UpdateIPAddress();
            }
        }

        private void UpdateIPAddress()
        {
            string newIpAddress = $"{Part1.Text}.{Part2.Text}.{Part3.Text}.{Part4.Text}";
            
            // Only update if the value has actually changed to avoid binding loops
            if (IPAddress != newIpAddress)
            {
                IPAddress = newIpAddress;
                
                // Debug logging to track IP address changes
                System.Diagnostics.Debug.WriteLine($"IPAddressInput: IP address updated to {IPAddress}");
            }
        }
    }
} 