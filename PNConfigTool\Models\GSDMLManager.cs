using System;
using System.Collections.Generic;
using System.IO;

namespace PNConfigTool.Models
{
    /// <summary>
    /// GSDML文件管理器，提供对GSDML文件的管理功能
    /// </summary>
    public static class GSDMLManager
    {
        // 存储已导入的GSDML文件列表
        private static List<string> _importedGSDMLList = new List<string>();

        /// <summary>
        /// 获取已导入的GSDML文件列表
        /// </summary>
        /// <returns>GSDML文件列表</returns>
        public static List<string> GetImportedGSDMLList()
        {
            return _importedGSDMLList;
        }

        /// <summary>
        /// 添加GSDML文件到导入列表
        /// </summary>
        /// <param name="fileName">文件名</param>
        public static void AddGSDMLFile(string fileName)
        {
            if (!_importedGSDMLList.Contains(fileName))
            {
                _importedGSDMLList.Add(fileName);
                
                // 标记项目为已修改
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null)
                {
                    projectManager.IsProjectModified = true;
                }
            }
        }

        /// <summary>
        /// 从导入列表中移除GSDML文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        public static void RemoveGSDMLFile(string fileName)
        {
            _importedGSDMLList.Remove(fileName);
            
            // 标记项目为已修改
            var projectManager = ProjectManager.Instance;
            if (projectManager.CurrentProject != null)
            {
                projectManager.IsProjectModified = true;
            }
        }
        
        /// <summary>
        /// 获取设备的GSDML文件路径
        /// </summary>
        /// <param name="device">设备配置</param>
        /// <returns>GSDML文件路径</returns>
        public static string GetGSDMLPath(DecentralDeviceConfig device)
        {
            // 从设备的项目特定扩展中获取GSDML路径
            // 这里需要根据实际的配置结构来获取
            return ""; // 临时返回空字符串，需要根据实际配置结构调整
        }
        
        /// <summary>
        /// 设置设备的GSDML文件路径
        /// </summary>
        /// <param name="device">设备配置</param>
        /// <param name="gsdmlPath">GSDML文件路径</param>
        public static void SetGSDMLPath(DecentralDeviceConfig device, string gsdmlPath)
        {
            // 这里需要根据实际的配置结构来设置GSDML路径
            // 临时注释掉，需要根据实际配置结构调整
            // device.GSDMLPath = gsdmlPath;

            // 如果文件存在且不在列表中，则添加到导入列表
            if (File.Exists(gsdmlPath) && !_importedGSDMLList.Contains(gsdmlPath))
            {
                _importedGSDMLList.Add(gsdmlPath);
            }
        }
    }
}