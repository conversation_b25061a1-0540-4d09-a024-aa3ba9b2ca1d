/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MachineTailorUtility.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.MachineTailor
{
    /// <summary>
    /// Summary description for MachineTailorUtility.
    /// </summary>
    internal static class MachineTailorUtility
    {
        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums
        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        /// Checks optional device state of a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose optional device state will be retrieved.</param>
        /// <returns>True if the given interface submodule is an optional device, false otherwise.</returns>
        internal static bool IsOptionalDeviceEnabled(PclObject interfaceSubmodule)
        {
            bool retValue = false;
            if (interfaceSubmodule != null)
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool isOptionalDeviceEnabled =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoInterfaceIsOptional,
                        attributeAccessCode,
                        false);
                retValue = isOptionalDeviceEnabled && attributeAccessCode.IsOkay;
            }
            return retValue;
        }

        /// <summary>
        /// Checks Programmable Peer state of a port module.
        /// </summary>
        /// <param name="portModule"></param>
        /// <returns>Returns true if the given port module is a Programmable Peer</returns>
        public static bool IsProgrammablePeerEnabled(DataModel.PCLObjects.Port portModule)
        {
            bool retValue = false;
            if (portModule != null)
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool isProgrammablePeerEnabled =
                    portModule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoProgrammablePeer,
                        attributeAccessCode,
                        false);
                retValue = isProgrammablePeerEnabled && attributeAccessCode.IsOkay;
            }
            return retValue;
        }

        /// <summary>
        /// Checks if given interface submodule has any Programmable Peer port modules.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <returns>Returns true if the given interface has a Programmable Peer</returns>
        public static bool HasInterfaceProgrammablePeer(Interface interfaceSubmodule)
        {
            List<DataModel.PCLObjects.Port> ports = (List<DataModel.PCLObjects.Port>)interfaceSubmodule.GetPorts();
            if (ports != null)
            {
                foreach (DataModel.PCLObjects.Port port in ports)
                {
                    if (IsProgrammablePeerEnabled(port))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// This method is used to set Deactivated Config value. (IsActive field for Config).
        /// Checks if given interface submodule belongs to a Machine Tailored IO-System. 
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        /// <returns></returns>
        public static bool IsMachineTailoringEnabledForIoDeviceInterface(Interface ioDeviceInterface)
        {
            if (ioDeviceInterface == null)
            {
                throw new ArgumentNullException(nameof(ioDeviceInterface));
            }

            if (!AttributeUtilities.IsIDevice(ioDeviceInterface) ||
                (NavigationUtilities.GetIoSystem(ioDeviceInterface) != null))
            {
                return IsIoSystemMachineTailorableAnyStartObject(ioDeviceInterface);
            }
            return false;
        }

        /// <summary>
        /// Checks if given IO-Controller supports Machine Tailoring.
        /// </summary>
        /// <param name="ioControllerInterface"></param>
        /// <returns></returns>
        public static bool IsMachineTailoringSupportedIoControllerInterfaceStartObject(Interface ioControllerInterface)
        {
            bool retValue = false;
            if (ioControllerInterface != null)
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool machineTailoringSupported =
                    ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoMachineTailoring,
                        attributeAccessCode,
                        false);
                retValue = attributeAccessCode.IsOkay && machineTailoringSupported;
            }
            return retValue;
        }

        /// <summary>
        /// Called if port's interface or peer's interface is an Optional Device. It scours through the Tailored Port Groups.
        /// Returns false if any of those ports does not have correct MAU Type.
        /// </summary>
        /// <param name="firstFixedPort"></param>
        /// <returns></returns>
        public static bool CheckMAUTypeStatus(DataModel.PCLObjects.Port firstFixedPort)
        {
            DataModel.PCLObjects.Port secondFixedPort = GetOtherFixedPortForOptionalDevice(firstFixedPort,
                NavigationUtilities.GetInterfaceOfPort(firstFixedPort));
            List<DataModel.PCLObjects.Port> fixedPorts = new List<DataModel.PCLObjects.Port>();
            fixedPorts.Add(firstFixedPort);
            if (secondFixedPort != null)
            {
                fixedPorts.Add(secondFixedPort);
            }

            bool isAutomatic = false;
            bool is100MbDuplex = false;
            bool hasOther = false;

            foreach (DataModel.PCLObjects.Port port in fixedPorts)
            {

                UInt32 mauType = GetMAUTypeStatus(port);
                IsMAUTypeCorrect(mauType, ref isAutomatic, ref is100MbDuplex, ref hasOther);

                Interface currentInterfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(GetPartnerPort(port));
                DataModel.PCLObjects.Port secondPort = port;
                while (IsOptionalDeviceEnabled(currentInterfaceSubmodule))
                {
                    DataModel.PCLObjects.Port currentPort = GetPartnerPort(secondPort);

                    //If this is the last port in the topology.
                    if (currentPort == null)
                    {
                        return true;
                    }

                    mauType = GetMAUTypeStatus(currentPort);
                    if (!IsMAUTypeCorrect(mauType, ref isAutomatic, ref is100MbDuplex, ref hasOther))
                    {
                        return false;
                    }

                    secondPort = GetOtherFixedPortForOptionalDevice(currentPort, currentInterfaceSubmodule);
                    if (secondPort == null)
                    {
                        return true;
                    }
                    mauType = GetMAUTypeStatus(secondPort);
                    if (!IsMAUTypeCorrect(mauType, ref isAutomatic, ref is100MbDuplex, ref hasOther))
                    {
                        return false;
                    }

                    //Iterate to next interface.
                    currentInterfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(GetPartnerPort(secondPort));
                }

                //Check the last non-optional device's fixed port supports short preambling (if it exists)
                secondPort = GetPartnerPort(port);
                if (secondPort == null)
                {
                    return true;
                }
                mauType = GetMAUTypeStatus(secondPort);
                if (!IsMAUTypeCorrect(mauType, ref isAutomatic, ref is100MbDuplex, ref hasOther))
                {
                    return false;
                }
            }
            return true;
        }
        /// <summary>
        /// Checks if the IO-System has any Optional Devices or Programmable Peers
        /// </summary>
        /// <param name="ioControllerInterface"></param>
        /// <returns></returns>
        public static bool IsIoSystemMachineTailorable(Interface ioControllerInterface)
        {
            if (ioControllerInterface == null)
            {
                throw new ArgumentNullException(nameof(ioControllerInterface));
            }

            //If controller has programmable peer or it is an iDevice with central PDEV with a programmable peer, 
            //this IO-System is Machine Tailorable.
            if (HasInterfaceProgrammablePeer(ioControllerInterface))
            {
                if (!(AttributeUtilities.IsIDevice(ioControllerInterface) && AttributeUtilities.IsDecentralPDEV(ioControllerInterface)))
                {
                    return true;
                }

            }

            PclObject ioSystem = NavigationUtilities.GetIoSystem(ioControllerInterface);
            if (ioSystem == null)
            {
                return false;
            }

            IList<PNIOD> ioDevicesOfIOSystem = ((DataModel.PCLObjects.IOSystem)ioSystem).GetParticipants();
            if (ioDevicesOfIOSystem == null)
            {
                return false;
            }

            foreach (PNIOD ioDevice in ioDevicesOfIOSystem)
            {
                Interface interfaceSubmodule = ioDevice.GetInterface();
                if (!IsOptionalDeviceEnabled(interfaceSubmodule) && !HasInterfaceProgrammablePeer(interfaceSubmodule))
                {
                    continue;
                }
                return true;
            }
            return false;
        }
        /// <summary>
        /// Checks if the IO-System has any Optional Devices or Programmable Peers, starting from any object in the IO-System.
        /// </summary>
        /// <param name="startObject"></param>
        /// <returns></returns>
        private static bool IsIoSystemMachineTailorableAnyStartObject(Interface startObject)
        {
            return IsIoSystemMachineTailorable(GetIoControllerInterfaceFromInterfaceStartObject(startObject));
        }

        /// <summary>
        /// Checks if Multiple Connections is enabled for given port module.
        /// </summary>
        /// <param name="port"></param>
        /// <returns></returns>
        public static bool IsMultipleConnectionEnabled(DataModel.PCLObjects.Port port)
        {
            bool retValue = false;
            if (port != null)
            {
                AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
                bool isOptionalDeviceEnabled = port.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsMultipleConnectionEnabled, attributeAccessCode, false);
                retValue = isOptionalDeviceEnabled && attributeAccessCode.IsOkay;
            }
            return retValue;
        }

        /// <summary>
        /// Gets the other port submodule that has a fixed peer. It is assumed there are only 2 fixed ports for an
        /// optional device.
        /// </summary>
        /// <param name="port">Current fixed port</param>
        /// <param name="interfaceSubmodule"></param>
        /// <returns>Second fixed port</returns>
        private static DataModel.PCLObjects.Port GetOtherFixedPortForOptionalDevice(DataModel.PCLObjects.Port port,
                                                                       Interface interfaceSubmodule)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(interfaceSubmodule).ToList();
            ports.Remove(port);
            foreach (DataModel.PCLObjects.Port portSubmodule in ports)
            {
                DataModel.PCLObjects.Port partnerPort = GetPartnerPort(portSubmodule);
                if (partnerPort != null)
                {
                    return portSubmodule;
                }
            }

            return null;
        }
        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation
        private static DataModel.PCLObjects.Port GetPartnerPort(DataModel.PCLObjects.Port port)
        {
            List<DataModel.PCLObjects.Port> result = new List<DataModel.PCLObjects.Port>();
            if (port == null)
            {
                return null;
            }

            List<DataModel.PCLObjects.Port> partnerPorts = (List<DataModel.PCLObjects.Port>)port.GetPartnerPorts();
            if (partnerPorts == null)
            {
                return null;
            }

            if (partnerPorts.Count == 0)
            {
                return null;
            }

            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                result.Add(partnerPort);
            }

            return result[0];
        }
        private static Interface GetIoControllerInterfaceFromInterfaceStartObject(Interface startObject)
        {
            if (startObject == null)
            {
                throw new ArgumentNullException(nameof(startObject));
            }
            PclObject deviceOfInterface = startObject.GetDevice();
            if (deviceOfInterface is CentralDevice)
            {
                return startObject;
            }
            if (deviceOfInterface is DecentralDevice)
            {
                return NavigationUtilities.GetControllerInterfaceOfDeviceInterface(startObject);
            }
            return null;
        }

        private static uint GetMAUTypeStatus(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            UInt32 mauType = port.AttributeAccess.GetAnyAttribute<UInt32>(InternalAttributeNames.PnEthernetMediumDuplex,
                                                                          ac, 8);
            if (ac.IsOkay)
            {
                return mauType;
            }
            return 0;
        }

        private static bool IsMAUTypeCorrect(UInt32 mauType, ref bool isAutomatic, ref bool is100MbDuplex,
                                             ref bool hasOther)
        {
            switch (mauType)
            {
                case (uint)PNEthernetMediumDuplex.Automatic:
                    isAutomatic = true;
                    if (is100MbDuplex || hasOther)
                    {
                        return false;
                    }
                    break;
                case (uint)PNEthernetMediumDuplex.Fullduplex100Mbit:
                case (uint)PNEthernetMediumDuplex.Fullduplex100MbitFO:
                case (uint)PNEthernetMediumDuplex.Fullduplex100MbitFOLD:
                case (uint)PNEthernetMediumDuplex.Fullduplex100MbitPOF:
                    is100MbDuplex = true;
                    if (isAutomatic || hasOther)
                    {
                        return false;
                    }
                    break;
                default:
                    hasOther = true;
                    if (isAutomatic || is100MbDuplex)
                    {
                        return false;
                    }
                    break;
            }
            return true;
        }
        #endregion
    }
}