/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: InterfaceCatalog.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

#endregion

namespace PNConfigLib.DataModel.PCLCatalogObjects
{
    /// <summary>
    /// The catalog object for an interface submodule, containing hardware specific information.
    /// </summary>
    public class InterfaceCatalog : PclCatalogObject
    {
        /// <summary>
        /// The list of business logic classes that will be initialized, based on
        /// which features the interface submodule supports.
        /// </summary>
        public IList<Type> DecorationList { get; } = new List<Type>();

        /// <summary>
        /// The constructor.
        /// </summary>
        public InterfaceCatalog()
        {
            PortList = new SortedList<uint, PortCatalog>();
        }

        /// <summary>
        /// A list of port catalog objects within this interface catalog object,
        /// sorted by their subslot numbers.
        /// </summary>
        public SortedList<uint, PortCatalog> PortList { get; }

        public void SetPortList(IDictionary<uint, PortCatalog> dictionary)
        {
            if (dictionary == null)
            {
                throw new ArgumentNullException(nameof(dictionary));
            }

            PortList.Clear();
            foreach (uint dictionaryKey in dictionary.Keys)
            {
                PortList.Add(dictionaryKey, dictionary[dictionaryKey]);
            }
        }
    }
}