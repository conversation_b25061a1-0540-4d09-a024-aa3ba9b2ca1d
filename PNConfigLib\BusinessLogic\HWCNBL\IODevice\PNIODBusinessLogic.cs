/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIODBusinessLogic.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.PNFrameGeneration;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Addresses;

#endregion

namespace PNConfigLib.HWCNBL.IODevice
{
    /// <summary>
    /// The business logic class for PNIOD.
    /// </summary>
    internal class PNIODBusinessLogic : PNIOConnectorBL
    {
        /// <summary>
        /// The IO device data model object of this business logic object.
        /// </summary>
        private readonly PNIOD m_IoDevice;

        //########################################################################################

        #region Nested Classes

        #endregion

        //########################################################################################

        #region Constants and Enums

        /// <summary>
        /// Dafault value for IOCS length.
        /// </summary>
        private const int s_DefaultIocsLength = 1;

        /// <summary>
        /// Dafault value for IOPS length.
        /// </summary>
        private const int s_DefaultIopsLength = 1;

        #endregion

        //########################################################################################

        #region Fields

        #endregion

        //########################################################################################

        #region Properties

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Getter for m_ioDevice.
        /// </summary>
        private PNIOD IODevice => m_IoDevice;

        /// <summary>
        /// The constructor for PNIodBusinessLogic.
        /// </summary>
        /// <param name="ioDevice">The IO device.</param>
        public PNIODBusinessLogic(PNIOD ioDevice)
        {
            m_IoDevice = ioDevice;
            InitBL();
        }

        #endregion

        //########################################################################################

        #region Public Methods

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion

        //########################################################################################

        #region Protected Methods

        #endregion

        //########################################################################################

        #region	Private	Implementation

        /// <summary>
        /// Initializes the BL class.
        /// </summary>
        private void InitBL()
        {
            InitAttributes();
            InitActions();
        }

        /// <summary>
        /// Initializes the generic methods used in PNIOD data model object.
        /// </summary>
        private void InitActions()
        {
            m_IoDevice.BaseActions.RegisterMethod(RefreshPNPlannerInputData.Name, GenericMethodRefreshPNPlannerInputData);

            ConsistencyManager.RegisterConsistencyCheck(IODevice, CheckPNIodConsistency);
        }

        /// <summary>
        /// Initializes the attributes in PNIOD data model object.
        /// </summary>
        private void InitAttributes()
        {
            //Add IsSlaveType Attribute
            m_IoDevice.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.IsSlaveType, true);

            //If the interface submodule has the operating mode NoPN
            //Set IsSlaveType false
            PNIOOperatingModes operatingMode =
                (PNIOOperatingModes)
                IODevice.ParentObject.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    (uint)PNIOOperatingModes.IODevice);
            if (operatingMode == PNIOOperatingModes.None)
            {
                //Command is not necessary, because this method will be called from a command.
                m_IoDevice.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.IsSlaveType, false);
            }

            //Add attribute PNStationNumber
            m_IoDevice.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnStationNumber, 0);

            //Add PNIODeviceLocalReductionRatio, Default=1
            m_IoDevice.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoDeviceLocalReductionRatio, 1);

            //Add PNIrtSyncRole, Default=0(UnSync)
            m_IoDevice.AttributeAccess.AddAnyAttribute<byte>(
                InternalAttributeNames.PnIrtSyncRole,
                (byte)PNIRTSyncRole.NotSynchronized);

            //Add PNUpdateTimeMode, Default=0(Automatic)
            m_IoDevice.AttributeAccess.AddAnyAttribute<byte>(
                InternalAttributeNames.PnUpdateTimeMode,
                (byte)PNUpdateTimeMode.Automatic);

            //Add PNIOWatchdogFactor, Default=3
            m_IoDevice.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoWatchdogFactor, 3);

            //Add PNIOFrameClass, Default=1(RT)
            m_IoDevice.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoFrameClass, 1);

            //Add PNIoUserAdjustedUpdTime, Default=32
            m_IoDevice.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoUserAdjustedUpdTime, 32);
        }

        #region Generic methods

        /// <summary>
        /// This generic method is called before PNPlanner starts.
        /// The PNFrameData objects are created and the attributes of the
        /// frame data objects are updated with their actual values.
        /// </summary>
        /// <param name="methodData">Data of the generic method.</param>
        protected virtual void GenericMethodRefreshPNPlannerInputData(IMethodData methodData)
        {
            DeviceFrame deviceFrameGeneration = new DeviceFrame(IODevice);
            deviceFrameGeneration.RefreshPNPlannerInputDataDevice(methodData);
        }

        /// <summary>
        /// Makes necessary consistency checks for PNIOD object.
        /// </summary>
        private void CheckPNIodConsistency()
        {
            CheckUpdateTimeConsistency(IODevice);

            Interface deviceInterface = IODevice.ParentObject as Interface;

            ParamObjectRefreshPNPlannerInput frameLength = ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();

            PclObject decentralDevice = IODevice.GetDevice();

            bool pnIoSharedDeviceSupported =
                decentralDevice.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceSupported,
                    new AttributeAccessCode(),
                    false);

            frameLength.SharedDeviceSupported = pnIoSharedDeviceSupported;

            GetFrameLengthsOfInterfaceSubmodule(IODevice.ParentObject as Interface, frameLength);

            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(IODevice, false);

            // Check data length if it is not MultipleIOCR project.
            if ((pnFrameDataList == null)
                || (pnFrameDataList.Count <= 2))
            {
                CheckConsistencyMaxIoData(IODevice, frameLength);
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            // Net DATA LENGTH
            if (deviceInterface != null)
            {
                int pnMaxDeviceInputNetDataLength =
                    (int)
                    deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoMaxDeviceInputNettoDataLength,
                        ac,
                        0);
                if ((frameLength.InputNetFrameLength > pnMaxDeviceInputNetDataLength)
                    && (pnMaxDeviceInputNetDataLength != PNConstants.DefaultPNIOMaxDeviceNettoDataNotDefined))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, 
                        deviceInterface, 
                        ConsistencyConstants.InputNetFrameLengthExceeded,
                        frameLength.InputNetFrameLength,
                        pnMaxDeviceInputNetDataLength);
                }
                int pnMaxDeviceOutputNetDataLength =
                    (int)
                    deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoMaxDeviceOutputNettoDataLength,
                        ac.GetNew(),
                        0);
                if ((frameLength.OutputNetFrameLength > pnMaxDeviceOutputNetDataLength)
                    && (pnMaxDeviceOutputNetDataLength != PNConstants.DefaultPNIOMaxDeviceNettoDataNotDefined))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.OutputNetFrameLengthExceeded,
                        frameLength.OutputNetFrameLength,
                        pnMaxDeviceOutputNetDataLength);
                }
                int pnMaxDeviceNetDataLength =
                    (int)
                    deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoMaxDeviceNettoDataLength,
                        ac.GetNew(),
                        0);
                if ((frameLength.InputNetFrameLength + frameLength.OutputNetFrameLength > pnMaxDeviceNetDataLength)
                    && (pnMaxDeviceNetDataLength != PNConstants.DefaultPNIOMaxDeviceNettoDataNotDefined))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.TotalNetFrameLengthExceeded,
                        frameLength.InputNetFrameLength + frameLength.OutputNetFrameLength,
                        pnMaxDeviceNetDataLength);
                }
            }

            Interface controllerInterface = IODevice.AssignedController.ParentObject as Interface;

            bool compatiblePdevModels = CheckConsistencyUtility.CheckPDEVModelCompatibility(
                controllerInterface,
                deviceInterface);

            if (!compatiblePdevModels)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    deviceInterface,
                    ConsistencyConstants.IOControllerDoesNotSupportIODevice);
            }
        }

        /// <summary>
        /// Makes consistency checks related to maximum IO data allowed on the IO device.
        /// </summary>
        /// <param name="ioDevice">The IODevice that is being checked.</param>
        /// <param name="frameLength">Frame length data.</param>
        private void CheckConsistencyMaxIoData(PclObject ioDevice, ParamObjectRefreshPNPlannerInput frameLength)
        {
            Interface deviceInterface = ioDevice.ParentObject as Interface;
            AttributeAccessCode ac = new AttributeAccessCode();

            if (deviceInterface == null)
            {
                return;
            }

            int pnMaxDeviceInputGrossDataLength =
                (int)
                deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceInputDataLength,
                    ac,
                    (uint)PNFunctionsDefaultAttributeValues.DefaultPNIOMaxDeviceInputDataLength);

            int pnMaxDeviceOutputGrossDataLength =
                (int)
                deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceOutputDataLength,
                    ac.GetNew(),
                    (uint)PNFunctionsDefaultAttributeValues.DefaultPNIOMaxDeviceOutputDataLength);

            int pnMaxDeviceGrossDataLength =
                (int)
                deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceDataLength,
                    ac.GetNew(),
                    (uint)(pnMaxDeviceInputGrossDataLength + pnMaxDeviceOutputGrossDataLength));

            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput =
                ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();

            // If the device operates as an IDevice then we need the length of the virtual modules/submodules.
            bool isIdevice = AttributeUtilities.IsIDevice(deviceInterface);
            if (!isIdevice)
            {
                paramObjectRefreshPNPlannerInput = frameLength;

                // The MaxDataLength attributes of some old GSD files don't contain the IOPS
                // and IOCS values. They must be added afterwards to prevent the a consistency error
                // message.
                bool legacyDataLengthConsistencyCheck =
                    deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoGsdLegacyDataLengthConsistencyCheck,
                        new AttributeAccessCode(),
                        false);
                if (legacyDataLengthConsistencyCheck)
                {
                    int totalIocsLength;
                    int totalIopsLength;

                    GetTotalIopsIocsLength(deviceInterface, out totalIocsLength, out totalIopsLength);

                    pnMaxDeviceInputGrossDataLength += totalIopsLength;
                    pnMaxDeviceOutputGrossDataLength += totalIocsLength;
                    pnMaxDeviceGrossDataLength += totalIocsLength + totalIopsLength;
                }
            }

            // Check data length
            // Gross DATA LENGTH
            int numberOfAr =
                Convert.ToInt32(
                    deviceInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoNumberOfAR,
                        ac.GetNew(),
                        1), CultureInfo.InvariantCulture);

            //in case of IDevice these checks are only needed 
            //if the PNIoNumberOfAr is 1 (default) (so if is not shared-IDevice)
            if (!(isIdevice && (numberOfAr != 1)))
            {
                if (paramObjectRefreshPNPlannerInput.InputGrossFrameLength
                    > Math.Min(pnMaxDeviceInputGrossDataLength, PNConstants.MaxIODataLengthDeviceindependent))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN,
                        LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.InputGrossFrameLengthExceeded,
                        paramObjectRefreshPNPlannerInput.InputGrossFrameLength,
                        Math.Min(pnMaxDeviceInputGrossDataLength, PNConstants.MaxIODataLengthDeviceindependent));
                }

                if (paramObjectRefreshPNPlannerInput.OutputGrossFrameLength
                    > Math.Min(pnMaxDeviceOutputGrossDataLength, PNConstants.MaxIODataLengthDeviceindependent))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN,
                        LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.OutputGrossFrameLengthExceeded,
                        paramObjectRefreshPNPlannerInput.OutputGrossFrameLength,
                        Math.Min(pnMaxDeviceOutputGrossDataLength, 
                            PNConstants.MaxIODataLengthDeviceindependent));
                }
            }

            int calculatedGrossDataLength;
            if (isIdevice && (numberOfAr > 1))
            {
                calculatedGrossDataLength = Math.Min(
                    pnMaxDeviceGrossDataLength,
                    PNConstants.MaxIODataLengthDeviceindependent * 2 * numberOfAr);
            }
            else
            {
                calculatedGrossDataLength = Math.Min(
                    pnMaxDeviceGrossDataLength,
                    PNConstants.MaxIODataLengthDeviceindependent * 2);
            }

            if (paramObjectRefreshPNPlannerInput.InputGrossFrameLength + paramObjectRefreshPNPlannerInput.OutputGrossFrameLength
                > calculatedGrossDataLength)
            {
                ConsistencyLogger.Log(ConsistencyType.PN,
                    LogSeverity.Error,
                    deviceInterface,
                    ConsistencyConstants.TotalGrossFrameLengthExceeded,
                    paramObjectRefreshPNPlannerInput.InputGrossFrameLength
                    + paramObjectRefreshPNPlannerInput.OutputGrossFrameLength,
                    calculatedGrossDataLength);
            }
        }

        /// <summary>
        /// Gets the total IOPS and IOCS lengths of an interface submodule.
        /// </summary>
        /// <param name="deviceinterfaceSubmodule">The interface submodule whose IOPS and IOCS lengths will be retrieved.</param>
        /// <param name="totalIocsLength">Out parameter containing IOCS length.</param>
        /// <param name="totalIopsLength">Out parameter containing IOPS length.</param>
        private void GetTotalIopsIocsLength(
            Interface deviceinterfaceSubmodule,
            out int totalIocsLength,
            out int totalIopsLength)
        {
            totalIocsLength = 0;
            totalIopsLength = 0;
            IList<PclObject> modules = PNNavigationUtility.GetModulesFromInterfaceSubmodule(deviceinterfaceSubmodule);
            bool discardIOXS = !Utility.GetIOXSRequired(deviceinterfaceSubmodule);
            foreach (PclObject module in modules)
            {
                GetIopsIocsLengthofModule(module, ref totalIocsLength, ref totalIopsLength, discardIOXS);
            }
        }

        /// <summary>
        /// Gets the total IOPS and IOCS lengths of a module.
        /// </summary>
        /// <param name="module">The module whose IOPS and IOCS lengths will be retrieved.</param>
        /// <param name="iocsLength">Out parameter containing IOCS length.</param>
        /// <param name="iopsLength">Out parameter containing IOPS length.</param>
        /// <param name="discardIOXS"></param>
        private void GetIopsIocsLengthofModule(
            PclObject module,
            ref int iocsLength,
            ref int iopsLength,
            bool discardIOXS)
        {
            if (module is Interface
                || module is DataModel.PCLObjects.Port
                || module is Submodule)
            {
                DataAddress inAdr;
                DataAddress outAdr;
                bool isDiagAdr;

                bool hasAdr = AddressUtility.GetAddressObjectsOfModule(module, out inAdr, out outAdr, out isDiagAdr);
                bool isIOEnabled = false;

                if (discardIOXS)
                {
                    if (hasAdr
                        && !isDiagAdr
                        && ((inAdr != null) || (outAdr != null)))
                    {
                        isIOEnabled = true;
                    }
                }

                int iocs, iops;
                if (discardIOXS && !isIOEnabled)
                {
                    iops = iocs = 0;
                }
                else
                {
                    iocs = module.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIOCSLength,
                        new AttributeAccessCode(),
                        s_DefaultIocsLength);
                    iocsLength += iocs;

                    iops = module.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIOPSLength,
                        new AttributeAccessCode(),
                        s_DefaultIopsLength);
                    iopsLength += iops;
                }

                if ((inAdr != null)
                    && (outAdr != null))
                {
                    iocsLength += iocs;
                    iopsLength += iops;
                }
            }

            List<PclObject> elements = (List<PclObject>)module.GetElements();
            if (null != elements)
            {
                foreach (PclObject element in elements)
                {
                    GetIopsIocsLengthofModule(element, ref iocsLength, ref iopsLength, discardIOXS);
                }
            }
        }

        /// <summary>
        /// Gets the frame lengths of a device interface submodule.
        /// </summary>
        /// <param name="decentralDeviceInterfaceSubmodule">The device interface submodule whose frame lengths will be retrieved.</param>
        /// <param name="paramObjectRefreshPNPlannerInput">The object that contains frame length data.</param>
        private void GetFrameLengthsOfInterfaceSubmodule(
            Interface decentralDeviceInterfaceSubmodule,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput)
        {
            paramObjectRefreshPNPlannerInput.DiscardIOXS = !Utility.GetIOXSRequired(decentralDeviceInterfaceSubmodule);

            AttributeAccessCode ac = new AttributeAccessCode();

            List<PclObject> modules =
                PNNavigationUtility.GetModulesFromInterfaceSubmodule(decentralDeviceInterfaceSubmodule);

            foreach (PclObject module in modules)
            {
                int iocsLengthOfModule = module.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIOCSLength,
                    ac.GetNew(),
                    s_DefaultIocsLength);

                int iopsLengthOfModule = module.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIOPSLength,
                    ac.GetNew(),
                    s_DefaultIopsLength);

                Utility.GetAddressLengthOfDeviceItem(
                    module,
                    paramObjectRefreshPNPlannerInput,
                    iocsLengthOfModule,
                    iopsLengthOfModule);
            }
        }

        /// <summary>
        /// Makes consistency checks related to the update time of the device.
        /// </summary>
        /// <param name="ioDevice">The IO device that is being checked.</param>
        private void CheckUpdateTimeConsistency(PNIOD ioDevice)
        {
            CheckConsistencyUtility.CheckConsistencyUpdateTime(ioDevice);

            CheckConsistencyUtility.ConsistencyCheck_UpdateTimeWatchDog(ioDevice);
        }

        #endregion

        #endregion
    }
}