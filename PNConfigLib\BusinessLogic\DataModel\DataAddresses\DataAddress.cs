/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DataAddress.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.DataModel.DataAddresses
{
    /// <summary>
    /// Represents the data addresses of submodules.
    /// </summary>
    internal class DataAddress
    {
        /// <summary>
        /// IO type.
        /// </summary>
        public readonly IoTypes IoType;

        /// <summary>
        /// Length of the data.
        /// </summary>
        public readonly int Length;

        /// <summary>
        /// Start address.
        /// </summary>
        public readonly int StartAddress;

        /// <summary>
        /// The constructor for DataAddress.
        /// </summary>
        /// <param name="ioType">IO type.</param>
        /// <param name="startAddress">Start address.</param>
        /// <param name="length">Length of the data.</param>
        public DataAddress(IoTypes ioType, int startAddress, int length)
        {
            IoType = ioType;
            StartAddress = startAddress;
            Length = length;
        }

        public int GetProcessImagePartNumber(int? defaultValue = null)
        {
            if (defaultValue.HasValue)
            {
                return defaultValue.Value;
            }
            throw new System.ArgumentException("no defaultValue", nameof(defaultValue));
        }

        public int GetBitOffset(int? defaultValue = null)
        {
            if (defaultValue.HasValue)
            {
                return defaultValue.Value;
            }
            throw new System.ArgumentException("no defaultValue", nameof(defaultValue));
        }

        public int GetTag(int? defaultValue = null)
        {
            if (defaultValue.HasValue)
            {
                return defaultValue.Value;
            }
            throw new System.ArgumentException("no defaultValue", nameof(defaultValue));
        }
    }
}