﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleConfigurator.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader
{
    internal static class ModuleConfigurator
    {
        private static List<ModuleType> GetModulesBySlotRelation(
          DecentralDeviceCatalog decentralDeviceCatalog,
          DecentralDeviceType xmlDecentralDevice,
          string gsdPath,
          SlotRelationType slotRelType)
        {
            List<ModuleType> retval = new List<ModuleType>();

            Dictionary<KeyValuePair<string, int>, ModuleCatalog> moduleLookup =
                ProjectManagerUtilities.GetModuleLookupBySlotRelation(decentralDeviceCatalog, gsdPath, slotRelType);
            int pnDeviceId = decentralDeviceCatalog.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnDeviceId,
                new AttributeAccessCode(),
                0);
            int fixedModuleCounter = 0;
            foreach (KeyValuePair<string, int> moduleInfo in moduleLookup.Keys)
            {
                if ((xmlDecentralDevice.Module != null)
                    && ((xmlDecentralDevice.Module.Count == 0)
                        || (xmlDecentralDevice.Module.Count > 0
                            && (xmlDecentralDevice.Module.All(x => x.GSDRefID != moduleInfo.Key)
                                || !xmlDecentralDevice.Module.Exists(
                                    x => x.GSDRefID == moduleInfo.Key && x.SlotNumber == moduleInfo.Value)))))
                {
                    ModuleType xmlModule =
                        new ModuleType
                        {
                            GSDRefID = moduleInfo.Key,
                            SlotNumber = (ushort)moduleInfo.Value,
                            ModuleID = string.Format(
                                    CultureInfo.InvariantCulture,
                                    "{0}_{1}_{2}_{3}",
                                    moduleInfo.Key,
                                    ++fixedModuleCounter,
                                    pnDeviceId, moduleInfo.Value)
                        };
                    retval.Add(xmlModule);
                }
            }
            return retval;
        }


        internal static void FillModuleValues(DecentralDeviceType xmlDecentralDevice, DecentralDeviceCatalog decentralDeviceCatalog, string gsdPath)
        {
            List<ModuleType> fixedModules = GetModulesBySlotRelation(
                decentralDeviceCatalog,
                xmlDecentralDevice,
                gsdPath,
                SlotRelationType.FixedInSlots);

            List<ModuleType> usedInSlotModules = GetModulesBySlotRelation(
                decentralDeviceCatalog,
                xmlDecentralDevice,
                gsdPath,
                SlotRelationType.UsedInSlots);

            if (usedInSlotModules != null)
            {
                foreach (ModuleType module in usedInSlotModules)
                {
                    if (xmlDecentralDevice.Module.Any(x => x.GSDRefID == module.GSDRefID) && xmlDecentralDevice.Module.Any(x => x.SlotNumber == module.SlotNumber))
                    {
                        continue;
                    }

                    if (
                        xmlDecentralDevice.Module.Any(
                            x => (x.SlotNumber == module.SlotNumber) && string.IsNullOrEmpty(x.GSDRefID)))
                    {
                        ModuleType currentModule =
                            xmlDecentralDevice.Module.FirstOrDefault(
                                x => (x.SlotNumber == module.SlotNumber) && string.IsNullOrEmpty(x.GSDRefID));
                        xmlDecentralDevice.Module.Remove(currentModule);
                        continue;
                    }

                    if (
                        xmlDecentralDevice.Module.Any(
                            x => (x.SlotNumber == module.SlotNumber) && (x.GSDRefID != module.GSDRefID)))
                    {
                        continue;
                    }
                    xmlDecentralDevice.Module.Add(module);
                }
            }
            xmlDecentralDevice.Module.AddRange(fixedModules);
        }
    }
}
