/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IoTypes.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;

namespace PNConfigLib.HWCNBL.Constants.AttributeValues
{
    /// <summary>
    /// IO type enum.
    /// </summary>
    [Flags]
    public enum IoTypes
    {
        /// <summary>
        /// No IO.
        /// </summary>
        None = 0x0000,

        /// <summary>
        /// Input IO.
        /// </summary>
        Input = 0x0001,

        /// <summary>
        /// Output IO.
        /// </summary>
        Output = 0x0002,

        /// <summary>
        /// </summary>
        Diagnosis = 0x0040,

        /// <summary>
        /// </summary>
        Substitute = 0x0010,

        /// <summary>
        /// </summary>
        OnBoard = 0x0020,

        /// <summary>
        /// </summary>
        Laddr = 0x0080
    }
}