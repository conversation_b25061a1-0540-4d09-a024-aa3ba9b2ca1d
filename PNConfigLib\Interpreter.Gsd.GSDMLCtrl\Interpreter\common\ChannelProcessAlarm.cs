/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ChannelProcessAlarm.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
	/// <summary>
    /// The ChannelProcessAlarm object describes channel type specific error texts.
	/// It contains properties to specify the error type of a specific channel.
	/// </summary>
    //[ComVisible(true), Guid("16E165E3-65D9-4f79-8E87-A4972D684F2E")] 
	public class ChannelProcessAlarm : 
		GsdObject,
        IChannelProcessAlarm
	{
		//########################################################################################
		#region Initialization & Termination

		/// <summary>
        /// Initializes the ChannelProcessAlarm if it is instantiated.
		/// The properties of the object are all initialized to empty 
		/// or with abstract default values.
		/// </summary>
		/// <remarks>The real content can only be written to the object by the Fill
		/// method, which is only available within this assembly.</remarks>
        public ChannelProcessAlarm()
		{
			m_Reason = 0;
			m_AlarmName = String.Empty;
			m_AlarmHelp = String.Empty;
            m_AlarmHelpTextId = String.Empty;
            m_AlarmNameTextId = String.Empty;
		}

		#endregion

		//########################################################################################
		#region Fields

		// Declaration of the properties
		private uint m_Reason;
        private string m_AlarmName;
        private string m_AlarmNameTextId;
        private string m_AlarmHelp;
        private string m_AlarmHelpTextId;
        private ArrayList m_ExtChannelProcessAlarms;
        private ArrayList m_ExtChanProcAlReasonAddValues;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the reason of channel process alarm.
        /// </summary>
        public UInt32 Reason => m_Reason;

        /// <summary>
        /// Accesses the name for the channel process alarm.
        /// </summary>
        public string Name => m_AlarmName;

        /// <summary>
        /// Accesses the name text id for the channel process alarm. Not accessible via COM
        /// </summary>
        public string NameTextId => m_AlarmNameTextId;

        /// <summary>
        /// Accesses the help text for the channel process alarm.
        /// </summary>
        public string Help => m_AlarmHelp;

        /// <summary>
        /// Accesses the help text id for the channel process alarm. Not accessible via COM
        /// </summary>
        public string HelpTextId => m_AlarmHelpTextId;

        /// <summary>
        /// Accesses the list of extended channel process alarm objects.
        /// </summary>
        public Array ExtChannelProcessAlarms =>
            m_ExtChannelProcessAlarms?.ToArray();

        /// <summary>
        /// Accesses the list of extended channel process alarm reason additional value objects.
        /// </summary>
        public Array ExtChanProcAlReasonAddValues =>
            m_ExtChanProcAlReasonAddValues?.ToArray();

#if !S7PLUS
        #region COM Interface Members Only

        // ONLY for the COM interface.
        // object GSDI.IChannelProcessAlarm.Reason
        // {
        //     get { return Reason; }
        // }
        #endregion
#endif

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter
				if (null == hash)
					throw new FillException("The input hash table parameter must not be 'null'!");

                // Own data.
                string member = Models.s_FieldReason;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    m_Reason = (uint)hash[member];

                member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_AlarmName = hash[member] as string;

                member = Models.s_FieldNameTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_AlarmNameTextId = hash[member] as string;

                member = Models.s_FieldHelp;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_AlarmHelp = hash[member] as string;

                member = Models.s_FieldHelpTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_AlarmHelpTextId = hash[member] as string;

                //
                member = Models.s_FieldExtChannelProcessAlarms;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_ExtChannelProcessAlarms = hash[member] as ArrayList;

                member = Models.s_FieldExtChanProcAlReasonAddValues;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_ExtChanProcAlReasonAddValues = hash[member] as ArrayList;


                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectChannelProcessAlarm);

			// ----------------------------------------------
			SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successful, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// ----------------------------------------------
			Export.WriteUint32Property(ref writer, Models.s_FieldReason, m_Reason);
			Export.WriteStringProperty(ref writer, Models.s_FieldName, m_AlarmName);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, m_AlarmNameTextId);
			Export.WriteStringProperty(ref writer, Models.s_FieldHelp, m_AlarmHelp);
            Export.WriteStringProperty(ref writer, Models.s_FieldHelpTextId, m_AlarmHelpTextId);
            //
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldExtChannelProcessAlarms, m_ExtChannelProcessAlarms);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldExtChanProcAlReasonAddValues, m_ExtChanProcAlReasonAddValues);

			return true; 
		}


		#endregion
	}
}