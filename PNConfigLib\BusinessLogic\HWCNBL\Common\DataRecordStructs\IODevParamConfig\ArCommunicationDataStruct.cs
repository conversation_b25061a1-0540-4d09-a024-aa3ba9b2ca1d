/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ArCommunicationDataStruct.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.IODevParamConfig
{
    /// <summary>
    /// The data record object for ArCommunicationData.
    /// </summary>
    internal class ArCommunicationDataStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for ArCommunicationDataStruct.
        /// </summary>
        public ArCommunicationDataStruct()
        {
            BlockType = DataRecords.BlockTypes.ArCommunicationData;
            BlockLength = 0x001C;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[24];
        }

        /// <summary>
        /// ARBlockVersion part of the data record.
        /// </summary>
        public int ARBlockVersion
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }

        /// <summary>
        /// ARProperties part of the data record.
        /// </summary>
        public uint ARProperties
        {
            set { BufferManager.Write32(Data, 20, value); }
            get { return BufferManager.Read32(Data, 20); }
        }

        /// <summary>
        /// ARType part of the data record.
        /// </summary>
        public int ARType
        {
            set { Transformator.Write16(Data, 2, (ushort)value); }
            get { return Transformator.Read16(Data, 2); }
        }

        /// <summary>
        /// ARUuid part of the data record.
        /// </summary>
        public byte[] ARUuid
        {
            set { BufferManager.WriteBuffer(Data, 4, value); }
            get { return BufferManager.ReadBuffer(Data, 4, 12); }
        }

        public bool RejectDcpSetRequests
        {
            set { BufferManager.WriteBool(Data, 20, 3, value); }
            get { return BufferManager.ReadBool(Data, 20, 3); }
        }
    }
}