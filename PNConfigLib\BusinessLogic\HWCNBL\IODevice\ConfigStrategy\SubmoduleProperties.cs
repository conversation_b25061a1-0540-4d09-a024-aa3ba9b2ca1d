/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SubmoduleProperties.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    /// <summary>
    /// SubmoduleProperties block related part of PNDeviceConfigStrategy class.
    /// </summary>
    internal partial class PNDeviceConfigStrategy
    {
        //########################################################################################

        #region Properties

        /// <summary>
        /// The accessor for m_SubmodulePropertiesData.
        /// </summary>
        protected virtual IDictionary<string, List<SubmodulePropertiesSubslotStruct>> SubmodulePropertiesData
        {
            set { m_SubmodulePropertiesData = value; }
            get { return m_SubmodulePropertiesData; }
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        #endregion

        //########################################################################################

        #region Constants and Enums

        #endregion

        //########################################################################################

        #region Fields

        /// <summary>
        /// The dictionary that contains SubmoduleProperties data.
        /// </summary>
        private IDictionary<string, List<SubmodulePropertiesSubslotStruct>> m_SubmodulePropertiesData;

        /// <summary>
        /// Whether submodule properties block is required.
        /// </summary>
        private bool m_IsSubmodulePropertiesRequired;

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion

        //########################################################################################

        #region Public Methods

        /// <summary>
        /// Gets the submodule properties subslot data block.
        /// </summary>
        /// <param name="module">The module whose subslot data block will be retrieved.</param>
        /// <param name="submodule">The submodule whose subslot data block will be retrieved.</param>
        /// <param name="api">The api number.</param>
        /// <returns></returns>
        public virtual byte[] GetSubmodulePropertiesSubslotData(PclObject module, PclObject submodule, long api)
        {
            int subslotNumber = GetSubmodulesPNSubslotNumber(submodule);

            if (ConfigData.ContainsKey(module.Id))
            {
                //get slot configuration of given module
                List<SubmodulePropertiesSubslotStruct> slotConfiguration = SubmodulePropertiesData[module.Id];

                foreach (SubmodulePropertiesSubslotStruct subslotConfiguration in slotConfiguration)
                {
                    //filter relevant subslots
                    if ((subslotConfiguration.SubslotNumber == subslotNumber)
                        && (subslotConfiguration.api == api))
                    {
                        return subslotConfiguration.ToByteArray;
                    }
                }
            }

            return new byte[0];
        }

        /// <summary>
        /// Gets whether SubmoduleProperties block should be created.
        /// </summary>
        /// <remarks>
        /// If Advanced Startup Mode is not active, the block shouldn't be generated.
        /// </remarks>
        /// <returns>Whether SubmoduleProperties block should be created.</returns>
        public bool IsSubmodulePropertiesRequired()
        {
            uint arProperties = GetArProperties();

            bool isAdvancedStartupModeActive = ((arProperties >> 30) & 1) == (uint)PNIOARStartupMode.Advanced;

            return m_IsSubmodulePropertiesRequired && isAdvancedStartupModeActive;
        }

        #endregion

        //########################################################################################

        #region Private Implementation

        /// <summary>
        /// Initializes the SubmoduleProperties data.
        /// </summary>
        private void InitializeSubmoduleProperties()
        {
            if (SubmodulePropertiesData == null)
            {
                SubmodulePropertiesData = new Dictionary<string, List<SubmodulePropertiesSubslotStruct>>();
            }
            else
            {
                SubmodulePropertiesData.Clear();
            }

            m_IsSubmodulePropertiesRequired = false;

            foreach (PclObject module in Modules)
            {
                //IConfigBase module = interfaceSubmodule.HwcnBasicsFacade.GetConfigObject(moduleCore);
                List<SubmodulePropertiesSubslotStruct> slotConfiguration = new List<SubmodulePropertiesSubslotStruct>();

                IList<PclObject> submodules = GetSubmodulesInternal(module);
                foreach (PclObject submodule in submodules)
                {
                    SubmodulePropertiesSubslotStruct subslotConfiguration = CreateSPSubslotStruct(submodule);
                    if ((subslotConfiguration != null)
                        && (subslotConfiguration.CombinedObjectContainerGroupNumber != 0))
                    {
                        slotConfiguration.Add(subslotConfiguration);
                        m_IsSubmodulePropertiesRequired = true;
                    }
                }

                SubmodulePropertiesData.Add(module.Id, slotConfiguration);
            }
        }

        /// <summary>
        /// Creates submodule properties subslot struct.
        /// </summary>
        /// <param name="submodule">The submodule whose submodule properties struct will be created.</param>
        /// <returns>The submodule properties subslot struct.</returns>
        protected virtual SubmodulePropertiesSubslotStruct CreateSPSubslotStruct(PclObject submodule)
        {
            SubmodulePropertiesSubslotStruct subslot = new SubmodulePropertiesSubslotStruct();
            AttributeAccessCode ac = new AttributeAccessCode();

            subslot.SubslotNumber = GetSubmodulesPNSubslotNumber(submodule);
            subslot.api = submodule.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnAPI, ac, 0);

            #region Combined Object Container

            if (PNAttributeUtility.IsCombinedObjectSupportedForIOD(this.Interface))
            {
                if (submodule is Interface)
                {
                    subslot.CombinedObjectContainerGroupNumber = 1;
                    subslot.IsAccessPoint = 1;
                }
                else if (submodule is DataModel.PCLObjects.Port)
                {
                    subslot.CombinedObjectContainerGroupNumber = 1;
                    subslot.IsAccessPoint = 0;
                }
            }

            #endregion

            return subslot;
        }

        #endregion
    }
}