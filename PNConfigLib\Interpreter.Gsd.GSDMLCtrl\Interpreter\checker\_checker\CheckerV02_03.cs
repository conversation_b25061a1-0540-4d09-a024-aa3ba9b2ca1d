/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_03.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;

using GSDI;
using System.Text.RegularExpressions;
using System.Diagnostics;
using PNConfigLib.Gsd.Interpreter;
using System.Runtime.InteropServices;
using System.IO;
using PNConfigLib.Gsd.Interpreter.Checker;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.3 and is based on GSD(ML) versions 2.25 and lower.
    ///		
    /// </summary>
    internal class CheckerV0203 : CheckerV02025
    {
        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV0203;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV0203;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00020002);
            Checks.Add(Constants.s_Cn_0X00020003);
            Checks.Add(Constants.s_Cn_0X00020008);
            Checks.Add(Constants.s_Cn_0X00020010);
            Checks.Add(Constants.s_Cn_0X00020012);
            Checks.Add(Constants.s_Cn_0X00020013);
            Checks.Add(Constants.s_Cn_0X00020014);
            Checks.Add(Constants.s_Cn_0X00020016);
            Checks.Add(Constants.s_Cn_0X00020017);
            Checks.Add(Constants.s_Cn_0X00020018);
            Checks.Add(Constants.s_Cn_0X00020020);
            Checks.Add(Constants.s_Cn_0X00020021);
            Checks.Add(Constants.s_Cn_0X00020022);
            Checks.Add(Constants.s_Cn_0X00020023);
            Checks.Add(Constants.s_Cn_0X00020024);
            Checks.Add(Constants.s_Cn_0X00020025);
            Checks.Add(Constants.s_Cn_0X00020026);
            Checks.Add(Constants.s_Cn_0X00020027);
            Checks.Add(Constants.s_Cn_0X00020028);
            Checks.Add(Constants.s_Cn_0X00020029);
            Checks.Add(Constants.s_Cn_0X0002002A);
            Checks.Add(Constants.s_Cn_0X0002002B);
            Checks.Add(Constants.s_Cn_0X0002002C);
            Checks.Add(Constants.s_Cn_0X0002002D);
            Checks.Add(Constants.s_Cn_0X0002002E);
            Checks.Add(Constants.s_Cn_0X00020030);
            Checks.Add(Constants.s_Cn_0X00020031);
            Checks.Add(Constants.s_Cn_0X00020032);

            return succeeded;
        }
        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();


            try
            {
                Checks.Remove(Constants.s_Cn_0X00011109);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }
        #endregion

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.3.
        /// </summary>
        public CheckerV0203()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version23);
        }

        #endregion

        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:CertificationInfo/@ApplicationClass" +
                            " | .//gsddef:ApplicationRelations/@StartupMode" +
                            " | .//gsddef:RT_Class3Properties/@StartupMode" +
                            " | .//gsddef:RT_Class3Properties/@ForwardingMode" +
                            " | .//gsddef:F_ParameterRecordDataItem/@F_SupportedParameters";
                return (xp);
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList +
                            " | .//gsddef:DeviceAccessPointItem/@ResetToFactoryModes";
                return (xp);
            }
        }

        protected override ReportTypes ReportType_0X00012208 => ReportTypes.GSD_RT_Error;

        protected virtual ReportTypes ReportType_0x00020021_4 => ReportTypes.GSD_RT_Warning;

        protected virtual ReportTypes ReportType_0x00020008 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0x0002002E => ReportTypes.GSD_RT_MinorError;

        protected virtual string Msg_0x00020025_2 => "0x00020025_2";

        protected virtual string Msg_0x00020026_1 => "0x00020026_1";

        protected virtual string Msg_0x00020026_2 => "0x00020026_2";

        #endregion

        #region Methods

        private IList<string> FindParamIds(XContainer elem)
        {
            // Collect the parameter ids
            var listParamIds = new List<string>();
            var parameterIds = elem.Descendants(NamespaceGsdDef + Elements.s_Ref);
            foreach (var an in parameterIds)
            {
                var parameterId = an.Attribute(Attributes.ID);
                if (parameterId != null)
                {
                    bool visible = true;    // Default value
                    string strVisible = Help.GetAttributeValueFromXElement(an, Attributes.s_Visible);
                    if (!string.IsNullOrEmpty(strVisible))
                        visible = XmlConvert.ToBoolean(strVisible);

                    if (visible)
                    {
                        string valueParamRefId = parameterId.Value;
                        if (!string.IsNullOrEmpty(valueParamRefId))
                        {
                            listParamIds.Add(Help.CollapseWhitespace(valueParamRefId));
                        }
                    }
                }
                else
                {
                    // Warning: When using menus, all 'Ref' elements must have an 'ID'.
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        string msg = Help.GetMessageString("M_0x00020008_1");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(ReportType_0x00020008, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00020008_1");
                    }
                }
            }
            return listParamIds;
        }
        private IList<string> FindParamReferences(XContainer elem)
        {
            // Collect the parameter references
            var listParamRefs = new List<string>();
            var menuRefs = elem.Descendants(NamespaceGsdDef + Elements.s_ParameterRef);
            foreach (var an in menuRefs)
            {
                string valueParameterTarget = Help.GetAttributeValueFromXElement(an, Attributes.s_ParameterTarget); // required
                listParamRefs.Add(valueParameterTarget);
            }
            return listParamRefs;
        }

        private IList<string> FindMenuItemIds(XContainer elem)
        {
            // Collect the menu item ids
            var listMenuItemIds = new List<string>();
            var menuItems = elem.Descendants(NamespaceGsdDef + Elements.s_MenuItem);
            foreach (var an in menuItems)
            {
                string valueMenuItemId = Help.GetAttributeValueFromXElement(an, Attributes.ID);  // required
                listMenuItemIds.Add(Help.CollapseWhitespace(valueMenuItemId));
            }
            return listMenuItemIds;
        }

        private IList<string> FindMenuReferences(XContainer elem)
        {
            // Collect the menu references
            var listMenuRefs = new List<string>();
            var menuRefs = elem.Descendants(NamespaceGsdDef + Elements.s_MenuRef);
            foreach (var an in menuRefs)
            {
                string valueMenuTarget = Help.GetAttributeValueFromXElement(an, Attributes.s_MenuTarget);  // required
                listMenuRefs.Add(valueMenuTarget);

            }
            return listMenuRefs;
        }

        private string GetTextId(XContainer elem)
        {
            string textId = string.Empty;
            var names = elem.Descendants(NamespaceGsdDef + Elements.s_Name).ToList();
            if (names.Count == 0)
                return textId;

            // Name is available only once
            var name = names[0];

            textId = Help.GetAttributeValueFromXElement(name, Attributes.s_TextId);

            return textId;
        }

        private XElement GetReferencedMenu(XContainer pr, XElement mref)
        {
            string refID = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(mref, Attributes.s_MenuTarget));
            var menus = pr.Descendants(NamespaceGsdDef + Elements.s_MenuItem);
            foreach (XElement menu in menus)
            {
                string menuID = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(menu, Attributes.ID));
                if (refID == menuID)
                    return menu;
            }
            return null;
        }

        /// <summary>
        /// Checks, if  submodule which is fixed plugged with this DAP has the attribute IsochroneModeRequired = "true".
        /// 
        /// </summary>
        /// <returns>True, if a submodule with IsochroneModeRequired = "true" found.</returns>
        protected bool SubmoduleWithIsochroneModeRequired(XElement dap)
        {
            // Make sure that it is really a DAP.

            if (dap.Name.LocalName != Elements.s_DeviceAccessPointItem)
            {
                return false;
            }

            // (1) Check all VirtualSubmoduleItems at the DAP
            if (CheckVirtualSubmoduleList(dap))
            {
                return true;
            }



            // (2) Check all UseableSubmodules at the DAP
            if (CheckUseableSubmodules(dap))
            {
                return true;
            }

            // (3) Check all VirtualSubmoduleItem of fixed modules at the DAP
            // (4) Check all UseableSubmodules of fixed modules at the DAP
            var useableModules = dap.Element(NamespaceGsdDef + Elements.s_UseableModules);
            if (useableModules == null)
                return false;

            if (CheckModuleItemRef(useableModules))
            {
                return true;
            }
            return false;

        }

        private bool CheckModuleItemRef(XContainer useableModules)
        {
            var moduleItemRefs = useableModules.Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);
            foreach (var moduleItemRef in moduleItemRefs)
            {
                if (String.IsNullOrEmpty(Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_FixedInSlots)))
                    continue;

                string moduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget)); // required
                PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                if (null == module)
                    continue;

                // (3) Check all VirtualSubmoduleItem of fixed modules at the DAP
                if (CheckModuleItemRefForVirtualSubmoduleItem(module))
                {
                    return true;
                }

                // (4) Check all UseableSubmodules of fixed modules at the DAP
                if (CheckModuleItemRefForUseableSubmodules(moduleItemRef))
                {
                    return true;
                }
            }

            return false;
        }

        private bool CheckModuleItemRefForUseableSubmodules(XElement moduleItemRef)
        {
            if (!ModuleRefToSubmoduleDictionary.TryGetValue(moduleItemRef, out IList<XElement> submodulesOfModule))
            {
                return false;
            }

            foreach (XElement submodule in submodulesOfModule)
            {
                if (IsIsochroneModeRequired(submodule))
                    return true;
            }

            return false;
        }

        private bool CheckModuleItemRefForVirtualSubmoduleItem(XContainer module)
        {
            var virtualSubmoduleListModule = module.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
            if (virtualSubmoduleListModule == null)
            {
                return false;
            }

            var virtualSubmoduleItems =
                virtualSubmoduleListModule.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
            {
                if (IsIsochroneModeRequired(virtualSubmoduleItem))
                    return true;
            }

            return false;
        }

        private bool CheckUseableSubmodules(XContainer dap)
        {
            var useableSubmodules = dap.Element(NamespaceGsdDef + Elements.s_UseableSubmodules);
            if (useableSubmodules == null)
            {
                return false;
            }
            var submoduleItemRefs = useableSubmodules.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                if (String.IsNullOrEmpty(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_FixedInSubslots)))
                {
                    continue;
                }
                string submoduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget)); // required
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                if (null == submodule)
                    continue;

                if (IsIsochroneModeRequired(submodule))
                    return true;
                break;
            }
            return false;
        }
        private bool CheckVirtualSubmoduleList(XContainer dap)
        {
            var virtualSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
            if (virtualSubmoduleList == null)
            {
                return false;
            }
            var virtualSubmoduleItems = virtualSubmoduleList.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
            {
                if (IsIsochroneModeRequired(virtualSubmoduleItem))
                    return true;
            }
            return false;
        }




        private bool IsIsochroneModeRequired(XContainer submoduleItem)
        {
            var isochroneMode = submoduleItem.Element(NamespaceGsdDef + Elements.s_IsochroneMode);
            if (isochroneMode != null)
            {
                bool isochroneModeRequired = false;
                string isochroneModeRequiredStr = Help.GetAttributeValueFromXElement(isochroneMode, Attributes.s_IsochroneModeRequired);
                if (!string.IsNullOrEmpty(isochroneModeRequiredStr))
                    isochroneModeRequired = XmlConvert.ToBoolean(isochroneModeRequiredStr);
                if (isochroneModeRequired)
                    return true;
            }
            return false;
        }

        /// <summary>
        /// Checks if the DAP supports system redundancy.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>true, if @DeviceType is R2.</returns>
        protected override bool DapRedundancySupported(XElement dap)
        {
            IList<string> deviceTypeList = GetDeviceTypes(dap);

            // Find the most important redundancy type
            if (deviceTypeList.Contains(Enums.s_R2))
                return true;

            return false;
        }

        /// <summary>
        /// Gets the device types from the DAP.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>list with device types.</returns>
        protected virtual IList<string> GetDeviceTypes(XElement dap)
        {
            IList<string> deviceTypeList = new List<string>();
            var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);

            if (systemRedundancy != null)
            {
                string valueDeviceType = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(systemRedundancy, Attributes.s_DeviceType));
                deviceTypeList.Add(valueDeviceType);
            }

            return deviceTypeList;
        }

        /// <summary>
        /// Checks if the DAP supports R2 system redundancy.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>true, if @DeviceType is R2.</returns>
        protected virtual bool R2RedundancySupported(XElement dap)
        {
            IList<string> deviceTypeList = GetDeviceTypes(dap);

            // Find the most important redundancy type
            if (deviceTypeList.Contains(Enums.s_R2))
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the DAP supports S2 system redundancy.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>true, if @DeviceType is S2.</returns>
        protected virtual bool S2RedundancySupported(XElement dap)
        {
            IList<string> deviceTypeList = GetDeviceTypes(dap);

            // Find the most important redundancy type
            if (deviceTypeList.Contains(Enums.s_S2))
                return true;

            return false;
        }

        /// <summary>
        /// Gets the 'CombinedApplicationClasses' from the DAP.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>list with device types.</returns>
        protected virtual IList<string> GetCombinedApplicationClasses(XElement dap)
        {
            var certificationInfo = dap.Element(NamespaceGsdDef + Elements.s_CertificationInfo);

            if (certificationInfo == null)
                return (new List<string>());

            string applicationClass = Help.GetAttributeValueFromXElement(certificationInfo, Attributes.s_ApplicationClass);
            IList<string> combinedApplClassList = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));
            var certificationInfoExtList = dap.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);
            foreach (var certificationInfoExt in certificationInfoExtList)
            {
                applicationClass = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ApplicationClass);
                var applClassExtList = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));
                foreach (string token in applClassExtList)
                {
                    if (!combinedApplClassList.Contains(token))
                    {
                        combinedApplClassList.Add(token);
                    }
                }
            }

            return combinedApplClassList;
        }

        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("LogBookEntryItem", "Status");
            ElementDescriptions.Add("ErrorCode2Item", "ErrorCode2");
            ElementDescriptions.Add("ProfileItem", "ProfileName");
            ElementDescriptions.Add("MenuItem", "ID");
            ElementDescriptions.Add("ParameterRef", "ParameterTarget");
            ElementDescriptions.Add("MenuRef", "MenuTarget");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = new List<string>();
            tokens1.Add("Isochronous");
            tokens1.Add("ProcessAutomation");
            AttributeTokenDictionary.Add("ApplicationClass", tokens1);

            var tokens2 = new List<string>();
            tokens2.Add("Advanced");
            tokens2.Add("Legacy");
            AttributeTokenDictionary.Add("StartupMode", tokens2);

            var tokens3 = new List<string>();
            tokens3.Add("Absolute");
            tokens3.Add("Relative");
            AttributeTokenDictionary.Add("ForwardingMode", tokens3);

            var tokens4 = new List<string>();
            tokens4.Add("F_iPar_CRC");
            tokens4.Add("F_WD_Time_2");
            AttributeTokenDictionary.Add("F_SupportedParameters", tokens4);
        }

        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// The attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// 
        /// Partly the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs into which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            string values1 = "1 2 3 4 8 9";
            AttributeValueListDictionary.Add("ResetToFactoryModes", values1);
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00010119
        /// The values used in the attribute "...TimingProperties/@SendClock" or
        /// "...RT_Class3TimingProperties/@SendClock",
        /// should not be double-entered and must be less than or equal to 128 or
        /// must be greater than or equal to 1.
        /// 
        /// Marketing Rule:
        /// These checks are not performed for "...TimingProperties/@SendClock":
        /// When DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C" and at least one
        /// submodule which is fixed plugged with this DAP has the attribute IsochroneModeRequired = "true",
        /// the element TimingProperties and all its attributes are ignored (and not checked for valid combinations).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010119()
        {
            // Range
            const uint Max = 128;
            //            uint min = 1;
            const uint MinRT = 8;
            const uint MinIrt = 1;

            var nl =
                GsdProfileBody.Descendants()
                    .Attributes(Attributes.s_SendClock)
                    .Where(
                        attribute =>
                            attribute.Parent != null && (attribute.Parent.Name.LocalName == Elements.s_TimingProperties ||
                                                         attribute.Parent.Name.LocalName == Elements.s_RTClass3TimingProperties));
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                uint min = MinRT;
                string xpathToNode = Help.GetXPath(an);
                if (!xpathToNode.Contains("RT_Class3TimingProperties"))
                {
                    if (IsAttributeConformanceClassSet(an))
                    {
                        continue;
                    }


                }
                else
                {
                    min = MinIrt;
                }

                // Split incoming string to individual numbers and areas in a list.
                List<ValueListHelper.ValueRangeT> splitlist = ValueListHelper.NormalizeValueList(an, Store);

                var lineInfo = (IXmlLineInfo)an;
                bool value32Found = false;
                for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
                {
                    CreateReport0x00010119_1(splitlist, currentRange, min, Max, an, lineInfo);


                    if (splitlist[currentRange].From <= 32 && splitlist[currentRange].To >= 32)
                    {
                        value32Found = true;
                    }
                }

                if (value32Found || Help.IsGeneratedGSDMLFile(CheckParameter["GsdName"]))
                {
                    continue;
                }
                CreateReport0x00010119_2(an, lineInfo);

            }

            return true;
        }

        private void CreateReport0x00010119_2(XObject an, IXmlLineInfo lineInfo)
        {
            // "The 'SendClock' attribute must contain the mandatory value 32."
            string msg = Help.GetMessageString("M_0x00010119_2");
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportType_0x00010119_2,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010119_2");
        }

        private void CreateReport0x00010119_1(
             IReadOnlyList<ValueListHelper.ValueRangeT> splitlist,
             int currentRange,
             uint min,
             uint Max,
             XObject an,
             IXmlLineInfo lineInfo)
        {
            if (splitlist[currentRange].From >= min && splitlist[currentRange].To <= Max)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
            {
                return;
            }
            // "Values contained within the '(RT_Class3)TimingProperties/@SendClock' attribute must
            // be higher or equal than {0} and lower or equal than {1}."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010119_1"), min, Max);
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010119_1");
        }
        private bool IsAttributeConformanceClassSet(XObject an)
        {
            double pnioVersion = 1.0;
            if (an.Parent == null)
            {
                return false;
            }
            XElement dap = an.Parent.Parent.Parent.Parent;

            if (dap != null && dap.Name.LocalName == Elements.s_DeviceAccessPointItem)
            {
                pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
            }
            if (!(pnioVersion >= 2.31))
            {
                return false;
            }
            string conformanceClass = string.Empty;
            XAttribute conformanceClassNode =
                ((IEnumerable)dap.XPathEvaluate("./gsddef:CertificationInfo/@ConformanceClass", Nsmgr)).Cast<XAttribute>()
                .FirstOrDefault();
            if (conformanceClassNode != null)
                conformanceClass = conformanceClassNode.Value;
            if (0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture)
                && SubmoduleWithIsochroneModeRequired(dap))
            {
                return true;
            }
            return false;
        }

        public virtual ValueListHelper.NormalizeResult CombineReductionRatios(string valueList, out List<ValueListHelper.ValueRangeT> valueRanges)
        {
            valueRanges = new List<ValueListHelper.ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return ValueListHelper.NormalizeResult.OK;
            }
            // ((\d+\.\.\d+)|(\d+))(( \d+\.\.\d+)|( \d+))*
            String pattern = "^(([0-9]+\\.\\.[0-9]+)|([0-9]+))(( [0-9]+\\.\\.[0-9]+)|( [0-9]+))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return ValueListHelper.NormalizeResult.NumericOverflow;
            }

            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                ValueListHelper.ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    if (!uint.TryParse(values[0], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From)
                        || !uint.TryParse(values[1], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.To))
                    {
                        valueRanges = null;
                        return ValueListHelper.NormalizeResult.NumericOverflow;
                    }
                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        return ValueListHelper.NormalizeResult.InvalidRange;
                    }
                    ValueListHelper.ValueRangeT vr1;
                    vr1.From = vr.From;
                    vr1.To = vr.From;
                    valueRanges.Add(vr1);
                    vr.From = vr.To;
                }
                else
                {
                    // Single value
                    if (!uint.TryParse(valueOrRange, NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From))
                    {
                        valueRanges = null;
                        return ValueListHelper.NormalizeResult.NumericOverflow;
                    }
                    vr.To = vr.From;
                }
                valueRanges.Add(vr);
            }
            valueRanges.Sort(new ValueListHelper.ValueRangeComparer());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges.RemoveAt(currentRange + 1);
                }
                else
                    currentRange++;
            }
            return ValueListHelper.NormalizeResult.OK;
        }

        protected virtual bool DoesPortOnlySupportLinkSpeedLess100Mbits(XElement port)
        {
            string mauTypesGreaterEqual100Mbits = "0 15..18 21..26 29..41 44..113";
            ValueListHelper.NormalizeValueList(mauTypesGreaterEqual100Mbits, out List<ValueListHelper.ValueRangeT> listmauTypesGreaterEqual100Mbits);

            XAttribute mauTypes = port.Attribute(Attributes.s_MauTypes);
            List<uint> mauTypesList;
            if (mauTypes != null)
                mauTypesList = ValueListHelper.SeparateUnsignedValueList(mauTypes, Store);
            else
                mauTypesList = new List<uint>();

            var mauTypeItems = port.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
            foreach (var mauTypeItem in mauTypeItems)
            {
                string valueStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value);  // required
                UInt16 value = XmlConvert.ToUInt16(valueStr);
                if (!mauTypesList.Contains(value))
                    mauTypesList.Add(value);
            }

            foreach (uint mauType in mauTypesList)
            {
                if (ValueListHelper.IsValueInValueList(mauType, listmauTypesGreaterEqual100Mbits))
                    return false;
            }

            return true;
        }

        protected virtual bool DoesAllPortsOfDapOnlySupportLinkSpeedLess100Mbits(XElement dap)
        {
            // Get all ports, fixed and plugable, for the dap
            DapToPortDictionary.TryGetValue(dap, out IList<XElement> portsOfDap);

            if (portsOfDap == null)
            {
                return true;
            }

            foreach (XElement port in portsOfDap)
            {
                if (!DoesPortOnlySupportLinkSpeedLess100Mbits(port))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010030
        /// The values used in the 'ReductionRatio', 'ReductionRatioPow2' or 'ReductionRatioNonPow2' attributes
        /// on the 'TimingProperties' or 'RT_Class3TimingProperties' elements should not be entered twice
        /// and must be less than or equal to 512 or greater than or equal to 1.
        /// 
        /// Marketing Rule:
        /// These checks are not performed for "...TimingProperties/@ReductionRatio":
        /// When DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C" and PNIO_Version >= "V2.31" and at
        /// least one submodule which is fixed plugged with this DAP has the attribute IsochroneModeRequired = "true",
        /// the element TimingProperties and all its attributes are ignored (and not checked for valid combinations).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010030()
        {
            List<uint> class3ReductionRatios = new() { 1, 2, 4, 8, 16 };
            double fileNameGsdmlVersion = GetPNioVersion(FileNameGsdmlVersion);

            string xp = ".//gsddef:TimingProperties | .//gsddef:RT_Class3TimingProperties";
            string xp1 = "./@ReductionRatio | ./@ReductionRatioPow2 | ./@ReductionRatioNonPow2";

            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var timingProp in nl)
            {
                double pnioVersion = 1.0;
                if (timingProp.Parent == null)
                {
                    continue;
                }

                if (timingProp.Parent.Parent == null)
                {
                    continue;
                }
                var dap = timingProp.Parent.Parent.Parent.Parent;
                bool doesPortsOnlySupportLinkSpeedLess100Mbits = false;

                // For PNIO V1.0 Devices not the DAP but the ApplicationProcess element is supplied.
                if (dap.Name.LocalName == Elements.s_DeviceAccessPointItem)
                {
                    pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    doesPortsOnlySupportLinkSpeedLess100Mbits = DoesAllPortsOfDapOnlySupportLinkSpeedLess100Mbits(dap);

                }
                string reductionRatioValues = GetReductionRatio(doesPortsOnlySupportLinkSpeedLess100Mbits, out List<uint> mandatoryReductionRatios);

                string conformanceClass = string.Empty;
                XAttribute conformanceClassNode = ((IEnumerable)dap.XPathEvaluate("./gsddef:CertificationInfo/@ConformanceClass", Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (conformanceClassNode != null)
                    conformanceClass = conformanceClassNode.Value;

                // The call to SubmoduleWithIsochroneModeRequired is quite expensive and therefore pulled before the if.
                // Only for @ConformanceClass = "C" and pnioVersion >= 2.31 the value has a meaning, otherwise it doesn't matter.
                bool specialTreatment = false;
                if (0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture) && pnioVersion >= 2.31)
                    specialTreatment = SubmoduleWithIsochroneModeRequired(dap);

                if (specialTreatment && timingProp.Name.LocalName == Elements.s_TimingProperties)
                {
                    continue;
                }

                List<ValueListHelper.ValueRangeT> listReductionRatios = null;
                ValueListHelper.NormalizeResult res = 0;

                var nl1 = (IEnumerable)timingProp.XPathEvaluate(xp1, Nsmgr);
                string combinedReductionRatios = GetCombinedReductionRatios(nl1);



                if (!string.IsNullOrEmpty(combinedReductionRatios))
                    // Split value string to individual numbers and areas in a list.
                    res = CombineReductionRatios(combinedReductionRatios, out listReductionRatios);
                CreateReportType_0x00010030_3_4(res, specialTreatment, timingProp, pnioVersion, combinedReductionRatios, listReductionRatios, mandatoryReductionRatios, fileNameGsdmlVersion, reductionRatioValues, class3ReductionRatios);


            }

            return true;
        }

        private void CreateReportType_0x00010030_3_4(
           ValueListHelper.NormalizeResult res,
           bool specialTreatment,
           XElement timingProp,
           double pnioVersion,
           string combinedReductionRatios,
           IList<ValueListHelper.ValueRangeT> listReductionRatios,
           IReadOnlyList<uint> mandatoryReductionRatios,
           double fileNameGsdmlVersion,
           string reductionRatioValues,
           IReadOnlyList<uint> class3ReductionRatios)
        {
            // The error is raised with first check of ReductionRatio (CheckReductionRatioValues())
            if (res != ValueListHelper.NormalizeResult.OK)
                return;

            if (specialTreatment && timingProp.Name.LocalName == Elements.s_RTClass3TimingProperties)
                return;
            ReportTypes reportType_0X0001003034 = (pnioVersion >= 2.31) ? ReportTypes.GSD_RT_Error : ReportTypes.GSD_RT_Warning;

            var lineInfo = (IXmlLineInfo)timingProp;
            if (string.IsNullOrEmpty(combinedReductionRatios) || listReductionRatios == null)
            {
                return;
            }
            if (timingProp.Name.LocalName == Elements.s_TimingProperties)
            {
                CreateReport0x00010030_4(
          mandatoryReductionRatios,
          listReductionRatios,
          timingProp,
          fileNameGsdmlVersion,
          reductionRatioValues,
          reportType_0X0001003034,
          lineInfo);

            }
            else
            {
                CreateReport0x00010030_3(
         class3ReductionRatios,
         listReductionRatios,
         timingProp,
         fileNameGsdmlVersion,
         reportType_0X0001003034,
         lineInfo);

            }
        }
        private string GetCombinedReductionRatios(IEnumerable nl1)
        {
            string combinedReductionRatios = "";
            foreach (XAttribute reductionRatio in nl1)
            {
                CheckReductionRatioValues(reductionRatio);
                if (string.IsNullOrEmpty(combinedReductionRatios))
                    combinedReductionRatios = reductionRatio.Value;
                else
                    combinedReductionRatios = combinedReductionRatios + " " + reductionRatio.Value;
            }
            return combinedReductionRatios;
        }

        private static string GetReductionRatio(
            bool doesPortsOnlySupportLinkSpeedLess100Mbits,
            out List<uint> mandatoryReductionRatios)
        {
            // If all ports only support a link speed less than 100 Mbits (APL), the mandatory ReductionRatios must be reduced
            string reductionRatioValues = "";
            if (doesPortsOnlySupportLinkSpeedLess100Mbits)
            {
                mandatoryReductionRatios = new List<uint>
                                               {
                                                   8,
                                                   16,
                                                   32,
                                                   64,
                                                   128,
                                                   256,
                                                   512
                                               };
                reductionRatioValues = "8 16 32 64 128 256 512";
            }
            else
            {
                mandatoryReductionRatios = new List<uint>
                                               {
                                                   1,
                                                   2,
                                                   4,
                                                   8,
                                                   16,
                                                   32,
                                                   64,
                                                   128,
                                                   256,
                                                   512
                                               };
                reductionRatioValues = "1 2 4 8 16 32 64 128 256 512";
            }

            return reductionRatioValues;
        }

        private void CreateReport0x00010030_4(
            IReadOnlyList<uint> mandatoryReductionRatios,
            IList<ValueListHelper.ValueRangeT> listReductionRatios,
            XObject timingProp,
            double fileNameGsdmlVersion,
            string reductionRatioValues,
            ReportTypes reportType0X0001003034,
            IXmlLineInfo lineInfo)
        {
            for (int j = 0; j < mandatoryReductionRatios.Count; j++)
            {
                if (ValueListHelper.IsValueInValueList(mandatoryReductionRatios[j], listReductionRatios))
                {
                    continue;
                }
                if (Help.CheckSchemaVersion(timingProp, SupportedGsdmlVersion))
                {
                    if (fileNameGsdmlVersion >= 2.33)
                    {
                        // "At least one of the 'ReductionRatio', 'ReductionRatioPow2' attributes
                        // at the 'TimingProperties' element must contain the mandatory values 1 2 4 8 16 32 64 128 256 512."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010030_4a"), reductionRatioValues);
                        string xpath = Help.GetXPath(timingProp);
                        Store.CreateAndAnnounceReport(
                            reportType0X0001003034,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010030_4");
                    }
                    else
                    {
                        // "At least one of the 'ReductionRatio', 'ReductionRatioPow2', 'ReductionRatioNonPow2' attributes
                        // at the 'TimingProperties' element must contain the mandatory values 1 2 4 8 16 32 64 128 256 512."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010030_4"), reductionRatioValues);
                        string xpath = Help.GetXPath(timingProp);
                        Store.CreateAndAnnounceReport(
                            reportType0X0001003034,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010030_4");
                    }
                }

                break;
            }
        }
        private void CreateReport0x00010030_3(
            IReadOnlyList<uint> class3ReductionRatios,
            IList<ValueListHelper.ValueRangeT> listReductionRatios,
            XObject timingProp,
            double fileNameGsdmlVersion,
            ReportTypes reportType0X0001003034,
            IXmlLineInfo lineInfo)
        {
            for (int k = 0; k < class3ReductionRatios.Count; k++)
            {
                if (ValueListHelper.IsValueInValueList(class3ReductionRatios[k], listReductionRatios))
                {
                    continue;
                }
                if (Help.CheckSchemaVersion(timingProp, SupportedGsdmlVersion))
                {
                    if (fileNameGsdmlVersion >= 2.33)
                    {
                        // "At least one of the 'ReductionRatio', 'ReductionRatioPow2' attributes
                        // at the 'RT_Class3TimingProperties' element must contain the mandatory values 1 2 4 8 16."
                        string msg = Help.GetMessageString("M_0x00010030_3a");
                        string xpath = Help.GetXPath(timingProp);
                        Store.CreateAndAnnounceReport(
                            reportType0X0001003034,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010030_3");
                    }
                    else
                    {
                        // "At least one of the 'ReductionRatio', 'ReductionRatioPow2', 'ReductionRatioNonPow2' attributes
                        // at the 'RT_Class3TimingProperties' element must contain the mandatory values 1 2 4 8 16."
                        string msg = Help.GetMessageString("M_0x00010030_3");
                        string xpath = Help.GetXPath(timingProp);
                        Store.CreateAndAnnounceReport(
                            reportType0X0001003034,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010030_3");
                    }
                }

                break;
            }

        }


        /// <summary>
        /// FrameID Range 7 is only allowed for legacy devices. If the attribute SupportedRT_Classes is
        /// missing, the attribute StartupMode must not contain the value 'Advanced' -> error. If the attribute
        /// SupportedRT_Classes is missing and at least one attribute StartupMode exists (but without 'Advanced')
        /// -> warning.
        /// </summary>
        protected virtual bool CheckCn_0X00020002()
        {
            const string Xp = ".//gsddef:DeviceAccessPointItem[not(.//@SupportedRT_Classes) and .//@StartupMode]";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                bool foundAdvanced = false;
                bool foundError = false;

                var nl2 = (IEnumerable)en.XPathEvaluate(".//@StartupMode", Nsmgr);
                foreach (XAttribute an in nl2)
                {
                    if (!Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    string values = an.Value;
                    string[] arrValues = values.Split(';');

                    foreach (string item in arrValues)
                    {
                        if (item != "Advanced")
                        {
                            continue;
                        }
                        foundAdvanced = true;
                        break;
                    }


                    if (!foundAdvanced)
                    {
                        continue;
                    }
                    // "The attribute 'SupportedRT_Classes' is necessary if the attribute 'StartupMode' contains the token "Advanced"."
                    string msg = Help.GetMessageString("M_0x00020002_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020002_1");
                    foundError = true;
                }
                if (foundError)
                {
                    continue;
                }




                {
                    // "FrameID range 7 is only allowed for legacy devices."
                    string msg = Help.GetMessageString("M_0x00020002_2");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020002_2");
                }
            }

            return true;
        }

        /// <summary>
        /// The attribute ApplicationRelations/@StartupMode shall be present and contain the token "Legacy"
        /// if RT_CLASS_2 is supported.
        /// The attribute InterfaceSubmoduleItem/ApplicationRelations/@StartupMode shall be present
        /// and contain the token "Advanced" if one or more of the following conditions are met:
        /// (1) DeviceAccessPointItem/@PNIO_Version >= "V2.31"
        /// (2) System redundancy is supported: DeviceAccessPointItem/SystemRedundancy is present
        /// (3) CiR is supported: DeviceAccessPointItem/@CIR_Supported is present and "true"
        /// </summary>
        /// <returns></returns>
        protected virtual bool CheckCn_0X00020003()
        {
            // Check the attribute ApplicationRelations/@StartupMode
            string xp = ".//gsddef:InterfaceSubmoduleItem";
            var interfaceSubmodules = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            interfaceSubmodules = Help.TryRemoveXElementsUnderXsAny(interfaceSubmodules, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in interfaceSubmodules)
            {
                bool startupModeAdvanced = false;
                var startupModeNode = ((IEnumerable)interfaceSubmoduleItem.XPathEvaluate("./gsddef:ApplicationRelations/@StartupMode", Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                string startupMode = startupModeNode != null ? startupModeNode.Value : "Legacy"; // default value
                var startupModes = new List<string>(startupMode.Split(Constants.s_Semicolon.ToCharArray()));
                if (startupModes.Contains("Advanced"))
                    startupModeAdvanced = true;

                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }

                XElement dap = interfaceSubmoduleItem.Parent.Parent;
                var rtClasses = GetRTClasses(interfaceSubmoduleItem);
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;

                // @StartupMode "Legacy" and RT_CLASS_2
                CreateReport0x00020003_4(pnioVersion, rtClasses, startupModes, interfaceSubmoduleItem, lineInfo);


                // (1)
                if (CreateReport0x00020003_1(pnioVersion, startupModeAdvanced, interfaceSubmoduleItem, lineInfo))
                {
                    continue;
                }


                // (2)
                if (CreateReport0x00020003_2(dap, startupModeAdvanced, interfaceSubmoduleItem, lineInfo))
                {
                    continue;
                }


                // (3)
                bool cirSupported = false;
                string cirSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_CirSupported);
                if (!string.IsNullOrEmpty(cirSupportedStr))
                    cirSupported = XmlConvert.ToBoolean(cirSupportedStr);
                if (!cirSupported || startupModeAdvanced)
                {
                    continue;
                }

                CreateReport0x00020003_3(interfaceSubmoduleItem, lineInfo);

            }

            return true;
        }

        private void CreateReport0x00020003_3(XObject interfaceSubmoduleItem, IXmlLineInfo lineInfo)
        {
            // "'DeviceAccessPointItem' supports CiR but does not support 'StartupMode' "Advanced" for "RT_CLASS_1"."
            string msg = Help.GetMessageString("M_0x00020003_3");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020003_3");
        }

        private bool CreateReport0x00020003_2(
            XNode dap,
            bool startupModeAdvanced,
            XObject interfaceSubmoduleItem,
            IXmlLineInfo lineInfo)
        {
            var systemRedundancy = dap.XPathSelectElement("./gsddef:SystemRedundancy", Nsmgr);
            if (systemRedundancy == null || startupModeAdvanced)
            {
                return false;
            }
            // "'DeviceAccessPointItem' supports 'SystemRedundancy' but does not support 'StartupMode' "Advanced" for "RT_CLASS_1"."
            string msg = Help.GetMessageString("M_0x00020003_2");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020003_2");
            return true;
        }

        private bool CreateReport0x00020003_1(
           double pnioVersion,
           bool startupModeAdvanced,
           XObject interfaceSubmoduleItem,
           IXmlLineInfo lineInfo)
        {
            if (!(pnioVersion >= 2.31) || startupModeAdvanced)
            {
                return false;
            }
            // "'DeviceAccessPointItem' supports 'PNIO_Version' >= "V2.31" but does not support 'StartupMode' "Advanced" for "RT_CLASS_1"."
            string msg = Help.GetMessageString("M_0x00020003_1");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020003_1");
            return true;
        }

        private void CreateReport0x00020003_4(
          double pnioVersion,
          ICollection<string> rtClasses,
          ICollection<string> startupModes,
          XObject interfaceSubmoduleItem,
          IXmlLineInfo lineInfo)
        {
            if (!(pnioVersion >= 2.31)
                || !rtClasses.Contains("RT_CLASS_2"))
            {
                return;
            }
            if (startupModes.Contains("Legacy"))
            {
                return;
            }
            // "If "RT_CLASS_2" is supported, the attribute 'ApplicationRelations/@StartupMode' shall be either missing or contain the token "Legacy"."
            string msg = Help.GetMessageString("M_0x00020003_4");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020003_4");

        }

        /// <summary>
        /// Check the attributes of the MenuItem elements under ParameterRecordDataItems/MenuList and ARVendorBlock/Request/MenuList.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00020008()
        {
            // Get all parameters
            const string Xp = ".//gsddef:ParameterRecordDataItem | .//gsddef:ARVendorBlock/gsddef:Request";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var menuHead in nl)
            {
                var xliMenuList = (IXmlLineInfo)menuHead.Element(NamespaceGsdDef + Elements.s_MenuList);

                var menuItems = menuHead.Descendants(NamespaceGsdDef + Elements.s_MenuItem).ToList();
                if (menuItems.Count == 0)
                {
                    continue;
                }
                CreateReport0x00020008_2(menuHead, menuItems, xliMenuList);


                var listParamIds = FindParamIds(menuHead);
                var listParamRefs = FindParamReferences(menuHead);
                var listMenuItemIds = FindMenuItemIds(menuHead);
                var listMenuItemRefs = FindMenuReferences(menuHead);
                CreateReport0x00020008_3(listParamRefs, menuHead, xliMenuList);
                CreateReport0x00020008_4(listMenuItemRefs, menuHead, xliMenuList);
                CreateReport0x00020008_5(listParamIds, listParamRefs, menuHead, xliMenuList);
                CreateReport0x00020008_6(listMenuItemIds, listMenuItemRefs, menuHead, xliMenuList);






                // Check the nesting level of the menu hierarchy
                CreateReport0x00020008_7(menuItems, menuHead);


            }

            return true;
        }

        private void CreateReport0x00020008_7(IReadOnlyList<XElement> menuItems, XContainer menuHead)
        {
            bool errorRaised = false;
            var root = menuItems[0];
            var rootRefs = root.Elements(NamespaceGsdDef + Elements.s_MenuRef);
            foreach (var rootRef in rootRefs)
            {
                var menu = GetReferencedMenu(menuHead, rootRef);
                var menuRefs = menu.Elements(NamespaceGsdDef + Elements.s_MenuRef);
                foreach (var menuRef in menuRefs)
                {
                    var submenu = GetReferencedMenu(menuHead, menuRef);
                    var submenuRefs = submenu.Elements(NamespaceGsdDef + Elements.s_MenuRef);
                    foreach (var submenuRef in submenuRefs)
                    {
                        if (errorRaised)
                            break;

                        var subsubmenu = GetReferencedMenu(menuHead, submenuRef);
                        var subsubmenuRefs = subsubmenu.Elements(NamespaceGsdDef + Elements.s_MenuRef);
                        if (subsubmenuRefs.FirstOrDefault() == null)
                        {
                            continue;
                        }
                        // "The menu hierarchy has exceeded the nesting level of three (root menu -> menu -> submenu -> sub-submenu)."
                        if (!Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
                        {
                            continue;
                        }
                        string msg = Help.GetMessageString("M_0x00020008_7");
                        string xpath = Help.GetXPath(root);
                        var xli = (IXmlLineInfo)root;
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00020008_7");
                        errorRaised = true;
                        break;
                    }
                }
            }

        }
        private void CreateReport0x00020008_6(
           IList<string> listMenuItemIds,
           ICollection<string> listMenuItemRefs,
           XContainer menuHead,
           IXmlLineInfo xliMenuList)
        {
            for (int i = 1; i < listMenuItemIds.Count; i++)
            {
                if (listMenuItemRefs.Contains(listMenuItemIds[i]))
                {
                    continue;
                }
                // "The menu item "{0}" is not referenced."
                if (!Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
                {
                    continue;
                }
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020008_6"), listMenuItemIds[i]);
                string xpath = Help.GetXPath(menuHead.Element(NamespaceGsdDef + Elements.s_MenuList));
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xliMenuList.LineNumber,
                    xliMenuList.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020008_6");
            }
        }
        private void CreateReport0x00020008_5(
             IList<string> listParamIds,
             ICollection<string> listParamRefs,
             XContainer menuHead,
             IXmlLineInfo xliMenuList)
        {

            for (int i = 0; i < listParamIds.Count; i++)
            {
                if (listParamRefs.Contains(listParamIds[i]))
                {
                    continue;
                }
                // "The parameter "{0}" is not referenced."
                if (!Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
                {
                    continue;
                }
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020008_5"), listParamIds[i]);
                string xpath = Help.GetXPath(menuHead.Element(NamespaceGsdDef + Elements.s_MenuList));
                Store.CreateAndAnnounceReport(
                    ReportType_0x00020008,
                    xliMenuList.LineNumber,
                    xliMenuList.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020008_5");
            }
        }

        private void CreateReport0x00020008_4(IList<string> listMenuItemRefs, XContainer menuHead, IXmlLineInfo xliMenuList)
        {
            bool errorRaised = false;
            for (int i = 0; i < listMenuItemRefs.Count; i++)
            {
                for (int j = i + 1; !errorRaised && j < listMenuItemRefs.Count; j++)
                {
                    if (!listMenuItemRefs[i].Equals(listMenuItemRefs[j], StringComparison.Ordinal))
                    {
                        continue;
                    }
                    // "The menu item "{0}" is referenced more than once."
                    if (Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020008_4"), listMenuItemRefs[i]);
                        string xpath = Help.GetXPath(menuHead.Element(NamespaceGsdDef + Elements.s_MenuList));
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xliMenuList.LineNumber,
                            xliMenuList.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00020008_4");
                    }
                    errorRaised = true;
                    break;
                }

            }
        }

        private void CreateReport0x00020008_3(IList<string> listParamRefs, XContainer menuHead, IXmlLineInfo xliMenuList)
        {
            bool errorRaised = false;
            for (int i = 0; i < listParamRefs.Count; i++)
            {
                for (int j = i + 1; !errorRaised && j < listParamRefs.Count; j++)
                {
                    if (!listParamRefs[i].Equals(listParamRefs[j], StringComparison.Ordinal))
                    {
                        continue;
                    }
                    // "The parameter "{0}" is referenced more than once."
                    if (Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020008_3"), listParamRefs[i]);
                        string xpath = Help.GetXPath(menuHead.Element(NamespaceGsdDef + Elements.s_MenuList));
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xliMenuList.LineNumber,
                            xliMenuList.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00020008_3");
                    }
                    errorRaised = true;
                    break;
                }
            }
        }

        private void CreateReport0x00020008_2(XElement menuHead, IReadOnlyList<XElement> menuItems, IXmlLineInfo xliMenuList)
        {
            if (menuHead.Name.LocalName != Elements.s_ParameterRecordDataItem)
            {
                return;
            }
            string rootName = GetTextId(menuItems[0]);
            string headName = GetTextId(menuHead);
            if (rootName == headName)
            {
                return;
            }
            // "The 'Name/@TextId' of the very first 'MenuItem' (= "{0}") in the 'MenuList' is different to the 'Name/@TextId' of the parameter record (= "{1}")."
            if (!Help.CheckSchemaVersion(menuHead, SupportedGsdmlVersion))
            {
                return;
            }
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020008_2"), rootName, headName);
            string xpath = Help.GetXPath(menuHead.Element(NamespaceGsdDef + Elements.s_MenuList));
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xliMenuList.LineNumber,
                xliMenuList.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020008_2");

        }

        /// <summary>
        /// Some additional checks for SystemRedundancy relating to PrimaryAR_OnBothNAPsSupported and FixedInSlots/AllowedInSlots, see the messages.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00020010()
        {
            const string Xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemRedundancy";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var systemRedundancy in nl)
            {
                var dap = systemRedundancy.Parent;
                var primaryAr = systemRedundancy.Attribute(Attributes.s_PrimaryArOnBothNapsSupported);

                // Check if the device is redundant
                if (DapRedundancySupported(dap))
                {
                    CreateReport0x00020010_1(primaryAr, systemRedundancy);


                    XElement deviceAPItem = FindNumberOfFixedSlots(systemRedundancy, out List<uint> fixedSlotList, out XAttribute fixedInSlots);

                    List<uint> allowedSlotList = FindNumberOfAllowedSlots(deviceAPItem, out XAttribute allowedInSlots);

                    CreateReport0x00020010_4(allowedSlotList, systemRedundancy, deviceAPItem, allowedInSlots);
                    CreateReport0x00020010_3(fixedSlotList, systemRedundancy, fixedInSlots);
                    CreateReport0x00020010_5(fixedSlotList, allowedSlotList, systemRedundancy, allowedInSlots);
                    if (fixedSlotList.Count != 2
                        || allowedSlotList.Count != 2)
                    {
                        continue;
                    }
                    CreateReport0x00020010_6(fixedSlotList, allowedSlotList, systemRedundancy, allowedInSlots);

                }




                else // The device is not redundant
                {
                    CreateReport0x00020010_2(primaryAr, systemRedundancy);

                }


            }

            return true;
        }

        private List<uint> FindNumberOfAllowedSlots(XElement deviceApItem, out XAttribute allowedInSlots)
        {
            // Find the number of allowed slots
            List<uint> allowedSlotList = new();
            allowedInSlots = deviceApItem.Attribute(Attributes.s_AllowedInSlots);
            IList<ValueListHelper.ValueRangeT> allowedSlots = ValueListHelper.NormalizeValueList(allowedInSlots, Store);
            uint index = 0;
            for (int currentRange = 0; currentRange < allowedSlots.Count; currentRange++)
            {
                uint currentValue = allowedSlots[currentRange].From;
                while (currentValue <= allowedSlots[currentRange].To && index < 3)
                {
                    allowedSlotList.Add(currentValue++);
                    index++; // No more than 3 entries are needed
                }
            }

            return allowedSlotList;
        }

        private XElement FindNumberOfFixedSlots(XObject systemRedundancy, out List<uint> fixedSlotList, out XAttribute fixedInSlots)
        {
            // Find the number of fixed slots
            var deviceApItem = systemRedundancy.Parent;
            fixedSlotList = new List<uint>();
            fixedInSlots = deviceApItem.Attribute(Attributes.s_FixedInSlots);
            IList<ValueListHelper.ValueRangeT> fixedSlots = ValueListHelper.NormalizeValueList(fixedInSlots, Store);
            uint index = 0;
            for (int currentRange = 0; currentRange < fixedSlots.Count; currentRange++)
            {
                uint currentValue = fixedSlots[currentRange].From;
                while (currentValue <= fixedSlots[currentRange].To && index < 3)
                {
                    fixedSlotList.Add(currentValue++);
                    index++; // No more than 3 entries are needed
                }
            }

            return deviceApItem;
        }

        private void CreateReport0x00020010_2(XAttribute primaryAr, XObject systemRedundancy)
        {
            if (primaryAr == null)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }
            // "If 'SystemRedundancy/@PrimaryAR_OnBothNAPsSupported' is given 'SystemRedundancy/@DeviceType[s]' must contain "R1" or "R2"."

            string msg = Help.GetMessageString("M_0x00020010_2");
            string xpath = Help.GetXPath(systemRedundancy);
            var xli = (IXmlLineInfo)systemRedundancy;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_2");
        }

        private void CreateReport0x00020010_6(
            IReadOnlyList<uint> fixedSlotList,
            IReadOnlyList<uint> allowedSlotList,
            XObject systemRedundancy,
            XObject allowedInSlots)
        {
            if (fixedSlotList[0] == allowedSlotList[0]
                && fixedSlotList[1] == allowedSlotList[1])
            {
                return;
            }

            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }

            // "If 'SystemRedundancy/@DeviceType[s]' contains "R1" or "R2" and 'FixedInSlots' contains two slots
            // 'AllowedInSlots' must be available and contain those two slots."
            string msg = Help.GetMessageString("M_0x00020010_6");
            string xpath = Help.GetXPath(allowedInSlots);
            var xli = (IXmlLineInfo)allowedInSlots;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_6");
        }



        private void CreateReport0x00020010_5(
            IReadOnlyList<uint> fixedSlotList,
            IReadOnlyList<uint> allowedSlotList,
            XObject systemRedundancy,
            XObject allowedInSlots)
        {
            if (fixedSlotList.Count != 1 || allowedSlotList.Count != 2)
            {
                return;
            }
            if (fixedSlotList[0] == allowedSlotList[0])
            {
                return;
            }
            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }

            // "If 'SystemRedundancy/@DeviceType[s]' contains "R1" or "R2" and 'FixedInSlots' contains one slot,
            // 'AllowedInSlots' must be available and contain the slot from 'FixedInSlots' plus one higher slot number."
            string msg = Help.GetMessageString("M_0x00020010_5");
            string xpath = Help.GetXPath(allowedInSlots);
            var xli = (IXmlLineInfo)allowedInSlots;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_5");

        }
        private void CreateReport0x00020010_3(ICollection fixedSlotList, XObject systemRedundancy, XObject fixedInSlots)
        {

            if (fixedSlotList.Count == 1 || fixedSlotList.Count == 2)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }
            // "If 'SystemRedundancy/@DeviceType[s]' contains "R1" or "R2" 'FixedInSlots' must contain one or two slots."
            string msg = Help.GetMessageString("M_0x00020010_3");
            string xpath = Help.GetXPath(fixedInSlots);
            var xli = (IXmlLineInfo)fixedInSlots;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_3");

        }
        private void CreateReport0x00020010_4(
            ICollection allowedSlotList,
            XObject systemRedundancy,
            XObject deviceApItem,
            XObject allowedInSlots)
        {
            if (allowedSlotList.Count == 2)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }
            /// "If 'SystemRedundancy/@DeviceType[s]' contains "R1" or "R2" 'AllowedInSlots'
            //  must be available and contain two slots."
            string msg = Help.GetMessageString("M_0x00020010_4");
            string xpath = string.Empty;
            IXmlLineInfo xli = null;
            if (allowedSlotList.Count == 0)
            {
                xpath = Help.GetXPath(deviceApItem);
                xli = deviceApItem;
            }
            else
            {
                xpath = Help.GetXPath(allowedInSlots);
                xli = allowedInSlots;
            }

            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_4");
        }
        private void CreateReport0x00020010_1(XAttribute primaryAr, XObject systemRedundancy)
        {
            // The device is redundant
            if (primaryAr != null)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
            {
                return;
            }

            // "If 'SystemRedundancy/@DeviceType[s]' contains "R1" or "R2" 'SystemRedundancy/@PrimaryAR_OnBothNAPsSupported' must be given."
            string msg = Help.GetMessageString("M_0x00020010_1");
            string xpath = Help.GetXPath(systemRedundancy);
            var xli = (IXmlLineInfo)systemRedundancy;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020010_1");
        }

        /// <summary>
        /// Check number: CN_0x00020012
        /// If both attributes "IOConfigData/@MaxApplicationInputLength" and "IOConfigData/@MaxApplicationOutputLength" are present,
        /// then the value of "IOConfigData/@MaxApplicationDataLength" must not be greater than the sum of
        /// "IOConfigData/@MaxApplicationInputLength" and "IOConfigData/@MaxApplicationOutputLength".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020012()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData)
                                   .Where(
                                   x =>
                                       x.Attribute(Attributes.s_MaxApplicationDataLength) != null &&
                                       x.Attribute(Attributes.s_MaxApplicationInputLength) != null &&
                                       x.Attribute(Attributes.s_MaxApplicationOutputLength) != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XElement io in nl)
            {
                XAttribute an = io.Attribute(Attributes.s_MaxApplicationDataLength);
                UInt16 maxDataLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationDataLength));
                UInt16 maxInputLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationInputLength));
                UInt16 maxOutputLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationOutputLength));
                if (maxDataLength <= (maxInputLength + maxOutputLength))
                {
                    continue;
                }
                // "The maximum application data length is greater than the sum of maximum application data input and output length."
                string msg = Help.GetMessageString("M_0x00020012_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020012_1");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020013
        /// If the attribute "IOConfigData/@MaxApplicationDataLength" is present, then its value must not be
        /// be smaller than the highest value from "IOConfigData/@MaxApplicationInputLength" and "IOConfigData/@MaxApplicationOutputLength".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020013()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData)
                                   .Where(
                                   x =>
                                       x.Attribute(Attributes.s_MaxApplicationDataLength) != null &&
                                       x.Attribute(Attributes.s_MaxApplicationInputLength) != null &&
                                       x.Attribute(Attributes.s_MaxApplicationOutputLength) != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XElement io in nl)
            {
                XAttribute an = io.Attribute(Attributes.s_MaxApplicationDataLength);
                UInt16 maxDataLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationDataLength));
                UInt16 maxInputLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationInputLength));
                UInt16 maxOutputLength = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxApplicationOutputLength));
                if (maxDataLength >= maxInputLength
                    && maxDataLength >= maxOutputLength)
                {
                    continue;
                }
                // "The maximum application data length is lower than the highest value of maximum application data input or output length."
                string msg = Help.GetMessageString("M_0x00020013_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020013_1");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020014
        /// On the LogBookEntryList/LogBookEntryItem the @Status attribute shall be checked if it matches one of the valid values from the
        /// table in the GSDML specification "Vendor specific PNIOStatus". Otherwise error.
        ///
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020014()
        {
            const string Xp = ".//gsddef:LogBookEntryList/gsddef:LogBookEntryItem";

            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var logBookEntryItem in nl)
            {
                var lineInfo = (IXmlLineInfo)logBookEntryItem;

                string strStatus = Help.GetAttributeValueFromXElement(logBookEntryItem, Attributes.s_Status);
                UInt32 valueStatus = XmlConvert.ToUInt32(strStatus);

                // Check, if the attribute status has one of the allowed values according to
                // table 97 - Vendor specific PNIOStatus of the GSDML specification V2.3
                if (HasAllowedValueStatus(valueStatus))
                {
                    CreateReport0x00020014_1(logBookEntryItem, valueStatus, lineInfo);
                }
                else if ((valueStatus == 8487423) ||
                         (valueStatus == 13599229) ||
                         (valueStatus == 13599231) ||
                         (valueStatus == 14320125) ||
                         (valueStatus == 14320127) ||
                         (valueStatus == 14385663) ||
                         (valueStatus == 14451199) ||
                         (valueStatus == 14516735))
                {
                    var errorCode2List = logBookEntryItem.Element(NamespaceGsdDef + Elements.s_ErrorCode2List);
                    if (errorCode2List == null)
                    {
                        CreateReport0x00020014_2(logBookEntryItem, valueStatus, lineInfo);
                    }
                    else
                    {
                        CreateReport0x00020014_3(errorCode2List, valueStatus);
                    }

                }
                else
                {
                    CreateReport0x00020014_4(logBookEntryItem, valueStatus, lineInfo);
                }
            }

            return true;
        }

        private static bool HasAllowedValueStatus(uint valueStatus)
        {
            return (2130432 <= valueStatus && valueStatus <= 4162303) ||
                   (14581923 <= valueStatus && valueStatus <= 14581926) ||
                   (14581930 <= valueStatus && valueStatus <= 14581935) ||
                   (14581947 <= valueStatus && valueStatus <= 14581951) ||
                   (14581956 <= valueStatus && valueStatus <= 14582015) ||
                   (14647459 <= valueStatus && valueStatus <= 14647462) ||
                   (14647466 <= valueStatus && valueStatus <= 14647471) ||
                   (14647483 <= valueStatus && valueStatus <= 14647487) ||
                   (14647492 <= valueStatus && valueStatus <= 14647551);
        }

        private void CreateReport0x00020014_4(XObject logBookEntryItem, uint valueStatus, IXmlLineInfo lineInfo)
        {
            if (!Help.CheckSchemaVersion(logBookEntryItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "The value of Status = {0} is not valid according to table "Vendor specific PNIOStatus" of the GSDML Specification."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020014_4"), valueStatus);
            string xpath = Help.GetXPath(logBookEntryItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020014_4");
        }

        private void CreateReport0x00020014_3(XContainer errorCode2List, uint valueStatus)
        {
            // Only one ErrorCode2List Element is allowed by XSD.
            var errorCode2Items = errorCode2List.Elements(NamespaceGsdDef + Elements.s_ErrorCode2Item);
            foreach (var errorCode2Item in errorCode2Items)
            {
                string strErrorCode2 = Help.GetAttributeValueFromXElement(errorCode2Item, Attributes.s_ErrorCode2);
                Byte valueErrorCode2 = XmlConvert.ToByte(strErrorCode2);
                if (valueStatus != 13599229
                    && valueStatus != 14320125)
                {
                    continue;
                }

                if (valueErrorCode2 >= 0xc9)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(errorCode2Item, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "For Status = {0} ErrorCode2 must be in the range 0xC9..0xFF. ErrorCode2 = {1} is not valid here."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020014_3"), valueStatus, valueErrorCode2);
                string xpath = Help.GetXPath(errorCode2Item);
                var xli = (IXmlLineInfo)errorCode2Item;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020014_3");
            }
        }

        private void CreateReport0x00020014_2(XObject logBookEntryItem, uint valueStatus, IXmlLineInfo lineInfo)
        {
            if (!Help.CheckSchemaVersion(logBookEntryItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For Status = {0} LogBookEntryItem/ErrorCode2List must be given."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020014_2"), valueStatus);
            string xpath = Help.GetXPath(logBookEntryItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020014_2");
        }

        private void CreateReport0x00020014_1(XContainer logBookEntryItem, uint valueStatus, IXmlLineInfo lineInfo)
        {
            var errorCode2Values = logBookEntryItem.Elements(NamespaceGsdDef + Elements.s_ErrorCode2Value).ToList();
            if (errorCode2Values.Count != 0)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(logBookEntryItem, SupportedGsdmlVersion))
            {
                return;
            }

            // "For Status = {0} LogBookEntryItem/ErrorCode2Value must be given."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020014_1"), valueStatus);
            string xpath = Help.GetXPath(logBookEntryItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020014_1");
        }

        /// <summary>
        /// Check number: CN_0x00020016
        /// If F_ParameterRecordDataItem/@F_SupportedParameters is specified, then the parameter F_iPar_CRC must be
        /// must be present exactly if the token "F_iPar_CRC" is contained in this attribute.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020016()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FParameterRecordDataItem).Attributes(Attributes.s_FSupportedParameters);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                var fParamRecordDataItem = an.Parent;
                string value = an.Value;
                var supportedParameterList = new List<string>();
                if (!string.IsNullOrEmpty(value))
                    supportedParameterList = new List<string>(value.Split(Constants.s_Semicolon.ToCharArray()));

                var lineInfo = (IXmlLineInfo)an;

                if (fParamRecordDataItem != null &&
                    supportedParameterList.Contains(Elements.s_FIParCrc) &&
                    (fParamRecordDataItem.Element(NamespaceGsdDef + Elements.s_FIParCrc) == null))
                {
                    // "When "F_iPar_CRC" is defined as token in 'F_SupportedParameters', 'F_iPar_CRC' shall be present."
                    string msg = Help.GetMessageString("M_0x00020016_1");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020016_1");
                }

                if (fParamRecordDataItem != null &&
                    !(supportedParameterList.Contains(Elements.s_FIParCrc)) &&
                    (fParamRecordDataItem.Element(NamespaceGsdDef + Elements.s_FIParCrc) != null))
                {
                    // "When 'F_SupportedParameters' is given, 'F_iPar_CRC' shall only be present when it is defined as token in 'F_SupportedParameters'."
                    string msg = Help.GetMessageString("M_0x00020016_2");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020016_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020017
        /// There are situations, where one or more SCs cannot be used at all because none of the supported RRs
        /// when combined with these SCs fulfill the MinDeviceInterval limit.
        /// Therefore check for SendClock for both TimingProperties and RT_Class3TimingProperties (if it exists):
        /// Walk the SendClocks in ascending order, as long as the SC is less than MinDeviceInterval.
        /// For each of these SendClocks, take the highest ReductionRatio applicable (from attribute ReductionRatio and
        /// from attribute ReductionRatioPow2 if the SC is a power of 2,
        /// or from attribute ReductionRatioNonPow2 if the SC is not a power of 2).
        /// If the product of SendClock and ReductionRatio is >= MinDeviceInterval, then  this SC can be used.
        /// If not, the SC cannot be used --> issue a Warning.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020017()
        {
            var daPs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daPs = Help.TryRemoveXElementsUnderXsAny(daPs, Nsmgr, Gsd);
            foreach (var dap in daPs)
            {
                XAttribute minDeviceInterval = dap.Attribute(Attributes.s_MinDeviceInterval); // Mandatory
                if (minDeviceInterval == null)
                {
                    continue;
                }
                UInt16 minDeviceIntervalValue = XmlConvert.ToUInt16(minDeviceInterval.Value);

                var systemDefinedSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList);

                if (systemDefinedSubmoduleList == null)
                    return true;

                var applicationRelations = systemDefinedSubmoduleList.Element(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).Element(NamespaceGsdDef + Elements.s_ApplicationRelations);

                if (applicationRelations != null)
                {
                    // TimingProperties
                    var timingProperties = applicationRelations.Element(NamespaceGsdDef + Elements.s_TimingProperties);
                    CheckSendClockValues(timingProperties, applicationRelations, minDeviceIntervalValue);

                    // RT_Class3TimingProperties
                    var rTClass3TimingProperties = applicationRelations.Element(NamespaceGsdDef + Elements.s_RTClass3TimingProperties);
                    if (rTClass3TimingProperties != null)
                    {
                        CheckSendClockValues(rTClass3TimingProperties, applicationRelations, minDeviceIntervalValue);
                    }
                }
                else
                {
                    CheckSendClockValues(null, systemDefinedSubmoduleList, minDeviceIntervalValue);
                }
            }

            return true;
        }

        protected virtual void CheckSendClockValues(XElement timingProperties, XElement parent, UInt16 minDeviceIntervalValue)
        {
            // Split send clock to individual numbers and areas in a list.
            XAttribute sendClockNode = null;
            if (timingProperties != null)
                sendClockNode = timingProperties.Attribute(Attributes.s_SendClock);
            List<uint> sendClockValues = GetSendClockValues(sendClockNode);


            // Split reduction ratio to individual numbers and areas in a list.
            uint rrHighest = 0;
            XAttribute reductionRatioNode = null;
            XAttribute reductionRatioPow2Node = null;
            XAttribute reductionRatioNonPow2Node = null;
            if (timingProperties != null)
            {
                reductionRatioNode = timingProperties.Attribute(Attributes.s_ReductionRatio);
                reductionRatioPow2Node = timingProperties.Attribute(Attributes.s_ReductionRatioPow2);
                reductionRatioNonPow2Node = timingProperties.Attribute(Attributes.s_ReductionRatioNonPow2);
            }

            if (reductionRatioNode != null)
            {
                IList<ValueListHelper.ValueRangeT> reductionRatioSplits = ValueListHelper.NormalizeValueList(reductionRatioNode, Store);
                if (reductionRatioSplits.Count != 0)
                    rrHighest = reductionRatioSplits.Last().To;
            }
            else
            {
                if (reductionRatioPow2Node == null && reductionRatioNonPow2Node == null)
                {
                    if ((timingProperties == null) || (timingProperties.Name.LocalName == Elements.s_TimingProperties))
                    {
                        rrHighest = 512;
                    }
                    else // (timingProperties.Name.LocalName == Elements.RT_Class3TimingProperties)
                    {
                        rrHighest = 16;
                    }
                }
            }

            // Split reduction ratio pow2 to individual numbers and areas in a list.
            uint rrPow2Highest = 0;
            if (reductionRatioPow2Node != null)
            {
                IList<ValueListHelper.ValueRangeT> reductionRatioPow2Splits = ValueListHelper.NormalizeValueList(reductionRatioPow2Node, Store);
                if (reductionRatioPow2Splits.Count > 0)
                    rrPow2Highest = reductionRatioPow2Splits.Last().To;
            }

            // Split reduction ratio non pow2 to individual numbers and areas in a list.
            uint rrNonPow2Highest = 0;
            if (reductionRatioNonPow2Node != null)
            {
                IList<ValueListHelper.ValueRangeT> reductionRatioNonPow2Splits = ValueListHelper.NormalizeValueList(reductionRatioNonPow2Node, Store);
                if (reductionRatioNonPow2Splits.Count > 0)
                    rrNonPow2Highest = reductionRatioNonPow2Splits.Last().To;
            }
            CreateReport0x00020017_1(timingProperties, parent, minDeviceIntervalValue, sendClockValues, rrHighest, rrPow2Highest, rrNonPow2Highest, sendClockNode);

        }



        private List<uint> GetSendClockValues(XAttribute sendClockNode)
        {
            List<uint> sendClockValues = new List<uint>();
            if (sendClockNode != null)
            {
                IList<ValueListHelper.ValueRangeT> sendClockSplits = ValueListHelper.NormalizeValueList(sendClockNode, Store);
                for (int currentRange = 0; currentRange < sendClockSplits.Count; currentRange++)
                {
                    uint currentValue = sendClockSplits[currentRange].From;
                    while (currentValue <= sendClockSplits[currentRange].To)
                    {
                        if (currentValue <= 128)
                        {
                            sendClockValues.Add(currentValue);
                        }
                        else
                        {
                            break;
                        }

                        currentValue++;
                    }
                }
            }
            else
            {
                sendClockValues.Add(32);
            }
            return sendClockValues;
        }

        private void CreateReport0x00020017_1(
            XObject timingProperties,
            XObject parent,
            ushort minDeviceIntervalValue,
            List<uint> sendClockValues,
            uint rrHighest,
            uint rrPow2Highest,
            uint rrNonPow2Highest,
            XObject sendClockNode)
        {
            foreach (uint sendClock in sendClockValues)
            {
                if (sendClock >= minDeviceIntervalValue)
                    break;

                uint rrMax = 0;
                // Is SendClock a power of 2?
                if (sendClock == 1
                    || sendClock == 2
                    || sendClock == 4
                    || sendClock == 8
                    || sendClock == 16
                    || sendClock == 32
                    || sendClock == 64
                    || sendClock == 128
                    || sendClock == 256
                    || sendClock == 512)
                    rrMax = Math.Max(rrHighest, rrPow2Highest);
                else
                    rrMax = Math.Max(rrHighest, rrNonPow2Highest);

                if ((sendClock * rrMax) >= minDeviceIntervalValue)
                {
                    continue;
                }
                // "SendClock = "{0}" cannot be used, because the product of SendClock and highest value of 'ReductionRatio' applicable
                //  (from attribute 'ReductionRatio', or from attribute 'ReductionRatioPow2'/'ReductionRatioNonPow2') is less than MinDeviceInterval."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020017_1"), sendClock);
                string xpath = Help.GetXPath(parent);
                var lineInfo = (IXmlLineInfo)parent;
                if (timingProperties != null)
                {
                    xpath = Help.GetXPath(timingProperties);
                    lineInfo = timingProperties;
                }
                if (sendClockNode != null)
                {
                    xpath = Help.GetXPath(sendClockNode);
                    lineInfo = sendClockNode;
                }

                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020017_1");
            }
        }
        /// <summary>
        /// Check number: CN_0x00020018
        /// When the value of the attribute PNIO_Version is higher than the GSDML
        /// version of the GSD file, issue a warning.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00020018()
        {
            var pnioVersions = GsdProfileBody.Descendants().Attributes(Attributes.s_PNioVersion);
            pnioVersions = Help.TryRemoveXAttributesUnderXsAny(pnioVersions, Nsmgr, Gsd);
            foreach (XAttribute pnioVersion in pnioVersions)
            {
                var lineInfo = (IXmlLineInfo)pnioVersion;

                double pnioVersionValue = GetPNioVersion(pnioVersion.Value);
                double fileNameGsdmlVersionValue = GetPNioVersion(FileNameGsdmlVersion);

                if (pnioVersionValue > fileNameGsdmlVersionValue)
                {
                    // "The PNIO version is higher than the GSDML version of the GSD file."
                    string msg = Help.GetMessageString("M_0x00020018_1");
                    string xpath = Help.GetXPath(pnioVersion);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020018_1");
                }
            }
            return true;
        }

        #region Marketing Rules

        /// <summary>
        /// Check number: CN_0x00020020
        /// (1)  If @PNIO_Version >= "V2.31", the attribute @MinDeviceInterval shall be less than or equal 4096 (128 ms)
        ///      if the attribute CertificationInfo/@ConformanceClass is "A" or "B" and it shall be less than or equal
        ///      32 (1 ms) if the attribute CertificationInfo/@ConformanceClass is "C".
        /// (2)  The attribute @MultipleWriteSupported shall be present and "true" if the attribute @PNIO_Version is >= "V2.31".
        /// (3)  If the attribute @RemoteApplicationTimeout is present its value shall be less than or equal 300 if
        ///      the attribute @PNIO_Version is >= "V2.31".
        /// (4)  The attribute @LLDP_NoD_Supported shall be present and "true" if the attribute @PNIO_Version is >= "V2.31".
        /// (5)  The attribute @ResetToFactoryModes shall be present and and contain the value "2"
        ///      if the attribute @PNIO_Version is >= "V2.31".
        /// (6)  The element DeviceAccessPointItem/SystemRedundancy shall be present when @PNIO_Version >= "V2.31"
        ///      and CertificationInfo/@ApplicationClass contains "ProcessAutomation".
        /// (7)  If present and @PNIO_Version >= "V2.31", the attribute SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class
        ///      shall be set to "Class3" when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C" or shall be set to "Class1"
        ///      when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "A" or "B".
        /// (8)  If @PNIO_Version >= "V2.31", the attribute SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes shall be present.
        ///      If @PNIO_Version >= "V2.31", the attribute SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes shall contain
        ///      "RT_CLASS_1" except when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C" and some submodule which is fixed
        ///      plugged with this DAP has the attribute IsochroneModeRequired = "true".
        ///      If @PNIO_Version >= "V2.31", the attribute DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes
        ///      shall contain "RT_CLASS_3" when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        ///      The attribute DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes
        ///      shall not contain "RT_CLASS_2" or "RT_CLASS_3" when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "A" or "B".
        /// (9)  If the attribute DeviceAccessPointItem/@PNIO_Version is >= "V2.31":
        ///      If the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token "Isochronous" (#3 - #4):
        ///        The attribute IsochroneModeSupported shall be "true" if present and the attribute IsochroneModeInRT_Classes shall be present
        ///        and contain the token "RT_CLASS_3".
        ///      Else (#1-2):
        ///        The attribute IsochroneModeSupported shall be missing or "false", and the attribute IsochroneModeInRT_Classes shall either
        ///        be missing or contain at most the token "RT_CLASS_2".
        /// (10) The attributes DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@PTP_BoundarySupported and @DCP_BoundarySupported
        ///      shall be present and "true" if the attribute PNIO_Version is >= "V2.31"."
        /// (11) When the PNIO_Version attribute at the DAP is >= "V2.31":
        ///      The attribute DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@MulticastBoundarySupported
        ///        - shall be missing or "false" if DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "A".
        ///        - shall be present and "true" if DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "B" or "C"
        ///          and if NumberOfAdditionalMulticastProviderCR > 0 or NumberOfMulticastConsumerCR > 0.
        /// (12) The element DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/MediaRedundancy shall be
        ///      present if DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "ProcessAutomation".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020020()
        {
            const string Xp = ".//gsddef:DeviceAccessPointItem[@PNIO_Version]";
            var daPs = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            daPs = Help.TryRemoveXElementsUnderXsAny(daPs, Nsmgr, Gsd);
            foreach (var dap in daPs)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (pnioVersion >= 2.31)
                {
                    // Get the needed Elements and Attributes:

                    // ConformanceClass
                    var certificationInfo = dap.Element(NamespaceGsdDef + Elements.s_CertificationInfo);
                    string conformanceClass = string.Empty;
                    if (certificationInfo != null)
                    {
                        conformanceClass = Help.GetAttributeValueFromXElement(certificationInfo, Attributes.s_ConformanceClass);
                    }
                    else
                    {
                        // "For 'PNIO_Version' >= "V2.31" the element 'CertificationInfo' is mandatory at the DAP."
                        string msg = Help.GetMessageString("M_0x00020020_0");
                        string xpath = Help.GetXPath(dap);
                        var lineInfo = (IXmlLineInfo)dap;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_0");
                    }

                    // ApplicationClass
                    IList<string> applicationClasses = GetCombinedApplicationClasses(dap);

                    // InterfaceSubmoduleItem
                    var interfaceSubmoduleItem = dap.XPathSelectElement("./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem", Nsmgr);

                    // (1)
                    CheckMinDeviceIntervalForMarketingRules(dap, conformanceClass);
                    Check_MinDeviceIntervalWithConformanceClassForMarketingRules(dap, conformanceClass);
                    // (2)
                    CheckMultipleWriteSupportedForMarketingRules(dap);

                    // (3)
                    CheckRemoteApplicationTimeoutForMarketingRules(dap);

                    // (4)
                    CheckLldpnoDSupportedForMarketingRules(dap);

                    // (5)
                    CheckResetToFactoryModesForMarketingRules(dap);

                    // (6)
                    CheckSystemRedundancyForMarketingRules(dap, applicationClasses);

                    // For the next checks interfaceSubmoduleItem is needed.
                    // If it is not available, return.
                    if (null == interfaceSubmoduleItem)
                        return true;

                    // (7)
                    CheckSupportedRTClassForMarketingRules(interfaceSubmoduleItem, conformanceClass);

                    // (8)
                    CheckSupportedRTClassesForMarketingRules(dap, interfaceSubmoduleItem, conformanceClass);

                    // (9)
                    CheckIsochroneModeForMarketingRules(dap, interfaceSubmoduleItem, applicationClasses);

                    // (10)
                    CheckPtpaDcpBoundarysForMarketingRules(interfaceSubmoduleItem);

                    // (11)
                    CheckMulticastBoundaryForMarketingRules(interfaceSubmoduleItem, conformanceClass);

                    // (12)
                    CheckMediaRedundancyForMarketingRules(dap, interfaceSubmoduleItem, applicationClasses);
                }
            }

            return true;
        }

        protected virtual void Check_MinDeviceIntervalWithConformanceClassForMarketingRules(XElement dap, string conformanceClass)
        {
            // the attribute @MinDeviceInterval shall be less than or equal 4096(128 ms)
            // if the attribute CertificationInfo/@ConformanceClass is "A" or "B"
            // and it shall be less than or equal 32 (1 ms) if the attribute CertificationInfo/@ConformanceClass is "C"

            XAttribute minDeviceInterval = dap.Attribute(Attributes.s_MinDeviceInterval);

            if (minDeviceInterval == null)
            {
                return;
            }

            UInt16 minDeviceIntervalValue = XmlConvert.ToUInt16(minDeviceInterval.Value);

            if (conformanceClass == "A"
                || conformanceClass == "B")
            {
                if (minDeviceIntervalValue <= 4096)
                {
                    return;
                }

                // "The attribute 'MinDeviceInterval' shall be less than or equal 4096 (128 ms) if the attribute 'ConformanceClass' is 'A' or 'B'."
                string msg = Help.GetMessageString("M_0x00020020_21");
                string xpath = Help.GetXPath(minDeviceInterval);
                var xli = (IXmlLineInfo)minDeviceInterval;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020020_21");
            }
            else if (conformanceClass == "C")
            {
                if (minDeviceIntervalValue <= 32)
                {
                    return;
                }

                // "The attribute 'MinDeviceInterval' shall be less than or equal 32 (1 ms) if the attribute 'ConformanceClass' is 'C'."
                string msg = Help.GetMessageString("M_0x00020020_22");
                string xpath = Help.GetXPath(minDeviceInterval);
                var xli = (IXmlLineInfo)minDeviceInterval;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020020_22");
            }

        }

        protected virtual void CheckMinDeviceIntervalForMarketingRules(XElement dap, string conformanceClass)
        {
            XAttribute minDeviceInterval = dap.Attribute(Attributes.s_MinDeviceInterval); // Mandatory
            if (minDeviceInterval != null)
            {
                UInt16 minDeviceIntervalValue = XmlConvert.ToUInt16(minDeviceInterval.Value);

                // Check if at least one combination of SendClock and ReductionRatio / ReductionRatioPow2 / ReductionRatioNonPow2
                // exactly equals the value of MinDeviceInterval.

                // Split send clock to individual numbers and areas in a list.
                string xp =
                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations//@SendClock";
                var nl = (IEnumerable)dap.XPathEvaluate(xp, Nsmgr);
                bool minDeviceIntervalFound = false;
                foreach (XAttribute sendClockNode in nl)
                {
                    List<uint> sendClockValues = CalculateSendClockValues(sendClockNode);

                    minDeviceIntervalFound = CheckMinDeviceIntervalInReductionRatio(
                        sendClockNode,
                        sendClockValues,
                        minDeviceIntervalValue,
                        minDeviceIntervalFound);

                    if (minDeviceIntervalFound)
                        break;

                    minDeviceIntervalFound = CheckMinDeviceIntervalInReductionRatioPow2(
                        sendClockNode,
                        sendClockValues,
                        minDeviceIntervalValue,
                        false);

                    if (minDeviceIntervalFound)
                        break;

                    minDeviceIntervalFound = CheckMinDeviceIntervalInReductionRatioNonPow2(
                        sendClockNode,
                        sendClockValues,
                        minDeviceIntervalValue,
                        false);
                }

                if (minDeviceIntervalFound)
                {
                    return;
                }
            }
            // "There shall be at least one combination of 'SendClock' and
            // 'ReductionRatio' / 'ReductionRatioPow2' / 'ReductionRatioNonPow2'
            // that exactly equals the value of 'MinDeviceInterval'."
            string msg = Help.GetMessageString("M_0x00020020_16");
            string xpath = Help.GetXPath(minDeviceInterval);
            var xli = (IXmlLineInfo)minDeviceInterval;
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020020_16");
            }
        }

        private bool CheckMinDeviceIntervalInReductionRatio(
            XObject sendClockNode,
            List<uint> sendClockValues,
            ushort minDeviceIntervalValue,
            bool minDeviceIntervalFound)
        {
            // Split reduction ratio to individual numbers and areas in a list.
            if (sendClockNode.Parent == null)
            {
                return minDeviceIntervalFound;
            }

            XAttribute reductionRatioNode = sendClockNode.Parent.Attribute(Attributes.s_ReductionRatio);
            List<uint> reductionRatioValues = CalculateReductionRatioValues(reductionRatioNode);

            foreach (uint reductionRatio in reductionRatioValues)
            {
                foreach (uint sendClock in sendClockValues)
                {
                    if (sendClock * reductionRatio != minDeviceIntervalValue)
                    {
                        continue;
                    }

                    minDeviceIntervalFound = true;
                    break;
                }
            }

            return minDeviceIntervalFound;
        }

        private bool CheckMinDeviceIntervalInReductionRatioPow2(
            XObject sendClockNode,
            List<uint> sendClockValues,
            ushort minDeviceIntervalValue,
            bool minDeviceIntervalFound)
        {
            // Split reduction ratio to individual numbers and areas in a list.
            if (sendClockNode.Parent == null)
            {
                return minDeviceIntervalFound;
            }

            XAttribute reductionRatioPow2Node = sendClockNode.Parent.Attribute(Attributes.s_ReductionRatioPow2);
            List<uint> reductionRatioPow2Values = CalculateReductionRatioPow2Values(reductionRatioPow2Node);

            foreach (uint reductionRatioPow2 in reductionRatioPow2Values)
            {
                foreach (uint sendClock in sendClockValues)
                {
                    if (sendClock * reductionRatioPow2 != minDeviceIntervalValue)
                    {
                        continue;
                    }

                    minDeviceIntervalFound = true;
                    break;
                }
            }

            return minDeviceIntervalFound;
        }

        private bool CheckMinDeviceIntervalInReductionRatioNonPow2(
            XObject sendClockNode,
            List<uint> sendClockValues,
            ushort minDeviceIntervalValue,
            bool minDeviceIntervalFound)
        {
            // Split reduction ratio to individual numbers and areas in a list.
            if (sendClockNode.Parent == null)
            {
                return minDeviceIntervalFound;
            }

            XAttribute reductionRatioNonPow2Node = sendClockNode.Parent.Attribute(Attributes.s_ReductionRatioNonPow2);
            List<uint> reductionRatioNonPow2Values = new();
            if (reductionRatioNonPow2Node != null)
            {
                IList<ValueListHelper.ValueRangeT> reductionRatioNonPow2Splits =
                    ValueListHelper.NormalizeValueList(reductionRatioNonPow2Node, Store);
                for (int currentRange = 0; currentRange < reductionRatioNonPow2Splits.Count; currentRange++)
                {
                    uint currentValue = reductionRatioNonPow2Splits[currentRange].From;
                    while (currentValue <= reductionRatioNonPow2Splits[currentRange].To)
                    {
                        if (currentValue <= 512
                            && !reductionRatioNonPow2Values.Contains(currentValue))
                        {
                            reductionRatioNonPow2Values.Add(currentValue);
                        }

                        currentValue++;
                    }
                }
            }

            foreach (uint reductionRatioNonPow2 in reductionRatioNonPow2Values)
            {
                foreach (uint sendClock in sendClockValues)
                {
                    if (sendClock * reductionRatioNonPow2 != minDeviceIntervalValue)
                    {
                        continue;
                    }

                    minDeviceIntervalFound = true;
                    break;
                }
            }

            return minDeviceIntervalFound;
        }

        private List<uint> CalculateReductionRatioPow2Values(XAttribute reductionRatioPow2Node)
        {
            List<uint> reductionRatioPow2Values = new();
            if (reductionRatioPow2Node == null)
            {
                return reductionRatioPow2Values;
            }

            IList<ValueListHelper.ValueRangeT> reductionRatioPow2Splits =
                ValueListHelper.NormalizeValueList(reductionRatioPow2Node, Store);
            for (int currentRange = 0; currentRange < reductionRatioPow2Splits.Count; currentRange++)
            {
                uint currentValue = reductionRatioPow2Splits[currentRange].From;
                while (currentValue <= reductionRatioPow2Splits[currentRange].To)
                {
                    if (currentValue <= 512
                        && !reductionRatioPow2Values.Contains(currentValue))
                    {
                        reductionRatioPow2Values.Add(currentValue);
                    }

                    currentValue++;
                }
            }

            return reductionRatioPow2Values;
        }

        private List<uint> CalculateReductionRatioValues(XAttribute reductionRatioNode)
        {
            List<uint> reductionRatioValues = new();
            if (reductionRatioNode == null)
            {
                return reductionRatioValues;
            }

            IList<ValueListHelper.ValueRangeT> reductionRatioSplits =
                ValueListHelper.NormalizeValueList(reductionRatioNode, Store);
            for (int currentRange = 0; currentRange < reductionRatioSplits.Count; currentRange++)
            {
                uint currentValue = reductionRatioSplits[currentRange].From;
                while (currentValue <= reductionRatioSplits[currentRange].To)
                {
                    if (currentValue <= 512
                        && !reductionRatioValues.Contains(currentValue))
                    {
                        reductionRatioValues.Add(currentValue);
                    }

                    currentValue++;
                }
            }

            return reductionRatioValues;
        }

        private List<uint> CalculateSendClockValues(XAttribute sendClockNode)
        {
            List<uint> sendClockValues = new();
            if (sendClockNode == null)
            {
                return sendClockValues;
            }

            IList<ValueListHelper.ValueRangeT> sendClockSplits = ValueListHelper.NormalizeValueList(sendClockNode, Store);
            for (int currentRange = 0; currentRange < sendClockSplits.Count; currentRange++)
            {
                uint currentValue = sendClockSplits[currentRange].From;
                while (currentValue <= sendClockSplits[currentRange].To)
                {
                    if (currentValue <= 128)
                    {
                        sendClockValues.Add(currentValue);
                    }
                    else
                    {
                        break;
                    }

                    currentValue++;
                }
            }

            return sendClockValues;
        }

        protected virtual void CheckMultipleWriteSupportedForMarketingRules(XElement dap)
        {
            string multipleWriteSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_MultipleWriteSupported);
            bool multipleWriteSupported = false;
            if (!string.IsNullOrEmpty(multipleWriteSupportedStr))
                multipleWriteSupported = XmlConvert.ToBoolean(multipleWriteSupportedStr);
            if (!multipleWriteSupported)
            {
                // "The attribute 'MultipleWriteSupported' shall be present and "true"
                // at the DAP if the attribute 'PNIO_Version' is >= "V2.31"."
                string msg = Help.GetMessageString("M_0x00020020_3");
                string xpath = Help.GetXPath(dap);
                var lineInfo = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_3");
            }
        }

        protected virtual void CheckRemoteApplicationTimeoutForMarketingRules(XElement dap)
        {
            string remoteApplicationTimeout = Help.GetAttributeValueFromXElement(dap, Attributes.s_RemoteApplicationTimeout);
            if (!string.IsNullOrEmpty(remoteApplicationTimeout))
            {
                if (XmlConvert.ToUInt32(remoteApplicationTimeout) > 300)
                {
                    // "If the attribute 'RemoteApplicationTimeout' is present its value shall
                    // be <= 300 at the DAP if the attribute 'PNIO_Version' is >= "V2.31"."
                    string msg = Help.GetMessageString("M_0x00020020_4");
                    string xpath = Help.GetXPath(dap);
                    var xli = (IXmlLineInfo)dap;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_4");
                }
            }
        }

        protected virtual void CheckLldpnoDSupportedForMarketingRules(XElement dap)
        {
            string lLdpnoDSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_LldpnoDSupported);
            bool lLdpnoDSupported = false;
            if (!string.IsNullOrEmpty(lLdpnoDSupportedStr))
                lLdpnoDSupported = XmlConvert.ToBoolean(lLdpnoDSupportedStr);
            if (!lLdpnoDSupported)
            {
                // "The attribute 'LLDP_NoD_Supported' shall be present and "true"
                // at the DAP if the attribute 'PNIO_Version' is >= "V2.31"."
                string msg = Help.GetMessageString("M_0x00020020_5");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_5");
            }
        }

        protected virtual void CheckResetToFactoryModesForMarketingRules(XElement dap)
        {
            XAttribute resetToFactoryModes = dap.Attribute(Attributes.s_ResetToFactoryModes);
            bool twoFound = false;
            List<ValueListHelper.ValueRangeT> factoryModeList = ValueListHelper.NormalizeValueList(resetToFactoryModes, Store);

            for (int currentRange = 0; currentRange < factoryModeList.Count; currentRange++)
            {
                if (factoryModeList[currentRange].From <= 2 && factoryModeList[currentRange].To >= 2)
                {
                    twoFound = true;
                    break;
                }
            }

            if (!twoFound)
            {
                // "The attribute 'ResetToFactoryModes' shall be present and and contain the value 2
                // at the DAP if the attribute 'PNIO_Version' is >= "V2.31"."
                string msg = Help.GetMessageString("M_0x00020020_6");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_6");
            }
        }

        protected virtual void CheckSystemRedundancyForMarketingRules(XElement dap, IList<string> applicationClasses)
        {
            if (applicationClasses.Contains("ProcessAutomation"))
            {
                var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);
                if (systemRedundancy == null)
                {
                    // "The element 'SystemRedundancy' shall be present at the DAP when 'PNIO_Version' >= "V2.31"
                    // and 'CertificationInfo/@ApplicationClass' contains "ProcessAutomation"."
                    string msg = Help.GetMessageString("M_0x00020020_7");
                    string xpath = Help.GetXPath(dap);
                    var xli = (IXmlLineInfo)dap;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_7");
                }
            }
        }

        protected virtual void CheckSupportedRTClassForMarketingRules(XElement interfaceSubmoduleItem, string conformanceClass)
        {
            string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
            string supportedRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
            if (string.IsNullOrEmpty(supportedRTClasses)) // If the attribute "SupportedRT_Classes" is present,
                                                          // the attribute "SupportedRT_Class" shall be ignored
            {
                if (!string.IsNullOrEmpty(conformanceClass) && !string.IsNullOrEmpty(supportedRTClass))
                {
                    var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;
                    if (0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
                    {
                        if (0 != string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
                        {
                            // "If present at the DAP, the attribute 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class'
                            // shall be set to "Class3" when 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31" and
                            // 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C"."
                            string msg = Help.GetMessageString("M_0x00020020_8");
                            string xpath = Help.GetXPath(interfaceSubmoduleItem);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_8");
                        }
                    }
                    else
                    {
                        if (0 != string.Compare(supportedRTClass, "Class1", StringComparison.InvariantCulture))
                        {
                            // "If present at the DAP, the attribute 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class'
                            // shall be set to "Class1" when 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31" and
                            // 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" or "B"."
                            string msg = Help.GetMessageString("M_0x00020020_9");
                            string xpath = Help.GetXPath(interfaceSubmoduleItem);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_9");
                        }
                    }
                }
            }
        }

        protected virtual void CheckSupportedRTClassesForMarketingRules(XElement dap, XElement interfaceSubmoduleItem, string conformanceClass)
        {
            var lineInfo = (IXmlLineInfo)dap;
            string supportedRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
            var rtClasses = new List<string>(supportedRTClasses.Split(Constants.s_Semicolon.ToCharArray()));
            if (string.IsNullOrEmpty(supportedRTClasses))
            {
                // "If 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31", the attribute
                // 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes'
                // shall be present."
                string msg = Help.GetMessageString("M_0x00020020_a");
                string xpath = Help.GetXPath(dap);
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_a");
            }

            if (!rtClasses.Contains("RT_CLASS_1"))
            {
                if ((0 != string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture)
                    || !SubmoduleWithIsochroneModeRequired(dap)) && !IsConformanceClassDOnly(dap))
                {
                    // "If 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31", the attribute
                    // 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes'
                    // shall contain "RT_CLASS_1" except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C"
                    // and some submodule which is fixed plugged with this DAP has the attribute 'IsochroneModeRequired' = "true"."
                    string msg = Help.GetMessageString("M_0x00020020_b");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_b");
                }
            }

            if (0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                if (!rtClasses.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes'
                    // shall contain "RT_CLASS_3" when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C"."
                    string msg = Help.GetMessageString("M_0x00020020_c");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_c");
                }
            }
            else if (0 == string.Compare(conformanceClass, Constants.s_CharacterA, StringComparison.InvariantCulture)
                     || 0 == string.Compare(conformanceClass, Constants.s_CharacterB, StringComparison.InvariantCulture))
            {
                if (rtClasses.Contains("RT_CLASS_2") || rtClasses.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes'
                    // shall not contain "RT_CLASS_2" or "RT_CLASS_3" when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" or "B"."
                    string msg = Help.GetMessageString("M_0x00020020_d");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_d");
                }
            }
        }

        protected virtual void CheckIsochroneModeForMarketingRules(XElement dap, XElement interfaceSubmoduleItem, IList<string> applicationClasses)
        {
            var lineInfo = (IXmlLineInfo)dap;

            string isochroneModeInRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);
            var isoRTClasses = new List<string>(isochroneModeInRTClasses.Split(Constants.s_Semicolon.ToCharArray()));
            string isochroneModeSupportedStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
            bool isochroneModeSupported = false;
            if (!string.IsNullOrEmpty(isochroneModeSupportedStr))
                isochroneModeSupported = XmlConvert.ToBoolean(isochroneModeSupportedStr);
            if (applicationClasses.Contains("Isochronous"))
            {
                if (!string.IsNullOrEmpty(isochroneModeSupportedStr) && !isochroneModeSupported || !isoRTClasses.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains the
                    // token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported'
                    // shall be "true" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'
                    // shall contain "RT_CLASS_3"."
                    string msg = Help.GetMessageString("M_0x00020020_e");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_e");
                }
            }
            else
            {
                if (isochroneModeSupported
                    || isoRTClasses.Contains("RT_CLASS_1")
                    || isoRTClasses.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' does not contain the
                    // token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported'
                    // shall be "false" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'
                    // shall either be missing or contain at most "RT_CLASS_2"."
                    string msg = Help.GetMessageString("M_0x00020020_f");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_f");
                }
            }
        }

        protected virtual void CheckPtpaDcpBoundarysForMarketingRules(XElement interfaceSubmoduleItem)
        {
            var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;

            string pTpBoundarySupportedStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_PtpBoundarySupported);
            bool pTpBoundarySupported = false;
            if (!string.IsNullOrEmpty(pTpBoundarySupportedStr))
                pTpBoundarySupported = XmlConvert.ToBoolean(pTpBoundarySupportedStr);
            if (!pTpBoundarySupported)
            {
                // "The attribute 'InterfaceSubmoduleItem/@PTP_BoundarySupported'
                // shall be present and "true" if the attribute 'PNIO_Version' is >= "V2.31"."
                string msg = Help.GetMessageString("M_0x00020020_10");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_10");
            }

            string dCpBoundarySupportedStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_DcpBoundarySupported);
            bool dCpBoundarySupported = false;
            if (!string.IsNullOrEmpty(dCpBoundarySupportedStr))
                dCpBoundarySupported = XmlConvert.ToBoolean(dCpBoundarySupportedStr);
            if (!dCpBoundarySupported)
            {
                // "The attribute 'InterfaceSubmoduleItem/@DCP_BoundarySupported'
                // shall be present and "true" if the attribute 'PNIO_Version' is >= "V2.31"."
                string msg = Help.GetMessageString("M_0x00020020_11");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_11");
            }
        }

        protected virtual void CheckMulticastBoundaryForMarketingRules(XElement interfaceSubmoduleItem, string conformanceClass)
        {
            var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;

            string multicastBoundarySupportedStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_MulticastBoundarySupported);
            bool multicastBoundarySupported = false;
            if (!string.IsNullOrEmpty(multicastBoundarySupportedStr))
                multicastBoundarySupported = XmlConvert.ToBoolean(multicastBoundarySupportedStr);
            if (0 == string.Compare(conformanceClass, Constants.s_CharacterA, StringComparison.InvariantCulture))
            {
                if (multicastBoundarySupported)
                {
                    // "The attribute 'InterfaceSubmoduleItem/@MulticastBoundarySupported'
                    // shall be missing or "false" if 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A"."
                    string msg = Help.GetMessageString("M_0x00020020_12");
                    string xpath = Help.GetXPath(interfaceSubmoduleItem);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_12");
                }
            }
            else if (0 == string.Compare(conformanceClass, Constants.s_CharacterB, StringComparison.InvariantCulture)
                     || 0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                XAttribute numberOfAdditionalMulticastProviderCr = interfaceSubmoduleItem.Elements(NamespaceGsdDef + Elements.s_ApplicationRelations).Attributes(Attributes.s_NumberOfAdditionalMulticastProviderCr).FirstOrDefault();
                UInt16 noProviderCr = 0;
                if (numberOfAdditionalMulticastProviderCr != null)
                    noProviderCr = XmlConvert.ToUInt16(numberOfAdditionalMulticastProviderCr.Value);

                XAttribute numberOfMulticastConsumerCr = interfaceSubmoduleItem.Elements(NamespaceGsdDef + Elements.s_ApplicationRelations).Attributes(Attributes.s_NumberOfMulticastConsumerCr).FirstOrDefault();
                UInt16 noConsumerCr = 0;
                if (numberOfMulticastConsumerCr != null)
                    noConsumerCr = XmlConvert.ToUInt16(numberOfMulticastConsumerCr.Value);
                if (noProviderCr > 0
                    || noConsumerCr > 0)
                {
                    if (!multicastBoundarySupported)
                    {
                        // "The attribute 'InterfaceSubmoduleItem/@MulticastBoundarySupported'
                        // shall be present and "true" if 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "B" or "C"
                        // and if 'NumberOfAdditionalMulticastProviderCR' > 0 or 'NumberOfMulticastConsumerCR' > 0."
                        string msg = Help.GetMessageString("M_0x00020020_13");
                        string xpath = Help.GetXPath(interfaceSubmoduleItem);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_13");
                    }
                }
            }
        }

        protected virtual void CheckMediaRedundancyForMarketingRules(XElement dap, XElement interfaceSubmoduleItem, IList<string> applicationClasses)
        {
            XElement mediaRedundancy = interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_MediaRedundancy);
            int countPorts = 0;
            if (DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
                countPorts = portSubmoduleItems.Count;
            if (mediaRedundancy == null && applicationClasses.Contains("ProcessAutomation") && countPorts > 1)
            {
                // "The element 'InterfaceSubmoduleItem/MediaRedundancy' shall be present if there are
                //  at least two port submodules assigned to the interface submodule and 'DeviceAccessPointItem/@PNIO_Version >= "V2.31"
                //  and 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "ProcessAutomation"."
                string msg = Help.GetMessageString("M_0x00020020_15");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                IXmlLineInfo xli = interfaceSubmoduleItem;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020020_15");
            }
        }

        /// <summary>
        /// Check number: CN_0x00020021
        /// The attribute @NetloadClass is mandatory at DeviceAccessPointItem/CertificationInfo
        /// for DeviceAccessPointItem/@PNIO_Version >= "V2.31".
        /// The tokens "Isochronous" and "HighPerformance" are only allowed for @ApplicationClass
        /// when 'PNIO_Version' >= "V2.31" and @ConformanceClass is "C".
        /// The attribute @ApplicationClass shall be empty at DeviceAccessPointItem/CertificationInfo
        /// for DeviceAccessPointItem/@PNIO_Version less than "V2.3".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020021()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:CertificationInfo";
            var cis = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            cis = Help.TryRemoveXElementsUnderXsAny(cis, Nsmgr, Gsd);
            foreach (var ci in cis)
            {
                var lineInfo = (IXmlLineInfo)ci;

                string versionStr = Help.GetAttributeValueFromXElement(ci.Parent, Attributes.s_PNioVersion);
                double version = GetPNioVersion(versionStr);
                CreateReport0x00020021_1(version, versionStr, ci, lineInfo);
                CreateReport0x00020021_2(version, ci, lineInfo);




                string tmpApplicationClasses = Help.GetAttributeValueFromXElement(ci, Attributes.s_ApplicationClass);
                IList<string> applicationClasses = new List<string>(tmpApplicationClasses.Split(Constants.s_Semicolon.ToCharArray()));
                string conformanceClass = Help.GetAttributeValueFromXElement(ci, Attributes.s_ConformanceClass);
                CreateReport0x00020021_3(version, applicationClasses, conformanceClass, ci, lineInfo);
                CreateReport0x00020021_4(version, applicationClasses, ci, lineInfo);


            }

            return true;
        }
        private void CreateReport0x00020021_4(double version, ICollection<string> applicationClasses, XObject ci, IXmlLineInfo lineInfo)
        {
            if (!(version < 2.3))
            {
                return;
            }
            if (!applicationClasses.Contains("ProcessAutomation")
             && !applicationClasses.Contains("HighPerformance"))
            {
                return;
            }
            // "The 'ApplicationClass' values "ProcessAutomation" and "HighPerformance" require 'PNIO_Version' >= "V2.3"."
            string msg = Help.GetMessageString("M_0x00020021_4");
            string xpath = Help.GetXPath(ci);
            Store.CreateAndAnnounceReport(
                ReportType_0x00020021_4,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020021_4");
        }
        private void CreateReport0x00020021_3(
           double version,
           ICollection<string> applicationClasses,
           string conformanceClass,
           XObject ci,
           IXmlLineInfo lineInfo)
        {
            if (!(version >= 2.31))
            {
                return;
            }
            if (!applicationClasses.Contains("Isochronous")
        && !applicationClasses.Contains("HighPerformance"))
            {
                return;
            }
            if (0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }
            // "When 'PNIO_Version' >= "V2.31", for 'ApplicationClass' the tokens "Isochronous" and "HighPerformance"
            // are only allowed if 'ConformanceClass' is "C"."
            string msg = Help.GetMessageString("M_0x00020021_3");
            string xpath = Help.GetXPath(ci);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020021_3");
        }
        private void CreateReport0x00020021_2(double version, XElement ci, IXmlLineInfo lineInfo)
        {
            if (!(version >= 2.31))
            {
                return;
            }
            string netloadClass = (string)ci.Attribute(Attributes.s_NetloadClass);
            if (!string.IsNullOrEmpty(netloadClass))
            {
                return;
            }
            // "The attribute 'NetloadClass' is mandatory at 'DeviceAccessPointItem/CertificationInfo'
            // for 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31"."
            string msg = Help.GetMessageString("M_0x00020021_2");
            string xpath = Help.GetXPath(ci);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020021_2");
        }

        private void CreateReport0x00020021_1(double version, string versionStr, XObject ci, IXmlLineInfo lineInfo)
        {
            if (!(version <= 2.31))
            {
                return;
            }
            double supportedVersion = GetPNioVersion(SupportedGsdmlVersion);
            if (!(version <= supportedVersion)
        || m_AllExistingGsdmlVersions.Contains(versionStr))
            {
                return;
            }
            // "The 'PNIO_Version' attribute references a non-existing GSDML version."
            string msg = Help.GetMessageString("M_0x00020021_1");
            string xpath = Help.GetXPath(ci);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020021_1");

        }

        /// <summary>
        /// Check number: CN_0x00020022
        /// The DeviceAccessPointItem/SystemDefinedSubmoduleList element shall only
        /// be present if the attribute DeviceAccessPointItem/@PNIO_Version >= "V2.0".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020022()
        {
            const string Xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList";
            var sdsList = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            sdsList = Help.TryRemoveXElementsUnderXsAny(sdsList, Nsmgr, Gsd);
            foreach (var sds in sdsList)
            {
                XElement dap = sds.Parent;
                double version = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (!(version < 2.0))
                {
                    continue;
                }
                // "The 'SystemDefinedSubmoduleList' element shall only be present at the DAP
                // if the attribute 'PNIO_Version' >= "V2.0"."
                string msg = Help.GetMessageString("M_0x00020022_1");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020022_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020023
        /// The DeviceAccessPointItem/ApplicationRelations element shall only
        /// be present if the attribute DeviceAccessPointItem/@PNIO_Version = "V1.0".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020023()
        {
            const string Xp = ".//gsddef:DeviceAccessPointItem/gsddef:ApplicationRelations";
            var aRs = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            aRs = Help.TryRemoveXElementsUnderXsAny(aRs, Nsmgr, Gsd);
            foreach (var ar in aRs)
            {
                var dap = ar.Parent;
                double version = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (version == 1.0)
                {
                    continue;
                }
                // "The 'ApplicationRelations' element shall only be present at the DAP
                // if the attribute 'PNIO_Version' = "V1.0"."
                string msg = Help.GetMessageString("M_0x00020023_1");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020023_1");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020024
        /// The attribute RT_Class3Properties/@ForwardingMode is mandatory if DeviceAccessPointItem/@PNIO_Version >= "V2.31".
        /// The attribute DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/RT_Class3Properties/@StartupMode
        /// shall be present and contain the token "Advanced" if one or more of the following conditions are met:
        /// (1) DeviceAccessPointItem/@PNIO_Version >= "V2.31" and "RT_CLASS_3" is supported
        /// (2) Fast forwarding is supported: InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW is present
        /// (3) Small send clock factors are supported: InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@SendClock
        ///     is present and contains at least one value less than 8
        /// (4) Fragmentation is supported: InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType is present
        /// (5) Dynamic frame packing is supported: InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames is present and > 0
        /// (6) MRPD is supported: InterfaceSubmoduleItem/MediaRedundancy/@MRPD_Supported is present and "true"
        /// (7) Short preamble is supported: PortSubmoduleItem/@ShortPreamble100MBitSupported is present and "true" on at least
        ///     one port submodule which is configurable with this DAP
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020024()
        {
            var itfs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            itfs = Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in itfs)
            {
                CheckCN_0x00020024_CheckInterfaceSubmoduleItem(interfaceSubmoduleItem);
            }

            return true;
        }

        private void CheckCN_0x00020024_CheckInterfaceSubmoduleItem(XElement interfaceSubmoduleItem)
        {
            var lineInfoItf = (IXmlLineInfo)interfaceSubmoduleItem;

            var rTClass3Properties = interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_RTClass3Properties);
            var lineInfoRtC3 = (IXmlLineInfo)rTClass3Properties;
            string startupMode = string.Empty;
            if (rTClass3Properties != null)
                startupMode = Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_StartupMode);
            IList<string> startupModes = new List<string>(startupMode.Split(Constants.s_Semicolon.ToCharArray()));
            if (interfaceSubmoduleItem.Parent == null)
            {
                return;
            }

            var dap = interfaceSubmoduleItem.Parent.Parent;
            double version = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
            var rtClasses = GetRTClasses(interfaceSubmoduleItem);

            // Check the attribute ForwardingMode
            if (CreateReport0x00020024_1(rTClass3Properties, version, lineInfoRtC3))
            {
                return;
            }

            // Check the attribute @StartupMode
            if (startupModes.Contains("Advanced"))
            {
                return;
            }

            // (1)
            if (CreateReportM_0x00020024_2(version, rtClasses, rTClass3Properties, lineInfoRtC3, interfaceSubmoduleItem, lineInfoItf))
            {
                return;
            }

            // (2)
            if (CreateReport0x00020024_3(rTClass3Properties, lineInfoRtC3))
            {
                return;
            }

            // (3)
            bool smallFactorFound = IsSmallFactorFound(interfaceSubmoduleItem);

            if (CreateReportM_0x00020024_4(smallFactorFound, rTClass3Properties, lineInfoRtC3, interfaceSubmoduleItem, lineInfoItf))
            {
                return;
            }

            // (4)
            if (CreateReport0x00020024_5(rTClass3Properties, lineInfoRtC3))
            {
                return;
            }

            // (5)
            if (CreateReport0x00020024_6(rTClass3Properties, lineInfoRtC3))
            {
                return;
            }

            // (6)
            XAttribute mrpdSupportedNode = ((IEnumerable)interfaceSubmoduleItem.XPathEvaluate("./gsddef:MediaRedundancy/@MRPD_Supported", Nsmgr)).Cast<XAttribute>().FirstOrDefault();

            bool mrpdSupported = GetMrpdSupported(mrpdSupportedNode);

            if (CreateReport0x00020024_7(mrpdSupported, rTClass3Properties, lineInfoRtC3, interfaceSubmoduleItem, lineInfoItf))
            {
                return;
            }

            // (7)
            IsShortPreambleSupported(dap, rTClass3Properties, lineInfoRtC3, interfaceSubmoduleItem, lineInfoItf);
        }

        private static bool GetMrpdSupported(XAttribute mrpdSupportedNode)
        {
            bool mrpdSupported = false;
            if (mrpdSupportedNode != null)
                mrpdSupported = XmlConvert.ToBoolean(mrpdSupportedNode.Value);
            return mrpdSupported;
        }

        private void IsShortPreambleSupported(
           XElement dap,
           XObject rTClass3Properties,
           IXmlLineInfo lineInfoRtC3,
           XObject interfaceSubmoduleItem,
           IXmlLineInfo lineInfoItf)
        {
            bool portWithShortPreambleFound = false;
            if (DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
            {
                foreach (var portSubmoduleItem in portSubmoduleItems)
                {
                    string shortPreamble100MbitSupportedStr = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_ShortPreamble100MbitSupported);
                    if (string.IsNullOrEmpty(shortPreamble100MbitSupportedStr))
                    {
                        continue;
                    }
                    bool shortPreamble100MbitSupported = XmlConvert.ToBoolean(shortPreamble100MbitSupportedStr);
                    if (!shortPreamble100MbitSupported)
                    {
                        continue;
                    }
                    portWithShortPreambleFound = true;
                    break;
                }
            }

            CreateReport0x00020024_8(
                portWithShortPreambleFound,
                rTClass3Properties,
                lineInfoRtC3,
                interfaceSubmoduleItem,
                lineInfoItf);
        }

        private bool IsSmallFactorFound(XNode interfaceSubmoduleItem)
        {
            XAttribute sendClockNode = ((IEnumerable)interfaceSubmoduleItem.XPathEvaluate(
                                               "./gsddef:ApplicationRelations/gsddef:RT_Class3TimingProperties/@SendClock",
                                               Nsmgr)).Cast<XAttribute>().FirstOrDefault();
            bool smallFactorFound = false;
            if (sendClockNode == null)
            {
                return false;
            }

            List<ValueListHelper.ValueRangeT> sendClocks = ValueListHelper.NormalizeValueList(sendClockNode, Store);

            for (int currentRange = 0; currentRange < sendClocks.Count; currentRange++)
            {
                if (sendClocks[currentRange].From >= 8)
                {
                    continue;
                }

                smallFactorFound = true;
                break;
            }

            return smallFactorFound;
        }

        private void CreateReport0x00020024_8(
            bool portWithShortPreambleFound,
            XObject rTClass3Properties,
            IXmlLineInfo lineInfoRtC3,
            XObject interfaceSubmoduleItem,
            IXmlLineInfo lineInfoItf)
        {
            if (!portWithShortPreambleFound)
            {
                return;
            }

            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // Short preamble is supported: 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is present and "true"
            // on at least one port submodule which is configurable with this DAP."
            string msg = Help.GetMessageString("M_0x00020024_8");
            if (rTClass3Properties != null)
            {
                string xpath = Help.GetXPath(rTClass3Properties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoRtC3.LineNumber,
                    lineInfoRtC3.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_8");
            }
            else
            {
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoItf.LineNumber,
                    lineInfoItf.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_8");
            }
        }

        private bool CreateReport0x00020024_7(
           bool mrpdSupported,
           XObject rTClass3Properties,
           IXmlLineInfo lineInfoRtC3,
           XObject interfaceSubmoduleItem,
           IXmlLineInfo lineInfoItf)
        {
            if (!mrpdSupported)
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // MRPD is supported: 'InterfaceSubmoduleItem/MediaRedundancy/@MRPD_Supported' is present and "true"."
            string msg = Help.GetMessageString("M_0x00020024_7");
            if (rTClass3Properties != null)
            {
                string xpath = Help.GetXPath(rTClass3Properties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_MinorError,
                    lineInfoRtC3.LineNumber,
                    lineInfoRtC3.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_7");
            }
            else
            {
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_MinorError,
                    lineInfoItf.LineNumber,
                    lineInfoItf.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_7");

            }

            return true;
        }
        private bool CreateReport0x00020024_6(XElement rTClass3Properties, IXmlLineInfo lineInfoRtC3)
        {
            if (rTClass3Properties == null)
            {
                return false;
            }
            string maxDFPFramesStr = Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_MaxDfpFrames);
            UInt16 maxDFPFrames = 0;
            if (!string.IsNullOrEmpty(maxDFPFramesStr))
                maxDFPFrames = XmlConvert.ToUInt16(maxDFPFramesStr);
            if (maxDFPFrames <= 0)
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // dynamic frame packing is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is present and > 0."
            string msg = Help.GetMessageString("M_0x00020024_6");
            string xpath = Help.GetXPath(rTClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfoRtC3.LineNumber,
                lineInfoRtC3.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020024_6");
            return true;

        }
        private bool CreateReport0x00020024_5(XElement rTClass3Properties, IXmlLineInfo lineInfoRtC3)
        {
            if (rTClass3Properties == null)
            {
                return false;
            }
            string fragmentationType = (string)rTClass3Properties.Attribute(Attributes.s_FragmentationType);
            if (string.IsNullOrEmpty(fragmentationType))
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // fragmentation is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType' is present."
            string msg = Help.GetMessageString("M_0x00020024_5");
            string xpath = Help.GetXPath(rTClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfoRtC3.LineNumber,
                lineInfoRtC3.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020024_5");
            return true;

        }
        private bool CreateReportM_0x00020024_4(
          bool smallFactorFound,
          XObject rTClass3Properties,
          IXmlLineInfo lineInfoRtC3,
          XObject interfaceSubmoduleItem,
          IXmlLineInfo lineInfoItf)
        {
            if (!smallFactorFound)
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // small send clock factors are supported:
            // 'InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@SendClock' is present and
            // contains at least one value less than 8."
            string msg = Help.GetMessageString("M_0x00020024_4");
            if (rTClass3Properties != null)
            {
                string xpath = Help.GetXPath(rTClass3Properties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoRtC3.LineNumber,
                    lineInfoRtC3.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_4");
            }
            else
            {
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoItf.LineNumber,
                    lineInfoItf.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_4");
            }

            return true;

        }

        private bool CreateReport0x00020024_3(XElement rTClass3Properties, IXmlLineInfo lineInfoRtC3)
        {
            if (rTClass3Properties == null)
            {
                return false;
            }
            string maxBridgeDelayFfw = (string)rTClass3Properties.Attribute(Attributes.s_MaxBridgeDelayFfw);
            if (string.IsNullOrEmpty(maxBridgeDelayFfw))
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // fast forwarding is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is present."
            string msg = Help.GetMessageString("M_0x00020024_3");
            string xpath = Help.GetXPath(rTClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfoRtC3.LineNumber,
                lineInfoRtC3.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020024_3");
            return true;
        }
        private bool CreateReportM_0x00020024_2(
              double version,
              ICollection<string> RT_Classes,
              XObject rTClass3Properties,
              IXmlLineInfo lineInfoRtC3,
              XObject interfaceSubmoduleItem,
              IXmlLineInfo lineInfoItf)
        {
            if (!(version >= 2.31)
       || !RT_Classes.Contains("RT_CLASS_3"))
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if
            // 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31" and "RT_CLASS_3" is supported."
            string msg = Help.GetMessageString("M_0x00020024_2");
            if (rTClass3Properties != null)
            {
                string xpath = Help.GetXPath(rTClass3Properties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoRtC3.LineNumber,
                    lineInfoRtC3.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_2");
            }
            else
            {
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoItf.LineNumber,
                    lineInfoItf.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020024_2");
            }

            return true;
        }

        private bool CreateReport0x00020024_1(XElement rTClass3Properties, double version, IXmlLineInfo lineInfoRtC3)
        {
            if (rTClass3Properties == null)
            {
                return false;
            }
            string forwardingMode = Help.GetAttributeValueFromXElement(rTClass3Properties, Attributes.s_ForwardingMode);
            if (!(version >= 2.31)
         || !string.IsNullOrEmpty(forwardingMode))
            {
                return false;
            }
            // "The attribute 'RT_Class3Properties/@ForwardingMode' is mandatory if 'DeviceAccessPointItem/@PNIO_Version' >= "V2.31"."
            string msg = Help.GetMessageString("M_0x00020024_1");
            string xpath = Help.GetXPath(rTClass3Properties);

            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfoRtC3.LineNumber,
                lineInfoRtC3.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020024_1");
            return true;
        }

        /// <summary>
        /// IsPortWireless
        /// Checks for a 'PortSubmoduleItem' if it has 'MAUTypes' = 0 (wireless).
        /// 
        /// </summary>
        /// <returns>True, if the port is wireless.</returns>
        protected virtual bool IsPortWireless(XElement portSubmoduleItem)
        {
            var mauTypes = portSubmoduleItem.Attribute(Attributes.s_MauTypes);

            if (null == mauTypes)
            {
                return false;
            }

            List<ValueListHelper.ValueRangeT> mauTypesList = ValueListHelper.NormalizeValueList(mauTypes, Store);

            if (mauTypesList.Count != 1
                || mauTypesList[0].From != mauTypesList[0].To
                || mauTypesList[0].From != 0)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// AllPortsWireless
        /// Checks for a 'DeviceAccessPointItem' if all port submodules which
        /// can be configured with this DAP have 'MAU Types' = 0 (wireless).
        /// 
        /// </summary>
        /// <returns>True, if all ports are wireless.</returns>
        protected virtual bool AllPortsWireless(XElement dap)
        {
            bool allPortsWireless = true;
            if (DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
            {
                foreach (var portSubmoduleItem in portSubmoduleItems)
                {
                    if (!IsPortWireless(portSubmoduleItem))
                    {
                        allPortsWireless = false;
                        break;
                    }
                }
            }
            return allPortsWireless;
        }

        /// <summary>
        /// Check number: CN_0x00020025
        /// The attribute InterfaceSubmoduleItem/@SupportedProtocols can have one or both of the following values: "SNMP", "LLDP".
        /// (1) When PNIO_Version >= "V2.31", SNMP shall be supported when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "B" or "C".
        /// (2) When PNIO_Version >= "V2.31", LLDP shall be supported except when DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "A"
        ///     and all port submodules which can be configured with this DAP have @MAUTypes=0 (CC A wireless).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020025()
        {
            var itfs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            itfs = Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in itfs)
            {
                var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;

                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }

                var dap = interfaceSubmoduleItem.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                if (!(pnioVersion >= 2.31))
                {
                    continue;
                }
                string supportedProtocols = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedProtocols);
                IList<string> supportedProtocolList = new List<string>(supportedProtocols.Split(Constants.s_Semicolon.ToCharArray()));
                var conformanceClassNode = ((IEnumerable)dap.XPathEvaluate("./gsddef:CertificationInfo/@ConformanceClass", Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                string conformanceClass = string.Empty;
                if (conformanceClassNode != null)
                    conformanceClass = conformanceClassNode.Value;

                // (1)
                CheckConformanceClassBorC(conformanceClass, supportedProtocolList, interfaceSubmoduleItem, lineInfo);

                // (2)
                CheckConformanceClassNotA(conformanceClass, supportedProtocolList, interfaceSubmoduleItem, dap, lineInfo);
            }

            return true;
        }

        private void CheckConformanceClassBorC(string conformanceClass, IList<string> supportedProtocolList, XElement interfaceSubmoduleItem, IXmlLineInfo lineInfo)
        {
            if (0 != string.Compare(conformanceClass, Constants.s_CharacterB, StringComparison.InvariantCulture)
                && 0 != string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }

            if (supportedProtocolList.Contains("SNMP"))
            {
                return;
            }

            // "When 'PNIO_Version' >= "V2.31" and 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' = "{0}"
            // is supported, 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "SNMP"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020025_1"), conformanceClass);
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                ReportCategories.TypeSpecific, "0x00020025_1");
        }

        private void CheckConformanceClassNotA(string conformanceClass, IList<string> supportedProtocolList, XElement interfaceSubmoduleItem, XElement dap, IXmlLineInfo lineInfo)
        {
            if (supportedProtocolList.Contains("LLDP"))
            {
                return;
            }

            {
                if (0 != string.Compare(conformanceClass, Constants.s_CharacterA, StringComparison.InvariantCulture))
                {
                    // "When 'PNIO_Version' >= "V2.31", 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "LLDP"
                    // except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" and all port submodules
                    // which can be configured with this DAP have 'MAUTypes' = 0 (wireless)."
                    string msg = Help.GetMessageString("M_" + Msg_0x00020025_2);
                    string xpath = Help.GetXPath(interfaceSubmoduleItem);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00020025_2");
                }
                else
                {
                    if (AllPortsWireless(dap))
                    {
                        return;
                    }

                    // "When 'PNIO_Version' >= "V2.31", 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "LLDP"
                    // except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" and all port submodules
                    // which can be configured with this DAP have 'MAUTypes' = 0 (wireless)."
                    string msg = Help.GetMessageString("M_" + Msg_0x00020025_2);
                    string xpath = Help.GetXPath(interfaceSubmoduleItem);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, Msg_0x00020025_2);
                }
            }
        }

        /// <summary>
        /// Check number: CN_0x00020026
        /// Checks some attributes of PortSubmoduleItem, if PNIO_Version >= "V2.31"
        /// (1) For @MAUTypes the value 0 is used for Radio communication and may only be used
        ///     when the attribute DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "A".
        /// (2) The attribute @MAUTypes shall be present if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        /// (3) The attribute @PortDeactivationSupported shall be present if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        /// (4) The attribute @LinkStateDiagnosisCapability shall be present if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        /// (5) The attribute @CheckMAUTypeSupported shall be present and "true" if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ConformanceClass is "C".
        /// (6) The attribute @ShortPreamble100MBitSupported shall be missing or "false" if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ConformanceClass is not "C".
        /// (7) The attribute @ShortPreamble100MBitSupported shall be present and "true" if the attribute
        ///     DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020026()
        {
            var ports = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem);
            ports = Help.TryRemoveXElementsUnderXsAny(ports, Nsmgr, Gsd);
            foreach (var portSubmoduleItem in ports)
            {
                var lineInfo = (IXmlLineInfo)portSubmoduleItem;

                if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
                {
                    continue;
                }

                foreach (var dap in dapsOfPort)
                {
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (!(pnioVersion >= 2.31))
                    {
                        continue;
                    }

                    string conformanceClass = string.Empty;
                    var conformanceClassNode = dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo).Attributes(Attributes.s_ConformanceClass).FirstOrDefault();
                    if (conformanceClassNode != null)
                        conformanceClass = conformanceClassNode.Value;
                    // (1)
                    CreateReport0x00020026_1(portSubmoduleItem, conformanceClass, dap, lineInfo);
                    // (2)
                    CreateReport0x00020026_2(portSubmoduleItem, conformanceClass, dap, lineInfo);





                    //(3)
                    var portDeactivationSupported = portSubmoduleItem.Attribute(Attributes.s_PortDeactivationSupported);
                    CreateReport0x00020026_3(portDeactivationSupported, conformanceClass, dap, portSubmoduleItem, lineInfo);


                    //(4)
                    var linkStateDiagnosisCapability = portSubmoduleItem.Attribute(Attributes.s_LinkStateDiagnosisCapability);
                    CreateReport0x00020026_4(linkStateDiagnosisCapability, conformanceClass, dap, portSubmoduleItem, lineInfo);


                    //(5)
                    string checkMauTypeSupportedStr = (string)portSubmoduleItem.Attribute(Attributes.s_CheckMauTypeSupported);
                    bool checkMauTypeSupported = false;
                    if (!string.IsNullOrEmpty(checkMauTypeSupportedStr))
                        checkMauTypeSupported = XmlConvert.ToBoolean(checkMauTypeSupportedStr);
                    CreateReport0x00020026_5(checkMauTypeSupported, conformanceClass, dap, portSubmoduleItem, lineInfo);


                    //(6)
                    string shortPreamble100MBitSupportedStr = (string)portSubmoduleItem.Attribute(Attributes.s_ShortPreamble100MbitSupported);
                    bool shortPreamble100MBitSupported = false;
                    if (!string.IsNullOrEmpty(shortPreamble100MBitSupportedStr))
                        shortPreamble100MBitSupported = XmlConvert.ToBoolean(shortPreamble100MBitSupportedStr);
                    CreateReport0x00020026_6(shortPreamble100MBitSupported, conformanceClass, dap, portSubmoduleItem, lineInfo);


                    // (7)
                    string applicationClass = string.Empty;
                    XAttribute applicationClassNode = dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo).Attributes("ApplicationClass").FirstOrDefault();
                    if (applicationClassNode != null)
                        applicationClass = applicationClassNode.Value;
                    IList<string> applicationClasses = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));
                    CreateReport0x00020026_7(shortPreamble100MBitSupported, applicationClasses, dap, portSubmoduleItem, lineInfo);


                }

            }


            return true;

        }

        private void CreateReport0x00020026_7(
                       bool shortPreamble100MBitSupported,
                       ICollection<string> applicationClasses,
                       XElement dap,
                       XObject portSubmoduleItem,
                       IXmlLineInfo lineInfo)
        {
            if (shortPreamble100MBitSupported || !applicationClasses.Contains("HighPerformance"))
            {
                return;
            }
            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ApplicationClass' "HighPerformance",
            // but the attribute 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is not present or not "true"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020026_7"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020026_7");
        }

        private void CreateReport0x00020026_6(
           bool shortPreamble100MBitSupported,
           string conformanceClass,
           XElement dap,
           XObject portSubmoduleItem,
           IXmlLineInfo lineInfo)
        {
            if (!shortPreamble100MBitSupported
       || 0 == string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }
            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' is not "C",
            // but the attribute 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is "true"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020026_6"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020026_6");
        }

        private void CreateReport0x00020026_5(
              bool checkMAUTypeSupported,
              string conformanceClass,
              XElement dap,
              XObject portSubmoduleItem,
              IXmlLineInfo lineInfo)
        {
            if (checkMAUTypeSupported || 0 != string.Compare(
         conformanceClass,
         Constants.s_CharacterC,
         StringComparison.InvariantCulture))
            {
                return;
            }

            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' "C",
            // but the attribute 'PortSubmoduleItem/@CheckMAUTypeSupported' is not present or not "true"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020026_5"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020026_5");
        }

        private void CreateReport0x00020026_4(
             XAttribute linkStateDiagnosisCapability,
             string conformanceClass,
             XElement dap,
             XObject portSubmoduleItem,
             IXmlLineInfo lineInfo)
        {
            if (linkStateDiagnosisCapability != null
     || 0 != string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }
            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' "C",
            // but the attribute 'PortSubmoduleItem/@LinkStateDiagnosisCapability' is not present."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020026_4"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020026_4");
        }
        private void CreateReport0x00020026_3(
              XAttribute portDeactivationSupported,
              string conformanceClass,
              XElement dap,
              XObject portSubmoduleItem,
              IXmlLineInfo lineInfo)
        {
            if (portDeactivationSupported != null
       || 0 != string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }
            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' "C",
            // but the attribute 'PortSubmoduleItem/@PortDeactivationSupported' is not present."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020026_3"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020026_3");
        }
        private void CreateReport0x00020026_2(
           XElement portSubmoduleItem,
           string conformanceClass,
           XElement dap,
           IXmlLineInfo lineInfo)
        {
            if (IsAdjustMauTypeSupported(portSubmoduleItem))
            {
                return;
            }

            if (0 != string.Compare(conformanceClass, Constants.s_CharacterC, StringComparison.InvariantCulture))
            {
                return;
            }
            // "The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"),
            // 'PNIO_Version' >= "V2.31" and 'ConformanceClass' "C",
            // but the attribute 'PortSubmoduleItem/@MAUTypes' is not present."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_" + Msg_0x00020026_2), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                Msg_0x00020026_2);
        }

        private void CreateReport0x00020026_1(
                       XElement portSubmoduleItem,
                       string conformanceClass,
                       XElement dap,
                       IXmlLineInfo lineInfo)
        {
            if (!IsPortWireless(portSubmoduleItem))
            {
                return;
            }
            if (0 == string.Compare(conformanceClass, Constants.s_CharacterA, StringComparison.InvariantCulture))
            {
                return;
            }
            // "The 'PortSubmoduleItem' with 'MAUTypes' = 0 is pluggable with 'DeviceAccessPointItem' ('ID' = "{0}")
            // and 'PNIO_Version' >= "V2.31", but 'DeviceAccessPointItem/.../@ConformanceClass' is not "A"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_" + Msg_0x00020026_1), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(portSubmoduleItem);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                Msg_0x00020026_1);
        }

        /// <summary>
        /// Check number: CN_0x00020027
        /// Some checks for RT_Class3Properties attributes and ApplicationClass, if PNIO_Version >= "V2.31"
        /// (1) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW shall be present and its
        ///     value shall be less or equal 2000 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// (2) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames shall be present and >0
        ///     if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "HighPerformance".
        /// (3) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType shall be present
        ///     if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains "HighPerformance".
        /// (4) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO shall be present and its
        ///     value shall be less or equal 3500 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// (5) The attribute InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed shall be present and its
        ///     value shall be less or equal 1000 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020027()
        {
            var rtcs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);
            foreach (var rtClass3Properties in rtcs)
            {
                var lineInfo = (IXmlLineInfo)rtClass3Properties;

                var dap = rtClass3Properties.Parent.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (!(pnioVersion >= 2.31))
                {
                    continue;
                }
                string applicationClass = string.Empty;
                var applicationClassNode = dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo)
                    .Attributes(Attributes.s_ApplicationClass)
                    .FirstOrDefault();
                if (applicationClassNode != null)
                    applicationClass = applicationClassNode.Value;
                var applicationClasses = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));

                if (!applicationClasses.Contains("HighPerformance"))
                {
                    continue;
                }
                // (1)
                string maxBridgeDelayFfwStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MaxBridgeDelayFfw);
                if (string.IsNullOrEmpty(maxBridgeDelayFfwStr))
                {
                    CreateReport0x00020027_1(rtClass3Properties, lineInfo);
                }
                else
                {
                    UInt16 maxBridgeDelayFfw = XmlConvert.ToUInt16(maxBridgeDelayFfwStr);
                    CreateReport0x00020027_2(maxBridgeDelayFfw, rtClass3Properties, lineInfo);
                }

                // (2)
                string maxDfpFramesStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MaxDfpFrames);
                if (string.IsNullOrEmpty(maxDfpFramesStr))
                {
                    CreateReport0x00020027_3(rtClass3Properties, lineInfo);
                }
                else
                {
                    UInt16 minNrtGap = XmlConvert.ToUInt16(maxDfpFramesStr);
                    CreateReport0x00020027_4(minNrtGap, rtClass3Properties, lineInfo);
                }

                // (3)
                var fragmentationType = rtClass3Properties.Attribute(Attributes.s_FragmentationType);
                CreateReport0x00020027_5(fragmentationType, rtClass3Properties, lineInfo);

                // (4)
                string minFsoStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MinFso);
                if (string.IsNullOrEmpty(minFsoStr))
                {
                    CreateReport0x00020027_6(rtClass3Properties, lineInfo);
                }
                else
                {
                    UInt16 minFso = XmlConvert.ToUInt16(minFsoStr);
                    CreateReport0x00020027_7(minFso, rtClass3Properties, lineInfo);
                }

                // (5)
                string maxDfpFeedStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MaxDfpFeed);
                if (string.IsNullOrEmpty(maxDfpFeedStr))
                {
                    CreateReport0x00020027_8(rtClass3Properties, lineInfo);
                }
                else
                {
                    UInt16 maxDfpFeed = XmlConvert.ToUInt16(maxDfpFeedStr);
                    CreateReport0x00020027_9(maxDfpFeed, rtClass3Properties, lineInfo);
                }


            }

            return true;
        }

        private void CreateReport0x00020027_1(XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is not present.
            // This attribute shall be present and its value shall be <= 2000 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00020027_1");
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_1");
        }

        private void CreateReport0x00020027_3(XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is not present.
            // This attribute shall be present and >0 if 'PNIO_Version' >= "V2.31" and
            // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00020027_3");
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_3");
        }

        private void CreateReport0x00020027_6(XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO' is not present.
            // This attribute shall be present and its value shall be <= 3500 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00020027_6");
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_6");
        }

        private void CreateReport0x00020027_8(XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed' is not present.
            // This attribute shall be present and its value shall be <= 1000 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00020027_8");
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_8");
        }

        private void CreateReport0x00020027_9(ushort maxDfpFeed, XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            if (maxDfpFeed <= 1000)
            {
                return;
            }

            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed' is {0}.
            // This attribute shall be present and its value shall be <= 1000 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020027_9"), maxDfpFeed);
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_9");
        }

        private void CreateReport0x00020027_7(ushort minFso, XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            if (minFso <= 3500)
            {
                return;
            }

            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO' is {0}.
            // This attribute shall be present and its value shall be <= 3500 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020027_7"), minFso);
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_7");
        }

        private void CreateReport0x00020027_5(XAttribute fragmentationType, XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            if (fragmentationType != null)
            {
                return;
            }

            // "'InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType' is not present.
            // This attribute shall be present if 'PNIO_Version' >= "V2.31" and the attribute
            // 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00020027_5");
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_5");
        }

        private void CreateReport0x00020027_4(ushort minNrtGap, XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            if (minNrtGap != 0)
            {
                return;
            }

            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is {0}.
            // This attribute shall be present and >0 if 'PNIO_Version' >= "V2.31" and
            // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020027_4"), minNrtGap);
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_4");
        }

        private void CreateReport0x00020027_2(ushort maxBridgeDelayFfw, XObject rtClass3Properties, IXmlLineInfo lineInfo)
        {
            if (maxBridgeDelayFfw <= 2000)
            {
                return;
            }

            // "'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is {0}.
            // This attribute shall be present and its value shall be <= 2000 if 'PNIO_Version' >= "V2.31"
            // and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020027_2"), maxBridgeDelayFfw);
            string xpath = Help.GetXPath(rtClass3Properties);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00020027_2");
        }

        /// <summary>
        /// Check number: CN_0x00020028
        /// Some checks for SynchronisationMode attributes and ApplicationClass, if 'PNIO_Version' >= "V2.31"
        /// (1) The attribute InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter shall be present and its
        ///     value shall be less or equal 250 if the attribute DeviceAccessPointItem/CertificationInfo/@ApplicationClass
        ///     contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020028()
        {
            var syncModes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SynchronisationMode);
            syncModes = Help.TryRemoveXElementsUnderXsAny(syncModes, Nsmgr, Gsd);
            foreach (var synchronisationMode in syncModes)
            {
                var lineInfo = (IXmlLineInfo)synchronisationMode;
                if (synchronisationMode.Parent == null)
                {
                    continue;
                }
                var dap = synchronisationMode.Parent.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (!(pnioVersion >= 2.31))
                {
                    continue;
                }
                string applicationClass = string.Empty;
                var applicationClassNode = dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo)
                    .Attributes(Attributes.s_ApplicationClass)
                    .FirstOrDefault();
                if (applicationClassNode != null)
                    applicationClass = applicationClassNode.Value;
                var applicationClasses = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));

                if (!applicationClasses.Contains("HighPerformance"))
                {
                    continue;
                }
                // (1)
                string peerToPeerJitterStr = Help.GetAttributeValueFromXElement(synchronisationMode, Attributes.s_PeerToPeerJitter);
                if (string.IsNullOrEmpty(peerToPeerJitterStr))
                {
                    // "'InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter' is not present.
                    // This attribute shall be present and its value shall be <= 250 if 'PNIO_Version' >= "V2.31" and
                    // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                    string msg = Help.GetMessageString("M_0x00020028_1");
                    string xpath = Help.GetXPath(synchronisationMode);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020028_1");
                }
                else
                {
                    UInt16 peerToPeerJitter = XmlConvert.ToUInt16(peerToPeerJitterStr);
                    if (peerToPeerJitter <= 250)
                    {
                        continue;
                    }
                    // "'InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter' is {0}.
                    // This attribute shall be present and its value shall be <= 250 if 'PNIO_Version' >= "V2.31" and
                    // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020028_2"), peerToPeerJitter);
                    string xpath = Help.GetXPath(synchronisationMode);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020028_2");

                }


            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020029
        /// Some checks for RT_Class3TimingProperties attributes and ApplicationClass, if PNIO_Version >= "V2.31"
        /// (1) The attribute InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@SendClock shall be present
        ///     and it shall contain value(s) less than 8 if the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass'
        ///     contains "HighPerformance".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020029()
        {
            var rtcs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3TimingProperties);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);
            foreach (var rtClass3TimingProperties in rtcs)
            {
                var lineInfo = (IXmlLineInfo)rtClass3TimingProperties;
                if (rtClass3TimingProperties.Parent != null)
                {
                    var dap = rtClass3TimingProperties.Parent.Parent.Parent.Parent;
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (!(pnioVersion >= 2.31))
                    {
                        continue;
                    }
                    string applicationClass = string.Empty;
                    var applicationClassNode = dap.Elements(NamespaceGsdDef + Elements.s_CertificationInfo)
                        .Attributes(Attributes.s_ApplicationClass)
                        .FirstOrDefault();
                    if (applicationClassNode != null)
                        applicationClass = applicationClassNode.Value;
                    var applicationClasses =
                        new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));
                    if (!applicationClasses.Contains("HighPerformance"))
                    {
                        continue;
                    }

                }
                // (1)
                CreateReport0x00020029(rtClass3TimingProperties, lineInfo);
            }

            return true;
        }

        private void CreateReport0x00020029(XElement rtClass3TimingProperties, IXmlLineInfo lineInfo)
        {
            var sendClockNode = rtClass3TimingProperties.Attribute(Attributes.s_SendClock);
            if (null == sendClockNode)
            {
                // "'InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock' is not present.
                // This attribute shall be present and shall contain value(s) < 8 if 'PNIO_Version' >= "V2.31" and
                // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = Help.GetMessageString("M_0x00020029_1");
                string xpath = Help.GetXPath(rtClass3TimingProperties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020029_1");
            }
            else
            {
                bool smallFactorFound = false;
                List<ValueListHelper.ValueRangeT> sendClocks = ValueListHelper.NormalizeValueList(sendClockNode, Store);

                for (int currentRange = 0; currentRange < sendClocks.Count; currentRange++)
                {
                    uint currentValue = sendClocks[currentRange].From;
                    while (currentValue <= sendClocks[currentRange].To)
                    {
                        if (currentValue < 8)
                        {
                            smallFactorFound = true;
                            break;
                        }

                        currentValue++;
                    }

                    if (smallFactorFound)
                        break;
                }

                if (smallFactorFound)
                {
                    return;
                }

                // "'InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock' is {0}.
                // This attribute shall be present and shall contain value(s) < 8 if 'PNIO_Version' >= "V2.31" and
                // the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance"."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020029_2"), sendClockNode.Value);
                string xpath = Help.GetXPath(rtClass3TimingProperties);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020029_2");
            }
        }

        /// <summary>
        /// Check number: CN_0x0002002A
        /// 
        /// Current rule for GSDML V2.3:
        /// The attribute DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported shall
        /// be present and "true" if the element SystemRedundancy is present.
        /// 
        /// The discussion in PN IO TT showed that there are Siemens devices that implement an interim version
        /// (not the official PNO version) of system redundancy. STEP 7 / TIA Portal support this interim version,
        /// but no other engineering tool. The differentiation between the interim version and the official version
        /// is done based on the attribute PrmBeginPrmEndSequenceSupported.
        /// The respective DAP cannot be removed from the GSD because of compatibility to existing user projects.
        /// 
        /// The GSDML Specification V2.34 has been enhanced that it allows PrmBeginPrmEndSequence to be missing for interim
        /// version, where a (non-Siemens) engineering tool shall issue a warning and ignore the feature system redundancy.
        /// 
        /// So from GSDML V2.34 on, this check is changed:
        /// If DAP/SystemRedundancy is present, but PrmBeginPrmEndSequenceSupported is missing or "false:
        /// If PNIO_Version is less or equal V2.31, issue a Warning.
        /// Suggestion: "If 'SystemRedundancy' is present but 'PrmBeginPrmEndSequenceSupported' is missing or "false"
        /// this DAP supports an interim version of system redundancy which is unsupported by most controllers."
        /// Else issue the current message as Error instead of MinorError.
        /// 
        /// Unchanged check:
        /// When system redundancy is supported, no port submodules shall be pluggable and the modules pluggable with this DAP
        /// shall not carry 'PortSubmodule' elements in its 'ModuleItem/SystemDefinedSubmoduleList'."
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0002002A()
        {
            var systemredundancyNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy);
            systemredundancyNodes = Help.TryRemoveXElementsUnderXsAny(systemredundancyNodes, Nsmgr, Gsd);
            foreach (var systemRedundancy in systemredundancyNodes)
            {
                var dap = systemRedundancy.Parent;
                string prmBeginPrmEndSequenceSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_PrmBeginPrmEndSequenceSupported);
                bool prmBeginPrmEndSequenceSupported = false;
                if (!string.IsNullOrEmpty(prmBeginPrmEndSequenceSupportedStr))
                    prmBeginPrmEndSequenceSupported = XmlConvert.ToBoolean(prmBeginPrmEndSequenceSupportedStr);
                if (!prmBeginPrmEndSequenceSupported)
                {
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                    if (pnioVersion <= 2.31)
                    {
                        // "If 'SystemRedundancy' is present but 'PrmBeginPrmEndSequenceSupported' is missing or "false" this DAP supports
                        //  an interim version of system redundancy which is unsupported by most controllers."
                        string msg = Help.GetMessageString("M_0x0002002A_4");
                        string xpath = Help.GetXPath(systemRedundancy);
                        var xli = (IXmlLineInfo)systemRedundancy;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0002002A_4");
                    }
                    else
                    {
                        // "'DeviceAccessPointItem/SystemRedundancy' is present, but the attribute
                        // 'DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported' is not present or not "true"."
                        string msg = Help.GetMessageString("M_0x0002002A_1");
                        string xpath = Help.GetXPath(systemRedundancy);
                        var xli = (IXmlLineInfo)systemRedundancy;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0002002A_1");
                    }
                }
                // TFS #2375313: When DeviceType is "S2", no error shall occur anymore
                if (S2RedundancySupported(dap))
                    continue;

                if (dap == null)
                {
                    continue;
                }

                var useableModules = dap.Element(NamespaceGsdDef + Elements.s_UseableModules);
                if (useableModules == null)
                {
                    continue;
                }

                CheckCN_0x0002002A_ModuleItemRef(useableModules, dap);
            }

            return true;
        }

        private void CheckCN_0x0002002A_ModuleItemRef(XContainer useableModules, XElement dap)
        {
            var moduleItemRefs = useableModules.Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);
            foreach (var moduleItemRef in moduleItemRefs)
            {
                string moduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                if (null == module)
                    continue;

                var lineInfoModule = (IXmlLineInfo)module;

                CreateReport0x0002002A_2(module, dap, lineInfoModule);

                if (CreateReport0x0002002A_3(moduleItemRef, dap, module, lineInfoModule))
                {
                    continue;
                }

                break;
            }
        }

        private bool CreateReport0x0002002A_3(
            XElement moduleItemRef,
            XElement dap,
            XObject module,
            IXmlLineInfo lineInfoModule)
        {
            // Check all usable port submodules of modules at the DAP
            ModuleRefToPluggablePortDictionary.TryGetValue(moduleItemRef, out IList<XElement> portSubmodules);
            if (portSubmodules == null
                || portSubmodules.Count <= 0)
            {
                return true;
            }

            {
                // "'SubmoduleItemRef/@SubmoduleItemTarget' references a 'PortSubmoduleItem', but if system redundancy is supported,
                // the modules pluggable with this DAP ('ID' = "{0}") shall not carry 'PortSubmodule' elements."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0002002A_3"), dap.Attribute(Attributes.ID));
                string xpath = Help.GetXPath(module);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfoModule.LineNumber,
                    lineInfoModule.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0002002A_3");
            }
            return false;
        }

        private void CreateReport0x0002002A_2(XContainer module, XElement dap, IXmlLineInfo lineInfoModule)
        {
            // Check for SystemDefinedSubmoduleList of modules at the DAP
            var systemDefinedSubmoduleListModule = module.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList);
            if (systemDefinedSubmoduleListModule == null)
            {
                return;
            }

            // "'ModuleItem/SystemDefinedSubmoduleList' is present, but if system redundancy is supported,
            // the modules pluggable with this DAP ('ID' = "{0}") shall not carry 'PortSubmodule' elements."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0002002A_2"), dap.Attribute(Attributes.ID));
            string xpath = Help.GetXPath(module);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfoModule.LineNumber,
                lineInfoModule.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0002002A_2");
        }

        /// <summary>
        /// Check number: CN_0x0002002B
        /// The attributes RT_Class3Properties/@MaxDFP_Feed and RT_Class3Properties/@AlignDFP_Subframes shall
        /// only be present if DFP is supported, i.e. RT_Class3Properties/@MaxDFP_Frames <>0.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0002002B()
        {
            var rtcs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties)
                .Where(x => x.Attribute(Attributes.s_MaxDfpFeed) != null || x.Attribute(Attributes.s_AlignDfpSubframes) != null);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);
            foreach (var rtClass3Properties in rtcs)
            {
                var lineInfo = (IXmlLineInfo)rtClass3Properties;

                string maxDFPFramesStr = Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MaxDfpFrames);
                UInt16 maxDFPFrames = 0;
                if (!string.IsNullOrEmpty(maxDFPFramesStr))
                    maxDFPFrames = XmlConvert.ToUInt16(maxDFPFramesStr);
                if (0 == maxDFPFrames)
                {
                    if (rtClass3Properties.Attribute(Attributes.s_MaxDfpFeed) != null)
                    {
                        // "The attribute 'MaxDFP_Feed' shall only be present if DFP is supported, i.e. 'MaxDFP_Frames' <>0."
                        string msg = Help.GetMessageString("M_0x0002002B_1");
                        string xpath = Help.GetXPath(rtClass3Properties);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0002002B_1");
                    }

                    if (rtClass3Properties.Attribute(Attributes.s_AlignDfpSubframes) != null)
                    {
                        // "The attribute 'AlignDFP_Subframes' shall only be present if DFP is supported, i.e. 'MaxDFP_Frames' <>0."
                        string msg = Help.GetMessageString("M_0x0002002B_2");
                        string xpath = Help.GetXPath(rtClass3Properties);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0002002B_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0002002C
        /// The attribute DeviceAccessPointItem/@CIR_Supported may
        /// only be present and "true" if the element SystemRedundancy is present.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0002002C()
        {
            var daps =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_CirSupported) != null);

            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strCirSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_CirSupported);
                bool cirSupported = XmlConvert.ToBoolean(strCirSupported);
                if (cirSupported)
                {
                    var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);
                    if (systemRedundancy == null)
                    {
                        // "'DeviceAccessPointItem/@CIR_Supported' is present
                        // and "true", but the element 'SystemRedundancy' is not present."
                        string msg = Help.GetMessageString("M_0x0002002C_1");
                        string xpath = Help.GetXPath(dap);
                        var xli = (IXmlLineInfo)dap;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0002002C_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0002002D
        /// If the attribute InterfaceSubmoduleItem/@SupportedRT_Class is present it shall
        /// be set to "Class3" when MediaRedundancy/@MRPD_Supported is "true".
        /// If the attribute InterfaceSubmoduleItem/@SupportedRT_Classes is present
        /// the attribute InterfaceSubmoduleItem/@SupportedRT_Class shall be ignored.
        /// If the attribute InterfaceSubmoduleItem/@SupportedRT_Classes is present it shall
        /// contain "RT_CLASS_3" when MediaRedundancy/@MRPD_Supported is "true".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0002002D()
        {
            var itfs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem)
                .Where(
                    x =>
                        x.Attribute(Attributes.s_SupportedRTClass) != null ||
                        x.Attribute(Attributes.s_SupportedRTClasses) != null);
            itfs = Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in itfs)
            {
                var mrpdSupportedNode = interfaceSubmoduleItem.Elements(NamespaceGsdDef + Elements.s_MediaRedundancy)
                    .Attributes(Attributes.s_MrpdSupported)
                    .FirstOrDefault();
                bool mrpdSupported = false;
                if (mrpdSupportedNode != null)
                    mrpdSupported = XmlConvert.ToBoolean(mrpdSupportedNode.Value);
                string supportedRTClassesStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
                if (!string.IsNullOrEmpty(supportedRTClassesStr))
                {
                    CheckSupportedRTClasses(interfaceSubmoduleItem, mrpdSupported, supportedRTClassesStr);
                }
                else
                {
                    CheckSupportedRTClass(interfaceSubmoduleItem, mrpdSupported);
                }
            }

            return true;
        }

        private void CheckSupportedRTClasses(XElement interfaceSubmoduleItem, bool mrpdSupported, string supportedRTClassesStr)
        {
            var supportedRTClasses = new List<string>(supportedRTClassesStr.Split(Constants.s_Semicolon.ToCharArray()));
            if (!mrpdSupported || supportedRTClasses.Contains("RT_CLASS_3"))
            {
                return;
            }

            // "The attribute 'InterfaceSubmoduleItem/@SupportedRT_Classes' is present and
            // 'MediaRedundancy/@MRPD_Supported' is "true", but the 'SupportedRT_Classes' does not contain "RT_CLASS_3"."
            string msg = Help.GetMessageString("M_0x0002002D_1");
            var supportedRTClassesNode = interfaceSubmoduleItem.Attribute(Attributes.s_SupportedRTClasses);
            string xpath = Help.GetXPath(supportedRTClassesNode);
            var lineInfo = (IXmlLineInfo)supportedRTClassesNode;
            if (lineInfo != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0002002D_1");
            }
        }

        private void CheckSupportedRTClass(XElement interfaceSubmoduleItem, bool mrpdSupported)
        {
            string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
            if (!mrpdSupported || 0 == string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
            {
                return;
            }

            // "The attribute 'InterfaceSubmoduleItem/@SupportedRT_Class' is present and legal and
            // 'MediaRedundancy/@MRPD_Supported' is "true", but the 'SupportedRT_Class' is not equal to "Class3"."
            string msg = Help.GetMessageString("M_0x0002002D_2");
            var supportedRTClassNode = interfaceSubmoduleItem.Attribute(Attributes.s_SupportedRTClass);
            if (supportedRTClassNode != null)
            {
                string xpath = Help.GetXPath(supportedRTClassNode);
                var lineInfo = (IXmlLineInfo)supportedRTClassNode;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x0002002D_2");
            }
            else
            {
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x0002002D_2");
            }
        }

        /// <summary>
        /// Check number: CN_0x0002002E
        /// The attribute MediaRedundancy/@MRT_Supported may only be present and "true"
        /// if the attribute @MRPD_Supported is present and "true".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0002002E()
        {
            // Find all MediaRedundancy with MRT_Supported = "true"
            var mediaRedundancyList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MediaRedundancy);
            mediaRedundancyList = Help.TryRemoveXElementsUnderXsAny(mediaRedundancyList, Nsmgr, Gsd);
            foreach (var mediaRedundancy in mediaRedundancyList)
            {
                string strMrtSupported = Help.GetAttributeValueFromXElement(mediaRedundancy, Attributes.s_MrtSupported);
                bool bMrtSupported = false;
                if (!string.IsNullOrEmpty(strMrtSupported))
                    bMrtSupported = XmlConvert.ToBoolean(strMrtSupported);
                if (bMrtSupported)
                {
                    // Check the attribute MRPD_Supported
                    string strMrpdSupported = Help.GetAttributeValueFromXElement(mediaRedundancy, Attributes.s_MrpdSupported);
                    bool bMrpdSupported = false;
                    if (!string.IsNullOrEmpty(strMrpdSupported))
                        bMrpdSupported = XmlConvert.ToBoolean(strMrpdSupported);
                    if (!bMrpdSupported)
                    {
                        if (Help.CheckSchemaVersion(mediaRedundancy, SupportedGsdmlVersion))
                        {
                            // "If the attribute 'MediaRedundancy/@MRT_Supported' is present and "true",
                            //  'MediaRedundancy/@MRPD_Supported' must be present and "true"."
                            string msg = Help.GetMessageString("M_0x0002002E_1");
                            string xpath = Help.GetXPath(mediaRedundancy);
                            var xli = (IXmlLineInfo)mediaRedundancy;
                            Store.CreateAndAnnounceReport(ReportType_0x0002002E, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.Validation, "0x0002002E_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check the attribute SupportedSyncProtocols in combination with supportedRTClasses.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00020030()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in nl)
            {
                XAttribute supportedSyncProtocolsNode = interfaceSubmoduleItem.Elements(NamespaceGsdDef + Elements.s_SynchronisationMode)
                    .Attributes(Attributes.s_SupportedSyncProtocols)
                    .FirstOrDefault();
                IList<string> supportedSyncProtocols;
                if (supportedSyncProtocolsNode != null)
                {
                    string supportedSyncProtocolsStr = supportedSyncProtocolsNode.Value;
                    supportedSyncProtocols = new List<string>(supportedSyncProtocolsStr.Split(Constants.s_Semicolon.ToCharArray()));
                }
                else
                {
                    supportedSyncProtocols = new List<string>();
                }

                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }

                var dap = interfaceSubmoduleItem.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (!(pnioVersion >= 2.2))
                {
                    continue;
                }

                var supportedRTClasses = GetRTClasses(interfaceSubmoduleItem);
                if (!supportedRTClasses.Contains("RT_CLASS_3")
                    || supportedSyncProtocols.Contains("PTCP"))
                {
                    continue;
                }
                // "The 'InterfaceSubmoduleItem' supports RT_CLASS_3, but the attribute 'SupportedSyncProtocols' does not contain the token "PTCP"."
                string msg = Help.GetMessageString("M_0x00020030_1");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                var xli = (IXmlLineInfo)interfaceSubmoduleItem;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00020030_1");


            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020031
        /// The attribute RT_Class3Properties/@MaxDFP_Frames shall not exceed ApplicationRelations/@NumberOfAR.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020031()
        {
            var rtcs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RTClass3Properties)
                .Where(x => x.Attribute(Attributes.s_MaxDfpFrames) != null);
            rtcs = Help.TryRemoveXElementsUnderXsAny(rtcs, Nsmgr, Gsd);
            foreach (var rtClass3Properties in rtcs)
            {
                var interfaceSubmoduleItem = rtClass3Properties.Parent;
                if (interfaceSubmoduleItem == null)
                {
                    continue;
                }

                var applicationRelations =
                    interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_ApplicationRelations);
                string numberOfARStr = string.Empty;
                if (applicationRelations != null)
                    numberOfARStr = Help.GetAttributeValueFromXElement(applicationRelations, Attributes.s_NumberOfAr);
                UInt16 numberOfAr = 1;
                if (!string.IsNullOrEmpty(numberOfARStr))
                    numberOfAr = XmlConvert.ToUInt16(numberOfARStr);
                UInt16 maxDFPFrames = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(rtClass3Properties, Attributes.s_MaxDfpFrames));
                if (maxDFPFrames <= numberOfAr)
                {
                    continue;
                }
                // "'RT_Class3Properties/@MaxDFP_Frames' ({0}) shall not exceed 'ApplicationRelations/@NumberOfAR' ({1})."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00020031_1"), maxDFPFrames, numberOfAr);
                string xpath = Help.GetXPath(rtClass3Properties.Attribute(Attributes.s_MaxDfpFrames));
                var xli = (IXmlLineInfo)rtClass3Properties.Attribute(Attributes.s_MaxDfpFrames);
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020031_1");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00020032
        /// (1) If the attribute '@SharedDeviceSupported' is present and "true", the attribute
        ///     'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' shall be present
        ///     and its value shall be >1.
        /// (2) If the element 'SystemRedundancy' is present, the attribute
        ///     '@NumberOfAR' shall be present and its value shall be greater than or equal to
        ///     the value of the attribute '@NumberOfAR_Sets',
        ///     multiplied with the number of ARs which constitute an SR-AR set.
        ///     An SR-AR set comprises two ARs for an S2 device (two for one NAP),
        ///     two ARs for an R1 device (one for each NAP),
        ///     and four ARs for an R2 device (two for each NAP).
        /// (3) If the attribute '@IO_SupervisorSupported' is present and "true",
        ///     the attribute '@NumberOfAR' shall be present and its value shall be >1.
        /// (4) When SharedDeviceSupported is missing or "false" and element SystemRedundancy is missing,
        ///     only one or two ARs may be established (depending on whether IO_SupervisorSupported is
        ///     missing or "false", or IO_SupervisorSupported is present and "true").
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00020032()
        {
            // (1)
            CreateReport0x00020032_1();




            // (2)
            CreateReport0x00020032_2();



            // (3)
            CreateReport0x00020032_3();



            // (4)
            CreateReport0x00020032_4();


            return true;
        }

        private void CreateReport0x00020032_4()
        {
            IEnumerable<XElement> daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
            .Where(x => x.Element(NamespaceGsdDef + Elements.s_SystemRedundancy) == null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strSharedDeviceSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_SharedDeviceSupported);
                bool sharedDeviceSupported = false;
                if (!string.IsNullOrEmpty(strSharedDeviceSupported))
                    sharedDeviceSupported = XmlConvert.ToBoolean(strSharedDeviceSupported);
                string strIOSupervisorSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_IOSupervisorSupported);
                bool IOSupervisorSupported = false;
                if (!string.IsNullOrEmpty(strIOSupervisorSupported))
                    IOSupervisorSupported = XmlConvert.ToBoolean(strIOSupervisorSupported);
                UInt16 defaultNumberOfAR = 1;
                if (IOSupervisorSupported)
                    defaultNumberOfAR = 2;

                XAttribute numberOfARNode = ((IEnumerable)dap.XPathEvaluate(
                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations/@NumberOfAR", Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (numberOfARNode == null) // If NumberOfAR is missing the default of "1" works, which is always allowed
                    continue;

                UInt16 numberOfAR = XmlConvert.ToUInt16(numberOfARNode.Value);
                if (sharedDeviceSupported || numberOfAR <= defaultNumberOfAR)
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "NumberOfAR is too high. When neither shared device nor system redundancy is supported, only one or two ARs
                // may be established (depending on whether IO supervisor is supported)."
                string msg = Help.GetMessageString("M_0x00020032_4");
                string xpath = Help.GetXPath(numberOfARNode);
                var xli = (IXmlLineInfo)numberOfARNode;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_MinorError,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020032_4");
            }
        }

        private void CreateReport0x00020032_3()
        {
            IEnumerable<XElement> daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                .Where(x => x.Attribute(Attributes.s_IOSupervisorSupported) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strIOSupervisorSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_IOSupervisorSupported);
                bool ioSupervisorSupported = XmlConvert.ToBoolean(strIOSupervisorSupported);
                if (!ioSupervisorSupported)
                {
                    continue;
                }

                UInt16 numberOfAr = 1; // default value
                XAttribute numberOfArNode = ((IEnumerable)dap.XPathEvaluate(
                                                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations/@NumberOfAR",
                                                    Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (numberOfArNode != null)
                    numberOfAr = XmlConvert.ToUInt16(numberOfArNode.Value);
                if (numberOfAr > 1)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "If the attribute 'DeviceAccessPointItem/@IO_SupervisorSupported' is present and "true",
                //  'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present and >1."
                string msg = Help.GetMessageString("M_0x00020032_3");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_MinorError,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020032_3");
            }
        }

        private void CreateReport0x00020032_2()
        {
            IEnumerable<XElement> daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                .Where(x => x.Element(NamespaceGsdDef + Elements.s_SystemRedundancy) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                UInt16 numberOfArSets = 1; // default value
                string strNumberOfArSets = Help.GetAttributeValueFromXElement(
                    dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy),
                    Attributes.s_NumberOfArSets);
                if (!string.IsNullOrEmpty(strNumberOfArSets))
                    numberOfArSets = XmlConvert.ToUInt16(strNumberOfArSets);
                UInt16 numberOfAr = 1; // default value
                XAttribute numberOfArNode = ((IEnumerable)dap.XPathEvaluate(
                                                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations/@NumberOfAR",
                                                    Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (numberOfArNode != null)
                    numberOfAr = XmlConvert.ToUInt16(numberOfArNode.Value);

                UInt16 numberOfARsForSets = 0;
                if (R2RedundancySupported(dap))
                    numberOfARsForSets = (UInt16)(numberOfArSets * 4);
                else if (S2RedundancySupported(dap)
                         || DapRedundancySupported(dap))
                    numberOfARsForSets = (UInt16)(numberOfArSets * 2);

                if (numberOfAr >= numberOfARsForSets)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "If the element 'DeviceAccessPointItem/SystemRedundancy' is present,
                //  'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present
                //  and greater than or equal to the value of the attribute NumberOfAR_Sets,
                //  multiplied with the number of ARs which constitute an SR-AR set."
                string msg = Help.GetMessageString("M_0x00020032_2");
                string xpath = Help.GetXPath(dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy));
                var xli = (IXmlLineInfo)dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_MinorError,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00020032_2");
                }
            }
        }

        private void CreateReport0x00020032_1()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                .Where(x => x.Attribute(Attributes.s_SharedDeviceSupported) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strSharedDeviceSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_SharedDeviceSupported);
                bool sharedDeviceSupported = XmlConvert.ToBoolean(strSharedDeviceSupported);
                if (!sharedDeviceSupported)
                {
                    continue;
                }

                UInt16 numberOfAr = 1; // default value
                XAttribute numberOfArNode = ((IEnumerable)dap.XPathEvaluate(
                                                    "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem/gsddef:ApplicationRelations/@NumberOfAR",
                                                    Nsmgr)).Cast<XAttribute>().FirstOrDefault();
                if (numberOfArNode != null)
                    numberOfAr = XmlConvert.ToUInt16(numberOfArNode.Value);
                if (numberOfAr > 1)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "If the attribute 'DeviceAccessPointItem/@SharedDeviceSupported' is present and "true",
                //  'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present and >1."
                string msg = Help.GetMessageString("M_0x00020032_1");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_MinorError,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00020032_1");
            }
        }

        #endregion

        #endregion
    }
}



