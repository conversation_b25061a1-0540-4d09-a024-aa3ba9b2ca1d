/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: GeneralUtilities.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.PNInterface;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Summary description for GeneralUtilities.
    /// </summary>
    internal static class GeneralUtilities
    {
        /// <summary>
        /// Returns true, if the IO Device Interface submodule supports the PN IO Submodule model 'PNIoSubmoduleModelSupp' and
        /// the PNParameterizationDisallowed is acitvated (PDEV)
        /// </summary>
        public static bool IsPDEVDevice(PclObject deviceInterfaceSubmodule)
        {
            // Is PNIoSubmoduleModelSupp PDEV supported
            bool pdevModelSupp =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSubmoduleModelSupp,
                    new AttributeAccessCode(),
                    false);
            if (!pdevModelSupp)
            {
                return false;
            }

            // Is PDEV decentrale activated
            return
                !deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnParameterizationDisallowed,
                    new AttributeAccessCode(),
                    false);
        }

        /// <summary>
        /// Gets the least common multiplier of two integer numbers
        /// </summary>
        /// <param name="num1">num1</param>
        /// <param name="num2">num2</param>
        /// <returns>the LCM</returns>
        private static int GetLCM(int num1, int num2)
        {
            return (num1 * num2) / GetGCD(num1, num2);
        }

        /// <summary>
        /// Gets the greatest common divisor of two integer numbers
        /// </summary>
        /// <param name="num1">num1</param>
        /// <param name="num2">num2</param>
        /// <returns>the GCD</returns>
        private static int GetGCD(int num1, int num2)
        {
            while (num1 != 0 && num2 != 0)
            {
                if (num1 > num2)
                { num1 = num1 % num2; }
                else
                { num2 = num2 % num1; }
            }
            // At this point, GCD(K,M) = GCD(k,m) = max(k,m)
            return Math.Max(num1, num2);
        }

        /// <summary>
        /// Gets the least common multiplier of two integer numbers
        /// </summary>
        /// <param name="numbers">numbers</param>
        /// <returns>the LCM</returns>
        public static int GetLCMM(List<int> numbers)
        {
            if (numbers == null) throw new ArgumentNullException(nameof(numbers));
            if (numbers.Count > 1)
            {
                int firstnum = numbers[0];
                numbers.RemoveAt(0);
                return GetLCM(firstnum, GetLCMM(numbers));
            }
            if (numbers.Count == 1)
            {
                return numbers[0];
            }
            return -1;
        }
        /// <summary>
        /// Returns the Ti/To raster value (if 'From OB' calculation mode is selected, it will give back the 
        /// value from the central interface)
        /// </summary>
        /// <param name="decentralInterface">Isochron decentral interface object</param>
        /// <returns>Raster value</returns>
        public static long GetTiToTimeBase(Interface decentralInterface)
        {
            if (decentralInterface == null)
            {
                throw new ArgumentNullException(nameof(decentralInterface));
            }

            Int64 tiToTimeBase = PNIsochronIFDecentralBL.CalculateIsochronParameters(
                decentralInterface,
                PNIsochronIFDecentralBL.IsochronParameter.PNIsoT_IO_BASE);
            return tiToTimeBase;
        }
    }
}