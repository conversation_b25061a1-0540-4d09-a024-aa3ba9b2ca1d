/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ParameterDatenblock.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL.ConfigParser.DeviceConfig
{
    internal class ParameterDatenblock : ConfigBlock, IParameterDatenblock
    {
        private ushort m_blockVersion;

        private Dataset[] m_Datasets;

        public ParameterDatenblock(byte[] data, int offset, int length, ushort blockVersion)
            : base(data, offset, length)
        {
            m_blockVersion = blockVersion;
        }

        #region interface IParameterDatenblock

        public IEnumerable<IDataset> Datasets
        {
            get
            {
                if (m_Datasets == null)
                {
                    ushort prmIdOffset = m_blockVersion == (ushort)257 ? (ushort)8 : (ushort)0;
                    ushort Anz_Datasets = Read16(2);
                    ushort offsetFirstBlock = (ushort)(4 + prmIdOffset);

                    m_Datasets = new Dataset[Anz_Datasets];

                    ushort offsetCurrentSubblock = offsetFirstBlock;

                    for (int i = 0; i < Anz_Datasets; i++)
                    {
                        ushort lengthCurrentSubblock = this.Read16(offsetCurrentSubblock);
                        m_Datasets[i] = new Dataset(
                            Data,
                            this.BlockOffset + offsetCurrentSubblock,
                            lengthCurrentSubblock);
                        offsetCurrentSubblock += lengthCurrentSubblock;
                    }
                }

                foreach (Dataset ds in m_Datasets)
                {
                    yield return ds;
                }
            }
        }

        public byte[] GetCompleteDataBlock()
        {
            byte[] result = ReadByteblock(0, base.BlockLength);
            return result;
        }

        int IParameterDatenblock.BlockOffset
        {
            get { return base.BlockOffset; }
        }

        int IParameterDatenblock.BlockLength
        {
            get { return base.BlockLength; }
        }

        byte[] IParameterDatenblock.GetData()
        {
            return Data;
        }

        #endregion
    }
}