/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MultiDeployableIoSystemController.cs      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
#endregion

namespace PNConfigLib.HWCNBL.Tailor.AddressTailoring.Options
{
    /// <summary>
    /// Responsible for checkig the consistency of a controller of a multi deployable IoSystem.
    /// </summary>
    class MultiDeployableIoSystemController : IAddressTailoringConsistency
    {
        #region Construction

        /// <summary>
        /// Constructs a MultiDeployableIoSystem instance.
        /// </summary>
        /// <param name="ioControllerInerface">The Interface of the controller of the current IOSystem</param>
        public MultiDeployableIoSystemController(
            DataModel.PCLObjects.Interface ioControllerInerface)
        {
            m_IOControllerInterface = ioControllerInerface;
        }

        #endregion

        #region InterfaceOverrides

        /// <summary>
        /// Checks that the controller settings are correct in case of a multi deployable system,
        /// when Adress Tailoring is used.
        /// </summary>
        public void CheckConsistency()
        {
            DataModel.PCLObjects.Node node = m_IOControllerInterface.Node;

            if (node == null)
            {
                return;
            }

            object[] errorParameters = new object[1];
            errorParameters[0] = AttributeUtilities.GetName(m_IOControllerInterface.GetDevice());

            //check that the device name is set to be generated via other means (tailoring in this case)
            if (AttributeUtilities.IsIDevice(m_IOControllerInterface))
            {
                uint currOperatingMode = m_IOControllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoOperatingMode,
                    new AttributeAccessCode(),
                    0);
                if (currOperatingMode != (uint)PNIOOperatingModes.IOControllerAndIODevice)
                {
                    if (!node.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnPnNoSViaOtherPath,
                            new AttributeAccessCode(),
                            false))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            m_IOControllerInterface,
                            ConsistencyConstants.PNIpConfig_DeviceNameUsingDifferentMethod,
                            errorParameters);
                    }
                }
                else
                {
                    // if IDevice is in IODevice mode and doesn't have a SuperOrdinate IO-System, then the consistency check applies for it too.
                    PclObject iDeviceSuperOrdinateIOSystem = NavigationUtilities.GetIoSystem(m_IOControllerInterface);
                    if (iDeviceSuperOrdinateIOSystem == null)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            m_IOControllerInterface,
                            ConsistencyConstants.PNIpConfig_DeviceNameUsingDifferentMethod,
                            errorParameters);
                    }
                }
            }
            else
            {
                if (!node.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPnNoSViaOtherPath,
                        new AttributeAccessCode(),
                        false))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        m_IOControllerInterface,
                        ConsistencyConstants.PNIpConfig_DeviceNameUsingDifferentMethod,
                        errorParameters);
                }
            }

            //check that the IP Address is set to be obtained by a different method, Address Tailoring in this case
            bool pnIoIpSuiteViaOtherPath = node.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                new AttributeAccessCode(),
                false);
            bool pnPnIpSuiteViaOtherPath = node.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                new AttributeAccessCode(),
                false);
            if (!(pnIoIpSuiteViaOtherPath || pnPnIpSuiteViaOtherPath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOControllerInterface,
                    ConsistencyConstants.PNIpConfig_IPAddressUsingDifferentMethod,
                    errorParameters);

            }
            AttributeAccessCode ac = new AttributeAccessCode();
            //See that  we have the exchange without medium set
            if (m_IOControllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoExchangeWithoutMMC,
                    ac,
                    0) == 0)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOControllerInterface,
                    ConsistencyConstants.InterfaceOptions_DeviceReplacementWithoutExchangableMedium,
                    errorParameters);

            }

            if (!m_IOControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnAllowOverwriteNoIActive,
                    ac,
                    false))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    m_IOControllerInterface,
                    ConsistencyConstants.OverwriteProfinetDeviceName,
                    errorParameters);

            }
        }

        #endregion

        #region PrivateImplementation
        private DataModel.PCLObjects.Interface m_IOControllerInterface;
        #endregion

        #region Private functions
        #endregion
    }
}