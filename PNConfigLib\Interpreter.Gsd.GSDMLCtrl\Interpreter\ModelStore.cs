/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModelStore.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Diagnostics;
using PNConfigLib.Gsd.Interpreter.Common;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Specifies the possible models, which could be stored.
    /// </summary>
    internal enum StoreModels
    {
        /// <summary>
        /// Is used, if no model is stored within the actual store.
        /// </summary>
        NoModel = 0,
        /// <summary>
        /// Is used, if a structure data object model is stored by
        /// the actual store.
        /// </summary>
        Structure = 2,
        /// <summary>
        /// Is used, if a common data object model is stored by the 
        /// actual store.
        /// </summary>
        Common = 3
    }

    /// <summary>
    /// The ModelStore is an internal helper class for collecting the
    /// objects of the structure or common data object model. It is also
    /// used to make the respective data object model easily available
    /// from the Interpeter and to export the respective data object model.
    /// </summary>
    internal class ModelStore
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class. 
        /// </summary>
        public ModelStore()
        {
            // Initialize the properties
            Model = StoreModels.NoModel;
            Device = null;
            DeviceAccessPoints = GsdObjectDictionaryFactory.CreateDictionary();
            Modules = GsdObjectDictionaryFactory.CreateDictionary();
            VirtualSubmodules = GsdObjectDictionaryFactory.CreateDictionary();
            PhysicalSubmodules = GsdObjectDictionaryFactory.CreateDictionary();
            PluggablePortsubmodules = GsdObjectDictionaryFactory.CreateDictionary();
            RecordDataItems = new Hashtable();
            CommunicationInterfaceItems = GsdObjectDictionaryFactory.CreateDictionary();
        }

        #endregion

        //########################################################################################


        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the type of the model, which is stored in that store.
        /// </summary>
        public StoreModels Model { get; set; }

        /// <summary>
        /// Accesses the object which represents the device.
        /// </summary>
        public object Device { get; set; }

        /// <summary>
        /// Accesses the dictionary, which contains the device access points.
        /// </summary>
        public IGsdObjectDictionary DeviceAccessPoints { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the moduls.
        /// </summary>
        public IGsdObjectDictionary Modules { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the virtual submodules.
        /// </summary>
        public IGsdObjectDictionary VirtualSubmodules { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the physical submodules
        /// (submodules which could be plugged from the catalog).
        /// </summary>
        public IGsdObjectDictionary PhysicalSubmodules { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the pluggable port
        /// submodules (ports which could be plugged from the catalog).
        /// </summary>
        public IGsdObjectDictionary PluggablePortsubmodules { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the RecordDataItems.
        /// </summary>
        public Hashtable RecordDataItems { get; }

        /// <summary>
        /// Accesses the dictionary, which contains the CommunicationInterfaceItems.
        /// </summary>
        public IGsdObjectDictionary CommunicationInterfaceItems { get; }
        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Serializes the objects of the stored data object model with enclosing 
        /// element to a fixed XML structure. 
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool Serialize(ref System.Xml.XmlTextWriter writer)
        {
            bool succeeded = true;

            try
            {
                // Write correct model.
                string gsdmodel = Export.s_ElementCommonModel;
                if (this.Model == StoreModels.Structure)
                    gsdmodel = Export.s_ElementStructureModel;

                // Object begin.
                writer.WriteStartElement(gsdmodel);

                // ----------------------------------------------
                succeeded = this.SerializeMembers(ref writer);
                if (!succeeded)
                    throw new SerializationException("Store couldn't serialize members!");

                // Object end.
                writer.WriteEndElement();

            }
            catch (ArgumentException)
            {
                succeeded = false;
            }

            catch (SerializationException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the objects of the stored data object model without 
        /// any enclosing element to a fixed XML structure. 
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool SerializeMembers(ref System.Xml.XmlTextWriter writer)
        {
            bool succeeded = true;
            Common.SerializeOptions option = Common.SerializeOptions.Export;

            try
            {
                // ----------------------------------------------
                // RecordDataItems
                string gsdcollectionname = Export.s_CollNameRecordDataItems;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameModuleStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, this.RecordDataItems);

                // ----------------------------------------------
                // CommunicationInterfaceItems
                gsdcollectionname = Export.s_CollNameCommunicationInterfaceItems;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameModuleStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, CommunicationInterfaceItems.CastToNonGenericDictionary());

                // ----------------------------------------------
                // PhysicalSubmodules  ---  NOTE: Not available at the moment
                gsdcollectionname = Export.s_CollNamePhysicalSubmodules;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameSubmoduleStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, PhysicalSubmodules.CastToNonGenericDictionary());

                // ----------------------------------------------
                // VirtualSubmodules  ---  NOTE: Only for the Common - data object model relevant
                gsdcollectionname = Export.s_CollNameVirtualSubmodules;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameSubmoduleStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, VirtualSubmodules.CastToNonGenericDictionary());

                // ----------------------------------------------
                // Modules
                gsdcollectionname = Export.s_CollNameModules;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameModuleStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, Modules.CastToNonGenericDictionary());

                // ----------------------------------------------
                // DeviceAccessPoints
                gsdcollectionname = Export.s_CollNameDeviceAccessPoints;
                if (this.Model == StoreModels.Structure)
                    gsdcollectionname = Export.s_CollNameDeviceAccessPointStructureElements;

                Export.WriteDictionaryList(option, ref writer, gsdcollectionname, DeviceAccessPoints.CastToNonGenericDictionary());

                // ----------------------------------------------
                // Device
                succeeded = ((Common.GsdObject)(this.Device)).Serialize(option, ref writer);
                if (!succeeded)
                    throw new SerializationException("Couldn't serialize Device!");

            }
            catch (ArgumentException)
            {
                succeeded = false;
            }

            catch (SerializationException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

    }
}


