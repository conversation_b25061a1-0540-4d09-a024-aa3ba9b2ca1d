/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPAddressBase.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;
using System.Globalization;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Networks.Ethernet;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Network
{
    /// <summary>
    /// Classes of ip addresses
    /// </summary>
    public enum IPAddressBaseAddressClasses
    {
        ClassA,

        ClassB,

        ClassC,

        ClassDorE
    }

    /// <summary>
    /// Summary description for IPAddressBase.
    /// </summary>
    public class IPAddressBase
    {
        private const string s_NotValid = "Not Valid";
        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class
        /// <summary>
        /// This static method parses a string and returns the contents an int64. No format exception will be thrown in case of
        /// malformed strings.
        /// This method checks for formatting and numerical errors, additional checking has to be done in derived classes.
        /// </summary>
        /// <param name="s">An IP address in the xx.xx.xx.xx format.</param>
        /// <returns>An Int64 with the ip address in the lower 4 byte or -1 in case of an error.</returns>
        private static long ParseStringInternal(string s)
        {
            string[] split = s.Split(".".ToCharArray());
            if (split.Length != 4)
            {
                return 0;
            }
            long result = 0;
            for (int i = 0; i < split.Length; i++)
            {
                byte b;
                // no special characters or spaces are allowed, just figures
                bool parseResult = byte.TryParse(split[i], NumberStyles.None, s_FormatProvider, out b);
                if (!parseResult)
                {
                    Debug.WriteLine("Warning: Cannot convert \"" + split[i] + "\" to byte");
                    return -1;
                }
                long shifted = b;
                shifted = shifted << (8 * (3 - i));
                result = result | shifted;
            }
            return result;
        }

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private long m_IPAddress; // Address

        private bool m_IsInitialized; //Has an address ever been assigned to the object

        private string m_OwnAttributeName; // Name of the Attribute containing the address.

        private static IFormatProvider s_FormatProvider = CultureInfo.InvariantCulture;

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// Error Code returned from parsing
        /// Has NoError value if parsing is successful
        /// </summary>
        public IPAddressErrorCodes ErrorCode { get; set; }

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// address as Int64 value
        /// </summary>
        public long AsInt64
        {
            get { return m_IPAddress; }
            internal set
            {
                m_IPAddress = value;
                m_IsInitialized = true;
            }
        }

        /// <summary>
        /// address as a string, if no address has been assigned yet, the property returns “Not Valid” info string instead of any
        /// address.
        /// </summary>
        public string AsString
        {
            get
            {
                string str = s_NotValid;
                if (m_IsInitialized)
                {
                    str = ((byte)(m_IPAddress >> 24)).ToString(s_FormatProvider) + '.'
                          + ((byte)(m_IPAddress >> 16)).ToString(s_FormatProvider) + '.'
                          + ((byte)(m_IPAddress >> 8)).ToString(s_FormatProvider) + '.'
                          + ((byte)(m_IPAddress >> 0)).ToString(s_FormatProvider);
                }

                return str;
            }
        }
        /// <summary>
        /// State of the object (read only), returns true if any address has been ever assigned to the object
        /// </summary>
        internal bool IsInitialized
        {
            get
            {
                return m_IsInitialized;
            }
            set
            {
                m_IsInitialized = value;
            }
        }

        /// <summary>
        ///  Name of the Attribute containing the address.
        /// </summary>
        protected string OwnAttributeName
        {
            private get { return m_OwnAttributeName; }
            set { m_OwnAttributeName = value; }
        }

        /// <summary>
        /// Class of IP address
        /// </summary>
        internal IPAddressBaseAddressClasses AddressClass
        {
            get
            {
                int highByte = (int)(AsInt64 >> 24);

                if (highByte >= 224) //ip address class D or E
                {
                    return IPAddressBaseAddressClasses.ClassDorE;
                }

                if (highByte >= 192) //ip address class C
                {
                    return IPAddressBaseAddressClasses.ClassC;
                }

                if (highByte >= 128) //ip address class B
                {
                    return IPAddressBaseAddressClasses.ClassB;
                }

                return IPAddressBaseAddressClasses.ClassA;
            }
        }

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// Constructor
        /// </summary>
        public IPAddressBase()
        {
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as Int64</param>
        protected IPAddressBase(long address)
        {
            AsInt64 = address;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as string</param>
        /// <param name="node"></param>
        protected IPAddressBase(string address, DataModel.PCLObjects.Node node)
        {
            if (node == null)
            {
                throw new ArgumentNullException(nameof(node));
            }

            IPAddressBase tmpAddress = ParseString<IPAddressBase>(address);
            if (ErrorCode == IPAddressErrorCodes.None)
            {
                AsInt64 = tmpAddress.AsInt64;
            }
            else
            {
                UtilityNodeIe utility = new UtilityNodeIe(node.NodeBusinessLogic as NodeIeBusinessLogic);
                utility.SetCheckConsistencyMessage(this, ErrorCode, address);
            }
        }


        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        /// Parses a string and returns object derivated from IPAdressBase (IPAddress, IPDefaultRouterAddress or IPSubnetMask)
        /// described by the string. Does not throw exceptions in case of malformed strings or invalid IP addresses.
        /// </summary>
        /// <param name="s">A string containing the IP address in the xx.xx.xx.xx format.</param>
        /// <returns>A new object derivated from IPAdressBase object containing the IP address or null in case of errors.</returns>
        internal T ParseString<T>(string s) where T : IPAddressBase, new()
        {
            ErrorCode = IPAddressErrorCodes.None;
            long address = ParseStringInternal(s);
            if (address == -1)
            {
                ErrorCode = IPAddressErrorCodes.WrongAddress;
                return null;
            }
            IPAddressBase result = new T();
            result.AsInt64 = address;
            return (T)result;
        }

        /// <summary>
        /// Return address as byte array.
        /// </summary>
        /// <returns>address as byte array</returns>
        public byte[] ToByteArray()
        {
            Debug.Assert(m_IPAddress >> 32 == 0, "IP-address is invalid", "Property Get IPAddress.AsByteArray");

            byte[] id = new byte[4];

            id[0] = (byte)(m_IPAddress >> 24);
            id[1] = (byte)(m_IPAddress >> 16);
            id[2] = (byte)(m_IPAddress >> 8);
            id[3] = (byte)m_IPAddress;

            return id;
        }

        /// <summary>
        /// Assign address from byte array.
        /// </summary>
        /// <param name="ipAddress">Address as byte array</param>
        public virtual void FromByteArray(byte[] ipAddress)
        {
            if (ipAddress == null)
            {
                throw new ArgumentNullException(nameof(ipAddress));
            }

            Debug.Assert(ipAddress.Length == 4, "IP-address should be 4 byte!!", "Method IPAddress.FromByteArray()");
            if (ipAddress.Length == 4)
            {
                m_IPAddress = ipAddress[3] + (ipAddress[2] << 8) + (ipAddress[1] << 16) + ((long)ipAddress[0] << 24);
                m_IsInitialized = true;
            }
        }

        /// <summary>
        /// Read the address directly from node.
        /// </summary>
        /// <param name="node">Node to read the address from.</param>
        /// <returns>0 if succeeded; -1 if failed</returns>
        protected int ReadFromNode(DataModel.PCLObjects.Node node)
        {
            return ReadFromAttribute(node, OwnAttributeName);
        }

        /// <summary>
        /// Write the address directly to the node.
        /// </summary>
        /// <param name="node">Node to write the address to</param>
        /// <returns>0 if succeeded; -1 if failed</returns>
        public int WriteToNode(PclObject node)
        {
            return WriteToAttribute(node, OwnAttributeName);
        }

        /// <summary>
        /// Compare the address to the nodes address.
        /// </summary>
        /// <param name="node">Node to compare with</param>
        /// <returns>true, if equal</returns>
        public bool IsEqual(PclObject node)
        {
            if (node == null)
            {
                throw new ArgumentNullException(nameof(node));
            }

            long oldAddress = node.AttributeAccess.GetAnyAttribute<long>(OwnAttributeName, new AttributeAccessCode(), 0);
            return oldAddress == AsInt64;
        }

        /// <summary>
        /// Read the address from the attribute.
        /// </summary>
        /// <param name="node">Node to read the address from</param>
        /// <param name="attributeName">name of the attribute</param>
        /// <returns>0 if succeeded; -1 if failed</returns>
        private int ReadFromAttribute(DataModel.PCLObjects.Node node, string attributeName)
        {
            if (node == null)
            {
                throw new ArgumentNullException(nameof(node));
            }

            if (attributeName == null)
            {
                Debug.Assert(false, "No attribute to read from.");
                return -1;
            }
            AttributeAccessCode code = new AttributeAccessCode();
            long ipAddress = node.AttributeAccess.GetAnyAttribute<long>(attributeName, code, 0);
            if (!code.IsOkay
                || (ipAddress == 0))
            {
                return -1;
            }

            AsInt64 = ipAddress;

            return 0;
        }

        /// <summary>
        /// Write the address to the attribute.
        /// </summary>
        /// <param name="node">Node to write the address to</param>
        /// <param name="attributeName">name of the attribute</param>
        /// <returns>0 if succeeded; -1 if failed</returns>
        public int WriteToAttribute(PclObject node, string attributeName)
        {
            if (node == null)
            {
                throw new ArgumentNullException(nameof(node));
            }

            if (attributeName == null)
            {
                Debug.Assert(false, "No attribute to write to.");
                return -1;
            }
            AttributeAccessCode code = node.AttributeAccess.SetAnyAttribute<long>(attributeName, AsInt64);
            if (code.IsOkay)
            {
                return 0;
            }
            return -1;
        }

        /// <summary>
        /// </summary>
        /// <returns></returns>
        internal IPAddressErrorCodes CheckFormat()
        {
            if (AsInt64 == 0)
            {
                return IPAddressErrorCodes.EmptyAddress;
            }
            if (AsInt64 >> 32 != 0)
            {
                return IPAddressErrorCodes.AddressTooLong; //ip address may be only 32 bits long
            }
            return IPAddressErrorCodes.None;
        }

        /// <summary>
        /// Validate the ip address
        /// </summary>
        /// <returns>IPAddressErrorCodes error code</returns>
        public virtual IPAddressErrorCodes Check()
        {
            IPAddressErrorCodes errorCode = CheckFormat();
            if (errorCode != IPAddressErrorCodes.None)
            {
                return errorCode;
            }

            // check the address family
            if (AsInt64 == 0xFFFFFFFF)
            {
                return IPAddressErrorCodes.BroadcastAddress; //  broadcast address is not allowed
            }
            // Loopback address
            // The most commonly used IP address on the loopback device is 127.0.0.1 for IPv4, 
            // although any address in the range ********* to *************** is mapped to it
            if (AsInt64 >> 24 == 127)
            {
                return IPAddressErrorCodes.LoopbackAddress;
            }
            return IPAddressErrorCodes.None;
        }

        #endregion
    }
}