/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_041.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml.XPath;
using System.Collections;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02041 :
        BuilderV0204
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02041()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version241);
        }

        #endregion


        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectCertificationInfo:
                        {
                            this.PrepareCertificationInfo(nav, ref hash);
                            obj = new C.CertificationInfo();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldCertificationInfo, null);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_CertificationInfo, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                object obj = CreateGsdObject(Models.s_ObjectCertificationInfo, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCertificationInfo + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCertificationInfo] = obj;
            }
        }

        protected virtual void PrepareCertificationInfo(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSecurityClass, null);

            ArrayList listSecurityClasses = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SecurityClass, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsSecurityClassEnumValueConvertable(type))
                    {
                        listSecurityClasses.Add(Enums.ConvertSecurityClassEnum(type));
                    }
                }
                hash[Models.s_FieldSecurityClass] = listSecurityClasses;
            }
        }

        #endregion

        #endregion

    }
}