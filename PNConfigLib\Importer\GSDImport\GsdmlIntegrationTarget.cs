/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: GsdmlIntegrationTarget.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Globalization;
using System.IO;

namespace PNConfigLib.GSDImport
{
    class GsdmlIntegrationTarget
    {
        //########################################################################################
        #region Constants and Enums
        // Contains all constants and enums
        private const int s_DateLength = 8;
        private const int s_TimeLength = 6;
        #endregion

        //########################################################################################
        #region Fields
        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        public FileInfo SourceFile
        {
            get; private set;
        }
        #endregion

        //########################################################################################
        #region Construction/Destruction/Initialisation
        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public GsdmlIntegrationTarget(FileInfo sourceFile)
        {
            SourceFile = sourceFile;
        }
        #endregion

        //########################################################################################
        #region Overrides and Overridables
        // Contains all public and protected overrides as well as overridables of the class
        public string Revision
        {
            get
            {
                return GetRevision(SourceFile.Name).ToString(CultureInfo.InvariantCulture);
            }
        }

        public string Version
        {
            get
            {
                return GetGsdRevision(SourceFile.Name);
            }
        }

        #endregion

        //########################################################################################
        #region Private Implementation
        // Contains the private implementation of the class
        private static string GetGsdRevision(string path)
        {
            string filename = Path.GetFileNameWithoutExtension(path);
            string gsdRevision = String.Empty;

            if (filename != null && filename.IndexOf('-') != -1)
            {
                string s = filename.Substring(filename.IndexOf("-", StringComparison.Ordinal) + 1);

                if (s.IndexOf('-') != -1)
                {
                    gsdRevision = (s.Substring(0, s.IndexOf("-", StringComparison.Ordinal))).ToUpperInvariant();
                }
            }

            return gsdRevision;
        }

        private DateTime GetRevision(string path)
        {
            string datestring = "00000000";

            string filename = Path.GetFileNameWithoutExtension(path);
            if (filename.ToUpperInvariant().Contains("-TEXT-"))
            {
                filename = filename.Substring(0, filename.Length - 8);
            }

            char[] seps = { '-' };
            string[] stringparts = filename.Split(seps);

            if (stringparts[stringparts.Length - 1].Length == s_DateLength)
            {
                datestring = stringparts[stringparts.Length - 1];

            }
            else if (stringparts[stringparts.Length - 1].Length == s_TimeLength)
            {
                datestring = string.Format(
                    CultureInfo.InvariantCulture,
                    "{0}-{1}",
                    stringparts[stringparts.Length - 2],
                    stringparts[stringparts.Length - 1]);
            }

            DateTime date;
            string[] formats = { "yyyyMMdd-hhmmss", "yyyyMMdd" };
            if (!DateTime.TryParseExact(datestring, formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
            {
                date = new DateTime();
            }

            return date;
        }
        #endregion

    }
}
