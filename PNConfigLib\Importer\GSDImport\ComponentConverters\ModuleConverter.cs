/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleConverter.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.GSDImport.Helper;

using PNConfigLib.Gsd.Interpreter.Common;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal sealed class ModuleConverter : ModuleBaseConverter
    {
        private Module m_Module;

        private ModuleCatalog m_ModuleCatalog;

        protected override string GsdId => m_Module.GsdID;
        public ModuleConverter(Module moduleItem)
        {
            m_Module = moduleItem;
        }

        public ModuleCatalog Convert(string fileName)
        {
            SubslotHelper = new SubslotHelper();
            if (m_Module.VirtualSubmodules != null)
            {
                foreach (VirtualSubmodule submodule in m_Module.VirtualSubmodules)
                {
                    ModulePlugData plugData = m_Module.GetSubmodulePlugData(submodule.GsdID);
                    if (plugData == null)
                    {
                        plugData = submodule.PlugData;
                    }
                    SubslotHelper.AddVirtualSubmodulePlugData(submodule.GsdID, plugData);
                    SubslotHelper.AddVirtualSubmodule(submodule.GsdID, submodule);
                }
            }
            Array physicalSubslots = m_Module.PhysicalSubslots;
            List<uint> physicalSubslotsTmp = new List<uint>();
            List<uint> sdsSubslotsTmp = new List<uint>();

            if (physicalSubslots != null)
            {
                foreach (uint subslot in physicalSubslots)
                {
                    if (subslot < 0x8000)
                    {
                        physicalSubslotsTmp.Add(subslot);
                    }
                    else
                    {
                        sdsSubslotsTmp.Add(subslot);
                    }
                }
            }
            SubslotHelper.SystemDefinedSubslots.AddRange(sdsSubslotsTmp);
            SubslotHelper.AddAllPhysicalSubslots(physicalSubslotsTmp);

            if (m_Module.PhysicalSubmodules != null)
            {
                foreach (ModuleObject submodule in m_Module.PhysicalSubmodules)
                {
                    ModulePlugData plugData = m_Module.GetSubmodulePlugData(submodule.GsdID);
                    SubslotHelper.AddPhysicalSubmodulePlugData(submodule.GsdID, plugData);
                    SubslotHelper.AddPhysicalSubmodule(submodule.GsdID, submodule);
                }
            }

            if (m_Module.Subslots != null)
            {
                foreach (Subslot subslot in m_Module.Subslots)
                {
                    SubslotHelper.AddSubslotDescription(subslot);
                }
            }

            if (m_Module.HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDPort))
        {
                Array submoduleList = m_Module.GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDPort);
                foreach (SystemDefinedSubmoduleObject port in submoduleList)
                {
                    SubslotHelper.AddSystemDefinedSubmodule(port);
                }
            }

            m_ModuleCatalog = new ModuleCatalog();
            m_ModuleCatalog.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GSDFileName, fileName);
            m_ModuleCatalog.AvailableSubSlots = m_Module.Subslots;

            AddAttributes();
            return m_ModuleCatalog;
        }
        private void AddAttributes()
        {
            AddCatalogInfoVariables();
            AddPNModuleIdentNumberAttribute();

        }
        private void AddCatalogInfoVariables()
        {
            if (m_Module.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo moduleInfo)
            {
                DefineInfoVariables(moduleInfo, m_ModuleCatalog);
            }
        }

        private void AddPNModuleIdentNumberAttribute()
        {
            m_ModuleCatalog.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnModuleIdentNumber,
                m_Module.IdentNumber);
        }
    }
}