/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: LAddress.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.LAddresses
{
    /// <summary>
    /// This class contains the properties related to an items LAddress (or HWIdentifier).
    /// </summary>
    /// <remarks>
    /// These properties include the numerical value and type of the LAddress.
    /// </remarks>
    internal class LAddress
    {
        /// <summary>
        /// Type of LAddress.
        /// </summary>
        public readonly LAddressType LAddressType;

        /// <summary>
        /// Value of LAddress.
        /// </summary>
        public readonly long LAddressValue;

        /// <summary>
        /// Constructor for LAddress.
        /// </summary>
        /// <param name="lAddress">Value of LAddress.</param>
        /// <param name="lAddressType">Type of LAddress.</param>
        public LAddress(long lAddress, LAddressType lAddressType)
        {
            LAddressValue = lAddress;
            this.LAddressType = lAddressType;
        }
    }
}