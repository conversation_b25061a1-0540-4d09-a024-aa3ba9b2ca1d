/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: GsdReferenceChecker.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class GsdReferenceChecker : IConsistencyChecker
    {
        private List<DecentralDeviceType> m_DecentralDevices;

        private readonly PNConfigLib.ConfigReader.ListOfNodes.ListOfNodes m_ListOfNodes;

        
        internal GsdReferenceChecker(List<DecentralDeviceType> decentralDevices, ConfigReader.ListOfNodes.ListOfNodes lon)
        {
            m_DecentralDevices = decentralDevices;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            foreach (ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice in m_ListOfNodes.DecentralDevice)
            {
                string gsdFileName = Path.GetFileName(lonDecentralDevice.GSDPath);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDecentralDevice.GSDRefID);

                DecentralDeviceType decentralDevice =
                    m_DecentralDevices.SingleOrDefault(e => e.DeviceRefID == lonDecentralDevice.DeviceID);
                if (decentralDevice == null)
                {
                    // This device is added in list of nodes, but not used in configuration.
                    continue;
                }

                AreNumberOfPortsValidAccorfingToGsd(decentralDeviceCatalog, decentralDevice);

                ArePortGsdRefIdsValid(decentralDeviceCatalog, decentralDevice);

                CheckModuleGsdIds(decentralDevice, decentralDeviceCatalog, gsdFileName, lonDecentralDevice.GSDRefID);
            }
        }

        private void AreNumberOfPortsValidAccorfingToGsd(DecentralDeviceCatalog decentralDeviceCatalog, DecentralDeviceType decentralDevice)
        {
            PNConfigLib.ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(decentralDevice.DeviceRefID, m_ListOfNodes);
            string gsdFileName = Path.GetFileName(lonDecentralDevice.GSDPath);

            Dictionary<int, PortCatalog> portCatalogValues = PortConfigurator.FillPortCatalogValues(decentralDeviceCatalog, gsdFileName);
            if (decentralDeviceCatalog.Interface.PortList.Count != 0
                && decentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports.Count
                > decentralDeviceCatalog.Interface.PortList.Count + portCatalogValues.Count)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NumberOfPortsExceeded,
                    decentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void ArePortGsdRefIdsValid(DecentralDeviceCatalog decentralDeviceCatalog, DecentralDeviceType decentralDevice)
        {
            foreach (PortType decentralDevicePort in decentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports)
            {
                if (string.IsNullOrEmpty(decentralDevicePort.GSDRefID))
                {
                    continue;
                }

                if (!decentralDeviceCatalog.PluggableSubmoduleList.ContainsKey(decentralDevicePort.GSDRefID)
                    && !decentralDeviceCatalog.SystemDefinedSubmoduleList.Exists(
                        m => m.GsdID == decentralDevicePort.GSDRefID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IncorrectGSDRefIDPort,
                        decentralDevicePort.GSDRefID,
                        decentralDevice.DeviceRefID,
                        decentralDevicePort.PortNumber);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckModuleGsdIds(DecentralDeviceType decentralDevice, DecentralDeviceCatalog decentralDeviceCatalog, string gsdFileName, string deviceGsdId)
        {
            List<int> usedSlots = new List<int>();
            HashSet<string> usedModuleIds = new HashSet<string>();

            foreach (ModuleType module in decentralDevice.Module)
            {
                HashSet<string> usedSubmoduleIds = new HashSet<string>();

                IsModuleIdUsedMoreThanOnce(module.ModuleID, usedModuleIds, decentralDevice.DeviceRefID);

                string moduleGsdKey = CatalogHelper.GetGsdKeyByGsdName(gsdFileName, module.GSDRefID);

                IsModuleGsdRefIdCorrect(module.GSDRefID, module.ModuleID, decentralDevice.DeviceRefID, moduleGsdKey);

                // Checking module is pluggable for device
                if (module.GSDRefID != string.Empty)
                {
                    IsModulePluggableForDevice(
                        deviceGsdId,
                        module.GSDRefID,
                        decentralDeviceCatalog,
                        decentralDevice.DeviceRefID,
                        module.ModuleID);

                    IsSlotNumberUsedMoreThanOneForModule(usedSlots, module.SlotNumber);

                    IsSlotAvailableForModule(
                        decentralDeviceCatalog,
                        module.GSDRefID,
                        module.SlotNumber,
                        module.ModuleID);
                }
                else
                // The used-in module is unplugged by introducing the module with an empty GSDRefId (""). Check if its slot number is valid.
                {
                    CheckIfUsedInSlotNumberIsValid(decentralDeviceCatalog, module);
                }

                List<int> usedSubslots = new List<int>();
                CheckSubmoduleGsdIds(module, decentralDevice.DeviceRefID, gsdFileName, usedSubmoduleIds, usedSubslots);

                CheckPortGsdIds(module.Port, gsdFileName, moduleGsdKey, usedSubslots);
            }
        }

        private void CheckPortGsdIds(List<PortType> ports, string gsdFileName, string moduleGsdKey, List<int> usedSubslots)
        {
            foreach (PortType port in ports)
            {
                string portGSDRefId = CatalogHelper.GetGsdKeyByGsdName(gsdFileName, port.GSDRefID);

                IsPortOfModuleExist(portGSDRefId, port.GSDRefID);

                IsSubslotSuitableForPort(moduleGsdKey, port.GSDRefID, port.SubslotNumber);

                IsPortSubslotNumberUsedMultipleTimes(port.SubslotNumber, usedSubslots);
            }
        }

        private void IsPortOfModuleExist(string portGSDRefId, string gsdId)
        {
            if (!Catalog.PortList.ContainsKey(portGSDRefId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_PortForModuleDoesNotExist,
                    gsdId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSubslotSuitableForPort(string moduleGsdKey, string portGsdId, uint subslotNumber)
        {
            if (Catalog.ModuleList[moduleGsdKey].PluggableSubmoduleList.ContainsKey(portGsdId))
            {
                SlotRelation slotRelation = Catalog.ModuleList[moduleGsdKey]
                    .PluggableSubmoduleList[portGsdId];
                List<uint> allowedInSlots = slotRelation.AllowedInSlots?.OfType<uint>().ToList();
                List<uint> fixedInSlots = slotRelation.FixedInSlots?.OfType<uint>().ToList();
                List<uint> usedInSlots = slotRelation.UsedInSlots?.OfType<uint>().ToList();
                if (!((((allowedInSlots != null) && allowedInSlots.Contains(subslotNumber)))
                      || ((fixedInSlots != null) && fixedInSlots.Contains(subslotNumber))
                      || ((usedInSlots != null) && usedInSlots.Contains(subslotNumber))))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SubslotNumberForPortNotSuitable,
                        portGsdId);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsPortSubslotNumberUsedMultipleTimes(int subslotNumber, List<int> usedSubslots)
        {
            if (usedSubslots.Contains(subslotNumber))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SubslotNumberUsedMultipleTimesForPort,
                    subslotNumber);
                throw new ConsistencyCheckException();
            }
            usedSubslots.Add(subslotNumber);
        }

        private void CheckSubmoduleGsdIds(
            ModuleType module,
            string deviceId,
            string gsdFileName,
            HashSet<string> usedSubmoduleIds,
            List<int> usedSubslots)
        {
            foreach (ModuleTypeSubmodule submodule in module.Submodule)
            {
                IsSubmoduleIdUsedMoreThanOnce(submodule.SubmoduleID, module.ModuleID, deviceId, usedSubmoduleIds);

                string submoduleGsdKey = CatalogHelper.GetGsdKeyByGsdName(gsdFileName, submodule.GSDRefID);

                IsGsdRefIdValidForSubmodule(
                    submoduleGsdKey,
                    submodule.GSDRefID,
                    deviceId,
                    module.ModuleID,
                    submodule.SubmoduleID);

                IsSubslotNumberUsedMoreThanOnce(usedSubslots, submodule.SubslotNumber);

                IsSubslotAvailableForModule(gsdFileName, module.GSDRefID, module.ModuleID, submodule);
            }
        }

        private void IsSubslotNumberUsedMoreThanOnce(List<int> usedSubslots, int subslotNumber)
        {
            if (usedSubslots.Contains(subslotNumber))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SubslotNumberIsUsedByMoreThanOneModule,
                    subslotNumber);
                throw new ConsistencyCheckException();
            }
            usedSubslots.Add(subslotNumber);
        }

        private void IsSubslotAvailableForModule(string gsdFileName, string moduleGsdId, string moduleId, ModuleTypeSubmodule submodule)
        {
            string gsdPathAndRefId = CatalogHelper.GetGsdKeyByGsdName(gsdFileName, moduleGsdId);
            if (!Catalog.DeviceList.ContainsKey(gsdPathAndRefId))
            {
                if (Catalog.ModuleList[gsdPathAndRefId].PluggableSubmoduleList.ContainsKey(submodule.GSDRefID))
                {
                    SlotRelation slotRelation = Catalog.ModuleList[gsdPathAndRefId]
                        .PluggableSubmoduleList[submodule.GSDRefID];
                    List<uint> allowedInSlots = slotRelation.AllowedInSlots?.OfType<uint>().ToList();
                    List<uint> fixedInSlots = slotRelation.FixedInSlots?.OfType<uint>().ToList();
                    List<uint> usedInSlots = slotRelation.UsedInSlots?.OfType<uint>().ToList();
                    if (!((allowedInSlots != null && allowedInSlots.Contains(submodule.SubslotNumber))
                          || (fixedInSlots != null && fixedInSlots.Contains(submodule.SubslotNumber))
                          || (usedInSlots != null && usedInSlots.Contains(submodule.SubslotNumber))))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SubslotIsNotAvailableForModule,
                            submodule.SubslotNumber,
                            submodule.SubmoduleID,
                            moduleId);
                        throw new ConsistencyCheckException();
                    }
                }
                else
                {
                    CheckVirtualSubmoduleSubslotNumber(gsdPathAndRefId, submodule);

                    if (!Catalog.ModuleList.ContainsKey(gsdPathAndRefId))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SubslotIsNotAvailableForModule,
                            submodule.SubslotNumber,
                            submodule.SubmoduleID,
                            moduleId);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void IsGsdRefIdValidForSubmodule(string submoduleGsdKey, string gsdId, string deviceId, string moduleId, string submoduleId)
        {
            bool isPort = Catalog.PortList.ContainsKey(submoduleGsdKey);
            bool isSubmodule = Catalog.SubmoduleList.ContainsKey(submoduleGsdKey);

            if (!(isPort || isSubmodule))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IncorrectGSDRefIDSubmodule,
                    gsdId,
                    deviceId,
                    moduleId,
                    submoduleId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSubmoduleIdUsedMoreThanOnce(string submoduleId, string moduleId, string deviceId, HashSet<string> usedSubmoduleIds)
        {
            if (usedSubmoduleIds.Contains(submoduleId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SubmoduleIdUsedMoreThanOnce,
                    submoduleId,
                    moduleId,
                    deviceId);
                throw new ConsistencyCheckException();
            }
            usedSubmoduleIds.Add(submoduleId);
        }

        private static void CheckIfUsedInSlotNumberIsValid(
            DecentralDeviceCatalog decentralDeviceCatalog,
            ModuleType module)
        {
            bool slotNumberValid = false;
            foreach (var slotRelation in decentralDeviceCatalog.PluggableModuleList.Values)
            {
                if (slotRelation.UsedInSlots == null)
                {
                    continue;
                }

                foreach (var usedInSlot in slotRelation.UsedInSlots)
                {
                    if (usedInSlot.ToString() == module.SlotNumber.ToString(CultureInfo.InvariantCulture))
                    {
                        slotNumberValid = true;
                        break;
                    }
                }
            }

            if (!slotNumberValid)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SlotIsNotAvailableForRemovingModule,
                    module.ModuleID,
                    module.SlotNumber);
                throw new ConsistencyCheckException();
            }

        }

        private void IsModuleIdUsedMoreThanOnce(string moduleId, HashSet<string> usedModuleIds, string deviceId)
        {
            if (usedModuleIds.Contains(moduleId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ModuleIdUsedMoreThanOnce,
                    moduleId,
                    deviceId);
                throw new ConsistencyCheckException();
            }

            usedModuleIds.Add(moduleId);
        }

        private void CheckVirtualSubmoduleSubslotNumber(string moduleCatalogPath, ModuleTypeSubmodule xmlSubmodule)
        {
            if ((Catalog.ModuleList[moduleCatalogPath] != null)
                && (Catalog.ModuleList[moduleCatalogPath].VirtualSubmoduleList != null)
                && Catalog.ModuleList[moduleCatalogPath].VirtualSubmoduleList.Any(
                    vsm => vsm.AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.GsdId)
                           && (vsm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId].ToString()
                               == xmlSubmodule.GSDRefID)))
            {
                foreach (SubmoduleCatalog submoduleCatalog in Catalog.ModuleList[moduleCatalogPath].VirtualSubmoduleList
                    .FindAll(
                        vsm => vsm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId].ToString()
                               == xmlSubmodule.GSDRefID))
                {
                    uint subslotNumberCatalog = submoduleCatalog.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnSubslotNumber,
                        new AttributeAccessCode(),
                        1);
                    if (xmlSubmodule.SubslotNumber != subslotNumberCatalog)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_WrongSubslotNumberForVsm,
                            xmlSubmodule.SubslotNumber,
                            xmlSubmodule.SubmoduleID,
                            subslotNumberCatalog);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void IsModuleGsdRefIdCorrect(
            string moduleGsdRefId,
            string moduleId,
            string deviceId,
            string moduleGsdKey)
        {
            if (!string.IsNullOrEmpty(moduleGsdRefId)
                && !Catalog.ModuleList.ContainsKey(moduleGsdKey)
                && !Catalog.DeviceList.ContainsKey(moduleGsdKey))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IncorrectGSDRefIDModule,
                    moduleGsdRefId,
                    deviceId,
                    moduleId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsModulePluggableForDevice(
            string deviceGsdId,
            string moduleGsdId,
            DecentralDeviceCatalog deviceCatalog,
            string deviceId,
            string moduleId)
        {
            if (deviceGsdId != moduleGsdId
                && !deviceCatalog.PluggableModuleList.ContainsKey(moduleGsdId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ModuleIsNotPluggableForDevice,
                    moduleGsdId,
                    deviceId,
                    moduleId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSlotNumberUsedMoreThanOneForModule(List<int> usedSlots, int moduleSlotNumber)
        {
            if (usedSlots.Contains(moduleSlotNumber))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SlotNumberIsUsedByMoreThanOneModule,
                    moduleSlotNumber);
                throw new ConsistencyCheckException();
            }
            usedSlots.Add(moduleSlotNumber);
        }

        private void IsSlotAvailableForModule(
            DecentralDeviceCatalog decentralDeviceCatalog,
            string moduleGsdId,
            uint moduleSlotNumber,
            string moduleId)
        {
            if (decentralDeviceCatalog.PluggableModuleList.ContainsKey(moduleGsdId))
            {
                SlotRelation pluggableSlotsToCheck = decentralDeviceCatalog.PluggableModuleList[moduleGsdId];
                List<uint> allowedPluggable = pluggableSlotsToCheck.AllowedInSlots?.OfType<uint>().ToList();
                List<uint> fixedInPluggable = pluggableSlotsToCheck.FixedInSlots?.OfType<uint>().ToList();
                List<uint> usedInPluggable = pluggableSlotsToCheck.UsedInSlots?.OfType<uint>().ToList();
                if (!((allowedPluggable != null && allowedPluggable.Contains(moduleSlotNumber))
                      || (fixedInPluggable != null && fixedInPluggable.Contains(moduleSlotNumber))
                      || (usedInPluggable != null && usedInPluggable.Contains(moduleSlotNumber))))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SlotIsNotAvailableForModule,
                        moduleSlotNumber,
                        moduleId);
                    throw new ConsistencyCheckException();
                }
            }
        }
    }
}
