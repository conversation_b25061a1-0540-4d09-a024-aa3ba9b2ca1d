/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Enums.cs                                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all enum names and values of the GSDML specification.
    /// </summary>
    internal static class Enums
    {
        //########################################################################################
        #region GSDML V1.0 Enums
        // Contains all enum names and values as constants

        #region FamilyEnum

        /// <summary>name of the FamilyEnum type from the GSDML</summary>
        public const string s_FamilyEnum = @"FamilyEnum";
        /// <summary>string value 'General' for the FamilyEnum type</summary>
        public const string s_General = @"General";
        /// <summary>string value 'Drives' for the FamilyEnum type</summary>
        public const string s_Drives = @"Drives";
        /// <summary>string value 'Switching Devices' for the FamilyEnum type</summary>
        public const string s_SwitchingDevices = @"Switching Devices";
        /// <summary>string value 'I/O' for the FamilyEnum type</summary>
        public const string s_IO = @"I/O";
        /// <summary>string value 'Valves' for the FamilyEnum type</summary>
        public const string s_Valves = @"Valves";
        /// <summary>string value 'Controllers' for the FamilyEnum type</summary>
        public const string s_Controllers = @"Controllers";
        /// <summary>string value 'HMI' for the FamilyEnum type</summary>
        public const string s_Hmi = @"HMI";
        /// <summary>string value 'Encoders' for the FamilyEnum type</summary>
        public const string s_Encoders = @"Encoders";
        /// <summary>string value 'NC/RC' for the FamilyEnum type</summary>
        public const string s_Ncrc = @"NC/RC";
        /// <summary>string value 'Gateway' for the FamilyEnum type</summary>
        public const string s_Gateway = @"Gateway";
        /// <summary>string value 'PLCs' for the FamilyEnum type</summary>
        public const string s_PlCs = @"PLCs";
        /// <summary>string value 'Ident Systems' for the FamilyEnum type</summary>
        public const string s_IdentSystems = @"Ident Systems";
        /// <summary>string value 'PA Profiles' for the FamilyEnum type</summary>
        public const string s_PaProfiles = @"PA Profiles";
        /// <summary>string value 'Network Components' for the FamilyEnum type (new in V2.0)</summary>
        public const string s_NetworkComponents = @"Network Components";
        /// <summary>string value 'Sensors' for the FamilyEnum type (new in V2.0)</summary>
        public const string s_Sensors = @"Sensors";

        #endregion

        #region GraphicsTypeEnum
        /// <summary>name of the GraphicsTypeEnum type from the GSDML</summary>
        public const string s_GraphicsTypeEnum = @"GraphicsTypeEnum";
        /// <summary>string value 'DeviceSymbol' for the GraphicsTypeEnum type</summary>
        public const string s_DeviceSymbol = @"DeviceSymbol";
        /// <summary>string value 'DeviceIcon' for the GraphicsTypeEnum type</summary>
        public const string s_DeviceIcon = @"DeviceIcon";

        #endregion

        #region DataItemTypeEnum

        /// <summary>name of the DataItemTypeEnum type from the GSDML</summary>
        public const string s_DataItemTypeEnum = @"DataItemTypeEnum";
        /// <summary>string value 'Integer8' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Integer8 = @"Integer8";
        /// <summary>string value 'Integer16' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Integer16 = @"Integer16";
        /// <summary>string value 'Integer32' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Integer32 = @"Integer32";
        /// <summary>string value 'Integer64' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Integer64 = @"Integer64";
        /// <summary>string value 'Unsigned8' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Unsigned8 = @"Unsigned8";
        /// <summary>string value 'Unsigned16' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Unsigned16 = @"Unsigned16";
        /// <summary>string value 'Unsigned32' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Unsigned32 = @"Unsigned32";
        /// <summary>string value 'Unsigned64' for the DataItemTypeEnum and DataTypeEnum type</summary>
        public const string s_Unsigned64 = @"Unsigned64";
        /// <summary>string value 'Float32' for the DataItemTypeEnum type</summary>
        public const string s_Float32 = @"Float32";
        /// <summary>string value 'Float64' for the DataItemTypeEnum type</summary>
        public const string s_Float64 = @"Float64";

        /// <summary>string value 'Date' for the DataItemTypeEnum type</summary>
        public const string s_Date = @"Date";
        /// <summary>string value 'TimeOfDay with date indication' for the DataItemTypeEnum type</summary>
        public const string s_TimeOfDayWithDateIndication = @"TimeOfDay with date indication";
        /// <summary>string value 'TimeOfDay without date indication' for the DataItemTypeEnum type</summary>
        public const string s_TimeOfDayWithoutDateIndication = @"TimeOfDay without date indication";
        /// <summary>string value 'TimeDifference with date indication' for the DataItemTypeEnum type</summary>
        public const string s_TimeDifferenceWithDateIndication = @"TimeDifference with date indication";
        /// <summary>string value 'TimeDifference without date indication' for the DataItemTypeEnum type</summary>
        public const string s_TimeDifferenceWithoutDateIndication = @"TimeDifference without date indication";
        /// <summary>string value 'NetworkTime' for the DataItemTypeEnum type</summary>
        public const string s_NetworkTime = @"NetworkTime";
        /// <summary>string value 'NetworkTimeDifference' for the DataItemTypeEnum type</summary>
        public const string s_NetworkTimeDifference = @"NetworkTimeDifference";
        /// <summary>string value 'VisibleString' for the DataItemTypeEnum type</summary>
        public const string s_VisibleString = @"VisibleString";
        /// <summary>string value 'OctetString' for the DataItemTypeEnum type</summary>
        public const string s_OctetString = @"OctetString";
        /// <summary>string value 'Float32+Status8' for the DataItemTypeEnum type (new in V2.0)</summary>
        public const string s_Float32Status8 = @"Float32+Status8";
        /// <summary>string value 'F_MessageTrailer4Byte' for the DataItemTypeEnum type (new in V2.0)</summary>
        public const string s_FMessageTrailer4Byte = @"F_MessageTrailer4Byte";
        /// <summary>string value 'F_MessageTrailer5Byte' for the DataItemTypeEnum type (new in V2.0)</summary>
        public const string s_FMessageTrailer5Byte = @"F_MessageTrailer5Byte";
        public const string s_FMessageTrailer5ByteTypingError = @"F_MessageTrailer5Byte ";
        /// <summary>string value 'Float32+Unsigned8' for the DataItemTypeEnum type (new in V2.2)</summary>
        public const string s_Float32Unsigned8 = @"Float32+Unsigned8";
        /// <summary>string value 'Unsigned8+Unsigned8' for the DataItemTypeEnum type (new in V2.2)</summary>
        public const string s_Unsigned8Unsigned8 = @"Unsigned8+Unsigned8";
        /// <summary>string value 'Boolean' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Boolean = @"Boolean";
        /// <summary>string value 'UnicodeString8' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_UnicodeString8 = @"UnicodeString8";
        /// <summary>string value '61131_STRING' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_String61131 = @"61131_STRING";
        /// <summary>string value '61131_WSTRING' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Wstring61131 = @"61131_WSTRING";
        /// <summary>string value 'TimeStamp' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_TimeStamp = @"TimeStamp";
        /// <summary>string value 'TimeStampDifference' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_TimeStampDifference = @"TimeStampDifference";
        /// <summary>string value 'TimeStampDifferenceShort' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_TimeStampDifferenceShort = @"TimeStampDifferenceShort";
        /// <summary>string value 'OctetString2+Unsigned8' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_OctetString2Unsigned8 = @"OctetString2+Unsigned8";
        /// <summary>string value 'Unsigned16_S' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Unsigned16S = @"Unsigned16_S";
        /// <summary>string value 'Integer16_S' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Integer16S = @"Integer16_S";
        /// <summary>string value 'Unsigned8_S' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Unsigned8S = @"Unsigned8_S";
        /// <summary>string value 'OctetString_S' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_OctetStringS = @"OctetString_S";
        /// <summary>string value 'N2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_N2 = @"N2";
        /// <summary>string value 'N4' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_N4 = @"N4";
        /// <summary>string value 'V2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_V2 = @"V2";
        /// <summary>string value 'L2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_L2 = @"L2";
        /// <summary>string value 'T2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_T2 = @"T2";
        /// <summary>string value 'T4' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_T4 = @"T4";
        /// <summary>string value 'D2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_D2 = @"D2";
        /// <summary>string value 'E2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_E2 = @"E2";
        /// <summary>string value 'C4' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_C4 = @"C4";
        /// <summary>string value 'X2' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_X2 = @"X2";
        /// <summary>string value 'X4' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_X4 = @"X4";
        /// <summary>string value 'Unipolar2.16' for the DataItemTypeEnum type (new in V2.32)</summary>
        public const string s_Unipolar216 = @"Unipolar2.16";

        #endregion

        #region IODataConsistencyEnum
        /// <summary>name of the IODataConsistencyEnum type from the GSDML</summary>
        public const string s_IODataConsistencyEnum = @"IODataConsistencyEnum";
        /// <summary>string value 'Item consistency' for the IODataConsistencyEnum type</summary>
        public const string s_ItemConsistency = @"Item consistency";
        /// <summary>string value 'All items consistency' for the IODataConsistencyEnum type</summary>
        public const string s_AllItemsConsistency = @"All items consistency";

        #endregion

        #region DataTypeEnum

        /// <summary>name of the DataTypeEnum type from the GSDML</summary>
        public const string s_DataTypeEnum = @"DataTypeEnum";

        /// <summary>string value 'Bit' for the DataTypeEnum type</summary>
        public const string s_Bit = @"Bit";
        /// <summary>string value 'BitArea' for the DataTypeEnum type</summary>
        public const string s_BitArea = @"BitArea";

        // The values Integer8, 16, 32, 64 and Unsigned8, 16, 32, 64 are defined by
        // DataItemTypeEnum.

        #endregion

        #endregion

        //########################################################################################
        #region GSDML V2.0 Enums
        // Contains all enum names and values as constants

        #region SyncRoleEnum
        /// <summary>name of the SyncRoleEnum type from the GSDML</summary>
        public const string s_SyncRoleEnum = @"SyncRoleEnum";
        /// <summary>string value 'SyncMaster' for the SynchronisationRoleEnum type</summary>
        public const string s_SyncMaster = @"SyncMaster";
        /// <summary>string value 'SyncSlave' for the SynchronisationRoleEnum type</summary>
        public const string s_SyncSlave = @"SyncSlave";
        /// <summary>string value 'SyncMaster+SyncSlave' for the SynchronisationRoleEnum type</summary>
        public const string s_SyncMasterAndSyncSlave = @"SyncMaster+SyncSlave";

        public const string s_SyncMasterRedundant = @"SyncMasterRedundant";  // since GSDML V2.2

        public const string s_SyncMasterRedundantAndSyncSlave = @"SyncMasterRedundant+SyncSlave";  // since GSDML V2.2

        #endregion

        #region RTClassEnum
        /// <summary>name of the RTClassEnum type from the GSDML</summary>
        public const string s_RTClassEnum = @"RTClassEnum";
        /// <summary>string value 'Class1' for the RTClassEnum type</summary>
        public const string s_RtClass1 = @"Class1";
        /// <summary>string value 'Class2' for the RTClassEnum type</summary>
        public const string s_RtClass2 = @"Class2";
        /// <summary>string value 'Class3' for the RTClassEnum type</summary>
        public const string s_RtClass3 = @"Class3";

        #endregion

        #region MAUTypeEnum

        /// <summary>name of the MAUTypeEnum type from the GSDML</summary>
        public const string s_MauTypeEnum = @"MAUTypeEnum";
        /// <summary>string value '100BASETXFD' for the MAUTypeEnum type</summary>
        public const string s_Mau100Basetxfd = @"100BASETXFD";
        /// <summary>string value '100BASEFXFD' for the MAUTypeEnum type</summary>
        public const string s_Mau100Basefxfd = @"100BASEFXFD";
        /// <summary>string value '1000BASEXFD' for the MAUTypeEnum type</summary>
        public const string s_Mau1000Basexfd = @"1000BASEXFD";
        /// <summary>string value '1000BASELXFD' for the MAUTypeEnum type</summary>
        public const string s_Mau1000Baselxfd = @"1000BASELXFD";
        /// <summary>string value '1000BASESXFD' for the MAUTypeEnum type</summary>
        public const string s_Mau1000Basesxfd = @"1000BASESXFD";
        /// <summary>string value '1000BASETFD' for the MAUTypeEnum type</summary>
        public const string s_Mau1000Basetfd = @"1000BASETFD";
        /// <summary>string value '10GigBASEFX' for the MAUTypeEnum type</summary>
        public const string s_Mau10GigBasefx = @"10GigBASEFX";

        #endregion

        #region LinkStateDiagnosisCapabilityEnum

        /// <summary>name of the MAUTypeEnum type from the GSDML</summary>
        public const string s_LinkStateDiagnosisEnum = @"LinkStateDiagnosisEnum";
        /// <summary>string value 'Up' for the LinkStateDiagnosisEnum type</summary>
        public const string s_LsdcUp = @"Up";
        /// <summary>string value '100BASEFXFD' for the LinkStateDiagnosisEnum type</summary>
        public const string s_LsdcDown = @"Down";
        /// <summary>string value '1000BASEXFD' for the LinkStateDiagnosisEnum type</summary>
        public const string s_LsdcUpandDown = @"Up+Down";

        #endregion

        #region F_CheckEnum

        /// <summary>string value 'Check' for the F_CheckEnum from GSDML</summary>
        public const string s_Check = @"Check";
        /// <summary>string value 'NoCheck' for the F_CheckEnum from GSDML</summary>
        public const string s_NoCheck = @"NoCheck";

        /// <summary>concrete value 'Check' for the F_CheckEnum from GSDML</summary>
        public const byte s_CheckValue = 1;
        /// <summary>concrete value 'NoCheck' for the F_CheckEnum from GSDML</summary>
        public const byte s_NoCheckValue = 0;

        #endregion

        #region F_SIL_Enum

        /// <summary>string value 'SIL1' for the F_SIL_Enum from GSDML</summary>
        public const string s_Sil1 = @"SIL1";
        /// <summary>string value 'SIL2' for the F_SIL_Enum from GSDML</summary>
        public const string s_Sil2 = @"SIL2";
        /// <summary>string value 'SIL3' for the F_SIL_Enum from GSDML</summary>
        public const string s_Sil3 = @"SIL3";
        /// <summary>string value 'NoSIL' for the F_SIL_Enum from GSDML</summary>
        public const string s_NoSil = @"NoSIL";

        /// <summary>concrete value 'SIL1' for the F_SIL_Enum from GSDML</summary>
        public const ushort s_Sil1Value = 0;
        /// <summary>concrete value 'SIL2' for the F_SIL_Enum from GSDML</summary>
        public const ushort s_Sil2Value = 1;
        /// <summary>concrete value 'SIL3' for the F_SIL_Enum from GSDML</summary>
        public const ushort s_Sil3Value = 2;
        /// <summary>concrete value 'NoSIL' for the F_SIL_Enum from GSDML</summary>
        public const ushort s_NoSilValue = 3;

        #endregion

        #region F_CRC_Length

        /// <summary>string value '3-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const string s_ByteCrc3 = @"3-Byte-CRC";
        /// <summary>string value '2-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const string s_ByteCrc2 = @"2-Byte-CRC";
        /// <summary>string value '4-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const string s_ByteCrc4 = @"4-Byte-CRC";

        /// <summary>concrete value '3-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const ushort s_ByteCrc3Value = 0;
        /// <summary>concrete value '2-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const ushort s_ByteCrc2Value = 1;
        /// <summary>concrete value '4-Byte-CRC' for the F_CRC_Length from GSDML</summary>
        public const ushort s_ByteCrc4Value = 2;

        #endregion

        #region MRSupportedRolesEnum

        /// <summary>string value 'Manager' for the MRSupportedRolesEnum type</summary>
        public const string s_MrManager = @"Manager";
        /// <summary>string value 'Manager (Auto)' for the MRSupportedRolesEnum type</summary>
        public const string s_MrManagerAuto1 = @"Manager(Auto)";
        public const string s_MrManagerAuto2 = @"Manager (Auto)";
        /// <summary>string value 'Client' for the MRSupportedRolesEnum type</summary>
        public const string s_MrClient = @"Client";
        /// <summary>string value 'Off' for the MRSupportedRolesEnum type</summary>
        public const string s_MrOff = @"Off";

        #endregion

        #endregion

        //########################################################################################
        #region GSDML V2.1 Enums
        // Contains all enum names and values as constants

        #region AddressAssignmentEnum

        /// <summary>string value 'DCP' for the AddressAssignmentEnum type</summary>
        public const string s_Dcp = @"DCP";
        /// <summary>string value 'DHCP' for the AddressAssignmentEnum type</summary>
        public const string s_Dhcp = @"DHCP";
        /// <summary>string value 'LOCAL' for the AddressAssignmentEnum type</summary>
        public const string s_Local = @"LOCAL";

        #endregion


        #region SupportedRT_ClassesEnum

        /// <summary>string value 'UDP' for the SupportedRT_ClassesEnum type</summary>
        public const string s_RtClassUdp = @"RT_CLASS_UDP";
        /// <summary>string value 'RT_CLASS_1' for the SupportedRT_ClassesEnum type</summary>
        public const string RT_CLASS_1 = @"RT_CLASS_1";
        /// <summary>string value 'RT_CLASS_2' for the SupportedRT_ClassesEnum type</summary>
        public const string RT_CLASS_2 = @"RT_CLASS_2";
        /// <summary>string value 'RT_CLASS_3' for the SupportedRT_ClassesEnum type</summary>
        public const string RT_CLASS_3 = @"RT_CLASS_3";

        #endregion


        #region SupportedProtocolsEnum

        /// <summary>string value 'LLDP' for the SupportedProtocolsEnum type</summary>
        public const string s_Lldp = @"LLDP";
        /// <summary>string value 'SNMP' for the SupportedProtocolsEnum type</summary>
        public const string s_Snmp = @"SNMP";


        #endregion


        #region SupportedSyncProtocolsEnum

        /// <summary>string value 'PTCP' for the SupportedProtocolsEnum type</summary>
        public const string s_Ptcp = @"PTCP";


        #endregion

        #endregion



        //########################################################################################
        #region GSDML V2.3 Enums

        #region FragmentationTypeEnum

        /// <summary>string value 'Dynamic' for the FragmentationTypeEnum type</summary>
        public const string s_Dynamic = "Dynamic";
        /// <summary>string value 'Static' for the FragmentationTypeEnum type</summary>
        public const string s_Static = "Static";

        #endregion

        #region RedundancyDeviceTypesEnum

        public const string s_S2 = @"S2";
        public const string s_R1 = @"R1";
        public const string s_R2 = @"R2";

        #endregion

        #endregion

        #region GSDML V2.31 Enums

        #region F_CRCSeed

        /// <summary>string value 'CRCSeed16' for the F_CRC_Seed Enum from GSDML</summary>
        public const string s_CrcSeed16 = @"CRC-Seed16";
        /// <summary>string value 'CRCSeed24/32' for the F_CRC_Seed Enum from GSDML</summary>
        public const string s_CrcSeed2432 = @"CRC-Seed24/32";

        /// <summary>concrete value 'CRCSeed16' for the F_CRC_Seed Enum from GSDML</summary>
        public const byte s_CrcSeed16Value = 0;
        /// <summary>concrete value 'CRCSeed24/32' for the F_CRC_Seed Enum from GSDML</summary>
        public const byte s_CrcSeed2432Value = 1;

        #endregion

        #region F_Passivation

        /// <summary>string value 'Device/Module' for the F_Passivation Enum from GSDML</summary>
        public const string s_DeviceModule = @"Device/Module";
        /// <summary>string value 'Channel' for the F_Passivation Enum from GSDML</summary>
        public const string s_Channel = @"Channel";

        /// <summary>concrete value 'Device/Module' for the F_Passivation Enum from GSDML</summary>
        public const byte s_DeviceModuleValue = 0;
        /// <summary>concrete value 'Channel' for the F_Passivation Enum from GSDML</summary>
        public const byte s_ChannelValue = 1;

        #endregion

        #endregion

        #region GSDML V2.33 Enums

        #region EntityClassEnum
        /// <summary>name of the RTClassEnum type from the GSDML</summary>
        public const string s_EntityClassEnum = @"EntityClassEnum";
        /// <summary>string value 'Class1' for the EntityClassEnum type</summary>
        public const string s_EntityClass1 = @"Class1";
        /// <summary>string value 'Class2' for the EntityClassEnum type</summary>
        public const string s_EntityClass2 = @"Class2";
        /// <summary>string value 'Class3' for the EntityClassEnum type</summary>
        public const string s_EntityClass3 = @"Class3";

        /// <summary>string value 'Subclass1' for the EntitySubclassEnum type</summary>
        public const string s_EntitySubclass1 = @"Subclass1";
        /// <summary>string value 'Class2' for the EntityClassEnum type</summary>
        public const string s_EntitySubclass2 = @"Subclass2";

        /// <summary>string value 'DigitalInput' for the Observer type</summary>
        public const string s_DigitalInput = @"DigitalInput";

        #endregion

        #endregion

        //########################################################################################

        #region GSDML V2.4 Enums

        #region TimeSynchronisationEnum

        public const string s_Master = "Master";
        public const string s_Slave = "Slave";

        #endregion

        #region SupportedFeaturesEnum

        public const string s_Tsn = "TSN";
        public const string s_TsnTas = "TSN_TAS";
        public const string s_TsnPreemption = "TSN_Preemption";

        #endregion

        #region SFPDiagnosis

        public const string s_SfpDiagThresholdBased = "ThresholdBased";
        public const string s_SfpDiagTxFault = "TxFault";
        public const string s_SfpDiagRxLoss = "RxLoss";

        #endregion

        #region ServiceProtocols

        public const string s_SrvProtClrpc = "CLRPC";
        public const string s_SrvProtRsi = "RSI";

        #endregion

        #endregion
        //########################################################################################
        #region GSDML V2.41 Enums

        #region SecurityClassEnum

        public const string s_SecurityClassEnum = @"SecurityClassEnum";

        /// <summary>string value 'Class1' for the EntityClassEnum type</summary>
        public const string s_SecurityClass1 = @"1";
        public const string s_SecurityClass2 = @"2";
        public const string s_SecurityClass3 = @"3";
        #endregion

        #endregion

        //########################################################################################
        #region GSDML V2.43 Enums

        #region SegmentClassEnum

        public const string SegmentClassEnum = @"SegmentClassEnum";

        /// <summary>string values for the SegmentClassEnums</summary>
        public const string SegmentClass_Spur = @"Spur";
        public const string SegmentClass_Trunk = @"Trunk";

        #endregion

        #region PortClassEnum

        public const string PortClassEnum = @"PortClassEnum";

        /// <summary>string values for the PortClassEnums</summary>
        public const string PortClass_PowerSource = @"PowerSource";
        public const string PortClass_PowerLoad = @"PowerLoad";
        public const string PortClass_PowerCascade = @"PowerCascade";

        #endregion

        #region PowerClassEnum

        public const string PowerClassEnum = @"PowerClassEnum";

        /// <summary>string values for the PowerClassEnums</summary>
        public const string PowerClass_A = @"A";
        public const string PowerClass_C = @"C";
        public const string PowerClass_3 = @"3";

        #endregion

        #region ProtectionClassEnum

        public const string ProtectionClassEnum = @"ProtectionClassEnum";

        /// <summary>string values for the ProtectionClassEnums</summary>
        public const string ProtectionClass_A = @"A";
        public const string ProtectionClass_C = @"C";
        public const string ProtectionClass_X = @"X";

        #endregion

        #region DCPSupportedFeaturesEnum

        public const string DCPFeaturesSupportedEnum = @"DCPFeaturesSupportedEnum";

        /// <summary>string values for the DCPSupportedFeaturesEnums</summary>
        public const string DCPSupportedFeature_PRUNING = @"PRUNING";
        public const string DCPSupportedFeature_RejectDCPSet = @"RejectDCPSet";

        #endregion

        #region SNMPSupportedFeaturesEnum

        public const string SNMPFeaturesSupportedEnum = @"SNMPFeaturesSupportedEnum";

        /// <summary>string values for the SNMPSupportedFeaturesEnums</summary>
        public const string SNMPSupportedFeature_SNMPAdjust = @"SNMPAdjust";

        #endregion

        #region APLSupportedFeaturesEnum

        public const string APLFeaturesSupportedEnum = @"APLFeaturesSupportedEnum";

        /// <summary>string values for the APLSupportedFeaturesEnums</summary>
        public const string APLSupportedFeature_PowerReal = @"PowerReal";

        /// <summary>string values for the LinkSpeedEnums</summary>
        public const string LinkSpeed_10 = @"10";
        public const string LinkSpeed_100 = @"100";
        public const string LinkSpeed_1000 = @"1000";
        public const string LinkSpeed_2500 = @"2500";
        public const string LinkSpeed_5000 = @"5000";
        public const string LinkSpeed_10000 = @"10000";

        /// <summary>string values for the PADeviceClassEnums</summary>
        public const string PADeviceClass_General = @"General";
        public const string PADeviceClass_ProcessControlDevice = @"ProcessControlDevice";

        /// <summary>string values for the SupportedCIMRecordEnums</summary>
        public const string SupportedRecord_CIM = @"CIM";
        public const string SupportedRecord_SCM = @"SCM";
        public const string SupportedRecord_DCP = @"DCP";
 
        /// <summary>string values for the Algorithms1Enums</summary>
        public const string Algorithms1_HKDF_SHA2_256 = @"HKDF-SHA2-256";

        /// <summary>string values for the Algorithms2Enums</summary>
        public const string Algorithms2_X25519 = @"X25519";
        public const string Algorithms2_X448 = @"X448";

        /// <summary>string values for the Algorithms3Enums</summary>
        public const string Algorithms3_Ed25519 = @"Ed25519";
        public const string Algorithms3_Ed448 = @"Ed448";
        public const string Algorithms3_P_256 = @"P-256";
        public const string Algorithms3_P_521 = @"P-521";

        /// <summary>string values for the AuthnXXXEnums</summary>
        public const string AuthnXXX_AES_GCM128 = @"AES-GCM#128";
        public const string AuthnXXX_AES_GCM256 = @"AES-GCM#256";
        public const string AuthnXXX_ChaCha20_Poly1305 = @"ChaCha20-Poly1305";

        #endregion

        #endregion


        #region GSDML V2.44 Enums
        /// <summary>string values for the TSNSupportedConfigurationsEnums</summary>
        public const string TSNSupportedConfiguration_TSN_Cfg_Default = @"TSN_Cfg_Default";
        public const string BridgeSupportedFeature_IngressRateLimiter = @"IngressRateLimiter";
        public const string SupportedAccess_Read = @"Read";
        public const string SupportedAccess_Write = @"Write";
        public const string SupportedAccess_Prm = @"Prm";

        #endregion

        //########################################################################################
        #region GSDML V1.0 Enum Converter Methods

        /// <summary>
        /// Check whether the given FamilyEnum string (from GSDML document) is convertable to the
        /// corresponding MainFamilies enum type.
        /// </summary>
        /// <param name="enumvalue">FamilyEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsFamilyEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_General:
                case s_Drives:
                case s_SwitchingDevices:
                case s_IO:
                case s_Valves:
                case s_Controllers:
                case s_Hmi:
                case s_Encoders:
                case s_Ncrc:
                case s_Gateway:
                case s_PlCs:
                case s_IdentSystems:
                case s_PaProfiles:
                case s_NetworkComponents:	// new in V2.0
                case s_Sensors:             // new in V2.0
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given GraphicsTypeEnum string (from GSDML document) is convertable 
        /// to the corresponding GraphicTypes enum type.
        /// </summary>
        /// <param name="enumvalue">GraphicsTypeEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsGraphicsTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_DeviceSymbol:
                case s_DeviceIcon:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given ObserverTypeEnum string (from GSDML document) is convertible 
        /// to the corresponding GraphicTypes enum type.
        /// </summary>
        /// <param name="enumvalue">ObserverTypeEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsObserverTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_DigitalInput:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given IODataConsistencyEnum string (from GSDML document) is 
        /// convertable to the corresponding IOConsistencies enum type.
        /// </summary>
        /// <param name="enumvalue">IODataConsistencyEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsIODataConsistencyEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_ItemConsistency:
                case s_AllItemsConsistency:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given DataTypeEnum string (from GSDML document) is 
        /// convertible to the corresponding IOConsistencies enum type.
        /// </summary>
        /// <param name="enumvalue">DataTypeEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsDataTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_Integer8:
                case s_Integer16:
                case s_Integer32:
                case s_Integer64:
                case s_Unsigned8:
                case s_Unsigned16:
                case s_Unsigned32:
                case s_Unsigned64:
                case s_Float32:
                case s_Float64:
                case s_Bit:
                case s_BitArea:
                case s_Date:
                case s_VisibleString:
                case s_OctetString:
                case s_TimeDifferenceWithDateIndication:
                case s_TimeDifferenceWithoutDateIndication:
                case s_TimeOfDayWithDateIndication:
                case s_TimeOfDayWithoutDateIndication:
                case s_NetworkTime:
                case s_NetworkTimeDifference:
                case s_FMessageTrailer4Byte:                // new in V2.0
                case s_FMessageTrailer5Byte:                // new in V2.0
                case s_FMessageTrailer5ByteTypingError:
                case s_Float32Unsigned8:                	// new in V2.2
                case s_Float32Status8:
                case s_Unsigned8Unsigned8:              	// new in V2.2
                case s_Boolean:                             // new in V2.32
                case s_UnicodeString8:                      // new in V2.32
                case s_String61131:                        // new in V2.32
                case s_Wstring61131:                       // new in V2.32
                case s_TimeStamp:                           // new in V2.32
                case s_TimeStampDifference:                 // new in V2.32
                case s_TimeStampDifferenceShort:            // new in V2.32
                case s_OctetString2Unsigned8:               // new in V2.32
                case s_Unsigned16S:                        // new in V2.32
                case s_Integer16S:                         // new in V2.32
                case s_Unsigned8S:                         // new in V2.32
                case s_OctetStringS:                       // new in V2.32
                case s_N2:                                  // new in V2.32
                case s_N4:                                  // new in V2.32
                case s_V2:                                  // new in V2.32
                case s_L2:                                  // new in V2.32
                case s_R2:                                  // new in V2.32
                case s_T2:                                  // new in V2.32
                case s_T4:                                  // new in V2.32
                case s_D2:                                  // new in V2.32
                case s_E2:                                  // new in V2.32
                case s_C4:                                  // new in V2.32
                case s_X2:                                  // new in V2.32
                case s_X4:                                  // new in V2.32
                case s_Unipolar216:                        // new in V2.32
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the FamilyEnum string (from GSDML document) to the corresponding MainFamilies 
        /// enum type.
        /// </summary>
        /// <param name="enumvalue">FamilyEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding MainFamilies enum type.</returns>
        public static MainFamilies ConvertFamilyEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_General:
                    {
                        return MainFamilies.GSDMfGeneral;
                    }
                case s_Drives:
                    {
                        return MainFamilies.GSDMfDrives;
                    }
                case s_SwitchingDevices:
                    {
                        return MainFamilies.GSDMfSwitchingDevices;
                    }
                case s_IO:
                    {
                        return MainFamilies.GsdmfIO;
                    }
                case s_Valves:
                    {
                        return MainFamilies.GSDMfValves;
                    }
                case s_Controllers:
                    {
                        return MainFamilies.GSDMfControllers;
                    }
                case s_Hmi:
                    {
                        return MainFamilies.GSDMfHmi;
                    }
                case s_Encoders:
                    {
                        return MainFamilies.GSDMfEncoders;
                    }
                case s_Ncrc:
                    {
                        return MainFamilies.GSDMfNcRc;
                    }
                case s_Gateway:
                    {
                        return MainFamilies.GSDMfGateway;
                    }
                case s_PlCs:
                    {
                        return MainFamilies.GSDMfPlCs;
                    }
                case s_IdentSystems:
                    {
                        return MainFamilies.GSDMfIdentSystems;
                    }
                case s_PaProfiles:
                    {
                        return MainFamilies.GSDMfPaProfiles;
                    }
                case s_NetworkComponents: // new in V2.0
                    {
                        return MainFamilies.GSDMfNetworkComponents;
                    }
                case s_Sensors:   // new in V2.0
                    {
                        return MainFamilies.GSDMfSensors;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid FamilyEnum string!", enumvalue);   // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the GraphicsTypeEnum string (from GSDML document) to the corresponding 
        /// GraphicTypes enum type.
        /// </summary>
        /// <param name="enumvalue">GraphicsTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding GraphicTypes enum type.</returns>
        public static GSDI.GraphicTypes ConvertGraphicsTypeEnum(string enumvalue)
        {
            #region Argument Preconditions

            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0) // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));

            #endregion

            switch (enumvalue)
            {
                case s_DeviceSymbol:
                    {
                        return GraphicTypes.GSDDeviceSymbol;
                    }
                case s_DeviceIcon:
                    {
                        return GraphicTypes.GSDDeviceIcon;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid GraphicsTypeEnum string!", enumvalue); // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the ObserverTypeEnum string (from GSDML document) to the corresponding 
        /// ObserverTypes enum type.
        /// </summary>
        /// <param name="enumvalue">ObserverTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding ObserverTypes enum type.</returns>
        public static ObserverTypes ConvertObserverTypeEnum(string enumvalue)
        {
            #region Argument Preconditions

            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0) // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));

            #endregion

            switch (enumvalue)
            {
                case s_DigitalInput:
                    {
                        return ObserverTypes.GSDDigitalInput;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid ObserverTypeEnum string!", enumvalue); // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the IODataConsistencyEnum string (from GSDML document) to the 
        /// corresponding IOConsistencies enum type.
        /// </summary>
        /// <param name="enumvalue">IODataConsistencyEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding IOConsistencies enum type.</returns>
        public static IOConsistencies ConvertIODataConsistencyEnum(string enumvalue)
        {
            #region Argument Preconditions

            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0) // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));

            #endregion

            switch (enumvalue)
            {
                case Enums.s_ItemConsistency:
                    {
                        return IOConsistencies.GSDType;
                    }
                case Enums.s_AllItemsConsistency:
                    {
                        return IOConsistencies.GSDComplete;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid IODataConsistencyEnum string!", enumvalue); // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the DataItemTypeEnum string (from GSDML document) to the corresponding 
        /// DataItemTypes enum type.
        /// </summary>
        /// <param name="enumvalue">DataItemTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding DataItemTypes enum type.</returns>
        public static DataItemTypes ConvertDataItemTypeEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Integer8:
                    {
                        return DataItemTypes.GSDDtInteger8;
                    }
                case s_Integer16:
                    {
                        return DataItemTypes.GSDDtInteger16;
                    }
                case s_Integer32:
                    {
                        return DataItemTypes.GSDDtInteger32;
                    }
                case s_Integer64:
                    {
                        return DataItemTypes.GSDDtInteger64;
                    }
                case s_Unsigned8:
                    {
                        return DataItemTypes.GSDDtUnsigned8;
                    }
                case s_Unsigned16:
                    {
                        return DataItemTypes.GSDDtUnsigned16;
                    }
                case s_Unsigned32:
                    {
                        return DataItemTypes.GSDDtUnsigned32;
                    }
                case s_Unsigned64:
                    {
                        return DataItemTypes.GSDDtUnsigned64;
                    }
                case s_Float32:
                    {
                        return DataItemTypes.GSDDtFloat32;
                    }
                case s_Float64:
                    {
                        return DataItemTypes.GSDDtFloat64;
                    }
                case s_Date:
                    {
                        return DataItemTypes.GSDDtDate;
                    }
                case s_VisibleString:
                    {
                        return DataItemTypes.GSDDtVisibleString;
                    }
                case s_OctetString:
                    {
                        return DataItemTypes.GSDDtOctetString;
                    }
                case s_TimeDifferenceWithDateIndication:
                    {
                        return DataItemTypes.GSDDtTimeDifferenceWithDateIndication;
                    }
                case s_TimeDifferenceWithoutDateIndication:
                    {
                        return DataItemTypes.GSDDtTimeDifferenceWithoutDateIndication;
                    }
                case s_TimeOfDayWithDateIndication:
                    {
                        return DataItemTypes.GSDDtTimeOfDayWithDateIndication;
                    }
                case s_TimeOfDayWithoutDateIndication:
                    {
                        return DataItemTypes.GSDDtTimeOfDayWithoutDateIndication;
                    }
                case s_FMessageTrailer4Byte:  // new in V2.0
                    {
                        return DataItemTypes.GsddtFMessageTrailer4Byte;
                    }
                case s_FMessageTrailer5Byte:	// new in V2.1, now corrected
                case s_FMessageTrailer5ByteTypingError: // new in V2.0, but with typing error
                    {
                        return DataItemTypes.GsddtFMessageTrailer5Byte;
                    }
                case s_NetworkTime:
                    {
                        return DataItemTypes.GSDDtNetworkTime;
                    }
                case s_NetworkTimeDifference:
                    {
                        return DataItemTypes.GSDDtNetworkTimeDifference;
                    }
                case s_Float32Unsigned8:	// new in V2.2
                case s_Float32Status8:
                    {
                        return DataItemTypes.GSDDtFloat32Unsigned8;
                    }
                case s_Unsigned8Unsigned8:	// new in V2.2
                    {
                        return DataItemTypes.GSDDtUnsigned8Unsigned8;
                    }
                case s_Boolean:                             // new in V2.32
                    {
                        return DataItemTypes.GSDDtBoolean;
                    }
                case s_UnicodeString8:                      // new in V2.32
                    {
                        return DataItemTypes.GSDDtUnicodeString8;
                    }
                case s_String61131:                        // new in V2.32
                    {
                        return DataItemTypes.GSDDtString61131;
                    }
                case s_Wstring61131:                       // new in V2.32
                    {
                        return DataItemTypes.GSDDtWstring61131;
                    }
                case s_TimeStamp:                           // new in V2.32
                    {
                        return DataItemTypes.GSDDtTimeStamp;
                    }
                case s_TimeStampDifference:                 // new in V2.32
                    {
                        return DataItemTypes.GSDDtTimeStampDifference;
                    }
                case s_TimeStampDifferenceShort:            // new in V2.32
                    {
                        return DataItemTypes.GSDDtTimeStampDifferenceShort;
                    }
                case s_OctetString2Unsigned8:               // new in V2.32
                    {
                        return DataItemTypes.GSDDtOctetString2Unsigned8;
                    }
                case s_Unsigned16S:                        // new in V2.32
                    {
                        return DataItemTypes.GSDDtUnsigned16S;
                    }
                case s_Integer16S:                         // new in V2.32
                    {
                        return DataItemTypes.GSDDtInteger16S;
                    }
                case s_Unsigned8S:                         // new in V2.32
                    {
                        return DataItemTypes.GSDDtUnsigned8S;
                    }
                case s_OctetStringS:                       // new in V2.32
                    {
                        return DataItemTypes.GSDDtOctetStringS;
                    }
                case s_N2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtN2;
                    }
                case s_N4:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtN4;
                    }
                case s_V2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtV2;
                    }
                case s_L2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtL2;
                    }
                case s_R2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtR2;
                    }
                case s_T2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtT2;
                    }
                case s_T4:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtT4;
                    }
                case s_D2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtD2;
                    }
                case s_E2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtE2;
                    }
                case s_C4:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtC4;
                    }
                case s_X2:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtX2;
                    }
                case s_X4:                                  // new in V2.32
                    {
                        return DataItemTypes.GsddtX4;
                    }
                case s_Unipolar216:                        // new in V2.32
                    {
                        return DataItemTypes.GSDDtUnipolar216;
                    }

                default:
                    {
                        throw new ArgumentException("Invalid DataItemTypeEnum string!", enumvalue);
                    }
            }
        }

        /// <summary>
        /// Converts the DataTypeEnum string (from GSDML document) to the corresponding 
        /// DataTypes enum type.
        /// </summary>
        /// <param name="enumvalue">DataTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding DataTypes enum type.</returns>
        public static DataTypes ConvertDataTypeEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Enums.s_Integer8:
                    {
                        return DataTypes.GSDInteger8;
                    }
                case Enums.s_Integer16:
                    {
                        return DataTypes.GSDInteger16;
                    }
                case Enums.s_Integer32:
                    {
                        return DataTypes.GSDInteger32;
                    }
                case Enums.s_Integer64:
                    {
                        return DataTypes.GSDInteger64;
                    }
                case Enums.s_Unsigned8:
                    {
                        return DataTypes.GSDUnsigned8;
                    }
                case Enums.s_Unsigned16:
                    {
                        return DataTypes.GSDUnsigned16;
                    }
                case Enums.s_Unsigned32:
                    {
                        return DataTypes.GSDUnsigned32;
                    }
                case Enums.s_Unsigned64:
                    {
                        return DataTypes.GSDUnsigned64;
                    }
                case Enums.s_Float32:
                    {
                        return DataTypes.GSDFloat32;
                    }
                case Enums.s_Float64:
                    {
                        return DataTypes.GSDFloat64;
                    }
                case Enums.s_Bit:
                    {
                        return DataTypes.GSDBit;
                    }
                case Enums.s_BitArea:
                    {
                        return DataTypes.GSDBitArea;
                    }
                case Enums.s_Date:
                    {
                        return DataTypes.GSDDate;
                    }
                case Enums.s_VisibleString:
                    {
                        return DataTypes.GSDVisibleString;
                    }
                case Enums.s_OctetString:
                    {
                        return DataTypes.GSDOctetString;
                    }
                case Enums.s_TimeDifferenceWithDateIndication:
                    {
                        return DataTypes.GSDTimeDifferenceWithDateIndication;
                    }
                case Enums.s_TimeDifferenceWithoutDateIndication:
                    {
                        return DataTypes.GSDTimeDifferenceWithoutDateIndication;
                    }
                case Enums.s_TimeOfDayWithDateIndication:
                    {
                        return DataTypes.GSDTimeOfDayWithDateIndication;
                    }
                case Enums.s_TimeOfDayWithoutDateIndication:
                    {
                        return DataTypes.GSDTimeOfDayWithoutDateIndication;
                    }
                case Enums.s_NetworkTime:
                    {
                        return DataTypes.GSDNetworkTime;
                    }
                case Enums.s_NetworkTimeDifference:
                    {
                        return DataTypes.GSDNetworkTimeDifference;
                    }
                case Enums.s_Float32Unsigned8:                	// new in V2.2
                case Enums.s_Float32Status8:
                    {
                        return DataTypes.GSDFloat32Unsigned8;
                    }
                case Enums.s_Unsigned8Unsigned8:              	// new in V2.2
                    {
                        return DataTypes.GSDUnsigned8Unsigned8;
                    }
                case Enums.s_Boolean:                             // new in V2.32
                    {
                        return DataTypes.GSDBoolean;
                    }
                case Enums.s_UnicodeString8:                      // new in V2.32
                    {
                        return DataTypes.GSDUnicodeString8;
                    }
                case Enums.s_String61131:                        // new in V2.32
                    {
                        return DataTypes.GSDString61131;
                    }
                case Enums.s_Wstring61131:                       // new in V2.32
                    {
                        return DataTypes.GSDWstring61131;
                    }
                case Enums.s_TimeStamp:                           // new in V2.32
                    {
                        return DataTypes.GSDTimeStamp;
                    }
                case Enums.s_TimeStampDifference:                 // new in V2.32
                    {
                        return DataTypes.GSDTimeStampDifference;
                    }
                case Enums.s_TimeStampDifferenceShort:            // new in V2.32
                    {
                        return DataTypes.GSDTimeStampDifferenceShort;
                    }
                case Enums.s_OctetString2Unsigned8:               // new in V2.32
                    {
                        return DataTypes.GSDOctetString2Unsigned8;
                    }
                case Enums.s_Unsigned16S:                        // new in V2.32
                    {
                        return DataTypes.GSDUnsigned16S;
                    }
                case Enums.s_Integer16S:                         // new in V2.32
                    {
                        return DataTypes.GSDInteger16S;
                    }
                case Enums.s_Unsigned8S:                         // new in V2.32
                    {
                        return DataTypes.GSDUnsigned8S;
                    }
                case Enums.s_OctetStringS:                       // new in V2.32
                    {
                        return DataTypes.GSDOctetStringS;
                    }
                case Enums.s_N2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdn2;
                    }
                case Enums.s_N4:                                  // new in V2.32
                    {
                        return DataTypes.Gsdn4;
                    }
                case Enums.s_V2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdv2;
                    }
                case Enums.s_L2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdl2;
                    }
                case Enums.s_R2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdr2;
                    }
                case Enums.s_T2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdt2;
                    }
                case Enums.s_T4:                                  // new in V2.32
                    {
                        return DataTypes.Gsdt4;
                    }
                case Enums.s_D2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdd2;
                    }
                case Enums.s_E2:                                  // new in V2.32
                    {
                        return DataTypes.Gsde2;
                    }
                case Enums.s_C4:                                  // new in V2.32
                    {
                        return DataTypes.Gsdc4;
                    }
                case Enums.s_X2:                                  // new in V2.32
                    {
                        return DataTypes.Gsdx2;
                    }
                case Enums.s_X4:                                  // new in V2.32
                    {
                        return DataTypes.Gsdx4;
                    }
                case Enums.s_Unipolar216:                        // new in V2.32
                    {
                        return DataTypes.GSDUnipolar216;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid DataTypeEnum string!", enumvalue);
                    }
            }
        }

        #endregion

        //########################################################################################
        #region GSDML V2.0 Enum Converter Methods

        /// <summary>
        /// Check whether the given SyncRoleEnum string (from GSDML document) is convertable to the
        /// corresponding SynchronisationRoles enum type.
        /// </summary>
        /// <param name="enumvalue">SyncRoleEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSynchronisationRoleEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_SyncMaster:
                case Enums.s_SyncSlave:
                case Enums.s_SyncMasterAndSyncSlave:
                case Enums.s_SyncMasterRedundant:
                case Enums.s_SyncMasterRedundantAndSyncSlave:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given RTClassEnum string (from GSDML document) is convertable to the
        /// corresponding RTClasses enum type.
        /// </summary>
        /// <param name="enumvalue">RTClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsRTClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_RtClass1:
                case Enums.s_RtClass2:
                case Enums.s_RtClass3:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given MAUTypeEnum string (from GSDML document) is convertable to the
        /// corresponding MAUTypes enum type.
        /// </summary>
        /// <param name="enumvalue">MAUTypeEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsMAUTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_Mau100Basetxfd:
                case Enums.s_Mau100Basefxfd:
                case Enums.s_Mau1000Basexfd:
                case Enums.s_Mau1000Baselxfd:
                case Enums.s_Mau1000Basesxfd:
                case Enums.s_Mau1000Basetfd:
                case Enums.s_Mau10GigBasefx:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given EntityClassEnum string (from GSDML document) is convertible to the
        /// corresponding EntityClasses enum type.
        /// </summary>
        /// <param name="enumvalue">EntityClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsEntityClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_EntityClass1:
                case Enums.s_EntityClass2:
                case Enums.s_EntityClass3:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given EntitySubclassEnum string (from GSDML document) is convertible to the
        /// corresponding EntitySubclasses enum type.
        /// </summary>
        /// <param name="enumvalue">EntitySubclassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsEntitySubclassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_EntitySubclass1:
                case Enums.s_EntitySubclass2:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given LinkStateDiagnosisEnum string (from GSDML document) is convertable to the
        /// corresponding LinkStateDiagnosis enum type.
        /// </summary>
        /// <param name="enumvalue">LinkStateDiagnosisEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsLinkStateDiagnosisEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.s_LsdcUp:
                case Enums.s_LsdcDown:
                case Enums.s_LsdcUpandDown:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given MRSupportedRolesEnum string (from GSDML document) is convertible to the
        /// corresponding MRSupportedRoles enum type.
        /// </summary>
        /// <param name="enumvalue">MRSupportedRolesEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsMRSupportedRolesEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_MrManager:
                case s_MrManagerAuto1:
                case s_MrManagerAuto2:
                case s_MrClient:
                case s_MrOff:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given FragmentationTypeEnum string (from GSDML document) is convertible to the
        /// corresponding FragmentationTypes enum type.
        /// </summary>
        /// <param name="enumvalue">FragmentationTypeEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsFragmentationTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_Dynamic:
                case s_Static:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given RedundancyDeviceTypeEnum string (from GSDML document) is convertible to the
        /// corresponding RedundancyDeviceTypes enum type.
        /// </summary>
        /// <param name="enumvalue">RedundancyDeviceEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsRedundancyDeviceTypeEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_S2:
                case s_R1:
                case s_R2:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SyncRoleEnum string (from GSDML document) to the corresponding 
        /// SynchronisationRoles enum type.
        /// </summary>
        /// <param name="enumvalue">SyncRoleEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SynchronisationRoles enum type.</returns>
        public static SynchronisationRoles ConvertSynchronisationRoleEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_SyncMaster:
                    {
                        return SynchronisationRoles.GSDSyncMaster;
                    }
                case s_SyncSlave:
                    {
                        return SynchronisationRoles.GSDSyncSlave;
                    }
                case s_SyncMasterAndSyncSlave:
                    {
                        return SynchronisationRoles.GSDSyncMasterAndSyncSlave;
                    }
                case s_SyncMasterRedundant:
                    {
                        return SynchronisationRoles.GSDSyncMasterRedundant;
                    }
                case s_SyncMasterRedundantAndSyncSlave:
                    {
                        return SynchronisationRoles.GSDSyncMasterRedundantAndSyncSlave;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SyncRoleEnum string!", enumvalue); // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the RTClassEnum string (from GSDML document) to the corresponding 
        /// RTClasses enum type.
        /// </summary>
        /// <param name="enumvalue">RTClassEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding RTClasses enum type.</returns>
        public static RTClasses ConvertRTClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_RtClass1:
                    {
                        return RTClasses.GSDClass1;
                    }
                case s_RtClass2:
                    {
                        return RTClasses.GSDClass2;
                    }
                case s_RtClass3:
                    {
                        return RTClasses.GSDClass3;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid RTClassEnum string!", enumvalue);  // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the MAUTypeEnum string (from GSDML document) to the corresponding 
        /// MAUTypes enum type.
        /// </summary>
        /// <param name="enumvalue">MAUTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding MAUTypes enum type.</returns>
        public static MauTypes ConvertMauTypeEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Mau100Basetxfd:
                    {
                        return MauTypes.GSDMau100Basetxfd;
                    }
                case s_Mau100Basefxfd:
                    {
                        return MauTypes.GSDMau100Basefxfd;
                    }
                case s_Mau1000Basexfd:
                    {
                        return MauTypes.GSDMau1000Basexfd;
                    }
                case s_Mau1000Baselxfd:
                    {
                        return MauTypes.GSDMau1000Baselxfd;
                    }
                case s_Mau1000Basesxfd:
                    {
                        return MauTypes.GSDMau1000Basesxfd;
                    }
                case s_Mau1000Basetfd:
                    {
                        return MauTypes.GSDMau1000Basetfd;
                    }
                case s_Mau10GigBasefx:
                    {
                        return MauTypes.GSDMau10GigBasefx;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid MAUTypeEnum string!", enumvalue);  // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the EntityClassEnum string (from GSDML document) to the corresponding 
        /// EntityClasses enum type.
        /// </summary>
        /// <param name="enumvalue">EntityClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding EntityClass enum type.</returns>
        public static EntityClasses ConvertEntityClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_EntityClass1:
                    {
                        return EntityClasses.GSDEntityClass1;
                    }
                case s_EntityClass2:
                    {
                        return EntityClasses.GSDEntityClass2;
                    }
                case s_EntityClass3:
                    {
                        return EntityClasses.GSDEntityClass3;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid EntityClassEnum string!", enumvalue);	// ---------->
                    }
            }
        }


        /// <summary>
        /// Converts the EntitySubclassEnum string (from GSDML document) to the corresponding 
        /// EntitySubclasses enum type.
        /// </summary>
        /// <param name="enumvalue">EntitySubclass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding EntitySubclass enum type.</returns>
        public static EntitySubclasses ConvertEntitySubclassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_EntitySubclass1:
                    {
                        return EntitySubclasses.GSDEntitySubclass1;
                    }
                case s_EntitySubclass2:
                    {
                        return EntitySubclasses.GSDEntitySubclass2;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid EntitySubclassEnum string!", enumvalue);	// ---------->
                    }
            }
        }


        /// <summary>
        /// Converts the LinkStateDiagnosisEnum string (from GSDML document) to the corresponding 
        /// LinkStateDiagnosis enum type.
        /// </summary>
        /// <param name="enumvalue">LinkStateDiagnosisEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding LinkStateDiagnosis enum type.</returns>
        public static LinkStateDiagnosisCapabilities ConvertLinkStateDiagnosisEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_LsdcUp:
                    {
                        return LinkStateDiagnosisCapabilities.GSDLsdcUp;
                    }
                case s_LsdcDown:
                    {
                        return LinkStateDiagnosisCapabilities.GSDLsdcDown;
                    }
                case s_LsdcUpandDown:
                    {
                        return LinkStateDiagnosisCapabilities.GSDLsdcUpanddown;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid LinkStateDiagnosis string!", enumvalue);   // ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the MRSupportedRolesEnum string (from GSDML document) to the corresponding 
        /// MRSupportedRoles enum type.
        /// </summary>
        /// <param name="enumvalue">MRSupportedRolesEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding MRSupportedRolesEnum enum type.</returns>
        public static MediaRedundancyRoles ConvertMRSupportedRolesEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_MrManager:
                    {
                        return MediaRedundancyRoles.GSDManager;
                    }
                case s_MrManagerAuto1:
                case s_MrManagerAuto2:
                    {
                        return MediaRedundancyRoles.GSDManagerAuto;
                    }
                case s_MrClient:
                    {
                        return MediaRedundancyRoles.GSDClient;
                    }
                case s_MrOff:
                    {
                        return MediaRedundancyRoles.GSDOff;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid MRSupportedRolesEnum string!", enumvalue);	// ---------->
                    }
            }
        }


        #endregion



        //########################################################################################
        #region GSDML V2.1 Enum Converter Methods


        /// <summary>
        /// Check whether the given AddressAssignment string (from GSDML document) is convertable to the
        /// corresponding AddressAssignment enum type.
        /// </summary>
        /// <param name="enumvalue">AddressAssignment GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsAddressAssignmentEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_Dcp:
                case s_Dhcp:
                case s_Local:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }



        /// <summary>
        /// Check whether the given SupportedRT_Classes string (from GSDML document) is convertable to the
        /// corresponding SupportedRT_Classes enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedRT_Classes GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSupportedRT_ClassesEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_RtClassUdp:
                case s_RtClass1:
                case s_RtClass2:
                case s_RtClass3:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given SupportedProtocols string (from GSDML document) is convertable to the
        /// corresponding SupportedProtocols enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedProtocols GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSupportedProtocolsEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_Lldp:
                case s_Snmp:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given SupportedSyncProtocols string (from GSDML document) is convertable to the
        /// corresponding SupportedSyncProtocols enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedSyncProtocols GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSupportedSyncProtocolsEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_Ptcp:
                    {
                        // Value is convertable!
                        return true;    // ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;   // ---------->
                    }
            }
        }

        #endregion


        #region GSDML V2.3 Enum Converter Methods

        /// <summary>
        /// Converts the FragmentationType string (from GSDML document) to the corresponding 
        /// FragmentationTypes enum type.
        /// </summary>
        /// <param name="enumvalue">FragmentationType GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding FragmentationType enum type.</returns>
        public static FragmentationTypes ConvertFragmentationTypeEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Dynamic:
                    {
                        return FragmentationTypes.GSDFragmentationTypeDynamic;
                    }
                case s_Static:
                    {
                        return FragmentationTypes.GSDFragmentationTypeStatic;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid FragmentationTypeEnum string!", enumvalue);	// ---------->
                    }
            }
        }


        /// <summary>
        /// Converts the DeviceType string (from GSDML document) to the corresponding 
        /// DeviceType enum type.
        /// </summary>
        /// <param name="enumvalue">DeviceType GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding DeviceType enum type.</returns>
        public static RedundancyDeviceTypes ConvertRedundancyDeviceTypeEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_S2:
                    {
                        return RedundancyDeviceTypes.GSDDeviceTypeS2;
                    }
                case s_R1:
                    {
                        return RedundancyDeviceTypes.GSDDeviceTypeR1;
                    }
                case s_R2:
                    {
                        return RedundancyDeviceTypes.GSDDeviceTypeR2;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid RedundancyDeviceTypes string!", enumvalue);	// ---------->
                    }
            }
        }


        #endregion


        #region GSDML V2.4 Enum Converter Methods

        /// <summary>
        /// Check whether the given Role string (from GSDML document) is convertable to the
        /// corresponding TimeSynchronisationRoles enum type.
        /// </summary>
        /// <param name="enumvalue">Role GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsRoleEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_Master:
                case s_Slave:
                    {
                        // Value is convertable!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the Role string (from GSDML document) to the corresponding 
        /// TimeSynchronisationRoles enum type.
        /// </summary>
        /// <param name="enumvalue">Role GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding TimeSynchronisationRoles enum type.</returns>
        public static TimeSynchronisationRoles ConvertRoleEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Master:
                    {
                        return TimeSynchronisationRoles.GSDTimeMaster;
                    }
                case s_Slave:
                    {
                        return TimeSynchronisationRoles.GSDTimeSlave;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid Role string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given SupportedFeatures string (from GSDML document) is convertable to the
        /// corresponding SupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedFeatures GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSupportedFeaturesEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_Tsn:
                case s_TsnTas:
                case s_TsnPreemption:
                    {
                        // Value is convertable!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SupportedFeatures string (from GSDML document) to the corresponding 
        /// SupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedFeatures GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SupportedFeatures enum type.</returns>
        public static SupportedFeatures ConvertSupportedFeaturesEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Tsn:
                    {
                        return SupportedFeatures.GSDTSN;
                    }
                case s_TsnTas:
                    {
                        return SupportedFeatures.GSDTSNTAS;
                    }
                case s_TsnPreemption:
                    {
                        return SupportedFeatures.GSDTSNPreemption;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SupportedFeatures string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given SFPDiagnosis string (from GSDML document) is convertable to the
        /// corresponding SFPDiagnosisSupported enum type.
        /// </summary>
        /// <param name="enumvalue">SFPDiagnosis GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsSFPDiagnosisEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_SfpDiagThresholdBased:
                case s_SfpDiagTxFault:
                case s_SfpDiagRxLoss:
                    {
                        // Value is convertable!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SFPDiagnosis string (from GSDML document) to the corresponding 
        /// SFPDiagnosisSupported enum type.
        /// </summary>
        /// <param name="enumvalue">SFPDiagnosis GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SFPDiagnosisSupported enum type.</returns>
        public static SfpDiagnosis ConvertSFPDiagnosisEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_SfpDiagThresholdBased:
                    {
                        return GSDI.SfpDiagnosis.GSDSFPDiagThresholdBased;
                    }
                case s_SfpDiagTxFault:
                    {
                        return GSDI.SfpDiagnosis.GSDSFPDiagTxFault;
                    }
                case s_SfpDiagRxLoss:
                    {
                        return GSDI.SfpDiagnosis.GSDSFPDiagRxLoss;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SFPDiagnosisSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given ServiceProtocols string (from GSDML document) is convertable to the
        /// corresponding SFPDiagnosisSupported enum type.
        /// </summary>
        /// <param name="enumvalue">ServiceProtocols GSDML entry.</param>
        /// <returns>True, if the given value is convertable, else false.</returns>
        public static bool IsServiceProtocolsEnumValueConvertable(string enumvalue)
        {
            switch (enumvalue)
            {
                case s_SrvProtClrpc:
                case s_SrvProtRsi:
                    {
                        // Value is convertable!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertable!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the ServiceProtocols string (from GSDML document) to the corresponding 
        /// SFPDiagnosisSupported enum type.
        /// </summary>
        /// <param name="enumvalue">ServiceProtocols GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SFPDiagnosisSupported enum type.</returns>
        public static ServiceProtocols ConvertServiceProtocolsEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_SrvProtClrpc:
                    {
                        return GSDI.ServiceProtocols.GSDSrvProtCLRPC;
                    }
                case s_SrvProtRsi:
                    {
                        return GSDI.ServiceProtocols.GSDSrvProtRSI;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SFPDiagnosisSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        #endregion

        #region GSDML V2.41 Enum Converter Methods

        /// <summary>
        /// Check whether the given SecurityClassEnum string (from GSDML document) is convertible to the
        /// corresponding SecurityClasses enum type.
        /// </summary>
        /// <param name="enumvalue">SecurityClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsSecurityClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case s_SecurityClass1:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                case s_SecurityClass2:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                case s_SecurityClass3:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SecurityClassEnum string (from GSDML document) to the corresponding 
        /// SecurityClasses enum type.
        /// </summary>
        /// <param name="enumvalue">SecurityClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SecurityClass enum type.</returns>
        public static SecurityClasses ConvertSecurityClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_SecurityClass1:
                    {
                        return SecurityClasses.GSDSecurityClass1;
                    }
                case s_SecurityClass2:
                    {
                        return SecurityClasses.GSDSecurityClass2;
                    }
                case s_SecurityClass3:
                    {
                        return SecurityClasses.GSDSecurityClass3;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SecurityClassEnum string!", nameof(enumvalue));	// ---------->
                    }
            }
        }

        #endregion

        #region GSDML V2.43 Enum Converter Methods

        /// <summary>
        /// Check whether the given SegmentClassEnum string (from GSDML document) is convertible to the
        /// corresponding SegmentClasses enum type.
        /// </summary>
        /// <param name="enumvalue">SegmentClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsSegmentClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case SegmentClass_Spur:
                case SegmentClass_Trunk:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SegmentClassEnum string (from GSDML document) to the corresponding 
        /// SegmentClasses enum type.
        /// </summary>
        /// <param name="enumvalue">SegmentClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SegmentClass enum type.</returns>
        public static SegmentClasses ConvertSegmentClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case SegmentClass_Spur:
                    {
                        return GSDI.SegmentClasses.GSDSegmentClassSpur;
                    }
                case SegmentClass_Trunk:
                    {
                        return GSDI.SegmentClasses.GSDSegmentClassTrunk;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SegmentClassEnum string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given PortClassEnum string (from GSDML document) is convertible to the
        /// corresponding PortClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PortClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsPortClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case PortClass_PowerSource:
                case PortClass_PowerLoad:
                case PortClass_PowerCascade:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the PortClassEnum string (from GSDML document) to the corresponding 
        /// PortClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PortClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding PortClass enum type.</returns>
        public static PortClasses ConvertPortClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case PortClass_PowerSource:
                    {
                        return PortClasses.GSDPortClassPowerSource;
                    }
                case PortClass_PowerLoad:
                    {
                        return PortClasses.GSDPortClassPowerLoad;
                    }
                case PortClass_PowerCascade:
                    {
                        return PortClasses.GSDPortClassPowerCascade;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid PortClassEnum string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given PowerClassEnum string (from GSDML document) is convertible to the
        /// corresponding PowerClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PowerClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsPowerClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case PowerClass_A:
                case PowerClass_C:
                case PowerClass_3:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the PowerClassEnum string (from GSDML document) to the corresponding 
        /// PowerClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PowerClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding PowerClass enum type.</returns>
        public static PowerClasses ConvertPowerClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case PowerClass_A:
                    {
                        return PowerClasses.GSDPowerClassA;
                    }
                case PowerClass_C:
                    {
                        return PowerClasses.GSDPowerClassC;
                    }
                case PowerClass_3:
                    {
                        return PowerClasses.GSDPowerClass3;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid PowerClassEnum string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given ProtectionClassEnum string (from GSDML document) is convertible to the
        /// corresponding ProtectionClasses enum type.
        /// </summary>
        /// <param name="enumvalue">ProtectionClassEnum GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsProtectionClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case ProtectionClass_A:
                case ProtectionClass_C:
                case ProtectionClass_X:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the ProtectionClassEnum string (from GSDML document) to the corresponding 
        /// ProtectionClasses enum type.
        /// </summary>
        /// <param name="enumvalue">ProtectionClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding ProtectionClass enum type.</returns>
        public static ProtectionClasses ConvertProtectionClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case ProtectionClass_A:
                    {
                        return ProtectionClasses.GSDProtectionClassA;
                    }
                case ProtectionClass_C:
                    {
                        return ProtectionClasses.GSDProtectionClassC;
                    }
                case ProtectionClass_X:
                    {
                        return ProtectionClasses.GSDProtectionClassX;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid ProtectionClassEnum string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given DCPFeaturesSupportedEnum string (from GSDML document) is convertible to the
        /// corresponding DCPSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">DCP_FeaturesSupported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsDCPFeaturesSupportedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case DCPSupportedFeature_PRUNING:
                case DCPSupportedFeature_RejectDCPSet:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the DCPFeaturesSupportedEnum string (from GSDML document) to the corresponding 
        /// DCPSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">DCP_FeaturesSupported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding DCPSupportedFeatures enum type.</returns>
        public static DcpSupportedFeatures ConvertDCPFeaturesSupportedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case DCPSupportedFeature_PRUNING:
                    {
                        return GSDI.DcpSupportedFeatures.GSDDCPSupportedFeaturePRUNING;
                    }
                case DCPSupportedFeature_RejectDCPSet:
                    {
                        return GSDI.DcpSupportedFeatures.GSDDCPSupportedFeatureRejectDCPSet;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid DCP_FeaturesSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given SNMPFeaturesSupportedEnum string (from GSDML document) is convertible to the
        /// corresponding SNMPSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">SNMP_FeaturesSupported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsSNMPFeaturesSupportedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case SNMPSupportedFeature_SNMPAdjust:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SNMPFeaturesSupportedEnum string (from GSDML document) to the corresponding 
        /// SNMPSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">SNMP_FeaturesSupported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SNMPSupportedFeatures enum type.</returns>
        public static SnmpSupportedFeatures ConvertSNMPFeaturesSupportedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case SNMPSupportedFeature_SNMPAdjust:
                    {
                        return SnmpSupportedFeatures.GSD_SNMPSupportedFeature_SNMPAdjust;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SNMP_FeaturesSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given APLFeaturesSupportedEnum string (from GSDML document) is convertible to the
        /// corresponding APLSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">APL_FeaturesSupported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsAPLFeaturesSupportedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case APLSupportedFeature_PowerReal:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the APLFeaturesSupportedEnum string (from GSDML document) to the corresponding 
        /// APLSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">APL_FeaturesSupported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding APLSupportedFeatures enum type.</returns>
        public static AplSupportedFeatures ConvertAPLFeaturesSupportedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case APLSupportedFeature_PowerReal:
                    {
                        return GSDI.AplSupportedFeatures.GSD_APLSupportedFeature_PowerReal;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid APL_FeaturesSupported string!", enumvalue);	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given BridgeFeaturesSupportedEnum string (from GSDML document) is convertible to the
        /// corresponding BridgeSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">Bridge_FeaturesSupported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsBridgeFeaturesSupportedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case BridgeSupportedFeature_IngressRateLimiter:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the BridgeFeaturesSupportedEnum string (from GSDML document) to the corresponding 
        /// BridgeSupportedFeatures enum type.
        /// </summary>
        /// <param name="enumvalue">Bridge_FeaturesSupported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding BridgeSupportedFeatures enum type.</returns>
        public static GSDI.BridgeSupportedFeatures ConvertBridgeFeaturesSupportedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Enums.BridgeSupportedFeature_IngressRateLimiter:
                    {
                        return GSDI.BridgeSupportedFeatures.GSD_BridgeSupportedFeature_IngressRateLimiter;
                    }
                default:
                    {
                        // Value is not convertible!
                        throw new ArgumentException("Invalid Bridge_FeaturesSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given TSNConfigurationsSupportedEnum string (from GSDML document) is convertible to the
        /// corresponding TSNSupportedConfigurations enum type.
        /// </summary>
        /// <param name="enumvalue">TSN_ConfigurationsSupported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsTSNConfigurationsSupportedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.TSNSupportedConfiguration_TSN_Cfg_Default:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the TSNConfigurationsSupportedEnum string (from GSDML document) to the corresponding 
        /// TSNSupportedConfigurations enum type.
        /// </summary>
        /// <param name="enumvalue">TSN_ConfigurationsSupported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding TSNSupportedConfigurations enum type.</returns>
        public static GSDI.TSNSupportedConfigurations ConvertTSNConfigurationsSupportedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Enums.TSNSupportedConfiguration_TSN_Cfg_Default:
                    {
                        return GSDI.TSNSupportedConfigurations.GSD_TSNSupportedConfiguration_TSN_Cfg_Default;
                    }
                default:
                    {
                        // Value is not convertible!
                        throw new ArgumentException("Invalid TSN_ConfigurationsSupported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given SupportedAccessEnum string (from GSDML document) is convertible to the
        /// corresponding SupportedAccess enum type.
        /// </summary>
        /// <param name="enumvalue">Access GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsSupportedAccessEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.SupportedAccess_Read:
                case Enums.SupportedAccess_Write:
                case Enums.SupportedAccess_Prm:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SupportedAccessEnum string.
        /// </summary>
        /// <param name="enumvalue">Access GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding Access enum type.</returns>
        public static GSDI.SupportedAccess ConvertSupportedAccessEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Enums.SupportedAccess_Read:
                    {
                        return GSDI.SupportedAccess.GSD_SupportedAccess_Read;
                    }
                case Enums.SupportedAccess_Write:
                    {
                        return GSDI.SupportedAccess.GSD_SupportedAccess_Write;
                    }
                case Enums.SupportedAccess_Prm:
                    {
                        return GSDI.SupportedAccess.GSD_SupportedAccess_Prm;
                    }
                default:
                    {
                        // Value is not convertible!
                        throw new ArgumentException("Invalid Access string!", enumvalue);	// ---------->
                    }
            }
        }


        /// <summary>
        /// Check whether the given LinkSpeedEnum string (from GSDML document) is convertible to the
        /// corresponding LinkSpeeds enum type.
        /// </summary>
        /// <param name="enumvalue">LinkSpeed GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsLinkSpeedEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case LinkSpeed_10:
                case LinkSpeed_100:
                case LinkSpeed_1000:
                case LinkSpeed_2500:
                case LinkSpeed_5000:
                case LinkSpeed_10000:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the LinkSpeedEnum string (from GSDML document) to the corresponding 
        /// LinkSpeeds enum type.
        /// </summary>
        /// <param name="enumvalue">LinkSpeed GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding LinkSpeed enum type.</returns>
        public static LinkSpeeds ConvertLinkSpeedEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case LinkSpeed_10:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_10;
                    }
                case LinkSpeed_100:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_100;
                    }
                case LinkSpeed_1000:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_1000;
                    }
                case LinkSpeed_2500:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_2500;
                    }
                case LinkSpeed_5000:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_5000;
                    }
                case LinkSpeed_10000:
                    {
                        return LinkSpeeds.GSD_LinkSpeed_10000;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid LinkSpeed string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given PADeviceClassEnum string (from GSDML document) is convertible to the
        /// corresponding PADeviceClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PADeviceClass GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsPADeviceClassEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Enums.PADeviceClass_General:
                case Enums.PADeviceClass_ProcessControlDevice:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the PADeviceClassEnum string (from GSDML document) to the corresponding 
        /// PADeviceClasses enum type.
        /// </summary>
        /// <param name="enumvalue">PADeviceClass GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding PADeviceClass enum type.</returns>
        public static GSDI.PADeviceClasses ConvertPADeviceClassEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Enums.PADeviceClass_General:
                    {
                        return GSDI.PADeviceClasses.GSD_PADeviceClass_General;
                    }
                case Enums.PADeviceClass_ProcessControlDevice:
                    {
                        return GSDI.PADeviceClasses.GSD_PADeviceClass_ProcessControlDevice;
                    }
                default:
                    {
                        // Value is not convertible!
                        throw new ArgumentException("Invalid PADeviceClass string!", enumvalue);	// ---------->
                    }
            }          
        }

        /// <summary>
        /// Check whether the given SupportedRecordEnum string (from GSDML document) is convertible to the
        /// corresponding SupportedRecords enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedRecord GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsSupportedRecordEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case SupportedRecord_CIM:
                case SupportedRecord_SCM:
                case SupportedRecord_DCP:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the SupportedRecordEnum string (from GSDML document) to the corresponding 
        /// SupportedCIMRecords enum type.
        /// </summary>
        /// <param name="enumvalue">SupportedRecord GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding SupportedRecord enum type.</returns>
        public static SupportedRecords ConvertSupportedRecordEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case SupportedRecord_CIM:
                    {
                        return GSDI.SupportedRecords.GSD_SupportedRecord_CIM;
                    }
                case SupportedRecord_SCM:
                    {
                        return GSDI.SupportedRecords.GSD_SupportedRecord_SCM;
                    }
                case SupportedRecord_DCP:
                    {
                        return GSDI.SupportedRecords.GSD_SupportedRecord_DCP;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid SupportedRecord string!", enumvalue);	// ---------->
                    }
            }
        }



        /// <summary>
        /// Check whether the given Algorithms1Enum string (attribute under element KeyDerivation from GSDML document)
        /// is convertible to the corresponding Algorithms1 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsAlgorithms1EnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Algorithms1_HKDF_SHA2_256:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the Algorithms1Enum string (attribute under element KeyDerivation from GSDML document) 
        /// to the corresponding Algorithms1 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding Algorithms1 enum type.</returns>
        public static Algorithms1 ConvertAlgorithms1Enum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Algorithms1_HKDF_SHA2_256:
                    {
                        return GSDI.Algorithms1.GSD_Algorithms1_HKDF_SHA2_256;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid Supported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given Algorithms2Enum string (attribute under element KeyAgreement from GSDML document)
        /// is convertible to the corresponding Algorithms2 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsAlgorithms2EnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Algorithms2_X25519:
                case Algorithms2_X448:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the Algorithms2Enum string (attribute under element KeyAgreement from GSDML document) 
        /// to the corresponding Algorithms2 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding Algorithms2 enum type.</returns>
        public static Algorithms2 ConvertAlgorithms2Enum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Algorithms2_X25519:
                    {
                        return Algorithms2.GSD_Algorithms2_X25519;
                    }
                case Algorithms2_X448:
                    {
                        return Algorithms2.GSD_Algorithms2_X448;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid Supported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given Algorithms3Enum string (attribute under element DigitalSignature from GSDML document)
        /// is convertible to the corresponding Algorithms3 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsAlgorithms3EnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case Algorithms3_Ed25519:
                case Algorithms3_Ed448:
                case Algorithms3_P_256:
                case Algorithms3_P_521:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the Algorithms3Enum string (attribute under element DigitalSignature from GSDML document) 
        /// to the corresponding Algorithms3 enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding Algorithms3 enum type.</returns>
        public static Algorithms3 ConvertAlgorithms3Enum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case Algorithms3_Ed25519:
                    {
                        return Algorithms3.GSD_Algorithms3_Ed25519;
                    }
                case Algorithms3_Ed448:
                    {
                        return Algorithms3.GSD_Algorithms3_Ed448;
                    }
                case Algorithms3_P_256:
                    {
                        return Algorithms3.GSD_Algorithms3_P_256;
                    }
                case Algorithms3_P_521:
                    {
                        return Algorithms3.GSD_Algorithms3_P_521;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid Supported string!", enumvalue);	// ---------->
                    }
            }
        }

        /// <summary>
        /// Check whether the given AuthnXXXEnum string (attribute AuthnOnly or AuthnEnc
        /// under element StreamProtection, AlarmProtection or ConnectionManagementProtection from GSDML document)
        /// is convertible to the corresponding AuthnXXX enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <returns>True, if the given value is convertible, else false.</returns>
        public static bool IsAuthnXXXEnumValueConvertable(string enumvalue)
        {
            if (string.IsNullOrEmpty(enumvalue))
                return false;

            switch (enumvalue)
            {
                case AuthnXXX_AES_GCM128:
                case AuthnXXX_AES_GCM256:
                case AuthnXXX_ChaCha20_Poly1305:
                    {
                        // Value is convertible!
                        return true;	// ---------->
                    }
                default:
                    {
                        // Value is not convertible!
                        return false;	// ---------->
                    }
            }
        }

        /// <summary>
        /// Converts the AuthnXXXEnum string (attribute AuthnOnly or AuthnEnc 
        /// under element StreamProtection, AlarmProtection or ConnectionManagementProtection from GSDML document)
        /// to the corresponding AuthnXXX enum type.
        /// </summary>
        /// <param name="enumvalue">Supported GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>Corresponding AuthnXXX enum type.</returns>
        public static AuthnXxx ConvertAuthnXXXEnum(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case AuthnXXX_AES_GCM128:
                    {
                        return AuthnXxx.GSD_AuthnXXX_AES_GCM128;
                    }
                case AuthnXXX_AES_GCM256:
                    {
                        return AuthnXxx.GSD_AuthnXXX_AES_GCM256;
                    }
                case AuthnXXX_ChaCha20_Poly1305:
                    {
                        return AuthnXxx.GSD_AuthnXXX_ChaCha20_Poly1305;
                    }
                default:
                    {
                        throw new ArgumentException("Invalid Supported string!", enumvalue);	// ---------->
                    }
            }
        }

        #endregion

        //########################################################################################
        #region Common Enum Methods

        /// <summary>
        /// Accesses the length in bits of the specified DataItemType.
        /// </summary>
        /// <param name="type">Type for which the bit length should be returned.</param>
        /// <returns>Length of the type in bits.  If type with variable bit length is given, 
        /// 0 would be returned.</returns>
        public static uint GetDataItemTypeBitLength(DataItemTypes type)
        {
            switch (type)
            {
                case DataItemTypes.GSDDtUnsigned8:
                case DataItemTypes.GSDDtInteger8:
                case DataItemTypes.GSDDtBoolean:                   // New in V2.32
                case DataItemTypes.GSDDtUnsigned8S:               // New in V2.32
                    {
                        return 1 * 8;
                    }
                case DataItemTypes.GSDDtUnsigned16:
                case DataItemTypes.GSDDtInteger16:
                case DataItemTypes.GSDDtUnsigned8Unsigned8:
                case DataItemTypes.GSDDtUnsigned16S:              // New in V2.32
                case DataItemTypes.GSDDtInteger16S:               // New in V2.32
                case DataItemTypes.GsddtN2:                        // New in V2.32
                case DataItemTypes.GsddtV2:                        // New in V2.32
                case DataItemTypes.GsddtL2:                        // New in V2.32
                case DataItemTypes.GsddtR2:                        // New in V2.32
                case DataItemTypes.GsddtT2:                        // New in V2.32
                case DataItemTypes.GsddtD2:                        // New in V2.32
                case DataItemTypes.GsddtE2:                        // New in V2.32
                case DataItemTypes.GsddtX2:                        // New in V2.32
                case DataItemTypes.GSDDtUnipolar216:              // New in V2.32
                    {
                        return 2 * 8;
                    }
                case DataItemTypes.GSDDtOctetString2Unsigned8:     // New in V2.32
                    {
                        return 3 * 8;
                    }
                case DataItemTypes.GSDDtFloat32:
                case DataItemTypes.GSDDtUnsigned32:
                case DataItemTypes.GSDDtInteger32:
                case DataItemTypes.GSDDtTimeDifferenceWithoutDateIndication:
                case DataItemTypes.GSDDtTimeOfDayWithoutDateIndication:
                case DataItemTypes.GsddtFMessageTrailer4Byte:	  // New in V2.0
                case DataItemTypes.GsddtN4:                        // New in V2.32
                case DataItemTypes.GsddtT4:                        // New in V2.32
                case DataItemTypes.GsddtC4:                        // New in V2.32
                case DataItemTypes.GsddtX4:                        // New in V2.32
                    {
                        return 4 * 8;
                    }
                case DataItemTypes.GsddtFMessageTrailer5Byte:	  // New in V2.0
                case DataItemTypes.GSDDtFloat32Unsigned8:		  // New in V2.0
                    {
                        return 5 * 8;
                    }
                case DataItemTypes.GSDDtTimeDifferenceWithDateIndication:
                case DataItemTypes.GSDDtTimeOfDayWithDateIndication:
                    {
                        return 6 * 8;
                    }
                case DataItemTypes.GSDDtDate:
                    {
                        return 7 * 8;
                    }
                case DataItemTypes.GSDDtFloat64:
                case DataItemTypes.GSDDtUnsigned64:
                case DataItemTypes.GSDDtInteger64:
                case DataItemTypes.GSDDtNetworkTime:
                case DataItemTypes.GSDDtNetworkTimeDifference:
                case DataItemTypes.GSDDtTimeStampDifferenceShort:  // New in V2.32
                    {
                        return 8 * 8;
                    }
                case DataItemTypes.GSDDtTimeStamp:                 // New in V2.32
                case DataItemTypes.GSDDtTimeStampDifference:       // New in V2.32
                    {
                        return 12 * 8;
                    }
                case DataItemTypes.GSDDtOctetString:
                case DataItemTypes.GSDDtVisibleString:
                case DataItemTypes.GSDDtUnicodeString8:            // New in V2.32
                case DataItemTypes.GSDDtString61131:              // New in V2.32
                case DataItemTypes.GSDDtWstring61131:             // New in V2.32
                case DataItemTypes.GSDDtOctetStringS:             // New in V2.32
                default:
                    {
                        return 0;	// ---------->
                    }
            }
        }


        /// <summary>
        /// Accesses the length in bits of the specified DataItemType.
        /// </summary>
        /// <param name="type">Type for which the bit length should be returned.</param>
        /// <returns>Length of the type in bits. If type with variable bit length is given, 
        /// 0 would be returned.</returns>
        public static uint GetDataItemTypeBitLength(string type)
        {
            GSDI.DataItemTypes dit = DataItemTypes.GSDDtVisibleString;
            try
            {
                if (IsDataTypeEnumValueConvertable(type))
                    dit = ConvertDataItemTypeEnum(type);
            }
            catch (ArgumentException)
            {
                return 0;	// ---------->
            }
            return GetDataItemTypeBitLength(dit);
        }


        /// <summary>
        /// Accesses the length in bits of the specified DataType.
        /// </summary>
        /// <param name="type">Type for which the bit length should be returned. If 
        /// DataType with variable bit length is given, 0 would be returned.</param>
        /// <returns>Length of the type in bits.</returns>
        public static uint GetDataTypeBitLength(DataTypes type)
        {
            // Return bit length for each data type.
            switch (type)
            {
                case DataTypes.GSDBit:
                    {
                        return 1;
                    }
                case DataTypes.GSDUnsigned8:
                case DataTypes.GSDInteger8:
                case DataTypes.GSDBoolean:                   // New in V2.32
                case DataTypes.GSDUnsigned8S:               // New in V2.32
                    {
                        return 1 * 8;
                    }
                case DataTypes.GSDUnsigned16:
                case DataTypes.GSDInteger16:
                case DataTypes.GSDUnsigned8Unsigned8:
                case DataTypes.GSDUnsigned16S:              // New in V2.32
                case DataTypes.GSDInteger16S:               // New in V2.32
                case DataTypes.Gsdn2:                        // New in V2.32
                case DataTypes.Gsdv2:                        // New in V2.32
                case DataTypes.Gsdl2:                        // New in V2.32
                case DataTypes.Gsdr2:                        // New in V2.32
                case DataTypes.Gsdt2:                        // New in V2.32
                case DataTypes.Gsdd2:                        // New in V2.32
                case DataTypes.Gsde2:                        // New in V2.32
                case DataTypes.Gsdx2:                        // New in V2.32
                case DataTypes.GSDUnipolar216:              // New in V2.32
                    {
                        return 2 * 8;
                    }
                case DataTypes.GSDOctetString2Unsigned8:     // New in V2.32
                    {
                        return 3 * 8;
                    }
                case DataTypes.GSDFloat32:
                case DataTypes.GSDUnsigned32:
                case DataTypes.GSDInteger32:
                case DataTypes.GSDTimeDifferenceWithoutDateIndication:
                case DataTypes.GSDTimeOfDayWithoutDateIndication:
                case DataTypes.GsdfMessageTrailer4Byte:      // New in V2.0
                case DataTypes.Gsdn4:                        // New in V2.32
                case DataTypes.Gsdt4:                        // New in V2.32
                case DataTypes.Gsdc4:                        // New in V2.32
                case DataTypes.Gsdx4:                        // New in V2.32
                    {
                        return 4 * 8;
                    }
                case DataTypes.GsdfMessageTrailer5Byte:	  // New in V2.0
                case DataTypes.GSDFloat32Unsigned8:		  // New in V2.0
                    {
                        return 5 * 8;
                    }
                case DataTypes.GSDTimeDifferenceWithDateIndication:
                case DataTypes.GSDTimeOfDayWithDateIndication:
                    {
                        return 6 * 8;
                    }
                case DataTypes.GSDDate:
                    {
                        return 7 * 8;
                    }
                case DataTypes.GSDFloat64:
                case DataTypes.GSDUnsigned64:
                case DataTypes.GSDInteger64:
                case DataTypes.GSDNetworkTime:
                case DataTypes.GSDNetworkTimeDifference:
                case DataTypes.GSDTimeStampDifferenceShort:  // New in V2.32
                    {
                        return 8 * 8;
                    }
                case DataTypes.GSDTimeStamp:                 // New in V2.32
                case DataTypes.GSDTimeStampDifference:       // New in V2.32
                    {
                        return 12 * 8;
                    }
                case DataTypes.GSDBitArea:
                case DataTypes.GSDOctetString:
                case DataTypes.GSDVisibleString:
                case DataTypes.GSDUnicodeString8:            // New in V2.32
                case DataTypes.GSDString61131:              // New in V2.32
                case DataTypes.GSDWstring61131:             // New in V2.32
                case DataTypes.GSDOctetStringS:             // New in V2.32
                default:
                    {
                        return 0;
                    }
            }
        }

        /// <summary>
        /// Accesses the length in bits of the specified DataType.
        /// </summary>
        /// <param name="type">Type for which the bit length should be returned. If 
        /// DataType with variable bit length is given, 0 would be returned.</param>
        /// <returns>Length of the type in bits.</returns>
        public static uint GetDataTypeBitLength(string type)
        {
            GSDI.DataTypes dt = DataTypes.GSDBitArea;
            try
            {
                if (IsDataTypeEnumValueConvertable(type))
                    dt = ConvertDataTypeEnum(type);
            }
            catch (ArgumentException)
            {
                return 0;	// ---------->
            }
            return GetDataTypeBitLength(dt);
        }

        /// <summary>
        /// Converts the given value to the .NET type which corresponds to the 
        /// specified DataType.
        /// </summary>
        /// <param name="val">Value which should be converted to corresponding
        /// .NET data type.</param>
        /// <param name="type">Type to which the given value should be converted.</param>
        /// <returns>Converted value. Null if it couldn't be converted.</returns>
        public static object GetDataTypeObject(string val, string type)
        {
            GSDI.DataTypes dt = DataTypes.GSDBit;
            try
            {
                if (IsDataTypeEnumValueConvertable(type))
                    dt = ConvertDataTypeEnum(type);
            }
            catch (ArgumentException)
            {
                return 0;	// ---------->
            }
            return GetDataTypeObject(val, dt);
        }

        /// <summary>
        /// Converts the given value to the .NET type which corresponds to the 
        /// specified DataType.
        /// </summary>
        /// <param name="val">Value which should be converted to corresponding
        /// .NET data type.</param>
        /// <param name="type">Type (as enum value) to which the given value should be converted.</param>
        /// <returns>Converted value. Null if it couldn't be converted.</returns>
        public static object GetDataTypeObject(string val, DataTypes type)
        {
            if (String.IsNullOrEmpty(val))
                throw new ArgumentException("Invalid parameters for method GetDataTypeObject");

            try
            {
                switch (type)
                {
                    case DataTypes.GSDBit:
                        {
                            Byte v = System.Xml.XmlConvert.ToByte(val);

                            if (!(v == 0 || v == 1))
                            {
                                return null;	// ---------->
                            }
                            return v;
                        }
                    case DataTypes.GSDBitArea:
                        {
                            return System.Xml.XmlConvert.ToUInt16(val);
                        }
                    case DataTypes.GSDUnsigned8:
                        {
                            return System.Xml.XmlConvert.ToByte(val);
                        }
                    case DataTypes.GSDUnsigned16:
                    case DataTypes.Gsdr2:
                    case DataTypes.Gsdt2:
                    case DataTypes.Gsdd2:
                        {
                            return System.Xml.XmlConvert.ToUInt16(val);
                        }
                    case DataTypes.GSDUnsigned32:
                    case DataTypes.Gsdt4:
                        {
                            return System.Xml.XmlConvert.ToUInt32(val);
                        }
                    case DataTypes.GSDUnsigned64:
                        {
                            return System.Xml.XmlConvert.ToUInt64(val);
                        }
                    case DataTypes.GSDInteger8:
                        {
                            return System.Xml.XmlConvert.ToSByte(val);
                        }
                    case DataTypes.GSDInteger16:
                    case DataTypes.Gsdn2:
                    case DataTypes.Gsdx2:
                        {
                            return System.Xml.XmlConvert.ToInt16(val);
                        }
                    case DataTypes.GSDInteger32:
                    case DataTypes.Gsdn4:
                    case DataTypes.Gsdx4:
                        {
                            return System.Xml.XmlConvert.ToInt32(val);
                        }
                    case DataTypes.GSDInteger64:
                        {
                            return System.Xml.XmlConvert.ToInt64(val);
                        }
                    case DataTypes.GSDFloat32:
                    case DataTypes.Gsde2:
                    case DataTypes.Gsdc4:
                    case DataTypes.GSDUnipolar216:
                        {
                            return System.Xml.XmlConvert.ToSingle(val);
                        }
                    case DataTypes.GSDFloat64:
                        {
                            return System.Xml.XmlConvert.ToDouble(val);
                        }
                    case DataTypes.GSDDate:
                    case DataTypes.GSDTimeOfDayWithDateIndication:
                    case DataTypes.GSDTimeOfDayWithoutDateIndication:
                    case DataTypes.GSDTimeDifferenceWithDateIndication:
                    case DataTypes.GSDTimeDifferenceWithoutDateIndication:
                    case DataTypes.GSDNetworkTime:
                    case DataTypes.GSDNetworkTimeDifference:
                    case DataTypes.GSDOctetString:
                    case DataTypes.GSDVisibleString:
                    case DataTypes.GSDBoolean:
                    case DataTypes.Gsdv2:
                    case DataTypes.Gsdl2:
                    case DataTypes.GSDUnsigned8Unsigned8:
                    case DataTypes.GSDFloat32Unsigned8:
                    case DataTypes.GSDOctetString2Unsigned8:
                    case DataTypes.GSDUnsigned16S:
                    case DataTypes.GSDInteger16S:
                    case DataTypes.GSDUnsigned8S:
                    case DataTypes.GSDOctetStringS:
                    case DataTypes.GSDTimeStamp:
                    case DataTypes.GSDTimeStampDifference:
                    case DataTypes.GSDTimeStampDifferenceShort:
                    case DataTypes.GSDUnicodeString8:
                    case DataTypes.GSDString61131:
                    case DataTypes.GSDWstring61131:
                        {
                            return val;
                        }

                    default:
                        {
                            return null;	// ---------->
                        }
                }
            }
            catch (ArgumentException)
            {
                return null;	// ---------->
            }
            catch (FormatException)
            {
                return null;	// ---------->
            }
            catch (InvalidCastException)
            {
                return null;	// ---------->
            }
            catch (OverflowException)
            {
                return null;	// ---------->
            }
        }


        /// <summary>
        /// Builds an area string for the given data type. For the bit area data type
        /// the length in bits is needed to build correct area string.
        /// </summary>
        /// <param name="type">Data type for which the area string should be built.</param>
        /// <param name="bitlength">Length in bit for the data type bit area.</param>
        /// <returns>Valid area string for the given data type. Empty string if a
        /// problem occurres.</returns>
        public static string GetAreaByDataType(DataTypes type, uint bitlength)
        {
            try { 
            switch (type)
            {
                case DataTypes.GSDBit:
                    {
                        return "0" + Constants.s_DoubleDot + "1";
                    }
                case DataTypes.GSDBitArea:
                    {
                        if (bitlength < Constants.BitArea[Constants.s_MinIndex] ||
                            bitlength > Constants.BitArea[Constants.s_MaxIndex])
                        {
                            break;
                        }

                        return "0" + Constants.s_DoubleDot + Constants.BitAreaMaximumValues[bitlength];
                    }
                case DataTypes.GSDUnsigned8:
                    {
                        return System.Xml.XmlConvert.ToString(Byte.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(Byte.MaxValue);
                    }
                case DataTypes.GSDUnsigned16:
                case DataTypes.Gsdr2:
                case DataTypes.Gsdt2:
                case DataTypes.Gsdd2:
                    {
                        return System.Xml.XmlConvert.ToString(UInt16.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(UInt16.MaxValue);
                    }
                case DataTypes.GSDUnsigned32:
                case DataTypes.Gsdt4:
                    {
                        return System.Xml.XmlConvert.ToString(UInt32.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(UInt32.MaxValue);
                    }
                case DataTypes.GSDUnsigned64:
                    {
                        return System.Xml.XmlConvert.ToString(UInt64.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(UInt64.MaxValue);
                    }
                case DataTypes.GSDInteger8:
                    {
                        return System.Xml.XmlConvert.ToString(SByte.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(SByte.MaxValue);
                    }
                case DataTypes.GSDInteger16:
                case DataTypes.Gsdn2:
                case DataTypes.Gsdx2:
                    {
                        return System.Xml.XmlConvert.ToString(Int16.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(Int16.MaxValue);
                    }
                case DataTypes.GSDInteger32:
                case DataTypes.Gsdn4:
                case DataTypes.Gsdx4:
                    {
                        return System.Xml.XmlConvert.ToString(Int32.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(Int32.MaxValue);
                    }
                case DataTypes.GSDInteger64:
                    {
                        return System.Xml.XmlConvert.ToString(Int64.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(Int64.MaxValue);
                    }
                case DataTypes.GSDFloat32:
                    {
                        return System.Xml.XmlConvert.ToString(Single.MinValue) + Constants.s_DoubleDot + System.Xml.XmlConvert.ToString(Single.MaxValue);
                    }
                case DataTypes.GSDFloat64:
                    {
                        // The minimum and maximum values of float64 must be omitted from the final digit after the decimal point,
                        // because otherwise it comes to an overflow later in the conversion of string back to float64.
                        return "-1.7976931348623E+308" + Constants.s_DoubleDot + "1.7976931348623E+308";
                    }
                case DataTypes.Gsde2:
                    {
                        return "-255.9921875" + Constants.s_DoubleDot + "255.9921875";
                    }
                case DataTypes.Gsdc4:
                    {
                        return "-214748.3648" + Constants.s_DoubleDot + "214748.3647";
                    }
                case DataTypes.GSDUnipolar216:
                    {
                        return "0" + Constants.s_DoubleDot + "399.999938965";
                    }
                case DataTypes.GSDDate:
                    {
                        return "0000-00-00T00:00:00.000" + Constants.s_DoubleDot + "9999-12-31T23:59:59.999";
                    }
                case DataTypes.GSDNetworkTime:
                    {
                        return "0000-01-01T00:00:00.000000000" + Constants.s_DoubleDot + "9999-12-31T23:59:59.999999999";
                    }
                case DataTypes.GSDNetworkTimeDifference:
                    {
                        return "-P24855DT00H00M00.000S" + Constants.s_DoubleDot + "P24855DT00H00M00.000S";
                    }
                case DataTypes.GSDTimeDifferenceWithDateIndication:
                    {
                        return "P00000DT00H00M00.000S" + Constants.s_DoubleDot + "P65535DT23H59M59.999S";
                    }
                case DataTypes.GSDTimeDifferenceWithoutDateIndication:
                    {
                        return "P00H00M00.000S" + Constants.s_DoubleDot + "P23H59M59.999S";
                    }
                case DataTypes.GSDTimeOfDayWithDateIndication:
                    {
                        return "0000-00-00T00:00:00.000" + Constants.s_DoubleDot + "9999-12-31T23:59:59.999";
                    }
                case DataTypes.GSDTimeOfDayWithoutDateIndication:
                    {
                        return "00:00:00.000" + Constants.s_DoubleDot + "23:59:59.999";
                    }
                    default:
                    {
                        break;
                    }
            }
            }
            catch (IndexOutOfRangeException)
            {
            }
            catch (ArgumentException)
            {
            }
            return String.Empty;
        }

        /// <summary>
        /// Checks if the DataTypeEnum string (from GSDML document) corresponds 
        /// to a numerical DataType.
        /// </summary>
        /// <param name="enumvalue">DataTypeEnum GSDML entry.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an unknown or empty enum value string</exception>
        /// <returns>True if DataType is numerical.</returns>
        public static bool IsDataTypeNumerical(string enumvalue)
        {
            #region Argument Preconditions
            if (enumvalue == null)
                throw new ArgumentNullException(nameof(enumvalue));
            if (enumvalue.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(enumvalue));
            #endregion

            switch (enumvalue)
            {
                case s_Bit:
                case s_BitArea:
                case s_Integer8:
                case s_Integer16:
                case s_Integer32:
                case s_Integer64:
                case s_Unsigned8:
                case s_Unsigned16:
                case s_Unsigned32:
                case s_Unsigned64:
                case s_Float32:
                case s_Float64:
                case s_E2:                                  // new in V2.32
                case s_C4:                                  // new in V2.32
                case s_Unipolar216:                        // new in V2.32
                case s_N2:                                  // new in V2.32
                case s_N4:                                  // new in V2.32
                case s_R2:                                  // new in V2.32
                case s_T2:                                  // new in V2.32
                case s_T4:                                  // new in V2.32
                case s_D2:                                  // new in V2.32
                case s_X2:                                  // new in V2.32
                case s_X4:                                  // new in V2.32
                    {
                        return true;
                    }
                default:
                    {
                        return false;
                    }
            }
        }

        #endregion

    }
}


