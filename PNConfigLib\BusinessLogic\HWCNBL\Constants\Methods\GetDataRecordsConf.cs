/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: GetDataRecordsConf.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.Methods
{
    /// <summary>
    /// Contains constants and types for the generic method: GetDataRecordsConf
    /// </summary>
    internal static class GetDataRecordsConf
    {
        /// <summary>
        /// The interface submodule of the IO controller that the module is connected to.
        /// Type: Interface
        /// </summary>
        public const string ioControllerInterface = "IOControllerInterface";

        /// <summary>
        /// The module whose data records will be retrieved.
        /// Type: PclObject
        /// </summary>
        public const string Module = "Module";

        /// <summary>
        /// Name of the method.
        /// </summary>
        public const string Name = "GetDataRecordsConf";
    }
}