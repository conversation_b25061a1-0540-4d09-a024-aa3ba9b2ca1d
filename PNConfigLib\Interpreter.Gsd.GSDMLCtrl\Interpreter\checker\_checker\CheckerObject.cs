/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerObject.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces

using System.IO;
using System.Collections.Specialized;
using System.Collections.Generic;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// The CheckerObject is the base class for all internal checker classes. It 
    /// is needed, because there is a must, that every internal checker object can 
    /// be accessed by the main checker by a common way.
    /// </summary>
   abstract internal class CheckerObject
    {
        public static readonly Dictionary<string, string> ElementDescriptions = new Dictionary<string, string>();
        /// <summary>
        /// Accesses the GSDML version supported from the internal checker.
        /// </summary>
        abstract public string SupportedGsdmlVersion { get; }


        /// <summary>
        /// Checks the given GSD(ML) document and reports problems at the report
        /// store mechanism.
        /// </summary>
        /// <param name="gsddocstream">Stream of the GSD(ML) document to check.</param>
        /// <param name="name">The name of the GSD to check. Can be 'null' also.</param>
        /// <param name="store">Store object which holds eventually created reports.</param>
        /// <param name="customArguments">Custom Arguments</param>
        /// <returns>True whether check was successful, else false.</returns>
        abstract public bool Check(Stream gsddocstream, string name, ReportStore store, NameValueCollection customArguments);

        /// <summary>
        /// Checks the given GSD(ML) document and returns whether the check was
        /// successful or not.
        /// </summary>
        /// <param name="gsddocstream">The stream of the GSD(ML) document to check.</param>
        /// <param name="name">The name of the GSD to check.</param>
        /// <param name="schemaPath">Path to GSDML schema.</param>
        /// <param name="store">The store, to which the created reports should
        /// be announced, if problems occur during the check.</param>
        /// <returns>True, if check was successful, else false.</returns>
        abstract public bool Check(Stream gsddocstream, string name, string schemaPath, ReportStore store);

        /// <summary>
        /// Gets the name of the matching schema file (correct GSDML version)
        /// </summary>
        /// <returns>Name of the schema file</returns>
        abstract protected string GetSchemaFileName();

        /// <summary>
        /// Gets the name of the matching primitives schema file (correct GSDML version)
        /// </summary>
        /// <returns>Name of the schema file</returns>
		abstract protected string GetPrimitivesFileName();

        /// <summary>
        /// Gets the name of the matching xml schema file (correct GSDML version)
        /// </summary>
        /// <returns>Name of the schema file</returns>
		abstract protected string GetXmlSchemaFileName();

        /// <summary>
        /// Gets the name of the matching signatur schema file (correct GSDML version)
        /// </summary>
        /// <returns>Name of the schema file</returns>
		abstract protected string GetSignatureSchemaFileName();
    }

}


