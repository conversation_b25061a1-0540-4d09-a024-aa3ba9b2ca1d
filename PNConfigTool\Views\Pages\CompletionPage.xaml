<Page x:Class="PNConfigTool.Views.Pages.CompletionPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="完成"
      Loaded="Page_Loaded">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="#E8F4FD" BorderBrush="#B0D4F1" BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 完成图标 -->
                <Border Grid.Column="0" Background="#4CAF50" CornerRadius="25" Width="50" Height="50" Margin="0,0,15,0">
                    <TextBlock Text="✓" FontSize="24" FontWeight="Bold" Foreground="White" 
                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="配置完成" FontSize="24" FontWeight="Bold" Foreground="#2E7D32"/>
                    <TextBlock x:Name="ProjectNameTextBlock" Text="项目名称" FontSize="14" Foreground="#666" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 设备信息区域 -->
        <Border Grid.Row="1" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 设备图标 -->
                <Border Grid.Column="0" Background="#E8F4FD" CornerRadius="5" Padding="10" Margin="0,0,15,0">
                    <Rectangle Width="40" Height="30" Fill="#2196F3" RadiusX="2" RadiusY="2"/>
                </Border>
                
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock x:Name="DeviceNameTextBlock" Text="设备名称" FontSize="16" FontWeight="SemiBold"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 地址总览表格 - 暂时屏蔽 -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Margin="20" Visibility="Collapsed">
            <StackPanel>
                <!-- 表格标题 -->
                <TextBlock Text="地址总览" FontSize="18" FontWeight="SemiBold"
                           Margin="0,0,0,15" Foreground="#333"/>

                <!-- 数据表格 -->
                <DataGrid x:Name="AddressDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="True"
                          CanUserResizeColumns="True"
                          CanUserResizeRows="False"
                          CanUserSortColumns="True"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          BorderBrush="#CCCCCC"
                          BorderThickness="1"
                          Background="White"
                          AlternatingRowBackground="#F8F9FA"
                          Height="300"
                          ColumnHeaderHeight="30">

                    <DataGrid.Columns>
                        <!-- 设备序号 -->
                        <DataGridTextColumn Header="设备序号" Binding="{Binding DeviceIndex}"
                                            Width="80" MinWidth="60"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- API -->
                        <DataGridTextColumn Header="API" Binding="{Binding API}"
                                            Width="60" MinWidth="50"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- 设备名 -->
                        <DataGridTextColumn Header="设备名" Binding="{Binding DeviceName}"
                                            Width="150" MinWidth="100"/>

                        <!-- 模块 -->
                        <DataGridTextColumn Header="模块" Binding="{Binding ModuleName}"
                                            Width="120" MinWidth="80"/>

                        <!-- 插槽_子插槽 -->
                        <DataGridTextColumn Header="插槽_子插槽" Binding="{Binding SlotSubslot}"
                                            Width="100" MinWidth="80"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- IO类型 -->
                        <DataGridTextColumn Header="IO类型" Binding="{Binding IOType}"
                                            Width="80" MinWidth="60"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- 起始地址 -->
                        <DataGridTextColumn Header="起始地址" Binding="{Binding StartAddress}"
                                            Width="80" MinWidth="70"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- 结束地址 -->
                        <DataGridTextColumn Header="结束地址" Binding="{Binding EndAddress}"
                                            Width="80" MinWidth="70"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>

                        <!-- 长度 -->
                        <DataGridTextColumn Header="长度" Binding="{Binding Length}"
                                            Width="*"
                                            ElementStyle="{StaticResource CenterAlignedTextBlock}"/>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="3" Background="#F0F0F0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧按钮 -->
            <Button x:Name="PreviousButton" Grid.Column="0" Content="&lt; 上一步" Width="100" 
                    Margin="10" Padding="10,5" Click="PreviousButton_Click"/>
            
            <!-- 右侧按钮 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="10">
                <Button x:Name="GenerateButton" Content="生成" Width="100" Padding="10,5" 
                        Margin="0,0,10,0" Click="GenerateButton_Click"/>
                <Button x:Name="RunPNConfigRunner" Content="运行PNConfigRunner" Width="150" Padding="10,5" 
                        Margin="0,0,10,0" Click="RunGenerateButton_Click" IsEnabled="False"/>
                <Button x:Name="SaveButton" Content="保存" Width="100" Padding="10,5" 
                        Margin="0,0,10,0" Click="SaveButton_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
