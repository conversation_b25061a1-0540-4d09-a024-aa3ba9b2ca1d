<Page x:Class="PNConfigTool.Views.Pages.NetworkConfigPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Loaded="Page_Loaded"
      Title="PROFINET 配置向导">

    <Grid Background="White" Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" Text="PROFINET 网络配置" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>

        <!-- 创建新项目提示 -->
        <Border Grid.Row="1" x:Name="NoProjectPanel" Background="#FFF5F5F5" Margin="0,10,0,0" Padding="15" BorderBrush="#FFE0E0E0" BorderThickness="1" CornerRadius="5" Visibility="Collapsed">
            <StackPanel>
                <TextBlock Text="没有打开项目" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15">
                    要配置 PROFINET 网络，您需要先创建或打开一个项目。请选择以下操作：
                </TextBlock>
                <StackPanel Orientation="Horizontal" Margin="0,5">
                    <Button Content="新建项目" Width="120" Height="30" Margin="0,0,15,0" Click="CreateNewProject_Click">
                    </Button>
                    <Button Content="打开项目" Width="120" Height="30" Click="OpenProject_Click">
                    </Button>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="1" x:Name="ConfigScrollViewer" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10">
                <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                    此向导允许您选择步配置 PROFINET 网络。PROFINET 配置信息会在项目中生成并存储，可以和项目一起下载到 PLC 中。
                </TextBlock>
                
                <!-- PLC角色 -->
                <GroupBox Header="PLC角色" Margin="0,10">
                    <StackPanel Margin="5">
                        <TextBlock Text="选择PLC的角色" Margin="0,5"/>
                        <CheckBox x:Name="ControllerCheckBox" Content="控制器" Margin="0,5" Checked="RoleCheckBox_Checked"/>
                        <CheckBox x:Name="IntelligentDeviceCheckBox" Content="智能设备" Margin="0,5" Checked="RoleCheckBox_Checked"/>
                        <CheckBox x:Name="PortBasedCheckBox" Content="PROFINET 接口参数由上位控制器分配" Margin="20,5,0,5" 
                                  IsEnabled="{Binding ElementName=IntelligentDeviceCheckBox, Path=IsChecked}"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 以太网端口 -->
                <GroupBox x:Name="EthernetPortGroup" Header="以太网端口" Margin="0,10" IsEnabled="False">
                    <StackPanel Margin="5">
                        <RadioButton x:Name="FixedIPRadioButton" Content="固定IP 地址和站名" Margin="0,5" IsChecked="True"/>
                        <Grid Margin="20,5,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="IP 地址:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,5">
                                <TextBox x:Name="IPAddressBox1" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="IPTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="IPAddressBox2" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="IPTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="IPAddressBox3" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="IPTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="IPAddressBox4" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="IPTextBox_TextChanged"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="子网掩码:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,5">
                                <TextBox x:Name="SubnetMaskBox1" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="SubnetMaskTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="SubnetMaskBox2" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="SubnetMaskTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="SubnetMaskBox3" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="SubnetMaskTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="SubnetMaskBox4" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="SubnetMaskTextBox_TextChanged"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="默认网关:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,5">
                                <TextBox x:Name="GatewayBox1" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="GatewayTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="GatewayBox2" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="GatewayTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="GatewayBox3" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="GatewayTextBox_TextChanged"/>
                                <TextBlock Text="." Margin="2,0"/>
                                <TextBox x:Name="GatewayBox4" Width="50" MaxLength="3" PreviewTextInput="NumberValidationTextBox" TextChanged="GatewayTextBox_TextChanged"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="站名:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <TextBox Grid.Row="3" Grid.Column="1" x:Name="StationNameTextBox" Text="Controller" Margin="0,5"/>
                        </Grid>
                        
                        <RadioButton x:Name="DHCPRadioButton" Content="从其他途径获取IP 地址" Margin="0,5" Visibility="Collapsed"/>
                    </StackPanel>
                </GroupBox>
                
              
                <!-- 通信 -->
                <GroupBox x:Name="CommunicationGroup" Header="通信" Margin="0,10" IsEnabled="False">
                    <Grid Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="发送时间:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                            <ComboBox x:Name="SendTimeComboBox" Width="100" SelectedIndex="0" Margin="0,5">
                                <ComboBoxItem Content="1.000"/>
                            </ComboBox>
                            <TextBlock Text="ms" VerticalAlignment="Center" Margin="5,0,0,0"/>
                        </StackPanel>
                        

                    </Grid>
                </GroupBox>
                <!-- Media redundancy -->
                <GroupBox x:Name="MediaRedundancyGroup" Header="Media redundancy" Margin="0,10" IsEnabled="False">
                    <StackPanel Margin="5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="MRP domain:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" x:Name="MrpDomainTextBox" Margin="0,5" Text="mrpdomain-1" IsReadOnly="True"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Media redundancy role:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" x:Name="MrpRoleComboBox" Width="200" HorizontalAlignment="Left" Margin="0,5">
                                <ComboBoxItem Content="Not device in the ring" IsSelected="True"/>
                                <ComboBoxItem Content="MrpManager"/>
                                <ComboBoxItem Content="MrpClient"/>
                                <ComboBoxItem Content="MrpAutoManager"/>
                            </ComboBox>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Ring port 1:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="RingPort1ComboBox" Width="200" HorizontalAlignment="Left" Margin="0,5" SelectionChanged="RingPort1ComboBox_SelectionChanged">
                                <ComboBoxItem Content="Port1"/>
                                <ComboBoxItem Content="Port2"/>
                            </ComboBox>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Ring port 2:" VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <ComboBox Grid.Row="3" Grid.Column="1" x:Name="RingPort2ComboBox" Width="200" HorizontalAlignment="Left" Margin="0,5" SelectionChanged="RingPort2ComboBox_SelectionChanged">
                                <ComboBoxItem Content="Port1"/>
                                <ComboBoxItem Content="Port2"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left">
            <Button x:Name="PreviousStepButton" Content="&lt; 上一步" Width="100" Padding="10,5" Margin="5" Click="PreviousStepButton_Click"/>
            <Button x:Name="PreviousButton" Content="&lt; 上一步" Width="100" Padding="10,5" Margin="5" Click="PreviousButton_Click" Visibility="Collapsed"/>
            <Button x:Name="NextButton" Content="下一步 >" Width="100" Padding="10,5" Margin="5" Click="NextButton_Click"/>
        </StackPanel>
    </Grid>
</Page>