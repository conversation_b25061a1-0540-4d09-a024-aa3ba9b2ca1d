/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerHelper.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

using Subnet = PNConfigLib.DataModel.PCLObjects.Subnet;

#endregion

namespace PNConfigLib.PNPlannerAdapter
{
    /// <summary>
    /// Helper methods / classes / definitions for PNPlanner algorithm.
    /// Used by readers and writers.
    /// </summary>
    internal class PNPlannerHelper
    {
        /// <summary>
        /// Keeps the information whether Fast Fowarding is supported or not. It is set to the value returned from
        /// the preplanner. It is used in PNPlannerInputWriter do write the input for the scheduler.
        /// </summary>
        internal bool FastForwarding;

        /// <summary>
        /// Keeps the information for the frame preamble. It is set to the value returned from the preplanner.
        ///  It is used in PNPlannerInputWriter do write the input for the scheduler.
        /// </summary>
        internal uint FramePreamble;

        /// <summary>
        /// Keeps the information whether redundant pathes do exists. It is used in PNPlannerOutputReader to
        /// determine whether rxports need to be parsed for redundant messages.
        /// </summary>
        internal bool RedundancyExists;

        /// <summary>
        /// Keeps the information for the start time.  It is used in PNPlannerInputWriter do write the input
        /// for the scheduler.
        /// </summary>
        internal uint StartTime;

        /// <summary>
        /// Contains the Startup Mode determined by the preplanner. It is used in PNPlannerInputWriter.
        /// to write the input for the scheduler.
        /// </summary>
        internal string StartupMode;

        public PNPlannerHelper(SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            SyncDomain = syncDomainBusinessLogic;
        }

        /// <summary>
        /// The business logic class for the sync domain used in PNPlanner.
        /// </summary>
        private SyncDomainBusinessLogic SyncDomain { get; }

        /// <summary>
        /// Gets the name of the switch to be used under the Switch element.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose name will be retrieved.</param>
        /// <returns></returns>
        public static string GetSwitchName(Interface interfaceSubmodule)
        {
            return interfaceSubmodule == null
                       ? string.Empty
                       : AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
        }

        /// <summary>
        ///   Finds the maximum frame length of all RT-Class 1 & 2 frames of the sync-domain.
        ///   The frame length is calculated starting from the ethernet header up to and including the frame check
        ///   sequence.
        /// </summary>
        /// <param name="allPNFrames">List, which contains lists of frames of a controller and its devices.</param>
        /// <returns></returns>
        public uint GetMaxRTC12FrameLength(InterfaceFrameDataList allPNFrames)
        {
            long maxFrameLength = 0;
            // Check all RT Class 1 & 2 frames
            foreach (KeyValuePair<Interface, List<IPNFrameData>> frameListsOfControllers in allPNFrames.AllPNFrames)
            {
                foreach (IPNFrameData pnFrameData in frameListsOfControllers.Value)
                {
                    if ((pnFrameData.FrameClass != (long)PNIOFrameClass.Class1Frame)
                        && (pnFrameData.FrameClass != (long)PNIOFrameClass.Class2Frame))
                    {
                        continue;
                    }
                    maxFrameLength = Math.Max(pnFrameData.GetTotalFrameLength(false), maxFrameLength);
                }
            }
            // subtract IFG (12 Byte), 7 Bytes Preamble and SFD (1 Byte)
            if (maxFrameLength > 20)
            {
                maxFrameLength -= 20;
            }
            return Convert.ToUInt32(maxFrameLength, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Creates a dictionary for connected interfaces and IO devices in a given sync domain.
        /// </summary>
        /// <param name="syncdomainParticipants">The sync domain that contains the interfaces whose mapping will be made.</param>
        /// <returns>Dictionary that contains the id's of connected interfaces and IO devices.</returns>
        internal static Dictionary<int, int> BuildInterfaceAndIODeviceMapping(List<Interface> syncdomainParticipants)
        {
            Dictionary<int, int> interfaceIdIoConnectorIdMapping = new Dictionary<int, int>();

            foreach (Interface deviceInterfaceSubmodule in syncdomainParticipants)
            {
                if (deviceInterfaceSubmodule.PNIOD != null)
                {
                    interfaceIdIoConnectorIdMapping.Add(deviceInterfaceSubmodule.GetHashCode(), deviceInterfaceSubmodule.PNIOD.GetHashCode());
                }
            }

            return interfaceIdIoConnectorIdMapping;
        }

        /// <summary>
        /// Fills the fields of dfp frame instance from the original rtc3 frames of it.
        /// </summary>
        /// <param name="dfpFrameData"></param>
        /// <param name="pnFrameDataMessageIds"></param>
        /// <returns>Original RTC3 frames which must be removed.</returns>
        internal List<IPNFrameData> FillDfPFrame(PNDfpFrameData dfpFrameData, Dictionary<long, IPNFrameData> pnFrameDataMessageIds)
        {
            // Find the original rtc3 frames.
            List<IPNFrameData> originalFrames = new List<IPNFrameData>();
            foreach (IPNSubframeData subframeData in dfpFrameData.Subframes.Values)
            {
                PNSubframeData data = subframeData as PNSubframeData;
                originalFrames.Add(pnFrameDataMessageIds[data.OriginalFrameId]);
            }

            // Find the smallest station number
            int minStationNo = int.MaxValue;
            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.StationNumber < minStationNo)
                {
                    minStationNo = originalFrame.StationNumber;
                }
            }
            dfpFrameData.StationNumber = minStationNo;

            // Fill the FrameType with 0 (DeviceFrame)
            dfpFrameData.FrameType = 0;

            // dfp frame is also an rtc3 frame
            dfpFrameData.FrameClass = 3;

            // Min frame interval must be taken from the frame with the largest value
            long minFrameInterval = long.MinValue;
            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.MinFrameIntervall > minFrameInterval)
                {
                    minFrameInterval = originalFrame.MinFrameIntervall;
                }
            }
            dfpFrameData.MinFrameIntervall = minFrameInterval;

            // Reduction ratios and supported send clock factor of the dfp frame 
            // must be the intersection of the original frames
            List<long>[] rrNonPowList = new List<long>[originalFrames.Count];
            List<long>[] rrPowList = new List<long>[originalFrames.Count];
            List<long>[] scfList = new List<long>[originalFrames.Count];
            for (int i = 0; i < originalFrames.Count; i++)
            {
                IPNFrameData nextFrame = originalFrames[i];
                rrNonPowList[i] = (List<long>)nextFrame.SuppRRNonPow;
                rrPowList[i] = (List<long>)nextFrame.SuppRRPow;
                scfList[i] = (List<long>)nextFrame.SuppSendClockFactors;
            }
            dfpFrameData.SuppRRNonPow = GetIntersection(rrNonPowList);
            dfpFrameData.SuppRRPow = GetIntersection(rrPowList);
            dfpFrameData.SuppSendClockFactors = GetIntersection(scfList);

            // Get the max. UpdateTimeMode
            byte updateTimeMode = byte.MinValue;
            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.UpdateTimeMode > updateTimeMode)
                {
                    updateTimeMode = originalFrame.UpdateTimeMode;
                }
            }
            dfpFrameData.UpdateTimeMode = updateTimeMode;

            // Check if any of the frame is using data
            bool hasUsingData = false;
            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.HasUsingData)
                {
                    hasUsingData = true;
                    break;
                }
            }
            dfpFrameData.HasUsingData = hasUsingData;

            // SlotNo and SubslotNo of the frame will be filled with the original frame which has the min. slot number.
            int slotNo = int.MaxValue;
            int subslotNo = int.MaxValue;
            foreach (IPNFrameData originalFrame in originalFrames)
            {
                if (originalFrame.SlotNumber < slotNo)
                {
                    slotNo = originalFrame.SlotNumber;
                    subslotNo = originalFrame.SubSlotNumber;
                }
            }
            dfpFrameData.SlotNumber = slotNo;
            dfpFrameData.SubSlotNumber = subslotNo;

            // The following fields must be the same in the original frames. Copy the values from the first frame.
            IPNFrameData firstOrigFrame = originalFrames[0];
            dfpFrameData.FrameDirection = firstOrigFrame.FrameDirection;
            dfpFrameData.DeviceLocalReductionRatio = firstOrigFrame.DeviceLocalReductionRatio;
            dfpFrameData.SendClockFactor = firstOrigFrame.SendClockFactor;
            // It is not allowed to create a pack group using ring nodes together with non-ring nodes, 
            // so framemultiplier must be same
            dfpFrameData.FrameMultiplier = firstOrigFrame.FrameMultiplier;
            dfpFrameData.FixedPhaseNumber = firstOrigFrame.FixedPhaseNumber;
            dfpFrameData.ProxyNumber = firstOrigFrame.ProxyNumber;

            // IoSyncRole doesn't have to be the same for all frames, but it makes no difference in this context. 
            // SLC only uses it to determine if the frame is synchronized (it must be so in this case). 
            // Copy the value from the first frame.
            dfpFrameData.IoSyncRole = firstOrigFrame.IoSyncRole;
            dfpFrameData.WatchdogFactor = firstOrigFrame.WatchdogFactor;
            dfpFrameData.DistributedWdFactor = (uint)firstOrigFrame.WatchdogFactor;
            dfpFrameData.DataHoldFactor = firstOrigFrame.DataHoldFactor;

            // Get the redundancy status. Normally, either all or none of the original frames must be redundant. 
            // Check only the first original frame.
            if (firstOrigFrame.RedundantFrameID != 0)
            {
                // Signal for DFP + MRPD. The redundant frame id will be assigned later.
                dfpFrameData.RedundantFrameID = 1;
            }

            return originalFrames;
        }

        /// <summary>
        /// Finds the receiver of a given PNFrameData object.
        /// </summary>
        /// <param name="frameData">The provided PNFrameData object.</param>
        /// <param name="syncDomainBl">The sync domain that contains the receiver.</param>
        /// <param name="controllerInterface">The controller interface that this frame is created for.</param>
        /// <param name="interfaceIdIoConnectorIdMapping">
        /// The dictionary that contains the mapping of connections between
        /// interfaces and connectors.
        /// </param>
        /// <returns>Receiver interface submodules. Controller will at the beginning of the list if it is a dfp frame.</returns>
        internal static List<Interface> GetReceiversOfFrame(IPNFrameData frameData, SyncDomainBusinessLogic syncDomainBl, Interface controllerInterface, Dictionary<int, int> interfaceIdIoConnectorIdMapping)
        {
            IList<Interface> controllerInterfaceSubmodules = syncDomainBl.IOControllerInterfaces;
            List<Interface> receivers = new List<Interface>();

            if (controllerInterfaceSubmodules == null)
            {
                return receivers;
            }

            if (frameData.CoreIds.Count == 1)
            {
                // One of the controller interface submodules or 
                // one of its device interface submodules is the only receiver.
                foreach (Interface controllerInterfaceSubmodule in controllerInterfaceSubmodules)
                {
                    if (!controllerInterfaceSubmodule.Equals(controllerInterface))
                    {
                        continue;
                    }
                    List<Interface> deviceInterfaceSubmodules =
                        GetDeviceInterfacesOfIoDevices(
                            controllerInterfaceSubmodule,
                            new List<int> { frameData.CoreId },
                            syncDomainBl,
                            interfaceIdIoConnectorIdMapping);

                    if ((deviceInterfaceSubmodules == null)
                        || (deviceInterfaceSubmodules.Count == 0))
                    {
                        // Skipped CCDX related code here.

                        if (frameData.FrameType == (int)PNPlannerFrameType.IDeviceFrame)
                        {
                            // No need to add receiver for iDevice frame.
                            continue;
                        }

                        deviceInterfaceSubmodules = NavigationUtilities.GetConnectedInterfaces(controllerInterfaceSubmodule);

                        if ((deviceInterfaceSubmodules == null)
                            || (deviceInterfaceSubmodules.Count == 0))
                        {
                            continue;
                        }

                        receivers.Add(frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? controllerInterfaceSubmodule : deviceInterfaceSubmodules[0]);

                        break;
                    }

                    // Receivers are found
                    receivers.Add(frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? controllerInterfaceSubmodule : deviceInterfaceSubmodules[0]);
                    break;
                }
            }
            else
            {
                // There are more than one receivers.
                IPNDfpFrameData dfpFrameData = frameData as IPNDfpFrameData;
                if (dfpFrameData != null)
                {
                    // Depending on the frame direction receivers may be one of the following: 
                    // InputFrame: Receivers are the iodevices other than the initiator of the frame,
                    // i.e. the one with the largest sfid, and the ioController.
                    // OutputFrame: Receivers are the iodevices.

                    List<int> subframeCoreIds = new List<int>();
                    int subframeCount = dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? dfpFrameData.Subframes.Count - 1 : dfpFrameData.Subframes.Count;
                    for (int i = 0; i < subframeCount; i++)
                    {
                        subframeCoreIds.Add(dfpFrameData.Subframes.Values[i].CoreId);
                    }

                    foreach (Interface controllerInterfaceSubmodule in controllerInterfaceSubmodules)
                    {
                        if (!controllerInterfaceSubmodule.Equals(controllerInterface))
                        {
                            continue;
                        }
                        List<Interface> deviceInterfaceSubmodules =
                            GetDeviceInterfacesOfIoDevices(
                                controllerInterfaceSubmodule,
                                subframeCoreIds,
                                syncDomainBl,
                                interfaceIdIoConnectorIdMapping);
                        if ((deviceInterfaceSubmodules == null)
                            || (deviceInterfaceSubmodules.Count == 0))
                        {
                            continue;
                        }

                        // Receivers are found
                        if (frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame)
                        {
                            receivers.Add(controllerInterfaceSubmodule);
                        }

                        receivers.AddRange(deviceInterfaceSubmodules);
                        break;
                    }
                }
                else
                {
                    List<int> coreIds = new List<int>();
                    foreach (int coreId in frameData.CoreIds)
                    {
                        coreIds.Add(coreId);
                    }

                    foreach (Interface controllerInterfaceSubmodule in controllerInterfaceSubmodules)
                    {
                        if (!controllerInterfaceSubmodule.Equals(controllerInterface))
                        {
                            continue;
                        }
                        List<Interface> deviceInterfaceSubmodules =
                            GetDeviceInterfacesOfIoDevices(
                                controllerInterfaceSubmodule,
                                coreIds,
                                syncDomainBl,
                                interfaceIdIoConnectorIdMapping);
                        if ((deviceInterfaceSubmodules == null)
                            || (deviceInterfaceSubmodules.Count == 0))
                        {
                            continue;
                        }

                        receivers.Add(frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? controllerInterfaceSubmodule : deviceInterfaceSubmodules[0]);
                        break;
                    }
                }
            }

            return receivers;
        }

        /// <summary>
        /// Finds the sender of a given PNFrameData object.
        /// </summary>
        /// <param name="frameData">The provided PNFrameData object.</param>
        /// <param name="syncDomainBl">The sync domain that contains the sender.</param>
        /// <param name="controllerInterface">The controller interface that this frame is created for.</param>
        /// <param name="interfaceIdIoConnectorIdMapping">
        /// The dictionary that contains the mapping of connections between
        /// interfaces and connectors.
        /// </param>
        /// <returns>The sender interface submodule.</returns>
        internal static Interface GetSenderOfFrame(
            IPNFrameData frameData,
            SyncDomainBusinessLogic syncDomainBl,
            Interface controllerInterface,
            Dictionary<int, int> interfaceIdIoConnectorIdMapping)
        {
            Interface senderSubmodule = null;
            IList<Interface> controllerInterfaceSubmodules = syncDomainBl.IOControllerInterfaces;

            if (controllerInterfaceSubmodules == null)
            {
                return null;
            }

            foreach (Interface controllerInterfaceSubmodule in controllerInterfaceSubmodules)
            {
                if (!controllerInterfaceSubmodule.Equals(controllerInterface))
                {
                    continue;
                }

                int coreIdToUse = frameData.CoreIds[0];
                if (frameData.CoreIds.Count > 1)
                {
                    // Possible Dfp Frame
                    IPNDfpFrameData dfpFrameData = frameData as IPNDfpFrameData;
                    if (dfpFrameData != null)
                    {
                        // The sender is the initiator of the frame, i.e. the one with the largest sfid.
                        // It may also be the controller, depending on the frame direction.
                        IPNSubframeData lastSubframe = dfpFrameData.Subframes.Values[dfpFrameData.Subframes.Count - 1];
                        coreIdToUse = lastSubframe.CoreId;
                    }
                    else
                    {
                        List<int> coreIds = new List<int>();
                        foreach (int coreId in frameData.CoreIds)
                        {
                            coreIds.Add(coreId);
                        }

                        if (controllerInterfaceSubmodule.Equals(controllerInterface))
                        {
                            List<Interface> interfaceSubmodules =
                                GetDeviceInterfacesOfIoDevices(
                                    controllerInterfaceSubmodule,
                                    coreIds,
                                    syncDomainBl,
                                    interfaceIdIoConnectorIdMapping);
                            if ((interfaceSubmodules == null)
                                || (interfaceSubmodules.Count == 0))
                            {
                                continue;
                            }

                            if (interfaceSubmodules.Count == 1)
                            {
                                senderSubmodule = frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? interfaceSubmodules[0] : controllerInterfaceSubmodule;
                            }

                            break;
                        }
                    }
                }

                List<Interface> deviceInterfaceSubmodules = GetDeviceInterfacesOfIoDevices(
                    controllerInterfaceSubmodule,
                    new List<int> { coreIdToUse },
                    syncDomainBl,
                    interfaceIdIoConnectorIdMapping);

                if ((deviceInterfaceSubmodules == null)
                    || (deviceInterfaceSubmodules.Count == 0))
                {
                    // Check for Bandwidth reservation frames (Shared device or iDevice frame.)
                    if (((frameData.FrameType == (int)PNPlannerFrameType.IDeviceFrame) || (frameData.FrameType == (int)PNPlannerFrameType.SharedFrame))
                        && (deviceInterfaceSubmodules != null))
                    {
                        // Add the iDevice itself as sender for iDevice frame.
                        deviceInterfaceSubmodules.Insert(0, controllerInterfaceSubmodule);
                    }
                    else
                    {
                        deviceInterfaceSubmodules = NavigationUtilities.GetConnectedInterfaces(controllerInterfaceSubmodule);
                    }

                    if ((deviceInterfaceSubmodules == null)
                        || (deviceInterfaceSubmodules.Count == 0))
                    {
                        continue;
                    }

                    senderSubmodule = frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? deviceInterfaceSubmodules[0] : controllerInterfaceSubmodule;
                    break;
                }

                // Sender is found
                senderSubmodule = frameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? deviceInterfaceSubmodules[0] : controllerInterfaceSubmodule;
                break;
            }

            return senderSubmodule;
        }

        /// <summary>
        /// The method goes through all Controller Interface Submodules and
        /// builds all IrtTop Islands in the Sync-Domain.
        /// </summary>
        /// <param name="topIsland">Output parameter that returns the created topIsland.</param>
        /// <param name="allFoundPNFrames">A dictionary of all found PN frames.</param>
        /// <returns>The result of the algorithm.</returns>
        internal PNPlannerResult CreateTopIsland(out InterfaceFrameDataList topIsland, InterfaceFrameDataList allFoundPNFrames)
        {
            PNPlannerResult result = new PNPlannerResult(PNPlannerResultType.Successful, string.Empty);
            topIsland = null;

            // Create the temporary list for keeping the controllers.
            Dictionary<Interface, ControllerRtClassState> controllerTable = new Dictionary<Interface, ControllerRtClassState>();

            #region Fill dictionary "m_ControllerTable" with interfaces of all controllers in the SyncDomain

            foreach (Interface controllerSubmodule in SyncDomain.IOControllerInterfaces)
            {
                SyncRole syncRole = (SyncRole)PNAttributeUtility.GetAdjustedSyncRole(controllerSubmodule);
                // Check the status of the synchronization.
                if (syncRole == SyncRole.Unsynchronized)
                {
                    controllerTable.Add(controllerSubmodule, ControllerRtClassState.Rt);
                }
                else
                {
                    List<IPNFrameData> controllerFrames = allFoundPNFrames.AllPNFrames[controllerSubmodule];
                    bool isIrtTop = false;
                    bool isRtTop = false;
                    foreach (IPNFrameData frameData in controllerFrames)
                    {
                        // Skipped code related to CCDX.
                        if (frameData.FrameClass == (long)PNIOFrameClass.Class3Frame)
                        {
                            isIrtTop = true;
                            break;
                        }

                        if ((frameData.FrameClass == (long)PNIOFrameClass.Class1Frame)
                            || (frameData.FrameClass == (long)PNIOFrameClass.Class2Frame))
                        {
                            isRtTop = true;
                        }
                    }

                    if (isIrtTop)
                    {
                        controllerTable.Add(controllerSubmodule, ControllerRtClassState.IrtTopUnchecked);
                    }
                    else if (isRtTop)
                    {
                        controllerTable.Add(controllerSubmodule, ControllerRtClassState.RtTopUnchecked);
                    }
                    else // Controller must be included in the input even if it has no RT frames, count of controllerFrames is 0 or frame is not irt, rt 
                    {
                        controllerTable.Add(controllerSubmodule, ControllerRtClassState.Unchecked);
                    }
                }
            }

            #endregion

            #region Create island through submodule

            HashSet<Interface> irtTopIsland = new HashSet<Interface>();

            // Go through all the elements in the list.
            foreach (KeyValuePair<Interface, ControllerRtClassState> interfaceSubmodule in controllerTable)
            {
                if ((interfaceSubmodule.Value != ControllerRtClassState.IrtTopUnchecked)
                    && (interfaceSubmodule.Value != ControllerRtClassState.RtTopUnchecked))
                {
                    continue;
                }

                // One controller, which uses IrtTop as RT class, is found.
                
                result = CreateIslandThroughController(interfaceSubmodule.Key, true, controllerTable, ref topIsland, allFoundPNFrames);

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    return result;
                }

                break;
            }
            // Execute the algorith and create the IrtTop island.
            foreach (KeyValuePair<Interface, ControllerRtClassState> interfaceSubmodule in controllerTable)
            {
                result = CreateIrtTopIsland(interfaceSubmodule.Key, controllerTable, ref irtTopIsland);

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    return result;
                }
            }

            #endregion

            Interface syncMaster = SyncDomain.SyncMaster;

            if (syncMaster != null && !irtTopIsland.Contains(syncMaster))
            {
                result.ResultType = PNPlannerResultType.AlgorithmError;
                result.MsgId = ConsistencyConstants.SyncDomainSyncMasterIsNotComprised;
                result.MessageParameters = new string[]
                                               { AttributeUtilities.GetSubmoduleNameWithContainer(syncMaster) };
                return result;
            }

            bool allSyncSlavesAreFound = true;
            foreach (Interface device in SyncDomain.AllParticipants)
            {
                if (DoesDeviceSubmoduleUseIrt(device) && !irtTopIsland.Contains(device))
                {
                    allSyncSlavesAreFound = false;
                    break;
                }
            }

            if (allSyncSlavesAreFound == false)
            {
                result.ResultType = PNPlannerResultType.AlgorithmError;
                return result;
            }

            #region Set PNIrtHighestSyncClass

            // Now, check all of the participating Controllers and set PNIrtHighestSyncClass attributes.
            // All IRTTop using Controllers should be contained in the IRTTop Islands.
            foreach (KeyValuePair<Interface, ControllerRtClassState> interfaceSubmodule in controllerTable)
            {
                switch (interfaceSubmodule.Value)
                {
                    case ControllerRtClassState.Rt:
                    // The controller does not use IRTTop.
                    case ControllerRtClassState.Unchecked:
                    case ControllerRtClassState.RtTopChecked:
                    case ControllerRtClassState.RtTopUnchecked:
                        interfaceSubmodule.Key.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnIrtHighestSyncClass, Convert.ToUInt32(PNRTClass.Rt, CultureInfo.InvariantCulture));
                        break;
                    case ControllerRtClassState.IrtTopChecked:
                        // The controller uses IRTTop.
                        interfaceSubmodule.Key.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnIrtHighestSyncClass, Convert.ToUInt32(PNRTClass.IrtTop, CultureInfo.InvariantCulture));
                        break;
                    case ControllerRtClassState.IrtTopUnchecked:
                        // More than one IRTTop islands have been found in the Sync-Domain.
                        // Return an error.
                        result.ResultType = PNPlannerResultType.MoreThanOneIsland;
                        result.MsgId = ConsistencyConstants.PNPlannerMultipleIslands;
                        return result;
                }
            }

            #endregion

            return result;
        }

        private PNPlannerResult CreateIrtTopIsland(
            Interface interfaceSubmodule,
            Dictionary<Interface, ControllerRtClassState> controllerTable,
            ref HashSet<Interface> irtTopIsland)
        {
            PNPlannerResult result = new PNPlannerResult(PNPlannerResultType.Successful, string.Empty);

            Queue<Interface> queue = new Queue<Interface>();
            HashSet<Interface> visitedSet = new HashSet<Interface>();

            queue.Enqueue(interfaceSubmodule);
            visitedSet.Add(interfaceSubmodule);

            while (queue.Count != 0)
            {
                Interface next = queue.Dequeue();
                IList<Interface> connectedSubmodules =
                    GetDirectlyConnectedSubmodules(next, controllerTable, ref result);

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    return result;
                }
                foreach (Interface submodule in connectedSubmodules)
                {
                    if (!visitedSet.Contains(submodule)
                        && DoesDeviceSubmoduleUseIrt(submodule))
                    {
                        visitedSet.Add(submodule);
                        queue.Enqueue(submodule);
                    }
                }
            }

            foreach (Interface visited in visitedSet)
            {
                irtTopIsland.Add(visited);
            }

            return result;
        }

        /// <summary>
        /// Searches for IE/PB Link devices in synchronized participants. If finds one, checks if the given coreIds
        /// belongs to one of its slaves.
        /// </summary>
        /// <param name="participantInterfaces">The interfaces to be searched.</param>
        /// <param name="coreIds">The coreIds that will be searched.</param>
        /// <param name="coreId">The coreId of a found slave.</param>
        /// <returns>The interface submodule which the found slave is connected to.</returns>
        private static Interface CheckForProxy(List<Interface> participantInterfaces, List<int> coreIds, out int coreId)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            foreach (Interface interfaceSubmodule in participantInterfaces)
            {
                PclObject headmodule = interfaceSubmodule.GetDevice();
                headmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.IEPBLinkOperatingMode, ac.GetNew(), 0);
                if (!ac.IsOkay)
                {
                    continue;
                }
                PclObject dpMasterSubmodule = NavigationUtilities.GetDpMasterSubmodule(headmodule);
                List<PclObject> slaves = NavigationUtilities.GetSlavesOfDpMaster(dpMasterSubmodule);
                foreach (PclObject slave in slaves)
                {
                    if (!coreIds.Contains(slave.GetHashCode()))
                    {
                        continue;
                    }
                    coreId = slave.GetHashCode();
                    return interfaceSubmodule;
                }
            }
            coreId = -1;
            return null;
        }

        /// <summary>
        /// Gets the interface submodules of the ioDevices with the given coreIds.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <param name="coreIds">Core ids of the iodevices which are being searched.</param>
        /// <param name="syncDomainBl"></param>
        /// <param name="itfIdIocIdMapping"></param>
        /// <returns>
        /// IconfigObjectCollection which contains the found ioDevices.
        /// The order of the list will be the same as coreIds, i.e. retVal[x] will contain
        /// the interface submodule of the coreIds[x]. The method returns an empty list if at least one of the
        /// iodevices cannot be found. Null is returned if the controller interface submodule is null or
        /// there is nothing to search.
        /// </returns>
        private static List<Interface> GetDeviceInterfacesOfIoDevices(
            Interface controllerInterfaceSubmodule,
            List<int> coreIds,
            SyncDomainBusinessLogic syncDomainBl,
            Dictionary<int, int> itfIdIocIdMapping)
        {
            if ((controllerInterfaceSubmodule == null)
                || (coreIds == null)
                || (coreIds.Count == 0))
            {
                return null;
            }

            Interface[] retArr = new Interface[coreIds.Count];

            int foundReceiverCount = 0;
            foreach (Interface deviceInterfaceSubmodule in syncDomainBl.AllParticipants)
            {
                int ioDeviceId;
                if (!itfIdIocIdMapping.TryGetValue(deviceInterfaceSubmodule.GetHashCode(), out ioDeviceId))
                {
                    continue;
                }
                int coreIdIndex = coreIds.IndexOf(ioDeviceId);
                if (coreIdIndex == -1)
                {
                    //Check Proxies.
                    int proxyCoreId;
                    Interface masterDevice = CheckForProxy((List<Interface>)syncDomainBl.AllParticipants, coreIds, out proxyCoreId);
                    if (masterDevice != null)
                    {
                        coreIdIndex = coreIds.IndexOf(proxyCoreId);
                        retArr[coreIdIndex] = masterDevice;
                        foundReceiverCount++;
                        if (foundReceiverCount == coreIds.Count)
                        {
                            break;
                        }
                    }
                    continue;
                }

                retArr[coreIdIndex] = deviceInterfaceSubmodule;
                foundReceiverCount++;
                if (foundReceiverCount == coreIds.Count)
                {
                    break;
                }
            }
            return retArr.ToList();
        }

        /// <summary>
        /// Creates topIsland by iterating through the devices connected to a controller.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule to be used for iteration.</param>
        /// <param name="isControllerSubmodule">Whether interfaceSubmodule is a controller interface or device interface.</param>
        /// <param name="controllerTable">The dictionary containing iterated controllers and their RT class states.</param>
        /// <param name="topIsland">Output parameter that returns the created topIsland.</param>
        /// <param name="allFoundPNFrames">A dictionary of all found PN frames.</param>
        /// <returns></returns>
        private PNPlannerResult CreateIslandThroughController(
            Interface interfaceSubmodule,
            bool isControllerSubmodule,
            Dictionary<Interface, ControllerRtClassState> controllerTable,
            ref InterfaceFrameDataList topIsland,
            InterfaceFrameDataList allFoundPNFrames)
        {
            PNPlannerResult result = new PNPlannerResult(PNPlannerResultType.Successful, string.Empty);
            bool isSubstitute =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.IsSubstituteConfigObjectType,
                    new AttributeAccessCode(),
                    false);

            // First of all, check whether this Controller has already been added to the island, or it is a 
            // substitute device

            if (((topIsland != null) && topIsland.AllPNFrames.ContainsKey(interfaceSubmodule)) || isSubstitute)
            {
                // The interface submodule has already been considered.
                return result;
            }

            // The island does not contain this interface submodule.
            if (isControllerSubmodule)
            {
                // This controller is not in controller table. It has no PN frames of its own. Shared device fix.
                if (!controllerTable.ContainsKey(interfaceSubmodule))
                {
                    return result;
                }

                // Get the actual RT class status.
                ControllerRtClassState rtClassState = controllerTable[interfaceSubmodule];

                if ((rtClassState == ControllerRtClassState.Rt)
                    || (rtClassState == ControllerRtClassState.RtTopChecked)
                    || (rtClassState == ControllerRtClassState.IrtTopChecked))
                {
                    // Controller does not use IrtTop, or already been added. Return the result.
                    return result;
                }

                // Get the directly connected submodules as well.
                List<Interface> connectedSubmodules =
                    (List<Interface>) SyncDomain.AllParticipants;

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    return result;
                }

                if (rtClassState == ControllerRtClassState.IrtTopUnchecked)
                {
                    // Assign the status to "TopChecked"
                    controllerTable[interfaceSubmodule] = ControllerRtClassState.IrtTopChecked;
                }

                if (rtClassState == ControllerRtClassState.RtTopUnchecked)
                {
                    controllerTable[interfaceSubmodule] = ControllerRtClassState.RtTopChecked;
                }
                else
                {
                    bool isPassThroughController = false;
                    // At least one of the conected controllers should support IRTTop.
                    foreach (Interface neighbour in connectedSubmodules)
                    {
                        if (Utility.IsProfinetControllerInterfaceSubmodule(neighbour))
                        {
                            // Get the RT class state of the controller.
                            ControllerRtClassState rtClassStateOfNeighbour = controllerTable[neighbour];

                            if ((rtClassStateOfNeighbour != ControllerRtClassState.IrtTopChecked)
                                && (rtClassStateOfNeighbour != ControllerRtClassState.IrtTopUnchecked)
                                && (rtClassStateOfNeighbour != ControllerRtClassState.RtTopChecked)
                                && (rtClassStateOfNeighbour != ControllerRtClassState.RtTopUnchecked))
                            {
                                continue;
                            }
                            // Controller uses IrtTop. Current item is PassThrough-Controller.
                            isPassThroughController = true;
                            break;
                        }

                        if (!DoesDeviceSubmoduleUseIrtTop(neighbour))
                        {
                            continue;
                        }

                        // Device uses IrtTop. Current item is PassThrough-Controller.
                        isPassThroughController = true;
                        break;
                    }

                    if (!isPassThroughController)
                    {
                        controllerTable[interfaceSubmodule] = ControllerRtClassState.Unchecked;

                        // Controller does not use IrtTop. Return the result.
                        return result;
                    }

                    // Assign the status to "TopChecked".
                    controllerTable[interfaceSubmodule] = ControllerRtClassState.IrtTopChecked;
                }

                List<IPNFrameData> irtTopPNPlannerDataObjects = new List<IPNFrameData>();
                if (allFoundPNFrames.AllPNFrames.ContainsKey(interfaceSubmodule))
                {
                    // Get the PNPlanner data objects of the controller that use IrtTop.
                    irtTopPNPlannerDataObjects = GetIrtTopFrames(interfaceSubmodule, allFoundPNFrames);
                }
                if (topIsland == null)
                {
                    // Create the island to add the new collection.
                    topIsland = new InterfaceFrameDataList(new Dictionary<Interface, List<IPNFrameData>>());
                }
                topIsland.AllPNFrames.Add(interfaceSubmodule, irtTopPNPlannerDataObjects);

                foreach (Interface submodule in connectedSubmodules)
                {
                    bool isDecentralPDEV =
                        submodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnPdevParametrizationDecentral,
                            new AttributeAccessCode(),
                            0) > 0;

                    // The Controller have PNPlanner Data Objects that use IrtTop as well.
                    // The isControllerSubmodule parameter of the CreateIslandThroughSubmodule() method is true only
                    // if under the submodule there is an IO Controller and the PDev attribute is false
                    // (if the PDev is true, it means that the IDevice functions as an IO Device)
                    result = CreateIslandThroughController(
                        submodule,
                        Utility.IsProfinetControllerInterfaceSubmodule(submodule) && !isDecentralPDEV,
                        controllerTable,
                        ref topIsland,
                        allFoundPNFrames);

                    if (result.ResultType != PNPlannerResultType.Successful)
                    {
                        return result;
                    }
                }
            }
            else
            {
                // The actual island should not be null. The search should have been started 
                // from a Controller Interface Submodule.
                if (topIsland == null)
                {
                    result.ResultType = PNPlannerResultType.AlgorithmError;
                    return result;
                }

                // Check whether the device currently uses IrtTop as real time class.
                if (!DoesDeviceSubmoduleUseIrtTop(interfaceSubmodule))
                {
                    return result;
                }

                // Add the collection to the actual island.
                topIsland.AllPNFrames.Add(interfaceSubmodule, null);

                // Consider directly connected submodules as well.
                IEnumerable<Interface> connectedSubmodules = GetDirectlyConnectedSubmodules(
                    interfaceSubmodule,
                    controllerTable,
                    ref result);

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    return result;
                }

                foreach (Interface submodule in connectedSubmodules)
                {
                    bool isDecentralPDEV =
                        submodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnPdevParametrizationDecentral,
                            new AttributeAccessCode(),
                            0) > 0;

                    // The Controller have PNPlanner Data Objects that use IrtTop as well.
                    // The isControllerSubmodule parameter of the CreateIslandThroughSubmodule() method is true only
                    // if under the submodule there is an IO Controller and the PDev attribute is false
                    // (if the PDev is true, it means that the IDevice functions as an IO Device)
                    result = CreateIslandThroughController(
                        submodule,
                        Utility.IsProfinetControllerInterfaceSubmodule(submodule) && !isDecentralPDEV,
                        controllerTable,
                        ref topIsland,
                        allFoundPNFrames);

                    if (result.ResultType != PNPlannerResultType.Successful)
                    {
                        return result;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the "intersection list" of given lists
        /// </summary>
        /// <param name="lists">Lists to look for intersection</param>
        /// <returns>Returns intersection list</returns>
        private static List<long> GetIntersection(params List<long>[] lists)
        {
            List<long> intersection = new List<long>();
            if (lists == null || lists.Length == 0)
            {
                return intersection;
            }
            List<long>[] sortedLists = new List<long>[lists.Length];
            for (int i = 0; i < lists.Length; i++)
            {
                sortedLists[i] = new List<long>(lists[i]);
                sortedLists[i].Sort();
            }
            // Traverse the first list
            for (int i = 0; i < sortedLists[0].Count; i++)
            {
                bool itemFound = true;
                // If the current item is not found in at least one of the other lists, continue with the next item.
                for (int j = 1; j < sortedLists.Length; j++)
                {
                    if (sortedLists[j].BinarySearch(sortedLists[0][i]) >= 0)
                    {
                        continue;
                    }
                    itemFound = false;
                    break;
                }
                if (itemFound)
                {
                    intersection.Add(sortedLists[0][i]);
                }
            }
            return intersection;
        }

        /// <summary>
        /// Checks whether a device interface submodule currently uses IrtTop.
        /// </summary>
        /// <param name="interfaceSubmodule">The device interface submodule to be checked.</param>
        /// <returns>True if the interface submodule uses IrtTop.</returns>
        private bool DoesDeviceSubmoduleUseIrtTop(Interface interfaceSubmodule)
        {
            // Get the IO Device object.
            PNIOD ioDevice = interfaceSubmodule.PNIOD;
            if (ioDevice == null)
            {
                return false;
            }
            PNIOFrameClass frameClass = PNIOFrameClass.Class1Frame;
            long frameClassAttr = ioDevice.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoFrameClass,
                new AttributeAccessCode(),
                -1);
            if (frameClassAttr != -1)
            {
                frameClass = (PNIOFrameClass)Convert.ToInt64(frameClassAttr, CultureInfo.InvariantCulture);
            }

            if ((frameClass == PNIOFrameClass.Class3Frame)
                || (frameClass == PNIOFrameClass.Class2Frame)
                || (frameClass == PNIOFrameClass.Class1Frame))
            {
                return true;
            }
            return false;
        }

        private bool DoesDeviceSubmoduleUseIrt(Interface interfaceSubmodule)
        {
            // Get the IO Device object.
            PNIOD ioDevice = interfaceSubmodule.PNIOD;
            if (ioDevice == null)
            {
                return false;
            }

            PNIOFrameClass frameClass = PNIOFrameClass.Class1Frame;
            long frameClassAttr = ioDevice.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoFrameClass,
                new AttributeAccessCode(),
                -1);
            if (frameClassAttr != -1)
            {
                frameClass = (PNIOFrameClass)Convert.ToInt64(frameClassAttr, CultureInfo.InvariantCulture);
            }

            if (frameClass == PNIOFrameClass.Class3Frame)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Gets all Interface Submodules that are in the same sync-domain and
        /// connected to the given submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The source interface submodule.</param>
        /// <param name="controllerTable">The dictionary containing controller interfaces and their states.</param>
        /// <param name="result">The result object.</param>
        /// <returns>The list of the directly connected interface submodules.</returns>
        private IList<Interface> GetDirectlyConnectedSubmodules(
            Interface interfaceSubmodule,
            Dictionary<Interface, ControllerRtClassState> controllerTable,
            ref PNPlannerResult result)
        {
            // Create a list to keep the directly connected interface submodules.
            List<Interface> connectedSubmodules = new List<Interface>();

            if (interfaceSubmodule == null)
            {
                return connectedSubmodules;
            }

            // Get the subnet of the interface.
            Node ethernetNode = interfaceSubmodule.Node;
            if (ethernetNode == null)
            {
                return connectedSubmodules;
            }

            Subnet subnet = ethernetNode.Subnet;
            if (subnet == null)
            {
                return connectedSubmodules;
            }

            // Get the ports of the interface submodule.
            IList<Port> ports = interfaceSubmodule.GetPorts();

            // Go through all the ports of the interface submodule.
            foreach (Port port in ports)
            {
                List<Port> partnerPorts = (List<Port>)port.GetPartnerPorts();

                if (partnerPorts == null)
                {
                    continue;
                }

                switch (partnerPorts.Count)
                {
                    case 0:
                        break;
                    case 1:
                        {
                            // Get the related interface submodule.
                            Interface partnerInterfaceSubmodule = partnerPorts[0].GetInterface();

                            // Check whether this interface submodule is in the same subnet
                            // Get the subnet of the interface.
                            Node partnerEthernetNode = partnerInterfaceSubmodule.Node;
                            if (partnerEthernetNode == null)
                            {
                                continue;
                            }

                            Subnet partnerSubnet = partnerEthernetNode.Subnet;
                            if (partnerSubnet == null)
                            {
                                continue;
                            }

                            SyncDomain partnerSyncDomain = partnerInterfaceSubmodule.SyncDomain;

                            if ((partnerSubnet.GetHashCode() != subnet.GetHashCode())
                                || (partnerSyncDomain.GetHashCode() != SyncDomain.PCLObject.GetHashCode()))
                            {
                                continue;
                            }

                            if ((partnerInterfaceSubmodule != null)
                                && !partnerInterfaceSubmodule.Equals(interfaceSubmodule))
                            {
                                if (!connectedSubmodules.Contains(partnerInterfaceSubmodule))
                                {
                                    connectedSubmodules.Add(partnerInterfaceSubmodule);
                                }
                            }
                            break;
                        }
                    default:
                        {
                            foreach (Port partnerPort in partnerPorts)
                            {
                                Interface partnerInterfaceSubmodule = partnerPort.GetInterface();
                                // Check whether this submodule is in the same subnet.
                                // Get the subnet of the interface.
                                Node partnerEthernetNode = partnerInterfaceSubmodule.Node;
                                if (partnerEthernetNode == null)
                                {
                                    continue;
                                }

                                Subnet partnerSubnet = partnerEthernetNode.Subnet;
                                if (partnerSubnet == null)
                                {
                                    continue;
                                }

                                SyncDomain partnerSyncDomain = partnerInterfaceSubmodule.SyncDomain;

                                if ((partnerSubnet.GetHashCode() != subnet.GetHashCode())
                                    || (partnerSyncDomain.GetHashCode() != SyncDomain.PCLObject.GetHashCode()))
                                {
                                    continue;
                                }

                                // Check whether the interface submodule currently uses IrtTop.
                                bool interfaceSubmoduleUsesIrtTop;
                                if (Utility.IsProfinetControllerInterfaceSubmodule(partnerInterfaceSubmodule))
                                {
                                    // Get the RT class state of the controller.
                                    ControllerRtClassState rtClassStateOfNeighbour =
                                        controllerTable[partnerInterfaceSubmodule];

                                    interfaceSubmoduleUsesIrtTop = (rtClassStateOfNeighbour
                                                                    == ControllerRtClassState.IrtTopChecked)
                                                                   || (rtClassStateOfNeighbour
                                                                       == ControllerRtClassState.IrtTopUnchecked)
                                                                   || (rtClassStateOfNeighbour
                                                                       == ControllerRtClassState.RtTopUnchecked)
                                                                   || (rtClassStateOfNeighbour
                                                                       == ControllerRtClassState.RtTopChecked);
                                }
                                else
                                {
                                    interfaceSubmoduleUsesIrtTop =
                                        DoesDeviceSubmoduleUseIrtTop(partnerInterfaceSubmodule);
                                }

                                if (interfaceSubmoduleUsesIrtTop)
                                {
                                    // The device should not use IrtTop because of the ToolChanger.
                                    result.ResultType = PNPlannerResultType.ToolChangerError;
                                    result.MsgId = ConsistencyConstants.PNPlannerToolChangerError;
                                    result.MessageParameters = new string[]
                                                                   {
                                                                       AttributeUtilities.GetName(interfaceSubmodule)
                                                                       + "/" + AttributeUtilities.GetName(port)
                                                                   };
                                }
                            }
                            break;
                        }
                }
            }

            result.ResultType = PNPlannerResultType.Successful;
            return connectedSubmodules;
        }

        /// <summary>
        /// Collects the PNPlanner Data Objects of a controller, whose FrameClass setting is set to FrameClass3.
        /// </summary>
        /// <param name="controllerSubmodule">The provided controller interface submodule.</param>
        /// <param name="allPNFrames">Dictionary of all PN frames.</param>
        /// <returns>PNPlanner data objects.</returns>
        private List<IPNFrameData> GetIrtTopFrames(
            Interface controllerSubmodule,
            InterfaceFrameDataList allPNFrames)
        {
            // Create a list to keep the PNPlanner data objects to return.
            List<IPNFrameData> irtTopPNPlannerDataObjects = new List<IPNFrameData>();

            // Get the SLC data objects of controllerSubmodule.
            List<IPNFrameData> pnFrameDataList = allPNFrames.AllPNFrames[controllerSubmodule];

            if (pnFrameDataList == null)
            {
                return irtTopPNPlannerDataObjects;
            }

            foreach (IPNFrameData frameData in pnFrameDataList)
            {
                irtTopPNPlannerDataObjects.Add(frameData);
            }

            return irtTopPNPlannerDataObjects;
        }
    }
}