/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNGsdIFPluggablePortExtensionBL.cs        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;

using PNConfigLib.Consistency;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.PNInterface
{
    class PNGsdIFPluggablePortExtensionBL : IFDecorator
    {
        //########################################################################################
        #region Constants and Enums
        // Contains all constants and enums       

        #endregion

        //########################################################################################
        #region Fields

        #endregion

        //########################################################################################
        #region Construction/Destruction/Initialisation
        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        public PNGsdIFPluggablePortExtensionBL(IInterfaceBusinessLogic decoratedIFBL) : base(decoratedIFBL)
        {
        }

        #endregion

        //########################################################################################
        #region Public Methods
        // Contains all public methods of the class



        #endregion

        //########################################################################################
        #region Overrides and Overridables

        public override void InitBL()
        {
            InitActions();
        }


        #endregion

        //########################################################################################
        #region Private Implementation
        // Contains the private implementation of the class

        #region BL Initialization

        private void InitActions()
        {
            ConsistencyManager.RegisterConsistencyCheck(Interface, OnCheckConsistency);
        }

        #endregion

        #region ConsistencyCheck


        private void OnCheckConsistency()
        {
            // Interface without ports is not allowed 
            CheckConsistencyUtility.CheckConsistencyNoPortForInterface(Interface);
        }

        #endregion



        #endregion
    }
}
