/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_04.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.4 and is based on GSD(ML) versions 2.35 and lower.
    ///		
    /// </summary>
    internal class CheckerV0204 : CheckerV02035
    {
        #region Fields

        #endregion


        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:WorkingClock/@Role" +
                            " | .//gsddef:GlobalTime/@Role" +
                            " | .//gsddef:MAUTypeItem/@SupportedFeatures" +
                            " | .//gsddef:DeviceAccessPointItem/@SFPDiagnosisSupported" +
                            " | .//gsddef:PortSubmoduleItem/@SFPDiagnosisMonitoring" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SupportedServiceProtocols" +
                            " | .//gsddef:CertificationInfoExt/@ApplicationClass" +
                            " | .//gsddef:SystemRedundancy/@DeviceTypes" +
                            " | .//gsddef:CertificationInfoExt/@ConformanceClass";
                return (xp);
            }
        }

        #endregion


        #region CheckerObject Members


        override protected string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV0204;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV0204;
        }


        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00040000);
            Checks.Add(Constants.s_Cn_0X00040001);
            Checks.Add(Constants.s_Cn_0X00040002);
            Checks.Add(Constants.s_Cn_0X00040003);
            Checks.Add(Constants.s_Cn_0X00040004);
            Checks.Add(Constants.s_Cn_0X00040005);
            Checks.Add(Constants.s_Cn_0X00040006);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                //Checks.Remove(Constants.CN_0x000XXXXX);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion




        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.4.
        /// </summary>
        public CheckerV0204()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version24);
        }

        #endregion

        /// <summary>
        /// CheckSupportedRTClassesContainsStreamCategory
        /// 
        /// <Description>
        /// The following check is implemented:
        /// - check, if 'InterfaceSubmoduleItem/@SupportedRT_Classes' contains
        ///   at least one of the stream categories
        ///   "RT_CLASS_STREAM_LO",
        ///   "RT_CLASS_STREAM_HI".
        /// </Description>
        /// 
        /// </summary>
        /// <returns> </returns>
        protected virtual void CheckSupportedRTClassesContainsStreamCategory(XElement interfaceSubmoduleItem, XElement certificationInfoExt)
        {
            if (interfaceSubmoduleItem != null)
            {
                string SupportedRT_ClassesValues = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
                IList<string> SupportedRT_ClassesList = SupportedRT_ClassesValues.Split(Constants.s_Semicolon.ToCharArray());
                if (!SupportedRT_ClassesList.Contains("RT_CLASS_STREAM_LO") &&
                    !SupportedRT_ClassesList.Contains("RT_CLASS_STREAM_HI"))
                {
                    // "When 'CertificationInfoExt/@ConformanceClass'="D", then
                    //  SupportedRT_Classes must contain "RT_CLASS_STREAM_LO" and/or "RT_CLASS_STREAM_HI"."
                    string msg = Help.GetMessageString("M_0x00040005_2");
                    string xpath = Help.GetXPath(certificationInfoExt);
                    var xli = (IXmlLineInfo)certificationInfoExt;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00040005_2");
                }
            }
        }

        #region Methods


        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("CertificationInfoExt", "ConformanceClass");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("Master");
            tokens1.Add("Slave");
            AttributeTokenDictionary.Add("Role", tokens1);

            IList<string> tokens2 = new List<string>();
            tokens2.Add("TSN");
            tokens2.Add("TSN_TAS");
            tokens2.Add("TSN_Preemption");
            AttributeTokenDictionary.Add("SupportedFeatures", tokens2);

            IList<string> tokens3 = new List<string>();
            tokens3.Add("ThresholdBased");
            tokens3.Add("TxFault");
            tokens3.Add("RxLoss");
            AttributeTokenDictionary.Add("SFPDiagnosisSupported", tokens3);

            IList<string> tokens4 = new List<string>();
            tokens4.Add("ThresholdBased");
            tokens4.Add("TxFault");
            tokens4.Add("RxLoss");
            AttributeTokenDictionary.Add("SFPDiagnosisMonitoring", tokens4);

            IList<string> tokens5 = new List<string>();
            tokens5.Add("CLRPC");
            tokens5.Add("RSI");
            AttributeTokenDictionary.Add("SupportedServiceProtocols", tokens5);

            IList<string> tokens6 = new List<string>();
            tokens6.Add("S2");
            tokens6.Add("R1");
            tokens6.Add("R2");
            AttributeTokenDictionary.Add("DeviceTypes", tokens6);

            var tokens7 = AttributeTokenDictionary["IsochroneModeInRT_Classes"];
            tokens7.Add("RT_CLASS_STREAM_LO");
            tokens7.Add("RT_CLASS_STREAM_HI");

            var tokens8 = AttributeTokenDictionary["SupportedRT_Classes"];
            tokens8.Add("RT_CLASS_STREAM_LO");
            tokens8.Add("RT_CLASS_STREAM_HI");

            IList<string> tokens9 = new List<string>();
            tokens9.Add("D");
            AttributeTokenDictionary.Add("ConformanceClass", tokens9);
        }
        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// The attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// As of GSDML V2.4:
        /// - New values 0x004F - 0x0066 for MAUTypes
        /// 
        /// Partly the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs into which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            AttributeValueListDictionary.Remove("MAUTypes-v2.3");
            string values1 = "-v2.35 0 5 10..13 15..18 21..26 29..78";                 // applies for pnio_version > 2.3
            AttributeValueListDictionary.Add("MAUTypes-v2.3", values1);
            string values2 = "0 5 10..13 15..18 21..26 29..78 79..102";               // applies for pnio_version > 2.35
            AttributeValueListDictionary.Add("MAUTypes-v2.35", values2);
        }

        #endregion


        #region Overrides

        /// <summary>
        /// Gets the device types from the DAP.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>list with device types.</returns>
        protected override IList<string> GetDeviceTypes(XElement dap)
        {
            IList<string> deviceTypeList = new List<string>();
            var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);

            if (systemRedundancy != null)
            {
                string deviceTypeValues = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(systemRedundancy, Attributes.s_DeviceTypes));
                deviceTypeList = deviceTypeValues.Split(Constants.s_Semicolon.ToCharArray());
            }

            return deviceTypeList;
        }

        protected override bool IsConformanceClassDOnly(XElement dap)
        {
            bool ConformanceClassDOnlyDevice = false;

            // Get CertificationInfo
            var certificationInfo = dap.Element(NamespaceGsdDef + Elements.s_CertificationInfo);

            if (certificationInfo != null)
            {
                // Get the attribute ConformanceClass
                var conformanceClass = certificationInfo.Attribute(Attributes.s_ConformanceClass);

                // Get the attribute ApplicationClass
                var applicationClass = certificationInfo.Attribute(Attributes.s_ApplicationClass);

                // Get the list of CertificationInfoExt nodes
                var certificationInfoExtList = dap.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);

                if (conformanceClass == null && applicationClass == null && certificationInfoExtList.Count() == 1)
                {
                    var certificationInfoExt = certificationInfoExtList.FirstOrDefault();

                    // Get the attribute ConformanceClass
                    string conformanceClassExt = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ConformanceClass);
                    if (conformanceClassExt == "D")
                        ConformanceClassDOnlyDevice = true;
                }
            }

            return ConformanceClassDOnlyDevice;
        }

        #endregion


        /// <summary>
        /// Check number: CN_0x00040000
        /// 
        /// PROFIsafe Parameterization in Run (PIR)
        /// 
        /// (Virtual)SubmoduleItem/@PROFIsafePIR_Supported may only be present and "true" when
        /// (1) PROFIsafe is supported (the neighboring attribute PROFIsafeSupported is also present and "true") and
        /// (2) PROFIsafe is >= 2.6 (F_ParameterRecordDataItem/F_CRC_Seed and ../F_Passivation are present)
        /// 
        /// (3) The attribute PrmBeginPrmEndSequenceSupported must be present and "true" on each DAP
        ///     where a (Virtual)SubmoduleItem with @PROFIsafePIR_Supported present and "true" can be plugged.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040000()
        {
            // Find all submodules with PROFIsafePIRSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strProfIsafePirSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafePirSupported);
                bool pRofIsafePirSupported = false;
                if (!string.IsNullOrEmpty(strProfIsafePirSupported))
                    pRofIsafePirSupported = XmlConvert.ToBoolean(strProfIsafePirSupported);
                if (!pRofIsafePirSupported)
                {
                    continue;
                }

                // (1)
                CreateReport0x00040000_1(submodule);

                // (2)
                CreateReport0x00040000_2(submodule);

                // (3)
                CheckAtributePrmBeginPrmEndSequenceSupported(submodule);
            }

            return true;
        }

        private void CheckAtributePrmBeginPrmEndSequenceSupported(XElement submodule)
        {
            var errorReportedDaps = new List<XElement>();
            IList<XElement> dapsOfSubmodule = new List<XElement>();
            if (SubmoduleToDapDictionary.ContainsKey(submodule))
            {
                dapsOfSubmodule = SubmoduleToDapDictionary[submodule];
            }
            else if (submodule.Name.LocalName == Elements.s_VirtualSubmoduleItem)
            {
                if (submodule.Parent != null)
                {
                    var dap = submodule.Parent.Parent;
                    CreateReport0x00040000_3(dap, errorReportedDaps);
                }
            }

            foreach (XElement dap in dapsOfSubmodule)
            {
                CreateReport0x00040000_3(dap, errorReportedDaps);
            }
        }

        private void CreateReport0x00040000_3(XElement dap, ICollection<XElement> errorReportedDaps)
        {
            string strPrmBeginPrmEndSequenceSupported =
                Help.GetAttributeValueFromXElement(dap, Attributes.s_PrmBeginPrmEndSequenceSupported);
            bool prmBeginPrmEndSequenceSupported = false;
            if (!string.IsNullOrEmpty(strPrmBeginPrmEndSequenceSupported))
                prmBeginPrmEndSequenceSupported = XmlConvert.ToBoolean(strPrmBeginPrmEndSequenceSupported);
            if (prmBeginPrmEndSequenceSupported)
            {
                return;
            }

            if (errorReportedDaps.Contains(dap))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return;
            }

            // "The attribute PrmBeginPrmEndSequenceSupported must be present and "true" on each DAP where a
            //  (Virtual)SubmoduleItem with @PROFIsafePIR_Supported present and "true" can be plugged."
            string msg = Help.GetMessageString("M_0x00040000_3");
            string xpath = Help.GetXPath(dap);
            var xli = (IXmlLineInfo)dap;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040000_3");
            errorReportedDaps.Add(dap);
        }

        private void CreateReport0x00040000_2(XContainer submodule)
        {
            XElement fParameterRecordDataItem = null;
            XElement recordDataList = submodule.Element(NamespaceGsdDef + Elements.s_RecordDataList);
            if (recordDataList != null)
                fParameterRecordDataItem = recordDataList.Element(NamespaceGsdDef + Elements.s_FParameterRecordDataItem);
            if (fParameterRecordDataItem == null
                || IsProfiSafeV26(fParameterRecordDataItem))
            {
                return;
            }

            if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
            {
                // "The attribute '(Virtual)SubmoduleItem/@PROFIsafePIR_Supported' must only be present and "true" when
                //  PROFIsafe is >= 2.6 ('F_ParameterRecordDataItem/F_CRC_Seed' and '../F_Passivation' are present)."
                string msg = Help.GetMessageString("M_0x00040000_2");
                string xpath = Help.GetXPath(submodule);
                var xli = (IXmlLineInfo)submodule;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040000_2");
            }
        }

        private void CreateReport0x00040000_1(XElement submodule)
        {
            string strProfIsafeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
            bool pRofIsafeSupported = false;
            if (!string.IsNullOrEmpty(strProfIsafeSupported))
                pRofIsafeSupported = XmlConvert.ToBoolean(strProfIsafeSupported);
            if (pRofIsafeSupported)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
            {
                return;
            }

            // "The attribute '(Virtual)SubmoduleItem/@PROFIsafePIR_Supported' must only be present and true when
            //  '(Virtual)SubmoduleItem/@PROFIsafeSupported' is also present and "true"."
            string msg = Help.GetMessageString("M_0x00040000_1");
            string xpath = Help.GetXPath(submodule);
            var xli = (IXmlLineInfo)submodule;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040000_1");
        }
        /// <summary>
        /// Check number: CN_0x00040001
        /// The attribute InterfaceSubmoduleItem/@SupportedServiceProtocols can have one or both of the following values: "CLRPC", "RSI".
        /// The following must be checked:
        /// (1) "RSI" may only be present when PNIO_Version >= "V2.4".
        /// (2) When CertificationInfoExt is present with ConformanceClass="D", then @SupportedServiceProtocols must be present and token "RSI" must be given.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040001()
        {
            var itfs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            itfs = Help.TryRemoveXElementsUnderXsAny(itfs, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in itfs)
            {
                var lineInfo = (IXmlLineInfo)interfaceSubmoduleItem;
                if (interfaceSubmoduleItem.Parent == null)
                {
                    continue;
                }

                var dap = interfaceSubmoduleItem.Parent.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                string supportedServiceProtocols = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedServiceProtocols);
                IList<string> supportedProtocolList = new List<string>(supportedServiceProtocols.Split(Constants.s_Semicolon.ToCharArray()));

                if (pnioVersion < 2.4)
                {
                    // (1)
                    if (supportedProtocolList.Contains("RSI"))
                    {
                        // "'InterfaceSubmoduleItem/@SupportedServiceProtocols' with "RSI" must only be present when PNIO_Version >= "V2.4"."
                        string msg = Help.GetMessageString("M_0x00040001_1");
                        string xpath = Help.GetXPath(interfaceSubmoduleItem);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00040001_1");
                    }
                }

                // (2)
                var certificationInfoExtList = dap.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);
                foreach (var certificationInfoExt in certificationInfoExtList)
                {
                    string conformanceClass = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ConformanceClass);
                    if (0 != string.Compare(conformanceClass, Constants.s_CharacterD, StringComparison.InvariantCulture))
                    {
                        continue;
                    }
                    if (supportedProtocolList.Contains("RSI"))
                    {
                        continue;
                    }
                            // "When 'DeviceAccessPointItem/CertificationInfo/CertificationInfoExt' is present with ConformanceClass="D", 
                            // 'InterfaceSubmoduleItem/@SupportedServiceProtocols' shall contain the value "RSI"."
                            string msg = Help.GetMessageString("M_0x00040001_2");
                            string xpath = Help.GetXPath(interfaceSubmoduleItem);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x00040001_2");
                        
                    
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00040002
        /// There are new attributes @SFPDiagnosisSupported at the DAP and SFPDiagnosisMonitoring at the PortSubmoduleItem both with the
        /// tokens "ThresholdBased", "TxFault" and "RxLoss".
        /// 
        /// The following is checked here:
        /// - For each DAP where the attribute SFPDiagnosisSupported exists, for all tokens contained in there,
        ///   there shall be at least one port submodule configurable with this DAP where this attribute
        ///   is present and which contains this token.
        ///   That means: What the DAP claims to support regarding SFP diagnosis must actually be usable.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040002()
        {
            // Find all daps with attribute SFPDiagnosisSupported
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                .Where(x => x.Attribute(Attributes.s_SfpDiagnosisSupported) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                var lineInfo = (IXmlLineInfo)dap;

                string dapSFPDiagnosisValues = Help.GetAttributeValueFromXElement(dap, Attributes.s_SfpDiagnosisSupported);
                IList<string> dapSFPDiagnosisList = new List<string>(dapSFPDiagnosisValues.Split(Constants.s_Semicolon.ToCharArray()));

                // Get the PortSubmoduleItems plugged or pluggable with this dap
                DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems);

                if (portSubmoduleItems == null || portSubmoduleItems.Count == 0)
                {
                    // "For this DAP the attribute SFPDiagnosisSupported exists, but no port submodule configurable with this DAP.
                    //  There shall be at least one port submodule configurable with this DAP where the attribute '@SFPDiagnosisMonitoring'
                    //  is present and which contains the tokens from '@SFPDiagnosisSupported'."
                    string msg = Help.GetMessageString("M_0x00040002_1");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00040002_1");
                    continue;
                }

                bool tokenFound = true;
                string tokenLastSearched = String.Empty;
                foreach (string token in dapSFPDiagnosisList)
                {
                    if (!tokenFound)
                        break;

                    tokenLastSearched = token;
                    tokenFound = false;
                    foreach (var port in portSubmoduleItems)
                    {
                        string portSFPDiagnosisValues = Help.GetAttributeValueFromXElement(port, Attributes.s_SfpDiagnosisMonitoring);
                        IList<string> portSFPDiagnosisList = new List<string>(portSFPDiagnosisValues.Split(Constants.s_Semicolon.ToCharArray()));
                        if (portSFPDiagnosisList.Contains(token))
                        {
                            tokenFound = true;
                            break;
                        }
                    }
                }

                if (!tokenFound)
                {
                    // "For this DAP the attribute SFPDiagnosisSupported exists with the token "{0}", but there is no port submodule configurable with this DAP
                    //  where the attribute '@SFPDiagnosisMonitoring' is present and contains this token."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00040002_2"), tokenLastSearched);
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00040002_2");
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00040003
        /// There is a new attribute DeviceAccessPointItem/SystemRedundancy/@DeviceTypes with the tokens "R2", "R1" and "S2".
        /// The old attribute @DeviceType is now optional with no default.
        /// 
        /// The following is checked here:
        /// (1) if both DeviceType and DeviceTypes is given, the value of DeviceType must also be present as token in DeviceTypes
        /// (2) if DeviceType is missing, the attribute @RequiredSchemaVersion must be present at the DAP with a value >= V2.4
        /// (3) When DeviceTypes contains "R1" and/or "R2" there must be provisions for a second DAP.
        ///     The provision depends on the presence of the token "S2": if present, the second DAP must be optionally pluggable (fixed not allowed).
        ///     Remark:
        ///     When the token "S2" is missing, the second DAP may be fixed plugged or optionally pluggable.
        ///     This is covered by the "old" checks in 0x00020010.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040003()
        {
            // (1)
            // Find all SystemRedundancy nodes with attribute DeviceType
            var systemRedundancyNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy)
                .Where(x => x.Attribute(Attributes.s_DeviceType) != null);
            systemRedundancyNodes = Help.TryRemoveXElementsUnderXsAny(systemRedundancyNodes, Nsmgr, Gsd);

            CreateReport0x00040003_1(systemRedundancyNodes);

            // (2)
            // Find all SystemRedundancy nodes without attribute DeviceType
            systemRedundancyNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy)
                .Where(x => x.Attribute(Attributes.s_DeviceType) == null);
            systemRedundancyNodes = Help.TryRemoveXElementsUnderXsAny(systemRedundancyNodes, Nsmgr, Gsd);

            CreateReport0x00040003_2(systemRedundancyNodes);

            // (3)
            // Find all SystemRedundancy nodes
            systemRedundancyNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SystemRedundancy);
            systemRedundancyNodes = Help.TryRemoveXElementsUnderXsAny(systemRedundancyNodes, Nsmgr, Gsd);

            CreateReportM_0x00040003_3(systemRedundancyNodes);

            return true;
        }

        private void CreateReportM_0x00040003_3(IEnumerable<XElement> systemRedundancyNodes)
        {
            foreach (var systemRedundancy in systemRedundancyNodes)
            {
                var dap = systemRedundancy.Parent;
                if (!DapRedundancySupported(dap))
                {
                    continue;
                }

                // Find the number of fixed slots
                List<uint> fixedSlotList = new();
                if (dap == null)
                {
                    continue;
                }

                XAttribute fixedInSlots = dap.Attribute(Attributes.s_FixedInSlots);
                IList<ValueListHelper.ValueRangeT> fixedSlots = ValueListHelper.NormalizeValueList(fixedInSlots, Store);
                uint index = 0;
                for (int currentRange = 0; currentRange < fixedSlots.Count; currentRange++)
                {
                    uint currentValue = fixedSlots[currentRange].From;
                    while (currentValue <= fixedSlots[currentRange].To && index < 3)
                    {
                        fixedSlotList.Add(currentValue++);
                        index++; // No more than 3 entries are needed
                    }
                }

                if (fixedSlotList.Count != 2
                    || !S2RedundancySupported(dap))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(systemRedundancy, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "When 'SystemRedundancy/@DeviceTypes' contains "R1" and/or "R2" there must be provisions for a second DAP.
                //  The provision depends on the presence of the token "S2": if present, the second DAP must be optionally pluggable
                //  what means '@FixedInSlots' must contain one and '@AllowedInSlots' two entries."
                string msg = Help.GetMessageString("M_0x00040003_3");
                string xpath = Help.GetXPath(fixedInSlots);
                var xli = (IXmlLineInfo)fixedInSlots;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040003_3");
            }
        }

        private void CreateReport0x00040003_2(IEnumerable<XElement> systemRedundancyNodes)
        {
            foreach (var systemRedundancyNode in systemRedundancyNodes)
            {
                var lineInfo = (IXmlLineInfo)systemRedundancyNode;

                // Get the attribute RequiredSchemaVersion
                double requiredSchemaVersion = 0;
                string requiredSchemaVersionStr = Help.GetAttributeValueFromXElement(
                    systemRedundancyNode.Parent,
                    Attributes.s_RequiredSchemaVersion);
                if (!String.IsNullOrEmpty(requiredSchemaVersionStr))
                    requiredSchemaVersion = GetPNioVersion(requiredSchemaVersionStr);

                if (!(requiredSchemaVersion < 2.4))
                {
                    continue;
                }

                // "If the attribute 'SystemRedundancy/@DeviceType' is missing, the attribute
                //  '@RequiredSchemaVersion' must be present at the DAP with a value >= V2.4."
                string msg = Help.GetMessageString("M_0x00040003_2");
                string xpath = Help.GetXPath(systemRedundancyNode);
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040003_2");
            }
        }

        private void CreateReport0x00040003_1(IEnumerable<XElement> systemRedundancyNodes)
        {
            foreach (var systemRedundancyNode in systemRedundancyNodes)
            {
                var lineInfo = (IXmlLineInfo)systemRedundancyNode;

                // Get the attribute DeviceType
                string deviceTypeValue = Help.GetAttributeValueFromXElement(systemRedundancyNode, Attributes.s_DeviceType);

                // Get the attribute DeviceTypes
                IList<string> deviceTypeList = GetDeviceTypes(systemRedundancyNode.Parent);

                if (deviceTypeList.Contains(deviceTypeValue))
                {
                    continue;
                }

                // "The value of DeviceType = "{0}" must also be present as token in DeviceTypes."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00040003_1"), deviceTypeValue);
                string xpath = Help.GetXPath(systemRedundancyNode);
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040003_1");
            }
        }
        /// <summary>
        /// Check number: CN_0x00040004
        /// On element CertificationInfo,
        /// - attributes ConformanceClass and ApplicationClass are now optional in schema
        /// - new element CertificationInfoExt with attributes ConformanceClass and ApplicationClass.
        /// 
        /// ConformanceClass has only token "D". ApplicationClass has the same tokens as on the parent element.
        /// The following checks are implemented:
        /// (1) if PNIO_Version less than V2.4, CertificationInfo / @ConformanceClass and @ApplicationClass must be present,
        ///     and CertificationInfoExt must not be present
        /// (2) if PNIO_Version >= V2.4, CertificationInfo / @ConformanceClass and @ApplicationClass must both
        ///     be present or both be missing. If missing, @RequiredSchemaVersion must be present on the DAP with a value >= V2.4
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040004()
        {
            // Find all CertificationInfo nodes
            var certificationInfoNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo);
            certificationInfoNodes = Help.TryRemoveXElementsUnderXsAny(certificationInfoNodes, Nsmgr, Gsd);
            foreach (var certificationInfo in certificationInfoNodes)
            {
                var lineInfo = (IXmlLineInfo)certificationInfo;

                // Get the DAP
                var dap = certificationInfo.Parent;

                // Get the attribute ConformanceClass
                XAttribute conformanceClassNode = certificationInfo.Attribute(Attributes.s_ConformanceClass);

                // Get the attribute ApplicationClass
                XAttribute applicationClassNode = certificationInfo.Attribute(Attributes.s_ApplicationClass);

                // Get the element CertificationInfoExt
                var certificationInfoExt = certificationInfo.Element(NamespaceGsdDef + Elements.CertificationInfoExt);

                // Gt the PNIO version
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                // (1)
                if (pnioVersion < 2.4)
                {
                    CreateReport0x00040004_1(conformanceClassNode, applicationClassNode, certificationInfoExt, certificationInfo, lineInfo);
                }
                // (2)
                else
                {
                    if ((conformanceClassNode == null && applicationClassNode != null) ||
                        (conformanceClassNode != null && applicationClassNode == null))
                    {
                        CreateReport0x00040004_2(certificationInfo, lineInfo);
                    }
                    else if (conformanceClassNode == null)
                    {
                        CreateReport0x00040004_3(dap, certificationInfo, lineInfo);
                    }
                }
            }

            return true;
        }

        private void CreateReport0x00040004_3(XElement dap, XObject certificationInfo, IXmlLineInfo lineInfo)
        {
            double requiredSchemaVersion = 1;
            string requiredSchemaVersionStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_RequiredSchemaVersion);
            if (!string.IsNullOrEmpty(requiredSchemaVersionStr))
                requiredSchemaVersion = GetPNioVersion(requiredSchemaVersionStr);
            if (!(requiredSchemaVersion < 2.4))
            {
                return;
            }

            // "When 'CertificationInfo/@ConformanceClass' and '@ApplicationClass' both are missing,
            //  '@RequiredSchemaVersion' must be present on the DAP with a value >= V2.4."
            string msg = Help.GetMessageString("M_0x00040004_3");
            string xpath = Help.GetXPath(certificationInfo);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040004_3");
        }

        private void CreateReport0x00040004_2(XObject certificationInfo, IXmlLineInfo lineInfo)
        {
            // "When '@PNIO_Version' >= V2.4, 'CertificationInfo/@ConformanceClass' and '@ApplicationClass'
            //  must both be present or both be missing."
            string msg = Help.GetMessageString("M_0x00040004_2");
            string xpath = Help.GetXPath(certificationInfo);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040004_2");
        }

        private void CreateReport0x00040004_1(
            XAttribute conformanceClassNode,
            XAttribute applicationClassNode,
            XElement certificationInfoExt,
            XObject certificationInfo,
            IXmlLineInfo lineInfo)
        {
            if (conformanceClassNode != null
                && applicationClassNode != null
                && certificationInfoExt == null)
            {
                return;
            }

            // "When '@PNIO_Version' is < V2.4, 'CertificationInfo/@ConformanceClass' and '@ApplicationClass' must be present,
            //  and 'CertificationInfoExt' must not be present."
            string msg = Help.GetMessageString("M_0x00040004_1");
            string xpath = Help.GetXPath(certificationInfo);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040004_1");
        }
        /// <summary>
        /// Check number: CN_0x00040005
        /// 
        /// On InterfaceSubmoduleItem: Attribute SupportedRT_Classes and IsochroneModeInRT_Classes: new token values "RT_CLASS_STREAM_LO" and "RT_CLASS_STREAM_HI"
        /// InterfaceSubmoduleItem/TimeSynchronisation: new elements WorkingClock and GlobalTime with attribute Role with tokens "Master" and "Slave".
        /// 
        /// The following checks are implemented:
        /// (1) when CertificationInfoExt/@ConformanceClass="D", then
        ///     (a) the neighboring attribute ApplicationClass shall not contain the token "HighPerformance"
        ///     (b) SupportedRT_Classes must contain "RT_CLASS_STREAM_LO" and/or "RT_CLASS_STREAM_HI"
        ///     (c) TimeSynchronisation/WorkingClock must be present
        ///     (d) there must be at least one PortSubmoduleItem pluggable with MAUTypeList/MAUTypeItem/@SupportedFeatures is present and contains "TSN"
        ///     (e) MinDeviceInterval less or equal 128 ms
        /// (2) when @ConformanceClass and @ApplicationClass are missing, and CertificationInfoExt/@ConformanceClass="D" (a D-only device),
        ///     (a) @MulticastBoundarySupported must be missing or false
        ///     (b) the token "RT_CLASS_1" is not required in SupportedRT_Classes
        ///         (2b) - is removed
        ///         May be it is needed in GSDML V2.41 once again
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040005()
        {
            // Find all CertificationInfo nodes
            var certificationInfoNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo);
            certificationInfoNodes = Help.TryRemoveXElementsUnderXsAny(certificationInfoNodes, Nsmgr, Gsd);
            foreach (var certificationInfo in certificationInfoNodes)
            {
                // Get the DAP
                var dap = certificationInfo.Parent;

                // Get the attribute ConformanceClass
                var conformanceClass = certificationInfo.Attribute(Attributes.s_ConformanceClass);

                // Get the attribute ApplicationClass
                var applicationClass = certificationInfo.Attribute(Attributes.s_ApplicationClass);

                if (dap == null)
                {
                    continue;
                }

                var certificationInfoExtList = dap.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);
                foreach (var certificationInfoExt in certificationInfoExtList)
                {
                    // Get the attribute ConformanceClass
                    string conformanceClassExt = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ConformanceClass);

                    // Get the attribute ApplicationClass
                    string applicationClassExtValues = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ApplicationClass));
                    IList<string> applicationClassExtList = applicationClassExtValues.Split(Constants.s_Semicolon.ToCharArray());
                    // (1)
                    if (conformanceClassExt != "D")
                    {
                        continue;
                    }

                    // (1a)
                    CreateReport0x00040005_1(applicationClassExtList, certificationInfoExt);
                    // (1b)
                    var interfaceSubmoduleItem = dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();
                    CheckSupportedRTClassesContainsStreamCategory(interfaceSubmoduleItem, certificationInfoExt);
                    // (1c)
                    CreateReport0x00040005_3(dap, certificationInfoExt);
                    // (1d)
                    // Get the PortSubmoduleItems plugged or pluggable with this dap
                    bool tsnFound = GetTsnFound(dap);

                    CreateReport0x00040005_4(tsnFound, certificationInfoExt);
                    // (1e)
                    CreateReport0x00040005_5(dap, certificationInfoExt);
                    // (2)
                    CreateReport0x00040005_6(conformanceClass, applicationClass, interfaceSubmoduleItem);
                }
            }

            return true;
        }
        private bool GetTsnFound(XElement dap)
        {
            bool tsnFound = false;
            if (!DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
            {
                return false;
            }

            foreach (var portSubmoduleItem in portSubmoduleItems)
            {
                var mauTypeItemList = portSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                    .Where(x => x.Attribute(Attributes.s_SupportedFeatures) != null);
                foreach (var mauTypeItem in mauTypeItemList)
                {
                    string strSupportedFeatures =
                        Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_SupportedFeatures);
                    if (string.IsNullOrEmpty(strSupportedFeatures))
                    {
                        continue;
                    }

                    IList<string> supportedFeaturesList = strSupportedFeatures.Split(Constants.s_Semicolon.ToCharArray());
                    if (!supportedFeaturesList.Contains("TSN"))
                    {
                        continue;
                    }

                    tsnFound = true;
                    break;
                }
            }

            return tsnFound;
        }

        private void CreateReport0x00040005_6(
            XAttribute conformanceClass,
            XAttribute applicationClass,
            XElement interfaceSubmoduleItem)
        {
            if (conformanceClass != null
                || applicationClass != null
                || interfaceSubmoduleItem == null)
            {
                return;
            }

            // (2a)  must be missing or false
            bool multicastBoundarySupported = false;
            string strMulticastBoundarySupported = Help.GetAttributeValueFromXElement(
                interfaceSubmoduleItem,
                Attributes.s_MulticastBoundarySupported);
            if (!string.IsNullOrEmpty(strMulticastBoundarySupported))
                multicastBoundarySupported = XmlConvert.ToBoolean(strMulticastBoundarySupported);
            if (!multicastBoundarySupported)
            {
                return;
            }

            // "When '@ConformanceClass' and '@ApplicationClass' are missing, and 'CertificationInfoExt/@ConformanceClass'="D" (a D-only device),
            //  'InterfaceSubmoduleItem/@MulticastBoundarySupported' must be missing or false."
            string msg = Help.GetMessageString("M_0x00040005_6");
            string xpath = Help.GetXPath(interfaceSubmoduleItem);
            var xli = (IXmlLineInfo)interfaceSubmoduleItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040005_6");
            // (2b) - is removed
            //         May be it is needed in GSDML V2.41 once again
            //string strSupportedRT_Classes = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.SupportedRT_Classes);
            //if (!string.IsNullOrEmpty(strSupportedRT_Classes))
            //{
            //    IList<string> supportedRT_ClassesList = strSupportedRT_Classes.Split(Constants.Semicolon.ToCharArray());
            //    if (supportedRT_ClassesList.Contains("RT_CLASS_1"))
            //    {
            //        // "When '@ConformanceClass' and '@ApplicationClass' are missing, and 'CertificationInfoExt/@ConformanceClass'="D" (a D-only device),
            //        //  the token "RT_CLASS_1" must not be contained in 'InterfaceSubmoduleItem/@SupportedRT_Classes'."
            //        string msg = Help.GetMessageString("M_0x00040005_7");
            //        string xpath = Help.GetXPath(interfaceSubmoduleItem);
            //        var xli = (IXmlLineInfo)interfaceSubmoduleItem;
            //        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
            //                                      ReportCategories.TypeSpecific, "0x00040005_7");
            //    }
            //}
        }

        private void CreateReport0x00040005_5(XElement dap, XObject certificationInfoExt)
        {
            UInt16 minDeviceInterval = 0;
            string strMinDeviceInterval = Help.GetAttributeValueFromXElement(dap, Attributes.s_MinDeviceInterval);
            if (!string.IsNullOrEmpty(strMinDeviceInterval))
                minDeviceInterval = XmlConvert.ToUInt16(strMinDeviceInterval);
            if (minDeviceInterval <= 4096)
            {
                return;
            }

            // "When 'CertificationInfoExt/@ConformanceClass'="D", then
            //  'DeviceAccessPointItem/@MinDeviceInterval' must be <= 128 ms."
            string msg = Help.GetMessageString("M_0x00040005_5");
            string xpath = Help.GetXPath(certificationInfoExt);
            var xli = (IXmlLineInfo)certificationInfoExt;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040005_5");
        }

        private void CreateReport0x00040005_4(bool tsnFound, XObject certificationInfoExt)
        {
            if (tsnFound)
            {
                return;
            }

            // "When 'CertificationInfoExt/@ConformanceClass'="D", then there must be at least one PortSubmoduleItem
            //  pluggable with MAUTypeList/MAUTypeItem/@SupportedFeatures is present and contains "TSN"."
            string msg = Help.GetMessageString("M_0x00040005_4");
            string xpath = Help.GetXPath(certificationInfoExt);
            var xli = (IXmlLineInfo)certificationInfoExt;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040005_4");
        }

        private void CreateReport0x00040005_3(XContainer dap, XObject certificationInfoExt)
        {
            var timeSynchronisation = dap.Descendants(NamespaceGsdDef + Elements.s_TimeSynchronisation).FirstOrDefault();
            XElement workingClock = null;
            if (timeSynchronisation != null)
                workingClock = dap.Descendants(NamespaceGsdDef + Elements.WorkingClock).FirstOrDefault();
            if (workingClock != null)
            {
                return;
            }

            // "When 'CertificationInfoExt/@ConformanceClass'="D", then
            //  TimeSynchronisation/WorkingClock must be present."
            string msg = Help.GetMessageString("M_0x00040005_3");
            string xpath = Help.GetXPath(certificationInfoExt);
            var xli = (IXmlLineInfo)certificationInfoExt;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040005_3");
        }

        private void CreateReport0x00040005_1(ICollection<string> applicationClassExtList, XObject certificationInfoExt)
        {
            if (!applicationClassExtList.Contains("HighPerformance"))
            {
                return;
            }

            // "When 'CertificationInfoExt/@ConformanceClass'="D", then the neighboring
            //  attribute '@ApplicationClass' shall not contain the token "HighPerformance"."
            string msg = Help.GetMessageString("M_0x00040005_1");
            string xpath = Help.GetXPath(certificationInfoExt);
            var xli = (IXmlLineInfo)certificationInfoExt;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040005_1");
        }

        /// <summary>
        /// Check number: CN_0x00040006
        /// PortSubmoduleItem/MAUTypeList/MAUTypeItem: New attribute SupportedFeatures with the tokens "TSN", "TSN_TAS" and "TSN_Preemption".
        /// 
        /// The following checks are implemented:
        /// MAUTypeList/MAUTypeItem/@SupportedFeatures:
        /// (1) may not be present when PNIO_Version less than V2.4
        /// (2) Token "TSN_TAS" and/or "TSN_Preemption" require token "TSN"
        /// (3) when Token "TSN" is given, MAUType values less or equal 100 Mbit/s need token "TSN_TAS"
        /// (4) when Token "TSN" is given, MAUType values less or equal 1 Gbit/s need token "TSN_Preemption"
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00040006()
        {
            // Find all MAUTypeItem nodes with attribute SupportedFeatures
            var mauTypeItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                .Where(x => x.Attribute(Attributes.s_SupportedFeatures) != null);
            mauTypeItems = Help.TryRemoveXElementsUnderXsAny(mauTypeItems, Nsmgr, Gsd);
            foreach (var mauTypeItem in mauTypeItems)
            {
                // (1)
                if (CreateReport0x00040006_1(mauTypeItem))
                {
                    continue; // Port is defined but not referenced
                }

                // (2)
                string strSupportedFeatures = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_SupportedFeatures);
                if (string.IsNullOrEmpty(strSupportedFeatures))
                {
                    continue;
                }

                {
                    IList<string> supportedFeaturesList = strSupportedFeatures.Split(Constants.s_Semicolon.ToCharArray());
                    CreateReport0x00040006_2(supportedFeaturesList, mauTypeItem);
                    // (3)
                    if (!supportedFeaturesList.Contains("TSN"))
                    {
                        continue;
                    }

                    {
                        string strMauTypesUpTo100Mbit = "0 5 10..13 15..18 42..46 105 141 144";
                        ValueListHelper.NormalizeValueList(strMauTypesUpTo100Mbit, out List<ValueListHelper.ValueRangeT> mauTypesUpTo100Mbit);

                        UInt16 mauTypeValue = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value));
                        CreateReport0x00040006_3(supportedFeaturesList, mauTypesUpTo100Mbit, mauTypeValue, mauTypeItem);
                        // (4)
                        string strMauTypesUpTo1Gbit = "21..26 29..30 47..53 56..58 79..83";
                        ValueListHelper.NormalizeValueList(strMauTypesUpTo1Gbit, out List<ValueListHelper.ValueRangeT> mauTypesUpTo1Gbit);

                        CreateReport0x00040006_4(supportedFeaturesList, mauTypesUpTo1Gbit, mauTypeValue, mauTypeItem);
                    }
                }
            }

            return true;
        }

        private void CreateReport0x00040006_4(
            ICollection<string> supportedFeaturesList,
            IReadOnlyList<ValueListHelper.ValueRangeT> mauTypesUpTo1Gbit,
            ushort mauTypeValue,
            XObject mauTypeItem)
        {
            if (supportedFeaturesList.Contains("TSN_Preemption"))
            {
                return;
            }

            for (int currentRange = 0; currentRange < mauTypesUpTo1Gbit.Count; currentRange++)
            {
                if (mauTypeValue < mauTypesUpTo1Gbit[currentRange].From
                    || mauTypeValue > mauTypesUpTo1Gbit[currentRange].To)
                {
                    continue;
                }

                // "When 'MAUTypeList/MAUTypeItem/@SupportedFeatures' with
                //  token "TSN" and 'MAUTypeItem/@Value' <= 1 Gbit/s is given '@SupportedFeatures' must also contain "TSN_Preemption"."
                string msg = Help.GetMessageString("M_0x00040006_4");
                string xpath = Help.GetXPath(mauTypeItem);
                var xli = (IXmlLineInfo)mauTypeItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040006_4");
            }
        }

        private void CreateReport0x00040006_3(
            ICollection<string> supportedFeaturesList,
            IReadOnlyList<ValueListHelper.ValueRangeT> mauTypesUpTo100Mbit,
            ushort mauTypeValue,
            XObject mauTypeItem)
        {
            if (supportedFeaturesList.Contains("TSN_TAS"))
            {
                return;
            }

            for (int currentRange = 0; currentRange < mauTypesUpTo100Mbit.Count; currentRange++)
            {
                if (mauTypeValue < mauTypesUpTo100Mbit[currentRange].From
                    || mauTypeValue > mauTypesUpTo100Mbit[currentRange].To)
                {
                    continue;
                }

                // "When 'MAUTypeList/MAUTypeItem/@SupportedFeatures' with
                //  token "TSN" and 'MAUTypeItem/@Value' <= 100 Mbit/s is given '@SupportedFeatures' must also contain "TSN_TAS"."
                string msg = Help.GetMessageString("M_0x00040006_3");
                string xpath = Help.GetXPath(mauTypeItem);
                var xli = (IXmlLineInfo)mauTypeItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040006_3");
            }
        }

        private void CreateReport0x00040006_2(ICollection<string> supportedFeaturesList, XObject mauTypeItem)
        {
            if ((!supportedFeaturesList.Contains("TSN_TAS") && !supportedFeaturesList.Contains("TSN_Preemption"))
                || supportedFeaturesList.Contains("TSN"))
            {
                return;
            }

            // "'MAUTypeList/MAUTypeItem/@SupportedFeatures' contains
            //  token "TSN_TAS" and/or "TSN_Preemption" but not required token "TSN"."
            string msg = Help.GetMessageString("M_0x00040006_2");
            string xpath = Help.GetXPath(mauTypeItem);
            var xli = (IXmlLineInfo)mauTypeItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00040006_2");
        }

        private bool CreateReport0x00040006_1(XObject mauTypeItem)
        {
            if (mauTypeItem.Parent == null)
            {
                return false;
            }

            var portSubmoduleItem = mauTypeItem.Parent.Parent;

            if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
                return true;

            foreach (var dap in dapsOfPort)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (!(pnioVersion < 2.4))
                {
                    continue;
                }

                // "'MAUTypeList/MAUTypeItem/@SupportedFeatures'
                //  may not be present when '@PNIO_Version' < V2.4."
                string msg = Help.GetMessageString("M_0x00040006_1");
                string xpath = Help.GetXPath(mauTypeItem);
                var xli = (IXmlLineInfo)mauTypeItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00040006_1");
                break;
            }

            return false;
        }

        #endregion
    }
}