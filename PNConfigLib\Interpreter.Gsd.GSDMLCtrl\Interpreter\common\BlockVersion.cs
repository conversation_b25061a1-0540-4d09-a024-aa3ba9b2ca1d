/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BlockVersion.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Runtime.InteropServices;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
	/// <summary>
	/// Summary description for BlockVersion.
	/// </summary>
    [ComVisible(true), Guid("60423AE7-3FEC-49b8-B78F-B8C5378EE912")]
	public class BlockVersion : 
		GsdObject,
		IBlockVersion
	{
		//########################################################################################
		#region Initialization & Termination

		/// <summary>
		/// Initializes the BitDataItem if it is instantiated.
		/// The properties of the object are all initialized to empty 
		/// or with abstract default values.
		/// </summary>
		/// <remarks>The real content can only be written to the object by the Fill
		/// method, which is only available within this assembly.</remarks>
		public BlockVersion()
		{
			m_Name = String.Empty;
			m_High = 0;
			m_Low = 0;
		}

		#endregion

		//########################################################################################
		#region Fields

		// Declaration of the properties
		private string m_Name;
		private uint m_High;
		private uint m_Low;

		#endregion

		//########################################################################################
		#region Properties

		/// <summary>
		/// Accesses the language specific name of the bit data item.
		/// </summary>
		public string Name
		{
			get { return this.m_Name; }
		}

		/// <summary>
		/// Accesses the ...
		/// </summary>
		public System.UInt32 High
		{
			get { return this.m_High; }
		}

		/// <summary>
		/// Accesses the ...
		/// </summary>
		public System.UInt32 Low
		{
			get { return this.m_Low; }
		}


        #region COM Interface Members Only
        // ONLY for the COM interface.
  
        #endregion

        #endregion


        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter
				if (null == hash)
					throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Name = hash[member] as string;

                member = Models.s_FieldHigh;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_High = (uint)hash[member];

                member = Models.s_FieldLow;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_Low = (uint)hash[member];

                // Base data.
                //succeeded = base.Fill(hash);
            }
			catch(FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			bool succeeded = true;

			try
			{
				// Object begin.
				writer.WriteStartElement(Export.s_ElementObject);
				writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectBlockVersionElement);

				// ----------------------------------------------
				succeeded = this.SerializeMembers(option, ref writer);
				if (!succeeded)
					throw new SerializationException("Couldn't serialize members!");

				// Object end.
				writer.WriteEndElement();
			}
			catch(ArgumentException)
			{
				succeeded = false;
			}
			catch(SerializationException)
			{
				succeeded = false;
			}

			return succeeded; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			bool succeeded = true;

			try
			{
				// ----------------------------------------------
				Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
				Export.WriteUint32Property(ref writer, Models.s_FieldHigh, this.m_High);
				Export.WriteUint32Property(ref writer, Models.s_FieldLow, this.m_Low);
			}
			catch(ArgumentException)
			{
				succeeded = false;
			}
			catch(SerializationException)
			{
				succeeded = false;
			}

			return succeeded; 
		}


		#endregion
	}
}

