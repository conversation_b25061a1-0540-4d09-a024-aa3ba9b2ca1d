﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DevicePortConfigureManager.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class DevicePortConfigureManager
    {
        private Project m_Project;

        private Topology.Topology m_Topology
        {
            get;
        }

        internal DevicePortConfigureManager(Project project, Topology.Topology topology) 
        {
            m_Topology = topology;
            m_Project = project;
        }

        internal void Configure(List<PortType> xmlPorts,
           Interface portContainer,
           DecentralDeviceCatalog decentralDeviceCatalog,
           string interfaceRefId,
           string gsdPath)
        {
            Dictionary<int, PortCatalog> systemDefinedPortLookup = FillPortCatalogValues(decentralDeviceCatalog, gsdPath);
            foreach (PortType xmlPort in xmlPorts)
            {
                Port port = new Port(xmlPort.PortNumber, (int)xmlPort.SubslotNumber);
                if (!string.IsNullOrEmpty(xmlPort.GSDRefID))
                {
                    port = new Port(xmlPort.PortNumber, (int)xmlPort.SubslotNumber);
                    port.PCLCatalogObject = Catalog.PortList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", gsdPath, xmlPort.GSDRefID)];
                }
                else
                {
                    if ((decentralDeviceCatalog.Interface.PortList != null)
                        && (decentralDeviceCatalog.Interface.PortList.Count != 0))
                    {
                        AttributeAccessCode ac = new AttributeAccessCode();
                        int subslotNumber =
                            decentralDeviceCatalog.Interface.PortList.Values[(int)xmlPort.PortNumber - 1].AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnSubslotNumber,
                                ac,
                                -1);

                        if (!ac.IsOkay)
                        {
                            throw new PNFunctionsException("Could not get subslot number for PortCatalog object.");
                        }

                        port = new Port(xmlPort.PortNumber, subslotNumber);
                        port.PCLCatalogObject = decentralDeviceCatalog.Interface.PortList.Values[(int)xmlPort.PortNumber - 1];
                    }
                    else if ((systemDefinedPortLookup != null)
                             && (systemDefinedPortLookup.Values.Count > 0))
                    {
                        port = new Port(
                            xmlPort.PortNumber,
                            systemDefinedPortLookup.Keys.ToList()[(int)xmlPort.PortNumber - 1]);
                        port.PCLCatalogObject = systemDefinedPortLookup.Values.ToList()[(int)xmlPort.PortNumber - 1];
                    }
                }
                if (port.PCLCatalogObject != null)
                {
                    portContainer.AddPort(port);
                    IPortBL portBL = PNBasePortBL.PortBLCreator(port);
                    bool isLocal;
                    portBL.Configure(
                        xmlPort,
                        ProjectManagerUtilities.FindPortInterconnectionByInterfaceAndPortNumber(
                            interfaceRefId,
                            xmlPort.PortNumber,
                            m_Topology,
                            out isLocal),
                            xmlPorts.Count,
                        isLocal);

                    m_Project.BusinessLogicList.Add(portBL);
                }
            }
        }

        internal static Dictionary<int, PortCatalog> FillPortCatalogValues(DecentralDeviceCatalog decentralDeviceCatalog, string gsdPath)
        {
            Dictionary<int, PortCatalog> portsOfDeviceLookup = new Dictionary<int, PortCatalog>();

            Dictionary<int, PortCatalog> fixedPortsOfDevice =
                ProjectManagerUtilities.GetDAPPortLookupBySlotRelation(
                    decentralDeviceCatalog,
                    gsdPath,
                    SlotRelationType.FixedInSlots);

            if ((fixedPortsOfDevice != null) && (fixedPortsOfDevice.Count > 0))
            {
                foreach (KeyValuePair<int, PortCatalog> fixedPort in fixedPortsOfDevice)
                {
                    if (!portsOfDeviceLookup.ContainsKey(fixedPort.Key))
                    {
                        portsOfDeviceLookup.Add(fixedPort.Key, fixedPort.Value);
                    }
                }
            }

            Dictionary<int, PortCatalog> usedPortsOfDevice =
                ProjectManagerUtilities.GetDAPPortLookupBySlotRelation(
                    decentralDeviceCatalog,
                    gsdPath,
                    SlotRelationType.UsedInSlots);

            if ((usedPortsOfDevice != null) && (usedPortsOfDevice.Count > 0))
            {
                foreach (KeyValuePair<int, PortCatalog> usedPort in usedPortsOfDevice)
                {
                    if (!portsOfDeviceLookup.ContainsKey(usedPort.Key))
                    {
                        portsOfDeviceLookup.Add(usedPort.Key, usedPort.Value);
                    }
                }
            }
            return portsOfDeviceLookup;
        }
    }
}
