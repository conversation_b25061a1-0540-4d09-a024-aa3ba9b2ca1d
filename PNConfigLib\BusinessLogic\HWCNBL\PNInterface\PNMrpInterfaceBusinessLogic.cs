/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNMrpInterfaceBusinessLogic.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.MultipleMrp;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNMrpInterfaceBusinessLogic : IFDecorator
    {
        public PNMrpInterfaceBusinessLogic(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        public override void InitBL()
        {
            InitActions();
        }

        private void InitActions()
        {
            Interface.BaseActions.RegisterMethod(
                GetInterfaceMrpParameterBlocks.Name,
                GenericMethodGetInterfaceMrpParameterBlocks);
            Interface.BaseActions.RegisterMethod(GetSelectedRingPorts.Name, GenericMethodGetSelectedRingPorts);
            Interface.BaseActions.RegisterMethod(GetIsMultipleMrpActive.Name, GenericMethodIsMultipleMrpActive);
            //This methad can be called only by multiple mrp supported devices
            if (MultipleMrpUtilities.IsMultipleInstancesAvailable(Interface))
            {
                Interface.BaseActions.RegisterMethod(
                    GetSelectedRingPortsOfInstance.Name,
                    GenericMethodGetSelectedRingPortsOfInstance);
            }
            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodConsistencyCheck);
        }

        public override void Configure(IConfigInterface xmlDeviceInterface, SyncDomainType syncDomainType)
        {
            DecentralDeviceTypeDecentralDeviceInterface xmlDecentralDeviceInterface =
                xmlDeviceInterface as DecentralDeviceTypeDecentralDeviceInterface;

            if (xmlDecentralDeviceInterface != null)
            {
                base.Configure(xmlDecentralDeviceInterface, syncDomainType);

                List<MrpRingType> xmlMrpRing = xmlDecentralDeviceInterface.AdvancedOptions.MediaRedundancy;
                if (xmlMrpRing != null
                    && xmlMrpRing.Count > 0)
                {
                    Enumerated mrpRoleEnumerated =
                        Interface.PCLCatalogObject.AttributeAccess.GetAnyAttribute<Enumerated>(
                            InternalAttributeNames.PnMrpRole,
                            new AttributeAccessCode(),
                            null);
                    if (mrpRoleEnumerated != null)
                    {
                        if (xmlMrpRing.Count(ring => ring.MrpRole != MrpRole.Notdeviceinthering) == 1)
                        {
                            MrpRingType currentRing =
                                xmlMrpRing.First(ring => ring.MrpRole != MrpRole.Notdeviceinthering);
                            currentRing.InstanceNumber = 1;
                            PNMrpRole PNMrpRole = MapPNMrpRole(currentRing.MrpRole);
                            Interface.AttributeAccess.SetAnyAttribute<UInt32>(
                                InternalAttributeNames.PnMrpRole,
                                Convert.ToUInt32(PNMrpRole, CultureInfo.InvariantCulture));
                            Interface.AttributeAccess.SetAnyAttribute(
                                InternalAttributeNames.PnMrpDiagnosis,
                                currentRing.DiagnosticsInterrupts);
                        }

                        foreach (MrpRingType mrpRing in xmlMrpRing)
                        {
                            ConfigureMrpAttributes(
                                mrpRing.MrpDomainRefID,
                                MapPNMrpRole(mrpRing.MrpRole),
                                mrpRing.DiagnosticsInterrupts,
                                mrpRing.RingPort,
                                mrpRing.InstanceNumber);
                        }
                    }
                }
            }
        }

        private PNMrpRole MapPNMrpRole(MrpRole mrpRole)
        {
            PNMrpRole retval = PNMrpRole.NotInRing;
            switch (mrpRole)
            {
                case MrpRole.MrpManager:
                    retval = PNMrpRole.Manager;
                    break;
                case MrpRole.MrpClient:
                    retval = PNMrpRole.Client;
                    break;
                case MrpRole.MrpAutoManager:
                    retval = PNMrpRole.NormManagerAuto;
                    break;
                case MrpRole.Notdeviceinthering:
                    retval = PNMrpRole.NotInRing;
                    break;
            }
            return retval;
        }

        private MrpDomain GetInterfaceMrpDomainByDomainId(string mrpDomainId)
        {
            List<MrpDomain> mrpDomains = Interface.Node.Subnet.DomainList
                .Where(dmn => dmn is MrpDomain).OfType<MrpDomain>().ToList();
            return mrpDomains.FirstOrDefault(x => x.Id == mrpDomainId);
        }

        private void ConfigureMrpAttributes(
            string mrpDomainId,
            PNMrpRole mrpRole,
            bool diagnosticsInterrupts,
            List<MrpRingTypeRingPort> ringPorts,
            int instanceNumber)
        {
            List<DataModel.PCLObjects.Port> mrpPorts = new List<DataModel.PCLObjects.Port>();
            if (mrpRole != PNMrpRole.NotInRing)
            {
                foreach (MrpRingTypeRingPort ringPort in ringPorts)
                {
                    mrpPorts.Add(
                        NavigationUtilities.GetPort(
                            Interface,
                            ringPort.PortNumber,
                            ringPort.SlotNumberSpecified,
                            ringPort.SlotNumberSpecified ? (uint)ringPort.SlotNumber : 0));
                }
                mrpPorts.ForEach(
                    p => p.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnMrpIsSelectedRingPort, true));
            }

            MrpDomain currentMrpDomain = GetInterfaceMrpDomainByDomainId(mrpDomainId);
            if (currentMrpDomain != null)
            {
                MrpDomainInstance mrpInstance = new MrpDomainInstance(
                    currentMrpDomain.Id,
                    AttributeUtilities.GetName(currentMrpDomain),
                    mrpRole,
                    mrpPorts,
                    instanceNumber);
                FillMrpInstanceAttributes(mrpInstance, diagnosticsInterrupts);
                Interface.MrpDomainInstances.Add(mrpInstance);
                if (!currentMrpDomain.Participants.Contains(Interface))
                {
                    currentMrpDomain.Participants.Add(Interface);
                }
            }
        }

        private void FillMrpInstanceAttributes(
            MrpDomainInstance mrpInstance,
            bool diagnosticsInterrupts,
            int instanceNumber = 0)
        {
            if (mrpInstance != null)
            {
                mrpInstance.AttributeAccess.AddAnyAttribute<Int32>(
                    InternalAttributeNames.PnInstanceNumber,
                    instanceNumber);
                mrpInstance.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnInstanceDiagnostics,
                    diagnosticsInterrupts);
            }
        }

        /// <summary>
        /// Creates the mrp-interface related parts for PDEV 1A block
        /// Supported datasets: 0x8051-PDInterfaceMrpDataCheck, 0x8052-PDInterfaceMrpDataAdjust. 
        /// </summary>
        private void GenericMethodGetInterfaceMrpParameterBlocks(IMethodData methodData)
        {
            if (methodData != null
                && methodData.Arguments["central"] != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                if (Interface != null
                    && Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.PnPdevParametrizationDecentral,
                        ac,
                        0) == 1)
                {
                    return;
                }
            }


            int dataSet = Convert.ToInt32(
                methodData.Arguments[GetConfig2002Pdev1ABlock.SelectedDataset],
                CultureInfo.InvariantCulture);
            bool isRecordwithoutHeader = false;
            for (int i = 0; i < methodData.Arguments.Count; i++)
            {
                if (methodData.Arguments.AllKeys[i] == RecordwithoutHeader.isRecordwithoutHeader)
                    isRecordwithoutHeader = true;
            }

            byte[] prmBlock = null;
            bool returnValue = false;
            try
            {
                switch (dataSet)
                {
                    case 0x8051:
                        prmBlock = ConfigUtility.GetPDInterfaceMrpDataCheck(Interface, out returnValue);
                        methodData.ReturnValue = returnValue;
                        break;
                    case 0x8052:
                        prmBlock = ConfigUtility.GetPDInterfaceMrpDataAdjust(Interface, out returnValue);
                        methodData.ReturnValue = returnValue;
                        break;
                    default:
                        Debug.Fail("Selected Interface Mrp Dataset must have a valid Dataset Number (0x8051 or 08052)");
                        break;
                }

            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetInterfaceMrpParameterBlocks", e);
            }

            if (returnValue)
            {
                if (isRecordwithoutHeader)
                {
                    // assign the output parameter of the method
                    methodData.ReturnValue = prmBlock;
                    return;
                }

                ParameterDatasetStruct block = new ParameterDatasetStruct();
                block.ParaDSNumber = dataSet;
                block.ParaDSIdentifier = 0;
                // add parameter block into DS Struct
                block.AddParaBlock(prmBlock);
                // assign the output parameter of the method
                methodData.Arguments[GetInterfaceMrpParameterBlocks.PDInterfaceMrpDataBlockEntry] = block.ToByteArray;
            }
            else
            {
                if (isRecordwithoutHeader)
                    methodData.ReturnValue = null;
            }
        }

        /// <summary>
        /// Returns the selected ring ports of the interface.
        /// </summary>
        private void GenericMethodGetSelectedRingPorts(IMethodData methoddata)
        {
            methoddata.Arguments[GetSelectedRingPorts.SelectedRingPorts] = GetSelectedMrpPorts();
        }

        /// <summary>
        /// Returns the selected ring ports of the interface.
        /// </summary>
        /// <returns>An IConfigObjectCollection which contains the selected mrp ports</returns>
        /// <remarks>The method returns default mrp ring ports if user haven't selected
        /// any ports manually.</remarks>
        private List<DataModel.PCLObjects.Port> GetSelectedMrpPorts()
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(Interface);
            List<DataModel.PCLObjects.Port> selectedRingPorts = new List<DataModel.PCLObjects.Port>();
            List<DataModel.PCLObjects.Port> defaultRingPorts = new List<DataModel.PCLObjects.Port>();

            AttributeAccessCode ac = new AttributeAccessCode();
            // Check the user selected ring ports
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                if (port.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnMrpIsSelectedRingPort,
                    ac.GetNew(),
                    false))
                {
                    selectedRingPorts.Add(port);
                }
                if (port.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnMrpIsDefaultRingPort,
                    ac.GetNew(),
                    false))
                {
                    defaultRingPorts.Add(port);
                }
            }

            // Interfaces supporting mrp can be added without port submodules (scalance)
            if (defaultRingPorts.Count == 0)
            {
                return selectedRingPorts;
            }

            // If the user doesn't manually configure the ring ports, 
            // fill the selected ring ports by default ring ports.
            // More than 2 ports can be selected as default ringports. 
            // If this is the case, get the ports with smallest port numbers.
            // Note: selectedRingPorts.Count == 1 means that the user manually selected
            // a ring port but it is the same as the port in the other combobox.
            // This is a consistency error, so don't fill with default ports in this case
            if (selectedRingPorts.Count == 0)
            {
                defaultRingPorts.Sort(Utility.ComparePortsBySlotAndPortNumber);
                // Find the next default port which is not selected by the user.
                for (int i = 0; i < 2; i++)
                {
                    if (i < defaultRingPorts.Count)
                    {
                        selectedRingPorts.Add(defaultRingPorts[i]);
                    }
                }
            }

            return selectedRingPorts;
        }

        /// <summary>
        /// Returns if multiple mpr is active on this interface or not
        /// </summary>
        private void GenericMethodIsMultipleMrpActive(IMethodData methoddata)
        {
            methoddata.Arguments[GetIsMultipleMrpActive.IsMultipleMrpActive] =
                MultipleMrpUtilities.IsMultipleInstancesActive(Interface);
        }

        /// <summary>
        /// Returns the selected ring ports of the instance from multiple mrp supported interface.
        /// </summary>
        private void GenericMethodGetSelectedRingPortsOfInstance(IMethodData methoddata)
        {
            methoddata.Arguments[GetSelectedRingPortsOfInstance.SelectedRingPorts] =
                MultipleMrpUtilities.GetSelectedPortConfigsOfInstance(
                    Interface,
                    (int)methoddata.Arguments[GetSelectedRingPortsOfInstance.InstanceNumber]);
        }

        #region Consistency Checks

        private void MethodConsistencyCheck()
        {
            //Check if the interface is an active member of the ring
            AttributeAccessCode ac = new AttributeAccessCode();

            uint numberOfInstances = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpMaxInstances,
                new AttributeAccessCode(),
                0);

            bool mrpSupported =
                Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnMrpSupported,
                    ac.GetNew(),
                    false);
            PNMrpRole currRole = (PNMrpRole)Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole,
                ac.GetNew(),
                (UInt32)PNMrpRole.NotInRing);
            if (!mrpSupported
                || (currRole == PNMrpRole.NotInRing && !MultipleMrpUtilities.IsMultipleInstancesActive(Interface)))
            {
                return;
            }

            if (numberOfInstances > 1)
            {
                if (MultipleMrpUtilities.IsMultipleInstancesActive(Interface))
                {
                    MultipleMrpConsistencyChecker.CheckMultipleMRPRolesAndDomains(Interface);
                    MultipleMrpConsistencyChecker.CheckMultipleMRPRingPorts(Interface);
                }
                else
                {
                    //If multiple mrp is not active then single mrp consistency checks should be run
                    MultipleMrpConsistencyChecker.CheckSingleMRPRoles(Interface);
                    CheckSelectedPorts();
                }
            }
            else
            {
                MultipleMrpConsistencyChecker.CheckSingleMRPRoles(Interface);
                CheckSelectedPorts();
            }

            CheckSubnetConnection();
            CheckFSU();
            CheckSyncRole();
        }

        /// <summary>
        /// This method checks if the interface is connected to a net if it is active.
        /// </summary>
        private void CheckSubnetConnection()
        {
            PNMrpRole currRole = (PNMrpRole)Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole,
                new AttributeAccessCode(),
                (UInt32)PNMrpRole.NotInRing);
            if (currRole == PNMrpRole.NotInRing)
            {
                return;
            }

            // Get the subnet of the interface
            if (Interface.Node == null
                || Interface.Node.Subnet == null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.MrpNoNetwork,
                    Utility.GetNameWithContainer(Interface));
            }
        }

        /// <summary>
        /// This method checks if prioritized startup is activated.
        /// MRP and prioritized startup are not available together. (MRP-K3)
        /// </summary>
        private void CheckFSU()
        {
            if (Interface.AttributeAccess.GetAnyAttribute<Boolean>(
                InternalAttributeNames.PnIoDeviceFSUPriority,
                new AttributeAccessCode(),
                false))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Warning,
                    Interface,
                    ConsistencyConstants.MrpFastStartupNotAllowed,
                    Utility.GetNameWithContainer(Interface));
            }
        }

        /// <summary>
        /// This method checks if synchronization is used. 
        /// MRP and any synchronization role other than "Unsynchronized" are not available together. (MRP-K4)
        /// An exception of this rule is interface submodules supporting MRPD. In this case it is required 
        /// to configure synchronization and MRP.
        /// </summary>
        private void CheckSyncRole()
        {
            List<PclObject> ioConnectors = NavigationUtilities.GetIOConnectors(Interface);
            if (ioConnectors == null)
            {
                return;
            }
            
            // PCL does not support MRPD. Therefore, mrpdSupported check is removed.
            // Even if interface supports MRPD, it is not allowed to project MRPD in PCL.

            foreach (PclObject ioConnector in ioConnectors)
            {
                // Check whether the interface submodule is synchronized.
                if (PNAttributeUtility.GetAdjustedSyncRole(ioConnector) == PNIRTSyncRole.NotSynchronized)
                {
                    continue;
                }

                // Prepare the error message.
                string ioConnectorText = string.Format(
                    CultureInfo.InvariantCulture,
                    @"{0}/{1}/{2}",
                    AttributeUtilities.GetName(Interface.ParentObject),
                    AttributeUtilities.GetName(Interface),
                    AttributeUtilities.GetName(ioConnector));
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface.ParentObject,
                    ConsistencyConstants.MrpSyncNotAllowed,
                    ioConnectorText);
            }
        }

        /// <summary>
        /// This method checks if there are exactly two ports are selected as ring ports. 
        /// </summary>
        private void CheckSelectedPorts()
        {
            // It is here known that the interface supports mrp and its role is not "not in ring"
            if (GetSelectedMrpPorts().Count == 2)
            {
                return;
            }
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                Interface,
                ConsistencyConstants.MrpWrongPortNumber,
                Utility.GetNameWithContainer(Interface));
        }

        #endregion
    }
}
