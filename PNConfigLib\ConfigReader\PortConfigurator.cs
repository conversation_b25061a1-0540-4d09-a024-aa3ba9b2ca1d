﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: PortConfigurator.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.PNProjectManager;
using System.Collections.Generic;

namespace PNConfigLib.ConfigReader
{
    internal static class PortConfigurator
    {
        internal static Dictionary<int, PortCatalog> FillPortCatalogValues(DecentralDeviceCatalog decentralDeviceCatalog, string gsdPath)
        {
            Dictionary<int, PortCatalog> portsOfDeviceLookup = new Dictionary<int, PortCatalog>();

            Dictionary<int, PortCatalog> fixedPortsOfDevice =
                ProjectManagerUtilities.GetDAPPortLookupBySlotRelation(
                    decentralDeviceCatalog,
                    gsdPath,
                    SlotRelationType.FixedInSlots);

            if ((fixedPortsOfDevice != null) && (fixedPortsOfDevice.Count > 0))
            {
                foreach (KeyValuePair<int, PortCatalog> fixedPort in fixedPortsOfDevice)
                {
                    if (!portsOfDeviceLookup.ContainsKey(fixedPort.Key))
                    {
                        portsOfDeviceLookup.Add(fixedPort.Key, fixedPort.Value);
                    }
                }
            }

            Dictionary<int, PortCatalog> usedPortsOfDevice =
                ProjectManagerUtilities.GetDAPPortLookupBySlotRelation(
                    decentralDeviceCatalog,
                    gsdPath,
                    SlotRelationType.UsedInSlots);

            if ((usedPortsOfDevice != null) && (usedPortsOfDevice.Count > 0))
            {
                foreach (KeyValuePair<int, PortCatalog> usedPort in usedPortsOfDevice)
                {
                    if (!portsOfDeviceLookup.ContainsKey(usedPort.Key))
                    {
                        portsOfDeviceLookup.Add(usedPort.Key, usedPort.Value);
                    }
                }
            }
            return portsOfDeviceLookup;
        }

        internal static void FillSystemDefinedValuesToPort(Dictionary<int, PortCatalog> systemDefinedPortLookup, SortedList<uint, PortCatalog> portCatalog, Dictionary<uint, PortCatalog> catalogLookup)
        {
            foreach (int subslotNumber in systemDefinedPortLookup.Keys)
            {
                if (!catalogLookup.ContainsKey((uint)subslotNumber))
                {
                    catalogLookup.Add((uint)subslotNumber, systemDefinedPortLookup[subslotNumber]);
                }
            }
            if (portCatalog != null)
            {
                foreach (uint subslotNumber in portCatalog.Keys)
                {
                    if (!catalogLookup.ContainsKey(subslotNumber))
                    {
                        catalogLookup.Add(subslotNumber, portCatalog[subslotNumber]);
                    }
                }
            }
        }
    }
}
