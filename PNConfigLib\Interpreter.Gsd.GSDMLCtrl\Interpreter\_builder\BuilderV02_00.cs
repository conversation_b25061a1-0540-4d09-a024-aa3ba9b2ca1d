/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_00.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Xml.XPath;
using C = PNConfigLib.Gsd.Interpreter.Common;
using GSDI;
using System.Collections.Generic;
using System.Globalization;
using System.Diagnostics;
using System.Linq;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal class BuilderV0200 :
        BuilderV0100
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0200()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version20);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectBitDataItem:
                        {
                            // NOTE: Navigator must point to BitDataItem.
                            PrepareBitDataItem(nav, ref hash);
                            obj = new C.BitDataItem();

                            break;
                        }
                    case Models.s_ObjectFParameterRecordData:
                        {
                            // NOTE: Navigator must point to F_ParameterRecordDataItem.
                            PrepareFParameterRecordData(nav, ref hash);
                            obj = new C.FParameterRecordData();

                            break;
                        }
                    case Models.s_ObjectSubslot:
                        {
                            // NOTE: Navigator must point to SubslotItem.
                            PrepareSubslot(nav, ref hash);
                            obj = new C.Subslot();

                            break;
                        }
                    case Models.s_ObjectInterfaceSubmodule:
                        {
                            // NOTE: Navigator must point to InterfaceSubmoduleItem.
                            PrepareInterfaceSubmodule(nav, ref hash);
                            obj = new C.InterfaceSubmodule();

                            break;
                        }
                    case Models.s_ObjectPortSubmodule:
                        {
                            // NOTE: Navigator must point to PortSubmoduleItem.
                            PreparePortSubmodule(nav, ref hash);
                            obj = new C.PortSubmodule();

                            break;
                        }
                    case Models.s_ObjectIsochroneMode:
                        {
                            // NOTE: Navigator must point to IsochroneMode.
                            PrepareIsochroneMode(nav, ref hash);
                            obj = new C.IsochroneMode();

                            break;
                        }
                    case Models.s_ObjectRtClass3TimingProperties:
                        {
                            // NOTE: Navigator must point to RT_Class3TimingProperties.
                            PrepareRTClass3TimingProperties(nav, ref hash);
                            obj = new C.TimingProperties();

                            break;
                        }
                    case Models.s_ObjectSubmodulePlugData:
                        {
                            // NOTE: Navigator must point to VirtualSubmoduleItem.
                            PrepareSubmodulePlugData(nav, ref hash);
                            obj = new C.ModulePlugData();

                            break;
                        }
                    case Models.s_ObjectExtendedChannelDiagnostic:
                        {
                            // NOTE: Navigator must point to ChannelDiagItem.
                            PrepareExtendedChannelDiagnostic(nav, ref hash);
                            obj = new C.ChannelDiagnostic();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Structure Model Methods

        protected override void PrepareAccessPointStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRequiredSchemaVersion, null);

            base.PrepareAccessPointStructureElement(nav, ref hash);

            // Get RequiredSchemaVersion info. Optional.
            // NOTE: The CompatibilityVersion is directly generated from the RequiredSchemaVersion.
            string rsv = nav.GetAttribute(Attributes.s_RequiredSchemaVersion, String.Empty);
            if (string.IsNullOrEmpty(rsv))
                rsv = Constants.s_Version10;
            hash[Models.s_FieldRequiredSchemaVersion] = rsv;
        }

        protected override void PrepareModuleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRequiredSchemaVersion, null);

            base.PrepareModuleStructureElement(nav, ref hash);

            // Get RequiredSchemaVersion info. Optional.
            // NOTE: The CompatibilityVersion is directly generated from the RequiredSchemaVersion.
            string rsv = nav.GetAttribute(Attributes.s_RequiredSchemaVersion, String.Empty);
            if (string.IsNullOrEmpty(rsv))
                rsv = Constants.s_Version10;
            hash[Models.s_FieldRequiredSchemaVersion] = rsv;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        // Overrided methods
        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for DeviceAccessPoint object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldRequiredSchemaVersion, null);
            hash.Add(Models.s_FieldIsMultipleWriteSupported, null);
            hash.Add(Models.s_FieldIsIoxsRequired, null);
            hash.Add(Models.s_FieldDeviceSpecificToolName, null);
            hash.Add(Models.s_FieldDeviceSpecificToolDescription, null);
            hash.Add(Models.s_FieldSubslots, null);
            hash.Add(Models.s_FieldSystemDefinedSubmodules, null);

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            // --------------------------------------------

            // Get MultipleWriteSupported attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_MultipleWriteSupported, String.Empty);
            hash[Models.s_FieldIsMultipleWriteSupported] = Help.GetBool(attr, Attributes.s_DefaultMultipleWriteSupported);

            // Get IOXS_Required attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_IOXSRequired, String.Empty);
            hash[Models.s_FieldIsIoxsRequired] = Help.GetBool(attr, Attributes.s_DefaultIOXSRequired);

            // Get RequiredSchemaVersion info. Optional.
            // NOTE: The CompatibilityVersion is directly generated from the RequiredSchemaVersion.
            string rsv = nav.GetAttribute(Attributes.s_RequiredSchemaVersion, String.Empty);
            //hash[Models.FieldRequiredSchemaVersion] = hash[Models.FieldCompatibilityVersion];
            if (string.IsNullOrEmpty(rsv))
                rsv = Constants.s_Version10;
            hash[Models.s_FieldRequiredSchemaVersion] = rsv;

            // Get DeviceSpecificTool info. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_DeviceSpecificTool, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                // Get Name attribute. Required.
                GetFieldNameForDeviceAccessPoint(hash, nodes);
           
                // Get Description. Optional.
                hash[Models.s_FieldDeviceSpecificToolDescription] = GetText(nodes.Current);

            }
            else
            {
                // Set DeviceSpecificTool default values.
                hash[Models.s_FieldDeviceSpecificToolName] = String.Empty;
                hash[Models.s_FieldDeviceSpecificToolDescription] = String.Empty;
            }


            // Navigate to InterfaceSubmoduleItem and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_InterfaceSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            if (nodes.MoveNext())
            {
                // Create list object at first time.
                list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectInterfaceSubmodule, nodes.Current);
                if (null == obj)
                    throw new PreparationException("Couldn't create InterfaceSubmodule!");

                list.Add(obj);	// Add it to the list.
            }

            // Navigate to PortSubmoduleItems and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_PortSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Create each found data object.
            list = CreateObjectPortSubmoduleForDeviceAccessPoint(nodes, list);
          

            // Set hash variable.
            hash[Models.s_FieldSystemDefinedSubmodules] = list;

            // Navigate to SubslotList/SubslotItem and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_SubslotItem, Namespaces.s_GsdmlDeviceProfile, false);
            list = null;

            // Create each found data object.
            list = CreateObjectSubslotForDeviceAccessPoint(nodes, list);
         

            // Set hash variable.
            hash[Models.s_FieldSubslots] = list;

            // Select virtual submodules.
            nodes = nav.SelectDescendants(Elements.s_VirtualSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Add Submodule(s) to hash.
            list = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string id = nodes.Current.GetAttribute(Attributes.ID, String.Empty);

                if (CStore.VirtualSubmodules.ContainsKey(id))
                {
                    list.Add(CStore.VirtualSubmodules[id]);
                }
            }
            hash[Models.s_FieldVirtualSubmodules] = list;
        }

        private ArrayList CreateObjectSubslotForDeviceAccessPoint(XPathNodeIterator nodes, ArrayList list)
        {
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectSubslot, nodes.Current);

                if (null == obj)
                    throw new PreparationException("Couldn't create Subslot!");

                list.Add(obj);  // Add it to the list.
            }
            return list;
        }
        private ArrayList CreateObjectPortSubmoduleForDeviceAccessPoint(XPathNodeIterator nodes, ArrayList list)
        {
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectPortSubmodule, nodes.Current);
                if (null == obj)
                    throw new PreparationException("Couldn't create PortSubmodule!");

                list.Add(obj);  // Add it to the list.
            }
            return list;
        }

        private static void GetFieldNameForDeviceAccessPoint(IDictionary hash, XPathNodeIterator nodes)
        {
            if (nodes.Current == null)
            {
                return;
            }

            string attr = nodes.Current.GetAttribute(Attributes.s_Name, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldName] = attr;
            }
            else
                throw new PreparationException("Attribute 'Name' isn't available from the 'DeviceSpecificTool' element!");
        }

        protected override void PrepareApplicationRelations(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for ApplicationRelations object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldArBlockVersion, null);
            hash.Add(Models.s_FieldIocrBlockVersion, null);
            hash.Add(Models.s_FieldAlarmCrBlockVersion, null);
            hash.Add(Models.s_FieldSubmoduleDataBlockVersion, null);
            hash.Add(Models.s_FieldTimingProperties, null);

            hash.Add(Models.s_FieldRtClass3TimingProperties, null);
            hash.Add(Models.s_FieldNumberOfAdditionalInputCr, null);
            hash.Add(Models.s_FieldNumberOfAdditionalOutputCr, null);
            hash.Add(Models.s_FieldNumberOfAdditionalMulticastProviderCr, null);
            hash.Add(Models.s_FieldNumberOfMulticastConsumerCr, null);

            // If navigator is null, create object with only default values.
            if (nav == null)
            {
                hash[Models.s_FieldArBlockVersion] = Attributes.s_DefaultArBlockVersion;
                hash[Models.s_FieldIocrBlockVersion] = Attributes.s_DefaultIocrBlockVersion;
                hash[Models.s_FieldAlarmCrBlockVersion] = Attributes.s_DefaultAlarmCrBlockVersion;
                hash[Models.s_FieldSubmoduleDataBlockVersion] = Attributes.s_DefaultSubmoduleDataBlockVersion;
                hash[Models.s_FieldTimingProperties] = CreateGsdObject(Models.s_ObjectTimingProperties, null);

                hash[Models.s_FieldRtClass3TimingProperties] = CreateGsdObject(Models.s_ObjectRtClass3TimingProperties, null);
                hash[Models.s_FieldNumberOfAdditionalInputCr] = Attributes.s_DefaultNumberOfAdditionalInputCr;
                hash[Models.s_FieldNumberOfAdditionalOutputCr] = Attributes.s_DefaultNumberOfAdditionalOutputCr;
                hash[Models.s_FieldNumberOfAdditionalMulticastProviderCr] = Attributes.s_DefaultNumberOfAdditionalMulticastProviderCr;
                hash[Models.s_FieldNumberOfMulticastConsumerCr] = Attributes.s_DefaultNumberOfMulticastConsumerCr;
                return;
            }

            // Get AR_BlockVersion attribute. Optional, Required.
            GetFieldARBlockVersionForApplicationRelations(nav, hash);




            // Get IOCR_BlockVersion attribute. Optional, Required.
            GetFieldIOCRBlockVersionForApplicationRelations(nav, hash);


            // Get AlarmCR_BlockVersion attribute. Optional, Required.
            GetFieldAlarmCRBlockVersionForApplicationRelations(nav, hash);


            // Get SubmoduleDataBlockVersion attribute. Optional, Required.
            GetFieldSubmoduleDataBlockVersionForApplicationRelations(nav, hash);
          

            // Navigate to TimingProperties. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_TimingProperties, Namespaces.s_GsdmlDeviceProfile);
            // Create element.
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectTimingProperties, nodes.Current);

                if (null == obj)
                    throw new PreparationException("Couldn't create TimingProperties!");

                // Set hash variable.
                hash[Models.s_FieldTimingProperties] = obj;
            }
            // --------------------------------------------

            // Navigate to RT_Class3TimingProperties. Optional.
            nodes = nav.SelectChildren(Elements.s_RTClass3TimingProperties, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectRtClass3TimingProperties, nodes.Current);

                if (null == obj)
                    throw new PreparationException("Couldn't create RTClass3 TimingProperties!");

                // Set hash variable.
                hash[Models.s_FieldRtClass3TimingProperties] = obj;
            }

            // Get NumberOfAdditionalInputCR attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_NumberOfAdditionalInputCr, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldNumberOfAdditionalInputCr] = Attributes.s_DefaultNumberOfAdditionalInputCr;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfAdditionalInputCr] = value;
            }

            // Get NumberOfAdditionalOutputCR attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_NumberOfAdditionalOutputCr, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldNumberOfAdditionalOutputCr] = Attributes.s_DefaultNumberOfAdditionalOutputCr;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfAdditionalOutputCr] = value;
            }

            // Get NumberOfAdditionalMulticastProviderCR attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_NumberOfAdditionalMulticastProviderCr, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldNumberOfAdditionalMulticastProviderCr] = Attributes.s_DefaultNumberOfAdditionalMulticastProviderCr;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfAdditionalMulticastProviderCr] = value;
            }

            // Get NumberOfMulticastConsumerCR attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_NumberOfMulticastConsumerCr, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldNumberOfMulticastConsumerCr] = Attributes.s_DefaultNumberOfMulticastConsumerCr;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfMulticastConsumerCr] = value;
            }
        }
        private static void GetFieldSubmoduleDataBlockVersionForApplicationRelations(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_SubmoduleDataBlockVersion, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSubmoduleDataBlockVersion] = Attributes.s_DefaultSubmoduleDataBlockVersion;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldSubmoduleDataBlockVersion] = value;
            }
        }
        private static void GetFieldAlarmCRBlockVersionForApplicationRelations(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_AlarmCrBlockVersion, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldAlarmCrBlockVersion] = Attributes.s_DefaultAlarmCrBlockVersion;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldAlarmCrBlockVersion] = value;
            }
        }
        private static void GetFieldIOCRBlockVersionForApplicationRelations(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_IocrBlockVersion, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIocrBlockVersion] = Attributes.s_DefaultIocrBlockVersion;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldIocrBlockVersion] = value;
            }
        }
        private static void GetFieldARBlockVersionForApplicationRelations(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_ArBlockVersion, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldArBlockVersion] = Attributes.s_DefaultArBlockVersion;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldArBlockVersion] = value;
            }
        }

        

        protected override void PrepareModule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Module object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldRequiredSchemaVersion, null);
            hash.Add(Models.s_FieldSystemDefinedSubmodules, null);
            hash.Add(Models.s_FieldSubslots, null);

            // Call base class method first.
            base.PrepareModule(nav, ref hash);

            // --------------------------------------------

            // Get RequiredSchemaVersion info. Optional.
            // NOTE: The CompatibilityVersion is directly generated from the RequiredSchemaVersion.
            string rsv = nav.GetAttribute(Attributes.s_RequiredSchemaVersion, String.Empty);
            //hash[Models.FieldRequiredSchemaVersion] = hash[Models.FieldCompatibilityVersion];
            if (string.IsNullOrEmpty(rsv))
                rsv = Constants.s_Version10;
            hash[Models.s_FieldRequiredSchemaVersion] = rsv;

            // --------------------------------------------

            // Navigate to PortSubmoduleItems and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_PortSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectPortSubmodule, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectPortSubmodule + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }
            hash[Models.s_FieldSystemDefinedSubmodules] = list;

            // --------------------------------------------

            // Navigate to SubslotList/SubslotItem and create it. Optional.
            list = null;
            nodes = nav.SelectDescendants(Elements.s_SubslotItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectSubslot, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSubslot + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }
            hash[Models.s_FieldSubslots] = list;

            // --------------------------------------------

            // Support of many fixed VirtualSubmodules yet!!!
            // Select virtual submodules.
            nodes = nav.SelectDescendants(Elements.s_VirtualSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Add Submodule(s) to hash.
            list = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string id = nodes.Current.GetAttribute(Attributes.ID, String.Empty);

                if (CStore.VirtualSubmodules.ContainsKey(id))
                {
                    list.Add(CStore.VirtualSubmodules[id]);
                }
            }
            hash[Models.s_FieldVirtualSubmodules] = list;
        }

        protected override void PrepareDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for DataItem object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldBitDataItems, null);

            // Call base class method first.
            base.PrepareDataItem(nav, ref hash);

            // --------------------------------------------

            // Navigate to BitDataItems and create it. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_BitDataItem, Namespaces.s_GsdmlDeviceProfile);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectBitDataItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectBitDataItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldBitDataItems] = list;
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for VirtualSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApi, null);
            hash.Add(Models.s_FieldIsIsochroneModeSupported, null);
            hash.Add(Models.s_FieldIsochroneMode, null);
            hash.Add(Models.s_FieldPlugData, null);
            hash.Add(Models.s_FieldIsProfIsafeSupported, null);
            hash.Add(Models.s_FieldFParameterRecordData, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            // --------------------------------------------

            // Get API attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_Api, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldApi] = value;
            }
            else	// Set default.
                hash[Models.s_FieldApi] = Attributes.s_DefaultApi;

            // Get IsochroneMode info. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_IsochroneMode, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsIsochroneModeSupported] = true;

                // Create object.
                obj = CreateGsdObject(Models.s_ObjectIsochroneMode, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectIsochroneMode + "' couldn't be created!");

                hash[Models.s_FieldIsochroneMode] = obj;
            }
            else
            {
                // Set IsochroneMode default values.
                hash[Models.s_FieldIsIsochroneModeSupported] = Attributes.s_DefaultIsochroneModeSupported;
                hash[Models.s_FieldIsochroneMode] = null;
            }

            // Navigate to FParameterRecordData and create it. Optional.
            obj = null;
            nodes = nav.SelectDescendants(Elements.s_FParameterRecordDataItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Create found record data.
            if (nodes.MoveNext())
            {
                // Create data record itself.
                obj = CreateGsdObject(Models.s_ObjectFParameterRecordData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectFParameterRecordData + "' couldn't be created!");
            }

            // Set hash variable.
            hash[Models.s_FieldFParameterRecordData] = obj;

            // Create PlugData.
            obj = CreateGsdObject(Models.s_ObjectSubmodulePlugData, nav);

            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectSubmodulePlugData + "' couldn't be created!");

            hash[Models.s_FieldPlugData] = obj;

            // Get PROFIsafeSupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ProfIsafeSupported, String.Empty);
            hash[Models.s_FieldIsProfIsafeSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafeSupported);
        }

        protected override void PrepareTimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSendClock, null);
            hash.Add(Models.s_FieldReductionRatio, null);

            // If navigator is null, create object with only default values.
            if (nav == null)
            {
                string v = Attributes.DefaultSendClock.GetValue(0).ToString();
                hash[Models.s_FieldSendClock] = ValueListHelper.SeparateUnsignedValueList(v, Attributes.DefaultSendClock);
                v = Attributes.DefaultRTClass3ReductionRatio.GetValue(0).ToString();
                hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(v, Attributes.DefaultReductionRatio);
                return;
            }

            // Get SendClock attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_SendClock, String.Empty);
            if (attr.Length == 0)
            {
                attr = Attributes.DefaultSendClock.GetValue(0).ToString();
                hash[Models.s_FieldSendClock] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultSendClock);
            }
            else
            {
                List<uint> sendclocks = ValueListHelper.SeparateUnsignedValueList(attr);

                // PNIO-TT Beschluss vom 13.10.2010: Bei Nicht-IDevice-GSDs
                // wird der Sendlock 32 erg'nzt, falls er fehlt
                if (!Filename.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture).Contains("#siemens-preconf_"))
                {
                    if (!sendclocks.Contains(32))
                    {
                        sendclocks.Add(32);
                        sendclocks.Sort();
                    }
                }
                hash[Models.s_FieldSendClock] = sendclocks;
            }

            PrepareTimingPropertiesReductionRatios(nav, ref hash);
        }

        protected virtual void PrepareTimingPropertiesReductionRatios(XPathNavigator nav, ref Hashtable hash)
        {
            // Get ReductionRatio attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_ReductionRatio, String.Empty);
            if (attr.Length == 0)
            {
                attr = Attributes.DefaultReductionRatio.GetValue(0)?.ToString();
                hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultReductionRatio);
            }
            else
            {
                hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr);
            }
        }

        protected override void PrepareIOData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for IOData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFioStructureDescCrc, null);

            // Call base class method first.
            base.PrepareIOData(nav, ref hash);

            // --------------------------------------------

            // Get F_IO_StructureDescCRC attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FIOStructureDescCrc, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldFioStructureDescCrc] = Attributes.s_DefaultFioStructureDescCrc;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldFioStructureDescCrc] = value;
            }
        }

        protected override void PrepareChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldExtendedChannelDiagnostics, null);

            // Call base class method first.
            base.PrepareChannelDiagnostic(nav, ref hash);

            // --------------------------------------------

            // Navigate to ExtChannelDiagItem elements and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ExtChannelDiagItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectExtendedChannelDiagnostic, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtendedChannelDiagnostic + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtendedChannelDiagnostics] = list;
        }

        protected virtual void PrepareBitDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for BitDataItem object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldBitOffset, null);

            // Get Name. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get BitOffset attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_BitOffset, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldBitOffset] = value;
        }

        protected virtual void PrepareFParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for FParameterRecordData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIndex, null);
            hash.Add(Models.s_FieldLength, null);
            hash.Add(Models.s_FieldName, null);		// always null!
            hash.Add(Models.s_FieldNameTextId, null);	// always null!
            hash.Add(Models.s_FieldConsts, null);

            hash.Add(Models.s_FieldTransferSequence, null);
            hash.Add(Models.s_FieldRefs, null);
            hash.Add(Models.s_FieldFParamDescCrc, null);

            // Get F_ParamDescCRC attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_FParamDescCrc, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldFParamDescCrc] = value;

            // Get Index attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Index, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldIndex] = value;

            // Get TransferSequence attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTransferSequence] = Attributes.s_DefaultTransferSequence;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldTransferSequence] = value;
            }

            // Set Length property.
            hash[Models.s_FieldLength] = Attributes.s_FixedFParameterRecordDataLength;

            object obj = null;
            Hashtable h = null;

            // Set Consts property.
            h = new Hashtable();
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldValues, null);

            ArrayList consts = new ArrayList();
            obj = this.PrepareFParameterRecordDataCreateConst();
            consts.Add(obj);
            hash[Models.s_FieldConsts] = consts;

            // Set Refs property.
            h = new Hashtable();
            h.Add(Models.s_FieldDataType, null);
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldBitOffset, null);
            h.Add(Models.s_FieldBitLength, null);
            h.Add(Models.s_FieldIsChangeable, null);
            h.Add(Models.s_FieldIsVisible, null);
            h.Add(Models.s_FieldName, null);
            h.Add(Models.s_FieldNameTextId, null);
            h.Add(Models.s_FieldHelp, null);
            h.Add(Models.s_FieldValueGsdId, null);
            h.Add(Models.s_FieldDefaultValue, null);
            h.Add(Models.s_FieldValues, null);
            h.Add(Models.s_FieldValueType, null);

            ArrayList refs = new ArrayList();
            obj = PrepareFParameterRecordDataCreateRefFCheckIPar(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSil(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcLength(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFBlockID(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFParVersion(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSourceAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFDestAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFWdTime(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFParCrc(nav, ref h);
            refs.Add(obj);
            hash[Models.s_FieldRefs] = refs;
        }

        protected virtual void PrepareSubslot(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Subslot object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldSubslotNumber, null);

            // Get TextId. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get SubslotNumber attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_SubslotNumber, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldSubslotNumber] = value;
        }

        protected virtual void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for InterfaceSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubslotNumber, null);
            hash.Add(Models.s_FieldIdentNumber, null);

            hash.Add(Models.s_FieldCompatibilityVersion, null);
            hash.Add(Models.s_FieldIsCompatible, null);

            hash.Add(Models.s_FieldSupportedRtClass, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldIsIsochroneModeSupported, null);
            hash.Add(Models.s_FieldMaxBridgeDelay, null);
            hash.Add(Models.s_FieldMaxNumberIrFrameData, null);
            hash.Add(Models.s_FieldIsSynchronisationModeSupported, null);
            hash.Add(Models.s_FieldSupportedSynchronisationRole, null);
            hash.Add(Models.s_FieldMaxLocalJitter, null);
            hash.Add(Models.s_FieldTPllMax, null);
            hash.Add(Models.s_FieldApplicationRelations, null);
            hash.Add(Models.s_FieldParameterRecordData, null);

            // Get compatibility info.
            string compatibilityversion = String.Empty;
            bool iscompatible = false;
            bool succeeded = Help.TryGetCompatibilityInfo(nav, Constants.s_Version20, SupportedGsdmlVersion,
                out compatibilityversion, out iscompatible);
            if (!succeeded)
                throw new PreparationException("Couldn't get compatibility info for '" + nav.LocalName + "' with ID '" + hash["GsdID"] + "'!");
            hash[Models.s_FieldCompatibilityVersion] = compatibilityversion;
            hash[Models.s_FieldIsCompatible] = iscompatible;

            // --------------------------------------------------------------

            // Get Name. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get SubslotNumber attribute. Fixed.
            
            GetFieldSubslotNumberForInterfaceSubmodule(nav, hash);


            // Attribute SubmoduleIdentNumber. Required.
            GetFieldIdentNumberForInterfaceSubmodule(nav, hash);


            // Get SupportedRT_Class attribute. Optional.
            GetFieldSupportedRTClassForInterfaceSubmodule(nav, hash);


            // Get IsochroneModeSupported attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_IsochroneModeSupported, String.Empty);
            hash[Models.s_FieldIsIsochroneModeSupported] = Help.GetBool(attr, Attributes.s_DefaultIsochroneModeSupported);

            // --------------------------------------------------------------

            // Get RTClass3Properties info. Optional.
            GetRT_Class3PropertiesForInterfaceSubmodule(nav, hash);

            // --------------------------------------------------------------

            // Get SynchronisationMode info. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_SynchronisationMode, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                // Set SynchronisationModeSupported.
                hash[Models.s_FieldIsSynchronisationModeSupported] = true;

                // Get MaxLocalJitter attribute. Required.
                attr = nodes.Current.GetAttribute(Attributes.s_MaxLocalJitter, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldMaxLocalJitter] = value;
                }

                // Get SupportedRole attribute. Optional.
                attr = nodes.Current.GetAttribute(Attributes.s_SupportedRole, String.Empty);
                if (Enums.IsSynchronisationRoleEnumValueConvertable(attr))
                {
                    hash[Models.s_FieldSupportedSynchronisationRole] = Enums.ConvertSynchronisationRoleEnum(attr);
                }
                else
                    hash[Models.s_FieldIsIsochroneModeSupported] = Attributes.s_DefaultSupportedSynchronisationRole;

                // Get T_PLL_MAX attribute. Optional.
                attr = nodes.Current.GetAttribute(Attributes.s_PllMax, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldTPllMax] = value;
                }
                else
                    hash[Models.s_FieldTPllMax] = Attributes.s_DefaultTPllMax;
            }
            else
            {
                // Set SynchronisationMode default values.
                hash[Models.s_FieldIsSynchronisationModeSupported] = Attributes.s_DefaultSynchronisationModeSupported;
                hash[Models.s_FieldSupportedSynchronisationRole] = SynchronisationRoles.GSDSyncRoleNone;
                hash[Models.s_FieldMaxLocalJitter] = uint.MinValue;
                hash[Models.s_FieldTPllMax] = uint.MinValue;
            }

            // --------------------------------------------------

            // Navigate to ApplicationRelations. Optional.
            nodes = nav.SelectChildren(Elements.s_ApplicationRelations, Namespaces.s_GsdmlDeviceProfile);
            // Create element.
            object obj = null;
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectApplicationRelations, nodes.Current);

            }
            else
            {
                obj = CreateGsdObject(Models.s_ObjectApplicationRelations, null);
            }

            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectApplicationRelations + "' couldn't be created!");

            // Set hash variable.
            hash[Models.s_FieldApplicationRelations] = obj;
        
            // --------------------------------------------------

            // Navigate to RecordDataList/ParameterRecordDataItem and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ParameterRecordDataItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectParameterRecordData, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectParameterRecordData + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldParameterRecordData] = list;
        }
        private static void GetRT_Class3PropertiesForInterfaceSubmodule(XPathNavigator nav, IDictionary hash)
        {
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_RTClass3Properties, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                // Get MaxBridgeDelay attribute. Required.
                string attr = nodes.Current.GetAttribute(Attributes.s_MaxBridgeDelay, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldMaxBridgeDelay] = value;
                }

                // Get MaxNumberIR_FrameData attribute. Required.
                attr = nodes.Current.GetAttribute(Attributes.s_MaxNumberIRFrameData, String.Empty);
                if (!String.IsNullOrEmpty(attr))
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldMaxNumberIrFrameData] = value;
                }
            }
            else
            {
                // Set RTClass3Properties default values.
                hash[Models.s_FieldMaxBridgeDelay] = uint.MinValue;
                hash[Models.s_FieldMaxNumberIrFrameData] = uint.MinValue;
            }
        }
        private static void GetFieldSupportedRTClassForInterfaceSubmodule(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_SupportedRTClass, String.Empty);
            if (Enums.IsRTClassEnumValueConvertable(attr))
            {
                hash[Models.s_FieldSupportedRtClass] = Enums.ConvertRTClassEnum(attr);
            }
            else    // Set default.
                hash[Models.s_FieldSupportedRtClass] = Attributes.s_DefaultSupportedRTClass;
        }
        private static void GetFieldIdentNumberForInterfaceSubmodule(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_SubmoduleIdentNumber, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIdentNumber] = UInt32.Parse(attr.Substring(2), System.Globalization.NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
            }
        }

        private void GetFieldSubslotNumberForInterfaceSubmodule(XPathNavigator nav, IDictionary hash)
        {
            string attr = nav.GetAttribute(Attributes.s_SubslotNumber, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldSubslotNumber] = value;
            }
            else    // Set fixed.
                hash[Models.s_FieldSubslotNumber] = Attributes.s_FixedSubslotNumberOfInterfaceSubmodule;

        }


        protected virtual void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for PortSubmodule object.

            PrepareModuleObject(nav, ref hash);

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubslotNumber, null);

            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldMauType, null);
            hash.Add(Models.s_FieldMaxPortTxDelay, null);
            hash.Add(Models.s_FieldMaxPortRxDelay, null);
            hash.Add(Models.s_FieldParameterRecordData, null);

            // Prepare data of base class.

            // Get Name. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get SubslotNumber attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_SubslotNumber, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldSubslotNumber] = value;
            }

            // Attribute SubmoduleIdentNumber. Required.
            attr = nav.GetAttribute(Attributes.s_SubmoduleIdentNumber, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIdentNumber] = UInt32.Parse(attr.Substring(2), System.Globalization.NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
            }

            // Get MAUType attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MauType, String.Empty);
            if (Enums.IsMAUTypeEnumValueConvertable(attr))
            {
                hash[Models.s_FieldMauType] = Enums.ConvertMauTypeEnum(attr);
            }

            // Get MaxPortTxDelay attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MaxPortTxDelay, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldMaxPortTxDelay] = value;
            }
            else
                hash[Models.s_FieldMaxPortTxDelay] = Attributes.s_DefaultMaxPortTxDelay;

            // Get MaxPortRxDelay attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MaxPortRxDelay, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldMaxPortRxDelay] = value;
            }
            else
                hash[Models.s_FieldMaxPortRxDelay] = Attributes.s_DefaultMaxPortRxDelay;

            // --------------------------------------------------------------

            // Navigate to RecordDataList/ParameterRecordDataItem and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ParameterRecordDataItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectParameterRecordData, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectParameterRecordData + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldParameterRecordData] = list;
        }

        protected virtual void PrepareIsochroneMode(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for IsochroneMode object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldTDcBase, null);
            hash.Add(Models.s_FieldTDcMin, null);
            hash.Add(Models.s_FieldTDcMax, null);
            hash.Add(Models.s_FieldTIOBase, null);
            hash.Add(Models.s_FieldTIOInputMin, null);
            hash.Add(Models.s_FieldTIOOutputMin, null);
            hash.Add(Models.s_FieldIsIsochroneModeRequired, null);

            // Get T_DC_Base attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_DcBase, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTDcBase] = value;

            // Get T_DC_Min attribute. Required.
            attr = nav.GetAttribute(Attributes.s_DcMin, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTDcMin] = value;

            // Get T_DC_Max attribute. Required.
            attr = nav.GetAttribute(Attributes.s_DcMax, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTDcMax] = value;

            // Get T_IO_Base attribute. Required.
            attr = nav.GetAttribute(Attributes.s_IOBase, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTIOBase] = value;

            // Get T_IO_InputMin attribute. Required.
            attr = nav.GetAttribute(Attributes.s_IOInputMin, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTIOInputMin] = value;

            // Get T_IO_OutputMin attribute. Required.
            attr = nav.GetAttribute(Attributes.s_IOOutputMin, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldTIOOutputMin] = value;

            // Get IsochroneModeRequired attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_IsochroneModeRequired, String.Empty);
            hash[Models.s_FieldIsIsochroneModeRequired] = Help.GetBool(attr, Attributes.s_DefaultIsochroneModeRequired);
        }

        protected virtual void PrepareRTClass3TimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for RTClass3 TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSendClock, null);
            hash.Add(Models.s_FieldReductionRatio, null);

            // If navigator is null, create object with only default values.
            if (nav == null)
            {
                string v = Attributes.DefaultRTClass3SendClock.GetValue(0)?.ToString();
                hash[Models.s_FieldSendClock] = ValueListHelper.SeparateUnsignedValueList(v, Attributes.DefaultRTClass3SendClock);
                v = Attributes.DefaultRTClass3ReductionRatio.GetValue(0)?.ToString();
                hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(v, Attributes.DefaultRTClass3ReductionRatio);
                return;
            }

            // Get SendClock attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_SendClock, String.Empty);
            if (attr.Length == 0)
            {
                attr = Attributes.DefaultRTClass3SendClock.GetValue(0)?.ToString();
                hash[Models.s_FieldSendClock] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultRTClass3SendClock);
            }
            else
            {
                List<uint> sendclocks = ValueListHelper.SeparateUnsignedValueList(attr);

                // PNIO-TT Beschluss vom 13.10.2010: Bei Nicht-IDevice-GSDs
                // wird der Sendlock 32 erg'nzt, falls er fehlt
                if (!Filename.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture).Contains("#siemens-preconf_"))
                {
                    if (!sendclocks.Contains(32))
                    {
                        sendclocks.Add(32);
                        sendclocks.Sort();
                    }
                }
                hash[Models.s_FieldSendClock] = sendclocks;

            }

            PrepareTimingPropertiesReductionRatios(nav, ref hash);
        }

        protected virtual void PrepareSubmodulePlugData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Submodule ModulePlugData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFixedInSlots, null);

            // Get FixedInSlots attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FixedInSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldFixedInSlots] = ValueListHelper.SeparateUnsignedValueList(attr);
        }

        protected virtual void PrepareExtendedChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldErrorType, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);

            // Get error type attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_ErrorType, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldErrorType] = value;

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldName] = GetText(nodes.Current);
                hash[Models.s_FieldNameTextId] = nodes.Current?.GetAttribute(Attributes.s_TextId, String.Empty);
            }

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = GetText(nodes.Current);
                hash[Models.s_FieldHelpTextId] = nodes.Current?.GetAttribute(Attributes.s_TextId, String.Empty);
            }
        }

        #region FParameterRecordData - Const/Ref creator methods

        /// <summary>
        /// The F-parameter record data is a fixed 10 byte block with configuration data. It is
        /// organized as follows:
        /// 0	F_Prm_Flag1		(Unsigned8)		0x08 (8)
        ///			Bit 0:	xxx	(F_Check_SeqNr)		0
        ///			Bit 1:	F_Check_iPar			0 "No check"
        ///			Bit 2:	F_SIL					0 "SIL3"
        ///			Bit 3:							1
        ///			Bit 4:	F_CRC_Length			0 "3 Byte CRC"
        ///			Bit 5:							0
        ///			Bit 6:							0 
        ///			Bit 7:	xxx						0 "reserved"
        /// 1	F_Prm_Flag2		(Unsigned8)		0x40 (64)
        ///			Bit 0:	xxx						0 "reserved"
        ///			Bit 1:	xxx						0 "reserved"
        ///			Bit 2:	xxx						0 "reserved"
        ///			Bit 3:	F_Block_ID				0 "F-Host/F-Device relationship"
        ///			Bit 4:							0
        ///			Bit 5:							0
        ///			Bit 6:	F_Par_Version			1 "PROFIsafe V2.0"
        ///			Bit 7:							0
        /// 2	F_Source_Add	(Unsigned16)	0x00 (0)
        /// 3									0x01 (1)
        /// 4	F_Dest_Add		(Unsigned16)	0x00 (0)
        /// 5									0x01 (1)
        /// 6	F_WD_Time		(Unsigned16)	0x00 (0)
        /// 7									0x96 (150)
        /// 8	F_Par_CRC		(Unsigned16)	0xBB (187)
        /// 9	(48021 : Heiner)				0x95 (149)
        /// </summary>
        /// <returns>Const object with default binary settings with byte length 10.</returns>
        protected virtual object PrepareFParameterRecordDataCreateConst()
        {
            Hashtable hash = new Hashtable();
            hash.Add(Models.s_FieldByteOffset, null);
            hash.Add(Models.s_FieldValues, null);

            // Set ByteOffset.
            hash[Models.s_FieldByteOffset] = Attributes.s_DefaultByteOffset;

            // Set Values.
            uint[] v = { 8, 64, 0, 1, 0, 1, 0, 150, 187, 149 };
            hash[Models.s_FieldValues] = new ArrayList(v);

            // Create object.
            C.GsdObject obj = new C.ConstData();

            // Fill object with data.
            bool succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectConstData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// This parameter defines whether or not the CRC3 of individual device parameters shall
        /// be included in the cyclic CRC2 key. The start value for the CRC1 calculation is "0"
        /// in case of "No check" and CRC3 in case of "Check". The F_Check_iPar is part of the
        /// parameter byte "F_Prm_Flag1":
        /// 
        /// Data type:		Bit
        /// Byte offset:	0
        /// Bit offset:		1
        /// 
        ///	Parameter values:
        ///		0 = "NoCheck" (default)
        ///		1 = "Check"
        /// </summary>
        /// <returns>Ref object for the F_Check_iPar parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFCheckIPar(XPathNavigator nav, ref Hashtable hash)
        {
            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FCheckIPar;
            hash[Models.s_FieldNameTextId] = Elements.s_FCheckIPar;
            hash[Models.s_FieldDataType] = GSDI.DataTypes.GSDBit;
            hash[Models.s_FieldByteOffset] = (uint)0;
            hash[Models.s_FieldBitOffset] = (uint)1;
            hash[Models.s_FieldBitLength] = (uint)1;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = GSDI.ValueTypes.GSDConcrete;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Constants.s_Value, null);
            valuehash.Add(Constants.s_Text, null);
            valuehash.Add(Constants.s_TextId, null);

            ArrayList list = new ArrayList();
            // NoCheck value ------------------------------------
            C.GsdObject obj = new C.ValueItem();
            valuehash[Constants.s_Value] = Enums.s_NoCheckValue;
            valuehash[Constants.s_Text] = Enums.s_NoCheck;
            valuehash[Constants.s_TextId] = Enums.s_NoCheck;
            bool succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);
            // Check value --------------------------------------
            obj = new C.ValueItem();
            valuehash[Constants.s_Value] = Enums.s_CheckValue;
            valuehash[Constants.s_Text] = Enums.s_Check;
            valuehash[Constants.s_TextId] = Enums.s_Check;
            succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            bool isChangeable = false;				// Is initially set with default value!
            bool isVisible = false;					// Is initially set with default value!
            byte defaultValue = Enums.s_NoCheckValue;	// Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FCheckIPar, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_Changeable, String.Empty);
                    isChangeable = Help.GetBool(attr, false);

                    attr = nodes.Current.GetAttribute(Attributes.s_Visible, String.Empty);
                    isVisible = Help.GetBool(attr, false);

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Equals(Enums.s_Check, StringComparison.Ordinal))
                        defaultValue = 1;
                }
            }

            hash[Models.s_FieldIsChangeable] = isChangeable;
            hash[Models.s_FieldIsVisible] = isVisible;
            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// In the safety-relevant case, risk-related safety circuits with different SIL (Safety Integrity Level)
        /// stages are distinguished. The F devices are able to use this locally available information for 
        /// checking the agreement between the SIL stage and the partner. If the configured SIL stage is higher 
        /// than the one in the connected F unit, the device failure status bit is set and a safe state reaction
        /// is triggered. The F_SIL is part of the parameter byte "F_Prm_Flag1":
        /// 
        /// Data type:		BitArea
        /// Byte offset:	0
        /// Bit offset:		2
        /// Bit length:		2
        /// 
        ///	Parameter values (default range: SIL1, SIL2, SIL3):
        ///		0 0 = "SIL1"
        ///		0 1 = "SIL2"
        ///		1 0 = "SIL3" (default)
        ///		1 1 = "NoSIL"
        /// </summary>
        /// <returns>Ref object for the F_SIL parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFSil(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FSil;
            hash[Models.s_FieldNameTextId] = Elements.s_FSil;
            hash[Models.s_FieldDataType] = DataTypes.GSDBitArea;
            hash[Models.s_FieldByteOffset] = (uint)0;
            hash[Models.s_FieldBitOffset] = (uint)2;
            hash[Models.s_FieldBitLength] = (uint)2;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDConcrete;

            bool isVisible = true;					// Is initially setted with default value!
            ushort defaultValue = Enums.s_Sil3Value;	// Is initially setted with default value!
            string allowedValues = String.Empty;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FSil, Namespaces.s_GsdmlDeviceProfile);

            bool isChangeable = GetDefaultValueForFParameterRecordData_Create_Ref_F_SIL(nodes, true, ref isVisible, ref defaultValue, ref allowedValues);
            
            hash[Models.s_FieldIsChangeable] = isChangeable;
            hash[Models.s_FieldIsVisible] = isVisible;
            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Constants.s_Value, null);
            valuehash.Add(Constants.s_Text, null);
            valuehash.Add(Constants.s_TextId, null);

            ArrayList list = new ArrayList();
            C.GsdObject obj = null;

            // SIL1 value ---------------------------------------
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_Sil1, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_Sil1Value;
                valuehash[Constants.s_Text] = Enums.s_Sil1;
                valuehash[Constants.s_TextId] = Enums.s_Sil1;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // SIL2 value ---------------------------------------
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_Sil2, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_Sil2Value;
                valuehash[Constants.s_Text] = Enums.s_Sil2;
                valuehash[Constants.s_TextId] = Enums.s_Sil2;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // SIL3 value ---------------------------------------
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_Sil3, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_Sil3Value;
                valuehash[Constants.s_Text] = Enums.s_Sil3;
                valuehash[Constants.s_TextId] = Enums.s_Sil3;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // NoSIL value --------------------------------------
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_NoSil, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_NoSilValue;
                valuehash[Constants.s_Text] = Enums.s_NoSil;
                valuehash[Constants.s_TextId] = Enums.s_NoSil;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        private static bool GetDefaultValueForFParameterRecordData_Create_Ref_F_SIL(
            XPathNodeIterator nodes,
            bool isChangeable,
            ref bool isVisible,
            ref ushort defaultValue,
            ref string allowedValues)
        {
            if (!nodes.MoveNext())
            {
                return isChangeable;
            }

            if (nodes.Current == null)
            {
                return isChangeable;
            }

            string attr = nodes.Current.GetAttribute(Attributes.s_Changeable, String.Empty);
            isChangeable = Help.GetBool(attr, isChangeable);

            attr = nodes.Current.GetAttribute(Attributes.s_Visible, String.Empty);
            isVisible = Help.GetBool(attr, isVisible);

            attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
            if (attr.Equals(Enums.s_Sil1, StringComparison.Ordinal))
            {
                defaultValue = Enums.s_Sil1Value;
            }
            else if (attr.Equals(Enums.s_Sil2, StringComparison.Ordinal))
            {
                defaultValue = Enums.s_Sil2Value;
            }
            else if (attr.Equals(Enums.s_NoSil, StringComparison.Ordinal))
            {
                defaultValue = Enums.s_NoSilValue;
            }

            attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
            allowedValues = attr;

            return isChangeable;
        }

        /// <summary>
        /// Depending on the length of the F process data (12 or 122 bytes) and the SIL stage, a CRC of 2, 
        /// or 4 bytes is required. This parameter transfers the expected length of the CRC2 key in the
        /// F message frame to the F component. The F_CRC_Length is part of the parameter byte "F_Prm_Flag1":
        /// 
        /// Data type:		BitArea
        /// Byte offset:	0
        /// Bit offset:		4
        /// Bit length:		2
        /// 
        ///	Parameter values (default range: 3-Byte-CRC):
        ///		0 0 = "3-Byte-CRC" (default)
        ///		0 1 = "2-Byte-CRC"
        ///		1 0 = "4-Byte-CRC"
        ///		1 1 = "reserved"
        /// </summary>
        /// <returns>Ref object for the F_CRC_Length parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFCrcLength(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FCrcLength;
            hash[Models.s_FieldNameTextId] = Elements.s_FCrcLength;
            hash[Models.s_FieldDataType] = DataTypes.GSDBitArea;
            hash[Models.s_FieldByteOffset] = (uint)0;
            hash[Models.s_FieldBitOffset] = (uint)4;
            hash[Models.s_FieldBitLength] = (uint)2;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDConcrete;

            bool isChangeable = false;					// Is initially setted with default value!
            bool isVisible = false;						// Is initially setted with default value!
            ushort defaultValue = Enums.s_ByteCrc3Value;	// Is initially setted with default value!
            string allowedValues = String.Empty;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FCrcLength, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                string attr = nodes.Current?.GetAttribute(Attributes.s_Changeable, String.Empty);
                isChangeable = Help.GetBool(attr, false);

                attr = nodes.Current?.GetAttribute(Attributes.s_Visible, String.Empty);
                isVisible = Help.GetBool(attr, false);

                attr = nodes.Current?.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                if (attr.Equals(Enums.s_ByteCrc2, StringComparison.Ordinal))
                {
                    defaultValue = Enums.s_ByteCrc2Value;
                }
                else if (attr.Equals(Enums.s_ByteCrc4, StringComparison.Ordinal))
                {
                    defaultValue = Enums.s_ByteCrc4Value;
                }

                attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                allowedValues = attr;
            }

            hash[Models.s_FieldIsChangeable] = isChangeable;
            hash[Models.s_FieldIsVisible] = isVisible;
            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Constants.s_Value, null);
            valuehash.Add(Constants.s_Text, null);
            valuehash.Add(Constants.s_TextId, null);

            ArrayList list = new ArrayList();
            C.GsdObject obj = null;

            // 3-Byte-CRC value ---------------------------------
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_ByteCrc3, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_ByteCrc3Value;
                valuehash[Constants.s_Text] = Enums.s_ByteCrc3;
                valuehash[Constants.s_TextId] = Enums.s_ByteCrc3;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // 2-Byte-CRC value ---------------------------------
            if (allowedValues.IndexOf(Enums.s_ByteCrc2, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_ByteCrc2Value;
                valuehash[Constants.s_Text] = Enums.s_ByteCrc2;
                valuehash[Constants.s_TextId] = Enums.s_ByteCrc2;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // 4-Byte-CRC value ---------------------------------
            if (allowedValues.IndexOf(Enums.s_ByteCrc4, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_ByteCrc4Value;
                valuehash[Constants.s_Text] = Enums.s_ByteCrc4;
                valuehash[Constants.s_TextId] = Enums.s_ByteCrc4;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// In oder to distinct parameters for future PROFINET safety modes a parameter type identification
        /// is established. The F_Block_ID is part of the parameter byte "F_Prm_Flag2":
        /// 
        /// Data type:		BitArea
        /// Byte offset:	1
        /// Bit offset:		3
        /// Bit length:		3
        /// 
        /// 
        ///	Parameter values (default range: 0, max. range: 0..7):
        ///		0 0 0 = Parameter set for F-Host/F-Device relationship (default)
        ///		0 0 1 = ""
        ///		0 1 0 = ""
        ///		0 1 1 = ""
        ///		1 0 0 = "reserved"
        ///		1 0 1 = "reserved"
        ///		1 1 0 = "reserved"
        ///		1 1 1 = "reserved"
        ///		
        ///	NOTE: Area with range 0 to 7!
        /// </summary>
        /// <returns>Ref object for the F_Block_ID parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFBlockID(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            string sAllowedValues = @"0..7";

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FBlockID;
            hash[Models.s_FieldNameTextId] = Elements.s_FBlockID;
            hash[Models.s_FieldDataType] = DataTypes.GSDBitArea;
            hash[Models.s_FieldByteOffset] = (uint)1;
            hash[Models.s_FieldBitOffset] = (uint)3;
            hash[Models.s_FieldBitLength] = (uint)3;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

            bool isChangeable = true;
            bool isVisible = true;


            ushort defaultValue = 0;	// Is initially setted with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FBlockID, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                if (attr.Length > 0)
                {
                    defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }

                attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                if (attr.Length != 0)
                    sAllowedValues = attr;

                attr = nodes.Current.GetAttribute(Attributes.s_Visible, String.Empty);
                if (attr.Length != 0)
                    isVisible = Help.GetBool(attr, true);

                attr = nodes.Current.GetAttribute(Attributes.s_Changeable, String.Empty);
                if (attr.Length != 0)
                    isChangeable = Help.GetBool(attr, true);
            }

            if (string.CompareOrdinal(SupportedGsdmlVersion, Constants.s_Version22) < 0)
            {
                hash[Models.s_FieldIsChangeable] = false;
                hash[Models.s_FieldIsVisible] = true;
            }
            else
            {
                hash[Models.s_FieldIsChangeable] = isChangeable;
                hash[Models.s_FieldIsVisible] = isVisible;
            }

            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            ArrayList list = new ArrayList();

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            // Split incoming string to pairs and numbers in a list.
            C.GsdObject obj = null;
            ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

            foreach (string s in splitlist)
            {
                // Get min and max value.
                string ssMin = String.Empty;
                string ssMax = String.Empty;
                int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                if (index != -1)
                {
                    // Area, separate area and add values (1..65534).
                    ssMin = s.Substring(0, index);	// 1
                    ssMax = s.Substring(index + 2);	// 65534
                }
                else
                {
                    // Value, create area with same min and max value.
                    ssMin = s;
                    ssMax = s;
                }

                // Create AreaItem(s).
                ushort usMin = System.Xml.XmlConvert.ToUInt16(ssMin);
                ushort usMax = System.Xml.XmlConvert.ToUInt16(ssMax);

                valuehash[Models.s_FieldMinValue] = usMin;
                valuehash[Models.s_FieldMaxValue] = usMax;

                obj = new C.AreaItem();
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);

            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// The purpose of this version counter is to identify new releases of an operational mode inside 
        /// the safety layer. The F Device shall respond with a device-specific diagnosis mesage in case the 
        /// requested version of the safety layer does not match the implemented version. 
        /// The F_Par_Version is part of the parameter byte "F_Prm_Flag2":
        /// 
        /// Data type:		BitArea
        /// Byte offset:	1
        /// Bit offset:		6
        /// Bit length:		2
        /// 
        ///	Parameter values (default range: 1, max. range: 1):
        ///		0 0 = Valid for PROFIsafe versions 1.00 up to 1.99
        ///		0 1 = Valid for PROFINET safety = PROFIsafe 2.0 (default)
        ///		1 0 = "reserved"
        ///		1 1 = "reserved"
        /// </summary>
        /// <returns>Ref object for the F_Block_ID parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFParVersion(XPathNavigator nav, ref Hashtable hash)
        {
            // NOTE: No entry from GSDML file for this F parameter!

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FParVersion;
            hash[Models.s_FieldNameTextId] = Elements.s_FParVersion;
            hash[Models.s_FieldDataType] = DataTypes.GSDBitArea;
            hash[Models.s_FieldByteOffset] = (uint)1;
            hash[Models.s_FieldBitOffset] = (uint)6;
            hash[Models.s_FieldBitLength] = (uint)2;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

            hash[Models.s_FieldIsChangeable] = false;
            hash[Models.s_FieldIsVisible] = true;
            hash[Models.s_FieldDefaultValue] = (ushort)1;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();
            C.GsdObject obj = null;

            obj = new C.AreaItem();
            valuehash[Models.s_FieldMinValue] = (ushort)1;
            valuehash[Models.s_FieldMaxValue] = (ushort)1;
            bool succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// The addresses of the F components of a safety control loop F input, F-CPU and F output shall be
        /// unambiguous. Locally, each F device has the configured source-destination relationship of the
        /// communication link with its partner. It is retentively stored in the F devices, 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	2
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 1..65534, max. range: 1..65534, default value: 1).
        /// </summary>
        /// <returns>Ref object for the F_Source_Add parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFSourceAdd(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FSourceAdd;
                hash[Models.s_FieldNameTextId] = Elements.s_FSourceAdd;
                hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
                hash[Models.s_FieldByteOffset] = (uint)2;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)16;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;

                // Build values.
                ArrayList list = new ArrayList();

                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                uint defaultValue = 1;
                string sAllowedValues = @"1..65534";

                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FSourceAdd, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                    if (attr.Length != 0)
                        sAllowedValues = attr;

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length != 0)
                        defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }

                hash[Models.s_FieldDefaultValue] = defaultValue;

                // Create needed area item(s).

                // Split incoming string to pairs and numbers in a list.
                C.GsdObject obj = null;
                ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

                foreach (string s in splitlist)
                {
                    // Get min and max value.
                    string ssMin = String.Empty;
                    string ssMax = String.Empty;
                    int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                    if (index != -1)
                    {
                        // Area, separate area and add values (1..65534).
                        ssMin = s.Substring(0, index);	// 1
                        ssMax = s.Substring(index + 2);	// 65534
                    }
                    else
                    {
                        // Value, create area with same min and max value.
                        ssMin = s;
                        ssMax = s;
                    }

                    // Create AreaItem(s).
                    ushort usMin = System.Xml.XmlConvert.ToUInt16(ssMin);
                    ushort usMax = System.Xml.XmlConvert.ToUInt16(ssMax);

                    valuehash[Models.s_FieldMinValue] = usMin;
                    valuehash[Models.s_FieldMaxValue] = usMax;

                    obj = new C.AreaItem();
                    succeeded = obj.Fill(valuehash);
                    if (!succeeded)
                        throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                    list.Add(obj);
                }

                hash[Models.s_FieldValues] = list;

                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_Source_Add)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_Source_Add)!", e);
            }
        }

        /// <summary>
        /// The addresses of the F components of a safety control loop F input, F-CPU and F output shall be
        /// unambiguous. Locally, each F device has the configured source-destination relationship of the
        /// communication link with its partner. It is retentively stored in the F devices, 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	4
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 1..65534, max. range: 1..65534, default value: 1).
        /// </summary>
        /// <returns>Ref object for the F_Dest_Add parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFDestAdd(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FDestAdd;
                hash[Models.s_FieldNameTextId] = Elements.s_FDestAdd;
                hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
                hash[Models.s_FieldByteOffset] = (uint)4;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)16;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;
                hash[Models.s_FieldDefaultValue] = (ushort)1;

                // Build values.
                ArrayList list = new ArrayList();

                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                string sAllowedValues = @"1..65534";
                uint defaultValue = 1;

                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FDestAdd, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                    if (attr.Length != 0)
                        sAllowedValues = attr;

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length != 0)
                        defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }

                hash[Models.s_FieldDefaultValue] = defaultValue;

                // Create needed area item(s).

                // Split incoming string to pairs and numbers in a list.
                C.GsdObject obj = null;
                ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

                foreach (string s in splitlist)
                {
                    // Get min and max value.
                    string ssMin = String.Empty;
                    string ssMax = String.Empty;
                    int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                    if (index != -1)
                    {
                        // Area, separate area and add values (1..65534).
                        ssMin = s.Substring(0, index);	// 1
                        ssMax = s.Substring(index + 2);	// 65534
                    }
                    else
                    {
                        // Value, create area with same min and max value.
                        ssMin = s;
                        ssMax = s;
                    }

                    // Create AreaItem(s).
                    ushort usMin = System.Xml.XmlConvert.ToUInt16(ssMin);
                    ushort usMax = System.Xml.XmlConvert.ToUInt16(ssMax);

                    valuehash[Models.s_FieldMinValue] = usMin;
                    valuehash[Models.s_FieldMaxValue] = usMax;

                    obj = new C.AreaItem();
                    succeeded = obj.Fill(valuehash);
                    if (!succeeded)
                        throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                    list.Add(obj);
                }

                hash[Models.s_FieldValues] = list;

                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_Dest_Add)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_Dest_Add)!", e);
            }
        }

        /// <summary>
        /// Locally, each F device maintains a configured F watchdog time for each source-destination
        /// relationship. The device starts this timer whenever it sends a safe message frame.
        /// The time base for the encoded watchdog time is 1 ms. 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	6
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 1..65535, max. range: 1..65535, default value: 150).
        /// </summary>
        /// <returns>Ref object for the F_WD_Time parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFWdTime(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FWdTime;
                hash[Models.s_FieldNameTextId] = Elements.s_FWdTime;
                hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
                hash[Models.s_FieldByteOffset] = (uint)6;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)16;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;

                // Build values.
                ArrayList list = new ArrayList();

                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                string sAllowedValues = @"1..65535";
                ushort defaultValue = 150;	// Is initially setted with default value!

                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FWdTime, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                    if (attr.Length != 0)
                        sAllowedValues = attr;

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length != 0)
                        defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }

                // Create needed area item(s).

                hash[Models.s_FieldDefaultValue] = defaultValue;

                // Split incoming string to pairs and numbers in a list.
                C.GsdObject obj = null;
                ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

                foreach (string s in splitlist)
                {
                    // Get min and max value.
                    string ssMin = String.Empty;
                    string ssMax = String.Empty;
                    int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                    if (index != -1)
                    {
                        // Area, separate area and add values (1..65534).
                        ssMin = s.Substring(0, index);	// 1
                        ssMax = s.Substring(index + 2);	// 65534
                    }
                    else
                    {
                        // Value, create area with same min and max value.
                        ssMin = s;
                        ssMax = s;
                    }

                    // Create AreaItem(s).
                    ushort usMin = System.Xml.XmlConvert.ToUInt16(ssMin);
                    ushort usMax = System.Xml.XmlConvert.ToUInt16(ssMax);

                    valuehash[Models.s_FieldMinValue] = usMin;
                    valuehash[Models.s_FieldMaxValue] = usMax;

                    obj = new C.AreaItem();
                    succeeded = obj.Fill(valuehash);
                    if (!succeeded)
                        throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                    list.Add(obj);
                }

                hash[Models.s_FieldValues] = list;

                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_WD_Time)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_WD_Time)!", e);
            }
        }

        /// <summary>
        /// This CRC1 key is generated by the engineering tool across the F parameters. 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	8
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 0..65535, max. range: 0..65535, default value: 53356).
        /// </summary>
        /// <returns>Ref object for the F_Par_CRC parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFParCrc(XPathNavigator nav, ref Hashtable hash)
        {
            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FParCrc;
            hash[Models.s_FieldNameTextId] = Elements.s_FParCrc;
            hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
            hash[Models.s_FieldByteOffset] = (uint)8;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)16;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;
            hash[Models.s_FieldIsChangeable] = true;	// auto
            hash[Models.s_FieldIsVisible] = true;

            ushort defaultValue = 53356;	// Is initially setted with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FParCrc, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                if (attr.Length > 0)
                    defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
            }

            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();

            C.GsdObject obj = new C.AreaItem();
            valuehash[Models.s_FieldMinValue] = ushort.MinValue;
            valuehash[Models.s_FieldMaxValue] = ushort.MaxValue;
            bool succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        #endregion

        #endregion

        #endregion

    }
}


