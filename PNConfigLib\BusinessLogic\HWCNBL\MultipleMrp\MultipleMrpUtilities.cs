/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MultipleMrpUtilities.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.MultipleMrp
{
    /// <summary>
    /// Contains and methods related to multiple MRP configuration.
    /// </summary>
    internal static class MultipleMrpUtilities
    {
        /// <summary>
        /// Gets the mrp role of a multiple mrp supported interface.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface whose MRP role will be retrieved.</param>
        /// <param name="instance">MRP instance number.</param>
        /// <returns>The MRP role of the interface for the given MRP instance.</returns>
        internal static PNMrpRole GetMultipleRole(Interface interfaceSubmodule, int instance)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            //Check if single role of first instance or not
            if ((instance == 1)
                && !IsMultipleInstancesActive(interfaceSubmodule))
            {
                uint pnMrpRole =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnMrpRole,
                        new AttributeAccessCode(),
                        0u);
                return (PNMrpRole)pnMrpRole;
            }

            MrpDomainInstance instanceObject = interfaceSubmodule.MrpDomainInstances[instance - 1];
            if (instanceObject != null)
            {
                int mrpRole =
                    instanceObject.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnInstanceMrpRole, new AttributeAccessCode(),
                        0);
                return (PNMrpRole)mrpRole;
            }

            return PNMrpRole.NotInRing;
        }

        /// <summary>
        /// Gets whether MRP block v1.1 should be generated.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule that is checked.</param>
        /// <returns>Whether MRP block v1.1 should be generated.</returns>
        internal static bool IsMrpBlocksV11(Interface interfaceSubmodule)
        {
            //If Number of max instances is grater than 0 than V1.1 blocks should be generated
            if (interfaceSubmodule == null)
            {
                return false;
            }
            if (interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnMrpMaxInstances,
                new AttributeAccessCode(),
                0) > 0)
            {
                return true;
            }

            //If Norm auto manager is available then V1.1 blocks should be generated
            Enumerated supportedMrpRoles =
                interfaceSubmodule.PCLCatalogObject.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnMrpRole,
                    new AttributeAccessCode(),
                    new Enumerated());
            foreach (PNMrpRole child in supportedMrpRoles.List)
            {
                if (child == PNMrpRole.NormManagerAuto)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Gets whether more than one MRP instance is active for given interface.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule that is checked.</param>
        /// <returns>Whether multiple MRP instances are active.</returns>
        internal static bool IsMultipleInstancesActive(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            if (!IsMultipleInstancesAvailable(interfaceSubmodule))
            {
                return false;
            }

            if (interfaceSubmodule.MrpDomainInstances != null)
            {
                if (interfaceSubmodule.MrpDomainInstances.Count(
                    instance => (PNMrpRole)(instance.AttributeAccess.GetAnyAttribute<int>(
                                                   InternalAttributeNames.PnInstanceMrpRole,
                                                   new AttributeAccessCode(),
                                                   0)) != PNMrpRole.NotInRing) > 1)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Gets whether multiple mrp instances for given interface is available.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule that is checked.</param>
        /// <returns>Whether multiple MRP instances are available.</returns>
        internal static bool IsMultipleInstancesAvailable(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            return interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnMrpMaxInstances,
                new AttributeAccessCode(),
                0u) > 1u;
        }

        /// <summary>
        /// The method finds the multiple mrp instance of given port submodule
        /// </summary>
        /// <param name="portSubmodule"></param>
        /// <returns>y</returns>
        internal static int GetInstanceOfPort(DataModel.PCLObjects.Port portSubmodule)
        {
            if (portSubmodule == null)
            {
                throw new ArgumentNullException(nameof(portSubmodule));
            }

            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);
            uint numberOfInstances = interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnMrpMaxInstances, new AttributeAccessCode(), 0);

            if (numberOfInstances <= 1)
                return 0;

            MrpDomainInstance mrpInstance = portSubmodule.GetInterface()
                .MrpDomainInstances.Find(instance => instance.RingPorts.Any(p=> p.Id == portSubmodule.Id));

            if (mrpInstance != null)
            {
                int currInstanceNumber = mrpInstance.AttributeAccess
                    .GetAnyAttribute<Int32>(InternalAttributeNames.PnInstanceNumber, new AttributeAccessCode(), 0);

                return currInstanceNumber;
            }

            return 0;
        }

        /// <summary>
        /// The method converts PNMrpRole into its text equivalent
        /// </summary>
        /// <param name="currRole"></param>
        /// <returns>y</returns>
        internal static string RoleToText(PNMrpRole currRole)
        {
            switch (currRole)
            {
                case PNMrpRole.Client:
                    return PNMrpRole.Client.ToString();
                case PNMrpRole.Manager:
                    return PNMrpRole.Manager.ToString();
                case PNMrpRole.NormManagerAuto:
                    return InternalAttributeNames.ManagerAuto;
                case PNMrpRole.NotInRing:
                    return PNMrpRole.NotInRing.ToString();
            }
            return string.Empty;
        }

        /// <summary>
        /// This method finds multiple mrp instance numbers of given interface from given mrp domain
        /// </summary>
        /// <param name="domain"></param>
        /// <param name="interfaceSubmoduleId"></param>
        /// <returns>instance numbers of the interface of the domain</returns>
        internal static List<int> GetParticipantInstancesFromMrpDomain(MrpDomain domain, string interfaceSubmoduleId)
        {
            if (domain == null)
            {
                throw new ArgumentNullException(nameof(domain));
            }
            List<int> instancesFromDomain = new List<int>();
            Interface interfaceSubmodule = domain.Participants.FirstOrDefault(p => p.Id == interfaceSubmoduleId);
            if (interfaceSubmodule != null)
            {
                IEnumerable<MrpDomainInstance> instanceList =
                    interfaceSubmodule.MrpDomainInstances.Where(mrpInstance => mrpInstance.MrpDomainId == domain.Id);
                foreach (MrpDomainInstance instance in instanceList)
                {
                    int instanceNumber = instance.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnInstanceNumber,
                        new AttributeAccessCode(),
                        0);

                    if (instanceNumber != 0)
                    {
                        instancesFromDomain.Add(
                            instance.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnInstanceNumber,
                                new AttributeAccessCode(),
                                0));
                    }
                }
                if (instancesFromDomain.Count == 0)
                {
                    instancesFromDomain.Add(1);
                }
            }
            return instancesFromDomain;
        }

        /// <summary>
        /// This method is basically same with GetSelectedPortsOfInstance however it returns config objects instead
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="instanceNumber"></param>
        /// <returns>y</returns>
        internal static List<DataModel.PCLObjects.Port> GetSelectedPortConfigsOfInstance(Interface interfaceSubmodule, int instanceNumber)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            List<DataModel.PCLObjects.Port> portsList = new List<DataModel.PCLObjects.Port>();
            List<int> usedPortIds = new List<int>();

            // Dictonary<instanceNumber, List<PortIds>>
            Dictionary<int, List<int>> configuredPortCoreIds = new Dictionary<int, List<int>>();
            int maxInstanceNumber = interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnMrpMaxInstances, new AttributeAccessCode(), 0);

            for (int i = 1; i <= maxInstanceNumber; i++)
            {
                MrpDomainInstance instance = interfaceSubmodule.MrpDomainInstances[i];

                configuredPortCoreIds.Add(i, new List<int>());
                foreach (DataModel.PCLObjects.Port port in instance.RingPorts)
                {
                    int portCoreId = port.GetHashCode();
                    if (portCoreId != 0)
                    {
                        configuredPortCoreIds[i].Add(portCoreId);
                        usedPortIds.Add(portCoreId);
                    }
                }
            }

            // DomainInstanceToPorts relation only exists if ring ports were selected deliberately
            foreach (DataModel.PCLObjects.Port port in interfaceSubmodule.GetPorts())
            {
                // add configured port names to portNames list
                int currPortId = port.GetHashCode();
                if (configuredPortCoreIds.ContainsKey(instanceNumber)
                    && configuredPortCoreIds[instanceNumber].Contains(currPortId))
                {
                    portsList.Add(port);

                    if (portsList.Count == 2)
                        break;
                }
                // autoconfigure free port
                else if (!usedPortIds.Contains(currPortId) &&
                         (port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnMrpIsDefaultRingPort, new AttributeAccessCode(), false) ||
                          port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnMrpSupportsRingConfig, new AttributeAccessCode(), false)))
                {
                    foreach (int key in configuredPortCoreIds.Keys)
                    {
                        if (configuredPortCoreIds[key].Count < 2)
                        {
                            configuredPortCoreIds[key].Add(currPortId);
                            if (key == instanceNumber)
                            {
                                portsList.Add(port);
                            }
                            break;
                        }
                    }
                }
            }
            return portsList;
        }
    }
}