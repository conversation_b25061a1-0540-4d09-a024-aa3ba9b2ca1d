<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="M_0x00001001_1" xml:space="preserve">
    <value>Checker, which supports GSD(ML) version "{0}", is used for GSD(ML) with version "{1}".</value>
  </data>
  <data name="M_0x00001002_1" xml:space="preserve">
    <value>Checker couldn't check GSD file ("{0}") correctly! Please note that the mentioned GSD file is not completely checked.</value>
  </data>
  <data name="M_0x00001002_2" xml:space="preserve">
    <value>!!!   Internal error !!!! Please note that the GSD file ("{0}") is not completely checked.</value>
  </data>
  <data name="M_0x00001002_3" xml:space="preserve">
    <value>Could not find the appropriate checker version. Please note that the GSD file is not checked.</value>
  </data>
  <data name="M_0x00010002_1" xml:space="preserve">
    <value>The GSD file must have the extension ".xml".</value>
  </data>
  <data name="M_0x00010002_2" xml:space="preserve">
    <value>Name of GSD must consist of at least 5 fields, separated by "-".</value>
  </data>
  <data name="M_0x00010002_3" xml:space="preserve">
    <value>GSD file name must begin with "GSDML-".</value>
  </data>
  <data name="M_0x00010002_4" xml:space="preserve">
    <value>"GSDML-" in GSD name must be upper case.</value>
  </data>
  <data name="M_0x00010002_5" xml:space="preserve">
    <value>GSD version field in GSD name must match "Vx.y" whereby "x" and "y" are unsigned numbers.</value>
  </data>
  <data name="M_0x00010002_6" xml:space="preserve">
    <value>The "V" from the version field in GSD name must be upper case.</value>
  </data>
  <data name="M_0x00010002_7" xml:space="preserve">
    <value>Sixth field of GSD name (release time): hour must be within 0-23.</value>
  </data>
  <data name="M_0x00010002_8" xml:space="preserve">
    <value>Sixth field of GSD name (release time): minute must be within 0-59.</value>
  </data>
  <data name="M_0x00010002_9" xml:space="preserve">
    <value>Sixth field of GSD name (release time): second must be within 0-59.</value>
  </data>
  <data name="M_0x00010002_a" xml:space="preserve">
    <value>After the vendor and device family name in GSD name, only a release date matching \"yyyymmdd\" and optionally a release time matching \"hhmmss\" may follow.</value>
  </data>
  <data name="M_0x00010002_b" xml:space="preserve">
    <value>Release date of GSD name: year should be within 2000-2099.</value>
  </data>
  <data name="M_0x00010002_c" xml:space="preserve">
    <value>Release date of GSD name: month must be within 1-12.</value>
  </data>
  <data name="M_0x00010002_d" xml:space="preserve">
    <value>Release date of GSD name: day must be within 1-31.</value>
  </data>
  <data name="M_0x00010002_e" xml:space="preserve">
    <value>Release date of GSD name: day exceeds length of month.</value>
  </data>
  <data name="M_0x00010003_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem/@ID' must be unique over all 'DeviceAccessPointItem' elements.</value>
  </data>
  <data name="M_0x00010004_1" xml:space="preserve">
    <value>The 'ModuleItem/@ID' must be unique over all 'ModuleItem' elements.</value>
  </data>
  <data name="M_0x00010004_2" xml:space="preserve">
    <value>The 'ModuleItemRef/@ModuleItemTarget' attribute must reference an existing module 'ID'.</value>
  </data>
  <data name="M_0x00010005_1" xml:space="preserve">
    <value>The 'ValueItem/@ID' must be unique over all 'ValueItem' elements.</value>
  </data>
  <data name="M_0x00010005_2" xml:space="preserve">
    <value>The 'Ref/@ValueItemTarget' attribute must reference an existing ValueItem 'ID'.</value>
  </data>
  <data name="M_0x00010006_1" xml:space="preserve">
    <value>The 'GraphicItem/@ID' must be unique over all 'GraphicItem' elements.</value>
  </data>
  <data name="M_0x00010006_2" xml:space="preserve">
    <value>The 'GraphicItemRef/@GraphicItemTarget' attribute must reference an existing GraphicItem 'ID'.</value>
  </data>
  <data name="M_0x00010007_1" xml:space="preserve">
    <value>The 'CategoryItem/@ID' must be unique over all 'CategoryItem' elements.</value>
  </data>
  <data name="M_0x00010007_2" xml:space="preserve">
    <value>The 'ModuleInfo/@CategoryRef' attribute must reference an existing CategoryItem 'ID'.</value>
  </data>
  <data name="M_0x00010007_3" xml:space="preserve">
    <value>The 'ModuleInfo/@SubCategory1Ref' attribute must reference an existing CategoryItem 'ID'.</value>
  </data>
  <data name="M_0x00010008_1" xml:space="preserve">
    <value>The 'VirtualSubmoduleItem/@ID' must be unique over all 'VirtualSubmoduleItem' elements.</value>
  </data>
  <data name="M_0x00010009_1" xml:space="preserve">
    <value>The 'Text/@TextId' attribute must be unique for all text IDs of the primary language.</value>
  </data>
  <data name="M_0x00010009_2" xml:space="preserve">
    <value>The 'xml:lang' attribute must be unique in the 'ExternalTextList'. The language "{0}" is more than one times defined.</value>
  </data>
  <data name="M_0x00010009_3" xml:space="preserve">
    <value>The language code "en" is not valid, because the PrimaryLanguage already contains the English texts.</value>
  </data>
  <data name="M_0x00010009_4" xml:space="preserve">
    <value>The language code is not a valid value as defined in ISO 639-1.</value>
  </data>
  <data name="M_0x00010009_5" xml:space="preserve">
    <value>The 'Text/@TextId' attribute must be unique for all text IDs of the language "{0}".</value>
  </data>
  <data name="M_0x00010009_6" xml:space="preserve">
    <value>The 'TextId' reference attribute must reference an existing primary language text.</value>
  </data>
  <data name="M_0x00010009_7" xml:space="preserve">
    <value>The 'TextId' reference attribute must reference an existing text of the language "{0}".</value>
  </data>
  <data name="M_0x0001000A_1" xml:space="preserve">
    <value>The maximal data length is greater than the sum of maximal input and output length.</value>
  </data>
  <data name="M_0x0001000B_1" xml:space="preserve">
    <value>The maximal data length is lower than the highest value of maximal input or output length.</value>
  </data>
  <data name="M_0x0001000C_1" xml:space="preserve">
    <value>The attribute '@Length' must be used, if the data type is "{0}".</value>
  </data>
  <data name="M_0x0001000C_2" xml:space="preserve">
    <value>The attribute '@Length' must be unequal to 0, if the data type is "{0}".</value>
  </data>
  <data name="M_0x0001000C_3" xml:space="preserve">
    <value>The attribute 'DataItem/@Length(= {0})' must match to 'DataItem/@DataType' (length= {1}).</value>
  </data>
  <data name="M_0x0001000C_4" xml:space="preserve">
    <value>The attribute '@Length' must be greater or equal 3, if the data type is "61131_STRING".</value>
  </data>
  <data name="M_0x0001000C_5" xml:space="preserve">
    <value>The attribute '@Length' must be greater or equal 6 and an even number, if the data type is "61131_WSTRING".</value>
  </data>
  <data name="M_0x0001000C_6" xml:space="preserve">
    <value>The attribute '@Length' must be greater or equal 3 and '@Length' modulo 3 equal 0, if the data type is "OctetString_S".</value>
  </data>
  <data name="M_0x0001000C_7" xml:space="preserve">
    <value>The attribute 'DataItem/@Length' is only allowed on (Octet)String data types with variable size.</value>
  </data>
  <data name="M_0x0001000D_1" xml:space="preserve">
    <value>The length of all input 'DataItem' or output 'DataItem' elements within a 'IOData' element plus the IOPS/IOCS length must not be greater than 1440 bytes.</value>
  </data>
  <data name="M_0x0001000E_1" xml:space="preserve">
    <value>The value of the 'Assign/@Content' attribute must be unique within each 'ValueItem/Assignments' element.</value>
  </data>
  <data name="M_0x0001000F_1" xml:space="preserve">
    <value>The value of the 'Assign/@TextId' attribute must be unique within each 'ValueItem/Assignments' element.</value>
  </data>
  <data name="M_0x00010010_1" xml:space="preserve">
    <value>The 'RecordDataList/*/@Index' attribute value must be unique within each 'VirtualSubmoduleItem' or 'SubmoduleItem' element.</value>
  </data>
  <data name="M_0x00010011_1" xml:space="preserve">
    <value>If the transfer sequence is specified for the record data objects, the 'TransferSequence' attribute must be specified and the values must be unequal to 0.</value>
  </data>
  <data name="M_0x00010011_2" xml:space="preserve">
    <value>If the transfer sequence is specified for the record data objects, the 'TransferSequence' attribute values must be unique within each 'VirtualSubmoduleItem' element.</value>
  </data>
  <data name="M_0x00010011_3" xml:space="preserve">
    <value>If the transfer sequence is specified for the record data objects, the 'TransferSequence' attribute values must be ascending and starting with 1.</value>
  </data>
  <data name="M_0x00010012_1" xml:space="preserve">
    <value>The 'Ref/@BitOffset' attribute can only be used in conjunction with data type "Bit" or "BitArea".</value>
  </data>
  <data name="M_0x00010013_1" xml:space="preserve">
    <value>The 'Ref/@BitLength' attribute must be in the range of 1 to 15 for the data type "BitArea".</value>
  </data>
  <data name="M_0x00010013_2" xml:space="preserve">
    <value>The 'Ref/@BitLength' attribute can only be used in conjunction with data type "BitArea".</value>
  </data>
  <data name="M_0x00010015_1" xml:space="preserve">
    <value>The 'ModuleIdentNumber' must not be 0x00000000.</value>
  </data>
  <data name="M_0x00010016_1" xml:space="preserve">
    <value>Slots defined in '{0}' are not available in '{1}'.</value>
  </data>
  <data name="M_0x00010016_2" xml:space="preserve">
    <value>Duplicate 'ModuleItemRef' elements found (it means, they have the same target).</value>
  </data>
  <data name="M_0x00010016_3" xml:space="preserve">
    <value>Slot {0} is reserved from another module.</value>
  </data>
  <data name="M_0x00010016_4" xml:space="preserve">
    <value>Slots defined in '{0}' must not be available in '{1}'.</value>
  </data>
  <data name="M_0x00010016_5" xml:space="preserve">
    <value>Without system redundancy, 'AllowedInSlots' should not be used.</value>
  </data>
  <data name="M_0x00010016_6" xml:space="preserve">
    <value>For a non redundant DAP only one slot number is allowed in the list of 'FixedInSlots'.</value>
  </data>
  <data name="M_0x00010017_1" xml:space="preserve">
    <value>The 'Type' ("{0}") is a duplicate.</value>
  </data>
  <data name="M_0x00010018_1" xml:space="preserve">
    <value>The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}.</value>
  </data>
  <data name="M_0x00010018_2" xml:space="preserve">
    <value>The 'ErrorType' ({0}) is a duplicate.</value>
  </data>
  <data name="M_0x00010018_3" xml:space="preserve">
    <value>The combination 'ErrorType' ({0}) and 'API' ({1}) is a duplicate.</value>
  </data>
  <data name="M_0x00010019_1" xml:space="preserve">
    <value>The 'UserStructureIdentifier' ({0}) is not in the range of {1} to {2}.</value>
  </data>
  <data name="M_0x00010019_2" xml:space="preserve">
    <value>The 'UserStructureIdentifier' ({0}) is a duplicate.</value>
  </data>
  <data name="M_0x00010019_3" xml:space="preserve">
    <value>The combination 'UserStructureIdentifier' ({0}) and 'API' ({1}) is a duplicate.</value>
  </data>
  <data name="M_0x00010020_1" xml:space="preserve">
    <value>The offset combination 'ByteOffset' ({0}) plus 'BitOffset' ({1}) is a duplicate.</value>
  </data>
  <data name="M_0x00010020_2" xml:space="preserve">
    <value>The 'Ref' data entries overlap.</value>
  </data>
  <data name="M_0x00010020_3" xml:space="preserve">
    <value>The 'Length' of the data items ({0}) is lower than the 'Ref' length ({1}).</value>
  </data>
  <data name="M_0x00010021_1" xml:space="preserve">
    <value>The 'ByteOffset' ({0}) is a duplicate.</value>
  </data>
  <data name="M_0x00010021_2" xml:space="preserve">
    <value>The 'Const' data entries are overlapped.</value>
  </data>
  <data name="M_0x00010021_3" xml:space="preserve">
    <value>The 'Length' of the data items ({0}) is lower than the 'Const' length ({1}).</value>
  </data>
  <data name="M_0x00010022_1" xml:space="preserve">
    <value>Ref is changeable but not visible.</value>
  </data>
  <data name="M_0x00010022_2" xml:space="preserve">
    <value>The 'DefaultValue' ({0}) does not comply with the 'DataType' ("{1}" with bit length {2}).</value>
  </data>
  <data name="M_0x00010022_3" xml:space="preserve">
    <value>The 'DefaultValue' ({0}) does not comply with the 'DataType' ("{1}").</value>
  </data>
  <data name="M_0x00010022_4" xml:space="preserve">
    <value>The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}" with bit length {2}).</value>
  </data>
  <data name="M_0x00010022_5" xml:space="preserve">
    <value>The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}").</value>
  </data>
  <data name="M_0x00010022_6" xml:space="preserve">
    <value>The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}" with bit length {3}).</value>
  </data>
  <data name="M_0x00010022_7" xml:space="preserve">
    <value>The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}").</value>
  </data>
  <data name="M_0x00010022_8" xml:space="preserve">
    <value>The 'DefaultValue' ({0}) is not available from 'ValueItem' assignments 'Content' attribute.</value>
  </data>
  <data name="M_0x00010022_9" xml:space="preserve">
    <value>The value ({0}) specified by 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute.</value>
  </data>
  <data name="M_0x00010022_a" xml:space="preserve">
    <value>The 'DefaultValue' ({0}) is not available from the 'AllowedValues' ({1}).</value>
  </data>
  <data name="M_0x00010022_b" xml:space="preserve">
    <value>For 'DataType' = "Float32" or 'DataType' = "Float64" 'AllowedValues' must not contain a range: {0}..{1}.</value>
  </data>
  <data name="M_0x00010022_c" xml:space="preserve">
    <value>For 'DataType' ("{0}") no AllowedValues must be given.</value>
  </data>
  <data name="M_0x00010022_d" xml:space="preserve">
    <value>For 'DataType' ("{0}") no ValueItemTarget must be given.</value>
  </data>
  <data name="M_0x00010022_e" xml:space="preserve">
    <value>For 'DataType' ("{0}") the DefaultValue does not fit.</value>
  </data>
  <data name="M_0x00010022_f" xml:space="preserve">
    <value>At least one value ({0}) specified by the range: {1}..{2} in 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute.</value>
  </data>
  <data name="M_0x00010023_1" xml:space="preserve">
    <value>The 'SendClock' attribute must contain the mandatory value 32.</value>
  </data>
  <data name="M_0x00010024_1" xml:space="preserve">
    <value>The element 'ProfileBody/ApplicationProcess' is missing. It must be given just one-time.</value>
  </data>
  <data name="M_0x00010025_1" xml:space="preserve">
    <value>The 'DeviceID' must not be 0.</value>
  </data>
  <data name="M_0x00010025_2" xml:space="preserve">
    <value>Error in value of 'DeviceID'.</value>
  </data>
  <data name="M_0x00010026_1" xml:space="preserve">
    <value>The 'schemaLocation' attribute must be given.</value>
  </data>
  <data name="M_0x00010026_2" xml:space="preserve">
    <value>The 'schemaLocation' attribute does not contain the appropriate number of two entries.</value>
  </data>
  <data name="M_0x00010026_3" xml:space="preserve">
    <value>The first entry of the 'schemaLocation' attribute must contain either the namespace: "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/Common/2003/11/Primitives".</value>
  </data>
  <data name="M_0x00010026_4" xml:space="preserve">
    <value>The first entry of the 'schemaLocation' attribute must contain either the namespace: "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/GSDML/2003/11/Primitives".</value>
  </data>
  <data name="M_0x00010026_5" xml:space="preserve">
    <value>The second entry of the 'schemaLocation' attribute must contain the device profile schema: "..\xsd\GSDML-DeviceProfile-vX.Y.xsd".</value>
  </data>
  <data name="M_0x00010026_6" xml:space="preserve">
    <value>The second entry of the 'schemaLocation' attribute must contain the primitoves schema: "..\..\xsd\Common-Primitives-v1.0.xsd".</value>
  </data>
  <data name="M_0x00010026_7" xml:space="preserve">
    <value>The second entry of the 'schemaLocation' attribute must contain the primitives schema: "..\..\xsd\GSDML-Primitives-vX.Y.xsd".</value>
  </data>
  <data name="M_0x00010026_8" xml:space="preserve">
    <value>The 'schemaLocation' attribute does not reference the appropriate schema file.</value>
  </data>
  <data name="M_0x00010027_1" xml:space="preserve">
    <value>'Ref/@DataType' = "VisibleString" or 'Ref/@DataType' = "OctetString" not allowed, because 'Ref/@Length' not available. 'Ref/@Length' is available from GSDML V2.1 on.</value>
  </data>
  <data name="M_0x00010027_5" xml:space="preserve">
    <value>The attribute 'Ref/@DefaultValue' contains {0} octets, but does not contain the appropriate number of {1} octets.</value>
  </data>
  <data name="M_0x00010027_6" xml:space="preserve">
    <value>The attribute 'Ref/@DefaultValue' contains {0} characters, but must not contain more than {1} characters.</value>
  </data>
  <data name="M_0x00010028_1" xml:space="preserve">
    <value>The Element 'ProfileBody/DeviceIdentity' is missing. It must be given just one-time.</value>
  </data>
  <data name="M_0x00010028_2" xml:space="preserve">
    <value>The element 'ProfileBody/DeviceIdentity' is more than one-time given. It must be given just one-time.</value>
  </data>
  <data name="M_0x00010029_1" xml:space="preserve">
    <value>The element 'ProfileBody/DeviceManager' is superfluous.</value>
  </data>
  <data name="M_0x00010029_2" xml:space="preserve">
    <value>The element 'ProfileBody/ExternalProfileHandle' is superfluous.</value>
  </data>
  <data name="M_0x0001002A_1" xml:space="preserve">
    <value>The element 'ProfileBody/DeviceFunction' must be given just one-time. Superfluous definitions will be ignored.</value>
  </data>
  <data name="M_0x0001002B_1" xml:space="preserve">
    <value>The element 'ProfileBody/ApplicationProcess' must be given just one-time. Superfluous definitions will be ignored.</value>
  </data>
  <data name="M_0x0001002C_1" xml:space="preserve">
    <value>The element 'ProfileHeader/ProfileIdentification' must have the value "PROFINET Device Profile". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002C_2" xml:space="preserve">
    <value>The element 'ProfileHeader/ProfileRevision' must have the value "1.00". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002C_3" xml:space="preserve">
    <value>The element 'ProfileHeader/ProfileName' must have the value "Device Profile for PROFINET Devices". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002C_4" xml:space="preserve">
    <value>The element 'ProfileHeader/ProfileSource' must have the value "PROFIBUS Nutzerorganisation e. V. (PNO)". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002C_5" xml:space="preserve">
    <value>The element 'ProfileHeader/ProfileClassID' must have the value "Device". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002C_6" xml:space="preserve">
    <value> The element 'ProfileHeader/ISO15745Reference/ISO15745Part' must have the value 4. You have given {0}.</value>
  </data>
  <data name="M_0x0001002C_7" xml:space="preserve">
    <value>The element 'ProfileHeader/ISO15745Reference/ISO15745Edition' must have the value 1. You have given {0}.</value>
  </data>
  <data name="M_0x0001002C_8" xml:space="preserve">
    <value>The element 'ProfileHeader/ISO15745Reference/ProfileTechnology' must have the value "GSDML". You have given "{0}".</value>
  </data>
  <data name="M_0x0001002D_1" xml:space="preserve">
    <value>As prefix for the schema instance namespace, http://www.w3.org/2001/XMLSchema-instance, "xsi" shall be used in the GSDML.</value>
  </data>
  <data name="M_0x0001002E_1" xml:space="preserve">
    <value>The 'ParameterRecordDataItem/@Index' attribute value (= {0}) must be in a range between 0..32767 for 'API' == 0 (default) or in a range between 0..32767 or 0xB000..0xBFFF for 'API' &lt;&gt; 0.</value>
  </data>
  <data name="M_0x0001002E_2" xml:space="preserve">
    <value>Profile specific records ('ParameterRecordDataItem/@Index' in a range between 0xB000 (45056) and 0xBFFF (49151)) may only be used if the corresponding submodule is defined in an 'API' &lt;&gt; 0.</value>
  </data>
  <data name="M_0x0001002E_3" xml:space="preserve">
    <value>The 'F_ParameterRecordDataItem/@Index' attribute value (= {0}) must be in a range between 0..32767.</value>
  </data>
  <data name="M_0x0001002F_1" xml:space="preserve">
    <value>The attribute 'ApplicationProcess/ExternalTextList/Language/@xml:lang' is mandatory.</value>
  </data>
  <data name="M_0x00010030_1" xml:space="preserve">
    <value>Values contained within the '(RT_Class3)TimingProperties/@ReductionRatio' attribute must be lower or equal than 512 and higher or equal than 1.</value>
  </data>
  <data name="M_0x00010030_2" xml:space="preserve">
    <value>The value {0} contained within the '(RT_Class3)TimingProperties/@ReductionRatio' attribute is not a power of 2.</value>
  </data>
  <data name="M_0x00010030_3" xml:space="preserve">
    <value>At least one of the 'ReductionRatio', 'ReductionRatioPow2', 'ReductionRatioNonPow2' attributes at the 'RT_Class3TimingProperties' element must contain the mandatory values 1 2 4 8 16.</value>
  </data>
  <data name="M_0x00010030_4" xml:space="preserve">
    <value>At least one of the 'ReductionRatio', 'ReductionRatioPow2', 'ReductionRatioNonPow2' attributes at the 'TimingProperties' element must contain the mandatory values 1 2 4 8 16 32 64 128 256 512.</value>
  </data>
  <data name="M_0x00010031_1" xml:space="preserve">
    <value>'IOPS_Length' and 'IOCS_Length' must always be 1.</value>
  </data>
  <data name="M_0x00010033_1" xml:space="preserve">
    <value>'RecordDataList/ParameterRecordDataItem' must contain either a 'Const' or a 'Ref' element.</value>
  </data>
  <data name="M_0x00010033_2" xml:space="preserve">
    <value>'DeviceAccessPointItem/ARVendorBlock/Request' must contain either a 'Const' or a 'Ref' element.</value>
  </data>
  <data name="M_0x00010034_1" xml:space="preserve">
    <value>'ValueList/ValueItem' must contain a 'Help' and/or 'Assignments' element.</value>
  </data>
  <data name="M_0x00010035_1" xml:space="preserve">
    <value>Slot {0} can't be used for this module, because it is used permanently by '{1}'.</value>
  </data>
  <data name="M_0x00010035_2" xml:space="preserve">
    <value>Slot {0} can't be used twice. It is already used by '{1}'.</value>
  </data>
  <data name="M_0x0001003E_1" xml:space="preserve">
    <value>Could not find Schema: "{0}\{1}".</value>
  </data>
  <data name="M_0x0001003E_2" xml:space="preserve">
    <value>{0}: File is not a GSD file.</value>
  </data>
  <data name="M_0x0001003E_3" xml:space="preserve">
    <value>Neither from GSD file name nor from 'SchemaLocation' a correct GSDML version could be extracted.</value>
  </data>
  <data name="M_0x0001003E_4" xml:space="preserve">
    <value>Problem during validating GSDML file or creating XML document object.</value>
  </data>
  <data name="M_0x00010043_1" xml:space="preserve">
    <value>The subslot 0 is not allowed.</value>
  </data>
  <data name="M_0x00010043_2" xml:space="preserve">
    <value>The subslot 0 is only allowed if the pull module alarm is supported by the interface.</value>
  </data>
  <data name="M_0x00010043_3" xml:space="preserve">
    <value>Slot numbers must be in a range between 0 and 0x7FFF (32767).</value>
  </data>
  <data name="M_0x00010043_4" xml:space="preserve">
    <value>Values for 'PhysicalSubslots' must be in a range between 0 and 0x8FFF (36863).</value>
  </data>
  <data name="M_0x00010043_5" xml:space="preserve">
    <value>Subslot numbers that refer to a 'PortSubmoduleItem' must be in a range between 0x8000 (32768) and 0x8FFF (36863).</value>
  </data>
  <data name="M_0x00010043_6" xml:space="preserve">
    <value>Subslot numbers that refer to a "normal" 'SubmoduleItem' (not 'PortSubmoduleItem') must be in a range between 0 and 0x7FFF (32767).</value>
  </data>
  <data name="M_0x00010043_7" xml:space="preserve">
    <value>Subslot 0 in combination with PullModuleAlarmSupported="true" is allowed, but might not work with all controllers.</value>
  </data>
  <data name="M_0x00010044_1" xml:space="preserve">
    <value>Wrong format at attribute 'RecordDataList/ParameterRecordDataItem/Const/@Data'. Format should be like "0x00,0x0a".</value>
  </data>
  <data name="M_0x00010045_1" xml:space="preserve">
    <value>Wrong format at attribute 'ProfileBody/DeviceIdentity/@VendorID'. Format should be like "0x000a".</value>
  </data>
  <data name="M_0x00010046_1" xml:space="preserve">
    <value>Wrong format at attribute 'ProfileBody/DeviceIdentity/@DeviceID'. Format should be like "0x000a".</value>
  </data>
  <data name="M_0x00010047_1" xml:space="preserve">
    <value>Wrong format at attribute 'DeviceAccessPointList/DeviceAccessPointItem/@ModuleIdentNumber'. Format should be like "0x0000000a".</value>
  </data>
  <data name="M_0x00010048_1" xml:space="preserve">
    <value>Wrong format at attribute 'DeviceAccessPointList/DeviceAccessPointItem/@DNS_CompatibleName'. Format should be like "Pno-Example-Dap".</value>
  </data>
  <data name="M_0x00010048_2" xml:space="preserve">
    <value>The total length of attribute 'DNS_CompatibleName' must be &lt;= 240.</value>
  </data>
  <data name="M_0x00010048_3" xml:space="preserve">
    <value>The first label of attribute 'DNS_CompatibleName' must not have the form "port-xyz" or "port-xyz-abcde" with a, b, c, d, e, x, y, z = 0..9.</value>
  </data>
  <data name="M_0x00010048_4" xml:space="preserve">
    <value>The attribute 'DNS_CompatibleName' must not have the form "a.b.c.d" with a, b, c, d = 0..999.</value>
  </data>
  <data name="M_0x00010049_1" xml:space="preserve">
    <value>Wrong format at attribute 'ModuleList/ModuleItem/@ModuleIdentNumber'. Format should be like "0x0000000a".</value>
  </data>
  <data name="M_0x0001004A_1" xml:space="preserve">
    <value>Wrong format at attribute '(Virtual)SubmoduleItem/@SubmoduleIdentNumber'. Format should be like "0x0000000a".</value>
  </data>
  <data name="M_0x00010102_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' uses at least one '(Virtual)SubmoduleItem' which requires IsochroneMode.</value>
  </data>
  <data name="M_0x00010102_2" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' uses a 'ModuleItem' which has at least one 'VirtualSubmoduleItem' which requires IsochroneMode.</value>
  </data>
  <data name="M_0x00010102_3" xml:space="preserve">
    <value>'DeviceAccessPointItem' does not support IsochroneMode but has a submodule which supports IsochroneMode.</value>
  </data>
  <data name="M_0x00010102_4" xml:space="preserve">
    <value>'DeviceAccessPointItem' does not support IsochroneMode but can be used together with modules which support IsochroneMode.</value>
  </data>
  <data name="M_0x00010102_5" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' does not support IsochroneMode but it can be used together with a 'SubmoduleItem' which requires IsochroneMode.</value>
  </data>
  <data name="M_0x00010102_6" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' does not support IsochroneMode but it can be used together with a 'SubmoduleItem' which supports IsochroneMode.</value>
  </data>
  <data name="M_0x00010103_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports IsochroneMode, but the attribute 'SupportedRT_Class' is not set to "Class2" respectively "Class3".</value>
  </data>
  <data name="M_0x00010103_2" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports IsochroneMode, but the attribute 'SupportedRT_Class' is not set to "Class2" respectively "Class3" or 'SupportedRT_Classes' does not contain "RT_CLASS_2" respectively "RT_CLASS_3".</value>
  </data>
  <data name="M_0x00010104_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports "RT_CLASS_2" or "RT_CLASS_3", but the element 'SynchronisationMode' is not set.</value>
  </data>
  <data name="M_0x00010104_2" xml:space="preserve">
    <value>The element 'SynchronisationMode' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_2" or "RT_CLASS_3".</value>
  </data>
  <data name="M_0x00010105_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the element 'RT_Class3Properties' is not set.</value>
  </data>
  <data name="M_0x00010105_2" xml:space="preserve">
    <value>The element 'RT_Class3Properties' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_3".</value>
  </data>
  <data name="M_0x00010106_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the related 'PortSubmoduleItem' element does not have the needed attributes 'MaxPortRxDelay' respectively 'MaxPortTxDelay'.</value>
  </data>
  <data name="M_0x00010106_2" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the 'PortSubmoduleItem' element from the referenced 'ModuleItem' does not have the needed attributes 'MaxPortRxDelay' respectively 'MaxPortTxDelay'.</value>
  </data>
  <data name="M_0x00010107_1" xml:space="preserve">
    <value>The attribute 'T_DC_Min' must be lower or equal than 'T_DC_Max'.</value>
  </data>
  <data name="M_0x00010108_1" xml:space="preserve">
    <value>The value of 'T_DC_Min' * 'T_DC_Base' must be lower or equal than 1024.</value>
  </data>
  <data name="M_0x00010109_1" xml:space="preserve">
    <value>The value of 'T_DC_Max' * 'T_DC_Base' must be lower or equal than 1024.</value>
  </data>
  <data name="M_0x0001010A_1" xml:space="preserve">
    <value>The 'T_IO_Base' * 'T_IO_InputMin' must be lower or equal than 32000000.</value>
  </data>
  <data name="M_0x0001010B_1" xml:space="preserve">
    <value>The 'T_IO_Base' * 'T_IO_OutputMin' must be lower or equal than 32000000.</value>
  </data>
  <data name="M_0x0001010D_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem/ApplicationRelations' is ignored because the DAP is not built according to PROFINET IO V1.0. 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/ApplicationRelations' is evaluated instead.</value>
  </data>
  <data name="M_0x0001010E_1" xml:space="preserve">
    <value>The 'DataItem/@UseAsBits' attribute must be set to 'true', if the child element 'BitDataItem' is specified.</value>
  </data>
  <data name="M_0x0001010F_1" xml:space="preserve">
    <value>The 'BitDataItem/@BitOffset' attribute value is higher than allowed with the specified 'DataItem/@DataType' and its bit length.</value>
  </data>
  <data name="M_0x00010110_1" xml:space="preserve">
    <value>The 'DataItem/@DataType' attribute values "F_MessageTrailer4Byte" and "F_MessageTrailer5Byte" can only be used, if the attribute 'PROFIsafeSupported' of the '(Virtual)SubmoduleItem' element is set to "true".</value>
  </data>
  <data name="M_0x00010110_2" xml:space="preserve">
    <value>The 'IOData' attributes 'F_IO_StructureDescVersion' and 'F_IO_StructureDescCRC' can only be used, if the attribute 'PROFIsafeSupported' of the '(Virtual)SubmoduleItem' element is set to "true".</value>
  </data>
  <data name="M_0x00010112_1" xml:space="preserve">
    <value>The 'SubslotNumber' attribute value must be unique for each 'PortSubmoduleItem' and 'InterfaceSubmoduleItem' element within a 'ModuleItem' or 'DeviceAccessPointItem'.</value>
  </data>
  <data name="M_0x00010113_1" xml:space="preserve">
    <value>If the '(Virtual)SubmoduleItem/@PROFIsafeSupported' attribute is set to "true", the element 'RecordDataList/F_ParameterRecordDataItem' must be specified and the 'IOData/@F_IO_StructureDescCRC' attribute must be available.</value>
  </data>
  <data name="M_0x00010114_1" xml:space="preserve">
    <value>If the '(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem' element is available, the attribute 'PROFIsafeSupported' must be set to "true".</value>
  </data>
  <data name="M_0x00010116_1" xml:space="preserve">
    <value>If more than one 'VirtualSubmoduleItem' element within a 'VirtualSubmoduleList' is specified, the attribute 'FixedInSubslots' must be used for all these elements.</value>
  </data>
  <data name="M_0x00010117_1" xml:space="preserve">
    <value>The specified subslot number ('SubslotItem/@SubslotNumber') must be also available as a real subslot number of a 'InterfaceSubmoduleItem/@SubslotNumber' or 'PortSubmoduleItem/@SubslotNumber' or '@PhysicalSubslots'.</value>
  </data>
  <data name="M_0x00010117_2" xml:space="preserve">
    <value>The specified subslot number ('SubslotItem/@SubslotNumber') must be also available as a real subslot number of a Submodule ('VirtualSubmoduleItem/@FixedInSubslots', '@PhysicalSubslots').</value>
  </data>
  <data name="M_0x00010118_1" xml:space="preserve">
    <value>Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must not be duplicates.</value>
  </data>
  <data name="M_0x00010118_2" xml:space="preserve">
    <value>Values contained within the 'VirtualSubmoduleItem/@FixedInSubslots' attribute must be lower than 32768 (0x8000).</value>
  </data>
  <data name="M_0x00010119_1" xml:space="preserve">
    <value>Values contained within the '(RT_Class3)TimingProperties/@SendClock' attribute must be higher or equal than {0} and lower or equal than {1}.</value>
  </data>
  <data name="M_0x00010119_2" xml:space="preserve">
    <value>The 'SendClock' attribute must contain the mandatory value 32.</value>
  </data>
  <data name="M_0x0001011B_1" xml:space="preserve">
    <value>Values for 'F_Source_Add/@AllowedValues' or 'F_Dest_Add/@AllowedValues' attribute must be in the range of 1 to 65534.</value>
  </data>
  <data name="M_0x0001011C_1" xml:space="preserve">
    <value>Values for 'F_WD_Time/@AllowedValues' attribute must be in the range of 1 to 65535.</value>
  </data>
  <data name="M_0x0001011F_1" xml:space="preserve">
    <value>The 'RequiredSchemaVersion' is higher than the GSDML version of this GSD file.</value>
  </data>
  <data name="M_0x0001011F_2" xml:space="preserve">
    <value>The 'RequiredSchemaVersion' attribute references a non-existing GSDML version.</value>
  </data>
  <data name="M_0x0001011F_3" xml:space="preserve">
    <value>The attribute 'ApplicationProcess/SubmoduleList/{0}/@RequiredSchemaVersion' must be &gt;= {1}, because prior to GSDML version V{1} no pluggable (port)submodules existed.</value>
  </data>
  <data name="M_0x00010120_1" xml:space="preserve">
    <value>No gaps allowed in 'AllowedValues' attribute.</value>
  </data>
  <data name="M_0x00010121_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' supports IsochroneMode, but none of its modules can use IsochroneMode.</value>
  </data>
  <data name="M_0x00010122_1" xml:space="preserve">
    <value>For F-Submodules: For a 'DataType' other than "Unsigned8", "Unsigned16" or "Unsigned32" 'UseAsBits' shall not be set.</value>
  </data>
  <data name="M_0x00010122_2" xml:space="preserve">
    <value>For F-Submodules: The DataType "Unsigned8", "Unsigned16" or "Unsigned32" requires 'UseAsBits' = "true".</value>
  </data>
  <data name="M_0x00010122_3" xml:space="preserve">
    <value>For F-Submodules 'IOData/Input' must be available.</value>
  </data>
  <data name="M_0x00010122_4" xml:space="preserve">
    <value>For F-Submodules 'IOData/Output' must be available.</value>
  </data>
  <data name="M_0x00010122_5" xml:space="preserve">
    <value>For F-Submodules 'IOData' at least one 'DataItem' with 'DataType' equal "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte" must be available for 'Input' and 'Output'.</value>
  </data>
  <data name="M_0x00010122_6" xml:space="preserve">
    <value>For packing boolean channels, it is strongly advised to use only DataItems with DataType="Unsigned8". This warning points to the first occurrence, but it might be other ones under this PROFIsafe submodule.</value>
  </data>
  <data name="M_0x00010122_8" xml:space="preserve">
    <value>For F-Submodules in 'IOData' a message trailer ("F_MessageTrailer4Byte" or "F_MessageTrailer5Byte") must be defined for 'Input' and 'Output'.</value>
  </data>
  <data name="M_0x00010122_9" xml:space="preserve">
    <value>The same message trailer must be used for 'IOData/Input' and 'IOData/Output'.</value>
  </data>
  <data name="M_0x00010122_b" xml:space="preserve">
    <value>The given 'Length' = {0} doesn't match with 'DataType' = "{1}".</value>
  </data>
  <data name="M_0x00010122_c" xml:space="preserve">
    <value>For F-Submodules 'IOData' the message trailer bytes for 'Input' ("{0}") and 'Output' ("{1}") must be identical.</value>
  </data>
  <data name="M_0x00010123_1" xml:space="preserve">
    <value>For F-Submodules 'Consistency'="All items consistency" must be given for 'Input'.</value>
  </data>
  <data name="M_0x00010124_1" xml:space="preserve">
    <value>For F-Submodules 'Consistency'="All items consistency" must be given for 'Output'.</value>
  </data>
  <data name="M_0x00010125_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length': "2-Byte-CRC" is allowed in V1-mode only, which is not applicable with PROFINET and must not be used for 'DefaultValue'.</value>
  </data>
  <data name="M_0x00010125_2" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length': "2-Byte-CRC" is allowed in V1-mode only, which is not applicable with PROFINET and must not be used for 'AllowedValues'.</value>
  </data>
  <data name="M_0x00010125_3" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length': The default value (= {0}) is not contained in allowed values (= {1}).</value>
  </data>
  <data name="M_0x00010125_4" xml:space="preserve">
    <value>"4-Byte-CRC" must be used when using 'Input' or 'Output' data with more than {0} bytes.</value>
  </data>
  <data name="M_0x00010125_5" xml:space="preserve">
    <value>"F_MessageTrailer5Byte" is necessary when using a "4-Byte-CRC" as default value.</value>
  </data>
  <data name="M_0x00010125_6" xml:space="preserve">
    <value>"F_MessageTrailer4Byte" is necessary when using a "3-Byte-CRC" as default value.</value>
  </data>
  <data name="M_0x00010125_7" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue': "4-Byte-CRC" is mandatory for PROFIsafe V2.6.1.</value>
  </data>
  <data name="M_0x00010125_8" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length/@AllowedValues': "4-Byte-CRC" is mandatory for PROFIsafe V2.6.1.</value>
  </data>
  <data name="M_0x00010125_9" xml:space="preserve">
    <value>For "4-Byte-CRC" a maximum of {0} bytes 'Input' or 'Output' is defined. You use {1} bytes.</value>
  </data>
  <data name="M_0x00010125_a" xml:space="preserve">
    <value>For "4-Byte-CRC" {0} bytes 'Input' or 'Output' are guaranteed. You use {1} bytes. This may not work with all PROFIsafe hosts.</value>
  </data>
  <data name="M_0x00010125_b" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue': "4-Byte-CRC" should not be used for PROFIsafe V2.4.</value>
  </data>
  <data name="M_0x00010126_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_SIL': The default value (= {0}) is not contained in allowed values (= {0}).</value>
  </data>
  <data name="M_0x00010127_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_WD_Time': The default value (= {0}) is not contained in allowed values (= {1}..{2}).</value>
  </data>
  <data name="M_0x00010128_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/@F_Par_CRC': The CRC over all default values is {0}, but should be {1}.</value>
  </data>
  <data name="M_0x00010128_2" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/@F_ParamDescCRC': The CRC over all F-parameters is {0}, but should be {1}.</value>
  </data>
  <data name="M_0x00010129_1" xml:space="preserve">
    <value>"'(Virtual)SubmoduleItem/IOData/@F_IO_StructureDescCRC': The CRC over all IOData is {0}, but should be {1}.</value>
  </data>
  <data name="M_0x00010131_1" xml:space="preserve">
    <value>For F-Submodules, the 'Input/DataItem' elements have to be ordered according to their 'DataType'.</value>
  </data>
  <data name="M_0x00010131_2" xml:space="preserve">
    <value>For F-Submodules, the 'Output/DataItem' elements have to be ordered according to their 'DataType'.</value>
  </data>
  <data name="M_0x00010131_3" xml:space="preserve">
    <value>The PROFIsafe specification requires hosts to support at least 64 boolean input channels. This submodule exceeds this number and thus may not work with all PROFIsafe hosts.</value>
  </data>
  <data name="M_0x00010131_4" xml:space="preserve">
    <value>The PROFIsafe specification requires hosts to support at least 64 boolean output channels. This submodule exceeds this number and thus may not work with all PROFIsafe hosts.</value>
  </data>
  <data name="M_0x00010131_5" xml:space="preserve">
    <value>For F-Submodules in 'IOData' a 'DataItem' with 'DataType' equal "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte" must be the last one, and not "{0}".</value>
  </data>
  <data name="M_0x00010131_6" xml:space="preserve">
    <value>For F-Submodules in 'IOData' the 'DataType' "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte" must not be double defined.</value>
  </data>
  <data name="M_0x00010131_7" xml:space="preserve">
    <value>The 'DataType' "{0}" is not allowed for PROFIsafe.</value>
  </data>
  <data name="M_0x00010131_8" xml:space="preserve">
    <value>For F-Submodules, the list of boolean channels on 'Input/DataItem' elements with DataType="UnsignedXX" and UseAsBits="true", may not have holes in it.</value>
  </data>
  <data name="M_0x00010131_9" xml:space="preserve">
    <value>For F-Submodules, the list of boolean channels on 'Output/DataItem' elements with DataType="UnsignedXX" and UseAsBits="true", may not have holes in it.</value>
  </data>
  <data name="M_0x00010132_1" xml:space="preserve">
    <value>'Visible'="false" does not only hide the F-parameter '{0}', but also sets its value to "{1}", which is not the same as the DefaultValue. This is probably not intended.</value>
  </data>
  <data name="M_0x00010133_1" xml:space="preserve">
    <value>If 'SupportedRT_Class' is "Class3" and/or 'SupportedRT_Classes' contains "RT_CLASS_3", the modules pluggable with this DAP (ID = "{0}") shall not carry PortSubmodules.</value>
  </data>
  <data name="M_0x00010134_1" xml:space="preserve">
    <value>For a 'DataType' other than "Unsigned8", "Unsigned16" or "Unsigned32" or "Unsigned64" or "OctetString" or "V2" 'UseAsBits' shall not be set.</value>
  </data>
  <data name="M_0x00010135_1" xml:space="preserve">
    <value>No PortSubmoduleItem is configurable with this DAP.</value>
  </data>
  <data name="M_0x00011101_1" xml:space="preserve">
    <value>'F_Block_ID' with 'DefaultValue' 1, 3, 5 or 7 but no 'F_iPar_CRC'.</value>
  </data>
  <data name="M_0x00011104_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' must have either a 'VirtualSubmoduleList', a 'SystemDefinedSubmoduleList' or a 'UseableSubmodules' list.</value>
  </data>
  <data name="M_0x00011104_2" xml:space="preserve">
    <value>The 'ModuleItem' must have either a 'VirtualSubmoduleList', a 'SystemDefinedSubmoduleList' or a 'UseableSubmodules' list.</value>
  </data>
  <data name="M_0x00011106_1" xml:space="preserve">
    <value>The 'DeviceAccessPointItem' or 'ModuleItem' has an 'UseableSubmodules' element but there are no 'PhysicalSubslots' defined.</value>
  </data>
  <data name="M_0x00011106_4" xml:space="preserve">
    <value>Physical subslot {0} is also used for 'VirtualSubmoduleItem'.</value>
  </data>
  <data name="M_0x00011106_5" xml:space="preserve">
    <value>Duplicate 'SubmoduleItemRef' elements found (it means, they have the same target).</value>
  </data>
  <data name="M_0x00011106_6" xml:space="preserve">
    <value>Subslots defined in '{0}' aren't available in '{1}'.</value>
  </data>
  <data name="M_0x00011106_7" xml:space="preserve">
    <value>Subslots defined in '{0}' must not be available in '{1}'.</value>
  </data>
  <data name="M_0x00011106_8" xml:space="preserve">
    <value>Physical subslot {0} is also used for 'PortSubmoduleItem'.</value>
  </data>
  <data name="M_0x00011106_9" xml:space="preserve">
    <value>Physical subslot {0} is also used for 'InterfaceSubmoduleItem'.</value>
  </data>
  <data name="M_0x00011107_1" xml:space="preserve">
    <value>The 'SubmoduleItem/@ID' must be unique for all 'SubmoduleItem' elements.</value>
  </data>
  <data name="M_0x00011107_2" xml:space="preserve">
    <value>The 'SubmoduleItemRef/@SubmoduleItemTarget' attribute must reference an existing submodule 'ID'.</value>
  </data>
  <data name="M_0x00011108_1" xml:space="preserve">
    <value>Invalid value in 'Writeable_IM_Records'.</value>
  </data>
  <data name="M_0x00011108_2" xml:space="preserve">
    <value>'Writeable_IM_Records' must not contain 5.</value>
  </data>
  <data name="M_0x00011109_1" xml:space="preserve">
    <value>The mandatory value "RT_CLASS_1" is missing.</value>
  </data>
  <data name="M_0x00011109_2" xml:space="preserve">
    <value>Supporting "RT_CLASS_2" without "RT_CLASS_3" is not recommended.</value>
  </data>
  <data name="M_0x00011109_3" xml:space="preserve">
    <value>Mismatch between attributes 'SupportedRT_Class' and 'SupportedRT_Classes'.</value>
  </data>
  <data name="M_0x0001119A_1" xml:space="preserve">
    <value>If the attribute 'FiberOpticTypes' is not given, attribute 'PowerBudgetControlSupported' shall not be present.</value>
  </data>
  <data name="M_0x0001119B_1" xml:space="preserve">
    <value>'DataItem/@DataType'="{0}" is not allowed, because 'DataItem/@Length' not available. 'DataItem/@Length' is available from GSDML V2.2 on.</value>
  </data>
  <data name="M_0x0001119B_2" xml:space="preserve">
    <value>The attribute 'DataItem/@Length' must be used, if 'DataItem/@DataType'="{0}".</value>
  </data>
  <data name="M_0x0001119B_3" xml:space="preserve">
    <value>The attribute 'DataItem/@Length'(={0}) must match to 'DataItem/@DataType' (length={1}).</value>
  </data>
  <data name="M_0x0001119C_1" xml:space="preserve">
    <value>The sum of all 'DataItem' elements in a 'ExtChannelAddValue' shall describe exactly 4 octets, you have {0} octets.</value>
  </data>
  <data name="M_0x0001119D_2" xml:space="preserve">
    <value>The combination 'ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem' is not allowed from GSDML V2.2 on.</value>
  </data>
  <data name="M_0x0001119E_1" xml:space="preserve">
    <value>In the text for text id "{0}" the used id (= {1}) is not defined as an id in the corresponding data items.</value>
  </data>
  <data name="M_0x0001119E_2" xml:space="preserve">
    <value>In the text for text id "{0}" the format "({1}:{2})" does not match to the data type "{3}".</value>
  </data>
  <data name="M_0x000111A0_1" xml:space="preserve">
    <value>The DAP (ID = "{0}") has no fixed Submodule where 'Writeable_IM_Records' contains the values 1-3.</value>
  </data>
  <data name="M_0x000111A1_1" xml:space="preserve">
    <value>Double defined tokens in token list.</value>
  </data>
  <data name="M_0x000111A1_2" xml:space="preserve">
    <value>The token "{0}" is not a known token for attribute '{1}' in this GSDML version.</value>
  </data>
  <data name="M_0x000111A1_3" xml:space="preserve">
    <value>No known token in token list.</value>
  </data>
  <data name="M_0x000111A2_1" xml:space="preserve">
    <value>The attribute 'ProfileExtChannelDiagItem/@API' must have the same value as the attribute 'API' of the overlying 'ProfileChannelDiagItem'.</value>
  </data>
  <data name="M_0x000111A3_1" xml:space="preserve">
    <value>The 'API' must be greater than 0.</value>
  </data>
  <data name="M_0x000111A4_1" xml:space="preserve">
    <value>No known value in value list.</value>
  </data>
  <data name="M_0x000111A4_2" xml:space="preserve">
    <value>The value {0} is not a known value for attribute '{1}' in this GSDML version.</value>
  </data>
  <data name="M_0x000111A5_1" xml:space="preserve">
    <value>The MAU type in attribute 'MAUType' should also be available in attribute 'MAUTypes'.</value>
  </data>
  <data name="M_0x000111A6_1" xml:space="preserve">
    <value>Subslots defined in 'VirtualSubmoduleList/VirtualSubmoduleItem/{0}' must not be available in '{1}'.</value>
  </data>
  <data name="M_0x000111A6_2" xml:space="preserve">
    <value>If only one VirtualSubmoduleItem is given, the default value for FixedInSubslots is 1. Thus 1 must not be available in '{0}'.</value>
  </data>
  <data name="M_0x000111A6_3" xml:space="preserve">
    <value>Subslots defined in 'SystemDefinedSubmoduleList/*SubmoduleItem/{0}' must not be available in '{1}'</value>
  </data>
  <data name="M_0x000111A6_4" xml:space="preserve">
    <value>If for 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem' the attribute SubslotNumber is missing, the default value is 32768. Thus 32768 must not be available in '{0}'.</value>
  </data>
  <data name="M_0x000111A7_1" xml:space="preserve">
    <value>'ChannelDiagList/(Profile)ChannelDiagItem/@MaintenanceAlarmState' = "QD" is only allowed with extended channel diagnosis.</value>
  </data>
  <data name="M_0x00012101_1" xml:space="preserve">
    <value>The value of 'F_IO_StructureDescCRC' requires a 'F_IO_StructureDescVersion' with a value greater or equal than 2.</value>
  </data>
  <data name="M_0x00012104_1" xml:space="preserve">
    <value>Value contained within the 'DeviceAccessPointItem/@MaxSupportedRecordSize' attribute must be at least {0}.</value>
  </data>
  <data name="M_0x00012105_1" xml:space="preserve">
    <value>Attribute 'InterfaceSubmoduleItem/@SupportedRT_Classes' is not present, while using 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'.</value>
  </data>
  <data name="M_0x00012105_2" xml:space="preserve">
    <value>Class type "{0}" contained within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' attribute is not supported. Each class shall be separated by a semicolon. One or more of the following class types can be used: "RT_CLASS_2" and or "RT_CLASS_3".</value>
  </data>
  <data name="M_0x00012105_3" xml:space="preserve">
    <value>Class type within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' attribute is not supported in 'InterfaceSubmoduleItem/@SupportedRT_Classes'.</value>
  </data>
  <data name="M_0x00012105_4" xml:space="preserve">
    <value>Class type within the 'InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' attribute is not supported in 'InterfaceSubmoduleItem/@SupportedRT_Class'.</value>
  </data>
  <data name="M_0x00012107_1" xml:space="preserve">
    <value>A 'DataItem' with the 'DataType' "Integer32" or "Unsigned8+Unsigned8" is used. That requires a 'F_IO_StructureDescVersion' with a value greater or equal than 2.</value>
  </data>
  <data name="M_0x00012108_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Block_ID': The default value (= {0}) is not contained in allowed values (= {1}..{2}).</value>
  </data>
  <data name="M_0x00012108_2" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Block_ID': If none of the values 1, 3, 5 or 7 is contained in 'AllowedValues', 'F_iPar_CRC' must not be given.</value>
  </data>
  <data name="M_0x00012109_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Check_iPar': The default value (= {0}) is not contained in allowed values (={1}).</value>
  </data>
  <data name="M_0x00012110_1" xml:space="preserve">
    <value>F_ParameterRecordDataItem/F_Source_Add: The default value (= {0}) is not contained in allowed values (= {1}..{2}).</value>
  </data>
  <data name="M_0x00012111_1" xml:space="preserve">
    <value>F_ParameterRecordDataItem/F_Dest_Add: The default value (= {0}) is not contained in allowed values (= {1}..{2}).</value>
  </data>
  <data name="M_0x00012112_1" xml:space="preserve">
    <value>F_ParameterRecordDataItem/F_Par_CRC: The default value (= {0}) is not contained in allowed values (= {1}..{2}).</value>
  </data>
  <data name="M_0x00012113_1" xml:space="preserve">
    <value>When 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", 'PowerOnToCommReady' on the DAP must be &gt;0.</value>
  </data>
  <data name="M_0x00012113_2" xml:space="preserve">
    <value>FSU (Fast Startup) can only work if autonegotiation can be turned off. That means, when 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", for all 'PortSubmoduleItem' elements pluggable on the DAP (ID = "{0}") 'MAUTypes' must be given, so that PDPortDataAdjust.AdjustMAUType is supported and autonegotiation can be switched off.</value>
  </data>
  <data name="M_0x00012113_3" xml:space="preserve">
    <value>FSU (Fast Startup) can only work if autonegotiation can be turned off. That means, when 'InterfaceSubmoduleItem/@DCP_HelloSupported' is available and "true", for all 'PortSubmoduleItem' elements pluggable on the DAP (ID = "{0}") at least one of the MAUTypeItem elements shall have the attribute 'AdjustSupported' present and "true" so that autonegotiation can be switched off.</value>
  </data>
  <data name="M_0x00012114_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Par_Version/@DefaultValue' must be 1 for v2-mode (PROFINET).</value>
  </data>
  <data name="M_0x00012114_2" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Par_Version/@AllowedValues' must be 1 for v2-mode (PROFINET).</value>
  </data>
  <data name="M_0x00012115_1" xml:space="preserve">
    <value>Values for 'F_Block_ID/@AllowedValues' attribute must be in the range of 0 to 7.</value>
  </data>
  <data name="M_0x0001211D_1" xml:space="preserve">
    <value>The attribute 'F_ParamDescCRC' is missing.</value>
  </data>
  <data name="M_0x00012202_1" xml:space="preserve">
    <value>The value of 'T_IO_InputMin' * 'T_IO_Base' must be lower or equal than 'T_DC_Max' * 'T_DC_Base' * 31250.</value>
  </data>
  <data name="M_0x00012203_1" xml:space="preserve">
    <value>The value of 'T_IO_OutputMin' * 'T_IO_Base' must be lower or equal than 'T_DC_Max' * 'T_DC_Base' * 31250.</value>
  </data>
  <data name="M_0x00012204_1" xml:space="preserve">
    <value>If the attribute 'ModuleItem/@FieldbusType' is present, 'VirtualSubmoduleItem/SlotCluster' must not be given.</value>
  </data>
  <data name="M_0x00012204_2" xml:space="preserve">
    <value>If the attribute 'ModuleItem/@FieldbusType' is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget must not reference a SubmoduleItem which works as a FAP ('@API' = 17920).</value>
  </data>
  <data name="M_0x00012204_3" xml:space="preserve">
    <value>If the attribute 'ModuleItem/@FieldbusType' is present, 'SystemDefinedSubmoduleList' must not be given.</value>
  </data>
  <data name="M_0x00012204_4" xml:space="preserve">
    <value>If the attribute 'ModuleItem/@FieldbusType' is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget must not reference a PortSubmoduleItem.</value>
  </data>
  <data name="M_0x00012205_1" xml:space="preserve">
    <value>If the attribute 'DeviceAccessPointItem/@SharedInputSupported' is present and "true", 'DeviceAccessPointItem/@SharedDeviceSupported' must be present and "true".</value>
  </data>
  <data name="M_0x00012206_1" xml:space="preserve">
    <value>When a FAP using slot cluster can be operated with the DAP, the element 'DeviceAccessPointItem/FieldbusIntegrationSlots must be given'.</value>
  </data>
  <data name="M_0x00012206_2" xml:space="preserve">
    <value>The attribute 'FieldbusIntegrationSlots/@MaxSupported' must be greater or equal 'SlotCluster/@Count' of every operable FAP.</value>
  </data>
  <data name="M_0x00012206_3" xml:space="preserve">
    <value>The attribute 'FieldbusIntegrationSlots/@Range' must be a correct range x..y with x&lt;y.</value>
  </data>
  <data name="M_0x00012206_4" xml:space="preserve">
    <value>The attribute 'FieldbusIntegrationSlots/@Range' must be in the range 0..32767.</value>
  </data>
  <data name="M_0x00012206_5" xml:space="preserve">
    <value>Values of attribute 'FieldbusIntegrationSlots/@Range' overlap with 'DeviceAccessPointItem/@PhysicalSlots'.</value>
  </data>
  <data name="M_0x00012206_6" xml:space="preserve">
    <value>The width of 'FieldbusIntegrationSlots/@Range' is lower than 'FieldbusIntegrationSlots/@MaxSupported'.</value>
  </data>
  <data name="M_0x00012206_7" xml:space="preserve">
    <value>For FBI module with field bus type (= {0}) no matching FAP is available.</value>
  </data>
  <data name="M_0x00012207_1" xml:space="preserve">
    <value>The element 'SlotCluster' may only appear on a FAP (detected by API = 17920), but not on other submodules.</value>
  </data>
  <data name="M_0x00012208_1" xml:space="preserve">
    <value>The attribute 'SupportedSubstitutionModes' must only be present if the '(Virtual)SubmoduleItem/IOData/Output' is given.</value>
  </data>
  <data name="M_0x00012209_1" xml:space="preserve">
    <value>The attribute ParameterizationDisallowed on DAP has not the same value as on PortSubmoduleItem which can be configured with this DAP.</value>
  </data>
  <data name="M_0x0001220A_1" xml:space="preserve">
    <value>@FieldbusType = "0" is not allowed, because the value "0" corresponds to API "0x4600" which is reserved for the FAP.</value>
  </data>
  <data name="M_0x0001220B_1" xml:space="preserve">
    <value>'SlotCluster/@Count' = "0" is not allowed, because the value "0" would mean, that no Slots are required for the FAP.</value>
  </data>
  <data name="M_0x00020002_1" xml:space="preserve">
    <value>The attribute 'SupportedRT_Classes' is necessary if the attribute 'StartupMode' contains the token "Advanced".</value>
  </data>
  <data name="M_0x00020002_2" xml:space="preserve">
    <value>FrameID range 7 is only allowed for legacy devices.</value>
  </data>
  <data name="M_0x00020003_1" xml:space="preserve">
    <value>'DeviceAccessPointItem' supports 'PNIO_Version' &gt;= "V2.31" but does not support 'StartupMode' "Advanced" for "RT_CLASS_1".</value>
  </data>
  <data name="M_0x00020003_2" xml:space="preserve">
    <value>'DeviceAccessPointItem' supports 'SystemRedundancy' but does not support 'StartupMode' "Advanced" for "RT_CLASS_1".</value>
  </data>
  <data name="M_0x00020003_3" xml:space="preserve">
    <value>'DeviceAccessPointItem' supports CiR but does not support 'StartupMode' "Advanced" for "RT_CLASS_1".</value>
  </data>
  <data name="M_0x00020003_4" xml:space="preserve">
    <value>If "RT_CLASS_2" is supported, the attribute 'ApplicationRelations/@StartupMode' shall be either missing or contain the token "Legacy".</value>
  </data>
  <data name="M_0x00020008_1" xml:space="preserve">
    <value>When using menus, all 'Ref' elements must have an 'ID'.</value>
  </data>
  <data name="M_0x00020008_2" xml:space="preserve">
    <value>The 'Name/@TextId' of the very first 'MenuItem' (= "{0}") in the 'MenuList' is different to the 'Name/@TextId' of the parameter record (= "{1}").</value>
  </data>
  <data name="M_0x00020008_3" xml:space="preserve">
    <value>The parameter "{0}" is referenced more than once.</value>
  </data>
  <data name="M_0x00020008_4" xml:space="preserve">
    <value>The menu item "{0}" is referenced more than once.</value>
  </data>
  <data name="M_0x00020008_5" xml:space="preserve">
    <value>The parameter "{0}" is not referenced.</value>
  </data>
  <data name="M_0x00020008_6" xml:space="preserve">
    <value>The menu item "{0}" is not referenced.</value>
  </data>
  <data name="M_0x00020008_7" xml:space="preserve">
    <value>The menu hierarchy has exceeded the nesting level of three (root menu -&gt; menu -&gt; submenu -&gt; sub-submenu).</value>
  </data>
  <data name="M_0x00020010_1" xml:space="preserve">
    <value>For 'SystemRedundancy/@DeviceType'="R2" (or "R1" from V2.32 on) 'SystemRedundancy/@PrimaryAR_OnBothNAPsSupported' must be given.</value>
  </data>
  <data name="M_0x00020010_2" xml:space="preserve">
    <value>If 'SystemRedundancy/@PrimaryAR_OnBothNAPsSupported' is given 'SystemRedundancy/@DeviceType' must be equal "R2" (or "R1" from V2.32 on).</value>
  </data>
  <data name="M_0x00020010_3" xml:space="preserve">
    <value>For 'SystemRedundancy/@DeviceType'="R2" (or "R1" from V2.32 on) 'FixedInSlots' must contain one or two slots.</value>
  </data>
  <data name="M_0x00020010_4" xml:space="preserve">
    <value>If for 'SystemRedundancy/@DeviceType'="R2" (or "R1" from V2.32 on), 'AllowedInSlots' must be available and contain two slots.</value>
  </data>
  <data name="M_0x00020010_5" xml:space="preserve">
    <value>If for 'SystemRedundancy/@DeviceType'="R2" (or "R1" from V2.32 on) 'FixedInSlots' contains one slot, 'AllowedInSlots' must be available and contain the slot from 'FixedInSlots' plus one higher slot number.</value>
  </data>
  <data name="M_0x00020010_6" xml:space="preserve">
    <value>If for 'SystemRedundancy/@DeviceType'="R2" (or "R1" from V2.32 on) 'FixedInSlots' contains two slots, 'AllowedInSlots' must be available and contain those two slots.</value>
  </data>
  <data name="M_0x00020012_1" xml:space="preserve">
    <value>The maximum application data length is greater than the sum of maximum application data input and output length.</value>
  </data>
  <data name="M_0x00020013_1" xml:space="preserve">
    <value>The maximum application data length is lower than the highest value of maximum application data input or output length.</value>
  </data>
  <data name="M_0x00020014_1" xml:space="preserve">
    <value>For Status = {0} LogBookEntryItem/ErrorCode2Value must be given.</value>
  </data>
  <data name="M_0x00020014_2" xml:space="preserve">
    <value>For Status = {0} LogBookEntryItem/ErrorCode2List must be given.</value>
  </data>
  <data name="M_0x00020014_3" xml:space="preserve">
    <value>For Status = {0} ErrorCode2 must be in the range 0xC9..0xFF. ErrorCode2 = {1} is not valid here.</value>
  </data>
  <data name="M_0x00020014_4" xml:space="preserve">
    <value>The value of Status = {0} is not valid according to table "Vendor specific PNIOStatus" of the GSDML Specification.</value>
  </data>
  <data name="M_0x00020016_1" xml:space="preserve">
    <value>When "F_iPar_CRC" is defined as token in 'F_SupportedParameters', 'F_iPar_CRC' shall be present.</value>
  </data>
  <data name="M_0x00020016_2" xml:space="preserve">
    <value>When 'F_SupportedParameters' is given, 'F_iPar_CRC' shall only be present when it is defined as token in 'F_SupportedParameters'.</value>
  </data>
  <data name="M_0x00020017_1" xml:space="preserve">
    <value>SendClock = "{0}" cannot be used, because the product of SendClock and highest value of 'ReductionRatio' applicable (from attribute 'ReductionRatio', or from attribute 'ReductionRatioPow2'/'ReductionRatioNonPow2') is less than MinDeviceInterval.</value>
  </data>
  <data name="M_0x00020018_1" xml:space="preserve">
    <value>The PNIO version is higher than the GSDML version of the GSD file.</value>
  </data>
  <data name="M_0x00020020_0" xml:space="preserve">
    <value>For 'PNIO_Version' &gt;= "V2.31" the element 'CertificationInfo' is mandatory at the DAP.</value>
  </data>
  <data name="M_0x00020020_10" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@PTP_BoundarySupported' shall be present and "true" if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_11" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@DCP_BoundarySupported' shall be present and "true" if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_12" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@MulticastBoundarySupported' shall be missing or "false" if 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A".</value>
  </data>
  <data name="M_0x00020020_13" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@MulticastBoundarySupported' shall be present and "true" if 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "B" or "C" and if 'NumberOfAdditionalMulticastProviderCR' &gt; 0 or 'NumberOfMulticastConsumerCR' &gt; 0.</value>
  </data>
  <data name="M_0x00020020_15" xml:space="preserve">
    <value>The element 'InterfaceSubmoduleItem/MediaRedundancy' shall be present if 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "ProcessAutomation".</value>
  </data>
  <data name="M_0x00020020_16" xml:space="preserve">
    <value>There shall be at least one combination of 'SendClock' and 'ReductionRatio' / 'ReductionRatioPow2' / 'ReductionRatioNonPow2' that exactly equals the value of 'MinDeviceInterval'.</value>
  </data>
  <data name="M_0x00020020_3" xml:space="preserve">
    <value>The attribute 'MultipleWriteSupported' shall be present and "true" at the DAP if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_4" xml:space="preserve">
    <value>If the attribute 'RemoteApplicationTimeout' is present its value shall be &lt;= 300 at the DAP if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_5" xml:space="preserve">
    <value>The attribute 'LLDP_NoD_Supported' shall be present and "true" at the DAP if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_6" xml:space="preserve">
    <value>The attribute 'ResetToFactoryModes' shall be present and contain the value 2 at the DAP if the attribute 'PNIO_Version' is &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020020_7" xml:space="preserve">
    <value>The element 'SystemRedundancy' shall be present at the DAP when 'PNIO_Version' &gt;= "V2.31" and 'CertificationInfo/@ApplicationClass' contains "ProcessAutomation".</value>
  </data>
  <data name="M_0x00020020_8" xml:space="preserve">
    <value>If present at the DAP, the attribute 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class' shall be set to "Class3" when 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31" and 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C".</value>
  </data>
  <data name="M_0x00020020_9" xml:space="preserve">
    <value>If present at the DAP, the attribute 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class' shall be set to "Class1" when 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31" and 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" or "B".</value>
  </data>
  <data name="M_0x00020020_a" xml:space="preserve">
    <value>If 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes' shall be present.</value>
  </data>
  <data name="M_0x00020020_b" xml:space="preserve">
    <value>If 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes' shall contain "RT_CLASS_1" except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C" and some submodule which is fixed plugged with this DAP has the attribute 'IsochroneModeRequired' = "true".</value>
  </data>
  <data name="M_0x00020020_c" xml:space="preserve">
    <value>If 'PNIO_Version' &gt;= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes' shall contain "RT_CLASS_3" when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "C".</value>
  </data>
  <data name="M_0x00020020_d" xml:space="preserve">
    <value>If 'PNIO_Version' &gt;= "V2.31", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes' shall not contain "RT_CLASS_2" or "RT_CLASS_3" when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" or "B".</value>
  </data>
  <data name="M_0x00020020_e" xml:space="preserve">
    <value>If 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains the token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported' shall be "true" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' shall contain "RT_CLASS_3".</value>
  </data>
  <data name="M_0x00020020_f" xml:space="preserve">
    <value>If 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' does not contain the token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported' shall be "false" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes' shall either be missing or contain at most "RT_CLASS_2".</value>
  </data>
  <data name="M_0x00020021_1" xml:space="preserve">
    <value>The 'PNIO_Version' attribute references a non-existing GSDML version.</value>
  </data>
  <data name="M_0x00020021_2" xml:space="preserve">
    <value>The attribute 'NetloadClass' is mandatory at 'DeviceAccessPointItem/CertificationInfo' for 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020021_3" xml:space="preserve">
    <value>When 'PNIO_Version' &gt;= "V2.31", for 'ApplicationClass' the tokens "Isochronous" and "HighPerformance" are only allowed if 'ConformanceClass' is "C".</value>
  </data>
  <data name="M_0x00020021_4" xml:space="preserve">
    <value>The 'ApplicationClass' values "ProcessAutomation" and "HighPerformance" require 'PNIO_Version' &gt;= "V2.3".</value>
  </data>
  <data name="M_0x00020022_1" xml:space="preserve">
    <value>The 'SystemDefinedSubmoduleList' element shall only be present at the DAP if the attribute 'PNIO_Version' &gt;= "V2.0".</value>
  </data>
  <data name="M_0x00020023_1" xml:space="preserve">
    <value>The 'ApplicationRelations' element shall only be present at the DAP if the attribute 'PNIO_Version' = "V1.0".</value>
  </data>
  <data name="M_0x00020024_1" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@ForwardingMode' is mandatory if 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31".</value>
  </data>
  <data name="M_0x00020024_2" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if 'DeviceAccessPointItem/@PNIO_Version' &gt;= "V2.31" and "RT_CLASS_3" is supported.</value>
  </data>
  <data name="M_0x00020024_3" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if fast forwarding is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is present.</value>
  </data>
  <data name="M_0x00020024_4" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if small send clock factors are supported: 'InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@SendClock' is present and contains at least one value less than 8.</value>
  </data>
  <data name="M_0x00020024_5" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if fragmentation is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType' is present.</value>
  </data>
  <data name="M_0x00020024_6" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if dynamic frame packing is supported: 'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is present and &gt; 0.</value>
  </data>
  <data name="M_0x00020024_7" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if MRPD is supported: 'InterfaceSubmoduleItem/MediaRedundancy/@MRPD_Supported' is present and "true".</value>
  </data>
  <data name="M_0x00020024_8" xml:space="preserve">
    <value>The attribute 'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" if Short preamble is supported: 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is present and "true" on at least one port submodule which is configurable with this DAP.</value>
  </data>
  <data name="M_0x00020025_1" xml:space="preserve">
    <value>When 'PNIO_Version' &gt;= "V2.31" and 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' = "{0}" is supported, 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "SNMP".</value>
  </data>
  <data name="M_0x00020025_2" xml:space="preserve">
    <value>When 'PNIO_Version' &gt;= "V2.31", 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "LLDP" except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" and all port submodules which can be configured with this DAP have 'MAUTypes' = 0 (wireless).</value>
  </data>
  <data name="M_0x00020025_3" xml:space="preserve">
    <value>When 'PNIO_Version' &gt;= "V2.31", 'InterfaceSubmoduleItem/@SupportedProtocols' shall contain the value "LLDP" except when 'DeviceAccessPointItem/CertificationInfo/@ConformanceClass' is "A" and all port submodules which can be configured with this DAP only have a single element MAUTypeList/MAUTypeItem and this element has attribute Value =0 (wireless/radio communication).</value>
  </data>
  <data name="M_0x00020026_1" xml:space="preserve">
    <value>The 'PortSubmoduleItem' with 'MAUTypes' = 0 is pluggable with 'DeviceAccessPointItem' ('ID' = "{0}") and 'PNIO_Version' &gt;= "V2.31", but 'DeviceAccessPointItem/.../@ConformanceClass' is not "A".</value>
  </data>
  <data name="M_0x00020026_2" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/@MAUTypes' is not present.</value>
  </data>
  <data name="M_0x00020026_3" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/@PortDeactivationSupported' is not present.</value>
  </data>
  <data name="M_0x00020026_4" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/@LinkStateDiagnosisCapability' is not present.</value>
  </data>
  <data name="M_0x00020026_5" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/@CheckMAUTypeSupported' is not present or not "true".</value>
  </data>
  <data name="M_0x00020026_6" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' is not "C", but the attribute 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is "true".</value>
  </data>
  <data name="M_0x00020026_7" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ApplicationClass' "HighPerformance", but the attribute 'PortSubmoduleItem/@ShortPreamble100MBitSupported' is not present or not "true".</value>
  </data>
  <data name="M_0x00020026_8" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is wireless ('MAUTypeList/MAUTypeItem/Value' = 0 and/or '@MAUTypes = 0’) and pluggable with 'DeviceAccessPointItem' ('ID' = "{0}") and 'PNIO_Version' &gt;= "V2.31", but 'DeviceAccessPointItem/.../@ConformanceClass' is not "A".</value>
  </data>
  <data name="M_0x00020026_9" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is pluggable into 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/MAUTypeList/MAUTypeItem/@AdjustSupported' is not present or not "true".</value>
  </data>
  <data name="M_0x00020027_1" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is not present. This attribute shall be present and its value shall be &lt;= 2000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_2" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW' is {0}. This attribute shall be present and its value shall be &lt;= 2000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_3" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is not present. This attribute shall be present and &gt;0 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_4" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames' is {0}. This attribute shall be present and &gt;0 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_5" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType' is not present. This attribute shall be present if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_6" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO' is not present. This attribute shall be present and its value shall be &lt;= 3500 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_7" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO' is {0}. This attribute shall be present and its value shall be &lt;= 3500 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_8" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed' is not present. This attribute shall be present and its value shall be &lt;= 1000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020027_9" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed' is {0}. This attribute shall be present and its value shall be &lt;= 1000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020028_1" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter' is not present. This attribute shall be present and its value shall be &lt;= 250 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020028_2" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter' is {0}. This attribute shall be present and its value shall be &lt;= 250 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020029_1" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock' is not present. This attribute shall be present and shall contain value(s) &lt; 8 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00020029_2" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock' is {0}. This attribute shall be present and shall contain value(s) &lt; 8 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x0002002A_1" xml:space="preserve">
    <value>'DeviceAccessPointItem/SystemRedundancy' is present, but the attribute 'DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported' is not present or not "true".</value>
  </data>
  <data name="M_0x0002002A_2" xml:space="preserve">
    <value>'ModuleItem/SystemDefinedSubmoduleList' is present, but if system redundancy is supported, the modules pluggable with this DAP ('ID' = "{0}") shall not carry 'PortSubmodule' elements.</value>
  </data>
  <data name="M_0x0002002A_3" xml:space="preserve">
    <value>'SubmoduleItemRef/@SubmoduleItemTarget' references a 'PortSubmoduleItem', but if system redundancy is supported, the modules pluggable with this DAP ('ID' = "{0}") shall not carry 'PortSubmodule' elements.</value>
  </data>
  <data name="M_0x0002002A_4" xml:space="preserve">
    <value>If 'SystemRedundancy' is present but 'PrmBeginPrmEndSequenceSupported' is missing or "false" this DAP supports an interim version of system redundancy which is unsupported by most controllers.</value>
  </data>
  <data name="M_0x0002002B_1" xml:space="preserve">
    <value>The attribute 'MaxDFP_Feed' shall only be present if DFP is supported, i.e. 'MaxDFP_Frames' &lt;&gt;0.</value>
  </data>
  <data name="M_0x0002002B_2" xml:space="preserve">
    <value>The attribute 'AlignDFP_Subframes' shall only be present if DFP is supported, i.e. 'MaxDFP_Frames' &lt;&gt;0.</value>
  </data>
  <data name="M_0x0002002C_1" xml:space="preserve">
    <value>'DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported' is present and "true", but the element 'SystemRedundancy' is not present.</value>
  </data>
  <data name="M_0x0002002D_1" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@SupportedRT_Classes' is present and 'MediaRedundancy/@MRPD_Supported' is "true", but the 'SupportedRT_Classes' does not contain "RT_CLASS_3".</value>
  </data>
  <data name="M_0x0002002D_2" xml:space="preserve">
    <value>The attribute 'InterfaceSubmoduleItem/@SupportedRT_Class' is present and legal and 'MediaRedundancy/@MRPD_Supported' is "true", but the 'SupportedRT_Class' is not equal to "Class3".</value>
  </data>
  <data name="M_0x0002002E_1" xml:space="preserve">
    <value>If the attribute 'MediaRedundancy/@MRT_Supported' is present and "true", 'MediaRedundancy/@MRPD_Supported' must be present and "true".</value>
  </data>
  <data name="M_0x00020030_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports RT_CLASS_3, but the attribute 'SupportedSyncProtocols' does not contain the token "PTCP".</value>
  </data>
  <data name="M_0x00020031_1" xml:space="preserve">
    <value>'RT_Class3Properties/@MaxDFP_Frames' ({0}) shall not exceed 'ApplicationRelations/@NumberOfAR' ({1}).</value>
  </data>
  <data name="M_0x00020032_1" xml:space="preserve">
    <value>If the attribute 'DeviceAccessPointItem/@SharedDeviceSupported' is present and "true", 'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present and &gt;1.</value>
  </data>
  <data name="M_0x00020032_2" xml:space="preserve">
    <value>If the element 'DeviceAccessPointItem/SystemRedundancy' is present, 'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present and greater than or equal to the value of the attribute NumberOfAR_Sets, multiplied with the number of ARs which constitute an SR-AR set.</value>
  </data>
  <data name="M_0x00020032_3" xml:space="preserve">
    <value>If the attribute 'DeviceAccessPointItem/@IO_SupervisorSupported' is present and "true", 'InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR' must be present and &gt;1.</value>
  </data>
  <data name="M_0x00030003_1" xml:space="preserve">
    <value>The attribute 'SupportedMultipleRole' must only be present if the device supports more than one MRP-instance (attribute 'MaxMRP_Instances').</value>
  </data>
  <data name="M_0x00030004_1" xml:space="preserve">
    <value>'PDEV_CombinedObjectSupported' shall be set to "true" if DeviceAccessPointItem/@PNIO_Version &gt;= "V2.31" and DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token "ProcessAutomation".</value>
  </data>
  <data name="M_0x00030004_2" xml:space="preserve">
    <value>'PDEV_CombinedObjectSupported' shall be set to "true" if 'DeviceAccessPointItem/@CIR_Supported' is present and "true".</value>
  </data>
  <data name="M_0x00030004_3" xml:space="preserve">
    <value>'PDEV_CombinedObjectSupported' shall be set to "true" if 'DeviceAccessPointItem/SystemRedundancy/@DeviceType' is "R2" (or "R1" from V2.32 on).</value>
  </data>
  <data name="M_0x00030005_1" xml:space="preserve">
    <value>The value 'MaxReductionRatioIsochroneMode' shall be one of the values listed in 'ReductionRatio', 'ReductionRatioPow2' or 'ReductionRatioNonPow2'.</value>
  </data>
  <data name="M_0x00030007_1" xml:space="preserve">
    <value>PROFIsafe V2.6.1 is used but not all mandatory PROFIsafe V2.6.1 elements or attributes are present.</value>
  </data>
  <data name="M_0x00030008_1" xml:space="preserve">
    <value>Submodules with RIOforFA ('Input/DataItem' with 'Subordinate'="true") and PROFIsafe must use PROFIsafe Version 2.6.1. 'F_Passivation' must be set to "Channel".</value>
  </data>
  <data name="M_0x00030008_2" xml:space="preserve">
    <value>'F_Passivation' with value "Channel" is only allowed in combination with RIOforFA ('Input/DataItem' with 'Subordinate'="true").</value>
  </data>
  <data name="M_0x00030009_1" xml:space="preserve">
    <value>'F_ParameterRecordDataItem/F_Passivation': The default value (= {0}) is not contained in allowed values (= {1}).</value>
  </data>
  <data name="M_0x00030011_1" xml:space="preserve">
    <value>Number of Q bits does not match number of IO channels.</value>
  </data>
  <data name="M_0x00030012_1" xml:space="preserve">
    <value>The 'DataType' "{0}" must not be used in combination with RIOforFA.</value>
  </data>
  <data name="M_0x00030012_2" xml:space="preserve">
    <value>Wrong order of 'DataItem' elements used in combination with RIOforFA.</value>
  </data>
  <data name="M_0x00030012_3" xml:space="preserve">
    <value>RIOforFA: For 'IOData/Output' no Qualifier bits must be given.</value>
  </data>
  <data name="M_0x00030013_1" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/@MaxFrameStartTime' is not given. This attribute shall be present and its value shall be &lt;= 3500 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030013_2" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/@MaxFrameStartTime' is {0}. This attribute shall be present and its value shall be &lt;= 3500 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030013_3" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/@MinNRT_Gap' is not given. This attribute shall be present and its value shall be &lt;= 1600 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030013_4" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/@MinNRT_Gap' is {0}. This attribute shall be present and its value shall be &lt;= 1600 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030014_1" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap' is not given. This attribute shall be present and its value shall be &lt;= 1600 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030014_2" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap' is {0}. This attribute shall be present and its value shall be &lt;= 1600 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030014_3" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin' is not given. This attribute shall be present and its value shall be &lt;= 1000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030014_4" xml:space="preserve">
    <value>'InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin' is {0}. This attribute shall be present and its value shall be &lt;= 1000 if 'PNIO_Version' &gt;= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighPerformance".</value>
  </data>
  <data name="M_0x00030015_1" xml:space="preserve">
    <value>The RIOforFA common profile requires BitDataItems for each of the DI, DO and Q bits.</value>
  </data>
  <data name="M_0x00030020_1" xml:space="preserve">
    <value>Referenced texts must not be an empty string or only consist of white spaces (blank, tabulator).</value>
  </data>
  <data name="M_0x0003002A_1" xml:space="preserve">
    <value>The 'InterfaceSubmoduleItem' supports RT_CLASS_3, but the element 'RT_Class3TimingProperties' is not present.</value>
  </data>
  <data name="M_0x0003002A_2" xml:space="preserve">
    <value>The element 'RT_Class3TimingProperties' is present, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_3".</value>
  </data>
  <data name="M_0x0003002B_1" xml:space="preserve">
    <value>If 'API' = 0, 'APStructureIdentifier' must be less than 0x8000 (32768).</value>
  </data>
  <data name="M_0x0003002C_1" xml:space="preserve">
    <value>If the attribute 'RT_Class3Properties/@ForwardingMode' contains the token "Absolute" and the attribute 'PNIO_Version' is &gt;= "V2.31", the value of the attribute 'RT_Class3Properties/@MaxNumberIR_FrameData' shall be &gt;= 128.</value>
  </data>
  <data name="M_0x0003002D_1" xml:space="preserve">
    <value>A DAP with 'ApplicationClass' "FunctionalSafety" must have at least one PROFIsafe submodule.</value>
  </data>
  <data name="M_0x0003002D_2" xml:space="preserve">
    <value>The submodule ('ID' = "{0}") with 'PROFIsafeSupported' = "true" is pluggable in a DAP with ApplicationClass "FunctionalSafety". Here I&amp;M4 must not be writable.</value>
  </data>
  <data name="M_0x0003002E_1" xml:space="preserve">
    <value>'ApplicationRelations/@StartupMode' shall be present and contain the token "Advanced" when PDEV_CombinedObjectSupported is present and "true".</value>
  </data>
  <data name="M_0x0003002E_2" xml:space="preserve">
    <value>'RT_Class3Properties/@StartupMode' shall be present and contain the token "Advanced" when PDEV_CombinedObjectSupported is present and "true".</value>
  </data>
  <data name="M_0x0003002F_1" xml:space="preserve">
    <value>For RIOforFA 'Consistency'="All items consistency" must be given for Input.</value>
  </data>
  <data name="M_0x0003002F_2" xml:space="preserve">
    <value>For RIOforFA 'Consistency'="All items consistency" must be given for Output.</value>
  </data>
  <data name="M_0x00030030_1" xml:space="preserve">
    <value>The ErrorType must be unique across SystemDefinedChannelDiagItems and ChannelDiagItems.</value>
  </data>
  <data name="M_0x00030031_1" xml:space="preserve">
    <value>@Subordinate="true" must not appear on 'IOData/Output/DataItem'.</value>
  </data>
  <data name="M_0x00032000_1" xml:space="preserve">
    <value>If 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' is present and contains the token "EnergySaving" the attribute 'PROFIenergyASE_Supported' shall be present and "true" at the DAP.</value>
  </data>
  <data name="M_0x00032000_2" xml:space="preserve">
    <value>If 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' is present and contains the token "EnergySaving" there shall be at least one submodule with element 'PROFIenergy' and attribute 'PESAP_uses_PROFIenergyASE' present and "true" which can be configured with this DAP.</value>
  </data>
  <data name="M_0x00032001_1" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap. 'BitOffset' = {0} is defined twice.</value>
  </data>
  <data name="M_0x00032001_2" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap. The referenced IO data area before must not reach the area starting with 'BitOffset' = {0}.</value>
  </data>
  <data name="M_0x00032001_3" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Data/@BitOffset' and 'Data/@BitLength' overlap. The referenced IO data area after must not be reached by the area starting with 'BitOffset' = {0}.</value>
  </data>
  <data name="M_0x00032001_4" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap. 'BitOffset' = {0} is defined twice.</value>
  </data>
  <data name="M_0x00032001_5" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap. The referenced IO data area before must not reach the area starting with 'BitOffset' = {0}.</value>
  </data>
  <data name="M_0x00032001_6" xml:space="preserve">
    <value>Parts of the IO data referenced by 'Quality/@BitOffset' and 'Quality/@Format' overlap. The referenced IO data area after must not be reached by the area starting with 'BitOffset' = {0}.</value>
  </data>
  <data name="M_0x00032001_7" xml:space="preserve">
    <value>'Channel/Data' or 'Channel/Quality' points outside the available IO data.</value>
  </data>
  <data name="M_0x00032001_8" xml:space="preserve">
    <value>'Channel/Data' or 'Channel/Quality' must point either to a part of one DataItem or to one or more complete DataItems.</value>
  </data>
  <data name="M_0x00032001_9" xml:space="preserve">
    <value>'Channel/Data' or 'Channel/Quality' which is only one bit wide and which is in a 'DataItem' of 'DataType' "UnsignedXX", "OctetString" or "V2" must be described by a 'BitDataItem'.</value>
  </data>
  <data name="M_0x00032001_a" xml:space="preserve">
    <value>All 'DataItem' elements except for those with 'DataType' "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte" must be at least partly used by 'Channel/Data' and/or 'Channel/Quality'.</value>
  </data>
  <data name="M_0x00032001_b" xml:space="preserve">
    <value>All 'DataItem' elements with 'DataType' "F_MessageTrailer4Byte" or "F_MessageTrailer5Byte" must not be referenced by 'Channel/Data' and/or 'Channel/Quality'.</value>
  </data>
  <data name="M_0x00032001_c" xml:space="preserve">
    <value>On 'DataItem' elements with 'BitDataItem' elements, all 'BitDataItem' elements must be used.</value>
  </data>
  <data name="M_0x00032002_2" xml:space="preserve">
    <value>'SystemRedundancy/@{0}' shall only be present if 'RT_InputOnBackupAR_Supported' is present and "true" and 'DeviceType' is "{1}" or "{2}".</value>
  </data>
  <data name="M_0x00032002_3" xml:space="preserve">
    <value>If 'RT_InputOnBackupAR_Supported' is present and "true" 'SystemRedundancy/@{0}' shall also be present if 'DeviceType' is "{1}" or "{2}".</value>
  </data>
  <data name="M_0x00032003_1" xml:space="preserve">
    <value>If 'MAUTypeItem/@Value=0' is present it must be the only one in the 'MAUTypeList'.</value>
  </data>
  <data name="M_0x00032003_2" xml:space="preserve">
    <value>If 'MAUTypeItem/@Value=0' is present the attribute 'AdjustSupported' shall be missing or "false".</value>
  </data>
  <data name="M_0x00032004_1" xml:space="preserve">
    <value>If 'MAUTypeItem/@Extension!=0' is present the attribute 'MAUTypeList/@ExtensionSupported' shall be present and "true".</value>
  </data>
  <data name="M_0x00032005_1" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUTypes' value = {0} no 'MAUTypeItem' with this value in attribute 'Value' found.</value>
  </data>
  <data name="M_0x00032005_2" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUTypes' value = {0} a 'MAUTypeItem' with this value in attribute 'Value' found, but attribute 'Extension' is not missing or set to 0.</value>
  </data>
  <data name="M_0x00032005_3" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUTypes' value = {0} a 'MAUTypeItem' with this value in attribute 'Value' found, but attribute 'AdjustSupported' is not set to "true".</value>
  </data>
  <data name="M_0x00032006_1" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUType' value = {0} no 'MAUTypeItem' with the mapped value in attribute 'Value' found.</value>
  </data>
  <data name="M_0x00032006_2" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUType' value = {0} a 'MAUTypeItem' with the mapped value in attribute 'Value' found, but attribute 'Extension' is not missing or set to 0.</value>
  </data>
  <data name="M_0x00032006_3" xml:space="preserve">
    <value>For 'PortSubmoduleItem/@MAUType' value = {0} a 'MAUTypeItem' with this mapped value in attribute 'Value' found, but attribute 'AdjustSupported' is not missing or set to "false".</value>
  </data>
  <data name="M_0x00032007_1" xml:space="preserve">
    <value>'RT_Class3Properties/@MaxRetentionTime' shall not be present if '@ForwardingMode' only contains the token "Absolute".</value>
  </data>
  <data name="M_0x00032008_1" xml:space="preserve">
    <value>The 'PortSubmoduleItem' is configurable with 'DeviceAccessPointItem' ('ID' = "{0}"), 'PNIO_Version' &gt;= "V2.31" and 'ConformanceClass' "C", but the attribute 'PortSubmoduleItem/@CheckMAUTypeDifferenceSupported' is not present or not "true".</value>
  </data>
  <data name="M_0x00032009_1" xml:space="preserve">
    <value>The length of the string in the attribute DefaultValue according to its DataType does not match to the Length attribute.</value>
  </data>
  <data name="M_0x00032009_2" xml:space="preserve">
    <value>The string in the attribute DefaultValue does not match its DataType.</value>
  </data>
  <data name="M_0x0003200A_1" xml:space="preserve">
    <value>Channel description is mandatory when the submodule follows the RIOforFA profile.</value>
  </data>
  <data name="M_0x00033000_1" xml:space="preserve">
    <value>If "Off" is present at 'MediaRedundancy/@SupportedRole', it must be the first token in the list.</value>
  </data>
  <data name="M_0x00033000_2" xml:space="preserve">
    <value>If "Off" is present at 'MediaRedundancy/@SupportedRole', there must be at least one other token in the list.</value>
  </data>
  <data name="M_0x00033001_1" xml:space="preserve">
    <value>On 'InterfaceSubmoduleItem' the attribute 'SynchronisationMode/@SupportedSyncProtocols' contains the token "PTCP", but the attribute 'DelayMeasurementSupported' is not set to "true".</value>
  </data>
  <data name="M_0x00033002_1" xml:space="preserve">
    <value>'@DeviceAccessSupported' is present and "true" on 'DeviceAccessPointItem', but '@NumberOfDeviceAccessAR' is not present.</value>
  </data>
  <data name="M_0x00033002_2" xml:space="preserve">
    <value>'@NumberOfDeviceAccessAR' is present on 'DeviceAccessPointItem', but '@DeviceAccessSupported' is not "true".</value>
  </data>
  <data name="M_0x00033003_1" xml:space="preserve">
    <value>A DAP with '.../InterfaceSubmoduleItem/ReportingSystem' must have at least one submodule which is configurable with this DAP which contains the 'ReportingSystemEvents'.</value>
  </data>
  <data name="M_0x00033005_1" xml:space="preserve">
    <value>'PROFIenergy/@EntityClass' must be present if '@ProfileVersion' is &gt;= "V1.1".</value>
  </data>
  <data name="M_0x00033005_10" xml:space="preserve">
    <value>'MeasurementItem/@Number' must not be present if '@ProfileVersion' is &lt; "V1.1".</value>
  </data>
  <data name="M_0x00033005_11" xml:space="preserve">
    <value>'MeasurementList' must only contain one 'MeasurementItem' if '@ProfileVersion' is &lt; "V1.1".</value>
  </data>
  <data name="M_0x00033005_2" xml:space="preserve">
    <value>'PROFIenergy/@EntityClass' must not be present if '@ProfileVersion' is &lt; "V1.1".</value>
  </data>
  <data name="M_0x00033005_3" xml:space="preserve">
    <value>'PROFIenergy/@EntitySubclass' must be present if '@ProfileVersion' is &gt;= "V1.2" and '@EntityClass' is "Class1" or "Class3".</value>
  </data>
  <data name="M_0x00033005_4" xml:space="preserve">
    <value>'PROFIenergy/@EntitySubclass' must not be present if '@ProfileVersion' is &lt; "V1.2" or '@EntityClass' is neither "Class1" nor "Class3".</value>
  </data>
  <data name="M_0x00033005_5" xml:space="preserve">
    <value>'PROFIenergy/EnergySavingModeList' must be present if '@EntityClass' is "Class1" or "Class3".</value>
  </data>
  <data name="M_0x00033005_6" xml:space="preserve">
    <value>'PROFIenergy/EnergySavingModeList' must not be present if '@EntityClass' is "Class2".</value>
  </data>
  <data name="M_0x00033005_7" xml:space="preserve">
    <value>'PROFIenergy/MeasurementList' must be present if '@EntityClass' is "Class2" or "Class3".</value>
  </data>
  <data name="M_0x00033005_8" xml:space="preserve">
    <value>'PROFIenergy/MeasurementList' must not be present if '@EntityClass' is "Class1".</value>
  </data>
  <data name="M_0x00033005_9" xml:space="preserve">
    <value>'MeasurementItem/@Number' must be present if '@ProfileVersion' is &gt;= "V1.1".</value>
  </data>
  <data name="M_0x00033006_1" xml:space="preserve">
    <value>The value given for 'EnergySavingModeItem/@ID' is not allowed according to PROFIenergy profile Table 2.</value>
  </data>
  <data name="M_0x00033006_2" xml:space="preserve">
    <value>The value given for 'MeasurementItem/@ID' is not allowed according to PROFIenergy profile Annex A.1.</value>
  </data>
  <data name="M_0x00033006_3" xml:space="preserve">
    <value>The value given for 'MeasurementItem/@AccuracyDomain' is not allowed according to PROFIenergy profile Table 5.</value>
  </data>
  <data name="M_0x00033006_4" xml:space="preserve">
    <value>The value given for 'MeasurementItem/@AccuracyClass' is not allowed according to PROFIenergy profile Table 6-8.</value>
  </data>
  <data name="M_0x00033007_1" xml:space="preserve">
    <value>Channel description on input side is mandatory when 'Observer/@Type' is "DigitalInput".</value>
  </data>
  <data name="M_0x00033007_2" xml:space="preserve">
    <value>Channel description on input side must contain at least one Channel with bit BitLength=1 when 'Observer/@Type' is "DigitalInput".</value>
  </data>
  <data name="M_0x00033008_1" xml:space="preserve">
    <value>"Off" is present at 'MediaRedundancy/@SupportedRole', so the PortSubmoduleItem (ID = '{0}'), which has '@IsDefaultRingport' = "true", must not be configurable with this DAP.</value>
  </data>
  <data name="M_General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="M_InvalidRange" xml:space="preserve">
    <value>The attribute '{0}' = "{1}" contains a range with a minimum value greater than the maximum value.</value>
  </data>
  <data name="M_KeyKeyref" xml:space="preserve">
    <value>KeyKeyref</value>
  </data>
  <data name="M_NumericOverflow" xml:space="preserve">
    <value>The value of the attribute '{0}' = "{1}" cannot be converted into the given data type (too many digits?).</value>
  </data>
  <data name="M_Overlap" xml:space="preserve">
    <value>The attribute '{0}' = "{1}" contains values and/or value ranges which overlap.</value>
  </data>
  <data name="M_PlugRules" xml:space="preserve">
    <value>PlugRules</value>
  </data>
  <data name="M_Signature" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="M_TypeSpecific" xml:space="preserve">
    <value>TypeSpecific</value>
  </data>
  <data name="M_UniqueIds_DAP_1" xml:space="preserve">
    <value>The DeviceAccessPoints (DAPs) {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
Reason: {2}</value>
  </data>
  <data name="M_UniqueIds_MOD_1" xml:space="preserve">
    <value>The DAP {0} and the Module {1} are incompatible and thus need different ModuleIdentNumbers.
The conflict arises e.g. when the Module is plugged with the DAP {2}.
Reason: {3}</value>
  </data>
  <data name="M_UniqueIds_MOD_2" xml:space="preserve">
    <value>The Module {0} and the DAP {1} are incompatible and thus need different ModuleIdentNumbers.
The conflict arises e.g. when the Module is plugged with the DAP {2}.
Reason: {3}</value>
  </data>
  <data name="M_UniqueIds_MOD_3" xml:space="preserve">
    <value>The Modules {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
The conflict arises e.g. when both Modules are plugged with the DAP {2}.
Reason: {3}</value>
  </data>
  <data name="M_UniqueIds_MOD_4" xml:space="preserve">
    <value>The Modules {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
The conflict arises e.g. when the first Module is plugged with the DAP {2} and the second Module is plugged with the DAP {3}.
Reason: {4}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_1" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when both Submodules are plugged in the DAP {4}.
 Reason: {5}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_2" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when both Submodules are plugged in the Module {4}.
 Reason: {5}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_3" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when the first Submodule is plugged in the DAP {4} and the second Submodule is plugged in the DAP {5}.
Reason: {6}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_4" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when the first Submodule is plugged in the DAP {4} and the second Submodule is plugged in the Module {5} which is plugged with the DAP {6}.
Reason: {7}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_5" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when the first Submodule is plugged in the Module "{4}" which is plugged with the DAP {5} and the second Submodule is plugged in the DAP {6}.
Reason: {7}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_6" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when the first Submodule is plugged in the Module {4} and the second Submodule is plugged in the Module {5} which are both plugged with the DAP {6}.
Reason: {7}</value>
  </data>
  <data name="M_UniqueIds_SUBMOD_7" xml:space="preserve">
    <value>The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
The conflict arises e.g. when the first Submodule is plugged in the Module {4} which is plugged with the DAP {5} and the second Submodule is plugged in the Module {6} which is plugged with the DAP {7}.
Reason: {8}</value>
  </data>
  <data name="M_Validation" xml:space="preserve">
    <value>Validation</value>
  </data>
</root>