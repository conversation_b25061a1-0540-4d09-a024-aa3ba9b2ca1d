/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: DataItem.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The DataItem element contains information about a single DataItem.
    /// It could be used to specify the characteristic of input or output
    /// data of a Submodule.
    /// </summary>
    public class DataItem :
        GsdObject,
        GSDI.IDataItem,
        GSDI.IDataItem2

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the DataItem if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public DataItem()
        {
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
            m_UseAsBits = false;
            m_DataType = GSDI.DataItemTypes.GSDDtUnsigned8;
            m_DataLength = 0;
            m_Subordinate = false;
            m_DataItemId = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_Name;
        private string m_NameTextID;
        private bool m_UseAsBits;
        private GSDI.DataItemTypes m_DataType;
        private string m_DataTypeAsString;
        private uint m_DataLength;
        private bool m_Subordinate;
        private string m_DataItemId;

        private ArrayList m_BitDataItems;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
		/// Accesses the language specific name of the data item.
		/// </summary>
        public string Name => this.m_Name;

        /// <summary>
        /// Accesses the name text id of the data item.
        /// </summary>
        public string NameTextId => this.m_NameTextID;

        /// <summary>
        /// Accesses the UseAsBits property, which specifies whether the 
        /// engineering system should display the data item in a bit representation,
        /// or in normal octet representation.
        /// </summary>
        /// <remarks>Instead of addressing an octet only completely (e.g. with one number 5),
        /// each bit of the octet can be addressed separately (e.g. with 5.0 to 5.7)</remarks>
        public bool UseAsBits => this.m_UseAsBits;

        /// <summary>
        /// Accesses the data type of the data item.
        /// </summary>
        public GSDI.DataItemTypes DataType => this.m_DataType;

        /// <summary>
        /// Accesses the data type of the data item as string.
        /// </summary>
        public string DataTypeAsString => m_DataTypeAsString;

        /// <summary>
        /// Accesses the length of the octets, specified with this data item.
        /// </summary>
        public UInt32 DataLength => this.m_DataLength;

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array BitDataItems =>
            this.m_BitDataItems?.ToArray();

        /// <summary>
        /// Accesses the Subordinate property, which specifies whether the 
        /// data item is used as Q-Channels
        /// </summary>
        public bool Subordinate => this.m_Subordinate;

        /// <summary>
        /// Accesses the id of the data item.
        /// </summary>
        public string DataItemId => this.m_DataItemId;
        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_Name = hash[member] as string;

                member = Models.s_FieldNameTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_NameTextID = hash[member] as string;

                member = Models.s_FieldUseAsBits;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    m_UseAsBits = (bool)hash[member];

                member = Models.s_FieldDataType;
                if (hash.ContainsKey(member) && hash[member] is GSDI.DataItemTypes)
                    m_DataType = (GSDI.DataItemTypes)hash[member];

                member = Models.s_FieldDataTypeAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_DataTypeAsString = (string)hash[member];

                member = Models.s_FieldDataLength;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    m_DataLength = (uint)hash[member];

                // V1.1
                member = Models.s_FieldBitDataItems;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_BitDataItems = hash[member] as ArrayList;

                member = Models.s_FieldSubordinate;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    m_Subordinate = (bool)hash[member];

                member = Models.s_FieldDataItemId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_DataItemId = (string)hash[member];
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectDataItem);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldUseAsBits, this.m_UseAsBits);
            Export.WriteEnumProperty(ref writer, Models.s_FieldDataType, this.m_DataType.ToString(), Export.s_SubtypeDataItemTypes);
            Export.WriteUint32Property(ref writer, Models.s_FieldDataLength, this.m_DataLength);

            // V1.1
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldBitDataItems, this.m_BitDataItems);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldSubordinate, this.m_Subordinate);

            Export.WriteStringProperty(ref writer, Models.s_FieldDataItemId, this.m_DataItemId);
            return true;
        }

        #endregion
    }
}