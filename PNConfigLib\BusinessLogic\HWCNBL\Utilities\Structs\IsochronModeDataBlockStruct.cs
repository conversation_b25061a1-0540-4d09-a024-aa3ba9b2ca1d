/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronModeDataBlockStruct.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Structs
{
    /// <summary>
    /// This class provides an access platform to the Isochron data contained in an Isochronblock array
    /// </summary>
    internal class IsochronModeDataBlockStruct
    {
        private byte[] data = new byte[32];

        public IsochronModeDataBlockStruct()
        {
            BlockLength = 28;
        }

        public IsochronModeDataBlockStruct(byte[] newData)
        {
            if (newData == null)
            {
                throw new ArgumentNullException(nameof(newData));
            }

            Debug.Assert(newData.Length == 32);
            Array.Copy(newData, 0, data, 0, newData.Length);
        }

        public int BlockLength
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public int BlockType
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int BlockVersionHigh
        {
            set { BufferManager.Write8(data, 4, value); }
            get { return BufferManager.Read8(data, 4); }
        }

        public int BlockVersionLow
        {
            set { BufferManager.Write8(data, 5, value); }
            get { return BufferManager.Read8(data, 5); }
        }

        public int CACF
        {
            set { BufferManager.Write16(data, 12, value); }
            get { return BufferManager.Read16(data, 12); }
        }

        public int SlotNumber
        {
            set { BufferManager.Write16(data, 8, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public int SubSlotNumber
        {
            set { BufferManager.Write16(data, 10, value); }
            get { return BufferManager.Read16(data, 10); }
        }

        public uint T_InputValid
        {
            set { BufferManager.Write32(data, 24, value); }
            get { return BufferManager.Read32(data, 24); }
        }

        public uint T_OutputValid
        {
            set { BufferManager.Write32(data, 28, value); }
            get { return BufferManager.Read32(data, 28); }
        }

        public int TDC
        {
            set { BufferManager.Write16(data, 14, value); }
            get { return BufferManager.Read16(data, 14); }
        }

        public uint TI
        {
            set { BufferManager.Write32(data, 16, value); }
            get { return BufferManager.Read32(data, 16); }
        }

        public uint TO
        {
            set { BufferManager.Write32(data, 20, value); }
            get { return BufferManager.Read32(data, 20); }
        }

        public byte[] ToByteArray()
        {
            return data;
        }
    }
}