/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIsochronIFCentralBL.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Diagnostics;

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Isochrone;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Addresses;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIsochronIFCentralBL : IFDecorator
    {
        // ########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// Initializes a new instance of the <see cref="PNIsochronIFCentralBL" /> class.
        /// </summary>
        /// <param name="decoratedInterface">
        /// Parameter BLInfo
        /// </param>
        public PNIsochronIFCentralBL(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        #endregion Construction/Destruction/Initialisation

        // ########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class
        // init BL
        /// <summary>
        /// Initialize
        /// </summary>
        public override void InitBL()
        {
            Interface.BaseActions.RegisterMethod(GetIsochronModeDataBlock.Name, GenericMethodGetIsochronModeDataBlock);

            ConsistencyManager.RegisterConsistencyCheck(Interface, MulticastMethodCheckConsistency);
        }

        #endregion Overrides and Overridables

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion Fields

        // ########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        public override void Configure(IConfigInterface xmlDeviceInterface, SyncDomainType syncDomainType)
        {
            CentralDeviceTypeCentralDeviceInterface xmlCentralDeviceInterface =
                xmlDeviceInterface as CentralDeviceTypeCentralDeviceInterface;
            base.Configure(xmlCentralDeviceInterface, syncDomainType);

            Interface.PNIOC.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.DelayTimeOB61, 0);
            Interface.PNIOC.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.DelayTimeAutomaticOb61, true);

            IsochronConfigurationStrategy isoStrategy = new IsochronConfigurationStrategy();
            if (xmlCentralDeviceInterface != null)
            {
                CentralAdvancedOptionsTypeIsochronousMode isochronousMode =
                    xmlCentralDeviceInterface.AdvancedOptions.IsochronousMode;

                long applicationCycle = Interface.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIsoUserAdjustedAppCycle,
                    new AttributeAccessCode(),
                    0);
                // if it was specified in the configuration.xml use the user given value
                if (isochronousMode.ApplicationCycleSpecified)
                {
                    isoStrategy.SetApplicationCycle(Interface, (long)(isochronousMode.ApplicationCycle * 1000));
                }
                // if not use the default value for the application cycle
                else
                {
                    isoStrategy.SetApplicationCycle(Interface, applicationCycle);
                }

                if (isochronousMode.Item is string) // Auto-minimum
                {
                    isoStrategy.SetAutomaticMinimum(Interface, true);
                    isoStrategy.SetDelayTime(Interface, null);
                }
                else if (isochronousMode.Item is float)
                {
                    isoStrategy.SetAutomaticMinimum(Interface, false);
                    float delayTime = (float)isochronousMode.Item;
                    IsochronConfigurationStrategy.ValidateDelayTime(
                        delayTime,
                        NavigationUtilities.GetIoSystem(Interface));
                    isoStrategy.SetDelayTime(Interface, delayTime);
                }
            }
        }


        #endregion

        // ########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion Constants and Enums

        // ########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion Nested Classes

        // ########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        // ########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        // ########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        // ########################################################################################

        #region Protected Methods

        #endregion

        // ########################################################################################

        #region Private Implementation

        /// <summary>
        /// Generates the Isochronous Mode data block
        /// </summary>
        /// <param name="methodData">
        /// The method data.
        /// </param>
        private void GenericMethodGetIsochronModeDataBlock(IMethodData methodData)
        {
            // methodData can not be null..
            if (methodData == null)
            {
                return;
            }

            bool isIsoncronEnabled = IsochronHelper.IsIsochronousModeEnabled(Interface);
            if (isIsoncronEnabled == false)
            {
                methodData.ReturnValue = null;
                return;
            }

            int blockversion = 0x0100;

            object value = methodData.Arguments[GetIsochronModeDataBlock.Blockversion];

            if (value != null)
            {
                blockversion = Convert.ToInt32(value, CultureInfo.InvariantCulture);
            }

            IsochronModeRecordStruct block = new IsochronModeRecordStruct();

            DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(Interface);

            block.Cacf = ioSystem.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.PnIsoCacf,
                new AttributeAccessCode(),
                0);

            IsochronConfigurationStrategy isochronConfigurationStrategy = new IsochronConfigurationStrategy();

            double delayTimeAsMilliSecond = isochronConfigurationStrategy.GetDelayTimeAsMilliSeconds(Interface);
            block.TcaStart = (uint)(delayTimeAsMilliSecond * 1000000.0);

            SyncDomain syncDomain = Interface.SyncDomain;
            uint tcaValid = (uint)syncDomain.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIsoTcaValid,
                new AttributeAccessCode(),
                0u);

            block.TcaValid = tcaValid;

            block.BlockVersion = blockversion;
            methodData.ReturnValue = block.ToByteArray;
        }

        #region Consistency Check

        /// <summary>
        /// The multicast method check consistency.
        /// </summary>
        private void MulticastMethodCheckConsistency()
        {
            if (!IsochronHelper.IsIsochronousModeEnabled(Interface))
            {
                return;
            }

            CheckInputOutputSize();
            CheckTdc();

            DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(Interface);

            Interface xmlCentralDeviceInterface = ioSystem.PNIOC.GetInterface();

            long applicationCycle = xmlCentralDeviceInterface.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIsoUserAdjustedAppCycle,
                new AttributeAccessCode(),
                0);
            float calculatedCacf = IsochronHelper.CalculateCacf(xmlCentralDeviceInterface, applicationCycle);
            bool isCacfValid = IsochronHelper.ValidateCacf(xmlCentralDeviceInterface, calculatedCacf);
            if (isCacfValid)
            {
                return;
            }

            string interfaceName = AttributeUtilities.GetName(Interface);
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                Interface,
                ConsistencyConstants.IsoCacfNotValid,
                interfaceName);
        }

        private void CheckInputOutputSize()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            //Check copytime only if there are isochrone PIPs
            // Max data lengths per PIP (16000000 is just indicating invalid value!) 
            int maxInCopyLen = Interface.AttributeAccess.GetAnyAttribute<Int32>(
                InternalAttributeNames.PnIsoTpaMaxInCopyLen, ac.GetNew(), 16000000);
            int maxOutCopyLen = Interface.AttributeAccess.GetAnyAttribute<Int32>(
                InternalAttributeNames.PnIsoTpaMaxOutCopyLen, ac.GetNew(), 16000000);

            PipLengths pipLengths = CollectPipLengths();
            if (pipLengths.InputLength > maxInCopyLen)
            {
                //Pip input copy time exceeds controller specific limit
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.InputPipSizeAgainstController);
            }

            if (pipLengths.OutputLength > maxOutCopyLen)
            {
                //Pip output copy time exceeds controller specific limit
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.OutputPipSizeAgainstController);
            }
        }

        private void CheckTdc()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            //Additional attributes for copy time calculation
            UInt32 pnIsoInterruptLatency = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoInterruptLatency,
                ac.GetNew(),
                0);
            UInt32 baseTimeInputs = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeInputBase,
                ac.GetNew(),
                0);
            UInt32 copyTimeInputByte = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeInputByte,
                ac.GetNew(),
                0);
            UInt32 copyTimeOutputBase = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeOutputBase,
                ac.GetNew(),
                0);
            UInt32 copyTimeOutputByte = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeOutputByte,
                ac.GetNew(),
                0);
            UInt32 provisionTime =
                Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnIsoProvisionTime,
                    ac.GetNew(),
                    0);
            UInt32 copyTimeInputSubmodule = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeInputSubmodule,
                ac.GetNew(),
                0);
            UInt32 copyTimeOutputSubmodule = Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnIsoCopyTimeOutputSubmodule,
                ac.GetNew(),
                0);

            //Check copy time against Classic-CPU specific limit
            Int64 tdc = Utility.GetTdc(Interface);
            IsochronConfigurationStrategy isochronConfigurationStrategy = new IsochronConfigurationStrategy();
            double delayTimeMs = isochronConfigurationStrategy.GetDelayTimeAsMilliSeconds(Interface);
            long delayTime = (long)(delayTimeMs * 1000000.0);

            SyncDomain syncdomain = Interface.SyncDomain;
            if (syncdomain.SyncDomainBusinessLogic == null)
            {
                Debug.Assert(false, "No SyncDomainBusinessLogic on SyncDomain.");
            }

            long endOfRed = syncdomain.SyncDomainBusinessLogic.PNPlannerResults.CalculatedPartIrt;

            //total copy time for outputs
            long tko = 0;
            //total copy time for inputs
            long tki = 0;

            PipLengths pipLengths = CollectPipLengths();
            // Check pip input copy time
            if (pipLengths.InputCount > 0)
            {
                //Add to total copy time for inputs
                tki += baseTimeInputs + pipLengths.InputLength * copyTimeInputByte
                       + pipLengths.InputCount * copyTimeInputSubmodule;
            }
            // Check pip output copy time
            if (pipLengths.OutputCount > 0)
            {
                //Add to total copy time for inputs
                tko += copyTimeOutputBase + pipLengths.OutputLength * copyTimeOutputByte + provisionTime
                       + pipLengths.OutputCount * copyTimeOutputSubmodule;
            }

            //Calculate copy time (minimal required send clock)
            long tdcMin = Math.Max((delayTime + pnIsoInterruptLatency), endOfRed) + tki + tko;

            //Compare minimal required send clock to set send clock
            if (tdc < tdcMin)
            {
                object[] errorParameters = new object[2];
                errorParameters[0] = tdcMin / 1000.0; //convert from nano to micro
                errorParameters[1] = tdc / 1000.0; //convert from nano to micro

                LogSeverity logSeverity = LogSeverity.Error;
                if (Interface.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIsoTDCConsistencyCheckAsWarning,
                    new AttributeAccessCode(),
                    false))
                {
                    logSeverity = LogSeverity.Warning;
                }
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    logSeverity,
                    Interface,
                    ConsistencyConstants.TdcAgainstTdcminCheck,
                    errorParameters);
            }
        }

        private class PipLengths
        {
            internal int InputLength
            {
                get; set;
            }
            internal int OutputLength
            {
                get; set;
            }
            internal int InputCount
            {
                get; set;
            }
            internal int OutputCount
            {
                get; set;
            }
            internal bool DiscardIOXS
            {
                get; set;
            }
            internal bool SharedDeviceSupported
            {
                get; set;
            }
        }

        private PipLengths CollectPipLengths()
        {
            //create parameter object with the needed data
            //create an input and an output size for all pips
            PipLengths pipLengths = new PipLengths();

            // Get the IOController IOSystem connector of the controllerInterfaceSubmodule
            List<PNIOD> devicesOfIOController = NavigationUtilities.GetIoDevicesOfIoController(Interface.PNIOC);
            if (devicesOfIOController == null)
            {
                return null;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            bool controllerSharedDeviceAssignmentSupp = Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoSharedDeviceAssignmentSupp,
                ac,
                true);

            foreach (PNIOD ioD in devicesOfIOController)
            {
                DecentralDevice device = ioD.GetDevice() as DecentralDevice;
                if (device != null)
                {
                    pipLengths.DiscardIOXS = !Utility.GetIOXSRequired(device.GetInterface());

                    if (controllerSharedDeviceAssignmentSupp)
                    {
                        bool sharedDeviceSupported = device.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnIoSharedDeviceSupported,
                            new AttributeAccessCode(),
                            false);

                        pipLengths.SharedDeviceSupported = sharedDeviceSupported;
                    }
                    
                    IList<PclObject> modules = PNNavigationUtility.GetModulesSorted(device);
                    foreach (PclObject module in modules)
                    {
                        if (module == null)
                        {
                            continue;
                        }
                        GetAddressLengthOfDeviceItem(module, pipLengths, 1, 1);
                    }
                }
            }
            return pipLengths;
        }

        private static void GetAddressLengthOfDeviceItem(PclObject deviceItem,
                                                         PipLengths pipLengths,
                                                         int iocsLength, int iopsLength)
        {
            if (deviceItem == null)
            {
                throw new ArgumentNullException(nameof(deviceItem));
            }

            bool isAssigned = true;
            if (pipLengths.SharedDeviceSupported)
            {
                UInt32 assignment =
                    deviceItem.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.SharedIoAssignment,
                        new AttributeAccessCode(),
                        0);
                isAssigned = (SharedIoAssignment)assignment == SharedIoAssignment.None;
            }

            if (isAssigned) //US 51: Simple shared device - Filter not assigned submodules
            {
                AddFrameLengths(deviceItem, pipLengths, iocsLength, iopsLength);
            }

            foreach (PclObject element in deviceItem.GetElements())
            {
                GetAddressLengthOfDeviceItem(element, pipLengths, iocsLength, iopsLength);
            }
        }

        private static void AddFrameLengths(
            PclObject deviceItem, PipLengths pipLengths, int iocsLength, int iopsLength)
        {
            if (deviceItem == null)
            {
                Debug.Assert(deviceItem != null);
            }

            DataAddress inAdr;
            DataAddress outAdr;
            bool isDiagAdr;
            bool hasAddress = AddressUtility.GetAddressObjectsOfModule(
                deviceItem,
                out inAdr,
                out outAdr,
                out isDiagAdr);

            if (!hasAddress)
            {
                return;
            }

            if (pipLengths.DiscardIOXS)
            {
                iocsLength = iopsLength = 0;
            }

            if (inAdr != null)
            {
                pipLengths.InputLength += GetLength(inAdr);
                pipLengths.InputLength += iopsLength;
                pipLengths.InputCount++;
            }

            if (outAdr != null)
            {
                pipLengths.OutputLength += GetLength(outAdr);
                pipLengths.OutputLength += iocsLength;
                pipLengths.OutputCount++;
            }
        }

        private static int GetLength(DataAddress adr)
        {
            if (adr == null)
            {
                return 0;
            }

            // Divison by 8 is deleted because PCL uses bytes for addresses
            return adr.Length;
        }

        #endregion

        // ########################################################################################

        #endregion
    }
}