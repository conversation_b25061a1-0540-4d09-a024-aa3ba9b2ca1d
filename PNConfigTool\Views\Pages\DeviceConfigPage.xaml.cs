using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Diagnostics;
using System.IO;
using System.Linq;
using PNConfigTool.Models;
using PNConfigTool.Services;
using PNConfigTool.Views.Windows;
using PNConfigTool.ViewModels;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.Importer.GSDImport.Helper;
using PNConfigLib.Gsd.Interpreter;
using GSDI;

namespace PNConfigTool.Views.Pages
{

    /// <summary>
    /// DeviceConfigPage.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceConfigPage : Page, INavigationAware, INavigationAwareLeaving
    {
        private ProjectManager? _projectManager;
        private GlobalAddressManager _globalAddressManager;
        private string _deviceName = string.Empty;
        private string _deviceType = string.Empty;
        private bool _isModified = false;
        private bool _isInitialized = false; // 标志是否已完成初始化，避免在加载时触发保存
        private ObservableCollection<ModuleViewModel> _modules = new ObservableCollection<ModuleViewModel>();

        // 实时设置相关属性
        private UpdateTimeConfig _updateTime = new UpdateTimeConfig();
        private uint _acceptedUpdateCyclesWithoutIOData = 3;
        
        public DeviceConfigPage()
        {
            try
            {
                InitializeComponent();
                _projectManager = ProjectManager.Instance;
                _globalAddressManager = GlobalAddressManager.Instance;
                
                // 初始化设备模块表格
                ModulesDataGrid.ItemsSource = _modules;
                
                // 设置按钮初始状态
                RemoveModuleButton.IsEnabled = false;
                
                // 绑定事件
                ModulesDataGrid.SelectionChanged += ModulesDataGrid_SelectionChanged;
                ModulesTreeView.SelectedItemChanged += ModulesTreeView_SelectedItemChanged;
                
                // 双击跳转功能已取消
                
                Debug.WriteLine("DeviceConfigPage initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing DeviceConfigPage: {ex.Message}");
                MessageBox.Show($"初始化设备配置页面时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新时间配置（用于数据绑定）
        /// </summary>
        public UpdateTimeConfig UpdateTime
        {
            get => _updateTime;
            set
            {
                _updateTime = value;
                _isModified = true;
            }
        }

        /// <summary>
        /// 更新时间值（用于ComboBox绑定）
        /// </summary>
        public float UpdateTimeValue
        {
            get => _updateTime.Value;
            set
            {
                _updateTime.Value = value;
                _isModified = true;
            }
        }

        /// <summary>
        /// 无IO数据时接受的更新周期数（用于数据绑定）
        /// </summary>
        public uint AcceptedUpdateCyclesWithoutIOData
        {
            get => _acceptedUpdateCyclesWithoutIOData;
            set
            {
                _acceptedUpdateCyclesWithoutIOData = value;
                _isModified = true;
            }
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            // 如果设备名为空，尝试再次从导航服务获取
            if (string.IsNullOrEmpty(_deviceName))
            {
                LoadDeviceFromNavigation();
            }
        }
        
        /// <summary>
        /// 实现INavigationAware接口，处理导航参数
        /// </summary>
        public void OnNavigatedTo(object? parameter)
        {
            Debug.WriteLine($"DeviceConfigPage.OnNavigatedTo called with parameter: {parameter}");
            
            try
            {
                if (parameter is string deviceName && !string.IsNullOrEmpty(deviceName))
                {
                    _deviceName = deviceName;
                    Debug.WriteLine($"接收到设备名称参数: {_deviceName}");
                    LoadDeviceData(_deviceName);
                }
                else
                {
                    Debug.WriteLine($"没有接收到有效的设备名称参数: {parameter}");
                    // 如果没有收到参数，尝试从页面名称解析
                    LoadDeviceFromNavigation();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"OnNavigatedTo错误: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"加载设备信息时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 实现INavigationAwareLeaving接口，在页面离开前保存更改
        /// </summary>
        public bool OnNavigatedFrom()
        {
            // 保存更改并返回true，表示允许导航
            if (_isModified)
            {
                    SaveDeviceConfiguration();
                    return true;

            }

            // 重置初始化标志
            _isInitialized = false;

            // 如果没有修改，允许导航
            return true;
        }
        
        /// <summary>
        /// 从导航参数加载设备信息
        /// </summary>
        private void LoadDeviceFromNavigation()
        {
            try
            {
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService != null && navigationService.CurrentPage != null)
                {
                    string pageName = navigationService.CurrentPage;
                    Debug.WriteLine($"Current page name: {pageName}");
                    
                    // 尝试从导航参数获取设备名称
                    if (navigationService.NavigationParameter is string deviceName && !string.IsNullOrEmpty(deviceName))
                    {
                        _deviceName = deviceName;
                        Debug.WriteLine($"Extracted device name from parameter: {_deviceName}");
                        LoadDeviceData(_deviceName);
                        return;
                    }
                    
                    // 如果导航参数为空，则尝试从页面名称解析
                    if (pageName.StartsWith("DeviceConfig_"))
                    {
                        _deviceName = pageName.Substring("DeviceConfig_".Length);
                        Debug.WriteLine($"Extracted device name from page name: {_deviceName}");
                        LoadDeviceData(_deviceName);
                    }
                    else if (pageName.StartsWith("DeviceDetail_"))
                    {
                        _deviceName = pageName.Substring("DeviceDetail_".Length);
                        Debug.WriteLine($"Extracted device name from page name: {_deviceName}");
                        LoadDeviceData(_deviceName);
                    }
                    else
                    {
                        Debug.WriteLine($"无法从页面名称提取设备名: {pageName}");
                    }
                }
                else
                {
                    Debug.WriteLine("导航服务或当前页面为空");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading device from navigation: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 加载设备数据
        /// </summary>
        private void LoadDeviceData(string deviceName)
        {
            try
            {
                if (_projectManager == null)
                {
                    _projectManager = ProjectManager.Instance;
                    if (_projectManager == null)
                    {
                        Debug.WriteLine("Error: ProjectManager.Instance is null");
                        MessageBox.Show("无法获取项目管理器实例", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }
                
                if (_projectManager.CurrentProject == null)
                {
                    Debug.WriteLine("Error: CurrentProject is null");
                    MessageBox.Show("当前没有打开的项目", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                Debug.WriteLine($"Loading device data for: {deviceName}");
                Debug.WriteLine($"Current device count: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");

                // 查找对应的设备配置
                DecentralDeviceConfig? deviceConfig = null;
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    Debug.WriteLine($"Checking device: {device.DeviceRefID}, Type: {device.DeviceType}");
                    if (device.DeviceRefID == deviceName)
                    {
                        deviceConfig = device;
                        Debug.WriteLine($"Found matching device: {device.DeviceRefID}, Type: {device.DeviceType}");
                        break;
                    }
                }

                if (deviceConfig != null)
                {
                    try
                    {
                        // 保存设备类型 - 使用DeviceType而不是DeviceRefID
                        _deviceType = !string.IsNullOrEmpty(deviceConfig.DeviceType) ? deviceConfig.DeviceType : deviceConfig.DeviceRefID;
                        Debug.WriteLine($"Device type: {_deviceType}");

                        // 确保UI已初始化
                        EnsureUIInitialized();

                        // 设置页面标题和设备信息
                        UpdateUIWithDeviceInfo(deviceConfig, deviceName);
                        
                        // 设置树视图 - 使用动态GSDML数据
                        UpdateModulesTreeView(_deviceType);
                        Debug.WriteLine("Updated module tree view with dynamic GSDML data");

                        // 加载设备的模块配置
                        LoadModulesConfiguration(deviceConfig);
                        Debug.WriteLine("Loaded module configuration");

                        // 加载实时设置配置
                        LoadRealTimeSettings(deviceConfig);
                        Debug.WriteLine("Loaded real-time settings");

                        // 加载MRP配置
                        LoadMRPConfiguration(deviceConfig);
                        Debug.WriteLine("Loaded MRP configuration");

                        // 加载UpdateTime配置
                        LoadUpdateTimeConfiguration(deviceConfig);
                        Debug.WriteLine("Loaded UpdateTime configuration");

                        // 初始化数据保持下拉框（3-255范围）
                        InitializeAcceptedUpdateCyclesComboBox();
                        Debug.WriteLine("Initialized AcceptedUpdateCycles configuration");

                        Debug.WriteLine("Device data loaded successfully");

                        // 重置修改状态
                        _isModified = false;

                        // 标记初始化完成，允许保存操作
                        _isInitialized = true;

                        // 注册UI事件以跟踪修改
                        RegisterUIChangeEvents();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error setting device data: {ex.Message}");
                        Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                        MessageBox.Show($"设置设备数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    Debug.WriteLine($"Device not found: {deviceName}");
                    MessageBox.Show($"未找到设备: {deviceName}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadDeviceData: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"加载设备数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 确保UI组件已初始化
        /// </summary>
        private void EnsureUIInitialized()
        {
            // 检查UI元素
            Debug.WriteLine($"PageTitle exists: {PageTitle != null}");
            Debug.WriteLine($"ModulesDataGrid exists: {ModulesDataGrid != null}");
            Debug.WriteLine($"ModulesTreeView exists: {ModulesTreeView != null}");

            // 确保页面加载完成
            if (PageTitle == null || ModulesDataGrid == null || ModulesTreeView == null)
            {
                Debug.WriteLine("UI elements not initialized, forcing UI update");

                // 强制UI线程更新
                if (Application.Current != null && Application.Current.Dispatcher != null)
                {
                    Application.Current.Dispatcher.Invoke(() => {
                        UpdateLayout();
                    });
                }
            }
        }
        
        /// <summary>
        /// 更新UI显示设备信息
        /// </summary>
        private void UpdateUIWithDeviceInfo(DecentralDeviceConfig deviceConfig, string deviceName)
        {
            try
            {
                if (PageTitle != null)
                {
                    PageTitle.Text = $"设备: {_deviceType} - {deviceName}";
                    Debug.WriteLine($"Set PageTitle to: 设备: {_deviceType} - {deviceName}");
                }
                else
                {
                    Debug.WriteLine("WARNING: PageTitle is null");
                }

                Debug.WriteLine($"设备信息已移至模块配置页面显示");
                Debug.WriteLine($"设备类型: {_deviceType}");
                Debug.WriteLine($"设备名称: {deviceName}");

                // 获取对应的ListOfNodes设备节点以获取GSDML路径
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode?.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode?.GSDRefID ?? string.Empty;

                Debug.WriteLine($"GSDML相对路径: {relativePath}");
                Debug.WriteLine($"GSDML绝对路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating UI: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// 从GSDML文件中提取DAP信息
        /// </summary>
        private DAPInfo? ExtractDAPInfoFromGSDML(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return null;
                }

                // 获取设备结构
                var deviceStructure = interpreter.GetDeviceStructureElement();
                if (deviceStructure == null)
                {
                    Debug.WriteLine("无法获取设备结构");
                    return null;
                }

                Debug.WriteLine($"设备结构获取成功，DAP数量: {deviceStructure?.DeviceAccessPoints?.Length ?? 0}");

                // 获取设备访问点
                var deviceAccessPoints = deviceStructure?.DeviceAccessPoints;
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("设备没有访问点");
                    return null;
                }

                // 查找匹配的DAP（根据用户选择的特定DAP）
                PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement? targetDAP = null;

                // 首先尝试根据GSDRefID匹配
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement accessPoint)
                    {
                        Debug.WriteLine($"检查DAP: Name={accessPoint.Name}, GsdID={accessPoint.GsdID}");

                        // 匹配GsdID或Name
                        if (!string.IsNullOrEmpty(accessPoint.GsdID) && accessPoint.GsdID.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过GsdID): {accessPoint.GsdID}");
                            break;
                        }
                        else if (!string.IsNullOrEmpty(accessPoint.Name) && accessPoint.Name.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过Name): {accessPoint.Name}");
                            break;
                        }
                    }
                }

                // 如果没有找到匹配的DAP，使用第一个DAP
                if (targetDAP == null && deviceAccessPoints.Length > 0)
                {
                    if (deviceAccessPoints.GetValue(0) is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement firstDAP)
                    {
                        targetDAP = firstDAP;
                        Debug.WriteLine($"使用第一个DAP: {firstDAP.Name ?? firstDAP.GsdID}");
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine("无法找到有效的DAP");
                    return null;
                }

                // 提取DAP信息
                var dapInfo = new DAPInfo();

                // 提取Category信息
                dapInfo.Category = !string.IsNullOrEmpty(targetDAP.Category) ? targetDAP.Category :
                                  (!string.IsNullOrEmpty(targetDAP.SubCategory1) ? targetDAP.SubCategory1 :
                                  (!string.IsNullOrEmpty(targetDAP.MainFamily) ? targetDAP.MainFamily : "未知"));

                // 提取OrderNumber信息
                dapInfo.OrderNumber = !string.IsNullOrEmpty(targetDAP.OrderNumber) ? targetDAP.OrderNumber : "未指定";

                // 提取Info信息
                dapInfo.Info = !string.IsNullOrEmpty(targetDAP.InfoText) ? targetDAP.InfoText : "无描述信息";

                // 提取InputLength和OutputLength信息
                ExtractDAPIOLengths(targetDAP, dapInfo);

                Debug.WriteLine($"成功提取DAP信息:");
                Debug.WriteLine($"  Category: {dapInfo.Category}");
                Debug.WriteLine($"  OrderNumber: {dapInfo.OrderNumber}");
                Debug.WriteLine($"  InputLength: {dapInfo.InputLength}");
                Debug.WriteLine($"  OutputLength: {dapInfo.OutputLength}");
                Debug.WriteLine($"  Info: {dapInfo.Info}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 成功完成 ===");

                return dapInfo;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取DAP信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 异常结束 ===");
                return null;
            }
        }

        /// <summary>
        /// 提取DAP的输入输出长度信息
        /// </summary>
        private void ExtractDAPIOLengths(PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement dap, DAPInfo dapInfo)
        {
            try
            {
                Debug.WriteLine($"=== ExtractDAPIOLengths 开始 ===");

                int totalInputLength = 0;
                int totalOutputLength = 0;

                // 从DAP的子模块中提取IO长度信息
                if (dap.Submodules != null && dap.Submodules.Length > 0)
                {
                    Debug.WriteLine($"DAP包含 {dap.Submodules.Length} 个子模块");

                    foreach (var submodule in dap.Submodules)
                    {
                        if (submodule is PNConfigLib.Gsd.Interpreter.Structure.SubmoduleStructureElement submoduleElement)
                        {
                            // 尝试从子模块获取IO数据长度
                            var ioData = GetSubmoduleIOData(submoduleElement);
                            if (ioData.HasValue)
                            {
                                totalInputLength += ioData.Value.InputLength;
                                totalOutputLength += ioData.Value.OutputLength;
                                Debug.WriteLine($"子模块 {submoduleElement.Name} IO长度: Input={ioData.Value.InputLength}, Output={ioData.Value.OutputLength}");
                            }
                        }
                    }
                }

                // 从DAP的模块中提取IO长度信息
                if (dap.Modules != null && dap.Modules.Length > 0)
                {
                    Debug.WriteLine($"DAP包含 {dap.Modules.Length} 个模块");

                    foreach (var module in dap.Modules)
                    {
                        if (module is PNConfigLib.Gsd.Interpreter.Structure.ModuleStructureElement moduleElement)
                        {
                            // 尝试从模块获取IO数据长度
                            var ioData = GetModuleIOData(moduleElement);
                            if (ioData.HasValue)
                            {
                                totalInputLength += ioData.Value.InputLength;
                                totalOutputLength += ioData.Value.OutputLength;
                                Debug.WriteLine($"模块 {moduleElement.Name} IO长度: Input={ioData.Value.InputLength}, Output={ioData.Value.OutputLength}");
                            }
                        }
                    }
                }

                dapInfo.InputLength = totalInputLength;
                dapInfo.OutputLength = totalOutputLength;

                Debug.WriteLine($"DAP总IO长度: InputLength={totalInputLength}, OutputLength={totalOutputLength}");
                Debug.WriteLine($"=== ExtractDAPIOLengths 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取DAP IO长度时出错: {ex.Message}");
                dapInfo.InputLength = 0;
                dapInfo.OutputLength = 0;
            }
        }

        /// <summary>
        /// 获取子模块的IO数据信息
        /// </summary>
        private (int InputLength, int OutputLength)? GetSubmoduleIOData(PNConfigLib.Gsd.Interpreter.Structure.SubmoduleStructureElement submodule)
        {
            try
            {
                // 这里可以根据PNConfigLib的具体实现来获取子模块的IO数据长度
                // 由于具体的API可能因版本而异，这里提供一个基础实现

                // 尝试从属性中获取IO长度信息
                // 注意：具体的属性名称可能需要根据实际的PNConfigLib版本调整

                return (0, 0); // 默认返回0，实际实现需要根据PNConfigLib的API
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取子模块IO数据时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取模块的IO数据信息
        /// </summary>
        private (int InputLength, int OutputLength)? GetModuleIOData(PNConfigLib.Gsd.Interpreter.Structure.ModuleStructureElement module)
        {
            try
            {
                // 这里可以根据PNConfigLib的具体实现来获取模块的IO数据长度
                // 由于具体的API可能因版本而异，这里提供一个基础实现

                // 尝试从属性中获取IO长度信息
                // 注意：具体的属性名称可能需要根据实际的PNConfigLib版本调整

                return (0, 0); // 默认返回0，实际实现需要根据PNConfigLib的API
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块IO数据时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 动态添加DAP内置模块（替换硬编码的Interface、Port1、Port2）
        /// </summary>
        private void AddDAPBuiltinModules(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                Debug.WriteLine($"=== AddDAPBuiltinModules 开始 ===");
                Debug.WriteLine($"设备配置: {deviceConfig.DeviceRefID}");

                // 获取对应的ListOfNodes设备节点以获取GSDML路径和引用ID
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                if (deviceNode == null)
                {
                    Debug.WriteLine($"未找到对应的设备节点");
                    return;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                Debug.WriteLine($"GSDML相对路径: {relativePath}");
                Debug.WriteLine($"GSDML绝对路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在");
                    return;
                }

                // 从GSDML文件中提取DAP内置模块
                var builtinModules = ExtractDAPBuiltinModules(gsdPath, gsdRefId);

                if (builtinModules != null && builtinModules.Any())
                {
                    Debug.WriteLine($"成功提取到 {builtinModules.Count} 个DAP内置模块");

                    // 添加提取到的内置模块
                    foreach (var module in builtinModules)
                    {
                        AddModulePropertyChangedListener(module);
                        _modules.Add(module);
                        Debug.WriteLine($"添加DAP内置模块: {module.SubmoduleName} (InterfaceSlot: {module.InterfaceSlot})");
                    }
                }
                else
                {
                    Debug.WriteLine("未能从GSDML提取到内置模块");
                }

                // 注意：不在这里初始化表格行数，将在LoadModulesConfiguration方法的最后统一处理
                Debug.WriteLine("DAP内置模块添加完成，表格行数将在LoadModulesConfiguration方法中统一初始化");

                Debug.WriteLine($"=== AddDAPBuiltinModules 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加DAP内置模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 从GSDML文件中提取DAP内置模块
        /// </summary>
        private List<ModuleViewModel>? ExtractDAPBuiltinModules(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== ExtractDAPBuiltinModules 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return null;
                }

                // 获取设备结构
                var deviceStructure = interpreter.GetDeviceStructureElement();
                if (deviceStructure == null)
                {
                    Debug.WriteLine("无法获取设备结构");
                    return null;
                }

                // 获取设备访问点
                var deviceAccessPoints = deviceStructure?.DeviceAccessPoints;
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("设备没有访问点");
                    return null;
                }

                // 查找匹配的DAP（根据用户选择的特定DAP）
                PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement? targetDAP = null;

                // 首先尝试根据GSDRefID匹配
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement accessPoint)
                    {
                        Debug.WriteLine($"检查DAP: Name={accessPoint.Name}, GsdID={accessPoint.GsdID}");

                        // 匹配GsdID或Name
                        if (!string.IsNullOrEmpty(accessPoint.GsdID) && accessPoint.GsdID.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过GsdID): {accessPoint.GsdID}");
                            break;
                        }
                        else if (!string.IsNullOrEmpty(accessPoint.Name) && accessPoint.Name.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过Name): {accessPoint.Name}");
                            break;
                        }
                    }
                }

                // 如果没有找到匹配的DAP，使用第一个DAP
                if (targetDAP == null && deviceAccessPoints.Length > 0)
                {
                    if (deviceAccessPoints.GetValue(0) is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement firstDAP)
                    {
                        targetDAP = firstDAP;
                        Debug.WriteLine($"使用第一个DAP: {firstDAP.Name ?? firstDAP.GsdID}");
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine("无法找到有效的DAP");
                    return null;
                }

                // 提取DAP内置模块 - 使用简化的方式获取所有系统定义的子模块
                var builtinModules = new List<ModuleViewModel>();
                int moduleIndex = 2; // 从索引2开始，因为索引1是主模块

                // 使用缓存的Common API来获取系统定义的子模块
                var commonInterpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon);
                if (commonInterpreter != null)
                {
                    // 尝试使用Common命名空间的DeviceAccessPoint
                    var commonDAP = commonInterpreter.GetDeviceAccessPoint(gsdRefId);
                    Debug.WriteLine($"commonInterpreter.GetDeviceAccessPoint({gsdRefId}) 返回: {commonDAP?.GsdID ?? "null"}");

                    if (commonDAP != null)
                    {
                        Debug.WriteLine($"找到Common DAP: {commonDAP.GsdID}");

                        // 检查DAP是否有系统定义的子模块
                        if (commonDAP.HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDAll))
                        {
                            var systemSubmodules = commonDAP.GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDAll);
                            if (systemSubmodules != null && systemSubmodules.Length > 0)
                            {
                                Debug.WriteLine($"找到 {systemSubmodules.Length} 个系统定义子模块");
                                foreach (var submodule in systemSubmodules)
                                {
                                    if (submodule is PNConfigLib.Gsd.Interpreter.Common.SystemDefinedSubmoduleObject systemElement)
                                    {
                                        var moduleViewModel = CreateModuleViewModelFromSystemSubmodule(systemElement, moduleIndex++);
                                        if (moduleViewModel != null)
                                        {
                                            builtinModules.Add(moduleViewModel);
                                            Debug.WriteLine($"添加系统定义子模块: {moduleViewModel.SubmoduleName} (插槽号: {moduleViewModel.SubslotInfo})");
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            Debug.WriteLine("DAP没有系统定义的子模块");
                        }

                    }
                    else
                    {
                        Debug.WriteLine("无法获取Common DAP");
                    }
                }
                else
                {
                    Debug.WriteLine("无法获取Common解析器");
                }

                Debug.WriteLine($"成功提取到 {builtinModules.Count} 个DAP内置模块");
                Debug.WriteLine($"=== ExtractDAPBuiltinModules 成功完成 ===");

                return builtinModules;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取DAP内置模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== ExtractDAPBuiltinModules 异常结束 ===");
                return null;
            }
        }

        // 注意：IsBuiltinSubmoduleType 方法已删除，因为我们使用简化的系统定义子模块提取方式

        // 注意：CreateModuleViewModelFromSubmoduleStructure 和 GetSubmoduleStructureName 方法已删除，因为我们使用简化的系统定义子模块提取方式

        // 注意：CreateModuleViewModelFromVirtualSubmodule 方法已删除，因为我们使用简化的系统定义子模块提取方式

        /// <summary>
        /// 从系统定义子模块创建ModuleViewModel
        /// </summary>
        private ModuleViewModel? CreateModuleViewModelFromSystemSubmodule(
            PNConfigLib.Gsd.Interpreter.Common.SystemDefinedSubmoduleObject systemSubmodule,
            int index)
        {
            try
            {
                // 根据具体的子模块类型获取名称
                string submoduleName = GetSystemSubmoduleNameFromConcreteType(systemSubmodule, index);

                // 直接从系统定义子模块获取插槽号
                string subslotNumber = systemSubmodule.SubslotNumber.ToString();

                // 获取子模块标识号，用于生成InterfaceSlot
                uint submoduleIdentNumber = (uint)systemSubmodule.IdentNumber;
                string interfaceSlot = $"0 {submoduleIdentNumber}";

                var moduleViewModel = new ModuleViewModel
                {
                    Index = index,
                    IsSelected = false, // 内置模块禁用选择
                    Position = "-",
                    ModuleName = "",
                    SubmoduleName = submoduleName,
                    InterfaceSlot = interfaceSlot,
                    PNIStartAddress = "-",
                    SubslotInfo = subslotNumber, // 直接使用SubslotNumber
                    IsBuiltIn = true // 标识为DAP内置模块，确保只读状态
                    // 注意：IsSelected = false 确保内置模块不可选择，实现只读效果
                };

                Debug.WriteLine($"创建系统定义子模块ViewModel: {submoduleName} (插槽号: {subslotNumber})");
                AddModulePropertyChangedListener(moduleViewModel);
                return moduleViewModel;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建系统定义子模块ViewModel时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从具体的系统定义子模块类型获取名称
        /// </summary>
        private string GetSystemSubmoduleNameFromConcreteType(
            PNConfigLib.Gsd.Interpreter.Common.SystemDefinedSubmoduleObject systemSubmodule,
            int index)
        {
            try
            {
                // 检查是否为InterfaceSubmodule类型
                if (systemSubmodule is PNConfigLib.Gsd.Interpreter.Common.InterfaceSubmodule interfaceSubmodule)
                {
                    // 从InterfaceSubmodule获取Name属性
                    return !string.IsNullOrEmpty(interfaceSubmodule.Name)
                        ? interfaceSubmodule.Name
                        : "Interface";
                }
                // 检查是否为PortSubmodule类型
                else if (systemSubmodule is PNConfigLib.Gsd.Interpreter.Common.PortSubmodule portSubmodule)
                {
                    // 从PortSubmodule获取Name属性
                    return !string.IsNullOrEmpty(portSubmodule.Name)
                        ? portSubmodule.Name
                        : "Port";
                }
                // 其他类型的系统定义子模块
                else
                {
                    // 尝试使用GsdID或默认名称
                    return !string.IsNullOrEmpty(systemSubmodule.GsdID)
                        ? systemSubmodule.GsdID
                        : $"SystemSubmodule_{index}";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取系统子模块名称时出错: {ex.Message}");
                return $"SystemSubmodule_{index}";
            }
        }

        // 注意：GetSystemSubmoduleName 方法已删除，因为我们直接使用 SystemDefinedSubmoduleObject.Name 属性

        // 注意：ExtractBuiltinModulesDirectly 方法已删除，因为我们使用简化的系统定义子模块提取方式

        // 注意：CreateModuleViewModelFromXmlNode 方法已删除，因为我们使用简化的系统定义子模块提取方式

        // 注意：GetSubslotInfo 和 GetSubmoduleNameFromXmlNode 方法已删除，因为我们使用简化的系统定义子模块提取方式


        /// <summary>
        /// 更新模块树视图 - 使用GSDML文件中的动态数据
        /// </summary>
        private void UpdateModulesTreeView(string deviceType)
        {
            try
            {
                Debug.WriteLine($"=== UpdateModulesTreeView 开始 ===");
                Debug.WriteLine($"设备类型: {deviceType}");

                // 清空现有树
                ModulesTreeView.Items.Clear();

                // 获取当前设备配置
                DecentralDeviceConfig? deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig == null)
                {
                    Debug.WriteLine("无法获取设备配置，使用默认树结构");
                    CreateDefaultTreeView(deviceType);
                    return;
                }

                Debug.WriteLine($"获取到设备配置: {deviceConfig.DeviceRefID}");

                // 使用设备类型作为根节点
                TreeViewItem rootItem = new TreeViewItem
                {
                    Header = deviceType,
                    IsExpanded = true
                };

                // // 添加"主模块"节点 (DAP)
                // TreeViewItem mainModuleItem = new TreeViewItem
                // {
                //     Header = "主模块 (DAP)",
                //     IsExpanded = true
                // };
                // mainModuleItem.Items.Add(new TreeViewItem { Header = deviceType });
                // rootItem.Items.Add(mainModuleItem);

                Debug.WriteLine("开始创建模块树节点...");
                // 从GSDML数据动态添加"模块"节点
                TreeViewItem? modulesItem = CreateModulesTreeNode(deviceConfig);
                if (modulesItem != null)
                {
                    Debug.WriteLine("成功创建模块树节点，添加到根节点");
                    rootItem.Items.Add(modulesItem);
                }
                else
                {
                    // 如果无法从GSDML获取模块数据，不添加模块节点，保持为空
                    Debug.WriteLine("无法从GSDML获取模块数据，模块节点保持为空");
                }

                Debug.WriteLine("开始创建子模块树节点...");
                // 从GSDML数据动态添加"子模块"节点
                TreeViewItem? submodulesItem = CreateSubmodulesTreeNode(deviceConfig);
                if (submodulesItem != null)
                {
                    Debug.WriteLine("成功创建子模块树节点，添加到根节点");
                    rootItem.Items.Add(submodulesItem);
                }
                else
                {
                    // 如果无法从GSDML获取子模块数据，不添加子模块节点，保持为空
                    Debug.WriteLine("无法从GSDML获取子模块数据，子模块节点保持为空");
                }

                // 将根节点添加到树视图
                ModulesTreeView.Items.Add(rootItem);

                Debug.WriteLine($"成功更新设备 {deviceType} 的模块树视图");
                Debug.WriteLine($"=== UpdateModulesTreeView 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新模块树视图时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 出错时使用默认树结构
                CreateDefaultTreeView(deviceType);
            }
        }

        /// <summary>
        /// 获取当前设备配置
        /// </summary>
        private DecentralDeviceConfig? GetCurrentDeviceConfig()
        {
            if (_projectManager?.CurrentProject == null || string.IsNullOrEmpty(_deviceName))
            {
                return null;
            }

            return _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.FirstOrDefault(d => d.DeviceRefID == _deviceName);
        }

        /// <summary>
        /// 创建默认树视图（当无法从GSDML获取数据时使用）
        /// </summary>
        private void CreateDefaultTreeView(string deviceType)
        {
            try
            {
                // 清空现有树
                ModulesTreeView.Items.Clear();

                // 使用设备类型作为根节点
                TreeViewItem rootItem = new TreeViewItem
                {
                    Header = deviceType,
                    IsExpanded = true
                };

                // // 添加"主模块"节点
                // TreeViewItem mainModuleItem = new TreeViewItem
                // {
                //     Header = "主模块",
                //     IsExpanded = true
                // };
                // mainModuleItem.Items.Add(new TreeViewItem { Header = deviceType });
                // rootItem.Items.Add(mainModuleItem);

                // 添加"模块"节点 - 使用固定模块列表作为后备
                TreeViewItem modulesItem = new TreeViewItem
                {
                    Header = "模块",
                    IsExpanded = true
                };

                string[] moduleTypes = new string[]
                {
                    "1 byte I", "1 byte IO", "1 byte O",
                    "64 bytes I", "64 bytes IO", "64 bytes O",
                    "250 bytes I", "250 bytes IO", "250 bytes O",
                    "Multi subslots I1 I1 IO IO"
                };

                foreach (var moduleType in moduleTypes)
                {
                    modulesItem.Items.Add(new TreeViewItem { Header = moduleType });
                }
                rootItem.Items.Add(modulesItem);

                // 添加"子模块"节点
                TreeViewItem submodulesItem = new TreeViewItem
                {
                    Header = "子模块",
                };
                rootItem.Items.Add(submodulesItem);

                // 将根节点添加到树视图
                ModulesTreeView.Items.Add(rootItem);

                Debug.WriteLine("使用默认树视图结构");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建默认树视图时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从GSDML数据创建模块树节点
        /// </summary>
        private TreeViewItem? CreateModulesTreeNode(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                Debug.WriteLine($"=== CreateModulesTreeNode 开始 ===");
                Debug.WriteLine($"设备配置DeviceRefID: {deviceConfig.DeviceRefID}");

                // 获取对应的ListOfNodes设备节点以获取GSDML路径和引用ID
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                if (deviceNode == null)
                {
                    Debug.WriteLine($"未找到对应的设备节点，DeviceRefID: {deviceConfig.DeviceRefID}");
                    Debug.WriteLine("可用的设备节点:");
                    foreach (var node in _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices ?? new List<DecentralDeviceNode>())
                    {
                        Debug.WriteLine($"  - DeviceID: {node.DeviceID}, GSDRefID: {node.GSDRefID}");
                    }
                    return null;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                Debug.WriteLine($"设备节点信息:");
                Debug.WriteLine($"  - GSDPath相对路径: {relativePath}");
                Debug.WriteLine($"  - GSDPath绝对路径: {gsdPath}");
                Debug.WriteLine($"  - GSDRefID: {gsdRefId}");

                if (string.IsNullOrEmpty(gsdPath) || string.IsNullOrEmpty(gsdRefId))
                {
                    Debug.WriteLine("设备配置缺少GSDML路径或引用ID");
                    return null;
                }

                // 获取GSDML文件名
                string gsdmlFileName = Path.GetFileName(gsdPath);
                Debug.WriteLine($"GSDML文件名: {gsdmlFileName}");

                // 创建模块节点
                TreeViewItem modulesItem = new TreeViewItem
                {
                    Header = "模块",
                    IsExpanded = true
                };

                // 从Catalog.ModuleList获取该设备的模块
                var deviceModules = GetDeviceModulesFromCatalog(gsdmlFileName, gsdRefId);

                if (deviceModules.Any())
                {
                    Debug.WriteLine($"成功获取到 {deviceModules.Count()} 个模块，开始按Category分组");

                    // 获取缓存的Interpreter来批量处理Category信息
                    var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon);

                    // 按Category分组模块
                    var modulesByCategory = new Dictionary<string, List<KeyValuePair<string, PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog>>>();

                    foreach (var module in deviceModules)
                    {
                        // 获取模块的Category信息（使用缓存的interpreter）
                        string category = GetModuleCategoryFromInterpreter(interpreter, module.Key);

                        if (!modulesByCategory.ContainsKey(category))
                        {
                            modulesByCategory[category] = new List<KeyValuePair<string, PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog>>();
                        }

                        modulesByCategory[category].Add(module);
                        Debug.WriteLine($"模块 {module.Key} 归类到Category: {category}");
                    }

                    Debug.WriteLine($"模块按Category分组完成，共 {modulesByCategory.Count} 个分类");

                    // 检查是否所有模块都是"未分类"，如果是则省略分类层级
                    bool shouldSkipCategoryLevel = modulesByCategory.Count == 1 && modulesByCategory.ContainsKey("未分类");

                    if (shouldSkipCategoryLevel)
                    {
                        Debug.WriteLine("所有模块都是'未分类'，省略分类层级，直接显示平铺模块列表");

                        // 直接添加所有模块到根节点，不创建分类层级
                        var allModules = modulesByCategory["未分类"];
                        foreach (var module in allModules)
                        {
                            // 获取模块的显示名称，优先使用模块名称，回退到GSDRefID
                            string moduleDisplayName = GetModuleDisplayNameForTree(module.Value, module.Key);
                            if (!string.IsNullOrEmpty(moduleDisplayName))
                            {
                                var moduleItem = new TreeViewItem { Header = moduleDisplayName };
                                moduleItem.Tag = module.Key; // 保存模块的GSDRefID以便后续使用
                                modulesItem.Items.Add(moduleItem);
                                Debug.WriteLine($"直接添加模块到根节点: {moduleDisplayName} (GSDRefID: {module.Key})");
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine("存在有效分类，创建分类层级结构");

                        // 为每个Category创建分类节点
                        foreach (var categoryGroup in modulesByCategory.OrderBy(kvp => kvp.Key))
                        {
                            string categoryName = categoryGroup.Key;
                            var modulesInCategory = categoryGroup.Value;

                            Debug.WriteLine($"创建Category节点: {categoryName}，包含 {modulesInCategory.Count} 个模块");

                            // 创建分类节点
                            TreeViewItem categoryItem = new TreeViewItem
                            {
                                Header = categoryName,
                                IsExpanded = true
                            };

                            // 添加该分类下的所有模块
                            foreach (var module in modulesInCategory)
                            {
                                // 获取模块的显示名称，优先使用模块名称，回退到GSDRefID
                                string moduleDisplayName = GetModuleDisplayNameForTree(module.Value, module.Key);
                                if (!string.IsNullOrEmpty(moduleDisplayName))
                                {
                                    var moduleItem = new TreeViewItem { Header = moduleDisplayName };
                                    moduleItem.Tag = module.Key; // 保存模块的GSDRefID以便后续使用
                                    categoryItem.Items.Add(moduleItem);
                                    Debug.WriteLine($"添加模块到Category '{categoryName}': {moduleDisplayName} (GSDRefID: {module.Key})");
                                }
                            }

                            // 将分类节点添加到模块根节点
                            modulesItem.Items.Add(categoryItem);
                        }
                    }

                    if (shouldSkipCategoryLevel)
                    {
                        Debug.WriteLine($"从GSDML加载了 {deviceModules.Count()} 个模块，全部为未分类，使用平铺结构");
                    }
                    else
                    {
                        Debug.WriteLine($"从GSDML加载了 {deviceModules.Count()} 个模块，分为 {modulesByCategory.Count} 个分类，使用层级结构");
                    }
                    Debug.WriteLine($"=== CreateModulesTreeNode 成功完成 ===");
                    return modulesItem;
                }
                else
                {
                    Debug.WriteLine("未从GSDML找到模块，返回null以触发默认模块列表");
                    Debug.WriteLine($"=== CreateModulesTreeNode 失败，返回null ===");
                    return null; // 返回null，让UpdateModulesTreeView使用默认模块列表
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建模块树节点时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== CreateModulesTreeNode 异常结束 ===");
                return null;
            }
        }

        /// <summary>
        /// 从GSDML数据创建子模块树节点
        /// </summary>
        private TreeViewItem? CreateSubmodulesTreeNode(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                // 获取对应的ListOfNodes设备节点以获取GSDML路径和引用ID
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);
                    
                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode?.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode?.GSDRefID ?? string.Empty;

                if (string.IsNullOrEmpty(gsdPath) || string.IsNullOrEmpty(gsdRefId))
                {
                    Debug.WriteLine("设备配置缺少GSDML路径或引用ID");
                    return null;
                }

                // 获取GSDML文件名
                string gsdmlFileName = Path.GetFileName(gsdPath);

                // 创建子模块节点
                TreeViewItem submodulesItem = new TreeViewItem
                {
                    Header = "子模块",
                    IsExpanded = false
                };

                // 从Catalog.SubmoduleList获取该设备的子模块
                var deviceSubmodules = GetDeviceSubmodulesFromCatalog(gsdmlFileName, gsdRefId);

                if (deviceSubmodules.Any())
                {
                    foreach (var submodule in deviceSubmodules)
                    {
                        string submoduleName = GetSubmoduleDisplayName(submodule.Value);
                        if (!string.IsNullOrEmpty(submoduleName))
                        {
                            var submoduleItem = new TreeViewItem { Header = submoduleName };
                            submoduleItem.Tag = submodule.Key; // 保存子模块的键值以便后续使用
                            submodulesItem.Items.Add(submoduleItem);
                        }
                    }

                    Debug.WriteLine($"从GSDML加载了 {deviceSubmodules.Count()} 个子模块");
                }
                else
                {
                    Debug.WriteLine("未从GSDML找到子模块，返回null以触发默认子模块处理");
                    return null; // 返回null，让UpdateModulesTreeView使用默认子模块处理
                }

                return submodulesItem;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建子模块树节点时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从Catalog获取指定设备的模块列表
        /// </summary>
        private IEnumerable<KeyValuePair<string, ModuleCatalog>> GetDeviceModulesFromCatalog(string gsdmlFileName, string deviceGsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== GetDeviceModulesFromCatalog 开始 ===");
                Debug.WriteLine($"参数: gsdmlFileName={gsdmlFileName}, deviceGsdRefId={deviceGsdRefId}");

                if (Catalog.DeviceList == null || !Catalog.DeviceList.Any())
                {
                    Debug.WriteLine("Catalog.DeviceList为空或未初始化");
                    Debug.WriteLine($"Catalog.DeviceList == null: {Catalog.DeviceList == null}");
                    if (Catalog.DeviceList != null)
                    {
                        Debug.WriteLine($"Catalog.DeviceList.Count: {Catalog.DeviceList.Count}");
                    }

                    // 尝试手动导入GSDML文件
                    Debug.WriteLine("尝试手动导入GSDML文件...");
                    bool importSuccess = TryImportGSDMLFile(gsdmlFileName);
                    if (importSuccess)
                    {
                        Debug.WriteLine("手动导入GSDML文件成功，重新检查Catalog.DeviceList");
                        Debug.WriteLine($"导入后Catalog.DeviceList.Count: {Catalog.DeviceList?.Count ?? 0}");
                    }
                    else
                    {
                        Debug.WriteLine("手动导入GSDML文件失败");
                        return Enumerable.Empty<KeyValuePair<string, ModuleCatalog>>();
                    }

                    // 如果导入后仍然为空，返回空结果
                    if (Catalog.DeviceList == null || !Catalog.DeviceList.Any())
                    {
                        return Enumerable.Empty<KeyValuePair<string, ModuleCatalog>>();
                    }
                }

                Debug.WriteLine($"Catalog.DeviceList包含 {Catalog.DeviceList.Count} 个设备");

                // 构建设备的键，格式为 "文件名\设备GSDRefID"
                string deviceKey = CatalogHelper.GetGsdKeyByGsdName(gsdmlFileName, deviceGsdRefId);
                Debug.WriteLine($"查找设备键: {deviceKey}");

                // 优化：使用高效的大小写不敏感查找
                string? matchingDeviceKey = FindMatchingKey(Catalog.DeviceList.Keys, deviceKey);

                if (matchingDeviceKey != null)
                {
                    deviceKey = matchingDeviceKey;
                    Debug.WriteLine($"找到匹配的设备键: {deviceKey}");
                }
                else
                {
                    Debug.WriteLine($"在Catalog.DeviceList中未找到设备键: {deviceKey}");
                    Debug.WriteLine("可用的设备键:");
                    foreach (var key in Catalog.DeviceList.Keys.Take(10))
                    {
                        Debug.WriteLine($"  - {key}");
                    }
                    Debug.WriteLine($"=== GetDeviceModulesFromCatalog 失败：未找到设备键 ===");
                    return Enumerable.Empty<KeyValuePair<string, ModuleCatalog>>();
                }

                var deviceCatalog = Catalog.DeviceList[deviceKey];
                Debug.WriteLine($"找到设备目录，可插拔模块数量: {deviceCatalog.PluggableModuleList?.Count ?? 0}");

                if (deviceCatalog.PluggableModuleList == null || !deviceCatalog.PluggableModuleList.Any())
                {
                    Debug.WriteLine("该设备没有可插拔模块");
                    Debug.WriteLine($"=== GetDeviceModulesFromCatalog 失败：没有可插拔模块 ===");
                    return Enumerable.Empty<KeyValuePair<string, ModuleCatalog>>();
                }

                Debug.WriteLine("可插拔模块列表:");
                foreach (var pluggable in deviceCatalog.PluggableModuleList.Take(10))
                {
                    Debug.WriteLine($"  - {pluggable.Key}");
                }

                // 从可插拔模块列表获取模块目录对象
                var deviceModules = new List<KeyValuePair<string, ModuleCatalog>>();

                // 从设备键中提取正确的文件名（大小写正确）
                string correctFileName = deviceKey.Split('\\')[0];
                Debug.WriteLine($"使用正确的文件名: {correctFileName}");

                foreach (var pluggableModule in deviceCatalog.PluggableModuleList)
                {
                    string moduleKey = CatalogHelper.GetGsdKeyByGsdName(correctFileName, pluggableModule.Key);
                    Debug.WriteLine($"检查模块键: {moduleKey}");

                    // 优化：使用高效的大小写不敏感查找
                    string? matchingModuleKey = FindMatchingKey(Catalog.ModuleList.Keys, moduleKey);
                    Debug.WriteLine($"查找模块键: {moduleKey}");

                    if (matchingModuleKey != null)
                    {
                        var moduleCatalog = Catalog.ModuleList[matchingModuleKey];
                        Debug.WriteLine($"找到匹配的模块键: {matchingModuleKey}");

                        if (!IsSystemDefinedModule(moduleCatalog))
                        {
                            // 使用模块键值作为显示名称，而不是从ModuleCatalog获取可能包含后缀的名称
                            deviceModules.Add(new KeyValuePair<string, ModuleCatalog>(pluggableModule.Key, moduleCatalog));
                            Debug.WriteLine($"添加用户可配置模块: {pluggableModule.Key}");
                        }
                        else
                        {
                            Debug.WriteLine($"跳过系统定义模块: {pluggableModule.Key}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"在Catalog.ModuleList中未找到模块键: {moduleKey}");
                    }
                }

                Debug.WriteLine($"过滤后剩余 {deviceModules.Count} 个用户可配置模块");

                if (deviceModules.Any())
                {
                    Debug.WriteLine("用户可配置模块列表:");
                    foreach (var module in deviceModules.Take(5)) // 只显示前5个
                    {
                        string moduleName = GetModuleDisplayName(module.Value);
                        Debug.WriteLine($"  - {module.Key}: {moduleName}");
                    }
                    Debug.WriteLine($"=== GetDeviceModulesFromCatalog 成功完成 ===");
                }
                else
                {
                    Debug.WriteLine($"=== GetDeviceModulesFromCatalog 失败：没有找到用户可配置模块 ===");
                }

                return deviceModules;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取设备模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== GetDeviceModulesFromCatalog 异常结束 ===");
                return Enumerable.Empty<KeyValuePair<string, ModuleCatalog>>();
            }
        }

        /// <summary>
        /// 尝试手动导入GSDML文件到Catalog
        /// </summary>
        private bool TryImportGSDMLFile(string gsdmlFileName)
        {
            try
            {
                // 获取当前设备配置以找到GSDML文件的完整路径
                var deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig == null)
                {
                    Debug.WriteLine("无法获取设备配置");
                    return false;
                }

                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                if (deviceNode == null || string.IsNullOrEmpty(deviceNode.GSDPath))
                {
                    Debug.WriteLine("无法获取GSDML文件路径");
                    return false;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath;
                string gsdmlPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);

                Debug.WriteLine($"尝试导入GSDML文件: {relativePath} -> {gsdmlPath}");

                if (!File.Exists(gsdmlPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdmlPath}");
                    return false;
                }

                // 检查文件是否已经导入
                string fileName = Path.GetFileName(gsdmlPath);
                if (Catalog.ImportedGSDMLList.Contains(fileName.ToUpperInvariant()))
                {
                    Debug.WriteLine($"GSDML文件已经导入: {fileName}");
                    return true;
                }

                // 使用PNConfigLib的导入器导入GSDML文件
                var consistencyManager = new PNConfigLib.Consistency.ConsistencyManager();
                bool importResult = PNConfigLib.GSDImport.Converter.ImportGSDML(gsdmlPath, consistencyManager);

                if (importResult)
                {
                    Debug.WriteLine($"成功导入GSDML文件: {fileName}");
                    Debug.WriteLine($"导入后Catalog.DeviceList.Count: {Catalog.DeviceList.Count}");
                    Debug.WriteLine($"导入后Catalog.ModuleList.Count: {Catalog.ModuleList.Count}");
                    Debug.WriteLine($"导入后Catalog.SubmoduleList.Count: {Catalog.SubmoduleList.Count}");
                }
                else
                {
                    Debug.WriteLine($"导入GSDML文件失败: {fileName}");
                }

                return importResult;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导入GSDML文件时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 判断是否为系统定义的模块（Interface、Port等）
        /// </summary>
        private bool IsSystemDefinedModule(ModuleCatalog moduleCatalog)
        {
            try
            {
                if (moduleCatalog?.AttributeAccess == null)
                {
                    return false;
                }

                var displayName = GetModuleDisplayName(moduleCatalog);

                // 检查模块名称是否包含系统关键字
                if (displayName.Contains("Interface", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("Port", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("DAP", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("System", StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }

                // 检查是否为虚拟模块
                var isVirtual = moduleCatalog.AttributeAccess.GetAnyAttribute<bool>("IsVirtual", null, false);
                if (isVirtual)
                {
                    return true;
                }

                // 检查其他可能的系统标识
                var isSystemDefined = moduleCatalog.AttributeAccess.GetAnyAttribute<bool>("IsSystemDefined", null, false);
                if (isSystemDefined)
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"判断系统定义模块时出错: {ex.Message}");
                return false; // 出错时不排除，保持原有行为
            }
        }

        /// <summary>
        /// 从Catalog获取指定设备的子模块列表
        /// </summary>
        private IEnumerable<KeyValuePair<string, SubmoduleCatalog>> GetDeviceSubmodulesFromCatalog(string gsdmlFileName, string deviceGsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== 开始获取设备子模块 ===");
                Debug.WriteLine($"GSDML文件名: {gsdmlFileName}");
                Debug.WriteLine($"设备GSD引用ID: {deviceGsdRefId}");

                if (Catalog.DeviceList == null || !Catalog.DeviceList.Any())
                {
                    Debug.WriteLine("Catalog.DeviceList为空或未初始化");
                    return Enumerable.Empty<KeyValuePair<string, SubmoduleCatalog>>();
                }

                // 构建设备的键，格式为 "文件名\设备GSDRefID"
                string deviceKey = CatalogHelper.GetGsdKeyByGsdName(gsdmlFileName, deviceGsdRefId);
                Debug.WriteLine($"查找设备键: {deviceKey}");

                // 优化：使用高效的大小写不敏感查找
                string? matchingDeviceKey = FindMatchingKey(Catalog.DeviceList.Keys, deviceKey);

                if (matchingDeviceKey != null)
                {
                    deviceKey = matchingDeviceKey;
                    Debug.WriteLine($"找到匹配的设备键: {deviceKey}");
                }
                else
                {
                    Debug.WriteLine($"在Catalog.DeviceList中未找到设备键: {deviceKey}");
                    Debug.WriteLine("可用的设备键:");
                    foreach (var key in Catalog.DeviceList.Keys.Take(10))
                    {
                        Debug.WriteLine($"  - {key}");
                    }
                    return Enumerable.Empty<KeyValuePair<string, SubmoduleCatalog>>();
                }

                var deviceCatalog = Catalog.DeviceList[deviceKey];
                Debug.WriteLine($"找到设备目录，可插拔子模块数量: {deviceCatalog.PluggableSubmoduleList?.Count ?? 0}");

                if (deviceCatalog.PluggableSubmoduleList == null || !deviceCatalog.PluggableSubmoduleList.Any())
                {
                    Debug.WriteLine("该设备没有可插拔子模块");
                    return Enumerable.Empty<KeyValuePair<string, SubmoduleCatalog>>();
                }

                // 从可插拔子模块列表获取子模块目录对象
                var deviceSubmodules = new List<KeyValuePair<string, SubmoduleCatalog>>();
                foreach (var pluggableSubmodule in deviceCatalog.PluggableSubmoduleList)
                {
                    string submoduleKey = CatalogHelper.GetGsdKeyByGsdName(gsdmlFileName, pluggableSubmodule.Key);

                    // 优化：使用高效的大小写不敏感查找
                    string? matchingSubmoduleKey = FindMatchingKey(Catalog.SubmoduleList.Keys, submoduleKey);
                    Debug.WriteLine($"查找子模块键: {submoduleKey}");

                    if (matchingSubmoduleKey != null)
                    {
                        var submoduleCatalog = Catalog.SubmoduleList[matchingSubmoduleKey];
                        Debug.WriteLine($"找到匹配的子模块键: {matchingSubmoduleKey}");
                        if (!IsSystemDefinedSubmodule(submoduleCatalog))
                        {
                            deviceSubmodules.Add(new KeyValuePair<string, SubmoduleCatalog>(pluggableSubmodule.Key, submoduleCatalog));
                        }
                    }
                }

                Debug.WriteLine($"过滤后剩余 {deviceSubmodules.Count} 个用户可配置子模块");

                if (deviceSubmodules.Any())
                {
                    Debug.WriteLine("用户可配置子模块列表:");
                    foreach (var submodule in deviceSubmodules.Take(5)) // 只显示前5个
                    {
                        string submoduleName = GetSubmoduleDisplayName(submodule.Value);
                        Debug.WriteLine($"  - {submodule.Key}: {submoduleName}");
                    }
                }

                Debug.WriteLine($"=== 最终返回 {deviceSubmodules.Count} 个子模块 ===");
                return deviceSubmodules;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取设备子模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return Enumerable.Empty<KeyValuePair<string, SubmoduleCatalog>>();
            }
        }

        /// <summary>
        /// 判断是否为系统定义的子模块（Interface、Port等）
        /// </summary>
        private bool IsSystemDefinedSubmodule(SubmoduleCatalog submoduleCatalog)
        {
            try
            {
                if (submoduleCatalog?.AttributeAccess == null)
                {
                    Debug.WriteLine("子模块AttributeAccess为空，不排除");
                    return false;
                }

                var displayName = GetSubmoduleDisplayName(submoduleCatalog);
                Debug.WriteLine($"正在检查子模块是否为系统定义: {displayName}");

                // 列出所有可用属性以便调试
                ListAvailableAttributes(submoduleCatalog.AttributeAccess, $"子模块[{displayName}]");

                // 检查子槽号是否为系统保留的（通常32768及以上为系统保留）
                var subslotNumber = submoduleCatalog.AttributeAccess.GetAnyAttribute<uint>("PnSubslotNumber", null, 0);
                Debug.WriteLine($"子模块 {displayName} 的子槽号: {subslotNumber}");
                if (subslotNumber >= 32768)
                {
                    Debug.WriteLine($"检测到系统保留子槽号 {subslotNumber}，排除子模块: {displayName}");
                    return true;
                }

                // 检查子模块名称是否包含系统关键字
                if (displayName.Contains("Interface", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("Port", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("DAP", StringComparison.OrdinalIgnoreCase) ||
                    displayName.Contains("System", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.WriteLine($"根据名称检测到系统子模块，排除: {displayName}");
                    return true;
                }

                // 检查是否为虚拟子模块（VirtualSubmodule）
                var isVirtual = submoduleCatalog.AttributeAccess.GetAnyAttribute<bool>("IsVirtual", null, false);
                Debug.WriteLine($"子模块 {displayName} 的IsVirtual属性: {isVirtual}");
                if (isVirtual)
                {
                    Debug.WriteLine($"检测到虚拟子模块，排除: {displayName}");
                    return true;
                }

                // 检查其他可能的系统标识
                var isSystemDefined = submoduleCatalog.AttributeAccess.GetAnyAttribute<bool>("IsSystemDefined", null, false);
                Debug.WriteLine($"子模块 {displayName} 的IsSystemDefined属性: {isSystemDefined}");
                if (isSystemDefined)
                {
                    Debug.WriteLine($"检测到系统定义子模块，排除: {displayName}");
                    return true;
                }

                Debug.WriteLine($"子模块 {displayName} 不是系统定义的，保留");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"判断系统定义子模块时出错: {ex.Message}");
                return false; // 出错时不排除，保持原有行为
            }
        }

        /// <summary>
        /// 获取模块的显示名称
        /// </summary>
        private string GetModuleDisplayName(ModuleCatalog moduleCatalog)
        {
            try
            {
                if (moduleCatalog == null)
                {
                    Debug.WriteLine("ModuleCatalog为空");
                    return "未知模块";
                }

                // 尝试从AttributeAccess获取名称信息
                try
                {
                    // 按优先级顺序获取显示名称，使用正确的属性名
                    var nameTextId = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("NameTextId", null, string.Empty);
                    if (!string.IsNullOrEmpty(nameTextId))
                    {
                        Debug.WriteLine($"使用AttributeAccess.NameTextId获取模块名称: {nameTextId}");
                        return nameTextId;
                    }

                    var typeName = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("TypeName", null, string.Empty);
                    if (!string.IsNullOrEmpty(typeName))
                    {
                        Debug.WriteLine($"使用AttributeAccess.TypeName获取模块名称: {typeName}");
                        return typeName;
                    }

                    var description = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("Description", null, string.Empty);
                    if (!string.IsNullOrEmpty(description))
                    {
                        Debug.WriteLine($"使用AttributeAccess.Description获取模块名称: {description}");
                        return description;
                    }

                    var orderNumber = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("OrderNumber", null, string.Empty);
                    if (!string.IsNullOrEmpty(orderNumber))
                    {
                        Debug.WriteLine($"使用AttributeAccess.OrderNumber获取模块名称: {orderNumber}");
                        return orderNumber;
                    }

                    var modelName = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("ModelName", null, string.Empty);
                    if (!string.IsNullOrEmpty(modelName))
                    {
                        Debug.WriteLine($"使用AttributeAccess.ModelName获取模块名称: {modelName}");
                        return modelName;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"从AttributeAccess获取模块名称时出错: {ex.Message}");
                }

                // 尝试从AttributeAccess获取GsdId
                var gsdId = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("GsdId", null, string.Empty);
                if (!string.IsNullOrEmpty(gsdId))
                {
                    Debug.WriteLine($"使用AttributeAccess.GsdId获取模块名称: {gsdId}");
                    return gsdId;
                }

                // 尝试获取GSDFileName
                var gsdFileName = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("GSDFileName", null, string.Empty);
                if (!string.IsNullOrEmpty(gsdFileName))
                {
                    Debug.WriteLine($"使用AttributeAccess.GSDFileName获取模块名称: {gsdFileName}");
                    return gsdFileName;
                }

                // 最后尝试从AttributeAccess获取（作为后备）
                if (moduleCatalog.AttributeAccess != null)
                {
                    Debug.WriteLine("尝试从AttributeAccess获取模块名称");
                    ListAvailableAttributes(moduleCatalog.AttributeAccess, "模块");

                    var accessCode = new AttributeAccessCode();
                    string displayName = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("Name", accessCode, string.Empty);
                    if (!string.IsNullOrEmpty(displayName) && accessCode.IsOkay)
                    {
                        Debug.WriteLine($"使用AttributeAccess.Name获取模块名称: {displayName}");
                        return displayName;
                    }
                }

                Debug.WriteLine("所有方法都无法获取模块名称，返回默认名称");
                return "未命名模块";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块显示名称时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return "未知模块";
            }
        }

        /// <summary>
        /// 获取子模块的显示名称
        /// </summary>
        private string GetSubmoduleDisplayName(SubmoduleCatalog submoduleCatalog)
        {
            try
            {
                if (submoduleCatalog == null)
                {
                    Debug.WriteLine("SubmoduleCatalog为空");
                    return "未知子模块";
                }

                // 尝试从AttributeAccess获取名称信息
                try
                {
                    // 按优先级顺序获取显示名称，使用正确的属性名
                    var nameTextId = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("NameTextId", null, string.Empty);
                    if (!string.IsNullOrEmpty(nameTextId))
                    {
                        Debug.WriteLine($"使用AttributeAccess.NameTextId获取子模块名称: {nameTextId}");
                        return nameTextId;
                    }

                    var typeName = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("TypeName", null, string.Empty);
                    if (!string.IsNullOrEmpty(typeName))
                    {
                        Debug.WriteLine($"使用AttributeAccess.TypeName获取子模块名称: {typeName}");
                        return typeName;
                    }

                    var description = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("Description", null, string.Empty);
                    if (!string.IsNullOrEmpty(description))
                    {
                        Debug.WriteLine($"使用AttributeAccess.Description获取子模块名称: {description}");
                        return description;
                    }

                    var orderNumber = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("OrderNumber", null, string.Empty);
                    if (!string.IsNullOrEmpty(orderNumber))
                    {
                        Debug.WriteLine($"使用AttributeAccess.OrderNumber获取子模块名称: {orderNumber}");
                        return orderNumber;
                    }

                    var modelName = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("ModelName", null, string.Empty);
                    if (!string.IsNullOrEmpty(modelName))
                    {
                        Debug.WriteLine($"使用AttributeAccess.ModelName获取子模块名称: {modelName}");
                        return modelName;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"从AttributeAccess获取子模块名称时出错: {ex.Message}");
                }

                // 尝试从AttributeAccess获取GsdId
                var gsdId = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("GsdId", null, string.Empty);
                if (!string.IsNullOrEmpty(gsdId))
                {
                    Debug.WriteLine($"使用AttributeAccess.GsdId获取子模块名称: {gsdId}");
                    return gsdId;
                }

                // 尝试获取GSDFileName
                var gsdFileName = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("GSDFileName", null, string.Empty);
                if (!string.IsNullOrEmpty(gsdFileName))
                {
                    Debug.WriteLine($"使用AttributeAccess.GSDFileName获取子模块名称: {gsdFileName}");
                    return gsdFileName;
                }

                // 最后尝试从AttributeAccess获取（作为后备）
                if (submoduleCatalog.AttributeAccess != null)
                {
                    Debug.WriteLine("尝试从AttributeAccess获取子模块名称");
                    ListAvailableAttributes(submoduleCatalog.AttributeAccess, "子模块");

                    var accessCode = new AttributeAccessCode();
                    string displayName = submoduleCatalog.AttributeAccess.GetAnyAttribute<string>("Name", accessCode, string.Empty);
                    if (!string.IsNullOrEmpty(displayName) && accessCode.IsOkay)
                    {
                        Debug.WriteLine($"使用AttributeAccess.Name获取子模块名称: {displayName}");
                        return displayName;
                    }
                }

                Debug.WriteLine("所有方法都无法获取子模块名称，返回默认名称");
                return "未命名子模块";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取子模块显示名称时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return "未知子模块";
            }
        }

        /// <summary>
        /// 在指定的键集合中查找大小写不敏感的匹配键
        /// </summary>
        /// <param name="keys">要搜索的键集合</param>
        /// <param name="targetKey">目标键</param>
        /// <returns>匹配的键，如果未找到则返回null</returns>
        private string? FindMatchingKey(IEnumerable<string> keys, string targetKey)
        {
            if (string.IsNullOrEmpty(targetKey))
            {
                return null;
            }

            // 使用大小写不敏感的匹配
            return keys.FirstOrDefault(k => string.Equals(k, targetKey, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 列出AttributeAccess中所有可用的属性（用于调试）
        /// </summary>
        private void ListAvailableAttributes(AttributeAccess attributeAccess, string objectType)
        {
            try
            {
                if (attributeAccess?.AttributeList == null)
                {
                    Debug.WriteLine($"{objectType} AttributeList为空");
                    return;
                }

                Debug.WriteLine($"{objectType} 可用属性列表:");
                foreach (var kvp in attributeAccess.AttributeList)
                {
                    Debug.WriteLine($"  - {kvp.Key}: {kvp.Value}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"列出{objectType}属性时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断模块是否为DAP主模块
        /// DAP主模块是设备的根模块，不应该被添加到Modules配置中
        /// </summary>
        /// <param name="moduleIndex">模块在UI列表中的索引</param>
        /// <param name="position">模块位置</param>
        /// <returns>如果是DAP主模块返回true，否则返回false</returns>
        private bool IsDAPMainModule(int moduleIndex, string position)
        {
            // DAP主模块的特征：
            // 1. 在UI列表中的索引为0（第一个模块）
            // 2. 位置为"0"
            return moduleIndex == 0 && position == "0";
        }

        /// <summary>
        /// 加载模块配置
        /// </summary>
        private void LoadModulesConfiguration(DecentralDeviceConfig deviceConfig)
        {
            // 清空现有模块
            _modules.Clear();

            // 检查设备是否有模块配置
            if (deviceConfig.Modules == null)
            {
                deviceConfig.Modules = new List<ModuleConfig>();
            }

            // 如果没有模块，创建默认的主模块和接口模块
            bool needAddDefaultModules = deviceConfig.Modules.Count == 0;

            // 首先添加主模块（DAP）
            var mainModule = new ModuleViewModel
            {
                Index = 1,
                IsSelected = false, // 主模块默认不选中
                Position = "0",
                ModuleName = _deviceType,
                SubmoduleName = "主模块",
                InterfaceSlot = "0",
                PNIStartAddress = "0",
                IsBuiltIn = true // 主模块也是内置模块
            };
            AddModulePropertyChangedListener(mainModule);
            _modules.Add(mainModule);

            // 注意：DAP主模块不需要添加到deviceConfig.Modules配置中
            // DAP主模块仅在UI中显示，其配置保存在设备级别
            if (needAddDefaultModules)
            {
                Debug.WriteLine($"设备 {deviceConfig.DeviceRefID} 没有模块配置，DAP主模块将仅在UI中显示，不保存到Modules配置");
                // DAP主模块已在UI中添加，无需添加到deviceConfig.Modules
            }

            // 动态添加DAP内置模块（替换硬编码的Interface、Port1、Port2）
            AddDAPBuiltinModules(deviceConfig);

            // 获取对应的ListOfNodes设备节点以获取GSDML路径和引用ID
            var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

            if (deviceNode != null)
            {
                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                Debug.WriteLine($"LoadDeviceModules GSDML路径转换: {relativePath} -> {gsdPath}");

                // 获取物理插槽数量并确保有足够的空行供用户添加模块
                int physicalSlotsCount = GetPhysicalSlotsCount(gsdPath, gsdRefId);
                if (physicalSlotsCount > 0)
                {
                    Debug.WriteLine($"获取到物理插槽数量: {physicalSlotsCount}，确保表格有足够的行数");
                    EnsureSlotRowsForPhysicalSlots(physicalSlotsCount);
                }
                else
                {
                    Debug.WriteLine("无法获取物理插槽数量，使用默认行数");
                }
            }

            // 加载已配置的额外模块（如果有）- 按槽位顺序填充到对应位置
            if (deviceConfig.Modules.Count > 0)
            {
                Debug.WriteLine($"从配置文件加载 {deviceConfig.Modules.Count} 个模块到对应插槽位置");

                // 按SlotNumber排序，确保按槽位顺序加载
                var sortedModules = deviceConfig.Modules.OrderBy(m => m.SlotNumber).ToList();

                foreach (var module in sortedModules)
                {
                    // 查找对应的插槽位置
                    var targetSlot = _modules.FirstOrDefault(m => m.Position == module.SlotNumber.ToString());

                    if (targetSlot != null)
                    {
                        // 从GSDML文件中提取模块的IO长度信息
                        var moduleInfo = ExtractModuleInfoFromGSDML(module.GSDRefID ?? string.Empty, module.ModuleRefID);
                        var firstSubmodule = module.Submodules.Count > 0 ? module.Submodules[0] : null;

                        // 使用显示名称作为ModuleName，如果无法获取则回退到ModuleRefID
                        string displayName = moduleInfo?.DisplayName ?? module.ModuleRefID;

                        Debug.WriteLine($"加载模块到插槽 {module.SlotNumber}: {module.ModuleRefID}");
                        Debug.WriteLine($"  GSDRefID: {module.GSDRefID}");
                        Debug.WriteLine($"  DisplayName: {displayName}");
                        Debug.WriteLine($"  提取的IO长度: Input={moduleInfo?.InputLength ?? 0}, Output={moduleInfo?.OutputLength ?? 0}");

                        // 更新现有插槽的模块信息
                        targetSlot.ModuleName = displayName;
                        targetSlot.SubmoduleName = firstSubmodule?.SubmoduleRefID ?? "";
                        targetSlot.InterfaceSlot = module.SlotNumber.ToString();
                        targetSlot.PNIStartAddress = firstSubmodule?.AddressConfiguration.InputStartAddress ?? "0";
                        targetSlot.InputStartAddress = firstSubmodule?.AddressConfiguration.InputStartAddress ?? "0";
                        targetSlot.PNQStartAddress = firstSubmodule?.AddressConfiguration.OutputStartAddress ?? "0";
                        targetSlot.OutputStartAddress = firstSubmodule?.AddressConfiguration.OutputStartAddress ?? "0";
                        targetSlot.GSDRefID = module.GSDRefID ?? "";
                        targetSlot.ModuleID = module.ModuleRefID;
                        targetSlot.InputLength = moduleInfo?.InputLength ?? 0;
                        targetSlot.OutputLength = moduleInfo?.OutputLength ?? 0;
                        targetSlot.IsBuiltIn = false; // 用户配置的模块不是内置模块

                        Debug.WriteLine($"已更新插槽 {module.SlotNumber} 的模块信息: {displayName} (ID: {module.ModuleRefID})");
                        Debug.WriteLine($"  输入地址: {targetSlot.InputStartAddress} (长度: {targetSlot.InputLength})");
                        Debug.WriteLine($"  输出地址: {targetSlot.OutputStartAddress} (长度: {targetSlot.OutputLength})");
                        Debug.WriteLine($"模块导航菜单将由MainWindow.RebuildDeviceNavigationMenu统一处理");
                    }
                    else
                    {
                        Debug.WriteLine($"警告：找不到插槽 {module.SlotNumber} 对应的行，跳过模块 {module.ModuleRefID}");
                    }
                }
            }

            // 重新排序模块并修正Index值，确保显示顺序正确
            ReorderModulesAndFixIndexes();

            // 重新计算所有模块的地址分配（保持现有地址，只填补空缺）
            RecalculateAllModuleAddresses(forceRecalculate: false);

            // 完成模块初始化，启用地址验证
            FinishModuleInitialization();

            // 验证当前配置的IO长度是否超出限制
            ValidateCurrentIOLengthLimits();

            Debug.WriteLine($"已加载设备 {deviceConfig.DeviceRefID} 的模块配置，共 {_modules.Count} 个模块，地址已重新计算");
        }

        /// <summary>
        /// 重新排序模块并修正Index值，确保显示顺序正确
        /// </summary>
        private void ReorderModulesAndFixIndexes()
        {
            try
            {
                Debug.WriteLine("=== ReorderModulesAndFixIndexes 开始 ===");

                // 按Position排序，确保插槽顺序正确
                var sortedModules = _modules
                    .OrderBy(m => {
                        if (int.TryParse(m.Position, out int pos))
                            return pos;
                        return m.Position == "-" ? -1 : int.MaxValue; // 内置模块排在前面
                    })
                    .ToList();

                // 清空原列表并重新添加排序后的模块
                _modules.Clear();

                for (int i = 0; i < sortedModules.Count; i++)
                {
                    sortedModules[i].Index = i + 1; // 重新设置Index
                    _modules.Add(sortedModules[i]);
                    Debug.WriteLine($"重新排序模块 {i + 1}: Position={sortedModules[i].Position}, ModuleName={sortedModules[i].ModuleName}");
                }

                Debug.WriteLine($"模块重新排序完成，总数: {_modules.Count}");
                Debug.WriteLine("=== ReorderModulesAndFixIndexes 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重新排序模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 加载实时设置配置
        /// </summary>
        private void LoadRealTimeSettings(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                // 确保高级配置结构存在
                if (deviceConfig.DecentralDeviceInterface.AdvancedConfiguration == null)
                {
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration = new DecentralAdvancedConfigurationConfig();
                }

                var realTimeSettings = deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.RealTimeSettings;
                if (realTimeSettings == null)
                {
                    realTimeSettings = new DecentralRealTimeSettingsConfig();
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.RealTimeSettings = realTimeSettings;
                }

                // 加载更新时间设置
                _updateTime = realTimeSettings.IOCycle.UpdateTime;

                // 加载数据保持时间设置
                _acceptedUpdateCyclesWithoutIOData = realTimeSettings.IOCycle.AcceptedUpdateCyclesWithoutIOData;

                // 确保更新时间模式为Manual（因为我们提供了具体的值）
                _updateTime.Mode = UpdateTimeMode.Manual;

                // 设置数据上下文以支持绑定
                this.DataContext = this;

                Debug.WriteLine($"已加载实时设置 - 更新时间: {_updateTime.Value}ms (模式: {_updateTime.Mode}), 数据保持: {_acceptedUpdateCyclesWithoutIOData}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载实时设置时出错: {ex.Message}");
                // 使用默认值
                _updateTime = new UpdateTimeConfig { Mode = UpdateTimeMode.Manual, Value = 128.0f };
                _acceptedUpdateCyclesWithoutIOData = 3;
                this.DataContext = this;
                Debug.WriteLine($"使用默认实时设置 - 更新时间: {_updateTime.Value}ms (模式: {_updateTime.Mode}), 数据保持: {_acceptedUpdateCyclesWithoutIOData}");
            }
        }

        /// <summary>
        /// 加载MRP配置
        /// </summary>
        private void LoadMRPConfiguration(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                Debug.WriteLine("=== LoadMRPConfiguration 开始 ===");

                // 获取对应的ListOfNodes设备节点以获取GSDML路径
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                if (deviceNode == null)
                {
                    Debug.WriteLine("未找到对应的设备节点");
                    return;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                Debug.WriteLine($"GSDML相对路径: {relativePath}");
                Debug.WriteLine($"GSDML绝对路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine("GSDML文件不存在，使用默认MRP配置");
                    InitializeDefaultMRPConfiguration();
                    return;
                }

                // 从GSDML文件中提取MRP配置
                var mrpConfig = ExtractMRPConfigurationFromGSDML(gsdPath, gsdRefId);
                if (mrpConfig != null && mrpConfig.SupportedRoles != null && mrpConfig.SupportedRoles.Count > 0)
                {
                    // 设备支持MRP，显示MRP控件
                    ShowMRPControls(true);

                    // 初始化MediaRedundancyRoleComboBox
                    InitializeMediaRedundancyRoleComboBox(mrpConfig.SupportedRoles);

                    // 初始化RingPort ComboBox
                    InitializeRingPortComboBoxes(mrpConfig.SupportedRingPorts);

                    // 加载已保存的MRP配置
                    LoadSavedMRPConfiguration(deviceConfig);

                    Debug.WriteLine($"成功加载MRP配置 - 支持的角色: {mrpConfig.SupportedRoles?.Count ?? 0}, 支持的环端口: {mrpConfig.SupportedRingPorts?.Count ?? 0}");
                }
                else
                {
                    // 设备不支持MRP，隐藏MRP控件并显示提示
                    ShowMRPControls(false);
                    Debug.WriteLine("设备不支持MRP，显示不支持提示");
                }

                Debug.WriteLine("=== LoadMRPConfiguration 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载MRP配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                InitializeDefaultMRPConfiguration();
            }
        }

        /// <summary>
        /// 加载UpdateTime配置（优化版本，使用缓存）
        /// </summary>
        private void LoadUpdateTimeConfiguration(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                Debug.WriteLine("=== LoadUpdateTimeConfiguration 开始 ===");

                // 获取对应的ListOfNodes设备节点以获取GSDML路径
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);

                if (deviceNode == null)
                {
                    Debug.WriteLine($"未找到设备节点: {deviceConfig.DeviceRefID}");
                    InitializeUpdateTimeComboBox(_defaultReductionRatios);
                    return;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager?.CurrentProjectFilePath ?? "");
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                // 创建缓存键
                string cacheKey = $"{gsdPath}|{gsdRefId}";

                Debug.WriteLine($"GSDML相对路径: {relativePath}");
                Debug.WriteLine($"GSDML绝对路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");
                Debug.WriteLine($"缓存键: {cacheKey}");

                // 检查缓存
                if (_updateTimeCache.TryGetValue(cacheKey, out var cachedRatios))
                {
                    Debug.WriteLine($"从缓存加载UpdateTime配置 - 支持的ReductionRatio: {cachedRatios.Count}");
                    InitializeUpdateTimeComboBox(cachedRatios);
                }
                else
                {
                    // 缓存未命中，从GSDML文件中提取配置
                    Debug.WriteLine("缓存未命中，从GSDML文件提取UpdateTime配置");
                    var extractedRatios = ExtractReductionRatiosFromGSDML(gsdPath, gsdRefId);

                    if (extractedRatios != null && extractedRatios.Count > 0)
                    {
                        // 缓存提取的结果
                        _updateTimeCache[cacheKey] = extractedRatios;
                        InitializeUpdateTimeComboBox(extractedRatios);
                        Debug.WriteLine($"成功提取并缓存UpdateTime配置 - 支持的ReductionRatio: {extractedRatios.Count}");
                    }
                    else
                    {
                        // 提取失败，使用默认配置并缓存
                        _updateTimeCache[cacheKey] = _defaultReductionRatios;
                        InitializeUpdateTimeComboBox(_defaultReductionRatios);
                        Debug.WriteLine("提取失败，使用默认配置");
                    }
                }

                Debug.WriteLine("=== LoadUpdateTimeConfiguration 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载UpdateTime配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                InitializeUpdateTimeComboBox(_defaultReductionRatios);
            }
        }

        /// <summary>
        /// 从GSDML文件中提取ReductionRatio值（简化版本）
        /// </summary>
        private List<uint>? ExtractReductionRatiosFromGSDML(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"开始提取ReductionRatio - 路径: {gsdPath}, RefID: {gsdRefId}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return null;
                }

                // 获取DAP
                var deviceAccessPoints = interpreter.GetDeviceAccessPoints();
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("无法获取设备访问点");
                    return null;
                }

                // 查找目标DAP
                PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint? targetDAP = null;
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint deviceAccessPoint &&
                        deviceAccessPoint.GsdID?.ToUpperInvariant() == gsdRefId?.ToUpperInvariant())
                    {
                        targetDAP = deviceAccessPoint;
                        Debug.WriteLine($"找到目标DAP: {deviceAccessPoint.GsdID}");
                        break;
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine($"未找到匹配的DAP: {gsdRefId}");
                    return null;
                }

                var reductionRatios = new List<uint>();

                // 检查DAP是否有系统定义的子模块
                if (targetDAP.HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDInterface))
                {
                    var interfaceSubmodules = targetDAP.GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDInterface);
                    if (interfaceSubmodules != null && interfaceSubmodules.Length > 0)
                    {
                        foreach (var interfaceSubmodule in interfaceSubmodules)
                        {
                            if (interfaceSubmodule is PNConfigLib.Gsd.Interpreter.Common.InterfaceSubmodule interfaceSubmoduleTyped)
                            {
                                // 检查ApplicationRelations和TimingProperties
                                if (interfaceSubmoduleTyped.ApplicationRelations?.TimingProperties?.ReductionRatio != null)
                                {
                                    var ratios = interfaceSubmoduleTyped.ApplicationRelations.TimingProperties.ReductionRatio;
                                    foreach (var ratio in ratios)
                                    {
                                        if (ratio is uint ratioValue)
                                        {
                                            reductionRatios.Add(ratioValue);
                                            Debug.WriteLine($"提取到ReductionRatio: {ratioValue}");
                                        }
                                    }
                                }
                                break; // 只处理第一个InterfaceSubmodule
                            }
                        }
                    }
                }

                if (reductionRatios.Count > 0)
                {
                    // 去重并排序
                    var uniqueRatios = reductionRatios.Distinct().OrderBy(r => r).ToList();
                    Debug.WriteLine($"提取完成，共{uniqueRatios.Count}个唯一ReductionRatio值");
                    return uniqueRatios;
                }
                else
                {
                    Debug.WriteLine("未找到ReductionRatio值");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取ReductionRatio时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// MRP配置信息
        /// </summary>
        private class MRPConfiguration
        {
            public List<string>? SupportedRoles { get; set; }
            public List<string>? SupportedRingPorts { get; set; }
        }

        /// <summary>
        /// 更新时间配置信息
        /// </summary>
        private class UpdateTimeConfiguration
        {
            public List<uint>? SupportedReductionRatios { get; set; }
        }

        /// <summary>
        /// UpdateTime配置缓存，避免重复解析GSDML文件
        /// </summary>
        private static readonly Dictionary<string, List<uint>> _updateTimeCache = new Dictionary<string, List<uint>>();

        /// <summary>
        /// 默认的ReductionRatio值
        /// </summary>
        private static readonly List<uint> _defaultReductionRatios = new List<uint> { 4, 8, 16, 32, 64, 128 };

        /// <summary>
        /// 从GSDML文件中提取MRP配置
        /// </summary>
        private MRPConfiguration? ExtractMRPConfigurationFromGSDML(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== ExtractMRPConfigurationFromGSDML 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return null;
                }

                // 注意：MRP配置需要Common模型，确保缓存的Interpreter已加载Common模型
                // 如果需要，可以重新加载包含Common模型的版本
                bool hasCommonModel = true; // 假设缓存的Interpreter已包含所需模型
                if (!hasCommonModel)
                {
                    bool loadResult = interpreter.AssignGsd(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure, "en", false);
                    if (!loadResult)
                    {
                        Debug.WriteLine($"无法重新加载GSDML文件的Common模型: {gsdPath}");
                        return null;
                    }
                }

                // 获取DAP
                var deviceAccessPoints = interpreter.GetDeviceAccessPoints();
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("无法获取设备访问点");
                    return null;
                }

                // 查找匹配的DAP
                PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint? targetDAP = null;
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint deviceAccessPoint)
                    {
                        if (deviceAccessPoint.GsdID == gsdRefId)
                        {
                            targetDAP = deviceAccessPoint;
                            Debug.WriteLine($"找到匹配的DAP: {deviceAccessPoint.GsdID}");
                            break;
                        }
                    }
                }

                // 如果没有找到匹配的DAP，使用第一个DAP
                if (targetDAP == null && deviceAccessPoints.Length > 0)
                {
                    if (deviceAccessPoints.GetValue(0) is PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint firstDAP)
                    {
                        targetDAP = firstDAP;
                        Debug.WriteLine($"使用第一个DAP: {firstDAP.GsdID}");
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine("无法找到有效的DAP");
                    return null;
                }

                var mrpConfig = new MRPConfiguration
                {
                    SupportedRoles = new List<string>(),
                    SupportedRingPorts = new List<string>()
                };

                // 提取MrSupportedRoles
                ExtractMrSupportedRoles(targetDAP, mrpConfig);

                // 提取SupportsRingportConfig
                ExtractSupportsRingportConfig(targetDAP, mrpConfig);

                Debug.WriteLine($"=== ExtractMRPConfigurationFromGSDML 完成 ===");
                return mrpConfig;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取MRP配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return null;
            }
        }





        /// <summary>
        /// 提取MrSupportedRoles
        /// </summary>
        private void ExtractMrSupportedRoles(PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint targetDAP, MRPConfiguration mrpConfig)
        {
            try
            {
                Debug.WriteLine("开始提取MrSupportedRoles");

                // 检查DAP是否有系统定义的子模块
                if (targetDAP.HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDInterface))
                {
                    var interfaceSubmodules = targetDAP.GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDInterface);
                    if (interfaceSubmodules != null && interfaceSubmodules.Length > 0)
                    {
                        foreach (var submodule in interfaceSubmodules)
                        {
                            if (submodule is PNConfigLib.Gsd.Interpreter.Common.InterfaceSubmodule interfaceSubmodule)
                            {
                                Debug.WriteLine($"找到InterfaceSubmodule: {interfaceSubmodule.Name}");

                                // 检查MediaRedundancy属性
                                if (interfaceSubmodule.MediaRedundancy != null)
                                {
                                    Debug.WriteLine("找到MediaRedundancy配置");

                                    // 获取MrSupportedRoles
                                    var supportedRoles = interfaceSubmodule.MediaRedundancy.MR_SupportedRoles;
                                    if (supportedRoles != null && supportedRoles.Length > 0)
                                    {
                                        foreach (var role in supportedRoles)
                                        {
                                            string roleString = role?.ToString() ?? "";
                                            if (!string.IsNullOrEmpty(roleString))
                                            {
                                                mrpConfig.SupportedRoles?.Add(roleString);
                                                Debug.WriteLine($"添加支持的MRP角色: {roleString}");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        Debug.WriteLine("未找到MrSupportedRoles");
                                    }
                                }
                                else
                                {
                                    Debug.WriteLine("未找到MediaRedundancy配置");
                                }
                                break; // 只处理第一个InterfaceSubmodule
                            }
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("DAP没有InterfaceSubmodule，设备不支持MRP");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取MrSupportedRoles时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取SupportsRingportConfig
        /// </summary>
        private void ExtractSupportsRingportConfig(PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint targetDAP, MRPConfiguration mrpConfig)
        {
            try
            {
                Debug.WriteLine("开始提取SupportsRingportConfig");

                // 检查DAP是否有系统定义的端口子模块
                if (targetDAP.HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDPort))
                {
                    var portSubmodules = targetDAP.GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes.GSDPort);
                    if (portSubmodules != null && portSubmodules.Length > 0)
                    {
                        foreach (var submodule in portSubmodules)
                        {
                            if (submodule is PNConfigLib.Gsd.Interpreter.Common.PortSubmodule portSubmodule)
                            {
                                Debug.WriteLine($"找到PortSubmodule: {portSubmodule.Name}");

                                // 检查SupportsRingportConfig属性
                                if (portSubmodule.SupportsRingportConfig)
                                {
                                    string portName = portSubmodule.Name ?? $"Port_{portSubmodule.SubslotNumber}";
                                    mrpConfig.SupportedRingPorts?.Add(portName);
                                    Debug.WriteLine($"添加支持环配置的端口: {portName}");
                                }
                            }
                        }
                    }
                }

                // 如果没有找到支持环配置的端口，添加默认端口
                if (mrpConfig.SupportedRingPorts?.Count == 0)
                {
                    Debug.WriteLine("未找到支持环配置的端口，添加默认端口");
                    mrpConfig.SupportedRingPorts?.Add("Port1");
                    mrpConfig.SupportedRingPorts?.Add("Port2");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取SupportsRingportConfig时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示或隐藏MRP控件
        /// </summary>
        private void ShowMRPControls(bool showControls)
        {
            try
            {
                if (showControls)
                {
                    // 显示MRP控件，隐藏不支持提示
                    MRPControlsPanel.Visibility = Visibility.Visible;
                    MRPNotSupportedTextBlock.Visibility = Visibility.Collapsed;
                    Debug.WriteLine("显示MRP控件");
                }
                else
                {
                    // 隐藏MRP控件，显示不支持提示
                    MRPControlsPanel.Visibility = Visibility.Collapsed;
                    MRPNotSupportedTextBlock.Visibility = Visibility.Visible;
                    Debug.WriteLine("隐藏MRP控件，显示不支持提示");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"显示/隐藏MRP控件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化默认MRP配置
        /// </summary>
        private void InitializeDefaultMRPConfiguration()
        {
            try
            {
                Debug.WriteLine("初始化默认MRP配置");

                // 清空现有项目
                MediaRedundancyRoleComboBox.Items.Clear();
                RingPort1ComboBox.Items.Clear();
                RingPort2ComboBox.Items.Clear();

                // 添加默认的MRP角色
                var defaultRoleItem = new ComboBoxItem { Content = "Not device in the ring", IsSelected = true };
                MediaRedundancyRoleComboBox.Items.Add(defaultRoleItem);

                // 添加默认的环端口
                var defaultPort1Item = new ComboBoxItem { Content = "Port1" };
                var defaultPort2Item = new ComboBoxItem { Content = "Port2" };
                RingPort1ComboBox.Items.Add(defaultPort1Item);
                RingPort1ComboBox.Items.Add(defaultPort2Item);

                var defaultPort1Item2 = new ComboBoxItem { Content = "Port1" };
                var defaultPort2Item2 = new ComboBoxItem { Content = "Port2" };
                RingPort2ComboBox.Items.Add(defaultPort1Item2);
                RingPort2ComboBox.Items.Add(defaultPort2Item2);

                // 确保两个ComboBox选择不同的默认值
                EnsureRingPortsHaveDifferentDefaults();

                Debug.WriteLine("默认MRP配置初始化完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化默认MRP配置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化MediaRedundancyRoleComboBox
        /// </summary>
        private void InitializeMediaRedundancyRoleComboBox(List<string>? supportedRoles)
        {
            try
            {
                Debug.WriteLine("初始化MediaRedundancyRoleComboBox");

                // 清空现有项目
                MediaRedundancyRoleComboBox.Items.Clear();

                // 添加默认的"Not device in the ring"选项
                var defaultItem = new ComboBoxItem { Content = "Not device in the ring", IsSelected = true };
                MediaRedundancyRoleComboBox.Items.Add(defaultItem);

                // 添加从GSDML提取的支持角色
                if (supportedRoles != null && supportedRoles.Count > 0)
                {
                    foreach (var role in supportedRoles)
                    {
                        // 转换角色名称为用户友好的显示名称
                        string displayName = ConvertMrpRoleToDisplayName(role);
                        if (!string.IsNullOrEmpty(displayName) && displayName != "Not device in the ring")
                        {
                            var roleItem = new ComboBoxItem { Content = displayName };
                            MediaRedundancyRoleComboBox.Items.Add(roleItem);
                            Debug.WriteLine($"添加MRP角色: {displayName}");
                        }
                    }
                }

                Debug.WriteLine($"MediaRedundancyRoleComboBox初始化完成，共{MediaRedundancyRoleComboBox.Items.Count}个选项");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化MediaRedundancyRoleComboBox时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化UpdateTimeComboBox（优化版本）
        /// </summary>
        private void InitializeUpdateTimeComboBox(List<uint> reductionRatios)
        {
            try
            {
                Debug.WriteLine("=== InitializeUpdateTimeComboBox 开始 ===");

                // 使用Dispatcher确保在UI线程上执行
                if (UpdateTimeComboBox.Dispatcher.CheckAccess())
                {
                    PopulateUpdateTimeComboBox(reductionRatios);
                }
                else
                {
                    UpdateTimeComboBox.Dispatcher.Invoke(() => PopulateUpdateTimeComboBox(reductionRatios));
                }

                Debug.WriteLine($"UpdateTimeComboBox初始化完成，共{reductionRatios.Count}个选项");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化UpdateTimeComboBox时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 填充UpdateTimeComboBox内容
        /// </summary>
        private void PopulateUpdateTimeComboBox(List<uint> reductionRatios)
        {
            // 暂停事件处理以提高性能
            UpdateTimeComboBox.SelectionChanged -= OnUpdateTimeChanged;

            try
            {
                // 清空现有项目
                UpdateTimeComboBox.Items.Clear();

                // 批量创建ComboBoxItem
                var items = new List<ComboBoxItem>(reductionRatios.Count);
                foreach (var ratio in reductionRatios)
                {
                    var item = new ComboBoxItem
                    {
                        Content = ratio.ToString(),
                        Tag = (float)ratio
                    };
                    items.Add(item);
                }

                // 批量添加到ComboBox
                foreach (var item in items)
                {
                    UpdateTimeComboBox.Items.Add(item);
                }

                Debug.WriteLine($"添加了{items.Count}个更新时间选项");
            }
            finally
            {
                // 恢复事件处理
                UpdateTimeComboBox.SelectionChanged += OnUpdateTimeChanged;
            }
        }

        /// <summary>
        /// 初始化AcceptedUpdateCyclesWithoutIODataComboBox（数据保持下拉框）
        /// </summary>
        private void InitializeAcceptedUpdateCyclesComboBox()
        {
            try
            {
                Debug.WriteLine("=== InitializeAcceptedUpdateCyclesComboBox 开始 ===");

                // 使用Dispatcher确保在UI线程上执行
                if (AcceptedUpdateCyclesWithoutIODataComboBox.Dispatcher.CheckAccess())
                {
                    PopulateAcceptedUpdateCyclesComboBox();
                }
                else
                {
                    AcceptedUpdateCyclesWithoutIODataComboBox.Dispatcher.Invoke(() => PopulateAcceptedUpdateCyclesComboBox());
                }

                Debug.WriteLine("AcceptedUpdateCyclesComboBox初始化完成，共253个选项（3-255）");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化AcceptedUpdateCyclesComboBox时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 填充AcceptedUpdateCyclesWithoutIODataComboBox内容（3-255范围）
        /// </summary>
        private void PopulateAcceptedUpdateCyclesComboBox()
        {
            // 暂停事件处理以提高性能
            AcceptedUpdateCyclesWithoutIODataComboBox.SelectionChanged -= OnAcceptedUpdateCyclesChanged;

            try
            {
                // 清空现有项目
                AcceptedUpdateCyclesWithoutIODataComboBox.Items.Clear();

                // 批量创建ComboBoxItem（3-255范围）
                var items = new List<ComboBoxItem>(253); // 255-3+1 = 253个选项
                for (uint i = 3; i <= 255; i++)
                {
                    var item = new ComboBoxItem
                    {
                        Content = i.ToString(),
                        Tag = i
                    };
                    items.Add(item);
                }

                // 批量添加到ComboBox
                foreach (var item in items)
                {
                    AcceptedUpdateCyclesWithoutIODataComboBox.Items.Add(item);
                }

                // 设置当前选中值（如果存在）
                SetAcceptedUpdateCyclesSelectedValue(_acceptedUpdateCyclesWithoutIOData);

                Debug.WriteLine($"添加了{items.Count}个数据保持选项（3-255），当前选中值: {_acceptedUpdateCyclesWithoutIOData}");
            }
            finally
            {
                // 恢复事件处理
                AcceptedUpdateCyclesWithoutIODataComboBox.SelectionChanged += OnAcceptedUpdateCyclesChanged;
            }
        }

        /// <summary>
        /// 设置AcceptedUpdateCyclesComboBox的选中值
        /// </summary>
        private void SetAcceptedUpdateCyclesSelectedValue(uint value)
        {
            try
            {
                foreach (ComboBoxItem item in AcceptedUpdateCyclesWithoutIODataComboBox.Items)
                {
                    if (item.Tag is uint tagValue && tagValue == value)
                    {
                        AcceptedUpdateCyclesWithoutIODataComboBox.SelectedItem = item;
                        Debug.WriteLine($"设置数据保持选中值为: {value}");
                        return;
                    }
                }

                // 如果没有找到匹配的值，选择默认值3
                if (AcceptedUpdateCyclesWithoutIODataComboBox.Items.Count > 0)
                {
                    AcceptedUpdateCyclesWithoutIODataComboBox.SelectedIndex = 0; // 选择第一个选项（值为3）
                    Debug.WriteLine($"未找到匹配值 {value}，使用默认值3");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置数据保持选中值时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// UpdateTime选择变更事件处理
        /// </summary>
        private void OnUpdateTimeChanged(object sender, SelectionChangedEventArgs e)
        {
            // 处理更新时间变更逻辑
            if (UpdateTimeComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is float value)
            {
                UpdateTimeValue = value;
                Debug.WriteLine($"更新时间已变更为: {value}ms");
            }
        }

        /// <summary>
        /// 数据保持选择变化事件处理
        /// </summary>
        private void OnAcceptedUpdateCyclesChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!_isInitialized) return; // 避免在初始化时触发保存

            try
            {
                if (AcceptedUpdateCyclesWithoutIODataComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is uint selectedValue)
                {
                    AcceptedUpdateCyclesWithoutIOData = selectedValue;
                    Debug.WriteLine($"数据保持已更改为: {selectedValue}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理数据保持变化时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化RingPort ComboBox
        /// </summary>
        private void InitializeRingPortComboBoxes(List<string>? supportedRingPorts)
        {
            try
            {
                Debug.WriteLine("初始化RingPort ComboBox");

                // 清空现有项目
                RingPort1ComboBox.Items.Clear();
                RingPort2ComboBox.Items.Clear();

                // 添加从GSDML提取的支持端口
                if (supportedRingPorts != null && supportedRingPorts.Count > 0)
                {
                    foreach (var port in supportedRingPorts)
                    {
                        var port1Item = new ComboBoxItem { Content = port };
                        var port2Item = new ComboBoxItem { Content = port };

                        RingPort1ComboBox.Items.Add(port1Item);
                        RingPort2ComboBox.Items.Add(port2Item);

                        Debug.WriteLine($"添加环端口: {port}");
                    }
                }
                else
                {
                    // 如果没有从GSDML提取到端口，使用默认端口
                    var defaultPort1Item = new ComboBoxItem { Content = "Port1" };
                    var defaultPort2Item = new ComboBoxItem { Content = "Port2" };
                    RingPort1ComboBox.Items.Add(defaultPort1Item);
                    RingPort1ComboBox.Items.Add(defaultPort2Item);

                    var defaultPort1Item2 = new ComboBoxItem { Content = "Port1" };
                    var defaultPort2Item2 = new ComboBoxItem { Content = "Port2" };
                    RingPort2ComboBox.Items.Add(defaultPort1Item2);
                    RingPort2ComboBox.Items.Add(defaultPort2Item2);
                }

                // 确保两个ComboBox选择不同的默认值
                EnsureRingPortsHaveDifferentDefaults();

                Debug.WriteLine($"RingPort ComboBox初始化完成，共{RingPort1ComboBox.Items.Count}个选项");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化RingPort ComboBox时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 转换MRP角色为显示名称
        /// </summary>
        private string ConvertMrpRoleToDisplayName(string role)
        {
            return role switch
            {
                // 从GSD读取的角色名称
                "GSDManager" => "MrpManager",
                "GSDManagerAuto" => "MrpAutoManager",
                "GSDClient" => "MrpClient",

                // 可能的角色名称格式
                "Manager" => "MrpManager",
                "Client" => "MrpClient",
                "NormManagerAuto" => "MrpAutoManager",

                _ => role
            };
        }

        /// <summary>
        /// 处理模块表格选择变更事件
        /// </summary>
        private void ModulesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 不再基于行选择更新删除按钮状态，只基于复选框状态
        }

        /// <summary>
        /// 处理模块表格单元格编辑结束事件（主要用于捕获复选框状态变化）
        /// </summary>
        private void ModulesDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // 只处理复选框列的编辑（第一列，索引为0）
            if (e.Column is DataGridCheckBoxColumn && e.Column.DisplayIndex == 0)
            {
                // 延迟更新，确保绑定已完成
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateRemoveButtonState();
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        /// <summary>
        /// 更新删除按钮状态
        /// </summary>
        private void UpdateRemoveButtonState()
        {
            // 检查是否有非内置模块且有模块名称的模块被选中（空插槽不能删除）
            bool hasSelectableModules = _modules.Any(m => m.IsSelected && !m.IsBuiltIn && !string.IsNullOrEmpty(m.ModuleName));
            RemoveModuleButton.IsEnabled = hasSelectableModules;
        }

        /// <summary>
        /// 为模块添加PropertyChanged事件监听
        /// </summary>
        private void AddModulePropertyChangedListener(ModuleViewModel module)
        {
            // 设置父页面引用，用于地址验证
            module.SetParentPage(this);

            // 设置初始化状态，避免在初始化期间触发验证
            module.SetInitializing(true);

            module.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(ModuleViewModel.IsSelected))
                {
                    UpdateRemoveButtonState();
                }
            };
        }

        /// <summary>
        /// 完成模块初始化，启用地址验证
        /// </summary>
        private void FinishModuleInitialization()
        {
            foreach (var module in _modules)
            {
                module.SetInitializing(false);
            }
            Debug.WriteLine("模块初始化完成，地址验证已启用");
        }

        /// <summary>
        /// 查找或创建模块对应的项目配置
        /// </summary>
        /// <param name="moduleViewModel">模块视图模型</param>
        /// <returns>模块配置，如果找不到则创建新的</returns>
        private ModuleConfig FindOrCreateModuleConfig(ModuleViewModel moduleViewModel)
        {
            try
            {
                // 内置模块不需要保存到项目配置中
                if (moduleViewModel.IsBuiltIn)
                {
                    Debug.WriteLine($"内置模块 {moduleViewModel.ModuleName} 不需要保存到项目配置");
                    return null;
                }

                // 获取当前设备配置
                var deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig?.Modules == null)
                {
                    Debug.WriteLine("设备配置或模块列表为空，无法保存地址配置");
                    return null;
                }

                // 根据ModuleID或Position查找现有模块配置
                string moduleId = !string.IsNullOrEmpty(moduleViewModel.ModuleID) ? moduleViewModel.ModuleID : $"{moduleViewModel.ModuleName}_{moduleViewModel.Position}";

                var existingModule = deviceConfig.Modules.FirstOrDefault(m =>
                    m.ModuleRefID == moduleId ||
                    m.SlotNumber.ToString() == moduleViewModel.Position);

                if (existingModule != null)
                {
                    Debug.WriteLine($"找到现有模块配置: {existingModule.ModuleRefID} (槽位: {existingModule.SlotNumber})");
                    return existingModule;
                }

                // 如果模块没有名称，说明是空插槽，不需要创建配置
                if (string.IsNullOrEmpty(moduleViewModel.ModuleName))
                {
                    Debug.WriteLine($"插槽 {moduleViewModel.Position} 为空，不创建模块配置");
                    return null;
                }

                // 创建新的模块配置
                if (int.TryParse(moduleViewModel.Position, out int slotNumber))
                {
                    var newModule = new ModuleConfig
                    {
                        ModuleRefID = moduleId,
                        SlotNumber = slotNumber,
                        GSDRefID = moduleViewModel.GSDRefID,
                        Submodules = new List<SubmoduleConfig>()
                    };

                    deviceConfig.Modules.Add(newModule);
                    Debug.WriteLine($"创建新模块配置: {newModule.ModuleRefID} (槽位: {newModule.SlotNumber})");
                    return newModule;
                }

                Debug.WriteLine($"无法解析插槽号: {moduleViewModel.Position}");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找或创建模块配置时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查找或创建子模块的地址配置
        /// </summary>
        /// <param name="moduleConfig">模块配置</param>
        /// <param name="moduleViewModel">模块视图模型</param>
        /// <returns>地址配置对象</returns>
        private AddressConfigurationConfig FindOrCreateAddressConfiguration(ModuleConfig moduleConfig, ModuleViewModel moduleViewModel)
        {
            try
            {
                // 确保子模块列表存在
                if (moduleConfig.Submodules == null)
                {
                    moduleConfig.Submodules = new List<SubmoduleConfig>();
                }

                // 如果没有子模块，创建一个默认子模块来承载地址信息
                if (moduleConfig.Submodules.Count == 0)
                {
                    var defaultSubmodule = new SubmoduleConfig
                    {
                        SubmoduleRefID = $"{moduleConfig.ModuleRefID}_Submodule",
                        AddressConfiguration = new AddressConfigurationConfig()
                    };
                    moduleConfig.Submodules.Add(defaultSubmodule);
                    Debug.WriteLine($"为模块 {moduleConfig.ModuleRefID} 创建默认子模块以承载地址信息");
                }

                // 返回第一个子模块的地址配置（通常模块只有一个主要子模块用于地址配置）
                var submodule = moduleConfig.Submodules.First();
                if (submodule.AddressConfiguration == null)
                {
                    submodule.AddressConfiguration = new AddressConfigurationConfig();
                }

                return submodule.AddressConfiguration;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找或创建地址配置时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 全选复选框选中事件
        /// </summary>
        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            // 选中所有非内置模块
            foreach (var module in _modules)
            {
                if (!module.IsBuiltIn)
                {
                    module.IsSelected = true;
                }
            }
            UpdateRemoveButtonState();
        }

        /// <summary>
        /// 全选复选框取消选中事件
        /// </summary>
        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            // 取消选中所有模块
            foreach (var module in _modules)
            {
                module.IsSelected = false;
            }
            UpdateRemoveButtonState();
        }

        /// <summary>
        /// Ring Port 1 ComboBox选择变更事件处理程序
        /// </summary>
        private void RingPort1ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedValue = selectedItem.Content?.ToString() ?? "";
                if (!ValidateRingPortSelection(comboBox, RingPort2ComboBox, selectedValue))
                {
                    // 如果选择无效，恢复到之前的选择
                    if (e.RemovedItems.Count > 0 && e.RemovedItems[0] is ComboBoxItem previousItem)
                    {
                        comboBox.SelectionChanged -= RingPort1ComboBox_SelectionChanged;
                        comboBox.SelectedItem = previousItem;
                        comboBox.SelectionChanged += RingPort1ComboBox_SelectionChanged;
                    }
                }
                else
                {
                    // 选择有效，保存MRP配置
                    SaveMRPConfigurationOnChange();
                }
            }
        }

        /// <summary>
        /// Ring Port 2 ComboBox选择变更事件处理程序
        /// </summary>
        private void RingPort2ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedValue = selectedItem.Content?.ToString() ?? "";
                if (!ValidateRingPortSelection(comboBox, RingPort1ComboBox, selectedValue))
                {
                    // 如果选择无效，恢复到之前的选择
                    if (e.RemovedItems.Count > 0 && e.RemovedItems[0] is ComboBoxItem previousItem)
                    {
                        comboBox.SelectionChanged -= RingPort2ComboBox_SelectionChanged;
                        comboBox.SelectedItem = previousItem;
                        comboBox.SelectionChanged += RingPort2ComboBox_SelectionChanged;
                    }
                }
                else
                {
                    // 选择有效，保存MRP配置
                    SaveMRPConfigurationOnChange();
                }
            }
        }

        /// <summary>
        /// 验证Ring Port选择是否有效（不能与另一个ComboBox选择相同的值）
        /// </summary>
        private bool ValidateRingPortSelection(ComboBox currentComboBox, ComboBox otherComboBox, string selectedValue)
        {
            if (otherComboBox?.SelectedItem is ComboBoxItem otherSelectedItem)
            {
                string otherSelectedValue = otherSelectedItem.Content?.ToString() ?? "";
                if (selectedValue == otherSelectedValue)
                {
                    Debug.WriteLine($"Ring Port选择冲突: 尝试选择 {selectedValue}，但另一个ComboBox已选择相同值");
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 确保Ring Port ComboBox初始化时选择不同的值
        /// </summary>
        private void EnsureRingPortsHaveDifferentDefaults()
        {
            try
            {
                // 临时移除事件处理程序
                RingPort1ComboBox.SelectionChanged -= RingPort1ComboBox_SelectionChanged;
                RingPort2ComboBox.SelectionChanged -= RingPort2ComboBox_SelectionChanged;

                // 确保有足够的选项
                if (RingPort1ComboBox.Items.Count >= 2 && RingPort2ComboBox.Items.Count >= 2)
                {
                    // 设置Ring Port 1为第一个选项
                    RingPort1ComboBox.SelectedIndex = 0;
                    // 设置Ring Port 2为第二个选项
                    RingPort2ComboBox.SelectedIndex = 1;

                    Debug.WriteLine("Ring Port ComboBox默认值设置完成：Port1 -> 第一个选项，Port2 -> 第二个选项");
                }
            }
            finally
            {
                // 重新添加事件处理程序
                RingPort1ComboBox.SelectionChanged += RingPort1ComboBox_SelectionChanged;
                RingPort2ComboBox.SelectionChanged += RingPort2ComboBox_SelectionChanged;
            }
        }
        
        /// <summary>
        /// 处理模块树选择变更事件
        /// </summary>
        private void ModulesTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is TreeViewItem selectedItem)
            {
                string header = selectedItem.Header?.ToString() ?? "";

                // 根据模块类型决定是否启用添加按钮
                bool isModuleSelected = IsSelectableModuleItem(selectedItem);

                AddModuleButton.IsEnabled = isModuleSelected;

                // 调试信息
                if (isModuleSelected)
                {
                    Debug.WriteLine($"选中可添加的模块/子模块: {header}");
                    if (selectedItem.Tag != null)
                    {
                        Debug.WriteLine($"模块/子模块键值: {selectedItem.Tag}");
                    }
                }
            }
            else
            {
                AddModuleButton.IsEnabled = false;
            }
        }

        /// <summary>
        /// 判断选中的树项是否为可选择的模块或子模块
        /// </summary>
        private bool IsSelectableModuleItem(TreeViewItem selectedItem)
        {
            if (selectedItem?.Parent is not TreeViewItem parentItem)
            {
                return false;
            }

            string parentHeader = parentItem.Header?.ToString() ?? "";
            string itemHeader = selectedItem.Header?.ToString() ?? "";

            // 首先检查是否为分组节点（category nodes）- 这些不能被添加
            if (IsCategoryNode(selectedItem))
            {
                return false;
            }

            // 检查是否为模块节点下的项目（叶子节点）
            if (IsModuleLeafNode(selectedItem, parentHeader, itemHeader))
            {
                return true;
            }

            // 检查是否为子模块节点下的项目（叶子节点）
            if (IsSubmoduleLeafNode(selectedItem, parentHeader, itemHeader))
            {
                return true;
            }

            // 检查是否为主模块节点下的项目
            if (parentHeader == "主模块" || parentHeader == "主模块 (DAP)")
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 判断是否为分组节点（category node）
        /// </summary>
        private bool IsCategoryNode(TreeViewItem selectedItem)
        {
            string header = selectedItem.Header?.ToString() ?? "";

            // 分组节点通常有子项，且本身是分类标题
            if (selectedItem.Items.Count > 0)
            {
                // 检查是否为已知的分组节点
                if (header == "模块" || header == "子模块" || header == "主模块" || header == "主模块 (DAP)")
                {
                    return true;
                }

                // 检查是否为模块分类节点（在"模块"节点下的分类）
                if (selectedItem.Parent is TreeViewItem parentItem)
                {
                    string parentHeader = parentItem.Header?.ToString() ?? "";
                    if (parentHeader == "模块" && selectedItem.Items.Count > 0)
                    {
                        // 这是一个模块分类节点（如"输入模块"、"输出模块"等）
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 判断是否为模块叶子节点
        /// </summary>
        private bool IsModuleLeafNode(TreeViewItem selectedItem, string parentHeader, string itemHeader)
        {
            // 直接在"模块"节点下的叶子节点
            if (parentHeader == "模块" && !string.IsNullOrEmpty(itemHeader) &&
                itemHeader != "无可用模块" && selectedItem.Items.Count == 0)
            {
                return true;
            }

            // 在模块分类节点下的叶子节点
            if (selectedItem.Parent is TreeViewItem directParent &&
                directParent.Parent is TreeViewItem grandParent)
            {
                string grandParentHeader = grandParent.Header?.ToString() ?? "";
                if (grandParentHeader == "模块" && !string.IsNullOrEmpty(itemHeader) &&
                    itemHeader != "无可用模块" && selectedItem.Items.Count == 0)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 判断是否为子模块叶子节点
        /// </summary>
        private bool IsSubmoduleLeafNode(TreeViewItem selectedItem, string parentHeader, string itemHeader)
        {
            // 直接在"子模块"节点下的叶子节点
            if (parentHeader == "子模块" && !string.IsNullOrEmpty(itemHeader) &&
                itemHeader != "无可用子模块" && selectedItem.Items.Count == 0)
            {
                return true;
            }

            // 在子模块分类节点下的叶子节点
            if (selectedItem.Parent is TreeViewItem directParent &&
                directParent.Parent is TreeViewItem grandParent)
            {
                string grandParentHeader = grandParent.Header?.ToString() ?? "";
                if (grandParentHeader == "子模块" && !string.IsNullOrEmpty(itemHeader) &&
                    itemHeader != "无可用子模块" && selectedItem.Items.Count == 0)
                {
                    return true;
                }
            }

            return false;
        }
        
        /// <summary>
        /// 添加模块按钮点击事件
        /// </summary>
        private void AddModuleButton_Click(object sender, RoutedEventArgs e)
        {
            if (ModulesTreeView.SelectedItem is TreeViewItem selectedItem)
            {
                AddModuleFromTreeViewItem(selectedItem);
            }
        }

        /// <summary>
        /// 从TreeViewItem添加模块的核心逻辑
        /// </summary>
        private void AddModuleFromTreeViewItem(TreeViewItem selectedItem)
        {
            try
            {
                string moduleType = selectedItem.Header?.ToString() ?? "";
                string moduleKey = selectedItem.Tag?.ToString() ?? "";

                // 获取父节点类型以确定是模块还是子模块
                string parentType = "";
                if (selectedItem.Parent is TreeViewItem parentItem)
                {
                    parentType = parentItem.Header?.ToString() ?? "";
                }

                // 检查插槽容量限制
                if (!CheckSlotCapacityBeforeAdd())
                {
                    return; // 已达到容量上限，不允许添加
                }

                // 提取模块信息以获取IO长度
                var moduleInfo = ExtractModuleInfoFromGSDML(moduleKey, moduleType);
                if (moduleInfo == null)
                {
                    MessageBox.Show("无法获取模块信息，无法添加模块。", "模块信息错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 检查PROFINET设备IO长度限制
                if (!CheckIOLengthLimitsBeforeAdd(moduleInfo))
                {
                    return; // 已达到IO长度上限，不允许添加
                }

                // 查找第一个空的可用插槽位置
                var availableSlot = FindFirstAvailableSlot();
                if (availableSlot == null)
                {
                    MessageBox.Show("没有可用的插槽位置，无法添加模块。", "插槽已满", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 模块信息已在前面提取并验证

                Debug.WriteLine($"ExtractModuleInfoFromGSDML返回的DisplayName: {moduleInfo.DisplayName}");
                Debug.WriteLine($"ExtractModuleInfoFromGSDML返回的GSDRefID: {moduleInfo.GSDRefID}");

                // 生成正确的ModuleID（参考ModuleConfigurator.cs的格式）
                if (!int.TryParse(availableSlot.Position, out int slotPosition))
                {
                    Debug.WriteLine($"错误：无法解析插槽位置 '{availableSlot.Position}' 为数字");
                    MessageBox.Show($"插槽位置格式错误：'{availableSlot.Position}'，无法添加模块。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                string moduleID = GenerateModuleID(moduleInfo.GSDRefID, slotPosition);

                // 在现有的空行中填充模块信息，而不是创建新行
                availableSlot.ModuleName = moduleInfo.DisplayName;
                availableSlot.SubmoduleName = ""; // 子模块名称留空，因为用户添加的是模块
                availableSlot.InterfaceSlot = slotPosition.ToString();
                availableSlot.GSDRefID = moduleInfo.GSDRefID;
                availableSlot.ModuleID = moduleID; // 这个将作为ModuleRefID使用
                availableSlot.InputLength = moduleInfo.InputLength;
                availableSlot.OutputLength = moduleInfo.OutputLength;
                availableSlot.IsBuiltIn = false; // 用户添加的模块不是内置模块

                // 清空可能存在的旧地址信息
                availableSlot.InputStartAddress = "";
                availableSlot.OutputStartAddress = "";
                availableSlot.PNIStartAddress = "";
                availableSlot.PNQStartAddress = "";

                // 确保Index属性正确设置（用于UI显示）
                if (availableSlot.Index == 0)
                {
                    availableSlot.Index = _modules.IndexOf(availableSlot) + 1;
                }

                // 重新计算所有模块的地址
                RecalculateAllModuleAddresses();

                // 显示当前IO长度使用情况
                var (currentInput, currentOutput) = GetCurrentIOLengthTotals();
                Debug.WriteLine($"模块 {moduleInfo.DisplayName} 已添加 - 地址分配: Input={availableSlot.InputStartAddress} (长度:{moduleInfo.InputLength}), Output={availableSlot.OutputStartAddress} (长度:{moduleInfo.OutputLength})");
                Debug.WriteLine($"设备当前IO长度使用: Input={currentInput}/1440 ({(double)currentInput/1440*100:F1}%), Output={currentOutput}/1440 ({(double)currentOutput/1440*100:F1}%)");

                // 手动触发UI更新
                ModulesDataGrid.Items.Refresh();

                // 选中新添加的模块
                ModulesDataGrid.SelectedItem = availableSlot;
                ModulesDataGrid.ScrollIntoView(availableSlot);

                // 立即保存模块到设备配置中，避免导航时丢失
                SaveNewModuleToDeviceConfig(availableSlot, slotPosition, moduleInfo);

                // 标记为已修改
                _isModified = true;

                // 为新添加的模块注册导航菜单
                RegisterModuleNavigation(availableSlot);

                Debug.WriteLine($"成功添加模块: {moduleInfo.DisplayName}，槽位: {slotPosition}，ModuleID: {moduleID}，GSDRefID: {moduleInfo.GSDRefID}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"添加模块时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region 拖拽功能实现

        private bool _isDragging = false;
        private Point _startPoint;

        /// <summary>
        /// 模块树鼠标按下事件
        /// </summary>
        private void ModulesTreeView_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _startPoint = e.GetPosition(null);
            _isDragging = false;
        }

        /// <summary>
        /// 模块树鼠标移动事件，处理拖拽开始
        /// </summary>
        private void ModulesTreeView_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isDragging)
            {
                Point mousePos = e.GetPosition(null);
                Vector diff = _startPoint - mousePos;

                if (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                    Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance)
                {
                    // 获取拖拽源项
                    TreeViewItem? treeViewItem = FindAncestor<TreeViewItem>((DependencyObject)e.OriginalSource);
                    if (treeViewItem != null)
                    {
                        // 检查是否是可拖拽的模块项（不是根节点或分类节点）
                        if (IsValidModuleItem(treeViewItem))
                        {
                            _isDragging = true;

                            // 创建拖拽数据
                            DataObject dragData = new DataObject("ModuleTreeViewItem", treeViewItem);

                            // 开始拖拽操作
                            DragDrop.DoDragDrop(ModulesTreeView, dragData, DragDropEffects.Copy);

                            _isDragging = false;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 模块表格拖拽悬停事件
        /// </summary>
        private void ModulesDataGrid_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("ModuleTreeViewItem"))
            {
                e.Effects = DragDropEffects.Copy;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        /// <summary>
        /// 模块表格拖拽放置事件
        /// </summary>
        private void ModulesDataGrid_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("ModuleTreeViewItem"))
            {
                TreeViewItem droppedItem = (TreeViewItem)e.Data.GetData("ModuleTreeViewItem");
                if (droppedItem != null)
                {
                    // 使用相同的添加逻辑
                    AddModuleFromTreeViewItem(droppedItem);
                }
            }
            e.Handled = true;
        }

        /// <summary>
        /// 查找指定类型的祖先元素
        /// </summary>
        private static T? FindAncestor<T>(DependencyObject current) where T : DependencyObject
        {
            do
            {
                if (current is T)
                {
                    return (T)current;
                }
                current = VisualTreeHelper.GetParent(current);
            }
            while (current != null);
            return default(T);
        }

        /// <summary>
        /// 检查TreeViewItem是否是有效的可拖拽模块项
        /// </summary>
        private bool IsValidModuleItem(TreeViewItem item)
        {
            if (item == null) return false;

            // 使用与IsSelectableModuleItem相同的逻辑来确保一致性
            return IsSelectableModuleItem(item);
        }

        #endregion

        /// <summary>
        /// 查找下一个可用的槽位
        /// </summary>
        private int FindNextAvailableSlot()
        {
            // 获取已使用的槽位
            var usedSlots = new HashSet<int>();
            foreach (var module in _modules)
            {
                if (int.TryParse(module.Position, out int slot) && slot > 0)
                {
                    usedSlots.Add(slot);
                }
            }

            // 查找第一个未使用的槽位（从1开始）
            for (int i = 1; i <= 32; i++)
            {
                if (!usedSlots.Contains(i))
                {
                    return i;
                }
            }

            // 如果所有槽位都被占用，返回下一个可用的槽位
            return usedSlots.Count > 0 ? usedSlots.Max() + 1 : 1;
        }

        /// <summary>
        /// 检查插槽容量限制，确保添加模块时不超过DAP设备的物理插槽容量
        /// </summary>
        /// <returns>如果可以添加模块返回true，否则返回false</returns>
        private bool CheckSlotCapacityBeforeAdd()
        {
            try
            {
                // 获取当前设备的配置信息
                var deviceConfig = _projectManager?.CurrentProject?.ConfigurationSettings?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceRefID == _deviceName);
                if (deviceConfig == null)
                {
                    Debug.WriteLine($"设备配置不存在: {_deviceName}");
                    MessageBox.Show("无法获取设备配置信息，无法检查插槽容量。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // 获取对应的ListOfNodes设备节点以获取GSDML路径和引用ID
                var deviceNode = _projectManager?.CurrentProject?.ListOfNodesConfiguration?.DecentralDevices
                    ?.FirstOrDefault(d => d.DeviceID == deviceConfig.DeviceRefID);
                if (deviceNode == null)
                {
                    Debug.WriteLine($"设备节点不存在: {deviceConfig.DeviceRefID}");
                    MessageBox.Show("无法获取设备节点信息，无法检查插槽容量。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string gsdPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);
                string gsdRefId = deviceNode.GSDRefID ?? string.Empty;

                Debug.WriteLine($"GSDML路径转换: {relativePath} -> {gsdPath}");

                // 获取物理插槽数量
                int physicalSlotsCount = GetPhysicalSlotsCount(gsdPath, gsdRefId);
                if (physicalSlotsCount <= 0)
                {
                    Debug.WriteLine("无法获取物理插槽数量，允许添加模块");
                    return true; // 如果无法获取插槽数量，允许添加（向后兼容）
                }

                // 计算当前已使用的用户可配置插槽数量（不包括内置模块）
                int usedUserSlotsCount = _modules.Count(m => !string.IsNullOrEmpty(m.ModuleName) && !m.IsBuiltIn);

                // 计算可用的用户插槽总数（物理插槽总数减去内置模块占用的插槽）
                int builtinSlotsCount = _modules.Count(m => m.IsBuiltIn);
                int availableUserSlotsCount = physicalSlotsCount -1 ;
                // 检查是否已达到用户可配置插槽的容量上限
                if (usedUserSlotsCount >= availableUserSlotsCount)
                {
                    MessageBox.Show($"已达到设备的最大用户可配置插槽容量（{availableUserSlotsCount}个插槽），无法添加更多模块。\n"
                                 ,"插槽容量已满", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查插槽容量时出错: {ex.Message}");
                // 出错时允许添加（向后兼容）
                return true;
            }
        }

        /// <summary>
        /// 检查PROFINET设备IO长度限制（输入和输出各1440字节）
        /// </summary>
        /// <param name="newModuleInfo">要添加的新模块信息</param>
        /// <returns>是否允许添加模块</returns>
        private bool CheckIOLengthLimitsBeforeAdd(GSDMLModuleInfo newModuleInfo)
        {
            try
            {
                // PROFINET设备的标准IO长度限制
                const int MAX_INPUT_LENGTH = 1440;
                const int MAX_OUTPUT_LENGTH = 1440;

                // 计算当前所有模块的累计输入和输出长度
                int currentInputLength = 0;
                int currentOutputLength = 0;

                foreach (var module in _modules)
                {
                    // 只计算已配置的模块（有模块名称的）
                    if (!string.IsNullOrEmpty(module.ModuleName))
                    {
                        currentInputLength += module.InputLength;
                        currentOutputLength += module.OutputLength;
                    }
                }

                // 计算添加新模块后的总长度
                int totalInputLength = currentInputLength + newModuleInfo.InputLength;
                int totalOutputLength = currentOutputLength + newModuleInfo.OutputLength;

                Debug.WriteLine($"IO长度检查: 当前输入={currentInputLength}, 当前输出={currentOutputLength}");
                Debug.WriteLine($"新模块: 输入={newModuleInfo.InputLength}, 输出={newModuleInfo.OutputLength}");
                Debug.WriteLine($"添加后总计: 输入={totalInputLength}, 输出={totalOutputLength}");

                // 检查输入长度限制
                if (totalInputLength > MAX_INPUT_LENGTH)
                {
                    MessageBox.Show(
                        $"无法添加模块：输入长度超出PROFINET设备限制。\n\n" +
                        $"当前设备输入长度：{currentInputLength} 字节\n" +
                        $"新模块输入长度：{newModuleInfo.InputLength} 字节\n" +
                        $"添加后总长度：{totalInputLength} 字节\n" +
                        $"PROFINET设备输入长度限制：{MAX_INPUT_LENGTH} 字节\n\n" +
                        $"请选择输入长度更小的模块或删除现有模块后再试。",
                        "输入长度限制",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                // 检查输出长度限制
                if (totalOutputLength > MAX_OUTPUT_LENGTH)
                {
                    MessageBox.Show(
                        $"无法添加模块：输出长度超出PROFINET设备限制。\n\n" +
                        $"当前设备输出长度：{currentOutputLength} 字节\n" +
                        $"新模块输出长度：{newModuleInfo.OutputLength} 字节\n" +
                        $"添加后总长度：{totalOutputLength} 字节\n" +
                        $"PROFINET设备输出长度限制：{MAX_OUTPUT_LENGTH} 字节\n\n" +
                        $"请选择输出长度更小的模块或删除现有模块后再试。",
                        "输出长度限制",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                Debug.WriteLine($"IO长度检查通过：输入 {totalInputLength}/{MAX_INPUT_LENGTH}, 输出 {totalOutputLength}/{MAX_OUTPUT_LENGTH}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查IO长度限制时出错: {ex.Message}");
                // 出错时允许添加（向后兼容）
                return true;
            }
        }

        /// <summary>
        /// 验证当前配置的IO长度是否超出PROFINET设备限制
        /// </summary>
        private void ValidateCurrentIOLengthLimits()
        {
            try
            {
                const int MAX_INPUT_LENGTH = 1440;
                const int MAX_OUTPUT_LENGTH = 1440;

                var (currentInput, currentOutput) = GetCurrentIOLengthTotals();

                Debug.WriteLine($"当前设备IO长度验证: Input={currentInput}/{MAX_INPUT_LENGTH}, Output={currentOutput}/{MAX_OUTPUT_LENGTH}");

                // 检查是否超出限制
                bool hasInputOverflow = currentInput > MAX_INPUT_LENGTH;
                bool hasOutputOverflow = currentOutput > MAX_OUTPUT_LENGTH;

                if (hasInputOverflow || hasOutputOverflow)
                {
                    string warningMessage = "警告：当前设备配置超出PROFINET设备IO长度限制！\n\n";

                    if (hasInputOverflow)
                    {
                        warningMessage += $"输入长度超限：{currentInput} / {MAX_INPUT_LENGTH} 字节 (超出 {currentInput - MAX_INPUT_LENGTH} 字节)\n";
                    }

                    if (hasOutputOverflow)
                    {
                        warningMessage += $"输出长度超限：{currentOutput} / {MAX_OUTPUT_LENGTH} 字节 (超出 {currentOutput - MAX_OUTPUT_LENGTH} 字节)\n";
                    }

                    warningMessage += "\n请删除部分模块以符合PROFINET设备规范。";

                    MessageBox.Show(warningMessage, "IO长度超限警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证当前IO长度限制时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前设备的IO长度统计信息
        /// </summary>
        /// <returns>输入长度和输出长度的元组</returns>
        private (int InputLength, int OutputLength) GetCurrentIOLengthTotals()
        {
            int totalInputLength = 0;
            int totalOutputLength = 0;

            foreach (var module in _modules)
            {
                // 只计算已配置的模块（有模块名称的）
                if (!string.IsNullOrEmpty(module.ModuleName))
                {
                    totalInputLength += module.InputLength;
                    totalOutputLength += module.OutputLength;
                }
            }

            return (totalInputLength, totalOutputLength);
        }

        /// <summary>
        /// 显示当前设备的IO长度使用状态
        /// </summary>
        private void ShowIOLengthStatus()
        {
            try
            {
                const int MAX_INPUT_LENGTH = 1440;
                const int MAX_OUTPUT_LENGTH = 1440;

                var (currentInput, currentOutput) = GetCurrentIOLengthTotals();

                string statusMessage = $"当前设备IO长度使用状态：\n\n" +
                                     $"输入长度：{currentInput} / {MAX_INPUT_LENGTH} 字节 ({(double)currentInput / MAX_INPUT_LENGTH * 100:F1}%)\n" +
                                     $"输出长度：{currentOutput} / {MAX_OUTPUT_LENGTH} 字节 ({(double)currentOutput / MAX_OUTPUT_LENGTH * 100:F1}%)\n\n" +
                                     $"剩余可用：\n" +
                                     $"输入：{MAX_INPUT_LENGTH - currentInput} 字节\n" +
                                     $"输出：{MAX_OUTPUT_LENGTH - currentOutput} 字节";

                MessageBox.Show(statusMessage, "IO长度使用状态", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"显示IO长度状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 查找第一个空的可用插槽位置（Position不为"0"且ModuleName为空的行）
        /// </summary>
        /// <returns>第一个可用的空插槽，如果没有则返回null</returns>
        private ModuleViewModel? FindFirstAvailableSlot()
        {
            try
            {
                // 查找第一个空的可用插槽位置
                // 条件：Position不为"0"（不是主模块）、不为"-"（不是内置模块）且ModuleName为空（未被使用）
                var availableSlot = _modules
                    .Where(m => m.Position != "0" && m.Position != "-" && string.IsNullOrEmpty(m.ModuleName))
                    .OrderBy(m => int.TryParse(m.Position, out int pos) ? pos : int.MaxValue)
                    .FirstOrDefault();

                if (availableSlot != null)
                {
                    Debug.WriteLine($"找到可用插槽位置: {availableSlot.Position}");
                }
                else
                {
                    Debug.WriteLine("没有找到可用的插槽位置");
                }

                return availableSlot;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找可用插槽时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查找下一个可用的用户槽位（从1开始，跳过主模块的0槽位）
        /// 保留此方法以保持向后兼容性
        /// </summary>
        private int FindNextAvailableUserSlot()
        {
            // 获取已使用的槽位（跳过槽位0，因为那是主模块）
            var usedSlots = new HashSet<int>();
            foreach (var module in _modules)
            {
                if (int.TryParse(module.Position, out int slot) && slot > 0)
                {
                    usedSlots.Add(slot);
                }
            }

            // 查找第一个未使用的槽位（从1开始）
            for (int i = 1; i <= 32; i++)
            {
                if (!usedSlots.Contains(i))
                {
                    return i;
                }
            }

            // 如果所有槽位都被占用，返回下一个可用的槽位
            return usedSlots.Count > 0 ? usedSlots.Max() + 1 : 1;
        }

        /// <summary>
        /// 获取DAP设备的物理插槽数量
        /// </summary>
        /// <param name="gsdPath">GSDML文件路径</param>
        /// <param name="gsdRefId">GSD引用ID</param>
        /// <returns>物理插槽数量，失败时返回-1</returns>
        private int GetPhysicalSlotsCount(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== GetPhysicalSlotsCount 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine("GSDML文件不存在");
                    return -1;
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine("无法获取GSDML解析器");
                    return -1;
                }

                // 获取Common模型的DAP
                var commonDAP = interpreter.GetDeviceAccessPoint(gsdRefId);
                if (commonDAP != null)
                {
                    Debug.WriteLine($"找到Common DAP: {commonDAP.GsdID}");

                    // 获取PhysicalSlots
                    var physicalSlots = commonDAP.PhysicalSlots;
                    if (physicalSlots != null)
                    {
                        int slotsCount = physicalSlots.Length;
                        Debug.WriteLine($"PhysicalSlots数量: {slotsCount}");
                        Debug.WriteLine($"=== GetPhysicalSlotsCount 成功完成 ===");
                        return slotsCount;
                    }
                    else
                    {
                        Debug.WriteLine("PhysicalSlots为空");
                    }
                }
                else
                {
                    Debug.WriteLine("无法获取Common DAP，尝试遍历所有可用的DAP");

                    // 尝试遍历所有可用的DAP
                    var allDAPs = interpreter.GetDeviceAccessPoints();
                    if (allDAPs != null && allDAPs.Length > 0)
                    {
                        foreach (var dap in allDAPs)
                        {
                            if (dap is PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint availableDAP)
                            {
                                Debug.WriteLine($"检查可用DAP: {availableDAP.GsdID}");

                                // 使用第一个可用的DAP
                                var physicalSlots = availableDAP.PhysicalSlots;
                                if (physicalSlots != null)
                                {
                                    int slotsCount = physicalSlots.Length;
                                    Debug.WriteLine($"使用DAP {availableDAP.GsdID} 的PhysicalSlots数量: {slotsCount}");
                                    Debug.WriteLine($"=== GetPhysicalSlotsCount 成功完成 ===");
                                    return slotsCount;
                                }
                            }
                        }
                    }
                }

                Debug.WriteLine("无法获取PhysicalSlots信息");
                Debug.WriteLine($"=== GetPhysicalSlotsCount 失败结束 ===");
                return -1;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取PhysicalSlots时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== GetPhysicalSlotsCount 异常结束 ===");
                return -1;
            }
        }

        /// <summary>
        /// 根据物理插槽数量初始化模块表格行数
        /// </summary>
        /// <param name="physicalSlotsCount">物理插槽数量</param>
        private void InitializeModuleTableRows(int physicalSlotsCount)
        {
            try
            {
                Debug.WriteLine($"=== InitializeModuleTableRows 开始 ===");
                Debug.WriteLine($"物理插槽数量: {physicalSlotsCount}");

                if (physicalSlotsCount <= 0)
                {
                    Debug.WriteLine("物理插槽数量无效，使用默认行数");
                    EnsureDefaultRows();
                    return;
                }

                // 确保主模块行存在（插槽0）
                if (_modules.Count == 0)
                {
                    // 第一行：主模块（插槽0）
                    var mainModule = new ModuleViewModel
                    {
                        Index = 1,
                        IsSelected = true,
                        Position = "0",
                        ModuleName = _deviceType,
                        SubmoduleName = "主模块",
                        InterfaceSlot = "0",
                        PNIStartAddress = "0",
                        InputStartAddress = "0",
                        OutputStartAddress = "0",
                        IsBuiltIn = true // 主模块也是内置模块
                    };

                    AddModulePropertyChangedListener(mainModule);
                    _modules.Add(mainModule);
                    Debug.WriteLine("添加主模块（插槽0）");
                }

                // 计算需要的总行数
                // 内置模块已经在LoadDAPBuiltinModules中添加
                // 这里只需要确保有足够的空行供用户添加模块
                int builtinModulesCount = _modules.Count(m => m.IsBuiltIn);
                int userSlotStart = 1; // 用户可配置插槽从1开始
                int userSlotEnd = physicalSlotsCount - 1; // 到PhysicalSlots.Length-1结束
                int totalRowsNeeded = builtinModulesCount + (userSlotEnd - userSlotStart + 1);

                Debug.WriteLine($"内置模块数量: {builtinModulesCount}");
                Debug.WriteLine($"用户可配置插槽范围: {userSlotStart} 到 {userSlotEnd}");
                Debug.WriteLine($"需要的总行数: {totalRowsNeeded}");

                // 添加空行以匹配物理插槽数量
                int currentRowCount = _modules.Count;
                for (int i = currentRowCount; i < totalRowsNeeded; i++)
                {
                    // 计算对应的插槽位置
                    int slotPosition = userSlotStart + (i - builtinModulesCount);
                    if (slotPosition <= userSlotEnd)
                    {
                        var emptyModule = new ModuleViewModel
                        {
                            Index = i + 1,
                            IsSelected = false,
                            Position = slotPosition.ToString(),
                            ModuleName = "",
                            SubmoduleName = "",
                            InterfaceSlot = "",
                            PNIStartAddress = "",
                            InputStartAddress = "",
                            OutputStartAddress = "",
                            IsBuiltIn = false // 用户可配置模块
                        };

                        AddModulePropertyChangedListener(emptyModule);
                        _modules.Add(emptyModule);
                        Debug.WriteLine($"添加空行用于插槽 {slotPosition}");
                    }
                }

                Debug.WriteLine($"模块表格初始化完成，总行数: {_modules.Count}");
                Debug.WriteLine($"=== InitializeModuleTableRows 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化模块表格行数时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== InitializeModuleTableRows 异常结束 ===");

                // 出错时使用默认行数
                EnsureDefaultRows();
            }
        }

        /// <summary>
        /// 确保有足够的插槽行供用户添加模块
        /// </summary>
        /// <param name="physicalSlotsCount">物理插槽数量</param>
        private void EnsureSlotRowsForPhysicalSlots(int physicalSlotsCount)
        {
            try
            {
                Debug.WriteLine($"=== EnsureSlotRowsForPhysicalSlots 开始 ===");
                Debug.WriteLine($"物理插槽数量: {physicalSlotsCount}");
                Debug.WriteLine($"当前模块行数: {_modules.Count}");

                if (physicalSlotsCount <= 0)
                {
                    Debug.WriteLine("物理插槽数量无效，跳过");
                    return;
                }

                // 计算需要的总行数（包括内置模块和用户可配置插槽）
                int userSlotStart = 1; // 用户可配置插槽从1开始
                int userSlotEnd = physicalSlotsCount - 1; // 到PhysicalSlots.Length-1结束

                // 检查每个用户可配置的插槽位置是否有对应的行
                for (int slotPosition = userSlotStart; slotPosition <= userSlotEnd; slotPosition++)
                {
                    // 查找是否已有该插槽位置的行
                    var existingRow = _modules.FirstOrDefault(m => m.Position == slotPosition.ToString());

                    if (existingRow == null)
                    {
                        // 创建空行
                        var emptyModule = new ModuleViewModel
                        {
                            Index = _modules.Count + 1,
                            IsSelected = false,
                            Position = slotPosition.ToString(),
                            ModuleName = "",
                            SubmoduleName = "",
                            InterfaceSlot = "",
                            PNIStartAddress = "",
                            InputStartAddress = "",
                            OutputStartAddress = "",
                            IsBuiltIn = false // 用户可配置模块
                        };

                        AddModulePropertyChangedListener(emptyModule);
                        _modules.Add(emptyModule);
                        Debug.WriteLine($"添加空行用于插槽 {slotPosition}");
                    }
                    else
                    {
                        Debug.WriteLine($"插槽 {slotPosition} 已有对应的行: {existingRow.ModuleName}");
                    }
                }

                Debug.WriteLine($"插槽行检查完成，总行数: {_modules.Count}");
                Debug.WriteLine($"=== EnsureSlotRowsForPhysicalSlots 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"确保插槽行时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 确保主模块行存在（保留原有方法作为后备）
        /// </summary>
        private void EnsureDefaultRows()
        {
            // 只确保主模块行存在，不再创建预留行
            if (_modules.Count == 0)
            {
                // 第一行：主模块
                var mainModule = new ModuleViewModel
                {
                    Index = 1,
                    IsSelected = true,
                    Position = "0",
                    ModuleName = _deviceType,
                    SubmoduleName = "主模块",
                    InterfaceSlot = "0",
                    PNIStartAddress = "0",
                    InputStartAddress = "0",
                    OutputStartAddress = "0",
                    IsBuiltIn = true // 主模块也是内置模块
                };

                AddModulePropertyChangedListener(mainModule);
                _modules.Add(mainModule);
            }
        }

        /// <summary>
        /// 从GSDML文件中提取模块信息（参考ModuleConfigurator.cs的实现）
        /// </summary>
        private GSDMLModuleInfo? ExtractModuleInfoFromGSDML(string moduleKey, string moduleType)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleKey))
                {
                    Debug.WriteLine($"模块键值为空，使用模块类型作为默认值: {moduleType}");
                    return CreateFallbackModuleInfo(moduleType, moduleType);
                }

                // 获取当前设备的DecentralDeviceCatalog和GSDML路径
                var deviceCatalog = GetCurrentDeviceCatalog();
                var gsdPath = GetCurrentGSDMLPath();

                if (deviceCatalog == null || string.IsNullOrEmpty(gsdPath))
                {
                    Debug.WriteLine("无法获取设备目录或GSDML路径，使用简化方法");
                    return ExtractModuleInfoSimplified(moduleKey, moduleType);
                }

                // 使用ProjectManagerUtilities获取模块查找表（参考ModuleConfigurator.cs）
                var moduleLookup = PNConfigLib.PNProjectManager.ProjectManagerUtilities.GetModuleLookupBySlotRelation(
                    deviceCatalog,
                    gsdPath,
                    PNConfigLib.DataModel.SlotRelationType.AllowedInSlots);

                // 查找匹配的模块
                foreach (var kvp in moduleLookup)
                {
                    var moduleInfo = kvp.Key; // KeyValuePair<string, int>
                    var moduleCatalog = kvp.Value; // ModuleCatalog

                    // moduleInfo.Key 是 GSDRefID（如 "DI 8x24VDC HF V2.0"）
                    // moduleInfo.Value 是 SlotNumber

                    if (moduleInfo.Key.Equals(moduleKey, StringComparison.OrdinalIgnoreCase) ||
                        moduleInfo.Key.Contains(moduleKey))
                    {
                        // 获取模块显示名称 - 使用与模块树相同的方法
                        string displayName = GetModuleDisplayNameForTree(moduleCatalog, moduleInfo.Key);

                        // 提取IO长度信息
                        var ioLengths = ExtractIOLengthFromModuleCatalog(moduleCatalog);

                        Debug.WriteLine($"找到匹配的模块: GSDRefID={moduleInfo.Key}, SlotNumber={moduleInfo.Value}, DisplayName={displayName}, InputLength={ioLengths.InputLength}, OutputLength={ioLengths.OutputLength}");

                        var result = new GSDMLModuleInfo
                        {
                            GSDRefID = moduleInfo.Key, // 这是正确的GSDRefID
                            DisplayName = displayName,
                            ModuleKey = moduleKey,
                            DefaultSlot = moduleInfo.Value, // 这是正确的SlotNumber
                            InputLength = ioLengths.InputLength,
                            OutputLength = ioLengths.OutputLength,
                            TotalDataLength = ioLengths.InputLength + ioLengths.OutputLength
                        };

                        Debug.WriteLine($"ExtractModuleInfoFromGSDML即将返回: DisplayName={result.DisplayName}, GSDRefID={result.GSDRefID}");
                        return result;
                    }
                }

                Debug.WriteLine($"未找到模块键值 {moduleKey} 对应的GSDML信息，使用简化方法");
                return ExtractModuleInfoSimplified(moduleKey, moduleType);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取GSDML模块信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return ExtractModuleInfoSimplified(moduleKey, moduleType);
            }
        }

        /// <summary>
        /// 获取当前设备的DecentralDeviceCatalog
        /// </summary>
        private PNConfigLib.DataModel.PCLCatalogObjects.DecentralDeviceCatalog? GetCurrentDeviceCatalog()
        {
            try
            {
                // 从当前设备配置中获取GSDML路径和设备信息
                if (_projectManager?.CurrentProject == null) return null;

                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                if (deviceConfig == null) return null;

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 从设备配置中获取GSDML文件的绝对路径
                string gsdmlPath = deviceConfig.GetGSDMLAbsolutePath(projectDirectory);
                if (string.IsNullOrEmpty(gsdmlPath)) return null;

                // 从Catalog中查找对应的DecentralDeviceCatalog
                foreach (var deviceEntry in PNConfigLib.DataModel.Catalog.DeviceList)
                {
                    if (deviceEntry.Key.Contains(gsdmlPath) || deviceEntry.Key.EndsWith(Path.GetFileName(gsdmlPath)))
                    {
                        return deviceEntry.Value;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取设备目录时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前GSDML文件路径（绝对路径）
        /// </summary>
        private string? GetCurrentGSDMLPath()
        {
            try
            {
                if (_projectManager?.CurrentProject == null) return null;

                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                if (deviceConfig == null) return null;

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 返回绝对路径用于文件操作
                return deviceConfig.GetGSDMLAbsolutePath(projectDirectory);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取GSDML路径时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 简化的模块信息提取方法（备用方案）
        /// </summary>
        private GSDMLModuleInfo? ExtractModuleInfoSimplified(string moduleKey, string moduleType)
        {
            try
            {
                // 直接从Catalog.ModuleList中查找模块信息
                foreach (var moduleEntry in PNConfigLib.DataModel.Catalog.ModuleList)
                {
                    // moduleEntry.Key 是完整路径，如 "GSDML-V2.43-Siemens-ERTEC200pEvalkit-20230301.xml\\DI 8x24VDC HF V2.0"
                    // 我们需要提取最后的部分作为GSDRefID
                    string fullPath = moduleEntry.Key;
                    string gsdRefID = fullPath.Contains("\\") ? fullPath.Substring(fullPath.LastIndexOf("\\") + 1) : fullPath;

                    if (gsdRefID.Equals(moduleKey, StringComparison.OrdinalIgnoreCase) ||
                        gsdRefID.Contains(moduleKey) ||
                        moduleKey.Contains(gsdRefID))
                    {
                        var moduleCatalog = moduleEntry.Value;

                        // 获取模块显示名称 - 使用与模块树相同的方法
                        Debug.WriteLine($"简化方法调用GetModuleDisplayNameForTree，gsdRefID: {gsdRefID}");
                        string displayName = GetModuleDisplayNameForTree(moduleCatalog, gsdRefID);
                        Debug.WriteLine($"简化方法GetModuleDisplayNameForTree返回: {displayName}");

                        // 提取IO长度信息
                        var ioLengths = ExtractIOLengthFromModuleCatalog(moduleCatalog);

                        Debug.WriteLine($"简化方法找到匹配的模块: GSDRefID={gsdRefID}, DisplayName={displayName}, FullPath={fullPath}, InputLength={ioLengths.InputLength}, OutputLength={ioLengths.OutputLength}");

                        return new GSDMLModuleInfo
                        {
                            GSDRefID = gsdRefID, // 使用提取的GSDRefID
                            DisplayName = displayName,
                            ModuleKey = moduleKey,
                            DefaultSlot = 1, // 默认槽位，实际槽位由用户选择
                            InputLength = ioLengths.InputLength,
                            OutputLength = ioLengths.OutputLength,
                            TotalDataLength = ioLengths.InputLength + ioLengths.OutputLength
                        };
                    }
                }

                Debug.WriteLine($"简化方法未找到模块键值 {moduleKey} 对应的GSDML信息，使用默认值");
                return CreateFallbackModuleInfo(moduleKey, moduleType);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"简化方法提取GSDML模块信息时出错: {ex.Message}");
                return CreateFallbackModuleInfo(moduleKey, moduleType);
            }
        }



        /// <summary>
        /// 获取模块显示名称
        /// </summary>
        private string GetModuleDisplayName(PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog moduleCatalog, string gsdRefID)
        {
            try
            {
                Debug.WriteLine($"开始获取模块显示名称，GSDRefID: {gsdRefID}");

                // 尝试从模块目录中获取显示名称
                var nameTextId = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("NameTextId", null, "");
                Debug.WriteLine($"获取到NameTextId: {nameTextId}");

                if (!string.IsNullOrEmpty(nameTextId))
                {
                    // 从nameTextId对应的Value获取显示名称
                    var displayName = GetDisplayNameFromTextId(nameTextId);
                    Debug.WriteLine($"从GetDisplayNameFromTextId获取到: {displayName}");

                    if (!string.IsNullOrEmpty(displayName) && displayName != nameTextId)
                    {
                        Debug.WriteLine($"从NameTextId获取模块显示名称: {displayName} (TextId: {nameTextId})");
                        return displayName;
                    }
                }

                // 尝试从模块目录的Name属性获取
                var name = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("Name", null, "");
                Debug.WriteLine($"获取到Name属性: {name}");

                if (!string.IsNullOrEmpty(name))
                {
                    Debug.WriteLine($"从Name属性获取模块显示名称: {name}");
                    return name;
                }

                // 尝试其他可能的属性
                var displayNameAttr = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("DisplayName", null, "");
                Debug.WriteLine($"获取到DisplayName属性: {displayNameAttr}");

                if (!string.IsNullOrEmpty(displayNameAttr))
                {
                    Debug.WriteLine($"从DisplayName属性获取模块显示名称: {displayNameAttr}");
                    return displayNameAttr;
                }

                // 尝试从模块目录的InfoText属性获取
                var infoText = moduleCatalog.AttributeAccess.GetAnyAttribute<string>("InfoText", null, "");
                Debug.WriteLine($"获取到InfoText属性: {infoText}");

                if (!string.IsNullOrEmpty(infoText))
                {
                    Debug.WriteLine($"从InfoText属性获取模块显示名称: {infoText}");
                    return infoText;
                }

                // 如果都没有找到，直接使用nameTextId（如果存在）
                if (!string.IsNullOrEmpty(nameTextId))
                {
                    Debug.WriteLine($"使用NameTextId作为显示名称: {nameTextId}");
                    return nameTextId;
                }

                // 最后回退到使用GSDRefID作为显示名称
                Debug.WriteLine($"无法获取模块显示名称，使用GSDRefID: {gsdRefID}");
                return gsdRefID;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块显示名称时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return gsdRefID;
            }
        }

        /// <summary>
        /// 从缓存的Interpreter获取模块的Category信息（优化版本）
        /// </summary>
        private string GetModuleCategoryFromInterpreter(PNConfigLib.Gsd.Interpreter.Interpreter? interpreter, string moduleGSDRefID)
        {
            try
            {
                if (interpreter == null)
                {
                    Debug.WriteLine($"Interpreter为空，返回默认Category: {moduleGSDRefID}");
                    return "未分类";
                }

                // 尝试从Common模型获取模块Category信息
                var commonModule = interpreter.GetModule(moduleGSDRefID);
                if (commonModule != null && commonModule.Info != null &&
                    commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                {
                    string category = !string.IsNullOrEmpty(commonModuleInfo.Category) ?
                                     commonModuleInfo.Category : "未分类";
                    Debug.WriteLine($"从Common模型获取模块Category: {moduleGSDRefID} -> {category}");
                    return category;
                }

                Debug.WriteLine($"无法从GSDML文件获取模块Category: {moduleGSDRefID}，返回默认值");
                return "未分类";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块Category时出错: {moduleGSDRefID}, {ex.Message}");
                return "未分类";
            }
        }

        /// <summary>
        /// 获取模块的Category信息
        /// </summary>
        private string GetModuleCategoryForTree(PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog moduleCatalog, string moduleGSDRefID)
        {
            try
            {
                Debug.WriteLine($"开始获取模块Category: {moduleGSDRefID}");

                // 获取当前设备配置
                DecentralDeviceConfig? deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig == null)
                {
                    Debug.WriteLine("无法获取设备配置，返回默认Category");
                    return "未分类";
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 获取GSDML文件的绝对路径
                string gsdPath = deviceConfig.GetGSDMLAbsolutePath(projectDirectory);
                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}，返回默认Category");
                    return "未分类";
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return "未分类";
                }

                // 尝试从Common模型获取模块Category信息
                var commonModule = interpreter.GetModule(moduleGSDRefID);
                if (commonModule != null && commonModule.Info != null &&
                    commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                {
                    string category = !string.IsNullOrEmpty(commonModuleInfo.Category) ?
                                     commonModuleInfo.Category : "未分类";
                    Debug.WriteLine($"从Common模型获取模块Category: {category}");
                    return category;
                }

                Debug.WriteLine($"无法从GSDML文件获取模块Category，返回默认值");
                return "未分类";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块Category时出错: {ex.Message}");
                return "未分类";
            }
        }

        /// <summary>
        /// 获取模块在树视图中的显示名称，参考ModuleConfigPage的实现
        /// </summary>
        private string GetModuleDisplayNameForTree(PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog moduleCatalog, string moduleGSDRefID)
        {
            try
            {
                Debug.WriteLine($"开始获取模块显示名称: {moduleGSDRefID}");

                // 获取当前设备配置
                DecentralDeviceConfig? deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig == null)
                {
                    Debug.WriteLine("无法获取设备配置，使用GSDRefID作为显示名称");
                    return moduleGSDRefID;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 获取GSDML文件的绝对路径
                string gsdPath = deviceConfig.GetGSDMLAbsolutePath(projectDirectory);
                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}，使用GSDRefID作为显示名称");
                    return moduleGSDRefID;
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return moduleGSDRefID;
                }

                // 首先尝试从Structure模型获取模块结构元素
                var moduleStructureElement = interpreter.GetModuleStructureElement(moduleGSDRefID);
                if (moduleStructureElement != null)
                {
                    Debug.WriteLine($"从Structure模型找到模块: {moduleGSDRefID}");
                    // 优先使用模块名称，回退到GSDRefID
                    string displayName = !string.IsNullOrEmpty(moduleStructureElement.Name) ? moduleStructureElement.Name : moduleGSDRefID;
                    Debug.WriteLine($"使用Structure模型获取模块显示名称: {displayName}");
                    return displayName;
                }

                // 如果Structure模型中没有找到，尝试从Common模型获取模块
                var commonModule = interpreter.GetModule(moduleGSDRefID);
                if (commonModule != null)
                {
                    Debug.WriteLine($"从Common模型找到模块: {moduleGSDRefID}");

                    // 从模块信息中提取名称
                    if (commonModule.Info != null && commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                    {
                        // 优先使用模块名称，回退到GSDRefID
                        string displayName = !string.IsNullOrEmpty(commonModuleInfo.Name) ? commonModuleInfo.Name : moduleGSDRefID;
                        Debug.WriteLine($"使用Common模型获取模块显示名称: {displayName}");
                        return displayName;
                    }
                }

                Debug.WriteLine($"无法从GSDML文件获取模块名称，使用GSDRefID: {moduleGSDRefID}");
                return moduleGSDRefID;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块树视图显示名称时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return moduleGSDRefID;
            }
        }

        /// <summary>
        /// 从ModuleCatalog中提取IO长度信息
        /// </summary>
        private (int InputLength, int OutputLength) ExtractIOLengthFromModuleCatalog(PNConfigLib.DataModel.PCLCatalogObjects.ModuleCatalog moduleCatalog)
        {
            try
            {
                int inputLength = 0;
                int outputLength = 0;

                Debug.WriteLine($"开始从ModuleCatalog提取IO长度信息");

                // 从模块的虚拟子模块列表中获取IO数据长度
                if (moduleCatalog.VirtualSubmoduleList != null && moduleCatalog.VirtualSubmoduleList.Count > 0)
                {
                    Debug.WriteLine($"模块包含 {moduleCatalog.VirtualSubmoduleList.Count} 个虚拟子模块");

                    foreach (var submodule in moduleCatalog.VirtualSubmoduleList)
                    {
                        if (submodule?.IOData != null)
                        {
                            // 获取子模块的IO数据长度（以位为单位）
                            uint submoduleInputBits = submodule.IOData.InputLength;
                            uint submoduleOutputBits = submodule.IOData.OutputLength;

                            // 将位长度转换为字节长度（向上取整）
                            int inputBytes = (int)Math.Ceiling(submoduleInputBits / 8.0);
                            int outputBytes = (int)Math.Ceiling(submoduleOutputBits / 8.0);

                            // 累计所有子模块的IO长度
                            inputLength += inputBytes;
                            outputLength += outputBytes;

                            // 获取子模块的GsdId用于调试
                            string submoduleId = "Unknown";
                            try
                            {
                                if (submodule.AttributeAccess?.AttributeList?.ContainsKey("GsdId") == true)
                                {
                                    submoduleId = submodule.AttributeAccess.AttributeList["GsdId"]?.ToString() ?? "Unknown";
                                }
                            }
                            catch
                            {
                                // 忽略获取GsdId时的错误
                            }

                            Debug.WriteLine($"子模块 {submoduleId} IO长度:");
                            Debug.WriteLine($"  输入: {submoduleInputBits} 位 = {inputBytes} 字节");
                            Debug.WriteLine($"  输出: {submoduleOutputBits} 位 = {outputBytes} 字节");
                        }
                        else
                        {
                            Debug.WriteLine($"子模块没有IOData信息");
                        }
                    }
                }
                else
                {
                    Debug.WriteLine($"模块没有虚拟子模块列表");
                }

                Debug.WriteLine($"模块总IO长度: InputLength={inputLength} 字节, OutputLength={outputLength} 字节");
                return (inputLength, outputLength);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取IO长度信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return (0, 0);
            }
        }

        /// <summary>
        /// 创建备用模块信息
        /// </summary>
        private GSDMLModuleInfo CreateFallbackModuleInfo(string moduleKey, string moduleType)
        {
            Debug.WriteLine($"创建备用模块信息: moduleKey={moduleKey}, moduleType={moduleType}");

            // 尝试从模块类型推断合理的IO长度
            int inputLength = 0;
            int outputLength = 0;

            // 根据模块类型名称推断IO长度
            if (moduleType.Contains("DI") || moduleType.Contains("输入"))
            {
                inputLength = 4; // 默认4字节输入
            }
            else if (moduleType.Contains("DO") || moduleType.Contains("输出"))
            {
                outputLength = 4; // 默认4字节输出
            }
            else if (moduleType.Contains("DIO") || moduleType.Contains("混合"))
            {
                inputLength = 2; // 默认2字节输入
                outputLength = 2; // 默认2字节输出
            }
            else
            {
                // 对于未知类型，提供最小的IO长度
                inputLength = 1;
                outputLength = 1;
            }

            Debug.WriteLine($"备用模块信息IO长度: InputLength={inputLength}, OutputLength={outputLength}");

            return new GSDMLModuleInfo
            {
                GSDRefID = moduleKey,
                DisplayName = moduleType, // moduleType是从Header获取的显示名称
                ModuleKey = moduleKey,
                DefaultSlot = 1,
                InputLength = inputLength,
                OutputLength = outputLength,
                TotalDataLength = inputLength + outputLength
            };
        }

        /// <summary>
        /// 从TextId获取显示名称，参考ModuleConfigPage的实现
        /// </summary>
        private string? GetDisplayNameFromTextId(string textId)
        {
            try
            {
                // 获取当前设备配置
                DecentralDeviceConfig? deviceConfig = GetCurrentDeviceConfig();
                if (deviceConfig == null)
                {
                    Debug.WriteLine("无法获取设备配置，直接返回textId");
                    return textId;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 获取GSDML文件的绝对路径
                string gsdPath = deviceConfig.GetGSDMLAbsolutePath(projectDirectory);
                if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}，直接返回textId");
                    return textId;
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdPath}");
                    return textId;
                }

                // 尝试从Common模型获取模块
                var commonModule = interpreter.GetModule(textId);
                if (commonModule != null)
                {
                    Debug.WriteLine($"从Common模型找到模块: {textId}");

                    // 从模块信息中提取名称
                    if (commonModule.Info != null && commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                    {
                        // 优先使用模块名称，回退到textId
                        string displayName = !string.IsNullOrEmpty(commonModuleInfo.Name) ? commonModuleInfo.Name : textId;
                        Debug.WriteLine($"从Common模型获取模块显示名称: {displayName}");
                        return displayName;
                    }
                }

                Debug.WriteLine($"无法从GSDML文件获取模块名称，直接返回textId: {textId}");
                return textId;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从TextId获取显示名称时出错: {ex.Message}");
                return textId;
            }
        }

        /// <summary>
        /// 生成有意义的ModuleID
        /// </summary>
        private string GenerateModuleID(string gsdRefID, int slotNumber)
        {
            try
            {
                // 根据GSDRefID生成有意义的ModuleID
                if (!string.IsNullOrEmpty(gsdRefID))
                {
                    // 如果GSDRefID包含有意义的信息，提取并使用
                    string moduleType = ExtractModuleTypeFromGSDRefID(gsdRefID);
                    if (!string.IsNullOrEmpty(moduleType))
                    {
                        return $"{moduleType}_{slotNumber}";
                    }
                }

                // 默认格式
                return $"module_{slotNumber}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"生成ModuleID时出错: {ex.Message}");
                // 返回简化的ModuleID
                return $"module_{slotNumber}";
            }
        }

        /// <summary>
        /// 从GSDRefID中提取模块类型
        /// </summary>
        private string ExtractModuleTypeFromGSDRefID(string gsdRefID)
        {
            try
            {
                if (string.IsNullOrEmpty(gsdRefID))
                    return "";

                // 常见的模块类型映射
                var moduleTypeMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "DI 8x24VDC HF V2.0", "input_8ch" },
                    { "DO 8x24VDC/0.5A HF V2.0", "output_8ch" },
                    { "AI 8xU/I/RTD/TC ST V2.0", "analog_input_8ch" },
                    { "AO 4xU/I ST V2.0", "analog_output_4ch" },
                    { "ID_Mod_11", "input_64bytes" },
                    { "ID_Mod_12", "output_64bytes" },
                    { "ID_Mod_01", "input_1byte" },
                    { "ID_Mod_02", "output_1byte" }
                };

                // 查找匹配的模块类型
                foreach (var mapping in moduleTypeMap)
                {
                    if (gsdRefID.Contains(mapping.Key))
                    {
                        return mapping.Value;
                    }
                }

                // 如果没有找到匹配，尝试从GSDRefID中提取有意义的部分
                if (gsdRefID.ToLower().Contains("input") || gsdRefID.ToLower().Contains("di"))
                {
                    return "input_module";
                }
                else if (gsdRefID.ToLower().Contains("output") || gsdRefID.ToLower().Contains("do"))
                {
                    return "output_module";
                }
                else if (gsdRefID.ToLower().Contains("analog"))
                {
                    return "analog_module";
                }

                // 默认返回简化的名称
                return "module";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取模块类型时出错: {ex.Message}");
                return "module";
            }
        }

        /// <summary>
        /// 获取设备的PnDeviceId
        /// </summary>
        private int GetDevicePnDeviceId()
        {
            try
            {
                // 从设备配置中获取PnDeviceId，如果没有则使用默认值
                // 这里可以根据实际需要从设备配置或GSDML文件中获取
                return 1; // 默认值，实际应该从设备配置中获取
            }
            catch
            {
                return 1; // 默认值
            }
        }

        /// <summary>
        /// 获取当前用户模块数量（不包括主模块）
        /// </summary>
        private int GetUserModuleCount()
        {
            int count = 0;
            for (int i = 1; i < _modules.Count; i++) // 从第2行开始计算（跳过主模块）
            {
                if (!string.IsNullOrWhiteSpace(_modules[i].ModuleName) && _modules[i].Position != "0")
                {
                    count++;
                }
            }
            return count;
        }

        // 注意：CalculateNextInputAddress 和 CalculateNextOutputAddress 方法已被移除
        // 现在使用 GlobalAddressManager 进行全局地址分配管理

        /// <summary>
        /// 重新计算所有模块的地址分配 - 使用全局地址管理器
        /// </summary>
        /// <param name="forceRecalculate">是否强制重新计算，false时保持现有地址</param>
        private void RecalculateAllModuleAddresses(bool forceRecalculate = true)
        {
            try
            {
                Debug.WriteLine("=== 开始使用全局地址管理器重新计算模块地址 ===");

                // 按Position排序，只处理有模块名称的插槽（跳过空插槽）
                var sortedModules = _modules
                    .Where(m => int.TryParse(m.Position, out _) && !string.IsNullOrEmpty(m.ModuleName))
                    .OrderBy(m => int.TryParse(m.Position, out int pos) ? pos : 0)
                    .ToList();

                if (forceRecalculate)
                {
                    // 强制重新计算时，使用批量重新分配方法
                    Debug.WriteLine($"强制重新计算：使用批量重新分配方法");

                    // 准备模块地址需求列表
                    var moduleRequests = new List<(string moduleId, int inputLength, int outputLength)>();
                    foreach (var module in sortedModules)
                    {
                        string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";
                        moduleRequests.Add((moduleId, module.InputLength, module.OutputLength));
                    }

                    // 批量重新分配地址
                    var allocationResults = _globalAddressManager.BatchReallocateDeviceAddresses(_deviceName, moduleRequests);

                    // 更新模块的地址信息
                    foreach (var module in sortedModules)
                    {
                        string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";

                        if (allocationResults.TryGetValue(moduleId, out var addresses))
                        {
                            // 更新输入地址
                            if (addresses.inputAddress >= 0)
                            {
                                module.InputStartAddress = addresses.inputAddress.ToString();
                                module.PNIStartAddress = addresses.inputAddress.ToString();
                                Debug.WriteLine($"  模块 {module.ModuleName} 输入地址: {addresses.inputAddress} (长度:{module.InputLength})");
                            }
                            else
                            {
                                module.InputStartAddress = "";
                                module.PNIStartAddress = "";
                            }

                            // 更新输出地址
                            if (addresses.outputAddress >= 0)
                            {
                                module.OutputStartAddress = addresses.outputAddress.ToString();
                                module.PNQStartAddress = addresses.outputAddress.ToString();
                                Debug.WriteLine($"  模块 {module.ModuleName} 输出地址: {addresses.outputAddress} (长度:{module.OutputLength})");
                            }
                            else
                            {
                                module.OutputStartAddress = "";
                                module.PNQStartAddress = "";
                            }
                        }
                    }
                }
                else
                {
                    // 非强制重新计算时，使用原有逻辑
                    foreach (var module in sortedModules)
                    {
                        Debug.WriteLine($"处理模块: {module.ModuleName} (槽位:{module.Position}, InputLength:{module.InputLength}, OutputLength:{module.OutputLength})");

                        string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";

                        // 分配输入地址
                        if (module.InputLength > 0)
                        {
                            int inputAddress = _globalAddressManager.AllocateInputAddress(_deviceName, moduleId, module.InputLength);
                            if (inputAddress >= 0)
                            {
                                module.InputStartAddress = inputAddress.ToString();
                                module.PNIStartAddress = inputAddress.ToString(); // 同步PNIStartAddress
                                Debug.WriteLine($"  全局输入地址分配: {module.InputStartAddress} (长度:{module.InputLength})");
                            }
                            else
                            {
                                Debug.WriteLine($"  输入地址分配失败: 模块 {module.ModuleName}, 需要长度 {module.InputLength}");
                                module.InputStartAddress = "";
                                module.PNIStartAddress = "";
                            }
                        }
                        else
                        {
                            // 无输入地址需求时清空地址
                            module.InputStartAddress = "";
                            module.PNIStartAddress = "";
                            Debug.WriteLine($"  无输入地址需求");
                        }

                        // 分配输出地址
                        if (module.OutputLength > 0)
                        {
                            int outputAddress = _globalAddressManager.AllocateOutputAddress(_deviceName, moduleId, module.OutputLength);
                            if (outputAddress >= 0)
                            {
                                module.OutputStartAddress = outputAddress.ToString();
                                module.PNQStartAddress = outputAddress.ToString(); // 同步PNQStartAddress
                                Debug.WriteLine($"  全局输出地址分配: {module.OutputStartAddress} (长度:{module.OutputLength})");
                            }
                            else
                            {
                                Debug.WriteLine($"  输出地址分配失败: 模块 {module.ModuleName}, 需要长度 {module.OutputLength}");
                                module.OutputStartAddress = "";
                                module.PNQStartAddress = "";
                            }
                        }
                        else
                        {
                            // 无输出地址需求时清空地址
                            module.OutputStartAddress = "";
                            module.PNQStartAddress = "";
                            Debug.WriteLine($"  无输出地址需求");
                        }
                    }
                }

                // 清空所有空插槽的地址信息
                var emptySlots = _modules.Where(m => string.IsNullOrEmpty(m.ModuleName)).ToList();
                foreach (var emptySlot in emptySlots)
                {
                    emptySlot.InputStartAddress = "";
                    emptySlot.OutputStartAddress = "";
                    emptySlot.PNIStartAddress = "";
                    emptySlot.PNQStartAddress = "";
                    Debug.WriteLine($"清空插槽 {emptySlot.Position} 的地址信息");
                }

                // 获取全局地址分配统计信息
                var stats = _globalAddressManager.GetAllocationStats();
                Debug.WriteLine($"全局地址分配完成 - {stats}");

                // 调试：打印所有地址分配状态
                _globalAddressManager.DebugPrintAllAllocations();

                Debug.WriteLine("=== 模块地址重新计算完成 ===");

                // 同步所有模块地址到项目配置
                SynchronizeAllModuleAddressesToProjectConfig();

                // 标记为已修改，确保地址更改被保存
                _isModified = true;
                _projectManager.IsProjectModified = true;

                // 验证地址分配的正确性
                ValidateAddressAllocation();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重新计算模块地址时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 验证地址分配的正确性 - 使用全局地址管理器
        /// </summary>
        private void ValidateAddressAllocation()
        {
            try
            {
                Debug.WriteLine("=== 开始验证全局地址分配 ===");

                // 使用全局地址管理器进行验证
                var validationResult = _globalAddressManager.ValidateAddressAllocation();

                if (validationResult.IsValid)
                {
                    Debug.WriteLine("✓ 全局地址分配验证通过");
                }
                else
                {
                    Debug.WriteLine("✗ 全局地址分配验证失败:");
                    foreach (var error in validationResult.GetAllErrors())
                    {
                        Debug.WriteLine($"  - {error}");
                    }
                }

                // 显示当前设备的模块地址信息
                foreach (var module in _modules.Where(m => int.TryParse(m.Position, out _) && !string.IsNullOrEmpty(m.ModuleName)).OrderBy(m => int.TryParse(m.Position, out int pos) ? pos : 0))
                {
                    Debug.WriteLine($"模块 {module.ModuleName}: 输入地址={module.InputStartAddress} (长度:{module.InputLength}), 输出地址={module.OutputStartAddress} (长度:{module.OutputLength})");
                }

                // 显示全局地址分配统计
                var stats = _globalAddressManager.GetAllocationStats();
                Debug.WriteLine($"全局地址统计: {stats}");

                Debug.WriteLine("=== 地址分配验证完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证地址分配时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新模块的IO地址信息
        /// </summary>
        private void UpdateModuleIOAddresses(ModuleConfig moduleConfig, ModuleViewModel moduleVM)
        {
            try
            {
                Debug.WriteLine($"=== 更新模块 {moduleConfig.ModuleRefID} 的IO地址信息 ===");

                // 确保模块有子模块列表
                if (moduleConfig.Submodules == null)
                {
                    moduleConfig.Submodules = new List<SubmoduleConfig>();
                }

                // 如果没有子模块，创建一个默认子模块来承载地址信息
                if (moduleConfig.Submodules.Count == 0)
                {
                    var defaultSubmodule = new SubmoduleConfig
                    {
                        SubmoduleRefID = $"{moduleConfig.ModuleRefID}_Submodule",
                        AddressConfiguration = new AddressConfigurationConfig()
                    };
                    moduleConfig.Submodules.Add(defaultSubmodule);
                    Debug.WriteLine($"为模块 {moduleConfig.ModuleRefID} 创建默认子模块以承载地址信息");
                }

                // 更新第一个子模块的地址信息
                var firstSubmodule = moduleConfig.Submodules[0];

                if (int.TryParse(moduleVM.InputStartAddress, out int inputAddr))
                {
                    firstSubmodule.AddressConfiguration.InputAddress = inputAddr;
                    firstSubmodule.AddressConfiguration.InputStartAddress = moduleVM.InputStartAddress;
                    Debug.WriteLine($"更新模块 {moduleConfig.ModuleRefID} 输入地址: {inputAddr}");
                }

                if (int.TryParse(moduleVM.OutputStartAddress, out int outputAddr))
                {
                    firstSubmodule.AddressConfiguration.OutputAddress = outputAddr;
                    firstSubmodule.AddressConfiguration.OutputStartAddress = moduleVM.OutputStartAddress;
                    Debug.WriteLine($"更新模块 {moduleConfig.ModuleRefID} 输出地址: {outputAddr}");
                }

                Debug.WriteLine($"=== 模块 {moduleConfig.ModuleRefID} IO地址信息更新完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新模块IO地址信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 创建带有GSDML信息的模块配置
        /// </summary>
        private ModuleConfig CreateModuleConfigWithGSDML(string moduleRefID, string gsdRefID, int slotNumber, string moduleName)
        {
            var moduleConfig = new ModuleConfig
            {
                ModuleRefID = moduleRefID,
                SlotNumber = slotNumber,
                GSDRefID = gsdRefID, // 设置GSDML引用ID
                Submodules = new List<SubmoduleConfig>()
            };

            Debug.WriteLine($"创建模块配置: ModuleRefID={moduleRefID}, GSDRefID={gsdRefID}, SlotNumber={slotNumber}");

            return moduleConfig;
        }

        /// <summary>
        /// 创建带有IO地址的子模块配置
        /// </summary>
        private SubmoduleConfig CreateSubmoduleConfigWithIOAddresses(
            string submoduleRefID,
            string submoduleName,
            int subslotNumber,
            string slotPosition,
            string inputStartAddress,
            string outputStartAddress,
            string comment)
        {
            var submoduleConfig = DeviceConfigFactory.CreateSubmoduleConfig(
                submoduleRefID,
                submoduleName,
                subslotNumber,
                slotPosition,
                inputStartAddress,
                outputStartAddress,
                comment
            );

            // 确保IO地址配置正确设置
            if (int.TryParse(inputStartAddress, out int inputAddr) && inputAddr > 0)
            {
                submoduleConfig.AddressConfiguration.InputAddress = inputAddr;
            }

            if (int.TryParse(outputStartAddress, out int outputAddr) && outputAddr > 0)
            {
                submoduleConfig.AddressConfiguration.OutputAddress = outputAddr;
            }

            Debug.WriteLine($"创建子模块配置: {submoduleRefID}, 输入地址: {inputStartAddress}, 输出地址: {outputStartAddress}");

            return submoduleConfig;
        }
        
        /// <summary>
        /// 删除模块按钮点击事件（支持批量删除）
        /// </summary>
        private void RemoveModuleButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取所有选中的非内置模块且有模块名称的模块（空插槽不能删除）
            var selectedModules = _modules.Where(m => m.IsSelected && !m.IsBuiltIn && !string.IsNullOrEmpty(m.ModuleName)).ToList();

            if (selectedModules.Count == 0)
            {
                MessageBox.Show("请先选择要删除的模块", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 构建确认删除的消息
            string confirmMessage;
            if (selectedModules.Count == 1)
            {
                confirmMessage = $"确定要删除模块 \"{selectedModules[0].ModuleName}\" 吗？\n\n注意：插槽将保留，可重新添加模块。";
            }
            else
            {
                var moduleNames = selectedModules.Select(m => m.ModuleName).ToList();
                confirmMessage = $"确定要删除以下 {selectedModules.Count} 个模块吗？\n\n" +
                               string.Join("\n", moduleNames.Select(name => $"• {name}")) +
                               "\n\n注意：插槽将保留，可重新添加模块。";
            }

            // 提示确认删除
            MessageBoxResult result = MessageBox.Show(
                confirmMessage,
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 执行批量删除
                PerformBatchDelete(selectedModules);
            }
        }

        /// <summary>
        /// IO状态按钮点击事件
        /// </summary>
        private void IOStatusButton_Click(object sender, RoutedEventArgs e)
        {
            ShowIOLengthStatus();
        }

        /// <summary>
        /// 执行批量删除操作（只清空模块数据，保留插槽行）
        /// </summary>
        private void PerformBatchDelete(List<ModuleViewModel> modulesToDelete)
        {
            try
            {
                Debug.WriteLine($"开始批量删除 {modulesToDelete.Count} 个模块（保留插槽行）");

                // 收集要删除的模块信息（用于导航菜单移除和设备配置删除）
                var moduleInfos = modulesToDelete.Select(m => new
                {
                    Module = m,
                    ModuleId = m.ModuleID,
                    Position = m.Position,
                    ModuleName = m.ModuleName
                }).ToList();

                // 先从设备配置中删除模块（在清空数据之前）
                foreach (var info in moduleInfos)
                {
                    RemoveModuleFromDeviceConfigByInfo(info.ModuleId, info.Position);
                }

                // 移除对应的导航菜单项
                foreach (var info in moduleInfos)
                {
                    if (!string.IsNullOrEmpty(info.ModuleId))
                    {
                        RemoveModuleFromNavigationByModuleId(info.ModuleId);
                    }
                }

                // 清空模块数据，但保留插槽行
                foreach (var module in modulesToDelete)
                {
                    Debug.WriteLine($"清空模块数据: {module.ModuleName} (插槽: {module.Position})");

                    // 保存原始的Position和Index，以及IsBuiltIn状态
                    string originalPosition = module.Position;
                    int originalIndex = module.Index;
                    bool originalIsBuiltIn = module.IsBuiltIn;

                    // 释放模块的全局地址分配
                    string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";
                    _globalAddressManager.ReleaseModuleAddresses(_deviceName, moduleId);
                    Debug.WriteLine($"已释放模块 {moduleId} 的全局地址分配");

                    // 清空模块数据，恢复为空插槽状态
                    module.ModuleName = "";
                    module.SubmoduleName = "";
                    module.InterfaceSlot = "";
                    module.PNIStartAddress = "";
                    module.InputStartAddress = "";
                    module.PNQStartAddress = "";
                    module.OutputStartAddress = "";
                    module.ModuleID = "";
                    module.GSDRefID = "";
                    module.InputLength = 0;
                    module.OutputLength = 0;
                    module.IsSelected = false;

                    // 恢复原始属性
                    module.Position = originalPosition;
                    module.Index = originalIndex;
                    module.IsBuiltIn = originalIsBuiltIn;

                    Debug.WriteLine($"插槽 {originalPosition} 已清空，恢复为可用状态");
                }

                // 重新计算所有模块的地址（删除模块后需要重新分配地址）
                RecalculateAllModuleAddresses();

                // 标记为已修改
                _isModified = true;

                // 更新删除按钮状态
                UpdateRemoveButtonState();

                // 刷新UI显示
                ModulesDataGrid.Items.Refresh();

                Debug.WriteLine($"批量删除完成，共清空 {modulesToDelete.Count} 个模块插槽，地址已重新分配");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"批量删除模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"删除模块时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 上一步按钮点击事件
        /// </summary>
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            // 导航回控制器配置页面
            var navigationService = ServiceLocator.GetService<INavigationService>();
            if (navigationService != null)
            {
                navigationService.Navigate("ControllerConfigPage");
            }
        }
        
        /// <summary>
        /// 下一步按钮点击事件
        /// </summary>
        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 保存模块配置
                SaveModuleConfiguration();
                
                // 获取导航服务
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService == null)
                {
                    Debug.WriteLine("无法获取导航服务");
                    MessageBox.Show("无法获取导航服务", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                
                // 获取主窗口中的导航ListBox
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Debug.WriteLine("无法获取主窗口");
                    return;
                }
                
                var navigationListBox = mainWindow.FindName("NavigationListBox") as ListBox;
                if (navigationListBox == null)
                {
                    Debug.WriteLine("无法获取导航ListBox");
                    return;
                }
                
                // 查找当前设备在导航列表中的位置
                int currentIndex = -1;
                string currentDeviceTag = $"DeviceConfig_{_deviceName}";
                
                for (int i = 0; i < navigationListBox.Items.Count; i++)
                {
                    if (navigationListBox.Items[i] is ListBoxItem item && item.Tag?.ToString() == currentDeviceTag)
                    {
                        currentIndex = i;
                        Debug.WriteLine($"找到当前页面在导航列表中的位置: {i}, Tag: {currentDeviceTag}");
                        break;
                    }
                }
                
                if (currentIndex >= 0 && currentIndex < navigationListBox.Items.Count - 1)
                {
                    // 直接获取下一个导航项，无论它是什么类型
                    var nextItem = navigationListBox.Items[currentIndex + 1] as ListBoxItem;
                    if (nextItem != null && nextItem.Tag != null)
                    {
                        string nextTag = nextItem.Tag.ToString()!;
                        Debug.WriteLine($"下一个导航项: {nextTag}");
                        
                        try
                        {
                            // 根据页面类型进行不同的导航
                            if (nextTag.StartsWith("DeviceConfig_"))
                            {
                                // 设备配置页面
                                string nextDeviceName = nextTag.Substring("DeviceConfig_".Length);
                                Debug.WriteLine($"导航到设备配置页面: {nextTag}, 参数: {nextDeviceName}");
                                navigationService.Navigate(nextTag, nextDeviceName);
                                return;
                            }
                            else if (nextTag.StartsWith("DeviceDetail_"))
                            {
                                // 模块配置页面
                                string deviceName = nextTag.Substring("DeviceDetail_".Length);
                                Debug.WriteLine($"导航到模块配置页面: {nextTag}, 设备: {deviceName}");
                                
                                // 查找该设备的类型
                                string deviceType = "";
                                if (_projectManager?.CurrentProject != null)
                                {
                                    Debug.WriteLine($"获取当前项目中添加的设备");
                                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                                    {
                                        if (device.DeviceRefID == deviceName)
                                        {
                                            deviceType = device.DeviceType; // 使用DeviceType而不是DeviceRefID
                                            break;
                                        }
                                    }
                                }
                                
                                // 创建导航参数，指向设备的第一个模块
                                var moduleParams = new NavigationModuleParams(
                                    deviceName,
                                    deviceType,
                                    "", // 默认无子模块名
                                    0   // 默认第一个模块
                                );
                                
                                navigationService.Navigate(nextTag, moduleParams);
                                return;
                            }
                            else
                            {
                                // 其他类型的页面
                                Debug.WriteLine($"导航到其他类型页面: {nextTag}");
                                navigationService.Navigate(nextTag);
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"导航到下一个页面出错: {ex.Message}");
                            MessageBox.Show($"导航到下一个页面时出错: {ex.Message}", "导航错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("当前页面索引无效或已是最后一个页面");
                }
                
                // 如果没有找到下一个导航项或者导航失败，则返回控制器配置页面
                Debug.WriteLine("返回控制器配置页面");
                navigationService.Navigate("ControllerConfigPage");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"下一步按钮点击处理出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"处理下一步操作时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 生成按钮点击事件
        /// </summary>
        private void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            // 保存模块配置
            SaveModuleConfiguration();
            
            // 生成配置
            MessageBox.Show("生成配置功能尚未实现。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 提示是否放弃更改
            MessageBoxResult result = MessageBox.Show(
                "是否放弃更改并返回?",
                "确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
                
            if (result == MessageBoxResult.Yes)
            {
                // 导航回控制器配置页面
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService != null)
                {
                    navigationService.Navigate("ControllerConfigPage");
                }
            }
        }
        
        /// <summary>
        /// 保存模块配置
        /// </summary>
        private void SaveModuleConfiguration()
        {
            if (_projectManager?.CurrentProject == null) return;
            
            // 查找对应的设备配置
            DecentralDeviceConfig? deviceConfig = null;
            int deviceIndex = -1;
            
            for (int i = 0; i < _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count; i++)
            {
                var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices[i];
                if (device.DeviceRefID == _deviceName)
                {
                    deviceConfig = device;
                    deviceIndex = i;
                    break;
                }
            }
            
            if (deviceConfig != null && deviceIndex >= 0)
            {
                try
                {
                    // 确保设备有模块列表
                    if (deviceConfig.Modules == null)
                    {
                        deviceConfig.Modules = new List<ModuleConfig>();
                    }
                    
                    // 将视图模型的模块保存到设备配置中
                    // 注意：跳过主模块（第一个模块），因为DAP主模块不应保存到Modules配置中
                    if (_modules.Count > 0)
                    {
                        var mainModule = _modules[0];
                        Debug.WriteLine($"跳过DAP主模块 {mainModule.ModuleName}，不保存到Modules配置中");

                        // DAP主模块的配置应该保存在设备级别，而不是Modules数组中
                        // 这里可以添加设备级别的配置保存逻辑，如IP地址等
                        // 例如：deviceConfig.DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = ...
                    }
                    
                    // 处理用户添加的模块（跳过主模块）
                    for (int i = 1; i < _modules.Count; i++)
                    {
                        var moduleVM = _modules[i];

                        // 跳过空模块或主模块
                        if (string.IsNullOrWhiteSpace(moduleVM.ModuleName) || moduleVM.Position == "0")
                        {
                            continue;
                        }

                        // 尝试解析位置
                        if (!int.TryParse(moduleVM.Position, out int position))
                        {
                            position = i + 1; // 基于索引计算默认位置
                        }
                        
                        // 查找是否已有此位置的模块
                        ModuleConfig? existingModule = null;
                        for (int j = 0; j < deviceConfig.Modules.Count; j++)
                        {
                            if (deviceConfig.Modules[j].SlotNumber == position)
                            {
                                existingModule = deviceConfig.Modules[j];
                                break;
                            }
                        }

                        if (existingModule != null)
                        {
                            // 更新现有模块
                            // General配置已移除，使用ModuleRefID

                            // 更新模块的IO地址信息
                            UpdateModuleIOAddresses(existingModule, moduleVM);

                            Debug.WriteLine($"更新了设备位置 {position} 的模块地址信息");
                        }
                        else
                        {
                            // 创建新模块，使用正确的GSDML信息
                            string moduleRefID = !string.IsNullOrEmpty(moduleVM.ModuleID) ? moduleVM.ModuleID : $"{moduleVM.ModuleName}_Module_{position}";
                            string gsdRefID = !string.IsNullOrEmpty(moduleVM.GSDRefID) ? moduleVM.GSDRefID : moduleVM.ModuleName;

                            // 直接创建模块配置，不创建子模块（用户添加的是模块，不是子模块）
                            var newModule = new ModuleConfig
                            {
                                ModuleRefID = moduleRefID,
                                SlotNumber = position,
                                GSDRefID = gsdRefID,
                                Submodules = new List<SubmoduleConfig>()
                            };

                            deviceConfig.Modules.Add(newModule);
                            Debug.WriteLine($"添加了新模块到设备位置 {position}，ModuleID: {moduleRefID}，GSDRefID: {gsdRefID}");
                        }
                    }
                    
                    // 更新项目修改状态
                    _projectManager.IsProjectModified = true;
                    Debug.WriteLine($"成功保存设备 {_deviceName} 的模块配置");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"保存模块配置时出错: {ex.Message}");
                    Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    MessageBox.Show($"保存模块配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        

        
        /// <summary>
        /// 导航到模块配置页面
        /// </summary>
        private void NavigateToModuleConfigPage(ModuleViewModel selectedModule)
        {
            try
            {
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService != null)
                {
                    // 创建页面键名和参数 - 使用设备名和插槽号的格式
                    string moduleConfigPageKey = $"ModuleConfig_{_deviceName}_{selectedModule.Position}";
                    int moduleIndex = _modules.IndexOf(selectedModule);

                    // 创建导航参数
                    var moduleParams = new NavigationModuleParams(
                        _deviceName,
                        selectedModule.ModuleName,
                        selectedModule.SubmoduleName,
                        moduleIndex
                    );

                    // 注册模块配置页面
                    try
                    {
                        if (navigationService.CurrentPage != moduleConfigPageKey)
                        {
                            navigationService.RegisterPage(moduleConfigPageKey, typeof(ModuleConfigPage));
                            Debug.WriteLine($"注册模块配置页面成功: {moduleConfigPageKey}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 页面可能已经注册
                        Debug.WriteLine($"注册模块配置页面出错 (可能已存在): {ex.Message}");
                    }
                    
                    // 导航到模块配置页面
                    try
                    {
                        navigationService.Navigate(moduleConfigPageKey, moduleParams);
                        Debug.WriteLine($"导航到模块配置页面: {moduleConfigPageKey}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"导航到模块配置页面出错: {ex.Message}");
                        MessageBox.Show($"导航到模块配置页面失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"NavigateToModuleConfigPage error: {ex.Message}");
                MessageBox.Show($"导航到模块配置页面出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 注册UI事件以跟踪修改
        /// </summary>
        private void RegisterUIChangeEvents()
        {
            // 监听模块列表变更
            _modules.CollectionChanged += (s, e) => _isModified = true;

            // 监听模块数据网格编辑
            ModulesDataGrid.CellEditEnding += (s, e) => _isModified = true;

            // 监听UpdateTime下拉框变更
            UpdateTimeComboBox.SelectionChanged += OnUpdateTimeChanged;

            Debug.WriteLine("UI事件已注册，设备信息编辑功能已移至模块配置页面");
        }

        /// <summary>
        /// 立即保存新添加的模块到设备配置中
        /// </summary>
        private void SaveNewModuleToDeviceConfig(ModuleViewModel moduleVM, int slotNumber, GSDMLModuleInfo moduleInfo)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("ProjectManager或CurrentProject为null，无法保存模块");
                    return;
                }

                // 查找对应的设备配置
                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                if (deviceConfig == null)
                {
                    Debug.WriteLine($"找不到设备配置: {_deviceName}");
                    return;
                }

                // 确保设备有模块列表
                if (deviceConfig.Modules == null)
                {
                    deviceConfig.Modules = new List<ModuleConfig>();
                }

                // 检查是否已存在相同槽位的模块
                var existingModule = deviceConfig.Modules.FirstOrDefault(m => m.SlotNumber == slotNumber);
                if (existingModule != null)
                {
                    Debug.WriteLine($"槽位 {slotNumber} 已存在模块，跳过添加");
                    return;
                }

                // 创建新的模块配置
                string moduleRefID = !string.IsNullOrEmpty(moduleVM.ModuleID) ? moduleVM.ModuleID : $"{moduleInfo.GSDRefID}_Module_{slotNumber}";
                string gsdRefID = !string.IsNullOrEmpty(moduleVM.GSDRefID) ? moduleVM.GSDRefID : moduleInfo.GSDRefID;

                var newModuleConfig = new ModuleConfig
                {
                    ModuleRefID = moduleRefID,
                    SlotNumber = slotNumber,
                    GSDRefID = gsdRefID,
                    Submodules = new List<SubmoduleConfig>()
                };

                // 设置模块的IO地址信息
                UpdateModuleIOAddresses(newModuleConfig, moduleVM);

                // 添加到设备配置
                deviceConfig.Modules.Add(newModuleConfig);

                // 标记项目已修改
                _projectManager.IsProjectModified = true;

                Debug.WriteLine($"立即保存模块到设备配置: ModuleID={moduleRefID}, SlotNumber={slotNumber}, GSDRefID={gsdRefID}, InputAddr={moduleVM.InputStartAddress}, OutputAddr={moduleVM.OutputStartAddress}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存新模块到设备配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 为新添加的模块注册导航菜单
        /// </summary>
        private void RegisterModuleNavigation(ModuleViewModel module)
        {
            try
            {
                // 获取MainWindow实例
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Debug.WriteLine("无法获取MainWindow实例");
                    return;
                }

                // 创建模块页面键 - 使用设备名和插槽号的格式
                string modulePageKey = $"ModuleConfig_{_deviceName}_{module.Position}";
                string moduleName = !string.IsNullOrEmpty(module.ModuleName) ? module.ModuleName : module.ModuleID;

                // 创建显示名称：模块名(插槽号)
                string displayName = $"{moduleName}({module.Position})";

                // 调试信息：显示当前设备名称和模块信息
                Debug.WriteLine($"=== RegisterModuleNavigation 调试信息 ===");
                Debug.WriteLine($"当前设备名称 (_deviceName): {_deviceName}");
                Debug.WriteLine($"模块ID: {module.ModuleID}");
                Debug.WriteLine($"模块页面键: {modulePageKey}");
                Debug.WriteLine($"模块显示名称: {displayName}");

                if (_projectManager?.CurrentProject != null)
                {
                    Debug.WriteLine("项目中的所有设备:");
                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                    {
                        Debug.WriteLine($"  - DeviceRefID: {device.DeviceRefID}, DeviceType: {device.DeviceType}");
                    }
                }

                // 添加模块导航项到MainWindow的导航菜单
                mainWindow.AddModuleToNavigation(_deviceName, displayName, modulePageKey, module.ModuleID);

                Debug.WriteLine($"已为模块 {displayName} 注册导航菜单: {modulePageKey}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"注册模块导航菜单时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从导航菜单中移除模块（使用ModuleID）
        /// </summary>
        private void RemoveModuleFromNavigationByModuleId(string moduleId)
        {
            try
            {
                // 获取MainWindow实例
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Debug.WriteLine("无法获取MainWindow实例");
                    return;
                }

                // 创建模块页面键（新格式：ModuleConfig_{ModuleID}）
                string modulePageKey = $"ModuleConfig_{moduleId}";

                // 从MainWindow的导航菜单中移除模块项
                mainWindow.RemoveModuleFromNavigation(modulePageKey);

                Debug.WriteLine($"已从导航菜单移除模块: {modulePageKey}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"移除模块导航菜单时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从导航菜单中移除模块（使用索引，备用方法）
        /// </summary>
        private void RemoveModuleFromNavigation(int moduleIndex)
        {
            try
            {
                // 获取MainWindow实例
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Debug.WriteLine("无法获取MainWindow实例");
                    return;
                }

                // 创建模块页面键（旧格式：ModuleConfig_{设备名}_{索引}）
                string modulePageKey = $"ModuleConfig_{_deviceName}_{moduleIndex}";

                // 从MainWindow的导航菜单中移除模块项
                mainWindow.RemoveModuleFromNavigation(modulePageKey);

                Debug.WriteLine($"已从导航菜单移除模块（备用方法）: {modulePageKey}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"移除模块导航菜单时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从设备配置中删除模块（使用模块信息）
        /// </summary>
        private void RemoveModuleFromDeviceConfigByInfo(string moduleId, string position)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("ProjectManager或CurrentProject为null，无法删除模块");
                    return;
                }

                // 查找对应的设备配置
                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                if (deviceConfig == null)
                {
                    Debug.WriteLine($"找不到设备配置: {_deviceName}");
                    return;
                }

                // 如果设备有模块列表，从中删除对应的模块
                if (deviceConfig.Modules != null)
                {
                    if (!string.IsNullOrEmpty(moduleId))
                    {
                        // 使用ModuleRefID来匹配模块
                        var moduleToRemove = deviceConfig.Modules.FirstOrDefault(m => m.ModuleRefID == moduleId);
                        if (moduleToRemove != null)
                        {
                            deviceConfig.Modules.Remove(moduleToRemove);
                            Debug.WriteLine($"已从设备配置中删除模块: {moduleId}");
                        }
                        else
                        {
                            Debug.WriteLine($"在设备配置中找不到要删除的模块: {moduleId}");
                        }
                    }
                    else
                    {
                        // 通过Position查找要删除的模块（需要转换为int类型）
                        if (int.TryParse(position, out int slotNumber))
                        {
                            var moduleToRemove = deviceConfig.Modules.FirstOrDefault(m => m.SlotNumber == slotNumber);
                            if (moduleToRemove != null)
                            {
                                deviceConfig.Modules.Remove(moduleToRemove);
                                Debug.WriteLine($"已从设备配置中删除插槽 {position} 的模块");
                            }
                        }
                    }
                }

                // 标记项目已修改
                _projectManager.IsProjectModified = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从设备配置中删除模块时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从设备配置中删除模块
        /// </summary>
        private void RemoveModuleFromDeviceConfig(ModuleViewModel moduleVM)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("ProjectManager或CurrentProject为null，无法删除模块");
                    return;
                }

                // 查找对应的设备配置
                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                if (deviceConfig == null)
                {
                    Debug.WriteLine($"找不到设备配置: {_deviceName}");
                    return;
                }

                // 如果设备有模块列表，从中删除对应的模块
                if (deviceConfig.Modules != null)
                {
                    // 获取模块ID（可能已被清空，所以需要从传入的参数获取）
                    string moduleIdToRemove = moduleVM.ModuleID;

                    // 如果ModuleID已被清空，尝试通过Position查找
                    if (string.IsNullOrEmpty(moduleIdToRemove))
                    {
                        // 通过Position和其他特征查找要删除的模块（需要转换为int类型）
                        if (int.TryParse(moduleVM.Position, out int slotNumber))
                        {
                            var moduleToRemove = deviceConfig.Modules.FirstOrDefault(m =>
                                m.SlotNumber == slotNumber);

                            if (moduleToRemove != null)
                            {
                                deviceConfig.Modules.Remove(moduleToRemove);
                                Debug.WriteLine($"已从设备配置中删除插槽 {moduleVM.Position} 的模块");
                            }
                        }
                    }
                    else
                    {
                        // 使用ModuleRefID来匹配模块（因为ModuleConfig类使用ModuleRefID属性）
                        var moduleToRemove = deviceConfig.Modules.FirstOrDefault(m => m.ModuleRefID == moduleIdToRemove);
                        if (moduleToRemove != null)
                        {
                            deviceConfig.Modules.Remove(moduleToRemove);
                            Debug.WriteLine($"已从设备配置中删除模块: {moduleIdToRemove}");
                        }
                        else
                        {
                            Debug.WriteLine($"在设备配置中找不到要删除的模块: {moduleIdToRemove}");
                        }
                    }
                }

                // 标记项目已修改
                _projectManager.IsProjectModified = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从设备配置中删除模块时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存设备配置
        /// </summary>
        private void SaveDeviceConfiguration()
        {
            if (_projectManager?.CurrentProject == null)
            {
                Debug.WriteLine("ProjectManager或CurrentProject为null，无法保存设备配置");
                return;
            }

            try
            {
                Debug.WriteLine($"开始保存设备配置: {_deviceName}");
                Debug.WriteLine($"当前项目中设备数量: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");

                // 查找对应的设备配置
                DecentralDeviceConfig? deviceConfig = null;
                int deviceIndex = -1;

                for (int i = 0; i < _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count; i++)
                {
                    var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices[i];
                    Debug.WriteLine($"检查设备 {i}: {device.DeviceRefID}");
                    if (device.DeviceRefID == _deviceName)
                    {
                        deviceConfig = device;
                        deviceIndex = i;
                        Debug.WriteLine($"找到匹配的设备，索引: {deviceIndex}");
                        break;
                    }
                }

                if (deviceConfig != null)
                {
                    // 设备名称编辑功能已移至模块配置页面
                    // 这里只保存模块配置相关的数据
                    Debug.WriteLine($"保存设备配置: {_deviceName}");

                    // 保存实时设置配置
                    SaveRealTimeSettings(deviceConfig);
                    Debug.WriteLine("已保存实时设置配置");

                    // 保存MRP配置
                    SaveMRPConfiguration(deviceConfig);
                    Debug.WriteLine("已保存MRP配置");

                    // 模块配置已在其他地方保存，这里不需要重复调用
                    // SaveModuleConfiguration(); // 移除重复调用

                    // 验证设备仍然存在于项目中
                    bool deviceStillExists = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                        .Any(d => d.DeviceRefID == _deviceName);

                    if (!deviceStillExists)
                    {
                        Debug.WriteLine($"警告：设备 {_deviceName} 在保存后不再存在于项目中！");
                        MessageBox.Show($"警告：设备 {_deviceName} 在保存后不再存在于项目中！", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }

                    // 保存全局地址分配状态
                    try
                    {
                        _globalAddressManager.SaveAddressAllocationToProject();
                        Debug.WriteLine("已保存全局地址分配状态");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"保存全局地址分配状态时出错: {ex.Message}");
                    }

                    // 标记项目已修改
                    _projectManager.IsProjectModified = true;

                    // 重置修改状态
                    _isModified = false;

                    Debug.WriteLine($"设备配置已保存: {_deviceName}，项目中设备数量: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");
                }
                else
                {
                    Debug.WriteLine($"找不到设备: {_deviceName}");
                    Debug.WriteLine("当前项目中的所有设备:");
                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                    {
                        Debug.WriteLine($"  - {device.DeviceRefID}");
                    }
                    MessageBox.Show($"找不到设备: {_deviceName}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存设备配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"保存设备配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存实时设置配置
        /// </summary>
        private void SaveRealTimeSettings(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                // 确保高级配置结构存在
                if (deviceConfig.DecentralDeviceInterface.AdvancedConfiguration == null)
                {
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration = new DecentralAdvancedConfigurationConfig();
                }

                var realTimeSettings = deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.RealTimeSettings;
                if (realTimeSettings == null)
                {
                    realTimeSettings = new DecentralRealTimeSettingsConfig();
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.RealTimeSettings = realTimeSettings;
                }

                // 确保更新时间模式为Manual（因为用户手动设置了值）
                _updateTime.Mode = UpdateTimeMode.Manual;

                // 保存更新时间设置
                realTimeSettings.IOCycle.UpdateTime = _updateTime;

                // 保存数据保持时间设置
                realTimeSettings.IOCycle.AcceptedUpdateCyclesWithoutIOData = _acceptedUpdateCyclesWithoutIOData;

                Debug.WriteLine($"已保存实时设置 - 更新时间: {_updateTime.Value}ms (模式: {_updateTime.Mode}), 数据保持: {_acceptedUpdateCyclesWithoutIOData}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存实时设置时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载已保存的MRP配置
        /// </summary>
        private void LoadSavedMRPConfiguration(DecentralDeviceConfig deviceConfig)
        {
            // 临时禁用初始化标志，避免在加载时触发保存
            bool wasInitialized = _isInitialized;
            _isInitialized = false;

            try
            {
                Debug.WriteLine("=== LoadSavedMRPConfiguration 开始 ===");

                // 检查是否有已保存的MRP配置（从AdvancedConfiguration中加载）
                var mediaRedundancy = deviceConfig.DecentralDeviceInterface?.AdvancedConfiguration?.MediaRedundancy;
                if (mediaRedundancy?.MrpRings != null && mediaRedundancy.MrpRings.Count > 0)
                {
                    var mrpRing = mediaRedundancy.MrpRings[0]; // 取第一个MRP环配置
                    Debug.WriteLine($"从AdvancedConfiguration找到已保存的MRP配置: Role={mrpRing.MrpRole}, Ports={string.Join(",", mrpRing.RingPorts.Select(p => p.PortNumber))}");

                    // 设置MRP角色
                    SetMRPRoleSelection(mrpRing.MrpRole);

                    // 设置Ring Port配置
                    SetRingPortSelection(mrpRing.RingPorts);

                    Debug.WriteLine("已从AdvancedConfiguration加载保存的MRP配置");
                }
                // 注意：旧的MediaRedundancy属性已被移除，不再需要向后兼容处理
                else
                {
                    Debug.WriteLine("没有找到已保存的MRP配置，使用默认值");
                    // 确保默认选择"Not device in the ring"
                    SetMRPRoleSelection("Not device in the ring");
                }

                // 恢复初始化标志
                _isInitialized = wasInitialized;

                Debug.WriteLine("=== LoadSavedMRPConfiguration 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载已保存的MRP配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 确保在异常情况下也恢复初始化标志
                _isInitialized = wasInitialized;
            }
        }

        /// <summary>
        /// 设置MRP角色选择
        /// </summary>
        private void SetMRPRoleSelection(string mrpRole)
        {
            try
            {
                foreach (ComboBoxItem item in MediaRedundancyRoleComboBox.Items)
                {
                    if (item.Content?.ToString() == mrpRole)
                    {
                        MediaRedundancyRoleComboBox.SelectedItem = item;
                        Debug.WriteLine($"设置MRP角色选择: {mrpRole}");
                        return;
                    }
                }
                Debug.WriteLine($"未找到匹配的MRP角色: {mrpRole}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置MRP角色选择时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置Ring Port选择
        /// </summary>
        private void SetRingPortSelection(List<RingPortConfig> ringPorts)
        {
            try
            {
                if (ringPorts.Count >= 1)
                {
                    string port1Name = $"Port{ringPorts[0].PortNumber}";
                    SetComboBoxSelection(RingPort1ComboBox, port1Name);
                    Debug.WriteLine($"设置Ring Port 1: {port1Name}");
                }

                if (ringPorts.Count >= 2)
                {
                    string port2Name = $"Port{ringPorts[1].PortNumber}";
                    SetComboBoxSelection(RingPort2ComboBox, port2Name);
                    Debug.WriteLine($"设置Ring Port 2: {port2Name}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置Ring Port选择时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置ComboBox选择项
        /// </summary>
        private void SetComboBoxSelection(ComboBox comboBox, string value)
        {
            try
            {
                foreach (ComboBoxItem item in comboBox.Items)
                {
                    if (item.Content?.ToString() == value)
                    {
                        comboBox.SelectedItem = item;
                        return;
                    }
                }
                Debug.WriteLine($"未找到匹配的ComboBox项: {value}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置ComboBox选择时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存MRP配置
        /// </summary>
        private void SaveMRPConfiguration(DecentralDeviceConfig deviceConfig)
        {
            try
            {
                Debug.WriteLine("=== SaveMRPConfiguration 开始 ===");

                // 确保高级配置结构存在
                if (deviceConfig.DecentralDeviceInterface.AdvancedConfiguration == null)
                {
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration = new DecentralAdvancedConfigurationConfig();
                }

                // 检查MRP角色选择
                if (MediaRedundancyRoleComboBox.SelectedItem is ComboBoxItem selectedRoleItem)
                {
                    string selectedRole = selectedRoleItem.Content?.ToString() ?? "";
                    Debug.WriteLine($"选中的MRP角色: {selectedRole}");

                    // 如果选择的是"Not device in the ring"，清除MRP配置
                    if (selectedRole == "Not device in the ring")
                    {
                        deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy = null;
                        Debug.WriteLine("清除MRP配置（设备不在环中）");
                        return;
                    }

                    // 获取Ring Port配置
                    var ringPorts = new List<RingPortConfig>();

                    // 获取Ring Port 1
                    if (RingPort1ComboBox.SelectedItem is ComboBoxItem ringPort1Item)
                    {
                        string port1 = ringPort1Item.Content?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(port1))
                        {
                            // 提取纯数字（去除"Port"前缀）
                            string portNumber1 = ExtractPortNumber(port1);
                            if (!string.IsNullOrEmpty(portNumber1))
                            {
                                ringPorts.Add(new RingPortConfig { PortNumber = portNumber1 });
                                Debug.WriteLine($"添加Ring Port 1: {portNumber1}");
                            }
                        }
                    }

                    // 获取Ring Port 2
                    if (RingPort2ComboBox.SelectedItem is ComboBoxItem ringPort2Item)
                    {
                        string port2 = ringPort2Item.Content?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(port2))
                        {
                            // 提取纯数字（去除"Port"前缀）
                            string portNumber2 = ExtractPortNumber(port2);
                            if (!string.IsNullOrEmpty(portNumber2))
                            {
                                ringPorts.Add(new RingPortConfig { PortNumber = portNumber2 });
                                Debug.WriteLine($"添加Ring Port 2: {portNumber2}");
                            }
                        }
                    }

                    // 创建MRP配置
                    if (ringPorts.Count > 0)
                    {
                        var mrpRing = new MrpRingConfig
                        {
                            MrpDomainRefID = "mrpdomain-1",
                            MrpRole = selectedRole,
                            RingPorts = ringPorts
                        };

                        deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy = new MediaRedundancyConfig
                        {
                            MrpRings = new List<MrpRingConfig> { mrpRing }
                        };

                        Debug.WriteLine($"保存MRP配置到AdvancedConfiguration: Role={selectedRole}, Ports={string.Join(",", ringPorts.Select(p => p.PortNumber))}");
                    }
                    else
                    {
                        Debug.WriteLine("没有有效的Ring Port配置，清除MRP配置");
                        deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy = null;
                    }
                }
                else
                {
                    Debug.WriteLine("没有选择MRP角色，清除MRP配置");
                    deviceConfig.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy = null;
                }

                Debug.WriteLine("=== SaveMRPConfiguration 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存MRP配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 从端口名称中提取纯数字
        /// </summary>
        private string ExtractPortNumber(string portName)
        {
            if (string.IsNullOrEmpty(portName))
                return string.Empty;

            // 使用正则表达式提取数字
            var match = System.Text.RegularExpressions.Regex.Match(portName, @"\d+");
            return match.Success ? match.Value : string.Empty;
        }

        /// <summary>
        /// MRP角色下拉框选择变更事件处理器
        /// </summary>
        private void MediaRedundancyRoleComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== MediaRedundancyRoleComboBox_SelectionChanged 开始 ===");
                SaveMRPConfigurationOnChange();
                Debug.WriteLine("=== MediaRedundancyRoleComboBox_SelectionChanged 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理MRP角色选择变更时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 当MRP配置发生变更时立即保存
        /// </summary>
        private void SaveMRPConfigurationOnChange()
        {
            try
            {
                Debug.WriteLine("=== SaveMRPConfigurationOnChange 开始 ===");

                // 如果还未完成初始化，不触发保存（避免在加载时保存）
                if (!_isInitialized)
                {
                    Debug.WriteLine("设备配置尚未完成初始化，跳过MRP配置保存");
                    return;
                }

                // 标记为已修改
                _isModified = true;

                // 立即保存MRP配置
                if (_projectManager?.CurrentProject != null && !string.IsNullOrEmpty(_deviceName))
                {
                    var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                        .FirstOrDefault(d => d.DeviceRefID == _deviceName);

                    if (deviceConfig != null)
                    {
                        SaveMRPConfiguration(deviceConfig);
                        Debug.WriteLine("MRP配置已保存到内存");

                        // 标记项目已修改并立即保存到磁盘（借鉴NetworkConfigPage的做法）
                        _projectManager.IsProjectModified = true;
                        _projectManager.SaveProject();
                        Debug.WriteLine("MRP配置已保存到磁盘");
                    }
                    else
                    {
                        Debug.WriteLine($"未找到设备配置: {_deviceName}");
                    }
                }
                else
                {
                    Debug.WriteLine("ProjectManager或设备名称为空，无法保存MRP配置");
                }

                Debug.WriteLine("=== SaveMRPConfigurationOnChange 完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"立即保存MRP配置时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// DataGrid行加载事件 - 为内置模块设置只读样式
        /// </summary>
        private void ModulesDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            if (e.Row.DataContext is ModuleViewModel module && module.IsBuiltIn)
            {
                // 为内置模块行设置只读样式
                e.Row.Background = new SolidColorBrush(Color.FromRgb(245, 245, 245)); // 浅灰色背景
                e.Row.Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)); // 深灰色文字
                e.Row.ToolTip = "此为DAP内置模块，不可编辑";
            }
        }

        /// <summary>
        /// DataGrid开始编辑事件 - 阻止内置模块编辑
        /// </summary>
        private void ModulesDataGrid_BeginningEdit(object sender, DataGridBeginningEditEventArgs e)
        {
            if (e.Row.DataContext is ModuleViewModel module && module.IsBuiltIn)
            {
                // 检查是否是地址列
                if (e.Column?.Header?.ToString() == "PNI起始地址" || e.Column?.Header?.ToString() == "PNQ起始地址")
                {
                    // 取消内置模块地址编辑操作
                    e.Cancel = true;

                    // 显示提示信息
                    MessageBox.Show(
                        "DAP内置模块的地址不可编辑。这些模块是设备必需的系统组件。",
                        "操作不允许",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
        }

        /// <summary>
        /// 验证模块输入地址
        /// </summary>
        /// <param name="module">要验证的模块</param>
        /// <param name="address">新地址</param>
        /// <param name="length">地址长度</param>
        /// <returns>验证是否通过</returns>
        public bool ValidateModuleInputAddress(ModuleViewModel module, int address, int length)
        {
            try
            {
                // 获取模块ID
                string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";

                Debug.WriteLine($"验证模块输入地址: 设备 {_deviceName}, 模块 {moduleId}, 地址 {address}, 长度 {length}");

                // 先打印当前地址分配状态
                _globalAddressManager.DebugPrintAllAllocations();

                // 检查地址冲突（排除当前模块）
                bool hasConflict = _globalAddressManager.HasInputAddressConflict(address, length, _deviceName, moduleId);

                Debug.WriteLine($"地址冲突检查结果: {(hasConflict ? "有冲突" : "无冲突")}");

                if (hasConflict)
                {
                    MessageBox.Show(
                        $"输入地址冲突：地址范围 {address}-{address + length - 1} 与其他模块的地址分配冲突。\n" +
                        "请选择其他地址或查看地址分配状态。",
                        "地址冲突",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证输入地址时出错: {ex.Message}");
                MessageBox.Show(
                    $"验证地址时出错: {ex.Message}",
                    "验证错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 验证模块输出地址
        /// </summary>
        /// <param name="module">要验证的模块</param>
        /// <param name="address">新地址</param>
        /// <param name="length">地址长度</param>
        /// <returns>验证是否通过</returns>
        public bool ValidateModuleOutputAddress(ModuleViewModel module, int address, int length)
        {
            try
            {
                // 获取模块ID
                string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";

                // 检查地址冲突（排除当前模块）
                bool hasConflict = _globalAddressManager.HasOutputAddressConflict(address, length, _deviceName, moduleId);

                if (hasConflict)
                {
                    MessageBox.Show(
                        $"输出地址冲突：地址范围 {address}-{address + length - 1} 与其他模块的地址分配冲突。\n" +
                        "请选择其他地址或查看地址分配状态。",
                        "地址冲突",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证输出地址时出错: {ex.Message}");
                MessageBox.Show(
                    $"验证地址时出错: {ex.Message}",
                    "验证错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 处理模块地址更改
        /// </summary>
        /// <param name="module">更改地址的模块</param>
        /// <param name="addressType">地址类型（Input或Output）</param>
        /// <param name="oldValue">旧地址值</param>
        /// <param name="newValue">新地址值</param>
        public void OnModuleAddressChanged(ModuleViewModel module, string addressType, string oldValue, string newValue)
        {
            try
            {
                Debug.WriteLine($"模块地址更改: {module.ModuleName} {addressType} {oldValue} -> {newValue}");

                // 获取模块ID
                string moduleId = !string.IsNullOrEmpty(module.ModuleID) ? module.ModuleID : $"{module.ModuleName}_{module.Position}";

                // 释放旧地址分配
                if (!string.IsNullOrEmpty(oldValue) && oldValue != "-" && int.TryParse(oldValue, out _))
                {
                    _globalAddressManager.ReleaseModuleAddresses(_deviceName, moduleId);
                    Debug.WriteLine($"已释放模块 {moduleId} 的旧地址分配");
                }

                // 分配新地址
                bool allocationSuccess = true;
                if (!string.IsNullOrEmpty(newValue) && newValue != "-" && int.TryParse(newValue, out int newAddress))
                {
                    if (addressType == "Input" && module.InputLength > 0)
                    {
                        allocationSuccess = _globalAddressManager.AllocateSpecificInputAddress(_deviceName, moduleId, newAddress, module.InputLength);
                        if (!allocationSuccess)
                        {
                            Debug.WriteLine($"错误：无法分配指定的输入地址 {newAddress}");
                        }
                    }
                    else if (addressType == "Output" && module.OutputLength > 0)
                    {
                        allocationSuccess = _globalAddressManager.AllocateSpecificOutputAddress(_deviceName, moduleId, newAddress, module.OutputLength);
                        if (!allocationSuccess)
                        {
                            Debug.WriteLine($"错误：无法分配指定的输出地址 {newAddress}");
                        }
                    }

                    if (!allocationSuccess)
                    {
                        Debug.WriteLine($"地址分配失败，模块 {moduleId} 的 {addressType} 地址未能设置为 {newAddress}");
                    }
                }

                // 如果地址分配成功，保存到项目配置
                if (allocationSuccess)
                {
                    SaveModuleAddressToProjectConfig(module, addressType, newValue);
                }

                // 标记为已修改
                _isModified = true;
                _projectManager.IsProjectModified = true;

                Debug.WriteLine($"模块 {moduleId} 的 {addressType} 地址已更新为 {newValue}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理模块地址更改时出错: {ex.Message}");
                MessageBox.Show(
                    $"更新地址分配时出错: {ex.Message}",
                    "更新错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存模块地址到项目配置
        /// </summary>
        /// <param name="module">模块视图模型</param>
        /// <param name="addressType">地址类型（Input或Output）</param>
        /// <param name="addressValue">地址值</param>
        private void SaveModuleAddressToProjectConfig(ModuleViewModel module, string addressType, string addressValue)
        {
            try
            {
                Debug.WriteLine($"保存模块地址到项目配置: {module.ModuleName} {addressType} = {addressValue}");

                // 查找或创建模块配置
                var moduleConfig = FindOrCreateModuleConfig(module);
                if (moduleConfig == null)
                {
                    Debug.WriteLine("无法获取模块配置，跳过保存");
                    return;
                }

                // 查找或创建地址配置
                var addressConfig = FindOrCreateAddressConfiguration(moduleConfig, module);
                if (addressConfig == null)
                {
                    Debug.WriteLine("无法获取地址配置，跳过保存");
                    return;
                }

                // 保存地址值
                if (addressType == "Input")
                {
                    addressConfig.InputStartAddress = addressValue;
                    // 同时更新数值字段
                    if (int.TryParse(addressValue, out int inputAddr))
                    {
                        addressConfig.InputAddress = inputAddr;
                    }
                    Debug.WriteLine($"已保存输入地址到项目配置: {addressValue}");
                }
                else if (addressType == "Output")
                {
                    addressConfig.OutputStartAddress = addressValue;
                    // 同时更新数值字段
                    if (int.TryParse(addressValue, out int outputAddr))
                    {
                        addressConfig.OutputAddress = outputAddr;
                    }
                    Debug.WriteLine($"已保存输出地址到项目配置: {addressValue}");
                }

                Debug.WriteLine($"模块 {moduleConfig.ModuleRefID} 的地址配置已更新");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存模块地址到项目配置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 同步所有模块地址到项目配置
        /// </summary>
        private void SynchronizeAllModuleAddressesToProjectConfig()
        {
            try
            {
                Debug.WriteLine("=== 开始同步所有模块地址到项目配置 ===");

                int syncCount = 0;
                foreach (var module in _modules)
                {
                    // 跳过内置模块和空插槽
                    if (module.IsBuiltIn || string.IsNullOrEmpty(module.ModuleName))
                    {
                        continue;
                    }

                    // 查找或创建模块配置
                    var moduleConfig = FindOrCreateModuleConfig(module);
                    if (moduleConfig == null)
                    {
                        continue;
                    }

                    // 查找或创建地址配置
                    var addressConfig = FindOrCreateAddressConfiguration(moduleConfig, module);
                    if (addressConfig == null)
                    {
                        continue;
                    }

                    // 同步输入地址
                    string inputAddress = !string.IsNullOrEmpty(module.InputStartAddress) ? module.InputStartAddress : "";
                    if (inputAddress != addressConfig.InputStartAddress)
                    {
                        addressConfig.InputStartAddress = inputAddress;
                        if (int.TryParse(inputAddress, out int inputAddr))
                        {
                            addressConfig.InputAddress = inputAddr;
                        }
                        Debug.WriteLine($"同步输入地址: {module.ModuleName} -> {inputAddress}");
                        syncCount++;
                    }

                    // 同步输出地址
                    string outputAddress = !string.IsNullOrEmpty(module.OutputStartAddress) ? module.OutputStartAddress : "";
                    if (outputAddress != addressConfig.OutputStartAddress)
                    {
                        addressConfig.OutputStartAddress = outputAddress;
                        if (int.TryParse(outputAddress, out int outputAddr))
                        {
                            addressConfig.OutputAddress = outputAddr;
                        }
                        Debug.WriteLine($"同步输出地址: {module.ModuleName} -> {outputAddress}");
                        syncCount++;
                    }
                }

                Debug.WriteLine($"=== 地址同步完成，共同步 {syncCount} 个地址 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"同步模块地址到项目配置时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 模块视图模型
    /// </summary>
    public class ModuleViewModel : INotifyPropertyChanged
    {
        private bool _isSelected;
        private DeviceConfigPage? _parentPage;
        private bool _isInitializing = false;

        public int Index { get; set; }

        /// <summary>
        /// 设置父页面引用，用于地址验证
        /// </summary>
        public void SetParentPage(DeviceConfigPage parentPage)
        {
            _parentPage = parentPage;
        }

        /// <summary>
        /// 设置初始化状态，在初始化期间跳过验证
        /// </summary>
        public void SetInitializing(bool isInitializing)
        {
            _isInitializing = isInitializing;
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        private string _position = string.Empty;
        private string _moduleName = string.Empty;
        private string _submoduleName = string.Empty;
        private string _interfaceSlot = string.Empty;
        private string _pniStartAddress = string.Empty;
        private string _inputStartAddress = string.Empty;
        private string _pnqStartAddress = string.Empty;
        private string _outputStartAddress = string.Empty;

        public string Position
        {
            get => _position;
            set
            {
                if (_position != value)
                {
                    _position = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ModuleName
        {
            get => _moduleName;
            set
            {
                if (_moduleName != value)
                {
                    _moduleName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SubmoduleName
        {
            get => _submoduleName;
            set
            {
                if (_submoduleName != value)
                {
                    _submoduleName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string InterfaceSlot
        {
            get => _interfaceSlot;
            set
            {
                if (_interfaceSlot != value)
                {
                    _interfaceSlot = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PNIStartAddress
        {
            get => _pniStartAddress;
            set
            {
                if (_pniStartAddress != value)
                {
                    // 如果正在初始化，直接设置值不进行验证
                    if (_isInitializing)
                    {
                        _pniStartAddress = value;
                        _inputStartAddress = value;
                        OnPropertyChanged();
                        OnPropertyChanged(nameof(InputStartAddress));
                        return;
                    }

                    // 验证新的地址值
                    if (ValidateInputAddress(value))
                    {
                        string oldValue = _pniStartAddress;
                        _pniStartAddress = value;

                        // 同步InputStartAddress
                        _inputStartAddress = value;

                        OnPropertyChanged();
                        OnPropertyChanged(nameof(InputStartAddress));

                        // 通知父页面地址已更改
                        _parentPage?.OnModuleAddressChanged(this, "Input", oldValue, value);
                    }
                    else
                    {
                        // 验证失败，触发PropertyChanged以刷新UI显示原值
                        OnPropertyChanged();
                    }
                }
            }
        }

        public string InputStartAddress
        {
            get => _inputStartAddress;
            set
            {
                if (_inputStartAddress != value)
                {
                    _inputStartAddress = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PNQStartAddress
        {
            get => _pnqStartAddress;
            set
            {
                if (_pnqStartAddress != value)
                {
                    // 如果正在初始化，直接设置值不进行验证
                    if (_isInitializing)
                    {
                        _pnqStartAddress = value;
                        _outputStartAddress = value;
                        OnPropertyChanged();
                        OnPropertyChanged(nameof(OutputStartAddress));
                        return;
                    }

                    // 验证新的地址值
                    if (ValidateOutputAddress(value))
                    {
                        string oldValue = _pnqStartAddress;
                        _pnqStartAddress = value;

                        // 同步OutputStartAddress
                        _outputStartAddress = value;

                        OnPropertyChanged();
                        OnPropertyChanged(nameof(OutputStartAddress));

                        // 通知父页面地址已更改
                        _parentPage?.OnModuleAddressChanged(this, "Output", oldValue, value);
                    }
                    else
                    {
                        // 验证失败，触发PropertyChanged以刷新UI显示原值
                        OnPropertyChanged();
                    }
                }
            }
        }

        public string OutputStartAddress
        {
            get => _outputStartAddress;
            set
            {
                if (_outputStartAddress != value)
                {
                    _outputStartAddress = value;
                    OnPropertyChanged();
                }
            }
        }

        // GSDML相关属性
        public string GSDRefID { get; set; } = string.Empty;
        public string ModuleID { get; set; } = string.Empty;
        public string SubslotInfo { get; set; } = string.Empty; // 新增：存储实际的子插槽信息
        public string SlotSubslot => !string.IsNullOrEmpty(SubslotInfo) ? SubslotInfo : Position; // 优先使用SubslotInfo

        // IO长度信息
        public int InputLength { get; set; } = 0;
        public int OutputLength { get; set; } = 0;
        public string InputLengthDisplay => InputLength > 0 ? InputLength.ToString() : "";
        public string OutputLengthDisplay => OutputLength > 0 ? OutputLength.ToString() : "";

        // 内置模块标识
        public bool IsBuiltIn { get; set; } = false; // 标识是否为DAP内置模块（VirtualSubmoduleList和SystemDefinedSubmoduleList）

        // 其他属性
        public string FirmwareVersion { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;

        /// <summary>
        /// 验证输入地址
        /// </summary>
        /// <param name="addressValue">地址值</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateInputAddress(string addressValue)
        {
            Debug.WriteLine($"验证输入地址: 模块 {ModuleName} (位置 {Position}), 地址值: '{addressValue}', 输入长度: {InputLength}");

            // 如果是内置模块，不允许编辑地址
            if (IsBuiltIn)
            {
                Debug.WriteLine("内置模块不允许编辑地址");
                System.Windows.MessageBox.Show(
                    "DAP内置模块的地址不可编辑。",
                    "操作不允许",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
                return false;
            }

            // 如果父页面未设置，允许设置（初始化阶段）
            if (_parentPage == null)
            {
                Debug.WriteLine("父页面未设置，允许设置（初始化阶段）");
                return true;
            }

            // 空值或"-"表示清空地址，允许
            if (string.IsNullOrEmpty(addressValue) || addressValue == "-")
            {
                Debug.WriteLine("空值或'-'，允许清空地址");
                return true;
            }

            // 验证地址格式（必须是数字）
            if (!int.TryParse(addressValue, out int address))
            {
                Debug.WriteLine($"地址格式无效: '{addressValue}'");
                System.Windows.MessageBox.Show(
                    $"输入地址格式无效：'{addressValue}'。地址必须是数字。",
                    "地址格式错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 验证地址范围
            if (address < 0 || address >= GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE)
            {
                Debug.WriteLine($"起始地址超出范围: {address}");
                System.Windows.MessageBox.Show(
                    $"输入起始地址超出有效范围：{address}。有效范围：0-{GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE - 1}。",
                    "地址范围错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 验证模块占用的完整地址范围（起始地址 + 模块长度）
            if (InputLength > 0)
            {
                int endAddress = address + InputLength - 1;
                if (endAddress >= GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE)
                {
                    Debug.WriteLine($"模块地址范围超出边界: {address}-{endAddress}");
                    System.Windows.MessageBox.Show(
                        $"模块输入地址范围超出边界：{address}-{endAddress}。\n" +
                        $"模块长度：{InputLength} 字节\n" +
                        $"有效地址范围：0-{GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE - 1}\n" +
                        $"建议起始地址不超过：{GlobalAddressManager.INPUT_ADDRESS_POOL_SIZE - InputLength}",
                        "模块地址范围错误",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Warning);
                    return false;
                }
            }

            // 如果模块没有输入长度，不需要分配地址
            if (InputLength <= 0)
            {
                Debug.WriteLine("模块没有输入长度，允许设置");
                return true;
            }

            // 检查地址冲突
            Debug.WriteLine($"检查地址冲突: 地址 {address}, 长度 {InputLength}");
            bool isValid = _parentPage.ValidateModuleInputAddress(this, address, InputLength);
            Debug.WriteLine($"地址冲突检查结果: {(isValid ? "通过" : "失败")}");
            return isValid;
        }

        /// <summary>
        /// 验证输出地址
        /// </summary>
        /// <param name="addressValue">地址值</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateOutputAddress(string addressValue)
        {
            // 如果是内置模块，不允许编辑地址
            if (IsBuiltIn)
            {
                System.Windows.MessageBox.Show(
                    "DAP内置模块的地址不可编辑。",
                    "操作不允许",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
                return false;
            }

            // 如果父页面未设置，允许设置（初始化阶段）
            if (_parentPage == null)
            {
                return true;
            }

            // 空值或"-"表示清空地址，允许
            if (string.IsNullOrEmpty(addressValue) || addressValue == "-")
            {
                return true;
            }

            // 验证地址格式（必须是数字）
            if (!int.TryParse(addressValue, out int address))
            {
                System.Windows.MessageBox.Show(
                    $"输出地址格式无效：'{addressValue}'。地址必须是数字。",
                    "地址格式错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 验证地址范围
            if (address < 0 || address >= GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE)
            {
                Debug.WriteLine($"起始地址超出范围: {address}");
                System.Windows.MessageBox.Show(
                    $"输出起始地址超出有效范围：{address}。有效范围：0-{GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE - 1}。",
                    "地址范围错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 如果模块没有输出长度，不需要分配地址
            if (OutputLength <= 0)
            {
                Debug.WriteLine("模块没有输出长度，允许设置");
                return true;
            }

            // 验证模块占用的完整地址范围（起始地址 + 模块长度）
            int endAddress = address + OutputLength - 1;
            if (endAddress >= GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE)
            {
                Debug.WriteLine($"模块地址范围超出边界: {address}-{endAddress}");
                System.Windows.MessageBox.Show(
                    $"模块输出地址范围超出边界：{address}-{endAddress}。\n" +
                    $"模块长度：{OutputLength} 字节\n" +
                    $"有效地址范围：0-{GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE - 1}\n" +
                    $"建议起始地址不超过：{GlobalAddressManager.OUTPUT_ADDRESS_POOL_SIZE - OutputLength}",
                    "模块地址范围错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 检查地址冲突
            return _parentPage.ValidateModuleOutputAddress(this, address, OutputLength);
        }
    }

    /// <summary>
    /// GSDML模块信息
    /// </summary>
    public class GSDMLModuleInfo
    {
        public string GSDRefID { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string ModuleKey { get; set; } = string.Empty;
        public int DefaultSlot { get; set; } = 1;
        public int InputLength { get; set; } = 0;
        public int OutputLength { get; set; } = 0;
        public int TotalDataLength { get; set; } = 0;
    }

    /// <summary>
    /// DAP信息结构
    /// </summary>
    public class DAPInfo
    {
        public string? Category { get; set; }
        public string? OrderNumber { get; set; }
        public int InputLength { get; set; } = 0;
        public int OutputLength { get; set; } = 0;
        public string? Info { get; set; }
    }
}