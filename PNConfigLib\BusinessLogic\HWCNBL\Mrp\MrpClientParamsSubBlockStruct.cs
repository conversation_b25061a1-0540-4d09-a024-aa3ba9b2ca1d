/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MrpClientParamsSubBlockStruct.cs          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Mrp
{
    /// <summary>
    /// The data record object for MrpClientParamsSubBlock.
    /// </summary>
    internal class MrpClientParamsSubBlockStruct : ParameterDSSubBlockStruct
    {
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        /// <summary>
        /// Contents of the data record.
        /// </summary>
        private byte[] m_Data = new byte[6];

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// Initializes the data record.
        /// </summary>
        public MrpClientParamsSubBlockStruct()
        {
            ParaBlockType = 0x0217;
            ParaBlockVersion = 0x0100;
            AddSubblock(m_Data);
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Nested classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Constants and enumerations

        // Contains all constants and enumerations

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// MrpLNKdownT part of the data record.
        /// </summary>
        public int MrpLNKdownT
        {
            set { BufferManager.Write16(m_Data, 0, value); }
            get { return BufferManager.Read16(m_Data, 0); }
        }

        /// <summary>
        /// MrpLNKupT part of the data record.
        /// </summary>
        public int MrpLNKupT
        {
            set { BufferManager.Write16(m_Data, 2, value); }
            get { return BufferManager.Read16(m_Data, 2); }
        }

        /// <summary>
        /// MrpLNKNRmax part of the data record.
        /// </summary>
        public int MrpLNKNRmax
        {
            set { BufferManager.Write16(m_Data, 4, value); }
            get { return BufferManager.Read16(m_Data, 4); }
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Delegates and events

        // Contains all delegate and events

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region I... members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Overrides and overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Protected methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Private implementation

        // Contains the private implementation of the class

        #endregion
    }
}