/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NavigationUtilities.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class NavigationUtilities
    {
        /// <summary>
        /// get the object according to relation 'Container'
        /// </summary>
        /// <param name="pclObject"></param>
        /// <returns>rack, module, etc.</returns>
        public static PclObject GetContainer(PclObject pclObject)
        {
            if (pclObject == null)
            {
                throw new ArgumentNullException(nameof(pclObject));
            }
            return pclObject.ParentObject;
        }

        /// <summary>
        /// Gets the ControllerInterfaceSubmodule connected to a Device's IOSystem.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">InterfaceSubmodule of the device.</param>
        /// <returns>
        /// The ControllerInterfaceSubmodule connected to the handled-over
        /// DeviceInterfaceSubmodule's IOSystem
        /// </returns>
        public static Interface GetControllerOfDevice(Interface deviceInterfaceSubmodule)
        {
            //return deviceInterfaceSubmodule.PNIOD.AssignedController.ParentObject as Interface;
            Interface returnValue = null;

            if ((deviceInterfaceSubmodule != null)
                && (deviceInterfaceSubmodule.PNIOD != null)
                && (deviceInterfaceSubmodule.PNIOD.AssignedController != null)
                && (deviceInterfaceSubmodule.PNIOD.AssignedController.ParentObject != null))
            {
                returnValue = deviceInterfaceSubmodule.PNIOD.AssignedController.ParentObject as Interface;
            }
            return returnValue;
        }

        public static PclObject GetCpu(PclObject module)
        {
            PclObject cpu = module.GetDevice() as CentralDevice;
            return cpu;
        }

        /// <summary>
        /// Gets the DeviceInterfaceSubmodules connected to a Controller's IOSystem.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The ControllerInterfaceSubmodule
        /// whose IOSystem is investigated for connected devices.
        /// </param>
        /// <returns>
        /// DeviceInterfaceSubmodules connected to the handled-over
        /// ControllerInterfaceSubmodule's IOSystem if there're any (default is an empty list)
        /// </returns>
        public static List<Interface> GetDevicesOfController(Interface controllerInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(controllerInterfaceSubmodule));
            }

            if (controllerInterfaceSubmodule.PNIOC == null)
            {
                throw new PNFunctionsException("Interface does not have an IO controller.");
            }

            List<Interface> devicesOfController = new List<Interface>();

            if (controllerInterfaceSubmodule.PNIOC.IOSystem != null)
            {
                foreach (PNIOD ioDevice in controllerInterfaceSubmodule.PNIOC.IOSystem.GetParticipants())
                {
                    devicesOfController.Add(ioDevice.ParentObject as Interface);
                }
            }

            return devicesOfController;
        }

        /// <summary>
        /// Returns the head module of the given device item
        /// </summary>
        /// <param name="interfaceSubmodule">Device item object</param>
        /// <returns>Headmodule</returns>
        public static DecentralDevice GetHeadmodule(Interface interfaceSubmodule)
        {
            return interfaceSubmodule.GetDevice() as DecentralDevice;
        }

        public static Interface GetInterfaceFromConnector(PclObject ioConnector)
        {
            if (ioConnector == null)
            {
                throw new ArgumentNullException(nameof(ioConnector));
            }

            if (!(ioConnector is PNIOC)
                && !(ioConnector is PNIOD))
            {
                throw new ArgumentException("ioConnector is not a PNIOC or PNIOD.");
            }

            return ioConnector.ParentObject as Interface;
        }

        /// <summary>
        /// Gets the InterfaceSubmodule of the given Port object.
        /// </summary>
        /// <param name="port"></param>
        /// <returns></returns>
        public static Interface GetInterfaceOfPort(DataModel.PCLObjects.Port port)
        {
            if (port == null)
            {
                throw new ArgumentNullException(nameof(port));
            }
            return port.GetInterface();
        }

        /// <summary>
        /// Gets the list of all Interface Submodules that are connected to the given subnet.
        /// </summary>
        /// <param name="subnet">The source subnet</param>
        /// <returns>The list of the Interface Submodules</returns>
        public static List<Interface> GetInterfacesOfSubnet(Subnet subnet)
        {
            List<Interface> interfaceList = new List<Interface>();

            foreach (DataModel.PCLObjects.Node node in subnet.Nodes)
            {
                if (node.ParentObject is Interface)
                {
                    interfaceList.Add(node.ParentObject as Interface);
                }
                else
                {
                    Debug.Fail("There shouldn't be a node without parent interface.");
                }
            }

            return interfaceList;
        }

        public static PclObject GetIOConnector(Interface intInterfaceSubmodule)
        {
            return intInterfaceSubmodule.GetIOConnector();
        }

        /// <summary>
        /// Get IODevice and/or IOController IO-System connectors of an interfaceSubmodule
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <returns></returns>
        public static List<PclObject> GetIOConnectors(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }

            List<PclObject> result = new List<PclObject>();
            PNIOC pnioc = interfaceSubmodule.PNIOC;
            PNIOD pniod = interfaceSubmodule.PNIOD;
            if (pnioc != null)
            {
                result.Add(pnioc);
            }
            if (pniod != null)
            {
                result.Add(pniod);
            }
            return result;
        }

        /// <summary>
        /// Get the IODevice IO-System connector from the Interface Submodule, if its IsSlaveType is true.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule of which IODevice we are looking for</param>
        /// <param name="ioSystemOfIOController">
        /// allows us to get the right IODevice in case of Multi AR (integrated shared device,
        /// S2, R2 Devices)
        /// </param>
        /// <returns>The IoDevice (default value is null)</returns>
        public static PNIOD GetIODevice(Interface interfaceSubmodule, DataModel.PCLObjects.IOSystem ioSystemOfIOController)
        {
            PNIOD ioDevice = GetIOConnector(interfaceSubmodule) as PNIOD;

            if (ioDevice == null)
            {
                return null;
            }

            DataModel.PCLObjects.IOSystem ioSystemObject = ioSystemOfIOController;
            if (ioSystemOfIOController != null)
            {
                if (ioDevice.AssignedController == ioSystemObject.PNIOC)
                {
                    return ioDevice;
                }
            }
            else
            {
                return ioDevice;
            }

            return null;
        }

        public static PclObject GetOwnerOfInterface(Interface interfacesm)
        {
            return GetContainer(interfacesm);
        }

        /// <summary>
        ///  Returns the IPNFrameData objects which are aggregated at the IODevice
        ///  object of the given device interface submodule.
        /// </summary>
        /// <param name="ioDeviceObject">ioDeviceObject of a given interface submodule(one AR to a controller)</param>
        /// <param name="includeSyncFrame">If false, syncframe will not be included into the list</param>
        /// <param name="controllerInterfaceSubmodule">
        /// controller interface reference can be added as input in bulk operations to
        /// avoid redundant navigation
        /// </param>
        /// <returns>the frames of the AR</returns>
        public static List<IPNFrameData> GetPNFrameDataListOfIODevice(
            PNIOD ioDeviceObject,
            bool includeSyncFrame,
            Interface controllerInterfaceSubmodule = null)
        {
            if (ioDeviceObject == null)
            {
                throw new ArgumentNullException(nameof(ioDeviceObject));
            }

            if (controllerInterfaceSubmodule == null)
            {
                controllerInterfaceSubmodule = ioDeviceObject.AssignedController.ParentObject as Interface;

                if (controllerInterfaceSubmodule == null)
                {
                    return null;
                }
            }

            IList<IPNFrameData> frameDataList = controllerInterfaceSubmodule.SyncDomain.SyncDomainBusinessLogic
                .GetFramesOfInterface(controllerInterfaceSubmodule);

            List<IPNFrameData> tmpFrameList = new List<IPNFrameData>();
            if (frameDataList != null)
            {
                for (int i = 0; i < frameDataList.Count; i++)
                {
                    if (frameDataList[i].CoreIds.Contains(ioDeviceObject.GetHashCode()))
                    {
                        tmpFrameList.Add(frameDataList[i]);
                    }
                }
            }

            if (frameDataList != null && !includeSyncFrame)
            {
                for (int i = 0; i < frameDataList.Count; i++)
                {
                    if (frameDataList[i].FrameType == (long)PNPlannerFrameType.SyncFrame)
                    {
                        tmpFrameList.RemoveAt(i--);
                    }
                }
            }
            return tmpFrameList;
        }

        /// <summary>
        /// Get all PNFrameData objects which are related to the 
        /// IOController of the given controllerInterfaceSubmodule
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <returns></returns>
        public static List<IPNFrameData> GetAllPNFrameDataOfController(
            Interface controllerInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule == null)
                throw new ArgumentNullException((nameof(controllerInterfaceSubmodule)));

            List<IPNFrameData> frameDataList =
               (List<IPNFrameData>)controllerInterfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.GetFramesOfInterface(controllerInterfaceSubmodule);

            return frameDataList;
        }
        /// <summary>
        /// Gets an ethernet submodule's port module(s).
        /// </summary>
        /// <param name="ethernetSubmodule"></param>
        /// <returns>ConfigObjectCollection which contains all ports.</returns>
        /// <remarks>
        /// The method returns physical as well as other logical ports of the interface.
        /// Logical ports are for example the ports of a pluggable modules (scalance).
        /// These port objects are not bound to a physical interface.
        /// Instead they are logical ports of the plugged interface.
        /// </remarks>
        public static List<DataModel.PCLObjects.Port> GetPortModules(Interface ethernetSubmodule)
        {
            if (ethernetSubmodule == null)
            {
                throw new ArgumentNullException(nameof(ethernetSubmodule));
            }
            List<DataModel.PCLObjects.Port> retval = new List<DataModel.PCLObjects.Port>();
            retval.AddRange(ethernetSubmodule.GetPorts()
                .OrderBy(p => p.AttributeAccess.GetAnyAttribute<int>(
                             InternalAttributeNames.PnPortNumber,
                             new AttributeAccessCode(),
                             0)));
            PclObject device = ethernetSubmodule.GetDevice();
            if (device != null && device is DecentralDevice)
            {
                IEnumerable<DataModel.PCLObjects.Port> ports = ((DecentralDevice)device).GetModules()
                    ?.OrderBy(
                        m => m.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.PositionNumber,
                            new AttributeAccessCode(),
                            0))
                    .SelectMany(m => m.GetPorts()?
                                        .OrderBy(p => p.AttributeAccess.GetAnyAttribute<int>(
                                                      InternalAttributeNames.PnPortNumber,
                                                      new AttributeAccessCode(),
                                                      0)));
                if (ports != null)
                {
                    retval.AddRange(ports);
                }
            }
            return retval;
        }

        public static IEnumerable<DataModel.PCLObjects.Port> SortPortObjects(List<DataModel.PCLObjects.Port> ports)
        {
            SortedDictionary<int, DataModel.PCLObjects.Port> dict =
                new SortedDictionary<int, DataModel.PCLObjects.Port>();
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                PclObject portContainer = GetContainer(port);
                int posContainer = AttributeUtilities.GetPositionNumber(portContainer);
                int posPort = AttributeUtilities.GetPositionNumber(port);
                int posCommon = (posContainer << 16) + posPort;
                if (dict.ContainsKey(posCommon))
                {
                    posCommon = (posContainer << 16) + posPort;
                    int counter = posCommon + 1;
                    while (dict.ContainsKey(counter))
                    {
                        counter++;
                    }
                    dict.Add(counter, port);
                }
                else
                {
                    dict.Add(posCommon, port);
                }
            }
            foreach (DataModel.PCLObjects.Port port in dict.Values)
            {
                yield return port;
            }
        }

        internal static List<Interface> GetConnectedInterfaces(Interface interfaceSubmodule)
        {
            List<Interface> connectedInterfaces = new List<Interface>();

            foreach (DataModel.PCLObjects.Port port in GetPortModules(interfaceSubmodule))
            {
                foreach (DataModel.PCLObjects.Port connectedPort in port.GetPartnerPorts())
                {
                    Interface connectedInterfaceSubmodule = GetInterfaceOfPort(connectedPort);
                    if (connectedInterfaces.Contains(connectedInterfaceSubmodule))
                    {
                        continue;
                    }
                    connectedInterfaces.Add(connectedInterfaceSubmodule);
                }
            }

            return connectedInterfaces;
        }

        internal static Interface GetControllerInterfaceOfDeviceInterface(Interface interfaceSubmodule)
        {
            PNIOD iod = interfaceSubmodule.PNIOD;
            DataModel.PCLObjects.IOSystem ioSystem = iod.IOSystem;
            PNIOC ioc = ioSystem.PNIOC;
            return ioc.ParentObject as Interface;
        }

        internal static PclObject GetDpMasterSubmodule(PclObject headmodule)
        {
            return null;
        }

        internal static List<PNIOD> GetIoDevicesOfIoController(PNIOC ioController)
        {
            return ioController.IOSystem.GetParticipants().ToList();
        }

        internal static DataModel.PCLObjects.IOSystem GetIoSystem(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule.PNIOC != null)
            {
                return interfaceSubmodule.PNIOC.IOSystem;
            }

            if (interfaceSubmodule.PNIOD != null)
            {
                return interfaceSubmodule.PNIOD.IOSystem;
            }

            return null;
        }

        internal static PclObject GetOwnerOfInterface(PclObject interfaceSubmodule)
        {
            return interfaceSubmodule.ParentObject;
        }

        /// <summary>
        /// This method returns the partner ports of a port
        /// </summary>
        /// <param name="port">The port of which partners we are looking for</param>
        /// <returns>A list contaning all the partner ports of the port (default is an empty list)</returns>
        internal static List<DataModel.PCLObjects.Port> GetPartnerPorts(DataModel.PCLObjects.Port port)
        {
            List<DataModel.PCLObjects.Port> result = new List<DataModel.PCLObjects.Port>();
            if (port != null)
            {
                result = (List<DataModel.PCLObjects.Port>)port.GetPartnerPorts();
            }

            return result;
        }

        internal static List<PclObject> GetSlavesOfDpMaster(PclObject dpMasterSubmodule)
        {
            return null;
        }

        internal static MrpDomain GetMediaRedundancyDomainOfInterface(Interface ioControllerInterface)
        {

            if (ioControllerInterface == null)
            {
                throw new ArgumentNullException(nameof(ioControllerInterface));
            }
            Domain domain = GetDomainOfInterface(ioControllerInterface, PNDomainType.MediaRedDomain);
            return domain as MrpDomain;
        }

        private static Domain GetDomainOfInterface(Interface interfaceSubmodule, PNDomainType domainType)
        {
            DataModel.PCLObjects.Node node = interfaceSubmodule.Node;
            List<Domain> domainList = node.Subnet.DomainList;
            foreach (var domain in domainList)
            {
                if (domain.GetType() == typeof(SyncDomain) && domainType == PNDomainType.SyncDomain)
                {
                    return domain as SyncDomain;
                }
                if (domainType == PNDomainType.MediaRedDomain && domain.GetType() == typeof(MrpDomain))
                {
                    return domain as MrpDomain;
                }

            }
            return null;
        }

        internal static DataModel.PCLObjects.Port GetPort(Interface interfaceSubmodule, uint portNumber, bool isSlotNumberSpecified, uint slotNumber)
        {
            DataModel.PCLObjects.Port retval = null;
            int positionNumber = interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                new AttributeAccessCode(),
                0);
            if (!isSlotNumberSpecified || slotNumber == positionNumber)
            {
                retval = interfaceSubmodule.GetPortByPortNumber(portNumber);
            }
            else
            {
                PclObject device = interfaceSubmodule.GetDevice();
                if (device != null
                    && device is DecentralDevice)
                {
                    Module module = ((DecentralDevice)device).GetModules().First(
                        m => m.AttributeAccess.GetAnyAttribute<int>(
                                 InternalAttributeNames.PositionNumber,
                                 new AttributeAccessCode(),
                                 0) == (int)slotNumber);
                    if (module != null)
                    {
                        List<DataModel.PCLObjects.Port> ports = module.GetPorts();
                        if (ports != null)
                        {
                            retval = ports.FirstOrDefault(p => p.PortNumber == portNumber);
                        }
                    }
                }
            }
            return retval;
        }
    }
}