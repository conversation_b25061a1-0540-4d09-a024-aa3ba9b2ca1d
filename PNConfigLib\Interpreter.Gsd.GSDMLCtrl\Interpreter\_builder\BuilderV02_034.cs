/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_034.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml.XPath;
using System.Collections;
using System.Globalization;

using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02034 :
        BuilderV02033
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02034()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version234);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            try
            {
                // Fill object with data.
                return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
            }
            catch (PreparationException)
            {
            }

            return null;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldNumberOfSubmodules, null);

            // Attribute NumberOfSubmodules
            string attr = nav.GetAttribute(Attributes.s_NumberOfSubmodules, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfSubmodules] = value;
            }

            // Attribute AdaptsRealIdentification
            attr = nav.GetAttribute(Attributes.s_AdaptsRealIdentification, String.Empty);
            hash[Models.s_FieldIsAdaptsRealIdentification] = Help.GetBool(attr, Attributes.s_DefaultAdaptsRealIdentification);

            // Navigate to Element AssetManagement. Optional.

            // Check whether AssetManagement is available.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_AssetManagement, Namespaces.s_GsdmlDeviceProfile);
            hash[Models.s_FieldIsAssetManagement] = false;
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsAssetManagement] = true;
            }
        }

        protected override void PrepareIOConfigData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOConfigData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApplicationLengthIncludesIoxS, null);

            // Call base class method first.
            base.PrepareIOConfigData(nav, ref hash);

            // Attribute ApplicationLengthIncludesIOxS
            string attr = nav.GetAttribute(Attributes.s_ApplicationLengthIncludesIoxS, String.Empty);
            hash[Models.s_FieldApplicationLengthIncludesIoxS] = Help.GetBool(attr, Attributes.s_DefaultApplicationLengthIncludesIoxS);
        }

        #endregion

        #endregion

    }
}
