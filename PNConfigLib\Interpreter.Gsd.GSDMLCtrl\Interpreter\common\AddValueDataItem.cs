/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: AddValueDataItem.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces

#endregion

using System.Collections;

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The DataItem element contains information about a single DataItem.
    /// It could be used to specify the characteristic of input or output
    /// data of a Submodule.
    /// </summary>
    public class AddValueDataItem :
        GsdObject,
        GSDI.IAddValueDataItem
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the DataItem if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public AddValueDataItem()
        {
            m_Id = 0;
            m_DataType = GSDI.DataItemTypes.GSDDtUnsigned8;
            m_DataLength = 0;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private uint m_Id;
        private GSDI.DataItemTypes m_DataType;
        private string m_DataTypeAsString;
        private uint m_DataLength;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the id of the data item.
        /// </summary>
        public uint Id => this.m_Id;

        /// <summary>
        /// Accesses the data type of the data item.
        /// </summary>
        public GSDI.DataItemTypes DataType => this.m_DataType;

        /// <summary>
        /// Accesses the data type of the data item as string.
        /// </summary>
        public string DataTypeAsString => m_DataTypeAsString;

        /// <summary>
        /// Accesses the length of the octets, specified with this data item.
        /// </summary>
        public System.UInt32 DataLength => this.m_DataLength;

#if !S7PLUS
        #region COM Interface Members Only

        #endregion
#endif

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldId;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_Id = (uint)hash[member];

                member = Models.s_FieldDataType;
                if (hash.ContainsKey(member) && hash[member] is GSDI.DataItemTypes)
                    this.m_DataType = (GSDI.DataItemTypes)hash[member];

                member = Models.s_FieldDataTypeAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_DataTypeAsString = (string)hash[member];

                member = Models.s_FieldDataLength;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_DataLength = (uint)hash[member];
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectDataItem);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldId, this.m_Id);
            Export.WriteEnumProperty(ref writer, Models.s_FieldDataType, this.m_DataType.ToString(), Export.s_SubtypeDataItemTypes);
            Export.WriteUint32Property(ref writer, Models.s_FieldDataLength, this.m_DataLength);

            return true;
        }


        #endregion
    }
}
