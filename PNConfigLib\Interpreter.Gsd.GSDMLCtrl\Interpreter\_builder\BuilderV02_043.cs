/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_043.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml;
using System.Xml.XPath;
using System.Collections;
using GSDI;
using C = PNConfigLib.Gsd.Interpreter.Common;
using S = PNConfigLib.Gsd.Interpreter.Structure;
using System.Globalization;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02043 :
        BuilderV02042
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02043()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version243);
        }

        #endregion


        //########################################################################################
        #region Methods

        /// <summary>
        /// New element ApplicationProcess/RecordDataList (8.9.4)
        /// </summary>
        protected override bool InitExpressions()
        {
            bool succeeded = true;

            try
            {
                succeeded = base.InitExpressions();
                if (!succeeded)
                {
                    return false;
                }

                // Create document navigator.
                XPathNavigator nav = Gsd.CreateNavigator();

                if (nav == null)
                {
                    return false;
                }

                // Create the NamespaceManager and add all XML Namespaces to it.
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                // InterfaceSubmoduleItem
                XPathExpression expr = nav.Compile(XPathes.AllInterfaceSubmoduleItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_InterfaceSubmoduleItem, expr);

                // PortSubmoduleItem
                expr = nav.Compile(XPathes.AllInterfacePortSubmoduleItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add("InterfacePortSubmoduleItem", expr);

                // ParameterRecordDataItem
                expr = nav.Compile(XPathes.AllRecordDataItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_ParameterRecordDataItem, expr);

                // CommunicationInterfaceItem
                expr = nav.Compile(XPathes.AllCommunicationInterfaces);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_CommunicationInterfaceItem, expr);
            }
            catch (XPathException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    // ------------- STRUCTURE ELEMENTS --------------------------
                    case Models.s_ObjectRecordDataStructureElement:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            this.PrepareRecordDataStructureElement(nav, ref hash);
                            obj = new S.RecordDataStructureElement();

                            break;
                        }
                    case Models.s_ObjectCommunicationInterfaceStructureElement:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            this.PrepareCommunicationInterfaceStructureElement(nav, ref hash);
                            obj = new S.CommunicationInterfaceStructureElement();

                            break;
                        }

                    // ------------- COMMON ELEMENTS --------------------------CommunicationInterfaceItems
                    case Models.s_ObjectAPLPortClassification:
                        {
                            this.PrepareAPLPortClassification(nav, ref hash);
                            obj = new C.AplPortClassification();

                            break;
                        }
                    case Models.s_ObjectNetloadClasses:
                        {
                            this.PrepareNetloadClasses(nav, ref hash);
                            obj = new C.NetloadClasses();

                            break;
                        }
                    case Models.s_ObjectProfileProcessAutomation:
                        {
                            this.PrepareProfileProcessAutomation(nav, ref hash);
                            obj = new C.ProfileProcessAutomation();

                            break;
                        }
                    case Models.s_ObjectCommunicationInterface:
                        {
                            this.PrepareCommunicationInterface(nav, ref hash);
                            obj = new C.CommunicationInterface();

                            break;
                        }
                    case Models.s_ObjectCIMInterface:
                        {
                            this.PrepareCIMInterface(nav, ref hash);
                            obj = new C.CimInterface();

                            break;
                        }
                    case Models.s_ObjectCIMSupportedRecords:
                        {
                            this.PrepareCIMSupportedRecords(nav, ref hash);
                            obj = new C.CimSupportedRecords();

                            break;
                        }
                    case Models.s_ObjectCIMResources:
                        {
                            this.PrepareCIMResources(nav, ref hash);
                            obj = new C.CimResources();

                            break;
                        }
                    case Models.s_ObjectCIMSupportedFeatures:
                        {
                            this.PrepareCIMSupportedFeatures(nav, ref hash);
                            obj = new C.CimSupportedFeatures();

                            break;
                        }
                    case Models.s_ObjectCIMProtection:
                        {
                            this.PrepareCIMProtection(nav, ref hash);
                            obj = new C.CimProtection();

                            break;
                        }
                  
                    case Models.s_ObjectKeyDerivation:
                        {
                            this.PrepareKeyDerivation(nav, ref hash);
                            obj = new C.KeyDerivation();

                            break;
                        }
                    case Models.s_ObjectKeyAgreement:
                        {
                            this.PrepareKeyAgreement(nav, ref hash);
                            obj = new C.KeyAgreement();

                            break;
                        }
                    case Models.s_ObjectDigitalSignature:
                        {
                            this.PrepareDigitalSignature(nav, ref hash);
                            obj = new C.DigitalSignature();

                            break;
                        }
                    case Models.s_ObjectStreamProtection:
                        {
                            this.PrepareStreamProtection(nav, ref hash);
                            obj = new C.StreamProtection();

                            break;
                        }
                    case Models.s_ObjectAlarmProtection:
                        {
                            this.PrepareAlarmProtection(nav, ref hash);
                            obj = new C.AlarmProtection();

                            break;
                        }
                    case Models.s_ObjectConnectionManagementProtection:
                        {
                            this.PrepareConnectionManagementProtection(nav, ref hash);
                            obj = new C.ConnectionManagementProtection();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion


        //########################################################################################
        #region Structure Model Methods

        protected override void CreateSubmoduleStructureElements()
        {
            base.CreateSubmoduleStructureElements();

            // Select all VirtualSubmodules
            XPathNavigator nav = Gsd.CreateNavigator();

            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_VirtualSubmoduleItem]);

            // Check whether Submodules are available.
            if (nodes.Count > 0)
            {
                // Prepare data for each Submodule, create it and add it to store.
                while (nodes.MoveNext())
                {
                    // Create object.
                    string name = Models.s_ObjectSubmoduleStructureElement;
                    S.SubmoduleStructureElement obj = (S.SubmoduleStructureElement)CreateGsdObject(name, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectSubmoduleStructureElement + "' couldn't be created!");

                    // Add object to store.
                    SStore.VirtualSubmodules.Add(obj.GsdID, obj);
                }
            }

            // Select all InterfaceSubmoduleItems
            nodes = nav.Select((XPathExpression)Expressions[Elements.s_InterfaceSubmoduleItem]);

            // Check whether Submodules are available.
            if (nodes.Count > 0)
            {
                // Prepare data for each Submodule, create it and add it to store.
                while (nodes.MoveNext())
                {
                    // Create object.
                    string name = Models.s_ObjectSubmoduleStructureElement;
                    S.SubmoduleStructureElement obj = (S.SubmoduleStructureElement)CreateGsdObject(name, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectSubmoduleStructureElement + "' couldn't be created!");

                    // Add object to store.
                    SStore.VirtualSubmodules.Add(obj.GsdID, obj);
                }
            }

            // Select all InterfacePortSubmoduleItems
            nodes = nav.Select((XPathExpression)Expressions["InterfacePortSubmoduleItem"]);

            // Check whether Submodules are available.
            if (nodes.Count > 0)
            {
                // Prepare data for each Submodule, create it and add it to store.
                while (nodes.MoveNext())
                {
                    // Create object.
                    string name = Models.s_ObjectSubmoduleStructureElement;
                    S.SubmoduleStructureElement obj = (S.SubmoduleStructureElement)CreateGsdObject(name, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectSubmoduleStructureElement + "' couldn't be created!");

                    // Add object to store.
                    SStore.VirtualSubmodules.Add(obj.GsdID, obj);
                }
            }
        }

        protected override void PrepareSubmoduleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRecordDataItems, null);

            // Prepare Submodule data with all sub components.

            // Call base class method first.
            base.PrepareSubmoduleStructureElement(nav, ref hash);

            XPathNodeIterator nodes = nav.SelectDescendants(
                Elements.s_ParameterRecordDataRef,
                Namespaces.s_GsdmlDeviceProfile,
                false);

            // Check whether submodules are available.
            if (nodes.Count <= 0)
            {
                return;
            }

            // Add parameter record data list to hash.
            ArrayList list = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current != null && this.SStore.RecordDataItems.ContainsKey(
                        nodes.Current.GetAttribute(Attributes.s_RecordDataTarget, String.Empty)))
                {
                    list.Add(
                        this.SStore.RecordDataItems[nodes.Current.GetAttribute(
                            Attributes.s_RecordDataTarget,
                            String.Empty)]);
                }
            }

            hash[Models.s_FieldRecordDataItems] = list;

        }

        protected override void PrepareAccessPointStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldCommunicationInterfaces, null);

            // Call base class method first.
            base.PrepareAccessPointStructureElement(nav, ref hash);

            XPathNodeIterator nodes = nav.SelectDescendants(
                Elements.s_CommunicationInterfaceModule,
                Namespaces.s_GsdmlDeviceProfile,
                false);

            // Check whether submodules are available.
            if (nodes.Count <= 0)
            {
                return;
            }

            // Add parameter record data list to hash.
            ArrayList list = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current != null && this.SStore.CommunicationInterfaceItems.ContainsKey(
                        Help.CollapseWhitespace(nodes.Current.GetAttribute(Attributes.s_CimTarget, String.Empty))))
                {
                    list.Add(
                        this.SStore.CommunicationInterfaceItems[Help.CollapseWhitespace(nodes.Current.GetAttribute(
                            Attributes.s_CimTarget,
                            String.Empty))]);
                }
            }

            hash[Models.s_FieldCommunicationInterfaces] = list;

        }

        protected override void CreateRecordDataStructureElements()
        {
            // Select all RecordDataItems
            XPathNavigator nav = this.Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_ParameterRecordDataItem]);

            // Check whether RecordDataItems are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each RecordDataItem, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectRecordDataStructureElement;
                S.RecordDataStructureElement obj = (S.RecordDataStructureElement)this.CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectRecordDataStructureElement + "' couldn't be created!");

                // Add object to store.
                this.SStore.RecordDataItems.Add(obj.RecordDataID, obj);
            }

        }

        protected virtual void PrepareRecordDataStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRecordDataId, null);

            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldRecordDataId] = attr;
        }

        protected override void CreateCommunicationInterfaceStructureElements()
        {
            // Select all CommunicationInterfaceItems
            XPathNavigator nav = this.Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_CommunicationInterfaceItem]);

            // Check whether CommunicationInterfaceItems are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each CommunicationInterfaceItem, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectCommunicationInterfaceStructureElement;
                S.CommunicationInterfaceStructureElement obj = (S.CommunicationInterfaceStructureElement)this.CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCommunicationInterfaceStructureElement + "' couldn't be created!");

                // Add object to store.
                this.SStore.CommunicationInterfaceItems.Add(obj.CIM_ID, obj);
            }
        }

        protected virtual void PrepareCommunicationInterfaceStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldCimId, null);
            hash.Add(Models.s_FieldVendorIdentNumber, null);
            hash.Add(Models.s_FieldDeviceIdentNumber, null);
            hash.Add(Models.s_FieldInstance, null);

            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldCimId] = attr;

            // Get data of CIM interface element.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_CimInterface, Namespaces.s_GsdmlDeviceProfile);

            // CIM_Interface is required.
            if (!nodes.MoveNext())
            {
                return;
            }

            XPathNavigator tempnav = nodes.Current;
            // -------------------------
            // Get VendorID. Required.
            if (tempnav != null)
            {
                attr = tempnav.GetAttribute(Attributes.s_VendorId, String.Empty);
                hash[Models.s_FieldVendorIdentNumber] = UInt32.Parse(
                    attr.Substring(2),
                    System.Globalization.NumberStyles.AllowHexSpecifier,
                    CultureInfo.InvariantCulture);

                // -------------------------
                // Get DeviceID. Required.
                attr = tempnav.GetAttribute(Attributes.s_DeviceId, String.Empty);
                hash[Models.s_FieldDeviceIdentNumber] = UInt32.Parse(
                    attr.Substring(2),
                    System.Globalization.NumberStyles.AllowHexSpecifier,
                    CultureInfo.InvariantCulture);

                // -------------------------
                // Get Instance. Required.
                attr = tempnav.GetAttribute(Attributes.s_Instance, String.Empty);
                
            }

            hash[Models.s_FieldInstance] = UInt32.Parse(
                attr.Substring(2),
                System.Globalization.NumberStyles.AllowHexSpecifier,
                CultureInfo.InvariantCulture);
        }

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare VirtualSubmodule data with all sub components.

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            // Navigate to RecordDataList.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_RecordDataList, Namespaces.s_GsdmlDeviceProfile);

            if (!nodes.MoveNext())
                return;

            nav = nodes.Current;

            // Navigate to all ParameterRecordData and create it. Optional.
            ArrayList list = null;
            if (nav != null)
            {
                nodes = nav.SelectDescendants(XPathNodeType.Element, false);
            }

            // Create each found data record.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                if (nodes.Current != null && nodes.Current.LocalName == Elements.s_ParameterRecordDataItem)
                {
                    CreateParameterRecordDataItem(nodes, list);
                   
                }
                else if (nodes.Current != null && nodes.Current.LocalName == Elements.s_ParameterRecordDataRef)
                {
                    CreateParameterRecordDataRef(nodes, list);
                   
                }
            }
            hash[Models.s_FieldParameterRecordData] = list;

        }

        private void CreateParameterRecordDataRef(XPathNodeIterator nodes, ArrayList list)
        {
            XPathNavigator nav = nodes.Current;
            if (nav == null)
            {
                return;
            }
            // Get RecordDataTarget attribute. Must.
            string sID = nav.GetAttribute(Attributes.s_RecordDataTarget, String.Empty);
            if (!this.CStore.RecordDataItems.ContainsKey(sID))
                throw new PreparationException("Couldn't get record data from store with ID '" + sID + "'!");

            // Get TransferSequence attribute. Optional.
            string strTransferSequence = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            UInt32 transferSequence = 0;
            if (!String.IsNullOrEmpty(strTransferSequence))
            {
                UInt32.TryParse(strTransferSequence, NumberStyles.Any, CultureInfo.InvariantCulture, out transferSequence);
            }

            // Get ChangeableWithBump attribute. Optional.
            string strChangeableWithBump = nav.GetAttribute(Attributes.s_ChangeableWithBump, String.Empty);
            bool changeableWithBump = Attributes.s_DefaultChangeableWithBump;
            if (!string.IsNullOrEmpty(strChangeableWithBump))
                changeableWithBump = Help.GetBool(strChangeableWithBump, Attributes.s_DefaultChangeableWithBump);

            // Build values.

            Hashtable valuehash = new Hashtable();
            C.ParameterRecordData prmRecDataRef = (C.ParameterRecordData)this.CStore.RecordDataItems[sID];
            valuehash.Add(Models.s_FieldChangeableWithBump, null);
            valuehash.Add(Models.s_FieldConsts, null);
            valuehash.Add(Models.s_FieldIndex, null);
            valuehash.Add(Models.s_FieldLength, null);
            valuehash.Add(Models.s_FieldMenuItems, null);
            valuehash.Add(Models.s_FieldName, null);
            valuehash.Add(Models.s_FieldNameTextId, null);
            valuehash.Add(Models.s_FieldRecordDataId, null);
            valuehash.Add(Models.s_FieldRefs, null);
            valuehash.Add(Models.s_FieldTransferSequence, null);

            if (!string.IsNullOrEmpty(strChangeableWithBump))
                valuehash[Models.s_FieldChangeableWithBump] = changeableWithBump;
            else
                valuehash[Models.s_FieldChangeableWithBump] = prmRecDataRef.ChangeableWithBump;
            if (prmRecDataRef.Consts != null)
            {
                ArrayList consts = new ArrayList(prmRecDataRef.Consts);
                valuehash[Models.s_FieldConsts] = consts;
            }
            else
            {
                valuehash[Models.s_FieldConsts] = null;
            }

            valuehash[Models.s_FieldIndex] = prmRecDataRef.Index;
            valuehash[Models.s_FieldLength] = prmRecDataRef.Length;
            if (prmRecDataRef.MenuItems != null)
            {
                ArrayList menuItems = new ArrayList(prmRecDataRef.MenuItems);
                valuehash[Models.s_FieldMenuItems] = menuItems;
            }
            else
            {
                valuehash[Models.s_FieldMenuItems] = null;
            }
            valuehash[Models.s_FieldName] = prmRecDataRef.Name;
            valuehash[Models.s_FieldNameTextId] = prmRecDataRef.NameTextID;
            valuehash[Models.s_FieldRecordDataId] = prmRecDataRef.RecordDataID;
            if (prmRecDataRef.Refs != null)
            {
                ArrayList refs = new ArrayList(prmRecDataRef.Refs);
                valuehash[Models.s_FieldRefs] = refs;
            }
            else
            {
                valuehash[Models.s_FieldRefs] = null;
            }
            if (!string.IsNullOrEmpty(strTransferSequence))
                valuehash[Models.s_FieldTransferSequence] = transferSequence;
            else
                valuehash[Models.s_FieldTransferSequence] = prmRecDataRef.TransferSequence;

            C.GsdObject prmRecData = new C.ParameterRecordData();
            bool succeeded = prmRecData.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectParameterRecordData + " couldn't be filled with data!");

            // Add it to the list.
            list.Add(prmRecData);
        }
        private void CreateParameterRecordDataItem(XPathNodeIterator nodes, ArrayList list)
        {
            // Create data record itself.
            object obj = this.CreateGsdObject(Models.s_ObjectParameterRecordData, nodes.Current);
            if (null == obj)
                throw new CreationException("Object '" + Models.s_ObjectParameterRecordData + "' couldn't be created!");

            // Add it to the list.
            list.Add(obj);
        }
        protected override void CreateRecordDataItems()
        {
            // Select all RecordDataItems
            XPathNavigator nav = this.Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_ParameterRecordDataItem]);

            // Check whether RecordDataItems are available.
            if (nodes.Count == 0)
                return;
            //throw new CreationException("Elements '" + Elements.ParameterRecordDataItem + "' aren't available from the actual GSDML file!");

            // Prepare data for each ParameterRecordDataItem, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectParameterRecordData;
                C.ParameterRecordData obj = (C.ParameterRecordData)this.CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectParameterRecordData + "' couldn't be created!");

                // Add object to store.
                this.CStore.RecordDataItems.Add(obj.RecordDataID, obj);
            }
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldCommunicationInterfaces, null);

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            // Select Communication Interface Modules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_CommunicationInterfaceModule, Namespaces.s_GsdmlDeviceProfile, false);

            // Check whether submodules are available.
            if (nodes.Count <= 0)
                return;

            ArrayList list = new ArrayList(nodes.Count);

            // Add found CommunicationInterfaces to hash.
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sID = nodes.Current.GetAttribute(Attributes.s_CimTarget, String.Empty);
                if (!this.CStore.CommunicationInterfaceItems.ContainsKey(sID))
                {
                    throw new PreparationException("Couldn't get communication interface from store with ID '" + sID + "'!");
                }
                else
                {
                    // Add communication interface to list.
                    list.Add(this.CStore.CommunicationInterfaceItems[sID]);
                }
            }

            hash[Models.s_FieldCommunicationInterfaces] = list;
        }

        protected override void CreateCommunicationInterfaces()
        {
            // Select all CommunicationInterfaces
            XPathNavigator nav = this.Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_CommunicationInterfaceItem]);

            // Check whether CommunicationInterfaceItems are available.
            if (nodes.Count == 0)
                return;
            //throw new CreationException("Elements '" + Elements.CommunicationInterfaceItem + "' aren't available from the actual GSDML file!");

            // Prepare data for each CommunicationInterfaceItem, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectCommunicationInterface;
                C.CommunicationInterface obj = (C.CommunicationInterface)this.CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCommunicationInterface + "' couldn't be created!");

                // Add object to store.
                this.CStore.CommunicationInterfaceItems.Add(obj.CIM_ID, obj);
            }
        }

        protected virtual void PrepareCommunicationInterface(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldCimId, null);
            hash.Add(Models.s_FieldCimInterface, null);
            hash.Add(Models.s_FieldCimSupportedRecords, null);
            hash.Add(Models.s_FieldCimResources, null);
            hash.Add(Models.s_FieldCimSupportedFeatures, null);
            hash.Add(Models.s_FieldCimProtection, null);

            // Get CIM ID attribute. Optional.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldCimId] = attr;

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_CimInterface, Namespaces.s_GsdmlDeviceProfile);
            object obj = null;
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectCIMInterface, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCIMInterface + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCimInterface] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_CimSupportedRecords, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectCIMSupportedRecords, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCIMSupportedRecords + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCimSupportedRecords] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_CimResources, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectCIMResources, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCIMResources + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCimResources] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_CimSupportedFeatures, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectCIMSupportedFeatures, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCIMSupportedFeatures + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCimSupportedFeatures] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_Protection, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectCIMProtection, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectCIMProtection + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldCimProtection] = obj;
            }
        }

        protected virtual void PrepareCIMInterface(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldVendorIdentNumber, null);
            hash.Add(Models.s_FieldDeviceIdentNumber, null);
            hash.Add(Models.s_FieldInstance, null);

            // Get VendorID attribute. Must.
            string vendorID = nav.GetAttribute(Attributes.s_VendorId, String.Empty);
            hash[Models.s_FieldVendorIdentNumber] = UInt32.Parse(vendorID.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);

            // Get DeviceID attribute. Must.
            string deviceID = nav.GetAttribute(Attributes.s_DeviceId, String.Empty);
            hash[Models.s_FieldDeviceIdentNumber] = UInt32.Parse(deviceID.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);

            // Get Instance attribute. Must.
            string instance = nav.GetAttribute(Attributes.s_Instance, String.Empty);
            hash[Models.s_FieldInstance] = UInt32.Parse(instance.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
        }

        protected virtual void PrepareCIMSupportedRecords(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSupportedRecords, null);

            // Get SupportedCIMRecords attribute. Optional.
            ArrayList listSupportedRecords = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SupportedRecords, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsSupportedRecordEnumValueConvertable(token))
                    {
                        listSupportedRecords.Add(Enums.ConvertSupportedRecordEnum(token));
                    }
                }
            }
            if (listSupportedRecords.Count == 0)
                listSupportedRecords.Add(GSDI.SupportedRecords.GSD_SupportedRecord_None);
            hash[Models.s_FieldSupportedRecords] = listSupportedRecords;
        }

        protected virtual void PrepareCIMResources(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldNumberOfDeviceAccessAr, null);
            hash.Add(Models.s_FieldNumberOfImplicitAr, null);

            // Get NumberOfDeviceAccessAR attribute. Optional.
            string numberOfDeviceAccessAR = nav.GetAttribute(Attributes.s_NumberOfDeviceAccessAr, String.Empty);
            if (!String.IsNullOrEmpty(numberOfDeviceAccessAR))
            {
                UInt32 value = 0;
                UInt32.TryParse(numberOfDeviceAccessAR, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfDeviceAccessAr] = value;
            }

            // Get NumberOfImplicitAR attribute. Optional.
            string numberOfImplicitAR = nav.GetAttribute(Attributes.s_NumberOfImplicitAr, String.Empty);
            if (!String.IsNullOrEmpty(numberOfImplicitAR))
            {
                UInt32 value = 0;
                UInt32.TryParse(numberOfImplicitAR, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfImplicitAr] = value;
            }
        }

        protected virtual void PrepareCIMSupportedFeatures(XPathNavigator nav, ref Hashtable hash)
        {
           
            hash.Add(Models.s_FieldSnmpFeaturesSupported, null);

          

            // Get SNMP_FeaturesSupported attribute. Optional.
            ArrayList listSupportedSNMPFeatures = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SnmpFeaturesSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsSNMPFeaturesSupportedEnumValueConvertable(token))
                    {
                        listSupportedSNMPFeatures.Add(Enums.ConvertSNMPFeaturesSupportedEnum(token));
                    }
                }
            }
            if (listSupportedSNMPFeatures.Count == 0)
                listSupportedSNMPFeatures.Add(GSDI.SnmpSupportedFeatures.GSD_SNMPSupportedFeature_None);
            hash[Models.s_FieldSnmpFeaturesSupported] = listSupportedSNMPFeatures;
        }

        protected virtual void PrepareCIMProtection(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldKeyDerivation, null);
            hash.Add(Models.s_FieldKeyAgreement, null);
            hash.Add(Models.s_FieldDigitalSignature, null);
            hash.Add(Models.s_FieldStreamProtection, null);
            hash.Add(Models.s_FieldAlarmProtection, null);
            hash.Add(Models.s_FieldConnectionManagementProtection, null);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_KeyDerivation, Namespaces.s_GsdmlDeviceProfile);
            object obj = null;
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectKeyDerivation, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectKeyDerivation + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldKeyDerivation] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_KeyAgreement, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectKeyAgreement, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectKeyAgreement + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldKeyAgreement] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_DigitalSignature, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectDigitalSignature, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectDigitalSignature + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldDigitalSignature] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_StreamProtection, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectStreamProtection, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectStreamProtection + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldStreamProtection] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_AlarmProtection, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectAlarmProtection, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAlarmProtection + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldAlarmProtection] = obj;
            }

            nodes = nav.SelectChildren(Elements.s_ConnectionManagementProtection, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }
                obj = CreateGsdObject(Models.s_ObjectConnectionManagementProtection, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectConnectionManagementProtection + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldConnectionManagementProtection] = obj;
            
        }

        
        protected virtual void PrepareKeyDerivation(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAlgorithms, null);

            // Get Algorithms attribute. Optional.
            ArrayList listAlgorithms = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_Algorithms, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAlgorithms1EnumValueConvertable(token))
                    {
                        listAlgorithms.Add(Enums.ConvertAlgorithms1Enum(token));
                    }
                }
            }
            if (listAlgorithms.Count == 0)
                listAlgorithms.Add(GSDI.Algorithms1.GSD_Algorithms1_None);
            hash[Models.s_FieldAlgorithms] = listAlgorithms;
        }

        protected virtual void PrepareKeyAgreement(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAlgorithms, null);

            // Get Algorithms attribute. Optional.
            ArrayList listAlgorithms = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_Algorithms, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAlgorithms2EnumValueConvertable(token))
                    {
                        listAlgorithms.Add(Enums.ConvertAlgorithms2Enum(token));
                    }
                }
            }
            if (listAlgorithms.Count == 0)
                listAlgorithms.Add(GSDI.Algorithms2.GSD_Algorithms2_None);
            hash[Models.s_FieldAlgorithms] = listAlgorithms;
        }

        protected virtual void PrepareDigitalSignature(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAlgorithms, null);

            // Get Algorithms attribute. Optional.
            ArrayList listAlgorithms = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_Algorithms, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAlgorithms3EnumValueConvertable(token))
                    {
                        listAlgorithms.Add(Enums.ConvertAlgorithms3Enum(token));
                    }
                }
            }
            if (listAlgorithms.Count == 0)
                listAlgorithms.Add(GSDI.Algorithms3.GSD_Algorithms3_None);
            hash[Models.s_FieldAlgorithms] = listAlgorithms;
        }

        protected virtual void PrepareStreamProtection(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAuthnOnly, null);
            hash.Add(Models.s_FieldAuthnEnc, null);

            // Get AuthnOnly attribute. Optional.
            ArrayList listAuthnOnly = new ArrayList();
            ArrayList separateTokens;
            string attr = nav.GetAttribute(Attributes.s_AuthnOnly, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnOnly.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnOnly.Count == 0)
                listAuthnOnly.Add(GSDI.AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnOnly] = listAuthnOnly;

            // Get AuthnEnc attribute. Optional.
            ArrayList listAuthnEnc = new ArrayList();
            attr = nav.GetAttribute(Attributes.s_AuthnEnc, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnEnc.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnEnc.Count == 0)
                listAuthnEnc.Add(GSDI.AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnEnc] = listAuthnEnc;
        }

        protected virtual void PrepareAlarmProtection(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAuthnOnly, null);
            hash.Add(Models.s_FieldAuthnEnc, null);

            // Get AuthnOnly attribute. Optional.
            ArrayList listAuthnOnly = new ArrayList();
            ArrayList separateTokens;
            string attr = nav.GetAttribute(Attributes.s_AuthnOnly, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnOnly.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnOnly.Count == 0)
                listAuthnOnly.Add(AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnOnly] = listAuthnOnly;

            // Get AuthnEnc attribute. Optional.
            ArrayList listAuthnEnc = new ArrayList();
            attr = nav.GetAttribute(Attributes.s_AuthnEnc, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnEnc.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnEnc.Count == 0)
                listAuthnEnc.Add(AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnEnc] = listAuthnEnc;
        }

        protected virtual void PrepareConnectionManagementProtection(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAuthnOnly, null);
            hash.Add(Models.s_FieldAuthnEnc, null);

            // Get AuthnOnly attribute. Optional.
            ArrayList listAuthnOnly = new ArrayList();
            ArrayList separateTokens;
            string attr = nav.GetAttribute(Attributes.s_AuthnOnly, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnOnly.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnOnly.Count == 0)
                listAuthnOnly.Add(AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnOnly] = listAuthnOnly;

            // Get AuthnEnc attribute. Optional.
            ArrayList listAuthnEnc = new ArrayList();
            attr = nav.GetAttribute(Attributes.s_AuthnEnc, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAuthnXXXEnumValueConvertable(token))
                    {
                        listAuthnEnc.Add(Enums.ConvertAuthnXXXEnum(token));
                    }
                }
            }
            if (listAuthnEnc.Count == 0)
                listAuthnEnc.Add(AuthnXxx.GSD_AuthnXXX_None);
            hash[Models.s_FieldAuthnEnc] = listAuthnEnc;
        }

        /// PortSubmoduleItem/MAUTypeList/MAUTypeItem: New element APLPortClassification
        /// with 4 Attributes SegmentClass, PortClass, PowerClass, IS_ProtectionClass
        protected override void PrepareMauTypeItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAplPortClassification, null);

            // Call base class method first.
            base.PrepareMauTypeItem(nav, ref hash);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_AplPortClassification, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                object obj = CreateGsdObject(Models.s_ObjectAPLPortClassification, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAPLPortClassification + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldAplPortClassification] = obj;
            }
        }

        /// InterfaceSubmoduleItem: New attributes DCP_FeaturesSupported, SNMP_FeaturesSupported, APL_FeaturesSupported
        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDcpFeaturesSupported, null);
            hash.Add(Models.s_FieldSnmpFeaturesSupported, null);
            hash.Add(Models.s_FieldAplFeaturesSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            hash[Models.s_FieldDcpFeaturesSupported] = GetListDcpFeaturesSupported(nav);
            hash[Models.s_FieldSnmpFeaturesSupported] = GetListSnmpFeaturesSupported(nav);
            hash[Models.s_FieldAplFeaturesSupported] = GetListAplFeaturesSupported(nav);

            

               
        }
        private  ArrayList GetListAplFeaturesSupported(XPathNavigator nav)
        {
            ArrayList listAPLFeaturesSupported = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_AplFeaturesSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsAPLFeaturesSupportedEnumValueConvertable(token))
                    {
                        listAPLFeaturesSupported.Add(Enums.ConvertAPLFeaturesSupportedEnum(token));
                    }
                }
            }
            if (listAPLFeaturesSupported.Count == 0)
                listAPLFeaturesSupported.Add(GSDI.AplSupportedFeatures.GSD_APLSupportedFeature_None);

            return listAPLFeaturesSupported;
        }
        private  ArrayList GetListSnmpFeaturesSupported(XPathNavigator nav)
        {
            ArrayList listSNMPFeaturesSupported = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SnmpFeaturesSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsSNMPFeaturesSupportedEnumValueConvertable(token))
                    {
                        listSNMPFeaturesSupported.Add(Enums.ConvertSNMPFeaturesSupportedEnum(token));
                    }
                }
            }
            if (listSNMPFeaturesSupported.Count == 0)
                listSNMPFeaturesSupported.Add(GSDI.SnmpSupportedFeatures.GSD_SNMPSupportedFeature_None);

            return listSNMPFeaturesSupported;
        }

        private  ArrayList GetListDcpFeaturesSupported(XPathNavigator nav)
        {
            ArrayList listDCPFeaturesSupported = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_DcpFeaturesSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsDCPFeaturesSupportedEnumValueConvertable(token))
                    {
                        listDCPFeaturesSupported.Add(Enums.ConvertDCPFeaturesSupportedEnum(token));
                    }
                }
            }
            if (listDCPFeaturesSupported.Count == 0)
                listDCPFeaturesSupported.Add(GSDI.DcpSupportedFeatures.GSDDCPSupportedFeatureNone);
            return listDCPFeaturesSupported;
        }

        /// PortSubmoduleItem/MAUTypeList/MAUTypeItem: New element APLPortClassification
        /// with 4 Attributes SegmentClass, PortClass, PowerClass, IS_ProtectionClass
        protected virtual void PrepareAPLPortClassification(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSegmentClass, null);
            hash.Add(Models.s_FieldPortClass, null);
            hash.Add(Models.s_FieldPowerClass, null);
            hash.Add(Models.s_FieldProtectionClass, null);
            hash.Add(Models.s_FieldSegmentClassAsString, null);
            hash.Add(Models.s_FieldPortClassAsString, null);
            hash.Add(Models.s_FieldPowerClassAsString, null);
            hash.Add(Models.s_FieldProtectionClassAsString, null);

            // Get SegmentClass attribute. Must.

            string segmentClass = nav.GetAttribute(Attributes.s_SegmentClass, String.Empty);
            hash[Models.s_FieldSegmentClassAsString] = segmentClass;
            if (Enums.IsSegmentClassEnumValueConvertable(segmentClass))
            {
                hash[Models.s_FieldSegmentClass] = Enums.ConvertSegmentClassEnum(segmentClass);
            }

            // Get PortClass attribute. Must.

            string portClass = nav.GetAttribute(Attributes.s_PortClass, String.Empty);
            hash[Models.s_FieldPortClassAsString] = portClass;
            if (Enums.IsPortClassEnumValueConvertable(portClass))
            {
                hash[Models.s_FieldPortClass] = Enums.ConvertPortClassEnum(portClass);
            }

            // Get PowerClass attribute. Must.

            string powerClass = nav.GetAttribute(Attributes.s_PowerClass, String.Empty);
            hash[Models.s_FieldPowerClassAsString] = powerClass;
            if (Enums.IsPowerClassEnumValueConvertable(powerClass))
            {
                hash[Models.s_FieldPowerClass] = Enums.ConvertPowerClassEnum(powerClass);
            }

            // Get ProtectionClass attribute. Must.

            string protectionClass = nav.GetAttribute(Attributes.s_ProtectionClass, String.Empty);
            hash[Models.s_FieldProtectionClassAsString] = protectionClass;
            if (Enums.IsProtectionClassEnumValueConvertable(protectionClass))
            {
                hash[Models.s_FieldProtectionClass] = Enums.ConvertProtectionClassEnum(protectionClass);
            }
        }

        protected override void PrepareParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRecordDataId, null);

            // Call base class method first.
            base.PrepareParameterRecordData(nav, ref hash);

            // Get RecordData ID attribute. Optional.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldRecordDataId] = attr;
        }

        protected override void PrepareCertificationInfo(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldNetloadClass, null);
            hash.Add(Models.s_FieldNetloadClasses, null);
            hash.Add(Models.s_FieldProfileProcessAutomation, null);
            // Call base class method first.
            base.PrepareCertificationInfo(nav, ref hash);

            // Get NetloadClass attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_NetloadClass, String.Empty);
            hash[Models.s_FieldNetloadClass] = attr;

            // Navigate to NetloadClasses elements and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_NetloadClasses, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectNetloadClasses, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectNetloadClasses + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldNetloadClasses] = list;
            // Navigate to ProfileProcessAutomation elements and create it. Optional.
            nodes = nav.SelectChildren(Elements.s_ProfileProcessAutomation, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = CreateGsdObject(Models.s_ObjectProfileProcessAutomation, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileProcessAutomation + "' couldn't be created!");

                // Set hash variable.
                hash[Models.s_FieldProfileProcessAutomation] = obj;
            }
        }

        protected virtual void PrepareNetloadClasses(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldLinkSpeed, null);
            hash.Add(Models.s_FieldLinkSpeedAsString, null);
            hash.Add(Models.s_FieldNetloadClass, null);

            // Get LinkSpeed attribute. Must.
            string attr = nav.GetAttribute(Attributes.s_LinkSpeed, String.Empty);
            hash[Models.s_FieldLinkSpeedAsString] = attr;
            if (Enums.IsLinkSpeedEnumValueConvertable(attr))
            {
                hash[Models.s_FieldLinkSpeed] = Enums.ConvertLinkSpeedEnum(attr);
            }

            // Get NetloadClass attribute. Must.
            attr = nav.GetAttribute(Attributes.s_NetloadClass, String.Empty);
            hash[Models.s_FieldNetloadClass] = attr;
        }

        protected virtual void PrepareProfileProcessAutomation(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldPADeviceClass, null);
            hash.Add(Models.s_FieldPAProfileVersion, null);
            hash.Add(Models.s_FieldPAProfileDeviceID, null);
            hash.Add(Models.s_FieldPAProfileDeviceDAP_ID, null);

            // Get LinkSpeed attribute. Optional.
            hash[Models.s_FieldPADeviceClass] = Enums.PADeviceClass_ProcessControlDevice;
            string attr = nav.GetAttribute(Attributes.s_PaDeviceClass, String.Empty);
            if (!string.IsNullOrEmpty(attr) && Enums.IsPADeviceClassEnumValueConvertable(attr))
            {
                hash[Models.s_FieldPADeviceClass] = Enums.ConvertPADeviceClassEnum(attr);
            }

            // Get PAProfileVersion attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PaProfileVersion, String.Empty);
            if (!string.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPAProfileVersion] = attr;
            }

            // Get PAProfileDeviceID attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PaProfileDeviceId, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPAProfileDeviceID] = UInt32.Parse(attr.Substring(2), NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture);
            }

            // Get PAProfileDeviceDAP_ID attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PaProfileDeviceDapId, String.Empty);
            if (!string.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPAProfileDeviceDAP_ID] = attr;
            }
        }

        /// <summary>
        /// The F-parameter record data (with F_iPar_CRC) is a fixed 14 byte block with configuration data. It is
        /// organized as follows:
        /// 0	F_Prm_Flag1		(Unsigned8)		0x08 (8)
        ///			Bit 0:	xxx	(F_Check_SeqNr)		0
        ///			Bit 1:	F_Check_iPar			0 "No check"
        ///			Bit 2:	F_SIL					0 "SIL3"
        ///			Bit 3:							1
        ///			Bit 4:	F_CRC_Length			0 "3 Byte CRC"
        ///			Bit 5:							0
        ///			Bit 6:	F_CRC_Seed			    0
        ///			Bit 7:	xxx						0 "reserved"
        /// 1	F_Prm_Flag2		(Unsigned8)		0x40 (64)
        ///			Bit 0:	F_Passivation		    0
        ///			Bit 1:	xxx						0 "reserved"
        ///			Bit 2:	xxx						0 "reserved"
        ///			Bit 3:	F_Block_ID				0 "F-Host/F-Device relationship"
        ///			Bit 4:							0
        ///			Bit 5:							0
        ///			Bit 6:	F_Par_Version			1 "PROFIsafe V2.0"
        ///			Bit 7:							0
        /// 2	F_Source_Add	(Unsigned16)	0x00 (0)
        /// 3									0x01 (1)
        /// 4	F_Dest_Add		(Unsigned16)	0x00 (0)
        /// 5									0x01 (1)
        /// 6	F_WD_Time		(Unsigned16)	0x00 (0)
        /// 7									0x96 (150)
        /// 8	 F_WD_Time_2	(Unsigned16)	0x00 (0)
        /// 9									0x3E8 (1000)
        /// 10   F_iPar_CRC		(Unsigned32)
        /// 14  F_Par_CRC		(Unsigned16)	0xBB (187)
        /// 
        /// If there is no F_iPar_CRC and no F_WD_TIME_2 the F-parameter record data is a fixed 10 byte block with configuration data
        /// If there is no F_iPar_CRC but F_WD_TIME_2 the F-parameter record data is a fixed 12 byte block with configuration data
        /// If there is no F_WD_TIME_2 but F_iPar_CRC the F-parameter record data is a fixed 14 byte block with configuration data
        /// </summary>
        /// <returns>Const object with default binary settings with byte length 10.</returns>
        protected override void PrepareFParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for FParameterRecordData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIndex, null);
            hash.Add(Models.s_FieldLength, null);
            hash.Add(Models.s_FieldName, null);		// always null!
            hash.Add(Models.s_FieldNameTextId, null);	// always null!
            hash.Add(Models.s_FieldConsts, null);

            hash.Add(Models.s_FieldTransferSequence, null);
            hash.Add(Models.s_FieldRefs, null);
            hash.Add(Models.s_FieldFParamDescCrc, null);
            hash.Add(Models.s_FieldChangeableWithBump, null);

            //XPathNodeIterator nodes = null;

            // Get F_ParamDescCRC attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_FParamDescCrc, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldFParamDescCrc] = value;

            // Get Index attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Index, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldIndex] = value;

            // Get TransferSequence attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTransferSequence] = Attributes.s_DefaultTransferSequence;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldTransferSequence] = value;
            }

            // Get ChangeableWithBump attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ChangeableWithBump, String.Empty);
            hash[Models.s_FieldChangeableWithBump] = Help.GetBool(attr, Attributes.s_DefaultChangeableWithBump);

            byte FBlockID = 0;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FBlockID, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                }

                byte.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out FBlockID);
            }

        
            bool IsiParCRC = false;
            if ((FBlockID & 1) == 1)
            {
                IsiParCRC = true;
            }

          
            bool IsWD_Time_2 = false;
            if ((FBlockID & 2) == 2)
            {
                IsWD_Time_2 = true;
            }

            if (IsiParCRC && IsWD_Time_2)
            {
                // If there is a F_iPar_CRC and F_WD_TIME_2, use a FParameterRecord with 16 Bytes
                hash[Models.s_FieldLength] = Attributes.s_Ext2FixedFParameterRecordDataLength;
            }
            else if (IsiParCRC)
            {
                // If there is a F_iPar_CRC but no F_WD_TIME_2, use a FParameterRecord with 14 Bytes
                hash[Models.s_FieldLength] = Attributes.s_ExtFixedFParameterRecordDataLength;
            }
            else if (IsWD_Time_2)
            {
                // If there is a F_WD_TIME_2 but no F_iPar_CRC, use a FParameterRecord with 12 Bytes
                hash[Models.s_FieldLength] = Attributes.s_Ext1FixedFParameterRecordDataLength;
            }
            else
            {
                // If there is no F_iPar_CRC and no F_WD_TIME_2, use a FParameterRecord with 10 Bytes
                hash[Models.s_FieldLength] = Attributes.s_FixedFParameterRecordDataLength;
            }

            // Set Length property.

            Hashtable h = null;

            // Set Consts property.
            h = new Hashtable();
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldValues, null);

            ArrayList consts = new ArrayList();
            object obj = this.PrepareFParameterRecordDataCreateConst();
            consts.Add(obj);
            hash[Models.s_FieldConsts] = consts;

            // Set Refs property.
            h = new Hashtable();
            h.Add(Models.s_FieldDataType, null);
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldBitOffset, null);
            h.Add(Models.s_FieldBitLength, null);
            h.Add(Models.s_FieldIsChangeable, null);
            h.Add(Models.s_FieldIsVisible, null);
            h.Add(Models.s_FieldName, null);
            h.Add(Models.s_FieldNameTextId, null);
            h.Add(Models.s_FieldHelp, null);
            h.Add(Models.s_FieldValueGsdId, null);
            h.Add(Models.s_FieldDefaultValue, null);
            h.Add(Models.s_FieldValues, null);
            h.Add(Models.s_FieldValueType, null);

            ArrayList refs = new ArrayList();
            obj = PrepareFParameterRecordDataCreateRefFCheckIPar(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSil(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcLength(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFBlockID(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFParVersion(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSourceAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFDestAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFWdTime(nav, ref h);
            refs.Add(obj);
            if (IsWD_Time_2)
            {
                // there is a F_WD_Time_2 
                obj = PrepareFParameterRecordData_Create_Ref_F_WD_Time_2(nav, ref h);
                refs.Add(obj);
            }
            if (IsiParCRC)
            {
                // there is a F_iPar_CRC 
                obj = PrepareFParameterRecordDataCreateRefFiParCrc(nav, ref h);
                refs.Add(obj);
            }
            obj = PrepareFParameterRecordDataCreateRefFParCrc(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcSeed(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFPassivation(nav, ref h);
            refs.Add(obj);

            hash[Models.s_FieldRefs] = refs;
        }

        /// <summary>
        /// The optional element F_WD_Time_2 defines the secondary watchdog time in the F unit.
        /// The engineering tool calculates and sets the value during commissioning.
        /// The time base for the encoded watchdog time is 1 ms. 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	8
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 1..65535, max. range: 1..65535, default value: 450).
        /// </summary>
        /// <returns>Ref object for the F_WD_Time parameter.</returns>
        protected virtual object PrepareFParameterRecordData_Create_Ref_F_WD_Time_2(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FWdTime2;
                hash[Models.s_FieldNameTextId] = Elements.s_FWdTime2;
                hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
                hash[Models.s_FieldByteOffset] = (uint)8;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)16;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;

                // Build values.
                ArrayList list = new ArrayList();

                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                string sAllowedValues = @"1..65535";
                ushort defaultValue = 1000;	// Is initially setted with default value!

                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FWdTime2, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    if (nodes.Current != null)
                    {
                        string attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                        if (attr.Length != 0)
                            sAllowedValues = attr;

                        attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                        if (attr.Length != 0)
                            defaultValue = XmlConvert.ToUInt16(attr);
                    }
                }

                // Create needed area item(s).

                hash[Models.s_FieldDefaultValue] = defaultValue;

                // Split incoming string to pairs and numbers in a list.
                C.GsdObject obj = null;
                ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

                foreach (string s in splitlist)
                {
                    // Get min and max value.
                    string ssMin = String.Empty;
                    string ssMax = String.Empty;
                    int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                    if (index != -1)
                    {
                        // Area, separate area and add values (1..65534).
                        ssMin = s.Substring(0, index);	// 1
                        ssMax = s.Substring(index + 2);	// 65534
                    }
                    else
                    {
                        // Value, create area with same min and max value.
                        ssMin = s;
                        ssMax = s;
                    }

                    // Create AreaItem(s).
                    ushort usMin = XmlConvert.ToUInt16(ssMin);
                    ushort usMax = XmlConvert.ToUInt16(ssMax);

                    valuehash[Models.s_FieldMinValue] = usMin;
                    valuehash[Models.s_FieldMaxValue] = usMax;

                    obj = new C.AreaItem();
                    succeeded = obj.Fill(valuehash);
                    if (!succeeded)
                        throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                    list.Add(obj);
                }

                hash[Models.s_FieldValues] = list;

                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_WD_Time)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FParameterRecordData - Ref (F_WD_Time)!", e);
            }
        }


        /// <summary>
        /// This CRC1 key is generated by the engineering tool across the F parameters. 
        /// 
        /// Data type:		Unsigned32
        /// Byte offset:	10
        /// Bit offset:		0
        /// Bit length:		32
        /// 
        ///	Parameter values (max. range: 0..4294967295)
        /// </summary>
        /// <returns>Ref object for the F_iPar_CRC parameter.</returns>
        protected override object PrepareFParameterRecordDataCreateRefFiParCrc(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            XPathNodeIterator nodesToCheck = nav.SelectChildren(Elements.s_FWdTime2, Namespaces.s_GsdmlDeviceProfile);
            bool IsWD_Time_2 = false;
            if (nodesToCheck.MoveNext())
            {
                IsWD_Time_2 = true;
            }

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FIParCrc;
            hash[Models.s_FieldNameTextId] = Elements.s_FIParCrc;
            hash[Models.s_FieldDataType] = GSDI.DataTypes.GSDUnsigned32;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)32;
            hash[Models.s_FieldHelp] = String.Empty;

            if (IsWD_Time_2)
            {
                // If there is a F_WD_TIME_2, the ByteOffset is 10 Bytes
                hash[Models.s_FieldByteOffset] = (uint)10;
            }
            else
            {
                // If there is no F_WD_TIME_2, the ByteOffset is 8 Bytes
                hash[Models.s_FieldByteOffset] = (uint)8;
            }

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = GSDI.ValueTypes.GSDArea;
            hash[Models.s_FieldIsChangeable] = true;	// auto
            hash[Models.s_FieldIsVisible] = true;

            uint DefaultValue = 0;	// Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length > 0)
                    {
                        UInt32 value = 0;
                        UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                        DefaultValue = value;
                    }
                }
            }

            hash[Models.s_FieldDefaultValue] = DefaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();

            C.GsdObject obj = new C.AreaItem();
            valuehash[Models.s_FieldMinValue] = uint.MinValue;
            valuehash[Models.s_FieldMaxValue] = uint.MaxValue;
            succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }


        /// <summary>
        /// This CRC1 key is generated by the engineering tool across the F parameters. 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	14
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 0..65535, max. range: 0..65535, default value: 53356)
        /// </summary>
        /// <returns>Ref object for the F_Par_CRC parameter.</returns>
        protected override object PrepareFParameterRecordDataCreateRefFParCrc(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            XPathNodeIterator nodesToCheck = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);
            bool IsiParCRC = false;
            if (nodesToCheck.MoveNext())
            {
                IsiParCRC = true;
            }

            nodesToCheck = nav.SelectChildren(Elements.s_FWdTime2, Namespaces.s_GsdmlDeviceProfile);
            bool IsWD_Time_2 = false;
            if (nodesToCheck.MoveNext())
            {
                IsWD_Time_2 = true;
            }

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FParCrc;
            hash[Models.s_FieldNameTextId] = Elements.s_FParCrc;
            hash[Models.s_FieldDataType] = GSDI.DataTypes.GSDUnsigned16;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)16;
            hash[Models.s_FieldHelp] = String.Empty;

            if (IsiParCRC && IsWD_Time_2)
            {
                // If there is a F_iPar_CRC and F_WD_TIME_2, the ByteOffset is 14 Bytes
                hash[Models.s_FieldByteOffset] = (uint)14;
            }
            else if (IsiParCRC)
            {
                // If there is a F_iPar_CRC but no F_WD_TIME_2, the ByteOffset is 12 Bytes
                hash[Models.s_FieldByteOffset] = (uint)12;
            }
            else if (IsWD_Time_2)
            {
                // If there is a F_WD_TIME_2 but no F_iPar_CRC, the ByteOffset is 10 Bytes
                hash[Models.s_FieldByteOffset] = (uint)10;
            }
            else
            {
                // If there is no F_iPar_CRC and no F_WD_TIME_2, the ByteOffset is 8 Bytes
                hash[Models.s_FieldByteOffset] = (uint)8;
            }

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = GSDI.ValueTypes.GSDArea;
            hash[Models.s_FieldIsChangeable] = true;	// auto
            hash[Models.s_FieldIsVisible] = true;

            ushort DefaultValue = 53356;	// Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FParCrc, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length > 0)
                        DefaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }
            }

            hash[Models.s_FieldDefaultValue] = DefaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();

            C.GsdObject obj = new C.AreaItem();
            valuehash[Models.s_FieldMinValue] = ushort.MinValue;
            valuehash[Models.s_FieldMaxValue] = ushort.MaxValue;
            succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        #endregion

        #endregion

    }
}
