/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AdressTailorCentralBL.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Tailor.Config;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.AddressTailoring.Logic
{
    internal class AdressTailorCentralBL : IFDecorator
    {
        public AdressTailorCentralBL(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        public override sealed void InitBL()
        {
            Interface.BaseActions.RegisterMethod(
                GetPdMasterTailorDataStructPlus.Name,
                GenericMethodGetPdMasterTailorDataStructPlus);
        }
        public override void Configure(
          IConfigInterface xmlDeviceInterface,
          SyncDomainType syncDomainType = null)
        {
            base.Configure(xmlDeviceInterface, syncDomainType);
            PclObject ioSystem = NavigationUtilities.GetIoSystem(Interface);
            if (ioSystem == null)
            {
                return;
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            bool pnIoAddressTailoringEnabled = ioSystem.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoAddressTailoringEnabled,
                ac,
                false);
            if (ac.IsOkay)
            {
                AddressTailorUtility.SetAddressTailoringState(Interface, pnIoAddressTailoringEnabled);
                Interface.PCLCatalogObject.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoAddressTailoringEnabled,
                    pnIoAddressTailoringEnabled);
                ioSystem.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnIoAddressTailoringEnabled);
            }
        }

        /// <summary>
        /// Function creates the PdMasterTailorDataStruct
        /// </summary>
        /// <param name="data">The Method Data</param>
        private void GenericMethodGetPdMasterTailorDataStructPlus(IMethodData data)
        {
            if (data == null)
            {
                return;
            }

            TailorConfigLogicCentral TailorConfigLogicCentral = new TailorConfigLogicCentral(Interface);
            PdMasterTailorDataStruct pdMasterTailorData = TailorConfigLogicCentral.GetPDMasterTailorData();
            data.ReturnValue = pdMasterTailorData == null ? null : pdMasterTailorData.ToByteArray;
        }
    }
}