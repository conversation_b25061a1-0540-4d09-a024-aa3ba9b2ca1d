﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralModuleConfigureManager.cs        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class DecentralModuleConfigureManager
    {
        private Project m_Project;

        private Topology.Topology m_Topology
        {
            get;
        }

        internal DecentralModuleConfigureManager(Project project, Topology.Topology topology)
        {
            m_Project = project;
            m_Topology = topology;
        }

        internal void Configure(DecentralDeviceType xmlDecentralDevice,
            DecentralDevice decentralDevice,
            PNConfigLib.ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice,
            string gsdPath,
            ref IOAddressManager ioAddressManager)
        {
            if (xmlDecentralDevice.Module == null)
            {
                return;
            }

            foreach (ModuleType xmlModule in xmlDecentralDevice.Module.Where(m => !string.IsNullOrEmpty(m.GSDRefID) && (m.GSDRefID != lonDecentralDevice.GSDRefID)))
            {
                Module module = new DataModel.PCLObjects.Module(xmlModule.ModuleID);
                ModuleCatalog moduleCatalog =
                    Catalog.ModuleList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", gsdPath, xmlModule.GSDRefID)];
                module.PCLCatalogObject = moduleCatalog;
                decentralDevice.AddModule(module);

                VirtualSubmoduleConfigureManager vsmConfigureManager = new VirtualSubmoduleConfigureManager(m_Project);
                vsmConfigureManager.Configure(xmlDecentralDevice, xmlModule, module, moduleCatalog.VirtualSubmoduleList);

                DecentralSubmoduleConfigureManager decentralSubmoduleConfigureManager = new DecentralSubmoduleConfigureManager(m_Project);
                decentralSubmoduleConfigureManager.Configure(xmlDecentralDevice, xmlModule, moduleCatalog, module, gsdPath, ioAddressManager);

                ModuleBL moduleBL = new ModuleBL(module);
                moduleBL.Configure(xmlModule, ioAddressManager);
                m_Project.BusinessLogicList.Add(moduleBL);

                ModulePortConfigureManager portConfigureManager = new ModulePortConfigureManager(m_Project, m_Topology);
                portConfigureManager.Configure(moduleCatalog, xmlModule.Port, module, gsdPath);
            }
        }
    }
}
