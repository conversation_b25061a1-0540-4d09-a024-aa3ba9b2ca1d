<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:base="http://www.profibus.com/GSDML/2003/11/Primitives" targetNamespace="http://www.profibus.com/GSDML/2003/11/Primitives" elementFormDefault="qualified" attributeFormDefault="unqualified" version="2.31">
	<xsd:import namespace="http://www.w3.org/XML/1998/namespace" schemaLocation="xml.xsd"/>
	<xsd:annotation>
		<xsd:documentation>This schema contains primitives for General Station Description Markup Language (GSDML).</xsd:documentation>
		<xsd:appinfo>
			<schemainfo versiondate="20131120"/>
		</xsd:appinfo>
	</xsd:annotation>
	<!--_________________________________________________________-->
	<!--*** Base Data Types for GSDML Device Description ***-->
	<!--_________________________________________________________-->
	<!--*** Object definition ***-->
	<xsd:complexType name="ObjectT">
		<xsd:annotation>
			<xsd:documentation>Base type for objects which can be referenced.</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="ID" type="base:IdT" use="required"/>
	</xsd:complexType>
	<xsd:simpleType name="IdT">
		<xsd:annotation>
			<xsd:documentation>Base type for object identifiers.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token"/>
	</xsd:simpleType>
	<xsd:simpleType name="RefIdT">
		<xsd:annotation>
			<xsd:documentation>Base type for object references.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token"/>
	</xsd:simpleType>
	<!--_________________________________________________________-->
	<!--*** External Text definition ***-->
	<xsd:simpleType name="ExternalTextRefIdT">
		<xsd:restriction base="base:RefIdT"/>
	</xsd:simpleType>
	<xsd:complexType name="ExternalTextRefT">
		<xsd:attribute name="TextId" type="base:ExternalTextRefIdT" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="ExternalTextT">
		<xsd:attribute name="TextId" type="base:IdT" use="required"/>
		<xsd:attribute name="Value" type="xsd:string" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="ExternalTextDocumentT">
		<xsd:sequence>
			<xsd:element name="Text" type="base:ExternalTextT" maxOccurs="unbounded"/>
		</xsd:sequence>
		<xsd:attribute ref="xml:lang" use="required"/>
	</xsd:complexType>
	<xsd:element name="ExternalTextDocument" type="base:ExternalTextDocumentT">
		<xsd:annotation>
			<xsd:documentation>This defines the root element of the file that contains external text definitions.</xsd:documentation>
		</xsd:annotation>
		<xsd:unique name="ExternalText-ID">
			<xsd:selector xpath="base:Text"/>
			<xsd:field xpath="@TextId"/>
		</xsd:unique>
	</xsd:element>
	<!--_________________________________________________________-->
	<!--*** Basic Data Types ***-->
	<xsd:simpleType name="Unsigned8T">
		<xsd:restriction base="xsd:unsignedByte"/>
	</xsd:simpleType>
	<xsd:simpleType name="Unsigned16T">
		<xsd:restriction base="xsd:unsignedShort"/>
	</xsd:simpleType>
	<xsd:simpleType name="Unsigned32T">
		<xsd:restriction base="xsd:unsignedInt"/>
	</xsd:simpleType>
	<!--	<xsd:simpleType name="Unsigned8hexT">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="0x[0-9a-fA-F]{1,2}"/>
		</xsd:restriction>
	</xsd:simpleType> -->
	<xsd:simpleType name="Unsigned16hexT">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="0x[0-9a-fA-F]{1,4}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Unsigned32hexT">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="0x[0-9a-fA-F]{1,8}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ValueListT">
		<xsd:annotation>
			<xsd:documentation>Base type for a list including ranges of unsigned values.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="((\d+\.\.\d+)|(\d+))(( \d+\.\.\d+)|( \d+))*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SignedOrFloatValueListT">
		<xsd:annotation>
			<xsd:documentation>Base type for a list including ranges of signed values OR floating-point values.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="((-?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|(-?\d+(\.\d+)?([eE]-?\d+)?))(( -?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|( -?\d+(\.\d+)?([eE]-?\d+)?))*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="TokenParameterT">
		<xsd:attribute name="Value" type="xsd:token" use="required"/>
	</xsd:complexType>
	<xsd:simpleType name="TokenListT">
		<xsd:annotation>
			<xsd:documentation>Base type for a list of tokens which may be empty.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9a-zA-Z_]+;)*([0-9a-zA-Z_]+)?"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TokenList1T">
		<xsd:annotation>
			<xsd:documentation>Base type for a list of tokens which may not be empty.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9a-zA-Z_]+;)*[0-9a-zA-Z_]+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="TokenList1exT">
		<xsd:annotation>
			<xsd:documentation>Base type for a list of tokens which may not be empty, with extended char range.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="([0-9a-zA-Z_ ()]+;)*[0-9a-zA-Z_ ()]+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--_________________________________________________________-->
	<!--*** Data Types for special purposes ***-->
	<xsd:simpleType name="VersionStringT">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="V\d+\.\d+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Unsigned32hex0T">
		<xsd:restriction base="base:Unsigned32hexT">
			<xsd:pattern value=".*[1-9a-fA-F].*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--_________________________________________________________-->
	<!--*** Enumerations ***-->
	<xsd:simpleType name="ProfileClassID_EnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="AIP"/>
			<xsd:enumeration value="Process"/>
			<xsd:enumeration value="InformationExchange"/>
			<xsd:enumeration value="Resource"/>
			<xsd:enumeration value="Device"/>
			<xsd:enumeration value="CommunicationNetwork"/>
			<xsd:enumeration value="Equipment"/>
			<xsd:enumeration value="Human"/>
			<xsd:enumeration value="Material"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IASInterfaceEnumT">
		<xsd:union>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:enumeration value="CSI"/>
					<xsd:enumeration value="HCI"/>
					<xsd:enumeration value="ISI"/>
					<xsd:enumeration value="API"/>
					<xsd:enumeration value="CMI"/>
					<xsd:enumeration value="ESI"/>
					<xsd:enumeration value="FSI"/>
					<xsd:enumeration value="MTI"/>
					<xsd:enumeration value="SEI"/>
					<xsd:enumeration value="USI"/>
				</xsd:restriction>
			</xsd:simpleType>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:length value="4"/>
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:union>
	</xsd:simpleType>
	<xsd:simpleType name="FamilyEnumT">
		<xsd:annotation>
			<xsd:documentation>Defines the possible settings for Family/MainFamily.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="General"/>
			<xsd:enumeration value="Drives"/>
			<xsd:enumeration value="Switching Devices"/>
			<xsd:enumeration value="I/O"/>
			<xsd:enumeration value="Valves"/>
			<xsd:enumeration value="Controllers"/>
			<xsd:enumeration value="HMI"/>
			<xsd:enumeration value="Encoders"/>
			<xsd:enumeration value="NC/RC"/>
			<xsd:enumeration value="Gateway"/>
			<xsd:enumeration value="PLCs"/>
			<xsd:enumeration value="Ident Systems"/>
			<xsd:enumeration value="PA Profiles"/>
			<xsd:enumeration value="Network Components"/>
			<xsd:enumeration value="Sensors"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DataItemTypeEnumT">
		<xsd:annotation>
			<xsd:documentation>Defines the possible data types for DataItems.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Integer8"/>
			<xsd:enumeration value="Integer16"/>
			<xsd:enumeration value="Integer32"/>
			<xsd:enumeration value="Integer64"/>
			<xsd:enumeration value="Unsigned8"/>
			<xsd:enumeration value="Unsigned16"/>
			<xsd:enumeration value="Unsigned32"/>
			<xsd:enumeration value="Unsigned64"/>
			<xsd:enumeration value="Float32"/>
			<xsd:enumeration value="Float64"/>
			<xsd:enumeration value="Date"/>
			<xsd:enumeration value="TimeOfDay with date indication"/>
			<xsd:enumeration value="TimeOfDay without date indication"/>
			<xsd:enumeration value="TimeDifference with date indication"/>
			<xsd:enumeration value="TimeDifference without date indication"/>
			<xsd:enumeration value="NetworkTime"/>
			<xsd:enumeration value="NetworkTimeDifference"/>
			<xsd:enumeration value="VisibleString"/>
			<xsd:enumeration value="OctetString"/>
			<xsd:enumeration value="Unsigned8+Unsigned8"/>
			<xsd:enumeration value="Float32+Unsigned8"/>
			<xsd:enumeration value="Float32+Status8"/> <!--obsolete, superseded by "Float32+Unsigned8"-->
			<xsd:enumeration value="F_MessageTrailer4Byte"/>
			<xsd:enumeration value="F_MessageTrailer5Byte"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RefTypeEnumT">
		<xsd:annotation>
			<xsd:documentation>Defines the possible data types for references to ValueItems (in ParameterRecordDataItem) and references to diagnosis data (in (Profile)UnitDiagTypeItem).</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Bit"/>
			<xsd:enumeration value="BitArea"/>
			<xsd:enumeration value="Integer8"/>
			<xsd:enumeration value="Integer16"/>
			<xsd:enumeration value="Integer32"/>
			<xsd:enumeration value="Integer64"/>
			<xsd:enumeration value="Unsigned8"/>
			<xsd:enumeration value="Unsigned16"/>
			<xsd:enumeration value="Unsigned32"/>
			<xsd:enumeration value="Unsigned64"/>
			<xsd:enumeration value="Float32"/>
			<xsd:enumeration value="Float64"/>
			<xsd:enumeration value="Date"/>
			<xsd:enumeration value="TimeOfDay with date indication"/>
			<xsd:enumeration value="TimeOfDay without date indication"/>
			<xsd:enumeration value="TimeDifference with date indication"/>
			<xsd:enumeration value="TimeDifference without date indication"/>
			<xsd:enumeration value="NetworkTime"/>
			<xsd:enumeration value="NetworkTimeDifference"/>
			<xsd:enumeration value="VisibleString"/>
			<xsd:enumeration value="OctetString"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ExtChannelAddValueDataItemTypeEnumT">
		<xsd:annotation>
			<xsd:documentation>Defines the possible data types for ExtChannelAddValues.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Integer8"/>
			<xsd:enumeration value="Integer16"/>
			<xsd:enumeration value="Integer32"/>
			<xsd:enumeration value="Unsigned8"/>
			<xsd:enumeration value="Unsigned16"/>
			<xsd:enumeration value="Unsigned32"/>
			<xsd:enumeration value="Float32"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="GraphicsTypeEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="DeviceSymbol"/>
			<xsd:enumeration value="DeviceIcon"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="IODataConsistencyEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Item consistency"/>
			<xsd:enumeration value="All items consistency"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="FragmentationTypeEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Dynamic"/>
			<xsd:enumeration value="Static"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SRDeviceTypeEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="S2"/>
			<xsd:enumeration value="R2"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="MAUTypeEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="100BASETXFD"/>
			<xsd:enumeration value="100BASEFXFD"/>
			<xsd:enumeration value="1000BASEXFD"/>
			<xsd:enumeration value="1000BASELXFD"/>
			<xsd:enumeration value="1000BASESXFD"/>
			<xsd:enumeration value="1000BASETFD"/>
			<xsd:enumeration value="10GigBASEFX"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="RT_ClassEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Class1"/>
			<xsd:enumeration value="Class2"/>
			<xsd:enumeration value="Class3"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="SyncRoleEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="SyncMaster"/>
			<xsd:enumeration value="SyncMasterRedundant"/>
			<xsd:enumeration value="SyncSlave"/>
			<xsd:enumeration value="SyncMaster+SyncSlave"/>
			<xsd:enumeration value="SyncMasterRedundant+SyncSlave"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="F_Check_iParEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Check"/>
			<xsd:enumeration value="NoCheck"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="F_SIL_EnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="SIL1"/>
			<xsd:enumeration value="SIL2"/>
			<xsd:enumeration value="SIL3"/>
			<xsd:enumeration value="NoSIL"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="F_CRC_LengthEnumT">
		<xsd:restriction base="xsd:string">
			<!-- "2-Byte-CRC" can only be used with F_Par_Version = 0 ("V1-mode") which isn't supported with PROFINET -->
			<xsd:enumeration value="3-Byte-CRC"/>
			<xsd:enumeration value="4-Byte-CRC"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="F_CRC_SeedEnumT">
		<xsd:restriction base="xsd:string">
			<!-- "CRC-Seed16" can only be used with PROFIsafe V2.4, but in this case F_CRC_Seed and F_Passivation are not present -->
			<xsd:enumeration value="CRC-Seed24/32"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="F_PassivationEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Device/Module"/>
			<xsd:enumeration value="Channel"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="LinkStateDiagnosisEnumT">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Up"/>
			<xsd:enumeration value="Down"/>
			<xsd:enumeration value="Up+Down"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
