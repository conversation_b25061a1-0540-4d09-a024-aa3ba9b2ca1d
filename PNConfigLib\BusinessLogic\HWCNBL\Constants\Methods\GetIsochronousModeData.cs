/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: GetIsochronousModeData.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.Methods
{
    /// <summary>
    /// Constants and types for the generic method: GetPdSyncDataParameters. 
    /// </summary>
    internal static class GetIsochronousModeData
    {
        /// <summary>
        /// Output parameter for the returned data block.
        /// </summary>
        public const string IsochronousModeDataBlockEntry = "IsochronousModeDataBlockEntry";

        /// <summary>
        /// Constant for sub-module/module argument
        /// </summary>
        public const string ModuleParameter = "Module";

        /// <summary>
        /// Name of the method.
        /// </summary>
        public const string Name = "GetIsochronousModeData";

        /// <summary>
        /// Determines which type of 0x8030 block should be generated
        /// </summary>
        public const string PNIsoDataModel = "PNIsoDataModel";
    }
}