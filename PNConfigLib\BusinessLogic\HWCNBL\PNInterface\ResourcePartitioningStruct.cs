/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ResourcePartitioningStruct.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Diagnostics;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    /// <summary>
    /// This class represents a ResourcePartitioningBlock.
    /// </summary>
    internal class ResourcePartitioningStruct
    {
        private readonly int readIndexBase = 4;

        public ResourcePartitioningStruct()
        {
            ToByteArray = new byte[32];
        }

        public void SetBlockLength(int blockLength)
        {
            WriteBuffer16(ToByteArray, 2, blockLength);
        }

        public void SetCbaApplicationId(int cbaApplicationId)
        {
            int ioApplicationPhaseLength = ReadBuffer16(ToByteArray, 10);
            WriteBuffer16(ToByteArray, 12 + ioApplicationPhaseLength * 2, cbaApplicationId);
        }

        public void SetCbaApplicationPhases(int[] cbaApplicationBases)
        {
            int ioApplicationPhaseLength = ReadBuffer16(ToByteArray, 10);
            int cbaApplicationPhaseLength = 0;
            if (null != cbaApplicationBases)
            {
                cbaApplicationPhaseLength = cbaApplicationBases.Length;
            }

            WriteBuffer16(ToByteArray, 14 + ioApplicationPhaseLength * 2, cbaApplicationPhaseLength);
            for (int i = 0; i < cbaApplicationPhaseLength; i++)
            {
                if (cbaApplicationBases != null)
                {
                    WriteBuffer16(
                        ToByteArray,
                        14 + ioApplicationPhaseLength * 2 + 2 * (i + 1),
                        cbaApplicationBases[i]);
                }
            }
        }

        public void SetIOApplicationId(int ioApplicationId)
        {
            WriteBuffer16(ToByteArray, 8, ioApplicationId);
        }

        public int[] IOApplicationPhases
        {
            set
            {
                int ioApplicationPhaseLength = 0;
                if (null != value)
                {
                    ioApplicationPhaseLength = value.Length;
                }
                WriteBuffer16(ToByteArray, 10, ioApplicationPhaseLength);
                for (int i = 0; i < ioApplicationPhaseLength; i++)
                {
                    if (value != null)
                    {
                        WriteBuffer16(ToByteArray, 10 + ((i + 1) << 1), value[i]);
                    }
                }
            }
            get
            {
                int ioApplicationPhaseLength = ReadBuffer16(ToByteArray, readIndexBase + 6);
                int[] retValue = new int[ioApplicationPhaseLength];
                for (int i = 0; i < ioApplicationPhaseLength; i++)
                {
                    retValue[i] = ReadBuffer16(ToByteArray, readIndexBase + 6 + ((i + 1) << 1));
                }
                return retValue;
            }
        }

        public void SetNumberOfApplications(int numberOfApplications)
        {
            WriteBuffer16(ToByteArray, 6, numberOfApplications);
        }

        public void SetParameterBlockId(int parameterBlockId)
        {
            WriteBuffer16(ToByteArray, 0, parameterBlockId);
        }

        /// <summary>
        /// WARNING: Can store send clock or send clock factor depending on the
        /// value of the SendClockFormat. If SendClockFormat is 0, it stores the send clock,
        /// else send clock factor.
        /// </summary>
        public int SendClock
        {
            set { WriteBitfieldBased16(ToByteArray, 4, 0, 15, value); }
            get { return ReadBitfieldBased16(ToByteArray, readIndexBase, 0, 15); }
        }

        public int SendClockFormat
        {
            set { WriteBitfieldBased16(ToByteArray, 4, 15, 1, value); }
            get { return ReadBitfieldBased16(ToByteArray, readIndexBase, 15, 1); }
        }

        public byte[] ToByteArray { get; }

        private int ReadBitfieldBased16(byte[] arr, int byteOffset, int bitOffset, int bitLength)
        {
            int mask = 1 << bitLength;
            mask -= 1;
            mask <<= bitOffset;

            int val = ReadBuffer16(arr, byteOffset);
            val &= mask;
            val >>= bitOffset;

            return val;
        }

        private int ReadBuffer16(byte[] arr, int ByteOffset)
        {
            int HI = arr[ByteOffset];
            int LO = arr[ByteOffset + 1];
            return (LO << 8) + HI;
        }

        private void WriteBitfieldBased16(byte[] arr, int byteOffset, int bitOffset, int bitLength, int val)
        {
            Debug.Assert((bitOffset >= 0) && (bitOffset <= 15));
            Debug.Assert(byteOffset < arr.Length - 1);

            int mask = (1 << bitLength) - 1;

            Debug.Assert((val & ~mask) == 0);

            mask <<= bitOffset;
            val <<= bitOffset;
            val &= mask;
            int nValue = ReadBuffer16(arr, byteOffset);
            nValue &= ~mask;
            nValue |= val;
            WriteBuffer16(arr, byteOffset, nValue);
        }

        private void WriteBuffer16(byte[] arr, int ByteOffset, int val)
        {
            Debug.Assert((val & 0xFFFF0000) == 0);
            Debug.Assert(ByteOffset < arr.Length - 1);

            byte LO = (byte)(val & 0x00FF);
            byte HI = (byte)((val & 0x0FF00) >> 8);
            arr[ByteOffset] = LO;
            arr[ByteOffset + 1] = HI;
        }
    }
}