/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: CompileUtility.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.Compiler.DataTypes;
using PNConfigLib.Compiler.DataTypes.Interfaces;
using PNConfigLib.Compiler.DataTypes.Variable;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.ConfigParser.DeviceConfig;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;

using ValueType = PNConfigLib.Compiler.DataTypes.Variable.ValueType;

#endregion

namespace PNConfigLib.Compiler
{
    internal static class CompileUtility
    {
        private static Interface s_ControllerInterface;

        public static CompositeVariable GenerateNetworkParamConfig(DecentralDevice decentralDevice)
        {
            Interface deviceInterface = decentralDevice.GetInterface();
            CompositeVariable networkParamConfig = new CompositeVariable(CompilerConstants.AttributeId.NetworkParamConfig);

            uint basicPackageVersion =
                s_ControllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnBasicPackageVersion,
                    new AttributeAccessCode(),
                    0);
            bool alignment = basicPackageVersion >= 0x0502;

            Dictionary<uint, byte[]> networkParamsLookup = GetDecentralNetworkParameters(
                deviceInterface.Node,
                true,
                alignment);
            if ((networkParamsLookup != null)
                && (networkParamsLookup.Keys.Count > 0))
            {
                networkParamsLookup.Keys.ToList()
                    .ForEach(
                        dataRecordIndex =>
                        networkParamConfig.AddField(new Field(dataRecordIndex, networkParamsLookup[dataRecordIndex])));
            }
            return networkParamConfig;
        }

        public static CompositeVariable GetDataRecordsCentral(PclObject pclObject)
        {
            CompositeVariable dataRecordsConf = new CompositeVariable(CompilerConstants.AttributeId.DataRecordsConf);

            Interface centralInterface = pclObject as Interface;
            if (centralInterface != null)
            {
                s_ControllerInterface = centralInterface;
                dataRecordsConf = GenerateDataRecordsCentralInterface(centralInterface);
            }

            else
            {
                Port portSubmodule = pclObject as Port;
                if (portSubmodule != null)
                {
                    dataRecordsConf = GenerateDataRecordsPortSubmodule(portSubmodule);
                }
            }
            return dataRecordsConf;
        }

        public static CompositeVariable GetDataRecordsConfCentral(DataModel.PCLObjects.CentralDevice centralDevice)
        {
            CompositeVariable dataRecordsConfigCpu =
                new CompositeVariable(CompilerConstants.AttributeId.DataRecordsConfCpu);
            Field expectedConfig = GetExpectedConfig(centralDevice);
            dataRecordsConfigCpu.AddField(expectedConfig);

            Field snmp = GetSnmpControlRecord(centralDevice);
            dataRecordsConfigCpu.AddField(snmp);

            return dataRecordsConfigCpu;
        }

        public static CompositeVariable GetDataRecordsConfDecentral(PclObject objectToCompile)
        {
            CompositeVariable dataRecordsConf = new CompositeVariable(CompilerConstants.AttributeId.DataRecordsConf);
            MethodData methodData = new MethodData();
            methodData.Name = GetDataRecordsConf.Name;
            methodData.Arguments[GetDataRecordsConf.Module] = objectToCompile;
            methodData.Arguments[GetDataRecordsConf.ioControllerInterface] = s_ControllerInterface;

            if (objectToCompile is Interface)
            {
                objectToCompile.BaseActions.CallMethod(methodData);
            }
            else if (objectToCompile is Port)
            {
                objectToCompile.GetInterface().BaseActions.CallMethod(methodData);
            }
            else
            {
                if (objectToCompile != null)
                {
                    DecentralDevice iod = (DecentralDevice)objectToCompile.GetDevice();
                    iod.GetInterface().BaseActions.CallMethod(methodData);
                }
            }

            byte[] dataRecordBlock = methodData.ReturnValue as byte[];

            if ((dataRecordBlock == null)
                || (dataRecordBlock.Length == 0))
            {
                return dataRecordsConf;
            }

            ParameterDatenblock pd = new ParameterDatenblock(dataRecordBlock, 0, dataRecordBlock.Length, 256);
            foreach (IDataset dataset1 in pd.Datasets)
            {
                Dataset dataset = (Dataset)dataset1;
                byte[] dataRecord = new byte[dataset.ParaDS_Len];
                Array.Copy(dataset.Data, dataset.BlockOffset + 8, dataRecord, 0, dataset.ParaDS_Len);
                dataRecordsConf.AddField(new Field(dataset.ParaDS_Nr, dataRecord));
            }
            return dataRecordsConf;
        }

        public static CompositeVariable GetDataRecordsTransferSequenceDecentral(PclObject objectToCompile)
        {
            CompositeVariable dataRecordsTransferSequence =
                new CompositeVariable(CompilerConstants.AttributeId.DataRecordsTransferSequence) { DataType = DataType.Scalar };
            byte[] dataRecordValue = GetDataRecordsTransferSequence(objectToCompile, s_ControllerInterface);

            if (dataRecordValue != null)
            {
                dataRecordsTransferSequence.Value = dataRecordValue;
                dataRecordsTransferSequence.Length = (uint)dataRecordValue.Length;
            }

            return dataRecordsTransferSequence;
        }

        public static IVariable GetDeactivatedConfig(DecentralDevice ioDevice)
        {
            bool deactivated =
                MachineTailorUtility.IsMachineTailoringEnabledForIoDeviceInterface(
                    ioDevice.GetInterface());

            return new SimpleVariable<bool>(CompilerConstants.AttributeId.DeactivatedConfig, deactivated);
        }

        public static CompositeVariable GetIoDevParamConfig(DecentralDevice ioDevice) 
        {
            CompositeVariable ioDevParamConfig = new CompositeVariable(CompilerConstants.AttributeId.IODevParamConfig);

            Interface controllerInterface =
                NavigationUtilities.GetControllerInterfaceOfDeviceInterface(ioDevice.GetInterface());

            MethodData methodData = new MethodData();
            methodData.Name = GetIoDevParam.Name;
            methodData.Arguments[GetDataRecordsConf.ioControllerInterface] = controllerInterface;

            ioDevice.GetInterface().BaseActions.CallMethod(methodData);

            ParameterDataBlockStruct parameterDataset = methodData.ReturnValue as ParameterDataBlockStruct;

            if (parameterDataset == null)
            {
                return null;
            }

            byte[] dataRecordBlock = parameterDataset.ToByteArray;
            ParameterDatenblock pd = new ParameterDatenblock(
                parameterDataset.ToByteArray,
                0,
                dataRecordBlock.Length,
                256);

            foreach (IDataset dataset1 in pd.Datasets)
            {
                Dataset dataset = (Dataset)dataset1;
                byte[] dataRecord = new byte[dataset.ParaDS_Len];
                Array.Copy(dataset.Data, dataset.BlockOffset + 8, dataRecord, 0, dataset.ParaDS_Len);
                ioDevParamConfig.AddField(new Field(dataset.ParaDS_Nr, dataRecord));
            }

            Field isochronData = GetIsochronModeDataDecentral(ioDevice.GetInterface());
            ioDevParamConfig.AddField(isochronData);

            return ioDevParamConfig;
        }

        public static CompositeVariable GetIoMapping(Submodule ioSubmodule)
        {
            if (ioSubmodule == null)
            {
                return null;
            }

            CompositeVariable ioMapping = new CompositeVariable(CompilerConstants.AttributeId.IOmapping);
            ioMapping.DataType = DataType.Scalar;
            ioMapping.ValueType = ValueType.STRUCT;
            ioMapping.HasHexadecimalValues = false;
            AttributeAccessCode accessCode = new AttributeAccessCode();

            int inputStartAddress = ioSubmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.InputStartAddress, accessCode, 0);
            ioMapping.AddElement(
                new Element(CompilerConstants.AttributeId.Ibase, inputStartAddress)
                    {
                        DataType = DataType.Scalar,
                        ValueType = ValueType.UINT32
                    });

            int inputLength = ioSubmodule.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.InAddressRange,
                accessCode.GetNew(),
                0);
            ioMapping.AddElement(
                new Element(CompilerConstants.AttributeId.Ilength, inputLength)
                    {
                        DataType = DataType.Scalar,
                        ValueType = ValueType.UINT16
                    });

            int outputStartAddress = ioSubmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.OutputStartAddress, accessCode, 0);
            ioMapping.AddElement(
                new Element(CompilerConstants.AttributeId.Qbase, outputStartAddress)
                    {
                        DataType = DataType.Scalar,
                        ValueType = ValueType.UINT32
                    });

            int outputLength = ioSubmodule.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.OutAddressRange,
                accessCode.GetNew(),
                0);
            ioMapping.AddElement(
                new Element(CompilerConstants.AttributeId.Qlength, outputLength)
                    {
                        DataType = DataType.Scalar,
                        ValueType = ValueType.UINT16
                    });

            if ((inputLength == 0)
                && (outputLength == 0))
            {
                return null;
            }

            return ioMapping;
        }

        public static CompositeVariable GetIoSysParamConfig(IOSystem decentraIOSystem)
        {
            CompositeVariable ioSysParamConfig = new CompositeVariable(CompilerConstants.AttributeId.IOsysParamConfig);
            Interface controllerInterface = (Interface)decentraIOSystem.PNIOC.ParentObject;

            if (controllerInterface == null)
            {
                return ioSysParamConfig;
            }

            Field controllerProperties = GetControllerProperties(controllerInterface);
            ioSysParamConfig.AddField(controllerProperties);

            return ioSysParamConfig;
        }

        public static Key GetKey(PclObject pclObject, CompilerConstants.Keys key)
        {
            int value = 0;
            AttributeAccessCode ac = new AttributeAccessCode();
            switch (key)
            {
                //Slot number is filled with position number for Module Lean.
                case CompilerConstants.Keys.SlotNumber:
                    value = pclObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PositionNumber,
                        ac.GetNew(),
                        0);
                    break;
                case CompilerConstants.Keys.SubslotNumber:
                    value = pclObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnSubslotNumber,
                        ac.GetNew(),
                        0);
                    break;
                case CompilerConstants.Keys.StationNumber:
                    value = pclObject.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnStationNumber,
                        ac.GetNew(),
                        0);
                    break;
            }

            return new Key((int)key, value);
        }

        public static IVariable GetLaddr(PclObject pclObject)
        {
            SimpleVariable<ushort> laddr = pclObject?.LAddress == null
                                               ? new SimpleVariable<ushort>(CompilerConstants.AttributeId.LADDR, 0)
                                               : new SimpleVariable<ushort>(
                                                     CompilerConstants.AttributeId.LADDR,
                                                     (ushort)pclObject.LAddress.LAddressValue);
            laddr.ValueType = ValueType.UINT16;
            laddr.DataType = DataType.Scalar;

            return laddr;
        }

        public static Link GetLink(Interface interfaceSubmodule)
        {
            uint runtimeId = GetIOSystemRuntimeId(interfaceSubmodule.PNIOC.IOSystem);
            return new Link(CompilerConstants.AttributeId.Link, runtimeId);
        }

        public static Rid GetRID(IOSystem ioSystem)
        {
            return new Rid(GetIOSystemRuntimeId(ioSystem));
        }

        private static Dictionary<uint, byte[]> GetDecentralNetworkParameters(
            Node nodeObject,
            bool isDecentral,
            bool alignment)
        {
            MethodData methodData = new MethodData();
            methodData.Name = HWCNBL.Constants.Methods.GetNetworkParameter.Name;

            methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.Alignment] = alignment;
            methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.IsDecentral] = isDecentral;
            nodeObject.BaseActions.CallMethod(methodData);

            Dictionary<uint, byte[]> returnValue = (Dictionary<uint, byte[]>)methodData.ReturnValue;
            return returnValue;
        }

        public static bool CheckIfCreatePdevSubmodules(Interface interfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            // Is PNIoSubmoduleModelSupp PDEV supported
            bool pdevModelSupp = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoSubmoduleModelSupp, ac, false);
            if (!pdevModelSupp)
            {
                return false;
            }

            // Is PDEV decentral activated
            bool pnParameterizationDisallowed = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnParameterizationDisallowed, ac.GetNew(), false);
            if (pnParameterizationDisallowed)
            {
                return false;
            }

            // "SharedIoAssignment" == true means that this submodule is assigned to another controller
            // and must therefore not be created for the current controller
            if (interfaceSubmodule.AttributeAccess
                .GetAnyAttribute<uint>(InternalAttributeNames.SharedIoAssignment, new AttributeAccessCode(), 0) == 1)
            {
                return false;
            }

            return true;
        }

        #region Private Implementation

        internal static byte[] GetDataRecordsTransferSequence(
            PclObject configObject,
            Interface controllerInterfaceSubmodule)
        {
            #region Check parameter for null

            if (configObject == null)
            {
                return null;
            }
            if (controllerInterfaceSubmodule == null)
            {
                throw new ArgumentNullException("controllerInterfaceSubmodule");
            }

            #endregion

            List<byte> byteList = new List<byte>();

            string dataRecordsTransferSequence =
                configObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.DataRecordTransferSequence,
                    new AttributeAccessCode(),
                    string.Empty);
            if (string.IsNullOrEmpty(dataRecordsTransferSequence))
            {
                return null;
            }

            bool supported =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDataRecordsTransferSeqSupp,
                    new AttributeAccessCode(),
                    false);
            if (!supported)
            {
                return null;
            }

            foreach (string dataRecordIndex in dataRecordsTransferSequence.Split(';'))
            {
                uint indexUInt32;
                if (!uint.TryParse(
                        dataRecordIndex,
                        NumberStyles.Integer,
                        CultureInfo.InvariantCulture,
                        out indexUInt32))
                {
                    continue;
                }

                // Convert UInt32 to 4 byte Big Endian
                for (int offset = 24; offset >= 0; offset -= 8)
                {
                    byteList.Add((byte)((indexUInt32 & (0xFF << offset)) >> offset));
                }
            }

            return byteList.ToArray();
        }

        private static CompositeVariable GenerateDataRecordsCentralInterface(Interface centralInterfaceSubmodule)
        {
            CompositeVariable dataRecordsConf = new CompositeVariable(CompilerConstants.AttributeId.DataRecordsConf);

            uint basicPackageVersion =
                s_ControllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnBasicPackageVersion,
                    new AttributeAccessCode(),
                    0);
            bool alignment = basicPackageVersion >= 0x0502;

            //IpAddressValidationLocal data block.
            Field field = GetNetworkParameter(
                DataRecords.Indexes.IpAddressValidationLocal,
                centralInterfaceSubmodule.Node,
                alignment,
                0x101);
            dataRecordsConf.AddField(field);

            //IPSuite data block.
            field = GetNetworkParameter(DataRecords.Indexes.IpV4Suite, centralInterfaceSubmodule.Node, alignment);
            dataRecordsConf.AddField(field);

            //NameOfStationValidation data block
            field = GetNetworkParameter(
                DataRecords.Indexes.NameOfStationValidation,
                centralInterfaceSubmodule.Node,
                alignment,
                0x101);
            dataRecordsConf.AddField(field);

            //NameOfStation data block
            field = GetNetworkParameter(DataRecords.Indexes.NameOfStation, centralInterfaceSubmodule.Node, alignment);
            dataRecordsConf.AddField(field);

            //PDSyncData data block
            field = GetPdSyncDataCentral(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            //IR Data block
            field = GetPDIRDataCentral(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            //PDIRSubframeData
            field = GetPDIRSubFrameData(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            field = GetSendClockParameterBlock(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            field = GetPDInterfaceAdjust(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            field = GetBlockIdentifications(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            //Isochron
            field = GetIsochronModeData(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            field = GetPDMasterTailorData(centralInterfaceSubmodule);
            dataRecordsConf.AddField(field);

            return dataRecordsConf;
        }

        private static CompositeVariable GenerateDataRecordsPortSubmodule(Port centralPortSubmodule)
        {
            CompositeVariable dataRecordsConf = new CompositeVariable(CompilerConstants.AttributeId.DataRecordsConf);

            Field pdPortDataAdjustField = GetPDPortDataAdjustParameterBlock(
                centralPortSubmodule,
                PNInterfaceType.IOController,
                CompilerConstants.PDPortDataAdjustIndex);
            dataRecordsConf.AddField(pdPortDataAdjustField);

            Field pdPortDataCheckField = GetPDPortDataCheckParameterBlock(
                centralPortSubmodule,
                PNInterfaceType.IOController,
                CompilerConstants.PDPortDataCheckIndex);
            dataRecordsConf.AddField(pdPortDataCheckField);

            return dataRecordsConf;
        }

        private static Field GetControllerProperties(Interface controllerInterface)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetIosysParamConfig.Name;

            controllerInterface.BaseActions.CallMethod(methodData);
            return methodData.ReturnValue == null
                       ? null
                       : new Field(DataRecords.Indexes.ControllerProperties, (byte[])methodData.ReturnValue);
        }

        private static Field GetExpectedConfig(DataModel.PCLObjects.CentralDevice centralDevice)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetDataRecordsConfCpu.Name;

            centralDevice.BaseActions.CallMethod(methodData);
            return methodData.ReturnValue == null
                       ? null
                       : new Field(DataRecords.Indexes.ExpectedConfigRecord, (byte[])methodData.ReturnValue);
        }

        private static Field GetSnmpControlRecord(DataModel.PCLObjects.CentralDevice centralDevice)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetSnmpControlRecordName.Name;

            centralDevice.BaseActions.CallMethod(methodData);
            return methodData.ReturnValue == null
                       ? null
                       : new Field(DataRecords.Indexes.SnmpControlRecord, (byte[])methodData.ReturnValue);
        }

        private static uint GetIOSystemRuntimeId(IOSystem ioSystem)
        {
            if (ioSystem.LAddress == null)
            {
                return CompilerConstants.AsRuntimeIdOffsetHwObject;
            }
            return CompilerConstants.AsRuntimeIdOffsetHwObject + (uint)ioSystem.LAddress.LAddressValue;
        }

        #region DataRecordsConf methods

        #region CentralInterface

        private static Field GetNetworkParameter(uint index, Node nodeObject, bool alignment, int? blockVersion = null)
        {
            MethodData methodData = new MethodData();
            methodData.Name = HWCNBL.Constants.Methods.GetNetworkParameter.Name;

            methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.SelectedDataset] = index;
            methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.Alignment] = alignment;
            methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.IsDecentral] = false;
            if (blockVersion != null)
            {
                methodData.Arguments[HWCNBL.Constants.Methods.GetNetworkParameter.Blockversion] = blockVersion;
            }

            nodeObject.BaseActions.CallMethod(methodData);

            byte[] returnValue = (byte[])methodData.ReturnValue;
            return returnValue == null ? null : new Field(index, returnValue);
        }

        private static Field GetPdSyncDataCentral(Interface centralInterface)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetPdSyncDataParameters.Name;
            methodData.Arguments[RecordwithoutHeader.isRecordwithoutHeader] = true;
            centralInterface.BaseActions.CallMethod(methodData);

            byte[] dataBlock = (byte[])methodData.Arguments[GetPdSyncDataParameters.PdSyncDataStructEntry];

            return dataBlock == null ? null : new Field(DataRecords.Indexes.ExpectedPDSyncData, dataBlock);
        }

        private static Field GetPDIRDataCentral(Interface centralInterface)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetConfig2002Pdev1ABlock.Name;
            methodData.Arguments[GetConfig2002Pdev1ABlock.SelectedDataset] = DataRecords.Indexes.PDIRData;
            methodData.Arguments[RecordwithoutHeader.isRecordwithoutHeader] = true;
            centralInterface.BaseActions.CallMethod(methodData);

            byte[] dataBlock = (byte[])methodData.ReturnValue;

            return dataBlock == null ? null : new Field(DataRecords.Indexes.PDIRData, dataBlock);
        }

        private static Field GetPDIRSubFrameData(Interface centralInterfaceSubmodule)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetPdirSubframeData.Name;
            centralInterfaceSubmodule.BaseActions.CallMethod(methodData);

            byte[] returnValue = (byte[])methodData.ReturnValue;
            return returnValue == null ? null :  new Field(DataRecords.Indexes.PdirSubframeData, returnValue);
        }

        private static Field GetSendClockParameterBlock(Interface interfaceSubmodule)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetSendClockParameterBlocks.Name;
            interfaceSubmodule.BaseActions.CallMethod(methodData);

            byte[] returnValue = (byte[])methodData.ReturnValue;
            return new Field(DataRecords.Indexes.SendClock, returnValue);
        }

        private static Field GetBlockIdentifications(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }
            IMethodData methodData = new MethodData();
            methodData.Name = HWCNBL.Constants.Methods.GetBlockIdentifications.Name;

            if (!interfaceSubmodule.BaseActions.CallMethod(methodData)
                || (methodData.ReturnValue is bool && !(bool)methodData.ReturnValue))
            {
                return null;
            }
            byte[] returnValue;
            if (methodData.ReturnValue is byte[])
            {
                returnValue = methodData.ReturnValue as byte[];
            }
            else
            {
                returnValue =
                    methodData.Arguments[
                        HWCNBL.Constants.Methods.GetBlockIdentifications.PNIdentificationBlockEntry]
                    as byte[];
            }
            return new Field(0x00023100, returnValue);
        }

        private static Field GetPDInterfaceAdjust(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }
            MethodData methodData = new MethodData();
            methodData.Name = HWCNBL.Constants.Methods.GetPDInterfaceAdjust.Name;

            if (!interfaceSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            byte[] returnValue = methodData.ReturnValue as byte[];

            return returnValue == null ? null : new Field(DataRecords.Indexes.PDInterfaceAdjust, returnValue);
        }

        private static Field GetIsochronModeData(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }
            MethodData methodData = new MethodData();
            methodData.Name = GetIsochronModeDataBlock.Name;

            if (!interfaceSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            byte[] returnValue = methodData.ReturnValue as byte[];

            return returnValue == null ? null : new Field(DataRecords.Indexes.IsochronModeData, returnValue);
        }
        private static Field GetPDMasterTailorData(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }
            MethodData methodData = new MethodData();
            methodData.Name = GetPdMasterTailorDataStructPlus.Name;

            if (!interfaceSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            byte[] returnValue = methodData.ReturnValue as byte[];

            return returnValue == null ? null : new Field(DataRecords.Indexes.PDMasterTailorData, returnValue);
        }

        private static Field GetIsochronModeDataDecentral(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }
            MethodData methodData = new MethodData();
            methodData.Name = GetIsochronousModeData.Name;

            if (!interfaceSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            ParameterDatasetStruct returnValue = methodData.ReturnValue as ParameterDatasetStruct;

            return returnValue == null ? null : new Field((uint)returnValue.ParaDSNumber, returnValue.ToByteArray);
        }

        #endregion

        #region Port

        private static Field GetPDPortDataCheckParameterBlock(
            Port portSubmodule,
            PNInterfaceType interfaceType,
            uint dataSet)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetPortParameterBlocks.Name;
            methodData.Arguments[GetPortParameterBlocks.SelectedDataset] = dataSet;
            methodData.Arguments[GetPortParameterBlocks.InterfaceType] = interfaceType;
            methodData.Arguments[RecordwithoutHeader.isRecordwithoutHeader] = true;

            if (!portSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            return methodData.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] == null
                       ? null
                       : new Field(dataSet, methodData.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] as byte[]);
        }

        private static Field GetPDPortDataAdjustParameterBlock(
            Port portSubmodule,
            PNInterfaceType interfaceType,
            uint dataSet)
        {
            MethodData methodData = new MethodData();
            methodData.Name = GetPortParameterBlocks.Name;
            methodData.Arguments[GetPortParameterBlocks.SelectedDataset] = dataSet;
            methodData.Arguments[GetPortParameterBlocks.InterfaceType] = interfaceType;
            methodData.Arguments[RecordwithoutHeader.isRecordwithoutHeader] = true;

            if (!portSubmodule.BaseActions.CallMethod(methodData))
            {
                return null;
            }

            return methodData.ReturnValue == null ? null : new Field(dataSet, methodData.ReturnValue as byte[]);
        }

        #endregion

        #endregion

        #endregion
    }
}