/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_033.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.33 and is based on GSD(ML) versions 2.32 and lower.
    ///		
    /// </summary>
    internal class CheckerV02033 : CheckerV02032
    {
        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:Observer/@Type";
                return (xp);
            }
        }

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02033;
        }
        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02033;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00033000);
            Checks.Add(Constants.s_Cn_0X00033001);
            Checks.Add(Constants.s_Cn_0X00033002);
            Checks.Add(Constants.s_Cn_0X00033003);
            Checks.Add(Constants.s_Cn_0X00033005);
            Checks.Add(Constants.s_Cn_0X00033006);
            Checks.Add(Constants.s_Cn_0X00033007);
            Checks.Add(Constants.s_Cn_0X00033009);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                Checks.Remove(Constants.s_Cn_0X00012114);
                Checks.Remove(Constants.s_Cn_0X00030030);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.33.
        /// </summary>
        public CheckerV02033()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version233);
        }

        #endregion

        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("EnergySavingModeItem", "ID");
            ElementDescriptions.Add("MeasurementItem", "Number");
            ElementDescriptions.Add("Observer", "Type");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = AttributeTokenDictionary["SupportedRole"];
            tokens1.Add("Off");

            IList<string> tokens2 = new List<string>();
            tokens2.Add("DigitalInput");
            AttributeTokenDictionary.Add("Type", tokens2);
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00033000
        /// 
        /// <Description>
        /// Starting with GSDML V2.33, the Attribute MediaRedundancy/@SupportedRole now allows the token "Off".
        /// The following must be checked:
        /// (1) If "Off" is present, it must be the first token in the list
        /// (2) If "Off" is present, there must be at least one other token in the list
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033000()
        {
            // SupportedRole => optional
            var supportedRolesNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MediaRedundancy).Attributes(Attributes.s_SupportedRole);
            supportedRolesNodes = Help.TryRemoveXAttributesUnderXsAny(supportedRolesNodes, Nsmgr, Gsd);
            foreach (var supportedRolesNode in supportedRolesNodes)
            {
                var supportedRoles = new List<string>();
                if (!string.IsNullOrEmpty(supportedRolesNode.Value))
                    supportedRoles = new List<string>(supportedRolesNode.Value.Split(Constants.s_Semicolon.ToCharArray()));

                int indexMrOff = supportedRoles.IndexOf(Enums.s_MrOff);
                if (indexMrOff != -1)
                {
                    // (1)
                    if (indexMrOff != 0)
                    {
                        if (Help.CheckSchemaVersion(supportedRolesNode, SupportedGsdmlVersion))
                        {
                            // "If "Off" is present at 'MediaRedundancy/@SupportedRole', it must be the first token in the list."
                            string msg = Help.GetMessageString("M_0x00033000_1");
                            string xpath = Help.GetXPath(supportedRolesNode);
                            var lineInfo = (IXmlLineInfo)supportedRolesNode;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                          msg, xpath, ReportCategories.TypeSpecific, "0x00033000_1");
                        }
                    }

                    // (2)
                    if (supportedRoles.Count == 1)
                    {
                        if (Help.CheckSchemaVersion(supportedRolesNode, SupportedGsdmlVersion))
                        {
                            // "If "Off" is present at 'MediaRedundancy/@SupportedRole', there must be at least one other token in the list."
                            string msg = Help.GetMessageString("M_0x00033000_2");
                            string xpath = Help.GetXPath(supportedRolesNode);
                            var lineInfo = (IXmlLineInfo)supportedRolesNode;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                          msg, xpath, ReportCategories.TypeSpecific, "0x00033000_2");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00033001
        /// 
        /// <Description>
        /// If SynchronisationMode/@SupportedSyncProtocols on InterfaceSubmoduleItem contains the protocol type "PTCP",
        /// the attribute @DelayMeasurementSupported shall be set to "true".
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033001()
        {
            var synchronisationModeNodes =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SynchronisationMode)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_SupportedSyncProtocols) != null);
            synchronisationModeNodes = Help.TryRemoveXElementsUnderXsAny(synchronisationModeNodes, Nsmgr, Gsd);

            foreach (var synchronisationModeNode in synchronisationModeNodes)
            {
                // SupportedSyncProtocols => conditional
                IList<string> supportedSyncProtocols = new List<string>();
                var supportedSyncProtocolsNode = synchronisationModeNode.Attribute(Attributes.s_SupportedSyncProtocols);
                if (supportedSyncProtocolsNode != null && !string.IsNullOrEmpty(supportedSyncProtocolsNode.Value))
                    supportedSyncProtocols = new List<string>(supportedSyncProtocolsNode.Value.Split(Constants.s_Semicolon.ToCharArray()));

                if (!supportedSyncProtocols.Contains("PTCP"))
                    continue;

                // DelayMeasurementSupported  => conditional
                var interfaceSubmoduleItem = synchronisationModeNode.Parent;
                bool delayMeasurementSupported = false;
                if (interfaceSubmoduleItem != null)
                {
                    var delayMeasurementSupportedNode =
                        interfaceSubmoduleItem.Attribute(Attributes.s_DelayMeasurementSupported);
                    if (delayMeasurementSupportedNode != null)
                        delayMeasurementSupported = XmlConvert.ToBoolean(
                            interfaceSubmoduleItem.Attribute(Attributes.s_DelayMeasurementSupported).Value);
                }
                if (delayMeasurementSupported)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(interfaceSubmoduleItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "On 'InterfaceSubmoduleItem' the attribute 'SynchronisationMode/@SupportedSyncProtocols' contains the token "PTCP",
                // but the attribute 'DelayMeasurementSupported' is not set to "true"."
                string msg = Help.GetMessageString("M_0x00033001_1");
                string xpath = Help.GetXPath(synchronisationModeNode.Attribute(Attributes.s_SupportedSyncProtocols));
                var lineInfo = (IXmlLineInfo)synchronisationModeNode.Attribute(Attributes.s_SupportedSyncProtocols);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00033001_1");


            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00033002
        /// 
        /// <Description>
        /// On DeviceAccessPointItem, the attribute NumberOfDeviceAccessAR shall be present if and only if DeviceAccessSupported is present and "true".
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033002()
        {
            var daps =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_DeviceAccessSupported) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strDeviceAccessSupported = Help.GetAttributeValueFromXElement(dap, Attributes.s_DeviceAccessSupported);
                bool deviceAccessSupported = XmlConvert.ToBoolean(strDeviceAccessSupported);
                if (!deviceAccessSupported)
                {
                    continue;
                }

                // DeviceAccessSupported => conditional
                var numberOfDeviceAccessArNode = dap.Attribute(Attributes.s_NumberOfDeviceAccessAr);
                if (numberOfDeviceAccessArNode != null)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "'@DeviceAccessSupported' is present and "true" on 'DeviceAccessPointItem', but '@NumberOfDeviceAccessAR' is not present."
                string msg = Help.GetMessageString("M_0x00033002_1");
                string xpath = Help.GetXPath(dap);
                var lineInfo = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00033002_1");
            }

            daps =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            x.Attribute(Attributes.s_NumberOfDeviceAccessAr) != null);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);

            foreach (var dap in daps)
            {
                // DeviceAccessSupported => must
                var deviceAccessSupportedNode = dap.Attribute(Attributes.s_DeviceAccessSupported);
                bool deviceAccessSupported = deviceAccessSupportedNode != null && XmlConvert.ToBoolean(deviceAccessSupportedNode.Value);
                if (deviceAccessSupported)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "'@NumberOfDeviceAccessAR' is present on 'DeviceAccessPointItem', but '@DeviceAccessSupported' is not "true"."
                string msg = Help.GetMessageString("M_0x00033002_2");
                string xpath = Help.GetXPath(deviceAccessSupportedNode);
                var lineInfo = (IXmlLineInfo)deviceAccessSupportedNode;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00033002_2");
            }

            return true;
        }

        /// <summary>
        /// FindAllUseableSubmodulesOfDap
        /// 
        /// <Description>
        /// Finds all submodules which can be configured with the given DAP.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>A List of submodules which can be configured with the given DAP.</returns>
        protected virtual IList<XElement> FindAllUseableSubmodulesOfDap(XElement dap)
        {
            IList<XElement> useableSubmodulesOfDap = new List<XElement>();

            // (1) Check all VirtualSubmoduleItems at the DAP
            var virtualSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
            if (virtualSubmoduleList != null)
            {
                AddVirtualSubmoduleItemToUsableSubmodules(virtualSubmoduleList, useableSubmodulesOfDap);
            }

            // (2) Check all UseableSubmodules at the DAP
            var useableSubmodules = dap.Element(NamespaceGsdDef + Elements.s_UseableSubmodules);
            if (useableSubmodules != null)
            {
                AddSubmoduleItemRefToUSableSubmodules(useableSubmodules, useableSubmodulesOfDap);
            }

            // (3) Check all VirtualSubmoduleItem of fixed modules at the DAP
            // (4) Check all UseableSubmodules of fixed modules at the DAP
            var useableModules = dap.Element(NamespaceGsdDef + Elements.s_UseableModules);
            if (useableModules == null)
                return useableSubmodulesOfDap;

            AddModuleItemRefToUsableSubmodules(useableModules, useableSubmodulesOfDap);

            return useableSubmodulesOfDap;
        }

        private void AddModuleItemRefToUsableSubmodules(XContainer useableModules, ICollection<XElement> useableSubmodulesOfDap)
        {
            var moduleItemRefs = useableModules.Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);
            foreach (var moduleItemRef in moduleItemRefs)
            {
                string moduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                if (null == module)
                    continue;

                // (3) Check all VirtualSubmoduleItem of fixed modules at the DAP
                var virtualSubmoduleListModule = module.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
                if (virtualSubmoduleListModule != null)
                {
                    var virtualSubmoduleItems =
                        virtualSubmoduleListModule.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                    foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
                    {
                        useableSubmodulesOfDap.Add(virtualSubmoduleItem);
                    }
                }

                // (4) Check all UseableSubmodules of fixed modules at the DAP
                if (ModuleRefToSubmoduleDictionary.TryGetValue(moduleItemRef, out IList<XElement> submodulesOfModule))
                {
                    foreach (XElement submodule in submodulesOfModule)
                    {
                        useableSubmodulesOfDap.Add(submodule);
                    }
                }
            }
        }

        private void AddSubmoduleItemRefToUSableSubmodules(XContainer useableSubmodules, ICollection<XElement> useableSubmodulesOfDap)
        {
            var submoduleItemRefs = useableSubmodules.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                string submoduleTarget = Help.CollapseWhitespace(
                    Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                if (null == submodule)
                    continue;

                if (!useableSubmodulesOfDap.Contains(submodule)) // Only possible for unknown GSDML Schema Version
                    useableSubmodulesOfDap.Add(submodule);
            }
        }

        private void AddVirtualSubmoduleItemToUsableSubmodules(XContainer virtualSubmoduleList, ICollection<XElement> useableSubmodulesOfDap)
        {
            var virtualSubmoduleItems = virtualSubmoduleList.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
            {
                useableSubmodulesOfDap.Add(virtualSubmoduleItem);
            }
        }

        /// <summary>
        /// Check number: CN_0x00033003
        /// 
        /// <Description>
        /// If DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem contains the new element ReportingSystem,
        /// there must be at least one submodule which is configurable with this DAP which contains the new element ReportingSystemEvents.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033003()
        {
            var daps =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
                    .Where(
                        x =>
                            (x.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList) != null) &&
                            (x.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList)
                                .Element(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem) != null) &&
                            (x.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList)
                                .Element(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem)
                                .Element(NamespaceGsdDef + Elements.s_ReportingSystem) != null));
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);

            foreach (var dap in daps)
            {
                bool reportingSystemEventsGiven = false;

                IList<XElement> useableSubmodulesOfDap = FindAllUseableSubmodulesOfDap(dap);

                foreach (var useableSubmodule in useableSubmodulesOfDap)
                {
                    if (useableSubmodule.Element(NamespaceGsdDef + Elements.s_ReportingSystemEvents) != null)
                    {
                        reportingSystemEventsGiven = true;
                        break;
                    }
                }

                if (!reportingSystemEventsGiven)
                {
                    if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                    {
                        // "A DAP with '.../InterfaceSubmoduleItem/ReportingSystem' must have at least one submodule which
                        // is configurable with this DAP which contains the 'ReportingSystemEvents'."
                        string msg = Help.GetMessageString("M_0x00033003_1");
                        string xpath = Help.GetXPath(dap);
                        var xli = (IXmlLineInfo)dap;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00033003_1");
                    }
                }

            } // foreach (XmlElement dap in DAPs)

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00033005
        /// 
        /// <Description>
        /// Check for (Virtual)SubmoduleItem/PROFIenergy:
        /// With severity error:
        /// (1) The attribute EntityClass shall be present if and only if the attribute ProfileVersion is >= "V1.1".
        /// (2) The attribute EntitySubclass shall be present if the attribute ProfileVersion is >= "V1.2" and
        ///     the attribute EntityClass is "Class1" or "Class3". It shall not be present otherwise.
        /// (3) 'PROFIenergy/EnergySavingModeList' must be present if '@EntityClass' is "Class1" or "Class3",
        ///     but must not be present if '@EntityClass' is "Class2".
        /// (4) 'PROFIenergy/MeasurementList' must be present if '@EntityClass' is "Class2" or "Class3",
        ///     but must not be present if '@EntityClass' is "Class1".
        /// (5) The attribute MeasurementList/MeasurementItem/@Number shall be present if and only if the attribute
        ///     ProfileVersion is >= "V1.1".
        /// (6) If ProfileVersion is less than "V1.1" (i.e. == "V1.0"), there shall be only one MeasurementItem per MeasurementList.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033005()
        {
            var itemsProfIenergy = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ProfIenergy);
            itemsProfIenergy = Help.TryRemoveXElementsUnderXsAny(itemsProfIenergy, Nsmgr, Gsd);
            foreach (var itemProfIenergy in itemsProfIenergy)
            {
                // Determine ProfileVersion
                double profileVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(itemProfIenergy, Attributes.s_ProfileVersion));

                // Determine EntityClass
                var entityClassNode = itemProfIenergy.Attribute(Attributes.s_EntityClass);
                string entityClass = string.Empty;
                if (entityClassNode != null)
                    entityClass = entityClassNode.Value;

                // Determine EntitySubclass
                var entitySubclassNode = itemProfIenergy.Attribute(Attributes.s_EntitySubclass);
                string entitySubclass = string.Empty;
                if (entitySubclassNode != null)
                    entitySubclass = entitySubclassNode.Value;

                // Determine EnergySavingModeList
                var energySavingModeListNode = itemProfIenergy.Element(NamespaceGsdDef + Elements.s_EnergySavingModeList);

                // Determine MeasurementList
                var measurementListNode = itemProfIenergy.Element(NamespaceGsdDef + Elements.s_MeasurementList);

                // (1)
                CreateReport0x00033005_1(entityClass, profileVersion, itemProfIenergy);
                CreateReport0x00033005_2(entityClass, profileVersion, itemProfIenergy);

                // (2)
                CreateReport0x00033005_3(entitySubclass, profileVersion, entityClass, itemProfIenergy);
                CreateReport0x00033005_4(entitySubclass, profileVersion, entityClass, itemProfIenergy);

                // (3)
                CreateReport0x00033005_5(energySavingModeListNode, entityClass, itemProfIenergy, entityClassNode);
                CreateReport0x00033005_6(energySavingModeListNode, entityClass, itemProfIenergy);

                // (4)
                CreateReport0x00033005_7(measurementListNode, entityClass, itemProfIenergy, entityClassNode);
                CreateReport0x00033005_8(measurementListNode, entityClass, itemProfIenergy);

                // (5)
                var measurementItems = itemProfIenergy.Descendants(NamespaceGsdDef + Elements.s_MeasurementItem);
                foreach (var measurementItem in measurementItems)
                {
                    var numberNode = measurementItem.Attribute(Attributes.s_Number);
                    CreateReport0x00033005_9(numberNode, profileVersion, itemProfIenergy);
                    CreateReport0x00033005_10(numberNode, profileVersion, itemProfIenergy);
                }

                // (6)
                CreateReport0x00033005_11(profileVersion, measurementItems, itemProfIenergy, measurementListNode);
            }

            return true;
        }

        private void CreateReport0x00033005_11(
            double profileVersion,
            IEnumerable<XElement> measurementItems,
            XObject itemProfIenergy,
            XObject measurementListNode)
        {
            if (!(profileVersion < 1.1)
                || measurementItems.Count() <= 1)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)measurementListNode;
            // "'MeasurementList' must only contain one 'MeasurementItem' if '@ProfileVersion' is < "V1.1"."
            string msg = Help.GetMessageString("M_0x00033005_11");
            string xpath = Help.GetXPath(measurementListNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_11");
        }

        private void CreateReport0x00033005_10(XAttribute numberNode, double profileVersion, XElement itemProfIenergy)
        {
            if (numberNode == null
                || !(profileVersion < 1.1))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var profileVersionNode = itemProfIenergy.Attribute(Attributes.s_ProfileVersion);
            var lineInfo = (IXmlLineInfo)profileVersionNode;
            // "'MeasurementItem/@Number' must not be present if '@ProfileVersion' is < "V1.1"."
            string msg = Help.GetMessageString("M_0x00033005_10");
            string xpath = Help.GetXPath(profileVersionNode);
            if (lineInfo != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033005_10");
            }
        }

        private void CreateReport0x00033005_9(XAttribute numberNode, double profileVersion, XElement itemProfIenergy)
        {
            if (numberNode != null
                || !(profileVersion >= 1.1))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var profileVersionNode = itemProfIenergy.Attribute(Attributes.s_ProfileVersion);
            var lineInfo = (IXmlLineInfo)profileVersionNode;
            // "'MeasurementItem/@Number' must be present if '@ProfileVersion' is >= "V1.1"."
            string msg = Help.GetMessageString("M_0x00033005_9");
            string xpath = Help.GetXPath(profileVersionNode);
            if (lineInfo != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033005_9");
            }
        }

        private void CreateReport0x00033005_8(XObject measurementListNode, string entityClass, XObject itemProfIenergy)
        {
            if (measurementListNode == null
                || entityClass != "Class1")
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)measurementListNode;
            // "'PROFIenergy/MeasurementList' must not be present if '@EntityClass' is "Class1"."
            string msg = Help.GetMessageString("M_0x00033005_8");
            string xpath = Help.GetXPath(measurementListNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_8");
        }

        private void CreateReport0x00033005_7(
            XElement measurementListNode,
            string entityClass,
            XObject itemProfIenergy,
            XObject entityClassNode)
        {
            if (measurementListNode != null
                || (entityClass != "Class2" && entityClass != "Class3"))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)entityClassNode;
            // "'PROFIenergy/MeasurementList' must be present if '@EntityClass' is "Class2" or "Class3"."
            string msg = Help.GetMessageString("M_0x00033005_7");
            string xpath = Help.GetXPath(entityClassNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_7");
        }

        private void CreateReport0x00033005_6(XObject energySavingModeListNode, string entityClass, XObject itemProfIenergy)
        {
            if (energySavingModeListNode == null
                || entityClass != "Class2")
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)energySavingModeListNode;
            // "'PROFIenergy/EnergySavingModeList' must not be present if '@EntityClass' is "Class2"."
            string msg = Help.GetMessageString("M_0x00033005_6");
            string xpath = Help.GetXPath(energySavingModeListNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_6");
        }

        private void CreateReport0x00033005_5(
            XElement energySavingModeListNode,
            string entityClass,
            XObject itemProfIenergy,
            XObject entityClassNode)
        {
            if (energySavingModeListNode != null
                || (entityClass != "Class1" && entityClass != "Class3"))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)entityClassNode;
            // "'PROFIenergy/EnergySavingModeList' must be present if '@EntityClass' is "Class1" or "Class3"."
            string msg = Help.GetMessageString("M_0x00033005_5");
            string xpath = Help.GetXPath(entityClassNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_5");
        }

        private void CreateReport0x00033005_4(
            string entitySubclass,
            double profileVersion,
            string entityClass,
            XObject itemProfIenergy)
        {
            if (string.IsNullOrEmpty(entitySubclass)
                || (!(profileVersion < 1.2) && (entityClass == "Class1" || entityClass == "Class3")))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)itemProfIenergy;
            // "'PROFIenergy/@EntitySubclass' must not be present if '@ProfileVersion' is < "V1.2" or '@EntityClass' is neither "Class1" nor "Class3"."
            string msg = Help.GetMessageString("M_0x00033005_4");
            string xpath = Help.GetXPath(itemProfIenergy);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_4");
        }

        private void CreateReport0x00033005_3(
            string entitySubclass,
            double profileVersion,
            string entityClass,
            XObject itemProfIenergy)
        {
            if (!string.IsNullOrEmpty(entitySubclass)
                || (!(profileVersion >= 1.2) || (entityClass != "Class1" && entityClass != "Class3")))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)itemProfIenergy;
            // "'PROFIenergy/@EntitySubclass' must be present if '@ProfileVersion' is >= "V1.2" and '@EntityClass' is "Class1" or "Class3"."
            string msg = Help.GetMessageString("M_0x00033005_3");
            string xpath = Help.GetXPath(itemProfIenergy);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033005_3");
        }

        private void CreateReport0x00033005_2(string entityClass, double profileVersion, XElement itemProfIenergy)
        {
            if (string.IsNullOrEmpty(entityClass)
                || !(profileVersion < 1.1))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var profileVersionNode = itemProfIenergy.Attribute(Attributes.s_ProfileVersion);
            var lineInfo = (IXmlLineInfo)profileVersionNode;
            // "'PROFIenergy/@EntityClass' must not be present if '@ProfileVersion' is < "V1.1"."
            string msg = Help.GetMessageString("M_0x00033005_2");
            string xpath = Help.GetXPath(profileVersionNode);
            if (lineInfo != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033005_2");
            }
        }

        private void CreateReport0x00033005_1(string entityClass, double profileVersion, XElement itemProfIenergy)
        {
            if (!string.IsNullOrEmpty(entityClass)
                || !(profileVersion >= 1.1))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var profileVersionNode = itemProfIenergy.Attribute(Attributes.s_ProfileVersion);
            var lineInfo = (IXmlLineInfo)profileVersionNode;
            // "'PROFIenergy/@EntityClass' must be present if '@ProfileVersion' is >= "V1.1"."
            string msg = Help.GetMessageString("M_0x00033005_1");
            string xpath = Help.GetXPath(profileVersionNode);
            if (lineInfo != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033005_1");
            }
        }

        /// <summary>
        /// Check number: CN_0x00033006
        /// 
        /// <Description>
        /// Check for PROFIenergy:
        /// With severity warning:
        /// (1) The attribute EnergySavingModeList/EnergySavingModeItem/@ID shall only contain values defined in
        ///     the PROFIenergy profile in Table 2
        /// (2) The attribute MeasurementList/MeasurementItem/MeasurementValue/@ID shall contain only values defined
        ///     in the PROFIenergy profile in Annex A.1.
        /// (3) The attribute MeasurementList/MeasurementItem/MeasurementValue/@AccuracyDomain shall contain
        ///     only values defined in the PROFIenergy profile in Table 5.
        /// (4) The attribute MeasurementList/MeasurementItem/MeasurementValue/@AccuracyClass shall contain
        ///     only values defined in the PROFIenergy profile in Table 6-8.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033006()
        {
            var itemsProfIenergy = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ProfIenergy);
            itemsProfIenergy = Help.TryRemoveXElementsUnderXsAny(itemsProfIenergy, Nsmgr, Gsd);
            foreach (var itemProfIenergy in itemsProfIenergy)
            {
                // (1)
                CreateReport0x00033006_1(itemProfIenergy);

                // (2)
                CreateReport0x00033006_2(itemProfIenergy);

                // (3) + (4)
                var measurementValueAccuracyDomains = itemProfIenergy.Descendants(NamespaceGsdDef + Elements.s_MeasurementValue).Attributes(Attributes.s_AccuracyDomain);
                foreach (var measurementValueAccuracyDomain in measurementValueAccuracyDomains)
                {
                    Byte measurementAccuracyDomainValue = XmlConvert.ToByte(measurementValueAccuracyDomain.Value);
                    var measurementValueAccuracyClass = measurementValueAccuracyDomain.Parent.Attribute(Attributes.s_AccuracyClass);
                    Byte measurementAccuracyClassValue = XmlConvert.ToByte(measurementValueAccuracyClass.Value);
                    // (3)
                    if (measurementAccuracyDomainValue > 4)
                    {
                        CreateReport0x00033006_3(itemProfIenergy, measurementValueAccuracyDomain);
                    }
                    // (4)
                    else
                    {
                        CreateReport0x00033006_4(measurementAccuracyDomainValue, measurementAccuracyClassValue, itemProfIenergy, measurementValueAccuracyClass);
                    }
                }
            }

            return true;
        }

        private void CreateReport0x00033006_4(
            byte measurementAccuracyDomainValue,
            byte measurementAccuracyClassValue,
            XObject itemProfIenergy,
            XObject measurementValueAccuracyClass)
        {
            if (((measurementAccuracyDomainValue != 1 && measurementAccuracyDomainValue != 2)
                 || measurementAccuracyClassValue <= 15)
                && (measurementAccuracyDomainValue != 3 || measurementAccuracyClassValue <= 13)
                && (measurementAccuracyDomainValue != 4 || measurementAccuracyClassValue <= 7))
            {
                return;
            }

            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)measurementValueAccuracyClass;
            // "The value given for 'MeasurementItem/@AccuracyClass' is not allowed according to PROFIenergy profile Table 6-8."
            string msg = Help.GetMessageString("M_0x00033006_4");
            string xpath = Help.GetXPath(measurementValueAccuracyClass);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033006_4");
        }

        private void CreateReport0x00033006_3(XObject itemProfIenergy, XObject measurementValueAccuracyDomain)
        {
            if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)measurementValueAccuracyDomain;
            // "The value given for 'MeasurementItem/@AccuracyDomain' is not allowed according to PROFIenergy profile Table 5."
            string msg = Help.GetMessageString("M_0x00033006_3");
            string xpath = Help.GetXPath(measurementValueAccuracyDomain);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033006_3");
        }

        private void CreateReport0x00033006_2(XContainer itemProfIenergy)
        {
            var measurementValueIds = itemProfIenergy.Descendants(NamespaceGsdDef + Elements.s_MeasurementValue)
                .Attributes(Attributes.ID);
            foreach (var measurementValueId in measurementValueIds)
            {
                UInt16 measurementIdValue = XmlConvert.ToUInt16(measurementValueId.Value);
                if ((measurementIdValue <= 24 || measurementIdValue >= 30)
                    && (measurementIdValue != 39)
                    && (measurementIdValue != 69)
                    && (measurementIdValue <= 98 || measurementIdValue >= 150)
                    && (measurementIdValue != 159)
                    && (measurementIdValue <= 166 || measurementIdValue >= 170)
                    && (measurementIdValue != 179)
                    && (measurementIdValue != 189)
                    && (measurementIdValue != 199)
                    && (measurementIdValue <= 206 || measurementIdValue >= 210)
                    && (measurementIdValue <= 216 || measurementIdValue >= 220)
                    && (measurementIdValue <= 226 || measurementIdValue >= 230)
                    && (measurementIdValue != 239)
                    && (measurementIdValue <= 244 || measurementIdValue >= 57344))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
                {
                    continue;
                }

                var lineInfo = (IXmlLineInfo)measurementValueId;
                // "The value given for 'MeasurementItem/@ID' is not allowed according to PROFIenergy profile Annex A.1."
                string msg = Help.GetMessageString("M_0x00033006_2");
                string xpath = Help.GetXPath(measurementValueId);
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033006_2");
            }
        }

        private void CreateReport0x00033006_1(XContainer itemProfIenergy)
        {
            var energyItemIds = itemProfIenergy.Descendants(NamespaceGsdDef + Elements.s_EnergySavingModeItem)
                .Attributes(Attributes.ID);
            foreach (var energyItemId in energyItemIds)
            {
                Byte energyItemValue = XmlConvert.ToByte(energyItemId.Value);
                if ((energyItemValue <= 0x1F || energyItemValue >= 0xF0)
                    && (energyItemValue <= 0xF0 || energyItemValue >= 0xFE))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(itemProfIenergy, SupportedGsdmlVersion))
                {
                    continue;
                }

                var lineInfo = (IXmlLineInfo)energyItemId;
                // "The value given for 'EnergySavingModeItem/@ID' is not allowed according to PROFIenergy profile Table 2."
                string msg = Help.GetMessageString("M_0x00033006_1");
                string xpath = Help.GetXPath(energyItemId);
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00033006_1");
            }
        }
        /// <summary>
        /// Check number: CN_0x00033007
        /// 
        /// <Description>
        /// For all (Virtual)SubmoduleItem elements:
        /// If ReportingSystemEvents/Observer with attribute Type="DigitalInput" is present, this submodule
        /// must describe its IO data using channel descriptions, i.e. IOData/Input must
        /// contain at least one Channel/Input element with at least one digital input channel
        /// (There must be at least one Channel with bit BitLength=1).
        /// 
        /// (The correct amount of Channel elements and their correct content is checked already elsewhere.)
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00033007()
        {
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);

            foreach (var submodule in allSubmodules)
            {
                var observerTypes = submodule.Elements(NamespaceGsdDef + Elements.s_ReportingSystemEvents)
                    .Elements(NamespaceGsdDef + Elements.s_Observer)
                    .Attributes(Attributes.s_Type);
                foreach (var observerType in observerTypes)
                {
                    if (observerType.Value != "DigitalInput")
                    {
                        continue;
                    }

                    // Check if IOData is distributed to channels
                    var ioInputChannels = submodule.Elements(NamespaceGsdDef + Elements.s_IoData)
                        .Elements(NamespaceGsdDef + Elements.s_Input)
                        .Elements(NamespaceGsdDef + Elements.s_Channel);

                    if (ioInputChannels.Count() == 0)
                    {
                        CreateReport0x00033007_1(submodule, observerType);
                    }
                    else
                    {
                        CreateReport0x00033007_2(ioInputChannels, submodule, observerType);
                    }
                }
            }

            return true;
        }

        private void CreateReport0x00033007_2(IEnumerable<XElement> ioInputChannels, XObject submodule, XObject observerType)
        {
            bool bChannelWithBitLength1 = false;
            foreach (var ioInputChannel in ioInputChannels)
            {
                if (XmlConvert.ToUInt16(
                        ioInputChannel.Element(NamespaceGsdDef + Elements.s_Data).Attribute(Attributes.s_BitLength).Value) != 1)
                {
                    continue;
                }

                bChannelWithBitLength1 = true;
                break;
            }

            if (bChannelWithBitLength1)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)observerType;
            // "Channel description on input side must contain at least one Channel with bit BitLength=1 when 'Observer/@Type' is "DigitalInput"."
            string msg = Help.GetMessageString("M_0x00033007_2");
            string xpath = Help.GetXPath(observerType);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033007_2");
        }

        private void CreateReport0x00033007_1(XObject submodule, XObject observerType)
        {
            if (!Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
            {
                return;
            }

            var lineInfo = (IXmlLineInfo)observerType;
            // "Channel description on input side is mandatory when 'Observer/@Type' is "DigitalInput"."
            string msg = Help.GetMessageString("M_0x00033007_1");
            string xpath = Help.GetXPath(observerType);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00033007_1");
        }

        /// <summary>
        /// Check number: CN_0x00033009
        /// 
        /// <Description>
        /// For all submodules which can be configured with a DAP where PNIO_Version is >= V2.33
        /// If the SubmoduleIdentNumber is 0, then issue the Warning
        /// "SubmoduleIdentNumber 0 should not be used manufacturer specific".
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00033009()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            IList<XElement> errorRaisedForSubmodules = new List<XElement>();
            foreach (var dap in daps)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                if (pnioVersion < 2.33)
                    continue;

                IList<XElement> useableSubmodulesOfDap = FindAllUseableSubmodulesOfDap(dap);

                foreach (var useableSubmodule in useableSubmodulesOfDap)
                {
                    string strSubmoduleIdentNumber = Help.GetAttributeValueFromXElement(useableSubmodule, Attributes.s_SubmoduleIdentNumber); // must
                    int index = strSubmoduleIdentNumber.IndexOf("x", StringComparison.Ordinal);
                    strSubmoduleIdentNumber = strSubmoduleIdentNumber.Remove(0, index + 1);
                    UInt32 submoduleIdentNumber = UInt32.Parse(strSubmoduleIdentNumber, NumberStyles.HexNumber, CultureInfo.InvariantCulture);
                    if (submoduleIdentNumber == 0)
                    {
                        if (!errorRaisedForSubmodules.Contains(useableSubmodule))
                        {
                            errorRaisedForSubmodules.Add(useableSubmodule);
                            if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                            {
                                // "SubmoduleIdentNumber 0 should not be used manufacturer specific."
                                string msg = Help.GetMessageString("M_0x00033009_1");
                                string xpath = Help.GetXPath(useableSubmodule);
                                var xli = (IXmlLineInfo)useableSubmodule;
                                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                              ReportCategories.PlugRules, "0x00033009_1");
                            }
                        }
                    }
                }

            } // foreach (XmlElement dap in DAPs)

            return true;
        }
        #endregion
    }
}
