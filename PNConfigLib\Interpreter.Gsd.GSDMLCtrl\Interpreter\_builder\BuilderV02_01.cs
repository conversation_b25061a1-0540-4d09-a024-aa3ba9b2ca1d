/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_01.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Xml;
using System.Xml.XPath;

using GSDI;

using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.Gsd.Interpreter.Structure;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal class BuilderV0201 :
        BuilderV0200
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0201()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version21);
        }

        #endregion

        #region Build Models

        /// <summary>
        /// Creates the common data object model. First, the submodules are created,
        /// than the modules, after that the access points and finally the device.
        /// </summary>
        protected override void CreateCommonModel()
        {
            try
            {
                // Create all virtual submodules and add them to the store.
                CreatePhysicalSubmodules();

                base.CreateCommonModel();
            }
            catch (CreationException e)
            {
                throw new CreationException("Couldn't create 'Common' model!", e);
            }
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override bool InitExpressions()
        {
            bool succeeded = true;

            try
            {
                succeeded = base.InitExpressions();
                if (!succeeded)
                {
                    return false;
                }

                // --------------------------------------------------------------

                if (!Expressions.ContainsKey(Elements.s_SystemDefinedChannelDiagItem))
                {
                    // Create document navigator.
                    XPathNavigator nav = Gsd.CreateNavigator();

                    if (nav == null)
                    {
                        return false;
                    }

                    // Create the NamespaceManager and add all XML Namespaces to it.
                    XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                    nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                    nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                    nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                    nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                    nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                    // ProfileChannelDiagItem
                    XPathExpression expr = nav.Compile(XPathes.AllProfileChannelDiagItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_ProfileChannelDiagItem, expr);

                    // SubmoduleItem
                    expr = nav.Compile(XPathes.AllSubmoduleItems);
                    expr.SetContext(nsmgr);
                    Expressions.Add(Elements.s_SubmoduleItem, expr);

                }
            }
            catch (XPathException)
            {
                succeeded = false;
            }
            catch (ArgumentException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            GsdObject obj = null;


            try
            {
                switch (name)
                {
                    // ------------- STRUCTURE ELEMENTS --------------------------
                    case Models.s_ObjectSubmoduleStructureElement:
                        {
                            // NOTE: Navigator must point to ProfileBody.
                            PrepareSubmoduleStructureElement(nav, ref hash);
                            obj = new SubmoduleStructureElement();

                            break;
                        }

                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectSubmodule:
                        {
                            // NOTE: Navigator must point to VirtualSubmodule.
                            PrepareVirtualSubmodule(nav, ref hash);
                            obj = new VirtualSubmodule();

                            break;
                        }
                    case Models.s_ObjectAddValueDataItem:
                        {
                            // NOTE: Navigator must point to ProcessAlarmReasonAddValue.
                            PrepareAddValueDataItem(nav, ref hash);
                            obj = new AddValueDataItem();

                            break;
                        }
                    case Models.s_ObjectProfileChannelDiagnostic:
                        {
                            // NOTE: Navigator must point to ProfileChannelDiagItem.
                            PrepareProfileChannelDiagnostic(nav, ref hash);
                            obj = new ProfileChannelDiagnostic();

                            break;
                        }
                    case Models.s_ObjectProfileExtendedChannelDiagnostic:
                        {
                            // NOTE: Navigator must point to ProfileExtChannelDiagItem.
                            PrepareProfileExtendedChannelDiagnostic(nav, ref hash);
                            obj = new ProfileChannelDiagnostic();

                            break;
                        }

                    case Models.s_ObjectSlot:
                        {
                            // NOTE: Navigator must point to SlotItem.
                            PrepareSlot(nav, ref hash);
                            obj = new Slot();

                            break;
                        }

                    case Models.s_ObjectMediaRedundancy:
                        {
                            // NOTE: Navigator must point to MediaRedundancy.
                            PrepareMediaRedundancy(nav, ref hash);
                            obj = new MediaRedundancy();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav); // Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAddressAssignment, null);
            hash.Add(Models.s_FieldSlots, null);
            hash.Add(Models.s_FieldPhysicalSubslots, null);
            hash.Add(Models.s_FieldRemoteApplicationTimeout, null);
            hash.Add(Models.s_FieldSubmodulePlugData, null);

            object obj = null;

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            // Navigate to SlotList/SlotItem and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_SlotItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectSlot, nodes.Current);

                if (null == obj)
                    throw new PreparationException("Couldn't create Slot!");

                list.Add(obj);  // Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldSlots] = list;

            // Get PhysicalSubslots Optional. 
            string attr = nav.GetAttribute(Attributes.s_PhysicalSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPhysicalSubslots] = ValueListHelper.SeparateUnsignedValueList(attr);
            }

            // Select submodules.
            nodes = nav.SelectDescendants(Elements.s_SubmoduleItemRef, Namespaces.s_GsdmlDeviceProfile, false);
            list = new ArrayList(nodes.Count);
            var slist = GsdObjectDictionaryFactory.CreateDictionary(true);

            // Check whether submodules are available.
            if (nodes.Count > 0)
            {
                // Add found submodules to hash and create module plug data.
                AddSubmodulesToHash(nodes, list, slist);
                      
            }
            hash[Models.s_FieldSubmodules] = list;
            hash[Models.s_FieldSubmodulePlugData] = slist;

            // Get AddressAssignment attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_AddressAssignment, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldAddressAssignment] = Help.SeparateTokenList(attr);
            }
            else if (!hash.ContainsKey(Models.s_FieldIsExtendedAddressAssignmentSupported))
            {
                // neither attribute AddressAssigment nor attribute ExtendedAddressAssignmentSupported
                // are present, use default value for AddressAssigment;
                ArrayList listAddressAssignment = new ArrayList();
                listAddressAssignment.Add(Attributes.s_DefaultAddressAssignment);
                hash[Models.s_FieldAddressAssignment] = listAddressAssignment;
            }

            attr = nav.GetAttribute(Attributes.s_RemoteApplicationTimeout, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldRemoteApplicationTimeout] = value;
            }
            else
            {
                hash[Models.s_FieldRemoteApplicationTimeout] = Attributes.s_DefaultRemoteApplicationTimeout;
            }
        }

        private void AddSubmodulesToHash(XPathNodeIterator nodes, ArrayList list, IGsdObjectDictionary slist)
        {
            object obj;
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }
                string sID = Help.CollapseWhitespace(nodes.Current.GetAttribute(Attributes.s_SubmoduleItemTarget, String.Empty));
                if (!(CStore.VirtualSubmodules.ContainsKey(sID) || CStore.PluggablePortsubmodules.ContainsKey(sID)))
                    throw new PreparationException("Couldn't get submodule from store with ID '" + sID + "'!");

                if (CStore.VirtualSubmodules.ContainsKey(sID))
                {
                    // Add module to list.
                    list.Add(CStore.VirtualSubmodules[sID]);
                }
                else
                {
                    list.Add(CStore.PluggablePortsubmodules[sID]);
                }

                // Create module plug data.
                obj = CreateGsdObject(Models.s_ObjectSubmodulePlugData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSubmodulePlugData + "' couldn't be created!");

                // Add it to the hash.
                slist.Add(sID, (GsdObject)obj);
            }
        }
        protected override void PrepareDevice(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Device data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldProfileChannelDiagnostics, null);

            // Call base class method first.
            base.PrepareDevice(nav, ref hash);

            // Navigate to channel diagnostics and create it. Optional.
            ArrayList list = null;
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ProfileChannelDiagItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectProfileChannelDiagnostic, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileChannelDiagnostic + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldProfileChannelDiagnostics] = list;
        }

        protected override void AddSubmodules2DAP(XPathNavigator nav, ref Hashtable hash)
        {
            // Select virtual submodule.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_VirtualSubmoduleItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Check whether VirtualSubmodule is available.
            if (nodes.Count == 0)
                return;
            base.AddSubmodules2DAP(nav, ref hash);
        }

        // Since GSDML V2.1 DeviceAccesspointItems without Modules (UseableModules) are allowed
        protected override void PrepareDAPModulePlugData(XPathNavigator nav, ref Hashtable hash)
        {
            // Select modules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ModuleItemRef, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = new ArrayList(nodes.Count);
            var slist = GsdObjectDictionaryFactory.CreateDictionary();

            // Check whether modules are available. Required.
            if (nodes.Count > 0)
            {

                // Add found modules to hash and create module plug data.

                while (nodes.MoveNext())
                {
                    if (nodes.Current == null)
                    {
                        continue;
                    }

                    string sID = nodes.Current.GetAttribute(Attributes.s_ModuleItemTarget, String.Empty);
                    if (!CStore.Modules.ContainsKey(sID))
                        throw new PreparationException("Couldn't get Module from store with ID '" + sID + "'!");

                    // Add module to list.
                    list.Add(CStore.Modules[sID]);

                    // Create module plug data.
                    var obj = CreateGsdObject(Models.s_ObjectModulePlugData, nodes.Current) as GsdObject;
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectModulePlugData + "' couldn't be created!");

                    // Add it to the hash.
                    slist.Add(sID, (GsdObject)obj);
                }
            }
            hash[Models.s_FieldModules] = list;
            hash[Models.s_FieldModulePlugData] = slist;
        }

        protected override void PrepareSubmodulePlugData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Submodule ModulePlugData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFixedInSlots, null);
            hash.Add(Models.s_FieldUsedInSlots, null);
            hash.Add(Models.s_FieldAllowedInSlots, null);
            hash.Add(Models.s_FieldFixedInSlotsString, null);
            hash.Add(Models.s_FieldUsedInSlotsString, null);
            hash.Add(Models.s_FieldAllowedInSlotsString, null);
            hash.Add(Models.s_FieldSubmodulePlugDataModel, null);

            // Get FixedInSlots attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FixedInSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldFixedInSlotsString] = attr;

            // Get UsedInSlots attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_UsedInSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldUsedInSlotsString] = attr;

            // Get AllowedInSlots attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_AllowedInSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldAllowedInSlotsString] = attr;

            hash[Models.s_FieldSubmodulePlugDataModel] = true;
        }


        protected override void PrepareModule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Module object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldPhysicalSubslots, null);
            hash.Add(Models.s_FieldSubmodulePlugData, null);


            // Call base class method first.
            base.PrepareModule(nav, ref hash);

            // --------------------------------------------

            // Get physical subslots. Optional.
            string attr = nav.GetAttribute(Attributes.s_PhysicalSubslots, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldPhysicalSubslots] = ValueListHelper.SeparateUnsignedValueList(attr);

            // --------------------------------------------
            // Select modules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_SubmoduleItemRef, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = new ArrayList(nodes.Count);
            var slist = GsdObjectDictionaryFactory.CreateDictionary(true);

            // Check whether submodules are available.
            if (nodes.Count > 0)
            {

                // Add found submodules to hash and create module plug data.

                while (nodes.MoveNext())
                {
                    if (nodes.Current == null)
                    {
                        continue;
                    }

                    string sID = nodes.Current.GetAttribute(Attributes.s_SubmoduleItemTarget, String.Empty);
                    if (!(CStore.VirtualSubmodules.ContainsKey(sID) || CStore.PluggablePortsubmodules.ContainsKey(sID)))
                        throw new PreparationException("Couldn't get submodule from store with ID '" + sID + "'!");

                    if (CStore.VirtualSubmodules.ContainsKey(sID))
                    {
                        // Add module to list.
                        list.Add(CStore.VirtualSubmodules[sID]);
                    }
                    else
                    {
                        list.Add(CStore.PluggablePortsubmodules[sID]);
                    }

                    // Create module plug data.
                    var obj = CreateGsdObject(Models.s_ObjectSubmodulePlugData, nodes.Current) as GsdObject;
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectSubmodulePlugData + "' couldn't be created!");

                    // Add it to the hash.
                    slist.Add(sID, (GsdObject)obj);
                }
            }
            hash[Models.s_FieldSubmodules] = list;
            hash[Models.s_FieldSubmodulePlugData] = slist;
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for VirtualSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldWriteableImRecords, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            // --------------------------------------------

            // Get Writeable_IM_Records. Optional.
            string attr = nav.GetAttribute(Attributes.s_WriteableImRecords, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldWriteableImRecords] = ValueListHelper.SeparateUnsignedValueList(attr);
            }
        }


        protected void PrepareAddValueDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for VirtualSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldId, null);
            hash.Add(Models.s_FieldDataType, null);
            hash.Add(Models.s_FieldDataLength, null);

            // --------------------------------------------

            // Get attribute Id. Required.
            string attr = nav.GetAttribute(Attributes.Id, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldId] = value;

            // Get data type attribute of data item. Required.
            attr = nav.GetAttribute(Attributes.s_DataType, String.Empty);
            if (Enums.IsDataTypeEnumValueConvertable(attr))
                hash[Models.s_FieldDataType] = Enums.ConvertDataItemTypeEnum(attr);

            // Get length attribute of data item. Optional.
            uint length = Enums.GetDataItemTypeBitLength(attr);
            if (length == 0)
            {
                attr = nav.GetAttribute(Attributes.s_Length, String.Empty);
                if (String.IsNullOrEmpty(attr))
                    throw new PreparationException("If type of DataItem is 'OctetString' or 'VisibleString', the length attribute must be specified!");
                length = System.Xml.XmlConvert.ToUInt32(attr) * 8;
            }
            hash[Models.s_FieldDataLength] = length;
        }

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for PortSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIsNetworkComponentDiagnosisSupported, null);
            hash.Add(Models.s_FieldSupportedRtClasses, null);
            hash.Add(Models.s_FieldSupportedProtocols, null);
            hash.Add(Models.s_FieldSupportedMibs, null);
            hash.Add(Models.s_FieldMediaRedundancy, null);
            hash.Add(Models.s_FieldIsMrpSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // --------------------------------------------

            // Get NetworkComponentDiagnosisSupported attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_NetworkComponentDiagnosisSupported, String.Empty);
            hash[Models.s_FieldIsNetworkComponentDiagnosisSupported] = Help.GetBool(attr, Attributes.s_DefaultNetworkComponentDiagnosisSupported);

            // Get SupportedRT_Classes attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_SupportedRTClasses, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSupportedRtClasses] = Help.SeparateTokenList(attr);
            }

            // Get SupportedProtocols attribute. Required.
            attr = nav.GetAttribute(Attributes.s_SupportedProtocols, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSupportedProtocols] = Help.SeparateTokenList(attr);
            }

            // Get SupportedMibs attribute. Required.
            attr = nav.GetAttribute(Attributes.s_SupportedMibs, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSupportedMibs] = Help.SeparateTokenList(attr);
            }

            // Navigate to MediaRedundancy. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_MediaRedundancy, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsMrpSupported] = true;

                obj = CreateGsdObject(Models.s_ObjectMediaRedundancy, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMediaRedundancy + "' couldn't be created!");
            }

            hash[Models.s_FieldMediaRedundancy] = obj;
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for PortSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFiberOpticTypes, null);
            hash.Add(Models.s_FieldIsPowerBudgetControlSupported, null);
            hash.Add(Models.s_FieldMauTypes, null);
            hash.Add(Models.s_FieldLinkStateDiagnosisCapability, null);
            hash.Add(Models.s_FieldIsPortDeactivationSupported, null);
            hash.Add(Models.s_FieldIsMrpSupported, null);
            hash.Add(Models.s_FieldIsDefaultRingport, null);
            hash.Add(Models.s_FieldSupportsRingportConfig, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            // --------------------------------------------
            // Get FiberOpticTypes attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FiberOpticTypes, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldFiberOpticTypes] = ValueListHelper.SeparateUnsignedValueList(attr);

            // Get PowerBudgetControlSupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PowerBudgetControlSupported, String.Empty);
            hash[Models.s_FieldIsPowerBudgetControlSupported] = Help.GetBool(attr, Attributes.s_DefaultPowerBudgetControlSupported);

            // Get LinkStateDiagnosisCapability attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_LinkStateDiagnosisCapability, String.Empty);
            if (Enums.IsLinkStateDiagnosisEnumValueConvertable(attr))
            {
                hash[Models.s_FieldLinkStateDiagnosisCapability] = Enums.ConvertLinkStateDiagnosisEnum(attr);
            }
            else	// Set default.
                hash[Models.s_FieldLinkStateDiagnosisCapability] = Attributes.s_DefaultLinkStateDiagnosisCapability;

            // Get MAUTypes attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MauTypes, String.Empty);
            if (!String.IsNullOrEmpty(attr))
                hash[Models.s_FieldMauTypes] = ValueListHelper.SeparateUnsignedValueList(attr);
            else if (hash.ContainsKey(Models.s_FieldMauType) && hash[Models.s_FieldMauType] is MauTypes)
            {
                // If attribute MAUTypes was not found, the value from the attribute MAUType is used
                ArrayList listMauTypes = new ArrayList();
                MauTypes mauType = (MauTypes)hash[Models.s_FieldMauType];
                listMauTypes.Add((uint)mauType);
                hash[Models.s_FieldMauTypes] = listMauTypes;
            }

            // Get PortDeactivationSupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PortDeactivationSupported, String.Empty);
            hash[Models.s_FieldIsPortDeactivationSupported] = Help.GetBool(attr, Attributes.s_DefaultPortDeactivationSupported);

            // Get IsDefaultRingport attribute. Optional.
            string isDefaultRingPort = nav.GetAttribute(Attributes.s_IsDefaultRingport, String.Empty);
            hash[Models.s_FieldIsDefaultRingport] = Help.GetBool(isDefaultRingPort, Attributes.s_DefaultIsDefaultRingport);

            // Get SupportsRingportConfig attribute. Optional.
            string supportsRingportConfig = nav.GetAttribute(Attributes.s_SupportsRingportConfig, String.Empty);
            hash[Models.s_FieldSupportsRingportConfig] = Help.GetBool(supportsRingportConfig,
                Attributes.s_DefaultSupportsRingportConfig);

            if (IsMrpPort(supportsRingportConfig, isDefaultRingPort))
            {
                hash[Models.s_FieldIsMrpSupported] = true;
            }
            else
            {
                hash[Models.s_FieldIsMrpSupported] = false;
            }
        }

        private bool IsMrpPort(string supportsRingportConfig, string isDefaultRingPort)
        {
            bool mrp = false;

            // This condition is not correct, it is only there to avoid compatibility problems
            // with previous versions (MDD related problems)
            if (supportsRingportConfig != string.Empty && isDefaultRingPort != string.Empty)
            {
                mrp = true;
            }
            else if (Help.GetBool(supportsRingportConfig, Attributes.s_DefaultSupportsRingportConfig) || Help.GetBool(isDefaultRingPort, Attributes.s_DefaultIsDefaultRingport))
            {
                mrp = true;
            }

            return mrp;
        }

        /// <summary>
        /// The F-parameter record data (with F_iPar_CRC) is a fixed 14 byte block with configuration data. It is
        /// organized as follows:
        /// 0	F_Prm_Flag1		(Unsigned8)		0x08 (8)
        ///			Bit 0:	xxx	(F_Check_SeqNr)		0
        ///			Bit 1:	F_Check_iPar			0 "No check"
        ///			Bit 2:	F_SIL					0 "SIL3"
        ///			Bit 3:							1
        ///			Bit 4:	F_CRC_Length			0 "3 Byte CRC"
        ///			Bit 5:							0
        ///			Bit 6:							0 
        ///			Bit 7:	xxx						0 "reserved"
        /// 1	F_Prm_Flag2		(Unsigned8)		0x40 (64)
        ///			Bit 0:	xxx						0 "reserved"
        ///			Bit 1:	xxx						0 "reserved"
        ///			Bit 2:	xxx						0 "reserved"
        ///			Bit 3:	F_Block_ID				0 "F-Host/F-Device relationship"
        ///			Bit 4:							0
        ///			Bit 5:							0
        ///			Bit 6:	F_Par_Version			1 "PROFIsafe V2.0"
        ///			Bit 7:							0
        /// 2	F_Source_Add	(Unsigned16)	0x00 (0)
        /// 3									0x01 (1)
        /// 4	F_Dest_Add		(Unsigned16)	0x00 (0)
        /// 5									0x01 (1)
        /// 6	F_WD_Time		(Unsigned16)	0x00 (0)
        /// 7									0x96 (150)
        /// 8  F_iPar_CRC		(Unsigned32)
        /// 12 F_Par_CRC		(Unsigned16)	0xBB (187)
        /// 
        /// If there is no F_iPar_CRC the F-parameter record data is a fixed 10 byte block with configuration data
        /// (see BuilderV02_00.cs).
        /// </summary>
        /// <returns>Const object with default binary settings with byte length 10.</returns>
        protected override void PrepareFParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for FParameterRecordData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIndex, null);
            hash.Add(Models.s_FieldLength, null);
            hash.Add(Models.s_FieldName, null); // always null!
            hash.Add(Models.s_FieldNameTextId, null); // always null!
            hash.Add(Models.s_FieldConsts, null);

            hash.Add(Models.s_FieldTransferSequence, null);
            hash.Add(Models.s_FieldRefs, null);
            hash.Add(Models.s_FieldFParamDescCrc, null);

            //XPathNodeIterator nodes = null;

            // Get F_ParamDescCRC attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_FParamDescCrc, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldFParamDescCrc] = value;

            // Get Index attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Index, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldIndex] = value;

            // Get TransferSequence attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTransferSequence] = Attributes.s_DefaultTransferSequence;
            }
            else
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldTransferSequence] = value;
            }

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);
            bool IsiParCRC = false;
            if (nodes.MoveNext())
            {
                // If there is a F_iPar_CRC, use a FParameterRecord with 14 Bytes
                IsiParCRC = true;
                hash[Models.s_FieldLength] = Attributes.s_ExtFixedFParameterRecordDataLength;
            }
            else
            {
                // If there is no F_iPar_CRC, use a FParameterRecord with 10 Bytes
                hash[Models.s_FieldLength] = Attributes.s_FixedFParameterRecordDataLength;
            }

            // Set Consts property.
            Hashtable h = new Hashtable();
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldValues, null);

            ArrayList consts = new ArrayList();
            object obj = PrepareFParameterRecordDataCreateConst();
            consts.Add(obj);
            hash[Models.s_FieldConsts] = consts;

            // Set Refs property.
            h = new Hashtable();
            h.Add(Models.s_FieldDataType, null);
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldBitOffset, null);
            h.Add(Models.s_FieldBitLength, null);
            h.Add(Models.s_FieldIsChangeable, null);
            h.Add(Models.s_FieldIsVisible, null);
            h.Add(Models.s_FieldName, null);
            h.Add(Models.s_FieldNameTextId, null);
            h.Add(Models.s_FieldHelp, null);
            h.Add(Models.s_FieldValueGsdId, null);
            h.Add(Models.s_FieldDefaultValue, null);
            h.Add(Models.s_FieldValues, null);
            h.Add(Models.s_FieldValueType, null);

            ArrayList refs = new ArrayList();
            obj = PrepareFParameterRecordDataCreateRefFCheckIPar(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSil(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcLength(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFBlockID(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFParVersion(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSourceAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFDestAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFWdTime(nav, ref h);
            refs.Add(obj);
            if (IsiParCRC)
            {
                // there is a F_iPar_CRC 
                obj = PrepareFParameterRecordDataCreateRefFiParCrc(nav, ref h);
                refs.Add(obj);
            }
            obj = PrepareFParameterRecordDataCreateRefFParCrc(nav, ref h);
            refs.Add(obj);


            hash[Models.s_FieldRefs] = refs;
        }

        /// <summary>
        /// This CRC1 key is generated by the engineering tool across the F parameters. 
        /// 
        /// Data type:		Unsigned32
        /// Byte offset:	8
        /// Bit offset:		0
        /// Bit length:		32
        /// 
        ///	Parameter values (max. range: 0..4294967295)
        /// </summary>
        /// <returns>Ref object for the F_iPar_CRC parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFiParCrc(XPathNavigator nav, ref Hashtable hash)
        {
            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FIParCrc;
            hash[Models.s_FieldNameTextId] = Elements.s_FIParCrc;
            hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned32;
            hash[Models.s_FieldByteOffset] = (uint)8;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)32;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;
            hash[Models.s_FieldIsChangeable] = true; // auto
            hash[Models.s_FieldIsVisible] = true;

            uint defaultValue = 0; // Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length > 0)
                    {
                        UInt32 value = 0;
                        UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                        defaultValue = value;
                    }
                }
            }

            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();
            GsdObject obj = new AreaItem();
            valuehash[Models.s_FieldMinValue] = uint.MinValue;
            valuehash[Models.s_FieldMaxValue] = uint.MaxValue;
            bool succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// This CRC1 key is generated by the engineering tool across the F parameters. 
        /// 
        /// Data type:		Unsigned16
        /// Byte offset:	10
        /// Bit offset:		0
        /// Bit length:		16
        /// 
        ///	Parameter values (default range: 0..65535, max. range: 0..65535, default value: 53356)
        /// </summary>
        /// <returns>Ref object for the F_Par_CRC parameter.</returns>
        protected override object PrepareFParameterRecordDataCreateRefFParCrc(XPathNavigator nav, ref Hashtable hash)
        {
            // If there is no F_iPar_CRC, use the implementation of the base class
            XPathNodeIterator nodesFiPar = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);
            if (!nodesFiPar.MoveNext())
            {
                return base.PrepareFParameterRecordDataCreateRefFParCrc(nav, ref hash);
            }

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FParCrc;
            hash[Models.s_FieldNameTextId] = Elements.s_FParCrc;
            hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned16;
            hash[Models.s_FieldByteOffset] = (uint)12;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)16;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = ValueTypes.GSDArea;
            hash[Models.s_FieldIsChangeable] = true; // auto
            hash[Models.s_FieldIsVisible] = true;

            ushort defaultValue = 53356; // Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FParCrc, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Length > 0)
                        defaultValue = System.Xml.XmlConvert.ToUInt16(attr);
                }
            }

            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Models.s_FieldMinValue, null);
            valuehash.Add(Models.s_FieldMaxValue, null);

            ArrayList list = new ArrayList();
            GsdObject obj = new AreaItem();
            valuehash[Models.s_FieldMinValue] = ushort.MinValue;
            valuehash[Models.s_FieldMaxValue] = ushort.MaxValue;
            bool succeeded = obj.Fill(valuehash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
            list.Add(obj);

            hash[Models.s_FieldValues] = list;

            // Create and fill RefData object.
            obj = new RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj; // ---------->
        }

        protected virtual void PrepareProfileChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApi, null);
            hash.Add(Models.s_FieldErrorType, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldProfileExtendedChannelDiagnostics, null);

            // Get error API attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Api, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldApi] = value;

            // Get error type attribute. Required.
            attr = nav.GetAttribute(Attributes.s_ErrorType, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldErrorType] = value;

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = GetText(nodes.Current);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // --------------------------------------------

            // Navigate to ProfileExtChannelDiagItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ProfileExtChannelDiagItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectProfileExtendedChannelDiagnostic, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileExtendedChannelDiagnostic + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfileExtendedChannelDiagnostics] = list;
        }

        protected override void PrepareExtendedChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldExtChannelAddValueDataItems, null);

            base.PrepareExtendedChannelDiagnostic(nav, ref hash);

            // Navigate to ExtChannelAddValueDataItems elements and create them. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectAddValueDataItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAddValueDataItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtChannelAddValueDataItems] = list;
        }

        protected virtual void PrepareProfileExtendedChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApi, null);
            hash.Add(Models.s_FieldErrorType, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldProfileExtChannelAddValueDataItems, null);

            // Get error API attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Api, String.Empty);
            UInt32 value = 0;
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldApi] = value;
            }

            // Get error type attribute. Required.
            attr = nav.GetAttribute(Attributes.s_ErrorType, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldErrorType] = value;

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldName] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Navigate to ExtChannelAddValueDataItems elements and create them. Optional.
            nodes = nav.SelectDescendants(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectAddValueDataItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAddValueDataItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfileExtChannelAddValueDataItems] = list;
        }

        protected override void PrepareRefData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Module object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldLength, null);

            // Call base class method first.
            base.PrepareRefData(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_Length, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldLength] = value;
            }
        }

        protected virtual void PrepareSlot(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Slot object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldSlotNumber, null);

            // Get TextId. Required.
            hash[Models.s_FieldName] = GetText(nav);
            hash[Models.s_FieldNameTextId] = nav.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get SubslotNumber attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_SlotNumber, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldSlotNumber] = value;
        }

        protected virtual void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for MediaRedundancy object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMrSupportedRoles, null);
            hash.Add(Models.s_FieldIsRTMediaRedundancySupported, null);
            hash.Add(Models.s_FieldIsMrpSupported, true);

            // Get SupportedRole attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_MrSupportedRoles, String.Empty);

            ArrayList listMrRoles = new ArrayList();

            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList attrList = Help.SeparateTokenList(attr);
                foreach (string role in attrList)
                {
                    if (Enums.IsMRSupportedRolesEnumValueConvertable(role))
                        listMrRoles.Add(Enums.ConvertMRSupportedRolesEnum(role));
                }
            }
            else
            {
                listMrRoles.Add(Attributes.DefaultMRSupportedRole);

            }

            hash[Models.s_FieldMrSupportedRoles] = listMrRoles;

            // Get RT_MediaRedundancySupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_RTMediaRedundancySupported, String.Empty);
            hash[Models.s_FieldIsRTMediaRedundancySupported] = Help.GetBool(attr, Attributes.s_DefaultRTMediaRedundancySupported);
        }

        #endregion

        #region Creation
        protected virtual void CreatePhysicalSubmodules()
        {
            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_SubmoduleItem]);

            // Prepare data for each submodule, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectSubmodule;
                ModuleObject obj = (ModuleObject)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModule + "' couldn't be created!");

                // Add object to store.
                CStore.VirtualSubmodules.Add( obj.GsdID, obj);
            }
        }

        protected override void CreateModules()
        {
            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ModuleItem]);

            // Check whether Modules are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each Module, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectModule;
                ModuleObject obj = (ModuleObject)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModule + "' couldn't be created!");

                // Add object to store.
                CStore.Modules.Add( obj.GsdID, obj);
            }
        }

        #endregion

        #endregion

        #region Structure Model Methods

        #region Preparation

        protected virtual void PrepareSubmoduleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Submodule data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmoduleType, null);

            if (nav.LocalName == Elements.s_SubmoduleItem)
            {
                hash[Models.s_FieldSubmoduleType] = "Submodule";
            }

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);
        }

        protected override void PrepareModuleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Module data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmodules, null);

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);

            // Check whether (Sub)Modules are available.
            // NOTE: The following logic will be implemented:
            // 
            //       Notation: A = AllowedIn(Sub)Slots
            //                 U = UsedIn(Sub)Slots
            //                 F = FixedIn(Sub)Slots
            //                 P = Physical(Sub)Slot
            //                 
            //       Only F:              (Sub)Module must not be available from the catalog,
            //                            because it is in each F and not allowed to be inserted elsewhere.
            //       A and F:             (Sub)Module must be available from the catalog, only if A contains additional (sub)slots,
            //                            which are not in F. The (Sub)Module is fixed in (sub)slots and can be inserted into each A,
            //                            which is not also a F. If all (sub)slots from A are also in F there is no possibility for insertion.
            //       Not A, not U, not F: (Sub)Module must be available from the catalog and can be inserted into each P.
            //       Only A:              (Sub)Module must be available from the catalog and can be inserted into each A.
            //       Only U:              (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be deleted and inserted again.
            //       A and U:             (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be additional inserted in each A.
            //       U and F:             (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and inserted again.
            //       A and U and F:       (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and additional inserted in each A (U must be contained in A).


            // Select compatible submodules.
            XPathNodeIterator nodes = nav.SelectDescendants(
                Elements.s_SubmoduleItemRef,
                Namespaces.s_GsdmlDeviceProfile,
                false);

            // Check whether submodules are available.
            if (nodes.Count <= 0)
            {
                return;
            }

            // Add submodule list to hash.
            ArrayList list = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }
                string sfixed = nodes.Current.GetAttribute(Attributes.s_FixedInSubslots, String.Empty);
                string sused = nodes.Current.GetAttribute(Attributes.s_UsedInSubslots, String.Empty);
                string sallowed = nodes.Current.GetAttribute(Attributes.s_AllowedInSubslots, String.Empty);

                bool submoduleInCatalog = IsSubmoduleInCatalog(sfixed, sused, sallowed);
                if (submoduleInCatalog)
                {
                    list.Add(
                        SStore.PhysicalSubmodules[nodes.Current.GetAttribute(
                            Attributes.s_SubmoduleItemTarget,
                            String.Empty)]);
                }
            }

            list.TrimToSize();

            hash[Models.s_FieldSubmodules] = list;

        }

        private  bool IsSubmoduleInCatalog(string sfixed, string sused, string sallowed)
    {
        bool submoduleInCatalog = true;

        if (sfixed.Length != 0 && sused.Length == 0 && sallowed.Length == 0)
        {
            // only F
            submoduleInCatalog = false;
        }
        else if (sfixed.Length != 0 && sused.Length == 0 && sallowed.Length != 0)
        {
            // A and F
            List<uint> listAllowed = ValueListHelper.SeparateUnsignedValueList(sallowed);
            List<uint> listFixed = ValueListHelper.SeparateUnsignedValueList(sfixed);

            // In principle the check for length is sufficient here, because all F must be contained in A.
            // But because it is not ensured that the checker run every time, the list contents must be checked.
            if (listAllowed.Count != listFixed.Count)
            {
                return true;
            }
            for (int i = 0; i < listAllowed.Count; i++)
            {
                if (listAllowed[i] == listFixed[i])
                {
                    submoduleInCatalog = false;
                }
                else
                {
                    submoduleInCatalog = true;
                    break;
                }
            }
        }
        return submoduleInCatalog;
    }

    protected override void PrepareAccessPointStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare AccessPoint data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmodules, null);
            hash.Add(Models.s_FieldModules, null);

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);

            // Check whether (Sub)Modules are available.
            // NOTE: The following logic will be implemented:
            // 
            //       Notation: A = AllowedIn(Sub)Slots
            //                 U = UsedIn(Sub)Slots
            //                 F = FixedIn(Sub)Slots
            //                 P = Physical(Sub)Slot
            //                 
            //       Only F:              (Sub)Module must not be available from the catalog,
            //                            because it is in each F and not allowed to be inserted elsewhere.
            //       A and F:             (Sub)Module must be available from the catalog, only if A contains additional (sub)slots,
            //                            which are not in F. The (Sub)Module is fixed in (sub)slots and can be inserted into each A,
            //                            which is not also a F. If all (sub)slots from A are also in F there is no possibility for insertion.
            //       Not A, not U, not F: (Sub)Module must be available from the catalog and can be inserted into each P.
            //       Only A:              (Sub)Module must be available from the catalog and can be inserted into each A.
            //       Only U:              (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be deleted and inserted again.
            //       A and U:             (Sub)Module must be available from the catalog. On default it is inserted in each U,
            //                            but can be additional inserted in each A.
            //       U and F:             (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and inserted again.
            //       A and U and F:       (Sub)Module must be available from the catalog. It is in each F and on default it is inserted
            //                            in each U, but can be deleted from U and additional inserted in each A (U must be contained in A).


            // Select compatible submodules.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_SubmoduleItemRef, Namespaces.s_GsdmlDeviceProfile, false);

            AddSubmodulesToHash(hash, nodes);

            // Select compatible modules.
            nodes = nav.SelectDescendants(Elements.s_ModuleItemRef, Namespaces.s_GsdmlDeviceProfile, false);

            // Check whether Modules are available. No modules are allowed since V2.1.
            if (nodes.Count == 0)
                return;

            // Add Module list to hash.
            ArrayList modulelist = new ArrayList();
            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sfixed = nodes.Current.GetAttribute(Attributes.s_FixedInSlots, String.Empty);
                string sused = nodes.Current.GetAttribute(Attributes.s_UsedInSlots, String.Empty);
                string sallowed = nodes.Current.GetAttribute(Attributes.s_AllowedInSlots, String.Empty);

                bool moduleInCatalog = IsSubmoduleInCatalog(sfixed, sused, sallowed);

                // If the module is fixed, it can have nevertheless submodules, which are pluggable.
                // In this case the module must be in catalog to reach the submodules behind.
                if (!moduleInCatalog)
                {
                    XPathNodeIterator nodes1 = nav.Select((XPathExpression)Expressions[Elements.s_ModuleItem]);
                    string sModuleItemTarget = nodes.Current.GetAttribute(Attributes.s_ModuleItemTarget, String.Empty);
                    while (nodes1.MoveNext() && !moduleInCatalog)
                    {
                        string sModuleID = nodes1.Current.GetAttribute(Attributes.ID, String.Empty);
                        if (0 != string.Compare(sModuleItemTarget, sModuleID, StringComparison.Ordinal))
                        {
                            continue;
                        }
                        moduleInCatalog = IsModuleInCatalog(nodes1, false);
                        
                        }
                    }
                

                if (moduleInCatalog)
                {
                    modulelist.Add(SStore.Modules[nodes.Current.GetAttribute(Attributes.s_ModuleItemTarget, String.Empty)]);
                }
            }
            modulelist.TrimToSize();

         
                hash[Models.s_FieldModules] = modulelist;

        }

        private static bool IsModuleInCatalog(XPathNodeIterator nodes1, bool moduleInCatalog)
        {
            if (nodes1.Current == null)
            {
                return moduleInCatalog;
            }

            XPathNodeIterator nodes2 = nodes1.Current.SelectDescendants(Elements.s_SubmoduleItemRef, Namespaces.s_GsdmlDeviceProfile, false);
            while (nodes2.MoveNext() && !moduleInCatalog)
            {
                string sfixedsub = nodes2.Current.GetAttribute(Attributes.s_FixedInSubslots, String.Empty);
                string susedsub = nodes2.Current.GetAttribute(Attributes.s_UsedInSubslots, String.Empty);
                string sallowedsub = nodes2.Current.GetAttribute(Attributes.s_AllowedInSubslots, String.Empty);

                if (sfixedsub.Length != 0 && susedsub.Length == 0 && sallowedsub.Length != 0)
                {
                    // A and F
                    List<uint> listAllowed = ValueListHelper.SeparateUnsignedValueList(sallowedsub);
                    List<uint> listFixed = ValueListHelper.SeparateUnsignedValueList(sfixedsub);

                    // In principle the check for length is sufficient here, because all F must be contained in A.
                    // But because it is not ensured that the checker run every time, the list contents must be checked.
                    if (listAllowed.Count != listFixed.Count)
                    {
                        continue;
                    }
                    for (int i = 0; i < listAllowed.Count; i++)
                    {
                        if (listAllowed[i] == listFixed[i])
                        {
                            continue;
                        }
                        moduleInCatalog = true;
                        break;

                    }

                }
                else if (sfixedsub.Length == 0 || susedsub.Length != 0 || sallowedsub.Length != 0)
                {
                    // not only F
                    moduleInCatalog = true;
                }
            }
            return moduleInCatalog;
        }
        private void AddSubmodulesToHash(IDictionary hash, XPathNodeIterator nodes)
        {
            if (nodes.Count <= 0)
            {
                return;
            }

            // Add Submodule list to hash.
            ArrayList submodulelist = new ArrayList();

            while (nodes.MoveNext())
            {
                if (nodes.Current == null)
                {
                    continue;
                }

                string sfixed = nodes.Current.GetAttribute(Attributes.s_FixedInSubslots, String.Empty);
                string sused = nodes.Current.GetAttribute(Attributes.s_UsedInSubslots, String.Empty);
                string sallowed = nodes.Current.GetAttribute(Attributes.s_AllowedInSubslots, String.Empty);

                bool submoduleInCatalog = IsSubmoduleInCatalog(sfixed, sused, sallowed);

                if (submoduleInCatalog)
                {
                    submodulelist.Add(SStore.PhysicalSubmodules[nodes.Current.GetAttribute(Attributes.s_SubmoduleItemTarget, String.Empty)]);
                }
            }
            submodulelist.TrimToSize();

            hash[Models.s_FieldSubmodules] = submodulelist;
        }
        #endregion

        #region Creation


        protected override void CreateSubmoduleStructureElements()
        {

            // Select all Submodules
            XPathNavigator nav = Gsd.CreateNavigator();

            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_SubmoduleItem]);


            // Check whether Submodules are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each Submodule, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectSubmoduleStructureElement;
                SubmoduleStructureElement obj = (SubmoduleStructureElement)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSubmoduleStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.PhysicalSubmodules.Add(obj.GsdID, obj);
            }
        }

        protected override void CreateModuleStructureElements()
        {
            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();

            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_ModuleItem]);

            // Check whether Modules are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each Module, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectModuleStructureElement;
                ModuleStructureElement obj = (ModuleStructureElement)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectModuleStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.Modules.Add(obj.GsdID, obj);
            }

        }

        #endregion

        #endregion

    }
}


