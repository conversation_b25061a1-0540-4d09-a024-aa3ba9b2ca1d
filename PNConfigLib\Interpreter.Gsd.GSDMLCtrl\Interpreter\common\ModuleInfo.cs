/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleInfo.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ModuleInfo object contains general information about a accesspoint,
    /// module or submodule.
    /// </summary>
    public class ModuleInfo :
        GsdObject,
        GSDI.IModuleInfo
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ModuleInfo if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ModuleInfo()
        {
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
            m_InfoText = String.Empty;
            m_Category = String.Empty;
            m_CategoryGsdID = String.Empty;
            m_CategoryTextId = String.Empty;
            m_CategoryInfoText = String.Empty;
            m_CategoryInfoTextId = String.Empty;
            m_SubCategory1 = String.Empty;
            m_SubCategory1GsdID = String.Empty;
            m_SubCategory1TextId = String.Empty;
            m_SubCategory1InfoText = String.Empty;
            m_SubCategory1InfoTextId = String.Empty;
            m_VendorName = String.Empty;
            m_OrderNumber = String.Empty;
            m_HardwareRelease = String.Empty;
            m_SoftwareRelease = String.Empty;
            m_MainFamily = String.Empty;
            m_MainFamilyAsEnum = GSDI.MainFamilies.GSDMfGeneral;
            m_ProductFamily = String.Empty;
            m_InfoTextID = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_Name;
        private string m_NameTextID;
        private string m_InfoText;
        private string m_InfoTextID;
        private string m_Category;
        private string m_CategoryGsdID; // only private
        private string m_CategoryTextId;
        private string m_CategoryInfoText;
        private string m_CategoryInfoTextId;
        private string m_SubCategory1;
        private string m_SubCategory1GsdID; // only private
        private string m_SubCategory1TextId;
        private string m_SubCategory1InfoText;
        private string m_SubCategory1InfoTextId;
        private string m_VendorName;
        private string m_OrderNumber;
        private string m_HardwareRelease;
        private string m_SoftwareRelease;
        private string m_MainFamily;
        private GSDI.MainFamilies m_MainFamilyAsEnum;
        private string m_ProductFamily;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the language dependent name of a accesspoint, module 
        /// or submodule.
        /// </summary>
        public string Name => this.m_Name;

        /// <summary>
		/// Accesses the language independent text id of the name of a
		/// accesspoint, module or submodule.
		/// </summary>
		public string NameTextID => this.m_NameTextID;

        /// <summary>
		/// Accesses the language dependent info text, which contains human 
		/// readable text information about a accesspoint, module or submodule.
		/// </summary>
		public string InfoText => this.m_InfoText;

        /// <summary>
		/// Accesses the InfoText's TextId of an module. Not accessible via COM.
		/// </summary>
		public string InfoTextID => this.m_InfoTextID;

        /// <summary>
		/// Accesses the category of an module.
		/// </summary>
		public string Category => this.m_Category;

        /// <summary>
		/// Accesses the category text id of an module.
		/// </summary>
		public string CategoryTextId => this.m_CategoryTextId;

        /// <summary>
		/// Accesses the category infotext of an module.
		/// </summary>
		public string CategoryInfoText => this.m_CategoryInfoText;

        /// <summary>
		/// Accesses the category infotext of an module.
		/// </summary>
		public string CategoryInfoTextId => this.m_CategoryInfoTextId;

        /// <summary>
		/// Accesses the subcategory of an module, which can be created
		/// within a category.
		/// </summary>
		public string SubCategory1 => this.m_SubCategory1;

        /// <summary>
		/// Accesses the subcategory of an module, which can be created
		/// within a category.
		/// </summary>
		public string SubCategory1TextId => this.m_SubCategory1TextId;

        /// <summary>
		/// Accesses the category infotext of an module.
		/// </summary>
		public string SubCategory1InfoText => this.m_SubCategory1InfoText;

        /// <summary>
		/// Accesses the category infotext of an module.
		/// </summary>
		public string SubCategory1InfoTextId => this.m_SubCategory1InfoTextId;

        /// <summary>
		/// Accesses the vendor name of the device vendor.
		/// </summary>
		public string VendorName => this.m_VendorName;

        /// <summary>
		/// Accesses the order number of a accesspoint, module or submodule.
		/// </summary>
		public string OrderNumber => this.m_OrderNumber;

        /// <summary>
		/// Accesses the hardware release of a accesspoint, module or submodule.
		/// </summary>
		public string HardwareRelease => this.m_HardwareRelease;

        /// <summary>
		/// Accesses the software release of a accesspoint, module or submodule.
		/// </summary>
		public string SoftwareRelease => this.m_SoftwareRelease;

        /// <summary>
		/// Accesses the MainFamily of the accesspoint, module or submodule, 
		/// which specifies the assignment to a function class.
		/// </summary>
		public string MainFamily => this.m_MainFamily;

        /// <summary>
		/// Accesses the ProductFamily of the accesspoint, module or submodule, 
		/// which contains the vendor specific assignment of the device to a 
		/// product family.
		/// </summary>
		public string ProductFamily => this.m_ProductFamily;

        #endregion

        //########################################################################################
        #region Methods
        /// <summary>
		/// Accesses the string and the enumeration representation of the MainFamily 
		/// of the Device.
		/// </summary>
		/// <param name="plMainFamily">Outparameter to which is written the 
		/// enumeration representation of the MainFamily.</param>
		/// <returns>The string representation of the MainFamily.</returns>
		public virtual string GetMainFamily(out GSDI.MainFamilies plMainFamily)
        {
            plMainFamily = this.m_MainFamilyAsEnum;
            return this.m_MainFamily;
        }
        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");
                FillFieldName(hash);

                FillFieldNameTextID(hash);

                FillFieldInfoText(hash);

                FillFieldInfoTextID(hash);

                FillFieldCategory(hash);

                FillFieldCategoryTextId(hash);

                FillFieldCategoryGsdID(hash);

                FillFieldCategoryInfoText(hash);

                FillFieldCategoryInfoTextId(hash);

                FillFieldSubCategory1(hash);

                FillFieldSubCategory1GsdID(hash);

                FillFieldSubCategory1TextId(hash);

                FillFieldSubCategory1InfoText(hash);

                FillFieldSubCategory1InfoTextId(hash);

                FillFieldOrderNumber(hash);

                FillFieldHardwareRelease(hash);

                FillFieldSoftwareRelease(hash);

                FillFieldMainFamily(hash);

                FillFieldProductFamily(hash);

                FillFieldVendorName(hash);

                FillFieldMainFamilyAsEnum(hash);



            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        private void FillFieldMainFamilyAsEnum(Hashtable hash)
        {
            string member = Models.s_FieldMainFamilyAsEnum;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.MainFamilies)
                this.m_MainFamilyAsEnum = (GSDI.MainFamilies)hash[member];
        }

        private void FillFieldVendorName(Hashtable hash)
        {
            string member = Models.s_FieldVendorName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_VendorName = hash[member] as string;
        }

        private void FillFieldProductFamily(Hashtable hash)
        {
            string member = Models.s_FieldProductFamily;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_ProductFamily = hash[member] as string;
        }

        private void FillFieldMainFamily(Hashtable hash)
        {
            string member = Models.s_FieldMainFamily;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_MainFamily = hash[member] as string;
        }

        private void FillFieldSoftwareRelease(Hashtable hash)
        {
            string member = Models.s_FieldSoftwareRelease;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SoftwareRelease = hash[member] as string;
        }

        private void FillFieldHardwareRelease(Hashtable hash)
        {
            string member = Models.s_FieldHardwareRelease;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_HardwareRelease = hash[member] as string;
        }

        private void FillFieldOrderNumber(Hashtable hash)
        {
            string member = Models.s_FieldOrderNumber;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_OrderNumber = hash[member] as string;
        }

        private void FillFieldSubCategory1InfoTextId(Hashtable hash)
        {
            string member = Models.s_FieldSubCategory1InfoTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SubCategory1InfoTextId = hash[member] as string;
        }

        private void FillFieldSubCategory1InfoText(Hashtable hash)
        {
            string member = Models.s_FieldSubCategory1InfoText;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SubCategory1InfoText = hash[member] as string;
        }

        private void FillFieldSubCategory1TextId(Hashtable hash)
        {
            string member = Models.s_FieldSubCategory1TextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SubCategory1TextId = hash[member] as string;
        }

        private void FillFieldSubCategory1GsdID(Hashtable hash)
        {
            string member = Models.s_FieldSubCategory1GsdId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SubCategory1GsdID = hash[member] as string;
        }

        private void FillFieldSubCategory1(Hashtable hash)
        {
            string member = Models.s_FieldSubCategory1;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_SubCategory1 = hash[member] as string;
        }

        private void FillFieldCategoryInfoTextId(Hashtable hash)
        {
            string member = Models.s_FieldCategoryInfoTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_CategoryInfoTextId = hash[member] as string;
        }

        private void FillFieldCategoryInfoText(Hashtable hash)
        {
            string member = Models.s_FieldCategoryInfoText;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_CategoryInfoText = hash[member] as string;
        }

        private void FillFieldCategoryGsdID(Hashtable hash)
        {
            string member = Models.s_FieldCategoryGsdId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_CategoryGsdID = hash[member] as string;
        }

        private void FillFieldCategoryTextId(Hashtable hash)
        {
            string member = Models.s_FieldCategoryTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_CategoryTextId = hash[member] as string;
        }

        private void FillFieldCategory(Hashtable hash)
        {
            string member = Models.s_FieldCategory;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_Category = hash[member] as string;
        }

        private void FillFieldInfoTextID(Hashtable hash)
        {
            string member = Models.s_FieldInfoTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_InfoTextID = hash[member] as string;
        }

        private void FillFieldInfoText(Hashtable hash)
        {
            string member = Models.s_FieldInfoText;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_InfoText = hash[member] as string;
        }

        private void FillFieldNameTextID(Hashtable hash)
        {
            string member = Models.s_FieldNameTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_NameTextID = hash[member] as string;
        }

        private void FillFieldName(Hashtable hash)
        {
            string member = Models.s_FieldName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_Name = hash[member] as string;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectModuleInfo);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();


            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, this.m_NameTextID);
            Export.WriteStringProperty(ref writer, Models.s_FieldInfoText, this.m_InfoText);
            Export.WriteStringProperty(ref writer, Models.s_FieldCategory, this.m_Category);
            Export.WriteStringProperty(ref writer, Models.s_FieldCategoryGsdId, this.m_CategoryGsdID);
            Export.WriteStringProperty(ref writer, Models.s_FieldCategoryInfoText, this.m_CategoryInfoText);
            Export.WriteStringProperty(ref writer, Models.s_FieldCategoryInfoTextId, this.m_CategoryInfoTextId);
            Export.WriteStringProperty(ref writer, Models.s_FieldSubCategory1, this.m_SubCategory1);
            Export.WriteStringProperty(ref writer, Models.s_FieldSubCategory1GsdId, this.m_SubCategory1GsdID);
            Export.WriteStringProperty(ref writer, Models.s_FieldSubCategory1InfoText, this.m_SubCategory1InfoText);
            Export.WriteStringProperty(ref writer, Models.s_FieldCategoryInfoTextId, this.m_SubCategory1InfoTextId);
            Export.WriteStringProperty(ref writer, Models.s_FieldVendorName, this.m_VendorName);
            Export.WriteStringProperty(ref writer, Models.s_FieldOrderNumber, this.m_OrderNumber);
            Export.WriteStringProperty(ref writer, Models.s_FieldHardwareRelease, this.m_HardwareRelease);
            Export.WriteStringProperty(ref writer, Models.s_FieldSoftwareRelease, this.m_SoftwareRelease);
            Export.WriteStringProperty(ref writer, Models.s_FieldMainFamily, this.m_MainFamily);
            Export.WriteEnumProperty(ref writer, Models.s_FieldMainFamilyAsEnum, this.m_MainFamilyAsEnum.ToString(), Export.s_SubtypeMainFamilies);
            Export.WriteStringProperty(ref writer, Models.s_FieldProductFamily, this.m_ProductFamily);

            return true;
        }

        #endregion

    }
}


