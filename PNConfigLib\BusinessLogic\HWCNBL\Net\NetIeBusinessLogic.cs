/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NetIeBusinessLogic.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Networks;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;
using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.Net
{
    /// <summary>
    /// The PROFINET related business logic class for Subnet.
    /// </summary>
    internal class NetIeBusinessLogic : NetBusinessLogic, INetIeBusinessLogic
    {
        /// <summary>
        /// Constructor for NetIeBusinessLogic.
        /// </summary>
        /// <param name="subnet">The subnet.</param>
        internal NetIeBusinessLogic(Subnet subnet) : base(subnet)
        {
            Subnet.NetBL = this;
            Initialize();
        }

        #region Constants and Enums

        // Contains all constants and enums
        /// <summary>
        /// Address types.
        /// </summary>
        private enum AddressTypeIE
        {
            Mac,

            IP,

            IPv6,

            DhcpClientId
        }

        #endregion

        #region Overrides and Overridables

        #endregion

        /// <summary>
        /// Checks the addresses of all nodes that are connected to this subnet.
        /// </summary>
        private void CheckNodeAddresses()
        {
            ConsistencyCheckDataNodesIE nodeData;
            // create consistency check data object and add it to the method data cache
            if (!ConsistencyManager.s_NodeInfos.TryGetValue(Subnet.Id, out nodeData))
            {
                nodeData = new ConsistencyCheckDataNodesIE();
            }

            nodeData.MultipleIPAddresses = GetMultipleAddresses<long>(AddressTypeIE.IP);
            ConsistencyManager.s_NodeInfos[Subnet.Id] = nodeData;
        }

        /// <summary>
        /// Makes the necessary consistency checks for the subnet.
        /// </summary>
        private void ConsistencyCheck()
        {
            // check the uniquness of names of interface
            CheckNameOfInterface();

            // check the uniquness of names of station
            CheckNameOfStation();
            CheckSyncDomainNamesOfSubnet();

            CheckNodeAddresses();

            CheckRingTopology();
            foreach (DataModel.PCLObjects.Node currNode in Subnet.Nodes)
            {
                CheckIoDevicesNotAssigendToController(currNode);
            }
        }

        /// <summary>
        /// This method checks if there are any rings which don't contain any 
        /// mrp-active interfaces. It produces a warning in such a case. (MRP-K6(2/2))
        /// </summary>
        private void CheckRingTopology()
        {
            List<Interface> interfaceSubmodules = NavigationUtilities.GetInterfacesOfSubnet(Subnet);
            List<List<Interface>> allRingsOfNet = PNTopologyUtilities.GetRings(interfaceSubmodules);
            if (allRingsOfNet == null)
            {
                // Net doesn't contain any rings (Or ports are not interconnected).
                return;
            }

            foreach (List<Interface> ring in allRingsOfNet)
            {
                // Check if a ring which contains non-mrp or mrp-passive members
                bool isMrpRing = true;

                List<Interface> ifsWithoutMediaRedundancy = new List<Interface>();
                List<Interface> ifsWithoutActiveMrp = new List<Interface>();
                List<Interface> ifsWithUnknownMediaRedundancy = new List<Interface>();
                List<Interface> ifsWithMrpSelectedPortMismatch = new List<Interface>();
                List<Interface> ifsWithAdditionalRedundancy = new List<Interface>();

                foreach (Interface interfaceSubmodule in ring)
                {
                    // if there is a docking port in the ring, than do not check
                    bool isDockingPortInRing = IsDockingPortInRing(interfaceSubmodule, ring);
                    if (isDockingPortInRing)
                        return;

                    // Check if the mrp ring port of the interface is used in the ring, if so don't check the ring
                    // anymore since mrp related tests are done in PNFunctions.
                    bool isInterfaceInMrpRing = IsInterfaceInMrpRing(ring, interfaceSubmodule);
                    isMrpRing = isMrpRing && isInterfaceInMrpRing;
                    if (isInterfaceInMrpRing)
                    {
                        continue;
                    }

                    CheckAdditionalMediaRedundancy(interfaceSubmodule, ifsWithUnknownMediaRedundancy,
                        ifsWithoutActiveMrp, ifsWithMrpSelectedPortMismatch, ifsWithoutMediaRedundancy,
                        ifsWithAdditionalRedundancy);
                }
                // If all interfaces support additional media redundancy protocols, don't
                // create an error or a warning. 
                if (isMrpRing)
                {
                    continue;
                }

                bool hasErrors = CreateRingTopologyErrorsIfNecessary(
                    ifsWithoutActiveMrp, ifsWithMrpSelectedPortMismatch, ifsWithoutMediaRedundancy);
                if (hasErrors)
                {
                    // If there are consistency errors, no need to display additional consistency warnings
                    return;
                }

                // If there are at least one interface whose media redundancy support is 
                // unknown, create a warning
                CreateRingTopologyWarningsIfNecessary(ifsWithUnknownMediaRedundancy, ifsWithAdditionalRedundancy);
            }
        }

        /// <summary>
        /// Checks and generates consistency warnings if there is at least one interface submodule which has
        /// an unknown media redundancy status (i.e. it is not known if the interface submodule really supports
        /// any media redundancy protocol).
        /// </summary>
        /// <param name="ifsWithUnknownMediaRedundancy"></param>
        private void CreateRingTopologyWarningsIfNecessary(IReadOnlyCollection<Interface> ifsWithUnknownMediaRedundancy,
            List<Interface> ifsWithAdditionalRedundancy)
        {
            if (ifsWithUnknownMediaRedundancy.Count == 0 && ifsWithAdditionalRedundancy.Count == 0)
            {
                return;
            }
            foreach (Interface ifSubmodule in ifsWithUnknownMediaRedundancy)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, ifSubmodule, ConsistencyConstants.RingWithUnknownMediaRedundancy,
                        Utility.GetNameWithContainer(ifSubmodule));
            }
            foreach (Interface ifSubmodule in ifsWithAdditionalRedundancy)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, ifSubmodule, ConsistencyConstants.IfWithAdditionalRedundancy,
                    Utility.GetNameWithContainer(ifSubmodule));
            }
        }

        /// <summary>
        /// Checks and creates error messages if there are at least one interface
        /// which doesn't support any media redundancy protocol OR
        /// which supports mrp but not active OR 
        /// Is active but selected ring ports don't match with the actual ring ports, 
        /// </summary>
        /// <param name="ifsWithoutActiveMrp"></param>
        /// <param name="ifsWithMrpSelectedPortMismatch"></param>
        /// <param name="ifsWithoutMediaRedundancy"></param>
        /// <returns>True if there are errors.</returns>
        private bool CreateRingTopologyErrorsIfNecessary(
            IReadOnlyCollection<Interface> ifsWithoutActiveMrp, IReadOnlyCollection<Interface> ifsWithMrpSelectedPortMismatch,
            IReadOnlyCollection<Interface> ifsWithoutMediaRedundancy)
        {
            if ((ifsWithoutMediaRedundancy.Count != 0) || (ifsWithoutActiveMrp.Count != 0) ||
                (ifsWithMrpSelectedPortMismatch.Count != 0))
            {
                foreach (Interface ifSubmodule in ifsWithoutMediaRedundancy)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, ifSubmodule, ConsistencyConstants.RingWithoutMediaRedundancy,
        Utility.GetNameWithContainer(ifSubmodule));
                }
                foreach (Interface ifSubmodule in ifsWithoutActiveMrp)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, ifSubmodule, ConsistencyConstants.RingWithoutActiveMrp,
                        Utility.GetNameWithContainer(ifSubmodule));
                }
                foreach (Interface ifSubmodule in ifsWithMrpSelectedPortMismatch)
                {

                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, ifSubmodule, ConsistencyConstants.RingWithMrpSelectedPortMismatch,
                        Utility.GetNameWithContainer(ifSubmodule));
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// Checks if the given interface submodule supports additional media redundancy protocols and updates the
        /// related lists accordingly.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="ifsWithUnknownMediaRedundancy"></param>
        /// <param name="ifsWithoutActiveMrp"></param>
        /// <param name="ifsWithMrpSelectedPortMismatch"></param>
        /// <param name="ifsWithoutMediaRedundancy"></param>
        /// <param name="ifsWithAdditionalRedundancy"></param>
        private void CheckAdditionalMediaRedundancy(Interface interfaceSubmodule,
                List<Interface> ifsWithUnknownMediaRedundancy, List<Interface> ifsWithoutActiveMrp,
                List<Interface> ifsWithMrpSelectedPortMismatch, List<Interface> ifsWithoutMediaRedundancy,
                List<Interface> ifsWithAdditionalRedundancy)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool alternativeRedundancyActive = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnAlternativeRedundancy, ac, false);

            if (!IsPNAddMediaRedundancyProtSupported(interfaceSubmodule))
            {
                // Interface doesn't have the attribute.
                ifsWithUnknownMediaRedundancy.Add(interfaceSubmodule);
            }
            else if (!alternativeRedundancyActive)

            {
                if (PNAddMediaRedundancyProtSupported(interfaceSubmodule))
                {
                    ifsWithAdditionalRedundancy.Add(interfaceSubmodule);
                    return;
                }
                bool isMultipleMrpActive = false;

                bool pnMrpSupported = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnMrpSupported,
                    ac.GetNew(),
                    false);
                if (pnMrpSupported)
                {
                    IMethodData isMultipleMrpActiveMethodData = new MethodData();
                    isMultipleMrpActiveMethodData.Name = GetIsMultipleMrpActive.Name;
                    interfaceSubmodule.BaseActions.CallMethod(isMultipleMrpActiveMethodData);
                    isMultipleMrpActive =
                        (bool)isMultipleMrpActiveMethodData.Arguments[GetIsMultipleMrpActive.IsMultipleMrpActive];
                }
                if (pnMrpSupported
                    && ((PNMrpRole)interfaceSubmodule.AttributeAccess
                                       .GetAnyAttribute<UInt32>(InternalAttributeNames.PnMrpRole, new AttributeAccessCode(),
                                           (UInt32)PNMrpRole.NotInRing) == PNMrpRole.NotInRing)
                    && !isMultipleMrpActive)
                {
                    ifsWithoutActiveMrp.Add(interfaceSubmodule);
                }
                else if (pnMrpSupported)
                {
                    // Interface is an active mrp interface, but the selected mrp ring ports don't match
                    // with the ports in the ring.
                    ifsWithMrpSelectedPortMismatch.Add(interfaceSubmodule);
                }
                else
                {
                    ifsWithoutMediaRedundancy.Add(interfaceSubmodule);
                }
            }
        }

        private bool IsPNAddMediaRedundancyProtSupported(PclObject interfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnAddMediaRedundancyProtSupported,
                ac,
                false);
            return ac.IsOkay;
        }

        private bool PNAddMediaRedundancyProtSupported(PclObject interfaceSubmodule)
        {
            return interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnAddMediaRedundancyProtSupported,
                new AttributeAccessCode(),
                false);
        }

        private static bool IsDockingPortInRing(Interface interfaceSubmodule, List<Interface> ring)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(interfaceSubmodule);

            foreach (DataModel.PCLObjects.Port port in ports)
            {
                IList<DataModel.PCLObjects.Port> partnerPorts = port.GetPartnerPorts();
                foreach (DataModel.PCLObjects.Port pPort in partnerPorts)
                {
                    Interface actualIntercafe = NavigationUtilities.GetInterfaceOfPort(pPort);
                    if (ring.Contains(actualIntercafe))
                    {
                        // one of the port is toolchanger port
                        if (port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsMultipleConnectionEnabled, new AttributeAccessCode(), false))
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Checks if the given interface submodule is member of the given ring and if the given ring is an mrp ring.
        /// </summary>
        /// <param name="ring"></param>
        /// <param name="interfaceSubmodule"></param>
        /// <returns>
        /// True, if the given ring is and mrp ring and the given interface is an active member of it.
        /// </returns>
        private bool IsInterfaceInMrpRing(List<Interface> ring, Interface interfaceSubmodule)
        {
            bool isMrpRing = false;

            bool pnMrpSupported = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnMrpSupported,
                new AttributeAccessCode(),
                false);

            if (!pnMrpSupported)
            {
                return false;
            }

            PNMrpRole mrpRole = (PNMrpRole)interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole,
                new AttributeAccessCode(),
                (UInt32)PNMrpRole.NotInRing);

            if (mrpRole != PNMrpRole.NotInRing)
            {
                // Interface supports mrp and its role is either client or a manager.
                // But still it must be checked if really its selected ring ports are used in the ring.

                // Get selected ring ports of the interface
                IMethodData methodData = new MethodData { Name = GetSelectedRingPorts.Name };
                interfaceSubmodule.BaseActions.CallMethod(methodData);
                List<DataModel.PCLObjects.Port> selectedRingPorts = (List<DataModel.PCLObjects.Port>)
                    methodData.Arguments[GetSelectedRingPorts.SelectedRingPorts];

                isMrpRing = IsPartnerInterfaceInTheRing(selectedRingPorts, ring);
            }
            else
            {
                IMethodData isMultipleMrpActiveMethodData = new MethodData();
                isMultipleMrpActiveMethodData.Name = GetIsMultipleMrpActive.Name;
                interfaceSubmodule.BaseActions.CallMethod(isMultipleMrpActiveMethodData);
                if ((bool)isMultipleMrpActiveMethodData.Arguments[GetIsMultipleMrpActive.IsMultipleMrpActive])
                {
                    //MultipleMRP is active so instances should be tested if they are on the ring or not
                    return IsMultipleMrpInstanceOfInterfaceInMrpRing(ring, interfaceSubmodule);
                }
            }

            return isMrpRing;
        }

        /// <summary>
        /// Checks if partners of the ring ports are in the ring or not.
        /// </summary>
        /// <param name="selectedRingPorts"></param>
        /// <param name="ring"></param>
        /// <returns></returns>
        private static bool IsPartnerInterfaceInTheRing(
            List<DataModel.PCLObjects.Port> selectedRingPorts,
            ICollection<Interface> ring)
        {
            bool retval = false;
            bool partnerInterfaceInTheRing = true;

            foreach (DataModel.PCLObjects.Port ringPort in selectedRingPorts)
            {
                if (ringPort.GetPartnerPorts() == null
                    || ringPort.GetPartnerPorts().Count == 0)
                {
                    // This port is not interconnected, this means another port of the interface is 
                    // used in the ring.
                    partnerInterfaceInTheRing = false;
                    break;
                }
                // Get the interface of the partner ports and check if they are in the ring
                foreach (DataModel.PCLObjects.Port partnerPort in ringPort.GetPartnerPorts())
                {
                    Interface partnerInterface = NavigationUtilities.GetInterfaceOfPort(partnerPort);
                    if (!ring.Contains(partnerInterface))
                    {
                        partnerInterfaceInTheRing = false;
                        break;
                    }
                }

                if (!partnerInterfaceInTheRing)
                {
                    break;
                }
            }

            if (partnerInterfaceInTheRing)
            {
                // This is a mixed ring or an mrp-ring. Navigate to the next ring.
                retval = true;
            }

            return retval;
        }

        /// <summary>
        /// Checks if any instance of given multiple mrp interface is member of the given ring .
        /// </summary>
        /// <param name="ring"></param>
        /// <param name="interfaceSubmodule"></param>
        /// <returns>
        /// True, if any instance of the given interface is an active member of it given ring.
        /// </returns>
        private static bool IsMultipleMrpInstanceOfInterfaceInMrpRing(List<Interface> ring,
                                                                      PclObject interfaceSubmodule)
        {
            uint pnMrpMaxInstances = interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnMrpMaxInstances,
                new AttributeAccessCode(),
                0);
            //MultipleMRP is active so instances should be tested if they are on the ring or not
            for (int i = 1; i <= pnMrpMaxInstances; i++)
            {
                // Get selected ring ports of the instance
                IMethodData ringPortMethodData = new MethodData();
                ringPortMethodData.Name = GetSelectedRingPortsOfInstance.Name;
                ringPortMethodData.Arguments[GetSelectedRingPortsOfInstance.InstanceNumber] = i;
                interfaceSubmodule.BaseActions.CallMethod(ringPortMethodData);
                List<DataModel.PCLObjects.Port> selectedRingPorts = (List<DataModel.PCLObjects.Port>)
                    ringPortMethodData.Arguments[GetSelectedRingPortsOfInstance.SelectedRingPorts];

                bool partnerInterfaceInTheRing = true;
                foreach (DataModel.PCLObjects.Port ringPort in selectedRingPorts)
                {
                    IList<DataModel.PCLObjects.Port> partnerPorts = ringPort.GetPartnerPorts();
                    if ((partnerPorts == null) || (partnerPorts.Count == 0))
                    {
                        // This port is not interconnected, this means another port of the interface is 
                        // used in the ring.
                        partnerInterfaceInTheRing = false;
                        break;
                    }
                    // Get the interface of the partner ports and check if they are in the ring
                    foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                    {
                        Interface partnerInterface = NavigationUtilities.GetInterfaceOfPort(partnerPort);
                        if (!ring.Contains(partnerInterface))
                        {
                            partnerInterfaceInTheRing = false;
                            break;
                        }
                    }
                    if (!partnerInterfaceInTheRing)
                    {
                        break;
                    }
                }

                if (partnerInterfaceInTheRing)
                {
                    // One of the instances are in the ring
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks whether an IO device is connected to a subnet, but not to an IO system.
        /// </summary>
        /// <param name="node">The Node of the device to be checked.</param>
        /// <returns>A consistency log containing the consistency check result; null if no checks fail.</returns>
        private void CheckIoDevicesNotAssigendToController(PclObject node)
        {
            Interface itfSubmodule = (Interface)node.ParentObject;
            if (itfSubmodule == null)
            {
                return;
            }
            if (itfSubmodule.PNIOD == null)
            {
                return;
            }
            if (NavigationUtilities.GetControllerOfDevice(itfSubmodule) != null)
            {
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, itfSubmodule, ConsistencyConstants.IoDNotAssignedToController,
                AttributeUtilities.GetName(itfSubmodule));
        }
        /// <summary>
        /// Checks the names of all interface that are connected to this subnet.
        /// </summary>
        private void CheckNameOfInterface()
        {
            // create consistency check data object if not exists
            ConsistencyCheckDataNodesIE nodeData;
            // create consistency check data object and add it to the method data cache
            if (!ConsistencyManager.s_NodeInfos.TryGetValue(Subnet.Id, out nodeData))
            {
                nodeData = new ConsistencyCheckDataNodesIE();
            }

            nodeData.MultipleInterfaceNames = GetMultipleNames(false);
            ConsistencyManager.s_NodeInfos[Subnet.Id] = nodeData;
        }

        /// <summary>
        /// Checks the names of all stations that are connected to this subnet.
        /// </summary>
        private void CheckNameOfStation()
        {
            // create consistency check data object if not exists
            ConsistencyCheckDataNodesIE nodeData;
            if (!ConsistencyManager.s_NodeInfos.TryGetValue(Subnet.Id, out nodeData))
            {
                nodeData = new ConsistencyCheckDataNodesIE();
            }

            nodeData.MultipleDeviceNames = GetMultipleNames(true);
            ConsistencyManager.s_NodeInfos[Subnet.Id] = nodeData;
        }

        private void CheckSyncDomainNamesOfSubnet()
        {
            List<SyncDomain> allSyncDomains = Subnet.DomainList.Where(dmn => dmn is SyncDomain)
                .Cast<SyncDomain>().ToList();
            int uniqueNameCount = allSyncDomains
                .Select(
                    syncDmn => syncDmn.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.Name,
                        new AttributeAccessCode(),
                        string.Empty)).Distinct().Count();
            if (uniqueNameCount != allSyncDomains.Count)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Subnet,
                    ConsistencyConstants.SyncDomainNameNotUnique,
                    Subnet.Id);
            }
        }

        /// <summary>
        /// This method gets all multiple names of stations (or interfaces - if getDeviceNames is false)
        /// within this subnet together with a list of the corresponding node objects.
        /// </summary>
        /// <returns>
        /// An IDictionary object with all multiple names of stations together with a list of the corresponding node
        /// objects.
        /// </returns>
        private IDictionary<string, IList<DataModel.PCLObjects.Node>> GetMultipleNames(bool getDeviceNames)
        {
            Dictionary<string, IList<DataModel.PCLObjects.Node>> multipleNames =
                new Dictionary<string, IList<DataModel.PCLObjects.Node>>(10);

            // create a temporary dictionary
            Dictionary<string, DataModel.PCLObjects.Node> temp = new Dictionary<string, DataModel.PCLObjects.Node>();

            foreach (DataModel.PCLObjects.Node node in Subnet.Nodes)
            {
                Interface pnInterface = (Interface)node.ParentObject;

                string name = AttributeUtilities.GetName(pnInterface);
                bool noSAutoGenerate =
                    node.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPnNoSAutoGenerate,
                        new AttributeAccessCode(),
                        false);
                if (getDeviceNames)
                {
                    if (noSAutoGenerate
                        || (node.GetDevice() is CentralDevice
                            && AddressTailorUtility.IsAddressTailoringEnabledIoControllerInterfaceStartObject(
                                pnInterface)))
                    {
                        name =
                            node.AttributeAccess.GetAnyAttribute<string>(
                                InternalAttributeNames.PnNameOfStationVirtual,
                                new AttributeAccessCode(),
                                String.Empty);
                    }
                    else
                    {
                        name = node.AttributeAccess.GetAnyAttribute<string>(
                            InternalAttributeNames.PnNameOfStation,
                            new AttributeAccessCode(),
                            String.Empty);
                    }
                }

                DataModel.PCLObjects.Node doublet;
                // does the current address exist more than once?
                if (temp.TryGetValue(name, out doublet))
                {
                    if ((pnInterface.PNIOD != null)
                        && !((DecentralDevice)pnInterface.ParentObject).IsShared)
                    {
                        IList<DataModel.PCLObjects.Node> nodeList;

                        // try to get the list of nodes corresponding to the current address
                        if (!multipleNames.TryGetValue(name, out nodeList))
                        {
                            // add a new node list with the doublet to the result dictionary
                            nodeList = new List<DataModel.PCLObjects.Node>(5);
                            nodeList.Add(doublet);
                            multipleNames[name] = nodeList;
                        }
                        // add current node to the node list of the result dictionary
                        nodeList.Add(node);
                    }
                }
                else // add current address and node to the temporary dictionary
                {
                    temp[name] = node;
                }
            }

            return multipleNames;
        }

        /// <summary>
        /// Initializes the BL class.
        /// </summary>
        private void Initialize()
        {
            ConsistencyManager.RegisterConsistencyCheck(Subnet, ConsistencyCheck, 2);
        }

        #region INetIEBL Members

        /// <summary> Returns all used IP addresses on this net </summary>
        /// <returns>Hashtable address -> node that uses the address</returns>
        public IDictionary GetUsedIPAddresses()
        {
            return (IDictionary)GetUsedAddresses<long>(AddressTypeIE.IP);
        }

        /// <summary>
        /// Returns all used IP addresses on this net
        /// - You can specify your own node as excluded node, in order to get all
        ///   forbidden addresses for a given node
        /// </summary>
        /// <param name="excludedNode">if excludedNode != null don´t return its address</param>
        /// <returns>Hashtable address -> node that uses the address</returns>
        public IDictionary GetUsedIPAddresses(DataModel.PCLObjects.Node excludedNode)
        {
            return (IDictionary)GetUsedAddresses<long>(AddressTypeIE.IP, excludedNode);
        }

        /// <summary> Returns all used addresses on this net </summary>
        /// <param name="addressType">IP or Mac address</param>
        /// <returns>Hashtable address -> node that uses the address</returns>
        private IDictionary<T, T> GetUsedAddresses<T>(AddressTypeIE addressType) where T : IConvertible
        {
            return GetUsedAddresses<T>(addressType, (DataModel.PCLObjects.Node)null);
        }

        /// <summary>
        /// Returns all used addresses on this net
        /// - You can specify your own node as excluded node, in order to get all forbidden addresses for a given node
        /// </summary>
        /// <param name="addressType">IP or Mac address</param>
        /// <param name="excludedNode">if excludedNode != null don´t return its address</param>
        /// <returns>Hashtable address -> node that uses the address</returns>
        private IDictionary<T, T> GetUsedAddresses<T>(AddressTypeIE addressType, DataModel.PCLObjects.Node excludedNode)
            where T : IConvertible
        {
            IDictionary<T, T> usedAddresses = new Dictionary<T, T>(this.Subnet.Nodes.Count);
            foreach (DataModel.PCLObjects.Node node in this.Subnet.Nodes)
            {
                if ((excludedNode != null)
                    && (node == excludedNode))
                {
                    continue;
                }

                List<T> addresses;
                if (!GetAddressOfNode(out addresses, node, addressType))
                {
                    continue;
                }

                foreach (T address in addresses)
                {
                    usedAddresses[address] = address;
                }
            }
            return usedAddresses;
        }

        /// <summary>
        /// This method gets all multiple addresses within this subnet together with a list of the corresponding node
        /// objects.
        /// </summary>
        /// <param name="addressType">IP or another types</param>
        /// <returns> An IDictionary object with all multiple addresses together with a list of the corresponding node objects. </returns>
        private IDictionary<T, IList<DataModel.PCLObjects.Node>> GetMultipleAddresses<T>(AddressTypeIE addressType)
            where T : IConvertible
        {
            Dictionary<T, IList<DataModel.PCLObjects.Node>> multipleAddresses =
                new Dictionary<T, IList<DataModel.PCLObjects.Node>>(10);

            List<DataModel.PCLObjects.Node> uniqueNodes = Subnet.Nodes.GroupBy(n => n.GetInterface().Id)
                .Select(grp => grp.First()).ToList();

            // create a temporary dictionary
            Dictionary<T, DataModel.PCLObjects.Node> temp =
                new Dictionary<T, DataModel.PCLObjects.Node>(uniqueNodes.Count);

            foreach (DataModel.PCLObjects.Node node in uniqueNodes)
            {
                List<T> currentAddresses;
                if (!GetAddressOfNode(out currentAddresses, node, addressType))
                {
                    continue;
                }

                foreach (T currentAddress in currentAddresses)
                {
                    DataModel.PCLObjects.Node doublet;
                    // does the current address exist more than once?
                    if (temp.TryGetValue(currentAddress, out doublet))
                    {
                        IList<DataModel.PCLObjects.Node> nodeList;
                        // try to get the list of nodes corresponding to the current address
                        if (!multipleAddresses.TryGetValue(currentAddress, out nodeList))
                        {
                            // add a new node list with the doublet to the result dictionary
                            nodeList = new List<DataModel.PCLObjects.Node>(5) { doublet };
                            multipleAddresses[currentAddress] = nodeList;
                        }
                        // add current node to the node list of the result dictionary
                        nodeList.Add(node);
                    }
                    else // add current address and node to the temporary dictionary
                    {
                        temp[currentAddress] = node;
                    }
                }
            }

            return multipleAddresses;
        }

        private bool GetAddressOfNode<T>(
            out List<T> addresses,
            DataModel.PCLObjects.Node node,
            AddressTypeIE addressType) where T : IConvertible
        {
            addresses = new List<T>();
            try
            {
                if (addressType == AddressTypeIE.IP)
                {
                    if (Helper.IsIPConfiguredInProject(node))
                    {
                        IPAddress ipAddr = new IPAddress(node);
                        T address = (T)Convert.ChangeType(ipAddr.AsInt64, typeof(T),CultureInfo.InvariantCulture);
                        addresses.Add(address);
                        return true;
                    }
                }
                else
                {
                    Debug.Assert(false, "Unknown address type for IP Nodes");
                }
            }
            catch (FormatException e)
            {
                Debug.Fail("FormatException {1}", e.Message);
                throw;
            }
            catch (OverflowException e)
            {
                Debug.Fail("OverflowException {1}", e.Message);
                throw;
            }

            return false;
        }

        #endregion
    }
}