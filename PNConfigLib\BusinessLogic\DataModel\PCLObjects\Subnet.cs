/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Subnet.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.HWCNBL.Net;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a Subnet.
    /// </summary>
    public class Subnet : PclObject
    {
        /// <summary>
        /// The list containing the domains under this Subnet.
        /// </summary>
        internal List<Domain> DomainList = new List<Domain>();

        /// <summary>
        /// The list containing the iosystems under this Subnet.
        /// </summary>
        internal List<IOSystem> IOSystems { get; private set; }

        /// <summary>
        /// All nodes connected to this subnet.
        /// </summary>
        internal List<Node> Nodes = new List<Node>();

        /// <summary>
        /// The constructor of Subnet.
        /// </summary>
        /// <param name="subnetID">The ID of the Subnet.</param>
        public Subnet(string subnetID)
        {
            Id = subnetID;
        }

        /// <summary>
        /// The business logic object of this Subnet.
        /// </summary>
        public INetIeBusinessLogic NetBL { get; set; }

        internal void AddIOSystem(IOSystem iosystem)
        {
            if (IOSystems == null)
            {
                IOSystems = new List<IOSystem>();
            }
            IOSystems.Add(iosystem);
            iosystem.Subnet = this;
        }
    }
}