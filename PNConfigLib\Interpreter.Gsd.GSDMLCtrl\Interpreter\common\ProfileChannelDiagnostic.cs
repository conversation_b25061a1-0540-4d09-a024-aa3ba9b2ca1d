/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ProfileChannelDiagnostic.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ProfileChannelDiagnostic object describes channel type specific error texts.
    /// It contains properties to specify the error type of a specific channel.
    /// </summary>
    public class ProfileChannelDiagnostic :
        GsdObject,
        GSDI.IProfileChannelDiagnostic,
        GSDI.IProfileChannelDiagnostic2,
        GSDI.IProfileChannelDiagnostic3
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ProfileChannelDiagnostic if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ProfileChannelDiagnostic()
        {
            m_ErrorType = 0;
            m_Api = 0;
            m_Name = String.Empty;
            m_Help = String.Empty;
            m_HelpTextId = String.Empty;
            m_NameTextId = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private uint m_ErrorType;
        private uint m_Api;
        private string m_Name;
        private string m_NameTextId;
        private string m_Help;
        private string m_HelpTextId;
        private ArrayList m_ProfileExtendedChannelDiagnostics;
        private ArrayList m_ExtChannelAddValueDataItems;
        private ArrayList m_ExtendedChannelDiagnostics;
        private ArrayList m_ChannelDiagExtChannelAddValueDataItems;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the error type where the diagnostic text definitions can 
        /// be assigned.
        /// </summary>
        public UInt32 ErrorType => m_ErrorType;

        /// <summary>
        /// Accesses the API definition for the channel diagnostic.
        /// </summary>
        public UInt32 API => m_Api;

        /// <summary>
        /// Accesses the name for the channel diagnostic.
        /// </summary>
        public string Name => m_Name;

        /// <summary>
        /// Accesses the name text id for the channel diagnostic.
        /// </summary>
        public string NameTextId => m_NameTextId;

        /// <summary>
        /// Accesses the help text for the channel diagnostic.
        /// </summary>
        public string Help => m_Help;

        /// <summary>
        /// Accesses the help text id for the channel diagnostic. Not accessible via COM
        /// </summary>
        public string HelpTextId => m_HelpTextId;

        /// <summary>
        /// Accesses the list of extended channel diagnostic objects.
        /// </summary>
        public Array ProfileExtendedChannelDiagnostics =>
            m_ProfileExtendedChannelDiagnostics?.ToArray();

        /// <summary>
        /// Accesses the list of extended channel diagnostic objects.
        /// </summary>
        public Array ExtChannelAddValueDataItems =>
            m_ExtChannelAddValueDataItems?.ToArray();

        /// <summary>
        /// Accesses the list of extended channel diagnostic objects.
        /// </summary>
        public Array ExtendedChannelDiagnostics =>
            m_ExtendedChannelDiagnostics?.ToArray();

        /// <summary>
        /// Accesses the list of extended channel diagnostic objects.
        /// </summary>
        public Array ChannelDiagExtChannelAddValueDataItems =>
            m_ChannelDiagExtChannelAddValueDataItems?.ToArray();
        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter must not be 'null'!");

                // Own data.
                FillFieldErrorType(hash);

                FillFieldAPI(hash);

                FillFieldName(hash);

                FillFieldNameTextID(hash);

                FillFieldHelp(hash);

                FillFieldHelpTextId(hash);

                FillFieldProfileExtendedChannelDiagnostics(hash);

                FillFieldProfileExtChannelAddValueDataItems(hash);

                FillFieldExtendedChannelDiagnostics(hash);

                FillFieldExtChannelAddValueDataItems(hash);

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        private void FillFieldExtChannelAddValueDataItems(Hashtable hash)
        {
            string member = Models.s_FieldExtChannelAddValueDataItems;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ChannelDiagExtChannelAddValueDataItems = hash[member] as ArrayList;
        }

        private void FillFieldExtendedChannelDiagnostics(Hashtable hash)
        {
            string member = Models.s_FieldExtendedChannelDiagnostics;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ExtendedChannelDiagnostics = hash[member] as ArrayList;
        }

        private void FillFieldProfileExtChannelAddValueDataItems(Hashtable hash)
        {
            string member = Models.s_FieldProfileExtChannelAddValueDataItems;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ExtChannelAddValueDataItems = hash[member] as ArrayList;
        }

        private void FillFieldProfileExtendedChannelDiagnostics(Hashtable hash)
        {
            string member = Models.s_FieldProfileExtendedChannelDiagnostics;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ProfileExtendedChannelDiagnostics = hash[member] as ArrayList;
        }

        private void FillFieldHelpTextId(Hashtable hash)
        {
            string member = Models.s_FieldHelpTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_HelpTextId = hash[member] as string;
        }

        private void FillFieldHelp(Hashtable hash)
        {
            string member = Models.s_FieldHelp;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_Help = hash[member] as string;
        }

        private void FillFieldNameTextID(Hashtable hash)
        {
            string member = Models.s_FieldNameTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_NameTextId = hash[member] as string;
        }

        private void FillFieldName(Hashtable hash)
        {
            string member = Models.s_FieldName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_Name = hash[member] as string;
        }

        private void FillFieldAPI(Hashtable hash)
        {
            string member = Models.s_FieldApi;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_Api = (uint)hash[member];
        }

        private void FillFieldErrorType(Hashtable hash)
        {
            string member = Models.s_FieldErrorType;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_ErrorType = (uint)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectProfileChannelDiagnostic);

            // ----------------------------------------------
            SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldErrorType, m_ErrorType);
            Export.WriteUint32Property(ref writer, Models.s_FieldApi, m_Api);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, m_NameTextId);
            Export.WriteStringProperty(ref writer, Models.s_FieldHelp, m_Help);
            //
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldProfileExtendedChannelDiagnostics, m_ProfileExtendedChannelDiagnostics);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldProfileExtChannelAddValueDataItems, m_ExtChannelAddValueDataItems);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldExtendedChannelDiagnostics, m_ExtendedChannelDiagnostics);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldExtChannelAddValueDataItems, m_ChannelDiagExtChannelAddValueDataItems);

            return true;
        }

        #endregion
    }
}


