/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NodeIPConfiguration.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.AttributeValues
{
    /// <summary>
    /// Possible values for attribute NodeIPConfiguration.
    /// </summary>
    /// <remarks>
    /// The attribute NodeIPConfiguration describes the way the node gets its IP address suite.
    /// More values will be defined for IP config level 2.
    /// </remarks>
    public enum NodeIPConfiguration
    {
        /// <summary>
        /// Error value
        /// </summary>
        None = -1,

        /// <summary>
        /// IP suite configured within project.
        /// </summary>
        Project = 0,

        /// <summary>
        /// IP suite managed via DHCP protocol (DHCP Client ID necessary).
        /// </summary>
        DHCP = 1,

        /// <summary>
        /// IP suite set via FB (function block).
        /// </summary>
        Application = 2,

        /// <summary>
        /// IP suite set via other (third party tool, for example PST tool).
        /// </summary>
        Other = 3,

        /// <summary>
        /// IP suite set via AddressTailoring.
        /// </summary>
        ViaIoController = 4
    }
}