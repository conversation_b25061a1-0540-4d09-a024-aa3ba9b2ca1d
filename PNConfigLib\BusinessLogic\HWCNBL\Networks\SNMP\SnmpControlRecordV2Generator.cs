﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SnmpControlRecordV2Generator.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Networks.SNMP._Interfaces;

#endregion

namespace PNConfigLib.HWCNBL.Networks.SNMP
{
    /// <summary>
    /// DLC to create SNMP control record for S7Plus PLCs
    /// </summary>
    internal class SnmpControlRecordV2Generator : ISnmpControlRecordGenerator
    {
        #region Constants

        private const int s_HeaderLength = 4;

        private const int s_DataStubLength = 8;

        private const int s_MaxCommunityNameLength = 240;

        private const int s_BlockLengthFieldPosition = 2;

        private const int s_ControlFieldPosition = 8;

        private const int s_LengthCNReadFieldPosition = 10;

        private const int s_LengthCNWriteFieldPosition = 11;

        private const int s_CNReadFieldPosition = 12;

        private const int s_ByteAlignment = 4;

        private readonly byte[] s_StartingBytes = { 240, 3, 0, 0, 2, 0, 0, 0, 0, 0 };

        #endregion

        #region ISnmpControlRecordGenerator members

        /// <summary>
        /// Generate SNMP control record
        /// </summary>
        /// <param name="headModuleDto"></param>
        /// <returns></returns>
        public byte[] GenerateSnmpControlRecord(PclObject pclObject)
        {
            #region Null check

            if (pclObject == null)
            {
                throw new ArgumentNullException(nameof(pclObject));
            }

            #endregion

            AttributeAccessCode ac = new AttributeAccessCode();

            bool snmpEnabled = pclObject.AttributeAccess.GetAnyAttribute(InternalAttributeNames.SnmpEnabled, ac, false);
            if (!ac.IsOkay)
            {
                return new byte[0];
                //throw new InvalidOperationException("The attribute SNMPEnabled can not be obtained.");
            }

            string snmpReadOnlyCommunityName = pclObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.SnmpReadOnlyCommunityName,
                ac,
                "public");
            if (snmpReadOnlyCommunityName.Length < 1
                || snmpReadOnlyCommunityName.Length > s_MaxCommunityNameLength)
            {
                throw new InvalidOperationException(
                    "The length of SNMPReadOnlyCommunityName attribute is outside the valid range.");
            }

            string snmpReadWriteCommunityName = pclObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.SnmpReadWriteCommunityName,
                ac,
                "private");
            if (snmpReadWriteCommunityName.Length < 1
                || snmpReadWriteCommunityName.Length > s_MaxCommunityNameLength)
            {
                throw new InvalidOperationException(
                    "The length of the SNMPReadWriteCommunityName attribute is outside the valid range.");
            }

            bool snmpReadWriteAccess = !pclObject.AttributeAccess.GetAnyAttribute(
                                           InternalAttributeNames.SnmpEnableReadOnly,
                                           ac,
                                           false);

            ISnmpControlRecordHelper helper = new SnmpControlRecordHelper();

            byte[] controlArray = helper.CreateArrayFromControl(snmpEnabled, snmpReadWriteAccess);
            byte[] readArray = helper.CreateArrayFromCommunityName(snmpReadOnlyCommunityName);
            byte[] writeArray = helper.CreateArrayFromCommunityName(snmpReadWriteCommunityName);

            return GenerateBlockFromData(readArray, writeArray, controlArray, helper);
        }

        #endregion

        #region Private methods

        private byte[] GenerateBlockFromData(
            byte[] communityNameReadArray,
            byte[] communityNameWriteArray,
            byte[] controlArray,
            ISnmpControlRecordHelper helper)
        {
            int dataLengthWithCNRead = s_DataStubLength + communityNameReadArray.Length;
            int paddingAfterReadCN = helper.CalculatePadding(s_HeaderLength + dataLengthWithCNRead, s_ByteAlignment);
            int dataLength = dataLengthWithCNRead + paddingAfterReadCN + communityNameWriteArray.Length;
            int paddingAtEndOfBlock = helper.CalculatePadding(s_HeaderLength + dataLength, s_ByteAlignment);
            int dataBlockLength = dataLength + paddingAtEndOfBlock;
            int totalBlockLength = s_HeaderLength + dataBlockLength;

            byte[] generatedBlock = new byte[totalBlockLength];
            byte[] dataBlockLengthArray = BitConverter.GetBytes((ushort)dataBlockLength);
            Array.Reverse(dataBlockLengthArray);

            Buffer.BlockCopy(s_StartingBytes, 0, generatedBlock, 0, s_StartingBytes.Length);
            Buffer.BlockCopy(
                dataBlockLengthArray,
                0,
                generatedBlock,
                s_BlockLengthFieldPosition,
                dataBlockLengthArray.Length);
            Buffer.BlockCopy(controlArray, 0, generatedBlock, s_ControlFieldPosition, controlArray.Length);

            generatedBlock[s_LengthCNReadFieldPosition] = (byte)communityNameReadArray.Length;
            generatedBlock[s_LengthCNWriteFieldPosition] = (byte)communityNameWriteArray.Length;

            Buffer.BlockCopy(
                communityNameReadArray,
                0,
                generatedBlock,
                s_CNReadFieldPosition,
                communityNameReadArray.Length);

            int communityNameWriteFieldPosition =
                s_CNReadFieldPosition + communityNameReadArray.Length + paddingAfterReadCN;
            Buffer.BlockCopy(
                communityNameWriteArray,
                0,
                generatedBlock,
                communityNameWriteFieldPosition,
                communityNameWriteArray.Length);

            return generatedBlock;
        }

        #endregion

        #region Public methods

        #endregion
    }
}