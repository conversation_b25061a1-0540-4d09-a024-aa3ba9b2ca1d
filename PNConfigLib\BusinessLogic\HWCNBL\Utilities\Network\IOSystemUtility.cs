/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IOSystemUtility.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Network
{
    internal static class IOSystemUtility
    {
        /// <summary>
        /// Returns the position number of an IO-System. If the IO-System is virtual and has an invalid number, it is transformed.
        /// </summary>
        /// <param name="ioSystem"></param>
        /// <returns></returns>
        public static int GetIOSystemPositionNumber(DataModel.PCLObjects.IOSystem ioSystem)
        {
            int positionNo = ioSystem.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                new AttributeAccessCode(),
                0);
            return positionNo;
        }
    }
}