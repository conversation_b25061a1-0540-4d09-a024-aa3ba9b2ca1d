/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DataRecordStruct.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.HWCNBL.Common;

#endregion

namespace PNConfigLib.HWCNBL.AdapterFramework.Utilities
{
    /// <summary>
    /// The class that represents a data record. Contains methods for accessing
    /// the contents of the data record, which is kept as a byte array.
    /// </summary>
    internal class DataRecordStruct
    {
        /// <summary>
        /// Contents of the data record.
        /// </summary>
        protected byte[] Data = new byte[0];

        /// <summary>
        /// Header of the data record.
        /// </summary>
        protected byte[] Header = new byte[8];

        /// <summary>
        /// The list that contains the sub blocks.
        /// </summary>
        private IList<byte[]> m_ListSubBlocks = new List<byte[]>();

        /// <summary>
        /// Block length part of the data record.
        /// </summary>
        public int BlockLength
        {
            set { Transformator.Write16(Header, 2, (ushort)value); }
            get { return Transformator.Read16(Header, 2); }
        }

        /// <summary>
        /// Block type part of the data record.
        /// </summary>
        public int BlockType
        {
            set { Transformator.Write16(Header, 0, (ushort)value); }
            get { return Transformator.Read16(Header, 0); }
        }

        /// <summary>
        /// Block version part of the data record.
        /// </summary>
        public int BlockVersion
        {
            set { Transformator.Write16(Header, 4, (ushort)value); }
            get { return Transformator.Read16(Header, 4); }
        }

        /// <summary>
        /// Reserved part of the data record.
        /// </summary>
        public int Reserved
        {
            set { Transformator.Write16(Header, 6, (ushort)value); }
            get { return Transformator.Read16(Header, 6); }
        }

        /// <summary>
        /// Gets the data record as a byte array.
        /// </summary>
        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();
                if (Header != null)
                {
                    block.AddRange(Header);
                }
                block.AddRange(Data);

                foreach (byte[] subblock in m_ListSubBlocks)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        /// <summary>
        /// Adds a subblock to the data record.
        /// </summary>
        /// <param name="subblock">The contents of the subblock to be added.</param>
        public void AddSubBlock(byte[] subblock)
        {
            m_ListSubBlocks.Add(subblock);
            BlockLength += subblock.Length;
        }
    }
}