/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: TimingProperties.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Collections.Generic;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The TimingProperties object defines the timing behaviour for 
    /// sending cyclic IO data.
    /// </summary>
    public class TimingProperties :
        GsdObject,
        GSDI.ITimingProperties,
        GSDI.ITimingProperties2,
        GSDI.ITimingProperties3

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the TimingProperties if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public TimingProperties()
        {
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private List<uint> m_SendClock;
        private List<uint> m_ReductionRatio;

        private List<uint> m_ReductionRatioPow2;
        private List<uint> m_ReductionRatioNonPow2;

        private uint m_PreferredSendClock;

        //V2.31
        private uint m_MaxReductionRatioIsochroneMode;


        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of cycle times supported by the AccessPoint for 
        /// sending cyclic data.
        /// </summary>
        /// <remarks>Basic clock tick is 31,25 microseconds. Each value of this 
        /// list contains the multiplier of the basic clock tick and can be in the 
        /// range 1 to 128.</remarks>
        public virtual Array SendClock =>
            (null != this.m_SendClock) ?
                new ArrayList(this.m_SendClock).ToArray() :
                null;

        /// <summary>
		/// Returns the list of supported reduction ratios.
		/// Accesses the reduction ratio, which means, that the send clock interval 
		/// can be reduced by a reduction, therefor it describes the supported reduction 
		/// ratios of a AccessPoint.
		/// </summary>
		/// <remarks>Each value of this list shall be in the range 1 to 16384.</remarks>
		public virtual Array ReductionRatio =>
            (null != this.m_ReductionRatio) ?
                new ArrayList(this.m_ReductionRatio).ToArray() :
                null;

        /// <summary>
		/// Returns the list of supported reduction ratios for sendclocks beeing in  a n^2 row..
		/// </summary>
		/// <remarks>Each value of this list shall be in the range 1 to 16384.</remarks>
		public virtual Array ReductionRatioPow2
        {
            get
            {
                ArrayList reductionRatios = new();
                if (m_ReductionRatioPow2 != null)
                {
                    reductionRatios.AddRange(m_ReductionRatioPow2);
                }

                if (m_ReductionRatio != null)
                {
                    foreach (uint val in m_ReductionRatio)
                    {
                        if (reductionRatios.Contains(val) == false)
                        {
                            reductionRatios.Add(val);
                        }
                    }
                }
                reductionRatios.Sort(0, reductionRatios.Count, null);
                return reductionRatios.ToArray();
            }
        }

        /// <summary>
        /// Returns the list of supported reduction ratios for sendclocks not beeing in  a n^2 row..
        /// </summary>
        /// <remarks>Each value of this list shall be in the range 1 to 16384.</remarks>
        public virtual Array ReductionRatioNonPow2
        {
            get
            {
                ArrayList reductionRatios = new();
                if (m_ReductionRatioNonPow2 != null)
                {
                    reductionRatios.AddRange(m_ReductionRatioNonPow2);
                }

                if (m_ReductionRatio != null)
                {
                    foreach (uint val in m_ReductionRatio)
                    {
                        if (reductionRatios.Contains(val) == false)
                        {
                            reductionRatios.Add(val);
                        }
                    }
                }
                reductionRatios.Sort(0, reductionRatios.Count, null);
                return reductionRatios.ToArray();
            }
        }

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxReductionRatioIsochroneMode => this.m_MaxReductionRatioIsochroneMode;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 PreferredSendClock => this.m_PreferredSendClock;


        #region COM Interface Members Only

        #endregion

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldSendClock;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_SendClock = hash[member] as List<uint>;

                member = Models.s_FieldReductionRatio;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_ReductionRatio = hash[member] as List<uint>;

                member = Models.s_FieldReductionRatioPow2;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_ReductionRatioPow2 = hash[member] as List<uint>;

                member = Models.s_FieldReductionRatioNonPow2;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_ReductionRatioNonPow2 = hash[member] as List<uint>;

                member = Models.s_FieldMaxReductionRatioIsochroneMode;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    m_MaxReductionRatioIsochroneMode = (uint)hash[member];

                member = Models.s_FieldPreferredSendClock;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    m_PreferredSendClock = (uint)hash[member];


            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectTimingProperties);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldReductionRatio, this.m_ReductionRatio, true);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldSendClock, this.m_SendClock, true);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldReductionRatioPow2, this.m_ReductionRatioPow2, true);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldReductionRatioNonPow2, this.m_ReductionRatioNonPow2, true);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxReductionRatioIsochroneMode, this.m_MaxReductionRatioIsochroneMode);
            Export.WriteUint32Property(ref writer, Models.s_FieldPreferredSendClock, this.m_PreferredSendClock);

            return true;
        }

        #endregion

    }
}


