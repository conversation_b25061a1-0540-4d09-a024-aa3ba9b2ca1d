﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CommunityNameConsistencyChecker.cs        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Networks.SNMP.Consistency
{
    /// <summary>
    /// Class to perform a consistency check on SNMP community names.
    /// </summary>
    internal class CommunityNameConsistencyChecker
    {
        #region Constants

        private const int m_MaxCharacterSize = 240;

        private const string s_RegExEndsWithInvalidCharacter = @"\.$|-$";

        private const string s_RegExStartsWithInvalidCharacter = @"^\.|^-";

        private const string s_RegExContainsInvalidCharacters = "[^0-9a-zA-Z.-]";

        #endregion

        #region Public Methods

        /// <summary>
        /// Performs a consistency check on the community name attributes provided in the
        /// communityNameAttributes array. If any of the attributes are present on the data.ConfigObject,
        /// their value is checked for validity.
        /// Any found problems are logged in the provided data object.
        /// </summary>
        /// <param name="pclObject"></param>
        public void CheckConsistency(PclObject pclObject)
        {
            foreach (string communityName in GetCommunityNameValues(pclObject.AttributeAccess))
            {
                if (string.IsNullOrEmpty(communityName))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        pclObject,
                        ConsistencyConstants.XML_EmptyCommunityName,
                        AttributeUtilities.GetName(pclObject));
                }

                string communityNameNormalized = communityName.Normalize(NormalizationForm.FormD);

                if (communityNameNormalized.Length > m_MaxCharacterSize)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        pclObject,
                        ConsistencyConstants.XML_MaxCharSizeCommunityName,
                        AttributeUtilities.GetName(pclObject));
                }

                if (ContainsInvalidCharacters(communityNameNormalized)
                    || StartsWithInvalidCharacter(communityNameNormalized)
                    || EndsWithInvalidCharacter(communityNameNormalized))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        pclObject,
                        ConsistencyConstants.XML_InvalidCharCommunityName,
                        AttributeUtilities.GetName(pclObject));
                }
            }
        }

        #endregion

        #region Private Methods

        private ICollection<string> GetCommunityNameValues(AttributeAccess attributeAccess)
        {
            AttributeAccessCode aac = new AttributeAccessCode();
            List<string> attributeValues = new List<string>();

            string snmpReadOnlyCommunityNameValue = attributeAccess.GetAnyAttribute(InternalAttributeNames.SnmpReadOnlyCommunityName, aac, string.Empty);

            if (aac.IsOkay)
            {
                attributeValues.Add(snmpReadOnlyCommunityNameValue);
            }

            aac.Reset();

            string snmpReadWriteCommunityNameValue = attributeAccess.GetAnyAttribute(InternalAttributeNames.SnmpReadWriteCommunityName, aac, string.Empty);

            if (aac.IsOkay)
            {
                attributeValues.Add(snmpReadWriteCommunityNameValue);
            }

            return attributeValues;
        }

        /// <summary>
        /// Checks if communityName contains characters outside the allowed range.
        /// Allowed range:
        ///                * english characters a-z
        ///                * english characters A-Z
        ///                * numbers 0-9
        ///                * dot (.)
        ///                * hyphen (-)
        /// </summary>
        /// <param name="communityName"></param>
        /// <returns>true if the communityName contains invalid characters, false otherwise</returns>
        private bool ContainsInvalidCharacters(string communityName)
        {
            Regex conditionInvalidCharacters = new Regex(s_RegExContainsInvalidCharacters);

            return conditionInvalidCharacters.IsMatch(communityName);
        }

        /// <summary>
        /// Checks if communityName starts with an character that is not allowed at the end of the communityName
        /// Invalid characters for the end of the communityName:
        ///                 * dot (.)
        ///                 * hyphen (-)
        /// </summary>
        /// <param name="communityName"></param>
        /// <returns>true if the communityName starts with a forbidden character, false otherwise</returns>
        private bool StartsWithInvalidCharacter(string communityName)
        {
            Regex conditionStartingCharacter = new Regex(s_RegExStartsWithInvalidCharacter);

            return conditionStartingCharacter.IsMatch(communityName);
        }

        /// <summary>
        /// Checks if communityName ends with an character that is not allowed at the end of the communityName
        /// Invalid characters for the end of the communityName:
        ///                 * dot (.)
        ///                 * hyphen (-)
        /// </summary>
        /// <param name="communityName"></param>
        /// <returns>true if the communityName ends with a forbidden character, false otherwise</returns>
        private bool EndsWithInvalidCharacter(string communityName)
        {
            Regex conditionEndingCharacter = new Regex(s_RegExEndsWithInvalidCharacter);

            return conditionEndingCharacter.IsMatch(communityName);
        }

        #endregion
    }
}