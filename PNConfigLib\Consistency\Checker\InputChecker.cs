/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: InputChecker.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Linq;

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.Consistency.Checker.ConfigurationFileChecker;

namespace PNConfigLib.Consistency.Checker
{
    internal class InputChecker : IInputChecker
    {
        private XsdChecker m_XsdChecker;

        private DefaultComponentReader m_DefaultComponentReader;

        private ListOfNodesChecker m_ListOfNodesChecker;

        private TopologyChecker m_TopologyChecker;

        private ConfigurationChecker m_ConfigurationChecker;

        private InputRelationChecker m_InputRelationChecker;

        private readonly Configuration m_Configuration;

        private readonly ListOfNodes m_ListOfNodes;

        private readonly Topology m_Topology;
        private readonly string m_ListOfNodesPath;

        /// <summary>
        /// Constructor with deserialized input classes
        /// </summary>
        /// <param name="lon"></param>
        /// <param name="cfg"></param>
        /// <param name="topo"></param>
        public InputChecker(
            XsdChecker xsdChecker,
            Configuration cfg,
            ListOfNodes lon,
            string listOfNodesPath,
            Topology topo = null
            )
        {
            m_XsdChecker = xsdChecker;
            m_Configuration = cfg;
            m_ListOfNodes = lon;
            m_Topology = topo;
            m_ListOfNodesPath = listOfNodesPath;
            InitializeCheckers();
        }

        /// <summary>
        /// Initialize Input checkers
        /// </summary>
        private void InitializeCheckers()
        {
            m_DefaultComponentReader = new DefaultComponentReader(m_Configuration, m_ListOfNodes);
            m_InputRelationChecker = new InputRelationChecker(m_Configuration, m_ListOfNodes, m_Topology);
            m_ListOfNodesChecker = new ListOfNodesChecker(m_ListOfNodes, m_XsdChecker, m_ListOfNodesPath);
            m_TopologyChecker = new TopologyChecker(m_Configuration, m_ListOfNodes, m_Topology, m_ListOfNodesPath);
            m_ConfigurationChecker = new ConfigurationChecker(m_Configuration, m_ListOfNodes, m_Topology, m_ListOfNodesPath);
            m_ConfigurationChecker.TopologyCheck += m_TopologyChecker.CheckTopologyForAlldevices;
            m_ConfigurationChecker.IsIRTProjectWithoutTopology += m_TopologyChecker.IsIRTProjectWithoutTopology;
        }

        /// <summary>
        /// Check consistencies
        /// </summary>
        /// <returns></returns>
        public bool Check()
        {
            bool retval;
            ConsistencyLogger.Reset();

            try
            {
                m_InputRelationChecker.Check();
                m_ListOfNodesChecker.Check();
                m_DefaultComponentReader.Read();
                m_ConfigurationChecker.Check();
                m_TopologyChecker.Check();
                retval = true;
            }
            catch (ConsistencyCheckException)
            {
                retval = !ConsistencyLogger.ConsistencyLogs.Any(x => x.Severity == LogSeverity.Error);
            }

            return retval;
        }
    }
}
