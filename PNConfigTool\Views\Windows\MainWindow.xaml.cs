using PNConfigTool.Services;
using PNConfigTool.ViewModels;
using PNConfigTool.Views.Pages;
using PNConfigTool.Models;
using System.Windows;
using System.Windows.Controls;
using System.Reflection;
using System.Windows.Threading;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using System.IO;
using GSDI;

namespace PNConfigTool.Views.Windows
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly INavigationService? _navigationService;
        private readonly ProjectManager? _projectManager;

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                //获取系统是以Left-handed（true）还是Right-handed（false）
                var ifLeft = SystemParameters.MenuDropAlignment;
                if (ifLeft)
                {
                    // change to false
                    var t = typeof(SystemParameters);
                    var field = t.GetField("_menuDropAlignment", BindingFlags.NonPublic | BindingFlags.Static);
                    if (field != null)
                    {
                        field.SetValue(null, false);
                        ifLeft = SystemParameters.MenuDropAlignment;
                    }
                }

                // Register navigation service
                _navigationService = new NavigationService(ContentFrame);
                
                // 注册页面类型
                System.Diagnostics.Debug.WriteLine("注册页面类型:");
                RegisterPages();
                
                // 将导航服务注册到ServiceLocator
                RegisterNavigationService();
                
                // 监听页面导航事件
                _navigationService!.PageChanged += NavigationService_PageChanged;

                // 获取服务
                var projectService = ServiceLocator.GetService<IProjectService>();
                var gsdmlService = ServiceLocator.GetService<IGSDMLService>();
                
                if (projectService == null || gsdmlService == null)
                {
                    MessageBox.Show("服务初始化失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Close();
                    return;
                }

                // 获取ProjectManager实例并订阅项目加载事件
                _projectManager = ProjectManager.Instance;
                
                // 创建ViewModel并设置DataContext
                var viewModel = new MainWindowViewModel(projectService, _navigationService, gsdmlService);
                DataContext = viewModel;

                // 窗口加载完成后自动导航到NetworkConfigPage并选中相应的导航项
                Loaded += MainWindow_Loaded;

                // 异步预热缓存
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await CacheManager.WarmupCaches();
                        Debug.WriteLine("缓存预热完成");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"缓存预热失败: {ex.Message}");
                    }
                });

                System.Diagnostics.Debug.WriteLine("MainWindow初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MainWindow初始化错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"初始化主窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 注册页面类型
        /// </summary>
        private void RegisterPages()
        {
            try
            {
                _navigationService?.RegisterPage("NetworkConfigPage", typeof(NetworkConfigPage));
                System.Diagnostics.Debug.WriteLine("- NetworkConfigPage");
                
                _navigationService?.RegisterPage("ControllerConfigPage", typeof(ControllerConfigPage));
                System.Diagnostics.Debug.WriteLine("- ControllerConfigPage");
                
                _navigationService?.RegisterPage("DeviceCatalogPage", typeof(DeviceCatalogPage));
                System.Diagnostics.Debug.WriteLine("- DeviceCatalogPage");
                
                _navigationService?.RegisterPage("DeviceConfigPage", typeof(DeviceConfigPage));
                System.Diagnostics.Debug.WriteLine("- DeviceConfigPage");

                _navigationService?.RegisterPage("CompletionPage", typeof(CompletionPage));
                System.Diagnostics.Debug.WriteLine("- CompletionPage");
                
                _navigationService?.RegisterPage("ModuleConfigPage", typeof(ModuleConfigPage));
                System.Diagnostics.Debug.WriteLine("- ModuleConfigPage");
                
                System.Diagnostics.Debug.WriteLine("所有页面注册完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册页面时出错: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 注册导航服务到ServiceLocator
        /// </summary>
        private void RegisterNavigationService()
        {
            try
            {
                // 直接将导航服务注册为单例
                if (_navigationService != null)
                {
                    ServiceLocator.RegisterSingleton<INavigationService>(_navigationService);
                    
                    // 确保服务提供者更新
                    ServiceLocator.BuildServiceProvider();
                    
                    // 验证注册是否成功
                    var service = ServiceLocator.GetService<INavigationService>();
                    if (service == _navigationService)
                    {
                        System.Diagnostics.Debug.WriteLine("成功将导航服务注册到ServiceLocator");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("导航服务注册失败，或获取的实例不同");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("无法注册导航服务：_navigationService为null");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册导航服务时出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }
        
        /// <summary>
        /// 获取导航服务
        /// </summary>
        public static INavigationService? GetNavigationService()
        {
            return ServiceLocator.GetService<INavigationService>();
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 使用Dispatcher延迟执行，确保UI元素已完全加载
            Dispatcher.BeginInvoke(DispatcherPriority.Loaded, new System.Action(() =>
            {
                // 选中导航栏中的"设置PROFINET网络"项
                if (NavigationListBox.Items.Count > 0)
                {
                    NavigationListBox.SelectedIndex = 0;
                }

                // 导航到NetworkConfigPage
                _navigationService?.Navigate("NetworkConfigPage");
                
                // 订阅项目服务的ProjectChanged事件
                var projectService = ServiceLocator.GetService<IProjectService>();
                if (projectService != null)
                {
                    projectService.ProjectChanged += ProjectService_ProjectChanged;
                    Debug.WriteLine("已订阅ProjectService.ProjectChanged事件");
                }
            }));
        }
        
        /// <summary>
        /// 处理项目变更事件
        /// </summary>
        private void ProjectService_ProjectChanged(object? sender, ProjectConfig? project)
        {
            if (project != null)
            {
                Debug.WriteLine($"项目已变更: {project.ProjectMetadata.ProjectName}，重建设备导航菜单");
                Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() => 
                {
                    RebuildDeviceNavigationMenu();
                }));
            }
            else
            {
                Debug.WriteLine("项目已关闭，清除设备导航菜单");
                Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() => 
                {
                    ClearDeviceNavigationMenu();
                }));
            }
        }
        
        /// <summary>
        /// 重建设备导航菜单
        /// </summary>
        public void RebuildDeviceNavigationMenu()
        {
            try
            {
                // 清除现有设备导航菜单
                ClearDeviceNavigationMenu();

                // 获取当前项目
                if (_projectManager == null) return;
                var project = _projectManager.CurrentProject;
                if (project == null) return;

                Debug.WriteLine($"重建设备导航菜单，设备数量: {project.ConfigurationSettings.DecentralDevices.Count}");

                // 添加每个设备到导航菜单
                foreach (var device in project.ConfigurationSettings.DecentralDevices)
                {
                    AddDeviceToNavigation(device.DeviceType, device.DeviceRefID);
                    Debug.WriteLine($"已添加设备到导航菜单: {device.DeviceType}, 类型: {device.DeviceRefID}");

                    // 重要：为每个设备重新添加其模块导航菜单项
                    // 这确保了项目加载时模块菜单能正确显示
                    RebuildModuleNavigationForDevice(device);
                }

                // 在所有设备添加完成后，添加全局的完成导航项
                AddGlobalCompletionNavigation();

                Debug.WriteLine("设备导航菜单重建完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重建设备导航菜单出错: {ex.Message}");
                MessageBox.Show($"重建设备导航菜单时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 为特定设备重建模块导航菜单
        /// </summary>
        /// <param name="device">设备配置</param>
        private void RebuildModuleNavigationForDevice(DecentralDeviceConfig device)
        {
            try
            {
                if (device.Modules == null || device.Modules.Count == 0)
                {
                    Debug.WriteLine($"设备 {device.DeviceRefID} 没有模块配置");
                    return;
                }

                Debug.WriteLine($"为设备 {device.DeviceRefID} 重建模块导航菜单，模块数量: {device.Modules.Count}");

                // 为每个模块添加导航菜单项，按插槽号排序
                var sortedModules = device.Modules
                    .Where(m => !string.IsNullOrEmpty(m.ModuleRefID))
                    .OrderBy(m => m.SlotNumber)
                    .ToList();

                foreach (var module in sortedModules)
                {
                    // 创建模块页面键 - 使用设备名和插槽号的格式
                    string modulePageKey = $"ModuleConfig_{device.DeviceRefID}_{module.SlotNumber}";

                    // 获取模块显示名称 - 优先从GSDML中获取，然后从GSDRefID获取
                    string moduleName = GetModuleDisplayNameFromGSDML(device.GSDMLFilePath, module.GSDRefID) ??
                                      module.GSDRefID ??
                                      ExtractDisplayNameFromModuleRefID(module.ModuleRefID);

                    // 创建显示名称：模块名(插槽号)
                    string displayName = $"{moduleName}({module.SlotNumber})";

                    // 添加模块到导航菜单
                    AddModuleToNavigation(device.DeviceRefID, displayName, modulePageKey, module.ModuleRefID);

                    Debug.WriteLine($"已为设备 {device.DeviceRefID} 重新添加模块导航菜单: {displayName} (页面键: {modulePageKey})");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"为设备 {device.DeviceRefID} 重建模块导航菜单时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从GSDML文件中获取模块显示名称
        /// </summary>
        /// <param name="gsdmlFilePath">GSDML文件路径</param>
        /// <param name="gsdRefID">GSDML引用ID</param>
        /// <returns>显示名称，如果无法获取则返回null</returns>
        private string? GetModuleDisplayNameFromGSDML(string gsdmlFilePath, string gsdRefID)
        {
            try
            {
                if (string.IsNullOrEmpty(gsdRefID) || string.IsNullOrEmpty(gsdmlFilePath))
                    return null;

                Debug.WriteLine($"尝试从GSDML文件获取模块显示名称: 文件={gsdmlFilePath}, GSDRefID={gsdRefID}");

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 获取GSDML文件的绝对路径
                string absoluteGsdmlPath = Path.IsPathRooted(gsdmlFilePath) ?
                    gsdmlFilePath :
                    Path.Combine(projectDirectory, gsdmlFilePath);

                if (!File.Exists(absoluteGsdmlPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {absoluteGsdmlPath}");
                    return null;
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = PNConfigTool.Services.GSDMLCacheService.GetCachedInterpreter(absoluteGsdmlPath,
                    ModelOptions.GSDCommon | ModelOptions.GSDStructure);

                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {absoluteGsdmlPath}");
                    return null;
                }

                // 尝试从Common模型获取模块
                var commonModule = interpreter.GetModule(gsdRefID);
                if (commonModule != null && commonModule.Info != null)
                {
                    if (commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                    {
                        string displayName = !string.IsNullOrEmpty(commonModuleInfo.Name) ? commonModuleInfo.Name : gsdRefID;
                        Debug.WriteLine($"从Common模型获取模块显示名称: {displayName}");
                        return displayName;
                    }
                }

                Debug.WriteLine($"无法从GSDML文件获取模块名称，返回null");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从GSDML获取模块显示名称时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从ModuleRefID中提取显示名称
        /// </summary>
        /// <param name="moduleRefID">模块引用ID，格式如 EM_DE16_1_1_1</param>
        /// <returns>显示名称，如 EM_DE16</returns>
        private string ExtractDisplayNameFromModuleRefID(string moduleRefID)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleRefID))
                    return moduleRefID;

                // ModuleRefID 格式通常是: {GSDRefID}_{fixedModuleCounter}_{pnDeviceId}_{slotNumber}
                // 例如: EM_DE16_1_1_1
                // 我们需要提取前面的 GSDRefID 部分作为显示名称

                // 查找最后三个下划线的位置，提取前面的部分
                var parts = moduleRefID.Split('_');
                if (parts.Length >= 4)
                {
                    // 重新组合前面的部分，排除最后三个数字部分
                    var displayNameParts = parts.Take(parts.Length - 3);
                    return string.Join("_", displayNameParts);
                }

                // 如果格式不符合预期，直接返回原值
                return moduleRefID;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取显示名称时出错: {ex.Message}");
                return moduleRefID;
            }
        }

        /// <summary>
        /// 清除设备导航菜单
        /// </summary>
        public void ClearDeviceNavigationMenu()
        {
            try
            {
                Debug.WriteLine("清除设备导航菜单");
                
                // 找到固定导航项的数量（网络配置、控制器配置等）
                int fixedItemCount = 2; // 假设前两项是固定的
                
                // 移除所有非固定项
                while (NavigationListBox.Items.Count > fixedItemCount)
                {
                    NavigationListBox.Items.RemoveAt(fixedItemCount);
                }
                
                Debug.WriteLine("设备导航菜单已清除");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清除设备导航菜单出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 添加设备到导航菜单
        /// </summary>
        public void AddDeviceToNavigation(string deviceType, string deviceName)
        {
            try
            {
                // 创建用于显示的设备类型和名称
                string displayName = $"{deviceType}-{deviceName}";
                
                // 创建父级导航项（设备类型-设备名）
                var parentItem = new ListBoxItem
                {
                    Content = displayName,
                    Tag = $"DeviceConfig_{deviceName}",
                    Margin = new Thickness(0, 1, 0, 1),
                    Padding = new Thickness(15, 10, 15, 10)
                };
                
                // 创建子级导航项（设备类型(0)）
                var childItem = new ListBoxItem
                {
                    Content = $"{deviceType}(0)",
                    Tag = $"DeviceDetail_{deviceName}",
                    Margin = new Thickness(0, 1, 0, 1),
                    Padding = new Thickness(30, 10, 15, 10)  // 左侧多一些缩进
                };

                // 将导航项添加到ListBox
                NavigationListBox.Items.Add(parentItem);
                NavigationListBox.Items.Add(childItem);
                
                // 注册设备页面
                RegisterDevicePages(deviceType, deviceName);
                
                Debug.WriteLine($"已添加设备到导航菜单: {deviceName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加设备到导航菜单出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加模块到导航菜单（按插槽号顺序插入）
        /// </summary>
        public void AddModuleToNavigation(string deviceName, string moduleName, string modulePageKey, string moduleId)
        {
            try
            {
                // 查找设备的子级导航项位置
                int deviceDetailIndex = -1;
                for (int i = 0; i < NavigationListBox.Items.Count; i++)
                {
                    if (NavigationListBox.Items[i] is ListBoxItem item &&
                        item.Tag?.ToString() == $"DeviceDetail_{deviceName}")
                    {
                        deviceDetailIndex = i;
                        break;
                    }
                }

                if (deviceDetailIndex == -1)
                {
                    Debug.WriteLine($"未找到设备 {deviceName} 的导航项");
                    return;
                }

                // 从模块页面键中提取插槽号
                int currentSlotNumber = ExtractSlotNumberFromPageKey(modulePageKey);

                // 创建模块导航项
                var moduleItem = new ListBoxItem
                {
                    Content = moduleName, // 使用模块名作为显示名称
                    Tag = modulePageKey,
                    Margin = new Thickness(0, 1, 0, 1),
                    Padding = new Thickness(45, 10, 15, 10)  // 比设备子项更多的缩进
                };

                // 找到正确的插入位置（按插槽号排序）
                int insertIndex = deviceDetailIndex + 1;
                for (int i = deviceDetailIndex + 1; i < NavigationListBox.Items.Count; i++)
                {
                    if (NavigationListBox.Items[i] is ListBoxItem existingItem &&
                        existingItem.Tag?.ToString()?.StartsWith($"ModuleConfig_{deviceName}_") == true)
                    {
                        int existingSlotNumber = ExtractSlotNumberFromPageKey(existingItem.Tag.ToString()!);
                        if (currentSlotNumber < existingSlotNumber)
                        {
                            insertIndex = i;
                            break;
                        }
                        insertIndex = i + 1;
                    }
                    else
                    {
                        // 遇到非模块项，停止查找
                        break;
                    }
                }

                // 插入模块项
                NavigationListBox.Items.Insert(insertIndex, moduleItem);

                // 注册模块配置页面
                _navigationService?.RegisterPage(modulePageKey, typeof(ModuleConfigPage));

                Debug.WriteLine($"已添加模块 {moduleName} 到导航菜单位置 {insertIndex}: {modulePageKey}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加模块到导航菜单出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从页面键中提取插槽号
        /// </summary>
        private int ExtractSlotNumberFromPageKey(string pageKey)
        {
            try
            {
                if (pageKey.StartsWith("ModuleConfig_"))
                {
                    string remainder = pageKey.Substring("ModuleConfig_".Length);
                    int lastUnderscoreIndex = remainder.LastIndexOf('_');
                    if (lastUnderscoreIndex > 0)
                    {
                        string slotNumberStr = remainder.Substring(lastUnderscoreIndex + 1);
                        if (int.TryParse(slotNumberStr, out int slotNumber))
                        {
                            return slotNumber;
                        }
                    }
                }
                return int.MaxValue; // 如果无法解析，放到最后
            }
            catch
            {
                return int.MaxValue;
            }
        }

        /// <summary>
        /// 从导航菜单中移除模块
        /// </summary>
        public void RemoveModuleFromNavigation(string modulePageKey)
        {
            try
            {
                // 查找并移除对应的导航项
                ListBoxItem? itemToRemove = null;
                foreach (var item in NavigationListBox.Items)
                {
                    if (item is ListBoxItem listBoxItem &&
                        listBoxItem.Tag?.ToString() == modulePageKey)
                    {
                        itemToRemove = listBoxItem;
                        break;
                    }
                }

                if (itemToRemove != null)
                {
                    NavigationListBox.Items.Remove(itemToRemove);
                    Debug.WriteLine($"已从导航菜单移除模块: {modulePageKey}");
                }

                // 取消注册页面
                _navigationService?.UnregisterPage(modulePageKey);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"移除模块导航菜单出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加全局完成导航项
        /// </summary>
        private void AddGlobalCompletionNavigation()
        {
            try
            {
                // 创建全局完成导航项
                var completionItem = new ListBoxItem
                {
                    Content = "完成",
                    Tag = "GlobalCompletion",
                    Margin = new Thickness(0, 1, 0, 1),
                    Padding = new Thickness(15, 10, 15, 10)  // 与主级导航项相同的缩进
                };

                // 添加到导航列表
                NavigationListBox.Items.Add(completionItem);

                // 注册全局完成页面
                _navigationService?.RegisterPage("GlobalCompletion", typeof(CompletionPage));

                Debug.WriteLine("已添加全局完成导航项");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加全局完成导航项出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册设备配置页面
        /// </summary>
        private void RegisterDevicePages(string deviceType, string deviceName)
        {
            try
            {
                // 注册设备配置页面
                string configPageKey = $"DeviceConfig_{deviceName}";
                string detailPageKey = $"DeviceDetail_{deviceName}";

                // 注册页面
                _navigationService?.RegisterPage(configPageKey, typeof(DeviceConfigPage));
                _navigationService?.RegisterPage(detailPageKey, typeof(ModuleConfigPage));

                Debug.WriteLine($"已注册设备页面: {configPageKey}, {detailPageKey}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"注册设备页面出错: {ex.Message}");
            }
        }

        private void NavigationService_PageChanged(object? sender, string pageName)
        {
            // 根据当前页面名称更新NavigationListBox的选中项
            foreach (ListBoxItem item in NavigationListBox.Items)
            {
                if (item.Tag?.ToString() == pageName)
                {
                    NavigationListBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void NavigationListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (e.AddedItems.Count > 0 && e.AddedItems[0] is ListBoxItem selectedItem)
                {
                    string? pageName = selectedItem.Tag?.ToString();
                    if (!string.IsNullOrEmpty(pageName))
                    {
                        // 避免循环导航 - 只有当当前页面不是选中的页面时才导航
                        if (_navigationService is NavigationService navService && 
                            (navService.CurrentPage != pageName))
                        {
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"尝试导航到页面: {pageName}");
                                
                                // 检查是否为设备配置页面或模块配置页面
                                if (pageName.StartsWith("DeviceConfig_"))
                                {
                                    // 设备配置页面 - 提取设备名称作为导航参数
                                    string deviceName = pageName.Substring(pageName.IndexOf('_') + 1);
                                    System.Diagnostics.Debug.WriteLine($"使用设备名参数导航: {deviceName}");
                                    _navigationService.Navigate(pageName, deviceName);
                                }
                                else if (pageName.StartsWith("DeviceDetail_"))
                                {
                                    // 模块配置页面 - 提取设备名称并创建模块参数
                                    string deviceName = pageName.Substring(pageName.IndexOf('_') + 1);
                                    System.Diagnostics.Debug.WriteLine($"使用设备名创建模块参数导航: {deviceName}");

                                    // 获取设备类型
                                    string deviceType = "未知类型";
                                    if (_projectManager?.CurrentProject != null)
                                    {
                                        var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                                            .FirstOrDefault(d => d.DeviceRefID == deviceName);
                                        if (device != null)
                                        {
                                            deviceType = device.DeviceType;
                                        }
                                    }

                                    // 创建导航参数，默认为该设备的第一个模块
                                    var moduleParams = new Models.NavigationModuleParams(
                                        deviceName,
                                        deviceType, // 使用实际的设备类型
                                        "", // 默认无子模块名
                                        0  // 默认第一个模块
                                    );

                                    _navigationService.Navigate(pageName, moduleParams);
                                }
                                else if (pageName.StartsWith("ModuleConfig_"))
                                {
                                    // 模块配置页面 - 新格式: ModuleConfig_{deviceName}_{slotNumber}
                                    // 由于设备名可能包含下划线，我们需要更智能的解析
                                    string remainder = pageName.Substring("ModuleConfig_".Length);
                                    int lastUnderscoreIndex = remainder.LastIndexOf('_');
                                    if (lastUnderscoreIndex > 0)
                                    {
                                        string deviceName = remainder.Substring(0, lastUnderscoreIndex);
                                        string slotNumber = remainder.Substring(lastUnderscoreIndex + 1);
                                        System.Diagnostics.Debug.WriteLine($"导航到模块配置页面: 设备={deviceName}, 插槽={slotNumber}");

                                        // 获取设备类型和查找正确的模块索引
                                        string deviceType = "未知类型";
                                        int moduleIndex = 0;
                                        if (_projectManager?.CurrentProject != null)
                                        {
                                            var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                                                .FirstOrDefault(d => d.DeviceRefID == deviceName);
                                            if (device != null)
                                            {
                                                deviceType = device.DeviceType;

                                                // 根据插槽号查找正确的模块索引
                                                if (device.Modules != null)
                                                {
                                                    for (int i = 0; i < device.Modules.Count; i++)
                                                    {
                                                        if (device.Modules[i].SlotNumber.ToString() == slotNumber)
                                                        {
                                                            moduleIndex = i;
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        // 创建导航参数
                                        var moduleParams = new Models.NavigationModuleParams(
                                            deviceName,
                                            deviceType,
                                            "", // 默认无子模块名
                                            moduleIndex   // 使用找到的正确模块索引
                                        );

                                        _navigationService.Navigate(pageName, moduleParams);
                                    }
                                    else
                                    {
                                        // 回退到旧格式处理
                                        string moduleId = remainder;
                                        System.Diagnostics.Debug.WriteLine($"导航到模块配置页面 (旧格式): 模块ID={moduleId}");
                                        _navigationService.Navigate(pageName);
                                    }
                                }
                                else if (pageName == "GlobalCompletion")
                                {
                                    // 全局完成页面 - 不需要特定的设备参数
                                    System.Diagnostics.Debug.WriteLine($"导航到全局完成页面");
                                    _navigationService.Navigate(pageName);
                                }
                                else
                                {
                                    // 其他页面不需要参数
                                    System.Diagnostics.Debug.WriteLine($"无参数导航到页面: {pageName}");
                                    _navigationService.Navigate(pageName);
                                }
                                System.Diagnostics.Debug.WriteLine($"导航成功到页面: {pageName}");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"导航失败: {ex.Message}");
                                MessageBox.Show($"导航到页面 {pageName} 时出错: {ex.Message}", "导航错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                
                                // 尝试重新注册页面
                                if (pageName.StartsWith("DeviceConfig_"))
                                {
                                    try
                                    {
                                        string deviceName = pageName.Substring(pageName.IndexOf('_') + 1);
                                        System.Diagnostics.Debug.WriteLine($"尝试重新注册设备配置页面: {pageName}");
                                        _navigationService.RegisterPage(pageName, typeof(Pages.DeviceConfigPage));
                                        _navigationService.Navigate(pageName, deviceName);
                                        System.Diagnostics.Debug.WriteLine($"重新注册并导航成功");
                                    }
                                    catch (Exception regEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"重新注册失败: {regEx.Message}");
                                    }
                                }
                                else if (pageName.StartsWith("DeviceDetail_"))
                                {
                                    try
                                    {
                                        string deviceName = pageName.Substring(pageName.IndexOf('_') + 1);
                                        System.Diagnostics.Debug.WriteLine($"尝试重新注册模块配置页面: {pageName}");
                                        _navigationService.RegisterPage(pageName, typeof(Pages.ModuleConfigPage));
                                        
                                        // 获取设备类型
                                        string deviceType = "未知类型";
                                        if (_projectManager?.CurrentProject != null)
                                        {
                                            var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                                                .FirstOrDefault(d => d.DeviceRefID == deviceName);
                                            if (device != null)
                                            {
                                                deviceType = device.DeviceType;
                                            }
                                        }

                                        // 创建导航参数
                                        var moduleParams = new Models.NavigationModuleParams(
                                            deviceName,
                                            deviceType, // 使用实际的设备类型
                                            "", // 默认无子模块名
                                            0  // 默认第一个模块
                                        );
                                        
                                        _navigationService.Navigate(pageName, moduleParams);
                                        System.Diagnostics.Debug.WriteLine($"重新注册并导航成功");
                                    }
                                    catch (Exception regEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"重新注册失败: {regEx.Message}");
                                    }
                                }
                                else if (pageName.StartsWith("ModuleConfig_"))
                                {
                                    try
                                    {
                                        System.Diagnostics.Debug.WriteLine($"尝试重新注册模块配置页面: {pageName}");
                                        _navigationService.RegisterPage(pageName, typeof(Pages.ModuleConfigPage));

                                        // 解析页面键
                                        string remainder = pageName.Substring("ModuleConfig_".Length);
                                        int lastUnderscoreIndex = remainder.LastIndexOf('_');
                                        if (lastUnderscoreIndex > 0)
                                        {
                                            string deviceName = remainder.Substring(0, lastUnderscoreIndex);
                                            string slotNumber = remainder.Substring(lastUnderscoreIndex + 1);

                                            // 获取设备类型和查找正确的模块索引
                                            string deviceType = "未知类型";
                                            int moduleIndex = 0;
                                            if (_projectManager?.CurrentProject != null)
                                            {
                                                var device = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                                                    .FirstOrDefault(d => d.DeviceRefID == deviceName);
                                                if (device != null)
                                                {
                                                    deviceType = device.DeviceType;

                                                    // 根据插槽号查找正确的模块索引
                                                    if (device.Modules != null)
                                                    {
                                                        for (int i = 0; i < device.Modules.Count; i++)
                                                        {
                                                            if (device.Modules[i].SlotNumber.ToString() == slotNumber)
                                                            {
                                                                moduleIndex = i;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            // 创建导航参数
                                            var moduleParams = new Models.NavigationModuleParams(
                                                deviceName,
                                                deviceType,
                                                "", // 默认无子模块名
                                                moduleIndex   // 使用找到的正确模块索引
                                            );

                                            _navigationService.Navigate(pageName, moduleParams);
                                        }
                                        else
                                        {
                                            // 旧格式，直接导航
                                            _navigationService.Navigate(pageName);
                                        }

                                        System.Diagnostics.Debug.WriteLine($"重新注册并导航成功");
                                    }
                                    catch (Exception regEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"重新注册失败: {regEx.Message}");
                                    }
                                }
                                else if (pageName.StartsWith("ModuleConfig_"))
                                {
                                    try
                                    {
                                        // 新格式: ModuleConfig_{ModuleID}
                                        string moduleId = pageName.Substring("ModuleConfig_".Length);
                                        System.Diagnostics.Debug.WriteLine($"尝试重新注册模块配置页面: {pageName}, 模块ID: {moduleId}");
                                        _navigationService.RegisterPage(pageName, typeof(Pages.ModuleConfigPage));

                                        // 直接导航，让 ModuleConfigPage 自己根据 ModuleID 查找对应的设备和模块
                                        _navigationService.Navigate(pageName);
                                        System.Diagnostics.Debug.WriteLine($"重新注册并导航成功");
                                    }
                                    catch (Exception regEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"重新注册模块页面失败: {regEx.Message}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"选择变更事件处理失败: {ex.Message}");
                MessageBox.Show($"导航处理出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}