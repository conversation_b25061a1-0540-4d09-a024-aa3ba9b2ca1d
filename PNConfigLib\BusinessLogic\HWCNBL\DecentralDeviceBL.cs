/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceBL.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL
{
    internal class DecentralDeviceBL : HwcnBusinessLogic
    {
        private DecentralDevice DecentralDevice
        {
            get;
        }
        public DecentralDeviceBL(DecentralDevice decentralDevice)
        {
            DecentralDevice = decentralDevice;

            InitBL();
        }
        private void InitActions()
        {
            ConsistencyManager.RegisterConsistencyCheck(DecentralDevice, CheckHeadmoduleConsistency);
        }

        private void InitAttributes()
        {
            if (DecentralDevice.PCLCatalogObject != null)
            {
                AttributeAccessCode aac = new AttributeAccessCode();
                int positionNumber = DecentralDevice.PCLCatalogObject.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PositionNumber, aac, 0);
                if (aac.IsOkay)
                {
                    DecentralDevice.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PositionNumber,
                        positionNumber);
                }
            }
            DecentralDevice.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)SharedIoAssignment.None);
        }

        private void InitBL()
        {
            InitAttributes();
            InitActions();
        }

        internal void Configure(
            DecentralDeviceType xmlDecentralDevice,
            ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice)
        {
            FillGeneralAttributes(DecentralDevice, xmlDecentralDevice.General
                , xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.Item as string
                , lonDecentralDevice.DeviceName
                , lonDecentralDevice.DeviceID);
            List<Submodule> virtualSubmodules = DecentralDevice.GetVirtualSubmodules();
            if (virtualSubmodules != null && virtualSubmodules.Count>0)
            {
                foreach (var virtualSubmodule in virtualSubmodules)
                {
                    Dictionary<uint, byte[]> parameterRecordDataItems;
                    if (xmlDecentralDevice.Module.Exists(w => w.GSDRefID == lonDecentralDevice.GSDRefID))
                    {
                        ModuleType headModule =
                            xmlDecentralDevice.Module.FirstOrDefault(w => w.GSDRefID == lonDecentralDevice.GSDRefID);

                        if (headModule != null && headModule.Submodule.Exists(w => w.GSDRefID == virtualSubmodule.Id))
                        {
                            List<ParameterRecordDataItemsTypeParameterRecordDataItem> sub =
                                headModule.Submodule.FirstOrDefault(w => w.GSDRefID == virtualSubmodule.Id)?.ParameterRecordDataItems;
                            parameterRecordDataItems = FillParameterRecordDataItems(
                                virtualSubmodule,
                                sub,
                                xmlDecentralDevice.DeviceRefID);
                        }
                        else
                        {
                            parameterRecordDataItems = FillParameterRecordDataItems(
                                virtualSubmodule,
                                xmlDecentralDevice.ParameterRecordDataItems,
                                xmlDecentralDevice.DeviceRefID);
                        }
                    }
                    else
                    {
                        parameterRecordDataItems = FillParameterRecordDataItems(
                            virtualSubmodule,
                            xmlDecentralDevice.ParameterRecordDataItems,
                            xmlDecentralDevice.DeviceRefID);
                    }
                    virtualSubmodule.SetParameterRecordDataItems(parameterRecordDataItems);
                }
            }
        }

        private void CheckHeadmoduleConsistency()
        {
            PNIOD pnIod = DecentralDevice.GetInterface().PNIOD;

            DataModel.PCLObjects.IOSystem ioSystem = pnIod?.IOSystem;

            Interface controllerInterface = null;

            if ((pnIod != null)
                && (ioSystem != null))
            {
                controllerInterface = ioSystem.PNIOC.ParentObject as Interface;
            }

            if (controllerInterface == null)
            {
                return;
            }

            bool pnIoallowSlot0WithoutSubmodule1 =
                controllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoAllowSlot0WithoutSubmodule1,
                    new AttributeAccessCode(),
                    false);

            if (pnIoallowSlot0WithoutSubmodule1)
            {
                return;
            }

            int pos = AttributeUtilities.GetPositionNumber(DecentralDevice);

            if (pos > 0)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, DecentralDevice, ConsistencyConstants.DifferentSlotModels);
            }
            CheckConsistencyUtility.CheckConsistencyModuleWithoutSubmodule(DecentralDevice);
        }
    }
}