/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: StringOperations.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: StringOperations.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */

using System.Text;

namespace PNConfigLib.Importer.GSDImport.Helper
{
    /// <summary>
    /// Contains helper methods for common string operations of the project
    /// </summary>
    public static class StringOperations
    {
        /// <summary>
        /// To split two parameters with back slash
        /// </summary>
        private const string s_BackSlash = @"\";

        /// <summary>
        /// Combines specified parameters with back slash
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static string CombineParametersWithBackSlash(params string[] parameters)
        {
            return CombineParametersWithSplitter(s_BackSlash, parameters);
        }

        /// <summary>
        /// Combines specified parameters with the desired splitter
        /// </summary>
        /// <param name="splitter"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static string CombineParametersWithSplitter(string splitter, params object[] parameters)
        {
            StringBuilder sb = new StringBuilder();

            for (int paramCursor = 0; paramCursor < parameters.Length; paramCursor++)
            {
                sb.Append(parameters[paramCursor]);

                if (paramCursor != parameters.Length - 1)
                {
                    sb.Append(splitter);
                }
            }

            return sb.ToString();
        }
    }
}
