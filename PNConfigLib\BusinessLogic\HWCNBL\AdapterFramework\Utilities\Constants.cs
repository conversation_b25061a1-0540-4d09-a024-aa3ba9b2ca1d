/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Constants.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Using

#endregion

namespace PNConfigLib.HWCNBL.AdapterFramework.Utilities
{
    //########################################################################################

    #region Constants

    /// <summary>
    /// The block version constants.
    /// </summary>
    internal static class BlockVersionDP
    {
        /// <summary>
        /// VersionHigh
        /// </summary>
        public const byte VersionHigh = 1;

        /// <summary>
        /// VersionLow
        /// </summary>
        public const byte VersionLow = 0;
    }

    /// <summary>
    /// Data record constants.
    /// </summary>
    internal static class DataRecords
    {
        /// <summary>
        /// Block type constants.
        /// </summary>
        internal static class BlockTypes
        {
            /// <summary>
            /// AlarmCrData
            /// </summary>
            public const ushort AlarmCrData = 0x3107;

            // AR
            /// <summary>
            /// ArCommunicationData
            /// </summary>
            public const ushort ArCommunicationData = 0x3100;

            /// <summary>
            /// ArRecordData
            /// </summary>
            public const ushort ArRecordData = 0x3105;

            // IOController
            /// <summary>
            /// ControllerProperties
            /// </summary>
            public const ushort ControllerProperties = 0x3040;

            /// <summary>
            /// ExpectedSubmoduleData
            /// </summary>
            public const ushort ExpectedSubmoduleData = 0x3101;

            /// <summary>
            /// IeParametersTimeSync
            /// </summary>
            public const ushort IeParametersTimeSync = 0x8000;

            /// <summary>
            /// IocrData
            /// </summary>
            public const ushort IocrData = 0x3102;

            /// <summary>
            /// IpAddressValidationLocal
            /// </summary>
            public const ushort IpAddressValidationLocal = 0x3006;

            /// <summary>
            /// IpAddressValidationRemote
            /// </summary>
            public const ushort IpAddressValidationRemote = 0x3011;

            // NetworkParameters
            /// <summary>
            /// IpV4Suite
            /// </summary>
            public const ushort IpV4Suite = 0x3000;

            // iIODevice
            /// <summary>
            /// iPNIODFrameResources
            /// </summary>
            public const ushort iPNIODFrameResources = 0x3080;

            // iIODevice
            /// <summary>
            /// iPNIODIOConfigData
            /// </summary>
            public const ushort iPNIODIOConfigData = 0x3082;

            /// <summary>
            /// iPNIODSubmoduleDefinition
            /// </summary>
            public const ushort iPNIODSubmoduleDefinition = 0x3081;

            /// <summary>
            /// LocalScfAdaption
            /// </summary>
            public const ushort LocalScfAdaption = 0x3104;

            /// <summary>
            /// McrData
            /// </summary>
            public const ushort McrData = 0x3103;

            /// <summary>
            /// NameOfStation
            /// </summary>
            public const ushort NameOfStation = 0xA201;

            /// <summary>
            /// NameOfStationValidation
            /// </summary>
            public const ushort NameOfStationValidation = 0x3009;

            /// <summary>
            /// PDNRTFeedInLoadLimitation
            /// </summary>
            public const ushort NRT_LOAD_LIMIT = 0xF004;

            // IODevice
            /// <summary>
            /// PNIODProperties
            /// </summary>
            public const ushort PNIODProperties = 0x3060;

            // Routing Table
            /// <summary>
            /// RoutingTable
            /// </summary>
            public const ushort RoutingTable = 0x1100;

            // IOInterface
            /// <summary>
            /// SendClock
            /// </summary>
            public const ushort SendClock = 0x3020;

            /// <summary>
            /// StationNameAlias
            /// </summary>
            public const ushort StationNameAlias = 0x3010;

            /// <summary>
            /// SubmoduleProperties
            /// </summary>
            public const ushort SubmoduleProperties = 0x3110;
        }

        /// <summary>
        /// Index constants.
        /// </summary>
        internal static class Indexes
        {
            /// <summary>
            /// AlarmCrData
            /// </summary>
            public const ushort AlarmCrData = 0x3107;

            // AR
            /// <summary>
            /// ArCommunicationData
            /// </summary>
            public const uint ArCommunicationData = 0x3100;

            /// <summary>
            /// ArRecordData
            /// </summary>
            public const uint ArRecordData = 0x3105;

            // IOController
            /// <summary>
            /// ControllerProperties
            /// </summary>
            public const uint ControllerProperties = 0x3040;

            /// <summary>
            /// ExpectedConfigRecord
            /// </summary>
            public const ushort ExpectedConfigRecord = 0x1005;

            /// <summary>
            /// SnmpRecord
            /// </summary>
            public const ushort SnmpControlRecord = 0xB071;

            /// <summary>
            /// ExpectedPDSyncData
            /// </summary>
            public const uint ExpectedPDSyncData = 0x802D;

            /// <summary>
            /// ExpectedSubmoduleData
            /// </summary>
            public const uint ExpectedSubmoduleData = 0x3101;

            /// <summary>
            /// IeParametersTimeSync
            /// </summary>
            public const uint IeParametersTimeSync = 0x1005;

            /// <summary>
            /// IocrData
            /// </summary>
            public const uint IocrData = 0x3102;

            /// <summary>
            /// IpAddressValidationLocal
            /// </summary>
            public const uint IpAddressValidationLocal = 0x1001;

            /// <summary>
            /// IpAddressValidationRemote
            /// </summary>
            public const uint IpAddressValidationRemote = 0x1007;

            // NetworkParameters
            /// <summary>
            /// IpV4Suite
            /// </summary>
            public const uint IpV4Suite = 0x1000;

            /// <summary>
            /// IrInfoBlock
            /// </summary>
            public const uint IrInfoBlock = 0x3106;

            /// <summary>
            /// IsochronModeData
            /// </summary>
            public const uint IsochronModeData = 0x230FF;

            /// <summary>
            /// IsoMParameter
            /// </summary>
            public const uint IsoMParameter = 0x8030;

            // IuMxDataConf
            /// <summary>
            /// IuMxDataConf
            /// </summary>
            public const uint IuM_AKZ_OKZ = 0xAFF1;

            public const uint IuMDescritpion = 0xAFF3;

            public const uint IuMInstallationDate = 0xAFF2;

            // iIODevice
            /// <summary>
            /// iPNIODFrameResources
            /// </summary>
            public const uint iPNIODFrameResources = 0x3080;

            // iIODevice
            /// <summary>
            /// iPNIODIOConfigData
            /// </summary>
            public const uint iPNIODIOConfigData = 0x3082;

            /// <summary>
            /// iPNIODSubmoduleDefinition
            /// </summary>
            public const uint iPNIODSubmoduleDefinition = 0x3081;

            /// <summary>
            /// LocalScfAdaption
            /// </summary>
            public const uint LocalScfAdaption = 0x3104;

            /// <summary>
            /// McrData
            /// </summary>
            public const uint McrData = 0x3103;

            /// <summary>
            /// NameOfStation
            /// </summary>
            public const uint NameOfStation = 0x1003;

            /// <summary>
            /// NameOfStationValidation
            /// </summary>
            public const uint NameOfStationValidation = 0x1004;

            /// <summary>
            /// PDEVValidation
            /// </summary>
            public const uint PDEVValidation = 0x3014;

            /// <summary>
            /// PDInterfaceAdjust
            /// </summary>
            public const uint PDInterfaceAdjust = 0x8071;

            /// <summary>
            /// CIMSNMPAdjust
            /// </summary>
            public const uint CIMSNMPAdjust = 0x8200;

            /// <summary>
            /// PDInterfaceFSUDataAdjust
            /// </summary>
            public const uint PDInterfaceFSUDataAdjust = 0x8090;

            // IOInterface
            /// <summary>
            /// PDInterfaceMrpAlternativeRedundancy
            /// </summary>
            public const uint PDInterfaceMrpAlternativeRedundancy = 0x0003;

            /// <summary>
            /// PDInterfaceMrpDataAdjust
            /// </summary>
            public const uint PDInterfaceMrpDataAdjust = 0x8052;

            /// <summary>
            /// PDInterfaceMrpDataCheck
            /// </summary>
            public const uint PDInterfaceMrpDataCheck = 0x8051;

            /// <summary>
            /// PDIRData
            /// </summary>
            public const uint PDIRData = 0x802C;

            /// <summary>
            /// PdirSubframeData
            /// </summary>
            public const uint PdirSubframeData = 0x8020;

            /// <summary>
            /// PDMasterTailorData, block for the address/machine tailoring feature.
            /// </summary>
            public const uint PDMasterTailorData = 0x17081;

            /// <summary>
            /// PDNRTFeedInLoadLimitation
            /// </summary>
            public const uint PDNRTFeedInLoadLimitation = 0x10003;

            // Port
            /// <summary>
            /// PDPortDataAdjust
            /// </summary>
            public const uint PDPortDataAdjust = 0x802F;

            /// <summary>
            /// PDPortDataCheck
            /// </summary>
            public const uint PDPortDataCheck = 0x802B;

            /// <summary>
            /// PDPortFODataAdjust
            /// </summary>
            public const uint PDPortFODataAdjust = 0x8062;

            /// <summary>
            /// PDPortFODataCheck
            /// </summary>
            public const uint PDPortFODataCheck = 0x8061;

            /// <summary>
            /// PDPortMrpDataAdjust
            /// </summary>
            public const uint PDPortMrpDataAdjust = 0x8053;

            /// <summary>
            /// PDPTCPTime
            /// </summary>
            public const uint PDPTCPTime = 0x802E;

            // IODevice
            /// <summary>
            /// PNIODProperties
            /// </summary>
            public const uint PNIODProperties = 0x3060;

            // Routing Table
            /// <summary>
            /// RoutingTable
            /// </summary>
            public const uint RoutingTable = 0x1100;

            /// <summary>
            /// SendClock
            /// </summary>
            public const uint SendClock = 0x10000;

            /// <summary>
            /// SrInfoData
            /// </summary>
            public const ushort SrInfoData = 0x3108;

            /// <summary>
            /// StationNameAlias
            /// </summary>
            public const uint StationNameAlias = 0x1006;

            /// <summary>
            /// SubmoduleProperties
            /// </summary>
            public const ushort SubmoduleProperties = 0x3110;
        }
    }

    #endregion
}