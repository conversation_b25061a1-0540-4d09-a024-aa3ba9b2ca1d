/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_045.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections;

using C = PNConfigLib.Gsd.Interpreter.Common;
using System.Globalization;
using System.Xml.XPath;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02045 : 
        BuilderV02044
    {
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02045()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version245);
        }

        #endregion

        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_FieldDataRef:
                        {
                            // NOTE: Navigator must point to DataRef.
                            PrepareDataRef(nav, ref hash);
                            obj = new C.DataRef();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException e)
            {
                obj = null;
            }
            catch (PreparationException e)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        #region Preparation

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfIsafeFscpTestModeSupported, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_ProfIsafeFscpTestModeSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafeFscpTestModeSupported);
        }

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfIsafeFscpTestModeSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_ProfIsafeFscpTestModeSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafeFscpTestModeSupported);
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfIsafeFscpTestModeSupported, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_ProfIsafeFscpTestModeSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafeFscpTestModeSupported);
        }

        protected override void PrepareDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDataItemId, null);

            base.PrepareDataItem(nav, ref hash);

            // Get ID attribute. Optional.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldDataItemId] = attr;
        }

        protected override void PrepareBitDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldBitDataItemId, null);
            hash.Add(Models.s_FieldBitLength, null);
            hash.Add(Models.s_FieldFormat, null);

            base.PrepareBitDataItem(nav, ref hash);

            // Get ID attribute. Optional.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldBitDataItemId] = attr;

            // Get BitLength attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_BitLength, String.Empty);
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 value);
            hash[Models.s_FieldBitLength] = value;

            // Get Format attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_Format, String.Empty);
            hash[Models.s_FieldFormat] = attr;
        }

        protected override void PrepareChannel(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDataRef, null);

            base.PrepareChannel(nav, ref hash);

            // Navigate to DataRef and create it. Required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_DataRef, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectDataRef, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectDataRef + "' couldn't be created!");
            }
            hash[Models.s_FieldDataRef] = obj;
        }

        protected virtual void PrepareDataRef(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDataTarget, null);

            // Get DataTarget attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_DataTarget, String.Empty);
            hash[Models.s_FieldDataTarget] = attr;
        }

        #endregion
    }
}
