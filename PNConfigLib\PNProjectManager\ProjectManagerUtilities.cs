/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnProjectManager                          :C&  */
/*                                                                           */
/*  F i l e               &F: ProjectManagerUtilities.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.IOSystem;
using PNConfigLib.HWCNBL.Net;
using DecentralDeviceType = PNConfigLib.ConfigReader.ListOfNodes.DecentralDeviceType;
using Subnet = PNConfigLib.DataModel.PCLObjects.Subnet;

#endregion

namespace PNConfigLib.PNProjectManager
{
    /// <summary>
    /// This class contains utility methods used in ProjectManager class.
    /// </summary>
    public static class ProjectManagerUtilities
    {
        public static Subnet FindSubnetById(string subnetRefId, List<IBusinessLogic> blList)
        {
            foreach (IBusinessLogic bl in blList)
            {
                if (bl is NetBusinessLogic)
                {
                    NetBusinessLogic subnetBusinessLogic = bl as NetBusinessLogic;
                    if (subnetBusinessLogic.Subnet.Id == subnetRefId)
                    {
                        return subnetBusinessLogic.Subnet;
                    }
                }
            }

            throw new PNFunctionsException("Subnet not found.");
        }

        /// <summary>
        /// Gets an IOSystem by its ID.
        /// </summary>
        /// <param name="ioSystemId">The ID of the IOSystem.</param>
        /// <param name="blList"></param>
        /// <exception cref="PNFunctionsException"> if an IOSystem with the given ID does not exist.</exception>
        /// <returns>The IOSystem with the given ID.</returns>
        internal static IOSystem FindIOSystemById(string ioSystemId, List<IBusinessLogic> blList)
        {
            foreach (IBusinessLogic bl in blList)
            {
                if (bl is IoSystemBusinessLogic)
                {
                    IoSystemBusinessLogic ioSystemBusinessLogic = bl as IoSystemBusinessLogic;
                    if (ioSystemBusinessLogic.IOSystem.Id == ioSystemId)
                    {
                        return ioSystemBusinessLogic.IOSystem;
                    }
                }
            }

            throw new PNFunctionsException("IO system not found.");
        }

        /// <summary>
        /// Gets a TopologyTypePortInterconnection by an interface reference id and port number.
        /// </summary>
        /// <param name="InterfaceRefID">The id of the referenced interface.</param>
        /// <param name="portNumber">The port number.</param>
        /// <param name="topology">The topology object where port interconnection will be searched.</param>
        /// <param name="isLocal"></param>
        /// <returns>
        /// TopologyTypePortInterconnection containing the given interface ref id and port number;
        /// null if port interconnection could not be found or the topology is null.
        /// </returns>
        internal static TopologyTypePortInterconnection FindPortInterconnectionByInterfaceAndPortNumber(
            string InterfaceRefID,
            uint portNumber,
            Topology topology,
            out bool isLocal)
        {
            isLocal = true;
            if (topology == null)
            {
                return null;
            }

            foreach (TopologyTypePortInterconnection portInterconnection in topology.PortInterconnection)
            {
                if ((portInterconnection.LocalPort.InterfaceRefID == InterfaceRefID)
                    && (portInterconnection.LocalPort.PortNumber == portNumber))
                {
                    return portInterconnection;
                }

                TopologyTypePortInterconnectionPartnerPort partnerPortComplexType = portInterconnection.PartnerPort;
                if ((partnerPortComplexType.InterfaceRefID == InterfaceRefID)
                    && (partnerPortComplexType.PortNumber == portNumber))
                {
                    isLocal = false;
                    return portInterconnection;
                }
            }

            return null;
        }

        /// <summary>
        /// Gets a SyncDomain by its ID.
        /// </summary>
        /// <param name="syncDomainId">The ID of the SyncDomain.</param>
        /// <param name="blList"></param>
        /// <exception cref="PNFunctionsException"> if a sync domain with the given ID does not exist.</exception>
        /// <returns>The SyncDomain with the given ID.</returns>
        internal static SyncDomain FindSyncDomainById(string syncDomainId, List<IBusinessLogic> blList)
        {
            foreach (IBusinessLogic bl in blList)
            {
                if (bl is SyncDomainBusinessLogic)
                {
                    SyncDomainBusinessLogic syncDomainBusinessLogic = bl as SyncDomainBusinessLogic;
                    if (syncDomainBusinessLogic.SyncDomain.Id == syncDomainId)
                    {
                        return syncDomainBusinessLogic.SyncDomain;
                    }
                }
            }

            throw new PNFunctionsException("Sync domain not found.");
        }

        /// <summary>
        /// Gets a sync domain XML object by its ID.
        /// </summary>
        /// <param name="syncDomainId">The ID of the SyncDomain.</param>
        /// <param name="configuration">The xml configuration where the SyncDomain will be searched.</param>
        /// <exception cref="PNFunctionsException"> if a sync domain with the given ID does not exist.</exception>
        /// <returns>A SyncDomainType object containing xml form of a SyncDomain.</returns>
        internal static SyncDomainType FindSyncDomainXmlById(string syncDomainId, Configuration configuration)
        {
            foreach (ConfigReader.Configuration.Subnet subnet in configuration.Subnet)
            {
                foreach (SyncDomainType syncDomain in subnet.DomainManagement.SyncDomains)
                {
                    if (syncDomain.SyncDomainID == syncDomainId)
                    {
                        return syncDomain;
                    }
                }
            }

            throw new PNFunctionsException("Sync domain not found.");
        }

        /// <summary>
        /// Gets a central device (PNDriver) object from the list of nodes by its device ID.
        /// </summary>
        /// <param name="listOfNodes">The list of nodes.</param>
        /// <param name="DeviceID">The ID of PNDriver object that will be retrieved.</param>
        /// <exception cref="PNFunctionsException"> if a central device with the given ID does not exist.</exception>
        /// <returns>The PNDriver object with the given device ID.</returns>
        internal static PNDriverType GetCentralDeviceFromListOfNodes(ListOfNodes listOfNodes, string DeviceID)
        {
            PNDriverType pnDriver = listOfNodes.PNDriver.SingleOrDefault(e => e.DeviceID == DeviceID);

            if (pnDriver == null)
            {
                throw new PNFunctionsException("PNDriver device not found in list of nodes: " + DeviceID);
            }

            return pnDriver;
        }

        internal static Dictionary<int, PortCatalog> GetDAPPortLookupBySlotRelation(
            DecentralDeviceCatalog decentralDeviceCatalog,
            string GSDPath,
            SlotRelationType slotRelType)
        {
            Dictionary<int, PortCatalog> portCatalogLookup = new Dictionary<int, PortCatalog>();

            List<KeyValuePair<string, int>> slotLookup =
                GetSlotLookupByRelation(decentralDeviceCatalog.PluggableSubmoduleList, slotRelType);
            foreach (KeyValuePair<string, int> objectInfo in slotLookup)
            {
                portCatalogLookup.Add(
                    objectInfo.Value,
                    Catalog.PortList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", GSDPath, objectInfo.Key)]);
            }
            return portCatalogLookup;
        }

        /// <summary>
        /// Gets a decentral device object from the list of nodes by its device ID.
        /// </summary>
        /// <param name="listOfNodes">The list of nodes.</param>
        /// <param name="DeviceID">The ID of PNDriver object that will be retrieved.</param>
        /// <exception cref="PNFunctionsException"> if a decentral device with the given ID does not exist.</exception>
        /// <returns>The PNDriver object with the given device ID.</returns>
        internal static DecentralDeviceType GetDecentralDeviceFromListOfNodes(ListOfNodes listOfNodes, string DeviceID)
        {
            DecentralDeviceType decentralDevice =
                listOfNodes.DecentralDevice.SingleOrDefault(e => e.DeviceID == DeviceID);

            if (decentralDevice == null)
            {
                throw new PNFunctionsException("Decentral device not found in list of nodes: " + DeviceID);
            }

            return decentralDevice;
        }

        public static Dictionary<KeyValuePair<string, int>, ModuleCatalog> GetModuleLookupBySlotRelation(
            DecentralDeviceCatalog decentralDeviceCatalog,
            string GSDPath,
            SlotRelationType slotRelType)
        {
            Dictionary<KeyValuePair<string, int>, ModuleCatalog> retval =
                new Dictionary<KeyValuePair<string, int>, ModuleCatalog>();
            List<KeyValuePair<string, int>> slotLookup =
                GetSlotLookupByRelation(decentralDeviceCatalog.PluggableModuleList, slotRelType);
            foreach (KeyValuePair<string, int> objectInfo in slotLookup)
            {
                retval.Add(objectInfo, Catalog.ModuleList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", GSDPath, objectInfo.Key)]);
            }
            return retval;
        }

        internal static Dictionary<KeyValuePair<string, int>, SubmoduleCatalog> GetSubmoduleLookupBySlotRelation(
            ModuleCatalog moduleCatalog,
            string GSDPath,
            SlotRelationType slotRelType)
        {
            Dictionary<KeyValuePair<string, int>, SubmoduleCatalog> retval =
                new Dictionary<KeyValuePair<string, int>, SubmoduleCatalog>();

            List<KeyValuePair<string, int>> slotLookup = GetSlotLookupByRelation(
                moduleCatalog.PluggableSubmoduleList,
                slotRelType);
            foreach (KeyValuePair<string, int> objectInfo in slotLookup)
            {
                retval.Add(objectInfo, Catalog.SubmoduleList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", GSDPath, objectInfo.Key)]);
            }
            return retval;
        }

        private static List<KeyValuePair<string, int>> GetSlotLookupByRelation(
            Dictionary<string, SlotRelation> relationLookup,
            SlotRelationType slotRelType)
        {
            List<KeyValuePair<string, int>> retval = new List<KeyValuePair<string, int>>();
            if (relationLookup != null)
            {
                foreach (KeyValuePair<string, SlotRelation> usableSubmodule in relationLookup)
                {
                    Array slotRealtions;
                    switch (slotRelType)
                    {
                        case SlotRelationType.FixedInSlots:
                            slotRealtions = usableSubmodule.Value.FixedInSlots;
                            break;
                        case SlotRelationType.UsedInSlots:
                            slotRealtions = usableSubmodule.Value.UsedInSlots;
                            break;
                        default:
                            slotRealtions = usableSubmodule.Value.AllowedInSlots;
                            break;
                    }
                    if (slotRealtions != null)
                    {
                        foreach (object sr in slotRealtions)
                        {
                            int subslotNumber;
                            bool conversionSuccessful = int.TryParse(
                                sr.ToString(),
                                NumberStyles.Integer,
                                CultureInfo.InvariantCulture,
                                out subslotNumber);
                            if (!conversionSuccessful)
                            {
                                throw new PNFunctionsException("Incorrect usable submodule subslot value.");
                            }
                            retval.Add(new KeyValuePair<string, int>(usableSubmodule.Key, subslotNumber));
                        }
                    }
                }
            }
            return retval;
        }
    }
}