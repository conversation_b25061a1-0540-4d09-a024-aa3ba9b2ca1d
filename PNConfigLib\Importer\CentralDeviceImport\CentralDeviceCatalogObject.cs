/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDeviceCatalogObject.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Xml.Serialization;
using PNConfigLib.DataModel.AttributeUtilities;

namespace PNConfigLib.CentralDeviceImport
{
    [XmlRoot("CentralDeviceCatalogObject", Namespace = "http://www.siemens.com/Automation/PNConfigLib/ControllerVariant")]
    [XmlInclude(typeof(Enumerated))]
    public class CentralDeviceCatalogObject
    {
        public List<CatalogAttribute> AttributeLookup { get; } = new List<CatalogAttribute>();
        public List<InterfaceCatalogItem> InterfaceCatalogItems { get; } = new List<InterfaceCatalogItem>();
    }
    public class InterfaceCatalogItem
    {
        public List<CatalogAttribute> AttributeLookup { get; } = new List<CatalogAttribute>();
        public List<PortCatalogItem> PortCatalogItems { get; } = new List<PortCatalogItem>();
    }
    public class PortCatalogItem
    {
        public List<CatalogAttribute> AttributeLookup { get; } = new List<CatalogAttribute>();
    }

    public class CatalogAttribute
    {
        public CatalogAttribute() { }

        public CatalogAttribute(string key, object value)
        {
            Key = key;
            Value = value;
        }

        [XmlAttribute]
        public string Key { get; set; }
        public object Value { get; set; }
    }
}
