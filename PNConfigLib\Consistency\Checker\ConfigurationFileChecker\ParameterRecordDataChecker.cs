/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ParameterRecordDataChecker.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

using GSDI;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class ParameterRecordDataChecker : IConsistencyChecker
    {
        //private const uint s_MaximumIOAddress = 32767;

        private const string s_BooleanTrue = "true";

        private const string s_BooleanFalse = "false";

        private const string s_HexadecimalPart = "0X";

        private readonly Devices m_Devices;

        private readonly ConfigReader.ListOfNodes.ListOfNodes m_ListOfNodes;

        internal ParameterRecordDataChecker(Devices devices, ConfigReader.ListOfNodes.ListOfNodes lon)
        {
            this.m_Devices = devices;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            AreParameterRecordDatasValid();
        }

        private void AreParameterRecordDatasValid()
        {

            if (m_Devices.DecentralDevice == null)
            {
                return;
            }

            foreach (DecentralDeviceType xmlDecentralDevice in m_Devices.DecentralDevice)
            {
                ConfigReader.ListOfNodes.DecentralDeviceType lonDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);

                if (lonDevice == null)
                {
                    continue;
                }

                string gsdFileName = Path.GetFileName(lonDevice.GSDPath);

                try
                {
                    CheckParameterRecordDataTypesAndMultipleVirtualSubmodule(lonDevice, gsdFileName, xmlDecentralDevice); // dummy method name

                    // module version       
                    if (xmlDecentralDevice.Module == null)
                    {
                        return;
                    }

                    BothIODeviceAndSubmoduleHaveParameterRecord(lonDevice, xmlDecentralDevice);
                    BothModuleAndSubmoduleHaveParameterRecord(lonDevice, gsdFileName, xmlDecentralDevice);
                }
                catch (ParameterRecordDataCheckerReturnException)
                {
                    return;
                }

            }
        }

        private void CheckParameterRecordDataTypesAndMultipleVirtualSubmodule(ConfigReader.ListOfNodes.DecentralDeviceType lonDevice, string gsdFileName, DecentralDeviceType xmlDecentralDevice)
        {
            if ((xmlDecentralDevice.ParameterRecordDataItems != null)
                && (xmlDecentralDevice.ParameterRecordDataItems.Count > 0))
            {
                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDevice.GSDRefID);

                if (decentralDeviceCatalog != null)
                {
                    if (decentralDeviceCatalog.VirtualSubmoduleList == null)
                    {
                        throw new ParameterRecordDataCheckerReturnException();
                    }

                    foreach (SubmoduleCatalog virtualSubmoduleCatalog in decentralDeviceCatalog
                        .VirtualSubmoduleList)
                    {
                        CheckParameterRecordDataTypes(
                                virtualSubmoduleCatalog,
                                xmlDecentralDevice.ParameterRecordDataItems,
                                xmlDecentralDevice.DeviceRefID);
                    }

                    if (decentralDeviceCatalog.VirtualSubmoduleList.Count > 1)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants
                                .XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord,
                            xmlDecentralDevice.DeviceRefID);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void BothIODeviceAndSubmoduleHaveParameterRecord(ConfigReader.ListOfNodes.DecentralDeviceType lonDevice, DecentralDeviceType xmlDecentralDevice)
        {
            if (xmlDecentralDevice.ParameterRecordDataItems?.Count > 0
                    && xmlDecentralDevice.Module.Exists(m => m.GSDRefID == lonDevice.GSDRefID))
            {
                ModuleType headModule =
                    xmlDecentralDevice.Module.FirstOrDefault(m => m.GSDRefID == lonDevice.GSDRefID);
                if (headModule != null
                    && headModule.Submodule.Exists(s => s.GSDRefID == lonDevice.GSDRefID)
                    && headModule.Submodule.FirstOrDefault(s => s.GSDRefID == lonDevice.GSDRefID)
                            ?.ParameterRecordDataItems.Count > 0)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_BothIODeviceAndSubmoduleHasParameterRecord,
                        xmlDecentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void BothModuleAndSubmoduleHaveParameterRecord(ConfigReader.ListOfNodes.DecentralDeviceType lonDevice, string gsdFileName, DecentralDeviceType xmlDecentralDevice)
        {
            foreach (ModuleType xmlModule in xmlDecentralDevice.Module.Where(
                    m => !string.IsNullOrEmpty(m.GSDRefID) && (m.GSDRefID != lonDevice.GSDRefID)))
            {
                ModuleCatalog moduleCatalog =
                    ModuleCatalogHelper.GetModuleCatalogWithGsdName(gsdFileName, xmlModule.GSDRefID);
                if (moduleCatalog == null)
                {
                    throw new ParameterRecordDataCheckerReturnException();
                }

                if (xmlModule.ParameterRecordDataItems != null
                    && xmlModule.ParameterRecordDataItems.Count > 0)
                {
                    foreach (SubmoduleCatalog virtualSubmoduleCatalog in moduleCatalog.VirtualSubmoduleList)
                    {
                        CheckParameterRecordDataTypes(
                                virtualSubmoduleCatalog,
                                xmlModule.ParameterRecordDataItems,
                                xmlModule.ModuleID);
                    }
                }

                if (xmlModule.Submodule.Exists(w => w.GSDRefID == xmlModule.GSDRefID))
                {
                    ModuleTypeSubmodule xmlSubmodule =
                        xmlModule.Submodule.FirstOrDefault(w => w.GSDRefID == xmlModule.GSDRefID);

                    if (xmlModule.ParameterRecordDataItems?.Count > 0
                        && xmlSubmodule?.ParameterRecordDataItems.Count > 0)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_BothModuleAndSubmoduleHasParameterRecord,
                            xmlModule.ModuleID);
                        throw new ConsistencyCheckException();
                    }
                }

                if (xmlModule.ParameterRecordDataItems.Count > 0
                    && moduleCatalog.VirtualSubmoduleList.Count > 1)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord,
                        xmlModule.ModuleID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckParameterRecordDataTypes(
            PclCatalogObject pclCatalogObject,
            IReadOnlyCollection<ParameterRecordDataItemsTypeParameterRecordDataItem> customParameterRecordDataItems,
            string deviceRefID)
        {
            // check parameter record data types consistency by comparing user input (customParameterRecordDataItems) with
            // gsdml (pclObject.PclCatalogObject.ParameterRecordDataList)
            if (customParameterRecordDataItems == null)
            {
                return;
            }

            foreach (ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData in
                customParameterRecordDataItems)
            {
                if (pclCatalogObject.ParameterRecordDataList == null)
                {
                    return;
                }

                if (pclCatalogObject.ParameterRecordDataList != null)
                {
                    bool indexExistenceCheck = false;
                    bool byteBitOffsetExistenceCheck = false;

                    // find the matching parameter record data item in the GSDML
                    FindMatchingParameterRecordDataItemInGSDML(pclCatalogObject, customParameterRecordData, ref indexExistenceCheck,
                        byteBitOffsetExistenceCheck, deviceRefID);

                    if (!indexExistenceCheck)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_ParameterRecordDataIndexNotExist,
                            customParameterRecordData.GSDRefIndex,
                            deviceRefID);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void FindMatchingParameterRecordDataItemInGSDML(PclCatalogObject pclCatalogObject,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData, ref bool indexExistenceCheck,
            bool byteBitOffsetExistenceCheck, string deviceRefID)
        {
            foreach (ParameterRecordData parameterRecordData in pclCatalogObject.ParameterRecordDataList)
            {
                if (customParameterRecordData.GSDRefIndex == parameterRecordData.Index)
                {
                    indexExistenceCheck = true;
                    // check the data type consistency for each ref of the parameter record data item if there are any

                    if (parameterRecordData.Refs.Length != 0)
                    {
                        // run through all the refs in the user entered parameterRecordDataItem refs 
                        // then run through the gsdml parameterRecordDataItem refs to find the users ref and compare
                        for (int x = 0; x < customParameterRecordData.Ref.Count; x++)
                        {
                            FindAndCompare(parameterRecordData,
                                customParameterRecordData,
                                ref byteBitOffsetExistenceCheck,
                                deviceRefID,
                                x);

                            if (!byteBitOffsetExistenceCheck)
                            {
                                // consistency check for Ref
                                ConsistencyLogger.Log(
                                    ConsistencyType.XML,
                                    LogSeverity.Error,
                                    string.Empty,
                                    ConsistencyConstants.XML_ParameterRecordDataRefIDNotExist,
                                    customParameterRecordData.Ref[x].ByteOffset
                                        .ToString(CultureInfo.InvariantCulture),
                                    customParameterRecordData.Ref[x].BitOffset
                                        .ToString(CultureInfo.InvariantCulture),
                                    customParameterRecordData.GSDRefIndex,
                                    deviceRefID);
                                throw new ConsistencyCheckException();
                            }
                        }
                    }
                }
            }
        }

        private void FindAndCompare(ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            ref bool byteBitOffsetExistenceCheck, string deviceRefID, int x)
        {
            foreach (RefData refData in parameterRecordData.Refs)
            {
                if ((refData.ByteOffset == customParameterRecordData.Ref[x].ByteOffset)
                    && (!customParameterRecordData.Ref[x].BitOffsetSpecified
                        || (customParameterRecordData.Ref[x].BitOffset == refData.BitOffset)))
                {
                    byteBitOffsetExistenceCheck = true;

                    if (!refData.IsChangeable)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_ParameterRecordDataIsNotChangeable,
                            refData.ByteOffset,
                            refData.BitOffset,
                            deviceRefID);
                        throw new ConsistencyCheckException();
                    }

                    // valueitemtarget is optional area in gsdml. if it does not exist, then values is null
                    if (refData.Values != null)
                    {
                        // now the user entered value is checked
                        CheckParameterRecordDataItemValueTypeAndAreaType(
                            customParameterRecordData,
                            refData,
                            x,
                            deviceRefID);
                    }
                    else
                    {
                        CheckParameterRecordDataItemAreaType(
                            parameterRecordData,
                            customParameterRecordData,
                            refData,
                            x,
                            deviceRefID);
                        
                    }
                }
            }
        }

        private void CheckParameterRecordDataItemAreaType(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID)
        {
            switch (refData.DataType)
            {
                case DataTypes.GSDVisibleString: //
                    CheckGSDVisibleString(customParameterRecordData, refData, x, deviceRefID);
                    break;
                case DataTypes.GSDFloat32Unsigned8: 
                case DataTypes.GSDUnsigned8Unsigned8:
                    CheckGSDUnsigned8Unsigned8(parameterRecordData, customParameterRecordData, refData, x, deviceRefID);
                    break;
                case DataTypes.GSDOctetString2Unsigned8:
                    CheckGSDOctetString2Unsigned8(parameterRecordData, customParameterRecordData, refData, x, deviceRefID);
                    break;
                case DataTypes.GSDDate:
                    CheckGSDDate(parameterRecordData, customParameterRecordData, refData, x, deviceRefID);
                    break;
                case DataTypes.GSDOctetString:
                    CheckGSDOctetString(parameterRecordData, customParameterRecordData, refData, x, deviceRefID);
                    break;
                case DataTypes.GSDBoolean:
                    CheckGSDBoolean(parameterRecordData, customParameterRecordData, refData, x, deviceRefID);
                    break;
            }

        }

        private void CheckGSDVisibleString(
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            if (customParameterRecordData.Ref[x].Value.Length > refData.Length)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordLengthIsOutOfLimit,
                    refData.ByteOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckGSDUnsigned8Unsigned8(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            string[] parts = customParameterRecordData.Ref[x].Value.Split(',');
            if (parts.Length != 2
                || !parts[1].ToUpperInvariant().StartsWith(
                    s_HexadecimalPart,
                    StringComparison.Ordinal))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordFormatIsNotValid,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckGSDOctetString2Unsigned8(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            string[] octetParts = customParameterRecordData.Ref[x].Value
                    .Split(',');
            if (octetParts.Length != 3)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants
                        .XML_ParameterRecordOctetUnsignedFormatIsNotValid,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }

            foreach (string octetPart in octetParts)
            {
                if (!octetPart.Trim().ToUpperInvariant().StartsWith(
                        s_HexadecimalPart,
                        StringComparison.Ordinal))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants
                            .XML_ParameterRecordOctetUnsignedFormatIsNotValid,
                        parameterRecordData.Index,
                        refData.ByteOffset,
                        refData.BitOffset,
                        deviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckGSDDate(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            string[] dateParts = customParameterRecordData.Ref[x].Value
                    .Split('.');
            if (dateParts.Length > 1)
            {
                string milliSecondPart = dateParts[1];
                milliSecondPart = milliSecondPart.Trim();
                int millisecondValue;
                if (milliSecondPart.Length > 3
                    || milliSecondPart.Length == 0
                    || int.TryParse("milliSecondPart", out millisecondValue)
                    || dateParts.Length > 2)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants
                            .XML_ParameterRecordDateFormatIsNotValid,
                        parameterRecordData.Index,
                        refData.ByteOffset,
                        refData.BitOffset,
                        deviceRefID);
                    throw new ConsistencyCheckException();
                }
            }

            if (dateParts.Length == 0)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants
                        .XML_ParameterRecordDateFormatIsNotValid,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }

            string datePart = dateParts[0];
            datePart = datePart.Trim();
            DateTime dateValue;
            string[] formats = { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss" };
            if (!DateTime.TryParseExact(
                    datePart,
                    formats,
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.None,
                    out dateValue))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants
                        .XML_ParameterRecordDateFormatIsNotValid,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckGSDOctetString(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            string[] octetParts = customParameterRecordData.Ref[x].Value
                    .Split(',');
            if (octetParts.Length > refData.Length)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants
                        .XML_ParameterRecordOctetStringLengthExceeded,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }
        private void CheckGSDBoolean(
            ParameterRecordData parameterRecordData,
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID) 
        {
            if (!(string.Equals(
                          customParameterRecordData.Ref[x].Value,
                          s_BooleanTrue,
                          StringComparison.InvariantCulture) || string.Equals(
                          customParameterRecordData.Ref[x].Value,
                          s_BooleanFalse,
                          StringComparison.InvariantCulture)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordDataNotAllowedValue,
                    parameterRecordData.Index,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckParameterRecordDataItemValueTypeAndAreaType(
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int x,
            string deviceRefID)
        {
            Type type = refData.Values.GetValue(0).GetType();
            if (type == typeof(ValueItem))
            {
                CheckParameterRecordDataItemValueType(
                        customParameterRecordData,
                        refData,
                        x,
                        deviceRefID);

            }
            else if (type == typeof(AreaItem))
            {
                CheckParameterRecordDataItemAreaType(
                        customParameterRecordData,
                        refData,
                        x,
                        deviceRefID);
            }
        }

        private void CheckParameterRecordDataItemValueType(
            ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordData,
            RefData refData,
            int counter,
            string deviceRefID)
        {
            bool checkAllowedValues = false;
            // check if the value given by the user is one of the allowed values written in the GSDML file by going through the allowed values
            foreach (ValueItem valueItem in refData.Values)
            {
                if (valueItem.Value.ToString() == customParameterRecordData.Ref[counter].Value)
                {
                    checkAllowedValues = true;
                    break;
                }
            }
            if (!checkAllowedValues)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordDataNotAllowedValue,
                    customParameterRecordData.GSDRefIndex,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckParameterRecordDataItemAreaType(
            ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData,
            RefData refData,
            int i,
            string deviceRefID)
        {
            AreaItem range = (AreaItem)(refData.Values.GetValue(0));
            try
            {
                switch (refData.DataType)
                {
                    case DataTypes.GSDBit:
                        // alllow the value to be only zero or one else throw an exception
                        CheckGSDBit(customparameterRecordData, i);
                        break;
                    case DataTypes.GSDInteger8:
                        CheckGSDInteger8(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDUnsigned8:
                        CheckGSDUnsigned8(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDInteger16:
                    case DataTypes.Gsdn2:
                    case DataTypes.Gsdx2:
                        CheckGsdx2(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDUnsigned16:
                    case DataTypes.Gsdr2:
                    case DataTypes.Gsdt2:
                    case DataTypes.Gsdd2:
                        CheckGsdd2(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDInteger32:
                    case DataTypes.Gsdn4:
                    case DataTypes.Gsdx4:
                        CheckGsdx4(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDUnsigned32:
                    case DataTypes.Gsdt4:
                        CheckGsdt4(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDFloat32:
                        CheckGSDFloat32(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDInteger64:
                        CheckGSDInteger64(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDBitArea:
                    case DataTypes.GSDUnsigned64:
                        CheckGSDUnsigned64(customparameterRecordData, range, i);
                        break;
                    case DataTypes.GSDFloat64:
                        CheckGSDFloat64(customparameterRecordData, i);
                        break;
                    case DataTypes.GSDBoolean:
                        CheckGSDBoolean(customparameterRecordData, i);
                        break;
                    case DataTypes.GSDDate:
                    case DataTypes.GSDOctetString2Unsigned8:
                        break;
                    default:
                        throw new InvalidOperationException(
                            string.Format(
                                CultureInfo.InvariantCulture,
                                "Not implemented data type: {0}",
                                refData.DataType));
                }
            }
            catch (ParameterRecordDataCheckerReturnException)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordDataNotAllowedValue,
                    customparameterRecordData.GSDRefIndex,
                    refData.ByteOffset,
                    refData.BitOffset,
                    deviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckGSDBit(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, int i)
        {
            if (!((customparameterRecordData.Ref[i].Value == "1") ||
                              (customparameterRecordData.Ref[i].Value == "0")))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDInteger8(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            sbyte check8;
            sbyte max8 = sbyte.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            sbyte min8 = sbyte.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            // the user entered value has beenconverted from string to integer16
            bool checkBool8 = sbyte.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out check8);
            if (!(checkBool8 && (check8 >= min8) && (check8 <= max8)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDUnsigned8(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            byte checkU8;
            byte maxU8 = byte.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            byte minU8 = byte.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            // the user entered value has beenconverted from string to integer16
            bool checkBoolU8 = byte.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out checkU8);
            if (!(checkBoolU8 && (checkU8 >= minU8) && (checkU8 <= maxU8)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGsdx2(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            short check16;
            short max16 = short.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            short min16 = short.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            // the user entered value has beenconverted from string to integer16
            bool checkBool16 = short.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out check16);
            if (!(checkBool16 && (check16 >= min16) && (check16 <= max16)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGsdd2(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            ushort checkU16;
            ushort maxU16 = ushort.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            ushort minU16 = ushort.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            // the user entered value has beenconverted from string to integer16
            bool checkBoolU16 = ushort.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out checkU16);
            if (!(checkBoolU16 && (checkU16 >= minU16) && (checkU16 <= maxU16)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGsdx4(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            int check32;
            int max32 = int.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            int min32 = int.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            bool checkBool32 = int.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out check32);
            if (!(checkBool32 && (check32 >= min32) && (check32 <= max32)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGsdt4(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            uint checkU32;
            uint maxU32 = uint.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            uint minU32 = uint.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            bool checkBoolU32 = uint.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out checkU32);
            if (!(checkBoolU32 && (checkU32 >= minU32) && (checkU32 <= maxU32)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDFloat32(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            float checkF32;
            float maxF32 = (float)range.MaxValue;
            float minF32 = (float)range.MinValue;
            bool checkBoolF32 = float.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Float,
                CultureInfo.InvariantCulture,
                out checkF32);
            if (!(checkBoolF32 && (checkF32 >= minF32) && (checkF32 <= maxF32)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDInteger64(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            long check64;
            long max64 = long.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            long min64 = long.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            bool checkBool64 = long.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out check64);
            if (!(checkBool64 && (check64 >= min64) && (check64 <= max64)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDUnsigned64(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, AreaItem range, int i)
        {
            ulong checkU64;
            ulong maxU64 = ulong.Parse(range.MaxValue.ToString(), CultureInfo.InvariantCulture);
            ulong minU64 = ulong.Parse(range.MinValue.ToString(), CultureInfo.InvariantCulture);
            bool checkBoolU64 = ulong.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Integer,
                CultureInfo.InvariantCulture,
                out checkU64);
            if (!(checkBoolU64 && (checkU64 >= minU64) && (checkU64 <= maxU64)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDFloat64(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, int i)
        {
            double checkDouble;
            bool checkBoolDouble = double.TryParse(
                customparameterRecordData.Ref[i].Value,
                NumberStyles.Float,
                CultureInfo.InvariantCulture,
                out checkDouble);
            if (!(checkBoolDouble && (checkDouble >= double.MinValue) && (checkDouble <= double.MaxValue)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }

        private void CheckGSDBoolean(ParameterRecordDataItemsTypeParameterRecordDataItem customparameterRecordData, int i)
        {
            if (!(string.Equals(customparameterRecordData.Ref[i].Value, s_BooleanTrue, StringComparison.InvariantCulture)
                            || string.Equals(customparameterRecordData.Ref[i].Value, s_BooleanFalse, StringComparison.InvariantCulture)))
            {
                throw new ParameterRecordDataCheckerReturnException();
            }
        }
    }
}
