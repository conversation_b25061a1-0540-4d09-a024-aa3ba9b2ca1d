/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: PortSubmoduleConverter.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Globalization;

using GSDI;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Port;

using PNConfigLib.Gsd.Interpreter.Common;

using PortSubmodule = PNConfigLib.Gsd.Interpreter.Common.PortSubmodule;
using PNConfigLib.HWCNBL.Utilities;
using System.Xml;
using System;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal sealed class PortSubmoduleConverter
    {
        private PortCatalog m_Port;
        private PortSubmodule m_PortSubmoduleItem;

        private const uint s_AutomaticMautypeValue = 8;

        public PortSubmoduleConverter(PortSubmodule portSubmoduleItem)
        {
            m_PortSubmoduleItem = portSubmoduleItem;
        }

        private bool IsIrt
        {
            // There is no safe way to check if a port supports IRT. The attribute MaxPortRxDelay is
            // mandatory for ports with IRT. If the value is higher than 0 we assume that the port supports
            // IRT.
            get
            {
                return m_PortSubmoduleItem.MaxPortRxDelay > 0;
            }
        }

        public PortCatalog Convert(string fileName)
        {
            m_Port = new PortCatalog { GsdId = m_PortSubmoduleItem.GsdID };
            AddAttributes(fileName);
            if (m_PortSubmoduleItem != null)
            {
                m_Port.ParameterRecordDataList = m_PortSubmoduleItem.ParameterRecordData;
            }

            InitDecorators();

            return m_Port;
        }
        private void AddAttributes(string fileName)
        {
            AddPNBasicPortAttributes();
            AddNameAttribute();
            AddTypeNameAttribute();
            AddInvariantTypeNameAttribute();
            AddIoTypeAttribute();
            AddPNFiberOpticAttribute();
            AddPNLinkStateDiagnosisCapabilityAttribute();
            AddPNPowerBudgetControlSupportedAttribute();
            AddPNEthernetMediumDuplexAttribute();
            AddCatalogInfoVariables();
            AddPNSubslotNumberAttribute();

            ConvertMrp();
            RedefineGeneralMenu(fileName);
        }

        private void ConvertMrp()
        {
            if (m_PortSubmoduleItem.IsDefaultRingport)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnMrpIsDefaultRingPort,
                        m_PortSubmoduleItem.IsDefaultRingport);
            }
            if (m_PortSubmoduleItem.SupportsRingportConfig)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnMrpSupportsRingConfig,
                   m_PortSubmoduleItem.SupportsRingportConfig);
            }

        }


        private void AddPNSubslotNumberAttribute()
        {
            if (m_PortSubmoduleItem.SubslotNumber != 0)
            {
                m_Port.AttributeAccess.AddAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    (int)m_PortSubmoduleItem.SubslotNumber);
            }
        }

        private void AddCatalogInfoVariables()
        {
            if (m_PortSubmoduleItem.Info != null)
            {
                // Description
                string description = Converter.GetTextFromTextId(m_PortSubmoduleItem.Info.InfoTextID);
                m_Port.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.Description,
                    description);

                // OrderNumber
                m_Port.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.OrderNumber,
                    m_PortSubmoduleItem.Info.OrderNumber);

                // FwVersion
                m_Port.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.FwVersion,
                    m_PortSubmoduleItem.Info.SoftwareRelease);

                // HwVersion
                m_Port.AttributeAccess.AddAnyAttribute<string>(
                    InternalAttributeNames.HwVersion,
                    m_PortSubmoduleItem.Info.HardwareRelease);
            }
        }

        private void AddPNEthernetMediumDuplexAttribute()
        {
            Enumerated mauTypes = new Enumerated();

            if (m_PortSubmoduleItem.MAUTypeList != null)
            {
                foreach (MauTypeItem mautype in m_PortSubmoduleItem.MAUTypeList.MAUTypeItems)
                {
                    mauTypes.List.Add((uint)mautype.Value);
                }
            }
            else if (m_PortSubmoduleItem.MAUTypes != null && m_PortSubmoduleItem.MAUTypes.Length > 0)
            {
                foreach (uint mauType in m_PortSubmoduleItem.MAUTypes)
                {
                    mauTypes.List.Add(mauType);
                }
            }
            else
            {
                if (m_PortSubmoduleItem.MAUType != GSDI.MauTypes.GSDMauNone)
                {
                    GSDI.MauTypes mauType = m_PortSubmoduleItem.MAUType;
                    mauTypes.List.Add((uint)mauType);
                }
            }

            // Cu ports must have value "TP 100 Mbit/s Vollduplex"
            if (m_PortSubmoduleItem.FiberOpticTypes == null || m_PortSubmoduleItem.FiberOpticTypes.Length == 0)
            {
                if (!mauTypes.List.Contains((uint)16))
                {
                    mauTypes.List.Add((uint)16);
                }
            }
            if (!mauTypes.List.Contains(s_AutomaticMautypeValue))
            {
                mauTypes.List.Add(s_AutomaticMautypeValue);
            }
            mauTypes.DefaultValue = s_AutomaticMautypeValue;
            m_Port.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnEthernetMediumDuplex,
                mauTypes);
        }

        private void AddPNPowerBudgetControlSupportedAttribute()
        {
            if (m_PortSubmoduleItem.IsPowerBudgetControlSupported)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnPowerBudgetControlSupported,
                    m_PortSubmoduleItem.IsPowerBudgetControlSupported);
            }
        }

        private void AddPNLinkStateDiagnosisCapabilityAttribute()
        {
            if (m_PortSubmoduleItem.LinkStateDiagnosisCapability != LinkStateDiagnosisCapabilities.GSDLsdcNone)
            {
                uint pnLinkStateDiagnosisCapability = 0;
                switch (m_PortSubmoduleItem.LinkStateDiagnosisCapability)
                {
                    case LinkStateDiagnosisCapabilities.GSDLsdcUp:
                        {
                            pnLinkStateDiagnosisCapability = 1;
                            break;
                        }
                    case LinkStateDiagnosisCapabilities.GSDLsdcDown:
                        {
                            pnLinkStateDiagnosisCapability = 2;
                            break;
                        }
                    case LinkStateDiagnosisCapabilities.GSDLsdcUpanddown:
                        {
                            pnLinkStateDiagnosisCapability = 3;
                            break;
                        }
                }
                m_Port.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnLinkStateDiagnosisCapability,
                    pnLinkStateDiagnosisCapability);
            }
        }

        private void AddIoTypeAttribute()
        {
            //IoType.Diagnosis
            if (!m_PortSubmoduleItem.IsParameterizationDisallowed)
            {
                m_Port.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.IoType, 0x0040);
            }
        }

        private void AddInvariantTypeNameAttribute()
        {
            if (m_PortSubmoduleItem.Info != null)
            {
                string infoName = Converter.GetTextFromTextId(m_PortSubmoduleItem.Info.NameTextID);
                m_Port.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.InvariantTypeName, infoName);
            }
        }

        private void AddNameAttribute()
        {
            string name = Converter.GetTextFromTextId(m_PortSubmoduleItem.NameTextID);
            m_Port.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Name, name);
        }

        private void RedefineGeneralMenu(string fileName)
        {
            AddCatalogInformationMenu(fileName);

        }
        private void AddCatalogInformationMenu(string fileName)
        {
            m_Port.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GSDFileName, fileName);

        }

        private const string s_FoTemplateString = "0000000{0}0000000{1}";

        private void AddPNFiberOpticAttribute()
        {
            IList<string> cableTypes = new List<string>
                                           {
                                               AttributeValues.CableTypes.CabeType1,
                                               AttributeValues.CableTypes.CabeType2,
                                               AttributeValues.CableTypes.CabeType3,
                                               AttributeValues.CableTypes.CabeTypeFoStandardCableGp50,
                                               AttributeValues.CableTypes.CableTypeFoTrailingCableGp,
                                               AttributeValues.CableTypes.CableTypeFoGroundCable,
                                               AttributeValues.CableTypes.CableTypeFoStandardCable625,
                                               AttributeValues.CableTypes.CableTypeFlexibleFoCable,
                                               AttributeValues.CableTypes.CableTypePofStandardCableGp,
                                               AttributeValues.CableTypes.CableTypePofTrailingCable,
                                               AttributeValues.CableTypes.CableTypePcfStandardCableGp,
                                               AttributeValues.CableTypes.CableTypePcfTrailingCableGp
                                           };

            if (m_PortSubmoduleItem.FiberOpticTypes != null
                && m_PortSubmoduleItem.FiberOpticTypes.Length > 0)
            {
                Enumerated enumEntry = new Enumerated();
                foreach (uint foType in m_PortSubmoduleItem.FiberOpticTypes)
                {
                    for (int i = 1; i <= 3; i++)
                    {
                        string foString = string.Format(CultureInfo.InvariantCulture, s_FoTemplateString, foType, i);
                        if (cableTypes.Contains(foString))
                        {
                            enumEntry.List.Add(long.Parse(foString, NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture));
                        }
                    }
                }
                enumEntry.DefaultValue = enumEntry.List[0];
                m_Port.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnFiberOptic, enumEntry);
            }
        }

        private void AddPNIrtGenerateAdjustPreambleLengthV14Attribute()
        {
            m_Port.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIrtGenerateAdjustPreambleLengthV14,
                m_PortSubmoduleItem.IsShortPreamble100MBitSupported);
        }

        private void AddPNIrtShortPreamble100MBitSupportedAttribute()
        {

            if (m_PortSubmoduleItem.IsShortPreamble100MBitSupportedExt)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIrtShortPreamble100MBitSupported,
                true);
            }
        }
        private void AddPNBasicPortAttributes()
        {
            // PNSubmoduleIdentNumber
            m_Port.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnSubmoduleIdentNumber,
                m_PortSubmoduleItem.IdentNumber);

            if (m_PortSubmoduleItem.IsPortDeactivationSupported)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnPortDeactivationSupported,
                    m_PortSubmoduleItem.IsPortDeactivationSupported);
            }

            // PNParameterizationDisallowed
            if (m_PortSubmoduleItem.IsParameterizationDisallowed)
            {
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnParameterizationDisallowed,
                    m_PortSubmoduleItem.IsParameterizationDisallowed);
            }

            // PNCheckMauTypeSupported
            if (m_PortSubmoduleItem.IsCheckMAUTypeSupported)
            {
                // Default value for attribute PNCheckMAUTypeSupported (obsolete) in base is false.
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnCheckMAUTypeSupported,
                    m_PortSubmoduleItem.IsCheckMAUTypeSupported);
            }
            else
            {
                // if CheckMAUTypeSupported attribute is present or true 
                // or if CheckMauTypeAttributeSupported attribute is missing and PortDeactivation is not supported
                if (m_PortSubmoduleItem.IsCheckMAUTypeSupportedAttributePresent
                     || !m_PortSubmoduleItem.IsPortDeactivationSupported)
                {
                    // Default value for attribute PNCheckMAUTypeRecordSupported in base is true.
                    m_Port.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnCheckMAUTypeRecordSupported,
                        false);
                }
            }
            if (m_PortSubmoduleItem.IsCheckMauTypeDifferenceSupportedAttributePresent)
            {
                // PNCheckMauTypeDifferenceSupported
                m_Port.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnCheckMAUTypeDifferenceSupported,
                    m_PortSubmoduleItem.IsCheckMauTypeDifferenceSupported);
            }
            else
            {
                m_Port.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnCheckMAUTypeDifferenceSupported);
            }

            if (IsIrt)
            {
                // PNIrtPortRxDelay
                m_Port.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortRxDelay,
                    m_PortSubmoduleItem.MaxPortRxDelay);

                // PNIrtPortTxDelay
                m_Port.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortTxDelay,
                    m_PortSubmoduleItem.MaxPortTxDelay);

                ConvertPreambleShortening();
            }

            if (m_PortSubmoduleItem.MAUTypeList != null)
            {
                m_Port.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnDontGenerateAdjustMAUTypeBlock);
                AddPnDontGenerateAdjustMAUTypeBlock();
            }
        }

        private void AddPnDontGenerateAdjustMAUTypeBlock()
        {
            if (m_PortSubmoduleItem.MAUTypeList.MAUTypeItems.Length == 0)
            {
                return;
            }

            Enumerated mauTypes = new Enumerated();

            foreach (MauTypeItem mauTypeItem in m_PortSubmoduleItem.MAUTypeList.MAUTypeItems)
            {
                if (!mauTypeItem.IsAdjustSupported)
                {
                    mauTypes.List.Add(mauTypeItem.Value);
                }
            }

            if (!mauTypes.List.Contains(s_AutomaticMautypeValue))
            {
                mauTypes.List.Add(s_AutomaticMautypeValue);
            }
            mauTypes.DefaultValue = s_AutomaticMautypeValue;

            m_Port.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnDontGenerateAdjustMAUTypeBlock,
                mauTypes);
        }

        private void AddTypeNameAttribute()
        {
            if (m_PortSubmoduleItem.Info != null)
            {
                string infoName = Converter.GetTextFromTextId(m_PortSubmoduleItem.Info.NameTextID);
                m_Port.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.TypeName, infoName);
            }
        }

        private void ConvertPreambleShortening()
        {
            AddPNIrtShortPreamble100MBitSupportedAttribute();
            AddPNIrtGenerateAdjustPreambleLengthV14Attribute();
        }

        private void InitDecorators()
        {
            m_Port.DecorationList.Add(typeof(PNIoPortBusinessLogic));

            if (IsIrt)
            {
                m_Port.DecorationList.Add(typeof(PNIrtPortBusinessLogic));
            }
            if (m_PortSubmoduleItem.IsDefaultRingport || m_PortSubmoduleItem.SupportsRingportConfig)
            {
                m_Port.DecorationList.Add(typeof(PNMrpPortBL));
            }
        }
    }
}