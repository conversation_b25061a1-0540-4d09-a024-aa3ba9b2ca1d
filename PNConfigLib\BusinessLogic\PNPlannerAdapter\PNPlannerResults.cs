/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerResults.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.DomainManagement;

#endregion

namespace PNConfigLib.HWCNBL
{
    /// <summary>
    /// The class that stores the results of PNPlanner.
    /// </summary>
    internal class PNPlannerResults
    {
        #region construct

        /// <summary>
        /// The default constructor.
        /// </summary>
        public PNPlannerResults()
        {
            InitPNPlannerResultStructures();
        }

        #endregion

        #region private functions

        /// <summary>
        /// Initializes PNPlanner result structures.
        /// </summary>
        private void InitPNPlannerResultStructures()
        {
            //Initialize Config2008 Blocks
            Config2008FramesBlocks = new Dictionary<string, List<IPNPlannerOutputFrame>>();
            Config2008BeginEndAssigmentBlocks =
                new Dictionary<string, Dictionary<int, List<IPNPlannerOutputBeginEndAssignment>>>();
            Config2008PhaseAssigmentBlocks =
                new Dictionary<string, Dictionary<int, List<IPNPlannerOutputPhaseAssignment>>>();
            Config2008MaxLineRxDelays = new Dictionary<string, Dictionary<int, int>>();
            BandwidthsForPhases = new Dictionary<string, List<IPNPlannerOutputBandwidthsForPhases>>();
            AllIrtTopPorts = new Dictionary<int, HashSet<PortElement>>();
            LocalRxPeriodNoSyncValues = new Dictionary<PclObject, long>();
        }

        #endregion

        #region calculated PNPlanner values from SyncDomainBusinessLogic

        /// <summary>
        /// Gets the Calculated Part for RT (in nanoseconds).
        /// </summary>
        public long CalculatedPartRt { get; set; }

        /// <summary>
        /// Gets the Calculated Part for IRT (in nanoseconds).
        /// </summary>
        public long CalculatedPartIrt { get; set; }

        /// <summary>
        /// Gets the length of the maximum RT-Class 1 & 2 Frame within IO-Systems of the Sync-Domain.
        /// The frame length is calculated starting from the ethernet header up to and including the frame check
        /// sequence.
        /// </summary>
        public uint MaxRTC12FrameLength { get; set; }

        /// <summary>
        /// OutputFingerprint element of the PNPlanner output file.
        /// </summary>
        public string PNPlannerOutputFingerprint { get; set; }

        /// <summary>
        /// Gets the maximum reduction ratio of the sync domain.
        /// Normally this property is IRTTop-island specific, but it is a user error if the
        /// domain contains more than one IRTTop island. PNPlanner is only called if there is
        /// one IRTTop island, so it is safe to accept this property as a SyncDomain specific property.
        /// </summary>
        /// <remarks>
        /// Warning: This property is set when PNPlanner input xml is prepared.
        /// Calls before this will probably return a wrong value.
        /// </remarks>
        public long MaxReductionRatio { get; set; }

        #endregion

        #region calculated PNPlanner values from PNPlannerHelper

        /// <summary>
        /// Dictionary: key = devicename, value = FrameBlock
        /// </summary>
        public Dictionary<string, List<IPNPlannerOutputFrame>> Config2008FramesBlocks { get; set; }

        /// <summary>
        ///   Dictionary: key = devicename, value = Dictionary :
        ///   key = portNumber(0-Based), value = BeginEndAssignmentblocks
        /// </summary>
        public Dictionary<string, Dictionary<int, List<IPNPlannerOutputBeginEndAssignment>>>
            Config2008BeginEndAssigmentBlocks
        { get; set; }

        /// <summary>
        /// Dictionary: key = devicename, value = Dictionary :
        /// key = portNumber(0-Based), value = PhaseAssignmentblocks
        /// </summary>
        public Dictionary<string, Dictionary<int, List<IPNPlannerOutputPhaseAssignment>>> Config2008PhaseAssigmentBlocks
        {
            get; set; }

        /// <summary>
        /// Dictionary: key = devicename, value = Dictionary :
        /// key = portNumber(0-Based), value = LineRxDelay of the port
        /// </summary>
        public Dictionary<string, Dictionary<int, int>> Config2008MaxLineRxDelays { get; set; }

        /// <summary>
        /// Key = devicename, value = list which contains bandwidths for each phase
        /// </summary>
        public Dictionary<string, List<IPNPlannerOutputBandwidthsForPhases>> BandwidthsForPhases { get; set; }

        /// <summary>
        /// Key = HashCode of interface submodule, value = hashset, which contains all irttop ports.
        /// Key of the hashset = port number
        /// </summary>
        public Dictionary<int, HashSet<PortElement>> AllIrtTopPorts { get; set; }

        /// <summary>
        /// Dictionary: key = devicename, value = Dictionary :
        /// key = portNumber(0-Based), value = LineRxDelay of the port
        /// </summary>
        public Dictionary<PclObject, long> LocalRxPeriodNoSyncValues { get; set; }

        #endregion
    }
}