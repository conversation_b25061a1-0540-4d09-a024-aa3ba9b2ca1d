﻿<?xml version="1.0" encoding="utf-8"?>
<ISO15745Profile xmlns="http://www.profibus.com/GSDML/2003/11/DeviceProfile" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.profibus.com/GSDML/2003/11/DeviceProfile ..\xsd\GSDML-DeviceProfile-V2.43.xsd">
  <!-- ProfileHeader Definition as defined in ISO15745-1. Please do not change the content. -->
  <ProfileHeader>
    <ProfileIdentification>PROFINET Device Profile</ProfileIdentification>
    <ProfileRevision>1.00</ProfileRevision>
    <ProfileName>Device Profile for PROFINET Devices</ProfileName>
    <ProfileSource>PROFIBUS Nutzerorganisation e. V. (PNO)</ProfileSource>
    <ProfileClassID>Device</ProfileClassID>
    <ISO15745Reference>
      <ISO15745Part>4</ISO15745Part>
      <ISO15745Edition>1</ISO15745Edition>
      <ProfileTechnology>GSDML</ProfileTechnology>
    </ISO15745Reference>
  </ProfileHeader>
  <ProfileBody>
    <DeviceIdentity VendorID="0x002A" DeviceID="0x0008">
      <InfoText TextId="TOK_DevIdent_InfoText" />
      <VendorName Value="SIEMENS" />
    </DeviceIdentity>
    <DeviceFunction>
      <Family MainFamily="I/O" ProductFamily="DEVKIT" />
    </DeviceFunction>
    <ApplicationProcess>
      <DeviceAccessPointList>
        <!-- =================================================================================== -->
        <!-- DAP1: Standard, RT, IRT, IsoM, MRP, Shared Device and Dynamic Reconfiguration (CiR) -->
        <!-- =================================================================================== -->
        <DeviceAccessPointItem ID="DAP 1" PhysicalSlots="0..16" ModuleIdentNumber="0x00000001" MinDeviceInterval="4" ImplementationType="ERTEC200P" DNS_CompatibleName="ERTEC-DEVKit" FixedInSlots="0" ObjectUUID_LocalIndex="1" MultipleWriteSupported="true" SharedDeviceSupported="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" MaxSupportedRecordSize="8192" NameOfStationNotTransferable="true" ParameterizationSpeedupSupported="false" LLDP_NoD_Supported="true" ResetToFactoryModes="2" CheckDeviceID_Allowed="true" PowerOnToCommReady="490" IOXS_Required="false" RequiredSchemaVersion="V2.4" PNIO_Version="V2.43" PrmBeginPrmEndSequenceSupported="true" CIR_Supported="true" AddressAssignment="LOCAL;DCP">
          <ModuleInfo CategoryRef="ID_ERTEC200PDEVKit">
            <Name TextId="TOK_Standard_MRP_S2_DR" />
            <InfoText TextId="TOK_ModInfo_InfoTextId_DAP1" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7 195-3BE00-0YA1" />
            <!-- optional keyword:  <HardwareRelease Value="0002"/>    -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <CertificationInfo ConformanceClass="C" ApplicationClass="Isochronous;HighPerformance;HighAvailability" NetloadClass="III" />
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="TOK_Subslot_8000" />
            <SubslotItem SubslotNumber="32769" TextId="TOK_Subslot_8001" />
            <SubslotItem SubslotNumber="32770" TextId="TOK_Subslot_8002" />
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" MaxDataLength="2880" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_10" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_11" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_16" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_17" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_18" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_50" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_51" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_52" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_53" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..16" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="DAP 1" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" MayIssueProcessAlarm="false">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_Standard_MRP_S2_DR" />
                <InfoText TextId="TOK_ModInfo_InfoTextId_DAP1" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_1I" SubslotNumber="32768" SubmoduleIdentNumber="0x0002" IsochroneModeSupported="true" IsochroneModeInRT_Classes="RT_CLASS_3" SupportedRT_Classes="RT_CLASS_1;RT_CLASS_3" TextId="TOK_DAP_InterfaceModule" SupportedProtocols="SNMP;LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" MulticastBoundarySupported="true" DCP_HelloSupported="true" MaxFrameStartTime="1600" MinNRT_Gap="960" DelayMeasurementSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" PDEV_CombinedObjectSupported="true" SupportedDelayMeasurements="PTCP">
              <RT_Class3Properties ForwardingMode="Absolute;Relative" MaxBridgeDelay="2088" MaxBridgeDelayFFW="1024" MaxNumberIR_FrameData="1024" MaxRangeIR_FrameID="1024" StartupMode="Legacy;Advanced" MaxDFP_Frames="1" MaxDFP_Feed="340" AlignDFP_Subframes="false" FragmentationType="Dynamic" MaxRedPeriodLength="4000" MinFSO="1760" MinRTC3_Gap="1120" MinYellowTime="10240" YellowSafetyMargin="160" MaxRetentionTime="262000" />
              <SynchronisationMode SupportedRole="SyncSlave" MaxLocalJitter="300" T_PLL_MAX="1000" SupportedSyncProtocols="PTCP" PeerToPeerJitter="250" />
              <ApplicationRelations NumberOfAR="4" StartupMode="Legacy;Advanced" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" PullModuleAlarmSupported="true">
                <!-- ReductionRatio="1 2 4 8 16 32 64 128 256 512 1024 2048 4096 8192 16384" /> -->
                <TimingProperties ReductionRatio="1 2 4 8 16 32 64 128 256 512" SendClock="8 16 32 64 128" />
                <RT_Class3TimingProperties ReductionRatioPow2="2 4 8 16" SendClock="4 6 8 12 16 20 24 28 32 36 40 44 48 52 56 60 64 68 72 76 80 84 88 92 96 100 104 108 112 116 120 124 128" ReductionRatio="1" />
              </ApplicationRelations>
              <MediaRedundancy SupportedRole="Client" MRPD_Supported="true" AdditionalForwardingRulesSupported="true" />
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_1P1" SubslotNumber="32769" SubmoduleIdentNumber="0x0003" TextId="TOK_Port1" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_1P2" SubslotNumber="32770" SubmoduleIdentNumber="0x0003" TextId="TOK_Port2" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_1" />
          </Graphics>
          <AssetManagement />
          <SystemRedundancy NumberOfAR_Sets="1" RT_InputOnBackupAR_Supported="false" MaxSwitchOverTime="30" DeviceTypes="S2" DeviceType="S2" />
        </DeviceAccessPointItem>
        <!-- ======================================================================== -->
        <!-- DAP2: Supports S2 & DR, does not support IRT and IsoM                    -->
        <!-- DAP2: Standard, RT, MRP, Shared Device and Dynamic Reconfiguration (CiR) -->
        <!-- ======================================================================== -->
        <DeviceAccessPointItem ID="DAP 2" PhysicalSlots="0..16" ModuleIdentNumber="0x00000002" MinDeviceInterval="8" ImplementationType="ERTEC200P" DNS_CompatibleName="ERTEC-DEVKit" FixedInSlots="0" ObjectUUID_LocalIndex="1" MultipleWriteSupported="true" SharedDeviceSupported="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" MaxSupportedRecordSize="8192" NameOfStationNotTransferable="true" ParameterizationSpeedupSupported="false" LLDP_NoD_Supported="true" ResetToFactoryModes="2" CheckDeviceID_Allowed="true" PowerOnToCommReady="490" IOXS_Required="false" RequiredSchemaVersion="V2.4" PNIO_Version="V2.43" PrmBeginPrmEndSequenceSupported="true" CIR_Supported="true" AddressAssignment="LOCAL;DCP">
          <ModuleInfo CategoryRef="ID_ERTEC200PDEVKit">
            <Name TextId="TOK_Standard_NoIRT_NoIsoM" />
            <InfoText TextId="TOK_ModInfo_InfoTextId_DAP2" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7 195-3BE00-0YA1" />
            <!-- optional keyword:  <HardwareRelease Value="0002"/>    -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <CertificationInfo ConformanceClass="B" ApplicationClass="HighAvailability" NetloadClass="III" />
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="TOK_Subslot_8000" />
            <SubslotItem SubslotNumber="32769" TextId="TOK_Subslot_8001" />
            <SubslotItem SubslotNumber="32770" TextId="TOK_Subslot_8002" />
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" MaxDataLength="2880" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_10" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_11" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_16" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_17" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_18" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..16" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="DAP 2" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" MayIssueProcessAlarm="false">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_Standard_NoIRT_NoIsoM" />
                <InfoText TextId="TOK_ModInfo_InfoTextId_DAP2" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_2I" SubslotNumber="32768" SubmoduleIdentNumber="0x0002" SupportedRT_Classes="RT_CLASS_1" TextId="TOK_DAP_InterfaceModule" SupportedProtocols="SNMP;LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" MulticastBoundarySupported="true" DCP_HelloSupported="true" MaxFrameStartTime="1600" MinNRT_Gap="960" DelayMeasurementSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" PDEV_CombinedObjectSupported="true" SupportedDelayMeasurements="PTCP">
              <ApplicationRelations NumberOfAR="4" StartupMode="Legacy;Advanced" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" PullModuleAlarmSupported="true">
                <!-- ReductionRatio="1 2 4 8 16 32 64 128 256 512 1024 2048 4096 8192 16384" /> -->
                <TimingProperties ReductionRatio="1 2 4 8 16 32 64 128 256 512" SendClock="8 16 32 64 128" />
              </ApplicationRelations>
              <MediaRedundancy SupportedRole="Client" AdditionalForwardingRulesSupported="true" />
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_2P1" SubslotNumber="32769" SubmoduleIdentNumber="0x0003" TextId="TOK_Port1" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_2P2" SubslotNumber="32770" SubmoduleIdentNumber="0x0003" TextId="TOK_Port2" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_2" />
          </Graphics>
          <AssetManagement />
          <SystemRedundancy NumberOfAR_Sets="1" RT_InputOnBackupAR_Supported="false" MaxSwitchOverTime="30" DeviceTypes="S2" DeviceType="S2" />
        </DeviceAccessPointItem>
        <!-- ======================================================================== -->
        <!-- DAP3: Standard, RT, IRT, IsoM, MRP, Shared Device and Dynamic Reconfiguration (CiR) -->
        <!-- ======================================================================== -->
        <DeviceAccessPointItem ID="DAP 3" PhysicalSlots="0..16" ModuleIdentNumber="0x00000003" MinDeviceInterval="4" ImplementationType="ERTEC200P" DNS_CompatibleName="ERTEC-DEVKit" FixedInSlots="0" ObjectUUID_LocalIndex="1" MultipleWriteSupported="true" SharedDeviceSupported="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" MaxSupportedRecordSize="8192" NameOfStationNotTransferable="true" ParameterizationSpeedupSupported="false" LLDP_NoD_Supported="true" ResetToFactoryModes="2" CheckDeviceID_Allowed="true" PowerOnToCommReady="490" IOXS_Required="false" RequiredSchemaVersion="V2.4" PNIO_Version="V2.43" PrmBeginPrmEndSequenceSupported="true" AddressAssignment="LOCAL;DCP">
          <ModuleInfo CategoryRef="ID_ERTEC200PDEVKit">
            <Name TextId="TOK_Standard_NoS2_NoDR" />
            <InfoText TextId="TOK_ModInfo_InfoTextId_DAP3" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7 195-3BE00-0YA1" />
            <!-- optional keyword:  <HardwareRelease Value="0002"/>    -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <CertificationInfo ConformanceClass="C" ApplicationClass="Isochronous;HighPerformance" NetloadClass="III" />
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="TOK_Subslot_8000" />
            <SubslotItem SubslotNumber="32769" TextId="TOK_Subslot_8001" />
            <SubslotItem SubslotNumber="32770" TextId="TOK_Subslot_8002" />
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" MaxDataLength="2880" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_10" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_11" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_16" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_17" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_18" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_50" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_51" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_52" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_53" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..16" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="DAP 3" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" MayIssueProcessAlarm="false">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_Standard_NoS2_NoDR" />
                <InfoText TextId="TOK_ModInfo_InfoTextId_DAP3" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_3I" SubslotNumber="32768" SubmoduleIdentNumber="0x0002" IsochroneModeSupported="true" IsochroneModeInRT_Classes="RT_CLASS_3" SupportedRT_Classes="RT_CLASS_1;RT_CLASS_3" TextId="TOK_DAP_InterfaceModule" SupportedProtocols="SNMP;LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" MulticastBoundarySupported="true" DCP_HelloSupported="true" MaxFrameStartTime="1600" MinNRT_Gap="960" DelayMeasurementSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" PDEV_CombinedObjectSupported="true" SupportedDelayMeasurements="PTCP">
              <RT_Class3Properties ForwardingMode="Absolute;Relative" MaxBridgeDelay="2088" MaxBridgeDelayFFW="1024" MaxNumberIR_FrameData="1024" MaxRangeIR_FrameID="1024" StartupMode="Legacy;Advanced" MaxDFP_Frames="1" MaxDFP_Feed="340" AlignDFP_Subframes="false" FragmentationType="Dynamic" MaxRedPeriodLength="4000" MinFSO="1760" MinRTC3_Gap="1120" MinYellowTime="10240" YellowSafetyMargin="160" MaxRetentionTime="262000" />
              <SynchronisationMode SupportedRole="SyncSlave" MaxLocalJitter="300" T_PLL_MAX="1000" SupportedSyncProtocols="PTCP" PeerToPeerJitter="250" />
              <ApplicationRelations NumberOfAR="4" StartupMode="Legacy;Advanced" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" PullModuleAlarmSupported="true">
                <!-- ReductionRatio="1 2 4 8 16 32 64 128 256 512 1024 2048 4096 8192 16384" /> -->
                <TimingProperties ReductionRatio="1 2 4 8 16 32 64 128 256 512" SendClock="8 16 32 64 128" />
                <RT_Class3TimingProperties ReductionRatioPow2="2 4 8 16" SendClock="4 6 8 12 16 20 24 28 32 36 40 44 48 52 56 60 64 68 72 76 80 84 88 92 96 100 104 108 112 116 120 124 128" ReductionRatio="1" />
              </ApplicationRelations>
              <MediaRedundancy SupportedRole="Client" MRPD_Supported="true" AdditionalForwardingRulesSupported="true" />
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_3P1" SubslotNumber="32769" SubmoduleIdentNumber="0x0003" TextId="TOK_Port1" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_3P2" SubslotNumber="32770" SubmoduleIdentNumber="0x0003" TextId="TOK_Port2" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_3" />
          </Graphics>
          <AssetManagement />
        </DeviceAccessPointItem>
        <!-- ===================================================== -->
        <!-- DAP4: Supports RT and MRP, does not support S2 and DR -->
        <!-- DAP4: Standard, RT, MRP                               -->
        <!-- ===================================================== -->
        <DeviceAccessPointItem ID="DAP 4" PhysicalSlots="0..16" ModuleIdentNumber="0x00000004" MinDeviceInterval="8" ImplementationType="ERTEC200P" DNS_CompatibleName="ERTEC-DEVKit" FixedInSlots="0" ObjectUUID_LocalIndex="1" MultipleWriteSupported="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" MaxSupportedRecordSize="8192" NameOfStationNotTransferable="true" ParameterizationSpeedupSupported="false" LLDP_NoD_Supported="true" ResetToFactoryModes="2" CheckDeviceID_Allowed="true" PowerOnToCommReady="490" IOXS_Required="false" RequiredSchemaVersion="V2.4" PNIO_Version="V2.43" PrmBeginPrmEndSequenceSupported="true" AddressAssignment="LOCAL;DCP">
          <ModuleInfo CategoryRef="ID_ERTEC200PDEVKit">
            <Name TextId="TOK_Standard_MRP" />
            <InfoText TextId="TOK_ModInfo_InfoTextId_DAP4" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7 195-3BE00-0YA1" />
            <!-- optional keyword:  <HardwareRelease Value="0002"/>    -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <CertificationInfo ConformanceClass="B" ApplicationClass="" NetloadClass="III" />
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="TOK_Subslot_8000" />
            <SubslotItem SubslotNumber="32769" TextId="TOK_Subslot_8001" />
            <SubslotItem SubslotNumber="32770" TextId="TOK_Subslot_8002" />
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" MaxDataLength="2880" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_10" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_11" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_16" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_17" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_18" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..16" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="DAP 4" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" MayIssueProcessAlarm="false">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_Standard_MRP" />
                <InfoText TextId="TOK_ModInfo_InfoTextId_DAP4" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_4I" SubslotNumber="32768" SubmoduleIdentNumber="0x0002" SupportedRT_Classes="RT_CLASS_1" TextId="TOK_DAP_InterfaceModule" SupportedProtocols="SNMP;LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" MulticastBoundarySupported="true" DCP_HelloSupported="true" MaxFrameStartTime="1600" MinNRT_Gap="960" DelayMeasurementSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" PDEV_CombinedObjectSupported="true" SupportedDelayMeasurements="PTCP">
              <ApplicationRelations NumberOfAR="1" StartupMode="Legacy;Advanced" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" PullModuleAlarmSupported="true">
                <!-- ReductionRatio="1 2 4 8 16 32 64 128 256 512 1024 2048 4096 8192 16384" /> -->
                <TimingProperties ReductionRatio="1 2 4 8 16 32 64 128 256 512" SendClock="8 16 32 64 128" />
              </ApplicationRelations>
              <MediaRedundancy SupportedRole="Client" AdditionalForwardingRulesSupported="true" />
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_4P1" SubslotNumber="32769" SubmoduleIdentNumber="0x0003" TextId="TOK_Port1" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_4P2" SubslotNumber="32770" SubmoduleIdentNumber="0x0003" TextId="TOK_Port2" MaxPortRxDelay="237" MaxPortTxDelay="21" IsDefaultRingport="true" MAUType="100BASETXFD" MAUTypes="16" PortDeactivationSupported="true" LinkStateDiagnosisCapability="Up+Down" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="198" MaxPortTxDelay="6" -->
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_4" />
          </Graphics>
          <AssetManagement />
        </DeviceAccessPointItem>
        <!-- ===================== -->
        <!-- DAP5:  Standard 2 POF -->
        <!-- ===================== -->
        <DeviceAccessPointItem ID="DAP 5" PhysicalSlots="0..16" ModuleIdentNumber="0x00000005" MinDeviceInterval="4" ImplementationType="ERTEC200P" DNS_CompatibleName="ERTEC-DEVKit" FixedInSlots="0" ObjectUUID_LocalIndex="1" MultipleWriteSupported="true" SharedDeviceSupported="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" MaxSupportedRecordSize="8192" NameOfStationNotTransferable="true" ParameterizationSpeedupSupported="true" LLDP_NoD_Supported="true" ResetToFactoryModes="2" CheckDeviceID_Allowed="true" PowerOnToCommReady="490" IOXS_Required="false" RequiredSchemaVersion="V2.4" PNIO_Version="V2.43" AddressAssignment="LOCAL;DCP">
          <ModuleInfo CategoryRef="ID_ERTEC200PDEVKit">
            <Name TextId="TOK_Standard_2POF" />
            <InfoText TextId="TOK_ModInfo_InfoTextId_DAP5" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7 195-3BE00-0YA1" />
            <!-- optional keyword:  <HardwareRelease Value="0002"/>    -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <CertificationInfo ConformanceClass="C" ApplicationClass="Isochronous;HighPerformance" NetloadClass="III" />
          <SubslotList>
            <SubslotItem SubslotNumber="32768" TextId="TOK_Subslot_8000" />
            <SubslotItem SubslotNumber="32769" TextId="TOK_Subslot_8001" />
            <SubslotItem SubslotNumber="32770" TextId="TOK_Subslot_8002" />
          </SubslotList>
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" MaxDataLength="2880" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_10" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_11" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_16" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_17" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_18" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_50" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_51" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_52" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_53" AllowedInSlots="1..16" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..16" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="DAP 5" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" MayIssueProcessAlarm="false">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_Standard_2POF" />
                <InfoText TextId="TOK_ModInfo_InfoTextId_DAP5" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_5I" SubslotNumber="32768" SubmoduleIdentNumber="0x0002" IsochroneModeSupported="true" IsochroneModeInRT_Classes="RT_CLASS_3" SupportedRT_Classes="RT_CLASS_1;RT_CLASS_3" TextId="TOK_DAP_InterfaceModule" SupportedProtocols="SNMP;LLDP" PTP_BoundarySupported="true" DCP_BoundarySupported="true" MulticastBoundarySupported="true" DCP_HelloSupported="true" MaxFrameStartTime="1600" MinNRT_Gap="960" DelayMeasurementSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true" PDEV_CombinedObjectSupported="true" SupportedDelayMeasurements="PTCP">
              <RT_Class3Properties ForwardingMode="Absolute;Relative" MaxBridgeDelay="2088" MaxBridgeDelayFFW="1024" MaxRangeIR_FrameID="1024" MaxNumberIR_FrameData="1024" StartupMode="Legacy;Advanced" MaxDFP_Frames="1" MaxDFP_Feed="340" AlignDFP_Subframes="false" FragmentationType="Dynamic" MaxRedPeriodLength="4000" MinFSO="1760" MinRTC3_Gap="1120" MinYellowTime="10240" YellowSafetyMargin="160" MaxRetentionTime="262000" />
              <SynchronisationMode SupportedRole="SyncSlave" MaxLocalJitter="300" T_PLL_MAX="1000" SupportedSyncProtocols="PTCP" PeerToPeerJitter="250" />
              <ApplicationRelations NumberOfAR="4" StartupMode="Legacy;Advanced" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" PullModuleAlarmSupported="true">
                <!-- ReductionRatio="1 2 4 8 16 32 64 128 256 512 1024 2048 4096 8192 16384" /> -->
                <TimingProperties ReductionRatio="1 2 4 8 16 32 64 128 256 512" SendClock="8 16 32 64 128" />
                <RT_Class3TimingProperties ReductionRatioPow2="2 4 8 16" SendClock="4 6 8 12 16 20 24 28 32 36 40 44 48 52 56 60 64 68 72 76 80 84 88 92 96 100 104 108 112 116 120 124 128" ReductionRatio="1" />
              </ApplicationRelations>
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_5P1" SubslotNumber="32769" SubmoduleIdentNumber="0x0003" TextId="TOK_Port1" MaxPortRxDelay="180" MaxPortTxDelay="20" MAUTypes="54" FiberOpticTypes="4 5" PortDeactivationSupported="true" PowerBudgetControlSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="100" MaxPortTxDelay="2" -->
              <MAUTypeList>
                <MAUTypeItem Value="54" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_5P2" SubslotNumber="32770" SubmoduleIdentNumber="0x0003" TextId="TOK_Port2" MaxPortRxDelay="180" MaxPortTxDelay="20" MAUTypes="54" FiberOpticTypes="4 5" PortDeactivationSupported="true" PowerBudgetControlSupported="true" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="true" CheckMAUTypeSupported="true" CheckMAUTypeDifferenceSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <!-- Former port delay values which can also be used together with ERTEC200P-2 : MaxPortRxDelay="100" MaxPortTxDelay="2" -->
              <MAUTypeList>
                <MAUTypeItem Value="54" AdjustSupported="true" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_5" />
          </Graphics>
          <AssetManagement />
        </DeviceAccessPointItem>
      </DeviceAccessPointList>
      <!-- ============================================ -->
      <!--            List of modules                   -->
      <!-- ============================================ -->
      <ModuleList>
        <!-- ================================== -->
        <!--    1 byte digital input/output     -->
        <!--   (param-rec, support PROFIenergy)-->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_01" ModuleIdentNumber="0x00000020">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1IO" />
            <InfoText TextId="TOK_InfoTextId_Module_1IO" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="1" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x20,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1IO" />
                <InfoText TextId="TOK_InfoTextId_Module_1IO" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--         1 byte digital input       -->
        <!--         (support PROFIenergy)      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_02" ModuleIdentNumber="0x00000021">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1I" />
            <InfoText TextId="TOK_InfoTextId_Module_1I" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="2" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1I" />
                <InfoText TextId="TOK_InfoTextId_Module_1I" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--       1 byte digital output        -->
        <!--         (support PROFIenergy)      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_03" ModuleIdentNumber="0x00000022">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1O" />
            <InfoText TextId="TOK_InfoTextId_Module_1O" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="3" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1O" />
                <InfoText TextId="TOK_InfoTextId_Module_1O" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--    64 byte digital input/output    -->
        <!--   (param-rec, support PROFIenergy) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_10" ModuleIdentNumber="0x00000029">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64IO" />
            <InfoText TextId="TOK_InfoTextId_Module_64IO" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="10" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_64" Length="64" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_64" Length="64" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x29,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64IO" />
                <InfoText TextId="TOK_InfoTextId_Module_64IO" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--         64 byte digital input      -->
        <!--   (param-rec, support PROFIenergy) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_11" ModuleIdentNumber="0x00000030">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64I" />
            <InfoText TextId="TOK_InfoTextId_Module_64I" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="11" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_64" Length="64" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x30,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64I" />
                <InfoText TextId="TOK_InfoTextId_Module_64I" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--       64 byte digital output       -->
        <!--   (param-rec, support PROFIenergy) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_12" ModuleIdentNumber="0x00000031">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64O" />
            <InfoText TextId="TOK_InfoTextId_Module_64O" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="12" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_64" Length="64" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x31,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64O" />
                <InfoText TextId="TOK_InfoTextId_Module_64O" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital input/output       -->
        <!--         (support PROFIenergy)      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_16" ModuleIdentNumber="0x0000002d">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250IO" />
            <InfoText TextId="TOK_InfoTextId_Module_250IO" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="16" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_250" Length="250" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_250" Length="250" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250IO" />
                <InfoText TextId="TOK_InfoTextId_Module_250IO" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital input        -->
        <!--         (support PROFIenergy)      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_17" ModuleIdentNumber="0x0000002e">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250I" />
            <InfoText TextId="TOK_InfoTextId_Module_250I" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="17" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_250" Length="250" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250I" />
                <InfoText TextId="TOK_InfoTextId_Module_250I" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital output       -->
        <!--         (support PROFIenergy)      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_18" ModuleIdentNumber="0x0000002f">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250O" />
            <InfoText TextId="TOK_InfoTextId_Module_250O" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="18" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_250" Length="250" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250O" />
                <InfoText TextId="TOK_InfoTextId_Module_250O" />
              </ModuleInfo>
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ===================================== -->
        <!--  64 byte digital input, isochronous   -->
        <!--         (support PROFIenergy)      -->
        <!-- ===================================== -->
        <ModuleItem ID="ID_Mod_50" ModuleIdentNumber="0x00000050">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64I_Isochron" />
            <InfoText TextId="TOK_InfoTextId_Module_64I_Isochron" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="50" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_64_Isochron" Length="64" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64I_Isochron" />
                <InfoText TextId="TOK_InfoTextId_Module_64I_Isochron" />
              </ModuleInfo>
              <IsochroneMode IsochroneModeRequired="false" T_DC_Base="1" T_DC_Max="128" T_DC_Min="4" T_IO_Base="1000" T_IO_InputMin="10" T_IO_OutputMin="40" />
              <!--<PROFIenergy ProfileVersion="V1.3"/> -->
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ===================================== -->
        <!--  64 byte digital output, isochronous  -->
        <!--         (support PROFIenergy)         -->
        <!-- ===================================== -->
        <ModuleItem ID="ID_Mod_51" ModuleIdentNumber="0x00000051">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64O_Isochron" />
            <InfoText TextId="TOK_InfoTextId_Module_64O_Isochron" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="51" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_64_Isochron" Length="64" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64O_Isochron" />
                <InfoText TextId="TOK_InfoTextId_Module_64O_Isochron" />
              </ModuleInfo>
              <IsochroneMode IsochroneModeRequired="false" T_DC_Base="1" T_DC_Max="128" T_DC_Min="4" T_IO_Base="1000" T_IO_InputMin="10" T_IO_OutputMin="40" />
              <!-- <PROFIenergy ProfileVersion="V1.0"/> -->
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ===================================== -->
        <!--  1 byte digital input, isochronous   -->
        <!--         (support PROFIenergy)      -->
        <!-- ===================================== -->
        <ModuleItem ID="ID_Mod_52" ModuleIdentNumber="0x00000052">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1I_Isochron" />
            <InfoText TextId="TOK_InfoTextId_Module_1I_Isochron" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="52" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1_Isochron" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1I_Isochron" />
                <InfoText TextId="TOK_InfoTextId_Module_1I_Isochron" />
              </ModuleInfo>
              <IsochroneMode IsochroneModeRequired="false" T_DC_Base="1" T_DC_Max="128" T_DC_Min="4" T_IO_Base="1000" T_IO_InputMin="10" T_IO_OutputMin="40" />
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ===================================== -->
        <!--  1 byte digital output, isochronous  -->
        <!--         (support PROFIenergy)         -->
        <!-- ===================================== -->
        <ModuleItem ID="ID_Mod_53" ModuleIdentNumber="0x00000053">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1O_Isochron" />
            <InfoText TextId="TOK_InfoTextId_Module_1O_Isochron" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="53" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1_Isochron" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1O_Isochron" />
                <InfoText TextId="TOK_InfoTextId_Module_1O_Isochron" />
              </ModuleInfo>
              <IsochroneMode IsochroneModeRequired="false" T_DC_Base="1" T_DC_Max="128" T_DC_Min="4" T_IO_Base="1000" T_IO_InputMin="10" T_IO_OutputMin="40" />
              <PROFIenergy ProfileVersion="V1.3" EntityClass="Class3" EntitySubclass="Subclass1">
                <EnergySavingModeList>
                  <EnergySavingModeItem ID="1" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="2" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                  <EnergySavingModeItem ID="255" TimeToPause="600" RTTO="600" TimeMinLengthOfStay="200" />
                </EnergySavingModeList>
                <MeasurementList>
                  <MeasurementItem Number="0">
                    <MeasurementValue ID="13" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="1">
                    <MeasurementValue ID="14" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                  <MeasurementItem Number="2">
                    <MeasurementValue ID="15" AccuracyDomain="1" AccuracyClass="3" />
                  </MeasurementItem>
                </MeasurementList>
              </PROFIenergy>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ========================================================= -->
        <!--  Multi API input module   ModuleID 0x60                   -->
        <!-- ========================================================= -->
        <ModuleItem ID="ID_Mod_60" ModuleIdentNumber="0x00000060">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_MultiSub" />
            <InfoText TextId="TOK_InfoTextId_Module_MultiSub" />
            <!-- optional keyword:  <HardwareRelease Value="0002" />   -->
            <!-- optional keyword:  <SoftwareRelease Value="V5.1" />   -->
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="601" SubmoduleIdentNumber="0x0001" FixedInSubslots="1" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1I" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="602" SubmoduleIdentNumber="0x0001" FixedInSubslots="2" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1I" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="603" SubmoduleIdentNumber="0x0002" FixedInSubslots="3" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1O" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="604" SubmoduleIdentNumber="0x0002" FixedInSubslots="4" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1O" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
      </ModuleList>
      <!-- ============================================ -->
      <!--       Channel diagnosis list                 -->
      <!-- ============================================ -->
      <ChannelDiagList>
        <ChannelDiagItem ErrorType="16">
          <Name TextId="TOK_Name_ErrorType16" />
          <Help TextId="TOK_HelpName_ErrorType16" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="17">
          <Name TextId="TOK_Name_ErrorType17" />
          <Help TextId="TOK_HelpName_ErrorType17" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="18">
          <Name TextId="TOK_Name_ErrorType18" />
          <Help TextId="TOK_HelpName_ErrorType18" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="20">
          <Name TextId="TOK_Name_ErrorType20" />
          <Help TextId="TOK_HelpName_ErrorType20" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="21">
          <Name TextId="TOK_Name_ErrorType21" />
          <Help TextId="TOK_HelpName_ErrorType21" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="22">
          <Name TextId="TOK_Name_ErrorType22" />
          <Help TextId="TOK_HelpName_ErrorType22" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="23">
          <Name TextId="TOK_Name_ErrorType23" />
          <Help TextId="TOK_HelpName_ErrorType23" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="24">
          <Name TextId="TOK_Name_ErrorType24" />
          <Help TextId="TOK_HelpName_ErrorType24" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="25">
          <Name TextId="TOK_Name_ErrorType25" />
          <Help TextId="TOK_HelpName_ErrorType25" />
        </ChannelDiagItem>
        <ChannelDiagItem ErrorType="26">
          <Name TextId="TOK_Name_ErrorType26" />
          <Help TextId="TOK_HelpName_ErrorType26" />
        </ChannelDiagItem>
      </ChannelDiagList>
      <GraphicsList>
        <GraphicItem ID="ID_Graph_1" GraphicFile="GSDML-002A-0008-ERTEC200P_EvalKit_DAP1" />
        <GraphicItem ID="ID_Graph_2" GraphicFile="GSDML-002A-0008-ERTEC200P_EvalKit_DAP2" />
        <GraphicItem ID="ID_Graph_3" GraphicFile="GSDML-002A-0008-ERTEC200P_EvalKit_DAP3" />
        <GraphicItem ID="ID_Graph_4" GraphicFile="GSDML-002A-0008-ERTEC200P_EvalKit_DAP4" />
        <GraphicItem ID="ID_Graph_5" GraphicFile="GSDML-002A-0008-ERTEC200P_EvalKit_DAP5" />
      </GraphicsList>
      <CategoryList>
        <CategoryItem ID="ID_ERTEC200PDEVKit" TextId="TOK_Category_ERTEC200PDEVKit" />
      </CategoryList>
      <!-- ============================================ -->
      <!--     language dependent text lists            -->
      <!-- ============================================ -->
      <ExternalTextList>
        <!-- ============================= -->
        <!--    primary language: english  -->
        <!-- ============================= -->
        <PrimaryLanguage>
          <Text TextId="TOK_DevIdent_InfoText" Value="ERTEC 200P Evaluation Kit" />
          <!--DAP - ERTEC 200p Devkit-->
          <Text TextId="TOK_Category_ERTEC200PDEVKit" Value="ERTEC 200P Evaluation Kit" />
          <Text TextId="TOK_Standard_MRP" Value="Standard, MRP" />
          <Text TextId="TOK_Standard_NoIRT_NoIsoM" Value="Standard, MRP, No IRT, No IsoM" />
          <Text TextId="TOK_Standard_NoS2_NoDR" Value="Standard,  MRP, No S2, No DR" />
          <Text TextId="TOK_Standard_2POF" Value="Standard, 2 POF Ports" />
          <Text TextId="TOK_Standard_MRP_S2" Value="Standard, MRP, S2 redundancy" />
          <Text TextId="TOK_Standard_MRP_S2_DR" Value="Standard, MRP, S2 redundancy, DR" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP1" Value="ERTEC 200P Evaluation Kit, standard, RT, IRT, IsoM, MRP, Shared Device, S2, DR" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP2" Value="ERTEC 200P Evaluation Kit, standard, no IRT, IsoM" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP3" Value="ERTEC 200P Evaluation Kit, standard, no S2, DR" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP4" Value="ERTEC 200P Evaluation Kit, standard, no IRT, IsoM, S2, DR" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP5" Value="ERTEC 200P Evaluation Kit, standard, POF, shared device" />
          <!--port name-->
          <Text TextId="TOK_Subslot_8000" Value="X1" />
          <Text TextId="TOK_Subslot_8001" Value="X1 P1" />
          <Text TextId="TOK_Subslot_8002" Value="X1 P2" />
          <Text TextId="TOK_DAP_InterfaceModule" Value="PN-IO" />
          <Text TextId="TOK_Port1" Value="Port 1" />
          <Text TextId="TOK_Port2" Value="Port 2" />
          <!--module name-->
          <Text TextId="TOK_TextId_Module_1IO" Value="  1 byte  IO" />
          <Text TextId="TOK_TextId_Module_1I" Value="  1 byte  I" />
          <Text TextId="TOK_TextId_Module_1O" Value="  1 byte  O" />
          <Text TextId="TOK_TextId_Module_64IO" Value=" 64 bytes IO" />
          <Text TextId="TOK_TextId_Module_64I" Value=" 64 bytes I" />
          <Text TextId="TOK_TextId_Module_64O" Value=" 64 bytes O" />
          <Text TextId="TOK_TextId_Module_250IO" Value="250 bytes IO" />
          <Text TextId="TOK_TextId_Module_250I" Value="250 bytes I" />
          <Text TextId="TOK_TextId_Module_250O" Value="250 bytes O" />
          <Text TextId="TOK_TextId_Module_64I_Isochron" Value=" 64 bytes I IRT" />
          <Text TextId="TOK_TextId_Module_64O_Isochron" Value=" 64 bytes O IRT" />
          <Text TextId="TOK_TextId_Module_1I_Isochron" Value=" 1 byte I IRT" />
          <Text TextId="TOK_TextId_Module_1O_Isochron" Value=" 1 byte O IRT" />
          <Text TextId="TOK_TextId_Module_MultiSub" Value=" multi subslots 1I 1I 1O 1O" />
          <Text TextId="TOK_TextId_MultiSub1I" Value="  1 byte  I" />
          <Text TextId="TOK_TextId_MultiSub1O" Value="  1 byte  O" />
          <!--module info name-->
          <Text TextId="TOK_InfoTextId_Module_1IO" Value="1 byte IO (overall consistency) - ModuleIdentNumber:0x20 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1I" Value="1 byte I (overall consistency) - ModuleIdentNumber:0x21 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1O" Value="1 byte O (overall consistency) - ModuleIdentNumber:0x22 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64IO" Value="64 bytes IO (overall consistency) - ModuleIdentNumber:0x29 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64I" Value="64 bytes I (overall consistency) - ModuleIdentNumber:0x30 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64O" Value="64 bytes O (overall consistency) - ModuleIdentNumber:0x31 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250IO" Value="250 bytes IO (overall consistency) - ModuleIdentNumber:0x2D - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250I" Value="250 bytes I (overall consistency) - ModuleIdentNumber:0x2E - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250O" Value="250 bytes O (overall consistency) - ModuleIdentNumber:0x2F - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64I_Isochron" Value="64 bytes I (isochrone, overall consistency) - ModuleIdentNumber:0x50 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64O_Isochron" Value="64 bytes O (isochrone, overall consistency) - ModuleIdentNumber:0x51 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1I_Isochron" Value="1 byte I (isochrone, overall consistency) - ModuleIdentNumber:0x52 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1O_Isochron" Value="1 byte O (isochrone, overall consistency) - ModuleIdentNumber:0x53 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_MultiSub" Value="1 byte I + 1 byte I + 1 byte O + 1 byte O - ModuleIdentNumber:0x60" />
          <Text TextId="TOK_InfoTextId_MultiSub1I" Value="1 byte I (overall consistency) - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_MultiSub1O" Value="1 byte O (overall consistency) - SubmoduleIdentNumber:0x0002 - API:0x00" />
          <!--dataitem name-->
          <Text TextId="TOK_Input_DataItem_1" Value="Input 1 byte" />
          <Text TextId="TOK_Output_DataItem_1" Value="Output 1 byte" />
          <Text TextId="TOK_Input_DataItem_64" Value="Input 64 bytes" />
          <Text TextId="TOK_Output_DataItem_64" Value="Output 64 bytes" />
          <Text TextId="TOK_Input_DataItem_250" Value="Input 250 bytes" />
          <Text TextId="TOK_Output_DataItem_250" Value="Output 250 bytes" />
          <Text TextId="TOK_Input_DataItem_64_Isochron" Value="Input 64 bytes IRT" />
          <Text TextId="TOK_Output_DataItem_64_Isochron" Value="Output 64 bytes IRT" />
          <Text TextId="TOK_Input_DataItem_1_Isochron" Value="Input 1 byte IRT" />
          <Text TextId="TOK_Output_DataItem_1_Isochron" Value="Output 1 byte IRT" />
          <!--errortype name-->
          <Text TextId="TOK_Name_ErrorType16" Value="parameter assignment error" />
          <Text TextId="TOK_Name_ErrorType17" Value="Power supply fault " />
          <Text TextId="TOK_Name_ErrorType18" Value="fuse blown  " />
          <Text TextId="TOK_Name_ErrorType20" Value="ground fault" />
          <Text TextId="TOK_Name_ErrorType21" Value="reference point lost" />
          <Text TextId="TOK_Name_ErrorType22" Value="process event lost / sampling error" />
          <Text TextId="TOK_Name_ErrorType23" Value="threshold warning" />
          <Text TextId="TOK_Name_ErrorType24" Value="output disabled" />
          <Text TextId="TOK_Name_ErrorType25" Value="safety-related event " />
          <Text TextId="TOK_Name_ErrorType26" Value="external fault" />
          <Text TextId="TOK_HelpName_ErrorType16" Value="parameter assignment error" />
          <Text TextId="TOK_HelpName_ErrorType17" Value="Power supply fault " />
          <Text TextId="TOK_HelpName_ErrorType18" Value="fuse blown  " />
          <Text TextId="TOK_HelpName_ErrorType20" Value="ground fault" />
          <Text TextId="TOK_HelpName_ErrorType21" Value="reference point lost" />
          <Text TextId="TOK_HelpName_ErrorType22" Value="process event lost / sampling error" />
          <Text TextId="TOK_HelpName_ErrorType23" Value="threshold warning" />
          <Text TextId="TOK_HelpName_ErrorType24" Value="output disabled" />
          <Text TextId="TOK_HelpName_ErrorType25" Value="safety-related event " />
          <Text TextId="TOK_HelpName_ErrorType26" Value="external fault" />
          <!--other text definitions-->
          <Text TextId="T_general_parameter" Value="general parameter" />
        </PrimaryLanguage>
        <!-- ==================== -->
        <!--      german          -->
        <!-- ==================== -->
        <Language xml:lang="de">
          <Text TextId="TOK_DevIdent_InfoText" Value="ERTEC 200P Evaluation Kit" />
          <!--DAP - ERTEC 200 Devkit-->
          <Text TextId="TOK_Category_ERTEC200PDEVKit" Value="ERTEC 200P Evaluation Kit" />
          <Text TextId="TOK_Standard_MRP" Value="Standard, MRP" />
          <Text TextId="TOK_Standard_NoIRT_NoIsoM" Value="Standard, MRP, No IRT, No IsoM" />
          <Text TextId="TOK_Standard_NoS2_NoDR" Value="Standard,  MRP, No S2, No DR" />
          <Text TextId="TOK_Standard_2POF" Value="Standard, 2 POF Ports" />
          <Text TextId="TOK_Standard_MRP_S2" Value="Standard, MRP, S2 redundancy" />
          <Text TextId="TOK_Standard_MRP_S2_DR" Value="Standard, MRP, S2 redundancy, DR" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP1" Value="ERTEC 200P Evaluation Kit, für PNIO Controller ohne Physical Device" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP2" Value="ERTEC 200P Evaluation Kit, Standard, kein MRP, Shared Device" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP3" Value="ERTEC 200P Evaluation Kit, Standard, MRP, Shared Device" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP4" Value="ERTEC 200P Evaluation Kit, Standard, 1 Port, Shared Device" />
          <Text TextId="TOK_ModInfo_InfoTextId_DAP5" Value="ERTEC 200P Evaluation Kit, Standard, POF, Shared Device" />
          <!--port name-->
          <Text TextId="TOK_Subslot_8000" Value="X1" />
          <Text TextId="TOK_Subslot_8001" Value="X1 P1" />
          <Text TextId="TOK_Subslot_8002" Value="X1 P2" />
          <Text TextId="TOK_DAP_InterfaceModule" Value="PN-IO" />
          <Text TextId="TOK_Port1" Value="Port 1" />
          <Text TextId="TOK_Port2" Value="Port 2" />
          <!--module name-->
          <Text TextId="TOK_TextId_Module_1IO" Value="  1 Byte  EA" />
          <Text TextId="TOK_TextId_Module_1I" Value="  1 Byte  E" />
          <Text TextId="TOK_TextId_Module_1O" Value="  1 Byte  A" />
          <Text TextId="TOK_TextId_Module_64IO" Value=" 64 Byte  EA" />
          <Text TextId="TOK_TextId_Module_64I" Value=" 64 Byte  E" />
          <Text TextId="TOK_TextId_Module_64O" Value=" 64 Byte  A" />
          <Text TextId="TOK_TextId_Module_250IO" Value="250 Byte  EA" />
          <Text TextId="TOK_TextId_Module_250I" Value="250 Byte  E" />
          <Text TextId="TOK_TextId_Module_250O" Value="250 Byte  A" />
          <Text TextId="TOK_TextId_Module_64I_Isochron" Value=" 64 bytes E IRT" />
          <Text TextId="TOK_TextId_Module_64O_Isochron" Value=" 64 bytes A IRT" />
          <Text TextId="TOK_TextId_Module_1I_Isochron" Value=" 1 byte E IRT" />
          <Text TextId="TOK_TextId_Module_1O_Isochron" Value=" 1 byte A IRT" />
          <Text TextId="TOK_TextId_Module_MultiSub" Value=" Multi Subslots 1E 1E 1A 1A" />
          <Text TextId="TOK_TextId_MultiSub1I" Value="  1 Byte  E" />
          <Text TextId="TOK_TextId_MultiSub1O" Value="  1 Byte  A" />
          <!--module info name-->
          <Text TextId="TOK_InfoTextId_Module_1IO" Value="1 Byte EA (Gesamtkonsistenz) - ModuleIdentNumber:0x20 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1I" Value="1 Byte E (Gesamtkonsistenz) - ModuleIdentNumber:0x21 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1O" Value="1 Byte A (Gesamtkonsistenz) - ModuleIdentNumber:0x22 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64IO" Value="64 Byte EA (Gesamtkonsistenz) - ModuleIdentNumber:0x29 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64I" Value="64 Byte E (Gesamtkonsistenz) - ModuleIdentNumber:0x30 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64O" Value="64 Byte A (Gesamtkonsistenz) - ModuleIdentNumber:0x31 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250IO" Value="250 Byte EA (Gesamtkonsistenz) - ModuleIdentNumber:0x2D - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250I" Value="250 Byte E (Gesamtkonsistenz) - ModuleIdentNumber:0x2E - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_250O" Value="250 Byte A (Gesamtkonsistenz) - ModuleIdentNumber:0x2F - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64I_Isochron" Value="64 bytes I (isochron, Gesamtkonsistenz) - ModuleIdentNumber:0x50 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_64O_Isochron" Value="64 bytes O (isochron, Gesamtkonsistenz) - ModuleIdentNumber:0x51 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1I_Isochron" Value="1 bytes I (isochron, Gesamtkonsistenz) - ModuleIdentNumber:0x52 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <Text TextId="TOK_InfoTextId_Module_1O_Isochron" Value="1 bytes O (isochron, Gesamtkonsistenz) - ModuleIdentNumber:0x53 - SubmoduleIdentNumber:0x0001 - API:0x00 - PROFIenergy" />
          <!--dataitem name-->
          <Text TextId="TOK_Input_DataItem_1" Value="Eingang 1 Byte" />
          <Text TextId="TOK_Output_DataItem_1" Value="Ausgang 1 Byte" />
          <Text TextId="TOK_Input_DataItem_64" Value="Eingang 64 Byte" />
          <Text TextId="TOK_Output_DataItem_64" Value="Ausgang 64 Byte" />
          <Text TextId="TOK_Input_DataItem_250" Value="Eingang 250 Byte" />
          <Text TextId="TOK_Output_DataItem_250" Value="Ausgang 250 Byte" />
          <Text TextId="TOK_Input_DataItem_64_Isochron" Value="Eingang 64 Byte IRT" />
          <Text TextId="TOK_Output_DataItem_64_Isochron" Value="Ausgang 64 Byte IRT" />
          <Text TextId="TOK_Input_DataItem_1_Isochron" Value="Eingang 1 Byte IRT" />
          <Text TextId="TOK_Output_DataItem_1_Isochron" Value="Ausgang 1 Byte IRT" />
          <Text TextId="TOK_InfoTextId_Module_MultiSub" Value="1 Byte E + 1 Byte E + 1 Byte A + 1 Byte A - ModuleIdentNumber:0x60" />
          <Text TextId="TOK_InfoTextId_MultiSub1I" Value="1 Byte E (Gesamtkonsistenz) - ModuleIdentNumber:0x60 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_MultiSub1O" Value="B byte A (Gesamtkonsistenz) - ModuleIdentNumber:0x60 - SubmoduleIdentNumber:0x0002 - API:0x00" />
          <!--errortype name-->
          <Text TextId="TOK_Name_ErrorType16" Value="Parametrierfehler" />
          <Text TextId="TOK_Name_ErrorType17" Value="Stromversorgungsfehler" />
          <Text TextId="TOK_Name_ErrorType18" Value="Sicherungsfall" />
          <Text TextId="TOK_Name_ErrorType20" Value="Massefehler" />
          <Text TextId="TOK_Name_ErrorType21" Value="Referenzpunkt verloren" />
          <Text TextId="TOK_Name_ErrorType22" Value="Prozessereignis verloren /Abtastfehler" />
          <Text TextId="TOK_Name_ErrorType23" Value="Schwellwert-Warnung" />
          <Text TextId="TOK_Name_ErrorType24" Value="Ausgang deaktiviert " />
          <Text TextId="TOK_Name_ErrorType25" Value="Ereignis mit Sicherheitsbezug" />
          <Text TextId="TOK_Name_ErrorType26" Value="externer Fehler" />
          <Text TextId="TOK_HelpName_ErrorType16" Value="Parametrierfehler" />
          <Text TextId="TOK_HelpName_ErrorType17" Value="Stromversorgungsfehler" />
          <Text TextId="TOK_HelpName_ErrorType18" Value="Sicherungsfall" />
          <Text TextId="TOK_HelpName_ErrorType20" Value="Massefehler" />
          <Text TextId="TOK_HelpName_ErrorType21" Value="Referenzpunkt verloren" />
          <Text TextId="TOK_HelpName_ErrorType22" Value="Prozessereignis verloren /Abtastfehler" />
          <Text TextId="TOK_HelpName_ErrorType23" Value="Schwellwert-Warnung" />
          <Text TextId="TOK_HelpName_ErrorType24" Value="Ausgang deaktiviert " />
          <Text TextId="TOK_HelpName_ErrorType25" Value="Ereignis mit Sicherheitsbezug" />
          <Text TextId="TOK_HelpName_ErrorType26" Value="externer Fehler" />
          <!--other text definitions-->
          <Text TextId="T_general_parameter" Value="Allgemeiner Parameter" />
        </Language>
      </ExternalTextList>
    </ApplicationProcess>
  </ProfileBody>
</ISO15745Profile>