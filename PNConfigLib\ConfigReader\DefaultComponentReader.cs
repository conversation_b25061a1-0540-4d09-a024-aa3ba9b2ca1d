/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DefaultComponentReader.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.IO;
using System.Linq;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency.Checker;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.Importer.GSDImport.Helper;
using PNConfigLib.PNProjectManager;

namespace PNConfigLib.ConfigReader
{
    class DefaultComponentReader
    {

        private readonly Configuration.Configuration m_Configuration;
        private readonly ListOfNodes.ListOfNodes m_ListOfNodes;

        public DefaultComponentReader(Configuration.Configuration cfg, ListOfNodes.ListOfNodes lon)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
        }

        public bool Read()
        {
            ReadDefaultPorts();
            return true;
        }

        private void ReadDefaultPorts()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_Configuration.Devices.DecentralDevice)
            {
                Dictionary<uint, PortCatalog> catalogLookup = new Dictionary<uint, PortCatalog>();
                ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);

                string gsdFileName = Path.GetFileName(lonDecentralDevice.GSDPath);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDecentralDevice.GSDRefID);

                Dictionary<int, PortCatalog> portsOfDeviceLookup =
                    PortConfigurator.FillPortCatalogValues(decentralDeviceCatalog, gsdFileName);

                PortConfigurator.FillSystemDefinedValuesToPort(portsOfDeviceLookup, decentralDeviceCatalog.Interface.PortList, catalogLookup);

                AddDefaultPorts(catalogLookup, xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports);
            }
        }

        private void AddDefaultPorts(Dictionary<uint, PortCatalog> catalogLookup, List<PortType> xmlPorts)
        {
            byte iPortNumberCursor = 0;
            foreach (uint slotNumber in catalogLookup.Keys)
            {
                ++iPortNumberCursor;
                // if not exist, add port; if it exist and subslot number is zero 
                if (xmlPorts.All(p => p.PortNumber != iPortNumberCursor))
                {
                    AddPorts(catalogLookup, xmlPorts, slotNumber, iPortNumberCursor);
                }
                else
                {
                    if (xmlPorts.Find(p => p.PortNumber == iPortNumberCursor).SubslotNumber == 0)
                    {
                        SetDefaultSlotNumber(xmlPorts, slotNumber, iPortNumberCursor);
                    }
                }
            }
        }

        private void AddPorts(Dictionary<uint, PortCatalog> catalogLookup, List<PortType> xmlPorts,
            uint slotNumber, byte iPortNumberCursor)
        {
            PortType xmlPort =
                        new PortType { SubslotNumber = (ushort)slotNumber, PortNumber = iPortNumberCursor };

            xmlPort.GSDRefID = catalogLookup[slotNumber].GsdId;
            xmlPorts.Add(xmlPort);
        }

        private void SetDefaultSlotNumber(List<PortType> xmlPorts, uint slotNumber, byte iPortNumberCursor)
        {
            xmlPorts.Find(p => p.PortNumber == iPortNumberCursor).SubslotNumber = (ushort)slotNumber;
        }

    }
}
