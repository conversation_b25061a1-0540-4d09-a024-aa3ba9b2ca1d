/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: GsdDevice.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The Device object is the root object of the complete data object
    /// model for the GSD(ML) data.
    /// </summary>
    public class GsdDevice :
        GsdObject,
        GSDI.IDevice
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Device if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public GsdDevice()
        {
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private DeviceInfo m_DeviceInfo;
        private ArrayList m_DaPs;
        private SortedList m_DAPQuickFinder;    // Only private!
        private ArrayList m_ChannelDiagnostics;
        private SortedList m_ChannelDiagnosticQuickFinder;	// Only private!
        private ArrayList m_ProfileChannelDiagnostics;
        private SortedList m_ProfileChannelDiagnosticQuickFinder;  // Only private!
        private ArrayList m_UnitDiagnosticTypes;
        private SortedList m_UnitDiagnosticTypeQuickFinder;	// Only private!
        private ArrayList m_SystemDefinedChannelDiagnostics;
        private SortedList m_SystemDefinedChannelDiagnosticQuickFinder;	// Only private!
        private ArrayList m_ChannelProcessAlarms;
        private SortedList m_ChannelProcessAlarmQuickFinder;
        private ArrayList m_SystemDefinedChannelProcessAlarms;
        private SortedList m_SystemDefinedChannelProcessAlarmQuickFinder;
        private ArrayList m_ProfileChannelProcessAlarms;
        private SortedList m_ProfileChannelProcessAlarmQuickFinder;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the DeviceInfo object, which contains general informations
        /// about the Device.
        /// </summary>
        public virtual DeviceInfo Info => this.m_DeviceInfo;

        /// <summary>
		/// Accesses the list of DeviceAccessPoints available with this Device.
		/// </summary>
		public virtual Array DeviceAccessPoints =>
            this.m_DaPs?.ToArray();

        /// <summary>
		/// Accesses the list of ChannelDiagnostic objects available with this Device.
		/// </summary>
		public virtual Array ChannelDiagnostics =>
            this.m_ChannelDiagnostics?.ToArray();

        /// <summary>
        /// Accesses the list of ProfileChannelDiagnostic objects available with this Device.
        /// </summary>
        public virtual Array ProfileChannelDiagnostics =>
            this.m_ProfileChannelDiagnostics?.ToArray();

        /// <summary>
		/// Accesses the list of UnitDiagnosticType objects available with this Device.
		/// </summary>
		public virtual Array UnitDiagnosticTypes =>
            this.m_UnitDiagnosticTypes?.ToArray();

        /// <summary>
        /// Accesses the list of SystemDefinedChannelDiagnostic objects available with this Device.
        /// </summary>
        public virtual Array SystemDefinedChannelDiagnostics =>
            this.m_SystemDefinedChannelDiagnostics?.ToArray();

        /// <summary>
        /// Accesses the list of ChannelProcessAlarm objects available with this Device.
        /// </summary>
        public virtual Array ChannelProcessAlarms =>
            this.m_ChannelProcessAlarms?.ToArray();

        /// <summary>
        /// Accesses the list of SystemDefinedChannelProcessAlarm objects available with this Device.
        /// </summary>
        public virtual Array SystemDefinedChannelProcessAlarms =>
            this.m_SystemDefinedChannelProcessAlarms?.ToArray();

        /// <summary>
        /// Accesses the list of ProfileChannelProcessAlarm objects available with this Device.
        /// </summary>
        public virtual Array ProfileChannelProcessAlarms =>
            this.m_ProfileChannelProcessAlarms?.ToArray();

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Returns the DeviceAccessPoint object, with the specified GsdID.
        /// </summary>
        /// <param name="bstrGsdID">Is the GsdID of the DeviceAccessPoint, which
        /// should be accessed.</param>
        public virtual DeviceAccessPoint GetDeviceAccessPoint(string bstrGsdID)
        {
            if (null != this.m_DAPQuickFinder)
            {
                if (this.m_DAPQuickFinder.ContainsKey(bstrGsdID))
                    return this.m_DAPQuickFinder[bstrGsdID] as DeviceAccessPoint;
            }

            // If no DeviceAccessPoint or this DeviceAccessPoint didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the ChannelDiagnostic object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// ChannelDiagnostic object, which should be accessed.</param>
        public virtual ChannelDiagnostic GetChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (null != this.m_ChannelDiagnosticQuickFinder)
            {
                if (this.m_ChannelDiagnosticQuickFinder.ContainsKey(vErrorType))
                    return this.m_ChannelDiagnosticQuickFinder[vErrorType] as ChannelDiagnostic;
            }

            // If no ChannelDiagnostic or this ChannelDiagnostic didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the ProfileChannelDiagnostic object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// ChannelDiagnostic object, which should be accessed.</param>
        public virtual ProfileChannelDiagnostic GetProfileChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (null != this.m_ProfileChannelDiagnosticQuickFinder)
            {
                if (this.m_ChannelDiagnosticQuickFinder.ContainsKey(vErrorType))
                    return this.m_ProfileChannelDiagnosticQuickFinder[vErrorType] as ProfileChannelDiagnostic;
            }

            // If no ProfileChannelDiagnostic or this ProfileChannelDiagnostic didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the UnitDiagnosticType object, with the specified USI.
        /// </summary>
        /// <param name="vUSI">Is the UserStructureIdentifier (USI) to identify the
        /// UnitDiagnosticType object, which should be accessed.</param>
        public virtual UnitDiagnosticType GetUnitDiagnosticType(System.UInt32 vUSI)
        {
            if (null != this.m_UnitDiagnosticTypeQuickFinder)
            {
                if (this.m_UnitDiagnosticTypeQuickFinder.ContainsKey(vUSI))
                    return this.m_UnitDiagnosticTypeQuickFinder[vUSI] as UnitDiagnosticType;
            }

            // If no UnitDiagnosticType or this UnitDiagnosticType didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the SystemDefinedChannelDiagnostic object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// ChannelDiagnostic object, which should be accessed.</param>
        public virtual SystemDefinedChannelDiagnostic GetSystemDefinedChannelDiagnostic(System.UInt32 vErrorType)
        {
            if (null != this.m_SystemDefinedChannelDiagnosticQuickFinder)
            {
                if (this.m_SystemDefinedChannelDiagnosticQuickFinder.ContainsKey(vErrorType))
                    return this.m_SystemDefinedChannelDiagnosticQuickFinder[vErrorType] as SystemDefinedChannelDiagnostic;
            }

            // If no SystemDefinedChannelDiagnostic or this SystemDefinedChannelDiagnostic didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the ChannelProcessAlarm object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// ChannelProcessAlarm object, which should be accessed.</param>
        public virtual ChannelProcessAlarm GetChannelProcessAlarm(System.UInt32 vReason)
        {
            if (null != this.m_ChannelProcessAlarmQuickFinder)
            {
                if (this.m_ChannelProcessAlarmQuickFinder.ContainsKey(vReason))
                    return this.m_ChannelProcessAlarmQuickFinder[vReason] as ChannelProcessAlarm;
            }

            // If no ChannelProcessAlarm or this ChannelProcessAlarm didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the SystemDefinedChannelProcessAlarm object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// SystemDefinedChannelProcessAlarm object, which should be accessed.</param>
        public virtual SystemDefinedChannelProcessAlarm GetSystemDefinedChannelProcessAlarm(System.UInt32 vReason)
        {
            if (null != this.m_SystemDefinedChannelProcessAlarmQuickFinder)
            {
                if (this.m_SystemDefinedChannelProcessAlarmQuickFinder.ContainsKey(vReason))
                    return this.m_SystemDefinedChannelProcessAlarmQuickFinder[vReason] as SystemDefinedChannelProcessAlarm;
            }

            // If no SystemDefinedChannelProcessAlarm or this SystemDefinedChannelProcessAlarm didn't exist, return nothing.
            return null;
        }

        /// <summary>
        /// Returns the ProfileChannelProcessAlarm object, with the specified ErrorType.
        /// </summary>
        /// <param name="vErrorType">Is the ErrorType to identify the
        /// ProfileChannelProcessAlarm object, which should be accessed.</param>
        public virtual ProfileChannelProcessAlarm GetProfileChannelProcessAlarm(System.UInt32 vReason)
        {
            if (null != this.m_ProfileChannelProcessAlarmQuickFinder)
            {
                if (this.m_ProfileChannelProcessAlarmQuickFinder.ContainsKey(vReason))
                    return this.m_ProfileChannelProcessAlarmQuickFinder[vReason] as ProfileChannelProcessAlarm;
            }

            // If no ProfileChannelProcessAlarm or this ProfileChannelProcessAlarm didn't exist, return nothing.
            return null;
        }


        #region COM Interface Members Only

        #endregion

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                FillFieldInfo(hash);
                FillFieldDeviceAccessPoints(hash);
                FillFieldChannelDiagnostics(hash);
                FillFieldProfileChannelDiagnostics(hash);
                FillFieldUnitDiagnosticTypes(hash);
                FillFieldSystemDefinedChannelDiagnostics(hash);
                FillFieldChannelProcessAlarms(hash);
                FillFieldSystemDefinedChannelProcessAlarms(hash);
                FillFieldProfileChannelProcessAlarms(hash);




                // Base data.
                //succeeded = base.Fill(hash);

                CreateDeviceAccessPointsHashTable();

                // For better handling of ChannelDiagnostic in relation to the ErrorType create private hashtable.
                CreateChannelDiagnosticHashTable();

                // For better handling of ProfileChannelDiagnostic in relation to the ErrorType create private hashtable.
                CreateProfileChannelDiagnosticHashTable();

                // For better handling of UnitDiagnosticType in relation to the UserStructureIdentifier create private hashtable.
                CreateUnitDiagnosticTypeHashTable();

                // For better handling of SystemDefinedChannelDiagnostic in relation to the ErrorType create private hashtable.
                CreateSystemDefinedChannelDiagnosticHashTable();

                // For better handling of ChannelProcessAlarm in relation to the Reason create private hash table.
                CreateChannelProcessAlarmHashTable();

                // For better handling of SystemDefinedChannelProcessAlarm in relation to the Reason create private hash table.
                CreateSystemDefinedChannelProcessAlarmHashTable();

                // For better handling of ProfileChannelProcessAlarm in relation to the Reason create private hash table.
                CreateProfileChannelProcessAlarmHashTable();
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        private void CreateProfileChannelProcessAlarmHashTable()
        {
            // For better handling of ProfileChannelProcessAlarm in relation to the Reason create private hash table.
            if (null == this.m_ProfileChannelProcessAlarms)
            {
                return;
            }

            foreach (ProfileChannelProcessAlarm o in this.m_ProfileChannelProcessAlarms)
            {
                if (this.m_ProfileChannelProcessAlarmQuickFinder == null)
                {
                    this.m_ProfileChannelProcessAlarmQuickFinder = new SortedList();
                }

                // Add object to quick finder hash table.
                if (!this.m_ProfileChannelProcessAlarmQuickFinder.Contains(o.Reason))
                    this.m_ProfileChannelProcessAlarmQuickFinder.Add(o.Reason, o);
            }

            if (null == this.m_ProfileChannelProcessAlarmQuickFinder)
            {
                return;
            }

            this.m_ProfileChannelProcessAlarmQuickFinder.TrimToSize();
            this.m_ProfileChannelProcessAlarms = new ArrayList(this.m_ProfileChannelProcessAlarmQuickFinder.Values);
        }

        private void CreateSystemDefinedChannelProcessAlarmHashTable()
        {
            // For better handling of SystemDefinedChannelProcessAlarm in relation to the Reason create private hash table.
            if (null == this.m_SystemDefinedChannelProcessAlarms)
            {
                return;
            }

            foreach (SystemDefinedChannelProcessAlarm o in this.m_SystemDefinedChannelProcessAlarms)
            {
                if (this.m_SystemDefinedChannelProcessAlarmQuickFinder == null)
                {
                    this.m_SystemDefinedChannelProcessAlarmQuickFinder = new SortedList();
                }

                // Add object to quick finder hash table.
                if (!this.m_SystemDefinedChannelProcessAlarmQuickFinder.Contains(o.Reason))
                    this.m_SystemDefinedChannelProcessAlarmQuickFinder.Add(o.Reason, o);
            }

            if (null == this.m_SystemDefinedChannelProcessAlarmQuickFinder)
            {
                return;
            }

            this.m_SystemDefinedChannelProcessAlarmQuickFinder.TrimToSize();
            this.m_SystemDefinedChannelProcessAlarms =
                new ArrayList(this.m_SystemDefinedChannelProcessAlarmQuickFinder.Values);
        }

        private void CreateChannelProcessAlarmHashTable()
        {
            // For better handling of ChannelProcessAlarm in relation to the Reason create private hash table.
            if (null == this.m_ChannelProcessAlarms)
            {
                return;
            }

            foreach (ChannelProcessAlarm o in this.m_ChannelProcessAlarms)
            {
                if (this.m_ChannelProcessAlarmQuickFinder == null)
                {
                    this.m_ChannelProcessAlarmQuickFinder = new SortedList();
                }

                // Add object to quick finder hash table.
                if (!this.m_ChannelProcessAlarmQuickFinder.Contains(o.Reason))
                    this.m_ChannelProcessAlarmQuickFinder.Add(o.Reason, o);
            }

            if (null == this.m_ChannelProcessAlarmQuickFinder)
            {
                return;
            }

            this.m_ChannelProcessAlarmQuickFinder.TrimToSize();
            this.m_ChannelProcessAlarms = new ArrayList(this.m_ChannelProcessAlarmQuickFinder.Values);
        }

        private void CreateSystemDefinedChannelDiagnosticHashTable()
        {
            // For better handling of SystemDefinedChannelDiagnostic in relation to the ErrorType create private hashtable.
            if (null == this.m_SystemDefinedChannelDiagnostics)
            {
                return;
            }

            foreach (SystemDefinedChannelDiagnostic o in this.m_SystemDefinedChannelDiagnostics)
            {
                if (this.m_SystemDefinedChannelDiagnosticQuickFinder == null)
                {
                    this.m_SystemDefinedChannelDiagnosticQuickFinder = new SortedList();
                }

                // Add object to quick finder hashtable.
                if (!this.m_SystemDefinedChannelDiagnosticQuickFinder.Contains(o.ErrorType))
                    this.m_SystemDefinedChannelDiagnosticQuickFinder.Add(o.ErrorType, o);
            }

            if (null == this.m_SystemDefinedChannelDiagnosticQuickFinder)
            {
                return;
            }

            this.m_SystemDefinedChannelDiagnosticQuickFinder.TrimToSize();
            this.m_SystemDefinedChannelDiagnostics =
                new ArrayList(this.m_SystemDefinedChannelDiagnosticQuickFinder.Values);
        }

        private void CreateUnitDiagnosticTypeHashTable()
        {
            // For better handling of UnitDiagnosticType in relation to the UserStructureIdentifier create private hashtable.
            if (null == this.m_UnitDiagnosticTypes)
            {
                return;
            }

            foreach (UnitDiagnosticType o in this.m_UnitDiagnosticTypes)
            {
                if (this.m_UnitDiagnosticTypeQuickFinder == null)
                {
                    this.m_UnitDiagnosticTypeQuickFinder = new SortedList();
                }

                // Add object to quick finder hashtable.
                if (!this.m_UnitDiagnosticTypeQuickFinder.Contains(o.UserStructureIdentifier))
                    this.m_UnitDiagnosticTypeQuickFinder.Add(o.UserStructureIdentifier, o);
            }

            if (null == this.m_UnitDiagnosticTypeQuickFinder)
            {
                return;
            }

            this.m_UnitDiagnosticTypeQuickFinder.TrimToSize();
            this.m_UnitDiagnosticTypes = new ArrayList(this.m_UnitDiagnosticTypeQuickFinder.Values);
        }

        private void CreateProfileChannelDiagnosticHashTable()
        {
            // For better handling of ProfileChannelDiagnostic in relation to the ErrorType create private hashtable.
            if (null == this.m_ProfileChannelDiagnostics)
            {
                return;
            }

            foreach (ProfileChannelDiagnostic o in this.m_ProfileChannelDiagnostics)
            {
                if (this.m_ProfileChannelDiagnosticQuickFinder == null)
                {
                    this.m_ProfileChannelDiagnosticQuickFinder = new SortedList();
                }

                // Add object to quick finder hashtable.
                if (!this.m_ProfileChannelDiagnosticQuickFinder.Contains(o.ErrorType))
                    this.m_ProfileChannelDiagnosticQuickFinder.Add(o.ErrorType, o);
            }

            if (null == this.m_ProfileChannelDiagnosticQuickFinder)
            {
                return;
            }

            this.m_ProfileChannelDiagnosticQuickFinder.TrimToSize();
            this.m_ProfileChannelDiagnostics = new ArrayList(this.m_ProfileChannelDiagnosticQuickFinder.Values);
        }

        private void CreateChannelDiagnosticHashTable()
        {
            // For better handling of ChannelDiagnostic in relation to the ErrorType create private hashtable.
            if (null == this.m_ChannelDiagnostics)
            {
                return;
            }

            foreach (ChannelDiagnostic o in this.m_ChannelDiagnostics)
            {
                if (this.m_ChannelDiagnosticQuickFinder == null)
                {
                    this.m_ChannelDiagnosticQuickFinder = new SortedList();
                }

                // Add object to quick finder hashtable.
                if (!this.m_ChannelDiagnosticQuickFinder.Contains(o.ErrorType))
                    this.m_ChannelDiagnosticQuickFinder.Add(o.ErrorType, o);
            }

            if (null == this.m_ChannelDiagnosticQuickFinder)
            {
                return;
            }

            this.m_ChannelDiagnosticQuickFinder.TrimToSize();
            this.m_ChannelDiagnostics = new ArrayList(this.m_ChannelDiagnosticQuickFinder.Values);
        }

        private void CreateDeviceAccessPointsHashTable()
        {
            // For better handling of DeviceAccessPoints in relation to the GsdID create private hash table.
            if (null == this.m_DaPs)
            {
                return;
            }

            foreach (DeviceAccessPoint o in this.m_DaPs)
            {
                if (this.m_DAPQuickFinder == null)
                {
                    this.m_DAPQuickFinder = new SortedList();
                }

                // Add object to quick finder hashtable.
                if (!this.m_DAPQuickFinder.Contains(o.GsdID))
                    this.m_DAPQuickFinder.Add(o.GsdID, o);
            }

            if (null == this.m_DAPQuickFinder)
            {
                return;
            }

            this.m_DAPQuickFinder.TrimToSize();
            this.m_DaPs = new ArrayList(this.m_DAPQuickFinder.Values);
        }
        
        private void FillFieldProfileChannelProcessAlarms(Hashtable hash)
        {
            string member = Models.s_FieldProfileChannelProcessAlarms;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_ProfileChannelProcessAlarms = hash[member] as ArrayList;
        }

        private void FillFieldSystemDefinedChannelProcessAlarms(Hashtable hash)
        {
            string member = Models.s_FieldSystemDefinedChannelProcessAlarms;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_SystemDefinedChannelProcessAlarms = hash[member] as ArrayList;
        }

        private void FillFieldChannelProcessAlarms(Hashtable hash)
        {
            string member = Models.s_FieldChannelProcessAlarms;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_ChannelProcessAlarms = hash[member] as ArrayList;
        }

        private void FillFieldSystemDefinedChannelDiagnostics(Hashtable hash)
        {
            string member = Models.s_FieldSystemDefinedChannelDiagnostics;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_SystemDefinedChannelDiagnostics = hash[member] as ArrayList;
        }

        private void FillFieldUnitDiagnosticTypes(Hashtable hash)
        {
            string member = Models.s_FieldUnitDiagnosticTypes;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_UnitDiagnosticTypes = hash[member] as ArrayList;
        }

        private void FillFieldProfileChannelDiagnostics(Hashtable hash)
        {
            string member = Models.s_FieldProfileChannelDiagnostics;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_ProfileChannelDiagnostics = hash[member] as ArrayList;
        }

        private void FillFieldChannelDiagnostics(Hashtable hash)
        {
            string member = Models.s_FieldChannelDiagnostics;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_ChannelDiagnostics = hash[member] as ArrayList;
        }

        private void FillFieldDeviceAccessPoints(Hashtable hash)
        {
            string member = Models.s_FieldDeviceAccessPoints;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_DaPs = hash[member] as ArrayList;
        }

        private void FillFieldInfo(Hashtable hash)
        {
            string member = Models.s_FieldInfo;
            if (hash.ContainsKey(member)
                && hash[member] is DeviceInfo)
                this.m_DeviceInfo = hash[member] as DeviceInfo;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectDevice);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInfo, m_DeviceInfo);
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldDeviceAccessPoints, m_DaPs);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldChannelDiagnostics, m_ChannelDiagnostics);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldProfileChannelDiagnostics, m_ProfileChannelDiagnostics);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldUnitDiagnosticTypes, m_UnitDiagnosticTypes);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSystemDefinedChannelDiagnostics, m_SystemDefinedChannelDiagnostics);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldChannelProcessAlarms, m_ChannelProcessAlarms);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSystemDefinedChannelProcessAlarms, m_SystemDefinedChannelProcessAlarms);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldProfileChannelProcessAlarms, m_ProfileChannelProcessAlarms);

            return true;
        }

        #endregion

    }
}