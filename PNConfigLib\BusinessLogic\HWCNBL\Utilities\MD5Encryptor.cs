/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MD5Encryptor.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// This class provides methods for calculating MD5 hashes.
    /// </summary>
    internal static class MD5Encryptor
    {
        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        ///   The method creates an encrypted form of a given string with the help of the MD5 algorithm.
        /// </summary>
        /// <param name="strToEncrypt">The string to be encrypted</param>
        /// <param name="encryptedStr">Encrypted string as byte array</param>
        /// <returns>True if the encryption is successful.</returns>
        public static bool Encrypt(string strToEncrypt, out byte[] encryptedStr)
        {
            if (strToEncrypt == null)
            {
                throw new ArgumentNullException(nameof(strToEncrypt));
            }
            byte[] byteArr = Encoding.ASCII.GetBytes(strToEncrypt);
            return Encrypt(byteArr, out encryptedStr);
        }

        /// <summary>
        ///   The method creates an encrypted form of a given byte array with the help of the MD5 algorithm.
        /// </summary>
        /// <param name="byteArrToEncrypt">The string to be encrypted</param>
        /// <param name="encryptedStr">Encrypted string as byte array</param>
        /// <returns>True if the encryption is successful.</returns>
        public static bool Encrypt(byte[] byteArrToEncrypt, out byte[] encryptedStr)
        {
            if (byteArrToEncrypt == null)
            {
                throw new ArgumentNullException(nameof(byteArrToEncrypt));
            }
            using (MD5 md5Hash = MD5.Create())
            {
                encryptedStr = md5Hash.ComputeHash(byteArrToEncrypt);
            }
            return true;
        }

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        #endregion
    }
}