﻿<?xml version="1.0" encoding="utf-8"?>
<ISO15745Profile xmlns="http://www.profibus.com/GSDML/2003/11/DeviceProfile" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.profibus.com/GSDML/2003/11/DeviceProfile ..\xsd\GSDML-DeviceProfile-V2.45.xsd">
  <ProfileHeader>
    <ProfileIdentification>PROFINET Device Profile</ProfileIdentification>
    <ProfileRevision>1.00</ProfileRevision>
    <ProfileName>Device Profile for PROFINET Devices</ProfileName>
    <ProfileSource>PROFIBUS Nutzerorganisation e. V. (PNO)</ProfileSource>
    <ProfileClassID>Device</ProfileClassID>
    <ISO15745Reference>
      <ISO15745Part>4</ISO15745Part>
      <ISO15745Edition>1</ISO15745Edition>
      <ProfileTechnology>GSDML</ProfileTechnology>
    </ISO15745Reference>
  </ProfileHeader>
  <ProfileBody>
    <DeviceIdentity VendorID="0x002A" DeviceID="0x0009">
      <InfoText TextId="IDT_FamilyDescription" />
      <VendorName Value="SIEMENS" />
    </DeviceIdentity>
    <DeviceFunction>
      <Family MainFamily="I/O" ProductFamily="DEVKIT" />
    </DeviceFunction>
    <ApplicationProcess>
      <DeviceAccessPointList>
        <DeviceAccessPointItem ID="DAP_1" PhysicalSlots="0..8" ModuleIdentNumber="0x00000001" MinDeviceInterval="32" DNS_CompatibleName="PND-IOD-Linux-Native" ObjectUUID_LocalIndex="1" FixedInSlots="0" MultipleWriteSupported="true" NameOfStationNotTransferable="true" DeviceAccessSupported="true" NumberOfDeviceAccessAR="1" ImplementationType="software" LLDP_NoD_Supported="true" PNIO_Version="V2.45" MaxSupportedRecordSize="32768" CheckDeviceID_Allowed="true" IOXS_Required="false" ResetToFactoryModes="2" AddressAssignment="LOCAL;DCP" SharedDeviceSupported="false">
          <ModuleInfo CategoryRef="ID_PND_LinuxNative">
            <Name TextId="TOK_TextId_Module_Name_Linux_Native" />
            <InfoText TextId="TOK_InfoTextId_Info_Linux_Native" />
            <VendorName Value="SIEMENS" />
            <OrderNumber Value="6ES7195-3AA00-0YA1" />
          </ModuleInfo>
          <CertificationInfo ConformanceClass="B" ApplicationClass="" NetloadClass="II" SecurityClass="1" />
          <IOConfigData MaxInputLength="1440" MaxOutputLength="1440" />
          <UseableModules>
            <ModuleItemRef ModuleItemTarget="ID_Mod_01" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_02" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_03" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_04" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_05" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_06" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_07" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_08" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_09" AllowedInSlots="1..8" />
            <ModuleItemRef ModuleItemTarget="ID_Mod_60" AllowedInSlots="1..8" />
          </UseableModules>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="IDS_D01" IM5_Supported="true" SubmoduleIdentNumber="0x00000001" MayIssueProcessAlarm="true" Writeable_IM_Records="1 2 3 4">
              <IOData />
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_Name_Linux_Native" />
                <InfoText TextId="TOK_InfoTextId_Info_Linux_Native" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
          <SystemDefinedSubmoduleList>
            <InterfaceSubmoduleItem ID="IDS_1I" SubslotNumber="32768" SubmoduleIdentNumber="0x00000010" TextId="TOK_TextId_Interface_Name_Linux_Native" DelayMeasurementSupported="false" SupportedProtocols="SNMP;LLDP" NetworkComponentDiagnosisSupported="false" DCP_BoundarySupported="true" PTP_BoundarySupported="true" DCP_FeaturesSupported="RejectDCPSet" SNMP_FeaturesSupported="SNMPAdjust" SupportedRT_Classes="RT_CLASS_1" PDEV_CombinedObjectSupported="true" Writeable_IM_Records="1 2 3 4" SupportedServiceProtocols="CLRPC" IM5_Supported="true">
              <ApplicationRelations NumberOfAR="1" NumberOfAdditionalInputCR="0" NumberOfAdditionalMulticastProviderCR="0" NumberOfAdditionalOutputCR="0" NumberOfMulticastConsumerCR="0" StartupMode="Advanced;Legacy" PullModuleAlarmSupported="false">
                <TimingProperties SendClock="32" ReductionRatio="1 2 4 8 16 32 64 128 256 512" />
              </ApplicationRelations>
              <MediaRedundancy MaxMRP_Instances="1" SupportedRole="Client;Manager;Manager (Auto)" AdditionalProtocolsSupported="true" MRPD_Supported="false"/>
            </InterfaceSubmoduleItem>
            <PortSubmoduleItem ID="IDS_1P1" TextId="TOK_Port1" SubslotNumber="32769" SubmoduleIdentNumber="0x00000011" IsDefaultRingport="true" CheckMAUTypeDifferenceSupported="true" PortDeactivationSupported="false" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="false" CheckMAUTypeSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="false" />
                <MAUTypeItem Value="30" AdjustSupported="false" />
              </MAUTypeList>
            </PortSubmoduleItem>
            <PortSubmoduleItem ID="IDS_1P2" TextId="TOK_Port2" SubslotNumber="32770" SubmoduleIdentNumber="0x00000011" IsDefaultRingport="true" CheckMAUTypeDifferenceSupported="true" PortDeactivationSupported="false" LinkStateDiagnosisCapability="Up+Down" ShortPreamble100MBitSupported="false" CheckMAUTypeSupported="true" Writeable_IM_Records="1 2 3 4" IM5_Supported="true">
              <MAUTypeList>
                <MAUTypeItem Value="16" AdjustSupported="false" />
                <MAUTypeItem Value="30" AdjustSupported="false" />
              </MAUTypeList>
            </PortSubmoduleItem>
          </SystemDefinedSubmoduleList>
          <Graphics>
            <GraphicItemRef Type="DeviceSymbol" GraphicItemTarget="ID_Graph_PNDriverDEVKIT" />
          </Graphics>
          <AssetManagement />
        </DeviceAccessPointItem>
      </DeviceAccessPointList>
      <!-- ============================================ -->
      <!--            List of modules                   -->
      <!-- ============================================ -->
      <ModuleList>
        <!-- ================================== -->
        <!--  1 byte digital input/output     -->
        <!--   (param-rec)                      -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_01" ModuleIdentNumber="0x00000020">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1IO" />
            <InfoText TextId="TOK_InfoTextId_Module_1IO" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="1" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x20,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1IO" />
                <InfoText TextId="TOK_InfoTextId_Module_1IO" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--       1 byte digital input       -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_02" ModuleIdentNumber="0x00000021">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1I" />
            <InfoText TextId="TOK_InfoTextId_Module_1I" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="2" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x21,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1I" />
                <InfoText TextId="TOK_InfoTextId_Module_1I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--     1 byte digital output        -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_03" ModuleIdentNumber="0x00000022">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_1O" />
            <InfoText TextId="TOK_InfoTextId_Module_1O" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="3" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x22,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_1O" />
                <InfoText TextId="TOK_InfoTextId_Module_1O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!-- 64 byte digital input/output    -->
        <!--   (param-rec) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_04" ModuleIdentNumber="0x00000030">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64IO" />
            <InfoText TextId="TOK_InfoTextId_Module_64IO" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="10" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_64" Length="64" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_64" Length="64" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x30,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64IO" />
                <InfoText TextId="TOK_InfoTextId_Module_64IO" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      64 byte digital input      -->
        <!--   (param-rec) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_05" ModuleIdentNumber="0x00000031">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64I" />
            <InfoText TextId="TOK_InfoTextId_Module_64I" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="11" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_64" Length="64" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x31,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64I" />
                <InfoText TextId="TOK_InfoTextId_Module_64I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--    64 byte digital output       -->
        <!--   (param-rec) -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_06" ModuleIdentNumber="0x00000032">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_64O" />
            <InfoText TextId="TOK_InfoTextId_Module_64O" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="12" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_64" Length="64" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x32,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_64O" />
                <InfoText TextId="TOK_InfoTextId_Module_64O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital input/output -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_07" ModuleIdentNumber="0x00000040">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250IO" />
            <InfoText TextId="TOK_InfoTextId_Module_250IO" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="16" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_250" Length="250" UseAsBits="true" />
                </Input>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_250" Length="250" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x40,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250IO" />
                <InfoText TextId="TOK_InfoTextId_Module_250IO" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital input        -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_08" ModuleIdentNumber="0x00000041">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250I" />
            <InfoText TextId="TOK_InfoTextId_Module_250I" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="17" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_250" Length="250" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x41,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250I" />
                <InfoText TextId="TOK_InfoTextId_Module_250I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ================================== -->
        <!--      250 byte digital output       -->
        <!-- ================================== -->
        <ModuleItem ID="ID_Mod_09" ModuleIdentNumber="0x00000042">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_250O" />
            <InfoText TextId="TOK_InfoTextId_Module_250O" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="18" SubmoduleIdentNumber="0x0001" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_250" Length="250" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x42,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_Module_250O" />
                <InfoText TextId="TOK_InfoTextId_Module_250O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
        <!-- ========================================================= -->
        <!--  Multi API input module   ModuleID 0x60                   -->
        <!-- ========================================================= -->
        <ModuleItem ID="ID_Mod_60" ModuleIdentNumber="0x00000060">
          <ModuleInfo>
            <Name TextId="TOK_TextId_Module_MultiSub" />
            <InfoText TextId="TOK_InfoTextId_Module_MultiSub" />
          </ModuleInfo>
          <VirtualSubmoduleList>
            <VirtualSubmoduleItem ID="601" SubmoduleIdentNumber="0x0001" FixedInSubslots="1" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x61,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1I" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="602" SubmoduleIdentNumber="0x0001" FixedInSubslots="2" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="true">
              <IOData>
                <Input Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Input_DataItem_1" Length="1" UseAsBits="true" />
                </Input>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x62,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1I" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1I" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="603" SubmoduleIdentNumber="0x0002" FixedInSubslots="3" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x63,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1O" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
            <VirtualSubmoduleItem ID="604" SubmoduleIdentNumber="0x0002" FixedInSubslots="4" Writeable_IM_Records="1 2 3 4" MayIssueProcessAlarm="false">
              <IOData>
                <Output Consistency="All items consistency">
                  <DataItem DataType="OctetString" TextId="TOK_Output_DataItem_1" Length="1" UseAsBits="true" />
                </Output>
              </IOData>
              <RecordDataList>
                <ParameterRecordDataItem Index="1" Length="4" TransferSequence="0">
                  <Name TextId="T_general_parameter" />
                  <Const Data="0x64,0x01,0x56,0x78" ByteOffset="0" />
                </ParameterRecordDataItem>
              </RecordDataList>
              <ModuleInfo>
                <Name TextId="TOK_TextId_MultiSub1O" />
                <InfoText TextId="TOK_InfoTextId_MultiSub1O" />
              </ModuleInfo>
            </VirtualSubmoduleItem>
          </VirtualSubmoduleList>
        </ModuleItem>
      </ModuleList>
      <GraphicsList>
        <GraphicItem ID="ID_Graph_PNDriverDEVKIT" GraphicFile="GSDML-002A-0009-PNDriver-IOD" />
      </GraphicsList>
      <CategoryList>
        <CategoryItem ID="ID_PND_LinuxNative" TextId="TOK_ID_PL" />
      </CategoryList>
      <ExternalTextList>
        <PrimaryLanguage>
          <!--Linux Native & Dap Info-->
          <Text TextId="TOK_TextId_Module_Name_Linux_Native" Value="IOD-Linux Native" />
          <Text TextId="TOK_TextId_Interface_Name_Linux_Native" Value="Interface" />
          <Text TextId="IDT_FamilyDescription" Value="PROFINET Driver IOD Linux Native" />
          <Text TextId="TOK_ID_PL" Value="PROFINET Driver" />
          <Text TextId="TOK_InfoTextId_Info_Linux_Native" Value="PROFINET Driver Linux Native Development Kit, EDDS, AFPacket, update time 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 ms supported" />
          <!--port name-->
          <Text TextId="TOK_Text_Id_Port_RJ45_Desc" Value="Port - RJ-45" />
          <Text TextId="TOK_Port1" Value="Port 1" />
          <Text TextId="TOK_Port2" Value="Port 2" />
          <!--module name-->
          <Text TextId="TOK_TextId_Module_1IO" Value="  1 byte IO" />
          <Text TextId="TOK_TextId_Module_1I" Value="  1 byte I" />
          <Text TextId="TOK_TextId_Module_1O" Value="  1 byte O" />
          <Text TextId="TOK_TextId_Module_64IO" Value="  64 bytes IO" />
          <Text TextId="TOK_TextId_Module_64I" Value="  64 bytes I" />
          <Text TextId="TOK_TextId_Module_64O" Value="  64 bytes O" />
          <Text TextId="TOK_TextId_Module_250IO" Value=" 250 bytes IO" />
          <Text TextId="TOK_TextId_Module_250I" Value=" 250 bytes I" />
          <Text TextId="TOK_TextId_Module_250O" Value=" 250 bytes O" />
          <Text TextId="TOK_TextId_Module_MultiSub" Value="Multi subslots 1I 1I 1O 1O" />
          <Text TextId="TOK_TextId_MultiSub1I" Value="1 byte  I" />
          <Text TextId="TOK_TextId_MultiSub1O" Value="1 byte  O" />
          <!--module info name-->
          <Text TextId="TOK_InfoTextId_Module_1IO" Value="1 byte IO (overall consistency) - ModuleIdentNumber:0x20 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_1I" Value="1 byte I (overall consistency) - ModuleIdentNumber:0x21 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_1O" Value="1 byte O (overall consistency) - ModuleIdentNumber:0x22 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_64IO" Value="64 bytes IO (overall consistency) - ModuleIdentNumber:0x30 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_64I" Value="64 bytes I (overall consistency) - ModuleIdentNumber:0x31 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_64O" Value="64 bytes O (overall consistency) - ModuleIdentNumber:0x32 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_250IO" Value="250 bytes IO (overall consistency) - ModuleIdentNumber:0x40 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_250I" Value="250 bytes I (overall consistency) - ModuleIdentNumber:0x41 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_250O" Value="250 bytes O (overall consistency) - ModuleIdentNumber:0x42 - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_Module_MultiSub" Value="1 byte I + 1 byte I + 1 byte O + 1 byte O - ModuleIdentNumber:0x60" />
          <Text TextId="TOK_InfoTextId_MultiSub1I" Value="1 byte I (overall consistency) - SubmoduleIdentNumber:0x0001 - API:0x00" />
          <Text TextId="TOK_InfoTextId_MultiSub1O" Value="1 byte O (overall consistency) - SubmoduleIdentNumber:0x0002 - API:0x00" />
          <!--dataitem name-->
          <Text TextId="TOK_Input_DataItem_1" Value="Input 1 byte" />
          <Text TextId="TOK_Output_DataItem_1" Value="Output 1 byte" />
          <Text TextId="TOK_Input_DataItem_64" Value="Input64 bytes" />
          <Text TextId="TOK_Output_DataItem_64" Value="Output64 bytes" />
          <Text TextId="TOK_Input_DataItem_250" Value="Input 250 bytes" />
          <Text TextId="TOK_Output_DataItem_250" Value="Output 250 bytes" />
          <!--other text definitions-->
          <Text TextId="T_general_parameter" Value="general parameter" />
        </PrimaryLanguage>
      </ExternalTextList>
    </ApplicationProcess>
  </ProfileBody>
</ISO15745Profile>