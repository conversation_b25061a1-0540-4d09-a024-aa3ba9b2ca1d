/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNMrpPortBL.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Diagnostics;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.MultipleMrp;
using PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.Port
{
    internal class PNMrpPortBL : PortDecorator
    {
        public PNMrpPortBL(IPortBL decoratedPort) : base(decoratedPort)
        {
            InitBL();
        }
        public override void InitBL()
        {
            InitActions();
        }

        private void InitActions()
        {
            Port.BaseActions.RegisterMethod(GetPortMrpParameterBlocks.Name, GenericMethodGetPortMrpParameterBlocks);
            ConsistencyManager.RegisterConsistencyCheck(Port, MethodConsistencyCheck);
        }

        /// <summary>
        /// Function creates the Mrp Port Parameterblock with dataset 
        /// 0x8053
        /// </summary>
        /// <param name="methodData">The Method Data</param>
        private void GenericMethodGetPortMrpParameterBlocks(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            bool isRecordwithoutHeader = false;
            for (int i = 0; i < methodData.Arguments.Count; i++)
            {
                if (methodData.Arguments.AllKeys[i] == RecordwithoutHeader.isRecordwithoutHeader)
                    isRecordwithoutHeader = true;
            }

            try
            {
                // init return value
                methodData.ReturnValue = false;
                if (isRecordwithoutHeader)
                    methodData.ReturnValue = null;

                // Return if interface submodule doesn't support mrp or its current role is 
                // not in ring.
                Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(Port);
                AttributeAccessCode ac = new AttributeAccessCode();
                if (!interfaceSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(
                        InternalAttributeNames.PnMrpSupported, ac, false))
                {
                    return;
                }
                if ((interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                         InternalAttributeNames.PnMrpRole, ac.GetNew(), (int)PNMrpRole.NotInRing) ==
                     (int)PNMrpRole.NotInRing) && !MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule))
                {
                    return;
                }

                bool returnVal;
                byte[] block = ConfigUtility.GetPDPortMrpDataAdjust(Port, out returnVal);
                methodData.ReturnValue = returnVal;

                if (returnVal)
                {
                    if (isRecordwithoutHeader)
                    {
                        // assign the output parameter of the method
                        methodData.ReturnValue = block;
                        return;
                    }

                    ParameterDatasetStruct paramDsPortMrpDataAdjust =
                        new ParameterDatasetStruct();
                    paramDsPortMrpDataAdjust.ParaDSNumber = 0x8053;
                    paramDsPortMrpDataAdjust.ParaDSIdentifier = 0;
                    paramDsPortMrpDataAdjust.AddParaBlock(block);

                    methodData.Arguments[GetPortMrpParameterBlocks.PDPortMrpDataBlockEntry] =
                        paramDsPortMrpDataAdjust.ToByteArray;
                }
            }
            catch (Exception e)
            {
                throw new PNFunctionsException(nameof(GenericMethodGetPortMrpParameterBlocks), e);
            }
        }

        #region Consistency Checks

        private void MethodConsistencyCheck()
        {
            Interface interfaceSubmodule = Port.GetInterface();
            // Check if the port is part of the ring
            if (interfaceSubmodule == null)
            {
                return;
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            if (!interfaceSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnMrpSupported, ac, false))
            {
                return;
            }
            if (interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnMrpRole, ac.GetNew(), (int)PNMrpRole.NotInRing) ==
                (int)PNMrpRole.NotInRing)
            {
                return;
            }

            IMethodData methodData = new MethodData();
            methodData.Name = GetSelectedRingPorts.Name;
            interfaceSubmodule.BaseActions.CallMethod(methodData);
            List<DataModel.PCLObjects.Port> selectedRingPorts = (List<DataModel.PCLObjects.Port>)
                methodData.Arguments[GetSelectedRingPorts.SelectedRingPorts];
            if (selectedRingPorts.Count == 0)
            {
                return;
            }


            if (!selectedRingPorts.Contains(Port))
            {
                // Check only if the port really should be selected.
                CheckRingPortConfig();
                // No need to do the other tests, because the port is not selected.
                return;
            }

            IEnumerable<DataModel.PCLObjects.Port> portToPorts = Port.GetPartnerPorts();
            CheckSubnetBoundary(portToPorts);
            CheckNonRingPorts(portToPorts);
            CheckTransferRate();
            CheckTopologyDiscovery();
            CheckRingPortSetting();
            ProgrammablePeerConsistencyChecker.CheckProgrammablePeerDeviceMrpRole(Port);
        }

        /// <summary>
        /// This method checks if the port interconnection of mrp gets out of the 
        /// subnet boundaries. This is not allowed. (MRP-K7)
        /// </summary>
        /// <param name="partnerPorts"></param>
        /// <remarks>The same check exists for Profinet-IO Ports, so this check is only done
        /// for the PNBasic Ports.</remarks>
        private void CheckSubnetBoundary(IEnumerable<DataModel.PCLObjects.Port> partnerPorts)
        {
            if (partnerPorts == null) // It should be io or irt port, don't check it again.
            {
                return;
            }

            Interface interfaceSubmodule = Port.GetInterface();
            // Get my subnet and compare with the subnets of the partner ports
            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                Interface partnerInterface = partnerPort.GetInterface();
                if (partnerInterface != null ? interfaceSubmodule.Node.Subnet.Id !=
                                               partnerInterface.Node.Subnet.Id : true)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortInterconnDifferentSubnet,
                            Utility.GetNameWithContainer(Port), Utility.GetNameWithContainer(partnerPort));
                }
            }
        }

        /// <summary>
        /// This method checks if the mrp ringport is interconnected with a 
        /// a. Nonring port of an active mrp interface. 
        /// b. Port of a device which is not mrp-capable.
        /// These are not allowed.(MRP-K8)
        /// </summary>
        /// <param name="partnerPorts"></param>
        private void CheckNonRingPorts(IEnumerable<DataModel.PCLObjects.Port> partnerPorts)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool isMultipleConnectionEnabled = Port.AttributeAccess
                .GetAnyAttribute<bool>(InternalAttributeNames.PnIsMultipleConnectionEnabled, ac, false);
            if (isMultipleConnectionEnabled || (partnerPorts == null))
            {
                return;
            }
            Interface interfaceSubmodule = Port.GetInterface();
            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                Interface partnerInterface = partnerPort.GetInterface();
                if ((partnerInterface != null)
                    && (partnerInterface.Node != null)
                    && (partnerInterface.Node.Subnet != null)
                    && (interfaceSubmodule != null)
                    && (interfaceSubmodule.Node != null)
                    && (interfaceSubmodule.Node.Subnet != null)
                    && (partnerInterface.Node.Subnet.Id == interfaceSubmodule.Node.Subnet.Id))
                {
                    // we are in the same subnet, if no, an other cons.check will be coming
                    if (!partnerInterface.AttributeAccess.GetAnyAttribute<Boolean>(
                            InternalAttributeNames.PnMrpSupported, ac, false))
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortInterconnNonRingPort,
                                Utility.GetNameWithContainer(Port), Utility.GetNameWithContainer(partnerPort));
                        return;
                    }

                    PNMrpRole partnerInterfaceRole;

                    if (MultipleMrpUtilities.IsMultipleInstancesActive(partnerInterface))
                    {
                        int partnerPortInstanceNumber = MultipleMrpUtilities.GetInstanceOfPort(partnerPort);

                        partnerInterfaceRole = partnerPortInstanceNumber > 0 ? MultipleMrpUtilities.GetMultipleRole(
                                                   partnerInterface, partnerPortInstanceNumber) : PNMrpRole.NotInRing;
                    }
                    else
                    {
                        partnerInterfaceRole = (PNMrpRole)partnerInterface.AttributeAccess.
                            GetAnyAttribute<UInt32>(InternalAttributeNames.PnMrpRole, ac.GetNew(),
                                (UInt32)PNMrpRole.NotInRing);
                    }

                    // Partner interface supports mrp, but not part of the ring
                    if (partnerInterfaceRole == PNMrpRole.NotInRing)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortInterconnNonRingPort,
                                Utility.GetNameWithContainer(Port), Utility.GetNameWithContainer(partnerPort));
                        return;
                    }

                    bool partnerPortSupportsMrp = partnerPort.AttributeAccess.GetAnyAttribute<bool>(
                                                      InternalAttributeNames.PnMrpIsDefaultRingPort, ac, false)
                                                    | partnerPort.AttributeAccess.GetAnyAttribute<bool>(
                                                      InternalAttributeNames.PnMrpSupportsRingConfig, ac, false);

                    IMethodData methodData = new MethodData();
                    methodData.Name = GetSelectedRingPorts.Name;
                    partnerInterface.BaseActions.CallMethod(methodData);
                    List<DataModel.PCLObjects.Port> partnerSelectedRingPorts = (List<DataModel.PCLObjects.Port>)
                        methodData.Arguments[GetSelectedRingPorts.SelectedRingPorts];

                    // Partner interface submodule does not have any selected ring ports (that shouldn't normally happen)
                    if ((partnerSelectedRingPorts == null) || (partnerSelectedRingPorts.Count == 0))
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortInterconnNonRingPort,
                                Utility.GetNameWithContainer(Port), Utility.GetNameWithContainer(partnerPort));
                        return;
                    }

                    //This check is already handled for multiple MRP devices
                    bool partnerPortIsInRing = MultipleMrpUtilities.IsMultipleInstancesActive(partnerInterface) ||
                                               (partnerSelectedRingPorts.Contains(partnerPort));
                    // Partner port either does not support mrp or it is not selected as a ring port
                    if (!partnerPortSupportsMrp || !partnerPortIsInRing)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortInterconnNonRingPort,
                                Utility.GetNameWithContainer(Port), Utility.GetNameWithContainer(partnerPort));
                        return;
                    }
                }
            }
        }

        /// <summary>
        /// This method checks whether ports have greater than or equal to 100MBit FullDuplex
        /// transfer rate (or Automatic setting is selected). (MRP-K5) 
        /// </summary>
        private void CheckTransferRate()
        {
            UInt32 portMediumDuplex = Port.AttributeAccess.
                GetAnyAttribute<uint>(InternalAttributeNames.PnEthernetMediumDuplex, new AttributeAccessCode(), 0);
            bool isDefinedTransferRate = Enum.IsDefined(
                typeof(PNEthernetMediumDuplex), portMediumDuplex);
            if (isDefinedTransferRate)
            {
                PNEthernetMediumDuplex transferRate = (PNEthernetMediumDuplex)portMediumDuplex;
                if ((transferRate != PNEthernetMediumDuplex.Automatic) &&
                    (transferRate != PNEthernetMediumDuplex.Fullduplex100Mbit))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortTransferRateNotAllowed,
                            Utility.GetNameWithContainer(Port));
                }
            }
            // Probably higher transfer rates are introduced, produce an assertion nevertheless
            Debug.Assert(
                !(!Port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, new AttributeAccessCode(), false) 
                && !isDefinedTransferRate), "Transfer Rate is not defined for the port");
        }

        /// <summary>
        /// This method checks if the "End of topology discovery".
        /// This is not allowed for MRP managers.
        /// </summary>
        private void CheckTopologyDiscovery()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            Interface interfaceSubmodule = Port.GetInterface();
            PNMrpRole mrpRole = (PNMrpRole)interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole, ac, (int)PNMrpRole.NotInRing);
            if ((mrpRole == PNMrpRole.NotInRing) || (mrpRole == PNMrpRole.Client))
            {
                return;
            }

            if (interfaceSubmodule.AttributeAccess
                .GetAnyAttribute<Boolean>(InternalAttributeNames.PnPTPBoundarySupported, ac.GetNew(), false)
                && (Port.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPTPBoundary, ac.GetNew(), 0) == 1))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortEndOfTopologyDiscovery,
                        Utility.GetNameWithContainer(Port));
            }
        }

        /// <summary>
        /// This method checks durring generation whether a disabled port is used in a Mrp
        /// </summary>
        private void CheckRingPortSetting()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            //Checking whether the current port is deactivated or not (if not then skipping it)
            if (!Port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, ac, false))
            {
                return;
            }

            Interface interfaceSubmodule = Port.GetInterface();
            //If the disabled port is not used in ring then no error message is required
            if ((PNMrpRole)interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnMrpRole,
                    ac.GetNew(),
                    (int)PNMrpRole.NotInRing) == PNMrpRole.NotInRing)
            {
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.DisabledPortUsed);
        }

        /// <summary>
        /// This method checks if the port is really configured as a ring port in cases where the port is a default
        /// ring port and does not support ring port configuration.
        /// It is assumed that the port is known to be not selected before calling this algorithm.
        /// </summary>
        private void CheckRingPortConfig()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            if (Port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnMrpSupportsRingConfig, ac, false))
            {
                // The port supports ring port configuration, user can freely deselect this port.
                return;
            }
            if (!Port.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnMrpIsDefaultRingPort, ac.GetNew(), false))
            {
                // The port doesn't support mrp (both of the attribute values are false)
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.MrpPortShouldBeSelected,
                Utility.GetNameWithContainer(Port));
        }

        #endregion
    }
}
