/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: IODeviceSubmodule.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.Compiler.DataTypes.Variable;

#endregion

namespace PNConfigLib.Compiler.DataTypes
{
    internal class IOSubmoduleNode : ProjectTreeNodeBase
    {
        public IOSubmoduleNode(
            Submodule submodule,
            CompilerConstants.ClassRid classRID = CompilerConstants.ClassRid.IOSubmodule)
        {
            if (submodule != null)
            {
                Name = AttributeUtilities.GetName(submodule);
            }

            if (string.IsNullOrEmpty(Name))
            {
                Name = CompilerConstants.SubModuleName;
            }

            ClassRID = classRID;
            PCLObject = submodule;
        }

        public override void Compile()
        {
            //Compile LADDR
            //Compile DataRecordsConf
            //Compile DataRecordsTransferSequence
            Variables.Add(CompileUtility.GetKey(PCLObject, CompilerConstants.Keys.SubslotNumber));
            Variables.Add(CompileUtility.GetLaddr(PCLObject));
            Variables.Add(CompileUtility.GetDataRecordsConfDecentral(PCLObject));
            Variables.Add(CompileUtility.GetDataRecordsTransferSequenceDecentral(PCLObject));

            CompositeVariable ioMapping = CompileUtility.GetIoMapping(PCLObject as Submodule);
            if (ioMapping != null)
            {
                Variables.Add(ioMapping);
            }
        }
    }
}