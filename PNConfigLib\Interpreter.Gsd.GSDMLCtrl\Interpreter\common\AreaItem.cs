/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: AreaItem.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The AreaItem defines an area with his minimal and maximal
    /// value. It is needed to specify an area value within the 
    /// RefData objects.
    /// </summary>
    public class AreaItem :
        GsdObject,
        GSDI.IAreaItem
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AreaItem if it is instantiated. The properties of the 
        /// object are all initialized to empty or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public AreaItem()
        {
        }

        #endregion

        //########################################################################################
        #region Fields

        private object m_MinValue;
        private object m_MaxValue;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the minimal value of the area.
        /// </summary>
        public object MinValue => this.m_MinValue;

        /// <summary>
        /// Accesses the maximal value of the area.
        /// </summary>
        public object MaxValue => this.m_MaxValue;
        protected string MinValueAsString
        {
            get
            {
                if (null != this.m_MinValue)
                    return this.m_MinValue.ToString();
                return null;
            }
        }

        protected string MaxValueAsString
        {
            get
            {
                if (null != this.m_MaxValue)
                    return this.m_MaxValue.ToString();
                return null;
            }
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldMinValue;
                if (hash.ContainsKey(member))
                    this.m_MinValue = hash[member];

                member = Models.s_FieldMaxValue;
                if (hash.ContainsKey(member))
                    this.m_MaxValue = hash[member];
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectAreaItem);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            string val = String.Empty;
            if (null != this.m_MinValue)    // Normally object, not string!
                val = this.m_MinValue.ToString();
            Export.WriteStringProperty(ref writer, Models.s_FieldMinValue, val);

            val = String.Empty;
            if (null != this.m_MaxValue)    // Normally object, not string!
                val = this.m_MaxValue.ToString();
            Export.WriteStringProperty(ref writer, Models.s_FieldMaxValue, val);

            return true;
        }

        #endregion

        //########################################################################################
        #region Object members

        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(AreaItem))
                return false;

            AreaItem area1 = this;
            AreaItem area2 = obj as AreaItem;

            if (area2 == null)
            {
                return false;
            }

            if (!area1.MinValue.Equals(area2.MinValue))
                return false;

            if (!area1.MaxValue.Equals(area2.MaxValue))
                return false;

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 13;

            hash = hash * 23 + MinValue.GetHashCode();
            hash = hash * 23 + MaxValue.GetHashCode();

            return hash;
        }

        #endregion

    }
}