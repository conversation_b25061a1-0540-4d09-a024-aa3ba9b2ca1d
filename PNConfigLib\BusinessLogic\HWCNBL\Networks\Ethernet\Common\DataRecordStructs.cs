/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DataRecordStructs.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.ConfigParser;

#endregion

namespace PNConfigLib.HWCNBL.Networks.Ethernet.Common
{
    internal class NameOfStationStruct : DataRecordStruct
    {
        public NameOfStationStruct()
        {
            BlockType = EthernetSubblockIdentifiers.DeviceNamesSubblockId;
            BlockVersion = 0x0100;
            BlockLength = 0x8; // BlockVersion, Reserved
            Data = new byte[4];
        }

        public int NameOfStationLength
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }

        public int Padding
        {
            set { Transformator.Write16(Data, 2, (ushort)value); }
            get { return Transformator.Read16(Data, 2); }
        }
    }

    internal class IpAddressValidationLocalStruct : DataRecordStruct
    {
        public IpAddressValidationLocalStruct()
        {
            BlockType = DataRecords.BlockTypes.IpAddressValidationLocal;
            BlockLength = 6;
            Data = new byte[2];
        }

        public int IPAddressValidation
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }
    }

    internal class NameOfStationValidationStruct : DataRecordStruct
    {
        public NameOfStationValidationStruct()
        {
            BlockType = DataRecords.BlockTypes.NameOfStationValidation;
            BlockLength = 6;
            Data = new byte[2];
        }

        public int IPAddressValidation
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }
    }

    internal class TimeSyncStruct : DataRecordStruct
    {
        public TimeSyncStruct()
        {
            BlockType = 0x8000;
            BlockLength = 0x20; // BlockVersion, Reserved
            BlockVersion = 0x0100;
            Reserved = 0;

            Data = new byte[28];

            //-----------------------------------------------------------------------------
            // Sub-block COMMON_SYNCH_PARAMETERS (length 16)
            //-----------------------------------------------------------------------------
            BlockTypeCommon = 0x1001;
            BlockLengthCommon = 0x0C;
            BlockVersionCommon = 0x0100;
            ReservedCommon = 0;

            TimeTransitionMethod = 0x02;
            TimeTransmissionInterval = 0x01;

            //-----------------------------------------------------------------------------
            // Sub-block NTP_SERVER_CONFIG (length 44)
            //-----------------------------------------------------------------------------

            BlockType_NTP_SERVER_CONFIG = 0x1002;
            BlockLength_NTP_SERVER_CONFIG = 0x8;
            BlockVersion_NTP_SERVER_CONFIG = 0x0100;
            Reserved_NTP_SERVER_CONFIG = 0;
        }

        public int AsNtpNumberOfServers
        {
            set { Transformator.Write16(Data, 24, (ushort)value); }
            get { return Transformator.Read16(Data, 24); }
        }

        public uint AsNtpUpdatingIntervalClient
        {
            set { Transformator.Write32(Data, 12, value); }
            get { return Transformator.Read32(Data, 12); }
        }

        public int BlockLength_NTP_SERVER_CONFIG
        {
            set { Transformator.Write16(Data, 18, (ushort)value); }
            get { return Transformator.Read16(Data, 18); }
        }

        public int BlockLengthCommon
        {
            set { Transformator.Write16(Data, 2, (ushort)value); }
            get { return Transformator.Read16(Data, 2); }
        }

        public int BlockType_NTP_SERVER_CONFIG
        {
            set { Transformator.Write16(Data, 16, (ushort)value); }
            get { return Transformator.Read16(Data, 16); }
        }

        public int BlockTypeCommon
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }

        public int BlockVersion_NTP_SERVER_CONFIG
        {
            set { Transformator.Write16(Data, 20, (ushort)value); }
            get { return Transformator.Read16(Data, 20); }
        }

        public int BlockVersionCommon
        {
            set { Transformator.Write16(Data, 4, (ushort)value); }
            get { return Transformator.Read16(Data, 4); }
        }

        public int Reserved_NTP_SERVER_CONFIG
        {
            set { Transformator.Write16(Data, 22, (ushort)value); }
            get { return Transformator.Read16(Data, 22); }
        }

        public int ReservedCommon
        {
            set { Transformator.Write16(Data, 6, (ushort)value); }
            get { return Transformator.Read16(Data, 6); }
        }

        public int TimeSyncRole
        {
            set { Transformator.Write8(Data, 10, value); }
            get { return Transformator.Read8(Data, 10); }
        }

        public int TimeTransitionMethod
        {
            set { Transformator.Write8(Data, 8, value); }
            get { return Transformator.Read8(Data, 8); }
        }

        public int TimeTransmissionInterval
        {
            set { Transformator.Write8(Data, 9, value); }
            get { return Transformator.Read8(Data, 9); }
        }
    }

    internal class NtpServerConfigSubblockStruct : DataRecordStruct
    {
        public NtpServerConfigSubblockStruct()
        {
            Header = new byte[0];
            Data = new byte[8];
        }

        public uint AsNtpServerAddress
        {
            set { Transformator.Write32(Data, 4, value); }
            get { return Transformator.Read32(Data, 4); }
        }

        public int FormatServerAddress
        {
            set { Transformator.Write8(Data, 0, value); }
            get { return Transformator.Read8(Data, 0); }
        }

        public int LengthServerAddress
        {
            set { Transformator.Write8(Data, 1, value); }
            get { return Transformator.Read8(Data, 1); }
        }
    }

    internal class StationNameAliasStruct : DataRecordStruct
    {
        public StationNameAliasStruct()
        {
            Header = new byte[10];
            BlockType = DataRecords.BlockTypes.StationNameAlias;
            BlockLength = 0x6; // BV, Reserved, Number of ..
            BlockVersion = 0x0100;
            Reserved = 0;
        }

        public int AliasNameCount
        {
            set { Transformator.Write16(Header, 8, (ushort)value); }
            get { return Transformator.Read16(Header, 8); }
        }
    }

    internal class IpAddressValidationRemoteStruct : DataRecordStruct
    {
        public IpAddressValidationRemoteStruct(int datalength)
        {
            BlockType = DataRecords.BlockTypes.IpAddressValidationRemote;
            BlockLength = 4;
            BlockVersion = 0x0100;
            Data = new byte[datalength];
        }

        public int Properties
        {
            set { Transformator.WriteBitfieldBased16(Data, 2, 4, 1, value); }
            get { return Transformator.ReadBitfieldBased16(Data, 2, 4, 1); }
        }

        public int Validation
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }
    }

    internal class IpSuiteStruct : DataRecordStruct
    {
        public IpSuiteStruct()
        {
            BlockType = DataRecords.BlockTypes.IpV4Suite;
            BlockLength = 0x10;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[12];
        }

        public uint DefaultGateway
        {
            set { Transformator.Write32(Data, 8, value); }
            get { return Transformator.Read32(Data, 8); }
        }

        public uint IpAddress
        {
            set { Transformator.Write32(Data, 0, value); }
            get { return Transformator.Read32(Data, 0); }
        }

        public uint SubnetMask
        {
            set { Transformator.Write32(Data, 4, value); }
            get { return Transformator.Read32(Data, 4); }
        }
    }
}