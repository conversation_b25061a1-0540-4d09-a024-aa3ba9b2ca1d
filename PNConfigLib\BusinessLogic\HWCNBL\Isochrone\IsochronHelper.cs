/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronHelper.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Isochrone
{
    internal static class IsochronHelper
    {
        /// <summary>
        /// Calculates Cacf with given application cycle value
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        /// <param name="applicationCycle">
        /// Application cycle.
        /// </param>
        /// <returns>
        /// Returns calculated CACF value <see cref="float" />.
        /// </returns>
        public static float CalculateCacf(Interface controllerInterfaceSubmodule, long applicationCycle)
        {
            long sendClokFactor = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoSendClockFactor,
                new AttributeAccessCode(),
                0);

            float sendClock = sendClokFactor * 31.25f;

            float calculatedCacf = applicationCycle / sendClock;
            return calculatedCacf;
        }

        /// <summary>
        /// Returns Supported CACF list
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        public static List<int> GetSupportedCacfList(Interface controllerInterfaceSubmodule)
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();

            Enumerated supportedCacfs =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIsoCacfSupported,
                    accessCode,
                    null);

            List<int> supportedCacfList = new List<int>();

            if (!accessCode.IsOkay
                || (supportedCacfs == null))
            {
                return supportedCacfList;
            }

            foreach (int child in supportedCacfs.List)
            {
                supportedCacfList.Add(child);
            }

            return supportedCacfList;
        }

        /// <summary>
        /// Returns true if Isochronous mode is enabled for any IO Device in IO System
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        public static bool IsIsochronousModeEnabled(Interface controllerInterfaceSubmodule)
        {
            bool isoModeDecentralSupported =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.IsoModeDecentralSupported,
                    new AttributeAccessCode(),
                    false);

            if (isoModeDecentralSupported == false)
            {
                return false;
            }

            List<Interface> devices = NavigationUtilities.GetDevicesOfController(controllerInterfaceSubmodule);
            foreach (Interface device in devices)
            {
                bool pnIsochron = device.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIsochron,
                    new AttributeAccessCode(),
                    false);
                if (pnIsochron)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Validate CACF
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The controller interface submodule.
        /// </param>
        /// <param name="calculatedCacf">
        /// Input Value
        /// </param>
        public static bool ValidateCacf(Interface controllerInterfaceSubmodule, float calculatedCacf)
        {
            if (Math.Abs(calculatedCacf % 1) > float.Epsilon)
            {
                // calculatedCacf % 1 != 0
                // Cacf is not integer
                return false;
            }
            List<int> supportedCacfList = GetSupportedCacfList(controllerInterfaceSubmodule);
            int icalculatedCacf;
            return int.TryParse(
                       calculatedCacf.ToString(CultureInfo.InvariantCulture),
                       NumberStyles.Float,
                       CultureInfo.InvariantCulture,
                       out icalculatedCacf) && supportedCacfList.Contains(
                       Convert.ToInt32(icalculatedCacf, CultureInfo.InvariantCulture));
        }
    }
}