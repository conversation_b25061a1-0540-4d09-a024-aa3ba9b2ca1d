/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPSubnetMask.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL.Networks.Ethernet;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Network
{
    /// <summary>
    /// Summary description for IPSubnetMask.
    /// </summary>
    public class IPSubnetMask : IPAddressBase
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// Constructor
        /// </summary>
        public IPSubnetMask()
        {
            OwnAttributeName = InternalAttributeNames.NodeIPSubnetMask;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as Int64</param>
        public IPSubnetMask(long address) : base(address)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPSubnetMask;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as string</param>
        /// <param name="node"></param>
        public IPSubnetMask(string address, DataModel.PCLObjects.Node node)
        {
            if (node == null)
            {
                throw new ArgumentNullException(nameof(node));
            }

            OwnAttributeName = InternalAttributeNames.NodeIPSubnetMask;
            IPSubnetMask tmpAddress = ParseString<IPSubnetMask>(address);
            if (ErrorCode == IPAddressErrorCodes.None)
            {
                AsInt64 = tmpAddress.AsInt64;
            }
            else
            {
                UtilityNodeIe utility = new UtilityNodeIe(node.NodeBusinessLogic as NodeIeBusinessLogic);
                utility.SetCheckConsistencyMessage(this, ErrorCode, address);
            }
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="node">node to read the address from</param>
        public IPSubnetMask(DataModel.PCLObjects.Node node)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPSubnetMask;
            ReadFromNode(node);
        }

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        /// Check if the IP address is valid subnet mask.
        /// </summary>
        /// <returns></returns>
        public override IPAddressErrorCodes Check()
        {
            if (GetSubnetMaskLength() > 0)
            {
                return IPAddressErrorCodes.None;
            }
            return IPAddressErrorCodes.WrongSubnetMask;
        }

        /// <summary>
        /// Check if the IP address is valid
        /// </summary>
        /// <param name="address"></param>
        /// <param name="canSupernet"></param>
        /// <returns></returns>
        public IPAddressErrorCodes CheckAgainstAddress(IPAddressBase address, bool canSupernet)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }
            if (!IsInitialized || !address.IsInitialized)
            {
                return IPAddressErrorCodes.None;
            }
            IPAddressErrorCodes retValue = address.Check();
            if (address.AddressClass == IPAddressBaseAddressClasses.ClassDorE)
            {
                return retValue;
            }

            //following chacks are necesarry only for A B und C classes
            if (retValue == IPAddressErrorCodes.None)
            {
                retValue = CheckAgainstAddressClass(address);
            }

            if (retValue == IPAddressErrorCodes.None
                || canSupernet && retValue == IPAddressErrorCodes.AddressClassInvalid)
            {
                int addressIP4 = (int)address.AsInt64;
                int maskIP4 = (int)AsInt64;
                if ((addressIP4 & ~maskIP4) == ~maskIP4) //broadcast address is not allowed
                {
                    retValue = IPAddressErrorCodes.BroadcastAddress;
                }
                else if ((addressIP4 & ~maskIP4) == 0) //default route address is not allowed
                {
                    retValue = IPAddressErrorCodes.DefaultRouteAddress;
                }
            }
            return retValue;
        }

        /// <summary>
        /// return Network part of IP address
        /// </summary>
        /// <param name="address">input ip address</param>
        /// <returns>Network part of IP address</returns>
        public long GetNetAddress(IPAddressBase address)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }

            return address.AsInt64 & AsInt64;
        }

        /// <summary>
        /// return Host part of IP address
        /// </summary>
        /// <param name="address">input ip address</param>
        /// <returns>Host part of IP address</returns>
        public long GetHostAddress(IPAddressBase address)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }

            return address.AsInt64 & ~AsInt64;
        }

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class
        /// <summary>
        /// Check wheather subnet mask is correct and get the number of bits set
        /// </summary>
        /// <returns>
        /// -1 on illegal subnet mask
        /// n : The number of trailing 1s
        /// </returns>
        private int GetSubnetMaskLength()
        {
            long mask = AsInt64;
            if (mask >> 32 != 0)
            {
                return -1; // IP v4 is may be only 32 bits long
            }

            int trailingOnes = 0;
            while ((mask & 0x80000000) != 0) // *********
            {
                trailingOnes++;
                mask = (mask - 0x80000000) << 1;
            }

            if (mask != 0)
            {
                return -1; // Subnet mask has form 1...10...0 
            }

            return trailingOnes;
        }

        /// <summary>
        /// Check if the subnet mask fits to IP address class.
        /// </summary>
        /// <returns></returns>
        private IPAddressErrorCodes CheckAgainstAddressClass(IPAddressBase ipAddress)
        {
            if (!IsInitialized || !ipAddress.IsInitialized)
            {
                return IPAddressErrorCodes.None;
            }
            int maskLen = GetSubnetMaskLength();
            if (maskLen < 0)
            {
                return IPAddressErrorCodes.WrongSubnetMask;
            }

            IPAddressBaseAddressClasses addressClass = ipAddress.AddressClass;

            switch (addressClass)
            {
                case IPAddressBaseAddressClasses.ClassDorE:
                    return IPAddressErrorCodes.None;
                case IPAddressBaseAddressClasses.ClassC:
                    if (maskLen >= 24)
                    {
                        return IPAddressErrorCodes.None;
                    }
                    break;
                case IPAddressBaseAddressClasses.ClassB:
                    if (maskLen >= 16)
                    {
                        return IPAddressErrorCodes.None;
                    }
                    break;
                case IPAddressBaseAddressClasses.ClassA:
                    if (maskLen >= 8)
                    {
                        return IPAddressErrorCodes.None;
                    }
                    break;
                default:
                    Debug.Fail("Unknown ip address class");
                    break;
            }
            return IPAddressErrorCodes.AddressClassInvalid;
        }

        #endregion
    }
}