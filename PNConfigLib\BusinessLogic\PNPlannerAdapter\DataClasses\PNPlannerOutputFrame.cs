/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerOutputFrame.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.HWCNBL.DomainManagement;

#endregion

namespace PNConfigLib.PNPlannerAdapter.DataClasses
{
    internal class PNPlannerOutputFrame : IPNPlannerOutputFrame
    {
        #region Fields

        private readonly List<int> m_Ports = new List<int>();

        private readonly List<long> m_PossibleReductionRatios = new List<long>();

        public PNPlannerOutputFrame()
        {
            Local = 0;
            MeaningFrameSendOffset = 0;
            SyncFrame = 0;
            RxPort = 0;
            EtherType = 0;
            FrameID = 0;
            RedundantFrameID = 0;
            Phase = 0;
            ControllerLocalReductionRatio = 0;
            Length = 0;
            Padding = 0;
            FrameSendOffset = 0;
            SendclockFactor = 0;
        }

        #endregion

        #region Properties

        public uint FrameSendOffset { get; set; }

        public int Length { get; set; }

        public int Padding { get; set; }

        public int ReductionRatio { get; set; }

        public int ControllerLocalReductionRatio { get; set; }

        public int DeviceLocalReductionRatio { get; set; }

        public int Phase { get; set; }

        public int ControllerLocalPhase { get; set; }

        public int DeviceLocalPhase { get; set; }

        public int FrameID { get; set; }

        public int OriginalFrameID { get; set; }

        public int RedundantFrameID { get; set; }

        public int EtherType { get; set; }

        public int RxPort { get; set; }

        public int SyncFrame { get; set; }

        public int MeaningFrameSendOffset { get; }

        public int Local { get; set; }

        public IList<int> Ports => m_Ports;
        
        public int SendclockFactor { get; set; }

        public IList<long> PossibleReductionRatios => m_PossibleReductionRatios;

        public IList<IPNPlannerOutputSubframe> Subframes { get; set; }

        public bool SfCrc16 { get; set; }

        #endregion
    }
}