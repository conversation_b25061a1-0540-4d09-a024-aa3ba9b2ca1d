/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SharedDeviceFrame.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL
{
    internal static class SharedDeviceFrame
    {
        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums
        private const int m_SharedDeviceStationNumberBase = 1 << 12; //Because in Classic: 2 ^ 12 = 4096

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class
        /// <summary>
        /// This method multiplies the required device frames to the shared device functionality
        /// It is important to planning with frames and to calculate RT and IRT bandwidth
        /// These frames are not relevant after PNPlanner calculation any more, so they are not valid after this method call
        /// DO NOT USE THESE FRAMES FOR OTHER PURPOSES!
        /// </summary>
        /// <param name="frameDataListOfController">Original frame list</param>
        /// <param name="hasSharedDfpFrame"></param>
        /// <returns>Only the multiplied frame list without original frames</returns>
        public static List<IPNFrameData> SharedPNFrameMultiplier(
            List<IPNFrameData> frameDataListOfController,
            bool hasSharedDfpFrame)
        {
            if (frameDataListOfController == null)
            {
                throw new ArgumentNullException(nameof(frameDataListOfController));
            }

            int sharedStationNumber = m_SharedDeviceStationNumberBase; //Sample from classic: 4096
            long maxFrameID = frameDataListOfController.Max(p => p.FrameID) + 1000; //This is not defined rule

            List<IPNFrameData> framesToMultiply =
                frameDataListOfController.Where(
                    q =>
                    (q.NumberOfARs > 0)
                    && ((q.FrameType == (long)PNPlannerFrameType.IDeviceFrame) || q.HasNotAssignedSubmodule)).ToList();

            if (hasSharedDfpFrame)
            {
                framesToMultiply.AddRange(GetDfpFramesToMultiply(frameDataListOfController));
            }

            //Helper method to station number mapping
            Func<uint, int[]> NewStationNumbers = delegate(uint numberOfARs)
                                                      {
                                                          int[] stationNumbers = new int[numberOfARs];
                                                          for (int i = 0; i < numberOfARs; i++)
                                                          {
                                                              stationNumbers[i] = sharedStationNumber++;
                                                          }
                                                          return stationNumbers;
                                                      };

            //Station number dictionary between original frames and multiplied frames
            Dictionary<int, int[]> stationNameDictionary =
                framesToMultiply.OrderBy(p => p.StationNumber)
                    .Select(q => new { q.StationNumber, q.NumberOfARs })
                    .Distinct()
                    .ToDictionary(p => p.StationNumber, p => NewStationNumbers(p.NumberOfARs));

            List<IPNFrameData> multipliedFrames = new List<IPNFrameData>();

            foreach (IPNFrameData pnFrameData in framesToMultiply)
            {
                PNFrameData frameData = (PNFrameData)pnFrameData;
                //Frame multiplication
                for (int i = 0; i < frameData.NumberOfARs; i++)
                {
                    //Create a new instance of the frame (avoid reference)
                    PNFrameData newFrameData = new PNFrameData(frameData);

                    if ((frameData.SharedDataLength > 0)
                        && (i == 0)) //Frame with max length is created first
                    {
                        newFrameData.DataLength = frameData.SharedDataLength;
                    }
                    else
                    {
                        newFrameData.DataLength = PNConstants.MinFramePayloadLength;
                    }

                    //Shared frames for Shared iDevices should have their frame lengths from the iDevice reservation
                    // frame.
                    if (frameData.FrameType == (long)PNPlannerFrameType.IDeviceFrame)
                    {
                        newFrameData.DataLength = frameData.DataLength;
                    }

                    //Required settings
                    newFrameData.FrameType = (long)PNPlannerFrameType.SharedFrame;
                    //The doubled frame must be in RT mode
                    newFrameData.FrameClass = (long)PNIOFrameClass.Class1Frame;
                    newFrameData.IoSyncRole = (byte)PNIRTSyncRole.NotSynchronized;
                    newFrameData.HasUsingData = true;
                    newFrameData.StationNumber =
                        stationNameDictionary.Single(p => p.Key == frameData.StationNumber).Value[i];

                    multipliedFrames.Add(newFrameData);
                }
            }

            //Assign the appropriate FrameID
            foreach (
                IPNFrameData framedData in multipliedFrames.OrderBy(p => p.StationNumber).ThenBy(q => q.FrameDirection))
            {
                framedData.FrameID = maxFrameID++;
            }
            //Return with practical order
            return multipliedFrames.OrderBy(p => p.FrameID).ToList();
        }

        #endregion

        //########################################################################################

        #region Private Implementations

        private static List<PNFrameData> GetDfpFramesToMultiply(List<IPNFrameData> frameDataListOfController)
        {
            List<PNFrameData> dfpFramesToMultiply = new List<PNFrameData>();
            if ((frameDataListOfController == null)
                || !frameDataListOfController.Exists(p => p is PNDfpFrameData))
            {
                return dfpFramesToMultiply;
            }
            foreach (IPNFrameData frameData in frameDataListOfController)
            {
                if (!(frameData is PNDfpFrameData))
                {
                    continue;
                }
                PNDfpFrameData dfpFrame = frameData as PNDfpFrameData;
                SortedList<int, IPNSubframeData> subframes = dfpFrame.Subframes;
                foreach (KeyValuePair<int, IPNSubframeData> subframeEntry in subframes)
                {
                    PNSubframeData subframe = subframeEntry.Value as PNSubframeData;
                    if ((subframe != null)
                        && (subframe.NumberOfARs > 0)
                        && dfpFrame.HasNotAssignedSubmodule)
                    {
                        //Create a new PNFrameData by copying the attributes of PNDfpFrameData
                        PNFrameData dfpFrameToMultiply = new PNFrameData(subframe.CoreId)
                                                             {
                                                                 ControllerLocalPhase =
                                                                     dfpFrame
                                                                     .ControllerLocalPhase,
                                                                 ControllerLocalReductionRatio
                                                                     =
                                                                     dfpFrame
                                                                     .ControllerLocalReductionRatio,
                                                                 DataHoldFactor =
                                                                     dfpFrame
                                                                     .DataHoldFactor,
                                                                 DeviceLocalPhase =
                                                                     dfpFrame
                                                                     .DeviceLocalPhase,
                                                                 DeviceLocalReductionRatio
                                                                     =
                                                                     dfpFrame
                                                                     .DeviceLocalReductionRatio,
                                                                 SharedDataLength =
                                                                     subframe
                                                                     .SharedDataLength,
                                                                 FixedPhaseNumber =
                                                                     dfpFrame
                                                                     .FixedPhaseNumber,
                                                                 FrameDirection =
                                                                     dfpFrame
                                                                     .FrameDirection,
                                                                 FrameMultiplier =
                                                                     dfpFrame
                                                                     .FrameMultiplier,
                                                                 HasUsingData =
                                                                     dfpFrame.HasUsingData,
                                                                 MinAutomaticUnsyncUpdateTime
                                                                     =
                                                                     dfpFrame
                                                                     .MinAutomaticUnsyncUpdateTime,
                                                                 MinFrameIntervall =
                                                                     dfpFrame
                                                                     .MinFrameIntervall,
                                                                 PossibleReductionRatios =
                                                                     dfpFrame
                                                                     .PossibleReductionRatios,
                                                                 ProxyNumber =
                                                                     dfpFrame.ProxyNumber,
                                                                 MinAutomaticUnsyncRR =
                                                                     dfpFrame
                                                                     .MinAutomaticUnsyncUpdateTime,
                                                                 SendClockFactor =
                                                                     dfpFrame
                                                                     .SendClockFactor,
                                                                 Sequence =
                                                                     dfpFrame.Sequence,
                                                                 SuppRRPow =
                                                                     dfpFrame.SuppRRPow,
                                                                 SuppRRNonPow =
                                                                     dfpFrame.SuppRRNonPow,
                                                                 SuppSendClockFactors =
                                                                     dfpFrame
                                                                     .SuppSendClockFactors,
                                                                 UpdateTimeMode =
                                                                     dfpFrame
                                                                     .UpdateTimeMode,
                                                                 StationNumber =
                                                                     subframe.StationNumber,
                                                                 SlotNumber =
                                                                     dfpFrame.SlotNumber,
                                                                 SubSlotNumber =
                                                                     dfpFrame.SubSlotNumber,
                                                                 WatchdogFactor =
                                                                     dfpFrame
                                                                     .WatchdogFactor,
                                                                 HasNotAssignedSubmodule =
                                                                     dfpFrame
                                                                     .HasNotAssignedSubmodule,
                                                                 NumberOfARs =
                                                                     subframe.NumberOfARs
                                                             };
                        dfpFramesToMultiply.Add(dfpFrameToMultiply);
                    }
                }
            }
            return dfpFramesToMultiply;
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion
    }
}