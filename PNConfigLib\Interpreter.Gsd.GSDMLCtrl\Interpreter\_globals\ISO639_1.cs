/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ISO639_1.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces

using System.Collections.Generic;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    static class Iso6391
    {
        // 2-letter language codes from ISO 639-1 (Alpha-2)
        public static Dictionary<string, string> Lang { get; } = new Dictionary<string, string>(186);

        static Iso6391()
        {
            Lang.Add("aa", "Afar");
            Lang.Add("ab", "Abkhazian");
            Lang.Add("ae", "Avestan");
            Lang.Add("af", "Afrikaans");
            Lang.Add("ak", "Akan");
            Lang.Add("am", "Amharic");
            Lang.Add("an", "Aragonese");
            Lang.Add("ar", "Arabic");
            Lang.Add("as", "Assamese");
            Lang.Add("av", "Avaric");
            Lang.Add("ay", "Aymara");
            Lang.Add("az", "Azerbaijani");
            Lang.Add("ba", "Bashkir");
            Lang.Add("be", "Belarusian");
            Lang.Add("bg", "Bulgarian");
            Lang.Add("bh", "Bihari");
            Lang.Add("bi", "Bislama");
            Lang.Add("bm", "Bambara");
            Lang.Add("bn", "Bengali");
            Lang.Add("bo", "Tibetan");
            Lang.Add("br", "Breton");
            Lang.Add("bs", "Bosnian");
            Lang.Add("ca", "Catalan");
            Lang.Add("ce", "Chechen");
            Lang.Add("ch", "Chamorro");
            Lang.Add("co", "Corsican");
            Lang.Add("cr", "Cree");
            Lang.Add("cs", "Czech");
            Lang.Add("cu", "Church Slavic");
            Lang.Add("cv", "Chuvash");
            Lang.Add("cy", "Welsh");
            Lang.Add("da", "Danish");
            Lang.Add("de", "German");
            Lang.Add("dv", "Divehi");
            Lang.Add("dz", "Dzongkha");
            Lang.Add("ee", "Ewe");
            Lang.Add("el", "Greek");
            Lang.Add("en", "English");
            Lang.Add("eo", "Esperanto");
            Lang.Add("es", "Spanish");
            Lang.Add("et", "Estonian");
            Lang.Add("eu", "Basque");
            Lang.Add("fa", "Persian");
            Lang.Add("ff", "Fulah");
            Lang.Add("fi", "Finnish");
            Lang.Add("fj", "Fijian");
            Lang.Add("fo", "Faroese");
            Lang.Add("fr", "French");
            Lang.Add("fy", "Western Frisian");
            Lang.Add("ga", "Irish");
            Lang.Add("gd", "Gaelic");
            Lang.Add("gl", "Galician");
            Lang.Add("gn", "Guarani");
            Lang.Add("gu", "Gujarati");
            Lang.Add("gv", "Manx");
            Lang.Add("ha", "Hausa");
            Lang.Add("he", "Hebrew");
            Lang.Add("hi", "Hindi");
            Lang.Add("ho", "Hiri Motu");
            Lang.Add("hr", "Croatian");
            Lang.Add("ht", "Haitian");
            Lang.Add("hu", "Hungarian");
            Lang.Add("hy", "Armenian");
            Lang.Add("hz", "Herero");
            Lang.Add("ia", "Interlingua");
            Lang.Add("id", "Indonesian");
            Lang.Add("ie", "Interlingue");
            Lang.Add("ig", "Igbo");
            Lang.Add("ii", "Sichuan Yi");
            Lang.Add("ik", "Inupiaq");
            Lang.Add("io", "Ido");
            Lang.Add("is", "Icelandic");
            Lang.Add("it", "Italian");
            Lang.Add("iu", "Inuktitut");
            Lang.Add("ja", "Japanese");
            Lang.Add("jv", "Javanese");
            Lang.Add("ka", "Georgian");
            Lang.Add("kg", "Kongo");
            Lang.Add("ki", "Kikuyu");
            Lang.Add("kj", "Kuanyama");
            Lang.Add("kk", "Kazakh");
            Lang.Add("kl", "Kalaallisut");
            Lang.Add("km", "Central Khmer");
            Lang.Add("kn", "Kannada");
            Lang.Add("ko", "Korean");
            Lang.Add("kr", "Kanuri");
            Lang.Add("ks", "Kashmiri");
            Lang.Add("ku", "Kurdish");
            Lang.Add("kv", "Komi");
            Lang.Add("kw", "Cornish");
            Lang.Add("ky", "Kirghiz");
            Lang.Add("la", "Latin");
            Lang.Add("lb", "Luxembourgish");
            Lang.Add("lg", "Ganda");
            Lang.Add("li", "Limburgan");
            Lang.Add("ln", "Lingala");
            Lang.Add("lo", "Lao");
            Lang.Add("lt", "Lithuanian");
            Lang.Add("lu", "Luba-Katanga");
            Lang.Add("lv", "Latvian");
            Lang.Add("mg", "Malagasy");
            Lang.Add("mh", "Marshallese");
            Lang.Add("mi", "Maori");
            Lang.Add("mk", "Macedonian");
            Lang.Add("ml", "Malayalam");
            Lang.Add("mn", "Mongolian");
            Lang.Add("mo", "Moldavian");
            Lang.Add("mr", "Marathi");
            Lang.Add("ms", "Malay");
            Lang.Add("mt", "Maltese");
            Lang.Add("my", "Burmese");
            Lang.Add("na", "Nauru");
            Lang.Add("nb", "Bokmål");
            Lang.Add("nd", "North Ndebele");
            Lang.Add("ne", "Nepali");
            Lang.Add("ng", "Ndonga");
            Lang.Add("nl", "Dutch");
            Lang.Add("nn", "Norwegian Nynorsk");
            Lang.Add("no", "Norwegian");
            Lang.Add("nr", "South Ndebele");
            Lang.Add("nv", "Navajo");
            Lang.Add("ny", "Chichewa");
            Lang.Add("oc", "Occitan");
            Lang.Add("oj", "Ojibwa");
            Lang.Add("om", "Oromo");
            Lang.Add("or", "Oriya");
            Lang.Add("os", "Ossetian");
            Lang.Add("pa", "Panjabi");
            Lang.Add("pi", "Pali");
            Lang.Add("pl", "Polish");
            Lang.Add("ps", "Pushto");
            Lang.Add("pt", "Portuguese");
            Lang.Add("qu", "Quechua");
            Lang.Add("rm", "Romansh");
            Lang.Add("rn", "Rundi");
            Lang.Add("ro", "Romanian");
            Lang.Add("ru", "Russian");
            Lang.Add("rw", "Kinyarwanda");
            Lang.Add("sa", "Sanskrit");
            Lang.Add("sc", "Sardinian");
            Lang.Add("sd", "Sindhi");
            Lang.Add("se", "Northern Sami");
            Lang.Add("sg", "Sango");
            Lang.Add("si", "Sinhala");
            Lang.Add("sk", "Slovak");
            Lang.Add("sl", "Slovenian");
            Lang.Add("sm", "Samoan");
            Lang.Add("sn", "Shona");
            Lang.Add("so", "Somali");
            Lang.Add("sq", "Albanian");
            Lang.Add("sr", "Serbian");
            Lang.Add("ss", "Swati");
            Lang.Add("st", "Southern Sotho");
            Lang.Add("su", "Sundanese");
            Lang.Add("sv", "Swedish");
            Lang.Add("sw", "Swahili");
            Lang.Add("ta", "Tamil");
            Lang.Add("te", "Telugu");
            Lang.Add("tg", "Tajik");
            Lang.Add("th", "Thai");
            Lang.Add("ti", "Tigrinya");
            Lang.Add("tk", "Turkmen");
            Lang.Add("tl", "Tagalog");
            Lang.Add("tn", "Tswana");
            Lang.Add("to", "Tonga");
            Lang.Add("tr", "Turkish");
            Lang.Add("ts", "Tsonga");
            Lang.Add("tt", "Tatar");
            Lang.Add("tw", "Twi");
            Lang.Add("ty", "Tahitian");
            Lang.Add("ug", "Uighur");
            Lang.Add("uk", "Ukrainian");
            Lang.Add("ur", "Urdu");
            Lang.Add("uz", "Uzbek");
            Lang.Add("ve", "Venda");
            Lang.Add("vi", "Vietnamese");
            Lang.Add("vo", "Volapük");
            Lang.Add("wa", "Walloon");
            Lang.Add("wo", "Wolof");
            Lang.Add("xh", "Xhosa");
            Lang.Add("yi", "Yiddish");
            Lang.Add("yo", "Yoruba");
            Lang.Add("za", "Zhuang");
            Lang.Add("zh", "Chinese");
            Lang.Add("zu", "Zulu");
        }

        public static string LangCodeToString(string langCode)
        {
            if (Lang.ContainsKey(langCode))
            {
                return Lang[langCode];
            }
            else
            {
                return langCode + "??";
            }
        }
    }
}
