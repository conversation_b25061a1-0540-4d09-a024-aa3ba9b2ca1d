/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleBL.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.PNProjectManager;
using Module = PNConfigLib.DataModel.PCLObjects.Module;

#endregion

namespace PNConfigLib.HWCNBL
{
    using PNConfigLib.BusinessLogic.HWCNBL;

    internal class ModuleBL : HwcnBusinessLogic
    {
        public ModuleBL(Module module)
        {
            Module = module;
            InitBL();
        }

        private Module Module { get; }

        public void Configure(ModuleType xmlModule, IOAddressManager ioAddressManager)
        {
            FillGeneralAttributes(Module, xmlModule.General, xmlModule.ModuleID);

            Module.AttributeAccess.SetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                (int)xmlModule.SlotNumber);

            Submodule virtualSubmodule = GetAddressCarrierSubmodule();
            if (virtualSubmodule == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            IOData smCatalogIOData = ((SubmoduleCatalog)virtualSubmodule.PCLCatalogObject).IOData;

            if (smCatalogIOData != null)
            {
                IoDataHelper ioDataHelper = new IoDataHelper();
                IoDataHelper.IoAddressImplementationType ioAddressImplementationType;
                int startAddress = 0;
                if (smCatalogIOData.InputDataItems != null && smCatalogIOData.InputDataItems.Length > 0)
                {
                    if (xmlModule.IOAddresses.Exists(w => w is ModuleTypeInputAddresses))
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromXml;
                        startAddress = (int)xmlModule.IOAddresses.OfType<ModuleTypeInputAddresses>()
                            .FirstOrDefault().StartAddress;
                    }
                    else if (!CheckIoAddresDefinedInVSM(xmlModule, virtualSubmodule, IoTypes.Input))
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromVirtualSubmodule;
                    }
                    else
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.AlreadySet;
                    }

                    ioDataHelper.SetIOData(
                        xmlModule.ModuleID,
                        startAddress,
                        IoTypes.Input,
                        ioAddressManager,
                        virtualSubmodule,
                        ac,
                        InternalAttributeNames.InAddressRange,
                        InternalAttributeNames.InputStartAddress,
                        ioAddressImplementationType);
                }

                if (smCatalogIOData.OutputDataItems != null && smCatalogIOData.OutputDataItems.Length > 0)
                {
                    if (xmlModule.IOAddresses.Exists(w => w is ModuleTypeOutputAddresses))
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromXml;
                        startAddress = (int)xmlModule.IOAddresses.OfType<ModuleTypeOutputAddresses>()
                            .FirstOrDefault().StartAddress;
                    }
                    else if (!CheckIoAddresDefinedInVSM(xmlModule, virtualSubmodule, IoTypes.Output)) 
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.FromVirtualSubmodule;
                    }
                    else
                    {
                        ioAddressImplementationType = IoDataHelper.IoAddressImplementationType.AlreadySet;
                    }

                    ioDataHelper.SetIOData(
                        xmlModule.ModuleID,
                        startAddress,
                        IoTypes.Output,
                        ioAddressManager,
                        virtualSubmodule,
                        ac,
                        InternalAttributeNames.OutAddressRange,
                        InternalAttributeNames.OutputStartAddress,
                        ioAddressImplementationType);
                }
            }

            if (virtualSubmodule.ParameterRecordDataItems.Count == 0 || xmlModule.ParameterRecordDataItems.Count > 0)
            {
                Dictionary<uint, byte[]> parameterRecordDataItems = FillParameterRecordDataItems(
                    virtualSubmodule,
                    xmlModule.ParameterRecordDataItems,
                    xmlModule.ModuleID);
                virtualSubmodule.SetParameterRecordDataItems(parameterRecordDataItems);
            }
        }

        private static bool CheckIoAddresDefinedInVSM(ModuleType xmlModule, Submodule virtualSubmodule, IoTypes ioType)
        {
            if (xmlModule.Submodule.Exists(w => w.GSDRefID == virtualSubmodule.Id))
            {
                ModuleTypeSubmodule submodule = xmlModule.Submodule.Find(w => w.GSDRefID == virtualSubmodule.Id);
                if (ioType == IoTypes.Input && submodule.IOAddresses.Exists(k => k is ModuleTypeInputAddresses))
                {
                    return true;
                }

                if (ioType == IoTypes.Output && submodule.IOAddresses.Exists(k => k is ModuleTypeOutputAddresses))
                {
                    return true;
                }
            }
            return false;
        }

        private Submodule GetAddressCarrierSubmodule()
        {
            List<Submodule> virtualSubmodules = Module.GetVirtualSubmodules();
            if ((virtualSubmodules != null)
                && (virtualSubmodules.Count > 0))
            {
                if (virtualSubmodules.Count > 1)
                {
                    return
                        virtualSubmodules.First(
                            vs =>
                            (vs.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.InAddressRange,
                                new AttributeAccessCode(),
                                -1) != -1)
                            || (vs.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.OutAddressRange,
                                new AttributeAccessCode(),
                                -1) != -1));
                }
                return virtualSubmodules.First();
            }
            return null;
        }

        private void InitActions()
        {
            ConsistencyManager.RegisterConsistencyCheck(Module, MethodCheckConsistency);
        }

        private void InitBL()
        {
            InitActions();
            InitAttributes();
        }

        private void InitAttributes()
        {
            Module.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)SharedIoAssignment.None);
        }

        private void MethodCheckConsistency()
        {
            CheckConsistencyUtility.CheckConsistencyModuleWithoutSubmodule(Module);
        }
    }
}