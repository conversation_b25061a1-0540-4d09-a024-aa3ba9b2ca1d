<Window x:Class="PNConfigTool.Views.Windows.GSDMLCatalogWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PNConfigTool.Views.Windows"
        xmlns:vm="clr-namespace:PNConfigTool.ViewModels"
        xmlns:models="clr-namespace:PNConfigTool.Models"
        xmlns:conv="clr-namespace:PNConfigTool.Converters"
        mc:Ignorable="d"
        Title="GSDML目录查看器" Height="650" Width="900" 
        WindowStartupLocation="CenterScreen" Background="#F4F4F4">
    
    <Window.Resources>
        <conv:MultiCollectionConverter x:Key="MultiCollectionConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        
        <!-- 主产品家族节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:MainFamilyNode}" 
                                 ItemsSource="{Binding Vendors}">
            <TextBlock Text="{Binding Name}" FontWeight="Bold" Foreground="#2E5266"/>
        </HierarchicalDataTemplate>
        
        <!-- 厂商节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:VendorFamilyNode}" 
                                 ItemsSource="{Binding ProductFamilies}">
            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" Foreground="#1E3D59"/>
        </HierarchicalDataTemplate>
        
        <!-- 产品家族节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:ProductFamilyNode}" 
                                 ItemsSource="{Binding Devices}">
            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" Foreground="#6B7A8F"/>
        </HierarchicalDataTemplate>
        
        <!-- 制造商节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:GSDMLManufacturerNode}" 
                                 ItemsSource="{Binding Devices}">
            <TextBlock Text="{Binding Name}" />
        </HierarchicalDataTemplate>
        
        <!-- 设备节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:GSDMLDeviceNode}" 
                                 ItemsSource="{Binding AllChildren}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding DisplayName}" FontWeight="SemiBold" />
            </StackPanel>
        </HierarchicalDataTemplate>
        
        <!-- DAP节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:DAPNode}" 
                                 ItemsSource="{Binding Children}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="DAP:" FontWeight="SemiBold" Foreground="Teal"/>
                <TextBlock Text="{Binding Name}" Margin="5,0,0,0" Foreground="Teal" />
            </StackPanel>
        </HierarchicalDataTemplate>
        
        <!-- 标题节点模板 -->
        <HierarchicalDataTemplate DataType="{x:Type vm:HeaderNode}" 
                                  ItemsSource="{Binding Children}">
            <TextBlock Text="{Binding Title}" 
                       FontWeight="Bold" 
                       FontStyle="Italic"
                       Foreground="#444444"/>
        </HierarchicalDataTemplate>
        

        
        <!-- 接口节点模板 -->
        <DataTemplate DataType="{x:Type vm:GSDMLInterfaceNode}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Name}" />
                <TextBlock Text="{Binding Size, StringFormat=' (Size: {0} bytes)'}" Margin="5,0,0,0" Foreground="DarkGray"/>
            </StackPanel>
        </DataTemplate>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 介绍信息 -->
        <GroupBox Grid.Row="0" Header="GSDML文件目录" Margin="10,5" Padding="5">
            <TextBlock TextWrapping="Wrap" Margin="5">
                此窗口显示所有已导入的GSDML文件及其包含的设备、模块、子模块和接口信息。您可以按照主产品家族、制造商、产品家族的结构浏览GSDML文件，查看详细配置信息。
            </TextBlock>
        </GroupBox>
        
        <!-- 工具栏 -->
        <ToolBar Grid.Row="1" Margin="10,0,10,5">
            <Button Command="{Binding RefreshCatalogCommand}" ToolTip="刷新GSDML目录">
                <TextBlock Text="刷新目录"/>
            </Button>
        </ToolBar>
        
        <!-- 树状视图 - 使用新的MainFamilies作为数据源 -->
        <TreeView Grid.Row="2" Margin="10,0,10,5" 
                  ItemsSource="{Binding MainFamilies}"
                  VirtualizingStackPanel.IsVirtualizing="True"
                  VirtualizingStackPanel.VirtualizationMode="Recycling">
            <TreeView.ItemContainerStyle>
                <Style TargetType="{x:Type TreeViewItem}">
                    <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}" />
                    <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}" />
                </Style>
            </TreeView.ItemContainerStyle>
        </TreeView>
        
        <!-- 加载指示器 -->
        <Grid Grid.Row="2" Background="#88FFFFFF" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
                <TextBlock Text="正在加载GSDML目录..." HorizontalAlignment="Center" Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Background="#E6E6E6">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <Button Content="关闭" Width="80" Margin="0,2" Click="CloseButton_Click"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window> 