/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AttributeValues.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.AttributeUtilities
{
    internal static class AttributeValues
    {
        internal static class CableTypes
        {
            internal const string CabeType1 = "0000000100000001";

            internal const string CabeType2 = "0000000100000002";

            internal const string CabeType3 = "0000000100000003";

            internal const string CabeTypeFoStandardCableGp50 = "0000000200000001";

            internal const string CableTypeFoTrailingCableGp = "0000000200000002";

            internal const string CableTypeFoGroundCable = "0000000200000003";

            internal const string CableTypeFoStandardCable625 = "0000000300000001";

            internal const string CableTypeFlexibleFoCable = "0000000300000002";

            internal const string CableTypePofStandardCableGp = "0000000400000001";

            internal const string CableTypePofTrailingCable = "0000000400000002";

            internal const string CableTypePcfStandardCableGp = "0000000500000001";

            internal const string CableTypePcfTrailingCableGp = "0000000500000002";
        }
    }
}
