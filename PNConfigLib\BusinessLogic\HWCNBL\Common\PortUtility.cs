/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PortUtility.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Common
{
    /// <summary>
    /// Summary description for Utility.
    /// </summary>
    internal static class PortUtility
    {
        /// <summary>
        /// The medium types.
        /// </summary>
        internal enum MediumType
        {
            Cu,

            POF,

            FO
        }
        /// <summary>
        /// Checks whether the PNDCPBoundary (detection of accessible nodes) is supported
        /// </summary>
        /// <returns></returns>
        internal static bool IsPNDCPBoundarySupported(DataModel.PCLObjects.Port port)
        {
            bool supported = false;
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(port);
            if (interfaceSubmodule != null)
            {
                supported = interfaceSubmodule.AttributeAccess.
                                               GetAnyAttribute<Boolean>(InternalAttributeNames.PnDCPBoundarySupported,
                                                                        new AttributeAccessCode(), false);
            }
            return supported;
        }
        /// <summary>
        /// Checks whether the PNPTPBoundarySupported (topology detection) is supported
        /// </summary>
        /// <returns></returns>
        internal static bool IsPNPTPBoundarySupported(DataModel.PCLObjects.Port port)
        {
            bool supported = false;
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(port);
            if (interfaceSubmodule != null)
            {
                supported = interfaceSubmodule.AttributeAccess.
                                               GetAnyAttribute<Boolean>(InternalAttributeNames.PnPTPBoundarySupported,
                                                                        new AttributeAccessCode(), false);
            }
            return supported;
        }
        internal static bool CheckMediumIdentity(DataModel.PCLObjects.Port port1, DataModel.PCLObjects.Port port2)
        {
            // Check if mediums are the same.
            bool port1IsFiberOptic = IsFiberOptic(port1);
            bool port2IsFiberOptic = IsFiberOptic(port2);

            // Check if mediums are the same.
            if (port1IsFiberOptic && port2IsFiberOptic)
            {
                // Both mediums are fiber optic cable.

                // PNFiberOpticType should be identical.
                AttributeAccessCode accessCode = new AttributeAccessCode();
                long fiberOpticType1 =
                    port1.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnFiberOptic,
                        accessCode,
                        0);
                long fiberOpticType2 =
                    port2.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnFiberOptic,
                        accessCode,
                        0);

                if ((fiberOpticType1 != 0)
                    && (fiberOpticType1 == fiberOpticType2))
                {
                    return true;
                }
            }

            if (!port1IsFiberOptic
                && !port2IsFiberOptic)
            {
                // Both mediums are copper cable.
                return true;
            }

            return false;
        }

        /// <summary>
        /// Get default cabledata for port.
        /// </summary>
        internal static CableData GetDefaultCableDataForPort(DataModel.PCLObjects.Port port)
        {
            MediumType mediumType = GetMediumTypeOfPort(port);

            switch (mediumType)
            {
                case MediumType.Cu:
                    return new CableData(
                        PNConstants.DefaultPNIrtSignalDelayTimeCU,
                        PNConstants.DefaultPNIrtSignalDelayTimeCU,
                        CableDataEditMode.LineLength);
                case MediumType.POF:
                    return new CableData(
                        PNConstants.DefaultPNIrtSignalDelayTimePOF,
                        PNConstants.DefaultPNIrtSignalDelayTimePOF,
                        CableDataEditMode.LineLength);
                case MediumType.FO:
                    return new CableData(
                        PNConstants.DefaultPNIrtSignalDelayTimeFO,
                        PNConstants.DefaultPNIrtSignalDelayTimeFO,
                        CableDataEditMode.LineLength);
                default:
                    Debug.Fail("Should never happen");
                    return null;
            }
        }

        /// <summary>
        /// Gets the mediumtype of the given port.
        /// </summary>
        internal static MediumType GetMediumTypeOfPort(DataModel.PCLObjects.Port port)
        {
            if (port != null)
            {
                // Get the Cable Name Composite Attribute
                AttributeAccessCode accessCode = new AttributeAccessCode();
                long value =
                    port.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnFiberOptic,
                        accessCode,
                        0);

                if (!accessCode.IsOkay)
                {
                    return MediumType.Cu;
                }
                if ((value & 0x0000000400000000) != 0)
                {
                    return MediumType.POF;
                }
                return MediumType.FO;
            }

            Debug.Fail("Should never happen");
            return MediumType.Cu;
        }

        /// <summary>
        /// Gets port deactivation capability.
        /// </summary>
        /// <returns>'true' if attribute 'PNPortDeactivated' exists, otherwise 'false'.</returns>
        internal static bool GetPortDeactivationCapability(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();
            port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, accessCode, false);

            return accessCode.IsOkay;
        }

        /// <summary>
        /// Checks whether the PNIrtPortSyncDomainBoundary switching should be supported
        /// </summary>
        /// <returns></returns>
        internal static bool IsPNIrtPortSyncDomainBoundarySupported(DataModel.PCLObjects.Port port, int numberOfPorts)
        {
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(port);

            bool isFrameClass2Supported = false;
            bool isPNIrtSyncRoleExists = false;

            AttributeAccessCode accessCode = new AttributeAccessCode();

            // check sync role
            if (interfaceSubmodule != null)
            {
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtSyncRoleSupp,
                    accessCode, null);
                isPNIrtSyncRoleExists = accessCode.IsOkay;
            }

            // Get the Composite Attribute of supported Frame Classes
            accessCode.Reset();
            Enumerated suppFrameClasses =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    accessCode,
                    null);
            
            if (accessCode.IsOkay)
            {
                foreach (object fc in suppFrameClasses.List)
                {
                    PNIOFrameClass frameClass;
                    if (Enum.TryParse(fc.ToString(), out frameClass)
                        && (frameClass == PNIOFrameClass.Class2Frame))
                    {
                        isFrameClass2Supported = true;
                        break;
                    }
                }
            }

            bool isPNIrtSupportedSyncProtocolsExist =
                ConfigUtility.GetSuppSyncProtocols(interfaceSubmodule).Contains(PNIRTSupportedSyncProtocols.RTSync);

            return (numberOfPorts >= 2 && isPNIrtSyncRoleExists && isFrameClass2Supported &&
                    isPNIrtSupportedSyncProtocolsExist);
        }

        /// <summary>
        /// Get port name in the following format: "DeviceName\InterfaceName (Xn)\PortName (Xn Pm)".
        /// </summary>
        /// <returns>formatted name of port</returns>
        internal static string GetPortName(DataModel.PCLObjects.Port port)
        {
            if (port == null)
            {
                return "Any partner";
            }

            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(port);
            if (interfaceSubmodule != null)
            {
                StringBuilder sb = new StringBuilder();

                sb.Append(Utility.GetNameWithContainer(interfaceSubmodule));
                sb.Append(@"\");
                string portName = port.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.Name,
                    new AttributeAccessCode(),
                    string.Empty);
                sb.Append(portName);
                return sb.ToString();
            }
            return string.Empty;
        }

        internal static bool IsFiberOptic(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            port.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnFiberOptic,
                ac,
                0);
            return ac.IsOkay;
        }

        /// <summary>
        /// Gets port deactivation status.
        /// </summary>
        /// <param name="port"></param>
        /// <returns>'true' if deactivated otherwise 'false'.</returns>
        internal static bool IsPortDeactivated(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();
            bool portDeactivated = port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPortDeactivated,
                accessCode,
                false);

            return portDeactivated;
        }

        /// <summary>
        ///  ambigous:( there are cases where only Class3 is supported but no PTCP(in this case no Config block has to be
        /// generated)
        ///  but there are cases where PTCP is supported but only Class2, and in this case the Config block has to be
        /// generated!
        /// </summary>
        internal static bool LineLengthSupportedBetweenPorts(
            DataModel.PCLObjects.Port port1,
            DataModel.PCLObjects.Port port2)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            Interface interface1 = NavigationUtilities.GetInterfaceOfPort(port1);
            Enumerated interface1_PNIrtSupportedSyncProtocols =
                interface1.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    ac,
                    null);

            ac.Reset();

            Interface interface2 = NavigationUtilities.GetInterfaceOfPort(port2);
            Enumerated interface2_PNIrtSupportedSyncProtocols =
                interface2.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    ac,
                    null);

            bool isInterface2ContainsRTSync = interface2_PNIrtSupportedSyncProtocols?.List != null
                                              && interface2_PNIrtSupportedSyncProtocols.List.Any(
                                                  x => Convert.ToInt32(x, CultureInfo.InvariantCulture)
                                                       == (int)PNIRTSupportedSyncProtocols.RTSync);

            bool isInterface1ContainsRTSync = interface1_PNIrtSupportedSyncProtocols?.List != null
                                              && interface1_PNIrtSupportedSyncProtocols.List.Any(
                                                  x => Convert.ToInt32(x, CultureInfo.InvariantCulture)
                                                       == (int)PNIRTSupportedSyncProtocols.RTSync);

            return isInterface1ContainsRTSync && isInterface2ContainsRTSync;
        }
    }
}