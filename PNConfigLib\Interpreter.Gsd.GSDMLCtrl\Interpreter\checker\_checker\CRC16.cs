/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CRC16.cs                                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    class Crc16
    {
        private const ushort s_InitValue = 0;
        private const ushort s_XorValue = 0;
        private const uint s_Polynom = 0x4EAB;

        private ushort[] m_Crctable = null;
        private ushort m_Crcvalue = s_InitValue;

        // Generate a table for a byte-wise 16-bit CRC calculation on the polynomial:
        public Crc16()
        {
            /* build table */
            uint poly = s_Polynom;
            m_Crctable = new ushort[256];
            for (uint u = 0; u < m_Crctable.Length; u++)
            {
                uint c = u << 8;
                for (int i = 0; i < 8; i++)
                {
                    c = ((c & 0x8000) != 0) ? poly ^ (c << 1) : (c << 1);
                }
                m_Crctable[u] = (ushort)c;
            }
        }

        // call this when re-starting checksum calculation
        public void InitChecksum()
        {
            this.m_Crcvalue = s_InitValue;
        }

        // add one byte to the CRC
        public void Update(byte data)
        {
            this.m_Crcvalue = (ushort)((this.m_Crcvalue << 8) ^ this.m_Crctable[(this.m_Crcvalue >> 8) ^ data]);
        }


        // add a byte array to the CRC
        public void UpdateChecksum(byte[] data)
        {
            for (int i = 0; i < data.Length; i++)
            {
                Update(data[i]);
            }
        }


        // finish the checksum process, returning the CRC
        // do not call Update() or UpdateChecksum() after this!
        public ushort FinishChecksum(bool zeroToOne)
        {
            this.m_Crcvalue ^= s_XorValue;
            if (zeroToOne && (this.m_Crcvalue == 0))
                this.m_Crcvalue = 1;
            return this.m_Crcvalue;
        }

        // everything in one go: init, add byte array to CRC, finish and return CRC
        public ushort BlockChecksum(byte[] data, bool zeroToOne)
        {
            InitChecksum();
            UpdateChecksum(data);
            return FinishChecksum(zeroToOne);
        }
    }
}
