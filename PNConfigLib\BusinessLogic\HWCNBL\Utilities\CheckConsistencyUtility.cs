/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CheckConsistencyUtility.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class CheckConsistencyUtility
    {
        public static void CheckConsistency_All_Ports_Deactivated(Interface deviceInterface)
        {
            bool isSharedDeviceSupported =
                deviceInterface.ParentObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceSupported,
                    new AttributeAccessCode(),
                    false);

            bool isInterfaceAssigned = !isSharedDeviceSupported
                                       || (SharedDeviceUtility.GetSharedAccess(deviceInterface)
                                           == SharedIoAssignment.None);

            if (!isInterfaceAssigned)
            {
                // Shared device: In case of not assigned interface & ports, the consistency check should be ignored.
                return;
            }

            IList<DataModel.PCLObjects.Port> ports = deviceInterface.GetPorts();

            // At an IO-Device Interface there must be at least one activated port.
            if (ports.Count > 0)
            {
                bool onePortActive = false;

                foreach (DataModel.PCLObjects.Port port in ports)
                {
                    bool deactivationSupported =
                        port.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnPortDeactivationSupported,
                            new AttributeAccessCode(),
                            false);

                    if (!deactivationSupported)
                    {
                        onePortActive = true;
                        break;
                    }

                    bool portDeactivated =
                        port.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnPortDeactivated,
                            new AttributeAccessCode(),
                            false);

                    if (!portDeactivated)
                    {
                        onePortActive = true;
                        break;
                    }
                }

                if (!onePortActive)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.InterfaceSubmodulePortsDeactivated,
                        Utility.GetNameWithContainer(deviceInterface));
                }
            }
        }

        /// <summary>
        /// Checks if the interface has at least one port
        /// </summary>
        /// <param name="PNInterface"></param>
        internal static void CheckConsistencyNoPortForInterface(Interface pnInterface)
        {
            if (!pnInterface.GetPorts().Any())
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, pnInterface, ConsistencyConstants.Msg_NoPortForInterface);
            }
        }

        public static void CheckConsistencyModuleWithoutSubmodule(PclObject module)
        {
            // This consistency check is only made for modules,
            // i.e. headmodules (DecentralDevice) and Modules.
            if (!(module is Module || module is DecentralDevice))
            {
                Debug.Fail("This consistency check should only be registered for modules and headmodules.");
                return;
            }

            if (module.GetElements().Count == 0)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, module, ConsistencyConstants.ModuleWithoutSubmodule,
                        AttributeUtilities.GetName(module));
            }
        }

        public static void CheckConsistencyUpdateTime(PNIOD ioDevice)
        {
            if (ioDevice.IOSystem != null)
            {
                List<float> updateTimes = GetPossibleUpdateTimes(ioDevice);

                Interface interfaceSubmodule = ioDevice.ParentObject as Interface;
                if (updateTimes.Count == 0)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.NoUpdateTime,
                        AttributeUtilities.GetName(ioDevice.ParentObject));

                }
                else if (interfaceSubmodule != null)
                {
                    // Check whether the actual UpdateTime is valid.
                    float updateTime;

                    PNUpdateTimeMode updateTimeMode =
                        (PNUpdateTimeMode)
                        interfaceSubmodule.AttributeAccess.GetAnyAttribute<byte>(
                            InternalAttributeNames.PnUpdateTimeMode,
                            new AttributeAccessCode(),
                            0);

                    if (updateTimeMode == PNUpdateTimeMode.FixedTime)
                    {
                        long userAdjustedUpdateTime =
                            interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIoUserAdjustedUpdTime,
                                new AttributeAccessCode(),
                                0);
                        updateTime = userAdjustedUpdateTime * PNConstants.PNTimeBase;
                    }
                    else
                    {
                        updateTime = Utility.GetUpdateTimeOfIODevice(ioDevice);
                    }

                    IPNFrameData frameData = GetPNPlannerFrameDataOfIODevice(ioDevice);
                    if (!updateTimes.Contains(updateTime)
                        || ((frameData != null) && (frameData.DeviceLocalReductionRatio == 0)))
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                            interfaceSubmodule,
                            ConsistencyConstants.UpdateTimeNotApplicable,
                            AttributeUtilities.GetName(ioDevice.ParentObject));
                    }
                }
            }
        }

        public static bool CheckPDEVModelCompatibility(Interface controllerInterface, Interface deviceInterface)
        {
            if ((controllerInterface == null)
                || (deviceInterface == null))
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            uint pdevSupportedController =
                controllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPdevSupportedModel,
                    ac,
                    0);
            if (pdevSupportedController != 0)
            {
                return true;
            }

            ac.Reset();
            bool deviceSubmoduleModelSupp =
                deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSubmoduleModelSupp,
                    ac,
                    false);
            if (!deviceSubmoduleModelSupp)
            {
                return true;
            }

            return false;
        }
        internal static void ConsistencyCheck_NumberOfSubmodules(Interface interfaceSubmodule)
        {
            const int unlimitedSubmodules = -1;
            AttributeAccessCode ac = new AttributeAccessCode();
            var maxNumberOfSubmodules =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnIoNumberOfSubmodules,
                    ac, unlimitedSubmodules);

            if (ac.IsOkay && maxNumberOfSubmodules != unlimitedSubmodules)
            {
                int countSubmodules = 0;

                // headmodule
                var headModule = interfaceSubmodule.GetDevice() as DecentralDevice;
                var headSubmodules = PNNavigationUtility.GetHeadSubmodulesSorted(headModule);
                countSubmodules += headSubmodules.Count;

                List<PclObject> modules = PNNavigationUtility.GetModulesFromInterfaceSubmodule(interfaceSubmodule);
                foreach (PclObject module in modules)
                {
                    if (module as DecentralDevice == null)
                    {
                        var submodules = PNNavigationUtility.GetSubmodulesSorted(module);
                        countSubmodules += submodules.Count;
                    }
                }

                if (countSubmodules > maxNumberOfSubmodules)
                {
                    object[] errorParameters = new object[2];
                    errorParameters[0] = countSubmodules;
                    errorParameters[1] = maxNumberOfSubmodules;

                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                        interfaceSubmodule,
                        ConsistencyConstants.ErrorTooManySubmodules,
                        errorParameters);
                }
            }
        }
        public static void ConsistencyCheck_DeviceExchangeWithoutMMC(Interface deviceInterface)
        {
            bool pnIoNameOfStationNotTransferable =
                deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoNameOfStationNotTransferable,
                    new AttributeAccessCode(),
                    false);

            // Device doesn't have a storage medium, check conditions of
            // device exchange is needed.
            if (pnIoNameOfStationNotTransferable)
            {
                // Get controller interface submodule.
                Interface controllerInterface = deviceInterface.PNIOD.AssignedController.ParentObject as Interface;

                if (controllerInterface != null)
                {
                    // Check if device exchange without MMC is activated.
                    uint deviceExchangeWithoutMMC =
                        controllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIoExchangeWithoutMMC,
                            new AttributeAccessCode(),
                            0);

                    // Check rule 1
                    if (deviceExchangeWithoutMMC > 0)
                    {
                        // Check rule 2
                        bool LLDPSupported = false;

                        // Check whether the current interface supports LLDP.
                        bool currentInterfaceLLDPSupported =
                            deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnLLDPSupported,
                                new AttributeAccessCode(),
                                false);

                        if (currentInterfaceLLDPSupported)
                        {
                            // Check whether one of the neighbours supports LLDP.
                            List<Interface> interconnectedInterfaces =
                                PNNavigationUtility.GetAccessiblePortInterconnectedInterfaces(deviceInterface);

                            // Check rule 3a
                            foreach (Interface interconnectedInterface in interconnectedInterfaces)
                            {
                                if (interconnectedInterface.GetHashCode() != deviceInterface.GetHashCode())
                                {
                                    bool interconnectedInterfaceLLDPSupported =
                                        interconnectedInterface.AttributeAccess.GetAnyAttribute<bool>(
                                            InternalAttributeNames.PnLLDPSupported,
                                            new AttributeAccessCode(),
                                            false);

                                    if (interconnectedInterfaceLLDPSupported)
                                    {
                                        LLDPSupported = true;
                                        break;
                                    }
                                }
                            }

                            // Check rule 3b
                            if (!LLDPSupported
                                && interconnectedInterfaces.Contains(controllerInterface))
                            {
                                LLDPSupported = true;
                            }

                            if (!LLDPSupported)
                            {
                                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning,
                                    deviceInterface,
                                    ConsistencyConstants.DeviceExchangeWithoutMMC,
                                    AttributeUtilities.GetName(deviceInterface.GetDevice()));
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Checks the count of the forwarded frames that are part of the PDIRFrameData and compares it with the
        /// maximum supported value of a given interface submodule.
        /// </summary>
        internal static void CheckMaxNoIrFrameData(Interface interfaceSubmodule, SyncDomainBusinessLogic syncDomainBusinessLogic)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            Enumerated pnIrtForwardingMode = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtForwardingMode, ac, null);
            PNIrtForwardingMode currForwardingMode = ac.IsOkay
                                     ? (PNIrtForwardingMode)pnIrtForwardingMode.DefaultValue
                                     : PNIrtForwardingMode.None;

            if (currForwardingMode == PNIrtForwardingMode.Relative)
            {
                // Relative forwarders don't have any forwarded frame in their PDIRFrameData.
                return;
            }

            int maxNumIrData =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIrtMaxNoIrFrameData,
                    ac.GetNew(),
                    128);
            List<IPNPlannerOutputFrame> frameBlocks = (List<IPNPlannerOutputFrame>)syncDomainBusinessLogic.GetConfig2008FrameBlocks(interfaceSubmodule);
            if (frameBlocks.Count < maxNumIrData)
            {
                // Don't need to check each frame block to understand if it is a forwarded frame
                return;
            }

            int numForwardedFrames = 0;
            foreach (IPNPlannerOutputFrame frameBlock in frameBlocks)
            {
                // Signs of a local frame or the current number of the forwarded frames is supported
                if ((frameBlock.RxPort == 0)
                    || (frameBlock.Local == 1)
                    || (++numForwardedFrames <= maxNumIrData))
                {
                    continue;
                }

                // Create the error message.
                string name = AttributeUtilities.GetSubmoduleNameWithContainer(interfaceSubmodule);
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.MaxNoIrFrameDataExceeded,
                        name,
                        maxNumIrData);
            }
        }

        internal static void ConsistencyCheck_CyclicBandwidth(
            Interface controllerInterfaceSubmodule,
            SyncDomainBusinessLogic syncDomain,
            long rtBandwidth,
            long irtBandwidth)
        {
            if (syncDomain == null)
            {
                return;
            }
            //SendClockFactor
            long pnIoSendClockFactor = AttributeUtilities.GetTransientPNSendClockFactor(
                controllerInterfaceSubmodule,
                32);

            if (pnIoSendClockFactor == -1)
            {
                pnIoSendClockFactor = 32;
            }

            long pnMaxCyclicDataBw = Utility.GetMaxIoCyclicBandwidth(pnIoSendClockFactor, syncDomain);
            long calculatedBandwidthForCyclic = rtBandwidth + irtBandwidth;
            // Check the conditions for Cyclic Bandwidth.
            if (calculatedBandwidthForCyclic > pnMaxCyclicDataBw)
            {
                // Calculated cyclic bandwidth exceeds entered max. cyclic bandwidth. Display an error.
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    syncDomain.SyncDomain,
                    ConsistencyConstants.CalculatedBandwidthExceededMaximum,
                        AttributeUtilities.GetName(controllerInterfaceSubmodule.ParentObject));
            }

            if (calculatedBandwidthForCyclic <= syncDomain.MaxPossibleBandwidthForCyclic)
            {
                return;
            }

            // Calculated cyclic bandwidth exceeds entered max. cyclic bandwidth. Display an error.    
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                syncDomain.SyncDomain,
                ConsistencyConstants.CalculatedTotalBandwidthExceededMaximum,
                AttributeUtilities.GetName(controllerInterfaceSubmodule.ParentObject));
        }

        internal static void ConsistencyCheck_CentralDataLengthSubmoduleLength(Interface controllerInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule == null)
            {
                return;
            }

            ConsistencyCheck_CentralK4(controllerInterfaceSubmodule);

            //count the local virtual submodules
            int subModuleCount = 0;

            //count of proxies (required for plus hw)
            int proxyCount = 0;

            //create parameter object with the needed data
            //create an input and an output size for all pips
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput =
                ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();

            // Get the IOController IOSystem connector of the controllerInterfaceSubmodule
            PNIOC ioController = controllerInterfaceSubmodule.PNIOC;

            List<PNIOD> devicesOfIOController = NavigationUtilities.GetIoDevicesOfIoController(ioController);

            if (devicesOfIOController == null)
            {
                return;
            }
            bool ctrlSharedDeviceAssignmentSupp = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoSharedDeviceSupported,
                new AttributeAccessCode(),
                false);
            Dictionary<PclObject, int> sseList = new Dictionary<PclObject, int>();

            long syncSlaveCounter = 0;

            // Pots for shared Provider / Consumer
            int maxSharedRTC1Provider = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTC1Provider, new AttributeAccessCode(), -1);
            int maxSharedRTC1Consumer = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTC1Consumer, new AttributeAccessCode(), -1);
            int maxSharedRTCxProvider = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTCxProvider, new AttributeAccessCode(), -1);
            int maxSharedRTCxConsumer = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTCxConsumer, new AttributeAccessCode(), -1);
            int maxSharedRTC3Provider = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTC3Provider, new AttributeAccessCode(), -1);
            int maxSharedRTC3Consumer = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>
                (PNConstants.PNIOMaxSharedRTC3Consumer, new AttributeAccessCode(), -1);

            bool checkForRtcSharedResourceLimits =
                maxSharedRTC1Provider != -1 || maxSharedRTC1Consumer != -1 ||
                maxSharedRTC3Provider != -1 || maxSharedRTC3Consumer != -1 ||
                maxSharedRTCxProvider != -1 || maxSharedRTCxConsumer != -1;

            // Counters for RTC Shared Resources
            int countOfRtc1ProviderCRs = 0;
            int countOfRtc1ConsumerCRs = 0;
            int countOfRtcxProviderCRs = 0;
            int countOfRtcxConsumerCRs = 0;
            int countOfRtc3ProviderCRs = 0;
            int countOfRtc3ConsumerCRs = 0;


            foreach (PNIOD deviceCore in devicesOfIOController)
            {
                Interface deviceInterface = deviceCore.GetInterface();

                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(deviceCore);
                if (syncRole != PNIRTSyncRole.NotSynchronized)
                {
                    syncSlaveCounter++;
                }
                bool sharedDeviceSupported = false;
                if (ctrlSharedDeviceAssignmentSupp)
                {
                    deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoSharedDeviceSupported,
                        new AttributeAccessCode(),
                        false);
                    paramObjectRefreshPNPlannerInput.SharedDeviceSupported = sharedDeviceSupported;
                }

                paramObjectRefreshPNPlannerInput.SharedDeviceSupported = sharedDeviceSupported;
                IList<PclObject> modules = PNNavigationUtility.GetModulesFromInterfaceSubmodule(deviceInterface);

                paramObjectRefreshPNPlannerInput.DiscardIOXS = !Utility.GetIOXSRequired(deviceCore.GetInterface());

                foreach (PclObject module in modules)
                {
                    Utility.GetAddressLengthOfDeviceItem(module, paramObjectRefreshPNPlannerInput, 1, 1);
                    int length = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIoMaxDeviceSubmoduleDataLength,
                        new AttributeAccessCode(),
                        0);
                    Utility.GetSubmoduleCountOfDeviceItem(
                        module,
                        ref subModuleCount,
                        ref proxyCount,
                        sharedDeviceSupported,
                        sseList,
                        length);
                }
                //submodule and proxy numbers should be added and propagated
                if (checkForRtcSharedResourceLimits)
                {
                    CountSharedRtcResources(deviceCore, deviceInterface,
                        controllerInterfaceSubmodule,
                        ref countOfRtc1ProviderCRs, ref countOfRtc1ConsumerCRs,
                        ref countOfRtc3ProviderCRs, ref countOfRtc3ConsumerCRs,
                        ref countOfRtcxProviderCRs, ref countOfRtcxConsumerCRs);
                }
            }

            if (checkForRtcSharedResourceLimits)
            {

                // Check if any RTC resource count is higher that its max value.
                PclObject device = NavigationUtilities.GetContainer(controllerInterfaceSubmodule);
                string deviceName = device.AttributeAccess.GetAnyAttribute<string>(InternalAttributeNames.Name,
                    new AttributeAccessCode(), string.Empty);

                object[] errorParameters = new object[3];
                errorParameters[1] = String.Concat(deviceName, "\\", AttributeUtilities.GetName(controllerInterfaceSubmodule));

                if (maxSharedRTC1Provider >= 0 && countOfRtc1ProviderCRs > maxSharedRTC1Provider)
                {
                    errorParameters[2] = maxSharedRTC1Provider;
                    errorParameters[0] = countOfRtc1ProviderCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTC1Provider,
                        errorParameters);
                }
                if (maxSharedRTC1Consumer >= 0 && countOfRtc1ConsumerCRs > maxSharedRTC1Consumer)
                {
                    errorParameters[2] = maxSharedRTC1Consumer;
                    errorParameters[0] = countOfRtc1ConsumerCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTC1Consumer,
                        errorParameters);
                }
                if (maxSharedRTC3Provider >= 0 && countOfRtc3ProviderCRs > maxSharedRTC3Provider)
                {
                    errorParameters[2] = maxSharedRTC3Provider;
                    errorParameters[0] = countOfRtc3ProviderCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTC3Provider,
                        errorParameters);
                }
                if (maxSharedRTC3Consumer >= 0 && countOfRtc3ConsumerCRs > maxSharedRTC3Consumer)
                {
                    errorParameters[2] = maxSharedRTC3Consumer;
                    errorParameters[0] = countOfRtc3ConsumerCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTC3Consumer,
                        errorParameters);
                }
                if (maxSharedRTCxProvider >= 0 && countOfRtcxProviderCRs > maxSharedRTCxProvider)
                {
                    errorParameters[2] = maxSharedRTCxProvider;
                    errorParameters[0] = countOfRtcxProviderCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTCXProvider,
                        errorParameters);
                }
                if (maxSharedRTCxConsumer >= 0 && countOfRtcxConsumerCRs > maxSharedRTCxConsumer)
                {
                    errorParameters[2] = maxSharedRTCxConsumer;
                    errorParameters[0] = countOfRtcxConsumerCRs;
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        controllerInterfaceSubmodule,
                        ConsistencyConstants.ControllerErrorMaxSharedRTCXConsumer,
                        errorParameters);
                }
            }
            List<long> frameLengths = new List<long>
                                          {
                                              paramObjectRefreshPNPlannerInput.InputNetFrameLength,
                                              paramObjectRefreshPNPlannerInput.OutputNetFrameLength
                                          };

            Utility.CheckSyncSlaveNumber(controllerInterfaceSubmodule, syncSlaveCounter);

            Utility.LogAddressesErrors(frameLengths, controllerInterfaceSubmodule);

            AttributeAccessCode acSubmodules = new AttributeAccessCode();
            int pnIoMaxDeviceSubmodules = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>(
                InternalAttributeNames.PnIoMaxDeviceSubmodules,
                acSubmodules,
                8192);

            if (acSubmodules.IsOkay
                && (pnIoMaxDeviceSubmodules < subModuleCount + proxyCount))
            {
                object[] errorParameters = new object[2];
                errorParameters[0] = pnIoMaxDeviceSubmodules;
                errorParameters[1] = subModuleCount + proxyCount - pnIoMaxDeviceSubmodules;
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    controllerInterfaceSubmodule,
                    ConsistencyConstants.ControllerErrorMaxSubmoduleCount,
                    errorParameters);
            }

            if (sseList.Count > 0)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                foreach (KeyValuePair<PclObject, int> sse in sseList)
                {
                    PclObject submodule = sse.Key;
                    string submoduleName = submodule.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.Name,
                        ac.GetNew(),
                        String.Empty);
                    int length = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIoMaxDeviceSubmoduleDataLength,
                        new AttributeAccessCode(),
                        0);

                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        submodule,
                        ConsistencyConstants.ControllerErrorMaxSubmoduleDataLength,
                        length,
                        submoduleName,
                        sse.Value);
                }
            }
        }

        private static void CountSharedRtcResources(
            PNIOD device,
            Interface deviceInterface,
            Interface controllerInterfaceSubmodule,
            ref int countOfRtc1ProviderCRs,
            ref int countOfRtc1ConsumerCRs,
            ref int countOfRtc3ProviderCRs,
            ref int countOfRtc3ConsumerCRs,
            ref int countOfRtcxProviderCRs,
            ref int countOfRtcxConsumerCRs)
        {
            // List of Frames to check Input/Output CR Number for shared Resources
            List<IPNFrameData> listOfFrames =
                NavigationUtilities.GetPNFrameDataListOfIODevice(device, true, controllerInterfaceSubmodule);
            int inputFrameCount = 0;
            int outputFrameCount = 0;
            AttributeAccessCode ac = new AttributeAccessCode();
            GetInputOutputCrCounts(listOfFrames, ref inputFrameCount, ref outputFrameCount);
            // Is a RT Device
            if ((PNIRTSyncRole)device.AttributeAccess.GetAnyAttribute<byte>(InternalAttributeNames.PnIrtSyncRole, ac, 0)
                == PNIRTSyncRole.NotSynchronized)
            {
                countOfRtc1ProviderCRs += inputFrameCount;
                countOfRtc1ConsumerCRs += outputFrameCount;
                countOfRtcxProviderCRs += inputFrameCount;
                countOfRtcxConsumerCRs += outputFrameCount;
            }

            List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(deviceInterface);
            // is an IRT Device          
            if ((PNIRTSyncRole)device.AttributeAccess.GetAnyAttribute<byte>(InternalAttributeNames.PnIrtSyncRole, ac, 0)
                != PNIRTSyncRole.NotSynchronized)
            {
                // Has only IRT Legacy Startup Mode
                if (!startupModes.Contains(PNIrtArStartupMode.Advanced))
                {
                    countOfRtc1ProviderCRs++;
                    countOfRtc1ConsumerCRs++;
                    countOfRtc3ProviderCRs++;
                    countOfRtc3ConsumerCRs++;
                    countOfRtcxProviderCRs += 2;
                    countOfRtcxConsumerCRs += 2;
                }
                else // IsAdvanced StartupMode
                {
                    countOfRtcxProviderCRs += inputFrameCount;
                    countOfRtcxConsumerCRs += outputFrameCount;
                    countOfRtc3ProviderCRs += inputFrameCount;
                    countOfRtc3ConsumerCRs += outputFrameCount;
                }
            }
        }

        private static void GetInputOutputCrCounts(List<IPNFrameData> listOfFrames,
                                                   ref int countOfInputRtc1CRs, ref int countOfOutputRtc1CRs)
        {
            foreach (IPNFrameData frameData in listOfFrames)
            {
                if (frameData.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame)
                {
                    countOfOutputRtc1CRs++;
                }
                else
                {
                    countOfInputRtc1CRs++;
                }
            }
        }
        private static void ConsistencyCheck_CentralK4(Interface controllerInterfaceSubmodule)
        {
            //Check if MaxDevices is exceeded Consistency Check K4:
            //FS PN-IO Chapter	"PROFINET IO controller on the PN interface of a CPU/CP"
            AttributeAccessCode acMaxDevices = new AttributeAccessCode();
            int pnIoMaxDevices = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>(
                InternalAttributeNames.PnIoMaxDevices, acMaxDevices, PNConstants.DefaultPNMaxDevices);
            // Negative values represent "unlimited"
            if (pnIoMaxDevices >= 0)
            {
                PNIOC ioController = controllerInterfaceSubmodule.PNIOC;
                List<PNIOD> ioDevices = NavigationUtilities.GetIoDevicesOfIoController(ioController);
                int maxCount = Math.Max(ioDevices.Count, ioDevices.Count);
                //if there is a local virtual device, it has to be added to the number of decentral devices

                Utility.CountLocalVirtualDevices(
                    controllerInterfaceSubmodule,
                    acMaxDevices,
                    pnIoMaxDevices, maxCount);
            }
        }
        internal static void ConsistencyCheck_UpdateTimeWatchDog(PNIOD ioDevice)
        {
            IPNFrameData frameData = GetPNPlannerFrameDataOfIODevice(ioDevice);

            if (frameData == null)
            {
                return;
            }

            bool max_WatchDogTime_Consistency_Error = false;

            //Check 1.1: Check Watchdog limit
            float updateTimeMs = frameData.DeviceLocalReductionRatio * frameData.SendClockFactor
                                                                     * PNConstants.PNTimeBase;

            if (updateTimeMs * frameData.WatchdogFactor > PNConstants.MaxAllowedWatchdogTime)
            {
                max_WatchDogTime_Consistency_Error = true;
            }

            //Check 1.2: Check Watchdog limit in adaptaion case (with LocalWatchdogFactor in LocalSCFAdaptionEntry)
            //if the controller interface supports uneven sendclock adaptation
            Interface controllerInterface = ioDevice.IOSystem.PNIOC.ParentObject as Interface;

            bool pnIoScfAdaptionNonPow2Supported = (controllerInterface != null)
                                                   && controllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                                                       InternalAttributeNames.PnIoScfAdaptionNonPow2Supported,
                                                       new AttributeAccessCode(),
                                                       false);

            if (pnIoScfAdaptionNonPow2Supported)
            {
                long controllerSendClockFactor =
                    controllerInterface.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnSendClockFactorTransient,
                        new AttributeAccessCode(),
                        0);

                if (controllerSendClockFactor != frameData.SendClockFactor)
                {
                    int localWatchDogFactor = Utility.CalcLocalWatchDogFactor(frameData, controllerSendClockFactor);

                    if (controllerSendClockFactor * frameData.ControllerLocalReductionRatio * PNConstants.PNTimeBase
                        * localWatchDogFactor > PNConstants.MaxAllowedWatchdogTime)
                    {
                        max_WatchDogTime_Consistency_Error = true;
                    }
                }
            }

            if (max_WatchDogTime_Consistency_Error)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    ioDevice.ParentObject,
                    ConsistencyConstants.MaxWatchdog,
                    AttributeUtilities.GetName(ioDevice.GetDevice()),
                    PNConstants.MaxAllowedWatchdogTime);
            }

            //Determine the controller sendclock factor
            bool isoModeActive =
                ioDevice.ParentObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsochron,
                    new AttributeAccessCode(),
                    false);
            PNUpdateTimeMode updateTimeMode =
                (PNUpdateTimeMode)
                ioDevice.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnUpdateTimeMode,
                    new AttributeAccessCode(),
                    0);

            //Only if interface is in isochron mode and update time mode is not automatic
            if (isoModeActive && (updateTimeMode == PNUpdateTimeMode.FixedTime))
            {
                if (controllerInterface != null)
                {
                    long attUpdtime =
                        ioDevice.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoUserAdjustedUpdTime,
                            new AttributeAccessCode(),
                            0);

                    long scf =
                        controllerInterface.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            new AttributeAccessCode(),
                            0);

                    if (attUpdtime != scf)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                            ioDevice.ParentObject,
                            ConsistencyConstants.UpdateTimeNotEqualSendClock);
                    }
                }
            }

            // Check if watchdog factor is valid
            List<long> possibleWatchdogFactors = Utility.GetWatchdogFactors(ioDevice.ParentObject as Interface);

            if (!possibleWatchdogFactors.Contains(frameData.WatchdogFactor))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    ioDevice.ParentObject,
                    ConsistencyConstants.InvalidWatchdogFactor,
                    frameData.WatchdogFactor);
            }
        }

        internal static IPNFrameData GetPNPlannerFrameDataOfIODevice(PNIOD ioDevice)
        {
            List<IPNFrameData> frameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(ioDevice, false);

            if ((frameDataList != null)
                && (frameDataList.Count > 0))
            {
                return frameDataList[0];
            }

            return null;
        }
        internal static void IsActivateDcpReadOnlyConfigurable(Interface deviceInterface)
        {
            bool pnDcpReadOnlyActivated = deviceInterface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.ActivateDcpReadOnly,
                new AttributeAccessCode(),
                false);

            Interface controllerInterface = NavigationUtilities.GetControllerOfDevice(deviceInterface);
            bool pnControllerCompetible = controllerInterface != null
                                          && controllerInterface.AttributeAccess.GetAnyAttribute(
                                              InternalAttributeNames.PnDCPReadOnlySupported,
                                              new AttributeAccessCode(),
                                              false);

            if (pnDcpReadOnlyActivated && !pnControllerCompetible)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    deviceInterface,
                    ConsistencyConstants.ActivateDcpReadOnlyConfigurable,
                    deviceInterface.ParentObject.Id,
                    deviceInterface.Id,
                    controllerInterface.Id);
            }
        }

        private static List<float> GetPossibleUpdateTimes(PNIOD ioDevice)
        {
            List<float> possibleUpdateTimes = new List<float>();

            IPNFrameData frameData = GetPNPlannerFrameDataOfIODevice(ioDevice);

            if (frameData != null)
            {
                foreach (long reductionRatio in frameData.PossibleReductionRatios)
                {
                    float updateTime = PNConstants.PNTimeBase * Convert.ToSingle(
                                           reductionRatio * frameData.SendClockFactor,
                                           CultureInfo.InvariantCulture);

                    possibleUpdateTimes.Add(updateTime);
                }
            }

            return possibleUpdateTimes;
        }
    }
}