/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PdNrtFeedInLoadLimitationStruct.cs        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.DataRecordsConf
{
    /// <summary>
    /// The data record object for PDNRTFeedInLoadLimitation.
    /// </summary>
    internal class PdNrtFeedInLoadLimitationStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for PDNRTFeedInLoadLimitationStruct.
        /// </summary>
        public PdNrtFeedInLoadLimitationStruct()
        {
            BlockType = DataRecords.BlockTypes.NRT_LOAD_LIMIT;
            BlockLength = 0x0008;
            Data = new byte[4];
        }

        /// <summary>
        /// IO_Configured part of the data record.
        /// </summary>
        public int IO_Configured
        {
            set { BufferManager.Write8(Data, 1, value); }
            get { return BufferManager.Read8(Data, 1); }
        }

        /// <summary>
        /// LoadLimiationActive part of the data record.
        /// </summary>
        public int LoadLimitationActive
        {
            set { BufferManager.Write8(Data, 0, value); }
            get { return BufferManager.Read8(Data, 0); }
        }
    }
}