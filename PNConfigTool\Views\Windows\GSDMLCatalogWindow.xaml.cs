using System;
using System.Windows;
using PNConfigTool.Services;
using PNConfigTool.ViewModels;

namespace PNConfigTool.Views.Windows
{
    /// <summary>
    /// GSDMLCatalogWindow.xaml 的交互逻辑
    /// </summary>
    public partial class GSDMLCatalogWindow : Window
    {
        private readonly IGSDMLService _gsdmlService;

        /// <summary>
        /// 默认构造函数（仅用于设计时）
        /// </summary>
        public GSDMLCatalogWindow()
        {
            InitializeComponent();
            // 从服务定位器获取服务实例以利用单例缓存
            _gsdmlService = ServiceLocator.GetService<IGSDMLService>();
            DataContext = new GSDMLCatalogViewModel(_gsdmlService);
        }

        /// <summary>
        /// 带服务的构造函数（运行时使用）
        /// </summary>
        /// <param name="gsdmlService">GSDML服务实例</param>
        public GSDMLCatalogWindow(IGSDMLService gsdmlService)
        {
            InitializeComponent();
            _gsdmlService = gsdmlService ?? throw new ArgumentNullException(nameof(gsdmlService));
            DataContext = new GSDMLCatalogViewModel(_gsdmlService);
        }

        /// <summary>
        /// 关闭窗口按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
} 