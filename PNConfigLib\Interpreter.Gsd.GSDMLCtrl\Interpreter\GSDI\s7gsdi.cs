/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: s7gsdi.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.Gsd.Interpreter.Common;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace GSDI
{
    /// <summary>This enum defines kind of model is created by the GSDML 
    /// interpreter.
    /// </summary>
    public enum ModelOptions
    {
        /// <summary>
        /// No model
        /// </summary>
	    GSDNoModel = 0,	// NoModel

        /// <summary>
        /// Common and structure model
        /// </summary>
	    GSDCommonAndStructure = 1,	// CommonAndStructure

        /// <summary>
        /// Structure model
        /// </summary>
	    GSDStructure = 2,	// Structure

        /// <summary>
        /// Common model
        /// </summary>
	    GSDCommon = 3		// Common
    }

    /// <summary>This enum defines the different types of objects which can appear in
    /// GSDML files
    /// </summary>
    public enum ModuleTypes
    {
        /// <summary>
        /// No module type
        /// </summary>
	    GSDNoModuleType = 0, // ---

        /// <summary>
        /// DeviceAccessPoint type
        /// </summary>
	    GSDDeviceAccessPoint = 1,   // DeviceAccessPoint
        //GSD_SlaveInterfaceModule	= 2, // SlaveInterfaceModule

        /// <summary>
        /// Module type
        /// </summary>
        GSDModule = 3, // Module

        /// <summary>
        /// VirtualSubmodule type
        /// </summary>
	    GSDVirtualSubmodule = 6     // VirtualSubmodule
        //GSD_PhysicalSubmodule			= 7		// PhysicalSubmodule
    }


    public enum SupportedAccess
    {
        GSD_SupportedAccess_Read = 0,
        GSD_SupportedAccess_Write = 1,
        GSD_SupportedAccess_Prm = 2
    };

    /// <summary>This enum defines the categories an IO device can get assigned to</summary>
    public enum MainFamilies
    {
        /// <summary>
        /// General category
        /// </summary>
        GSDMfGeneral = 0,	// General

        /// <summary>
        /// Drives category
        /// </summary>
        GSDMfDrives = 1,	// Drives

        /// <summary>
        /// Switching devices category
        /// </summary>
        GSDMfSwitchingDevices = 2,	// Switching Devices

        /// <summary>
        /// IO Category
        /// </summary>
        GsdmfIO = 3,	// I/O

        /// <summary>
        /// Valves category
        /// </summary>
        GSDMfValves = 4,	// Valves

        /// <summary>
        /// Controllers category
        /// </summary>
        GSDMfControllers = 5,	// Controllers

        /// <summary>
        /// HMI Category
        /// </summary>
        GSDMfHmi = 6,	// HMI

        /// <summary>
        /// Encoders category
        /// </summary>
        GSDMfEncoders = 7,	// Encoders

        /// <summary>
        /// NC/RC category
        /// </summary>
        GSDMfNcRc = 8,	// NC/RC

        /// <summary>
        /// Gateway category
        /// </summary>
        GSDMfGateway = 9,	// Gateway

        /// <summary>
        /// PLC category
        /// </summary>
        GSDMfPlCs = 10,	// PLCs

        /// <summary>
        /// Ident systems category
        /// </summary>
        GSDMfIdentSystems = 11,	// Ident Systems

        /// <summary>
        /// PA profiles category
        /// </summary>
        GSDMfPaProfiles = 12,	// PA Profiles

        /// <summary>
        /// Network components category
        /// </summary>
        GSDMfNetworkComponents = 13,	// Network Components (new in V2.0)

        /// <summary>
        /// Sensors category
        /// </summary>
        GSDMfSensors = 14,	// Sensors (new in V2.0)

        /// <summary>
        /// Preconfigured Stations category
        /// </summary>
        GSDMfPreconfiguredStation = 15	// Preconfigured Station
    }

    public enum PADeviceClasses
    {
        GSD_PADeviceClass_General = 0,
        GSD_PADeviceClass_ProcessControlDevice = 1
    };

    /// <summary>This enum defines the different types of systemdefined submodules which
    /// can be found in an GSDML file.</summary>
    public enum SystemDefinedSubmoduleTypes
    {
        /// <summary>
        /// All types
        /// </summary>
	    GSDAll = 0,	// All system defined submodules

        /// <summary>
        /// Interface type
        /// </summary>
	    GSDInterface = 1,	// internal interface system defined submodules

        /// <summary>
        /// Port type
        /// </summary>
	    GSDPort = 2		// Port system defined submodules
    }

    /// <summary></summary>
    public enum IOConsistencies
    {
        /// <summary>
        /// Item consistency
        /// </summary>
	    GSDType = 0,

        /// <summary>
        /// All items consistency
        /// </summary>
	    GSDComplete = 1
    }

    /// <summary></summary>
    public enum DataItemTypes
    {
        None = 0,
        GSDDtInteger8 = 2,	// Integer8
        GSDDtInteger16 = 3,	// Integer16
        GSDDtInteger32 = 4,	// Integer32
        GSDDtInteger64 = 55,	// Integer64
        GSDDtUnsigned8 = 5,	// Unsigned8
        GSDDtUnsigned16 = 6,	// Unsigned16
        GSDDtUnsigned32 = 7,	// Unsigned32
        GSDDtUnsigned64 = 56,	// Unsigned64
        GSDDtFloat32 = 8,	// Float32
        GSDDtFloat64 = 15,	// Float64
        GSDDtDate = 50,	// Date
        GSDDtTimeOfDayWithDateIndication = 12,	// TimeOfDay with date indication
        GSDDtTimeOfDayWithoutDateIndication = 52,	// TimeOfDay without date indication
        GSDDtTimeDifferenceWithDateIndication = 53,	// TimeDifference with date indication
        GSDDtTimeDifferenceWithoutDateIndication = 54,	// TimeDifference without date indication
        GSDDtNetworkTime = 58,	// NetworkTime
        GSDDtNetworkTimeDifference = 59,	// NetworkTimeDifference
        GSDDtVisibleString = 9,	// VisibleString
        GSDDtOctetString = 10,	// OctetString
        GSDDtFloat32Unsigned8 = 101,	// Float32+Unsigned8 (new in V2.0)
        GsddtFMessageTrailer4Byte = 110,	// F_MessageTrailer4Byte (new in V2.0)
        GsddtFMessageTrailer5Byte = 111,	// F_MessageTrailer5Byte (new in V2.0)
        GSDDtUnsigned8Unsigned8 = 102,	// Unsigned8+Unsigned8 (new in V2.2)
        GSDDtBoolean = 1,	// Boolean (new in V2.32)
        GSDDtUnicodeString8 = 40,	// UnicodeString8 (new in V2.32)
        GSDDtString61131 = 41,	// 61131_STRING (new in V2.32)
        GSDDtWstring61131 = 42,	// 61131_WSTRING (new in V2.32)
        GSDDtTimeStamp = 60,	// TimeStamp (new in V2.32)
        GSDDtTimeStampDifference = 61,	// TimeStampDifference (new in V2.32)
        GSDDtTimeStampDifferenceShort = 62,	// TimeStampDifferenceShort (new in V2.32)
        GSDDtOctetString2Unsigned8 = 103,	// OctetString2+Unsigned8 (new in V2.32)
        GSDDtUnsigned16S = 104,	// Unsigned16_S (new in V2.32)
        GSDDtInteger16S = 105,	// Integer16_S (new in V2.32)
        GSDDtUnsigned8S = 106,	// Unsigned8_S (new in V2.32)
        GSDDtOctetStringS = 107,	// OctetString_S (new in V2.32)
        GsddtN2 = 113,	// N2 (new in V2.32)
        GsddtN4 = 114,	// N4 (new in V2.32)
        GsddtV2 = 115,	// V2 (new in V2.32)
        GsddtL2 = 116,	// L2 (new in V2.32)
        GsddtR2 = 117,	// R2 (new in V2.32)
        GsddtT2 = 118,	// T2 (new in V2.32)
        GsddtT4 = 119,	// T4 (new in V2.32)
        GsddtD2 = 120,	// D2 (new in V2.32)
        GsddtE2 = 121,	// E2 (new in V2.32)
        GsddtC4 = 122,	// C4 (new in V2.32)
        GsddtX2 = 123,	// X2 (new in V2.32)
        GsddtX4 = 124,	// X4 (new in V2.32)
        GSDDtUnipolar216 = 125	// Unipolar2.16 (new in V2.32)
    }

    /// <summary></summary>
    public enum DataTypes
    {
        /// <summary>None</summary>
        None = 0,
        GSDBit = 254,	// Bit
        GSDBitArea = 255,	// BitArea
        GSDInteger8 = 2,	// Integer8
        GSDInteger16 = 3,	// Integer16
        GSDInteger32 = 4,	// Integer32
        GSDInteger64 = 55,	// Integer64
        GSDUnsigned8 = 5,	// Unsigned8
        GSDUnsigned16 = 6,	// Unsigned16
        GSDUnsigned32 = 7,	// Unsigned32
        GSDUnsigned64 = 56,	// Unsigned64
        GSDDate = 50,	// Date
        GSDFloat32 = 8,	// Float32
        GSDFloat64 = 15,	// Float64
        GSDTimeOfDayWithDateIndication = 12,	// TimeOfDay with date indication
        GSDTimeOfDayWithoutDateIndication = 52,	// TimeOfDay without date indication
        GSDTimeDifferenceWithDateIndication = 53,	// TimeDifference with date indication
        GSDTimeDifferenceWithoutDateIndication = 54,	// TimeDifference without date indication
        GSDNetworkTime = 58,	// NetworkTime
        GSDNetworkTimeDifference = 59,	// NetworkTimeDifference
        GSDVisibleString = 9,	// VisibleString
        GSDOctetString = 10,	// OctetString
        GSDFloat32Unsigned8 = 101,	// Float32+Unsigned8 (new in V2.0)
        GsdfMessageTrailer4Byte = 110,	// F_MessageTrailer4Byte (new in V2.0)
        GsdfMessageTrailer5Byte = 111,	// F_MessageTrailer5Byte (new in V2.0)
        GSDUnsigned8Unsigned8 = 102,	// Unsigned8+Unsigned8 (new in V2.2)
        GSDBoolean = 1,	// Boolean (new in V2.32)
        GSDUnicodeString8 = 40,	// UnicodeString8 (new in V2.32)
        GSDString61131 = 41,	// 61131_STRING (new in V2.32)
        GSDWstring61131 = 42,	// 61131_WSTRING (new in V2.32)
        GSDTimeStamp = 60,	// TimeStamp (new in V2.32)
        GSDTimeStampDifference = 61,	// TimeStampDifference (new in V2.32)
        GSDTimeStampDifferenceShort = 62,	// TimeStampDifferenceShort (new in V2.32)
        GSDOctetString2Unsigned8 = 103,	// OctetString2+Unsigned8 (new in V2.32)
        GSDUnsigned16S = 104,	// Unsigned16_S (new in V2.32)
        GSDInteger16S = 105,	// Integer16_S (new in V2.32)
        GSDUnsigned8S = 106,	// Unsigned8_S (new in V2.32)
        GSDOctetStringS = 107,	// OctetString_S (new in V2.32)
        Gsdn2 = 113,	// N2 (new in V2.32)
        Gsdn4 = 114,	// N4 (new in V2.32)
        Gsdv2 = 115,	// V2 (new in V2.32)
        Gsdl2 = 116,	// L2 (new in V2.32)
        Gsdr2 = 117,	// R2 (new in V2.32)
        Gsdt2 = 118,	// T2 (new in V2.32)
        Gsdt4 = 119,	// T4 (new in V2.32)
        Gsdd2 = 120,	// D2 (new in V2.32)
        Gsde2 = 121,	// E2 (new in V2.32)
        Gsdc4 = 122,	// C4 (new in V2.32)
        Gsdx2 = 123,	// X2 (new in V2.32)
        Gsdx4 = 124,	// X4 (new in V2.32)
        GSDUnipolar216 = 125	// Unipolar2.16 (new in V2.32)

    }

    /// <summary>Graphic Types</summary>
    public enum GraphicTypes
    {
        /// <summary>Device symbol</summary>
        GSDDeviceSymbol = 0,

        /// <summary>Device icon</summary>
        GSDDeviceIcon = 1,

        /// <summary>manufacturer specific</summary>
        GSDManufacturerSpecific = 2
    }

    public enum ObserverTypes
    {
        GSDDigitalInput = 0
    }

    /// <summary>Report types</summary>
    public enum ReportTypes
    {
        /// <summary>All reports</summary>
        GsdrtAll = 0,

        /// <summary>Info reports</summary>
        GSD_RT_Info = 1,

        /// <summary>warning reports</summary>
        GSD_RT_Warning = 2,

        /// <summary>error reports</summary>
        GSD_RT_Error = 3,

        GSD_RT_MinorError = 4

    }

    /// <summary>Value types</summary>
    public enum ValueTypes
    {
        /// <summary>Concrete value</summary>
        GSDConcrete = 0,

        /// <summary>area value</summary>
        GSDArea = 1
    }

    /// <summary>Synchronisationb roles</summary>
    public enum SynchronisationRoles
    {
        /// <summary>None</summary>
        GSDSyncRoleNone = 0,

        /// <summary>Sync master</summary>
        GSDSyncMaster = 1,

        /// <summary>Sync slave</summary>
        GSDSyncSlave = 2,

        /// <summary>Sync master and sync slave</summary>
        GSDSyncMasterAndSyncSlave = 3,

        /// <summary>Reduandant sync master</summary>
        GSDSyncMasterRedundant = 4,

        /// <summary>Redundant sync master and sync slave</summary>
	    GSDSyncMasterRedundantAndSyncSlave = 5,
    }

    // -----------------------------------------------------------------------------
    /// <summary>RT classes</summary>
    public enum RTClasses
    {
        /// <summary>None</summary>
        GSDClassNone = 0,

        /// <summary>RT Class 1</summary>
        GSDClass1 = 1,

        /// <summary>RT Class 2</summary>
        GSDClass2 = 2,

        /// <summary>RT Class 3</summary>
        GSDClass3 = 3
    }

    /// <summary>MAU types</summary>
    public enum MauTypes
    {
        /// <summary>none</summary>
        GSDMauNone = 0,

        /// <summary>100BASETXFD</summary>
        GSDMau100Basetxfd = 16,

        /// <summary>100BASEFXFD</summary>
        GSDMau100Basefxfd = 18,

        /// <summary>1000BASEXFD</summary>
        GSDMau1000Basexfd = 22,

        /// <summary>1000BASELXFD</summary>
        GSDMau1000Baselxfd = 24,

        /// <summary>1000BASESXFD</summary>
        GSDMau1000Basesxfd = 26,

        /// <summary>1000BASETFD</summary>
        GSDMau1000Basetfd = 30,

        /// <summary>10GigBASEFX</summary>
        GSDMau10GigBasefx = 31
    }

    /// <summary>Link state daignosis capabilities</summary>
    public enum LinkStateDiagnosisCapabilities
    {
        /// <summary>None</summary>
	    GSDLsdcNone = 0,

        /// <summary>Up</summary>
	    GSDLsdcUp = 1,

        /// <summary>Down</summary>
	    GSDLsdcDown = 2,

        /// <summary>Up and down</summary>
	    GSDLsdcUpanddown = 3
    }

    /// <summary>Address assignment methods</summary>
    public enum AddressAssignment
    {
        /// <summary>DCP</summary>
	    GSDAddressassignmentDcp = 0,

        /// <summary>DHCP</summary>
	    GSDAddressassignmentDhcp = 1,

        /// <summary>Local</summary>
	    GSDAddressassignmentLocal = 2,
    }

    /// <summary>Media redundancy roles</summary>
    public enum MediaRedundancyRoles
    {
        /// <summary>None</summary>
	    GSDMrRoleNone = 0,

        /// <summary>Client</summary>
        GSDClient = 1,

        /// <summary>Manager</summary>
        GSDManager = 2,

        GSDManagerAuto = 3,

        GSDOff = 4,	// Off
    }


    public enum AddProtocolsSupported
    {
        No = 0,
        Yes = 1,
        Unknown = 2
    }

    public enum ShortPreamble100MBitSupported
    {
        No = 0,
        Yes = 1,
        Unknown = 2
    }

    public enum FragmentationTypes
    {
        GSDFragmentationTypeNone = 0,
        GSDFragmentationTypeDynamic = 1,	// Dynamic
        GSDFragmentationTypeStatic = 2,	// Static
    }

    // -----------------------------------------------------------------------------
    public enum RedundancyDeviceTypes
    {
        GSDDeviceTypeNone = 0,
        GSDDeviceTypeS2 = 1,	// S2
        GSDDeviceTypeR2 = 2,	// R2
        GSDDeviceTypeR1 = 3,	// R1
    }


    public enum EntityClasses
    {
        GSDEntityClassNone = 0,
        GSDEntityClass1 = 1,   // Class1
        GSDEntityClass2 = 2,   // Class2
        GSDEntityClass3 = 3		// Class3
    }

    public enum EntitySubclasses
    {
        GSDEntitySubclassNone = 0,
        GSDEntitySubclass1 = 1,    // Subclass1
        GSDEntitySubclass2 = 2,	// Subclass2
    }

    // V2.4 ========================================================================


    // -----------------------------------------------------------------------------
    public enum TimeSynchronisationRoles
    {
        GSDTimeNone = 0,
        GSDTimeMaster = 1, // Master
        GSDTimeSlave = 2       // Slave
    }


    // -----------------------------------------------------------------------------
    public enum SupportedFeatures
    {
        GSDTSNNone = 0,
        GSDTSN = 1,    // Time-Sensitive Networking
        GSDTSNTAS = 2,    // Scheduled traffic
        GSDTSNPreemption = 3      // Preemption
    }

    // -----------------------------------------------------------------------------
    public enum SfpDiagnosis
    {
        GSDSFPDiagNone = 0,
        GSDSFPDiagThresholdBased = 1, // Temp High, Bias High and Low, TX Power High and Low,
                                        // and RX Power High and Low monitoring supported
        GSDSFPDiagTxFault = 2,    // TX_FAULT monitoring supported
        GSDSFPDiagRxLoss = 3      // RX_LOS monitoring supported
    }


    // -----------------------------------------------------------------------------
    public enum ServiceProtocols
    {
        GSDSrvProtNone = 0,
        GSDSrvProtCLRPC = 1,  // The interface supports startup and record access 
                                // with the IP based remote procedure call
        GSDSrvProtRSI = 2     // The interface supports startup and record access
                                // with the RSI based remote service interface
    }


    // V2.41 ========================================================================


    // -----------------------------------------------------------------------------
    public enum SecurityClasses
    {
        GSDSecurityClassNone = 0,
        GSDSecurityClass1 = 1,
        GSDSecurityClass2 = 2,
        GSDSecurityClass3 = 3
    }

    // -----------------------------------------------------------------------------
    public enum SignatureCheckResults
    {
        GSDNoSignature = 0,
        GSDCertificateRevoked = 1,
        GSDCertificateNotSigned = 2,
        GSDCertificateOutOfDate = 3,
        GSDContentChanged = 4,
        GSDOtherFaultyResult = 5,
        GSDNotValid = 6,
        GSDValid = 7
    }

    // V2.43 ========================================================================


    // -----------------------------------------------------------------------------
    public enum SegmentClasses
    {
        GSDSegmentClassNone = 0,
        GSDSegmentClassSpur = 1,
        GSDSegmentClassTrunk = 2
    }

    // -----------------------------------------------------------------------------
    public enum PortClasses
    {
        GSDPortClassNone = 0,
        GSDPortClassPowerSource = 1,
        GSDPortClassPowerLoad = 2,
        GSDPortClassPowerCascade = 3
    }


    // -----------------------------------------------------------------------------
    public enum PowerClasses
    {
        GSDPowerClassNone = 0,
        GSDPowerClassA = 1,
        GSDPowerClassC = 2,
        GSDPowerClass3 = 3
    }


    // -----------------------------------------------------------------------------
    public enum ProtectionClasses
    {
        GSDProtectionClassNone = 0,
        GSDProtectionClassA = 1,
        GSDProtectionClassC = 2,
        GSDProtectionClassX = 3
    }


    // -----------------------------------------------------------------------------
    public enum DcpSupportedFeatures
    {
        GSDDCPSupportedFeatureNone = 0,
        GSDDCPSupportedFeaturePRUNING = 1,
        GSDDCPSupportedFeatureRejectDCPSet = 2
    }


    // -----------------------------------------------------------------------------
    public enum SnmpSupportedFeatures
    {
        GSD_SNMPSupportedFeature_None = 0,
        GSD_SNMPSupportedFeature_SNMPAdjust = 1
    }


    // -----------------------------------------------------------------------------
    public enum AplSupportedFeatures
    {
        GSD_APLSupportedFeature_None = 0,
        GSD_APLSupportedFeature_PowerReal = 1
    }


    // -----------------------------------------------------------------------------
    public enum LinkSpeeds
    {
        GSD_LinkSpeed_None = 0,
        GSD_LinkSpeed_10 = 1,
        GSD_LinkSpeed_100 = 2,
        GSD_LinkSpeed_1000 = 3,
        GSD_LinkSpeed_2500 = 4,
        GSD_LinkSpeed_5000 = 5,
        GSD_LinkSpeed_10000 = 6
    }


    // ----------------------------------------------------------------------------- 
    public enum SupportedRecords
    {
        GSD_SupportedRecord_None = 0,
        GSD_SupportedRecord_CIM = 1,
        GSD_SupportedRecord_SCM = 2,
        GSD_SupportedRecord_DCP = 3
    }


    // -----------------------------------------------------------------------------
    public enum Supported
    {
        GSD_Supported_None = 0,
        GSD_Supported_NoneAuthn = 1,
        GSD_Supported_AuthnOnly = 2,
        GSD_Supported_AuthnOnlyAndEnc = 3,
        GSD_Supported_AuthnEnc = 4
    }
    // -----------------------------------------------------------------------------

    public enum TSNSupportedConfigurations
    {
        GSD_TSNSupportedConfiguration_None = 0,
        GSD_TSNSupportedConfiguration_TSN_Cfg_Default = 1
    };

 


    // -----------------------------------------------------------------------------
    public enum Algorithms1
    {
        GSD_Algorithms1_None = 0,
        GSD_Algorithms1_HKDF_SHA2_256 = 1
    }


    // -----------------------------------------------------------------------------
    public enum Algorithms2
    {
        GSD_Algorithms2_None = 0,
        GSD_Algorithms2_X25519 = 1,
        GSD_Algorithms2_X448 = 2
    }


    // -----------------------------------------------------------------------------
    public enum Algorithms3
    {
        GSD_Algorithms3_None = 0,
        GSD_Algorithms3_Ed25519 = 1,
        GSD_Algorithms3_Ed448 = 2,
        GSD_Algorithms3_P_256 = 3,
        GSD_Algorithms3_P_521 = 4
    }


    // -----------------------------------------------------------------------------
    public enum AuthnXxx
    {
        GSD_AuthnXXX_None = 0,
        GSD_AuthnXXX_AES_GCM128 = 1,
        GSD_AuthnXXX_AES_GCM256 = 2,
        GSD_AuthnXXX_ChaCha20_Poly1305 = 3
    }

    public enum BridgeSupportedFeatures
    {
        GSD_BridgeSupportedFeature_None = 0,
        GSD_BridgeSupportedFeature_IngressRateLimiter = 1
    };



    /// <summary></summary>
    public interface INterpreter
    {
    }

    /// <summary></summary>
    public interface IInterpreterCommon
    {
    }

    /// <summary></summary>
    public interface IInterpreterStructure
    {
    }

    /// <summary></summary>
    public interface IInterpreterStructure2
    {
    }

    /// <summary></summary>
    public interface IInterpreterStructure3
    {
    }
    /// <summary></summary>
    public interface IInterpreterInfo
    {
    }

    /// <summary></summary>
    public interface IChecker
    {
    }

    /// <summary></summary>
    public interface ICheckerInfo
    {
    }

    /// <summary></summary>
    public interface IReport
    {
        /// <summary></summary>
        ReportTypes Type { get; }
    }

    /// <summary></summary>
    public interface IDeviceStructureElement
    {
    }

    /// <summary></summary>
    public interface IAccessPointStructureElement
    {
    }

  

    /// <summary></summary>
    public interface IModuleStructureElement
    {
    }

    /// <summary></summary>
    public interface ISubmoduleStructureElement
    {
    }

    /// <summary></summary>
    public interface IDeviceInfo
    {
    }

    /// <summary></summary>
    public interface IStructureElement
    {
    }

    /// <summary></summary>
    public interface ICompatibility
    {
    }

    /// <summary></summary>
    public interface IGraphic
    {
    }

    /// <summary></summary>
    public interface IDevice
    {
    }


    /// <summary></summary>
    public interface IDeviceAccessPoint
    {
    }

    /// <summary></summary>
    public interface IDeviceAccessPoint2
    {
    }

    /// <summary></summary>
    public interface IDeviceAccessPoint3
    {
    }

    /// <summary></summary>
    public interface IDeviceAccessPoint4
    {
    }

    /// <summary></summary>
    public interface IDeviceAccessPoint5
    {
    }

    /// <summary></summary>
    public interface IDeviceAccessPoint6
    {
    }

    /// <summary></summary>
    public interface ISystemDefinedSubmoduleObject
    {
    }


    /// <summary></summary>
    public interface IInterfaceSubmodule
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule2
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule3
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule4
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule5
    {
    }
    /// <summary></summary>
    public interface IProfileProcessAutomation
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule6
    {
    }

    /// <summary></summary>
    public interface IInterfaceSubmodule7
    {
    }

    /// <summary></summary>
    public interface IPortSubmodule
    {
    }

    /// <summary></summary>
    public interface IPortSubmodule2
    {
    }

    /// <summary></summary>
    public interface IPortSubmodule3
    {
    }

    /// <summary></summary>
    public interface IPortSubmodule4
    {
    }

    /// <summary></summary>
    public interface IPortSubmodule5
    {
    }

    /// <summary></summary>
    public interface IApplicationRelations
    {
    }

    /// <summary></summary>
    public interface IApplicationRelations2
    {
    }

    /// <summary></summary>
    public interface IApplicationRelations3
    {
    }

    /// <summary></summary>
    public interface IApplicationRelations4
    {
    }

    /// <summary></summary>
    public interface IApplicationRelations5
    {
    }

    /// <summary></summary>
    public interface IModuleInfo
    {
    }

    /// <summary></summary>
    public interface IModule
    {
    }

    /// <summary></summary>
    public interface IModule2
    {
    }

    /// <summary></summary>
    public interface IModule3
    {
    }

    /// <summary></summary>
    public interface IModule4
    {
    }

    /// <summary></summary>
    public interface IModulePlugData
    {
    }

    /// <summary></summary>
    public interface IVirtualSubmodule
    {
    }

    /// <summary></summary>
    public interface IVirtualSubmodule2
    {
    }

    /// <summary></summary>
    public interface IVirtualSubmodule3
    {
    }

    /// <summary></summary>
    public interface IVirtualSubmodule4
    {
    }

    /// <summary></summary>
    public interface IProfiSafe
    {
    }

    /// <summary></summary>
    public interface IProfiSafe2
    {
    }

    /// <summary></summary>
    public interface ISubslot
    {
    }

    /// <summary></summary>
    public interface IParameterRecordData
    {
    }


    /// <summary></summary>
    public interface IFParameterRecordData
    {
    }

    /// <summary></summary>
    public interface FBaseIDRecordData 
    {
    }

    /// <summary></summary>
    public interface IRecordData
    {
    }

    /// <summary></summary>
    public interface IDataItem
    {
    }


    /// <summary></summary>
    public interface IDataItem2
    {
    }

    /// <summary></summary>
    public interface IModuleObject
    {
    }

    /// <summary></summary>
    public interface IAreaItem
    {
    }

    /// <summary></summary>
    public interface IUnitDiagnosticType
    {
    }

    /// <summary></summary>
    public interface IIsochroneMode
    {
    }

    /// <summary></summary>
    public interface IIOData
    {
    }

    /// <summary></summary>
    public interface IIOConfigData
    {
    }

    /// <summary></summary>
    public interface IIOConfigData2
    {
    }


    /// <summary></summary>
    public interface IConstData
    {
    }

    /// <summary>IRefData Interface</summary>
    public interface IRefData
    {
    }

    /// <summary>IRefData2 Interface </summary>
    public interface IRefData2
    {
    }

    /// <summary></summary>
    public interface IValueItem
    {
    }

    /// <summary></summary>
    public interface IBitDataItem
    {
    }


    /// <summary></summary>
    public interface ITimingProperties
    {
    }

    /// <summary></summary>
    public interface ITimingProperties2
    {
    }

    /// <summary></summary>
    public interface ITimingProperties3
    {
    }

    /// <summary></summary>
    public interface IChannelDiagnostic
    {
    }

    /// <summary></summary>
    public interface IChannelDiagnostic2
    {
    }

    /// <summary></summary>
    public interface IChannelDiagnostic3
    {
    }
    /// <summary></summary>
    public interface IAddValueDataItem
    {
    }
    /// <summary></summary>
    public interface ISystemRedundancy
    {
    }

    /// <summary></summary>
    public interface IMenuData
    {
    }

    /// <summary></summary>
    public interface ISystemDefinedChannelDiagnostic
    {
    }


    public interface IProfileChannelDiagnostic
    {
    }

    public interface IProfileChannelDiagnostic2
    {
    }

    public interface IProfileChannelDiagnostic3
    {
    }


    public interface ISlot
    {
    }

    public interface IParamRef
    {
    }

    public interface IMenuRef
    {
    }

    public interface IEnergySavingModeItem
    {
    }

    public interface IEnergySavingModeList
    {
    }

    public interface IMeasurementItem
    {
    }

    public interface IMeasurementList
    {
    }

    public interface IMeasurementValue
    {
    }

    public interface IObserver
    {
    }

    public interface IProfIenergy
    {
    }

    public interface IReportingSystemEvents
    {
    }

    public interface IChannel
    {
    }

    public interface IData
    {
    }

    public interface IQuality
    {
    }

    public interface ICertificationInfo
    {
    }

    public interface IWorkingClock
    {
    }

    public interface ITimeSynchronisation
    {
    }

    public interface IGlobalTime
    {
    }

    public interface IMediaRedundancy
    {
    }

    public interface ICimInterface
    {
    }

    public interface ICimProtection
    {
    }

    public interface ICimResources
    {
    }

    public interface ICimSupportedFeatures
    {
    }

    public interface ICimSupportedRecords
    {
    }

    public interface IKeyAgreement
    {
    }

    public interface IKeyDerivation
    {
    }

    public interface ICommunicationInterface
    {
    }

    public interface IDigitalSignature
    {
    }

    public interface IStreamProtection
    {
    }

    public interface INetloadClasses
    {
    }

    public interface IConnectionManagementProtection
    {
    }

    public interface IAlarmProtection
    {
    }

    public interface IAplPortClassification
    {
    }

    public interface IARProtectionPropertiesSupported
    {
    }

    public interface IRecordDataStructureElement
    {
    }

    public interface ICommunicationInterfaceStructureElement
    {
    }

    public interface IBlockVersion
    {
    }

    public interface IChannelProcessAlarm
    {
    }

    public interface ISystemDefinedChannelProcessAlarm
    {
    }

    public interface IProfileChannelProcessAlarm
    {
    }

    public interface IInterconnection
    {
    }

    public interface IDataRef
    {
    }
}
