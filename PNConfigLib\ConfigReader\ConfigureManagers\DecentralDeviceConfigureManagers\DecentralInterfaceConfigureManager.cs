﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralInterfaceConfigureManager.cs     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.PNInterface;
using System;
using System.Collections.Generic;
using System.Text;
using PNConfigLib.DataModel.AttributeUtilities;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class DecentralInterfaceConfigureManager
    {
        internal Interface Configure(Configuration.DecentralDeviceType xmlDecentralDevice, 
            DecentralDeviceCatalog decentralDeviceCatalog, 
            DecentralDevice decentralDevice, 
            Project project)
        {
            Interface interfaceSubmodule =
                new Interface(xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID);
            interfaceSubmodule.PCLCatalogObject = decentralDeviceCatalog.Interface;
            decentralDevice.AddInterface(interfaceSubmodule);

            IInterfaceBusinessLogic interfaceBL = PNBaseInterfaceBL.InterfaceBLCreator(interfaceSubmodule);
            project.BusinessLogicList.Add(interfaceBL);

            interfaceSubmodule.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.ActivateDcpReadOnly,
                xmlDecentralDevice.AdvancedConfiguration.Dcp.ActivateDcpReadOnly);

            return interfaceSubmodule;
        }
    }
}
