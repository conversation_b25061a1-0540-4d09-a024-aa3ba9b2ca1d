/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IoDevicePropertiesStruct.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.IODevParamConfig
{
    /// <summary>
    /// The data record object for IoDeviceProperties.
    /// </summary>
    internal class IoDevicePropertiesStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for IoDevicePropertiesStruct.
        /// </summary>
        public IoDevicePropertiesStruct()
        {
            BlockType = DataRecords.BlockTypes.PNIODProperties;
            BlockLength = 0x001C;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[24];
        }

        /// <summary>
        /// AcceleratedStartupProcedure part of the data record.
        /// </summary>
        public bool AcceleratedStartupProcedure
        {
            get { return BufferManager.ReadBool(Data, 14, 2); }
            set { BufferManager.WriteBool(Data, 14, 2, value); }
        }

        /// <summary>
        /// AllowNameOfStationOverwrite part of the data record.
        /// </summary>
        public bool AllowNameOfStationOverwrite
        {
            get { return BufferManager.ReadBool(Data, 14, 5); }
            set { BufferManager.WriteBool(Data, 14, 5, value); }
        }

        /// <summary>
        /// CheckDeviceID part of the data record.
        /// </summary>
        public bool CheckDeviceID
        {
            get { return BufferManager.ReadBool(Data, 14, 4); }
            set { BufferManager.WriteBool(Data, 14, 4, value); }
        }

        public int DeviceIdentNumber
        {
            set { Transformator.Write16(Data, 2, (ushort)value); }
            get { return Transformator.Read16(Data, 2); }
        }

        /// <summary>
        /// FastStartupProcedure part of the data record.
        /// </summary>
        public bool FastStartupProcedure
        {
            get { return BufferManager.ReadBool(Data, 14, 1); }
            set { BufferManager.WriteBool(Data, 14, 1, value); }
        }

        /// <summary>
        /// HighPrioScanCycleIn10ms part of the data record.
        /// </summary>
        public int HighPrioScanCycleIn10ms
        {
            get { return BufferManager.ReadBitfieldBased8(Data, 15, 0, 7); }
            set { BufferManager.WriteBitfieldBased8(Data, 15, 0, 7, value); }
        }

        /// <summary>
        /// InstanceID part of the data record.
        /// </summary>
        public int InstanceId
        {
            set { Transformator.Write16(Data, 4, (ushort)value); }
            get { return Transformator.Read16(Data, 4); }
        }

        /// <summary>
        /// MaxRecordSize part of the data record.
        /// </summary>
        public int MaxRecordSize
        {
            set { Transformator.Write16(Data, 6, (ushort)value); }
            get { return Transformator.Read16(Data, 6); }
        }

        /// <summary>
        /// MultipleWriteSupported part of the data record.
        /// </summary>
        public bool MultipleWriteSupported
        {
            get { return BufferManager.ReadBool(Data, 11, 0); }
            set { BufferManager.WriteBool(Data, 11, 0, value); }
        }

        /// <summary>
        /// VendorID part of the data record.
        /// </summary>
        public int VendorID
        {
            set { Transformator.Write16(Data, 0, (ushort)value); }
            get { return Transformator.Read16(Data, 0); }
        }

        public bool UseIndividualDefaultRouter
        {
            get
            {
                return BufferManager.ReadBool(Data, 14, 0);
            }
            set
            {
                BufferManager.WriteBool(Data, 14, 0, value);
            }
        }
    }
}