/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SwitchType.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.PNPlannerAdapter.DataClasses
{
    /// <summary>
    /// PNPlanner class that represents an interface (with defined settings).
    /// </summary>
    internal class SwitchType
    {
        //####################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// The constructor for SwitchType.
        /// </summary>
        /// <param name="submodule">The interface submodule for this SwitchType.</param>
        /// <param name="syncDomain">The sync domain that contains this SwitchType.</param>
        public SwitchType(Interface submodule, SyncDomainBusinessLogic syncDomain)
        {
            Name = "";
            if (submodule == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            // Gathering the required attributes from the interface submodule.
            MaxBridgeDelay =
                submodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIrtSwitchBridgingDelay,
                    ac.GetNew(),
                    0);

            bool isFFWActivatedByUser =
                syncDomain.PCLObject.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIrtSyncDomainFFW,
                    new AttributeAccessCode(),
                    false);

            if (isFFWActivatedByUser && PNPlannerUtility.IsFFWSupported(submodule))
            {
                if (AttributeUtilities.IsIDevice(submodule)
                    || !Utility.IsProfinetControllerInterfaceSubmodule(submodule))
                {
                    MaxBridgeDelayFFW =
                        (int)submodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIrtSwitchBridgingDelayFFW,
                            ac.GetNew(),
                            0);
                }
                else if (Utility.IsProfinetControllerInterfaceSubmodule(submodule))
                {
                    MaxBridgeDelayFFW =
                        (int)submodule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIrtSwitchBridgingDelayFFWIOC,
                            ac.GetNew(),
                            0);
                }
            }
            else
            {
                MaxBridgeDelayFFW = 0;
            }

            MaxBufferTime =
                submodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIrtSwitchMaxBufferTime,
                    ac.GetNew(),
                    0);

            uint minRTC3Gap = submodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtMinRTC3Gap,
                ac.GetNew(),
                1120);
            string minInterLsduGapStr =
                syncDomain.PCLObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.PnPnPlannerMinInterLsduGap,
                    ac.GetNew(),
                    string.Empty);
            uint minInterLsduGap = uint.Parse(minInterLsduGapStr, CultureInfo.InvariantCulture);
            AdditionalLsduGap = minRTC3Gap - minInterLsduGap;

            SwitchesOfType = new List<PclObject>();

            Ports = new List<Port>();

            // Get the ports of the switch and add them to the input XML file.
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(submodule);

            if (ports != null)
            {
                // Add all ports of the 
                for (int portCursor = 0; portCursor < ports.Count; portCursor++)
                {
                    AddPort(ports[portCursor], portCursor);
                }
            }

            // Get the forwarding mode.
            Enumerated forwardingMode =
                submodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtForwardingMode,
                    ac.GetNew(),
                    null);
            if (forwardingMode == null)
            {
                ForwardingMode = PNIrtForwardingMode.None;
            }
            else
            {
                ForwardingMode = (PNIrtForwardingMode)forwardingMode.DefaultValue;
            }

            uint jitter = 0;
            if (!Utility.IsProfinetControllerInterfaceSubmodule(submodule))
            {
                // Get max. dfp feed. Only exists in IOD, so use the default value for IOC
                MaxDfpFeed = submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMaxDfpFeed,
                    ac.GetNew(),
                    0);

                // Get the peer to peer jitter, and if not exists or 0, use global synchronization jitter
                jitter = submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPeerToPeerJitter,
                    ac.GetNew(),
                    0);
            }
            if (jitter == 0)
            {
                jitter =
                    (uint)
                    submodule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnIrtPllWindow,
                        ac.GetNew(),
                        1000);
            }
            PeerToPeerJitter = jitter;

            // Check if subframe checksums are supported. Only exists in IOC, If IOC does not support then value 
            //of IOD does not matter but If IOC supports then IOD should support DfpOutboundTruncation or
            //Debug Checkbok Enable DFP O
            if (Utility.IsProfinetControllerInterfaceSubmodule(submodule))
            {
                SfCrc16 =
                    submodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtSubframeCRCSupported,
                        ac.GetNew(),
                        false);
            }
            else
            {
                SfCrc16 =
                    submodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtDfpOutboundTruncationSupported,
                        ac.GetNew(),
                        false);
            }

            if (ForwardingMode != PNIrtForwardingMode.Relative)
            {
                MaxTimeLag = PNConstants.PNIrtMaxTimeLag;
            }
            else
            {
                uint pnIrtMaxRetentionTime =
                    submodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxRetentionTime,
                        ac.GetNew(),
                        0);
                MaxTimeLag = ac.IsOkay ? pnIrtMaxRetentionTime : PNConstants.DefaultMaxTimeLag;
            }

            InterfaceSubmodule = submodule;

            // Get the restart factor for distributed watchdog. Only exists in IOC, use 0 for IOD.
            DistributedWatchdogRestartFactor =
                submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtRestartFactorForDistributedWD,
                    ac.GetNew(),
                    0);

            // Check if dfp in ring is supported
            SupportsDfpInRing = false;

            if (AttributeUtilities.IsIDevice(submodule)
                || !Utility.IsProfinetControllerInterfaceSubmodule(submodule))
            {
                MaxDfpFrames = submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMaxDfpFrames,
                    ac.GetNew(),
                    0);
            }
            else if (Utility.IsProfinetControllerInterfaceSubmodule(submodule))
            {
                MaxDfpFrames =
                    submodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxDfpFramesIOC,
                        ac.GetNew(),
                        0);
            }

            List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(submodule);

            bool advancedSupportFound = startupModes.Contains(PNIrtArStartupMode.Advanced);
            bool legacySupportFound = startupModes.Contains(PNIrtArStartupMode.Legacy);

            StartupMode = (advancedSupportFound ? PNPlannerConstants.m_XmlAdvancedText : string.Empty)
                          + (advancedSupportFound && legacySupportFound ? " " : string.Empty)
                          + (legacySupportFound ? PNPlannerConstants.m_XmlLegacyText : string.Empty);

            MinFSO = submodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtMinFrameSendOffset,
                ac.GetNew(),
                5000);

            MaxFrameStartTime =
                submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxFrameStartTime,
                    ac.GetNew(),
                    PNFunctionsDefaultAttributeValues.DefaultPNIOMaxFrameStartTime);

            //DistributionMode
            DistributionMode =
                submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoPnPlannerDistributionMode,
                    ac,
                    PNFunctionsDefaultAttributeValues.DefaultPNPlannerDistributionMode);

            //MaxFramesPerMs
            MaxFramesPerMs =
                submodule.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoPnPlannerMaxFramesPerMs,
                    ac.GetNew(),
                    -1);

            //MaxBytesPerMs
            MaxBytesPerMs = submodule.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoPnPlannerMaxBytesPerMs,
                ac.GetNew(),
                -1);

            //MaxBytesPerMs
            MaxRTC12BytesPerMs =
                submodule.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoPnPlannerMaxRTC12BytesPerMs,
                    ac.GetNew(),
                    -1);

            //SuppRR12
            ac.Reset();

            SuppRr12 = AttributeUtilities.GetCurrentSuppRR12(submodule);

            //SuppRR3
            ac.Reset();
            SuppRr3 = AttributeUtilities.GetCurrentSuppRR3(submodule);

            //ScfAdaptionSupported
            ScfAdaptionSupported =
                submodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoScfAdaptionSupported,
                    ac.GetNew(),
                    PNFunctionsDefaultAttributeValues.DefaultPNIOScfAdaptionSupported)
                    ? 1
                    : 0;

            //ScfAdaptionNonPow2Supported
            ScfAdaptionNonPow2Supported =
                submodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoScfAdaptionNonPow2Supported,
                    ac.GetNew(),
                    PNFunctionsDefaultAttributeValues.DefaultPNIOScfAdaptionNonPow2Supported)
                    ? 1
                    : 0;

            //SupportedSendClockFactors12
            ac.Reset();

            SupportedSendClockFactors12 =
                submodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF12, ac, null);

            //SupportedSendClockFactors3
            ac.Reset();
            Enumerated suppScf3Enumerated;
            Utility.GetSupportedSendClockFactors3(submodule, out suppScf3Enumerated);
            SupportedSendClockFactors3 = suppScf3Enumerated;

            //MinNrtGap
            MinNrtGap = submodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIoMinNRTGap,
                ac.GetNew(),
                PNFunctionsDefaultAttributeValues.DefaultPNIOMinNRTGap);

            //MinFrameInterval
            ac.Reset();
            MinFrameInterval =
                submodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMinFrameIntFactor,
                    ac,
                    (int)PNFunctionsDefaultAttributeValues.DefaultPNMinFrameIntFactor);

            //SuppRR12Pow2
            ac.Reset();
            SuppRr12Pow2 = submodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIoSuppRR12Pow2,
                ac,
                null);
            if (!ac.IsOkay)
            {
                SuppRr12Pow2 = new Enumerated();
                SuppRr12Pow2.List.AddRange(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow12.Cast<object>());
            }

            //SuppRR12NonPow2
            ac.Reset();
            SuppRr12NonPow2 =
                submodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppRR12NonPow2,
                    ac,
                    null);
            if (!ac.IsOkay)
            {
                SuppRr12NonPow2 = new Enumerated();
                SuppRr12NonPow2.List.AddRange(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow12.Cast<object>());
            }

            //SuppRR3Pow2
            ac.Reset();
            SuppRr3Pow2 = submodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIoSuppRR3Pow2,
                ac,
                null);
            if (!ac.IsOkay)
            {
                SuppRr3Pow2 = new Enumerated();
                SuppRr3Pow2.List.AddRange(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow3.Cast<object>());
            }

            //SuppRR3NonPow2
            ac.Reset();
            SuppRr3NonPow2 =
                submodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppRR3NonPow2,
                    ac,
                    null);
            if (!ac.IsOkay)
            {
                SuppRr3NonPow2 = new Enumerated();
                SuppRr3NonPow2.List.AddRange(PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow3.Cast<object>());
            }

            //IsProxy
            ac.Reset();
            IsProxy = submodule.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsProxy, ac, false);

            if (IsProxy)
            {
                ac.Reset();
                // MaxFramesPerMs
                long pnIoPNPlannerMaxFramesPerMs =
                    submodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoPnPlannerMaxFramesPerMs,
                        ac.GetNew(),
                        0);

                MaxFramesPerMs = pnIoPNPlannerMaxFramesPerMs;

                // MaxBytesPerMs
                long pnIoPNPlannerMaxBytesPerMs =
                    submodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoPnPlannerMaxBytesPerMs,
                        ac.GetNew(),
                        0);

                MaxBytesPerMs = pnIoPNPlannerMaxBytesPerMs;

                // MaxRTC12BytesPerMs
                long PNIoPNPlannerMaxRTC12BytesPerMs =
                    submodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoPnPlannerMaxRTC12BytesPerMs,
                        ac.GetNew(),
                        0);

                MaxRTC12BytesPerMs = PNIoPNPlannerMaxRTC12BytesPerMs;
            }

            PNIOD ioDevice = submodule.PNIOD;

            PNIOC ioController = submodule.PNIOC;
            ac.GetNew();

            if (ioController != null)
            {
                //IoSyncRole
                IoSyncRole = (byte)PNAttributeUtility.GetAdjustedSyncRole(ioController);

                //DeviceLocalReductionRatio
                ac.GetNew();
                DeviceLocalReductionRatio =
                    ioController.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                        ac.GetNew(),
                        0);

                //StationNo
                ac.GetNew();
                StationNo = ioController.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnStationNumber,
                    ac,
                    0);

                //SlotNo
                SlotNo = 0;

                //SubSlotNo
                SubSlotNo = 0;

                //FrameClass
                ac.GetNew();
                FrameClass = ioController.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoFrameClass,
                    ac,
                    0);

                //UpdateTimeMode
                ac.GetNew();
                PNIOFrameClass fc =
                    ioController.AttributeAccess.GetAnyAttribute<PNIOFrameClass>(
                        InternalAttributeNames.PnIoFrameClass,
                        ac,
                        0);
                byte origUpdateTimeMode =
                    ioController.AttributeAccess.GetAnyAttribute<byte>(
                        InternalAttributeNames.PnUpdateTimeMode,
                        ac.GetNew(),
                        PNFunctionsDefaultAttributeValues.DefaultPNUpdateTimeMode);
                UpdateTimeMode = fc == PNIOFrameClass.Class3Frame
                                     ? (byte)PNUpdateTimeMode.FixedReduction
                                     : origUpdateTimeMode;

                //FixedPhaseNumber
                ac.GetNew();
                if (((FrameClass == (long)PNIOFrameClass.Class3Frame)
                     && ((PNUpdateTimeMode)UpdateTimeMode == PNUpdateTimeMode.Automatic))
                    || (FrameClass != (long)PNIOFrameClass.Class3Frame))
                {
                    FixedPhaseNumber = 0; // this value will be at the caller-side
                }
                else
                {
                    FixedPhaseNumber = 1;
                }
            }

            else if (ioDevice != null)
            {
                //IoSyncRole
                ac.GetNew();
                IoSyncRole = (byte)PNAttributeUtility.GetAdjustedSyncRole(ioDevice);

                //DeviceLocalReductionRatio
                ac.GetNew();
                DeviceLocalReductionRatio =
                    ioDevice.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                        ac.GetNew(),
                        1);

                //StationNo
                ac.GetNew();
                StationNo = ioDevice.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnStationNumber, ac, 1);
                //SlotNo
                SlotNo = 0;

                //SubSlotNo
                SubSlotNo = 0;

                //FrameClass
                ac.GetNew();
                FrameClass = ioDevice.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoFrameClass,
                    ac,
                    0);

                //UpdateTimeMode
                ac.GetNew();
                PNIOFrameClass fc =
                    ioDevice.AttributeAccess.GetAnyAttribute<PNIOFrameClass>(
                        InternalAttributeNames.PnIoFrameClass,
                        ac,
                        0);
                byte origUpdateTimeMode =
                    ioDevice.AttributeAccess.GetAnyAttribute<byte>(
                        InternalAttributeNames.PnUpdateTimeMode,
                        ac.GetNew(),
                        PNFunctionsDefaultAttributeValues.DefaultPNUpdateTimeMode);
                UpdateTimeMode = fc == PNIOFrameClass.Class3Frame
                                     ? (byte)PNUpdateTimeMode.FixedReduction
                                     : origUpdateTimeMode;

                //FixedPhaseNumber
                ac.GetNew();
                if (((FrameClass == (long)PNIOFrameClass.Class3Frame)
                     && ((PNUpdateTimeMode)UpdateTimeMode == PNUpdateTimeMode.Automatic))
                    || (FrameClass != (long)PNIOFrameClass.Class3Frame))
                {
                    FixedPhaseNumber = 0; // this value will be at the caller-side
                }
                else
                {
                    FixedPhaseNumber = 1;
                }
            }
        }

        #endregion

        //####################################################################################

        #region Private Implementation

        /// <summary>
        /// The method adds a port to the switch type.
        /// </summary>
        /// <param name="portObject">The port to add.</param>
        private void AddPort(DataModel.PCLObjects.Port portObject, int portNumber)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            Port port;

            portObject.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.UniquePortNumber, portNumber);
            port.Number = portNumber;
            port.RxDelay = portObject.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtPortRxDelay,
                new AttributeAccessCode(),
                0);
            port.TxDelay = portObject.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtPortTxDelay,
                new AttributeAccessCode(),
                0);
            port.ShortPreamble100MBitSupported =
                portObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtShortPreamble100MBitSupported,
                    new AttributeAccessCode(),
                    false);
            portObject.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnFiberOptic,
                ac,
                0);
            port.IsFiberOptic = ac.IsOkay;
            Ports.Add(port);
        }

        #endregion

        //####################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //####################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// Whether SwitchType uses isochronous real time.
        /// </summary>
        public bool IsIrt { get; set; }

        /// <summary>
        /// Gets the name of the SwitchType.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets the MaxBridgeDelay of the SwitchType.
        /// </summary>
        public int MaxBridgeDelay { get; }

        /// <summary>
        /// Gets the MaxBridgeDelayFFW of the SwitchType.
        /// </summary>
        public int MaxBridgeDelayFFW { get; }

        /// <summary>
        /// Gets the MaxBufferTime of the SwitchType.
        /// </summary>
        public int MaxBufferTime { get; }

        /// <summary>
        /// Gets the AdditionalLsduGap of the SwitchType.
        /// </summary>
        public uint AdditionalLsduGap { get; }

        /// <summary>
        /// Whether the SwitchType is a proxy.
        /// </summary>
        public bool IsProxy { get; }

        /// <summary>
        /// Gets the ports of the SwitchType.
        /// </summary>
        public List<Port> Ports { get; }

        /// <summary>
        /// Gets the Switch Objects of this SwitchType.
        /// </summary>
        public List<PclObject> SwitchesOfType { get; }

        /// <summary>
        /// Max. Dfp feed of the switch if it is used as an IOD and supports dfp.
        /// </summary>
        public uint MaxDfpFeed { get; }

        /// <summary>
        /// Synchronisation jitter between two dfp devices.
        /// </summary>
        public uint PeerToPeerJitter { get; }

        /// <summary>
        /// Determines if the switch supports subframe checksums if it is used as an IOC and supports dfp.
        /// </summary>
        public bool SfCrc16 { get; }

        public uint MaxTimeLag { get; }

        /// <summary>
        /// Distributes watchdog restart factor of the switch if it is used as an IOC and supports dfp.
        /// </summary>
        public uint DistributedWatchdogRestartFactor { get; }

        /// <summary>
        /// SwitchType supports packgroups in the ring as IOC.
        /// </summary>
        public bool SupportsDfpInRing { get; }

        /// <summary>
        /// Maximum number of dfp frames supported if the switchtype is an IOC.
        /// 0 means there are no restrictions on that.
        /// </summary>
        public uint MaxDfpFrames { get; }

        /// <summary>
        /// Supported startup modes of a device.
        /// </summary>
        public string StartupMode { get; }

        /// <summary>
        /// Supported forwarding mode of a device.
        /// </summary>
        public PNIrtForwardingMode ForwardingMode { get; }

        /// <summary>
        /// Minimum frame send offset supported by a device.
        /// </summary>
        public uint MinFSO { get; }

        // Note: Don't forget to modify the Equals() method after adding a new property.
        /// <summary>
        /// The distribution mode of SwitchType.
        /// </summary>
        public uint DistributionMode { get; }

        /// <summary>
        /// Maximum frames per millisecond supported by SwitchType.
        /// </summary>
        public long MaxFramesPerMs { get; private set; }

        /// <summary>
        /// Maximum bytes per millisecond supported by SwitchType.
        /// </summary>
        public long MaxBytesPerMs { get; private set; }

        /// <summary>
        /// Maximum bytes per millisecond for RT Class 1,2.
        /// </summary>
        public long MaxRTC12BytesPerMs { get; private set; }

        /// <summary>
        /// Supported reduction ratios for RT Class 1,2.
        /// </summary>
        private List<int> SuppRr12 { get; set; }

        /// <summary>
        /// Supported reduction ratios for RT Class 3.
        /// </summary>
        private List<int> SuppRr3 { get; set; }

        /// <summary>
        /// Whether sendclock factor adaption is supported by SwitchType.
        /// </summary>
        public int ScfAdaptionSupported { get; private set; }

        /// <summary>
        /// Whether sendclock factor adaption for sendclock values
        /// that are not power of 2 is supported by SwitchType.
        /// </summary>
        public int ScfAdaptionNonPow2Supported { get; private set; }

        /// <summary>
        /// Supported sendclock factors for RT Class 1,2.
        /// </summary>
        public Enumerated SupportedSendClockFactors12 { get; private set; }

        /// <summary>
        /// Supported sendclock factors for RT Class 3.
        /// </summary>
        public Enumerated SupportedSendClockFactors3 { get; private set; }

        /// <summary>
        /// Minimum inter-spacing supported by the SwitchType for the injection of NRT frames.
        /// </summary>
        public uint MinNrtGap { get; }

        /// <summary>
        /// Minimum time between 2 frames.
        /// </summary>
        public long MinFrameInterval { get; }

        /// <summary>
        /// Supported RT Class 1,2 reduction ratios for sendclocks that are power of 2.
        /// </summary>
        public Enumerated SuppRr12Pow2 { get; }

        /// <summary>
        /// Supported RT Class 1,2 reduction ratios for sendclocks that are not power of 2.
        /// </summary>
        public Enumerated SuppRr12NonPow2 { get; }

        /// <summary>
        /// Supported RT Class 3 reduction ratios for sendclocks that are power of 2.
        /// </summary>
        public Enumerated SuppRr3Pow2 { get; }

        /// <summary>
        /// Supported RT Class 3 reduction ratios for sendclocks that are not power of 2.
        /// </summary>
        public Enumerated SuppRr3NonPow2 { get; }

        /// <summary>
        /// Sync role of SwitchType.
        /// </summary>
        private byte IoSyncRole { get; }

        /// <summary>
        /// User adjusted reduction ratio in "fixed reduction" mode.
        /// </summary>
        private long DeviceLocalReductionRatio { get; set; }

        /// <summary>
        /// Station number.
        /// </summary>
        private int StationNo { get; set; }

        /// <summary>
        /// Slot number.
        /// </summary>
        private int SlotNo { get; set; }

        /// <summary>
        /// Subslot number.
        /// </summary>
        private int SubSlotNo { get; set; }

        /// <summary>
        /// Frame class.
        /// </summary>
        private long FrameClass { get; }

        /// <summary>
        /// Update time mode.
        /// </summary>
        private byte UpdateTimeMode { get; }

        /// <summary>
        /// Minimum frame send offset supported by this SwitchType for the injection
        /// of the first NRT frame within the green period.
        /// </summary>
        public uint MaxFrameStartTime { get; private set; }

        /// <summary>
        /// Fixed phase number.
        /// </summary>
        private long FixedPhaseNumber { get; set; }

        /// <summary>
        /// Corresponding data model object for this SwitchType.
        /// </summary>
        public Interface InterfaceSubmodule { get; private set; }

        #endregion

        //####################################################################################

        #region Public Methods

        // Contains all public methods of the class	
        /// <summary>
        /// Returns the hash code for this instance.
        /// </summary>
        /// <returns>A 32-bit signed integer hash code.</returns>
        public override int GetHashCode()
        {
            return 0;
        }

        /// <summary>
        /// Compares the given SwitchType with the owner.
        /// </summary>
        /// <param name="obj">Provided SwitchType.</param>
        /// <returns>True if the given SwitchType is equal to the owner.</returns>
        public override bool Equals(object obj)
        {
            SwitchType typeToCompare = obj as SwitchType;

            if (typeToCompare == null)
            {
                return false;
            }

            // Compare all properties of two switches.
            if ((typeToCompare.MaxBytesPerMs != MaxBytesPerMs)
                || (typeToCompare.MaxFramesPerMs != MaxFramesPerMs)
                || (typeToCompare.MaxFrameStartTime != MaxFrameStartTime)
                || (typeToCompare.MaxRTC12BytesPerMs != MaxRTC12BytesPerMs)
                || (typeToCompare.SupportedSendClockFactors12 != SupportedSendClockFactors12)
                || (typeToCompare.SupportedSendClockFactors3 != SupportedSendClockFactors3)
                || (!typeToCompare.SuppRr12.SequenceEqual(SuppRr12))
                || (typeToCompare.SuppRr12NonPow2 != SuppRr12NonPow2)
                || (typeToCompare.SuppRr12Pow2 != SuppRr12Pow2)
                || (!typeToCompare.SuppRr3.SequenceEqual(SuppRr3))
                || (typeToCompare.SuppRr3NonPow2 != SuppRr3NonPow2)
                || (typeToCompare.SuppRr3Pow2 != SuppRr3Pow2)
                || (typeToCompare.ScfAdaptionNonPow2Supported != ScfAdaptionNonPow2Supported)
                || (typeToCompare.ScfAdaptionSupported != ScfAdaptionSupported)
                || (typeToCompare.MaxBridgeDelay != MaxBridgeDelay)
                || (typeToCompare.MaxBridgeDelayFFW != MaxBridgeDelayFFW)
                || (typeToCompare.MaxBufferTime != MaxBufferTime)
                || (typeToCompare.Ports.Count != Ports.Count)
                || (typeToCompare.ForwardingMode != ForwardingMode)
                || (typeToCompare.MaxDfpFeed != MaxDfpFeed)
                || (typeToCompare.PeerToPeerJitter != PeerToPeerJitter)
                || (typeToCompare.SfCrc16 != SfCrc16)
                || (typeToCompare.SupportsDfpInRing != SupportsDfpInRing)
                || (typeToCompare.MaxDfpFrames != MaxDfpFrames)
                || (typeToCompare.StartupMode != StartupMode)
                || (typeToCompare.MinFSO != MinFSO)
                || (typeToCompare.MaxTimeLag != MaxTimeLag)
                || (typeToCompare.MinNrtGap != MinNrtGap)
                || (typeToCompare.MinFrameInterval != MinFrameInterval))
            {
                return false;
            }

            // Compare all ports. The count of ports has already been compared.
            foreach (Port port in typeToCompare.Ports)
            {
                if (!Ports.Contains(port))
                {
                    return false;
                }
            }

            // The types are the same.
            return true;
        }

        /// <summary>
        /// Adds a switch to the local list.
        /// </summary>
        /// <param name="interfaceSubmodule">The switch to be added.</param>
        public void AddSwitch(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule != null)
            {
                SwitchesOfType.Add(interfaceSubmodule);
            }
        }

        #endregion
    }
}