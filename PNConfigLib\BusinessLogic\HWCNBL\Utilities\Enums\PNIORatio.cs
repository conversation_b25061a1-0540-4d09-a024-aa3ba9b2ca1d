/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIORatio.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Utilities.Enums
{
    /// <summary>
    /// Contains the PNIO/CBA Communication Ratios.
    /// </summary>
    public enum PNIORatio
    {
        None = 0x00000000,

        OneToZero = 0x00010000, // --> 100% for PNIO

        SevenToOne = 0x00070001, // --> 87,5% for PNIO

        ThreeToOne = 0x00030001, // --> 75% for PNIO

        OneToOne = 0x00010001, // --> 50% for PNIO

        OneToThree = 0x00010003, // --> 25% for PNIO

        OneToSeven = 0x00010007, // --> 12,5% for PNIO

        ZeroToOne = 0x00000001 // --> 0% for PNIO
    }
}