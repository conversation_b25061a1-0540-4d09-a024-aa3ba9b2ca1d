﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: GSDI                                      :P&  */
/*                                                                           */
/*  P a c k a g e         &W: Interpreter                               :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_045.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.45 and is based on GSD(ML) versions 2.44 and lower.
    ///	
    /// </summary>
    internal class CheckerV02045 : CheckerV02044
    {

        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList;
                return xp;
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList;
                return xp;
            }
        }

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02045;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02045;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            Checks ??= new List<string>();

            Checks.Add(Constants.s_Cn_0X00045000);
            Checks.Add(Constants.s_Cn_0X00045001);
            Checks.Add(Constants.s_Cn_0X00045002);
            Checks.Add(Constants.s_Cn_0X00045003);
            Checks.Add(Constants.s_Cn_0X00045004);
            Checks.Add(Constants.s_Cn_0X00045005);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {

            }
            catch (NotSupportedException e)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.45.
        /// </summary>
        public CheckerV02045()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version245);
        }

        #endregion

        #region All Checks

        #region Category : Validation

        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// The attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// As of GSDML V2.4:
        /// - New values 0x004F - 0x0066 for MAUTypes
        /// As of GSDML V2.41
        /// - New values 103..113, 141, 144 for MAUTypes
        /// As of GSDML V2.45
        /// - New values 145..218 for MAUTypes
        /// 
        /// Partly the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs into which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            AttributeValueListDictionary.Remove("MAUTypes-v2.4");
            string values1 = "-v2.44 0 5 10..13 15..18 21..26 29..102 103..113 141 144";    // applies for pnio_version > 2.4
            AttributeValueListDictionary.Add("MAUTypes-v2.4", values1);
            string values2 = "0 5 10..13 15..18 21..26 29..102 103..113 141 144 145..218";  // applies for pnio_version > 2.44
            AttributeValueListDictionary.Add("MAUTypes-v2.44", values2);
        }

        #endregion

        #endregion


        /// <summary>
        /// Check number: CN_0x00045000
        /// 
        /// Checks if the ModuleIdentNumber is not unique within the DeviceAccessPointList.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045000()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);

            HashSet<string> moduleIdentNumbers = new();

            foreach (var dap in daps)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (pnioVersion < 2.45)
                    continue;

                string moduleIdentNumber = Help.GetAttributeValueFromXElement(dap, Attributes.s_ModuleIdentNumber);

                // Try to add the moduleIdentNumber to the HashSet
                if (moduleIdentNumbers.Add(moduleIdentNumber))
                {
                    continue;
                }

                // If the Add method returns false, the moduleIdentNumber is not unique
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045000_1"), moduleIdentNumber);
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00045000_1");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00045001
        /// 
        /// Checks if the Main Family "PA Profiles" is only used for PA Proflie specific GSDs
        /// (1) If @VendorID == 0xF100, then DeviceFunction/Family/@MainFamily shall be "PA Profiles".
        /// (2) If @VendorID != 0xF100, then DeviceFunction/Family/@MainFamily shall not be "PA Profiles".
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045001()
        {
            var vendorId = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceIdentity).Attributes(Attributes.s_VendorId);
            vendorId = Help.TryRemoveXAttributesUnderXsAny(vendorId, Nsmgr, Gsd);
            foreach (XAttribute vendorIdAttribute in vendorId)
            {
                string vendorIdValue = vendorIdAttribute.Value;

                var deviceFunction = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceFunction);
                var mainFamily = deviceFunction.Descendants(NamespaceGsdDef + Elements.s_Family).Attributes(Attributes.s_MainFamily);
                mainFamily = Help.TryRemoveXAttributesUnderXsAny(mainFamily, Nsmgr, Gsd);

                foreach (XAttribute mainFamilyAttribute in mainFamily)
                {
                    string mainFamilyValue = mainFamilyAttribute.Value;

                    // (1)
                    if (vendorIdValue == "0xF100" && mainFamilyValue != "PA Profiles") 
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045001_1"));
                        string xpath = Help.GetXPath(vendorIdAttribute);
                        var xli = (IXmlLineInfo)vendorIdAttribute;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00045001_1");
                    }

                    // (2)
                    if (vendorIdValue != "0xF100" && mainFamilyValue == "PA Profiles")
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045001_2"));
                        string xpath = Help.GetXPath(vendorIdAttribute);
                        var xli = (IXmlLineInfo)vendorIdAttribute;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00045001_2");
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00045002
        ///
        /// Checks if the Access attribute has the correct values for each type of usage 
        /// (1) The ParameterRecordDataItem referred from ParameterRecordDataRef element's Access attribute shall contain the token "prm".
        /// (2) The ParameterRecordDataItem referred from AvailableRecordDataList element's Access attribute shall contain the token "read" or "write".
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045002()
        {
            // Get all ParameterRecordDataItem elements from the RecordDataList in the GsdProfileBody
            var allParameterRecordDataItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RecordDataList)
                .SelectMany(recordDataList => recordDataList.Descendants(NamespaceGsdDef + Elements.s_ParameterRecordDataItem))
                .ToList();

            // (1)
            // Get all RecordDataTarget attributes from the ParameterRecordDataRef elements in the VirtualSubmoduleItem elements
            var allRecordDataTargets = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem)
                .SelectMany(virtualSubmoduleItem => virtualSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_ParameterRecordDataRef))
                .Select(recordDataRef => Help.TryRemoveXAttributesUnderXsAny(recordDataRef.Attributes(Attributes.s_RecordDataTarget), Nsmgr, Gsd))
                .SelectMany(recordDataTarget => recordDataTarget)
                .Select(recordDataTarget => recordDataTarget.Value)
                .ToList();

            // Iterate over all RecordDataTargets
            foreach (var recordDataTarget in allRecordDataTargets)
            {
                // Find the ParameterRecordDataItem that matches the current RecordDataTarget
                var parameterRecordDataItem = allParameterRecordDataItems
                    .FirstOrDefault(item => recordDataTarget == Help.GetAttributeValueFromXElement(item, Attributes.ID));

                // If no matching ParameterRecordDataItem is found, skip to the next iteration
                if (null == parameterRecordDataItem)
                {
                    continue;
                }

                // Get the Access attribute value from the ParameterRecordDataItem
                string parameterRecordDataItemAccess = Help.GetAttributeValueFromXElement(parameterRecordDataItem, Attributes.s_Access);

                // Set the default value for the Access attribute
                if (string.IsNullOrEmpty(parameterRecordDataItemAccess))
                {
                    parameterRecordDataItemAccess = "prm";
                }

                // If the Access attribute contains "prm", skip to the next iteration
                if (parameterRecordDataItemAccess.Contains("prm"))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(parameterRecordDataItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                // If the Access attribute does not contain "prm", create an error report
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045002_1"));
                string xpath = Help.GetXPath(parameterRecordDataItem);
                var xli = (IXmlLineInfo)parameterRecordDataItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00045002_1");
            }

            // (2)
            // Get all RecordDataTarget attributes from the RecordDataRef elements in the AvailableRecordDataList elements in the VirtualSubmoduleItem elements
            allRecordDataTargets = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem)
                .SelectMany(virtualSubmoduleItem => virtualSubmoduleItem.Descendants(NamespaceGsdDef + Elements.s_AvailableRecordDataList))
                .SelectMany(availableRecordDataList => availableRecordDataList.Descendants(NamespaceGsdDef + Elements.s_RecordDataRef))
                .Select(recordDataRef => Help.TryRemoveXAttributesUnderXsAny(recordDataRef.Attributes(Attributes.s_RecordDataTarget), Nsmgr, Gsd))
                .SelectMany(recordDataTarget => recordDataTarget)
                .Select(recordDataTarget => recordDataTarget.Value)
                .ToList();

            // Iterate over all RecordDataTargets
            foreach (string recordDataTarget in allRecordDataTargets)
            {
                // Find the ParameterRecordDataItem that matches the current RecordDataTarget
                var parameterRecordDataItem = allParameterRecordDataItems
                    .FirstOrDefault(item => recordDataTarget == Help.GetAttributeValueFromXElement(item, Attributes.ID));

                // If no matching ParameterRecordDataItem is found, skip to the next iteration
                if (null == parameterRecordDataItem)
                {
                    continue;
                }

                // Get the Access attribute value from the ParameterRecordDataItem
                string parameterRecordDataItemAccess = Help.GetAttributeValueFromXElement(parameterRecordDataItem, Attributes.s_Access);

                // If the Access attribute contains "read" or "write", skip to the next iteration
                if (parameterRecordDataItemAccess.Contains("read") || parameterRecordDataItemAccess.Contains("write"))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(parameterRecordDataItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                // If the Access attribute does not contain "read" or "write", create an error report
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045002_2"));
                string xpath = Help.GetXPath(parameterRecordDataItem);
                var xli = (IXmlLineInfo)parameterRecordDataItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00045002_2");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00045003
        ///
        /// Checks if BitLength attribute is used together with Format attribute in BitDataItem elements.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045003()
        {
            var allBitDataItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_BitDataItem);

            foreach (var bitDataItem in allBitDataItems)
            {
                if (bitDataItem.Attribute(Attributes.s_BitLength) == null)
                {
                    continue;
                }

                if (bitDataItem.Attribute(Attributes.s_Format) == null)
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(bitDataItem, SupportedGsdmlVersion))
                {
                    continue;
                }

                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045003_1"));
                string xpath = Help.GetXPath(bitDataItem);
                var xli = (IXmlLineInfo)bitDataItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00045003_1");
            }

            return true;
        }

        /// <summary>
        /// Validate the F_Par_CRC.
        /// </summary>
        protected override bool CheckCn_0X00010128_1()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var fparamRecord in nl)
            {
                string requiredSchemaVersionStr;

                var submodule = GetSubmoduleFromFparamRecord(fparamRecord);
                var dapModuleOrAppl = GetDapModuleOrApplFromSubmodule(submodule);

                if (null == dapModuleOrAppl)
                {
                    continue;
                }

                if (dapModuleOrAppl.Name.LocalName == Elements.s_ApplicationProcess)
                {
                    // The pluggable submodule has the RequiredSchemaVersion itself
                    requiredSchemaVersionStr = Help.GetAttributeValueFromXElement(submodule, Attributes.s_RequiredSchemaVersion);
                    if (string.IsNullOrEmpty(requiredSchemaVersionStr))
                    {
                        requiredSchemaVersionStr = "V2.1";
                    }
                }
                else
                {
                    // For the fixed submodule the RequiredSchemaVersion must be taken from dap or module
                    requiredSchemaVersionStr = Help.GetAttributeValueFromXElement(dapModuleOrAppl, Attributes.s_RequiredSchemaVersion);
                    if (string.IsNullOrEmpty(requiredSchemaVersionStr))
                    {
                        requiredSchemaVersionStr = "V1.0";
                    }
                }

                double requiredSchemaVersion = GetPNioVersion(requiredSchemaVersionStr);
                if (requiredSchemaVersion < 2.44)
                {
                    calculateFParCRC(fparamRecord);
                }
                else
                {
                    calculateFParCRC_PROFIsafeMU2(fparamRecord);
                }
            }

            return true;
        }

        private XElement GetSubmoduleFromFparamRecord(XObject fparamRecord)
        {
            if (null == fparamRecord.Parent)
            {
                return null;
            }

            var submodule = fparamRecord.Parent.Parent;

            return submodule;
        }

        private XElement GetDapModuleOrApplFromSubmodule(XObject submodule)
        {
            if (null == submodule)
            {
                return null;
            }

            if (null == submodule.Parent)
            {
                return null;
            }

            return submodule.Parent.Parent;
        }

        /// <summary>
        /// Calculate the F_Par_CRC.
        /// </summary>
        protected virtual void calculateFParCRC_PROFIsafeMU2(XElement fparamRecord)
        {
            Crc16 calcFParCrc = new();

            byte fblockId = 0;  // Default: "0"
            var fblockIds = fparamRecord.Element(NamespaceGsdDef + Elements.s_FBlockID);
            if (fblockIds != null)
            {
                string strFBlockId = Help.GetAttributeValueFromXElement(fblockIds, Attributes.s_DefaultValue);
                if (!String.IsNullOrEmpty(strFBlockId))
                    fblockId = byte.Parse(strFBlockId, CultureInfo.InvariantCulture);
            }

            bool isiParCrc = (fblockId & 1) == 1;
            bool isWdTime2 = (fblockId & 2) == 2;

            // Build the F_Par_CRC
            calcFParCrc.InitChecksum();

            if (isWdTime2)
            {
                // The next two bytes are occupied with F_WD_Time_2, if available
                // Add F_WD_Time_2, if bit 1 of F_Block_ID is set to "1"
                byte[] data5 = GetFWdTime2Bytes(fparamRecord);

                // Add the byte array to the CRC
                calcFParCrc.UpdateChecksum(data5);
            }

            // The first four bytes (0 - 3) are occupied with F_iPar_CRC, if available
            // Add F_iPar_CRC, if bit 0 of F_Block_ID is set to "1"
            if (isiParCrc)
            {
                byte[] data1 = GetFiParBytes(fparamRecord);

                // Add the byte array to the CRC
                calcFParCrc.UpdateChecksum(data1);
            }

            // The next byte is occupied with F_Prm_Flag1 = F_Check_SeqNr, F_Check_iPar, F_SIL, F_CRC_Length, F_CRC_Seed
            byte fPrmFlag1 = GetFPrmFlag1(fparamRecord);

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag1);

            // The next byte is occupied with F_Prm_Flag2 = F_Passivation, F_Block_ID, F_Par_Version
            byte fPrmFlag2 = GetFPrmFlag2(fparamRecord);

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag2);

            // The next two bytes are occupied with F_Source_Add
            // Add F_Source_Add, if visible
            byte[] data2 = GetFSourceAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data2);

            // The next two bytes are occupied with F_Dest_Add
            // Add F_Dest_Add, if visible
            byte[] data3 = GetFDestAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data3);

            // The next two bytes are occupied with F_WD_Time
            // Add F_WD_Time, if visible
            byte[] data4 = GetFWdTimeBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data4);

            // Finish the checksum process, returning the CRC
            UInt16 calculatedFParCrc = calcFParCrc.FinishChecksum(false);

            // Get the F_Par_CRC from Xml file
            UInt16 fparCrc = 53356;  // Default: "53356"
            var fparCrCs = fparamRecord.Element(NamespaceGsdDef + Elements.s_FParCrc);
            XObject node = fparamRecord;
            if (fparCrCs != null)
            {
                node = fparCrCs;
                XAttribute nodeFParCrc = fparCrCs.Attribute(Attributes.s_DefaultValue);
                if (nodeFParCrc != null)
                {
                    fparCrc = XmlConvert.ToUInt16(nodeFParCrc.Value);
                    node = nodeFParCrc;
                }
            }

            CreateReport0X00010128_1(calculatedFParCrc, fparCrc, fparamRecord, node);
        }

        /// <summary>
        /// Check number: CN_0x00045004
        ///
        /// (1) If the '(Virtual)SubmoduleItem/@PROFIsafeSupported' attribute is set to "true" and the element 'RecordDataList/F_BaseIDRecordDataItem'
        ///     is present, the 'F_ParameterRecordDataItem/@F_CRC_Seed' attribute must be available.
        /// (2) If the '(Virtual)SubmoduleItem/@PROFIsafeSupported' attribute is not set to "true", the element 'RecordDataList/F_BaseIDRecordDataItem'
        ///     shall not be present.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045004()
        {
            // Find all submodules with PROFIsafeSupported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);

            foreach (var submodule in allSubmodules)
            {
                string strProfIsafeSupported =
                    Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafeSupported);
                bool pRofIsafeSupported = !string.IsNullOrEmpty(strProfIsafeSupported) && XmlConvert.ToBoolean(strProfIsafeSupported);

                var allFBaseIdRecordDataItems = submodule.Descendants(NamespaceGsdDef + Elements.s_FBaseIDRecordDataItem).ToList();
                var allFCrdSeeds = submodule.Descendants(NamespaceGsdDef + Elements.s_FCrcSeed).ToList();

                // (1)
                if (pRofIsafeSupported)
                {
                    if (allFBaseIdRecordDataItems.Any() && !allFCrdSeeds.Any())
                    {
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045004_1"));
                        string xpath = Help.GetXPath(submodule);
                        var xli = (IXmlLineInfo)submodule;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00045004_1");
                    }
                }

                // (2)
                else if (allFBaseIdRecordDataItems.Any())
                {
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045004_2"));
                    string xpath = Help.GetXPath(submodule);
                    var xli = (IXmlLineInfo)submodule;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00045004_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00045005
        ///
        /// Checks if the RecordDataList/F_BaseIDRecordDataItem/@DefaultValue attribute is of type Integer64.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00045005()
        {
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);

            foreach (var submodule in allSubmodules)
            {
                var allFBaseIdRecordDataItems = submodule.Descendants(NamespaceGsdDef + Elements.s_FBaseIDRecordDataItem).ToList();

                foreach (var bBaseIdRecordDataItem in allFBaseIdRecordDataItems)
                {
                    var allFBaseIds = bBaseIdRecordDataItem.Descendants(NamespaceGsdDef + Elements.s_FBaseID);

                    foreach (var fBaseId in allFBaseIds)
                    {
                        string defaultValue = Help.GetAttributeValueFromXElement(fBaseId, Attributes.s_DefaultValue);

                        if (string.IsNullOrEmpty(defaultValue))
                        {
                            continue;
                        }

                        bool isDefaultValueInt64 = long.TryParse(defaultValue, NumberStyles.Any, CultureInfo.InvariantCulture, out long _);

                        if (isDefaultValueInt64)
                        {
                            continue;
                        }


                        if (!Help.CheckSchemaVersion(fBaseId, SupportedGsdmlVersion))
                        {
                            continue;
                        }
                        
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00045005_1"));
                        string xpath = Help.GetXPath(bBaseIdRecordDataItem);
                        var xli = (IXmlLineInfo)bBaseIdRecordDataItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00045005_1");
                    }
                }
            }

            return true;
        }
    }
}
