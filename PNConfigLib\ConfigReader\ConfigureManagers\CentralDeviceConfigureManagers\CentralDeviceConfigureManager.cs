﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDeviceConfigureManager.cs          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;
using PNConfigLib.BusinessLogic.HWCNBL;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.PNProjectManager;

namespace PNConfigLib.ConfigReader.ConfigureManagers.CentralDeviceConfigureManagers
{
    internal class CentralDeviceConfigureManager
    {
        private Project m_Project;

        private Configuration.Configuration m_Configuration
        {
            get;
        }

        private ListOfNodes.ListOfNodes m_ListOfNodes
        {
            get;
        }

        private Topology.Topology m_Topology
        {
            get;
        }

        internal CentralDeviceConfigureManager(Project project,
            Configuration.Configuration configuration,
            ListOfNodes.ListOfNodes listOfNodes,
            Topology.Topology topology)
        {
            m_Project = project;
            m_ListOfNodes = listOfNodes;
            m_Configuration = configuration;
            m_Topology = topology;
        }

        /// <summary>
        /// Configure central device and dall related configurators
        /// </summary>
        /// <param name="configurationDevicesCentralDevice"></param>
        internal void Configure(List<CentralDeviceType> configurationDevicesCentralDevice, string listOfNodesPath)
        {
            foreach (CentralDeviceType xmlCentralDevice in configurationDevicesCentralDevice)
            {
                IOAddressManager ioAddressManager = new IOAddressManager();

                PNDriverType lonPnDriver = ProjectManagerUtilities.GetCentralDeviceFromListOfNodes(
                    m_ListOfNodes,
                    xmlCentralDevice.DeviceRefID);
                CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                    lonPnDriver.Interface.InterfaceType,
                    lonPnDriver.Interface.CustomInterfacePath,
                    lonPnDriver.DeviceVersion,
                    listOfNodesPath);

                CentralDevice centralDevice = new CentralDevice(xmlCentralDevice.DeviceRefID);
                centralDevice.PCLCatalogObject = pndCatalog;

                CentralDeviceBL centralDeviceBL = null;

                if (centralDevice.IsSnmpSupported())
                {
                    centralDeviceBL = new SnmpCentralDeviceBL(centralDevice);
                }
                else
                {
                    centralDeviceBL = new CentralDeviceBL(centralDevice);
                }
                
                m_Project.BusinessLogicList.Add(centralDeviceBL);

                centralDeviceBL.Configure(xmlCentralDevice, lonPnDriver);

                CentralInterfaceConfigureManager ifConfigureManager = new CentralInterfaceConfigureManager(m_Project, m_Configuration, m_Topology);
                Interface interfaceSubmodule = ifConfigureManager.Configure(xmlCentralDevice, pndCatalog, lonPnDriver, centralDevice);

                List<Configuration.DecentralDeviceType> relatedXmlDecentralDevices = new List<Configuration.DecentralDeviceType>();
                foreach (Configuration.DecentralDeviceType xmlDecentralDevice in m_Configuration.Devices.DecentralDevice)
                {
                    string ioSystemRefId = xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses
                        .IOSystemRefID;
                    if ((!string.IsNullOrEmpty(ioSystemRefId) && (interfaceSubmodule.PNIOC.IOSystem.Id == ioSystemRefId))
                        || (xmlDecentralDevice.SharedDevice != null)
                        && xmlDecentralDevice.SharedDevice.Any(sd => sd.DeviceRefID == centralDevice.Id))
                    {
                        relatedXmlDecentralDevices.Add(xmlDecentralDevice);
                    }
                }
                DecentralDeviceConfigureManager decentralDeviceConfigureManager = new DecentralDeviceConfigureManager(m_Project, m_Configuration, m_ListOfNodes, m_Topology);
                decentralDeviceConfigureManager.Configure(relatedXmlDecentralDevices, interfaceSubmodule.PNIOC.IOSystem, ref ioAddressManager, configurationDevicesCentralDevice);
            }
        }
    }
}
