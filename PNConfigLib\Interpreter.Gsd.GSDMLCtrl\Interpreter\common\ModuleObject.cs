/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModuleObject.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ModuleObject itself is only a abstract base class for all accesspoints,
    /// modules and submodules.
    /// </summary>
    public abstract class ModuleObject :
        GsdObject,
        IModuleObject
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ModuleObject if a inherited class is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        protected ModuleObject()
        {
            // Initialize the properties
            m_GsdID = String.Empty;
            m_GsdIdRaw = String.Empty;
            m_IdentNumber = 0;
            m_Info = null;

            m_CompatibilityVersion = Constants.s_Version20;
            m_IsCompatible = true;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_GsdID;
        private string m_GsdIdRaw;
        private uint m_IdentNumber;
        private ArrayList m_Graphics;
        private Hashtable m_GraphicsQuickFinder;    // Only private!
        private ModuleInfo m_Info;

        private string m_CompatibilityVersion;
        private bool m_IsCompatible;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the ID of the module, which is unique over the entire
        /// GSD document.
        /// </summary>
        /// <remarks>The ID is only unique over the entire GSD document for 
        /// the same type of elements. For example, the ID "ID_1" on the
        /// Module element must be unique for all Module elements over the
        /// entire document, but it is possible to use the same ID for a
        /// DeviceAccessPoint.</remarks>
        public string GsdID => this.m_GsdID;

        public string GsdIdRaw => this.m_GsdIdRaw;

        /// <summary>
        /// Accesses the ident number of the module.
        /// </summary>
        public UInt32 IdentNumber => this.m_IdentNumber;

        /// <summary>
        /// Accesses the list of Graphic objects, available from this module. 
        /// </summary>
        public virtual Array Graphics =>
            this.m_Graphics?.ToArray();

        /// <summary>
        /// Accesses the module info object, which contains general information 
        /// about a module.
        /// </summary>
        public virtual ModuleInfo Info => this.m_Info;

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Makes the graphic object with the specified type available.
        /// For one type there is only one graphic object possible.
        /// </summary>
        /// <param name="lType">Type of the graphic which should be getted.</param>
        /// <returns>The graphic object, if one with the given type is found,
        /// else null.</returns>
        public virtual Graphic GetGraphic(GSDI.GraphicTypes lType)
        {
            if (null != this.m_GraphicsQuickFinder)
            {
                if (this.m_GraphicsQuickFinder.ContainsKey(lType))
                    return this.m_GraphicsQuickFinder[lType] as Graphic;
            }

            // If no graphic or this graphic didn't exist, return nothing.
            return null;
        }

        #endregion

        //########################################################################################
        #region ICompatibility Members

        /// <summary>
        /// Returns whether the GSDML version of the actual structure element is compatible
        /// to the actual supported GSDML version.
        /// </summary>
        /// <returns>True if it is compatible, else false.</returns>
        public bool IsCompatible()
        {
            return this.m_IsCompatible;
        }

        /// <summary>
        /// Accesses the GSDML version to which the actual structure element is compatible.
        /// </summary>
        public string CompatibilityVersion => this.m_CompatibilityVersion;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                FillFieldGsdId(hash);

                FillFieldGsdIdRaw(hash);

                FillFieldFieldIdentNumber(hash);

                FillFieldFieldGraphics(hash);

                FillFieldFieldInfo(hash);

                FillFieldFieldCompatibilityVersion(hash);

                FillFieldFieldIsCompatible(hash);

                // For better handling of graphics in relation to the type create private hashtable.
                if (null != m_Graphics)
                {
                    foreach (Graphic g in m_Graphics)
                    {
                        // If graphics exist, create hashtable.
                        if (null == m_GraphicsQuickFinder)
                            m_GraphicsQuickFinder = new Hashtable(m_Graphics.Count);

                        // Add graphic to quick finder hashtable.
                        m_GraphicsQuickFinder.Add(g.Type, g);
                    }
                }

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        private void FillFieldFieldIsCompatible(Hashtable hash)
        {
            string member = Models.s_FieldIsCompatible;
            if (hash.ContainsKey(member) && hash[member] is bool)
                this.m_IsCompatible = (bool)hash[member];
        }

        private void FillFieldFieldCompatibilityVersion(Hashtable hash)
        {
            string member = Models.s_FieldCompatibilityVersion;
            if (hash.ContainsKey(member) && hash[member] is string)
                this.m_CompatibilityVersion = hash[member] as string;
        }

        private void FillFieldFieldInfo(Hashtable hash)
        {
            string member = Models.s_FieldInfo;
            if (hash.ContainsKey(member) && hash[member] is ModuleInfo)
                this.m_Info = hash[member] as ModuleInfo;
        }

        private void FillFieldFieldGraphics(Hashtable hash)
        {
            string member = Models.s_FieldGraphics;
            if (hash.ContainsKey(member) && hash[member] is ArrayList)
                this.m_Graphics = hash[member] as ArrayList;
        }

        private void FillFieldFieldIdentNumber(Hashtable hash)
        {
            string member = Models.s_FieldIdentNumber;
            if (hash.ContainsKey(member) && hash[member] is uint)
                this.m_IdentNumber = (uint)hash[member];
        }

        private void FillFieldGsdIdRaw(Hashtable hash)
        {
            string member = Models.s_FieldGsdIdRaw;
            if (hash.ContainsKey(member) && hash[member] is string)
                this.m_GsdIdRaw = hash[member] as string;
        }

        private void FillFieldGsdId(Hashtable hash)
        {
            string member = Models.s_FieldGsdId;
            if (hash.ContainsKey(member) && hash[member] is string)
                this.m_GsdID = hash[member] as string;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // NOTE: Normally not needed because this class is abstract!!!
            // Object begin.

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldGsdId, m_GsdID);
            Export.WriteStringProperty(ref writer, Models.s_FieldGsdIdRaw, this.m_GsdIdRaw);
            Export.WriteUint32Property(ref writer, Models.s_FieldIdentNumber, m_IdentNumber);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInfo, m_Info);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldGraphics, m_Graphics);
            Export.WriteStringProperty(ref writer, Models.s_FieldCompatibilityVersion, m_CompatibilityVersion);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsCompatible, m_IsCompatible);

            return true;
        }


        #endregion

    }
}


