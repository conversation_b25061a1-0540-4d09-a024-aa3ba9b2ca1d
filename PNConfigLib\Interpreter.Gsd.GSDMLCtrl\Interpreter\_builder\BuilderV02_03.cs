/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_03.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml.XPath;
using System.Collections;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV0203 :
        BuilderV02025
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0203()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version23);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectSystemRedundancy:
                        {
                            PrepareSystemRedundancy(nav, ref hash);
                            obj = new C.SystemRedundancy();

                            break;
                        }
                    case Models.s_ObjectMenuData:
                        {
                            PrepareMenuData(nav, ref hash);
                            obj = new C.MenuData();

                            break;
                        }

                    case Models.s_ObjectMenuRef:
                        {
                            PrepareMenuRef(nav, ref hash);
                            obj = new C.MenuRef();

                            break;
                        }

                    case Models.s_ObjectParamRef:
                        {
                            PrepareParamRef(nav, ref hash);
                            obj = new C.ParamRef();

                            break;
                        }

                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldForwardingMode, null);
            hash.Add(Models.s_FieldStartupMode, null);
            hash.Add(Models.s_FieldMaxRangeIrFrameId, null);
            hash.Add(Models.s_FieldMaxBridgeDelayFfw, null);
            hash.Add(Models.s_FieldMaxDfpFeed, null);
            hash.Add(Models.s_FieldMaxDfpFrames, null);
            hash.Add(Models.s_FieldAlignDfpSubframes, null);
            hash.Add(Models.s_FieldFragmentationType, null);
            hash.Add(Models.s_FieldMaxRedPeriodLength, null);
            hash.Add(Models.s_FieldMinFso, null);
            hash.Add(Models.s_FieldIsMaxBridgeDelayFfw, null);
            hash.Add(Models.s_FieldIsMaxDfpFeed, null);


            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // Get RTClass3Properties info. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(
                Elements.s_RTClass3Properties,
                Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                AddAttributesToHashtable(hash, nodes);
            }



            nodes = nav.SelectChildren(Elements.s_SynchronisationMode, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            if (nodes.Current == null)
            {
                return;
            }

            string attr = nodes.Current.GetAttribute(Attributes.s_PeerToPeerJitter, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPeerToPeerJitter] = System.Xml.XmlConvert.ToUInt32(attr);
            }
            else if (hash.ContainsKey(Models.s_FieldTPllMax))
            {
                // If attribute PeerToPeerJitter was not found, the value from the attribute T_PLL_Max is used
                hash[Models.s_FieldPeerToPeerJitter] = hash[Models.s_FieldTPllMax];
            }

        }

        private static void AddAttributesToHashtable(IDictionary hash, XPathNodeIterator nodes)
        {
            if (nodes.Current == null)
            {
                return;
            }

            string attr = nodes.Current.GetAttribute(Attributes.s_AlignDfpSubframes, String.Empty);
            hash[Models.s_FieldAlignDfpSubframes] = Help.GetBool(attr, Attributes.s_DefaultAlignDfpSubframes);

            attr = nodes.Current.GetAttribute(Attributes.s_MaxDfpFrames, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxDfpFrames] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // Get ForwardingMode attribute. Optional.
            attr = nodes.Current.GetAttribute(Attributes.s_ForwardingMode, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldForwardingMode] = Help.SeparateTokenList(attr);
            }

            attr = nodes.Current.GetAttribute(Attributes.s_FragmentationType, String.Empty);
            if (Enums.IsFragmentationTypeEnumValueConvertable(attr))
            {
                hash[Models.s_FieldFragmentationType] = Enums.ConvertFragmentationTypeEnum(attr);
            }
            else
            {
                hash[Models.s_FieldFragmentationType] = GSDI.FragmentationTypes.GSDFragmentationTypeNone;
            }

            // MaxDFP_Feed attribute, optional
            hash[Models.s_FieldIsMaxDfpFeed] = false;
            attr = nodes.Current.GetAttribute(Attributes.s_MaxDfpFeed, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldIsMaxDfpFeed] = true;
                hash[Models.s_FieldMaxDfpFeed] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // MaxBridgeDelayFFW attribute, optional
            hash[Models.s_FieldIsMaxBridgeDelayFfw] = false;
            attr = nodes.Current.GetAttribute(Attributes.s_MaxBridgeDelayFfw, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldIsMaxBridgeDelayFfw] = true;
                hash[Models.s_FieldMaxBridgeDelayFfw] = System.Xml.XmlConvert.ToUInt32(attr);
            }
            else
            {
                // Set the default value of MaxBridgeDelayFFW
                hash[Models.s_FieldMaxBridgeDelayFfw] = uint.MinValue;
            }


            attr = nodes.Current.GetAttribute(Attributes.s_MaxRangeIrFrameId, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldMaxRangeIrFrameId] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nodes.Current.GetAttribute(Attributes.s_MaxRedPeriodLength, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldMaxRedPeriodLength] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nodes.Current.GetAttribute(Attributes.s_MinFso, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMinFso] = System.Xml.XmlConvert.ToUInt32(attr);
            }
            else
            {
                hash[Models.s_FieldMinFso] = Attributes.s_DefaultMinFso;
            }

            attr = nodes.Current.GetAttribute(Attributes.s_StartupMode, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldStartupMode] = Help.SeparateTokenList(attr);
            }
            else
            {
                ArrayList list = new ArrayList();
                list.Add(Attributes.s_DefaultStartupMode);
                hash[Models.s_FieldStartupMode] = list;
            }
        }
        protected override void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMrpdSupported, null);
            hash.Add(Models.s_FieldMrtSupported, null);

            // Call base class method first.
            base.PrepareMediaRedundancy(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_MrpdSupported, String.Empty);
            hash[Models.s_FieldMrpdSupported] = Help.GetBool(attr, Attributes.s_DefaultMrpdSupported);

            attr = nav.GetAttribute(Attributes.s_MrtSupported, String.Empty);
            hash[Models.s_FieldMrtSupported] = Help.GetBool(attr, Attributes.s_DefaultMrtSupported);
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldIsAutoConfigurationSupported, null);
            hash.Add(Models.s_FieldIsCirSupported, null);
            hash.Add(Models.s_FieldSystemRedundancy, null);
            hash.Add(Models.s_FieldIsLldpnoDSupported, null);
            hash.Add(Models.s_FieldIsPrmBeginPrmEndSequenceSupported, null);
            hash.Add(Models.s_FieldResetToFactoryModes, null);

            string attr = nav.GetAttribute(Attributes.s_CirSupported, String.Empty);
            hash[Models.s_FieldIsCirSupported] = Help.GetBool(attr, Attributes.s_DefaultCirSupported);

            attr = nav.GetAttribute(Attributes.s_LldpnoDSupported, String.Empty);
            hash[Models.s_FieldIsLldpnoDSupported] = Help.GetBool(attr, Attributes.s_DefaultLldpnoDSupported);

            attr = nav.GetAttribute(Attributes.s_PrmBeginPrmEndSequenceSupported, String.Empty);
            hash[Models.s_FieldIsPrmBeginPrmEndSequenceSupported] = Help.GetBool(attr, Attributes.s_DefaultPrmBeginPrmEndSequenceSupported);

            attr = nav.GetAttribute(Attributes.s_AutoConfigurationSupported, String.Empty);
            hash[Models.s_FieldIsAutoConfigurationSupported] = Help.GetBool(attr, Attributes.s_DefaultAutoConfigurationSupported);

            attr = nav.GetAttribute(Attributes.s_ResetToFactoryModes, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldResetToFactoryModes] = ValueListHelper.SeparateUnsignedValueList(attr);
            }

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_SystemRedundancy, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                object obj = CreateGsdObject(Models.s_ObjectSystemRedundancy, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSystemRedundancy + "' couldn´t be created!");

                // Set hash variable.
                hash[Models.s_FieldSystemRedundancy] = obj;
            }
        }

        protected override void PrepareIOConfigData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOConfigData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMaxApplicationInputLength, null);
            hash.Add(Models.s_FieldIsMaxApplicationInputLength, null);
            hash.Add(Models.s_FieldMaxApplicationOutputLength, null);
            hash.Add(Models.s_FieldIsMaxApplicationOutputLength, null);
            hash.Add(Models.s_FieldMaxApplicationDataLength, null);
            hash.Add(Models.s_FieldIsMaxApplicationDataLength, null);

            // Call base class method first.
            base.PrepareIOConfigData(nav, ref hash);

            // Get maximal input length attribute. Optional.
            hash[Models.s_FieldIsMaxApplicationInputLength] = false;
            string attr = nav.GetAttribute(Attributes.s_MaxApplicationInputLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxApplicationInputLength] = Attributes.s_DefaultMaxApplicationInputLength;
            }
            else
            {
                hash[Models.s_FieldMaxApplicationInputLength] = System.Xml.XmlConvert.ToUInt32(attr);
                hash[Models.s_FieldIsMaxApplicationInputLength] = true;
            }

            // Get maximal output length attribute. Optional.
            hash[Models.s_FieldIsMaxApplicationOutputLength] = false;
            attr = nav.GetAttribute(Attributes.s_MaxApplicationOutputLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxApplicationOutputLength] = Attributes.s_DefaultMaxApplicationOutputLength;
            }
            else
            {
                hash[Models.s_FieldMaxApplicationOutputLength] = System.Xml.XmlConvert.ToUInt32(attr);
                hash[Models.s_FieldIsMaxApplicationOutputLength] = true;
            }

            // Get maximal data length attribute. Optional.
            hash[Models.s_FieldIsMaxApplicationDataLength] = false;
            attr = nav.GetAttribute(Attributes.s_MaxApplicationDataLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxApplicationDataLength] = Attributes.s_DefaultMaxApplicationDataLength;
            }
            else
            {
                hash[Models.s_FieldMaxApplicationDataLength] = System.Xml.XmlConvert.ToUInt32(attr);
                hash[Models.s_FieldIsMaxApplicationDataLength] = true;
            }
        }

        protected override void PrepareApplicationRelations(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldStartupMode, null);

            // Call base class method first.
            base.PrepareApplicationRelations(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_StartupMode, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldStartupMode] = Help.SeparateTokenList(attr);
            }
            else
            {
                ArrayList list = new ArrayList();
                list.Add(Attributes.s_DefaultStartupMode);
                hash[Models.s_FieldStartupMode] = list;
            }
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldShortPreamble100MbitSupported, null);
            hash.Add(Models.s_FieldIsShortPreamble100MbitSupported, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            hash[Models.s_FieldIsShortPreamble100MbitSupported] = false;
            string attr = nav.GetAttribute(Attributes.s_ShortPreamble100MbitSupported, String.Empty);
            hash[Models.s_FieldShortPreamble100MbitSupported] = Help.GetBool(attr, Attributes.s_DefaultShortPreamble100MbitSupported);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldIsShortPreamble100MbitSupported] = true;
            }
        }

        protected virtual void PrepareSystemRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDeviceType, null);
            hash.Add(Models.s_FieldMaxSwitchOverTime, null);
            hash.Add(Models.s_FieldPrimaryArOnBothNapsSupported, null);
            hash.Add(Models.s_FieldNumberOfSrArSets, null);

            string attr = nav.GetAttribute(Attributes.s_DeviceType, String.Empty);
            if (Enums.IsRedundancyDeviceTypeEnumValueConvertable(attr))
            {
                hash[Models.s_FieldDeviceType] = Enums.ConvertRedundancyDeviceTypeEnum(attr);
            }

            attr = nav.GetAttribute(Attributes.s_MaxSwitchOverTime, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxSwitchOverTime] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_NumberOfSrArSets, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldNumberOfSrArSets] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_PrimaryArOnBothNapsSupported, String.Empty);
            hash[Models.s_FieldPrimaryArOnBothNapsSupported] = Help.GetBool(attr, Attributes.s_DefaultPrimaryArOnBothNapsSupported);

            attr = nav.GetAttribute(Attributes.s_RTInputOnBackupArSupported, String.Empty);
            hash[Models.s_FieldRTInputOnBackupArSupported] = Help.GetBool(attr, false);
        }

        protected override void PrepareParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldChangeableWithBump, null);
            hash.Add(Models.s_FieldMenuItems, null);

            // Call base class method first.
            base.PrepareParameterRecordData(nav, ref hash);

            // Get ChangeableWithBump attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_ChangeableWithBump, String.Empty);
            hash[Models.s_FieldChangeableWithBump] = Help.GetBool(attr, Attributes.s_DefaultChangeableWithBump);

            // Navigate to menu data and create it. Optional.
            ArrayList list = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_MenuList, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.Count == 1)
                nodes.MoveNext();

            XPathNavigator nav1 = nodes.Current;
            if (nav1 != null)
            {
                nodes = nav1.SelectChildren(Elements.s_MenuItem, Namespaces.s_GsdmlDeviceProfile);
            }

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = this.CreateGsdObject(Models.s_ObjectMenuData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMenuData + "' couldn´t be created!");

                list.Add(obj);	// Add it to the list.
            }
            hash[Models.s_FieldMenuItems] = list;
        }

        protected override void PrepareFParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldChangeableWithBump, null);

            // Call base class method first.
            base.PrepareFParameterRecordData(nav, ref hash);


            // Get ChangeableWithBump attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_ChangeableWithBump, String.Empty);
            hash[Models.s_FieldChangeableWithBump] = Help.GetBool(attr, Attributes.s_DefaultChangeableWithBump);
        }

        protected override void PrepareRefData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Module object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldParamId, null);

            // Call base class method first.
            base.PrepareRefData(nav, ref hash);
            nav.GetAttribute(Attributes.ID, String.Empty);
            string paramID = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldParamId] = paramID;
        }

        protected virtual void PrepareMenuData(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMenuItemId, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldMenuRefs, null);
            hash.Add(Models.s_FieldParamRefs, null);

            string menuItemID = nav.GetAttribute(Attributes.ID, String.Empty);
            hash[Models.s_FieldMenuItemId] = menuItemID;

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.Count == 1)
            {
                nodes.MoveNext();
                XPathNavigator nav1 = nodes.Current;
                string menuName = GetText(nav1);
                hash[Models.s_FieldName] = menuName;
                if (nav1 != null)
                {
                    hash[Models.s_FieldNameTextId] = nav1.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            AddMenuRefToList(nav, hash);

            AddParameterRefToList(nav, hash);
        }
        private void AddParameterRefToList(XPathNavigator nav, IDictionary hash)
        {

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ParameterRef, Namespaces.s_GsdmlDeviceProfile);
            // Add each found ParameterRef to list.
            ArrayList listParamRef = null;
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == listParamRef)
                {
                    listParamRef = new ArrayList(nodes.Count);
                }

                // Find ParameterRef.
                XPathNavigator nav1 = nodes.Current;
                string paramName = string.Empty;
                if (nav1 == null)
                {
                    continue;
                }
                string paramRefID = nav1.GetAttribute(Attributes.s_ParameterTarget, String.Empty);
                XPathNodeIterator nodesRef = nav.SelectChildren(Elements.s_ParameterRef, Namespaces.s_GsdmlDeviceProfile);
                nodesRef.MoveNext();
                nav1 = nodesRef.Current;
                nav1.MoveToParent();
                nav1.MoveToParent();
                nav1.MoveToParent();

                XPathNodeIterator nodes1 = nav1.SelectChildren(Elements.s_Ref, Namespaces.s_GsdmlDeviceProfile);
                while (nodes1.MoveNext())
                {
                    XPathNavigator nav2 = nodes1.Current;
                    string paramID = nav2.GetAttribute(Attributes.ID, String.Empty);
                    if (paramID != paramRefID)
                    {
                        continue;
                    }
                        paramName = GetText(nav2);
                        break;
                    
                }

                C.ParamRef paramRefCurr = new C.ParamRef();
                paramRefCurr.Name = paramName;
                paramRefCurr.ParamRefID = paramRefID;
                listParamRef.Add(paramRefCurr);	// Add it to the list.
            }
            hash[Models.s_FieldParamRefs] = listParamRef;
        }

        private void AddMenuRefToList(XPathNavigator nav, IDictionary hash)
        {
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_MenuRef, Namespaces.s_GsdmlDeviceProfile);
            // Add each found MenuRef to list.
            ArrayList listMenuRef = null;
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == listMenuRef)
                    listMenuRef = new ArrayList(nodes.Count);

                // Find MenuRef.
                XPathNavigator nav1 = nodes.Current;
                string menuName = string.Empty;
                if (nav1 == null)
                {
                    continue;
                }
                string menuRefID = nav1.GetAttribute(Attributes.s_MenuTarget, String.Empty);
                XPathNodeIterator nodesRef = nav.SelectChildren(Elements.s_MenuRef, Namespaces.s_GsdmlDeviceProfile);
                nodesRef.MoveNext();
                nav1 = nodesRef.Current;
                nav1.MoveToParent();
                nav1.MoveToParent();

                XPathNodeIterator nodes1 = nav1.SelectChildren(Elements.s_MenuItem, Namespaces.s_GsdmlDeviceProfile);
                while (nodes1.MoveNext())
                {
                    XPathNavigator nav2 = nodes1.Current;
                    string menuID = nav2.GetAttribute(Attributes.ID, String.Empty);
                    if (menuID != menuRefID)
                    {
                        continue;
                    }
                    XPathNodeIterator nodes2 = nav2.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
                    if (nodes2.Count == 1)
                    {
                        nodes2.MoveNext();
                        XPathNavigator nav3 = nodes2.Current;
                        menuName = GetText(nav3);
                    }
                    break;

                }

                C.MenuRef menuRefCurr = new C.MenuRef();
                menuRefCurr.Name = menuName;
                menuRefCurr.MenuRefID = menuRefID;
                listMenuRef.Add(menuRefCurr);	// Add it to the list.
            }
            hash[Models.s_FieldMenuRefs] = listMenuRef;
        }
        protected virtual void PrepareMenuRef(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMenuRefId, null);
            hash.Add(Models.s_FieldName, null);

            string menuRefID = nav.GetAttribute(Attributes.s_MenuTarget, String.Empty);
            hash[Models.s_FieldMenuRefId] = menuRefID;

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.Count == 1)
            {
                nodes.MoveNext();
                XPathNavigator nav1 = nodes.Current;
                string menuName = GetText(nav1);
                hash[Models.s_FieldName] = menuName;
            }
        }

        protected virtual void PrepareParamRef(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldParamRefId, null);
            hash.Add(Models.s_FieldName, null);

            string paramRefID = nav.GetAttribute(Attributes.s_ParameterTarget, String.Empty);
            hash[Models.s_FieldParamRefId] = paramRefID;

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Ref, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.Count == 1)
            {
                nodes.MoveNext();
                XPathNavigator nav1 = nodes.Current;
                string paramName = GetText(nav1);
                hash[Models.s_FieldName] = paramName;
            }
        }

        #endregion

        #endregion

    }
}
