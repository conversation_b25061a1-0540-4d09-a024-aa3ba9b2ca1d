﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: GSDImport                                 :C&  */
/*                                                                           */
/*  F i l e               &F: AssemblyInfo.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

#endregion

[assembly:CLSCompliant(false)]

// The following GUID is for the ID of the typelib if this project is exposed to COM

[assembly: Guid("be5d8156-3fd1-4958-8dba-9718f302ade4")]
#if DEBUG

[assembly: AssemblyConfiguration("Debug")]
#else
[assembly: AssemblyConfiguration("Release")]
#endif

[assembly: AssemblyTitle("PNConfigLib")]
[assembly: AssemblyCompany("Siemens AG")]
[assembly: AssemblyProduct("PNConfigLib")]
[assembly: AssemblyCopyright("\u00A9 Siemens AG, 2018-2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
#if DEBUG

[assembly: AssemblyDescription("PNConfigLib_BUILD_01.00.00.00_00.00.00.00 *********** debug")]
#else
[assembly: AssemblyDescription("PNConfigLib_BUILD_01.00.00.00_00.00.00.00 *********** release")]
#endif


// Setting ComVisible to false makes the types in this assembly not visible 
// to COM components.  If you need to access a type in this assembly from 
// COM, set the ComVisible attribute to true on that type.

[assembly: ComVisible(false)]
