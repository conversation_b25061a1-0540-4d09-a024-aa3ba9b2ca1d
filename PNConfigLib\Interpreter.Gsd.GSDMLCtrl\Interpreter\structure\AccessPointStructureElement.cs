/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: AccessPointStructureElement.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

using C = PNConfigLib.Gsd.Interpreter.Common;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Structure
{
    /// <summary>
    /// The AccessPointStructureElement object is the representative of an
    /// access point in the structural data object model.
    /// </summary>
    public class AccessPointStructureElement :
        StructureElement,
        GSDI.IAccessPointStructureElement	// Interface
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class. 
        /// </summary>
        public AccessPointStructureElement()
        {
            // Initialize the properties
            //m_Submodules = null;
            //m_Modules = null;
        }

        #endregion

        //########################################################################################
        #region Fields

        private ArrayList m_Submodules;
        private ArrayList m_Modules;
        private ArrayList m_CommunicationInterfaces;

        private string m_RequiredSchemaVersion = String.Empty;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of SubmoduleStructureElement objects, which are
        /// available from this access point.
        /// </summary>
        public virtual Array Submodules => this.m_Submodules?.ToArray();
            
        /// <summary>
        /// Accesses a list of ModuleStructureElement objects, which are
        /// available with this access point.
        /// </summary>
        public virtual Array Modules => this.m_Modules?.ToArray();
           

        /// <summary>
        /// Accesses a list of CommunicationInterfaceStructureElement objects, which are
        /// available from this DAP.
        /// </summary>
        public virtual Array CommunicationInterfaces => this.m_CommunicationInterfaces?.ToArray();
           

        /// <summary>
        /// Returns the category info text
        /// </summary>
        public virtual string CategoryInfotext => CategoryInfoText;
            
        

        /// <summary>
        /// Returns the subcategory1 info text
        /// </summary>
        public virtual string SubCategory1Infotext => SubCategory1InfoText;
            

        /// <summary>
        /// Returns the required schema version
        /// </summary>
        public virtual string RequiredSchemaVersion => m_RequiredSchemaVersion;
            
        protected new object IdentNumber => ((StructureElement)this).IdentNumber; 
        


        #region COM Interface Members Only

        #endregion

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldSubmodules;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Submodules = hash[member] as ArrayList;

                member = Models.s_FieldModules;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Modules = hash[member] as ArrayList;

                member = Models.s_FieldCommunicationInterfaces;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_CommunicationInterfaces = hash[member] as ArrayList;

                // Base data.
                succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectAccessPointStructureElement);

            // ----------------------------------------------
            bool succeeded = this.SerializeMembers(option, ref writer);
            if (!succeeded)
                throw new SerializationException("Couldn't serialize members!");

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            // From base class
            bool succeeded = base.SerializeMembers(option, ref writer);
            if (!succeeded)
                throw new SerializationException("Couldn't serialize members!");

            // ----------------------------------------------
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldSubmodules, this.m_Submodules);
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldModules, this.m_Modules);
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldCommunicationInterfaces, this.m_CommunicationInterfaces);

            return true;
        }

        #endregion

    }
}


