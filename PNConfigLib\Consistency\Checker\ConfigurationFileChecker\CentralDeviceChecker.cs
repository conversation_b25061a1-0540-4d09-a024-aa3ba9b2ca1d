/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDeviceChecker.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class CentralDeviceChecker : IConsistencyChecker
    {
        /// <summary>
        /// Keeps Central Device Type
        /// </summary>
        private List<CentralDeviceType> m_CentralDevices;

        /// <summary>
        /// Keeps subnets of the configuration file
        /// </summary>
        private List<Subnet> m_Subnets;

        /// <summary>
        /// Keeps ListOfNodes file
        /// </summary>
        private readonly ListOfNodes m_ListOfNodes;

        private readonly string m_ListOfNodesPath;

        public CentralDeviceChecker(List<CentralDeviceType> centralDevices, List<Subnet> subnets, ListOfNodes lon, string listOfNodesPath)
        {
            m_CentralDevices = centralDevices;
            m_Subnets = subnets;
            m_ListOfNodes = lon;
            m_ListOfNodesPath = listOfNodesPath;
        }

        /// <summary>
        /// Start point for the central device checks
        /// </summary>
        /// <returns></returns>
        public void Check()
        {
            // Call related check methods
            IsCentralDeviceSyncRoleValid();
            DoesSubnetExistForCentralDevice();
            IsIOSystemFoundAndUniqueForIOC();
            IsSpecifiedSyncDomainFound();
            ArePermitOverridingAndSupportDeviceReplacementValid();
            IsIrtSupportedForIOControllerInterface();
            CheckExpertMode();
            CheckCentralDeviceSyncRole();
            IsSendClockValid();
        }

        private void IsCentralDeviceSyncRoleValid()
        {
            foreach (CentralDeviceType centralDevice in m_CentralDevices)
            {
                if (centralDevice?.CentralDeviceInterface?.AdvancedOptions?.RealTimeSettings?.Synchronization == null)
                {
                    continue;
                }

                SyncRole currentSyncRole = centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;

                if (currentSyncRole == SyncRole.Unsynchronized)
                {
                    continue;
                }

                PNDriverType pndriver =
                    ListOfNodesChecker.GetListOfNodesCentralDeviceById(centralDevice.DeviceRefID, m_ListOfNodes);

                if (pndriver == null)
                {
                    continue;
                }

                CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                    pndriver.Interface.InterfaceType,
                    pndriver.Interface.CustomInterfacePath,
                    pndriver.DeviceVersion,
                    m_ListOfNodesPath
                   );

                Enumerated supportedSyncRolesEnumerated =
                    pndCatalog.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSyncRoleSupp,
                        new AttributeAccessCode(),
                        null);

                if (supportedSyncRolesEnumerated == null)
                {
                    continue;
                }

                List<PNIRTSyncRole> supportedSyncRoles =
                    supportedSyncRolesEnumerated.List.Cast<PNIRTSyncRole>().ToList();

                if (!supportedSyncRoles.Contains(AttributeUtilities.MapSyncRoleEnum(currentSyncRole)))
                {
                    string interfaceIdPath = StringOperations.CombineParametersWithBackSlash(
                        centralDevice.DeviceRefID,
                        centralDevice.CentralDeviceInterface.InterfaceRefID);

                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_InvalidSyncRole,
                        currentSyncRole,
                        interfaceIdPath);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsSpecifiedSyncDomainFound()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                string deviceRefId = xmlCentralDevice.DeviceRefID;
                string interfaceRefId = xmlCentralDevice.CentralDeviceInterface.InterfaceRefID;
                string subnetRefId = xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID;
                string syncDomainRefId = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SyncDomainRefID;
                Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == subnetRefId);
                if (!xmlSubnet.DomainManagement.SyncDomains.Exists(e => e.SyncDomainID == syncDomainRefId))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SpecifiedSyncDomainNotFound,
                        syncDomainRefId,
                        deviceRefId,
                        interfaceRefId);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckExpertMode()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {              
                Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID);
                if (xmlSubnet == null || xmlSubnet.DomainManagement == null)
                {
                    continue;
                }
                PNDriverType pnDriver = m_ListOfNodes.PNDriver.Find(w => w.DeviceID == xmlCentralDevice.DeviceRefID);
                FwVersion fwVersion = HWCNBL.Utilities.AttributeUtilities.MapVersion(pnDriver.DeviceVersion);
                CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                pnDriver.Interface.InterfaceType,
                pnDriver.Interface.CustomInterfacePath,
                pnDriver.DeviceVersion,
                m_ListOfNodesPath);

                // Get PnIoSuppFrameClass to check whether the central device supports IRT.
                Enumerated supportedFrames = pndCatalog.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    new AttributeAccessCode(),
                    null);
               
                foreach (SyncDomainType syncDomain in xmlSubnet.DomainManagement.SyncDomains)
                {
                    // Check if the High Performance/Expert Mode is activated and the device version is above V2.1 or the device supports IRT and High Performance/Expert Mode is activated.
                    if ((fwVersion != FwVersion.Undefined
                        && syncDomain.ActivateHighPerformance
                        && fwVersion <= FwVersion.V2_1
                        && pnDriver.Interface.InterfaceType != PNDriverInterfaceEnum.Custom)
                        || (!supportedFrames.List.Contains((int)PNIOFrameClass.Class3Frame)
                        && syncDomain.ActivateHighPerformance == true))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_RestrictedVersionForExpertMode,
                            pnDriver.Interface.InterfaceType,
                            pnDriver.DeviceVersion);
                        throw new ConsistencyCheckException();
                    }

                    // Check if the High Performance/Expert Mode is activated before Fast Forwarding
                    if (syncDomain.ActivateHighPerformance == false
                        && syncDomain.PermitUsageOfFastForwarding == true)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.ExpertModeRequiredForFastForwarding);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void IsIrtSupportedForIOControllerInterface()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                SyncRole syncRole = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;

                PNDriverType pnDriver = m_ListOfNodes.PNDriver.Find(w => w.DeviceID == xmlCentralDevice.DeviceRefID);
                CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                    pnDriver.Interface.InterfaceType,
                    pnDriver.Interface.CustomInterfacePath,
                    pnDriver.DeviceVersion,
                    m_ListOfNodesPath);
                Enumerated supportedFrames = pndCatalog.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    new AttributeAccessCode(),
                    null);
                if ((syncRole != SyncRole.Unsynchronized)
                    && !supportedFrames.List.Contains((int)PNIOFrameClass.Class3Frame))
                {
                    string interfaceType = pnDriver.Interface.InterfaceType.ToString();

                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IRTNotSupportedForIOControllerInterface,
                        interfaceType,
                        xmlCentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void ArePermitOverridingAndSupportDeviceReplacementValid()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                CentralDeviceInterfaceOptionsType centralDeviceInterfaceOptions =
                    xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.InterfaceOptions;
                if (!centralDeviceInterfaceOptions.SupportDeviceReplacementWithoutExchangeableMedium
                    && centralDeviceInterfaceOptions.PermitOverwritingOfDeviceNamesOfAllAssignedIODevices)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement,
                        xmlCentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckCentralDeviceSyncRole()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                SyncRole xmlSyncRole = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;

                if (xmlSyncRole == SyncRole.Unsynchronized)
                {
                    continue;
                }

                PNIRTSyncRole currentSyncRole = AttributeUtilities.MapSyncRoleEnum(xmlSyncRole);
                var lonDevice =
                    m_ListOfNodes.PNDriver.FirstOrDefault(d => d.DeviceID == xmlCentralDevice.DeviceRefID);

                if (lonDevice == null)
                {
                    continue;
                }

                CentralDeviceCatalog catalog = Catalog.GetCentralDeviceCatalog(
                    lonDevice.Interface.InterfaceType,
                    lonDevice.Interface.CustomInterfacePath,
                    lonDevice.DeviceVersion,
                    m_ListOfNodesPath);

                Enumerated supportedSyncRolesEnumerated =
                    catalog.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSyncRoleSupp,
                        new AttributeAccessCode(),
                        null);

                List<SyncRole> supportedSyncRoles =
                    supportedSyncRolesEnumerated.List.Cast<SyncRole>().ToList();

                List<PNIRTSyncRole> mappedSupportedSyncRoles = new List<PNIRTSyncRole>();
                foreach (SyncRole syncRole in supportedSyncRoles)
                {
                    mappedSupportedSyncRoles.Add(AttributeUtilities.MapSyncRoleEnum(syncRole));
                }

                if (mappedSupportedSyncRoles.Contains(currentSyncRole))
                {
                    continue;
                }

                string interfaceIdPath = StringOperations.CombineParametersWithBackSlash(
                    xmlCentralDevice.DeviceRefID,
                    xmlCentralDevice.CentralDeviceInterface.InterfaceRefID);

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InvalidSyncRole,
                    xmlSyncRole,
                    interfaceIdPath);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSendClockValid()
        {
            foreach (SyncDomainType syncDomain in m_Subnets.SelectMany(s => s.DomainManagement.SyncDomains))
            {
                List<CentralDeviceType> centralDevicesOfSyncDomain = m_CentralDevices.FindAll(
                    c => c.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization.SyncDomainRefID
                         == syncDomain.SyncDomainID);

                foreach (CentralDeviceType centralDevice in centralDevicesOfSyncDomain)
                {
                    CentralAdvancedOptionsTypeRealTimeSettings realTimeSettings =
                        centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings;

                    IsSendClockSet(centralDevice, syncDomain, realTimeSettings);

                    if (realTimeSettings.Synchronization.SynchronizationRole != SyncRole.Unsynchronized) // IRT
                    {
                        IsIRTSendClockSetUnderRightElement(centralDevice, syncDomain, realTimeSettings);
                        IsSendClockValueValidForIRT(centralDevice, syncDomain);
                    }
                    else // RT
                    {
                        bool isIrtConfiguredCentralDeviceExistInSyncDomain = centralDevicesOfSyncDomain.Any(
                            cd => cd.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization.SynchronizationRole
                                  != SyncRole.Unsynchronized);

                        IsRTSendClockSetUnderRightElement(
                            centralDevice,
                            syncDomain,
                            realTimeSettings,
                            isIrtConfiguredCentralDeviceExistInSyncDomain);
                        IsSendClockValueValidForRT(centralDevice, syncDomain);
                    }
                }
            }
        }

        public void IsSendClockValueValidForRT(CentralDeviceType centralDevice, SyncDomainType syncDomain)
        {

            PNDriverType pndriver =
                ListOfNodesChecker.GetListOfNodesCentralDeviceById(centralDevice.DeviceRefID, m_ListOfNodes);

            if (pndriver == null)
            {
                return;
            }

            CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                pndriver.Interface.InterfaceType,
                pndriver.Interface.CustomInterfacePath,
                pndriver.DeviceVersion,
                m_ListOfNodesPath);

            List<long> sendClockList = PNAttributeUtility.GetSupportedSendClockFactorList(pndCatalog.Interface, PNIOFrameClass.Class1Frame);

            if (centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClockSpecified)
            {
                if (!sendClockList.Contains((long)centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClock * 32))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_InvalidSendClock,
                        centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClock,
                        centralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
            else 
            {
                if (!sendClockList.Contains((long)(syncDomain.SendClock * 32)))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_InvalidSendClock,
                        centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClock,
                        centralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
            
        }

        public void IsSendClockValueValidForIRT(CentralDeviceType centralDevice, SyncDomainType syncDomain)
        {
            PNDriverType pndriver =
                ListOfNodesChecker.GetListOfNodesCentralDeviceById(centralDevice.DeviceRefID, m_ListOfNodes);

            if (pndriver == null)
            {
                return;
            }

            CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                pndriver.Interface.InterfaceType,
                pndriver.Interface.CustomInterfacePath,
                pndriver.DeviceVersion,
                m_ListOfNodesPath);

            List<long> sendClockList = PNAttributeUtility.GetSupportedSendClockFactorList(pndCatalog.Interface, PNIOFrameClass.Class3Frame);

            if (!sendClockList.Contains((long)(syncDomain.SendClock * 32)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InvalidSendClock,
                    syncDomain.SendClock,
                    centralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSendClockSet(CentralDeviceType centralDevice, SyncDomainType syncDomain, CentralAdvancedOptionsTypeRealTimeSettings realTimeSettings)
        {
            if (!realTimeSettings.IOCommunication.SendClockSpecified
                && !syncDomain.SendClockSpecified)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SendClockNotSet,
                    centralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsRTSendClockSetUnderRightElement(CentralDeviceType centralDevice, SyncDomainType syncDomain, CentralAdvancedOptionsTypeRealTimeSettings realTimeSettings, bool isIrtConfiguredCentralDeviceExistInSyncDomain)
        {
            if (isIrtConfiguredCentralDeviceExistInSyncDomain)
            {
                if (realTimeSettings.IOCommunication.SendClockSpecified)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SendClockRtAndIrtInSameSyncDomain,
                        syncDomain.SyncDomainID,
                        centralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
            else
            {
                if (syncDomain.SendClockSpecified)
                {
                    if (realTimeSettings.IOCommunication.SendClockSpecified)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Warning,
                            string.Empty,
                            ConsistencyConstants.XML_SendClockAlreadySetInDevice,
                            centralDevice.DeviceRefID);
                    }
                    else
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SendClockOfRtDeviceWrongLocation,
                            centralDevice.DeviceRefID);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void IsIRTSendClockSetUnderRightElement(CentralDeviceType centralDevice, SyncDomainType syncDomain, CentralAdvancedOptionsTypeRealTimeSettings realTimeSettings)
        {
            if (realTimeSettings.IOCommunication.SendClockSpecified)
            {
                if (syncDomain.SendClockSpecified)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Warning,
                        string.Empty,
                        ConsistencyConstants.XML_SendClockAlreadyGivenInSyncDomain,
                        centralDevice.DeviceRefID);
                }
                else
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SendClockMustBeUnderSyncDomain,
                        syncDomain.SyncDomainID,
                        centralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void DoesSubnetExistForCentralDevice()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                string deviceRefId = xmlCentralDevice.DeviceRefID;
                string interfaceRefId = xmlCentralDevice.CentralDeviceInterface.InterfaceRefID;
                string subnetRefId = xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID;
                Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == subnetRefId);
                if (xmlSubnet == null)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SubnetNotFoundForIOC,
                        subnetRefId,
                        deviceRefId,
                        interfaceRefId);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsIOSystemFoundAndUniqueForIOC()
        {
            IOSystemChecker.GetIOSystemIDs().Clear();
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                string deviceRefId = xmlCentralDevice.DeviceRefID;
                string interfaceRefId = xmlCentralDevice.CentralDeviceInterface.InterfaceRefID;
                string subnetRefId = xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID;
                Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == subnetRefId);
                string ioSystemRefId = xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID;
                if (!string.IsNullOrEmpty(ioSystemRefId))
                {
                    if (!xmlSubnet.IOSystem.Exists(e => e.IOSystemID == ioSystemRefId))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_IoSystemNotFounfForIOC,
                            ioSystemRefId,
                            deviceRefId,
                            interfaceRefId);
                        throw new ConsistencyCheckException();
                    }

                    if (IOSystemChecker.GetIOSystemIDs().Contains(ioSystemRefId))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_MultipleIOControllersInIOSystem,
                            ioSystemRefId,
                            deviceRefId,
                            interfaceRefId);
                        throw new ConsistencyCheckException();
                    }

                    IOSystemChecker.GetIOSystemIDs().Add(ioSystemRefId);
                }
            }
        }
    }
}
