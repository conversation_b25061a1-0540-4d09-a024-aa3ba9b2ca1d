// ------------------------------------------------------------------------------
//  <auto-generated>
//    Generated by Xsd2Code. Version 3.4.0.32989
//    <NameSpace>PNConfigLib.ConfigReader.Configuration</NameSpace><Collection>List</Collection><codeType>CSharp</codeType><EnableDataBinding>False</EnableDataBinding><EnableLazyLoading>False</EnableLazyLoading><TrackingChangesEnable>False</TrackingChangesEnable><GenTrackingClasses>False</GenTrackingClasses><HidePrivateFieldInIDE>False</HidePrivateFieldInIDE><EnableSummaryComment>False</EnableSummaryComment><VirtualProp>False</VirtualProp><IncludeSerializeMethod>False</IncludeSerializeMethod><UseBaseClass>False</UseBaseClass><GenBaseClass>False</GenBaseClass><GenerateCloneMethod>False</GenerateCloneMethod><GenerateDataContracts>False</GenerateDataContracts><CodeBaseTag>Net20</CodeBaseTag><SerializeMethodName>Serialize</SerializeMethodName><DeserializeMethodName>Deserialize</DeserializeMethodName><SaveToFileMethodName>SaveToFile</SaveToFileMethodName><LoadFromFileMethodName>LoadFromFile</LoadFromFileMethodName><GenerateXMLAttributes>True</GenerateXMLAttributes><OrderXMLAttrib>False</OrderXMLAttrib><EnableEncoding>False</EnableEncoding><AutomaticProperties>False</AutomaticProperties><GenerateShouldSerialize>False</GenerateShouldSerialize><DisableDebug>False</DisableDebug><ProPNameSpecified>Default</ProPNameSpecified><Encoder>UTF8</Encoder><CustomUsings></CustomUsings><ExcludeIncludedTypes>False</ExcludeIncludedTypes><EnableInitializeFields>True</EnableInitializeFields>
//  </auto-generated>
// ------------------------------------------------------------------------------
namespace PNConfigLib.ConfigReader.Configuration {
    using System;
    using System.Diagnostics;
    using System.Xml.Serialization;
    using System.Collections;
    using System.Xml.Schema;
    using System.ComponentModel;
    using System.Collections.Generic;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute("Configuration", Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public partial class Configuration {
        
        private Devices devicesField;
        
        private List<Subnet> subnetField;
        
        private string configurationIDField;
        
        private string configurationNameField;
        
        private string configurationDescriptionField;
        
        private string topologyRefIDField;
        
        private string listOfNodesRefIDField;
        
        private string schemaVersionField;
        
        public Configuration() {
            this.subnetField = new List<Subnet>();
            this.devicesField = new Devices();
            this.schemaVersionField = "1.0";
        }
        
        public Devices Devices {
            get {
                return this.devicesField;
            }
            set {
                this.devicesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("Subnet")]
        public List<Subnet> Subnet {
            get {
                return this.subnetField;
            }
            set {
                this.subnetField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string ConfigurationID {
            get {
                return this.configurationIDField;
            }
            set {
                this.configurationIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ConfigurationName {
            get {
                return this.configurationNameField;
            }
            set {
                this.configurationNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ConfigurationDescription {
            get {
                return this.configurationDescriptionField;
            }
            set {
                this.configurationDescriptionField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TopologyRefID {
            get {
                return this.topologyRefIDField;
            }
            set {
                this.topologyRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ListOfNodesRefID {
            get {
                return this.listOfNodesRefIDField;
            }
            set {
                this.listOfNodesRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string schemaVersion {
            get {
                return this.schemaVersionField;
            }
            set {
                this.schemaVersionField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public partial class Devices {
        
        private List<CentralDeviceType> centralDeviceField;
        
        private List<DecentralDeviceType> decentralDeviceField;
        
        public Devices() {
            this.decentralDeviceField = new List<DecentralDeviceType>();
            this.centralDeviceField = new List<CentralDeviceType>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("CentralDevice")]
        public List<CentralDeviceType> CentralDevice {
            get {
                return this.centralDeviceField;
            }
            set {
                this.centralDeviceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("DecentralDevice")]
        public List<DecentralDeviceType> DecentralDevice {
            get {
                return this.decentralDeviceField;
            }
            set {
                this.decentralDeviceField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralDeviceType {
        
        private GeneralType generalField;
        
        private CentralDeviceTypeCentralDeviceInterface centralDeviceInterfaceField;
        
        private CustomizationType customizationField;

        private SnmpType snmpField;

        private string deviceRefIDField;
        
        public CentralDeviceType() {
            this.customizationField = new CustomizationType();
            this.centralDeviceInterfaceField = new CentralDeviceTypeCentralDeviceInterface();
            this.snmpField = new SnmpType();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        public CentralDeviceTypeCentralDeviceInterface CentralDeviceInterface {
            get {
                return this.centralDeviceInterfaceField;
            }
            set {
                this.centralDeviceInterfaceField = value;
            }
        }
        
        public CustomizationType Customization {
            get {
                return this.customizationField;
            }
            set {
                this.customizationField = value;
            }
        }

        public SnmpType Snmp
        {
            get
            {
                return this.snmpField;
            }
            set
            {
                this.snmpField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceRefID {
            get {
                return this.deviceRefIDField;
            }
            set {
                this.deviceRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute("General", Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public partial class GeneralType {
        
        private string authorField;
        
        private string commentField;
        
        private string nameField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Author {
            get {
                return this.authorField;
            }
            set {
                this.authorField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Comment {
            get {
                return this.commentField;
            }
            set {
                this.commentField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class MrpDomainType {
        
        private string mrpDomainIDField;
        
        private string mrpDomainNameField;
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string MrpDomainID {
            get {
                return this.mrpDomainIDField;
            }
            set {
                this.mrpDomainIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string MrpDomainName {
            get {
                return this.mrpDomainNameField;
            }
            set {
                this.mrpDomainNameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class SyncDomainType {
        
        private SyncDomainTypeDetails detailsField;
        
        private string syncDomainIDField;
        
        private string syncDomainNameField;
        
        private float sendClockField;
        
        private bool sendClockFieldSpecified;
        
        private bool permitUsageOfFastForwardingField;
        
        private bool activateHighPerformanceField;
        
        public SyncDomainType() {
            this.detailsField = new SyncDomainTypeDetails();
            this.permitUsageOfFastForwardingField = false;
            this.activateHighPerformanceField = false;
        }
        
        public SyncDomainTypeDetails Details {
            get {
                return this.detailsField;
            }
            set {
                this.detailsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string SyncDomainID {
            get {
                return this.syncDomainIDField;
            }
            set {
                this.syncDomainIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SyncDomainName {
            get {
                return this.syncDomainNameField;
            }
            set {
                this.syncDomainNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float SendClock {
            get {
                return this.sendClockField;
            }
            set {
                this.sendClockField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SendClockSpecified {
            get {
                return this.sendClockFieldSpecified;
            }
            set {
                this.sendClockFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool PermitUsageOfFastForwarding {
            get {
                return this.permitUsageOfFastForwardingField;
            }
            set {
                this.permitUsageOfFastForwardingField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool ActivateHighPerformance {
            get {
                return this.activateHighPerformanceField;
            }
            set {
                this.activateHighPerformanceField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SyncDomainTypeDetails {
        
        private BandwidthUse bandwidthUseField;
        
        public SyncDomainTypeDetails() {
            this.bandwidthUseField = BandwidthUse.Maximum50cyclicIOdataBalancedproportion;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(BandwidthUse.Maximum50cyclicIOdataBalancedproportion)]
        public BandwidthUse BandwidthUse {
            get {
                return this.bandwidthUseField;
            }
            set {
                this.bandwidthUseField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public enum BandwidthUse {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Maximum 25% cyclic IO data. Focus on non cyclic data")]
        Maximum25cyclicIOdataFocusonnonecyclicdata,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Maximum 37,5% cyclic IO data. Focus on non cyclic data")]
        Maximum375cyclicIOdataFocusonnonecyclicdata,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Maximum 50% cyclic IO data. Balanced proportion")]
        Maximum50cyclicIOdataBalancedproportion,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Maximum 90% cyclic IO data. Focus on cyclic data")]
        Maximum90cyclicIOdataFocusoncyclicdata,

        [System.Xml.Serialization.XmlEnumAttribute("Always 90% cyclic IO data. Focus on cyclic data")]
        Always90cyclicdataFocusoncyclicdata,

    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class ModuleType {
        
        private GeneralType generalField;
        
        private List<object> iOAddressesField;
        
        private List<ModuleTypeSubmodule> submoduleField;
        
        private List<PortType> portField;
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemsField;
        
        private string moduleIDField;
        
        private ushort slotNumberField;
        
        private string gSDRefIDField;
        
        public ModuleType() {
            this.parameterRecordDataItemsField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
            this.portField = new List<PortType>();
            this.submoduleField = new List<ModuleTypeSubmodule>();
            this.iOAddressesField = new List<object>();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("InputAddresses", typeof(ModuleTypeInputAddresses), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("OutputAddresses", typeof(ModuleTypeOutputAddresses), IsNullable=false)]
        public List<object> IOAddresses {
            get {
                return this.iOAddressesField;
            }
            set {
                this.iOAddressesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("Submodule")]
        public List<ModuleTypeSubmodule> Submodule {
            get {
                return this.submoduleField;
            }
            set {
                this.submoduleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("Port")]
        public List<PortType> Port {
            get {
                return this.portField;
            }
            set {
                this.portField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("ParameterRecordDataItem", IsNullable=false)]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItems {
            get {
                return this.parameterRecordDataItemsField;
            }
            set {
                this.parameterRecordDataItemsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ModuleID {
            get {
                return this.moduleIDField;
            }
            set {
                this.moduleIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SlotNumber {
            get {
                return this.slotNumberField;
            }
            set {
                this.slotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string GSDRefID {
            get {
                return this.gSDRefIDField;
            }
            set {
                this.gSDRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class ModuleTypeInputAddresses {
        
        private uint startAddressField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public uint StartAddress {
            get {
                return this.startAddressField;
            }
            set {
                this.startAddressField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class ModuleTypeOutputAddresses {
        
        private uint startAddressField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public uint StartAddress {
            get {
                return this.startAddressField;
            }
            set {
                this.startAddressField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class ModuleTypeSubmodule {
        
        private GeneralType generalField;
        
        private List<object> iOAddressesField;
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemsField;
        
        private string submoduleIDField;
        
        private ushort subslotNumberField;
        
        private string gSDRefIDField;
        
        public ModuleTypeSubmodule() {
            this.parameterRecordDataItemsField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
            this.iOAddressesField = new List<object>();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("InputAddresses", typeof(ModuleTypeInputAddresses), IsNullable=false)]
        [System.Xml.Serialization.XmlArrayItemAttribute("OutputAddresses", typeof(ModuleTypeOutputAddresses), IsNullable=false)]
        public List<object> IOAddresses {
            get {
                return this.iOAddressesField;
            }
            set {
                this.iOAddressesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("ParameterRecordDataItem", IsNullable=false)]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItems {
            get {
                return this.parameterRecordDataItemsField;
            }
            set {
                this.parameterRecordDataItemsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubmoduleID {
            get {
                return this.submoduleIDField;
            }
            set {
                this.submoduleIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SubslotNumber {
            get {
                return this.subslotNumberField;
            }
            set {
                this.subslotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string GSDRefID {
            get {
                return this.gSDRefIDField;
            }
            set {
                this.gSDRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class ParameterRecordDataItemsTypeParameterRecordDataItem {
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItemRef> refField;
        
        private ushort gSDRefIndexField;
        
        public ParameterRecordDataItemsTypeParameterRecordDataItem() {
            this.refField = new List<ParameterRecordDataItemsTypeParameterRecordDataItemRef>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("Ref")]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItemRef> Ref {
            get {
                return this.refField;
            }
            set {
                this.refField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort GSDRefIndex {
            get {
                return this.gSDRefIndexField;
            }
            set {
                this.gSDRefIndexField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class ParameterRecordDataItemsTypeParameterRecordDataItemRef {
        
        private uint byteOffsetField;
        
        private int bitOffsetField;
        
        private bool bitOffsetFieldSpecified;
        
        private string valueField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public uint ByteOffset {
            get {
                return this.byteOffsetField;
            }
            set {
                this.byteOffsetField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int BitOffset {
            get {
                return this.bitOffsetField;
            }
            set {
                this.bitOffsetField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BitOffsetSpecified {
            get {
                return this.bitOffsetFieldSpecified;
            }
            set {
                this.bitOffsetFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class PortType {
        
        private GeneralType generalField;
        
        private PortTypePortOptions portOptionsField;
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemsField;
        
        private byte portNumberField;
        
        private string gSDRefIDField;
        
        private ushort subslotNumberField;
        
        private bool subslotNumberFieldSpecified;
        
        public PortType() {
            this.parameterRecordDataItemsField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
            this.portOptionsField = new PortTypePortOptions();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        public PortTypePortOptions PortOptions {
            get {
                return this.portOptionsField;
            }
            set {
                this.portOptionsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("ParameterRecordDataItem", IsNullable=false)]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItems {
            get {
                return this.parameterRecordDataItemsField;
            }
            set {
                this.parameterRecordDataItemsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public byte PortNumber {
            get {
                return this.portNumberField;
            }
            set {
                this.portNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string GSDRefID {
            get {
                return this.gSDRefIDField;
            }
            set {
                this.gSDRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SubslotNumber {
            get {
                return this.subslotNumberField;
            }
            set {
                this.subslotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SubslotNumberSpecified {
            get {
                return this.subslotNumberFieldSpecified;
            }
            set {
                this.subslotNumberFieldSpecified = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class PortTypePortOptions {
        
        private TransmissionRate transmissionRateField;
        
        private bool monitorField;
        
        private bool activateThisPortForUseField;
        
        private bool enableAutonegotiationField;
        
        private bool endOfDetectionOfAccessibleDevicesField;
        
        private bool endOfTheSyncDomainField;
        
        private bool endOfTopologyDiscoveryField;
        
        public PortTypePortOptions() {
            this.transmissionRateField = TransmissionRate.Automatic;
            this.monitorField = false;
            this.activateThisPortForUseField = true;
            this.enableAutonegotiationField = true;
            this.endOfDetectionOfAccessibleDevicesField = false;
            this.endOfTheSyncDomainField = false;
            this.endOfTopologyDiscoveryField = false;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(TransmissionRate.Automatic)]
        public TransmissionRate TransmissionRate {
            get {
                return this.transmissionRateField;
            }
            set {
                this.transmissionRateField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool Monitor {
            get {
                return this.monitorField;
            }
            set {
                this.monitorField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(true)]
        public bool ActivateThisPortForUse {
            get {
                return this.activateThisPortForUseField;
            }
            set {
                this.activateThisPortForUseField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(true)]
        public bool EnableAutonegotiation {
            get {
                return this.enableAutonegotiationField;
            }
            set {
                this.enableAutonegotiationField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool EndOfDetectionOfAccessibleDevices {
            get {
                return this.endOfDetectionOfAccessibleDevicesField;
            }
            set {
                this.endOfDetectionOfAccessibleDevicesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool EndOfTheSyncDomain {
            get {
                return this.endOfTheSyncDomainField;
            }
            set {
                this.endOfTheSyncDomainField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool EndOfTopologyDiscovery {
            get {
                return this.endOfTopologyDiscoveryField;
            }
            set {
                this.endOfTopologyDiscoveryField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public enum TransmissionRate {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TP 10-Mbps Half Duplex")]
        TP10MbpsHalfDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TP 10-Mbps Full Duplex")]
        TP10MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("AsyncFiber 10-Mbps Half Duplex")]
        AsyncFiber10MbpsHalfDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("AsyncFiber 10-Mbps Full Duplex")]
        AsyncFiber10MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TP 100-Mbps Half Duplex")]
        TP100MbpsHalfDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TP 100-Mbps Full Duplex")]
        TP100MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("POF/PCF 100-Mbps Full Duplex")]
        POFPCF100MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO 100-Mbps Full Duplex")]
        FO100MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("X 1000-Mbps Full Duplex")]
        X1000MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO 1000-Mbps Full Duplex")]
        FO1000MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO 1000-Mbps Full Duplex LD")]
        FO1000MbpsFullDuplexLD,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("TP 1000-Mbps Full Duplex")]
        TP1000MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO 10000-Mbps Full Duplex")]
        FO10000MbpsFullDuplex,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO 100-Mbps Full Duplex LD")]
        FO100MbpsFullDuplexLD,
        
        /// <remarks/>
        Automatic,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class IsochronousModeType {
        
        private IsochronousModeTypeTiToValues tiToValuesField;
        
        private List<IsochronousModeTypeIsochronousSubmodule> isochronousSubmoduleField;
        
        private bool isoModeEnabledField;
        
        public IsochronousModeType() {
            this.isochronousSubmoduleField = new List<IsochronousModeTypeIsochronousSubmodule>();
            this.tiToValuesField = new IsochronousModeTypeTiToValues();
        }
        
        public IsochronousModeTypeTiToValues TiToValues {
            get {
                return this.tiToValuesField;
            }
            set {
                this.tiToValuesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("IsochronousSubmodule")]
        public List<IsochronousModeTypeIsochronousSubmodule> IsochronousSubmodule {
            get {
                return this.isochronousSubmoduleField;
            }
            set {
                this.isochronousSubmoduleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public bool IsoModeEnabled {
            get {
                return this.isoModeEnabledField;
            }
            set {
                this.isoModeEnabledField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class IsochronousModeTypeTiToValues {
        
        private object itemField;
        
        [System.Xml.Serialization.XmlElementAttribute("AutomaticMinimum", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("Manual", typeof(IsochronousModeTypeTiToValuesManual))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class IsochronousModeTypeTiToValuesManual {
        
        private float timeTiField;
        
        private float timeToField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float TimeTi {
            get {
                return this.timeTiField;
            }
            set {
                this.timeTiField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float TimeTo {
            get {
                return this.timeToField;
            }
            set {
                this.timeToField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class IsochronousModeTypeIsochronousSubmodule {
        
        private string moduleRefIDField;
        
        private string submoduleRefIDField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ModuleRefID {
            get {
                return this.moduleRefIDField;
            }
            set {
                this.moduleRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubmoduleRefID {
            get {
                return this.submoduleRefIDField;
            }
            set {
                this.submoduleRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class IOCycleType {
        
        private IOCycleTypeSharedDevicePart sharedDevicePartField;
        
        private IOCycleTypeUpdateTime updateTimeField;
        
        private uint acceptedUpdateCyclesWithoutIODataField;
        
        public IOCycleType() {
            this.updateTimeField = new IOCycleTypeUpdateTime();
            this.sharedDevicePartField = new IOCycleTypeSharedDevicePart();
            this.acceptedUpdateCyclesWithoutIODataField = ((uint)(3));
        }
        
        public IOCycleTypeSharedDevicePart SharedDevicePart {
            get {
                return this.sharedDevicePartField;
            }
            set {
                this.sharedDevicePartField = value;
            }
        }
        
        public IOCycleTypeUpdateTime UpdateTime {
            get {
                return this.updateTimeField;
            }
            set {
                this.updateTimeField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(typeof(uint), "3")]
        public uint AcceptedUpdateCyclesWithoutIOData {
            get {
                return this.acceptedUpdateCyclesWithoutIODataField;
            }
            set {
                this.acceptedUpdateCyclesWithoutIODataField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class IOCycleTypeSharedDevicePart {
        
        private uint iOControllerOutsideProjectWithAccessToThisIODeviceField;
        
        private float iODeviceSendClockField;
        
        private bool iODeviceSendClockFieldSpecified;
        
        public IOCycleTypeSharedDevicePart() {
            this.iOControllerOutsideProjectWithAccessToThisIODeviceField = ((uint)(0));
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(typeof(uint), "0")]
        public uint IOControllerOutsideProjectWithAccessToThisIODevice {
            get {
                return this.iOControllerOutsideProjectWithAccessToThisIODeviceField;
            }
            set {
                this.iOControllerOutsideProjectWithAccessToThisIODeviceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float IODeviceSendClock {
            get {
                return this.iODeviceSendClockField;
            }
            set {
                this.iODeviceSendClockField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IODeviceSendClockSpecified {
            get {
                return this.iODeviceSendClockFieldSpecified;
            }
            set {
                this.iODeviceSendClockFieldSpecified = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class IOCycleTypeUpdateTime {
        
        private IOCycleTypeUpdateTimeMode modeField;

        private bool modeFieldSpecified;
        
        private float valueField;
        
        private bool valueFieldSpecified;
        
        public IOCycleTypeUpdateTime() {
            this.modeField = IOCycleTypeUpdateTimeMode.Automatic;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(IOCycleTypeUpdateTimeMode.Automatic)]
        public IOCycleTypeUpdateTimeMode Mode {
            get {
                return this.modeField;
            }
            set {
                this.modeField = value;
            }
        }

        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ModeSpecified {
            get {
                return this.modeFieldSpecified;
            }
            set {
                this.modeFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValueSpecified {
            get {
                return this.valueFieldSpecified;
            }
            set {
                this.valueFieldSpecified = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public enum IOCycleTypeUpdateTimeMode {
        
        /// <remarks/>
        Automatic,
        
        /// <remarks/>
        Manual,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralDeviceInterfaceOptionsType {
        
        private bool optionalIODeviceField;
        
        private bool prioritizedStartupField;
        
        private bool useIECV22LLDPModeField;

        public DecentralDeviceInterfaceOptionsType() {
            this.optionalIODeviceField = false;
            this.prioritizedStartupField = false;
            this.useIECV22LLDPModeField = false;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool OptionalIODevice {
            get {
                return this.optionalIODeviceField;
            }
            set {
                this.optionalIODeviceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool PrioritizedStartup {
            get {
                return this.prioritizedStartupField;
            }
            set {
                this.prioritizedStartupField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute("UseIECV2.2LLDPMode")]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool UseIECV22LLDPMode {
            get {
                return this.useIECV22LLDPModeField;
            }
            set {
                this.useIECV22LLDPModeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralAdvancedOptionsType {
        
        private DecentralDeviceInterfaceOptionsType interfaceOptionsField;
        
        private List<PortType> portsField;
        
        private List<MrpRingType> mediaRedundancyField;
        
        private DecentralAdvancedOptionsTypeRealTimeSettings realTimeSettingsField;
        
        public DecentralAdvancedOptionsType() {
            this.realTimeSettingsField = new DecentralAdvancedOptionsTypeRealTimeSettings();
            this.mediaRedundancyField = new List<MrpRingType>();
            this.portsField = new List<PortType>();
            this.interfaceOptionsField = new DecentralDeviceInterfaceOptionsType();
        }
        
        public DecentralDeviceInterfaceOptionsType InterfaceOptions {
            get {
                return this.interfaceOptionsField;
            }
            set {
                this.interfaceOptionsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("Port", IsNullable=false)]
        public List<PortType> Ports {
            get {
                return this.portsField;
            }
            set {
                this.portsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("MrpRing", IsNullable=false)]
        public List<MrpRingType> MediaRedundancy {
            get {
                return this.mediaRedundancyField;
            }
            set {
                this.mediaRedundancyField = value;
            }
        }
        
        public DecentralAdvancedOptionsTypeRealTimeSettings RealTimeSettings {
            get {
                return this.realTimeSettingsField;
            }
            set {
                this.realTimeSettingsField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class MrpRingType {
        
        private List<MrpRingTypeRingPort> ringPortField;
        
        private int instanceNumberField;
        
        private bool instanceNumberFieldSpecified;
        
        private string mrpDomainRefIDField;
        
        private MrpRole mrpRoleField;
        
        private bool diagnosticsInterruptsField;
        
        public MrpRingType() {
            this.ringPortField = new List<MrpRingTypeRingPort>();
            this.mrpRoleField = MrpRole.Notdeviceinthering;
            this.diagnosticsInterruptsField = false;
        }
        
        [System.Xml.Serialization.XmlElementAttribute("RingPort")]
        public List<MrpRingTypeRingPort> RingPort {
            get {
                return this.ringPortField;
            }
            set {
                this.ringPortField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public int InstanceNumber {
            get {
                return this.instanceNumberField;
            }
            set {
                this.instanceNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool InstanceNumberSpecified {
            get {
                return this.instanceNumberFieldSpecified;
            }
            set {
                this.instanceNumberFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string MrpDomainRefID {
            get {
                return this.mrpDomainRefIDField;
            }
            set {
                this.mrpDomainRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(MrpRole.Notdeviceinthering)]
        public MrpRole MrpRole {
            get {
                return this.mrpRoleField;
            }
            set {
                this.mrpRoleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool DiagnosticsInterrupts {
            get {
                return this.diagnosticsInterruptsField;
            }
            set {
                this.diagnosticsInterruptsField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class MrpRingTypeRingPort {
        
        private ushort slotNumberField;
        
        private bool slotNumberFieldSpecified;
        
        private byte portNumberField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SlotNumber {
            get {
                return this.slotNumberField;
            }
            set {
                this.slotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SlotNumberSpecified {
            get {
                return this.slotNumberFieldSpecified;
            }
            set {
                this.slotNumberFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public byte PortNumber {
            get {
                return this.portNumberField;
            }
            set {
                this.portNumberField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public enum MrpRole {
        
        /// <remarks/>
        MrpClient,
        
        /// <remarks/>
        MrpManager,
        
        /// <remarks/>
        MrpAutoManager,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Not device in the ring")]
        Notdeviceinthering,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralAdvancedOptionsTypeRealTimeSettings {
        
        private DecentralAdvancedOptionsTypeRealTimeSettingsIOCycle iOCycleField;
        
        private DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization synchronizationField;
        
        public DecentralAdvancedOptionsTypeRealTimeSettings() {
            this.synchronizationField = new DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization();
            this.iOCycleField = new DecentralAdvancedOptionsTypeRealTimeSettingsIOCycle();
        }
        
        public DecentralAdvancedOptionsTypeRealTimeSettingsIOCycle IOCycle {
            get {
                return this.iOCycleField;
            }
            set {
                this.iOCycleField = value;
            }
        }
        
        public DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization Synchronization {
            get {
                return this.synchronizationField;
            }
            set {
                this.synchronizationField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralAdvancedOptionsTypeRealTimeSettingsIOCycle : IOCycleType {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization {
        
        private SyncRole synchronizationRoleField;
        
        private string syncDomainRefIDField;
        
        public DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization() {
            this.synchronizationRoleField = SyncRole.Unsynchronized;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(SyncRole.Unsynchronized)]
        public SyncRole SynchronizationRole {
            get {
                return this.synchronizationRoleField;
            }
            set {
                this.synchronizationRoleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SyncDomainRefID {
            get {
                return this.syncDomainRefIDField;
            }
            set {
                this.syncDomainRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public enum SyncRole {
        
        /// <remarks/>
        SyncMaster,
        
        /// <remarks/>
        SyncSlave,
        
        /// <remarks/>
        RedundantSyncMaster,
        
        /// <remarks/>
        Unsynchronized,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralDeviceNameType {
        
        private string itemField;
        
        private uint deviceNumberField;
        
        [System.Xml.Serialization.XmlElementAttribute("PNDeviceName")]
        public string Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public uint DeviceNumber {
            get {
                return this.deviceNumberField;
            }
            set {
                this.deviceNumberField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralIPProtocolType {
        
        private object itemField;
        
        private ItemChoiceType itemElementNameField;
        
        [System.Xml.Serialization.XmlElementAttribute("SetByTheIOController", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("SetDirectlyAtTheDevice", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("SetInTheProject", typeof(DecentralIPProtocolTypeSetInTheProject))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType ItemElementName {
            get {
                return this.itemElementNameField;
            }
            set {
                this.itemElementNameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralIPProtocolTypeSetInTheProject {
        
        private string routerAddressField;
        
        private string iPAddressField;
        
        private string subnetMaskField;
        
        private bool synchronizeRouterSettingsWithIOControllerField;
        
        public DecentralIPProtocolTypeSetInTheProject() {
            this.routerAddressField = "0.0.0.0";
            this.synchronizeRouterSettingsWithIOControllerField = true;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute("0.0.0.0")]
        public string RouterAddress {
            get {
                return this.routerAddressField;
            }
            set {
                this.routerAddressField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IPAddress {
            get {
                return this.iPAddressField;
            }
            set {
                this.iPAddressField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubnetMask {
            get {
                return this.subnetMaskField;
            }
            set {
                this.subnetMaskField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(true)]
        public bool SynchronizeRouterSettingsWithIOController {
            get {
                return this.synchronizeRouterSettingsWithIOControllerField;
            }
            set {
                this.synchronizeRouterSettingsWithIOControllerField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IncludeInSchema=false)]
    public enum ItemChoiceType {
        
        /// <remarks/>
        SetByTheIOController,
        
        /// <remarks/>
        SetDirectlyAtTheDevice,
        
        /// <remarks/>
        SetInTheProject,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralDeviceEthernetAddressesType {
        
        private DecentralIPProtocolType iPProtocolField;
        
        private DecentralDeviceEthernetAddressesTypePROFINETDeviceName pROFINETDeviceNameField;
        
        private string iOSystemRefIDField;
        
        private string subnetRefIDField;
        
        public DecentralDeviceEthernetAddressesType() {
            this.pROFINETDeviceNameField = new DecentralDeviceEthernetAddressesTypePROFINETDeviceName();
            this.iPProtocolField = new DecentralIPProtocolType();
        }
        
        public DecentralIPProtocolType IPProtocol {
            get {
                return this.iPProtocolField;
            }
            set {
                this.iPProtocolField = value;
            }
        }
        
        public DecentralDeviceEthernetAddressesTypePROFINETDeviceName PROFINETDeviceName {
            get {
                return this.pROFINETDeviceNameField;
            }
            set {
                this.pROFINETDeviceNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IOSystemRefID {
            get {
                return this.iOSystemRefIDField;
            }
            set {
                this.iOSystemRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubnetRefID {
            get {
                return this.subnetRefIDField;
            }
            set {
                this.subnetRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralDeviceEthernetAddressesTypePROFINETDeviceName : DecentralDeviceNameType {
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class DecentralDeviceType {
        
        private GeneralType generalField;
        
        private DecentralDeviceTypeDecentralDeviceInterface decentralDeviceInterfaceField;
        
        private List<ModuleType> moduleField;
        
        private List<SharedDeviceTypeAssignedIOController> sharedDeviceField;
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemsField;

        private AdvancedConfigurationType advancedConfigurationField;

        private string deviceRefIDField;
        
        public DecentralDeviceType() {
            this.parameterRecordDataItemsField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
            this.sharedDeviceField = new List<SharedDeviceTypeAssignedIOController>();
            this.moduleField = new List<ModuleType>();
            this.decentralDeviceInterfaceField = new DecentralDeviceTypeDecentralDeviceInterface();
            this.generalField = new GeneralType();
            this.advancedConfigurationField = new AdvancedConfigurationType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        public DecentralDeviceTypeDecentralDeviceInterface DecentralDeviceInterface {
            get {
                return this.decentralDeviceInterfaceField;
            }
            set {
                this.decentralDeviceInterfaceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("Module")]
        public List<ModuleType> Module {
            get {
                return this.moduleField;
            }
            set {
                this.moduleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("AssignedIOController", IsNullable=false)]
        public List<SharedDeviceTypeAssignedIOController> SharedDevice {
            get {
                return this.sharedDeviceField;
            }
            set {
                this.sharedDeviceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("ParameterRecordDataItem", IsNullable=false)]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItems {
            get {
                return this.parameterRecordDataItemsField;
            }
            set {
                this.parameterRecordDataItemsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceRefID {
            get {
                return this.deviceRefIDField;
            }
            set {
                this.deviceRefIDField = value;
            }
        }

        [System.Xml.Serialization.XmlElementAttribute("AdvancedConfiguration")]
        public AdvancedConfigurationType AdvancedConfiguration
        {
            get
            {
                return this.advancedConfigurationField;
            }
            set
            {
                this.advancedConfigurationField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class DecentralDeviceTypeDecentralDeviceInterface : IConfigInterface {
        
        private GeneralType generalField;
        
        private DecentralDeviceEthernetAddressesType ethernetAddressesField;
        
        private DecentralAdvancedOptionsType advancedOptionsField;
        
        private IsochronousModeType isochronousModeField;
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemsField;
        
        private string interfaceRefIDField;
        
        public DecentralDeviceTypeDecentralDeviceInterface() {
            this.parameterRecordDataItemsField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
            this.isochronousModeField = new IsochronousModeType();
            this.advancedOptionsField = new DecentralAdvancedOptionsType();
            this.ethernetAddressesField = new DecentralDeviceEthernetAddressesType();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        public DecentralDeviceEthernetAddressesType EthernetAddresses {
            get {
                return this.ethernetAddressesField;
            }
            set {
                this.ethernetAddressesField = value;
            }
        }
        
        public DecentralAdvancedOptionsType AdvancedOptions {
            get {
                return this.advancedOptionsField;
            }
            set {
                this.advancedOptionsField = value;
            }
        }
        
        public IsochronousModeType IsochronousMode {
            get {
                return this.isochronousModeField;
            }
            set {
                this.isochronousModeField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("ParameterRecordDataItem", IsNullable=false)]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItems {
            get {
                return this.parameterRecordDataItemsField;
            }
            set {
                this.parameterRecordDataItemsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceRefID {
            get {
                return this.interfaceRefIDField;
            }
            set {
                this.interfaceRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SharedDeviceTypeAssignedIOController {
        
        private List<SharedDeviceTypeAssignedIOControllerSharedModule> sharedModuleField;
        
        private bool isPDEVSharedField;
        
        private string deviceRefIDField;
        
        private string interfaceRefIDField;
        
        public SharedDeviceTypeAssignedIOController() {
            this.sharedModuleField = new List<SharedDeviceTypeAssignedIOControllerSharedModule>();
            this.isPDEVSharedField = false;
        }
        
        [System.Xml.Serialization.XmlElementAttribute("SharedModule")]
        public List<SharedDeviceTypeAssignedIOControllerSharedModule> SharedModule {
            get {
                return this.sharedModuleField;
            }
            set {
                this.sharedModuleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool IsPDEVShared {
            get {
                return this.isPDEVSharedField;
            }
            set {
                this.isPDEVSharedField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceRefID {
            get {
                return this.deviceRefIDField;
            }
            set {
                this.deviceRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceRefID {
            get {
                return this.interfaceRefIDField;
            }
            set {
                this.interfaceRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SharedDeviceTypeAssignedIOControllerSharedModule {
        
        private List<SharedDeviceTypeAssignedIOControllerSharedModuleSharedSubmodule> sharedSubmoduleField;
        
        private string moduleRefIDField;
        
        public SharedDeviceTypeAssignedIOControllerSharedModule() {
            this.sharedSubmoduleField = new List<SharedDeviceTypeAssignedIOControllerSharedModuleSharedSubmodule>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("SharedSubmodule")]
        public List<SharedDeviceTypeAssignedIOControllerSharedModuleSharedSubmodule> SharedSubmodule {
            get {
                return this.sharedSubmoduleField;
            }
            set {
                this.sharedSubmoduleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ModuleRefID {
            get {
                return this.moduleRefIDField;
            }
            set {
                this.moduleRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SharedDeviceTypeAssignedIOControllerSharedModuleSharedSubmodule {
        
        private string submoduleRefIDField;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubmoduleRefID {
            get {
                return this.submoduleRefIDField;
            }
            set {
                this.submoduleRefIDField = value;
            }
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class AdvancedConfigurationType
    {
        private SnmpType snmpField;

        private DcpType dcpField;

        public AdvancedConfigurationType()
        {
            this.snmpField = new SnmpType();
            this.dcpField = new DcpType();
        }
        public SnmpType Snmp
        {
            get
            {
                return this.snmpField;
            }
            set
            {
                this.snmpField = value;
            }
        }
        public DcpType Dcp
        {
            get
            {
                return this.dcpField;
            }
            set
            {
                this.dcpField = value;
            }
        }

    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CustomizationType {
        
        private ushort pNVendorIDField;
        
        private ushort pNDeviceIDField;
        
        public CustomizationType() {
            this.pNVendorIDField = ((ushort)(42));
            this.pNDeviceIDField = ((ushort)(8));
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(typeof(ushort), "42")]
        public ushort PNVendorID {
            get {
                return this.pNVendorIDField;
            }
            set {
                this.pNVendorIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(typeof(ushort), "8")]
        public ushort PNDeviceID {
            get {
                return this.pNDeviceIDField;
            }
            set {
                this.pNDeviceIDField = value;
            }
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable = true)]
    public partial class SnmpType
    {
        private bool snmpEnabledField;
        private bool snmpEnableReadOnlyField;
        private string snmpReadOnlyCommunityNameField;
        private string snmpReadWriteCommunityNameField;

        public SnmpType()
        {
            this.snmpEnabledField = false;
            this.snmpEnableReadOnlyField = false;
            this.snmpReadOnlyCommunityNameField = "public";
            this.snmpReadWriteCommunityNameField = "private";
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool SNMPEnabled
        {
            get
            {
                return this.snmpEnabledField;
            }
            set
            {
                this.snmpEnabledField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool SNMPEnableReadOnly
        {
            get
            {
                return this.snmpEnableReadOnlyField;
            }
            set
            {
                this.snmpEnableReadOnlyField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute("public")]
        public string SNMPReadOnlyCommunityName
        {
            get
            {
                return this.snmpReadOnlyCommunityNameField;
            }
            set
            {
                this.snmpReadOnlyCommunityNameField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute("private")]
        public string SNMPReadWriteCommunityName
        {
            get
            {
                return this.snmpReadWriteCommunityNameField;
            }
            set
            {
                this.snmpReadWriteCommunityNameField = value;
            }
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(
        Namespace = "http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(
        Namespace = "http://www.siemens.com/Automation/PNConfigLib/Configuration",
        IsNullable = true)]
    public partial class DcpType
    {
        private bool activateDcpReadOnlyField;

        public DcpType()
        {
            this.activateDcpReadOnlyField = false;
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool ActivateDcpReadOnly
        {
            get
            {
                return this.activateDcpReadOnlyField;
            }
            set
            {
                this.activateDcpReadOnlyField = value;
            }
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralDeviceInterfaceOptionsType {
        
        private bool permitOverwritingOfDeviceNamesOfAllAssignedIODevicesField;
        
        private bool useIECV22LLDPModeField;
        
        private bool supportDeviceReplacementWithoutExchangeableMediumField;

        private bool KeepApplicationRelationAtCommunicationErrorField;

        public CentralDeviceInterfaceOptionsType() {
            this.permitOverwritingOfDeviceNamesOfAllAssignedIODevicesField = false;
            this.useIECV22LLDPModeField = false;
            this.supportDeviceReplacementWithoutExchangeableMediumField = true;
            this.KeepApplicationRelationAtCommunicationErrorField = false;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool PermitOverwritingOfDeviceNamesOfAllAssignedIODevices {
            get {
                return this.permitOverwritingOfDeviceNamesOfAllAssignedIODevicesField;
            }
            set {
                this.permitOverwritingOfDeviceNamesOfAllAssignedIODevicesField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute("UseIECV2.2LLDPMode")]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool UseIECV22LLDPMode {
            get {
                return this.useIECV22LLDPModeField;
            }
            set {
                this.useIECV22LLDPModeField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(true)]
        public bool SupportDeviceReplacementWithoutExchangeableMedium {
            get {
                return this.supportDeviceReplacementWithoutExchangeableMediumField;
            }
            set {
                this.supportDeviceReplacementWithoutExchangeableMediumField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool KeepApplicationRelationAtCommunicationError
        {
            get
            {
                return this.KeepApplicationRelationAtCommunicationErrorField;
            }
            set
            {
                this.KeepApplicationRelationAtCommunicationErrorField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralAdvancedOptionsType {
        
        private CentralDeviceInterfaceOptionsType interfaceOptionsField;
        
        private List<PortType> portsField;
        
        private List<MrpRingType> mediaRedundancyField;
        
        private CentralAdvancedOptionsTypeIsochronousMode isochronousModeField;
        
        private CentralAdvancedOptionsTypeRealTimeSettings realTimeSettingsField;
        
        public CentralAdvancedOptionsType() {
            this.realTimeSettingsField = new CentralAdvancedOptionsTypeRealTimeSettings();
            this.isochronousModeField = new CentralAdvancedOptionsTypeIsochronousMode();
            this.mediaRedundancyField = new List<MrpRingType>();
            this.portsField = new List<PortType>();
            this.interfaceOptionsField = new CentralDeviceInterfaceOptionsType();
        }
        
        public CentralDeviceInterfaceOptionsType InterfaceOptions {
            get {
                return this.interfaceOptionsField;
            }
            set {
                this.interfaceOptionsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("Port", IsNullable=false)]
        public List<PortType> Ports {
            get {
                return this.portsField;
            }
            set {
                this.portsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("MrpRing", IsNullable=false)]
        public List<MrpRingType> MediaRedundancy {
            get {
                return this.mediaRedundancyField;
            }
            set {
                this.mediaRedundancyField = value;
            }
        }
        
        public CentralAdvancedOptionsTypeIsochronousMode IsochronousMode {
            get {
                return this.isochronousModeField;
            }
            set {
                this.isochronousModeField = value;
            }
        }
        
        public CentralAdvancedOptionsTypeRealTimeSettings RealTimeSettings {
            get {
                return this.realTimeSettingsField;
            }
            set {
                this.realTimeSettingsField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralAdvancedOptionsTypeIsochronousMode {
        
        private object itemField;
        
        private float applicationCycleField;
        
        private bool applicationCycleFieldSpecified;
        
        [System.Xml.Serialization.XmlElementAttribute("AutomaticSetting", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("DelayTime", typeof(float))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float ApplicationCycle {
            get {
                return this.applicationCycleField;
            }
            set {
                this.applicationCycleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ApplicationCycleSpecified {
            get {
                return this.applicationCycleFieldSpecified;
            }
            set {
                this.applicationCycleFieldSpecified = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralAdvancedOptionsTypeRealTimeSettings {
        
        private CentralAdvancedOptionsTypeRealTimeSettingsIOCommunication iOCommunicationField;
        
        private CentralAdvancedOptionsTypeRealTimeSettingsSynchronization synchronizationField;
        
        public CentralAdvancedOptionsTypeRealTimeSettings() {
            this.synchronizationField = new CentralAdvancedOptionsTypeRealTimeSettingsSynchronization();
            this.iOCommunicationField = new CentralAdvancedOptionsTypeRealTimeSettingsIOCommunication();
        }
        
        public CentralAdvancedOptionsTypeRealTimeSettingsIOCommunication IOCommunication {
            get {
                return this.iOCommunicationField;
            }
            set {
                this.iOCommunicationField = value;
            }
        }
        
        public CentralAdvancedOptionsTypeRealTimeSettingsSynchronization Synchronization {
            get {
                return this.synchronizationField;
            }
            set {
                this.synchronizationField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralAdvancedOptionsTypeRealTimeSettingsIOCommunication {
        
        private float sendClockField;
        
        private bool sendClockFieldSpecified;
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public float SendClock {
            get {
                return this.sendClockField;
            }
            set {
                this.sendClockField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SendClockSpecified {
            get {
                return this.sendClockFieldSpecified;
            }
            set {
                this.sendClockFieldSpecified = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralAdvancedOptionsTypeRealTimeSettingsSynchronization {
        
        private SyncRole synchronizationRoleField;
        
        private string syncDomainRefIDField;
        
        public CentralAdvancedOptionsTypeRealTimeSettingsSynchronization() {
            this.synchronizationRoleField = SyncRole.Unsynchronized;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(SyncRole.Unsynchronized)]
        public SyncRole SynchronizationRole {
            get {
                return this.synchronizationRoleField;
            }
            set {
                this.synchronizationRoleField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SyncDomainRefID {
            get {
                return this.syncDomainRefIDField;
            }
            set {
                this.syncDomainRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralDeviceNameType {
        
        private object itemField;
        
        [System.Xml.Serialization.XmlElementAttribute("DeviceNameIsSetDirectlyAtTheDevice", typeof(bool))]
        [System.Xml.Serialization.XmlElementAttribute("PNDeviceName", typeof(string))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralIPProtocolType {
        
        private object itemField;
        
        [System.Xml.Serialization.XmlElementAttribute("SetDirectlyAtTheDevice", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("SetInTheProject", typeof(CentralIPProtocolTypeSetInTheProject))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralIPProtocolTypeSetInTheProject {
        
        private string routerAddressField;
        
        private string iPAddressField;
        
        private string subnetMaskField;
        
        public CentralIPProtocolTypeSetInTheProject() {
            this.routerAddressField = "0.0.0.0";
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute("0.0.0.0")]
        public string RouterAddress {
            get {
                return this.routerAddressField;
            }
            set {
                this.routerAddressField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IPAddress {
            get {
                return this.iPAddressField;
            }
            set {
                this.iPAddressField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubnetMask {
            get {
                return this.subnetMaskField;
            }
            set {
                this.subnetMaskField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class CentralDeviceEthernetAddressesType {
        
        private CentralIPProtocolType iPProtocolField;
        
        private CentralDeviceNameType pROFINETDeviceNameField;
        
        private string iOSystemRefIDField;
        
        private string subnetRefIDField;
        
        public CentralDeviceEthernetAddressesType() {
            this.pROFINETDeviceNameField = new CentralDeviceNameType();
            this.iPProtocolField = new CentralIPProtocolType();
        }
        
        public CentralIPProtocolType IPProtocol {
            get {
                return this.iPProtocolField;
            }
            set {
                this.iPProtocolField = value;
            }
        }
        
        public CentralDeviceNameType PROFINETDeviceName {
            get {
                return this.pROFINETDeviceNameField;
            }
            set {
                this.pROFINETDeviceNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IOSystemRefID {
            get {
                return this.iOSystemRefIDField;
            }
            set {
                this.iOSystemRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SubnetRefID {
            get {
                return this.subnetRefIDField;
            }
            set {
                this.subnetRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class CentralDeviceTypeCentralDeviceInterface : IConfigInterface {
        
        private GeneralType generalField;
        
        private CentralDeviceEthernetAddressesType ethernetAddressesField;
        
        private CentralAdvancedOptionsType advancedOptionsField;
        
        private string interfaceRefIDField;
        
        public CentralDeviceTypeCentralDeviceInterface() {
            this.advancedOptionsField = new CentralAdvancedOptionsType();
            this.ethernetAddressesField = new CentralDeviceEthernetAddressesType();
            this.generalField = new GeneralType();
        }
        
        public GeneralType General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        public CentralDeviceEthernetAddressesType EthernetAddresses {
            get {
                return this.ethernetAddressesField;
            }
            set {
                this.ethernetAddressesField = value;
            }
        }
        
        public CentralAdvancedOptionsType AdvancedOptions {
            get {
                return this.advancedOptionsField;
            }
            set {
                this.advancedOptionsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceRefID {
            get {
                return this.interfaceRefIDField;
            }
            set {
                this.interfaceRefIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public partial class Subnet {
        
        private List<SubnetIOSystem> iOSystemField;
        
        private SubnetDomainManagement domainManagementField;
        
        private string subnetIDField;
        
        public Subnet() {
            this.domainManagementField = new SubnetDomainManagement();
            this.iOSystemField = new List<SubnetIOSystem>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("IOSystem")]
        public List<SubnetIOSystem> IOSystem {
            get {
                return this.iOSystemField;
            }
            set {
                this.iOSystemField = value;
            }
        }
        
        public SubnetDomainManagement DomainManagement {
            get {
                return this.domainManagementField;
            }
            set {
                this.domainManagementField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string SubnetID {
            get {
                return this.subnetIDField;
            }
            set {
                this.subnetIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SubnetIOSystem {
        
        private SubnetIOSystemGeneral generalField;
        
        private string iOSystemIDField;
        
        public SubnetIOSystem() {
            this.generalField = new SubnetIOSystemGeneral();
        }
        
        public SubnetIOSystemGeneral General {
            get {
                return this.generalField;
            }
            set {
                this.generalField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string IOSystemID {
            get {
                return this.iOSystemIDField;
            }
            set {
                this.iOSystemIDField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SubnetIOSystemGeneral {
        
        private bool multipleUseIOSystemField;
        
        private ushort iOSystemNumberField;
        
        private bool iOSystemNumberFieldSpecified;
        
        private string iOSystemNameField;
        
        public SubnetIOSystemGeneral() {
            this.multipleUseIOSystemField = false;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(false)]
        public bool MultipleUseIOSystem {
            get {
                return this.multipleUseIOSystemField;
            }
            set {
                this.multipleUseIOSystemField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort IOSystemNumber {
            get {
                return this.iOSystemNumberField;
            }
            set {
                this.iOSystemNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool IOSystemNumberSpecified {
            get {
                return this.iOSystemNumberFieldSpecified;
            }
            set {
                this.iOSystemNumberFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string IOSystemName {
            get {
                return this.iOSystemNameField;
            }
            set {
                this.iOSystemNameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    public partial class SubnetDomainManagement {
        
        private List<SyncDomainType> syncDomainsField;
        
        private List<MrpDomainType> mrpDomainsField;
        
        public SubnetDomainManagement() {
            this.mrpDomainsField = new List<MrpDomainType>();
            this.syncDomainsField = new List<SyncDomainType>();
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("SyncDomain", IsNullable=false)]
        public List<SyncDomainType> SyncDomains {
            get {
                return this.syncDomainsField;
            }
            set {
                this.syncDomainsField = value;
            }
        }
        
        [System.Xml.Serialization.XmlArrayItemAttribute("MrpDomain", IsNullable=false)]
        public List<MrpDomainType> MrpDomains {
            get {
                return this.mrpDomainsField;
            }
            set {
                this.mrpDomainsField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute("ParameterRecordDataItems", Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=false)]
    public partial class ParameterRecordDataItemsType {
        
        private List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItemField;
        
        public ParameterRecordDataItemsType() {
            this.parameterRecordDataItemField = new List<ParameterRecordDataItemsTypeParameterRecordDataItem>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("ParameterRecordDataItem")]
        public List<ParameterRecordDataItemsTypeParameterRecordDataItem> ParameterRecordDataItem {
            get {
                return this.parameterRecordDataItemField;
            }
            set {
                this.parameterRecordDataItemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Configuration", IsNullable=true)]
    public partial class SharedDeviceType {
        
        private List<SharedDeviceTypeAssignedIOController> assignedIOControllerField;
        
        public SharedDeviceType() {
            this.assignedIOControllerField = new List<SharedDeviceTypeAssignedIOController>();
        }
        
        [System.Xml.Serialization.XmlElementAttribute("AssignedIOController")]
        public List<SharedDeviceTypeAssignedIOController> AssignedIOController {
            get {
                return this.assignedIOControllerField;
            }
            set {
                this.assignedIOControllerField = value;
            }
        }
    }
}
