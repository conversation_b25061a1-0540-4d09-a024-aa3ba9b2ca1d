/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Help.cs                                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Reflection;
using System.Resources;
using System.Text.RegularExpressions;
using System.Threading;
using System.Xml.XPath;
using System.Xml;
using System.Collections.Generic;
using System.Globalization;
using System.Xml.Linq;
using System.Linq;
using PNConfigLib.Gsd.Interpreter.Checker;
using System.Collections.Specialized;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal static class Help
    {
        private static readonly Regex s_RegexCollapseWhite = new Regex(@"[ \t\n\r]+");

        //########################################################################################
        #region Version Helper Functions

        /// <summary>
        /// Check whether the given version is higher (newer) as the given refernce version.
        /// </summary>
        /// <param name="version">Version, which should be compared with the reference
        /// version (e.g. "V1.2")</param>
        /// <param name="referenceversion">Reference version to compare the given
        /// version to (e.g. "V1.0")</param>
        /// <exception cref="ArgumentNullException">is thrown, if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an invalid or empty version string</exception>
        /// <returns>True, if the version is higher (newer) as the reference version, else false.</returns>
        public static bool IsHigherVersion(string version, string referenceversion)
        {
            #region Argument Preconditions

            if (version == null)
                throw new ArgumentNullException(nameof(version));
            if (referenceversion == null)
                throw new ArgumentNullException(nameof(referenceversion));
            if (version.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(version));
            if (referenceversion.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(referenceversion));

            // Check for version (e.g. "V1.0")
            if (!s_RegularExpressionGsdmlVersion.IsMatch(version))
                return false;
            if (!s_RegularExpressionGsdmlVersion.IsMatch(referenceversion))
                throw new ArgumentException("Input parameter is invalid version string.", nameof(referenceversion));

            #endregion

            return string.Compare(version, referenceversion, StringComparison.OrdinalIgnoreCase) > 0;
        }

        /// <summary>
        /// Check whether the given version is higher (newer) or equal as the given 
        /// refernce version.
        /// </summary>
        /// <param name="version">Version, which should be compared with the reference
        /// version (e.g. "V1.2")</param>
        /// <param name="referenceversion">Reference version to compare the given
        /// version to (e.g. "V1.0")</param>
        /// <exception cref="ArgumentNullException">is thrown if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an invalid or empty version string</exception>
        /// <returns>True, if the version is higher (newer) or equal as the reference version,
        /// else false.</returns>
        public static bool IsHigherOrEqualVersion(string version, string referenceversion)
        {
            #region Argument Preconditions

            if (version == null)
                throw new ArgumentNullException(nameof(version));
            if (referenceversion == null)
                throw new ArgumentNullException(nameof(referenceversion));
            if (version.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(version));
            if (referenceversion.Length == 0)  // -> empty string

                throw new ArgumentException("The parameter can not be empty.", nameof(referenceversion));

            // Check for version (e.g. "V1.0")
            if (!s_RegularExpressionGsdmlVersion.IsMatch(version))
                throw new ArgumentException("Input parameter is invalid version string.", nameof(version));
            if (!s_RegularExpressionGsdmlVersion.IsMatch(referenceversion))
                throw new ArgumentException("Input parameter is invalid version string.", nameof(referenceversion));

            #endregion

            if (version.ToUpperInvariant() == referenceversion.ToUpperInvariant())
                return true;	// ------------>

            return IsHigherVersion(version, referenceversion);
        }

        public static string GetGsdmlVersionFromFilename(string filename)
        {
            if (filename == null)
            {
                return string.Empty;
            }

            string stringTemp = filename.Substring(filename.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal) + 1);
            return (stringTemp.Substring(0, stringTemp.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal))).ToUpperInvariant();
        }
        #endregion

        //########################################################################################
        #region Value List Helper Functions


        /// <summary>
        /// Checks if there a gaps in a ValueList (e.g. "0 1 3" or "0..2 4")
        /// </summary>
        /// <param name="values">string from GSD file which contains a ValueList</param>
        /// <returns>true, if a gap has been found</returns>
        public static bool ValueListHasGaps(string values)
        {
            if (null == values)
            {
                return false;
            }

            bool hasGaps = false;
            SortedDictionary<UInt32, UInt32> ranges = new SortedDictionary<UInt32, UInt32>();
            string[] tmpRanges = values.Split(' ');
            foreach (string range in tmpRanges)
            {
                GetRangesForValueListGaps(range, ranges);
            }
            UInt32? lastRangeEnd = null;
            foreach (UInt32 key in ranges.Keys)
            {
                if (lastRangeEnd != null)
                {
                    if (key - lastRangeEnd.Value > 1)
                    {
                        hasGaps = true;
                    }
                }
                lastRangeEnd = ranges[key];
            }
            return hasGaps;
        }
        private static void GetRangesForValueListGaps(string range, SortedDictionary<uint, uint> ranges)
        {
            int index = range.IndexOf("..", StringComparison.Ordinal);
            if (index > 0)
            {
                string[] tmp = Regex.Split(range, @"\.\.");
                UInt32 start, end;
                if (!UInt32.TryParse(tmp[0], NumberStyles.Any, CultureInfo.InvariantCulture, out start) || !UInt32.TryParse(tmp[1], NumberStyles.Any, CultureInfo.InvariantCulture, out end))    // For numeric overflow the error is reported elsewhere
                {
                    return;
                }

                if (!ranges.ContainsKey(start))
                    ranges.Add(start, end);
            }
            else
            {
                UInt32 val;
                if (!UInt32.TryParse(range, NumberStyles.Any, CultureInfo.InvariantCulture, out val))    // For numeric overflow the error is reported elsewhere
                {
                    return;
                }

                if (!ranges.ContainsKey(val))
                        ranges.Add(val, val);
                }
            
        }

        #endregion

        #region Token List Helper Functions

        /// <summary>
        /// Separates a string of combined string values to a list of strings.
        /// </summary>
        /// <param name="values">String of combined string values.</param>
        /// <exception cref="ArgumentNullException">is thrown if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an invalid or empty value list string</exception>
        /// <returns>List of valid strings.</returns>
        public static ArrayList SeparateTokenList(string values)
        {
            #region Argument Preconditions

            if (values == null)
                throw new ArgumentNullException(nameof(values));
            if (values.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(values));

            #endregion

            // Split incoming string to individual numbers in a list.
            ArrayList splitlist = new ArrayList(values.Split(Constants.s_Semicolon.ToCharArray()));

            return splitlist;
        }

        #endregion

        //########################################################################################
        #region Compatibility Helper Functions

        /// <summary>
        /// Gets the compatibility info, like compatible GSDML schema version, for the given
        /// element.
        /// </summary>
        /// <param name="nav">Navigator which points to a element, which can have compatibility 
        /// information.</param>
        /// <param name="originalversion">GSDML version to which the element is established.</param>
        /// <param name="supportedversion">GSDML version which is supported from the caller.</param>
        /// <param name="compatibilityversion">Out parameter which gives the compatibility
        /// version for the given element.</param>
        /// <param name="iscompatible">Out parameter which specifies, whether the element
        /// is compatible to the supported version. True, is compatible, false is not.</param>
        /// <returns>True if version compatibility finding succeeds, else false.</returns>
        public static bool TryGetCompatibilityInfo(XPathNavigator nav, string originalversion,
            string supportedversion, out string compatibilityversion, out bool iscompatible)
        {
            #region Argument Preconditions

            if (nav == null)
                throw new ArgumentNullException(nameof(nav));
            if (String.IsNullOrEmpty(originalversion))  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(originalversion));
            if (String.IsNullOrEmpty(supportedversion))  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(supportedversion));

            // Check for version (e.g. "V1.0")
            if (!Help.s_RegularExpressionGsdmlVersion.IsMatch(originalversion))
                throw new ArgumentException("Input parameter is invalid version string.", nameof(originalversion));
            if (!Help.s_RegularExpressionGsdmlVersion.IsMatch(supportedversion))
                throw new ArgumentException("Input parameter is invalid version string.", nameof(supportedversion));

            #endregion

            compatibilityversion = originalversion;
            iscompatible = true;

            string rsv = nav.GetAttribute(Attributes.s_RequiredSchemaVersion, String.Empty);
            if (!string.IsNullOrEmpty(rsv))
            {
                if (!s_RegularExpressionGsdmlVersion.IsMatch(rsv))
                    throw new InterpreterException("Attribute 'RequiredSchemaVersion' content is invalid version string!");
                compatibilityversion = rsv;
            }

            if (compatibilityversion == supportedversion)
                return true;   // --------->

            if (IsHigherVersion(compatibilityversion, supportedversion))
            {
                iscompatible = false;
                return true;   // ---------->
            }

            return true;
        }

        #endregion

        //########################################################################################
        #region Common Helper Functions
        /// <summary>
        /// Regex object for GSDML version string (e.g. "v1.0", "V2.2")
        /// </summary>
        public static readonly Regex s_RegularExpressionGsdmlVersion = new Regex(Constants.s_RegExVersion, RegexOptions.IgnoreCase);
        /// <summary>
        /// Regex object for GSDML file name (e.g. "gsdml-v2.0-siemens-et200pro-20050530.xml", "GSDML-V1.0-Siemens-ET200S-20040128.xml")
        /// </summary>
        public static readonly Regex s_RegularExpressionGsdmlName = new Regex(Constants.s_RegExGsdmlName, RegexOptions.IgnoreCase);
        /// <summary>
        /// Regex object for unsigned value list (e.g. "1..5 7 10 12..20")
        /// </summary>
        public static readonly Regex s_RegularExpressionUnsignedValueList = new Regex(Constants.s_RegExUnsignedValueList, RegexOptions.IgnoreCase);

        /// <summary>
        /// Get the correct boolean value for a given string, which should represent the value.
        /// </summary>
        /// <param name="stringvalue">String which should represent the boolean value (e.g. "false", 
        /// "true", "0", "1", ...).</param>
        /// <param name="defaultvalue">Default value for the boolean value, if the stringvalue is
        /// empty or illegal.</param>
        /// <exception cref="Exception">is thrown, in case of an unknown problem</exception>
        /// <returns>True, if the stringvalue represents true. False, if the stringvalue represents
        /// false. Else defaultvalue.</returns>
        public static bool GetBool(string stringvalue, bool defaultvalue)
        {
            // Check for input parameter.
            if (string.IsNullOrEmpty(stringvalue))
                return defaultvalue;    // ---------->

            bool retVal;
            try
            {
                retVal = XmlConvert.ToBoolean(stringvalue);
            }
            catch (FormatException)
            {
                retVal = defaultvalue;
            }

            return retVal;
        }

        #endregion

        //########################################################################################
        #region Checker Helper Functions


        /// <summary>
        /// Checks if the RequiredSchemaVersion of the node or its parent's nodes is supported by this
        /// version of the GSDML interpreter.
        /// </summary>
        /// <param name="node">Node which should be checked</param>
        /// <param name="supportedGSDMLVersion">supported GSDML version</param>
        /// <returns>true, if schema version of node is supported</returns>
        public static bool CheckSchemaVersion(XObject node, string supportedGSDMLVersion)
        {
            if (node == null)
                throw new ArgumentNullException(nameof(node));

            if (String.IsNullOrEmpty(supportedGSDMLVersion))
                throw new ArgumentException("supportedGSDMLVersion");
            try
            {
                XObject tmpNode = node;
                do
                {
                    if (tmpNode.NodeType == XmlNodeType.Element)
                    {
                        XElement element = (XElement)tmpNode;
                        var schemaVersion = element.Attribute("RequiredSchemaVersion");
                        if (schemaVersion != null)
                        {
                            if (IsHigherVersion(schemaVersion.Value, supportedGSDMLVersion))
                            {
                                return false;
                            }
                        }
                    }

                    tmpNode = tmpNode.Parent;
                } while (tmpNode != null);
            }
            catch (XPathException)
            {
            }
            return true;
        }

        /// <summary>
        /// Builds the XPath expression for the specified node within the GSDML document.
        /// </summary>
        /// <param name="node">Node for which the XPath expression should be built. It can be a element
        /// or attribute node.</param>
        /// <returns>The XPath expression string if it could be built, else an empty string.</returns>
        public static string GetXPath(XObject node)
        {
            string xpath = String.Empty;

            // NOTE: The XmlNode is needed for creating the xpath string!
            if (node == null)
                return xpath;

            try
            {
                if (node.NodeType == XmlNodeType.Attribute)
                {
                    XAttribute nodeAtt = (XAttribute)node;
                    GenerateXPath(nodeAtt.Parent, ref xpath);
                    if (!String.IsNullOrEmpty(xpath))
                        xpath += Constants.s_Slash + Constants.s_CommercialAt + nodeAtt.Name + Constants.s_SquareBracketLeft
                                 + Constants.s_Dot + Constants.s_EqualSign + Constants.s_Apostrophe + nodeAtt.Value
                                 + Constants.s_Apostrophe + Constants.s_SquareBracketRight;
                }
                else
                    GenerateXPath((XElement)node, ref xpath);
            }
            catch (ArgumentNullException)
            {
                xpath = String.Empty;
            }

            return xpath;
        }

        /// <summary>
        /// Builds the XPath expression recursively for the parent elements of the given
        /// node and concatenate the separate XPath parts.
        /// </summary>
        /// <param name="node">Node for which the XPath expression should be built. Only elements are
        /// expected.</param>
        /// <param name="xpath">Concatenated XPath expression string.</param>
        private static void GenerateXPath(XElement node, ref string xpath)
        {
            // NOTE: The XmlNode is needed for creating the xpath string!

            if (node == null)
                throw new ArgumentNullException(nameof(node));

            try
            {
                string element = node.Name.LocalName;
                if (CheckerObject.ElementDescriptions.ContainsKey(element))
                {
                    HandleElementDescription(node, ref xpath, element);
                }
                else
                {
                    HandleNoElementDescription(node, ref xpath, element);
                }
            }
            catch (ArgumentException)
            {
            }
        }

        private static void HandleElementDescription(XElement node, ref string xpath, string element)
        {
            string[] descriptions = CheckerObject.ElementDescriptions[element].Split(",".ToCharArray());
            string localxpath = GetLocalXPathFromDescriptions(node, descriptions);


            if (string.IsNullOrEmpty(localxpath))
                localxpath = node.Name.LocalName;

            if (!String.IsNullOrEmpty(xpath))
                localxpath += Constants.s_Slash;

            if (xpath == null)
            {
                return;
            }

            xpath = xpath.Insert(0, localxpath);

            if (IsXPathBeginningReached(element))
            {
                localxpath = Constants.s_DoubleSlash;
                xpath = xpath.Insert(0, localxpath);
            }
            else if (element != Elements.s_Iso15745Profile)
            {
                GenerateXPath(node.Parent, ref xpath);
            }
        }

        private static void HandleNoElementDescription(XElement node, ref string xpath, string element)
        {
            string localxpath = node.Name.LocalName;
            if (!String.IsNullOrEmpty(xpath))
                localxpath += Constants.s_Slash;

            if (xpath == null)
            {
                return;
            }

            xpath = xpath.Insert(0, localxpath);
            if (element != Elements.s_Iso15745Profile)
                GenerateXPath(node.Parent, ref xpath);
        }

        private static bool IsXPathBeginningReached(string element)
        {
            switch (element)
            {
                case "ProfileHeader":
                case "ProfileBody":
                case "DeviceAccessPointItem":
                case "ModuleItem":
                case "SubmoduleItem":
                case "SubmoduleList":
                case "ValueItem":
                case "ChannelDiagItem":
                case "ProfileChannelDiagItem":
                case "SystemDefinedChannelDiagItem":
                case "GraphicItem":
                case "CategoryItem":
                case "UnitDiagTypeItem":
                case "ProfileUnitDiagTypeItem":
                case "Language":
                case "PrimaryLanguage":
                case "LogBookEntryItem":
                    return true;
                default:
                    return false;
            }
        }

        private static string GetLocalXPathFromDescriptions(XElement node, string[] descriptions)
        {
            string localxpath = string.Empty;
            foreach (string description in descriptions)
            {
                bool addRequired = false;
                string attributeName = description;
                if (attributeName.Contains("+"))
                {
                    addRequired = true;
                    attributeName = attributeName.Remove(0, 1);
                }
                string attributeValue = GetAttributeValueFromXElement(node, attributeName);

                if (string.IsNullOrEmpty(attributeValue)
                    || (!string.IsNullOrEmpty(localxpath) && !addRequired))
                {
                    continue;
                }
                    if (string.IsNullOrEmpty(localxpath))
                        localxpath = node.Name.LocalName;

                    localxpath += Constants.s_SquareBracketLeft + Constants.s_CommercialAt +
                        attributeName + Constants.s_EqualSign + Constants.s_Apostrophe +
                        attributeValue + Constants.s_Apostrophe + Constants.s_SquareBracketRight;   
            }
            return localxpath;
        }
        public static uint GetBitLengthFromDataItemType(string type)
        {
            // Return bit length for each data item type.
            // If bit length = 0 is returned, the bit length is defined by
            // the Attribute 'Length' (for 'VisibleString' and 'OctetString').

            switch (type)
            {
                case Enums.s_Unsigned8:
                case Enums.s_Integer8:
                case Enums.s_Boolean:
                case Enums.s_Unsigned8S:
                    {
                        return 1 * 8;
                    }
                case Enums.s_Unsigned16:
                case Enums.s_Integer16:
                case Enums.s_Unsigned8Unsigned8:
                case Enums.s_Unsigned16S:
                case Enums.s_Integer16S:
                case Enums.s_N2:
                case Enums.s_V2:
                case Enums.s_L2:
                case Enums.s_R2:
                case Enums.s_T2:
                case Enums.s_D2:
                case Enums.s_E2:
                case Enums.s_X2:
                case Enums.s_Unipolar216:
                    {
                        return 2 * 8;
                    }
                case Enums.s_OctetString2Unsigned8:
                    {
                        return 3 * 8;
                    }
                case Enums.s_Float32:
                case Enums.s_Unsigned32:
                case Enums.s_Integer32:
                case Enums.s_TimeOfDayWithoutDateIndication:
                case Enums.s_TimeDifferenceWithoutDateIndication:
                case Enums.s_FMessageTrailer4Byte:
                case Enums.s_N4:
                case Enums.s_T4:
                case Enums.s_C4:
                case Enums.s_X4:
                    {
                        return 4 * 8;
                    }
                case Enums.s_Float32Unsigned8:
                case Enums.s_Float32Status8:
                case Enums.s_FMessageTrailer5Byte:
                case Enums.s_FMessageTrailer5ByteTypingError:
                    {
                        return 5 * 8;
                    }
                case Enums.s_TimeOfDayWithDateIndication:
                case Enums.s_TimeDifferenceWithDateIndication:
                    {
                        return 6 * 8;
                    }
                case Enums.s_Date:
                    {
                        return 7 * 8;
                    }
                case Enums.s_Float64:
                case Enums.s_Unsigned64:
                case Enums.s_Integer64:
                case Enums.s_NetworkTime:
                case Enums.s_NetworkTimeDifference:
                case Enums.s_TimeStampDifferenceShort:
                    {
                        return 8 * 8;
                    }
                case Enums.s_TimeStamp:
                case Enums.s_TimeStampDifference:
                    {
                        return 12 * 8;
                    }
                case Enums.s_VisibleString:
                case Enums.s_OctetString:
                case Enums.s_UnicodeString8:
                case Enums.s_String61131:
                case Enums.s_Wstring61131:
                case Enums.s_OctetStringS:
                    {
                        // Bit length is defined by the Attribute 'Length'.
                        break;
                    }
                default:
                    {
                        break;
                    }
            }

            return 0;
        }

        public static object GetValueByDataType(string val, string type, uint bitlength)
        {
            if (val == null)
                throw new ArgumentException("Parameter val must not be null or empty");

            if (String.IsNullOrEmpty(type))
                throw new ArgumentException("Parameter type must not be null or empty");

        

            // Return value with correct data type.

            try
            {
                switch (type)
                {
                    case Enums.s_Bit:
                        {
                            Byte v = XmlConvert.ToByte(val);

                            if (!(v == 0 || v == 1))
                            {
                                return null;
                            }

                            return v;
                        }
                    case Enums.s_BitArea:
                        {
                            uint internalvalue = XmlConvert.ToUInt16(val);
                            uint maxvalue = GetBitAreaMaximumValue(bitlength);

                            if (internalvalue > maxvalue)
                            {
                                return null;
                            }
                            return internalvalue;
                        }
                    case Enums.s_Unsigned8:
                        {
                            return XmlConvert.ToByte(val);
                        }
                    case Enums.s_Unsigned16:
                    case Enums.s_R2:
                    case Enums.s_T2:
                    case Enums.s_D2:
                        {
                            return XmlConvert.ToUInt16(val);
                        }
                    case Enums.s_Unsigned32:
                    case Enums.s_T4:
                        {
                            return XmlConvert.ToUInt32(val);
                        }
                    case Enums.s_Unsigned64:
                        {
                            return XmlConvert.ToUInt64(val);
                        }
                    case Enums.s_Integer8:
                        {
                            return XmlConvert.ToSByte(val);
                        }
                    case Enums.s_Integer16:
                    case Enums.s_N2:
                    case Enums.s_X2:
                        {
                            return XmlConvert.ToInt16(val);
                        }
                    case Enums.s_Integer32:
                    case Enums.s_N4:
                    case Enums.s_X4:
                        {
                            return XmlConvert.ToInt32(val);
                        }
                    case Enums.s_Integer64:
                        {
                            return XmlConvert.ToInt64(val);
                        }
                    case Enums.s_Float32:
                    case Enums.s_E2:
                    case Enums.s_C4:
                    case Enums.s_Unipolar216:
                        {
                            return XmlConvert.ToSingle(val);
                        }
                    case Enums.s_Float64:
                        {
                            return XmlConvert.ToDouble(val);
                        }
                    default:
                        {
                            return null;	// ---------->
                        }
                }
            }
            catch (FormatException)
            {
                return null;
            }
            catch (OverflowException)
            {
                return null;
            }
        }
        public static bool GetMaximumOverflowByDataType(string val, string type, uint bitlength)
        {
            if (val == null)
                throw new ArgumentException("Parameter val must not be null or empty");

            if (String.IsNullOrEmpty(type))
                throw new ArgumentException("Parameter type must not be null or empty");


            // Return value with correct data type.

            try
            {
                switch (type)
                {
                    case "Bit":
                        {
                            Byte v = XmlConvert.ToByte(val);

                            if (!(v == 0 || v == 1))
                            {
                                return true;
                            }

                            return false;
                        }
                    case "BitArea":
                        {
                            uint internalvalue = XmlConvert.ToUInt16(val);
                            uint maxvalue = GetBitAreaMaximumValue(bitlength);

                            if (internalvalue > maxvalue)
                            {
                                return true;
                            }

                            return false;
                        }
                    default:
                        {
                            return false;	// ---------->
                        }
                }
            }
            catch (FormatException)
            {
                // NOTE: It is not necessary to trace the exception here, because it would be a report
                // created later in the code.
                //SITrace.Write(TraceCategories.Error, "Value '" + val + "' unsuitable to type '" + type.ToString() + "'. " + e.ToString(), SITrace.tgc);
                return false;
            }
            catch (OverflowException)
            {
                // NOTE: It is not necessary to trace the exception here, because it would be a report
                // created later in the code.
                //SITrace.Write(TraceCategories.Error, "Value '" + val + "' unsuitable to type '" + type.ToString() + "'. " + e.ToString(), SITrace.tgc);
                return false;
            }
        }
        public static uint GetBitAreaMaximumValue(uint bitlength)
        {
            // NOTE: For invalid bit length use minimal or maximal value. The bit length value itself
            //       is tested on another check!

            switch (bitlength)
            {
                case 0:
                    {
                        return 1;		// ---------->
                    }
                case 1:
                    {
                        return 1;		// ---------->
                    }
                case 2:
                    {
                        return 3;		// ---------->
                    }
                case 3:
                    {
                        return 7;		// ---------->
                    }
                case 4:
                    {
                        return 15;		// ---------->
                    }
                case 5:
                    {
                        return 31;		// ---------->
                    }
                case 6:
                    {
                        return 63;		// ---------->
                    }
                case 7:
                    {
                        return 127;		// ---------->
                    }
                case 8:
                    {
                        return 255;		// ---------->
                    }
                case 9:
                    {
                        return 511;		// ---------->
                    }
                case 10:
                    {
                        return 1023;	// ---------->
                    }
                case 11:
                    {
                        return 2047;	// ---------->
                    }
                case 12:
                    {
                        return 4095;	// ---------->
                    }
                case 13:
                    {
                        return 8191;	// ---------->
                    }
                case 14:
                    {
                        return 16383;	// ---------->
                    }
                case 15:
                    {
                        return 32767;	// ---------->
                    }
                default:
                    {
                        return 32767;	// ---------->
                    }
            }
        }

        public static bool DoesFormatAndDataTypeMatch(string sFormat, string type)
        {
            // Checks if format and data item type match.
            //
            // From GSDML V.31 on the data types "TimeOfDay without date indication", "TimeDifference without date indication",
            // "VisibleString", "OctetString" and "Float64" are not valid any more. But in this method no change is made,
            // because the data types are checked by schema.

            switch (type)
            {
                case Enums.s_Unsigned8:
                case Enums.s_Integer8:
                case Enums.s_Unsigned16:
                case Enums.s_Integer16:
                case Enums.s_Unsigned32:
                case Enums.s_Integer32:
                case Enums.s_Unsigned64:
                case Enums.s_Integer64:
                case "TimeOfDay without date indication":
                case "TimeDifference without date indication":
                case Enums.s_VisibleString:
                case Enums.s_OctetString:
                    {
                        if (sFormat == "d" || sFormat == "D" || sFormat == "x" || sFormat == "X")
                        {
                            return true;
                        }
                        return false;
                    }
                case Enums.s_Float32:
                case Enums.s_Float64:
                    {
                        if (sFormat == "f" || sFormat == "F")
                        {
                            return true;
                        }
                        return false;
                    }
                default:
                    {
                        return false;
                    }
            }
        }

        public static UInt16 GetBitLengthFromQualityFormat(string format)
        {
            // Return bit length for each quality format. 

            switch (format)
            {
                case "Qualifier":
                    {
                        return 1;
                    }
                case "Embedded Status":
                    {
                        return 2;
                    }
                case "Status":
                    {
                        return 8;
                    }
                default:
                    {
                        break;
                    }
            }

            return 0;
        }

        public static bool IsDataTypeUsableAsBitDataItem(string dataType)
        {
            switch (dataType)
            {
                case Enums.s_Unsigned8:
                case Enums.s_Unsigned16:
                case Enums.s_Unsigned32:
                case Enums.s_Unsigned64:
                case Enums.s_OctetString:
                case Enums.s_V2:
                    {
                        return true;
                    }
            }
            return false;
        }

        public static string FindIdInText(string text, ref int index, out bool wrongFormat)
        {
            // Finds the format specification field in a text. 

            // As coding the following expression is used:
            // {index[:formatString]}   e.g.   {1:d}

            wrongFormat = false;
            string pattern = "^{[0-9]{1,3}(:([dx]|([f][0-9]{0,2})))?}";
            string numbers = "0123456789";
            string sId = String.Empty;

            if (null == text)
            {
                return sId;
            }

            index = text.IndexOf(Constants.s_CurlyBracketLeft, index, StringComparison.Ordinal);

            if (index == -1)
                return sId;

            int index2 = text.IndexOf(Constants.s_CurlyBracketRight, index, StringComparison.Ordinal);
            if (index2 == -1)
            {
                index = -1;
                wrongFormat = true;
                return sId;
            }

            string subtext = text.Substring(index, index2 - index + 1);

            Match dataTypeMatch = Regex.Match(subtext, pattern);
            if (!dataTypeMatch.Success)
            {
                index = -1;
                wrongFormat = true;
                return sId;
            }

            string sChar = text.Substring(++index, 1);
            while (numbers.Contains(sChar) && XmlConvert.ToInt16(sChar) >= 0 && XmlConvert.ToInt16(sChar) <= 9)
            {
                sId += sChar;
                sChar = text.Substring(++index, 1);
            }

            return sId;
        }

        public static string CollapseWhitespace(string s)
        {
            // replace TAB, LF and CR by space
            // replace sequences of space by single space
            // trim spaces at both ends
            //Regex r = new Regex(@"[ \t\n\r]+");
            return s_RegexCollapseWhite.Replace(s, " ").Trim();
        }

        // Find an element by the value of an attribute which constitutes an ID (of type string).
        public static XElement GetElementById(XElement xeStartingPoint, string xpath,
            string attributeName, string attributeValue, XmlNamespaceManager nsmgr)
        {
            
            // if the attribute value does not contain ",
            // we can use SelectSingleNode(), which is probably more efficient
            if (attributeValue != null && !attributeValue.Contains("\""))
            {
                XElement elem = xeStartingPoint.XPathSelectElement("(" + xpath + ")" + "[@" + attributeName + "=\"" + attributeValue + "\"]", nsmgr);
                if (elem != null)
                    return elem;

            }
            // Otherwise, SelectSingleNode() can't be used, because this function only supports
            // XPath 1.0 and this does not support escaping of the ' and " characters.
            // So SelectSingleNode() is vulnerable to XPath injection.
            var xnCandidates = xeStartingPoint.XPathSelectElements(xpath, nsmgr);
            foreach (var xeCandidate in xnCandidates)
            {
                XAttribute xaAttr = xeCandidate.Attribute(attributeName);
                if (xaAttr == null)
                    continue;
                if (xaAttr.Value == attributeValue)
                {
                    return xeCandidate;
                }
            }
            return null;
        }

        public static string GetAttributeValueFromXElement(XElement element, string attributeName)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (String.IsNullOrEmpty(attributeName))
                throw new ArgumentException("Attribute name is null or empty");

            if (attributeName.StartsWith("xml:", StringComparison.Ordinal))
            {
                attributeName = "{http://www.w3.org/XML/1998/namespace}" + attributeName.Remove(0, 4);
            }
            XAttribute attribute = element.Attribute(attributeName);
            return attribute != null ? attribute.Value : String.Empty;
        }

        public static bool IsGeneratedGSDMLFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return false;

            string[] parts = fileName.Split("-".ToCharArray());

            // vendor name
            string vendorName = parts[2];

            if (!string.IsNullOrEmpty(vendorName) && vendorName[0] == '#')
                return true;

            return false;
        }

        public static bool IsNodeUnderXsAny(XElement elem, XAttribute attr)
        {
            bool bUnderXsAny = false;
            XElement parent;
            if (elem != null)
            {
                if (elem.Name.LocalName == "Embedded" || elem.Name.LocalName == "Signature")
                    return true;
                parent = elem.Parent;
            }
            else if (attr != null)
            {
                if (attr.Name.LocalName == "Embedded" || attr.Name.LocalName == "Signature")
                    return true;
                parent = attr.Parent;
            }
            else
                parent = null;

            while (parent != null)
            {
                if (parent.Name.LocalName == "Embedded" || parent.Name.LocalName == "Signature")
                {
                    parent = null;
                    bUnderXsAny = true;
                }
                else
                {
                    parent = parent.Parent;
                }
            }
            return bUnderXsAny;
        }

        public static IEnumerable<XElement> TryRemoveXElementsUnderXsAny(IEnumerable<XElement> elements, XmlNamespaceManager nsmgr, XDocument Gsd)
        {
            if (null == elements)
            {
                return null;
            }

            var nl = (IEnumerable)Gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsddef:ProfileBody/gsddef:ApplicationProcess/gsddef:GraphicsList/gsddef:GraphicItem/gsddef:Embedded", nsmgr);
            XElement embededed = nl.Cast<XElement>().FirstOrDefault();
#if DEBUG
            var nl2 = (IEnumerable)Gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsdsig:Signature", nsmgr);
            XElement signature = nl2.Cast<XElement>().FirstOrDefault();
            if (embededed == null && signature == null)
                return elements;
#else
            if (embededed == null)
                return elements;
#endif
            IList<XElement> elementsNew = new List<XElement>();
            foreach (XElement elem in elements)
            {
                if (!IsNodeUnderXsAny(elem, null))
                    elementsNew.Add(elem);
            }

            return elementsNew;
        }

        public static IEnumerable<XAttribute> TryRemoveXAttributesUnderXsAny(IEnumerable<XAttribute> attributes, XmlNamespaceManager nsmgr, XDocument Gsd)
        {
            if (null == attributes)
            {
                return null;
            }

            var nl = (IEnumerable)Gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsddef:ProfileBody/gsddef:ApplicationProcess/gsddef:GraphicsList/gsddef:GraphicItem/gsddef:Embedded", nsmgr);
            XElement embededed = nl.Cast<XElement>().FirstOrDefault();
#if DEBUG
            var nl2 = (IEnumerable)Gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsddef:Signature", nsmgr);
            XElement signature = nl2.Cast<XElement>().FirstOrDefault();
            if (embededed == null && signature == null)
                return attributes;
#else
            if (embededed == null)
                return attributes;
#endif

            IList<XAttribute> attributesNew = new List<XAttribute>();
            foreach (XAttribute attr in attributes)
            {
                if (!IsNodeUnderXsAny(null, attr))
                    attributesNew.Add(attr);
            }

            return attributesNew;
        }

        public static IEnumerable TryRemoveXAttributesUnderXsAny(IEnumerable attributes, XmlNamespaceManager nsmgr, XDocument gsd)
        {
            if (null == attributes)
            {
                return null;
            }

            var nl = (IEnumerable)gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsddef:ProfileBody/gsddef:ApplicationProcess/gsddef:GraphicsList/gsddef:GraphicItem/gsddef:Embedded", nsmgr);
            XElement embededed = nl.Cast<XElement>().FirstOrDefault();
#if DEBUG
            var nl2 = (IEnumerable)gsd.XPathEvaluate("/gsddef:ISO15745Profile/gsddef:Signature", nsmgr);
            XElement signature = nl2.Cast<XElement>().FirstOrDefault();
            if (embededed == null && signature == null)
                return attributes;
#else
            if (embededed == null)
                return attributes;
#endif

            IList<XAttribute> attributesNew = new List<XAttribute>();
            foreach (XAttribute attr in attributes)
            {
                if (!IsNodeUnderXsAny(null, attr))
                    attributesNew.Add(attr);
            }
            return attributesNew;
        }

        #endregion

        #region error messages

        private static ResourceManager s_ResMan = null;

        private static CultureInfo s_CultInfo = null;

        public static void InitResources(Assembly ass)
        {
            s_ResMan = new ResourceManager("PNConfigLib.Gsd.Interpreter.Checker.Resources", typeof(Help).Assembly);
            s_CultInfo = Thread.CurrentThread.CurrentCulture;
        }

        public static ResourceManager ResMan => s_ResMan;

        public static CultureInfo Ci => s_CultInfo;

        public static string GetMessageString(string errorID)
        {
            string message = "";

            try
            {
                message = ResMan.GetString(errorID, Ci);
                if (message == null)
                {
                    message = "";
                    System.Diagnostics.Debug.Assert(false);
                }
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Assert(false);
                throw new CheckerException("Error reading resource files!", e);
            }

            return message;
        }

        #endregion

        #region configuration

        private static string m_ConfigFile = null;

        public static void InitConfiguration(string configFile)
        {
            m_ConfigFile = configFile;
        }

        public static NameValueCollection GetConfig(string element)
        {
            NameValueCollection collection = new NameValueCollection();

            try
            {
                XmlReader reader = XmlReader.Create(m_ConfigFile);
                while (reader.Read())
                {
                    if (reader.NodeType == XmlNodeType.Element)
                    {
                        string Element = reader.Name;
                        if (Element == element)
                        {
                            while (reader.MoveToNextAttribute()) // Read the attributes.
                            {
                                string Attribute = reader.Name;
                                string Value = reader.Value;
                                collection.Add(Attribute, Value);
                            }
                            break;
                        }
                    }
                }
                reader.Close();
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.Assert(false);
                throw new CheckerException("Error reading configuration file!", e);
            }

            return collection;
        }


        #endregion

    }
}


