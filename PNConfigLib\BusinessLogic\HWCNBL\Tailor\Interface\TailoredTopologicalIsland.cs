/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: TailoredTopologicalIsland.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Tailor.Config;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.Interface
{
    /// <summary>
    /// This class provides the required functionality for Topological Islands in a tailored IO-System.
    /// </summary>
    internal class TailoredTopologicalIsland
    {
        //########################################################################################
        #region Nested Classes
        // Contains all non-public nested classes and locally scoped interface definitions
        #endregion

        //########################################################################################
        #region Constants and Enums
        // Contains all constants and enums
        #endregion

        //########################################################################################
        #region Fields
        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private readonly LinkedList<DataModel.PCLObjects.Port> m_PortsOfIsland;

        public TailoredTopologicalIsland()
        {
            m_PortsOfIsland = new LinkedList<DataModel.PCLObjects.Port>();
        }

        #endregion

        //########################################################################################

        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)


        /// <summary>
        /// Checks whether this island includes the IO Controller within.
        /// </summary>
        public bool HasController
        {
            get; set;
        }

        /// <summary>
        /// Checks whether this island has at least one programmable peer.
        /// </summary>
        public bool HasProgrammablePeer
        {
            get; set;
        }

        /// <summary>
        /// Holds all ports in the island (not just interconnected ports)
        /// </summary>
        public LinkedList<DataModel.PCLObjects.Port> PortsOfIsland => m_PortsOfIsland;

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class

        /// <summary>
        /// Adds the port to this Topological Island.
        /// </summary>
        /// <param name="portSubmodule"></param>
        public void AddPort(DataModel.PCLObjects.Port portSubmodule)
        {
            m_PortsOfIsland.AddLast(portSubmodule);
        }

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        #endregion
    }
}