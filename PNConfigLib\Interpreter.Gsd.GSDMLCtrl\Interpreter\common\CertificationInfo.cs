﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CertificationInfo.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using GSDI;
using PNConfigLib.Gsd.Interpreter.Common;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    public class CertificationInfo : GsdObject, GSDI.ICertificationInfo
    {
        //########################################################################################
        #region Fields

        private ArrayList m_SecurityClass;
        private string m_NetloadClass;
        private ArrayList m_NetloadClasses;
        private ProfileProcessAutomation m_ProfileProcessAutomation;


        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public virtual Array SecurityClass =>
            m_SecurityClass?.ToArray();

        public string NetloadClass => this.m_NetloadClass;

        public Array NetloadClasses =>
            (null != this.m_NetloadClasses) ?
                m_NetloadClasses.ToArray() :
                null;

        public ProfileProcessAutomation ProfileProcessAutomation => m_ProfileProcessAutomation;


#if !S7PLUS

        #region COM Interface Members Only

        #endregion
#endif

        #endregion


        //########################################################################################
        #region Initialization & Termination

        public CertificationInfo()
        {
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        		/// <summary>
		/// Fills the created object with all relevant data, which is needed
		/// for the properties.
		/// </summary>
		/// <param name="hash">Hashtable which contains name value pairs for
		/// all properties available from this object.</param>
		/// <returns>True, if filling was successfull, else false.</returns>
        override internal bool Fill(Hashtable hash)
		{
		    bool succeeded = true;
		    try
		    {
		        // Check parameter.
		        if (null == hash)
		            throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldSecurityClass;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_SecurityClass = hash[member] as ArrayList;

                member = Models.s_FieldNetloadClass;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_NetloadClass = hash[member] as string;

                member = Models.s_FieldNetloadClasses;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_NetloadClasses = hash[member] as ArrayList;
                member = Models.s_FieldProfileProcessAutomation;
                if (hash.ContainsKey(member) && hash[member] is ProfileProcessAutomation)
                    m_ProfileProcessAutomation = hash[member] as ProfileProcessAutomation;

            }
		    catch (FillException)
		    {
                succeeded = false;
		    }
            return succeeded;
		}

        #endregion
    }
}
