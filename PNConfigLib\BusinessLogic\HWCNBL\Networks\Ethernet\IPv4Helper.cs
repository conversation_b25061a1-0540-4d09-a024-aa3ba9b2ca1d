/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPv4Helper.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.HWCNBL.Utilities.Network;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Networks.Ethernet
{
    internal static class IPv4Helper
    {
        //########################################################################################
        #region Constants and Enums
        #endregion

        //########################################################################################
        #region Internal Static Implementation

        /// <summary> Checks the validation of a IP address recording to the RCFs </summary>
        /// <param name="ipAddress">Address for validation</param>
        /// <param name="subnetMask">Corresponding SubnetMask</param>
        /// <returns>Error state</returns>
        internal static IPAddressErrorCodes CheckIPAddressRfcValidation(IPAddress ipAddress, IPSubnetMask subnetMask)
        {
            var errorCode = IPAddressErrorCodes.None;
            //an address in the address range from 0.0.0.0 up to ************* (highest 8 bit of the address are set to “0000 0000”),
            //Note: 0.0.0.0/8 is reserved according RFC 1122 section *******.
            Int64 highBits = ipAddress.AsInt64 & 0xFF000000;

            if( highBits == 0 )
                errorCode = IPAddressErrorCodes.NotAllowedAddressRange;

            //a multicast address: an address in the address range from ********* up to *************** (highest 4 bit of the address are “1110”), and
            //Note: *********/4 is allocated for use in IPv4 multicast address assignments. See RFC3171.
            highBits = ipAddress.AsInt64 & 0xF0000000;
            Int64 lowBits = ipAddress.AsInt64 & 0x0FFFFFFF;

            if( (highBits == 0xE0000000) && (lowBits >= 0x0) && (lowBits <= 0x0FFFFFFF) )
                errorCode = IPAddressErrorCodes.NotAllowedAddressRange;

            //a reserved address: an address in the address range from 240.0.0.0 up to *************** (highest 4 bit of the address are “1111”).
            //Note: 240.0.0.0/4 is reserved for future addressing modes.*/
            highBits = ipAddress.AsInt64 & 0xF0000000;
            lowBits = ipAddress.AsInt64 & 0x0FFFFFFF;

            if( (highBits == 0xF0000000) && (lowBits >= 0x0) && (lowBits <= 0x0FFFFFFF) )
                errorCode = IPAddressErrorCodes.NotAllowedAddressRange;

            //The Subnetpart of the address is "0",Note: Reserved according to RFC 3232.
            Int64 netAddress = subnetMask.GetNetAddress( ipAddress );   //Net part of IP address
            var maskIP4 = (Int32)subnetMask.AsInt64;

            //net part of IP address is "0" is not allowed
            if( ( netAddress & maskIP4 ) == 0 )
                errorCode = IPAddressErrorCodes.NetAddressNull;

            return errorCode;
        }

        #endregion

    }
}
