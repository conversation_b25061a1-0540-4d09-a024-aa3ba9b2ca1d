using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using PNConfigTool.Models;
using System.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel;
using PNConfigLib.GSDImport;
using PNConfigLib.BusinessLogic.DataModel;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace PNConfigTool.Services
{
    /// <summary>
    /// GSDML文件服务实现
    /// </summary>
    public class GSDMLService : IGSDMLService, IDisposable
    {
        private readonly string _gsdmlDirectory;
        private string _customGSDMLDirectory;
        private bool _useCustomDirectory;
        private readonly ConsistencyManager _consistencyManager;

        // 缓存相关字段
        private readonly ConcurrentDictionary<string, List<string>> _fileListCache = new();
        private readonly ConcurrentDictionary<string, bool> _fileExistsCache = new();
        private readonly ConcurrentDictionary<string, DateTime> _directoryLastModified = new();
        private FileSystemWatcher? _fileWatcher;
        private readonly object _cacheLock = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public GSDMLService()
        {
            // 获取应用程序数据目录
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "PNConfigTool");
            
            // 创建GSDML目录
            _gsdmlDirectory = Path.Combine(appDataPath, "GSDML");
            if (!Directory.Exists(_gsdmlDirectory))
            {
                Directory.CreateDirectory(_gsdmlDirectory);
            }
            
            _customGSDMLDirectory = string.Empty;
            _useCustomDirectory = false;
            _consistencyManager = new ConsistencyManager();

            // 初始化文件系统监控
            InitializeFileWatcher();
        }

        /// <summary>
        /// 初始化文件系统监控器
        /// </summary>
        private void InitializeFileWatcher()
        {
            try
            {
                _fileWatcher = new FileSystemWatcher(_gsdmlDirectory)
                {
                    Filter = "*.xml",
                    IncludeSubdirectories = true,
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite | NotifyFilters.CreationTime
                };

                _fileWatcher.Created += OnFileSystemChanged;
                _fileWatcher.Deleted += OnFileSystemChanged;
                _fileWatcher.Changed += OnFileSystemChanged;
                _fileWatcher.Renamed += OnFileSystemChanged;

                _fileWatcher.EnableRaisingEvents = true;
                Debug.WriteLine($"文件系统监控已启动: {_gsdmlDirectory}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化文件系统监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文件系统变化事件处理
        /// </summary>
        private void OnFileSystemChanged(object sender, FileSystemEventArgs e)
        {
            lock (_cacheLock)
            {
                // 清除相关缓存
                string directory = Path.GetDirectoryName(e.FullPath) ?? "";
                _fileListCache.TryRemove(directory, out _);
                _fileExistsCache.TryRemove(e.FullPath, out _);
                _directoryLastModified.TryRemove(directory, out _);

                // 清除GSDML解析缓存
                if (e.FullPath.EndsWith(".xml", StringComparison.OrdinalIgnoreCase))
                {
                    GSDMLCacheService.ClearCache();
                }

                Debug.WriteLine($"文件系统变化，清除缓存: {e.FullPath}");
            }
        }

        /// <inheritdoc/>
        public List<string> GetGSDMLFiles()
        {
            try
            {
                string dir = _useCustomDirectory ? _customGSDMLDirectory : _gsdmlDirectory;

                // 检查缓存是否有效
                if (_fileListCache.TryGetValue(dir, out var cachedFiles))
                {
                    if (IsDirectoryCacheValid(dir))
                    {
                        Debug.WriteLine($"使用缓存的文件列表: {dir} ({cachedFiles.Count} 个文件)");
                        return cachedFiles;
                    }
                }

                // 缓存无效或不存在，重新扫描
                Debug.WriteLine($"扫描GSDML目录: {dir}");
                var files = Directory.GetFiles(dir, "*.xml", SearchOption.AllDirectories).ToList();

                // 更新缓存
                lock (_cacheLock)
                {
                    _fileListCache[dir] = files;
                    _directoryLastModified[dir] = Directory.GetLastWriteTime(dir);
                }

                Debug.WriteLine($"扫描完成，找到 {files.Count} 个GSDML文件");
                return files;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"GetGSDMLFiles 错误: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 检查目录缓存是否有效
        /// </summary>
        private bool IsDirectoryCacheValid(string directory)
        {
            if (!_directoryLastModified.TryGetValue(directory, out var cachedTime))
                return false;

            try
            {
                var currentTime = Directory.GetLastWriteTime(directory);
                return currentTime == cachedTime;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 缓存的文件存在性检查
        /// </summary>
        private bool IsFileExistsCached(string filePath)
        {
            if (_fileExistsCache.TryGetValue(filePath, out var cachedResult))
            {
                return cachedResult;
            }

            bool exists = File.Exists(filePath);
            _fileExistsCache[filePath] = exists;
            return exists;
        }

        // Implementation of async version for interface compatibility
        public Task<List<string>> GetGSDMLFilesAsync()
        {
            return Task.FromResult(GetGSDMLFiles());
        }

        /// <inheritdoc/>
        public async Task<bool> ImportGSDMLFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            try
            {
                // 使用缓存的文件存在性检查
                if (!IsFileExistsCached(filePath) || !filePath.EndsWith(".xml", StringComparison.OrdinalIgnoreCase))
                {
                    Debug.WriteLine($"ImportGSDMLFile 错误: 文件不存在或不是XML文件: {filePath}");
                    return false;
                }

                string fileName = Path.GetFileName(filePath);
                string destPath = GetDestinationPath(filePath);
                
                // 确保目标目录存在
                string? destDir = Path.GetDirectoryName(destPath);
                if (string.IsNullOrEmpty(destDir))
                {
                    throw new InvalidOperationException("无法获取目标目录路径");
                }
                
                if (!Directory.Exists(destDir))
                {
                    Directory.CreateDirectory(destDir);
                }

                // 使用文件流确保正确释放文件资源
                using (FileStream sourceStream = new(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                using (FileStream destStream = new(destPath, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    await sourceStream.CopyToAsync(destStream);
                }
                
                // 使用PNConfigLib.GSDImport导入GSDML文件到Catalog
                bool importResult = await Task.Run(() => {
                    try
                    {
                        // 尝试提取GSDML版本信息
                        string gsdmlVersion = ExtractGSDMLVersion(destPath);

                        // 使用提取的版本信息验证文件
                        bool validationResult = _consistencyManager.ValidateGSDML(gsdmlVersion, destPath);
                        if (!validationResult)
                        {
                            Debug.WriteLine($"GSDML验证失败: {destPath}");
                        }

                        // 即使验证失败，我们仍尝试导入 (由Converter.ImportGSDML来做进一步的验证)
                        bool result = Converter.ImportGSDML(destPath, _consistencyManager);

                        // 如果导入成功，预加载到缓存中
                        if (result)
                        {
                            var interpreter = GSDMLCacheService.GetCachedInterpreter(destPath);
                            if (interpreter != null)
                            {
                                Debug.WriteLine($"GSDML文件已预加载到缓存: {destPath}");
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Converter.ImportGSDML 错误: {ex.Message}");
                        return false;
                    }
                });
                
                if (importResult)
                {
                    GSDMLManager.AddGSDMLFile(fileName);
                }
                
                return importResult;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ImportGSDMLFile 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 尝试从GSDML文件名提取版本信息
        /// </summary>
        private string ExtractGSDMLVersion(string filePath)
        {
            try
            {
                string fileName = Path.GetFileName(filePath);
                if (fileName.StartsWith("GSDML-V", StringComparison.OrdinalIgnoreCase))
                {
                    int hyphenIndex = fileName.IndexOf('-', 7);
                    if (hyphenIndex > 7)
                    {
                        return fileName.Substring(7, hyphenIndex - 7);
                    }
                }
                // 默认版本
                return "2.3";
            }
            catch
            {
                // 出错时返回默认版本
                return "2.3";
            }
        }
        
        /// <inheritdoc/>
        public async Task<Dictionary<string, bool>> ImportGSDMLFiles(IEnumerable<string> filePaths)
        {
            var results = new Dictionary<string, bool>();
            
            foreach (var filePath in filePaths)
            {
                results[filePath] = await ImportGSDMLFile(filePath);
            }
            
            return results;
        }

        /// <inheritdoc/>
        public async Task<bool> RemoveGSDMLFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;
                
            try
            {
                if (File.Exists(filePath))
                {
                    // 使用FileInfo确保正确释放文件资源
                    FileInfo fileInfo = new(filePath);
                    await Task.Run(() => fileInfo.Delete());
                    
                    // 从Catalog中移除引用
                    string fileName = Path.GetFileName(filePath);
                    if (Catalog.ImportedGSDMLList.Contains(fileName.ToUpperInvariant()))
                    {
                        Catalog.ImportedGSDMLList.Remove(fileName.ToUpperInvariant());
                        
                        // 注意：这里只是从列表中移除了文件记录
                        // 理想情况下，应该也移除此文件关联的所有设备、模块等
                        // 但这需要更复杂的清理逻辑
                    }
                    
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"RemoveGSDMLFile 错误: {ex.Message}");
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<Dictionary<string, bool>> RemoveGSDMLFiles(IEnumerable<string> filePaths)
        {
            var results = new Dictionary<string, bool>();
            
            foreach (var filePath in filePaths)
            {
                results[filePath] = await RemoveGSDMLFile(filePath);
            }
            
            return results;
        }

        /// <inheritdoc/>
        public async Task<List<string>> GetAvailableDevices()
        {
            // 从Catalog中获取可用设备信息
            return await Task.Run(() => {
                var devices = new List<string>();
                foreach (var device in Catalog.DeviceList)
                {
                    string deviceName = device.Value.AttributeAccess.GetAnyAttribute<string>("GSDNameTextId", null, 
                                        Path.GetFileNameWithoutExtension(device.Value.AttributeAccess.GetAnyAttribute<string>("GSDFileName", null, "unknown")));
                    devices.Add(deviceName);
                }
                return devices;
            });
        }

        /// <inheritdoc/>
        public string GetGSDMLDirectory()
        {
            return _useCustomDirectory ? _customGSDMLDirectory : _gsdmlDirectory;
        }

        /// <inheritdoc/>
        public void SetCustomGSDMLDirectory(string directoryPath)
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                _useCustomDirectory = false;
                // 重新初始化文件监控器到默认目录
                ReinitializeFileWatcher(_gsdmlDirectory);
                return;
            }

            _customGSDMLDirectory = directoryPath;
            _useCustomDirectory = true;

            // 确保目录存在
            if (!Directory.Exists(_customGSDMLDirectory))
            {
                Directory.CreateDirectory(_customGSDMLDirectory);
            }

            // 重新初始化文件监控器到新目录
            ReinitializeFileWatcher(_customGSDMLDirectory);

            // 清除旧的缓存
            ClearAllCaches();
        }

        /// <summary>
        /// 重新初始化文件监控器
        /// </summary>
        private void ReinitializeFileWatcher(string directory)
        {
            try
            {
                _fileWatcher?.Dispose();

                _fileWatcher = new FileSystemWatcher(directory)
                {
                    Filter = "*.xml",
                    IncludeSubdirectories = true,
                    NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite | NotifyFilters.CreationTime
                };

                _fileWatcher.Created += OnFileSystemChanged;
                _fileWatcher.Deleted += OnFileSystemChanged;
                _fileWatcher.Changed += OnFileSystemChanged;
                _fileWatcher.Renamed += OnFileSystemChanged;

                _fileWatcher.EnableRaisingEvents = true;
                Debug.WriteLine($"文件系统监控已重新初始化: {directory}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重新初始化文件系统监控失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取GSDML文件的目标保存路径
        /// </summary>
        private string GetDestinationPath(string sourceFilePath)
        {
            if (string.IsNullOrEmpty(sourceFilePath))
            {
                throw new ArgumentNullException(nameof(sourceFilePath));
            }

            string fileName = Path.GetFileName(sourceFilePath);
            string destDirectory = GetGSDMLDirectory();

            return Path.Combine(destDirectory, fileName);
        }

        /// <summary>
        /// 清除所有缓存
        /// </summary>
        public void ClearAllCaches()
        {
            lock (_cacheLock)
            {
                _fileListCache.Clear();
                _fileExistsCache.Clear();
                _directoryLastModified.Clear();
                GSDMLCacheService.ClearCache();
                Debug.WriteLine("所有缓存已清除");
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public (int FileListCacheCount, int FileExistsCacheCount, int GSDMLCacheCount, long GSDMLMemoryEstimate) GetCacheStatistics()
        {
            lock (_cacheLock)
            {
                var (gsdmlCount, gsdmlMemory) = GSDMLCacheService.GetCacheStats();
                return (_fileListCache.Count, _fileExistsCache.Count, gsdmlCount, gsdmlMemory);
            }
        }

        /// <summary>
        /// 析构函数，清理资源
        /// </summary>
        ~GSDMLService()
        {
            Dispose(false);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _fileWatcher?.Dispose();
                ClearAllCaches();
            }
        }
    }
}