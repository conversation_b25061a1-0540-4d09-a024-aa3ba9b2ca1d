/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: InterfaceSubmoduleConverter.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;
using GSDI;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.GSDImport.Helper;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.IODevice.ConfigStrategy;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Utilities.Enums;

using PNConfigLib.Gsd.Interpreter.Common;

using ApplicationRelations = PNConfigLib.Gsd.Interpreter.Common.ApplicationRelations;
using DeviceAccessPoint = PNConfigLib.Gsd.Interpreter.Common.DeviceAccessPoint;
using InterfaceSubmodule = PNConfigLib.Gsd.Interpreter.Common.InterfaceSubmodule;
using TimingProperties = PNConfigLib.Gsd.Interpreter.Common.TimingProperties;
using PNConfigLib.BusinessLogic.HWCNBL.PNInterface;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal sealed class InterfaceSubmoduleConverter
    {
        private const int s_IpConfigDcpSupported = 1;
        private const int s_IpConfigDhcpSupported = 2;
        private const int s_IpConfigLocalSupported = 4;
        private const int s_IpConfigUnknownSupported = 8;
        private SubslotHelper m_SubslotHelper;
        private const int DummyPortSubslot = 0x8001;
        private DeviceAccessPoint m_DAP;
        private GsdDevice m_GsdDevice;

        private InterfaceCatalog m_Interface;

        private InterfaceSubmodule m_InterfaceSubmoduleItem;

        public InterfaceSubmoduleConverter(
            DeviceAccessPoint dap,
            InterfaceSubmodule interfaceSubmodule,
            GsdDevice gsdGsdDevice)
        {
            m_DAP = dap;
            m_InterfaceSubmoduleItem = interfaceSubmodule;
            m_GsdDevice = gsdGsdDevice;
        }

        private bool IsIrtCluster
        {
            get
            {
                if ((m_InterfaceSubmoduleItem != null)
                    && !m_InterfaceSubmoduleItem.IsParameterizationDisallowed)
                {
                    return m_InterfaceSubmoduleItem.SupportedSynchronisationRole > 0;
                }
                return false;
            }
        }

        private TimingProperties RtClass3TimingProperties
        {
            get
            {
                TimingProperties timingProperties = null;
                if (m_InterfaceSubmoduleItem != null)
                {
                    ApplicationRelations ar = m_InterfaceSubmoduleItem.ApplicationRelations;
                    if (ar != null)
                    {
                        timingProperties = ar.RTClass3TimingProperties;
                    }
                }
                return timingProperties;
            }
        }

        private TimingProperties TimingProperties
        {
            get
            {
                TimingProperties timingProperties = null;
                if (m_InterfaceSubmoduleItem != null)

                {
                    ApplicationRelations ar = m_InterfaceSubmoduleItem.ApplicationRelations;
                    if (ar != null)
                    {
                        timingProperties = ar.TimingProperties;
                    }
                }

                // if no timing properties found, look for timing properties
                // of dap
                if (timingProperties == null)
                {
                    ApplicationRelations ar = m_DAP.ApplicationRelations;
                    if (ar != null)
                    {
                        timingProperties = ar.TimingProperties;
                    }
                }
                return timingProperties;
            }
        }

        public InterfaceCatalog Convert(string fileName)
        {
            m_SubslotHelper = new SubslotHelper();
            List<uint> subslotList = new List<uint>();
            if (m_DAP.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDAll))
            {
                if (m_DAP.HasSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort))
                {
                    Array submoduleList = m_DAP.GetSystemDefinedSubmodules(SystemDefinedSubmoduleTypes.GSDPort);
                    foreach (SystemDefinedSubmoduleObject submodule in submoduleList)
                    {
                        m_SubslotHelper.AddSystemDefinedSubmodule(submodule);
                    }
                }
                Array physicalSubslots = m_DAP.PhysicalSubslots;
                if (physicalSubslots != null)
                {
                    foreach (uint subslot in physicalSubslots)
                    {
                        if (subslot >= 0x8000)
                        {
                            subslotList.Add(subslot);
                        }
                    }
                }
                m_SubslotHelper.SystemDefinedSubslots.AddRange(subslotList);

                if (m_DAP.Subslots != null)
                {
                    foreach (Subslot subslot in m_DAP.Subslots)
                    {
                        m_SubslotHelper.AddSubslotDescription(subslot);
                    }
                }
            }
            else
            {
                // dummy port
                subslotList.Add(DummyPortSubslot);
                m_SubslotHelper.SystemDefinedSubslots.AddRange(subslotList.ToArray());
            }

            if (m_Interface != null)
            {
                IList<uint> rtClasses = GetSupportedRtClasses();
                foreach (uint rtClass in rtClasses)
                {
                    if (rtClass == (int)RTClasses.GSDClass3)
                    {
                        break;
                    }
                }
            }
            m_Interface = new InterfaceCatalog();
            m_Interface.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GSDFileName, fileName);

            AddRequiredAttributes(fileName);

            AddSuppScf12Attribute();
            AddSuppScf3Attribute();
            AddSuppFrameClassAttribute();
            AddPNSupportedSyncProtocolsAttribute();

            AddPNIoSuppRr12Pow2Attribute();
            AddPNIoSuppRr12NonPow2Attribute();
            AddPNIoSuppRr3Pow2Attribute();
            AddPNIoSuppRr3NonPow2Attribute();

            AddPNIoArStartupModeAttribute();
            AddPNIrtArStartupModeAttribute();
            AddPNIrtForwardingModeAttribute();
            AddPNIrtPllWindowAttribute();

            AddPNIoMaxNumberOfArAttribute();

            //AddMaxNumberOfCrAttributes();

            ConvertMrp();

            AddPNIrtFragmentationTypeAttribute();

            InitDecorators();

            AddDcpEnableReadOnlyAttribute();

            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.ParameterRecordDataList = m_InterfaceSubmoduleItem.ParameterRecordData;
            }

            return m_Interface;
        }

        private void AddRequiredAttributes(string fileName)
        {
            AddPNBasicInterfaceAttributes(fileName);
            AddPNIrtSyncRoleSuppAttribute();
            AddIoTypeAttribute();
            AddPNSubslotNumberAttribute();
            AddNodeIPSubnetMaskUnrestrictedAttribute();
        }

        private void AddPNIoIpConfigModeSupportedAttribute()
        {
            long configModes = 0;

            Array array = m_DAP.AddressAssignment;
            if (array != null)
            {
                foreach (string mode in array)
                {
                    switch (mode)
                    {
                        case "DCP":
                            configModes |= s_IpConfigDcpSupported;
                            break;
                        case "LOCAL":
                            configModes |= s_IpConfigLocalSupported;
                            break;
                        case "DHCP":
                            configModes |= s_IpConfigDhcpSupported;
                            break;
                    }
                }
            }
            else if (m_DAP.IsExtendedAddressAssignmentSupported)
            {
                // if ExtendedAddressAssignment is set to true, any additional way
                // of address assignment next to DCP is supported (it is unknown
                // which one)
                configModes |= s_IpConfigDcpSupported;
                configModes |= s_IpConfigUnknownSupported;
            }
            else
            {
                configModes |= s_IpConfigDcpSupported;
            }

            if (configModes != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<Int32>(
                    InternalAttributeNames.PnIoIpConfigModeSupported,
                    (int)configModes);
            }
        }
        private void AddPNIoNumberOfSubmodulesAttribute()
        {
            if (m_DAP.NumberOfSubmodules > 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                   InternalAttributeNames.PnIoNumberOfSubmodules,
                   m_DAP.NumberOfSubmodules);
            }
        }

        private void AddDelayMeasurementSupportAttribute()
        {
            if (IsPdevActive())
            {
                if (m_InterfaceSubmoduleItem.IsDelayMeasurementSupported)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnDelayMeasurementSupported,
                        m_InterfaceSubmoduleItem.IsDelayMeasurementSupported);
                }
            }
        }

        private void AddDeviceIdAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnDeviceId,
                (int)m_GsdDevice.Info.DeviceIdentNumber);
        }

        private void AddIoTypeAttribute()
        {
            if (IsPdevActive())
            {
                m_Interface.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.IoType, (int)IoTypes.Diagnosis);
            }
        }

        private void AddPNIrtSyncRoleSuppAttribute()
        {
            if ((m_InterfaceSubmoduleItem != null) && IsPdevActive())
            {
                SynchronisationRoles roles = m_InterfaceSubmoduleItem.SupportedSynchronisationRole;
                Enumerated syncRoles = new Enumerated();
                syncRoles.List.Add(PNIRTSyncRole.NotSynchronized);
                syncRoles.DefaultValue = syncRoles.List.First();

                switch (roles)
                {
                    case SynchronisationRoles.GSDSyncMaster:
                        {
                            syncRoles.List.Add(PNIRTSyncRole.SyncMaster);
                            break;
                        }
                    case SynchronisationRoles.GSDSyncSlave:
                        {
                            syncRoles.List.Add(PNIRTSyncRole.SyncSlave);
                            break;
                        }
                    case SynchronisationRoles.GSDSyncMasterAndSyncSlave:
                        {
                            syncRoles.List.Add(PNIRTSyncRole.SyncMaster);
                            syncRoles.List.Add(PNIRTSyncRole.SyncSlave);
                            break;
                        }
                    case SynchronisationRoles.GSDSyncMasterRedundant:
                        {
                            syncRoles.List.Add(PNIRTSyncRole.SyncMaster);
                            syncRoles.List.Add(PNIRTSyncRole.SecondarySyncMaster);
                            break;
                        }
                    case SynchronisationRoles.GSDSyncMasterRedundantAndSyncSlave:
                        {
                            syncRoles.List.Add(PNIRTSyncRole.SyncMaster);
                            syncRoles.List.Add(PNIRTSyncRole.SecondarySyncMaster);
                            syncRoles.List.Add(PNIRTSyncRole.SyncSlave);
                            break;

                        }
                }
                m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtSyncRoleSupp, syncRoles);
            }
        }

        private void AddMaxDeviceDataLengthAttributes(string fileName)
        {
            if (m_DAP.IOConfigData == null)
            {
                return;
            }

            uint maxInputLength = m_DAP.IOConfigData.MaxInputLength;
            uint maxOutputLength = m_DAP.IOConfigData.MaxOutputLength;
            uint maxDataLength = m_DAP.IOConfigData.MaxDataLength;

            long maxApplicationInputLength = m_DAP.IOConfigData.MaxApplicationInputLength;
            long maxApplicationOutputLength = m_DAP.IOConfigData.MaxApplicationOutputLength;
            long maxApplicationDataLength = m_DAP.IOConfigData.MaxApplicationDataLength;

            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoMaxDeviceInputDataLength,
                maxInputLength);
            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoMaxDeviceOutputDataLength,
                maxOutputLength);
            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoMaxDeviceDataLength,
                maxDataLength);

            // Some old GSD files have invalid values for MaxInputLength and MaxOutputLength. The consistency check
            // must make an exception for these devices.
            string versionPart = fileName.ToUpperInvariant().Substring(0, 10);
            if ((string.Compare(versionPart, "gsdml-v2.0", StringComparison.OrdinalIgnoreCase) <= 0)
                || ((string.Compare(versionPart, "gsdml-v2.1", StringComparison.OrdinalIgnoreCase) <= 0)
                    && fileName.ToUpperInvariant().Contains("SCALANCE")))
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIoGsdLegacyDataLengthConsistencyCheck,
                    true);
            }

            if (m_DAP.IOConfigData.IsMaxApplicationInputLength)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceInputNettoDataLength,
                    (uint)maxApplicationInputLength);
            }

            if (m_DAP.IOConfigData.IsMaxApplicationOutputLength)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceOutputNettoDataLength,
                    (uint)maxApplicationOutputLength);
            }

            if (maxApplicationDataLength > 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxDeviceNettoDataLength,
                    (uint)maxApplicationDataLength);
            }
            if (m_DAP.IOConfigData.ApplicationLengthIncludesIOxS)
            {
                int defaultValue = m_DAP.IOConfigData.ApplicationLengthIncludesIOxS ? 1 : 0;
                m_Interface.AttributeAccess.AddAnyAttribute<int>(
                   InternalAttributeNames.PnIoNettoDataLengthIncludesIOxS, defaultValue);
            }
        }

        private void AddMinFrameIntFactorAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoMinFrameIntFactor,
                m_DAP.MinDeviceInterval);
        }

        private void AddPNAddMediaRedundancyProtSupportedAttribute()
        {
            // Attribute PNAddMediaRedundancyProtSupported must be deleted (= support is unknown) if
            // - the GSDML version is < v2.25
            // - the GSDML version is >= v2.25 but MRP is not supported (element MediaRedundancy missing in GSD file)
            bool deleteVariable = true;
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnAddMediaRedundancyProtSupported,
                false);
            if (m_InterfaceSubmoduleItem != null)
            {
                String gsdFileName = m_Interface.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.GSDFileName,
                        new AttributeAccessCode(),
                        string.Empty);
                string versionPart = gsdFileName.ToUpperInvariant().Substring(0, 11);
                if (string.Compare(versionPart, "gsdml-v2.25", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    if (m_InterfaceSubmoduleItem.MediaRedundancy != null)
                    {
                        deleteVariable = false;
                        bool addMediaRedProtTmp = m_InterfaceSubmoduleItem.MediaRedundancy.AdditionalMrpProtocolsSupported;

                        m_Interface.AttributeAccess.SetAnyAttribute<bool>(
                            InternalAttributeNames.PnAddMediaRedundancyProtSupported,
                                    addMediaRedProtTmp);

                    }
                }
            }
            if (deleteVariable)
            {
                m_Interface.AttributeAccess.RemoveAttribute(
                    InternalAttributeNames.PnAddMediaRedundancyProtSupported);
            }
        }

        private void AddPNAdditionalForwardingRulesSupportedAttribute()
        {
            bool additionalForwardingRulesSupported = false;
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.MediaRedundancy != null)
                {
                    if (m_InterfaceSubmoduleItem.MediaRedundancy.AdditionalForwardingRulesSupported)
                    {
                        additionalForwardingRulesSupported = true;
                    }
                }
                
            }

            if (!additionalForwardingRulesSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<int>(
                              InternalAttributeNames.PnAdditionalForwardingRulesSupported,
                              0);
            }
        }
        private void AddPNBasicInterfaceAttributes(string fileName)
        {
            AddVendorIdAttribute();
            AddDeviceIdAttribute();
            AddPNSubmoduleIdentNumberAttribute();
            AddSupportedProtocolsAttributes();

            AddPNDcpBoundarySupportedAttribute();
            AddPNPtpBoundarySupportedAttribute();

            AddDelayMeasurementSupportAttribute();
            AddPNNetworkComponentDiagnosisSupportedAttribute();
            AddPNParameterizationDisallowedAttribute();
            AddPNAddMediaRedundancyProtSupportedAttribute();
            AddPNIoCheckDeviceIDAllowedAttribute();

            AddPNSubmoduleModelSuppAttribute();
            AddPNObjectUUIDLocalIndexAttribute();
            AddPNMultipleWriteSupportedAttribute();
            AddPNDcpHelloSupportedAttribute();
            AddPNParameterizationSpeedupSupportedAttribute();
            AddPNIoIpConfigModeSupportedAttribute();

            AddPNNameOfStationAtribute();
            AddPNIoIOXSRequiredAttribute();
            AddPNIoLldpnoDSupportedAttribute();

            AddPNIoNumberOfAdditionalInputCrAttribute();
            AddPNIoNumberOfAdditionalOutputCrAttribute();

            AddMaxDeviceDataLengthAttributes(fileName);
            AddPNMaxRecordSupportedAttribute();
            AddMinFrameIntFactorAttribute();

            AddPNIoMaxFrameStartTimeAttribute();
            AddPNIoMinNrtGapAttribute();

            AddPNIoPdevCombinedObjectSupportedAttribute();
            AddPNIoNumberOfSubmodulesAttribute();
            AddPNAdditionalForwardingRulesSupportedAttribute();

            if (IsIrtCluster)
            {
                AddPNIrtSwitchBridgingDelayAttribute();
                AddPNSwitchBridgingDelayFfwAttribute();
                ConvertDfp();
                AddPNIrtMinFrameSendOffsetAttribute();
                AddPNIrtMinYellowTimeAttribute();
                AddPNIrtYellowSafetyMarginAttribute();
                AddPNIrtMaxRetentionTimeAttribute();
            }
        }

        private void AddPNDcpBoundarySupportedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.IsDCPBoundarySupported)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnDCPBoundarySupported,
                        m_InterfaceSubmoduleItem.IsDCPBoundarySupported);
                }
            }
        }

        private void AddPNDcpHelloSupportedAttribute()
        {
            if (IsPdevActive()
                && m_InterfaceSubmoduleItem.IsDCP_HelloSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoDCPHelloSupported, true);
            }
        }

        private void AddPNIoArStartupModeAttribute()
        {
            if (m_InterfaceSubmoduleItem == null)
            {
                return;
            }

            if(m_InterfaceSubmoduleItem.ApplicationRelations == null)
            {
                return;
            }

            Enumerated startup = new Enumerated();
            List<int> startupList = new List<int>();

            startup.DefaultValue = (int)PNIOARStartupMode.Legacy;

            if (m_InterfaceSubmoduleItem.ApplicationRelations.StartupMode != null)
            {
                foreach (string mode in m_InterfaceSubmoduleItem.ApplicationRelations.StartupMode)
                {
                    if (mode == Enum.GetName(typeof(PNIOARStartupMode), PNIOARStartupMode.Advanced))
                    {
                        startupList.Add((int)PNIOARStartupMode.Advanced);
                        startup.DefaultValue = (int)PNIOARStartupMode.Advanced;
                    }
                    else if (mode == Enum.GetName(typeof(PNIOARStartupMode), PNIOARStartupMode.Legacy))
                    {
                        startupList.Add((int)PNIOARStartupMode.Legacy);
                    }
                }
            }
            else
            {
                startupList.Add((int)PNIOARStartupMode.Legacy);
            }

            startup.SetList(startupList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIoArStartupMode, startup);
        }

        private void AddPNIoCheckDeviceIDAllowedAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIoCheckDeviceIDAllowed,
                m_DAP.IsCheckDeviceIDAllowed);
        }

        private void AddPNIoIOXSRequiredAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIoIOXSRequired,
                m_DAP.IsIOXSRequired);
        }

        private void AddPNIoLldpnoDSupportedAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnLLDPNoDSupported,
                m_DAP.IsLldpnoDSupported);
        }

        private void AddPNIoMaxFrameStartTimeAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMaxFrameStartTime,
                    m_InterfaceSubmoduleItem.MaxFrameStartTime);
            }
        }

        private void AddPNIoMaxNumberOfArAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.ApplicationRelations != null)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.PnIoMaxNumberOfAR,
                        m_InterfaceSubmoduleItem.ApplicationRelations.NumberOfAr);
                }
            }
        }

        private void AddPNIoMinNrtGapAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMinNRTGap,
                    m_InterfaceSubmoduleItem.MinNRTGap);
            }
        }

        private void AddPNIoNumberOfAdditionalInputCrAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.ApplicationRelations != null)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.PnIoNumberOfAdditionalInputCR,
                        m_InterfaceSubmoduleItem.ApplicationRelations.NumberOfAdditionalInputCR);
                }
            }
        }

        private void AddPNIoNumberOfAdditionalOutputCrAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.ApplicationRelations != null)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.PnIoNumberOfAdditionalOutputCR,
                        m_InterfaceSubmoduleItem.ApplicationRelations.NumberOfAdditionalOutputCR);
                }
            }
        }

        private void AddPNIoPdevCombinedObjectSupportedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIoPdevCombinedObjectSupported,
                    m_InterfaceSubmoduleItem.PDEVCombinedObjectSupported);
            }
        }

        private void AddPNIoSuppRr12NonPow2Attribute()
        {
            if (TimingProperties != null)
            {
                Array reductionRatios = TimingProperties.ReductionRatioNonPow2;
                if ((reductionRatios != null)
                    && (reductionRatios.Length > 0))
                {
                    uint defaultValue = (uint)reductionRatios.GetValue(reductionRatios.Length - 1);
                    foreach (uint value in reductionRatios)
                    {
                        if (value == s_ReductionRatioDefault)
                        {
                            defaultValue = s_ReductionRatioDefault;
                        }
                    }

                    Enumerated reductions = new Enumerated();
                    reductions.SetList(reductionRatios.OfType<object>());
                    reductions.DefaultValue = defaultValue;

                    m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR12NonPow2,
                        reductions);
                }
            }
        }

        private void AddPNIoSuppRr12Pow2Attribute()
        {
            if (TimingProperties != null)
            {
                Array reductionRatios = TimingProperties.ReductionRatioPow2;
                if ((reductionRatios != null)
                    && (reductionRatios.Length > 0))
                {
                    uint defaultValue = (uint)reductionRatios.GetValue(reductionRatios.Length - 1);
                    foreach (uint value in reductionRatios)
                    {
                        if (value == s_ReductionRatioDefault)
                        {
                            defaultValue = s_ReductionRatioDefault;
                        }
                    }

                    Enumerated reductions = new Enumerated();
                    reductions.SetList(reductionRatios.OfType<object>());
                    reductions.DefaultValue = defaultValue;

                    m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR12Pow2,
                        reductions);
                }
            }
        }

        private void AddPNIoSuppRr3NonPow2Attribute()
        {
            if (RtClass3TimingProperties != null)
            {
                Array reductionRatios = RtClass3TimingProperties.ReductionRatioNonPow2;
                if ((reductionRatios != null)
                    && (reductionRatios.Length > 0))
                {
                    uint defaultValue = (uint)reductionRatios.GetValue(reductionRatios.Length - 1);
                    foreach (uint value in reductionRatios)
                    {
                        if (value == s_ReductionRatioDefault)
                        {
                            defaultValue = s_ReductionRatioDefault;
                        }
                    }

                    Enumerated reductions = new Enumerated();
                    reductions.SetList(reductionRatios.OfType<object>());
                    reductions.DefaultValue = defaultValue;

                    m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppRR3NonPow2,
                        reductions);
                }
            }
        }

        private void AddPNIoSuppRr3Pow2Attribute()
        {
            if (RtClass3TimingProperties == null)
            {
                return;
            }
            Array reductionRatios = RtClass3TimingProperties.ReductionRatioPow2;
            if ((reductionRatios == null)
                || (reductionRatios.Length <= 0))
            {
                return;
            }

            uint defaultValue = (uint)reductionRatios.GetValue(reductionRatios.Length - 1);
            foreach (uint value in reductionRatios)
            {
                if (value == s_ReductionRatioDefault)
                {
                    defaultValue = s_ReductionRatioDefault;
                }
            }

            Enumerated reductions = new Enumerated();
            reductions.SetList(reductionRatios.OfType<object>());
            reductions.DefaultValue = defaultValue;

            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppRR3Pow2, reductions);
        }

        private void AddPNIrtArStartupModeAttribute()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || (IsPdevActive() == false))
            {
                return;
            }

            Enumerated startup = new Enumerated();
            List<int> startupList = new List<int>();

            startup.DefaultValue = (int)PNIrtArStartupMode.Legacy;

            if (m_InterfaceSubmoduleItem.RtClass3StartupMode != null)
            {
                foreach (string mode in m_InterfaceSubmoduleItem.RtClass3StartupMode)
                {
                    if (mode == Enum.GetName(typeof(PNIrtArStartupMode), PNIrtArStartupMode.Advanced))
                    {
                        startupList.Add((int)PNIrtArStartupMode.Advanced);
                        startup.DefaultValue = (int)PNIrtArStartupMode.Advanced;
                    }
                    else if (mode == Enum.GetName(typeof(PNIrtArStartupMode), PNIrtArStartupMode.Legacy))
                    {
                        startupList.Add((int)PNIrtArStartupMode.Legacy);
                    }
                }
            }
            else
            {
                startupList.Add((int)PNIrtArStartupMode.Legacy);
            }

            startup.SetList(startupList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtArStartupMode, startup);
        }

        //private void AddMaxNumberOfCrAttributes()
        //{
        //    if (m_InterfaceSubmoduleItem?.ApplicationRelations != null)
        //    {
        //        // Add these attributes only if GSDML contains it (different than 0)
        //        if (m_InterfaceSubmoduleItem.ApplicationRelations.MaxNumberOfInputCr != 0)
        //        {
        //            AddPNIoMaxNumberOfInputCrAttribute();
        //        }
        //        if (m_InterfaceSubmoduleItem.ApplicationRelations.MaxNumberOfOutputCr != 0)
        //        {
        //            AddPNIoMaxNumberOfOutputCrAttribute();
        //        }
        //    }
        //}
        //private void AddPNIoMaxNumberOfInputCrAttribute()
        //{
        //    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
        //        InternalAttributeNames.PnIoMaxNumberOfInputCR,
        //      m_InterfaceSubmoduleItem.ApplicationRelations.MaxNumberOfInputCr);
        //}

        //private void AddPNIoMaxNumberOfOutputCrAttribute()
        //{
        //    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
        //        InternalAttributeNames.PnIoMaxNumberOfOutputCR,
        //        m_InterfaceSubmoduleItem.ApplicationRelations.MaxNumberOfOutputCr);
        //}
        private void AddPNIrtForwardingModeAttribute()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || (IsPdevActive() == false))
            {
                return;
            }

            Enumerated forwarding = new Enumerated();
            List<int> forwardingList = new List<int>();

            forwarding.DefaultValue = 0;

            if (m_InterfaceSubmoduleItem.ForwardingMode != null)
            {
                foreach (string mode in m_InterfaceSubmoduleItem.ForwardingMode)
                {
                    if (mode == "Relative")
                    {
                        forwardingList.Add(2);
                        forwarding.DefaultValue = 2;
                    }
                    else if (mode == "Absolute")
                    {
                        forwardingList.Add(1);

                        // Use the value of relative mode if it exists
                        if ((int)forwarding.DefaultValue != 2)
                        {
                            forwarding.DefaultValue = 1;
                        }
                    }
                }
            }
            else if ((m_InterfaceSubmoduleItem.RtClass3StartupMode != null)
                     && (Array.IndexOf(
                         m_InterfaceSubmoduleItem.RtClass3StartupMode,
                         Enum.GetName(typeof(PNIrtArStartupMode), PNIrtArStartupMode.Advanced)) > -1))
            {
                //If advanced startup mode is supported then forwarding mode should be absolute 
                //even if it is not defined in GSDML
                forwardingList.Add(1);
                forwarding.DefaultValue = 1;
            }
            else
            {
                forwardingList.Add(0);
            }

            forwarding.SetList(forwardingList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIrtForwardingMode,
                forwarding);
        }

        private void AddPNIrtMaxRetentionTimeAttribute()
        {
            if (m_InterfaceSubmoduleItem.MaxRetentionTime != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMaxRetentionTime,
                    m_InterfaceSubmoduleItem.MaxRetentionTime);
            }
        }

        private void AddPNIrtMinFrameSendOffsetAttribute()
        {
            if (m_InterfaceSubmoduleItem.MinFSO != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMinFrameSendOffset,
                    m_InterfaceSubmoduleItem.MinFSO);
            }
        }

        private void AddPNIrtYellowSafetyMarginAttribute()
        {
            if (m_InterfaceSubmoduleItem.YellowSafetyMargin != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtYellowSafetyMargin,
                    m_InterfaceSubmoduleItem.YellowSafetyMargin);
            }
        }

        private void AddPNIrtMinYellowTimeAttribute()
        {
            if (m_InterfaceSubmoduleItem.MinYellowTime != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMinYellowTime,
                    m_InterfaceSubmoduleItem.MinYellowTime);
            }
        }

        
        private void AddPNIrtPllWindowAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (GetSupportedRtClasses().Contains(3))
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtPllWindow,
                        m_InterfaceSubmoduleItem.T_PLL_Max);
                }
            }
        }

        private void AddPNSwitchBridgingDelayFfwAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtSwitchBridgingDelayFFW,
                    m_InterfaceSubmoduleItem.MaxBridgeDelayFFW);
            }
        }

        private void AddPNMaxRecordSupportedAttribute()
        {
            uint maxRecordSize = m_DAP.MaxSupportedRecordSize;
            if (maxRecordSize == 0)
            {
                // Attribute may be missing in GSD file. In this case use default value.
                maxRecordSize = s_DefaultMaxRecordSize;
            }

            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIoMaxRecordSupported,
                maxRecordSize);
        }

        private void AddPNMultipleWriteSupportedAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIoMultipleWriteSupported,
                m_DAP.IsMultipleWriteSupported);
        }

        private void AddPNNameOfStationAtribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                InternalAttributeNames.PnIoNameOfStationNotTransferable,
                m_DAP.IsNameOfStationNotTransferable);
        }

        private void AddPNNetworkComponentDiagnosisSupportedAttribute()
        {
            bool networkComponentDiagnosisSupported = false;
            if (m_InterfaceSubmoduleItem != null)
            {
                networkComponentDiagnosisSupported = m_InterfaceSubmoduleItem.IsNetworkComponentDiagnosisSupported;
            }
            if (!networkComponentDiagnosisSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnNetworkComponentDiagnosisSupported,
                    networkComponentDiagnosisSupported);
            }
        }

        private void AddPNObjectUUIDLocalIndexAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnIoObjectUUIDLocalIndex,
                (int)m_DAP.ObjectUUIDLocalIndex);
        }

        private void AddPNParameterizationDisallowedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnParameterizationDisallowed,
                    m_InterfaceSubmoduleItem.IsParameterizationDisallowed);
            }
        }

        private void AddPNParameterizationSpeedupSupportedAttribute()
        {
            if (m_DAP.IsParameterizationSpeedupSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIoParameterizationSpeedupSupported,
                    true);
            }
        }

        private void AddPNPtpBoundarySupportedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.IsPTPBoundarySupported)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnPTPBoundarySupported,
                        m_InterfaceSubmoduleItem.IsPTPBoundarySupported);
                }
            }
        }

        private void AddPNSubmoduleIdentNumberAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnSubmoduleIdentNumber,
                    m_InterfaceSubmoduleItem.IdentNumber);
            }
        }

        private void AddPNSubmoduleModelSuppAttribute()
        {
            bool pDevSupported = (m_DAP.PhysicalSubslots != null) && (m_DAP.PhysicalSubslots.Length > 0);

            if ((m_InterfaceSubmoduleItem != null)
                || (m_DAP.VirtualSubmodules.Length > 1))
            {
                pDevSupported = true;
            }

            if (pDevSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoSubmoduleModelSupp, true);
            }
        }

        private void AddPNSubslotNumberAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    (int)m_InterfaceSubmoduleItem.SubslotNumber);
            }
        }

        private void AddNodeIPSubnetMaskUnrestrictedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.NodeIPSubnetMaskUnrestricted,
                    true);
            }
        }

        private void AddPNSupportedSyncProtocolsAttribute()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || (IsPdevActive() == false))
            {
                return;
            }

            bool ptcpSupported = false;
            bool rtaSupported = false;
            bool rtcSupported = false;

            bool rtClass2Supported = false;
            bool rtClass3Supported = false;

            if (m_InterfaceSubmoduleItem.SupportedSyncProtocols != null)
            {
                foreach (string protocol in m_InterfaceSubmoduleItem.SupportedSyncProtocols)
                {
                    switch (protocol)
                    {
                        case "PTCP":
                            ptcpSupported = true;
                            break;
                    }
                }
            }

            // if PTCP isn't supported check if RTA or RTC are supported
            if (!ptcpSupported)
            {
                IList<uint> supportedRtClasses = GetSupportedRtClasses();
                if (supportedRtClasses.Count > 0)
                {
                    foreach (uint rtClass in supportedRtClasses)
                    {
                        if (rtClass == 2)
                        {
                            rtClass2Supported = true;
                        }
                        else if (rtClass == 3)
                        {
                            rtClass3Supported = true;
                        }
                    }
                }
            }

            // if PTCP isn't supported, but RT-Class 2 is supported -> RTA
            if (!ptcpSupported && rtClass2Supported)
            {
                rtaSupported = true;
            }

            // if PTCP isn't supported, but RT-Class 3 is supported -> RTC
            if (!ptcpSupported && rtClass3Supported)
            {
                rtcSupported = true;
            }

            if (ptcpSupported
                || rtaSupported
                || rtcSupported)
            {
                Enumerated suppProtocols = new Enumerated();

                List<uint> protocols = new List<uint>();

                if (rtcSupported)
                {
                    protocols.Add(2);
                    suppProtocols.DefaultValue = 2;
                }
                if (rtaSupported)
                {
                    protocols.Add(1);
                    suppProtocols.DefaultValue = 1;
                }
                if (ptcpSupported)
                {
                    protocols.Add(3);
                    suppProtocols.DefaultValue = 3;
                }

                suppProtocols.SetList(protocols.Cast<object>());

                m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    suppProtocols);
            }
        }

        private void AddSuppFrameClassAttribute()
        {
            if (m_InterfaceSubmoduleItem == null)
            {
                return;
            }

            List<uint> supportedRtClasses = GetSupportedRtClasses();

            Enumerated suppClass = new Enumerated();

            foreach (uint supportedClass in supportedRtClasses.ToList())
            {
                suppClass.List.Add((PNIOFrameClass)supportedClass);
            }
            suppClass.SetList(supportedRtClasses.Cast<object>());

            // RTClass 1 should be used as default value, but some DAPs don't support
            // RTClass 1 in this case take the lowest RTClass
            suppClass.DefaultValue = supportedRtClasses[0];

            foreach (uint rtClass in supportedRtClasses)
            {
                if (rtClass == s_RtClassDefault)
                {
                    suppClass.DefaultValue = rtClass;
                }
            }

            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIoSuppFrameClass,
                suppClass);
        }

        private void AddSupportedProtocolsAttributes()
        {
            bool lldpSupported = false;

            if (IsPdevActive())
            {
                if (m_InterfaceSubmoduleItem.SupportedProtocols != null)
                {
                    foreach (string protocol in m_InterfaceSubmoduleItem.SupportedProtocols)
                    {
                        if (protocol == "LLDP")
                        {
                            lldpSupported = true;
                        }
                    }
                }
            }
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnLLDPSupported, lldpSupported);
        }

        private void AddSuppScf12Attribute()
        {
            if (TimingProperties != null)
            {
                Enumerated scf12 = new Enumerated();

                scf12.SetList(TimingProperties.SendClock.OfType<object>());
                bool defaultScfFound = false;
                foreach (uint sendclock in TimingProperties.SendClock)
                {
                    if (sendclock == s_SendclockDefault)
                    {
                        defaultScfFound = true;
                        break;
                    }
                }

                scf12.DefaultValue = s_SendclockDefault;
                if (!defaultScfFound)
                {
                    scf12.DefaultValue = (uint)TimingProperties.SendClock.GetValue(0);
                }

                m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF12, scf12);
            }
        }

        private void AddSuppScf3Attribute()
        {
            if (RtClass3TimingProperties != null)
            {
                Enumerated scf3 = new Enumerated();
                scf3.SetList(RtClass3TimingProperties.SendClock.OfType<object>());

                bool defaultScfFound = false;
                foreach (uint sendclock in RtClass3TimingProperties.SendClock)
                {
                    if (sendclock == s_SendclockDefault)
                    {
                        defaultScfFound = true;
                        break;
                    }
                }

                scf3.DefaultValue = s_SendclockDefault;
                if (!defaultScfFound)
                {
                    scf3.DefaultValue = (uint)RtClass3TimingProperties.SendClock.GetValue(0);
                }

                m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF3, scf3);
            }
        }

        private void AddVendorIdAttribute()
        {
            m_Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.PnVendorId,
                (int)m_GsdDevice.Info.VendorIdentNumber);
        }

        private List<uint> GetSupportedRtClasses()
        {
            List<uint> rtClasses = new List<uint>();

            bool rTClassesFound = false;
            if ((m_InterfaceSubmoduleItem.SupportedRTClasses != null)
                && (m_InterfaceSubmoduleItem.SupportedRTClasses.Length > 0))
            {
                rTClassesFound = true;
                foreach (string rtClass in m_InterfaceSubmoduleItem.SupportedRTClasses)
                {
                    if (rtClass == "RT_CLASS_1")
                    {
                        rtClasses.Add(1);
                    }
                    else if (rtClass == "RT_CLASS_2")
                    {
                        rtClasses.Add(2);
                    }
                    else if (rtClass == "RT_CLASS_3")
                    {
                        rtClasses.Add(3);
                    }
                }
                // If the attribute SupportedRT_Classes is in the GSD file and it
                // contains the value RT_CLASS_1 the IO device also supports the
                // RT_Class 2.
                if (rtClasses.Contains(1)
                    && !rtClasses.Contains(2))
                {
                    rtClasses.Add(2);
                }
            }

            if (!rTClassesFound)
            {
                RTClasses maxRtClass = m_InterfaceSubmoduleItem.SupportedRTClass;
                if (maxRtClass == RTClasses.GSDClass2)
                {
                    rtClasses.Add(1);
                    rtClasses.Add(2);
                }
                else if (maxRtClass == RTClasses.GSDClass3)
                {
                    rtClasses.Add(1);
                    rtClasses.Add(3);
                }
                else
                {
                    rtClasses.Add(1);
                }
            }
            return rtClasses;
        }

        private void InitDecorators()
        {
            m_Interface.DecorationList.Add(typeof(PNIOInterfaceBusinessLogic));

            // There can only be one PNDeviceConfigStrategy.
            if (m_DAP.IsSharedDeviceSupported)
            {
                m_Interface.DecorationList.Add(typeof(PNSharedDeviceConfigStrategy));
            }
            else
            {
                m_Interface.DecorationList.Add(typeof(PNDeviceConfigStrategy));
            }
            m_Interface.DecorationList.Add(typeof(PNIOInterfaceDecentralBusinessLogic));

            if (IsIrtCluster)
            {
                m_Interface.DecorationList.Add(typeof(PNIrtIFDecentralBL));
            }

            if (m_InterfaceSubmoduleItem == null)
            {
                return;
            }
            if (m_InterfaceSubmoduleItem.IsIsochroneModeSupported)
            {
                m_Interface.DecorationList.Add(typeof(PNIsochronIFDecentralBL));
            }
            if (m_InterfaceSubmoduleItem.IsIsochroneModeSupported)
            {
                m_Interface.DecorationList.Add(typeof(PNGsdIFPluggablePortExtensionBL));
            }
            if (m_InterfaceSubmoduleItem.MediaRedundancy != null)
            {
                m_Interface.DecorationList.Add(typeof(PNMrpInterfaceBusinessLogic));
            }
            if (m_InterfaceSubmoduleItem.SNMP_FeaturesSupported != null)
            {
                foreach (var feature in m_InterfaceSubmoduleItem.SNMP_FeaturesSupported)
                {
                    if (feature.Equals(SnmpSupportedFeatures.GSD_SNMPSupportedFeature_SNMPAdjust))
                    {
                        m_Interface.DecorationList.Add(typeof(SnmpInterfaceDecentralBL));
                        break;
                    }
                }
                        
            }
        }

        private bool IsPdevActive()
        {
            if (m_InterfaceSubmoduleItem == null)
            {
                return false;
            }

            if (m_InterfaceSubmoduleItem.IsParameterizationDisallowed)
            {
                return false;
            }

            return true;
        }
        private void AddDcpEnableReadOnlyAttribute()
        {
            if (m_InterfaceSubmoduleItem?.DCP_FeaturesSupported != null)
            {
                foreach (var feature in m_InterfaceSubmoduleItem.DCP_FeaturesSupported)
                {
                    if (feature.Equals(DcpSupportedFeatures.GSDDCPSupportedFeatureRejectDCPSet))
                    {
                        m_Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.DcpEnableReadOnly, true);
                        break;
                    }
                }
            }
        }

        #region Constants and Enums

        // Contains all constants and enums
        private const uint s_ReductionRatioDefault = 2;

        private const uint s_SendclockDefault = 32;

        private const uint s_RtClassDefault = 1;

        private const string s_MrpRoleClient = "Client";

        private const string s_MrpRoleManager = "Manager";

        private const string s_MrpRoleManagerAuto = "Manager (Auto)";

        private const int s_DefaultMaxRecordSize = 4068;

        #endregion

        #region DFP

        private void ConvertDfp()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || (IsPdevActive() == false))
            {
                return;
            }

            AddPNIrtMaxDfpFeedAttribute();
            AddPNIrtMaxDfpFramesAttribute();
            AddPNIrtAlignDfpSubframesAttribute();
            AddPNIrtPeerToPeerJitterAttribute();
            AddPNIrtDfpOutboundTruncationSupportedAttribute();
        }

        private void AddPNIrtMaxDfpFramesAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMaxDfpFrames,
                    m_InterfaceSubmoduleItem.MaxDfpFrames);
            }
        }

        private void AddPNIrtMaxDfpFeedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtMaxDfpFeed,
                    m_InterfaceSubmoduleItem.MaxDfpFeed);
            }
        }

        private void AddPNIrtAlignDfpSubframesAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                if (m_InterfaceSubmoduleItem.AlignDfpSubframes)
                {
                    m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtAlignDfpSubframes,
                        m_InterfaceSubmoduleItem.AlignDfpSubframes);
                }
            }
        }

        private void AddPNIrtPeerToPeerJitterAttribute()
        {
            if ((m_InterfaceSubmoduleItem != null)
                && (m_InterfaceSubmoduleItem.PeerToPeerJitter != 0))
            {
                m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPeerToPeerJitter,
                    m_InterfaceSubmoduleItem.PeerToPeerJitter);
            }
        }

        private void AddPNIrtDfpOutboundTruncationSupportedAttribute()
        {
            if ((m_InterfaceSubmoduleItem != null)
                && m_InterfaceSubmoduleItem.DFP_OutboundTruncationSupported)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtDfpOutboundTruncationSupported,
                    m_InterfaceSubmoduleItem.DFP_OutboundTruncationSupported);
            }
        }

        #endregion

        #region MRP

        private void ConvertMrp()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || (IsPdevActive() == false))
            {
                return;
            }

            if (m_InterfaceSubmoduleItem.MediaRedundancy == null)
            {
                return;
            }
        
            m_Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnMrpSupported, true);
            m_Interface.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.PnMrpDomainNameService,
                "mrpdomain-1");
            m_Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnMrpManagerPriority, 0xA000);

            AddPNMrpRoleAttribute();

            AddPNMrpSupportedMultipleRoleAttribute();

            AddPNMrpMaxInstancesAttribute();

            if (IsIrtCluster)
            {
                AddPNIrtMrtSupportedAttribute();
            }
        }

        private void AddPNIrtSwitchBridgingDelayAttribute()
        {
            if (m_InterfaceSubmoduleItem.MaxBridgeDelay != 0)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<int>(
                    InternalAttributeNames.PnIrtSwitchBridgingDelay,
                    (int)m_InterfaceSubmoduleItem.MaxBridgeDelay);
            }
        }

        private void AddPNMrpRoleAttribute()
        {
            Enumerated mrpRoles = new Enumerated();
            List<PNMrpRole> rolesList = new List<PNMrpRole>();

            rolesList.Add(PNMrpRole.NotInRing);
            mrpRoles.DefaultValue = PNMrpRole.NotInRing;

            foreach (MediaRedundancyRoles role in m_InterfaceSubmoduleItem.MediaRedundancy.MR_SupportedRoles)
            {
                switch (role)
                {
                    case MediaRedundancyRoles.GSDManagerAuto:
                        {
                            rolesList.Add(PNMrpRole.NormManagerAuto);
                            break;
                        }
                    case MediaRedundancyRoles.GSDManager:
                        {
                            rolesList.Add(PNMrpRole.Manager);
                            break;
                        }
                    case MediaRedundancyRoles.GSDClient:
                        {
                            rolesList.Add(PNMrpRole.Client);
                            break;
                        }
                }
            }

            mrpRoles.SetList(rolesList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute(InternalAttributeNames.PnMrpRole, mrpRoles);
        }

        private void AddPNMrpSupportedMultipleRoleAttribute()
        {
            if ((m_InterfaceSubmoduleItem == null)
                || !m_InterfaceSubmoduleItem.MediaRedundancy.IsMR_SupportedMultipleRoles)
            {
                return;
            }

            Enumerated mrpRoles = new Enumerated();
            List<int> rolesList = new List<int>();

            foreach (MediaRedundancyRoles role in m_InterfaceSubmoduleItem.MediaRedundancy.MR_SupportedMultipleRoles)
            {
                switch (role)
                {
                    //MrpRole names are same with Multiple mrp role names
                    case MediaRedundancyRoles.GSDManager:
                        {
                            rolesList.Add((int)PNMrpRole.Manager);

                            break;
                        }
                    case MediaRedundancyRoles.GSDClient:
                        {
                            rolesList.Add((int)PNMrpRole.Client);

                            break;
                        }
                    case MediaRedundancyRoles.GSDManagerAuto:
                        {
                            rolesList.Add((int)PNMrpRole.NormManagerAuto);
                            break;
                        }
                }
            }

            mrpRoles.SetList(rolesList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnMrpSupportedMultipleRole,
                mrpRoles);
        }

        private void AddPNMrpMaxInstancesAttribute()
        {
            if (m_InterfaceSubmoduleItem == null)
            {
                return;
            }

            m_Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnMrpMaxInstances,
                m_InterfaceSubmoduleItem.MediaRedundancy.MaxMRP_Instances);
        }

        private void AddPNIrtMrtSupportedAttribute()
        {
            if (m_InterfaceSubmoduleItem != null)
            {
                m_Interface.AttributeAccess.AddAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtMediaRedTimerSupported,
                    m_InterfaceSubmoduleItem.MediaRedundancy.IsMrtSupported);
            }
        }

        private void AddPNIrtFragmentationTypeAttribute()
        {
            if (m_InterfaceSubmoduleItem == null)
            {
                return;
            }

            if (m_InterfaceSubmoduleItem.FragmentationType == FragmentationTypes.GSDFragmentationTypeNone)
            {
                return;
            }

            Enumerated fragmentation = new Enumerated();
            List<byte> fragmentationList = new List<byte>();

            fragmentation.DefaultValue = (byte)PNIRTFragmentationType.None;

            switch (m_InterfaceSubmoduleItem.FragmentationType)
            {
                case FragmentationTypes.GSDFragmentationTypeNone:
                    break;
                case FragmentationTypes.GSDFragmentationTypeDynamic:
                    fragmentationList.Add((byte)PNIRTFragmentationType.Dynamic);

                    fragmentation.DefaultValue = (byte)PNIRTFragmentationType.Dynamic;
                    break;
                case FragmentationTypes.GSDFragmentationTypeStatic:
                    fragmentationList.Add((byte)PNIRTFragmentationType.Static);

                    fragmentation.DefaultValue = (byte)PNIRTFragmentationType.Static;
                    break;
            }

            fragmentation.SetList(fragmentationList.Cast<object>());
            m_Interface.AttributeAccess.AddAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIrtFragmentationType,
                fragmentation);
        }

        #endregion
    }
}