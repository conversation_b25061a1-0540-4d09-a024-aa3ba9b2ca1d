/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronConfigUtility.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Structs;

#endregion

namespace PNConfigLib.HWCNBL.Isochrone
{
    internal static class IsochronConfigUtility
    {
        /// <summary>
        /// Fills the IsochronousModeData block of Config 2003.
        /// Only filled when InterfaceSubmodule has PNIsochron=true.
        /// </summary>
        /// <param name="interfaceSubmodule">Interface sub-module</param>
        /// <param name="module">Sub-module (I/O sub-modules)</param>
        /// <param name="returnValueOk">
        /// Output parameter. True, if the block can be filled
        /// successfully.
        /// </param>
        /// <param name="isoDataModel"></param>
        /// <returns>Byte array which contains the Config info</returns>
        internal static byte[] GetIsochronousModeData(
            Interface interfaceSubmodule,
            PclObject module,
            out bool returnValueOk,
            PNIsoDataModel isoDataModel = PNIsoDataModel.Model1)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            //Device interface relevant attributes
            uint tiValid = 0;
            uint toValid = 0;
            uint isoTi = 0;
            uint isoTo = 0;

            if (interfaceSubmodule != null)
            {
                isoTi = (uint)interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoTi, ac, 0);
                ac.Reset();

                isoTo = (uint)interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoTo, ac, 0);
                ac.Reset();

                tiValid =
                    Convert.ToUInt32(
                        interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIsoT_IO_InputValid,
                            ac,
                            0),
                        CultureInfo.InvariantCulture);
                ac.Reset();

                long pnisoT_IO_OutputValid = interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_IO_OutputValid, ac, 0);
                long calcPartIrt = 0;

                SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
                if (syncDomain != null)
                {
                    SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
                    if (syncDomainBl != null)
                    {
                        calcPartIrt = syncDomainBl.PNPlannerResults.CalculatedPartIrt;
                    }
                }

                if ((pnisoT_IO_OutputValid > calcPartIrt)
                    || interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsoUseDeviceLocalOutputValid, ac.GetNew(), false))
                {
                    toValid = Convert.ToUInt32(interfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_IO_OutputValid, ac, 0), CultureInfo.InvariantCulture);
                }
                else
                {
                    toValid = Convert.ToUInt32(calcPartIrt, CultureInfo.InvariantCulture);
                }
            }

            //Submodlue relevant attributes
            int slotNumber = 0;
            int subSlotNumber = 1;

            if (module != null)
            {
                if (module is Submodule)
                {
                    subSlotNumber = module.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnSubslotNumber,
                        ac.GetNew(),
                        1);

                    PclObject container = module.ParentObject;

                    if (container != null)
                    {
                        slotNumber = container.AttributeAccess.GetAnyAttribute(
                            InternalAttributeNames.PositionNumber,
                            ac.GetNew(),
                            0);
                    }
                }
                else if (module is Module)
                {
                    slotNumber = module.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PositionNumber, ac.GetNew(), 0);
                }
            }

            //IO-System relevant attribute
            int cacf = 0;
            DataModel.PCLObjects.IOSystem ioSystem = interfaceSubmodule?.PNIOD.IOSystem;
            if (ioSystem != null)
            {
                ac.Reset();
                cacf = ioSystem.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnIsoCacf, ac, 1);
            }

            //Controller interface relevant attribute
            int scf = 0;
            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);
            if (controllerInterfaceSubmodule != null)
            {
                scf =
                    (int)
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoSendClockFactor,
                        ac.GetNew(),
                        0);
            }
            IsochronModeDataBlockStruct isochronModeDataBlock = new IsochronModeDataBlockStruct();

            isochronModeDataBlock.BlockLength = 28;
            isochronModeDataBlock.BlockType = 0x204;
            isochronModeDataBlock.BlockVersionHigh = 0x1;
            isochronModeDataBlock.BlockVersionLow = 0x0;
            isochronModeDataBlock.SlotNumber = slotNumber;
            isochronModeDataBlock.SubSlotNumber = subSlotNumber;
            isochronModeDataBlock.CACF = cacf;
            isochronModeDataBlock.TDC = scf;

            if (isoDataModel == PNIsoDataModel.Model1)
            {
                FillIsochronousBlock_Model1(isochronModeDataBlock, isoTi, isoTo, tiValid, toValid);
            }

            returnValueOk = true;

            return isochronModeDataBlock.ToByteArray();
        }

        private static void FillIsochronousBlock_Model1(
            IsochronModeDataBlockStruct isochronModeDataBlock,
            uint isoTi,
            uint isoTo,
            uint tiValid,
            uint toValid)
        {
            isochronModeDataBlock.TI = isoTi;
            isochronModeDataBlock.TO = isoTo;
            isochronModeDataBlock.T_InputValid = tiValid;
            isochronModeDataBlock.T_OutputValid = toValid;
        }

    }
}