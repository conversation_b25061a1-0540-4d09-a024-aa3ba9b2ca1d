using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using PNConfigTool.Models;
using PNConfigTool.ViewModels;
using PNConfigTool.Services;
using PNConfigTool.Views.Windows;
using System.IO;
using ViewModelGSDMLDeviceNode = PNConfigTool.ViewModels.GSDMLDeviceNode;
using ViewModelDAPNode = PNConfigTool.ViewModels.DAPNode;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using PNConfigLib.DataModel;
using PNConfigLib.Gsd.Interpreter;
using PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.GSDImport;
using GSDI;

namespace PNConfigTool.Views.Pages
{
    /// <summary>
    /// ControllerConfigPage.xaml 的交互逻辑
    /// </summary>
    public partial class ControllerConfigPage : Page, INavigationAware, INavigationAwareLeaving
    {
        private ProjectManager _projectManager;
        private ObservableCollection<DeviceViewModel> _devices = new ObservableCollection<DeviceViewModel>();
        private DeviceCatalogPage? _deviceCatalogPage;
        private object? _selectedCatalogItem; // 存储当前选中的目录项
        private bool _isModified = false;

        public ControllerConfigPage()
        {
            InitializeComponent();
            _projectManager = ProjectManager.Instance;
            
            // 初始化设备列表，现在会在OnNavigatedTo中进行，确保每次导航到本页面时都刷新数据
            
            // 加载设备目录页面
            LoadDeviceCatalog();
            
            // 设置DataGrid为拖放目标
            DevicesDataGrid.AllowDrop = true;
            DevicesDataGrid.Drop += DevicesDataGrid_Drop;
            DevicesDataGrid.DragOver += DevicesDataGrid_DragOver;
            
            // 初始化按钮状态
            AddDeviceButton.IsEnabled = false; // 默认禁用添加按钮，直到选择了DAP节点
            DeleteDeviceButton.IsEnabled = false; // 默认禁用删除按钮，直到在设备表中选择了设备
            
            // 添加设备表选择变更事件
            DevicesDataGrid.SelectionChanged += DevicesDataGrid_SelectionChanged;
            
            // 注册修改跟踪事件
            RegisterModificationTracking();
        }

        /// <summary>
        /// 注册修改跟踪事件
        /// </summary>
        private void RegisterModificationTracking()
        {
            // 监听设备集合变更
            _devices.CollectionChanged += (s, e) => _isModified = true;
            
            // 监听设备属性变更
            DevicesDataGrid.CellEditEnding += (s, e) => _isModified = true;
        }

        /// <summary>
        /// 导航到此页面时执行
        /// </summary>
        public void OnNavigatedTo(object? parameter)
        {
            Debug.WriteLine("ControllerConfigPage: 导航到此页面");
            
            // 每次导航到此页面时重新初始化设备列表，确保显示最新数据
            InitializeDevicesList();
            
            // 重置修改状态
            _isModified = false;
        }
        
        /// <summary>
        /// 实现INavigationAwareLeaving接口，在页面离开前保存更改
        /// </summary>
        public bool OnNavigatedFrom()
        {
            Debug.WriteLine($"ControllerConfigPage.OnNavigatedFrom: _isModified={_isModified}, 设备数量={_devices.Count}");

            // 检查是否有设备需要保存（即使_isModified为false）
            bool hasDevicesToSave = _devices.Count > 0;
            bool needsSaving = _isModified || hasDevicesToSave;

            if (needsSaving)
            {
                // 如果有设备或有修改，自动保存而不询问用户
                // 这确保设备配置总是被保存
                Debug.WriteLine("检测到需要保存的设备配置，自动保存");
                SaveControllerConfig();
                return true;
            }

            // 如果没有修改且没有设备，允许导航
            Debug.WriteLine("没有需要保存的内容，允许导航");
            return true;
        }
        
        /// <summary>
        /// 加载设备目录页面
        /// </summary>
        private void LoadDeviceCatalog()
        {
            _deviceCatalogPage = new DeviceCatalogPage();
            DeviceCatalogFrame.Navigated += DeviceCatalogFrame_Navigated;
            DeviceCatalogFrame.Navigate(_deviceCatalogPage);
        }

        /// <summary>
        /// 处理设备目录页面导航完成事件
        /// </summary>
        private void DeviceCatalogFrame_Navigated(object sender, System.Windows.Navigation.NavigationEventArgs e)
        {
            if (e.Content is DeviceCatalogPage deviceCatalogPage)
            {
                // 隐藏底部按钮区域
                HideDeviceCatalogButtons(deviceCatalogPage);
                
                // 监听树视图选择变更事件
                deviceCatalogPage.DeviceTreeView.SelectedItemChanged += DeviceTreeView_SelectedItemChanged;
                
                // 配置拖放行为
                SetupDragDropForTreeView(deviceCatalogPage.DeviceTreeView);
            }
        }
        
        /// <summary>
        /// 为TreeView设置拖放行为
        /// </summary>
        private void SetupDragDropForTreeView(TreeView treeView)
        {
            // 使用附加属性绑定事件，因为TreeView的子项是动态创建的
            treeView.PreviewMouseLeftButtonDown += TreeView_PreviewMouseLeftButtonDown;
            treeView.MouseMove += TreeView_MouseMove;
        }
        
        // 拖放相关变量
        private Point _startPoint;
        private bool _isDragging = false;
        
        /// <summary>
        /// 处理TreeView鼠标按下事件，记录起始位置
        /// </summary>
        private void TreeView_PreviewMouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            _startPoint = e.GetPosition(null);
        }
        
        /// <summary>
        /// 处理TreeView鼠标移动事件，启动拖放操作
        /// </summary>
        private void TreeView_MouseMove(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (e.LeftButton == System.Windows.Input.MouseButtonState.Pressed && !_isDragging)
            {
                Point position = e.GetPosition(null);
                
                // 检查是否移动了足够的距离开始拖放
                if (Math.Abs(position.X - _startPoint.X) > SystemParameters.MinimumHorizontalDragDistance ||
                    Math.Abs(position.Y - _startPoint.Y) > SystemParameters.MinimumVerticalDragDistance)
                {
                    if (_selectedCatalogItem is ViewModelDAPNode dapNode)
                    {
                        // 开始拖放操作
                        _isDragging = true;
                        DataObject dragData = new DataObject("DAPNode", dapNode);
                        System.Windows.DragDrop.DoDragDrop((DependencyObject)sender, dragData, System.Windows.DragDropEffects.Copy);
                        _isDragging = false;
                    }
                }
            }
        }
        
        /// <summary>
        /// 处理DataGrid拖放进入事件
        /// </summary>
        private void DevicesDataGrid_DragOver(object sender, System.Windows.DragEventArgs e)
        {
            if (e.Data.GetDataPresent("DAPNode"))
            {
                e.Effects = System.Windows.DragDropEffects.Copy;
            }
            else
            {
                e.Effects = System.Windows.DragDropEffects.None;
            }
            e.Handled = true;
        }
        
        /// <summary>
        /// 处理DataGrid拖放释放事件
        /// </summary>
        private void DevicesDataGrid_Drop(object sender, System.Windows.DragEventArgs e)
        {
            if (e.Data.GetDataPresent("DAPNode"))
            {
                // 检查设备数量限制
                if (_devices.Count >= 64)
                {
                    MessageBox.Show("最多支持64个设备", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }
            
                ViewModelDAPNode dapNode = (ViewModelDAPNode)e.Data.GetData("DAPNode");
                AddDeviceFromDAP(dapNode);
                e.Handled = true;
            }
        }
        
        /// <summary>
        /// 隐藏DeviceCatalogPage中的按钮区域
        /// </summary>
        private void HideDeviceCatalogButtons(DeviceCatalogPage deviceCatalogPage)
        {
            try
            {
                // 查找页面根Grid
                if (deviceCatalogPage.Content is Grid mainGrid && mainGrid.Children.Count > 0)
                {
                    // 尝试查找底部按钮所在的StackPanel
                    foreach (UIElement element in mainGrid.Children)
                    {
                        if (element is StackPanel panel && Grid.GetRow(panel) == 3) // 底部按钮在Row=3
                        {
                            panel.Visibility = Visibility.Collapsed;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不中断运行
                System.Diagnostics.Debug.WriteLine($"隐藏DeviceCatalogPage按钮时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理设备目录树选择变更事件
        /// </summary>
        private void DeviceTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            // 保存当前选中的项
            _selectedCatalogItem = e.NewValue;
            
            // 更新添加按钮状态
            AddDeviceButton.IsEnabled = _selectedCatalogItem is ViewModelDAPNode;
        }
        
        /// <summary>
        /// 处理设备表选择变更事件
        /// </summary>
        private void DevicesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 更新删除按钮状态
            DeleteDeviceButton.IsEnabled = DevicesDataGrid.SelectedItem != null;
        }

        /// <summary>
        /// 初始化设备列表
        /// </summary>
        private void InitializeDevicesList()
        {
            try
            {
                Debug.WriteLine("开始初始化设备列表");
                _devices.Clear();

                // 检查项目管理器状态
                if (_projectManager == null)
                {
                    Debug.WriteLine("警告：ProjectManager为null");
                    DevicesDataGrid.ItemsSource = _devices;
                    return;
                }

                if (_projectManager.CurrentProject == null)
                {
                    Debug.WriteLine("当前没有打开的项目");
                    DevicesDataGrid.ItemsSource = _devices;
                    return;
                }

                Debug.WriteLine($"当前项目: {_projectManager.CurrentProject.ProjectMetadata.ProjectName}");

                // 检查配置设置
                if (_projectManager.CurrentProject.ConfigurationSettings == null)
                {
                    Debug.WriteLine("警告：ConfigurationSettings为null");
                    DevicesDataGrid.ItemsSource = _devices;
                    return;
                }

                if (_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices == null)
                {
                    Debug.WriteLine("警告：DecentralDevices为null");
                    DevicesDataGrid.ItemsSource = _devices;
                    return;
                }

                Debug.WriteLine($"项目中的设备数量: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");

                // 如果项目中已经有设备配置，则加载它们
                if (_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count > 0)
                {
                    int deviceNumber = 1;
                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                    {
                        try
                        {
                            Debug.WriteLine($"处理设备 {deviceNumber}: {device.DeviceRefID ?? "未知设备"}");

                            // 获取对应的ListOfNodes设备节点以获取更多信息
                            var deviceNode = _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices
                                .FirstOrDefault(d => d.DeviceID == device.DeviceRefID);

                            if (deviceNode == null)
                            {
                                Debug.WriteLine($"警告：设备 {device.DeviceRefID} 没有对应的ListOfNodes条目");
                            }

                            // 安全地获取IP地址
                            string ipAddress = "0.0.0.0";
                            string pnDeviceName = "error_pndevice_name";
                            try
                            {
                                ipAddress = device.DecentralDeviceInterface?.EthernetAddresses?.IPProtocol?.SetInTheProject?.IPAddress ?? "0.0.0.0";
                                pnDeviceName = device.DecentralDeviceInterface?.EthernetAddresses?.PROFINETDeviceName?.PNDeviceName ?? "error_pndevice_name";
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"获取设备IP地址时出错: {ex.Message}");
                                Debug.WriteLine($"获取设备PNDeviceName时出错: {ex.Message}");
                            }

                            var deviceViewModel = new DeviceViewModel
                            {
                                DeviceNumber = deviceNumber++,
                                DeviceType = !string.IsNullOrEmpty(device.DeviceType) ? device.DeviceType : (device.DeviceRefID ?? ""),
                                DeviceName = pnDeviceName,
                                IPSettings = "用户设置",
                                IPAddress = ipAddress,
                                Comment = string.IsNullOrEmpty(deviceNode?.GSDPath) ? "" : $"GSDML: {Path.GetFileName(deviceNode.GSDPath)}"
                            };

                            Debug.WriteLine($"创建设备视图模型: {deviceViewModel.DeviceName}, IP: {deviceViewModel.IPAddress}");

                            // 添加属性变更事件处理程序
                            deviceViewModel.PropertyChanged += DeviceViewModel_PropertyChanged;
                            _devices.Add(deviceViewModel);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"处理设备时出错: {ex.Message}");
                            Debug.WriteLine($"设备信息: DeviceRefID={device.DeviceRefID}, Name={device.DeviceRefID}");
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("项目中没有设备配置");
                }

                DevicesDataGrid.ItemsSource = _devices;
                Debug.WriteLine($"设备列表初始化完成，共加载 {_devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化设备列表时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 确保UI不会崩溃
                _devices.Clear();
                DevicesDataGrid.ItemsSource = _devices;

                MessageBox.Show($"初始化设备列表时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理设备视图模型属性变更事件
        /// </summary>
        private void DeviceViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (sender is DeviceViewModel deviceViewModel)
            {
                // 记录更详细的属性变更信息
                if (e.PropertyName == nameof(DeviceViewModel.IPAddress))
                {
                    Debug.WriteLine($"设备IP地址变更: {deviceViewModel.DeviceName}, IP: {deviceViewModel.IPAddress}");
                }
                else
                {
                    Debug.WriteLine($"设备属性变更: {deviceViewModel.DeviceName}, 属性: {e.PropertyName}");
                }
                
                // 当属性改变时标记项目已修改
                _projectManager.IsProjectModified = true;
                this._isModified = true;

                // 立即更新项目设备配置 (in-memory model only)
                UpdateProjectDeviceConfig(deviceViewModel);
                
                // 强制保存项目以确保更改被持久化
                _projectManager.SaveProject();
            }
        }

        /// <summary>
        /// 更新项目中的设备配置
        /// </summary>
        private void UpdateProjectDeviceConfig(DeviceViewModel deviceViewModel)
        {
            if (_projectManager.CurrentProject == null || deviceViewModel == null)
                return;

            // 查找对应的设备配置
            int index = deviceViewModel.DeviceNumber - 1;
            if (index >= 0 && index < _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count)
            {
                var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices[index];

                // 更新设备配置 (in-memory)
                // 更新IP地址和PNDeviceName，不要修改DeviceType
                // deviceConfig.DeviceType = deviceViewModel.DeviceType;
                deviceConfig.DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = deviceViewModel.IPAddress;
                deviceConfig.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName = deviceViewModel.DeviceName;
                Debug.WriteLine($"已更新设备配置(内存): {deviceConfig.DeviceRefID}, IP: {deviceConfig.DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress}, PNDeviceName: {deviceConfig.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName}");
            }
        }

        /// <summary>
        /// 添加设备按钮点击事件
        /// </summary>
        private void AddDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            // 最多支持64个设备
            if (_devices.Count >= 64)
            {
                MessageBox.Show("最多支持64个设备", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 检查当前选中的项是否是DAPNode - 按钮应该已经禁用，但为了安全起见额外检查
            if (_selectedCatalogItem is ViewModelDAPNode dapNode)
            {
                AddDeviceFromDAP(dapNode);
            }
            else
            {
                // 这种情况下按钮应该被禁用，但为了代码健壮性保留此检查
                MessageBox.Show("请先选择要添加的DAP设备", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                AddDeviceButton.IsEnabled = false;
            }
        }
        
        /// <summary>
        /// 复制GSDML文件到项目GSDMLs文件夹
        /// </summary>
        /// <param name="sourcePath">源GSDML文件路径</param>
        /// <returns>项目中的目标路径</returns>
        private string CopyGSDMLToProjectFolder(string sourcePath)
        {
            Debug.WriteLine($"开始复制GSDML文件: {sourcePath}");
            
            if (string.IsNullOrEmpty(sourcePath))
            {
                Debug.WriteLine("源GSDML文件路径为空");
                return string.Empty;
            }
            
            if (!File.Exists(sourcePath))
            {
                Debug.WriteLine($"源GSDML文件不存在: {sourcePath}");
                return string.Empty;
            }

            try
            {
                // 获取项目目录
                if (_projectManager.CurrentProject == null)
                {
                    Debug.WriteLine("项目未创建，无法复制GSDML文件");
                    return sourcePath; // 返回原路径
                }
                
                if (string.IsNullOrEmpty(_projectManager.CurrentProjectFilePath))
                {
                    Debug.WriteLine("项目未保存，尝试先保存项目");
                    bool saved = _projectManager.SaveProject();
                    if (!saved || string.IsNullOrEmpty(_projectManager.CurrentProjectFilePath))
                    {
                        Debug.WriteLine("项目保存失败，无法复制GSDML文件");
                        return sourcePath; // 返回原路径
                    }
                }

                string? projectDirectory = Path.GetDirectoryName(_projectManager.CurrentProjectFilePath);
                if (string.IsNullOrEmpty(projectDirectory))
                {
                    Debug.WriteLine("无法获取项目目录");
                    return sourcePath; // 返回原路径
                }

                Debug.WriteLine($"项目目录: {projectDirectory}");
                
                // 创建GSDMLs文件夹
                string gsdmlsDirectory = Path.Combine(projectDirectory, "GSDMLs");
                if (!Directory.Exists(gsdmlsDirectory))
                {
                    Debug.WriteLine($"创建GSDMLs文件夹: {gsdmlsDirectory}");
                    Directory.CreateDirectory(gsdmlsDirectory);
                }
                else
                {
                    Debug.WriteLine($"GSDMLs文件夹已存在: {gsdmlsDirectory}");
                }

                // 获取源文件名
                string fileName = Path.GetFileName(sourcePath);
                string targetPath = Path.Combine(gsdmlsDirectory, fileName);

                Debug.WriteLine($"目标文件路径: {targetPath}");
                
                // 检查文件是否已经在项目目录中
                if (File.Exists(targetPath))
                {
                    Debug.WriteLine($"文件已存在于项目目录中: {targetPath}");
                    
                    // 检查文件内容是否相同
                    bool filesAreIdentical = FilesAreIdentical(sourcePath, targetPath);
                    if (filesAreIdentical)
                    {
                        Debug.WriteLine("文件内容相同，无需复制");
                        return targetPath; // 直接返回已存在的文件路径
                    }
                    
                    // 文件内容不同，使用唯一文件名
                    Debug.WriteLine($"文件内容不同，生成唯一文件名");
                    string fileNameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                    string extension = Path.GetExtension(fileName);
                    int counter = 1;
                    
                    while (File.Exists(targetPath))
                    {
                        fileName = $"{fileNameWithoutExt}_{counter}{extension}";
                        targetPath = Path.Combine(gsdmlsDirectory, fileName);
                        counter++;
                    }
                    
                    Debug.WriteLine($"新的目标文件路径: {targetPath}");
                }

                // 复制文件
                Debug.WriteLine($"开始复制文件: {sourcePath} -> {targetPath}");
                File.Copy(sourcePath, targetPath, true); // 使用overwrite=true确保覆盖
                Debug.WriteLine($"文件复制成功: {targetPath}");

                return targetPath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"复制GSDML文件时出错: {ex.Message}");
                Debug.WriteLine($"异常详情: {ex}");
                return sourcePath; // 出错时返回原路径
            }
        }

        /// <summary>
        /// 比较两个文件的内容是否相同
        /// </summary>
        /// <param name="file1">第一个文件路径</param>
        /// <param name="file2">第二个文件路径</param>
        /// <returns>如果文件内容相同则返回true，否则返回false</returns>
        private bool FilesAreIdentical(string file1, string file2)
        {
            try
            {
                // 首先比较文件大小
                var fi1 = new FileInfo(file1);
                var fi2 = new FileInfo(file2);

                if (fi1.Length != fi2.Length)
                {
                    return false;
                }

                // 比较文件内容
                const int BYTES_TO_READ = 8192;
                using (var fs1 = new FileStream(file1, FileMode.Open, FileAccess.Read))
                using (var fs2 = new FileStream(file2, FileMode.Open, FileAccess.Read))
                {
                    byte[] buffer1 = new byte[BYTES_TO_READ];
                    byte[] buffer2 = new byte[BYTES_TO_READ];
                    int bytesRead1, bytesRead2;

                    do
                    {
                        bytesRead1 = fs1.Read(buffer1, 0, BYTES_TO_READ);
                        bytesRead2 = fs2.Read(buffer2, 0, BYTES_TO_READ);

                        if (bytesRead1 != bytesRead2)
                        {
                            return false;
                        }

                        for (int i = 0; i < bytesRead1; i++)
                        {
                            if (buffer1[i] != buffer2[i])
                            {
                                return false;
                            }
                        }
                    } while (bytesRead1 > 0);

                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"比较文件时出错: {ex.Message}");
                return false; // 出错时假设文件不同
            }
        }

        /// <summary>
        /// 从DAP节点添加设备
        /// </summary>
        private void AddDeviceFromDAP(ViewModelDAPNode dapNode)
        {
            if (dapNode == null) return;
            
            Debug.WriteLine($"添加DAP节点: {dapNode.Name}, ID: {dapNode.Id}");
             
            // 获取DAP节点所属的GSDMLDeviceNode以获取更多信息
            ViewModelGSDMLDeviceNode? parentDevice = null;
            if (_deviceCatalogPage != null)
            {
                parentDevice = _deviceCatalogPage.FindParentDeviceForDAP(dapNode);
                Debug.WriteLine($"找到父设备: {(parentDevice != null ? parentDevice.Name : "未找到")}");
            }
             
            // 准备设备数据
            int newDeviceNumber = _devices.Count + 1;
            string deviceName = dapNode.Name;
            string deviceType = dapNode.Name; // 默认使用DAP节点名作为设备类型
            string gsdmlFileName = "未知GSDML文件";
            string gsdmlFilePath = string.Empty;
            string dnsCompatibleName = string.Empty;
            string dapId = dapNode.Id ?? "DAP1";

            // Get the next available IP address in the same subnet as the controller
            string nextIP = GetNextAvailableIPAddress();
  
            // 如果找到了父设备，获取更多详细信息
            if (parentDevice != null)
            {
                Debug.WriteLine($"父设备信息: 名称={parentDevice.Name}, ID={parentDevice.DeviceId}");
                
                if (!string.IsNullOrEmpty(parentDevice.FilePath))
                {
                    gsdmlFilePath = parentDevice.FilePath;
                    gsdmlFileName = Path.GetFileName(parentDevice.FilePath);
                    Debug.WriteLine($"GSDML文件路径: {gsdmlFilePath}");
                    
                    // 检查文件是否存在
                    if (File.Exists(gsdmlFilePath))
                    {
                        Debug.WriteLine($"GSDML文件存在: {gsdmlFilePath}");
                        
                        // 显示文件信息
                        var fileInfo = new FileInfo(gsdmlFilePath);
                        Debug.WriteLine($"文件大小: {fileInfo.Length} 字节");
                        Debug.WriteLine($"创建时间: {fileInfo.CreationTime}");
                        Debug.WriteLine($"修改时间: {fileInfo.LastWriteTime}");
                        
                        // 复制GSDML文件到项目GSDMLs文件夹
                        string projectGsdmlPath = CopyGSDMLToProjectFolder(gsdmlFilePath);
                        if (!string.IsNullOrEmpty(projectGsdmlPath))
                        {
                            gsdmlFilePath = projectGsdmlPath;
                            gsdmlFileName = Path.GetFileName(projectGsdmlPath);
                            Debug.WriteLine($"已复制GSDML文件到: {projectGsdmlPath}");

                            // 从GSDML文件中提取DAP信息，传入选择的DAP ID
                            var dapInfo = ExtractDAPInfoFromGSDML(gsdmlFilePath, dapNode.Id);
                            if (dapInfo.HasValue)
                            {
                                dnsCompatibleName = dapInfo.Value.dnsCompatibleName;
                                dapId = dapInfo.Value.dapId;
                                Debug.WriteLine($"从GSDML提取的DAP信息 - DNS名称: {dnsCompatibleName}, DAP ID: {dapId}");
                            }
                            else
                            {
                                Debug.WriteLine("无法从GSDML文件提取DAP信息，使用默认值");
                                dnsCompatibleName = deviceName; // 使用DAP节点名作为备用
                            }
                        }
                        else
                        {
                            Debug.WriteLine("GSDML文件复制失败");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"GSDML文件不存在: {gsdmlFilePath}");
                        
                        // 尝试在应用程序数据目录中查找GSDML文件
                        string appDataPath = Path.Combine(
                            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                            "PNConfigTool", "GSDML");
                        
                        string fileName = Path.GetFileName(gsdmlFilePath);
                        string appDataFilePath = Path.Combine(appDataPath, fileName);
                        
                        Debug.WriteLine($"尝试在应用程序数据目录中查找: {appDataFilePath}");
                        
                        if (File.Exists(appDataFilePath))
                        {
                            Debug.WriteLine($"在应用程序数据目录中找到GSDML文件: {appDataFilePath}");
                            gsdmlFilePath = appDataFilePath;
                            
                            // 复制GSDML文件到项目GSDMLs文件夹
                            string projectGsdmlPath = CopyGSDMLToProjectFolder(gsdmlFilePath);
                            if (!string.IsNullOrEmpty(projectGsdmlPath))
                            {
                                gsdmlFilePath = projectGsdmlPath;
                                gsdmlFileName = Path.GetFileName(projectGsdmlPath);
                                Debug.WriteLine($"已复制GSDML文件到: {projectGsdmlPath}");

                                // 从GSDML文件中提取DAP信息，传入选择的DAP ID
                                var dapInfo = ExtractDAPInfoFromGSDML(gsdmlFilePath, dapNode.Id);
                                if (dapInfo.HasValue)
                                {
                                    dnsCompatibleName = dapInfo.Value.dnsCompatibleName;
                                    dapId = dapInfo.Value.dapId;
                                    Debug.WriteLine($"从GSDML提取的DAP信息 - DNS名称: {dnsCompatibleName}, DAP ID: {dapId}");
                                }
                                else
                                {
                                    Debug.WriteLine("无法从GSDML文件提取DAP信息，使用默认值");
                                    dnsCompatibleName = deviceName; // 使用DAP节点名作为备用
                                }
                            }
                            else
                            {
                                Debug.WriteLine("GSDML文件复制失败");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"在应用程序数据目录中也未找到GSDML文件");
                            MessageBox.Show($"GSDML文件不存在: {gsdmlFilePath}\n在应用程序数据目录中也未找到: {appDataFilePath}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("父设备没有关联的GSDML文件路径");
                }
            }
            else
            {
                Debug.WriteLine("未找到DAP节点的父设备");
            }
             
            // 使用提取的DNS兼容名称生成设备ID，如果没有提取到则使用DAP节点名
            string deviceIdBase = !string.IsNullOrEmpty(dnsCompatibleName) ? dnsCompatibleName : deviceName;
            string generatedDeviceID = GenerateDeviceID(deviceIdBase);

            // 设备名称使用生成的设备ID（遵循标准格式：DeviceName = DeviceID）
            string deviceNameFormatted = generatedDeviceID;

            // 确保设备名称唯一（如果已存在相同名称，GenerateDeviceID已经处理了序列号）
            int suffix = 1;
            string originalDeviceName = deviceNameFormatted;
            while (_projectManager.CurrentProject?.ConfigurationSettings.DecentralDevices.Any(d => d.DeviceRefID == deviceNameFormatted) == true)
            {
                suffix++;
                deviceNameFormatted = $"{originalDeviceName}_{suffix}";
            }

            // 获取项目目录
            string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

            // 使用DeviceConfigFactory创建项目设备配置
            var projectDeviceConfig = DeviceConfigFactory.CreateDecentralDeviceConfig(
                deviceRefID: generatedDeviceID,
                deviceName: deviceNameFormatted.ToLower().Replace(" ", ""),
                ipAddress: nextIP,
                subnetMask: "*************",
                deviceNumber: newDeviceNumber,
                pnDeviceName: deviceNameFormatted.ToLower().Replace(" ", ""),
                gsdmlFilePath: gsdmlFilePath,
                deviceType: deviceName,  // 设置DAP节点名称作为设备类型
                projectDirectory: projectDirectory
            );

            // 添加到项目配置中
            if (_projectManager.CurrentProject != null)
            {
                // 使用DeviceConfigFactory创建ListOfNodes设备节点
                var deviceNode = DeviceConfigFactory.CreateDecentralDeviceNode(
                    deviceID: generatedDeviceID,
                    deviceName: deviceNameFormatted.ToLower().Replace(" ", ""),
                    gsdmlPath: gsdmlFilePath,
                    gsdRefID: dapId,
                    deviceType: deviceName,  // 设置DAP节点名称作为设备类型
                    projectDirectory: projectDirectory
                );
                
                // 添加到ListOfNodesConfiguration
                _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices.Add(deviceNode);
                
                // 添加到ConfigurationSettings
                _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Add(projectDeviceConfig);
                _projectManager.IsProjectModified = true;
                 
                // 创建设备视图模型
                var deviceViewModel = new DeviceViewModel
                {
                    DeviceNumber = newDeviceNumber,
                    DeviceType = deviceName,  // 使用DAP节点名作为设备类型
                    DeviceName = deviceNameFormatted.ToLower().Replace(" ", ""),
                    IPSettings = "用户设置",
                    IPAddress = nextIP,
                    Comment = $"GSDML: {gsdmlFileName}"
                };
                 
                // 添加属性变更事件处理程序
                deviceViewModel.PropertyChanged += DeviceViewModel_PropertyChanged;

                // 添加到设备列表
                _devices.Add(deviceViewModel);

                // 标记页面已修改
                _isModified = true;

                // 保存项目确保更改立即生效，并触发ProjectChanged事件
                _projectManager.SaveProject();

                // 手动触发导航菜单更新
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.RebuildDeviceNavigationMenu();

                Debug.WriteLine($"已添加设备: {deviceNameFormatted}, IP: {nextIP}, GSDML: {gsdmlFilePath}");
            }
        }
        
        /// <summary>
        /// 使用传统方式添加设备（动态从GSDML文件提取设备信息）
        /// </summary>
        private void AddLegacyDevice(string deviceName)
        {
            string deviceType = deviceName; // 使用传入的设备名称作为设备类型
            string ipSetting = "用户设置";
            int newDeviceNumber = _devices.Count + 1;
            string gsdmlFilePath = "";
            string dnsCompatibleName = "";
            string dapId = "";

            Debug.WriteLine($"开始添加传统设备: {deviceName}");

            // 尝试查找对应的GSDML文件
            string? foundGsdmlPath = FindGSDMLFileForLegacyDevice(deviceName);

            if (!string.IsNullOrEmpty(foundGsdmlPath))
            {
                Debug.WriteLine($"找到GSDML文件: {foundGsdmlPath}");
                gsdmlFilePath = foundGsdmlPath;

                // 复制GSDML文件到项目文件夹
                string projectGsdmlPath = CopyGSDMLToProjectFolder(gsdmlFilePath);
                if (!string.IsNullOrEmpty(projectGsdmlPath))
                {
                    gsdmlFilePath = projectGsdmlPath;
                    Debug.WriteLine($"已复制GSDML文件到项目: {projectGsdmlPath}");
                }

                // 从GSDML文件中提取DAP信息
                var dapInfo = ExtractDAPInfoFromGSDML(gsdmlFilePath, null);
                if (dapInfo.HasValue)
                {
                    dnsCompatibleName = dapInfo.Value.dnsCompatibleName;
                    dapId = dapInfo.Value.dapId;
                    Debug.WriteLine($"从GSDML提取的DAP信息 - DNS名称: {dnsCompatibleName}, DAP ID: {dapId}");
                }
                else
                {
                    Debug.WriteLine("无法从GSDML文件提取DAP信息，使用设备名称作为备用");
                    dnsCompatibleName = deviceName.Replace(" ", "-").Replace(".", "");
                    dapId = "DAP1";
                }
            }
            else
            {
                Debug.WriteLine($"未找到设备 '{deviceName}' 对应的GSDML文件，使用设备名称作为DNS兼容名称");
                // 如果没有找到GSDML文件，使用设备名称作为DNS兼容名称
                dnsCompatibleName = deviceName.Replace(" ", "-").Replace(".", "").Replace(",", "");
                dapId = "GSD_Ref_" + Guid.NewGuid().ToString().Substring(0, 8);
            }

            // 使用提取的DNS兼容名称生成设备ID
            string generatedDeviceID = GenerateDeviceID(dnsCompatibleName);

            // 设备名称使用生成的设备ID（遵循标准格式：DeviceName = DeviceID）
            string deviceNameFormatted = generatedDeviceID;

            // 确保设备名称唯一（如果已存在相同名称，GenerateDeviceID已经处理了序列号）
            int suffix = 1;
            string originalDeviceName = deviceNameFormatted;
            while (_projectManager.CurrentProject?.ConfigurationSettings.DecentralDevices.Any(d => d.DeviceRefID == deviceNameFormatted) == true)
            {
                suffix++;
                deviceNameFormatted = $"{originalDeviceName}_{suffix}";
            }

            // Get the next available IP address in the same subnet as the controller
            string nextIP = GetNextAvailableIPAddress();
            
            // 获取项目目录
            string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

            // 使用DeviceConfigFactory创建项目设备配置
            var projectDeviceConfig = DeviceConfigFactory.CreateDecentralDeviceConfig(
                deviceRefID: generatedDeviceID,
                deviceName: deviceNameFormatted,
                ipAddress: nextIP,
                subnetMask: "*************",
                deviceNumber: newDeviceNumber,
                pnDeviceName: deviceNameFormatted.ToLower().Replace(" ", ""),
                gsdmlFilePath: gsdmlFilePath,
                deviceType: deviceName,  // 设置传入的设备名称作为设备类型
                projectDirectory: projectDirectory
            );

            // 添加到项目配置中
            if (_projectManager.CurrentProject != null)
            {
                // 使用DeviceConfigFactory创建ListOfNodes设备节点
                var deviceNode = DeviceConfigFactory.CreateDecentralDeviceNode(
                    deviceID: generatedDeviceID,
                    deviceName: deviceNameFormatted,
                    gsdmlPath: gsdmlFilePath,
                    gsdRefID: dapId,
                    deviceType: deviceName,  // 设置传入的设备名称作为设备类型
                    projectDirectory: projectDirectory
                );
                
                // 添加到ListOfNodesConfiguration
                _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices.Add(deviceNode);
                
                // 添加到ConfigurationSettings
                _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Add(projectDeviceConfig);
                _projectManager.IsProjectModified = true;
                
                // 创建设备视图模型
                var deviceViewModel = new DeviceViewModel
                {
                    DeviceNumber = newDeviceNumber,
                    DeviceType = deviceName,  // 使用传入的设备名称作为设备类型
                    DeviceName = deviceNameFormatted,
                    IPSettings = ipSetting,
                    IPAddress = nextIP, // Use the automatically assigned IP
                    Comment = ""
                };
                
                // 添加属性变更事件处理程序
                deviceViewModel.PropertyChanged += DeviceViewModel_PropertyChanged;
                
                // 添加到设备列表
                _devices.Add(deviceViewModel);

                // 标记页面已修改
                _isModified = true;

                // 保存项目确保更改立即生效
                _projectManager.SaveProject();

                // 手动触发导航菜单更新
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.RebuildDeviceNavigationMenu();

                Debug.WriteLine($"已添加传统设备: {deviceNameFormatted}, IP: {nextIP}");
            }
        }

        /// <summary>
        /// 删除设备按钮点击事件
        /// </summary>
        private void DeleteDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            if (DevicesDataGrid.SelectedItem is DeviceViewModel selectedDevice)
            {
                // 显示确认对话框
                MessageBoxResult result = MessageBox.Show(
                    "删除此设备也将删除已有的模块配置。要继续吗?",
                    "提示",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                // 如果用户确认删除
                if (result == MessageBoxResult.Yes)
                {
                    int index = _devices.IndexOf(selectedDevice);
                    if (index >= 0)
                    {
                        string deviceName = selectedDevice.DeviceName;
                        Debug.WriteLine($"删除设备: {deviceName}, 索引: {index}");
                        
                        // 移除属性变更事件处理程序
                        selectedDevice.PropertyChanged -= DeviceViewModel_PropertyChanged;
                        
                        _devices.RemoveAt(index);
                        
                        // 从项目配置中删除
                        if (_projectManager.CurrentProject != null && index < _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count)
                        {
                            // 获取要删除的设备的引用ID
                            string deviceRefId = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices[index].DeviceRefID;
                            
                            // 从ConfigurationSettings中删除
                            _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.RemoveAt(index);
                            
                            // 从ListOfNodesConfiguration中删除
                            var nodeToRemove = _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices
                                .FirstOrDefault(d => d.DeviceID == deviceRefId);
                            if (nodeToRemove != null)
                            {
                                _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices.Remove(nodeToRemove);
                                Debug.WriteLine($"已从ListOfNodesConfiguration中删除设备节点: {nodeToRemove.DeviceName}");
                            }
                            
                            _projectManager.IsProjectModified = true;

                            // 标记页面已修改
                            _isModified = true;

                            // 保存项目以触发ProjectChanged事件
                            _projectManager.SaveProject();

                            // 手动触发导航菜单更新
                            var mainWindow = Application.Current.MainWindow as MainWindow;
                            mainWindow?.RebuildDeviceNavigationMenu();

                            Debug.WriteLine($"已从项目中删除设备: {deviceName}");
                        }
                        
                        // 重新编号
                        for (int i = 0; i < _devices.Count; i++)
                        {
                            _devices[i].DeviceNumber = i + 1;
                        }
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的设备", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 保存控制器配置
        /// </summary>
        private void SaveControllerConfig()
        {
            if (_projectManager.CurrentProject != null)
            {
                try
                {
                    Debug.WriteLine("开始保存控制器配置");
                    Debug.WriteLine($"UI中的设备数量: {_devices.Count}");
                    Debug.WriteLine($"项目中的设备数量: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");

                    // 只更新现有设备的基本信息（如IP地址等），不重新构建设备列表
                    // 因为设备在添加时已经被正确保存到项目配置中
                    foreach (var deviceViewModel in _devices)
                    {
                        Debug.WriteLine($"更新设备信息: {deviceViewModel.DeviceName}");

                        // 查找对应的设备配置
                        var deviceConfig = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                            .FirstOrDefault(d => d.DeviceRefID == deviceViewModel.DeviceName);

                        if (deviceConfig != null)
                        {
                            // 更新可能在UI中被修改的信息
                            deviceConfig.DeviceType = deviceViewModel.DeviceType; // 更新设备类型
                            deviceConfig.DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = deviceViewModel.IPAddress;
                            Debug.WriteLine($"已更新设备配置: {deviceViewModel.DeviceName}, IP: {deviceViewModel.IPAddress}, 类型: {deviceViewModel.DeviceType}");
                        }
                        else
                        {
                            Debug.WriteLine($"警告：在项目配置中未找到设备: {deviceViewModel.DeviceName}");
                        }

                        // 查找对应的ListOfNodes设备节点并更新信息
                        var deviceNode = _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices
                            .FirstOrDefault(d => d.DeviceName == deviceViewModel.DeviceName);
                        if (deviceNode != null)
                        {
                            deviceNode.DeviceName = deviceViewModel.DeviceName;
                            deviceNode.DeviceType = deviceViewModel.DeviceType; // 更新设备类型
                            Debug.WriteLine($"已更新设备节点: {deviceViewModel.DeviceName}, 类型: {deviceViewModel.DeviceType}");
                        }
                        else
                        {
                            Debug.WriteLine($"警告：在ListOfNodes中未找到设备节点: {deviceViewModel.DeviceName}");
                        }
                    }

                    // 验证设备数据
                    Debug.WriteLine("验证保存后的设备数据:");
                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                    {
                        Debug.WriteLine($"设备: {device.DeviceRefID}, 类型: {device.DeviceType}, 模块数量: {device.Modules?.Count ?? 0}");
                    }

                    // 保存项目
                    bool saveResult = _projectManager.SaveProject();
                    Debug.WriteLine($"项目保存结果: {saveResult}");

                    Debug.WriteLine("控制器配置已保存");
                    _isModified = false;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"保存控制器配置时出错: {ex.Message}");
                    Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                    MessageBox.Show($"保存控制器配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 上一步按钮点击事件
        /// </summary>
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            // 导航回网络配置页面
            var navigationService = ServiceLocator.GetService<INavigationService>();
            if (navigationService != null)
            {
                navigationService.Navigate("NetworkConfigPage");
            }
        }

        /// <summary>
        /// 下一步按钮点击事件
        /// </summary>
        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 保存控制器配置
                SaveControllerConfig();

                // 如果有设备，导航到第一个设备的配置页面
                if (_projectManager.CurrentProject != null && 
                    _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices != null && 
                    _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count > 0)
                {
                    var firstDevice = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices[0];
                    var navigationService = ServiceLocator.GetService<INavigationService>();
                    if (navigationService != null)
                    {
                        // 确保设备配置页面已经注册
                        string devicePageKey = $"DeviceConfig_{firstDevice.DeviceRefID}";

                        // 检查页面是否已注册，如果未注册，则重新注册
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"注册设备页面: {devicePageKey} -> DeviceConfigPage");
                            navigationService.RegisterPage(devicePageKey, typeof(DeviceConfigPage));

                            // 使用设备名称作为导航参数
                            System.Diagnostics.Debug.WriteLine($"导航到设备页面: {devicePageKey}, 参数: {firstDevice.DeviceRefID}");
                            navigationService.Navigate(devicePageKey, firstDevice.DeviceRefID);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"导航到设备页面出错: {ex.Message}");
                            MessageBox.Show($"导航到设备配置页面失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("无法获取导航服务");
                        MessageBox.Show("无法获取导航服务", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("没有设备，请先添加设备");
                    MessageBox.Show("请先添加设备后再继续。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"下一步按钮点击事件处理出错: {ex.Message}");
                MessageBox.Show($"发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            // 保存控制器配置
            SaveControllerConfig();
            
            // TODO: 实现生成配置文件的逻辑
            MessageBox.Show("生成配置文件功能尚未实现。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查是否有未保存的修改
            if (_projectManager.IsProjectModified)
            {
                MessageBoxResult result = MessageBox.Show(
                    "有未保存的修改，是否保存后退出？",
                    "提示",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // 保存控制器配置
                    SaveControllerConfig();
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    // 取消退出
                    return;
                }
            }
            
            // 导航回上一页
            NavigationService.GoBack();
        }

        private string GetNextAvailableIPAddress()
        {
            // Get controller's IP configuration from project manager
            string? controllerIP = _projectManager.CurrentProject?.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress;
            if (string.IsNullOrEmpty(controllerIP))
            {
                return "0.0.0.0"; // Fallback to default if no controller IP is set
            }

            // Parse controller IP
            string[] ipParts = controllerIP.Split('.');
            if (ipParts.Length != 4)
            {
                return "0.0.0.0";
            }

            // Get the network part (first 3 octets) from controller IP
            string networkPart = $"{ipParts[0]}.{ipParts[1]}.{ipParts[2]}";

            // Get all used IP addresses in the current subnet
            var usedIPs = new HashSet<int>();
            // Add controller's last octet to used IPs
            if (int.TryParse(ipParts[3], out int controllerLastOctet))
            {
                usedIPs.Add(controllerLastOctet);
            }

            foreach (var device in _devices)
            {
                string[] deviceIPParts = device.IPAddress.Split('.');
                if (deviceIPParts.Length == 4)
                {
                    if ($"{deviceIPParts[0]}.{deviceIPParts[1]}.{deviceIPParts[2]}" == networkPart)
                    {
                        if (int.TryParse(deviceIPParts[3], out int lastOctet))
                        {
                            usedIPs.Add(lastOctet);
                        }
                    }
                }
            }

            // Find the next available IP in the range 2-254 (avoiding 0, 1, and 255)
            for (int i = 2; i < 255; i++)
            {
                if (!usedIPs.Contains(i))
                {
                    return $"{networkPart}.{i}";
                }
            }

            return "0.0.0.0"; // Fallback if no IPs are available
        }

        /// <summary>
        /// 根据设备名称查找对应的GSDML文件
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>找到的GSDML文件路径，如果未找到则返回null</returns>
        private string? FindGSDMLFileForLegacyDevice(string deviceName)
        {
            try
            {
                Debug.WriteLine($"开始查找设备 '{deviceName}' 对应的GSDML文件");

                // 定义可能的搜索路径
                var searchPaths = new List<string>();

                // 1. 项目GSDMLs文件夹
                if (_projectManager.CurrentProject != null && !string.IsNullOrEmpty(_projectManager.CurrentProjectFilePath))
                {
                    string projectDirectory = Path.GetDirectoryName(_projectManager.CurrentProjectFilePath) ?? "";
                    string projectGsdmlPath = Path.Combine(projectDirectory, "GSDMLs");
                    if (Directory.Exists(projectGsdmlPath))
                    {
                        searchPaths.Add(projectGsdmlPath);
                    }
                }

                // 2. 示例GSDML文件夹
                string exampleGsdmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GSDML_Example");
                if (Directory.Exists(exampleGsdmlPath))
                {
                    searchPaths.AddRange(Directory.GetDirectories(exampleGsdmlPath, "*", SearchOption.AllDirectories));
                    searchPaths.Add(exampleGsdmlPath);
                }

                // 3. 应用程序数据目录
                string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PNConfigTool", "GSDML");
                if (Directory.Exists(appDataPath))
                {
                    searchPaths.Add(appDataPath);
                }

                // 根据设备名称定义搜索模式
                var searchPatterns = new List<string>();
                if (deviceName.Contains("IM60") || deviceName.Contains("ET200SMART"))
                {
                    searchPatterns.AddRange(new[] { "*IM60*.xml", "*ET200SMART*.xml", "*ET200*.xml" });
                }
                else if (deviceName.Contains("IOD-Linux") || deviceName.Contains("Linux"))
                {
                    searchPatterns.AddRange(new[] { "*Linux*.xml", "*IOD*.xml", "*PNDriver*.xml" });
                }
                else if (deviceName.Contains("Standard") || deviceName.Contains("MRP"))
                {
                    searchPatterns.AddRange(new[] { "*Standard*.xml", "*MRP*.xml", "*ERTEC*.xml" });
                }
                else
                {
                    // 通用搜索模式
                    searchPatterns.Add("*.xml");
                }

                // 在所有路径中搜索匹配的GSDML文件
                foreach (string searchPath in searchPaths)
                {
                    if (!Directory.Exists(searchPath)) continue;

                    foreach (string pattern in searchPatterns)
                    {
                        var files = Directory.GetFiles(searchPath, pattern, SearchOption.TopDirectoryOnly);
                        foreach (string file in files)
                        {
                            Debug.WriteLine($"检查文件: {file}");

                            // 验证是否为有效的GSDML文件并包含相关设备信息
                            if (IsValidGSDMLFileForDevice(file, deviceName))
                            {
                                Debug.WriteLine($"找到匹配的GSDML文件: {file}");
                                return file;
                            }
                        }
                    }
                }

                Debug.WriteLine($"未找到设备 '{deviceName}' 对应的GSDML文件");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找GSDML文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证GSDML文件是否适用于指定设备
        /// </summary>
        /// <param name="gsdmlFilePath">GSDML文件路径</param>
        /// <param name="deviceName">设备名称</param>
        /// <returns>是否匹配</returns>
        private bool IsValidGSDMLFileForDevice(string gsdmlFilePath, string deviceName)
        {
            try
            {
                // 使用缓存的解释器实例来验证文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdmlFilePath, GSDI.ModelOptions.GSDCommon);
                if (interpreter == null)
                {
                    return false;
                }

                // 获取设备信息进行匹配
                var deviceAccessPoints = interpreter.GetDeviceAccessPoints();
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    return false;
                }

                // 检查是否包含相关的设备信息
                foreach (DeviceAccessPoint dap in deviceAccessPoints)
                {
                    if (dap?.DNSCompatibleName != null)
                    {
                        string dnsName = dap.DNSCompatibleName.ToLower();
                        string deviceNameLower = deviceName.ToLower();

                        // 根据设备名称进行匹配
                        if (deviceNameLower.Contains("im60") && dnsName.Contains("et200smart"))
                        {
                            return true;
                        }
                        if (deviceNameLower.Contains("linux") && dnsName.Contains("linux"))
                        {
                            return true;
                        }
                        if (deviceNameLower.Contains("standard") || deviceNameLower.Contains("mrp"))
                        {
                            // 对于标准MRP设备，可能需要更具体的匹配逻辑
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证GSDML文件时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从GSDML文件中提取DAP信息
        /// </summary>
        /// <param name="gsdmlFilePath">GSDML文件路径</param>
        /// <param name="selectedDapId">选择的DAP ID，如果为null则提取第一个DAP</param>
        /// <returns>包含DAP信息的元组，如果提取失败则返回null</returns>
        private (string dnsCompatibleName, string dapId)? ExtractDAPInfoFromGSDML(string gsdmlFilePath, string? selectedDapId = null)
        {
            try
            {
                Debug.WriteLine($"开始从GSDML文件提取DAP信息: {gsdmlFilePath}");

                if (string.IsNullOrEmpty(gsdmlFilePath) || !File.Exists(gsdmlFilePath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdmlFilePath}");
                    return null;
                }

                // 使用缓存的解释器实例
                var interpreter = GSDMLCacheService.GetCachedInterpreter(gsdmlFilePath, GSDI.ModelOptions.GSDCommon);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {gsdmlFilePath}");
                    return null;
                }

                // 获取设备访问点
                var deviceAccessPoints = interpreter.GetDeviceAccessPoints();
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine($"GSDML文件中未找到设备访问点: {gsdmlFilePath}");
                    return null;
                }

                DeviceAccessPoint? targetDAP = null;

                // 如果指定了DAP ID，则查找对应的DAP
                if (!string.IsNullOrEmpty(selectedDapId))
                {
                    Debug.WriteLine($"查找指定的DAP ID: {selectedDapId}");
                    foreach (DeviceAccessPoint dap in deviceAccessPoints)
                    {
                        if (dap != null && string.Equals(dap.GsdID, selectedDapId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = dap;
                            Debug.WriteLine($"找到匹配的DAP: {dap.GsdID}");
                            break;
                        }
                    }

                    if (targetDAP == null)
                    {
                        Debug.WriteLine($"未找到指定的DAP ID: {selectedDapId}，将使用第一个DAP");
                    }
                }

                // 如果没有找到指定的DAP或没有指定DAP ID，则使用第一个DAP
                if (targetDAP == null)
                {
                    targetDAP = deviceAccessPoints.GetValue(0) as DeviceAccessPoint;
                    if (targetDAP == null)
                    {
                        Debug.WriteLine($"无法获取设备访问点: {gsdmlFilePath}");
                        return null;
                    }
                    Debug.WriteLine($"使用第一个DAP: {targetDAP.GsdID}");
                }

                string dnsCompatibleName = targetDAP.DNSCompatibleName ?? "UnknownDevice";
                string dapId = targetDAP.GsdID ?? "DAP1";

                Debug.WriteLine($"成功提取DAP信息 - DNS名称: {dnsCompatibleName}, DAP ID: {dapId}");
                return (dnsCompatibleName, dapId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从GSDML文件提取DAP信息时出错: {ex.Message}");
                Debug.WriteLine($"异常详情: {ex}");
                return null;
            }
        }

        /// <summary>
        /// 生成设备ID，使用DNS兼容名称和序列号
        /// </summary>
        /// <param name="dnsCompatibleName">DNS兼容名称</param>
        /// <returns>生成的设备ID</returns>
        private string GenerateDeviceID(string dnsCompatibleName)
        {
            if (string.IsNullOrEmpty(dnsCompatibleName))
            {
                dnsCompatibleName = "Device";
            }

            // 计算当前使用相同DNS名称的设备数量
            int sequenceNumber = 1;
            string baseDeviceID = dnsCompatibleName;

            if (_projectManager.CurrentProject?.ConfigurationSettings.DecentralDevices != null)
            {
                // 查找所有使用相同DNS名称的设备
                var existingDevicesWithSameName = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices
                    .Where(d => d.DeviceRefID != null && d.DeviceRefID.StartsWith(baseDeviceID + "_"))
                    .ToList();

                // 找到最大的序列号
                int maxSequence = 0;
                foreach (var device in existingDevicesWithSameName)
                {
                    string deviceId = device.DeviceRefID ?? "";
                    string[] parts = deviceId.Split('_');
                    if (parts.Length >= 2 && int.TryParse(parts[parts.Length - 1], out int sequence))
                    {
                        maxSequence = Math.Max(maxSequence, sequence);
                    }
                }

                sequenceNumber = maxSequence + 1;
            }

            return $"{baseDeviceID}_{sequenceNumber}";
        }
    }

    /// <summary>
    /// 设备视图模型
    /// </summary>
    public class DeviceViewModel : INotifyPropertyChanged
    {
        private int _deviceNumber;
        private string _deviceType = string.Empty;
        private string _deviceName = string.Empty;
        private string _ipSettings = string.Empty;
        private string _ipAddress = "0.0.0.0";
        private string? _comment;

        public int DeviceNumber
        {
            get => _deviceNumber;
            set
            {
                if (_deviceNumber != value)
                {
                    _deviceNumber = value;
                    OnPropertyChanged(nameof(DeviceNumber));
                }
            }
        }

        public string DeviceType
        {
            get => _deviceType;
            set
            {
                if (_deviceType != value)
                {
                    _deviceType = value;
                    OnPropertyChanged(nameof(DeviceType));
                }
            }
        }

        public string DeviceName
        {
            get => _deviceName;
            set
            {
                if (_deviceName != value)
                {
                    _deviceName = value;
                    OnPropertyChanged(nameof(DeviceName));
                }
            }
        }

        public string IPSettings
        {
            get => _ipSettings;
            set
            {
                if (_ipSettings != value)
                {
                    _ipSettings = value;
                    OnPropertyChanged(nameof(IPSettings));
                }
            }
        }

        public string IPAddress
        {
            get => _ipAddress;
            set
            {
                if (_ipAddress != value)
                {
                    _ipAddress = value;
                    OnPropertyChanged(nameof(IPAddress));
                }
            }
        }

        public string? Comment
        {
            get => _comment;
            set
            {
                if (_comment != value)
                {
                    _comment = value;
                    OnPropertyChanged(nameof(Comment));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}