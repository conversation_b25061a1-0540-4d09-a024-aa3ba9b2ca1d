/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: FrameGeneration.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.PNFrameGeneration
{
    /// <summary>
    /// Frame generation for the PNPlanner algorithm
    /// </summary>
    internal class FrameGeneration
    {
        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #region MethodDataArguments

        //output
        private List<IPNFrameData> m_PNFrameDataList;

        #endregion

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public FrameGeneration(Interface controllerInterfaceSubmodule)
        {
            ControllerInterfaceSubmodule = controllerInterfaceSubmodule;
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        private Interface ControllerInterfaceSubmodule { get; }

        #region MethodDataArguments

        //output
        public Dictionary<string, ParamObjectRefreshPNPlannerInput> FrameLengthColl { get; private set; }

        public List<IPNFrameData> PNFrameDataList => m_PNFrameDataList; 

        #endregion

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        #region Generic Methods

        /// <summary>
        /// Preparation for PNPlanner is done here. Generated frames are returned to get their ids.
        /// </summary>
        /// <param name="isCompile"></param>
        internal void MethodPrepareForPNPlannerScf(bool isCompile)
        {
            //Call the generic method "RefreshPNPlannerInputData" at all IOConnectors in the IOSystem
            Dictionary<int, List<IPNFrameData>> pnFrameDataListOfController =
                RefreshPNPlannerInputDataOfInterfaces(ControllerInterfaceSubmodule, isCompile);

            if (pnFrameDataListOfController != null)
            {
                PNIOC ioController = ControllerInterfaceSubmodule.PNIOC;
                int coreId = ioController.GetHashCode();

                if (!pnFrameDataListOfController.TryGetValue(coreId, out m_PNFrameDataList))
                {
                    m_PNFrameDataList = new List<IPNFrameData>();
                }
            }
        }
        /// <summary>
        /// This method calls the generic method "RefreshPNPlannerInputData" at the given
        /// controllerInterfaceSubmodule and at each deviceInterfaceSubmodule
        /// which is connected to the controllerInterfaceSubmodule.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <param name="isCompile"></param>
        private Dictionary<int, List<IPNFrameData>> RefreshPNPlannerInputDataOfInterfaces(
            Interface controllerInterfaceSubmodule,
            bool isCompile)
        {
            Dictionary<int, List<IPNFrameData>> pnFrameDataListOfController = new Dictionary<int, List<IPNFrameData>>();

            MethodData md = new MethodData();
            md.Name = RefreshPNPlannerInputData.Name;
            md.Arguments.Add(RefreshPNPlannerInputData.PNFrameDataList, pnFrameDataListOfController);
            PNIOC ioController = controllerInterfaceSubmodule.PNIOC;
            md.Arguments[RefreshPNPlannerInputData.IOController] = ioController;

            md.Arguments.Add(PNConstants.IsCompile, isCompile);

            // Shared device - put the attribute to method data which tells the shared device capability
            bool ctrlSharedDeviceAssignmentSupp =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceAssignmentSupp,
                    new AttributeAccessCode(),
                    false);
            md.Arguments[RefreshPNPlannerInputData.CtrlSharedDeviceAssignmentSupp] = ctrlSharedDeviceAssignmentSupp;

            List<PNIOD> ioDevices = NavigationUtilities.GetIoDevicesOfIoController(ioController);

            ioController.BaseActions.CallMethod(md);

            if (ioDevices != null)
            {
                foreach (PNIOD ioDevice in ioDevices)
                {
                    ioDevice.BaseActions.CallMethod(md);
                }
            }

            FrameLengthColl =
                (Dictionary<string, ParamObjectRefreshPNPlannerInput>)md.Arguments[PrepareForPNPlanner.FrameLengthColl];

            return pnFrameDataListOfController;
        }

        #endregion

        #endregion
    }
}