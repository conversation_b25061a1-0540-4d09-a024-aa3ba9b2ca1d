/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Interface.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.PNInterface;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents an interface submodule.
    /// </summary>
    public class Interface : PclObject
    {
        /// <summary>
        /// The PNIOc object of the interface.
        /// </summary>
        /// <remarks>
        /// If the PNIOc object is set, then this interface belongs to a central device.
        /// Both PNIOc and PNIOd can not be set at the same time, unless the iDevice feature
        /// is used, and iDevice is not supported yet by PNDriver.
        /// </remarks>
        private PNIOC m_PNIOC;

        /// <summary>
        /// The PNIOd object of the interface.
        /// </summary>
        /// <remarks>
        /// If the PNIOc object is set, then this interface belongs to a decentral device.
        /// Both PNIOc and PNIOd can not be set at the same time, unless the iDevice feature
        /// is used, and iDevice is not supported yet by PNDriver.
        /// </remarks>
        private PNIOD m_PNIOD;

        /// <summary>
        /// The list that contains ports added to this interface.
        /// </summary>
        private List<Port> m_Ports;

        /// <summary>
        /// The constructor of Interface.
        /// </summary>
        /// <param name="interfaceId">ID of the interface.</param>
        public Interface(string interfaceId)
        {
            m_Ports = new List<Port>();
            Id = interfaceId;
        }

        /// <summary>
        /// The interface that keeps a reference to the BL class of this Interface object.
        /// </summary>
        public IInterfaceBusinessLogic InterfaceBL { get; set; }

        /// <summary>
        /// The Node of the object, used for connecting to a subnet.
        /// </summary>
        public Node Node { get; set; }

        /// <summary>
        /// The property used for getting and setting PNIOc.
        /// </summary>
        public PNIOC PNIOC
        {
            get { return m_PNIOC; }
            set
            {
                m_PNIOC = value;
                if (value == null)
                {
                    throw new ArgumentNullException(nameof(value));
                }

                value.ParentObject = this;
                
            }
        }

        /// <summary>
        /// The property used for getting and setting PNIOd.
        /// </summary>
        public PNIOD PNIOD
        {
            get { return m_PNIOD; }
            set
            {
                m_PNIOD = value;
                if (value == null)
                {
                    throw new ArgumentNullException(nameof(value));
                }

                value.ParentObject = this; 
            }
        }

        /// <summary>
        /// The sync domain that this Interface is in.
        /// </summary>
        public SyncDomain SyncDomain { get; set; }

        /// <summary>
        /// Adds a Port object to this Interface and registers its address.
        /// </summary>
        /// <param name="port">The Port to be added.</param>
        /// <exception cref="ArgumentNullException">if port is null.</exception>
        /// <exception cref="InvalidOperationException">if the Interface that the Port is being added is not connected to a device.</exception>
        public void AddPort(Port port)
        {
            if (port == null)
            {
                throw new ArgumentNullException(nameof(port));
            }

            PclObject connectedDevice = GetDevice();

            if (connectedDevice == null)
            {
                throw new InvalidOperationException("The interface must be connected to a device before adding ports.");
            }

            m_Ports.Add(port);
            port.ParentObject = this;
            port.Id = HWCNBL.Utilities.AttributeUtilities.GetName(this) + "_Port" + port.PortNumber;
            RegisterWithAddressManager(port);
        }

        /// <summary>
        /// Gets the objects connected to this Interface with Element relation.
        /// These are the Port objects of this Interface.
        /// </summary>
        /// <remarks>
        /// This method may work the same as calling GetPorts() method, but is used for traversing
        /// the project tree while generating frames and is called from a PclObject instance; therefore it is necessary.
        /// </remarks>
        /// <returns>A list containing the Port objects of this Interface.</returns>
        public override IList<PclObject> GetElements()
        {
            List<PclObject> elementList = new List<PclObject>();

            elementList.AddRange(m_Ports);

            return elementList;
        }

        /// <summary>
        /// Gets the list of Interface objects.
        /// </summary>
        /// <returns>The list of interfaces.</returns>
        internal override Interface GetInterface()
        {
            return this;
        }

        public void SetNode()
        {
            Node = new Node(this);
        }

        /// <summary>
        /// Gets the PNIOc or PNIOd object of this Interface.
        /// </summary>
        /// <returns>
        /// PNIOc or PNIOd object of this Interface, whichever is not null.
        /// Returns null if both of them are null.
        /// </returns>
        public PclObject GetIOConnector()
        {
            if (PNIOC != null)
            {
                return PNIOC;
            }

            if (PNIOD != null)
            {
                return PNIOD;
            }

            return null;
        }

        /// <summary>
        /// Returns a Port of this interface by its port number.
        /// </summary>
        /// <param name="portNumber">The port number.</param>
        /// <exception cref="PNFunctionsException">if a Port with the given port number does not exist.</exception>
        /// <returns>Port object with the given port number.</returns>
        public Port GetPortByPortNumber(uint portNumber)
        {
            Port port = m_Ports.SingleOrDefault(e => e.PortNumber == portNumber);

            if (port == null)
            {
                throw new PNFunctionsException("Port with given port number does not exist.");
            }

            return port;
        }

        /// <summary>
        /// Gets the list of Port objects of this Interface sorted by their position numbers.
        /// </summary>
        /// <returns>A list containing the ports connected to this Interface sorted by their position numbers.</returns>
        public IList<Port> GetPortModulesSorted()
        {
            return m_Ports.OrderBy(HWCNBL.Utilities.AttributeUtilities.GetPositionNumber).ToList();
        }

        /// <summary>
        /// Returns the list of Port objects of this Interface.
        /// </summary>
        /// <returns>A list containing the ports connected to this Interface.</returns>
        public IList<Port> GetPorts()
        {
            return m_Ports;
        }

        internal List<MrpDomainInstance> MrpDomainInstances = new List<MrpDomainInstance>();

        internal List<MrpDomainInstance> GetSortedMrpDomainInstances() 
        {
            return MrpDomainInstances.OrderBy(m => m.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnInstanceNumber, new AttributeAccessCode(), Int32.MinValue)).ToList();
        }
    }
}