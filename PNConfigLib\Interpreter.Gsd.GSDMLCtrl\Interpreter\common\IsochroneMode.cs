/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: IsochroneMode.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The IsochroneMode object contains information about ...
    /// </summary>
    public class IsochroneMode :
        GsdObject,
        GSDI.IIsochroneMode
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the IOConfigData if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public IsochroneMode()
        {
            m_TDcBase = 0;
            m_TDcMin = 0;
            m_TDcMax = 0;
            m_TIOBase = 0;
            m_TIOInputMin = 0;
            m_TIOOutputMin = 0;
            m_IsIsochroneModeRequired = false;

        }

        #endregion

        //########################################################################################
        #region Fields

        // V2.0
        private uint m_TDcBase;
        private uint m_TDcMin;
        private uint m_TDcMax;
        private uint m_TIOBase;
        private uint m_TIOInputMin;
        private uint m_TIOOutputMin;
        private bool m_IsIsochroneModeRequired;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_DC_Base => this.m_TDcBase;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_DC_Min => this.m_TDcMin;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_DC_Max => this.m_TDcMax;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_IO_Base => this.m_TIOBase;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_IO_InputMin => this.m_TIOInputMin;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 T_IO_OutputMin => this.m_TIOOutputMin;

        /// <summary>
        /// ...
        /// </summary>
        public virtual bool IsIsochroneModeRequired => this.m_IsIsochroneModeRequired;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldTDcBase;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TDcBase = (uint)hash[member];

                member = Models.s_FieldTDcMin;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TDcMin = (uint)hash[member];

                member = Models.s_FieldTDcMax;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TDcMax = (uint)hash[member];

                member = Models.s_FieldTIOBase;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TIOBase = (uint)hash[member];

                member = Models.s_FieldTIOInputMin;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TIOInputMin = (uint)hash[member];

                member = Models.s_FieldTIOOutputMin;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TIOOutputMin = (uint)hash[member];

                member = Models.s_FieldIsIsochroneModeRequired;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    this.m_IsIsochroneModeRequired = (bool)hash[member];

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectIsochroneMode);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldTDcBase, this.m_TDcBase);
            Export.WriteUint32Property(ref writer, Models.s_FieldTDcMin, this.m_TDcMin);
            Export.WriteUint32Property(ref writer, Models.s_FieldTDcMax, this.m_TDcMax);
            Export.WriteUint32Property(ref writer, Models.s_FieldTIOBase, this.m_TIOBase);
            Export.WriteUint32Property(ref writer, Models.s_FieldTIOInputMin, this.m_TIOInputMin);
            Export.WriteUint32Property(ref writer, Models.s_FieldTIOOutputMin, this.m_TIOOutputMin);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsIsochroneModeRequired, this.m_IsIsochroneModeRequired);

            return true;
        }

        #endregion

    }
}


