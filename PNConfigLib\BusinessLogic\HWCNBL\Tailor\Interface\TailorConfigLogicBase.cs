/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: TailorConfigLogicBase.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Tailor.Config;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.Interface
{
    internal abstract class TailorConfigLogicBase : TailorLogicBase
    {
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Constants and enumerations
        // Contains all constants and enumerations
        private const int s_NumberOfDeviceTailorProperties = 7;
        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        public DataModel.PCLObjects.Interface InterfaceSubmodule
        {
            get;
        }

        protected BlockDataWrapper<DeviceTailorPropertiesEnum, bool> DeviceTailorProperties
        {
            get;
        }


        #endregion

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        protected TailorConfigLogicBase(DataModel.PCLObjects.Interface interfaceSubmodule)
        {
            InterfaceSubmodule = interfaceSubmodule;
            DeviceTailorProperties = new BlockDataWrapper<DeviceTailorPropertiesEnum, bool>(s_NumberOfDeviceTailorProperties);
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class
        internal ParameterDatasetStruct GetPDMasterTailorDataDatasetStruct()
        {
            ParameterDatasetStruct pdMasterTailorDataRecord = null;
            PdMasterTailorDataStruct pdMasterTailorDataStruct = GetPDMasterTailorData();
            if (pdMasterTailorDataStruct != null)
            {
                pdMasterTailorDataRecord = new ParameterDatasetStruct();
                pdMasterTailorDataRecord.ParaDSNumber = pdMasterTailorDataStruct.IndexLow;
                pdMasterTailorDataRecord.ParaDSIdentifier = pdMasterTailorDataStruct.IndexHigh;
                pdMasterTailorDataRecord.AddParaBlock(pdMasterTailorDataStruct.ToByteArray);
            }
            return pdMasterTailorDataRecord;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Overrides and overridables
        // Contains all public and protected overrides as well as overridables of the class

        internal virtual PdMasterTailorDataStruct GetPDMasterTailorData()
        {
            PdMasterTailorDataStruct pdMasterTailorDataStruct = null;
            bool isPNIoTailoringExtDefined = IoControllerInterface.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIoTailoringExt, new AttributeAccessCode(), false);
            if (AddressTailoringEnabled || MachineTailorable)
            {
                var portTailorInfos = GetOfPortTailorInfos(isPNIoTailoringExtDefined);

                pdMasterTailorDataStruct = new PdMasterTailorDataStruct();

                pdMasterTailorDataStruct.SetBlockElement(PdMasterTailorDataStructEnum.DeviceGroup, 0);

                pdMasterTailorDataStruct.SetBlockElement(PdMasterTailorDataStructEnum.DeviceTailorProperties,
                    GetDeviceTailorProperties());

                //In case of toolchager we have a PDMasterTailorData but  no PortMasterTailorData
                pdMasterTailorDataStruct.SetBlockElement(PdMasterTailorDataStructEnum.NumberOfPortTailorInfos,
                    (portTailorInfos == null) ? (ushort)0 : (ushort)portTailorInfos.Count);

                if (portTailorInfos != null)
                {
                    foreach (var item in portTailorInfos)
                    {
                        pdMasterTailorDataStruct.AddPortMasterTailorData(item);
                    }
                }
            }
            return pdMasterTailorDataStruct;
        }

        public abstract bool HasPDIRData();

        public virtual void InitTailorProperties()
        {
            bool addressTailor = AddressTailoringEnabled;

            //Do not use NameOfStation, PDInterfaceMrpDataAdjust, PDInterfaceMrpDataCheck, PDSyncData tailoring
            //if the iosystem is not set to multiple deployable
            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.NameOfStation, addressTailor);
            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.PDInterfaceMrpDataAdjust,
                                                   ConfigUtility.HasPDInterfaceMrpDataAdjust(InterfaceSubmodule) &&
                                                   addressTailor);
            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.PDInterfaceMrpDataCheck,
                                                   ConfigUtility.HasPDInterfaceMrpDataCheck(InterfaceSubmodule) &&
                                                   addressTailor);

            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.PDSyncData, GetPDSyncDataValue());
        }

        public abstract PNInterfaceType PNInterfaceType
        {
            get;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Protected methods
        // Contains all protected (non overridables) methods of the class

        protected IList<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> GetOfPortTailorInfos(bool isPNIoTailoringExtDefined)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(InterfaceSubmodule);

            List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> retValue = new List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>>();
            foreach (DataModel.PCLObjects.Port port in NavigationUtilities.SortPortObjects(ports))
            {
                bool dockingPort = false;
                PortTailorConfigLogic portLogic = new PortTailorConfigLogic(port, this);

                List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> portTailorInfos = portLogic.ToBlockDataWrapper(ref dockingPort, isPNIoTailoringExtDefined);

                // PortMasterTailorData shouldn't be generated if any of the following are true:
                // Port has 'Alternative Partners'
                if ((dockingPort && !isPNIoTailoringExtDefined) || portTailorInfos == null)
                {
                    continue;
                }
                retValue.AddRange(portTailorInfos);
            }
            return retValue;
        }

        protected ushort GetDeviceTailorProperties()
        {
            int retValue = 0;

            InitTailorProperties();

            for (int i = 0; i < s_NumberOfDeviceTailorProperties; i++)
            {
                ConfigUtility.SetBit(ref retValue, s_NumberOfDeviceTailorProperties, i,
                    DeviceTailorProperties.GetBlockElement((DeviceTailorPropertiesEnum)i));
            }

            return (ushort)retValue;
        }

        /// <summary>
        /// </summary>
        /// <returns></returns>
        protected bool HasPDSyncData()
        {
            return ConfigUtility.IsRTSync(InterfaceSubmodule, PNInterfaceType) && InterfaceSubmodule.SyncDomain != null;
        }

        protected bool CalculateTailorPDIRData()
        {
            if (HasPDIRData() && HasSyncDomainOptionalDevice())
            {
                return true;
            }
            return false;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Private implementation
        // Contains the private implementation of the class
        private bool GetPDSyncDataValue()
        {
            if (!HasPDSyncData())
            {
                return false;
            }
            if (AddressTailoringEnabled)
            {
                return true;
            }
            bool ioControllerHasPDSyncData = ConfigUtility.IsRTSync(IoControllerInterface, PNInterfaceType.IOController);
            if (MachineTailorable && ioControllerHasPDSyncData)
            {
                return AreAllRTC3DevicesOptional();
            }
            return false;
        }

        private bool AreAllRTC3DevicesOptional()
        {
            SyncDomain syncDomain = InterfaceSubmodule.SyncDomain;
            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBl == null)
            {
                return false;
            }

            foreach (DataModel.PCLObjects.Interface interfaceSubmodule in syncDomainBl.SynchronizedParticipants)
            {
                if (Utilities.MachineTailor.MachineTailorUtility.IsOptionalDeviceEnabled(interfaceSubmodule))
                {
                    continue;
                }

                //Check if it's the controller. 
                if (interfaceSubmodule == IoControllerInterface)
                {
                    continue;
                }
                return false;
            }
            return true;
        }

        protected bool HasSyncDomainOptionalDevice()
        {
            SyncDomain syncDomain = InterfaceSubmodule.SyncDomain;
            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBl == null)
            {
                return false;
            }
            foreach (DataModel.PCLObjects.Interface interfaceSubmodule in syncDomainBl.SynchronizedParticipants)
            {
                if (Utilities.MachineTailor.MachineTailorUtility.IsOptionalDeviceEnabled(interfaceSubmodule) && !interfaceSubmodule.Equals(InterfaceSubmodule))
                {
                    return true;
                }
            }
            return false;
        }


        #endregion
    }
}