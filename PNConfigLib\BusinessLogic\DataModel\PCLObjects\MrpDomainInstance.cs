/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MrpDomainInstance.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

namespace PNConfigLib.DataModel.PCLObjects
{
    internal class MrpDomainInstance : PclObject
    {
        internal string MrpDomainId { get; }

        internal List<Port> RingPorts { get; }

        internal MrpDomainInstance(
            string mrpDomainId,
            string mrpName,
            PNMrpRole PNMrpRole,
            List<Port> ringPorts,
            int instanceNumber = 1)
        {
            MrpDomainId = mrpDomainId;
            RingPorts = ringPorts;

            AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnInstanceMrpRole
                , Convert.ToInt32(PNMrpRole, CultureInfo.InvariantCulture));
            AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name
                ,mrpName);
            AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnInstanceNumber
                , instanceNumber);
        }
    }
}
