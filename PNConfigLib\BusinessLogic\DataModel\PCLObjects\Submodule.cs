/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Submodule.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a submodule.
    /// </summary>
    internal class Submodule : PclObject
    {
        /// <summary>
        /// The default constructor for Submodule
        /// </summary>
        /// <param name="subslotNumber">The number of the subslot that this submodule is plugged to.</param>
        public Submodule(PclCatalogObject catalog, int subslotNumber = -1)
        {
            this.PCLCatalogObject = catalog;
            Id = PCLCatalogObject.AttributeAccess.GetAnyAttribute<string>(
                InternalAttributeNames.GsdId,
                new AttributeAccessCode(),
                string.Empty);

            if (subslotNumber != -1)
            {
                AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnSubslotNumber, subslotNumber);
            }
        }

        /// <summary>
        /// Whether the Submodule object is virtual.
        /// </summary>
        public bool IsVirtual { get; set; }

        /// <summary>
        /// Gets the list of Interface objects connected to this Submodule.
        /// </summary>
        /// <returns>The list of interfaces connected to this Submodule.</returns>
        internal override Interface GetInterface()
        {
            return GetDevice().GetInterface();
        }
    }
}