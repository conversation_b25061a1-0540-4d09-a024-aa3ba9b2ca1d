/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_025.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Diagnostics;
using System.Globalization;
using System.Xml;
using System.Xml.XPath;
using C = PNConfigLib.Gsd.Interpreter.Common;
using S = PNConfigLib.Gsd.Interpreter.Structure;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal class BuilderV02025 :
        BuilderV0202
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02025()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version225);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override bool InitExpressions()
        {
            bool succeeded = true;

            try
            {
                succeeded = base.InitExpressions();
                if (!succeeded)
                {
                    return false;
                }

                // Create document navigator.
                XPathNavigator nav = Gsd.CreateNavigator();

                if (nav == null)
                {
                    return false;
                }

                // Create the NamespaceManager and add all XML Namespaces to it.
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                // pluggable port submodules
                XPathExpression expr = nav.Compile(XPathes.AllPluggablePortsubmoduleItems);
                expr.SetContext(nsmgr);
                Expressions.Add(Elements.s_PortSubmoduleItem, expr);
            }
            catch (XPathException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {

                switch (name)
                {
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectSubmodule:
                        {
                            // NOTE: Navigator must point to VirtualSubmodule.
                            PrepareVirtualSubmodule(nav, ref hash);
                            obj = new C.VirtualSubmodule();

                            break;
                        }
                    case Models.s_ObjectAddValueDataItem:
                        {
                            // NOTE: Navigator must point to ProcessAlarmReasonAddValue.
                            PrepareAddValueDataItem(nav, ref hash);
                            obj = new C.AddValueDataItem();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }


        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIsParameterizationDisallowed, null);
            hash.Add(Models.s_FieldGsdId, null);
            hash.Add(Models.s_FieldWriteableImRecords, null);
            hash.Add(Models.s_FieldIsMulticastBoundarySupported, null);
            hash.Add(Models.s_FieldIsDelayMeasurementSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // Attribute ID. Required.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldGsdId] = attr;
            }

            // Get ParameterizationDisallowed attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ParameterizationDisallowed, String.Empty);
            hash[Models.s_FieldIsParameterizationDisallowed] = Help.GetBool(
                attr,
                Attributes.s_DefaultParameterizationDisallowed);

            // Get MulticastBoundarySupported. Optional
            attr = nav.GetAttribute(Attributes.s_MulticastBoundarySupported, String.Empty);
            hash[Models.s_FieldIsMulticastBoundarySupported] = Help.GetBool(
                attr,
                Attributes.s_DefaultMulticastBoundarySupported);

            // Get DelayMeasurementSupported. Optional
            attr = nav.GetAttribute(Attributes.s_DelayMeasurementSupported, String.Empty);
            hash[Models.s_FieldIsDelayMeasurementSupported] = Help.GetBool(
                attr,
                Attributes.s_DefaultDelayMeasurementSupported);

            // Get Writeable_IM_Records. Optional.
            attr = nav.GetAttribute(Attributes.s_WriteableImRecords, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldWriteableImRecords] = ValueListHelper.SeparateUnsignedValueList(attr);
            }

            // Get SynchronisationMode info. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(
                Elements.s_SynchronisationMode,
                Namespaces.s_GsdmlDeviceProfile);

            if (!nodes.MoveNext())
            {
                return;
            }

            // Get PeerToPeerJitter. Optional
            if (nodes.Current != null)
            {
                attr = nodes.Current.GetAttribute(Attributes.s_PeerToPeerJitter, String.Empty);
            }

            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPeerToPeerJitter] = UInt32.Parse(attr, CultureInfo.InvariantCulture);
            }

        }

        protected override void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAdditionalMrpProtocolsSupported, null);

            // Call base class method first.
            base.PrepareMediaRedundancy(nav, ref hash);

            // Attribute IsRT_MediaRedundancySupported is removed
            hash[Models.s_FieldIsRTMediaRedundancySupported] = null;

            // Get AdditionalProtocolsSupported. Optional
            string attr = nav.GetAttribute(Attributes.s_AdditionalProtocolsSupported, String.Empty);

            // Only fill AdditionalMrpProtocolsSupported field, if it is present in GSD file. A missing
            // attribute has a different meaning than an attribute with value 'false'
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldAdditionalMrpProtocolsSupported] = Help.GetBool(attr, Attributes.s_DefaultAdditionalProtocolsSupported);
            }
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIsParameterizationDisallowed, null);
            hash.Add(Models.s_FieldWriteableImRecords, null);
            hash.Add(Models.s_FieldIsCheckMauTypeSupported, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            // Attribute ID. Required.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldGsdId] = attr;
            }

            // Get ParameterizationDisallowed attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ParameterizationDisallowed, String.Empty);
            hash[Models.s_FieldIsParameterizationDisallowed] = Help.GetBool(attr, Attributes.s_DefaultParameterizationDisallowed);

            // Get CheckMAUTypeSupported. Optional
            attr = nav.GetAttribute(Attributes.s_CheckMauTypeSupported, String.Empty);
            if (!string.IsNullOrEmpty(attr))
                hash[Models.s_FieldIsCheckMauTypeSupported] = Help.GetBool(attr, Attributes.s_DefaultCheckMauTypeSupported);

            // Get Writeable_IM_Records. Optional.
            attr = nav.GetAttribute(Attributes.s_WriteableImRecords, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldWriteableImRecords] = ValueListHelper.SeparateUnsignedValueList(attr);
            }
        }

        protected override void PrepareTimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldPreferredSendClock, null);

            // Call base class method first.
            base.PrepareTimingProperties(nav, ref hash);

            if (nav != null)
            {
                // Get PreferredSendclock attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_PreferredSendClock, String.Empty);
                if (String.IsNullOrEmpty(attr))
                {
                    // TODO
                }
                else
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldPreferredSendClock] = value;
                }
            }
        }

        protected override void PrepareRTClass3TimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldPreferredSendClock, null);

            // Call base class method first.
            base.PrepareRTClass3TimingProperties(nav, ref hash);

            if (nav != null)
            {
                // Get PreferredSendclock attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_PreferredSendClock, String.Empty);
                if (attr.Length != 0)
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldPreferredSendClock] = value;

                }
            }
        }

        protected override void PrepareApplicationRelations(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for ApplicationRelations object.
            hash.Add(Models.s_FieldNumberOfAr, null);

            base.PrepareApplicationRelations(nav, ref hash);

            // Get PullModuleAlarmSupported attribute. Optional.
            //RQ AP01298586 Abfrage ob nav ein NULLPoint ist eingebaut
            if (nav != null)
            {
                string attr = nav.GetAttribute(Attributes.s_NumberOfAr, String.Empty);
                if (String.IsNullOrEmpty(attr))
                {
                    hash[Models.s_FieldNumberOfAr] = Attributes.s_DefaultNumberOfAr;
                }
                else
                {
                    UInt32 value = 0;
                    UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                    hash[Models.s_FieldNumberOfAr] = value;

                }
            }

            // --------------------------------------------
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIsSharedDeviceSupported, null);
            hash.Add(Models.s_FieldIsSharedInputSupported, null);
            hash.Add(Models.s_FieldIsDeviceAccessSupported, null);

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            // Get SharedDeviceSupported Optional. 
            string attr = nav.GetAttribute(Attributes.s_SharedDeviceSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))

            {
                hash[Models.s_FieldIsSharedDeviceSupported] = Help.GetBool(attr, Attributes.s_DefaultSharedDeviceSupported);
            }
            else
            {
                hash[Models.s_FieldIsSharedDeviceSupported] = Attributes.s_DefaultSharedDeviceSupported;
            }

            // Get SharedInputSupported Optional. 
            attr = nav.GetAttribute(Attributes.s_SharedInputSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIsSharedInputSupported] = Help.GetBool(attr, Attributes.s_DefaultSharedInputSupported);
            }
            else
            {
                hash[Models.s_FieldIsSharedInputSupported] = Attributes.s_DefaultSharedInputSupported;
            }

            // Get DeviceAccessSupported Optional. 
            attr = nav.GetAttribute(Attributes.s_DeviceAccessSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIsDeviceAccessSupported] = Help.GetBool(attr, Attributes.s_DefaultDeviceAccessSupported);
            }
            else
            {
                hash[Models.s_FieldIsDeviceAccessSupported] = Attributes.s_DefaultDeviceAccessSupported;
            }

            attr = nav.GetAttribute(Attributes.s_WebServer, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldWebServer] = attr;
            }
        }

        protected override void PrepareIOConfigData(XPathNavigator nav, ref Hashtable hash)
        {
            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMaxTotalDataLength, null);

            // Call base class method first.
            base.PrepareIOConfigData(nav, ref hash);

            // Get maximal total data length attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_MaxTotalDataLength, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                // Set default.
                hash[Models.s_FieldMaxTotalDataLength] =
                    (UInt32)hash[Models.s_FieldMaxDataLength];
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldMaxDataLength] = value;
            }
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIsProfIenergySupported, null);
            hash.Add(Models.s_FieldSupportedSubstitutionModes, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            // Get SupportedSubstitutionModes attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_SupportedSubstitutionModes, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldSupportedSubstitutionModes] = ValueListHelper.SeparateUnsignedValueList(attr);
            }

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ProfIenergy, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsProfIenergySupported] = true;
            }
        }

        #endregion

        #region Creation

        protected override void CreatePhysicalSubmodules()
        {
            base.CreatePhysicalSubmodules();

            // Select all Modules
            XPathNavigator nav = Gsd.CreateNavigator();

            if (nav == null)
            {
                return;
            }

            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_PortSubmoduleItem]);

            // Prepare data for each submodule, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectPortSubmodule;
                C.PortSubmodule obj = (C.PortSubmodule)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectPortSubmodule + "' couldn't be created!");

                // Add object to store.
                CStore.PluggablePortsubmodules.Add(obj.GsdID, obj);
            }
        }

        #endregion


        #endregion

        //########################################################################################
        #region Structure Model Methods

        #region Preparation
        //#########################################################################################

        protected override void PrepareSubmoduleStructureElement(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Submodule data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSubmoduleType, null);

            switch (nav.LocalName)
            {
                case Elements.s_SubmoduleItem:
                case Elements.s_VirtualSubmoduleItem:
                case Elements.s_InterfaceSubmoduleItem:
                    {
                        hash[Models.s_FieldSubmoduleType] = "Submodule";
                        break;
                    }
                case Elements.s_PortSubmoduleItem:
                    {
                        hash[Models.s_FieldSubmoduleType] = "Port";
                        break;
                    }
            }

            // Prepare data of base class.
            PrepareStructureElement(nav, ref hash);
        }

        #endregion

        //########################################################################################
        #region Creation

        protected override void CreateSubmoduleStructureElements()
        {
            base.CreateSubmoduleStructureElements();

            // Select all Submodules
            XPathNavigator nav = Gsd.CreateNavigator();
            if (nav == null)
            {
                return;
            }
            XPathNodeIterator nodes = nav.Select((XPathExpression)Expressions[Elements.s_PortSubmoduleItem]);

            // Check whether Submodules are available.
            if (nodes.Count == 0)
                return;

            // Prepare data for each Submodule, create it and add it to store.
            while (nodes.MoveNext())
            {
                // Create object.
                string name = Models.s_ObjectSubmoduleStructureElement;
                S.SubmoduleStructureElement obj = (S.SubmoduleStructureElement)CreateGsdObject(name, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSubmoduleStructureElement + "' couldn't be created!");

                // Add object to store.
                SStore.PhysicalSubmodules.Add(obj.GsdID, obj);
            }
        }

        #endregion

        #endregion

    }
}

