/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyLog.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

#endregion

namespace PNConfigLib.Consistency
{
    public enum ConsistencyType
    {
        GSDML,

        XSD,

        XML,

        PN,

        InternalError,

        Undefined
    }
    public enum LogSeverity
    {
        Unknown,

        Debug,

        Info,

        Warning,

        Error
    }

    public class ConsistencyLog
    {
        public ConsistencyLog(ConsistencyType type, LogSeverity severity, string message, string fullPath)
        {
            Type = type;
            Severity = severity;
            Message = message;
            Path = fullPath;
        }

        public string Message { get; }

        public string Path { get; }

        public LogSeverity Severity { get; }

        public ConsistencyType Type { get; }
    }
}