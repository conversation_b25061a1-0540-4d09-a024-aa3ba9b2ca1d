/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyLogger.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;
using System.Resources;
using System.Text;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.Consistency
{
    internal static class ConsistencyLogger
    {
        private static List<ConsistencyLog> s_ConsistencyLogs = new List<ConsistencyLog>();
        internal static List<ConsistencyLog> ConsistencyLogs => s_ConsistencyLogs;

        internal static void Log(ConsistencyType type, LogSeverity severity, object path, string consistencyKey, params object[] messageParameters)
        {
            PclObject pclObject = path as PclObject;
            if (pclObject != null)
            {
                path = RetrieveFullObjectPath(pclObject);
            }
            string logMessage = RetrieveConsistencyMessage(consistencyKey, messageParameters);
            s_ConsistencyLogs.Add(new ConsistencyLog(type, severity, logMessage, path as string));
        }
        internal static void Reset()
        {
            s_ConsistencyLogs.Clear();
        }
        /// <summary>
        /// Retrieves the whole path of the given PclObject.
        /// </summary>
        /// <param name="pclObject">Object from which the path will be retrieved</param>
        /// <returns>Full hierarchical path as a string</returns>
        private static string RetrieveFullObjectPath(PclObject pclObject)
        {
            if (pclObject == null)
            {
                return "";
            }

            PclObject tempObject = pclObject;
            List<string> hierarchy = new List<string>();

            int iteration = 0;
            while (tempObject != null)
            {
                hierarchy.Add(AttributeUtilities.GetName(tempObject));
                tempObject = tempObject.ParentObject;
                iteration++;
            }

            StringBuilder stringBuilder = new StringBuilder();
            for (int i = iteration - 1; i >= 0; i--)
            {
                stringBuilder.Append(hierarchy[i]);
                stringBuilder.Append("/");
            }

            return stringBuilder.ToString();
        }

        /// <summary>
        /// Retrieves the consistency Message with the given key from the ConsistencyMessage resources.
        /// </summary>
        /// <param name="key">The key to be queried in the resource file.</param>
        /// <param name="args">Arguments that can passed for the string</param>
        /// <returns></returns>
        internal static string RetrieveConsistencyMessage(string key, params object[] args)
        {
            ResourceManager resourceManager = new ResourceManager(
                "PNConfigLib.Consistency.ConsistencyMessagesEn",
                Assembly.GetExecutingAssembly());
            string rawString = resourceManager.GetString(key, CultureInfo.InvariantCulture);

            if (string.IsNullOrEmpty(rawString))
            {
                return key;
            }
            if ((args != null) && (args.Length > 0))
            {
                return string.Format(CultureInfo.InvariantCulture, rawString, args);
            }
            return rawString;
        }
    }
}
