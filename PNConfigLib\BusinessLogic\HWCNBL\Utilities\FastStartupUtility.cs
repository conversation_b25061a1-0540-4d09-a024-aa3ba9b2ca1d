/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: FastStartupUtility.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class FastStartupUtility
    {
        internal static void CheckConsistencyFastStartup(Interface interfaceSubmodule)
        {
            bool pnDeviceFSUPriority =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDeviceFSUPriority,
                    new AttributeAccessCode(),
                    false);

            if (pnDeviceFSUPriority)
            {
                //The devices which are not connected to an iosystem, 
                //cannot be fast startup devices.
                DataModel.PCLObjects.IOSystem ioSystem = interfaceSubmodule.PNIOD.IOSystem;

                if (ioSystem == null)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.FastStartupDeviceWithoutIOSystem,
                            AttributeUtilities.GetName(interfaceSubmodule));
                }

                //Check Autoneg/Autocrossover option of ports,
                //if it is a fast startup device
                IList<DataModel.PCLObjects.Port> ports = interfaceSubmodule.GetPorts();
                List<string> faultyPortNames = new List<string>();
                if ((null != ports)
                    && (ports.Count() != 0))
                {
                    foreach (DataModel.PCLObjects.Port port in ports)
                    {
                        bool pnPortAutoNegotiation =
                            port.AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnPortAutoNegotiation,
                                new AttributeAccessCode(),
                                false);

                        //Check if no MAU type is set for the port, if it is a fast startup device
                        uint currentValue =
                            port.AttributeAccess.GetAnyAttribute<uint>(
                                InternalAttributeNames.PnEthernetMediumDuplex,
                                new AttributeAccessCode(),
                                8);
                        if (((PNEthernetMediumDuplex)currentValue == PNEthernetMediumDuplex.Automatic)
                            || pnPortAutoNegotiation)
                        {
                            faultyPortNames.Add(AttributeUtilities.GetName(port));
                        }
                    }

                    if (faultyPortNames.Count > 0)
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN,LogSeverity.Info, interfaceSubmodule, ConsistencyConstants.PortMauNotSetOrAutonegotiationActivated,
                                AttributeUtilities.GetName(interfaceSubmodule),
                                faultyPortNames.Aggregate((onePort, otherPort) => onePort + "," + otherPort));
                    }
                }
            }
        }

        internal static int FastStartupMaxCountExceeded(Interface controllerInterface)
        {
            List<Interface> devices = NavigationUtilities.GetDevicesOfController(controllerInterface);
            AttributeAccessCode ac = new AttributeAccessCode();
            int PNIoFastStartupMaxCount =
                (int)
                controllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoFastStartupMaxCount,
                    ac,
                    0);
            if ((devices == null)
                || !devices.Any())
            {
                return -PNIoFastStartupMaxCount;
            }
            int pnFastStartupCount = 0;
            foreach (Interface device in devices)
            {
                if (device.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDeviceFSUPriority,
                    ac.GetNew(),
                    false))
                {
                    pnFastStartupCount++;
                }
            }
            return pnFastStartupCount - PNIoFastStartupMaxCount;
        }
    }
}