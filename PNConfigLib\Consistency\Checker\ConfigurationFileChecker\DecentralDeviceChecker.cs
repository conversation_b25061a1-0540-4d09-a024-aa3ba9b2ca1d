/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceChecker.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.Importer.GSDImport.Helper;
using Subnet = PNConfigLib.ConfigReader.Configuration.Subnet;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class DecentralDeviceChecker : IConsistencyChecker
    {
        /// <summary>
        /// Keeps Central Device Type
        /// </summary>
        private List<DecentralDeviceType> m_DecentralDevices;

        /// <summary>
        /// Keeps subnets of the configuration file
        /// </summary>
        private List<Subnet> m_Subnets;

        private readonly Topology m_Topology;

        private readonly ConfigReader.ListOfNodes.ListOfNodes m_ListOfNodes;

        public DecentralDeviceChecker(List<DecentralDeviceType> decentralDevices, List<Subnet> subnets, ConfigReader.ListOfNodes.ListOfNodes lon, Topology topo)
        {
            m_DecentralDevices = decentralDevices;
            m_Subnets = subnets;
            m_ListOfNodes = lon;
            m_Topology = topo;
        }

        /// <summary>
        /// Start point for the decentral device checks
        /// </summary>
        /// <returns></returns>
        public void Check()
        {
            // Call related check methods
            CheckDecentralDeviceSyncRole();
            CheckDefaultRouterSettingsAndParameterRecordDataItem();
            IsSubnetConfigurationValidForIOD();
            CheckParameterization();
            CheckDeviceHasInterface();
            CheckDeviceParametrization();
            CheckDcpReadOnlyEnabled();
        }

        internal void CheckDeviceHasInterface()
        {
            foreach (DecentralDeviceType decentralDevice in m_DecentralDevices)
            {
               ConfigReader.ListOfNodes.DecentralDeviceType lonDevice = m_ListOfNodes.DecentralDevice
                    .FirstOrDefault(d => d.DeviceID == decentralDevice.DeviceRefID);

                DecentralDeviceCatalog decentralDeviceCatalog =
                        DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                            lonDevice.GSDPath,
                            lonDevice.GSDRefID);

                var gsdDeviceInterfaceSupported = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSubmoduleModelSupp,
                    new AttributeAccessCode(),
                    false);

                if (decentralDevice.DecentralDeviceInterface != null && !gsdDeviceInterfaceSupported)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Info,
                        string.Empty,
                        ConsistencyConstants.XML_InterfaceDoesNotExist,
                        decentralDevice.DeviceRefID,
                        decentralDevice.DecentralDeviceInterface.InterfaceRefID);
                }
            }
        }

        internal void CheckDeviceParametrization()
        {
            foreach (DecentralDeviceType decentralDevice in m_DecentralDevices)
            {
                ConfigReader.ListOfNodes.DecentralDeviceType lonDevice = m_ListOfNodes.DecentralDevice
                    .FirstOrDefault(d => d.DeviceID == decentralDevice.DeviceRefID);

                DecentralDeviceCatalog decentralDeviceCatalog =
                        DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                            lonDevice.GSDPath,
                            lonDevice.GSDRefID);

                bool isParameterizationDisallowed = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnParameterizationDisallowed,
                new AttributeAccessCode(),
                false);

                if (decentralDevice.DecentralDeviceInterface != null && isParameterizationDisallowed)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Info,
                        string.Empty,
                        ConsistencyConstants.XML_InterfaceParameterizationDisallowed,
                        decentralDevice.DeviceRefID,
                        decentralDevice.DecentralDeviceInterface.InterfaceRefID);
                }
            }
        }

        internal void CheckParameterization()
        {
            foreach (DecentralDeviceType decentralDevice in m_DecentralDevices)
            {
                var lonDevice = m_ListOfNodes.DecentralDevice
                    .FirstOrDefault(d => d.DeviceID == decentralDevice.DeviceRefID);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                if (!IsParametrizationValid(decentralDevice, decentralDeviceCatalog))
                {
                    return;
                }

                if (!IsInterfaceConfigurationAvailable(decentralDevice, decentralDeviceCatalog))
                {
                    return;
                }

                DecentralIPProtocolType ipProtocol =
                    decentralDevice.DecentralDeviceInterface.EthernetAddresses.IPProtocol;
                if ((PNAttributeUtility.GetPNIoIpConfigModeSupported(decentralDeviceCatalog.Interface)
                     == NodeIeBusinessLogic.PNIoIpConfigModeSupported.Local)
                    && ipProtocol.ItemElementName != ItemChoiceType.SetDirectlyAtTheDevice)
                {
                    // IP Configuration Consistency
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IoIpConfigMode,
                        decentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }
        internal void CheckDcpReadOnlyEnabled()
        {
            foreach (DecentralDeviceType decentralDevice in m_DecentralDevices)
            {
                var lonDevice =
                    m_ListOfNodes.DecentralDevice.FirstOrDefault(d => d.DeviceID == decentralDevice.DeviceRefID);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                bool isDcpReadOnlyEnabled =
                    decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.DcpEnableReadOnly,
                        new AttributeAccessCode(),
                        false);
                bool isDcpReadOnlyActivated = decentralDevice.AdvancedConfiguration.Dcp.ActivateDcpReadOnly;

                if (isDcpReadOnlyActivated && !isDcpReadOnlyEnabled)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.GSDML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.DcpEnableReadOnlyConfigurable,
                        lonDevice.DeviceID);
                }
            }
        }
        private bool IsParametrizationValid(DecentralDeviceType device, DecentralDeviceCatalog decentralDeviceCatalog)
        {
            bool isParameterizationDisallowed = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnParameterizationDisallowed,
                new AttributeAccessCode(),
                false);

            if (!isParameterizationDisallowed)
            {
                return true;
            }

            if (AreInterfaceOptionsSet(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterizationDisallowedInterfaceOptions,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            if (IsSyncRoleSet(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterizationDisallowed_SyncRole,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            if (IsMrpConfigured(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterizationDisallowed_MediaRedundancy,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            return true;
        }

        private bool IsInterfaceConfigurationAvailable(DecentralDeviceType device, DecentralDeviceCatalog decentralDeviceCatalog)
        {
            bool interfaceSubmoduleSupported = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoSubmoduleModelSupp,
                new AttributeAccessCode(),
                false);

            if (interfaceSubmoduleSupported)
            {
                return true;
            }

            if (AreInterfaceOptionsSet(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InterfaceNotSupported_InterfaceOptions,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            if (IsSyncRoleSet(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InterfaceNotSupported_SyncRole,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            if (IsMrpConfigured(device))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InterfaceNotSupported_MediaRedundancy,
                    device.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            return true;
        }

        private bool AreInterfaceOptionsSet(DecentralDeviceType device)
        {
            DecentralDeviceInterfaceOptionsType interfaceOptions =
                device.DecentralDeviceInterface.AdvancedOptions.InterfaceOptions;
            // check if any InterfaceOptions property true because their defaults are false
            if ((interfaceOptions != null)
                && (interfaceOptions.OptionalIODevice || interfaceOptions.PrioritizedStartup
                                                      || interfaceOptions.UseIECV22LLDPMode))
            {

                return true;
            }

            return false;
        }

        private bool IsSyncRoleSet(DecentralDeviceType device)
        {
            DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization syncronization = device
                .DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization;
            if ((syncronization != null)
                && (syncronization.SynchronizationRole != SyncRole.Unsynchronized))
            {
                return true;
            }

            return false;
        }

        private bool IsMrpConfigured(DecentralDeviceType device)
        {
            List<MrpRingType> xmlMediaRedundancy =
                device.DecentralDeviceInterface.AdvancedOptions.MediaRedundancy;
            if ((xmlMediaRedundancy != null)
                && (xmlMediaRedundancy.Count > 0))
            {
                return true;
            }

            return false;
        }

        private void IsSubnetConfigurationValidForIOD()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_DecentralDevices)
            {
                string deviceRefId = xmlDecentralDevice.DeviceRefID;
                string interfaceRefId = xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID;
                string subnetRefId = xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID;
                string ioSystemRefId = xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID;
                string syncDomainRefId = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SyncDomainRefID;
                if (string.IsNullOrEmpty(subnetRefId))
                {
                    CheckSubnetIOSystemAndSyncDomainRelationForIOD(subnetRefId, deviceRefId, ioSystemRefId, syncDomainRefId);
                    continue;
                }

                DoesSubnetExist(subnetRefId, deviceRefId, interfaceRefId);
                CheckSharedDeviceAndIOSystem(xmlDecentralDevice, subnetRefId, interfaceRefId, deviceRefId, ioSystemRefId, syncDomainRefId);
                CheckUpdateTime(xmlDecentralDevice);
            }
        }

        private void CheckSubnetIOSystemAndSyncDomainRelationForIOD(string subnetRefId, string deviceRefId, string ioSystemRefId, string syncDomainRefId)
        {
            if ((ioSystemRefId != null)
                        && (syncDomainRefId != null))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain,
                    deviceRefId,
                    subnetRefId,
                    ioSystemRefId,
                    syncDomainRefId);
                throw new ConsistencyCheckException();
            }
            if (ioSystemRefId != null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IODNotConnectedToSubnetButToIOSystem,
                    deviceRefId,
                    subnetRefId,
                    ioSystemRefId);
                throw new ConsistencyCheckException(); ;
            }
            if (syncDomainRefId != null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IODNotConnectedToSubnetButToSyncDomain,
                    deviceRefId,
                    subnetRefId,
                    syncDomainRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void DoesSubnetExist(string subnetRefId, string deviceRefId, string interfaceRefId)
        {
            Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == subnetRefId);
            if (xmlSubnet == null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SubnetNotFoundForIOD,
                    subnetRefId,
                    deviceRefId,
                    interfaceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckSharedDeviceAndIOSystem(DecentralDeviceType xmlDecentralDevice, string subnetRefId, string interfaceRefId, string deviceRefId, string ioSystemRefId, string syncDomainRefId)
        {
            if ((xmlDecentralDevice.SharedDevice != null)
                    && (xmlDecentralDevice.SharedDevice.Count > 0))
            {
                AreAssignedControllersUniqueForSharedDevice(xmlDecentralDevice, deviceRefId);
                DoesSharedDeviceHaveIOSystem(deviceRefId, ioSystemRefId);
                DoesSharedDeviceHaveSyncDomain(deviceRefId, syncDomainRefId);
            }
            else
            {
                // Check IOSystemRefID.
                if (!string.IsNullOrEmpty(ioSystemRefId))
                {
                    DoesIOSystemExistForIOD(subnetRefId, interfaceRefId, deviceRefId, ioSystemRefId);
                }
                else
                {
                    IsIOSystemAndSyncDomainDeclaredForIod(xmlDecentralDevice, deviceRefId, syncDomainRefId);

                }
            }
        }

        private void AreAssignedControllersUniqueForSharedDevice(DecentralDeviceType xmlDecentralDevice, string deviceRefId)
        {
            if (xmlDecentralDevice.SharedDevice.Count != xmlDecentralDevice.SharedDevice
                        .GroupBy(sd => sd.DeviceRefID).Distinct().Count())
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SharedDeviceAssignedControllerUniqueness,
                    deviceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void DoesSharedDeviceHaveIOSystem(string deviceRefId, string ioSystemRefId)
        {
            if (!string.IsNullOrEmpty(ioSystemRefId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SharedDeviceIOSystemCheck,
                    deviceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void DoesSharedDeviceHaveSyncDomain(string deviceRefId, string syncDomainRefId)
        {
            if (!string.IsNullOrEmpty(syncDomainRefId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SharedDeviceSyncDomainCheck,
                    deviceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void DoesIOSystemExistForIOD(string subnetRefId, string interfaceRefId, string deviceRefId, string ioSystemRefId)
        {
            Subnet xmlSubnet = m_Subnets.SingleOrDefault(e => e.SubnetID == subnetRefId);
            if (!xmlSubnet.IOSystem.Exists(e => e.IOSystemID == ioSystemRefId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IoSystemNotFoundForIOD,
                    ioSystemRefId,
                    deviceRefId,
                    interfaceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckUpdateTime(DecentralDeviceType xmlDecentralDevice)
        {
            IOCycleTypeUpdateTime updateTime = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions
                    .RealTimeSettings.IOCycle.UpdateTime;
            IsUpdateTimeSetInManualMode(xmlDecentralDevice, updateTime);
            IsUpdateTimeNotSetInAutomaticMode(xmlDecentralDevice, updateTime);
        }

        private void IsUpdateTimeSetInManualMode(DecentralDeviceType xmlDecentralDevice, IOCycleTypeUpdateTime updateTime)
        {
            if ((updateTime.Mode == IOCycleTypeUpdateTimeMode.Manual)
                && (updateTime.Value == 0f))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UpdateTimeMustBeSetInManualMode,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsUpdateTimeNotSetInAutomaticMode(DecentralDeviceType xmlDecentralDevice, IOCycleTypeUpdateTime updateTime)
        {
            if ((updateTime.Mode == IOCycleTypeUpdateTimeMode.Automatic)
                    && updateTime.ModeSpecified
                    && (updateTime.Value != 0f))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UpdateTimeMustNotBeSetInAutomaticMode,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsIOSystemAndSyncDomainDeclaredForIod(DecentralDeviceType xmlDecentralDevice, string deviceRefId, string syncDomainRefId)
        {
            if (string.IsNullOrEmpty(syncDomainRefId)
                        && (m_Topology != null)
                        && m_Topology.PortInterconnection.Any(
                            portInterConn => (portInterConn.LocalPort.DeviceRefID == xmlDecentralDevice.DeviceRefID)
                                             || (portInterConn.PartnerPort.DeviceRefID
                                                 == xmlDecentralDevice.DeviceRefID)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IOSystemAndSyncDomainNotDeclaredForIod,
                    deviceRefId);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckDefaultRouterSettingsAndParameterRecordDataItem()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_DecentralDevices)
            {
                string deviceRefId = xmlDecentralDevice.DeviceRefID;
                string interfaceRefId = xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID;
                CheckDefaultRouterSettings(xmlDecentralDevice, deviceRefId, interfaceRefId);
                CheckParameterRecordDataItem(xmlDecentralDevice);
            }
        }

        private void CheckDefaultRouterSettings(
            DecentralDeviceType xmlDecentralDevice,
            string deviceRefId,
            string interfaceRefId)
        {
            DecentralIPProtocolTypeSetInTheProject setIpAddressInProject =
            xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses.IPProtocol.Item as
                DecentralIPProtocolTypeSetInTheProject;
            if (setIpAddressInProject != null)
            {
                bool isRouterSyncWithController = setIpAddressInProject.SynchronizeRouterSettingsWithIOController;
                string routerAddress = setIpAddressInProject.RouterAddress;
                if ((routerAddress != PNConstants.DefaultRouterIP) && isRouterSyncWithController)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_WrongDefaultRouterSettingInIOD,
                        deviceRefId,
                        interfaceRefId);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckParameterRecordDataItem(DecentralDeviceType xmlDecentralDevice)
        {
            if (xmlDecentralDevice == null)
            {
                return;
            }
            if ((xmlDecentralDevice.ParameterRecordDataItems != null)
                && !CheckParameterRecords(xmlDecentralDevice.ParameterRecordDataItems))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_ParameterRecordDataItemIndexNotUnique,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
            if (xmlDecentralDevice.Module == null)
            {
                return;
            }
            foreach (ModuleType xmlModule in xmlDecentralDevice.Module)
            {
                if ((xmlModule.ParameterRecordDataItems != null)
                    && !CheckParameterRecords(xmlModule.ParameterRecordDataItems))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_ParameterRecordDataItemIndexNotUnique,
                        xmlModule.ModuleID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private bool CheckParameterRecords(List<ParameterRecordDataItemsTypeParameterRecordDataItem> parameterRecordDataItems)
        {
            List<int> indexList = new List<int>();

            foreach (ParameterRecordDataItemsTypeParameterRecordDataItem parameterRecordData in parameterRecordDataItems)
            {
                indexList.Add(parameterRecordData.GSDRefIndex);
            }
            return indexList.Distinct().Count() == indexList.Count();
        }

        private void CheckDecentralDeviceSyncRole()
        {
            foreach (ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice in m_DecentralDevices)
            {
                SyncRole xmlSyncRole = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;

                if (xmlSyncRole == SyncRole.Unsynchronized)
                {
                    continue;
                }

                PNIRTSyncRole currentSyncRole = AttributeUtilities.MapSyncRoleEnum(xmlSyncRole);
                var lonDevice =
                    m_ListOfNodes.DecentralDevice.FirstOrDefault(d => d.DeviceID == xmlDecentralDevice.DeviceRefID);

                if (lonDevice == null)
                {
                    continue;
                }

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                if (decentralDeviceCatalog == null)
                {
                    continue;
                }

                Enumerated supportedSyncRolesEnumerated =
                    decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSyncRoleSupp,
                        new AttributeAccessCode(),
                        null);

                if (supportedSyncRolesEnumerated == null)
                {
                    continue;
                }

                List<PNIRTSyncRole> supportedSyncRoles =
                    supportedSyncRolesEnumerated.List.Cast<PNIRTSyncRole>().ToList();

                if (supportedSyncRoles.Contains(currentSyncRole))
                {
                    continue;
                }

                string interfaceIdPath = StringOperations.CombineParametersWithBackSlash(
                    xmlDecentralDevice.DeviceRefID,
                    xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID);
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InvalidSyncRole,
                    xmlSyncRole,
                    interfaceIdPath);
                throw new ConsistencyCheckException();
            }
        }

        internal static void CanIpAddressSetDirectlyAtTheDevice(
            DecentralDeviceTypeDecentralDeviceInterface xmlDeviceInterface,
            bool ipConfigModeSupported,
            bool addressTailoringEnabled)
        {
            DecentralIPProtocolType ipProtocol = xmlDeviceInterface.EthernetAddresses.IPProtocol;

            if (!ipConfigModeSupported
                && !addressTailoringEnabled
                && ipProtocol.ItemElementName == ItemChoiceType.SetDirectlyAtTheDevice)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IpSetDirectlyAtTheDeviceNotSupported,
                    xmlDeviceInterface.InterfaceRefID);
            }
        }
    }
}
