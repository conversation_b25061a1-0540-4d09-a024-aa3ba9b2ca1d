/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: CatalogHelper.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: CatalogHelper.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */

using System.IO;

namespace PNConfigLib.Importer.GSDImport.Helper
{
    /// <summary>
    /// Contains helper methods to read catalog objects
    /// </summary>
    public static class CatalogHelper
    {
        /// <summary>
        /// Gets gsdml object id
        /// </summary>
        /// <param name="gsdName"></param>
        /// <param name="gsdId"></param>
        /// <returns></returns>
        public static string GetGsdKeyByGsdName(string gsdName, string gsdId)
        {
            return StringOperations.CombineParametersWithBackSlash(gsdName, gsdId);
        }

        /// <summary>
        /// Gets gsdml object id
        /// </summary>
        /// <param name="gsdPath"></param>
        /// <param name="gsdId"></param>
        /// <returns></returns>
        public static string GetGsdKeyByGsdPath(string gsdPath, string gsdId)
        {
            string gsdName = Path.GetFileName(gsdPath);
            return GetGsdKeyByGsdName(gsdName, gsdId);
        }
    }
}
