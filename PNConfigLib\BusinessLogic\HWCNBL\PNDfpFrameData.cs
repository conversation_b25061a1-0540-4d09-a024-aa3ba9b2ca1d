/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNDfpFrameData.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL
{
    /// <summary>
    /// Summary description for PNDfpFrameData.
    /// </summary>
    internal class PNDfpFrameData : IPNDfpFrameData
    {
        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private readonly List<int> m_CoreIds;

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public PNDfpFrameData(List<int> coreIds)
        {
            m_CoreIds = coreIds;
            PossibleReductionRatios = new List<long>();
            SuppRRNonPow = new List<long>();
            SuppRRPow = new List<long>();
            SuppSendClockFactors = new List<long>();
            Subframes = new SortedList<int, IPNSubframeData>();
            FrameIdSet = false;
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #region IPNDfpFrameData Members

        public IList<int> CoreIds => m_CoreIds; 

        public int CoreId
        {
            get
            {
                Debug.Assert(m_CoreIds.Count == 1, "More than one io-device have this frame. Use CoreIds instead.");
                return m_CoreIds[0];
            }
        }

        public string Version => string.Empty; 

        public long FrameID { get; set; }

        public long RedundantFrameID { get; set; }

        public int StationNumber { get; set; }

        public long FrameType { get; set; }

        public byte FrameDirection { get; set; }

        public long FrameClass { get; set; }

        public long DataLength { get; set; }

        public long SharedDataLength { get; set; }

        public long DeviceLocalReductionRatio { get; set; }

        public int ReductionGranularity => 0; 

        public long SendClockFactor { get; set; }

        public long MinFrameIntervall { get; set; }

        public IList<long> SuppRRPow { get; set; }

        public IList<long> SuppRRNonPow { get; set; }

        public IList<long> SuppSendClockFactors { get; set; }

        public byte UpdateTimeMode { get; set; }

        public byte IoSyncRole { get; set; }

        public bool HasUsingData { get; set; }

        public int FrameMultiplier { get; set; }

        public long FixedPhaseNumber { get; set; }

        public int ProxyNumber { get; set; }

        public int MinAutomaticUnsyncUpdateTime => 2; 

        public uint NumberOfARs { get; set; }

        public bool HasNotAssignedSubmodule { get; set; }

        public int SlotNumber { get; set; }

        public int SubSlotNumber { get; set; }

        public IList<long> PossibleReductionRatios { get; set; }

        public void SetPossibleReductionRatios(IEnumerable<long> list)
        {
            PossibleReductionRatios.Clear();
            ((List<long>)PossibleReductionRatios).AddRange(list);
        }

        public long ControllerLocalReductionRatio { get; set; }

        public long DeviceLocalPhase { get; set; }

        public long ControllerLocalPhase { get; set; }

        public long Sequence { get; set; }

        public long DataHoldFactor { get; set; }

        public long WatchdogFactor { get; set; }

        public int GroupNo { get; set; }

        public int IOCRReferenceNo { get; set; }

        public bool FrameIdSet { get; set; }

        public bool SfCrc16 { get; set; }

        public uint DistributedWdFactor { get; set; }

        public uint DistributedRestartFactor { get; set; }

        public bool DfpRedundantPathLayout { get; set; }

        public SortedList<int, IPNSubframeData> Subframes { get; }

        /// <summary>
        /// Gets the total frame length by adding the header and the trailer.
        /// Parts of the frame considered:
        /// IFG(12 Byte) + Safety (in RTC3) (2 Byte) + Preamble (1 or 7 Byte) + Start Frame Delimiter (1 Byte) +
        /// Ethernet Header (14 Byte) + VLAN Header (in RTC 1&2) (4 Byte) + PROFINET Header (2 Byte) + Datalength +
        /// PROFINET Trailer (4 Byte) + Frame Check Sequence (4 Byte)
        /// Datalength must be minimum 40 Bytes. If the actual data length is smaller than 40 bytes, a payload is added
        /// to make it 40 Bytes.
        /// </summary>
        /// <param name="useShortPreambleForRTC3">
        /// If true, 1-Byte preamble will be used in RTC3 Frames
        /// instead of 7.
        /// </param>
        public long GetTotalFrameLength(bool useShortPreambleForRTC3)
        {
            //Minimum Frame lengths
            const int class12Header = 48;
            int class3Header = useShortPreambleForRTC3 ? 40 : 46;

            long totalFrameLength = Math.Max(DataLength, PNConstants.MinFramePayloadLength);
            if ((FrameClass == (long)PNIOFrameClass.Class1Frame)
                || (FrameClass == (long)PNIOFrameClass.Class2Frame))
            {
                totalFrameLength += class12Header;
            }
            else // Frame class 3
            {
                totalFrameLength += class3Header;
            }
            return totalFrameLength;
        }

        #endregion

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        #endregion
    }
}