/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SyncDomain.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.HWCNBL.DomainManagement;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a sync domain.
    /// </summary>
    public class SyncDomain : Domain
    {
        /// <summary>
        /// The constructor of SyncDomain.
        /// </summary>
        /// <param name="syncDomainId">The ID of the SyncDomain.</param>
        public SyncDomain(string syncDomainId)
        {
            Id = syncDomainId;
        }

        /// <summary>
        /// The business logic object for this SyncDomain.
        /// </summary>
        public SyncDomainBusinessLogic SyncDomainBusinessLogic { get; set; }

        /// <summary>
        /// Adds an interface to the sync domain.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule to be added.</param>
        public void AddInterface(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            Participants.Add(interfaceSubmodule);
            interfaceSubmodule.SyncDomain = this;
        }

        /// <summary>
        /// Gets the list of interfaces included in this sync domain.
        /// </summary>
        /// <returns>A list containing the interfaces included in this sync domain.</returns>
        public IList<Interface> GetInterfaces()
        {
            return Participants;
        }
    }
}