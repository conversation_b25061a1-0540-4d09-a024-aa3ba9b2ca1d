/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: SyncDomainChecker.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;


namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{

    class SyncDomainChecker : IConsistencyChecker
    {

        private readonly Configuration m_Configuration;

        private readonly ListOfNodes m_ListOfNodes;

        public SyncDomainChecker(Configuration cfg, ListOfNodes lon)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            CheckDecentralDeviceSyncDomainRefId();
            CheckSyncDomainRef();
        }

        private void CheckDecentralDeviceSyncDomainRefId()
        {
            foreach (ConfigReader.Configuration.DecentralDeviceType xmlDevice in m_Configuration.Devices.DecentralDevice)
            {
                var lonDecentralDevice = ListOfNodesChecker.GetListOfNodesDeviceById(xmlDevice.DeviceRefID, m_ListOfNodes);
                string subnetRefId = xmlDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID;
                string syncDomainRefId =
                    xmlDevice.DecentralDeviceInterface.AdvancedOptions
                        .RealTimeSettings.Synchronization.SyncDomainRefID;
                Subnet xmlSubnet = m_Configuration.Subnet.SingleOrDefault(e => e.SubnetID == subnetRefId);
                // Check SyncDomainRefID

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDecentralDevice.GSDPath,
                        lonDecentralDevice.GSDRefID);
                if (decentralDeviceCatalog == null)
                {
                    continue;
                }

                bool parametrizationDisallowed =
                    decentralDeviceCatalog.Interface.AttributeAccess.AttributeList.ContainsKey(
                        InternalAttributeNames.PnParameterizationDisallowed)
                    && decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnParameterizationDisallowed,
                        new AttributeAccessCode(),
                        false);

                if (!string.IsNullOrEmpty(syncDomainRefId)
                    && !parametrizationDisallowed
                    && !xmlSubnet.DomainManagement.SyncDomains.Exists(e => e.SyncDomainID == syncDomainRefId))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SpecifiedSyncDomainNotFound,
                        syncDomainRefId,
                        xmlDevice.DeviceRefID,
                        xmlDevice.DecentralDeviceInterface.InterfaceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckSyncDomainRef()
        {
            foreach (ConfigReader.Configuration.DecentralDeviceType xmlDevice in m_Configuration.Devices.DecentralDevice)
            {
                string ioSystemRefId =
                    xmlDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID;
                if (string.IsNullOrEmpty(ioSystemRefId))
                {
                    continue;
                }

                ConfigReader.ListOfNodes.DecentralDeviceType lonDevice =
                    m_ListOfNodes.DecentralDevice.FirstOrDefault(d => d.DeviceID == xmlDevice.DeviceRefID);

                DecentralDeviceCatalog deviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                if (deviceCatalog == null)
                {
                    continue;
                }

                bool parametrizationDisallowed =
                    deviceCatalog.Interface.AttributeAccess.AttributeList.ContainsKey(
                        InternalAttributeNames.PnParameterizationDisallowed)
                    && (bool)deviceCatalog.Interface.AttributeAccess.AttributeList[InternalAttributeNames
                        .PnParameterizationDisallowed];
                string syncDomainRefId =
                    xmlDevice.DecentralDeviceInterface.AdvancedOptions
                        .RealTimeSettings.Synchronization.SyncDomainRefID;
                if (parametrizationDisallowed && !string.IsNullOrEmpty(syncDomainRefId))
                {
                    CentralDeviceType assignedController = m_Configuration.Devices.CentralDevice?.FirstOrDefault(
                        cd => cd.CentralDeviceInterface.EthernetAddresses.IOSystemRefID == xmlDevice
                                  .DecentralDeviceInterface.EthernetAddresses.IOSystemRefID);

                    if (assignedController?.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                            .Synchronization.SyncDomainRefID != xmlDevice.DecentralDeviceInterface
                            .AdvancedOptions.RealTimeSettings.Synchronization.SyncDomainRefID)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SyncDomainOfPDEV,
                            xmlDevice.DeviceRefID);
                        throw new ConsistencyCheckException();
                    }
                }

                if (!parametrizationDisallowed && string.IsNullOrEmpty(syncDomainRefId))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SyncDomainNotFounfForIOD,
                        xmlDevice.DeviceRefID,
                        xmlDevice.DecentralDeviceInterface.InterfaceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }
    }
}
