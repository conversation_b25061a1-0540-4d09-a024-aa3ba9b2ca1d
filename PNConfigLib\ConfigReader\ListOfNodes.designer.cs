// ------------------------------------------------------------------------------
//  <auto-generated>
//    Generated by Xsd2Code. Version 3.4.0.32989
//    <NameSpace>PNConfigLib.ConfigReader.ListOfNodes</NameSpace><Collection>List</Collection><codeType>CSharp</codeType><EnableDataBinding>False</EnableDataBinding><EnableLazyLoading>False</EnableLazyLoading><TrackingChangesEnable>False</TrackingChangesEnable><GenTrackingClasses>False</GenTrackingClasses><HidePrivateFieldInIDE>False</HidePrivateFieldInIDE><EnableSummaryComment>False</EnableSummaryComment><VirtualProp>False</VirtualProp><IncludeSerializeMethod>False</IncludeSerializeMethod><UseBaseClass>False</UseBaseClass><GenBaseClass>False</GenBaseClass><GenerateCloneMethod>False</GenerateCloneMethod><GenerateDataContracts>False</GenerateDataContracts><CodeBaseTag>Net20</CodeBaseTag><SerializeMethodName>Serialize</SerializeMethodName><DeserializeMethodName>Deserialize</DeserializeMethodName><SaveToFileMethodName>SaveToFile</SaveToFileMethodName><LoadFromFileMethodName>LoadFromFile</LoadFromFileMethodName><GenerateXMLAttributes>True</GenerateXMLAttributes><OrderXMLAttrib>False</OrderXMLAttrib><EnableEncoding>False</EnableEncoding><AutomaticProperties>False</AutomaticProperties><GenerateShouldSerialize>False</GenerateShouldSerialize><DisableDebug>False</DisableDebug><ProPNameSpecified>Default</ProPNameSpecified><Encoder>UTF8</Encoder><CustomUsings></CustomUsings><ExcludeIncludedTypes>False</ExcludeIncludedTypes><EnableInitializeFields>True</EnableInitializeFields>
//  </auto-generated>
// ------------------------------------------------------------------------------
namespace PNConfigLib.ConfigReader.ListOfNodes {
    using System;
    using System.Diagnostics;
    using System.Xml.Serialization;
    using System.Collections;
    using System.Xml.Schema;
    using System.ComponentModel;
    using System.Collections.Generic;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=false)]
    public partial class ListOfNodes {
        
        private List<PNDriverType> pNDriverField;
        
        private List<DecentralDeviceType> decentralDeviceField;
        
        private string listOfNodesIDField;
        
        private string schemaVersionField;
        
        public ListOfNodes() {
            this.decentralDeviceField = new List<DecentralDeviceType>();
            this.pNDriverField = new List<PNDriverType>();
            this.schemaVersionField = "1.0";
        }
        
        [System.Xml.Serialization.XmlElementAttribute("PNDriver")]
        public List<PNDriverType> PNDriver {
            get {
                return this.pNDriverField;
            }
            set {
                this.pNDriverField = value;
            }
        }
        
        [System.Xml.Serialization.XmlElementAttribute("DecentralDevice")]
        public List<DecentralDeviceType> DecentralDevice {
            get {
                return this.decentralDeviceField;
            }
            set {
                this.decentralDeviceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string ListOfNodesID {
            get {
                return this.listOfNodesIDField;
            }
            set {
                this.listOfNodesIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string schemaVersion {
            get {
                return this.schemaVersionField;
            }
            set {
                this.schemaVersionField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=true)]
    public partial class PNDriverType {
        
        private PNDriverInterfaceType interfaceField;
        
        private string deviceIDField;
        
        private string deviceNameField;
        
        private string deviceVersionField;
        
        public PNDriverType() {
            this.interfaceField = new PNDriverInterfaceType();
        }
        
        public PNDriverInterfaceType Interface {
            get {
                return this.interfaceField;
            }
            set {
                this.interfaceField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string DeviceID {
            get {
                return this.deviceIDField;
            }
            set {
                this.deviceIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceName {
            get {
                return this.deviceNameField;
            }
            set {
                this.deviceNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceVersion {
            get {
                return this.deviceVersionField;
            }
            set {
                this.deviceVersionField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=true)]
    public partial class PNDriverInterfaceType {
        
        private string interfaceIDField;
        
        private string interfaceNameField;
        
        private PNDriverInterfaceEnum interfaceTypeField;

        private string customInterfacePathField;

        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string InterfaceID {
            get {
                return this.interfaceIDField;
            }
            set {
                this.interfaceIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceName {
            get {
                return this.interfaceNameField;
            }
            set {
                this.interfaceNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public PNDriverInterfaceEnum InterfaceType {
            get {
                return this.interfaceTypeField;
            }
            set {
                this.interfaceTypeField = value;
            }
        }

        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string CustomInterfacePath
        {
            get
            {
                return this.customInterfacePathField;
            }
            set
            {
                this.customInterfacePathField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=false)]
    public enum PNDriverInterfaceEnum {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("CP1625Stand-alone")]
        CP1625Standalone,
        
        /// <remarks/>
        CP1625Host,
        
        /// <remarks/>
        Linux,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Linux Native")]
        LinuxNative,

        /// <remarks/>
        IoT20x0,
        
        /// <remarks/>
        Windows,

        /// <remarks/>
        Custom,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=true)]
    public partial class DecentralDeviceInterfaceType {
        
        private string interfaceIDField;
        
        private string interfaceNameField;
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string InterfaceID {
            get {
                return this.interfaceIDField;
            }
            set {
                this.interfaceIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceName {
            get {
                return this.interfaceNameField;
            }
            set {
                this.interfaceNameField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes", IsNullable=true)]
    public partial class DecentralDeviceType {
        
        private DecentralDeviceInterfaceType interfaceField;
        
        private string deviceIDField;
        
        private string gSDPathField;
        
        private string gSDRefIDField;
        
        private string deviceNameField;
        
        public DecentralDeviceType() {
            this.interfaceField = new DecentralDeviceInterfaceType();
        }
        
        public DecentralDeviceInterfaceType Interface {
            get {
                return this.interfaceField;
            }
            set {
                this.interfaceField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string DeviceID {
            get {
                return this.deviceIDField;
            }
            set {
                this.deviceIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string GSDPath {
            get {
                return this.gSDPathField;
            }
            set {
                this.gSDPathField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string GSDRefID {
            get {
                return this.gSDRefIDField;
            }
            set {
                this.gSDRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceName {
            get {
                return this.deviceNameField;
            }
            set {
                this.deviceNameField = value;
            }
        }
    }
}
