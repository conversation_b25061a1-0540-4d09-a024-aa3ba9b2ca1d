using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace PNConfigTool.Services
{
    public interface INavigationService
    {
        void Navigate(string pageName);
        void Navigate(string pageName, object? parameter);
        void NavigateBack();
        void RegisterPage(string pageName, Type pageType);
        void UnregisterPage(string pageName);
        event EventHandler<string>? PageChanged;
        string? CurrentPage { get; }
        object? NavigationParameter { get; }
    }

    public interface INavigationAware
    {
        void OnNavigatedTo(object? parameter);
    }

    /// <summary>
    /// 页面离开时保存状态的接口
    /// </summary>
    public interface INavigationAwareLeaving
    {
        /// <summary>
        /// 当页面即将被导航离开时调用
        /// </summary>
        /// <returns>如果可以继续导航返回true，否则返回false</returns>
        bool OnNavigatedFrom();
    }

    public class NavigationService : INavigationService
    {
        private readonly Dictionary<string, Type> _pages = new();
        private readonly Stack<NavigationEntry> _navigationHistory = new();
        private readonly ContentControl _contentControl;
        private string? _currentPage;
        private object? _navigationParameter;

        public event EventHandler<string>? PageChanged;

        public string? CurrentPage => _currentPage;
        
        public object? NavigationParameter => _navigationParameter;

        public NavigationService(ContentControl contentControl)
        {
            _contentControl = contentControl ?? throw new ArgumentNullException(nameof(contentControl));
        }

        public void RegisterPage(string pageName, Type pageType)
        {
            ArgumentException.ThrowIfNullOrEmpty(pageName);
            ArgumentNullException.ThrowIfNull(pageType);
                
            if (!typeof(Page).IsAssignableFrom(pageType))
            {
                throw new ArgumentException($"页面类型 {pageType.Name} 必须继承自Page类型");
            }

            // 如果页面已经注册，只是更新注册信息而不抛出异常
            if (_pages.ContainsKey(pageName))
            {
                // 如果页面类型相同，则不做任何操作
                if (_pages[pageName] == pageType)
                {
                    System.Diagnostics.Debug.WriteLine($"页面 {pageName} 已注册且类型相同，跳过注册。");
                    return;
                }
                
                // 如果页面类型不同，则更新页面类型
                System.Diagnostics.Debug.WriteLine($"页面 {pageName} 已注册但类型不同，更新为新类型: {pageType.Name}。");
                _pages[pageName] = pageType;
                return;
            }

            // 注册新页面
            _pages[pageName] = pageType;
            System.Diagnostics.Debug.WriteLine($"页面 {pageName} 成功注册为类型: {pageType.Name}。");
        }

        public void UnregisterPage(string pageName)
        {
            ArgumentException.ThrowIfNullOrEmpty(pageName);

            if (_pages.ContainsKey(pageName))
            {
                _pages.Remove(pageName);
                System.Diagnostics.Debug.WriteLine($"页面 {pageName} 已取消注册。");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"页面 {pageName} 未注册，无需取消注册。");
            }
        }

        public void Navigate(string pageName)
        {
            Navigate(pageName, null);
        }
        
        public void Navigate(string pageName, object? parameter)
        {
            try 
            {
                ArgumentException.ThrowIfNullOrEmpty(pageName);
                
                System.Diagnostics.Debug.WriteLine($"尝试导航到页面: {pageName}，参数类型: {parameter?.GetType().Name ?? "null"}");
                
                if (!_pages.TryGetValue(pageName, out Type? pageType))
                {
                    System.Diagnostics.Debug.WriteLine($"错误: 页面 {pageName} 未注册");
                    System.Diagnostics.Debug.WriteLine($"已注册的页面: {string.Join(", ", _pages.Keys)}");
                    throw new ArgumentException($"页面 {pageName} 未注册");
                }

                // 检查当前页面是否实现了INavigationAwareLeaving接口，如果是，则调用OnNavigatedFrom方法
                if (_contentControl.Content is INavigationAwareLeaving currentPage)
                {
                    System.Diagnostics.Debug.WriteLine($"当前页面实现了INavigationAwareLeaving接口，调用OnNavigatedFrom");
                    bool canNavigate = currentPage.OnNavigatedFrom();
                    if (!canNavigate)
                    {
                        System.Diagnostics.Debug.WriteLine($"页面阻止了导航");
                        return; // 如果页面阻止了导航，则不继续执行
                    }
                }

                System.Diagnostics.Debug.WriteLine($"创建页面实例: {pageType.Name}");
                if (Activator.CreateInstance(pageType) is not Page page)
                {
                    System.Diagnostics.Debug.WriteLine($"错误: 无法创建页面实例: {pageName}");
                    throw new InvalidOperationException($"无法创建页面实例: {pageName}");
                }
                
                System.Diagnostics.Debug.WriteLine($"设置ContentControl的内容为: {pageType.Name}");
                _contentControl.Content = page;
                _currentPage = pageName;
                _navigationParameter = parameter;
                
                // 支持INavigationAware接口，实现页面获取导航参数
                if (page is INavigationAware navigationAware)
                {
                    System.Diagnostics.Debug.WriteLine($"页面 {pageName} 实现了 INavigationAware 接口，调用 OnNavigatedTo");
                    navigationAware.OnNavigatedTo(parameter);
                }
                
                // 更新导航历史
                var entry = new NavigationEntry(pageName, parameter);
                if (_navigationHistory.Count == 0 || !_navigationHistory.Peek().Equals(entry))
                {
                    _navigationHistory.Push(entry);
                    System.Diagnostics.Debug.WriteLine($"添加导航历史: {pageName}");
                }
                
                System.Diagnostics.Debug.WriteLine($"触发PageChanged事件: {pageName}");
                PageChanged?.Invoke(this, pageName);
                
                System.Diagnostics.Debug.WriteLine($"成功导航到页面: {pageName}");
            }
            catch (Exception ex) 
            {
                System.Diagnostics.Debug.WriteLine($"导航过程中出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                throw; // 重新抛出异常以便上层处理
            }
        }

        public void NavigateBack()
        {
            if (_navigationHistory.Count <= 1)
            {
                return;
            }

            // 检查当前页面是否实现了INavigationAwareLeaving接口，如果是，则调用OnNavigatedFrom方法
            if (_contentControl.Content is INavigationAwareLeaving currentPage)
            {
                System.Diagnostics.Debug.WriteLine($"当前页面实现了INavigationAwareLeaving接口，调用OnNavigatedFrom");
                bool canNavigate = currentPage.OnNavigatedFrom();
                if (!canNavigate)
                {
                    System.Diagnostics.Debug.WriteLine($"页面阻止了导航");
                    return; // 如果页面阻止了导航，则不继续执行
                }
            }

            _navigationHistory.Pop(); // 移除当前页面
            var previousEntry = _navigationHistory.Peek();
            
            if (!_pages.TryGetValue(previousEntry.PageName, out Type? pageType))
            {
                throw new InvalidOperationException($"找不到先前的页面: {previousEntry.PageName}");
            }
            
            if (Activator.CreateInstance(pageType) is not Page page)
            {
                throw new InvalidOperationException($"无法创建页面实例: {previousEntry.PageName}");
            }
                
            _contentControl.Content = page;
            _currentPage = previousEntry.PageName;
            _navigationParameter = previousEntry.Parameter;
            
            // 支持INavigationAware接口，实现页面获取导航参数
            if (page is INavigationAware navigationAware)
            {
                System.Diagnostics.Debug.WriteLine($"返回页面 {previousEntry.PageName} 实现了 INavigationAware 接口，调用 OnNavigatedTo");
                navigationAware.OnNavigatedTo(previousEntry.Parameter);
            }
            
            PageChanged?.Invoke(this, previousEntry.PageName);
            System.Diagnostics.Debug.WriteLine($"成功返回到页面: {previousEntry.PageName}");
        }
        
        /// <summary>
        /// 导航历史条目
        /// </summary>
        private class NavigationEntry
        {
            public string PageName { get; }
            public object? Parameter { get; }
            
            public NavigationEntry(string pageName, object? parameter)
            {
                PageName = pageName;
                Parameter = parameter;
            }
            
            public override bool Equals(object? obj)
            {
                if (obj is NavigationEntry other)
                {
                    return PageName == other.PageName && Equals(Parameter, other.Parameter);
                }
                return false;
            }
            
            public override int GetHashCode()
            {
                return HashCode.Combine(PageName, Parameter);
            }
        }
    }
} 