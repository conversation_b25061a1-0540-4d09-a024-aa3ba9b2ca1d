﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CIMSNMPAdjustRecordGenerator.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Networks.SNMP._Interfaces;

#endregion

namespace PNConfigLib.HWCNBL.Networks.SNMP
{
    /// <summary>
    /// DLC to create CIMSNMPAdjust record for decentral PN interfaces
    /// </summary>
    internal class CimSnmpAdjustRecordGenerator : ISnmpControlRecordGenerator
    {
        #region Constants

        private const int s_HeaderLength = 4;

        private const int s_DataStubLength = 4;

        private const int s_MaxCommunityNameLength = 240;

        private const int s_BlockLengthFieldPosition = 2;

        private const int s_ControlFieldPosition = 6;

        private const int s_CnReadFieldPosition = 8;

        private const int s_ByteAlignment = 4;

        private readonly byte[] m_StartingBytes = { 03, 00, 0, 0, 1, 0, 0, 0 };

        #endregion

        #region ISnmpControlRecordGenerator members

        /// <summary>
        /// Generate SNMP control record
        /// </summary>
        /// <param name="headModuleDto"></param>
        /// <returns></returns>
        public byte[] GenerateSnmpControlRecord(PclObject pclObject)
        {
            #region Null check

            if (pclObject == null)
            {
                throw new ArgumentNullException(nameof(pclObject));
            }

            #endregion

            AttributeAccessCode ac = new AttributeAccessCode();

            bool snmpEnabled = pclObject.AttributeAccess.GetAnyAttribute(InternalAttributeNames.SnmpEnabled, ac, false);
            if (!ac.IsOkay)
            {
                return new byte[0];
            }

            string snmpReadOnlyCommunityName = pclObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.SnmpReadOnlyCommunityName,
                ac,
                "public");
            if (snmpReadOnlyCommunityName.Length < 1
                || snmpReadOnlyCommunityName.Length > s_MaxCommunityNameLength)
            {
                throw new InvalidOperationException(
                    "The length of SNMPReadOnlyCommunityName attribute is outside the valid range.");
            }

            string snmpReadWriteCommunityName = pclObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.SnmpReadWriteCommunityName,
                ac,
                "private");
            if (snmpReadWriteCommunityName.Length < 1
                || snmpReadWriteCommunityName.Length > s_MaxCommunityNameLength)
            {
                throw new InvalidOperationException(
                    "The length of the SNMPReadWriteCommunityName attribute is outside the valid range.");
            }

            bool snmpReadWriteAccess = !pclObject.AttributeAccess.GetAnyAttribute(
                                           InternalAttributeNames.SnmpEnableReadOnly,
                                           ac,
                                           false);

            ISnmpControlRecordHelper helper = new SnmpControlRecordHelper();

            byte[] controlArray = helper.CreateArrayFromControlForCim(snmpEnabled, snmpReadWriteAccess);

            byte[] readArray = new byte[0];
            byte[] writeArray = new byte[0];

            if (snmpEnabled)
            {
                readArray = helper.CreateArrayFromCommunityName(snmpReadOnlyCommunityName);

                if (snmpReadWriteAccess)
                {
                    writeArray = helper.CreateArrayFromCommunityName(snmpReadWriteCommunityName);
                }
            }

            return GenerateBlockFromData(readArray, writeArray, controlArray, helper);
        }

        #endregion

        #region Private methods

        private byte[] GenerateBlockFromData(
            byte[] communityNameReadArray,
            byte[] communityNameWriteArray,
            byte[] controlArray,
            ISnmpControlRecordHelper helper)
        {
            byte[] snmpReadOnlyCommunityNameBlock = new byte[communityNameReadArray.Length + 1];
            byte[] snmpReadWriteCommunityNameBlock = new byte[communityNameWriteArray.Length + 1];

            snmpReadOnlyCommunityNameBlock[0] = (byte)communityNameReadArray.Length;
            snmpReadWriteCommunityNameBlock[0] = (byte)communityNameWriteArray.Length;

            if (communityNameReadArray.Length > 0)
            {
                Buffer.BlockCopy(
                    communityNameReadArray,
                    0,
                    snmpReadOnlyCommunityNameBlock,
                    1,
                    communityNameReadArray.Length);
            }

            if (communityNameWriteArray.Length > 0)
            {
                Buffer.BlockCopy(
                    communityNameWriteArray,
                    0,
                    snmpReadWriteCommunityNameBlock,
                    1,
                    communityNameWriteArray.Length);
            }

            int dataLength = s_DataStubLength + snmpReadOnlyCommunityNameBlock.Length
                                              + snmpReadWriteCommunityNameBlock.Length;
            int paddingAtEndOfBlock = helper.CalculatePadding(s_HeaderLength + dataLength, s_ByteAlignment);
            int dataBlockLength = dataLength + paddingAtEndOfBlock;
            int totalBlockLength = s_HeaderLength + dataBlockLength;

            byte[] generatedBlock = new byte[totalBlockLength];
            byte[] dataBlockLengthArray = BitConverter.GetBytes((ushort)dataBlockLength);
            Array.Reverse(dataBlockLengthArray);

            Buffer.BlockCopy(m_StartingBytes, 0, generatedBlock, 0, m_StartingBytes.Length);
            Buffer.BlockCopy(
                dataBlockLengthArray,
                0,
                generatedBlock,
                s_BlockLengthFieldPosition,
                dataBlockLengthArray.Length);
            Buffer.BlockCopy(controlArray, 0, generatedBlock, s_ControlFieldPosition, controlArray.Length);

            Buffer.BlockCopy(
                snmpReadOnlyCommunityNameBlock,
                0,
                generatedBlock,
                s_CnReadFieldPosition,
                snmpReadOnlyCommunityNameBlock.Length);

            int communityNameWriteFieldPosition = s_CnReadFieldPosition + snmpReadOnlyCommunityNameBlock.Length;
            Buffer.BlockCopy(
                snmpReadWriteCommunityNameBlock,
                0,
                generatedBlock,
                communityNameWriteFieldPosition,
                snmpReadWriteCommunityNameBlock.Length);

            return generatedBlock;
        }

        #endregion

    }
}