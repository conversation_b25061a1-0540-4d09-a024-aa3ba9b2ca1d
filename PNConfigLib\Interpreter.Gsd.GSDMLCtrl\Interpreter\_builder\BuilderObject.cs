/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderObject.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces

using System.IO;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
	/// <summary>
	/// This is a base class for all builder classes. It is needed, because 
	/// there is a must, that every builder object can be accessed by the
	/// interpreter by a common way.
	/// </summary>
	internal abstract class BuilderObject
	{

		/// <value>
		/// Accesses the GSDML version supported from the internal builder.
		/// </value>
		public abstract string SupportedGsdmlVersion { get; }

		/// <summary>
		/// Builds the specified data object model(s) for the given GSD file and
		/// returns whether the building was successfull or not.
		/// The created data object model(s) can be accessed by the specific model
		/// stores.
		/// </summary>
		/// <param name="gsddocstream">Stream of the GSD document to check.</param>
		/// <param name="pathname">The full path name of the GSD to check.</param>
		/// <param name="lModelOption">Specifies whether the structure, common or
		/// both models should be created.</param>
		/// <param name="language">Specifies the language, for which the entries
		/// of the GSD should be extracted.</param>
		/// <param name="sstore">Model store for the structure data object model.</param>
		/// <param name="cstore">Model store for the common data object model.</param>
		/// <param name="languageStore">language store for the common data object model.</param>
		/// <returns>True, if building was successful, else false.</returns>
        public abstract bool BuildModels(Stream gsddocstream, string pathname, GSDI.ModelOptions lModelOption,
            string language, ref ModelStore sstore, ref ModelStore cstore, ref ArrayList languageStore);
	}
}


