/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigException.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Globalization;
using System.Runtime.Serialization;

#endregion

namespace PNConfigLib.HWCNBL.Basics
{
    /// <summary>
    /// The base class for all Config exceptions
    /// </summary>
    [Serializable]
    public class ConfigException : Exception
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Default constructor for the ConfigException.
        /// </summary>
        public ConfigException()
        {
        }

        /// <summary>
        /// Constructor initializes an instance of the CoreException class with serialized data.
        /// </summary>
        /// <param name="serializationInfo">Serialized object data about the exception</param>
        /// <param name="streamingContext">Contextual information about the exception</param>
        protected ConfigException(SerializationInfo serializationInfo, StreamingContext streamingContext)
            : base(serializationInfo, streamingContext)
        {
        }

        /// <summary>
        /// Constructor initializes an instance with a message for human being.
        /// </summary>
        /// <param name="message">Exception message</param>
        public ConfigException(string message) : base(message)
        {
        }

        /// <summary>
        /// Constructor initializes an instance with an inner Exception.
        /// </summary>
        /// <param name="message">Error message explaining the exception</param>
        /// <param name="innerException">Inner exception - the cause of the current exception</param>
        public ConfigException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Constructor initializes an instance of the CoreException class with a possibity
        /// to specifiy a message and an argument.
        /// </summary>
        /// <param name="message">Error message explaining the exception</param>
        /// <param name="argument">Argument of the error message</param>
        public ConfigException(string message, object argument) : base(
            string.Format(CultureInfo.InvariantCulture, message, argument))
        {
        }

        #endregion
    }
}