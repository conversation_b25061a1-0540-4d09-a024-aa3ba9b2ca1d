using System.Collections.Generic;
using System.Threading.Tasks;

namespace PNConfigTool.Services
{
    /// <summary>
    /// GSDML文件服务接口
    /// </summary>
    public interface IGSDMLService
    {
        /// <summary>
        /// 获取所有已导入的GSDML文件路径
        /// </summary>
        /// <returns>GSDML文件路径列表</returns>
        List<string> GetGSDMLFiles();

        /// <summary>
        /// 获取所有已导入的GSDML文件路径（异步版本）
        /// </summary>
        /// <returns>GSDML文件路径列表</returns>
        Task<List<string>> GetGSDMLFilesAsync();

        /// <summary>
        /// 导入GSDML文件
        /// </summary>
        /// <param name="filePath">GSDML文件路径</param>
        /// <returns>导入结果，成功返回true，失败返回false</returns>
        Task<bool> ImportGSDMLFile(string filePath);

        /// <summary>
        /// 批量导入GSDML文件
        /// </summary>
        /// <param name="filePaths">GSDML文件路径列表</param>
        /// <returns>每个文件的导入结果</returns>
        Task<Dictionary<string, bool>> ImportGSDMLFiles(IEnumerable<string> filePaths);

        /// <summary>
        /// 删除GSDML文件
        /// </summary>
        /// <param name="filePath">GSDML文件路径</param>
        /// <returns>删除结果，成功返回true，失败返回false</returns>
        Task<bool> RemoveGSDMLFile(string filePath);

        /// <summary>
        /// 批量删除GSDML文件
        /// </summary>
        /// <param name="filePaths">GSDML文件路径列表</param>
        /// <returns>每个文件的删除结果</returns>
        Task<Dictionary<string, bool>> RemoveGSDMLFiles(IEnumerable<string> filePaths);

        /// <summary>
        /// 获取可用设备列表
        /// </summary>
        /// <returns>设备列表</returns>
        Task<List<string>> GetAvailableDevices();

        /// <summary>
        /// 获取GSDML目录路径
        /// </summary>
        /// <returns>GSDML目录路径</returns>
        string GetGSDMLDirectory();

        /// <summary>
        /// 设置自定义GSDML目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        void SetCustomGSDMLDirectory(string directoryPath);

        /// <summary>
        /// 清除所有缓存
        /// </summary>
        void ClearAllCaches();

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息元组</returns>
        (int FileListCacheCount, int FileExistsCacheCount, int GSDMLCacheCount, long GSDMLMemoryEstimate) GetCacheStatistics();
    }
}