/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyCheckerUtilities.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.IO;
using System.Text.RegularExpressions;

using PNConfigLib.ConfigReader;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.Consistency.Checker
{
    internal static class ConsistencyCheckerUtilities
    {
        internal static bool IsConfigurationFileExist(string configXmlPath)
        {
            if (string.IsNullOrEmpty(configXmlPath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    string.Empty,
                    ConsistencyConstants.XML_NullOrEmptyConfigXML);
                return false;
            }
            if (!File.Exists(configXmlPath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotExistConfigXML);
                return false;
            }
            return true;
        }

        internal static void CheckInvalidDeviceName(
            string deviceName,
            IConfigInterface deviceInterface,
            PNNameOfStationConverter.DnsNameConvertStatus convertStatus)
        {
            string deviceNamePortRegex =
                @"^(port-[0-9][0-9][0-9]$|port-[0-9][0-9][0-9]\.|port-[0-9][0-9][0-9]-[0-9][0-9][0-9][0-9][0-9]$|port-[0-9][0-9][0-9]-[0-9][0-9][0-9][0-9][0-9]\.)";

            Match portMatch = Regex.Match(deviceName, deviceNamePortRegex);

            if (portMatch.Success)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    deviceInterface,
                    ConsistencyConstants.XML_InvalidNamePort,
                    deviceName,
                    deviceInterface.InterfaceRefID);
            }

            string deviceNameIpFormatRegex = @"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b";
            Match ipFormatMatch = Regex.Match(deviceName, deviceNameIpFormatRegex);
            if (ipFormatMatch.Success)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    deviceInterface,
                    ConsistencyConstants.XML_InvalidNameIpFormat,
                    deviceName,
                    deviceInterface.InterfaceRefID);
            }

            switch (convertStatus)
            {
                case PNNameOfStationConverter.DnsNameConvertStatus.CpuNameLengthExceeded:
                case PNNameOfStationConverter.DnsNameConvertStatus.IOSystemNameLengthExceeded:
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        deviceInterface,
                        ConsistencyConstants.XML_DnsConvert_PNDeviceNameLengthExceeded,
                        deviceName,
                        deviceInterface.InterfaceRefID);
                    break;
            }
        }    
    }
}
