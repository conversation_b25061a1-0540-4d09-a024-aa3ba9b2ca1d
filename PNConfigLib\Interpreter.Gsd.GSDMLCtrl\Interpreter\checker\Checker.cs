/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Checker.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Runtime.InteropServices;
using System.Reflection;
using System.Collections;
using System.Collections.Specialized;
using System.Globalization;
using System.IO;
using System.Xml;
using System.Xml.XPath;
using System.Xml.Xsl;
using GSDI;
using System.Text;
using System.Diagnostics;
using System.Xml.Linq;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// The Checker is the most importent entry point for all checking actions
    /// for GSD(ML) files.
    /// It supplies functionality to check the GSD file and get some usefull 
    /// information about the result(s) of the check. The results will be offered
    /// as report objects, which can also be browsed and exported. 
    /// </summary>
    public class Checker :
        GSDI.IChecker,
        GSDI.ICheckerInfo
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Checker if it is instantiated. This is the default
        /// constructor of the Checker.
        /// </summary>
        public Checker()
        {
            // Initialize the properties
            SetCheckerVersion(Constants.s_CurrentCheckerVersion);
            SetSupportedGsdmlVersion(Constants.s_CurrentGsdmlVersion);

            // Register the required encoding provider
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            m_GsdPathName = String.Empty;
            m_GsdName = String.Empty;
            m_SchemaPath = String.Empty;

			m_ReportStore = null;
			m_UseExternalSchemaPath = false;

		}

        #endregion

        //########################################################################################
        #region Fields

        private string m_CheckerVersion;
        private string m_SupportedGsdmlVersion;
        private string m_GsdName;
        private string m_GsdPathName;
        private ReportStore m_ReportStore;
        private string m_SchemaPath;
        private bool m_UseExternalSchemaPath = true;
        private GSDI.SignatureCheckResults m_SignatureCheckingResult;

        #endregion

        //########################################################################################
        #region Properties

        protected virtual string GsdPathName
        {
            get => this.m_GsdPathName;
            set => this.m_GsdPathName = value;
        }

        internal virtual ReportStore Store
        {
            get => this.m_ReportStore;
            set => this.m_ReportStore = value;
        }

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Sets the version of the Checker
        /// </summary>
        /// <param name="version">Version of the Checker</param>
        /// <returns></returns>
        protected void SetCheckerVersion(string version) { this.m_CheckerVersion = version; }

        /// <summary>
        /// Sets the supported GSDML version
        /// </summary>
        /// <param name="version">Supported GSDML version</param>
        /// <returns></returns>
        protected void SetSupportedGsdmlVersion(string version) { this.m_SupportedGsdmlVersion = version; }

        /// <summary>
        /// Sets the GSD name
        /// </summary>
        /// <param name="name">GSD name</param>
        /// <returns></returns>
        protected virtual void SetActualGsdName(string name) { this.m_GsdName = name; }

        /// <summary>
        /// Sets the signature checking result
        /// </summary>
        /// <param name="name">Signature checking result</param>
        /// <returns></returns>
        virtual protected void SetSignatureCheckingResult(GSDI.SignatureCheckResults result)
        { 
            this.m_SignatureCheckingResult = result; 
        }

        #endregion

        //########################################################################################
        #region IChecker Members


        /// <summary>
        /// Checks the specified GSD(ML) and reports problems.
        /// </summary>
        /// <param name="bstrPathName">The full path name of the GSD to check.</param>
        /// <param name="schemaPath">Path to GSDML schema</param>
        /// <returns>True, if the check was successful, else false.</returns>
        public bool CheckGsd(Stream gsddocstream, string name, string path, string gsdmlversion)
        {
            bool succeeded;

            try
            {
                // Search and get the GSD(ML) file!
                if (null == gsddocstream)
                    throw new ArgumentException("Bad input parameter for 'gsddocstream'!");

                if (String.IsNullOrEmpty(name))
                    throw new ArgumentException("Bad input parameter for 'name'!");

                if (String.IsNullOrEmpty(path))
                    throw new ArgumentException("Bad input parameter for 'path'!");

                // NOTE: If gsdmlversion isn't given, extract version from the given name. If name
                // isn't given also, the gsdml version is set to the actual supported gsdml version!
                string version;
                if (String.IsNullOrEmpty(gsdmlversion))
                {

                    // Get GSDML version of GSD from file name.
                    // NOTE: Example GSD name = 'GSDML-V1.0-Siemens-PNIO_Example-20030708.xml'
                    string s = name.Substring(name.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal) + 1);
                    version = (s.Substring(0, s.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal))).ToUpperInvariant();
                }
                else
                    version = gsdmlversion;

                // Call internal assignment method.
                succeeded = this.InternalCheck(gsddocstream, name, path, version, null);


            }
            catch (CheckerException e)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Finding of the needed internal checker and making checks. After that, report occurring 
        /// problems.
        /// </summary>
        /// <param name="gsddocstream">Stream of the GSD document to check.</param>
        /// <param name="name">The name of the given GSD.</param>
        /// <param name="path">The path of the given GSD.</param>
        /// <param name="gsdmlversion">GSDML version of the document contained in the stream.</param>
        /// <param name="customArguments">Custom arguments</param>
        /// <returns>True, if the check was successful, else false.</returns>
        private bool InternalCheck(Stream gsddocstream, string name, string path, string gsdmlversion, NameValueCollection customArguments)
        {
            bool succeeded = true;
            Stream docstream = gsddocstream;
            string pathname = Path.Combine(path, name);
            string version = gsdmlversion;
            ReportStore store = new ReportStore();
			GSDI.SignatureCheckResults signatureCheckingResult = SignatureCheckResults.GSDNoSignature;

            Help.InitResources(this.GetType().Assembly);

            try
            {
                // Get needed internal checker object.
                CheckerObject checker = this.GetInternalChecker(version, store);
                if (null == checker)
                    succeeded = false;

                FileInfo fileinfo = new(pathname);
                long docstreamLength = 0;
                if (docstream != null)
                {
                    docstreamLength = docstream.Length;
                }

                // GSDI shall reject to open GSDML files larger then 50 MiB
                if (fileinfo.Length >= 52428800 || docstreamLength >= 52428800)
                {
                    // "The GSD file ("{0}") is larger then 50 MiB!
                    //  Please note that the mentioned GSD file is not checked."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00001002_4"), pathname);
                    store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, 0, 0, msg, null,
                        ReportCategories.General, "0x00001002_4");
                    succeeded = false;
                }

                // Check given gsd.
                if (succeeded)
                {
                    if (m_UseExternalSchemaPath)
                    {
                        succeeded = checker.Check(docstream, name, m_SchemaPath, store);
                    }
                    else
                    {
                        succeeded = checker.Check(docstream, name, store, customArguments);
                    }
                }

                // Check result.
                if (!succeeded)
                {
                    //throw new Exception("Checker couldn't check Gsd file ('" + pathname + "') correctly!");
                    string msg = string.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00001002_1"), pathname);
                    store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, 0, 0, msg, null,
                        ReportCategories.General, "0x00001002_1");
                }

            }
            catch (CheckerException)
            {
                string msg = string.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00001002_2"), pathname);
                store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, 0, 0, msg, null,
                    ReportCategories.General, "0x00001002_2");
                succeeded = false;
            }

            // Set the signature checking result
            SetSignatureCheckingResult(signatureCheckingResult);
				
            // Assign internal variables.
            this.SetActualGsdName(name);
            this.GsdPathName = pathname;
            this.Store = store;

            // Check result.
            if (!succeeded)
            {
                return false;		// ---------->
            }

            // Gsd file contains errors.
            if (store.ErrorFlag)
                return false;       // ---------->

            return true;
        }

        /// <summary>
        /// Searches the correct internal checker for the given version.
        /// </summary>
        /// <remarks>There is always a internal checker found for making
        /// the check.</remarks>
        /// <param name="version">Version of the GSD file, which should be
        /// checked, and therefore for which the best internal checker
        /// should be searched.</param>
        /// <param name="store">Store object which holds eventually created reports.</param>
        /// <returns>The internal checker, which matches the version best.</returns>
        internal virtual CheckerObject GetInternalChecker(string version, ReportStore store)
        {
            // CHECKER VERSION "Vx.x" -----------------------------
            //checkerversion = "Vx.x";
            // NOTE: Place for adding queries for newer versions!!!

            try
            {
                Help.InitResources(this.GetType().Assembly);

            // CHECKER VERSION "V2.45" -----------------------------
            string checkerversion = Constants.s_Version245;
            CheckerObject checker;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02045();

                // Check whether this is the exactly needed checker!
                if (version != checkerversion)
                {
                    // "Checker, which supports GSD(ML) version "{0}", is used for GSD(ML) with version "{1}".";
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00001001_1"), checker.SupportedGsdmlVersion, version);
                    store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, 0, 0, msg, "", ReportCategories.General, "0x00001001_1");
                }

                return checker; // ------------>
            }
            // CHECKER VERSION "V2.44" -----------------------------
            checkerversion = Constants.s_Version244;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02044();
                return checker; // ------------>
            }

            checkerversion = Constants.s_Version243;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02043();
                return checker;	// ------------>
            }

            checkerversion = Constants.s_Version242;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02042();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.41" -----------------------------
            checkerversion = Constants.s_Version241;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02041();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.4" -----------------------------
            checkerversion = Constants.s_Version24;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0204();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.35" -----------------------------
            checkerversion = Constants.s_Version235;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02035();
                return checker; // ------------>
            }
            checkerversion = Constants.s_Version234;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02034();
                return checker; // ------------>
            }

            checkerversion = Constants.s_Version233;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02033();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.32" -----------------------------
            checkerversion = Constants.s_Version232;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02032();
                return checker;	// ------------>
            }
            // CHECKER VERSION "V2.31" -----------------------------
            checkerversion = Constants.s_Version231;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02031();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.3" -----------------------------
            checkerversion = Constants.s_Version23;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0203();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.25" -----------------------------
            checkerversion = Constants.s_Version225;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV02025();
                return checker;	// ------------>
            }

            // CHECKER VERSION "V2.2" -----------------------------
            checkerversion = Constants.s_Version22;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0202();
                return checker; // ------------>
            }

            // CHECKER VERSION "V2.1" -----------------------------
            checkerversion = Constants.s_Version21;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0201();
                return checker; // ------------>
            }

            // CHECKER VERSION "V2.0" -----------------------------
            checkerversion = Constants.s_Version20;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0200();
                return checker; // ------------>
            }

            // CHECKER VERSION "V1.0" -----------------------------
            checkerversion = Constants.s_Version10;
            if (Help.IsHigherOrEqualVersion(version, checkerversion))
            {
                checker = new CheckerV0100();

                return checker; // ------------>
            }
            }
            catch (ArgumentException e)
            {
                // "Could not find the appropriate checker version. Please note that the GSD file is not checked."
                string msg = Help.GetMessageString("M_0x00001002_3");
                store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, 0, 0, msg, null,
                    ReportCategories.General, "0x00001002_3");
                return null;
            }
            return null;
        }
        #endregion
        #region IChecker Members

        /// <summary>
        /// Accesses the name of the actual (last) tested GSD.
        /// </summary>
        public string ActualGsdName => this.m_GsdName;

        /// <summary>
		/// Accesses the number of available error reports.
		/// </summary>
		public int NumberOfErrorReports
        {
            get
            {
                if (null != this.Store)
                {
                    if (null != this.Store.ErrorReports)
                        return this.Store.ErrorReports.Length;
                }
                return 0;
            }
        }

        /// <summary>
        /// Accesses the number of available minor error reports.
        /// </summary>
        public int NumberOfMinorErrorReports
        {
            get
            {
                if (null != this.Store)
                {
                    if (null != this.Store.MinorErrorReports)
                        return this.Store.MinorErrorReports.Length;
                }
                return 0;
            }
        }

        /// <summary>
        /// Accesses the number of available warning reports.
        /// </summary>
        public int NumberOfWarningReports
        {
            get
            {
                if (null != this.Store)
                {
                    if (null != this.Store.WarningReports)
                        return this.Store.WarningReports.Length;
                }
                return 0;
            }
        }

        /// <summary>
        /// Accesses the number of available info reports.
        /// </summary>
        public int NumberOfInfoReports
        {
            get
            {
                if (null != this.Store)
                {
                    if (null != this.Store.InfoReports)
                        return this.Store.InfoReports.Length;
                }
                return 0;
            }
        }

        /// <summary>
        /// Checks the specified GSD(ML) and reports problems.
        /// </summary>
        /// <param name="bstrPathName">The full path name of the GSD to check.</param>
        /// <returns>True, if the check was successful, else false.</returns>
        public bool CheckGsd(string bstrPathName)
        {
            return CheckGsd(bstrPathName, (NameValueCollection)null);
        }


        /// <summary>
        /// Checks the specified GSD(ML) and reports problems.
        /// </summary>
        /// <param name="bstrPathName">The full path name of the GSD to check.</param>
        /// <param name="customArguments">Custom Arguments</param>
        /// <returns>True, if the check was successful, else false.</returns>
        public bool CheckGsd(string bstrPathName, NameValueCollection customArguments)
        {
            bool succeeded;
            string pathname = bstrPathName;
            string gsdmlversion = String.Empty;
            FileStream filestream = null;

            try
            {
                // Search and get the GSD(ML) file!
                string gsdname;
                string gsdpath;
                if (File.Exists(pathname))
                {
                    FileInfo fileinfo = new(pathname);
                    gsdname = fileinfo.Name;
                    gsdpath = fileinfo.DirectoryName;

                    // GSDI shall reject to open GSDML files larger then 50 MiB
                    if (fileinfo.Length < 52428800)
                    {
                        filestream = new FileStream(pathname, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    }
                }
                else
                    throw new ArgumentException("Can't find Gsd file with the given path: '" + pathname + "'!");

                // Check the rough structure of Gsd name. It must have (at least) 4 hyphen.
                char[] cGsdname = gsdname.ToCharArray();
                int numberOfHyphen = 0;
                for (int i = 0; i < cGsdname.Length; i++)
                    if (cGsdname[i] == '-')
                        numberOfHyphen++;


                // Get GSDML version of GSD.
                // NOTE: Example Gsd name = 'GSDML-V1.0-Siemens-PNIO_Example-20030708.xml'
                if (numberOfHyphen > 0)
                {
                    string s = (gsdname.Substring(gsdname.IndexOf(Constants.s_Hyphen, StringComparison.InvariantCulture) + 1)).ToUpperInvariant();
                    int endOfVersion = s.IndexOf(Constants.s_Hyphen, StringComparison.InvariantCulture);
                    if (endOfVersion == -1) // GSD name is very short ('GSDML-V1.0.xml') or misshapen ('GSDML-V1.0SiemensPNIO_Example20030708.xml')
                        endOfVersion = s.IndexOf(".XML", StringComparison.InvariantCulture);
                    gsdmlversion = s.Substring(0, endOfVersion);
                }

                // Call internal assignment method.
                succeeded = this.InternalCheck(filestream, gsdname, gsdpath, gsdmlversion, customArguments);

                // Write reports to trace.

            }
            catch (CheckerException e)
            {
                succeeded = false;
            }
            finally
            {
                if (null != filestream)
                    filestream.Close();
            }

            return succeeded;
        }

        /// <summary>
        /// Checks the specified GSD(ML) and reports problems.
        /// </summary>
        /// <param name="bstrPathName">The full path name of the GSD to check.</param>
        /// <param name="schemaPath">Path to GSDML schema</param>
        /// <returns>True, if the check was successful, else false.</returns>
        public bool CheckGsd(string bstrPathName, string schemaPath)
        {
            bool succeeded;
            string pathname = bstrPathName;
            FileStream filestream = null;
            m_UseExternalSchemaPath = true;

            try
            {
                // Search and get the GSD(ML) file!
                string gsdpath;
                string gsdname;
                if (File.Exists(pathname))
                {
                    FileInfo fileinfo = new(pathname);
                    gsdname = fileinfo.Name;
                    gsdpath = fileinfo.DirectoryName;

                    // GSDI shall reject to open GSDML files larger then 50 MiB
                    if (fileinfo.Length < 52428800)
                    {
                        filestream = new FileStream(pathname, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    }
                }
                else
                    throw new FileNotFoundException("Can't find Gsd file with the given path: '" + pathname + "'!");

                if (Directory.Exists(schemaPath))
                {
                    m_SchemaPath = schemaPath;
                }
                else
                    throw new FileNotFoundException("Can't find  path: '" + schemaPath + "'!");

                // Get GSDML version of GSD.
                // NOTE: Example Gsd name = 'GSDML-V1.0-Siemens-PNIO_Example-20030708.xml'
                string s = gsdname.Substring(gsdname.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal) + 1);
                string gsdmlversion = (s.Substring(0, s.IndexOf(Constants.s_Hyphen, StringComparison.Ordinal))).ToUpperInvariant();

                // Call internal assignment method.
                succeeded = this.InternalCheck(filestream, gsdname, gsdpath, gsdmlversion, null);

                // Write reports to trace.

            }
            catch (CheckerException e)
            {
                succeeded = false;
            }
            catch (IOException)
            {
                succeeded = false;
            }
            finally
            {
                if (null != filestream)
                    filestream.Close();
            }

            return succeeded;
        }
        /// <summary>
        /// Checks whether reports of the given type are available or not.
        /// </summary>
        /// <param name="lReportType">Type of the reports, which should be getted.</param>
        /// <returns>True, if any available, else false.</returns>
        public bool HasReports(GSDI.ReportTypes lReportType)
        {
            // Check store availability.
            if (null == this.Store)
                return false;   // ---------->

            bool ret = false;
            switch (lReportType)
            {
                case GSDI.ReportTypes.GsdrtAll:
                    {
                        ret = this.Store.AllFlag;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Error:
                    {
                        ret = this.Store.ErrorFlag;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_MinorError:
                    {
                        ret = this.Store.MinorErrorFlag;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Warning:
                    {
                        ret = this.Store.WarningFlag;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Info:
                    {
                        ret = this.Store.InfoFlag;
                        break;
                    }
            }
            return ret;
        }

        /// <summary>
        /// Searches in the report store, if any reports of the given type are available
        /// and returns the found reports.
        /// </summary>
        /// <param name="lReportType">Type of the reports, which should be getted.</param>
        /// <returns>A list with report objects, if any exist, else null.</returns>
        public Array GetReports(GSDI.ReportTypes lReportType)
        {
            // Check store availability.
            if (null == this.Store)
                return null;    // ---------->

            Array ret = null;
            switch (lReportType)
            {
                case GSDI.ReportTypes.GsdrtAll:
                    {
                        ret = this.Store.AllReports;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Error:
                    {
                        ret = this.Store.ErrorReports;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_MinorError:
                    {
                        ret = this.Store.MinorErrorReports;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Warning:
                    {
                        ret = this.Store.WarningReports;
                        break;
                    }
                case GSDI.ReportTypes.GSD_RT_Info:
                    {
                        ret = this.Store.InfoReports;
                        break;
                    }
            }
            return ret;
        }

        /// <summary>
		/// Available reports are serialized to a fixed XML structure and returned as string.
		/// Only these reports are exported, which match the given type.
		/// </summary>
		/// <param name="lReportType">Type of the reports, which should be exported.</param>
		/// <returns>An empty string, if export didn't succeed, else the exported XML data.</returns>
		public string ExportReports(GSDI.ReportTypes lReportType)
        {
            XmlTextWriter writer = null;
            StreamReader reader = null;

            try
            {
                // Create xml writer object and serialize the store.
                // NOTE: Encoding is UTF-8 for default. If another encoding is wanted, it must be explicitely set.
                MemoryStream mstream = new();
                BufferedStream bstream = new(mstream, 1000);
                writer = new XmlTextWriter(bstream, null);

                // Set formatting options.
                if (Settings.Indentation)
                {
                    writer.Formatting = Formatting.Indented;
                    writer.Indentation = Export.s_Indentation;
                    writer.IndentChar = Export.s_IndentationCharacter;
                }

                // Start a new xml document.
                writer.WriteStartDocument();
                writer.WriteStartElement(Export.s_ElementReportExport);

                // Call internal serialize.
                if (null != this.Store)
                {
                    bool succeeded = this.Store.Serialize(lReportType, ref writer);
                    if (!succeeded)
                        throw new SerializationException("Couldn't export reports: '" + lReportType + "'!");
                }

                writer.WriteEndElement();
                writer.WriteEndDocument();
                writer.Flush();

                // Read stream and write to string.
                reader = new StreamReader(writer.BaseStream);
                reader.BaseStream.Position = 0;
                return reader.ReadToEnd();  // ---------->

            }
            catch (SerializationException e)
            {
            }
            catch (IOException e)
            {
            }
            catch (ArgumentException e)
            {
            }
            catch (OutOfMemoryException e)
            {
            }
            catch (InvalidOperationException e)
            {
            }
            finally
            {
                // Close assigned memory.
                if (null != writer)
                    writer.Close();

                if (null != reader)
                    reader.Close();
            }

            return String.Empty;
        }

        /// <summary>
        /// Available reports are serialized to a fixed XML structure and written to the
        /// specified file. Only this reports are exported, which match the given type.
        /// </summary>
        /// <param name="bstrPathName">Name of the file to write the export result.</param>
        /// <param name="lReportType">Type of the reports, which should be exported.</param>
        /// <returns>True, if export was successfull, else false.</returns>
        public bool ExportReportsToFile(string bstrPathName, GSDI.ReportTypes lReportType)
        {
            bool succeeded = true;
            XmlTextWriter writer = null;

            try
            {
                // Check whether file exists.
                //if (System.IO.File.Exists(bstrPathName))
                //	throw new ArgumentException("Couldn't export to existing file '" + bstrPathName + "'!", "bstrPathName");

                // Create xml writer object and serialize the stores to file.
                // TODO: evtl. encoding must be unicode.
                writer = new XmlTextWriter(bstrPathName, null);

                // Set formatting options.
                if (Settings.Indentation)
                {
                    writer.Formatting = Formatting.Indented;
                    writer.Indentation = Export.s_Indentation;
                    writer.IndentChar = Export.s_IndentationCharacter;
                }

                // Start a new xml document.
                writer.WriteStartDocument();
                writer.WriteStartElement(Export.s_ElementReportExport);

                // Call internal serialize.
                if (null != this.Store)
                {
                    succeeded = this.Store.Serialize(lReportType, ref writer);
                    if (!succeeded)
                        throw new SerializationException("Couldn't export report(s) to file: '" + bstrPathName + "'!");
                }

                writer.WriteEndElement();
                writer.WriteEndDocument();
                writer.Flush();

            }
            catch (ArgumentException e)
            {
                succeeded = false;
            }
            catch (SerializationException e)
            {
                succeeded = false;
            }
            catch (InvalidOperationException e)
            {
                succeeded = false;
            }
            catch (IOException e)
            {
                succeeded = false;
            }
            catch (System.Security.SecurityException e)
            {
                succeeded = false;
            }
            catch (UnauthorizedAccessException e)
            {
                succeeded = false;
            }
            finally
            {
                // Close assigned file.
                if (null != writer)
                {
                    writer.Close();
                    if (!succeeded)
                        File.Delete(bstrPathName);
                }
            }

            return succeeded;
        }
        #endregion

        //########################################################################################
        #region ICheckerInfo Members

        /// <value>
        /// Accesses the GSDML version, which is supported from the checker.
        /// </value>
        public string SupportedGsdmlVersion => this.m_SupportedGsdmlVersion;

        /// <value>
        /// Accesses the version of the checker itself.
        /// </value>
        public string Version => this.m_CheckerVersion;
        #endregion
    }
}
