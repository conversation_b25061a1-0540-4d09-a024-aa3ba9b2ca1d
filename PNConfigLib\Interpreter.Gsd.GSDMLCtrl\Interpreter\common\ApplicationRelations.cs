/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ApplicationRelations.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// This object contains information about the application relations 
    /// implemented in an IO Device. The version information properties are 
    /// needed to check, if the structure of the connect PDU complies with 
    /// the functionality of an IO Device. 
    /// The engineering tool has to fill in the version information into 
    /// the connect PDU with this properties.
    /// </summary>
    public class ApplicationRelations :
        GsdObject,
        GSDI.IApplicationRelations,
        GSDI.IApplicationRelations2,
        GSDI.IApplicationRelations3,
        GSDI.IApplicationRelations4,
        GSDI.IApplicationRelations5
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ApplicationRelations object if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ApplicationRelations()
        {
            m_ArBlockVersion = 0;
            m_IocrBlockVersion = 0;
            m_AlarmCrBlockVersion = 0;
            m_SubmoduleDataBlockVersion = 0;
            m_NumberOfAdditionalInputCr = 0;
            m_NumberOfAdditionalOutputCr = 0;
            m_NumberOfAdditionalMulticastProviderCr = 0;
            m_NumberOfMulticastConsumerCr = 0;

            m_NumberOfAr = Attributes.s_DefaultNumberOfAr;

      
            m_StartupMode = null;
        }


        #endregion

        //########################################################################################
        #region Fields

        // V1.0
        private uint m_ArBlockVersion;
        private uint m_IocrBlockVersion;
        private uint m_AlarmCrBlockVersion;
        private uint m_SubmoduleDataBlockVersion;
        private TimingProperties m_TimingProperties;

        // V2.0
        private TimingProperties m_RTClass3TimingProperties;
        private uint m_NumberOfAdditionalInputCr;
        private uint m_NumberOfAdditionalOutputCr;
        private uint m_NumberOfAdditionalMulticastProviderCr;
        private uint m_NumberOfMulticastConsumerCr;

        // V2.2
        private bool m_PullModuleAlarmSupported;

        // V2.3
        private uint m_NumberOfAr;

      
        private ArrayList m_StartupMode;
        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the version information about the AR block.
        /// </summary>
        public UInt32 ArBlockVersion => this.m_ArBlockVersion;

        /// <summary>
        /// Accesses Contains the version information about the IO CR block.
        /// </summary>
        public UInt32 IocrBlockVersion => this.m_IocrBlockVersion;

        /// <summary>
        /// Accesses the version information about the alarm block.
        /// </summary>
        public UInt32 AlarmCRBlockVersion => this.m_AlarmCrBlockVersion;

        /// <summary>
        /// Accesses the version information about the submodule data block.
        /// </summary>
        public UInt32 SubmoduleDataBlockVersion => this.m_SubmoduleDataBlockVersion;

        /// <summary>
        /// Accesses the TimingProperties object, which defines the timing 
        /// behaviour for sending cyclic IO data.
        /// </summary>
        public virtual TimingProperties TimingProperties => this.m_TimingProperties;

        /// <summary>
        /// ...
        /// </summary>
        public virtual TimingProperties RTClass3TimingProperties => this.m_RTClass3TimingProperties;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 NumberOfAdditionalInputCR => this.m_NumberOfAdditionalInputCr;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 NumberOfAdditionalOutputCR => this.m_NumberOfAdditionalOutputCr;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 NumberOfAdditionalMulticastProviderCR => this.m_NumberOfAdditionalMulticastProviderCr;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 NumberOfMulticastConsumerCR => this.m_NumberOfMulticastConsumerCr;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsPullModuleAlarmSupported => this.m_PullModuleAlarmSupported;

        #region IApplicationRelations4 Members

        public UInt32 NumberOfAr => this.m_NumberOfAr;

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array StartupMode =>
            m_StartupMode?.ToArray();



        #endregion

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            // Check parameter
            if (null == hash)
                throw new FillException("The input hashtable parameter couldn't be 'null'!");
            // Own data.
            FillFieldARBlockVersion(hash);
            FillFieldIOCRBlockVersion(hash);
            FillFieldAlarmCRBlockVersion(hash);
            FillFieldSubmoduleDataBlockVersion(hash);
            FillFieldTimingProperties(hash);

            // V2.0
            FillFieldRTClass3TimingProperties(hash);
            FillFieldNumberOfAdditionalInputCR(hash);
            FillFieldNumberOfAdditionalOutputCR(hash);
            FillFieldNumberOfAdditionalMulticastProviderCR(hash);
            FillFieldNumberOfMulticastConsumerCR(hash);

            // V2.2
            FillFieldPullModuleAlarmSupported(hash);

            // V2.3
            FillFieldNumberOfAR(hash);
            FillFieldStartupMode(hash);


            // Base data.
            //succeeded = base.Fill(hash);


            return true;
        }

        private void FillFieldStartupMode(Hashtable hash)
        {
            string member = Models.s_FieldStartupMode;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_StartupMode = hash[member] as ArrayList;
        }

        private void FillFieldNumberOfAR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfAr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_NumberOfAr = (uint)hash[member];
        }

        private void FillFieldPullModuleAlarmSupported(Hashtable hash)
        {
            string member = Models.s_FieldPullModuleAlarmSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_PullModuleAlarmSupported = (bool)hash[member];
        }

        private void FillFieldNumberOfMulticastConsumerCR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfMulticastConsumerCr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_NumberOfMulticastConsumerCr = (uint)hash[member];
        }

        private void FillFieldNumberOfAdditionalMulticastProviderCR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfAdditionalMulticastProviderCr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_NumberOfAdditionalMulticastProviderCr = (uint)hash[member];
        }

        private void FillFieldNumberOfAdditionalOutputCR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfAdditionalOutputCr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_NumberOfAdditionalOutputCr = (uint)hash[member];
        }

        private void FillFieldNumberOfAdditionalInputCR(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfAdditionalInputCr;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_NumberOfAdditionalInputCr = (uint)hash[member];
        }

        private void FillFieldRTClass3TimingProperties(Hashtable hash)
        {
            string member = Models.s_FieldRtClass3TimingProperties;
            if (hash.ContainsKey(member)
                && hash[member] is TimingProperties)
                m_RTClass3TimingProperties = hash[member] as TimingProperties;
        }

        private void FillFieldTimingProperties(Hashtable hash)
        {
            string member = Models.s_FieldTimingProperties;
            if (hash.ContainsKey(member)
                && hash[member] is TimingProperties)
                m_TimingProperties = hash[member] as TimingProperties;
        }

        private void FillFieldSubmoduleDataBlockVersion(Hashtable hash)
        {
            string member = Models.s_FieldSubmoduleDataBlockVersion;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_SubmoduleDataBlockVersion = (uint)hash[member];
        }

        private void FillFieldAlarmCRBlockVersion(Hashtable hash)
        {
            string member = Models.s_FieldAlarmCrBlockVersion;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_AlarmCrBlockVersion = (uint)hash[member];
        }

        private void FillFieldIOCRBlockVersion(Hashtable hash)
        {
            string member = Models.s_FieldIocrBlockVersion;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
            m_IocrBlockVersion = (uint)hash[member];
        }

        private void FillFieldARBlockVersion(Hashtable hash)
        {
            string member = Models.s_FieldArBlockVersion;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_ArBlockVersion = (uint)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectApplicationRelations);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldArBlockVersion, this.m_ArBlockVersion);
            Export.WriteUint32Property(ref writer, Models.s_FieldIocrBlockVersion, this.m_IocrBlockVersion);
            Export.WriteUint32Property(ref writer, Models.s_FieldAlarmCrBlockVersion, this.m_AlarmCrBlockVersion);
            Export.WriteUint32Property(ref writer, Models.s_FieldSubmoduleDataBlockVersion, this.m_SubmoduleDataBlockVersion);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldTimingProperties, this.m_TimingProperties);

            // V2.0
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldRtClass3TimingProperties, this.m_RTClass3TimingProperties);
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfAdditionalInputCr, this.m_NumberOfAdditionalInputCr);
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfAdditionalOutputCr, this.m_NumberOfAdditionalOutputCr);
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfAdditionalMulticastProviderCr, this.m_NumberOfAdditionalMulticastProviderCr);
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfMulticastConsumerCr, this.m_NumberOfMulticastConsumerCr);

            // V2.2
            Export.WriteBooleanProperty(ref writer, Models.s_FieldPullModuleAlarmSupported, this.m_PullModuleAlarmSupported);

            // V2.3
            Export.WriteUint32Property(ref writer, Models.s_FieldNumberOfAr, this.m_NumberOfAr);


            return true;
        }

        #endregion
    }
}
