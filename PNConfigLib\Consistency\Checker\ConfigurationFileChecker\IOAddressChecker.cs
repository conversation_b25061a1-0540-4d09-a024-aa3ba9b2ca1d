/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: IOAddressChecker.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

using PNConfigLib.BusinessLogic.HWCNBL;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.Importer.GSDImport.Helper;


namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class IOAddressChecker : IConsistencyChecker
    {
        internal const uint s_MaximumIOAddress = 32767;

        private readonly Configuration m_Configuration;

        private readonly ConfigReader.ListOfNodes.ListOfNodes m_ListOfNodes;

        private Dictionary<string, string> centralDeviceIOSystem;

        private IOAdressSettings IOAddressSettings;

        internal IOAddressChecker(Configuration configuration, ConfigReader.ListOfNodes.ListOfNodes lon)
        {
            this.m_Configuration = configuration;
            this.m_ListOfNodes = lon;
            this.centralDeviceIOSystem = new Dictionary<string, string>();
            this.IOAddressSettings = new IOAdressSettings();
        }

        public void Check()
        {
            IsIOAddressDefinitionValid();
            AreTypeOfIOAddressesValid();
        }

        private void AreTypeOfIOAddressesValid()
        {
            List<string> ioSystems = new List<string>();
            m_Configuration.Subnet.ForEach(w => w.IOSystem.ForEach(d => ioSystems.Add(d.IOSystemID)));
            
            m_Configuration.Devices.CentralDevice.ForEach(
                w => centralDeviceIOSystem.Add(
                    w.DeviceRefID,
                    w.CentralDeviceInterface.EthernetAddresses.IOSystemRefID));

            IOAddressSettings.initialize(ioSystems);

            foreach (ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice in m_ListOfNodes.DecentralDevice)
            {
                DecentralDeviceType decentralDevice =
                    m_Configuration.Devices.DecentralDevice.SingleOrDefault(
                        e => e.DeviceRefID == lonDecentralDevice.DeviceID);

                if (decentralDevice == null
                    || ((decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID == null)
                        && (decentralDevice.SharedDevice.Count <= 0)))
                {
                    continue;
                }

                CheckIOAddressForDecentralDevice(decentralDevice, lonDecentralDevice);
                
            }

            CheckIOAddressExceeded(new InputOutputAddressMap(IOAddressSettings.StartIOAddressesForInput));
            CheckIOAddressExceeded(new InputOutputAddressMap(IOAddressSettings.EndIOAddressesForInput));
            CheckIOAddressExceeded(new InputOutputAddressMap(IOAddressSettings.StartIOAddressesForOutput));
            CheckIOAddressExceeded(new InputOutputAddressMap(IOAddressSettings.EndIOAddressesForOutput));

            foreach (var ioSystem in ioSystems)
            {
                List<uint> ioAddressCollisions = CheckConsistencyFromIOAddress(
                   IOAddressSettings.StartIOAddressesForInput[ioSystem],
                   IOAddressSettings.EndIOAddressesForInput[ioSystem]);
                CheckCollisionExistForIOAddress(ioSystem, ioAddressCollisions, IoTypes.Input);

                ioAddressCollisions = CheckConsistencyFromIOAddress(
                   IOAddressSettings.StartIOAddressesForOutput[ioSystem],
                   IOAddressSettings.EndIOAddressesForOutput[ioSystem]);
                CheckCollisionExistForIOAddress(ioSystem, ioAddressCollisions, IoTypes.Output);

            }

        }

        private void CheckModulesIOAddress(ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice, ModuleType module, string ioSystem, bool elementUsed)
        {
            foreach (var ioAddress in module.IOAddresses)
            {
                if (!elementUsed)
                {
                    continue;
                }

                uint startAddress;
                uint endAddress = 0;
                bool isInputAddress = ioAddress.GetType() == typeof(ModuleTypeInputAddresses);

                PclCatalogObject catalogObject = null;
                ModuleCatalog moduleCatalog =
                    ModuleCatalogHelper.GetModuleCatalogWithGsdPath(
                        lonDecentralDevice.GSDPath,
                        module.GSDRefID);

                List<SubmoduleCatalog> virtualSubmodules = null;

                if (moduleCatalog != null)
                {
                    catalogObject = moduleCatalog;
                    virtualSubmodules = moduleCatalog.VirtualSubmoduleList;
                }
                else
                {
                    DecentralDeviceCatalog deviceCatalog =
                        DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                            lonDecentralDevice.GSDPath,
                            lonDecentralDevice.GSDRefID);

                    if (deviceCatalog != null)
                    {
                        catalogObject = deviceCatalog;
                        virtualSubmodules = deviceCatalog.VirtualSubmoduleList;
                    }
                }

                if (isInputAddress)
                {
                    startAddress = ((ModuleTypeInputAddresses)ioAddress).StartAddress;
                    CheckIOAddressIsUsedForModule(InternalAttributeNames.InAddressRange, IoTypes.Input, catalogObject, startAddress, ref endAddress, virtualSubmodules, module);
                    IOAddressSettings.StartIOAddressesForInput[ioSystem].Add(startAddress);
                    IOAddressSettings.EndIOAddressesForInput[ioSystem].Add(endAddress);
                    FillAvailableIOAddress(startAddress, endAddress, IOAddressSettings.AvailableInputPositions[ioSystem]);
                }
                else
                {
                    startAddress = ((ModuleTypeOutputAddresses)ioAddress).StartAddress;
                    CheckIOAddressIsUsedForModule(InternalAttributeNames.OutAddressRange, IoTypes.Output, catalogObject, startAddress, ref endAddress, virtualSubmodules, module);
                    IOAddressSettings.StartIOAddressesForOutput[ioSystem].Add(startAddress);
                    IOAddressSettings.EndIOAddressesForOutput[ioSystem].Add(endAddress);
                    FillAvailableIOAddress(startAddress, endAddress, IOAddressSettings.AvailableOutputPositions[ioSystem]);
                }
            }
        }

        private void CheckIOAddressIsUsedForModule(string internalAttrName, IoTypes ioTypes, PclCatalogObject catalogObject, uint startAddress, ref uint endAddress, List<SubmoduleCatalog> virtualSubmodules, ModuleType module)
        {
            if ((catalogObject != null)
                        && catalogObject.AttributeAccess.AttributeList.ContainsKey(
                            internalAttrName))
            {
                endAddress += Convert.ToUInt32(
                    catalogObject.AttributeAccess.AttributeList[internalAttrName],
                    CultureInfo.InvariantCulture);
                endAddress =
                    endAddress + startAddress
                    - 1; // for check mechanism this usage is better, for ex. if there is 4 byte, it starts 0 and ends in 3.
            }
            else if ((virtualSubmodules != null)
                     && (virtualSubmodules
                             .FindAll(
                                 w => w.AttributeAccess.AttributeList.ContainsKey(
                                     internalAttrName)).Count == 1))
            {
                endAddress += Convert.ToUInt32(
                    virtualSubmodules
                        .Find(
                            w => w.AttributeAccess.AttributeList.ContainsKey(internalAttrName)).AttributeAccess
                        .AttributeList[internalAttrName],
                    CultureInfo.InvariantCulture);
                endAddress =
                    endAddress + startAddress
                    - 1; // for check mechanism this usage is better, for ex. if there is 4 byte, it starts 0 and ends in 3.
            }
            else
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TypeOfIOAddressNotExist,
                    ioTypes,
                    module.ModuleID);
                throw new ConsistencyCheckException();
            }
        }
        private void CheckSubmodulesIOAddress(bool isDeviceShared, DecentralDeviceType decentralDevice, ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice, ModuleType module, string ioSystem)
        {
            foreach (ModuleTypeSubmodule submodule in module.Submodule)
            {
                bool submoduleUsed = !isDeviceShared || decentralDevice.SharedDevice.Exists(
                                         w => w.SharedModule.Exists(
                                             d => (d.ModuleRefID == module.ModuleID || d.ModuleRefID
                                                   == decentralDevice.DeviceRefID)
                                                  && d.SharedSubmodule.Exists(
                                                      s => s.SubmoduleRefID == submodule.SubmoduleID)));
                if (submoduleUsed && isDeviceShared)
                {
                    ioSystem = centralDeviceIOSystem[decentralDevice.SharedDevice.Find(
                        w => w.SharedModule.Exists(
                            d => (d.ModuleRefID == module.ModuleID
                                  || d.ModuleRefID == decentralDevice.DeviceRefID)
                                 && d.SharedSubmodule.Exists(
                                     s => s.SubmoduleRefID == submodule.SubmoduleID))).DeviceRefID];
                }

                foreach (var ioAddress in submodule.IOAddresses)
                {
                    uint startAddress;
                    uint endAddress = 0;
                    bool isInputAddress = ioAddress.GetType() == typeof(ModuleTypeInputAddresses);

                    SubmoduleCatalog smCatalog = SubmoduleCatalogHelper.GetSubmoduleCatalogWithGsdPath(
                        lonDecentralDevice.GSDPath,
                        submodule.GSDRefID);

                    if (submoduleUsed && isInputAddress)
                    {
                        startAddress = ((ModuleTypeInputAddresses)ioAddress).StartAddress;

                        CheckIOAddressIsUsedForSubmodule(InternalAttributeNames.InAddressRange, IoTypes.Input, smCatalog, submodule, startAddress, ref endAddress);
                        IOAddressSettings.StartIOAddressesForInput[ioSystem].Add(startAddress);
                        IOAddressSettings.EndIOAddressesForInput[ioSystem].Add(endAddress);
                        FillAvailableIOAddress(startAddress, endAddress, IOAddressSettings.AvailableInputPositions[ioSystem]);
                    }
                    else if (submoduleUsed)
                    {
                        startAddress = ((ModuleTypeOutputAddresses)ioAddress).StartAddress;
                        CheckIOAddressIsUsedForSubmodule(InternalAttributeNames.OutAddressRange, IoTypes.Output, smCatalog, submodule, startAddress, ref endAddress);
                        IOAddressSettings.StartIOAddressesForOutput[ioSystem].Add(startAddress);
                        IOAddressSettings.EndIOAddressesForOutput[ioSystem].Add(endAddress);
                        FillAvailableIOAddress(startAddress, endAddress, IOAddressSettings.AvailableOutputPositions[ioSystem]);
                    }
                }
            }
        }

        private void CheckIOAddressIsUsedForSubmodule(string addressRange, IoTypes ioType, SubmoduleCatalog smCatalog, ModuleTypeSubmodule submodule, uint startAddress, ref uint endAddress)
        {
            if (smCatalog.AttributeAccess.AttributeList
                            .ContainsKey(addressRange))
            {
                endAddress += Convert.ToUInt32(
                    smCatalog.AttributeAccess
                        .AttributeList[addressRange],
                    CultureInfo.InvariantCulture);
                endAddress =
                    endAddress + startAddress
                    - 1; // for check mechanism this usage is better, for ex. if there is 4 byte, it starts 0 and ends in 3.
            }
            else
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TypeOfIOAddressNotExist,
                    ioType.ToString(),
                    submodule.SubmoduleID);
                throw new ConsistencyCheckException();
            }
        }
        private void CheckIOAddressForDecentralDevice(DecentralDeviceType decentralDevice, ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice)
        {
            bool isDeviceShared = decentralDevice.SharedDevice != null
                                      && decentralDevice.SharedDevice.Count > 0;
            string ioSystem = isDeviceShared
                                  ? String.Empty
                                  : decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID;

            foreach (ModuleType module in decentralDevice.Module)
            {
                bool elementUsed =
                    !isDeviceShared
                    || (decentralDevice.SharedDevice.Exists(
                               w => w.SharedModule.Exists(d => d.ModuleRefID == module.ModuleID)));
                if (elementUsed && isDeviceShared)
                {
                    ioSystem = centralDeviceIOSystem[decentralDevice.SharedDevice
                        .Find(w => w.SharedModule.Exists(d => d.ModuleRefID == module.ModuleID)).DeviceRefID];
                }

                CheckModulesIOAddress(lonDecentralDevice, module, ioSystem, elementUsed);

                CheckSubmodulesIOAddress(isDeviceShared, decentralDevice, lonDecentralDevice, module, ioSystem);

            }
        }

        private void CheckCollisionExistForIOAddress(string ioSystem, List<uint> ioAddressCollisions, IoTypes ioType)
        {
            if (ioAddressCollisions.Count > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_CollisionInIOAddress,
                    ioType,
                    ioAddressCollisions[0],
                    ioAddressCollisions[1],
                    ioSystem);
                throw new ConsistencyCheckException();
            }
        }
        private void IsIOAddressDefinitionValid()
        {
            foreach (ConfigReader.ListOfNodes.DecentralDeviceType lonDevice in m_ListOfNodes.DecentralDevice)
            {
                DecentralDeviceType decentralDevice =
                    m_Configuration.Devices.DecentralDevice.FirstOrDefault(d => d.DeviceRefID == lonDevice.DeviceID);

                if (decentralDevice == null)
                {
                    // This device is added in list of nodes, but not used in configuration.
                    continue;
                }

                DecentralDeviceCatalog deviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                CheckXmlModule(lonDevice, decentralDevice, deviceCatalog);

                CheckPluggableModule(lonDevice, decentralDevice, deviceCatalog);

                CheckNotIOAddressTypedModuleAndSubmodules(decentralDevice, lonDevice.GSDPath);
            }
        }

        private void CheckXmlModule(ConfigReader.ListOfNodes.DecentralDeviceType lonDevice, DecentralDeviceType decentralDevice, DecentralDeviceCatalog deviceCatalog)
        {
            string vsmIdPath =
                    StringOperations.CombineParametersWithBackSlash(lonDevice.DeviceID, lonDevice.GSDRefID);
            if (!decentralDevice.Module.Exists(m => m.GSDRefID == lonDevice.GSDRefID))
            {
                ModuleType xmlModule = decentralDevice.Module.FirstOrDefault(m => m.GSDRefID == lonDevice.GSDRefID);
                CheckModuleVsmIOAddressDefinition(
                        xmlModule,
                        deviceCatalog.VirtualSubmoduleList.Where(
                            vsm => ((int)vsm.AttributeAccess.AttributeList[InternalAttributeNames.IoType]
                                    == (int)IoTypes.Input)
                                   || ((int)vsm.AttributeAccess.AttributeList[InternalAttributeNames.IoType]
                                       == (int)IoTypes.Output)),
                        vsmIdPath);
            }
            else
            {
                foreach (ModuleType xmlModule in decentralDevice.Module.FindAll(
                    m => m.GSDRefID == lonDevice.GSDRefID))
                {
                    CheckModuleVsmIOAddressDefinition(
                            xmlModule,
                            deviceCatalog.VirtualSubmoduleList.Where(
                                vsm => ((int)vsm.AttributeAccess.AttributeList[InternalAttributeNames.IoType]
                                        == (int)IoTypes.Input)
                                       || ((int)vsm.AttributeAccess.AttributeList[InternalAttributeNames.IoType]
                                           == (int)IoTypes.Output)),
                            vsmIdPath);
                }
            }
        }

        private void CheckPluggableModule(ConfigReader.ListOfNodes.DecentralDeviceType lonDevice, DecentralDeviceType decentralDevice, DecentralDeviceCatalog deviceCatalog)
        {
            foreach (string pluggableModuleKey in deviceCatalog.PluggableModuleList.Keys)
            {
                string gsdFileName = Path.GetFileName(lonDevice.GSDPath);
                string moduleIdPath =
                    StringOperations.CombineParametersWithBackSlash(gsdFileName, pluggableModuleKey);
                if (!decentralDevice.Module.Exists(m => m.GSDRefID == pluggableModuleKey))
                {
                    ModuleType xmlPlugModule =
                        decentralDevice.Module.FirstOrDefault(m => m.GSDRefID == pluggableModuleKey);
                    CheckIOAddressDefinitionForModule(
                            deviceCatalog,
                            pluggableModuleKey,
                            moduleIdPath,
                            xmlPlugModule,
                            lonDevice,
                            decentralDevice);
                }
                else
                {
                    foreach (ModuleType xmlPlugModule in decentralDevice.Module.FindAll(
                        m => m.GSDRefID == pluggableModuleKey))
                    {
                        CheckIOAddressDefinitionForModule(
                                deviceCatalog,
                                pluggableModuleKey,
                                moduleIdPath,
                                xmlPlugModule,
                                lonDevice,
                                decentralDevice);
                    }
                }
            }
        }

        private void CheckModuleVsmIOAddressDefinition(
            ModuleType xmlModule,
            IEnumerable<SubmoduleCatalog> submodules,
            string moduleGsdId)
        {
            if (xmlModule == null)
            {
                return;
            }

            if (submodules != null
                && submodules.Any())
            {

                if (submodules.Count() == 1)
                {
                    
                    CheckVsmIOAddressDefinedTwice(xmlModule, submodules, moduleGsdId);
                    
                }
                else
                {
                    CheckModuleHasMultipleIOAddressOwnerVsm(xmlModule, moduleGsdId);
                    
                }
            }
        }

        private void CheckVsmIOAddressDefinedTwice(ModuleType xmlModule, IEnumerable<SubmoduleCatalog> submodules, string moduleGsdId)
        {
            SubmoduleCatalog currentSmCatalog = submodules.FirstOrDefault();

            if (((xmlModule.IOAddresses != null) && (xmlModule.IOAddresses.Count > 0))
                        && xmlModule.Submodule.Exists(
                            sm => sm.GSDRefID
                                  == (string)currentSmCatalog.AttributeAccess.AttributeList[
                                      InternalAttributeNames.GsdId])
                        && xmlModule.Submodule.Exists(
                            sm => (sm.GSDRefID
                                   == (string)currentSmCatalog?.AttributeAccess.AttributeList[InternalAttributeNames
                                       .GsdId]) && (sm.IOAddresses != null) && (sm.IOAddresses.Count > 0)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_VsmIOAddressesDefinedTwice,
                    moduleGsdId,
                    (string)currentSmCatalog?.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckModuleHasMultipleIOAddressOwnerVsm(ModuleType xmlModule, string moduleGsdId)
        { 
            if (xmlModule.IOAddresses != null
                        && xmlModule.IOAddresses.Count > 0)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IOAddressesDefinitionOfMultipleVsm,
                    moduleGsdId);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckIOAddressDefinitionForModule(
            DecentralDeviceCatalog deviceCatalog,
            string pluggableModuleKey,
            string moduleIdPath,
            ModuleType xmlPlugModule,
            ConfigReader.ListOfNodes.DecentralDeviceType lonDevice,
            DecentralDeviceType decentralDevice)
        {
            string moduleId = StringOperations.CombineParametersWithBackSlash(lonDevice.DeviceID, pluggableModuleKey);

            if (deviceCatalog.PluggableModuleList[pluggableModuleKey].FixedInSlots?.Length > 0 && Catalog.ModuleList[moduleIdPath].VirtualSubmoduleList != null)
            {
                CheckModuleVsmIOAddressDefinition(
                    xmlPlugModule,
                    Catalog.ModuleList[moduleIdPath].VirtualSubmoduleList.Where(
                        vsm => (vsm.IOData != null)
                               && ((vsm.IOData.InputDataItems != null) || (vsm.IOData.OutputDataItems != null))),
                    moduleId);

            }

            var usedInModules = deviceCatalog.PluggableModuleList[pluggableModuleKey].UsedInSlots;
            if ((usedInModules != null)
                && (usedInModules.Length > 0))
            {
                CheckSlotsUsedInModules(usedInModules, decentralDevice, xmlPlugModule, pluggableModuleKey, moduleId, moduleIdPath);
                
            }

            if ((deviceCatalog.PluggableModuleList[pluggableModuleKey].UsedInSlots == null)
                && (deviceCatalog.PluggableModuleList[pluggableModuleKey].FixedInSlots == null)
                && (deviceCatalog.PluggableModuleList[pluggableModuleKey].AllowedInSlots != null))
            {
                if (xmlPlugModule == null)
                    return;

                CheckModuleVsmIOAddressDefinition(
                    xmlPlugModule,
                    Catalog.ModuleList[moduleIdPath].VirtualSubmoduleList.Where(
                        vsm => (vsm.IOData != null) && ((vsm.IOData.InputDataItems != null)
                                                        || (vsm.IOData.OutputDataItems != null))),
                    moduleId);
            }
        }

        private void CheckSlotsUsedInModules(Array usedInModules, DecentralDeviceType decentralDevice, ModuleType xmlPlugModule, string pluggableModuleKey, string moduleId, string moduleIdPath)
        {
            foreach (uint slot in usedInModules)
            {
                bool isModuleReplacedOrUnplugged =
                    decentralDevice.Module.Exists(m => m.SlotNumber == slot && m.GSDRefID != pluggableModuleKey);

                if (isModuleReplacedOrUnplugged)
                {
                    continue;
                }

                CheckModuleVsmIOAddressDefinition(
                    xmlPlugModule,
                    Catalog.ModuleList[moduleIdPath].VirtualSubmoduleList.Where(
                        vsm => (vsm.IOData != null) && ((vsm.IOData.InputDataItems != null)
                                                        || (vsm.IOData.OutputDataItems != null))),
                    moduleId);
            }
        }


        private void CheckNotIOAddressTypedModuleAndSubmodules(DecentralDeviceType xmlDevice, string gsdPath)
        {
            if (xmlDevice.Module == null)
            {
                return;
            }

            foreach (ModuleType xmlModule in xmlDevice.Module)
            {
                ModuleCatalog moduleCatalog =
                    ModuleCatalogHelper.GetModuleCatalogWithGsdPath(gsdPath, xmlModule.GSDRefID);

                DecentralDeviceCatalog deviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(gsdPath, xmlModule.GSDRefID);

                CheckModuleIsIOType(xmlModule, moduleCatalog, deviceCatalog);
                CheckSubmodules(xmlModule, moduleCatalog, deviceCatalog, gsdPath);
                
            }
        }

        private void CheckModuleIsIOType(ModuleType xmlModule, ModuleCatalog moduleCatalog, DecentralDeviceCatalog deviceCatalog)
        {
            if ((xmlModule.IOAddresses != null)
                    && (xmlModule.IOAddresses.Count > 0)
                    && ((moduleCatalog != null && moduleCatalog.VirtualSubmoduleList.All(
                             vsm => (vsm.IOData.InputDataItems == null) && (vsm.IOData.OutputDataItems == null)))
                        || (deviceCatalog != null && deviceCatalog.VirtualSubmoduleList.All(
                                vsm => (vsm.IOData.InputDataItems == null) && (vsm.IOData.OutputDataItems == null)))))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotIOAddressTypedModule,
                    xmlModule.ModuleID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckSubmodules(ModuleType xmlModule, ModuleCatalog moduleCatalog, DecentralDeviceCatalog deviceCatalog, string gsdPath)
        {
            foreach (ModuleTypeSubmodule xmlSubmodule in xmlModule.Submodule)
            {
               
                if ((xmlSubmodule.IOAddresses == null)
                    || (xmlSubmodule.IOAddresses.Count <= 0))
                {
                    continue;
                }

                CheckSubmoduleCatalogIsIOType(xmlSubmodule, gsdPath);

                if (moduleCatalog?.VirtualSubmoduleList != null)
                {
                    CheckVirtualSubmoduleCatalogIsIOType(xmlSubmodule, moduleCatalog.VirtualSubmoduleList);
                }

                if (deviceCatalog?.VirtualSubmoduleList != null)
                {
                    CheckVirtualSubmoduleCatalogIsIOType(xmlSubmodule, deviceCatalog.VirtualSubmoduleList);
                }

                
                
            }
        }

        private void CheckSubmoduleCatalogIsIOType(ModuleTypeSubmodule xmlSubmodule, string gsdPath)
        {
            SubmoduleCatalog smCatalog =
                   SubmoduleCatalogHelper.GetSubmoduleCatalogWithGsdName(gsdPath, xmlSubmodule.GSDRefID);

            if (smCatalog != null
                    && smCatalog.IOData.InputDataItems == null
                    && smCatalog.IOData.OutputDataItems == null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotIOAddressTypedModule,
                    xmlSubmodule.SubmoduleID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckVirtualSubmoduleCatalogIsIOType(ModuleTypeSubmodule xmlSubmodule, List<SubmoduleCatalog> moduleList)
        {
            foreach (SubmoduleCatalog virtualSm in moduleList?.FindAll(
                    sm => (string)sm.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]
                          == xmlSubmodule.GSDRefID))
            {
                if ((virtualSm.IOData.InputDataItems == null)
                    && (virtualSm.IOData.OutputDataItems == null))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_NotIOAddressTypedModule,
                        xmlSubmodule.SubmoduleID);
                    throw new ConsistencyCheckException();
                }
            }
        }
        private static void FillAvailableIOAddress(uint startIndex, uint endIndex, IList<bool> positions)
        {
            for (uint i = startIndex; i <= endIndex; i++)
            {
                positions[(int)i] = false;
            }
        }

        private static void CheckIOAddressExceeded(InputOutputAddressMap inputOutputAddressMap)
        {
            foreach (KeyValuePair<string, List<uint>> startIOPair in inputOutputAddressMap.StartIOAddressesForOutput)
            {
                foreach (uint ioAddress in startIOPair.Value)
                {
                    if (ioAddress > s_MaximumIOAddress)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_IOAddressRangeExceeded);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private static List<uint> CheckConsistencyFromIOAddress(IReadOnlyList<uint> startAddress, IReadOnlyList<uint> endAddress)
        {
            List<uint> result = new List<uint>();
            for (int i = 0; i < startAddress.Count; i++)
            {
                for (int j = i + 1; j < startAddress.Count; j++)
                {
                    if ((startAddress[i] >= startAddress[j]) && (startAddress[i] <= endAddress[j]))
                    {
                        result.Add(startAddress[i]);
                        result.Add(startAddress[j]);
                        break;
                    }
                    if ((endAddress[i] >= startAddress[j]) && (endAddress[i] <= endAddress[j]))
                    {
                        result.Add(startAddress[i]);
                        result.Add(startAddress[j]);
                        break;
                    }
                    if ((startAddress[j] >= startAddress[i]) && (startAddress[j] <= endAddress[i]))
                    {
                        result.Add(startAddress[i]);
                        result.Add(startAddress[j]);
                        break;
                    }
                    if ((endAddress[j] >= startAddress[i]) && (endAddress[j] <= endAddress[i]))
                    {
                        result.Add(startAddress[i]);
                        result.Add(startAddress[j]);
                        break;
                    }
                }
            }
            return result;
        }
    }

    internal class IOAdressSettings
    {
        
        private Dictionary<string, List<uint>> startIOAddressesForInput;
        private Dictionary<string, List<uint>> startIOAddressesForOutput;
        private Dictionary<string, List<uint>> endIOAddressesForInput;
        private Dictionary<string, List<uint>> endIOAddressesForOutput;
        private Dictionary<string, bool[]> availableInputPositions;
        private Dictionary<string, bool[]> availableOutputPositions;

        internal IOAdressSettings()
        {
            this.startIOAddressesForInput = new Dictionary<string, List<uint>>();
            this.startIOAddressesForOutput = new Dictionary<string, List<uint>>();
            this.endIOAddressesForInput = new Dictionary<string, List<uint>>();
            this.endIOAddressesForOutput = new Dictionary<string, List<uint>>();
            this.availableInputPositions = new Dictionary<string, bool[]>();
            this.availableOutputPositions = new Dictionary<string, bool[]>();

        }

        internal Dictionary<string, List<uint>> StartIOAddressesForInput { get => startIOAddressesForInput; set => startIOAddressesForInput = value; }
        internal Dictionary<string, List<uint>> StartIOAddressesForOutput { get => startIOAddressesForOutput; set => startIOAddressesForOutput = value; }
        internal Dictionary<string, List<uint>> EndIOAddressesForInput { get => endIOAddressesForInput; set => endIOAddressesForInput = value; }
        internal Dictionary<string, List<uint>> EndIOAddressesForOutput { get => endIOAddressesForOutput; set => endIOAddressesForOutput = value; }
        internal Dictionary<string, bool[]> AvailableInputPositions { get => availableInputPositions; set => availableInputPositions = value; }
        internal Dictionary<string, bool[]> AvailableOutputPositions { get => availableOutputPositions; set => availableOutputPositions = value; }

        internal void initialize(List<string> ioSystems)
        {
            foreach (var ioSystem in ioSystems)
            {
                startIOAddressesForInput[ioSystem] = new List<uint>();
                startIOAddressesForOutput[ioSystem] = new List<uint>();
                endIOAddressesForInput[ioSystem] = new List<uint>();
                endIOAddressesForOutput[ioSystem] = new List<uint>();
                availableInputPositions[ioSystem] = new bool[IOAddressChecker.s_MaximumIOAddress + 1];
                availableOutputPositions[ioSystem] = new bool[IOAddressChecker.s_MaximumIOAddress + 1];
                for (int i = 0; i < availableInputPositions[ioSystem].Length; i++)
                {
                    availableInputPositions[ioSystem][i] = true;
                    availableOutputPositions[ioSystem][i] = true;
                }
            }
        }
    }
}
