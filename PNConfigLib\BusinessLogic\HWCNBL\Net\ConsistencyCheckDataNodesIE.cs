/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyCheckDataNodesIE.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL.Net
{
    internal class ConsistencyCheckDataNodesIE
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// The default constructor.
        /// </summary>
        public ConsistencyCheckDataNodesIE()
        {
            m_MultipleIPAddresses = null;
            m_MultipleInterfaceNames = null;
        }

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private IDictionary<long, IList<DataModel.PCLObjects.Node>> m_MultipleIPAddresses;

        private IDictionary<string, IList<DataModel.PCLObjects.Node>> m_MultipleInterfaceNames;

        private IDictionary<string, IList<DataModel.PCLObjects.Node>> m_MultipleDeviceNames;

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// This property contains all multiple IP addresses together with a list of the
        /// corresponding node objects.
        /// </summary>
        public IDictionary<long, IList<DataModel.PCLObjects.Node>> MultipleIPAddresses
        {
            get { return m_MultipleIPAddresses; }
            set { m_MultipleIPAddresses = value; }
        }
        /// <summary>
        /// This property contains all names of interface together with a list of the
        /// corresponding node objects.
        /// </summary>
        public IDictionary<string, IList<DataModel.PCLObjects.Node>> MultipleInterfaceNames
        {
            get { return m_MultipleInterfaceNames; }
            set { m_MultipleInterfaceNames = value; }
        }

        /// <summary>
        /// This property contains all names of station together with a list of the
        /// corresponding node objects.
        /// </summary>
        public IDictionary<string, IList<DataModel.PCLObjects.Node>> MultipleDeviceNames
        {
            get { return m_MultipleDeviceNames; }
            set { m_MultipleDeviceNames = value; }
        }

        #endregion
    }
}