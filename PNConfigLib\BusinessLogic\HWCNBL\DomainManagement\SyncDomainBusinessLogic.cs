/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SyncDomainBusinessLogic.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.PNPlannerAdapter;

using Subnet = PNConfigLib.DataModel.PCLObjects.Subnet;

#endregion

namespace PNConfigLib.HWCNBL.DomainManagement
{
    public class InterfaceFrameDataList
    {
        public InterfaceFrameDataList(Dictionary<Interface, List<IPNFrameData>> allPNFrames)
        {
            AllPNFrames = allPNFrames;
        }

        /// <summary>
        /// The dictionary that contains all the frames in the project, grouped by the Interfaces.
        /// </summary>
        public Dictionary<Interface, List<IPNFrameData>> AllPNFrames { get; }

        public void SetAllPNFrames(InterfaceFrameDataList frameDataList)
        {
            if (frameDataList == null)
            {
                throw new ArgumentNullException(nameof(frameDataList));
            }

            AllPNFrames.Clear();
            foreach (Interface key in frameDataList.AllPNFrames.Keys)
            {
                AllPNFrames.Add(key, frameDataList.AllPNFrames[key]);
            }
        }
    }

    /// <summary>
    /// Business logic class for sync domain.
    /// </summary>
    public class SyncDomainBusinessLogic : DomainBusinessLogic
    {
        public Dictionary<Interface, List<long>> PossibleSendClockFactors { get; } =
            new Dictionary<Interface, List<long>>();

        /// <summary>
        /// PNPlannerHelper object that containts helper methods for PNPlanner.
        /// </summary>
        private PNPlannerHelper m_PNPlannerHelper;

        /// <summary>
        /// The object that contains results of PNPlanner results.
        /// </summary>
        private PNPlannerResults m_PNPlannerResults = new PNPlannerResults();

        private bool m_IsPNPlannerSuccessful;

        /// <summary>
        /// The constructor for SyncDomainBusinessLogic.
        /// </summary>
        /// <param name="syncDomain">The corresponding SyncDomain object for this sync domain.</param>
        public SyncDomainBusinessLogic(SyncDomain syncDomain)
        {
            if (syncDomain == null)
            {
                throw new ArgumentNullException(nameof(syncDomain));
            }

            PCLObject = syncDomain;
            syncDomain.SyncDomainBusinessLogic = this;
            m_PNPlannerHelper = new PNPlannerHelper(this);
            InitBL();
            ConsistencyManager.RegisterConsistencyCheck(PCLObject, CheckSyncDomainConsistency);
        }

        /// <summary>
        /// Gets all of the participants of the sync domain.
        /// </summary>
        public IList<Interface> AllParticipants
        {
            get
            {
                SyncDomain syncDomain = PCLObject as SyncDomain;
                if (syncDomain != null)
                {
                    return (List<Interface>)syncDomain.GetInterfaces();
                }
                return null;
            }
        }

        /// <summary>
        /// Returns whether advance startup mode is active as has been determined by PNPlanner.
        /// </summary>
        public bool IsAdvancedStartupModeActive => m_PNPlannerHelper.StartupMode
                                                   == PNIrtArStartupMode.Advanced.ToString();

        /// <summary>
        /// Returns whether fast forwarding is active as has been determined by PNPlanner.
        /// </summary>
        public bool IsFFWActive =>  m_PNPlannerHelper.FastForwarding; 

        /// <summary>
        /// Checks if the fragmentation is active in the sync domain.
        /// </summary>
        /// <remarks>
        /// It is only active if IsFragmentationPossible == true AND Sendclock - Calculated Cyclic Bandwidth is smaller than 125000
        /// ns.
        /// </remarks>
        public bool IsFragmentationActive => (SendClock - MaxBandwidthForCyclic < 125000) && IsFragmentationPossible;

        /// <summary>
        /// Checks if fragmentable frames can be fragmented in runtime.
        /// </summary>
        /// <remarks>
        /// Frames can be fragmented if all synchronized participants support fragmentation. For IOD interface submodules,
        /// having a PNIrtFragmentationType other than "none" is sufficient. For IOC interface submodules,
        /// PNIrtFragmentationMode must either be "all" or "onePort". In the latter case, the interface submodule
        /// must only have one active port.
        /// </remarks>
        public bool IsFragmentationPossible
        {
            get
            {
                bool syncParticipantExists = false;
                foreach (Interface interfaceSubmodule in SynchronizedParticipants)
                {
                    syncParticipantExists = true;

                    if (AttributeUtilities.IsIDevice(interfaceSubmodule))
                    {
                        bool isDecentralPDEV = AttributeUtilities.IsDecentralPDEV(interfaceSubmodule);
                        if (!CheckFragmentationSupport(interfaceSubmodule, isDecentralPDEV))
                        {
                            return false;
                        }
                    }

                    else if (Utility.IsProfinetDeviceInterfaceSubmodule(interfaceSubmodule))
                    {
                        if (!CheckFragmentationSupport(interfaceSubmodule, true))
                        {
                            return false;
                        }
                    }
                    else if (Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
                    {
                        if (!CheckFragmentationSupport(interfaceSubmodule, false))
                        {
                            return false;
                        }
                    }
                }
                return syncParticipantExists;
            }
        }

        /// <summary>
        /// Checks if PNPlanner is executed successfully.
        /// </summary>
        /// <remarks>
        /// When PNPlanner fails, it is unnecessary to check non-calculated values. Therefore this propert is used for this check.
        /// </remarks>
        public bool IsPNPlannerSuccessful =>  m_IsPNPlannerSuccessful;
        
        /// <summary>
        /// Gets the maximum cyclic bandwidth (in nanoseconds).
        /// </summary>
        public long MaxBandwidthForCyclic
        {
            get
            {
                int sendClockFactor = SendClockFactor;

                if ((sendClockFactor <= 0)
                    && (SyncMaster != null))
                {
                    sendClockFactor =
                        (int)
                        SyncMaster.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            new AttributeAccessCode(),
                            32);
                }

                return Utility.GetMaxIoCyclicBandwidth(sendClockFactor, this);
            }
        }

        /// <summary>
        /// Finds the maximum of all PNIOMaxFrameStartTime off all interface submodules
        /// within the sync domain.
        /// </summary>
        public uint MaxFrameStartTime => FindMaxAttribute<uint>(
            AllParticipants,
            InternalAttributeNames.PnIoMaxFrameStartTime,
            PNConstants.DefaultMaxFrameStartTime,
            PNConstants.DefaultMaxFrameStartTime);

        /// <summary>
        /// Finds the maximum of all MinYellowTime attributes off all interface submodules
        /// within the sync domain.
        /// </summary>
        public uint MaxMinYellowTime => FindMaxAttribute<uint>(
            SynchronizedParticipants,
            InternalAttributeNames.PnIrtMinYellowTime,
            PNConstants.DefaultMinYellowTime,
            PNConstants.DefaultPDIRDataYellowTime);

        /// <summary>
        /// Finds the maximum of all YellowSafetyMarginAttributes off all interface submodules
        /// within the sync domain.
        /// </summary>
        public uint MaxYellowSafetyMargin => FindMaxAttribute<uint>(
            SynchronizedParticipants,
            InternalAttributeNames.PnIrtYellowSafetyMargin,
            PNConstants.DefaultYellowSafetyMargin,
            PNConstants.DefaultYellowSafetyMargin);

        /// <summary>
        /// Gets the PLLWindow parameter of the Sync-Domain.
        /// </summary>
        public uint PllWindow =>  s_PllWindow; 

        /// <summary>
        /// Property that stores calculated PNPlanner values.
        /// </summary>
        internal PNPlannerResults PNPlannerResults =>  m_PNPlannerResults; 

        /// <summary>
        /// Gets the PtcpTimeoutFactor parameter of the Sync-Domain.
        /// </summary>
        public int PtcpTimeoutFactor =>  s_PtcpTimeoutFactor; 

        /// <summary>
        /// Gets the ReservedIntervalBegin parameter of the Sync-Domain.
        /// </summary>
        public uint ReservedIntervalBegin =>  s_ReservedIntervalBegin; 

        /// <summary>
        /// Gets the ReservedIntervalEnd parameter of the Sync-Domain.
        /// </summary>
        public uint ReservedIntervalEnd =>  s_ReservedIntervalEnd; 

        /// <summary>
        /// Gets the reserved IRT bandwidth (in nanoseconds).
        /// </summary>
        public long ReservedIrtBandwidth
        {
            get
            {
                long resCyclicBandwidth =
                    PCLObject.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnResSyncCyclicBandwidth,
                        new AttributeAccessCode(),
                        s_DefaultMaxCyclicBandwidth);

                return resCyclicBandwidth;
            }
        }

        /// <summary>
        /// Gets the reserved IRT bandwidth ratio (in percentage).
        /// </summary>
        public long ReservedIrtBandwidthRatio
        {
            get
            {
                if (HasDevicesUsingIrtTop)
                {
                    // IRTTop does not have a reserved bandwidth ratio, the entire cyclic bandwidth can be used.
                    return 100;
                }
                long pnResSyncCyclicRatio =
                    PCLObject.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnResSyncCyclicRatio,
                        new AttributeAccessCode(),
                        s_ResSyncCyclicRatioDefault);

                return pnResSyncCyclicRatio;
            }
        }

        /// <summary>
        /// Gets the secondary sync master of the sync domain.
        /// </summary>
        public Interface SecondarySyncMaster
        {
            get
            {
                foreach (Interface interfaceSubmodule in AllParticipants)
                {
                    PclObject ioConnector = interfaceSubmodule.GetIOConnector();

                    if (ioConnector == null)
                    {
                        continue;
                    }

                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);
                    // Check whether the interface submodule is the secondary sync master.
                    if (syncRole == PNIRTSyncRole.SecondarySyncMaster)
                    {
                        return interfaceSubmodule;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Gets the send clock (in nanoseconds) from the actual sync master, if exists.
        /// Returns the default value otherwise.
        /// </summary>
        public long SendClock
        {
            get
            {
                if (SendClockFactor == s_SendClockFactorErrorValue)
                {
                    return s_SendClockDefaultValue;
                }

                return SendClockFactor * s_SendClockGetFactor;
            }
        }

        /// <summary>
        /// Gets the SendClockFactor parameter of the Sync-Domain.
        /// </summary>
        public int SendClockFactor
        {
            get
            {
                long sendClockFactor;

                TryGetSelectedSendClockfactor(out sendClockFactor);

                return (int)sendClockFactor;
            }
        }

        /// <summary>
        /// Getter for the SyncDomain data model object of this SyncDomainBusinessLogic class.
        /// </summary>
        public SyncDomain SyncDomain =>  PCLObject as SyncDomain; 

        /// <summary>
        /// Gets the sync master of the sync domain.
        /// </summary>
        public Interface SyncMaster
        {
            get
            {
                foreach (Interface interfaceSubmodule in AllParticipants)
                {
                    PclObject ioConnector = interfaceSubmodule.GetIOConnector();

                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);
                    // Check whether the interface submodule is the master.
                    if ((syncRole == PNIRTSyncRole.SyncMaster)
                        || (syncRole == PNIRTSyncRole.PrimarySyncMaster))
                    {
                        return interfaceSubmodule;
                    }
                }

                return null;
            }
        }

        /// <summary>
        /// Gets the SyncSendFactor parameter of the Sync-Domain.
        /// </summary>
        public uint SyncSendFactor =>  s_SyncSendFactor; 

        /// <summary>
        /// Whether a given sync domain has devices that use IRTTop.
        /// </summary>
        /// <remarks>
        /// This method checks the PNIOFrameClass attribute of each device's PN interface connected to the sync domain.
        /// An interface uses IRTTop if its PNIOFrameClass attribute value is Class3Frame.
        /// </remarks>
        /// <returns>True if there are PN interfaces that use IRTTop in the sync domain; false otherwise.</returns>
        internal bool HasDevicesUsingIrtTop
        {
            get
            {
                foreach (Interface interfaceSubmodule in IODeviceInterfaces)
                {
                    PclObject ioConnector = interfaceSubmodule.GetIOConnector();

                    if (ioConnector != null)
                    {
                        if (!(ioConnector is PNIOD))
                        {
                            continue;
                        }

                        PNIOFrameClass frameClass =
                            (PNIOFrameClass)
                            ioConnector.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIoFrameClass,
                                new AttributeAccessCode(),
                                1);

                        if (frameClass == PNIOFrameClass.Class3Frame)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }
        }

        /// <summary>
        ///   Checks whether some participant devices use IrtFlex.
        /// </summary>
        internal Boolean HasDevicesUsingIrtFlex
        {
            get
            {
                // Check the attributes of each Device Interface Submodule.
                foreach (Interface interfaceSubmodule in IODeviceInterfaces)
                {
                    // Get the IO Device object.
                    PclObject ioDevice =interfaceSubmodule.PNIOD;
                    if (ioDevice == null)
                    {
                        continue;
                    }

                    // Check whether the device is synchronized
                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioDevice);
                    if (syncRole == PNIRTSyncRole.NotSynchronized)
                    {
                        continue;
                    }
                    PNIOFrameClass frameClass =
                          (PNIOFrameClass)
                          ioDevice.AttributeAccess.GetAnyAttribute<long>(
                              InternalAttributeNames.PnIoFrameClass,
                              new AttributeAccessCode(),
                              0);

                    // Check whether the actual Frame Class is Class3Frame.
                    if (frameClass == PNIOFrameClass.Class2Frame)
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        /// <summary>
        ///   Checks whether the sync. domain has at least one device which is a secondary sync-master.
        /// </summary>
        internal Boolean HasDevicesUsingIrtFlexWithSecondarySyncMaster
        {
            get
            {
                // Check the attributes of each Device Interface Submodule.
                foreach (Interface interfaceSubmodule in IODeviceInterfaces)
                {
                    // Get the IO Device object.
                    PclObject ioDevice = interfaceSubmodule.PNIOD;
                    if (ioDevice == null)
                    {
                        continue;
                    }

                    // Check whether the device is synchronized
                    PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioDevice);
                    if (syncRole == PNIRTSyncRole.SecondarySyncMaster)
                    {
                        PNIOFrameClass frameClass =
                           (PNIOFrameClass)
                           ioDevice.AttributeAccess.GetAnyAttribute<long>(
                               InternalAttributeNames.PnIoFrameClass,
                               new AttributeAccessCode(),
                               0);
                        // Check whether the actual Frame Class is Class2Frame. (IRT Flex)
                        if (frameClass == PNIOFrameClass.Class2Frame)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }
        }

        /// <summary>
        /// Whether fast IO is supported by the sync domain.
        /// </summary>
        internal bool IsFastIoSupported
        {
            get
            {
                if (SyncMaster == null)
                {
                    return false;
                }
                if (SendClock > s_SendClockLowClassLimit)
                {
                    // The Send Clock is not fast enough to support Fast IO.
                    return false;
                }

                // Go through all participants in the Sync-Domain.
                foreach (Interface participant in IOControllerInterfaces)
                {
                    if (AttributeUtilities.GetTransientPNSendClockFactor(participant, 0) > 8)
                    {
                        return false;
                    }
                }
                foreach (Interface participant in IODeviceInterfaces)
                {
                    bool pnFastIoSupported =
                        participant.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnFastIoSupported,
                            new AttributeAccessCode(),
                            false);
                    {
                        if (!pnFastIoSupported)
                        {
                            return false;
                        }
                    }
                }

                return true;
            }
        }

        /// <summary>
        /// Gets the maximum possible value of the cyclic bandwidth in nanoseconds.
        /// MaxBandwidthForCyclic and this property return the same values if advanced startup mode is not active
        /// for at least one synchronized participant.
        /// </summary>
        internal long MaxPossibleBandwidthForCyclic
        {
            get
            {
                if (!HasDevicesUsingIrtTop)
                {
                    return MaxBandwidthForCyclic;
                }

                if (IsAdvancedStartupModeActive)
                {
                    return (long)(SendClock * 0.9);
                }

                AttributeAccessCode ac = new AttributeAccessCode();
                bool expertModeEnabled =
                    PCLObject.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtSyncDomainExpertMode,
                        ac.GetNew(),
                        false);

                return expertModeEnabled ? Math.Min(SendClock - 125000, MaxRedPeriodLength) : MaxBandwidthForCyclic;
            }
        }

        /// <summary>
        /// Gets the maximum possible IRTTop bandwidth supported by the synchronized participants.
        /// </summary>
        internal long MaxRedPeriodLength
        {
            get
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                uint maxRedPeriodLength = uint.MaxValue;
                foreach (Interface participant in SynchronizedParticipants)
                {
                    uint maxRedPeriodOfInterface =
                        participant.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIrtMaxRedPeriodLength,
                            ac.GetNew(),
                            PNConstants.DefaultMaxRedPeriodLength);

                    maxRedPeriodLength = Math.Min(maxRedPeriodLength, maxRedPeriodOfInterface);
                }
                return maxRedPeriodLength * 1000; // Micro to nano conversion.
            }
        }

        /// <summary>
        /// Gets the synchronized participants.
        /// </summary>
        internal IEnumerable<Interface> SynchronizedParticipants
        {
            get
            {
                foreach (Interface interfaceSubmodule in AllParticipants)
                {
                    PclObject ioConnector = interfaceSubmodule.GetIOConnector();

                    if (ioConnector == null)
                    {
                        //There can be interface sub-modules that do not have IO-connector (e.g. GBIT_1 IF sub-module)
                        continue;
                    }

                    // Check whether the interface submodule is synchronized.
                    bool interfaceSynchronized = PNAttributeUtility.GetAdjustedSyncRole(ioConnector)
                                                 != PNIRTSyncRole.NotSynchronized;
                    if (interfaceSynchronized)
                    {
                        yield return interfaceSubmodule;
                    }
                }
            }
        }

        internal InterfaceFrameDataList m_InterfaceFrameDataList =
            new InterfaceFrameDataList(new Dictionary<Interface, List<IPNFrameData>>());


        /// <summary>
        /// Makes the necessary consistency checks for the sync domain.
        /// </summary>
        public void CheckSyncDomainConsistency()
        {
            Subnet subnet = null;
            if (AllParticipants.Count != 0)
            {
                subnet = AllParticipants[0].Node.Subnet;
            }
            if (subnet != null)
            {
                SyncDomainConsistencyChecker.CheckConsistencySyncDomainName(this);
            }

            m_IsPNPlannerSuccessful = ExecutePNPlanner();

            SyncDomainConsistencyChecker.CheckConsistencyAfterPNPlanner1(this);
            SyncDomainConsistencyChecker.CheckConsistencyAfterPNPlanner2(this);

            // If Fast Forwarding is permitted then warning is shown because of potential IPv6 Device 
            SyncDomainConsistencyChecker.CheckIfFastForwardingPermitted(this);
        }

        /// <summary>
        ///   Assigns the given Interface Submodule as the SyncMaster or the Primary SyncMaster 
        ///   of the Domain, if possible.
        /// </summary>
        /// <param name="candidate">interface submodule</param>
        /// <param name="interfaceType">interface type</param>
        /// <returns></returns>
        internal Boolean AssignAsSyncMaster(PclObject candidate, PNInterfaceType interfaceType)
        {
            // Check preconditions
            if (candidate == null)
                throw new ArgumentNullException(nameof(candidate));

            List<PclObject> ioConnectors = NavigationUtilities.GetIOConnectors(candidate.GetInterface());
            if (null == ioConnectors)
            {
                return false;
            }

            bool assignedAsSyncMaster = false;
            foreach (PclObject ioConnector in ioConnectors)
            {
                string attrName = interfaceType == PNInterfaceType.IOController ?
                    InternalAttributeNames.IsMasterType : InternalAttributeNames.IsSlaveType;
                bool isCorrectType = ioConnector.AttributeAccess.GetAnyAttribute<bool>(attrName,
                    new AttributeAccessCode(), false);
                if (!isCorrectType)
                {
                    continue;
                }
                // Check the supported sync master mode (either SyncMaster or PrimarySyncMaster is supported)
                List<PNIRTSyncRole> suppSyncRoles = GetSupportedSyncRoles(candidate.GetInterface());
                bool setAsSyncMaster = suppSyncRoles.Contains(PNIRTSyncRole.SyncMaster);
                bool setAsPrimarySyncMaster = suppSyncRoles.Contains(PNIRTSyncRole.PrimarySyncMaster);
                if (!setAsSyncMaster && !setAsPrimarySyncMaster)
                {
                    continue;
                }
                PNIRTSyncRole syncMasterRoleToSet = setAsSyncMaster ? PNIRTSyncRole.SyncMaster
                                                        : PNIRTSyncRole.PrimarySyncMaster;
                ioConnector.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnIrtSyncRole, (byte)syncMasterRoleToSet);
                assignedAsSyncMaster = true;
                break;
            }
            return assignedAsSyncMaster;
        }
        /// <summary>
        ///   Gets the supported SyncRoles of an Interface Submodule.
        /// </summary>
        /// <param name = "interfaceSubmodule">The Interface Submodule</param>
        /// <returns>The list of the supported Sync Roles</returns>
        private List<PNIRTSyncRole> GetSupportedSyncRoles(PclObject interfaceSubmodule)
        {
            // Create a list to keep the supported SyncRoles of the Interface Submodule.
            List<PNIRTSyncRole> supportedSyncRoles = new List<PNIRTSyncRole>();

            // Get the Composite Attribute of PNIrtSupportedSyncProtocols
            AttributeAccessCode ac = new AttributeAccessCode();
            Enumerated syncProtocols =
            interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIrtSupportedSyncProtocols,
                ac,
                null);

            if (!ac.IsOkay)
            {
                // Add only "Not Synchronized" mode.
                supportedSyncRoles.Add(PNIRTSyncRole.NotSynchronized);
                return supportedSyncRoles;
            }

            bool rtSyncSupported = false;
            foreach (PNIRTSupportedSyncProtocols childProtocol in syncProtocols.List)
            {
                // Get the value of the enum.
                if (childProtocol != PNIRTSupportedSyncProtocols.RTSync)
                {
                    continue;
                }
                rtSyncSupported = true;
                break;
            }

            if (!rtSyncSupported)
            {
                // Add only "Not Synchronized" mode.
                supportedSyncRoles.Add(PNIRTSyncRole.NotSynchronized);
                return supportedSyncRoles;
            }

            // The submodule supports RtSync. Check the PNIrtSyncRole attribute.
            Enumerated syncRoles =
                               interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                                   InternalAttributeNames.PnIrtSyncRoleSupp,
                                   ac,
                                   null);
            if (!ac.IsOkay)
            {
                // Add only "Not Synchronized" mode.
                supportedSyncRoles.Add(PNIRTSyncRole.NotSynchronized);
                return supportedSyncRoles;
            }

            // Add all supported SyncRoles
            foreach (PNIRTSyncRole childRole in syncRoles.List)
            {
                supportedSyncRoles.Add(childRole);
            }

            return supportedSyncRoles;
        }

        /// <summary>
        /// Fills the corresponding PROFINET attributes from the PNConfigLib configuration XML.
        /// </summary>
        /// <param name="xmlSyncDomain">The XML object of the sync domain.</param>
        public void Configure(SyncDomainType xmlSyncDomain)
        {
            if (xmlSyncDomain == null)
            {
                throw new ArgumentNullException(nameof(xmlSyncDomain));
            }

            if (string.IsNullOrEmpty(xmlSyncDomain.SyncDomainName))
            {
                SyncDomain.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlSyncDomain.SyncDomainID);
            }
            else
            {
                SyncDomain.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlSyncDomain.SyncDomainName);
            }

            SyncDomain.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnIrtSyncDomainExpertMode, xmlSyncDomain.ActivateHighPerformance);

            if (xmlSyncDomain.ActivateHighPerformance)
            {
                SyncDomain.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnIrtSyncDomainFFW, xmlSyncDomain.PermitUsageOfFastForwarding);
            }

            BandwidthUse bandwidthUse = xmlSyncDomain.Details.BandwidthUse;
            PNBandwidthUse mappedBandwidthUse = AttributeUtilities.MapBandwidthUse(bandwidthUse);
            SyncDomain.AttributeAccess.SetAnyAttribute<byte>(
                InternalAttributeNames.PnIrtBandwidthLevel,
                (byte)mappedBandwidthUse);
        }

        /// <summary>
        /// The main entry point for creating input xml for PNPlanner.
        /// </summary>
        /// <returns>True if PNPlanner is executed successfully; false otherwise.</returns>
        public bool ExecutePNPlanner()
        {
            if (m_PNPlannerHelper == null)
            {
                m_PNPlannerHelper = new PNPlannerHelper(this);
            }
            // The result to return.
            IList<Interface> controllerInterfaces = IOControllerInterfaces;
            // Create frames.
            MethodData checkData = new MethodData();
            var allFrames = CreateFrames(checkData, controllerInterfaces);
            m_InterfaceFrameDataList.SetAllPNFrames(new InterfaceFrameDataList(allFrames));

            GenerateTemporaryPNPlannerFrameIds(m_InterfaceFrameDataList);

            // Set default values.
            m_PNPlannerHelper.StartTime = 5000;
            m_PNPlannerHelper.FramePreamble = 8;
            m_PNPlannerHelper.StartupMode = PNIOARStartupMode.Legacy.ToString();
            m_PNPlannerHelper.FastForwarding = false;

            // Create the IrtTop Island for the execution.
            InterfaceFrameDataList topIsland;

            PNPlannerResult creationResult = m_PNPlannerHelper.CreateTopIsland(out topIsland, m_InterfaceFrameDataList);
            if (HasDevicesUsingIrtTop && !SyncDomainConsistencyChecker.CheckIrtTopIsland(creationResult, topIsland, SyncDomain.SyncDomainBusinessLogic))
            {
                return false;
            }
            if ((creationResult!= null) && (creationResult.ResultType != PNPlannerResultType.Successful))
            {
                return SyncDomainConsistencyChecker.ProcessPNPlannerResult(SyncDomain, creationResult);
            }

            PNPlannerInputWriter pnPlannerInputWriter = new PNPlannerInputWriter(this);
            Dictionary<string, Interface> interfacesOfSwitchNames = new Dictionary<string, Interface>();
            byte[] inputXml;
            PNPlannerResult result = pnPlannerInputWriter.CreatePNPlannerInput(topIsland, interfacesOfSwitchNames, out inputXml);
            if ((result != null) && (result.ResultType != PNPlannerResultType.Successful))
            {
                return SyncDomainConsistencyChecker.ProcessPNPlannerResult(SyncDomain, result);
            }
            if (inputXml == null)
            {
                return true;
            }

            IntPtr inputPtr = Marshal.AllocHGlobal(Marshal.SizeOf(inputXml[0]) * inputXml.Length);
            Marshal.Copy(inputXml, 0, inputPtr, inputXml.Length);

            uint outputXmlLength = 0;
            uint errorXmlLength = 0;
            IntPtr errorPtr = IntPtr.Zero;
            IntPtr outputPtr = IntPtr.Zero;

            schedulePNData(
                inputPtr,
                inputXml.Length,
                ref outputPtr,
                ref outputXmlLength,
                ref errorPtr,
                ref errorXmlLength);

            PNPlannerOutputReader pnPlannerOutputReader = new PNPlannerOutputReader(this, m_PNPlannerHelper);

            byte[] errorBuffer = new byte[errorXmlLength];
            Marshal.Copy(errorPtr, errorBuffer, 0, (int)errorXmlLength);

            if (!pnPlannerOutputReader.ReadPNPlannerErrorXml(
                errorBuffer,
                (int)errorXmlLength))
            {
                Cleanup();
                return false;
            }
            byte[] outputBuffer = new byte[outputXmlLength];
            Marshal.Copy(outputPtr, outputBuffer, 0, (int)outputXmlLength);

            pnPlannerOutputReader.Read(outputBuffer, (int)outputXmlLength, topIsland, interfacesOfSwitchNames);
            
            if (HasDevicesUsingIrtTop)
            {
                PNPlannerResults.MaxRTC12FrameLength = m_PNPlannerHelper.GetMaxRTC12FrameLength(m_InterfaceFrameDataList);
            }
            SyncDomain.AttributeAccess.SetAnyAttribute<long>(InternalAttributeNames.PnIsoTcaValid,
                PNPlannerResults.CalculatedPartIrt);
            IEnumerable<Interface> iodInterfaces = SyncDomain.GetInterfaces().Where(i => i.PNIOD != null);
            foreach (Interface ifSubmodule in iodInterfaces)
            {
                MethodData methodDataCalculateIsochronParameters =
                    new MethodData { Name = CalculateIsochronParametersForGsdml.Name };
                ifSubmodule.BaseActions.CallMethod(methodDataCalculateIsochronParameters);
            }

            m_InterfaceFrameDataList.SetAllPNFrames(new InterfaceFrameDataList(topIsland.AllPNFrames));
            Cleanup();
            return true;
        }

        /// <summary>
        /// The method returns the BandwidthsForPhases for the controller interface submodule.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The controller interface submodule whose BandwidthsForPhases will be
        /// retrieved.
        /// </param>
        /// <returns>A list containing BandwidthsForPhases of the interface submodule.</returns>
        public IList<IPNPlannerOutputBandwidthsForPhases> GetBandwidthsForPhases(Interface controllerInterfaceSubmodule)
        {
            List<IPNPlannerOutputBandwidthsForPhases> bandwidthsForPhases = null;
            if (null != controllerInterfaceSubmodule)
            {
                string deviceName =
                    AttributeUtilities.GetSubmoduleNameWithContainerAndStation(controllerInterfaceSubmodule);
                if (PNPlannerResults.BandwidthsForPhases.ContainsKey(deviceName))
                {
                    bandwidthsForPhases = PNPlannerResults.BandwidthsForPhases[deviceName];
                }
            }
            return bandwidthsForPhases;
        }


        /// <summary>
        /// Gets the frames of a given controller interface submodule.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">The controller interface submodule whose frames will be retrieved.</param>
        /// <returns>A list containing the frames of the controller interface submodule.</returns>
        public IList<IPNFrameData> GetFramesOfController(Interface controllerInterfaceSubmodule)
        {
            if (m_InterfaceFrameDataList.AllPNFrames.ContainsKey(controllerInterfaceSubmodule))
            {
                return m_InterfaceFrameDataList.AllPNFrames[controllerInterfaceSubmodule];
            }
            return null;
        }

        public IList<IPNFrameData> GetFramesOfInterface(Interface interfaceSubmodule)
        {
            // Get the controller interface submodule
            bool isControllerInterfaceSubmodule = interfaceSubmodule != null
                                                  && interfaceSubmodule.ParentObject is CentralDevice;

            if (isControllerInterfaceSubmodule)
            {
                Interface controllerInterfaceSubmodule = isControllerInterfaceSubmodule
                                                             ? interfaceSubmodule
                                                             : NavigationUtilities.GetControllerOfDevice(
                                                                 interfaceSubmodule);

                return GetFramesOfController(controllerInterfaceSubmodule);
            }


            List<IPNFrameData> allFrames = new List<IPNFrameData>();
            foreach (var interfaceFramePair in m_InterfaceFrameDataList.AllPNFrames)
            {
                if (interfaceFramePair.Value != null)
                {
                    allFrames.AddRange(interfaceFramePair.Value);
                }
            }

            List<IPNFrameData> framesOfInterface = new List<IPNFrameData>();

            foreach (IPNFrameData frame in allFrames)
            {
                if (interfaceSubmodule != null
                    && frame.CoreIds.Contains(interfaceSubmodule.PNIOD.GetHashCode()))
                {
                    framesOfInterface.Add(frame);
                }
            }

            return framesOfInterface.Count <= 0 ? null : framesOfInterface;
        }

        /// <summary>
        /// The method returns the config2008 begin end assignment blocks for a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose config2008 begin end assignment blocks will be retrieved.</param>
        /// <param name="portNumber"></param>
        /// <returns>A list containing the begin end assigments for the corresponding config block.</returns>
        public IList<IPNPlannerOutputBeginEndAssignment> GetConfig2008BeginEndAssignmentBlocks(
            Interface interfaceSubmodule,
            int portNumber)
        {
            List<IPNPlannerOutputBeginEndAssignment> config2008BeginEndAssignments = null;
            if (interfaceSubmodule == null)
            {
                return null;
            }

            string deviceName = AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
            if (PNPlannerResults.Config2008BeginEndAssigmentBlocks.ContainsKey(deviceName))
            {
                if (PNPlannerResults.Config2008BeginEndAssigmentBlocks[deviceName].ContainsKey(portNumber))
                {
                    config2008BeginEndAssignments = PNPlannerResults.Config2008BeginEndAssigmentBlocks[deviceName][portNumber];
                }
            }
            return config2008BeginEndAssignments;
        }

        /// <summary>
        /// The method returns the config2008 frame blocks for a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose config2008 frame blocks will be retrieved.</param>
        /// <returns>A list containing frames for the corresponding config frame block.</returns>
        public IList<IPNPlannerOutputFrame> GetConfig2008FrameBlocks(Interface interfaceSubmodule)
        {
            List<IPNPlannerOutputFrame> config2008FrameBlocks = new List<IPNPlannerOutputFrame>();

            if (null != interfaceSubmodule)
            {
                string deviceName = AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
                if (PNPlannerResults.Config2008FramesBlocks.ContainsKey(deviceName))
                {
                    config2008FrameBlocks = PNPlannerResults.Config2008FramesBlocks[deviceName];
                }
            }

            return config2008FrameBlocks;
        }

        /// <summary>
        /// Returns the line rx delay of the given port, which is read from the PNPlanner output xml.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose port will be used.</param>
        /// <param name="portNumber">The number of the port whose rx delay will be retrieved.</param>
        /// <returns>The rx delay value of the port.</returns>
        public int GetConfig2008MaxLineRxDelays(Interface interfaceSubmodule, int portNumber)
        {
            if (interfaceSubmodule != null)
            {
                string deviceName = AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
                if (PNPlannerResults.Config2008MaxLineRxDelays.ContainsKey(deviceName)
                    && PNPlannerResults.Config2008MaxLineRxDelays[deviceName].ContainsKey(portNumber))
                {
                    return PNPlannerResults.Config2008MaxLineRxDelays[deviceName][portNumber];
                }
            }
            return 0;
        }

        /// <summary>
        /// The method returns the config2008 phase assignment blocks for a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose config2008 phase assignment blocks will be retrieved.</param>
        /// <param name="portNumber"></param>
        /// <returns>A list containing the phase assigments for the corresponding config block.</returns>
        public IList<IPNPlannerOutputPhaseAssignment> GetConfig2008PhaseAssignmentBlocks(
            Interface interfaceSubmodule,
            int portNumber)
        {
            List<IPNPlannerOutputPhaseAssignment> config2008PhaseAssignmentBlocks = null;
            if (interfaceSubmodule == null)
            {
                return null;
            }

            string deviceName = AttributeUtilities.GetSubmoduleNameWithContainerAndStation(interfaceSubmodule);
            if (PNPlannerResults.Config2008PhaseAssigmentBlocks.ContainsKey(deviceName))
            {
                if (PNPlannerResults.Config2008PhaseAssigmentBlocks[deviceName].ContainsKey(portNumber))
                {
                    config2008PhaseAssignmentBlocks = PNPlannerResults.Config2008PhaseAssigmentBlocks[deviceName][portNumber];
                }
            }

            return config2008PhaseAssignmentBlocks;
        }

        /// <summary>
        ///  The method builds the UUID of a Sync-Domain by connecting domain
        ///  specific attributes and running the MD5 algorithm.
        /// </summary>
        /// <remarks>
        ///  ReservedIntervalBegin   = 0
        ///  ReservedIntervalEnd     All synchronized devices uses the frame class 3 ==> 0
        ///  Otherwise ==> Reserved Bandwidth * 0.5 * Send Clock
        ///  PLLWindow               = 0x3e8
        ///  SyncSendFactor          = 0x400
        ///  SendClockFactor         = SendClockFactor
        ///  PTCPTimeoutFactor       = 6
        ///  SyncDomainName          = SyncDomainName
        /// </remarks>
        /// <returns>A byte array containing the UUID.</returns>
        public byte[] GetSyncDomainUuid()
        {
            // Create the input text and add the interval start.
            string syncDomainUuidText = s_ReservedIntervalBegin.ToString(CultureInfo.InvariantCulture);

            // Get the actual Send Clock Value.
            long sendClock = SendClock;

            // Add the interval end.
            syncDomainUuidText += s_ReservedIntervalEnd;

            // Add the PLL Window.
            syncDomainUuidText += s_PllWindow.ToString(CultureInfo.InvariantCulture);

            // Add the Sync Send Factor.
            syncDomainUuidText += s_SyncSendFactor.ToString(CultureInfo.InvariantCulture);

            // Add the Send Clock Factor.
            syncDomainUuidText += Convert.ToString(sendClock / s_SendClockGetFactor, CultureInfo.InvariantCulture);

            // Add the PTCP Timeout Factor.
            syncDomainUuidText += s_PtcpTimeoutFactor.ToString(CultureInfo.InvariantCulture);

            // Add the Sync-Domain Name.
            syncDomainUuidText += DomainName;

            // Run the MD5 algorithm and return the result.
            byte[] syncDomainUuid;
            if (!MD5Encryptor.Encrypt(syncDomainUuidText, out syncDomainUuid))
            {
                // If the encryption is unsuccessful, return the default UUID.
                syncDomainUuid = new byte[] { 16, 30, 152, 171, 7, 133, 109, 183, 150, 125, 20, 212, 31, 83, 18, 192 };
            }

            return syncDomainUuid;
        }

        /// <summary>
        /// Initialization method.
        /// </summary>
        private void InitBL()
        {
            InitAttributes();
        }

        /// <summary>
        /// Checks if the given port of the interface submodule is an IrtTop port.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose port will be checked.</param>
        /// <param name="portNo">The number of the port that will be checked.</param>
        /// <returns>True if port is IrtTop; false otherwise.</returns>
        public bool IsIrtTopPort(Interface interfaceSubmodule, int portNo)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            HashSet<PortElement> irtTopPortsOfInterface;
            if (PNPlannerResults.AllIrtTopPorts.TryGetValue(
                interfaceSubmodule.GetHashCode(),
                out irtTopPortsOfInterface))
            {
                if (irtTopPortsOfInterface == null)
                {
                    return false;
                }

                return irtTopPortsOfInterface.Any(port => port.Nr == portNo);
            }
            return false;
        }

        /// <summary>
        /// Checks if the given port of the interface submodule is using short preamble.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose port will be checked.</param>
        /// <param name="portNo">The number of the port that will be checked.</param>
        /// <returns>True if port is using short preamble; false otherwise.</returns>
        public bool IsPortUsingShortPreamble(Interface interfaceSubmodule, int portNo)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            HashSet<PortElement> irtTopPortsOfInterface;

            if (PNPlannerResults.AllIrtTopPorts.TryGetValue(
                interfaceSubmodule.GetHashCode(),
                out irtTopPortsOfInterface))
            {
                if (irtTopPortsOfInterface == null)
                {
                    return false;
                }

                PortElement actualPort = irtTopPortsOfInterface.FirstOrDefault(port => port.Nr == portNo);

                return actualPort.UsingShortPreamble;
            }

            return false;
        }

        /// <summary>
        /// Gets max available bandwidth level.
        /// </summary>
        /// <returns>Max available bandwidth level.</returns>
        internal PNIRTBandwidthLevel GetMaxAvailableBandwidthLevel(bool expertModeFree = false)
        {
            PNIRTBandwidthLevel maxAvailableLevel = PNIRTBandwidthLevel.MaximumIO;
            AttributeAccessCode ac = new AttributeAccessCode();
            if (!expertModeFree)
            {
                bool expertModeEnabled =
                    PCLObject.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIrtSyncDomainExpertMode,
                        ac.GetNew(),
                        false);
                //If Expert mode enabled than More IO also available
                maxAvailableLevel = expertModeEnabled ? maxAvailableLevel : PNIRTBandwidthLevel.Fair;
            }

            //Search for the minimum of maxiumums
            foreach (Interface interfaceSubmodule in IOControllerInterfaces)
            {
                PNIRTBandwidthLevel currentLevel =
                    (PNIRTBandwidthLevel)
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxSuppBwLevel,
                        ac.GetNew(),
                        (uint)PNIRTBandwidthLevel.Fair);

                if (currentLevel < maxAvailableLevel)
                {
                    maxAvailableLevel = currentLevel;
                }
            }

            return maxAvailableLevel;
        }

        /// <summary>
        /// Gets the selected send clock factor for the sync domain.
        /// </summary>
        /// <param name="sendClockFactor">The output parameter that will contain the send clock factor.</param>
        /// <returns>True if send clock factor is retrieved successfully; false otherwise.</returns>
        private bool TryGetSelectedSendClockfactor(out long sendClockFactor)
        {
            sendClockFactor = 0;
            if (SyncMaster == null)
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            Interface syncMaster = SyncMaster;

            uint pdevSetting =
                syncMaster.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPdevParametrizationDecentral,
                    ac,
                    0);

            Interface controllerInterfaceSubmodule = Utility.IsProfinetDeviceInterfaceSubmodule(syncMaster)
                                                     && (!ac.IsOkay || (pdevSetting != 0))
                                                         ? NavigationUtilities.GetControllerOfDevice(syncMaster)
                                                         : syncMaster;

            sendClockFactor = controllerInterfaceSubmodule == null
                                  ? PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor
                                  : AttributeUtilities.GetTransientPNSendClockFactor(
                                      controllerInterfaceSubmodule,
                                      PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor);

            if (controllerInterfaceSubmodule == null)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Find the maximum of a given attribute of all objects within an object collection.
        /// </summary>
        /// <typeparam name="T">Type of the given attribute.</typeparam>
        /// <param name="objectCollection">Collection of objects where the max value will be found.</param>
        /// <param name="attributeName">Name of the attribute to be searched.</param>
        /// <param name="defaultValue">Value to be used if for an object the attribute does not exist.</param>
        /// <param name="defaultValueCollectionEmpty">Value to be used if the complete collection is empty or null.</param>
        /// <returns>Maximum value within the objectCollection.</returns>
        private static T FindMaxAttribute<T>(
            IEnumerable<PclObject> objectCollection,
            string attributeName,
            T defaultValue,
            T defaultValueCollectionEmpty) where T : IComparable<T>
        {
            if ((objectCollection == null)
                || !objectCollection.Any())
            {
                return defaultValueCollectionEmpty;
            }

            // For YellowTime, IO-Controllers use another attribute, i.e. InternalAttributeNames.PnIrtMinYellowTimeIOC
            bool isYellowtimeAtt = attributeName == InternalAttributeNames.PnIrtMinYellowTime;

            T max = default(T);
            AttributeAccessCode ac = new AttributeAccessCode();
            foreach (PclObject obj in objectCollection)
            {
                T value;
                if (isYellowtimeAtt && Utility.IsProfinetControllerInterfaceSubmodule(obj as Interface))
                {
                    value = obj.AttributeAccess.GetAnyAttribute<T>(
                        InternalAttributeNames.PnIrtMinYellowTimeIOC,
                        ac,
                        defaultValue);
                }
                else
                {
                    value = obj.AttributeAccess.GetAnyAttribute<T>(attributeName, ac, defaultValue);
                }
                if (ac.IsOkay
                    && (value.CompareTo(max) > 0))
                {
                    max = value;
                }
            }
            return max.Equals(default(T)) ? defaultValue : max;
        }

        /// <summary>
        /// Sets redundancy level of all RTC3 frames to 2 and generates temporary frame ids.
        /// </summary>
        /// <param name="interfaceFrameDataList">List of frames for which the temporary frame ids will be generated.</param>
        private static void GenerateTemporaryPNPlannerFrameIds(InterfaceFrameDataList interfaceFrameDataList)
        {
            int nextIrtID = (int)PNRTFrameIdLimits.RTClass3FrameId.RedundantMin;
            int nextRtID = (int)PNRTFrameIdLimits.RTClass2FrameId.Min;
            foreach (List<IPNFrameData> pnFrameDataOfController in interfaceFrameDataList.AllPNFrames.Values)
            {
                foreach (IPNFrameData frameData in pnFrameDataOfController)
                {
                    if (frameData.FrameClass == (long)PNIOFrameClass.Class3Frame)
                    {
                        frameData.FrameID = nextIrtID++;
                        frameData.RedundantFrameID = nextIrtID++;
                        frameData.FrameMultiplier *= 2;
                    }
                    else
                    {
                        frameData.FrameID = nextRtID++;
                        frameData.RedundantFrameID = nextRtID++;
                    }
                }
            }
        }

        /// <summary>
        /// Checks whether fragmentation is supported for a given interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule which will be checked.</param>
        /// <param name="isIODevice">Whether the interface submodule belongs to an IO device.</param>
        /// <returns>True if fragmentation is supported; false otherwise.</returns>
        private bool CheckFragmentationSupport(PclObject interfaceSubmodule, bool isIODevice)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool checkFragmentationNone;
            PNIRTFragmentationType fragmentationType;
            if (isIODevice)
            {
                // Check PNIrtFragmentationType
                Enumerated fragType = 
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtFragmentationType,
                        ac.GetNew(),
                        null);
                if(fragType == null)
                {
                    fragmentationType = PNIRTFragmentationType.None;
                }
                else
                {
                    fragmentationType = (PNIRTFragmentationType)(byte)fragType.DefaultValue;
                }

                checkFragmentationNone = fragmentationType == PNIRTFragmentationType.None;
            }
            else
            {
                // Check PNIrtFragmentationMode
                uint fragMode =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtFragmentationMode,
                        ac.GetNew(),
                        (uint)PNIRTFragmentationMode.None);
                PNIRTFragmentationMode fragmentationMode = (PNIRTFragmentationMode)(byte)fragMode;
                checkFragmentationNone = fragmentationMode == PNIRTFragmentationMode.None;
            }

            if (checkFragmentationNone)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// Creates frame objects for use in PNPlannerInputXMLWriter.
        /// </summary>
        /// <param name="checkData">
        /// MethodData object that keeps all the inputs, outputs and other related data used in this
        /// method.
        /// </param>
        /// <param name="controllerInterfaceSubmodules">
        /// The list that keeps the controller interfaces for which the frames will be
        /// generated.
        /// </param>
        /// <returns>A dictionary of interfaces and the frames that belong to them.</returns>
        private Dictionary<Interface, List<IPNFrameData>> CreateFrames(
            IMethodData checkData,
            IList<Interface> controllerInterfaceSubmodules)
        {
            Dictionary<Interface, List<IPNFrameData>> allPNFrames = new Dictionary<Interface, List<IPNFrameData>>();

            foreach (Interface interfaceSubmodule in controllerInterfaceSubmodules)
            {
                MethodData methodDataPrepareStep = new MethodData();
                methodDataPrepareStep.Name = PrepareForPNPlanner.Name;
                methodDataPrepareStep.Arguments[PNConstants.IsCompile] = checkData != null;
                interfaceSubmodule.BaseActions.CallMethod(methodDataPrepareStep);
                if ((checkData != null)
                    && (checkData.Arguments[PrepareForPNPlanner.FrameLengthColl] == null))
                {
                    checkData.Arguments[PrepareForPNPlanner.FrameLengthColl] =
                        methodDataPrepareStep.Arguments[PrepareForPNPlanner.FrameLengthColl];
                }
                List<IPNFrameData> pnFrameDataListOfController =
                    (List<IPNFrameData>)methodDataPrepareStep.Arguments[PrepareForPNPlanner.PNFrameDataList];
                if (pnFrameDataListOfController != null)
                {
                    allPNFrames.Add(interfaceSubmodule, pnFrameDataListOfController);
                }
            }
            return allPNFrames;
        }

        /// <summary>
        /// Initialize attributes for the sync domain.
        /// </summary>
        private void InitAttributes()
        {
            // Create the PNRtResCyclicBandwidth Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnResSyncCyclicBandwidth, 0);

            // Create the PNMaxCyclicDataBw Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnMaxIoCyclicBandwidth, 0);

            // Create the GetPNSyncDomainConfiguration Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<byte>(InternalAttributeNames.PnSyncDomainConfiguration, 0);

            // Create the attribute PNContainsSynchronizedItems.
            PCLObject.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnContainsSynchronizedItems, false);

            // Create min. frame length attribute
            PCLObject.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnPnPlannerMinFrameLength, 0);

            // Create the PNIOSendClockFactor Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoSendClockFactor, 0);

            // Create the PNIsoTcaValid Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIsoTcaValid, 0);

            // Create the attribute for SyncDomain-UUID.
            PCLObject.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.PnSyncDomainUuid, string.Empty);

            // Create the PNResSyncCyclicRatio Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<long>(
                InternalAttributeNames.PnResSyncCyclicRatio,
                s_ResSyncCyclicRatioDefault);

            // Create the attribute PNIrtMrpdDiagnosis 
            PCLObject.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIrtMrpdDiagnosis, false);

            // Create the attribute PNIrtSyncDomainExpertMode
            // Expert mode is always treated as active in PNConfigLib.
            PCLObject.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIrtSyncDomainExpertMode, false);

            // Create the attribute PNIrtSyncDomainFFW 
            PCLObject.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIrtSyncDomainFFW, false);

            // Create the PNIrtBandwidthLevel Attribute
            PCLObject.AttributeAccess.AddAnyAttribute<byte>(InternalAttributeNames.PnIrtBandwidthLevel, 0);

            PCLObject.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.PnPnPlannerMinInterLsduGap,
                PNPlannerConstants.AttributeValues.PNPNPlannerMinInterLsduGap);
            PCLObject.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.PnPnPlannerByteLengthFactor,
                PNPlannerConstants.AttributeValues.PNPNPlannerByteLengthFactor);
            PCLObject.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.PnPnPlannerStartAlignedBorder,
                PNPlannerConstants.AttributeValues.PNPNPlannerStartAlignedBorder);
            PCLObject.AttributeAccess.AddAnyAttribute<string>(
                InternalAttributeNames.PnPnPlannerAdditionalLinkDelay,
                PNPlannerConstants.AttributeValues.PNPNPlannerAdditionalLinkDelay);
            PCLObject.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnPnPlannerMaxFrameLength,
                PNPlannerConstants.AttributeValues.PNPNPlannerMaxFrameLength);
        }

        #region Dll Import

        /// <summary>
        /// Entry point for running PNPlanner algorithm.
        /// </summary>
        /// <param name="inputXml">Pointer to the start of the byte array containing PNPlanner input.</param>
        /// <param name="inputLen">Length of the PNPlanner input.</param>
        /// <param name="ppOutputXml">Pointer to the start of the byte array containing PNPlanner output.</param>
        /// <param name="ppOutputLen">Length of the PNPlanner output.</param>
        /// <param name="errorXml">Pointer to the start of the byte array containing PNPlanner error output.</param>
        /// <param name="errorLen">Length of the PNPlanner error output.</param>
        /// <returns>
        /// A return code specifying the result of the PNPlanner call. If this return code is:
        /// - Equal to 0, the planning algorithm ran successfully.
        /// - Is greater than 0, the algorithm ran with warnings.
        /// - Is less than 0, the algorithm encountered an error.
        /// See PNPlanner documentation for the exact return codes and their corresponding warnings and errors.
        /// </returns>
        [DllImport("PNPlanner", CallingConvention = CallingConvention.Cdecl)]
        private static extern int schedulePNData(
            IntPtr inputXml,
            int inputLen,
            ref IntPtr ppOutputXml,
            ref uint ppOutputLen,
            ref IntPtr errorXml,
            ref uint errorLen);

        /// <summary>
        /// Entry point for the cleanup method of PNPlanner algorithm.
        /// </summary>
        /// <returns>
        /// A return code specifying the result of the PNPlanner call. If this return code is:
        /// - Equal to 0, the planning algorithm ran successfully.
        /// - Is greater than 0, the algorithm ran with warnings.
        /// - Is less than 0, the algorithm encountered an error.
        /// See PNPlanner documentation for the exact return codes and their corresponding warnings and errors.
        /// </returns>
        [DllImport("PNPlanner")]
        private static extern int Cleanup();

        #endregion

        #region Bandwidth Distribution

        /// <summary>
        /// Default value for reserved IRT bandwidth ratio (in percentage).
        /// </summary>
        private const long s_ResSyncCyclicRatioDefault = 30;

        /// <summary>
        /// Send clock get factor used for converting send clock value to nanoseconds.
        /// </summary>
        private const int s_SendClockGetFactor = 31250;

        /// <summary>
        /// Send clock limit for Fast IO.
        /// </summary>
        private const int s_SendClockLowClassLimit = 250000; // In nanoseconds

        /// <summary>
        /// Default value used for determining whether the current send clock is incorrect.
        /// </summary>
        private const long s_SendClockFactorErrorValue = -1;

        /// <summary>
        /// Default value for send clock.
        /// </summary>
        private const long s_SendClockDefaultValue = 1000000; // In nanoseconds

        /// <summary>
        /// Default value for reserved IRT bandwidth (in nanoseconds).
        /// </summary>
        private const int s_DefaultMaxCyclicBandwidth = 500000; // In nanoseconds

        #endregion

        #region Sync-Domain UUID

        /// <summary>
        /// Values used for UUID generation.
        /// </summary>
        private const uint s_ReservedIntervalBegin = 0;

        private const uint s_ReservedIntervalEnd = 0;

        private const uint s_PllWindow = 0x3E8;

        private const uint s_SyncSendFactor = 0x3C0;

        private const int s_PtcpTimeoutFactor = 6;

        #endregion
    }
}