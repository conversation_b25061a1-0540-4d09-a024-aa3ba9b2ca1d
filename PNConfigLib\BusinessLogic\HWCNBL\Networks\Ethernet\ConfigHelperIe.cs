/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigHelperIe.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Text;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Networks.Ethernet.Common;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.PNFunctions._Interfaces;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;
using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.Networks.Ethernet
{
    internal class ConfigHelperIe
    {
        #region Fields

        private readonly NodeIeBusinessLogic m_NodeIeBusinessLogic;

        #endregion

        public ConfigHelperIe(NodeIeBusinessLogic nodeIeBusinessLogic)
        {
            m_NodeIeBusinessLogic = nodeIeBusinessLogic;
        }

        /// <summary>
        /// Get the content for subblock 0xA201 (DEV_NAME_OF_STATION) of Config2002 H1 Busparams
        /// </summary>
        /// <param name="methodData"></param>
        public void GetDeviceNameOfStationBlock(IMethodData methodData)
        {
            // methodData can not be null..
            if (methodData == null)
            {
                return;
            }

            methodData.ReturnValue = false;

            BlobCreator block = new BlobCreator();

            #region Block Header

            byte[] blockHeader = new byte[s_HeaderLength];
            blockHeader[0] = EthernetBlockIdentifiers.DeviceNamesBlockId;
            block.SerializeByteArray(blockHeader);

            #endregion

            BlobCreator subblock = new BlobCreator();

            // create the Name Of Station subblock
            subblock.SerializeByteArray(CreateSubblockHeader(EthernetSubblockIdentifiers.DeviceNamesSubblockId));

            string deviceName;

            // For PNIpConfig: If the option "Name Of Station via other method" activated, 
            // For Coupled IDevice, Sub Ordinate IO-System Address Tailoring,
            // then the value will be set the NameOfStationLen as "1", and the deviceName as "".
            if (CheckIfAddressTailored()
                || IsCoupledIDeviceSubOrdinateIOSystemAddressTailored())
            {
                subblock.SerializeByte(1, 4);
                subblock.Index += 3;
            }
            else
            {
                int sizeOfLengthField = 2;
                PNNameOfStationConverter.EncodePNNameOfStation(
                    m_NodeIeBusinessLogic.GetPNNameOfStation(),
                    null,
                    null,
                    out deviceName);

                if (string.IsNullOrEmpty(deviceName))
                {
                    return;
                }

                subblock.SerializeStringExt(deviceName, sizeOfLengthField);
                // serializes the name led in by 2 bytes of length indication  
            }

            //2 Bytes padding
            int padding = subblock.Index % 2;
            if (padding > 0)
            {
                subblock.SerializeByteArray(new byte[padding]);
            }

            subblock.BigEndian = false; // length indication in Intel format
            InsertLengthIndication(subblock, s_IgnoreHeaderLength);

            block.SerializeByteArray(subblock.ToByteArray());

            InsertLengthIndication(block, true);

            if (Convert.ToBoolean(methodData.Arguments[NodeGetDeviceNameOfStationBlock.IsNewGenerationDevice], CultureInfo.InvariantCulture))
            {
                methodData.ReturnValue = block.ToByteArray();
            }
            else
            {
                methodData.Arguments[NodeGetDeviceNameOfStationBlock.DeviceNameOfStationBlockEntry] =
                    block.ToByteArray();
                methodData.ReturnValue = true;
            }
        }

        /// <summary>
        /// Method to get Network Parameter Block.
        /// </summary>
        /// <param name="alignment"></param>
        /// <param name="isDecentral"></param>
        /// <param name="blockversion"></param>
        /// <param name="index"></param>
        /// <param name="isControllerSupportBv0X0100"></param>
        /// <returns></returns>
        public byte[] GetNetworkParameters(
            bool alignment,
            bool isDecentral,
            int blockversion,
            int index,
            bool isControllerSupportBv0X0100)
        {
            switch (index)
            {
                case 0x1000:
                    return GetIpSuiteRecord(isDecentral, blockversion);
                case 0x1001:
                    return GetIpAddressValidationLocalBlock(alignment, blockversion);
                case 0x1003:
                    return GetNameOfStationBlock(alignment, blockversion, isDecentral);
                case 0x1004:
                    return GetNameOfStationValidationBlock(alignment, blockversion);
                case 0x1005:
                    return GetTimeSyncRecord(alignment, blockversion);
                case 0x1006:
                    return GetNameOfStationAliasBlock(blockversion);
                case 0x1007:
                    return GetIpAddressValidationRemoteBlock(isControllerSupportBv0X0100);
                default:
                    Debug.Assert(
                        index != 0,
                        string.Format(CultureInfo.InvariantCulture, "Missing Networksparam methode Index {0} must be defined!", index));
                    return null;
            }
        }

        /// <summary>
        /// Get Ip Address Validation Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetIpAddressValidationLocalBlock(bool alignment, int blockversion)
        {
            IpAddressValidationLocalStruct block = new IpAddressValidationLocalStruct();
            block.BlockVersion = blockversion;

            block.IPAddressValidation = (ushort)GetIpAddressValidationValue();

            if (alignment)
            {
                int mAlignment = BufferManager.Alignment(block.ToByteArray.Length, 4);

                if (mAlignment != 0)
                {
                    block.AddSubBlock(new byte[mAlignment]);
                }
            }
            return block.ToByteArray;
        }

        /// <summary>
        /// Get Ip Address Validation Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetIpAddressValidationRemoteBlock(bool isControllerSupportBv0X0100)
        {
            bool alignment = false;
            int blockversion = isControllerSupportBv0X0100 ? 0x0100 : 0x0101;
            if (!isControllerSupportBv0X0100)
            {
                alignment = true;
            }

            int dataLength = blockversion == 0x0100 ? 2 : 4;
            IpAddressValidationRemoteStruct block = new IpAddressValidationRemoteStruct(dataLength);

            block.BlockLength += dataLength;
            block.BlockVersion = blockversion;

            ushort validationValue = 0x0000;
            ushort propertiesValue = 0x0000;

            if (m_NodeIeBusinessLogic.IsPNIoIpSuiteViaOtherPathActive)
            {
                validationValue = 0xFFFF;
            }

            Interface interfaceSubmodule = m_NodeIeBusinessLogic.Node.ParentObject as Interface;
            IPNDeviceConfigStrategy configStrategy = null;
            if (interfaceSubmodule != null)
            {
                IFDecorator decentralBL = interfaceSubmodule.InterfaceBL as IFDecorator;
                if (decentralBL != null)
                {
                    configStrategy = decentralBL.Strategy;
                }
            }
            if (configStrategy == null)
            {
                return new byte[0];
            }
            //Address mode flags
            //Bit Offset 0	IP issue 0: The IO controller issues IP Suite 1: The IO controller uses the found IP Suite
            //Bit Offset 1	Search protocol	0: The IO controller searches with DCP  
            //Bit Offset 2		Don’t care.
            //Bit Offset 3	Reserved	
            //Bit Offset 4	set IP Suite remanent	0: set IP Suite temporary 1: set IP Suite remanent, e.g. FSU is activated PNDeviceFSUPriority
            //Bit Offset 5..31	Reserved	
            uint addressModeFlags = configStrategy.GetDeviceAddressMode();

            if ((validationValue == 0x0000)
                && ((addressModeFlags & 5) == 5))
            {
                // AddressModeFlags == 5 indicate that the IODevice does not want to receive its
                // IP configuration from the controller, but manages its IP configuration by itself
                // (usually because it is an iDevice). To prevent the controller from sending the
                // IP configuration to this IODevice, the configuration is set to 'ipSuiteViaOtherPath'.
                validationValue = 0xFFFF;
            }

            //When AddressTailoring is enabled we use use 0 for the validation block in all cases.
            if ((m_NodeIeBusinessLogic.AddressTailoringEnabled && !m_NodeIeBusinessLogic.IsPNIoIpSuiteViaOtherPathActive)
                || m_NodeIeBusinessLogic.IsAddressTailoredIDevice)
            {
                validationValue = 0x0000;
            }

            block.Validation = validationValue;

            //Support at Blockversion 0x0101
            if (blockversion > 0x0100)
            {
                //set if IP suite permanent
                if ((addressModeFlags & 0x00000010) == 0x00000010)
                {
                    propertiesValue = 1;
                }
                block.Properties = propertiesValue;
            }
            if (alignment)
            {
                int mAlignment = BufferManager.Alignment(block.ToByteArray.Length, 4);

                if (mAlignment != 0)
                {
                    block.AddSubBlock(new byte[mAlignment]);
                }
            }
            return block.ToByteArray;
        }

        /// <summary>
        /// Get IpSuite Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetIpSuiteRecord(bool isDecentral, int blockversion)
        {
            if ((!isDecentral && m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive
                 || isDecentral && m_NodeIeBusinessLogic.IsPNIoIpSuiteViaOtherPathActive)
                && !(isDecentral && m_NodeIeBusinessLogic.IsAddressTailoredIDevice))
            {
                return new byte[0];
            }

            IpSuiteStruct block = new IpSuiteStruct();
            block.BlockVersion = blockversion;

            Array.Reverse(GetIpAddressValue());
            // IPAddress
            byte[] bGetIpAddressValue = GetIpAddressValue();
            Array.Reverse(bGetIpAddressValue);
            block.IpAddress = BitConverter.ToUInt32(bGetIpAddressValue, 0);

            // SubnetMask
            byte[] bGetSubnetMaskValue = GetSubnetMaskValue();
            Array.Reverse(bGetSubnetMaskValue);
            block.SubnetMask = BitConverter.ToUInt32(bGetSubnetMaskValue, 0);

            bool isRouterUsed = false;

            //DefaultGateway
            // write IP instead of 0 as default gateway if it is not used.
            if (isDecentral)
            {
                if (!m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive)
                {
                    isRouterUsed = true;
                }
            }
            else
            {
                isRouterUsed = m_NodeIeBusinessLogic.IPDefaultRouterAddressUsed;
            }

            if (isRouterUsed)
            {
                byte[] bGetDefaultRouterAddressValue = GetDefaultRouterAddressValue();
                Array.Reverse(bGetDefaultRouterAddressValue);
                block.DefaultGateway = BitConverter.ToUInt32(bGetDefaultRouterAddressValue, 0);
            }
            else
            {
                block.DefaultGateway = block.IpAddress;
            }

            return block.ToByteArray;
        }

        /// <summary>
        /// Get NameOfStationAlias Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetNameOfStationAliasBlock(int blockversion)
        {
            int nameAliasCount = 0;
            Interface deviceItfSubmodule = m_NodeIeBusinessLogic.Node.ParentObject as Interface;

            if (deviceItfSubmodule == null)
            {
                return new byte[0];
            }

            IFDecorator decorator = deviceItfSubmodule.InterfaceBL as IFDecorator;
            IPNDeviceConfigStrategy configStrategy = null;
            if (decorator != null)
            {
                configStrategy = decorator.Strategy;
            }
            if (configStrategy == null)
            {
                return new byte[0];
            }

            // ExchangeWithoutMMCSupported isn't required/supported
            if (
                !(configStrategy.IsExchangeWithoutMMCSupported()
                  || MachineTailorUtility.HasInterfaceProgrammablePeer(deviceItfSubmodule)))
            {
                // set empty return arguments
                return new byte[0];
            }

            // if the device doesn't supports the PortInterconnection (PNIoSubmoduleModelSupp)
            // then we don't generate the StationNameAliasTableBlock.
            if (
                !deviceItfSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSubmoduleModelSupp,
                    new AttributeAccessCode(),
                    false))
            {
                // set empty return arguments
                return new byte[0];
            }

            StationNameAliasStruct block = new StationNameAliasStruct();
            block.BlockVersion = blockversion;

            IList<byte[]> aliasEntries = configStrategy.GetStationNameAliases();
            if ((aliasEntries == null)
                || (aliasEntries.Count == 0))
            {
                return new byte[0];
            }
            foreach (byte[] stationAlias in aliasEntries)
            {
                //add subblocks
                block.AddSubBlock(stationAlias);
                nameAliasCount++;
            }
            block.AliasNameCount = nameAliasCount;

            return block.ToByteArray;
        }

        /// <summary>
        /// Get NameOfStation Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetNameOfStationBlock(bool alignment, int blockversion, bool isDecentral)
        {
            if (!isDecentral
                && (m_NodeIeBusinessLogic.IsPNPNNoSViaOtherPathActive || IsCoupledIDeviceSubOrdinateIOSystemAddressTailored()))
            {
                return new byte[0];
            }

            NameOfStationStruct block = new NameOfStationStruct();

            block.BlockType = EthernetSubblockIdentifiers.DeviceNamesSubblockId;
            block.BlockVersion = blockversion;

            IMethodData methodeData = new MethodData();
            methodeData.Arguments[NodeGetDeviceNameOfStationBlock.IsNewGenerationDevice] = true;
            GetDeviceNameOfStationBlock(methodeData);
            byte[] data = methodeData.ReturnValue as byte[];

            if (data == null)
            {
                return new byte[0];
            }

            int nameOfStationLength = data[8] + data[9];
            block.NameOfStationLength = nameOfStationLength;
            block.Padding = 0;
            byte[] subblock = new byte[nameOfStationLength];
            Array.Copy(data, 10, subblock, 0, nameOfStationLength);

            block.AddSubBlock(subblock);

            if (alignment)
            {
                int mAlignment = BufferManager.Alignment(block.ToByteArray.Length, 4);

                if (mAlignment != 0)
                {
                    block.AddSubBlock(new byte[mAlignment]);
                }
            }
            return block.ToByteArray;
        }

        /// <summary>
        /// Get NameOfStation Validation Block
        /// </summary>
        /// <returns></returns>
        private byte[] GetNameOfStationValidationBlock(bool alignment, int blockversion)
        {
            NameOfStationValidationStruct block = new NameOfStationValidationStruct();

            block.BlockVersion = blockversion;

            block.IPAddressValidation = (ushort)GetNameOfStationValidationValue();

            if (alignment)
            {
                int mAlignment = BufferManager.Alignment(block.ToByteArray.Length, 4);

                if (mAlignment != 0)
                {
                    block.AddSubBlock(new byte[mAlignment]);
                }
            }
            return block.ToByteArray;
        }

        /// <summary>
        /// Get TimeSync for
        /// </summary>
        /// <returns></returns>
        private byte[] GetTimeSyncRecord(bool alignment, int blockversion)
        {
            Interface interfaceSubmodule = m_NodeIeBusinessLogic.Node.ParentObject as Interface;

            if (interfaceSubmodule == null)
            {
                return new byte[0];
            }

            byte timeSyncRole =
                (byte)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.TimeSyncRole,
                    new AttributeAccessCode(),
                    0);
            // don't fill TimeSync recoder if NTP mode disabled
            if (timeSyncRole == 0)
            {
                return new byte[0];
            }

            TimeSyncStruct block = new TimeSyncStruct();

            block.BlockVersion = blockversion;
            //-----------------------------------------------------------------------------
            // Sub-block COMMON_SYNCH_PARAMETERS (length 16)
            //-----------------------------------------------------------------------------
            block.TimeSyncRole = timeSyncRole;
            uint updatingIntervalClient =
                (uint)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.TimeSyncInterval,
                    new AttributeAccessCode(),
                    0);
            // Value between 10 .. 86400
            if (updatingIntervalClient < 10)
            {
                return new byte[0];
            }
            block.AsNtpUpdatingIntervalClient = updatingIntervalClient;

            //subblockConfig.AsNtpNumberOfServers = 4;
            //-----------------------------------------------------------------------------
            // For each configured NTP server (4 x 8 == length 32)
            //-----------------------------------------------------------------------------
            //version 11
            //Wrong, should be dynamically countNTPServerx8 = ?? length, max 32; 

            string[] asTimeSyncNtpServers =
                {
                    InternalAttributeNames.AsNtpServerAddress1,
                    InternalAttributeNames.AsNtpServerAddress2,
                    InternalAttributeNames.AsNtpServerAddress3,
                    InternalAttributeNames.AsNtpServerAddress4
                };

            int countNTPServer = 0;
            NtpServerConfigSubblockStruct subsubblock;
            foreach (string astimeSyncNtpServer in asTimeSyncNtpServers)
            {
                uint asNtpServerAddress = interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    astimeSyncNtpServer,
                    new AttributeAccessCode(),
                    0);
                if (asNtpServerAddress != 0)
                {
                    countNTPServer++;
                    subsubblock = new NtpServerConfigSubblockStruct();

                    subsubblock.FormatServerAddress = 0x00;
                    subsubblock.LengthServerAddress = 0x04;
                    subsubblock.AsNtpServerAddress = asNtpServerAddress;
                    block.AddSubBlock(subsubblock.ToByteArray);
                }
            }
            block.BlockLength_NTP_SERVER_CONFIG += countNTPServer * 8;
            block.AsNtpNumberOfServers = countNTPServer;

            if (alignment)
            {
                int mAlignment = BufferManager.Alignment(block.ToByteArray.Length, countNTPServer);

                if (mAlignment != 0)
                {
                    block.AddSubBlock(new byte[mAlignment]);
                }
            }

            return block.ToByteArray;
        }

        #region Constants and Enums

        private const bool s_IgnoreHeaderLength = false;

        private const int s_HeaderLength = 4;

        #endregion

        /// <summary>
        /// returns the value of SubnetMask, according to PNIpSuite selection.
        /// If the option "IP-Suite via other method" activated, then the value will be set as "0".
        /// </summary>
        /// <returns></returns>
        private byte[] GetSubnetMaskValue()
        {
            byte[] subnetMask;

            // For PNIpConfig: If the option "IP-Suite via other method" activated, then the value will be set as "0".
            if (m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive
                && !m_NodeIeBusinessLogic.IsAddressTailoredIDevice)
            {
                subnetMask = new IPSubnetMask(0).ToByteArray();
            }
            else
            {
                subnetMask = m_NodeIeBusinessLogic.IPSubnetMask.ToByteArray();
            }

            return subnetMask;
        }

        /// <summary>
        /// Inserts a block's length indication into bytes 2-3.
        /// </summary>
        /// <param name="block">current block</param>
        /// <param name="regardHeaderLength">true in case the value shall be inserted in big endian format; false otherwise</param>
        private static void InsertLengthIndication(BlobCreator block, bool regardHeaderLength)
        {
            int length = block.Index;
            if (!regardHeaderLength)
            {
                Debug.Assert(block.Index >= s_HeaderLength);
                length -= s_HeaderLength;
            }
            // insert length indication into bytes 2-3
            block.SerializeIntExt(length, 2, 2);
        }

        /// <summary>
        /// returns the value of IPAddress, according to PNIpSuite selection.
        /// For PNIpConfig: If the option "IP-Suite via other method" activated, then the value will be set as "0".
        /// </summary>
        /// <returns></returns>
        private byte[] GetIpAddressValue()
        {
            byte[] ipAddress;

            // For PNIpConfig: If the option "IP-Suite via other method" activated, then the value will be set as "0".
            if (m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive
                && !m_NodeIeBusinessLogic.IsAddressTailoredIDevice)
            {
                ipAddress = new IPAddress(0).ToByteArray();
            }
            else
            {
                ipAddress = m_NodeIeBusinessLogic.IPAddress.ToByteArray();
            }

            return ipAddress;
        }

        /// Creates a subblock header.
        /// <param name="id">subblock id</param>
        /// <returns>subblock header as a byte buffer</returns>
        /// <information>
        /// a subblock header looks like that:
        /// +---------------------------------------+
        /// |				id (word)				|
        /// +---------------------------------------+
        /// |		length indication (word)		|
        /// +---------------------------------------+
        /// </information>
        private static byte[] CreateSubblockHeader(int id)
        {
            BlobCreator blob = new BlobCreator(s_HeaderLength);
            blob.SerializeIntExt(id, 2); // bytes 0-1: id
            blob.SerializeIntExt(0, 2); // bytes 2-3: length indication
            return blob.ToByteArray();
        }

        /// <summary>
        /// returns the value of DefaultRouterAddress, according to PNIpSuite selection.
        /// If the option "IP-Suite via other method" activated, then the value will be set as "0".
        /// </summary>
        /// <returns></returns>
        private byte[] GetDefaultRouterAddressValue()
        {
            long defaultRouterAddress;

            // For PNIpConfig: If the option "IP-Suite via other method" activated, then the value will be set as "0".
            if (m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive)
            {
                defaultRouterAddress = 0;
            }
            else
            {
                defaultRouterAddress = GetIPDefaultRouterAddressForConfig(m_NodeIeBusinessLogic.IPAddress.AsInt64);
            }

            return new IPAddress(defaultRouterAddress).ToByteArray();
        }

        /// <summary>
        /// Special function for Config.
        /// If no router is used: return the node IP address as router address.
        /// </summary>
        /// <param name="nodeIPAddress">IP address of the node</param>
        /// <returns>
        /// Two cases:
        /// - Router is used: return IP from router
        /// - No router is used: return IP from node
        /// </returns>
        private long GetIPDefaultRouterAddressForConfig(long nodeIPAddress)
        {
            if (m_NodeIeBusinessLogic.IPDefaultRouterAddressUsed)
            {
                return m_NodeIeBusinessLogic.IPDefaultRouterAddress.AsInt64;
            }
            return nodeIPAddress;
        }

        private int GetIpAddressValidationValue()
        {
            int ipConfig;

            // For PNIpConfig: If the option "IP-Suite via other method" activated, then the value will be set as "0xFFFF".
            if (m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive)
            {
                ipConfig = 0xFFFF;
            }
            else
            {
                ipConfig = (int)AttributeUtilities.GetNodeIPConfiguration(m_NodeIeBusinessLogic.Node);
                if (ipConfig == (int)NodeIPConfiguration.Other)
                {
                    ipConfig = 0xFFFF;
                }
            }

            return ipConfig;
        }

        /// <summary>
        /// Get Name Of Station Validation Value according to "Name Of Station via other method" selection.
        /// If the option "Name Of Station via other method" activated, then the value will be set as "0xFFFF".
        /// </summary>
        /// <returns></returns>
        private int GetNameOfStationValidationValue()
        {
            int valueNameOfStationValidation = 0;

            // For PNIpConfig: If the option "Name Of Station via other method" activated, then the value will be set as "0xFFFF".
            // For Coupled IDevice, Sub Ordinate IO-System Address Tailoring, values must be preserved.
            if (m_NodeIeBusinessLogic.IsPNPNNoSViaOtherPathActive
                || IsCoupledIDeviceSubOrdinateIOSystemAddressTailored())
            {
                valueNameOfStationValidation = 0xFFFF;
            }

            return valueNameOfStationValidation;
        }

        /// <summary>
        /// Check whether the sub-ordinate IO-System of the IDevice which has Super & Sub Ordinate IO-Systems at the same time, is
        /// AddressTailoring enabled.
        /// </summary>
        /// <returns></returns>
        private bool IsCoupledIDeviceSubOrdinateIOSystemAddressTailored()
        {
            if (m_NodeIeBusinessLogic.PNIoOperatingMode != PNIOOperatingModes.IOControllerAndIODevice)
            {
                return false;
            }

            Interface deviceItemInterface = m_NodeIeBusinessLogic.Node.ParentObject as Interface;

            // This check only applies for IDevice which has Super & Sub Ordinate IO-Systems at the same time.
            return (NavigationUtilities.GetIoSystem(deviceItemInterface) != null)
                   && (NavigationUtilities.GetIoSystem(deviceItemInterface) != null)
                   && AddressTailorUtility.IsAddressTailoringEnabledIoControllerInterfaceStartObject(
                       deviceItemInterface);
        }
        /// <summary>
        /// Check if Address Tailoring is activated for device
        /// </summary>
        /// <param name="checkWithPNPNNoSViaOtherPathActive"></param>
        /// <returns></returns>
        private bool CheckIfAddressTailored(bool checkWithPNPNNoSViaOtherPathActive = true)
        {
            return (checkWithPNPNNoSViaOtherPathActive ? m_NodeIeBusinessLogic.IsPNPNNoSViaOtherPathActive : m_NodeIeBusinessLogic.IsPNPNIpSuiteViaOtherPathActive)
                && !m_NodeIeBusinessLogic.IsAddressTailoredIDevice;
        }
    }
}