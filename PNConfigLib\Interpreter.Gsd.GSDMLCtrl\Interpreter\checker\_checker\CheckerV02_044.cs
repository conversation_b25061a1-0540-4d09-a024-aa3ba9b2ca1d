﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_044.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;
using System.Globalization;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.44 and is based on GSD(ML) versions 2.43 and lower.
    ///	
    /// </summary>
    internal class CheckerV02044 : CheckerV02043
    {
        #region Fields

        #endregion


        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                " | .//gsddef:InterfaceSubmoduleItem/@Bridge_FeaturesSupported" +
                " | .//gsddef:InterfaceSubmoduleItem/@TSN_ConfigurationsSupported" +
                " | .//gsddef:CIM_Interface/@SupportedServiceProtocols" +
                " | .//gsddef:ParameterRecordDataItem/@Access";
                return (xp);
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList;
                return (xp);
            }
        }

        #endregion


        #region CheckerObject Members


        override protected string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02044;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02044;
        }


        override protected bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00044000);
            Checks.Add(Constants.s_Cn_0X00044001);
            Checks.Add(Constants.s_Cn_0X00044002);
            Checks.Add(Constants.s_Cn_0X00044003);

            return succeeded;
        }

        override protected bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                //Checks.Remove(Constants.CN_0x000XXXXX);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion




        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.44.
        /// </summary>
        public CheckerV02044()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version244);
        }

        #endregion


        #region Methods

        /// <summary>
        /// CheckForApplClass
        /// 
        /// For the token "Drive" the following is to check:
        /// 1) At least one submodule shall exist(fixed or pluggable) containing the PROFIdrive MAP submodule.
        ///    (A submodule with @API == 0x3A00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) == 0xFFFF)
        /// 
        /// For the token "Encoder" the following is to check:
        /// 1) At least one submodule shall exist(fixed or pluggable) containing the PROFIdrive MAP submodule.
        ///    (A submodule with @API == 0x3D00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) == 0xFFFF)
        /// 
        /// </summary>
        /// <returns>True, if a submodule was found which fulfill condition 1 and 2.</returns>
        protected virtual bool CheckForApplClass(List<string> applClassList, IList<XElement> submoduleItems, string token, UInt32 checkValue)
        {
            if (applClassList.Contains(token))  // token = "Drive" or "Encoder"
            {
                foreach (var submodule in submoduleItems)
                {
                    // Get the API attribute
                    UInt32 api = 0;
                    string strApi = Help.GetAttributeValueFromXElement(submodule, Attributes.s_Api);
                    if (!string.IsNullOrEmpty(strApi))
                        api = XmlConvert.ToUInt32(strApi);

                    // A submodule with @API == 0x3A00 ("Drive") or @API == 0x3D00 ("Encoder") must exist
                    if (api != checkValue)
                        continue;

                    // Get the SubmoduleIdentNumber attribute
                    UInt32 submoduleId = Convert.ToUInt32(Help.GetAttributeValueFromXElement(submodule, Attributes.s_SubmoduleIdentNumber), 16);   // required attribute

                    // This submodule must have a (@SubmoduleIdentNumber & 0x0000FFFF) == 0xFFFF
                    if (!((submoduleId & 0x0000FFFF) == 0xFFFF))
                        continue;

                    // A submodule is found, which fulfill condition 1 !!!

                    // Now check if a submodule can be found, which fulfill condition 2
                    if (CheckForApplClassCond2(applClassList, submoduleItems, token, checkValue))
                        return true;
                }
            }
            else
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// CheckForApplClassCond2
        /// 
        /// For the token "Drive" the following is to check:
        /// 2) At least one additional PROFIdrive submodule shall exist(fixed or pluggable) containing IOData.
        ///    (A submodule with @API == 0x3A00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) != 0xFFFF
        ///    and (IOData/Input or IOData/Output) are present).
        /// 
        /// For the token "Encoder" the following is to check:
        /// 2) At least one additional PROFIdrive submodule shall exist(fixed or pluggable) containing IOData.
        ///    (A submodule with @API == 0x3D00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) != 0xFFFF
        ///    and (IOData/Input or IOData/Output) are present).
        /// 
        /// </summary>
        /// <returns>True, if a submodule was found which fulfill condition 2.</returns>
        protected virtual bool CheckForApplClassCond2(List<string> applClassList, IList<XElement> submoduleItems, string token, UInt32 checkValue)
        {
            if (!applClassList.Contains(token)) // token = "Drive" or "Encoder"
            {
                return false;
            }

                foreach (var submodule in submoduleItems)
                {
                    // Get the API attribute
                    UInt32 api = 0;
                    string strApi = Help.GetAttributeValueFromXElement(submodule, Attributes.s_Api);
                    if (!string.IsNullOrEmpty(strApi))
                        api = XmlConvert.ToUInt32(strApi);

                    // A submodule with @API == 0x3A00 ("Drive") or @API == 0x3D00 ("Encoder") must exist
                    if (api != checkValue)
                        continue;

                    // Get the SubmoduleIdentNumber attribute
                    UInt32 submoduleId = Convert.ToUInt32(Help.GetAttributeValueFromXElement(submodule, Attributes.s_SubmoduleIdentNumber), 16);   // required attribute

                    // This submodule must have a (@SubmoduleIdentNumber & 0x0000FFFF) != 0xFFFF
                    if (!((submoduleId & 0x0000FFFF) != 0xFFFF))
                        continue;

                // For this submodule IOData/Input or IOData/Output must be present
                var inputOrOutput = submodule.Descendants().Where(x => x.Parent != null && x.Parent.Name.LocalName == Elements.s_IoData);

                if (inputOrOutput.Count() != 0)
                        return true;
                }
            

            return false;
        }

        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();
        }


        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("IngressRateLimiter");
            AttributeTokenDictionary.Add("Bridge_FeaturesSupported", tokens1);

            IList<string> tokens2 = new List<string>();
            tokens2.Add("TSN_Cfg_Default");
            AttributeTokenDictionary.Add("TSN_ConfigurationsSupported", tokens2);

            var tokens3 = AttributeTokenDictionary["ApplicationClass"];
            tokens3.Add("Drive");
            tokens3.Add("Encoder");

            IList<string> tokens4 = new List<string>();
            tokens4.Add("read");
            tokens4.Add("write");
            tokens4.Add("prm");
            AttributeTokenDictionary.Add("Access", tokens4);
        }

        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// Attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// As of GSDML V2.4:
        /// - New values 0x004F - 0x0066 for MAUTypes
        /// As of GSDML V2.41
        /// - New values 103..113, 141, 144 for MAUTypes
        /// 
        /// Partially the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs in which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();
        }

        #endregion


        #region Overrides

        #endregion


        /// <summary>
        /// Check number: CN_0x00044000
        /// InterfaceSubmoduleItem: New attribute TSN_ConfigurationsSupported
        /// 
        /// The following check is implemented:
        /// This attribute shall be present if CertificationInfoExt/@ConformanceClass
        /// contains the token "D", which means that the DAP supports TSN
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00044000()
        {
            // Find all CertificationInfoExt nodes
            var certificationInfoExtNodes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);
            certificationInfoExtNodes = Help.TryRemoveXElementsUnderXsAny(certificationInfoExtNodes, Nsmgr, Gsd);
            foreach (var certificationInfoExt in certificationInfoExtNodes)
            {
                // Get the attribute ConformanceClass
                string conformanceClass = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ConformanceClass);

                if (conformanceClass != "D")
                {
                    continue;
                }

                // Get the DAP
                if (certificationInfoExt.Parent == null)
                {
                    continue;
                }

                var dap = certificationInfoExt.Parent.Parent;

                // Get the InterfaceSubmoduleItem
                var interfaceSubmoduleItem = dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();
                if (interfaceSubmoduleItem == null)
                    continue;

                string strTsnConfigurationsSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_TSN_ConfigurationsSupported);

                if (!string.IsNullOrEmpty(strTsnConfigurationsSupported))
                    continue;

                // "When 'CertificationInfoExt/@ConformanceClass'="D", 'InterfaceSubmoduleItem/@TSN_ConfigurationsSupported' must be present."
                string msg = Help.GetMessageString("M_0x00044000_1");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                var xli = (IXmlLineInfo)interfaceSubmoduleItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00044000_1");
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00044001
        /// (CertificationInfo || CertificationInfoExt)/@ApplicationClass: New tokens "Drive" and "Encoder"
        /// 
        /// For the token "Drive" the following is to check:
        /// 1) At least one submodule shall exist(fixed or pluggable) containing the PROFIdrive MAP submodule.
        ///    (A submodule with @API == 0x3A00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) == 0xFFFF)
        /// 2) At least one additional PROFIdrive submodule shall exist(fixed or pluggable) containing IOData.
        ///    (A submodule with @API == 0x3A00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) != 0xFFFF
        ///    and (IOData/Input or IOData/Output) are present).
        /// 
        /// For the token "Encoder" the following is to check:
        /// 1) At least one submodule shall exist(fixed or pluggable) containing the PROFIdrive MAP submodule.
        ///    (A submodule with @API == 0x3D00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) == 0xFFFF)
        /// 2) At least one additional PROFIdrive submodule shall exist(fixed or pluggable) containing IOData.
        ///    (A submodule with @API == 0x3D00 and (@SubmoduleIdentNumber BITWISE - AND 0x0000FFFF) != 0xFFFF
        ///    and (IOData/Input or IOData/Output) are present).
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00044001()
        {
            // Find all ApplicationClass attributes
            var applicationClassNodes = GsdProfileBody.Descendants().Attributes(Attributes.s_ApplicationClass);
            applicationClassNodes = Help.TryRemoveXAttributesUnderXsAny(applicationClassNodes, Nsmgr, Gsd);
            foreach (var applicationClassNode in applicationClassNodes)
            {
                string applicationClass = applicationClassNode.Value;
                var applClassList = new List<string>(applicationClass.Split(Constants.s_Semicolon.ToCharArray()));

                if (!applClassList.Contains("Drive") && !applClassList.Contains("Encoder"))
                    continue;

                // Find the dap
                if (applicationClassNode.Parent == null)
                {
                    continue;
                }

                var dap = applicationClassNode.Parent.Parent;
                if (dap.Name.LocalName == Elements.s_CertificationInfo)
                    dap = applicationClassNode.Parent.Parent.Parent;

                // Get the SubmoduleItems plugged or pluggable with this dap
                DapToSubmoduleDictionary.TryGetValue(dap, out IList<XElement> submoduleItems);

                if (!CheckForApplClass(applClassList, submoduleItems, "Drive", 0x3A00))
                {
                    // "For '(CertificationInfo || CertificationInfoExt)/@ApplicationClass' token "Drive" at least
                    //  one submodule shall exist with '@API' == 0x3A00 and (('@SubmoduleIdentNumber' & 0x0000FFFF) == 0xFFFF)
                    //  and one additional submodule with '@API' == 0x3A00 and (('@SubmoduleIdentNumber' & 0x0000FFFF) != 0xFFFF)
                    //  and ('IOData/Input' or 'IOData/Output') are present."
                    string msg = Help.GetMessageString("M_0x00044001_1");
                    string xpath = Help.GetXPath(applicationClassNode);
                    var xli = (IXmlLineInfo)applicationClassNode;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00044001_1");
                }

                if (!CheckForApplClass(applClassList, submoduleItems, "Encoder", 0x3D00))
                {
                    // "For '(CertificationInfo || CertificationInfoExt)/@ApplicationClass' token "Encoder" at least
                    //  one submodule shall exist with '@API' == 0x3D00 and (('@SubmoduleIdentNumber' & 0x0000FFFF) == 0xFFFF)
                    //  and one additional submodule with '@API' == 0x3D00 and (('@SubmoduleIdentNumber' & 0x0000FFFF) != 0xFFFF)
                    //  and ('IOData/Input' or 'IOData/Output') are present."
                    string msg = Help.GetMessageString("M_0x00044001_2");
                    string xpath = Help.GetXPath(applicationClassNode);
                    var xli = (IXmlLineInfo)applicationClassNode;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00044001_2");
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00044002
        /// 
        /// Checks for F_BaseIDRecordDataItem/F_BaseID if the default value is contained in the allowed values.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00044002()
        {
            var fBaseIDs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_FBaseID);
            fBaseIDs = Help.TryRemoveXElementsUnderXsAny(fBaseIDs, Nsmgr, Gsd);
            foreach (var fBaseId in fBaseIDs)
            {
                // Find the AllowedValues
                Int64 allowedValuesFrom;
                Int64 allowedValuesTo;
                List<ValueListHelper.Int64ValueRangeT> allowedValues = ValueListHelper.NormalizeValueListInt64(fBaseId.Attribute(Attributes.s_AllowedValues), Store);

                if (allowedValues == null && allowedValues.Count == 0)
                {
                    allowedValuesFrom = -9223372036854775808;
                    allowedValuesTo = 9223372036854775807;
                }
                else
                {
                    allowedValuesFrom = allowedValues[0].From;
                    allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                }

                // Find the DefaultValue
                Int64 defaultValue = 0;
                string strDefaultValue = Help.GetAttributeValueFromXElement(fBaseId, Attributes.s_DefaultValue);
                if (!string.IsNullOrEmpty(strDefaultValue))
                {
                    long.TryParse(strDefaultValue, NumberStyles.Any, CultureInfo.InvariantCulture, out defaultValue);
                }

                if (defaultValue < allowedValuesFrom || defaultValue > allowedValuesTo)
                {
                    if (Help.CheckSchemaVersion(fBaseId, SupportedGsdmlVersion))
                    {
                        // "'F_BaseIDRecordDataItem/F_BaseID': The default value (= {0}) is not contained in allowed values (= {1}..{2})."
                        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00044002_1"), defaultValue, allowedValuesFrom, allowedValuesTo);
                        string xpath = Help.GetXPath(fBaseId);
                        var xli = (IXmlLineInfo)fBaseId;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00044002_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00044003
        ///
        /// Checks if F_BaseID_CRC matches the calculated CRC over the BaseID and the F_Par_CRC.
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00044003()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_BaseIDRecordDataItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            Crc32 calcFBaseIdCrc = new();

            foreach (var fBaseIdRecord in nl)
            {
                calcFBaseIdCrc.InitChecksum();

                // Get the DefaultValue of the element F_BaseID and add it to a byte array
                string fBaseIdValue = fBaseIdRecord.Element(NamespaceGsdDef + Elements.s_FBaseID).Attribute(Attributes.s_DefaultValue).Value;
                long fBbaseId = long.Parse(fBaseIdValue, NumberStyles.Any, CultureInfo.InvariantCulture);
                byte[] data1 =
                    {
                       (byte)(fBbaseId >> 56),
                       (byte)(fBbaseId >> 48),
                       (byte)(fBbaseId >> 40),
                       (byte)(fBbaseId >> 32),
                       (byte)(fBbaseId >> 24),
                       (byte)(fBbaseId >> 16),
                       (byte)(fBbaseId >> 8),
                       (byte)(fBbaseId)

                   };

                // Add the byte array to the CRC
                calcFBaseIdCrc.UpdateChecksum(data1);

                // Get the DefaultValue of the element F_Par_CRC of the element F_ParameterRecordDataItem of fBaseIdRecord parent and add it to a byte array
                uint fParCrc = calculateFParCRC32_PROFIsafeMU2(fBaseIdRecord.Parent.Element(NamespaceGsdDef + Elements.s_FParameterRecordDataItem));
                byte[] data2 =
                    {
                       (byte)(fParCrc >> 24),
                       (byte)(fParCrc >> 16),
                       (byte)(fParCrc >> 8),
                       (byte)(fParCrc)
                   };

                // Add the byte array to the CRC
                calcFBaseIdCrc.UpdateChecksum(data2);

                // Finish the checksum process, returning the CRC
                uint calculatedFBaseIdCrc = calcFBaseIdCrc.FinishChecksum();

                uint fBaseIdCrc = uint.Parse(fBaseIdRecord.Element(NamespaceGsdDef + Elements.s_FBaseID_CRC).Attribute(Attributes.s_DefaultValue).Value, NumberStyles.Any, CultureInfo.InvariantCulture);

                if (calculatedFBaseIdCrc == fBaseIdCrc)
                {
                    continue;
                }

                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00044003_1"), fBaseIdCrc, calculatedFBaseIdCrc);
                string xpath = Help.GetXPath(fBaseIdRecord);
                var xli = (IXmlLineInfo)fBaseIdRecord;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00044003_1");

            }

            return true;
        }

        private uint calculateFParCRC32_PROFIsafeMU2(XElement fparamRecord)
        {
            Crc32 calcFParCrc = new();

            byte fblockId = 0; // Default: "0"
            var fblockIds = fparamRecord.Element(NamespaceGsdDef + Elements.s_FBlockID);
            if (fblockIds != null)
            {
                string strFBlockId = Help.GetAttributeValueFromXElement(fblockIds, Attributes.s_DefaultValue);
                if (!String.IsNullOrEmpty(strFBlockId))
                    fblockId = byte.Parse(strFBlockId, CultureInfo.InvariantCulture);
            }

            bool isiParCrc = (fblockId & 1) == 1;
            bool isWdTime2 = (fblockId & 2) == 2;

            // Build the F_Par_CRC
            calcFParCrc.InitChecksum();

            if (isWdTime2)
            {
                // The next two bytes are occupied with F_WD_Time_2, if available
                // Add F_WD_Time_2, if bit 1 of F_Block_ID is set to "1"
                byte[] data5 = GetFWdTime2Bytes(fparamRecord);

                // Add the byte array to the CRC
                calcFParCrc.UpdateChecksum(data5);
            }

            // The first four bytes (0 - 3) are occupied with F_iPar_CRC, if available
            // Add F_iPar_CRC, if bit 0 of F_Block_ID is set to "1"
            if (isiParCrc)
            {
                byte[] data1 = GetFiParBytes(fparamRecord);

                // Add the byte array to the CRC
                calcFParCrc.UpdateChecksum(data1);
            }

            // The next byte is occupied with F_Prm_Flag1 = F_Check_SeqNr, F_Check_iPar, F_SIL, F_CRC_Length, F_CRC_Seed
            byte fPrmFlag1 = GetFPrmFlag1(fparamRecord);

            // For the CRC32, the F_CRC_Seed bit must be set to 1
            fPrmFlag1 |= 1 << 6;

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag1);

            // The next byte is occupied with F_Prm_Flag2 = F_Passivation, F_Block_ID, F_Par_Version
            byte fPrmFlag2 = GetFPrmFlag2(fparamRecord);

            // Add the byte to the CRC
            calcFParCrc.Update(fPrmFlag2);

            // The next two bytes are occupied with F_Source_Add
            // Add F_Source_Add, if visible
            byte[] data2 = GetFSourceAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data2);

            // The next two bytes are occupied with F_Dest_Add
            // Add F_Dest_Add, if visible
            byte[] data3 = GetFDestAddBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data3);

            // The next two bytes are occupied with F_WD_Time
            // Add F_WD_Time, if visible
            byte[] data4 = GetFWdTimeBytes(fparamRecord);

            // Add the byte array to the CRC
            calcFParCrc.UpdateChecksum(data4);

            // Finish the checksum process, returning the CRC
            return calcFParCrc.FinishChecksum();
        }

        protected virtual byte[] GetFWdTime2Bytes(XElement fparamRecord)
        {
            byte[] data5 = new byte[2];
            data5[0] = 0;
            data5[1] = 0;

            var fwdTimes2 = fparamRecord.Element(NamespaceGsdDef + Elements.s_FWdTime2);
            if (fwdTimes2 != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fwdTimes2, Attributes.s_Visible);
                bool bVisible = true;    // Default

                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);

                if (!bVisible)
                {
                    return data5;
                }

                UInt16 fwdTime2 = 1000;  // Default: "1000" (1000 ms)
                string strFwdTime2 = Help.GetAttributeValueFromXElement(fwdTimes2, Attributes.s_DefaultValue);
                if (!String.IsNullOrEmpty(strFwdTime2))
                    fwdTime2 = UInt16.Parse(strFwdTime2, CultureInfo.InvariantCulture);

                data5[0] = (byte)(fwdTime2 >> 8);
                data5[1] = (byte)fwdTime2;
            }
            else
            {
                // F_WD_Time is not given, but because it is always visible the default value must be added
                UInt16 fwdTime = 1000;  // Default: "1000" (1000 ms)
                data5[0] = (byte)(fwdTime >> 8);
                data5[1] = (byte)fwdTime;
            }
            return data5;
        }
        #endregion
    }
}
