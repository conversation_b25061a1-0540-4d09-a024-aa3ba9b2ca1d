/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CIMProtection.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Runtime.InteropServices;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The CIMProtection object describes the data structure of a 
    /// parameter record.
    /// </summary>
    //[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C9E")] 
    public class CimProtection :
        GsdObject,
        ICimProtection
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the CIMProtection if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public CimProtection()
		{
			// Initialize the properties
        }

		#endregion

		//########################################################################################
		#region Fields

		// Declaration of the properties
        private KeyDerivation m_KeyDerivation;
        private KeyAgreement m_KeyAgreement;
        private DigitalSignature m_DigitalSignature;
        private StreamProtection m_StreamProtection;
        private AlarmProtection m_AlarmProtection;
        private ConnectionManagementProtection m_ConnectionManagementProtection;

        #endregion

        //########################################################################################
        #region Properties


        public virtual KeyDerivation KeyDerivation => this.m_KeyDerivation;

        public virtual KeyAgreement KeyAgreement => this.m_KeyAgreement;

        public virtual DigitalSignature DigitalSignature => this.m_DigitalSignature;

        public virtual StreamProtection StreamProtection => this.m_StreamProtection;

        public virtual AlarmProtection AlarmProtection => this.m_AlarmProtection;

        public virtual ConnectionManagementProtection ConnectionManagementProtection => this.m_ConnectionManagementProtection;

        #region COM Interface Members Only

        // ONLY for the COM interface.

        #endregion

        #endregion


        //########################################################################################
        #region Methods

        #region COM Interface Methods Only

        #endregion

        #endregion

        //########################################################################################
        #region Object members

        #endregion


        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
		{
			bool succeeded = true;

            try
            {
				// Check parameter.
				if (null == hash)
					throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.

                string member = Models.s_FieldKeyDerivation;
                if (hash.ContainsKey(member) && hash[member] is KeyDerivation)
                    this.m_KeyDerivation = hash[member] as KeyDerivation;

                member = Models.s_FieldKeyAgreement;
                if (hash.ContainsKey(member) && hash[member] is KeyAgreement)
                    this.m_KeyAgreement = hash[member] as KeyAgreement;

                member = Models.s_FieldDigitalSignature;
                if (hash.ContainsKey(member) && hash[member] is DigitalSignature)
                    this.m_DigitalSignature = hash[member] as DigitalSignature;

                member = Models.s_FieldStreamProtection;
                if (hash.ContainsKey(member) && hash[member] is StreamProtection)
                    this.m_StreamProtection = hash[member] as StreamProtection;

                member = Models.s_FieldAlarmProtection;
                if (hash.ContainsKey(member) && hash[member] is AlarmProtection)
                    this.m_AlarmProtection = hash[member] as AlarmProtection;

                member = Models.s_FieldConnectionManagementProtection;
                if (hash.ContainsKey(member) && hash[member] is ConnectionManagementProtection)
                    this.m_ConnectionManagementProtection = hash[member] as ConnectionManagementProtection;

                //// Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
			writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectCIMProtection);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// ----------------------------------------------
			// From base class
			base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldKeyDerivation, this.m_KeyDerivation);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldKeyAgreement, this.m_KeyAgreement);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldDigitalSignature, this.m_DigitalSignature);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldStreamProtection, this.m_StreamProtection);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldAlarmProtection, this.m_AlarmProtection);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldConnectionManagementProtection, this.m_ConnectionManagementProtection);

            return true; 
		}


        #endregion

    }
}
