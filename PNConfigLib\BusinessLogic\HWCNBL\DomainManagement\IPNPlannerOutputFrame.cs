/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPNPlannerOutputFrame.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL.DomainManagement
{
    /// <summary>
    /// Interface for the PNPlanner output frame which is used to fill PDIRFrameData.
    /// </summary>
    public interface IPNPlannerOutputFrame
    {
        /// <summary>
        /// Controller local phase of the frame.
        /// </summary>
        int ControllerLocalPhase { get; }

        /// <summary>
        /// Controller local reduction ratio of the frame.
        /// </summary>
        int ControllerLocalReductionRatio { get; }

        /// <summary>
        /// Device local phase of the frame.
        /// </summary>
        int DeviceLocalPhase { get; }

        /// <summary>
        /// Device local reduction ratio of the frame.
        /// </summary>
        int DeviceLocalReductionRatio { get; }

        /// <summary>
        /// Ethertype of the frame.
        /// </summary>
        int EtherType { get; }

        /// <summary>
        /// Frame ID of the frame.
        /// </summary>
        int FrameID { get; }

        /// <summary>
        /// Contains when the frame is sent (in ns).
        /// </summary>
        uint FrameSendOffset { get; }

        /// <summary>
        /// Padded data length.
        /// </summary>
        int Length { get; }

        /// <summary>
        /// Specifies whether the frame is locally sent or received.
        /// Set to 1 if it is.
        /// </summary>
        int Local { get; }

        /// <summary>
        /// Unused meaning frame send offset for now; fixed to 0.
        /// </summary>
        int MeaningFrameSendOffset { get; }

        /// <summary>
        /// Original frame ID of the frame.
        /// </summary>
        int OriginalFrameID { get; }

        /// <summary>
        /// Padding length in payload.
        /// </summary>
        int Padding { get; }

        /// <summary>
        /// Phase of the frame.
        /// </summary>
        int Phase { get; }

        /// <summary>
        /// List of the TX ports which should send the frame.
        /// </summary>
        IList<int> Ports { get; }

        /// <summary>
        /// List of the possible reduction ratios.
        /// </summary>
        IList<long> PossibleReductionRatios { get; }

        /// <summary>
        /// Reduction ratio of the frame.
        /// </summary>
        int ReductionRatio { get; }

        /// <summary>
        /// If the frame is received, contains which port receives this frame.
        /// </summary>
        int RxPort { get; }

        /// <summary>
        /// Specifies whether the frame is a sync or redundant sync frame.
        /// Set to 1 if it is.
        /// </summary>
        int SyncFrame { get; }

        /// <summary>
        /// List of the subframes (for dfp frames).
        /// </summary>
        IList<IPNPlannerOutputSubframe> Subframes { get; }

        bool SfCrc16 { get; set; }
    }
}