/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: SystemRedundancy.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    public class SystemRedundancy : GsdObject, GSDI.ISystemRedundancy
    {
        //########################################################################################
        #region Fields

        private RedundancyDeviceTypes m_DeviceType;
        private uint m_MaxSwitchOverTime;
        private bool m_PrimaryArOnBothNapsSupported;
        private uint m_NumberOfSrArSets;
        private bool m_RtInputOnBackupArSupported;
        private bool m_ExistsMinRdht;
        private uint m_MinRdht;
        private bool m_DataInvalidOnBackupArSupported;  // V2.32
        private uint m_S2MaxInputOnBackupDelay;         // V2.32
        private uint m_R2MaxInputOnBackupDelay;         // V2.32                     

        private ArrayList m_DeviceTypes;     // V2.4

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public RedundancyDeviceTypes DeviceType => m_DeviceType;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public uint MaxSwitchOverTime => m_MaxSwitchOverTime;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsPrimaryAR_OnBothNAPsSupported => m_PrimaryArOnBothNapsSupported;

        // RQ AP01274799 begin
        ///// <summary>
        ///// Accesses, ....
        ///// </summary>
        ///// <remarks>....</remarks>
        //public bool IsRedundancyAlarmSupported
        //{
        //    get { return m_RedundancyAlarmSupported; }
        //}
        // RQ AP01274799 end

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public uint NumberOfSR_AR_Sets => m_NumberOfSrArSets;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public uint MinRDHT => m_MinRdht;

        public bool ExistsMinRDHT => m_ExistsMinRdht;

        public bool RtInputOnBackupArSupported => m_RtInputOnBackupArSupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsDataInvalidOnBackupARSupported => m_DataInvalidOnBackupArSupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public uint S2MaxInputOnBackupDelay => m_S2MaxInputOnBackupDelay;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public uint R2MaxInputOnBackupDelay => m_R2MaxInputOnBackupDelay;

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array DeviceTypes =>
            m_DeviceTypes?.ToArray();
        #endregion

        //########################################################################################
        #region Initialization & Termination

        public SystemRedundancy()
        {
            m_DeviceType = RedundancyDeviceTypes.GSDDeviceTypeNone;
            m_NumberOfSrArSets = Attributes.s_DefaultNumberOfSrArSets;
            m_PrimaryArOnBothNapsSupported = Attributes.s_DefaultPrimaryArOnBothNapsSupported;
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                FillFieldDeviceType(hash);

                FillFieldMaxSwitchOverTime(hash);

                FillFieldNumberOfSrArSets(hash);

                FillFieldPrimaryArOnBothNapsSupported(hash);

                FillFieldMinRDHT(hash);

                FillFieldExistsMinRDHT(hash);

                FillFieldRTInputOnBackupARSupported(hash);

                FillFieldDataInvalidOnBackupARSupported(hash);

                FillFieldS2MaxInputOnBackupDelay(hash);

                FillFieldR2MaxInputOnBackupDelay(hash);

                // RQ AP01274799 begin
                //member = Models.FieldRedundancyAlarmSupported;
                //if (hash.ContainsKey(member) && hash[member] is bool)
                //    m_RedundancyAlarmSupported = (bool)hash[member];
                // RQ AP01274799 end

                /*
		        // Own data.
		        member = Models.FieldInputConsistency;
		        if (hash.ContainsKey(member) && hash[member] is GSDI.IOConsistencies)
		            this.m_InputConsistency = (GSDI.IOConsistencies) hash[member];
                 * */

                FillFieldDeviceTypes(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }
            return succeeded;

        }

        private void FillFieldDeviceTypes(Hashtable hash)
        {
            string member = Models.s_FieldDeviceTypes;
            if (hash.ContainsKey(member) && hash[member] is ArrayList)
                m_DeviceTypes = hash[member] as ArrayList;
        }

        private void FillFieldR2MaxInputOnBackupDelay(Hashtable hash)
        {
            string member = Models.s_FieldR2MaxInputOnBackupDelay;
            if (hash.ContainsKey(member) && hash[member] is uint)
                m_R2MaxInputOnBackupDelay = (uint)hash[member];
        }

        private void FillFieldS2MaxInputOnBackupDelay(Hashtable hash)
        {
            string member = Models.s_FieldS2MaxInputOnBackupDelay;
            if (hash.ContainsKey(member) && hash[member] is uint)
                m_S2MaxInputOnBackupDelay = (uint)hash[member];
        }

        private void FillFieldDataInvalidOnBackupARSupported(Hashtable hash)
        {
            string member = Models.s_FieldDataInvalidOnBackupArSupported;
            if (hash.ContainsKey(member) && hash[member] is bool)
                m_DataInvalidOnBackupArSupported = (bool)hash[member];
        }

        private void FillFieldRTInputOnBackupARSupported(Hashtable hash)
        {
            string member = Models.s_FieldRTInputOnBackupArSupported;
            if (hash.ContainsKey(member) && hash[member] is bool)
                m_RtInputOnBackupArSupported = (bool)hash[member];
        }

        private void FillFieldExistsMinRDHT(Hashtable hash)
        {
            string member = Models.s_FieldExistsMinRdht;
            if (hash.ContainsKey(member) && hash[member] is bool)
                m_ExistsMinRdht = (bool)hash[member];
        }

        private void FillFieldMinRDHT(Hashtable hash)
        {
            string member = Models.s_FieldMinRdht;
            if (hash.ContainsKey(member) && hash[member] is uint)
                m_MinRdht = (uint)hash[member];
        }

        private void FillFieldPrimaryArOnBothNapsSupported(Hashtable hash)
        {
            string member = Models.s_FieldPrimaryArOnBothNapsSupported;
            if (hash.ContainsKey(member) && hash[member] is bool)
                m_PrimaryArOnBothNapsSupported = (bool)hash[member];
        }

        private void FillFieldNumberOfSrArSets(Hashtable hash)
        {
            string member = Models.s_FieldNumberOfSrArSets;
            if (hash.ContainsKey(member) && hash[member] is uint)
                m_NumberOfSrArSets = (uint)hash[member];
        }

        private void FillFieldMaxSwitchOverTime(Hashtable hash)
        {
            string member = Models.s_FieldMaxSwitchOverTime;
            if (hash.ContainsKey(member) && hash[member] is uint)
                m_MaxSwitchOverTime = (uint)hash[member];
        }

        private void FillFieldDeviceType(Hashtable hash)
        {
            string member = Models.s_FieldDeviceType;
            if (hash.ContainsKey(member) && hash[member] is RedundancyDeviceTypes)
                m_DeviceType = (RedundancyDeviceTypes)hash[member];
        }
        #endregion
    }
}

