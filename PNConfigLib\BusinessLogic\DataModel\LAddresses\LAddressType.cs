/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: LAddressType.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.LAddresses
{
    /// <summary>
    /// This enum specifies the type of the address.
    /// </summary>
    /// <remarks>
    /// Some objects can contain several addresses with different types. For example,
    /// a CentralDevice has a StationAddress and a RackAddress in addition to its
    /// regular PclObject address. This enum is used for making that distinction.
    /// </remarks>
    public enum LAddressType
    {
        /// <summary>
        /// The address belongs to the PclObject itself.
        /// </summary>
        PCLObject,

        /// <summary>
        /// The address belongs to the station of a CentralDevice or DecentralDevice.
        /// </summary>
        Station,

        /// <summary>
        /// The address belongs to the rack of a CentralDevice or DecentralDevice.
        /// </summary>
        Rack
    }
}