/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_02.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Globalization;
using System.Xml.XPath;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common helper functions.
    /// </summary>
    internal class BuilderV0202 :
        BuilderV0201
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0202()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version22);
        }

        #endregion

        //########################################################################################
        #region Methods

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {

            hash.Add(Models.s_FieldSupportedSyncProtocols, null);
            hash.Add(Models.s_FieldIsDcpBoundarySupported, null);
            hash.Add(Models.s_FieldIsPtpBoundarySupported, null);
            hash.Add(Models.s_FieldIsDcpHelloSupported, null);
            hash.Add(Models.s_FieldIsochroneModeInRtClasses, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // Get DCPBoundarySupported attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_DcpBoundarySupported, String.Empty);
            hash[Models.s_FieldIsDcpBoundarySupported] = Help.GetBool(attr, Attributes.s_DefaultDcpBoundarySupported);

            // Get PTPBoundarySupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_PtpBoundarySupported, String.Empty);
            hash[Models.s_FieldIsPtpBoundarySupported] = Help.GetBool(attr, Attributes.s_DefaultPtpBoundarySupported);

            // Get DCP_HelloSupported attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_DcpHelloSupported, String.Empty);
            hash[Models.s_FieldIsDcpHelloSupported] = Help.GetBool(attr, Attributes.s_DefaultDcpHelloSupported);

            // Get IsochroneModeInRT_Classes attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_IsochroneModeInRTClasses, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIsochroneModeInRtClasses] = Help.SeparateTokenList(attr);
            }

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_SynchronisationMode, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                // Get SupportedSyncProtocols attribute. Optional.
                if (nodes.Current != null)
                {
                    attr = nodes.Current.GetAttribute(Attributes.s_SupportedSyncProtocols, String.Empty);
                }

                if (!String.IsNullOrEmpty(attr))
                {
                    hash[Models.s_FieldSupportedSyncProtocols] = Help.SeparateTokenList(attr);
                }
            }
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMaxSupportedRecordSize, null);
            hash.Add(Models.s_FieldIsParameterizationSpeedupSupported, null);
            hash.Add(Models.s_FieldIsNameOfStationNotTransferable, null);
            hash.Add(Models.s_FieldPowerOnToCommReady, null);

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            // Get MaxSupportedRecordSize Optional. 
            string attr = nav.GetAttribute(Attributes.s_MaxSupportedRecordSize, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldMaxSupportedRecordSize] = value;
            }
            else
            {
                hash[Models.s_FieldMaxSupportedRecordSize] = Attributes.s_DefaultMaxSupportedRecordSize;
            }

            // Get PowerOnToCommReady Optional. 
            attr = nav.GetAttribute(Attributes.s_PowerOnToCommReady, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldPowerOnToCommReady] = value;
            }
            else
            {
                hash[Models.s_FieldPowerOnToCommReady] = Attributes.s_DefaultPowerOnToCommReady;
            }

            // Get ParameterizationSpeedupSupported Optional. 
            attr = nav.GetAttribute(Attributes.s_ParameterizationSpeedupSupported, String.Empty);
            hash[Models.s_FieldIsParameterizationSpeedupSupported] = Help.GetBool(attr, Attributes.s_DefaultParameterizationSpeedupSupported);

            // Get NameOfStationNotTransferable Optional. 
            attr = nav.GetAttribute(Attributes.s_NameOfStationNotTransferable, String.Empty);
            hash[Models.s_FieldIsNameOfStationNotTransferable] = Help.GetBool(attr, Attributes.s_DefaultNameOfStationNotTransferable);
        }

        protected override void PrepareIOData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for IOData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFioStructureDescVersion, null);

            // Call base class method first.
            base.PrepareIOData(nav, ref hash);

            // --------------------------------------------

            // Get F_IO_StructureDescVersion attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_FIOStructureDescVersion, String.Empty);
            if (String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldFioStructureDescVersion] = Attributes.s_DefaultFioStructureDescVersion;
            }
            else
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldFioStructureDescVersion] = value;
            }
        }


        protected override void PrepareTimingPropertiesReductionRatios(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldReductionRatioPow2, null);
            hash.Add(Models.s_FieldReductionRatioNonPow2, null);

            if (nav != null)
            {
                // Get ReductionRatioPow2 attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_ReductionRatioPow2, String.Empty);
                if (attr.Length != 0)
                {
                    hash[Models.s_FieldReductionRatioPow2] = ValueListHelper.SeparateUnsignedValueList(attr);
                }

                // Get ReductionRatioNonPow2 attribute. Optional.
                attr = nav.GetAttribute(Attributes.s_ReductionRatioNonPow2, String.Empty);
                if (attr.Length != 0)
                {
                    hash[Models.s_FieldReductionRatioNonPow2] = ValueListHelper.SeparateUnsignedValueList(attr);
                }

                if ((nav.GetAttribute(Attributes.s_ReductionRatioPow2, String.Empty) == String.Empty) && (nav.GetAttribute(Attributes.s_ReductionRatioNonPow2, String.Empty) == String.Empty))
                {
                    // if ReductionRatioPow2 and ReductionRatioNonPow2 are missing, use the values which
                    // are given via attribute ReductionRatio. If the attribute is also missing, use
                    // default values.
                    attr = nav.GetAttribute(Attributes.s_ReductionRatio, String.Empty);
                    if (attr.Length == 0)
                    {
                        attr = Attributes.DefaultReductionRatio.GetValue(0)?.ToString();
                        hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr, Attributes.DefaultReductionRatio);
                    }
                    else
                    {
                        hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr);
                    }
                }
                else
                {
                    // if ReductionRatioPow2 and/or ReductionRatioNonPow2 are present, use also the values from
                    // the attribute ReductionRatio. If the attribute is  missing, don't use default values.

                    attr = nav.GetAttribute(Attributes.s_ReductionRatio, String.Empty);
                    if (!String.IsNullOrEmpty(attr))
                    {
                        hash[Models.s_FieldReductionRatio] = ValueListHelper.SeparateUnsignedValueList(attr);
                    }
                }
            }
        }


        protected override void PrepareApplicationRelations(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for ApplicationRelations object.
            hash.Add(Models.s_FieldPullModuleAlarmSupported, null);

            base.PrepareApplicationRelations(nav, ref hash);

            if (nav != null)
            {
                // Get PullModuleAlarmSupported attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_PullModuleAlarmSupported, String.Empty);
                hash[Models.s_FieldPullModuleAlarmSupported] = Help.GetBool(attr, Attributes.s_DefaultPullModuleAlarmSupported);
            }
            else
            {
                hash[Models.s_FieldPullModuleAlarmSupported] = Attributes.s_DefaultPullModuleAlarmSupported;
            }

            // --------------------------------------------
        }

        protected override void PrepareModuleObject(XPathNavigator nav, ref Hashtable hash)
        {
            // Call base class method first.
            base.PrepareModuleObject(nav, ref hash);

            // Attribute ID. Required.
            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldGsdId] = Help.CollapseWhitespace(attr);
            }
        }

        #endregion

        #endregion

    }
}

