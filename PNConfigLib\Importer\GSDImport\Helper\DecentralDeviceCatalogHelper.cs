/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceCatalogHelper.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceCatalogHelper.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */

using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;

namespace PNConfigLib.Importer.GSDImport.Helper
{
    /// <summary>
    /// Contains helper methods to access device catalog
    /// </summary>
    internal static class DecentralDeviceCatalogHelper
    {
        /// <summary>
        /// Gets decentral device catalog object using gsdml id of the device and gsdml file path
        /// </summary>
        /// <param name="gsdPath"></param>
        /// <param name="gsdId"></param>
        /// <returns></returns>
        internal static DecentralDeviceCatalog GetDecentralDeviceCatalogWithGsdPath(string gsdPath, string gsdId)
        {
            string deviceGsdKey = CatalogHelper.GetGsdKeyByGsdPath(gsdPath, gsdId);
            return GetDecentralDeviceCatalog(deviceGsdKey);
        }

        /// <summary>
        /// Gets decentral device catalog object using gsdml id of the device and gsdml file name
        /// </summary>
        /// <param name="gsdName"></param>
        /// <param name="gsdId"></param>
        /// <returns></returns>
        internal static DecentralDeviceCatalog GetDecentralDeviceCatalogWithGsdName(string gsdName, string gsdId)
        {
            string deviceGsdKey = CatalogHelper.GetGsdKeyByGsdName(gsdName, gsdId);
            return GetDecentralDeviceCatalog(deviceGsdKey);
        }

        /// <summary>
        /// Gets decentral device catalog object from the catalog
        /// </summary>
        /// <param name="deviceGsdKey"></param>
        /// <returns></returns>
        private static DecentralDeviceCatalog GetDecentralDeviceCatalog(string deviceGsdKey)
        {
            DecentralDeviceCatalog retval = null;

            if (Catalog.DeviceList != null
                && Catalog.DeviceList.ContainsKey(deviceGsdKey))
            {
                retval = Catalog.DeviceList[deviceGsdKey];
            }

            return retval;
        }
    }
}
