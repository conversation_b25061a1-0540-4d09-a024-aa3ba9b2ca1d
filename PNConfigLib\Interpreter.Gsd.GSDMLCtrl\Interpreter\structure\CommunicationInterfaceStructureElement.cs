/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CommunicationInterfaceStructureElement.cs :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Runtime.InteropServices;
using GSDI;
using C = PNConfigLib.Gsd.Interpreter.Common;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Structure
{
    /// <summary>
    /// Summary description for RecordDataStructureElement.
    /// </summary>
    //[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C8B")] 
    public class CommunicationInterfaceStructureElement :
        C.GsdObject,
        GSDI.ICommunicationInterfaceStructureElement // Interface
    {

		#region Initialization & Termination

		/// <summary>
		/// Creates an instance of this class. 
		/// </summary>
		public CommunicationInterfaceStructureElement()
		{
            m_CIMID = String.Empty;
            m_VendorIdentNumber = 0;
            m_DeviceIdentNumber = 0;
            m_Instance = 0;

        }
        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_CIMID;
        private uint m_VendorIdentNumber;
        private uint m_DeviceIdentNumber;
        private uint m_Instance;

        #endregion

        #region Properties

        public string CIM_ID => this.m_CIMID;

        public UInt32 VendorIdentNumber => this.m_VendorIdentNumber;

        public UInt32 DeviceIdentNumber => this.m_DeviceIdentNumber;

        public UInt32 Instance => this.m_Instance;


        #region COM Interface Members Only

        #endregion

        #endregion


        #region GsdObject Members


        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldCimId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_CIMID = hash[member] as string;

                member = Models.s_FieldVendorIdentNumber;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_VendorIdentNumber = (uint)hash[member];

                member = Models.s_FieldDeviceIdentNumber;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_DeviceIdentNumber = (uint)hash[member];

                member = Models.s_FieldInstance;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_Instance = (uint)hash[member];

                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }


		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
			writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectRecordDataStructureElement);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        override internal bool SerializeMembers(C.SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldCimId, this.m_CIMID);
            Export.WriteUint32Property(ref writer, Models.s_FieldVendorIdentNumber, this.m_VendorIdentNumber);
            Export.WriteUint32Property(ref writer, Models.s_FieldDeviceIdentNumber, this.m_DeviceIdentNumber);
            Export.WriteUint32Property(ref writer, Models.s_FieldInstance, this.m_Instance);

            return true;
        }



        #endregion

    }
}
