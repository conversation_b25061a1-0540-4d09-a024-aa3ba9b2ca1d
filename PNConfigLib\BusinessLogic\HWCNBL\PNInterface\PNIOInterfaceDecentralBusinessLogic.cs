/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIOInterfaceDecentralBusinessLogic.cs    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Addresses;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIOInterfaceDecentralBusinessLogic : IFDecorator
    {
        private const int m_DCPHelloReqOnLinkUp = 1;

        private const int m_DefaultFSHelloInterval = 30;

        private const int m_DefaultFSHelloRetry = 15;

        public PNIOInterfaceDecentralBusinessLogic(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        /// <summary>
        /// Gets the Supported PNPlanner Frame Classes of this submodule and the Controller
        /// Interface Submodule and returns the Common PNPlanner Frame Classes.
        /// </summary>
        /// <returns>Build the intersection of the supported frame classes of IO-D and IO-C </returns>
        private static List<PNIOFrameClass> GetCommonFrameClasses(Interface interfaceSubmodule)
        {
            List<PNIOFrameClass> commonFrameClasses = new List<PNIOFrameClass>();

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);

            if (controllerInterfaceSubmodule != null)
            {
                //Cut IRTflex: PNIoSuppCommClasses --> PNIoSuppFrameClass
                AttributeAccessCode accessCode = new AttributeAccessCode();

                Enumerated controllerRtClassesEnumerated =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppFrameClass,
                        accessCode,
                        null);

                if (accessCode.IsOkay)
                {
                    List<PNIOFrameClass> suppFrameClasses = PNAttributeUtility.GetSuppFrameClasses(interfaceSubmodule);

                    foreach (Enumerated child in controllerRtClassesEnumerated.List)
                    {
                        // Get the value of the enum.
                        PNIOFrameClass frameClass =
                            (PNIOFrameClass)Enum.Parse(typeof(PNIOFrameClass), (string)child.Value, true);

                        // Check whether both Submodules support the RtClass
                        if (suppFrameClasses.Contains(frameClass))
                        {
                            // Add the class to the global list.
                            commonFrameClasses.Add(frameClass);
                        }
                    }
                }
            }
            return commonFrameClasses;
        }

        /// <summary>
        /// Gets the Supported Synchronization Protocols of this submodule and the Controller
        /// Interface Submodule and returns the Common Protocols
        /// </summary>
        /// <returns>The list of the Common Protocols</returns>
        public static List<PNIRTSupportedSyncProtocols> GetCommonSyncProtocols(Interface interfaceSubmodule)
        {
            List<PNIRTSupportedSyncProtocols> commonSyncProtocols = new List<PNIRTSupportedSyncProtocols>();

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);

            if (controllerInterfaceSubmodule != null)
            {
                // Get the related Composite Attribute
                AttributeAccessCode accessCode = new AttributeAccessCode();

                Enumerated controllerSyncProtocolsEnumerated =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSupportedSyncProtocols,
                        accessCode,
                        null);

                if (accessCode.IsOkay
                    && (controllerSyncProtocolsEnumerated != null)
                    && (controllerSyncProtocolsEnumerated.List != null))
                {
                    foreach (Enumerated child in controllerSyncProtocolsEnumerated.List)
                    {
                        // Get the value of the enum.
                        PNIRTSupportedSyncProtocols syncProtocol =
                            (PNIRTSupportedSyncProtocols)
                            Enum.Parse(typeof(PNIRTSupportedSyncProtocols), (string)child.Value, true);

                        // Check whether both Submodules support the Protocol
                        if (GetSuppSyncProtocols(interfaceSubmodule).Contains(syncProtocol))
                        {
                            // Add the class to the global list.
                            commonSyncProtocols.Add(syncProtocol);
                        }
                    }
                }
            }

            return commonSyncProtocols;
        }

        /// <summary>
        /// Gets the Supported Synchronization Protocols of the Interface Submodule
        /// </summary>
        /// <returns>The list of the Supported Protocols</returns>
        public static List<PNIRTSupportedSyncProtocols> GetSuppSyncProtocols(Interface interfaceSubmodule)
        {
            List<PNIRTSupportedSyncProtocols> suppSyncProtocols = new List<PNIRTSupportedSyncProtocols>();

            // Get the related Composite Attribute
            AttributeAccessCode accessCode = new AttributeAccessCode();

            Enumerated syncProtocolsEnumerated =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    accessCode,
                    null);

            if (accessCode.IsOkay)
            {
                foreach (Enumerated child in syncProtocolsEnumerated.List)
                {
                    // Get the value of the enum.
                    PNIRTSupportedSyncProtocols syncProtocol =
                        (PNIRTSupportedSyncProtocols)
                        Enum.Parse(typeof(PNIRTSupportedSyncProtocols), (string)child.Value, true);

                    // Add the class to the global list.
                    suppSyncProtocols.Add(syncProtocol);
                }
            }

            return suppSyncProtocols;
        }

        public override void InitBL()
        {
            InitActions();
            InitAttributes();
        }

        public override void Configure(IConfigInterface xmlDeviceInterface, SyncDomainType syncDomainType = null)
        {
            DecentralDeviceTypeDecentralDeviceInterface xmlDecentralDeviceInterface =
                xmlDeviceInterface as DecentralDeviceTypeDecentralDeviceInterface;
            base.Configure(xmlDeviceInterface, syncDomainType);
            #region Attributes kept in Interface

            FillGeneralAttributes(Interface, xmlDecentralDeviceInterface.General, xmlDecentralDeviceInterface.InterfaceRefID);

            Interface.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PositionNumber, 1);
            Interface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnIoDeviceFSUPriority,
                xmlDecentralDeviceInterface.AdvancedOptions.InterfaceOptions.PrioritizedStartup);
            Interface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnLLDPLegacyEnforce,
                xmlDecentralDeviceInterface.AdvancedOptions.InterfaceOptions.UseIECV22LLDPMode);
            Interface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnIoInterfaceIsOptional,
                xmlDecentralDeviceInterface.AdvancedOptions.InterfaceOptions.OptionalIODevice);

            #endregion

            #region Attributes kept in IOConnector

            SyncRole syncRole =
                xmlDecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization.SynchronizationRole;
            PNIRTSyncRole mappedSyncRole = AttributeUtilities.MapSyncRoleEnum(syncRole);
            AttributeUtilities.SetSyncRole(Interface, mappedSyncRole);

            Interface.PNIOD.AttributeAccess.SetAnyAttribute<int>(
                InternalAttributeNames.PnStationNumber,
                (int)xmlDecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.DeviceNumber);
            Interface.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                InternalAttributeNames.PnIoWatchdogFactor,
                xmlDecentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCycle.AcceptedUpdateCyclesWithoutIOData);

            IOCycleTypeUpdateTime updateTime =
                xmlDecentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCycle.UpdateTime;

            if (updateTime != null && updateTime.ValueSpecified)
            {
                updateTime.Mode = IOCycleTypeUpdateTimeMode.Manual;
            }
            if ((updateTime == null)
                || (updateTime.Mode == IOCycleTypeUpdateTimeMode.Automatic))
            {
                Interface.PNIOD.AttributeAccess.SetAnyAttribute<byte>(InternalAttributeNames.PnUpdateTimeMode, 0);
            }
            else
            {
                Interface.PNIOD.AttributeAccess.SetAnyAttribute<byte>(InternalAttributeNames.PnUpdateTimeMode, 1);
                Interface.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                    InternalAttributeNames.PnIoUserAdjustedUpdTime,
                    (long)updateTime.Value * 32);

                AttributeAccessCode ac = new AttributeAccessCode();
                Interface interfaceObj = Interface.PNIOD.AssignedController.ParentObject as Interface;
                if (interfaceObj != null)
                {
                    long sendClockFactor =
                        interfaceObj.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            ac,
                            -1);
                    if (!ac.IsOkay)
                    {
                        throw new PNFunctionsException("Could not get send clock factor for IO device.");
                    }
                    Interface.PNIOD.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                        (long)(updateTime.Value * 32 / sendClockFactor));
                }
            }

            #endregion

            Interface.Node.NodeBusinessLogic.Configure(xmlDecentralDeviceInterface);
            Dictionary<uint, byte[]> parameterRecordDataItems = FillParameterRecordDataItems(
                Interface,
                xmlDecentralDeviceInterface.ParameterRecordDataItems,
                xmlDecentralDeviceInterface.InterfaceRefID);
            Interface.SetParameterRecordDataItems(parameterRecordDataItems);
        }

        /// <summary>
        /// This generic function creates an AlarmCRData Record.
        /// </summary>
        private void GenericMethodGetAlarmCRDataStruct(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }
            if (Strategy == null)
            {
                return;
            }
            methodData.Arguments[GetAlarmCRDataStruct.AlarmCRDataStruct] = Utility.GetAlarmCrData(Strategy);
        }

        /// <summary>
        /// </summary>
        private void GenericMethodGetCommonSyncProtocolsAndFrameClasses(IMethodData methodData)
        {
            methodData.ReturnValue = true;

            methodData.Arguments[GetCommonSyncProtocolsAndFrameClasses.CommonFrameClasses] =
                GetCommonFrameClasses(this.Interface);

            methodData.Arguments[GetCommonSyncProtocolsAndFrameClasses.CommonSyncProtocols] =
                GetCommonSyncProtocols(this.Interface);
        }

        /// <summary>
        /// This generic function deliver all Records of DataRecordsConf at Interface.
        /// </summary>
        private void GenericMethodGetDataRecordsConf(IMethodData methodData)
        {
            PclObject module = methodData.Arguments[GetDataRecordsConf.Module] as PclObject;
            Interface ioControllerInterface =
                methodData.Arguments[GetDataRecordsConf.ioControllerInterface] as Interface;

            byte[] dataRecords = null;

            // At this point we make a decision if the datasets for the module can be fetched from the cache
            // in PNDeviceConfigStrategy or if it is necessary to create the datasets.
            DataModel.PCLObjects.Port port = module as DataModel.PCLObjects.Port;
            if ((module is Interface && ((module as Interface).PNIOC == null))
                || (module is DataModel.PCLObjects.Port && port.ParentObject is Interface
                    && ((port.ParentObject as Interface).PNIOC == null)))
            {
                dataRecords = Utility.GetDataRecordsConf(module, this.Interface, ioControllerInterface, Strategy);
                methodData.ReturnValue = dataRecords;
                return;
            }

            if (Strategy != null)
            {
                dataRecords = Strategy.GetDataRecords(module);
            }

            // Fallback: if the cache has no records, try the old (and slow) method.
            if (dataRecords == null)
            {
                dataRecords = Utility.GetDataRecordsConf(module, this.Interface, ioControllerInterface, Strategy);
            }
            methodData.ReturnValue = dataRecords;
        }

        private void GenericMethodGetFSHelloBlock(IMethodData data)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            data.ReturnValue = true;
            data.Arguments[GetFSHelloBlock.FSHelloBlock] = new byte[0];
            bool pnDCPHelloSupported =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDCPHelloSupported,
                    ac,
                    false);

            if (pnDCPHelloSupported)
            {
                ac.Reset();
                bool pnDeviceFSUPriority =
                    this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoDeviceFSUPriority,
                        ac,
                        false);
                if (pnDeviceFSUPriority)
                {
                    FSHelloBlockStruct fsHelloBlockStruct = new FSHelloBlockStruct();
                    fsHelloBlockStruct.FSHelloInterval = m_DefaultFSHelloInterval;
                    fsHelloBlockStruct.FSHelloRetry = m_DefaultFSHelloRetry;

                    fsHelloBlockStruct.FSHelloMode = m_DCPHelloReqOnLinkUp;
                    fsHelloBlockStruct.FSHelloDelay = 0;

                    data.Arguments[GetFSHelloBlock.FSHelloBlock] = fsHelloBlockStruct.ToByteArray;
                }
            }
        }

        /// <summary>
        /// This method fills the IOCRStruct with the attributes of PNPlannerDataObject.
        /// If an attribute cannot be retrieved properly,
        /// false is ruturned in  methodData.ReturnValue
        /// </summary>
        /// <param name="methodData"></param>
        private void GenericMethodGetIocrEntry(IMethodData methodData)
        {
            Utility.GetIocrEntry(methodData, Interface);
        }

        /// <summary>
        /// This method deliver all records needed for ioDevParamConfig
        /// </summary>
        private void GenericMethodGetIoDevParam(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            if (Strategy == null)
            {
                return;
            }
            Interface ioControllerInterface =
                methodData.Arguments[GetDataRecordsConf.ioControllerInterface] as Interface;
            methodData.ReturnValue = Utility.GetIoDevParamData(Strategy, this.Interface, ioControllerInterface, false);
            Strategy.ReleaseCaches();
        }

        /// <summary>
        /// </summary>
        private void GenericMethodGetIsoMParameterEntry(IMethodData methodData)
        {
            methodData.ReturnValue = true;

            IsoMParameterStruct parameterStruct = new IsoMParameterStruct();

            //Length of the Data Record
            parameterStruct.ParaBlockLength = 0x001C;

            //Detect, whether slave is clock-sync-enabled. If not, then return 0
            AttributeAccessCode ac = new AttributeAccessCode();

            parameterStruct.SlotNumber = Convert.ToInt16(
                methodData.Arguments[GetIsoMParameterEntry.SlotNumber],
                CultureInfo.InvariantCulture);
            parameterStruct.SubslotNumber = Convert.ToInt16(
                methodData.Arguments[GetIsoMParameterEntry.SubSlotNumber],
                CultureInfo.InvariantCulture);

            parameterStruct.ControllerApplicationCycleFactor = 0x0001;

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(this.Interface);
            parameterStruct.TimeDataCycle = controllerInterfaceSubmodule == null
                                                       ? 0
                                                       : (ushort)
                                                         controllerInterfaceSubmodule.AttributeAccess
                                                             .GetAnyAttribute<long>(
                                                                 InternalAttributeNames.PnIoSendClockFactor,
                                                                 ac.GetNew(),
                                                                 0);
            parameterStruct.TimeIOInput =
                (uint)this.Interface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.ClockSyncTi, ac, 0);
            parameterStruct.TimeIOOutput =
                (uint)this.Interface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.ClockSyncTo, ac, 0);
            parameterStruct.TimeIOInputValid =
                (uint)this.Interface.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.ClockSyncTiValid,
                    ac,
                    0);
            parameterStruct.TimeIOOutputValid =
                (uint)this.Interface.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.ClockSyncToValid,
                    ac,
                    0);

            byte[] block = parameterStruct.ToByteArray;
            methodData.Arguments[GetIsoMParameterEntry.IsoMParameterEntry] = block;
        }

        /// <summary>
        /// This method fills the LocalScfAdaptionEntryStruct with the attributes of PNPlannerDataObject.
        /// If an attribute cannot be retrieved properly,
        /// false is ruturned in  methodData.ReturnValue
        /// </summary>
        private void GenericMethodGetLocalScfAdaptionEntry(IMethodData methodData)
        {
            methodData.ReturnValue = true;
            IPNFrameData frameData = (IPNFrameData)methodData.Arguments[GetLocalScfAdaptionEntry.PNFrameData];

            if (null == frameData)
            {
                methodData.ReturnValue = false;
                return;
            }
            methodData.Arguments[GetLocalScfAdaptionEntry.LocalSCFAdaptionEntry] =
                Utility.GetLocalScfAdaptionEntry(this.Interface, frameData);
        }

        private void InitActions()
        {
            this.Interface.BaseActions.RegisterMethod(GetAlarmCRDataStruct.Name, GenericMethodGetAlarmCRDataStruct);
            this.Interface.BaseActions.RegisterMethod(GetFSHelloBlock.Name, GenericMethodGetFSHelloBlock);
            this.Interface.BaseActions.RegisterMethod(GetIoDevParam.Name, GenericMethodGetIoDevParam);
            this.Interface.BaseActions.RegisterMethod(GetDataRecordsConf.Name, GenericMethodGetDataRecordsConf);
            this.Interface.BaseActions.RegisterMethod(GetIocrEntry.Name, GenericMethodGetIocrEntry);
            this.Interface.BaseActions.RegisterMethod(GetPdSyncDataParameters.Name, GenericMethodGetPdSyncDataStruct);
            this.Interface.BaseActions.RegisterMethod(
                GetLocalScfAdaptionEntry.Name,
                GenericMethodGetLocalScfAdaptionEntry);
            this.Interface.BaseActions.RegisterMethod(GetIsoMParameterEntry.Name, GenericMethodGetIsoMParameterEntry);
            this.Interface.BaseActions.RegisterMethod(
                GetCommonSyncProtocolsAndFrameClasses.Name,
                GenericMethodGetCommonSyncProtocolsAndFrameClasses);

            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodCheckConsistency);
        }

        private void InitAttributes()
        {
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoDeviceFSUPriority, false);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnLLDPLegacyEnforce, false);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoInterfaceIsOptional, false);
            Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.InputDataLength,
                GetInputDataLength,
                null);

            Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.OutputDataLength,
                GetOutputDataLength,
                null);

            Interface.AttributeAccess.AddAnyAttribute<int>(
                InternalAttributeNames.DeviceFunctionStatus,
                GetDeviceFunctionStatus,
                null);

            Interface.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnIoOperatingMode,
                (uint)PNIOOperatingModes.IODevice);

            Interface.AttributeAccess.AddAnyAttribute<Guid>(InternalAttributeNames.PnIoArUuid, Guid.NewGuid());
            Interface.AttributeAccess.AddAnyAttribute(InternalAttributeNames.PnMrpDiagnosis, false);
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIoNumberOfAR, 1); // Default value
        }

        private void MethodCheckConsistency()
        {
            CheckConsistencyUtility.CheckConsistency_All_Ports_Deactivated(Interface);

            FastStartupUtility.CheckConsistencyFastStartup(Interface);

            //Optional Device related consistencies.
            if (MachineTailorUtility.IsOptionalDeviceEnabled(Interface))
            {
                OptionalDeviceOptionsDecentral optionalDevice = new OptionalDeviceOptionsDecentral(Interface);
                optionalDevice.OptionalDevice.CheckConsistency();
            }

            CheckConsistencyUtility.ConsistencyCheck_DeviceExchangeWithoutMMC(Interface);
            //Consistency check for max number of submodules
            CheckConsistencyUtility.ConsistencyCheck_NumberOfSubmodules(Interface);

            CheckConsistencyUtility.IsActivateDcpReadOnlyConfigurable(Interface);
        }

        #region Attribute callbacks

        private int GetInputDataLength()
        {
            IList<PclObject> modules = PNNavigationUtility.GetModulesSorted(this.Interface);

            int result = 0;
            foreach (PclObject moduleVar in modules)
            {
                Module module = (Module)moduleVar;
                if (module == null)
                {
                    continue;
                }

                DataAddress inAdr, outAdr;
                bool isDiagAdr;

                AddressUtility.GetAddressObjectsOfModule(module, out inAdr, out outAdr, out isDiagAdr);

                if (inAdr != null)
                {
                    int length = inAdr.Length;
                    result += length;
                    continue;
                }

                List<Submodule> submodules = module.GetSubmodules();
                if (submodules != null)
                {
                    foreach (Submodule submodule in submodules)
                    {
                        if (AttributeUtilities.HasInputSignals(submodule))
                        {
                            AddressUtility.GetAddressObjectsOfModule(submodule, out inAdr, out outAdr, out isDiagAdr);

                            if (inAdr != null)
                            {
                                int length = inAdr.Length;
                                result += length;
                            }
                        }
                    }
                }
            }
            return result;
        }

        private int GetOutputDataLength()
        {
            IList<PclObject> modules = PNNavigationUtility.GetModulesSorted(this.Interface);
            int result = 0;
            foreach (PclObject moduleVar in modules)
            {
                Module module = (Module)moduleVar;
                if (module == null)
                {
                    continue;
                }

                DataAddress inAdr, outAdr;
                bool isDiagAdr;
                AddressUtility.GetAddressObjectsOfModule(module, out inAdr, out outAdr, out isDiagAdr);
                if (outAdr != null)
                {
                    int length = outAdr.Length;
                    result += length;
                    continue;
                }

                List<Submodule> submodules = module.GetSubmodules();
                if (submodules != null)
                {
                    foreach (Submodule submodule in submodules)
                    {
                        if (AttributeUtilities.HasOutputSignals(submodule))
                        {
                            AddressUtility.GetAddressObjectsOfModule(submodule, out inAdr, out outAdr, out isDiagAdr);
                            if (outAdr != null)
                            {
                                int length = outAdr.Length;
                                result += length;
                            }
                        }
                    }
                }
            }
            return result;
        }

        private int GetDeviceFunctionStatus()
        {
            int deviceFunctionStatus = 0x01;

            return deviceFunctionStatus;
        }

        #endregion

        /// <summary>
        /// This method fills the PDSyncDataStruct with the attributes of the Sync-Domain 
        /// and the synchronization parameters of the Interface Submodule. 
        /// </summary>
        private void GenericMethodGetPdSyncDataStruct(IMethodData methodData)
        {
            if (methodData == null)
                return;

            if (ConfigUtility.IsRTSync(Interface, PNInterfaceType.IODevice) == false)
                return;

            if (Interface.SyncDomain != null)
            {
                // Generate the related Data Block.
                CompileUtility.FillPdSyncDataStruct(methodData, Interface, Interface.SyncDomain, PNInterfaceType.IODevice);
            }
            else
            {
                methodData.ReturnValue = false;
            }
        }
    }
}