/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNFunctionsDefaultAttributeValues.cs      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL.Constants.AttributeValues
{
    /// <summary>
    /// Default attribute values for PNFunctions attributes.
    /// </summary>
    internal static class PNFunctionsDefaultAttributeValues
    {
        /// <summary>
        /// Default value of frame multiplier for devices.
        /// </summary>
        public const int DefaultPNFrameMultiplier = 1;

        /// <summary>
        /// Default value of PNIOMaxDeviceInputDataLength.
        /// </summary>
        public const long DefaultPNIOMaxDeviceInputDataLength = 1440;

        /// <summary>
        /// Default value of PNIOMaxDeviceOutputDataLength.
        /// </summary>
        public const long DefaultPNIOMaxDeviceOutputDataLength = 1440;

        /// <summary>
        /// Default value of PNIOMaxFrameStartTime.
        /// </summary>
        public const int DefaultPNIOMaxFrameStartTime = 1600;

        /// <summary>
        /// Default value of PNIOMinNRTGap.
        /// </summary>
        public const int DefaultPNIOMinNRTGap = 960;

        /// <summary>
        /// Default value of ScfAdaptionNonPow2Supported for controllers.
        /// </summary>
        public const bool DefaultPNIOScfAdaptionNonPow2Supported = false;

        /// <summary>
        /// Default value of ScfAdaptionSupported for controllers.
        /// </summary>
        public const bool DefaultPNIOScfAdaptionSupported = false;

        /// <summary>
        /// Default value of PNIrtMaxRangeIRFrameID for IRT interface submodules.
        /// </summary>
        public const uint DefaultPNIRTMaxRangeIRFrameID = 1024;

        /// <summary>
        /// Default value of MinFrameIntFactor for devices.
        /// </summary>
        public const long DefaultPNMinFrameIntFactor = 32;

        /// <summary>
        /// Default value of PowerOnToCommReady for devices.
        /// </summary>
        public const int DefaultPNPowerOnToCommReady = 0;

        /// <summary>
        /// Default value of send clock factor controllers and devices.
        /// </summary>
        public const long DefaultPNSendClockFactor = 32;

        /// <summary>
        /// Default value of PNPlannerDistributionMode for controllers.
        /// </summary>
        public const uint DefaultPNPlannerDistributionMode = 1;

        /// <summary>
        /// Default value of update time mode for devices.
        /// </summary>
        public const byte DefaultPNUpdateTimeMode = 0;

        /// <summary>
        /// Default value for supported send clock factors for controllers and devices for RT.
        /// </summary>
        private static int[] m_DefaultPNRTSupportedSendClockFactors = { 32 };

        /// <summary>
        /// Default value for supported reduction ratios for controllers and devices,
        /// for class 1 and 2 frames and send clocks that are not power of 2.
        /// </summary>
        private static int[] m_DefaultPNSuppRRNonPow12 = { 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 };

        /// <summary>
        /// Default value for supported reduction ratios for controllers and devices,
        /// for class 3 frames and send clocks that are not power of 2.
        /// </summary>
        private static int[] m_DefaultPNSuppRRNonPow3 = { 1, 2, 4, 8, 16 };

        /// <summary>
        /// Default value for supported reduction ratios for controllers and devices,
        /// for class 1 and 2 frames and send clocks that are power of 2.
        /// </summary>
        private static int[] m_DefaultPNSuppRRPow12 = { 1, 2, 4, 8, 16, 32, 64, 128, 256, 512 };

        /// <summary>
        /// Default value for supported reduction ratios for controllers and devices,
        /// for class 3 frames and send clocks that are power of 2.
        /// </summary>
        private static int[] m_DefaultPNSuppRRPow3 = { 1, 2, 4, 8, 16 };

        /// <summary>
        /// Getter for m_DefaultPNRTSupportedSendClockFactors.
        /// </summary>
        public static IList<int> DefaultPNRTSupportedSendClockFactors
        {
            get { return m_DefaultPNRTSupportedSendClockFactors; }
        }

        /// <summary>
        /// Getter for m_DefaultPNSuppRRNonPow12.
        /// </summary>
        public static IList<int> DefaultPNSuppRRNonPow12 => m_DefaultPNSuppRRNonPow12; 

        /// <summary>
        /// Getter for m_DefaultPNSuppRRNonPow3.
        /// </summary>
        public static IList<int> DefaultPNSuppRRNonPow3 =>  m_DefaultPNSuppRRNonPow3; 
        

        /// <summary>
        /// Getter for m_DefaultPNSuppRRPow12.
        /// </summary>
        public static IList<int> DefaultPNSuppRRPow12 =>  m_DefaultPNSuppRRPow12; 

        /// <summary>
        /// Getter for m_DefaultPNSuppRRPow3.
        /// </summary>
        public static IList<int> DefaultPNSuppRRPow3 => m_DefaultPNSuppRRPow3; 
    }
}