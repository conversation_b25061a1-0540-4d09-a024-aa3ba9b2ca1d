<Window x:Class="PNConfigTool.Views.Windows.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PNConfigTool.Views.Windows"
        xmlns:vm="clr-namespace:PNConfigTool.ViewModels"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="600" Width="950"
        Background="{StaticResource BackgroundColor}">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVis" />
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{StaticResource SecondaryColor}">
            <MenuItem Header="文件">
                <MenuItem Header="新建项目" Command="{Binding NewProjectCommand}"/>
                <MenuItem Header="打开项目" Command="{Binding OpenProjectCommand}"/>
                <MenuItem Header="保存项目" Command="{Binding SaveProjectCommand}"/>
                <MenuItem Header="另存为" Command="{Binding SaveProjectAsCommand}"/>
                <Separator/>
                <MenuItem Header="退出" Command="{Binding ExitCommand}"/>
            </MenuItem>
            <MenuItem Header="编辑">
                <MenuItem Header="撤销"/>
                <MenuItem Header="重做"/>
            </MenuItem>
            <MenuItem Header="视图">
                <MenuItem Header="工具栏"/>
                <MenuItem Header="状态栏"/>
            </MenuItem>
            <MenuItem Header="工具">
                <MenuItem Header="GSDML管理" Command="{Binding ManageGSDMLCommand}"/>
                <MenuItem Header="GSDML目录查看器" Command="{Binding ViewGSDMLCatalogCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助">
                <MenuItem Header="关于" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>
        
        <!-- 工具栏 -->
        <ToolBar Grid.Row="1" Background="{StaticResource SecondaryColor}">
            <Button ToolTip="新建项目" Command="{Binding NewProjectCommand}">
                <TextBlock Text="&#xE710;" FontFamily="Segoe MDL2 Assets" FontSize="16"/>
            </Button>
            <Button ToolTip="打开项目" Command="{Binding OpenProjectCommand}">
                <TextBlock Text="&#xE838;" FontFamily="Segoe MDL2 Assets" FontSize="16"/>
            </Button>
            <Button ToolTip="保存项目" Command="{Binding SaveProjectCommand}">
                <TextBlock Text="&#xE74E;" FontFamily="Segoe MDL2 Assets" FontSize="16"/>
            </Button>
            <Separator/>
            <Button ToolTip="GSDML管理" Command="{Binding ManageGSDMLCommand}">
                <TextBlock Text="&#xE7B8;" FontFamily="Segoe MDL2 Assets" FontSize="16"/>
            </Button>
            <Button ToolTip="GSDML目录查看器" Command="{Binding ViewGSDMLCatalogCommand}">
                <TextBlock Text="&#xE890;" FontFamily="Segoe MDL2 Assets" FontSize="16"/>
            </Button>
        </ToolBar>
        
        <!-- 主内容区域 -->
        <TabControl Grid.Row="2" Background="White">
            <TabItem Header="组态步骤">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="140" MinWidth="100" MaxWidth="300"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 左侧导航面板 -->
                    <ListBox Grid.Column="0" Background="{StaticResource SecondaryColor}" x:Name="NavigationListBox"
                            BorderThickness="0" Padding="0,10" SelectionChanged="NavigationListBox_SelectionChanged">
                        <ListBox.Resources>
                            <!-- 自定义ListBoxItem样式 -->
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="15,10"/>
                                <Setter Property="Margin" Value="0,1"/>
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Cursor" Value="Hand"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ListBoxItem">
                                            <Border x:Name="ItemBorder" 
                                                    Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="0"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="#E1EEFE"/>
                                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                                    <Setter Property="Foreground" Value="#0067C0"/>
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E9EFF5"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </ListBox.Resources>
                        <ListBoxItem Content="设置PROFINET网络" Tag="NetworkConfigPage" x:Name="NetworkConfigNavItem">
                            <ListBoxItem.InputBindings>
                                <MouseBinding MouseAction="LeftClick" 
                                             Command="{Binding NavigateToNetworkCommand}"/>
                            </ListBoxItem.InputBindings>
                        </ListBoxItem>
                        <ListBoxItem Content="控制器组件" Tag="ControllerConfigPage" x:Name="ControllerNavItem"
                                   Visibility="{Binding IsProjectOpen, Converter={StaticResource BoolToVis}}">
                            <ListBoxItem.InputBindings>
                                <MouseBinding MouseAction="LeftClick" 
                                             Command="{Binding NavigateToControllerCommand}"/>
                            </ListBoxItem.InputBindings>
                        </ListBoxItem>
                    </ListBox>

                    <!-- 分隔条 -->
                    <GridSplitter Grid.Column="1" 
                                Width="5"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Stretch"
                                Background="{StaticResource SecondaryColor}"
                                ShowsPreview="True"/>
                    
                    <!-- 右侧内容区域 -->
                    <Frame Grid.Column="2" x:Name="ContentFrame" NavigationUIVisibility="Hidden"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Background="{StaticResource SecondaryColor}">
            <StatusBarItem Content="{Binding StatusMessage}"/>
        </StatusBar>
    </Grid>
</Window>