using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Navigation;
using PNConfigTool.Views.Windows;
using PNConfigTool.Services;
using PNConfigTool.ViewModels;
using PNConfigTool.Models;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Diagnostics;
using PNConfigLib.Gsd.Interpreter;
using PNConfigLib.Gsd.Interpreter.Common;
using GSDI;

namespace PNConfigTool.Views.Pages
{
    /// <summary>
    /// DeviceCatalogPage.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceCatalogPage : Page
    {
        private readonly IGSDMLService _gsdmlService;
        private GSDMLCatalogViewModel _viewModel;

        public DeviceCatalogPage()
        {
            InitializeComponent();

            // 从服务定位器获取服务实例以利用单例缓存
            _gsdmlService = ServiceLocator.GetService<IGSDMLService>() ?? throw new InvalidOperationException("IGSDMLService not found in ServiceLocator");

            // 创建ViewModel并设置DataContext
            _viewModel = new GSDMLCatalogViewModel(_gsdmlService);
            DataContext = _viewModel;
            
            // 注册设备选择事件
            DeviceTreeView.SelectedItemChanged += DeviceTreeView_SelectedItemChanged;
            
            // 显示加载状态
            StatusTextBlock.Text = "正在加载GSDML目录...";
            StatusProgressBar.Visibility = Visibility.Visible;
            
            // 绑定ViewModel的属性以更新UI状态
            _viewModel.PropertyChanged += (sender, e) => 
            {
                if (e.PropertyName == nameof(GSDMLCatalogViewModel.IsLoading))
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        StatusProgressBar.Visibility = _viewModel.IsLoading ? Visibility.Visible : Visibility.Collapsed;
                    });
                }
                else if (e.PropertyName == nameof(GSDMLCatalogViewModel.StatusMessage))
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        StatusTextBlock.Text = _viewModel.StatusMessage;
                    });
                }
            };
        }

        /// <summary>
        /// 查找DAPNode所属的GSDMLDeviceNode
        /// </summary>
        public ViewModels.GSDMLDeviceNode? FindParentDeviceForDAP(DAPNode dapNode)
        {
            // 查找所有主类别
            foreach (var mainFamily in _viewModel.MainFamilies)
            {
                // 查找所有厂商
                foreach (var vendor in mainFamily.Vendors)
                {
                    // 查找所有产品系列
                    foreach (var productFamily in vendor.ProductFamilies)
                    {
                        // 查找所有设备
                        foreach (var device in productFamily.Devices)
                        {
                            // 检查设备的DAP列表是否包含当前DAP
                            if (device.DAPsFromInterpreter.Contains(dapNode))
                            {
                                return device;
                            }
                        }
                    }
                }
            }

            // 如果在主分类结构中没有找到，尝试在制造商结构中查找
            foreach (var manufacturer in _viewModel.Manufacturers)
            {
                foreach (var device in manufacturer.Devices)
                {
                    if (device.DAPsFromInterpreter.Contains(dapNode))
                    {
                        return device;
                    }
                }
            }

            // 未找到父设备
            return null;
        }

        /// <summary>
        /// TreeView选中项改变事件处理
        /// </summary>
        private void DeviceTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            // 清空设备详情
            OrderNumberTextBlock.Text = string.Empty;
            VersionTextBox.Text = string.Empty;
            DescriptionTextBlock.Text = string.Empty;

            // 只有选中DAP节点时才显示设备信息
            if (e.NewValue is DAPNode dapNode)
            {
                // 查找DAP节点所属的设备节点以获取GSDML文件路径
                ViewModels.GSDMLDeviceNode? parentDevice = FindParentDeviceForDAP(dapNode);

                // 版本号保持不变：显示GSDML完整文件名（字母转大写）
                if (parentDevice != null && !string.IsNullOrEmpty(parentDevice.FilePath))
                {
                    string gsdmlFileName = Path.GetFileName(parentDevice.FilePath).ToUpperInvariant();
                    VersionTextBox.Text = gsdmlFileName;
                }
                else
                {
                    // 如果找不到父设备或文件路径为空，则显示DAP名称（字母转大写）
                    VersionTextBox.Text = dapNode.Name.ToUpperInvariant();
                }

                // 动态获取订货号和说明信息
                string orderNumber = "DAP设备";
                string description = "设备访问点 (Device Access Point)";

                if (parentDevice != null && !string.IsNullOrEmpty(parentDevice.FilePath) && !string.IsNullOrEmpty(dapNode.Id))
                {
                    // 从GSDML文件中提取DAP信息
                    var dapInfo = ExtractDAPInfoFromGSDML(parentDevice.FilePath, dapNode.Id);
                    if (dapInfo != null)
                    {
                        orderNumber = dapInfo.OrderNumber ?? orderNumber;
                        description = dapInfo.Info ?? description;

                        Debug.WriteLine($"成功从GSDML提取DAP信息:");
                        Debug.WriteLine($"  订货号: {orderNumber}");
                        Debug.WriteLine($"  描述: {description}");
                    }
                    else
                    {
                        Debug.WriteLine("无法从GSDML文件提取DAP信息，使用默认值");
                    }
                }
                else
                {
                    Debug.WriteLine("缺少GSDML路径或DAP ID，使用默认值");
                    Debug.WriteLine($"  GSDML路径: {parentDevice?.FilePath ?? "null"}");
                    Debug.WriteLine($"  DAP ID: {dapNode.Id ?? "null"}");
                }

                // 设置订货号
                OrderNumberTextBlock.Text = orderNumber;

                // 如果有接口信息则添加到描述中
                if (dapNode.Children != null && dapNode.Children.Count > 0)
                {
                    description += $"\n\n接口: {dapNode.Children.Count}个";
                    foreach (var child in dapNode.Children)
                    {
                        if (child is HeaderNode headerNode)
                        {
                            description += $"\n- {headerNode.Title}";
                        }
                    }
                }

                DescriptionTextBlock.Text = description;
            }
            // 其他节点类型不显示任何信息，保持为空白
        }

        /// <summary>
        /// 从GSDML文件中提取DAP信息
        /// </summary>
        private DeviceCatalogDAPInfo? ExtractDAPInfoFromGSDML(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                // 如果gsdPath只是文件名，需要查找完整路径
                string? fullGsdPath = FindFullGSDMLPath(gsdPath);
                if (string.IsNullOrEmpty(fullGsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}");
                    return null;
                }

                Debug.WriteLine($"找到完整GSDML路径: {fullGsdPath}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(fullGsdPath, GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {fullGsdPath}");
                    return null;
                }

                Debug.WriteLine("GSDML文件加载成功，开始查找DAP");

                // 获取设备结构
                var deviceStructure = interpreter.GetDeviceStructureElement();
                if (deviceStructure == null)
                {
                    Debug.WriteLine("无法获取设备结构");
                    return null;
                }

                Debug.WriteLine($"设备结构获取成功，DAP数量: {deviceStructure?.DeviceAccessPoints?.Length ?? 0}");

                // 获取设备访问点
                var deviceAccessPoints = deviceStructure?.DeviceAccessPoints;
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("设备没有访问点");
                    return null;
                }

                Debug.WriteLine($"找到 {deviceAccessPoints.Length} 个设备访问点");

                // 查找匹配的DAP
                PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement? targetDAP = null;
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement accessPoint)
                    {
                        Debug.WriteLine($"检查DAP: Name={accessPoint.Name}, GsdID={accessPoint.GsdID}");
                        if (accessPoint.GsdID == gsdRefId)
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP: {accessPoint.Name ?? accessPoint.GsdID}");
                            break;
                        }
                    }
                }

                // 如果没有找到匹配的DAP，使用第一个DAP
                if (targetDAP == null && deviceAccessPoints.Length > 0)
                {
                    if (deviceAccessPoints.GetValue(0) is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement firstDAP)
                    {
                        targetDAP = firstDAP;
                        Debug.WriteLine($"使用第一个DAP: {firstDAP.Name ?? firstDAP.GsdID}");
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine("无法找到有效的DAP");
                    return null;
                }

                // 提取DAP信息
                var dapInfo = new DeviceCatalogDAPInfo();

                // 提取Category信息
                dapInfo.Category = !string.IsNullOrEmpty(targetDAP.Category) ? targetDAP.Category :
                                  (!string.IsNullOrEmpty(targetDAP.SubCategory1) ? targetDAP.SubCategory1 :
                                  (!string.IsNullOrEmpty(targetDAP.MainFamily) ? targetDAP.MainFamily : "未知"));

                // 提取OrderNumber信息
                dapInfo.OrderNumber = !string.IsNullOrEmpty(targetDAP.OrderNumber) ? targetDAP.OrderNumber : "未指定";

                // 提取Info信息
                dapInfo.Info = !string.IsNullOrEmpty(targetDAP.InfoText) ? targetDAP.InfoText : "无描述信息";

                Debug.WriteLine($"成功提取DAP信息:");
                Debug.WriteLine($"  Category: {dapInfo.Category}");
                Debug.WriteLine($"  OrderNumber: {dapInfo.OrderNumber}");
                Debug.WriteLine($"  Info: {dapInfo.Info}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 成功完成 ===");

                return dapInfo;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取DAP信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 异常结束 ===");
                return null;
            }
        }

        /// <summary>
        /// 查找GSDML文件的完整路径
        /// </summary>
        private string? FindFullGSDMLPath(string gsdFileName)
        {
            try
            {
                Debug.WriteLine($"开始查找GSDML文件: {gsdFileName}");

                // 如果已经是完整路径且文件存在，直接返回
                if (Path.IsPathRooted(gsdFileName) && File.Exists(gsdFileName))
                {
                    Debug.WriteLine($"文件已存在: {gsdFileName}");
                    return gsdFileName;
                }

                // 定义可能的搜索路径
                var searchPaths = new List<string>();

                // 1. 示例GSDML文件夹
                string exampleGsdmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GSDML_Example");
                if (Directory.Exists(exampleGsdmlPath))
                {
                    searchPaths.AddRange(Directory.GetDirectories(exampleGsdmlPath, "*", SearchOption.AllDirectories));
                    searchPaths.Add(exampleGsdmlPath);
                }

                // 2. 应用程序数据目录
                string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PNConfigTool", "GSDML");
                if (Directory.Exists(appDataPath))
                {
                    searchPaths.Add(appDataPath);
                }

                // 3. 项目GSDMLs文件夹（如果有当前项目）
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null && !string.IsNullOrEmpty(projectManager.CurrentProjectFilePath))
                {
                    string projectDirectory = Path.GetDirectoryName(projectManager.CurrentProjectFilePath) ?? "";
                    string projectGsdmlPath = Path.Combine(projectDirectory, "GSDMLs");
                    if (Directory.Exists(projectGsdmlPath))
                    {
                        searchPaths.Add(projectGsdmlPath);
                    }
                }

                // 在所有路径中搜索文件
                foreach (string searchPath in searchPaths)
                {
                    if (!Directory.Exists(searchPath)) continue;

                    // 直接匹配文件名
                    string fullPath = Path.Combine(searchPath, gsdFileName);
                    if (File.Exists(fullPath))
                    {
                        Debug.WriteLine($"找到GSDML文件: {fullPath}");
                        return fullPath;
                    }

                    // 尝试不区分大小写的搜索
                    var files = Directory.GetFiles(searchPath, "*.xml", SearchOption.TopDirectoryOnly);
                    foreach (string file in files)
                    {
                        if (string.Equals(Path.GetFileName(file), gsdFileName, StringComparison.OrdinalIgnoreCase))
                        {
                            Debug.WriteLine($"找到GSDML文件（不区分大小写）: {file}");
                            return file;
                        }
                    }
                }

                Debug.WriteLine($"未找到GSDML文件: {gsdFileName}");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找GSDML文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// DAP信息结构（DeviceCatalogPage专用）
        /// </summary>
        private class DeviceCatalogDAPInfo
        {
            public string? Category { get; set; }
            public string? OrderNumber { get; set; }
            public int InputLength { get; set; } = 0;
            public int OutputLength { get; set; } = 0;
            public string? Info { get; set; }
        }
    }
}