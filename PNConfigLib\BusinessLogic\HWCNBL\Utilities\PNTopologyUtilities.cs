/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNTopologyUtilities.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;

namespace PNConfigLib.HWCNBL.Utilities
{
    public class SortedGraphDictionary
    {
        public SortedGraphDictionary(SortedDictionary<string, List<string>> graphDictionary)
        {
            GraphDictionary = graphDictionary;
        }

        public SortedDictionary<string, List<string>> GraphDictionary { get; }
    }

    public class ListOfList
    {
        public ListOfList(IList<List<string>> listInList)
        {
            ListInList = (List<List<string>>)listInList;
        }

        public IList<List<string>> ListInList { get; }
    }

    internal class PNTopologyUtilities
    {
        #region Nested Classes
        /// <summary>
        /// Searchs all elementary cycles in a given object list.
        /// This is accomplished using the algorithm of Donald B. Johnson.
        /// For a description of the algorithm see:
        /// Donald B. Johnson: Finding All the Elementary Circuits of a Directed Graph.
        /// SIAM Journal on Computing. Volume 4, Nr. 1 (1975), pp. 77-84.
        /// For a comparison of algorithms which enumerate cycles of graphs see:
        /// Prabhaker Mateti and Narsingh Deo: 
        /// On Algorithms for Enumerating All Circuits of a Graph.
        /// SIAM Journal on Computing. Volume 5, Issue 1, pp. 90-99 (1976) 
        /// </summary>
        private class ElementaryCycleSearcher
        {
            /// <summary>
            /// Dictionary which keeps interface of ids.
            /// </summary>
            private readonly Dictionary<string, Interface> m_CoreIdToInterfaceDict;

            /// <summary>
            /// Adjacency list representation of the given graph.
            /// </summary>
            private readonly SortedDictionary<string, List<string>> m_AdjacencyList;

            /// <summary>
            /// Blocked nodes of the Johnson's algorithm.
            /// </summary>
            private readonly HashSet<string> m_BlockedNodes;

            /// <summary>
            /// B-Lists of the Johnson's algorithm. They are used to remember information 
            /// obtained from searches of portions of the graph which do not yield an 
            /// elementary circuit.
            /// </summary>
            private readonly SortedDictionary<string, List<string>> m_B;

            public ElementaryCycleSearcher(List<Interface> interfaceSubmodules)
            {
                m_CoreIdToInterfaceDict = new Dictionary<string, Interface>();
                m_AdjacencyList = GetAdjacencyListOfGraph(interfaceSubmodules);
                m_B = new SortedDictionary<string, List<string>>();
                m_BlockedNodes = new HashSet<string>();
            }

            /// <summary>
            /// Finds all elementary cycles of the given IConfigObjectCollection.
            /// </summary>
            /// <returns>List of IConfigObjectCollections, each of which contains a found ring.
            /// </returns>
            /// <remarks>The original Johnson algorithm runs on directed graphs. 
            /// To use the algorithm in undirected graphs, for each edge (port interconnection)
            /// in original undirected graph, 2 edges with opposite directions are introduced.
            /// This method creates new elementary cycles which don't exist in the original 
            /// undirected graph. The number of cycles in the directed version of the graph can
            /// be found with this formula: C(D) = 2 * C(U) + e, where C(U) = number of cycles in
            /// the undirected version of the graph and e is the number of edges in the undirected
            /// graph (See the paper of Prabhaker Mateti and Narsingh Deo for more details).
            /// After the algorithm ends, these newly introduced cycles are removed from the
            /// solution set.</remarks>
            public List<List<Interface>> GetElementaryCycles()
            {
                // List of lists, each of which contains rings of coreIds.
                ListOfList elemCycles = new ListOfList(new List<List<string>>());
                Stack<string> nodeStack = new Stack<string>();

                StronglyConnectedComponentsFinder sccFinder =
                    new StronglyConnectedComponentsFinder(m_AdjacencyList);
                foreach (string nodeCoreId in m_AdjacencyList.Keys)
                {
                    SortedDictionary<string, List<string>> sccOfNode = sccFinder.
                        GetStronglyConnectedComponent(nodeCoreId);
                    if (sccOfNode != null && sccOfNode.Count > 1)
                    {
                        foreach (string coreId in sccOfNode.Keys)
                        {
                            m_BlockedNodes.Remove(coreId);
                            m_B.Remove(coreId);
                        }
                        FindElementaryCycles(nodeCoreId, nodeCoreId, new SortedGraphDictionary(sccOfNode), nodeStack, elemCycles);
                    }
                }
                // Remove cycles which are introduced with the undirected to directed conversion
                // and return the rings with IConfigObjectCollection representation
                return RemoveDirectedCycles(elemCycles);
            }

            /// <summary>
            /// CIRCUIT function of Johnson's algorithm. Recursively searches for cycles.
            /// </summary>
            /// <param name="currNodeCoreId">Internal parameter of the recursive algorithm.
            /// It must initially be equal to startNodeCoreId.</param>
            /// <param name="startNodeCoreId">Cycles with node coreIds >= startNodeCoreId are 
            /// being searced.</param>
            /// <param name="coreIdSccList">Strongly connected subgraph of the original graph
            /// which contains the given coreId as the "smallest" coreId. </param>
            /// <param name="nodeStack">Internal parameter of the recursive algorithm.
            /// It must initially be empty.</param>
            /// <param name="listOfList">Output parameter. All cycles found are returned 
            /// with this parameter.</param>
            /// <returns></returns>
            private bool FindElementaryCycles(string currNodeCoreId, string startNodeCoreId,
                                              SortedGraphDictionary coreIdSccList, Stack<string> nodeStack,
                                              ListOfList listOfList)
            {
                bool cycleFound = false;
                nodeStack.Push(currNodeCoreId);
                m_BlockedNodes.Add(currNodeCoreId);

                for (int i = 0; i < coreIdSccList.GraphDictionary[currNodeCoreId].Count; i++)
                {
                    string nextCoreId = coreIdSccList.GraphDictionary[currNodeCoreId][i];
                    if (nextCoreId == startNodeCoreId)
                    {
                        // Cycle found
                        List<string> cycle = new List<string>();
                        cycle.AddRange(nodeStack.ToArray());
                        listOfList.ListInList.Add(cycle);
                        cycleFound = true;
                    }
                    else if (!m_BlockedNodes.Contains(nextCoreId))
                    {
                        if (FindElementaryCycles(nextCoreId, startNodeCoreId, coreIdSccList, nodeStack, listOfList))
                        {
                            cycleFound = true;
                        }
                    }
                }

                if (cycleFound)
                {
                    UnblockNode(currNodeCoreId);
                }
                else
                {
                    for (int i = 0; i < coreIdSccList.GraphDictionary[currNodeCoreId].Count; i++)
                    {
                        string nextCoreId = coreIdSccList.GraphDictionary[currNodeCoreId][i];
                        if (m_B.ContainsKey(nextCoreId)
                            && !m_B[nextCoreId].Contains(currNodeCoreId))
                        {
                            continue;
                        }

                        if (!m_B.ContainsKey(nextCoreId))
                        {
                            m_B.Add(nextCoreId, new List<string> { currNodeCoreId });
                        }
                        else
                        {
                            m_B[nextCoreId].Add(currNodeCoreId);
                        }
                    }
                }
                nodeStack.Pop();
                return cycleFound;
            }

            /// <summary>
            /// Unblocks recursively all blocked nodes, starting with a given node.
            /// </summary>
            /// <param name="nodeCoreId"></param>
            private void UnblockNode(string nodeCoreId)
            {
                m_BlockedNodes.Remove(nodeCoreId);
                if (!m_B.ContainsKey(nodeCoreId))
                {
                    return;
                }

                List<string> bOfNode = m_B[nodeCoreId];
                while (bOfNode.Count > 0)
                {
                    string nextCoreId = bOfNode[0];
                    bOfNode.RemoveAt(0);

                    if (m_BlockedNodes.Contains(nextCoreId))
                    {
                        UnblockNode(nextCoreId);
                    }
                }
            }

            /// <summary>
            /// Returns the adjacency list of a given graph of interface submodules. It also 
            /// fills the coreIdToInterfaceDict.
            /// </summary>
            /// <param name="interfaceSubmodules"></param>
            /// <returns>Dictionary which contains the adjacency information. 
            /// Key: Core id of the interface, value: List of interface core ids 
            /// which are connected to the interface.</returns>
            private SortedDictionary<string, List<string>> GetAdjacencyListOfGraph(
                List<Interface> interfaceSubmodules)
            {
                SortedDictionary<string, List<string>> adjList =
                    new SortedDictionary<string, List<string>>();
                foreach (Interface interfaceSubmodule in interfaceSubmodules)
                {
                    string coreId = interfaceSubmodule.Id;
                    if (!m_CoreIdToInterfaceDict.ContainsKey(coreId))
                    {
                        m_CoreIdToInterfaceDict.Add(coreId, interfaceSubmodule);
                        List<Interface> connectedInterfaces = NavigationUtilities.
                            GetConnectedInterfaces(interfaceSubmodule);
                        if (connectedInterfaces.Count != 0)
                        {
                            List<string> connectedInterfaceCoreIds = new List<string>();
                            foreach (Interface connectedInterface in connectedInterfaces)
                            {
                                if (interfaceSubmodules.Contains(connectedInterface))
                                {
                                    // Only add to the adj. list if the interface is in the list.
                                    connectedInterfaceCoreIds.Add(
                                        connectedInterface.Id);
                                }
                            }
                            adjList.Add(coreId, connectedInterfaceCoreIds);
                        }
                    }
                }
                return adjList;
            }

            /// <summary>
            /// Removes cycles which are introduced with the conversion of IConfigObjectCollection 
            /// to a directed graph. It also converts the found cycles to the 
            /// IConfigObjectCollection representation.
            /// </summary>
            /// <param name="elemCycles">All cycles of the directed graph representation.</param>
            /// <returns></returns>
            private List<List<Interface>> RemoveDirectedCycles(
                ListOfList elemCycleList)
            {
                List<List<string>> elemCycles = (List<List<string>>)elemCycleList.ListInList;
                // Sort nodes of all cycles wrt coreIds (will be used in comparison)
                List<List<string>> sortedCycles = new List<List<string>>();
                foreach (List<string> elemCycle in elemCycles)
                {
                    List<string> sortedCycle = new List<string>(elemCycle);
                    sortedCycle.Sort();
                    sortedCycles.Add(sortedCycle);
                }

                // Remove same cycles with different directions 
                // (sorted versions will be same)
                for (int i = 0; i < elemCycles.Count; i++)
                {
                    List<string> sortedCycle = sortedCycles[i];
                    // Search if this cycle exists in previous members.
                    for (int j = 0; j < i; j++)
                    {
                        List<string> prevSortedCycle = sortedCycles[j];
                        if (sortedCycle.Count != prevSortedCycle.Count)
                        {
                            continue;
                        }
                        bool sameCycleFound = true;
                        for (int k = 0; k < sortedCycle.Count; k++)
                        {
                            if (sortedCycle[k] != prevSortedCycle[k])
                            {
                                sameCycleFound = false;
                                break;
                            }
                        }
                        if (sameCycleFound)
                        {
                            elemCycles.RemoveAt(i);
                            sortedCycles.RemoveAt(i--);
                            break;
                        }
                    }
                }

                // Convert the remaining cycles to the IConfigObjectCollection representation.
                List<List<Interface>> cycles = new List<List<Interface>>();
                foreach (List<string> elemCycle in elemCycles)
                {
                    Interface firstInterface = m_CoreIdToInterfaceDict[elemCycle[0]];
                    List<Interface> cycle = new List<Interface>();
                    cycle.Add(firstInterface);
                    for (int i = 1; i < elemCycle.Count; i++)
                    {
                        cycle.Add(m_CoreIdToInterfaceDict[elemCycle[i]]);
                    }
                    cycles.Add(cycle);
                }

                // Remove the cycles which are created by changing the undirected edge to 
                // 2 directed edges with different directions.
                // This is done by checking the remaining cycles
                for (int i = 0; i < cycles.Count; i++)
                {
                    List<Interface> cycle = cycles[i];
                    if (cycle.Count == 2)
                    {
                        // Check if the interfaces are connected with each other with at least
                        // 2 port interconnections.
                        int numInterConnections = 0;
                        IList<DataModel.PCLObjects.Port> ports = cycle[0].GetPorts();

                        foreach (DataModel.PCLObjects.Port port in ports)
                        {
                            List<DataModel.PCLObjects.Port> connectedPorts = (List<DataModel.PCLObjects.Port>)port.GetPartnerPorts();
                            if (connectedPorts == null)
                            {
                                continue;
                            }
                            foreach (DataModel.PCLObjects.Port connectedPort in connectedPorts)
                            {
                                Interface connectedInterfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(connectedPort);
                                if (connectedInterfaceSubmodule.Id == cycle[1].Id)
                                {
                                    numInterConnections++;
                                    if (numInterConnections == 2)
                                    {
                                        break;
                                    }
                                }
                            }
                            if (numInterConnections == 2)
                            {
                                break;
                            }
                        }

                        // The current cycle is not really a cycle, remove it from the list.
                        if (numInterConnections < 2)
                        {
                            cycles.RemoveAt(i--);
                        }
                    }
                    else
                    {
                        // Check if the interfaces are connected with each other

                        bool nonCycleFound = false;
                        for (int j = 0; j < cycle.Count - 1; j++)
                        {
                            nonCycleFound = !AreConnectedInterfaces(cycle[j], cycle[j + 1]);
                            if (nonCycleFound)
                            {
                                break;
                            }
                        }
                        // Check if the last ie connected with the first
                        if (!nonCycleFound)
                        {
                            nonCycleFound =
                                !AreConnectedInterfaces(cycle[cycle.Count - 1], cycle[0]);
                        }

                        if (nonCycleFound)
                        {
                            cycles.RemoveAt(i--);
                        }
                    }
                }

                // We may have deleted some actual cycles. Consider 1=2-3-1 
                // (2 port interconnections between interface 1 and 2) or the case
                // where there are more than 2 port interconnection between 2 interfaces.
                // The implementation above deletes cycles which actually exist for these cases.
                // The solution is checking the remaining cycles, iterating through each edge and
                // checking the number of port interconnections.
                Dictionary<List<Interface>, int> cycleMultiplicators =
                    new Dictionary<List<Interface>, int>();
                foreach (List<Interface>cycle in cycles)
                {
                    cycleMultiplicators.Add(cycle, 1);
                    int numEdges;
                    for (int i = 0; i < cycle.Count - 1; i++)
                    {
                        Interface interface1 = cycle[i];
                        Interface interface2 = cycle[i + 1];
                        numEdges = GetNumPortInterconnections(interface1, interface2);
                        if (cycle.Count > 2 && numEdges > 1)
                        {
                            // Cycles with more than 2 edges require only 1 edge between each 
                            // neighbor edge. If more than 1 edge are found, the number of actual 
                            // cycles are multiplied by the numEdges (select 1 from numEdges)
                            cycleMultiplicators[cycle] *= numEdges;
                        }
                        else if (numEdges > 2)
                        {
                            // Cycles with 2 edges require at least 2 edges between each other. 
                            // If more than 2 edges are found, the number of actual cycles are
                            // multiplied by the combination(numedges, 2). (select 2 from numEdges)
                            cycleMultiplicators[cycle] = numEdges * (numEdges - 1) / 2;
                        }
                    }
                    // do the same thing between the last and the first interface
                    if (cycle.Count > 2)
                    {
                        Interface lastInterface = cycle[cycle.Count - 1];
                        Interface firstInterface = cycle[0];
                        numEdges = GetNumPortInterconnections(lastInterface, firstInterface);
                        if (numEdges > 1)
                        {
                            cycleMultiplicators[cycle] *= numEdges;
                        }
                    }
                }
                // Multiply each cycle with its cycleMultiplicator
                foreach (KeyValuePair<List<Interface>, int> cycleMultiplicator in
                    cycleMultiplicators)
                {
                    for (int i = 0; i < cycleMultiplicator.Value - 1; i++)
                    {
                        cycles.Add(cycleMultiplicator.Key);
                    }
                }
                return cycles;
            }

            /// <summary>
            /// Returns the number of port interconnections (edges) between given interface 
            /// submodules.
            /// </summary>
            /// <param name="interface1"></param>
            /// <param name="interface2"></param>
            /// <returns></returns>
            private int GetNumPortInterconnections(Interface interface1, Interface interface2)
            {
                List<Interface> connectedInterfaces = NavigationUtilities.
                    GetConnectedInterfaces(interface1);
                int interface2Count = 0;
                for (int i = 0; i < connectedInterfaces.Count; i++)
                {
                    if (connectedInterfaces[i] == interface2)
                    {
                        interface2Count++;
                    }
                }
                return interface2Count;
            }
        }

        /// <summary>
        /// Searches all strongly connected components of a given directed graph.
        /// This is accomplished using the algorithm of Robert Tarjan.
        /// For a description of the algorithm see:
        /// Robert Tarjan: Depth-first search and linear graph algorithms.
        /// SIAM Journal on Computing. Volume 1, Nr. 2 (1972), pp. 146-160.
        /// </summary>
        private class StronglyConnectedComponentsFinder
        {
            private readonly Dictionary<string, int> m_LowLinks;
            private readonly Dictionary<string, int> m_Indices;
            private readonly Stack<string> m_NodeStack;
            private int m_CurrIndex;

            /// <summary>
            /// Adjacency-list of original graph
            /// </summary>
            private readonly SortedDictionary<string, List<string>> m_AdjListOriginal;

            /// <summary>
            /// 
            /// </summary>
            /// <param name="adjacencyList">Adjacency List structure of the original graph.</param>
            public StronglyConnectedComponentsFinder(
                SortedDictionary<string, List<string>> adjacencyList)
            {
                m_AdjListOriginal = adjacencyList;
                m_LowLinks = new Dictionary<string, int>();
                m_Indices = new Dictionary<string, int>();
                m_NodeStack = new Stack<string>();
            }

            /// <summary>
            /// Returns the strongly connected subgraph of the original graph
            /// which contains the given coreId as the "smallest" coreId. 
            /// Trivial strongly connected components with just one node are not returned.
            /// CoreIds are compared by their string value.
            /// </summary>
            /// <param name="coreId"></param>
            /// <returns>Adjacency list of the strongly connected component which contains the 
            /// given coreId.</returns>
            public SortedDictionary<string, List<string>> GetStronglyConnectedComponent(
                string coreId)
            {
                m_LowLinks.Clear();
                m_Indices.Clear();
                m_NodeStack.Clear();
                m_CurrIndex = 0;

                SortedGraphDictionary adjListSubGraph = CreateSubGraph(coreId);
                ListOfList foundSCCs = new ListOfList(new List<List<string>>());
                SortedDictionary<string, List<string>> adjListOfSCC = null;

                foreach (string nextCoreId in adjListSubGraph.GraphDictionary.Keys)
                {
                    // DFS for each non-visited node
                    if (!m_Indices.ContainsKey(nextCoreId))
                    {
                        TarjanAlgorithm(nextCoreId, adjListSubGraph, foundSCCs);
                    }
                }

                // Find the strongly connected component which contains the given node
                foreach (List<string> scc in foundSCCs.ListInList)
                {
                    if (scc.Contains(coreId))
                    {
                        adjListOfSCC = CreateGraphFromSCC(scc, adjListSubGraph);
                        break;
                    }
                }
                return adjListOfSCC;
            }

            /// <summary>
            /// Creates a graph from a given strongly connected component and the graph
            /// which contains this scc.
            /// It basically takes all nodes of the scc and their interconnections from the 
            /// reference graph.
            /// </summary>
            /// <param name="scc"></param>
            /// <param name="referenceGraph">Reference graph with an adjacency structure.</param>
            /// <returns>An adjacency list structure of the scc</returns>
            private SortedDictionary<string, List<string>> CreateGraphFromSCC(List<string> scc,
                                                                              SortedGraphDictionary referenceGraph)
            {
                SortedDictionary<string, List<string>> adjList =
                    new SortedDictionary<string, List<string>>();

                foreach (string coreId in referenceGraph.GraphDictionary.Keys)
                {
                    // The node doesn't exist in scc. Pass to the next node
                    if (!scc.Contains(coreId))
                    {
                        continue;
                    }

                    List<string> connectedNodes = new List<string>();
                    foreach (string connectedNode in referenceGraph.GraphDictionary[coreId])
                    {
                        if (scc.Contains(connectedNode))
                        {
                            // Connected node exists in scc. Add also to the subgraph.
                            connectedNodes.Add(connectedNode);
                        }
                    }
                    adjList.Add(coreId, connectedNodes);
                }

                return adjList;
            }

            private void TarjanAlgorithm(string coreId,
                                         SortedGraphDictionary adjList, ListOfList foundSCCs)
            {
                // Set the initial index and lowlink
                if (m_Indices.ContainsKey(coreId))
                {
                    m_Indices[coreId] = m_CurrIndex;
                }
                else
                {
                    m_Indices.Add(coreId, m_CurrIndex);
                }

                if (m_LowLinks.ContainsKey(coreId))
                {
                    m_LowLinks[coreId] = m_CurrIndex;
                }
                else
                {
                    m_LowLinks.Add(coreId, m_CurrIndex);
                }

                m_CurrIndex++;
                // push the node to the stack
                m_NodeStack.Push(coreId);

                // Iterate through all connected nodes
                List<string> coreIdsOfConnectedNodes = adjList.GraphDictionary[coreId];
                foreach (string connectedNodeCoreId in coreIdsOfConnectedNodes)
                {
                    // If not visited, recurse
                    if (!m_Indices.ContainsKey(connectedNodeCoreId))
                    {
                        TarjanAlgorithm(connectedNodeCoreId, adjList, foundSCCs);
                        m_LowLinks[coreId] = Math.Min(
                            m_LowLinks[coreId], m_LowLinks[connectedNodeCoreId]);
                    }
                    else if (m_NodeStack.Contains(connectedNodeCoreId))
                    {
                        m_LowLinks[coreId] = Math.Min(
                            m_LowLinks[coreId], m_Indices[connectedNodeCoreId]);
                    }
                }
                if (m_LowLinks[coreId] == m_Indices[coreId])
                {
                    // Found a SCC.
                    List<string> scc = new List<string>();
                    string nextCoreId;
                    do
                    {
                        nextCoreId = m_NodeStack.Pop();
                        scc.Add(nextCoreId);
                    } while (nextCoreId != coreId);

                    foundSCCs.ListInList.Add(scc);
                }
            }

            /// <summary>
            /// Returns the adjacency-list of a subgraph which contains coreIds greater 
            /// than or equal to the given coreId.
            /// </summary>
            /// <param name="coreId"></param>
            /// <returns></returns>
            private SortedGraphDictionary CreateSubGraph(string coreId)
            {
                SortedGraphDictionary subGraph =
                    new SortedGraphDictionary(new SortedDictionary<string, List<string>>());
                foreach (KeyValuePair<string, List<string>> pair in m_AdjListOriginal)
                {
                    string coreIdOfNode = pair.Key;
                    List<string> coreIdsOfConnectedNodes = pair.Value;

                    if (string.Compare(coreId, coreIdOfNode, StringComparison.Ordinal) > 0)
                    {
                        continue;
                    }

                    List<string> subGraphConnectedNodes = new List<string>();
                    foreach (string nextCoreId in coreIdsOfConnectedNodes)
                    {
                        if (string.Compare(coreId, nextCoreId, StringComparison.Ordinal) <= 0)
                        {
                            subGraphConnectedNodes.Add(nextCoreId);
                        }
                    }
                    if (subGraphConnectedNodes.Count != 0)
                    {
                        subGraph.GraphDictionary.Add(coreIdOfNode, subGraphConnectedNodes);
                    }
                }
                return subGraph;
            }
        }

        #endregion

        /// <summary>
        /// Finds the rings of a given graph of interface submodules.
        /// Port connections are considered as edges of the graph.
        /// </summary>
        /// <param name="interfaceSubmodules">List of interface submodules</param>
        /// <returns>List of interface submodule lists, each of which consists an 
        /// "elementary circuit", i.e. a ring in which each interface appears only once.
        /// Returns null if given graph does not contain a circuit.
        /// </returns>
        public static List<List<Interface>> GetRings(
            List<Interface> interfaceSubmodules)
        {
            if (interfaceSubmodules == null) throw new ArgumentNullException(nameof(interfaceSubmodules));
            // First, check if a ring exists.
            bool result = IncludesRingCore(interfaceSubmodules);

            if (!result)
            {
                return null;
            }

            ElementaryCycleSearcher cycleSearcher = new ElementaryCycleSearcher(
                interfaceSubmodules);
            return cycleSearcher.GetElementaryCycles();
        }

        /// <summary>
        /// Checks if the given interfaces are interconnected with each other.
        /// </summary>
        private static bool AreConnectedInterfaces(Interface interfaceSubmodule1,
                                                   Interface interfaceSubmodule2)
        {
            List<Interface> connectedIes = NavigationUtilities.GetConnectedInterfaces(
                interfaceSubmodule1);
            return connectedIes.Contains(interfaceSubmodule2);
        }

        /// <summary>
        /// Finds if the given graph of interface submodules includes a ring.
        /// Port connections are considered as edges of the graph.
        /// </summary>
        /// <param name="interfaceSubmodules">List of interface submodules</param>
        private static bool IncludesRingCore(List<Interface> interfaceSubmodules)
        {
            HashSet<Interface> visitedNodes = new HashSet<Interface>();
            HashSet<DataModel.PCLObjects.Port> visitedPorts = new HashSet<DataModel.PCLObjects.Port>();

            foreach (Interface nextIeSubmodule in interfaceSubmodules)
            {
                // For all unconnected subgraphs, call the internal method.
                if (!visitedNodes.Contains(nextIeSubmodule))
                {
                    if (IncludesRingInternalCore(interfaceSubmodules, nextIeSubmodule, visitedNodes, visitedPorts))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Internal method which is used in ring detection algorithm. Depth first searches for 
        /// a ring in the given graph.
        /// </summary>
        private static bool IncludesRingInternalCore(List<Interface> interfaceSubmodules,
                                                     Interface startNode,
                                                     HashSet<Interface> visitedNodes, 
                                                     HashSet<DataModel.PCLObjects.Port> visitedPorts)
        {
            Stack<Interface> nodeStack = new Stack<Interface>();
            nodeStack.Push(startNode);
            while (nodeStack.Count > 0)
            {
                Interface nextNode = nodeStack.Pop();
                if (visitedNodes.Contains(nextNode))
                {
                    // Found a ring, return true.
                    return true;
                }
                visitedNodes.Add(nextNode);
                // Get all ports
                IList<DataModel.PCLObjects.Port> ports = nextNode.GetPorts();
                // Get all non-visited ports and add the interfaces of them to the stack.
                foreach (DataModel.PCLObjects.Port port in ports)
                {
                    if (visitedPorts.Contains(port))
                    {
                        continue;
                    }
                    visitedPorts.Add(port);
                    List<DataModel.PCLObjects.Port> connectedPorts = (List<DataModel.PCLObjects.Port>)port.GetPartnerPorts();
                    if (connectedPorts == null)
                    {
                        // The port is not connected to any ports, continue with the next port.
                        continue;
                    }

                    foreach (DataModel.PCLObjects.Port connectedPort in connectedPorts)
                    {
                        visitedPorts.Add(connectedPort);
                        Interface connectedInterface = NavigationUtilities.GetInterfaceOfPort(connectedPort);
                        if (interfaceSubmodules.Contains(connectedInterface))
                        {
                            // Continue branching only if the connected interface is in the list.
                            nodeStack.Push(connectedInterface);
                        }
                    }
                }
            }
            // Connected graph has no rings, return false.
            return false;
        }
    }
}
