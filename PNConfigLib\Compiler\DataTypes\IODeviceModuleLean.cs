/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: IODeviceModuleLean.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.Compiler.DataTypes
{
    internal class IODeviceModuleLean : ProjectTreeNodeBase
    {
        public IODeviceModuleLean(PclObject module)
        {
            Name = AttributeUtilities.GetName(module);
            ClassRID = CompilerConstants.ClassRid.ModuleLean;
            PCLObject = module;
        }

        public override void Compile()
        {
            Variables.Add(CompileUtility.GetKey(PCLObject, CompilerConstants.Keys.SlotNumber));
            base.Compile();
        }

        public override void GenerateChildren()
        {
            if (PCLObject is DecentralDevice)
            {
                GenerateIODeviceChildren();
            }
            else if (PCLObject is Module)
            {
                GenerateIODModuleChildren();
            }
        }

        private void GenerateInterfaceSubmodule()
        {
            DecentralDevice decentralDevice = (DecentralDevice)PCLObject;
            ChildNodes.Add(new IODeviceInterfaceSubmodule(decentralDevice.GetInterface()));
        }

        private void GenerateIODeviceChildren()
        {
            DecentralDevice decentralDevice = (DecentralDevice)this.PCLObject;

            if (decentralDevice != null)
            {
                List<string> notAssignedSubmoduleIDs = new List<string>();
                bool isSharedAssignmentSupported =
                    decentralDevice.AttributeAccess
                    .GetAnyAttribute<bool>(InternalAttributeNames.PnIoSharedDeviceSupported,
                        new AttributeAccessCode(), false);
                IList<PclObject> headSubmodules = PNNavigationUtility
                    .GetHeadSubmodulesPN(decentralDevice, isSharedAssignmentSupported,
                    ref notAssignedSubmoduleIDs);

                if (!CompileUtility.CheckIfCreatePdevSubmodules(decentralDevice.GetInterface()))
                {
                    if (headSubmodules.Count - notAssignedSubmoduleIDs.Count > 1)
                    {
                        GenerateModuleProxy();
                    }
                    if (headSubmodules.Count - notAssignedSubmoduleIDs.Count > 0)
                    {
                        GenerateSubmodule(decentralDevice.GetVirtualSubmodules());
                    }
                }
                else
                {
                    GenerateModuleProxy();
                    GenerateSubmodule(decentralDevice.GetVirtualSubmodules());
                    GenerateInterfaceSubmodule();
                    GeneratePortSubmodules();
                }
            }
        }

        private void GenerateIODModuleChildren()
        {
            Module moduleObject = (Module)PCLObject;

            List<Submodule> submodulesToGenerate = new List<Submodule>();
            List<Submodule> submoduleList = moduleObject.GetSubmodules();
            List<PclObject> allSubmodules = (List<PclObject>)moduleObject.GetElements();

            List<Submodule> virtualSubmodules = moduleObject.GetVirtualSubmodules();
            // If there is more than one Submodule, create a ModuleProxy first
            if (allSubmodules != null
                && allSubmodules.Count(sm => (SharedIoAssignment)sm.AttributeAccess.GetAnyAttribute<uint>(
                                                  InternalAttributeNames.SharedIoAssignment,
                                                  new AttributeAccessCode(),
                                                  0) == SharedIoAssignment.None) > 1
                )
            {
                GenerateModuleProxy();
            }
            if (submoduleList != null)
            {
                foreach (Submodule sm in submoduleList)
                {
                    if ((SharedIoAssignment)sm.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.SharedIoAssignment,
                            new AttributeAccessCode(),
                            0) == SharedIoAssignment.None)
                    {
                        submodulesToGenerate.Add(sm);
                    }
                }
            }
            if (virtualSubmodules != null)
            {
                submodulesToGenerate.AddRange(virtualSubmodules);
            }
            GenerateSubmodule(submodulesToGenerate);
            GeneratePortSubmodules();
        }

        private void GenerateModuleProxy()
        {
            ChildNodes.Add(new IODeviceModuleProxy(PCLObject));
        }

        private void GeneratePortSubmodules()
        {
            IList<Port> portsToGenerate = null;
            DecentralDevice decentralDevice = PCLObject as DecentralDevice;
            if (decentralDevice != null)
            {
                portsToGenerate = decentralDevice.GetInterface().GetPorts();
            }
            else
            {
                Module module = PCLObject as Module;
                if (module != null)
                {
                    portsToGenerate = module.GetPorts();
                }
            }

            if (portsToGenerate != null)
            {
                foreach (Port port in portsToGenerate)
                {
                    ChildNodes.Add(new IODevicePortSubmodule(port));
                }
            }
        }

        private void GenerateSubmodule(List<Submodule> submodulesToGenerate)
        {
            if (submodulesToGenerate != null)
            {
                foreach (Submodule sm in submodulesToGenerate)
                {
                    if ((SharedIoAssignment)sm.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.SharedIoAssignment,
                            new AttributeAccessCode(),
                            0) == SharedIoAssignment.None)
                    {
                        IoTypes ioType = (IoTypes)sm.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.IoType,
                            new AttributeAccessCode(),
                            0);
                        if ((ioType & (IoTypes.Output | IoTypes.Input)) != 0)
                        {
                            ChildNodes.Add(new IOSubmoduleNode(sm, CompilerConstants.ClassRid.IOSubmodule));
                        }
                        else
                        {
                            ChildNodes.Add(new SubmoduleNode(sm, CompilerConstants.ClassRid.Submodule));
                        }
                    }
                }
            }
        }
    }
}