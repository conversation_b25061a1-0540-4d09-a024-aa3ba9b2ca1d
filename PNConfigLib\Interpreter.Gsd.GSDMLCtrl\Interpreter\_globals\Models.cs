/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Models.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all object and field names of the data object model.
    /// </summary>
    internal static class Models
    {
        //########################################################################################
        #region Data Object Model - Fields, Properties
        // Contains all object field names as constants

        #region V1.0

        /// <summary>name of the GsdID field</summary>
        public const string s_FieldGsdId = @"GsdID";
        /// <summary>name of the unchanged GsdID field</summary>
        public const string s_FieldGsdIdRaw = @"GsdIdRaw";
        /// <summary>name of the IdentNumber field</summary>
        public const string s_FieldIdentNumber = @"IdentNumber";
        /// <summary>name of the Name field</summary>
        public const string s_FieldName = @"Name";
        /// <summary>name of the NameTextID field</summary>
        public const string s_FieldNameTextId = @"NameTextID";
        /// <summary>name of the InfoText field</summary>
        public const string s_FieldInfoText = @"InfoText";
        public const string s_FieldInfoTextId = @"InfoTextID";
        /// <summary>name of the Category field</summary>
        public const string s_FieldCategory = @"Category";
        /// <summary>name of the CategoryGsdID field</summary>
        public const string s_FieldCategoryGsdId = @"CategoryGsdID";
        public const string s_FieldCategoryTextId = @"CategoryTextId";
        public const string s_FieldCategoryInfoText = @"CategoryInfoText";
        public const string s_FieldCategoryInfoTextId = @"CategoryInfoTextId";


        /// <summary>name of the SubCategory1 field</summary>
        public const string s_FieldSubCategory1 = @"SubCategory1";
        /// <summary>name of the SubCategory1GsdID field</summary>
        public const string s_FieldSubCategory1GsdId = @"SubCategory1GsdID";
        public const string s_FieldSubCategory1TextId = @"SubCategory1TextId";
        public const string s_FieldSubCategory1InfoText = @"SubCategory1InfoText";
        public const string s_FieldSubCategory1InfoTextId = @"SubCategory1InfoTextId";

        /// <summary>name of the VendorName field</summary>
        /// 
        public const string s_FieldVendorName = @"VendorName";
        /// <summary>name of the OrderNumber field</summary>
        public const string s_FieldOrderNumber = @"OrderNumber";
        /// <summary>name of the HardwareRelease field</summary>
        public const string s_FieldHardwareRelease = @"HardwareRelease";
        /// <summary>name of the SoftwareRelease field</summary>
        public const string s_FieldSoftwareRelease = @"SoftwareRelease";
        /// <summary>name of the MainFamily field</summary>
        public const string s_FieldMainFamily = @"MainFamily";
        /// <summary>name of the MainFamilyAsEnum field</summary>
        public const string s_FieldMainFamilyAsEnum = @"MainFamilyAsEnum";
        /// <summary>name of the ProductFamily field</summary>
        public const string s_FieldProductFamily = @"ProductFamily";
        /// <summary>name of the CompatibilityVersion field</summary>
        public const string s_FieldCompatibilityVersion = @"CompatibilityVersion";
        /// <summary>name of the IsCompatible field</summary>
        public const string s_FieldIsCompatible = @"IsCompatible";
        /// <summary>name of the Submodules field</summary>
        public const string s_FieldSubmodules = @"Submodules";
        /// <summary>name of the Modules field</summary>
        public const string s_FieldModules = @"Modules";
        /// <summary>name of the DeviceAccessPoints field</summary>
        public const string s_FieldDeviceAccessPoints = @"DeviceAccessPoints";
        /// <summary>name of the VendorIdentNumber field</summary>
        public const string s_FieldVendorIdentNumber = @"VendorIdentNumber";
        /// <summary>name of the DeviceIdentNumber field</summary>
        public const string s_FieldDeviceIdentNumber = @"DeviceIdentNumber";
        /// <summary>name of the Graphics field</summary>
        public const string s_FieldGraphics = @"Graphics";
        /// <summary>name of the Info field</summary>
        public const string s_FieldInfo = @"Info";
        /// <summary>name of the IOData field</summary>
        public const string s_FieldIoData = @"IOData";
        /// <summary>name of the ParameterRecordData field</summary>
        public const string s_FieldParameterRecordData = @"ParameterRecordData";
        /// <summary>name of the VirtualSubmodules field</summary>
        public const string s_FieldVirtualSubmodules = @"VirtualSubmodules";
        /// <summary>name of the ModulePlugData field</summary>
        public const string s_FieldModulePlugData = @"ModulePlugData";
        /// <summary>name of the IOConfigData field</summary>
        public const string s_FieldIoConfigData = @"IOConfigData";
        /// <summary>name of the ApplicationRelations field</summary>
        public const string s_FieldApplicationRelations = @"ApplicationRelations";
        /// <summary>name of the PlugData field</summary>
        public const string s_FieldPlugData = @"PlugData";
        /// <summary>name of the PhysicalSlots field</summary>
        public const string s_FieldPhysicalSlots = @"PhysicalSlots";
        /// <summary>name of the MinDeviceInterval field</summary>
        public const string s_FieldMinDeviceInterval = @"MinDeviceInterval";
        /// <summary>name of the ImplementationType field</summary>
        public const string s_FieldImplementationType = @"ImplementationType";
        /// <summary>name of the DNSCompatibleName field</summary>
        public const string s_FieldDnsCompatibleName = @"DNSCompatibleName";
        /// <summary>name of the ObjectUUIDLocalIndex field</summary>
        public const string s_FieldObjectUuidLocalIndex = @"ObjectUUIDLocalIndex";
        /// <summary>name of the IsExtendedAddressAssignmentSupported field</summary>
        public const string s_FieldIsExtendedAddressAssignmentSupported = @"IsExtendedAddressAssignmentSupported";
        /// <summary>name of the ChannelDiagnostics field</summary>
        public const string s_FieldChannelDiagnostics = @"ChannelDiagnostics";
        /// <summary>name of the UnitDiagnosticTypes field</summary>
        public const string s_FieldUnitDiagnosticTypes = @"UnitDiagnosticTypes";
        /// <summary>name of the ErrorType field</summary>
        public const string s_FieldErrorType = @"ErrorType";
        /// <summary>name of the Help field</summary>
        public const string s_FieldHelp = @"Help";
        public const string s_FieldHelpTextId = @"HelpTextId";
        /// <summary>name of the UserStructureIdentifier field</summary>
        public const string s_FieldUserStructureIdentifier = @"UserStructureIdentifier";
        /// <summary>name of the File field</summary>
        public const string s_FieldFile = @"File";
        /// <summary>name of the SVG field</summary>
        public const string s_FieldSvg = @"SVG";
        /// <summary>name of the Type field</summary>
        public const string s_FieldType = @"Type";
        /// <summary>name of the GraphicGsdID field</summary>
        public const string s_FieldGraphicGsdId = @"GraphicGsdID";
        /// <summary>name of the MaxInputLength field</summary>
        public const string s_FieldMaxInputLength = @"MaxInputLength";
        /// <summary>name of the MaxOutputLength field</summary>
        public const string s_FieldMaxOutputLength = @"MaxOutputLength";
        /// <summary>name of the MaxDataLength field</summary>
        public const string s_FieldMaxDataLength = @"MaxDataLength";
        /// <summary>name of the InputDataItems field</summary>
        public const string s_FieldInputDataItems = @"InputDataItems";
        /// <summary>name of the OutputDataItems field</summary>
        public const string s_FieldOutputDataItems = @"OutputDataItems";
        /// <summary>name of the InputConsistency field</summary>
        public const string s_FieldInputConsistency = @"InputConsistency";
        /// <summary>name of the OutputConsistency field</summary>
        public const string s_FieldOutputConsistency = @"OutputConsistency";
        /// <summary>name of the IOPSLength field</summary>
        public const string s_FieldIopsLength = @"IOPSLength";
        /// <summary>name of the IOCSLength field</summary>
        public const string s_FieldIocsLength = @"IOCSLength";
        /// <summary>name of the DataType field</summary>
        public const string s_FieldDataType = @"DataType";
        /// <summary>name of the DataType field (as string)</summary>
        public const string s_FieldDataTypeAsString = @"DataTypeAsString";
        /// <summary>name of the UseAsBits field</summary>
        public const string s_FieldUseAsBits = @"UseAsBits";
        /// <summary>name of the DataLength field</summary>
        public const string s_FieldDataLength = @"DataLength";
        /// <summary>name of the Refs field</summary>
        public const string s_FieldRefs = @"Refs";
        /// <summary>name of the TransferSequence field</summary>
        public const string s_FieldTransferSequence = @"TransferSequence";
        /// <summary>name of the Consts field</summary>
        public const string s_FieldConsts = @"Consts";
        /// <summary>name of the Index field</summary>
        public const string s_FieldIndex = @"Index";
        /// <summary>name of the Length field</summary>
        public const string s_FieldLength = @"Length";
        /// <summary>name of the Values field</summary>
        public const string s_FieldValues = @"Values";
        /// <summary>name of the ValueGsdID field</summary>
        public const string s_FieldValueGsdId = @"ValueGsdID";
        /// <summary>name of the DefaultValue field</summary>
        public const string s_FieldDefaultValue = @"DefaultValue";
        /// <summary>name of the ValueType field</summary>
        public const string s_FieldValueType = @"ValueType";
        /// <summary>name of the ByteOffset field</summary>
        public const string s_FieldByteOffset = @"ByteOffset";
        /// <summary>name of the BitOffset field</summary>
        public const string s_FieldBitOffset = @"BitOffset";
        /// <summary>name of the BitLength field</summary>
        public const string s_FieldBitLength = @"BitLength";
        /// <summary>name of the IsChangeable field</summary>
        public const string s_FieldIsChangeable = @"IsChangeable";
        /// <summary>name of the IsVisible field</summary>
        public const string s_FieldIsVisible = @"IsVisible";
        /// <summary>name of the FixedInSlots field</summary>
        public const string s_FieldFixedInSlots = @"FixedInSlots";
        /// <summary>name of the FixedInSlotsString field</summary>
        public const string s_FieldFixedInSlotsString = @"FixedInSlotsString";
        /// <summary>name of the UsedInSlots field</summary>
        public const string s_FieldUsedInSlots = @"UsedInSlots";
        /// <summary>name of the UsedInSlotsString field</summary>
        public const string s_FieldUsedInSlotsString = @"UsedInSlotsString";
        /// <summary>name of the AllowedInSlots field</summary>
        public const string s_FieldAllowedInSlots = @"AllowedInSlots";
        /// <summary>name of the AllowedInSlotsString field</summary>
        public const string s_FieldAllowedInSlotsString = @"AllowedInSlotsString";
        /// <summary>name of the TimingProperties field</summary>
        public const string s_FieldTimingProperties = @"TimingProperties";
        /// <summary>name of the ARBlockVersion field</summary>
        public const string s_FieldArBlockVersion = @"ARBlockVersion";
        /// <summary>name of the IOCRBlockVersion field</summary>
        public const string s_FieldIocrBlockVersion = @"IOCRBlockVersion";
        /// <summary>name of the AlarmCRBlockVersion field</summary>
        public const string s_FieldAlarmCrBlockVersion = @"AlarmCRBlockVersion";
        /// <summary>name of the SubmoduleDataBlockVersion field</summary>
        public const string s_FieldSubmoduleDataBlockVersion = @"SubmoduleDataBlockVersion";
        /// <summary>name of the SendClock field</summary>
        public const string s_FieldSendClock = @"SendClock";
        /// <summary>name of the ReductionRatio field</summary>
        public const string s_FieldReductionRatio = @"ReductionRatio";
        /// <summary>name of the MinValue field</summary>
        public const string s_FieldMinValue = @"MinValue";
        /// <summary>name of the MaxValue field</summary>
        public const string s_FieldMaxValue = @"MaxValue";
        /// <summary>name of the InputLength field</summary>
        public const string s_FieldInputLength = @"InputLength";
        /// <summary>name of the OutputLength field</summary>
        public const string s_FieldOutputLength = @"OutputLength";
        /// <summary>name of the Value field</summary>
        public const string s_FieldValue = @"Value";
        /// <summary>name of the Text field</summary>
        public const string s_FieldText = @"Text";
        /// <summary>name of the TextID field</summary>
        public const string s_FieldTextId = @"TextID";

        #endregion

        #region V2.0

        /// <summary>name of the PortProperties field</summary>
        public const string s_FieldPortProperties = @"PortProperties";
        /// <summary>name of the SystemDefinedSubmodules field</summary>
        public const string s_FieldSystemDefinedSubmodules = @"SystemDefinedSubmodules";
        /// <summary>name of the RequiredSchemaVersion field</summary>
        public const string s_FieldRequiredSchemaVersion = @"RequiredSchemaVersion";
        /// <summary>name of the RTClass3TimingProperties field</summary>
        public const string s_FieldRtClass3TimingProperties = @"RTClass3TimingProperties";
        /// <summary>name of the NumberOfAdditionalInputCR field</summary>
        public const string s_FieldNumberOfAdditionalInputCr = @"NumberOfAdditionalInputCR";
        /// <summary>name of the NumberOfAdditionalOutputCR field</summary>
        public const string s_FieldNumberOfAdditionalOutputCr = @"NumberOfAdditionalOutputCR";
        /// <summary>name of the NumberOfAdditionalMulticastProviderCR field</summary>
        public const string s_FieldNumberOfAdditionalMulticastProviderCr = @"NumberOfAdditionalMulticastProviderCR";
        /// <summary>name of the NumberOfMulticastConsumerCR field</summary>
        public const string s_FieldNumberOfMulticastConsumerCr = @"NumberOfMulticastConsumerCR";
        /// <summary>name of the BitDataItems field</summary>
        public const string s_FieldBitDataItems = @"BitDataItems";
        /// <summary>name of the Subslots field</summary>
        public const string s_FieldSubslots = @"Subslots";
        /// <summary>name of the IsIsochroneModeSupported field</summary>
        public const string s_FieldIsIsochroneModeSupported = @"IsIsochroneModeSupported";
        /// <summary>name of the IsochroneMode field</summary>
        public const string s_FieldIsochroneMode = @"IsochroneMode";
        /// <summary>name of the FParameterRecordData field</summary>
        public const string s_FieldFParameterRecordData = @"FParameterRecordData";
        /// <summary>name of the T_DC_Base field</summary>
        public const string s_FieldTDcBase = @"T_DC_Base";
        /// <summary>name of the T_DC_Min field</summary>
        public const string s_FieldTDcMin = @"T_DC_Min";
        /// <summary>name of the T_DC_Max field</summary>
        public const string s_FieldTDcMax = @"T_DC_Max";
        /// <summary>name of the T_IO_Base field</summary>
        public const string s_FieldTIOBase = @"T_IO_Base";
        /// <summary>name of the T_IO_InputMin field</summary>
        public const string s_FieldTIOInputMin = @"T_IO_InputMin";
        /// <summary>name of the T_IO_OutputMin field</summary>
        public const string s_FieldTIOOutputMin = @"T_IO_OutputMin";
        /// <summary>name of the SubslotNumber field</summary>
        public const string s_FieldSubslotNumber = @"SubslotNumber";
        /// <summary>name of the SupportedRTClass field</summary>
        public const string s_FieldSupportedRtClass = @"SupportedRTClass";
        /// <summary>name of the IsIsochroneModeRequired field</summary>
        public const string s_FieldIsIsochroneModeRequired = @"IsIsochroneModeRequired";
        /// <summary>name of the T_PLL_Max field</summary>
        public const string s_FieldTPllMax = @"T_PLL_Max";
        /// <summary>name of the MaxPortTxDelay field</summary>
        public const string s_FieldMaxPortTxDelay = @"MaxPortTxDelay";
        /// <summary>name of the MaxPortRxDelay field</summary>
        public const string s_FieldMaxPortRxDelay = @"MaxPortRxDelay";
        /// <summary>name of the MaxBridgeDelay field</summary>
        public const string s_FieldMaxBridgeDelay = @"MaxBridgeDelay";
        /// <summary>name of the IsSynchronisationModeSupported field</summary>
        public const string s_FieldIsSynchronisationModeSupported = @"IsSynchronisationModeSupported";
        /// <summary>name of the SupportedSynchronisationRole field</summary>
        public const string s_FieldSupportedSynchronisationRole = @"SupportedSynchronisationRole";
        /// <summary>name of the MaxLocalJitter field</summary>
        public const string s_FieldMaxLocalJitter = @"MaxLocalJitter";
        /// <summary>name of the MAUType field</summary>
        public const string s_FieldMauType = @"MAUType";
        /// <summary>name of the Version field</summary>
        public const string s_FieldVersion = @"Version";
        /// <summary>name of the FParamDescCRC field</summary>
        public const string s_FieldFParamDescCrc = @"FParamDescCRC";
        /// <summary>name of the API field</summary>
        public const string s_FieldApi = @"API";
        /// <summary>name of the MaxNumberIRFrameData field</summary>
        public const string s_FieldMaxNumberIrFrameData = @"MaxNumberIRFrameData";
        /// <summary>name of the AllowedValues field</summary>
		public const string s_FieldAllowedValues = @"AllowedValues";
        /// <summary>name of the IsPROFIsafeSupported field</summary>
        public const string s_FieldIsProfIsafeSupported = @"IsPROFIsafeSupported";
        /// <summary>name of the FIOStructureDescCRC field</summary>
        public const string s_FieldFioStructureDescCrc = @"FIOStructureDescCRC";
        /// <summary>name of the IsMultipleWriteSupported field</summary>
        public const string s_FieldIsMultipleWriteSupported = @"IsMultipleWriteSupported";
        /// <summary>name of the IsIOXSRequired field</summary>
        public const string s_FieldIsIoxsRequired = @"IsIOXSRequired";
        /// <summary>name of the DeviceSpecificToolName field</summary>
        public const string s_FieldDeviceSpecificToolName = @"DeviceSpecificToolName";
        /// <summary>name of the DeviceSpecificToolDescription field</summary>
        public const string s_FieldDeviceSpecificToolDescription = @"DeviceSpecificToolDescription";
        /// <summary>name of the ExtendedChannelDiagnostics field</summary>
        public const string s_FieldExtendedChannelDiagnostics = @"ExtendedChannelDiagnostics";
        /// <summary>name of the ProfileExtendedChannelDiagnostics field</summary>
        public const string s_FieldProfileExtendedChannelDiagnostics = @"ProfileExtendedChannelDiagnostics";

        #endregion

        #region V2.1

        /// <summary>name of the PowerBudgetControlSupported field</summary>
        public const string s_FieldIsPowerBudgetControlSupported = @"IsPowerBudgetControlSupported";
        /// <summary>name of the FiberOpticTypes field</summary>
        public const string s_FieldFiberOpticTypes = @"FiberOpticTypes";
        /// <summary>name of the NetworkComponentDiagnosisSupported field</summary>
        public const string s_FieldIsNetworkComponentDiagnosisSupported = @"IsNetworkComponentDiagnosisSupported";
        /// <summary>name of the MAUTypes field</summary>
        public const string s_FieldMauTypes = @"MAUTypes";
        /// <summary>name of the LinkStateDiagnosisCapability field</summary>
        public const string s_FieldLinkStateDiagnosisCapability = @"LinkStateDiagnosisCapability";
        /// <summary>name of the SupportedRTClasses field</summary>
        public const string s_FieldSupportedRtClasses = @"SupportedRT_Classes";
        /// <summary>name of the PortDeactivationSupported field</summary>
        public const string s_FieldIsPortDeactivationSupported = @"IsPortDeactivationSupported";
        /// <summary>name of the AddressAssignment field</summary>
        public const string s_FieldAddressAssignment = @"AddressAssignment";
        /// <summary>name of the W field</summary>
        public const string s_FieldWriteableImRecords = @"Writeable_IM_Records";

        public const string s_FieldPhysicalSubslots = @"PhysicalSubslots";

        public const string s_FieldSlots = @"Slots";
        public const string s_FieldSlotNumber = @"SlotNumber";

        public const string s_FieldRemoteApplicationTimeout = @"RemoteApplicationTimeout";

        public const string s_FieldSubmodulePlugData = @"SubmodulePlugData";
        public const string s_FieldSupportedProtocols = @"SupportedProtocols";
        public const string s_FieldSupportedMibs = @"SupportedMibs";

        public const string s_FieldMediaRedundancy = @"MediaRedundancy";
        public const string s_FieldMrSupportedRoles = @"MRSupportedRoles";
        public const string s_FieldIsRTMediaRedundancySupported = @"RT_MediaRedundancySupported";

        public const string s_FieldIsDefaultRingport = @"IsDefaultRingport";
        public const string s_FieldSupportsRingportConfig = @"SupportsRingportConfig";

        /// <summary>name of the ProfileChannelDiagnostics field</summary>
        public const string s_FieldProfileChannelDiagnostics = @"ProfileChannelDiagnostics";

        /// <summary>name of the ExtendedAddValueDataItems field</summary>
        public const string s_FieldExtChannelAddValueDataItems = @"ExtChannelAddValueDataItems";

        /// <summary>name of the ExtendedAddValueDataItems fields for Profile Diags</summary>
        public const string s_FieldProfileExtChannelAddValueDataItems = @"ProfileExtChannelAddValueDataItems";

        /// <summary>name of the Id field</summary>
        public const string s_FieldId = @"Id";

        /// <summary>name of the SubmodulePlugDataModel field</summary>
        public const string s_FieldSubmodulePlugDataModel = @"SubmodulePlugDataModel";

        #endregion

        #region V2.2

        /// <summary>name of the SupportedSyncProtocols field</summary>
        public const string s_FieldSupportedSyncProtocols = @"SupportedSyncProtocols";

        /// <summary>name of the DCPBoundarySupported field</summary>
        public const string s_FieldIsDcpBoundarySupported = @"DCPBoundarySupported";

        /// <summary>name of the PTPBoundarySupported field</summary>
        public const string s_FieldIsPtpBoundarySupported = @"PTPBoundarySupported";

        /// <summary>name of the DCP_HelloSupported field</summary>
        public const string s_FieldIsDcpHelloSupported = @"DCP_HelloSupported";

        /// <summary>name of the F_IO_StructureDescVersion field</summary>
        public const string s_FieldFioStructureDescVersion = @"FIOStructureDescVersion";

        /// <summary>name of the ParamterizationSpeedupSupported field</summary>
        public const string s_FieldIsParameterizationSpeedupSupported = @"ParameterizationSpeedupSupported";

        /// <summary>name of the MaxSupportedRecordSize field</summary>
        public const string s_FieldMaxSupportedRecordSize = @"MaxSupportedRecordSize";

        /// <summary>name of the NameOfStationNotTransferable field</summary>
        public const string s_FieldIsNameOfStationNotTransferable = @"NameOfStationNotTransferable";

        /// <summary>name of the PowerOnToCommReady field</summary>
        public const string s_FieldPowerOnToCommReady = @"PowerOnToCommReady";

        /// <summary>name of the IsochroneModeInRT field</summary>
        public const string s_FieldIsochroneModeInRtClasses = @"IsochroneModeInRT_Classes";

        /// <summary>name of the ReductionRatioPow2 field</summary>
        public const string s_FieldReductionRatioPow2 = @"ReductionRatioPow2";

        /// <summary>name of the ReductionRatioNonPow2 field</summary>
        public const string s_FieldReductionRatioNonPow2 = @"ReductionRatioNonPow2";

        /// <summary>name of the High field</summary>
        public const string s_FieldHigh = @"High";

        /// <summary>name of the Low field</summary>
        public const string s_FieldLow = @"Low";

        /// <summary>name of the BlockVersions field</summary>
        public const string s_FieldBlockVersions = @"BlockVersions";
        /// <summary>name of the PullModuleAlarmSupported field</summary>
        public const string s_FieldPullModuleAlarmSupported = "@PullModuleAlarmSupported";

        #endregion

        #region V2.25

        /// <summary>name of the IsParameterizationDisallowed field</summary>
        public const string s_FieldIsParameterizationDisallowed = @"ParameterizationDisallowed";

        /// <summary>name of the PreferredSendclock field</summary>
        public const string s_FieldPreferredSendClock = @"PreferredSendClock";

        /// <summary>name of the NumberOfAR field</summary>
        public const string s_FieldNumberOfAr = @"NumberOfAR";

        /// <summary>name of the SharedDeviceSupported field</summary>
        public const string s_FieldIsSharedDeviceSupported = @"SharedDeviceSupported";

        /// <summary>name of the SharedInputSupported field</summary>
        public const string s_FieldIsSharedInputSupported = @"SharedInputSupported";

        /// <summary>name of the MaxTotalDataLength field</summary>
        public const string s_FieldMaxTotalDataLength = @"MaxTotalDataLength";

        /// <summary>name of the DeviceAccessSupported field</summary>
        public const string s_FieldIsDeviceAccessSupported = @"DeviceAccessSupported";

        /// <summary>name of the MulticastBoundarySupported field</summary>
        public const string s_FieldIsMulticastBoundarySupported = @"MulticastBoundarySupported";

        /// <summary>name of the CheckMAUTypeSupported field</summary>
        public const string s_FieldIsCheckMauTypeSupported = @"CheckMAUTypeSupported";

        /// <summary>name of the DelayMeasurementSupported field</summary>
        public const string s_FieldIsDelayMeasurementSupported = @"DelayMeasurementSupported";

        /// <summary>name of the AdditionalProtocolsSupported field</summary>
        public const string s_FieldAdditionalMrpProtocolsSupported = @"AdditionalMrpProtocolsSupported";

        /// <summary>name of the WebServer field</summary>
        public const string s_FieldWebServer = @"WebServer";

        /// <summary>name of the PROFIenergySupported field</summary>
        public const string s_FieldIsProfIenergySupported = @"PROFIenergySupported";

        /// <summary>name of the SupportedSubstitutionModes field</summary>
        public const string s_FieldSupportedSubstitutionModes = @"SupportedSubstitutionModes";

        /// <summary>name of the SubmoduleType field</summary>
        public const string s_FieldSubmoduleType = @"SubmoduleType";

        /// <summary>name of the FieldForwardingMode field</summary>
        public const string s_FieldForwardingMode = @"ForwardingMode";


        #endregion

        #region V2.3

        #region RT_Class3Properties

        public const string s_FieldAlignDfpSubframes = "AlignDFP_Subframes";

        public const string s_FieldStartupMode = "StartupMode";

        public const string s_FieldFragmentationType = "FragmentationType";

        public const string s_FieldMaxDfpFeed = "MaxDFP_Feed";

        public const string s_FieldMaxDfpFrames = "MaxDFP_Frames";

        public const string s_FieldMaxBridgeDelayFfw = "MaxBridgeDelayFFW";

        public const string s_FieldIsMaxBridgeDelayFfw = "IsMaxBridgeDelayFFW";

        public const string s_FieldIsMaxDfpFeed = "IsMaxDfpFeed";

        public const string s_FieldMaxRangeIrFrameId = "MaxRangeIrFrameID";

        public const string s_FieldMaxRedPeriodLength = "MaxRedPeriodLength";

        public const string s_FieldMrpdSupported = "MrpdSupported";

        public const string s_FieldMrtSupported = "MrtSupported";

        public const string s_FieldMinFso = "MinFSO";

        public const string s_FieldMaxApplicationInputLength = @"MaxApplicationInputLength";

        public const string s_FieldIsMaxApplicationInputLength = @"IsMaxApplicationInputLength";

        public const string s_FieldMaxApplicationOutputLength = @"MaxApplicationOutputLength";

        public const string s_FieldIsMaxApplicationOutputLength = @"IsMaxApplicationOutputLength";

        public const string s_FieldMaxApplicationDataLength = @"MaxApplicationDataLength";

        public const string s_FieldIsMaxApplicationDataLength = @"IsMaxApplicationDataLength";

        #endregion

        #region Synchronisation

        public const string s_FieldPeerToPeerJitter = "PeerToPeerJitter";

        #endregion

        #region SystemRedundancy

        public const string s_FieldSystemRedundancy = "SystemRedundancy";

        public const string s_FieldDeviceType = "DeviceType";

        public const string s_FieldMaxSwitchOverTime = "MaxSwitchOverTime";

        public const string s_FieldPrimaryArOnBothNapsSupported = "PrimaryArOnBothNapsSupported";

        public const string s_FieldNumberOfSrArSets = "NumberOfSrArSets";

        public const string s_FieldRTInputOnBackupArSupported = "RT_InputOnBackupAR_Supported";

        #endregion

        public const string s_FieldShortPreamble100MbitSupported = "ShortPreamble100MBitSupported";
        public const string s_FieldIsShortPreamble100MbitSupported = "IsShortPreamble100MBitSupported";

        public const string s_FieldIsMrpSupported = "IsMrpSupported";

        public const string s_FieldIsCirSupported = "IsCirSupported";

        public const string s_FieldIsLldpnoDSupported = "IsLLDPNoDSupported";

        public const string s_FieldIsPrmBeginPrmEndSequenceSupported = "IsPrmBeginPrmEndSequenceSupported";

        public const string s_FieldResetToFactoryModes = "FieldResetToFactoryModes";

        public const string s_FieldIsAutoConfigurationSupported = "IsAutoConfigurationSupported";

        public const string s_FieldMenuItems = "MenuItems";

        public const string s_FieldMenuItemId = "MenuItemID";

        public const string s_FieldMenuRefs = "MenuRefs";

        public const string s_FieldParamRefs = "ParamRefs";

        public const string s_FieldParamId = "ParamID";

        public const string s_FieldMenuRefId = "MenuRefID";

        public const string s_FieldParamRefId = "ParamRefID";

        public const string s_FieldChangeableWithBump = "ChangeableWithBump";

        #endregion

        #region V2.31

        public const string s_FieldSubordinate = "Subordiante";

        #region TimingProperties

        /// <summary>name of the MaxReductionRatioIsochroneMode field</summary>
        public const string s_FieldMaxReductionRatioIsochroneMode = "@MaxReductionRatioIsochroneMode";

        #endregion

        #region InterfaceSubmoduleItem

        public const string s_FieldPdevCombinedObjectSupported = @"PDEV_CombinedObjectSupported";

        public const string s_FieldMaxFrameStartTime = @"MaxFrameStartTime";

        public const string s_FieldIsMaxFrameStartTime = @"IsMaxFrameStartTime";

        public const string s_FieldMinNrtGap = @"MinNRTGap";

        public const string s_FieldIsMinNrtGap = @"IsMinNRTGap";

        #endregion

        #region SystemRedundancy

        public const string s_FieldSupportedMultipleRoles = @"SupportedMultipleRoles";

        public const string s_FieldMaxMrpInstances = @"MaxMRPInstances";

        public const string s_FieldIsSupportedMultipleRoles = "IsSupportedMultipleRoles";

        public const string s_FieldIsMaxMrpInstances = "IsMaxMRPInstances";

        public const string s_FieldMinRdht = @"MinRDHT";
        public const string s_FieldExistsMinRdht = @"ExistsMinRDHT";

        #endregion

        public const string s_FieldIsCheckDeviceIdAllowed = "@IsCheckDeviceIDAllowed";

        public const string s_FieldMinRtc3Gap = @"MinRTC3Gap";

        public const string s_FieldIsMinRtc3Gap = @"IsMinRTC3Gap";

        public const string s_FieldMinYellowTime = @"MinYellowTime";

        public const string s_FieldIsMinYellowTime = @"IsMinYellowTime";

        public const string s_FieldYellowSafetyMargin = @"YellowSafetyMargin";

        public const string s_FieldIsYellowSafetyMargin = @"IsYellowSafetyMargin";

        #endregion

        #region V2.32

        #region SystemRedundancy

        public const string s_FieldDataInvalidOnBackupArSupported = @"DataInvalidOnBackupAR_Supported";
        public const string s_FieldS2MaxInputOnBackupDelay = @"S2MaxInputOnBackupDelay";
        public const string s_FieldR2MaxInputOnBackupDelay = @"R2MaxInputOnBackupDelay";

        #endregion

        #region MAUTypes

        public const string s_FieldExtensionSupported = @"ExtensionSupported";
        public const string s_FieldMauTypeList = @"MAUTypeList";
        public const string s_FieldMauTypeItems = @"MAUTypeItems";
        public const string s_FieldAdjustSupported = @"AdjustSupported";
        public const string s_FieldExtension = @"Extension";

        #endregion

        #region Channels

        public const string s_FieldInputChannels = @"InputChannels";
        public const string s_FieldOutputChannels = @"OutputChannels";
        public const string s_FieldChannelNumber = @"Number";
        public const string s_FieldData = @"Data";
        public const string s_FieldQuality = @"Quality";
        public const string s_FieldFormat = @"Format";
        public const string s_FieldOppositeDirection = @"OppositeDirection";

        #endregion

        public const string s_FieldIm5Supported = @"IM5_Supported";
        public const string s_FieldUsesStaticArpCacheEntries = @"UsesStaticARP_CacheEntries";
        public const string s_FieldDFPOutboundTruncationSupported = @"DFP_OutboundTruncationSupported";
        public const string s_FieldMaxRetentionTime = @"MaxRetentionTime";
        public const string s_FieldCheckMauTypeDifferenceSupported = @"CheckMAUTypeDifferenceSupported";
        public const string s_FieldMayIssueProcessAlarm = @"MayIssueProcessAlarm";

        #endregion

        #endregion

        #region V2.33

        #region DeviceAccessPointItem

        public const string s_FieldIsAssetManagement = @"AssetManagement";
        public const string s_FieldNumberOfDeviceAccessAr = @"NumberOfDeviceAccessAR";
        public const string s_FieldIsAdaptsRealIdentification = @"AdaptsRealIdentification";

        #endregion

        #region InterfaceSubmoduleItem

        public const string s_FieldIsReportingSystem = @"ReportingSystem";
        public const string s_FieldIsTimeSynchronisation = @"IsTimeSynchronisation";
        public const string s_FieldIsOwnIPSetsStandardGateway = @"OwnIP_SetsStandardGateway";
        public const string s_FieldDFPRedundantPathLayoutSupported = @"DFP_RedundantPathLayoutSupported";
        public const string s_FieldAdditionalForwardingRulesSupported = @"AdditionalForwardingRulesSupported";

        #endregion

        #region (Virtual)SubmoduleItem/PROFIenergy/

        public const string s_FieldProfIenergy = @"PROFIenergy";
        public const string s_FieldProfileVersion = @"ProfileVersion";
        public const string s_FieldEntityClass = @"EntityClass";
        public const string s_FieldEntitySubclass = @"EntitySubclass";
        public const string s_FieldIsDynamicTimeAndEnergyValues = @"DynamicTimeAndEnergyValues";

        public const string s_FieldEnergySavingModeList = @"EnergySavingModeList";
        public const string s_FieldEnergySavingModeItems = @"EnergySavingModeItems";
        public const string FieldID = @"ID";
        public const string s_FieldTimeToPause = @"TimeToPause";
        public const string s_FieldRtto = @"RTTO";
        public const string s_FieldTimeMinLengthOfStay = @"TimeMinLengthOfStay";
        public const string s_FieldPowerConsumption = @"PowerConsumption";
        public const string s_FieldEnergyConsumptionToPause = @"EnergyConsumptionToPause";
        public const string s_FieldEnergyConsumptionToOperation = @"EnergyConsumptionToOperation";

        public const string s_FieldMeasurementList = @"MeasurementList";
        public const string s_FieldMeasurementItems = @"MeasurementItems";
        public const string s_FieldMeasurementValues = @"MeasurementValues";
        public const string s_FieldMeasurementNumber = @"Number";
        public const string s_FieldAccuracyDomain = @"AccuracyDomain";
        public const string s_FieldAccuracyClass = @"AccuracyClass";

        public const string s_FieldReportingSystemEvents = @"ReportingSystemEvents";
        public const string s_FieldObservers = @"Observers";

        #endregion

        #endregion

        #region V2.34

        #region DeviceAccessPointItem

        public const string s_FieldNumberOfSubmodules = @"NumberOfSubmodules";

        #endregion

        #region IOConfigData

        public const string s_FieldApplicationLengthIncludesIoxS = @"ApplicationLengthIncludesIOxS";

        #endregion
        #endregion
     

        

        #region V2.35

        #region DeviceAccessPointItem

        public const string s_FieldNumberOfImplicitAr = @"NumberOfImplicitAR";

        #endregion

        #region ProcessAlarm

        public const string s_FieldReason = @"Reason";
        public const string s_FieldExtChannelProcessAlarms = @"ExtChannelProcessAlarms";
        public const string s_FieldProfileExtChannelProcessAlarms = @"ProfileExtChannelProcessAlarms";
        public const string s_FieldProfExtChanProcAlReasonAddValues = @"ProfExtChanProcAlReasonAddValues";
        public const string s_FieldExtChanProcAlReasonAddValues = @"ExtChanProcAlReasonAddValues";

        #endregion

        #region MediaRedundancy

        public const string s_FieldInterconnection = @"Interconnection";
        public const string s_FieldSupportedMrpInterconnRole = @"SupportedMRP_InterconnRole";
        public const string s_FieldMaxMrpInterconnInstances = @"MaxMRP_InterconnInstances";
        public const string s_FieldSupportsMrpInterconnPortConfig = @"SupportsMRP_InterconnPortConfig";

        #endregion

        #endregion

        #region V2.4

        #region SystemRedundancy

        public const string s_FieldDeviceTypes = "DeviceTypes";

        #endregion

        #region InterfaceSubmoduleItem

        public const string s_FieldTimeSynchronisation = @"TimeSynchronisation";
        public const string s_FieldWorkingClock = @"WorkingClock";
        public const string s_FieldGlobalTime = @"GlobalTime";
        public const string s_FieldRoles = @"Roles";
        public const string s_FieldSupportedServiceProtocols = @"SupportedServiceProtocols";

        #endregion

        #region PortSubmoduleItem

        public const string s_FieldSupportedFeatures = @"SupportedFeatures";
        public const string s_FieldSfpDiagnosisMonitoring = @"SFPDiagnosisMonitoring";
        public const string s_FieldProfIsafePirSupported = @"PROFIsafePIR_Supported";

        #endregion

        #region DeviceAccessPointItemItem

        public const string s_FieldSFPDiagnosisSupported = @"SFPDiagnosisSupported";

        #endregion

        #endregion

        #region V2.41

        #region CertificationInfo

        public const string s_FieldCertificationInfo = "CertificationInfo";
        public const string s_FieldSecurityClass = "SecurityClass";

        #endregion

        #endregion

        #region V2.42

        #region InterfaceSubmoduleItem

        public const string s_FieldSupportedDelayMeasurements = "SupportedDelayMeasurements";

        #endregion
        #region MAUTypeItem

        public const string s_FieldMaxTransferTimeTX = "MaxTransferTimeTX";
        public const string s_FieldMaxTransferTimeRX = "MaxTransferTimeRX";

        #endregion

        #region IOConfigData

        public const string s_FieldMaxApplicationARs = @"MaxApplicationARs";

        #endregion

        #endregion

        #region V2.43

        #region InterfaceSubmoduleItem

        public const string s_FieldDcpFeaturesSupported = "DCP_FeaturesSupported";
        public const string s_FieldSnmpFeaturesSupported = "SNMP_FeaturesSupported";
        public const string s_FieldAplFeaturesSupported = "APL_FeaturesSupported";

        #endregion

        #region MAUTypeItem

        public const string s_FieldAplPortClassification = "APLPortClassification";

        #endregion

        #region APLPortClassification

        public const string s_FieldSegmentClass = "SegmentClass";
        public const string s_FieldPortClass = "PortClass";
        public const string s_FieldPowerClass = "PowerClass";
        public const string s_FieldProtectionClass = "ProtectionClass";

        public const string s_FieldSegmentClassAsString = "SegmentClassAsString";
        public const string s_FieldPortClassAsString = "PortClassAsString";
        public const string s_FieldPowerClassAsString = "PowerClassAsString";
        public const string s_FieldProtectionClassAsString = "ProtectionClassAsString";

        #endregion

        #region ParameterRecordData

        public const string s_FieldRecordDataItems = "RecordDataItems";
        public const string s_FieldRecordDataId = "RecordDataID";

        #endregion

        #region CertificationInfo

        public const string s_FieldNetloadClass = "NetloadClass";
        public const string s_FieldNetloadClasses = "NetloadClasses";
        public const string s_FieldLinkSpeed = "LinkSpeed";
        public const string s_FieldLinkSpeedAsString = "LinkSpeedAsString";

        public const string s_FieldProfileProcessAutomation = "ProfileProcessAutomation";
        public const string s_FieldPADeviceClass = "PADeviceClass";
        public const string s_FieldPAProfileVersion = "PAProfileVersion";
        public const string s_FieldPAProfileDeviceID = "PAProfileDeviceID";
        public const string s_FieldPAProfileDeviceDAP_ID = "PAProfileDeviceDAP_ID";

        #endregion

        #region Communication Interface Modules

        public const string s_FieldCimTarget = "CIM_Target";
        public const string s_FieldCimId = "CIM_ID";
        public const string s_FieldInstance = "Instance";
        public const string s_FieldCommunicationInterfaces = "CommunicationInterfaces";

        public const string s_FieldCimInterface = "CIM_Interface";
        public const string s_FieldCimSupportedRecords = "CIM_SupportedRecords";
        public const string s_FieldCimResources = "CIM_Resources";
        public const string s_FieldCimSupportedFeatures = "CIM_SupportedFeatures";
        public const string s_FieldCimProtection = "Protection";

        public const string s_FieldSupportedRecords = "SupportedRecords";

        public const string s_FieldKeyDerivation = "KeyDerivation";
        public const string s_FieldKeyAgreement = "KeyAgreement";
        public const string s_FieldDigitalSignature = "DigitalSignature";
        public const string s_FieldStreamProtection = "StreamProtection";
        public const string s_FieldAlarmProtection = "AlarmProtection";
        public const string s_FieldConnectionManagementProtection = "ConnectionManagementProtection";

        public const string s_FieldSupported = "Supported";
        public const string s_FieldAlgorithms = "Algorithms";
        public const string s_FieldAuthnOnly = "AuthnOnly";
        public const string s_FieldAuthnEnc = "AuthnEnc";

        #endregion

        #endregion

        #region V2.44

        #region InterfaceSubmoduleItem

        public const string s_FieldBridge_FeaturesSupported = "Bridge_FeaturesSupported";
        public const string s_FieldTSN_ConfigurationsSupported = "TSN_ConfigurationsSupported";

        #endregion

        #region ParameterRecordData

        public const string s_FieldAccess = "Access";

        #endregion

        #endregion

        #region V2.45

        #region(Virtual) SubmoduleItem

        public const string s_FieldProfIsafeFscpTestModeSupported = "PROFIsafeFSCP_TestMode_Supported";

        #endregion

        #region DataItem

        public const string s_FieldDataItemId = "DataItemID";

        #endregion

        #region BitDataItem

        public const string s_FieldBitDataItemId = "BitDataItemID";

        #endregion

        #region DataRef

        public const string s_FieldDataTarget = "DataTarget";

        #endregion

        #region Channel

        public const string s_FieldDataRef = "DataRef";

        #endregion

        #endregion
        //########################################################################################
        #region Data Object Model - Objects (even abstract objects)
        // Contains all object names as constants (even abstract object names)

        /// <summary>name of the ModulePlugData object</summary>
        public const string s_ObjectPortProperties = @"PortProperties";

        #region V1.0

        // Structure Objects
        /// <summary>name of the DeviceStructureElement object</summary>
        public const string s_ObjectDeviceStructureElement = @"DeviceStructureElement";
        /// <summary>name of the AccessPointStructureElement object</summary>
        public const string s_ObjectAccessPointStructureElement = @"AccessPointStructureElement";
        /// <summary>name of the ModuleStructureElement object</summary>
        public const string s_ObjectModuleStructureElement = @"ModuleStructureElement";
        /// <summary>name of the abstract StructureElement object</summary>
		public const string s_ObjectStructureElement = @"StructureElement";

        // Common Objects
        /// <summary>name of the ApplicationRelations object</summary>
        public const string s_ObjectApplicationRelations = @"ApplicationRelations";
        /// <summary>name of the AreaItem object</summary>
        public const string s_ObjectAreaItem = @"AreaItem";
        /// <summary>name of the ChannelDiagnostic object</summary>
        public const string s_ObjectChannelDiagnostic = @"ChannelDiagnostic";
        /// <summary>name of the ConstData object</summary>
        public const string s_ObjectConstData = @"ConstData";
        /// <summary>name of the DataItem object</summary>
        public const string s_ObjectDataItem = @"DataItem";
        /// <summary>name of the Device object</summary>
        public const string s_ObjectDevice = @"Device";
        /// <summary>name of the DeviceAccessPoint object</summary>
        public const string s_ObjectDeviceAccessPoint = @"DeviceAccessPoint";
        /// <summary>name of the DeviceInfo object</summary>
        public const string s_ObjectDeviceInfo = @"DeviceInfo";
        /// <summary>name of the Graphic object</summary>
        public const string s_ObjectGraphic = @"Graphic";
        /// <summary>name of the abstract GsdObject object</summary>
        public const string s_ObjectGsdObject = @"GsdObject";
        /// <summary>name of the IOConfigData object</summary>
        public const string s_ObjectIoConfigData = @"IOConfigData";
        /// <summary>name of the IOData object</summary>
        public const string s_ObjectIoData = @"IOData";
        /// <summary>name of the Module object</summary>
        public const string s_ObjectModule = @"Module";
        /// <summary>name of the ModuleInfo object</summary>
        public const string s_ObjectModuleInfo = @"ModuleInfo";
        /// <summary>name of the abstract ModuleObject object</summary>
		public const string s_ObjectModuleObject = @"ModuleObject";
        /// <summary>name of the ModulePlugData object</summary>
        public const string s_ObjectModulePlugData = @"ModulePlugData";
        /// <summary>name of the ParameterRecordData object</summary>
        public const string s_ObjectParameterRecordData = @"ParameterRecordData";
        /// <summary>name of the abstract RecordData object</summary>
		public const string s_ObjectRecordData = @"RecordData";
        /// <summary>name of the RefData object</summary>
        public const string s_ObjectRefData = @"RefData";
        /// <summary>name of the TimingProperties object</summary>
        public const string s_ObjectTimingProperties = @"TimingProperties";
        /// <summary>name of the UnitDiagnosticType object</summary>
        public const string s_ObjectUnitDiagnosticType = @"UnitDiagnosticType";
        /// <summary>name of the ValueItem object</summary>
        public const string s_ObjectValueItem = @"ValueItem";
        /// <summary>name of the VirtualSubmodule object</summary>
        public const string s_ObjectVirtualSubmodule = @"VirtualSubmodule";

        #endregion

        #region V2.0

        // Structure Objects

        // Common Objects
        /// <summary>name of the IsochroneMode object</summary>
        public const string s_ObjectIsochroneMode = @"IsochroneMode";
        /// <summary>name of the Subslot object</summary>
        public const string s_ObjectSubslot = @"Subslot";
        /// <summary>name of the abstract PredefinedSubmoduleObject object</summary>
		public const string s_ObjectPredefinedSubmoduleObject = @"PredefinedSubmoduleObject";
        /// <summary>name of the InterfaceSubmodule object</summary>
        public const string s_ObjectInterfaceSubmodule = @"InterfaceSubmodule";
        /// <summary>name of the PortSubmodule object</summary>
        public const string s_ObjectPortSubmodule = @"PortSubmodule";
        /// <summary>name of the FParameterRecordData object</summary>
        public const string s_ObjectFParameterRecordData = @"FParameterRecordData";
        // <summary>name of the FRefData object</summary>
        //public const string ObjectFRefData = @"FRefData";
        /// <summary>name of the BitDataItem object</summary>
        public const string s_ObjectBitDataItem = @"BitDataItem";
        /// <summary>name of the RTClass3TimingProperties dummy object</summary>
        public const string s_ObjectRtClass3TimingProperties = @"RTClass3TimingProperties";
        /// <summary>name of the SubmodulePlugData dummy object</summary>
        public const string s_ObjectSubmodulePlugData = @"SubmodulePlugData";
        /// <summary>name of the ExtendedChannelDiagnostic dummy object</summary>
        public const string s_ObjectExtendedChannelDiagnostic = @"ExtendedChannelDiagnostic";
        /// <summary>name of the SystemDefinedChannelDiagnostic dummy object</summary>
        public const string s_ObjectSystemDefinedChannelDiagnostic = @"SystemDefinedChannelDiagnostic";

        #endregion

        #region V2.1

        /// <summary>name of the SubmoduleStructureElement object</summary>
        public const string s_ObjectSubmoduleStructureElement = @"SubmoduleStructureElement";

        /// <summary>name of the Slot object</summary>
        public const string s_ObjectSlot = @"Slot";

        public const string s_ObjectSubmodule = @"Submodule";

        /// <summary>name of the AddValueDataItem object</summary>
        public const string s_ObjectAddValueDataItem = @"AddValueDataItem";

        /// <summary>name of the ProfileChannelDiagnostic object</summary>
        public const string s_ObjectProfileChannelDiagnostic = @"ProfileChannelDiagnostic";

        /// <summary>name of the ProfileExtendedChannelDiagnostic dummy object</summary>
        public const string s_ObjectProfileExtendedChannelDiagnostic = @"ProfileExtendedChannelDiagnostic";

        /// <summary>name of the MediaRedundancy object</summary>
        public const string s_ObjectMediaRedundancy = @"MediaRedundancy";

        #endregion

        #region V2.2

        public const string s_ObjectBlockVersionElement = @"BlockVersion";

        #endregion

        #region V2.25

        public const string s_ObjectPluggablePortSubmodule = @"PluggablePortsubmodule";

        #endregion

        #region V2.3

        /// <summary>name of the SystemRedundancy object</summary>
        public const string s_ObjectSystemRedundancy = @"SystemRedundancy";

        /// <summary>name of the MenuData object</summary>
        public const string s_ObjectMenuData = @"MenuData";
        public const string s_ObjectMenuRef = @"MenuRef";
        public const string s_ObjectParamRef = @"ParamRef";

        #endregion

        #region V2.31

        /// <summary>name of the SystemDefinedChannelDiagnostics field</summary>
        public const string s_FieldSystemDefinedChannelDiagnostics = @"SystemDefinedChannelDiagnostics";

        #endregion

        #region V2.32

        public const string s_ObjectMauTypeList = @"MAUTypeList";
        public const string s_ObjectMauTypeItem = @"MAUTypeItem";
        public const string s_ObjectChannel = @"Channel";
        public const string s_ObjectData = @"Data";
        public const string s_ObjectQuality = @"Quality";

        #endregion

        #region V2.33

        public const string s_ObjectProfIenergy = @"PROFIenergy";
        public const string s_ObjectEnergySavingModeList = @"EnergySavingModeList";
        public const string s_ObjectEnergySavingModeItem = @"EnergySavingModeItem";
        public const string s_ObjectMeasurementList = @"MeasurementList";
        public const string s_ObjectMeasurementItem = @"MeasurementItem";
        public const string s_ObjectMeasurementValue = @"MeasurementValue";
        public const string s_ObjectReportingSystemEvents = @"ReportingSystemEvents";
        public const string s_ObjectObserver = @"Observer";

        #endregion

        #region V2.34

        //TBD

        #endregion

        #region V2.35

        public const string s_FieldChannelProcessAlarms = @"ChannelProcessAlarms";
        public const string s_FieldSystemDefinedChannelProcessAlarms = @"SystemDefinedChannelProcessAlarms";
        public const string s_FieldProfileChannelProcessAlarms = @"ProfileChannelProcessAlarms";

        public const string s_ObjectChannelProcessAlarm = @"ChannelProcessAlarm";
        public const string s_ObjectSystemDefinedChannelProcessAlarm = @"SystemDefinedChannelProcessAlarm";
        public const string s_ObjectProfileChannelProcessAlarm = @"ProfileChannelProcessAlarm";
        public const string s_ObjectExtChannelProcessAlarm = @"ExtChannelProcessAlarm";
        public const string s_ObjectProfileExtChannelProcessAlarm = @"ProfileExtChannelProcessAlarm";

        public const string s_ObjectInterconnection = @"Interconnection";

        #endregion

        #region V2.4

        #region InterfaceSubmoduleItem

        public const string s_ObjectTimeSynchronisation = @"TimeSynchronisation";
        public const string s_ObjectWorkingClock = @"WorkingClock";
        public const string s_ObjectGlobalTime = @"GlobalTime";

        #endregion

        #endregion

        #region V2.41

        #region DeviceAccessPointItem

        public const string s_ObjectCertificationInfo = @"CertificationInfo";

        #endregion

        #endregion

        #region V2.43

        #region ParameterRecordData

        public const string s_ObjectRecordDataStructureElement = @"RecordDataStructureElement";

        #endregion

        #region MAUTypeItem

        public const string s_ObjectAPLPortClassification = @"APLPortClassification";

        #endregion

        #region CertificationInfo

        public const string s_ObjectNetloadClasses = @"NetloadClasses";
        public const string s_ObjectProfileProcessAutomation = @"ProfileProcessAutomation";

        #endregion

        #region Communication Interface Modules

        public const string s_ObjectCommunicationInterfaceStructureElement = @"CommunicationInterfaceStructureElement";
        public const string s_ObjectCommunicationInterface = @"CommunicationInterface";

        public const string s_ObjectCIMInterface = @"CIM_Interface";
        public const string s_ObjectCIMSupportedRecords = @"CIM_SupportedRecords";
        public const string s_ObjectCIMResources = @"CIM_Resources";
        public const string s_ObjectCIMSupportedFeatures = @"CIM_SupportedFeatures";
        public const string s_ObjectCIMProtection = @"Protection";

        public const string s_ObjectKeyDerivation = "KeyDerivation";
        public const string s_ObjectKeyAgreement = "KeyAgreement";
        public const string s_ObjectDigitalSignature = "DigitalSignature";
        public const string s_ObjectStreamProtection = "StreamProtection";
        public const string s_ObjectAlarmProtection = "AlarmProtection";
        public const string s_ObjectConnectionManagementProtection = "ConnectionManagementProtection";

        #endregion

        #endregion
        #region V2.44

        public const string s_FieldFBaseIDRecordData = @"FBaseIDRecordData";

        public const string s_ObjectFBaseIDRecordData = @"FBaseIDRecordData";

        #endregion
        #region V2.45

        public const string s_ObjectDataRef = "DataRef";

        #endregion
        #endregion
    }
}


