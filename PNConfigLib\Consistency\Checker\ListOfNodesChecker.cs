/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ListOfNodesChecker.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Xml;
using PNConfigLib.BusinessLogic.HWCNBL.Utilities;
using PNConfigLib.CentralDeviceImport;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker
{
    internal class ListOfNodesChecker
    {
        private readonly ListOfNodes m_ListOfNodes;

        private XsdChecker m_XsdChecker;
        private readonly string m_ListOfNodesPath;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="lon"></param>
        public ListOfNodesChecker(ListOfNodes lon, XsdChecker xsdChecker, string listOfNodesPath)
        {
            m_ListOfNodes = lon;
            m_XsdChecker = xsdChecker;
            m_ListOfNodesPath = listOfNodesPath;
        }

        /// <summary>
        /// Check consistencies
        /// </summary>
        /// <returns></returns>
        public void Check()
        {
            // Call check methods
            CheckCentralDeviceCatalog();
            CheckCentralDeviceInterface();
            CheckGSDMLUniqueness();
            IsDecentralDeviceGSDRefIdValid();
        }

        /// <summary>
        /// 
        /// </summary>
        private void CheckCentralDeviceCatalog()
        {
            foreach (PNDriverType pnd in m_ListOfNodes.PNDriver)
            {
                if (pnd.Interface?.InterfaceType == PNDriverInterfaceEnum.Custom)
                {
                    IsCustomInterfacePathValid(pnd.Interface.CustomInterfacePath, pnd.DeviceID);

                    string customInterfacePath = Path.Combine(Path.GetDirectoryName(m_ListOfNodesPath), pnd.Interface.CustomInterfacePath);

                    IsCustomInterfaceFileExist(customInterfacePath, pnd.DeviceID);

                    IsCustomInterfaceFileValidForXsd(customInterfacePath);

                    if (!string.IsNullOrEmpty(pnd.DeviceVersion))
                    {
                        IsCustomDeviceVersionValid(pnd.DeviceVersion);
                    }
                }
                else
                {
                    IsCustomInterfacePathNotNull(pnd.Interface?.CustomInterfacePath, pnd.DeviceID);

                    string interfaceResourceFileName = string.Format(
                        CultureInfo.InvariantCulture,
                        CentralDeviceCatalogObjectReader.CentralDeviceInterfaceFormat,
                        pnd?.Interface?.InterfaceType,
                        pnd.DeviceVersion);

                    IsCentralDeviceVariantsSupported(pnd.Interface.InterfaceType, interfaceResourceFileName, pnd.DeviceVersion);

                    IsInterfaceResourceFileNameValidForXsd(interfaceResourceFileName);
                }
            }
        }

        private void CheckCentralDeviceInterface()
        {
            foreach (PNDriverType pnd in m_ListOfNodes.PNDriver)
            {
                CentralDeviceCatalog catalog = Catalog.GetCentralDeviceCatalog(
                    pnd.Interface.InterfaceType,
                    pnd.Interface.CustomInterfacePath,
                    pnd.DeviceVersion,
                    m_ListOfNodesPath
                    );

                IsAttributeListValid(catalog.AttributeAccess.AttributeList, pnd);
                IsAttributeListValid(catalog.Interface.AttributeAccess.AttributeList, pnd);
                catalog.Interface.PortList.Values.ToList().ForEach(
                    p => IsAttributeListValid(p.AttributeAccess.AttributeList, pnd));
            }
        }

        internal static bool AreGsdmlFilesExist(List<DecentralDeviceType> decentralDevices, string listOfNodesPath)
        {
            bool retval = true;
            
            foreach (DecentralDeviceType lonDecentralDevice in decentralDevices)
            {               
                string gsdPath = FileOperations.GetFullDirectoryPath(lonDecentralDevice.GSDPath, listOfNodesPath);

                if (!File.Exists(gsdPath))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_GSDMLExistence,
                        lonDecentralDevice.GSDPath);
                    retval = false;
                    return retval;
                }
            }

            return retval;
        }

        private void CheckGSDMLUniqueness()
        {
            Dictionary<string, string> gsdmlPaths = new Dictionary<string, string>();
            HashSet<string> repeatGsdml = new HashSet<string>();
            foreach (DecentralDeviceType lonDecentralDevice in m_ListOfNodes.DecentralDevice)
            {
                string fileName = Path.GetFileName(lonDecentralDevice.GSDPath);
                string path = lonDecentralDevice.GSDPath.ToUpperInvariant().Replace(@"\\", @"\");
                if (fileName != null
                    && gsdmlPaths.ContainsKey(fileName.ToUpperInvariant())
                    && gsdmlPaths[fileName.ToUpperInvariant()] != path)
                {
                    repeatGsdml.Add(fileName.ToUpperInvariant());
                }
                else if (fileName != null
                         && !gsdmlPaths.ContainsKey(fileName.ToUpperInvariant()))
                {
                    gsdmlPaths.Add(fileName.ToUpperInvariant(), path);
                }
            }

            foreach (string gsdml in repeatGsdml)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Warning,
                    string.Empty,
                    ConsistencyConstants.XML_GSDMLPathUniqueness,
                    gsdml);
            }
        }

        internal void IsDecentralDeviceGSDRefIdValid()
        {
            foreach (DecentralDeviceType lonDecentralDevice in m_ListOfNodes.DecentralDevice)
            {
                string gsdFileName = Path.GetFileName(lonDecentralDevice.GSDPath);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDecentralDevice.GSDRefID);

                if (decentralDeviceCatalog == null)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IncorrectGSDRefIDDevice,
                        lonDecentralDevice.GSDRefID,
                        lonDecentralDevice.DeviceID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsCustomInterfacePathValid(string customInterfacePath, string deviceId)
        {
            if(string.IsNullOrEmpty(customInterfacePath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_CustomInterfacePathNull,
                    deviceId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsCustomDeviceVersionValid(string deviceVersion)
        {
            FwVersion fwVersion = HWCNBL.Utilities.AttributeUtilities.MapVersion(deviceVersion);
            if (fwVersion.Equals(FwVersion.Undefined))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_DeviceVersionIsNotValid,
                    deviceVersion);
                throw new ConsistencyCheckException();
            }
        }
        private void IsCustomInterfaceFileExist(string customInterfacePath, string deviceId)
        {
            if (!File.Exists(customInterfacePath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_CustomInterfaceFileNotExist,
                    customInterfacePath,
                    deviceId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsCustomInterfaceFileValidForXsd(string interfacePath)
        {
            if (!m_XsdChecker.ValidateXsd(interfacePath, XsdChecker.ControllerVariantXsdPath))
            {
                throw new ConsistencyCheckException();
            }
        }

        private void IsInterfaceResourceFileNameValidForXsd(string interfaceResourceFileName)
        {
            if (!m_XsdChecker.ValidateResource(interfaceResourceFileName, XsdChecker.ControllerVariantXsdPath))
            {
                throw new ConsistencyCheckException();
            }
        }

        private void IsCustomInterfacePathNotNull(string customInterfacePath, string deviceId)
        {
            if (!string.IsNullOrEmpty(customInterfacePath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Warning,
                    string.Empty,
                    ConsistencyConstants.XML_CustomInterfacePathNotNull,
                    deviceId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsCentralDeviceVariantsSupported(PNDriverInterfaceEnum interfaceType, string interfaceResourceFileName , string deviceVersion)
        {
            if (!IsCentralInterfaceConfigFileExist(interfaceResourceFileName))
            {
                string deviceVariant = StringOperations.CombineParametersWithSplitter(
                    "_",
                    interfaceType.ToString(),
                    deviceVersion);

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_CentralDeviceVariantsNotSupported,
                    deviceVariant);
                throw new ConsistencyCheckException();
            }
        }

        private bool IsCentralInterfaceConfigFileExist(string interfaceResourceFileName)
        {
            List<string> resourceFileNames = Assembly.GetExecutingAssembly().GetManifestResourceNames().ToList();
            return resourceFileNames.Any(resFile => resFile.Contains(interfaceResourceFileName));
        }

        private static void IsAttributeListValid(Dictionary<string, object> attributeLookup, PNDriverType centralDevice)
        {
            foreach (string attrKey in attributeLookup.Keys)
            {
                if (!(attributeLookup[attrKey] is XmlNode)
                    && !(attributeLookup[attrKey] is XmlNode[]))
                {
                    continue;
                }

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InvalidCentralDeviceAttributeType,
                    attrKey,
                    centralDevice.Interface.CustomInterfacePath,
                    centralDevice.DeviceID);
                throw new ConsistencyCheckException();
            }
        }

        internal static bool IsListOfNodesFileExist(string listOfNodesXmlPath)
        {
            if (string.IsNullOrEmpty(listOfNodesXmlPath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NullOrEmptyListOfNodesXML);
                return false;
            }
            if (!File.Exists(listOfNodesXmlPath))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotExistListOfNodesXML);
                return false;
            }

            return true;
        }

        internal static string GetGsdFileName(string deviceId, ListOfNodes lon)
        {
            DecentralDeviceType lonDecentralDevice = GetListOfNodesDeviceById(deviceId, lon);
            return Path.GetFileName(lonDecentralDevice?.GSDPath);
        }

        internal static DecentralDeviceType GetListOfNodesDeviceById(string deviceId, ListOfNodes lon)
        {
            return lon.DecentralDevice.SingleOrDefault(d => d.DeviceID == deviceId);
        }

        internal static PNDriverType GetListOfNodesCentralDeviceById(string deviceId, ListOfNodes lon)
        {
            return lon.PNDriver.SingleOrDefault(pnd => pnd.DeviceID == deviceId);
        }
    }
}
