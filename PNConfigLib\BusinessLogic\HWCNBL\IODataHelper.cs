/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IODataHelper.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IODataHelper.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */


namespace PNConfigLib.BusinessLogic.HWCNBL
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;

    using PNConfigLib.ConfigReader.Configuration;
    using PNConfigLib.DataModel.AttributeUtilities;
    using PNConfigLib.DataModel.DataAddresses;
    using PNConfigLib.DataModel.PCLObjects;
    using PNConfigLib.HWCNBL;
    using PNConfigLib.HWCNBL.Constants.AttributeValues;
    using PNConfigLib.PNProjectManager;

    /// <summary>
    /// IoDataHelper
    /// </summary>
    internal class IoDataHelper
    {
        internal void SetIOData(
            string moduleId,
            int startAddress,
            IoTypes ioType,
            IOAddressManager ioAddressManager,
            Submodule virtualSubmodule,
            AttributeAccessCode ac,
            string addressRangeType,
            string startAddressType,
            IoAddressImplementationType ioaddressImplementationType)
        {
            int addressRange =
                virtualSubmodule.AttributeAccess.GetAnyAttribute<int>(addressRangeType, ac.GetNew(), -1);

            if (!ac.IsOkay)
            {
                throw new PNFunctionsException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "{0} address length not given for module: {1}",
                        ioType.ToString(),
                        moduleId));
            }

            if (ioaddressImplementationType == IoAddressImplementationType.FromVirtualSubmodule)
            {
                List<int> freeAddress = ioAddressManager.GetFreeAddresses(addressRange, ioType, false);
                startAddress = freeAddress[0];
            }

            if (ioaddressImplementationType == IoAddressImplementationType.FromXml || ioaddressImplementationType
                == IoAddressImplementationType.FromVirtualSubmodule)
            {
                virtualSubmodule.AttributeAccess.SetAnyAttribute<int>(startAddressType, startAddress);
                virtualSubmodule.DataAddresses.Add(new DataAddress(ioType, startAddress, addressRange));
            }
        }

        internal enum IoAddressImplementationType
        {
            FromXml,

            FromVirtualSubmodule,

            AlreadySet
        }
    }
}
