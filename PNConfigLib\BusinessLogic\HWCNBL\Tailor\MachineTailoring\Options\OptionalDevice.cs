/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: OptionalDevice.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options
{
    /// <summary>
    /// Summary description for OptionalDevice.
    /// </summary>
    public class OptionalDevice : ITailoringConsistency
    {
        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        private Dictionary<string, string> portBoundaryNames = new Dictionary<string, string>
            {
                {InternalAttributeNames.PnIrtPortSyncDomainBoundary, "Sync Domain Boundary"},
                {InternalAttributeNames.PnPTPBoundary, "PTP Boundary"},
                {InternalAttributeNames.PnDCPBoundary, "DCP Boundary"}
            };

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private DataModel.PCLObjects.Interface m_IODevice;

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        public OptionalDevice(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            m_IODevice = ioDeviceInterface;
        }

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        public void CheckConsistency()
        {
            CheckToolChangerWithOptionalDeviceConsistency(m_IODevice);

            CheckSyncRoleOfOptionalDeviceConsistency(m_IODevice);

            CheckMrpRoleOfOptionalDeviceConsistency(m_IODevice);

            CheckNeighborIDeviceConsistency(m_IODevice);

            CheckOptionalIDeviceConsistency(m_IODevice);

            CheckFixedPeerConsistency(m_IODevice);

            CheckPortBoundariesConsistency(m_IODevice);

            CheckMAUTypeConsistency(m_IODevice);
        }

        public void CheckConsistency(bool omitTopologyCheck)
        {

        }

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        /// <summary>
        /// Consistency Checks for 'An optional interface shall not have a tool changer port' and 'An optional IOD
        /// shall not be the first IO device head device of a docking unit'.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckToolChangerWithOptionalDeviceConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            object[] parameters = new object[1];
            List<DataModel.PCLObjects.Port> portModules = NavigationUtilities.GetPortModules(ioDeviceInterface);
            foreach (DataModel.PCLObjects.Port portModule in portModules)
            {
                if (Utilities.MachineTailor.MachineTailorUtility.IsMultipleConnectionEnabled(portModule))
                {
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(portModule);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        portModule,
                        ConsistencyConstants.Optional_Device_With_Tool_Changer_Port,
                        parameters);

                    break;
                }
                IList<DataModel.PCLObjects.Port> partnerPorts = portModule.GetPartnerPorts();
                if (partnerPorts == null || partnerPorts.Count == 0)
                {
                    continue;
                }
                if (partnerPorts.Count == 1)
                {
                    if (Utilities.MachineTailor.MachineTailorUtility.IsMultipleConnectionEnabled(partnerPorts[0]))
                    {

                        parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(portModule);
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            portModule,
                            ConsistencyConstants.Optional_Device_Interconnected_With_Tool_Changer_Port,
                            parameters);

                        break;
                    }
                }
            }
        }

        /// <summary>
        /// Consistency Check for 'Optional IODs shall not be a sync-master'.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckSyncRoleOfOptionalDeviceConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            PNIOD ioDevice =ioDeviceInterface.PNIOD;
            PNIRTSyncRole syncRole = (PNIRTSyncRole)ioDevice.AttributeAccess.GetAnyAttribute<byte>(
                InternalAttributeNames.PnIrtSyncRole,
                ac,
                (byte)PNIRTSyncRole.NotSynchronized);
            if (syncRole == PNIRTSyncRole.PrimarySyncMaster
                || syncRole == PNIRTSyncRole.SecondarySyncMaster
                || syncRole == PNIRTSyncRole.SyncMaster)
            {
                object[] parameters = new object[1];
                parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    ioDeviceInterface,
                    ConsistencyConstants.Optional_Device_Incompatible_Sync_Role,
                    parameters);

            }
        }

        /// <summary>
        /// Consistency Check for 'Optional IODs shall not be an mrp manager' and 'For optional Devices, 
        /// it is not allowed to have one ring port as a fixed peer and one as any peer'. 
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckMrpRoleOfOptionalDeviceConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            object[] parameters = new object[1];
            AttributeAccessCode ac = new AttributeAccessCode();

            PNMrpRole mrpRole = (PNMrpRole)ioDeviceInterface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole,
                ac,
                (UInt32)PNMrpRole.NotInRing);
            if (mrpRole == PNMrpRole.Manager
                || mrpRole == PNMrpRole.NormManagerAuto)
            {
                parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    ioDeviceInterface,
                    ConsistencyConstants.Optional_Device_Incompatible_Mrp_Role,
                    parameters);

            }

            if (mrpRole != PNMrpRole.NotInRing)
            {
                int numberOfFixedPeers = 0;
                // Get selected ring ports of the interface
                IMethodData ringPortMethodData = new MethodData();
                ringPortMethodData.Name = GetSelectedRingPorts.Name;
                ioDeviceInterface.BaseActions.CallMethod(ringPortMethodData);
                List<DataModel.PCLObjects.Port> selectedRingPorts =
                    ringPortMethodData.Arguments[GetSelectedRingPorts.SelectedRingPorts] as
                        List<DataModel.PCLObjects.Port>;

                //We assume only 2 ring ports exist
                if (selectedRingPorts.Count != 2)
                {
                    return;
                }
                foreach (DataModel.PCLObjects.Port selectedRingPort in selectedRingPorts)
                {
                    IList<DataModel.PCLObjects.Port> partnerPorts = selectedRingPort.GetPartnerPorts();
                    //Only 1 partner port should exist. 
                    if (partnerPorts.Count != 1)
                    {
                        continue;
                    }
                    numberOfFixedPeers++;
                }

                //Either 0 or 2 fixed peers.
                if (numberOfFixedPeers % 2 != 0)
                {
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        ioDeviceInterface,
                        ConsistencyConstants.Optional_Device_Mrp_Ring_Port_Error,
                        parameters);
                }

            }
        }


        /// <summary>
        /// Consistency Check for 'Neighbor interface submodules of an Optional IOD interface submodule
        /// must have the attribute PNIoSubmoduleModelSupp with a value higher than 0 '.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckNeighborIDeviceConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            object[] parameters = new object[1];
            List<DataModel.PCLObjects.Interface> connectedInterfaces =
                NavigationUtilities.GetConnectedInterfaces(ioDeviceInterface);
            if (connectedInterfaces.Count == 0)
            {
                return;
            }
            foreach (DataModel.PCLObjects.Interface connectedInterface in connectedInterfaces)
            {
                if (connectedInterface.Equals(
                    NavigationUtilities.GetControllerInterfaceOfDeviceInterface(ioDeviceInterface)))
                {
                    continue;
                }
                if (AttributeUtilities.IsIDevice(connectedInterface))
                {
                    if (!AttributeUtilities.IsDecentralPDEV(connectedInterface))
                    {
                        parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(connectedInterface);
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            connectedInterface,
                            ConsistencyConstants.Optional_Device_Iod_Pdev_Assigned_Iocontroller,
                            parameters);

                    }
                }
                else if (!GeneralUtilities.IsPDEVDevice(connectedInterface))
                {
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(connectedInterface);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        connectedInterface,
                        ConsistencyConstants.Optional_Device_Iod_Pdev_Assigned_Iocontroller,
                        parameters);

                }
            }

            if (AttributeUtilities.IsIDevice(ioDeviceInterface))
            {
                if (!AttributeUtilities.IsDecentralPDEV(ioDeviceInterface))
                {
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        ioDeviceInterface,
                        ConsistencyConstants.Optional_Device_Iod_Pdev_Assigned_Iocontroller,
                        parameters);

                }
            }
        }

        /// <summary>
        /// Consistency Check for IDevice assigned as an Optional Device. In this case the IDevice should have a super-ordinate IO-System.
        /// An Optional IDevice shouldn't be compiled if it is not connected to an IO-Controller.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckOptionalIDeviceConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            object[] parameters = new object[1];
            if (!AttributeUtilities.IsIDevice(ioDeviceInterface))
            {
                return;
            }
            PclObject iDeviceSuperOrdinate =
                NavigationUtilities.GetIoSystem(ioDeviceInterface);
            if (iDeviceSuperOrdinate != null)
            {
                return;
            }
            parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                ioDeviceInterface,
                ConsistencyConstants.Optional_IDevice_With_No_SuperOrdinate,
                parameters);

        }

        /// <summary>
        /// Consistency Check for 'At most 2 ports of an Optional Device shall be interconnected to a fixed partner'.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckFixedPeerConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            object[] parameters = new object[1];
            int numberOfFixedPeers = 0;
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(ioDeviceInterface);
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                IList<DataModel.PCLObjects.Port> partnerPorts = port.GetPartnerPorts();
                if (partnerPorts.Count != 1)
                {
                    continue;
                }
                numberOfFixedPeers++;
                if (numberOfFixedPeers > 2)
                {
                    parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(ioDeviceInterface);
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        ioDeviceInterface,
                        ConsistencyConstants.Optional_Device_Max_Fixed_Peer_Limit_Exceeded,
                        parameters);
                }
            }
        }

        /// <summary>
        /// Consistency Check for 'No boundary shall be set on any of the Optional Device�s ports'.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckPortBoundariesConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(ioDeviceInterface);
            foreach (DataModel.PCLObjects.Port portModule in ports)
            {
                CheckPortBoundaryConsistencyGeneric(InternalAttributeNames.PnIrtPortSyncDomainBoundary, portModule);
                CheckPortBoundaryConsistencyGeneric(InternalAttributeNames.PnPTPBoundary, portModule);
                CheckPortBoundaryConsistencyGeneric(InternalAttributeNames.PnDCPBoundary, portModule);
            }
        }

        private void CheckPortBoundaryConsistencyGeneric(string nameOfBoundary, DataModel.PCLObjects.Port portModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            object[] parameters = new object[2];
            UInt32 boundarySet = portModule.AttributeAccess.GetAnyAttribute<UInt32>(nameOfBoundary, ac, 0);
            if (boundarySet != 0)
            {
                parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(portModule);
                parameters[1] = portBoundaryNames[nameOfBoundary];
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    portModule,
                    ConsistencyConstants.Optional_Device_With_Set_Boundaries,
                    parameters);
            }
        }

        /// <summary>
        /// Consistency Check for 'For all ports in a particular tailored port group, 
        /// Transmission Medium/Duplex shall be set either to �Automatic Settings� (Autonegotiation), or 
        /// to one of the �100Mbit/s full duplex� settings'.
        /// </summary>
        /// <param name="ioDeviceInterface"></param>
        private void CheckMAUTypeConsistency(DataModel.PCLObjects.Interface ioDeviceInterface)
        {
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(ioDeviceInterface);
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                IList<DataModel.PCLObjects.Port> partnerPorts = port.GetPartnerPorts();
                if (partnerPorts.Count == 1)
                {
                    if (!Utilities.MachineTailor.MachineTailorUtility.CheckMAUTypeStatus(port))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Warning,
                            port,
                            ConsistencyConstants.Optional_Device_Mau_Type_Warning,
                            new object[] { AttributeUtilities.GetSubmoduleNameWithContainer(port) });
                    }
                    break;
                }
            }
        }

        #endregion
    }
}