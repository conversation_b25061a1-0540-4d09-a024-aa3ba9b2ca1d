/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerConstants.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.PNPlannerAdapter
{
    internal static class PNPlannerConstants
    {
        internal const string m_XmlDWD_RestartFactorCaption = "DWD_RestartFactor";

        internal const string m_XmlEndCaption = "End";

        internal const string m_XmlExternalSendClockFactor = "ExternalSendClockFactor";

        internal const string m_XmlFalseText = "false";

        internal const string m_XmlFastForwardingCaption = "FastForwarding";

        internal const string m_XmlFixedPhaseNumberCaption = "FixedPhaseNumber";

        internal const string m_XmlForwardingModeCaption = "ForwardingMode";

        internal const string m_XmlFOText = "FO";

        internal const string m_XmlFrameClassCaption = "FrameClass";

        internal const string m_XmlFrameDirection = "FrameDirection";

        internal const string m_XmlFramePreambleCaption = "FramePreamble";

        internal const string m_XmlFrameType = "FrameType";

        internal const string m_XmlFromSwitchCaption = "FromSwitch";

        internal const string m_XmlFromPortCaption = "FromPort";

        internal const string m_XmlToSwitchCaption = "ToSwitch";

        internal const string m_XmlToPortCaption = "ToPort";

        internal const string m_XmlGlobalsCaption = "Globals";

        internal const string m_XmlGroupNumber = "GroupNumber";

        internal const string m_XmlHasUsingData = "HasUsingData";

        internal const string m_XmlHeaderText = "PNPlannerInput";

        internal const string m_XmlIOControllerCaption = "IOController";

        internal const string m_XmlIoControllerSyncRoleCaption = "IoControllerSyncRole";

        internal const string m_XmlIoDeviceSyncRoleCaption = "IoDeviceSyncRole";

        internal const string m_XmlIRTCommonValues = "IRTCommonValues";

        internal const string m_XmlIrtStartupModeCaption = "IrtStartupMode";

        internal const string m_XmlIsOptionalCaption = "IsOptional";

        internal const string m_XmlinboundText = "inbound";

        internal const string m_XmlLegacyText = "Legacy";

        internal const string m_XmlLengthCaption = "Length";

        internal const string m_XmlLineRxDelayCaption = "LineRxDelay";

        internal const string m_XmlLinkCaption = "Link";

        internal const string m_XmlLinkDelayCaption = "LinkDelay";

        internal const string m_XmlLinksCaption = "Links";

        internal const string m_XmlLocalCaption = "Local";

        internal const string m_XmlLocalRTC3PeriodCaption = "LocalRTC3Period";

        internal const string m_XmlMaxBridgeDelayCaption = "MaxBridgeDelay";

        internal const string m_XmlMaxBridgeDelayFFWCaption = "MaxBridgeDelayFFW";

        internal const string m_XmlMaxBufferTimeCaption = "MaxBufferTime";

        internal const string m_XmlMaxBytesPerMs = "MaxBytesPerMs";

        internal const string m_XmlMaxDfpFeedCaption = "MaxDfpFeed";

        internal const string m_XmlMaxDfpFramesCaption = "MaxDfpFrames";

        internal const string m_XmlMaxFramesPerMs = "MaxFramesPerMs";

        internal const string m_XmlMaxFrameStartTime = "MaxFrameStartTime";

        internal const string m_XmlMaxIOCyclicBandwidth = "MaxIOCyclicBandwidth";

        internal const string m_XmlMaxPackgroupSwitchesCaption = "MaxPackgroupSwitches";

        internal const string m_XmlMaxPhasesCaption = "MaxPhases";

        internal const string m_XmlMaxPortActivityCaption = "MaxPortActivity";

        internal const string m_XmlMaxRTC12BytesPerMs = "MaxRTC12BytesPerMs";

        internal const string m_XmlMaxSizeREDCaption = "MaxSizeRED";

        internal const string m_XmlMaxSwitchTimeLagCaption = "MaxTimeLag";

        internal const string m_XmlMediaType = "MediaType";

        internal const string m_XmlMessageCaption = "Message";

        internal const string m_XmlMessagesCaption = "Messages";

        internal const string m_XmlMinAutomaticUnsyncUpdateTime = "MinAutomaticUnsyncUpdateTime";

        internal const string m_XmlMinFrameInterval = "MinFrameInterval";

        internal const string m_XmlMinFSOCaption = "MinFSO";

        internal const string m_XmlMinInterLsduGapCaption = "MinInterLsduGap";

        internal const string m_XmlMinNRTGap = "MinNRTGap";

        internal const string m_XmlMinRTC3GapCaption = "MinRTC3Gap";

        internal const string m_XmlMsgIdCaption = "MsgId";

        internal const string m_XmlNameCaption = "Name";

        internal const string m_XmlNumberCaption = "Nr";

        internal const string m_XmlOneText = "1";

        internal const string m_XmloutboundText = "outbound";

        internal const string m_XmlOutputFingerprintCaption = "OutputFingerprint";

        internal const string m_XmlPaddingCaption = "Padding";

        internal const string m_XmlPeerToPeerJitterCaption = "PeerToPeerJitter";

        internal const string m_XmlPhaseCaption = "Phase";

        internal const string m_XmlPNProxy = "PNProxy";

        internal const string m_XmlPortCaption = "Port";

        internal const string m_XmlPortsCaption = "Ports";

        internal const string m_XmlProxyNumberCaption = "ProxyNumber";

        internal const string m_XmlReceiverCaption = "Receiver";

        internal const string m_XmlRedBeginEndGroupCaption = "RedBeginEndGroup";

        internal const string m_XmlReductionCaption = "Reduction";

        internal const string m_XmlReductionsCaption = "Reductions";

        internal const string m_XmlRedundancyCaption = "Redundancy";

        internal const string m_XmlRedundancyExistsCaption = "RedundancyExists";

        internal const string m_XmlRedundantFrameIdCaption = "RedundantFrameId";

        internal const string m_XmlRedundantMsgIdCaption = "RedundantMsgId";

        internal const string m_XmlRelativeText = "Relative";

        internal const string m_XmlRxDelayCaption = "RxDelay";

        internal const string m_XmlRxPeriodGroupCaption = "RxPeriodGroup";

        internal const string m_XmlRxPortCaption = "RxPort";

        internal const string m_XmlRxPortMsgCaption = "RxPortMsg";

        internal const string m_XmlScfAdaptionNonPow2Supported = "ScfAdaptionNonPow2Supported";

        internal const string m_XmlScfAdaptionSupported = "ScfAdaptionSupported";

        internal const string m_XmlSendClockFactor = "SendClockFactor";

        internal const string m_XmlSenderCaption = "Sender";

        internal const string m_XmlSFCRC16Caption = "SFCRC16";

        internal const string m_XmlSFIdCaption = "SFId";

        internal const string m_XmlSFLengthCaption = "SFLength";

        internal const string m_XmlShortPreamble100MBitSupported = "ShortPreamble100MBitSupported";

        internal const string m_XmlShowDebugInfoText = "true";

        internal const string m_XmlSlotNoCaption = "SlotNo";

        internal const string m_XmlStartCaption = "Start";

        internal const string m_XmlStationNoCaption = "StationNo";

        internal const string m_XmlSubSlotNoCaption = "SubSlotNo";

        internal const string m_XmlSupportedSendClockFactors12 = "SupportedSendClockFactors12";

        internal const string m_XmlSupportedSendClockFactors3 = "SupportedSendClockFactors3";

        internal const string m_XmlSupportsDfpInRingCaption = "SupportsDfpInRing";

        internal const string m_XmlSuppRR12NonPow2 = "SuppRR12NonPow2";

        internal const string m_XmlSuppRR12Pow2 = "SuppRR12Pow2";

        internal const string m_XmlSuppRR3NonPow2 = "SuppRR3NonPow2";

        internal const string m_XmlSuppRR3Pow2 = "SuppRR3Pow2";

        internal const string m_XmlSuppRTC3StartupModes = "SuppRTC3StartupModes";

        internal const string m_XmlSwitchCaption = "Switch";

        internal const string m_XmlSwitchesCaption = "Switches";

        internal const string m_XmlSwitchTypeCaption = "SwitchType";

        internal const string m_XmlSwitchTypesCaption = "SwitchTypes";

        internal const string m_XmlSyncCaption = "Sync";

        internal const string m_XmlSyncDomFragActive = "SyncDomFragActive";

        internal const string m_XmlTimeCaption = "Time";

        internal const string m_XmlTopologyCaption = "Topology";

        internal const string m_XmlTrueText = "true";

        internal const string m_XmlTxDelayCaption = "TxDelay";

        internal const string m_XmlTxPeriodGroupCaption = "TxPeriodGroup";

        internal const string m_XmlTxPortMsgCaption = "TxLocalMsg";

        internal const string m_XmlTxPortsCaption = "TxPorts";

        internal const string m_XmlTxRxPeriodCaption = "TxRxPeriod";

        internal const string m_XmlTxRxPeriodsByPhaseCaption = "TxRxPeriodsByPhase";

        internal const string m_XmlTypeCaption = "Type";

        internal const string m_XmlUpdateTimeModeCaption = "UpdateTimeMode";

        internal const string m_XmlUseAverageBwMode = "UseAverageBwMode";

        internal const string m_XmlUsingShortPreambleCaption = "UsingShortPreamble";

        internal const string m_XmlWatchdogFactorCaption = "WatchdogFactor";

        internal const string m_XmlZeroText = "0";

        internal const string m_InputFrameId = "InputFrameId";

        internal const string m_InputMsgId = "InputMsgId";

        internal const string m_XmlDoubleQuoteText = "\"";

        // SFId (1 Byte), SFDataLength (1 Byte), SFCycleCounter (1 Byte), DataStatus (1 Byte), SFCRC16 (2 Byte)
        internal const int m_SubframeAppendix = 6;

        internal const string m_XmlAbsoluteText = "Absolute";

        internal const string m_XmlAdditionalLinkDelayCaption = "AdditionalLinkDelay";

        internal const string m_XmlAdvancedText = "Advanced";

        internal const string m_XmlAlignDFP_SubframesCaption = "AlignDFP_Subframes";

        internal const string m_XmlAssignedControllerCaption = "AssignedController";

        internal const string m_XmlByteLengthFactorCaption = "ByteLengthFactor";

        internal const string m_XmlCalculatedBridgeDelayCaption = "CalculatedBridgeDelay";

        internal const string m_XmlControllerCaption = "Controller";

        internal const string m_XmlCreateNRTFrames = "CreateNRTFrames";

        internal const string m_XmlCUText = "CU";

        internal const string m_XmlDebugInfoAllCaption = "DebugInfoAll";

        internal const string m_XmlDebugInfoCaption = "DebugInfo";

        internal const string m_XmlDelayCaption = "Delay";

        internal const string m_XmlDfpSupportedCaption = "DfpSupported";

        internal const string m_XmlDirectionCaption = "Direction";

        internal const string m_XmlDistributionMode = "DistributionMode";

        internal const string m_XmlFrameEquipartition = "FrameEquipartition";

        internal const string m_XmlByteEquipartition = "ByteEquipartition";

        internal const string m_XmlAutomatic = "Automatic";

        internal const string m_XmlFixedTime = "FixedTime";

        internal const string m_XmlFixedReduction = "FixedReduction";

        #region PNPlanner Output Constants

        internal const string m_XmlControllersCaption = "Controllers";

        internal const string m_XmlFramesCaption = "Frames";

        internal const string m_XmlFrameCaption = "Frame";

        internal const string m_XmlIrtSwitchesCaption = "IrtSwitches";

        internal const string m_XmlIrtSwitchCaption = "IrtSwitch";

        internal const string m_XmlControllerNameCaption = "ControllerName";

        internal const string m_XmlRtBandwidthCaption = "RTBandwidth";

        internal const string m_XmlIrtBandwidthCaption = "IRTBandwidth";

        internal const string m_XmlFrameIdCaption = "FrameId";

        internal const string m_XmlControllerLocalReductionRatioCaption = "ControllerLocalReductionRatio";

        internal const string m_XmlDeviceLocalReductionRatioCaption = "DeviceLocalReductionRatio";

        internal const string m_XmlControllerLocalPhaseCaption = "ControllerLocalPhase";

        internal const string m_XmlDeviceLocalPhaseCaption = "DeviceLocalPhase";

        internal const string m_XmlPossibleReductionRatiosCaption = "PossibleReductionRatios";

        internal const string PossibleSendClockFactorsText = "PossibleSendclockFactors";

        internal const string m_XmlSendClockFactorCaption = "SendclockFactor";

        internal const string m_XmlResultLogCaption = "Log";

        internal const string m_XmlResultNoCaption = "No";

        internal const string m_XmlResultTypeCaption = "Type";

        internal const string m_XmlResultMessageCaption = "Message";

        internal const string m_XmlResultAdditionalInfoCaption = "AdditionalInfo";

        internal const string m_XmlAddInfoCaption = "AddInfo";

        internal const string m_XmlFrameIDCaption = "FrameID";

        internal const string m_XmlValue = "Value";

        internal const string m_XmlDfpFrameCaption = "DfpSubFrame";

        internal const string m_XmlPackSize = "PackSize";

        #endregion

        internal static class AttributeValues
        {
            internal const string PNPNPlannerMinInterLsduGap = "1120";

            internal const string PNPNPlannerByteLengthFactor = "80";

            internal const string PNPNPlannerStartAlignedBorder = "true";

            internal const string PNPNPlannerAdditionalLinkDelay = "1000";

            internal const uint PNPNPlannerMaxFrameLength = 1440;
        }
    }
}