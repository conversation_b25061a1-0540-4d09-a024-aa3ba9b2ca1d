﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SnmpControlRecordHelper.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Text;

using PNConfigLib.HWCNBL.Networks.SNMP._Interfaces;

#endregion

namespace PNConfigLib.HWCNBL.Networks.SNMP
{
    /// <summary>
    /// Contains helper methods for creating SnmpControlRecord block.
    /// </summary>
    internal class SnmpControlRecordHelper : ISnmpControlRecordHelper
    {
        #region Constants

        private const int s_ByteAlignment = 4;

        #endregion

        #region Public methods

        /// <summary>
        /// Calculates the number of bytes needed to pad a byte array of length [dataLength]
        /// to align with blocks of [byteAlignment] bytes
        /// </summary>
        /// <param name="dataLength">Length of the array that needs to be padded</param>
        /// <param name="byteAlignment">Number of bytes in a block to align to. Must be greater than 0.</param>
        /// <returns>The number of bytes required to pad the record</returns>
        public int CalculatePadding(int dataLength, int byteAlignment = s_ByteAlignment)
        {
            if (byteAlignment <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(byteAlignment));
            }

            int bytesTo32Bit = dataLength % byteAlignment;

            return bytesTo32Bit > 0 ? byteAlignment - bytesTo32Bit : 0;
        }

        /// <summary>
        /// Creates a byte array from the communityName input string using ASCII encoding.
        /// </summary>
        /// <param name="communityName">The string to be converted into byte array</param>
        /// <returns>The converted byte array</returns>
        public byte[] CreateArrayFromCommunityName(string communityName)
        {
            if (string.IsNullOrEmpty(communityName))
            {
                throw new ArgumentNullException(nameof(communityName));
            }

            string readNormalized = communityName.Normalize(NormalizationForm.FormD);

            return Encoding.ASCII.GetBytes(readNormalized);
        }

        /// <summary>
        /// Creates a 2 byte long array from snmpEnabledValue and readWriteAccess depends on the given generic type parameter
        /// </summary>
        /// <param name="snmpEnabledValue">the snmpEnabledValue value to be converted to the 1nd bit</param>
        /// <param name="readWriteAccess">the readWriteAccess value to be converted to the 2nd bit</param>
        /// <returns>The converted byte array</returns>
        public byte[] CreateArrayFromControl(bool snmpEnabledValue, bool readWriteAccess)
        {
            int controlValue = Convert.ToUInt16(snmpEnabledValue) | (Convert.ToUInt16(readWriteAccess) << 1);

            byte[] controlArray = BitConverter.GetBytes(Convert.ToUInt16(controlValue));

            Array.Reverse(controlArray);

            return controlArray;
        }

        /// <summary>
        /// Creates a 2 byte long array for CIM from snmpEnabledValue and readWriteAccess
        /// depends on the given generic type parameter
        /// </summary>
        /// <param name="snmpEnabledValue">the snmpEnabledValue value to be converted to the 1nd bit</param>
        /// <param name="readWriteAccess">the readWriteAccess value to be converted to the 2nd bit</param>
        /// <returns>The converted byte array</returns>
        public byte[] CreateArrayFromControlForCim(bool snmpEnabledValue, bool readWriteAccess)
        {
            int controlValue = 0;

            if (snmpEnabledValue)
            {
                controlValue = 1 + Convert.ToUInt16(readWriteAccess);
            }

            byte[] controlArray = BitConverter.GetBytes(Convert.ToUInt16(controlValue));

            Array.Reverse(controlArray);

            return controlArray;
        }

        #endregion
    }
}