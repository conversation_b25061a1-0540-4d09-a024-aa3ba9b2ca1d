<Page x:Class="PNConfigTool.Views.Pages.DeviceCatalogPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      xmlns:converters="clr-namespace:PNConfigTool.Converters"
      xmlns:vm="clr-namespace:PNConfigTool.ViewModels"
      mc:Ignorable="d" 
      d:DesignHeight="650" d:DesignWidth="600"
      Title="设备目录">

    <Page.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        
        <!-- String to boolean converter (for checking if a string is not null or empty) -->
        <converters:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
        
        <!-- 设备树的HierarchicalDataTemplate -->
        <HierarchicalDataTemplate DataType="{x:Type vm:MainFamilyNode}" ItemsSource="{Binding Vendors}">
            <TextBlock Text="{Binding Name}" VerticalAlignment="Center" FontWeight="Bold"/>
        </HierarchicalDataTemplate>
        
        <HierarchicalDataTemplate DataType="{x:Type vm:VendorFamilyNode}" ItemsSource="{Binding ProductFamilies}">
            <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
        </HierarchicalDataTemplate>
        
        <HierarchicalDataTemplate DataType="{x:Type vm:ProductFamilyNode}" ItemsSource="{Binding Devices}">
            <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
        </HierarchicalDataTemplate>
        
        <HierarchicalDataTemplate DataType="{x:Type vm:GSDMLDeviceNode}" ItemsSource="{Binding AllChildren}">
            <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center"/>
        </HierarchicalDataTemplate>
        
        <HierarchicalDataTemplate DataType="{x:Type vm:DAPNode}" ItemsSource="{Binding Children}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding DisplayName}" VerticalAlignment="Center" Foreground="#0066CC"/>
            </StackPanel>
        </HierarchicalDataTemplate>
        
        <HierarchicalDataTemplate DataType="{x:Type vm:HeaderNode}" ItemsSource="{Binding Children}">
            <TextBlock Text="{Binding Title}" VerticalAlignment="Center" FontStyle="Italic"/>
        </HierarchicalDataTemplate>
        

        

        
        <!-- 样式资源 -->
        <Style x:Key="ExpanderToggleStyle" TargetType="ToggleButton">
            <Setter Property="Focusable" Value="False"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Width="16" Height="16" Background="Transparent">
                            <Path x:Name="ExpandPath" Fill="#FF595959" Stroke="#FF595959" 
                                  Data="M0,0 L0,6 L6,0 z" 
                                  HorizontalAlignment="Left" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="ExpandPath" Property="Data" Value="M0,0 L6,0 L3,6 z"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="TreeViewItemStyle" TargetType="{x:Type TreeViewItem}">
            <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
            <Setter Property="IsSelected" Value="{Binding IsSelected, Mode=TwoWay}"/>
            <Setter Property="Padding" Value="2,2,0,2"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=Header}" Value="{x:Type vm:DAPNode}">
                    <Setter Property="Cursor" Value="Hand"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Page.Resources>
    
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>  <!-- 标题 -->
            <RowDefinition Height="3*" MinHeight="300"/>  <!-- 设备目录树 -->
            <RowDefinition Height="1*" MinHeight="150"/>  <!-- 设备信息区域 -->
            <RowDefinition Height="Auto"/>  <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" Text="设备目录" FontSize="16" FontWeight="Bold" Margin="10,10,0,5"/>
        
        <!-- 设备目录树 -->
        <Grid Grid.Row="1" Margin="10,0,10,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <TreeView x:Name="DeviceTreeView" Grid.Row="0" BorderThickness="1" 
                     BorderBrush="{StaticResource BorderColor}"
                     SelectedItemChanged="DeviceTreeView_SelectedItemChanged"
                     ItemContainerStyle="{StaticResource TreeViewItemStyle}"
                     ItemsSource="{Binding MainFamilies}">
            </TreeView>
            
            <!-- 加载状态指示器 -->
            <TextBlock x:Name="StatusTextBlock" Grid.Row="1" Text="{Binding StatusMessage}" 
                      TextWrapping="Wrap" Margin="0,5,0,0" FontStyle="Italic" FontSize="12"/>
            <ProgressBar x:Name="StatusProgressBar" Grid.Row="1" Height="3" Margin="0,2,0,0" 
                        IsIndeterminate="True" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
        
        <!-- 设备信息区域 -->
        <Border Grid.Row="2" Background="#F2F2F2" BorderBrush="#E0E0E0" BorderThickness="1" Margin="10,0,10,10">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="订货号：" FontWeight="Bold" Margin="0,0,5,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="OrderNumberTextBlock" Text="" TextWrapping="Wrap" Margin="0,0,0,5"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="版本：" FontWeight="Bold" Margin="0,0,5,5"/>
                <TextBox Grid.Row="1" Grid.Column="1" x:Name="VersionTextBox" IsReadOnly="True" BorderThickness="1" Margin="0,0,0,5"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="说明：" FontWeight="Bold" Margin="0,0,5,5" VerticalAlignment="Top"/>
                <ScrollViewer Grid.Row="2" Grid.Column="1" VerticalScrollBarVisibility="Auto">
                    <TextBlock x:Name="DescriptionTextBlock" Text="" TextWrapping="Wrap" Margin="0,0,0,0"/>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Page> 