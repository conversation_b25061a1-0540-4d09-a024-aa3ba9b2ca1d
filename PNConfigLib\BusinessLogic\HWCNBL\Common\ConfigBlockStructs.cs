/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigBlockStructs.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Text;

using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common
{

    //##################################################################

    #region ARTableBlock

    /// <summary>
    /// </summary>
    internal class ARBlockStruct
    {
        //blockheader
        protected byte[] header = new byte[8];

        //list of subblocks
        protected IList<byte[]> listSubBlocks = new List<byte[]>();

        public ARBlockStruct()
        {
            //set defaults
            BlockLength = 8; //length of header 
            SetBlockVersion(256); //Version 1.0
        }

        public ARBlockStruct(byte[] _data)
        {
            Array.Copy(_data, 0, header, 0, 8);

            if (_data.Length > 8)
            {
                byte[] subSlotData = new byte[_data.Length - 8];
                Array.Copy(_data, 8, subSlotData, 0, _data.Length - 8);
                listSubBlocks.Add(subSlotData);
            }
        }

        public int BlockCount
        {
            set { BufferManager.Write16(header, 6, value); }
            get { return BufferManager.Read16(header, 6); }
        }

        private int BlockLength
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        private void SetBlockVersion(int blockVersion)
        {
            BufferManager.Write16(header, 2, blockVersion);
        }

        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>();

                data.AddRange(header);

                foreach (byte[] subblock in listSubBlocks)
                {
                    data.AddRange(subblock);
                }

                return data.ToArray();
            }
        }

        public virtual void AddSubblock(byte[] subblock)
        {
            if ((subblock != null)
                && (subblock.Length > 0))
            {
                listSubBlocks.Add(subblock);
                BlockCount++;
                BlockLength += subblock.Length;
            }
        }

        public void AlignmentBlock(int alignmentLength)
        {
            int alignment = BlockLength % alignmentLength != 0 ? alignmentLength - BlockLength % alignmentLength : 0;
            listSubBlocks.Add(new byte[alignment]);
            BlockLength += alignment;
        }

        public ArConfigApiStruct GetArBlockApiStruct(int idx)
        {
            return new ArConfigApiStruct(listSubBlocks[idx]);
        }
    }

    internal class IocrRelatedSubblockStruct
    {
        public IList<RelatedIOBlockStruct> listRelatedIOCS = new List<RelatedIOBlockStruct>();

        public IList<RelatedIOBlockStruct> listRelatedIOData = new List<RelatedIOBlockStruct>();

        public void SetRelatedIOCS(RelatedIOBlockStruct relatedIocs)
        {
            listRelatedIOCS.Add(relatedIocs);
        }

        public int RelatedIOCSCount
        {
            get { return listRelatedIOCS.Count; }
        }

        public void SetRelatedIOData(RelatedIOBlockStruct relatedIoData)
        {
            listRelatedIOData.Add(relatedIoData);
        }

        public int RelatedIODataCount
        {
            get { return listRelatedIOData.Count; }
        }
    }

    #region Configuration structs

    internal class ArConfigApiStruct : ARBlockStruct
    {
        public ArConfigApiStruct()
        {
        }

        public ArConfigApiStruct(byte[] _data)
        {
            Array.Copy(_data, 0, header, 0, 8);

            if (_data.Length > 8)
            {
                BlobCreator creator = new BlobCreator(_data) { Index = 8 };
                for (int i = 0; i < this.BlockCount; i++)
                {
                    ushort subBlockLength = (ushort)creator.DeSerializeShort();
                    creator.Index = creator.Index - 2;

                    byte[] subBlock = creator.DeSerializeByteArrayOfKnownSize(subBlockLength);
                    listSubBlocks.Add(subBlock);
                }
            }
        }

        public uint API
        {
            set { BufferManager.Write32(header, 0, value); }
            get { return BufferManager.Read32(header, 0); }
        }

        public ARConfigSlotStruct GetArConfigSlotStruct(int idx)
        {
            return new ARConfigSlotStruct(listSubBlocks[idx]);
        }
    }

    internal class ARConfigSlotStruct
    {
        private byte[] data = new byte[20];

        private IList<byte[]> listSubSlots = new List<byte[]>();

        public ARConfigSlotStruct()
        {
            SlotBlockLength = 20;
        }

        public ARConfigSlotStruct(byte[] _data)
        {
            Array.Copy(_data, 0, data, 0, 20);

            if (_data.Length > 20)
            {
                BlobCreator creator = new BlobCreator(_data) { Index = 20 };
                for (int i = 0; i < this.SubmoduleCount; i++)
                {
                    byte[] subSlotBlock = creator.DeSerializeByteArrayOfKnownSize(24);
                    listSubSlots.Add(subSlotBlock);
                }
            }
        }

        public int MaxSubmoduleNr
        {
            set { BufferManager.Write16(data, 14, value); }
            get { return BufferManager.Read16(data, 14); }
        }

        public uint ModuleIdentNr
        {
            get { return BufferManager.Read32(data, 8); }
            set { BufferManager.Write32(data, 8, value); }
        }

        public void SetModuleProperties(int moduleProperties)
        {
            BufferManager.Write16(data, 12, moduleProperties);
        }

        public int SlotBlockLength
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int SlotNumber
        {
            get { return BufferManager.Read16(data, 4); }
            set { BufferManager.Write16(data, 4, value); }
        }

        public int SubmoduleCount
        {
            set { BufferManager.Write16(data, 18, value); }
            get { return BufferManager.Read16(data, 18); }
        }

        public void SetSubmoduleDataBlockVersion(int submoduleDataBlockVersion)
        {
            BufferManager.Write16(data, 2, submoduleDataBlockVersion);
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);

                foreach (byte[] subblock in listSubSlots)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        public void AddSubmodule(byte[] subblock)
        {
            if ((subblock != null)
                && (subblock.Length > 0))
            {
                listSubSlots.Add(subblock);
                SlotBlockLength += subblock.Length;
                SubmoduleCount++;
            }
        }

        public ARConfigSubslotStruct GetARConfigSubSlotStruct(int idx)
        {
            return new ARConfigSubslotStruct(listSubSlots[idx]);
        }
    }

    internal class ARConfigSubslotStruct
    {
        //hidden attribute - will not be written in config data block only for api filtering
        public uint api;

        private byte[] data = new byte[12];

        private IList<ARConfigSubslotDataStruct> listSubDataBlocks = new List<ARConfigSubslotDataStruct>();

        public ARConfigSubslotStruct()
        {
        }

        public ARConfigSubslotStruct(byte[] _data)
        {
            Array.Copy(_data, 0, data, 0, 12);

            if (_data.Length > 12)
            {
                Debug.Assert(_data.Length == 24);
                if (_data.Length == 24)
                {
                    byte[] subSlotData = new byte[12];
                    Array.Copy(_data, 12, subSlotData, 0, 12);
                    listSubDataBlocks.Add(new ARConfigSubslotDataStruct(subSlotData));
                }
            }
        }

        public int BlockLength { get; private set; } = 12;

        public void SetDiscardIOXS(int discardIOXS)
        {
            BufferManager.WriteBitfieldBased8(data, 9, 5, 1, discardIOXS);
        }

        public void SetReduceInputSubmoduleDataLength(int reduceInputSubmoduleDataLength)
        {
            BufferManager.WriteBitfieldBased8(data, 9, 3, 1, reduceInputSubmoduleDataLength);
        }

        public void SetReduceOutputSubmoduleDataLength(int reduceOutputSubmoduleDataLength)
        {
            BufferManager.WriteBitfieldBased8(data, 9, 4, 1, reduceOutputSubmoduleDataLength);
        }

        public void SetSharedInputData(int sharedInputData)
        {
            BufferManager.WriteBitfieldBased8(data, 9, 2, 1, sharedInputData);
        }

        public uint SubmoduleIdentNr
        {
            set { BufferManager.Write32(data, 4, value); }
            get { return BufferManager.Read32(data, 4); }
        }

        public int SubmoduleProperties
        {
            set { BufferManager.WriteBitfieldBased16(data, 8, 7, 1, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public int SubmoduleType
        {
            set { BufferManager.WriteBitfieldBased8(data, 9, 0, 2, value); }
            get { return BufferManager.ReadBitfieldBased8(data, 9, 0, 2); }
        }

        public int SubslotNumber
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public void SetSuperordinatedLocked(int superordinatedLocked)
        {
            BufferManager.WriteBitfieldBased8(data, 9, 6, 1, superordinatedLocked);
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);

                foreach (ARConfigSubslotDataStruct subblock in listSubDataBlocks)
                {
                    block.AddRange(subblock.ToByteArray);
                }

                return block.ToArray();
            }
        }

        public void AddSubmoduleData(ARConfigSubslotDataStruct submoduleData)
        {
            listSubDataBlocks.Add(submoduleData);
            BlockLength += submoduleData.BlockLength;
        }
    }

    internal class ARConfigSubslotDataStruct
    {
        public ARConfigSubslotDataStruct()
        {
            SetIOPSLength(1);
            SetIOCSLength(1);
        }

        public ARConfigSubslotDataStruct(byte[] _data)
        {
            ToByteArray = _data;
        }

        public int BlockLength
        {
            get { return ToByteArray.Length; }
        }

        public void SetDataDescriptionType(int dataDescriptionType)
        {
            BufferManager.WriteBit(ToByteArray, 1, 0, dataDescriptionType);
        }

        public void SetDataLength(int dataLength)
        {
            BufferManager.Write16(ToByteArray, 2, dataLength);
        }

        public void SetIOCSLength(int iocsLength)
        {
            BufferManager.Write16(ToByteArray, 6, iocsLength);
        }

        public void SetIOPSLength(int iopsLength)
        {
            BufferManager.Write16(ToByteArray, 4, iopsLength);
        }

        public byte[] ToByteArray { get; } = new byte[12];
    }

    #endregion

    #region IsoMParameter Data structs

    internal class IsoMParameterStruct : ParameterDSStruct
    {
        private byte[] data = new byte[24];

        public IsoMParameterStruct()
        {
            ParaBlockType = 0x0204;
            ParaBlockVersion = 0x0100;

            AddSubblock(this.data);
        }

        public int ControllerApplicationCycleFactor
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int SlotNumber
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int SubslotNumber
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public int TimeDataCycle
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public uint TimeIOInput
        {
            set { BufferManager.Write32(data, 8, value); }
            get { return BufferManager.Read32(data, 8); }
        }

        public uint TimeIOInputValid
        {
            set { BufferManager.Write32(data, 16, value); }
            get { return BufferManager.Read32(data, 16); }
        }

        public uint TimeIOOutput
        {
            set { BufferManager.Write32(data, 12, value); }
            get { return BufferManager.Read32(data, 12); }
        }

        public uint TimeIOOutputValid
        {
            set { BufferManager.Write32(data, 20, value); }
            get { return BufferManager.Read32(data, 20); }
        }
    }

    #endregion

    #region IOCR Data structs

    internal class IocrEntryStruct
    {
        public IocrEntryStruct()
        {
            ToByteArray = new byte[40];
        }

        public IocrEntryStruct(byte[] dataToParse)
        {
            ToByteArray = dataToParse;
        }

        public int BlockLength
        {
            set { BufferManager.Write16(ToByteArray, 0, value); }
            get { return BufferManager.Read16(ToByteArray, 0); }
        }

        public int DataHoldFactor
        {
            set { BufferManager.Write16(ToByteArray, 34, value); }
            get { return BufferManager.Read16(ToByteArray, 34); }
        }

        public int DataLength
        {
            set { BufferManager.Write16(ToByteArray, 16, value); }
            get { return BufferManager.Read16(ToByteArray, 16); }
        }

        public void SetEthertype(int ethertype)
        {
            BufferManager.Write16(ToByteArray, 8, ethertype);
        }

        public int FrameID
        {
            set { BufferManager.Write16(ToByteArray, 18, value); }
            get { return BufferManager.Read16(ToByteArray, 18); }
        }

        public void SetFrameSendOffset(uint frameSendOffset)
        {
            BufferManager.Write32(ToByteArray, 28, frameSendOffset);
        }

        public void SetIOCRProperties(uint iocrProperties)
        {
            BufferManager.Write32(ToByteArray, 12, iocrProperties);
        }

        public void SetIOCRReference(int iocrReference)
        {
            BufferManager.Write16(ToByteArray, 6, iocrReference);
        }

        public void SetIOCRTAGHeader(int iocrTAGHeader)
        {
            BufferManager.Write16(ToByteArray, 38, iocrTAGHeader);
        }

        public int IOCRType
        {
            set { BufferManager.Write16(ToByteArray, 4, value); }
            get { return BufferManager.Read16(ToByteArray, 4); }
        }

        public int Phase
        {
            set { BufferManager.Write16(ToByteArray, 24, value); }
            get { return BufferManager.Read16(ToByteArray, 24); }
        }

        public int ReductionRatio
        {
            set { BufferManager.Write16(ToByteArray, 22, value); }
            get { return BufferManager.Read16(ToByteArray, 22); }
        }

        public int RTClass
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 15, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 15, 0, 4); }
        }

        public int SendClockFactor
        {
            set { BufferManager.Write16(ToByteArray, 20, value); }
            get { return BufferManager.Read16(ToByteArray, 20); }
        }

        public int Sequence
        {
            set { BufferManager.Write16(ToByteArray, 26, value); }
            get { return BufferManager.Read16(ToByteArray, 26); }
        }

        public byte[] ToByteArray { get; }

        public void SetVersion(int version)
        {
            BufferManager.Write16(ToByteArray, 2, version);
        }

        public int WatchDogFactor
        {
            set { BufferManager.Write16(ToByteArray, 32, value); }
            get { return BufferManager.Read16(ToByteArray, 32); }
        }
    }

    internal class IocrStruct
    {
        private byte[] data = new byte[40];

        private IList<byte[]> listIOCRSubblocks = new List<byte[]>();

        public IocrStruct()
        {
        }

        public IocrStruct(byte[] _data)
        {
            Array.Copy(_data, 0, data, 0, 40);

            if (_data.Length > 40)
            {
                byte[] ioCRSubBlock = new byte[_data.Length - 40];
                Array.Copy(_data, 40, ioCRSubBlock, 0, _data.Length - 40);
                listIOCRSubblocks.Add(ioCRSubBlock);
            }
        }

        public int BlockLength
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public void SetDataHoldFactor(int dataHoldFactor)
        {
            BufferManager.Write16(data, 34, dataHoldFactor);
        }

        public void SetDataLength(int dataLength)
        {
            BufferManager.Write16(data, 16, dataLength);
        }

        public int Ethertype
        {
            set { BufferManager.Write16(data, 8, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public void SetFrameID(int frameID)
        {
            BufferManager.Write16(data, 18, frameID);
        }

        public void SetFrameSendOffset(uint frameSendOffset)
        {
            BufferManager.Write32(data, 28, frameSendOffset);
        }

        public void SetIOCRProperties(uint iocrProperties)
        {
            BufferManager.Write32(data, 12, iocrProperties);
        }

        public int IOCRReference
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public int IOCRType
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public void SetIOCRUserPriority(int iocrUserPriority)
        {
            BufferManager.WriteBitfieldBased16(data, 38, 13, 3, iocrUserPriority);
        }

        public void SetIOCRVLANID(int iocrVLANID)
        {
            BufferManager.WriteBitfieldBased16(data, 38, 0, 12, iocrVLANID);
        }

        public void SetPhase(int phase)
        {
            BufferManager.Write16(data, 24, phase);
        }

        public int ReductionRatio
        {
            set { BufferManager.Write16(data, 22, value); }
            get { return BufferManager.Read16(data, 22); }
        }

        public void SetRTClass(int rtClass)
        {
            BufferManager.WriteBitfieldBased8(data, 15, 0, 4, rtClass);
        }

        public void SetSendClockFactor(int sendClockFactor)
        {
            BufferManager.Write16(data, 20, sendClockFactor);
        }

        public void SetSequence(int sequence)
        {
            BufferManager.Write16(data, 26, sequence);
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);

                foreach (byte[] subblock in listIOCRSubblocks)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        public int Version
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public int WatchDogFactor
        {
            get { return BufferManager.Read16(data, 32); }
            set { BufferManager.Write16(data, 32, value); }
        }

        public void AddSubblock(byte[] subblock)
        {
            listIOCRSubblocks.Add(subblock);
            BlockLength += subblock.Length;
        }

        public IocrSubblockStruct GetIoCrSubBlockStruct(int idx)
        {
            return new IocrSubblockStruct(listIOCRSubblocks[idx]);
        }

        public void SetIOCREntry(byte[] subblock)
        {
            BufferManager.WriteBuffer(data, BlockLength, subblock);
            BlockLength += subblock.Length;
        }
    }

    internal class IocrSubblockStruct
    {
        private int blockLength = 20;

        private byte[] data = new byte[20];

        private IList<byte[]> listAPISubblocks = new List<byte[]>();

        public IocrSubblockStruct()
        {
        }

        public IocrSubblockStruct(byte[] _data)
        {
            Array.Copy(_data, 0, data, 0, 20);

            Debug.Assert(APICount == 1);

            byte[] apiSubBlock = new byte[_data.Length - 20];
            Array.Copy(_data, 20, apiSubBlock, 0, _data.Length - 20);
            listAPISubblocks.Add(apiSubBlock);
        }

        public int APICount
        {
            set { BufferManager.Write16(data, 18, value); }
            get { return BufferManager.Read16(data, 18); }
        }

        public uint KRAMOffset
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);

                foreach (byte[] subblock in listAPISubblocks)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        public void AddAPI(byte[] subblock)
        {
            listAPISubblocks.Add(subblock);
            blockLength += subblock.Length;
        }

        public IocrApiSubblockStruct GetApiSubBlock(int idx)
        {
            return new IocrApiSubblockStruct(listAPISubblocks[idx]);
        }
    }

    internal class IocrApiSubblockStruct : IocrRelatedSubblockStruct
    {
        public IocrApiSubblockStruct()
        {
        }

        public IocrApiSubblockStruct(byte[] _data)
        {
            BlobCreator creator = new BlobCreator(_data);
            API = (uint)creator.DeSerializeInt();

            ushort cntIODataObjects = (ushort)creator.DeSerializeShort();
            for (int i = 0; i < cntIODataObjects; i++)
            {
                byte[] ioBlock = creator.DeSerializeByteArrayOfKnownSize(8);
                RelatedIOBlockStruct ioBlockStruct = new RelatedIOBlockStruct(ioBlock);
                listRelatedIOData.Add(ioBlockStruct);
            }

            creator.DeSerializeShort(); // reserved field

            for (int i = 0; i < cntIODataObjects; i++)
            {
                byte[] csBlock = creator.DeSerializeByteArrayOfKnownSize(8);
                RelatedIOBlockStruct ioBlockStruct = new RelatedIOBlockStruct(csBlock);
                listRelatedIOCS.Add(ioBlockStruct);
            }

            creator.DeSerializeShort(); // reserved field
        }

        public uint API { set; get; }

        public int BlockLength
        {
            get { return listRelatedIOData.Count * 8 + listRelatedIOCS.Count * 8 + 12; }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                byte[] _api = new byte[4];
                BufferManager.Write32(_api, 0, API);
                block.AddRange(_api);

                byte[] num = new byte[2];

                BufferManager.Write16(num, 0, listRelatedIOData.Count);
                block.AddRange(num);

                foreach (RelatedIOBlockStruct relatedIOData in listRelatedIOData)
                {
                    block.AddRange(relatedIOData.ToByteArray);
                }
                byte[] res = { 0, 0 };
                block.AddRange(res);

                BufferManager.Write16(num, 0, listRelatedIOCS.Count);
                block.AddRange(num);

                foreach (RelatedIOBlockStruct relatedIOCS in listRelatedIOCS)
                {
                    block.AddRange(relatedIOCS.ToByteArray);
                }
                block.AddRange(res);

                return block.ToArray();
            }
        }
    }

    internal class IocrRelatedSubblockStructV10 : IocrRelatedSubblockStruct
    {
        public IocrRelatedSubblockStructV10()
        {
        }

        public IocrRelatedSubblockStructV10(byte[] _data)
        {
            BlobCreator creator = new BlobCreator(_data);

            ushort cntIODataObjects = (ushort)creator.DeSerializeShort();
            for (int i = 0; i < cntIODataObjects; i++)
            {
                byte[] ioBlock = creator.DeSerializeByteArrayOfKnownSize(8);
                RelatedIOBlockStruct ioBlockStruct = new RelatedIOBlockStruct(ioBlock);
                listRelatedIOData.Add(ioBlockStruct);
            }

            creator.DeSerializeShort(); // reserved field

            for (int i = 0; i < cntIODataObjects; i++)
            {
                byte[] csBlock = creator.DeSerializeByteArrayOfKnownSize(8);
                RelatedIOBlockStruct ioBlockStruct = new RelatedIOBlockStruct(csBlock);
                listRelatedIOCS.Add(ioBlockStruct);
            }

            creator.DeSerializeShort(); // reserved field
        }

        public int BlockLength
        {
            get { return listRelatedIOData.Count * 8 + listRelatedIOCS.Count * 8 + 8; }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                byte[] res = { 0, 0 };
                block.AddRange(res);

                byte[] numIOD = new byte[2];
                numIOD[1] = Convert.ToByte(listRelatedIOData.Count, CultureInfo.InvariantCulture);
                block.AddRange(numIOD);

                foreach (RelatedIOBlockStruct relatedIOData in listRelatedIOData)
                {
                    block.AddRange(relatedIOData.ToByteArray);
                }

                block.AddRange(res);

                byte[] numIOCS = new byte[2];
                numIOCS[1] = Convert.ToByte(listRelatedIOCS.Count, CultureInfo.InvariantCulture);
                block.AddRange(numIOCS);

                foreach (RelatedIOBlockStruct relatedIOCS in listRelatedIOCS)
                {
                    block.AddRange(relatedIOCS.ToByteArray);
                }

                return block.ToArray();
            }
        }
    }

    internal class RelatedIOBlockStruct
    {
        public RelatedIOBlockStruct()
        {
        }

        public RelatedIOBlockStruct(byte[] _data)
        {
            Debug.Assert(_data.Length == 8);

            if (_data.Length == 8)
            {
                ToByteArray = _data;
            }
        }

        public void SetFrameOffset(int frameOffset)
        {
            BufferManager.WriteBitfieldBased16(ToByteArray, 4, 0, 11, frameOffset);
        }

        public void SetSlotNumber(int slotNumber)
        {
            BufferManager.Write16(ToByteArray, 0, slotNumber);
        }

        public void SetSubslotNumber(int subslotNumber)
        {
            BufferManager.Write16(ToByteArray, 2, subslotNumber);
        }

        public byte[] ToByteArray { get; } = new byte[8];
    }

    #endregion

    #region Address Table structs

    internal class AddressTableEntryStruct
    {
        public AddressTableEntryStruct()
        {
            ToByteArray = new byte[44];
            BlockLength = 44; //static block size
            ToByteArray[23] = 0xFF; //reserved
        }

        public AddressTableEntryStruct(byte[] dataBlock)
        {
            ToByteArray = dataBlock;
        }

        public uint AddressFlags
        {
            set { BufferManager.Write32(ToByteArray, 30, value); }
            get { return BufferManager.Read32(ToByteArray, 30); }
        }

        public int AlarmOBNr
        {
            set { BufferManager.Write8(ToByteArray, 24, value); }
            get { return BufferManager.Read8(ToByteArray, 24); }
        }

        public void SetAPIV100(uint apiV100)
        {
            BufferManager.Write32(ToByteArray, 38, apiV100);
        }

        public void SetAPIV102(uint apiV102)
        {
            BufferManager.Write32(ToByteArray, 40, apiV102);
        }

        public int BlockLength
        {
            set { BufferManager.Write16(ToByteArray, 0, value); }
            get { return BufferManager.Read16(ToByteArray, 0); }
        }

        public int ConsistencyReference
        {
            set { BufferManager.Write16(ToByteArray, 36, value); }
            get { return BufferManager.Read16(ToByteArray, 36); }
        }

        public int ConsistencyUnit
        {
            set { BufferManager.Write16(ToByteArray, 28, value); }
            get { return BufferManager.Read16(ToByteArray, 28); }
        }

        public int DSNr
        {
            set { BufferManager.Write16(ToByteArray, 12, value); }
            get { return BufferManager.Read16(ToByteArray, 12); }
        }

        public void SetIOCSReferenceV100(int iocsReferenceV100)
        {
            BufferManager.Write16(ToByteArray, 40, iocsReferenceV100);
        }

        public void SetIOCSReferenceV102(int iocsReferenceV102)
        {
            BufferManager.Write16(ToByteArray, 38, iocsReferenceV102);
        }

        public int IODataReference
        {
            set { BufferManager.Write16(ToByteArray, 34, value); }
            get { return BufferManager.Read16(ToByteArray, 34); }
        }

        public int IOIdentifier
        {
            set { BufferManager.WriteBit(ToByteArray, 2, 7, value); }
            get { return BufferManager.ReadBit(ToByteArray, 2, 7); }
        }

        public int LogicalAddress
        {
            set { BufferManager.WriteBitfieldBased16(ToByteArray, 2, 0, 15, value); }
            get { return BufferManager.ReadBitfieldBased16(ToByteArray, 2, 0, 15); }
        }

        public ushort LogicalAddressRaw
        {
            get { return (ushort)(LogicalAddress + (IOIdentifier != 0 ? 0x8000 : 0)); }
        }

        public int LogicalBaseAddress
        {
            set { BufferManager.WriteBit(ToByteArray, 33, 0, value); }
            get { return BufferManager.ReadBit(ToByteArray, 33, 0); }
        }

        public int ParameterDS
        {
            set { BufferManager.Write16(ToByteArray, 26, value); }
            get { return BufferManager.Read16(ToByteArray, 26); }
        }

        public int ConfigDistribution
        {
            set { BufferManager.WriteBit(ToByteArray, 33, 1, value); }
            get { return BufferManager.ReadBit(ToByteArray, 33, 1); }
        }

        public int SlotNumber
        {
            set { BufferManager.Write16(ToByteArray, 4, value); }
            get { return BufferManager.Read16(ToByteArray, 4); }
        }

        public int SubslotNumber
        {
            set { BufferManager.Write16(ToByteArray, 6, value); }
            get { return BufferManager.Read16(ToByteArray, 6); }
        }

        public byte[] ToByteArray { get; }

        public int TPANr
        {
            set { BufferManager.Write8(ToByteArray, 25, value); }
            get { return BufferManager.Read8(ToByteArray, 25); }
        }

        public int VirtualAddress
        {
            set { BufferManager.Write16(ToByteArray, 8, value); }
            get { return BufferManager.Read16(ToByteArray, 8); }
        }

        public int VirtualAddressIOCS
        {
            set { BufferManager.Write16(ToByteArray, 18, value); }
            get { return BufferManager.Read16(ToByteArray, 18); }
        }

        public int VirtualAddressIOCSLength
        {
            set { BufferManager.Write16(ToByteArray, 20, value); }
            get { return BufferManager.Read16(ToByteArray, 20); }
        }

        public int VirtualAddressIOPS
        {
            set { BufferManager.Write16(ToByteArray, 14, value); }
            get { return BufferManager.Read16(ToByteArray, 14); }
        }

        public int VirtualAddressIOPSLength
        {
            set { BufferManager.Write16(ToByteArray, 16, value); }
            get { return BufferManager.Read16(ToByteArray, 16); }
        }

        public int VirtualAddressLength
        {
            set { BufferManager.Write16(ToByteArray, 10, value); }
            get { return BufferManager.Read16(ToByteArray, 10); }
        }

        public void CopyData(byte[] dest)
        {
            Array.Copy(ToByteArray, dest, 44);
        }
    }

    #endregion

    #region Parameter Table structs

    internal class ParameterDataBlockStruct
    {
        private readonly byte[] data;

        private readonly IList<ParameterDatasetStruct> listSubBlocks = new List<ParameterDatasetStruct>();

        private readonly bool m_HasPrmId;

        public ParameterDataBlockStruct() : this(false)
        {
        }

        public ParameterDataBlockStruct(bool hasPrmID)
        {
            m_HasPrmId = hasPrmID;
            if (hasPrmID)
            {
                data = new byte[12];
                BlockLength = 12;
            }
            else
            {
                data = new byte[4];
                BlockLength = 4;
            }
        }

        public ParameterDataBlockStruct(byte[] _data)
        {
            m_HasPrmId = false;
            data = new byte[4];
            BlobCreator creator = new BlobCreator(_data);

            creator.BigEndian = true;
            BlockLength = creator.DeSerializeShort();
            ParamBlockCount = creator.DeSerializeShort();
            creator.BigEndian = false;

            for (int i = 0; i < ParamBlockCount; i++)
            {
                listSubBlocks.Add(new ParameterDatasetStruct(creator));
            }
        }

        public int BlockLength
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int ParamBlockCount
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        /// <summary>
        /// Gets/sets the PrmID. It has no effect if "hasPrmId" parameter of the constructor is false.
        /// </summary>
        public byte[] PrmID
        {
            set
            {
                if (m_HasPrmId)
                {
                    BufferManager.WriteBuffer(data, 4, value, 8);
                }
            }
            get { return m_HasPrmId ? BufferManager.ReadBuffer(data, 4, 8) : null; }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> _data = new List<byte>();

                _data.AddRange(data);

                foreach (ParameterDatasetStruct subblock in listSubBlocks)
                {
                    _data.AddRange(subblock.ToByteArray);
                }

                return _data.ToArray();
            }
        }

        public byte[] ToByteArrayNetto
        {
            get
            {
                List<byte> _data = new List<byte>();

                foreach (ParameterDatasetStruct subblock in listSubBlocks)
                {
                    _data.AddRange(subblock.ToByteArray);
                }

                return _data.ToArray();
            }
        }

        public void AddFirstParamDSBlock(ParameterDatasetStruct param)
        {
            listSubBlocks.Insert(0, param);
            ParamBlockCount++;
            BlockLength += param.ParaDSBlockLength;
        }

        public void AddParamDSBlock(ParameterDatasetStruct param)
        {
            listSubBlocks.Add(param);
            ParamBlockCount++;
            BlockLength += param.ParaDSBlockLength;
        }

        public ParameterDatasetStruct GetParamDataset(int idx)
        {
            return listSubBlocks[idx];
        }
    }

    internal class ParameterDatasetStruct
    {
        private byte[] data = new byte[8];

        private IList<byte[]> listSubBlocks = new List<byte[]>();

        public ParameterDatasetStruct()
        {
            ParaDSBlockLength = 8;
        }

        public ParameterDatasetStruct(BlobCreator creator)
        {
            creator.BigEndian = true;
            ParaDSBlockLength = (ushort)creator.DeSerializeShort();
            ParaDSLength = (ushort)creator.DeSerializeShort();
            ParaDSNumber = (ushort)creator.DeSerializeShort();
            ParaDSIdentifier = (ushort)creator.DeSerializeShort();
            creator.BigEndian = false;

            if (ParaDSLength > 0)
            {
                listSubBlocks.Add(creator.DeSerializeByteArrayOfKnownSize(ParaDSLength));
            }

            // Deserialize fillbytes for alignment
            if (ParaDSBlockLength - 8 > ParaDSLength)
            {
                creator.DeSerializeByteArrayOfKnownSize(ParaDSBlockLength - 8 - ParaDSLength);
            }
        }

        // construct Dataset, fill with data without 8 bytes DS header data
        public ParameterDatasetStruct(byte[] fillData)
        {
            Debug.Assert(fillData.Length > 8);

            ParaDSBlockLength = 8;
            int paramLength = fillData.Length - 8;
            byte[] param = new byte[paramLength];
            Array.Copy(fillData, 8, param, 0, paramLength);

            AddParaBlock(param);
        }

        public int ParaDSBlockLength
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int ParaDSIdentifier
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public int ParaDSLength
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public int ParaDSNumber
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int ParamSubBlockCount
        {
            get { return listSubBlocks.Count; }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> _data = new List<byte>();
                _data.AddRange(data);


                foreach (byte[] subblock in listSubBlocks)
                {
                    _data.AddRange(subblock);
                }

                return _data.ToArray();
            }
        }

        public void AddParaBlock(byte[] param)
        {
            listSubBlocks.Add(param);
            ParaDSLength += param.Length;
            ParaDSBlockLength += param.Length;
        }

        public void AlignDS(int alignmentLength)
        {
            int alignment = ParaDSBlockLength % alignmentLength != 0
                                ? alignmentLength - ParaDSBlockLength % alignmentLength
                                : 0;
            listSubBlocks.Add(new byte[alignment]);
            ParaDSBlockLength += alignment;
        }

        public byte[] GetParaBlock(int idx)
        {
            return listSubBlocks[idx];
        }
    }

    internal class ParameterDSStruct
    {
        //BlockType, BlockLength, BlockVersion, Padding, Padding
        protected byte[] header = new byte[8];

        private IList<byte[]> listSubBlocks = new List<byte[]>();

        public ParameterDSStruct()
        {
            // init value, the length of header without BlockType and BlockLength
            ParaBlockLength = 4;
        }

        public int ParaBlockLength
        {
            set { BufferManager.Write16(header, 2, value); }
            get { return BufferManager.Read16(header, 2); }
        }

        public int ParaBlockReserve
        {
            set { BufferManager.Write16(header, 6, value); }
            get { return BufferManager.Read16(header, 6); }
        }

        public int ParaBlockType
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        public int ParaBlockVersion
        {
            set { BufferManager.Write16(header, 4, value); }
            get { return BufferManager.Read16(header, 4); }
        }

        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>();

                data.AddRange(header);

                foreach (byte[] subblock in listSubBlocks)
                {
                    data.AddRange(subblock);
                }

                return data.ToArray();
            }
        }

        public void AddSubblock(byte[] param)
        {
            listSubBlocks.Add(param);
            ParaBlockLength += param.Length;
        }
    }

    internal class ParameterDSSubBlockStruct
    {
        //BlockType, BlockLength, BlockVersion
        protected byte[] header = new byte[6];

        private IList<byte[]> listSubBlocks = new List<byte[]>();

        public ParameterDSSubBlockStruct()
        {
            ParaBlockLength = 2; // init value, the length of header without BlockType and BlockLength 
        }

        public int ParaBlockLength
        {
            set { BufferManager.Write16(header, 2, value); }
            get { return BufferManager.Read16(header, 2); }
        }

        public int ParaBlockType
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        public int ParaBlockVersion
        {
            set { BufferManager.Write16(header, 4, value); }
            get { return BufferManager.Read16(header, 4); }
        }

        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>();
                data.AddRange(header);

                foreach (byte[] subblock in listSubBlocks)
                {
                    data.AddRange(subblock);
                }
                return data.ToArray();
            }
        }

        public void AddSubblock(byte[] param)
        {
            listSubBlocks.Add(param);
            ParaBlockLength += param.Length;
        }
    }

    /// <summary>
    /// Wrapper class for the Config122 format Config content
    /// Global format (to be generated or parsed),
    /// Moduleblock header       (6 byte)
    /// Header of first dataset  (6 byte / 8 byte)
    /// Header of second dataset (6 byte / 8 byte)
    /// Header of ...   dataset (6 byte / 8 byte)
    /// first dataset
    /// second dataset
    /// ...
    /// Moduleblock header (6 byte) format:
    /// 0: Reserve
    /// 1: TransferAttribute 
    /// 2: Reserve
    /// 3: Dataset count
    /// 4-5: ModuleType
    /// </summary>
    internal class ParameterSlotBlockStruct
    {
        private const int HeaderLength = 6;

        private byte[] m_header;

        private int m_totalLength;

        /// <summary>
        /// Create empty parameter slot block struct
        /// </summary>
        public ParameterSlotBlockStruct()
        {
            m_header = new byte[HeaderLength];
            Datasets = new List<ParameterSlotDatasetStruct>();
            ParamBlockCount = 0;
            m_totalLength = HeaderLength;
        }

        /// <summary>
        /// Create parameter slot block struct. This constructor parses the raw data, which
        /// contains the whole slot block in Config122 format
        /// </summary>
        /// <param name="parameterSlotBlockData">parsable raw slot block in Config122 format</param>
        /// <param name="useLongHeader">if <c>true</c>, then the long (GSD) header format is expected in the datasets</param>
        public ParameterSlotBlockStruct(byte[] parameterSlotBlockData, bool useLongHeader = false) : this()
        {
            if ((parameterSlotBlockData == null)
                || (parameterSlotBlockData.Length < 6))
            {
                return;
            }

            // parse slotblock header (but reset block count)
            Array.Copy(parameterSlotBlockData, 0, m_header, 0, HeaderLength);
            ParamBlockCount = 0;

            // parse datasets from full data array
            int currentHeaderOffset = HeaderLength;
            ParameterSlotDatasetStruct dataset;

            for (int parsedLength = HeaderLength;
                 parsedLength < parameterSlotBlockData.Length;
                 parsedLength += dataset.TotalLength)
            {
                #region Short / Long dataset selection logic

                if (!useLongHeader)
                {
                    dataset = new ParameterSlotDatasetStructShort();
                }
                else
                {
                    dataset = new ParameterSlotDatasetStructLong();
                }

                #endregion

                dataset.Parse(parameterSlotBlockData, currentHeaderOffset);

                AddDataset(dataset);
                currentHeaderOffset += dataset.HeaderLength;
            }
        }

        public IList<ParameterSlotDatasetStruct> Datasets { get; }

        public int ModuleType
        {
            set { BufferManager.Write16(m_header, 4, value); }
            get { return BufferManager.Read16(m_header, 4); }
        }

        public int ParamBlockCount
        {
            set { BufferManager.Write8(m_header, 3, value); }
            get { return BufferManager.Read8(m_header, 3); }
        }

        public int TransferAttribute
        {
            set { BufferManager.WriteBitfieldBased8(m_header, 1, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(m_header, 1, 0, 4); }
        }

        public void AddDataset(ParameterSlotDatasetStruct dataset)
        {
            ParamBlockCount++;
            Datasets.Add(dataset);
            m_totalLength += dataset.TotalLength;
        }

        /// <summary>
        /// Create raw byte array from headers + datasets (see format)
        /// </summary>
        /// <returns>raw slot block data</returns>
        public byte[] ToByteArray()
        {
            byte[] finalArray = new byte[m_totalLength];

            // copy block header
            Array.Copy(m_header, finalArray, HeaderLength);

            if (Datasets.Count > 0)
            {
                // Init the data offset with the block header and all dataset headers
                // The dataset header length is read from the first dataset
                int currentDataOffset = HeaderLength + Datasets.Count * Datasets[0].HeaderLength;
                for (int i = 0; i < Datasets.Count; i++)
                {
                    ParameterSlotDatasetStruct dataset = Datasets[i];

                    // Dataset offset must be set to the dataset explicitly.
                    // This can be done only here, because it depends on 
                    // the actual position of the dataset within param block
                    dataset.ParaDSOffset = currentDataOffset;

                    // copy dataset header to right position (block header + 'n'th header)
                    Array.Copy(
                        dataset.HeaderByteArray,
                        0,
                        finalArray,
                        HeaderLength + i * dataset.HeaderLength,
                        dataset.HeaderLength);

                    // copy dataset netto data to right position
                    Array.Copy(dataset.NettoDataByteArray, 0, finalArray, currentDataOffset, dataset.NettoLength);

                    currentDataOffset += dataset.NettoLength;
                }
            }

            return finalArray;
        }
    }

    /// <summary>
    /// Wrapper class for one dataset within parameter slot block (ParameterSlotBlockStruct)
    /// </summary>
    internal abstract class ParameterSlotDatasetStruct
    {
        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Default ctor, called only by derived classes
        /// </summary>
        protected ParameterSlotDatasetStruct(int headerLength)
        {
            m_headerByteArray = new byte[headerLength];
        }

        #endregion

        #region Public methods

        /// <summary>
        /// Save provided netto data to local array
        /// </summary>
        /// <param name="nettoData">netto data to be saved</param>
        /// <param name="datasetNumber">number of dataset</param>
        public void SetDataset(byte[] nettoData, int datasetNumber)
        {
            int alignLen = nettoData.Length + BufferManager.Alignment(nettoData.Length, 4);
            m_nettoDataByteArray = new byte[alignLen];
            Array.Copy(nettoData, m_nettoDataByteArray, nettoData.Length);

            ParaDSLength = nettoData.Length;
            ParaDSNumber = datasetNumber;
        }

        #endregion

        #region Private and protected methods

        /// <summary>
        /// Get netto data  from the Config122 structured raw byte array and store it in the dataset
        /// </summary>
        /// <param name="parameterSlotDatasetData">Config122 structured raw byte array</param>
        /// <param name="headerOffset">header offset of the dataset header</param>
        /// <param name="nettoDsLen">netto data length to be read</param>
        /// <param name="nettoDsNum">dataset number</param>
        protected void ParseNettoData(byte[] parameterSlotDatasetData, int headerOffset, int nettoDsLen, int nettoDsNum)
        {
            byte[] nettoData = new byte[nettoDsLen];
            int nettoDataOffset = BufferManager.Read8(parameterSlotDatasetData, headerOffset + 1);
            Array.Copy(parameterSlotDatasetData, nettoDataOffset, nettoData, 0, nettoDsLen);

            SetDataset(nettoData, nettoDsNum);
        }

        #endregion

        #region Fields

        protected byte[] m_headerByteArray;

        protected byte[] m_nettoDataByteArray;

        #endregion

        #region Overrides and Overridables

        /// <summary>
        /// Dataset header length
        /// </summary>
        public abstract int HeaderLength { get; }

        /// <summary>
        /// Dataset number
        /// </summary>
        public abstract int ParaDSNumber { get; set; }

        /// <summary>
        /// Dataset netto data lenght (real data length, without alignment)
        /// </summary>
        public abstract int ParaDSLength { get; set; }

        public abstract int ParaDSErstellungsKennung { get; set; }

        /// <summary>
        /// Parse a dataset from the Config122 slot block struct.
        /// </summary>
        /// <param name="parameterSlotDatasetData">slot block which contains a dataset</param>
        /// <param name="headerOffset">offset of the currently parsed dataset header</param>
        public abstract void Parse(byte[] parameterSlotDatasetData, int headerOffset);

        #endregion

        #region Properties

        public int ParaDSOffset
        {
            set { BufferManager.Write8(m_headerByteArray, 1, value); }
            get { return BufferManager.Read8(m_headerByteArray, 1); }
        }

        public int NettoLength
        {
            get { return m_nettoDataByteArray.Length; }
        }

        public int TotalLength
        {
            get { return m_headerByteArray.Length + m_nettoDataByteArray.Length; }
        }

        public byte[] HeaderByteArray
        {
            get { return m_headerByteArray; }
        }

        public byte[] NettoDataByteArray
        {
            get { return m_nettoDataByteArray; }
        }

        #endregion
    }

    /// <summary>
    /// Wrapper class for one dataset within parameter slot block (ParameterSlotBlockStruct)
    /// Header format (6 byte):
    /// 0: Static/Dynamic (S/D)
    /// 1: Netto data Offset
    /// 2: DSNum
    /// 3: DSLen
    /// 4: Reserve
    /// 5: Erstellungskennung
    /// </summary>
    internal class ParameterSlotDatasetStructShort : ParameterSlotDatasetStruct
    {
        #region Fields and Constants

        private const int c_headerLength = 6;

        #endregion

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Create empty dataset.
        /// Netto data can be added with dedicated method.
        /// </summary>
        public ParameterSlotDatasetStructShort() : base(c_headerLength)
        {
        }

        #endregion

        #region Overrides and Overridables

        public override int HeaderLength
        {
            get { return c_headerLength; }
        }

        public override int ParaDSNumber
        {
            set { BufferManager.Write8(m_headerByteArray, 2, value); }
            get { return BufferManager.Read8(m_headerByteArray, 2); }
        }

        public override int ParaDSLength
        {
            set { BufferManager.Write8(m_headerByteArray, 3, value); }
            get { return BufferManager.Read8(m_headerByteArray, 3); }
        }

        public override int ParaDSErstellungsKennung
        {
            set { BufferManager.Write8(m_headerByteArray, 5, value); }
            get { return BufferManager.Read8(m_headerByteArray, 5); }
        }

        /// <summary>
        /// Parse a dataset from the Config122 slot block struct.
        /// </summary>
        /// <param name="parameterSlotDatasetData">slot block which contains a dataset</param>
        /// <param name="headerOffset">offset of the currently parsed dataset header</param>
        public override void Parse(byte[] parameterSlotDatasetData, int headerOffset)
        {
            int nettoDsNum = BufferManager.Read8(parameterSlotDatasetData, headerOffset + 2);
            int nettoDsLen = BufferManager.Read8(parameterSlotDatasetData, headerOffset + 3);

            ParseNettoData(parameterSlotDatasetData, headerOffset, nettoDsLen, nettoDsNum);
        }

        #endregion
    }

    /// <summary>
    /// Wrapper class for one dataset within parameter slot block (ParameterSlotBlockStruct)
    /// Long header format (8 byte):
    /// 0: Static/Dynamic (S/D)
    /// 1: Netto data Offset
    /// 2-3: DSNum
    /// 4-5: DSLen
    /// 6: Reserve
    /// 7: Erstellungskennung
    /// </summary>
    internal class ParameterSlotDatasetStructLong : ParameterSlotDatasetStruct
    {
        #region Fields and Constants

        private const int c_headerLength = 8;

        #endregion

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Create empty dataset.
        /// Netto data can be added with dedicated method.
        /// </summary>
        public ParameterSlotDatasetStructLong() : base(c_headerLength)
        {
        }

        #endregion

        #region Overrides and Overridables

        public override int HeaderLength
        {
            get { return c_headerLength; }
        }

        public override int ParaDSNumber
        {
            set { BufferManager.Write16(m_headerByteArray, 2, value); }
            get { return BufferManager.Read16(m_headerByteArray, 2); }
        }

        public override int ParaDSLength
        {
            set { BufferManager.Write16(m_headerByteArray, 4, value); }
            get { return BufferManager.Read16(m_headerByteArray, 4); }
        }

        public override int ParaDSErstellungsKennung
        {
            set { BufferManager.Write8(m_headerByteArray, 7, value); }
            get { return BufferManager.Read8(m_headerByteArray, 7); }
        }

        /// <summary>
        /// Parse a dataset from the Config122 slot block struct.
        /// </summary>
        /// <param name="parameterSlotDatasetData">slot block which contains a dataset</param>
        /// <param name="headerOffset">offset of the currently parsed dataset header</param>
        public override void Parse(byte[] parameterSlotDatasetData, int headerOffset)
        {
            int nettoDsNum = BufferManager.Read16(parameterSlotDatasetData, headerOffset + 2);
            int nettoDsLen = BufferManager.Read16(parameterSlotDatasetData, headerOffset + 4);

            ParseNettoData(parameterSlotDatasetData, headerOffset, nettoDsLen, nettoDsNum);
        }

        #endregion
    }

    internal class PDSyncDataStruct : ParameterDSStruct
    {
        private byte[] data;

        private string domainName = "";

        public PDSyncDataStruct()
        {
            data = new byte[45];
            ParaBlockType = 515;
            ParaBlockLength = 49;
            ParaBlockVersion = 258;
        }

        public PDSyncDataStruct(byte[] syncBlock)
        {
            data = new byte[45];

            Debug.Assert(syncBlock.Length > 52);

            //it can be that the byte array contains the dataset information too(8 byte length)
            //depending on the length of the byte data we define our base indexes
            int subBlockStartPosConfig = 16;
            int domNameStartPosConfig = 61;

            int subBlockStartPos = 8;
            int domNameStartPos = 53;

            // syncBlock contains header bytes
            int lengthDomainName;
            byte[] domName;
            bool configUpload;

            // first try parse with Config position numbers
            if (configUpload = subBlockStartPosConfig + 45 <= syncBlock.Length)
            {
                Array.Copy(syncBlock, subBlockStartPosConfig, data, 0, 45);

                lengthDomainName = data[44];

                if (configUpload = domNameStartPosConfig + lengthDomainName <= syncBlock.Length)
                {
                    domName = new byte[lengthDomainName];

                    Array.Copy(syncBlock, domNameStartPosConfig, domName, 0, lengthDomainName);
                    domainName = Encoding.ASCII.GetString(domName);
                }
            }

            if (!configUpload)
            {
                Array.Copy(syncBlock, subBlockStartPos, data, 0, 45);

                lengthDomainName = data[44];
                domName = new byte[lengthDomainName];

                Array.Copy(syncBlock, domNameStartPos, domName, 0, lengthDomainName);
                domainName = Encoding.ASCII.GetString(domName);
            }

            ParaBlockType = 515;
            ParaBlockLength = 49;
            ParaBlockVersion = 258;
        }

        public uint PLLWindow
        {
            set { BufferManager.Write32(data, 24, value); }
            get { return BufferManager.Read32(data, 24); }
        }

        public int PTCPMasterPriority1
        {
            set { BufferManager.Write8(data, 42, value); }
            get { return BufferManager.Read8(data, 42); }
        }

        public int PTCPMasterPriority2
        {
            set { BufferManager.Write8(data, 43, value); }
            get { return BufferManager.Read8(data, 43); }
        }

        public int PTCPMasterStartupTime
        {
            set { BufferManager.Write16(data, 38, value); }
            get { return BufferManager.Read16(data, 38); }
        }

        public byte[] PTCPSubdomainID
        {
            set { BufferManager.WriteBuffer(data, 0, value); }
            get { return BufferManager.ReadBuffer(data, 0, 16); }
        }

        public int PTCPTakeoverTimeoutFactor
        {
            set { BufferManager.Write16(data, 36, value); }
            get { return BufferManager.Read16(data, 36); }
        }

        public int PTCPTimeoutFactor
        {
            set { BufferManager.Write16(data, 34, value); }
            get { return BufferManager.Read16(data, 34); }
        }

        public uint ReservedIntervalBegin
        {
            set { BufferManager.Write32(data, 16, value); }
            get { return BufferManager.Read32(data, 16); }
        }

        public uint ReservedIntervalEnd
        {
            set { BufferManager.Write32(data, 20, value); }
            get { return BufferManager.Read32(data, 20); }
        }

        public int SendClockFactor
        {
            set { BufferManager.Write16(data, 32, value); }
            get { return BufferManager.Read16(data, 32); }
        }

        public string SyncDomainName
        {
            set
            {
                domainName = value;
                SyncDomainNameLength = domainName.Length;
                int align = BufferManager.Alignment(ParaBlockLength + SyncDomainNameLength, 4);
                ParaBlockLength += SyncDomainNameLength + align;
            }
            get { return domainName; }
        }

        public int SyncDomainNameLength
        {
            set { BufferManager.Write8(data, 44, value); }
            get { return BufferManager.Read8(data, 44); }
        }

        public int SyncID
        {
            set { BufferManager.WriteBitfieldBased16(data, 40, 8, 5, value); }
            get { return BufferManager.ReadBitfieldBased16(data, 40, 8, 5); }
        }

        public int SyncProperties
        {
            set { BufferManager.Write16(data, 40, value); }
            get { return BufferManager.Read16(data, 40); }
        }

        public int SyncRole
        {
            set { BufferManager.WriteBitfieldBased16(data, 40, 0, 2, value); }
            get { return BufferManager.ReadBitfieldBased16(data, 40, 0, 2); }
        }

        public uint SyncSendFactor
        {
            set { BufferManager.Write32(data, 28, value); }
            get { return BufferManager.Read32(data, 28); }
        }

        public override byte[] ToByteArray
        {
            get
            {
                byte[] syncDomainName = Encoding.ASCII.GetBytes(domainName);

                List<byte> _data = new List<byte>();

                _data.AddRange(header);
                _data.AddRange(data);
                _data.AddRange(syncDomainName);

                int alignment = BufferManager.Alignment(_data.Count, 4);
                _data.AddRange(new byte[alignment]);

                return _data.ToArray();
            }
        }
    }

    internal class PdncDataCheckStruct : ParameterDSStruct
    {
        private byte[] data = new byte[12];

        public PdncDataCheckStruct()
        {
            this.ParaBlockType = 560;
            this.ParaBlockVersion = 256;

            AddSubblock(this.data);
        }

        public uint ErrorDropBudget
        {
            set { BufferManager.Write32(data, 8, value); }
            get { return BufferManager.Read32(data, 8); }
        }

        public uint MaintenanceDemandedDropBudget
        {
            set { BufferManager.Write32(data, 4, value); }
            get { return BufferManager.Read32(data, 4); }
        }

        public uint MaintenanceRequiredDropBudget
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }
    }

    internal class PdFsuDataAdjustStruct : ParameterDSStruct
    {
        //only header required
        public PdFsuDataAdjustStruct()
        {
            //set correct blocktype
            ParaBlockType = 0x0608;
            ParaBlockVersion = 256;
        }
    }

    internal class FSHelloBlockStruct : ParameterDSStruct
    {
        private byte[] data = new byte[16];

        public FSHelloBlockStruct()
        {
            //set correct blocktype
            this.ParaBlockType = 0x0600;
            this.ParaBlockVersion = 256;

            AddSubblock(this.data);
        }

        public FSHelloBlockStruct(byte[] dsBlock)
        {
            Debug.Assert(dsBlock.Length == 24);
            //get the data block without 8 Bytes header information
            Array.Copy(dsBlock, 8, this.data, 0, 16);
            AddSubblock(this.data);
        }

        public uint FSHelloDelay
        {
            set { BufferManager.Write32(data, 12, value); }
            get { return BufferManager.Read32(data, 12); }
        }

        public uint FSHelloInterval
        {
            set { BufferManager.Write32(data, 4, value); }
            get { return BufferManager.Read32(data, 4); }
        }

        public uint FSHelloMode
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }

        public uint FSHelloRetry
        {
            set { BufferManager.Write32(data, 8, value); }
            get { return BufferManager.Read32(data, 8); }
        }
    }

    internal class PDPortFODataAdjustStruct : ParameterDSStruct
    {
        private byte[] data = new byte[8];

        public PDPortFODataAdjustStruct()
        {
            this.ParaBlockType = 546;
            this.ParaBlockVersion = 256;

            AddSubblock(this.data);
        }

        public uint FiberOpticCableType
        {
            set { BufferManager.Write32(data, 4, value); }
            get { return BufferManager.Read32(data, 4); }
        }

        public uint FiberOpticType
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }
    }

    internal class PDPortFODataCheckStruct : ParameterDSStruct
    {
        private byte[] data = new byte[12];

        public PDPortFODataCheckStruct()
        {
            this.ParaBlockType = 547;
            this.ParaBlockVersion = 256;

            AddSubblock(this.data);
        }

        public uint ErrorPowerBudget
        {
            set { BufferManager.Write32(data, 8, value); }
            get { return BufferManager.Read32(data, 8); }
        }

        public uint MaintenanceDemandedPowerBudget
        {
            set { BufferManager.Write32(data, 4, value); }
            get { return BufferManager.Read32(data, 4); }
        }

        public uint MaintenanceRequiredPowerBudget
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }
    }

    internal class TlvParamSubBlockStruct : ParameterDSStruct
    {
        private byte[] data = new byte[4];

        public TlvParamSubBlockStruct()
        {
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int TLVParamSlotNumber
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int TLVParamSubSlotNumber
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }
    }

    #region DS 0x802B PDPortDataCheck structs

    internal class CheckLineDelaySubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[6];

        public CheckLineDelaySubBlockStruct()
        {
            this.ParaBlockType = 0x020B;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public uint LineDelay
        {
            set { BufferManager.Write32(data, 2, value); }
            get { return BufferManager.Read32(data, 2); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }
    }

    internal class CheckPeersSubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[1];

        public CheckPeersSubBlockStruct()
        {
            this.ParaBlockType = 0x020A;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int NumberOfPeerPorts
        {
            set { BufferManager.Write8(data, 0, value); }
            get { return BufferManager.Read8(data, 0); }
        }
    }

    internal class CheckMauTypeSubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[2];

        public CheckMauTypeSubBlockStruct()
        {
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int MAUType
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }
    }

    internal class CheckSyncDifferenceSubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[2];

        public CheckSyncDifferenceSubBlockStruct()
        {
            this.ParaBlockType = 0x021E;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public void SetCableDelay(int cableDelay)
        {
            BufferManager.WriteBit(data, 1, 0, cableDelay);
        }

        public void SetSyncMaster(int syncMaster)
        {
            BufferManager.WriteBit(data, 1, 1, syncMaster);
        }
    }

    #endregion

    #region DS 0x802F PDPortDataAdjust structs

    internal class AdjustMauTypeSubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[6];

        public AdjustMauTypeSubBlockStruct()
        {
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int AdjustProperties
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int MAUType
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }
    }

    internal class AdjustDomainBoundarySubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[14];

        public AdjustDomainBoundarySubBlockStruct()
        {
            this.ParaBlockType = 0x0209;
            this.ParaBlockVersion = 0x0101; // with BlockVersionLow = 1!!!
            AddSubblock(this.data);
        }

        public int AdjustProperties
        {
            set { BufferManager.Write16(data, 10, value); }
            get { return BufferManager.Read16(data, 10); }
        }

        public uint DomainBoundaryEgress
        {
            set { BufferManager.Write32(data, 6, value); }
            get { return BufferManager.Read32(data, 6); }
        }

        public uint DomainBoundaryIngress
        {
            set { BufferManager.Write32(data, 2, value); }
            get { return BufferManager.Read32(data, 2); }
        }

        public int EndPadding
        {
            set { BufferManager.Write16(data, 12, value); }
            get { return BufferManager.Read16(data, 12); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }
    }

    internal class AdjustPeerToPeerBoundarySubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[10];

        public AdjustPeerToPeerBoundarySubBlockStruct()
        {
            this.ParaBlockType = 0x0224;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int AdjustProperties
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public int EndPadding
        {
            set { BufferManager.Write16(data, 8, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public uint PeerToPeerBoundary
        {
            set { BufferManager.Write32(data, 2, value); }
            get { return BufferManager.Read32(data, 2); }
        }
    }

    internal class AdjustDcpBoundarySubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[10];

        public AdjustDcpBoundarySubBlockStruct()
        {
            this.ParaBlockType = 0x0225;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int AdjustProperties
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public uint DCPBoundary
        {
            set { BufferManager.Write32(data, 2, value); }
            get { return BufferManager.Read32(data, 2); }
        }

        public int EndPadding
        {
            set { BufferManager.Write16(data, 8, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }
    }

    internal class AdjustPreambleLengthSubBlockStruct : ParameterDSSubBlockStruct
    {
        private byte[] data = new byte[6];

        public AdjustPreambleLengthSubBlockStruct()
        {
            this.ParaBlockType = 0x0226;
            this.ParaBlockVersion = 0x0100;
            AddSubblock(this.data);
        }

        public int AdjustProperties
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int Padding
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int PreambleLength
        {
            set { BufferManager.WriteBitfieldBased16(data, 2, 0, 1, value); }
            get { return BufferManager.ReadBitfieldBased16(data, 2, 0, 1); }
        }
    }

    #endregion

    #region DS 0x8071 PDInterfaceAdjust structs

    internal class PDInterfaceAdjustStruct : ParameterDSStruct
    {
        private readonly byte[] m_Data;

        public PDInterfaceAdjustStruct()
        {
            m_Data = new byte[4];
            // init value, the length of header without BlockType and BlockLength
            ParaBlockLength = 4;
            ParaBlockType = 0x0250;
            ParaBlockVersion = 0x0100;
            AddSubblock(m_Data);
        }

        public int MultiInterfaceModeNameOfDevice
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 0, 1); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 0, 1, value); }
        }
    }

    #endregion

    #endregion

    #region Local SCF Adaption Table structs

    internal class LocalScfAdaptionEntryStruct
    {
        public LocalScfAdaptionEntryStruct()
        {
            BlockLength = 20;
        }

        public int BlockLength
        {
            set { BufferManager.Write16(ToByteArray, 0, value); }
            get { return BufferManager.Read16(ToByteArray, 0); }
        }

        public int IOCRReference
        {
            set { BufferManager.Write16(ToByteArray, 2, value); }
            get { return BufferManager.Read16(ToByteArray, 2); }
        }

        public int LocalDataHoldFactor
        {
            set { BufferManager.Write16(ToByteArray, 18, value); }
            get { return BufferManager.Read16(ToByteArray, 18); }
        }

        public uint LocalFrameSendOffset
        {
            set { BufferManager.Write32(ToByteArray, 12, value); }
            get { return BufferManager.Read32(ToByteArray, 12); }
        }

        public int LocalPhase
        {
            set { BufferManager.Write16(ToByteArray, 8, value); }
            get { return BufferManager.Read16(ToByteArray, 8); }
        }

        public int LocalReductionRatio
        {
            set { BufferManager.Write16(ToByteArray, 6, value); }
            get { return BufferManager.Read16(ToByteArray, 6); }
        }

        public int LocalSendClockFactor
        {
            set { BufferManager.Write16(ToByteArray, 4, value); }
            get { return BufferManager.Read16(ToByteArray, 4); }
        }

        public int LocalSequence
        {
            set { BufferManager.Write16(ToByteArray, 10, value); }
            get { return BufferManager.Read16(ToByteArray, 10); }
        }

        public int LocalWatchdogFactor
        {
            set { BufferManager.Write16(ToByteArray, 16, value); }
            get { return BufferManager.Read16(ToByteArray, 16); }
        }

        public byte[] ToByteArray { get; } = new byte[20];
    }

    #endregion

    #region AR Record Table structs

    internal class FSParameterBlockStruct : ParameterDSStruct
    {
        private byte[] data = new byte[20];

        public FSParameterBlockStruct()
        {
            //set correct blocktype
            ParaBlockType = 0x0601;
            ParaBlockLength = 4;
            ParaBlockVersion = 256;

            AddSubblock(data);
        }

        public uint FSParameterMode
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }

        public byte[] FSParameterUUID
        {
            set { BufferManager.WriteBuffer(data, 4, value); }
            get { return BufferManager.ReadBuffer(data, 4, 16); }
        }
    }

    #endregion

    #region IRInfoBlock structs

    internal class IRInfoBlockStructPlus : ParameterDSStruct
    {
        public IRInfoBlockStructBody irInfoStructBody = new IRInfoBlockStructBody();

        public IRInfoBlockStructPlus()
        {
            ParaBlockType = 0x3106;
            ParaBlockVersion = 256;
            ParaBlockLength = 24;
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>();
                data.AddRange(header);
                data.AddRange(irInfoStructBody.ToByteArray);

                return data.ToArray();
            }
        }

        /// <summary>
        /// Length of the entire structure block including overall block length.
        /// </summary>
        public void UpdateBlockLength()
        {
            foreach (DfpIocrEntryStruct dfpIOCREntry in irInfoStructBody.m_DfpIocrEntries)
            {
                ParaBlockLength += dfpIOCREntry.Length;
            }
        }
    }

    internal class IRInfoBlockStructBody
    {
        internal readonly IList<DfpIocrEntryStruct> m_DfpIocrEntries = new List<DfpIocrEntryStruct>();

        private readonly byte[] m_Data = new byte[20];

        public IRInfoBlockStructBody()
        {
            NumberOfDfpIOCRs = 0;
        }

        public byte[] IRDataUUID
        {
            set { BufferManager.WriteBuffer(m_Data, 0, value); }
            get { return BufferManager.ReadBuffer(m_Data, 0, 16); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>();
                data.AddRange(m_Data);
                foreach (DfpIocrEntryStruct dfpIocrEntry in m_DfpIocrEntries)
                {
                    data.AddRange(dfpIocrEntry.ToByteArray);
                }
                return data.ToArray();
            }
        }

        // Byte 20 - 22 : Reserved

        private ushort NumberOfDfpIOCRs
        {
            get { return (ushort)BufferManager.Read16(m_Data, 18); }
            set { BufferManager.Write16(m_Data, 18, value); }
        }

        public void AddDfpIocrEntry(DfpIocrEntryStruct dfpIOCREntry)
        {
            m_DfpIocrEntries.Add(dfpIOCREntry);
            NumberOfDfpIOCRs++;
        }
    }

    internal class DfpIocrEntryStruct
    {
        private readonly byte[] m_Data = new byte[4];

        public ushort IOCRReference
        {
            get { return (ushort)BufferManager.Read16(m_Data, 0); }
            set { BufferManager.Write16(m_Data, 0, value); }
        }

        public ushort Length
        {
            // Change if the length should be changed
            get { return 8; }
        }

        public SubframeData SubFrameData { get; } = new SubframeData();

        public ushort SubframeOffset
        {
            get { return (ushort)BufferManager.Read16(m_Data, 2); }
            set { BufferManager.Write16(m_Data, 2, value); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> data = new List<byte>(8);
                data.AddRange(m_Data);
                data.AddRange(SubFrameData.ToByteArray);
                return data.ToArray();
            }
        }
    }

    #endregion

    #region SubmoduleProperties structs

    internal class SubmodulePropertiesApiStruct : ARBlockStruct
    {
        public SubmodulePropertiesApiStruct()
        {
        }

        public SubmodulePropertiesApiStruct(byte[] _data)
        {
            Array.Copy(_data, 0, header, 0, 8);

            if (_data.Length > 8)
            {
                BlobCreator creator = new BlobCreator(_data) { Index = 8 };
                for (int i = 0; i < this.BlockCount; i++)
                {
                    ushort subBlockLength = (ushort)creator.DeSerializeShort();
                    creator.Index = creator.Index - 2;

                    byte[] subBlock = creator.DeSerializeByteArrayOfKnownSize(subBlockLength);
                    listSubBlocks.Add(subBlock);
                }
            }
        }

        public uint API
        {
            set { BufferManager.Write32(header, 0, value); }
            get { return BufferManager.Read32(header, 0); }
        }

        public SubmodulePropertiesSlotStruct GetSubmodulePropertiesSlotStruct(int idx)
        {
            return new SubmodulePropertiesSlotStruct(listSubBlocks[idx]);
        }
    }

    internal class SubmodulePropertiesSlotStruct
    {
        private byte[] data = new byte[8];

        private IList<byte[]> listSubSlots = new List<byte[]>();

        public SubmodulePropertiesSlotStruct()
        {
            SlotBlockLength = 8;
        }

        public SubmodulePropertiesSlotStruct(byte[] _data)
        {
            Array.Copy(_data, 0, data, 0, 8);

            if (_data.Length > 8)
            {
                BlobCreator creator = new BlobCreator(_data) { Index = 8 };
                for (int i = 0; i < this.SubmoduleCount; i++)
                {
                    byte[] subSlotBlock = creator.DeSerializeByteArrayOfKnownSize(12);
                    listSubSlots.Add(subSlotBlock);
                }
            }
        }

        public void SetModuleProperties(int moduleProperties)
        {
            BufferManager.Write16(data, 2, moduleProperties);
        }

        public int SlotBlockLength { get; set; }

        public int SlotNumber
        {
            get { return BufferManager.Read16(data, 0); }
            set { BufferManager.Write16(data, 0, value); }
        }

        public int SubmoduleCount
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);

                foreach (byte[] subblock in listSubSlots)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        public void AddSubmodule(byte[] subblock)
        {
            if ((subblock != null)
                && (subblock.Length > 0))
            {
                listSubSlots.Add(subblock);
                SubmoduleCount++;
                SlotBlockLength += subblock.Length;
            }
        }

        public SubmodulePropertiesSubslotStruct GetSubmodulePropertiesSubSlotStruct(int idx)
        {
            return new SubmodulePropertiesSubslotStruct(listSubSlots[idx]);
        }
    }

    internal class SubmodulePropertiesSubslotStruct
    {
        //hidden attribute - will not be written in config only for api filtering
        public uint api;

        private int blockLength = 12; //header

        public SubmodulePropertiesSubslotStruct()
        {
        }

        public SubmodulePropertiesSubslotStruct(byte[] _data)
        {
            Debug.Assert(_data.Length == 12, "The Length SubmodulePropertiesSubslotStruct is inappropriate!");
            Array.Copy(_data, 0, ToByteArray, 0, 12);
        }

        public int BlockLength
        {
            get { return blockLength; }
        }

        public int CombinedObjectContainerGroupNumber
        {
            set { BufferManager.Write8(ToByteArray, 3, value); }
            get { return BufferManager.Read8(ToByteArray, 3); }
        }

        public int IsAccessPoint
        {
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 2, 0, 1); }
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 2, 0, 1, value); }
        }

        public int SubslotNumber
        {
            set { BufferManager.Write16(ToByteArray, 0, value); }
            get { return BufferManager.Read16(ToByteArray, 0); }
        }

        public byte[] ToByteArray { get; } = new byte[12];
    }

    #endregion

    #endregion

    #region StationNameAliasTable

    internal class StationNameAliasEntryStruct
    {
        private string aliasName;

        private byte[] header = new byte[3];

        public string AliasName
        {
            set
            {
                aliasName = value;
                AliasNameLength = aliasName.Length;

                int alignment = BufferManager.Alignment(AliasNameLength + 3, 8);
                BlockLength += AliasNameLength + 3 + alignment;
            }
            get { return aliasName; }
        }

        public int AliasNameLength
        {
            set { BufferManager.Write8(header, 2, value); }
            get { return BufferManager.Read8(header, 2); }
        }

        public int BlockLength
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        public byte[] ToByteArray
        {
            get
            {
                byte[] stationAliasName = Encoding.ASCII.GetBytes(aliasName);

                List<byte> _data = new List<byte>();

                _data.AddRange(header);
                _data.AddRange(stationAliasName);

                int alignment = BufferManager.Alignment(_data.Count, 8);
                _data.AddRange(new byte[alignment]);

                return _data.ToArray();
            }
        }
    }

    #endregion

    internal class SendClock
    {
        protected byte[] header = new byte[6];

        private byte[] data = new byte[6];

        public SendClock()
        {
            ParaBlockType = 0xF000;
            ParaBlockLength = 0x0010;
            ParaBlockVersion = 0x0100;
        }

        public int ExtraData
        {
            set { BufferManager.Write8(data, 6, value); }
            get { return BufferManager.Read8(data, 6); }
        }

        public int ExtraDataLength
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int ParaBlockLength
        {
            set { BufferManager.Write16(header, 2, value); }
            get { return BufferManager.Read16(header, 2); }
        }

        public int ParaBlockType
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        public int ParaBlockVersion
        {
            set { BufferManager.Write16(header, 4, value); }
            get { return BufferManager.Read16(header, 4); }
        }

        public int SendClockFactor
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int SendClockProperties
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(header);

                block.AddRange(data);

                return block.ToArray();
            }
        }
    }

    internal class IRData
    {
        private byte[] header = new byte[16];

        private IList<byte[]> listRecordData = new List<byte[]>();

        public IRData()
        {
            TotalBlockLength = 16;
            BlockVersion = 0x0100;
            BlockLength = 8;
            DSNumber = 0x802C;
        }

        public int BlockLength
        {
            set { BufferManager.Write16(header, 8, value); }
            get { return BufferManager.Read16(header, 8); }
        }

        public int BlockVersion
        {
            set { BufferManager.Write16(header, 2, value); }
            get { return BufferManager.Read16(header, 2); }
        }

        public int CreationID
        {
            set { BufferManager.Write16(header, 14, value); }
            get { return BufferManager.Read16(header, 14); }
        }

        public int DSLength
        {
            set { BufferManager.Write16(header, 10, value); }
            get { return BufferManager.Read16(header, 10); }
        }

        public int DSNumber
        {
            set { BufferManager.Write16(header, 12, value); }
            get { return BufferManager.Read16(header, 12); }
        }

        public int SlotNumber
        {
            set { BufferManager.Write16(header, 4, value); }
            get { return BufferManager.Read16(header, 4); }
        }

        public int SubSlotNumber
        {
            set { BufferManager.Write16(header, 6, value); }
            get { return BufferManager.Read16(header, 6); }
        }

        public byte[] ToByteArray
        {
            get
            {
                // Fill to 16-Byte alignment
                ushort alignmentLength = (ushort)BufferManager.Alignment(TotalBlockLength, 16);
                TotalBlockLength += alignmentLength;

                List<byte> block = new List<byte>();
                block.AddRange(header);
                foreach (byte[] recordData in listRecordData)
                {
                    block.AddRange(recordData);
                }

                block.AddRange(new byte[alignmentLength]);

                // Subtract the alignment length again not to increase it everytime when the property is called.
                TotalBlockLength -= alignmentLength;
                return block.ToArray();
            }
        }

        public int TotalBlockLength
        {
            set { BufferManager.Write16(header, 0, value); }
            get { return BufferManager.Read16(header, 0); }
        }

        public void AddRecordData(byte[] recorddata)
        {
            listRecordData.Add(recorddata);
            TotalBlockLength += recorddata.Length;
            BlockLength += recorddata.Length;
            DSLength += recorddata.Length;
        }
    }

    #region IRData V0x0101

    internal class IRDataV101
    {
        private readonly byte[] m_Header = new byte[8];

        private readonly IList<byte[]> m_SubmoduleParameterBlocks = new List<byte[]>(1);

        public IRDataV101()
        {
            OverallBlockLength = 8;
            BlockVersion = 0x0101;
        }

        public IRDataV101(BlobCreator creator)
        {
            creator.BigEndian = true;
            //Byte 0-2 : Overall block length (interface block length)
            OverallBlockLength = (ushort)creator.DeSerializeShort();
            //Byte 2-4 : Blockversion 
            BlockVersion = (ushort)creator.DeSerializeShort();
            //Byte 4-6 : Reserved
            creator.Index += 2;
            //Byte 6-8 : Number of submodule parameter block entires
            NumberOfEntries = (ushort)creator.DeSerializeShort();
            creator.BigEndian = false;

            if (NumberOfEntries > 0)
            {
                for (int i = 0; i < NumberOfEntries; i++)
                {
                    //Collect submodule parameter blocks
                    ListParamDataBlocks.Add(new IRDataParameterBlock(creator));
                }
            }
        }

        public ushort BlockVersion
        {
            set { BufferManager.Write16(m_Header, 2, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 2); }
        }

        // Bytes 4 - 6 are reserved
        public ushort NumberOfEntries
        {
            set { BufferManager.Write16(m_Header, 6, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 6); }
        }

        public ushort OverallBlockLength
        {
            set { BufferManager.Write16(m_Header, 0, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 0); }
        }

        public byte[] ToByteArray
        {
            get
            {
                // Fill to 16-Byte alignment
                ushort alignmentLength = (ushort)BufferManager.Alignment(OverallBlockLength, 16);
                OverallBlockLength += alignmentLength;

                List<byte> block = new List<byte>();
                block.AddRange(m_Header);
                foreach (byte[] parameterBlock in m_SubmoduleParameterBlocks)
                {
                    block.AddRange(parameterBlock);
                }

                block.AddRange(new byte[alignmentLength]);

                // Subtract the alignment length again not to increase it everytime when the property is called.
                OverallBlockLength -= alignmentLength;
                return block.ToArray();
            }
        }

        //List of parameter blocks for submodules
        internal IList<IRDataParameterBlock> ListParamDataBlocks { get; } = new List<IRDataParameterBlock>();

        public void AddSubmoduleParameterBlock(byte[] parameterBlock)
        {
            m_SubmoduleParameterBlocks.Add(parameterBlock);
            OverallBlockLength += (ushort)parameterBlock.Length;
            NumberOfEntries++;
        }
    }

    internal class IRDataParameterBlock
    {
        private readonly IList<byte[]> m_DataRecords = new List<byte[]>();

        private readonly byte[] m_Header = new byte[24];

        public IRDataParameterBlock()
        {
            ParaBlockLength = 24;
        }

        public IRDataParameterBlock(BlobCreator creator)
        {
            creator.BigEndian = true;
            //Byte 0-2 (in paramBlock) : Block length
            ParaBlockLength = (ushort)creator.DeSerializeShort();
            //Byte 2-4 : Reserved
            creator.Index += 2;
            //Byte 4-8 : API 
            API = (uint)creator.DeSerializeInt();
            //Byte 8-10: SlotNumber
            SlotNumber = (ushort)creator.DeSerializeShort();
            //Byte 10-12: SubSlotNumber
            SubslotNumber = (ushort)creator.DeSerializeShort();
            //Byte 12-20: PrmID
            PrmID = (ulong)creator.DeSerializeLong();
            //Byte 20-22: Reserved
            creator.Index += 2;
            //Byte 22-24: Number of data records
            NumberOfDataRecords = (ushort)creator.DeSerializeShort();
            creator.BigEndian = false;

            if (NumberOfDataRecords > 0)
            {
                for (int j = 0; j < NumberOfDataRecords; j++)
                {
                    //Collect data records of parameter submodule block entires
                    ListDataRecords.Add(new ParameterDatasetStruct(creator));
                }
            }
        }

        // Bytes 2 - 4 are reserved
        public uint API
        {
            set { BufferManager.Write32(m_Header, 4, value); }
            get { return BufferManager.Read32(m_Header, 4); }
        }

        // Bytes 20 - 22 are reserved
        public ushort NumberOfDataRecords
        {
            set { BufferManager.Write16(m_Header, 22, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 22); }
        }

        public ushort ParaBlockLength
        {
            set { BufferManager.Write16(m_Header, 0, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 0); }
        }

        public ulong PrmID
        {
            set { BufferManager.Write64(m_Header, 12, value); }
            get { return BufferManager.Read64(m_Header, 12); }
        }

        public ushort SlotNumber
        {
            set { BufferManager.Write16(m_Header, 8, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 8); }
        }

        public ushort SubslotNumber
        {
            set { BufferManager.Write16(m_Header, 10, value); }
            get { return (ushort)BufferManager.Read16(m_Header, 10); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();
                block.AddRange(m_Header);
                foreach (byte[] dataset in m_DataRecords)
                {
                    block.AddRange(dataset);
                }
                return block.ToArray();
            }
        }

        //List of data records in parameter blocks for submodule
        internal IList<ParameterDatasetStruct> ListDataRecords { get; } = new List<ParameterDatasetStruct>();

        public void AddDataRecord(byte[] dataRecord)
        {
            m_DataRecords.Add(dataRecord);
            ParaBlockLength += (ushort)dataRecord.Length;
            NumberOfDataRecords++;
        }
    }

    #endregion

    internal class PdIrData : ParameterDSStruct
    {
        private byte[] data = new byte[4];

        private IList<byte[]> listRecordData = new List<byte[]>();

        public PdIrData()
        {
            ParaBlockType = 0x0205;
            ParaBlockLength = 8;
            ParaBlockVersion = 0x0101;
        }

        public int SlotNumber
        {
            set { BufferManager.Write16(data, 0, value); }
            get { return BufferManager.Read16(data, 0); }
        }

        public int SubSlotNumber
        {
            set { BufferManager.Write16(data, 2, value); }
            get { return BufferManager.Read16(data, 2); }
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(header);

                block.AddRange(data);

                foreach (byte[] recordData in listRecordData)
                {
                    block.AddRange(recordData);
                }

                return block.ToArray();
            }
        }

        public void AddRecordData(byte[] recorddata)
        {
            listRecordData.Add(recorddata);
            ParaBlockLength += recorddata.Length;
        }
    }

    internal class PdIrGlobalData : ParameterDSStruct
    {
        private byte[] data = new byte[24];

        private IList<byte[]> listRecordData = new List<byte[]>();

        public PdIrGlobalData()
        {
            this.ParaBlockLength = 28;
            this.ParaBlockType = 0x0206;
            this.ParaBlockVersion = 0x0101;
        }

        public uint BridgeDelay
        {
            set { BufferManager.Write32(data, 16, value); }
            get { return BufferManager.Read32(data, 16); }
        }

        public byte[] IRDataID
        {
            set { BufferManager.WriteBuffer(data, 0, value); }
            get { return BufferManager.ReadBuffer(data, 0, 16); }
        }

        public uint PortCount
        {
            set { BufferManager.Write32(data, 20, value); }
            get { return BufferManager.Read32(data, 20); }
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(header);

                block.AddRange(data);

                foreach (byte[] recordData in listRecordData)
                {
                    block.AddRange(recordData);
                }

                return block.ToArray();
            }
        }

        /// <summary>
        /// Port Data for PDIRGlobalData with version smaller than or equal to 0x101.
        /// Use the variant with maxLineRxDelay and yellowTime for newer versions.
        /// </summary>
        /// <param name="maxPortTxDelay"></param>
        /// <param name="maxPortRxDelay"></param>
        public void AddPortData(uint maxPortTxDelay, uint maxPortRxDelay)
        {
            Debug.Assert(this.ParaBlockVersion <= 0x0101, "Fill the port data for newer versions of PDIRGlobalData.");
            byte[] portData = new byte[8];

            BufferManager.Write32(portData, 0, maxPortTxDelay);
            BufferManager.Write32(portData, 4, maxPortRxDelay);

            listRecordData.Add(portData);
            PortCount++;
            ParaBlockLength += 8;
        }

        /// <summary>
        /// Port Data for PDIRGlobalData with version greater than or equal to 0x102.
        /// Use the variant without maxLineRxDelay and yellowTime for older versions.
        /// </summary>
        /// <param name="maxPortTxDelay"></param>
        /// <param name="maxPortRxDelay"></param>
        /// <param name="maxLineRxDelay"></param>
        /// <param name="yellowTime"></param>
        public void AddPortData(uint maxPortTxDelay, uint maxPortRxDelay, uint maxLineRxDelay, uint yellowTime)
        {
            Debug.Assert(this.ParaBlockVersion >= 0x0102, "Fill the port data for older versions of PDIRGlobalData.");

            byte[] portData = new byte[16];

            BufferManager.Write32(portData, 0, maxPortTxDelay);
            BufferManager.Write32(portData, 4, maxPortRxDelay);
            BufferManager.Write32(portData, 8, maxLineRxDelay);
            BufferManager.Write32(portData, 12, yellowTime);

            listRecordData.Add(portData);
            PortCount++;
            ParaBlockLength += 16;
        }

        public void ByteArrayToStruct(byte[] byteArray)
        {
            //data
            data = new byte[byteArray.Length - header.Length];
            Array.Copy(byteArray, header.Length, data, 0, byteArray.Length - header.Length);
            //header
            Array.Copy(byteArray, 0, header, 0, header.Length);
        }

        /// <summary>
        /// </summary>
        /// <param name="index"> </param>
        /// <param name="maxPortTxDelay"></param>
        /// <param name="maxPortRxDelay"></param>
        public void GetPortData(int index, out int maxPortTxDelay, out int maxPortRxDelay)
        {
            if (index + 8 > data.Length)
            {
                maxPortTxDelay = 0;
                maxPortRxDelay = 0;
                return;
            }

            maxPortTxDelay = Convert.ToInt32(BufferManager.Read32(data, index), CultureInfo.InvariantCulture);
            maxPortRxDelay = Convert.ToInt32(BufferManager.Read32(data, index + 4), CultureInfo.InvariantCulture);
        }
    }

    /// <summary>
    /// PDIRFrameData.FrameDataProperties
    /// Must be generated for PDIRFrameData version > 0x100
    /// </summary>
    internal class PdIrFrameDataProperties
    {
        public int FastForwardingMulticastMACAdd
        {
            set { BufferManager.WriteBitfieldBased32(ToByteArray, 0, 1, 2, value); }
            get { return BufferManager.ReadBitfieldBased32(ToByteArray, 0, 1, 2); }
        }

        public int ForwardingMode
        {
            set { BufferManager.WriteBitfieldBased32(ToByteArray, 0, 0, 1, value); }
            get { return BufferManager.ReadBitfieldBased32(ToByteArray, 0, 0, 1); }
        }

        public int FragmentationMode
        {
            set { BufferManager.WriteBitfieldBased32(ToByteArray, 0, 3, 3, value); }
            get { return BufferManager.ReadBitfieldBased32(ToByteArray, 0, 3, 3); }
        }

        public byte[] ToByteArray { get; private set; } = new byte[4];

        public void ByteArrayToStruct(byte[] byteArray)
        {
            //data
            ToByteArray = new byte[4];
            Array.Copy(byteArray, 0, ToByteArray, 0, 4);
        }
    }

    /// <summary>
    /// Main part of the PDIRFrameData. Should be generated for each frame.
    /// If PDIRFrameData version > 0x100, PDIRFrameDataProperties must also be generated.
    /// </summary>
    internal class PdIrFrameDataMain
    {
        private byte[] data = new byte[17];

        private IList<PdIrFrameTxPortGroups> portsData = new List<PdIrFrameTxPortGroups>();

        public int DataLength
        {
            set { BufferManager.Write16(data, 4, value); }
            get { return BufferManager.Read16(data, 4); }
        }

        public int DisjoinedPaths
        {
            set { BufferManager.WriteBitfieldBased8(data, 15, 4, 1, value); }
            get { return BufferManager.ReadBitfieldBased8(data, 15, 4, 1); }
        }

        public int EtherType
        {
            set { BufferManager.Write16(data, 12, value); }
            get { return BufferManager.Read16(data, 12); }
        }

        public int FrameDetails
        {
            set { BufferManager.Write8(data, 15, value); }
            get { return BufferManager.Read8(data, 15); }
        }

        public int FrameID
        {
            set { BufferManager.Write16(data, 10, value); }
            get { return BufferManager.Read16(data, 10); }
        }

        public uint FrameSendOffset
        {
            set { BufferManager.Write32(data, 0, value); }
            get { return BufferManager.Read32(data, 0); }
        }

        public int MeaningFrameSendOffset
        {
            set { BufferManager.WriteBitfieldBased8(data, 15, 2, 2, value); }
            get { return BufferManager.ReadBitfieldBased8(data, 15, 2, 2); }
        }

        public int MediaRedundancyWatchdog
        {
            set { BufferManager.WriteBitfieldBased8(data, 15, 7, 1, value); }
            get { return BufferManager.ReadBitfieldBased8(data, 15, 7, 1); }
        }

        public int NumTxPortGroups
        {
            set { BufferManager.Write8(data, 16, value); }
            get { return BufferManager.Read8(data, 16); }
        }

        public int Phase
        {
            set { BufferManager.Write16(data, 8, value); }
            get { return BufferManager.Read16(data, 8); }
        }

        public int ReductionRatio
        {
            set { BufferManager.Write16(data, 6, value); }
            get { return BufferManager.Read16(data, 6); }
        }

        public int RxPort
        {
            set { BufferManager.Write8(data, 14, value); }
            get { return BufferManager.Read8(data, 14); }
        }

        public int SyncFrame
        {
            set { BufferManager.WriteBitfieldBased8(data, 15, 0, 2, value); }
            get { return BufferManager.ReadBitfieldBased8(data, 15, 0, 2); }
        }

        public byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(data);
                foreach (PdIrFrameTxPortGroups portData in portsData)
                {
                    block.AddRange(portData.ToByteArray);
                }
                // Add padding for unsigned integer 32 alignment
                int paddingByteCount = 4 - (data.Length + portsData.Count) % 4;
                if (paddingByteCount != 4)
                {
                    block.AddRange(new byte[paddingByteCount]);
                }

                return block.ToArray();
            }
        }

        public void AddPortData(PdIrFrameTxPortGroups portData)
        {
            portsData.Add(portData);
        }
    }

    internal class PdIrFrameTxPortGroups
    {
        public int Local
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 0, 0, 1, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 0, 0, 1); }
        }

        public byte[] ToByteArray { get; } = new byte[1];

        /// <summary>
        /// Gets the list of ports and converts it to the bitcoded Config format.
        /// </summary>
        /// <param name="ports"></param>
        public void SetPorts(IList<int> ports)
        {
            foreach (int port in ports)
            {
                BufferManager.WriteBitfieldBased8(ToByteArray, 0, port % 8, 1, 1);
            }
        }
    }

    internal class PdIrBeginEndData : ParameterDSStruct
    {
        private IList<byte[]> data = new List<byte[]>();

        public PdIrBeginEndData()
        {
            ParaBlockLength = 4;
            ParaBlockType = 0x0208;
            ParaBlockVersion = 0x0100;
        }

        public void SetAssignmentCount(uint assignmentCount)
        {
            byte[] temp = new byte[4];
            BufferManager.Write32(temp, 0, assignmentCount);

            data.Add(temp);
            ParaBlockLength += 4;
        }

        public void SetEndOfReqFrameID(int endOfReqFrameID)
        {
            byte[] temp = new byte[2];
            BufferManager.Write16(temp, 0, endOfReqFrameID);

            data.Add(temp);
            ParaBlockLength += 2;
        }

        public void SetPhaseCount(uint phaseCount)
        {
            byte[] temp = new byte[4];
            BufferManager.Write32(temp, 0, phaseCount);

            data.Add(temp);
            ParaBlockLength += 4;
        }

        public void SetPortCount(uint portCount)
        {
            byte[] temp = new byte[4];
            BufferManager.Write32(temp, 0, portCount);

            data.Add(temp);
            ParaBlockLength += 4;
        }

        public void SetStartOfReqFrameID(int startOfReqFrameID)
        {
            byte[] temp = new byte[2];
            BufferManager.Write16(temp, 0, startOfReqFrameID);

            data.Add(temp);
            ParaBlockLength += 2;
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();

                block.AddRange(header);

                foreach (byte[] d in data)
                {
                    block.AddRange(d);
                }

                return block.ToArray();
            }
        }

        public void Add(byte[] recorddata)
        {
            data.Add(recorddata);
            ParaBlockLength += recorddata.Length;
        }
    }

    internal class PdIrBeginEndAssignment
    {
        public uint RX_OrangePeriodBegin
        {
            set { BufferManager.Write32(ToByteArray, 16, value); }
            get { return BufferManager.Read32(ToByteArray, 16); }
        }

        public uint RX_RedOrangePeriodEnd
        {
            set { BufferManager.Write32(ToByteArray, 20, value); }
            get { return BufferManager.Read32(ToByteArray, 20); }
        }

        public uint RX_RedPeriodBegin
        {
            set { BufferManager.Write32(ToByteArray, 12, value); }
            get { return BufferManager.Read32(ToByteArray, 12); }
        }

        public byte[] ToByteArray { get; } = new byte[24];

        public uint TX_OrangePeriodBegin
        {
            set { BufferManager.Write32(ToByteArray, 4, value); }
            get { return BufferManager.Read32(ToByteArray, 4); }
        }

        public uint TX_RedOrangePeriodEnd
        {
            set { BufferManager.Write32(ToByteArray, 8, value); }
            get { return BufferManager.Read32(ToByteArray, 8); }
        }

        public uint TX_RedPeriodBegin
        {
            set { BufferManager.Write32(ToByteArray, 0, value); }
            get { return BufferManager.Read32(ToByteArray, 0); }
        }
    }

    internal class PdIrPhaseAssignment
    {
        public int RX_OrangeBegin
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 3, 4, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 3, 4, 4); }
        }

        public int RX_Phase
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 2, 4, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 2, 4, 4); }
        }

        public int RX_ReservedBegin
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 3, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 3, 0, 4); }
        }

        public int RX_ReservedEnd
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 2, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 2, 0, 4); }
        }

        public byte[] ToByteArray { get; } = new byte[4];

        public int TX_OrangeBegin
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 1, 4, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 1, 4, 4); }
        }

        public int TX_Phase
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 0, 4, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 0, 4, 4); }
        }

        public int TX_ReservedBegin
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 1, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 1, 0, 4); }
        }

        public int TX_ReservedEnd
        {
            set { BufferManager.WriteBitfieldBased8(ToByteArray, 0, 0, 4, value); }
            get { return BufferManager.ReadBitfieldBased8(ToByteArray, 0, 0, 4); }
        }
    }

    #region DFP Related Blocks

    /// <summary>
    /// PDIRSubframeData block. It doesn't contain 2 reserved block in its header, 
    /// so it extends ParameterDSSubblockStruct instead of ParameterDSStruct.
    /// </summary>
    internal class PdirSubframeData : ParameterDSSubBlockStruct
    {
        private readonly byte[] m_Data = new byte[2];
        private readonly IList<byte[]> m_SubframeBlocks = new List<byte[]>();

        public PdirSubframeData()
        {
            ParaBlockLength = 4;
            ParaBlockType = 0x022A;
            ParaBlockVersion = 0x0100;
            NumberOfSubframeBlocks = 0;
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();
                block.AddRange(header);
                block.AddRange(m_Data);
                foreach (byte[] subframeBlock in m_SubframeBlocks)
                {
                    block.AddRange(subframeBlock);
                }
                return block.ToArray();
            }
        }

        internal int NumberOfSubframeBlocks
        {
            private set { BufferManager.Write16(m_Data, 0, value); }
            get { return BufferManager.Read16(m_Data, 0); }
        }

        public void AddSubframeBlock(SubframeBlock subframeBlock)
        {
            byte[] arr = subframeBlock.ToByteArray;
            m_SubframeBlocks.Add(arr);
            NumberOfSubframeBlocks++;
            ParaBlockLength += arr.Length;
        }
    }

    internal class SubframeBlock : ParameterDSSubBlockStruct
    {
        private readonly byte[] m_Data = new byte[2];
        private readonly SfiocrProperties m_Props = new SfiocrProperties();
        private readonly IList<byte[]> m_SubframeDatas = new List<byte[]>();

        public SubframeBlock()
        {
            ParaBlockLength = 8; // version, sfiocrprops and frameid length
            ParaBlockType = 0x022B;
            ParaBlockVersion = 0x0100;
        }

        public override byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();
                block.AddRange(header);
                block.AddRange(m_Data);
                block.AddRange(Properties.ToByteArray);
                foreach (byte[] subframeData in m_SubframeDatas)
                {
                    block.AddRange(subframeData);
                }
                return block.ToArray();
            }
        }

        public int FrameID
        {
            set { BufferManager.Write16(m_Data, 0, value); }
            get { return BufferManager.Read16(m_Data, 0); }
        }

        public SfiocrProperties Properties
        {
            get { return m_Props; }
        }

        public void AddSubframeData(SubframeData subframeData)
        {
            byte[] arr = subframeData.ToByteArray;
            m_SubframeDatas.Add(arr);
            ParaBlockLength += arr.Length;
        }
    }

    internal class SfiocrProperties
    {
        private readonly byte[] m_Data = new byte[4];

        public ICollection<byte> ToByteArray
        {
            get { return m_Data; }
        }

        // Bits 0 - 7
        public int DistributedWatchDogFactor
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 0, 8); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 0, 8, value); }
        }

        // ReSharper disable InconsistentNaming
        // Bits 8 - 15
        public int RestartFactorForDistributedWD
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 8, 8); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 8, 8, value); }
        }
        // ReSharper restore InconsistentNaming

        // Bits 16 - 23
        public int DFPMode
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 16, 8); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 16, 8, value); }
        }

        // Bits 24 - 28 are reserved.

        // Bit 29 
        public int DFPType
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 29, 1); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 29, 1, value); }
        }

        // Bit 30
        public int DFPRedundantPathLayout
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 30, 1); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 30, 1, value); }
        }

        // ReSharper disable InconsistentNaming
        // Bit 31
        public int SFCRC16
        {
            get { return BufferManager.ReadBitfieldBased32(m_Data, 0, 31, 1); }
            set { BufferManager.WriteBitfieldBased32(m_Data, 0, 31, 1, value); }
        }
        // ReSharper restore InconsistentNaming
    }

    internal class SubframeData
    {
        // Bit 7 is reserved

        // Bits 8 - 15
        public int DataLength
        {
            set { BufferManager.WriteBitfieldBased32(ToByteArray, 0, 8, 8, value); }
            get { return BufferManager.ReadBitfieldBased32(ToByteArray, 0, 8, 8); }
        }

        // Bits 0 - 6
        public int Position
        {
            set { BufferManager.WriteBitfieldBased32(ToByteArray, 0, 0, 6, value); }
            get { return BufferManager.ReadBitfieldBased32(ToByteArray, 0, 0, 6); }
        }

        public byte[] ToByteArray { get; } = new byte[4];

        // Bits 16 - 31 are reserved
    }

    #endregion

    internal class IdentificationBlock : ParameterDSSubBlockStruct
    {
        private readonly byte[] m_Data = new byte[12];

        public IdentificationBlock()
        {
            ParaBlockLength = 16; // version, reserved(2 Bytes), VendorID, DeviceID and InstanceID
            ParaBlockType = 0xF001;
            ParaBlockVersion = 0x0100;
        }

        //bytes 4-5
        public int DeviceID
        {
            set { BufferManager.Write16(m_Data, 2, value); }
            get { return BufferManager.Read16(m_Data, 2); }
        }

        //bytes 6-7
        public int InstanceID
        {
            set { BufferManager.Write16(m_Data, 4, value); }
            get { return BufferManager.Read16(m_Data, 4); }
        }

        public override byte[] ToByteArray
        {
            get
            {
                byte[] padding = new byte[2];
                List<byte> block = new List<byte>();
                block.AddRange(header);
                block.AddRange(padding);
                block.AddRange(m_Data);
                return block.ToArray();
            }
        }

        //bytes 0-1 are reserved

        //bytes 2-3
        public int VendorID
        {
            set { BufferManager.Write16(m_Data, 0, value); }
            get { return BufferManager.Read16(m_Data, 0); }
        }
    }
}