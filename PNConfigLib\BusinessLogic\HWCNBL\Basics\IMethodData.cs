/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IMethodData.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

#endregion

namespace PNConfigLib.HWCNBL.Basics
{
    /// <summary>
    /// The interface IMethodData provides access to the data of a generic method via
    /// IBaseActions.CallMethod().
    /// </summary>
    public interface IMethodData
    {
        /// <summary>
        /// The collection of all arguments, where each argument is referenced by name (string)
        /// and of type object.
        /// </summary>
        NameObjectCollection Arguments { get; }

        /// <summary>
        /// The name of the method, which is registered at the config object; it’s not
        /// necessarily the same as the name of the corresponding callback
        /// (but a similarity is highly recommended).
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// The return value of the method; may be null in case of a void-method.
        /// </summary>
        object ReturnValue { get; set; }
    }
}