/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: BlockDataWrapper.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Globalization;

namespace PNConfigLib.HWCNBL.Tailor.Config
{
    /// <summary>
    /// BlockDataWrapper generic class for managing an U(ValueType) array with T(enum) indexes
    /// </summary>
    internal class BlockDataWrapper<T, U>
        where T : struct
        where U : struct
    {
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Nested classes
        // Contains all non-public nested classes and locally scoped interface definitions
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Constants and enumerations
        // Contains all constants and enumerations
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Fields
        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private U[] m_Data;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Delegates and events
        // Contains all delegate and events
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Construction/destruction/initialization
        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nr">the number of U properties</param>
        public BlockDataWrapper(int nr)
        {
            if (!typeof(T).IsEnum)
            {
                throw new NotSupportedException("Type T is not enum");
            }

            if (!typeof(U).IsValueType)
            {
                throw new NotSupportedException("Type U is not ValueType");
            }

            m_Data = new U[nr];
            BlockLength = nr * System.Runtime.InteropServices.Marshal.SizeOf(typeof(U));
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Public methods
        // Contains all public methods of the class
        public U GetBlockElement(T portMasterTailorDataElement)
        {
            return m_Data[Convert.ToByte(portMasterTailorDataElement, CultureInfo.InvariantCulture)];
        }

        public void SetBlockElement(T portMasterTailorDataElement, U valueToSet)
        {
            m_Data[Convert.ToByte(portMasterTailorDataElement, CultureInfo.InvariantCulture)] = valueToSet;
        }

        public byte[] ToByteArray => GetByteArray(m_Data);

        private byte[] GetByteArray(U[] array)
        {
            byte[] arr = null;
            if (array.Length > 0)
            {
                if (array[0].GetType().IsInstanceOfType((ushort)0))
                {
                    arr = new byte[array.Length << 1];
                    int index = 0;
                    foreach (U item in array)
                    {
                        ushort uItem = Convert.ToUInt16(item, CultureInfo.InvariantCulture);
                        arr[index++] = (byte)((uItem & 0xFF00) >> 8);
                        arr[index++] = (byte)(uItem & 0x00FF);
                    }
                }
                else
                {
                    throw new NotSupportedException("Type U can only be ushort!");
                }
            }
            return arr;
        }

        public int BlockLength
        {
            set;
            get;
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region I... members
        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Overrides and overridables
        // Contains all public and protected overrides as well as overridables of the class
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Protected methods
        // Contains all protected (non overridables) methods of the class
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Private implementation
        // Contains the private implementation of the class
        #endregion
    }
}
