/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: TopologyChecker.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigLib.Consistency.Checker
{
    internal class TopologyChecker
    {
        private readonly Topology m_Topology;

        private readonly ConfigReader.Configuration.Configuration m_Configuration;

        private readonly ListOfNodes m_ListOfNodes;
        private readonly string m_ListOfNodesPath;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="topo"></param>
        /// <param name="cfg"></param>
        /// <param name="lon"></param>
        public TopologyChecker(ConfigReader.Configuration.Configuration cfg, ListOfNodes lon, Topology topo, string listOfNodesPath)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
            m_Topology = topo;
            m_ListOfNodesPath = listOfNodesPath;
        }

        /// <summary>
        /// Check consistencies
        /// </summary>
        /// <returns></returns>
        public void Check()
        {
            // Call check methods
            //CheckTopologyForAlldevices(); //Handled by event
            CheckTopology();
        }

        private void CheckTopology()
        {
            if (m_Topology == null)
            {
                return;
            }

            foreach (TopologyTypePortInterconnection portInterconnection in m_Topology.PortInterconnection)
            {
                CheckTopologyPortNumberAndSlots(
                        m_Configuration,
                        m_ListOfNodes,
                        portInterconnection.LocalPort.DeviceRefID,
                        portInterconnection.LocalPort.SlotNumberSpecified,
                        portInterconnection.LocalPort.SlotNumber,
                        portInterconnection.LocalPort.PortNumber);

                if (portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                {
                    CheckTopologyPortNumberAndSlots(
                        m_Configuration,
                        m_ListOfNodes,
                        portInterconnection.PartnerPort.DeviceRefID,
                        portInterconnection.PartnerPort.SlotNumberSpecified,
                        portInterconnection.PartnerPort.SlotNumber,
                        portInterconnection.PartnerPort.PortNumber);
                }
            }
        }

        private void CheckTopologyPortNumberAndSlots(
            ConfigReader.Configuration.Configuration configuration,
            ListOfNodes lon,
            string deviceRefId,
            bool isSlotNumberSpecified,
            uint slotNr,
            uint portNr)
        {
            CentralDeviceType xmlCentralDevice =
                configuration.Devices.CentralDevice.FirstOrDefault(c => c.DeviceRefID == deviceRefId);
            if (xmlCentralDevice != null)
            {
                CheckDeviceSlotForCentralDevice(slotNr, xmlCentralDevice);
                CheckPortNumberForCentralDevice(lon, xmlCentralDevice, portNr);
            }
            else
            {
                ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice =
                    configuration.Devices.DecentralDevice.FirstOrDefault(d => d.DeviceRefID == deviceRefId);
                

                string gsdFilePath = lon.DecentralDevice
                    .FirstOrDefault(d => d.DeviceID == xmlDecentralDevice.DeviceRefID)?.GSDPath;


                if (!isSlotNumberSpecified)
                {
                    CheckPortNumberForDecentralDevice(lon, xmlDecentralDevice, portNr, gsdFilePath);
                    
                }
                else
                {
                    CheckModulePortAndSlotNumberForDecentralDevices(lon, deviceRefId, slotNr, portNr, xmlDecentralDevice, gsdFilePath);
                }
            }
        }

        private void CheckDeviceSlotForCentralDevice(uint slotNr, CentralDeviceType xmlCentralDevice)
        {
            if (slotNr != 0)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TopologyDeviceSlotCheck,
                    xmlCentralDevice.DeviceRefID,
                    slotNr);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPortNumberForCentralDevice(ListOfNodes lon, CentralDeviceType xmlCentralDevice, uint portNr)
        {
            PNDriverType pndriver = lon.PNDriver.FirstOrDefault(p => p.DeviceID == xmlCentralDevice.DeviceRefID);
            if (pndriver == null)
            {
                return;
            }

            CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                pndriver.Interface.InterfaceType,
                pndriver.Interface.CustomInterfacePath,
                pndriver.DeviceVersion,
                m_ListOfNodesPath);

            if (pndCatalog.Interface.PortList.Count < portNr)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TopologyInvalidPortNumber,
                    portNr,
                    xmlCentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPortNumberForDecentralDevice(ListOfNodes lon, ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice, uint portNr, string gsdFilePath)
        {
            ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice =
                        lon.DecentralDevice.FirstOrDefault(d => d.DeviceID == xmlDecentralDevice.DeviceRefID);

            DecentralDeviceCatalog lonDeviceCatalog =
                DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                    gsdFilePath,
                    lonDecentralDevice.GSDRefID);

            uint deviceInterfaceCount = 1;
            if (xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports.All(
                    p => p.PortNumber != portNr)
                && (lonDeviceCatalog.Interface.PortList.Count < portNr)
                && (lonDeviceCatalog.PhysicalSubslotList.Count - deviceInterfaceCount < portNr))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TopologyInvalidPortNumber,
                    portNr,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckInterconnectedPortOwnerDeleted(ModuleType xmlModule, uint slotNr, ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice)
        {
            if (xmlModule.GSDRefID == string.Empty)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InterconnectedPortOwnerDeleted,
                    slotNr,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPortNumberValidForDecentralDevice(ModuleType xmlModule, uint portNr, ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice, string gsdFilePath)
        {
            ModuleCatalog moduleCatalog =
                            ModuleCatalogHelper.GetModuleCatalogWithGsdPath(gsdFilePath, xmlModule.GSDRefID);

            if (moduleCatalog != null
                && moduleCatalog.SystemDefinedPorts.Count < portNr)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TopologyInvalidPortNumber,
                    portNr,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckModulePortAndSlotNumberForDecentralDevices(ListOfNodes lon, string deviceRefId, uint slotNr, uint portNr, ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice, string gsdFilePath)
        {
            ConfigReader.ListOfNodes.DecentralDeviceType decentralLon =
                    lon.DecentralDevice.FirstOrDefault(d => d.DeviceID == deviceRefId);

            DecentralDeviceCatalog deviceCatalog =
            DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                decentralLon.GSDPath,
                decentralLon.GSDRefID);

            int catalogSlotNumber = deviceCatalog.AttributeAccess.GetAnyAttribute<int>(
            InternalAttributeNames.PositionNumber,
            new AttributeAccessCode(),
            0);

            ModuleType xmlModule = xmlDecentralDevice?.Module?.FirstOrDefault(m => m.SlotNumber == slotNr);


            if (xmlModule != null)
            {
                CheckInterconnectedPortOwnerDeleted(xmlModule, slotNr, xmlDecentralDevice);

                if (xmlModule.Port != null
                    && !xmlModule.Port.All(p => p.PortNumber != portNr))
                {
                    return;
                }

                CheckPortNumberValidForDecentralDevice(xmlModule, portNr, xmlDecentralDevice, gsdFilePath);

            }
            else
            {
                CheckDeviceSlotForDecentralDevice(xmlDecentralDevice, catalogSlotNumber, slotNr);
                
            }
        }

        private void CheckDeviceSlotForDecentralDevice(ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice, int catalogSlotNumber, uint slotNr)
        {
            if (catalogSlotNumber != slotNr)
            {
                ConsistencyLogger.Log(
                ConsistencyType.XML,
                LogSeverity.Error,
                string.Empty,
                ConsistencyConstants.XML_TopologyDeviceSlotCheck,
                xmlDecentralDevice.DeviceRefID,
                slotNr);
                throw new ConsistencyCheckException();
            }
        }
        internal static bool isTopologyFileExist(string topologyXmlPath, out bool topologyExists) 
        {
            topologyExists = false;

            if (!string.IsNullOrEmpty(topologyXmlPath))
            {
                if (File.Exists(topologyXmlPath))
                {
                    topologyExists = true;
                }
                else
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_NotExistTopologyXML);
                    return false;
                }
            }
            return true;
        }

        private void CheckLocalPortDeviceRefIDExist(TopologyTypePortInterconnection portInterconnection) 
        {

            // check deviceRefID
            if (m_ListOfNodes.PNDriver.All(d => d.DeviceID != portInterconnection.LocalPort.DeviceRefID)
                && m_ListOfNodes.DecentralDevice.All(
                    d => d.DeviceID != portInterconnection.LocalPort.DeviceRefID))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NonExistingDeviceRefIDInTopology,
                    portInterconnection.LocalPort.DeviceRefID);
                throw new ConsistencyCheckException();
            }

        }

        private void CheckLocalInterfaceRefIDExist(IConfigInterface localInterface)
        {
            // check InterfaceRefID
            if (m_ListOfNodes.PNDriver.All(w => w.Interface.InterfaceID != localInterface.InterfaceRefID)
                && m_ListOfNodes.DecentralDevice.All(
                    w => w.Interface.InterfaceID != localInterface.InterfaceRefID))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NonExistingInterfaceRefIDInTopology,
                    localInterface.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPartnerPortInterfaceRefIDExist(TopologyTypePortInterconnection portInterconnection)
        {
            // check InterfaceRefID partner port
            if ((portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                && (m_ListOfNodes.PNDriver.All(
                        w => w.Interface.InterfaceID != portInterconnection.PartnerPort.InterfaceRefID)
                    && m_ListOfNodes.DecentralDevice.All(
                        w => w.Interface.InterfaceID != portInterconnection.PartnerPort.InterfaceRefID)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NonExistingInterfaceRefIDInTopology,
                    portInterconnection.PartnerPort.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckLocalPortDeviceRefIDMatchedInPNDrivers(IConfigInterface localInterface, TopologyTypePortInterconnection portInterconnection) 
        {
            // check that on existing InterfaceRef IDs the deviceRefId is also correct on the local port for PNDriver
            if (m_ListOfNodes.PNDriver.Exists(w => w.Interface.InterfaceID == localInterface.InterfaceRefID)
                && m_ListOfNodes.PNDriver.Any(
                    w => (w.Interface.InterfaceID == localInterface.InterfaceRefID)
                         && (w.DeviceID != portInterconnection.LocalPort.DeviceRefID)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UnmatchedInterfaceRefIDInTopology,
                    localInterface.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPartnerPortDeviceRefIDExist(TopologyTypePortInterconnection portInterconnection)
        {
            // check deviceRefID partner port

            if ((portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                && (m_ListOfNodes.PNDriver.All(d => d.DeviceID != portInterconnection.PartnerPort.DeviceRefID)
                    && m_ListOfNodes.DecentralDevice.All(
                        d => d.DeviceID != portInterconnection.PartnerPort.DeviceRefID)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NonExistingDeviceRefIDInTopology,
                    portInterconnection.PartnerPort.DeviceRefID);
                throw new ConsistencyCheckException();
            }

        }

        private void CheckPartnerPortDeviceRefIDMatchedInPNDrivers(TopologyTypePortInterconnection portInterconnection)
        {
            // check that on existing InterfaceRef IDs the deviceRefId is also correct on the partner port
            if ((portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                && (m_ListOfNodes.PNDriver.Exists(
                        w => w.Interface.InterfaceID == portInterconnection.PartnerPort.InterfaceRefID)
                    && m_ListOfNodes.PNDriver.Any(
                        w => (w.Interface.InterfaceID == portInterconnection.PartnerPort.InterfaceRefID)
                             && (w.DeviceID != portInterconnection.PartnerPort.DeviceRefID))))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UnmatchedInterfaceRefIDInTopology,
                    portInterconnection.PartnerPort.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckLocalPortDeviceRefIDMatchedInDecentralDevices(IConfigInterface localInterface, TopologyTypePortInterconnection portInterconnection)
        {
            // check that on existing InterfaceRef IDs the deviceRefId is also correct on the local port for decentral device
            if (m_ListOfNodes.DecentralDevice.Exists(
                    w => w.Interface.InterfaceID == localInterface.InterfaceRefID)
                && m_ListOfNodes.DecentralDevice.Any(
                    w => (w.Interface.InterfaceID == localInterface.InterfaceRefID)
                         && (w.DeviceID != portInterconnection.LocalPort.DeviceRefID)))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UnmatchedInterfaceRefIDInTopology,
                    localInterface.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPartnerPortDeviceRefIDMatchedInDecentralDevices(TopologyTypePortInterconnection portInterconnection)
        {
            // check that on existing InterfaceRef IDs the deviceRefId is also correct on the partner port
            if ((portInterconnection.LocalPort.PartnerPortType != PartnerPortType.SetByUserProgram)
                && (m_ListOfNodes.DecentralDevice.Exists(
                        w => w.Interface.InterfaceID == portInterconnection.PartnerPort.InterfaceRefID)
                    && m_ListOfNodes.DecentralDevice.Any(
                        w => (w.Interface.InterfaceID == portInterconnection.PartnerPort.InterfaceRefID)
                             && (w.DeviceID != portInterconnection.PartnerPort.DeviceRefID))))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_UnmatchedInterfaceRefIDInTopology,
                    portInterconnection.PartnerPort.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckPortIsUsedMoreThanOneForLocalPort(TopologyTypePortInterconnectionLocalPort local, List<string> portList)
        {
            // No ports from different subnets should be connected.
            string localPort = StringOperations.CombineParametersWithSplitter(
                "/",
                local.DeviceRefID,
                local.SlotNumber,
                local.PortNumber);

            if (portList.Contains(localPort))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_PortIsUsedMoreThanOneInTopology,
                    localPort);
                throw new ConsistencyCheckException();
            }
            portList.Add(localPort);
        }

        private void CheckPortIsUsedMoreThanOneForPartnerPort(TopologyTypePortInterconnectionPartnerPort partner, List<string> portList)
        {
            // If the partner port is not specified.
            if ((partner.DeviceRefID != null)
                && (partner.InterfaceRefID != null))
            {
                string partnerPort = StringOperations.CombineParametersWithSplitter(
                    "/",
                    partner.DeviceRefID,
                    partner.SlotNumber,
                    partner.PortNumber);

                if (portList.Contains(partnerPort))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_PortIsUsedMoreThanOneInTopology,
                        partnerPort);
                    throw new ConsistencyCheckException();
                }
                portList.Add(partnerPort);
            }
        }
        internal void CheckTopologyForAlldevices(object o, EventArgs eventArgs)
        {
            if (m_Topology == null)
            {
                return;
            }

            List<IConfigInterface> interfacesInProject = new List<IConfigInterface>();

            GetInterfacesInProject(interfacesInProject);
            

            List<string> portList = new List<string>();
            foreach (TopologyTypePortInterconnection portInterconnection in m_Topology.PortInterconnection)
            {
                IConfigInterface localInterface =
                    interfacesInProject.SingleOrDefault(
                        e => e.InterfaceRefID == portInterconnection.LocalPort.InterfaceRefID);

                CheckInterfaceRefIDCorrectForLocalPort( localInterface, portInterconnection);
                
                CheckLocalPortDeviceRefIDExist(portInterconnection);
                CheckLocalInterfaceRefIDExist(localInterface);
                CheckPartnerPortDeviceRefIDExist(portInterconnection);
                CheckPartnerPortInterfaceRefIDExist(portInterconnection);
                CheckLocalPortDeviceRefIDMatchedInPNDrivers(localInterface, portInterconnection);
                CheckPartnerPortDeviceRefIDMatchedInPNDrivers(portInterconnection);
                CheckLocalPortDeviceRefIDMatchedInDecentralDevices(localInterface, portInterconnection);
                CheckPartnerPortDeviceRefIDMatchedInDecentralDevices(portInterconnection);

                CheckPortIsUsedMoreThanOneForLocalPort(portInterconnection.LocalPort, portList);
                CheckPortIsUsedMoreThanOneForPartnerPort(portInterconnection.PartnerPort, portList);

            }
        }

        private void GetInterfacesInProject(List<IConfigInterface> interfacesInProject)
        {
            foreach (CentralDeviceType xmlCentralDevice in m_Configuration.Devices.CentralDevice)
            {
                interfacesInProject.Add(xmlCentralDevice.CentralDeviceInterface);
            }

            foreach (ConfigReader.Configuration.DecentralDeviceType xmlDecentralDevice in m_Configuration.Devices.DecentralDevice)
            {
                interfacesInProject.Add(xmlDecentralDevice.DecentralDeviceInterface);
            }
        }

        private void CheckInterfaceRefIDCorrectForLocalPort(IConfigInterface localInterface, TopologyTypePortInterconnection portInterconnection)
        {
            
            if (localInterface == null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IncorrectInterfaceRefID,
                    portInterconnection.LocalPort.InterfaceRefID);
                throw new ConsistencyCheckException();
            }
        }

        internal void IsIRTProjectWithoutTopology(object o, EventArgs eventArgs)
        {
            bool irtProject = false;

            foreach (CentralDeviceType centralDevice in m_Configuration.Devices.CentralDevice)
            {
                if (centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                        .SynchronizationRole != SyncRole.Unsynchronized)
                {
                    List<ConfigReader.Configuration.DecentralDeviceType> decentralDevicesOfIOSystem =
                        m_Configuration.Devices.DecentralDevice
                            .Where(
                                d => d.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID == centralDevice
                                         .CentralDeviceInterface.EthernetAddresses.IOSystemRefID).ToList();

                    if (decentralDevicesOfIOSystem.Any(
                        d => d.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                                 .SynchronizationRole != SyncRole.Unsynchronized))
                    {
                        irtProject = true;
                    }
                }
            }

            if (irtProject && (m_Topology == null))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IRTProjectWithoutTopology);
                throw new ConsistencyCheckException();
            }
        }
    }
}
