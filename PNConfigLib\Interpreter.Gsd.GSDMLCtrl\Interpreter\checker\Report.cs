/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Report.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    //########################################################################
    #region Enums

    /// <summary>
    /// Specifies the possible categories of the report.
    /// </summary>
    public enum ReportCategories
    {
        /// <summary>
        /// Is used for general tests like naming conventions, and so on.
        /// </summary>
        General,
        /// <summary>
        /// Is used for all reports during validation with the schema.
        /// </summary>
        Validation,
        /// <summary>
        /// Is used for reports during signature checks.
        /// </summary>
        Signature,
        /// <summary>
        /// Is used for reports during key and key reference tests.
        /// </summary>
        KeyKeyref,
        /// <summary>
        /// Is used for reports during plug rule checks.
        /// </summary>
        PlugRules,
        /// <summary>
        /// Is used for reports during test of all other specific elements.
        /// </summary>
        TypeSpecific
    }

    #endregion

    /// <summary>
    /// The Report object is used to remember information about a specific
    /// problem or basic information.
    /// </summary>
    public class Report :
        GSDI.IReport
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated. The properties 
        /// of the object are initialized to the values given within the 
        /// hashtable parameter.
        /// </summary>
        /// <remarks>Further on property settings can only be made by the Fill
        /// method, which is only available within this assembly.</remarks>
        public Report(Hashtable hash)
        {
            this.InitReport();

            // Fill report with data.
            this.Fill(hash);
        }


        /// <summary>
        /// Makes the initialization of the properties to empty or abstract
        /// default values.
        /// </summary>
        protected bool InitReport()
        {
            this.m_LineNumber = 0;
            this.m_LinePosition = 0;

            this.m_Message = String.Empty;
            this.m_SourceXPath = String.Empty;
            this.m_Category = String.Empty;
            this.m_CheckNumber = String.Empty;

            this.m_Type = GSDI.ReportTypes.GsdrtAll;

            return true;
        }


        #endregion

        //########################################################################################
        #region Fields

        private int m_LineNumber;
        private int m_LinePosition;

        private string m_Message;
        private string m_SourceXPath;
        private string m_Category;
        private string m_CheckNumber;

        private GSDI.ReportTypes m_Type;

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter can't be 'null'!");

                // Own data.
                string member = Constants.s_ReportLineNumber;
                if (hash.ContainsKey(member) && hash[member] is int)
                    this.m_LineNumber = (int)hash[member];

                member = Constants.s_ReportLinePosition;
                if (hash.ContainsKey(member) && hash[member] is int)
                    this.m_LinePosition = (int)hash[member];

                member = Constants.s_ReportCheckNumber;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_CheckNumber = hash[member] as string;

                member = Constants.s_ReportMessage;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Message = hash[member] as string;

                member = Constants.s_ReportSourceXPath;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_SourceXPath = hash[member] as string;

                member = Constants.s_ReportCategory;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Category = hash[member] as string;

                member = Constants.s_ReportType;
                if (hash.ContainsKey(member) && hash[member] is GSDI.ReportTypes)
                    this.m_Type = (GSDI.ReportTypes)hash[member];
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool Serialize(ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementReport);

            // ----------------------------------------------
            this.SerializeMembers(ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool SerializeMembers(ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Constants.s_ReportType, this.m_Type.ToString(), Export.s_SubtypeReportTypes);
            Export.WriteStringProperty(ref writer, Constants.s_ReportCategory, this.m_Category);
            Export.WriteInt32Property(ref writer, Constants.s_ReportLineNumber, this.m_LineNumber);
            Export.WriteInt32Property(ref writer, Constants.s_ReportLinePosition, this.m_LinePosition);
            Export.WriteStringProperty(ref writer, Constants.s_ReportCheckNumber, this.m_CheckNumber);
            Export.WriteStringProperty(ref writer, Constants.s_ReportMessage, this.m_Message);
            Export.WriteStringProperty(ref writer, Constants.s_ReportSourceXPath, this.m_SourceXPath);

            return true;
        }

        #endregion

        //########################################################################################
        #region IReport Members

        /// <summary>
        /// Accesses the line number on which the problem occurres. If
        /// the line number is unknown, it is set to 0.
        /// </summary>
        public int LineNumber => this.m_LineNumber;

        /// <summary>
        /// Accesses the line position on which the problem occurres. If
        /// the line position is unknown, it is set to 0.
        /// </summary>
        public int LinePosition => this.m_LinePosition;

        /// <summary>
        /// Accesses the problem message, which should describe the
        /// problem in a simple, short and understandable way.
        /// </summary>
        public string Message => this.m_Message;
        /// <summary>
        /// Accesses the xpath expression, which specifies the location
        /// causing the problem. If it couldn't be created, an empty
        /// string is returned.
        /// </summary>
        public string SourceXPath => this.m_SourceXPath;

        /// <summary>
        /// Accesses the category, specifying the group to which the
        /// problem belongs to. 
        /// </summary>
        public string Category => this.m_Category;

        /// <summary>
        /// Accesses the check number, specifying the check exactly,
        /// for which the report would be generated.
        /// </summary>
        public string CheckNumber => this.m_CheckNumber;

        /// <summary>
        /// Accesses the type of the report, which specifies, whether
        /// it is an error, warning or info report.
        /// </summary>
        public GSDI.ReportTypes Type => this.m_Type;

        #endregion

    }
}