/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ParamObjectRefreshPNPlannerInput.cs       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL
{
    internal class ParamObjectRefreshPNPlannerInput
    {
        /// <summary>
        /// CountPIPInput
        /// </summary>
        internal Dictionary<int, int> CountPIPInput { get; set; }

        /// <summary>
        /// CountPIPOutput
        /// </summary>
        internal Dictionary<int, int> CountPIPOutput { get; set; }

        /// <summary>
        /// DiscardIOXS
        /// </summary>
        internal bool DiscardIOXS { get; set; }

        /// <summary>
        /// HasUsingData
        /// </summary>
        internal bool HasUsingData { get; set; }

        /// <summary>
        /// InputGrossLength
        /// </summary>
        internal long InputGrossFrameLength { get; set; }

        /// <summary>
        /// InputNetLength
        /// </summary>
        internal long InputNetFrameLength { get; set; }

        /// <summary>
        /// PNPlanner triggered from compile
        /// </summary>
        internal bool IsCompile { get; set; }

        /// <summary>
        /// OutputGrossLength
        /// </summary>
        internal long OutputGrossFrameLength { get; set; }

        /// <summary>
        /// OutputNetLength
        /// </summary>
        internal long OutputNetFrameLength { get; set; }

        /// <summary>
        /// ProxyCount
        /// </summary>
        internal short ProxyCount { get; set; }

        /// <summary>
        /// SharedDeviceSupported
        /// </summary>
        internal bool SharedDeviceSupported { get; set; }

        /// <summary>
        /// Calculated input sizes for every assigned pips
        /// </summary>
        internal Dictionary<int, long> SizePIPInput { get; set; }

        /// <summary>
        /// Calculated output sizes for every assigned pips
        /// </summary>
        internal Dictionary<int, long> SizePIPOutput { get; set; }

        /// <summary>
        /// SubmoduleCount
        /// </summary>
        internal short SubmoduleCount { get; set; }

        /// <summary>
        /// CreateParamObjectRefreshPNPlannerInput
        /// </summary>
        /// <returns>param object PNPlanner</returns>
        public static ParamObjectRefreshPNPlannerInput CreateParamObjectRefreshPNPlannerInput()
        {
            //create parameter object with the needed data
            //create an input and an output size for all pips
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput = new ParamObjectRefreshPNPlannerInput
                                                                        {
                                                                            IsCompile = true,
                                                                            InputGrossFrameLength
                                                                                = 0,
                                                                            OutputGrossFrameLength
                                                                                = 0,
                                                                            InputNetFrameLength
                                                                                = 0,
                                                                            OutputNetFrameLength
                                                                                = 0,
                                                                            ProxyCount = 0,
                                                                            SubmoduleCount = 0,
                                                                            HasUsingData =
                                                                                false,
                                                                            SizePIPInput =
                                                                                new Dictionary
                                                                                <int, long>(),
                                                                            SizePIPOutput =
                                                                                new Dictionary
                                                                                <int, long>(),
                                                                            CountPIPInput =
                                                                                new Dictionary
                                                                                <int, int>(),
                                                                            CountPIPOutput =
                                                                                new Dictionary
                                                                                <int, int>()
                                                                        };
            return paramObjectRefreshPNPlannerInput;
        }
    }
}