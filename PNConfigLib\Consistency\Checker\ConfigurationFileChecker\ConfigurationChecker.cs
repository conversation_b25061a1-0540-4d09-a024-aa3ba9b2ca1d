/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigurationChecker.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class ConfigurationChecker
    {
        private IConsistencyChecker m_DecentralDeviceChecker;

        private IConsistencyChecker m_CentralDeviceChecker;

        private IConsistencyChecker m_SharedDeviceChecker;

        private IConsistencyChecker m_SubnetChecker;

        private IConsistencyChecker m_IsochronChecker;

        private IConsistencyChecker m_ParameterRecordDataChecker;

        private IConsistencyChecker m_PortChecker;

        private IConsistencyChecker m_MrpChecker;

        private IConsistencyChecker m_IOAddressChecker;

        private IConsistencyChecker m_GsdReferenceChecker;

        private IConsistencyChecker m_IPConfigurationChecker;

        private IConsistencyChecker m_SyncDomainChecker;

        private IConsistencyChecker m_IOSystemChecker;

        private readonly Configuration m_Configuration;

        private readonly ListOfNodes m_ListOfNodes;

        private readonly Topology m_Topology;
        private readonly string m_ListOfNodesPath;

        internal event EventHandler TopologyCheck;

        internal event EventHandler IsIRTProjectWithoutTopology;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="cfg"></param>
        public ConfigurationChecker(Configuration cfg, ListOfNodes lon, Topology topo, string listOfNodesPath)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
            m_Topology = topo;
            m_ListOfNodesPath = listOfNodesPath;
            Initialize();
        }

        /// <summary>
        /// Start point for the configuration checks
        /// </summary>
        /// <returns></returns>
        internal void Check()
        {
            IsAddressTailoringSupported();
            m_IPConfigurationChecker.Check();
            AreCentralDevicePortsValid();
            m_SubnetChecker.Check();
            m_CentralDeviceChecker.Check();
            m_IOSystemChecker.Check();
            this.IsIRTProjectWithoutTopology(this, new EventArgs());
            this.TopologyCheck(this, new EventArgs());
            m_DecentralDeviceChecker.Check();
            m_IsochronChecker.Check();
            m_MrpChecker.Check();
            m_GsdReferenceChecker.Check();
            m_SyncDomainChecker.Check();
            m_SharedDeviceChecker.Check();
            m_IOAddressChecker.Check();
            m_PortChecker.Check();
            m_ParameterRecordDataChecker.Check();
        }

        private void IsAddressTailoringSupported()
        {
            IEnumerable<SubnetIOSystem> ioSystems = m_Configuration.Subnet?.SelectMany(s => s.IOSystem);

            if (ioSystems == null)
            {
                return;
            }

            foreach (SubnetIOSystem ioSys in ioSystems)
            {
                if (ioSys.General == null
                    || !ioSys.General.MultipleUseIOSystem)
                {
                    continue;
                }

                CentralDeviceType centralDevice = m_Configuration.Devices.CentralDevice.FirstOrDefault(
                    cd => cd.CentralDeviceInterface?.EthernetAddresses?.IOSystemRefID == ioSys.IOSystemID);
                if (centralDevice == null)
                {
                    continue;
                }

                PNDriverType pndriver =
                    ListOfNodesChecker.GetListOfNodesCentralDeviceById(centralDevice.DeviceRefID, m_ListOfNodes);

                if (pndriver == null)
                {
                    continue;
                }

                CentralDeviceCatalog pndCatalog = Catalog.GetCentralDeviceCatalog(
                    pndriver.Interface.InterfaceType,
                    pndriver.Interface.CustomInterfacePath,
                    pndriver.DeviceVersion,
                    m_ListOfNodesPath);

                bool addressTailoringSupported = pndCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoAddressTailoring,
                    new AttributeAccessCode(),
                    false);

                if (addressTailoringSupported)
                {
                    continue;
                }

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_AddressTailoringActivatedButNotSupported,
                    ioSys.IOSystemID,
                    centralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void AreCentralDevicePortsValid()
        {
            if (m_ListOfNodes?.PNDriver == null)
            {
                return;
            }


            foreach (PNDriverType pnd in m_ListOfNodes.PNDriver)
            {
                CentralDeviceCatalog catalog = Catalog.GetCentralDeviceCatalog(
                    pnd.Interface.InterfaceType,
                    pnd.Interface.CustomInterfacePath,
                    pnd.DeviceVersion,
                    m_ListOfNodesPath);

                if (m_Configuration?.Devices?.CentralDevice == null)
                {
                    continue;
                }

                CentralDeviceType currentCentralDevice =
                    m_Configuration.Devices.CentralDevice.FirstOrDefault(
                        c => c.CentralDeviceInterface.InterfaceRefID == pnd.Interface.InterfaceID);

                if (currentCentralDevice != null
                    && catalog.Interface.PortList.Count < currentCentralDevice.CentralDeviceInterface
                        .AdvancedOptions.Ports.Count)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_InvalidCentralDevicePortCount,
                        currentCentralDevice.CentralDeviceInterface.InterfaceRefID,
                        catalog.Interface.PortList.Count,
                        currentCentralDevice.CentralDeviceInterface.AdvancedOptions.Ports.Count);
                    throw new ConsistencyCheckException();
                }
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private void Initialize()
        {
            ConsistencyCheckerFactory consistencyCheckerFactory = new ConsistencyCheckerFactory(m_Configuration, m_ListOfNodes, m_ListOfNodesPath, m_Topology);

            m_CentralDeviceChecker = consistencyCheckerFactory.GetCentralDeviceChecker();
            m_DecentralDeviceChecker = consistencyCheckerFactory.GetDecentralDeviceChecker();
            m_SharedDeviceChecker = consistencyCheckerFactory.GetSharedDeviceChecker();
            m_SubnetChecker = consistencyCheckerFactory.GetSubnetChecker();
            m_IsochronChecker = consistencyCheckerFactory.GetIsochronChecker();
            m_MrpChecker = consistencyCheckerFactory.GetMrpChecker();
            m_GsdReferenceChecker = consistencyCheckerFactory.GetGsdReferenceChecker();
            m_ParameterRecordDataChecker = consistencyCheckerFactory.GetParameterRecordDataChecker();
            m_IOAddressChecker = consistencyCheckerFactory.GetIOAddressChecker();
            m_PortChecker = consistencyCheckerFactory.GetPortChecker();
            m_IPConfigurationChecker = consistencyCheckerFactory.GetIPConfigurationChecker();
            m_SyncDomainChecker = consistencyCheckerFactory.GetSyncDomainChecker();
            m_IOSystemChecker = consistencyCheckerFactory.GetIOSystemChecker();
        }

    }
}