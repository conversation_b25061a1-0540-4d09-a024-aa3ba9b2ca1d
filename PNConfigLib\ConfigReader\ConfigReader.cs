/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigReader.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using System.IO;
using System.Xml.Serialization;

namespace PNConfigLib.ConfigReader
{
    internal class ConfigReader
    {
        internal void DeserializeXMLFiles(string configXmlPath,
            out Configuration.Configuration configuration,
            string listOfNodesXmlPath,
            out ListOfNodes.ListOfNodes listOfNodes,
            string topologyXmlPath,
            bool topologyExists,
            out Topology.Topology topology)
        {
            topology = null;

            configuration = DeserializeInput<Configuration.Configuration>(configXmlPath);

            if (topologyExists)
            {
                topology = DeserializeInput<Topology.Topology>(topologyXmlPath);
            }

            listOfNodes = DeserializeInput<ListOfNodes.ListOfNodes>(listOfNodesXmlPath);
        }

        internal static T DeserializeInput<T>(string filePath)
        {
            T retval;
            XmlSerializer xmlSerializer = new XmlSerializer(typeof(T));
            using (FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                retval = (T)xmlSerializer.Deserialize(fileStream);
            }
            return retval;
        } 
    }
}
