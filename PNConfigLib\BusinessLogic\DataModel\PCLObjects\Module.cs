/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Module.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a module.
    /// </summary>
    internal class Module : PclObject
    {
        /// <summary>
        /// List of Port objects connected to this Module.
        /// </summary>
        private List<Port> m_Ports;

        /// <summary>
        /// List of Submodule objects connected to this Module.
        /// </summary>
        private List<Submodule> m_Submodules;

        /// <summary>
        /// List of virtual Submodule objects of this Module.
        /// </summary>
        private List<Submodule> m_VirtualSubmodules;

        /// <summary>
        /// Default constructor of Module.
        /// </summary>
        public Module(string id)
        {
            Id = id;
            m_Submodules = new List<Submodule>();
            m_VirtualSubmodules = new List<Submodule>();
            m_Ports = new List<Port>();
        }

        /// <summary>
        /// Adds a Port to this Module and registers its address.
        /// </summary>
        /// <param name="port">The Port to be added.</param>
        /// <exception cref="ArgumentNullException">if port is null.</exception>
        /// <exception cref="InvalidOperationException">if the Module that the Port is being added to is not connected to a device.</exception>
        public void AddPort(Port port)
        {
            if (port == null)
            {
                throw new ArgumentNullException(nameof(port));
            }

            PclObject connectedDevice = GetDevice();

            if (connectedDevice == null)
            {
                throw new InvalidOperationException("The module must be connected to a device before adding ports.");
            }
            m_Ports.Add(port);
            port.ParentObject = this;
            port.Id = HWCNBL.Utilities.AttributeUtilities.GetName(this) + "_Port" + port.PortNumber;
            RegisterWithAddressManager(port);
        }

        /// <summary>
        /// Adds a Submodule to this Module and registers its address.
        /// </summary>
        /// <param name="submodule">The Submodule to be added.</param>
        /// <param name="isVirtual"></param>
        /// <exception cref="ArgumentNullException">if submodule is null.</exception>
        /// <exception cref="InvalidOperationException">
        /// if the Module that the Submodule is being added to is not connected to a
        /// device.
        /// </exception>
        public void AddSubmodule(Submodule submodule, bool isVirtual)
        {
            if (submodule == null)
            {
                throw new ArgumentNullException(nameof(submodule));
            }

            PclObject connectedDevice = GetDevice();

            if (connectedDevice == null)
            {
                throw new InvalidOperationException(
                    "The module must be connected to a device before adding a submodule.");
            }
            if (isVirtual)
            {
                m_VirtualSubmodules.Add(submodule);
                submodule.IsVirtual = true;
            }
            else
            {
                m_Submodules.Add(submodule);
            }

            submodule.ParentObject = this;
            RegisterWithAddressManager(submodule);
        }

        /// <summary>
        /// Gets the objects connected to this Interface with Element relation.
        /// These are the virtual Submodule and Submodule objects.
        /// </summary>
        /// <returns>A list containing the virtual Submodule and Submodule objects of this Module.</returns>
        public override IList<PclObject> GetElements()
        {
            List<PclObject> elementList = new List<PclObject>();

            elementList.AddRange(m_Submodules);
            elementList.AddRange(m_VirtualSubmodules);
            elementList.AddRange(m_Ports);

            return elementList;
        }

        /// <summary>
        /// Gets the list of Interface objects connected to this Module.
        /// </summary>
        /// <returns>The list of interfaces connected to this Module.</returns>
        internal override Interface GetInterface()
        {
            return GetDevice().GetInterface();
        }

        /// <summary>
        /// Gets the list of Port objects of this Module.
        /// </summary>
        /// <returns>The list of Port objects of this Module.</returns>
        public List<Port> GetPorts()
        {
            return m_Ports;
        }

        /// <summary>
        /// Gets the list of Submodule objects of this Module.
        /// </summary>
        /// <returns>The list of Submodule objects of this Module.</returns>
        public List<Submodule> GetSubmodules()
        {
            return m_Submodules;
        }

        /// <summary>
        /// Gets the list of virtual Submodule objects if this Module.
        /// </summary>
        /// <returns>The list of Submodule objects of this Module.</returns>
        public List<Submodule> GetVirtualSubmodules()
        {
            return m_VirtualSubmodules;
        }
    }
}