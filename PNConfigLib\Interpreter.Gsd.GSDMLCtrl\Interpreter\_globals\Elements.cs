/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Elements.cs                               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all element names of the GSDML specification.
    /// </summary>
    internal static class Elements
    {
        //########################################################################################
        #region GSDML V1.0 Elements
        // Contains all element names as constants

        /// <summary>name of the ISO15745Profile element</summary>
        public const string s_Iso15745Profile = @"ISO15745Profile";
        /// <summary>name of the ProfileHeader element</summary>
        public const string s_ProfileHeader = @"ProfileHeader";
        /// <summary>name of the ProfileIdentification element</summary>
        public const string s_ProfileIdentification = @"ProfileIdentification";
        /// <summary>name of the ProfileRevision element</summary>
        public const string s_ProfileRevision = @"ProfileRevision";
        /// <summary>name of the ProfileName element</summary>
        public const string s_ProfileName = @"ProfileName";
        /// <summary>name of the ProfileSource element</summary>
        public const string s_ProfileSource = @"ProfileSource";
        /// <summary>name of the ProfileClassID element</summary>
        public const string s_ProfileClassId = @"ProfileClassID";
        /// <summary>name of the ISO15745Reference element</summary>
        public const string s_Iso15745Reference = @"ISO15745Reference";
        /// <summary>name of the ISO15745Part element</summary>
        public const string s_Iso15745Part = @"ISO15745Part";
        /// <summary>name of the ISO15745Edition element</summary>
        public const string s_Iso15745Edition = @"ISO15745Edition";
        /// <summary>name of the ProfileTechnology element</summary>
        public const string s_ProfileTechnology = @"ProfileTechnology";
        /// <summary>name of the ProfileBody element</summary>
        public const string s_ProfileBody = @"ProfileBody";
        /// <summary>name of the DeviceIdentity element</summary>
        public const string s_DeviceIdentity = @"DeviceIdentity";
        /// <summary>name of the InfoText element</summary>
        public const string s_InfoText = @"InfoText";
        /// <summary>name of the VendorName element</summary>
        public const string s_VendorName = @"VendorName";
        /// <summary>name of the DeviceFunction element</summary>
        public const string s_DeviceFunction = @"DeviceFunction";
        /// <summary>name of the Family element</summary>
        public const string s_Family = @"Family";
        /// <summary>name of the ApplicationProcess element</summary>
        public const string s_ApplicationProcess = @"ApplicationProcess";
        /// <summary>name of the DeviceAccessPointList element</summary>
        public const string s_DeviceAccessPointList = @"DeviceAccessPointList";
        /// <summary>name of the DeviceAccessPointItem element</summary>
        public const string s_DeviceAccessPointItem = @"DeviceAccessPointItem";
        /// <summary>name of the ModuleInfo element</summary>
        public const string s_ModuleInfo = @"ModuleInfo";
        /// <summary>name of the Name element</summary>
        public const string s_Name = @"Name";
        /// <summary>name of the OrderNumber element</summary>
        public const string s_OrderNumber = @"OrderNumber";
        /// <summary>name of the HardwareRelease element</summary>
        public const string s_HardwareRelease = @"HardwareRelease";
        /// <summary>name of the SoftwareRelease element</summary>
        public const string s_SoftwareRelease = @"SoftwareRelease";
        /// <summary>name of the IOConfigData element</summary>
        public const string s_IoConfigData = @"IOConfigData";
        /// <summary>name of the UseableModules element</summary>
        public const string s_UseableModules = @"UseableModules";
        /// <summary>name of the ModuleItemRef element</summary>
        public const string s_ModuleItemRef = @"ModuleItemRef";
        /// <summary>name of the VirtualSubmoduleList element</summary>
        public const string s_VirtualSubmoduleList = @"VirtualSubmoduleList";
        /// <summary>name of the VirtualSubmoduleItem element</summary>
        public const string s_VirtualSubmoduleItem = @"VirtualSubmoduleItem";
        /// <summary>name of the IOData element</summary>
        public const string s_IoData = @"IOData";
        /// <summary>name of the Input element</summary>
        public const string s_Input = @"Input";
        /// <summary>name of the Output element</summary>
        public const string s_Output = @"Output";
        /// <summary>name of the DataItem element</summary>
        public const string s_DataItem = @"DataItem";
        /// <summary>name of the RecordDataList element</summary>
        public const string s_RecordDataList = @"RecordDataList";
        /// <summary>name of the ParameterRecordDataItem element</summary>
        public const string s_ParameterRecordDataItem = @"ParameterRecordDataItem";
        /// <summary>name of the Graphics element</summary>
        public const string s_Graphics = @"Graphics";
        /// <summary>name of the GraphicItemRef element</summary>
        public const string s_GraphicItemRef = @"GraphicItemRef";
        /// <summary>name of the ApplicationRelations element</summary>
        public const string s_ApplicationRelations = @"ApplicationRelations";
        /// <summary>name of the TimingProperties element</summary>
        public const string s_TimingProperties = @"TimingProperties";
        /// <summary>name of the ModuleList element</summary>
        public const string s_ModuleList = @"ModuleList";
        /// <summary>name of the ModuleItem element</summary>
        public const string s_ModuleItem = @"ModuleItem";
        /// <summary>name of the ValueList element</summary>
        public const string s_ValueList = @"ValueList";
        /// <summary>name of the ValueItem element</summary>
        public const string s_ValueItem = @"ValueItem";
        /// <summary>name of the Help element</summary>
        public const string s_Help = @"Help";
        /// <summary>name of the Assignments element</summary>
        public const string s_Assignments = @"Assignments";
        /// <summary>name of the Assign element</summary>
        public const string s_Assign = @"Assign";
        /// <summary>name of the ChannelDiagList element</summary>
        public const string s_ChannelDiagList = @"ChannelDiagList";
        /// <summary>name of the ChannelDiagItem element</summary>
        public const string s_ChannelDiagItem = @"ChannelDiagItem";
        /// <summary>name of the UnitDiagTypeList element</summary>
        public const string s_UnitDiagTypeList = @"UnitDiagTypeList";
        /// <summary>name of the UnitDiagTypeItem element</summary>
        public const string s_UnitDiagTypeItem = @"UnitDiagTypeItem";
        /// <summary>name of the GraphicsList element</summary>
        public const string s_GraphicsList = @"GraphicsList";
        /// <summary>name of the GraphicItem element</summary>
        public const string s_GraphicItem = @"GraphicItem";
        /// <summary>name of the Embedded element</summary>
        public const string s_Embedded = @"Embedded";
        /// <summary>name of the CategoryList element</summary>
        public const string s_CategoryList = @"CategoryList";
        /// <summary>name of the CategoryItem element</summary>
        public const string s_CategoryItem = @"CategoryItem";
        /// <summary>name of the ExternalTextList element</summary>
        public const string s_ExternalTextList = @"ExternalTextList";
        /// <summary>name of the PrimaryLanguage element</summary>
        public const string s_PrimaryLanguage = @"PrimaryLanguage";
        /// <summary>name of the Language element</summary>
        public const string s_Language = @"Language";
        /// <summary>name of the Text element</summary>
        public const string s_Text = @"Text";
        /// <summary>name of the Ref element</summary>
        public const string s_Ref = @"Ref";
        /// <summary>name of the Const element</summary>
        public const string s_Const = @"Const";
        /// <summary>name of the DeviceManager element</summary>
        public const string s_DeviceManager = @"DeviceManager";
        /// <summary>name of the ExternalProfileHandle element</summary>
        public const string s_ExternalProfileHandle = @"ExternalProfileHandle";

        #endregion

        //########################################################################################
        #region GSDML V2.0 Elements
        // Contains all element names as constants

        /// <summary>name of the SystemDefinedSubmoduleList element</summary>
        public const string s_SystemDefinedSubmoduleList = @"SystemDefinedSubmoduleList";
        /// <summary>name of the InterfaceSubmoduleItem element</summary>
        public const string s_InterfaceSubmoduleItem = @"InterfaceSubmoduleItem";
        /// <summary>name of the PortSubmoduleItem element</summary>
        public const string s_PortSubmoduleItem = @"PortSubmoduleItem";
        /// <summary>name of the RT_Class3Properties element</summary>
        public const string s_RTClass3Properties = @"RT_Class3Properties";
        /// <summary>name of the IsochroneMode element</summary>
        public const string s_IsochroneMode = @"IsochroneMode";
        /// <summary>name of the SynchronisationMode element</summary>
        public const string s_SynchronisationMode = @"SynchronisationMode";
        /// <summary>name of the F_ParameterRecordData element</summary>
        public const string s_FParameterRecordDataItem = @"F_ParameterRecordDataItem";
        /// <summary>name of the SubslotList element</summary>
        public const string s_SubslotList = @"SubslotList";
        /// <summary>name of the SubslotItem element</summary>
        public const string s_SubslotItem = @"SubslotItem";
        /// <summary>name of the RT_Class3TimingProperties element</summary>
        public const string s_RTClass3TimingProperties = @"RT_Class3TimingProperties";
        /// <summary>name of the BitDataItem element</summary>
        public const string s_BitDataItem = @"BitDataItem";
        /// <summary>name of the DeviceSpecificTool element</summary>
        public const string s_DeviceSpecificTool = @"DeviceSpecificTool";
        /// <summary>name of the ExtChannelDiagList element</summary>
        public const string s_ExtChannelDiagList = @"ExtChannelDiagList";
        /// <summary>name of the ExtChannelDiagItem element</summary>
        public const string s_ExtChannelDiagItem = @"ExtChannelDiagItem";
        /// <summary>name of the SystemDefinedChannelDiagList element</summary>
        public const string s_SystemDefinedChannelDiagList = @"SystemDefinedChannelDiagList";
        /// <summary>name of the SystemDefinedChannelDiagItem dummy element</summary>
        public const string s_SystemDefinedChannelDiagItem = @"SystemDefinedChannelDiagItem";
        /// <summary>name of the F_Check_iPar element</summary>
        public const string s_FCheckIPar = @"F_Check_iPar";
        /// <summary>name of the F_SIL element</summary>
        public const string s_FSil = @"F_SIL";
        /// <summary>name of the F_CRC_Length element</summary>
        public const string s_FCrcLength = @"F_CRC_Length";
        /// <summary>name of the F_Block_ID element</summary>
        public const string s_FBlockID = @"F_Block_ID";
        /// <summary>name of the F_Par_Version element</summary>
        public const string s_FParVersion = @"F_Par_Version";
        /// <summary>name of the F_Source_Add element</summary>
        public const string s_FSourceAdd = @"F_Source_Add";
        /// <summary>name of the F_Dest_Add element</summary>
        public const string s_FDestAdd = @"F_Dest_Add";
        /// <summary>name of the F_WD_Time element</summary>
        public const string s_FWdTime = @"F_WD_Time";
        /// <summary>name of the F_Par_CRC element</summary>
        public const string s_FParCrc = @"F_Par_CRC";

        #endregion

        #region GSDML V2.1 Elements

        /// <summary>name of the F_iPar_CRC element</summary>
        public const string s_FIParCrc = @"F_iPar_CRC";

        public const string s_UseableSubmodules = @"UseableSubmodules";

        public const string s_SubmoduleList = @"SubmoduleList";

        public const string s_SubmoduleItem = @"SubmoduleItem";

        public const string s_SubmoduleItemRef = @"SubmoduleItemRef";

        public const string s_SlotGroups = @"SlotGroups";

        public const string s_SlotGroup = @"SlotGroup";

        public const string s_SlotList = @"SlotList";

        public const string s_SlotItem = @"SlotItem";

        public const string s_MediaRedundancy = @"MediaRedundancy";
        
        public const string s_ExtChannelAddValue = @"ExtChannelAddValue";

        public const string s_ProfileUnitDiagTypeItem = @"ProfileUnitDiagTypeItem";

        public const string s_ProfileExtChannelDiagItem = @"ProfileExtChannelDiagItem";

        public const string s_ProfileChannelDiagItem = @"ProfileChannelDiagItem";

        #endregion

        #region GSDML V2.2 Elements

        /// <summary>name of the BlockVersions element</summary>
        public const string s_BlockVersions = @"BlockVersions";

        #endregion

        #region GSDML V2.25 Elements

        /// <summary>name of the PROFIenergy element</summary>
        public const string s_ProfIenergy = @"PROFIenergy";
        public const string s_FieldbusIntegrationSlots = @"FieldbusIntegrationSlots";

        #endregion

        #region GSDML V2.3 Elements

        /// <summary>name of the SystemRedundancy element</summary>
        public const string s_SystemRedundancy = @"SystemRedundancy";

        /// <summary>name of the MenuList element</summary>
        public const string s_MenuList = @"MenuList";

        /// <summary>name of the MenuItem element</summary>
        public const string s_MenuItem = @"MenuItem";

        /// <summary>name of the MenuRef element</summary>
        public const string s_MenuRef = @"MenuRef";

        /// <summary>name of the ParameterRef element</summary>
        public const string s_ParameterRef = @"ParameterRef";

        /// <summary>name of the SlotCluster element</summary>
        public const string s_SlotCluster = @"SlotCluster";

        /// <summary>name of the LogBookEntryList element</summary>
        public const string s_LogBookEntryList = @"LogBookEntryList";
        /// <summary>name of the LogBookEntryItem element</summary>
        public const string s_LogBookEntryItem = @"LogBookEntryItem";
        /// <summary>name of the ErrorCode2Value element</summary>
        public const string s_ErrorCode2Value = @"ErrorCode2Value";
        /// <summary>name of the ErrorCode2List element</summary>
        public const string s_ErrorCode2List = @"ErrorCode2List";
        /// <summary>name of the ErrorCode2Item element</summary>
        public const string s_ErrorCode2Item = @"ErrorCode2Item";

        /// <summary>name of the ARVendorBlock element</summary>
        public const string s_ArVendorBlock = @"ARVendorBlock";
        /// <summary>name of the Request element</summary>
        public const string s_Request = @"Request";

        /// <summary>name of the CertificationInfo element</summary>
        public const string s_CertificationInfo = @"CertificationInfo";

        #endregion

        #region GSDML V2.31 Elements

        /// <summary>name of the F_CRC_Seed element</summary>
	    public const string s_FCrcSeed = @"F_CRC_Seed";

        /// <summary>name of the F_Passivation element</summary>
        public const string s_FPassivation = @"F_Passivation";

        #endregion

        #region GSDML V2.32 Elements

        public const string s_Channel = @"Channel";
        public const string s_Data = @"Data";
        public const string s_Quality = @"Quality";

        public const string s_MauTypeList = @"MAUTypeList";
        public const string s_MauTypeItem = @"MAUTypeItem";

        #endregion

        #region GSDML V2.33 Elements

        public const string s_TimeSynchronisation = @"TimeSynchronisation";
        public const string s_ReportingSystem = @"ReportingSystem";
        public const string s_ReportingSystemEvents = @"ReportingSystemEvents";
        public const string s_Observer = @"Observer";
        public const string s_EnergySavingModeList = @"EnergySavingModeList";
        public const string s_EnergySavingModeItem = @"EnergySavingModeItem";
        public const string s_MeasurementList = @"MeasurementList";
        public const string s_MeasurementItem = @"MeasurementItem";
        public const string s_MeasurementValue = @"MeasurementValue";
        public const string s_AssetManagement = @"AssetManagement";

        #endregion

        #region GSDML V2.34 Elements

        #endregion

        #region GSDML V2.35 Elements

        public const string s_ChannelProcessAlarmList = @"ChannelProcessAlarmList";
        public const string s_ChannelProcessAlarmItem = @"ChannelProcessAlarmItem";
        public const string s_SystemDefinedChannelProcessAlarmItem = @"SystemDefinedChannelProcessAlarmItem";
        public const string s_ProfileChannelProcessAlarmItem = @"ProfileChannelProcessAlarmItem";
        public const string s_ExtChannelProcessAlarmList = @"ExtChannelProcessAlarmList";
        public const string s_ExtChannelProcessAlarmItem = @"ExtChannelProcessAlarmItem";
        public const string s_ProfileExtChannelProcessAlarmItem = @"ProfileExtChannelProcessAlarmItem";
        public const string s_ProcessAlarmReasonAddValue = @"ProcessAlarmReasonAddValue";

        public const string s_Interconnection = @"Interconnection";

        #endregion

        #region GSDML V2.4 Elements

        public const string WorkingClock = @"WorkingClock";
        public const string GlobalTime = @"GlobalTime";
        public const string CertificationInfoExt = @"CertificationInfoExt";

        #endregion

        #region GSDML V2.41 Elements

        #region Signature

        public const string s_Signature = @"Signature";
        public const string s_SignedInfo = @"SignedInfo";
        public const string s_CanonicalizationMethod = @"CanonicalizationMethod";
        public const string s_SignatureMethod = @"SignatureMethod";
        public const string s_Reference = @"Reference";
        public const string s_Transforms = @"Transforms";
        public const string s_Transform = @"Transform";
        public const string s_DigestMethod = @"DigestMethod";
        public const string s_KeyInfo = @"KeyInfo";
        public const string s_X509Data = @"X509Data";
        public const string s_X509Certificate = @"X509Certificate";
        public const string s_Object = @"Object";
        public const string s_QualifyingProperties = @"QualifyingProperties";
        public const string s_SignedProperties = @"SignedProperties";
        public const string s_SignedSignatureProperties = @"SignedSignatureProperties";
        public const string s_SigningTime = @"SigningTime";
        public const string s_SigningCertificateV2 = @"SigningCertificateV2";
        public const string s_Cert = @"Cert";
        public const string s_CertDigest = @"CertDigest";
        public const string s_DigestValue = @"DigestValue";
        public const string s_IssuerSerialV2 = @"IssuerSerialV2";

        #endregion

        public const string s_ProfileProcessAutomation = @"ProfileProcessAutomation";

        #endregion

        #region GSDML V2.43 Elements

        public const string s_AplPortClassification = @"APLPortClassification";
        public const string s_FWdTime2 = @"F_WD_Time_2";
        public const string s_ParameterRecordDataRef = @"ParameterRecordDataRef";

        #region Communication Interface Modules

        public const string s_CommunicationInterfaceModule = @"CommunicationInterfaceModule";
        public const string s_CommunicationInterfaceList = @"CommunicationInterfaceList";
        public const string s_CommunicationInterfaceItem = @"CommunicationInterfaceItem";
        public const string s_CimInterface = @"CIM_Interface";
        public const string s_CimSupportedRecords = @"CIM_SupportedRecords";
        public const string s_CimResources = @"CIM_Resources";
        public const string s_CimSupportedFeatures = @"CIM_SupportedFeatures";

        #region Protection

        public const string s_Protection = @"Protection";
        public const string s_KeyDerivation = @"KeyDerivation";
        public const string s_KeyAgreement = @"KeyAgreement";
        public const string s_DigitalSignature = @"DigitalSignature";
        public const string s_StreamProtection = @"StreamProtection";
        public const string s_AlarmProtection = @"AlarmProtection";
        public const string s_ConnectionManagementProtection = @"ConnectionManagementProtection";

        #endregion

        #region CertificationInfo

        public const string s_NetloadClasses = @"NetloadClasses";

        #endregion

        #endregion

        #endregion

        #region GSDML V2.44 Elements

        public const string s_FBaseIDRecordDataItem = @"F_BaseIDRecordDataItem";
        public const string s_FBaseID = @"F_BaseID";
        public const string s_FBaseID_CRC = @"F_BaseID_CRC";

        #endregion

        #region GSDML V2.45 Elements

        public const string s_AvailableRecordDataList = @"AvailableRecordDataList";
        public const string s_RecordDataRef = @"RecordDataRef";

        #region Channel

        public const string s_DataRef = @"DataRef";

        #endregion

        #endregion
    }
}


