/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: PNChecker.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using System.Collections.Generic;
using System.Linq;
using PNConfigLib.DataModel.PCLObjects;

namespace PNConfigLib.Consistency
{
    internal class PNChecker
    {
        //This dictionary keeps the objects to
        private Dictionary<int, PclObject> m_ObjectsToCheck = new Dictionary<int, PclObject>();

        private int m_DefaultStepNumber = 1000;
        public PNChecker()
        {
            m_ObjectsToCheck.Clear();
        }

        /// <summary>
        /// Check all consistency 
        /// </summary>
        /// <returns>Logs concerning the ConsistencyChecks </returns>
        internal bool CheckPNConsistency()
        {
            foreach (KeyValuePair<int, PclObject> orderedObject in m_ObjectsToCheck.OrderBy(i => i.Key))
            {
                Interface objectInterface = orderedObject.Value.GetInterface();
                // If PNIOD is not connected to IOSystem, then do not check its consistency.
                if ((objectInterface?.PNIOD != null) && (objectInterface.PNIOD.IOSystem == null))
                {
                    continue;
                }
                orderedObject.Value.BaseActions.CallAllConsistencyChecks();
            }
            if (ConsistencyLogger.ConsistencyLogs.Any(msg => msg.Severity == LogSeverity.Error))
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// Registers a Consistency Check method into Consistency Manager to be run in future.
        /// </summary>
        /// <param name="objectToRegister"></param>
        /// <param name="methodCallback"></param>
        /// <param name="stepNumber">Optional. The Step Number of the consistency Check</param>
        internal void RegisterConsistencyCheck(
            PclObject objectToRegister,
            PclObject.Actions.MethodConsistencyCallback methodCallback,
            int stepNumber = -1)
        {
            //If step number is not specified increment from the default step numbers.
            if (stepNumber == -1)
            {
                stepNumber = m_DefaultStepNumber;
                m_DefaultStepNumber++;
            }
            objectToRegister.BaseActions.RegisterConsistencyMethod(methodCallback);
            if (!m_ObjectsToCheck.ContainsValue(objectToRegister))
            {
                m_ObjectsToCheck.Add(stepNumber, objectToRegister);
            }
        }
    }

}
