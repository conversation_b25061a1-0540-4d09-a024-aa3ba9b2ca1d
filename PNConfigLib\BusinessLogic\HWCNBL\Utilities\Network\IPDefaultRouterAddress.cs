/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPDefaultRouterAddress.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Network
{
    /// <summary>
    /// Summary description for IPDefaultRouterAddress.
    /// </summary>
    public class IPDefaultRouterAddress : IPAddressBase
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)


        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as string</param>
        /// <param name="node"></param>
        public IPDefaultRouterAddress(string address, DataModel.PCLObjects.Node node) : base(address, node)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPDefaultRouterAddress;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="node">The node.</param>
        public IPDefaultRouterAddress(DataModel.PCLObjects.Node node)
        {
            this.OwnAttributeName = InternalAttributeNames.NodeIPDefaultRouterAddress;
            this.ReadFromNode(node);
        }

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class		
        /// <summary>
        /// Check if the default router address fits to ip address
        /// </summary>
        /// <param name="address">ip address</param>
        /// <param name="subnetMask">subnet mask</param>
        /// <returns></returns>
        public IPAddressErrorCodes CheckAgainstAddresses(IPAddressBase address, IPSubnetMask subnetMask)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }
            if (subnetMask == null)
            {
                throw new ArgumentNullException(nameof(subnetMask));
            }
            if (!IsInitialized || !address.IsInitialized || !subnetMask.IsInitialized)
            {
                return IPAddressErrorCodes.None;
            }
            if (AsInt64 == address.AsInt64)
            {
                return IPAddressErrorCodes.DefaultRouterSameAsAddress; // Address may not be equal to router address
            }
            if (address.AddressClass != IPAddressBaseAddressClasses.ClassDorE)
                //ip address class other then D or E (A B C)
            {
                //check if net part of ip address differ from net part of default router address
                if (subnetMask.GetNetAddress(address) != subnetMask.GetNetAddress(this))
                {
                    return IPAddressErrorCodes.DefaultRoutersNetOtherThenAddressesNet;
                }
            }
            return IPAddressErrorCodes.None;
        }

        #endregion
    }
}