/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: SerializationException.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Runtime.Serialization;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Custom Exception for the GSD Interpreter Component.
    /// It does not add any special functionality to the base exception.
    /// This kind of exeception is thrown, when a possible serialization problem by the data object 
    /// model will occur, e.g. the export of the data object model objects.
    /// </summary>
    [Serializable]
    public class SerializationException : Exception
    {
        //########################################################################################
        #region Initialization & Termination
        /// <summary>
        /// Default constructor for the SerializationException;
        /// initializes a new instance of the SerializationException class
        /// </summary>
        public SerializationException()
        {
        }

        // The content of the message parameter is intended to be understood by humans. 
        // The caller of this constructor is required to ensure that this string has been 
        // localized for the current system culture.
        // This message takes into account the current system culture.
        /// <summary>
        /// Initializes a new instance of the SerializationException class with a specified error
        /// message.
        /// </summary>
        /// <param name="message">the error message</param>
        public SerializationException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SerializationException class with a specified 
        /// error message and a reference to the inner exception that is the cause of 
        /// this exception.
        /// </summary>
        /// <param name="message">The error message string.</param>
        /// <param name="innerException">The inner exception reference.</param>
        public SerializationException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SerializationException class with serialized data.
        /// This constructor is called during deserialization to reconstitute the exception object 
        /// transmitted over a stream. 
        /// </summary>
        /// <param name="serializationInfo">The object that holds the serialized object data. </param>
        /// <param name="streamingContext">The contextual information about the source or destination.</param>
        protected SerializationException(SerializationInfo serializationInfo, StreamingContext streamingContext)
            : base(serializationInfo, streamingContext)
        {
        }

        #endregion
    }
}


