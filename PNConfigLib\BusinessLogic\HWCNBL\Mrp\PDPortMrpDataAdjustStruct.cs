/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PDPortMrpDataAdjustStruct.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.Mrp
{
    internal class PDPortMrpDataAdjustStruct : ParameterDSSubBlockStruct
    {
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Nested classes
        // Contains all non-public nested classes and locally scoped interface definitions
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Constants and enumerations
        // Contains all constants and enumerations

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private byte[] data = new byte[18];

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        public byte MrpInstance
        {
            set { BufferManager.Write8(data, 1, value); }
            get { return (byte)BufferManager.Read8(data, 1); }
        }

        public byte[] MrpDomainUUID
        {
            set { BufferManager.WriteBuffer(data, 2, value); }
            get { return BufferManager.ReadBuffer(data, 2, 16); }
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Delegates and events

        // Contains all delegate and events

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        public PDPortMrpDataAdjustStruct()
        {
            ParaBlockType = 0x0214;
            ParaBlockVersion = 0x0101;
            AddSubblock(data);
        }



        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class


        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region I... members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Overrides and overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Protected methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Private implementation

        // Contains the private implementation of the class

        #endregion
    }
}
