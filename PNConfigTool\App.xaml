<Application x:Class="PNConfigTool.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:PNConfigTool">
    <Application.Resources>
        <!-- 应用程序级资源，参考西门子Step7 Micro/WIN风格 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 可以在这里添加其他资源字典 -->
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 基本颜色 -->
            <SolidColorBrush x:Key="PrimaryColor" Color="#0078D7"/>
            <SolidColorBrush x:Key="SecondaryColor" Color="#E6E6E6"/>
            <SolidColorBrush x:Key="BackgroundColor" Color="#F0F0F0"/>
            <SolidColorBrush x:Key="BorderColor" Color="#CCCCCC"/>
            
            <!-- 按钮样式 -->
            <Style x:Key="StandardButton" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
                <Setter Property="Foreground" Value="Black"/>
                <Setter Property="Padding" Value="10,5"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
            <!-- 数据网格样式 -->
            <Style x:Key="StandardDataGrid" TargetType="DataGrid">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="RowBackground" Value="White"/>
                <Setter Property="AlternatingRowBackground" Value="{StaticResource SecondaryColor}"/>
                <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderColor}"/>
                <Setter Property="VerticalGridLinesBrush" Value="{StaticResource BorderColor}"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
            </Style>

            <!-- 居中对齐的文本块样式 -->
            <Style x:Key="CenterAlignedTextBlock" TargetType="TextBlock">
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="TextAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>