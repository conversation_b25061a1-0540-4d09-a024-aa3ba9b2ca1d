/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyManager.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Net;

#endregion

namespace PNConfigLib.Consistency
{
    public class ConsistencyManager
    {
        internal static readonly Dictionary<string, ConsistencyCheckDataNodesIE> s_NodeInfos =
            new Dictionary<string, ConsistencyCheckDataNodesIE>();

        private static PNChecker s_PNChecker = new PNChecker();
        private GsdmlChecker m_GSDMLChecker;
        private XsdChecker m_XsdChecker;

        public ConsistencyManager()
        {
            m_XsdChecker = new XsdChecker();
            m_GSDMLChecker = new GsdmlChecker();
            ConsistencyLogger.Reset();
        }

        internal bool ValidatePN()
        {
            return s_PNChecker.CheckPNConsistency();
        }

        internal bool ValidateInputPaths(string configXmlPath, string listOfNodesXmlPath, string topologyXmlPath, out bool topologyExists)
        {
            topologyExists = false;
            return Checker.ConsistencyCheckerUtilities.IsConfigurationFileExist(configXmlPath)
                   && Checker.ListOfNodesChecker.IsListOfNodesFileExist(listOfNodesXmlPath)
                   && Checker.TopologyChecker.isTopologyFileExist(topologyXmlPath, out topologyExists);

        }

        internal bool ValidateXsd(string configXmlPath, string listOfNodesXmlPath, string topologyXmlPath, bool topologyExists)
        {
            bool retval = m_XsdChecker.ValidateXsd(configXmlPath, XsdChecker.ConfigurationXsdPath)
                          && m_XsdChecker.ValidateXsd(listOfNodesXmlPath, XsdChecker.ListOfNodesXsdPath);

            if (topologyExists)
            {
                retval = retval && m_XsdChecker.ValidateXsd(topologyXmlPath, XsdChecker.TopologyXsdPath);
            }

            return retval;
        }

        internal bool AreInputsValid(Configuration configuration, ListOfNodes listOfNodes, Topology topology, string listOfNodesPath)
        {
            Checker.InputChecker inputChecker = new Checker.InputChecker(m_XsdChecker, configuration, listOfNodes, listOfNodesPath, topology);
            return inputChecker.Check();
        }

        public bool ValidateGSDML(string gsdmlRevision, string xmlPath)
        {
            return m_GSDMLChecker.CheckGSDMLConsistency(gsdmlRevision, xmlPath);
        }

        internal static void RegisterConsistencyCheck(PclObject objectToRegister, PclObject.Actions.MethodConsistencyCallback methodCallback, int stepNumber = -1)
        {
            s_PNChecker.RegisterConsistencyCheck(objectToRegister, methodCallback, stepNumber);
        }

        internal List<ConsistencyLog> GetAllMessages()
        {
            return ConsistencyLogger.ConsistencyLogs;
        }
        internal static void Reset()
        {
            s_PNChecker = new PNChecker();
            ConsistencyLogger.Reset();
        }
    }
}