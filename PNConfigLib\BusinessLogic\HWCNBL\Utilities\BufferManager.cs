/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: BufferManager.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Diagnostics;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class BufferManager
    {
        #region Write

        public static void WriteBit(byte[] arr, int byteOffset, int bitOffset, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((val == 0) || (val == 1));
            Debug.Assert((bitOffset >= 0) && (bitOffset <= 7));
            Debug.Assert(byteOffset < arr.Length);

            int mask = 1 << bitOffset;
            val <<= bitOffset;
            val &= mask;
            arr[byteOffset] &= (byte)~mask;
            arr[byteOffset] |= (byte)val;
        }

        public static void WriteBool(byte[] arr, int byteOffset, int bitOffset, bool val)
        {
            WriteBit(arr, byteOffset, bitOffset, val ? 1 : 0);
        }

        public static void WriteBitfieldBased8(byte[] arr, int byteOffset, int bitOffset, int bitLength, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((bitOffset >= 0) && (bitOffset <= 7));
            Debug.Assert(byteOffset < arr.Length);

            int mask = (1 << bitLength) - 1;

            Debug.Assert((val & ~mask) == 0);

            mask <<= bitOffset;
            val <<= bitOffset;
            val &= mask;
            arr[byteOffset] &= (byte)~mask;
            arr[byteOffset] |= (byte)val;
        }

        public static void WriteBitfieldBased16(byte[] arr, int byteOffset, int bitOffset, int bitLength, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((bitOffset >= 0) && (bitOffset <= 15));
            Debug.Assert(byteOffset < arr.Length - 1);

            int mask = (1 << bitLength) - 1;

            Debug.Assert((val & ~mask) == 0);

            mask <<= bitOffset;
            val <<= bitOffset;
            val &= mask;
            int nValue = Read16(arr, byteOffset);
            nValue &= ~mask;
            nValue |= val;
            Write16(arr, byteOffset, nValue);
        }

        public static void WriteBitfieldBased32(byte[] arr, int byteOffset, int bitOffset, int bitLength, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((bitOffset >= 0) && (bitOffset <= 31));
            Debug.Assert(byteOffset < arr.Length - 3);
            Debug.Assert(val >= 0);

            int mask = (1 << bitLength) - 1;

            Debug.Assert((val & ~mask) == 0);

            mask <<= bitOffset;
            val <<= bitOffset;
            val &= mask;
            long nValue = Read32(arr, byteOffset);
            nValue &= ~mask;
            nValue |= (uint)val;
            Write32(arr, byteOffset, (uint)nValue);
        }

        public static void Write8(byte[] arr, int ByteOffset, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((val & 0xFFFFFF00) == 0);
            Debug.Assert(ByteOffset < arr.Length);

            arr[ByteOffset] = (byte)val;
        }

        public static void Write16(byte[] arr, int ByteOffset, int val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert((val & 0xFFFF0000) == 0);
            Debug.Assert(ByteOffset < arr.Length - 1);

            byte LO = (byte)(val & 0x00FF);
            byte HI = (byte)((val & 0x0FF00) >> 8);
            arr[ByteOffset] = HI;
            arr[ByteOffset + 1] = LO;
        }

        public static void Write32(byte[] arr, int ByteOffset, uint val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert(ByteOffset < arr.Length - 3);

            byte LO1 = (byte)((val & 0x00FF0000) >> 16);
            byte HI1 = (byte)((val & 0xFF000000) >> 24);

            byte LO2 = (byte)(val & 0x00FF);
            byte HI2 = (byte)((val & 0xFF00) >> 8);

            arr[ByteOffset] = HI1;
            arr[ByteOffset + 1] = LO1;
            arr[ByteOffset + 2] = HI2;
            arr[ByteOffset + 3] = LO2;
        }

        public static void Write64(byte[] arr, int byteOffset, ulong val)
        {
            if (arr == null)
            {
                return;
            }

            Debug.Assert(byteOffset < arr.Length - 7);

            byte LO1 = (byte)((val & 0x00FF000000000000) >> 48);
            byte HI1 = (byte)((val & 0xFF00000000000000) >> 56);

            byte LO2 = (byte)((val & 0x000000FF00000000) >> 32);
            byte HI2 = (byte)((val & 0x0000FF0000000000) >> 40);

            byte LO3 = (byte)((val & 0x0000000000FF0000) >> 16);
            byte HI3 = (byte)((val & 0x00000000FF000000) >> 24);

            byte LO4 = (byte)(val & 0x00000000000000FF);
            byte HI4 = (byte)((val & 0x000000000000FF00) >> 8);

            arr[byteOffset] = HI1;
            arr[byteOffset + 1] = LO1;
            arr[byteOffset + 2] = HI2;
            arr[byteOffset + 3] = LO2;
            arr[byteOffset + 4] = HI3;
            arr[byteOffset + 5] = LO3;
            arr[byteOffset + 6] = HI4;
            arr[byteOffset + 7] = LO4;
        }

        public static void WriteBuffer(byte[] arr, int ByteOffset, byte[] val)
        {
            if (arr == null)
            {
                return;
            }

            if (val == null)
            {
                return;
            }

            Debug.Assert(ByteOffset <= arr.Length - val.Length);

            Array.Copy(val, 0, arr, ByteOffset, val.Length);
        }

        public static void WriteBuffer(byte[] arr, int ByteOffset, byte[] val, int blocklength)
        {
            if (arr == null)
            {
                return;
            }

            if (val == null)
            {
                return;
            }

            Debug.Assert(ByteOffset <= arr.Length - blocklength);

            Array.Copy(val, 0, arr, ByteOffset, blocklength);
        }

        #endregion

        #region Read

        public static int ReadBit(byte[] arr, int ByteOffset, int BitOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            Debug.Assert((BitOffset >= 0) && (BitOffset <= 7));
            Debug.Assert(ByteOffset < arr.Length);

            return (arr[ByteOffset] >> BitOffset) & 1;
        }

        public static bool ReadBool(byte[] arr, int ByteOffset, int BitOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            int ret = ReadBit(arr, ByteOffset, BitOffset);
            return ret == 1;
        }

        public static int ReadBitfieldBased8(byte[] arr, int ByteOffset, int BitOffset, int BitLength)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            int mask = 1 << BitLength;
            mask -= 1;
            mask <<= BitOffset;

            int val = arr[ByteOffset];
            val &= mask;
            val >>= BitOffset;

            return val;
        }

        public static int ReadBitfieldBased16(byte[] arr, int ByteOffset, int BitOffset, int BitLength)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            int mask = 1 << BitLength;
            mask -= 1;
            mask <<= BitOffset;

            int val = Read16(arr, ByteOffset);
            val &= mask;
            val >>= BitOffset;

            return val;
        }

        public static int ReadBitfieldBased32(byte[] arr, int ByteOffset, int BitOffset, int BitLength)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            int mask = 1 << BitLength;
            mask -= 1;
            mask <<= BitOffset;

            long val = Read32(arr, ByteOffset);
            val &= mask;
            val >>= BitOffset;

            return (int)val;
        }

        public static int Read8(byte[] arr, int ByteOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            return arr[ByteOffset];
        }

        public static int Read16(byte[] arr, int ByteOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            int HI = arr[ByteOffset];
            int LO = arr[ByteOffset + 1];
            return (HI << 8) + LO;
        }

        public static uint Read32(byte[] arr, int ByteOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            uint HI1 = arr[ByteOffset];
            uint LO1 = arr[ByteOffset + 1];
            uint HI2 = arr[ByteOffset + 2];
            uint LO2 = arr[ByteOffset + 3];

            return (((HI1 << 8) + LO1) << 16) + (HI2 << 8) + LO2;
        }

        public static ulong Read64(byte[] arr, int byteOffset)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            uint HI1 = arr[byteOffset];
            uint LO1 = arr[byteOffset + 1];
            uint HI2 = arr[byteOffset + 2];
            uint LO2 = arr[byteOffset + 3];
            uint HI3 = arr[byteOffset + 4];
            uint LO3 = arr[byteOffset + 5];
            uint HI4 = arr[byteOffset + 6];
            uint LO4 = arr[byteOffset + 7];

            return (((HI1 << 8) + LO1) << 48) + (((HI2 << 8) + LO2) << 32) + (((HI3 << 8) + LO3) << 16) + (HI4 << 8)
                   + LO4;
        }

        public static byte[] ReadBuffer(byte[] arr, int ByteOffset, int Length)
        {
            if (arr == null)
            {
                throw new ArgumentNullException(nameof(arr));
            }

            Debug.Assert(arr.Length >= ByteOffset + Length);

            byte[] val = new byte[Length];

            Array.Copy(arr, ByteOffset, val, 0, Length);

            return val;
        }

        public static int Alignment(int blocklength, int alignmentValue)
        {
            Debug.Assert(alignmentValue != 0);
            return blocklength % alignmentValue != 0 ? alignmentValue - blocklength % alignmentValue : 0;
        }

        #endregion
    }
}