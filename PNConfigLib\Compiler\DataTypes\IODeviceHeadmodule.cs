/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: IODeviceHeadmodule.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.Compiler.DataTypes
{
    internal class IODeviceHeadmodule : ProjectTreeNodeBase
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public IODeviceHeadmodule(DecentralDevice headmodule)
        {
            Name = AttributeUtilities.GetName(headmodule);
            ClassRID = CompilerConstants.ClassRid.IODevice;
            DecentralDevice = headmodule;
            PCLObject = headmodule;
        }

        #endregion

        private List<IODeviceModuleLean> GenerateDeviceLeanModules()
        {
            List<IODeviceModuleLean> leanModules = new List<IODeviceModuleLean>();
            IODeviceModuleLean deviceLeanModule;

            if (DecentralDevice.GetElements().Any(
                element => (SharedIoAssignment)element.AttributeAccess.GetAnyAttribute<uint>(
                               InternalAttributeNames.SharedIoAssignment,
                               new AttributeAccessCode(),
                               0) == SharedIoAssignment.None))
            {
                deviceLeanModule = new IODeviceModuleLean(DecentralDevice);
                deviceLeanModule.GenerateChildren();
                leanModules.Add(deviceLeanModule);
            }
            foreach (Module leanModule in DecentralDevice.GetModules())
            {
                if ((SharedIoAssignment)leanModule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.SharedIoAssignment,
                        new AttributeAccessCode(),
                        0) == SharedIoAssignment.None)
                {
                    deviceLeanModule = new IODeviceModuleLean(leanModule);
                    deviceLeanModule.GenerateChildren();
                    leanModules.Add(deviceLeanModule);
                }
            }
            return leanModules;
        }

        private IODeviceNetworkParameters GenerateNetworkParameters()
        {
            IODeviceNetworkParameters networkParameters = new IODeviceNetworkParameters(DecentralDevice);
            return networkParameters;
        }

        //########################################################################################

        #region Constants and Enums         

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        private DecentralDevice DecentralDevice { get; }

        [SerializationProperties(Tag = "GSDMLFile", IsParent = false)]
        public string GsdFileName
        {
            get
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                string name = DecentralDevice.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.GSDFileName,
                    ac,
                    "");
                if (ac.IsOkay
                    && !string.IsNullOrEmpty(name))
                {
                    return name;
                }
                return "";
            }
        }

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition
        public override void GenerateChildren()
        {
            ChildNodes.Add(GenerateNetworkParameters());
            ChildNodes.AddRange(GenerateDeviceLeanModules());
        }

        public override void Compile()
        {
            Variables.Add(
                CompileUtility.GetKey(DecentralDevice.GetInterface().PNIOD, CompilerConstants.Keys.StationNumber));
            Variables.Add(CompileUtility.GetDeactivatedConfig((DecentralDevice)PCLObject));
            Variables.Add(CompileUtility.GetLaddr(PCLObject));
            Variables.Add(CompileUtility.GetIoDevParamConfig(DecentralDevice));
            base.Compile();
        }

        #endregion
    }
}