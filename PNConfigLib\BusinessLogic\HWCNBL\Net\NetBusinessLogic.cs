/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NetBusinessLogic.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.HWCNBL.Net
{
    /// <summary>
    /// The business logic class for Subnet.
    /// </summary>
    internal class NetBusinessLogic : INetBL, IBusinessLogic
    {
        /// <summary>
        /// The constructor for NetBusinessLogic.
        /// </summary>
        /// <param name="subnet">The subnet.</param>
        protected NetBusinessLogic(Subnet subnet)
        {
            Subnet = subnet;
            Initialize();
        }

        /// <summary>
        /// Gets the net type of the subnet.
        /// </summary>
        public int NetType
        {
            get
            {
                return Subnet.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.NetType,
                    new AttributeAccessCode(),
                    0);
            }
        }

        /// <summary>
        /// The Subnet data model object of this business class object.
        /// </summary>
        public Subnet Subnet { get; }

        /// <summary>
        /// Fills the corresponding PROFINET attributes from the PNConfigLib configuration XML.
        /// </summary>
        /// <param name="xmlSubnet">The XML object of the SyncDomain.</param>
        public void Configure(ConfigReader.Configuration.Subnet xmlSubnet)
        {
            Subnet.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, xmlSubnet.SubnetID);
        }

        #region Constants and Enums
        #endregion

        #region Construction/Destruction/Initialisation

        /// <summary>
        /// Initializes the BL class.
        /// </summary>
        private void Initialize()
        {
            // set net type
            Subnet.AttributeAccess.AddAnyAttribute(InternalAttributeNames.NetType, NetType);
        }

        #endregion

        #region Private Implementation

        #endregion

        #region Public Methods

        // Contains all public methods of the class

        #endregion
    }
}