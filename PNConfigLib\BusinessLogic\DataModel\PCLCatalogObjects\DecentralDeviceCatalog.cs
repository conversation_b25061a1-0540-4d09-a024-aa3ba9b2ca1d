/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceCatalog.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.Gsd.Interpreter.Common;
using System;
using System.Collections.Generic;

#endregion

namespace PNConfigLib.DataModel.PCLCatalogObjects
{
    /// <summary>
    /// The catalog object for a decentral device, containing hardware specific information.
    /// </summary>
    public class DecentralDeviceCatalog : PclCatalogObject
    {
        /// <summary>
        /// Available slot information in this decentral device.
        /// </summary>
        public Array AvailableSlots;

        /// <summary>
        /// Available subslot information in this decentral device.
        /// </summary>
        public Array AvailableSubSlots;

        /// <summary>
        /// The list of module catalog objects that can be used in this decentral device.
        /// </summary>
        public Dictionary<string, SlotRelation> PluggableModuleList = new Dictionary<string, SlotRelation>();

        /// <summary>
        /// The list of submodule catalog objects that can be used in this decentral device.
        /// </summary>
        public Dictionary<string, SlotRelation> PluggableSubmoduleList = new Dictionary<string, SlotRelation>();

        /// <summary>
        /// The list of physical subslots that can be used in this decentral device.
        /// </summary>
        public List<uint> PhysicalSubslotList = new List<uint>();

        /// <summary>
        /// The list of virtual submodule catalog objects that can be used in this decentral device.
        /// </summary>
        public List<SubmoduleCatalog> VirtualSubmoduleList = new List<SubmoduleCatalog>();

        /// <summary>
        /// The list of system defined submodule catalog objects that can be used in this decentral device.
        /// </summary>
        public List<SystemDefinedSubmoduleObject> SystemDefinedSubmoduleList = new List<SystemDefinedSubmoduleObject>();

        /// <summary>
        /// The interface catalog object contained within this decentral device type.
        /// </summary>
        public InterfaceCatalog Interface { get; set; }
    }
}