/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: MediaRedundancy.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: MediaRedundancy.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */

#region Namespaces
using System;
using System.Collections;
using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// This object contains information about the media redundancy 
    /// implemented in an IO Device. T
    /// he version information properties are 
    /// needed to check, if the structure of the connect PDU complies with 
    /// the functionality of an IO Device. 
    /// The engineering tool has to fill in the version information into 
    /// the connect PDU with this properties.
    /// </summary>
    //[ComVisible(true), Guid("38B6CEC9-BDC1-4f05-9BC2-33992F83BC0E")] 
    public class MediaRedundancy : 
        GsdObject,
        IMediaRedundancy
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the MediaRedundancy object if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public MediaRedundancy()
        {
            // GSDML V2.1
            m_RT_MediaRedundancySupported = false;
            m_IsMrpSupported = false;

            // GSDML V2.25
            m_AdditionalMRPProtocolsSupported = false;

            // GSDML V2.3
            m_MrpdSupported = false;
            m_MrtSupported = false;

            // GSDML V2.31
            m_MaxMRPInstances = Attributes.s_DefaultMaxMrpInstances;
            m_IsSupportedMultipleRoles = false;
            m_IsMaxMRPInstances = false;

            // GSDML V2.33
            m_AdditionalForwardingRulesSupported = false;
        }


        #endregion

        //########################################################################################
        #region Fields
        
        // V2.1
        private bool m_RT_MediaRedundancySupported;
        private ArrayList m_MRSupportedRoles;
        private bool m_IsMrpSupported;

        // V2.25
        private bool m_AdditionalMRPProtocolsSupported;

        // V2.3
        private bool m_MrpdSupported;
        private bool m_MrtSupported;

        // V2.31
        private ArrayList m_SupportedMultipleRoles;
        private uint m_MaxMRPInstances;
        private bool m_IsSupportedMultipleRoles;
        private bool m_IsMaxMRPInstances;

        // V2.33
        private bool m_AdditionalForwardingRulesSupported;


        // GSDML V2.35
        private Interconnection m_Interconnection;

        #endregion

        //########################################################################################
        #region Properties


        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsRT_MediaRedundancySupported => m_RT_MediaRedundancySupported;

        /// <summary>
        /// ...
        /// </summary>

        public virtual Array MR_SupportedRoles =>
            m_MRSupportedRoles?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxMRP_Instances => m_MaxMRPInstances;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMaxMRP_Instances => m_IsMaxMRPInstances;

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array MR_SupportedMultipleRoles =>
            m_SupportedMultipleRoles?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMR_SupportedMultipleRoles => m_IsSupportedMultipleRoles;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMrpSupported => m_IsMrpSupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMrpdSupported => m_MrpdSupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMrtSupported => m_MrtSupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool AdditionalMrpProtocolsSupported => m_AdditionalMRPProtocolsSupported;

        /// <summary>
        /// ...
        /// </summary>
        public bool AdditionalForwardingRulesSupported => this.m_AdditionalForwardingRulesSupported;

        /// <summary>
        /// Accesses the Interconnection object, which defines the timing 
        /// behavior for sending cyclic IO data.
        /// </summary>
        public virtual Interconnection Interconnection => this.m_Interconnection;

#if !S7PLUS
        #region COM Interface Members Only


        #endregion
#endif

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            // Check parameter
            if (null == hash)
                throw new FillException("The input hash table parameter couldn't be 'null'!");

			try
			{
                // Own data.
                FillFieldIsRT_MediaRedundancySupported(hash);

                FillFieldMRSupportedRoles(hash);

                FillFieldIsMrpSupported(hash);

                FillFieldSupportedMultipleRoles(hash);

                FillFieldIsSupportedMultipleRoles(hash);

                FillFieldMaxMRPInstances(hash);

                FillFieldIsMaxMRPInstances(hash);

                FIllFieldAdditionalMrpProtocolsSupported(hash);

                FillFieldMrpdSupported(hash);

                FillFieldMrtSupported(hash);

                FillFieldAdditionalForwardingRulesSupported(hash);

                FillFieldInterconnection(hash);
                

            }
			catch(FillException)
			{
				succeeded = false;
			}


            return succeeded;
        }

        private void FillFieldInterconnection(Hashtable hash)
        {
            string member = Models.s_FieldInterconnection;
            if (hash.ContainsKey(member)
                && hash[member] is Interconnection)
                m_Interconnection = hash[member] as Interconnection;
        }

        private void FillFieldAdditionalForwardingRulesSupported(Hashtable hash)
        {
            string member = Models.s_FieldAdditionalForwardingRulesSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_AdditionalForwardingRulesSupported = (bool)hash[member];
        }

        private void FillFieldMrtSupported(Hashtable hash)
        {
            string member = Models.s_FieldMrtSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_MrtSupported = (bool)hash[member];
        }

        private void FillFieldMrpdSupported(Hashtable hash)
        {
            string member = Models.s_FieldMrpdSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_MrpdSupported = (bool)hash[member];
        }

        private void FIllFieldAdditionalMrpProtocolsSupported(Hashtable hash)
        {
            string member = Models.s_FieldAdditionalMrpProtocolsSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_AdditionalMRPProtocolsSupported = (bool)hash[member];
        }

        private void FillFieldIsMaxMRPInstances(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxMrpInstances;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxMRPInstances = (bool)hash[member];
        }

        private void FillFieldMaxMRPInstances(Hashtable hash)
        {
            string member = Models.s_FieldMaxMrpInstances;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxMRPInstances = (uint)hash[member];
        }

        private void FillFieldIsSupportedMultipleRoles(Hashtable hash)
        {
            string member = Models.s_FieldIsSupportedMultipleRoles;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsSupportedMultipleRoles = (bool)hash[member];
        }

        private void FillFieldSupportedMultipleRoles(Hashtable hash)
        {
            string member = Models.s_FieldSupportedMultipleRoles;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedMultipleRoles = hash[member] as ArrayList;
        }

        private void FillFieldIsMrpSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsMrpSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMrpSupported = (bool)hash[member];
        }

        private void FillFieldMRSupportedRoles(Hashtable hash)
        {
            string member = Models.s_FieldMrSupportedRoles;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_MRSupportedRoles = hash[member] as ArrayList;
        }

        private void FillFieldIsRT_MediaRedundancySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsRTMediaRedundancySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_RT_MediaRedundancySupported = (bool)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
        { 
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectMediaRedundancy);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true; 
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
        { 
            // ----------------------------------------------
			Export.WriteBooleanProperty(ref writer, Models.s_FieldIsRTMediaRedundancySupported, m_RT_MediaRedundancySupported);
			Export.WriteArrayStringProperty(ref writer, Models.s_FieldMrSupportedRoles, new ArrayList(m_MRSupportedRoles),  true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedMultipleRoles, new ArrayList(m_SupportedMultipleRoles), true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsSupportedMultipleRoles, m_IsSupportedMultipleRoles);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMrpSupported, m_IsMrpSupported);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxMrpInstances, m_MaxMRPInstances);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxMrpInstances, m_IsMaxMRPInstances);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldMrpdSupported, m_MrpdSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldMrtSupported, m_MrtSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldAdditionalMrpProtocolsSupported, m_AdditionalMRPProtocolsSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldAdditionalForwardingRulesSupported, m_AdditionalForwardingRulesSupported);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInterconnection, this.m_Interconnection);

            return true; 
        }


        #endregion
    }
}
