/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_04.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections;
using System.Xml.XPath;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV0204 :
        BuilderV02035
    {

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV0204()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version24);
        }

        #endregion


        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectTimeSynchronisation:
                        {
                            // NOTE: Navigator must point to TimeSynchronisation.
                            this.PrepareTimeSynchronisation(nav, ref hash);
                            obj = new C.TimeSynchronisation();

                            break;
                        }
                    case Models.s_ObjectWorkingClock:
                        {
                            // NOTE: Navigator must point to WorkingClock.
                            this.PrepareWorkingClock(nav, ref hash);
                            obj = new C.WorkingClock();

                            break;
                        }
                    case Models.s_ObjectGlobalTime:
                        {
                            // NOTE: Navigator must point to GlobalTime.
                            this.PrepareGlobalTime(nav, ref hash);
                            obj = new C.TimeSynchronisation();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation


        protected override void PrepareSystemRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDeviceTypes, null);

            // Call base class method first.
            base.PrepareSystemRedundancy(nav, ref hash);

            ArrayList listDeviceTypes = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_DeviceTypes, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsRedundancyDeviceTypeEnumValueConvertable(type))
                    {
                        listDeviceTypes.Add(Enums.ConvertRedundancyDeviceTypeEnum(type));
                    }
                }
                hash[Models.s_FieldDeviceTypes] = listDeviceTypes;
            }
        }

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldTimeSynchronisation, null);
            hash.Add(Models.s_FieldSupportedServiceProtocols, null);
            hash.Add(Models.s_FieldProfIsafePirSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            ArrayList listSupportedServiceProtocols = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SupportedServiceProtocols, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsServiceProtocolsEnumValueConvertable(type))
                    {
                        listSupportedServiceProtocols.Add(Enums.ConvertServiceProtocolsEnum(type));
                    }
                }
                hash[Models.s_FieldSupportedServiceProtocols] = listSupportedServiceProtocols;
            }
            if (listSupportedServiceProtocols.Count == 0)
                listSupportedServiceProtocols.Add(GSDI.ServiceProtocols.GSDSrvProtCLRPC);

            // Navigate to TimingProperties. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_TimeSynchronisation, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectTimeSynchronisation, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectTimeSynchronisation + "' couldn't be created!");
            }
            hash[Models.s_FieldTimeSynchronisation] = obj;

            attr = nav.GetAttribute(Attributes.s_ProfIsafePirSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafePirSupported);
        }

        protected override void PrepareMauTypeItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSupportedFeatures, null);

            // Call base class method first.
            base.PrepareMauTypeItem(nav, ref hash);

            ArrayList listSupportedFeatures = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SupportedFeatures, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsSupportedFeaturesEnumValueConvertable(type))
                    {
                        listSupportedFeatures.Add(Enums.ConvertSupportedFeaturesEnum(type));
                    }
                }
                hash[Models.s_FieldSupportedFeatures] = listSupportedFeatures;
            }
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSFPDiagnosisSupported, null);

            // Call base class method first.
            base.PrepareDeviceAccessPoint(nav, ref hash);

            ArrayList listSFPDiagnosisSupported = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SfpDiagnosisSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsSFPDiagnosisEnumValueConvertable(type))
                    {
                        listSFPDiagnosisSupported.Add(Enums.ConvertSFPDiagnosisEnum(type));
                    }
                }
                hash[Models.s_FieldSFPDiagnosisSupported] = listSFPDiagnosisSupported;
            }
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSfpDiagnosisMonitoring, null);
            hash.Add(Models.s_FieldProfIsafePirSupported, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            ArrayList listSFPDiagnosisMonitoring = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SfpDiagnosisMonitoring, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsSFPDiagnosisEnumValueConvertable(type))
                    {
                        listSFPDiagnosisMonitoring.Add(Enums.ConvertSFPDiagnosisEnum(type));
                    }
                }
                hash[Models.s_FieldSfpDiagnosisMonitoring] = listSFPDiagnosisMonitoring;
            }

            attr = nav.GetAttribute(Attributes.s_ProfIsafePirSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafePirSupported);
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfIsafePirSupported, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_ProfIsafePirSupported, String.Empty);
            hash[Models.s_FieldProfIsafePirSupported] = Help.GetBool(attr, Attributes.s_DefaultProfIsafePirSupported);
        }

        protected virtual void PrepareTimeSynchronisation(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldWorkingClock, null);
            hash.Add(Models.s_FieldGlobalTime, null);

            // Navigate to WorkingClock. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.WorkingClock, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectWorkingClock, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectWorkingClock + "' couldn't be created!");
            }
            hash[Models.s_FieldWorkingClock] = obj;

            // Navigate to GlobalTime. Optional.
            obj = null;
            nodes = nav.SelectChildren(Elements.GlobalTime, Namespaces.s_GsdmlDeviceProfile);
            // Create element if available.
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectGlobalTime, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectGlobalTime + "' couldn't be created!");
            }
            hash[Models.s_FieldGlobalTime] = obj;
        }

        protected virtual void PrepareWorkingClock(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRoles, null);

            // Get Role attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_Role, String.Empty);
            ArrayList listRoles = new ArrayList();
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList attrList = Help.SeparateTokenList(attr);
                foreach (string role in attrList)
                {
                    if (Enums.IsRoleEnumValueConvertable(role))
                        listRoles.Add(Enums.ConvertRoleEnum(role));
                }
                hash[Models.s_FieldRoles] = listRoles;
            }
        }

        protected virtual void PrepareGlobalTime(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldRoles, null);

            // Get Role attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_Role, String.Empty);
            ArrayList listRoles = new ArrayList();
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList attrList = Help.SeparateTokenList(attr);
                foreach (string role in attrList)
                {
                    if (Enums.IsRoleEnumValueConvertable(role))
                        listRoles.Add(Enums.ConvertRoleEnum(role));
                }
                hash[Models.s_FieldRoles] = listRoles;
            }
        }

        #endregion

        #endregion
    }
}
