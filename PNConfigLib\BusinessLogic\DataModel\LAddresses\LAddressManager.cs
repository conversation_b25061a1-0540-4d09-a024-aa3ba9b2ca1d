/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: LAddressManager.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Linq;

using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.DataModel.LAddresses
{
    /// <summary>
    /// This is the main class used for automatically providing LAddress values to connected devices.
    /// </summary>
    /// <remarks>
    /// Each CentralDevice and DecentralDevice has an AddressManager, which is responsible
    /// for keeping the used LAddress values, registering new LAddresses, reorganizing used
    /// LAddresses and also determining next free LAddresses.
    /// The address assignment logic depends on the uniqueness of all LAdresses belonging to
    /// hardware items that belong to a CentralDevice and all DecentralDevice objects connected
    /// to that CentralDevice.This connection and relation is managed by CentralAddressManager
    /// class which is inherited from AddressManager.
    /// </remarks>
    internal class LAddressManager
    {
        /// <summary>
        /// The minimum value used when assigning LAddresses.
        /// </summary>
        private const long s_MinDynamicAddress = 256;

        /// <summary>
        /// Default for AddressManager that instantiates an empty list for used addresses.
        /// </summary>
        /// <summary>
        /// Gets a single free address.
        /// </summary>
        /// <remarks>
        /// - If this AddressManager is connected to a CentralAddressManager, this means
        /// that this device is connected to a CentralDevice. In this case, get an
        /// unused address by checking the used addresses belonging to every PclObject
        /// of that CentralDevice and all the DecentralDevice objects connected to it.
        /// - If this AddressManager is not connected to a CentralAddressManager, this means
        /// either this is a CentralDevice, or this device is not connected to a CentralDevice.
        /// If former; get the free addresses as explained above. If latter; just check the PclObject
        /// items of the current device and get an unused address based on that.
        /// </remarks>
        /// <exception cref="InvalidOperationException">if there are not enough addresses.</exception>
        /// <returns>A single free address value.</returns>
        private long GetFreeAddress()
        {
            if (LAddressContainer.UsedAddresses != null)
            {
                if (LAddressContainer.UsedAddresses.Count == 0)
                {
                    return s_MinDynamicAddress;
                }
                long lastLAddrValue = LAddressContainer.UsedAddresses.Keys.Last().LAddressValue;
                if (lastLAddrValue  < long.MaxValue)
                {
                    return lastLAddrValue + 1;
                }
                throw new InvalidOperationException("Not enough free addresses. LAddress exceeded long.MaxValue");
            }
            throw new InvalidOperationException("Not enough free addresses.");
        }

        /// <summary>
        /// Gets a list of used address values.
        /// </summary>
        /// <remarks>
        /// If this AddressManager belongs to a CentralDevice or a DecentralDevice
        /// that is connected to a CentralDevice, it gets used addresses of every
        /// PclObject belonging to the CentralDevice and all DecentralDevices
        /// connected to it.
        /// If this AddressManager belongs to a DecentralDevice that isn't connected
        /// to a CentralDevice, it gets used addresses of every PclObject belonging
        /// to this device only.
        /// </remarks>
        /// <returns>A list that contains used address values.</returns>
        /// <summary>
        /// Registers a PclObject with a specified address type.
        /// </summary>
        /// <param name="pclObject">The PclObject that will be registered.</param>
        /// <param name="lAddressType">The type of the address that will be registered.</param>
        public void RegisterPCLObject(PclObject pclObject, LAddressType lAddressType)
        {
            RegisterPCLObject(pclObject, new LAddress(GetFreeAddress(), lAddressType));
        }

        /// <summary>
        /// The internal method that is used for registering a PCLObject.
        /// </summary>
        /// <param name="pclObject">The PclObject that will be registered.</param>
        /// <param name="lAddress">The actual AddressItem object that will be used for registration.</param>
        /// <exception cref="ArgumentException">if the given address value is already in use.</exception>
        private void RegisterPCLObject(PclObject pclObject, LAddress lAddress)
        {
            if (LAddressContainer.UsedAddresses.Count(e => e.Key.LAddressValue == lAddress.LAddressValue) != 0)
            {
                throw new ArgumentException("The address is already used.");
            }

            LAddressContainer.UsedAddresses.Add(lAddress, pclObject);
            SetAddressItemOfPCLObject(pclObject, lAddress);
        }

        /// <summary>
        /// This method is used internally for setting the related properties of a PCLObject item for different address types.
        /// </summary>
        /// <param name="pclObject">The PCLObject whose properties are set.</param>
        /// <param name="lAddress">The address item of the PCLObject.</param>
        /// <exception cref="InvalidOperationException">
        /// if a Rack or Station type of address is assigned to a
        /// PclObject other than DecentralDevice or CentralDevice.
        /// </exception>
        private void SetAddressItemOfPCLObject(PclObject pclObject, LAddress lAddress)
        {
            CentralDevice centralDevice;
            DecentralDevice decentralDevice;
            switch (lAddress.LAddressType)
            {
                case LAddressType.PCLObject:
                    pclObject.LAddress = lAddress;
                    break;
                case LAddressType.Rack:
                    centralDevice = pclObject as CentralDevice;
                    if (centralDevice != null)
                    {
                        centralDevice.RackLAddress = lAddress;
                    }
                    else
                    {
                        decentralDevice = pclObject as DecentralDevice;
                        if (decentralDevice != null)
                        {
                            decentralDevice.RackLAddress = lAddress;
                        }
                        else
                        {
                            throw new InvalidOperationException("A non-device item is assigned rack type address.");
                        }
                    }

                    break;
                case LAddressType.Station:
                    centralDevice = pclObject as CentralDevice;
                    if (centralDevice != null)
                    {
                        centralDevice.StationLAddress = lAddress;
                    }
                    else
                    {
                        decentralDevice = pclObject as DecentralDevice;
                        if (decentralDevice != null)
                        {
                            decentralDevice.StationLAddress = lAddress;
                        }
                        else
                        {
                            throw new InvalidOperationException("A non-device item is assigned station type address.");
                        }
                    }

                    break;
            }
        }
    }
}