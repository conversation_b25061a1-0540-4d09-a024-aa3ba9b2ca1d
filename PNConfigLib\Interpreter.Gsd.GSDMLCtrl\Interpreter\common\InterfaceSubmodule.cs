/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: InterfaceSubmodule.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using GSDI;
using System.Collections.Generic;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The InterfaceSubmodule ...
    /// </summary>
    public class InterfaceSubmodule :
        SystemDefinedSubmoduleObject,
        GSDI.IInterfaceSubmodule,
        IInterfaceSubmodule2,
        IInterfaceSubmodule3,
        IInterfaceSubmodule4,
        IInterfaceSubmodule5,
        IInterfaceSubmodule6,
        IInterfaceSubmodule7,
        ICompatibility
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the InterfaceSubmodule if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public InterfaceSubmodule()
        {
            m_SupportedRTClass = RTClasses.GSDClass1;
            m_SupportedRTClasses = null;

            m_IsIsochroneModeSupported = false;

            m_TPllMax = 0;
            m_MaxBridgeDelay = 0;
            m_MaxNumberIRFrameData = 0;
            m_IsSynchronisationModeSupported = false;
            m_SupportedSynchronisationRole = SynchronisationRoles.GSDSyncSlave;
            m_MaxLocalJitter = 0;
            m_Name = String.Empty;
            m_NameTextID = String.Empty;	// only private!

            m_ApplicationRelations = null;
            m_ParameterRecordData = null;
            m_NetworkComponentDiagnosisSupported = false;
            m_SupportedProtocols = null;
            m_SupportedMibs = null;
            // GSDML V2.2
            m_SupportedSyncProtocols = null;
            m_DcpBoundarySupported = false;
            m_PTPBoundarySupported = false;
            m_DcpHelloSupported = false;
            m_IsochroneModeInRTClasses = null;

            // GSDML V2.25
            m_ParameterizationDisallowed = false;
            m_MulticastBoundarySupported = false;
            m_DelayMeasurementSupported = false;
            m_WriteableImRecords = null;
            m_IsTransferSequenceDefined = false;
            m_ForwardingMode = null;

            // GSDML V2.3
            m_AlignDfpSubframes = Attributes.s_DefaultAlignDfpSubframes;
            m_RtClass3StartupMode = null;
            m_FragmentationType = FragmentationTypes.GSDFragmentationTypeNone;
            m_MaxBridgeDelayFfw = Attributes.s_DefaultMaxBridgeDelayFfw;
            m_IsMaxBridgeDelayFfw = false;
            m_MaxDFPFeed = Attributes.s_DefaultMaxDfpFeed;
            m_IsMaxDFPFeed = false;
            m_MaxDFPFrames = Attributes.s_DefaultMaxDfpFrames;
            m_MaxRangeIrFrameId = Attributes.s_DefaultMaxRangeIrFrameId;
            m_MaxRedPeriodLength = Attributes.s_DefaultMaxRedPeriodLength;
            m_MinFso = Attributes.s_DefaultMinFso;
            m_PeerToPeerJitter = Attributes.s_DefaultPeerToPeerJitter;

            // GSDML V2.31

            m_MinRtc3Gap = Attributes.s_DefaultMinRtc3Gap;
            m_MaxFrameStartTime = Attributes.s_DefaultMaxFrameStartTime;
            m_MinNrtGap = Attributes.s_DefaultMinNrtGap;
            m_MinYellowTime = Attributes.s_DefaultMinYellowTime;
            m_YellowSafetyMargin = Attributes.s_DefaultYellowSafetyMargin;
            m_IsMinRtc3Gap = false;
            m_IsMaxFrameStartTime = false;
            m_IsMinNrtGap = false;
            m_IsMinYellowTime = false;
            m_IsYellowSafetyMargin = false;
            m_PdevCombinedObjectSupported = Attributes.s_DefaultPdevCombinedObjectSupported;

            // GSDML V2.32
            m_IsProfIenergySupported = false;
            m_IsIm5Supported = false;
            m_UsesStaticArpCacheEntries = false;
            m_DFPOutboundTruncationSupported = false;


            // GSDML V2.33
            m_IsOwnIPSetsStandardGateway = false;
            m_DFPRedundantPathLayoutSupported = false;
            m_IsReportingSystem = false;
            m_IsTimeSynchronisation = false;

            // GSDML V2.4
            m_SupportedServiceProtocols = null;
            m_IsPROFIsafePIR_Supported = false;
            m_TimeSynchronisation = null;

            // GSDML V2.42
            m_SupportedDelayMeasurements = null;

            // GSDML V2.43
            m_DCPFeaturesSupported = null;
            m_SNMPFeaturesSupported = null;
            m_APLFeaturesSupported = null;

            // GSDML V2.44
            m_BridgeFeaturesSupported = null;
            m_TSNConfigurationsSupported = null;

            // GSDML V2.45
            m_IsProfisafeFscpTestModeSupported = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        //
        private RTClasses m_SupportedRTClass;
        private ArrayList m_SupportedRTClasses;

        private bool m_IsIsochroneModeSupported;
        //private bool m_IsIsochroneModeRequired;
        private uint m_TPllMax;
        private uint m_MaxBridgeDelay;
        private uint m_MaxNumberIRFrameData;
        private bool m_IsSynchronisationModeSupported;
        private SynchronisationRoles m_SupportedSynchronisationRole;
        private uint m_MaxLocalJitter;
        private string m_Name;
        private string m_NameTextID;
        private ApplicationRelations m_ApplicationRelations;
        private MediaRedundancy m_MediaRedundancy;
        private ArrayList m_ParameterRecordData;

        private bool m_NetworkComponentDiagnosisSupported;
        private ArrayList m_SupportedProtocols;
        private ArrayList m_SupportedMibs;
        // GSDML V2.2
        private ArrayList m_SupportedSyncProtocols;
        private bool m_DcpBoundarySupported;
        private bool m_PTPBoundarySupported;
        private bool m_DcpHelloSupported;

        private ArrayList m_IsochroneModeInRTClasses;


        // GSDML V2.25
        private bool m_ParameterizationDisallowed;
        private bool m_MulticastBoundarySupported;

        private bool m_DelayMeasurementSupported;
        private List<uint> m_WriteableImRecords;
        private bool m_IsTransferSequenceDefined;

        private ArrayList m_ForwardingMode;

        // GSDML V2.3
        private bool m_AlignDfpSubframes;
        private uint m_MaxDFPFeed;
        private bool m_IsMaxDFPFeed;
        private uint m_MaxDFPFrames;
        private uint m_MaxBridgeDelayFfw;
        private bool m_IsMaxBridgeDelayFfw;
        private ArrayList m_RtClass3StartupMode;
        private FragmentationTypes m_FragmentationType;
        private uint m_MaxRangeIrFrameId;
        private uint m_MaxRedPeriodLength;
        private uint m_MinFso;
        private uint m_PeerToPeerJitter;

        // GSDML V2.31
        private uint m_MinRtc3Gap;
        private uint m_MaxFrameStartTime;
        private uint m_MinNrtGap;
        private uint m_MinYellowTime;
        private uint m_YellowSafetyMargin;
        private bool m_IsMinRtc3Gap;
        private bool m_IsMaxFrameStartTime;
        private bool m_IsMinNrtGap;
        private bool m_IsMinYellowTime;
        private bool m_IsYellowSafetyMargin;
        private bool m_PdevCombinedObjectSupported;

        // GSDML V2.32
        private bool m_IsProfIenergySupported;
        private bool m_IsIm5Supported;
        private bool m_UsesStaticArpCacheEntries;
        private bool m_DFPOutboundTruncationSupported;
        private uint m_MaxRetentionTime;
        private bool m_ExistsMaxRetentionTime;

        // GSDML V2.33
        private bool m_IsOwnIPSetsStandardGateway;
        private bool m_DFPRedundantPathLayoutSupported;
        private bool m_IsReportingSystem;
        private bool m_IsTimeSynchronisation;

        // GSDML V2.4
        private TimeSynchronisation m_TimeSynchronisation;
        private ArrayList m_SupportedServiceProtocols;
        private bool m_IsPROFIsafePIR_Supported;


        // GSDML V2.42
        private ArrayList m_SupportedDelayMeasurements;


        // GSDML V2.43
        private ArrayList m_DCPFeaturesSupported;
        private ArrayList m_SNMPFeaturesSupported;
        private ArrayList m_APLFeaturesSupported;

        // GSDML V2.44
        private ArrayList m_BridgeFeaturesSupported;
        private ArrayList m_TSNConfigurationsSupported;

        // GSDML V2.45
        private bool m_IsProfisafeFscpTestModeSupported;
        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// ...
        /// </summary>
        public virtual RTClasses SupportedRTClass => m_SupportedRTClass;

        /// <summary>
		/// ...
		/// </summary>
		public virtual Array SupportedRTClasses =>
            m_SupportedRTClasses?.ToArray();

        /// <summary>
		/// ...
		/// </summary>
		public virtual bool IsIsochroneModeSupported => m_IsIsochroneModeSupported;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 T_PLL_Max => m_TPllMax;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 MaxBridgeDelay => m_MaxBridgeDelay;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 MaxNumberIRFrameData => m_MaxNumberIRFrameData;

        /// <summary>
		/// ...
		/// </summary>
		public virtual bool IsSynchronisationModeSupported => m_IsSynchronisationModeSupported;

        /// <summary>
		/// ...
		/// </summary>
		public virtual SynchronisationRoles SupportedSynchronisationRole => m_SupportedSynchronisationRole;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 MaxLocalJitter => m_MaxLocalJitter;

        /// <summary>
		/// ...
		/// </summary>
		public virtual string Name => m_Name;

        /// <summary>
		/// ...
		/// </summary>
		public virtual string NameTextId => m_NameTextID;

        /// <summary>
        /// Accesses the ApplicationRelations object, which contains information about the 
        /// application relations implemented in an IO Device.
        /// </summary>
        public virtual ApplicationRelations ApplicationRelations => m_ApplicationRelations;

        /// <summary>
        /// Accesses the MediaRedundancy object, which contains information about the 
		/// application relations implemented in an IO Device.
		/// </summary>
        public virtual MediaRedundancy MediaRedundancy => m_MediaRedundancy;

        /// <summary>
		/// Accesses a list of parameter record data objects, which contains
		/// information about the possible parametrization of the submodule.
		/// </summary>
		public virtual Array ParameterRecordData =>
            m_ParameterRecordData?.ToArray();

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsNetworkComponentDiagnosisSupported => m_NetworkComponentDiagnosisSupported;

        /// <summary>
		/// ...
		/// </summary>
		public virtual Array SupportedProtocols =>
            m_SupportedProtocols?.ToArray();

        /// <summary>
		/// ...
		/// </summary>
		public virtual Array SupportedMibs =>
            m_SupportedMibs?.ToArray();

        /// <summary>
		/// ...
		/// </summary>
		public virtual Array SupportedSyncProtocols =>
            m_SupportedSyncProtocols?.ToArray();

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsDCPBoundarySupported => m_DcpBoundarySupported;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsPTPBoundarySupported => m_PTPBoundarySupported;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsDCP_HelloSupported => m_DcpHelloSupported;

        /// <summary>
		/// ...
		/// </summary>
		public virtual Array IsochroneModeInRT_Classes =>
            m_IsochroneModeInRTClasses?.ToArray();

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
        public bool IsParameterizationDisallowed => m_ParameterizationDisallowed;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMulticastBoundarySupported => m_MulticastBoundarySupported;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsDelayMeasurementSupported => m_DelayMeasurementSupported;

        public virtual Array Writeable_IM_Records =>
            (null != m_WriteableImRecords) ?
                new ArrayList(m_WriteableImRecords).ToArray() :

                null;

        /// <summary>
        /// Accesses whether the transfer sequence for the parameter record
        /// data objects is defined.
        /// </summary>
        /// <remarks>There is so, if the transfer sequence of any parameter
        /// record data object is unequal to 0.</remarks>
        public virtual bool IsTransferSequenceDefined()
        {
            return m_IsTransferSequenceDefined;
        }


        /// <summary>
        /// ...
        /// </summary>
        public virtual Array ForwardingMode =>
            m_ForwardingMode?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array RtClass3StartupMode =>
            m_RtClass3StartupMode?.ToArray();

        /// <summary>
	    /// ...
	    /// </summary>
	    public bool AlignDfpSubframes => m_AlignDfpSubframes;

        /// <summary>
        /// ...
        /// </summary>
        public virtual FragmentationTypes FragmentationType => m_FragmentationType;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxBridgeDelayFFW => m_MaxBridgeDelayFfw;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMaxBridgeDelayFFW => m_IsMaxBridgeDelayFfw;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxDfpFeed => m_MaxDFPFeed;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsMaxDfpFeed => m_IsMaxDFPFeed;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxDfpFrames => m_MaxDFPFrames;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxRangeIrFrameID => m_MaxRangeIrFrameId;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxRedPeriodLength => m_MaxRedPeriodLength;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MinFSO => m_MinFso;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 PeerToPeerJitter => m_PeerToPeerJitter;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MinRTC3Gap => m_MinRtc3Gap;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMinRTC3Gap => m_IsMinRtc3Gap;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxFrameStartTime => m_MaxFrameStartTime;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMaxFrameStartTime => m_IsMaxFrameStartTime;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MinNRTGap => m_MinNrtGap;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMinNRTGap => m_IsMinNrtGap;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MinYellowTime => m_MinYellowTime;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMinYellowTime => m_IsMinYellowTime;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 YellowSafetyMargin => m_YellowSafetyMargin;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsYellowSafetyMargin => m_IsYellowSafetyMargin;

        /// <summary>
        /// ...
        /// </summary>
        /// <remarks>....</remarks>
        public bool PDEVCombinedObjectSupported => m_PdevCombinedObjectSupported;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsPROFIenergySupported => this.m_IsProfIenergySupported;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsIM5_Supported => this.m_IsIm5Supported;

        /// <summary>
        /// ...
        /// </summary>
        public bool UsesStaticARP_CacheEntries => this.m_UsesStaticArpCacheEntries;

        /// <summary>
        /// ...
        /// </summary>
        public bool DFP_OutboundTruncationSupported => this.m_DFPOutboundTruncationSupported;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxRetentionTime => m_MaxRetentionTime;

        public bool ExistsMaxRetentionTime => m_ExistsMaxRetentionTime;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsOwnIP_SetsStandardGateway => this.m_IsOwnIPSetsStandardGateway;

        /// <summary>
        /// ...
        /// </summary>
        public bool DFP_RedundantPathLayoutSupported => this.m_DFPRedundantPathLayoutSupported;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsReportingSystem => this.m_IsReportingSystem;

        /// <summary>
        /// ...
        /// </summary>
        public bool IsTimeSynchronisation => this.m_IsTimeSynchronisation;

        /// <summary>
        /// Accesses the TimeSynchronisation object, which contains information about the 
        /// application relations implemented in an IO Device.
        /// </summary>
        public virtual TimeSynchronisation TimeSynchronisation => m_TimeSynchronisation;

        public virtual Array SupportedServiceProtocols =>
            (null != this.m_SupportedServiceProtocols) ?
                m_SupportedServiceProtocols.ToArray() :
                null;

        public bool IsPROFIsafePIR_Supported => this.m_IsPROFIsafePIR_Supported;

        public virtual Array SupportedDelayMeasurements =>
            m_SupportedDelayMeasurements?.ToArray();

        public virtual Array DCP_FeaturesSupported =>
            (null != this.m_DCPFeaturesSupported) ?
                m_DCPFeaturesSupported.ToArray() :
                null;

        public virtual Array SNMP_FeaturesSupported =>
            (null != this.m_SNMPFeaturesSupported) ?
                m_SNMPFeaturesSupported.ToArray() :
                null;

        public virtual Array APL_FeaturesSupported =>
            (null != this.m_APLFeaturesSupported) ?
                m_APLFeaturesSupported.ToArray() :
                null;

        protected new object SubslotNumber => ((SystemDefinedSubmoduleObject)this).SubslotNumber;

        protected new object IdentNumber => ((ModuleObject)this).IdentNumber;

        public virtual Array Bridge_FeaturesSupported =>
            (null != this.m_BridgeFeaturesSupported) ?
                m_BridgeFeaturesSupported.ToArray() :
                null;

        public virtual Array TSN_ConfigurationsSupported =>
            (null != this.m_TSNConfigurationsSupported) ?
                m_TSNConfigurationsSupported.ToArray() :
                null;

        public virtual bool IsPROFIsafeFSCP_TestMode_Supported => this.m_IsProfisafeFscpTestModeSupported;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                FillFieldSupportedRTClass(hash);

                FillFieldIsIsochroneModeSupported(hash);

                //member = Models.FieldIsIsochroneModeRequired;
                //if (hash.ContainsKey(member) && hash[member] is bool)
                //	this.m_IsIsochroneModeRequired = (bool) hash[member];

                FillFieldT_PLL_Max(hash);

                FillFieldMaxBridgeDelay(hash);

                FillFieldIsSynchronisationModeSupported(hash);

                FillFieldSupportedSynchronisationRole(hash);

                FillFieldMaxLocalJitter(hash);

                FillFieldMaxNumberIRFrameData(hash);

                FillFieldName(hash);

                FillFieldNameTextID(hash);

                FillFieldApplicationRelations(hash);

                FillFieldMediaRedundancy(hash);

                FillFieldParameterRecordData(hash);

                FillFieldIsNetworkComponentDiagnosisSupported(hash);

                FillFieldSupportedRTClasses(hash);

                FillFieldSupportedProtocols(hash);

                FillFieldSupportedMibs(hash);

                FillFieldSupportedSyncProtocols(hash);

                FillFieldIsDCPBoundarySupported(hash);

                FillFieldIsPTPBoundarySupported(hash);

                FillFieldIsDCP_HelloSupported(hash);

                FillFieldIsochroneModeInRT_Classes(hash);

                FillFieldIsParameterizationDisallowed(hash);

                FillFieldIsMulticastBoundarySupported(hash);

                FillFieldIsDelayMeasurementSupported(hash);

                FillFieldWriteable_IM_Records(hash);

                FillFieldForwardingMode(hash);

                FillFieldAlignDfpSubframes(hash);

                FillFieldStartupMode(hash);

                FillFieldFragmentationType(hash);

                FillFieldMaxBridgeDelayFfw(hash);

                FillFieldIsMaxBridgeDelayFfw(hash);

                FillFieldMaxDfpFeed(hash);

                FillFieldIsMaxDfpFeed(hash);

                FillFieldMaxDfpFrames(hash);

                FillFieldMaxRangeIrFrameId(hash);

                FillFieldMaxRedPeriodLength(hash);

                FillFieldMinFso(hash);

                FillFieldPeerToPeerJitter(hash);

                FillFieldMinRTC3Gap(hash);

                FillFieldIsMinRTC3Gap(hash);

                FillFieldMaxFrameStartTime(hash);

                FillFieldIsMaxFrameStartTime(hash);

                FillFieldMinNRTGap(hash);

                FillFieldIsMinNRTGap(hash);

                FillFieldMinYellowTime(hash);

                FillFieldIsMinYellowTime(hash);

                FillFieldYellowSafetyMargin(hash);

                FillFieldIsYellowSafetyMargin(hash);

                FillFieldPDEVCombinedObjectSupported(hash);

                FillFieldIsPROFIenergySupported(hash);

                FillFieldIM5_Supported(hash);

                FillFieldUsesStaticARP_CacheEntries(hash);

                FillFieldDFP_OutboundTruncationSupported(hash);

                FillFieldMaxRetentionTime(hash);

                FillFieldIsOwnIP_SetsStandardGateway(hash);

                FillFieldDFP_RedundantPathLayoutSupported(hash);

                FillFieldIsReportingSystem(hash);

                FillFieldIsTimeSynchronisation(hash);

                FillFieldTimeSynchronisation(hash);

                FillFieldSupportedServiceProtocols(hash);

                FillFieldPROFIsafePIR_Supported(hash);

                FillFieldSupportedDelayMeasurements(hash);

                FillFieldDCP_FeaturesSupported(hash);

                FillFieldSNMP_FeaturesSupported(hash);

                FillFieldAPL_FeaturesSupported(hash);

                FillFieldBridge_FeaturesSupported(hash);

                FillFieldTSN_ConfigurationsSupported(hash);

                FillFieldPROFIsafeFSCP_TestMode_supported(hash);


                // Base data.
                succeeded = base.Fill(hash);

                // Get TransferSequence information.
                if (null != m_ParameterRecordData)
                {
                    if (m_ParameterRecordData.Count != 0)
                    {
                        // Remember whether TransferSequence is defined by the ParameterRecords.
                        m_IsTransferSequenceDefined = (((ParameterRecordData)m_ParameterRecordData[0]).TransferSequence != 0);

                        // Sort parameter records by index.
                        SortedList list = new SortedList();
                        foreach (RecordData rec in m_ParameterRecordData)
                            list.Add(rec.Index, rec);
                        m_ParameterRecordData = new ArrayList(list.Values);
                    }
                }

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }
        private void FillFieldBridge_FeaturesSupported(Hashtable hash)
        {
            string member = Models.s_FieldBridge_FeaturesSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_BridgeFeaturesSupported = hash[member] as ArrayList;
        }

        private void FillFieldTSN_ConfigurationsSupported(Hashtable hash)
        {
            string member = Models.s_FieldTSN_ConfigurationsSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_TSNConfigurationsSupported = hash[member] as ArrayList;
        }
        private void FillFieldAPL_FeaturesSupported(Hashtable hash)
        {
            string member = Models.s_FieldAplFeaturesSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_APLFeaturesSupported = hash[member] as ArrayList;
        }

        private void FillFieldSNMP_FeaturesSupported(Hashtable hash)
        {
            string member = Models.s_FieldSnmpFeaturesSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SNMPFeaturesSupported = hash[member] as ArrayList;
        }

        private void FillFieldDCP_FeaturesSupported(Hashtable hash)
        {
            string member = Models.s_FieldDcpFeaturesSupported;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_DCPFeaturesSupported = hash[member] as ArrayList;
        }

        private void FillFieldSupportedDelayMeasurements(Hashtable hash)
        {
            string member = Models.s_FieldSupportedDelayMeasurements;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedDelayMeasurements = hash[member] as ArrayList;
        }

        private void FillFieldPROFIsafePIR_Supported(Hashtable hash)
        {
            string member = Models.s_FieldIsProfIsafeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsPROFIsafePIR_Supported = (bool)hash[member];
        }

        private void FillFieldSupportedServiceProtocols(Hashtable hash)
        {
            string member = Models.s_FieldSupportedServiceProtocols;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedServiceProtocols = hash[member] as ArrayList;
        }

        private void FillFieldTimeSynchronisation(Hashtable hash)
        {
            string member = Models.s_FieldTimeSynchronisation;
            if (hash.ContainsKey(member)
                && hash[member] is TimeSynchronisation)
                m_TimeSynchronisation = hash[member] as TimeSynchronisation;
        }

        private void FillFieldIsTimeSynchronisation(Hashtable hash)
        {
            string member = Models.s_FieldIsTimeSynchronisation;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsTimeSynchronisation = (bool)hash[member];
        }

        private void FillFieldIsReportingSystem(Hashtable hash)
        {
            string member = Models.s_FieldIsReportingSystem;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsReportingSystem = (bool)hash[member];
        }

        private void FillFieldDFP_RedundantPathLayoutSupported(Hashtable hash)
        {
            string member = Models.s_FieldDFPRedundantPathLayoutSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_DFPRedundantPathLayoutSupported = (bool)hash[member];
        }

        private void FillFieldIsOwnIP_SetsStandardGateway(Hashtable hash)
        {
            string member = Models.s_FieldIsOwnIPSetsStandardGateway;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsOwnIPSetsStandardGateway = (bool)hash[member];
        }

        private void FillFieldMaxRetentionTime(Hashtable hash)
        {
            string member = Models.s_FieldMaxRetentionTime;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
            {
                m_MaxRetentionTime = (uint)hash[member];
                m_ExistsMaxRetentionTime = true;
            }
        }

        private void FillFieldDFP_OutboundTruncationSupported(Hashtable hash)
        {
            string member = Models.s_FieldDFPOutboundTruncationSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_DFPOutboundTruncationSupported = (bool)hash[member];
        }

        private void FillFieldUsesStaticARP_CacheEntries(Hashtable hash)
        {
            string member = Models.s_FieldUsesStaticArpCacheEntries;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_UsesStaticArpCacheEntries = (bool)hash[member];
        }

        private void FillFieldIM5_Supported(Hashtable hash)
        {
            string member = Models.s_FieldIm5Supported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsIm5Supported = (bool)hash[member];
        }

        private void FillFieldIsPROFIenergySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsProfIenergySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfIenergySupported = (bool)hash[member];
        }

        private void FillFieldPDEVCombinedObjectSupported(Hashtable hash)
        {
            string member = Models.s_FieldPdevCombinedObjectSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_PdevCombinedObjectSupported = (bool)hash[member];
        }

        private void FillFieldIsYellowSafetyMargin(Hashtable hash)
        {
            string member = Models.s_FieldIsYellowSafetyMargin;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsYellowSafetyMargin = (bool)hash[member];
        }

        private void FillFieldYellowSafetyMargin(Hashtable hash)
        {
            string member = Models.s_FieldYellowSafetyMargin;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_YellowSafetyMargin = (uint)hash[member];
        }

        private void FillFieldIsMinYellowTime(Hashtable hash)
        {
            string member = Models.s_FieldIsMinYellowTime;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMinYellowTime = (bool)hash[member];
        }

        private void FillFieldMinYellowTime(Hashtable hash)
        {
            string member = Models.s_FieldMinYellowTime;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MinYellowTime = (uint)hash[member];
        }

        private void FillFieldIsMinNRTGap(Hashtable hash)
        {
            string member = Models.s_FieldIsMinNrtGap;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMinNrtGap = (bool)hash[member];
        }

        private void FillFieldMinNRTGap(Hashtable hash)
        {
            string member = Models.s_FieldMinNrtGap;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MinNrtGap = (uint)hash[member];
        }

        private void FillFieldIsMaxFrameStartTime(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxFrameStartTime;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxFrameStartTime = (bool)hash[member];
        }

        private void FillFieldMaxFrameStartTime(Hashtable hash)
        {
            string member = Models.s_FieldMaxFrameStartTime;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxFrameStartTime = (uint)hash[member];
        }

        private void FillFieldIsMinRTC3Gap(Hashtable hash)
        {
            string member = Models.s_FieldIsMinRtc3Gap;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMinRtc3Gap = (bool)hash[member];
        }

        private void FillFieldMinRTC3Gap(Hashtable hash)
        {
            string member = Models.s_FieldMinRtc3Gap;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MinRtc3Gap = (uint)hash[member];
        }

        private void FillFieldPeerToPeerJitter(Hashtable hash)
        {
            string member = Models.s_FieldPeerToPeerJitter;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_PeerToPeerJitter = (uint)hash[member];
        }

        private void FillFieldMinFso(Hashtable hash)
        {
            string member = Models.s_FieldMinFso;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MinFso = (uint)hash[member];
        }

        private void FillFieldMaxRedPeriodLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxRedPeriodLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxRedPeriodLength = (uint)hash[member];
        }

        private void FillFieldMaxRangeIrFrameId(Hashtable hash)
        {
            string member = Models.s_FieldMaxRangeIrFrameId;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxRangeIrFrameId = (uint)hash[member];
        }

        private void FillFieldMaxDfpFrames(Hashtable hash)
        {
            string member = Models.s_FieldMaxDfpFrames;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxDFPFrames = (uint)hash[member];
        }

        private void FillFieldIsMaxDfpFeed(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxDfpFeed;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxDFPFeed = (bool)hash[member];
        }

        private void FillFieldMaxDfpFeed(Hashtable hash)
        {
            string member = Models.s_FieldMaxDfpFeed;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxDFPFeed = (uint)hash[member];
        }

        private void FillFieldIsMaxBridgeDelayFfw(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxBridgeDelayFfw;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxBridgeDelayFfw = (bool)hash[member];
        }

        private void FillFieldMaxBridgeDelayFfw(Hashtable hash)
        {
            string member = Models.s_FieldMaxBridgeDelayFfw;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxBridgeDelayFfw = (uint)hash[member];
        }

        private void FillFieldFragmentationType(Hashtable hash)
        {
            string member = Models.s_FieldFragmentationType;
            if (hash.ContainsKey(member)
                && hash[member] is FragmentationTypes)
                m_FragmentationType = (FragmentationTypes)hash[member];
        }

        private void FillFieldStartupMode(Hashtable hash)
        {
            string member = Models.s_FieldStartupMode;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_RtClass3StartupMode = hash[member] as ArrayList;
        }

        private void FillFieldAlignDfpSubframes(Hashtable hash)
        {
            string member = Models.s_FieldAlignDfpSubframes;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_AlignDfpSubframes = (bool)hash[member];
        }

        private void FillFieldForwardingMode(Hashtable hash)
        {
            string member = Models.s_FieldForwardingMode;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ForwardingMode = hash[member] as ArrayList;
        }

        private void FillFieldWriteable_IM_Records(Hashtable hash)
        {
            string member = Models.s_FieldWriteableImRecords;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_WriteableImRecords = hash[member] as List<uint>;
        }

        private void FillFieldIsDelayMeasurementSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsDelayMeasurementSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_DelayMeasurementSupported = (bool)hash[member];
        }

        private void FillFieldIsMulticastBoundarySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsMulticastBoundarySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_MulticastBoundarySupported = (bool)hash[member];
        }

        private void FillFieldIsParameterizationDisallowed(Hashtable hash)
        {
            string member = Models.s_FieldIsParameterizationDisallowed;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ParameterizationDisallowed = (bool)hash[member];
        }

        private void FillFieldIsochroneModeInRT_Classes(Hashtable hash)
        {
            string member = Models.s_FieldIsochroneModeInRtClasses;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_IsochroneModeInRTClasses = hash[member] as ArrayList;
        }

        private void FillFieldIsDCP_HelloSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsDcpHelloSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_DcpHelloSupported = (bool)hash[member];
        }

        private void FillFieldIsPTPBoundarySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsPtpBoundarySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_PTPBoundarySupported = (bool)hash[member];
        }

        private void FillFieldIsDCPBoundarySupported(Hashtable hash)
        {
            string member = Models.s_FieldIsDcpBoundarySupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_DcpBoundarySupported = (bool)hash[member];
        }

        private void FillFieldSupportedSyncProtocols(Hashtable hash)
        {
            string member = Models.s_FieldSupportedSyncProtocols;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedSyncProtocols = hash[member] as ArrayList;
        }

        private void FillFieldSupportedMibs(Hashtable hash)
        {
            string member = Models.s_FieldSupportedMibs;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedMibs = hash[member] as ArrayList;
        }

        private void FillFieldSupportedProtocols(Hashtable hash)
        {
            string member = Models.s_FieldSupportedProtocols;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedProtocols = hash[member] as ArrayList;
        }

        private void FillFieldSupportedRTClasses(Hashtable hash)
        {
            string member = Models.s_FieldSupportedRtClasses;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SupportedRTClasses = hash[member] as ArrayList;
        }

        private void FillFieldIsNetworkComponentDiagnosisSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsNetworkComponentDiagnosisSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_NetworkComponentDiagnosisSupported = (bool)hash[member];
        }

        private void FillFieldParameterRecordData(Hashtable hash)
        {
            string member = Models.s_FieldParameterRecordData;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ParameterRecordData = hash[member] as ArrayList;
        }

        private void FillFieldMediaRedundancy(Hashtable hash)
        {
            string member = Models.s_FieldMediaRedundancy;
            if (hash.ContainsKey(member)
                && hash[member] is MediaRedundancy)
                m_MediaRedundancy = hash[member] as MediaRedundancy;
        }

        private void FillFieldApplicationRelations(Hashtable hash)
        {
            string member = Models.s_FieldApplicationRelations;
            if (hash.ContainsKey(member)
                && hash[member] is ApplicationRelations)
                m_ApplicationRelations = hash[member] as ApplicationRelations;
        }

        private void FillFieldNameTextID(Hashtable hash)
        {
            string member = Models.s_FieldNameTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_NameTextID = hash[member] as string;
        }

        private void FillFieldName(Hashtable hash)
        {
            string member = Models.s_FieldName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_Name = hash[member] as string;
        }

        private void FillFieldMaxNumberIRFrameData(Hashtable hash)
        {
            string member = Models.s_FieldMaxNumberIrFrameData;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxNumberIRFrameData = (uint)hash[member];
        }

        private void FillFieldMaxLocalJitter(Hashtable hash)
        {
            string member = Models.s_FieldMaxLocalJitter;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxLocalJitter = (uint)hash[member];
        }

        private void FillFieldSupportedSynchronisationRole(Hashtable hash)
        {
            string member = Models.s_FieldSupportedSynchronisationRole;
            if (hash.ContainsKey(member)
                && hash[member] is SynchronisationRoles)
                m_SupportedSynchronisationRole = (SynchronisationRoles)hash[member];
        }

        private void FillFieldIsSynchronisationModeSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsSynchronisationModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsSynchronisationModeSupported = (bool)hash[member];
        }

        private void FillFieldMaxBridgeDelay(Hashtable hash)
        {
            string member = Models.s_FieldMaxBridgeDelay;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxBridgeDelay = (uint)hash[member];
        }

        private void FillFieldT_PLL_Max(Hashtable hash)
        {
            string member = Models.s_FieldTPllMax;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_TPllMax = (uint)hash[member];
        }

        private void FillFieldIsIsochroneModeSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsIsochroneModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsIsochroneModeSupported = (bool)hash[member];
        }

        private void FillFieldSupportedRTClass(Hashtable hash)
        {
            string member = Models.s_FieldSupportedRtClass;
            if (hash.ContainsKey(member)
                && hash[member] is RTClasses)
                m_SupportedRTClass = (RTClasses)hash[member];
        }

        private void FillFieldPROFIsafeFSCP_TestMode_supported(Hashtable hash)
        {
            string member = Models.s_FieldProfIsafeFscpTestModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfisafeFscpTestModeSupported = (bool)hash[member];
        }
        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectInterfaceSubmodule);

            // ----------------------------------------------
            SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {

            // ----------------------------------------------
            // From base class
            base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldSupportedRtClass, m_SupportedRTClass.ToString(), Export.s_SubtypeRtClasses);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsIsochroneModeSupported, m_IsIsochroneModeSupported);
            Export.WriteUint32Property(ref writer, Models.s_FieldTPllMax, m_TPllMax);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxBridgeDelay, m_MaxBridgeDelay);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsSynchronisationModeSupported, m_IsSynchronisationModeSupported);
            Export.WriteEnumProperty(ref writer, Models.s_FieldSupportedSynchronisationRole, m_SupportedSynchronisationRole.ToString(), Export.s_SubtypeSynchronisationRoles);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxLocalJitter, m_MaxLocalJitter);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxNumberIrFrameData, m_MaxNumberIRFrameData);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, m_NameTextID);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldApplicationRelations, m_ApplicationRelations);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldMediaRedundancy, m_MediaRedundancy);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldParameterRecordData, m_ParameterRecordData);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedRtClasses, m_SupportedRTClasses, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsNetworkComponentDiagnosisSupported, m_NetworkComponentDiagnosisSupported);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedProtocols, m_SupportedProtocols, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedMibs, m_SupportedMibs, true);


            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedSyncProtocols, m_SupportedSyncProtocols, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDcpBoundarySupported, m_DcpBoundarySupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsPtpBoundarySupported, m_PTPBoundarySupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDcpHelloSupported, m_DcpHelloSupported);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldIsochroneModeInRtClasses, m_IsochroneModeInRTClasses, true);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsParameterizationDisallowed, m_ParameterizationDisallowed);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMulticastBoundarySupported, m_MulticastBoundarySupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDelayMeasurementSupported, m_DelayMeasurementSupported);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldTransferSequence, m_IsTransferSequenceDefined);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldWriteableImRecords, m_WriteableImRecords, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldForwardingMode, m_ForwardingMode, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldStartupMode, m_RtClass3StartupMode, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldAlignDfpSubframes, m_AlignDfpSubframes);
            Export.WriteEnumProperty(ref writer, Models.s_FieldFragmentationType, m_FragmentationType.ToString(), Export.s_SubtypeFragmentationTypes);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxBridgeDelayFfw, m_IsMaxBridgeDelayFfw);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxBridgeDelayFfw, m_MaxBridgeDelayFfw);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxDfpFeed, m_IsMaxDFPFeed);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxDfpFeed, m_MaxDFPFeed);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxDfpFrames, m_MaxDFPFrames);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxRangeIrFrameId, m_MaxRangeIrFrameId);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxRedPeriodLength, m_MaxRedPeriodLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMinFso, m_MinFso);
            Export.WriteUint32Property(ref writer, Models.s_FieldPeerToPeerJitter, m_PeerToPeerJitter);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMinRtc3Gap, m_IsMinRtc3Gap);
            Export.WriteUint32Property(ref writer, Models.s_FieldMinRtc3Gap, m_MinRtc3Gap);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxFrameStartTime, m_IsMaxFrameStartTime);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxFrameStartTime, m_MaxFrameStartTime);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMinNrtGap, m_IsMinNrtGap);
            Export.WriteUint32Property(ref writer, Models.s_FieldMinNrtGap, m_MinNrtGap);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMinYellowTime, m_IsMinYellowTime);
            Export.WriteUint32Property(ref writer, Models.s_FieldMinYellowTime, m_MinYellowTime);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsYellowSafetyMargin, m_IsYellowSafetyMargin);
            Export.WriteUint32Property(ref writer, Models.s_FieldYellowSafetyMargin, m_YellowSafetyMargin);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldPdevCombinedObjectSupported, m_PdevCombinedObjectSupported);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsProfIenergySupported, m_IsProfIenergySupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIm5Supported, m_IsIm5Supported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldUsesStaticArpCacheEntries, m_UsesStaticArpCacheEntries);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldDFPOutboundTruncationSupported, m_DFPOutboundTruncationSupported);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxRetentionTime, m_MaxRetentionTime);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsOwnIPSetsStandardGateway, m_IsOwnIPSetsStandardGateway);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldDFPRedundantPathLayoutSupported, m_DFPRedundantPathLayoutSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsReportingSystem, m_IsReportingSystem);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsTimeSynchronisation, m_IsTimeSynchronisation);

            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldTimeSynchronisation, m_TimeSynchronisation);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedServiceProtocols, m_SupportedServiceProtocols, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafePirSupported, m_IsPROFIsafePIR_Supported);

            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedDelayMeasurements, m_SupportedDelayMeasurements, true);

            Export.WriteArrayStringProperty(ref writer, Models.s_FieldDcpFeaturesSupported, m_DCPFeaturesSupported, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSnmpFeaturesSupported, m_SNMPFeaturesSupported, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldAplFeaturesSupported, m_APLFeaturesSupported, true);

            Export.WriteArrayStringProperty(ref writer, Models.s_FieldBridge_FeaturesSupported, m_BridgeFeaturesSupported, true);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldTSN_ConfigurationsSupported, m_TSNConfigurationsSupported, true);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafeFscpTestModeSupported, m_IsProfisafeFscpTestModeSupported);

            return true;
        }

        #endregion

    }
}