/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: RecordData.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

using GSDI;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The RecordData itself is only a abstract base class for the real
    /// parameter record data objects, which describes the data structure
    /// of the parameter.
    /// </summary>
    public abstract class RecordData :
        GsdObject,
        IRecordData
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the RecordData if a inherited class is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        protected RecordData()
        {
            m_Index = 0;
            m_Length = 0;
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private uint m_Index;
        private uint m_Length;
        private string m_Name;
        private string m_NameTextID;
        private ArrayList m_Consts;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the unique ID of a record data object, which is used to address the 
        /// object with PROFINET IO RPC calls.
        /// </summary>
        public UInt32 Index => this.m_Index;

        /// <summary>
        /// Accesses the length of the record data object in octets.
        /// </summary>
        public UInt32 Length => this.m_Length;

        /// <summary>
        /// Accesses the human readable, language specific name of the record
        /// data object. 
        /// </summary>
        public string Name => this.m_Name;

        /// <summary>
        /// Accesses the language independent text id of the name of the record data
        /// object.
        /// </summary>
        public string NameTextID => this.m_NameTextID;

        /// <summary>
        /// Accesses a list of ConstData objects, which are used to initialize the data of
        /// the record data object.
        /// </summary>
        public virtual Array Consts =>
            this.m_Consts?.ToArray();

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldIndex;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_Index = (uint)hash[member];

                member = Models.s_FieldLength;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_Length = (uint)hash[member];

                member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Name = hash[member] as string;

                member = Models.s_FieldNameTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_NameTextID = hash[member] as string;

                member = Models.s_FieldConsts;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Consts = hash[member] as ArrayList;


            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            this.SerializeMembers(option, ref writer);

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldName, m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, m_NameTextID);
            Export.WriteUint32Property(ref writer, Models.s_FieldIndex, m_Index);
            Export.WriteUint32Property(ref writer, Models.s_FieldLength, m_Length);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldConsts, m_Consts);

            return true;
        }

        #endregion
    }
}


