/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ParameterTable.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using PNConfigLib.BusinessLogic.HWCNBL.Constants.Methods;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.Structs;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    internal partial class PNDeviceConfigStrategy
    {
        //########################################################################################

        #region Fields

        private IDictionary<PclObject, ParameterDataBlockStruct> m_Parameters;

        #endregion

        //########################################################################################

        #region Properties

        protected virtual IDictionary<PclObject, ParameterDataBlockStruct> Parameters
        {
            set { m_Parameters = value; }
            get { return m_Parameters; }
        }

        #endregion

        //########################################################################################

        #region Public Methods

        /// <summary>
        /// Collect the parameterblocks from all modules and submodules and cache it in the dictionary "Parameters".
        /// </summary>
        public virtual void InitializePrmData()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            if (Parameters == null)
            {
                Parameters = new Dictionary<PclObject, ParameterDataBlockStruct>();
            }
            else
            {
                Parameters.Clear();
            }

            bool interfaceIsIsochron =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsochron,
                    ac.GetNew(),
                    false);
            PNIsoDataModel isoDataModel = PNIsoDataModel.Model1;

            if (interfaceIsIsochron)
            {
                isoDataModel =
                    (PNIsoDataModel)
                    this.Interface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIsoDataModel,
                        ac.GetNew(),
                        (uint)PNIsoDataModel.Model1);
            }

            ParameterDatasetStruct firstIsoImData = null;
            ParameterDatasetStruct firstIsoPeriData = null;
            foreach (PclObject module in Modules)
            {
                if (module == null)
                {
                    continue;
                }

                IList<PclObject> submodules = GetSubmodulesInternal(module);

                GetParamSlotBlock(module);
                if (submodules.Count == 0)
                {
                    if (interfaceIsIsochron)
                    {
            
                        if (isoDataModel == PNIsoDataModel.Model1)
                        {
                            BuildIsoParamSlotBlock(isoDataModel, module, ref firstIsoImData);
                        }
                        else
                        {
                            BuildIsoParamSlotBlock(isoDataModel, module, ref firstIsoPeriData);
                        }
                    }
                }
                else
                {
                    foreach (PclObject submodule in submodules)
                    {
                        // submodule list --> do not generate parameter data twice
                        if (!submodule.Equals(module))
                        {
                            GetParamSlotBlock(submodule);
                        }

                        if (interfaceIsIsochron)
                        {
                            if (isoDataModel == PNIsoDataModel.Model1)
                            {
                                BuildIsoParamSlotBlock(isoDataModel, submodule, ref firstIsoImData);
                            }
                            else
                            {
                                BuildIsoParamSlotBlock(isoDataModel, submodule, ref firstIsoPeriData);
                            }
                        }
                    }
                }
            }
        }

        private void BuildIsoParamSlotBlock(
            PNIsoDataModel isoDataModel,
            PclObject submodule,
            ref ParameterDatasetStruct firstIsoData)
        {
            // Do not use for Interface or Port
            if (submodule is Interface
                || submodule is DataModel.PCLObjects.Port)
            {
                return;
            }

            // Collect the isochronblocks for all modules/submodules in the device.
            // Performance:
            // The isochron blocks of two modules in a device only differ in the slot-/subslotnumber.
            // The complete calculation of this block is only necessary for the first block. All other blocks
            // are copies of the first block with the actual slot/subslotnumber.

            AttributeAccessCode ac = new AttributeAccessCode();
            bool clockSyncMode = submodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.ClockSyncMode,
                ac.GetNew(),
                false);
            if (!clockSyncMode)
            {
                return;
            }

            bool isoRecordNotSupported =
                submodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsoRecordNotSupported,
                    ac.GetNew(),
                    false);
            if (isoRecordNotSupported)
            {
                return;
            }

            if (firstIsoData == null)
            {
                firstIsoData = GetIsochronBlock(submodule, isoDataModel);
                if (firstIsoData == null)
                {
                    return;
                }

                SetIsochronBlockFirst(submodule, firstIsoData);
            }
            else
            {
                ParameterDatasetStruct copiedIsoData = CreateUpdatedIsochronBlock(submodule, firstIsoData);
                SetIsochronBlockFirst(submodule, copiedIsoData);
            }
        }

        /// <summary>
        /// Create a copy of the isochronblock and replace the slot-/subslotnumber
        /// Assumtion:
        /// If a IO-Device has isochron modules, then the only difference in the isochronblocks
        /// of these modules are the slot/subslotnumber.
        /// </summary>
        private ParameterDatasetStruct CreateUpdatedIsochronBlock(
            PclObject configObject,
            ParameterDatasetStruct isoData)
        {
            // Evaluate slot/subslot number for the configobject.

            int slotNumber = 0;
            int subSlotNumber = 1;
            AttributeAccessCode ac = new AttributeAccessCode();

            if (configObject is Module)
            {
                slotNumber = configObject.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PositionNumber,
                    ac.GetNew(),
                    0);
            }
            else if (configObject is Submodule)
            {
                subSlotNumber = configObject.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    ac.GetNew(),
                    1);

                PclObject container = configObject.ParentObject;

                if (container != null)
                {
                    slotNumber = container.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PositionNumber,
                        ac.GetNew(),
                        0);
                }
            }

            // Create a new isochronblock and replace slot/subslotnumber.

            byte[] origIsoStruct = isoData.GetParaBlock(0);

            IsochronModeDataBlockStruct newIsoStruct = new IsochronModeDataBlockStruct(origIsoStruct);
            newIsoStruct.SlotNumber = slotNumber;
            newIsoStruct.SubSlotNumber = subSlotNumber;
            ParameterDatasetStruct newIsoData = new ParameterDatasetStruct();
            newIsoData.ParaDSNumber = isoData.ParaDSNumber;
            newIsoData.ParaDSIdentifier = isoData.ParaDSIdentifier;
            newIsoData.AddParaBlock(newIsoStruct.ToByteArray());

            return newIsoData;
        }

        public virtual byte[] GetPrmDataBlock()
        {
            ICollection<ParameterDataBlockStruct> listParameterBlocks = Parameters.Values;

            List<byte> list = new List<byte>();

            foreach (ParameterDataBlockStruct parameterBlock in listParameterBlocks)
            {
                int paramBlockLength = parameterBlock.ToByteArray.Length;
                int mod4 = paramBlockLength % 4;

                if (mod4 != 0)
                {
                    byte[] paramBlockArray = new byte[parameterBlock.BlockLength];
                    Array.Copy(parameterBlock.ToByteArray, paramBlockArray, paramBlockLength);
                    list.AddRange(paramBlockArray);
                }
                else
                {
                    list.AddRange(parameterBlock.ToByteArray);
                }
            }
            return list.ToArray();
        }

        #endregion

        //########################################################################################

        #region Private Implementation

        private ParameterDatasetStruct GetIsochronBlock(PclObject module, PNIsoDataModel isoDataModel)
        {
            ParameterDatasetStruct isoData;
            IMethodData md = new MethodData();
            md.Name = GetIsochronousModeData.Name;
            md.Arguments.Add(GetIsochronousModeData.ModuleParameter, module);
            md.Arguments.Add(GetIsochronousModeData.PNIsoDataModel, isoDataModel);

            this.Interface.BaseActions.CallMethod(md);

            if (!(bool)md.ReturnValue)
            {
                throw new PNFunctionsException("GenericMethod: " + GetIsochronousModeData.Name + " failed.");
            }

            isoData = md.Arguments[GetIsochronousModeData.IsochronousModeDataBlockEntry] as ParameterDatasetStruct;

            return isoData;
        }

        /// <summary>
        /// Insert the isochron as the first dataset in the collection of datasets for a module.
        /// </summary>
        private void SetIsochronBlockFirst(PclObject submodule, ParameterDatasetStruct isoData)
        {
            if (isoData == null)
            {
                return;
            }

            ParameterDataBlockStruct pds;

            if (Parameters.ContainsKey(submodule))
            {
                pds = Parameters[submodule];
            }
            else
            {
                pds = new ParameterDataBlockStruct();
                Parameters[submodule] = pds;
            }

            pds.AddFirstParamDSBlock(isoData);

            int mod4 = pds.ToByteArray.Length % 4;
            if (mod4 != 0)
            {
                pds.BlockLength += mod4;
            }
        }

        protected virtual void GetParamSlotBlock(PclObject module)
        {
            if (module is Interface
                || module is DataModel.PCLObjects.Port)
            {
                ParameterDataBlockStruct paramDataBlock = GetParamGetPDDataBlocks(module);
                if (paramDataBlock.ParamBlockCount != 0)
                {
                    Parameters.Add(module, paramDataBlock);
                }
            }

            GetParamGetSlotBlock(module);
        }
        
        protected void GetParamGetSlotBlock(PclObject module)
        {
            ParameterDataBlockStruct paramDataBlock = Utility.GetParamDataBlock(
                module,
                this.Interface,
                ControllerInterfaceSubmodule);
            if (paramDataBlock != null)
            {
                if (!Parameters.ContainsKey(module))
                {
                    Parameters.Add(module, paramDataBlock);
                }
                else
                {
                    // module already has a ParameterDataBlock, so append datasets to this existing one
                    for (int i = 0; i < paramDataBlock.ParamBlockCount; i++)
                    {
                        Parameters[module].AddParamDSBlock(paramDataBlock.GetParamDataset(i));
                    }
                }
            }
        }

        #region PDEV Blocks

        /// <summary>
        /// Function delivers the PDEV Interface and Port submodule parameter data blocks for PN-IO Device Config.
        /// </summary>
        private ParameterDataBlockStruct GetParamGetPDDataBlocks(PclObject submodule)
        {
            // Check if the parameter data must be generated with PrmID.
            AttributeAccessCode ac = new AttributeAccessCode();
            bool hasPrmId = (ControllerInterfaceSubmodule != null)
                            && ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnIoGenerateConfig2003BlocksV1_1,
                                ac,
                                false);

            ParameterDataBlockStruct paramDataBlock = new ParameterDataBlockStruct(hasPrmId);

            // PDEV Interface Blocks
            if (submodule is Interface
                && AttributeUtilities.IsDecentralPDEV(submodule))
            {
                //check, whether the submodule model is supported by the InterfaceSubmodule
                if (!GeneralUtilities.IsPDEVDevice(submodule))
                {
                    return paramDataBlock;
                }

                // Block 0x802D - ExpectedPDSyncData
                if (IsRTSync())
                {
                    AddPDSyncData(ref paramDataBlock);
                }

                // Block 0x8052 - PDInterfaceMrpDataAdjust
                AddPDInterfaceMrpDataAdjust(ref paramDataBlock);

                //// Block 0x8051 - PDInterfaceMrpDataCheck
                AddPDInterfaceMrpDataCheck(ref paramDataBlock);

                // Block 0x8020 - PDIRSubframeData
                AddPDIRSubframeData(ref paramDataBlock);

                // Block 0x8090 - PDInterfaceFSUDataAdjust
                if (Utility.IsFastStartupActivated(this.Interface))
                {
                    AddPDFSUDataAdjust(ref paramDataBlock);
                }

                //Block 0x8071 - PDInterfaceAdjust
                AddPDInterfaceAdjust(ref paramDataBlock);

                #region address tailor data block

                AddPdMasterTailorData(
                    ControllerInterfaceSubmodule,
                    submodule as Interface,
                    paramDataBlock.AddParamDSBlock);

                #endregion

                #region CIMSNMPAdjust
                //Block 0x8200 - CIMSNMPAdjust
                AddCIMSNMPAdjust(
                    ref paramDataBlock);
                #endregion
            }
            // PDEV Port Blocks
            else if (submodule is DataModel.PCLObjects.Port)
            {
                GetParamGetPDDataBlocksForPort(submodule as DataModel.PCLObjects.Port, ref paramDataBlock);
            }

            // For block version 101 a PrmID (64 bit CRC) must be generated
            if (hasPrmId && (paramDataBlock.ParamBlockCount != 0))
            {
                Crc64 crc = new Crc64();
                paramDataBlock.PrmID = crc.ComputeHash(paramDataBlock.ToByteArrayNetto);
            }

            return paramDataBlock;
        }

        /// <summary>
        /// Get port specific PDEV blocks and add them to the paramDataBlock.
        /// </summary>
        /// <param name="portSubmodule"></param>
        /// <param name="paramDataBlock"></param>
        private void GetParamGetPDDataBlocksForPort(
            DataModel.PCLObjects.Port portSubmodule,
            ref ParameterDataBlockStruct paramDataBlock)
        {
            Interface itfSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);
            Debug.Assert(
                itfSubmodule != null,
                "Interface submodule can not be found from port submodule" + portSubmodule);

            if ((itfSubmodule != null)
                && AttributeUtilities.IsDecentralPDEV(this.Interface))
            {
                //check, whether the submodule model is supported by the InterfaceSubmodule
                if (!GeneralUtilities.IsPDEVDevice(this.Interface))
                {
                    return;
                }
                // Block 0x802F - PDPortDataAdjust
                AddPDPortDataAdjust(ref paramDataBlock, portSubmodule);
                // Block 0x802B - PDPortDataCheck
                AddPDPortDataCheck(ref paramDataBlock, portSubmodule);
                // Block 0x8053 - PDPortMrpDataAdjust
                AddPDPortMrpDataAdjust(ref paramDataBlock, portSubmodule);
                // Block 0x8062 - PDPortFODataAdjust
                AddPDPortFODataAdjust(ref paramDataBlock, portSubmodule);
                // Block 0x8061 - PDPortFODataCheck
                AddPDPortFODataCheck(ref paramDataBlock, portSubmodule);
            }
        }

        #region PDEV interface blocks

        /// <summary>
        /// PDEV Block 0x802D
        /// </summary>
        protected virtual void AddPDSyncData(ref ParameterDataBlockStruct paramDataBlock)
        {
            IMethodData md = new MethodData();
            md.Name = GetPdSyncDataParameters.Name;

            if (!this.Interface.BaseActions.CallMethod(md))
            {
                throw new PNFunctionsException("GenericMethod: " + GetPdSyncDataParameters.Name + "failed.");
            }

            byte[] syncData = md.Arguments[GetPdSyncDataParameters.PdSyncDataStructEntry] as byte[];

            if (syncData != null)
            {
                ParameterDatasetStruct paramDs = new ParameterDatasetStruct();

                paramDs.ParaDSNumber = 0x802D;
                paramDs.AddParaBlock(syncData);

                paramDataBlock.AddParamDSBlock(paramDs);
            }
        }

        private void AddPDFSUDataAdjust(ref ParameterDataBlockStruct paramDataBlock)
        {
            //construct new Dataset-header
            ParameterDatasetStruct paramDs = Utility.GetPdfsuDataAdjust(this.Interface);

            //add Dataset to ParameterDataBlock
            paramDataBlock.AddParamDSBlock(paramDs);
        }

        /// <summary>
        /// PDEV Block 0x8051
        /// </summary>
        private void AddPDInterfaceMrpDataCheck(ref ParameterDataBlockStruct paramDataBlock)
        {
            bool isToGenerate;
            byte[] mrpData = ConfigUtility.GetPDInterfaceMrpDataCheck(Interface, out isToGenerate);
            //Check if the PDInterfaceMrpDataCheck parameter block is to be generated
            if (isToGenerate && (mrpData != null))
            {
                ParameterDatasetStruct paramDsInterfaceMrpDataCheck = new ParameterDatasetStruct();
                paramDsInterfaceMrpDataCheck.ParaDSNumber = 0x8051;
                // add parameter block into DS Struct
                paramDsInterfaceMrpDataCheck.AddParaBlock(mrpData);
                // add DS Block to parameter data blocks
                paramDataBlock.AddParamDSBlock(paramDsInterfaceMrpDataCheck);
            }
        }

        /// <summary>
        /// PDEV Block 0x8052
        /// </summary>
        private void AddPDInterfaceMrpDataAdjust(ref ParameterDataBlockStruct paramDataBlock)
        {
            bool isToGenerate;
            byte[] mrpData = ConfigUtility.GetPDInterfaceMrpDataAdjust(Interface, out isToGenerate);
            //Check if the PDInterfaceMrpDataAdjust parameter block is to be generated
            if (isToGenerate && (mrpData != null))
            {
                ParameterDatasetStruct paramDsInterfaceMrpDataAdjust = new ParameterDatasetStruct();
                paramDsInterfaceMrpDataAdjust.ParaDSNumber = 0x8052;
                // add parameter block into DS Struct
                paramDsInterfaceMrpDataAdjust.AddParaBlock(mrpData);
                // add DS Block to parameter data blocks
                paramDataBlock.AddParamDSBlock(paramDsInterfaceMrpDataAdjust);
            }
        }

        private void AddPDIRSubframeData(ref ParameterDataBlockStruct paramDataBlock)
        {
            byte[] pdirSubframeData = ConfigUtility.GetPDIRSubframeData(Interface);
            //Check if the PDIRSubframeData block is to be generated
            if (pdirSubframeData != null)
            {
                ParameterDatasetStruct pdirSubframeDataStruct = new ParameterDatasetStruct();
                pdirSubframeDataStruct.ParaDSNumber = 0x8020;
                // add parameter block into DS Struct
                pdirSubframeDataStruct.AddParaBlock(pdirSubframeData);
                // add DS Block to parameter data blocks
                paramDataBlock.AddParamDSBlock(pdirSubframeDataStruct);
            }
        }

        private void AddPDInterfaceAdjust(ref ParameterDataBlockStruct paramDataBlock)
        {
            bool returnVal;
            byte[] pdInterfaceAdjust = ConfigUtility.GetPDInterfaceAdjustBlock(this.Interface, out returnVal);
            if (!returnVal
                || (pdInterfaceAdjust == null))
            {
                return;
            }
            ParameterDatasetStruct pdInterfaceAdjustStruct = new ParameterDatasetStruct();
            pdInterfaceAdjustStruct.ParaDSNumber = 0x8071;
            pdInterfaceAdjustStruct.ParaDSIdentifier = 0;
            // add parameter block into DS Struct
            pdInterfaceAdjustStruct.AddParaBlock(pdInterfaceAdjust);
            // add DS Block to parameter data blocks
            paramDataBlock.AddParamDSBlock(pdInterfaceAdjustStruct);
        }

        private void AddCIMSNMPAdjust(ref ParameterDataBlockStruct paramDataBlock)
        {
            IMethodData md = new MethodData();
            md.Name = GetCimSnmpAdjustRecord.Name;
            md.Arguments[GetCimSnmpAdjustRecord.ConnectedInterface] = this.Interface;

            if (this.Interface.BaseActions.CallMethod(md)
                && md.ReturnValue != null
                && ((byte[])md.ReturnValue).Length > 0)
            {
                ParameterDatasetStruct pdCimSnmpAdjustStruct = new ParameterDatasetStruct();
                pdCimSnmpAdjustStruct.ParaDSNumber = (int)DataRecords.Indexes.CIMSNMPAdjust;
                pdCimSnmpAdjustStruct.ParaDSIdentifier = 0;
                pdCimSnmpAdjustStruct.AddParaBlock((byte[])md.ReturnValue);
                paramDataBlock.AddParamDSBlock(pdCimSnmpAdjustStruct);
            }
        }

        #endregion

        #region PDEV port blocks

        /// <summary>
        /// Blocks 0x802F
        /// </summary>
        private static void AddPDPortDataAdjust(
            ref ParameterDataBlockStruct paramDataBlock,
            DataModel.PCLObjects.Port portModule)
        {
            IMethodData md = new MethodData();
            md.Name = GetPortParameterBlocks.Name;
            md.Arguments[GetPortParameterBlocks.SelectedDataset] = 0x802F;
            md.Arguments[GetPortParameterBlocks.InterfaceType] = PNInterfaceType.IODevice;

            byte[] portData = null;

            if (portModule.BaseActions.CallMethod(md))
            {
                if ((bool)md.ReturnValue)
                {
                    portData = md.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] as byte[];
                }
            }

            if (portData != null)
            {
                ParameterDatasetStruct paramDsPortDataAdjust = new ParameterDatasetStruct(portData);
                paramDsPortDataAdjust.ParaDSNumber = 0x802F;
                paramDsPortDataAdjust.ParaDSIdentifier = 0;

                paramDataBlock.AddParamDSBlock(paramDsPortDataAdjust);
            }
        }

        /// <summary>
        /// Blocks 0x802B
        /// </summary>
        private static void AddPDPortDataCheck(
            ref ParameterDataBlockStruct paramDataBlock,
            DataModel.PCLObjects.Port portModule)
        {
            IMethodData md = new MethodData();
            md.Name = GetPortParameterBlocks.Name;
            md.Arguments[GetPortParameterBlocks.SelectedDataset] = 0x802B;
            md.Arguments[GetPortParameterBlocks.InterfaceType] = PNInterfaceType.IODevice;
            byte[] portData = null;

            if (portModule.BaseActions.CallMethod(md))
            {
                if ((md.ReturnValue != null)
                    && (bool)md.ReturnValue)
                {
                    portData = md.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] as byte[];
                }
            }

            if (portData != null)
            {
                ParameterDatasetStruct paramDSPortDataCheck = new ParameterDatasetStruct(portData);
                paramDSPortDataCheck.ParaDSNumber = 0x802B;
                paramDSPortDataCheck.ParaDSIdentifier = 0;

                paramDataBlock.AddParamDSBlock(paramDSPortDataCheck);
            }
        }

        /// <summary>
        /// Blocks 0x8062
        /// </summary>
        private static void AddPDPortFODataAdjust(
            ref ParameterDataBlockStruct paramDataBlock,
            DataModel.PCLObjects.Port portModule)
        {
            IMethodData md = new MethodData();
            md.Name = GetPortParameterBlocks.Name;
            md.Arguments[GetPortParameterBlocks.SelectedDataset] = 0x8062;
            byte[] portData = null;

            if (portModule.BaseActions.CallMethod(md))
            {
                if ((bool)md.ReturnValue)
                {
                    portData = md.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] as byte[];
                }
            }

            if (portData != null)
            {
                ParameterDatasetStruct paramDSPortFODataAdjust = new ParameterDatasetStruct(portData);
                paramDSPortFODataAdjust.ParaDSNumber = 0x8062;
                paramDSPortFODataAdjust.ParaDSIdentifier = 0;

                paramDataBlock.AddParamDSBlock(paramDSPortFODataAdjust);
            }
        }

        /// <summary>
        /// Blocks 0x8061
        /// </summary>
        private static void AddPDPortFODataCheck(
            ref ParameterDataBlockStruct paramDataBlock,
            DataModel.PCLObjects.Port portModule)
        {
            IMethodData md = new MethodData();
            md.Name = GetPortParameterBlocks.Name;
            md.Arguments[GetPortParameterBlocks.SelectedDataset] = 0x8061;
            byte[] portData = null;

            if (portModule.BaseActions.CallMethod(md))
            {
                if (md.ReturnValue is bool)
                {
                    if ((bool)md.ReturnValue)
                    {
                        portData = md.Arguments[GetPortParameterBlocks.PdPortDataBlockEntry] as byte[];
                    }
                }
                else if (md.ReturnValue is byte[])
                {
                    portData = md.ReturnValue as byte[];
                }
            }

            if (portData != null)
            {
                ParameterDatasetStruct paramDsPortFoDataCheck = new ParameterDatasetStruct(portData);
                paramDsPortFoDataCheck.ParaDSNumber = 0x8061;
                paramDsPortFoDataCheck.ParaDSIdentifier = 0;

                paramDataBlock.AddParamDSBlock(paramDsPortFoDataCheck);
            }
        }

        /// <summary>
        /// Block 0x8053
        /// </summary>
        private static void AddPDPortMrpDataAdjust(
            ref ParameterDataBlockStruct paramDataBlock,
            DataModel.PCLObjects.Port portModule)
        {
            IMethodData md = new MethodData();
            md.Name = GetPortMrpParameterBlocks.Name;
            byte[] portData = null;

            if (portModule.BaseActions.CallMethod(md)
                && (bool)md.ReturnValue)
            {
                portData = md.Arguments[GetPortMrpParameterBlocks.PDPortMrpDataBlockEntry] as byte[];
            }

            if (portData != null)
            {
                ParameterDatasetStruct paramDSPortMrpDataAdjust = new ParameterDatasetStruct(portData);
                paramDSPortMrpDataAdjust.ParaDSNumber = 0x8053;
                paramDSPortMrpDataAdjust.ParaDSIdentifier = 0;

                paramDataBlock.AddParamDSBlock(paramDSPortMrpDataAdjust);
            }
        }

        #endregion

        #endregion

        /// <summary>
        /// </summary>
        private bool IsRTSync()
        {
            if (ConfigUtility.IsSynchronized(this.Interface, PNInterfaceType.IODevice))
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                Enumerated syncPEnumerated =
                    this.Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSupportedSyncProtocols,
                        ac,
                        null);
                if ((syncPEnumerated != null) && (syncPEnumerated.Value != null)
                    && (Convert.ToInt32(syncPEnumerated.Value, CultureInfo.InvariantCulture) == 3))
                {
                    return true;
                }
            }

            return false;
        }

        #endregion
    }
}