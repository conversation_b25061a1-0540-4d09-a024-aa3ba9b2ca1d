/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AddressUtility.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Addresses
{
    internal static class AddressUtility
    {
        internal static bool GetAddressObjectsOfModule(
            PclObject addressee,
            out DataAddress inAddressObj,
            out DataAddress outAddressObj,
            out bool hasDiagnosticAddresses)
        {
            if (addressee == null)
            {
                throw new ArgumentNullException(nameof(addressee));
            }
            DataAddress substituteAddress;

            return GetAddressObjectsOfModule(
                addressee,
                out inAddressObj,
                out outAddressObj,
                out substituteAddress,
                out hasDiagnosticAddresses);
        }

        private static bool GetAddressObjectsOfModule(
            PclObject addressee,
            out DataAddress inAddressObj,
            out DataAddress outAddressObj,
            out DataAddress substituteAddressObj,
            out bool hasDiagnosticAddresses)
        {
            inAddressObj = null;
            outAddressObj = null;
            substituteAddressObj = null;
            hasDiagnosticAddresses = false;

            List<DataAddress> addresses = addressee.DataAddresses;

            if (addresses.Count == 0)
            {
                return false;
            }

            // Packed IO-Modules can have up to 3 addresses.
            if ((addresses == null)
                || !addresses.Any()
                || (addresses.Count > 3))
            {
                return false;
            }

            foreach (DataAddress address in addresses)
            {
                if (address.GetBitOffset(0) > 0)
                {
                    continue; 
                }

                switch (address.IoType)
                {
                    case IoTypes.Output:
                        outAddressObj = address;
                        break;
                    case IoTypes.Input:
                        inAddressObj = address;
                        break;
                    case IoTypes.Substitute:
                        substituteAddressObj = address;
                        break;
                    case IoTypes.Diagnosis:
                        if (((inAddressObj != null) || (outAddressObj != null))
                            && !hasDiagnosticAddresses)
                        {
                            if (inAddressObj == null)
                            {
                                inAddressObj = address;
                            }
                            else
                            {
                                outAddressObj = address;
                            }
                        }
                        else
                        {
                            if (inAddressObj == null)
                            {
                                inAddressObj = address;
                            }
                            else
                            {
                                int tag1 = inAddressObj.GetTag(-1);
                                int tag2 = address.GetTag(-1);

                                if (tag2 > tag1)
                                {
                                    outAddressObj = address;
                                }
                                else
                                {
                                    outAddressObj = inAddressObj;
                                    inAddressObj = address;
                                }
                            }
                        }
                        hasDiagnosticAddresses = true;
                        break;
                }
            }
            return true;
        }

        /// <summary>
        /// The method returns true, if the module has an input or output address with a bitoffset greater than 0.
        /// </summary>
        internal static bool IsPackedModule(PclObject module)
        {
            if (module == null)
            {
                throw new ArgumentNullException(nameof(module));
            }

            foreach (DataAddress address in module.DataAddresses)
            {
                IoTypes ioType = address.IoType;
                if (ioType == IoTypes.Input || ioType == IoTypes.Output)
                {
                    return (address.GetBitOffset(0) > 0);
                }
            }

            return false;
        }
    }
}