/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDevice.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.Compiler.DataTypes.Interfaces;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.Compiler.DataTypes
{
    internal class CentralDevice : ProjectTreeNodeBase
    {
        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private readonly DataModel.PCLObjects.CentralDevice s_CentralDeviceObject;

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public CentralDevice(DataModel.PCLObjects.CentralDevice centralDevice)
        {
            Name = AttributeUtilities.GetName(centralDevice);
            ClassRID = CompilerConstants.ClassRid.CentralDevice;
            s_CentralDeviceObject = centralDevice;
        }

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition
        public override void GenerateChildren()
        {
            ChildNodes.Add(GeneratePNInterface());
            ChildNodes.AddRange(GeneratePortModules());
        }

        #endregion

        //########################################################################################

        #region Constants and Enums                        

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class
        private IProjectTreeNode GeneratePNInterface()
        {
            Interface interfacePclObject = s_CentralDeviceObject.GetInterface();
            ControllerInterfaceSubmodule controllerInterfaceNode = new ControllerInterfaceSubmodule(interfacePclObject);
            controllerInterfaceNode.GenerateChildren();
            return controllerInterfaceNode;
        }

        private List<ControllerPortSubmodule> GeneratePortModules()
        {
            List<ControllerPortSubmodule> portSubmoduleNodes = new List<ControllerPortSubmodule>();
            Interface interfacePclObject = s_CentralDeviceObject.GetInterface();
            foreach (Port portSubmodules in interfacePclObject.GetPorts())
            {
                ControllerPortSubmodule portSubmoduleNode = new ControllerPortSubmodule(portSubmodules);
                portSubmoduleNode.GenerateChildren();
                portSubmoduleNodes.Add(portSubmoduleNode);
            }
            return portSubmoduleNodes;
        }

        public override void Compile()
        {
            PNDriverInterfaceEnum pndInterfaceType = s_CentralDeviceObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.InterfaceType,
                new AttributeAccessCode(),
                PNDriverInterfaceEnum.Windows);

            FwVersion fwVersion = s_CentralDeviceObject.GetCentralDeviceFwVersion();

            if (pndInterfaceType == PNDriverInterfaceEnum.Custom
                || fwVersion > FwVersion.V2_1)
            {
                Variables.Add(CompileUtility.GetDataRecordsConfCentral(s_CentralDeviceObject));
            }
            base.Compile();
        }

        #endregion
    }
}