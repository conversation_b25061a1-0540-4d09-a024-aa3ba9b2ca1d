/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronConfigurationStrategy.cs          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Utilities;
using System.Globalization;

#endregion

namespace PNConfigLib.HWCNBL.Isochrone
{
    internal class IsochronConfigurationStrategy
    {
        /// <summary>
        /// Returns Delay Time as milisecond
        /// </summary>
        public double GetDelayTimeAsMilliSeconds(Interface controllerInterfaceSubmodule)
        {
            long valueInNanoSeconds;

            AttributeAccessCode ac = new AttributeAccessCode();
            PNIOC cpu = controllerInterfaceSubmodule.PNIOC;
            bool delayTimeAutomatic = cpu.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.DelayTimeAutomaticOb61,
                ac, false);
            if (delayTimeAutomatic)
            {
                valueInNanoSeconds = GetDelayTimeFromSyncDomain(controllerInterfaceSubmodule);
            }
            else
            {
                valueInNanoSeconds = cpu.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.DelayTimeOB61,
                    ac.GetNew(), 0);
            }
            double delayTimeMilliSec = ConvertDelayTimeToMilliSeconds(valueInNanoSeconds);
            return delayTimeMilliSec;
        }

        /// <summary>
        /// Validates Delay Time value that is being set
        /// </summary>
        /// <param name="delayTime">
        /// Input Delay Time
        /// </param>
        /// <param name="ioSystem"></param>
        /// <returns>
        /// Returns true if Delay Time is valid, false if not<see cref="bool"/>.
        /// </returns>
        internal static bool ValidateDelayTime(float delayTime, DataModel.PCLObjects.IOSystem ioSystem)
        {
            long delayTimeMinimum = ioSystem.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIsoMinDelayTime,
                new AttributeAccessCode(),
                0);
            long delayTimeMaximum = ioSystem.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIsoMaxDelayTime,
                new AttributeAccessCode(),
                0);
            long delayTimeCurrent = ConvertDelayTimeToNanoSeconds(delayTime);


            if ((delayTimeCurrent < delayTimeMinimum) || (delayTimeCurrent > delayTimeMaximum))
            {
                double delayTimeMinimumMs = ConvertDelayTimeToMilliSeconds(delayTimeMinimum);
                double delayTimeMaximumMs = ConvertDelayTimeToMilliSeconds(delayTimeMaximum);
                object[] parameters =
                   {
                        delayTime,
                    delayTimeMinimumMs,
                        delayTimeMaximumMs
                    };
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    ioSystem,
                    ConsistencyConstants.DelayTimeOutOfRange,
                    parameters);
                return false;
            }
            return true;
        }

        /// <summary>
        /// Converts delay time to miliseconds
        /// </summary>
        /// <param name="time">
        /// Input time
        /// </param>
        /// <returns>
        /// Returns delay time as miliseconds <see cref="double" />.
        /// </returns>
        private static double ConvertDelayTimeToMilliSeconds(long time)
        {
            return time / 1000000.0;
        }

        /// <summary>
        /// Gets the delay time value when automatic minimum is selected
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        /// <returns>
        /// returns TCA_Valid value of sync domain <see cref="long" />.
        /// </returns>
        private long GetDelayTimeFromSyncDomain(Interface controllerInterfaceSubmodule)
        {
            SyncDomain syncDomain = controllerInterfaceSubmodule.SyncDomain;

            if (syncDomain == null)
            {
                return 0;
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            long tcaValid = syncDomain.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIsoTcaValid,
                ac,
                0);
            return tcaValid;
        }
        /// <summary>
        /// Sets Application Cycle and IO System CACF(PNIsoCacf)
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        /// <param name="newValue">
        /// Value to set
        /// </param>
        public void SetApplicationCycle(Interface controllerInterfaceSubmodule, long newValue)
        {
            SetIoSystemCacf(controllerInterfaceSubmodule, newValue);
            controllerInterfaceSubmodule.PCLCatalogObject.AttributeAccess.SetAnyAttribute(
                InternalAttributeNames.PnIsoUserAdjustedAppCycle,
                newValue);
        }
        /// <summary>
        /// Sets automatic minumum flag for Isochron PVC
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        /// <param name="newValue">
        /// New value to be set
        /// </param>
        public void SetAutomaticMinimum(Interface controllerInterfaceSubmodule, bool newValue)
        {
            PNIOC cpu = controllerInterfaceSubmodule.PNIOC;
            cpu.AttributeAccess.SetAnyAttribute(InternalAttributeNames.DelayTimeAutomaticOb61, newValue);
        }
        /// <summary>
        /// Sets delay time
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule.
        /// </param>
        /// <param name="newValue">
        /// Value to be set.
        /// </param>
        public void SetDelayTime(Interface controllerInterfaceSubmodule, float? newValue)
        {
            long valueInNanoSeconds;

            if (newValue == null)
            {
                valueInNanoSeconds = GetDelayTimeFromSyncDomain(controllerInterfaceSubmodule);
            }
            else
            {
                valueInNanoSeconds = ConvertDelayTimeToNanoSeconds(newValue.Value);
            }

            PNIOC cpu = controllerInterfaceSubmodule.PNIOC;
            cpu.AttributeAccess.SetAnyAttribute(InternalAttributeNames.DelayTimeOB61, valueInNanoSeconds);
        }
        /// <summary>
        /// Sets IO System Cacf(PNIsoCacf)
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// Controller Interface Submodule
        /// </param>
        /// <param name="applicationCycle">
        /// Application Cycle value
        /// </param>
        public void SetIoSystemCacf(Interface controllerInterfaceSubmodule, long applicationCycle)
        {
            float calculatedCacf = IsochronHelper.CalculateCacf(controllerInterfaceSubmodule, applicationCycle);
            DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(
                controllerInterfaceSubmodule);
            bool cacfValidated = IsochronHelper.ValidateCacf(controllerInterfaceSubmodule, calculatedCacf);
            if (cacfValidated)
            {
                ioSystem.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PnIsoCacf, (int)calculatedCacf);
            }
        }
        /// <summary>
        /// Converts delay time to nanoseconds
        /// </summary>
        /// <param name="newValue">
        /// Input time
        /// </param>
        /// Flag to set conversion state
        /// <returns>
        /// Returns delay time as nanoseconds <see cref="long"/>.
        /// </returns>
        private static long ConvertDelayTimeToNanoSeconds(float newValue)
        {

            double valueNanoSec = 1000000 * newValue;
            long result = (long)Math.Round(valueNanoSec);
            return result;
        }
    }

}