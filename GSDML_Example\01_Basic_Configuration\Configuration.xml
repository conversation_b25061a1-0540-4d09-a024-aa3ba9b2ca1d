﻿<!--***********************************************************************
  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      
***************************************************************************
  This program is protected by German copyright law and international      
  treaties. The use of this software including but not limited to its      
  Source Code is subject to restrictions as agreed in the license          
  agreement between you and Siemens.                                       
  according to your license agreement with Siemens.                        
  Copying or distribution is not allowed unless expressly permitted        
***************************************************************************
                                                                           
  P r o j e c t         Basic Components                            
                                                                           
  P a c k a g e         PROFINET Configuration Library      
                                                                           
  C o m p o n e n t     Examples                          
                                                                           
  F i l e               Configuration.xml                 
                                                                           
***************************************************************************-->

<!--************************************************************************************
    *** Example For a Basic Configuration of a Central Device and a Decentral Device ***
    ************************************************************************************
______________________________________ Description _____________________________________
- A central device and a decentral device are configured.
- The IP addresses and Subnet Masks are set in the project.
- The PROFINET device names are set in the project.
- The subnet is not configured in the project. Because there is only one central device, 
and its "SubnetRefID", "SyncDomainRefID" and "IOSystemRefID" are empty, PNConfigLib will 
create the default subnet, sync domain and IO system.
-->
<Configuration schemaVersion="1.0"
               ConfigurationID="ConfigurationID"
               ConfigurationName="ConfigurationName"
               ListOfNodesRefID="ListOfNodesID"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns="http://www.siemens.com/Automation/PNConfigLib/Configuration"
               xsi:schemaLocation="http://www.siemens.com/Automation/PNConfigLib/Configuration Configuration.xsd">
    <Devices>
        <CentralDevice DeviceRefID="PN_Driver_1">
            <CentralDeviceInterface InterfaceRefID="PN_Driver_1_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************"
                                         RouterAddress="***********00" />
                    </IPProtocol>
                    <PROFINETDeviceName>
                        <PNDeviceName>pndriver1</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
                <AdvancedOptions>
                    <RealTimeSettings>
                        <IOCommunication SendClock="1" />
                    </RealTimeSettings>
                </AdvancedOptions>
            </CentralDeviceInterface>
        </CentralDevice>
        <DecentralDevice DeviceRefID="ET200SP_1">
            <DecentralDeviceInterface InterfaceRefID="ET200SP_1_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************"/>
                    </IPProtocol>
                    <PROFINETDeviceName DeviceNumber="1">
                        <PNDeviceName>et200sp1</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
            </DecentralDeviceInterface>
        </DecentralDevice>
    </Devices>
</Configuration>