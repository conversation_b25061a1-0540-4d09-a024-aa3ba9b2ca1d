/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: GetNetworkParameter.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.Methods
{
    /// <summary>
    /// Contains constants and types for the generic method: GetNetworkParameter
    /// </summary>
    internal static class GetNetworkParameter
    {
        /// <summary>
        /// Whether alignment is used.
        /// Type: bool
        /// </summary>
        public const string Alignment = "Alignment";

        /// <summary>
        /// Block version for the data block to be retrieved.
        /// Type: Int32
        /// </summary>
        public const string Blockversion = "Blockversion";

        /// <summary>
        /// Whether the controller supports IP Address validation block 0x0100.
        /// </summary>
        public const string IsControllerSupportBv0X0100 = "IsControllerSupportBv0X0100";

        /// <summary>
        /// Whether the data records are retrieved for a decentral device.
        /// Type: bool
        /// </summary>
        public const string IsDecentral = "IsDecentral";

        /// <summary>
        /// Name der GenericMethod
        /// </summary>
        public const string Name = "GetNetworkParameter";

        /// <summary>
        /// ID of the data set to be retrieved.
        /// Type: Int32
        /// </summary>
        public const string SelectedDataset = "SelectedDataset";
    }
}