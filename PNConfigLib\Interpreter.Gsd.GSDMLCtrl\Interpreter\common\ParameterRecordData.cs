/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ParameterRecordData.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Data;
using System.Diagnostics;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ParameterRecordData object describes the data structure of a 
    /// parameter record.
    /// </summary>
    public class ParameterRecordData :
        RecordData,
        GSDI.IParameterRecordData
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ParameterRecordData if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ParameterRecordData()
        {
            // Initialize the properties
            m_TransferSequence = 0;
            m_ChangeableWithBump = false;
            m_RecordDataID = string.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private ArrayList m_Refs;
        private ArrayList m_MenuItems;
        private uint m_TransferSequence;
        private bool m_ChangeableWithBump;
        private string m_RecordDataID;
        private ArrayList m_Access;


        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of RefData objects, which are used to reference a section
        /// in a record data block.
        /// </summary>
        public virtual Array Refs =>
            this.m_Refs?.ToArray();

        // RQ AP01303495 begin
        /// <summary>
        /// Accesses a list of MenuItem objects, which are used to reference a parameter
        /// in a record data block.
        /// </summary>
        public virtual Array MenuItems =>
            this.m_MenuItems?.ToArray();
        // RQ AP01303495 end

        /// <summary>
        /// Accesses the transfer sequence of the parameter record data objects, which
        /// controls the transfer sequence during start-up.
        /// </summary>
        /// <remarks>The transfer sequence shall be unique within a submodule. The first 
        /// transferred parameter record data object shall have this attribute set to 1, 
        /// the following objects shall have this attribute incremented in direct sequence 
        /// (without gaps). If this attribute is set to 0, then the transfer sequence is 
        /// undefined and the transfer sequence of all parameter record data objects within
        /// the submodule must be 0.</remarks>
        public UInt32 TransferSequence => this.m_TransferSequence;

        public Boolean ChangeableWithBump => this.m_ChangeableWithBump;

        public String RecordDataID => this.m_RecordDataID;

        protected new object Length => ((RecordData)this).Length;

        protected new object Index => ((RecordData)this).Index;

        public virtual Array Access =>
            (null != this.m_Access) ?
                m_Access.ToArray() :
                null;

        #region COM Interface Members Only

        #endregion

        #endregion

        #region Methods
        /// <summary>
        /// ...
        /// </summary>
        public virtual MenuData GetMenuData(string bstrName)
        {
            // Check whether MenuItem list exists.
            if (null == this.MenuItems)
                return null;	// ---------->

            // Search for MenuData with given MenuData name.
            foreach (MenuData md in this.MenuItems)
            {
                if (md.MenuItemID == bstrName)
                    return md;	// ---------->
            }
            return null;
        }


        #region COM Interface Methods Only

        #endregion

        #endregion

        //########################################################################################
        #region Object members

        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(ParameterRecordData))
                return false;

            ParameterRecordData prd1 = this;
            ParameterRecordData prd2 = obj as ParameterRecordData;

            if (prd2 == null)
            {
                return false;
            }

            if (prd1.Index != prd2.Index)
                return false;

            if (prd1.Length != prd2.Length)
                return false;

            if (prd1.TransferSequence != prd2.TransferSequence)
                return false;

            if (!EqualsParameterRecordData(prd1, prd2))
            {
                return false;
            }

            // RQ AP01303495 begin
            if (!EqualsMenuItems(prd1, prd2))
            {
                return false;
            }
            // RQ AP01303495 end

            if (!EqualsConsts(prd1, prd2))
            {
                return false;
            }

            return true;
        }

        private static bool EqualsConsts(ParameterRecordData prd1, ParameterRecordData prd2)
        {
            Array prd1Consts = prd1.Consts;
            Array prd2Consts = prd2.Consts;

            if ((prd1Consts == null && prd2Consts != null)
                || (prd1Consts != null && prd2Consts == null))
                return false;

            if (prd1Consts == null)
            {
                return true;
            }

            if (prd1Consts.Length != prd2Consts.Length)
                return false;

            for (int j = 0; j < prd1Consts.Length; j++)
            {
                if (!prd1Consts.GetValue(j).Equals(prd2Consts.GetValue(j)))
                    return false;
            }

            return true;
        }

        private static bool EqualsMenuItems(ParameterRecordData prd1, ParameterRecordData prd2)
        {
            Array prd1MenuItems = prd1.MenuItems;
            Array prd2MenuItems = prd2.MenuItems;

            if (prd1MenuItems == null
                && prd2MenuItems != null)
                return false;

            if (prd1MenuItems != null
                && prd2MenuItems == null)
                return false;

            if (prd1MenuItems == null)
            {
                return true;
            }

            if (prd1MenuItems.Length != prd2MenuItems.Length)
                return false;

            for (int i = 0; i < prd1MenuItems.Length; i++)
            {
                if (!prd1MenuItems.GetValue(i).Equals(prd2MenuItems.GetValue(i)))
                    return false;
            }

            return true;
        }

        private static bool EqualsParameterRecordData(ParameterRecordData prd1, ParameterRecordData prd2)
        {
            Array prd1Refs = prd1.Refs;
            Array prd2Refs = prd2.Refs;

            if (prd1Refs == null
                && prd2Refs != null)
                return false;

            if (prd1Refs != null
                && prd2Refs == null)
                return false;

            if (prd1Refs == null)
            {
                return true;
            }

            if (prd1Refs.Length != prd2Refs.Length)
                return false;

            for (int i = 0; i < prd1Refs.Length; i++)
            {
                if (!prd1Refs.GetValue(i).Equals(prd2Refs.GetValue(i)))
                    return false;
            }

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 19;

            hash = hash * 11 + Index.GetHashCode();
            hash = hash * 11 + Length.GetHashCode();
            hash = hash * 11 + TransferSequence.GetHashCode();

            foreach (RefData refData in Refs)
            {
                hash = hash * 11 + refData.GetHashCode();
            }
            if (MenuItems != null)
            {
                foreach (MenuData menuData in MenuItems)
                {
                    hash = hash * 11 + menuData.GetHashCode();
                }
            }
            if (Consts != null)
            {
                foreach (ConstData constData in Consts)
                {
                    hash = hash * 11 + constData.GetHashCode();
                }
            }

            return hash;
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldRefs;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_Refs = hash[member] as ArrayList;

                // RQ AP01303495 begin
                member = Models.s_FieldMenuItems;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_MenuItems = hash[member] as ArrayList;
                // RQ AP01303495 end

                member = Models.s_FieldTransferSequence;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    this.m_TransferSequence = (uint)hash[member];

                member = Models.s_FieldChangeableWithBump;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    this.m_ChangeableWithBump = (bool)hash[member];

                member = Models.s_FieldRecordDataId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_RecordDataID = (string)hash[member];
                member = Models.s_FieldAccess;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_Access = hash[member] as ArrayList;
                // Base data.
                succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectParameterRecordData);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            // From base class
            base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldTransferSequence, this.m_TransferSequence);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldChangeableWithBump, this.m_ChangeableWithBump);
            Export.WriteStringProperty(ref writer, Models.s_FieldRecordDataId, this.m_RecordDataID);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldAccess, m_Access, true);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldRefs, this.m_Refs);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldMenuItems, this.m_MenuItems); // RQ AP01303495

            return true;
        }

        #endregion

    }
}


