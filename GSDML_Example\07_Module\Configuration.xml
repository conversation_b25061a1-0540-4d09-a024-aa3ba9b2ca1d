<!--***********************************************************************
  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      
***************************************************************************
  This program is protected by German copyright law and international      
  treaties. The use of this software including but not limited to its      
  Source Code is subject to restrictions as agreed in the license          
  agreement between you and Siemens.                                       
  according to your license agreement with Siemens.                        
  Copying or distribution is not allowed unless expressly permitted        
***************************************************************************
                                                                           
  P r o j e c t         Basic Components                            
                                                                           
  P a c k a g e         PROFINET Configuration Library      
                                                                           
  C o m p o n e n t     Examples                          
                                                                           
  F i l e               Configuration.xml                 
                                                                           
***************************************************************************-->

<!--************************************************************************************
    ************************* Example For Module Configuration *************************
    ************************************************************************************
______________________________________ Description _____________________________________
- The modules plugged on the decentral devices are configured.
- A unique ID within the file is set for each module.
- The slot numbers on which the modules are plugged are set.
- The ID of the related "ModuleItem" is referred in the "GSDRefID" XML attribute.

_________________________________ Additional Information _______________________________
- A central device and three decentral devices are configured.
- The IP addresses and Subnet Masks are set in the project.
- The PROFINET device names are set in the project.
- The subnet is not configured in the project. Because there is only one central device, 
and its "SubnetRefID", "SyncDomainRefID" and "IOSystemRefID" are empty, PNConfigLib will 
create the default subnet, sync domain and IO system.
-->
<Configuration schemaVersion="1.0"
               ConfigurationID="ConfigurationID"
               ConfigurationName="ConfigurationName"
               ListOfNodesRefID="ListOfNodesID"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xmlns="http://www.siemens.com/Automation/PNConfigLib/Configuration"
               xsi:schemaLocation="http://www.siemens.com/Automation/PNConfigLib/Configuration Configuration.xsd">
    <Devices>
        <CentralDevice DeviceRefID="PN_Driver_1">
            <CentralDeviceInterface InterfaceRefID="PN_Driver_1_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************"
                                         RouterAddress="***********00" />
                    </IPProtocol>
                    <PROFINETDeviceName>
                        <PNDeviceName>pndriver1</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
                <AdvancedOptions>
                    <RealTimeSettings>
                        <IOCommunication SendClock="1" />
                    </RealTimeSettings>
                </AdvancedOptions>
            </CentralDeviceInterface>
        </CentralDevice>
        <DecentralDevice DeviceRefID="ET200SP_1">
            <DecentralDeviceInterface InterfaceRefID="ET200SP_1_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************"/>
                    </IPProtocol>
                    <PROFINETDeviceName DeviceNumber="1">
                        <PNDeviceName>et200sp1</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
            </DecentralDeviceInterface>
            <!-- GSDML reference of the plugged module: <ModuleItemRef ModuleItemTarget="DI 8x24VDC HF V2.0" AllowedInSlots="1..32" />
                 This module has a virtual submodule with IO data. In this case, its IO addresses must be configured. 
                 Because it has only one virtual submodule, this configuration can be directly done under the module. 
                 Alternatively, it can be done under the submodule as in the "11_IO_Addresses" example.
                 GSDML reference of the virtual submodule: <VirtualSubmoduleItem ID="DI 8x24VDC HF V2.0_01" SubmoduleIdentNumber="0x00000008" FixedInSubslots="1" Writeable_IM_Records="1 2 3" MayIssueProcessAlarm="true">  -->
            <Module ModuleID="di8hf-sp_1"
                    SlotNumber="1"
                    GSDRefID="DI 8x24VDC HF V2.0">
                <IOAddresses>
                    <InputAddresses StartAddress="1" />
                </IOAddresses> 
            </Module>
            <!-- GSDML reference of the plugged module: <ModuleItemRef ModuleItemTarget="DI 8x24VDC HF V2.0" AllowedInSlots="1..32" />
                 This module has a virtual submodule with IO data. In this case, its IO addresses must be configured. 
                 Because it has only one virtual submodule, this configuration can be directly done under the module. 
                 Alternatively, it can be done under the submodule as in the "11_IO_Addresses" example.
                 GSDML reference of the virtual submodule: <VirtualSubmoduleItem ID="DI 8x24VDC HF V2.0_01" SubmoduleIdentNumber="0x00000008" FixedInSubslots="1" Writeable_IM_Records="1 2 3" MayIssueProcessAlarm="true">  -->
            <Module ModuleID="di8hf-sp_2"
                    SlotNumber="2"
                    GSDRefID="DI 8x24VDC HF V2.0">
                <IOAddresses>
                    <InputAddresses StartAddress="2" />
                </IOAddresses> 
            </Module>
            <!-- GSDML reference of the plugged module: <ModuleItemRef ModuleItemTarget="DI 8x24VDC HF V1.2, MSI" AllowedInSlots="1..32" /> -->
            <Module ModuleID="di8hf-sp_3"
                    SlotNumber="3"
                    GSDRefID="DI 8x24VDC HF V1.2, MSI">
                    <Submodule SubmoduleID="Submodulewith200bytesinputand200bytesoutput1"
                           SubslotNumber="1"
                           GSDRefID="DI 8x24VDC HF V1.2 QI1">
                    <IOAddresses>
                        <InputAddresses StartAddress="20" />
                    </IOAddresses>
                </Submodule>
            </Module>
            <!-- GSDML reference of the module: <ModuleItemRef ModuleItemTarget="Servermodule_without_io" AllowedInSlots="1..33" /> -->
            <Module ModuleID="servermodule"
                    SlotNumber="4"
                    GSDRefID="Servermodule_without_io"/>
        </DecentralDevice>
        <DecentralDevice DeviceRefID="SCALANCEX4143E_1">
            <DecentralDeviceInterface InterfaceRefID="SCALANCEX4143E_1_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************" />
                    </IPProtocol>
                    <PROFINETDeviceName DeviceNumber="2">
                        <PNDeviceName>scalance-x4143e-1</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
            </DecentralDeviceInterface>
            <!-- PNConfigLib automatically configures the modules with the "UsedInSlots" attribute with their default settings, even if they are not specified in the configuration file.
                 To remove it, it should be introduced in the configuration file with an empty GSDRefID (GSDRefID="").
                 GSDML reference of the removed module: <ModuleItemRef ModuleItemTarget="ID_Mod_05" UsedInSlots="5"/> -->
            <Module ModuleID="Module_05"
                    SlotNumber="5"
                    GSDRefID=""/>
            <!-- GSDML reference of the module: <ModuleItemRef ModuleItemTarget="ID_Mod_06" AllowedInSlots="12..13"/> -->
            <Module ModuleID="Module_06"
                    SlotNumber="12"
                    GSDRefID="ID_Mod_06"/>
        </DecentralDevice>
        <DecentralDevice DeviceRefID="SCALANCEX4143E_2">
            <DecentralDeviceInterface InterfaceRefID="SCALANCEX4143E_2_Interface">
                <EthernetAddresses>
                    <IPProtocol>
                        <SetInTheProject IPAddress="***********"
                                         SubnetMask="*************" />
                    </IPProtocol>
                    <PROFINETDeviceName DeviceNumber="3">
                        <PNDeviceName>scalance-x4143e-2</PNDeviceName>
                    </PROFINETDeviceName>
                </EthernetAddresses>
            </DecentralDeviceInterface>
            <!-- PNConfigLib automatically configures the modules with the "UsedInSlots" attribute with their default settings, even if they are not specified in the configuration file.
                 To replace it with a different module, the new module should be introduced in the configuration file with the same slot or subslot number. Here:
                 GSDML reference of the replaced module: <ModuleItemRef ModuleItemTarget="ID_Mod_05" UsedInSlots="5"/>
                 GSDML reference of the plugged module: <ModuleItemRef ModuleItemTarget="ID_Mod_12" AllowedInSlots="5"/> -->
            <Module ModuleID="Module_12"
                    SlotNumber="5"
                    GSDRefID="ID_Mod_12"/>
        </DecentralDevice>
    </Devices>
</Configuration>