/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV01_00.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Globalization;
using System.Xml;
using System.Xml.Schema;
using System.Collections;
using System.Collections.Specialized;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Linq;

using GSDI;
using System.Xml.Linq;
using System.Xml.XPath;
using System.Reflection;

using PNConfigLib.Gsd.Interpreter.Common;
using System.Runtime.InteropServices.ComTypes;
using PNConfigLib.Gsd.Interpreter;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented
    /// for the GSD(ML) version 1.0.
    ///		
    /// </summary>
    internal class CheckerV0100 : CheckerObject
    {
        //########################################################################################

        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 1.0.
        /// </summary>
        public CheckerV0100()
        {
            m_SchemaPath = String.Empty;

            m_AllExistingGsdmlVersions = new List<string>();

            SetSupportedGsdmlVersion(Constants.s_Version10);

        }

        #endregion

        //########################################################################################

        #region Fields

        // Declaration of the properties
        private string m_SupportedGsdmlVersion;

        private string m_FileNameGsdmlVersion;

        protected List<string> m_AllExistingGsdmlVersions;

        public static Dictionary<string, string> ElementDescriptions { get; } = new Dictionary<string, string>();

        private IList<string> m_Checks;

        private IList<string> m_DeregisteredChecks;

        private XDocument m_Gsd;

        private XElement m_GsdProfileBody;

        private ReportStore m_ReportStore;

        private XmlNamespaceManager m_Nsmgr;

        private IDictionary<string, string> m_CheckParameter;

        private string m_SchemaPath;

        bool m_UseExternalSchemaPath;

        private GSDI.SignatureCheckResults m_SignatureCheckingResult;

        private IDictionary<string, string> m_Namespaces;

        private IDictionary<string, string> m_CacheContent;

        private Stack m_Context;

        private ValidationEventArgs m_ValidationEventArgs;

        // Dictionaries for fast access
        protected Dictionary<XElement, IList<XElement>> PortToDapDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<XElement, IList<XElement>> DapToPortDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<XElement, IList<XElement>> SubmoduleToDapDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<XElement, IList<XElement>> DapToSubmoduleDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<string, XElement> PluggablePortSubmoduleItems = new Dictionary<string, XElement>();

        protected Dictionary<string, XElement> PluggableSubmoduleItems = new Dictionary<string, XElement>();

        protected Dictionary<string, XElement> PluggableModuleItems = new Dictionary<string, XElement>();

        protected Dictionary<string, XElement> ValueItems = new Dictionary<string, XElement>();

        protected Dictionary<XElement, IList<XElement>> ModuleRefToFixedPortDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<XElement, IList<XElement>> ModuleRefToPluggablePortDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<XElement, IList<XElement>> ModuleRefToSubmoduleDictionary =
            new Dictionary<XElement, IList<XElement>>();

        protected Dictionary<uint, SlotInfo> SlotToSlotUsage = new Dictionary<uint, SlotInfo>();

        protected Dictionary<uint, IList<XElement>> SlotAsAllowedValue = new Dictionary<uint, IList<XElement>>();

        protected Dictionary<string, IList<string>> AttributeTokenDictionary = new Dictionary<string, IList<string>>();

        protected Dictionary<string, string> AttributeValueListDictionary = new Dictionary<string, string>();

        #endregion

        //########################################################################################

        #region Properties

        protected virtual XDocument Gsd
        {
            get => m_Gsd;
            set => m_Gsd = value;
        }

        protected virtual XElement GsdProfileBody
        {
            get => m_GsdProfileBody;
            set => m_GsdProfileBody = value;
        }

        protected virtual ReportStore Store
        {
            get => m_ReportStore;
            set => m_ReportStore = value;
        }

        protected virtual GSDI.SignatureCheckResults SignatureCheckingResult
        {
            get => m_SignatureCheckingResult;
            set => m_SignatureCheckingResult = value;
        }

        protected virtual XmlNamespaceManager Nsmgr
        {
            get => m_Nsmgr;
            set => m_Nsmgr = value;
        }

        protected IList<string> Checks
        {
            get => m_Checks;
            set => m_Checks = value;
        }

        protected virtual IList<string> DeregisteredChecks
        {
            get => m_DeregisteredChecks;
            set => m_DeregisteredChecks = value;
        }

        protected virtual Stack Context
        {
            get => m_Context;
            set => m_Context = value;
        }

        protected virtual ValidationEventArgs ValidationEventArgs
        {
            get => m_ValidationEventArgs;
            set => m_ValidationEventArgs = value;
        }

        protected virtual IDictionary<string, string> CheckParameter
        {
            get
            {
                if (m_CheckParameter == null)
                    m_CheckParameter = new Dictionary<string, string>();
                return m_CheckParameter;
            }
            set => m_CheckParameter = value;
        }


        protected virtual IDictionary<string, string> Namespaces
        {
            get => m_Namespaces;
            set => m_Namespaces = value;
        }

        protected virtual XNamespace NamespaceGsdDef { get; set; }

        protected virtual IDictionary<string, string> CacheContent
        {
            get => m_CacheContent;
            set => m_CacheContent = value;
        }

        protected List<string> AllExistinggsdmlVersions => m_AllExistingGsdmlVersions;

        protected ValueListHelper ValueListHelper { get; set; }

        /// <summary>
        /// Version of the GSDML specification, which is extracted from given GSDML-File.
        /// </summary>
        public virtual string FileNameGsdmlVersion => m_FileNameGsdmlVersion;

        protected virtual GSDI.ReportTypes ReportType_0X000100093 => ReportTypes.GSD_RT_Warning;

        protected virtual GSDI.ReportTypes ReportType_0X000100181 => ReportTypes.GSD_RT_Warning;

        protected virtual GSDI.ReportTypes ReportType_0X00010020 => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X000100132 => ReportTypes.GSD_RT_Warning;

        protected virtual GSDI.ReportTypes ReportType_0X00010015 => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X000100161 => ReportTypes.GSD_RT_Warning;

        protected virtual GSDI.ReportTypes ReportType_0X000100166 => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X00010022 => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X00010026 => ReportTypes.GSD_RT_Warning;

        protected virtual GSDI.ReportTypes ReportType_0X0001002C => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X0001002D => ReportTypes.GSD_RT_Warning;


        protected virtual GSDI.ReportTypes ReportType_0X000100302 => ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X00010034 => ReportTypes.GSD_RT_Warning;

        protected virtual string DataTypesRequiresLength => "(@DataType='VisibleString' or @DataType='OctetString')";


    #endregion

    //########################################################################################

    #region Methods

    protected void SetSupportedGsdmlVersion(string version)
    {
        m_SupportedGsdmlVersion = version;
        m_AllExistingGsdmlVersions.Add(version);
        ValueListHelper = new ValueListHelper();
    }

    protected void SetFileNameGsdmlVersion()
    {
        // Possibly the name is not given for checking.
        string fileName = String.Empty;
        string fileNameVersion = String.Empty;
        string fileNamePattern = @"-(v[0-9]+\.[0-9]*)";

        if (CheckParameter.ContainsKey("GsdName")
            && CheckParameter["GsdName"] != null)
            fileName = CheckParameter["GsdName"] as string;

        if (fileName.Length > 0)
        {
            // get version from file name
            Match fileNameMatch = Regex.Match(
                fileName.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture),
                fileNamePattern);
            fileNameVersion = fileNameMatch.Groups[1].ToString();
        }

        if (String.IsNullOrEmpty(fileNameVersion))
        {
            var nl = (IEnumerable)Gsd.XPathEvaluate("/gsddef:ISO15745Profile/@gsdxsi:schemaLocation", Nsmgr);
            XAttribute att = nl.Cast<XAttribute>().FirstOrDefault();
            if (att != null)
            {
                string schemaLocation = att.Value;
                Match fileNameMatch = Regex.Match(
                    schemaLocation.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture),
                    fileNamePattern);
                fileNameVersion = fileNameMatch.Groups[1].ToString();
            }
        }

        m_FileNameGsdmlVersion = fileNameVersion;
    }

    protected virtual bool RegisterChecks()
    {
        if (Checks == null)
            Checks = new List<string>();

        Checks.Add(
            Constants.s_Cn_0X00010002); // From here to CN_0x0001050 are checks for syntax that must be done at the beginning
        Checks.Add(Constants.s_Cn_0X00010044);
        Checks.Add(Constants.s_Cn_0X00010045);
        Checks.Add(Constants.s_Cn_0X00010046);
        Checks.Add(Constants.s_Cn_0X00010047);
        Checks.Add(Constants.s_Cn_0X00010048);
        Checks.Add(Constants.s_Cn_0X00010049);
        Checks.Add(Constants.s_Cn_0X0001004A);
        Checks.Add(Constants.s_Cn_0X0001004B);
        Checks.Add(Constants.s_Cn_0X0001004C);
        Checks.Add(Constants.s_Cn_0X0001004D);

        Checks.Add(Constants.s_Cn_0X00010003);
        Checks.Add(Constants.s_Cn_0X00010004);
        Checks.Add(Constants.s_Cn_0X00010005);
        Checks.Add(Constants.s_Cn_0X00010006);
        Checks.Add(Constants.s_Cn_0X00010007);
        Checks.Add(Constants.s_Cn_0X00010008);
        Checks.Add(Constants.s_Cn_0X00010009);
        Checks.Add(Constants.s_Cn_0X0001000A);
        Checks.Add(Constants.s_Cn_0X0001000B);
        Checks.Add(Constants.s_Cn_0X0001000C);
        Checks.Add(Constants.s_Cn_0X0001000D);
        Checks.Add(Constants.s_Cn_0X0001000E);
        Checks.Add(Constants.s_Cn_0X0001000F);
        Checks.Add(Constants.s_Cn_0X00010010);
        Checks.Add(Constants.s_Cn_0X00010011);
        Checks.Add(Constants.s_Cn_0X00010012);
        Checks.Add(Constants.s_Cn_0X00010013);

        Checks.Add(Constants.s_Cn_0X00010015);
        Checks.Add(Constants.s_Cn_0X00010016);
        Checks.Add(Constants.s_Cn_0X00010017);
        Checks.Add(Constants.s_Cn_0X00010018);
        Checks.Add(Constants.s_Cn_0X00010019);
        Checks.Add(Constants.s_Cn_0X00010020);
        Checks.Add(Constants.s_Cn_0X00010021);
        Checks.Add(Constants.s_Cn_0X00010022);
        Checks.Add(Constants.s_Cn_0X00010023);
        Checks.Add(Constants.s_Cn_0X00010024);
        Checks.Add(Constants.s_Cn_0X00010025);
        Checks.Add(Constants.s_Cn_0X00010026);
        Checks.Add(Constants.s_Cn_0X00010027);
        Checks.Add(Constants.s_Cn_0X00010028);
        Checks.Add(Constants.s_Cn_0X00010029);
        Checks.Add(Constants.s_Cn_0X0001002A);
        Checks.Add(Constants.s_Cn_0X0001002B);
        Checks.Add(Constants.s_Cn_0X0001002C);
        Checks.Add(Constants.s_Cn_0X0001002D);
        Checks.Add(Constants.s_Cn_0X0001002E);
        Checks.Add(Constants.s_Cn_0X0001002F);
        Checks.Add(Constants.s_Cn_0X00010030);
        Checks.Add(Constants.s_Cn_0X00010031);
        Checks.Add(Constants.s_Cn_0X00010032);
        Checks.Add(Constants.s_Cn_0X00010033);
        Checks.Add(Constants.s_Cn_0X00010034);
        Checks.Add(Constants.s_Cn_0X00010035);
        Checks.Add(Constants.s_Cn_0X00010043);

        return true;
    }

    protected virtual bool DeregisterObsoleteChecks()
    {
        return true;
    }

    /// <summary>
    /// CheckForUnexpectedDecimalDigits
    /// 
    /// The GSDML Schemas contain at different locations the type "string" with a pattern as restriction.
    /// These patterns follow the regular expression pattern syntax.
    /// And on some locations, the character class "\d" for decimal digit was used.
    /// The intention was to allow only the characters "0" to "9", but "\d" is short for "\p{Nd}",
    /// that means the Unicode character class decimal digit.
    /// 
    /// Therefore this patterns must be double checked here.
    /// 
    /// This happens in the Primitives schema at the types
    /// (1) ValueListT
    /// (2) SignedOrFloatValueListT
    /// (3) VersionStringT
    /// 
    /// and in the GSDML schema at the attributes
    /// (4) FieldbusIntegrationSlots/@Range
    /// (5) Assign/@Content.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckForUnexpectedDecimalDigits()
    {
        bool succeeded = true;

        // (1)
        succeeded = CheckForUnexpectedDecimalDigits_ValueListT(succeeded);


        // (2)
        succeeded = CheckForUnexpectedDecimalDigits_SignedOrFloatValueListT(succeeded);


        // (3)
        succeeded = CheckForUnexpectedDecimalDigits_VersionStringT(succeeded);


        // (4)
        succeeded = CheckForUnexpectedDecimalDigits_FieldbusIntegrationSlots(succeeded);


        // (5)
        succeeded = CheckForUnexpectedDecimalDigits_Assign(succeeded);

        return succeeded;

    }

    private bool CheckForUnexpectedDecimalDigits_Assign(bool succeeded)
    {
        var nl5 = GsdProfileBody.Descendants().Attributes()
            .Where(attribute => attribute.Name.LocalName == Attributes.s_Content);
        nl5 = Help.TryRemoveXAttributesUnderXsAny(nl5, Nsmgr, Gsd);

        foreach (XAttribute an in nl5)
        {
            var parent = an.Parent;
            if (parent != null
                && parent.Name.LocalName != Elements.s_Assign)
            {
                continue;
            }

            string pattern = "^-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?$";

            Match digitMatch = Regex.Match(an.Value, pattern);

            if (digitMatch.Success)
            {
                continue;
            }

            // "Decimal digits must only contains the characters "0" to "9"."
            string msg = Help.GetMessageString("M_DecimalDigits");
            string xpath = Help.GetXPath(an);
            var xli = (IXmlLineInfo)an;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.Validation,
                "DecimalDigits");
            succeeded = false;
        }

        return succeeded;
    }

    private bool CheckForUnexpectedDecimalDigits_FieldbusIntegrationSlots(bool succeeded)
    {
        var nl4 = GsdProfileBody.Descendants().Attributes()
            .Where(attribute => attribute.Name.LocalName == Attributes.s_Range);
        nl4 = Help.TryRemoveXAttributesUnderXsAny(nl4, Nsmgr, Gsd);

        foreach (XAttribute an in nl4)
        {
            var parent = an.Parent;
            if (parent == null
                || parent.Name.LocalName != Elements.s_FieldbusIntegrationSlots)
            {
                continue;
            }

            string pattern = "^[0-9]+\\.\\.[0-9]+$";

            Match digitMatch = Regex.Match(an.Value, pattern);

            if (digitMatch.Success)
            {
                continue;
            }

            // "Decimal digits must only contains the characters "0" to "9"."
            string msg = Help.GetMessageString("M_DecimalDigits");
            string xpath = Help.GetXPath(an);
            var xli = (IXmlLineInfo)an;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.Validation,
                "DecimalDigits");
            succeeded = false;

        }

        return succeeded;
    }

    private bool CheckForUnexpectedDecimalDigits_VersionStringT(bool succeeded)
    {
        var nl3 = GsdProfileBody.Descendants().Attributes().Where(
            attribute => attribute.Name.LocalName == Attributes.s_PNioVersion
                         || attribute.Name.LocalName == Attributes.s_RequiredSchemaVersion
                         || attribute.Name.LocalName == Attributes.s_ProfileVersion
                         || attribute.Name.LocalName == Attributes.s_PaProfileVersion);
        nl3 = Help.TryRemoveXAttributesUnderXsAny(nl3, Nsmgr, Gsd);

        string pattern = "^V[0-9]+\\.[0-9]+$";
        foreach (XAttribute an in nl3)
        {
            Match digitMatch = Regex.Match(an.Value, pattern);

            if (!digitMatch.Success)
            {
                // "Decimal digits must only contains the characters "0" to "9"."
                string msg = Help.GetMessageString("M_DecimalDigits");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.Validation,
                    "DecimalDigits");
                succeeded = false;
            }
        }

        return succeeded;
    }

    private bool CheckForUnexpectedDecimalDigits_SignedOrFloatValueListT(bool succeeded)
    {
        var nl2 = GsdProfileBody.Descendants().Attributes()
            .Where(attribute => attribute.Name.LocalName == Attributes.s_AllowedValues);
        nl2 = Help.TryRemoveXAttributesUnderXsAny(nl2, Nsmgr, Gsd);

        foreach (XAttribute an in nl2)
        {
            var parent = an.Parent;
            if (parent == null)
            {
                return false;
            }

            Match digitMatch;
            string pattern;

            if (parent.Name.LocalName == Elements.s_FBlockID
                || parent.Name.LocalName == Elements.s_FParVersion
                || parent.Name.LocalName == Elements.s_FSourceAdd
                || parent.Name.LocalName == Elements.s_FDestAdd
                || parent.Name.LocalName == Elements.s_FWdTime
                || parent.Name.LocalName == Elements.s_FParCrc
                || parent.Name.LocalName == Elements.s_FIParCrc)
            {
                pattern = "^(([0-9]+\\.\\.[0-9]+)|([0-9]+))(( [0-9]+\\.\\.[0-9]+)|( [0-9]+))*$";

                digitMatch = Regex.Match(an.Value, pattern);

                if (digitMatch.Success)
                {
                    continue;
                }

                // "Decimal digits must only contains the characters "0" to "9"."
                string msg = Help.GetMessageString("M_DecimalDigits");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.Validation,
                    "DecimalDigits");
                succeeded = false;

            }
            else if (parent.Name.LocalName == Elements.s_Ref)
            {
                pattern =
                    "^((-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|(-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))(( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))*$";

                digitMatch = Regex.Match(an.Value, pattern);

                if (digitMatch.Success)
                {
                    continue;
                }

                // "Decimal digits must only contains the characters "0" to "9"."
                string msg = Help.GetMessageString("M_DecimalDigits");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.Validation,
                    "DecimalDigits");
                succeeded = false;

            }
        }

        return succeeded;
    }

        private bool CheckForUnexpectedDecimalDigits_ValueListT(bool succeeded)
        {
            var nl1 = GsdProfileBody.Descendants().Attributes().Where(
                attribute => attribute.Name.LocalName == Attributes.s_PhysicalSlots
                             || attribute.Name.LocalName == Attributes.s_AllowedInSlots
                             || attribute.Name.LocalName == Attributes.s_FixedInSlots
                             || attribute.Name.LocalName == Attributes.s_UsedInSlots
                             || attribute.Name.LocalName == Attributes.s_SlotList
                             || attribute.Name.LocalName == Attributes.s_PhysicalSubslots
                             || attribute.Name.LocalName == Attributes.s_AllowedInSubslots
                             || attribute.Name.LocalName == Attributes.s_UsedInSubslots
                             || attribute.Name.LocalName == Attributes.s_FixedInSubslots
                             || attribute.Name.LocalName == Attributes.s_SendClock
                             || attribute.Name.LocalName == Attributes.s_ReductionRatio
                             || attribute.Name.LocalName == Attributes.s_ReductionRatioPow2
                             || attribute.Name.LocalName == Attributes.s_WriteableImRecords
                             || attribute.Name.LocalName == Attributes.s_MauTypes
                             || attribute.Name.LocalName == Attributes.s_FiberOpticTypes
                             || attribute.Name.LocalName == Attributes.s_SupportedSubstitutionModes
                             || attribute.Name.LocalName == Attributes.s_ResetToFactoryModes);
            nl1 = Help.TryRemoveXAttributesUnderXsAny(nl1, Nsmgr, Gsd);

            String pattern = "^(([0-9]+\\.\\.[0-9]+)|([0-9]+))(( [0-9]+\\.\\.[0-9]+)|( [0-9]+))*$";
            foreach (XAttribute an in nl1)
            {
                Match digitMatch = Regex.Match(an.Value, pattern);

                if (!digitMatch.Success)
                {
                    // "Decimal digits must only contains the characters "0" to "9"."
                    string msg = Help.GetMessageString("M_DecimalDigits");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.Validation,
                        "DecimalDigits");
                    succeeded = false;
                }
            }

            return succeeded;

        }

        protected virtual bool ProcessRegisteredChecks()
    {
        bool succeeded = true;

        Type type = GetType();

        try
        {
            RegisterChecks();

            if (DeregisteredChecks != null)
            {
                foreach (string deregisteredCheck in DeregisteredChecks)
                {
                    Checks.Remove(deregisteredCheck);
                }
            }

            DeregisterObsoleteChecks();

            // Process checks.
            if (Checks == null)
                throw new CheckerException("No checks registered for processing!");

            GenerateDictionariesForFastAccess();

            foreach (string check in Checks)
            {
                MethodInfo mi = type.GetMethod(
                    Constants.s_CheckCn + check,
                    BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.IgnoreCase);

                if (mi == null)
                {
                    continue;
                }

                bool ret = (bool)mi.Invoke(this, null);
                if (ret)
                {
                    continue;
                }

                succeeded = false;
            }
        }
        catch (Exception e)
        {
            throw new CheckerException("Error processing checks!", e);
        }

        return succeeded;
    }

    protected virtual void AddDeregisteredChecks(string ignoredChecks)
    {
        if (m_DeregisteredChecks == null)
            m_DeregisteredChecks = new List<string>();

        string[] checks = ignoredChecks.Split(';');
        foreach (string check in checks)
        {
            m_DeregisteredChecks.Add(check);
        }
    }

    protected virtual string GetValidationXPath()
    {
        // NOTE: A correct XPath could only be created, if the context objects
        //       are set by the validation process.
        string xpath = "";
        StringBuilder xpathBuilder = new StringBuilder();

        try
        {
            if (Context.Count != 0)
            {
                bool ready = false;
                IEnumerator enumerator = Context.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    if (ready)
                        break;

                    if (null == enumerator.Current)
                        continue;

                    ValidationContext c = (ValidationContext)enumerator.Current;
                    StringBuilder s = new StringBuilder();
                    s.Append(c.ElementName);
                    ready = GetValidationXPathAppendAttributes(c, s, ready);

                    if (!String.IsNullOrEmpty(xpath))
                    {
                        string sh = xpath;

                        xpathBuilder.Append(s.ToString());
                        xpathBuilder.Append("/");
                        xpathBuilder.Append(sh);
                        xpath = xpathBuilder.ToString();
                        xpathBuilder.Remove(0, xpathBuilder.Length);
                    }
                    else
                        xpath = s.ToString();
                }

                string sh2 = xpath;
                xpathBuilder.Append("//");
                xpathBuilder.Append(sh2);
                xpath = xpathBuilder.ToString();
                xpathBuilder.Remove(0, xpathBuilder.Length);
            }

        }
        catch (InvalidOperationException)
        {
            xpath = "";
        }

        return xpath;
    }

    private static bool GetValidationXPathAppendAttributes(ValidationContext c, StringBuilder s, bool ready)
    {
        if (null != c.Attributes
            && c.Attributes.Count != 0)
        {
            bool first = true;
            s.Append("[");
            foreach (string attr in c.Attributes.Keys)
            {
                if (!first)
                    s.Append(" && ");

                s.Append("@");
                s.Append(attr);
                s.Append(" = '");
                if (c.Attributes[attr] != null)
                    s.Append(c.Attributes[attr]);
                s.Append("'");

                first = false;

                if (attr == "ID")
                    ready = true;
            }

            s.Append("]");
        }

        return ready;
    }
    protected virtual bool InitNamespaces()
    {
        // --------------------------------------------------------------
        if (null == Namespaces)
        {
            // Prepare xml namespace hashtable.
            // Define your own namespace prefixes so that you can later use XPath expressions to access elements in these namespaces during the business logic tests.
            // This happens after the schema checks and has nothing to do with them.
            Namespaces = new Dictionary<string, string>();
            Namespaces.Add("gsddef", "http://www.profibus.com/GSDML/2003/11/DeviceProfile");
            Namespaces.Add("gsdbase", "http://www.profibus.com/GSDML/2003/11/Primitives");
            Namespaces.Add("gsdprim", "http://www.profibus.com/Common/2003/11/Primitives");
            Namespaces.Add("gsdxsi", "http://www.w3.org/2001/XMLSchema-instance");
            //Namespaces.Add("gsdxsd", "http://www.w3.org/2001/XMLSchema");
            Namespaces.Add("gsdsig", "http://www.w3.org/2000/09/xmldsig#");
            //Namespaces.Add("gsdXAdES", "http://uri.etsi.org/01903/v1.3.2#");
            Namespaces.Add("gsdxml", "http://www.w3.org/XML/1998/namespace");
        }

        return true;
    }

    protected void GenerateDictionariesForFastAccess()
    {
        GetPluggablePortSubmoduleItems();

        GetPluggableSubmoduleItems();

        GetPluggableModuleItems();
        GetValueItems();




        var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
        daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
        foreach (var dap in daps)
        {
            // (1) Get all PortSubmoduleItems at the DAP
            IList<XElement> ports = GetAllPortSubmoduleItemsAtTheDAP(dap);


            // (2) Check all UseableSubmodules at the DAP
            IList<XElement> submodules = CheckAllUseableSubmodulesAtTheDAP(dap, ports);


            // (3) Check all PortSubmoduleItems of modules at the module
            // (4) Check all VirtualSubmoduleItems of modules at the dap
            // (5) Check all UseableSubmodules of modules at the dap
            var moduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_ModuleItemRef);
            foreach (var moduleItemRef in moduleItemRefs)
            {
                XElement module = null;
                XAttribute moduleItemTarget = moduleItemRef.Attribute(Attributes.s_ModuleItemTarget);
                if (moduleItemTarget != null)
                {
                    string moduleTarget = Help.CollapseWhitespace(moduleItemTarget.Value);
                    if (PluggableModuleItems.ContainsKey(moduleTarget))
                        module = PluggableModuleItems[moduleTarget];
                }

                if (null == module)
                    continue;

                // (3) Check all PortSubmoduleItems of modules at the dap
                CheckAllPortSubmoduleItemsOfModulesAtTheDAP(module, dap, ports, moduleItemRef);


                // (4) Check all VirtualSubmoduleItems of modules at the dap
                CheckAllVirtualSubmoduleItemsOfModulesAtTheDAP(module, dap, submodules);


                // (5) Check all UseableSubmodules of modules at the DAP
                IList<XElement> pluggablePortsOfModule = CheckAllUseableSubmodulesOfModulesAtTheDAP(
                    module,
                    dap,
                    ports,
                    submodules,
                    out IList<XElement> submodulesOfModule);

                if (!ModuleRefToPluggablePortDictionary.ContainsKey(moduleItemRef))
                {
                    ModuleRefToPluggablePortDictionary.Add(moduleItemRef, pluggablePortsOfModule);
                    ModuleRefToSubmoduleDictionary.Add(moduleItemRef, submodulesOfModule);
                }
            }

            DapToPortDictionary.Add(dap, ports);
            DapToSubmoduleDictionary.Add(dap, submodules);
        }
    }

    private IList<XElement> CheckAllUseableSubmodulesOfModulesAtTheDAP(
        XContainer module,
        XElement dap,
        ICollection<XElement> ports,
        ICollection<XElement> submodules,
        out IList<XElement> submodulesOfModule)
    {
        var submoduleItemRefsModule = module.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
        IList<XElement> pluggablePortsOfModule = new List<XElement>();
        submodulesOfModule = new List<XElement>();
        foreach (var submoduleItemRef in submoduleItemRefsModule)
        {
            XElement portSubmodule = null;
            XElement submodule = null;

            XAttribute submoduleItemTargetAtt = submoduleItemRef.Attribute(Attributes.s_SubmoduleItemTarget);
            if (submoduleItemTargetAtt != null)
            {
                string submoduleItemTarget = Help.CollapseWhitespace(submoduleItemTargetAtt.Value);
                if (PluggablePortSubmoduleItems.ContainsKey(submoduleItemTarget))
                    portSubmodule = PluggablePortSubmoduleItems[submoduleItemTarget];
                if (PluggableSubmoduleItems.ContainsKey(submoduleItemTarget))
                    submodule = PluggableSubmoduleItems[submoduleItemTarget];
            }

            if (null != submodule)
                submodulesOfModule.Add(submodule);

            if (null == portSubmodule
                && null == submodule)
                continue;

            CheckAllUseableSubmodulesOfModulesAtTheDAP_CheckPortSubmodule(
                dap,
                ports,
                portSubmodule,
                pluggablePortsOfModule);

            CheckAllUseableSubmodulesOfModulesAtTheDAP_CheckSubmodule(dap, submodules, submodule);


        }

        return pluggablePortsOfModule;
    }

    private void CheckAllUseableSubmodulesOfModulesAtTheDAP_CheckSubmodule(
        XElement dap,
        ICollection<XElement> submodules,
        XElement submodule)
    {

        if (null == submodule)
        {
            return;
        }

        if (SubmoduleToDapDictionary.ContainsKey(submodule))
        {
            var dapsOfSubmodule = SubmoduleToDapDictionary[submodule];
            if (!dapsOfSubmodule.Contains(dap))
            {
                dapsOfSubmodule.Add(dap);
                SubmoduleToDapDictionary[submodule] = dapsOfSubmodule;
            }
        }
        else
        {
            var dapsOfSubmodule = new List<XElement>();
            dapsOfSubmodule.Add(dap);
            SubmoduleToDapDictionary.Add(submodule, dapsOfSubmodule);
        }

        if (!submodules.Contains(submodule))
        {
            submodules.Add(submodule);
        }
    }

    private void CheckAllUseableSubmodulesOfModulesAtTheDAP_CheckPortSubmodule(
        XElement dap,
        ICollection<XElement> ports,
        XElement portSubmodule,
        ICollection<XElement> pluggablePortsOfModule)
    {
        if (null == portSubmodule)
        {
            return;
        }

        pluggablePortsOfModule.Add(portSubmodule);

        if (PortToDapDictionary.ContainsKey(portSubmodule))
        {
            var dapsOfPort = PortToDapDictionary[portSubmodule];
            if (!dapsOfPort.Contains(dap))
            {
                dapsOfPort.Add(dap);
                PortToDapDictionary[portSubmodule] = dapsOfPort;
            }
        }
        else
        {
            var dapsOfPort = new List<XElement>();
            dapsOfPort.Add(dap);
            PortToDapDictionary.Add(portSubmodule, dapsOfPort);
        }

        if (!ports.Contains(portSubmodule))
        {
            ports.Add(portSubmodule);
        }
    }







    private void CheckAllVirtualSubmoduleItemsOfModulesAtTheDAP(
        XContainer module,
        XElement dap,
        ICollection<XElement> submodules)
    {
        var virtualSubmoduleItems = module.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
        foreach (var submodule in virtualSubmoduleItems)
        {
            if (SubmoduleToDapDictionary.ContainsKey(submodule))
            {
                IList<XElement> dapsOfSubmodule = SubmoduleToDapDictionary[submodule];
                if (!dapsOfSubmodule.Contains(dap))
                {
                    dapsOfSubmodule.Add(dap);
                    SubmoduleToDapDictionary[submodule] = dapsOfSubmodule;
                }
            }
            else
            {
                IList<XElement> dapsOfSubmodule = new List<XElement>();
                dapsOfSubmodule.Add(dap);
                SubmoduleToDapDictionary.Add(submodule, dapsOfSubmodule);
            }

            if (!submodules.Contains(submodule))
            {
                submodules.Add(submodule);
            }
        }
    }

    private void CheckAllPortSubmoduleItemsOfModulesAtTheDAP(
        XContainer module,
        XElement dap,
        ICollection<XElement> ports,
        XElement moduleItemRef)
    {
        var portSubmodules = module.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem);
        IList<XElement> fixedPortsOfModule = new List<XElement>();
        foreach (var portSubmodule in portSubmodules)
        {
            if (PortToDapDictionary.ContainsKey(portSubmodule))
            {
                IList<XElement> dapsOfPort = PortToDapDictionary[portSubmodule];
                if (!dapsOfPort.Contains(dap))
                {
                    dapsOfPort.Add(dap);
                    PortToDapDictionary[portSubmodule] = dapsOfPort;
                }
            }
            else
            {
                IList<XElement> dapsOfPort = new List<XElement>();
                dapsOfPort.Add(dap);
                PortToDapDictionary.Add(portSubmodule, dapsOfPort);
            }

            if (!ports.Contains(portSubmodule))
            {
                ports.Add(portSubmodule);
            }

            fixedPortsOfModule.Add(portSubmodule);
        }

        if (!ModuleRefToFixedPortDictionary.ContainsKey(moduleItemRef))
            ModuleRefToFixedPortDictionary.Add(moduleItemRef, fixedPortsOfModule);

    }

    private IList<XElement> CheckAllUseableSubmodulesAtTheDAP(XElement dap, ICollection<XElement> ports)
    {
        IList<XElement> submodules = new List<XElement>();
        var submoduleItemRefs = dap.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
        foreach (var submoduleItemRef in submoduleItemRefs)
        {
            XElement portSubmodule = null;
            XElement submodule = null;
            XAttribute submoduleItemTarget = submoduleItemRef.Attribute(Attributes.s_SubmoduleItemTarget);
            if (submoduleItemTarget != null)
            {
                string itemTarget = Help.CollapseWhitespace(submoduleItemTarget.Value);
                if (PluggablePortSubmoduleItems.ContainsKey(itemTarget))
                    portSubmodule = PluggablePortSubmoduleItems[itemTarget];
                if (PluggableSubmoduleItems.ContainsKey(itemTarget))
                    submodule = PluggableSubmoduleItems[itemTarget];
            }

            if (null == portSubmodule
                && null == submodule)
                continue;

            CheckAllUseableSubmodulesAtTheDAP_CheckPortSubmodule(dap, ports, portSubmodule);

            CheckAllUseableSubmodulesAtTheDAP_CheckSubmodule(dap, submodule, submodules);

        }

        return submodules;
    }

    private void CheckAllUseableSubmodulesAtTheDAP_CheckSubmodule(
        XElement dap,
        XElement submodule,
        ICollection<XElement> submodules)
    {
        if (null == submodule)
        {
            return;
        }

        if (SubmoduleToDapDictionary.ContainsKey(submodule))
        {
            IList<XElement> dapsOfSubmodule = SubmoduleToDapDictionary[submodule];
            if (!dapsOfSubmodule.Contains(dap))
            {
                dapsOfSubmodule.Add(dap);
            }
        }
        else
        {
            IList<XElement> dapsOfSubmodule = new List<XElement>();
            dapsOfSubmodule.Add(dap);
            SubmoduleToDapDictionary.Add(submodule, dapsOfSubmodule);
        }

        if (!submodules.Contains(submodule))
        {
            submodules.Add(submodule);
        }
    }

    private void CheckAllUseableSubmodulesAtTheDAP_CheckPortSubmodule(
        XElement dap,
        ICollection<XElement> ports,
        XElement portSubmodule)
    {
        if (null == portSubmodule)
        {
            return;
        }

        if (PortToDapDictionary.ContainsKey(portSubmodule))
        {
            IList<XElement> dapsOfPort = PortToDapDictionary[portSubmodule];
            if (!dapsOfPort.Contains(dap))
            {
                dapsOfPort.Add(dap);
            }
        }
        else
        {
            IList<XElement> dapsOfPort = new List<XElement>();
            dapsOfPort.Add(dap);
            PortToDapDictionary.Add(portSubmodule, dapsOfPort);
        }

        if (!ports.Contains(portSubmodule))
        {
            ports.Add(portSubmodule);
        }
    }

    private IList<XElement> GetAllPortSubmoduleItemsAtTheDAP(XElement dap)
    {
        IList<XElement> ports = new List<XElement>();
        var portSubmoduleItems = dap.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem);
        foreach (var portSubmoduleItem in portSubmoduleItems)
        {
            IList<XElement> dapsOfPort = new List<XElement>();
            dapsOfPort.Add(dap);
            PortToDapDictionary.Add(portSubmoduleItem, dapsOfPort);
            ports.Add(portSubmoduleItem);
        }

        return ports;
    }

    private void GetValueItems()
    {
        IEnumerable<XElement> nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ValueItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var valueItem in nl)
        {
            XAttribute xatt = valueItem.Attribute(Attributes.ID);
            if (xatt == null)
            {
                continue;
            }

            string id = Help.CollapseWhitespace(xatt.Value);
            if (ValueItems.ContainsKey(id))
            {
                continue;
            }

            ValueItems.Add(id, valueItem);

        }
    }

    private void GetPluggableModuleItems()
    {
        IEnumerable<XElement> nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var pluggableModuleItem in nl)
        {
            XAttribute xatt = pluggableModuleItem.Attribute(Attributes.ID);
            if (xatt == null)
            {
                continue;
            }

            string id = Help.CollapseWhitespace(xatt.Value);
            if (PluggableModuleItems.ContainsKey(id))
            {
                continue;
            }

            PluggableModuleItems.Add(id, pluggableModuleItem);

        }
    }

    private void GetPluggableSubmoduleItems()
    {
        IEnumerable<XElement> nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var pluggableSubmoduleItem in nl)
        {
            XAttribute xatt = pluggableSubmoduleItem.Attribute(Attributes.ID);
            if (xatt == null)
            {
                continue;
            }

            string id = Help.CollapseWhitespace(xatt.Value);
            if (PluggableSubmoduleItems.ContainsKey(id))
            {
                continue;
            }

            PluggableSubmoduleItems.Add(id, pluggableSubmoduleItem);


        }
    }

    private void GetPluggablePortSubmoduleItems()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var pluggablePortSubmoduleItem in nl)
        {
            XAttribute xatt = pluggablePortSubmoduleItem.Attribute(Attributes.ID);
            if (xatt == null)
            {
                continue;
            }

            string id = Help.CollapseWhitespace(xatt.Value);
            if (PluggablePortSubmoduleItems.ContainsKey(id))
            {
                continue;
            }

            PluggablePortSubmoduleItems.Add(id, pluggablePortSubmoduleItem);
        }
    }


    #endregion

    //########################################################################################

    #region CheckerObject Members

    /// <summary>
    /// Version of the GSDML specification, which is supported from the internal Checker.
    /// </summary>
    public override string SupportedGsdmlVersion => m_SupportedGsdmlVersion;

    /// <summary>
    /// Checks the given GSD(ML) document and reports problems at the report
    /// store mechanism.
    /// </summary>
    /// <param name="gsddocstream">Stream of the GSD(ML) document to check.</param>
    /// <param name="name">The name of the GSD to check. Can be 'null' also.</param>
    /// <param name="store">Store object which holds eventually created reports.</param>
    /// <param name="customArguments">Custom Arguments</param>
    /// <returns>True whether check was successful, else false.</returns>
    public override bool Check(Stream gsddocstream, string name, ReportStore store, NameValueCollection customArguments)
    {
        return InternalCheck(gsddocstream, name, store, customArguments);
    }

    /// <summary>
    /// Checks the given GSD(ML) document and reports problems at the report
    /// store mechanism.
    /// </summary>
    /// <param name="gsddocstream">Stream of the GSD(ML) document to check.</param>
    /// <param name="name">The name of the GSD to check. Can be 'null' also.</param>
    /// <param name="schemaPath">The path to the schema files (*.xsd)</param>
    /// <param name="store">Store object which holds eventually created reports.</param>
    /// <returns>True whether check was successful, else false.</returns>
    public override bool Check(Stream gsddocstream, string name, string schemaPath, ReportStore store)
    {
        m_SchemaPath = schemaPath;
        m_UseExternalSchemaPath = true;

        // NOTE: All check methods return a boolean value. If they return false, something went wrong
        // by execution. The meaning is not, that only a check didn't succeed (in this case a report
        // is created and true is returned), but the meaning is, that the execution flow breaks with
        // error exception, or something else goes completely wrong.
        // If the validation leads to an error report, the checks will be stopped also.

        return InternalCheck(gsddocstream, name, store, null);
    }

    private bool InternalCheck(Stream gsddocstream, string name, ReportStore store, NameValueCollection customArguments)
    {
        // Set local variables.
        CheckParameter.Add("GsdName", name);

        if (customArguments != null)
        {
            string ignoredChecks = customArguments[Constants.s_IgnoredChecks];
            if (ignoredChecks != null)
            {
                AddDeregisteredChecks(ignoredChecks);
            }
        }

        // Init Namespaces relevant for this Gsd version.
        bool succeeded = InitNamespaces();
        if (!succeeded)
        {
            return false; // ---------->
        }

        // Set needed internal store variable.
        Store = store;

        // Load Gsd.
        try
        {
            // Test input parameters.
            if (null == store
                || null == gsddocstream)
                throw new CheckerException("Bad report store or gsd document stream parameter given!");

            // Set needed internal store variable.
            Store = store;

            // Validate document.
            long position = gsddocstream.Position;
            succeeded = CheckForExternalTextFile(gsddocstream);
            if (!succeeded)
                return false; // ---------->

            gsddocstream.Position = position;
            succeeded = Validation(gsddocstream);
            if (!succeeded)
                return false; // ---------->

            // NOTE: If an error report is available after validation, the checks will be stopped!
            if (store.ErrorFlag)
                return true; // ---------->

            // Load Gsd from stream.
            gsddocstream.Position = position;
            XmlReaderSettings readerSettings = new();
            readerSettings.CheckCharacters = true;
            readerSettings.ConformanceLevel = ConformanceLevel.Document;
            readerSettings.IgnoreComments = false;
            readerSettings.IgnoreWhitespace = false;
            readerSettings.DtdProcessing = DtdProcessing.Prohibit;
            readerSettings.ValidationFlags = XmlSchemaValidationFlags.None;
            readerSettings.ValidationType = ValidationType.None;
            using (XmlReader reader = XmlReader.Create(gsddocstream, readerSettings))
            {
                Gsd = XDocument.Load(reader, LoadOptions.PreserveWhitespace | LoadOptions.SetLineInfo);
            }

        }
        catch (IOException)
        {
            Gsd = null;
            // "Problem during validating GSDML file or creating XML document object."
            string msg = Help.GetMessageString("M_0x0001003E_4");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x0001003E_4");
            return false;
        }
        catch (XmlException)
        {
            Gsd = null;
            // "Problem during validating GSDML file or creating XML document object."
            string msg = Help.GetMessageString("M_0x0001003E_4");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x0001003E_4");
            return false;
        }

        // Create the NamespaceManager and add all XML Namespaces to it.
        XmlNamespaceManager nsmgr = new XmlNamespaceManager(new NameTable());
        foreach (string s in Namespaces.Keys)
            nsmgr.AddNamespace(s, Namespaces[s]);
        Nsmgr = nsmgr;
        NamespaceGsdDef = Nsmgr.LookupNamespace("gsddef");
        var nl = (IEnumerable)Gsd.XPathEvaluate(".//gsddef:ISO15745Profile/gsddef:ProfileBody", Nsmgr);
        GsdProfileBody = nl.Cast<XElement>().First();

        SetFileNameGsdmlVersion();

        // Without a correct GSDML version from filename the checks make no sense
        if (String.IsNullOrEmpty(m_FileNameGsdmlVersion))
        {
            // "Neither from GSD file name nor from 'SchemaLocation' a correct GSDML version could be extracted."
            string msg = Help.GetMessageString("M_0x0001003E_3");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x0001003E_3");
            return false;
        }

        // Check for unicode chars of class decimal digit not being 0-9
        succeeded = CheckForUnexpectedDecimalDigits();
        if (!succeeded)
            return false; // ---------->

        // Process all registered checks.
        succeeded = ProcessRegisteredChecks();

        return succeeded;
    }

    /// <summary>
    /// Gets the name of the matching schema file (correct GSDML version)
    /// </summary>
    /// <returns>Name of the schema file</returns>
    protected override string GetSchemaFileName()
    {
        return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileName;
    }

    /// <summary>
    /// Gets the name of the matching primitives schema file (correct GSDML version)
    /// </summary>
    /// <returns>Name of the schema file</returns>
    protected override string GetPrimitivesFileName()
    {
        return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesName;
    }

    /// <summary>
    /// Gets the name of the matching xml schema file (correct GSDML version)
    /// </summary>
    /// <returns>Name of the schema file</returns>
    protected override string GetXmlSchemaFileName()
    {
        return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaXmlSchemaName;
    }

    /// <summary>
    /// Gets the name of the matching signatur schema file (correct GSDML version)
    /// </summary>
    /// <returns>Name of the schema file</returns>
    protected override string GetSignatureSchemaFileName()
    {
        return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaXmlSignatureName;
    }

    #endregion


    //########################################################################################

    #region All Checks

    #region Category : Validation

    protected virtual void DefineElementDescriptions()
    {
        // Syntax:
        // ',' separates parts of the description
        // if a part succeeds, the following parts are skipped unless there is a '+' after the ','

        // Check, if already initialized
        if (ElementDescriptions.Count != 0)
            ElementDescriptions.Clear();
        ElementDescriptions.Add("DeviceAccessPointItem", "ID");
        ElementDescriptions.Add("ModuleItem", "ID");
        ElementDescriptions.Add("VirtualSubmoduleItem", "ID");
        ElementDescriptions.Add("GraphicItem", "ID");
        ElementDescriptions.Add("CategoryItem", "ID");
        ElementDescriptions.Add("ValueItem", "ID");

        ElementDescriptions.Add("ChannelDiagItem", "ErrorType");

        ElementDescriptions.Add("UnitDiagTypeItem", "UserStructureIdentifier");

        ElementDescriptions.Add("ModuleItemRef", "ModuleItemTarget");
        ElementDescriptions.Add("GraphicItemRef", "GraphicItemTarget");

        ElementDescriptions.Add("DataItem", "TextId");
        ElementDescriptions.Add("ParameterRecordDataItem", "Index");

        ElementDescriptions.Add("Language", "xml:lang");

        ElementDescriptions.Add("Const", "ByteOffset");
        ElementDescriptions.Add("Ref", "ByteOffset,+BitOffset");
        ElementDescriptions.Add("Text", "TextId");

        ElementDescriptions.Add("Assign", "Content");
    }

    protected virtual void DefineAttributeTokenDescriptions()
    {
        // Is empty at the moment, because in GSDML V1.0 not token lists existing
    }

    protected virtual void DefineAttributeValueListDescriptions()
    {
        // Is empty at the moment, because in GSDML V1.0 not value lists existing
    }

    protected virtual bool CheckForExternalTextFile(Stream stream)
    {
        bool succeeded = true;
        string filename = CheckParameter["GsdName"];
        XmlReaderSettings vrSettings = new XmlReaderSettings();

        try
        {
            var context = new XmlParserContext(null, null, null, XmlSpace.None);
            StreamReader reader = new StreamReader(stream);
            string encodingLine = reader.ReadLine();
            stream.Position = 0;
            reader.DiscardBufferedData();

            if (encodingLine != null
                && encodingLine.Contains("windows-1252"))
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                context.Encoding = Encoding.GetEncoding(1252);
            }

            XmlReader vr = null;
            using (vr = XmlReader.Create(stream, vrSettings, context))
            {
                // Read the beginning of document to weed out external text file.
                while (vr.Read())
                {
                    if (vr.NodeType != XmlNodeType.Element)
                    {
                        continue;
                    }

                    // Weed out external text file which has top level element ExternalTextDocument
                    if ((vr.LocalName != "ISO15745Profile"))
                    {
                        // "{0}: File is not a GSD file."
                        string msg = string.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x0001003E_2"),
                            filename);
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            0,
                            0,
                            msg,
                            String.Empty,
                            ReportCategories.General,
                            "0x0001003E_2");
                        vr.Close();
                        succeeded = false;

                    }
                    else
                    {
                        vr.Close();
                    }

                }
            }
        }
        catch (XmlSchemaException e)
        {
            string msg = e.Message;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                "",
                ReportCategories.General,
                "0x0001003E");
            succeeded = false;
        }
        catch (XmlException e)
        {
            string msg = e.Message;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                "",
                ReportCategories.General,
                "0x0001003E");
            succeeded = false;
        }

        return succeeded;

    }

    protected virtual bool Validation(Stream stream)
    {
        bool succeeded = true;
        string path = @"./xsd";
        string schema = null;


        // Get GSDML version of GSD.
        // NOTE: Example Gsd name = 'GSDML-V1.0-Siemens-PNIO_Example-20030708.xml'
        string filename = (string)CheckParameter["GsdName"];
        string stringTemp = filename
            .Substring(filename.IndexOf(Constants.s_Hyphen, StringComparison.InvariantCulture) + 1).ToUpperInvariant()
            .ToLower(CultureInfo.CurrentCulture);
        int endOfVersion = stringTemp.IndexOf(Constants.s_Hyphen, StringComparison.InvariantCulture);
        if (endOfVersion
            == -1) // GSD name is very short ('GSDML-V1.0.xml') or misshapen ('GSDML-V1.0SiemensPNIO_Example20030708.xml')
            endOfVersion = stringTemp.IndexOf(".xml", StringComparison.InvariantCulture);
        string gsdmlversion = stringTemp.Substring(0, endOfVersion);

        DefineElementDescriptions();
        DefineAttributeTokenDescriptions();
        DefineAttributeValueListDescriptions();

        if (Help.IsHigherVersion(gsdmlversion, SupportedGsdmlVersion))
            return true;

        try
        {
            bool bValidate = true;
            schema = GetSchemaForValidation(
                ref path,
                out string primitivesSchema,
                out string xmlSchema,
                out string signatureSchema,
                ref bValidate);


            // Test whether validation should be done, or not.
            if (!bValidate)
                return true; // ------------>

            // Validate given Gsd file.
            Context = new Stack();

            XmlReader vr = null;
            XmlReaderSettings vrSettings = new XmlReaderSettings();
            vrSettings.ValidationType = ValidationType.Schema;

            vrSettings.Schemas.Add((string)Namespaces["gsddef"], path + Constants.s_Slash + schema);
            vrSettings.Schemas.Add((string)Namespaces["gsdbase"], path + Constants.s_Slash + primitivesSchema);
            vrSettings.Schemas.Add((string)Namespaces["gsdxml"], path + Constants.s_Slash + xmlSchema);
            vrSettings.Schemas.Add((string)Namespaces["gsdsig"], path + Constants.s_Slash + signatureSchema);
            vrSettings.ValidationEventHandler += ValidationEventHandler;

            using (vr = XmlReader.Create(stream, vrSettings))
            {
                ReadDocumentForValidation(vr, filename);
            }
        }
        catch (XmlSchemaException e)
        {
            string msg = e.Message;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                "",
                ReportCategories.General,
                "0x0001003E");
            succeeded = false;
        }
        catch (XmlException e)
        {
            string msg = e.Message;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                "",
                ReportCategories.General,
                "0x0001003E");
            succeeded = false;
        }
        catch (FileNotFoundException)
        {
            // "Could not find Schema: "{0}\{1}"."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x0001003E_1"),
                path,
                schema);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                "",
                ReportCategories.General,
                "0x0001003E_1");
            succeeded = false;
        }

        return succeeded;
    }

    private void ReadDocumentForValidation(XmlReader vr, string filename)
    {
        // Read the document for validation.
        while (vr.Read())
        {
            ReadElementForValidation(vr, filename);
        }
    }

    private void ReadElementForValidation(XmlReader vr, string filename)
    {
        if (vr.NodeType == XmlNodeType.Element)
        {
            ValidationContext c = new ValidationContext(vr.LocalName);
            Context.Push(c);
            AddAttributesForValidation(vr, c);


            // If error occured during reading, announce it here!
            if (null != ValidationEventArgs)
            {
                string xpath = GetValidationXPath();
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    ValidationEventArgs.Exception.LineNumber,
                    ValidationEventArgs.Exception.LinePosition,
                    ValidationEventArgs.Message,
                    xpath,
                    ReportCategories.Validation,
                    "SchemaValidation");
                ValidationEventArgs = null;
            }

            // If empty element, remove from Stack here!!!
            if (vr.IsEmptyElement)
                if (Context.Count != 0)
                    Context.Pop();
        }

        // XmlNodeType.Attribute - Not available here!!!

        if (vr.NodeType == XmlNodeType.EndElement)
        {
            GSDI.ReportTypes reportType = GSDI.ReportTypes.GSD_RT_Error;

            // Change ReportType to warning in this case.
            if (filename.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture).Contains("#siemens"))
            {
                reportType = GSDI.ReportTypes.GSD_RT_Warning;
            }

            // If error occured during reading, announce it here!
            if (null != ValidationEventArgs)
            {
                string xpath = GetValidationXPath();
                Store.CreateAndAnnounceReport(
                    reportType,
                    ValidationEventArgs.Exception.LineNumber,
                    ValidationEventArgs.Exception.LinePosition,
                    ValidationEventArgs.Message,
                    xpath,
                    ReportCategories.Validation,
                    "SchemaValidation");
                ValidationEventArgs = null;
            }


            // Remove from Stack!!!
            if (Context.Count != 0)
                Context.Pop();
        }
    }

    private static void AddAttributesForValidation(XmlReader vr, ValidationContext c)
    {
        string element = vr.LocalName;
        if (ElementDescriptions.ContainsKey(element))
        {
            string[] descriptions = ElementDescriptions[element].Split(",".ToCharArray());
            foreach (string description in descriptions)
            {
                string attributeName = description;

                string attributeValue = vr.GetAttribute(attributeName);

                if (attributeValue == null)
                    continue;

                c.AddAttribute(attributeName, attributeValue);
                break;
            }
        }
    }


    private string GetSchemaForValidation(
        ref string path,
        out string primitivesSchema,
        out string xmlSchema,
        out string signatureSchema,
        ref bool bValidate)
    {
        string schema = string.Empty;
        primitivesSchema = string.Empty;
        xmlSchema = string.Empty;
        signatureSchema = string.Empty;
        if (m_UseExternalSchemaPath)
        {
            path = m_SchemaPath;
            schema = GetSchemaFileName();
            primitivesSchema = GetPrimitivesFileName();
            xmlSchema = GetXmlSchemaFileName();
            signatureSchema = GetSignatureSchemaFileName();
        }

        //else
        //{
        //    schema = GetSchemaFileName();
        //    primitivesSchema = GetPrimitivesFileName();
        //    xmlSchema = GetXmlSchemaFileName();
        //    signatureSchema = GetSignatureSchemaFileName();
        //}
        return schema;
    }

    protected virtual void ValidationEventHandler(object sender, ValidationEventArgs vea)
    {
        // NOTE: "sender" is undefined object, therefore no information could be getted, which
        // object causes the event!

        // NOTE: Because there are some warnings by a valid GSD, if embedded graphic is defined,
        // it is better to disable report creation for warnings!!!

        // Create report and announce it to store.
        if (vea.Severity == XmlSeverityType.Error)
        {
            // Remember validation event and announce report later, if the reader reads the
            // appropriate element!
            ValidationEventArgs = vea;
        }
    }

    #endregion

    #region Category : PlugRules

    protected virtual bool CheckPlugRuleAccessPoint(XElement element)
    {
        // NOTE: 'element' must be 'DeviceAccessPoint' node!
        bool succeeded = true;

        XAttribute physicalSlots = element.Attribute("PhysicalSlots");
        List<ValueListHelper.ValueRangeT> physicalSlotList = ValueListHelper.NormalizeValueList(physicalSlots, Store);

        XAttribute attrAllowed = element.Attribute("AllowedInSlots");
        List<ValueListHelper.ValueRangeT> allowedSlotList = ValueListHelper.NormalizeValueList(attrAllowed, Store);

        XAttribute attrFixed = element.Attribute("FixedInSlots");
        List<ValueListHelper.ValueRangeT> fixedSlotList = ValueListHelper.NormalizeValueList(attrFixed, Store);

        // NOTE: (1) Without system redundancy, 'AllowedInSlots' should not be used.
        // (2) For a non redundant DAP only one slot number is allowed in the list of 'FixedInSlots'.
        if (!DapRedundancySupported(element))
        {
            if (null != attrAllowed)
            {
                // "Without system redundancy or with 'DeviceType'="S2", 'AllowedInSlots' should not be used."
                string msg = Help.GetMessageString("M_0x00010016_5");
                string xpath = Help.GetXPath(attrAllowed);
                var xli = (IXmlLineInfo)attrAllowed;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_5");
            }

            if (fixedSlotList.Count > 1)
            {
                // "For a non redundant DAP only one slot number is allowed in the list of 'FixedInSlots'."
                string msg = Help.GetMessageString("M_0x00010016_6");
                string xpath = Help.GetXPath(attrFixed);
                var xli = (IXmlLineInfo)attrFixed;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportType_0X000100166,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.PlugRules,
                        "0x00010016_6");
                }
            }
        }

        // NOTE: (3) All values from 'AllowedInSlots' must be available as 'PhysicalSlots'.
        // Further on the values from (4) 'FixedInSlots' must be also available from 'AllowedInSlots'.
        // (5) Check plug rules for all there contained 'ModuleItemRef' elements and check for duplicate
        // reference entries (means, which points to the same target).
        // (6) Check whether 'FixedInSlots' entries are overlapped.

        // (3)
        CreateReport0x00010016_1(
            allowedSlotList,
            physicalSlotList,
            attrAllowed,
            physicalSlots,
            fixedSlotList,
            attrFixed);


        // ----------------------------------------------
        // Check plug rules from Module references, available from current '...AccessPointItem'.
        string xp = "./gsddef:UseableModules/gsddef:ModuleItemRef";
        var nl = element.XPathSelectElements(xp, Nsmgr);

        var references = new List<string>();
        foreach (var em in nl)
        {
            // (5)
            succeeded = CheckPlugRuleAccessPointModuleRef(physicalSlotList, em);
            if (!succeeded)
                throw new CheckerException("Can't check ModuleItemRef - Plug data consistency!");

            // Check for duplicate references.
            var attr = em.Attribute("ModuleItemTarget");
            if (attr != null
                && references.Contains(attr.Value))
            {
                // "Duplicate 'ModuleItemRef' elements found (it means, they have the same target)."
                string msg = Help.GetMessageString("M_0x00010016_2");
                string xpath = Help.GetXPath(attr);
                var xli = (IXmlLineInfo)attr;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_2");
            }
            else if (attr != null)
                references.Add(attr.Value);
        }

        // ----------------------------------------------
        // Check slot overlap from default modules of the module references.
        // (6)
        succeeded = CheckPlugRuleAccessPointSlotOverlap(element);
        if (!succeeded)
            throw new CheckerException("Can't check slot overlap from default modules - Plug data consistency!");

        return succeeded;
    }

    private void CreateReport0x00010016_1(
        IList<ValueListHelper.ValueRangeT> allowedSlotList,
        IList<ValueListHelper.ValueRangeT> physicalSlotList,
        XAttribute attrAllowed,
        XAttribute physicalSlots,
        IList<ValueListHelper.ValueRangeT> fixedSlotList,
        XAttribute attrFixed)
    {
        if (0 != allowedSlotList.Count
            && 0 != physicalSlotList.Count)
        {
            if (!CompareSlotLists(allowedSlotList, physicalSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrAllowed.Name.LocalName,
                    physicalSlots.Name.LocalName);
                string xpath = Help.GetXPath(attrAllowed);
                var xli = (IXmlLineInfo)attrAllowed;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }

        //
        // (4)
        if (0 != fixedSlotList.Count
            && 0 != physicalSlotList.Count)
        {
            if (!CompareSlotLists(fixedSlotList, physicalSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrFixed.Name.LocalName,
                    physicalSlots.Name.LocalName);
                string xpath = Help.GetXPath(attrFixed);
                var xli = (IXmlLineInfo)attrFixed;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }

        if (0 != fixedSlotList.Count
            && 0 != allowedSlotList.Count)
        {
            if (!CompareSlotLists(fixedSlotList, allowedSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrFixed.Name.LocalName,
                    attrAllowed.Name.LocalName);
                string xpath = Help.GetXPath(attrFixed);
                var xli = (IXmlLineInfo)attrFixed;
                Store.CreateAndAnnounceReport(
                    ReportType_0X000100161,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }
    }

    protected virtual bool CheckPlugRuleAccessPointModuleRef(
        IList<ValueListHelper.ValueRangeT> physicalSlotList,
        XElement element)
    {
        // NOTE: 'element' must be 'ModuleItemRef' node!

        // NOTE: (1) All values from 'AllowedInSlots' must be available as 'PhysicalSlots'. If 
        // 'AllowedInSlots' lacks, the values from 'PhysicalSlots' are used.
        // Further on the values from (2) 'UsedInSlots' and (3) 'FixedInSlots' must be also available
        // from 'AllowedInSlots'. (4) Slots defined in 'FixedInSlots' must not be available in 'UsedInSlots'.

        var attrAllowed = element.Attribute("AllowedInSlots");
        List<ValueListHelper.ValueRangeT> allowedSlots = ValueListHelper.NormalizeValueList(attrAllowed, Store);

        var attrUsed = element.Attribute("UsedInSlots");
        List<ValueListHelper.ValueRangeT> usedSlots = ValueListHelper.NormalizeValueList(attrUsed, Store);

        var attrFixed = element.Attribute("FixedInSlots");
        List<ValueListHelper.ValueRangeT> fixedSlots = ValueListHelper.NormalizeValueList(attrFixed, Store);

        // (1)
        CheckAllowedInSlotsAvailableInPhysicalSlots(physicalSlotList, allowedSlots, attrAllowed);


        //
        // (2)
        CheckUsedInSlotsAvailableInPhysicalSlots(physicalSlotList, attrUsed, usedSlots, allowedSlots, attrAllowed);


        //
        // (3)
        IXmlLineInfo xliFixed = CheckFixedInSlotsAvailableInPhysicalSlots(
            physicalSlotList,
            attrFixed,
            fixedSlots,
            allowedSlots,
            attrAllowed);

        //
        // (4)
        if (0 == fixedSlots.Count
            || 0 == usedSlots.Count)
        {
            return true;
        }

        if (SlotListsDisjunct(fixedSlots, usedSlots))
        {
            return true;
        }

        // "Slots defined in '{0}' must not be available in '{1}'."
        if (attrFixed == null)
        {
            return true;
        }

        if (attrUsed == null)
        {
            return true;
        }

        // "Slots defined in '{0}' must not be available in '{1}'."
        string msg = String.Format(
            CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x00010016_4"),
            attrFixed.Name.LocalName,
            attrUsed.Name.LocalName);
        string xpath = Help.GetXPath(attrFixed);
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            xliFixed.LineNumber,
            xliFixed.LinePosition,
            msg,
            xpath,
            ReportCategories.PlugRules,
            "0x00010016_4");


        return true;
    }

    private IXmlLineInfo CheckFixedInSlotsAvailableInPhysicalSlots(
        IList<ValueListHelper.ValueRangeT> physicalSlotList,
        XAttribute attrFixed,
        IList<ValueListHelper.ValueRangeT> fixedSlots,
        IList<ValueListHelper.ValueRangeT> allowedSlots,
        XAttribute attrAllowed)
    {
        IXmlLineInfo xliFixed = attrFixed;
        if (0 != fixedSlots.Count
            && 0 != physicalSlotList.Count)
        {
            if (!CompareSlotLists(fixedSlots, physicalSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrFixed.Name.LocalName,
                    "PhysicalSlots");
                string xpath = Help.GetXPath(attrFixed);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xliFixed.LineNumber,
                    xliFixed.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }

        if (0 != fixedSlots.Count
            && 0 != allowedSlots.Count)
        {
            if (!CompareSlotLists(fixedSlots, allowedSlots))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrFixed.Name.LocalName,
                    attrAllowed.Name.LocalName);
                string xpath = Help.GetXPath(attrFixed);
                Store.CreateAndAnnounceReport(
                    ReportType_0X000100161,
                    xliFixed.LineNumber,
                    xliFixed.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }

        return xliFixed;
    }

    private void CheckUsedInSlotsAvailableInPhysicalSlots(
        IList<ValueListHelper.ValueRangeT> physicalSlotList,
        XAttribute attrUsed,
        IList<ValueListHelper.ValueRangeT> usedSlots,
        IList<ValueListHelper.ValueRangeT> allowedSlots,
        XAttribute attrAllowed)
    {
        IXmlLineInfo xliUsed = attrUsed;
        if (0 != usedSlots.Count
            && 0 != physicalSlotList.Count)
        {
            if (!CompareSlotLists(usedSlots, physicalSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrUsed.Name.LocalName,
                    "PhysicalSlots");
                string xpath = Help.GetXPath(attrUsed);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xliUsed.LineNumber,
                    xliUsed.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }

        if (0 != usedSlots.Count
            && 0 != allowedSlots.Count)
        {
            if (!CompareSlotLists(usedSlots, allowedSlots))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrUsed.Name.LocalName,
                    attrAllowed.Name.LocalName);
                string xpath = Help.GetXPath(attrUsed);
                Store.CreateAndAnnounceReport(
                    ReportType_0X000100161,
                    xliUsed.LineNumber,
                    xliUsed.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }
    }

    private void CheckAllowedInSlotsAvailableInPhysicalSlots(
        IList<ValueListHelper.ValueRangeT> physicalSlotList,
        IList<ValueListHelper.ValueRangeT> allowedSlots,
        XAttribute attrAllowed)
    {
        if (0 != allowedSlots.Count
            && 0 != physicalSlotList.Count)
        {
            if (!CompareSlotLists(allowedSlots, physicalSlotList))
            {
                // "Slots defined in '{0}' are not available in '{1}'."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_1"),
                    attrAllowed.Name.LocalName,
                    "PhysicalSlots");
                string xpath = Help.GetXPath(attrAllowed);
                IXmlLineInfo xli = attrAllowed;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_1");
            }
        }
    }
    protected virtual bool CheckPlugRuleAccessPointSlotOverlap(XElement element)
    {
        // NOTE: 'element' must be 'DeviceAccessPoint' node!

        // Collect all slots from AccessPoint - FixedInSlots (1) and all ModuleItemRef -
        // FixedInSlots (2). No overlap may exist between these slots.

        // FixedInSlots from AccessPoint.
        // (1)
        element.Attribute("FixedInSlots");
        List<ValueListHelper.ValueRangeT> reservedslots;
        string reservedslotsStr = Help.GetAttributeValueFromXElement(element, Attributes.s_FixedInSlots); // must

        // FixedInSlots from ModuleItemRef.
        string xp =
            "./gsddef:UseableModules/gsddef:ModuleItemRef/@FixedInSlots | ./gsddef:UseableModules/gsddef:ModuleItemRef/@UsedInSlots";

        var nl = (IEnumerable)element.XPathEvaluate(xp, Nsmgr);
        foreach (XAttribute an in nl)
        {
            string attrStr = an.Value;
            if (!string.IsNullOrEmpty(attrStr))
                reservedslotsStr = reservedslotsStr + " " + attrStr;

            ValueListHelper.NormalizeResult res = ValueListHelper.NormalizeValueList(
                reservedslotsStr,
                out reservedslots);

            // Other errors are raised with call of ValueListHelper.NormalizeValueList(an, Store)
            if (res == ValueListHelper.NormalizeResult.Overlap)
            {
                // "At least one slot of the list "{0}" is reserved from another module."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010016_3"),
                    attrStr);
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010016_3");
            }
        }

        return true;
    }

    /// <summary>
    /// Compares two Lists (containing int values).
    /// </summary>
    /// <param name="slots1"></param>
    /// <param name="slots2"></param>
    /// <returns>true if the values from slots1 are contained in slots2.</returns>
    private bool CompareSlotLists(IList<ValueListHelper.ValueRangeT> slots1, IList<ValueListHelper.ValueRangeT> slots2)
    {
        if (slots1 != null
            && slots2 != null)
        {
            for (int currentRange = 0; currentRange < slots1.Count; currentRange++)
            {
                uint currentValue = slots1[currentRange].From;
                while (currentValue <= slots1[currentRange].To)
                {
                    if (!ValueListHelper.IsValueInValueList(currentValue++, slots2))
                        return false;
                }
            }
        }

        return true;
    }

    /// <summary>
    /// Compares two Lists (containing int values).
    /// </summary>
    /// <param name="slots1"></param>
    /// <param name="slots2"></param>
    /// <returns>false if at least one value from slots1 is contained in slots2.</returns>
    private bool SlotListsDisjunct(IList<ValueListHelper.ValueRangeT> slots1, IList<ValueListHelper.ValueRangeT> slots2)
    {
        if (slots1 != null
            && slots2 != null)
        {
            for (int currentRange = 0; currentRange < slots1.Count; currentRange++)
            {
                uint currentValue = slots1[currentRange].From;
                while (currentValue <= slots1[currentRange].To)
                {
                    if (ValueListHelper.IsValueInValueList(currentValue++, slots2))
                        return false;
                }
            }
        }

        return true;
    }

    #endregion

    /// <summary>
    /// Checks if the DAP supports system redundancy.
    /// </summary>
    /// <param name="dap"></param>
    /// <returns>always false, because system redundancy is supported from GSDML V2.3 on.</returns>
    protected virtual bool DapRedundancySupported(XElement dap)
    {
        return false;
    }

    /// <summary>
    /// Find the error msg for a length - data type, which requires length combination.
    /// </summary>
    /// <param name="en"></param>
    /// <returns>The error message or empty string.</returns>
    protected virtual void FindMsgForDataTypesRequiresLength(XElement en)
    {
        string dataType = String.Empty;
        string strLength = String.Empty;

        var lineInfo = (IXmlLineInfo)en;

        XAttribute dataTypeAtt = en.Attribute(Attributes.s_DataType);
        if (dataTypeAtt != null)
        {
            dataType = dataTypeAtt.Value;
        }

        XAttribute lengthAtt = en.Attribute(Attributes.s_Length);
        if (lengthAtt != null)
        {
            strLength = lengthAtt.Value;
        }

        if (string.IsNullOrEmpty(strLength))
        {
            // "The attribute '@Length' must be used, if the data type is "{0}"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001000C_1"), dataType);
            string xpath = Help.GetXPath(en);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001000C_1");
            return;
        }

        UInt16 length = XmlConvert.ToUInt16(strLength);

        switch (dataType)
        {
            case Enums.s_VisibleString:
            case Enums.s_OctetString:
                {
                    if (length == 0)
                    {
                        // "The attribute '@Length' must be unequal to 0, if the data type is "{0}"."
                        string msg = String.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x0001000C_2"),
                            dataType);
                        string xpath = Help.GetXPath(en);
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            lineInfo.LineNumber,
                            lineInfo.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x0001000C_2");
                    }
                }
                break;
        }
    }

    /// <summary>
    /// Find the error msg for a length - data type, which not requires length combination.
    /// </summary>
    /// <param name="en"></param>
    /// <returns>The error message or empty string.</returns>
    protected virtual void FindMsgForDataTypesNotRequiresLength(XElement en)
    {
        var attLength = en.Attribute(Attributes.s_Length);
        if (attLength != null)
        {
            string sLength = attLength.Value;
            if (String.IsNullOrEmpty(sLength))
                return;
            UInt16 length = XmlConvert.ToUInt16(sLength);

            var attDataType = en.Attribute(Attributes.s_DataType);
            if (attDataType != null)
            {
                string dataType = attDataType.Value;
                UInt16 dataTypeLength = (UInt16)(Help.GetBitLengthFromDataItemType(dataType) / 8);
                if (dataTypeLength != length)
                {
                    // "The attribute 'DataItem/@Length(= {0})' must match to 'DataItem/@DataType' (length= {1})."
                    string msg = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x0001000C_3"),
                        length,
                        dataTypeLength);
                    string xpath = Help.GetXPath(en);
                    IXmlLineInfo xli = en;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x0001000C_3");
                }
            }
        }
    }

    /// <summary>
    /// Check number: CN_0x00010002
    /// Check GSD file name, which must conform to a strict naming convention.
    /// 
    /// Example: gsdml-v1.0-siemens-et200s-20041022.xml
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010002()
    {
        // Eventually the name is not given for checking.
        string fileName = String.Empty;

        if (CheckParameter.ContainsKey("GsdName")
            && CheckParameter["GsdName"] != null)
            fileName = CheckParameter["GsdName"] as string;

        if (fileName.Length <= 0)
        {
            return true;
        }

        // The name of a GSDML based file shall be composed of the six fields below in the following order:
        // 1. "GSDML"
        // 2. The version ID in format Vx.y whereby "x" and "y" are unsigned numbers. The version ID refers to
        // the ID of the GSDML Schema used.
        // 3. Vendor name (may contain "-")
        // 4. Device family name (may contain "-")
        // 5. Release date of the GSDML based file in format yyyymmdd
        // 6. Release time of the GSDML based file in format hhmmss (Optional).
        // 7. ".xml" (file extension)
        // As a delimiter between the fields the dash character "-" (ASCII 45 decimal) shall be used.
        // GSDML-V2.1-Lieferant-ET200X-20030818-201213.xml
        //
        // The name of the external file shall be built by the name of the corresponding GSDML
        // based file name appending the string "-Text-" and the ISO 639-1 compliant two letter code.
        // GSDML-V2.1-Lieferant-ET200X-20030818-Text-fr.xml"
        fileName = CreateReport0x00010002_1(fileName);


        string[] parts = fileName.Split("-".ToCharArray());
        int numberOfParts = parts.Length;

        // Empty fields do not count to the number of parts
        for (int i = 0; i < parts.Length; i++)
        {
            if (string.IsNullOrEmpty(parts[i]))
                numberOfParts--;
        }

        // Check, if the last field is given in release time format
        Regex rxTime = new Regex(@"^[0-9]{6}$");
        String sTime = parts[parts.Length - 1];
        Match mTime = rxTime.Match(sTime);

        // If optional release time of GSDML is available, it does not count to the needed fields of GSDML name
        if (mTime.Success)
            numberOfParts--;

        if (CreateReport0x00010002_2(numberOfParts))
        {
            return true;
        }

        // field 1: "GSDML"
        if (!parts[0].Equals("GSDML", StringComparison.CurrentCultureIgnoreCase))
        {
            CreateReport0x00010002_3();

        }
        else
        {
            CreateReport0x00010002_4(parts);

        }

        // field 2: The version ID in format Vx.y whereby "x" and "y" are unsigned numbers.
        // The version ID refers to the ID of the GSDML Schema used.
        Regex rxVersion = new Regex(@"^V[0-9]+\.[0-9]+$", RegexOptions.IgnoreCase);
        Match mVersion = rxVersion.Match(parts[1]);
        if (!mVersion.Success)
        {
            CreateReport0x00010002_5();

        }
        else
        {
            CreateReport0x00010002_6(parts);

        }

        // field 3: Vendor name (may contain "-")
        // field 4: Device family name (may contain "-")
        // next fields must be parsed backwards:
        // field 6: Release time of the GSDML based file in format hhmmss (optional)
        int iCurrentPart = parts.Length - 1; // index of current filename part
        if (parts.Length > 5) // do not check for time if we only have 5 parts
        {
            if (mTime.Success)
            {
                int hour = int.Parse(sTime.Substring(0, 2), CultureInfo.InvariantCulture);
                int minute = int.Parse(sTime.Substring(2, 2), CultureInfo.InvariantCulture);
                int second = int.Parse(sTime.Substring(4, 2), CultureInfo.InvariantCulture);

                CreateReport0x00010002_7(hour);

                CreateReport0x00010002_8(minute);

                CreateReport0x00010002_9(second);


                // it looks like a time field (at least it can't be a date field),
                // so proceed to next part for the date field
                iCurrentPart--;
            }
        }

        // field 5: Release date of the GSDML based file in format yyyymmdd
        CreateReport0x00010002(parts, iCurrentPart);
        return true;

    }

    private void CreateReport0x00010002(IReadOnlyList<string> parts, int iCurrentPart)
    {
        Regex rxDate = new Regex(@"^[0-9]{8}$");
        String sDate = parts[iCurrentPart];
        Match mDate = rxDate.Match(sDate);
        if (!mDate.Success)
        {
            CreateReport0x00010002_a();

        }
        else
        {
            int year = int.Parse(sDate.Substring(0, 4), CultureInfo.InvariantCulture);
            int month = int.Parse(sDate.Substring(4, 2), CultureInfo.InvariantCulture);
            int day = int.Parse(sDate.Substring(6, 2), CultureInfo.InvariantCulture);
            CreateReport0x00010002_b(year);
            CreateReport0x00010002_c(month);


            if ((day < 1)
                || (day > 31))
            {
                CreateReport0x00010002_d();

            }
            else
            {
                CreateReport0x00010002_e(year, month, day);

            }
        }
    }

    private void CreateReport0x00010002_e(int year, int month, int day)
    {
        if ((year >= 1)
            && (month >= 1)
            && (month <= 12)
            && (day > DateTime.DaysInMonth(year, month)))
        {
            // "Release date of GSD name: day exceeds length of month."
            string msg = Help.GetMessageString("M_0x00010002_e");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_e");
        }
    }

    private void CreateReport0x00010002_d()
    {
        // "Release date of GSD name: day must be within 1-31."
        string msg = Help.GetMessageString("M_0x00010002_d");
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            0,
            0,
            msg,
            String.Empty,
            ReportCategories.General,
            "0x00010002_d");
    }

    private void CreateReport0x00010002_c(int month)
    {
        if ((month < 1)
            || (month > 12))
        {
            // "Release date of GSD name: month must be within 1-12."
            string msg = Help.GetMessageString("M_0x00010002_c");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_c");
        }
    }

    private void CreateReport0x00010002_b(int year)
    {
        if ((year < 2000)
            || (year > 2099))
        {
            // "Release date of GSD name: year should be within 2000-2099."
            string msg = Help.GetMessageString("M_0x00010002_b");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_b");
        }
    }

    private void CreateReport0x00010002_a()
    {
        // "After the vendor and device family name in GSD name, only a release date matching \"yyyymmdd\"
        // and optionally a release time matching \"hhmmss\" may follow."
        string msg = Help.GetMessageString("M_0x00010002_a");
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            0,
            0,
            msg,
            String.Empty,
            ReportCategories.General,
            "0x00010002_a");
    }

    private void CreateReport0x00010002_9(int second)
    {
        if (second > 59)
        {
            // "Sixth field of GSD name (release time): second must be within 0-59."
            string msg = Help.GetMessageString("M_0x00010002_9");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_9");
        }
    }

    private void CreateReport0x00010002_8(int minute)
    {
        if (minute > 59)
        {
            // "Sixth field of GSD name (release time): minute must be within 0-59."
            string msg = Help.GetMessageString("M_0x00010002_8");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_8");
        }
    }

    private void CreateReport0x00010002_7(int hour)
    {
        if (hour > 23)
        {
            // "Sixth field of GSD name (release time): hour must be within 0-23."
            string msg = Help.GetMessageString("M_0x00010002_7");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_7");
        }
    }

    private void CreateReport0x00010002_6(IReadOnlyList<string> parts)
    {
        if (!parts[1].StartsWith("V", StringComparison.Ordinal))
        {
            // "The "V" from the version field in GSD name must be upper case."
            string msg = Help.GetMessageString("M_0x00010002_6");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_6");
        }
    }

    private void CreateReport0x00010002_5()
    {
        // "GSD version field in GSD name must match "Vx.y" whereby "x" and "y" are unsigned numbers."
        string msg = Help.GetMessageString("M_0x00010002_5");
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            0,
            0,
            msg,
            String.Empty,
            ReportCategories.General,
            "0x00010002_5");
    }

    private void CreateReport0x00010002_4(IReadOnlyList<string> parts)
    {
        if (parts[0] != "GSDML")
        {
            // ""GSDML-" in GSD name must be upper case."
            string msg = Help.GetMessageString("M_0x00010002_4");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_4");
        }
    }

    private void CreateReport0x00010002_3()
    {
        // "GSD file name must begin with "GSDML-"."
        string msg = Help.GetMessageString("M_0x00010002_3");
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            0,
            0,
            msg,
            String.Empty,
            ReportCategories.General,
            "0x00010002_3");
    }

    private bool CreateReport0x00010002_2(int numberOfParts)
    {
        if (numberOfParts < 5)
        {
            // "Name of GSD must consist of at least 5 fields, separated by "-"."
            string msg = Help.GetMessageString("M_0x00010002_2");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_2");
            return true;
        }

        return false;
    }

    private string CreateReport0x00010002_1(string fileName)
    {
        if (-1 == fileName.IndexOf(".xml", 0, StringComparison.CurrentCultureIgnoreCase))
        {
            // "The GSD file must have the extension ".xml"."
            string msg = Help.GetMessageString("M_0x00010002_1");
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                0,
                0,
                msg,
                String.Empty,
                ReportCategories.General,
                "0x00010002_1");
        }
        else
        {
            // Extension is not needed any more for further checks of GSD name.
            int index = fileName.LastIndexOf(".", StringComparison.InvariantCulture);
            fileName = fileName.Remove(index);
        }

        return fileName;
    }

    /// <summary>
    /// Check number: CN_0x00010003
    /// The 'DeviceAccessPointItem/@ID' must be unique over all 'DeviceAccessPointItem' elements.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010003()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem)
            .Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        IList<string> keys = new List<string>();
        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'DeviceAccessPointItem/@ID' must be unique over all 'DeviceAccessPointItem' elements."
                string msg = Help.GetMessageString("M_0x00010003_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010003_1");
            }
            else
                keys.Add(an.Value);
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010004
    /// The 'ModuleItem/@ID' must be unique over all 'ModuleItem' elements.
    /// The 'ModuleItemRef/@ModuleItemTarget' attribute must reference an existing module 'ID'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010004()
    {
        // Keys -----
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem).Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        IList<string> keys = new List<string>();
        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'ModuleItem/@ID' must be unique over all 'ModuleItem' elements."
                string msg = Help.GetMessageString("M_0x00010004_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010004_1");
            }
            else
                keys.Add(an.Value);
        }

        // KeyRefs -----
        nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItemRef)
            .Attributes(Attributes.s_ModuleItemTarget);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        foreach (XAttribute an in nl)
        {
            if (!keys.Contains(an.Value))
            {
                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                {
                    // "The 'ModuleItemRef/@ModuleItemTarget' attribute must reference an existing module 'ID'."
                    string msg = Help.GetMessageString("M_0x00010004_2");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.KeyKeyref,
                        "0x00010004_2");
                }
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010005
    /// The 'ValueItem/@ID' must be unique over all 'ValueItem' elements.
    /// The 'Ref/@ValueItemTarget' attribute must reference an existing ValueItem 'ID'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010005()
    {
        // Keys -----
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ValueItem).Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        IList<string> keys = new List<string>();

        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'ValueItem/@ID' must be unique over all 'ValueItem' elements."
                string msg = Help.GetMessageString("M_0x00010005_1");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010005_1");
            }
            else
                keys.Add(an.Value);
        }

        // KeyRefs -----
        nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Ref).Attributes(Attributes.s_ValueItemTarget);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        foreach (XAttribute an in nl)
        {
            if (!keys.Contains(an.Value))
            {
                // "The 'Ref/@ValueItemTarget' attribute must reference an existing ValueItem 'ID'."
                string msg = Help.GetMessageString("M_0x00010005_2");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010005_2");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010006
    /// The 'GraphicItem/@ID' must be unique over all 'GraphicItem' elements.
    /// The 'GraphicItemRef/@GraphicItemTarget' attribute must reference an existing GraphicItem 'ID'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010006()
    {
        // Keys -----
        IList<string> keys = new List<string>();
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_GraphicItem).Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'GraphicItem/@ID' must be unique over all 'GraphicItem' elements."
                string msg = Help.GetMessageString("M_0x00010006_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010006_1");
            }
            else
                keys.Add(an.Value);
        }

        // KeyRefs -----
        nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_GraphicItemRef)
            .Attributes(Attributes.s_GraphicItemTarget);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            if (!keys.Contains(an.Value))
            {
                // "The 'GraphicItemRef/@GraphicItemTarget' attribute must reference an existing GraphicItem 'ID'."
                string msg = Help.GetMessageString("M_0x00010006_2");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010006_2");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010007
    /// The 'CategoryItem/@ID' must be unique over all 'CategoryItem' elements.
    /// The 'ModuleInfo/@CategoryRef' attribute must reference an existing CategoryItem 'ID'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010007()
    {
        // Keys -----
        var keys = new List<string>();

        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CategoryItem).Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'CategoryItem/@ID' must be unique over all 'CategoryItem' elements."
                string msg = Help.GetMessageString("M_0x00010007_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010007_1");
            }
            else
                keys.Add(an.Value);
        }

        // KeyRefs -----
        nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleInfo).Attributes(Attributes.s_CategoryRef);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            if (!keys.Contains(an.Value))
            {
                // "The 'ModuleInfo/@CategoryRef' attribute must reference an existing CategoryItem 'ID'."
                string msg = Help.GetMessageString("M_0x00010007_2");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010007_2");
            }
        }

        // ---
        nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleInfo)
            .Attributes(Attributes.s_SubCategory1Ref);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            if (!keys.Contains(an.Value))
            {
                // "The 'ModuleInfo/@SubCategory1Ref' attribute must reference an existing CategoryItem 'ID'."
                string msg = Help.GetMessageString("M_0x00010007_3");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010007_3");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010008
    /// The 'VirtualSubmoduleItem/@ID' must be unique over all 'VirtualSubmoduleItem' elements.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010008()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem)
            .Attributes(Attributes.ID);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        IList<string> keys = new List<string>();

        foreach (XAttribute an in nl)
        {
            if (keys.Contains(an.Value))
            {
                // "The 'VirtualSubmoduleItem/@ID' must be unique over all 'VirtualSubmoduleItem' elements."
                string msg = Help.GetMessageString("M_0x00010008_1");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010008_1");
            }
            else
                keys.Add(an.Value);
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010009
    /// The 'Text/@TextId' attribute must be unique for all text IDs of the primary language. 
    /// The 'xml:lang' attribute must be unique in the 'ExternalTextList'. The language "{0}" is more than one times defined.
    /// The 'TextId' reference attribute must reference an existing primary language text. 
    /// The 'TextId' reference attribute must reference an existing text of the language "{0}".
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010009()
    {
        bool succeeded = true;

        // Keys -----
        IList<string> primarykeys = new List<string>();
        CreateReport0x00010009_1(primarykeys);


        Hashtable languages = new Hashtable();
        IList<string> keys = new List<string>();
        IList<string> doubleDefined = new List<string>();

        var nlLanguages = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Language);
        nlLanguages = Help.TryRemoveXElementsUnderXsAny(nlLanguages, Nsmgr, Gsd);

        foreach (XElement en in nlLanguages)
        {
            var lineInfo = (IXmlLineInfo)en;
            string langcode = Help.GetAttributeValueFromXElement(en, Attributes.s_XMLLang);
            if (!string.IsNullOrEmpty(langcode))
            {
                string language = Iso6391.LangCodeToString(langcode);
                if (languages.Contains(langcode)
                    && !doubleDefined.Contains(langcode))
                {
                    CreateReport0x00010009_2(doubleDefined, langcode, language, en, lineInfo);

                }
                else if (!languages.Contains(langcode))
                {
                    CreateReport0x00010009_3(langcode, en, lineInfo);

                    CreateReport0x00010009_4(language, en, lineInfo);

                    keys = CreateReport0x00010009_5(en, keys, language);



                    languages.Add(langcode, keys);
                    keys = null;
                }
            }
        }

        // KeyRefs -----
        IEnumerable<XAttribute> nl = from attribute in GsdProfileBody.Descendants().Attributes(Attributes.s_TextId)
                                     where attribute.Parent != null
                                           && attribute.Parent.Name.LocalName != Elements.s_Text
                                     select attribute;
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            var lineInfo = (IXmlLineInfo)an;

            string normalizedId = Help.CollapseWhitespace(an.Value);
            CreateReportM_0x00010009_6(primarykeys, normalizedId, an, lineInfo);

            CreateReport0x00010009_7(languages, normalizedId, an, lineInfo);

        }

        return succeeded;
    }

    private void CreateReport0x00010009_7(IDictionary languages, string normalizedId, XObject an, IXmlLineInfo lineInfo)
    {
        foreach (string langcode in languages.Keys)
        {
            if (!((IList<string>)languages[langcode]).Contains(normalizedId))
            {
                // "The 'TextId' reference attribute must reference an existing text of the language "{0}"."
                string language = Iso6391.LangCodeToString(langcode);
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010009_7"),
                    language);
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010009_7");
            }
        }
    }

    private void CreateReportM_0x00010009_6(
        ICollection<string> primarykeys,
        string normalizedId,
        XObject an,
        IXmlLineInfo lineInfo)
    {
        if (!primarykeys.Contains(normalizedId))
        {
            // "The 'TextId' reference attribute must reference an existing primary language text."
            string msg = Help.GetMessageString("M_0x00010009_6");
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00010009_6");
        }
    }

    private IList<string> CreateReport0x00010009_5(XContainer en, IList<string> keys, string language)
    {

        var nltemp = en.Elements(NamespaceGsdDef + Elements.s_Text).Attributes(Attributes.s_TextId);
        foreach (XAttribute an in nltemp)
        {
            if (keys == null)
                keys = new List<string>();

            string normalizedId = Help.CollapseWhitespace(an.Value);
            if (keys.Contains(normalizedId))
            {
                // "The 'Text/@TextId' attribute must be unique for all text IDs of the language "{0}"."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010009_5"),
                    language);
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010009_5");
            }
            else
                keys.Add(normalizedId);
        }

        return keys;
    }

    private void CreateReport0x00010009_4(string language, XElement en, IXmlLineInfo lineInfo)
    {
        if (language.Contains("??"))
        {
            // "The language code is not a valid value as defined in ISO 639-1."
            string msg = Help.GetMessageString("M_0x00010009_4");
            string xpath = Help.GetXPath(
                en.Attribute("{http://www.w3.org/XML/1998/namespace}" + Attributes.s_XMLLang.Remove(0, 4)));
            xpath = xpath.Substring(0, xpath.LastIndexOf('@') - 1);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00010009_4");
        }
    }

    private void CreateReport0x00010009_3(string langcode, XElement en, IXmlLineInfo lineInfo)
    {
        if (langcode.Equals("en", StringComparison.Ordinal))
        {
            // "The language code "en" is not valid, because English is defined to be the default value."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010009_3"));
            string xpath = Help.GetXPath(
                en.Attribute("{http://www.w3.org/XML/1998/namespace}" + Attributes.s_XMLLang.Remove(0, 4)));
            xpath = xpath.Substring(0, xpath.LastIndexOf('@') - 1);
            Store.CreateAndAnnounceReport(
                ReportType_0X000100093,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.KeyKeyref,
                "0x00010009_3");
        }
    }

    private void CreateReport0x00010009_2(
        ICollection<string> doubleDefined,
        string langcode,
        string language,
        XElement en,
        IXmlLineInfo lineInfo)
    {
        // "The 'xml:lang' attribute must be unique in the 'ExternalTextList'. The language "{0}" is more than one times defined."
        doubleDefined.Add(langcode);
        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010009_2"), language);
        string xpath = Help.GetXPath(
            en.Attribute("{http://www.w3.org/XML/1998/namespace}" + Attributes.s_XMLLang.Remove(0, 4)));
        xpath = xpath.Substring(0, xpath.LastIndexOf('@') - 1);
        Store.CreateAndAnnounceReport(
            ReportTypes.GSD_RT_Error,
            lineInfo.LineNumber,
            lineInfo.LinePosition,
            msg,
            xpath,
            ReportCategories.KeyKeyref,
            "0x00010009_2");
    }

    private void CreateReport0x00010009_1(ICollection<string> primarykeys)
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_PrimaryLanguage)
            .Elements(NamespaceGsdDef + Elements.s_Text).Attributes(Attributes.s_TextId);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            if (primarykeys.Contains(Help.CollapseWhitespace(an.Value)))
            {
                // "The 'Text/@TextId' attribute must be unique for all text IDs of the primary language."
                string msg = Help.GetMessageString("M_0x00010009_1");
                string xpath = Help.GetXPath(an);
                var xli = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.KeyKeyref,
                    "0x00010009_1");
            }
            else
                primarykeys.Add(Help.CollapseWhitespace(an.Value));
        }
    }

    /// <summary>
    /// Check number: CN_0x0001000A
    /// The maximal data length should not be greater than the sum of maximal input and output length.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000A()
    {
        var ioWithDataLen = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData)
            .Where(x => x.Attribute(Attributes.s_MaxDataLength) != null);
        ioWithDataLen = Help.TryRemoveXElementsUnderXsAny(ioWithDataLen, Nsmgr, Gsd);
        foreach (XElement io in ioWithDataLen)
        {
            UInt16 maxDataLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxDataLength));
            UInt16 maxInputLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxInputLength));
            UInt16 maxOutputLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxOutputLength));
            if (maxDataLength > (maxInputLength + maxOutputLength))
            {
                // "The maximal data length is greater than the sum of maximal input and output length."
                string msg = Help.GetMessageString("M_0x0001000A_1");
                string xpath = Help.GetXPath(io);
                var xli = (IXmlLineInfo)io;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001000A_1");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001000B
    /// The maximal data length should not be lower than the highest value of maximal input or output length.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000B()
    {
        var ioWithDataLen = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData)
            .Where(x => x.Attribute(Attributes.s_MaxDataLength) != null);
        ioWithDataLen = Help.TryRemoveXElementsUnderXsAny(ioWithDataLen, Nsmgr, Gsd);
        foreach (XElement io in ioWithDataLen)
        {
            UInt16 maxDataLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxDataLength));
            UInt16 maxInputLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxInputLength));
            UInt16 maxOutputLength =
                XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(io, Attributes.s_MaxOutputLength));
            if (maxDataLength < maxInputLength
                || maxDataLength < maxOutputLength)
            {
                // "The maximal data length is lower than the highest value of maximal input or output length."
                string msg = Help.GetMessageString("M_0x0001000B_1");
                string xpath = Help.GetXPath(io);
                var xli = (IXmlLineInfo)io;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001000B_1");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001000C
    /// If "DataItem/@DataType" has the values "VisibleString" or "OctetString",
    /// then "DataItem/@Length" should exist and its value should not be equal to 0.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000C()
    {
        string xp =
            "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IOData[./gsddef:Input or ./gsddef:Output]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // Find all DataTypes which requires a length given without '@Length' given.
            xp = "(./gsddef:Input | ./gsddef:Output)/gsddef:DataItem[" + DataTypesRequiresLength + "]";
            var nltemp1 = en.XPathSelectElements(xp, Nsmgr);

            // Check all DataTypes which requires a length against the given length.
            foreach (var entemp1 in nltemp1)
            {
                FindMsgForDataTypesRequiresLength(entemp1);
            }

            // Check, if the data type match with the data length.
            xp = "(./gsddef:Input | ./gsddef:Output)/gsddef:DataItem[not " + DataTypesRequiresLength + "]";
            var nltemp3 = en.XPathSelectElements(xp, Nsmgr);

            foreach (var entemp3 in nltemp3)
            {
                FindMsgForDataTypesNotRequiresLength(entemp3);
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001000D
    /// The length of all input 'DataItem' or output 'DataItem' elements within a
    /// 'IOData' element plus the IOPS/IOCS length must not be greater than 1440 bytes.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000D()
    {
        string xp =
            "(//gsddef:VirtualSubmoduleItem | //gsddef:SubmoduleItem)/gsddef:IOData[./gsddef:Input or ./gsddef:Output]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            var nltemp1 = en.Elements(NamespaceGsdDef + Elements.s_Input)
                .Elements(NamespaceGsdDef + Elements.s_DataItem).ToList();
            var nltemp2 = en.Elements(NamespaceGsdDef + Elements.s_Output)
                .Elements(NamespaceGsdDef + Elements.s_DataItem).ToList();

            const uint Maximum = 1440 * 8;

            uint iops = 1;
            if (nltemp1.Count > 0
                && nltemp2.Count > 0)
                iops = 2;
            uint resultInput = iops * 8;
            uint resultOutput = iops * 8;

            // Check maximum data length of all input data items
            resultInput += CheckCn_0X0001000D2(nltemp1, Maximum);

            // Check maximum data length of all output data items
            resultOutput += CheckCn_0X0001000D2(nltemp2, Maximum);

            if ((resultInput > Maximum)
                || (resultOutput > Maximum))
            {
                // "The length of all input 'DataItem' or output 'DataItem' elements within a
                // 'IOData' element plus the IOPS/IOCS length must not be greater than 1440 bytes."
                string msg = Help.GetMessageString("M_0x0001000D_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001000D_1");
            }
        }

        return true;
    }

    private uint CheckCn_0X0001000D2(IEnumerable<XElement> nodeList, uint maximum)
    {
        uint result = 0;
        foreach (var entemp in nodeList)
        {
            uint length = 0;
            XAttribute attDataType = entemp.Attribute(Attributes.s_DataType);
            if (attDataType == null)
                continue;

            string sdt = attDataType.Value;
            if (sdt == Enums.s_VisibleString
                || sdt == Enums.s_OctetString
                || sdt == Enums.s_UnicodeString8 && sdt == Enums.s_String61131 && sdt == Enums.s_Wstring61131
                && sdt == Enums.s_OctetStringS)
            {
                XAttribute attLength = entemp.Attribute(Attributes.s_Length);
                if (attLength != null)
                {
                    string slength = attLength.Value;
                    if (!String.IsNullOrEmpty(slength))
                        length = XmlConvert.ToUInt32(slength) * 8;
                }
            }
            else
            {
                length = Enums.GetDataItemTypeBitLength(sdt);
            }

            result += length; // Collect overall length!

            // If the result exceeds the maximum it makes no sense to work on.
            // With this an overflow of uint can be avoided.
            if (result > maximum)
                break;
        }

        return result;
    }

    /// <summary>
    /// Check number: CN_0x0001000E
    /// The value of the 'Assign/@Content' attribute must be unique within each 'ValueItem/Assignments' element.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000E()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ValueItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

        foreach (var en in nl)
        {
            IList<double> list = new List<double>();

            IEnumerable nltemp = en.Elements(NamespaceGsdDef + Elements.s_Assignments)
                .Elements(NamespaceGsdDef + Elements.s_Assign).Attributes(Attributes.s_Content);

            foreach (XAttribute an in nltemp)
            {
                double value = 0;
                try
                {
                    value = XmlConvert.ToDouble(an.Value);
                }
                catch (OverflowException)
                {
                    continue;
                }
                catch (FormatException)
                {
                    // It is sufficient to just catch the exception, because a wrong value is already detected in check 0x00010022_7.
                    continue;
                }

                if (list.Contains(value))
                {
                    // "The value of the 'Assign/@Content' attribute must be unique within each 'ValueItem/Assignments' element."
                    string msg = Help.GetMessageString("M_0x0001000E_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x0001000E_1");
                }
                else
                    list.Add(value);
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001000F
    /// The value of the 'Assign/@TextId' attribute must be unique within each 'ValueItem/Assignments' element.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001000F()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ValueItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

        foreach (var en in nl)
        {
            IList<string> list = new List<string>();

            var nltemp = en.Elements(NamespaceGsdDef + Elements.s_Assignments)
                .Elements(NamespaceGsdDef + Elements.s_Assign).Attributes(Attributes.s_TextId);
            foreach (XAttribute an in nltemp)
            {
                if (list.Contains(Help.CollapseWhitespace(an.Value)))
                {
                    // "The value of the 'Assign/@TextId' attribute must be unique within each 'ValueItem/Assignments' element."
                    string msg = Help.GetMessageString("M_0x0001000F_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x0001000F_1");
                }
                else
                    list.Add(Help.CollapseWhitespace(an.Value));
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010010
    /// The 'RecordDataList/*/@Index' attribute value must
    /// be unique within each 'VirtualSubmoduleItem' or 'SubmoduleItem' element.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010010()
    {
        //string xp1 = "//gsddef:RecordDataList";
        string xp2 = "./*/@Index";

        var nl1 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RecordDataList);
        nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
        foreach (var recordDataList in nl1)
        {
            List<UInt16> indices = new List<UInt16>();

            var nl2 = (IEnumerable)recordDataList.XPathEvaluate(xp2, Nsmgr);
            foreach (XAttribute indexNode in nl2)
            {
                UInt16 index = XmlConvert.ToUInt16(indexNode.Value);
                if (indices.Contains(index))
                {
                    // "The 'RecordDataList/*/@Index' attribute value must
                    // be unique within each 'VirtualSubmoduleItem' or 'SubmoduleItem' element."
                    string msg = Help.GetMessageString("M_0x00010010_1");
                    string xpath = Help.GetXPath(indexNode);
                    var xli = (IXmlLineInfo)indexNode;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010010_1");
                }
                else
                {
                    indices.Add(index);
                }
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010011
    /// If the transfer sequence is specified for the record data objects, the 'TransferSequence'
    /// attribute must be specified and the values must be unequal to 0. 
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010011()
    {
        IList<XElement> recordDataLists = new List<XElement>(); // 1)
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_RecordDataList);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        CheckCN_0x00010011_CreateRecordDataList(nl, recordDataLists);


        foreach (var recordDataList in recordDataLists)
        {
            // --------------------------------------------------------
            string xp = "./*[not(@TransferSequence) or @TransferSequence=0]"; // 2)
            var nltemp = recordDataList.XPathSelectElements(xp, Nsmgr).ToList();
            CreateReport0x00010011_1(nltemp);


            if (nltemp.Count > 0)
                continue; // -----

            // --------------------------------------------------------
            var nlTransferSequence = recordDataList.Descendants().Attributes(Attributes.s_TransferSequence).Where(
                x => x.Parent != null && (x.Parent.Name.LocalName == Elements.s_FBaseIDRecordDataItem
                                          || x.Parent.Name.LocalName == Elements.s_ParameterRecordDataItem
                                          || x.Parent.Name.LocalName == Elements.s_FParameterRecordDataItem));
            IList<XAttribute> listTransferSequence = nlTransferSequence.Cast<XAttribute>().ToList();
            IList<UInt16> list1 = new List<UInt16>();
            bool errorFound = false;
            errorFound = CreateReport0x00010011_2(listTransferSequence, list1, errorFound);


            if (errorFound)
                continue;

            // --------------------------------------------------------

            // Build number string (e.g. "1 2 3 ")
            StringBuilder sb = new StringBuilder();
            for (uint ui = 1; ui <= listTransferSequence.Count; ui++)
                sb.Append(ui + Constants.s_Space);
            string referencenumberstring = sb.ToString();

            List<UInt16> list2 = new List<UInt16>();
            foreach (var antemp in listTransferSequence)
                list2.Add(XmlConvert.ToUInt16(antemp.Value));
            list2.Sort();

            sb.Length = 0;
            for (int i = 0; i < list2.Count; i++)
                sb.Append(list2[i] + Constants.s_Space);
            string numberstring = sb.ToString();

            if (referencenumberstring != numberstring)
            {
                // "If the transfer sequence is specified for the record data objects, the 'TransferSequence'
                // attribute values must be ascending and starting with 1."
                string msg = Help.GetMessageString("M_0x00010011_3");
                string xpath = Help.GetXPath(recordDataList);
                var xli = (IXmlLineInfo)recordDataList;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010011_3");
            }
        }

        return true;
    }


    private bool CreateReport0x00010011_2(
        IList<XAttribute> listTransferSequence,
        ICollection<ushort> list1,
        bool errorFound)
    {
        foreach (XAttribute antemp in listTransferSequence)
        {
            if (list1.Contains(XmlConvert.ToUInt16(antemp.Value)))
            {
                // "If the transfer sequence is specified for the record data objects, the'TransferSequence'
                // attribute values must be unique within each 'VirtualSubmoduleItem' element."
                string msg = Help.GetMessageString("M_0x00010011_2");
                string xpath = Help.GetXPath(antemp);
                var xli = (IXmlLineInfo)antemp;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010011_2");
                errorFound = true;
            }
            else
                list1.Add(XmlConvert.ToUInt16(antemp.Value));
        }

        return errorFound;
    }

    private void CreateReport0x00010011_1(List<XElement> nltemp)
    {
        foreach (var entemp in nltemp)
        {
            // "If the transfer sequence is specified for the record data objects, the 'TransferSequence'
            // attribute must be specified and the values must be unequal to 0."
            string msg = Help.GetMessageString("M_0x00010011_1");
            string xpath = Help.GetXPath(entemp);
            var xli = (IXmlLineInfo)entemp;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010011_1");
        }
    }

    private static void CheckCN_0x00010011_CreateRecordDataList(
        IEnumerable<XElement> nl,
        ICollection<XElement> recordDataLists)
    {
        foreach (var en in nl)
        {
            var allParameterRecordDataItems = en.Descendants().Where(
                x => x.Name.LocalName == Elements.s_FBaseIDRecordDataItem
                     || x.Name.LocalName == Elements.s_ParameterRecordDataItem
                     || x.Name.LocalName == Elements.s_FParameterRecordDataItem);
            foreach (var parameterRecordDataItem in allParameterRecordDataItems)
            {
                XAttribute transferSequence = parameterRecordDataItem.Attribute(Attributes.s_TransferSequence);
                if (transferSequence == null)
                    continue;

                if (XmlConvert.ToUInt16(transferSequence.Value) != 0)
                {
                    recordDataLists.Add(en);
                    break;
                }
            }
        }
    }

    /// <summary>
    /// Check number: CN_0x00010012
    /// The 'Ref/@BitOffset' attribute can only be used in conjunction with data type "Bit" or "BitArea".
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010012()
    {
        string xp = ".//gsddef:Ref[not(@DataType='Bit' or @DataType='BitArea') and @BitOffset!=0]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                // "The 'Ref/@BitOffset' attribute can only be used in conjunction with data type "Bit" or "BitArea"."
                string msg = Help.GetMessageString("M_0x00010012_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010012_1");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010013
    /// The 'Ref/@BitLength' attribute must be in the range of 1 to 15 for the data type "BitArea". 
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010013()
    {
        string xp = ".//gsddef:Ref[@DataType='BitArea' and (@BitLength < 1 or @BitLength > 15)]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                // "The 'Ref/@BitLength' attribute must be in the range of 1 to 15 for the data type "BitArea"."
                string msg = Help.GetMessageString("M_0x00010013_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010013_1");
            }
        }

        xp = ".//gsddef:Ref[not(@DataType='BitArea') and @BitLength]";
        nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
            {
                // "The 'Ref/@BitLength' attribute can only be used in conjunction with data type "BitArea"."
                string msg = Help.GetMessageString("M_0x00010013_2");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(
                    ReportType_0X000100132,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010013_2");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010015
    /// Checks if ModuleIdentNumber=0x00000000. If so, just show a warning (RQ AP00347380).
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010015()
    {
        // NOTE: (1) The 'ModuleIdentNumber' must not be 0.

        const string Xp =
            ".//*[@ModuleIdentNumber and not(contains(translate(@ModuleIdentNumber,'123456789ABCDEFabcdef','YYYYYYYYYYYYYYYYYYYYY'),'Y'))]";
        var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "The 'ModuleIdentNumber' must not be 0x00000000."
            string msg = Help.GetMessageString("M_0x00010015_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportType_0X00010015,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.General,
                "0x00010015_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010016
    /// Check plug rules for 'DeviceAccessPointItem' elements and their attributes
    /// 'PhysicalSlots', 'AllowedInSlots', 'FixedInSlots'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010016()
    {
        bool succeeded = true;

        // Find all DeviceAccessPointItems.
        string xp = ".//gsddef:DeviceAccessPointItem";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            succeeded = CheckPlugRuleAccessPoint(en);
            if (!succeeded)
                throw new CheckerException("Can't check DeviceAccessPointItem - Plug data consistency!");
        }

        return succeeded;
    }

    /// <summary>
    /// Check number: CN_0x00010017
    /// Check The 'Type' ("{0}") is not a duplicate.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010017()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Graphics);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            IList<string> types = new List<string>();
            var nlRefs = en.Elements(NamespaceGsdDef + Elements.s_GraphicItemRef);
            foreach (var graphicItemRef in nlRefs)
            {
                XAttribute typeAtt = graphicItemRef.Attribute("Type");
                if (typeAtt == null)
                    continue;

                if (types.Contains(typeAtt.Value))
                {
                    // "The 'Type' ("{0}") is a duplicate."
                    string msg = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00010017_1"),
                        typeAtt.Value);
                    string xpath = Help.GetXPath(en);
                    IXmlLineInfo xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010017_1");
                }
                else
                {
                    types.Add(typeAtt.Value);
                }
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010018
    /// The 'ErrorType' attribute must be between 0x000F and 0x7FFF and
    /// unique for all 'ChannelDiagItem' entries.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010018()
    {
        var lerrortypes = new List<uint>();

        // NOTE: (1) The 'ErrorType' attribute must be between 0x000F and 0x7FFF and 
        // (2) unique for all 'ChannelDiagItem' entries.

        // Get all 'ErrorType' attributes (required).
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ChannelDiagItem)
            .Attributes(Attributes.s_ErrorType);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

        foreach (XAttribute an in nl)
        {
            var lineInfo = (IXmlLineInfo)an;

            uint errortype = XmlConvert.ToUInt32(an.Value);

            // (1)
            if (errortype < 0x000F
                || errortype > 0x7FFF)
            {
                // "The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010018_1"),
                    errortype,
                    0x000F,
                    0x7FFF);
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_MinorError,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010018_1");
            }

            // (2)
            if (lerrortypes.Contains(errortype))
            {
                // "The 'ErrorType' ({0}) is a duplicate."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010018_2"),
                    errortype);
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010018_2");
            }
            else
                lerrortypes.Add(errortype);
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010019
    /// The 'UserStructureIdentifier' attribute must be in the range from 0 to 32767
    /// and unique for all 'UnitDiagTypeItem' entries.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010019()
    {
        IList<uint> lusi = new List<uint>();

        // NOTE: (1) The 'UserStructureIdentifier' attribute must be in the range
        // from 0 to 32767 and (2) unique for all 'UnitDiagTypeItem' entries.

        // Get all 'UserStructureIdentifier' attributes (required).
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_UnitDiagTypeItem)
            .Attributes(Attributes.s_UserStructureIdentifier);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            uint usi = XmlConvert.ToUInt32(an.Value);
            const uint Maximum = 32767;

            var xli = (IXmlLineInfo)an;

            // (1)
            if (!(usi >= 0 && usi <= Maximum))
            {
                // "The 'UserStructureIdentifier' ({0}) is not in the range of {1} to {2}."
                string msg = String.Format(
                    CultureInfo.CurrentCulture,
                    Help.GetMessageString("M_0x00010019_1"),
                    usi,
                    0,
                    Maximum);
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010019_1");
            }

            // (2)
            if (lusi.Contains(usi))
            {
                // "The 'UserStructureIdentifier' ({0}) is a duplicate."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010019_2"), usi);
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010019_2");
            }
            else
                lusi.Add(usi);
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010020
    /// The offset combination 'ByteOffset' ({0}) plus 'BitOffset' ({1}) 
    /// mustn't be a duplicate under 'Ref' element.
    /// Combination of 'ByteOffset' and 'BitOffset' plus length of the 'Ref' element must be
    /// lower or equal to the next combination of 'ByteOffset' and 'BitOffset'.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010020()
    {
        var nl = GsdProfileBody.XPathSelectElements(".//gsddef:Ref/..", Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            SortedList sl = new SortedList();

            // Get 'Length' attribute entry (optional).

            string slength = Help.GetAttributeValueFromXElement(en, Attributes.s_Length);
            int length = -1;
            if (!String.IsNullOrEmpty(slength))
                length = XmlConvert.ToInt32(slength);

            var refNodes = en.XPathSelectElements("./gsddef:Ref", Nsmgr);
            refNodes = Help.TryRemoveXElementsUnderXsAny(refNodes, Nsmgr, Gsd);
            CreateReport0x00010020_1(refNodes, sl, en);


            // Check each entry.
            uint reflength = 0;
            reflength = CreateReport0x00010020_2(sl, reflength, en);


            // (3)
            // NOTE: The reflength of the last list entry, which is the HIGHEST entry, must be 
            // used to test overall length.
            CreateReport0x00010020_3(length, reflength, slength, en);

        }

        return true;
    }

    private void CreateReport0x00010020_3(int length, uint reflength, string slength, XElement en)
    {
        if (length == -1
            || (length * 8) >= reflength)
        {
            return;
        }


        // "The 'Length' of the data items ({0}) is lower than the 'Ref' length ({1})."
        string msg = String.Format(
            CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x00010020_3"),
            slength,
            (reflength / 8));
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_Length));
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_Length);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_MinorError,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010020_3");
        }

    }

    private uint CreateReport0x00010020_2(IDictionary sl, uint reflength, XElement en)
    {
        foreach (uint ui in sl.Keys)
        {
            // Combination of 'ByteOffset' and 'BitOffset' plus length of the 'Ref' element must be
            // lower or equal to the next combination of 'ByteOffset' and 'BitOffset'.

            // (2)
            if (reflength > ui)
            {
                // No error message for (Profile)UnitDiagTypeItem
                if (en.Name.LocalName != Elements.s_UnitDiagTypeItem
                    && en.Name.LocalName != Elements.s_ProfileUnitDiagTypeItem)
                {
                    // "The 'Ref' data entries overlap."
                    string msg = Help.GetMessageString("M_0x00010020_2");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(
                        ReportType_0X00010020,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010020_2");
                }
            }

            reflength = ui + (uint)sl[ui];
        }

        return reflength;
    }

    private void CreateReport0x00010020_1(IEnumerable<XElement> refNodes, IDictionary sl, XElement en)
    {
        foreach (var refElement in refNodes)
        {
            uint bitoffset = 0;
            uint byteoffset = 0;

            uint reflengthtemp = GetDataTypeOffset(
                refElement,
                byteoffset,
                bitoffset,
                out string sbyteoffset,
                out string sbitoffset,
                out uint offsetValue);

            if (sl.Contains(offsetValue))
            {
                // No error message for (Profile)UnitDiagTypeItem
                if (en.Name.LocalName != Elements.s_UnitDiagTypeItem
                    && en.Name.LocalName != Elements.s_ProfileUnitDiagTypeItem)
                {
                    // "The offset combination 'ByteOffset' ({0}) plus 'BitOffset' ({1}) is a duplicate."
                    string msg = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00010020_1"),
                        sbyteoffset,
                        sbitoffset);
                    string xpath = Help.GetXPath(refElement);
                    var xli = (IXmlLineInfo)refElement;
                    Store.CreateAndAnnounceReport(
                        ReportType_0X00010020,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010020_1");
                }

                if ((uint)sl[offsetValue] < reflengthtemp)
                    sl[offsetValue] = reflengthtemp;
            }
            else
            {
                sl.Add(offsetValue, reflengthtemp);
            }
        }
    }

    private static uint GetDataTypeOffset(
        XElement refElement,
        uint byteoffset,
        uint bitoffset,
        out string sbyteoffset,
        out string sbitoffset,
        out uint offsetValue)
    {
        // Get 'DataType' attribute entry (required) and find its length
        uint reflengthtemp = 1;
        string sdatatype = Help.GetAttributeValueFromXElement(refElement, Attributes.s_DataType);
        if (!(sdatatype == "Bit" || sdatatype == "BitArea"))
        {
            reflengthtemp = Help.GetBitLengthFromDataItemType(sdatatype);
        }
        else if (sdatatype == "BitArea")
        {
            // Get 'BitLength' attribute entry (optional).
            string sbitlength = Help.GetAttributeValueFromXElement(refElement, Attributes.s_BitLength);
            if (!String.IsNullOrEmpty(sbitlength))
                reflengthtemp = XmlConvert.ToUInt32(sbitlength);
        }

        if (reflengthtemp == 0)
        {
            string sreflengthtemp = Help.GetAttributeValueFromXElement(refElement, Attributes.s_Length);
            if (!String.IsNullOrEmpty(sreflengthtemp))
                reflengthtemp = XmlConvert.ToUInt32(sreflengthtemp) * 8;
        }

        sbyteoffset = Help.GetAttributeValueFromXElement(refElement, Attributes.s_ByteOffset);
        if (!String.IsNullOrEmpty(sbyteoffset))
        {
            byteoffset = XmlConvert.ToUInt32(sbyteoffset);
        }

        sbitoffset = Help.GetAttributeValueFromXElement(refElement, Attributes.s_BitOffset);
        if (!String.IsNullOrEmpty(sbitoffset))
        {
            bitoffset = (UInt16)XmlConvert.ToInt16(sbitoffset); // This (double)cast is important to convert -0
        }

        // A ParameterRecordDataItem must not have two Refs with the same ByteOffset and BitOffset
        offsetValue = byteoffset * 8 + bitoffset;
        return reflengthtemp;
    }

    /// <summary>
    /// Check number: CN_0x00010021
    /// The 'ByteOffset' mustn't be a duplicate.
    /// The 'ByteOffset' plus length of the 'Const' element entries must be
    /// lower or equal to the next 'ByteOffset'.
    /// The value of the 'Length' attribute on the '...RecordDataItem' elements
    /// must be higher or equal to the maximal length of the 'Const' data entries.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010021()
    {
        var nl = GsdProfileBody.Descendants()
            .Where(x => x.Elements(NamespaceGsdDef + Elements.s_Const).ToList().Count > 0);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

        foreach (var element in nl)
        {
            // --------------------------------------------------
            // (1)
            // NOTE: (1) The 'ByteOffset' mustn't be a duplicate.
            // (2) The 'ByteOffset' plus length of the 'Const' element entries must be
            // lower or equal to the next 'ByteOffset'.
            // (3) The value of the 'Length' attribute on the '...RecordDataItem' elements 
            // must be higher or equal to the maximal length of the 'Const' data entries.

            // Get 'Length' attribute entry (required).
            string slength = Help.GetAttributeValueFromXElement(element, Attributes.s_Length);
            uint length = XmlConvert.ToUInt32(slength);

            // Collect relevant information.
            SortedList sl = new SortedList();

            // Get all 'Const' elements of the context node.
            var nl2 = element.Elements(NamespaceGsdDef + Elements.s_Const);

            foreach (var en in nl2)
            {
                // Get 'ByteOffset' attribute entry (optional).
                string sbyteoffset = Help.GetAttributeValueFromXElement(en, Attributes.s_ByteOffset);
                uint byteoffset = 0;
                if (!String.IsNullOrEmpty(sbyteoffset))
                    byteoffset = XmlConvert.ToUInt32(sbyteoffset);

                // Get 'Data' attribute entry (required).
                string sdata = Help.GetAttributeValueFromXElement(en, Attributes.s_Data);
                const string Delimiter = ",";
                IList<string> sldata = new List<string>(sdata.Split(Delimiter.ToCharArray()));

                // (1)
                if (sl.Contains(byteoffset))
                {
                    // "The 'ByteOffset' ({0}) is a duplicate."
                    string message = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00010021_1"),
                        byteoffset);
                    string xPath = Help.GetXPath(en);
                    var xlin = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xlin.LineNumber,
                        xlin.LinePosition,
                        message,
                        xPath,
                        ReportCategories.TypeSpecific,
                        "0x00010021_1");
                    return true; // ---------->
                }

                sl.Add(byteoffset, (UInt32)sldata.Count);
            }

            // (2)
            uint constlength = 0;
            foreach (uint ui in sl.Keys)
            {
                // 'Data' entry length and 'ByteOffset' must be lower or equal to the next 'ByteOffset'!
                if (constlength > ui)
                {
                    // "The 'Const' data entries are overlapped."
                    string message = Help.GetMessageString("M_0x00010021_2");
                    string xPath = Help.GetXPath(element);
                    var xlin = (IXmlLineInfo)element;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xlin.LineNumber,
                        xlin.LinePosition,
                        message,
                        xPath,
                        ReportCategories.TypeSpecific,
                        "0x00010021_2");
                    return true; // ---------->
                }

                constlength = ui + (uint)sl[ui];
            }

            // (3)
            // NOTE: The constlength of the last list entry, which is the HIGHEST entry, must be 
            // used to test overall length.
            if (length >= constlength)
            {
                continue;
            }

            // "The 'Length' of the data items ({0}) is lower than the 'Const' length ({1})."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010021_3"),
                slength,
                constlength);
            string xpath = Help.GetXPath(element.Attribute(Attributes.s_Length));
            var xli = (IXmlLineInfo)element.Attribute(Attributes.s_Length);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010021_3");
            }

            return true; // ---------->

        }

        return true;
    }

    /// <summary>
    /// (1) No 'AllowedValues' attribute must be given.
    /// (2) No 'ValueItemTarget' attribute must be given.
    /// (3) The 'DefaultValue' must fit to the DataType format.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckNonNumericDataTypes(XElement en, ref IList<string> errList)
    {
        // Get 'DefaultValue' attribute entry
        // (required for ParameterRecordDataItem/Ref and ARVendorBlock/Request/Ref, but not for (Profile)UnitDiagTypeItem/Ref).
        var adefaultvalue = en.Attribute(Attributes.s_DefaultValue);

        // Get 'AllowedValues' attribute entry (optional).
        var aallowedvalues = en.Attribute(Attributes.s_AllowedValues);

        // Get 'ValueItemTarget' attribute (optional).
        var avaluegsdid = en.Attribute(Attributes.s_ValueItemTarget);

        // Get 'DataType' attribute entry (required).
        string sdatatype = Help.GetAttributeValueFromXElement(en, Attributes.s_DataType);

        // (1) No 'AllowedValues' attribute must be given.
        if (aallowedvalues != null)
        {
            // "For 'DataType' ("{0}") no AllowedValues must be given."
            string message = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010022_c"), sdatatype);
            string xPath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
            var xlin = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);
            if (xlin != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportType_0X00010022,
                    xlin.LineNumber,
                    xlin.LinePosition,
                    message,
                    xPath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_c");
            }

            if (ReportType_0X00010022 == ReportTypes.GSD_RT_MinorError)
                errList.Add("m" + "0x00010022_c" + message);
            else
                errList.Add("e" + "0x00010022_c" + message);
        }

        // (2) No 'ValueItemTarget' attribute must be given.
        if (avaluegsdid != null)
        {
            // "For 'DataType' ("{0}") no ValueItemTarget must be given."
            string message = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010022_d"), sdatatype);
            string xPath = Help.GetXPath(en.Attribute(Attributes.s_ValueItemTarget));
            var xlin = (IXmlLineInfo)en.Attribute(Attributes.s_ValueItemTarget);
            if (xlin != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportType_0X00010022,
                    xlin.LineNumber,
                    xlin.LinePosition,
                    message,
                    xPath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_d");
            }

            if (ReportType_0X00010022 == ReportTypes.GSD_RT_MinorError)
                errList.Add("m" + "0x00010022_d" + message);
            else
                errList.Add("e" + "0x00010022_d" + message);
        }

        // (3) The 'DefaultValue' must fit to the DataType format, if available
        if (adefaultvalue == null)
            return;
        string sdefaultvalue = adefaultvalue.Value;

        String dataTypePattern = "";
        switch (sdatatype)
        {
            case "Date":
            case "TimeOfDay with date indication":
                dataTypePattern = "^[0-9]{4}-[0-9]{2}-[0-9]{2}(T[0-9]{2}:[0-9]{2}:[0-9]{2}(\\.[0-9]{1,3})?)?$";
                break;
            case "TimeOfDay without date indication":
                dataTypePattern = "^[0-9]{2}:[0-9]{2}:[0-9]{2}(\\.[0-9]{1,3})?$";
                break;
            case "TimeDifference with date indication":
                dataTypePattern = "^P([0-9]+D)?(T([0-9]+H)?([0-9]+M)?([0-9]+(\\.[0-9]{1,3})?S)?)?$";
                break;
            case "TimeDifference without date indication":
                dataTypePattern = "^P([0-9]+H)?([0-9]+M)?([0-9]+(\\.[0-9]{1,3})?S)?$";
                break;
            case "NetworkTime":
                dataTypePattern = "^[0-9]{4}-[0-9]{2}-[0-9]{2}(T[0-9]{2}:[0-9]{2}:[0-9]{2}(\\.[0-9]{1,9})?)?$";
                break;
            case "NetworkTimeDifference":
                dataTypePattern = "^-?P([0-9]+D)?(T([0-9]+H)?([0-9]+M)?([0-9]+(\\.[0-9]{1,9})?S)?)?$";
                break;
            case "VisibleString":
            case "UnicodeString8":
            case "61131_WSTRING":
            case "61131_STRING":
                dataTypePattern = ".*";
                break;
            case "OctetString":
                dataTypePattern = "^(0x[0-9a-fA-F][0-9a-fA-F],)*0x[0-9a-fA-F][0-9a-fA-F]$";
                break;
            case "Boolean":
                dataTypePattern = "^true|false$";
                break;
            case "V2":
                dataTypePattern = "^((true|false),){15}(true|false)$";
                break;
            case "L2":
                dataTypePattern = "^([0-9]+,){3}[0-9]+$";
                break;
            case "Unsigned8+Unsigned8":
                dataTypePattern = "^[0-9]+,0x[0-9a-fA-F][0-9a-fA-F]$";
                break;
            case "Float32+Unsigned8":
                dataTypePattern = "^-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?,0x[0-9a-fA-F][0-9a-fA-F]$";
                break;
            case "OctetString2+Unsigned8":
                dataTypePattern = "^0x[0-9a-fA-F][0-9a-fA-F],0x[0-9a-fA-F][0-9a-fA-F],0x[0-9a-fA-F][0-9a-fA-F]$";
                break;
            case "Unsigned16_S":
            case "Unsigned8_S":
                dataTypePattern = "^[0-9]+[BSUG]$";
                break;
            case "Integer16_S":
                dataTypePattern = "^-?[0-9]+[BSUG]$";
                break;
            case "OctetString_S":
                dataTypePattern = "^(0x[0-9a-fA-F][0-9a-fA-F][BSUG]{8},)*0x[0-9a-fA-F][0-9a-fA-F][BSUG]{8}$";
                break;
            case "TimeStamp":
                dataTypePattern = "^[GLA][0-9]+(\\.[0-9]{1,9})?$";
                break;
            case "TimeStampDifference":
            case "TimeStampDifferenceShort":
                dataTypePattern = "^-?[0-9]+(\\.[0-9]{1,9})?$";
                break;
        }

        Match dataTypeMatch = Regex.Match(sdefaultvalue, dataTypePattern);

        if (dataTypeMatch.Success)
        {
            return;
        }

        // "For 'DataType' ("{0}") the DefaultValue does not fit."
        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010022_e"), sdatatype);
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_DefaultValue);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                ReportType_0X00010022,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010022_e");
        }

        if (ReportType_0X00010022 == ReportTypes.GSD_RT_MinorError)
            errList.Add("m" + "0x00010022_e" + msg);
        else
            errList.Add("e" + "0x00010022_e" + msg);
    }

    /// <summary>
    /// (1) The value of the 'DefaultValue' attribute on the 'Ref' element must comply,
    ///     if available, with the specified data type from the 'DataType' attribute.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckDefaultValueAgainstDataType(
        XElement en,
        string sdefaultValue,
        object defaultvalue,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        // The value of the 'DefaultValue' attribute on the 'Ref' element must comply,
        // if available, with the specified data type from the 'DataType' attribute.
        if (sdefaultValue == null)
        {
            return;
        }

        if (null != defaultvalue)
        {
            return;
        }

        string msg = "";
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        IXmlLineInfo xli = en.Attribute(Attributes.s_DefaultValue);
        if (sdatatype == "BitArea")
        {
            // "The 'DefaultValue' ({0}) does not comply with the 'DataType' ("{1}" with bit length {2})."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_2"),
                sdefaultValue,
                sdatatype,
                bitlength);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_2");
            }

            errList.Add("e" + "0x00010022_2" + msg);
        }
        else
        {
            // "The 'DefaultValue' ({0}) does not comply with the 'DataType' ("{1}")."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_3"),
                sdefaultValue,
                sdatatype);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_3");
            }

            errList.Add("e" + "0x00010022_3" + msg);
        }


    }

    /// <summary>
    /// (2) Each 'Content' attribute entry from the 'ValueItem'
    ///     assignments must comply with the data type from the 'DataType' attribute.
    /// 
    /// </summary>
    /// <returns>Gets the list of all content values.</returns>
    protected virtual void CheckAssignContentAgainstDataTypeInt64(
        XElement en,
        string svaluegsdid,
        string sdatatype,
        uint bitlength,
        ref List<Int64> nlcontent,
        ref IList<string> errList)
    {
        // Each 'Content' attribute entry from the 'ValueItem'
        // assignments must comply with the data type from the 'DataType' attribute.
        if (!(string.IsNullOrEmpty(svaluegsdid)))
        {
            // Find the matching content nodes
            ValueItems.TryGetValue(svaluegsdid, out XElement valueItem);

            if (null == valueItem)
                return;

            var contents = (IEnumerable)valueItem.XPathEvaluate("./gsddef:Assignments/gsddef:Assign/@Content", Nsmgr);
            foreach (XAttribute content in contents)
            {
                object contentValue = Help.GetValueByDataType(content.Value, sdatatype, bitlength);
                bool maximumOverflow = Help.GetMaximumOverflowByDataType(content.Value, sdatatype, bitlength);
                // (2)
                if (null == contentValue)
                {
                    // Hier wird nicht als Fehler gemeldet, wenn Werte aus @Content nicht in der BitArea oder in Bit liegen, weil die ValueItem
                    // Eintr'ge mit ihren verschiedenen Assignments/Assign/@Content f'r verschiedene Referenzen wiederverwendet werden.
                    if (maximumOverflow && (sdatatype == "BitArea" || sdatatype == "Bit"))
                        continue;
                    string msg = "";
                    string xpath = Help.GetXPath(content);
                    IXmlLineInfo xli = content;
                    if (sdatatype == "BitArea")
                    {
                        // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}" with bit length {3})."
                        msg = String.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x00010022_6"),
                            svaluegsdid,
                            content.Value,
                            sdatatype,
                            bitlength);
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010022_6");
                        errList.Add("e" + "0x00010022_6" + msg);
                    }
                    else
                    {
                        // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}")."
                        msg = String.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x00010022_7"),
                            svaluegsdid,
                            content.Value,
                            sdatatype);
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010022_7");
                        errList.Add("e" + "0x00010022_7" + msg);
                    }
                }
                else
                {
                    // Fill the list with content values.
                    nlcontent.Add(Convert.ToInt64(contentValue, CultureInfo.InvariantCulture));
                }
            }

            nlcontent.Sort();
        }
    }
    protected virtual void CheckAssignContentAgainstDataTypeUInt64(
        XElement en,
        string svaluegsdid,
        string sdatatype,
        uint bitlength,
        ref List<UInt64> nlcontent,
        ref IList<string> errList)
    {
        // Each 'Content' attribute entry from the 'ValueItem'
        // assignments must comply with the data type from the 'DataType' attribute.
        if (!(string.IsNullOrEmpty(svaluegsdid)))
        {
            // Find the matching content nodes
            ValueItems.TryGetValue(svaluegsdid, out XElement valueItem);

            if (null == valueItem)
                return;

            var contents = (IEnumerable)valueItem.XPathEvaluate("./gsddef:Assignments/gsddef:Assign/@Content", Nsmgr);
            foreach (XAttribute content in contents)
            {
                object contentValue = Help.GetValueByDataType(content.Value, sdatatype, bitlength);
                bool maximumOverflow = Help.GetMaximumOverflowByDataType(content.Value, sdatatype, bitlength);

                // (2)
                if (null == contentValue)
                {
                    // Here no error is returned if the values of @Content don't reside in a BitArea or a Bit. The reason of this is that
                    // these entries can be reused for different Assignments/Assign/@Content for different references.
                    if (maximumOverflow && (sdatatype == "BitArea" || sdatatype == "Bit"))
                        continue;
                    string msg = "";
                    string xpath = Help.GetXPath(content);
                    IXmlLineInfo xli = content;
                    if (sdatatype == "BitArea")
                    {
                        // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}" with bit length {3})."
                        msg = String.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x00010022_6"),
                            svaluegsdid,
                            content.Value,
                            sdatatype,
                            bitlength);
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010022_6");
                        errList.Add("e" + "0x00010022_6" + msg);
                    }
                    else
                    {
                        // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}")."
                        msg = String.Format(
                            CultureInfo.CurrentCulture,
                            Help.GetMessageString("M_0x00010022_7"),
                            svaluegsdid,
                            content.Value,
                            sdatatype);
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010022_7");
                        errList.Add("e" + "0x00010022_7" + msg);
                    }
                }
                else
                {
                    // Fill the list with content values.
                    nlcontent.Add(Convert.ToUInt64(contentValue, CultureInfo.InvariantCulture));
                }
            }

            nlcontent.Sort();
        }
    }

    /// <summary>
    /// (3) Each entry in the 'AllowedValues' list must comply with the specified
    ///     data type from the 'DataType' attribute.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckAllowedValuesAgainstDataTypeInt64(
        XElement en,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        // Each entry must comply with the specified
        // data type from the 'DataType' attribute.
        for (int CurrentRange = 0; CurrentRange < allowedValueList.Count; CurrentRange++)
        {
            object allowedValueFrom = Help.GetValueByDataType(
                XmlConvert.ToString(allowedValueList[CurrentRange].From),
                sdatatype,
                bitlength);
            if (null == allowedValueFrom)
            {
                HandleInvalidAllowedValueFromInt64(
                    en,
                    allowedValueList,
                    CurrentRange,
                    sdatatype,
                    bitlength,
                    ref errList);
            }
            else if (allowedValueList[CurrentRange].From != allowedValueList[CurrentRange].To)
            {
                HandleInvalidAllowedValueToInt64(en, allowedValueList, CurrentRange, sdatatype, bitlength, ref errList);
            }
        }
    }

    private void HandleInvalidAllowedValueFromInt64(
        XElement en,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        int currentRange,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);
        string msg = "";
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));

        if (sdatatype == "BitArea")
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}" with bit length {2})."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_4"),
                XmlConvert.ToString(allowedValueList[currentRange].From),
                sdatatype,
                bitlength);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_4");
            }

            errList.Add("e" + "0x00010022_4" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_5"),
                XmlConvert.ToString(allowedValueList[currentRange].From),
                sdatatype);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_5");
            }

            errList.Add("e" + "0x00010022_5" + msg);
        }
    }

    private void HandleInvalidAllowedValueToInt64(
        XElement en,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        int currentRange,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        object allowedValueTo = Help.GetValueByDataType(
            XmlConvert.ToString(allowedValueList[currentRange].To),
            sdatatype,
            bitlength);
        if (null != allowedValueTo)
        {
            return;
        }

        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);
        string msg = "";
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));

        if (sdatatype == "BitArea")
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}" with bit length {2})."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_4"),
                XmlConvert.ToString(allowedValueList[currentRange].To),
                sdatatype,
                bitlength);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_4");
            }

            errList.Add("e" + "0x00010022_4" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_5"),
                XmlConvert.ToString(allowedValueList[currentRange].To),
                sdatatype);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_5");
            }

            errList.Add("e" + "0x00010022_5" + msg);
        }
    }
    protected virtual void CheckAllowedValuesAgainstDataTypeUInt64(
        XElement en,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        // Each entry must comply with the specified
        // data type from the 'DataType' attribute.

        for (int currentRange = 0; currentRange < allowedValueList.Count; currentRange++)
        {
            object allowedValueFrom = Help.GetValueByDataType(
                XmlConvert.ToString(allowedValueList[currentRange].From),
                sdatatype,
                bitlength);
            if (null == allowedValueFrom)
            {
                HandleInvalidAllowedValueFromUInt64(
                    en,
                    allowedValueList,
                    currentRange,
                    sdatatype,
                    bitlength,
                    ref errList);
            }
            else if (allowedValueList[currentRange].From != allowedValueList[currentRange].To)
            {
                HandleInvalidAllowedValueToUInt64(
                    en,
                    allowedValueList,
                    currentRange,
                    sdatatype,
                    bitlength,
                    ref errList);
            }
        }
    }

    private void HandleInvalidAllowedValueFromUInt64(
        XElement en,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        int currentRange,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);
        string msg = "";
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));

        if (sdatatype == "BitArea")
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}" with bit length {2})."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_4"),
                XmlConvert.ToString(allowedValueList[currentRange].From),
                sdatatype,
                bitlength);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_4");
            }

            errList.Add("e" + "0x00010022_4" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_5"),
                XmlConvert.ToString(allowedValueList[currentRange].From),
                sdatatype);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_5");
            }

            errList.Add("e" + "0x00010022_5" + msg);
        }
    }

    private void HandleInvalidAllowedValueToUInt64(
        XElement en,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        int currentRange,
        string sdatatype,
        uint bitlength,
        ref IList<string> errList)
    {
        object allowedValueTo = Help.GetValueByDataType(
            XmlConvert.ToString(allowedValueList[currentRange].To),
            sdatatype,
            bitlength);
        if (null != allowedValueTo)
        {
            return;
        }

        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);
        string msg = "";
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
        if (sdatatype == "BitArea")
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}" with bit length {2})."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_4"),
                XmlConvert.ToString(allowedValueList[currentRange].To),
                sdatatype,
                bitlength);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_4");
            }

            errList.Add("e" + "0x00010022_4" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
            msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_5"),
                XmlConvert.ToString(allowedValueList[currentRange].To),
                sdatatype);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_5");
            }

            errList.Add("e" + "0x00010022_5" + msg);
        }
    }
    /// <summary>
    /// (4) The 'DefaultValue' must be available in the 'Content' list.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckDefaultValueAgainstAssignContentInt64(
        XElement en,
        string sdefaultvalue,
        object defaultvalue,
        List<Int64> nlcontent,
        ref IList<string> errList)
    {
        // The 'DefaultValue' must be available in the 'Content' list.
        if (defaultvalue == null
            || nlcontent.Count <= 0)
        {
            return;
        }

        if (nlcontent.Contains(Convert.ToInt64(defaultvalue, CultureInfo.InvariantCulture)))
        {
            return;
        }

        // "The 'DefaultValue' ({0}) is not available from 'ValueItem' assignments 'Content' attribute."
        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010022_8"), sdefaultvalue);
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_DefaultValue);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010022_8");
        }

        errList.Add("e" + "0x00010022_8" + msg);


    }

    protected virtual void CheckDefaultValueAgainstAssignContentUInt64(
        XElement en,
        string sdefaultvalue,
        object defaultvalue,
        List<UInt64> nlcontent,
        ref IList<string> errList)
    {
        // The 'DefaultValue' must be available in the 'Content' list.
        if (defaultvalue == null
            || nlcontent.Count <= 0)
        {
            return;
        }

        if (nlcontent.Contains(Convert.ToUInt64(defaultvalue, CultureInfo.InvariantCulture)))
        {
            return;
        }

        // "The 'DefaultValue' ({0}) is not available from 'ValueItem' assignments 'Content' attribute."
        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010022_8"), sdefaultvalue);
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_DefaultValue);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010022_8");
        }

        errList.Add("e" + "0x00010022_8" + msg);

    }

    /// <summary>
    /// (5) Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckAllowedValuesAgainstAssignContentInt64(
        XElement en,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        List<Int64> nlcontent,
        ref IList<string> errList)
    {
        // Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
        if (nlcontent.Count <= 0)
        {
            return;
        }

        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);

        int i = 0;
        long contentValue = nlcontent[i];
        for (int CurrentRange = 0; CurrentRange < allowedValueList.Count; CurrentRange++)
        {
            Int64 allowedValue = allowedValueList[CurrentRange].From;
            while (allowedValue <= allowedValueList[CurrentRange].To)
            {
                bool foundInContent = CheckIfAllowedValueFoundInContent(
                    nlcontent,
                    allowedValue,
                    ref contentValue,
                    ref i);

                if (!foundInContent)
                {
                    HandleNotFoundInContent(en, allowedValueList, CurrentRange, allowedValue, xli, ref errList);
                    break;
                }

                allowedValue++;
            }
        }

    }

    private void HandleNotFoundInContent(
        XElement en,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        int currentRange,
        Int64 allowedValue,
        IXmlLineInfo xli,
        ref IList<string> errList)
    {
        if (allowedValueList[currentRange].From != allowedValueList[currentRange].To)
        {
            // "At least one value ({0}) specified by the range: {1}..{2} in 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_f"),
                allowedValue,
                allowedValueList[currentRange].From,
                allowedValueList[currentRange].To);
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010022_f");
            }

            errList.Add("e" + "0x00010022_f" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_9"),
                allowedValue);
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_9");
            }

            errList.Add("e" + "0x00010022_9" + msg);
        }
    }

    private static bool CheckIfAllowedValueFoundInContent(
        IReadOnlyList<long> nlcontent,
        long allowedValue,
        ref long contentValue,
        ref int i)
    {
        bool foundInContent = false;
        while (contentValue <= allowedValue && i < nlcontent.Count)
        {
            if (allowedValue == contentValue)
            {
                foundInContent = true;
                break;
            }

            i++;
            if (i < nlcontent.Count)
                contentValue = nlcontent[i];
        }

        return foundInContent;
    }
    protected virtual void CheckAllowedValuesAgainstAssignContentUInt64(
        XElement en,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        List<UInt64> nlcontent,
        ref IList<string> errList)
    {
        // Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
        if (nlcontent.Count <= 0)
        {
            return;
        }

        var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);

        int i = 0;
        UInt64 contentValue = nlcontent[i];
        for (int CurrentRange = 0; CurrentRange < allowedValueList.Count; CurrentRange++)
        {
            UInt64 allowedValue = allowedValueList[CurrentRange].From;
            while (allowedValue <= allowedValueList[CurrentRange].To)
            {
                bool foundInContent = CheckIfAllowedULongValueFoundInContent(
                    nlcontent,
                    allowedValue,
                    ref contentValue,
                    ref i);

                if (!foundInContent)
                {
                    HandleNotFoundInContent(en, allowedValueList, CurrentRange, allowedValue, xli, ref errList);
                    break;
                }

                allowedValue++;
            }
        }

    }

    private void HandleNotFoundInContent(
        XElement en,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        int currentRange,
        UInt64 allowedValue,
        IXmlLineInfo xli,
        ref IList<string> errList)
    {
        if (allowedValueList[currentRange].From != allowedValueList[currentRange].To)
        {
            // "At least one value ({0}) specified by the range: {1}..{2} in 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_f"),
                allowedValue,
                allowedValueList[currentRange].From,
                allowedValueList[currentRange].To);
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.PlugRules,
                    "0x00010022_f");
            }

            errList.Add("e" + "0x00010022_f" + msg);
        }
        else
        {
            // "The value ({0}) specified by 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010022_9"),
                allowedValue);
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_9");
            }

            errList.Add("e" + "0x00010022_9" + msg);
        }
    }

    private static bool CheckIfAllowedULongValueFoundInContent(
        IReadOnlyList<ulong> nlcontent,
        ulong allowedValue,
        ref ulong contentValue,
        ref int i)
    {
        bool foundInContent = false;
        while (contentValue <= allowedValue && i < nlcontent.Count)
        {
            if (allowedValue == contentValue)
            {
                foundInContent = true;
                break;
            }

            i++;
            if (i < nlcontent.Count)
                contentValue = nlcontent[i];
        }

        return foundInContent;
    }
    /// <summary>
    /// (6) The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckDefaultValueAgainstAllowedValuesInt64(
        XElement en,
        string sdefaultvalue,
        object defaultvalue,
        List<ValueListHelper.Int64ValueRangeT> allowedValueList,
        ref IList<string> errList)
    {
        // The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
        if (defaultvalue == null
            || allowedValueList.Count <= 0)
        {
            return;
        }

        bool valueFoundInRange = false;
        for (int currentRange = 0; currentRange < allowedValueList.Count; currentRange++)
        {
            Int64 allowedValueFrom = allowedValueList[currentRange].From;
            Int64 allowedValueTo = allowedValueList[currentRange].To;
            if (Convert.ToInt64(defaultvalue, CultureInfo.InvariantCulture) < allowedValueFrom
                || Convert.ToInt64(defaultvalue, CultureInfo.InvariantCulture) > allowedValueTo)
            {
                continue;
            }

            valueFoundInRange = true;
            break;
        }

        if (valueFoundInRange)
        {
            return;
        }

        // "The 'DefaultValue' ({0}) is not available from the 'AllowedValues' ({1})."
        string msg = String.Format(
            CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x00010022_a"),
            sdefaultvalue,
            Help.GetAttributeValueFromXElement(en, Attributes.s_AllowedValues));
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        IXmlLineInfo xli = en.Attribute(Attributes.s_DefaultValue);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010022_a");
        }

        errList.Add("e" + "0x00010022_a" + msg);


    }

    protected virtual void CheckDefaultValueAgainstAllowedValuesUInt64(
        XElement en,
        string sdefaultvalue,
        object defaultvalue,
        List<ValueListHelper.UInt64ValueRangeT> allowedValueList,
        ref IList<string> errList)
    {
        // The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
        if (defaultvalue == null
            || allowedValueList.Count <= 0)
        {
            return;
        }

        bool valueFoundInRange = false;
        for (int CurrentRange = 0; CurrentRange < allowedValueList.Count; CurrentRange++)
        {
            UInt64 allowedValueFrom = allowedValueList[CurrentRange].From;
            UInt64 allowedValueTo = allowedValueList[CurrentRange].To;
            if (Convert.ToUInt64(defaultvalue, CultureInfo.InvariantCulture) >= allowedValueFrom
                && Convert.ToUInt64(defaultvalue, CultureInfo.InvariantCulture) <= allowedValueTo)
            {
                valueFoundInRange = true;
                break;
            }
        }

        if (valueFoundInRange)
        {
            return;
        }

        // "The 'DefaultValue' ({0}) is not available from the 'AllowedValues' ({1})."
        string msg = String.Format(
            CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x00010022_a"),
            sdefaultvalue,
            Help.GetAttributeValueFromXElement(en, Attributes.s_AllowedValues));
        string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
        IXmlLineInfo xli = (IXmlLineInfo)en.Attribute(Attributes.s_DefaultValue);
        if (xli != null)
        {
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010022_a");
        }

        errList.Add("e" + "0x00010022_a" + msg);


    }

    /// <summary>
    /// (1) The value of the 'DefaultValue' attribute on the 'Ref' element must comply,
    ///     if available, with the specified data type from the 'DataType' attribute.
    /// (2) Each 'Content' attribute entry from the 'ValueItem'
    ///     assignments must comply with the data type from the 'DataType' attribute.
    /// (3) Each entry in the 'AllowedValues' list must comply with the specified
    ///     data type from the 'DataType' attribute.
    /// (4) The 'DefaultValue' must be available in the 'Content' list.
    /// (5) Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
    /// (6) The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckNumericDataTypes(XElement en, ref IList<string> errList)
    {
        // Get 'DataType' attribute entry (required).
        string sdatatype = Help.GetAttributeValueFromXElement(en, Attributes.s_DataType);

        // Get 'ValueItemTarget' attribute entry (optional).
        string svaluegsdid = Help.GetAttributeValueFromXElement(en, Attributes.s_ValueItemTarget); // Optional.

        // Get 'BitLength' attribute entry (optional). Normally only needed for data type 'BitArea'.
        uint bitlength = 1; // Is default!
        string sbitlength = Help.GetAttributeValueFromXElement(en, Attributes.s_BitLength); // Optional.
        if (!String.IsNullOrEmpty(sbitlength))
            bitlength = XmlConvert.ToUInt32(sbitlength);

        // Get 'DefaultValue' attribute entry (required).
        var adefaultvalue = en.Attribute(Attributes.s_DefaultValue);
        string sdefaultvalue = null;
        object defaultvalue = null;
        if (adefaultvalue != null)
        {
            sdefaultvalue = adefaultvalue.Value;
            defaultvalue = Help.GetValueByDataType(sdefaultvalue, sdatatype, bitlength);
        }

        switch (sdatatype)
        {
            case "Integer8":
            case "Integer16":
            case "N2":
            case "X2":
            case "Integer32":
            case "N4":
            case "X4":
            case "Integer64":
                {
                    // Get the list from the 'AllowedValues' attribute entry (required).
                    List<ValueListHelper.Int64ValueRangeT> allowedValueListInt64 =
                        ValueListHelper.NormalizeValueListInt64(en.Attribute(Attributes.s_AllowedValues), Store);

                    // List with content values.
                    List<Int64> nlcontentInt64 = new List<Int64>();

                    CheckDefaultValueAgainstDataType(en, sdefaultvalue, defaultvalue, sdatatype, bitlength, ref errList);              // (1)
                    CheckAssignContentAgainstDataTypeInt64(en, svaluegsdid, sdatatype, bitlength, ref nlcontentInt64, ref errList);    // (2)
                    CheckAllowedValuesAgainstDataTypeInt64(en, allowedValueListInt64, sdatatype, bitlength, ref errList);              // (3)
                    CheckDefaultValueAgainstAssignContentInt64(en, sdefaultvalue, defaultvalue, nlcontentInt64, ref errList);          // (4)
                    CheckAllowedValuesAgainstAssignContentInt64(en, allowedValueListInt64, nlcontentInt64, ref errList);               // (5)
                    CheckDefaultValueAgainstAllowedValuesInt64(en, sdefaultvalue, defaultvalue, allowedValueListInt64, ref errList);   // (6)
                    }
                break;

            default:
                {
                    // Get the list from the 'AllowedValues' attribute entry (required).
                    List<ValueListHelper.UInt64ValueRangeT> allowedValueListUInt64 =
                        ValueListHelper.NormalizeValueListUInt64(en.Attribute(Attributes.s_AllowedValues), Store);

                    // List with content values.
                    List<UInt64> nlcontentUInt64 = new List<UInt64>();

                    CheckDefaultValueAgainstDataType(en, sdefaultvalue, defaultvalue, sdatatype, bitlength, ref errList);                // (1)
                    CheckAssignContentAgainstDataTypeUInt64(en, svaluegsdid, sdatatype, bitlength, ref nlcontentUInt64, ref errList);    // (2)
                    CheckAllowedValuesAgainstDataTypeUInt64(en, allowedValueListUInt64, sdatatype, bitlength, ref errList);              // (3)
                    CheckDefaultValueAgainstAssignContentUInt64(en, sdefaultvalue, defaultvalue, nlcontentUInt64, ref errList);          // (4)
                    CheckAllowedValuesAgainstAssignContentUInt64(en, allowedValueListUInt64, nlcontentUInt64, ref errList);              // (5)
                    CheckDefaultValueAgainstAllowedValuesUInt64(en, sdefaultvalue, defaultvalue, allowedValueListUInt64, ref errList);   // (6)
                    }
                break;
        }
    }

    /// <summary>
    /// Checks numeric and non-numeric values and data types.
    /// 
    /// </summary>
    /// <returns>None.</returns>
    protected virtual void CheckValuesAndDataTypes(XElement en, ref IList<string> errList)
    {
        // Get 'DataType' attribute entry (required).
        string sdatatype = Help.GetAttributeValueFromXElement(en, Attributes.s_DataType);

        // Some DataType can not be checked
        if (sdatatype == "Date"
            || sdatatype == "TimeOfDay with date indication"
            || sdatatype == "TimeOfDay without date indication"
            || sdatatype == "TimeDifference with date indication"
            || sdatatype == "TimeDifference without date indication"
            || sdatatype == "NetworkTime"
            || sdatatype == "NetworkTimeDifference"
            || sdatatype == "VisibleString"
            || sdatatype == "OctetString"
            || sdatatype == "Boolean"
            || sdatatype == "V2"
            || sdatatype == "L2"
            || sdatatype == "Unsigned8+Unsigned8"
            || sdatatype == "Float32+Unsigned8"
            || sdatatype == "OctetString2+Unsigned8"
            || sdatatype == "Unsigned16_S"
            || sdatatype == "Integer16_S"
            || sdatatype == "Unsigned8_S"
            || sdatatype == "OctetString_S"
            || sdatatype == "TimeStamp"
            || sdatatype == "TimeStampDifference"
            || sdatatype == "TimeStampDifferenceShort"
            || sdatatype == "UnicodeString8"
            || sdatatype == "61131_STRING"
            || sdatatype == "61131_WSTRING")

        {
            CheckNonNumericDataTypes(en, ref errList);
        }
        else
        {
            CheckNumericDataTypes(en, ref errList);
        }
    }

    /// <summary>
    /// Check number: CN_0x00010022
    /// (1) Warning if Ref is changeable but not visible.
    /// (2) Checks numeric and non-numeric values and data types.
    /// 
    /// From GSDML V2.2 on the DataTypes Float32 and Float64 are allowed here.
    /// It is checked by schema, that Float32 and Float64 are not used before.
    /// For GSDML V2.2 an override will be done so that Float32 and Float64 are also correctly handled. 
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010022()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Ref);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        // The list with refs can have up to 30000 entries.
        // This means extremely long runtimes.
        // But for each module the refs are often repeated.
        // Same refs don't need to be checked twice regarding data type,
        // therefore the list of refs is sorted and the duplicate entries are not checked,
        // but only the previously stored error message is output.
        List<XElement> nl1 = new List<XElement>(nl.Count());

        foreach (XElement en in nl)
        {
            nl1.Add(en);
        }

        RefComparer rc = new RefComparer();
        nl1.Sort(rc);

        IList<string> errList = new List<string>();

        for (int i = 0; i < nl1.Count; i++)
        {
            XElement en = nl1[i];

            // (1)
            // Warning if Changeable =  true or 1 and Visible= false or 0
            string visibleStr = Help.GetAttributeValueFromXElement(en, Attributes.s_Visible);
            string changeableStr = Help.GetAttributeValueFromXElement(en, Attributes.s_Changeable);

            bool bVisible = visibleStr == "" ? true : XmlConvert.ToBoolean(visibleStr);
            bool bChangeable = changeableStr == "" ? true : XmlConvert.ToBoolean(changeableStr);
            if (!bVisible && bChangeable)
            {
                // "Ref is changeable but not visible."
                string msg = Help.GetMessageString("M_0x00010022_1");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_1");
                errList.Add("w" + "0x00010022_1" + msg);
            }

            // (2)
            // Checks numeric and non-numeric values and data types.
            CheckValuesAndDataTypes(en, ref errList);

            // Skip the double defined refs (on different DAPs)
            i = SkipDoubleRefinedRefs(i, nl1, rc, en, errList);

        }

        return true;
    }

    private int SkipDoubleRefinedRefs(
        int i,
        IReadOnlyList<XElement> nl1,
        RefComparer rc,
        XElement en,
        ICollection<string> errList)
    {
        int res = 0;
        while (res == 0)
        {
            if (i == nl1.Count - 1)
                break;

            XElement en1 = nl1[i + 1];
            res = rc.Compare(en, en1);
            if (res == 0)
            {
                foreach (string err in errList)
                {
                    string errType = err.Substring(0, 1);
                    string errNo = err.Substring(1, 12);
                    string errMsg = err.Substring(13);
                    string xpath = Help.GetXPath(en1);
                    var xli = (IXmlLineInfo)en1;
                    ReportTypes reportType = ReportTypes.GSD_RT_Error;
                    if (errType == "m")
                        reportType = GSDI.ReportTypes.GSD_RT_MinorError;
                    if (errType == "w")
                        reportType = ReportTypes.GSD_RT_Warning;
                    Store.CreateAndAnnounceReport(
                        reportType,
                        xli.LineNumber,
                        xli.LinePosition,
                        errMsg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        errNo);
                }

                i++;
            }
            else
            {
                errList.Clear();
            }
        }

        return i;
    }

    /// <summary>
    /// Check number: CN_0x00010023
    /// Check that one entry of the 'SendClock' attribute (list of values) is '32'.
    /// 
    /// </summary>
    /// <remarks>
    /// All other aspects of timing properties will not be checked, because this element
    /// is only used for very old devices with PNIO-1.
    /// 
    /// </remarks>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010023()
    {
        // Find all 'DeviceAccessPointItem' elements (required).
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

        foreach (var dap in nl)
        {
            // Get 'SendClock' list (optional).
            //var nlsendclock = (IEnumerable)en.XPathEvaluate(".//gsddef:TimingProperties/@SendClock", Nsmgr);
            var nlsendclock = dap.Descendants(NamespaceGsdDef + Elements.s_TimingProperties)
                .Attributes(Attributes.s_SendClock);

            foreach (XAttribute attSendclock in nlsendclock)
            {
                if (null != attSendclock)
                {
                    List<ValueListHelper.ValueRangeT> lsendclock =
                        ValueListHelper.NormalizeValueList(attSendclock, Store);
                    if (!ValueListHelper.IsValueInValueList(32, lsendclock))
                    {
                        // "The 'SendClock' attribute must contain the mandatory value 32."
                        string msg = Help.GetMessageString("M_0x00010023_1");
                        string xpath = Help.GetXPath(attSendclock);
                        var xli = (IXmlLineInfo)attSendclock;
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_MinorError,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010023_1");
                    }
                }
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010024
    /// The 'ProfileBody/ApplicationProcess' element must be present for CheckUniqueIds.Check to be called.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCN_0x00010024()
    {
        var nl = Gsd.Descendants(NamespaceGsdDef + Elements.s_ApplicationProcess).FirstOrDefault();

        if (Help.IsNodeUnderXsAny(nl, null))
            nl = null;

        if (nl == null)
        {
            nl = Gsd.Descendants(NamespaceGsdDef + Elements.s_ProfileBody).FirstOrDefault();
            if (nl == null)
                nl = Gsd.Descendants(NamespaceGsdDef + Elements.s_Iso15745Profile).FirstOrDefault();
            int lineNumber = 0;
            int linePosition = 0;
            if (nl != null)
            {
                IXmlLineInfo xli = nl;
                lineNumber = xli.LineNumber;
                linePosition = xli.LinePosition;
            }

            // "The element 'ProfileBody/ApplicationProcess' is missing. It must be given just one-time."
            string msg = Help.GetMessageString("M_0x00010024_1");
            string xpath = "//ISO15745Profile/ProfileBody";
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineNumber,
                linePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010024_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010025
    /// DeviceID must not be 0.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010025()
    {
        var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Iso15745Profile)
            .Elements(NamespaceGsdDef + Elements.s_ProfileBody)
            .Elements(NamespaceGsdDef + Elements.s_DeviceIdentity)
            .Attributes(Attributes.s_DeviceId);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute attribute in nl)
        {
            var xli = (IXmlLineInfo)attribute;

            uint deviceId = 0;
            try
            {
                string deviceIdAsString = attribute.Value;
                deviceId = Convert.ToUInt16(deviceIdAsString.Substring(2), 16);
            }
            catch (FormatException)
            {
                // "Error in value of 'DeviceID'."
                string msg = Help.GetMessageString("M_0x00010025_2");
                string xpath = Help.GetXPath(attribute);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010025_2");
                return true;
            }

            if (deviceId == 0)
            {
                // "The 'DeviceID' must not be 0."
                string msg = Help.GetMessageString("M_0x00010025_1");
                string xpath = Help.GetXPath(attribute);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010025_1");
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010026
    /// The first entry of the 'schemaLocation' attribute must contain either the namespace:
    /// "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/Common/2003/11/Primitives".
    /// The second entry of the 'schemaLocation' attribute must contain the primitives schema: "..\..\xsd\Common-Primitives-v1.0.xsd".
    /// The second entry of the 'schemaLocation' attribute must contain the device profile schema: "..\xsd\GSDML-DeviceProfile-vX.Y.xsd".
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010026()
    {
        XNamespace ns = Nsmgr.LookupNamespace("gsdxsi");
        if (Gsd.Root == null)
        {
            return true;
        }

        XAttribute nl = Gsd.Root.Attribute(ns + "schemaLocation");
        if (nl != null)
        {
            var xli = (IXmlLineInfo)nl;
            if (!CheckParameter.ContainsKey("GsdName")
                || CheckParameter["GsdName"] == null)
            {
                return true;
            }

            const bool IgnoreCase = true;
            bool deviceProfileUsed = false;

            int index1 = nl.Value.IndexOf("http", StringComparison.CurrentCultureIgnoreCase);
            if (CreateReport0x00010026_3(
                    index1,
                    nl,
                    xli,
                    IgnoreCase,
                    out bool xsdGiven,
                    ref deviceProfileUsed,
                    out int index2))
            {
                return true;
            }

            if (CreateReport0x00010026_2(index2, xsdGiven, nl, xli))
            {
                return true;
            }


            if (-1 == index2)
                index2 = 0;

            if (deviceProfileUsed)
            {
                string deviceProfileSchema = nl.Value.Remove(0, index2);
                CreateReport0x00010026_5(deviceProfileSchema, IgnoreCase, nl, xli);
                CreateReport0x00010026_8(deviceProfileSchema, nl, xli);

            }
            else
            {
                CreateReport0x00010026_6(nl, index2, IgnoreCase, xli);

            }

        }
        else
        {
            CreateReport0x00010026_1();

        }

        return true;
    }

    private void CreateReport0x00010026_1()
    {
        int lineNumber = 0;
        int linePosition = 0;
        if (Gsd.Root != null)
        {
            var xli = (IXmlLineInfo)Gsd.Root;
            lineNumber = xli.LineNumber;
            linePosition = xli.LinePosition;
        }

        // "The 'schemaLocation' attribute must be given."
        string msg = Help.GetMessageString("M_0x00010026_1");
        Store.CreateAndAnnounceReport(
            ReportType_0X00010026,
            lineNumber,
            linePosition,
            msg,
            null,
            ReportCategories.TypeSpecific,
            "0x00010026_1");
    }

    private void CreateReport0x00010026_6(XAttribute nl, int index2, bool ignoreCase, IXmlLineInfo xli)
    {
        string primitivesSchema = nl.Value.Remove(0, index2);
        const string RequiredPrimitives = "..\\..\\xsd\\Common-Primitives-v1.0.xsd";
        if (0 != string.Compare(
                primitivesSchema,
                0,
                RequiredPrimitives,
                0,
                RequiredPrimitives.Length,
                ignoreCase,
                CultureInfo.InvariantCulture))
        {
            // "The second entry of the 'schemaLocation' attribute must contain the primitives schema: "..\..\xsd\Common-Primitives-v1.0.xsd"."
            string msg = Help.GetMessageString("M_0x00010026_6");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_6");
        }
    }

    private void CreateReport0x00010026_8(string deviceProfileSchema, XObject nl, IXmlLineInfo xli)
    {

        // get version from schemaLocation attribute, DeviceProfile entry
        const string DeviceProfilePattern = @"deviceprofile-(v[0-9]+\.[0-9]*).xsd\s*$";
        Match deviceProfileMatch = Regex.Match(
            deviceProfileSchema.ToUpperInvariant().ToLower(CultureInfo.CurrentCulture),
            DeviceProfilePattern);

        // get all versions as string
        string deviceProfileVersion = deviceProfileMatch.Groups[1].ToString();

        if (deviceProfileVersion != FileNameGsdmlVersion)
        {
            // "The 'schemaLocation' attribute does not reference the appropriate schema file."
            string msg = Help.GetMessageString("M_0x00010026_8");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_8");
        }
    }

    private void CreateReport0x00010026_5(string deviceProfileSchema, bool ignoreCase, XObject nl, IXmlLineInfo xli)
    {
        const string RequiredProfileSchema = "..\\XSD\\GSDML-DeviceProfile";
        if (0 != string.Compare(
                deviceProfileSchema,
                0,
                RequiredProfileSchema,
                0,
                RequiredProfileSchema.Length,
                ignoreCase,
                CultureInfo.InvariantCulture))
        {
            // "The second entry of the 'schemaLocation' attribute must contain the device profile schema: "..\xsd\GSDML-DeviceProfile-vX.Y.xsd"."
            string msg = Help.GetMessageString("M_0x00010026_5");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_5");
        }
    }

    private bool CreateReport0x00010026_2(int index2, bool xsdGiven, XObject nl, IXmlLineInfo xli)
    {
        if (-1 == index2
            && !xsdGiven)
        {
            // "The 'schemaLocation' attribute does not contain the appropriate number of two entries."
            string msg = Help.GetMessageString("M_0x00010026_2");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_2");
            return true;
        }

        return false;
    }

    private bool CreateReport0x00010026_3(
        int index1,
        XAttribute nl,
        IXmlLineInfo xli,
        bool ignoreCase,
        out bool xsdGiven,
        ref bool deviceProfileUsed,
        out int index2)
    {
        xsdGiven = false;
        index2 = 0;
        if (-1 == index1)
        {
            // "The first entry of the 'schemaLocation' attribute must contain either the namespace:
            // "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/Common/2003/11/Primitives"."
            string msg = Help.GetMessageString("M_0x00010026_3");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_3");
            return true;
        }

        const string RequiredNamespace1 = "http://www.profibus.com/GSDML/2003/11/DeviceProfile";
        const string RequiredNamespace2 = "http://www.profibus.com/Common/2003/11/Primitives";
        xsdGiven = false;
        if ((nl.Value.Length > index1 + RequiredNamespace2.Length)
            && (-1 != nl.Value.IndexOf(
                    "xsd",
                    index1 + RequiredNamespace2.Length,
                    StringComparison.CurrentCultureIgnoreCase)))
            xsdGiven = true;
        if (0 == string.Compare(
                nl.Value,
                index1,
                RequiredNamespace1,
                0,
                RequiredNamespace1.Length,
                ignoreCase,
                CultureInfo.InvariantCulture))
        {
            deviceProfileUsed = true;
            index2 = nl.Value.IndexOf(
                "..",
                index1 + RequiredNamespace1.Length,
                StringComparison.CurrentCultureIgnoreCase);
        }
        else if (0 == string.Compare(
                     nl.Value,
                     index1,
                     RequiredNamespace2,
                     0,
                     RequiredNamespace2.Length,
                     ignoreCase,
                     CultureInfo.InvariantCulture))
        {
            deviceProfileUsed = false;
            index2 = nl.Value.IndexOf(
                "..",
                index1 + RequiredNamespace2.Length,
                StringComparison.CurrentCultureIgnoreCase);
        }
        else
        {
            // "The first entry of the 'schemaLocation' attribute must contain either the namespace:
            // "http://www.profibus.com/GSDML/2003/11/DeviceProfile" or "http://www.profibus.com/Common/2003/11/Primitives"."
            string msg = Help.GetMessageString("M_0x00010026_3");
            string xpath = Help.GetXPath(nl);
            Store.CreateAndAnnounceReport(
                ReportType_0X00010026,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010026_3");
            return true;
        }

        return false;
    }

    /// <summary>
    /// Check number: CN_0x00010027
    /// If "Ref/@DataType" has either the value "VisibleString" or "OctetString",
    /// then "Ref/@Length" should exist and its value should not be equal to 0.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010027()
    {
        // Find all 'VisibleString' or 'OctetString' without '@Length' given.
        string xp = ".//gsddef:Ref[" + DataTypesRequiresLength + "]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "'Ref/@DataType' = "VisibleString" or 'Ref/@DataType' = "OctetString" not allowed,
            // because 'Ref/@Length' not available. 'Ref/@Length' is available from GSDML V2.1 on."
            string msg = Help.GetMessageString("M_0x00010027_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010027_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010028
    /// Check, that 'ProfileBody/DeviceIdentity' is given just one-time.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010028()
    {
        string xp = ".//gsddef:DeviceIdentity";
        var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
        var nl = nl1.ToList();
        // Check, that 'ProfileBody/DeviceIdentity' is given just one-time.
        if (nl.Count == 0)
        {
            var profile = Gsd.XPathSelectElement(".//gsddef:ISO15745Profile/gsddef:ProfileBody", Nsmgr);
            if (profile == null)
                profile = Gsd.Root;
            int lineNumber = 0;
            int linePosition = 0;
            if (profile != null)
            {
                var xli = (IXmlLineInfo)profile;
                lineNumber = xli.LineNumber;
                linePosition = xli.LinePosition;
            }

            // "The element 'ProfileBody/DeviceIdentity' is missing. It must be given just one-time."
            string msg = Help.GetMessageString("M_0x00010028_1");
            string xpath = "//ISO15745Profile/ProfileBody";
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineNumber,
                linePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010028_1");
        }
        else if (nl.Count > 1)
        {
            // "The element 'ProfileBody/DeviceIdentity' is more than one-time given. It must be given just one-time."
            string msg = Help.GetMessageString("M_0x00010028_2");
            string xpath = "//ISO15745Profile/ProfileBody";
            var xli = (IXmlLineInfo)nl[1];
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010028_2");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010029
    /// The element 'ProfileBody/DeviceManager' is superfluous.
    /// The element 'ProfileBody/ExternalProfileHandle' is superfluous.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010029()
    {
        string xp = ".//gsddef:DeviceManager";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        // Find superfluous 'ProfileBody/DeviceManager'
        foreach (var en in nl)
        {
            // "The element 'ProfileBody/DeviceManager' is superfluous."
            string msg = Help.GetMessageString("M_0x00010029_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010029_1");
        }

        xp = ".//gsddef:ExternalProfileHandle";
        nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        // Find superfluous 'ProfileBody/ExternalProfileHandle'
        foreach (var en in nl)
        {
            // "The element 'ProfileBody/ExternalProfileHandle' is superfluous."
            string msg = Help.GetMessageString("M_0x00010029_2");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010029_2");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001002A
    /// The element 'ProfileBody/DeviceFunction' must be given just one-time.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002A()
    {
        string xp = ".//gsddef:DeviceFunction";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        var nl1 = nl.ToList();
        // Check, if 'ProfileBody/DeviceFunction' is given more than one-time.
        if (nl1.Count > 1)
        {
            // "The element 'ProfileBody/DeviceFunction' must be given just one-time.
            // Superfluous definitions will be ignored."
            string msg = Help.GetMessageString("M_0x0001002A_1");
            string xpath = "//ISO15745Profile/ProfileBody";
            var xli = (IXmlLineInfo)nl1[1];
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002A_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001002B
    /// The element 'ProfileBody/ApplicationProcess' must be given just one-time.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002B()
    {
        string xp = ".//gsddef:ApplicationProcess";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        var nl1 = nl.ToList();
        // Check, if 'ProfileBody/DeviceFunction' is given not more than one-time.
        if (nl1.Count > 1)
        {
            // "The element 'ProfileBody/ApplicationProcess' must be given just one-time.
            // Superfluous definitions will be ignored."
            string msg = Help.GetMessageString("M_0x0001002B_1");
            string xpath = "//ISO15745Profile/ProfileBody";
            var xli = (IXmlLineInfo)nl1[1];
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002B_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001002C
    /// 'ProfileHeader' should be given just one-time with predefined values.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002C()
    {
        var nl = Gsd.Descendants(NamespaceGsdDef + Elements.s_ProfileHeader).ToList();

        // The check, that 'ProfileHeader' is given just one-time, is done by schema, but for safety reasons ...
        if (nl.Count == 0)
            return true;

        XElement profileHeader = nl[0];

        if (Help.IsNodeUnderXsAny(profileHeader, null))
            return true;
        string value;
        CheckProfileIdentification(profileHeader);

        CheckProfileRevision(profileHeader);

        CheckProfileName(profileHeader);

        CheckProfileSource(profileHeader);

        CheckProfileClassID(profileHeader);

        // ISO15745Reference
        XElement iSo15745References = profileHeader.Element(NamespaceGsdDef + Elements.s_Iso15745Reference);

        // Check the ISO15745Reference/ISO15745Part
        if (iSo15745References == null)
        {
            return true;
        }

        XElement iSo15745Part = iSo15745References.Element(NamespaceGsdDef + Elements.s_Iso15745Part);
        if (iSo15745Part != null)
        {
            value = iSo15745Part.Value;
            if (0 != string.Compare(value, "4", StringComparison.InvariantCulture))
            {
                // "The element 'ProfileHeader/ISO15745Reference/ISO15745Part' must have the value 4.
                // You have given {0}."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002C_6"), value);
                string xpath = "//ISO15745Profile/ProfileHeader/ISO15745Reference/ISO15745Part";
                var xli = (IXmlLineInfo)iSo15745Part;
                Store.CreateAndAnnounceReport(
                    ReportType_0X0001002C,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001002C_6");
            }
        }

        // Check the ISO15745Reference/ISO15745Edition
        XElement iSo15745Edition = iSo15745References.Element(NamespaceGsdDef + Elements.s_Iso15745Edition);
        if (iSo15745Edition != null)
        {
            value = iSo15745Edition.Value;
            if (0 != string.Compare(value, "1", StringComparison.InvariantCulture))
            {
                // "The element 'ProfileHeader/ISO15745Reference/ISO15745Edition' must have the value 1.
                // You have given {0}."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002C_7"), value);
                string xpath = "//ISO15745Profile/ProfileHeader/ISO15745Reference/ISO15745Edition";
                var xli = (IXmlLineInfo)iSo15745Edition;
                Store.CreateAndAnnounceReport(
                    ReportType_0X0001002C,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001002C_7");
            }
        }

        // Check the ISO15745Reference/ProfileTechnology
        XElement profileTechnology = iSo15745References.Element(NamespaceGsdDef + Elements.s_ProfileTechnology);
        value = profileTechnology.Value;
        if (0 != string.Compare(value, "GSDML", StringComparison.InvariantCulture))
        {
            // "The element 'ProfileHeader/ISO15745Reference/ProfileTechnology' must have the value "GSDML".
            // You have given "{0}"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002C_8"), value);
            string xpath = "//ISO15745Profile/ProfileHeader/ISO15745Reference/ProfileTechnology";
            var xli = (IXmlLineInfo)profileTechnology;
            Store.CreateAndAnnounceReport(
                ReportType_0X0001002C,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002C_8");
        }

        return true;
    }

    private void CheckProfileClassID(XElement profileHeader)
    {
        const bool IgnoreCase = false;
        // Check the ProfileClassID
        XElement profileClassId = profileHeader.Element(NamespaceGsdDef + Elements.s_ProfileClassId);
        if (profileClassId == null)
        {
            return;
        }

        string value = profileClassId.Value;
        if (0 == string.Compare(value, "Device", IgnoreCase, CultureInfo.InvariantCulture))
        {
            return;
        }

        // "The element 'ProfileHeader/ProfileClassID' must have the value "Device".
        // You have given "{0}"."
        string msg = string.Format(
            (IFormatProvider)CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x0001002C_5"),
            (object)value);
        string xpath = "//ISO15745Profile/ProfileHeader/ProfileClassID";
        var xli = (IXmlLineInfo)profileClassId;
        Store.CreateAndAnnounceReport(
            ReportType_0X0001002C,
            xli.LineNumber,
            xli.LinePosition,
            msg,
            xpath,
            ReportCategories.TypeSpecific,
            "0x0001002C_5");
    }

    private void CheckProfileSource(XElement profileHeader)
    {
        const bool IgnoreCase = false;
        // Check the ProfileSource
        XElement profileSource = profileHeader.Element(NamespaceGsdDef + Elements.s_ProfileSource);
        if (profileSource == null)
        {
            return;
        }

        string value = profileSource.Value;
        if (0 == string.Compare(
                value,
                "PROFIBUS Nutzerorganisation e. V. (PNO)",
                IgnoreCase,
                CultureInfo.InvariantCulture))
        {
            return;
        }

        // "The element 'ProfileHeader/ProfileSource' must have the value "PROFIBUS Nutzerorganisation e. V. (PNO)".
        // You have given "{0}"."
        string msg = string.Format(
            (IFormatProvider)CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x0001002C_4"),
            (object)value);
        string xpath = "//ISO15745Profile/ProfileHeader/ProfileSource";
        var xli = (IXmlLineInfo)profileSource;
        Store.CreateAndAnnounceReport(
            ReportType_0X0001002C,
            xli.LineNumber,
            xli.LinePosition,
            msg,
            xpath,
            ReportCategories.TypeSpecific,
            "0x0001002C_4");
    }

    private void CheckProfileName(XElement profileHeader)
    {
        const bool IgnoreCase = false;
        // Check the ProfileName
        XElement profileName = profileHeader.Element(NamespaceGsdDef + Elements.s_ProfileName);
        if (profileName == null)
        {
            return;
        }

        string value = profileName.Value;
        if (0 == string.Compare(value, "Device Profile for PROFINET Devices", IgnoreCase, CultureInfo.InvariantCulture))
        {
            return;
        }

        // "The element 'ProfileHeader/ProfileName' must have the value "Device Profile for PROFINET Devices".
        // You have given "{0}"."
        string msg = string.Format(
            (IFormatProvider)CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x0001002C_3"),
            (object)value);
        string xpath = "//ISO15745Profile/ProfileHeader/ProfileName";
        var xli = (IXmlLineInfo)profileName;
        Store.CreateAndAnnounceReport(
            ReportType_0X0001002C,
            xli.LineNumber,
            xli.LinePosition,
            msg,
            xpath,
            ReportCategories.TypeSpecific,
            "0x0001002C_3");
    }

    private void CheckProfileRevision(XElement profileHeader)
    {
        // Check the ProfileRevision
        XElement profileRevision = profileHeader.Element(NamespaceGsdDef + Elements.s_ProfileRevision);
        if (profileRevision == null)
        {
            return;
        }

        string value = profileRevision.Value;
        if (0 == string.Compare(value, "1.00", StringComparison.InvariantCulture))
        {
            return;
        }

        // "The element 'ProfileHeader/ProfileRevision' must have the value "1.00".
        // You have given "{0}"."
        string msg = string.Format(
            (IFormatProvider)CultureInfo.CurrentCulture,
            Help.GetMessageString("M_0x0001002C_2"),
            (object)value);
        string xpath = "//ISO15745Profile/ProfileHeader/ProfileRevision";
        var xli = (IXmlLineInfo)profileRevision;
        Store.CreateAndAnnounceReport(
            ReportType_0X0001002C,
            xli.LineNumber,
            xli.LinePosition,
            msg,
            xpath,
            ReportCategories.TypeSpecific,
            "0x0001002C_2");
    }

    private void CheckProfileIdentification(XElement profileHeader)
    {
        // Check the ProfileIdentification
        XElement profileIdentification = profileHeader.Element(NamespaceGsdDef + Elements.s_ProfileIdentification);
        if (profileIdentification == null)
        {
            return;
        }

        string value = profileIdentification.Value;
        const bool IgnoreCase = false;
        if (0 == string.Compare(value, "PROFINET Device Profile", IgnoreCase, CultureInfo.InvariantCulture))
        {
            return;
        }

        // "The element 'ProfileHeader/ProfileIdentification' must have the value "PROFINET Device Profile".
        // You have given "{0}"."
        string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002C_1"), value);
        string xpath = "//ISO15745Profile/ProfileHeader/ProfileIdentification";
        var xli = (IXmlLineInfo)profileIdentification;
        Store.CreateAndAnnounceReport(
            ReportType_0X0001002C,
            xli.LineNumber,
            xli.LinePosition,
            msg,
            xpath,
            ReportCategories.TypeSpecific,
            "0x0001002C_1");
    }
    /// <summary>
    /// Check number: CN_0x0001002D
    /// As prefix for the schema instance namespace, "xsi" shall be used in the GSDML.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002D()
    {
        //XElement en = Gsd.XPathSelectElement("/gsddef:ISO15745Profile", Nsmgr);
        if (Gsd.Root == null)
        {
            return true;
        }

        foreach (var attribute in Gsd.Root.Attributes())
        {
            string attrValue = attribute.Value;
            if (!attrValue.Contains("XMLSchema-instance"))
            {
                continue;
            }

            string attrName = attribute.Name.LocalName;
            if (string.Equals(attrName, "xsi", StringComparison.InvariantCultureIgnoreCase))
            {
                continue;
            }

            // "As prefix for the schema instance namespace, http://www.w3.org/2001/XMLSchema-instance, "xsi" shall be used in the GSDML."
            string msg = Help.GetMessageString("M_0x0001002D_1");
            string xpath = Help.GetXPath(attribute);
            var xli = (IXmlLineInfo)attribute;
            Store.CreateAndAnnounceReport(
                ReportType_0X0001002D,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002D_1");


        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x0001002E
    /// (1) For ParameterRecordDataItem:
    ///     Checks that the index is in a range between 0 and 0x7FFF (32767)
    ///     or 0xB000 (45056) and 0xBFFF (49151).
    /// (2) For ParameterRecordDataItem:
    ///     Checks that if the index is in a range between 0xB000 (45056) and 0xBFFF (49151),
    ///     the corresponding submodule is defined in an API <> 0.
    /// (3) For F_ParameterRecordDataItem:
    ///     The index must be in a range between 0 and 0x7FFF (32767).
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002E()
    {
        var nl = GsdProfileBody.Descendants().Attributes(Attributes.s_Index);
        nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
        foreach (XAttribute an in nl)
        {
            var lineInfo = (IXmlLineInfo)an;
            UInt16 index = XmlConvert.ToUInt16(an.Value);
            if (an.Parent != null
                && an.Parent.Name.LocalName == Elements.s_ParameterRecordDataItem)
            {
                // (1)
                uint api = CreateReport0x0001002E_1(index, an, lineInfo);



                // (2)
                CreateReport0x0001002E_2(index, api, an, lineInfo);

            }
            else
            {
                // (3)
                CreateReport0x0001002E_3(index, an, lineInfo);


            }
        }

        return true;
    }

    private void CreateReport0x0001002E_3(ushort index, XObject an, IXmlLineInfo lineInfo)
    {
        if (index > 32767)
        {
            // "The 'F_ParameterRecordDataItem/@Index' attribute value (= {0}) must be in a range between 0..32767."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002E_3"), index);
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002E_3");
        }
    }

    private void CreateReport0x0001002E_2(ushort index, uint api, XObject an, IXmlLineInfo lineInfo)
    {
        if (index > 45056
            && index < 49151)
        {
            if (api == 0)
            {
                // "Profile specific records ('ParameterRecordDataItem/@Index' in a range between 0xB000 (45056) and 0xBFFF (49151))
                // may only be used if the corresponding submodule is defined in an 'API' <> 0."
                string msg = Help.GetMessageString("M_0x0001002E_2");
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Warning,
                    lineInfo.LineNumber,
                    lineInfo.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x0001002E_2");
            }
        }
    }

    private uint CreateReport0x0001002E_1(ushort index, XObject an, IXmlLineInfo lineInfo)
    {
        if (index > 32767
            && (index < 45056 || index > 49151))
        {
            // "The 'ParameterRecordDataItem/@Index' attribute value (= {0}) must be in a range between 0..32767 for 'API' == 0 (default)
            // or in a range between 0..32767 or 0xB000..0xBFFF for 'API' <> 0."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x0001002E_1"), index);
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x0001002E_1");
        }

        XElement submoduleItem = an.Parent.Parent.Parent;
        string apiStr = Help.GetAttributeValueFromXElement(submoduleItem, Attributes.s_Api);
        UInt32 api = 0;
        if (!string.IsNullOrEmpty(apiStr))
        {
            api = XmlConvert.ToUInt32(apiStr);
        }

        return api;
    }

    /// <summary>
    /// Check number: CN_0x0001002F
    /// The attribute 'ApplicationProcess/ExternalTextList/Language/@xml:lang' is mandatory.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X0001002F()
    {
        string xp = ".//gsddef:ExternalTextList/gsddef:Language";

        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            string langcode = String.Empty;
            XNamespace ns = Nsmgr.LookupNamespace("xml");
            var langAtt = en.Attribute(ns + "lang");
            if (langAtt != null)
            {
                langcode = Help.CollapseWhitespace(langAtt.Value);
            }

            if (string.IsNullOrEmpty(langcode))
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The attribute 'ApplicationProcess/ExternalTextList/Language/@xml:lang' is mandatory."
                    string msg = Help.GetMessageString("M_0x0001002F_1");
                    string xpath = "//Language";
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x0001002F_1");
                }
            }
        }

        return true;
    }

    protected virtual void CheckReductionRatioValues(XAttribute an)
    {
        // Range
        uint max = 512;
        uint min = 1;

        var xli = (IXmlLineInfo)an;

        List<uint> allReductionRatios = new List<uint>
                                            {
                                                1,
                                                2,
                                                4,
                                                8,
                                                16,
                                                32,
                                                64,
                                                128,
                                                256,
                                                512
                                            };

        // Split incoming attribute value string to individual numbers and areas in a list.
        List<ValueListHelper.ValueRangeT> lattr = ValueListHelper.NormalizeValueList(an, Store);

        for (int CurrentRange = 0; CurrentRange < lattr.Count; CurrentRange++)
        {
            uint uiFrom = lattr[CurrentRange].From;
            uint uiTo = lattr[CurrentRange].To;
            if (uiFrom < min
                || uiTo > max)
            {
                CreateReport0x00010030_1(an, xli);

            }
            else if (uiFrom == uiTo
                     && !allReductionRatios.Contains(uiFrom))
            {
                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                {
                    // "The value {0} contained within the '(RT_Class3)TimingProperties/@{1}' attribute is not a power of 2."
                    string msg = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00010030_2"),
                        uiFrom,
                        an.Name.LocalName);
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(
                        ReportType_0X000100302,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010030_2");
                }
            }
            else if (uiFrom != uiTo)
            {
                CreateReport0x00010030_2(an, lattr, CurrentRange, allReductionRatios, xli);

            }
        }
    }


    private void CreateReport0x00010030_2(
        XAttribute an,
        IReadOnlyList<ValueListHelper.ValueRangeT> lattr,
        int CurrentRange,
        ICollection<uint> allReductionRatios,
        IXmlLineInfo xli)
    {
        for (uint ui = lattr[CurrentRange].From; ui <= lattr[CurrentRange].To; ui++)
        {
            if (!allReductionRatios.Contains(ui))
            {
                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                {
                    // "The value {0} contained within the '(RT_Class3)TimingProperties/@{1}' attribute is not a power of 2."
                    string msg = String.Format(
                        CultureInfo.CurrentCulture,
                        Help.GetMessageString("M_0x00010030_2"),
                        ui,
                        an.Name.LocalName);
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(
                        ReportType_0X000100302,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00010030_2");
                }

                break;
            }
        }
    }

    private void CreateReport0x00010030_1(XAttribute an, IXmlLineInfo xli)
    {
        if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
        {
            // "Values contained within the '(RT_Class3)TimingProperties/@{0}' attribute
            // must be higher or equal than 1 and lower or equal than 512."
            string msg = String.Format(
                CultureInfo.CurrentCulture,
                Help.GetMessageString("M_0x00010030_1"),
                an.Name.LocalName);
            string xpath = Help.GetXPath(an);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010030_1");
        }
    }
        /// <summary>
        /// Check number: CN_0x00010030
        /// Values contained within the '(RT_Class3)TimingProperties/@{0}' attribute
        /// must be higher or equal than 1 and lower or equal than 512.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010030()
    {
        bool succeeded = true;

        string xp = ".//gsddef:TimingProperties | //gsddef:RT_Class3TimingProperties";
        string xp1 = "./@ReductionRatio | ./@ReductionRatioPow2 | ./@ReductionRatioNonPow2";

        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var timingProp in nl)
        {
            var nl1 = (IEnumerable)timingProp.XPathEvaluate(xp1, Nsmgr);
            foreach (XAttribute reductionRatio in nl1)
            {
                CheckReductionRatioValues(reductionRatio);
            }
        }

        return succeeded;
    }

    /// <summary>
    /// Check number: CN_0x00010031
    /// 'IOPS_Length' and 'IOCS_Length' must always be 1.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010031()
    {
        string xp = ".//gsddef:VirtualSubmoduleItem/gsddef:IOData[(@IOCS_Length > 1) or (@IOPS_Length > 1)]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "'IOPS_Length' and 'IOCS_Length' must always be 1."
            string msg = Help.GetMessageString("M_0x00010031_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010031_1");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010032
    /// Call CheckUniqueIds.
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCN_0x00010032()
    {
        var nl = Gsd.Descendants(NamespaceGsdDef + Elements.s_ApplicationProcess).FirstOrDefault();
        if (nl != null
            && !Help.IsNodeUnderXsAny(nl, null))
        {
            try
            {
                CheckUniqueIds.Check(nl, Nsmgr, Store);
            }
            catch (Exception e) when (e is ArgumentException || e is ArgumentNullException)
            {
                // CheckUniqueIds assumes that the GSDML file is error-free except for the IDs.
                // However, this cannot be guaranteed.
                return true;
            }
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010033
    /// 'RecordDataList/ParameterRecordDataItem' must contain either a 'Const' or a 'Ref' element.
    /// 'DeviceAccessPointItem/ARVendorBlock/Request' must contain either a 'Const' or a 'Ref' element.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010033()
    {
        string xp = ".//gsddef:RecordDataList/gsddef:ParameterRecordDataItem[not(./gsddef:Const) and not(./gsddef:Ref)]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "'RecordDataList/ParameterRecordDataItem' must contain either a 'Const' or a 'Ref' element."
            string msg = Help.GetMessageString("M_0x00010033_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010033_1");
        }

        xp =
            ".//gsddef:DeviceAccessPointItem/gsddef:ARVendorBlock/gsddef:Request[not(./gsddef:Const) and not(./gsddef:Ref)]";
        nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "'DeviceAccessPointItem/ARVendorBlock/Request' must contain either a 'Const' or a 'Ref' element."
            string msg = Help.GetMessageString("M_0x00010033_2");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010033_2");
        }

        return true;
    }

    /// <summary>
    /// Check number: CN_0x00010034
    /// 'ValueList/ValueItem' must contain a 'Help' and/or 'Assignments' element.
    /// 
    /// </summary>
    /// <returns>True, if no runtime problem occurred.</returns>
    protected virtual bool CheckCn_0X00010034()
    {
        string xp = ".//gsddef:ValueList/gsddef:ValueItem[not(./gsddef:Help) and not(./gsddef:Assignments)]";
        var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
        nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
        foreach (var en in nl)
        {
            // "'ValueList/ValueItem' must contain a 'Help' and/or 'Assignments' element."
            string msg = Help.GetMessageString("M_0x00010034_1");
            string xpath = Help.GetXPath(en);
            var xli = (IXmlLineInfo)en;
            Store.CreateAndAnnounceReport(
                ReportType_0X00010034,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00010034_1");
        }

        return true;
    
}

protected enum SlotUsage
        {
            Allowed,

            Used,

            Fixed
        }

        protected struct SlotInfo : IEquatable<SlotInfo>
        {
            public SlotUsage Usage;
            public XElement ModuleItemRef;

            public bool Equals(SlotInfo other)
            {
                return Usage.Equals(other.Usage) &&
                       ModuleItemRef.Equals(other.ModuleItemRef);
            }
        }
          
        

        protected virtual void CheckSlotUsage(XElement moduleItemRef)
        {
            CheckSlotUsage_AllowedInSlots(moduleItemRef);
            CheckSlotUsage_UsedInSlots(moduleItemRef);
            CheckSlotUsage_FixedInSlots(moduleItemRef);
          
        }
        private void CheckSlotUsage_FixedInSlots(XElement moduleItemRef)
        {
            string attribute;
            if (moduleItemRef.Name.LocalName == Elements.s_ModuleItemRef)
                attribute = Attributes.s_FixedInSlots;
            else
                attribute = Attributes.s_FixedInSubslots;
            XAttribute attrFixed = moduleItemRef.Attribute(attribute);
            List<ValueListHelper.ValueRangeT> fixedSlotList = ValueListHelper.NormalizeValueList(attrFixed, Store);
            for (int currentSlotRange = 0; currentSlotRange < fixedSlotList.Count; currentSlotRange++)
            {
                uint currentSlot = fixedSlotList[currentSlotRange].From;
                uint upperLimit = fixedSlotList[currentSlotRange].To;
                if (upperLimit > 32767)
                    upperLimit = 32767; // Max allowed Slot no
                while (currentSlot <= upperLimit)
                {
                    if (!SlotToSlotUsage.TryGetValue(currentSlot, out SlotInfo slotInfo))
                    {
                        slotInfo = new SlotInfo();
                        slotInfo.Usage = SlotUsage.Fixed;
                        slotInfo.ModuleItemRef = moduleItemRef;
                        SlotToSlotUsage.Add(currentSlot, slotInfo);
                    }
                    else
                    {
                        if (slotInfo.Usage == SlotUsage.Used || slotInfo.Usage == SlotUsage.Fixed)
                        {
                            CreateReport0x00010035_2(moduleItemRef, currentSlot, slotInfo);
                            
                        }
                        else // (slotInfo.Usage == SlotUsage.Allowed)
                        {
                            SlotAsAllowedValue.TryGetValue(currentSlot, out IList<XElement> refsUsingSlotAsAllowed);
                            CreateReport0x00010035_1(moduleItemRef, refsUsingSlotAsAllowed, currentSlot);
                            

                            SlotAsAllowedValue[currentSlot] = new List<XElement>();

                            // Update slotInfo
                            slotInfo.Usage = SlotUsage.Fixed;
                            slotInfo.ModuleItemRef = moduleItemRef;
                            SlotToSlotUsage[currentSlot] = slotInfo;
                        }
                    }
                    currentSlot++;
                }
            }
        }

        private void CreateReport0x00010035_1(XObject moduleItemRef, IList<XElement> refsUsingSlotAsAllowed, uint currentSlot)
        {
            for (int index = 0; refsUsingSlotAsAllowed != null && index < refsUsingSlotAsAllowed.Count; index++)
            {
                if (moduleItemRef != refsUsingSlotAsAllowed[index])
                {
                    // "Slot {0} can't be used for this module, because it is used permanently by '{1}'."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010035_1"), currentSlot, Help.GetXPath(moduleItemRef));
                    string xpath = Help.GetXPath(refsUsingSlotAsAllowed[index]);
                    var xli = (IXmlLineInfo)refsUsingSlotAsAllowed[index];
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010035_1");
                }
            }
        }
        private void CreateReport0x00010035_2(XElement moduleItemRef, uint currentSlot, SlotInfo slotInfo)
        {
            // "Slot {0} can't be used twice. It is already used by '{1}'."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010035_2"), currentSlot, Help.GetXPath(slotInfo.ModuleItemRef));
            string xpath = Help.GetXPath(moduleItemRef);
            var xli = (IXmlLineInfo)moduleItemRef;
            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010035_2");

            if (slotInfo.Usage == SlotUsage.Used)
            {
                // Update slotInfo
                slotInfo.Usage = SlotUsage.Fixed;
                slotInfo.ModuleItemRef = moduleItemRef;
                SlotToSlotUsage[currentSlot] = slotInfo;
            }
        }
        private void CheckSlotUsage_UsedInSlots(XElement moduleItemRef)
        {
            string attribute;
            if (moduleItemRef.Name.LocalName == Elements.s_ModuleItemRef)
                attribute = Attributes.s_UsedInSlots;
            else
                attribute = Attributes.s_UsedInSubslots;
            XAttribute attrUsed = moduleItemRef.Attribute(attribute);
            List<ValueListHelper.ValueRangeT> usedSlotList = ValueListHelper.NormalizeValueList(attrUsed, Store);
            for (int currentSlotRange = 0; currentSlotRange < usedSlotList.Count; currentSlotRange++)
            {
                uint currentSlot = usedSlotList[currentSlotRange].From;
                uint upperLimit = usedSlotList[currentSlotRange].To;
                if (upperLimit > 32767)
                    upperLimit = 32767; // Max allowed Slot no
                while (currentSlot <= upperLimit)
                {
                     
                    if (!SlotToSlotUsage.TryGetValue(currentSlot, out SlotInfo slotInfo))
                    {
                        slotInfo = new SlotInfo();
                        slotInfo.Usage = SlotUsage.Used;
                        slotInfo.ModuleItemRef = moduleItemRef;
                        SlotToSlotUsage.Add(currentSlot, slotInfo);
                    }
                    else
                    {
                        if (slotInfo.Usage == SlotUsage.Used || slotInfo.Usage == SlotUsage.Fixed)
                        {
                            // "Slot {0} can't be used twice. It is already used by '{1}'."
                            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010035_2"), currentSlot, Help.GetXPath(slotInfo.ModuleItemRef));
                            string xpath = Help.GetXPath(moduleItemRef);
                            var xli = (IXmlLineInfo)moduleItemRef;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010035_2");
                        }
                        else // (slotInfo.Usage == SlotUsage.Allowed)
                        {
                            // Update slotInfo
                            slotInfo.Usage = SlotUsage.Used;
                            slotInfo.ModuleItemRef = moduleItemRef;
                            SlotToSlotUsage[currentSlot] = slotInfo;
                        }
                    }

                    currentSlot++;
                }
            }
        }

        private void CheckSlotUsage_AllowedInSlots(XElement moduleItemRef)
        {
            string attribute = string.Empty;
            if (moduleItemRef.Name.LocalName == Elements.s_ModuleItemRef)
                attribute = Attributes.s_AllowedInSlots;
            else
                attribute = Attributes.s_AllowedInSubslots;
            XAttribute attrAllowed = moduleItemRef.Attribute(attribute);
            List<ValueListHelper.ValueRangeT> allowedSlotList = ValueListHelper.NormalizeValueList(attrAllowed, Store);
            for (int currentSlotRange = 0; currentSlotRange < allowedSlotList.Count; currentSlotRange++)
            {
                uint currentSlot = allowedSlotList[currentSlotRange].From;
                uint upperLimit = allowedSlotList[currentSlotRange].To;
                if (upperLimit > 32767)
                    upperLimit = 32767; // Max allowed Slot no
                while (currentSlot <= upperLimit)
                {
                    
                    if (!SlotAsAllowedValue.TryGetValue(currentSlot, out IList<XElement> refsUsingSlotAsAllowed))
                    {
                        refsUsingSlotAsAllowed = new List<XElement>();
                        refsUsingSlotAsAllowed.Add(moduleItemRef);
                        SlotAsAllowedValue.Add(currentSlot, refsUsingSlotAsAllowed);
                    }
                    else
                    {
                        refsUsingSlotAsAllowed.Add(moduleItemRef);
                        SlotAsAllowedValue[currentSlot] = refsUsingSlotAsAllowed;
                    }
                     
                    if (!SlotToSlotUsage.TryGetValue(currentSlot, out SlotInfo slotInfo))
                    {
                        slotInfo = new SlotInfo();
                        slotInfo.Usage = SlotUsage.Allowed;
                        slotInfo.ModuleItemRef = moduleItemRef;
                        SlotToSlotUsage.Add(currentSlot, slotInfo);
                    }
                    else
                    {
                        if (slotInfo.Usage == SlotUsage.Fixed && moduleItemRef != slotInfo.ModuleItemRef)
                        {
                            // "Slot {0} can't be used for this module, because it is used permanently by '{1}'."
                            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00010035_1"), currentSlot, Help.GetXPath(slotInfo.ModuleItemRef));
                            string xpath = Help.GetXPath(moduleItemRef);
                            var xli = (IXmlLineInfo)moduleItemRef;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010035_1");
                        }
                    }

                    currentSlot++;
                }
            }
        }

        /// <summary>
        /// Check number: CN_0x00010035
        /// (1) A UseableModules/ModuleItemRef element on a DAP places the referenced module permanently into
        ///     the slots listed in the FixedInSlots attribute. These slots may therefore not appear in the
        ///     FixedInSlots or UsedInSlots attributes of the other ModuleItemRef elements.
        /// (2) The same is true for the UseableSubmodules/SubmoduleItemRef elements of DAPs or modules,
        ///     just one level deeper.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010035()
        {
            // (1)
            var useableModules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_UseableModules);
            useableModules = Help.TryRemoveXElementsUnderXsAny(useableModules, Nsmgr, Gsd);

            foreach (var useableModule in useableModules)
            {
                var moduleItemRefs = useableModule.Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);

                foreach (var moduleItemRef in moduleItemRefs)
                {
                    CheckSlotUsage(moduleItemRef);
                }

                SlotToSlotUsage.Clear();
                SlotAsAllowedValue.Clear();
            }

            // (2)
            var useableSubmodules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_UseableSubmodules);
            useableSubmodules = Help.TryRemoveXElementsUnderXsAny(useableSubmodules, Nsmgr, Gsd);

            foreach (var useableSubmodule in useableSubmodules)
            {
                var submoduleItemRefs = useableSubmodule.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);

                foreach (var submoduleItemRef in submoduleItemRefs)
                {
                    CheckSlotUsage(submoduleItemRef);
                }

                SlotToSlotUsage.Clear();
                SlotAsAllowedValue.Clear();
            }

            return true;
        }



        /// <summary>
        /// Check number: CN_0x00010043
        /// (1) Checks that slot numbers are in the range between 0 and 0x7FFF (32767).
        /// (2) PhysicalSubslots must be in the range between 0 and 0x8FFF (36863).
        /// (3) Subslot numbers that refer to a PortSubmoduleItem must be in a range between 0x8000 (32768) and 0x8FFF (36863).
        ///     Subslot numbers that refer to a "normal" SubmoduleItem (not PortSubmoduleItem) must be in a range between 0 and 0x7FFF (32767).
        /// (4) The subslot no. 0 is not allowed for submodules.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010043()
        {
            // (1)
            CreateReport0x00010043_3();


            // (2)
            CreateReport0x00010043_4();


            // (3)
            IEnumerable<XAttribute> nl =
                GsdProfileBody.Descendants()
                    .Attributes()
                    .Where(
                        attribute =>
                            attribute.Name.LocalName == Attributes.s_AllowedInSubslots ||
                            attribute.Name.LocalName == Attributes.s_FixedInSubslots ||
                            attribute.Name.LocalName == Attributes.s_UsedInSubslots);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                var submoduleItemRef = an.Parent;
                // This filters out VirtualSubmoduleItem
                if (submoduleItemRef != null && submoduleItemRef.Name.LocalName != Elements.s_SubmoduleItemRef)
                    continue;

                var lineInfo = (IXmlLineInfo)an;

                
                string portSubmoduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggablePortSubmoduleItems.TryGetValue(portSubmoduleTarget, out XElement portSubmodule);

                bool isRefToPort = portSubmodule != null;


                List<ValueListHelper.ValueRangeT> lattr = ValueListHelper.NormalizeValueList(an, Store);

                if (isRefToPort)
                {
                    CreateReport0x00010043_5(lattr, an, lineInfo);
                    
                }
                else
                {
                    CreateReport0x00010043_6(lattr, an, lineInfo);
                   
                }
            }

            // (4)
            CheckSubslotFor0();

            return true;
        }
        private void CreateReport0x00010043_6(IReadOnlyList<ValueListHelper.ValueRangeT> lattr, XObject an, IXmlLineInfo lineInfo)
        {
            for (int currentRange = 0; currentRange < lattr.Count; currentRange++)
            {
                uint uiTo = lattr[currentRange].To;
                if (uiTo > 32767)
                {
                    // "Subslot numbers that refer to a "normal" 'SubmoduleItem' (not 'PortSubmoduleItem') must be in a range between 0 and 0x7FFF (32767)."
                    string msg = Help.GetMessageString("M_0x00010043_6");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00010043_6");
                }
            }
        }
        private void CreateReport0x00010043_5(List<ValueListHelper.ValueRangeT> lattr, XObject an, IXmlLineInfo lineInfo)
        {
            if (ValueListHelper.IsValueInValueList(0x8000, lattr) ||
            ValueListHelper.IsValueInValueList(0x8100, lattr) ||
            ValueListHelper.IsValueInValueList(0x8200, lattr) ||
            ValueListHelper.IsValueInValueList(0x8300, lattr) ||
            ValueListHelper.IsValueInValueList(0x8400, lattr) ||
            ValueListHelper.IsValueInValueList(0x8500, lattr) ||
            ValueListHelper.IsValueInValueList(0x8600, lattr) ||
            ValueListHelper.IsValueInValueList(0x8700, lattr) ||
            ValueListHelper.IsValueInValueList(0x8800, lattr) ||
            ValueListHelper.IsValueInValueList(0x8900, lattr) ||
            ValueListHelper.IsValueInValueList(0x8A00, lattr) ||
            ValueListHelper.IsValueInValueList(0x8B00, lattr) ||
            ValueListHelper.IsValueInValueList(0x8C00, lattr) ||
            ValueListHelper.IsValueInValueList(0x8D00, lattr) ||
            ValueListHelper.IsValueInValueList(0x8E00, lattr) ||
            ValueListHelper.IsValueInValueList(0x8F00, lattr))
            {
                // "Subslot numbers that refer to a 'PortSubmoduleItem' must be in a range between 0x8000 (32768) and 0x8FFF (36863)
                // and different from the subslots reserved for InterfaceSubmoduleItems."
                string msg = Help.GetMessageString("M_0x00010043_5");
                string xpath = Help.GetXPath(an);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00010043_5");
            }
            else
            {
                for (int CurrentRange = 0; CurrentRange < lattr.Count; CurrentRange++)
                {
                    uint uiFrom = lattr[CurrentRange].From;
                    uint uiTo = lattr[CurrentRange].To;
                    if (uiFrom < 32768 || uiTo > 36863)
                    {
                        // "Subslot numbers that refer to a 'PortSubmoduleItem' must be in a range between 0x8000 (32768) and 0x8FFF (36863)
                        // and different from the subslots reserved for InterfaceSubmoduleItems."
                        string msg = Help.GetMessageString("M_0x00010043_5");
                        string xpath = Help.GetXPath(an);
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00010043_5");
                    }
                }
            }
        }
        private void CreateReport0x00010043_4()
        {
            IEnumerable<XAttribute> nl = GsdProfileBody.Descendants().Attributes(Attributes.s_PhysicalSubslots);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                List<ValueListHelper.ValueRangeT> lattr = ValueListHelper.NormalizeValueList(an, Store);

                if (ValueListHelper.IsValueInValueList(0x8000, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8100, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8200, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8300, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8400, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8500, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8600, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8700, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8800, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8900, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8A00, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8B00, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8C00, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8D00, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8E00, lattr) ||
                    ValueListHelper.IsValueInValueList(0x8F00, lattr))
                {
                    // "Values for 'PhysicalSubslots' must be in a range between 0 and 0x8FFF (36863)
                    // and different from the subslots reserved for InterfaceSubmoduleItems."
                    string msg = Help.GetMessageString("M_0x00010043_4");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00010043_4");
                }
                for (int currentRange = 0; currentRange < lattr.Count; currentRange++)
                {
                    uint uiTo = lattr[currentRange].To;
                    if (uiTo > 36863)
                    {
                        // "Values for 'PhysicalSubslots' must be in a range between 0 and 0x8FFF (36863)
                        // and different from the subslots reserved for InterfaceSubmoduleItems."
                        string msg = Help.GetMessageString("M_0x00010043_4");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00010043_4");
                    }
                }
            }
        }
        private void CreateReport0x00010043_3()
        {
            var nl =
            GsdProfileBody.Descendants()
                .Attributes()
                .Where(
                    attribute =>
                        attribute.Name.LocalName == Attributes.s_PhysicalSlots ||
                        attribute.Name.LocalName == Attributes.s_AllowedInSlots ||
                        attribute.Name.LocalName == Attributes.s_FixedInSlots ||
                        attribute.Name.LocalName == Attributes.s_UsedInSlots);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                List<ValueListHelper.ValueRangeT> lattr = ValueListHelper.NormalizeValueList(an, Store);
                // ValueListHelper.NormalizeValueList() offers a sorted list
                uint uiTo = 0;
                if (lattr.Count > 0)
                    uiTo = lattr[lattr.Count - 1].To;
                if (uiTo > 32767)
                {
                    // "Slot numbers must be in a range between 0 and 0x7FFF (32767)."
                    string msg = Help.GetMessageString("M_0x00010043_3");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00010043_3");
                }
            }
        }

        protected virtual bool CheckSubslotFor0()
        {
            String subslot0Pattern = @"^(.* )?0+((\.\.| ).*)?$";

            var nl = GsdProfileBody.Descendants().Attributes(Attributes.s_FixedInSubslots);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                Match subslot0Match = Regex.Match(an.Value, subslot0Pattern);
                if (subslot0Match.Success)
                {
                    // "The subslot 0 is not allowed."
                    string msg = Help.GetMessageString("M_0x00010043_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00010043_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010044
        /// RQ AP01224009
        /// ./RecordDataList/ParameterRecordDataItem/Const/@Data
        /// must be checked for syntax errors up to and including V2.1 with the following regexp:
        /// (0x[0-9a-fA-F][0-9a-fA-F],)*0x[0-9a-fA-F][0-9a-fA-F].
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010044()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ParameterRecordDataItem)
                        .Elements(NamespaceGsdDef + Elements.s_Const)
                        .Attributes(Attributes.s_Data);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^(0x[0-9a-fA-F][0-9a-fA-F],)*0x[0-9a-fA-F][0-9a-fA-F]$"))
                {
                    // "Wrong format at attribute 'RecordDataList/ParameterRecordDataItem/Const/@Data'.
                    // Format should be like "0x00,0x0a"."
                    string msg = Help.GetMessageString("M_0x00010044_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010044_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010045
        /// RQ AP01254305
        /// ./ProfileBody/DeviceIdentity/@VendorID
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// 0x[0-9a-fA-F]{1,4}.
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010045()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceIdentity).Attributes(Attributes.s_VendorId);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^0x[0-9a-fA-F]{1,4}$"))
                {
                    // "Wrong format at attribute 'ProfileBody/DeviceIdentity/@VendorID'.
                    // Format should be like "0x000a"."
                    string msg = Help.GetMessageString("M_0x00010045_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010045_1");
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010046
        /// RQ AP01254305
        /// ./ProfileBody/DeviceIdentity/@DeviceID
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// 0x[0-9a-fA-F]{1,4}.
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010046()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceIdentity).Attributes(Attributes.s_DeviceId);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^0x[0-9a-fA-F]{1,4}$"))
                {
                    // "Wrong format at attribute 'ProfileBody/DeviceIdentity/@DeviceID'.
                    // Format should be like "0x000a"."
                    string msg = Help.GetMessageString("M_0x00010046_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010046_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010047
        /// RQ AP01254305
        /// ./DeviceAccessPointList/DeviceAccessPointItem/@ModuleIdentNumber
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// 0x[0-9a-fA-F]{1,8}.
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010047()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem).Attributes(Attributes.s_ModuleIdentNumber);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^0x[0-9a-fA-F]{1,8}$"))
                {
                    // "Wrong format at attribute 'DeviceAccessPointList/DeviceAccessPointItem/@ModuleIdentNumber'.
                    // Format should be like "0x0000000a"."
                    string msg = Help.GetMessageString("M_0x00010047_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010047_1");
                }
            }
            return true;
        }

        protected virtual bool DoSchemaCheck_0X00010048(XAttribute an)
        {
            string value = an.Value;
            if (!Regex.IsMatch(value, "^[0-9a-zA-Z]([0-9a-zA-Z\\-]{0,61}[0-9a-zA-Z])?(\\.[0-9a-zA-Z]([0-9a-zA-Z\\-]{0,61}[0-9a-zA-Z])?)*$"))
            {
                // "Wrong format at attribute 'DeviceAccessPointList/DeviceAccessPointItem/@DNS_CompatibleName'.
                // Format should be like "Pno-Example-Dap"."
                string msg = Help.GetMessageString("M_0x00010048_1");
                string xpath = Help.GetXPath(an);
                var lineInfo = (IXmlLineInfo)an;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                ReportCategories.TypeSpecific, "0x00010048_1");
                return false;
            }
            return true;

        }

        /// <summary>
        /// Check number: CN_0x00010048
        /// RQ AP01254305
        /// ./DeviceAccessPointList/DeviceAccessPointItem/@DNS_CompatibleName
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// [0-9a-zA-Z]([0-9a-zA-Z\-]{0,61}[0-9a-zA-Z])?(\.[0-9a-zA-Z]([0-9a-zA-Z\-]{0,61}[0-9a-zA-Z])?)*.
        /// After that a schema check takes effect.
        ///  
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010048()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem).Attributes(Attributes.s_DnsCompatibleName);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                var lineInfo = (IXmlLineInfo)an;

                string value = an.Value;

                if (value.Length > 240)
                {
                    // "The total length of attribute 'DNS_CompatibleName' must be <= 240.".
                    string msg = Help.GetMessageString("M_0x00010048_2");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010048_2");
                }

                if (!DoSchemaCheck_0X00010048(an))
                {
                    return true;
                }

                if (Regex.IsMatch(value, "^port\\-[0-9]{3}(?:$|\\.)") || Regex.IsMatch(value, "^port\\-[0-9]{3}\\-[0-9]{5}(?:$|\\.)"))
                {
                    // "The first label of attribute 'DNS_CompatibleName' must not have the form "port-xyz"
                    // or "port-xyz-abcde" with a, b, c, d, e, x, y, z = 0..9.".
                    string msg = Help.GetMessageString("M_0x00010048_3");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010048_3");
                }

                if (Regex.IsMatch(value, "^[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}$"))
                {
                    // "The attribute 'DNS_CompatibleName' must not have the form "a.b.c.d" with a, b, c, d = 0..999.".
                    string msg = Help.GetMessageString("M_0x00010048_4");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010048_4");
                }
            }
            return true;

        }

        /// <summary>
        /// Check number: CN_0x00010049
        /// RQ AP01254305
        /// ./ModuleList/ModuleItem/@ModuleIdentNumber
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// 0x[0-9a-fA-F]{1,8}.
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00010049()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem).Attributes(Attributes.s_ModuleIdentNumber);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^0x[0-9a-fA-F]{1,8}$"))
                {
                    // "Wrong format at attribute 'ModuleList/ModuleItem/@ModuleIdentNumber'.
                    // Format should be like "0x0000000a"."
                    string msg = Help.GetMessageString("M_0x00010049_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x00010049_1");
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001004A
        /// RQ AP01254305
        /// (//VirtualSubmoduleItem | //SubmoduleItem)[./@SubmoduleIdentNumber]
        /// must be checked for syntax errors with the following regexp up to and including V2.1:
        /// 0x[0-9a-fA-F]{1,8}.
        /// After that a schema check takes effect.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001004A()
        {
            var allubmoduleNums =
                GsdProfileBody.Descendants().Attributes(Attributes.s_SubmoduleIdentNumber)
                    .Where(
                        x =>
                            x.Parent != null && (x.Parent.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Parent.Name.LocalName == Elements.s_SubmoduleItem));
            allubmoduleNums = Help.TryRemoveXAttributesUnderXsAny(allubmoduleNums, Nsmgr, Gsd);
            foreach (XAttribute an in allubmoduleNums)
            {
                string value = an.Value;
                if (!Regex.IsMatch(value, "^0x[0-9a-fA-F]{1,8}$"))
                {
                    // "Wrong format at attribute '(Virtual)SubmoduleItem/@SubmoduleIdentNumber'.
                    // Format should be like "0x0000000a"."
                    string msg = Help.GetMessageString("M_0x0001004A_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                    ReportCategories.TypeSpecific, "0x0001004A_1");

                }
            }
            return true;
        }

#endregion

        /// <summary>
        /// Check number: CN_0x0001004B
        /// TFS #2917794
        /// For GraphicsList/GraphicItem/@GraphicFile it must be checked,
        /// whether this string ends with ".bmp" or with ".ico" (case insensitive).
        /// If yes, issue a warning that the filename shall be entered without filename extension.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001004B()
        {
            // Find all GraphicItem elements
            var GraphicItemList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_GraphicItem);
            GraphicItemList = Help.TryRemoveXElementsUnderXsAny(GraphicItemList, Nsmgr, Gsd);

            foreach (var graphicItem in GraphicItemList)
            {
                // Get GraphicFile attribute
                string graphicFile = Help.GetAttributeValueFromXElement(graphicItem, Attributes.s_GraphicFile); //must

                // Check if ".bmp" or ".ico" is as filename extension at the end of the string
                if (graphicFile.EndsWith(".bmp", StringComparison.InvariantCultureIgnoreCase) ||
                    graphicFile.EndsWith(".ico", StringComparison.InvariantCultureIgnoreCase))
                {
                    // "The attribute 'GraphicFile' on 'GraphicsList/GraphicItem' must contain the filename without file extension."
                    string msg = Help.GetMessageString("M_0x0001004B_1");
                    string xpath = Help.GetXPath(graphicItem);
                    var xli = (IXmlLineInfo)graphicItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x0001004B_1");
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001004C
        /// TFS #3196038
        /// For all Ref elements (in ARVendorBlock/Request, ParameterRecordDataItem, (Profile)UnitDiagItem):
        /// When Changeable="false", but AllowedValues is present and contains more than one value, issue a warning,
        /// that it is useless to specify more than one value when Changeable=false.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001004C()
        {
            // Find all Ref elements
            var refs = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Ref)
                .Where(x => x.Attribute(Attributes.s_Changeable) != null);
            refs = Help.TryRemoveXElementsUnderXsAny(refs, Nsmgr, Gsd);

            CheckAllowedValuesAgainstChangeableFalse(refs);

            return true;
        }

        protected virtual void CheckAllowedValuesAgainstChangeableFalse(IEnumerable<XElement> elementList)
        {
            foreach (var elem in elementList)
            {
                // Get GraphicFile attribute
                bool changeable = XmlConvert.ToBoolean(Help.GetAttributeValueFromXElement(elem, Attributes.s_Changeable));

                if (changeable)
                {
                    continue;
                }
                    string allowedValues = Help.GetAttributeValueFromXElement(elem, Attributes.s_AllowedValues);

                    if (string.IsNullOrEmpty(allowedValues))
                        continue;

                    string[] ValuesOrRanges = allowedValues.Split(" ".ToCharArray());

                    bool multipleValues = false;
                    if (ValuesOrRanges.Count() > 1)
                        multipleValues = true;

                    foreach (string ValueOrRange in ValuesOrRanges)
                    {
                        if (multipleValues)
                            break;

                    if (!ValueOrRange.Contains(".."))
                    {
                        continue;
                    }
                    // Range
                    string[] Values = ValueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                            if (Values[0] != Values[1])
                                multipleValues = true;
                        }


                if (!multipleValues)
                {
                    continue;
                }
                        // "For the attribute 'AllowedValues' it is useless to specify more than one value when the attribute 'Changeable' =false."
                        string msg = Help.GetMessageString("M_0x0001004C_1");
                        string xpath = Help.GetXPath(elem);
                        var xli = (IXmlLineInfo)elem;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x0001004C_1");
                    
                
            }
        }


        /// <summary>
        /// Check number: CN_0x0001004D
        /// TFS #3955561
        /// Check the ValueLists below for exceeding the maximum value according to the actual allowed values and
        /// return only a single error if a value from a ValueList exceeds the allowed value range.
        /// (1)	ResetToFactoryModes Bit1-15 -> 0-32767
        /// (2)	SlotGroups/SlotGroup: SlotList 0-32767
        /// (3)	SupportedSubstitutionModes Unsigned16
        /// (4)	MAUTypes Unsigned16
        /// 
        /// Remark: The attributes handled here are introduced in later GSDML versions, but for simplicity reasons all it is done here.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001004D()
        {
            // (1)
            // Find all ResetToFactoryModes attributes
            var resetToFactoryModesList =
                GsdProfileBody.Descendants().Attributes(Attributes.s_ResetToFactoryModes);
            resetToFactoryModesList = Help.TryRemoveXAttributesUnderXsAny(resetToFactoryModesList, Nsmgr, Gsd);
            foreach (var resetToFactoryModes in resetToFactoryModesList)
            {
                List<ValueListHelper.ValueRangeT> factoryModeList = ValueListHelper.NormalizeValueList(resetToFactoryModes, Store);
                if (ValueListHelper.DoesValueListExceedsMaximum(factoryModeList, 32767))
                {
                    // "The attribute 'ResetToFactoryModes' exceeds the maximum value of "32767"."
                    string msg = Help.GetMessageString("M_0x0001004D_1");
                    string xpath = Help.GetXPath(resetToFactoryModes);
                    var xli = (IXmlLineInfo)resetToFactoryModes;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x0001004D_1");
                }
            }

            // (2)
            // Find all SlotList attributes
            var slotLists =
                GsdProfileBody.Descendants().Attributes(Attributes.s_SlotList)
                    .Where(
                        x =>
                            x.Parent != null && x.Parent.Name.LocalName == Elements.s_SlotGroup);
            slotLists = Help.TryRemoveXAttributesUnderXsAny(slotLists, Nsmgr, Gsd);
            foreach (var slotList in slotLists)
            {
                List<ValueListHelper.ValueRangeT> slotListList = ValueListHelper.NormalizeValueList(slotList, Store);
                if (!ValueListHelper.DoesValueListExceedsMaximum(slotListList, 32767))
                {
                    continue;
                }

                // "The attribute 'SlotGroups/SlotGroup/SlotList' exceeds the maximum value of "32767"."
                    string msg = Help.GetMessageString("M_0x0001004D_2");
                    string xpath = Help.GetXPath(slotList);
                    var xli = (IXmlLineInfo)slotList;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x0001004D_2");
                
            }

            // (3)
            // Find SupportedSubstitutionModes attributes
            var supportedSubstitutionModes =
                GsdProfileBody.Descendants().Attributes(Attributes.s_SupportedSubstitutionModes);
            supportedSubstitutionModes = Help.TryRemoveXAttributesUnderXsAny(supportedSubstitutionModes, Nsmgr, Gsd);
            foreach (var supportedSubstitutionMode in supportedSubstitutionModes)
            {
                List<ValueListHelper.ValueRangeT> supportedSubstitutionModeList = ValueListHelper.NormalizeValueList(supportedSubstitutionMode, Store);
                if (!ValueListHelper.DoesValueListExceedsMaximum(supportedSubstitutionModeList, UInt16.MaxValue))
                {
                    continue;
                }

                // "The attribute 'SupportedSubstitutionModes' exceeds the maximum value of Unsigned16."
                    string msg = Help.GetMessageString("M_0x0001004D_3");
                    string xpath = Help.GetXPath(supportedSubstitutionMode);
                    var xli = (IXmlLineInfo)supportedSubstitutionMode;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x0001004D_3");
                
            }

            // (4)
            // Find all MAUTypes attributes
            var mauTypesList =
                GsdProfileBody.Descendants().Attributes(Attributes.s_MauTypes);
            mauTypesList = Help.TryRemoveXAttributesUnderXsAny(mauTypesList, Nsmgr, Gsd);
            foreach (var mauTypes in mauTypesList)
            {
                List<ValueListHelper.ValueRangeT> singleMAUTypesList = ValueListHelper.NormalizeValueList(mauTypes, Store);
                if (!ValueListHelper.DoesValueListExceedsMaximum(singleMAUTypesList, UInt16.MaxValue))
                {
                    continue;
                }

                // "The attribute 'MAUTypes' exceeds the maximum value of Unsigned16."
                    string msg = Help.GetMessageString("M_0x0001004D_4");
                    string xpath = Help.GetXPath(mauTypes);
                    var xli = (IXmlLineInfo)mauTypes;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x0001004D_4");
                
            }

            return true;
        }

        //########################################################################################
        #region Nested Context Class For Validation

        internal class ValidationContext
        {

#region Initialization & Termination

            public ValidationContext()
            {
                // Initialize the properties
                m_ElementName = "";
                m_Attributes = null;
            }

            public ValidationContext(string elementname)
            {
                // Initialize the properties
                m_ElementName = elementname;
                m_Attributes = null;
            }

#endregion

#region Properties

            // Declaration of the properties
            private string m_ElementName;
            private Hashtable m_Attributes;

            internal string ElementName => m_ElementName;
            internal Hashtable Attributes => m_Attributes;

    #endregion

    #region Methods

    internal virtual bool AddAttribute(string name, string val)
            {
                if (null == m_Attributes)
                    m_Attributes = new Hashtable();

                if (!m_Attributes.ContainsKey(name))
                    m_Attributes.Add(name, val);

                return true;
            }

#endregion

        }

#endregion

        internal class RefComparer : IComparer<XElement>
        {
            public int Compare(XElement e1, XElement e2)
            {
                if (e1 == null)
                    throw new ArgumentNullException(nameof(e1));

                if (e2 == null)
                    throw new ArgumentNullException(nameof(e2));

                int res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_TextId), Help.GetAttributeValueFromXElement(e2, Attributes.s_TextId), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_DataType), Help.GetAttributeValueFromXElement(e2, Attributes.s_DataType), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_Visible), Help.GetAttributeValueFromXElement(e2, Attributes.s_Visible), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_Changeable), Help.GetAttributeValueFromXElement(e2, Attributes.s_Changeable), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_BitLength), Help.GetAttributeValueFromXElement(e2, Attributes.s_BitLength), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_DefaultValue), Help.GetAttributeValueFromXElement(e2, Attributes.s_DefaultValue), StringComparison.InvariantCulture);
                if (res != 0) return res;
                res = string.Compare(Help.GetAttributeValueFromXElement(e1, Attributes.s_AllowedValues), Help.GetAttributeValueFromXElement(e2, Attributes.s_AllowedValues), StringComparison.InvariantCulture);
                return res;
            }
        }

    }
}