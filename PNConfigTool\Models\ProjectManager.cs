using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Windows;
using Microsoft.Win32;
using PNConfigTool.Models;
using PNConfigTool.Utilities;

namespace PNConfigTool.Models
{
    /// <summary>
    /// 项目管理类，负责项目的创建、保存和加载
    /// </summary>
    public class ProjectManager
    {
        private static ProjectManager? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 当前项目配置
        /// </summary>
        public ProjectConfig? CurrentProject { get; private set; }

        /// <summary>
        /// 当前项目文件路径
        /// </summary>
        public string? CurrentProjectFilePath { get; set; }

        /// <summary>
        /// 项目是否已修改
        /// </summary>
        public bool IsProjectModified { get; set; }

        /// <summary>
        /// 获取ProjectManager单例实例
        /// </summary>
        public static ProjectManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ProjectManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private ProjectManager()
        {
            CurrentProject = null;
            CurrentProjectFilePath = null;
            IsProjectModified = false;
        }

        /// <summary>
        /// 创建新项目
        /// </summary>
        /// <param name="projectName">项目名称</param>
        /// <returns>是否创建成功</returns>
        public bool CreateNewProject(string projectName)
        {
            try
            {
                string masterName = "PROFINET Driver";
                string listOfNodesID = $"ListOfNodes_{projectName}";
                string configurationID = $"Configuration_{projectName}";

                CurrentProject = new ProjectConfig
                {
                    // 项目元数据
                    ProjectMetadata = new ProjectMetadata
                    {
                        ProjectName = projectName,
                        CreationDate = DateTime.Now,
                        LastModifiedDate = DateTime.Now,
                        Version = "1.0",
                        CurrentStepStatus = StepStatus.NONE,
                        Description = $"{projectName} PROFINET项目配置"
                    },

                    // ListOfNodes配置
                    ListOfNodesConfiguration = new ListOfNodesConfiguration
                    {
                        ListOfNodesID = listOfNodesID,
                        SchemaVersion = "1.0",
                        PNDriver = new PNDriverConfig
                        {
                            DeviceID = "PN_Driver_1",
                            DeviceName = masterName,
                            DeviceVersion = "v3.1",
                            Interface = new PNDriverInterface
                            {
                                InterfaceID = "PN_Driver_1_Interface",
                                InterfaceName = "PN_Driver_1_Interface",
                                InterfaceType = "Custom",
                                CustomInterfacePath = ""
                            }
                        },
                        DecentralDevices = new List<DecentralDeviceNode>()
                    },

                    // Configuration配置
                    ConfigurationSettings = new ConfigurationSettings
                    {
                        ConfigurationID = configurationID,
                        ConfigurationName = projectName,
                        ListOfNodesRefID = listOfNodesID,
                        SchemaVersion = "1.0",

                        CentralDevice = new CentralDeviceConfig
                        {
                            DeviceRefID = "PN_Driver_1",

                            CentralDeviceInterface = new CentralDeviceInterfaceConfig
                            {
                                InterfaceRefID = "PN_Driver_1_Interface",
                                EthernetAddresses = new EthernetAddressesConfig
                                {
                                    IPProtocol = new IPProtocolConfig
                                    {
                                        SetInTheProject = new SetInTheProjectConfig
                                        {
                                            IPAddress = "***********",
                                            SubnetMask = "*************",
                                            RouterAddress = "*************"
                                        }
                                    },
                                    PROFINETDeviceName = new PROFINETDeviceNameConfig
                                    {
                                        PNDeviceName = masterName.ToLower().Replace(" ", "")
                                    }
                                    // SubnetRefID 和 IOSystemRefID 默认为 null（不使用显式子网配置）
                                }
                            },
                            AdvancedConfiguration = new AdvancedConfigurationConfig
                            {
                                RealTimeSettings = new RealTimeSettingsConfig
                                {
                                    IOCommunication = new IOCommunicationConfig
                                    {
                                        SendClock = 1.0
                                    }
                                }
                            }
                        },
                        DecentralDevices = new List<DecentralDeviceConfig>(),
                        // Subnets 默认为空列表，仅在用户明确需要时才添加
                        Subnets = new List<SubnetConfig>()
                    },

                    // 项目特定扩展
                    ProjectSpecificExtensions = new ProjectSpecificExtensions
                    {
                        MasterRole = "Controller",
                        MasterCustomInterfacePath = "",
                        MasterMrpRole = "Not device in the ring",
                        MrpDomain = "mrpdomain-1",
                        RingPort1 = "Port1",
                        RingPort2 = "Port2",
                        DiagnosticsInterrupts = false
                    },

                    // 输出配置
                    OutputConfiguration = new OutputConfiguration
                    {
                        GenerationStatus = "未生成",
                        CommandLinePath = "",
                        InputParameters = "",
                        OutputDirectory = ""
                    }
                };

                // 创建项目文件夹的逻辑将在保存项目时处理
                // 此时仅创建项目对象，不设置文件路径
                CurrentProjectFilePath = null;
                IsProjectModified = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
        
        /// <summary>
        /// 创建项目目录
        /// </summary>
        /// <param name="projectPath">项目文件路径</param>
        /// <returns>项目目录路径</returns>
        private string CreateProjectDirectory(string projectPath)
        {
            try
            {
                if (string.IsNullOrEmpty(projectPath))
                {
                    throw new ArgumentNullException(nameof(projectPath));
                }

                System.Diagnostics.Debug.WriteLine($"创建项目目录，输入路径: {projectPath}");
                
                string projectName = Path.GetFileNameWithoutExtension(projectPath);
                string? parentDirectory = Path.GetDirectoryName(projectPath);
                
                System.Diagnostics.Debug.WriteLine($"项目名称: {projectName}, 父目录: {parentDirectory}");
                
                if (string.IsNullOrEmpty(parentDirectory))
                {
                    throw new InvalidOperationException("无法获取父目录路径");
                }
                
                // 检查父目录名称是否已经是项目名称，避免重复创建嵌套目录
                string parentFolderName = Path.GetFileName(parentDirectory);
                
                System.Diagnostics.Debug.WriteLine($"父目录名称: {parentFolderName}");
                
                // 如果父目录已经是项目名称，直接返回父目录
                if (string.Equals(parentFolderName, projectName, StringComparison.OrdinalIgnoreCase))
                {
                    System.Diagnostics.Debug.WriteLine($"父目录已经是项目名称，直接返回: {parentDirectory}");
                    return parentDirectory;
                }
                
                // 否则创建新的项目目录
                string projectDirectory = Path.Combine(parentDirectory, projectName);
                
                System.Diagnostics.Debug.WriteLine($"需要创建新的项目目录: {projectDirectory}");
                
                // 创建项目目录
                if (!Directory.Exists(projectDirectory))
                {
                    Directory.CreateDirectory(projectDirectory);
                    System.Diagnostics.Debug.WriteLine($"已创建项目目录: {projectDirectory}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"项目目录已存在: {projectDirectory}");
                }
                
                return projectDirectory;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建项目目录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"创建项目目录失败: {ex.Message}");
                string? fallbackDir = Path.GetDirectoryName(projectPath);
                return fallbackDir ?? string.Empty;
            }
        }

        /// <summary>
        /// 保存项目
        /// </summary>
        /// <returns>是否保存成功</returns>
        public bool SaveProject()
        {
            if (CurrentProject == null)
            {
                MessageBox.Show("没有打开的项目", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            if (string.IsNullOrEmpty(CurrentProjectFilePath))
            {
                return SaveProjectAs();
            }

            // 先保存项目文件，确保CurrentProjectFilePath正确设置
            bool result = SaveProjectToFile(CurrentProjectFilePath);
            
            // 然后确保项目目录结构完整
            if (result)
            {
                EnsureProjectDirectories();
            }

            return result;
        }

        /// <summary>
        /// 另存为项目
        /// </summary>
        /// <returns>是否保存成功</returns>
        public bool SaveProjectAs()
        {
            if (CurrentProject == null)
            {
                MessageBox.Show("没有打开的项目", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "PROFINET项目配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                Title = "保存项目",
                FileName = CurrentProject.ProjectMetadata.ProjectName
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                return SaveProjectToFile(saveFileDialog.FileName);
            }

            return false;
        }

        /// <summary>
        /// 将项目保存到指定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        private bool SaveProjectToFile(string filePath)
        {
            try
            {
                if (CurrentProject == null) return false;

                System.Diagnostics.Debug.WriteLine($"保存项目到文件: {filePath}");
                
                // 创建项目目录
                string projectDirectory = CreateProjectDirectory(filePath);
                System.Diagnostics.Debug.WriteLine($"项目目录: {projectDirectory}");
                
                // 更新项目文件路径为项目目录中的文件
                string projectFileName = Path.GetFileName(filePath);
                string projectFilePath = Path.Combine(projectDirectory, projectFileName);
                System.Diagnostics.Debug.WriteLine($"项目文件路径: {projectFilePath}");
                
                // 更新最后修改时间
                CurrentProject.ProjectMetadata.LastModifiedDate = DateTime.Now;

                // 保存全局地址分配状态
                try
                {
                    var globalAddressManager = Services.GlobalAddressManager.Instance;
                    globalAddressManager.SaveAddressAllocationToProject();
                    System.Diagnostics.Debug.WriteLine("已保存全局地址分配状态");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存全局地址分配状态时出错: {ex.Message}");
                }

                // 设置项目输出目录路径（但不创建目录）
                string outputDirectory = Path.Combine(projectDirectory, "output");
                CurrentProject.OutputConfiguration.OutputDirectory = outputDirectory;
                System.Diagnostics.Debug.WriteLine($"输出目录路径: {outputDirectory}");

                // 设置XML输出路径为相对路径
                CurrentProject.OutputConfiguration.ConfigurationXmlPath = PathHelper.ToRelativePath(Path.Combine(outputDirectory, "Configuration.xml"), projectDirectory);
                CurrentProject.OutputConfiguration.ListOfNodesXmlPath = PathHelper.ToRelativePath(Path.Combine(outputDirectory, "ListOfNodes.xml"), projectDirectory);

                // 序列化项目配置
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                string jsonString = JsonSerializer.Serialize(CurrentProject, options);
                File.WriteAllText(projectFilePath, jsonString);
                System.Diagnostics.Debug.WriteLine($"已保存项目文件: {projectFilePath}");

                CurrentProjectFilePath = projectFilePath;
                IsProjectModified = false;

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"保存项目失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 打开项目
        /// </summary>
        /// <returns>是否打开成功</returns>
        public bool OpenProject()
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PROFINET项目配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                Title = "打开项目"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                return LoadProjectFromFile(openFileDialog.FileName);
            }

            return false;
        }

        /// <summary>
        /// 从文件加载项目
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否加载成功</returns>
        private bool LoadProjectFromFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("文件不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"开始加载项目文件: {filePath}");
                string jsonString = File.ReadAllText(filePath);

                // 处理可能的版本兼容性问题
                var options = new JsonSerializerOptions
                {
                    ReadCommentHandling = JsonCommentHandling.Skip,
                    AllowTrailingCommas = true,
                    PropertyNameCaseInsensitive = true
                };

                ProjectConfig? loadedProject = JsonSerializer.Deserialize<ProjectConfig>(jsonString, options);

                if (loadedProject == null)
                {
                    MessageBox.Show("项目加载失败，格式无效", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // 验证和修复项目数据完整性
                bool dataIntegrityFixed = ValidateAndFixProjectData(loadedProject);
                if (dataIntegrityFixed)
                {
                    System.Diagnostics.Debug.WriteLine("项目数据完整性已修复");
                }

                CurrentProject = loadedProject;
                CurrentProjectFilePath = filePath;
                IsProjectModified = dataIntegrityFixed; // 如果修复了数据，标记为已修改

                // 确保项目目录结构完整
                EnsureProjectDirectories();

                // 加载全局地址分配状态
                try
                {
                    var globalAddressManager = Services.GlobalAddressManager.Instance;
                    globalAddressManager.LoadAddressAllocationFromProject();
                    System.Diagnostics.Debug.WriteLine("已加载全局地址分配状态");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"加载全局地址分配状态时出错: {ex.Message}");
                }

                // 输出加载结果的调试信息
                LogProjectLoadingResults();

                // 触发项目加载完成事件
                OnProjectLoaded();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载项目失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"加载项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 更新项目状态
        /// </summary>
        /// <param name="status">步骤状态</param>
        public void UpdateProjectStatus(StepStatus status)
        {
            if (CurrentProject != null)
            {
                CurrentProject.ProjectMetadata.CurrentStepStatus = status;
                IsProjectModified = true;
            }
        }

        /// <summary>
        /// 项目加载完成后的处理
        /// </summary>
        private void OnProjectLoaded()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("项目加载完成，开始后处理");

                // 项目加载完成，UI会通过其他机制更新
                // 例如：页面导航时会调用InitializeDevicesList()

                System.Diagnostics.Debug.WriteLine("项目加载后处理完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"项目加载后处理出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证和修复项目数据完整性
        /// </summary>
        /// <param name="project">项目配置</param>
        /// <returns>是否进行了数据修复</returns>
        private bool ValidateAndFixProjectData(ProjectConfig project)
        {
            bool dataFixed = false;

            try
            {
                System.Diagnostics.Debug.WriteLine("开始验证项目数据完整性");

                // 确保基本配置结构存在
                if (project.ProjectMetadata == null)
                {
                    project.ProjectMetadata = new ProjectMetadata();
                    dataFixed = true;
                    System.Diagnostics.Debug.WriteLine("修复：创建缺失的ProjectMetadata");
                }

                if (project.ListOfNodesConfiguration == null)
                {
                    project.ListOfNodesConfiguration = new ListOfNodesConfiguration();
                    dataFixed = true;
                    System.Diagnostics.Debug.WriteLine("修复：创建缺失的ListOfNodesConfiguration");
                }

                if (project.ConfigurationSettings == null)
                {
                    project.ConfigurationSettings = new ConfigurationSettings();
                    dataFixed = true;
                    System.Diagnostics.Debug.WriteLine("修复：创建缺失的ConfigurationSettings");
                }

                // 确保设备列表存在
                if (project.ListOfNodesConfiguration.DecentralDevices == null)
                {
                    project.ListOfNodesConfiguration.DecentralDevices = new List<DecentralDeviceNode>();
                    dataFixed = true;
                    System.Diagnostics.Debug.WriteLine("修复：创建缺失的ListOfNodes.DecentralDevices");
                }

                if (project.ConfigurationSettings.DecentralDevices == null)
                {
                    project.ConfigurationSettings.DecentralDevices = new List<DecentralDeviceConfig>();
                    dataFixed = true;
                    System.Diagnostics.Debug.WriteLine("修复：创建缺失的ConfigurationSettings.DecentralDevices");
                }

                // 验证设备数据一致性
                dataFixed |= ValidateDeviceDataConsistency(project);

                // 确保每个设备都有完整的配置结构
                dataFixed |= EnsureDeviceConfigurationIntegrity(project);

                System.Diagnostics.Debug.WriteLine($"项目数据验证完成，是否修复: {dataFixed}");
                return dataFixed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证项目数据时出错: {ex.Message}");
                return dataFixed;
            }
        }

        /// <summary>
        /// 验证设备数据一致性
        /// </summary>
        /// <param name="project">项目配置</param>
        /// <returns>是否进行了数据修复</returns>
        private bool ValidateDeviceDataConsistency(ProjectConfig project)
        {
            bool dataFixed = false;

            try
            {
                var listOfNodesDevices = project.ListOfNodesConfiguration.DecentralDevices;
                var configurationDevices = project.ConfigurationSettings.DecentralDevices;

                System.Diagnostics.Debug.WriteLine($"ListOfNodes设备数量: {listOfNodesDevices.Count}");
                System.Diagnostics.Debug.WriteLine($"Configuration设备数量: {configurationDevices.Count}");

                // 检查是否有Configuration设备缺少对应的ListOfNodes条目
                // DeviceRefID 应该引用对应的 DeviceID
                foreach (var configDevice in configurationDevices.ToList())
                {
                    var correspondingNode = listOfNodesDevices
                        .FirstOrDefault(n => n.DeviceID == configDevice.DeviceRefID);

                    if (correspondingNode == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：Configuration设备 {configDevice.DeviceRefID} (DeviceRefID: {configDevice.DeviceRefID}) 缺少对应的ListOfNodes条目");
                        // 可以选择创建缺失的节点或移除孤立的配置
                        // 这里我们选择保留配置，但记录警告
                    }
                }

                // 检查是否有ListOfNodes设备缺少对应的Configuration条目
                // Configuration 的 DeviceRefID 应该引用 ListOfNodes 的 DeviceID
                foreach (var nodeDevice in listOfNodesDevices.ToList())
                {
                    var correspondingConfig = configurationDevices
                        .FirstOrDefault(c => c.DeviceRefID == nodeDevice.DeviceID);

                    if (correspondingConfig == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：ListOfNodes设备 {nodeDevice.DeviceName} (DeviceID: {nodeDevice.DeviceID}) 缺少对应的Configuration条目");
                        // 可以选择创建缺失的配置或移除孤立的节点
                        // 这里我们选择保留节点，但记录警告
                    }
                }

                return dataFixed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证设备数据一致性时出错: {ex.Message}");
                return dataFixed;
            }
        }

        /// <summary>
        /// 确保设备配置完整性
        /// </summary>
        /// <param name="project">项目配置</param>
        /// <returns>是否进行了数据修复</returns>
        private bool EnsureDeviceConfigurationIntegrity(ProjectConfig project)
        {
            bool dataFixed = false;

            try
            {
                foreach (var device in project.ConfigurationSettings.DecentralDevices)
                {
                    // General配置已移除，不再需要修复

                    // 确保DecentralDeviceInterface配置存在
                    if (device.DecentralDeviceInterface == null)
                    {
                        device.DecentralDeviceInterface = new DecentralDeviceInterfaceConfig();
                        dataFixed = true;
                        System.Diagnostics.Debug.WriteLine($"修复：为设备 {device.DeviceRefID} 创建缺失的DecentralDeviceInterface配置");
                    }

                    // 确保模块列表存在
                    if (device.Modules == null)
                    {
                        device.Modules = new List<ModuleConfig>();
                        dataFixed = true;
                        System.Diagnostics.Debug.WriteLine($"修复：为设备 {device.DeviceRefID} 创建缺失的Modules列表");
                    }
                }

                return dataFixed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保设备配置完整性时出错: {ex.Message}");
                return dataFixed;
            }
        }

        /// <summary>
        /// 输出项目加载结果的调试信息
        /// </summary>
        private void LogProjectLoadingResults()
        {
            if (CurrentProject == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine("=== 项目加载结果 ===");
                System.Diagnostics.Debug.WriteLine($"项目名称: {CurrentProject.ProjectMetadata.ProjectName}");
                System.Diagnostics.Debug.WriteLine($"项目版本: {CurrentProject.ProjectMetadata.Version}");
                System.Diagnostics.Debug.WriteLine($"创建时间: {CurrentProject.ProjectMetadata.CreationDate}");
                System.Diagnostics.Debug.WriteLine($"最后修改: {CurrentProject.ProjectMetadata.LastModifiedDate}");

                System.Diagnostics.Debug.WriteLine($"ListOfNodes设备数量: {CurrentProject.ListOfNodesConfiguration.DecentralDevices.Count}");
                foreach (var device in CurrentProject.ListOfNodesConfiguration.DecentralDevices)
                {
                    System.Diagnostics.Debug.WriteLine($"  - ListOfNodes设备: {device.DeviceName} (ID: {device.DeviceID})");
                }

                System.Diagnostics.Debug.WriteLine($"Configuration设备数量: {CurrentProject.ConfigurationSettings.DecentralDevices.Count}");
                foreach (var device in CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    System.Diagnostics.Debug.WriteLine($"  - Configuration设备: {device.DeviceRefID} (RefID: {device.DeviceRefID}), 模块数量: {device.Modules?.Count ?? 0}");
                }

                System.Diagnostics.Debug.WriteLine("=== 项目加载结果结束 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"输出项目加载结果时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保项目目录结构完整
        /// </summary>
        private void EnsureProjectDirectories()
        {
            if (CurrentProject == null || string.IsNullOrEmpty(CurrentProjectFilePath)) return;

            // 获取项目目录路径
            string? projectDirectory = Path.GetDirectoryName(CurrentProjectFilePath);
            if (string.IsNullOrEmpty(projectDirectory)) return;

            System.Diagnostics.Debug.WriteLine($"确保项目目录结构完整: {projectDirectory}");

            // 创建输出目录
            if (!Directory.Exists(CurrentProject.OutputConfiguration.OutputDirectory))
            {
                Directory.CreateDirectory(CurrentProject.OutputConfiguration.OutputDirectory);
                System.Diagnostics.Debug.WriteLine($"已创建输出目录: {CurrentProject.OutputConfiguration.OutputDirectory}");
            }

            // 创建GSDMLs文件夹
            string gsdmlsDirectory = Path.Combine(projectDirectory, "GSDMLs");
            if (!Directory.Exists(gsdmlsDirectory))
            {
                Directory.CreateDirectory(gsdmlsDirectory);
                System.Diagnostics.Debug.WriteLine($"已创建GSDMLs文件夹: {gsdmlsDirectory}");
            }

            // 自动部署程序内置GSDML文件
            DeployProgramGSDMLFiles(gsdmlsDirectory);
        }
        
        /// <summary>
        /// 添加分布式设备
        /// </summary>
        /// <param name="deviceNode">分布式设备节点配置</param>
        /// <param name="deviceConfig">分布式设备配置</param>
        /// <returns>是否添加成功</returns>
        public bool AddDecentralDevice(DecentralDeviceNode deviceNode, DecentralDeviceConfig deviceConfig)
        {
            if (CurrentProject == null) return false;

            // 检查设备ID是否已存在
            if (CurrentProject.ListOfNodesConfiguration.DecentralDevices.Exists(d => d.DeviceID == deviceNode.DeviceID))
            {
                MessageBox.Show($"设备ID '{deviceNode.DeviceID}' 已存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            // 添加到ListOfNodes配置
            CurrentProject.ListOfNodesConfiguration.DecentralDevices.Add(deviceNode);

            // 添加到Configuration配置
            CurrentProject.ConfigurationSettings.DecentralDevices.Add(deviceConfig);

            IsProjectModified = true;

            // 如果当前状态在MASTER_CONFIGURED之后，更新状态为DEVICES_ADDED
            if (CurrentProject.ProjectMetadata.CurrentStepStatus >= StepStatus.MASTER_CONFIGURED)
            {
                CurrentProject.ProjectMetadata.CurrentStepStatus = StepStatus.DEVICES_ADDED;
            }

            return true;
        }
        
        /// <summary>
        /// 移除分布式设备
        /// </summary>
        /// <param name="deviceID">设备ID（来自ListOfNodes的DeviceID）</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveDecentralDevice(string deviceID)
        {
            if (CurrentProject == null) return false;

            // 从ListOfNodes配置中移除（直接使用DeviceID）
            int nodeIndex = CurrentProject.ListOfNodesConfiguration.DecentralDevices.FindIndex(d => d.DeviceID == deviceID);
            if (nodeIndex != -1)
            {
                CurrentProject.ListOfNodesConfiguration.DecentralDevices.RemoveAt(nodeIndex);
            }

            // 从Configuration配置中移除（DeviceRefID应该等于DeviceID）
            int configIndex = CurrentProject.ConfigurationSettings.DecentralDevices.FindIndex(d => d.DeviceRefID == deviceID);
            if (configIndex != -1)
            {
                CurrentProject.ConfigurationSettings.DecentralDevices.RemoveAt(configIndex);
            }

            IsProjectModified = true;
            return nodeIndex != -1 || configIndex != -1;
        }

        /// <summary>
        /// 更新分布式设备
        /// </summary>
        /// <param name="deviceNode">分布式设备节点配置</param>
        /// <param name="deviceConfig">分布式设备配置</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateDecentralDevice(DecentralDeviceNode deviceNode, DecentralDeviceConfig deviceConfig)
        {
            if (CurrentProject == null) return false;

            // 更新ListOfNodes配置
            int nodeIndex = CurrentProject.ListOfNodesConfiguration.DecentralDevices.FindIndex(d => d.DeviceID == deviceNode.DeviceID);
            if (nodeIndex != -1)
            {
                CurrentProject.ListOfNodesConfiguration.DecentralDevices[nodeIndex] = deviceNode;
            }

            // 更新Configuration配置
            int configIndex = CurrentProject.ConfigurationSettings.DecentralDevices.FindIndex(d => d.DeviceRefID == deviceConfig.DeviceRefID);
            if (configIndex != -1)
            {
                CurrentProject.ConfigurationSettings.DecentralDevices[configIndex] = deviceConfig;
            }

            IsProjectModified = true;
            return nodeIndex != -1 || configIndex != -1;
        }

        /// <summary>
        /// 获取分布式设备节点配置
        /// </summary>
        /// <param name="deviceID">设备ID</param>
        /// <returns>设备节点配置，如果不存在则返回null</returns>
        public DecentralDeviceNode? GetDecentralDeviceNode(string deviceID)
        {
            if (CurrentProject == null) return null;

            return CurrentProject.ListOfNodesConfiguration.DecentralDevices.Find(d => d.DeviceID == deviceID);
        }

        /// <summary>
        /// 获取分布式设备配置
        /// </summary>
        /// <param name="deviceID">设备ID（来自ListOfNodes的DeviceID）</param>
        /// <returns>设备配置，如果不存在则返回null</returns>
        public DecentralDeviceConfig? GetDecentralDeviceConfig(string deviceID)
        {
            if (CurrentProject == null) return null;

            // DeviceRefID 应该引用对应的 DeviceID
            return CurrentProject.ConfigurationSettings.DecentralDevices.Find(d => d.DeviceRefID == deviceID);
        }

        /// <summary>
        /// 启用显式子网配置
        /// </summary>
        /// <param name="subnetID">子网ID，默认为"PNIE_1"</param>
        /// <param name="ioSystemID">IO系统ID，默认为"IOSystem1"</param>
        /// <param name="ioSystemNumber">IO系统编号，默认为100</param>
        /// <param name="ioSystemName">IO系统名称，默认为"PROFINET IO-System"</param>
        /// <returns>是否启用成功</returns>
        public bool EnableExplicitSubnetConfiguration(
            string subnetID = "PNIE_1",
            string ioSystemID = "IOSystem1",
            int ioSystemNumber = 100,
            string ioSystemName = "PROFINET IO-System")
        {
            if (CurrentProject == null) return false;

            try
            {
                SubnetConfigManager.EnableExplicitSubnetConfiguration(
                    CurrentProject.ConfigurationSettings,
                    subnetID,
                    ioSystemID,
                    ioSystemNumber,
                    ioSystemName);

                IsProjectModified = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启用子网配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 禁用显式子网配置
        /// </summary>
        /// <returns>是否禁用成功</returns>
        public bool DisableExplicitSubnetConfiguration()
        {
            if (CurrentProject == null) return false;

            try
            {
                SubnetConfigManager.DisableExplicitSubnetConfiguration(CurrentProject.ConfigurationSettings);
                IsProjectModified = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"禁用子网配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 添加同步域配置
        /// </summary>
        /// <param name="subnetID">子网ID</param>
        /// <param name="syncDomainID">同步域ID</param>
        /// <param name="syncDomainName">同步域名称</param>
        /// <param name="bandwidthUse">带宽使用配置</param>
        /// <returns>是否添加成功</returns>
        public bool AddSyncDomain(
            string subnetID,
            string syncDomainID,
            string syncDomainName,
            string bandwidthUse = "Maximum 50% cyclic IO data. Balanced proportion")
        {
            if (CurrentProject == null) return false;

            try
            {
                SubnetConfigManager.AddSyncDomain(
                    CurrentProject.ConfigurationSettings,
                    subnetID,
                    syncDomainID,
                    syncDomainName,
                    bandwidthUse);

                IsProjectModified = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加同步域失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 添加MRP域配置
        /// </summary>
        /// <param name="subnetID">子网ID</param>
        /// <param name="mrpDomainID">MRP域ID</param>
        /// <param name="mrpDomainName">MRP域名称</param>
        /// <returns>是否添加成功</returns>
        public bool AddMrpDomain(
            string subnetID,
            string mrpDomainID,
            string mrpDomainName)
        {
            if (CurrentProject == null) return false;

            try
            {
                SubnetConfigManager.AddMrpDomain(
                    CurrentProject.ConfigurationSettings,
                    subnetID,
                    mrpDomainID,
                    mrpDomainName);

                IsProjectModified = true;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加MRP域失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 检查项目是否启用了显式子网配置
        /// </summary>
        /// <returns>是否启用了显式子网配置</returns>
        public bool HasExplicitSubnetConfiguration()
        {
            return CurrentProject?.ConfigurationSettings.HasExplicitSubnetConfiguration ?? false;
        }

        /// <summary>
        /// 从程序目录部署GSDML文件到项目目录
        /// </summary>
        /// <param name="gsdmlsDirectory">目标GSDML目录</param>
        private void DeployProgramGSDMLFiles(string gsdmlsDirectory)
        {
            try
            {
                // 获取程序运行目录
                string programDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string sourceGSDMLsDirectory = Path.Combine(programDirectory, "Resources", "GSDMLs");

                System.Diagnostics.Debug.WriteLine($"程序目录: {programDirectory}");
                System.Diagnostics.Debug.WriteLine($"源GSDML目录: {sourceGSDMLsDirectory}");

                // 检查源GSDML目录是否存在
                if (!Directory.Exists(sourceGSDMLsDirectory))
                {
                    System.Diagnostics.Debug.WriteLine($"源GSDML目录不存在: {sourceGSDMLsDirectory}");
                    return;
                }

                // 要复制的GSDML文件
                string sourceFileName = "LinuxNative_v3.1_Mrp.xml";
                string sourceFilePath = Path.Combine(sourceGSDMLsDirectory, sourceFileName);
                string targetFilePath = Path.Combine(gsdmlsDirectory, sourceFileName);

                // 检查源文件是否存在
                if (!File.Exists(sourceFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"源GSDML文件不存在: {sourceFilePath}");
                    return;
                }

                // 如果目标文件已存在，跳过复制但仍更新路径
                if (File.Exists(targetFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"GSDML文件已存在，跳过复制: {targetFilePath}");

                    // 更新CustomInterfacePath
                    UpdateCustomInterfacePath(targetFilePath);
                    return;
                }

                // 复制文件从程序目录到项目目录
                File.Copy(sourceFilePath, targetFilePath, overwrite: false);

                System.Diagnostics.Debug.WriteLine($"成功复制GSDML文件: {sourceFilePath} -> {targetFilePath}");

                // 更新CustomInterfacePath
                UpdateCustomInterfacePath(targetFilePath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制GSDML文件失败: {ex.Message}");
                MessageBox.Show($"复制GSDML文件失败: {ex.Message}", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 更新Interface节点的CustomInterfacePath属性
        /// </summary>
        /// <param name="gsdmlFilePath">GSDML文件路径</param>
        private void UpdateCustomInterfacePath(string gsdmlFilePath)
        {
            try
            {
                if (CurrentProject?.ListOfNodesConfiguration?.PNDriver?.Interface != null)
                {
                    // 获取项目目录
                    string projectDirectory = PathHelper.GetProjectDirectory(CurrentProjectFilePath ?? "");

                    // 将绝对路径转换为相对路径
                    string relativePath = PathHelper.ToRelativePath(gsdmlFilePath, projectDirectory);

                    // 设置CustomInterfacePath为相对路径
                    CurrentProject.ListOfNodesConfiguration.PNDriver.Interface.CustomInterfacePath = relativePath;

                    // 同时更新ProjectSpecificExtensions中的MasterCustomInterfacePath
                    if (CurrentProject.ProjectSpecificExtensions != null)
                    {
                        CurrentProject.ProjectSpecificExtensions.MasterCustomInterfacePath = relativePath;
                    }

                    // 标记项目已修改
                    IsProjectModified = true;

                    System.Diagnostics.Debug.WriteLine($"已更新CustomInterfacePath: {gsdmlFilePath} -> {relativePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新CustomInterfacePath失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取CustomInterfacePath的绝对路径
        /// </summary>
        /// <returns>CustomInterfacePath的绝对路径</returns>
        public string GetCustomInterfaceAbsolutePath()
        {
            try
            {
                if (CurrentProject?.ProjectSpecificExtensions == null)
                {
                    return string.Empty;
                }

                string relativePath = CurrentProject.ProjectSpecificExtensions.MasterCustomInterfacePath;
                if (string.IsNullOrEmpty(relativePath))
                {
                    return string.Empty;
                }

                // 获取项目目录
                string projectDirectory = PathHelper.GetProjectDirectory(CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径
                return PathHelper.ToAbsolutePath(relativePath, projectDirectory);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取CustomInterfacePath绝对路径失败: {ex.Message}");
                return string.Empty;
            }
        }
    }
}