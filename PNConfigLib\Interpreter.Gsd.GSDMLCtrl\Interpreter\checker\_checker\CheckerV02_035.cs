﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_035.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.35 and is based on GSD(ML) versions 2.34 and lower.
    ///		
    /// </summary>
    internal class CheckerV02035 : CheckerV02034
    {
        #region Fields

        #endregion


        #region Properties

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02035;
        }


        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02035;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00035001);
            Checks.Add(Constants.s_Cn_0X00035002);
            Checks.Add(Constants.s_Cn_0X00035003);

            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                //Checks.Remove(Constants.CN_0x000XXXXX);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion

        //########################################################################################
        #region Initialization & Termination
        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.34.
        /// </summary>
        public CheckerV02035()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version235);
        }

        #endregion

        

        #region Methods


        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("ChannelProcessAlarmItem", "Reason");
            ElementDescriptions.Add("SystemDefinedChannelProcessAlarmItem", "Reason");
            ElementDescriptions.Add("ProfileChannelProcessAlarmItem", "API,+Reason");
            ElementDescriptions.Add("ExtChannelProcessAlarmItem", "Reason");
            ElementDescriptions.Add("ProfileExtChannelProcessAlarmItem", "API,+Reason");
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = AttributeTokenDictionary["ApplicationClass"];
            tokens1.Add("HighAvailability");
        }


        #endregion


        /// <summary>
        /// This check must be called only if PDEV_CombinedObjectSupported is not present or "false"!
        /// 
        /// For PNIO_Version less than V2.35 the attribute PDEV_CombinedObjectSupported must be present and "true" (amongst other reasons)
        /// if SystemRedundancy is present and DeviceType is 'R1' or 'R2'.
        /// 
        /// Starting with GSDML V2.35 the following must be checked:
        /// If PNIO_Version >= V2.35, PDEV_CombinedObjectSupported must be present and "true"
        /// if SystemRedundancy is present, independent of the value of the attribute DeviceType.
        /// </summary>
        /// <param name="dap"></param>
        /// <returns>none</returns>
        protected override void CheckPDEVCombinedObjectSupportedAgainstSystemRedundancy(XElement dap)
        {
            double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

            if (pnioVersion < 2.35)
            {
                base.CheckPDEVCombinedObjectSupportedAgainstSystemRedundancy(dap);
            }
            else
            {
                var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);
                if (systemRedundancy != null)
                {
                    // "If PNIO_Version >= V2.35, 'PDEV_CombinedObjectSupported' must be set to "true" if 'DeviceAccessPointItem/SystemRedundancy' is present."
                    string msg = Help.GetMessageString("M_0x00030004_4");
                    string xpath = Help.GetXPath(dap);
                    var xli = (IXmlLineInfo)dap;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                ReportCategories.TypeSpecific, "0x00030004_4");
                }
            }
        }

        /// <summary>
        /// Check number: CN_0x00035001
        /// 
        /// The feature MRP Interconnection requires the following checks:
        /// 
        /// (1) This MediaRedundancy/Interconnection contains the two attributes SupportedMRP_InterconnRole and MaxMRP_InterconnInstances.
        ///     It is not allowed that only one of the two attributes is present. Either both must be present or none at all. Else error.
        /// 
        /// (2) If the element Interconnection contains both attributes, at least one of the PortSubmoduleItems configurable with this DAP
        ///     shall support MRP Interconnection (the attribute SupportsMRP_InterconnPortConfig shall be present and "true"). Else minor error.
        ///
        /// Remark: The other direction, i.e. a PortSubmoduleItem with SupportsMRP_InterconnPortConfig must be configurable with a DAP
        ///         that has the element Interconnection with the two attributes, shall NOT be checked.
        ///         This means that a PortSubmodule with that attribute may be configured with any DAP, and therefore there is no need to define
        ///         two PortSubmodules which are identical except for that attribute.
        ///         Further consequence is, that this attribute can be checked normally in the CheckUniqueIds algorithm.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00035001()
        {
            // Find all Interconnection elements
            var interconnectionList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_Interconnection);
            interconnectionList = Help.TryRemoveXElementsUnderXsAny(interconnectionList, Nsmgr, Gsd);
            foreach (var interconnection in interconnectionList)
            {
                // (1)
                string supportedMrpInterconnRole = Help.GetAttributeValueFromXElement(interconnection, Attributes.s_SupportedMrpInterconnRole);
                string maxMrpInterconnInstances = Help.GetAttributeValueFromXElement(interconnection, Attributes.s_MaxMrpInterconnInstances);

                if (CreateReport0x00035001_1(supportedMrpInterconnRole, maxMrpInterconnInstances, interconnection))
                {
                    continue;
                }

                if (string.IsNullOrEmpty(supportedMrpInterconnRole))
                    continue;

                // (2)
                if (interconnection.Parent == null)
                {
                    continue;
                }

                var dap = interconnection.Parent.Parent.Parent.Parent;

                // Get the PortSubmoduleItems plugged or pluggable with this dap
                bool supportsMrpInterconnPortConfig = false;
                if (DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
                {
                    foreach (var portSubmoduleItem in portSubmoduleItems)
                    {
                        string supportsMrpInterconnPortConfigStr = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_SupportsMrpInterconnPortConfig);
                        if (string.IsNullOrEmpty(supportsMrpInterconnPortConfigStr))
                        {
                            continue;
                        }

                        supportsMrpInterconnPortConfig = XmlConvert.ToBoolean(supportsMrpInterconnPortConfigStr);
                        if (supportsMrpInterconnPortConfig)
                            break;
                    }
                }

                CreateReport0x00035001_2(supportsMrpInterconnPortConfig, interconnection);
            }

            return true;
        }

        private void CreateReport0x00035001_2(bool supportsMrpInterconnPortConfig, XObject interconnection)
        {
            if (supportsMrpInterconnPortConfig)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(interconnection, SupportedGsdmlVersion))
            {
                return;
            }

            // "For 'MediaRedundancy/Interconnection' if both attributes 'SupportedMRP_InterconnRole' and 'MaxMRP_InterconnInstances' are present,
            //  at least for one of the PortSubmoduleItems configurable with this DAP the attribute SupportsMRP_InterconnPortConfig must be present and "true"."
            string msg = Help.GetMessageString("M_0x00035001_2");
            string xpath = Help.GetXPath(interconnection);
            var xli = (IXmlLineInfo)interconnection;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_MinorError,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035001_2");
        }

        private bool CreateReport0x00035001_1(
            string supportedMrpInterconnRole,
            string maxMrpInterconnInstances,
            XObject interconnection)
        {
            if ((!string.IsNullOrEmpty(supportedMrpInterconnRole) || string.IsNullOrEmpty(maxMrpInterconnInstances))
                && (string.IsNullOrEmpty(supportedMrpInterconnRole) || !string.IsNullOrEmpty(maxMrpInterconnInstances)))
            {
                return false;
            }

            if (!Help.CheckSchemaVersion(interconnection, SupportedGsdmlVersion))
            {
                return true;
            }

            // "For 'MediaRedundancy/Interconnection' either both attributes 'SupportedMRP_InterconnRole' and 'MaxMRP_InterconnInstances'
            //  must be present or none at all."
            string msg = Help.GetMessageString("M_0x00035001_1");
            string xpath = Help.GetXPath(interconnection);
            var xli = (IXmlLineInfo)interconnection;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035001_1");

            return true;

        }

        /// <summary>
        /// Check number: CN_0x00035002
        /// 
        /// (1) The sum of the lengths of all 'DataItem' elements in a 'ProcessAlarmReasonAddValue' must not exceed 127 byte.
        /// 
        /// (2) Check each user text in 'DataItem' elements if used ids are defined and if the format given in text matches with the data type.
        /// </summary>
        protected virtual bool CheckCN_0x00035002()
        {
            // Find all ProcessAlarmReasonAddValue elements
            var processAlarmReasonAddValueList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ProcessAlarmReasonAddValue);
            processAlarmReasonAddValueList = Help.TryRemoveXElementsUnderXsAny(processAlarmReasonAddValueList, Nsmgr, Gsd);
            foreach (var processAlarmReasonAddValue in processAlarmReasonAddValueList)
            {
                IList<Byte> idList = new List<Byte>();
                IList<string> typeList = new List<string>();

                uint total_length = 0;
                var dataItems = processAlarmReasonAddValue.Elements(NamespaceGsdDef + Elements.s_DataItem);

                // Check all DataItem elements
                foreach (var dataItem in dataItems)
                {
                    string datatype = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                    uint actual_length = Help.GetBitLengthFromDataItemType(datatype) / 8;

                    // Sum of the lengths of all 'DataItem' elements
                    total_length += actual_length;

                    // Collect all DataItem Ids and DataTypes in lists
                    Byte dataItemId = XmlConvert.ToByte(Help.GetAttributeValueFromXElement(dataItem, Attributes.Id));
                    string dataItemType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                    idList.Add(dataItemId);
                    typeList.Add(dataItemType);
                }

                // (1)
                CreateReport0x00035002_1(total_length, processAlarmReasonAddValue);

                // (2)
                // Find the TextId corresponding to the ProcessAlarmReasonAddValue
                var extChannelProcessAlarmItem = processAlarmReasonAddValue.Parent;
                if (extChannelProcessAlarmItem == null)
                {
                    continue;
                }

                var nameElem = extChannelProcessAlarmItem.Element(NamespaceGsdDef + Elements.s_Name);
                string textId = Help.GetAttributeValueFromXElement(nameElem, Attributes.s_TextId);

                // Find all user texts in different languages for the TextId
                var nlLanguages =
                    GsdProfileBody.Descendants()
                        .Where(
                            x =>
                                x.Name.LocalName == Elements.s_PrimaryLanguage ||
                                x.Name.LocalName == Elements.s_Language);
                nlLanguages = Help.TryRemoveXElementsUnderXsAny(nlLanguages, Nsmgr, Gsd);

                foreach (XElement language in nlLanguages)
                {
                    var text = Help.GetElementById(language, "./gsddef:Text", Attributes.s_TextId, textId, Nsmgr);

                    if (text == null)
                        continue;

                    CheckIfUsedIdsAreDefined(text, idList, textId, typeList);
                }
            }

            return true;
        }

        private void CheckIfUsedIdsAreDefined(XElement text, IList<byte> idList, string textId, IList<string> typeList)
        {
            // Check each user text if used ids are defined
            string sText = Help.GetAttributeValueFromXElement(text, Attributes.s_Value);
            int indexInText = 0;
            while (indexInText != -1)
            {
                bool wrongFormat = false;
                string sIdStr = Help.FindIdInText(sText, ref indexInText, out wrongFormat);
                CreateReport0x00035002_4(wrongFormat, sText, text);
                if (String.IsNullOrEmpty(sIdStr)
                    || indexInText == -1)
                {
                    continue;
                }

                if (!idList.Contains(Byte.Parse(sIdStr, CultureInfo.InvariantCulture)))
                {
                    CreateReport0x00035002_2(textId, sIdStr, text);
                }
                else
                {
                    // Check if the format given in text matches with the data type
                    if (sText.Substring(indexInText, 1) != Constants.s_Colon)
                    {
                        continue;
                    }

                    string sFormat = sText.Substring(++indexInText, 1);
                    int indexInIds = idList.IndexOf(Byte.Parse(sIdStr, CultureInfo.InvariantCulture));
                    CreateReport0x00035002_3(typeList, indexInIds, sFormat, textId, sIdStr, text);
                }
            }
        }

        private void CreateReport0x00035002_3(
            IList<string> typeList,
            int indexInIds,
            string sFormat,
            string textId,
            string sIdStr,
            XElement text)
        {
            string dataItemType = typeList[indexInIds];
            if (Help.DoesFormatAndDataTypeMatch(sFormat, dataItemType))
            {
                return;
            }

            // "In the text for text id "{0}" the format "({1}:{2})" does not match to the data type "{3}"."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00035002_3"), textId, sIdStr, sFormat, dataItemType);
            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00035002_3");
            }
        }

        private void CreateReport0x00035002_2(string textId, string sIdStr, XElement text)
        {
            // "In the text for text id "{0}" the used id (= {1}) is not defined as an id in the corresponding data items."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00035002_2"), textId, sIdStr);
            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00035002_2");
            }
        }

        private void CreateReport0x00035002_4(bool wrongFormat, string sText, XElement text)
        {
            if (!wrongFormat)
            {
                return;
            }

            // "In the text "{0}" the format does not match the following expression: "{index[:formatString]}"."
            string expression = "{index[:formatString]}";
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00035002_4"), sText, expression);
            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00035002_4");
            }
        }

        private void CreateReport0x00035002_1(uint totalLength, XObject processAlarmReasonAddValue)
        {
            if (totalLength <= 127)
            {
                return;
            }

            // "The sum of the lengths of all 'DataItem' elements in a 'ProcessAlarmReasonAddValue' must not exceed 127 byte, you have {0} bytes."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00035002_1"), totalLength);
            string xpath = Help.GetXPath(processAlarmReasonAddValue);
            IXmlLineInfo xli = processAlarmReasonAddValue;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035002_1");
        }

        protected override void CheckSystemRedundancyForMarketingRules(XElement dap, IList<string> applicationClasses)
        {
            double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
            var systemRedundancy = dap.Element(NamespaceGsdDef + Elements.s_SystemRedundancy);

            base.CheckSystemRedundancyForMarketingRules(dap, applicationClasses);

            if (pnioVersion >= 2.35 && applicationClasses.Contains("HighAvailability") && systemRedundancy == null)
            {
                // "The element 'SystemRedundancy' must be present at the DAP when 'PNIO_Version' >= "V2.35"
                // and 'CertificationInfo/@ApplicationClass' contains "HighAvailability"."
                string msg = Help.GetMessageString("M_0x00020020_17");
                string xpath = Help.GetXPath(dap);
                var xli = (IXmlLineInfo)dap;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00020020_17");
            }
        }

        protected override void CheckPDEVCombinedObjectSupportedAgainstApplicationClasses(XElement dap, IList<string> applicationClasses)
        {
            double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

            base.CheckPDEVCombinedObjectSupportedAgainstApplicationClasses(dap, applicationClasses);

            if (pnioVersion >= 2.35 && applicationClasses.Contains("HighAvailability"))
            {
                // "'PDEV_CombinedObjectSupported' must be set to "true" if 'DeviceAccessPointItem/@PNIO_Version >= "V2.35"
                // and DeviceAccessPointItem/CertificationInfo[/CertificationInfoExt]/@ApplicationClass contains the token "HighAvailability"."
                string msg = Help.GetMessageString("M_0x00030004_5");
                var xli = (IXmlLineInfo)dap;
                string xpath = Help.GetXPath(dap);
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "0x00030004_5");
            }
        }

        protected override void CheckMediaRedundancyForMarketingRules(XElement dap, XElement interfaceSubmoduleItem, IList<string> applicationClasses)
        {
            double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
            XElement mediaRedundancy = interfaceSubmoduleItem.Element(NamespaceGsdDef + Elements.s_MediaRedundancy);
            int countPorts = 0;
            if (DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
                countPorts = portSubmoduleItems.Count;

            base.CheckMediaRedundancyForMarketingRules(dap, interfaceSubmoduleItem, applicationClasses);

            if (pnioVersion >= 2.35 && mediaRedundancy == null && (applicationClasses.Contains("HighAvailability") || applicationClasses.Contains("ProcessAutomation")) && countPorts > 1)
            {
                // "The element 'InterfaceSubmoduleItem/MediaRedundancy' must be present if there are
                //  at least two port submodules assigned to the interface submodule and 'DeviceAccessPointItem/@PNIO_Version >= "V2.35"
                //  and 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains "HighAvailability" or "ProcessAutomation"."
                string msg = Help.GetMessageString("M_0x00020020_18");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                IXmlLineInfo xli = interfaceSubmoduleItem;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00020020_18");
            }
        }

        /// <summary>
        /// Check number: CN_0x00035003
        /// 
        /// GSDML V2.35 adds a new ApplicationClass "HighAvailability". Some checks are needed here:
        /// (1) The token "HighAvailability" may only appear on DAPs with PNIO_Version >= V2.35, else error.
        /// (2) If the attribute ApplicationClass contains the token "ProcessAutomation",
        ///      it shall not contain the token "HighAvailability", else error.
        /// (3) If PNIO_Version >= V2.35, and if the attribute ApplicationClass contains the token "ProcessAutomation",
        ///     the attribute DeviceAccessSupported shall be present and "true", else error.
        /// (4) If PNIO_Version >= V2.35, the element SystemRedundancy shall be present not only if the attribute
        ///     ApplicationClass contains the token "ProcessAutomation", but also for the token "HighAvailability", else error.
        ///     => See new version of Check_SystemRedundancyForMarketingRules().
        /// (5) If PNIO_Version >= V2.35, the attribute PDEV_CombinedObjectSupported shall be present and "true" not only if the
        ///     attribute ApplicationClass contains the token "ProcessAutomation", but also for the token "HighAvailability", else error.
        ///     => See new version of CheckPDEVCombinedObjectSupportedAgainstApplicationClasses().
        /// (6) If PNIO_Version >= V2.35, the element MediaRedundancy shall be present not only if the attribute ApplicationClass
        ///     contains the token "ProcessAutomation", but also for the token "HighAvailability", else error.
        ///     => See new version of Check_MediaRedundancyForMarketingRules().
        /// (7) If PNIO_Version >= V2.35, the attribute MediaRedundancy/@AdditionalForwardingRulesSupported shall be present and "true"
        ///     if the attribute ApplicationClass contains one of the tokens "ProcessAutomation" and "HighAvailability", else error.

        /// </summary>
        protected virtual bool CheckCN_0x00035003()
        {
            // Find all CertificationInfo elements
            var certificationInfoList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo);
            certificationInfoList = Help.TryRemoveXElementsUnderXsAny(certificationInfoList, Nsmgr, Gsd);
            foreach (var certificationInfo in certificationInfoList)
            {
                var dap = certificationInfo.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                IList<string> applicationClasses = GetCombinedApplicationClasses(dap);

                // (1)
                CreateReport0x00035003_1(applicationClasses, pnioVersion, certificationInfo);

                // (2)
                CreateReport0x00035003_2(applicationClasses, pnioVersion, certificationInfo);

                // (3)
                CreateReport0x00035003_3(pnioVersion, applicationClasses, dap);

                // (4)
                // Check_SystemRedundancyForMarketingRules()

                // (5)
                // CheckPDEVCombinedObjectSupportedAgainstApplicationClasses()

                // (6)
                // Check_MediaRedundancyForMarketingRules()

                // (7)
                CreateReport0x00035003_4(pnioVersion, applicationClasses, dap);

                // (8)
                bool cirSupported = false;
                string cirSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_CirSupported);
                if (!string.IsNullOrEmpty(cirSupportedStr))
                    cirSupported = XmlConvert.ToBoolean(cirSupportedStr);
                CreateReport0x00035003_5(cirSupported, applicationClasses, certificationInfo);
            }

            return true;
        }
        private void CreateReport0x00035003_5(bool cirSupported, ICollection<string> applicationClasses, XObject certificationInfo)
        {
            if (cirSupported || !applicationClasses.Contains("HighAvailability"))
            {
                return;
            }

            // "If the attribute ApplicationClass contains the token "HighAvailability", the attribute "CIR_Supported" must be present and "true"."
            string msg = Help.GetMessageString("M_0x00035003_5");
            string xpath = Help.GetXPath(certificationInfo);
            IXmlLineInfo xli = certificationInfo;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035003_5");
        }

        private void CreateReport0x00035003_4(double pnioVersion, ICollection<string> applicationClasses, XContainer dap)
        {
            if (!(pnioVersion >= 2.35)
                || (!applicationClasses.Contains("ProcessAutomation")
                    && !applicationClasses.Contains("HighAvailability")))
            {
                return;
            }

            var mediaRedundancyList = dap.Descendants(NamespaceGsdDef + Elements.s_MediaRedundancy);
            XElement mediaRedundancy = mediaRedundancyList.ElementAtOrDefault(0);
            bool additionalForwardingRulesSupported = false;
            if (mediaRedundancy == null)
            {
                return;
            }

            string additionalForwardingRulesSupportedStr = Help.GetAttributeValueFromXElement(
                mediaRedundancy,
                Attributes.s_AdditionalForwardingRulesSupported);
            if (!string.IsNullOrEmpty(additionalForwardingRulesSupportedStr))
                additionalForwardingRulesSupported = XmlConvert.ToBoolean(additionalForwardingRulesSupportedStr);
            if (additionalForwardingRulesSupported)
            {
                return;
            }

            // "If PNIO_Version >= V2.35, the attribute 'MediaRedundancy/@AdditionalForwardingRulesSupported' must be present and "true"
            //  if the attribute ApplicationClass contains one of the tokens "ProcessAutomation" and "HighAvailability"."
            string msg = Help.GetMessageString("M_0x00035003_4");
            string xpath = Help.GetXPath(dap);
            IXmlLineInfo xli = dap;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035003_4");
        }

        private void CreateReport0x00035003_3(double pnioVersion, ICollection<string> applicationClasses, XElement dap)
        {
            if (!(pnioVersion >= 2.35)
                || !applicationClasses.Contains("ProcessAutomation"))
            {
                return;
            }

            string deviceAccessSupportedStr =
                Help.GetAttributeValueFromXElement(dap, Attributes.s_DeviceAccessSupported); // must
            bool deviceAccessSupported = XmlConvert.ToBoolean(deviceAccessSupportedStr);
            if (deviceAccessSupported)
            {
                return;
            }

            // "If PNIO_Version >= V2.35, and if the attribute ApplicationClass contains the token "ProcessAutomation",
            //  the attribute DeviceAccessSupported must be present and "true"."
            string msg = Help.GetMessageString("M_0x00035003_3");
            string xpath = Help.GetXPath(dap);
            IXmlLineInfo xli = dap;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035003_3");
        }

        private void CreateReport0x00035003_2(ICollection<string> applicationClasses, double pnioVersion, XObject certificationInfo)
        {
            if (!applicationClasses.Contains("ProcessAutomation")
                || !applicationClasses.Contains("HighAvailability")
                || !(pnioVersion < 2.42))
            {
                return;
            }

            // "If the attribute ApplicationClass contains the token "ProcessAutomation", it must not contain the token "HighAvailability"."
            string msg = Help.GetMessageString("M_0x00035003_2");
            string xpath = Help.GetXPath(certificationInfo);
            IXmlLineInfo xli = certificationInfo;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035003_2");
        }

        private void CreateReport0x00035003_1(ICollection<string> applicationClasses, double pnioVersion, XObject certificationInfo)
        {
            if (!applicationClasses.Contains("HighAvailability")
                || !(pnioVersion < 2.35))
            {
                return;
            }

            // "The ApplicationClass "HighAvailability" must only appear on DAPs with PNIO_Version >= V2.35."
            string msg = Help.GetMessageString("M_0x00035003_1");
            string xpath = Help.GetXPath(certificationInfo);
            IXmlLineInfo xli = certificationInfo;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00035003_1");
        }
        #endregion
    }
}
