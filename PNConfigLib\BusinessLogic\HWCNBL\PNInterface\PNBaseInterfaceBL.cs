/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNBaseInterfaceBL.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNBaseInterfaceBL : HwcnBusinessLogic, IInterfaceBusinessLogic
    {
        public PNBaseInterfaceBL(Interface interfaceSubmodule)
        {
            Interface = interfaceSubmodule;
            Interface.InterfaceBL = this;
            InitBL();
        }

        public Interface Interface { get; }

        public static IInterfaceBusinessLogic InterfaceBLCreator(Interface interfaceSubmodule)
        {
            IInterfaceBusinessLogic interfaceBL = new PNBaseInterfaceBL(interfaceSubmodule);

            foreach (Type decorator in ((InterfaceCatalog)interfaceSubmodule.PCLCatalogObject).DecorationList)
            {
                ConstructorInfo constructorInfo = decorator.GetConstructor(new Type[] { typeof(IInterfaceBusinessLogic) });
                if (constructorInfo != null)
                {
                    interfaceBL = (IInterfaceBusinessLogic)constructorInfo.Invoke(
                        BindingFlags.Default,
                        null,
                        new object[] { interfaceBL },
                        CultureInfo.InvariantCulture);
                }
            }

            return interfaceBL;
        }

        public void Configure(IConfigInterface xmlDeviceInterface)
        {
        }

        public void Configure(IConfigInterface xmlDeviceInterface, SyncDomainType syncDomainType)
        {
        }
        
        public void InitBL()
        {
            InitActions();
            InitAttributes();
            Interface.SetNode();
        }

        private void GenericMethodGetGetBlockIdentifications(IMethodData methodData)
        {
            if (Interface == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            bool identificationRequired =
                Interface.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoIdentificationBlockRequired,
                    ac,
                    false);

            if (!identificationRequired)
            {
                methodData.ReturnValue = false;
                return;
            }

            byte[] blockIdentifications = ConfigUtility.GetBlockIdentifications(Interface);
            //Check if the parameter block is to be generated
            if (blockIdentifications == null)
            {
                methodData.ReturnValue = false;
                return;
            }

            methodData.ReturnValue = blockIdentifications;
        }

        /// <summary>
        /// Creates and returns PDInterfaceAdjust Block.
        /// </summary>
        /// <param name="methodData"></param>
        private void GenericMethodGetPDInterfaceAdjustBlock(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            byte[] prmBlock;
            bool returnValue;
            try
            {
                prmBlock = ConfigUtility.GetPDInterfaceAdjustBlock(Interface, out returnValue);
            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetPDInterfaceAdjustBlock", e);
            }
            
            if (!returnValue
                || (prmBlock == null))
            {
                return;
            }

            methodData.ReturnValue = prmBlock;
        }

        #region Overrides and Overridables

        protected virtual void InitAttributes()
        {
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.TimeSyncRole, 0);
            Interface.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.PnMrpRole,
                (UInt32)PNMrpRole.NotInRing);
            Interface.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)SharedIoAssignment.None);
        }

        private void InitActions()
        {
            Interface.BaseActions.RegisterMethod(GetPDInterfaceAdjust.Name, GenericMethodGetPDInterfaceAdjustBlock);
            Interface.BaseActions.RegisterMethod(GetBlockIdentifications.Name, GenericMethodGetGetBlockIdentifications);

            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodConsistencyCheck);
        }

        private void MethodConsistencyCheck()
        {
            // Consistency Check of PNIPConfig
            // If “NoS via other method” is activated the option “IP-Suite via other method” must also be activated.
            AttributeAccessCode ac = new AttributeAccessCode();
            int pnPNIpConfigModeSupported =
                Interface.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnPnIpConfigModeSupported, ac, 0);
            if (pnPNIpConfigModeSupported != 0)
            {
                DataModel.PCLObjects.Node node = Interface.Node;

                if (node != null)
                {
                    bool isPNPNNoSViaOtherPath =
                        node.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnPnNoSViaOtherPath,
                            ac,
                            false);
                    if (isPNPNNoSViaOtherPath)
                    {
                        bool isPNPNIpSuiteViaOtherPath =
                            node.AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                                ac,
                                false);
                        NodeIPConfiguration ipConfig =
                            (NodeIPConfiguration)
                            node.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, ac, (int)NodeIPConfiguration.Project);

                        if (!isPNPNIpSuiteViaOtherPath
                            && (ipConfig != NodeIPConfiguration.Other))
                        {
                            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Interface, ConsistencyConstants.NoSActiveButIPSuiteNotActive);
                        }
                    }
                }
            }

            // Check if all of the ports are deactivated.
            ConsistencyCheckDeactivatedPort(Interface);
        }

        // Only controller interface consistency checks are done here.
        // Device interface consistency checks are done at PNIoInterfaceDecentralBusinessLogic.
        private void ConsistencyCheckDeactivatedPort(Interface controllerInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule.PNIOC == null)
            {
                return;
            }

            DataModel.PCLObjects.IOSystem ioSystem = controllerInterfaceSubmodule.PNIOC.IOSystem;

            if (ioSystem != null)
            {
                if (ArePortsOfInterfaceDeactivated(controllerInterfaceSubmodule))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Interface, ConsistencyConstants.AllPortsDeactivated,
                        AttributeUtilities.GetName(controllerInterfaceSubmodule.GetDevice()));
                }
            }
        }

        /// <summary>
        /// Checks if there's at least 1 active port of the interface
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <returns>true if there's at least 1 port active, false otherwise.</returns>
        private bool ArePortsOfInterfaceDeactivated(Interface interfaceSubmodule)
        {
            IList<DataModel.PCLObjects.Port> portList = interfaceSubmodule.GetPorts();

            if (portList.Count > 0)
            {
                foreach (DataModel.PCLObjects.Port port in portList)
                {
                    bool isPortDeactivated =
                        port.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnPortDeactivated,
                            new AttributeAccessCode(),
                            false);

                    if (!isPortDeactivated)
                    {
                        return false;
                    }
                }

                return true;
            }

            return false;
        }

        #endregion
    }
}