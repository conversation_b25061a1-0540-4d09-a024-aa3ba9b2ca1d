/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: CompilerConstants.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using System.ComponentModel;

namespace PNConfigLib.Compiler.DataTypes
{
    internal class CompilerConstants
    {
        internal const uint AsRuntimeIdOffsetHwObject = 0x88E10000;

        internal const string HwConfigurationName = "HWConfiguration";

        internal const uint PDPortDataAdjustIndex = 0x802F;

        internal const uint PDPortDataCheckIndex = 0x802B;

        internal const string SubModuleName = "(Sub)Module";

        //Class for attribute (variable) IDs.
        public enum AttributeId
        {
            ModuleProxy = 2,

            DeactivatedConfig = 4,

            IOmapping = 5,

            Ibase = 6,

            Ilength = 7,

            Qbase = 8,

            Qlength = 9,

            LADDR = 10,

            DataRecordsConf = 11,

            NetworkParamConfig = 12,

            IODevParamConfig = 13,

            DataRecordsTransferSequence = 14,

            IOsysParamConfig = 15,

            Link = 16,

            [Description("DataRecordsConf")]
            DataRecordsConfCpu = 17
        }

        //Enum for class (object) Runtime IDs
        public enum ClassRid
        {
            HwConfiguration = 1,

            CentralDevice = 2,

            ControllerInterface = 3,

            ControllerPort = 4,

            IOSystem = 5,

            IODevice = 6,

            ModuleLean = 7,

            ModuleProxy = 8,

            Submodule = 9,

            IOSubmodule = 10,

            NetworkParameters = 11
        }

        public enum Keys
        {
            SlotNumber = 1,

            SubslotNumber = 2,

            StationNumber = 3,

            ModuleProxy = 65520
        }
    }
}