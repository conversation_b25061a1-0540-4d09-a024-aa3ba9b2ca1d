﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: SharedDeviceConfigureManager.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class SharedDeviceConfigureManager
    {
        private Configuration.Configuration m_Configuration
        {
            get;
        }

        internal SharedDeviceConfigureManager(Configuration.Configuration configuration)
        {
            m_Configuration = configuration;
        }

        internal void Configure(IOCycleTypeSharedDevicePart sharedDevice,
           IReadOnlyCollection<SharedDeviceTypeAssignedIOController> sharedDeviceProperties,
           DecentralDevice decentralDevice,
           Configuration.DecentralDeviceType xmlDecentralDevice,
           List<CentralDeviceType> xmlCentralDevices)
        {
            ConfigureSharedDeviceProperties(sharedDevice, sharedDeviceProperties, decentralDevice, xmlDecentralDevice, xmlCentralDevices);
        }

        private void ConfigureSharedDeviceProperties(IOCycleTypeSharedDevicePart sharedDevice,
           IReadOnlyCollection<SharedDeviceTypeAssignedIOController> sharedDeviceProperties
           , DecentralDevice decentralDevice, Configuration.DecentralDeviceType xmlDecentralDevice,
                                                    List<CentralDeviceType> xmlCentralDevices)
        {
            if (sharedDeviceProperties == null || sharedDeviceProperties.Count == 0)
            {
                return;
            }

            Interface deviceInterface = decentralDevice.GetInterface();

            SharedDeviceTypeAssignedIOController xmlCurentAssignment =
                sharedDeviceProperties.FirstOrDefault(
                    x => x.DeviceRefID == deviceInterface.PNIOD.AssignedController.GetDevice().Id);


            SyncRole syncRole = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings
                .Synchronization.SynchronizationRole;
            PNIRTSyncRole mappedSyncRole = AttributeUtilities.MapSyncRoleEnum(syncRole);
            AttributeUtilities.SetSyncRole(deviceInterface, mappedSyncRole);

            SharedIoAssignment headModuleSharedIoAssignment =
                xmlCurentAssignment.SharedModule.Any(sm => sm.ModuleRefID == decentralDevice.Id)
                ? SharedIoAssignment.None : SharedIoAssignment.NotAssigned;

            decentralDevice.AttributeAccess.SetAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)headModuleSharedIoAssignment);

            ConfigureSharedVirtualSubmoduleAssignment(decentralDevice.GetVirtualSubmodules(), xmlDecentralDevice, xmlCurentAssignment, headModuleSharedIoAssignment);
            ConfigureSharedModuleAssignment(decentralDevice.GetModules(), xmlCurentAssignment, decentralDevice, xmlDecentralDevice);

            SharedIoAssignment pdevSharedIoAssignment = xmlCurentAssignment.IsPDEVShared
            ? SharedIoAssignment.None
            : SharedIoAssignment.NotAssigned;

            deviceInterface.AttributeAccess.SetAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)pdevSharedIoAssignment);
            foreach (Port portSubmodule in deviceInterface.GetPorts())
            {
                portSubmodule.AttributeAccess.SetAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                    (UInt32)pdevSharedIoAssignment);
            }

            uint internalAssignedControllerCount = (uint)sharedDeviceProperties.Count;
            uint numberOfAr = (uint)internalAssignedControllerCount - 1
                              + (uint)sharedDevice.IOControllerOutsideProjectWithAccessToThisIODevice;
            Interface decentralDeviceIf = decentralDevice.GetInterface();
            decentralDeviceIf.AttributeAccess.SetAnyAttribute(InternalAttributeNames.PnIoNumberOfAR,
                numberOfAr);

            SharedDeviceTypeAssignedIOController pdevOwnerCentralDevice =
                sharedDeviceProperties.FirstOrDefault(sdp => sdp.IsPDEVShared);

            ConfigureAssignedIOController(pdevOwnerCentralDevice, xmlCentralDevices, decentralDeviceIf, sharedDevice);
        }

        private void ConfigureAssignedIOController(SharedDeviceTypeAssignedIOController pdevOwnerCentralDevice,
            List<CentralDeviceType> xmlCentralDevices,
            Interface decentralDeviceIf,
            IOCycleTypeSharedDevicePart sharedDevice)
        {
            if (pdevOwnerCentralDevice != null)
            {
                if (xmlCentralDevices == null)
                {
                    return;
                }
                CentralDeviceType xmlCentralDevice = xmlCentralDevices
                    .FirstOrDefault(c => c.DeviceRefID == pdevOwnerCentralDevice.DeviceRefID);
                ConfigureIOCommunication(xmlCentralDevice, decentralDeviceIf);
            }
            else if (sharedDevice.IODeviceSendClockSpecified)
            {
                decentralDeviceIf.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoExternalSendClockFactor,
                    (long)sharedDevice.IODeviceSendClock * 32);
            }
        }

        private void ConfigureIOCommunication(CentralDeviceType xmlCentralDevice, IPclObject decentralDeviceIf)
        {
            if (xmlCentralDevice == null)
            {
                return;
            }
            var IoCommunication = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions
                               .RealTimeSettings.IOCommunication;
            if (IoCommunication.SendClockSpecified)
            {
                decentralDeviceIf.AttributeAccess.AddAnyAttribute<long>(
                    InternalAttributeNames.PnIoExternalSendClockFactor,
                    (long)IoCommunication.SendClock * 32);
            }
            else
            {
                float sendClock = m_Configuration.Subnet.FirstOrDefault(
                    s => s.SubnetID == xmlCentralDevice.CentralDeviceInterface.EthernetAddresses
                             .SubnetRefID)
                             .DomainManagement.SyncDomains
                             .FirstOrDefault(s =>
                             s.SyncDomainID == xmlCentralDevice.CentralDeviceInterface
                             .AdvancedOptions.RealTimeSettings.Synchronization.SyncDomainRefID).SendClock;
                decentralDeviceIf.AttributeAccess.AddAnyAttribute<long>(
                    InternalAttributeNames.PnIoExternalSendClockFactor,
                    (long)sendClock * 32);
            }
        }

        private static void ConfigureSharedVirtualSubmoduleAssignment(List<Submodule> virtualSubmodules,
            DecentralDeviceType xmlDecentralDevice,
            SharedDeviceTypeAssignedIOController xmlCurentAssignment,
            SharedIoAssignment headModuleSharedIoAssignment)
        {
            foreach (Submodule submodule in virtualSubmodules)
            {
                bool isFixedIn = submodule.PCLCatalogObject.AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.PnSubslotNumber);
                if (isFixedIn)
                {
                    SharedIoAssignment submoduleSharedIoAssignment =
                        xmlDecentralDevice.Module.Any(
                            w => w.Submodule.Any(
                                t => t.GSDRefID == submodule.Id && xmlCurentAssignment.SharedModule.Any(
                                         sm => sm.ModuleRefID == submodule.ParentObject.Id
                                               && sm.SharedSubmodule.Any(ssm => ssm.SubmoduleRefID == t.SubmoduleID))))
                            ? SharedIoAssignment.None
                            : SharedIoAssignment.NotAssigned;

                    submodule.AttributeAccess.SetAnyAttribute<UInt32>(
                        InternalAttributeNames.SharedIoAssignment,
                        (UInt32)(submoduleSharedIoAssignment));
                }
                else
                {
                    submodule.AttributeAccess.SetAnyAttribute<UInt32>(
                        InternalAttributeNames.SharedIoAssignment,
                        (UInt32)headModuleSharedIoAssignment);
                }
            }

        }

        private static void ConfigureShareModuleFromCatalog(Module module, 
            ModuleType xmlModule,
            SharedDeviceTypeAssignedIOControllerSharedModule xmlSharedModule,
            SharedIoAssignment moduleSharedIoAssignment,
            string gsdFilePath)
        {
            KeyValuePair<string, ModuleCatalog> moduleCatalog = Catalog.ModuleList.FirstOrDefault(
                         m => string.Equals(m.Key, string.Format(CultureInfo.InvariantCulture, @"{0}\{1}", gsdFilePath, xmlModule.GSDRefID), StringComparison.InvariantCultureIgnoreCase));

            if (string.IsNullOrEmpty(moduleCatalog.Key))
            {
                return;
            }

            if (moduleCatalog.Value.PluggableSubmoduleList != null
                && moduleCatalog.Value.PluggableSubmoduleList.Count > 0)
            {
                foreach (PclObject submodule in module.GetElements())
                {
                    SharedIoAssignment submoduleSharedIOAssignment;
                    if ((submodule is Submodule)
                        && ((Submodule)submodule).IsVirtual)
                    {
                        submoduleSharedIOAssignment =
                            xmlSharedModule.SharedSubmodule.Any(
                                sharedSm => sharedSm.SubmoduleRefID == submodule.Id)
                                ? SharedIoAssignment.None
                                : SharedIoAssignment.NotAssigned;
                    }
                    else
                    {
                        ModuleTypeSubmodule xmlSubmodule =
                            xmlModule.Submodule.FirstOrDefault(x => x.GSDRefID == submodule.Id);
                        submoduleSharedIOAssignment =
                            xmlSharedModule.SharedSubmodule.Any(
                                sharedSm => sharedSm.SubmoduleRefID == xmlSubmodule.SubmoduleID)
                                ? SharedIoAssignment.None
                                : SharedIoAssignment.NotAssigned;
                    }
                    submodule.AttributeAccess.SetAnyAttribute<UInt32>(
                        InternalAttributeNames.SharedIoAssignment,
                        (UInt32)submoduleSharedIOAssignment);
                }
            }
            else
            {
                module.GetVirtualSubmodules()
                    .ForEach(
                        vsm => vsm.AttributeAccess.SetAnyAttribute<UInt32>(
                            InternalAttributeNames.SharedIoAssignment,
                            (UInt32)moduleSharedIoAssignment));
            }
        }

        private static void ConfigureSharedModuleAssignment(List<Module> modules,
            SharedDeviceTypeAssignedIOController xmlCurentAssignment,
            IPclObject decentralDevice,
            DecentralDeviceType xmlDecentralDevice)
        {
            foreach (Module module in modules)
            {
                SharedIoAssignment moduleSharedIoAssignment =
                    xmlCurentAssignment.SharedModule.Any(sm => sm.ModuleRefID == module.Id)
                        ? SharedIoAssignment.None
                        : SharedIoAssignment.NotAssigned;
                module.AttributeAccess.SetAnyAttribute<UInt32>(
                    InternalAttributeNames.SharedIoAssignment,
                    (UInt32)moduleSharedIoAssignment);

                string gsdFilePath = decentralDevice.AttributeAccess.GetAnyAttribute<string>(
                                        InternalAttributeNames.GSDFileName,
                                        new AttributeAccessCode(),
                                        string.Empty);

                SharedDeviceTypeAssignedIOControllerSharedModule xmlSharedModule = xmlCurentAssignment.SharedModule
                    .FirstOrDefault(sharedModule => sharedModule.ModuleRefID == module.Id);
                if (xmlSharedModule != null)
                {
                    ModuleType xmlModule =
                        xmlDecentralDevice.Module.FirstOrDefault(m => m.ModuleID == xmlSharedModule.ModuleRefID);

                    if (xmlModule == null)
                    {
                        return;
                    }

                    ConfigureShareModuleFromCatalog(module, xmlModule, xmlSharedModule, moduleSharedIoAssignment, gsdFilePath);
                }
                else
                {
                    module.GetElements()
                        .ToList().ForEach(
                            vsm => vsm.AttributeAccess.SetAnyAttribute<UInt32>(
                                InternalAttributeNames.SharedIoAssignment,
                                (UInt32)SharedIoAssignment.NotAssigned));
                }
            }
        }
    }
}
