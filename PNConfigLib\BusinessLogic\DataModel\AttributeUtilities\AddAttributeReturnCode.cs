/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AddAttributeReturnCode.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// The return code indicating the result of adding an attribute.
    /// </summary>
    public enum AddAttributeReturnCode
    {
        /// <summary>
        /// The attribute is added.
        /// </summary>
        Added = 0,

        /// <summary>
        /// The Add[DataType]Attribute()-call is ignored, since the attribute is already defined with the same properties.
        /// </summary>
        Ignored,

        /// <summary>
        /// The attribute could not be added, since it already exists as a dynamically added attribute.
        /// </summary>
        /// <remarks>
        /// In this case, it is possible to replace the argument by calling RemoveAttribute() and Add[DataType]Attribute() again.
        /// </remarks>
        ExistsDynamic,

        /// <summary>
        /// The attribute could not be added, since it already exists as a fixed attribute, which can not be removed.
        /// </summary>
        ExistsFixed,

        /// <summary>
        /// The attribute could not be added, since the specified attribute set is unknown at this object type.
        /// </summary>
        UnknownAttributeSet,

        /// <summary>
        /// The attribute could not be added, since the specified attribute set is not expando.
        /// </summary>
        NonExpandoAttributeSet,

        /// <summary>
        /// The attribute could not be added, since the specified default value is invalid, e.g. of a wrong data type.
        /// </summary>
        InvalidDefaultValue,

        /// <summary>
        /// The attribute should not be added as Object when a specific implementation exists,ne.g. using AddAnyAttribute to add
        /// any (non-array) type.
        /// </summary>
        InvalidType,

        /// <summary>
        /// The name of the attribute is invalid.
        /// </summary>
        /// <remarks>
        /// This is true for null, String.Empty and reserved values.
        /// </remarks>
        InvalidName,

        /// <summary>
        /// The data type can not be added to the attribute set
        /// and texts are not supported
        /// </summary>
        InvalidTypeForAttributeSet,

        /// <summary>
        /// The adding of an attribute is not supported in the current state; e.g. changing the read-only state of
        /// an attribute  while initializing the config object outside of a create action.
        /// </summary>
        NotSupported
    }
}