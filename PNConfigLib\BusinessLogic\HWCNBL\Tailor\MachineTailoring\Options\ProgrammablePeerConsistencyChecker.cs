/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ProgrammablePeerConsistencyChecker.cs     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.MachineTailoring.Options
{
    /// <summary>
    /// Summary description for ProgrammablePeerConsistencyChecker.
    /// </summary>
    public static class ProgrammablePeerConsistencyChecker
    {
        //########################################################################################

        #region Nested Classes

        // Contains all non-public nested classes and locally scoped interface definitions

        #endregion

        //########################################################################################

        #region Constants and Enums

        // Contains all constants and enums

        #endregion

        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        #endregion

        //########################################################################################

        #region Public Methods

        // Contains all public methods of the class

        /// <summary>
        /// Consistency Check for 'iDevice with central PDEV cannot have any programmable peers'.
        /// </summary>
        /// <param name="port"></param>
        internal static void CheckPdevConsistency(DataModel.PCLObjects.Port port)
        {
            if (port == null)
            {
                return;
            }

            if (!Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(port))
            {
                return;
            }

            DataModel.PCLObjects.Interface pnInterface = NavigationUtilities.GetInterfaceOfPort(port);

            if (pnInterface == null)
            {
                return;
            }

            if (!AttributeUtilities.IsIDevice(pnInterface))
            {
                return;
            }

            if (AttributeUtilities.IsDecentralPDEV(pnInterface))
            {
                return;
            }

            bool iDeviceInMaTailProject = false;
            DataModel.PCLObjects.IOSystem ioSystemOfDevice = NavigationUtilities.GetIoSystem(pnInterface);
            if (ioSystemOfDevice != null)
            {
                List<PNIOD> ioDevicesOfIOSystem = (List<PNIOD>)ioSystemOfDevice.GetParticipants();

                foreach (PNIOD ioDevice in ioDevicesOfIOSystem)
                {
                    PclObject interfaceSubmodule = ioDevice.GetInterface();
                    if (interfaceSubmodule.Equals(pnInterface))
                    {
                        continue;
                    }

                    if (!Utilities.MachineTailor.MachineTailorUtility.IsOptionalDeviceEnabled(interfaceSubmodule)
                        && !Utilities.MachineTailor.MachineTailorUtility.HasInterfaceProgrammablePeer(
                            interfaceSubmodule as DataModel.PCLObjects.Interface))
                    {
                        continue;
                    }

                    iDeviceInMaTailProject = true;
                    break;
                }
            }

            if (!iDeviceInMaTailProject)
            {
                return;
            }

            object[] parameters = new object[2];
            parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(port);
            parameters[1] = AttributeUtilities.GetSubmoduleNameWithContainer(pnInterface);

            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                port,
                ConsistencyConstants.Port_ProgrammablePeer_PdevAssignedToController,
                parameters);
        }

        /// <summary>
        /// Consistency Check for 'Port interconnections of the ring ports cannot be set as Programmable Peer'.
        /// </summary>
        /// <param name="port"></param>
        internal static void CheckProgrammablePeerDeviceMrpRole(DataModel.PCLObjects.Port port)
        {
            if (port == null)
            {
                return;
            }

            if (!Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(port))
            {
                return;
            }

            DataModel.PCLObjects.Interface pnInterface = NavigationUtilities.GetInterfaceOfPort(port);

            if (pnInterface == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            PNMrpRole mrpRole = (PNMrpRole)pnInterface.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole,
                ac.GetNew(),
                (UInt32)PNMrpRole.NotInRing);
            if (mrpRole == PNMrpRole.NotInRing)
            {
                return;
            }

            IMethodData methodData = new MethodData();
            methodData.Name = GetSelectedRingPorts.Name;
            pnInterface.BaseActions.CallMethod(methodData);
            List<DataModel.PCLObjects.Port> selectedPorts =
                (List<DataModel.PCLObjects.Port>)methodData.Arguments[GetSelectedRingPorts.SelectedRingPorts];
            object[] parameters = new object[1];
            // Partner interface submodule does not have any selected ring ports (that shouldn't normally happen)
            if (selectedPorts == null)
            {
                parameters[0] = PortUtility.GetPortName(port);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    port,
                    ConsistencyConstants.Mrp_PortInterconn_NonRingPort,
                    parameters);


                return;
            }

            if (!selectedPorts.Contains(port))
            {
                return;
            }

            parameters[0] = AttributeUtilities.GetSubmoduleNameWithContainer(port);

            ConsistencyLogger.Log(
                ConsistencyType.PN,
                LogSeverity.Error,
                port,
                ConsistencyConstants.Port_ProgrammablePeer_Not_In_Ring,
                parameters);
        }

        #endregion

        //########################################################################################

        #region I... Members

        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        // Contains all public and protected overrides as well as overridables of the class

        #endregion

        //########################################################################################

        #region Protected Methods

        // Contains all protected (non overridables) methods of the class

        #endregion

        //########################################################################################

        #region Private Implementation

        // Contains the private implementation of the class

        #endregion
    }

}