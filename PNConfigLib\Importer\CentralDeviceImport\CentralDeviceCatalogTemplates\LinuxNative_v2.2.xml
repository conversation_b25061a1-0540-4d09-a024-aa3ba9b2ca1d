<?xml version="1.0" encoding="utf-8"?>
<CentralDeviceCatalogObject xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                            xmlns="http://www.siemens.com/Automation/PNConfigLib/ControllerVariant"
                            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                            xsi:schemaLocation="http://www.siemens.com/Automation/PNConfigLib/ControllerVariant ControllerVariant.xsd">
  <AttributeLookup>
    <CatalogAttribute Key="FwVersion">
      <Value xsi:type="xsd:string">v2.2</Value>
    </CatalogAttribute>
    <CatalogAttribute Key="PnModuleIdentNumber">
      <Value xsi:type="xsd:int">45569</Value>
    </CatalogAttribute>
  </AttributeLookup>
  <InterfaceCatalogItems>
    <InterfaceCatalogItem>
      <AttributeLookup>
        <CatalogAttribute Key="PnIoIODDefaultRouterSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeIECheckProtocolPresence">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeIPSubnetMaskUnrestricted">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="NodeDefaultIPAddress">
          <Value xsi:type="xsd:string">***********</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnAllowOverwriteNoISupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnBasicPackageVersion">
          <Value xsi:type="xsd:unsignedInt">1537</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnDCPBoundarySupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnDelayMeasurementSupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAddressTailoring">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAddressTailoringEnabled">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoAllowSlot0WithoutSubmodule1">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoArRecordSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoArStartupMode">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">1</DefaultValue>
            <Value xsi:type="xsd:int">2</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoConfigAlarmCrDataSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoCheckDeviceIDSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoControllerPdevCombinedObjectSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoDataRecordsTransferSeqSupp">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoFastStartupMaxCount">
          <Value xsi:type="xsd:unsignedInt">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoIdentificationBlockRequired">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoIOXSRequired">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMachineTailoring">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMachineTailoringTopologyRequired">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxControllerInputDataLength">
          <Value xsi:type="xsd:long">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxControllerOutputDataLength">
          <Value xsi:type="xsd:long">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDevices">
          <Value xsi:type="xsd:int">128</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDeviceSubmoduleDataLength">
          <Value xsi:type="xsd:int">1024</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxDeviceSubmodules">
          <Value xsi:type="xsd:int">8192</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxFrameStartTime">
          <Value xsi:type="xsd:unsignedInt">1600</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxIrtDevices">
          <Value xsi:type="xsd:int">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxStationNumber">
          <Value xsi:type="xsd:int">2047</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMinMaxWatchdogFactor">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">3</anyType>
              <anyType xsi:type="xsd:int">255</anyType>
            </List>
            <Value xsi:type="xsd:int">3</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMinNRTGap">
          <Value xsi:type="xsd:unsignedInt">960</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMultipleWriteSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoObjectUuidV23Supported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoOperatingMode">
          <Value xsi:type="xsd:unsignedInt">1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoScfAdaptionNonPow2Supported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoScfAdaptionSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSharedDeviceAssignmentSupp">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerDistributionMode">
          <Value xsi:type="xsd:unsignedInt">1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxBytesPerMs">
          <Value xsi:type="xsd:long">5872</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoMaxSharedRTC3Provider">
          <Value xsi:type="xsd:int">64</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxFramesPerMs">
          <Value xsi:type="xsd:long">13</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoPnPlannerMaxRTC12BytesPerMs">
          <Value xsi:type="xsd:long">-1</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppFrameClass">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppSCF12">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">32</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">32</DefaultValue>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppSCF3">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">32</anyType>
            </List>
            <DefaultValue xsi:type="xsd:int">32</DefaultValue>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR12Pow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">4</anyType>
              <anyType xsi:type="xsd:int">8</anyType>
              <anyType xsi:type="xsd:int">16</anyType>
              <anyType xsi:type="xsd:int">32</anyType>
              <anyType xsi:type="xsd:int">64</anyType>
              <anyType xsi:type="xsd:int">128</anyType>
              <anyType xsi:type="xsd:int">256</anyType>
              <anyType xsi:type="xsd:int">512</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR12NonPow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR3Pow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
              <anyType xsi:type="xsd:int">2</anyType>
              <anyType xsi:type="xsd:int">4</anyType>
              <anyType xsi:type="xsd:int">8</anyType>
              <anyType xsi:type="xsd:int">16</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppRR3NonPow2">
          <Value xsi:type="Enumerated">
            <List>
              <anyType xsi:type="xsd:int">1</anyType>
            </List>
            <Value xsi:type="xsd:int">1</Value>
          </Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnIoSuppStartupModes">
          <Value xsi:type="xsd:unsignedInt">0</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnLLDPNoDSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnLLDPSupported">
          <Value xsi:type="xsd:boolean">true</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnNetworkComponentDiagnosisSupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPdevSupportedModel">
          <Value xsi:type="xsd:unsignedInt">7</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPnIpConfigModeSupported">
          <Value xsi:type="xsd:int">3</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnPTPBoundarySupported">
          <Value xsi:type="xsd:boolean">false</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnSubmoduleIdentNumber">
          <Value xsi:type="xsd:unsignedInt">128</Value>
        </CatalogAttribute>
        <CatalogAttribute Key="PnSubslotNumber">
          <Value xsi:type="xsd:int">32768</Value>
        </CatalogAttribute>
      </AttributeLookup>
      <PortCatalogItems>
        <PortCatalogItem>
          <AttributeLookup>
            <CatalogAttribute Key="PnCheckMAUTypeDifferenceSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnCheckMAUTypeRecordSupported">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnDCPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnDontGenerateAdjustMAUTypeBlock">
              <Value xsi:type="Enumerated">
                <List>
                  <anyType xsi:type="xsd:unsignedInt">8</anyType>
                </List>
                <Value xsi:type="xsd:unsignedInt">8</Value>
              </Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnEthernetMediumDuplex">
              <Value xsi:type="Enumerated">
                <List>
                  <anyType xsi:type="xsd:unsignedInt">8</anyType>
                </List>
                <Value xsi:type="xsd:unsignedInt">8</Value>
              </Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnIoProgrammablePeer">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosis">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnLinkStateDiagnosisCapability">
              <Value xsi:type="xsd:unsignedInt">1</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortAutoNegotiation">
              <Value xsi:type="xsd:boolean">true</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortDeactivationSupported">
              <Value xsi:type="xsd:boolean">false</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPortNumber">
              <Value xsi:type="xsd:int">1</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnPTPBoundary">
              <Value xsi:type="xsd:unsignedInt">0</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubmoduleIdentNumber">
              <Value xsi:type="xsd:unsignedInt">129</Value>
            </CatalogAttribute>
            <CatalogAttribute Key="PnSubslotNumber">
              <Value xsi:type="xsd:int">32769</Value>
            </CatalogAttribute>
          </AttributeLookup>
        </PortCatalogItem>
      </PortCatalogItems>
    </InterfaceCatalogItem>
  </InterfaceCatalogItems>
</CentralDeviceCatalogObject>