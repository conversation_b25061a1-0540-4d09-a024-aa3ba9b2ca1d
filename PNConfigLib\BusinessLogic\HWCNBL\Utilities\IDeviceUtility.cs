/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IDeviceUtility.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Contains methods and utilities used for accessing attributes related to iDevice feature and relevant values.
    /// </summary>
    internal static class IDeviceUtility
    {
        /// <summary>
        /// Determines if the IDevice can support send clocks other than the value currently set.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface that will be checked.</param>
        /// <exception cref="ArgumentNullException">if deviceInterfaceSubmodule is null.</exception>
        /// <returns>
        /// True if the interface is decentral PDEV or PNIDeviceMultipleSendClock attribute value is true; false
        /// otherwise.
        /// </returns>
        internal static bool CanDeviceSupportMultipleSCF(PclObject deviceInterfaceSubmodule)
        {
            if (deviceInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(deviceInterfaceSubmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            uint isDecentralPDEV =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPdevParametrizationDecentral,
                    ac,
                    1);

            if (isDecentralPDEV > 0)
            {
                return true;
            }

            if (deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIDeviceMultipleSendClock,
                ac.GetNew(),
                false))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        ///In case of decentral PDEV I-Device without assigned controller all PDEV parametrization setting have to be disabled
        ///because there is no point to set these PDEV setting in this scenario 
        /// </summary>
        /// <param name="iDeviceInterfaceSubmodule"></param>
        /// <returns></returns>
        public static bool IsStandaloneDecPDEV(Interface iDeviceInterfaceSubmodule)
        {
            bool retValue = false;

            if (iDeviceInterfaceSubmodule == null)
            {
                return retValue;
            }

            uint pDev = iDeviceInterfaceSubmodule.AttributeAccess
                .GetAnyAttribute<UInt32>(InternalAttributeNames.PnPdevParametrizationDecentral, new AttributeAccessCode(), 0);

            if (iDeviceInterfaceSubmodule.PNIOD.AssignedController == null && pDev == 1)
            {
                retValue = true;
            }
            return retValue;
        }

    }
}