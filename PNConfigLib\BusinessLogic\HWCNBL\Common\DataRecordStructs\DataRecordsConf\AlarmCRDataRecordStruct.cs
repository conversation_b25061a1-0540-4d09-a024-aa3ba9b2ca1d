/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AlarmCRDataRecordStruct.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.DataRecordsConf
{
    /// <summary>
    /// The data record object for AlarmCRDataRecord.
    /// </summary>
    internal class AlarmCRDataRecordStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for AlarmCRDataRecordStruct.
        /// </summary>
        public AlarmCRDataRecordStruct()
        {
            BlockType = 0x3107;
            BlockLength = 24;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[20];
        }

        /// <summary>
        /// AlarmCRBlockVersion part of the data record.
        /// </summary>
        public int AlarmCRBlockVersion
        {
            set { BufferManager.Write16(Data, 0, value); }
            get { return BufferManager.Read16(Data, 0); }
        }

        /// <summary>
        /// AlarmCRPProperties part of the data record.
        /// </summary>
        public uint AlarmCRProperties
        {
            set { BufferManager.Write32(Data, 8, value); }
            get { return BufferManager.Read32(Data, 8); }
        }

        /// <summary>
        /// AlarmCRTagHeaderHigh part of the data record.
        /// </summary>
        public int AlarmCRTagHeaderHigh
        {
            set { BufferManager.Write16(Data, 16, value); }
            get { return BufferManager.Read16(Data, 16); }
        }

        /// <summary>
        /// AlarmCRTagHeaderLow part of the data record.
        /// </summary>
        public int AlarmCRTagHeaderLow
        {
            set { BufferManager.Write16(Data, 18, value); }
            get { return BufferManager.Read16(Data, 18); }
        }

        /// <summary>
        /// AlarmCRType part of the data record.
        /// </summary>
        public int AlarmCRType
        {
            set { BufferManager.Write16(Data, 2, value); }
            get { return BufferManager.Read16(Data, 2); }
        }

        /// <summary>
        /// Ethertype part of the data record.
        /// </summary>
        public int Ethertype
        {
            set { BufferManager.Write16(Data, 4, value); }
            get { return BufferManager.Read16(Data, 4); }
        }

        /// <summary>
        /// Reserved part of the data record.
        /// </summary>
        public int ReservedData
        {
            set { BufferManager.Write16(Data, 6, value); }
            get { return BufferManager.Read16(Data, 6); }
        }

        /// <summary>
        /// RTARetries part of the data record.
        /// </summary>
        public int RTARetries
        {
            set { BufferManager.Write16(Data, 14, value); }
            get { return BufferManager.Read16(Data, 14); }
        }

        /// <summary>
        /// RTATimeoutFactor part of the data record.
        /// </summary>
        public int RTATimeoutFactor
        {
            set { BufferManager.Write16(Data, 12, value); }
            get { return BufferManager.Read16(Data, 12); }
        }
    }
}