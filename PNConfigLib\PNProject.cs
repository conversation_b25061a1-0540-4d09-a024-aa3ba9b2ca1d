/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PNProject                                 :C&  */
/*                                                                           */
/*  F i l e               &F: PNProject.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;

using PNConfigLib.Consistency;
using PNConfigLib.PNProjectManager;

namespace PNConfigLib
{
    public class PNProject
    {
        /// <summary>
        /// The entry point for PNConfigLib.
        /// </summary>
        /// <param name="configFilePath">Path of the configuration XML file.</param>
        /// <param name="listOfNodesFilePath">Path of the list of nodes XML file.</param>
        /// <returns>
        /// A PNConfigLibResult object that contains whether or not the project
        /// is successful,a return code,the message explaining the result and the path
        /// to the generated output XML file(s) if it is successful.
        /// </returns>
        public PNConfigLibResult Run(string configFilePath, string listOfNodesFilePath)
        {
            return Run(configFilePath, listOfNodesFilePath, null);
        }

        /// <summary>
        /// The entry point for PNConfigLib.
        /// </summary>
        /// <param name="configFilePath">Path of the configuration XML file.</param>
        /// <param name="listOfNodesFilePath">Path of the list of nodes XML file.</param>
        /// <param name="topologyFilePath">Path of the topology XML file, optional.</param>
        /// <returns>
        /// A PNConfigLibResult object that contains whether or not the project
        /// is successful,a return code,the message explaining the result and the path
        /// to the generated output XML file(s) if it is successful.
        /// </returns>
        public PNConfigLibResult Run(string configFilePath, string listOfNodesFilePath, string topologyFilePath)
        {
            PNConfigLibResult pclResult;

            try
            {
                ProjectManager pm = new ProjectManager();
                pclResult = pm.RunPNConfigLib(configFilePath, listOfNodesFilePath, topologyFilePath);
            }
            catch (Exception exception)
            {
                List<ConsistencyLog> messages =
                    new List<ConsistencyLog>
                        {
                            new ConsistencyLog(
                                ConsistencyType.InternalError,
                                LogSeverity.Error,
                                exception.Message,
                                null)
                        };
                pclResult = new PNConfigLibResult();
                pclResult.SetMessages(messages);
            }

            return pclResult;
        }
    }
}
