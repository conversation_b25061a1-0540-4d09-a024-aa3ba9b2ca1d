﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: VirtualSubmoduleConfigureManager.cs       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class VirtualSubmoduleConfigureManager
    {
        private Project m_Project;

        internal VirtualSubmoduleConfigureManager(Project project) 
        {
            m_Project = project;
        }
        internal void Configure(DecentralDeviceType xmlDecentralDevice, 
            ModuleType xmlModule, 
            Module module,
            List<SubmoduleCatalog> virtualSubmoduleList) 
        {
            foreach (SubmoduleCatalog submoduleCatalog in virtualSubmoduleList)
            {
                if (!xmlModule.Submodule.Exists(
                        w => submoduleCatalog.AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.GsdId)
                             && w.GSDRefID == submoduleCatalog.AttributeAccess.AttributeList[InternalAttributeNames.GsdId]
                                 .ToString()))
                {
                    AttributeAccessCode aacSubslotNumber = new AttributeAccessCode();
                    uint subslotNumberCatalog = submoduleCatalog.AttributeAccess
                        .GetAnyAttribute<uint>(InternalAttributeNames.PnSubslotNumber,
                            aacSubslotNumber, 1);
                    Submodule submodule = new Submodule(submoduleCatalog, (int)subslotNumberCatalog);
                    bool isochronousSubmodule =
                        xmlDecentralDevice.DecentralDeviceInterface.IsochronousMode.IsochronousSubmodule
                            .Any(
                                sm => (sm.ModuleRefID == xmlModule.ModuleID)
                                      && string.IsNullOrEmpty(sm.SubmoduleRefID));

                    SubmoduleBL submoduleBL = new SubmoduleBL(submodule);
                    if (isochronousSubmodule)
                    {
                        submodule.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.ClockSyncMode, true);
                        submodule.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.IsClockSyncModeSelected, true);
                    }
                    module.AddSubmodule(submodule, true);
                    m_Project.BusinessLogicList.Add(submoduleBL);
                }
            }
        }
    }
}
