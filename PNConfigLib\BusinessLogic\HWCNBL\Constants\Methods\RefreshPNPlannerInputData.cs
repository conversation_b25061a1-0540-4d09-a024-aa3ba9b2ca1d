/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: RefreshPNPlannerInputData.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants.Methods
{
    /// <summary>
    /// Contains constants and types for the generic method: RefreshPNPlannerInputData
    /// </summary>
    public static class RefreshPNPlannerInputData
    {
        /// <summary>
        /// Whether the controller supports shared device assignment.
        /// Type: bool
        /// </summary>
        public const string CtrlSharedDeviceAssignmentSupp = "CtrlSharedDeviceAssignmentSupp";

        /// <summary>
        /// The IO controller that a device is connected to.
        /// Type: PNIOc
        /// </summary>
        public const string IOController = "IOController";

        /// <summary>
        /// Name of the method.
        /// </summary>
        public const string Name = "RefreshPNPlannerInputData";

        /// <summary>
        /// Collection containing the list of frames of each PNIOc or PNIOd object.
        /// Type: Dictionary of Int32 and IPNFrameData.
        /// The "Int32" in the dictionary is the hash code of PNIOc or PNIOd object.
        /// </summary>
        public const string PNFrameDataList = "PNFrameDataList";
    }
}