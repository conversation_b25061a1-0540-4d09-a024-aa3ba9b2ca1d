using PNConfigTool.Models;
using System;
using System.Threading.Tasks;

namespace PNConfigTool.Services
{
    public interface IProjectService
    {
        ProjectConfig? CurrentProject { get; }
        event EventHandler<ProjectConfig?>? ProjectChanged;
        
        Task<bool> CreateNewProject(string name);
        Task<bool> OpenProject();
        Task<bool> SaveProject();
        Task<bool> SaveProjectAs();
        Task<bool> CloseProject();
    }

    public class ProjectService : IProjectService
    {
        private readonly ProjectManager _projectManager;
        private ProjectConfig? _currentProject;

        public ProjectConfig? CurrentProject 
        { 
            get => _currentProject;
            private set
            {
                _currentProject = value;
                ProjectChanged?.Invoke(this, _currentProject);
            }
        }

        public event EventHandler<ProjectConfig?>? ProjectChanged;

        public ProjectService()
        {
            _projectManager = ProjectManager.Instance;
            CurrentProject = _projectManager.CurrentProject;
        }

        public Task<bool> CreateNewProject(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return Task.FromResult(false);

            try
            {
                bool result = _projectManager.CreateNewProject(name);
                if (result)
                {
                    CurrentProject = _projectManager.CurrentProject;
                }
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CreateNewProject 错误: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public Task<bool> OpenProject()
        {
            try
            {
                bool result = _projectManager.OpenProject();
                if (result)
                {
                    CurrentProject = _projectManager.CurrentProject;
                }
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"OpenProject 错误: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public Task<bool> SaveProject()
        {
            try
            {
                if (CurrentProject == null)
                    return Task.FromResult(false);

                bool result = _projectManager.SaveProject();
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SaveProject 错误: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public Task<bool> SaveProjectAs()
        {
            try
            {
                if (CurrentProject == null)
                    return Task.FromResult(false);

                bool result = _projectManager.SaveProjectAs();
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SaveProjectAs 错误: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public Task<bool> CloseProject()
        {
            try
            {
                // ProjectManager doesn't have a CloseProject method
                // Just clear the current project
                CurrentProject = null;
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CloseProject 错误: {ex.Message}");
                return Task.FromResult(false);
            }
        }
    }
} 