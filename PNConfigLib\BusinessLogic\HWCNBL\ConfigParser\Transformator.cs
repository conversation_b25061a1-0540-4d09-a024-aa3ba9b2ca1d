/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Transformator.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Diagnostics;

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Basics;

#endregion

namespace PNConfigLib.HWCNBL.ConfigParser
{
    internal static class Transformator
    {
        #region Write

        /// <summary>
        /// Writes the header structure for a binary attribute in the as model.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="blockType"></param>
        /// <param name="blockLength"></param>
        public static void WriteHeader(byte[] arr, ushort blockType, ushort blockLength)
        {
            WriteHeader(arr, blockType, blockLength, BlockVersionDP.VersionHigh, BlockVersionDP.VersionLow);
        }

        /// <summary>
        /// Writes the header structure for a binary attribute in the as model.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="blockType"></param>
        /// <param name="blockLength"></param>
        /// <param name="blockVersionHigh"></param>
        /// <param name="blockVersionLow"></param>
        public static void WriteHeader(
            byte[] arr,
            ushort blockType,
            ushort blockLength,
            byte blockVersionHigh,
            byte blockVersionLow)
        {
            Write16(arr, 0, blockType);
            Write16(arr, 2, blockLength);
            Write8(arr, 4, blockVersionHigh);
            Write8(arr, 5, blockVersionLow);
        }

        public static void WriteBitfieldBased16(byte[] arr, int byteOffset, int bitOffset, int bitLength, int val)
        {
            Debug.Assert(bitOffset + bitLength <= 16);
            Debug.Assert(byteOffset < arr.Length - 1);

            if ((val & 0xFFFF0000) != 0)
            {
                throw new ConfigException("WriteBitfieldBased16: Invalid value");
            }

            int mask = (1 << bitLength) - 1;

            Debug.Assert((val & ~mask) == 0);

            mask <<= bitOffset;
            val <<= bitOffset;
            val &= mask;
            int nValue = Read16(arr, byteOffset);
            nValue &= ~mask;
            nValue |= val;
            Write16(arr, byteOffset, (ushort)nValue);
        }

        /// <summary>
        /// Writes a byte to the array with the given offset
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="val"></param>
        public static void Write8(byte[] arr, int byteOffset, byte val)
        {
            Debug.Assert((byteOffset >= 0) && (byteOffset < arr.Length));
            arr[byteOffset] = val;
        }

        /// <summary>
        /// Writes a byte to the array with the given offset
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="val"></param>
        public static void Write8(byte[] arr, int byteOffset, int val)
        {
            Debug.Assert(byteOffset < arr.Length);
            Debug.Assert((val >= 0) && (val <= 255));
            arr[byteOffset] = (byte)val;
        }

        /// <summary>
        /// Writes a 16 bit word in the array. The byte order is big endian.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="val"></param>
        public static void Write16(byte[] arr, int byteOffset, ushort val)
        {
            Debug.Assert(byteOffset < arr.Length - 1);

            byte lo = (byte)(val & 0x00FF);
            byte hi = (byte)((val & 0x0FF00) >> 8);
            arr[byteOffset] = hi;
            arr[byteOffset + 1] = lo;
        }

        /// <summary>
        /// added by me, writes an integer as 4 bytes into a byte array
        /// </summary>
        /// <param name="arr">here is the int value stored</param>
        /// <param name="byteOffset">offset in the array</param>
        /// <param name="val">the (int) value</param>
        public static void Write32(byte[] arr, int byteOffset, uint val)
        {
            Debug.Assert(byteOffset < arr.Length - 1);
            arr[byteOffset + 3] = (byte)(val & 0xff);
            arr[byteOffset + 2] = (byte)((val >> 8) & 0xff);
            arr[byteOffset + 1] = (byte)((val & 0xff0000) >> 16);
            arr[byteOffset] = (byte)((val & 0xff000000) >> 24);
        }

        #endregion

        #region Read

        /// <summary>
        /// Reads a sequence of bits from a byte. All bits must be inside the byte.
        /// Condition: bitOffset + bitLength smaller than 8
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="bitOffset"></param>
        /// <param name="bitLength"></param>
        /// <returns></returns>
        public static byte ReadBitfieldBased8(byte[] arr, int byteOffset, int bitOffset, int bitLength)
        {
            Debug.Assert(bitOffset + bitLength <= 8);

            int mask = 1 << bitLength;
            mask -= 1;
            mask <<= bitOffset;

            int val = arr[byteOffset];
            val &= mask;
            val >>= bitOffset;

            return (byte)val;
        }

        /// <summary>
        /// Reads a sequence of bits from a word. All bits must be inside the word.
        /// Condition: bitOffset + bitLength smaller than 16
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="bitOffset"></param>
        /// <param name="bitLength"></param>
        /// <returns></returns>
        public static ushort ReadBitfieldBased16(byte[] arr, int byteOffset, int bitOffset, int bitLength)
        {
            Debug.Assert(bitOffset + bitLength <= 16);

            // Beispiel für bitOffset = 2, bitLength = 3
            int mask = 1 << bitLength; // mask = 00001000
            mask -= 1; // mask = 00000111
            mask <<= bitOffset; // mask = 00011100

            int val = Read16(arr, byteOffset);
            val &= mask;
            val >>= bitOffset;

            return (ushort)val;
        }

        /// <summary>
        /// Reads a sequence of bits from a long word.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <param name="bitOffset"></param>
        /// <param name="bitLength"></param>
        /// <returns></returns>
        public static uint ReadBitfieldBased32(byte[] arr, int byteOffset, int bitOffset, int bitLength)
        {
            Debug.Assert(bitOffset + bitLength <= 32);

            uint mask = (uint)(1 << bitLength);
            mask -= 1;
            mask <<= bitOffset;

            uint val = Read32(arr, byteOffset);
            val &= mask;
            val >>= bitOffset;

            return val;
        }

        /// <summary>
        /// Reads a byte from an array.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <returns></returns>
        public static byte Read8(byte[] arr, int byteOffset)
        {
            Debug.Assert((byteOffset >= 0) && (byteOffset < arr.Length));
            return arr[byteOffset];
        }

        /// <summary>
        /// Reads a 16 bit word from an array. The byte order is big endian.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <returns></returns>
        public static ushort Read16(byte[] arr, int byteOffset)
        {
            Debug.Assert((byteOffset >= 0) && (byteOffset < arr.Length - 1));

            int hi = arr[byteOffset];
            int lo = arr[byteOffset + 1];
            int result = (hi << 8) + lo;
            return (ushort)result;
        }

        /// <summary>
        /// Reads a 32 bit word from an array.
        /// </summary>
        /// <param name="arr"></param>
        /// <param name="byteOffset"></param>
        /// <returns></returns>
        public static uint Read32(byte[] arr, int byteOffset)
        {
            uint hi1 = arr[byteOffset];
            uint lo1 = arr[byteOffset + 1];
            uint hi2 = arr[byteOffset + 2];
            uint lo2 = arr[byteOffset + 3];

            return (((hi1 << 8) + lo1) << 16) + (hi2 << 8) + lo2;
        }

        #endregion
    }
}