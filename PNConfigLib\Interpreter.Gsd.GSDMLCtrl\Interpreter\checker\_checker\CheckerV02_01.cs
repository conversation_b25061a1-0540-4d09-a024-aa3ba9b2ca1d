/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_01.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Xml;
using GSDI;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using System.Xml.XPath;
using PNConfigLib.Gsd.Interpreter;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.1 and is based on GSD(ML) versions 2.0 and lower.
    ///		
    /// </summary>
    internal class CheckerV0201 : CheckerV0200
    {

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.1.
        /// </summary>
        public CheckerV0201()
        {
            SetSupportedGsdmlVersion(Constants.s_Version21);
        }

        #endregion

        //########################################################################################
        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV0201;
        }
        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV0201;
        }

        #endregion

        //########################################################################################
        #region Properties


        protected override ReportTypes ReportType_0X00010015 => ReportTypes.GSD_RT_Error;

        protected virtual ReportTypes ReportType_0X00011109 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000111A0 => ReportTypes.GSD_RT_MinorError;

        protected virtual ReportTypes ReportType_0X000111A5 => ReportTypes.GSD_RT_MinorError;

        protected override string FMessageTrailer5Byte => Enums.s_FMessageTrailer5Byte;

        protected virtual string AttributesWithTokenList
        {
            get
            {
                string xp = ".//gsddef:ExtChannelDiagItem/@MaintenanceAlarmState" +
                            " | .//@MaintenanceAlarmState" +
                            " | .//gsddef:DeviceAccessPointItem/@AddressAssignment" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SupportedRT_Classes" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SupportedProtocols" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SupportedMibs" +
                            " | .//gsddef:MediaRedundancy/@SupportedRole";

                return (xp);

            }
        }

        protected virtual string AttributesWithValueList
        {
            get
            {
                string xp = ".//gsddef:PortSubmoduleItem/@MAUTypes" +
                            " | .//gsddef:PortSubmoduleItem/@FiberOpticTypes";
                return (xp);
            }
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00011101);
            Checks.Add(Constants.s_Cn_0X00011104);
            Checks.Add(Constants.s_Cn_0X00011106);
            Checks.Add(Constants.s_Cn_0X00011107);
            Checks.Add(Constants.s_Cn_0X00011108);
            Checks.Add(Constants.s_Cn_0X00011109);
            Checks.Add(Constants.s_Cn_0X0001119A);
            Checks.Add(Constants.s_Cn_0X0001119B);
            Checks.Add(Constants.s_Cn_0X0001119C);
            Checks.Add(Constants.s_Cn_0X0001119D);
            Checks.Add(Constants.s_Cn_0X0001119E);
            Checks.Add(Constants.s_Cn_0X000111A0);
            Checks.Add(Constants.s_Cn_0X000111A1);
            Checks.Add(Constants.s_Cn_0X000111A2);
            Checks.Add(Constants.s_Cn_0X000111A3);
            Checks.Add(Constants.s_Cn_0X000111A4);
            Checks.Add(Constants.s_Cn_0X000111A5);
            Checks.Add(Constants.s_Cn_0X000111A6);
            Checks.Add(Constants.s_Cn_0X000111A7);
            Checks.Add(Constants.s_Cn_0X000111A8);

            return succeeded;
        }

        #endregion

        #region All Checks

        #region Category : Validation

        /// <summary>
        /// MaxMRP_Instances:
        /// MaxMRP_Instances shall be less or equal half of the maximum configurable number of PortSubmodules which have
        /// either the attribute IsDefaultRingport or the attribute SupportsRingportConfig or both set to "true".
        /// 
        /// IsDefaultRingport:
        /// If InterfaceSubmoduleItem/MediaRedundancy is present, the attribute IsDefaultRingport shall be
        /// present and "true" on at least two assigned (configurable) port submodules.
        /// 
        /// This rule is modified in GSDML V2.33 (see check CN_0x00033008). 
        /// </summary>
        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("SubmoduleItem", "ID");
            ElementDescriptions.Add("ProfileChannelDiagItem", "API,+ErrorType");
            ElementDescriptions.Add("ProfileExtChannelDiagItem", "API,+ErrorType");
            ElementDescriptions.Add("ProfileUnitDiagTypeItem", "API,+UserStructureIdentifier");
            ElementDescriptions.Add("SubmoduleItemRef", "SubmoduleItemTarget");
            ElementDescriptions.Add("SlotItem", "SlotNumber");
            ElementDescriptions.Add("SlotGroup", "SlotList");
            ElementDescriptions["DataItem"] += ",Id";
        }

        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("D");
            tokens1.Add("MR");
            tokens1.Add("MD");
            tokens1.Add("QD");
            AttributeTokenDictionary.Add("MaintenanceAlarmState", tokens1);

            IList<string> tokens2 = new List<string>();
            tokens2.Add("DCP");
            tokens2.Add("DHCP");
            tokens2.Add("LOCAL");
            AttributeTokenDictionary.Add("AddressAssignment", tokens2);

            IList<string> tokens3 = new List<string>();
            tokens3.Add("RT_CLASS_UDP");
            tokens3.Add("RT_CLASS_1");
            tokens3.Add("RT_CLASS_2");
            tokens3.Add("RT_CLASS_3");
            AttributeTokenDictionary.Add("SupportedRT_Classes", tokens3);

            IList<string> tokens4 = new List<string>();
            tokens4.Add("SNMP");
            tokens4.Add("LLDP");
            AttributeTokenDictionary.Add("SupportedProtocols", tokens4);

            IList<string> tokens5 = new List<string>();
            tokens5.Add("MIB2");
            AttributeTokenDictionary.Add("SupportedMibs", tokens5);

            IList<string> tokens6 = new List<string>();
            tokens6.Add("Manager");
            tokens6.Add("Client");
            AttributeTokenDictionary.Add("SupportedRole", tokens6);
        }

        /// - MAUTypes
        /// - FiberOpticTypes
        /// 
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            string values1 = "-v2.3 0 5 10..13 15..18 21..26 29..31 46 54";
            AttributeValueListDictionary.Add("MAUTypes", values1);
            string values2 = "0 5 10..13 15..18 21..26 29..78";
            AttributeValueListDictionary.Add("MAUTypes-v2.3", values2);

            string values3 = "-v2.2 0..7 128..255";
            AttributeValueListDictionary.Add("FiberOpticTypes", values3);
            string values4 = "0..8 128..255";
            AttributeValueListDictionary.Add("FiberOpticTypes-v2.2", values4);
        }

        #endregion

        #region Methods

        private double FindMinPNioVersionForPluggablePortSubmodule(XElement portSubmoduleItem)
        {
            double pnioVersion = 100;
            bool pnioVersionFound = false;

            if (PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
            {
                foreach (XElement dap in dapsOfPort)
                {
                    if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion)))
                    {
                        pnioVersionFound = true;
                        double pnioVersionTemp = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                        if (pnioVersionTemp < pnioVersion)
                            pnioVersion = pnioVersionTemp;
                    }
                }
            }

            if (!pnioVersionFound)
                pnioVersion = 0;

            return pnioVersion;
        }

        private IList<string> GetAllowedTokens(XAttribute an)
        {
            switch (an.Name.LocalName)
            {
                case "Algorithms":
                    {
                        XElement parent = an.Parent;
                        if (parent != null)
                        {
                            switch (parent.Name.LocalName)
                            {
                                case "KeyDerivation":
                                    return (AttributeTokenDictionary["Algorithms1"]);
                                case "KeyAgreement":
                                    return (AttributeTokenDictionary["Algorithms2"]);
                                case "DigitalSignature":
                                    return (AttributeTokenDictionary["Algorithms3"]);
                            }
                        }
                    }
                    break;
            }



            return (AttributeTokenDictionary[an.Name.LocalName]);
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00010018
        /// 
        /// As of V1.0:
        /// The value of the ChannelDiagItem/@ErrorType attribute must be in the value range
        /// between 0x000F and 0x7FFF and must be
        /// be unique across all 'ChannelDiagItem' entries.
        /// 
        /// As of V2.0
        /// For ChannelDiagList/ChannelDiagItem/ExtChannelDiagList/ExtChannelDiagItem must be checked:
        /// - ErrorType must be unique within the ChannelDiagItem
        /// - ErrorType must be in the range 1-32767
        /// 
        /// As of V2.1
        /// For ChannelDiagList/ProfileChannelDiagItem is to be checked:
        /// - Combination of ErrorType and API must be unique across all ProfileChannelDiagItems.
        /// - ErrorType must be in the range 36864-40959
        /// For ChannelDiagList/(Profile)ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem must be checked:
        /// - Combination of ErrorType and API must be unique within the (Profile)ChannelDiagItem
        /// - ErrorType must be in the range 36864-40959
        /// For ChannelDiagList/ProfileChannelDiagItem/ExtChannelDiagList/ExtChannelDiagItem check:
        /// - ErrorType must be unique within the ProfileChannelDiagItem
        /// - ErrorType must be in the range 1-32767
        /// 
        /// From V2.2
        /// these checks are handled by the schema. The check can then be removed.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010018()
        {
            base.CheckCn_0X00010018();

            Dictionary<uint, uint> errortypesAndApis = null;

            var nl = GsdProfileBody.XPathSelectElements(".//gsddef:ChannelDiagList/gsddef:ProfileChannelDiagItem", Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (null == errortypesAndApis)
                    errortypesAndApis = new Dictionary<uint, uint>();

                uint errortype = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en, Attributes.s_ErrorType));    // required attribute
                uint api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en, Attributes.s_Api));    // required attribute

                var xli = (IXmlLineInfo)en.Attribute(Attributes.s_ErrorType);

                // (1)
                if (errortype < 36864 || errortype > 40959)
                {
                    // "The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}."
                    string msg = String.Format(Help.GetMessageString("M_0x00010018_1"), errortype, 36864, 40959);
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_ErrorType));
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportType_0X000100181,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010018_1");
                    }
                }

                // (2)
                if (errortypesAndApis.TryGetValue(errortype, out uint storedApi))
                {
                    if (storedApi != api)
                    {
                        continue;
                    }

                    // "The combination 'ErrorType' ({0}) and 'API' ({1}) is a duplicate."
                    string msg = String.Format(Help.GetMessageString("M_0x00010018_3"), errortype, api);
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_ErrorType));
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010018_3");
                    }

                }
                else
                    errortypesAndApis.Add(errortype, api);
            }

            errortypesAndApis = null;

            // For ChannelDiagList/(Profile)ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem check:
            // (3) ErrorType must be in the range 36864-40959
            // (4) Combination of ErrorType and API must be unique within the (Profile)ChannelDiagItem.

            var nl1 = GsdProfileBody.XPathSelectElements(".//gsddef:ChannelDiagList/gsddef:ChannelDiagItem | //gsddef:ChannelDiagList/gsddef:ProfileChannelDiagItem", Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en1 in nl1)
            {
                var nl2 = en1.XPathSelectElements("./gsddef:ExtChannelDiagList/gsddef:ProfileExtChannelDiagItem", Nsmgr);

                foreach (var en2 in nl2)
                {
                    if (null == errortypesAndApis)
                        errortypesAndApis = new Dictionary<uint, uint>();

                    var xli = (IXmlLineInfo)en2.Attribute(Attributes.s_ErrorType);

                    uint errortype = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en2, Attributes.s_ErrorType)); // required attribute
                    uint api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en2, Attributes.s_Api)); // required attribute

                    // (3)
                    if (errortype < 36864 || errortype > 40959)
                    {
                        // "The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}."
                        string msg = String.Format(Help.GetMessageString("M_0x00010018_1"), errortype, 36864, 40959);
                        string xpath = Help.GetXPath(en2.Attribute(Attributes.s_ErrorType));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportType_0X000100181,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010018_1");
                        }
                    }

                    // (4)
                    if (errortypesAndApis.TryGetValue(errortype, out uint storedApi))
                    {
                        if (storedApi != api)
                        {
                            continue;
                        }

                        // "The combination 'ErrorType' ({0}) and 'API' ({1}) is a duplicate."
                        string msg = String.Format(Help.GetMessageString("M_0x00010018_3"), errortype, api);
                        string xpath = Help.GetXPath(en2.Attribute(Attributes.s_ErrorType));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010018_3");
                        }

                    }
                    else
                        errortypesAndApis.Add(errortype, api);
                }
            }

            // For ChannelDiagList/ProfileChannelDiagItem/ExtChannelDiagList/ExtChannelDiagItem check:
            // (5) ErrorType must be in the range 1-32767
            // (6) ErrorType must be unique within the ProfileChannelDiagItem.

            var nl3 = GsdProfileBody.XPathSelectElements(".//gsddef:ChannelDiagList/gsddef:ProfileChannelDiagItem", Nsmgr);
            nl3 = Help.TryRemoveXElementsUnderXsAny(nl3, Nsmgr, Gsd);
            foreach (var en3 in nl3)
            {
                var nl4 = en3.XPathSelectElements("./gsddef:ExtChannelDiagList/gsddef:ExtChannelDiagItem", Nsmgr);

                IList<uint> lerrortypes = null;

                foreach (var en4 in nl4)
                {
                    if (null == lerrortypes)
                        lerrortypes = new List<uint>();

                    var xli = (IXmlLineInfo)en4.Attribute(Attributes.s_ErrorType);

                    uint errortype = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en4, Attributes.s_ErrorType)); // required attribute

                    // (5)
                    if (errortype < 1 || errortype > 32767)
                    {
                        // "The 'ErrorType' ({0}) is not in the allowed range between {1} and {2}."
                        string msg = String.Format(Help.GetMessageString("M_0x00010018_1"), errortype, 1, 32767);
                        string xpath = Help.GetXPath(en4.Attribute(Attributes.s_ErrorType));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010018_1");
                        }
                    }

                    // (6)
                    if (lerrortypes.Contains(errortype))
                    {
                        // "The 'ErrorType' ({0}) is a duplicate."
                        string msg = String.Format(Help.GetMessageString("M_0x00010018_2"), errortype);
                        string xpath = Help.GetXPath(en4.Attribute(Attributes.s_ErrorType));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010018_2");
                        }
                    }
                    else
                        lerrortypes.Add(errortype);
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010019
        /// 
        /// As of V1.0:
        /// For UnitDiagTypeList/UnitDiagTypeItem check:
        /// - UserStructureIdentifier must be in the range 0-32767
        /// - UserStructureIdentifier must be unique across all UnitDiagTypeItems.
        /// 
        /// As of V2.1:
        /// For UnitDiagTypeList/ProfileUnitDiagTypeItem must be checked:
        /// - UserStructureIdentifier must be in the range 36864-40959
        /// - Combination of UserStructureIdentifier and API must be unique across all ProfileUnitDiagTypeItems.
        /// 
        /// From V2.2
        /// these checks are handled by the schema. The check can then be removed.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010019()
        {
            base.CheckCn_0X00010019();

            Dictionary<uint, uint> usisAndApis = null;

            // For UnitDiagTypeList/ProfileUnitDiagTypeItem check:
            // (1) UserStructureIdentifier must be in the range 36864-40959
            // (2) Combination of UserStructureIdentifier and API must be unique across all ProfileUnitDiagTypeItems.

            var nl = GsdProfileBody.XPathSelectElements(".//gsddef:UnitDiagTypeList/gsddef:ProfileUnitDiagTypeItem", Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (null == usisAndApis)
                    usisAndApis = new Dictionary<uint, uint>();

                uint usi = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en, Attributes.s_UserStructureIdentifier)); // required attribute
                uint api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(en, Attributes.s_Api)); // required attribute

                // (1)
                if (usi < 36864 || usi > 40959)
                {
                    // "The 'UserStructureIdentifier' ({0}) is not in the range of {1} to {2}."
                    string msg = String.Format(Help.GetMessageString("M_0x00010019_1"), usi, 36864, 40959);
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_UserStructureIdentifier));
                    IXmlLineInfo xli = en.Attribute(Attributes.s_UserStructureIdentifier);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010018_1");
                    }
                }

                // (2)
                if (usisAndApis.TryGetValue(usi, out uint storedApi))
                {
                    if (storedApi != api)
                    {
                        continue;
                    }
                    // "The combination 'UserStructureIdentifier' ({0}) and 'API' ({1}) is a duplicate."
                    string msg = String.Format(Help.GetMessageString("M_0x00010019_3"), usi, api);
                    string xpath = Help.GetXPath(en.Attribute(Attributes.s_UserStructureIdentifier));
                    IXmlLineInfo xli = en.Attribute(Attributes.s_UserStructureIdentifier);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00010018_3");
                    }
                }
                else
                    usisAndApis.Add(usi, api);
            }

            return true;
        }

        protected override bool CheckCn_0X00010102()
        {
            // execute base check
            base.CheckCn_0X00010102();

            // Find all SubmoduleItem IDs, which support IsochroneMode.
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found Submodule IDs.
            var submoduleids = new List<string>();
            foreach (XElement en in nl)
            {
                XAttribute an = en.Attribute(Attributes.ID);
                if (an != null && !submoduleids.Contains(Help.CollapseWhitespace(an.Value)))
                    submoduleids.Add(Help.CollapseWhitespace(an.Value));
            }

            if (submoduleids.Count <= 0)
            {
                return true;
            }

            // Find all DeviceAccessPoints, which don't support IsochroneMode.
            var interfaceSubmoduleItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            interfaceSubmoduleItems = Help.TryRemoveXElementsUnderXsAny(interfaceSubmoduleItems, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in interfaceSubmoduleItems)
            {
                string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
                bool isochroneModeSupported = false;
                if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                    isochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                string strIsochroneModeInRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);

                if (isochroneModeSupported || strIsochroneModeInRTClasses != string.Empty)
                {
                    continue;
                }

                bool showWarning = false;

                var dap = interfaceSubmoduleItem.Parent.Parent;

                // Check UseableSubmodule
                var useableSubmoduleNodes = dap.Elements(NamespaceGsdDef + Elements.s_UseableSubmodules)
                                              .Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef)
                                              .Attributes(Attributes.s_SubmoduleItemTarget);
                foreach (XAttribute attribute in useableSubmoduleNodes)
                {
                    if (!submoduleids.Contains(Help.CollapseWhitespace(attribute.Value)))
                    {
                        continue;
                    }

                    // Submodule supports IsochroneMode. If the IsochroneMode is
                    // required show an error, otherwise only a warning

                    XElement submoduleItem = Help.GetElementById(Gsd.Root, ".//gsddef:SubmoduleList/gsddef:SubmoduleItem",
                                                                   Attributes.ID, attribute.Value, Nsmgr);

                    var isochroneElement = submoduleItem.Element(NamespaceGsdDef + Elements.s_IsochroneMode);
                    if (isochroneElement == null)
                    {
                        continue;
                    }
                    string isochroneRequiredStr =
                        Help.GetAttributeValueFromXElement(isochroneElement, Attributes.s_IsochroneModeRequired);
                    bool isochroneRequired = false;
                    if (!string.IsNullOrEmpty(isochroneRequiredStr))
                        isochroneRequired = XmlConvert.ToBoolean(isochroneRequiredStr);
                    if (isochroneRequired)
                    {
                        // "The 'DeviceAccessPointItem' does not support IsochroneMode but it can be used
                        // together with a 'SubmoduleItem' which requires IsochroneMode."
                        string msg = Help.GetMessageString("M_0x00010102_5");
                        string xpath = Help.GetXPath(dap);
                        IXmlLineInfo xli = dap;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_5");
                        submoduleids.Remove(Help.CollapseWhitespace(attribute.Value)); // For this submodule a warning is not needed any more
                    }
                    else
                    {
                        showWarning = true;
                    }


                }

                // Find all Module references starting from current DAP.
                var nltemp = dap.Elements(NamespaceGsdDef + Elements.s_UseableModules)
                                              .Elements(NamespaceGsdDef + Elements.s_ModuleItemRef)
                                              .Attributes(Attributes.s_ModuleItemTarget);
                foreach (XAttribute moduleId in nltemp)
                {
                    // find all submodules which can be used in the module
                    var moduleItem = Help.GetElementById(Gsd.Root, ".//gsddef:ModuleList/gsddef:ModuleItem",
                                                                Attributes.ID, moduleId.Value, Nsmgr);
                    var moduleSubmodules = moduleItem.Elements(NamespaceGsdDef + Elements.s_UseableSubmodules)
                                                  .Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef)
                                                  .Attributes(Attributes.s_SubmoduleItemTarget);
                    foreach (XAttribute submoduleId in moduleSubmodules)
                    {
                        if (submoduleids.Contains(Help.CollapseWhitespace(submoduleId.Value)))
                        {
                            showWarning = true;
                        }
                    }
                }

                if (showWarning)
                {
                    var en1 = dap.XPathSelectElement("./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem", Nsmgr);
                    if (en1 != null)
                    {
                        // "The 'DeviceAccessPointItem' does not support IsochroneMode but it can be used
                        // together with a 'SubmoduleItem' which supports IsochroneMode."
                        string msg = Help.GetMessageString("M_0x00010102_6");
                        string xpath = Help.GetXPath(dap);
                        IXmlLineInfo xli = dap;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010102_6");
                    }
                }

            }


            return true;
        }

        protected override bool CheckCn_0X00010121()
        {

            // Find all ModuleItem IDs, which support IsochroneMode.
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Collect found Module IDs.
            var moduleids = new List<string>();
            XAttribute an;
            foreach (XElement en in nl)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && !moduleids.Contains(Help.CollapseWhitespace(an.Value)))
                    moduleids.Add(Help.CollapseWhitespace(an.Value));
            }


            // Find all SubmoduleItem IDs, which support IsochroneMode.
            var submodulesWithisochroneMode = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_IsochroneMode).FirstOrDefault() != null);
            submodulesWithisochroneMode = Help.TryRemoveXElementsUnderXsAny(submodulesWithisochroneMode, Nsmgr, Gsd);

            // Collect found Submodule IDs.
            var submoduleids = new List<string>();
            foreach (XElement en in submodulesWithisochroneMode)
            {
                an = en.Attribute(Attributes.ID);
                if (an != null && !submoduleids.Contains(Help.CollapseWhitespace(an.Value)))
                    submoduleids.Add(Help.CollapseWhitespace(an.Value));
            }

            // Find all DeviceAccessPoints, which support IsochroneMode and have no VirtualSubmoduleItem with IsochroneMode element.
            var interfaceSubmoduleItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            interfaceSubmoduleItems = Help.TryRemoveXElementsUnderXsAny(interfaceSubmoduleItems, Nsmgr, Gsd);
            foreach (var interfaceSubmoduleItem in interfaceSubmoduleItems)
            {
                string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
                bool isochroneModeSupported = false;
                if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                    isochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                string strIsochroneModeInRTClasses = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);

                if (!isochroneModeSupported
                    && strIsochroneModeInRTClasses == string.Empty)
                {
                    continue;
                }

                bool submoduleWithIsochroneModeFound = false;

                var dap = interfaceSubmoduleItem.Parent.Parent;


                // check VirtualSubmoduleItem of DAP
                string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem/gsddef:IsochroneMode";
                var vsmList = dap.XPathSelectElements(xp, Nsmgr).ToList();

                if (vsmList.Count > 0)
                    submoduleWithIsochroneModeFound = true;

                if (!submoduleWithIsochroneModeFound)
                {
                    // check pluggable SubmoduleItems of DAP
                    xp = "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef/@SubmoduleItemTarget";
                    var submoduleItemsList = (IEnumerable)dap.XPathEvaluate(xp, Nsmgr);
                    foreach (XAttribute submoduleId in submoduleItemsList)
                    {
                        if (submoduleids.Contains(Help.CollapseWhitespace(submoduleId.Value)))
                        {
                            submoduleWithIsochroneModeFound = true;
                            break;
                        }
                    }
                }

                if (!submoduleWithIsochroneModeFound)
                {
                    xp = "./gsddef:UseableModules/gsddef:ModuleItemRef/@ModuleItemTarget";
                    var moduleItemsList = (IEnumerable)dap.XPathEvaluate(xp, Nsmgr);
                    foreach (XAttribute moduleId in moduleItemsList)
                    {
                        // check pluggable modules of dap
                        if (moduleids.Contains(Help.CollapseWhitespace(moduleId.Value)))
                        {
                            submoduleWithIsochroneModeFound = true;
                            break;
                        }
                        else
                        {
                            // check pluggable SubmoduleItems of module
                            var moduleItem = Help.GetElementById(Gsd.Root, ".//gsddef:ModuleList/gsddef:ModuleItem",
                                                                        Attributes.ID, moduleId.Value, Nsmgr);
                            xp = string.Format(
                                    CultureInfo.InvariantCulture,
                                    "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef/@SubmoduleItemTarget",
                                    moduleId.Value);
                            var submoduleItemsOfModuleList = (IEnumerable)moduleItem.XPathEvaluate(xp, Nsmgr);
                            foreach (XAttribute submoduleId in submoduleItemsOfModuleList)
                            {
                                if (submoduleids.Contains(Help.CollapseWhitespace(submoduleId.Value)))
                                {
                                    submoduleWithIsochroneModeFound = true;
                                    break;
                                }
                            }
                        }

                        // if no submodule with IsochroneMode support found execute base check
                        if (!submoduleWithIsochroneModeFound)
                        {
                            var moduleItem = Help.GetElementById(Gsd.Root, ".//gsddef:ModuleList/gsddef:ModuleItem",
                                                                        Attributes.ID, moduleId.Value, Nsmgr);
                            xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem/gsddef:IsochroneMode";
                            vsmList = moduleItem.XPathSelectElements(xp, Nsmgr).ToList();

                            if (vsmList.Count > 0)
                            {
                                submoduleWithIsochroneModeFound = true;
                                break;
                            }
                        }
                    }
                }

                if (!submoduleWithIsochroneModeFound)
                {
                    // "The 'DeviceAccessPointItem' supports IsochroneMode, but none of its modules can use the IsochroneMode."
                    string msg = Help.GetMessageString("M_0x00010121_1");
                    string xpath = Help.GetXPath(dap);
                    var xli = (IXmlLineInfo)dap;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition,
                        msg, xpath,
                        ReportCategories.TypeSpecific, "0x00010121_1");
                }

            }


            return true;
        }

        /// <summary>
        /// If the values 1, 3, 5 or 7 are specified for 'F_Block_ID' for DefaultValue, 'F_iPar_CRC' must also be specified.
        /// </summary>
        protected virtual bool CheckCn_0X00011101()
        {
            string xp = ".//gsddef:RecordDataList/gsddef:F_ParameterRecordDataItem[count(./gsddef:F_iPar_CRC)=0]/gsddef:F_Block_ID[@DefaultValue mod 2 >0]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // Get the attribute 'Visible'
                bool visible = true; // Default value
                string strVisible = Help.GetAttributeValueFromXElement(en, Attributes.s_Visible);
                if (!String.IsNullOrEmpty(strVisible))
                    visible = XmlConvert.ToBoolean(strVisible);

                // If 'Visible' is not set, the default works for DefaultValue (=0) and no warning must be reported.
                if (visible)
                {
                    if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                    {
                        // "'F_Block_ID' with 'DefaultValue' 1, 3, 5 or 7 but no 'F_iPar_CRC'."
                        string msg = Help.GetMessageString("M_0x00011101_1");
                        string xpath = Help.GetXPath(en);
                        var xli = (IXmlLineInfo)en;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011101_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        //// As of GSDML V2.1, DeviceAccessPointItems and ModuleItems no longer have to have a VirtualSubmoduleList
        /// if a SystemDefinedSubmoduleList exists.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00011104()
        {
            // //DeviceAccessPointItem[not(./VirtualSubmoduleList) and not(./SystemDefinedSubmoduleList) and not(./UseableSubmodules)]
            string xp = ".//gsddef:DeviceAccessPointItem[not(./gsddef:VirtualSubmoduleList) and not(./gsddef:SystemDefinedSubmoduleList) and not(./gsddef:UseableSubmodules)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The 'DeviceAccessPointItem' must have either a 'VirtualSubmoduleList', a 'SystemDefinedSubmoduleList' or a 'UseableSubmodules' list."
                string msg = Help.GetMessageString("M_0x00011104_1");
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011104_1");
            }

            // //ModuleItem[not(./VirtualSubmoduleList) and not(./SystemDefinedSubmoduleList)]
            xp = ".//gsddef:ModuleItem[not(./gsddef:VirtualSubmoduleList) and not(./gsddef:SystemDefinedSubmoduleList) and not(./gsddef:UseableSubmodules)]";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The 'ModuleItem' must have either a 'VirtualSubmoduleList', a 'SystemDefinedSubmoduleList' or a 'UseableSubmodules' list."
                    string msg = Help.GetMessageString("M_0x00011104_2");
                    string xpath = Help.GetXPath(en);
                    IXmlLineInfo xli = en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011104_2");
                }
            }

            return true;
        }

        /// <summary>
        /// (2) Each 'Content' attribute entry from the 'ValueItem'
        ///     assignments must comply with the data type from the 'DataType' attribute.
        /// 
        /// </summary>
        /// <returns>Gets the list of all content values (float).</returns>
        protected virtual void CheckAssignContentAgainstDataTypeFloat(XElement en, string svaluegsdid, string sdatatype, uint bitlength,
                                                                      ref List<float> nlcontentFloat32, ref List<double> nlcontentFloat64, ref IList<string> errList)
        {
            // Each 'Content' attribute entry from the 'ValueItem'
            // assignments must comply with the data type from the 'DataType' attribute.
            if (!(string.IsNullOrEmpty(svaluegsdid)))
            {
                // Find the matching content nodes
                ValueItems.TryGetValue(svaluegsdid, out XElement valueItem);
                if (null == valueItem)
                    return;
                var contents = (IEnumerable)valueItem.XPathEvaluate("./gsddef:Assignments/gsddef:Assign/@Content", Nsmgr);
                if (sdatatype == "Float32" || sdatatype == "E2" || sdatatype == "C4" || sdatatype == "Unipolar2.16")
                {
                    foreach (XAttribute content in contents)
                    {
                        // (2)
                        object contentValue = Help.GetValueByDataType(content.Value, sdatatype, bitlength);
                        if (null == contentValue)
                        {
                            string xpath = Help.GetXPath(content);
                            var xli = (IXmlLineInfo)content;
                            // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}")."
                            string msg = String.Format(Help.GetMessageString("M_0x00010022_7"), svaluegsdid, content.Value, sdatatype);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010022_7");
                            errList.Add("e" + "0x00010022_7" + msg);
                        }
                        else
                        {
                            // Fill the list with content values.
                            nlcontentFloat32.Add(Convert.ToSingle(contentValue));
                        }
                    }
                    nlcontentFloat32.Sort();
                }
                else
                {
                    foreach (XAttribute content in contents)
                    {
                        // (2)
                        object contentValue = Help.GetValueByDataType(content.Value, sdatatype, bitlength);
                        if (null == contentValue)
                        {
                            string xpath = Help.GetXPath(content);
                            IXmlLineInfo xli = content;
                            // "The 'ValueItem' (ID "{0}") assignments 'Content' value ({1}) does not comply with the 'DataType' ("{2}")."
                            string msg = String.Format(Help.GetMessageString("M_0x00010022_7"), svaluegsdid, content.Value, sdatatype);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010022_7");
                            errList.Add("e" + "0x00010022_7" + msg);
                        }
                        else
                        {
                            // Fill the list with content values.
                            nlcontentFloat64.Add(Convert.ToDouble(contentValue));
                        }
                    }
                    nlcontentFloat64.Sort();
                }
            }
        }

        /// <summary>
        /// (3) Each entry in the 'AllowedValues' list must comply with the specified
        ///     data type from the 'DataType' attribute.
        /// 
        /// </summary>
        /// <returns>None.</returns>
        protected virtual void CheckAllowedValuesAgainstDataTypeFloat(XElement en,
                                                                       List<ValueListHelper.Float32ValueRangeT> allowedValueListFloat32,
                                                                       List<ValueListHelper.Float64ValueRangeT> allowedValueListFloat64,
                                                                       string sdatatype, uint bitlength, ref IList<string> errList)
        {
            var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);

            // Each entry must comply with the specified
            // data type From the 'DataType' attribute.
            if (sdatatype == "Float32" || sdatatype == "E2" || sdatatype == "C4" || sdatatype == "Unipolar2.16")
            {
                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat32.Count; CurrentRange++)
                {
                    object allowedValueFrom = Help.GetValueByDataType(XmlConvert.ToString(allowedValueListFloat32[CurrentRange].From), sdatatype, bitlength);
                    if (null == allowedValueFrom)
                    {
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_5"), allowedValueListFloat32[CurrentRange].From, sdatatype);
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_5");
                        }
                        errList.Add("e" + "0x00010022_5" + msg);
                    }
                    else if (allowedValueListFloat32[CurrentRange].From != allowedValueListFloat32[CurrentRange].To)
                    {
                        object allowedValueTo = Help.GetValueByDataType(XmlConvert.ToString(allowedValueListFloat32[CurrentRange].To), sdatatype, bitlength);
                        if (null != allowedValueTo)
                        {
                            continue;
                        }
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_5"), allowedValueListFloat32[CurrentRange].To, sdatatype);
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_5");
                        }
                        errList.Add("e" + "0x00010022_5" + msg);

                    }
                }
            }
            else
            {
                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat64.Count; CurrentRange++)
                {
                    object allowedValueFrom = Help.GetValueByDataType(XmlConvert.ToString(allowedValueListFloat64[CurrentRange].From), sdatatype, bitlength);
                    if (null == allowedValueFrom)
                    {
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_5"), allowedValueListFloat64[CurrentRange].From, sdatatype);
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_5");
                        }
                        errList.Add("e" + "0x00010022_5" + msg);
                    }
                    else if (allowedValueListFloat64[CurrentRange].From != allowedValueListFloat64[CurrentRange].To)
                    {
                        object allowedValueTo = Help.GetValueByDataType(XmlConvert.ToString(allowedValueListFloat64[CurrentRange].To), sdatatype, bitlength);
                        if (null != allowedValueTo)
                        {
                            continue;
                        }
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        // "The value ({0}) specified by 'AllowedValues' does not comply with the 'DataType' ("{1}")."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_5"), allowedValueListFloat64[CurrentRange].To, sdatatype);
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_5");
                        }
                        errList.Add("e" + "0x00010022_5" + msg);

                    }
                }
            }
        }

        /// <summary>
        /// (4) The 'DefaultValue' must be available in the 'Content' list.
        /// 
        /// </summary>
        /// <returns>None.</returns>
        protected virtual void CheckDefaultValueAgainstAssignContentFloat(XElement en, string sdefaultvalue, object defaultvalue, string sdatatype,
                                                                          List<float> nlcontentFloat32, List<double> nlcontentFloat64, ref IList<string> errList)
        {
            // The 'DefaultValue' must be available in the 'Content' list.
            bool error = false;

            if (defaultvalue != null)
            {
                if (sdatatype == "Float32" || sdatatype == "E2" || sdatatype == "C4" || sdatatype == "Unipolar2.16")
                {
                    if (nlcontentFloat32 != null && nlcontentFloat32.Count > 0 && !nlcontentFloat32.Contains(Convert.ToSingle(defaultvalue, CultureInfo.InvariantCulture)))
                    {
                        error = true;
                    }
                }
                else if (nlcontentFloat64 != null && nlcontentFloat64.Count > 0 && !nlcontentFloat64.Contains(Convert.ToDouble(defaultvalue, CultureInfo.InvariantCulture)))
                {
                    error = true;
                }
            }

            if (!error)
            {
                return;
            }

            // "The 'DefaultValue' ({0}) is not available from 'ValueItem' assignments 'Content' attribute."
            string msg = String.Format(Help.GetMessageString("M_0x00010022_8"), sdefaultvalue);
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
            IXmlLineInfo xli = en.Attribute(Attributes.s_DefaultValue);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_8");
            }
            errList.Add("e" + "0x00010022_8" + msg);

        }

        /// <summary>
        /// (5) For float: Each entry in the 'AllowedValues' must be value (not a range), when a 'Content' attribute entry is referenced.
        ///     Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
        /// 
        /// </summary>
        /// <returns>None.</returns>
        protected virtual void CheckAllowedValuesAgainstAssignContentFloat(XElement en, string sdatatype,
                                                                           List<ValueListHelper.Float32ValueRangeT> allowedValueListFloat32,
                                                                           List<ValueListHelper.Float64ValueRangeT> allowedValueListFloat64,
                                                                           List<float> nlcontentFloat32, List<double> nlcontentFloat64,
                                                                           ref IList<string> errList)
        {
            var xli = (IXmlLineInfo)en.Attribute(Attributes.s_AllowedValues);


            if (sdatatype == "Float32" || sdatatype == "E2" || sdatatype == "C4" || sdatatype == "Unipolar2.16")
            {
                if (nlcontentFloat32.Count <= 0)
                    return;

                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat32.Count; CurrentRange++)
                {
                    // Each entry in the 'AllowedValues' must be value (not a range), when a 'Content' attribute entry is referenced.
                    if (allowedValueListFloat32[CurrentRange].From != allowedValueListFloat32[CurrentRange].To)
                    {
                        // "For 'DataType' = "Float32" or 'DataType' = "Float64" 'AllowedValues' must not contain a range: {0}..{1}."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_b"),
                                                   allowedValueListFloat32[CurrentRange].From, allowedValueListFloat32[CurrentRange].To);
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.PlugRules,
                                "0x00010022_b");
                        }
                        errList.Add("e" + "0x00010022_b" + msg);
                    }

                    // Each entry in the 'AllowedValues' must comply with a 'Content' attribute entry.
                    if (!nlcontentFloat32.Contains(allowedValueListFloat32[CurrentRange].From))
                    {
                        // "The value ({0}) specified by 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_9"), allowedValueListFloat32[CurrentRange].From);
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_9");
                        }
                        errList.Add("e" + "0x00010022_9" + msg);
                    }
                }
            }
            else
            {
                if (nlcontentFloat64.Count <= 0)
                    return;

                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat64.Count; CurrentRange++)
                {
                    // Each entry in the 'AllowedValues' must be value (not a range), when a 'Content' attribute entry is referenced.
                    if (allowedValueListFloat64[CurrentRange].From != allowedValueListFloat64[CurrentRange].To)
                    {
                        // "For 'DataType' = "Float32" or 'DataType' = "Float64" 'AllowedValues' must not contain a range: {0}..{1}."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_b"),
                                                   allowedValueListFloat64[CurrentRange].From, allowedValueListFloat64[CurrentRange].To);
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.PlugRules,
                                "0x00010022_b");
                        }
                        errList.Add("e" + "0x00010022_b" + msg);
                    }

                    // Each entry in the 'AllowedValues' must comply with a 'Content' attribute entry.
                    if (!nlcontentFloat64.Contains(allowedValueListFloat64[CurrentRange].From))
                    {
                        // "The value ({0}) specified by 'AllowedValues' isn't available from 'ValueItem' assignments 'Content' attribute."
                        string msg = String.Format(Help.GetMessageString("M_0x00010022_9"), allowedValueListFloat64[CurrentRange].From);
                        string xpath = Help.GetXPath(en.Attribute(Attributes.s_AllowedValues));
                        if (xli != null)
                        {
                            Store.CreateAndAnnounceReport(
                                ReportTypes.GSD_RT_Error,
                                xli.LineNumber,
                                xli.LinePosition,
                                msg,
                                xpath,
                                ReportCategories.TypeSpecific,
                                "0x00010022_9");
                        }
                        errList.Add("e" + "0x00010022_9" + msg);
                    }
                }
            }
        }

        /// <summary>
        /// (6) The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
        /// 
        /// </summary>
        /// <returns>None.</returns>
        protected virtual void CheckDefaultValueAgainstAssignContentFloat(XElement en, string sdatatype, string sdefaultvalue, object defaultvalue,
                                                                          List<ValueListHelper.Float32ValueRangeT> allowedValueListFloat32,
                                                                          List<ValueListHelper.Float64ValueRangeT> allowedValueListFloat64, ref IList<string> errList)
        {
            // The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
            if (defaultvalue == null)
                return;

            bool valueFoundInRange = false;
            if (sdatatype == "Float32" || sdatatype == "E2" || sdatatype == "C4" || sdatatype == "Unipolar2.16")
            {
                if (allowedValueListFloat32.Count <= 0)
                    return;

                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat32.Count; CurrentRange++)
                {
                    if (Convert.ToSingle(defaultvalue) >= allowedValueListFloat32[CurrentRange].From &&
                        Convert.ToSingle(defaultvalue) <= allowedValueListFloat32[CurrentRange].To)
                    {
                        valueFoundInRange = true;
                        break;
                    }
                }
            }
            else
            {
                if (allowedValueListFloat64.Count <= 0)
                    return;

                for (int CurrentRange = 0; CurrentRange < allowedValueListFloat64.Count; CurrentRange++)
                {
                    if (Convert.ToDouble(defaultvalue) >= allowedValueListFloat64[CurrentRange].From &&
                        Convert.ToDouble(defaultvalue) <= allowedValueListFloat64[CurrentRange].To)
                    {
                        valueFoundInRange = true;
                        break;
                    }
                }
            }

            if (valueFoundInRange)
            {
                return;
            }

            // "The 'DefaultValue' ({0}) is not available from the 'AllowedValues' ({1})."
            string msg = String.Format(Help.GetMessageString("M_0x00010022_a"), sdefaultvalue, Help.GetAttributeValueFromXElement(en, Attributes.s_AllowedValues));
            string xpath = Help.GetXPath(en.Attribute(Attributes.s_DefaultValue));
            var xli = (IXmlLineInfo)en.Attribute(Attributes.s_DefaultValue);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00010022_a");
            }
            errList.Add("e" + "0x00010022_a" + msg);

        }

        /// <summary>
        /// (1) The value of the 'DefaultValue' attribute on the 'Ref' element must comply,
        ///     if available, with the specified data type from the 'DataType' attribute.
        /// (2) Each 'Content' attribute entry from the 'ValueItem'
        ///     assignments must comply with the data type from the 'DataType' attribute.
        /// (3) Each entry in the 'AllowedValues' list must comply with the specified
        ///     data type from the 'DataType' attribute.
        /// (4) The 'DefaultValue' must be available in the 'Content' list.
        /// (5) For float: Each entry in the 'AllowedValues' must be value (not a range), when a 'Content' attribute entry is referenced.
        ///     Each entry in the 'AllowedValues' list must comply with a 'Content' attribute entry.
        /// (6) The 'DefaultValue' must be available from the 'AllowedValues' if any entry exists.
        /// 
        /// </summary>
        /// <returns>None.</returns>
        protected override void CheckNumericDataTypes(XElement en, ref IList<string> errList)
        {
            // Get 'DataType' attribute entry (required).
            string sdatatype = Help.GetAttributeValueFromXElement(en, Attributes.s_DataType);

            // Get 'ValueItemTarget' attribute entry (optional).
            string svaluegsdid = Help.GetAttributeValueFromXElement(en, Attributes.s_ValueItemTarget);

            // Get 'BitLength' attribute entry (optional). Normally only needed for data type 'BitArea'.
            uint bitlength = 1;	// Is default!
            string sbitlength = Help.GetAttributeValueFromXElement(en, Attributes.s_BitLength);
            if (!String.IsNullOrEmpty(sbitlength))
                bitlength = XmlConvert.ToUInt32(sbitlength);

            // Get 'DefaultValue' attribute entry (required).
            var adefaultvalue = en.Attribute(Attributes.s_DefaultValue);
            string sdefaultvalue = null;
            object defaultvalue = null;
            if (adefaultvalue != null)
            {
                sdefaultvalue = adefaultvalue.Value;
                defaultvalue = Help.GetValueByDataType(sdefaultvalue, sdatatype, bitlength);
            }

            // Get the 'AllowedValues' attribute entry (required).
            var allowedvalues = en.Attribute(Attributes.s_AllowedValues);

            switch (sdatatype)
            {
                case "Float32":
                case "Float64":
                case "E2":
                case "C4":
                case "Unipolar2.16":
                    {
                        List<ValueListHelper.Float32ValueRangeT> allowedValueListFloat32 = null;
                        List<ValueListHelper.Float64ValueRangeT> allowedValueListFloat64 = null;
                        if (sdatatype == "Float64")
                            allowedValueListFloat64 = ValueListHelper.NormalizeValueListFloat64(allowedvalues, Store);
                        else
                            allowedValueListFloat32 = ValueListHelper.NormalizeValueListFloat32(allowedvalues, Store);

                        List<float> nlcontentFloat32 = new();
                        List<double> nlcontentFloat64 = new();

                        CheckDefaultValueAgainstDataType(en, sdefaultvalue, defaultvalue, sdatatype, bitlength, ref errList);                                      // (1)
                        CheckAssignContentAgainstDataTypeFloat(en, svaluegsdid, sdatatype, bitlength, ref nlcontentFloat32, ref nlcontentFloat64, ref errList);    // (2)
                        CheckAllowedValuesAgainstDataTypeFloat(en, allowedValueListFloat32, allowedValueListFloat64, sdatatype, bitlength, ref errList);           // (3)
                        CheckDefaultValueAgainstAssignContentFloat(en, sdefaultvalue, defaultvalue, sdatatype, nlcontentFloat32, nlcontentFloat64, ref errList);   // (4)
                        CheckAllowedValuesAgainstAssignContentFloat(
                            en, sdatatype, allowedValueListFloat32, allowedValueListFloat64, nlcontentFloat32, nlcontentFloat64, ref errList);                     // (5)
                        CheckDefaultValueAgainstAssignContentFloat(
                            en, sdatatype, sdefaultvalue, defaultvalue, allowedValueListFloat32, allowedValueListFloat64, ref errList);                            // (6)
                    }
                    break;

                case "Integer8":
                case "Integer16":
                case "N2":
                case "X2":
                case "Integer32":
                case "N4":
                case "X4":
                case "Integer64":
                    {
                        List<ValueListHelper.Int64ValueRangeT> allowedValueListInt64 = ValueListHelper.NormalizeValueListInt64(allowedvalues, Store);
                        List<Int64> nlcontentInt64 = new();

                        CheckDefaultValueAgainstDataType(en, sdefaultvalue, defaultvalue, sdatatype, bitlength, ref errList);              // (1)
                        CheckAssignContentAgainstDataTypeInt64(en, svaluegsdid, sdatatype, bitlength, ref nlcontentInt64, ref errList);    // (2)
                        CheckAllowedValuesAgainstDataTypeInt64(en, allowedValueListInt64, sdatatype, bitlength, ref errList);              // (3)
                        CheckDefaultValueAgainstAssignContentInt64(en, sdefaultvalue, defaultvalue, nlcontentInt64, ref errList);          // (4)
                        CheckAllowedValuesAgainstAssignContentInt64(en, allowedValueListInt64, nlcontentInt64, ref errList);               // (5)
                        CheckDefaultValueAgainstAllowedValuesInt64(en, sdefaultvalue, defaultvalue, allowedValueListInt64, ref errList);   // (6)
                    }
                    break;

                default:
                    {
                        List<ValueListHelper.UInt64ValueRangeT> allowedValueListUInt64 = ValueListHelper.NormalizeValueListUInt64(allowedvalues, Store);
                        List<UInt64> nlcontentUInt64 = new();

                        CheckDefaultValueAgainstDataType(en, sdefaultvalue, defaultvalue, sdatatype, bitlength, ref errList);              // (1)
                        CheckAssignContentAgainstDataTypeUInt64(en, svaluegsdid, sdatatype, bitlength, ref nlcontentUInt64, ref errList);  // (2)
                        CheckAllowedValuesAgainstDataTypeUInt64(en, allowedValueListUInt64, sdatatype, bitlength, ref errList);            // (3)
                        CheckDefaultValueAgainstAssignContentUInt64(en, sdefaultvalue, defaultvalue, nlcontentUInt64, ref errList);        // (4)
                        CheckAllowedValuesAgainstAssignContentUInt64(en, allowedValueListUInt64, nlcontentUInt64, ref errList);            // (5)
                        CheckDefaultValueAgainstAllowedValuesUInt64(en, sdefaultvalue, defaultvalue, allowedValueListUInt64, ref errList); // (6)
                    }
                    break;
            }

        }
        /// Check number: CN_0x00010027
        /// The check for DataTypes which requires a length against the given length is unitized
        /// and placed now in method FindMsgForDataTypesRequiresLength().
        /// This entails that errors are raised with error numbers from FindMsgForDataTypesRequiresLength()
        /// which originally was build for check CN_0x0001000C.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010027()
        {
            // Find all DataTypes which requires a length.
            string xp = ".//gsddef:Ref[" + DataTypesRequiresLength + "]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            // Check all DataTypes which requires a length against the given length.
            foreach (var en in nl)
            {
                FindMsgForDataTypesRequiresLength(en);
            }

            xp = ".//gsddef:Ref[@Length!=0 and @DataType='OctetString']";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                string strLength = Help.GetAttributeValueFromXElement(en, Attributes.s_Length);
                UInt16 length = 0;
                if (!string.IsNullOrEmpty(strLength))
                    length = XmlConvert.ToUInt16(strLength);

                // Check that the octet string has the right format
                string sDefaultValue = Help.GetAttributeValueFromXElement(en, Attributes.s_DefaultValue);
                String octetStringPattern = @"^(0x[0-9a-fA-F][0-9a-fA-F],)*0x[0-9a-fA-F][0-9a-fA-F]$";
                Match octetStringMatch = Regex.Match(sDefaultValue, octetStringPattern);

                string octetString = octetStringMatch.ToString();
                if (!string.IsNullOrEmpty(octetString))
                {
                    // Find the number of octets
                    int index = 0;
                    UInt16 numberOctets = 1;
                    while (index != -1)
                    {
                        index = octetString.IndexOf(Constants.s_Comma, StringComparison.InvariantCulture);
                        if (index != -1)
                        {
                            numberOctets++;
                            octetString = octetString.Remove(index, 1);
                        }
                    }

                    if (numberOctets != length)
                    {
                        // "The attribute 'Ref/@DefaultValue' contains {0} octets, but does not contain the appropriate number of {1} octets."
                        string msg = String.Format(Help.GetMessageString("M_0x00010027_5"), numberOctets, length);
                        string xpath = Help.GetXPath(en);
                        var lineInfo = (IXmlLineInfo)en;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010027_5");
                    }
                }
            }

            xp = ".//gsddef:Ref[@Length!=0 and @DataType='VisibleString']";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                string strLength = Help.GetAttributeValueFromXElement(en, Attributes.s_Length);
                UInt16 length = 0;
                if (!string.IsNullOrEmpty(strLength))
                    length = XmlConvert.ToUInt16(strLength);

                string sDefaultValue = Help.GetAttributeValueFromXElement(en, Attributes.s_DefaultValue);
                UInt16 stringLength = (UInt16)sDefaultValue.Length;
                if (stringLength > length)
                {
                    // "The attribute 'Ref/@DefaultValue' contains {0} characters, but must not contain more than {1} characters."
                    string msg = String.Format(Help.GetMessageString("M_0x00010027_6"), stringLength, length);
                    string xpath = Help.GetXPath(en);
                    var lineInfo = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010027_6");
                }
            }

            return true;
        }

        protected bool IsSubslot0(XElement submod, out XAttribute subslot)
        {
            String subslot0Pattern = @"^(.* )?0+((\.\.| ).*)?$";

            subslot = null;
            string xp = "./@FixedInSubslots | ./@AllowedInSubslots | ./@UsedInSubslots | ./@PhysicalSubslots";
            var nl = (IEnumerable)submod.XPathEvaluate(xp, Nsmgr);
            foreach (XAttribute an in nl)
            {
                Match subslot0Match = Regex.Match(an.Value, subslot0Pattern);
                string subslot0String = subslot0Match.ToString();

                if (!string.IsNullOrEmpty(subslot0String))
                {
                    subslot = an;
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks, if some submodule at a DAP without pull module alarm supported has a subslot 0 defined.
        /// 
        /// </summary>
        /// <returns>True, if a submodule with subslot 0 found.</returns>
        protected bool SubmoduleWithSubslot0(XElement dap, Dictionary<string, ModuleSubslot0T> allModules, out XAttribute subslot)
        {
            subslot = null;

            // Test this dap for PhysicalSubslots = 0
            if (IsSubslot0(dap, out subslot))
            {
                return true;
            }

            // (1) Check all VirtualSubmoduleItems at the DAP
            var virtualSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
            if (virtualSubmoduleList != null)
            {
                var virtualSubmoduleItems = virtualSubmoduleList.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
                {
                    if (IsSubslot0(virtualSubmoduleItem, out subslot))
                    {
                        return true;
                    }
                }
            }

            // (2) Check all UseableSubmodules at the DAP
            var useableSubmodules = dap.Element(NamespaceGsdDef + Elements.s_UseableSubmodules);
            if (useableSubmodules != null)
            {
                var submoduleItemRefs = useableSubmodules.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
                foreach (var submoduleItemRef in submoduleItemRefs)
                {
                    if (IsSubslot0(submoduleItemRef, out subslot))
                    {
                        return true;
                    }
                }
            }

            // (3) Check all VirtualSubmoduleItem of modules at the DAP
            // (4) Check all UseableSubmodules of modules at the DAP
            var useableModules = dap.Element(NamespaceGsdDef + Elements.s_UseableModules);
            if (useableModules != null)
            {
                var moduleItemRefs = useableModules.Elements(NamespaceGsdDef + Elements.s_ModuleItemRef);
                foreach (var moduleItemRef in moduleItemRefs)
                {
                    string ID = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                    if (!allModules.TryGetValue(ID, out ModuleSubslot0T ms0))
                    {
                        // this is an error which is detected elsewhere; ignore it here
                    }
                    else
                    {
                        if (ms0.State == Subslot0StateT.UnTested)
                        {
                            // Test this module for PhysicalSubslots = 0
                            if (IsSubslot0(ms0.Module, out subslot))
                            {
                                return true;
                            }

                            // (3) Check all VirtualSubmoduleItem of modules at the DAP
                            var virtualSubmoduleListModule = ms0.Module.Element(NamespaceGsdDef + Elements.s_VirtualSubmoduleList);
                            if (virtualSubmoduleListModule != null)
                            {
                                var virtualSubmoduleItems = virtualSubmoduleListModule.Elements(NamespaceGsdDef + Elements.s_VirtualSubmoduleItem);
                                foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
                                {
                                    if (IsSubslot0(virtualSubmoduleItem, out ms0.Attr))
                                    {
                                        ms0.State = Subslot0StateT.Subslot0Found;
                                        break;
                                    }
                                }
                            }
                            if (ms0.State != Subslot0StateT.Subslot0Found)
                            {
                                // (4) Check all UseableSubmodules of modules at the DAP
                                var useableSubmodulesModule = ms0.Module.Element(NamespaceGsdDef + Elements.s_UseableSubmodules);
                                if (useableSubmodulesModule != null)
                                {
                                    var submoduleItemRefs = useableSubmodulesModule.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
                                    foreach (var submoduleItemRef in submoduleItemRefs)
                                    {
                                        if (IsSubslot0(submoduleItemRef, out ms0.Attr))
                                        {
                                            ms0.State = Subslot0StateT.Subslot0Found;
                                            break;
                                        }
                                    }
                                }
                            }
                            if (ms0.State != Subslot0StateT.Subslot0Found) ms0.State = Subslot0StateT.Subslot0NotFound;
                            allModules[ID] = ms0;
                        }
                        if (ms0.State == Subslot0StateT.Subslot0Found)
                        {
                            subslot = ms0.Attr;
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        protected enum Subslot0StateT
        {
            UnTested,

            Subslot0NotFound,

            Subslot0Found
        }

        protected struct ModuleSubslot0T
        {
            public XElement Module;

            public Subslot0StateT State;

            public XAttribute Attr; // If State == NotOK: The first offending attribute, else null


        }

        protected override bool CheckSubslotFor0()
        {
            // prepare Module dictionary
            string xp = ".//gsddef:ModuleItem";
            var modules = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            modules = Help.TryRemoveXElementsUnderXsAny(modules, Nsmgr, Gsd);
            Dictionary<string, ModuleSubslot0T> allModules = new();
            ModuleSubslot0T ms0;
            ms0.State = Subslot0StateT.UnTested;
            ms0.Attr = null;
            foreach (var module in modules)
            {
                string id = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(module, Attributes.ID));
                if (allModules.ContainsKey(id))
                {
                    // this is an error which is detected elsewhere; ignore it here
                }
                else
                {
                    ms0.Module = module;
                    allModules.Add(id, ms0);
                }
            }

            // check
            xp = ".//gsddef:DeviceAccessPointItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                if (SubmoduleWithSubslot0(dap, allModules, out XAttribute subslot))
                {
                    if (Help.CheckSchemaVersion(subslot, SupportedGsdmlVersion))
                    {
                        // "The subslot 0 is not allowed."
                        string msg = Help.GetMessageString("M_0x00010043_1");
                        string xpath = Help.GetXPath(subslot);
                        IXmlLineInfo xli = subslot;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010043_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010103
        /// If "InterfaceSubmoduleItem/@IsochroneModeSupported" is set to "true",
        /// then RT_CLASS_2 or RT_CLASS_3 must be supported.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010103()
        {
            // Find all interface submodules with IsochroneModeSupported = "true"
            var submodules = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem);
            submodules = Help.TryRemoveXElementsUnderXsAny(submodules, Nsmgr, Gsd);
            foreach (var submodule in submodules)
            {
                string strIsochroneModeSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_IsochroneModeSupported);
                bool bIsochroneModeSupported = false;
                if (!string.IsNullOrEmpty(strIsochroneModeSupported))
                    bIsochroneModeSupported = XmlConvert.ToBoolean(strIsochroneModeSupported);
                if (bIsochroneModeSupported)
                {
                    bool error = true;

                    string rtClass = Help.GetAttributeValueFromXElement(submodule, Attributes.s_SupportedRTClass);
                    string rtClasses = Help.GetAttributeValueFromXElement(submodule, Attributes.s_SupportedRTClasses);

                    if (rtClasses.Length > 0)
                    {
                        if ((rtClasses.IndexOf("RT_CLASS_2", StringComparison.InvariantCulture) >= 0) || (rtClasses.IndexOf("RT_CLASS_3", StringComparison.InvariantCulture) >= 0))
                            error = false;
                    }
                    else if (rtClass.Length > 0)
                    {
                        if (rtClass == "Class2" || rtClass == "Class3")
                            error = false;
                    }

                    if (error)
                    {
                        if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                        {
                            // "The 'InterfaceSubmoduleItem' supports IsochroneMode, but the attribute
                            // 'SupportedRT_Class' is not set to "Class2" respectively "Class3" or
                            // 'SupportedRT_Classes' does not contain "RT_CLASS_2" respectively "RT_CLASS_3"."
                            string msg = Help.GetMessageString("M_0x00010103_2");
                            string xpath = Help.GetXPath(submodule);
                            IXmlLineInfo xli = submodule;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010103_2");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010104
        /// If RT_CLASS_2 or RT_CLASS_3 is supported, "InterfaceSubmoduleItem/SynchronisationMode" element should exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010104()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem" +
                        "[(@SupportedRT_Class or @SupportedRT_Classes) and not(./gsddef:SynchronisationMode)]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                bool error = false;

                string rtClass = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClass);
                string rtClasses = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClasses);

                if (rtClasses.Length > 0)
                {
                    if ((rtClasses.IndexOf("RT_CLASS_2", StringComparison.InvariantCulture) >= 0) || (rtClasses.IndexOf("RT_CLASS_3", StringComparison.InvariantCulture) >= 0))
                        error = true;
                }
                else if (rtClass.Length > 0)
                {
                    if (rtClass == "Class2" || rtClass == "Class3")
                        error = true;
                }

                if (error)
                {
                    // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_2" or "RT_CLASS_3",
                    // but the element 'SynchronisationMode' is not set."
                    string msg = Help.GetMessageString("M_0x00010104_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportType_0X000101041, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010104_1");
                }
            }

            xp = ".//gsddef:SynchronisationMode";
            var nl2 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl2 = Help.TryRemoveXElementsUnderXsAny(nl2, Nsmgr, Gsd);
            foreach (var en in nl2)
            {
                var lineInfo = (IXmlLineInfo)en;

                var interfaceSubmoduleItem = en.Parent;
                string supportedRTClassesStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
                if (!string.IsNullOrEmpty(supportedRTClassesStr))
                {
                    var supportedRTClasses = new List<string>(supportedRTClassesStr.Split(Constants.s_Semicolon.ToCharArray()));
                    if (!supportedRTClasses.Contains("RT_CLASS_2") && !supportedRTClasses.Contains("RT_CLASS_3"))
                    {
                        // "The element 'SynchronisationMode' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_2" or "RT_CLASS_3"."
                        string msg = Help.GetMessageString("M_0x00010104_2");
                        string xpath = Help.GetXPath(en);
                        Store.CreateAndAnnounceReport(ReportType_0X000101042, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010104_2");
                    }
                }
                else
                {
                    string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
                    if (0 != string.Compare(supportedRTClass, "Class2", StringComparison.InvariantCulture) && 0 != string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
                    {

                        // "The element 'SynchronisationMode' is set, but the 'InterfaceSubmoduleItem' does not support RT_CLASS_2 or RT_CLASS_3."
                        string msg = Help.GetMessageString("M_0x00010104_2");
                        string xpath = Help.GetXPath(en);
                        Store.CreateAndAnnounceReport(ReportType_0X000101042, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010104_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010105
        /// If RT_CLASS_3 is supported, "InterfaceSubmoduleItem/RT_Class3Properties" element should exist.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010105()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/" +
                        "gsddef:InterfaceSubmoduleItem[(@SupportedRT_Class or @SupportedRT_Classes) and not(./gsddef:RT_Class3Properties)]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en in nl1)
            {
                bool error = false;

                string rtClass = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClass);
                string rtClasses = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClasses);

                if (rtClasses.Length > 0)
                {
                    if (rtClasses.IndexOf("RT_CLASS_3", StringComparison.InvariantCulture) >= 0)
                        error = true;
                }
                else if (rtClass.Length > 0)
                {
                    if (rtClass == "Class3")
                        error = true;
                }

                if (error)
                {
                    // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3",
                    // but the element 'RT_Class3Properties' is not set."
                    string msg = Help.GetMessageString("M_0x00010105_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010105_1");
                }
            }

            xp = ".//gsddef:RT_Class3Properties";
            var nl2 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl2 = Help.TryRemoveXElementsUnderXsAny(nl2, Nsmgr, Gsd);
            foreach (var en in nl2)
            {
                var lineInfo = (IXmlLineInfo)en;

                var interfaceSubmoduleItem = en.Parent;
                string supportedRTClassesStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
                if (!string.IsNullOrEmpty(supportedRTClassesStr))
                {
                    var supportedRTClasses = new List<string>(supportedRTClassesStr.Split(Constants.s_Semicolon.ToCharArray()));
                    if (!supportedRTClasses.Contains("RT_CLASS_3"))
                    {
                        // "The element 'RT_Class3Properties' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_3"."
                        string msg = Help.GetMessageString("M_0x00010105_2");
                        string xpath = Help.GetXPath(en);
                        Store.CreateAndAnnounceReport(ReportType_0X000101052, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010105_2");
                    }
                }
                else
                {
                    string supportedRTClass = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClass);
                    if (0 != string.Compare(supportedRTClass, "Class3", StringComparison.InvariantCulture))
                    {
                        // "The element 'RT_Class3Properties' is set, but the 'InterfaceSubmoduleItem' does not support "RT_CLASS_3"."
                        string msg = Help.GetMessageString("M_0x00010105_2");
                        string xpath = Help.GetXPath(en);
                        Store.CreateAndAnnounceReport(ReportType_0X000101052, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010105_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010106
        /// If "InterfaceSubmoduleItem/@SupportedRT_Class" is set to "Class3",
        /// then"MaxPortTxDelay" and "MaxPortRxDelay" should exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010106()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem[" +
                        "(../gsddef:InterfaceSubmoduleItem/@SupportedRT_Class or ../gsddef:InterfaceSubmoduleItem/" +
                        "@SupportedRT_Classes) and not(@MaxPortRxDelay and @MaxPortTxDelay)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                xp = "../gsddef:InterfaceSubmoduleItem";
                var en2 = en.XPathSelectElement(xp, Nsmgr);

                bool error = false;

                string rtClass = Help.GetAttributeValueFromXElement(en2, Attributes.s_SupportedRTClass);
                string rtClasses = Help.GetAttributeValueFromXElement(en2, Attributes.s_SupportedRTClasses);

                if (rtClasses.Length > 0)
                {
                    if (rtClasses.IndexOf("RT_CLASS_3", StringComparison.InvariantCulture) >= 0)
                        error = true;
                }
                else
                {
                    if (rtClass == "Class3")
                        error = true;
                }

                if (error)
                {
                    // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the related 'PortSubmoduleItem'
                    // element does not have the needed attributes 'MaxPortRxDelay' respectively 'MaxPortTxDelay'."
                    string msg = Help.GetMessageString("M_0x00010106_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010106_1");
                }
            }

            // ------------------------------------------------------------

            // Find all Modules (IDs) with Ports without MaxPortRxDelay or MaxPortTxDelay.
            var nlPorts = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem).Descendants(NamespaceGsdDef + Elements.s_PortSubmoduleItem)
                         .Where(
                             x =>
                                 x.Attribute(Attributes.s_MaxPortRxDelay) == null ||
                                 x.Attribute(Attributes.s_MaxPortTxDelay) == null);
            nlPorts = Help.TryRemoveXElementsUnderXsAny(nlPorts, Nsmgr, Gsd);

            // Collect found Module IDs.
            var moduleids = new List<string>();
            foreach (XElement port in nlPorts)
            {
                XAttribute an = port.Parent.Parent.Attribute(Attributes.ID);
                if (!moduleids.Contains(an.Value))
                    moduleids.Add(an.Value);
            }

            // If no module is found which has ports with no MaxPortRxDelay or MaxPortTxDelay attributes!
            if (moduleids.Count <= 0)
                return true; // ---------->

            // Find all InterfaceSubmoduleItem elements, which supports RT_CLASS_3 (IRT).
            xp = ".//gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem[./@SupportedRT_Class or ./@SupportedRT_Classes]";
            nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                bool supportsRTClass3 = false;

                string rtClass = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClass);
                string rtClasses = Help.GetAttributeValueFromXElement(en, Attributes.s_SupportedRTClasses);

                if (rtClasses.Length > 0)
                {
                    if (rtClasses.IndexOf("RT_CLASS_3", StringComparison.InvariantCulture) >= 0)
                        supportsRTClass3 = true;
                }
                else if (rtClass.Length > 0)
                {
                    if (rtClass == "Class3")
                        supportsRTClass3 = true;
                }

                if (supportsRTClass3)
                {
                    // Check PortSubmoduleItem parameters (MaxPortRxDelay and MaxPortTxDelay must exist) on
                    // used ModuleItems.
                    xp = "../../gsddef:UseableModules/gsddef:ModuleItemRef";
                    var nltemp = en.XPathSelectElements(xp, Nsmgr);

                    foreach (var en2 in nltemp)
                    {
                        string moduleref = Help.GetAttributeValueFromXElement(en2, Attributes.s_ModuleItemTarget);
                        if (moduleids.Contains(moduleref))
                        {
                            // "The 'InterfaceSubmoduleItem' supports "RT_CLASS_3", but the 'PortSubmoduleItem'
                            // element from the referenced 'ModuleItem' does not have the needed attributes
                            // 'MaxPortRxDelay' respectively 'MaxPortTxDelay'."
                            string msg = Help.GetMessageString("M_0x00010106_2");
                            string xpath = Help.GetXPath(en2);
                            IXmlLineInfo xli = en2;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00010106_2");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00010136
        /// 
        /// TFS #4051208:
        /// The InterfaceSubmoduleItem attribute SubslotNumber was added in GSDML V2.0 with a
        /// fixed (correct) value of 32768.
        /// 
        /// Other values (0x8000, 0x8100, 0x8200, .. 0x8F00) were allowed starting with GSDML V2.1.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected override bool CheckCn_0X00010136()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                // InterfaceSubmoduleItem
                UInt16 interfaceSubslot = 0x8000;    // Default value
                XElement ism = dap.XPathSelectElement(".//gsddef:InterfaceSubmoduleItem", Nsmgr);
                if (ism != null)
                {
                    string strInterfaceSubslot = Help.GetAttributeValueFromXElement(ism, "SubslotNumber");
                    if (!string.IsNullOrEmpty(strInterfaceSubslot))
                    {
                        interfaceSubslot = XmlConvert.ToUInt16(strInterfaceSubslot);
                    }
                    if (interfaceSubslot != 0x8000 && interfaceSubslot != 0x8100 && interfaceSubslot != 0x8200 && interfaceSubslot != 0x8300 &&
                        interfaceSubslot != 0x8400 && interfaceSubslot != 0x8500 && interfaceSubslot != 0x8600 && interfaceSubslot != 0x8700 &&
                        interfaceSubslot != 0x8800 && interfaceSubslot != 0x8900 && interfaceSubslot != 0x8A00 && interfaceSubslot != 0x8B00 &&
                        interfaceSubslot != 0x8C00 && interfaceSubslot != 0x8D00 && interfaceSubslot != 0x8E00 && interfaceSubslot != 0x8F00)
                    {
                        if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                        {
                            // "From GSDML V2.1 on 'InterfaceSubmoduleItem/@SubslotNumber' must have one of the following values:
                            // 32768 (0x8000 � Interface 1), 33024 (0x8100 � Interface 2) to 36608 (0x8F00 � Interface 16)."
                            string msg = Help.GetMessageString("M_0x00010136_2");
                            string xpath = Help.GetXPath(ism);
                            var lineInfo = (IXmlLineInfo)ism;
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x00010136_2");
                        }
                    }
                }
            }

            return true;
        }

        #region Submodule Plug Rules

        protected virtual bool CheckCn_0X00011106()
        {
            bool succeeded = true;
            // NOTE: Check plug rules for (1) 'DeviceAccessPointItem' and 'ModuleItem' elements.
            string xp = ".//gsddef:DeviceAccessPointItem|//gsddef:ModuleItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var en in nl)
            {
                XElement useableSubmodules = en.XPathSelectElement("gsddef:UseableSubmodules", Nsmgr);

                // If there is an UseableSubmodules element, a PhysicalSubslots attribute must be present
                XAttribute physicalSubslotsNode = en.Attribute(Attributes.s_PhysicalSubslots);
                if (physicalSubslotsNode == null)
                {
                    if (useableSubmodules != null)
                    {
                        // "The 'DeviceAccessPointItem' or 'ModuleItem' has an 'UseableSubmodules'
                        // element but there are no 'PhysicalSubslots' defined."
                        string msg = Help.GetMessageString("M_0x00011106_1");
                        string xpath = Help.GetXPath(en);
                        IXmlLineInfo xli = en;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, "0x00011106_1");
                    }
                }
                else
                {
                    List<ValueListHelper.ValueRangeT> physicalSubslotList = ValueListHelper.NormalizeValueList(physicalSubslotsNode, Store);

                    if (useableSubmodules != null)
                    {
                        var submoduleRefs = useableSubmodules.XPathSelectElements("gsddef:SubmoduleItemRef", Nsmgr);
                        List<ValueListHelper.ValueRangeT> submodAllFixedSlots = new();

                        List<ValueListHelper.ValueRangeT> submodAllUsedSlots = new();

                        foreach (var submodule in submoduleRefs)
                        {
                            XAttribute attrAllowed = submodule.Attribute("AllowedInSubslots");
                            XAttribute attrUsed = submodule.Attribute("UsedInSubslots");
                            XAttribute attrFixed = submodule.Attribute("FixedInSubslots");

                            CompareSubslotLists(attrAllowed, physicalSubslotsNode, true);
                            CompareSubslotLists(attrUsed, physicalSubslotsNode, true);
                            CompareSubslotLists(attrFixed, physicalSubslotsNode, true);
                            CompareSubslotLists(attrUsed, attrAllowed, false);
                            CompareSubslotLists(attrFixed, attrAllowed, false);
                            SubslotListsDisjunct(attrFixed, attrUsed, true, "0x00011106_7");

                            XAttribute fsubslot = submodule.Attribute(Attributes.s_FixedInSubslots);
                            if (fsubslot != null)
                            {
                                List<ValueListHelper.ValueRangeT> submodFixedSlots = ValueListHelper.NormalizeValueList(fsubslot, Store);

                                for (int currentRange = 0; currentRange < submodFixedSlots.Count; currentRange++)
                                {
                                    submodAllFixedSlots.Add(submodFixedSlots[currentRange]);
                                }
                            }

                            XAttribute usubslot = submodule.Attribute(Attributes.s_UsedInSubslots);
                            if (usubslot != null)
                            {
                                List<ValueListHelper.ValueRangeT> submodUsedSlots = ValueListHelper.NormalizeValueList(usubslot, Store);
                                for (int currentRange = 0; currentRange < submodUsedSlots.Count; currentRange++)
                                {
                                    submodAllUsedSlots.Add(submodUsedSlots[currentRange]);
                                }
                            }
                        }
                    }

                    // VirtualSubmoduleItems
                    List<ValueListHelper.ValueRangeT> vsmAllSubslots = new();

                    var vsms = en.XPathSelectElements(".//gsddef:VirtualSubmoduleItem", Nsmgr); // en is dap or module
                    foreach (var vsm in vsms)
                    {
                        XAttribute fsubslot = vsm.Attribute(Attributes.s_FixedInSubslots);
                        if (fsubslot != null)
                        {
                            List<ValueListHelper.ValueRangeT> vsmSubslots = ValueListHelper.NormalizeValueList(fsubslot, Store);
                            for (int currentRange = 0; currentRange < vsmSubslots.Count; currentRange++)
                            {
                                uint ui = vsmSubslots[currentRange].From;
                                bool notContained = false;
                                while (ui <= vsmSubslots[currentRange].To)
                                {
                                    if (!ValueListHelper.IsValueInValueList(ui++, vsmAllSubslots))
                                    {
                                        notContained = true;
                                        break;
                                    }
                                }

                                if (notContained)
                                {
                                    vsmAllSubslots.Add(vsmSubslots[currentRange]);
                                }
                            }
                        }
                        else
                        {
                            ValueListHelper.ValueRangeT range1;
                            range1.From = 1;
                            range1.To = 1;
                            vsmAllSubslots.Add(range1);
                        }
                    }

                    // PortSubmoduleItems
                    List<UInt16> psmSubslots = new();
                    var psms = en.XPathSelectElements(".//gsddef:PortSubmoduleItem", Nsmgr); // en is dap or module
                    foreach (var psm in psms)
                    {
                        string strSubslot = Help.GetAttributeValueFromXElement(psm, "SubslotNumber"); // Must
                        UInt16 subslot = XmlConvert.ToUInt16(strSubslot);
                        if (!psmSubslots.Contains(subslot))
                        {
                            psmSubslots.Add(subslot);
                        }
                    }

                    for (int i = 0; i < psmSubslots.Count; i++)
                    {
                        if (ValueListHelper.IsValueInValueList(psmSubslots.ElementAt(i), physicalSubslotList))
                        {
                            // "Physical subslot{0} is also used for 'PortSubmoduleItem'."
                            string msg = String.Format(Help.GetMessageString("M_0x00011106_8"), psmSubslots.ElementAt(i));
                            string xpath = Help.GetXPath(physicalSubslotsNode);
                            var xli = (IXmlLineInfo)physicalSubslotsNode;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                          ReportCategories.PlugRules, "0x00011106_8");
                        }
                    }

                    for (int CurrentRange = 0; CurrentRange < vsmAllSubslots.Count; CurrentRange++)
                    {
                        uint virtualSubslot = vsmAllSubslots[CurrentRange].From;
                        while (virtualSubslot <= vsmAllSubslots[CurrentRange].To)
                        {
                            if (ValueListHelper.IsValueInValueList(virtualSubslot, physicalSubslotList))
                            {
                                // "Physical subslot {0} is also used for 'VirtualSubmoduleItem'."
                                string msg = String.Format(Help.GetMessageString("M_0x00011106_4"), virtualSubslot);
                                string xpath = Help.GetXPath(physicalSubslotsNode);
                                var xli = (IXmlLineInfo)physicalSubslotsNode;
                                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                              ReportCategories.PlugRules, "0x00011106_4");
                            }
                            virtualSubslot++;
                        }
                    }
                }

                // check for duplicate SubmoduleItemTargets
                if (useableSubmodules != null)
                {
                    List<string> submoduleIds = new();

                    var submoduleRefs = useableSubmodules.Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef);
                    foreach (var submodule in submoduleRefs)
                    {
                        XAttribute target = submodule.Attribute(Attributes.s_SubmoduleItemTarget);
                        if (submoduleIds.Contains(Help.CollapseWhitespace(target.Value)))
                        {
                            // "Duplicate 'SubmoduleItemRef' elements found (it means, they have the same target)."
                            string msg = Help.GetMessageString("M_0x00011106_5");
                            string xpath = Help.GetXPath(target);
                            var xli = (IXmlLineInfo)target;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, "0x00011106_5");
                        }
                        else
                        {
                            submoduleIds.Add(Help.CollapseWhitespace(target.Value));
                        }
                    }
                }
            }

            return succeeded;
        }

        private bool CompareSubslotLists(XAttribute attr1, XAttribute attr2, bool generateError)
        {
            ReportTypes reportClass = ReportTypes.GSD_RT_MinorError;

            if (generateError)
                reportClass = ReportTypes.GSD_RT_Error;


            if (null != attr1 && null != attr2)
            {
                List<ValueListHelper.ValueRangeT> subslots1 = ValueListHelper.NormalizeValueList(attr1, Store);
                List<ValueListHelper.ValueRangeT> subslots2 = ValueListHelper.NormalizeValueList(attr2, Store);

                // NormalizeValueList assures that all ranges are strictly ascending, non-overlapping, and not joinable
                // (there is a gap between two ranges). Therefore, each range in subslots1 must be *completely contained*
                // in some range of subslots2. A range in subslots2 may contain more than one range of subslots1. But a
                // range from subslots1 can't be split over more than one range from subslots2, because then there would
                // be a gap between and then not the whole range of subslots1 would be covered.

                int Range2 = 0;
                for (int Range1 = 0; Range1 < subslots1.Count; Range1++)
                {
                    // skip all ranges from subslots2 which have too low values
                    while ((Range2 < subslots2.Count) && (subslots1[Range1].To > subslots2[Range2].To))
                    {
                        Range2++;
                    }

                    if ((Range2 >= subslots2.Count) || // no more range available
                       (subslots1[Range1].From < subslots2[Range2].From)) // not completely contained
                    {
                        // "Subslots defined in '{0}' aren't available in '{1}'."
                        string msg = String.Format(Help.GetMessageString("M_0x00011106_6"), attr1.Name.LocalName, attr2.Name.LocalName);
                        string xpath = Help.GetXPath(attr1);
                        IXmlLineInfo xli = attr1;
                        Store.CreateAndAnnounceReport(reportClass, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, "0x00011106_6");
                        break;
                    }
                }
            }

            return true;
        }

        private bool SubslotListsDisjunct(XAttribute attr1, XAttribute attr2, bool generateError, string checkNo)
        {
            ReportTypes reportClass = ReportTypes.GSD_RT_Warning;

            if (generateError)
                reportClass = ReportTypes.GSD_RT_Error;


            if (null != attr1 && null != attr2)
            {
                List<ValueListHelper.ValueRangeT> subslots1 = ValueListHelper.NormalizeValueList(attr1, Store);
                List<ValueListHelper.ValueRangeT> subslots2 = ValueListHelper.NormalizeValueList(attr2, Store);

                int Range1 = 0;
                int Range2 = 0;
                while ((Range1 < subslots1.Count) && (Range2 < subslots2.Count))
                {
                    // Do the two Ranges overlap?
                    if (subslots1[Range1].From > subslots2[Range2].To)
                    {
                        // no, Range 1 is completely behind Range 2 -> skip Range 2
                        Range2++;
                        continue;
                    }

                    if (subslots2[Range2].From > subslots1[Range1].To)
                    {
                        // no, Range 2 is completely behind Range 1 -> skip Range 1
                        Range1++;
                        continue;
                    }
                    // "Subslots defined in '{0}' must not be available in '{1}'."
                    string msgNo = "M_" + checkNo;
                    string msg = String.Format(Help.GetMessageString(msgNo), attr1.Name.LocalName, attr2.Name.LocalName);
                    string xpath = Help.GetXPath(attr2);
                    IXmlLineInfo xli = attr2;
                    Store.CreateAndAnnounceReport(reportClass, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, checkNo);
                    break;
                }
            }

            return true;
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00011107
        /// The "SubmoduleItem/@ID" attribute must be unique across all submodules.
        /// In addition, the "SubmoduleItemRef/@SubmoduleItemTarget" attribute must reference an existing submodule.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00011107()
        {
            // Keys -----
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItem).Attributes(Attributes.ID);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);

            var keys = new List<string>();
            foreach (XAttribute an in nl)
            {
                string submoduleItem = Help.CollapseWhitespace(an.Value);
                if (keys.Contains(submoduleItem))
                {
                    // "The 'SubmoduleItem/@ID' must be unique for all 'SubmoduleItem' elements."
                    string msg = Help.GetMessageString("M_0x00011107_1");
                    string xpath = Help.GetXPath(an);
                    var xli = (IXmlLineInfo)an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00011107_1");
                }
                else
                    keys.Add(submoduleItem);
            }

            // KeyRefs -----
            nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SubmoduleItemRef).Attributes(Attributes.s_SubmoduleItemTarget);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                if (!keys.Contains(Help.CollapseWhitespace(an.Value)))
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "The 'SubmoduleItemRef/@SubmoduleItemTarget' attribute must reference an existing submodule 'ID'."
                        string msg = Help.GetMessageString("M_0x00011107_2");
                        string xpath = Help.GetXPath(an);
                        var xli = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.KeyKeyref, "0x00011107_2");
                    }
                }
            }

            return true;
        }

        protected virtual bool CheckCn_0X00011108()
        {
            var nl = GsdProfileBody.Descendants().Attributes(Attributes.s_WriteableImRecords);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                List<ValueListHelper.ValueRangeT> values = ValueListHelper.NormalizeValueList(an, Store);
                for (int currentRange = 0; currentRange < values.Count; currentRange++)
                {
                    if (values[currentRange].From < 1 || values[currentRange].To > 15)
                    {
                        // "Invalid value in 'Writeable_IM_Records'."
                        string msg = Help.GetMessageString("M_0x00011108_1");
                        string xpath = Help.GetXPath(an);
                        IXmlLineInfo xli = an;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011108_1");
                    }

                    if (values[currentRange].From <= 5 && values[currentRange].To >= 5)
                    {
                        // "'Writeable_IM_Records' must not contain 5."
                        string msg = Help.GetMessageString("M_0x00011108_2");
                        string xpath = Help.GetXPath(an);
                        IXmlLineInfo xli = an;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011108_2");
                    }
                }
            }
            return true;
        }

        protected virtual bool IsConformanceClassDOnly(XElement dap)
        {
            return false;
        }

        /// <summary>
        /// RT class combinations
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00011109()
        {
            const string xp1 = "/gsddef:ISO15745Profile/gsddef:ProfileBody/gsddef:ApplicationProcess/" +
                "gsddef:DeviceAccessPointList/gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/" +
                "gsddef:InterfaceSubmoduleItem";

            var nl = GsdProfileBody.XPathSelectElements(xp1, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var element in nl)
            {
                var lineInfo = (IXmlLineInfo)element;

                string supportedRtClass = Help.GetAttributeValueFromXElement(element, Attributes.s_SupportedRTClass);
                string supportedRtClasses = Help.GetAttributeValueFromXElement(element, Attributes.s_SupportedRTClasses);

                if (!String.IsNullOrEmpty(supportedRtClasses))
                {
                    bool rtClass2Found = false;
                    bool rtClass3Found = false;

                    string[] rtClasses = supportedRtClasses.Split(';');
                    foreach (string rtClass in rtClasses)
                    {
                        if (rtClass == "RT_CLASS_2")
                        {
                            rtClass2Found = true;
                        }
                        else if ((rtClass == "RT_CLASS_3"))
                        {
                            rtClass3Found = true;
                        }
                    }



                    if (rtClass2Found && !rtClass3Found)
                    {
                        // "Supporting "RT_CLASS_2" without "RT_CLASS_3" is not recommended."
                        string msg = Help.GetMessageString("M_0x00011109_2");
                        string xpath = Help.GetXPath(element);
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x00011109_2");
                    }

                    if (!String.IsNullOrEmpty(supportedRtClass))
                    {
                        if ((supportedRtClass == "Class1" && (rtClass2Found || rtClass3Found)) ||
                            (supportedRtClass == "Class2" && !rtClass2Found) ||
                            (supportedRtClass == "Class3" && !rtClass3Found))
                        {
                            // "Mismatch between attributes 'SupportedRT_Class' and 'SupportedRT_Classes'."
                            string msg = Help.GetMessageString("M_0x00011109_3");
                            string xpath = Help.GetXPath(element);
                            Store.CreateAndAnnounceReport(ReportType_0X00011109, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.General, "0x00011109_3");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001119A
        /// If the attribute 'FiberOpticTypes' is not given, attribute 'PowerBudgetControlSupported' shall not be present.
        ///
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001119A()
        {
            string xp = ".//gsddef:DeviceAccessPointItem/gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem[not(@FiberOpticTypes) and (@PowerBudgetControlSupported)]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "If the attribute 'FiberOpticTypes' is not given, attribute 'PowerBudgetControlSupported' shall not be present."
                string msg = Help.GetMessageString("M_0x0001119A_1");
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119A_1");
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001119B
        /// Check "DataItem/@DataType" values with "VisibleString" or "OctetString" without '@Length' given.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001119B()
        {
            // Find all 'VisibleString' or 'OctetString' without '@Length' given.
            string xp = "(//gsddef:ProfileExtChannelDiagItem | //gsddef:ExtChannelDiagItem)/gsddef:ExtChannelAddValue/" +
                        "gsddef:DataItem[not(@Length) and " + DataTypesRequiresLength + "]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "'DataItem/@DataType'="{0}" is not allowed, because 'DataItem/@Length' not available.
                // 'DataItem/@Length' is available from GSDML V2.2 on."
                string msg = String.Format(Help.GetMessageString("M_0x0001119B_1"), Help.GetAttributeValueFromXElement(en, Attributes.s_DataType));
                string xpath = Help.GetXPath(en);
                IXmlLineInfo xli = en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119B_1");
            }

            return true;
        }

        /// <summary>
        /// If this attribute is used for data types with fixed length definition, the length shall correspond
        /// to the definition in PNO-2.712 Application Layer services, "Data Type ASE".
        /// The sum of all DataItem elements shall describe exactly 4 octets.
        /// </summary>
        protected virtual bool CheckCn_0X0001119C()
        {
            const string Xp = ".//gsddef:ExtChannelAddValue";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var an in nl)
            {
                uint totalLength = 0;
                var nl1 = an.Elements(NamespaceGsdDef + Elements.s_DataItem);

                foreach (var at in nl1)
                {
                    string strLength = Help.GetAttributeValueFromXElement(at, Attributes.s_Length);
                    string datatype = Help.GetAttributeValueFromXElement(at, Attributes.s_DataType);
                    uint actualLength = 0;

                    if (!String.IsNullOrEmpty(strLength))
                        actualLength = XmlConvert.ToUInt32(strLength);

                    uint dataTypeLength = Help.GetBitLengthFromDataItemType(datatype) / 8;
                    if (dataTypeLength != 0)
                        actualLength = dataTypeLength;

                    totalLength += actualLength;
                }

                if (totalLength != 4)
                {
                    // "The sum of all 'DataItem' elements in a 'ExtChannelAddValue' shall describe exactly 4 octets, you have {0} octets."
                    string msg = String.Format(Help.GetMessageString("M_0x0001119C_1"), totalLength);
                    string xpath = Help.GetXPath(an);
                    IXmlLineInfo xli = an;
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119C_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001119D
        /// Combinations of ChannelDiagItem and ProfileExtChannelDiagItem is not useful.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001119D()
        {
            // Find all ChannelDiagItem - ProfileExtChannelDiagItem combinations.
            string xp = ".//gsddef:ChannelDiagItem/gsddef:ExtChannelDiagList/gsddef:ProfileExtChannelDiagItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The combination 'ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem'
                // is not allowed from GSDML V2.2 on."
                string msg = Help.GetMessageString("M_0x0001119D_2");
                string xpath = Help.GetXPath(en);
                var xli = (IXmlLineInfo)en;
                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x0001119D_2");
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001119E
        /// Check each user text if used ids are defined and
        /// if the format given in text matches with the data type.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001119E()
        {
            // Find all ExtChannelAddValues
            string xp = "(//gsddef:ProfileExtChannelDiagItem | //gsddef:ExtChannelDiagItem)/gsddef:ExtChannelAddValue";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var extChannelAddValue in nl)
            {
                IList<Byte> idList = new List<Byte>();
                IList<string> typeList = new List<string>();
                // Find all DataItems of a ExtChannelAddValue

                var dataItems = extChannelAddValue.Descendants(NamespaceGsdDef + Elements.s_DataItem);
                foreach (var dataItem in dataItems)
                {
                    // Collect all DataItem Ids and DataTypes in lists
                    string dataItemId = Help.GetAttributeValueFromXElement(dataItem, Attributes.Id);    // required
                    string dataItemType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
                    idList.Add(XmlConvert.ToByte(dataItemId));
                    typeList.Add(dataItemType);
                }

                // Find the TextId corresponding to the ExtChannelAddValue
                var extChannelDiagItem = extChannelAddValue.Parent;
                if (extChannelDiagItem == null)
                {
                    continue;
                }
                var nameElem = extChannelDiagItem.Element(NamespaceGsdDef + Elements.s_Name);
                string textId = Help.GetAttributeValueFromXElement(nameElem, Attributes.s_TextId);

                // Find all user texts in different languages for the TextId
                var nlLanguages =
                    GsdProfileBody.Descendants()
                        .Where(
                            x =>
                                x.Name.LocalName == Elements.s_PrimaryLanguage ||
                                x.Name.LocalName == Elements.s_Language);
                nlLanguages = Help.TryRemoveXElementsUnderXsAny(nlLanguages, Nsmgr, Gsd);

                foreach (XElement language in nlLanguages)
                {
                    var text = Help.GetElementById(language, "./gsddef:Text", Attributes.s_TextId, textId, Nsmgr);

                    if (text == null)
                        continue;

                    // Check each user text if used ids are defined
                    string sText = Help.GetAttributeValueFromXElement(text, Attributes.s_Value);
                    int indexInText = 0;
                    while (indexInText != -1)
                    {
                        bool wrongFormat = false;
                        string sIdStr = Help.FindIdInText(sText, ref indexInText, out wrongFormat);
                        if (wrongFormat)
                        {
                            // "In the text "{0}" the format does not match the following expression: "{index[:formatString]}"."
                            string expression = "{index[:formatString]}";
                            string msg = String.Format(Help.GetMessageString("M_0x0001119E_3"), sText, expression);
                            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
                            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x0001119E_3");
                        }
                        if (String.IsNullOrEmpty(sIdStr)
                            || indexInText == -1)
                        {
                            continue;
                        }

                        if (!idList.Contains(Byte.Parse(sIdStr)))
                        {
                            // "In the text for text id "{0}" the used id (= {1}) is not defined as an id in the corresponding data items."
                            string msg = String.Format(Help.GetMessageString("M_0x0001119E_1"), textId, sIdStr);
                            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
                            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x0001119E_1");
                        }
                        else
                        {
                            // Check if the format given in text matches with the data type
                            if (sText.Substring(indexInText, 1) != Constants.s_Colon)
                            {
                                continue;
                            }
                            string sFormat = sText.Substring(++indexInText, 1);
                            int indexInIds = idList.IndexOf(byte.Parse(sIdStr));
                            string dataItemType = typeList[indexInIds];
                            if (Help.DoesFormatAndDataTypeMatch(sFormat, dataItemType))
                            {
                                continue;
                            }
                            // "In the text for text id "{0}" the format "({1}:{2})" does not match to the data type "{3}"."
                            string msg = String.Format(
                                            Help.GetMessageString("M_0x0001119E_2"),
                                            textId,
                                            sIdStr,
                                            sFormat,
                                            dataItemType);
                            string xpath = Help.GetXPath(text.Attribute(Attributes.s_Value));
                            IXmlLineInfo xli = text.Attribute(Attributes.s_Value);
                            Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                          ReportCategories.TypeSpecific, "0x0001119E_2");


                        }

                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Checks, if Writeable_IM_Records (I&M1, I&M2 and I&M3 = "1 2 3") are available at the Submodule.
        /// 
        /// </summary>
        /// <returns>True, if Writeable_IM_Records are available.</returns>
        protected bool SubmoduleHasImRecords(XElement submoduleItem)
        {
            if (submoduleItem == null)
            {
                return false;
            }

            XAttribute an = submoduleItem.Attribute(Attributes.s_WriteableImRecords);
            List<ValueListHelper.ValueRangeT> values = ValueListHelper.NormalizeValueList(an, Store);
            if (values.Count != 1)
            {
                return false;
            }

            if (values[0].From == 1 && (values[0].To == 3 || values[0].To == 4))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Checks, if Writeable_IM_Records (I&M1, I&M2 and I&M3 = "1 2 3") are available at the DAP.
        /// Note: I&M4 is not mandatory, because this record is currently under discussion.
        /// 
        /// </summary>
        /// <returns>True, if Writeable_IM_Records are available.</returns>
        protected bool WriteableImRecordsAvailable(XElement dap)
        {
            // (1) Check all VirtualSubmoduleItems at the DAP
            string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem";
            var virtualSubmoduleItems = dap.XPathSelectElements(xp, Nsmgr);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
            {
                if (SubmoduleHasImRecords(virtualSubmoduleItem))
                {
                    return true;
                }
            }

            // (2) Check the InterfaceSubmoduleItem at the DAP
            xp = "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem";
            var interfaceSubmoduleItem = dap.XPathSelectElement(xp, Nsmgr);
            if (SubmoduleHasImRecords(interfaceSubmoduleItem))
            {
                return true;
            }

            // (3) Check all PortSubmoduleItems at the DAP
            xp = "./gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem";
            var portSubmoduleItems = dap.XPathSelectElements(xp, Nsmgr);
            foreach (var portSubmoduleItem in portSubmoduleItems)
            {
                if (SubmoduleHasImRecords(portSubmoduleItem))
                {
                    return true;
                }
            }

            // (4) Check all UseableSubmodules at the DAP
            xp = "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef";
            var submoduleItemRefs = dap.XPathSelectElements(xp, Nsmgr);

            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                string fixedInSubslots = Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_FixedInSubslots);
                if (!string.IsNullOrEmpty(fixedInSubslots))
                {
                    string submoduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));

                    if (!PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule))
                        PluggablePortSubmoduleItems.TryGetValue(submoduleTarget, out submodule);

                    if (null == submodule)
                        continue;

                    if (SubmoduleHasImRecords(submodule))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Check number: CN_0x000111A0
        /// Each DAP shall have at least one fixed Submodule where Writeable_IM_Records contains the values 1-3.
        /// 
        /// A fixed Submodule is one of the following:
        /// '	the SystemDefinedSubmoduleList/InterfaceSubmoduleItem
        /// '	a SystemDefinedSubmoduleList/PortSubmoduleItem
        /// '	a VirtualSubmoduleItemList/VirtualSubmoduleItem
        /// '	a (Port)Submodule referenced by UseableSubmodules/SubmoduleItemRef/@Submodule-ItemTarget, when @FixedInSubslots is present
        /// 
        /// NOTE: I&M4 should only be used together with ApplicationClass FunctionalSafety,
        ///       where it is read-only and thus not included in Writeable_IM_Records.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A0()
        {
            string xp = ".//gsddef:DeviceAccessPointItem";
            var daps = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (pnioVersion >= 2.31)
                {
                    if (!WriteableImRecordsAvailable(dap))
                    {
                        if (Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                        {
                            // "The DAP (ID = "{0}") has no fixed Submodule where 'Writeable_IM_Records' contains the values 1-3."
                            string msg = String.Format(Help.GetMessageString("M_0x000111A0_1"), Help.GetAttributeValueFromXElement(dap, Attributes.ID));
                            string xpath = Help.GetXPath(dap);
                            IXmlLineInfo xli = dap;
                            Store.CreateAndAnnounceReport(ReportType_0X000111A0, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x000111A0_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A1
        /// Check the values of all TokenLists:
        /// 
        /// 1. Any token (known or unknown) may appear in an attribute value at most once, otherwise error.
        /// 2. If the GSDML version is unknown, it is checked if a known token is included,
        ///    if at least one token is specified, unless the use is prevented by a sufficiently
        ///    high set attribute RequiredSchemaVersion.
        /// 3. If the GSDML version is known, an unknown token generates a warning.
        /// 
        /// Note:
        /// Help.CheckSchemaVersion is called before an warning is reported for 2. and 3..
        /// It checks whether a specific XmlNode comes in the specified GSDML version into effect,
        /// depending on RequiredSchemaVersion but regardless of the GSDML version the GSD is based on.
        /// If for a plug-in module or submodule the attribute RequiredSchemaVersion does not exist or
        /// is not set higher than the highest of GSDI supported GSDML version, so it may still be
        /// that this module or sub-module is ignored by the engineering and thus the warnings from
        /// this check come "too much". This happens if the module is not referenced by any DAP or
        /// if the submodule is not referenced by any DAP or module. Secondly, it happens when all DAPs
        /// which reference the module or all modules and DAPs which reference the submodule contain
        /// the attribute RequiredSchemaVersion with a value larger than the largest value supported by GSDI.
        /// These dependencies are not checked by the checker.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A1()
        {
            var nl = (IEnumerable)GsdProfileBody.XPathEvaluate(AttributesWithTokenList, Nsmgr);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                var lineInfo = (IXmlLineInfo)an;

                string strValueList = an.Value;
                if (string.IsNullOrEmpty(strValueList))
                {
                    continue;
                }

                List<string> values = new List<string>(strValueList.Split(Constants.s_Semicolon.ToCharArray()));
                values.Sort();
                bool doubleDefinitionFound = false;
                for (int i = 0; i < values.Count - 1; i++)
                {
                    if (values[i] == values[i + 1])
                    {
                        doubleDefinitionFound = true;
                        // Remove the double defined token, so that subsequent errors are not reported twice.
                        values.Remove(values[i]);
                    }
                }

                if (doubleDefinitionFound)
                {
                    // "Double defined tokens in token list."
                    string msg = Help.GetMessageString("M_0x000111A1_1");
                    string xpath = Help.GetXPath(an);
                    Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x000111A1_1");
                }

                IList<string> allowedTokens = GetAllowedTokens(an);
                if (Help.IsHigherVersion(FileNameGsdmlVersion, SupportedGsdmlVersion))
                {
                    // If the GSDML version is unknown, it is checked if a known token is included,
                    // if at least one token is specified, unless the use is prevented by a sufficiently
                    // high set attribute RequiredSchemaVersion.
                    bool knownTokenFound = false;
                    for (int k = 0; k < values.Count; k++)
                    {
                        if (allowedTokens.Contains(values[k]))
                        {
                            knownTokenFound = true;
                            break;
                        }
                    }

                    if (!knownTokenFound)
                    {
                        if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                        {
                            // "No known token in token list."
                            string msg = Help.GetMessageString("M_0x000111A1_3");
                            string xpath = Help.GetXPath(an);
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x000111A1_3");
                        }
                    }
                }
                else
                {
                    // If the GSDML version is known, an unknown token generates a warning.
                    for (int j = 0; j < values.Count; j++)
                    {
                        if (!allowedTokens.Contains(values[j]))
                        {
                            if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                            {
                                // "The token "{0}" is not a known token for attribute '{1}' in this GSDML version."
                                string msg = String.Format(Help.GetMessageString("M_0x000111A1_2"), values[j], an.Name);
                                string xpath = Help.GetXPath(an);
                                Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Warning, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x000111A1_2");
                            }
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A2
        /// ChannelDiagList/ProfileChannelDiagItem has an attribute @API.
        /// Underlying elements ExtChannelDiagList/ProfileExtChannelDiagItem have also an attribute @API.
        /// Both attributes must have the same value, otherwise error.
        /// 
        /// From GSDML V2.31 on ProfileExtChannelDiagItem has under ProfileChannelDiagItem/ExtChannelDiagList
        /// no attribute @API anymore and the check must be removed.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A2()
        {
            string xp1 = ".//gsddef:ProfileChannelDiagItem";
            string xp2 = "./*/gsddef:ProfileExtChannelDiagItem";
            var nl1 = GsdProfileBody.XPathSelectElements(xp1, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var en1 in nl1)
            {
                string strApi1 = Help.GetAttributeValueFromXElement(en1, Attributes.s_Api); // required attribute
                UInt32 api1 = XmlConvert.ToUInt32(strApi1);
                var nl2 = en1.XPathSelectElements(xp2, Nsmgr);
                foreach (var en2 in nl2)
                {
                    string strApi2 = Help.GetAttributeValueFromXElement(en2, Attributes.s_Api); // required attribute
                    UInt32 api2 = XmlConvert.ToUInt32(strApi2);
                    if (api1 != api2)
                    {
                        if (Help.CheckSchemaVersion(en2, SupportedGsdmlVersion))
                        {
                            // "The attribute 'ProfileExtChannelDiagItem/@API' must have the same value as
                            //  the attribute 'API' of the overlying 'ProfileChannelDiagItem'.
                            string msg = Help.GetMessageString("M_0x000111A2_1");
                            string xpath = Help.GetXPath(en2);
                            IXmlLineInfo xli = en2;
                            Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.TypeSpecific, "0x000111A2_1");
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// As of V2.1
        /// For ChannelDiagList/ProfileChannelDiagItem and
        /// For ChannelDiagList/(Profile)ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem and
        /// For UnitDiagTypeList/ProfileUnitDiagTypeItem must be checked:
        /// - API must be >0
        /// 
        /// As of V2.31
        /// these checks are handled by the schema. The check can then be removed.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A3()
        {
            string xp = ".//gsddef:ChannelDiagList/gsddef:ProfileChannelDiagItem[@API = 0] | ";
            xp += ".//gsddef:ExtChannelDiagList/gsddef:ProfileExtChannelDiagItem[@API = 0] | ";
            xp += ".//gsddef:UnitDiagTypeList/gsddef:ProfileUnitDiagTypeItem[@API = 0]";

            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                // "The 'API' must be greater than 0."
                string msg = Help.GetMessageString("M_0x000111A3_1");
                string xpath = Help.GetXPath(en.Attribute(Attributes.s_Api));
                IXmlLineInfo xli = en.Attribute(Attributes.s_Api);
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x000111A3_1");
                }
            }

            return true;
        }

        /// Check number: CheckOneAttributeWithValueList
        /// 
        /// Does the checks from CN_0x000111A4 for one attribute with value list
        ///     
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckOneAttributeWithValueList(XAttribute an, List<ValueListHelper.ValueRangeT> values, XElement portSubmodule, string attributeName)
        {
            // PNIO version derived from the GSDML version of the file name.
            // It doesn't matter that pnioVersion = 2.25 can also occur here.
            // The comparison below works anyway.
            double pnioVersion = GetPNioVersion(FileNameGsdmlVersion);
            // Up to this PNIO version the values in allowedValuesStr are valid

            string xpath = Help.GetXPath(an);
            IXmlLineInfo xli = an;
            string name = an.Name.LocalName;

            if (portSubmodule != null && portSubmodule.Name.LocalName == Elements.s_PortSubmoduleItem)
            {
                if (portSubmodule.Parent != null)
                {
                    XElement dap = portSubmodule.Parent.Parent;
                    if (dap.Name.LocalName == Elements.s_DeviceAccessPointItem)
                    {
                        if (!string.IsNullOrEmpty(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion)))
                            pnioVersion = GetPNioVersion(
                                Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    }
                    else
                    {
                        pnioVersion = FindMinPNioVersionForPluggablePortSubmodule(portSubmodule);
                        if (pnioVersion == 0)
                        {
                            pnioVersion = GetPNioVersion(FileNameGsdmlVersion);
                        }
                    }
                }
            }

            bool allowedValuesFound = false;
            bool dependendOnPNIOVersion = false;
            string allowedValuesStr = AttributeValueListDictionary[attributeName];
            int index = allowedValuesStr.IndexOf("-", StringComparison.InvariantCulture);
            if (index >= 0)
                dependendOnPNIOVersion = true;
            while (!allowedValuesFound)
            {
                if (index == -1)
                {
                    allowedValuesFound = true;
                }
                else
                {
                    int VersionLen = allowedValuesStr.IndexOf(" ", StringComparison.InvariantCulture);
                    string pnioVersionLimitStr = allowedValuesStr.Substring(index + 1, VersionLen - 1);
                    double pnioVersionLimit = GetPNioVersion(pnioVersionLimitStr);
                    allowedValuesStr = allowedValuesStr.Substring(VersionLen + 1);
                    index = allowedValuesStr.IndexOf("-", StringComparison.InvariantCulture);

                    if (pnioVersion > pnioVersionLimit)
                    {
                        string newName = string.Concat(attributeName, "-", pnioVersionLimitStr);
                        allowedValuesStr = AttributeValueListDictionary[newName];
                        index = allowedValuesStr.IndexOf("-", StringComparison.InvariantCulture);
                    }
                }
            }

            ValueListHelper.NormalizeValueList(allowedValuesStr, out List<ValueListHelper.ValueRangeT> allowedValues);
            if (Help.IsHigherVersion(FileNameGsdmlVersion, SupportedGsdmlVersion))
            {
                // If the GSDML version is unknown, it is checked if a known token is included,
                // if at least one token is specified, unless the use is prevented by a sufficiently
                // high set attribute RequiredSchemaVersion.
                bool knownValueFound = false;
                for (int Range1 = 0; Range1 < values.Count; Range1++)
                {
                    for (uint k = values[Range1].From; k <= values[Range1].To; k++)
                    {
                        for (int Range2 = 0; Range2 < allowedValues.Count; Range2++)
                        {
                            if (k >= allowedValues[Range2].From && k <= allowedValues[Range2].To)
                            {
                                knownValueFound = true;
                                break;
                            }
                        }
                        if (knownValueFound)
                            break;
                    }
                    if (knownValueFound)
                        break;
                }
                if (!knownValueFound)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "No known value in value list."
                        string msg = Help.GetMessageString("M_0x000111A4_1");
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x000111A4_1");
                    }
                }
            }
            else
            {
                // If the GSDML version is known, an unknown token generates a warning.
                string pnioVersionText = string.Empty;
                if (dependendOnPNIOVersion)
                    pnioVersionText = " or PNIO";

                for (int Range1 = 0; Range1 < values.Count; Range1++)
                {
                    for (uint k = values[Range1].From; k <= values[Range1].To; k++)
                    {
                        bool isValueKnown = false;
                        if (k <= allowedValues[allowedValues.Count - 1].To)
                        {
                            for (int Range2 = 0; Range2 < allowedValues.Count; Range2++)
                            {
                                if (k >= allowedValues[Range2].From && k <= allowedValues[Range2].To)
                                {
                                    isValueKnown = true;
                                    break;
                                }
                            }
                            if (!isValueKnown)
                            {
                                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                                {
                                    // "The value {0} is not a known value for attribute / list of values '{1}' in this GSDML version."
                                    string msg = String.Format(Help.GetMessageString("M_0x000111A4_2"), k, name, pnioVersionText);
                                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                                  ReportCategories.TypeSpecific, "0x000111A4_2");
                                }
                            }
                        }
                        else
                        {
                            if (values[Range1].From == values[Range1].To)
                            {
                                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                                {
                                    // "The value {0} is not a known value for attribute / list of values '{1}' in this GSDML version."
                                    string msg = String.Format(Help.GetMessageString("M_0x000111A4_2"), k, name, pnioVersionText);
                                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                                  ReportCategories.TypeSpecific, "0x000111A4_2");
                                }
                            }
                            else
                            {
                                string range = String.Format("{0}..{1}", values[Range1].From, values[Range1].To);
                                if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                                {
                                    // "The value {0} is not a known value for attribute / list of values '{1}' in this GSDML version."
                                    string msg = String.Format(Help.GetMessageString("M_0x000111A4_2"), range, name, pnioVersionText);
                                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                                  ReportCategories.TypeSpecific, "0x000111A4_2");
                                }
                            }
                            break;
                        }
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A4
        ///     
        /// Check the values of all ValueLists:
        /// 
        /// 1. Any value (known or unknown) may appear at most once, otherwise error.
        ///    This is automatically checked by ValueListHelper.NormalizeValueList.
        /// 2. If the PNIO version is unknown, it is checked if a known value is included,
        ///    if at least one value is specified, unless the use is prevented by a sufficiently
        ///    high set attribute RequiredSchemaVersion.
        /// 3. If the PNIO version is known, an unknown value generates a warning.
        /// 
        /// Note:
        /// Help.CheckSchemaVersion is called before an warning is reported for 2. and 3..
        /// It checks whether a specific XmlNode comes in the specified GSDML version into effect,
        /// depending on RequiredSchemaVersion but regardless of the GSDML version the GSD is based on.
        /// If for a plug-in module or submodule the attribute RequiredSchemaVersion does not exist or
        /// is not set higher than the highest of GSDI supported GSDML version, so it may still be
        /// that this module or sub-module is ignored by the engineering and thus the warnings from
        /// this check come "too much". This happens if the module is not referenced by any DAP or
        /// if the submodule is not referenced by any DAP or module. Secondly, it happens when all DAPs
        /// which reference the module or all modules and DAPs which reference the submodule contain
        /// the attribute RequiredSchemaVersion with a value larger than the largest value supported by GSDI.
        /// These dependencies are not checked by the checker.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A4()
        {
            var nl = (IEnumerable)GsdProfileBody.XPathEvaluate(AttributesWithValueList, Nsmgr);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                List<ValueListHelper.ValueRangeT> values = ValueListHelper.NormalizeValueList(an, Store);

                CheckOneAttributeWithValueList(an, values, an.Parent, an.Name.LocalName);
            }

            return true;
        }

        /// <summary>
        /// MapMAUTypeStringToValue
        /// 
        /// Maps a MAUType string to a MAUType value.
        /// 
        /// </summary>
        /// <returns>The mapped MAUType as UInt16.</returns>
        protected virtual UInt16 MapMauTypeStringToValue(string mauTypeStr)
        {
            UInt16 mauType = 0;
            switch (mauTypeStr)
            {
                case "100BASETXFD":
                    mauType = 16;
                    break;
                case "100BASEFXFD":
                    mauType = 18;
                    break;
                case "1000BASEXFD":
                    mauType = 22;
                    break;
                case "1000BASELXFD":
                    mauType = 24;
                    break;
                case "1000BASESXFD":
                    mauType = 26;
                    break;
                case "1000BASETFD":
                    mauType = 30;
                    break;
                case "10GigBASEFX":
                    mauType = 31;
                    break;
            }

            return mauType;
        }

        /// <summary>
        /// Check number: CN_0x000111A5
        /// 
        /// If PortSubmoduleItem/@MAUType and PortSubmoduleItem/@MAUTypes are both specified at the same time,
        /// the value of MAUType should already be included in the list of values in MAUTypes.
        /// Mapping of strings from MAUType to values in MAUTypes is done in method MapMAUTypeStringToValue.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A5()
        {
            string xp = ".//gsddef:PortSubmoduleItem[@MAUType and @MAUTypes]";

            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var portSubmoduleItem in nl)
            {
                double requiredSchemaVersion = 0;
                var submoduleList = portSubmoduleItem.Parent;
                if (submoduleList != null && submoduleList.Name.LocalName == Elements.s_SubmoduleList) // Can be SubmoduleList or SystemDefinedSubmoduleList
                {
                    // For a pluggable PortSubmoduleItem the RequiredSchemaVersion can be found directly as attribute of PortSubmoduleItem
                    requiredSchemaVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_RequiredSchemaVersion));
                }
                else // submoduleList.LocalName == Elements.SystemDefinedSubmoduleList
                {
                    // For a PortSubmoduleItem which is fixed plugged in a DAP or Module the RequiredSchemaVersion can be found as attribute of DAP or Module
                    var dapOrMod = submoduleList.Parent;
                    requiredSchemaVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dapOrMod, Attributes.s_RequiredSchemaVersion));
                }

                if (requiredSchemaVersion >= 2.1)
                    continue;

                string mauTypeStr = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_MauType);
                UInt16 mauType = MapMauTypeStringToValue(mauTypeStr);

                XAttribute mauTypes = portSubmoduleItem.Attribute(Attributes.s_MauTypes);
                List<ValueListHelper.ValueRangeT> mauTypesList = ValueListHelper.NormalizeValueList(mauTypes, Store);

                // "The MAU type in attribute 'MAUType' should also be available in attribute 'MAUTypes'."
                if (ValueListHelper.IsValueInValueList(mauType, mauTypesList))
                {
                    continue;
                }
                string msg = Help.GetMessageString("M_0x000111A5_1");
                string xpath = Help.GetXPath(portSubmoduleItem.Attribute(Attributes.s_MauType));
                var xli = (IXmlLineInfo)portSubmoduleItem.Attribute(Attributes.s_MauType);
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        ReportType_0X000111A5,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x000111A5_1");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A6
        /// 
        /// Subslot numbers defined in AllowedInSubslots, UsedInSubslots or FixedInSubslots should not collide
        /// with Subslot numbers in VirtualSubmoduleList or SystemDefinedSubmoduleList.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A6()
        {
            string xp1 = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem";
            string xp2 = "./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem | " +
                         "./gsddef:SystemDefinedSubmoduleList/gsddef:PortSubmoduleItem";

            var nl =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_UseableSubmodules).Elements(NamespaceGsdDef + Elements.s_SubmoduleItemRef)
                    .Attributes()
                    .Where(
                        attribute =>
                            attribute.Name.LocalName == Attributes.s_AllowedInSubslots ||
                            attribute.Name.LocalName == Attributes.s_UsedInSubslots ||
                            attribute.Name.LocalName == Attributes.s_FixedInSubslots);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute pluggable in nl)
            {
                if (pluggable.Parent == null)
                {
                    continue;
                }
                var dapOrModule = pluggable.Parent.Parent.Parent;

                var virtualSubmoduleItems = dapOrModule.XPathSelectElements(xp1, Nsmgr).ToList();
                var submoduleItems = dapOrModule.XPathSelectElements(xp2, Nsmgr);

                foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
                {
                    XAttribute fixedInSlots = virtualSubmoduleItem.Attribute(Attributes.s_FixedInSubslots);
                    if (fixedInSlots != null)
                    {
                        // "Subslots defined in 'VirtualSubmoduleList/VirtualSubmoduleItem/{0}' must not be available in '{1}'."
                        SubslotListsDisjunct(fixedInSlots, pluggable, true, "0x000111A6_1");
                    }
                    else
                    {
                        if (virtualSubmoduleItems.Count != 1)
                        {
                            continue;
                        }

                        // If there is only one VirtualSubmoduleItem in VirtualSubmoduleList,
                        // FixedInSubslots may be missing and then the default must be 1.
                        List<ValueListHelper.ValueRangeT> subslots = ValueListHelper.NormalizeValueList(pluggable, Store);
                        uint defaultValue = 1;
                        if (!ValueListHelper.IsValueInValueList(defaultValue, subslots))
                        {
                            continue;
                        }
                        // "If only one VirtualSubmoduleItem is given, the default value for FixedInSubslots is 1. Thus 1 must not be available in '{0}'."
                        string msg = String.Format(Help.GetMessageString("M_0x000111A6_2"), pluggable.Name.LocalName);
                        string xpath = Help.GetXPath(pluggable);
                        var xli = (IXmlLineInfo)pluggable;
                        Store.CreateAndAnnounceReport(ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, "0x000111A6_2");


                    }
                }

                foreach (var submoduleItem in submoduleItems)
                {
                    XAttribute subslotNumber = submoduleItem.Attribute(Attributes.s_SubslotNumber);
                    if (subslotNumber != null)
                    {
                        // "Subslots defined in 'SystemDefinedSubmoduleList/*SubmoduleItem/{0}' must not be available in '{1}'."
                        SubslotListsDisjunct(subslotNumber, pluggable, true, "0x000111A6_3");
                    }
                    else
                    {
                        if (submoduleItem.Name.LocalName != Elements.s_InterfaceSubmoduleItem)
                        {
                            continue;
                        }

                        List<ValueListHelper.ValueRangeT> subslots = ValueListHelper.NormalizeValueList(pluggable, Store);
                        uint defaultValue = 32768;
                        if (!ValueListHelper.IsValueInValueList(defaultValue, subslots))
                        {
                            continue;
                        }
                        // "If for 'SystemDefinedSubmoduleList/InterfaceSubmoduleItem' the attribute SubslotNumber is missing,
                        //  the default value is 32768. Thus 32768 must not be available in '{0}'."
                        string msg = String.Format(Help.GetMessageString("M_0x000111A6_4"), pluggable.Name.LocalName);
                        string xpath = Help.GetXPath(pluggable);
                        IXmlLineInfo xli = pluggable;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath, ReportCategories.PlugRules, "0x000111A6_4");
                    }
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A7
        /// ChannelDiagList/(Profile)ChannelDiagItem/@MaintenanceAlarmState = "QD" is only allowed with extended channel diagnosis.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X000111A7()
        {
            string xp = "(//gsddef:ChannelDiagList/gsddef:ChannelDiagItem | //gsddef:ChannelDiagList/gsddef:ProfileChannelDiagItem)[@MaintenanceAlarmState]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var channelDiagItem in nl)
            {
                string maintenanceAlarmStateStr = Help.GetAttributeValueFromXElement(channelDiagItem, Attributes.s_MaintenanceAlarmState);
                var maintenanceAlarmStates = new List<string>();
                if (!string.IsNullOrEmpty(maintenanceAlarmStateStr))
                    maintenanceAlarmStates = new List<string>(maintenanceAlarmStateStr.Split(Constants.s_Semicolon.ToCharArray()));
                if (maintenanceAlarmStates.Contains("QD") && channelDiagItem.XPathSelectElement("gsddef:ExtChannelDiagList", Nsmgr) == null)
                {
                    if (Help.CheckSchemaVersion(channelDiagItem, SupportedGsdmlVersion))
                    {
                        // "'ChannelDiagList/(Profile)ChannelDiagItem/@MaintenanceAlarmState' = "QD" is only allowed with extended channel diagnosis."
                        string msg = Help.GetMessageString("M_0x000111A7_1");
                        string xpath = Help.GetXPath(channelDiagItem);
                        var lineInfo = (IXmlLineInfo)channelDiagItem;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_MinorError, lineInfo.LineNumber, lineInfo.LinePosition,
                                                      msg, xpath, ReportCategories.TypeSpecific, "0x000111A7_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x000111A8
        /// With GSDML schema V2.1 the following problems exists:
        /// The TokenList pattern "(([A-z0-9_]*)(;[A-z0-9_]*)*)" allows characters between Z and a: [ ] backslash `.
        /// It also allows to start or end with ";" and contain sequences of ";".
        /// With GSDML schemas V2.3-V2.35 the following problem exists:
        /// The TokenList pattern "([0-9a-zA-Z_]+;)*([0-9a-zA-Z_]+)?" allows a single ";" at the end.
        /// 
        /// This check raises an error for an incorrect TokenList.
        /// 
        /// From GSDML V2.4 on this is done by schema.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x000111A8()
        {
            var nl1 = GsdProfileBody.Descendants().Attributes(Attributes.s_AddressAssignment);
            var nl2 = GsdProfileBody.Descendants().Attributes(Attributes.s_ApplicationClass);
            var nl3 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SynchronisationMode).Attributes(Attributes.s_SupportedSyncProtocols);
            var nl4 = GsdProfileBody.Descendants().Attributes(Attributes.s_MaintenanceAlarmState);
            var nl5 = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MediaRedundancy).Attributes(Attributes.s_SupportedRole);
            var nl6 = GsdProfileBody.Descendants().Attributes(Attributes.s_SupportedRTClasses);
            var nl7 = GsdProfileBody.Descendants().Attributes(Attributes.s_IsochroneModeInRTClasses);
            var nl8 = GsdProfileBody.Descendants().Attributes(Attributes.s_SupportedProtocols);
            var nl9 = GsdProfileBody.Descendants().Attributes(Attributes.s_SupportedMibs);
            var nla = GsdProfileBody.Descendants().Attributes(Attributes.s_FSupportedParameters);
            var nlAll = nl1.Concat(nl2).Concat(nl3).Concat(nl4).Concat(nl5).Concat(nl6).Concat(nl7).Concat(nl8).Concat(nl9).Concat(nla);
            nlAll = Help.TryRemoveXAttributesUnderXsAny(nlAll, Nsmgr, Gsd);

            if (nlAll == null)
                return true;

            foreach (XAttribute tokenlist in nlAll)
            {
                string value = tokenlist.Value;
                if ((value.IndexOfAny("[\\]^`".ToCharArray())) >= 0)
                {
                    // "Attributes of type Tokenlist must not contain a "[", "]", "\", "^" or "'" in the value string."
                    string msg = Help.GetMessageString("M_0x000111A8_1");
                    string xpath = Help.GetXPath(tokenlist);
                    var lineInfo = (IXmlLineInfo)tokenlist;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                  msg, xpath, ReportCategories.TypeSpecific, "0x000111A8_1");
                }
                if (value.StartsWith(";") || value.EndsWith(";"))
                {
                    // "Attributes of type Tokenlist must not start or end with ";"."
                    string msg = Help.GetMessageString("M_0x000111A8_2");
                    string xpath = Help.GetXPath(tokenlist);
                    var lineInfo = (IXmlLineInfo)tokenlist;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                  msg, xpath, ReportCategories.TypeSpecific, "0x000111A8_2");
                }
                if (value.Contains(";;"))
                {
                    // "Attributes of type Tokenlist must not contain ";;" in the value string."
                    string msg = Help.GetMessageString("M_0x000111A8_3");
                    string xpath = Help.GetXPath(tokenlist);
                    var lineInfo = (IXmlLineInfo)tokenlist;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                  msg, xpath, ReportCategories.TypeSpecific, "0x000111A8_3");
                }
            }

            return true;
        }

        #endregion




    }
}


