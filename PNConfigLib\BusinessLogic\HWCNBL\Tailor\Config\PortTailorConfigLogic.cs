/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PortTailorConfigLogic.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.Config
{
    /// <summary>
    /// Summary description for PortTailorConfigLogic.
    /// </summary>
    internal class PortTailorConfigLogic
    {
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Nested classes
        // Contains all non-public nested classes and locally scoped interface definitions
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Constants and enumerations

        // Contains all constants and enumerations
        private const int s_NumberOfPortTailorProperties = 5;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Fields
        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        private DataModel.PCLObjects.Port m_Port;
        private TailorConfigLogicBase m_TailorConfigLogicBase;

        private BlockDataWrapper<PortTailorPropertiesEnum, bool> m_PortTailorProperties;

        private bool? m_MRPPort;
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Delegates and events
        // Contains all delegate and events
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Construction/destruction/initialization
        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        /// <summary>
        /// 
        /// </summary>
        /// <param name="port">the port object</param>
        /// <param name="tailorConfigLogicBase"></param>
        public PortTailorConfigLogic(DataModel.PCLObjects.Port port, TailorConfigLogicBase tailorConfigLogicBase)
        {
            m_Port = port;
            m_TailorConfigLogicBase = tailorConfigLogicBase;
            m_PortTailorProperties = new BlockDataWrapper<PortTailorPropertiesEnum, bool>(5);
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Public methods
        // Contains all public methods of the class
        /// <summary>
        /// Checks PortMasterTailorData generation rules and determines its content
        /// </summary>
        /// <param name="dockingPort"></param>
        /// <param name="isPNIoTailoringExtDefined"></param>
        /// <returns></returns>
        public List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> ToBlockDataWrapper(ref bool dockingPort, bool isPNIoTailoringExtDefined)
        {
            //if Multiple Connection (Alternative Partner) is Enabled for this port, then PortMasterTailorData should not be generated
            IList<DataModel.PCLObjects.Port> partnerPorts = m_Port.GetPartnerPorts();

            List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>> portTailorInfos = new List<BlockDataWrapper<PortMasterTailorDataEnum, ushort>>();

            if (partnerPorts.Count == 0)
            {
                bool isProgrammablePeer = Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(m_Port);
                //if no partnerPort exists and port is not mrp port or a programmable peer port, the PdPortTailorData should not be generated for this port
                m_MRPPort = ConfigUtility.HasPDPortMrpDataAdjust(m_Port,
                                                              m_TailorConfigLogicBase.InterfaceSubmodule);
                if (!m_MRPPort.Value && !isProgrammablePeer)
                {
                    return null;
                }
                portTailorInfos.Add(GetSinglePortTailorInfo(partnerPorts, dockingPort, isPNIoTailoringExtDefined));
            }

            if (dockingPort && isPNIoTailoringExtDefined)
            {
                //Add a single PortTailorInfo for all of the connected tools
                portTailorInfos.Add(GetSinglePortTailorInfo(partnerPorts, dockingPort, isPNIoTailoringExtDefined));
                return portTailorInfos;
            }

            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                portTailorInfos.Add(GetSinglePortTailorInfo(partnerPorts, dockingPort, isPNIoTailoringExtDefined));
            }
            return portTailorInfos;
        }

        private BlockDataWrapper<PortMasterTailorDataEnum, ushort> GetSinglePortTailorInfo(IList<DataModel.PCLObjects.Port> partnerPorts, bool isDockingPort, bool isPNIoTailoringExtDefined)
        {
            BlockDataWrapper<PortMasterTailorDataEnum, ushort> portMasterTailorData = new BlockDataWrapper<PortMasterTailorDataEnum, ushort>(6);

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.LocalSlotNumber,
                (ushort)ConfigUtility.GetSlotNumberOfPort(m_Port));

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.LocalSubslotNumber,
                (ushort)m_Port.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnSubslotNumber, new AttributeAccessCode(), 0));


            ushort peerSlotNumber = 0xFFFF;
            ushort peerSubslotNumber = 0xFFFF;
            ushort peerStationNumber = 0xFFFF;

            //in case of a toolchanger with CPU firmware >= 2.0 the above variables are to be generated with default values.
            if (isDockingPort == false || !isPNIoTailoringExtDefined)
            {
                GetPeerData(partnerPorts, ref peerSlotNumber, ref peerSubslotNumber, ref peerStationNumber);
            }

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.PeerSlotNumber, peerSlotNumber);

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.PeerSubslotNumber, peerSubslotNumber);

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.PeerStationNumber, peerStationNumber);

            portMasterTailorData.SetBlockElement(PortMasterTailorDataEnum.PortTailorProperties, GetPortTailorProperties(partnerPorts, (isDockingPort == false || !isPNIoTailoringExtDefined)));

            return portMasterTailorData;
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region I... members
        // Contains the full implementation of the interface (each interfaces has it's own region)
        // including properties and events that are part of the interface definition
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Overrides and overridables
        // Contains all public and protected overrides as well as overridables of the class
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Protected methods
        // Contains all protected (non overridables) methods of the class
        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Private implementation
        // Contains the private implementation of the class

        private void GetPeerData(
            IList<DataModel.PCLObjects.Port> partnerPorts,
            ref ushort peerSlotNumber,
            ref ushort peerSubslotNumber,
            ref ushort peerStationNumber)
        {
            if (partnerPorts.Count > 0)
            {
                DataModel.PCLObjects.Interface peerInterfaceSubmodule =
                    NavigationUtilities.GetInterfaceOfPort(partnerPorts[0]);

                PclObject peerDevice = peerInterfaceSubmodule.GetDevice();
                PclObject peerIoConnector;
                if (peerDevice is CentralDevice)
                {
                    peerIoConnector = peerInterfaceSubmodule.PNIOC;
                }
                else
                {
                    peerIoConnector = peerInterfaceSubmodule.PNIOD;
                }

                peerSlotNumber = (ushort)ConfigUtility.GetSlotNumberOfPort(partnerPorts[0]);

                peerSubslotNumber = (ushort)partnerPorts[0].AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    0);

                //If device can not be Controller nor IODevice, use 0 as PNStationNumber
                peerStationNumber = peerIoConnector != null
                                        ? (ushort)peerIoConnector.AttributeAccess.GetAnyAttribute<int>(
                                            InternalAttributeNames.PnStationNumber,
                                            new AttributeAccessCode(),
                                            0)
                                        : ushort.MinValue;
            }
        }

        private ushort GetPortTailorProperties(IList<DataModel.PCLObjects.Port> partnerPorts, bool isAlternativePeerConfig)
        {
            int retValue = 0;

            InitTailorProperties(partnerPorts, isAlternativePeerConfig);

            //set bits 0-4
            for (int i = 0; i < s_NumberOfPortTailorProperties; i++)
            {
                ConfigUtility.SetBit(ref retValue, s_NumberOfPortTailorProperties, i,
                    m_PortTailorProperties.GetBlockElement((PortTailorPropertiesEnum)i));
            }

            return (ushort)retValue;
        }

        public virtual void InitTailorProperties(IList<DataModel.PCLObjects.Port> partnerPorts, bool isAnyPeerConfig)
        {
            //by default "Any peer" the first two bits are 0x03
            bool firstPeerModeBitValue = !((partnerPorts.Count > 0) && isAnyPeerConfig);
            bool secondPeerModeBitValue = firstPeerModeBitValue;
            bool peerIsOptionalDevice = false;

            //Check if partner is an Optional IO device. Set Peer mode 0x01 if so.
            if (partnerPorts.Count > 0)
            {
                foreach (DataModel.PCLObjects.Port portSubmodule in partnerPorts)
                {
                    DataModel.PCLObjects.Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);
                    {
                        if (Utilities.MachineTailor.MachineTailorUtility.IsOptionalDeviceEnabled(interfaceSubmodule))
                        {
                            firstPeerModeBitValue = true;
                            peerIsOptionalDevice = true;
                            break;
                        }
                    }
                }
            }

            //Check if port is programmable peer. Set Peer mode 0x02 if so.
            else if (Utilities.MachineTailor.MachineTailorUtility.IsProgrammablePeerEnabled(m_Port))
            {
                secondPeerModeBitValue = true;
                firstPeerModeBitValue = false;
            }

            m_PortTailorProperties.SetBlockElement(PortTailorPropertiesEnum.PeerModeBit1, firstPeerModeBitValue);
            m_PortTailorProperties.SetBlockElement(PortTailorPropertiesEnum.PeerModeBit2, secondPeerModeBitValue);

            //�port-xyz�
            bool nameOfPortCoding = false;
            DecentralDevice heads = m_Port.GetDevice() as DecentralDevice;
            if (ConfigUtility.IsModularScalance(heads))
            {
                //�port-xyz-rstuv�
                nameOfPortCoding = true;
            }

            m_PortTailorProperties.SetBlockElement(PortTailorPropertiesEnum.NameOfPortCoding, nameOfPortCoding);

            // Fill this block with 1 if port is red and peer is an optional IOD.
            m_PortTailorProperties.SetBlockElement(PortTailorPropertiesEnum.TailorRedPort,
                IsRedPort(m_Port) && peerIsOptionalDevice);


            m_PortTailorProperties.SetBlockElement(PortTailorPropertiesEnum.TailorPDPortMrpDataAdjust,
                                                   (m_MRPPort.HasValue
                                                        ? m_MRPPort.Value
                                                        : ConfigUtility.HasPDPortMrpDataAdjust(m_Port,
                                                                                            m_TailorConfigLogicBase
                                                                                                .InterfaceSubmodule))
                                                                                                &&
                                                   m_TailorConfigLogicBase.AddressTailoringEnabled);
        }

        private bool IsRedPort(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            int portNumber = port.AttributeAccess.GetAnyAttribute(InternalAttributeNames.PnPortNumber, ac, 0);
            SyncDomain syncDomain = m_TailorConfigLogicBase.InterfaceSubmodule.SyncDomain;
            SyncDomainBusinessLogic syncDomainBl = syncDomain != null ? syncDomain.SyncDomainBusinessLogic : null;
            if (syncDomainBl == null)
            {
                return false;
            }
            return syncDomainBl.IsIrtTopPort(m_TailorConfigLogicBase.InterfaceSubmodule, portNumber);
        }

        #endregion
    }
}