/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: GsdmlChecker.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/
using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Text;

using PNConfigLib.Gsd.Interpreter.Checker;

namespace PNConfigLib.Consistency
{
    internal class GsdmlChecker
    {
        private static string s_XsdPath = Path.Combine(
            Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "Interpreter/checker/xsd");

        public bool CheckGSDMLConsistency(string gsdmlRevision, string xmlPath)
        {
            var success = true;
            bool gsdRevisionSupported = true;

            if (GsdCheckerInternalSource.GsdmlFromInternalSource)
            {
                return success;
            }

            Gsd.Interpreter.Checker.Checker checker =
                       new Gsd.Interpreter.Checker.Checker();
            Debug.Assert(checker != null);

            if (string.Compare(checker.SupportedGsdmlVersion, gsdmlRevision, StringComparison.Ordinal) < 0)
            {
                gsdRevisionSupported = false;
            }

            if (gsdRevisionSupported)
            {
                success = checker.CheckGsd(xmlPath, s_XsdPath);
            }

            GSDI.ReportTypes reportType = GSDI.ReportTypes.GSD_RT_Warning;
            if (checker.HasReports(GSDI.ReportTypes.GSD_RT_Error))
                reportType = GSDI.ReportTypes.GSD_RT_Error;

            Array reports = checker.GetReports(reportType);
            if (reports != null)
            {
                foreach (Report report in reports)
                {
                    if (reportType == GSDI.ReportTypes.GSD_RT_Error)
                    {
                        StringBuilder messageBuilder = new StringBuilder();
                        messageBuilder.Append("Check-Number: ");
                        messageBuilder.AppendLine(report.CheckNumber);
                        messageBuilder.Append("Message: ");
                        messageBuilder.AppendLine(report.Message);
                        messageBuilder.Append("Line: ");
                        messageBuilder.AppendLine(report.LineNumber.ToString(CultureInfo.InvariantCulture));
                        messageBuilder.Append("XPath: ");
                        messageBuilder.Append(report.SourceXPath);
                        messageBuilder.Append("\r\n");

                        string message = messageBuilder.ToString();
                        ConsistencyLogger.Log(ConsistencyType.GSDML, LogSeverity.Error, xmlPath, message);
                        success = false;
                    }
                }
            }
            return success;
        }
    }

    internal static class GsdCheckerInternalSource
    {
        public static bool GsdmlFromInternalSource
        {
            get; set;
        }
    }
}
