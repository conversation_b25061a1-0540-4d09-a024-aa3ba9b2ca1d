/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDevice.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.LAddresses;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a decentral device. It contains information about
    /// Rack, Station and HeadModule combined in one object.
    /// </summary>
    internal class DecentralDevice : PclObject
    {
        /// <summary>
        /// The Interface object connected to this DecentralDevice.
        /// </summary>
        private Interface m_Interface;

        /// <summary>
        /// AddressManager controls the address assignment for the DecentralDevice.
        /// </summary>
        private LAddressManager m_LAddressManager;

        /// <summary>
        /// The list of modules connected to this DecentralDevice.
        /// </summary>
        private List<Module> m_Modules;

        /// <summary>
        /// The list of virtual submodules of this DecentralDevice.
        /// </summary>
        private List<Submodule> m_VirtualSubmodules;

        internal bool IsShared { get;set; }

        /// <summary>
        /// The constructor of DecentralDevice; instantiates the AddressManager and
        /// an empty list for Module as well as registering itself with the AddressManager.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        public DecentralDevice(string deviceId)
        {
            Id = deviceId;

            m_LAddressManager = new LAddressManager();
            m_Modules = new List<Module>();
            m_VirtualSubmodules = new List<Submodule>();

            m_LAddressManager.RegisterPCLObject(this, LAddressType.Station);
            m_LAddressManager.RegisterPCLObject(this, LAddressType.Rack);
            m_LAddressManager.RegisterPCLObject(this, LAddressType.PCLObject);
        }

        /// <summary>
        /// The rack address of the DecentralDevice.
        /// </summary>
        public LAddress RackLAddress { get; set; }

        /// <summary>
        /// The station address of the DecentralDevice.
        /// </summary>
        public LAddress StationLAddress { get; set; }

        /// <summary>
        /// Adds an interface to this DecentralDevice, but throws an exception if there is already an interface connected to it.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule to be connected.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <exception cref="InvalidOperationException">if DecentralDevice already has an interface connected to it.</exception>
        public void AddInterface(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            if (m_Interface != null)
            {
                throw new InvalidOperationException("Decentral device already has an interface.");
            }

            m_Interface = interfaceSubmodule;
            interfaceSubmodule.ParentObject = this;
            m_LAddressManager.RegisterPCLObject(interfaceSubmodule, LAddressType.PCLObject);
        }

        /// <summary>
        /// Adds a module to this DecentralDevice.
        /// </summary>
        /// <param name="module">The module to be added.</param>
        /// <exception cref="ArgumentNullException">if module is null.</exception>
        public void AddModule(Module module)
        {
            if (module == null)
            {
                throw new ArgumentNullException(nameof(module));
            }

            m_Modules.Add(module);
            m_LAddressManager.RegisterPCLObject(module, LAddressType.PCLObject);
            module.ParentObject = this;
        }

        /// <summary>
        /// Adds a virtual submodule to this DecentralDevice.
        /// </summary>
        /// <param name="submodule">The virtual submodule to be added.</param>
        /// <exception cref="ArgumentNullException">if submodule is null.</exception>
        public void AddVirtualSubmodule(Submodule submodule)
        {
            if (submodule == null)
            {
                throw new ArgumentNullException(nameof(submodule));
            }

            m_VirtualSubmodules.Add(submodule);
            submodule.ParentObject = this;
            submodule.IsVirtual = true;
            m_LAddressManager.RegisterPCLObject(submodule, LAddressType.PCLObject);
        }

        /// <summary>
        /// Accessor for the AddressManager of this DecentralDevice.
        /// </summary>
        /// <returns>The AddressManager of this DecentralDevice.</returns>
        public LAddressManager GetAddressManager()
        {
            return m_LAddressManager;
        }

        /// <summary>
        /// Gets the objects connected to this DecentralDevice with Element relation.
        /// These objects are the Interface and virtual Submodule objects of this DecentralDevice.
        /// </summary>
        /// <returns>The list containing Interface and virtual Submodule objects of this DecentralDevice.</returns>
        public override IList<PclObject> GetElements()
        {
            List<PclObject> elementList = new List<PclObject>();

            elementList.Add(m_Interface);
            elementList.AddRange(m_VirtualSubmodules);

            return elementList;
        }

        /// <summary>
        /// Gets the Interface connected to this DecentralDevice.
        /// </summary>
        /// <returns>The Interface connected to this DecentralDevice.</returns>
        internal override Interface GetInterface()
        {
            return m_Interface;
        }

        /// <summary>
        /// Gets the list of Module objects connected to this DecentralDevice.
        /// </summary>
        /// <returns>The list of Module objects connected to this DecentralDevice.</returns>
        public List<Module> GetModules()
        {
            return m_Modules;
        }

        /// <summary>
        /// Gets the list of virtual Submodule objects of this DecentralDevice.
        /// </summary>
        /// <returns>The list of virtual Submodule objects of this DecentralDevice.</returns>
        public List<Submodule> GetVirtualSubmodules()
        {
            return m_VirtualSubmodules;
        }

        internal List<PclObject> GetDeviceItems()
        {
            List<PclObject> retval = new List<PclObject>(m_Modules);
            retval.AddRange(GetElements());
            return retval;
        }
    }
}