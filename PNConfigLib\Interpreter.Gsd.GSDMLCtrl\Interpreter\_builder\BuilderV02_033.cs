/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_033.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml.XPath;
using System.Collections;
using System.Globalization;

using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02033 :
        BuilderV02032
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02033()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version233);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectProfIenergy:
                        {
                            // NOTE: Navigator must point to PROFIenergy.
                            this.PrepareProfIenergy(nav, ref hash);
                            obj = new C.ProfIenergy();

                            break;
                        }
                    case Models.s_ObjectEnergySavingModeList:
                        {
                            // NOTE: Navigator must point to EnergySavingModeList.
                            this.PrepareEnergySavingModeList(nav, ref hash);
                            obj = new C.EnergySavingModeList();

                            break;
                        }
                    case Models.s_ObjectEnergySavingModeItem:
                        {
                            // NOTE: Navigator must point to EnergySavingModeItem.
                            this.PrepareEnergySavingModeItem(nav, ref hash);
                            obj = new C.EnergySavingModeItem();

                            break;
                        }
                    case Models.s_ObjectMeasurementList:
                        {
                            // NOTE: Navigator must point to MeasurementList.
                            this.PrepareMeasurementList(nav, ref hash);
                            obj = new C.MeasurementList();

                            break;
                        }
                    case Models.s_ObjectMeasurementItem:
                        {
                            // NOTE: Navigator must point to MeasurementItem.
                            this.PrepareMeasurementItem(nav, ref hash);
                            obj = new C.MeasurementItem();

                            break;
                        }
                    case Models.s_ObjectMeasurementValue:
                        {
                            // NOTE: Navigator must point to MeasurementValue.
                            this.PrepareMeasurementValue(nav, ref hash);
                            obj = new C.MeasurementValue();

                            break;
                        }
                    case Models.s_ObjectReportingSystemEvents:
                        {
                            // NOTE: Navigator must point to ReportingSystemEvents.
                            this.PrepareReportingSystemEvents(nav, ref hash);
                            obj = new C.ReportingSystemEvents();

                            break;
                        }
                    case Models.s_ObjectObserver:
                        {
                            // NOTE: Navigator must point to Observer.
                            this.PrepareObserver(nav, ref hash);
                            obj = new C.Observer();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.
            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldNumberOfDeviceAccessAr, null);
            hash.Add(Models.s_FieldIsAdaptsRealIdentification, null);
            hash.Add(Models.s_FieldIsAssetManagement, null);

            // Attribute NumberOfDeviceAccessAR
            string attr = nav.GetAttribute(Attributes.s_NumberOfDeviceAccessAr, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfDeviceAccessAr] = value;
            }

            // Attribute AdaptsRealIdentification
            attr = nav.GetAttribute(Attributes.s_AdaptsRealIdentification, String.Empty);
            hash[Models.s_FieldIsAdaptsRealIdentification] = Help.GetBool(attr, Attributes.s_DefaultAdaptsRealIdentification);

            // Check whether AssetManagement is available.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_AssetManagement, Namespaces.s_GsdmlDeviceProfile);
            hash[Models.s_FieldIsAssetManagement] = false;
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsAssetManagement] = true;
            }
        }

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIsOwnIPSetsStandardGateway, null);
            hash.Add(Models.s_FieldDFPRedundantPathLayoutSupported, null);
            hash.Add(Models.s_FieldIsReportingSystem, null);
            hash.Add(Models.s_FieldIsTimeSynchronisation, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // Attribute OwnIP_SetsStandardGateway
            string attr = nav.GetAttribute(Attributes.s_OwnIPSetsStandardGateway, String.Empty);
            hash[Models.s_FieldIsOwnIPSetsStandardGateway] = Help.GetBool(attr, Attributes.s_DefaultOwnIPSetsStandardGateway);

            // Get RTClass3Properties, optional
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_RTClass3Properties, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                // Attribute DFP_RedundantPathLayoutSupported
                if (nodes.Current != null)
                {
                    attr = nodes.Current.GetAttribute(Attributes.s_DFPRedundantPathLayoutSupported, String.Empty);
                }

                hash[Models.s_FieldDFPRedundantPathLayoutSupported] = Help.GetBool(attr, Attributes.s_DefaultDFPRedundantPathLayoutSupported);
            }

            // Check whether ReportingSystem is available.
            nodes = nav.SelectChildren(Elements.s_ReportingSystem, Namespaces.s_GsdmlDeviceProfile);
            hash[Models.s_FieldIsReportingSystem] = false;
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsReportingSystem] = true;
            }

            // Check whether TimeSynchronisation is available.
            nodes = nav.SelectChildren(Elements.s_TimeSynchronisation, Namespaces.s_GsdmlDeviceProfile);
            hash[Models.s_FieldIsTimeSynchronisation] = false;
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldIsTimeSynchronisation] = true;
            }
        }

        protected override void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAdditionalForwardingRulesSupported, null);

            // Call base class method first.
            base.PrepareMediaRedundancy(nav, ref hash);

            // Attribute AdditionalForwardingRulesSupported
            string attr = nav.GetAttribute(Attributes.s_AdditionalForwardingRulesSupported, String.Empty);
            hash[Models.s_FieldAdditionalForwardingRulesSupported] = Help.GetBool(attr, Attributes.s_DefaultAdditionalForwardingRulesSupported);
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfIenergy, null);
            hash.Add(Models.s_FieldReportingSystemEvents, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ProfIenergy, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectProfIenergy, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectEnergySavingModeList + "' couldn't be created!");
            }
            hash[Models.s_FieldProfIenergy] = obj;

            obj = null;
            nodes = nav.SelectChildren(Elements.s_ReportingSystemEvents, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectReportingSystemEvents, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectEnergySavingModeList + "' couldn't be created!");
            }
            hash[Models.s_FieldReportingSystemEvents] = obj;
        }

        protected virtual void PrepareProfIenergy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldProfileVersion, null);
            hash.Add(Models.s_FieldEntityClass, null);
            hash.Add(Models.s_FieldEntitySubclass, null);
            hash.Add(Models.s_FieldIsDynamicTimeAndEnergyValues, null);
            hash.Add(Models.s_FieldEnergySavingModeList, null);
            hash.Add(Models.s_FieldMeasurementList, null);

            string attr = nav.GetAttribute(Attributes.s_ProfileVersion, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldProfileVersion] = attr;
            }

            // Get EntityClass attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_EntityClass, String.Empty);
            if (Enums.IsEntityClassEnumValueConvertable(attr))
            {
                hash[Models.s_FieldEntityClass] = Enums.ConvertEntityClassEnum(attr);
            }

            // Get EntitySubclass attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_EntitySubclass, String.Empty);
            if (Enums.IsEntitySubclassEnumValueConvertable(attr))
            {
                hash[Models.s_FieldEntitySubclass] = Enums.ConvertEntitySubclassEnum(attr);
            }

            // Attribute DynamicTimeAndEnergyValues
            attr = nav.GetAttribute(Attributes.s_DynamicTimeAndEnergyValues, String.Empty);
            hash[Models.s_FieldIsDynamicTimeAndEnergyValues] = Help.GetBool(attr, Attributes.s_DefaultDynamicTimeAndEnergyValues);

            // Navigate to EnergySavingModeList and create it. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_EnergySavingModeList, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectEnergySavingModeList, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectEnergySavingModeList + "' couldn't be created!");
            }
            hash[Models.s_FieldEnergySavingModeList] = obj;

            // Navigate to MeasurementList and create it. Optional.
            obj = null;
            nodes = nav.SelectChildren(Elements.s_MeasurementList, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectMeasurementList, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMeasurementList + "' couldn't be created!");
            }
            hash[Models.s_FieldMeasurementList] = obj;
        }

        protected virtual void PrepareEnergySavingModeList(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare EnergySavingModeList with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldEnergySavingModeItems, null);

            // Navigate to EnergySavingModeItem elements and create it. One or more.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_EnergySavingModeItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectEnergySavingModeItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectEnergySavingModeItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldEnergySavingModeItems] = list;
        }

        protected virtual void PrepareEnergySavingModeItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.FieldID, null);
            hash.Add(Models.s_FieldTimeToPause, null);
            hash.Add(Models.s_FieldRtto, null);
            hash.Add(Models.s_FieldTimeMinLengthOfStay, null);
            hash.Add(Models.s_FieldPowerConsumption, null);
            hash.Add(Models.s_FieldEnergyConsumptionToPause, null);
            hash.Add(Models.s_FieldEnergyConsumptionToOperation, null);

            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.FieldID] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_TimeToPause, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTimeToPause] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_Rtto, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldRtto] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_TimeMinLengthOfStay, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldTimeMinLengthOfStay] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_PowerConsumption, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldPowerConsumption] = System.Xml.XmlConvert.ToSingle(attr);
            }
            else
            {
                hash[Models.s_FieldPowerConsumption] = 0.0f;
            }

            attr = nav.GetAttribute(Attributes.s_EnergyConsumptionToPause, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldEnergyConsumptionToPause] = System.Xml.XmlConvert.ToSingle(attr);
            }
            else
            {
                hash[Models.s_FieldEnergyConsumptionToPause] = 0.0f;
            }

            attr = nav.GetAttribute(Attributes.s_EnergyConsumptionToOperation, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldEnergyConsumptionToOperation] = System.Xml.XmlConvert.ToSingle(attr);
            }
            else
            {
                hash[Models.s_FieldEnergyConsumptionToOperation] = 0.0f;
            }
        }

        protected virtual void PrepareMeasurementList(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare PrepareMeasurementList with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMeasurementItems, null);

            // Navigate to MeasurementItem elements and create it. One or more.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_MeasurementItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectMeasurementItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMeasurementItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldMeasurementItems] = list;
        }

        protected virtual void PrepareMeasurementItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMeasurementNumber, null);
            hash.Add(Models.s_FieldMeasurementValues, null);

            string attr = nav.GetAttribute(Attributes.s_Number, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMeasurementNumber] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // Navigate to MeasurementValue elements and create it. One or more.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_MeasurementValue, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectMeasurementValue, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMeasurementValue + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldMeasurementValues] = list;
        }

        protected virtual void PrepareMeasurementValue(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.FieldID, null);
            hash.Add(Models.s_FieldAccuracyDomain, null);
            hash.Add(Models.s_FieldAccuracyClass, null);

            string attr = nav.GetAttribute(Attributes.ID, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.FieldID] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_AccuracyDomain, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldAccuracyDomain] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_AccuracyClass, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldAccuracyClass] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        protected virtual void PrepareReportingSystemEvents(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ReportingSystemEvents with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldObservers, null);

            // Navigate to EnergySavingModeItem elements and create it. One or more.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_Observer, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectObserver, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectObserver + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldObservers] = list;
        }

        protected virtual void PrepareObserver(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldType, null);

            string attr = nav.GetAttribute(Attributes.s_Type, String.Empty);
            if (Enums.IsObserverTypeEnumValueConvertable(attr))
                hash[Models.s_FieldType] = Enums.ConvertObserverTypeEnum(attr);
        }

        #endregion

        #endregion

    }
}
