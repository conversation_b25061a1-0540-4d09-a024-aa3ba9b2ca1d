﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: DecentralDeviceConfigureManager.cs        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.IODevice;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.PNProjectManager;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class DecentralDeviceConfigureManager
    {
        private Topology.Topology m_Topology
        {
            get;
        }

        private ListOfNodes.ListOfNodes m_ListOfNodes
        {
            get;
        }

        private Configuration.Configuration m_Configuration
        {
            get;
        }

        private Project m_Project;

        internal DecentralDeviceConfigureManager(Project project,
            Configuration.Configuration configuration,
            ListOfNodes.ListOfNodes listOfNodes,
            Topology.Topology topology)
        {
            m_Project = project;
            m_ListOfNodes = listOfNodes;
            m_Configuration = configuration;
            m_Topology = topology;
        }

        internal void Configure(List<DecentralDeviceType> cfgDecentralDevice,
            IOSystem ioSystem, ref IOAddressManager ioAddressManager, List<CentralDeviceType> xmlCentralDevices = null)
        {
            if (cfgDecentralDevice.Count > 0)
            {
                cfgDecentralDevice = cfgDecentralDevice.OrderBy(
                    d => d.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.DeviceNumber).ToList();
            }

            IOAddressConfigureManager ioConfigureManager = new IOAddressConfigureManager(m_ListOfNodes);
            ioConfigureManager.Configure(cfgDecentralDevice, ioAddressManager);

            foreach (DecentralDeviceType xmlDecentralDevice in cfgDecentralDevice)
            {
                ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ProjectManagerUtilities.GetDecentralDeviceFromListOfNodes(
                        m_ListOfNodes,
                        xmlDecentralDevice.DeviceRefID);

                string gsdPath = Path.GetFileName(lonDecentralDevice.GSDPath);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    Catalog.DeviceList[string.Format(CultureInfo.InvariantCulture, "{0}\\{1}", gsdPath, lonDecentralDevice.GSDRefID)];

                DecentralDevice decentralDevice = new DecentralDevice(xmlDecentralDevice.DeviceRefID);
                decentralDevice.PCLCatalogObject = decentralDeviceCatalog;

                AddSubmoduleBL(decentralDeviceCatalog, xmlDecentralDevice, lonDecentralDevice, ref ioAddressManager, decentralDevice);

                DecentralDeviceBL decentralDeviceBL = new DecentralDeviceBL(decentralDevice);
                m_Project.BusinessLogicList.Add(decentralDeviceBL);
                decentralDeviceBL.Configure(xmlDecentralDevice, lonDecentralDevice);

                DecentralInterfaceConfigureManager decentralInterfaceConfigureManager = new DecentralInterfaceConfigureManager();
                Interface interfaceSubmodule =
                    decentralInterfaceConfigureManager.Configure(xmlDecentralDevice, decentralDeviceCatalog, decentralDevice, m_Project);

                string subnetRefID = xmlDecentralDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID;
                if (subnetRefID != null)
                {
                    interfaceSubmodule.Node.Subnet = ProjectManagerUtilities.FindSubnetById(
                        subnetRefID,
                        m_Project.BusinessLogicList);
                }

                DevicePortConfigureManager devicePortConfigureManager = new DevicePortConfigureManager(m_Project, m_Topology);
                devicePortConfigureManager.Configure(xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports,
                    interfaceSubmodule,
                    decentralDeviceCatalog,
                    xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID,
                    gsdPath);

                PNIOD pnIOD = new PNIOD();
                interfaceSubmodule.PNIOD = pnIOD;
                pnIOD.Id = AttributeUtilities.GetName(interfaceSubmodule) + "_PnIOD";
                PNIODBusinessLogic pnIodBusinessLogic = new PNIODBusinessLogic(pnIOD);
                m_Project.BusinessLogicList.Add(pnIodBusinessLogic);

                if (ioSystem != null)
                {
                    ioSystem.AddParticipant(pnIOD);
                    pnIOD.AssignedController = pnIOD.IOSystem.PNIOC;
                    SyncDomain syncDomain =
                        pnIOD.IOSystem.PNIOC.GetDevice().GetInterface().SyncDomain;
                    syncDomain.AddInterface(interfaceSubmodule);
                }
                else
                {
                    string syncDomainRefID =
                        xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                            .SyncDomainRefID;
                    if (syncDomainRefID != null)
                    {
                        ProjectManagerUtilities.FindSyncDomainById(syncDomainRefID, m_Project.BusinessLogicList)
                            .AddInterface(interfaceSubmodule);
                    }
                }

                decentralDevice.IsShared = xmlDecentralDevice.SharedDevice != null
                                           && xmlDecentralDevice.SharedDevice.Count > 1;
                DecentralModuleConfigureManager moduleConfigureManager = new DecentralModuleConfigureManager(m_Project, m_Topology);
                moduleConfigureManager.Configure(xmlDecentralDevice, decentralDevice, lonDecentralDevice, gsdPath, ref ioAddressManager);

                // Interface is configured after PnIod is created, because some attributes need to be set in PnIod object.
                interfaceSubmodule.InterfaceBL.Configure(xmlDecentralDevice.DecentralDeviceInterface);

                SharedDeviceConfigureManager sharedDeviceConfigureManager = new SharedDeviceConfigureManager(m_Configuration);
                sharedDeviceConfigureManager.Configure(xmlDecentralDevice.DecentralDeviceInterface
                    .AdvancedOptions.RealTimeSettings.IOCycle.SharedDevicePart,
                    xmlDecentralDevice.SharedDevice,
                    decentralDevice,
                    xmlDecentralDevice,
                    xmlCentralDevices);

                SnmpConfigureManager snmpConfigureManager = new SnmpConfigureManager();
                snmpConfigureManager.Configure(xmlDecentralDevice.AdvancedConfiguration.Snmp, decentralDevice.GetInterface());
            }
        }

        private void AddSubmoduleBL(DecentralDeviceCatalog decentralDeviceCatalog,
                                    DecentralDeviceType xmlDecentralDevice,
                                    ListOfNodes.DecentralDeviceType lonDecentralDevice,
                                    ref IOAddressManager ioAddressManager, DecentralDevice decentralDevice)
        {
            foreach (SubmoduleCatalog submoduleCatalog in decentralDeviceCatalog.VirtualSubmoduleList)
            {
                Submodule submodule;
                uint subslotNumberCatalog = submoduleCatalog.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    1);
                submodule = new Submodule(submoduleCatalog, (int)subslotNumberCatalog);
                ModuleTypeSubmodule xmlSubmodule = xmlDecentralDevice.Module?.SelectMany(m => m.Submodule)
                    ?.FirstOrDefault(m => m.GSDRefID == submodule.Id);

                List<object> ioAddresses = null;
                if (xmlSubmodule != null
                    && xmlSubmodule.IOAddresses != null
                    && xmlSubmodule.IOAddresses.Count > 0)
                {
                    ioAddresses = xmlSubmodule.IOAddresses;
                }
                else
                {
                    ModuleType xmlModule =
                        xmlDecentralDevice.Module.FirstOrDefault(m => m.GSDRefID == lonDecentralDevice.GSDRefID);
                    if (xmlModule != null
                        && xmlModule.IOAddresses != null
                        && xmlModule.IOAddresses.Count > 0)
                    {
                        ioAddresses = xmlModule.IOAddresses;
                    }
                }

                SubmoduleBL submoduleBL = new SubmoduleBL(submodule, ioAddresses, ioAddressManager);
                decentralDevice.AddVirtualSubmodule(submodule);
                m_Project.BusinessLogicList.Add(submoduleBL);
            }
        }
    }
}