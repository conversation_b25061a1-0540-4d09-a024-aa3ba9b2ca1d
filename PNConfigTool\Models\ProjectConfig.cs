using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace PNConfigTool.Models
{
    /// <summary>
    /// 项目配置类，采用标准化的PNConfigLib架构
    /// </summary>
    public class ProjectConfig
    {
        /// <summary>
        /// 项目元数据
        /// </summary>
        public ProjectMetadata ProjectMetadata { get; set; } = new ProjectMetadata();

        /// <summary>
        /// ListOfNodes配置
        /// </summary>
        public ListOfNodesConfiguration ListOfNodesConfiguration { get; set; } = new ListOfNodesConfiguration();

        /// <summary>
        /// Configuration配置设置
        /// </summary>
        public ConfigurationSettings ConfigurationSettings { get; set; } = new ConfigurationSettings();

        /// <summary>
        /// 项目特定扩展字段
        /// </summary>
        public ProjectSpecificExtensions ProjectSpecificExtensions { get; set; } = new ProjectSpecificExtensions();

        /// <summary>
        /// 输出配置
        /// </summary>
        public OutputConfiguration OutputConfiguration { get; set; } = new OutputConfiguration();
    }

    /// <summary>
    /// 项目元数据
    /// </summary>
    public class ProjectMetadata
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreationDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改日期
        /// </summary>
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 版本号
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 当前步骤状态
        /// </summary>
        public StepStatus CurrentStepStatus { get; set; } = StepStatus.NONE;

        /// <summary>
        /// 项目描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// ListOfNodes配置
    /// </summary>
    public class ListOfNodesConfiguration
    {
        /// <summary>
        /// ListOfNodes标识符
        /// </summary>
        public string ListOfNodesID { get; set; } = string.Empty;

        /// <summary>
        /// Schema版本
        /// </summary>
        public string SchemaVersion { get; set; } = "1.0";

        /// <summary>
        /// PROFINET驱动配置
        /// </summary>
        public PNDriverConfig PNDriver { get; set; } = new PNDriverConfig();

        /// <summary>
        /// 分布式设备列表
        /// </summary>
        public List<DecentralDeviceNode> DecentralDevices { get; set; } = new List<DecentralDeviceNode>();
    }

    /// <summary>
    /// PROFINET驱动配置
    /// </summary>
    public class PNDriverConfig
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceID { get; set; } = "PN_Driver_1";

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = "PROFINET Driver";

        /// <summary>
        /// 设备版本
        /// </summary>
        public string DeviceVersion { get; set; } = "v3.1";

        /// <summary>
        /// 接口配置
        /// </summary>
        public PNDriverInterface Interface { get; set; } = new PNDriverInterface();
    }

    /// <summary>
    /// PROFINET驱动接口配置
    /// </summary>
    public class PNDriverInterface
    {
        /// <summary>
        /// 接口ID
        /// </summary>
        public string InterfaceID { get; set; } = "PN_Driver_1_Interface";

        /// <summary>
        /// 接口名称
        /// </summary>
        public string InterfaceName { get; set; } = "PN_Driver_1_Interface";

        /// <summary>
        /// 接口类型
        /// </summary>
        public string InterfaceType { get; set; } = "Custom";

        /// <summary>
        /// 自定义接口路径
        /// </summary>
        public string CustomInterfacePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// 分布式设备节点（用于ListOfNodes）
    /// </summary>
    public class DecentralDeviceNode
    {
        /// <summary>
        /// 设备ID - 唯一标识符，格式：DNS_CompatibleName + "_" + 序号
        /// 例如：ET200SP_1, IM60_2
        /// 此ID被 Configuration 中的 DeviceRefID 引用
        /// </summary>
        public string DeviceID { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型 - DAP节点名称，仅用于UI显示和导航，不参与XML生成
        /// </summary>
        public string DeviceType { get; set; } = string.Empty;

        /// <summary>
        /// GSDML文件路径
        /// </summary>
        public string GSDPath { get; set; } = string.Empty;

        /// <summary>
        /// GSDML引用ID
        /// </summary>
        public string GSDRefID { get; set; } = string.Empty;

        /// <summary>
        /// 接口配置
        /// </summary>
        public DecentralDeviceInterface Interface { get; set; } = new DecentralDeviceInterface();
    }

    /// <summary>
    /// 分布式设备接口配置
    /// </summary>
    public class DecentralDeviceInterface
    {
        /// <summary>
        /// 接口ID
        /// </summary>
        public string InterfaceID { get; set; } = string.Empty;

        /// <summary>
        /// 接口名称
        /// </summary>
        public string InterfaceName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Configuration配置设置
    /// </summary>
    public class ConfigurationSettings
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public string ConfigurationID { get; set; } = string.Empty;

        /// <summary>
        /// 配置名称
        /// </summary>
        public string ConfigurationName { get; set; } = string.Empty;

        /// <summary>
        /// ListOfNodes引用ID
        /// </summary>
        public string ListOfNodesRefID { get; set; } = string.Empty;

        /// <summary>
        /// Schema版本
        /// </summary>
        public string SchemaVersion { get; set; } = "1.0";



        /// <summary>
        /// 中央设备配置
        /// </summary>
        public CentralDeviceConfig CentralDevice { get; set; } = new CentralDeviceConfig();

        /// <summary>
        /// 分布式设备配置列表
        /// </summary>
        public List<DecentralDeviceConfig> DecentralDevices { get; set; } = new List<DecentralDeviceConfig>();

        /// <summary>
        /// 子网配置列表（可选，仅在需要显式子网配置时使用）
        /// </summary>
        public List<SubnetConfig> Subnets { get; set; } = new List<SubnetConfig>();

        /// <summary>
        /// 检查是否启用了显式子网配置
        /// </summary>
        public bool HasExplicitSubnetConfiguration => Subnets.Count > 0;
    }



    /// <summary>
    /// 中央设备配置
    /// </summary>
    public class CentralDeviceConfig
    {
        /// <summary>
        /// 设备引用ID
        /// </summary>
        public string DeviceRefID { get; set; } = string.Empty;



        /// <summary>
        /// 中央设备接口配置
        /// </summary>
        public CentralDeviceInterfaceConfig CentralDeviceInterface { get; set; } = new CentralDeviceInterfaceConfig();

        /// <summary>
        /// 高级配置
        /// </summary>
        public AdvancedConfigurationConfig AdvancedConfiguration { get; set; } = new AdvancedConfigurationConfig();
    }

    /// <summary>
    /// 中央设备接口配置
    /// </summary>
    public class CentralDeviceInterfaceConfig
    {
        /// <summary>
        /// 接口引用ID
        /// </summary>
        public string InterfaceRefID { get; set; } = string.Empty;

        /// <summary>
        /// 以太网地址配置
        /// </summary>
        public EthernetAddressesConfig EthernetAddresses { get; set; } = new EthernetAddressesConfig();
    }

    /// <summary>
    /// 分布式设备配置（用于Configuration）
    /// </summary>
    public class DecentralDeviceConfig
    {
        /// <summary>
        /// 设备引用ID - 引用对应的 ListOfNodes 中的 DeviceID
        /// 格式：DNS_CompatibleName + "_" + 序号（例如：ET200SP_1）
        /// </summary>
        public string DeviceRefID { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型 - DAP节点名称，仅用于UI显示和导航，不参与XML生成
        /// </summary>
        public string DeviceType { get; set; } = string.Empty;

        /// <summary>
        /// GSDML文件路径
        /// </summary>
        public string GSDMLFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 分布式设备接口配置
        /// </summary>
        public DecentralDeviceInterfaceConfig DecentralDeviceInterface { get; set; } = new DecentralDeviceInterfaceConfig();

        /// <summary>
        /// 模块配置列表
        /// </summary>
        public List<ModuleConfig> Modules { get; set; } = new List<ModuleConfig>();

        /// <summary>
        /// 设置GSDML文件路径（自动转换为相对路径）
        /// </summary>
        /// <param name="absolutePath">绝对路径</param>
        /// <param name="projectDirectory">项目目录</param>
        public void SetGSDMLFilePath(string absolutePath, string projectDirectory)
        {
            if (string.IsNullOrEmpty(absolutePath))
            {
                GSDMLFilePath = string.Empty;
                return;
            }

            // 将绝对路径转换为相对路径
            GSDMLFilePath = PNConfigTool.Utilities.PathHelper.ToRelativePath(absolutePath, projectDirectory);
        }

        /// <summary>
        /// 获取GSDML文件的绝对路径
        /// </summary>
        /// <param name="projectDirectory">项目目录</param>
        /// <returns>GSDML文件的绝对路径</returns>
        public string GetGSDMLAbsolutePath(string projectDirectory)
        {
            if (string.IsNullOrEmpty(GSDMLFilePath))
            {
                return string.Empty;
            }

            // 将相对路径转换为绝对路径
            return PNConfigTool.Utilities.PathHelper.ToAbsolutePath(GSDMLFilePath, projectDirectory);
        }

        /// <summary>
        /// 确保GSDML文件存在并返回有效的绝对路径
        /// </summary>
        /// <param name="projectDirectory">项目目录</param>
        /// <returns>存在的GSDML文件绝对路径，如果不存在则返回空字符串</returns>
        public string EnsureGSDMLFileExists(string projectDirectory)
        {
            if (string.IsNullOrEmpty(GSDMLFilePath))
            {
                return string.Empty;
            }

            string absolutePath = GetGSDMLAbsolutePath(projectDirectory);
            return PNConfigTool.Utilities.PathHelper.EnsureFileExists(absolutePath, projectDirectory);
        }

    }

    /// <summary>
    /// 分布式设备接口配置
    /// </summary>
    public class DecentralDeviceInterfaceConfig
    {
        /// <summary>
        /// 接口引用ID
        /// </summary>
        public string InterfaceRefID { get; set; } = string.Empty;

        /// <summary>
        /// 以太网地址配置
        /// </summary>
        public EthernetAddressesConfig EthernetAddresses { get; set; } = new EthernetAddressesConfig();

        /// <summary>
        /// 高级配置
        /// </summary>
        public DecentralAdvancedConfigurationConfig AdvancedConfiguration { get; set; } = new DecentralAdvancedConfigurationConfig();
    }

    /// <summary>
    /// 以太网地址配置
    /// </summary>
    public class EthernetAddressesConfig
    {
        /// <summary>
        /// IP协议配置
        /// </summary>
        public IPProtocolConfig IPProtocol { get; set; } = new IPProtocolConfig();

        /// <summary>
        /// PROFINET设备名称配置
        /// </summary>
        public PROFINETDeviceNameConfig PROFINETDeviceName { get; set; } = new PROFINETDeviceNameConfig();

        /// <summary>
        /// 子网引用ID（可选，仅在需要显式子网配置时使用）
        /// </summary>
        public string? SubnetRefID { get; set; } = null;

        /// <summary>
        /// IO系统引用ID（可选，仅在需要显式子网配置时使用）
        /// </summary>
        public string? IOSystemRefID { get; set; } = null;

        /// <summary>
        /// 检查是否使用显式子网配置
        /// </summary>
        public bool HasExplicitSubnetConfiguration =>
            !string.IsNullOrEmpty(SubnetRefID) || !string.IsNullOrEmpty(IOSystemRefID);
    }

    /// <summary>
    /// IP协议配置
    /// </summary>
    public class IPProtocolConfig
    {
        /// <summary>
        /// 在项目中设置
        /// </summary>
        public SetInTheProjectConfig SetInTheProject { get; set; } = new SetInTheProjectConfig();
    }

    /// <summary>
    /// 在项目中设置的配置
    /// </summary>
    public class SetInTheProjectConfig
    {
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; } = string.Empty;

        /// <summary>
        /// 子网掩码
        /// </summary>
        public string SubnetMask { get; set; } = string.Empty;

        /// <summary>
        /// 路由地址
        /// </summary>
        public string RouterAddress { get; set; } = string.Empty;

        /// <summary>
        /// 与IO控制器同步路由设置
        /// </summary>
        public bool SynchronizeRouterSettingsWithIOController { get; set; } = true;
    }

    /// <summary>
    /// PROFINET设备名称配置
    /// </summary>
    public class PROFINETDeviceNameConfig
    {
        /// <summary>
        /// PROFINET设备名称
        /// </summary>
        public string PNDeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 设备编号
        /// </summary>
        public int DeviceNumber { get; set; } = 1;
    }

    /// <summary>
    /// 高级配置
    /// </summary>
    public class AdvancedConfigurationConfig
    {
        /// <summary>
        /// 实时设置
        /// </summary>
        public RealTimeSettingsConfig RealTimeSettings { get; set; } = new RealTimeSettingsConfig();
    }

    /// <summary>
    /// 实时设置配置
    /// </summary>
    public class RealTimeSettingsConfig
    {
        /// <summary>
        /// IO通信配置
        /// </summary>
        public IOCommunicationConfig IOCommunication { get; set; } = new IOCommunicationConfig();
    }

    /// <summary>
    /// IO通信配置
    /// </summary>
    public class IOCommunicationConfig
    {
        /// <summary>
        /// 发送时钟
        /// </summary>
        public double SendClock { get; set; } = 1.0;
    }

    /// <summary>
    /// 模块配置
    /// </summary>
    public class ModuleConfig
    {
        /// <summary>
        /// 模块引用ID
        /// </summary>
        public string ModuleRefID { get; set; } = string.Empty;

        /// <summary>
        /// 槽位号
        /// </summary>
        public int SlotNumber { get; set; } = 0;

        /// <summary>
        /// GSDML引用ID
        /// </summary>
        public string GSDRefID { get; set; } = string.Empty;

        /// <summary>
        /// 子模块配置列表
        /// </summary>
        public List<SubmoduleConfig> Submodules { get; set; } = new List<SubmoduleConfig>();
    }

    /// <summary>
    /// 子模块配置
    /// </summary>
    public class SubmoduleConfig
    {
        /// <summary>
        /// 子模块引用ID
        /// </summary>
        public string SubmoduleRefID { get; set; } = string.Empty;



        /// <summary>
        /// UI属性（保留的UI相关字段）
        /// </summary>
        public UIPropertiesConfig UIProperties { get; set; } = new UIPropertiesConfig();

        /// <summary>
        /// 地址配置（保留的地址配置字段）
        /// </summary>
        public AddressConfigurationConfig AddressConfiguration { get; set; } = new AddressConfigurationConfig();
    }

    /// <summary>
    /// UI属性配置（保留的UI相关字段）
    /// </summary>
    public class UIPropertiesConfig
    {
        /// <summary>
        /// 索引
        /// </summary>
        public int Index { get; set; } = 0;

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected { get; set; } = false;

        /// <summary>
        /// 位置
        /// </summary>
        public string Position { get; set; } = string.Empty;
    }

    /// <summary>
    /// 地址配置（保留的地址配置字段）
    /// </summary>
    public class AddressConfigurationConfig
    {
        /// <summary>
        /// 输入起始地址
        /// </summary>
        public string InputStartAddress { get; set; } = string.Empty;

        /// <summary>
        /// 输出起始地址
        /// </summary>
        public string OutputStartAddress { get; set; } = string.Empty;

        /// <summary>
        /// 输入地址
        /// </summary>
        public int InputAddress { get; set; } = 0;

        /// <summary>
        /// 输出地址
        /// </summary>
        public int OutputAddress { get; set; } = 0;
    }

    /// <summary>
    /// 子网配置
    /// </summary>
    public class SubnetConfig
    {
        /// <summary>
        /// 子网ID
        /// </summary>
        public string SubnetID { get; set; } = "Subnet_1";

        /// <summary>
        /// IO系统配置列表
        /// </summary>
        public List<IOSystemConfig> IOSystems { get; set; } = new List<IOSystemConfig>();

        /// <summary>
        /// 域管理配置
        /// </summary>
        public DomainManagementConfig DomainManagement { get; set; } = new DomainManagementConfig();
    }

    /// <summary>
    /// IO系统配置
    /// </summary>
    public class IOSystemConfig
    {
        /// <summary>
        /// IO系统ID
        /// </summary>
        public string IOSystemID { get; set; } = "IOSystem_1";

        /// <summary>
        /// 通用配置
        /// </summary>
        public IOSystemGeneralConfig General { get; set; } = new IOSystemGeneralConfig();
    }

    /// <summary>
    /// IO系统通用配置
    /// </summary>
    public class IOSystemGeneralConfig
    {
        /// <summary>
        /// IO系统编号
        /// </summary>
        public int IOSystemNumber { get; set; } = 1;

        /// <summary>
        /// IO系统名称
        /// </summary>
        public string IOSystemName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 域管理配置
    /// </summary>
    public class DomainManagementConfig
    {
        /// <summary>
        /// 同步域配置列表
        /// </summary>
        public List<SyncDomainConfig> SyncDomains { get; set; } = new List<SyncDomainConfig>();

        /// <summary>
        /// MRP域配置列表
        /// </summary>
        public List<MrpDomainConfig> MrpDomains { get; set; } = new List<MrpDomainConfig>();
    }

    /// <summary>
    /// 同步域配置
    /// </summary>
    public class SyncDomainConfig
    {
        /// <summary>
        /// 同步域ID
        /// </summary>
        public string SyncDomainID { get; set; } = string.Empty;

        /// <summary>
        /// 同步域名称
        /// </summary>
        public string SyncDomainName { get; set; } = string.Empty;

        /// <summary>
        /// 同步域详细配置
        /// </summary>
        public SyncDomainDetailsConfig Details { get; set; } = new SyncDomainDetailsConfig();
    }

    /// <summary>
    /// 同步域详细配置
    /// </summary>
    public class SyncDomainDetailsConfig
    {
        /// <summary>
        /// 带宽使用配置
        /// </summary>
        public string BandwidthUse { get; set; } = "Maximum 50% cyclic IO data. Balanced proportion";
    }

    /// <summary>
    /// MRP域配置
    /// </summary>
    public class MrpDomainConfig
    {
        /// <summary>
        /// MRP域ID
        /// </summary>
        public string MrpDomainID { get; set; } = string.Empty;

        /// <summary>
        /// MRP域名称
        /// </summary>
        public string MrpDomainName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 项目特定扩展字段（保留的项目特定字段）
    /// </summary>
    public class ProjectSpecificExtensions
    {
        /// <summary>
        /// 主站角色
        /// </summary>
        public string MasterRole { get; set; } = "Controller";

        /// <summary>
        /// 主站自定义接口路径
        /// </summary>
        public string MasterCustomInterfacePath { get; set; } = string.Empty;

        /// <summary>
        /// 主站MRP角色
        /// </summary>
        public string MasterMrpRole { get; set; } = "Not device in the ring";

        /// <summary>
        /// MRP域
        /// </summary>
        public string MrpDomain { get; set; } = "mrpdomain-1";

        /// <summary>
        /// 环端口1
        /// </summary>
        public string RingPort1 { get; set; } = "PROFINET interface_1 [X1]Port_1 [X1 P1 R]";

        /// <summary>
        /// 环端口2
        /// </summary>
        public string RingPort2 { get; set; } = "PROFINET interface_1 [X1]Port_2 [X1 P2 R]";

        /// <summary>
        /// 诊断中断
        /// </summary>
        public bool DiagnosticsInterrupts { get; set; } = false;

        /// <summary>
        /// 全局地址分配信息
        /// </summary>
        public GlobalAddressAllocation GlobalAddressAllocation { get; set; } = new GlobalAddressAllocation();
    }

    /// <summary>
    /// 全局地址分配信息
    /// </summary>
    public class GlobalAddressAllocation
    {
        /// <summary>
        /// 已分配的输入地址段
        /// </summary>
        public List<AddressSegmentInfo> InputSegments { get; set; } = new List<AddressSegmentInfo>();

        /// <summary>
        /// 已分配的输出地址段
        /// </summary>
        public List<AddressSegmentInfo> OutputSegments { get; set; } = new List<AddressSegmentInfo>();

        /// <summary>
        /// 地址分配版本（用于兼容性检查）
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 地址段信息（用于序列化）
    /// </summary>
    public class AddressSegmentInfo
    {
        /// <summary>
        /// 起始地址
        /// </summary>
        public int StartAddress { get; set; }

        /// <summary>
        /// 结束地址
        /// </summary>
        public int EndAddress { get; set; }

        /// <summary>
        /// 地址长度
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 模块ID
        /// </summary>
        public string ModuleId { get; set; } = string.Empty;

        /// <summary>
        /// 地址类型（Input/Output）
        /// </summary>
        public string AddressType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 输出配置
    /// </summary>
    public class OutputConfiguration
    {
        /// <summary>
        /// Configuration.xml文件路径
        /// </summary>
        public string ConfigurationXmlPath { get; set; } = string.Empty;

        /// <summary>
        /// ListOfNodes.xml文件路径
        /// </summary>
        public string ListOfNodesXmlPath { get; set; } = string.Empty;

        /// <summary>
        /// 生成状态
        /// </summary>
        public string GenerationStatus { get; set; } = string.Empty;

        /// <summary>
        /// 命令行工具路径
        /// </summary>
        public string CommandLinePath { get; set; } = string.Empty;

        /// <summary>
        /// 传入参数
        /// </summary>
        public string InputParameters { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录
        /// </summary>
        public string OutputDirectory { get; set; } = string.Empty;
    }

    /// <summary>
    /// 子网配置管理器
    /// </summary>
    public static class SubnetConfigManager
    {
        /// <summary>
        /// 为项目启用显式子网配置
        /// </summary>
        /// <param name="configurationSettings">配置设置</param>
        /// <param name="subnetID">子网ID，默认为"PNIE_1"</param>
        /// <param name="ioSystemID">IO系统ID，默认为"IOSystem1"</param>
        /// <param name="ioSystemNumber">IO系统编号，默认为100</param>
        /// <param name="ioSystemName">IO系统名称，默认为"PROFINET IO-System"</param>
        public static void EnableExplicitSubnetConfiguration(
            ConfigurationSettings configurationSettings,
            string subnetID = "PNIE_1",
            string ioSystemID = "IOSystem1",
            int ioSystemNumber = 100,
            string ioSystemName = "PROFINET IO-System")
        {
            // 如果已经存在子网配置，则不重复添加
            if (configurationSettings.Subnets.Any(s => s.SubnetID == subnetID))
                return;

            var subnet = new SubnetConfig
            {
                SubnetID = subnetID,
                IOSystems = new List<IOSystemConfig>
                {
                    new IOSystemConfig
                    {
                        IOSystemID = ioSystemID,
                        General = new IOSystemGeneralConfig
                        {
                            IOSystemNumber = ioSystemNumber,
                            IOSystemName = ioSystemName
                        }
                    }
                },
                DomainManagement = new DomainManagementConfig
                {
                    SyncDomains = new List<SyncDomainConfig>(),
                    MrpDomains = new List<MrpDomainConfig>()
                }
            };

            configurationSettings.Subnets.Add(subnet);

            // 更新所有设备的引用
            UpdateDeviceSubnetReferences(configurationSettings, subnetID, ioSystemID);
        }

        /// <summary>
        /// 禁用显式子网配置
        /// </summary>
        /// <param name="configurationSettings">配置设置</param>
        public static void DisableExplicitSubnetConfiguration(ConfigurationSettings configurationSettings)
        {
            configurationSettings.Subnets.Clear();

            // 清除所有设备的子网引用
            ClearDeviceSubnetReferences(configurationSettings);
        }

        /// <summary>
        /// 添加同步域配置
        /// </summary>
        /// <param name="configurationSettings">配置设置</param>
        /// <param name="subnetID">子网ID</param>
        /// <param name="syncDomainID">同步域ID</param>
        /// <param name="syncDomainName">同步域名称</param>
        /// <param name="bandwidthUse">带宽使用配置</param>
        public static void AddSyncDomain(
            ConfigurationSettings configurationSettings,
            string subnetID,
            string syncDomainID,
            string syncDomainName,
            string bandwidthUse = "Maximum 50% cyclic IO data. Balanced proportion")
        {
            var subnet = configurationSettings.Subnets.FirstOrDefault(s => s.SubnetID == subnetID);
            if (subnet == null)
                throw new InvalidOperationException($"子网 '{subnetID}' 不存在。请先启用显式子网配置。");

            var syncDomain = new SyncDomainConfig
            {
                SyncDomainID = syncDomainID,
                SyncDomainName = syncDomainName,
                Details = new SyncDomainDetailsConfig
                {
                    BandwidthUse = bandwidthUse
                }
            };

            subnet.DomainManagement.SyncDomains.Add(syncDomain);
        }

        /// <summary>
        /// 添加MRP域配置
        /// </summary>
        /// <param name="configurationSettings">配置设置</param>
        /// <param name="subnetID">子网ID</param>
        /// <param name="mrpDomainID">MRP域ID</param>
        /// <param name="mrpDomainName">MRP域名称</param>
        public static void AddMrpDomain(
            ConfigurationSettings configurationSettings,
            string subnetID,
            string mrpDomainID,
            string mrpDomainName)
        {
            var subnet = configurationSettings.Subnets.FirstOrDefault(s => s.SubnetID == subnetID);
            if (subnet == null)
                throw new InvalidOperationException($"子网 '{subnetID}' 不存在。请先启用显式子网配置。");

            var mrpDomain = new MrpDomainConfig
            {
                MrpDomainID = mrpDomainID,
                MrpDomainName = mrpDomainName
            };

            subnet.DomainManagement.MrpDomains.Add(mrpDomain);
        }

        /// <summary>
        /// 更新设备的子网引用
        /// </summary>
        private static void UpdateDeviceSubnetReferences(
            ConfigurationSettings configurationSettings,
            string subnetRefID,
            string ioSystemRefID)
        {
            // 更新中央设备引用
            if (configurationSettings.CentralDevice?.CentralDeviceInterface?.EthernetAddresses != null)
            {
                configurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID = subnetRefID;
                configurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID = ioSystemRefID;
            }

            // 更新分布式设备引用
            foreach (var device in configurationSettings.DecentralDevices)
            {
                if (device.DecentralDeviceInterface?.EthernetAddresses != null)
                {
                    device.DecentralDeviceInterface.EthernetAddresses.SubnetRefID = subnetRefID;
                    device.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID = ioSystemRefID;
                }
            }
        }

        /// <summary>
        /// 清除设备的子网引用
        /// </summary>
        private static void ClearDeviceSubnetReferences(ConfigurationSettings configurationSettings)
        {
            // 清除中央设备引用
            if (configurationSettings.CentralDevice?.CentralDeviceInterface?.EthernetAddresses != null)
            {
                configurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID = null;
                configurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID = null;
            }

            // 清除分布式设备引用
            foreach (var device in configurationSettings.DecentralDevices)
            {
                if (device.DecentralDeviceInterface?.EthernetAddresses != null)
                {
                    device.DecentralDeviceInterface.EthernetAddresses.SubnetRefID = null;
                    device.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID = null;
                }
            }
        }
    }

    /// <summary>
    /// 步骤状态枚举
    /// </summary>
    public enum StepStatus
    {
        /// <summary>
        /// 无状态
        /// </summary>
        NONE,

        /// <summary>
        /// GSDML已加载
        /// </summary>
        GSDML_LOADED,

        /// <summary>
        /// 主站已配置
        /// </summary>
        MASTER_CONFIGURED,

        /// <summary>
        /// 设备已添加
        /// </summary>
        DEVICES_ADDED,

        /// <summary>
        /// 配置已生成
        /// </summary>
        CONFIG_GENERATED,

        /// <summary>
        /// 配置已完成
        /// </summary>
        COMPLETED
    }

    /// <summary>
    /// 分布式设备高级配置
    /// </summary>
    public class DecentralAdvancedConfigurationConfig
    {
        /// <summary>
        /// 实时设置
        /// </summary>
        public DecentralRealTimeSettingsConfig RealTimeSettings { get; set; } = new DecentralRealTimeSettingsConfig();

        /// <summary>
        /// 媒体冗余配置
        /// </summary>
        public MediaRedundancyConfig? MediaRedundancy { get; set; }
    }

    /// <summary>
    /// 分布式设备实时设置配置
    /// </summary>
    public class DecentralRealTimeSettingsConfig
    {
        /// <summary>
        /// IO周期配置
        /// </summary>
        public IOCycleConfig IOCycle { get; set; } = new IOCycleConfig();

        /// <summary>
        /// 同步配置
        /// </summary>
        public SynchronizationConfig Synchronization { get; set; } = new SynchronizationConfig();
    }

    /// <summary>
    /// IO周期配置
    /// </summary>
    public class IOCycleConfig
    {
        /// <summary>
        /// 更新时间配置
        /// </summary>
        public UpdateTimeConfig UpdateTime { get; set; } = new UpdateTimeConfig();

        /// <summary>
        /// 无IO数据时接受的更新周期数（数据保持时间）
        /// </summary>
        public uint AcceptedUpdateCyclesWithoutIOData { get; set; } = 3;
    }

    /// <summary>
    /// 更新时间配置
    /// </summary>
    public class UpdateTimeConfig
    {
        /// <summary>
        /// 更新时间模式
        /// </summary>
        public UpdateTimeMode Mode { get; set; } = UpdateTimeMode.Manual;

        /// <summary>
        /// 更新时间值（毫秒）
        /// </summary>
        public float Value { get; set; } = 128.0f;
    }

    /// <summary>
    /// 更新时间模式枚举
    /// </summary>
    public enum UpdateTimeMode
    {
        /// <summary>
        /// 自动模式
        /// </summary>
        Automatic,

        /// <summary>
        /// 手动模式
        /// </summary>
        Manual
    }

    /// <summary>
    /// 同步配置
    /// </summary>
    public class SynchronizationConfig
    {
        /// <summary>
        /// 同步角色
        /// </summary>
        public SyncRole SynchronizationRole { get; set; } = SyncRole.Unsynchronized;

        /// <summary>
        /// 同步域引用ID
        /// </summary>
        public string SyncDomainRefID { get; set; } = string.Empty;
    }

    /// <summary>
    /// 同步角色枚举
    /// </summary>
    public enum SyncRole
    {
        /// <summary>
        /// 未同步
        /// </summary>
        Unsynchronized,

        /// <summary>
        /// 同步从设备
        /// </summary>
        SyncSlave,

        /// <summary>
        /// 同步主设备
        /// </summary>
        SyncMaster
    }

    /// <summary>
    /// 媒体冗余配置
    /// </summary>
    public class MediaRedundancyConfig
    {
        /// <summary>
        /// MRP环配置列表
        /// </summary>
        public List<MrpRingConfig> MrpRings { get; set; } = new List<MrpRingConfig>();
    }

    /// <summary>
    /// MRP环配置
    /// </summary>
    public class MrpRingConfig
    {
        /// <summary>
        /// MRP域引用ID
        /// </summary>
        public string MrpDomainRefID { get; set; } = "mrpdomain-1";

        /// <summary>
        /// MRP角色
        /// </summary>
        public string MrpRole { get; set; } = string.Empty;

        /// <summary>
        /// 环端口配置列表
        /// </summary>
        public List<RingPortConfig> RingPorts { get; set; } = new List<RingPortConfig>();
    }

    /// <summary>
    /// 环端口配置
    /// </summary>
    public class RingPortConfig
    {
        /// <summary>
        /// 端口号（纯数字，如"1"、"2"）
        /// </summary>
        public string PortNumber { get; set; } = string.Empty;
    }
}