/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNSharedDeviceConfigStrategy.cs           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Linq;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    /// <summary>
    /// Shared device related part of PNDeviceConfigStrategy class.
    /// </summary>
    internal class PNSharedDeviceConfigStrategy : PNDeviceConfigStrategy
    {
        /// <summary>
        /// Constructor for PNSharedDeviceConfigStrategy.
        /// </summary>
        /// <param name="decoratedInterface">
        /// The InterfaceBL that will be transferred to the upper InterfaceBL class' constructor.
        /// </param>
        public PNSharedDeviceConfigStrategy(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodCheckConsistency);
        }

        /// <summary>
        /// Creates subslot data for shared device.
        /// </summary>
        /// <param name="submodule">The submodule whose subslot data will be created.</param>
        /// ///
        /// <param name="dummySubmodule">Whether submodule is a dummy submodule.</param>
        /// <param name="isApduController">Whether the controller is APDU.</param>
        /// <returns></returns>
        protected override ARConfigSubslotStruct CreateSubslotData(
            PclObject submodule,
            bool dummySubmodule,
            bool isApduController)
        {
            //in case of a SharedDevice the sort order is always APDU!
            return base.CreateSubslotData(submodule, dummySubmodule, true);
        }

        #region Overrides and Overridables

        public override byte[] GetPrmDataBlock()
        {
            ICollection<PclObject> listModules = Parameters.Keys;
            int ioType;
            List<byte> list = new List<byte>();

            foreach (PclObject module in listModules)
            {
                ioType = module.AttributeAccess.GetAnyAttribute<Int32>(InternalAttributeNames.IoType, new AttributeAccessCode(), 0);

                ParameterDataBlockStruct paramBlock = Parameters[module];
                list.AddRange(paramBlock.ToByteArray);
                // for mix modules (with Input address with BitAddress == 0) Additional PRM_DATA
                if ((ioType & 3) == 3)
                {
                    DataAddress dataAddress = module.DataAddresses
                        .FirstOrDefault(a => (a.IoType == IoTypes.Input) 
                                            && (a.GetBitOffset(0) == 0));

                    if (dataAddress != null)
                    {
                        list.AddRange(paramBlock.ToByteArray);
                    }
                }
            }

            return list.ToArray();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="outputRelatedObjects"></param>
        /// <param name="api"></param>
        /// <param name="isApdu"></param>
        /// <param name="inputPNFrameData"></param>
        /// <param name="outputPNFrameData"></param>
        /// <param name="inputRelatedObjects"></param>
        /// <returns></returns>
        protected override void CreateRelatedIOEntries(IPNFrameData inputPNFrameData, 
            IPNFrameData outputPNFrameData, out byte[] inputRelatedObjects, 
            out byte[] outputRelatedObjects, UInt32 api, bool isApdu)
        {
            IocrApiSubblockStruct inputIocrApi = new IocrApiSubblockStruct();
            IocrApiSubblockStruct outputIocrApi = new IocrApiSubblockStruct();
            inputIocrApi.API = outputIocrApi.API = api;

            //get slots of module
            IList<PclObject> modules = PNNavigationUtility.GetModulesSorted(Interface);
            foreach (PclObject module in modules)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                int slotNumber = module.AttributeAccess.GetAnyAttribute<Int32>(InternalAttributeNames.PositionNumber, ac, 0);

                // check whether the module has Input, Output or Diagnostic address
                bool hasInOrOutOrDiagAddr = ConfigUtility.HasModuleInOutOrDiagAddress(module);


                //get available submodules
                IList<PclObject> submodules = GetSubmodulesInternal(module);

                int SubslotNumber = 1;
                //if no submodule available then get data from module, but only if module has not got address
                //When submodules are available but they are shared the "dummy" block is not necessary
                if ((submodules.Count == 0) && !hasInOrOutOrDiagAddr && !IsSharedSubmoduleExist(module))
                {
                    if (module.AttributeAccess.GetAnyAttribute<UInt32>(InternalAttributeNames.PnAPI, ac.GetNew(), 0) != api)
                        continue;

                    CreateRelatedEntries(module, slotNumber, SubslotNumber, inputPNFrameData, outputPNFrameData, inputIocrApi, outputIocrApi, isApdu);
                }
                else
                {
                    //else each submodule provides data
                    foreach (PclObject submodule in submodules)
                    {
                        int subslotNumber = submodule.AttributeAccess.GetAnyAttribute<Int32>(InternalAttributeNames.PnSubslotNumber, ac.GetNew(), 1);
                        if (submodule.AttributeAccess.GetAnyAttribute<UInt32>(InternalAttributeNames.PnAPI, ac.GetNew(), 0) != api)
                            continue;

                        CreateRelatedEntries(submodule, slotNumber, subslotNumber, inputPNFrameData, 
                            outputPNFrameData, inputIocrApi, outputIocrApi, isApdu);
                    }
                }
                // if a physical module has Input, Output or Diagnostic address(es) it must have IO entry(ies)
                if (!hasInOrOutOrDiagAddr
                    || (module.DataAddresses == null))
                {
                    continue;
                }

                if (module.AttributeAccess.GetAnyAttribute<UInt32>(InternalAttributeNames.PnAPI, ac.GetNew(), 0) != api)
                    continue;

                CreateRelatedEntries(module, slotNumber, SubslotNumber, inputPNFrameData, 
                    outputPNFrameData, inputIocrApi, outputIocrApi, isApdu);
                
            }

            inputRelatedObjects = inputIocrApi.ToByteArray;
            outputRelatedObjects = outputIocrApi.ToByteArray;
        }

        /// <summary>
        /// Returns with true, when shared submodule exists under the module
        /// </summary>
        /// <param name="module"></param>
        /// <returns></returns>
        protected override bool IsSharedSubmoduleExist(PclObject module)
        {
            return SharedDeviceUtility.IsSharedSubmoduleExist(module);
        }

        /// <summary>
        /// Returns an array of APIs. 
        /// </summary>
        public override uint[] GetAPIs()
        {
            Apis = new HashSet<uint>();
            foreach (PclObject module in Modules)
            {
                IList<PclObject> submodules = GetSubmodulesInternal(module);
                AttributeAccessCode ac = new AttributeAccessCode();

                foreach (PclObject submodule in submodules)
                {
                    UInt32 api = submodule.AttributeAccess.
                        GetAnyAttribute<UInt32>(InternalAttributeNames.PnAPI, ac, 0);
                    Apis.Add(api);
                }

                PclObject firstSharedSubmodule;

                if ((submodules.Count == 0) && !SharedDeviceUtility
                    .FindSubModule(module, SharedIoAssignment.NotAssigned, out firstSharedSubmodule))
                {
                    //Check whether PNAPI is accessible at the module itself. If it is, then consider it for the generation!
                    UInt32 api = module.AttributeAccess.GetAnyAttribute<UInt32>(InternalAttributeNames.PnAPI, ac.GetNew(), 0);

                    Apis.Add(api);
                }
            }
            return Apis.ToArray();
        }

        /// <summary>
        /// ARFSUDataAdjust : The record is generated only for the IO device whose IO contoller having full access to the PDEV submodules
        /// </summary>
        /// <returns></returns>
        public override bool IsARRecordSupported()
        {
            bool isArRecordSupported = base.IsARRecordSupported();
            return isArRecordSupported;
        }

        /// <summary>
        /// Checks if ArRecord is active
        /// </summary>
        /// <returns></returns>
        public override bool IsArRecordActive()
        {
            bool isArRecordActive = base.IsArRecordActive();
            AttributeAccessCode ac = new AttributeAccessCode();
            // Check only the interface shared assignment
            SharedIoAssignment assignment =
                (SharedIoAssignment)Interface.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.SharedIoAssignment, ac, 0);

            if (ac.IsOkay && (assignment == SharedIoAssignment.NotAssigned))
            {
                isArRecordActive = false;
            }

            return isArRecordActive;
        }

        public override void CreateRelatedEntries(PclObject module, int slotNumber, int subslotNumber, IPNFrameData inputPNFrameData,
                                                  IPNFrameData outputPNFrameData, IocrApiSubblockStruct inputIocrApi, IocrApiSubblockStruct outputIocrApi, bool isApduController)
        {
            SortOrder sortOrder = (isApduController ? SortOrder.APDU : SortOrder.IO);

            // dirty fix, sortOrder should aware of SharedDevice 
            List<DataAddress> addressObjects = ConfigUtility.GetAddressObjectsSortedSharedDevice(module, sortOrder);
            foreach (DataAddress addressObj in addressObjects)
            {
                IoTypes ioType = addressObj.IoType;
                int length = addressObj.Length;
                int iopsLength = Utility.GetIOXSLength(Interface, ioType, s_LengthIops);

                #region (pnPlannerFrameDirection == PNPlannerFrameDirection.InputFrame)

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis) ||
                    (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, (length + iopsLength), inputIocrApi, ioType, inputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, inputIocrApi, ioType, inputPNFrameData);
                }

                #endregion

                #region output

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis) ||
                    (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, outputIocrApi, ioType, outputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, length + iopsLength, outputIocrApi, ioType, outputPNFrameData);
                }
                #endregion
            }

            if (addressObjects.Count == 0)
            {
                IoTypes ioType = (IoTypes)module.AttributeAccess.
                    GetAnyAttribute<Int32>(InternalAttributeNames.IoType, new AttributeAccessCode(), 0);
                int iopsLength = Utility.GetIOXSLength(Interface, ioType, s_LengthIops);
                CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, inputIocrApi, IoTypes.Input, inputPNFrameData);
                CreateRelatedIOEntry(slotNumber, subslotNumber, iopsLength, outputIocrApi, IoTypes.Input, outputPNFrameData);
            }
        }

        public override void CreateRelatedEntriesNoApi(PclObject module, int slotNumber, int subslotNumber, IPNFrameData inputPNFrameData,
                                                       IPNFrameData outputPNFrameData, IocrRelatedSubblockStructV10 inputIocrApi, IocrRelatedSubblockStructV10 outputIocrApi, bool isApduController)
        {
            SortOrder sortOrder = (isApduController ? SortOrder.APDU : SortOrder.IO);

            List<DataAddress> addressObjects = ConfigUtility.GetAddressObjectsSortedSharedDevice(module, sortOrder);
            foreach (DataAddress addressObj in addressObjects)
            {
                IoTypes ioType = addressObj.IoType;
                int length = addressObj.Length;

                #region if (pnPlannerFrameDirection == PNPlannerFrameDirection.InputFrame)

                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis) ||
                    (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, (length + s_LengthIops), inputIocrApi, ioType, inputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, s_LengthIops, inputIocrApi, ioType, inputPNFrameData);
                }
                #endregion
                #region output
                if ((((int)ioType & (int)IoTypes.Diagnosis) == (int)IoTypes.Diagnosis) ||
                    (((int)ioType & (int)IoTypes.Input) == (int)IoTypes.Input))
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, s_LengthIops, outputIocrApi, ioType, outputPNFrameData);
                }
                else if (((int)ioType & (int)IoTypes.Output) == (int)IoTypes.Output)
                {
                    CreateRelatedIOEntry(slotNumber, subslotNumber, length + s_LengthIops, outputIocrApi, ioType, outputPNFrameData);
                }
                #endregion
            }

            if (addressObjects.Count == 0)
            {
                CreateRelatedIOEntry(slotNumber, subslotNumber, s_LengthIops, inputIocrApi, IoTypes.Input, outputPNFrameData);
                CreateRelatedIOEntry(slotNumber, subslotNumber, s_LengthIops, outputIocrApi, IoTypes.Input, outputPNFrameData);
            }
        }

        protected override IList<PclObject> GetSubmodulesInternal(PclObject module)
        {
            List<PclObject> submodules = new List<PclObject>();
            foreach (PclObject submodule in base.GetSubmodulesInternal(module))
            {
                SharedIoAssignment assignment = (SharedIoAssignment)submodule.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.SharedIoAssignment, new AttributeAccessCode(), 0);
                if (assignment == SharedIoAssignment.None)
                {
                    submodules.Add(submodule);
                }
            }
            return submodules;
        }

        #endregion
        private void MethodCheckConsistency()
        {
            DecentralDevice decentralDevice = Interface.ParentObject as DecentralDevice;
            // At least one sub module of an IO Device must be assigned to an IO-Controller
            SharedDeviceUtility.ConsistencyCheckAllModulesShared(decentralDevice);

            //// Priorized startup is only possible in context of the IO Controller
            //// which has full access of the PROFINET interface.
            SharedDeviceUtility.ConsistencyCheckFastStartupWithNotAssignedPDEVModules(decentralDevice);

            if (Interface == null) { return; }

            bool hasNotAssignedModule = SharedDeviceUtility.IsSharedIoDevice(Interface);

            // Indicates consistency error if IODevice: 
            // synchronised, IRT, uneven sendclock adjusted and not assigned module/submodule exist
            SharedDeviceUtility.ConsistencyCheckIRTUnevenSendClockWithNotAssignedModules(hasNotAssignedModule, Interface);
        }
    }
}