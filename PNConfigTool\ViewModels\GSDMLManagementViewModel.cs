using Microsoft.Win32;
using PNConfigTool.Common;
using PNConfigTool.Services;
using PNConfigTool.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using PNConfigLib.Gsd.Interpreter.Checker;
using PNConfigLib.GSDImport;
using PNConfigLib.BusinessLogic.DataModel;
using System.Diagnostics;
using PNConfigLib.DataModel;

namespace PNConfigTool.ViewModels
{
    public class GSDMLFile : ViewModelBase
    {
        private bool _isSelected;
        private string _status = "正常";

        public string FilePath { get; }
        public string FileName { get; }
        public string InstallDate { get; }

        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public GSDMLFile(string filePath)
        {
            FilePath = filePath;
            FileName = Path.GetFileName(filePath);
            
            // 获取文件创建日期作为安装日期
            FileInfo fileInfo = new FileInfo(filePath);
            InstallDate = fileInfo.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    public class GSDMLManagementViewModel : ViewModelBase
    {
        private readonly IGSDMLService _gsdmlService;
        private string _gsdmlImportPath = string.Empty;
        private bool _isImporting;
        private bool _isBusy;
        private string _statusMessage = string.Empty;
        private string _statusColor = "Black";

        public ObservableCollection<GSDMLFile> GSDMLFiles { get; } = new();

        public string GSDMLImportPath
        {
            get => _gsdmlImportPath;
            set => SetProperty(ref _gsdmlImportPath, value);
        }

        public bool IsImporting
        {
            get => _isImporting;
            private set => SetProperty(ref _isImporting, value);
        }

        public bool IsBusy
        {
            get => _isBusy;
            private set => SetProperty(ref _isBusy, value);
        }
        
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }
        
        public string StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }
        
        public bool HasStatusMessage => !string.IsNullOrEmpty(StatusMessage);

        public ICommand BrowseGSDMLCommand { get; }
        public ICommand RemoveSelectedGSDMLCommand { get; }
        public ICommand CloseWindowCommand { get; }
        public ICommand ViewCatalogCommand { get; }

        public GSDMLManagementViewModel(IGSDMLService gsdmlService)
        {
            _gsdmlService = gsdmlService ?? throw new ArgumentNullException(nameof(gsdmlService));
            
            BrowseGSDMLCommand = new RelayCommand(ExecuteBrowseGSDML, 
                param => !IsImporting && !IsBusy);
            RemoveSelectedGSDMLCommand = new RelayCommand(ExecuteRemoveSelectedGSDML, 
                param => GSDMLFiles.Any(f => f.IsSelected) && !IsImporting && !IsBusy);
            CloseWindowCommand = new RelayCommand(ExecuteCloseWindow);
            ViewCatalogCommand = new RelayCommand(ExecuteViewCatalog, 
                param => Catalog.DeviceList.Count > 0 && !IsImporting && !IsBusy);
            
            // 启动时加载GSDML文件
            _ = LoadGSDMLFiles();
        }
        
        private void ExecuteCloseWindow(object? parameter)
        {
            if (parameter is System.Windows.Window window)
            {
                window.DialogResult = true;
                window.Close();
            }
        }
        
        private void ExecuteViewCatalog(object? parameter)
        {
            try
            {
                var catalogWindow = new Views.Windows.GSDMLCatalogWindow(_gsdmlService);
                catalogWindow.Owner = Application.Current.MainWindow;
                catalogWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"打开目录查看器时出错: {ex.Message}");
                SetStatus($"打开目录查看器出错: {ex.Message}", "Red");
            }
        }
        
        private void SetStatus(string message, string color)
        {
            StatusMessage = message;
            StatusColor = color;
            
            // 3秒后自动清除状态消息
            Task.Delay(3000).ContinueWith(_ => 
            {
                App.Current.Dispatcher.Invoke(() => 
                {
                    StatusMessage = string.Empty;
                });
            });
        }

        private async Task LoadGSDMLFiles()
        {
            try
            {
                IsBusy = true;

                // 使用异步版本以获得更好的性能
                var files = await _gsdmlService.GetGSDMLFilesAsync();

                // 在主UI线程更新集合
                App.Current.Dispatcher.Invoke(() =>
                {
                    GSDMLFiles.Clear();
                    foreach (var file in files)
                    {
                        GSDMLFiles.Add(new GSDMLFile(file));
                    }
                });

                // 显示导入的GSDML文件数量和已解析的设备数量
                if (GSDMLFiles.Count > 0)
                {
                    SetStatus($"已加载 {GSDMLFiles.Count} 个GSDML文件，包含 {Catalog.DeviceList.Count} 个设备", "Green");
                }
                else
                {
                    SetStatus("没有找到GSDML文件", "Orange");
                }
                
                // 如果是通过项目打开的，通知项目管理器可以使用全局GSDML库
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null)
                {
                    if (GSDMLFiles.Count > 0)
                    {
                        // 如果有GSDML文件，将项目状态更新为GSDML_LOADED
                        projectManager.UpdateProjectStatus(StepStatus.GSDML_LOADED);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载GSDML文件时出错: {ex.Message}");
                SetStatus($"加载GSDML文件出错: {ex.Message}", "Red");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void ExecuteBrowseGSDML(object? parameter)
        {
            var dialog = new OpenFileDialog
            {
                Filter = "GSDML Files (*.xml)|*.xml",
                Title = "选择GSDML文件",
                Multiselect = true // 支持多文件选择
            };

            if (dialog.ShowDialog() == true)
            {
                IsImporting = true;
                GSDMLImportPath = string.Join("; ", dialog.FileNames);
                
                try
                {
                    // 创建GSDML检查器实例
                    var checker = new Checker();
                    
                    // 导入选中的文件
                    var results = await _gsdmlService.ImportGSDMLFiles(dialog.FileNames);
                    
                    // 统计成功和失败的数量
                    int successCount = results.Count(r => r.Value);
                    int failCount = results.Count(r => !r.Value);
                    
                    // 对每个文件进行GSDML格式验证
                    foreach (var filePath in dialog.FileNames)
                    {
                        string fileName = Path.GetFileName(filePath);
                        bool isValid = checker.CheckGsd(filePath);
                        
                        if (!isValid)
                        {
                            var result = MessageBox.Show($"验证失败。\n{filePath}\n信息：\n格式可能不符合GSDML规范，如果导入这个 GSDML 文件，一些数据在 PROFINET 向导中可能会不正确。要继续吗？", 
                                "验证失败", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                            
                            if (result == MessageBoxResult.No)
                            {
                                continue;
                            }
                        }
                    }
                    
                    // 更新状态消息
                    if (successCount > 0)
                    {
                        SetStatus($"成功导入 {successCount} 个GSDML文件，包含 {Catalog.DeviceList.Count} 个设备" + 
                            (failCount > 0 ? $"，{failCount} 个文件导入失败" : ""), 
                            failCount > 0 ? "Orange" : "Green");
                    }
                    else
                    {
                        SetStatus("没有文件被导入", "Red");
                    }
                    
                    // 如果有项目打开，更新项目状态
                    var projectManager = ProjectManager.Instance;
                    if (projectManager.CurrentProject != null && successCount > 0)
                    {
                        projectManager.UpdateProjectStatus(StepStatus.GSDML_LOADED);
                    }
                    
                    // 重新加载文件列表
                    await LoadGSDMLFiles();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"导入GSDML文件时出错: {ex.Message}");
                    SetStatus($"导入出错: {ex.Message}", "Red");
                }
                finally
                {
                    IsImporting = false;
                }
            }
        }

        private async void ExecuteRemoveSelectedGSDML(object? parameter)
        {
            var selectedFiles = GSDMLFiles.Where(f => f.IsSelected).ToList();
            if (selectedFiles.Count == 0)
                return;
                
            try
            {
                IsBusy = true;
                
                // 获取所有选中文件的路径
                var filePaths = selectedFiles.Select(f => f.FilePath).ToList();
                
                // 使用批量删除方法
                var results = await _gsdmlService.RemoveGSDMLFiles(filePaths);
                
                // 从列表中移除成功删除的文件
                int successCount = 0;
                foreach (var file in selectedFiles.ToList())
                {
                    if (results.TryGetValue(file.FilePath, out bool success) && success)
                    {
                        GSDMLFiles.Remove(file);
                        successCount++;
                    }
                }
                
                if (successCount > 0)
                {
                    SetStatus($"成功删除 {successCount} 个GSDML文件", "Green");
                }
                else
                {
                    SetStatus("没有文件被删除", "Red");
                }
                
                // 如果没有GSDML文件了，通知项目管理器（如果有项目打开）
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null && GSDMLFiles.Count == 0)
                {
                    projectManager.UpdateProjectStatus(StepStatus.NONE);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"删除GSDML文件时出错: {ex.Message}");
                SetStatus($"删除出错: {ex.Message}", "Red");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
} 