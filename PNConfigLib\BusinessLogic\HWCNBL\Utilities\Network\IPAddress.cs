/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPAddress.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using PNConfigLib.DataModel.AttributeUtilities;

#endregion

namespace PNConfigLib.HWCNBL.Utilities.Network
{
    /// <summary>
    /// Summary description for IPAddress.
    /// </summary>
    public class IPAddress : IPAddressBase
    {
        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)

        /// <summary>
        /// Constructor
        /// </summary>
        public IPAddress()
        {
            OwnAttributeName = InternalAttributeNames.NodeIPAddress;
        }
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as Int64</param>
        public IPAddress(long address) : base(address)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPAddress;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="address">IP address as string</param>
        /// <param name="node"></param>
        public IPAddress(string address, DataModel.PCLObjects.Node node) : base(address, node)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPAddress;
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="node">node to read the address from</param>
        public IPAddress(DataModel.PCLObjects.Node node)
        {
            OwnAttributeName = InternalAttributeNames.NodeIPAddress;
            ReadFromNode(node);
        }

        /// <summary>
        /// Return address as byte array.
        /// </summary>
        /// <returns>address as byte array</returns>
        public override void FromByteArray(byte[] ipAddress)
        {
            if (ipAddress == null)
            {
                throw new ArgumentNullException(nameof(ipAddress));
            }

            OwnAttributeName = InternalAttributeNames.NodeIPAddress;
            base.FromByteArray(ipAddress);
        }

        #endregion

        //########################################################################################
        /// <summary>
        /// Check if IP address is network address
        /// </summary>
        /// <param name="subnetMask">The subnet mask</param>
        /// <returns>True if IP address is identical with network address </returns>
        public bool IsNetworkAddress(Int64 subnetMask)
        {

            return (AsInt64 & subnetMask) == AsInt64;
        }
        #region Private Implementation

        // Contains the private implementation of the class

        #endregion
    }
}