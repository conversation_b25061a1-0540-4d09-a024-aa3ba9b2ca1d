﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SnmpEnableReadOnlyConsistencyChecker.cs   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Utilities;
#endregion

namespace PNConfigLib.HWCNBL.Networks.SNMP.Consistency
{
    /// <summary>
    /// Responsibe for displaying the SNMP Enable Readonly Consistency Check Errors
    /// </summary>
    internal class SnmpEnableReadOnlyConsistencyChecker
    {
        #region Public Methods

        /// <summary>
        /// Consistency checker method
        /// </summary>
        /// <param name="data"></param>
        public void CheckConsistency(PclObject pclObject)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool snmpEnableReadOnly = pclObject.AttributeAccess.GetAnyAttribute(
                InternalAttributeNames.SnmpEnableReadOnly,
                ac,
                false);

            bool snmpEnabled = pclObject.AttributeAccess.GetAnyAttribute(InternalAttributeNames.SnmpEnabled, ac, false);

            if (!snmpEnabled && snmpEnableReadOnly)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    pclObject,
                    ConsistencyConstants.XML_InvalidSNMPEnableReadOnly,
                    AttributeUtilities.GetName(pclObject));
            }
        }
    }

    #endregion
}