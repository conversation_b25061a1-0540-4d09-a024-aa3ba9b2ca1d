using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Xml;
using System.Xml.Linq;
using PNConfigTool.Models;
using PNConfigTool.Services;

namespace PNConfigTool.Views.Pages
{
    /// <summary>
    /// CompletionPage.xaml 的交互逻辑，用于显示配置完成信息
    /// </summary>
    public partial class CompletionPage : Page, INavigationAware
    {
        private ProjectManager? _projectManager;
        private string _deviceName = string.Empty;
        private string _deviceType = string.Empty;
        private ObservableCollection<AddressOverviewItem> _addressItems = new ObservableCollection<AddressOverviewItem>();

        public CompletionPage()
        {
            InitializeComponent();
            _projectManager = ProjectManager.Instance;
            AddressDataGrid.ItemsSource = _addressItems;
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadCompletionInfo();
        }

        /// <summary>
        /// 实现INavigationAware接口，接收导航参数
        /// </summary>
        public void OnNavigatedTo(object? parameter)
        {
            if (parameter is NavigationModuleParams moduleParams)
            {
                // 如果有特定设备参数，只显示该设备信息（保留向后兼容性）
                _deviceName = moduleParams.DeviceName;
                _deviceType = moduleParams.ModuleType;
                Debug.WriteLine($"完成页面接收到导航参数: 设备={_deviceName}, 类型={_deviceType}");

                LoadCompletionInfo();
            }
            else if (parameter is string deviceName)
            {
                // 如果有特定设备名，只显示该设备信息
                _deviceName = deviceName;
                Debug.WriteLine($"完成页面接收到设备名参数: {_deviceName}");

                LoadCompletionInfo();
            }
            else
            {
                // 没有参数时，显示所有设备的汇总信息（全局完成页面）
                _deviceName = string.Empty;
                _deviceType = string.Empty;
                Debug.WriteLine($"完成页面显示全局汇总信息");

                LoadCompletionInfo();
            }
        }

        /// <summary>
        /// 加载完成信息
        /// </summary>
        private void LoadCompletionInfo()
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("当前没有打开的项目");
                    return;
                }

                // 更新项目信息显示
                ProjectNameTextBlock.Text = $"项目: {_projectManager.CurrentProject.ProjectMetadata.ProjectName}";
                
                // 更新设备信息显示
                if (!string.IsNullOrEmpty(_deviceName))
                {
                    // 显示特定设备信息
                    DeviceNameTextBlock.Text = _deviceName;
                }
                else
                {
                    // 显示全局汇总信息
                    int deviceCount = _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices?.Count ?? 0;
                    DeviceNameTextBlock.Text = $"项目配置完成 ({deviceCount} 个设备)";
                    // DeviceTypeTextBlock.Text = "所有设备配置汇总";
                }

                // 加载地址总览数据
                LoadAddressOverview();

                // 更新项目状态为完成
                _projectManager.UpdateProjectStatus(StepStatus.COMPLETED);
                
                // 检查XML文件是否已生成，如果已生成则启用运行PNConfigRunner按钮
                CheckAndEnableRunButton();
                
                Debug.WriteLine("完成页面信息加载完毕");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载完成信息出错: {ex.Message}");
                MessageBox.Show($"加载完成信息时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载地址总览数据
        /// </summary>
        private void LoadAddressOverview()
        {
            try
            {
                _addressItems.Clear();

                if (_projectManager?.CurrentProject?.ConfigurationSettings.DecentralDevices == null)
                {
                    Debug.WriteLine("项目或设备列表为空");
                    return;
                }

                int rowIndex = 1;
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    // 如果指定了设备名，只显示该设备的信息；否则显示所有设备
                    if (!string.IsNullOrEmpty(_deviceName) && device.DeviceRefID != _deviceName)
                    {
                        continue;
                    }

                    Debug.WriteLine($"处理设备: {device.DeviceRefID}, 类型: {device.DeviceRefID}");

                    // 添加设备主模块信息（DAP - Device Access Point）
                    _addressItems.Add(new AddressOverviewItem
                    {
                        DeviceIndex = rowIndex++,
                        API = 0,
                        DeviceName = device.DeviceRefID,
                        ModuleName = "Standard_MRP",
                        SlotSubslot = "0_1",
                        IOType = "-",
                        StartAddress = "-",
                        EndAddress = "-",
                        Length = "-"
                    });

                    // 添加设备模块信息
                    if (device.Modules != null && device.Modules.Count > 0)
                    {
                        Debug.WriteLine($"设备 {device.DeviceRefID} 有 {device.Modules.Count} 个模块");

                        foreach (var module in device.Modules)
                        {
                            Debug.WriteLine($"处理模块: {module.ModuleRefID}, 位置: {module.SlotNumber}");

                            // 计算子槽位号
                            string inputSubslot = "32768";  // 0x8000 - 输入子槽位
                            string outputSubslot = "32770"; // 0x8002 - 输出子槽位

                            // 处理模块的子模块
                            foreach (var submodule in module.Submodules)
                            {
                                // 输入模块
                                if (submodule.AddressConfiguration.InputAddress > 0)
                                {
                                    // 默认长度为1字节，实际项目中可能需要从GSDML或其他配置中获取
                                    int defaultLength = GetModuleDataLength(module.ModuleRefID, true);
                                    string endAddress = CalculateEndAddress(submodule.AddressConfiguration.InputAddress.ToString(), defaultLength);

                                    _addressItems.Add(new AddressOverviewItem
                                    {
                                        DeviceIndex = rowIndex++,
                                        API = 0,
                                        DeviceName = device.DeviceRefID,
                                        ModuleName = module.ModuleRefID,
                                        SlotSubslot = $"{module.SlotNumber}_{inputSubslot}",
                                        IOType = "I",
                                        StartAddress = submodule.AddressConfiguration.InputAddress.ToString(),
                                        EndAddress = endAddress,
                                        Length = defaultLength.ToString()
                                    });
                                }

                                // 输出模块
                                if (submodule.AddressConfiguration.OutputAddress > 0)
                                {
                                    // 默认长度为1字节，实际项目中可能需要从GSDML或其他配置中获取
                                    int defaultLength = GetModuleDataLength(module.ModuleRefID, false);
                                    string endAddress = CalculateEndAddress(submodule.AddressConfiguration.OutputAddress.ToString(), defaultLength);

                                    _addressItems.Add(new AddressOverviewItem
                                    {
                                        DeviceIndex = rowIndex++,
                                        API = 0,
                                        DeviceName = device.DeviceRefID,
                                        ModuleName = module.ModuleRefID,
                                        SlotSubslot = $"{module.SlotNumber}_{outputSubslot}",
                                        IOType = "O",
                                        StartAddress = submodule.AddressConfiguration.OutputAddress.ToString(),
                                        EndAddress = endAddress,
                                        Length = defaultLength.ToString()
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"设备 {device.DeviceRefID} 没有配置模块");
                    }
                }

                Debug.WriteLine($"加载了 {_addressItems.Count} 条地址总览记录");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载地址总览数据出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取模块数据长度
        /// </summary>
        private int GetModuleDataLength(string moduleName, bool isInput)
        {
            try
            {
                // 根据模块名称推断数据长度
                // 这是一个简化的实现，实际项目中应该从GSDML文件或模块配置中获取
                if (string.IsNullOrEmpty(moduleName))
                    return 1;

                string lowerModuleName = moduleName.ToLower();

                // 根据模块名称中的关键字推断长度
                if (lowerModuleName.Contains("1 byte") || lowerModuleName.Contains("1byte"))
                    return 1;
                else if (lowerModuleName.Contains("2 byte") || lowerModuleName.Contains("2byte"))
                    return 2;
                else if (lowerModuleName.Contains("4 byte") || lowerModuleName.Contains("4byte"))
                    return 4;
                else if (lowerModuleName.Contains("8 byte") || lowerModuleName.Contains("8byte"))
                    return 8;
                else if (lowerModuleName.Contains("16 byte") || lowerModuleName.Contains("16byte"))
                    return 16;
                else if (lowerModuleName.Contains("32 byte") || lowerModuleName.Contains("32byte"))
                    return 32;
                else if (lowerModuleName.Contains("64 byte") || lowerModuleName.Contains("64byte"))
                    return 64;
                else if (lowerModuleName.Contains("128 byte") || lowerModuleName.Contains("128byte"))
                    return 128;
                else if (lowerModuleName.Contains("250 byte") || lowerModuleName.Contains("250byte"))
                    return 250;

                // 默认返回1字节
                return 1;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块数据长度出错: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 计算结束地址
        /// </summary>
        private string CalculateEndAddress(string startAddress, int length)
        {
            try
            {
                if (int.TryParse(startAddress, out int start) && length > 0)
                {
                    return (start + length - 1).ToString();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"计算结束地址出错: {ex.Message}");
            }
            return "-";
        }

        /// <summary>
        /// 上一步按钮点击事件
        /// </summary>
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService != null)
                {
                    if (!string.IsNullOrEmpty(_deviceName))
                    {
                        // 返回到特定设备的模块配置页面
                        string moduleConfigPageKey = $"DeviceDetail_{_deviceName}";
                        var moduleParams = new NavigationModuleParams(
                            _deviceName,
                            _deviceType,
                            "",
                            0
                        );
                        navigationService.Navigate(moduleConfigPageKey, moduleParams);
                    }
                    else
                    {
                        // 全局完成页面，返回到最后一个设备的模块配置页面
                        var lastDevice = GetLastDevice();
                        if (lastDevice != null)
                        {
                            string moduleConfigPageKey = $"DeviceDetail_{lastDevice.DeviceRefID}";
                            var moduleParams = new NavigationModuleParams(
                                lastDevice.DeviceRefID,
                                lastDevice.DeviceRefID,
                                "",
                                0
                            );
                            navigationService.Navigate(moduleConfigPageKey, moduleParams);
                        }
                        else
                        {
                            // 如果没有设备，返回控制器配置页面
                            navigationService.Navigate("ControllerConfigPage");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"上一步按钮点击处理出错: {ex.Message}");
                MessageBox.Show($"返回上一步时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取最后一个设备
        /// </summary>
        private DecentralDeviceConfig? GetLastDevice()
        {
            try
            {
                if (_projectManager?.CurrentProject?.ConfigurationSettings.DecentralDevices != null &&
                    _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count > 0)
                {
                    return _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.LastOrDefault();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取最后一个设备出错: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 生成按钮点击事件
        /// </summary>
        private void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    MessageBox.Show("当前没有打开的项目，无法生成配置文件。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 显示生成进度
                var progressWindow = new Window
                {
                    Title = "生成配置文件",
                    Width = 300,
                    Height = 150,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.NoResize
                };

                var progressText = new TextBlock
                {
                    Text = "正在生成XML配置文件...",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(20)
                };

                progressWindow.Content = progressText;
                progressWindow.Show();

                try
                {
                    // 使用XML生成服务
                    var xmlGenerationService = new XmlGenerationService();
                    var result = xmlGenerationService.GenerateXmlFiles(_projectManager.CurrentProject);

                    progressWindow.Close();

                    if (result.Success)
                    {
                        // 保存项目（更新XML文件路径）
                        _projectManager.SaveProject();

                        string message = $"XML配置文件生成成功！\n\n" +
                                       $"ListOfNodes.xml: {result.ListOfNodesPath}\n" +
                                       $"Configuration.xml: {result.ConfigurationPath}\n\n" +
                                       $"文件已保存到项目输出目录。";

                        MessageBox.Show(message, "生成成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 启用运行PNConfigRunner按钮
                        RunPNConfigRunner.IsEnabled = true;

                        Debug.WriteLine($"XML文件生成成功: ListOfNodes={result.ListOfNodesPath}, Configuration={result.ConfigurationPath}");
                    }
                    else
                    {
                        MessageBox.Show($"生成配置文件失败：\n{result.Message}", "生成失败", MessageBoxButton.OK, MessageBoxImage.Error);
                        Debug.WriteLine($"XML文件生成失败: {result.Message}");
                    }
                }
                catch (Exception)
                {
                    progressWindow.Close();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"生成按钮点击处理出错: {ex.Message}");
                MessageBox.Show($"生成配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存按钮点击事件
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_projectManager != null)
                {
                    bool saved = _projectManager.SaveProject();
                    if (saved)
                    {
                        MessageBox.Show("项目保存成功。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("项目保存失败。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存按钮点击处理出错: {ex.Message}");
                MessageBox.Show($"保存项目时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 运行PNConfigRunner按钮点击事件
        /// </summary>
        private void RunGenerateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    MessageBox.Show("当前没有打开的项目，无法运行PNConfigRunner。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 检查XML文件是否已生成（从相对路径转换为绝对路径）
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");
                string configXmlRelativePath = _projectManager.CurrentProject.OutputConfiguration.ConfigurationXmlPath;
                string listOfNodesXmlRelativePath = _projectManager.CurrentProject.OutputConfiguration.ListOfNodesXmlPath;

                if (string.IsNullOrEmpty(configXmlRelativePath) || string.IsNullOrEmpty(listOfNodesXmlRelativePath))
                {
                    MessageBox.Show("请先生成XML配置文件，然后再运行PNConfigRunner。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 转换为绝对路径
                string configXmlPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(configXmlRelativePath, projectDirectory);
                string listOfNodesXmlPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(listOfNodesXmlRelativePath, projectDirectory);

                if (!File.Exists(configXmlPath) || !File.Exists(listOfNodesXmlPath))
                {
                    MessageBox.Show("XML配置文件不存在，请先生成XML配置文件。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 设置默认输出目录
                string outputDirectory = _projectManager.CurrentProject.OutputConfiguration.OutputDirectory;

                // 预处理阶段：删除已存在的ConsistencyLog.xml文件
                string consistencyLogPath = Path.Combine(outputDirectory, "ConsistencyLog.xml");
                try
                {
                    if (File.Exists(consistencyLogPath))
                    {
                        File.Delete(consistencyLogPath);
                        Debug.WriteLine($"已删除旧的ConsistencyLog.xml文件: {consistencyLogPath}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"删除ConsistencyLog.xml文件失败: {ex.Message}");
                    // 不阻止程序继续执行，只记录警告
                }

                // 显示运行进度
                var progressWindow = new Window
                {
                    Title = "运行PNConfigRunner",
                    Width = 400,
                    Height = 200,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    ResizeMode = ResizeMode.NoResize
                };

                var progressPanel = new StackPanel
                {
                    Margin = new Thickness(20)
                };

                var progressText = new TextBlock
                {
                    Text = "正在运行PNConfigRunner...",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20)
                };

                var progressBar = new ProgressBar
                {
                    IsIndeterminate = true,
                    Height = 20,
                    Width = 350,
                    Margin = new Thickness(0, 0, 0, 20)
                };

                var statusText = new TextBlock
                {
                    Text = "正在初始化...",
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                progressPanel.Children.Add(progressText);
                progressPanel.Children.Add(progressBar);
                progressPanel.Children.Add(statusText);
                progressWindow.Content = progressPanel;
                
                // 显示进度窗口
                progressWindow.Show();

                try
                {
                    // 构建PNConfigLibRunner.exe路径
                    string exePath = Path.Combine(
                        AppDomain.CurrentDomain.BaseDirectory,
                        "PNConfigLibRunner",
                        "PNConfigLibRunner.exe");

                    if (!File.Exists(exePath))
                    {
                        progressWindow.Close();
                        System.Windows.MessageBox.Show($"找不到PNConfigLibRunner.exe: {exePath}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        return;
                    }

                    // 构建命令行参数
                    string arguments = $"-c \"{configXmlPath}\" -l \"{listOfNodesXmlPath}\" -o \"{outputDirectory}\"";

                    statusText.Text = "正在启动PNConfigLibRunner...";

                    // 设置工作目录为项目目录，这样相对路径就能正确解析
                    string workingDirectory = projectDirectory;

                    // 调试输出
                    Debug.WriteLine($"PNConfigRunner 参数:");
                    Debug.WriteLine($"  工作目录: {workingDirectory}");
                    Debug.WriteLine($"  Configuration.xml: {configXmlPath}");
                    Debug.WriteLine($"  ListOfNodes.xml: {listOfNodesXmlPath}");
                    Debug.WriteLine($"  输出目录: {outputDirectory}");

                    // 创建进程
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = exePath,
                            Arguments = arguments,
                            WorkingDirectory = workingDirectory,
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            CreateNoWindow = true
                        },
                        EnableRaisingEvents = true
                    };

                    // 收集输出
                    var output = new System.Text.StringBuilder();
                    var error = new System.Text.StringBuilder();

                    process.OutputDataReceived += (sender, args) =>
                    {
                        if (!string.IsNullOrEmpty(args.Data))
                        {
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                output.AppendLine(args.Data);
                                statusText.Text = args.Data;
                            });
                        }
                    };

                    process.ErrorDataReceived += (sender, args) =>
                    {
                        if (!string.IsNullOrEmpty(args.Data))
                        {
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                error.AppendLine(args.Data);
                                statusText.Text = "错误: " + args.Data;
                            });
                        }
                    };

                    // 进程结束事件
                    process.Exited += (sender, args) =>
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            progressWindow.Close();

                            if (process.ExitCode == 0)
                            {
                                // PNConfigRunner执行成功，检查是否需要进行MRP后处理
                                bool mrpPostProcessed = false;
                                try
                                {
                                    mrpPostProcessed = PerformMrpPostProcessing(outputDirectory);
                                }
                                catch (Exception mrpEx)
                                {
                                    Debug.WriteLine($"MRP后处理失败: {mrpEx.Message}");
                                    System.Windows.MessageBox.Show($"MRP后处理失败: {mrpEx.Message}", "警告", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                                }

                                // 后处理阶段：检查并解析ConsistencyLog.xml文件
                                bool hasErrors = false;
                                try
                                {
                                    hasErrors = CheckAndDisplayConsistencyErrors(outputDirectory);
                                }
                                catch (Exception logEx)
                                {
                                    Debug.WriteLine($"解析ConsistencyLog.xml失败: {logEx.Message}");
                                    // 解析失败时不阻止成功消息的显示
                                    hasErrors = false;
                                }

                                // 只有在没有发现错误时才显示成功消息
                                if (!hasErrors)
                                {
                                    string successMessage = $"PNConfigRunner执行成功！\n\n输出目录: {outputDirectory}";
                                    if (mrpPostProcessed)
                                    {
                                        successMessage += "\n\n已完成MRP配置后处理。";
                                    }
                                    System.Windows.MessageBox.Show(successMessage, "执行成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                                }
                                else
                                {
                                    Debug.WriteLine("由于发现配置错误，已抑制成功消息的显示");
                                }
                            }
                            else
                            {
                                string errorMessage = $"PNConfigRunner执行失败，退出代码: {process.ExitCode}\n\n错误信息:\n{error}";
                                System.Windows.MessageBox.Show(errorMessage, "执行失败", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                            }
                        });
                    };

                    // 启动进程
                    process.Start();
                    process.BeginOutputReadLine();
                    process.BeginErrorReadLine();
                }
                catch (Exception ex)
                {
                    progressWindow.Close();
                    throw new Exception($"启动PNConfigLibRunner失败: {ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"运行PNConfigRunner按钮点击处理出错: {ex.Message}");
                System.Windows.MessageBox.Show($"运行PNConfigRunner时出错: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 检查XML文件是否已生成，如果已生成则启用运行PNConfigRunner按钮
        /// </summary>
        private void CheckAndEnableRunButton()
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("当前没有打开的项目");
                    return;
                }

                // 检查XML文件是否已生成（从相对路径转换为绝对路径）
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");
                string configXmlRelativePath = _projectManager.CurrentProject.OutputConfiguration.ConfigurationXmlPath;
                string listOfNodesXmlRelativePath = _projectManager.CurrentProject.OutputConfiguration.ListOfNodesXmlPath;

                if (string.IsNullOrEmpty(configXmlRelativePath) || string.IsNullOrEmpty(listOfNodesXmlRelativePath))
                {
                    Debug.WriteLine("XML文件路径为空");
                    return;
                }

                // 转换为绝对路径
                string configXmlPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(configXmlRelativePath, projectDirectory);
                string listOfNodesXmlPath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(listOfNodesXmlRelativePath, projectDirectory);

                if (File.Exists(configXmlPath) && File.Exists(listOfNodesXmlPath))
                {
                    Debug.WriteLine("XML文件已生成，启用运行PNConfigRunner按钮");
                    RunPNConfigRunner.IsEnabled = true;
                }
                else
                {
                    Debug.WriteLine("XML文件未生成，禁用运行PNConfigRunner按钮");
                    RunPNConfigRunner.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查XML文件并启用运行PNConfigRunner按钮出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行MRP配置后处理
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>是否执行了后处理</returns>
        private bool PerformMrpPostProcessing(string outputDirectory)
        {
            try
            {
                Debug.WriteLine("=== 开始MRP后处理检查 ===");

                // 检查项目配置中的MasterMrpRole设置
                if (_projectManager?.CurrentProject?.ProjectSpecificExtensions == null)
                {
                    Debug.WriteLine("项目配置或ProjectSpecificExtensions为空，跳过MRP后处理");
                    return false;
                }

                string masterMrpRole = _projectManager.CurrentProject.ProjectSpecificExtensions.MasterMrpRole;
                Debug.WriteLine($"当前MasterMrpRole设置: {masterMrpRole}");

                // 只有当MasterMrpRole为"MrpManager"时才进行后处理
                if (masterMrpRole != "MrpManager")
                {
                    Debug.WriteLine("MasterMrpRole不是'MrpManager'，跳过MRP后处理");
                    return false;
                }

                Debug.WriteLine("检测到MasterMrpRole为'MrpManager'，开始执行MRP后处理");

                // 构建目标XML文件路径
                string targetXmlPath = Path.Combine(outputDirectory, "PROFINET Driver_PN_Driver_1.xml");

                if (!File.Exists(targetXmlPath))
                {
                    Debug.WriteLine($"目标XML文件不存在: {targetXmlPath}");
                    return false;
                }

                Debug.WriteLine($"找到目标XML文件: {targetXmlPath}");

                // 执行XML修改
                return ModifyMrpConfigurationInXml(targetXmlPath);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"MRP后处理过程中发生异常: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 修改XML文件中的MRP配置
        /// </summary>
        /// <param name="xmlFilePath">XML文件路径</param>
        /// <returns>是否成功修改</returns>
        private bool ModifyMrpConfigurationInXml(string xmlFilePath)
        {
            try
            {
                Debug.WriteLine($"开始修改XML文件: {xmlFilePath}");

                // 加载XML文档
                XDocument doc = XDocument.Load(xmlFilePath);
                bool modified = false;

                // 1. 修改MRP角色配置 (Key="32850")
                modified |= ModifyMrpRoleConfiguration(doc);

                // 2. 修改Port 1的MRP配置 (Key="32851")
                modified |= ModifyPortMrpConfiguration(doc, "Port 1");

                // 3. 修改Port 2的MRP配置 (Key="32851")
                modified |= ModifyPortMrpConfiguration(doc, "Port 2");

                if (modified)
                {
                    // 保存修改后的XML文件
                    doc.Save(xmlFilePath);
                    Debug.WriteLine("XML文件MRP配置修改完成并已保存");
                    return true;
                }
                else
                {
                    Debug.WriteLine("未找到需要修改的MRP配置节点");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"修改XML文件中的MRP配置时发生异常: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 修改MRP角色配置
        /// </summary>
        /// <param name="doc">XML文档</param>
        /// <returns>是否成功修改</returns>
        private bool ModifyMrpRoleConfiguration(XDocument doc)
        {
            try
            {
                Debug.WriteLine("开始修改MRP角色配置 (Key=32850)");

                // 查找路径: HWConfiguration -> profinet driver -> PN_Driver_1_Interface -> DataRecordsConf -> Field Key="32850"
                var hwConfigElement = doc.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == "HWConfiguration");

                if (hwConfigElement == null)
                {
                    Debug.WriteLine("未找到HWConfiguration节点");
                    return false;
                }

                var profinetDriverElement = hwConfigElement.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == "profinet driver" || 
                                         e.Attribute("Name")?.Value == "profinetdriver");

                if (profinetDriverElement == null)
                {
                    Debug.WriteLine("未找到profinet driver节点");
                    return false;
                }

                var interfaceElement = profinetDriverElement.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == "PN_Driver_1_Interface");

                if (interfaceElement == null)
                {
                    Debug.WriteLine("未找到PN_Driver_1_Interface节点");
                    return false;
                }

                var dataRecordsConfVariable = interfaceElement.Descendants("Variable")
                    .FirstOrDefault(v => v.Attribute("Name")?.Value == "DataRecordsConf");

                if (dataRecordsConfVariable == null)
                {
                    Debug.WriteLine("未找到DataRecordsConf变量");
                    return false;
                }

                var sparseArrayValue = dataRecordsConfVariable.Descendants("Value")
                    .FirstOrDefault(v => v.Attribute("Datatype")?.Value == "SparseArray" &&
                                        v.Attribute("Valuetype")?.Value == "BLOB");

                if (sparseArrayValue == null)
                {
                    Debug.WriteLine("未找到SparseArray BLOB值");
                    return false;
                }

                // 查找Key="32850"的Field
                var field32850 = sparseArrayValue.Descendants("Field")
                    .FirstOrDefault(f => f.Attribute("Key")?.Value == "32850");

                if (field32850 != null)
                {
                    // 替换整个Field元素
                    var newField = new XElement("Field",
                        new XAttribute("Key", "32850"),
                        new XAttribute("Length", "68"),
                        "02110040010100010231003801000000C3D687FE789E03A1ACDBE5BFCBBC27B6000200000B6D7270646F6D61696E2D31021600100100800000010003000A001400030000");

                    field32850.ReplaceWith(newField);
                    Debug.WriteLine("成功替换Key=32850的MRP角色配置");
                    return true;
                }
                else
                {
                    Debug.WriteLine("未找到Key=32850的Field");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"修改MRP角色配置时发生异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 修改端口MRP配置
        /// </summary>
        /// <param name="doc">XML文档</param>
        /// <param name="portName">端口名称 (如 "Port 1" 或 "Port 2")</param>
        /// <returns>是否成功修改</returns>
        private bool ModifyPortMrpConfiguration(XDocument doc, string portName)
        {
            try
            {
                Debug.WriteLine($"开始修改{portName}的MRP配置 (Key=32851)");

                // 查找路径: HWConfiguration -> profinet driver -> Port X -> DataRecordsConf -> Field Key="32851"
                var hwConfigElement = doc.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == "HWConfiguration");

                if (hwConfigElement == null)
                {
                    Debug.WriteLine("未找到HWConfiguration节点");
                    return false;
                }

                var profinetDriverElement = hwConfigElement.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == "profinet driver" || 
                                         e.Attribute("Name")?.Value == "profinetdriver");

                if (profinetDriverElement == null)
                {
                    Debug.WriteLine("未找到profinet driver节点");
                    return false;
                }

                var portElement = profinetDriverElement.Descendants("Object")
                    .FirstOrDefault(e => e.Attribute("Name")?.Value == portName);

                if (portElement == null)
                {
                    Debug.WriteLine($"未找到{portName}节点");
                    return false;
                }

                var dataRecordsConfVariable = portElement.Descendants("Variable")
                    .FirstOrDefault(v => v.Attribute("Name")?.Value == "DataRecordsConf");

                if (dataRecordsConfVariable == null)
                {
                    Debug.WriteLine($"未找到{portName}的DataRecordsConf变量");
                    return false;
                }

                // 查找现有的Value元素
                var existingValue = dataRecordsConfVariable.Descendants("Value")
                    .FirstOrDefault(v => v.Attribute("Datatype")?.Value == "SparseArray" &&
                                        v.Attribute("Valuetype")?.Value == "BLOB");

                if (existingValue != null)
                {
                    // 如果存在Value元素，检查是否为自闭合标签或空内容
                    if (!existingValue.HasElements && string.IsNullOrWhiteSpace(existingValue.Value))
                    {
                        // 替换为包含Field的完整结构
                        var newValue = new XElement("Value",
                            new XAttribute("Datatype", "SparseArray"),
                            new XAttribute("Valuetype", "BLOB"),
                            new XElement("Field",
                                new XAttribute("Key", "32851"),
                                new XAttribute("Length", "24"),
                                "0214001401010000C3D687FE789E03A1ACDBE5BFCBBC27B6"));

                        existingValue.ReplaceWith(newValue);
                        Debug.WriteLine($"成功替换{portName}的MRP配置 (空Value -> 包含Field的Value)");
                        return true;
                    }
                    else
                    {
                        // 检查是否已存在Key="32851"的Field
                        var field32851 = existingValue.Descendants("Field")
                            .FirstOrDefault(f => f.Attribute("Key")?.Value == "32851");

                        if (field32851 != null)
                        {
                            // 更新现有Field
                            field32851.SetAttributeValue("Length", "24");
                            field32851.Value = "0214001401010000C3D687FE789E03A1ACDBE5BFCBBC27B6";
                            Debug.WriteLine($"成功更新{portName}的现有MRP配置Field");
                            return true;
                        }
                        else
                        {
                            // 添加新的Field
                            var newField = new XElement("Field",
                                new XAttribute("Key", "32851"),
                                new XAttribute("Length", "24"),
                                "0214001401010000C3D687FE789E03A1ACDBE5BFCBBC27B6");

                            existingValue.Add(newField);
                            Debug.WriteLine($"成功添加{portName}的新MRP配置Field");
                            return true;
                        }
                    }
                }
                else
                {
                    Debug.WriteLine($"未找到{portName}的Value元素");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"修改{portName}MRP配置时发生异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查并显示ConsistencyLog.xml中的错误信息
        /// </summary>
        /// <param name="outputDirectory">输出目录</param>
        /// <returns>如果发现错误返回true，否则返回false</returns>
        private bool CheckAndDisplayConsistencyErrors(string outputDirectory)
        {
            try
            {
                string consistencyLogPath = Path.Combine(outputDirectory, "ConsistencyLog.xml");

                if (!File.Exists(consistencyLogPath))
                {
                    Debug.WriteLine($"ConsistencyLog.xml文件不存在: {consistencyLogPath}");
                    return false; // 没有文件，视为没有错误
                }

                Debug.WriteLine($"开始解析ConsistencyLog.xml文件: {consistencyLogPath}");

                // 解析XML文件
                XDocument doc = XDocument.Load(consistencyLogPath);

                // 查找所有Severity="Error"和"Warning"的ConsistencyLog条目
                var errorLogs = doc.Descendants("ConsistencyLog")
                    .Where(log => log.Attribute("Severity")?.Value == "Error")
                    .ToList();

                var warningLogs = doc.Descendants("ConsistencyLog")
                    .Where(log => log.Attribute("Severity")?.Value == "Warning")
                    .ToList();

                if (errorLogs.Count > 0 || warningLogs.Count > 0)
                {
                    var messages = new List<string>();

                    // 收集错误信息
                    if (errorLogs.Count > 0)
                    {
                        messages.Add($"=== 错误信息 ({errorLogs.Count} 个) ===");
                        foreach (var errorLog in errorLogs)
                        {
                            string message = errorLog.Attribute("Message")?.Value ?? "未知错误";
                            string type = errorLog.Attribute("Type")?.Value ?? "";

                            if (!string.IsNullOrEmpty(type))
                            {
                                messages.Add($"[{type}] {message}");
                            }
                            else
                            {
                                messages.Add(message);
                            }
                        }
                    }

                    // 收集警告信息
                    if (warningLogs.Count > 0)
                    {
                        if (messages.Count > 0)
                        {
                            messages.Add(""); // 添加空行分隔
                        }
                        messages.Add($"=== 警告信息 ({warningLogs.Count} 个) ===");
                        foreach (var warningLog in warningLogs)
                        {
                            string message = warningLog.Attribute("Message")?.Value ?? "未知警告";
                            string type = warningLog.Attribute("Type")?.Value ?? "";

                            if (!string.IsNullOrEmpty(type))
                            {
                                messages.Add($"[{type}] {message}");
                            }
                            else
                            {
                                messages.Add(message);
                            }
                        }
                    }

                    // 在MessageBox中显示所有信息
                    string allMessages = string.Join("\n", messages);
                    string title = $"配置检查结果";
                    if (errorLogs.Count > 0 && warningLogs.Count > 0)
                    {
                        title = $"发现 {errorLogs.Count} 个错误和 {warningLogs.Count} 个警告";
                    }
                    else if (errorLogs.Count > 0)
                    {
                        title = $"发现 {errorLogs.Count} 个错误";
                    }
                    else if (warningLogs.Count > 0)
                    {
                        title = $"发现 {warningLogs.Count} 个警告";
                    }

                    var messageBoxImage = errorLogs.Count > 0 ?
                        System.Windows.MessageBoxImage.Warning :
                        System.Windows.MessageBoxImage.Information;

                    System.Windows.MessageBox.Show(
                        $"PNConfigRunner执行过程中发现以下问题：\n\n{allMessages}",
                        title,
                        System.Windows.MessageBoxButton.OK,
                        messageBoxImage);

                    Debug.WriteLine($"显示了{errorLogs.Count}个错误和{warningLogs.Count}个警告信息");

                    // 返回是否发现了错误（不包括警告）
                    return errorLogs.Count > 0;
                }
                else
                {
                    Debug.WriteLine("ConsistencyLog.xml中没有发现错误或警告信息");
                    return false; // 没有错误
                }
            }
            catch (XmlException xmlEx)
            {
                Debug.WriteLine($"解析ConsistencyLog.xml时发生XML格式错误: {xmlEx.Message}");
                System.Windows.MessageBox.Show(
                    $"解析ConsistencyLog.xml文件时发生格式错误：\n{xmlEx.Message}",
                    "XML解析错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
                return false; // XML解析错误，视为没有发现配置错误
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查ConsistencyLog.xml时发生异常: {ex.Message}");
                throw;
            }
        }
    }

    /// <summary>
    /// 地址总览项目数据模型
    /// </summary>
    public class AddressOverviewItem
    {
        public int DeviceIndex { get; set; }
        public int API { get; set; }
        public string DeviceName { get; set; } = string.Empty;
        public string ModuleName { get; set; } = string.Empty;
        public string SlotSubslot { get; set; } = string.Empty;
        public string IOType { get; set; } = string.Empty;
        public string StartAddress { get; set; } = string.Empty;
        public string EndAddress { get; set; } = string.Empty;
        public string Length { get; set; } = string.Empty;
    }
}
