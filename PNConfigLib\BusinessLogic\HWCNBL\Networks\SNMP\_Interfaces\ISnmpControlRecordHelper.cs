﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ISnmpControlRecordHelper.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Networks.SNMP._Interfaces
{
    /// <summary>
    /// Interface of helper class for generating SNMP control records
    /// </summary>
    internal interface ISnmpControlRecordHelper
    {
        /// <summary>
        /// Calculates the number of bytes needed to pad a byte array of length [dataLength]
        /// to align with blocks of [byteAlignment] bytes
        /// </summary>
        /// <param name="dataLength">Length of the array that needs to be padded</param>
        /// <param name="byteAlignment">Number of bytes in a block to align to</param>
        /// <returns>The number of bytes required to pad the record</returns>
        int CalculatePadding(int dataLength, int byteAlignment);

        /// <summary>
        /// Creates a byte array from the communityName input string using ASCII encoding.
        /// </summary>
        /// <param name="communityName">The string to be converted into byte array</param>
        /// <returns>The converted byte array</returns>
        byte[] CreateArrayFromCommunityName(string communityName);

        /// <summary>
        /// Creates a 2 byte long array from readWriteAccess and snmpEnabledValue
        /// </summary>
        /// <param name="snmpEnabledValue"></param>
        /// <param name="readWriteAccess">the boolean value to be converted</param>
        /// <returns>The converted byte array</returns>
        byte[] CreateArrayFromControl(bool snmpEnabledValue, bool readWriteAccess);

        /// <summary>
        /// Creates a 2 byte long array for CIMSNMPAdjust from readWriteAccess and snmpEnabledValue
        /// </summary>
        /// <param name="snmpEnabledValue"></param>
        /// <param name="readWriteAccess">the boolean value to be converted</param>
        /// <returns>The converted byte array</returns>
        byte[] CreateArrayFromControlForCim(bool snmpEnabledValue, bool readWriteAccess);
    }
}