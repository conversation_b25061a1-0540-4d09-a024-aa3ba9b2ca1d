/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerInputWriter.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;
using PNConfigLib.PNPlannerAdapter.DataClasses;

using Port = PNConfigLib.DataModel.PCLObjects.Port;

#endregion

namespace PNConfigLib.PNPlannerAdapter
{
    internal class PNPlannerInputWriter
    {
        /// <summary>
        /// The list of Links.
        /// </summary>
        private readonly List<Link> m_Links;

        /// <summary>
        /// The list of SwitchTypes.
        /// </summary>
        private readonly List<SwitchType> m_SwitchTypes;

        /// <summary>
        /// The business logic class for sync domain.
        /// </summary>
        private readonly SyncDomainBusinessLogic m_SyncDomain;


        /// <summary>
        /// Constructor for PNPlannerInputWriter.
        /// </summary>
        /// <param name="syncDomain">The sync domain for which the PNPlanner input will be created.</param>
        public PNPlannerInputWriter(SyncDomainBusinessLogic syncDomain)
        {
            m_SyncDomain = syncDomain;
            m_SwitchTypes = new List<SwitchType>();
            m_Links = new List<Link>();
        }

        /// <summary>
        /// Getter for the sync domain object.
        /// </summary>
        private SyncDomainBusinessLogic SyncDomain
        {
            get { return m_SyncDomain; }
        }

        /// <summary>
        /// The main method that creates the PNPlanner input xml and writes it to a file.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="interfacesOfSwitchNames"></param>
        /// <param name="inputXml"></param>
        internal PNPlannerResult CreatePNPlannerInput(
            InterfaceFrameDataList topIsland,
            Dictionary<string, Interface> interfacesOfSwitchNames, out byte[] inputXml)
        {
            if ((topIsland == null)
                || (topIsland.AllPNFrames.Count <= 0))
            {
                inputXml = null;
                return null;
            }
            PNPlannerResult result = Write(topIsland, interfacesOfSwitchNames, out inputXml);
            if (result.ResultType != PNPlannerResultType.Successful)
            {
                inputXml = null;
            }
            return result;
        }

        /// <summary>
        /// Checks if a given frame list has any shared devices which is also a part of a DFP packgroup.
        /// </summary>
        /// <param name="frameDataList">The list of frames.</param>
        /// <returns>True if frameDataList has shared devices which is also a part of a DFP packgroup; false otherwise.</returns>
        private static bool CheckForSharedDfpFrame(List<IPNFrameData> frameDataList)
        {
            if ((frameDataList == null)
                || !frameDataList.Exists(p => p is PNDfpFrameData))
            {
                return false;
            }
            foreach (IPNFrameData frameData in frameDataList)
            {
                if (!(frameData is PNDfpFrameData))
                {
                    continue;
                }
                PNDfpFrameData dfpFrame = frameData as PNDfpFrameData;
                SortedList<int, IPNSubframeData> subframes = dfpFrame.Subframes;
                foreach (KeyValuePair<int, IPNSubframeData> subframeEntry in subframes)
                {
                    PNSubframeData subframe = subframeEntry.Value as PNSubframeData;
                    if ((subframe != null)
                        && (subframe.NumberOfARs > 0)
                        && dfpFrame.HasNotAssignedSubmodule)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Checks some sync domain properties before starting to write the PNPlanner input.
        /// </summary>
        /// <returns>The result of the check. Successful if no errors occur, else detailed result is returned.</returns>
        private PNPlannerResult CheckPreconditions(InterfaceFrameDataList topIsland)
        {
            PNPlannerResult result = new PNPlannerResult(PNPlannerResultType.Successful, "");
            // The sync master should be assigned.
            if (m_SyncDomain.HasDevicesUsingIrtTop && (m_SyncDomain.SyncMaster == null))
            {
                // No Sync-Master is found, create an error message.
                result.ResultType = PNPlannerResultType.AlgorithmError;
                result.MsgId = ConsistencyConstants.SyncDomainNoSyncMaster;
                result.MessageParameters = new string[] { SyncDomain.DomainName };
            }
            else if (DomainManagementUtility.GetNumberOfSyncMastersInSyncDomain(m_SyncDomain.PCLObject) > 1)
            {
                // More than one sync-masters found, create an error message.
                result.ResultType = PNPlannerResultType.AlgorithmError;
                result.MsgId = ConsistencyConstants.SyncDomainSyncMasterCount;
                result.MessageParameters = new string[] { SyncDomain.DomainName };
            }
            else if (DomainManagementUtility.GetNumberOfSecondarySyncMastersInSyncDomain(m_SyncDomain.PCLObject) > 1)
            {
                // More than one secondary sync-masters found, create an error message
                result.ResultType = PNPlannerResultType.AlgorithmError;
                result.MsgId = ConsistencyConstants.SyncDomainSyncMasterCount;
                result.MessageParameters = new string[] { SyncDomain.DomainName };
            }
            else if (m_SyncDomain.HasDevicesUsingIrtTop && !topIsland.AllPNFrames.ContainsKey(m_SyncDomain.SyncMaster))
            {
                // Create an error message.
                result.ResultType = PNPlannerResultType.AlgorithmError;
                result.MsgId = ConsistencyConstants.SyncDomainSyncMasterIsNotComprised;
                result.MessageParameters = new string[] {
                    AttributeUtilities.GetSubmoduleNameWithContainer(m_SyncDomain.SyncMaster) };
            }
            return result;
        }

        /// <summary>
        /// Creates the SwitchType of a given Interface Submodule if the SwitchType has not been created before.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule for which the switchtype will be created.</param>
        private void CreateSwitchType(Interface interfaceSubmodule)
        {
            // Create a SwitchType for the submodule.
            SwitchType switchType = new SwitchType(interfaceSubmodule, SyncDomain);

            // Go through all SwitchTypes and check whether the type has already been created.
            foreach (SwitchType addedType in m_SwitchTypes)
            {
                if (!switchType.Equals(addedType))
                {
                    continue;
                }
                // Add the Interface Submodule to the list in the SwitchType, which contains 
                // the Interface Submodules of this SwitchType.
                addedType.AddSwitch(interfaceSubmodule);

                // Exit the method because the type has already been created.
                return;
            }

            // Add the Interface Submodule to the list in the SwitchType, which contains 
            // the Interface Submodules of this SwitchType.
            switchType.AddSwitch(interfaceSubmodule);

            switchType.IsIrt = IsIrtSupported(interfaceSubmodule);

            // The SwitchType is not a previously defined type. Add it to the global list.
            m_SwitchTypes.Add(switchType);
        }

        /// <summary>
        /// Finds the SwitchType of the interface submodule in the global SwitchType list.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose SwitchType will be retrieved.</param>
        /// <returns>The SwitchType of the interface submodule.</returns>
        private SwitchType GetSwitchType(Interface interfaceSubmodule)
        {
            // Go through all SwitchTypes and check whether the type contains the Submodule.
            foreach (SwitchType switchType in m_SwitchTypes)
            {
                if (switchType.SwitchesOfType.Contains(interfaceSubmodule))
                {
                    return switchType;
                }
            }

            //This code segment handles devices which are not connected in topology
            //This method should be improved to create all switch types at one step
            CreateSwitchType(interfaceSubmodule);
            foreach (SwitchType switchType in m_SwitchTypes)
            {
                if (switchType.SwitchesOfType.Contains(interfaceSubmodule))
                {
                    return switchType;
                }
            }

            return null;
        }

        /// <summary>
        /// TGets used reduction ratios of frames which are input to the PNPlanner algorithm.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <returns>Used reduction ratios, separated with commas.</returns>
        private string GetUsedReductionsOfIRTTopIsland(InterfaceFrameDataList topIsland)
        {
            List<long> usedReductions = new List<long>(5);

            foreach (Interface interfaceSubmodule in topIsland.AllPNFrames.Keys)
            {
                List<IPNFrameData> pnFrameDataList = topIsland.AllPNFrames[interfaceSubmodule];

                // Check whether the collection exists. The Item might be Device Submodule.
                if (pnFrameDataList == null)
                {
                    continue;
                }

                foreach (IPNFrameData frameData in pnFrameDataList)
                {
                    //Only irt frames should be taken into consideration for global reduction tag
                    if (frameData.FrameClass != (long)PNIOFrameClass.Class3Frame)
                    {
                        continue;
                    }
                    if (frameData.UpdateTimeMode != (byte)PNUpdateTimeMode.Automatic)
                    {
                        if (frameData.DeviceLocalReductionRatio == 0)
                        {
                            continue;
                        }
                        int searchIndex = usedReductions.BinarySearch(frameData.DeviceLocalReductionRatio);
                        if (searchIndex < 0) // not found in the list
                        {
                            usedReductions.Insert(~searchIndex, frameData.DeviceLocalReductionRatio);
                        }
                    }
                }
            }

            // Set maximum reduction
            SyncDomain.PNPlannerResults.MaxReductionRatio = usedReductions.Count > 0
                                                                ? usedReductions[usedReductions.Count - 1]
                                                                : 1;

            StringBuilder builder = new StringBuilder(15);
            foreach (long reduction in usedReductions)
            {
                builder.AppendFormat(CultureInfo.InvariantCulture, " {0}", reduction);
            }
            if (usedReductions.Count > 0)
            {
                builder.Remove(0, 1); //first comma and blank is deleted
                return builder.ToString();
            }
            return "1";
        }

        private bool IsIrtSupported(Interface interfaceSubmodule)
        {
            Enumerated supportedIrtSyncProtocols =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    new AttributeAccessCode(),
                    null);

            if ((supportedIrtSyncProtocols != null)
                && (supportedIrtSyncProtocols.List.Count > 0))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Multiplies shared device frames in the frame list.
        /// </summary>
        /// <param name="pnFrameDataList">The list of frames.</param>
        private void MultiplySharedDeviceFrames(ref List<IPNFrameData> pnFrameDataList)
        {
            //Check the shared device relevant frames
            if ((pnFrameDataList == null)
                || !pnFrameDataList.Exists(p => p.NumberOfARs > 0))
            {
                return;
            }
            //Create a new instance of the list. Changes will not be kept.
            pnFrameDataList = new List<IPNFrameData>(pnFrameDataList);
            //Add temporary multiplied shared frames to list.

            bool hasSharedDfpFrame = CheckForSharedDfpFrame(pnFrameDataList);
            pnFrameDataList.AddRange(SharedDeviceFrame.SharedPNFrameMultiplier(pnFrameDataList, hasSharedDfpFrame));
        }

        /// <summary>
        /// Prepares the input XML file for the PNPlanner algorithm.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="interfacesOfSwitchNames">
        /// The dictionary that contains the corresponding interface objects for switch
        /// names.
        /// </param>
        /// <param name="inputXml">The output byte array that will contain created XML.</param>
        /// <returns>The result of the operation.</returns>
        private PNPlannerResult Write(
            InterfaceFrameDataList topIsland,
            Dictionary<string, Interface> interfacesOfSwitchNames,
            out byte[] inputXml)
        {
            inputXml = null;
            PNPlannerResult result = CheckPreconditions(topIsland);
            if (result.ResultType != PNPlannerResultType.Successful)
            {
                return result;
            }
            //Clear the member lists.
            m_SwitchTypes.Clear();
            m_Links.Clear();

            using (XmlTextWriter inputXmlWriter = new XmlTextWriter(new MemoryStream(), Encoding.UTF8))
            {
                inputXmlWriter.Formatting = Formatting.Indented;

                // Create the header section.
                inputXmlWriter.WriteStartDocument();

                const string rootElem = PNPlannerConstants.m_XmlHeaderText;
                inputXmlWriter.WriteStartElement(rootElem);
                inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlNameCaption, "PNConfigLib");

                WriteDebugInfo(inputXmlWriter);
                WriteGlobals(topIsland, inputXmlWriter);
                WriteSwitchTypes(topIsland, inputXmlWriter);
                WriteTopology(topIsland, interfacesOfSwitchNames, result, inputXmlWriter);

                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    inputXmlWriter.Close();
                    return result;
                }
                WriteMessages(topIsland, result, inputXmlWriter);
                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    inputXmlWriter.Close();
                    return result;
                }
                if (result.ResultType != PNPlannerResultType.Successful)
                {
                    inputXmlWriter.Close();
                    return result;
                }
                // Close the header section.
                inputXmlWriter.WriteEndElement();

                inputXmlWriter.WriteComment("");

                inputXmlWriter.WriteEndDocument();
                inputXmlWriter.Flush();

                //From MemoryStream to byte[] containing UTF8 characters.
                inputXmlWriter.BaseStream.Position = 0;
                using (StreamReader reader = new StreamReader(inputXmlWriter.BaseStream, Encoding.UTF8, false))
                {
                    string text = reader.ReadToEnd() + '\0';
                    // Uncomment the following 3 lines if you want to write the generated PNPlanner input to disk 
                    //string tempFilePath = Path.GetTempFileName();
                    //Console.WriteLine($"PNPlanner input path: {tempFilePath}");
                    //File.WriteAllText(tempFilePath, text, Encoding.UTF8);
                    inputXml = Encoding.UTF8.GetBytes(text.ToCharArray());
                }

                inputXmlWriter.Close();
            }

            return result;
        }

        /// <summary>
        /// Writes a composite attribute as an xml element, separated with space.
        /// </summary>
        /// <param name="enumeratedAttribute">The attribute to be written.</param>
        /// <param name="inputDataWriter">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="inputtext">The name of the element to be written.</param>
        /// <param name="lists">The list that contains additional input to be written before the attribute value.</param>
        private void WriteComplexXmlElement(
            Enumerated enumeratedAttribute,
            XmlTextWriter inputDataWriter,
            string inputtext,
            List<string> lists)
        {
            if (enumeratedAttribute == null)
            {
                throw new ArgumentNullException(nameof(enumeratedAttribute));
            }

            if (null == inputDataWriter)
            {
                throw new ArgumentNullException(nameof(inputDataWriter));
            }

            foreach (object enumeratedValue in enumeratedAttribute.List)
            {
                lists.Add(enumeratedValue.ToString());
                lists.Add(" ");
            }

            if (lists.Count != 0)
            {
                lists.RemoveAt(lists.Count - 1);
            }

            inputDataWriter.WriteElementString(inputtext, string.Format(CultureInfo.InvariantCulture, @"""{0}""", string.Join("", lists.ToArray())));
        }

        /// <summary>
        /// Writes a list of integers as an xml element, separated with space.
        /// </summary>
        /// <param name="integerList">The list of integers to be written.</param>
        /// <param name="inputDataWriter">XmlTextWriter object used for creating the XML content.</param>
        /// <param name="inputtext">The name of the element to be written.</param>
        /// <param name="lists">The list that contains additional input to be written before the attribute value.</param>
        private void WriteComplexXmlElement(
            IList<int> integerList,
            XmlTextWriter inputDataWriter,
            string inputtext,
            List<string> lists)
        {
            if (null == integerList)
            {
                throw new ArgumentNullException(nameof(integerList));
            }

            if (null == inputDataWriter)
            {
                throw new ArgumentNullException(nameof(inputDataWriter));
            }

            int length = integerList.Count;

            for (int i = 0; i < length; i++)
            {
                lists.Add(integerList[i].ToString(CultureInfo.InvariantCulture));
                if (i != length - 1)
                {
                    lists.Add(" ");
                }
            }
            inputDataWriter.WriteElementString(inputtext, string.Join("", lists.ToArray()));
        }

        /// <summary>
        /// Creates the DebugInfo section of a PNPlanner input file.
        /// </summary>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        private void WriteDebugInfo(XmlTextWriter inputXmlWriter)
        {
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlDebugInfoCaption);

            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlDebugInfoAllCaption,
                PNPlannerConstants.m_XmlShowDebugInfoText);

            // Close the DebugInfo section.
            inputXmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Writes a message to the PNPlanner input xml for each dfp subframe.
        /// </summary>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        /// <param name="frameData">Frame data.</param>
        /// <param name="sender">The sender of messages.</param>
        /// <param name="receivers">
        /// A list which contains the receivers. In inbound direction, the controller
        /// interface submodule is expected at the beginning of the list. In all cases device interface submodules
        /// must be given sorted according to their subframe id.
        /// </param>
        private void WriteDfpMessages(
            XmlTextWriter inputXmlWriter,
            IPNFrameData frameData,
            Interface sender,
            List<Interface> receivers)
        {
            // For each subframe, create a message element.
            IPNDfpFrameData dfpFrameData = frameData as IPNDfpFrameData;
            if ((dfpFrameData == null)
                || (sender == null)
                || (receivers == null)
                || (receivers.Count == 0))
            {
                return;
            }

            // One of the sender or receiver is always fixed with the controller interface submodule.
            Interface currSender = dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame ? sender : null;
            Interface currReceiver = dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame
                                         ? receivers[0]
                                         : null;
            int receiverIndex = 0;
            foreach (IPNSubframeData subframeData in dfpFrameData.Subframes.Values)
            {
                // Get the current sender or receiver from the receiver list. Assume the list is sorted according 
                // to sfId.
                if (dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame)
                {
                    currSender = receiverIndex == receivers.Count - 1 ? sender : receivers[++receiverIndex];
                }
                else if (dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame)
                {
                    currReceiver = receivers[receiverIndex++];
                }

                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlMessageCaption);

                // Attributes
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlMsgIdCaption,
                    dfpFrameData.FrameID.ToString(CultureInfo.InvariantCulture));
                if (frameData.RedundantFrameID != 0)
                {
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlRedundantMsgIdCaption,
                        dfpFrameData.RedundantFrameID.ToString(CultureInfo.InvariantCulture));
                }
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSFIdCaption,
                    subframeData.SubframeId.ToString(CultureInfo.InvariantCulture));

                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlLengthCaption,
                    subframeData.SubframeLength.ToString(CultureInfo.InvariantCulture));

                // Children Elements
                string direction = dfpFrameData.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame
                                       ? PNPlannerConstants.m_XmlinboundText
                                       : PNPlannerConstants.m_XmloutboundText;
                inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlDirectionCaption, direction);

                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlReductionCaption,
                    frameData.DeviceLocalReductionRatio.ToString(CultureInfo.InvariantCulture));
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlPhaseCaption,
                    PNPlannerConstants.m_XmlZeroText);

                //FrameType             
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlFrameType,
                    frameData.FrameType.ToString(CultureInfo.InvariantCulture));

                //FrameDirection
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlFrameDirection,
                    frameData.FrameDirection.ToString(CultureInfo.InvariantCulture));

                //HasUsingData
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlHasUsingData,
                    frameData.HasUsingData.ToString(CultureInfo.InvariantCulture));

                //StationNo
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlStationNoCaption,
                    frameData.StationNumber.ToString(CultureInfo.InvariantCulture));

                //FrameClass
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlFrameClassCaption,
                    frameData.FrameClass.ToString(CultureInfo.InvariantCulture));

                //FixedPhaseNumber
                if ((((PNIOFrameClass)frameData.FrameClass == PNIOFrameClass.Class3Frame)
                     && ((PNUpdateTimeMode)frameData.UpdateTimeMode == PNUpdateTimeMode.Automatic))
                    || ((PNIOFrameClass)frameData.FrameClass != PNIOFrameClass.Class3Frame))
                {
                    frameData.FixedPhaseNumber = 0; // this value will be at the caller-side
                }
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlFixedPhaseNumberCaption,
                    frameData.FixedPhaseNumber.ToString(CultureInfo.InvariantCulture));

                if (frameData.UpdateTimeMode == 0)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                        "\"" + PNPlannerConstants.m_XmlAutomatic + "\"");
                }

                else if (frameData.UpdateTimeMode == 1)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                        "\"" + PNPlannerConstants.m_XmlFixedTime + "\"");
                }

                else if (frameData.UpdateTimeMode == 2)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                        "\"" + PNPlannerConstants.m_XmlFixedReduction + "\"");
                }

                // Write the current sender
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSenderCaption);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSwitchCaption,
                    currSender == null
                        ? string.Empty
                        : AttributeUtilities.GetSubmoduleNameWithContainerAndStation(currSender));
                inputXmlWriter.WriteEndElement();

                // Write the current receiver
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlReceiverCaption);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSwitchCaption,
                    currReceiver == null
                        ? string.Empty
                        : AttributeUtilities.GetSubmoduleNameWithContainerAndStation(currReceiver));
                if (dfpFrameData.RedundantFrameID != 0)
                {
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlRedundancyCaption, "2");
                }
                inputXmlWriter.WriteEndElement();

                inputXmlWriter.WriteEndElement(); // End of Message
            }
        }

        /// <summary>
        /// Creates the Globals section of a PNPlanner input file.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        private void WriteGlobals(InterfaceFrameDataList topIsland, XmlTextWriter inputXmlWriter)
        {
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlGlobalsCaption);

            AttributeAccessCode ac = new AttributeAccessCode();
            string attributeValue =
                SyncDomain.PCLObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.PnPnPlannerByteLengthFactor,
                    ac,
                    string.Empty);
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlByteLengthFactorCaption, attributeValue);

            attributeValue =
                SyncDomain.PCLObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.PnPnPlannerMinInterLsduGap,
                    ac.GetNew(),
                    string.Empty);
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlMinInterLsduGapCaption, attributeValue);

            string reductionRatiosText = GetUsedReductionsOfIRTTopIsland(topIsland);

            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlReductionsCaption, "\"" + reductionRatiosText + "\"");

            // If the ClockSynchronization is not activated, MaxPhases are same as 
            // the Possible Reduction Ratios.
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlMaxPhasesCaption, "\"" + reductionRatiosText + "\"");

            attributeValue =
                SyncDomain.PCLObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.PnPnPlannerAdditionalLinkDelay,
                    ac.GetNew(),
                    string.Empty);
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlAdditionalLinkDelayCaption, attributeValue);


            bool syncDomFragActive = SyncDomain.IsFragmentationActive;
            PNIRTBandwidthLevel currentBandwidthLevel =
                (PNIRTBandwidthLevel)
                SyncDomain.PCLObject.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtBandwidthLevel,
                    ac,
                    (byte)PNIRTBandwidthLevel.Fair);
            //SyncDomFragActive
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlSyncDomFragActive,
                syncDomFragActive ? PNPlannerConstants.m_XmlTrueText : PNPlannerConstants.m_XmlFalseText);
            //CreateNRTFrames
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlCreateNRTFrames,
                syncDomFragActive || (currentBandwidthLevel > PNIRTBandwidthLevel.Fair)
                    ? PNPlannerConstants.m_XmlTrueText
                    : PNPlannerConstants.m_XmlFalseText);
            // UseAverageBwMode is only activated if sync-domain bandwidth value is MaximumIO
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlUseAverageBwMode,
                currentBandwidthLevel == PNIRTBandwidthLevel.MaximumIO
                    ? PNPlannerConstants.m_XmlTrueText : PNPlannerConstants.m_XmlFalseText);

            //MinAutomaticUnsyncUpdateTime
            int defaultRR;

            Interface interfaceSubmodule = SyncDomain.AllParticipants.First();
            if (Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
            {
                if (AttributeUtilities.IsIDevice(interfaceSubmodule) &&
                    AttributeUtilities.IsDecentralPDEV(interfaceSubmodule))
                {
                    defaultRR =
                        Utility.GetDefaultRRForUnsyncFrames(
                            NavigationUtilities.GetControllerInterfaceOfDeviceInterface(interfaceSubmodule));
                }
                else
                {
                    defaultRR = Utility.GetDefaultRRForUnsyncFrames(interfaceSubmodule);
                }
            }
            else
            {
                defaultRR =
                    Utility.GetDefaultRRForUnsyncFrames(
                        NavigationUtilities.GetControllerInterfaceOfDeviceInterface(interfaceSubmodule));
            }
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlMinAutomaticUnsyncUpdateTime, defaultRR.ToString(CultureInfo.InvariantCulture));


            WritePrePlanningGlobals(inputXmlWriter);
            // Close the Globals section.
            inputXmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Writes links to the PNPlanner input xml.
        /// </summary>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        private void WriteLinks(XmlTextWriter inputXmlWriter, InterfaceFrameDataList topIsland)
        {

            foreach (Interface interfaceSubmodule in topIsland.AllPNFrames.Keys)
            {
                // Get the ports of the switch.
                List<Port> ports = NavigationUtilities.GetPortModules(interfaceSubmodule);

                // Get the links and add them to the input XML file.
                foreach (Port port in ports)
                {
                    // Get the partner ports of the switch.
                    List<Port> partnerPorts = (List<Port>)port.GetPartnerPorts();
                    

                    foreach (Port partnerPort in partnerPorts)
                    {
                        // Create the link object for the ports.
                        Link link = new Link(port, partnerPort);

                        // Check whether the "From" and "To" Submodules are in the topology.
                        if (!topIsland.AllPNFrames.ContainsKey(link.From)
                            || !topIsland.AllPNFrames.ContainsKey(link.To))
                        {
                            // If one of them is not included, that means it does not use IrtTop
                            // but has a port-to-port connection. Skip this link.
                            continue;
                        }

                        // Check whether the link has already been created.
                        if ((link.To == null)
                            || m_Links.Contains(link))
                        {
                            continue;
                        }
                        // The link has not been added before, add it to the local list.
                        m_Links.Add(link);
                    }
                }
            }

            if (m_Links != null
                && m_Links.Count > 0)
            {
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlLinksCaption);

                foreach (Link link in m_Links)
                {
                    inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlLinkCaption);

                    // Write the element attributes of the port.
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlLinkDelayCaption,
                        link.LinkDelay.ToString(CultureInfo.InvariantCulture));
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlFromSwitchCaption, link.FromSwitchText);
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlFromPortCaption, link.FromPortText);
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlToSwitchCaption, link.ToSwitchText);
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlToPortCaption, link.ToPortText);

                    // Close the header section of the link.
                    inputXmlWriter.WriteEndElement();
                }
                inputXmlWriter.WriteEndElement();
            }
        }

        /// <summary>
        /// Writes a message to the PNPlanner input xml.
        /// </summary>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        /// <param name="frameData">Frame data.</param>
        /// <param name="sender">Sender of the message.</param>
        /// <param name="receivers">Receivers of the message.</param>
        private void WriteMessage(
            XmlTextWriter inputXmlWriter,
            IPNFrameData frameData,
            Interface sender,
            List<Interface> receivers)
        {
            // Create the header section of the message.
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlMessageCaption);
            // Write the element attributes of the port.
            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlMsgIdCaption,
                frameData.FrameID.ToString(CultureInfo.InvariantCulture));

            if (frameData.RedundantFrameID != 0)
            {
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlRedundantMsgIdCaption,
                    frameData.RedundantFrameID.ToString(CultureInfo.InvariantCulture));
            }

            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlLengthCaption,
                frameData.DataLength.ToString(CultureInfo.InvariantCulture));

            if (frameData.FrameType == (long)PNPlannerFrameType.SyncFrame)
            {
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSyncCaption,
                    PNPlannerConstants.m_XmlOneText);
            }

            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlWatchdogFactorCaption,
                frameData.WatchdogFactor.ToString(CultureInfo.InvariantCulture));

            if (frameData.GroupNo != 0)
            {
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlGroupNumber,
                    frameData.GroupNo.ToString(CultureInfo.InvariantCulture));
            }

            // Write the elements of the section
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlReductionCaption,
                frameData.DeviceLocalReductionRatio.ToString(CultureInfo.InvariantCulture));

            //FrameType
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlFrameType,
                frameData.FrameType.ToString(CultureInfo.InvariantCulture));

            //FrameDirection
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlFrameDirection,
                frameData.FrameDirection.ToString(CultureInfo.InvariantCulture));

            //HasUsingData
            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlHasUsingData, frameData.HasUsingData.ToString(CultureInfo.InvariantCulture));

            //StationNo
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlStationNoCaption,
                frameData.StationNumber.ToString(CultureInfo.InvariantCulture));

            //FrameClass
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlFrameClassCaption,
                frameData.FrameClass.ToString(CultureInfo.InvariantCulture));

            //FixedPhaseNumber
            if ((((PNIOFrameClass)frameData.FrameClass == PNIOFrameClass.Class3Frame)
                 && ((PNUpdateTimeMode)frameData.UpdateTimeMode == PNUpdateTimeMode.Automatic))
                || ((PNIOFrameClass)frameData.FrameClass != PNIOFrameClass.Class3Frame))
            {
                frameData.FixedPhaseNumber = 0; // this value will be at the caller-side
            }
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlFixedPhaseNumberCaption,
                frameData.FixedPhaseNumber.ToString(CultureInfo.InvariantCulture));

            if (frameData.UpdateTimeMode == 0)
            {
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                    "\"" + PNPlannerConstants.m_XmlAutomatic + "\"");
            }

            else if (frameData.UpdateTimeMode == 1)
            {
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                    "\"" + PNPlannerConstants.m_XmlFixedTime + "\"");
            }

            else if (frameData.UpdateTimeMode == 2)
            {
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlUpdateTimeModeCaption,
                    "\"" + PNPlannerConstants.m_XmlFixedReduction + "\"");
            }

            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlSendClockFactor,
                frameData.SendClockFactor.ToString(CultureInfo.InvariantCulture));

            // Check external send clock.
            // External send clock is determined from IOD; check both sender and receivers.
            bool externalSendClock = false;
            if ((sender != null)
                && Utility.IsProfinetDeviceInterfaceSubmodule(sender))
            {
                if ((SharedDeviceUtility.GetSharedAccess(sender) == SharedIoAssignment.NotAssigned)
                    && !SharedDeviceUtility.IsParameterizationDisallowed(sender))
                {
                    externalSendClock = true;
                    long externalSendClockFactor =
                        sender.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoExternalSendClockFactor,
                            new AttributeAccessCode(),
                            32);
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlExternalSendClockFactor,
                        externalSendClockFactor.ToString(CultureInfo.InvariantCulture));
                }
            }

            if (externalSendClock == false)
            {
                foreach (Interface receiver in receivers)
                {
                    if (Utility.IsProfinetDeviceInterfaceSubmodule(receiver))
                    {
                        if ((SharedDeviceUtility.GetSharedAccess(receiver) == SharedIoAssignment.NotAssigned)
                            && !SharedDeviceUtility.IsParameterizationDisallowed(receiver))
                        {
                            long externalSendClockFactor =
                                receiver.AttributeAccess.GetAnyAttribute<long>(
                                    InternalAttributeNames.PnIoExternalSendClockFactor,
                                    new AttributeAccessCode(),
                                    32);
                            inputXmlWriter.WriteElementString(
                                PNPlannerConstants.m_XmlExternalSendClockFactor,
                                externalSendClockFactor.ToString(CultureInfo.InvariantCulture));
                            break;
                        }
                    }
                }
            }

            // Create the element for the sender.
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSenderCaption);
            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlSwitchCaption,
                sender == null ? string.Empty : AttributeUtilities.GetSubmoduleNameWithContainerAndStation(sender));
            // Close the element for the sender.
            inputXmlWriter.WriteEndElement();

            foreach (Interface receiver in receivers)
            {
                // Create the element for the receiver.
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlReceiverCaption);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSwitchCaption,
                    receiver == null
                        ? string.Empty
                        : AttributeUtilities.GetSubmoduleNameWithContainerAndStation(receiver));
                if (frameData.RedundantFrameID != 0)
                {
                    // Add the related attribute
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlRedundancyCaption, "2");
                }
                // Close the element for the receiver.
                inputXmlWriter.WriteEndElement();
            }

            // Close the header section of the message.
            inputXmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Writes messages to the PNPlanner input xml.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="result">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        private void WriteMessages(
            InterfaceFrameDataList topIsland,
            PNPlannerResult result,
            XmlTextWriter inputXmlWriter)
        {
            // Create the Messages section.
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlMessagesCaption);

            int numberOfMessagesWritten = 0;

            // Getting the actual settings for Message Lengths
            uint maxFrameLength =
                SyncDomain.PCLObject.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPnPlannerMaxFrameLength,
                    new AttributeAccessCode(),
                    0);

            Dictionary<int, int> interfaceIdIoConnectorIdMapping =
                PNPlannerHelper.BuildInterfaceAndIODeviceMapping(SyncDomain.AllParticipants.ToList());

            // Get the messages and add them to the input XML file.
            foreach (Interface interfaceSubmodule in topIsland.AllPNFrames.Keys)
            {
                List<IPNFrameData> pnFrameDataList = topIsland.AllPNFrames[interfaceSubmodule];
                
                // Check whether the collection exists. The Item might be Device Submodule.
                if (pnFrameDataList == null)
                {
                    continue;
                }

                MultiplySharedDeviceFrames(ref pnFrameDataList);

                foreach (IPNFrameData frameData in pnFrameDataList)
                {
                    if (frameData.FrameType == (long)PNPlannerFrameType.SyncFrame)
                    {
                        continue;
                    }
                    // Get the sender and receiver of the message.
                    Interface sender = PNPlannerHelper.GetSenderOfFrame(
                        frameData,
                        SyncDomain,
                        interfaceSubmodule,
                        interfaceIdIoConnectorIdMapping);
                    List<Interface> receivers = PNPlannerHelper.GetReceiversOfFrame(
                        frameData,
                        SyncDomain,
                        interfaceSubmodule,
                        interfaceIdIoConnectorIdMapping);
                    // Check whether the sender and receivers are in the topology.)
                    foreach (Interface receiver in receivers)
                    {
                        if (!topIsland.AllPNFrames.ContainsKey(receiver))
                        {
                            break;
                        }
                    }

                    // Provide the FrameLength to be in the permitted interval.
                    if ((frameData.DataLength > maxFrameLength)
                        && (frameData.FrameClass == 3))
                    {
                        frameData.DataLength = maxFrameLength;
                        Debug.Fail("Data length of the frame is larger than the max. frame length.");
                    }

                    if (frameData is IPNDfpFrameData)
                    {
                        WriteDfpMessages(inputXmlWriter, frameData, sender, receivers);
                    }
                    else
                    {
                        WriteMessage(inputXmlWriter, frameData, sender, receivers);
                    }

                    numberOfMessagesWritten++;
                }
            }

            // Close the Messages section.
            inputXmlWriter.WriteEndElement();

            result.ResultType = numberOfMessagesWritten == 0
                                    ? PNPlannerResultType.NoMessagesToSend
                                    : PNPlannerResultType.Successful;
        }

        /// <summary>
        /// Prepares the additional definitions for the Switch element of the preplanning input.
        /// </summary>
        /// <param name="inputXmlWriter">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="interfaceSubmodule">The interface submodule for which additional definitions will be written.</param>
        private void WritePrePlanningExForSwitch(XmlTextWriter inputXmlWriter, Interface interfaceSubmodule)
        {
            if ((inputXmlWriter == null)
                || (interfaceSubmodule == null))
            {
                return;
            }

            // Check if subframe alignment is switched on from the settings

            if (Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                int dfpFrames =
                    (int)interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxDfpFramesIOC,
                        ac,
                        0);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlDfpSupportedCaption,
                    dfpFrames > 0 ? PNPlannerConstants.m_XmlTrueText : PNPlannerConstants.m_XmlFalseText);
            }

            else if (Utility.IsProfinetDeviceInterfaceSubmodule(interfaceSubmodule))
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                uint dfpFrames =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtMaxDfpFrames,
                        ac,
                        0);
                if (dfpFrames > 0)
                {
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlDfpSupportedCaption,
                        PNPlannerConstants.m_XmlTrueText);

                    string attrVal =
                        interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnIrtAlignDfpSubframes,
                            ac.GetNew(),
                            false).ToString(CultureInfo.InvariantCulture).ToUpperInvariant();
                    inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlAlignDFP_SubframesCaption, attrVal);
                }

                else
                {
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlDfpSupportedCaption,
                        PNPlannerConstants.m_XmlFalseText);
                }
            }
        }

        /// <summary>
        /// Prepares the additional definitions for the SwitchType element of the preplanning input.
        /// </summary>
        /// <param name="inputXmlWriter">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="switchType">The switchType for which additional definitions will be written.</param>
        private void WritePrePlanningExForSwitchType(XmlTextWriter inputXmlWriter, SwitchType switchType)
        {
            if (inputXmlWriter == null)
            {
                return;
            }

            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlDWD_RestartFactorCaption,
                switchType.DistributedWatchdogRestartFactor.ToString(CultureInfo.InvariantCulture));
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlSupportsDfpInRingCaption,
                switchType.SupportsDfpInRing ? "true" : "false");
            if (switchType.MaxDfpFrames != 0)
            {
                inputXmlWriter.WriteElementString(
                    PNPlannerConstants.m_XmlMaxDfpFramesCaption,
                    switchType.MaxDfpFrames.ToString(CultureInfo.InvariantCulture));
            }

            inputXmlWriter.WriteElementString(PNPlannerConstants.m_XmlMinFSOCaption, switchType.MinFSO.ToString(CultureInfo.InvariantCulture));
        }

        /// <summary>
        /// Prepares the additional definitions for the Globals element of the the preplanning input.
        /// </summary>
        /// <param name="inputXmlWriter"></param>
        private void WritePrePlanningGlobals(XmlTextWriter inputXmlWriter)
        {
            if (inputXmlWriter == null)
            {
                return;
            }

            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlMaxSizeREDCaption,
                DomainManagementUtility.GetMaxSizeRed(SyncDomain).ToString(CultureInfo.InvariantCulture));
            inputXmlWriter.WriteElementString(
                PNPlannerConstants.m_XmlMaxPackgroupSwitchesCaption,
                PNConstants.MaxPackgroupSwitches.ToString(CultureInfo.InvariantCulture));
        }

        /// <summary>
        /// Writes the switch to the PNPlanner input xml.
        /// </summary>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        /// <param name="interfaceSubmodule">The interface submodule (switch) that will be written.</param>
        /// <param name="result">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="interfacesOfSwitchNames">
        /// The dictionary that contains the corresponding interface objects for switch
        /// names.
        /// </param>
        private void WriteSwitch(
            XmlTextWriter inputXmlWriter,
            Interface interfaceSubmodule,
            PNPlannerResult result,
            Dictionary<string, Interface> interfacesOfSwitchNames)
        {
            // Get corresponding switch type.
            SwitchType switchType = GetSwitchType(interfaceSubmodule);
            if (switchType == null)
            {
                result.ResultType = PNPlannerResultType.SystemError;
                result.Description = "SwitchType cannot be found.";
                inputXmlWriter.Flush();
                inputXmlWriter.Close();
                return;
            }

            // Create the header section of the switch.
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSwitchCaption);

            // Write the element attributes of the switch.
            string switchName = PNPlannerHelper.GetSwitchName(interfaceSubmodule);

            PNIOD ioDevice = interfaceSubmodule.PNIOD;
            PNIOC ioController = interfaceSubmodule.PNIOC;

            int proxyNumber = 0;
            AttributeAccessCode ac = new AttributeAccessCode();

            bool isProxy = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIsProxy,
                ac,
                false);

            if ((ioDevice != null) && isProxy)
            {
                int pnStationNumber =
                    ioDevice.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnStationNumber, ac, 0);
                proxyNumber = pnStationNumber;
            }

            interfacesOfSwitchNames.Add(switchName, interfaceSubmodule);
            inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlNameCaption, switchName);
            inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlTypeCaption, switchType.Name);

            //IoSyncRole
            if (ioController != null)
            {
                int ioControllerSyncRole = (int)PNAttributeUtility.GetAdjustedSyncRole(ioController);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlIoControllerSyncRoleCaption,
                    ioControllerSyncRole.ToString(CultureInfo.InvariantCulture));
            }
            if (ioDevice != null)
            {
                int ioDeviceSyncRole = (int)PNAttributeUtility.GetAdjustedSyncRole(ioDevice);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlIoDeviceSyncRoleCaption,
                    ioDeviceSyncRole.ToString(CultureInfo.InvariantCulture));
            }

            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlProxyNumberCaption,
                proxyNumber.ToString(CultureInfo.InvariantCulture));

            if (Utility.IsProfinetDeviceInterfaceSubmodule(interfaceSubmodule))
            {
                Interface controllerInterface = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlAssignedControllerCaption,
                    PNPlannerHelper.GetSwitchName(controllerInterface));
            }

            WritePrePlanningExForSwitch(inputXmlWriter, interfaceSubmodule);

            if (MachineTailorUtility.IsOptionalDeviceEnabled(interfaceSubmodule))
            {
                inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlIsOptionalCaption, "true");
            }

            //SendClockFactor
            long pnIoSendClockFactor = 32;
            if (Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
            {
                pnIoSendClockFactor = AttributeUtilities.GetTransientPNSendClockFactor(
                    interfaceSubmodule,
                    PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor);
                inputXmlWriter.WriteAttributeString(
                    PNPlannerConstants.m_XmlSendClockFactor,
                    pnIoSendClockFactor.ToString(CultureInfo.InvariantCulture));
            }

            //MaxIOCyclicBandwidth
            long pnMaxCyclicDataBw = Utility.GetMaxIoCyclicBandwidth(pnIoSendClockFactor, SyncDomain);
            inputXmlWriter.WriteAttributeString(
                PNPlannerConstants.m_XmlMaxIOCyclicBandwidth,
                pnMaxCyclicDataBw.ToString(CultureInfo.InvariantCulture));

            // Close the header section of the switch.
            inputXmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Writes the switchtypes to the PNPlanner input xml.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        private void WriteSwitchTypes(InterfaceFrameDataList topIsland, XmlTextWriter inputXmlWriter)
        {
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSwitchTypesCaption);

            int switchIndex = 1;

            foreach (Interface interfaceSubmodule in topIsland.AllPNFrames.Keys)
            {
                // Try to create a SwitchType for this Interface Submodule.
                // The method adds it to m_SwitchTypes
                CreateSwitchType(interfaceSubmodule);
            }
            foreach (SwitchType switchType in m_SwitchTypes)
            {
                // Assign the name of the SwitchType externally
                switchType.Name = PNPlannerConstants.m_XmlSwitchCaption + switchIndex++;

                // Create the header section of the switch.
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSwitchTypeCaption);
                inputXmlWriter.WriteAttributeString(PNPlannerConstants.m_XmlNameCaption, switchType.Name);

                // Write the elements of the switch.

                if (switchType.IsIrt)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxBridgeDelayCaption,
                        switchType.MaxBridgeDelay.ToString(CultureInfo.InvariantCulture));
                    if (switchType.MaxBridgeDelayFFW != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlMaxBridgeDelayFFWCaption,
                            switchType.MaxBridgeDelayFFW.ToString(CultureInfo.InvariantCulture));
                    }

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlSuppRTC3StartupModes,
                        PNPlannerConstants.m_XmlDoubleQuoteText + switchType.StartupMode + PNPlannerConstants.m_XmlDoubleQuoteText);
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlForwardingModeCaption,
                        PNIrtForwardingMode.Relative == switchType.ForwardingMode
                            ? PNPlannerConstants.m_XmlDoubleQuoteText + PNPlannerConstants.m_XmlRelativeText + PNPlannerConstants.m_XmlDoubleQuoteText
                            : PNPlannerConstants.m_XmlDoubleQuoteText + PNPlannerConstants.m_XmlAbsoluteText + PNPlannerConstants.m_XmlDoubleQuoteText);
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxBufferTimeCaption,
                        switchType.MaxBufferTime.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMinRTC3GapCaption,
                        switchType.AdditionalLsduGap.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxDfpFeedCaption,
                        switchType.MaxDfpFeed.ToString(CultureInfo.InvariantCulture));
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlPeerToPeerJitterCaption,
                        switchType.PeerToPeerJitter.ToString(CultureInfo.InvariantCulture));
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxSwitchTimeLagCaption,
                        switchType.MaxTimeLag.ToString(CultureInfo.InvariantCulture));
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlSFCRC16Caption,
                        switchType.SfCrc16.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxFrameStartTime,
                        switchType.MaxFrameStartTime.ToString(CultureInfo.InvariantCulture));
                    //DWD_RestartFactor, SupportsDfpInRing, MaxDfpFrames, MinFSO
                    WritePrePlanningExForSwitchType(inputXmlWriter, switchType);
                }

                if (Utility.IsProfinetControllerInterfaceSubmodule(switchType.InterfaceSubmodule))
                {
                    // Create the header section of the IOController.
                    inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlIOControllerCaption);

                    if (switchType.DistributionMode == 1)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlDistributionMode,
                            PNPlannerConstants.m_XmlDoubleQuoteText + PNPlannerConstants.m_XmlFrameEquipartition + PNPlannerConstants.m_XmlDoubleQuoteText);
                    }

                    else if (switchType.DistributionMode == 2)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlDistributionMode,
                            PNPlannerConstants.m_XmlDoubleQuoteText + PNPlannerConstants.m_XmlByteEquipartition + PNPlannerConstants.m_XmlDoubleQuoteText);
                    }

                    if (switchType.MaxFramesPerMs != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlMaxFramesPerMs,
                            switchType.MaxFramesPerMs.ToString(CultureInfo.InvariantCulture));
                    }

                    if (switchType.MaxBytesPerMs != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlMaxBytesPerMs,
                            switchType.MaxBytesPerMs.ToString(CultureInfo.InvariantCulture));
                    }

                    if (switchType.MaxRTC12BytesPerMs != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlMaxRTC12BytesPerMs,
                            switchType.MaxRTC12BytesPerMs.ToString(CultureInfo.InvariantCulture));
                    }

                    if (switchType.ScfAdaptionSupported != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlScfAdaptionSupported,
                            switchType.ScfAdaptionSupported.ToString(CultureInfo.InvariantCulture));
                    }

                    if (switchType.ScfAdaptionNonPow2Supported != 0)
                    {
                        inputXmlWriter.WriteElementString(
                            PNPlannerConstants.m_XmlScfAdaptionNonPow2Supported,
                            switchType.ScfAdaptionNonPow2Supported.ToString(CultureInfo.InvariantCulture));
                    }

                    // Create the header section of the IOController.
                    inputXmlWriter.WriteEndElement();
                }

                // If IoDevice is a PNProxy Device
                if (switchType.IsProxy)
                {
                    // Create the header section of the Proxy.
                    inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlPNProxy);

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxFramesPerMs,
                        switchType.MaxFramesPerMs.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxBytesPerMs,
                        switchType.MaxBytesPerMs.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMaxRTC12BytesPerMs,
                        switchType.MaxRTC12BytesPerMs.ToString(CultureInfo.InvariantCulture));

                    inputXmlWriter.WriteEndElement();
                }

                //SupportedSendClockFactors12(PNIoSuppSCF12)

                List<string> listSCF12 = new List<string>();
                if (switchType.SupportedSendClockFactors12 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SupportedSendClockFactors12,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSupportedSendClockFactors12,
                        listSCF12);
                }
                else
                {
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNRTSupportedSendClockFactors,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSupportedSendClockFactors12,
                        listSCF12);
                }

                //SupportedSendClockFactors3(PNIoSuppSCF3)
                List<string> listSCF3 = new List<string>();
                if (switchType.SupportedSendClockFactors3 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SupportedSendClockFactors3,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSupportedSendClockFactors3,
                        listSCF3);
                }
                else
                {
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNRTSupportedSendClockFactors,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSupportedSendClockFactors3,
                        listSCF3);
                }

                //MinNrtGap
                if (switchType.MinNrtGap != 0)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMinNRTGap,
                        switchType.MinNrtGap.ToString(CultureInfo.InvariantCulture));
                }

                //MinFrameInterval
                if (switchType.MinFrameInterval != 0)
                {
                    inputXmlWriter.WriteElementString(
                        PNPlannerConstants.m_XmlMinFrameInterval,
                        switchType.MinFrameInterval.ToString(CultureInfo.InvariantCulture));
                }

                //SuppRR12Pow2
                List<string> suppRR12Pow2 = new List<string>();
                if (switchType.SuppRr12Pow2 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SuppRr12Pow2,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR12Pow2,
                        suppRR12Pow2);
                }
                else
                {
                    // Write Default Values
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow12,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR12Pow2,
                        suppRR12Pow2);
                }

                //SuppRR12NonPow2   
                List<string> suppRR12NonPow2 = new List<string>();
                if (switchType.SuppRr12NonPow2 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SuppRr12NonPow2,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR12NonPow2,
                        suppRR12NonPow2);
                }
                else
                {
                    // Write Default Values
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow12,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR12NonPow2,
                        suppRR12NonPow2);
                }

                //SuppRR3Pow2
                List<string> suppRR3Pow2 = new List<string>();
                if (switchType.SuppRr3Pow2 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SuppRr3Pow2,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR3Pow2,
                        suppRR3Pow2);
                }
                else
                {
                    // Write Default Values
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow3,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR3Pow2,
                        suppRR3Pow2);
                }

                //SuppRR3NonPow2   
                List<string> suppRR3NonPow2 = new List<string>();
                if (switchType.SuppRr3NonPow2 != null)
                {
                    WriteComplexXmlElement(
                        switchType.SuppRr3NonPow2,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR3NonPow2,
                        suppRR3NonPow2);
                }
                else
                {
                    // Write Default Values
                    WriteComplexXmlElement(
                        PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow3,
                        inputXmlWriter,
                        PNPlannerConstants.m_XmlSuppRR3NonPow2,
                        suppRR3NonPow2);
                }

                // If there are no ports, close the header section of the switch and continue with next switch type.
                if (switchType.Ports.Count == 0)
                {
                    // Close the header section of the switch.
                    inputXmlWriter.WriteEndElement();
                    continue;
                }

                // Create the header section of the ports.
                inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlPortsCaption);
                // Go through all ports of the switch and add them to the input XML file.
                foreach (DataClasses.Port port in switchType.Ports)
                {
                    // Create the header section of the port.
                    inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlPortCaption);

                    // Write the element attributes of the port.
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlNumberCaption,
                        port.Number.ToString(CultureInfo.InvariantCulture));
                    if (!switchType.IsIrt)
                    {
                        inputXmlWriter.WriteEndElement();
                        continue;
                    }

                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlRxDelayCaption,
                        port.RxDelay.ToString(CultureInfo.InvariantCulture));
                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlTxDelayCaption,
                        port.TxDelay.ToString(CultureInfo.InvariantCulture));
                    if (port.ShortPreamble100MBitSupported)
                    {
                        inputXmlWriter.WriteAttributeString(
                            PNPlannerConstants.m_XmlShortPreamble100MBitSupported,
                            PNPlannerConstants.m_XmlTrueText);
                    }

                    inputXmlWriter.WriteAttributeString(
                        PNPlannerConstants.m_XmlMediaType,
                        port.IsFiberOptic ? PNPlannerConstants.m_XmlFOText : PNPlannerConstants.m_XmlCUText);

                    // Close the header section of the port.
                    inputXmlWriter.WriteEndElement();
                }
                // Create the header section of the ports.
                inputXmlWriter.WriteEndElement();

                // Close the header section of the switch.
                inputXmlWriter.WriteEndElement();
            }

            // Close the Switches section.
            inputXmlWriter.WriteEndElement();
        }

        /// <summary>
        /// Writes the topology to the PNPlanner input xml.
        /// </summary>
        /// <param name="topIsland">The dictionary that contains interfaces and their list of frames.</param>
        /// <param name="interfacesOfSwitchNames">
        /// The dictionary that contains the corresponding interface objects for switch
        /// names.
        /// </param>
        /// <param name="result">The PNPlannerResult object that contains the result of the operation.</param>
        /// <param name="inputXmlWriter">XmlTextWriter object used for creating the XML content.</param>
        private void WriteTopology(
            InterfaceFrameDataList topIsland,
            Dictionary<string, Interface> interfacesOfSwitchNames,
            PNPlannerResult result,
            XmlTextWriter inputXmlWriter)
        {
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlTopologyCaption);
            // Create the Switches section.
            inputXmlWriter.WriteStartElement(PNPlannerConstants.m_XmlSwitchesCaption);

            // get distinct participants to recognize shared devices
            List<Interface> distinctSyncDomainParticipants = SyncDomain.AllParticipants 
                .GroupBy(participant => participant.Id)
                .Select(gp => gp.First())
                .ToList();
            // Go through all switches that uses IrtTop.
            foreach (Interface interfaceSubmodule in distinctSyncDomainParticipants)
            {
                WriteSwitch(inputXmlWriter, interfaceSubmodule, result, interfacesOfSwitchNames);
            }

            // Close the Switches section.
            inputXmlWriter.WriteEndElement();

            WriteLinks(inputXmlWriter, topIsland);

            // Close the Topology section.
            inputXmlWriter.WriteEndElement();
        }
    }
}