﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_043.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Xml.XPath;
using System.Globalization;
using PNConfigLib.Gsd.Interpreter.Common;
using GSDI;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.43 and is based on GSD(ML) versions 2.42 and lower.
    ///	
    /// </summary>
    internal class CheckerV02043 : CheckerV02042
    {
        #region Fields

        #endregion


        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:InterfaceSubmoduleItem/@DCP_FeaturesSupported" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SNMP_FeaturesSupported" +
                            " | .//gsddef:InterfaceSubmoduleItem/@APL_FeaturesSupported" +
                            " | .//gsddef:NetloadClasses/@LinkSpeed" +
                            " | .//gsddef:CIM_SupportedRecords/@SupportedRecords" +
                            " | .//gsddef:CIM_SupportedFeatures/@SNMP_FeaturesSupported " +
                            " | .//gsddef:KeyDerivation/@Algorithms" +
                            " | .//gsddef:KeyAgreement/@Algorithms" +
                            " | .//gsddef:DigitalSignature/@Algorithms" +
                            " | .//gsddef:StreamProtection/@AuthnOnly" +
                            " | .//gsddef:StreamProtection/@AuthnEnc" +
                            " | .//gsddef:AlarmProtection/@AuthnOnly" +
                            " | .//gsddef:AlarmProtection/@AuthnEnc" +
                            " | .//gsddef:ConnectionManagementProtection/@AuthnOnly" +
                            " | .//gsddef:ConnectionManagementProtection/@AuthnEnc";
                return (xp);
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList;
                return (xp);
            }
        }

        #endregion


        #region CheckerObject Members


        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02043;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02043;
        }


        override protected bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00043000);
            Checks.Add(Constants.s_Cn_0X00043001);
            Checks.Add(Constants.s_Cn_0X00043002);
            Checks.Add(Constants.s_Cn_0X00043003);
            Checks.Add(Constants.s_Cn_0X00043004);
            Checks.Add(Constants.s_Cn_0X00043005);
            Checks.Add(Constants.s_Cn_0X00043006);
            Checks.Add(Constants.s_Cn_0X00043007);
            Checks.Add(Constants.s_Cn_0X00043008);
            Checks.Add(Constants.s_Cn_0X00043009);
            Checks.Add(Constants.s_Cn_0X00043010);

            return succeeded;
        }

        override protected bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                Checks.Remove(Constants.s_Cn_0X00041000);
                Checks.Remove(Constants.s_Cn_0X00041001);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion




        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.43.
        /// </summary>
        public CheckerV02043()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version243);
        }

        #endregion


        #region Methods

        /// <summary>
        /// 'ProfileProcessAutomation/@PAProfileVersion': version check must be removed.
        /// </summary>
        /// <param></param>
        /// <returns></returns>
        protected override void CheckPaProfileVersion(XElement profileProcessAutomation)
        {
        }


        protected virtual void F_ParamDescCRC_for_F_WD_Time_2(XElement fparamRecord, Crc16 calcFParamDescCRC)
        {
            // Add F_WD_Time_2, if visible
            var fwdTime2 = fparamRecord.Element(NamespaceGsdDef + Elements.s_FWdTime2);
            if (fwdTime2 != null)
            {
                string strVisible = Help.GetAttributeValueFromXElement(fwdTime2, Attributes.s_Visible);
                bool bVisible = true;    // Default
                if (!string.IsNullOrEmpty(strVisible))
                    bVisible = XmlConvert.ToBoolean(strVisible);
                if (bVisible)
                {
                    // Default value
                    UInt16 dFWDTime2 = 1000;  // Default: "1000" (1000 ms)
                    string strFWDTime2 = Help.GetAttributeValueFromXElement(fwdTime2, Attributes.s_DefaultValue);
                    if (!String.IsNullOrEmpty(strFWDTime2))
                        dFWDTime2 = XmlConvert.ToUInt16(strFWDTime2);

                    // Allowed values
                    uint allowedValuesFrom = 1;
                    uint allowedValuesTo = 65535;
                    bool valid = FindAllowedValues(fwdTime2, out List<ValueListHelper.ValueRangeT> allowedValues);
                    if (valid)
                    {
                        allowedValuesFrom = allowedValues[0].From;
                        allowedValuesTo = allowedValues[allowedValues.Count - 1].To;
                    }


                    byte[] data = new byte[]
                    {
                        (byte)'F',(byte)'_',(byte)'W',(byte)'D',(byte)'_',
                        (byte)'T',(byte)'i',(byte)'m',(byte)'e',(byte)'_',(byte)'2',
                        0x02, 0x00,  // Type=Unsigned16
                        (byte)(dFWDTime2), (byte)(dFWDTime2 >> 8),  // Default value
                        (byte)(allowedValuesFrom), (byte)(allowedValuesFrom >> 8),  // Min value
                        (byte)(allowedValuesTo), (byte)(allowedValuesTo >> 8)  // Max value
                    };

                    // Add the byte array to the CRC
                    calcFParamDescCRC.UpdateChecksum(data);
                }
            }
        }


        protected override void CollectBytesFParamDescCrc(XElement fparamRecord, Crc16 calcFParamDescCrc)
        {
            // F_Check_iPar
            FParamDescCrcForFCheckIPar(fparamRecord, calcFParamDescCrc);

            // F_SIL
            FParamDescCrcForFSil(fparamRecord, calcFParamDescCrc);

            // F_CRC_Length
            FParamDescCrcForFCrcLength(fparamRecord, calcFParamDescCrc);

            // F_CRC_Seed
            FParamDescCrcForFCrcSeed(fparamRecord, calcFParamDescCrc);

            // F_Passivation
            FParamDescCrcForFPassivation(fparamRecord, calcFParamDescCrc);

            // F_Block_ID
            FParamDescCrcForFBlockID(fparamRecord, calcFParamDescCrc);

            // F_Par_Version
            FParamDescCrcForFParVersion(fparamRecord, calcFParamDescCrc);

            // F_Source_Add
            FParamDescCrcForFSourceAdd(fparamRecord, calcFParamDescCrc);

            // F_Dest_Add
            FParamDescCrcForFDestAdd(fparamRecord, calcFParamDescCrc);

            // F_WD_Time
            FParamDescCrcForFWdTime(fparamRecord, calcFParamDescCrc);

            // F_WD_Time
            F_ParamDescCRC_for_F_WD_Time_2(fparamRecord, calcFParamDescCrc);

            // F_iPar_CRC
            FParamDescCrcForFIParCrc(fparamRecord, calcFParamDescCrc);

            // F_Par_CRC
            FParamDescCrcForFParCrc(fparamRecord, calcFParamDescCrc);
        }

        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();
        }


        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("PRUNING");
            tokens1.Add("RejectDCPSet");
            AttributeTokenDictionary.Add("DCP_FeaturesSupported", tokens1);

            IList<string> tokens2 = new List<string>();
            tokens2.Add("SNMPAdjust");
            AttributeTokenDictionary.Add("SNMP_FeaturesSupported", tokens2);

            IList<string> tokens3 = new List<string>();
            tokens3.Add("PowerReal");
            AttributeTokenDictionary.Add("APL_FeaturesSupported", tokens3);

            IList<string> tokens4 = new List<string>();
            tokens4.Add("10");
            tokens4.Add("100");
            tokens4.Add("1000");
            tokens4.Add("2500");
            tokens4.Add("5000");
            tokens4.Add("10000");
            AttributeTokenDictionary.Add("LinkSpeed", tokens4);

            IList<string> tokens5 = new List<string>();
            tokens5.Add("CIM");
            tokens5.Add("SCM");
            tokens5.Add("DCP");
            AttributeTokenDictionary.Add("SupportedRecords", tokens5);

            

            IList<string> tokens9 = new List<string>();
            tokens9.Add("HKDF-SHA2-256");
            AttributeTokenDictionary.Add("Algorithms1", tokens9);

            IList<string> tokens10 = new List<string>();
            tokens10.Add("X25519");
            tokens10.Add("X448");
            AttributeTokenDictionary.Add("Algorithms2", tokens10);

            IList<string> tokens11 = new List<string>();
            tokens11.Add("Ed25519");
            tokens11.Add("Ed448");
            tokens11.Add("P-256");
            tokens11.Add("P-521");
            AttributeTokenDictionary.Add("Algorithms3", tokens11);

            IList<string> tokens12 = new List<string>();
            tokens12.Add("AES-GCM#128");
            tokens12.Add("AES-GCM#256");
            tokens12.Add("ChaCha20-Poly1305");
            AttributeTokenDictionary.Add("AuthnOnly", tokens12);

            IList<string> tokens13 = new List<string>();
            tokens13.Add("AES-GCM#128");
            tokens13.Add("AES-GCM#256");
            tokens13.Add("ChaCha20-Poly1305");
            AttributeTokenDictionary.Add("AuthnEnc", tokens13);
        }

        /// Die Menge der erlaubten/bekannten Attributwerte ist hier von der PNIO_Version abhängig.
        /// 
        /// Zu prüfen sind die Attribute:
        /// 
        /// Ab GSDML V2.1:
        /// - MAUTypes am Portsubmodul
        /// - FiberOpticTypes am Portsubmodul
        /// Ab GSDML V2.25:
        /// - FieldbusType am Modul sowie am Element SlotCluster am Submodul
        /// - SupportedSubstitutionModes am Submodul
        /// Ab GSDML V2.3:
        /// - ResetToFactoryModes am DAP
        /// Ab GSDML V2.4:
        /// - Neue Werte 0x004F - 0x0066 für MAUTypes
        /// Ab GSDML V2.41
        /// - Neue Werte 103..113, 141, 144 für MAUTypes
        /// 
        /// Teilweise sind die zu prüfenden Werte abhängig von der PNIO Version am DAP oder
        /// von der kleinsten PNIO Version an den DAPs, in die das (Port)Submodul gesteckt werden kann.
        /// In diesen Fällen wird das durch eine spezielle Codierung in den betreffenden Wertebereichen
        /// des Attributs gekennzeichnet. Ein '-x.xx' am Anfang des Wertebereichs bedeutet, dass dieser
        /// Wertebereich nur bis einschließlich dieser PNIO Version gültig ist. Ihm folgt ein
        /// 'Attributname-x.xx' Eintrag, der den Wertebereich für PNIO Versionen > x.xx enthält.
        /// 
        /// Nicht auf diese Weise überprüft werden sollen die Attribute:
        /// - API, APStructureIdentifier und ErrorType weil sich Profile schneller ändern können
        ///   als die PN IO Norm / GSDML Spec / der GSDML Checker
        ///   - Die PROFIsafe-Attribute F_IO_StructureDescVersion sowie DefaultValue und AllowedValues
        ///     an F_Block_ID und F_Par_Version, da bei unbekannten Werten an diesen Attributen das
        ///     Engineering Tool und der GSDML Checker nichts mehr damit anfangen können.
        ///     Hier bleibt es bei den vorhandenen harten Checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();
        }

        #endregion


        #region Overrides

        #endregion


        /// <summary>
        /// Check number: CN_0x00043000
        /// PortSubmoduleItem/MAUTypeList/MAUTypeItem: New element APLPortClassification
        /// 
        /// The following checks are implemented:
        /// MAUTypeList/MAUTypeItem/APLPortClassification:
        /// (1)  For devices with one port only:
        ///      If this element exists, the attribute InterfaceSubmoduleItem/@DCP_FeaturesSupported
        ///      shall exist and shall contain the token "PRUNING".
        ///      PowerClass (M, APL_PowerClassEnum, V2.43)
        /// (2)  According to Ethernet-APL Port Profile Specification (ETH-APL-2021),
        ///      other combinations of attributes @PowerClass and @SegmentClass as the following are not allowed:
        ///      PowerClass "A" 	Means up to "15 VDC / 0,54 W" for SegmentClass "Spur"
        ///      PowerClass "C" 	Means up to "15 VDC / 1,11 W" for SegmentClass "Spur"
        ///      PowerClass "3" 	Means up to "50 VDC / 57,5 W" for SegmentClass "Trunk"
        /// (3a) This element shall exist only, if the MAUTypeItem/@Value equals to 10BaseT1L and MAUTypeItem/@Extension
        ///      equals to APL (see IEC 61158-6-10:2022) => MAUType = 141 with MAUTypeExtension = 512,
        /// (3b) otherwise this element shall not exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043000()
        {
            // Over all APLPortClassification:
            var aplPortClassifications = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_AplPortClassification);
            aplPortClassifications = Help.TryRemoveXElementsUnderXsAny(aplPortClassifications, Nsmgr, Gsd);
            foreach (var aplPortClassification in aplPortClassifications)
            {
                // (1)
                if (aplPortClassification.Parent != null)
                {
                    var portSubmoduleItem = aplPortClassification.Parent.Parent.Parent;
                    CreateReport0x00043000_1(portSubmoduleItem);
                }

                // (2)
                Createreport0x00043000_2(aplPortClassification);

                // (3a)
                CreateReport0x00043000_3(aplPortClassification);
            }

            // (3b)
            CreateReport0x00043000_4();

            return true;
        }

        private void CreateReport0x00043000_4()
        {
            // Over all MAUTypeItem:
            var mauTypeItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem);
            mauTypeItems = Help.TryRemoveXElementsUnderXsAny(mauTypeItems, Nsmgr, Gsd);
            foreach (var mauTypeItem in mauTypeItems)
            {
                UInt16 mauType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value));
                var extensionStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension);
                if (string.IsNullOrEmpty(extensionStr))
                    continue;
                UInt16 extension = XmlConvert.ToUInt16(extensionStr);
                if (mauType != 141
                    && extension != 512)
                {
                    continue;
                }

                var aplPortClassification = mauTypeItem.Element(NamespaceGsdDef + Elements.s_AplPortClassification);
                if (aplPortClassification != null)
                {
                    continue;
                }

                // "If 'MAUTypeItem/@Value' equals to 10BaseT1L = "141" and MAUTypeItem/@Extension equals to APL = "512",
                //  the element 'PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification' must exist, too."
                string msg = Help.GetMessageString("M_0x00043000_4");
                string xpath = Help.GetXPath(mauTypeItem);
                IXmlLineInfo xli = mauTypeItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00043000_4");
            }
        }

        private void CreateReport0x00043000_3(XObject aplPortClassification)
        {
            var mauTypeItem = aplPortClassification.Parent;
            UInt16 mauType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value));
            UInt16 extension = 0;
            var extensionStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension);
            if (!string.IsNullOrEmpty(extensionStr))
                extension = XmlConvert.ToUInt16(extensionStr);
            if (mauType == 141
                && extension == 512)
            {
                return;
            }

            // "The element 'PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification' must exist only,
            //  if 'MAUTypeItem/@Value' equals to 10BaseT1L = "141" and MAUTypeItem/@Extension
            //  equals to APL = "512"."
            string msg = Help.GetMessageString("M_0x00043000_3");
            string xpath = Help.GetXPath(mauTypeItem);
            IXmlLineInfo xli = mauTypeItem;
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00043000_3");
            }
        }

        private void Createreport0x00043000_2(XElement aplPortClassification)
        {
            string powerClass = Help.GetAttributeValueFromXElement(aplPortClassification, Attributes.s_PowerClass);
            string segmentClass = Help.GetAttributeValueFromXElement(aplPortClassification, Attributes.s_SegmentClass);
            string portClass = Help.GetAttributeValueFromXElement(aplPortClassification, Attributes.s_PortClass);
            if ((powerClass == "A" || powerClass == "C" || segmentClass != "Spur")
                && (powerClass == "3" || segmentClass != "Trunk")
                && (portClass != "PowerCascade" || segmentClass != "Spur"))
            {
                return;
            }

            // "According to Ethernet-APL Port Profile Specification (ETH-APL-2021),
            //  the combination of 'APLPortClassification/@SegmentClass' = "{0}", 'APLPortClassification/@PortClass' = "{1}" and 'APLPortClassification/@PowerClass' = "{2}" is not allowed."
            string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00043000_2"), segmentClass, portClass, powerClass);
            string xpath = Help.GetXPath(aplPortClassification);
            IXmlLineInfo xli = aplPortClassification;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00043000_2");
        }

        private void CreateReport0x00043000_1(XElement portSubmoduleItem)
        {
            if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
            {
                return;
            }

            foreach (XElement dap in dapsOfPort)
            {
                DapToPortDictionary.TryGetValue(dap, out IList<XElement> portsOfDap);
                if (portsOfDap.Count() > 1)
                    continue;
                var interfaceSubmoduleItem =
                    dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();

                string strDcpFeaturesSupported = Help.GetAttributeValueFromXElement(
                    interfaceSubmoduleItem,
                    Attributes.s_DcpFeaturesSupported);
                IList<string> dcpFeaturesSupported = strDcpFeaturesSupported.Split(Constants.s_Semicolon.ToCharArray());
                if (dcpFeaturesSupported.Contains("PRUNING"))
                {
                    continue;
                }

                // "When 'PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification' exists,
                //  the attribute 'InterfaceSubmoduleItem/@DCP_FeaturesSupported' must exist and must contain the token "PRUNING"."
                string msg = Help.GetMessageString("M_0x00043000_1");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                IXmlLineInfo xli = interfaceSubmoduleItem;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043000_1");
                }
            }
        }
        /// <summary>
        /// Check number: CN_0x00043001
        /// (Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem: New element F_WD_TIME_2
        /// 
        /// The following checks are implemented:
        /// (Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem/F_WD_TIME_2:
        /// This element shall exist if '@PROFIsafePIR_Supported' is present and "true" for this submodule,
        /// otherwise this element shall not exist.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043001()
        {
            // Find all submodules with PROFIsafePIR_Supported = "true"
            var allSubmodules =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                            x.Name.LocalName == Elements.s_SubmoduleItem);
            allSubmodules = Help.TryRemoveXElementsUnderXsAny(allSubmodules, Nsmgr, Gsd);
            foreach (var submodule in allSubmodules)
            {
                string strPROFIsafePIRSupported = Help.GetAttributeValueFromXElement(submodule, Attributes.s_ProfIsafePirSupported);
                bool pROFIsafePIRSupported = false;
                if (!string.IsNullOrEmpty(strPROFIsafePIRSupported))
                    pROFIsafePIRSupported = XmlConvert.ToBoolean(strPROFIsafePIRSupported);
                XElement fwdtime2 = null;
                XElement fParameterRecordDataItem = null;
                XElement recordDataList = submodule.Element(NamespaceGsdDef + Elements.s_RecordDataList);
                if (recordDataList != null)
                    fParameterRecordDataItem = recordDataList.Element(NamespaceGsdDef + Elements.s_FParameterRecordDataItem);
                if (fParameterRecordDataItem != null)
                    fwdtime2 = fParameterRecordDataItem.Element(NamespaceGsdDef + Elements.s_FWdTime2);
                if (fwdtime2 == null && pROFIsafePIRSupported)
                {
                    if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                    {
                        // "The element '(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem/F_WD_TIME_2' shall exist if
                        //  '@PROFIsafePIR_Supported' is present and "true" for this submodule."
                        string msg = Help.GetMessageString("M_0x00043001_1");
                        string xpath = Help.GetXPath(submodule);
                        IXmlLineInfo xli = submodule;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00043001_1");
                    }
                }
                if (fwdtime2 != null && !pROFIsafePIRSupported)
                {
                    if (Help.CheckSchemaVersion(submodule, SupportedGsdmlVersion))
                    {
                        // "The element '(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem/F_WD_TIME_2' shall not exist if
                        //  '@PROFIsafePIR_Supported' is not present or not "true" for this submodule."
                        string msg = Help.GetMessageString("M_0x00043001_2");
                        string xpath = Help.GetXPath(submodule);
                        IXmlLineInfo xli = submodule;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00043001_2");
                    }
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00043002
        /// New elements under CommunicationInterfaceList/CommunicationInterfaceItem
        /// 
        /// The following checks are implemented:
        /// CommunicationInterfaceItem/CIM_SupportedRecords:
        /// (1) If this element exists, the element 'CommunicationInterfaceItem/CIM_Interface'
        ///     must exist, too.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043002()
        {
            // Over all CommunicationInterfaceItem:
            var cimItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceItem)
                                   .Where(x => x.Descendants(NamespaceGsdDef + Elements.s_CimSupportedRecords).FirstOrDefault() != null);
            cimItems = Help.TryRemoveXElementsUnderXsAny(cimItems, Nsmgr, Gsd);
            foreach (var cimItem in cimItems)
            {
                var cimIntfc = cimItem.Descendants(NamespaceGsdDef + Elements.s_CimInterface).FirstOrDefault();
                if (cimIntfc == null)
                {
                    // "When 'CommunicationInterfaceItem/CIM_SupportedRecords' exists,
                    //  the element 'CommunicationInterfaceItem/CIM_Interface' must exist, too."
                    string msg = Help.GetMessageString("M_0x00043002_1");
                    string xpath = Help.GetXPath(cimItem);
                    IXmlLineInfo xli = cimItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00043002_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00043003
        /// New element DeviceAccessPointItem/CommunicationInterfaceModule
        /// 
        /// The following must be checked:
        /// (1) If PNIO_Version >= "V2.43" and if the DAP describes a ConformanceClass "D" device,
        ///     the element 'DeviceAccessPointItem/CommunicationInterfaceModule' must be present.
        /// (2) If the DAP describes a SecurityClass "2" or "3" device,
        ///     the element 'DeviceAccessPointItem/CommunicationInterfaceModule' must be present.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043003()
        {
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (pnioVersion < 2.43)
                    continue;

                var communicationInterfaceModule = dap.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceModule).FirstOrDefault();
                if (communicationInterfaceModule != null)
                    continue;   // CommunicationInterfaceModule available => nothing to check

                // (1)
                var certificationInfoExtList = dap.Descendants(NamespaceGsdDef + Elements.CertificationInfoExt);
                foreach (var certificationInfoExt in certificationInfoExtList)
                {
                    string conformanceClass = Help.GetAttributeValueFromXElement(certificationInfoExt, Attributes.s_ConformanceClass);
                    if (0 == string.Compare(conformanceClass, Constants.s_CharacterD, StringComparison.InvariantCulture)) // conformanceClass "D" found?
                    {
                        // "If PNIO_Version >= "V2.43" and if the DAP describes a ConformanceClass "D" device,
                        //  the element 'DeviceAccessPointItem/CommunicationInterfaceModule' must be present."
                        string msg = Help.GetMessageString("M_0x00043003_1");
                        string xpath = Help.GetXPath(dap);
                        IXmlLineInfo xli = dap;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "0x00043003_1");
                        break;
                    }
                }

                // (2)
                var certificationInfo = dap.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo).FirstOrDefault();
                string securityClass = Help.GetAttributeValueFromXElement(certificationInfo, Attributes.s_SecurityClass);
                var securityClasses = new List<string>(securityClass.Split(Constants.s_Semicolon.ToCharArray()));
                if (securityClasses.Contains("2") || securityClasses.Contains("3"))
                {
                    // "If the DAP describes a SecurityClass "2" or "3" device,
                    //  the element 'DeviceAccessPointItem/CommunicationInterfaceModule' must be present."
                    string msg = Help.GetMessageString("M_0x00043003_2");
                    string xpath = Help.GetXPath(dap);
                    IXmlLineInfo xli = dap;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00043003_2");
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00043004
        /// New elements under CommunicationInterfaceList/CommunicationInterfaceItem/Protection
        /// 
        /// The following check is implemented:
        /// Elements StreamProtection, AlarmProtection, ConnectionManagementProtection: 
        /// If the element is present, then one of the attributes @AuthnOnly or @AuthnEnc must be present.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043004()
        {
            // Find all submodules with PROFIsafePIR_Supported = "true"
            var allElements =
                GsdProfileBody.Descendants()
                    .Where(
                        x =>
                            x.Name.LocalName == Elements.s_StreamProtection ||
                            x.Name.LocalName == Elements.s_AlarmProtection ||
                            x.Name.LocalName == Elements.s_ConnectionManagementProtection);
            allElements = Help.TryRemoveXElementsUnderXsAny(allElements, Nsmgr, Gsd);
            foreach (var el in allElements)
            {
                var authnOnly = el.Attribute(Attributes.s_AuthnOnly);
                var authnEnc = el.Attribute(Attributes.s_AuthnEnc);
                if (authnOnly == null && authnEnc == null)
                {
                    // "If the element '{0}' is present, then one of the attributes '@AuthnOnly' or '@AuthnEnc' must be present."
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00043004_1"), el.Name.LocalName);
                    string xpath = Help.GetXPath(el);
                    IXmlLineInfo xli = el;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00043004_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00043005
        /// For PNIO_Version >= "V2.43":
        /// (1) The attribute 'InterfaceSubmoduleItem/TimingProperties/@SendClock' shall contain
        ///     the value 32 if all ports only support a link speed less than 100 Mbits/s.
        /// (2) The attribute 'DeviceAccessPointItem/@MinDeviceInterval' shall be >= 256 (8 ms)
        ///     if all ports only support a link speed less than 100 Mbits/s.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043005()
        {
            // Find all 'DeviceAccessPointItem' elements (required).
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);

            foreach (var dap in nl)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                if (pnioVersion < 2.43)
                    continue;

                // (1.1)
                // Get 'SendClock' attribute of 'TimingProperties' - Default = "32" (1 ms)
                var sendClock =
                    dap.Descendants(NamespaceGsdDef + Elements.s_TimingProperties).Attributes(Attributes.s_SendClock).FirstOrDefault();
                bool isValue32Contained = true; // The default value is "32"
                if (null != sendClock)
                {
                    List<ValueListHelper.ValueRangeT> listSendClocks = ValueListHelper.NormalizeValueList(sendClock, Store);
                    isValue32Contained = ValueListHelper.IsValueInValueList(32, listSendClocks);
                }

                // (2.1)
                // Get 'MinDeviceInterval' attribute of 'DeviceAccessPointItem' - Must
                var strMinDeviceInterval =
                    Help.GetAttributeValueFromXElement(dap, Attributes.s_MinDeviceInterval);
                UInt16 minDeviceInterval = XmlConvert.ToUInt16(strMinDeviceInterval);

                if (isValue32Contained && minDeviceInterval >= 256)
                    continue;

                // Get all ports, fixed and plugable, for the dap
                bool doesPortsOnlySupportLinkSpeedLess100Mbits = DoesAllPortsOfDapOnlySupportLinkSpeedLess100Mbits(dap);

                

                // (1.2)
                if (doesPortsOnlySupportLinkSpeedLess100Mbits && !isValue32Contained)
                {
                    // "The 'InterfaceSubmoduleItem/TimingProperties/@SendClock' attribute must contain
                    //  the value 32 if all ports only support a link speed less than 100 Mbits/s."
                    string msg = Help.GetMessageString("M_0x00043005_1");
                    string xpath = Help.GetXPath(sendClock);
                    var xli = (IXmlLineInfo)sendClock;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_MinorError, xli.LineNumber,
                        xli.LinePosition, msg, xpath,
                        ReportCategories.TypeSpecific, "0x00043005_1");
                }

                // (2.2)
                if (doesPortsOnlySupportLinkSpeedLess100Mbits && minDeviceInterval < 256)
                {
                    // "The attribute 'DeviceAccessPointItem/@MinDeviceInterval' shall be >= 256 (8 ms)
                    //  if all ports only support a link speed less than 100 Mbits/s."
                    string msg = Help.GetMessageString("M_0x00043005_2");
                    string xpath = Help.GetXPath(sendClock);
                    var xli = (IXmlLineInfo)sendClock;
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_MinorError,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043005_2");
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Check number: CN_0x00043006
        ///
        /// Checks if the SecurityClass attribute has a valid token combination
        /// (1) Security Class 2 requires existence of Security Class 1.
        /// (2) Security Class 3 requires existence of Security Class 2,
        /// 
        /// <returns>True, if no runtime problem occurred.</returns>
        /// </summary>
        protected virtual bool CheckCN_0x00043006()
        {
            // Get all SecurityClass attributes from CertificationInfo elements in the GsdProfileBody
            var securityClassAttributes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo)
                .Attributes(Attributes.s_SecurityClass)
                .Select(attr => Help.TryRemoveXAttributesUnderXsAny(new[] { attr }, Nsmgr, Gsd).FirstOrDefault())
                .Where(attr => attr != null && !string.IsNullOrEmpty(attr.Value)).ToList();

            // Iterate over each SecurityClass attribute
            foreach (var securityClassAttribute in securityClassAttributes)
            {
                string attrValue = securityClassAttribute.Value;

                // (1)
                if (attrValue.Contains('2')
                    && !attrValue.Contains('1'))
                {
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00043006_1"));
                    string xpath = Help.GetXPath(securityClassAttribute);
                    var xli = (IXmlLineInfo)securityClassAttribute;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043006_1");
                }

                // (2)
                if (attrValue.Contains('3')
                    && !attrValue.Contains('2'))
                {
                    string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00043006_2"));
                    string xpath = Help.GetXPath(securityClassAttribute);
                    var xli = (IXmlLineInfo)securityClassAttribute;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043006_2");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00043007
        ///
        /// If the DAP describes a SecurityClass "1" device
        /// (1) The attribute InterfaceSubmoduleItem/@DCP_FeaturesSupported must exist and must contain the token "RejectDCPSet"
        /// (2) The attribute InterfaceSubmoduleItem/@SNMP_FeaturesSupported must exist and must contain the token "SNMPAdjust"
        /// (3) If the DAP has a reference to a CommunicationInterfaceItem, then the attribute CommunicationInterfaceItem/CIM_SupportedFeatures/@SNMP_FeaturesSupported must exist.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043007()
        {
            // Get all SecurityClass attributes from CertificationInfo elements in the GsdProfileBody
            var securityClassAttributes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo)
                .Attributes(Attributes.s_SecurityClass)
                .Select(attr => Help.TryRemoveXAttributesUnderXsAny(new[] { attr }, Nsmgr, Gsd).FirstOrDefault())
                .Where(attr => attr != null && attr.Value.Contains('1')).ToList();

            foreach (var securityClassAttribute in securityClassAttributes)
            {
                // Get the DAP
                var dap = securityClassAttribute.Parent?.Parent;

                // Get the InterfaceSubmoduleItem
                var interfaceSubmoduleItem =
                    dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();

                if (interfaceSubmoduleItem != null)
                {
                    // (1)
                    string dcpFeatureSupported = Help.GetAttributeValueFromXElement(
                        interfaceSubmoduleItem,
                        Attributes.s_DcpFeaturesSupported);

                    if (string.IsNullOrEmpty(dcpFeatureSupported)
                        || !dcpFeatureSupported.Contains("RejectDCPSet"))
                    {
                        // The attribute InterfaceSubmoduleItem/@DCP_FeaturesSupported must exist and must contain the token "RejectDCPSet"
                        string msg = Help.GetMessageString("M_0x00043007_1");
                        string xpath = Help.GetXPath(interfaceSubmoduleItem);
                        var xli = (IXmlLineInfo)interfaceSubmoduleItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043007_1");
                    }

                    // (2)
                    string snmpFeatureSupported = Help.GetAttributeValueFromXElement(
                        interfaceSubmoduleItem,
                        Attributes.s_SnmpFeaturesSupported);

                    if (string.IsNullOrEmpty(snmpFeatureSupported)
                        || !snmpFeatureSupported.Contains("SNMPAdjust"))
                    {
                        // The attribute InterfaceSubmoduleItem/@SNMP_FeaturesSupported must exist and must contain the token "SNMPAdjust"
                        string msg = Help.GetMessageString("M_0x00043007_2");
                        string xpath = Help.GetXPath(interfaceSubmoduleItem);
                        var xli = (IXmlLineInfo)interfaceSubmoduleItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043007_2");
                    }
                }



                // (3)
                CheckIfSnmpFeaturesSupportedExists(dap);
            }

            return true;
        }

        private void CheckIfSnmpFeaturesSupportedExists(XElement dap)
        {
            var cimTarget = dap.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceModule)
                .Attributes(Attributes.s_CimTarget).FirstOrDefault();

            if (cimTarget == null)
            {
                return;
            }

            var allCommunicationInterfaceList =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceList);

            foreach (var communicationInterfaceList in allCommunicationInterfaceList)
            {
                var communicationInterfaceItem = communicationInterfaceList
                    .Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceItem).FirstOrDefault(
                        item => Help.GetAttributeValueFromXElement(item, Attributes.ID) == cimTarget.Value);

                if (communicationInterfaceItem == null)
                {
                    continue;
                }

                var snmpFeaturesSupported = communicationInterfaceItem
                    .Descendants(NamespaceGsdDef + Elements.s_CimSupportedFeatures)
                    .Attributes(Attributes.s_SnmpFeaturesSupported);

                if (!snmpFeaturesSupported.Any())
                {
                    // If the DAP has a reference to a CommunicationInterfaceItem, then CommunicationInterfaceItem/CIM_SupportedFeatures/@SNMP_FeaturesSupported must be present.
                    string msg = Help.GetMessageString("M_0x00043007_3");
                    string xpath = Help.GetXPath(communicationInterfaceItem);
                    var xli = (IXmlLineInfo)communicationInterfaceItem;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043007_3");
                }
            }
        }

        /// <summary>
        /// Check number: CN_0x00043008
        ///
        /// If the DAP describes a SecurityClass "2" device
        /// (1) The element CommunicationInterfaceItem/CIM_Interface must exist.
        /// (2) The element CommunicationInterfaceItem/Protection must exist.
        /// (3) The attribute CommunicationInterfaceItem/CIM_SupportedRecords/@SupportedRecords must exist and must contain the token "SCM".
        /// (4) The attribute CommunicationInterfaceItem/Protection/StreamProtection/@AuthnOnly must exist.
        /// (5) The attribute CommunicationInterfaceItem/Protection/AlarmProtection/@AuthnOnly must exist.
        /// (6) The attribute CommunicationInterfaceItem/Protection/ConnectionManagementProtection/@AuthnOnly must exist.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043008()
        {
            // Get all SecurityClass attributes from CertificationInfo elements in the GsdProfileBody
            var securityClassAttributes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo)
                .Attributes(Attributes.s_SecurityClass)
                .Select(attr => Help.TryRemoveXAttributesUnderXsAny(new[] { attr }, Nsmgr, Gsd).FirstOrDefault())
                .Where(attr => attr != null && attr.Value.Contains('2')).ToList();

            foreach (var securityClassAttribute in securityClassAttributes)
            {
                // Get the DAP
                var dap = securityClassAttribute.Parent?.Parent;

                var cimTarget = dap.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceModule)
                    .Attributes(Attributes.s_CimTarget).FirstOrDefault();

                if (cimTarget == null)
                {
                    continue;
                }

                var allCommunicationInterfaceList =
                    GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceList);

                foreach (var communicationInterfaceList in allCommunicationInterfaceList)
                {
                    var communicationInterfaceItem = communicationInterfaceList
                        .Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceItem).FirstOrDefault(
                            item => Help.GetAttributeValueFromXElement(item, Attributes.ID) == cimTarget.Value);

                    if (communicationInterfaceItem == null)
                    {
                        continue;
                    }

                    // (1)
                    CheckElementExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_CimInterface, "M_0x00043008_1", "0x00043008_1");
                    // (2)
                    CheckElementExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_Protection, "M_0x00043008_2", "0x00043008_2");
                    // (3)
                    CheckAttributeExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_CimSupportedRecords, Attributes.s_SupportedRecords, "SCM", "M_0x00043008_3", "0x00043008_3");
                    // (4)
                    CheckAttributeExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_StreamProtection, Attributes.s_AuthnOnly, null, "M_0x00043008_4", "0x00043008_4");
                    // (5)
                    CheckAttributeExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_AlarmProtection, Attributes.s_AuthnOnly, null, "M_0x00043008_5", "0x00043008_5");
                    // (6)
                    CheckAttributeExists(communicationInterfaceItem, NamespaceGsdDef + Elements.s_ConnectionManagementProtection, Attributes.s_AuthnOnly, null, "M_0x00043008_6", "0x00043008_6");
                }
            }

            return true;
        }

        private void CheckElementExists(XElement communicationInterfaceItem, XName elementName, string messageKey, string reportKey)
        {
            var element = communicationInterfaceItem.Descendants(elementName);

            if (element.Any())
            {
                return;
            }

            string msg = Help.GetMessageString(messageKey);
            string xpath = Help.GetXPath(communicationInterfaceItem);
            var xli = (IXmlLineInfo)communicationInterfaceItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                reportKey);
        }

        private void CheckAttributeExists(XElement communicationInterfaceItem, XName elementName, string attributeName, string expectedValue, string messageKey, string reportKey)
        {
            var attribute = communicationInterfaceItem
                .Descendants(elementName)
                .Attributes(attributeName)
                .Select(
                    attr => Help.TryRemoveXAttributesUnderXsAny(new[] { attr }, Nsmgr, Gsd).FirstOrDefault())
                .Where(attr => attr != null && (expectedValue == null || attr.Value.Contains(expectedValue))).ToList();

            if (attribute.Any())
            {
                return;
            }

            string msg = Help.GetMessageString(messageKey);
            string xpath = Help.GetXPath(communicationInterfaceItem);
            var xli = (IXmlLineInfo)communicationInterfaceItem;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                reportKey);
        }

        /// <summary>
        /// Check number: CN_0x00043009
        ///
        /// If the DAP describes a SecurityClass "3" device
        /// (1) The attribute CommunicationInterfaceItem/Protection/StreamProtection/@AuthnEnc must exist.
        /// (2) The attribute CommunicationInterfaceItem/Protection/AlarmProtection/@AuthnEnc must exist.
        /// (3) The attribute CommunicationInterfaceItem/Protection/ConnectionManagementProtection/@AuthnEnc must exist.
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043009()
        {
            // Get all SecurityClass attributes from CertificationInfo elements in the GsdProfileBody
            var securityClassAttributes = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo)
                .Attributes(Attributes.s_SecurityClass)
                .Select(attr => Help.TryRemoveXAttributesUnderXsAny(new[] { attr }, Nsmgr, Gsd).FirstOrDefault())
                .Where(attr => attr != null && attr.Value.Contains('3')).ToList();

            foreach (var securityClassAttribute in securityClassAttributes)
            {
                // Get the DAP
                var dap = securityClassAttribute.Parent?.Parent;

                var cimTarget = dap.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceModule)
                    .Attributes(Attributes.s_CimTarget).FirstOrDefault();

                if (cimTarget == null)
                {
                    continue;
                }

                var allCommunicationInterfaceList =
                    GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceList);

                foreach (var communicationInterfaceList in allCommunicationInterfaceList)
                {
                    var communicationInterfaceItem = communicationInterfaceList
                        .Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceItem).FirstOrDefault(
                            item => Help.GetAttributeValueFromXElement(item, Attributes.ID) == cimTarget.Value);

                    if (communicationInterfaceItem == null)
                    {
                        continue;
                    }

                    var protection = communicationInterfaceItem.Descendants(NamespaceGsdDef + Elements.s_Protection);

                    // (1)
                    var streamProtectionAuthnEnc = protection.Descendants(NamespaceGsdDef + Elements.s_StreamProtection)
                        .Attributes(Attributes.s_AuthnEnc);

                    if (!streamProtectionAuthnEnc.Any())
                    {
                        string msg = Help.GetMessageString("M_0x00043009_1");
                        string xpath = Help.GetXPath(communicationInterfaceItem);
                        var xli = (IXmlLineInfo)communicationInterfaceItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043009_1");
                    }

                    // (2)
                    var alarmProtectionAuthnEnc = protection.Descendants(NamespaceGsdDef + Elements.s_AlarmProtection)
                        .Attributes(Attributes.s_AuthnEnc);

                    if (!alarmProtectionAuthnEnc.Any())
                    {
                        string msg = Help.GetMessageString("M_0x00043009_2");
                        string xpath = Help.GetXPath(communicationInterfaceItem);
                        var xli = (IXmlLineInfo)communicationInterfaceItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043009_2");
                    }

                    // (3)
                    var connectionManagementProtectionAuthnEnc = protection
                        .Descendants(NamespaceGsdDef + Elements.s_ConnectionManagementProtection)
                        .Attributes(Attributes.s_AuthnEnc);

                    if (!connectionManagementProtectionAuthnEnc.Any())
                    {
                        string msg = Help.GetMessageString("M_0x00043009_3");
                        string xpath = Help.GetXPath(communicationInterfaceItem);
                        var xli = (IXmlLineInfo)communicationInterfaceItem;
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00043009_3");
                    }


                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00043010
        ///
        /// Mandatory checks for CommunicationInterfaceItem/Protection
        /// (1) The value "HKDF-SHA2-256" is mandatory for the attribute Protection/KeyDerivation/@Algorithms.
        /// (2) The value "X25519" is mandatory for the attribute Protection/KeyAgreement/@Algorithms.
        /// (3) The values "Ed25519", "P-256" and "P-521" are mandatory for the attribute Protection/DigitalSignature/@Algorithms.
        /// (4) The value "AES-GCM#256" is mandatory for the attribute @AuthnOnly.
        /// (5) The value "AES-GCM#256" is mandatory for the attribute @AuthnEnc.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00043010()
        {
            var allProtectionElements = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceList)
                .Descendants(NamespaceGsdDef + Elements.s_CommunicationInterfaceItem)
                .Descendants(NamespaceGsdDef + Elements.s_Protection);

            foreach (var protection in allProtectionElements)
            {
                // (1)
                var keyDerivationAlgorithms = protection.Descendants(NamespaceGsdDef + Elements.s_KeyDerivation)
                    .Attributes(Attributes.s_Algorithms)
                    .Where(val => !val.Value.Contains(Enums.Algorithms1_HKDF_SHA2_256));

                foreach (var val in keyDerivationAlgorithms)
                {
                    string msg = Help.GetMessageString("M_0x00043010_1");
                    string xpath = Help.GetXPath(val);
                    var xli = (IXmlLineInfo)val;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043010_1");
                }

                // (2)
                var keyAgreementAlgorithms = protection.Descendants(NamespaceGsdDef + Elements.s_KeyAgreement)
                    .Attributes(Attributes.s_Algorithms)
                    .Where(val => !val.Value.Contains(Enums.Algorithms2_X25519));

                foreach (var val in keyAgreementAlgorithms)
                {
                    string msg = Help.GetMessageString("M_0x00043010_2");
                    string xpath = Help.GetXPath(val);
                    var xli = (IXmlLineInfo)val;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043010_2");
                }

                // (3)
                var digitalSignatureAlgorithms = protection.Descendants(NamespaceGsdDef + Elements.s_DigitalSignature)
                    .Attributes(Attributes.s_Algorithms)
                    .Where(val => !val.Value.Contains(Enums.Algorithms3_Ed25519))
                    .Where(val => !val.Value.Contains(Enums.Algorithms3_P_256))
                    .Where(val => !val.Value.Contains(Enums.Algorithms3_P_521));

                foreach (var val in digitalSignatureAlgorithms)
                {
                    string msg = Help.GetMessageString("M_0x00043010_3");
                    string xpath = Help.GetXPath(val);
                    var xli = (IXmlLineInfo)val;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043010_3");
                }

                // (4)
                var authnOnlyAttributes = protection.Descendants().Attributes(Attributes.s_AuthnOnly)
                    .Where(val => !val.Value.Contains(Enums.AuthnXXX_AES_GCM256));

                foreach (var val in authnOnlyAttributes)
                {
                    string msg = Help.GetMessageString("M_0x00043010_4");
                    string xpath = Help.GetXPath(val);
                    var xli = (IXmlLineInfo)val;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043010_4");
                }

                // (5)
                var authnEncAttributes = protection.Descendants().Attributes(Attributes.s_AuthnEnc)
                    .Where(val => !val.Value.Contains(Enums.AuthnXXX_AES_GCM256));

                foreach (var val in authnEncAttributes)
                {
                    string msg = Help.GetMessageString("M_0x00043010_5");
                    string xpath = Help.GetXPath(val);
                    var xli = (IXmlLineInfo)val;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00043010_5");
                }
            }

            return true;
        }



        #endregion
    }
}
