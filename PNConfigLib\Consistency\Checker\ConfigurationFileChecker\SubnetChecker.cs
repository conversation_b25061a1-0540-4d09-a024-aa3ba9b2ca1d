/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: SubnetChecker.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.HWCNBL.Constants;

using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class SubnetChecker : IConsistencyChecker
    {

        private List<Subnet> m_Subnets;

        private Devices m_Devices;

        internal SubnetChecker(List<Subnet> subnets, Devices devices)
        {
            m_Subnets = subnets;
            m_Devices = devices;
        }

        public void Check()
        {
            AreSubnetMasksOfCentralAndDecentralDeviceTheSame();
            CheckDefaultSubnet();
        }

        private void AreSubnetMasksOfCentralAndDecentralDeviceTheSame()
        {
            foreach (CentralDeviceType centralDevice in m_Devices.CentralDevice)
            {
                string centralDeviceIOSystemRefID = string.Empty;
                if (!string.IsNullOrEmpty(centralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID))
                {
                    centralDeviceIOSystemRefID = centralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID;
                }

                foreach (ConfigReader.Configuration.DecentralDeviceType decentralDevice in m_Devices.DecentralDevice)
                {
                    string decentralDeviceIOSystemRefID = string.Empty;
                    if (!string.IsNullOrEmpty(decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID))
                    {
                        decentralDeviceIOSystemRefID = decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID;
                    }

                    if (centralDeviceIOSystemRefID != decentralDeviceIOSystemRefID)
                    {
                        continue;
                    }

                    string centralSubnetMask = GetCentralDeviceSubnetMask(centralDevice);
                    string decentralSubnetMask = GetDecentralDeviceSubnetMask(decentralDevice);

                    if (string.IsNullOrEmpty(centralSubnetMask) || string.IsNullOrEmpty(decentralSubnetMask))
                    {
                        // one of them is missing. nothing to check on this pair
                        continue;
                    }

                    if (centralSubnetMask != decentralSubnetMask)
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.SubnetMaskDifferenceInIOSystem,
                            decentralSubnetMask,
                            decentralDevice.DeviceRefID,
                            centralSubnetMask,
                            centralDevice.DeviceRefID,
                            centralDeviceIOSystemRefID);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private static string GetCentralDeviceSubnetMask(CentralDeviceType centralDevice)
        {
            var ipProtocol = centralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol;
            if (ipProtocol != null
                && ipProtocol.Item.GetType() == typeof(CentralIPProtocolTypeSetInTheProject))
            {
                var setIpAddressInProject = ipProtocol.Item as CentralIPProtocolTypeSetInTheProject;
                if (setIpAddressInProject == null)
                {
                    return string.Empty;
                }

                string centralDeviceSubnetMask = setIpAddressInProject.SubnetMask;
                return centralDeviceSubnetMask;
            }

            return string.Empty;
        }

        private static string GetDecentralDeviceSubnetMask(DecentralDeviceType decentralDevice)
        {
            var ipProtocol = decentralDevice.DecentralDeviceInterface.EthernetAddresses.IPProtocol;
            if (ipProtocol != null && ipProtocol.Item.GetType() == typeof(DecentralIPProtocolTypeSetInTheProject))
            {
                var setIpAddressInProject = ipProtocol.Item as DecentralIPProtocolTypeSetInTheProject;
                if (setIpAddressInProject == null)
                {
                    return string.Empty;
                }

                string decentralDeviceSubnetMask = setIpAddressInProject.SubnetMask;
                return decentralDeviceSubnetMask;
            }

            return string.Empty;
        }

        internal void CheckDefaultSubnet()
        {
            bool defaultSubnetNeeded = true;
            bool defaultIoSystemNeeded = true;
            bool defaultSyncDomainNeeded = true;

            CheckDefaultSettingsForCentralDevice(ref defaultSubnetNeeded, ref defaultIoSystemNeeded, ref defaultSyncDomainNeeded);
            CheckDefaultSettingsForDecentralDevice(ref defaultSubnetNeeded, ref defaultIoSystemNeeded, ref defaultSyncDomainNeeded);

            if (defaultSubnetNeeded)
            {
                CheckDeviceConnectedToAnySubnet(defaultSyncDomainNeeded, defaultIoSystemNeeded);

                CheckSyncDomainDefinedWithoutSubnet(defaultSyncDomainNeeded);

                CheckIOSystemDefinedWithoutSubnet(defaultIoSystemNeeded);

                CheckMultipleIOCExist();

                SetSubnetIOSystemSyncDomain();
            }
        }

        private void CheckDeviceConnectedToAnySubnet(bool defaultSyncDomainNeeded, bool defaultIoSystemNeeded)
        {
            if (m_Subnets.Count > 0)
            {
                CheckIOSystemAndSyncDomainDefinedWithoutSubnet(defaultSyncDomainNeeded, defaultIoSystemNeeded);
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_EmptyProject);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckIOSystemAndSyncDomainDefinedWithoutSubnet(bool defaultSyncDomainNeeded, bool defaultIoSystemNeeded)
        {
            // No subnets given, but there are subnets defined.
            if (!defaultSyncDomainNeeded
                || !defaultIoSystemNeeded)
            {
                // There are IOSystems and SyncDomains specified without a subnet. This is not supported for the time being,
                // but could be supported in the future to automatically set the subnet the IOSystem or SyncDomain belong to.
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_WithoutSubnet);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckSyncDomainDefinedWithoutSubnet(bool defaultSyncDomainNeeded)
        {
            // No subnets are defined. Create a default subnet, IOSystem and SyncDomain;
            // and use them in all devices.
            if (!defaultSyncDomainNeeded)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SyncDomainRefIDWithoutSubnet);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckIOSystemDefinedWithoutSubnet(bool defaultIoSystemNeeded)
        {
            if (!defaultIoSystemNeeded)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IOSystemRefIDWithoutSubnet);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckMultipleIOCExist()
        {
            if (m_Devices.CentralDevice.Count > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_DefaultIOSystemForMultipleIOC);
                throw new ConsistencyCheckException();
            }
        }

        private void SetSubnetIOSystemSyncDomain()
        {
            Subnet subnet = CreateDefaultSubnet();
            m_Subnets.Add(subnet);

            SubnetIOSystem ioSystem = CreateDefaultIoSystem();
            subnet.IOSystem.Add(ioSystem);

            SyncDomainType syncDomain = CreateDefaultSyncDomain();
            subnet.DomainManagement.SyncDomains.Add(syncDomain);

            SetSubnetIOSystemSyncDomainForCentralDevice(subnet, ioSystem, syncDomain);

            SetSubnetIOSystemSyncDomainForDecentralDevice(subnet, ioSystem, syncDomain);
        }

        private void SetSubnetIOSystemSyncDomainForCentralDevice(Subnet subnet, SubnetIOSystem ioSystem, SyncDomainType syncDomain)
        {
            m_Devices.CentralDevice[0].CentralDeviceInterface.EthernetAddresses.SubnetRefID =
                   subnet.SubnetID;
            m_Devices.CentralDevice[0].CentralDeviceInterface.EthernetAddresses.IOSystemRefID =
                ioSystem.IOSystemID;
            m_Devices.CentralDevice[0].CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                .Synchronization.SyncDomainRefID = syncDomain.SyncDomainID;
        }

        private void SetSubnetIOSystemSyncDomainForDecentralDevice(Subnet subnet, SubnetIOSystem ioSystem, SyncDomainType syncDomain)
        {
            foreach (DecentralDeviceType decentralDevice in m_Devices.DecentralDevice)
            {
                decentralDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID = subnet.SubnetID;
                decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID = ioSystem.IOSystemID;
                decentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                    .SyncDomainRefID = syncDomain.SyncDomainID;
            }
        }
        private void CheckDefaultSettingsForCentralDevice(ref bool defaultSubnetNeeded, ref bool defaultIoSystemNeeded, ref bool defaultSyncDomainNeeded)
        {
            foreach (CentralDeviceType centralDevice in m_Devices.CentralDevice)
            {
                if (!string.IsNullOrEmpty(centralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID))
                {
                    defaultSubnetNeeded = false;
                }

                if (!string.IsNullOrEmpty(centralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID))
                {
                    defaultIoSystemNeeded = false;
                }

                if (!string.IsNullOrEmpty(
                        centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                            .SyncDomainRefID))
                {
                    defaultSyncDomainNeeded = false;
                }
            }
        }

        private void CheckDefaultSettingsForDecentralDevice(ref bool defaultSubnetNeeded, ref bool defaultIoSystemNeeded, ref bool defaultSyncDomainNeeded)
        {
            foreach (DecentralDeviceType decentralDevice in m_Devices.DecentralDevice)
            {
                if (!string.IsNullOrEmpty(decentralDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID))
                {
                    defaultSubnetNeeded = false;
                }

                if (!string.IsNullOrEmpty(decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID))
                {
                    defaultIoSystemNeeded = false;
                }

                if (!string.IsNullOrEmpty(
                        decentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                            .SyncDomainRefID))
                {
                    defaultSyncDomainNeeded = false;
                }
            }
        }

        private Subnet CreateDefaultSubnet()
        {
            Subnet subnet = new Subnet();
            subnet.SubnetID = "PNIE_1";

            return subnet;
        }

        private SubnetIOSystem CreateDefaultIoSystem()
        {
            SubnetIOSystem ioSystem = new SubnetIOSystem();
            ioSystem.IOSystemID = "PROFINET IO-System";
            ioSystem.General.IOSystemName = "PROFINET IO-System";
            ioSystem.General.IOSystemNumber = 100;
            ioSystem.General.IOSystemNumberSpecified = true;

            return ioSystem;
        }

        private static SyncDomainType CreateDefaultSyncDomain()
        {
            SyncDomainType syncDomain = new SyncDomainType();
            syncDomain.SyncDomainID = "Sync-Domain_1";

            return syncDomain;
        }
    }
}
