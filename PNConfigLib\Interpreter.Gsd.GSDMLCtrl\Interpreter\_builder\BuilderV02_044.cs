/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_044.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml;
using System.Xml.XPath;
using System.Collections;
using GSDI;
using C = PNConfigLib.Gsd.Interpreter.Common;
using PNConfigLib.Gsd.Interpreter;
using System.Globalization;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02044 :
        BuilderV02043
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02044()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version244);
        }

        #endregion


        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    // ------------- COMMON ELEMENTS --------------------------
                    case Models.s_ObjectFBaseIDRecordData:
                        {
                            // NOTE: Navigator must point to F_BaseIDRecordDataItem.
                            PrepareFBaseIDRecordData(nav, ref hash);
                            obj = new C.FBaseIDRecordData();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion


        //########################################################################################
        #region Structure Model Methods

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation

        /// InterfaceSubmoduleItem: New attribute TSN_ConfigurationsSupported
        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldTSN_ConfigurationsSupported, null);
            hash.Add(Models.s_FieldBridge_FeaturesSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);


            // Get Bridge_FeaturesSupported attribute.
            ArrayList listBridgeFeaturesSupported = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_Bridge_FeaturesSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsBridgeFeaturesSupportedEnumValueConvertable(token))
                    {
                        listBridgeFeaturesSupported.Add(Enums.ConvertBridgeFeaturesSupportedEnum(token));
                    }
                }
            }
            
            if (listBridgeFeaturesSupported.Count == 0)
                listBridgeFeaturesSupported.Add(GSDI.BridgeSupportedFeatures.GSD_BridgeSupportedFeature_None);
            hash[Models.s_FieldBridge_FeaturesSupported] = listBridgeFeaturesSupported;


            // Get TSN_ConfigurationsSupported attribute.
            ArrayList listTSNConfigurationsSupported = new ArrayList();
            attr = nav.GetAttribute(Attributes.s_TSN_ConfigurationsSupported, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsTSNConfigurationsSupportedEnumValueConvertable(token))
                    {
                        listTSNConfigurationsSupported.Add(Enums.ConvertTSNConfigurationsSupportedEnum(token));
                    }
                }
            }
            if (listTSNConfigurationsSupported.Count == 0)
                listTSNConfigurationsSupported.Add(GSDI.TSNSupportedConfigurations.GSD_TSNSupportedConfiguration_None);
            hash[Models.s_FieldTSN_ConfigurationsSupported] = listTSNConfigurationsSupported;
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for VirtualSubmodule object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldFBaseIDRecordData, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            // --------------------------------------------

            // Navigate to FBaseIDRecordData and create it. Optional.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_FBaseIDRecordDataItem, Namespaces.s_GsdmlDeviceProfile, false);

            // Create found record data.
            if (nodes.MoveNext())
            {
                // Create data record itself.
                obj = CreateGsdObject(Models.s_ObjectFBaseIDRecordData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectFBaseIDRecordData + "' couldn't be created!");
            }

            // Set hash variable.
            hash[Models.s_FieldFBaseIDRecordData] = obj;
        }

        protected override void PrepareParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAccess, null);

            // Call base class method first.
            base.PrepareParameterRecordData(nav, ref hash);


            // Get TSN_ConfigurationsSupported attribute. Optional.
            ArrayList listAccess = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_Access, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string token in separateTokens)
                {
                    if (Enums.IsSupportedAccessEnumValueConvertable(token))
                    {
                        listAccess.Add(Enums.ConvertSupportedAccessEnum(token));
                    }
                }
            }
            if (listAccess.Count == 0)
                listAccess.Add(GSDI.SupportedAccess.GSD_SupportedAccess_Prm);
            hash[Models.s_FieldTSN_ConfigurationsSupported] = listAccess;
        }

        protected override void PrepareCIMInterface(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSupportedServiceProtocols, null);

            // Call base class method first.
            base.PrepareCIMInterface(nav, ref hash);

            // Get SupportedServiceProtocols attribute. Optional.
            ArrayList listSupportedServiceProtocols = new ArrayList();
            string attr = nav.GetAttribute(Attributes.s_SupportedServiceProtocols, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList separateTokens = Help.SeparateTokenList(attr);
                foreach (string type in separateTokens)
                {
                    if (Enums.IsServiceProtocolsEnumValueConvertable(type))
                    {
                        listSupportedServiceProtocols.Add(Enums.ConvertServiceProtocolsEnum(type));
                    }
                }
                hash[Models.s_FieldSupportedServiceProtocols] = listSupportedServiceProtocols;
            }
            if (listSupportedServiceProtocols.Count == 0)
                listSupportedServiceProtocols.Add(GSDI.ServiceProtocols.GSDSrvProtRSI);
        }

        protected virtual void PrepareFBaseIDRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for FBaseIDRecordData object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldIndex, null);
            hash.Add(Models.s_FieldLength, null);
            hash.Add(Models.s_FieldName, null);		// always null!
            hash.Add(Models.s_FieldNameTextId, null);	// always null!
            hash.Add(Models.s_FieldConsts, null);

            hash.Add(Models.s_FieldTransferSequence, null);
            hash.Add(Models.s_FieldChangeableWithBump, null);
            hash.Add(Models.s_FieldRefs, null);

            //XPathNodeIterator nodes = null;


            // Get Index attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Index, String.Empty);
            UInt32 value = 0;
            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
            hash[Models.s_FieldIndex] = value;

            // Get TransferSequence attribute. Optional.
            hash[Models.s_FieldTransferSequence] = Attributes.s_DefaultTransferSequence;
            attr = nav.GetAttribute(Attributes.s_TransferSequence, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldTransferSequence] = value;
            }

            // Get ChangeableWithBump attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_ChangeableWithBump, String.Empty);
            hash[Models.s_FieldChangeableWithBump] = Help.GetBool(attr, Attributes.s_DefaultChangeableWithBump);

            // Set Length property.
            hash[Models.s_FieldLength] = Attributes.s_FixedFBaseIDRecordDataLength;

            Hashtable h = null;

            // Set Consts property.
            h = new Hashtable();
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldValues, null);

            ArrayList consts = new ArrayList();
            object obj = this.PrepareFBaseIDRecordData_Create_Const();
            consts.Add(obj);
            hash[Models.s_FieldConsts] = consts;

            // Set Refs property.
            h = new Hashtable();
            h.Add(Models.s_FieldDataType, null);
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldBitOffset, null);
            h.Add(Models.s_FieldBitLength, null);
            h.Add(Models.s_FieldIsChangeable, null);
            h.Add(Models.s_FieldIsVisible, null);
            h.Add(Models.s_FieldName, null);
            h.Add(Models.s_FieldNameTextId, null);
            h.Add(Models.s_FieldHelp, null);
            h.Add(Models.s_FieldValueGsdId, null);
            h.Add(Models.s_FieldDefaultValue, null);
            h.Add(Models.s_FieldValues, null);
            h.Add(Models.s_FieldValueType, null);

            ArrayList refs = new ArrayList();
            obj = PrepareFBaseIDRecordData_Create_Ref_F_BaseID(nav, ref h);
            refs.Add(obj);
            obj = PrepareFBaseIDRecordData_Create_Ref_F_BaseID_CRC(nav, ref h);
            refs.Add(obj);
            hash[Models.s_FieldRefs] = refs;
        }

        /// <summary>
        /// The F-BaseID parameter data is a fixed 12 byte block with configuration data. It is
        /// organized as follows:
        /// 0	F_BaseID    	(Signed64)  	0x00 (0)
        /// 1									0x00 (0)
        /// 2									0x00 (0)
        /// 3									0x00 (0)
        /// 4									0x00 (0)
        /// 5									0x00 (0)
        /// 6									0x00 (0)
        /// 7									0x00 (0)
        /// 8	F_BaseID_CRC	(Unsigned32)	0x00 (0)
        /// 9									0x00 (0)
        /// 10									0x00 (0)
        /// 11									0x00 (0)
        /// </summary>
        /// <returns>Const object with default binary settings with byte length 12.</returns>
        protected virtual object PrepareFBaseIDRecordData_Create_Const()
        {
            Hashtable hash = new Hashtable();
            hash.Add(Models.s_FieldByteOffset, null);
            hash.Add(Models.s_FieldValues, null);

            // Set ByteOffset.
            hash[Models.s_FieldByteOffset] = Attributes.s_DefaultByteOffset;

            // Set Values.
            uint[] v = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
            hash[Models.s_FieldValues] = new ArrayList(v);

            // Create object.
            C.GsdObject obj = new C.ConstData();

            // Fill object with data.
            bool succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectConstData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        /// <summary>
        /// The F_BaseID parameter is a 64 bit integer that augments the codename and allows for detecting authenticity (i.e., addressing) errors. 
        /// 
        /// Data type:		Integer64
        /// Byte offset:	0
        /// Bit offset:		0
        /// Bit length:		64
        /// 
        ///	Parameter values (max. range: -9223372036854775808..9223372036854775807)
        /// </summary>
        /// <returns>Ref object for the F_BaseID parameter.</returns>
        protected virtual object PrepareFBaseIDRecordData_Create_Ref_F_BaseID(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FBaseID;
                hash[Models.s_FieldNameTextId] = Elements.s_FBaseID;
                hash[Models.s_FieldDataType] = DataTypes.GSDInteger64;
                hash[Models.s_FieldByteOffset] = (uint)0;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)64;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;

                // Build values.
                Int64 defaultValue = 0;	// Is initially setted with default value!
                string sAllowedValues = @"-9223372036854775808..9223372036854775807";
                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FBaseID, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    if (nodes.Current != null)
                    {
                        string attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                        if (attr.Length != 0)
                            sAllowedValues = attr;

                        attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                        if (attr.Length > 0)
                        {
                            Int64 value = XmlConvert.ToInt64(attr);
                            defaultValue = value;
                        }
                    }
                }

                hash[Models.s_FieldDefaultValue] = defaultValue;


                // Create needed area item(s).

                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                ArrayList list = new ArrayList();
                C.GsdObject obj = null;

                // Split incoming string to pairs and numbers in a list.
                ArrayList splitlist = new ArrayList(sAllowedValues.Split(Constants.s_Space.ToCharArray()));

                foreach (string s in splitlist)
                {
                    // Get min and max value.
                    string ssMin = String.Empty;
                    string ssMax = String.Empty;
                    int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                    if (index != -1)
                    {
                        // Area, separate area and add values (-9223372036854775808..9223372036854775807).
                        ssMin = s.Substring(0, index);	// -9223372036854775808
                        ssMax = s.Substring(index + 2);	// 9223372036854775807
                    }
                    else
                    {
                        // Value, create area with same min and max value.
                        ssMin = s;
                        ssMax = s;
                    }

                    // Create AreaItem(s).
                    Int64 nMin = XmlConvert.ToInt64(ssMin);
                    Int64 nMax = XmlConvert.ToInt64(ssMax);

                    valuehash[Models.s_FieldMinValue] = nMin;
                    valuehash[Models.s_FieldMaxValue] = nMax;

                    obj = new C.AreaItem();
                    succeeded = obj.Fill(valuehash);
                    if (!succeeded)
                        throw new PreparationException(Models.s_ObjectFBaseIDRecordData + " couldn't be filled with data!");
                    list.Add(obj);
                }

                hash[Models.s_FieldValues] = list;


                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FBaseIDRecordData - Ref (F_BaseID)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FBaseIDRecordData - Ref (F_BaseID)!", e);
            }
        }

        /// <summary>
        /// The F_BaseID_CRC defines the CRC for the BaseID Parameters. 
        /// 
        /// Data type:		Unsigned32
        /// Byte offset:	8
        /// Bit offset:		0
        /// Bit length:		32
        /// 
        ///	Parameter values (max. range: 0..4294967295)
        /// </summary>
        /// <returns>Ref object for the F_BaseID parameter.</returns>
        protected virtual object PrepareFBaseIDRecordData_Create_Ref_F_BaseID_CRC(XPathNavigator nav, ref Hashtable hash)
        {
            try
            {
                // Set hash values.
                hash[Models.s_FieldName] = Elements.s_FBaseID_CRC;
                hash[Models.s_FieldNameTextId] = Elements.s_FBaseID_CRC;
                hash[Models.s_FieldDataType] = DataTypes.GSDUnsigned32;
                hash[Models.s_FieldByteOffset] = (uint)8;
                hash[Models.s_FieldBitOffset] = (uint)0;
                hash[Models.s_FieldBitLength] = (uint)32;
                hash[Models.s_FieldHelp] = String.Empty;

                hash[Models.s_FieldValueGsdId] = String.Empty;
                hash[Models.s_FieldValueType] = ValueTypes.GSDArea;

                hash[Models.s_FieldIsChangeable] = true;
                hash[Models.s_FieldIsVisible] = true;

                // Build values.
                uint defaultValue = 0;	// Is initially setted with default value!
                XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FBaseID_CRC, Namespaces.s_GsdmlDeviceProfile);

                if (nodes.MoveNext())
                {
                    if (nodes.Current != null)
                    {
                        string attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                        if (attr.Length > 0)
                        {
                            UInt32 value = 0;
                            UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                            defaultValue = value;
                        }
                    }
                }

                hash[Models.s_FieldDefaultValue] = defaultValue;


                // Create needed area item(s).
                Hashtable valuehash = new Hashtable();
                valuehash.Add(Models.s_FieldMinValue, null);
                valuehash.Add(Models.s_FieldMaxValue, null);

                ArrayList list = new ArrayList();

                C.GsdObject obj = new C.AreaItem();
                valuehash[Models.s_FieldMinValue] = uint.MinValue;
                valuehash[Models.s_FieldMaxValue] = uint.MaxValue;
                bool succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);

                hash[Models.s_FieldValues] = list;

                // Create and fill RefData object.
                obj = new C.RefData();
                succeeded = obj.Fill(hash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

                return obj;	// ---------->

            }
            catch (FormatException e)
            {
                throw new PreparationException("Couldn't prepare FBaseIDRecordData - Ref (F_BaseID_CRC)!", e);
            }
            catch (OverflowException e)
            {
                throw new PreparationException("Couldn't prepare FBaseIDRecordData - Ref (F_BaseID_CRC)!", e);
            }
        }


        #endregion

        #endregion

    }
}
