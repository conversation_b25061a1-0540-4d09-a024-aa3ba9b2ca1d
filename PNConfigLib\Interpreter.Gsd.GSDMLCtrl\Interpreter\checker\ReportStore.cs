/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ReportStore.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// The ReportStore is an internal helper class for collecting report
    /// objects in a simple and common way. It is used from the internal
    /// checker to collect the reports during check operation and after that,
    /// to make the reports available.
    /// </summary>
    internal class ReportStore
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the ReportStore if it is instantiated.
        /// The list properties are all initialized to null and the flags to false. 
        /// </summary>
        public ReportStore()
        {
            m_RepError = new List<GSDI.IReport>();
            m_RepMinorError = new List<GSDI.IReport>();
            m_RepWarning = new List<GSDI.IReport>();
            m_RepInfo = new List<GSDI.IReport>();

            m_RepFlagError = false;
            m_RepFlagMinorError = false;
            m_RepFlagWarning = false;
            m_RepFlagInfo = false;

        }

        #endregion

        //########################################################################################
        #region Fields

        private List<GSDI.IReport> m_RepError;
        private List<GSDI.IReport> m_RepMinorError;
        private List<GSDI.IReport> m_RepWarning;
        private List<GSDI.IReport> m_RepInfo;

        private bool m_RepFlagError;
        private bool m_RepFlagMinorError;
        private bool m_RepFlagWarning;
        private bool m_RepFlagInfo;

        // Specifies the number of errors, which will be accepted, before
        // the check is aborted.
        private static uint s_MaxErrorCount = Settings.MaxErrorCount;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses whether any report is available. True is returned, if any
        /// error, minor error, warning or info report exists in the store, else false.
        /// </summary>
        public bool AllFlag => (m_RepFlagError || m_RepFlagMinorError || m_RepFlagWarning || m_RepFlagInfo);

        /// <summary>
        /// Accesses whether any error report is available.
        /// </summary>
        public bool ErrorFlag => m_RepFlagError;

        /// <summary>
        /// Accesses whether any minor error report is available.
        /// </summary>
        public bool MinorErrorFlag => m_RepFlagMinorError;

        /// <summary>
        /// Accesses whether any warning report is available.
        /// </summary>
        public bool WarningFlag => m_RepFlagWarning;

        /// <summary>
        /// Accesses whether any info report is available.
        /// </summary>
        public bool InfoFlag => m_RepFlagInfo;
        /// <summary>
        /// Accesses a list of report objects, which contains all error, warning
        /// and info reports.
        /// </summary>
        public virtual Array AllReports
        {
            get
            {
                List<GSDI.IReport> allReports = new List<GSDI.IReport>();
                allReports.AddRange(m_RepError);
                allReports.AddRange(m_RepMinorError);
                allReports.AddRange(m_RepWarning);
                allReports.AddRange(m_RepInfo);

                return allReports.ToArray();
            }
        }
        /// <summary>
        /// Accesses a list of report objects, which contains all error reports.
        /// </summary>
        public virtual Array ErrorReports
        {
            get
            {
                if (null != m_RepError)
                    return m_RepError.ToArray();
                return null;
            }
        }
        /// <summary>
        /// Accesses a list of report objects, which contains all minor error reports.
        /// </summary>
        public virtual Array MinorErrorReports
        {
            get
            {
                if (null != m_RepMinorError)
                    return m_RepMinorError.ToArray();
                return null;
            }
        }
        /// <summary>
        /// Accesses a list of report objects, which contains all warning reports.
        /// </summary>
        public virtual Array WarningReports
        {
            get
            {
                if (null != m_RepWarning)
                    return m_RepWarning.ToArray();
                return null;
            }
        }
        /// <summary>
        /// Accesses a list of report objects, which contains all info reports.
        /// </summary>
        public virtual Array InfoReports
        {
            get
            {
                if (null != m_RepInfo)
                    return m_RepInfo.ToArray();
                return null;
            }
        }

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Creates a report object with the given parameter as content.
        /// </summary>
        /// <param name="type">Specifies the type of the report.</param>
        /// <param name="linenr">The line number of the file, where the problem occurs.</param>
        /// <param name="linepos">The position whitin the line of the file, where the problem occurs.</param>
        /// <param name="msg">A human readable message, describing the occurred problem.</param>
        /// <param name="xpath">An XPath expression, specifying exactly the element (or attribute)
        /// on which the problem occurs.</param>
        /// <param name="category">The category of the occurred problem.</param>
        /// <param name="checknr">The number of the check, which exposes the actual problem.</param>
        /// <returns>The report object, created with the given parameter data.</returns>
        public static Report CreateReport(GSDI.ReportTypes type, int linenr, int linepos, string msg, string xpath, ReportCategories category, string checknr)
        {
            // Prepare relevant data in hashtable.
            Hashtable hash = new();

            hash[Constants.s_ReportType] = type;
            hash[Constants.s_ReportLineNumber] = linenr;
            hash[Constants.s_ReportLinePosition] = linepos;
            hash[Constants.s_ReportMessage] = msg;
            hash[Constants.s_ReportCheckNumber] = checknr;
            hash[Constants.s_ReportSourceXPath] = xpath;

            switch (category)
            {
                case ReportCategories.General:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_General");
                        break;
                    }
                case ReportCategories.KeyKeyref:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_KeyKeyref");
                        break;
                    }
                case ReportCategories.PlugRules:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_PlugRules");
                        break;
                    }
                case ReportCategories.Signature:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_Signature");
                        break;
                    }
                case ReportCategories.TypeSpecific:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_TypeSpecific");
                        break;
                    }
                case ReportCategories.Validation:
                    {
                        hash[Constants.s_ReportCategory] = Help.GetMessageString("M_Validation");
                        break;
                    }
            }

            // Create and announce report.
            Report r = new(hash);
            return r;
        }


        /// <summary>
        /// Creates a report object with the given parameter as content. Furtheron the
        /// just created report is announced to the actual store instance.
        /// </summary>
        /// <param name="type">Specifies the type of the report.</param>
        /// <param name="linenr">The line number of the file, where the problem occurs.</param>
        /// <param name="linepos">The position within the line of the file, where the problem occurs.</param>
        /// <param name="msg">A human readable message, describing the occurred problem.</param>
        /// <param name="xpath">An XPath expression, specifying exactly the element (or attribute)
        /// on which the problem occurs.</param>
        /// <param name="category">The category of the occurred problem.</param>
        /// <param name="checknr">The number of the check, which exposes the actual problem.</param>
        /// <returns>True, if creation and announcement was successful, else false.</returns>
        public virtual bool CreateAndAnnounceReport(GSDI.ReportTypes type, int linenr, int linepos, string msg, string xpath, ReportCategories category, string checknr)
        {
            try
            {
                // Create and announce report.
                Report r = CreateReport(type, linenr, linepos, msg, xpath, category, checknr);
                return AnnounceReport(r);
            }
            catch (ArgumentException e)
            {
            }

            return false;
        }

        /// <summary>
        /// Announces the given report to the report store. The corresponding 
        /// report count increases and the relevant lists are updated.
        /// </summary>
        /// <param name="rep">Report object, which should be announced to the
        /// report store.</param>
        /// <returns>True, if announcement was successful, else false.</returns>
        public virtual bool AnnounceReport(GSDI.IReport rep)
        {
            // NOTE: If maximal error count is reached, the method returns wrong!
            bool succeeded = true;
            try
            {
                switch (rep.Type)
                {
                    case GSDI.ReportTypes.GSD_RT_Error:
                        {
                            // Set flag.
                            m_RepFlagError = true;

                            // Add report.
                            m_RepError.Add(rep);

                            break;
                        }
                    case GSDI.ReportTypes.GSD_RT_MinorError:
                        {
                            // Set flag.
                            m_RepFlagMinorError = true;

                            // Add report.
                            if (m_RepMinorError.Count <= s_MaxErrorCount)
                            {
                                m_RepMinorError.Add(rep);
                            }

                            break;
                        }
                    case GSDI.ReportTypes.GSD_RT_Warning:
                        {
                            // Set flag.
                            m_RepFlagWarning = true;

                            // Add report.
                            if (m_RepWarning.Count <= s_MaxErrorCount)
                            {
                                m_RepWarning.Add(rep);
                            }

                            break;
                        }
                    case GSDI.ReportTypes.GSD_RT_Info:
                        {
                            // Set flag.
                            m_RepFlagInfo = true;

                            // Add report.
                            if (m_RepInfo.Count <= s_MaxErrorCount)
                            {
                                m_RepInfo.Add(rep);
                            }

                            break;
                        }
                    default:
                        {
                            throw new ArgumentException("Invalid report type asserted!");
                        }
                }

            }
            catch (ArgumentException e)
            {
                succeeded = false;
            }
            catch (NotSupportedException e)
            {
                succeeded = false;
            }

            return succeeded;
        }


        /// <summary>
        /// Serializes the announced report objects with enclosing element to a fixed 
        /// XML structure. 
        /// Which reports are serialized depends on the given type.
        /// </summary>
        /// <param name="lReportType">Type of the report, which should be serialized.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool Serialize(GSDI.ReportTypes lReportType, ref System.Xml.XmlTextWriter writer)
        {
            writer.WriteStartElement(Export.s_ElementReports);

            // ----------------------------------------------
            SerializeMembers(lReportType, ref writer);

            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes the announced report objects without any enclosing element 
        /// to a fixed XML structure. 
        /// Which reports are serialized depends on the given type.
        /// </summary>
        /// <param name="lReportType">Type of the report, which should be serialized.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal virtual bool SerializeMembers(GSDI.ReportTypes lReportType, ref System.Xml.XmlTextWriter writer)
        {
            switch (lReportType)
            {
                case GSDI.ReportTypes.GsdrtAll:
                    {
                        if (m_RepError != null)
                            writer.WriteAttributeString(Export.s_AttributeErrors, m_RepError.Count.ToString(CultureInfo.CurrentCulture));
                        if (m_RepMinorError != null)
                            writer.WriteAttributeString(Export.s_AttributeMinorErrors, m_RepMinorError.Count.ToString(CultureInfo.CurrentCulture));
                        if (m_RepWarning != null)
                            writer.WriteAttributeString(Export.s_AttributeWarnings, m_RepWarning.Count.ToString(CultureInfo.CurrentCulture));
                        if (m_RepInfo != null)
                            writer.WriteAttributeString(Export.s_AttributeInformations, m_RepInfo.Count.ToString(CultureInfo.CurrentCulture));

                        break;
                    }
                //
                case GSDI.ReportTypes.GSD_RT_Error:
                    {
                        SerializeError(ref writer);
                        break;
                    }
                //
                case GSDI.ReportTypes.GSD_RT_MinorError:
                    {
                        SerializeMinorError(ref writer);
                        break;
                    }
                //
                case GSDI.ReportTypes.GSD_RT_Warning:
                    {
                        SerializeWarning(ref writer);
                        break;
                    }
                //
                case GSDI.ReportTypes.GSD_RT_Info:
                    {
                        SerializeInfo(ref writer);
                        break;
                    }
            }

            return true;
        }

        private void SerializeInfo(ref System.Xml.XmlTextWriter writer)
        {
            writer.WriteAttributeString(Export.s_AttributeInformations, m_RepInfo.Count.ToString(CultureInfo.CurrentCulture));

            if (null == m_RepInfo)
            {
                return;
            }

            foreach (Report r in m_RepInfo)
            {
                r.Serialize(ref writer);
            }
        }

        private void SerializeWarning(ref System.Xml.XmlTextWriter writer)
        {
            writer.WriteAttributeString(Export.s_AttributeWarnings, m_RepWarning.Count.ToString(CultureInfo.CurrentCulture));

            if (null == m_RepWarning)
            {
                return;
            }

            foreach (Report r in m_RepWarning)
            {
                r.Serialize(ref writer);
            }
        }

        private void SerializeMinorError(ref System.Xml.XmlTextWriter writer)
        {
            writer.WriteAttributeString(Export.s_AttributeMinorErrors, m_RepMinorError.Count.ToString(CultureInfo.CurrentCulture));

            if (null == m_RepMinorError)
            {
                return;
            }

            foreach (Report r in m_RepMinorError)
            {
                r.Serialize(ref writer);
            }
        }

        private void SerializeError(ref System.Xml.XmlTextWriter writer)
        {
            writer.WriteAttributeString(Export.s_AttributeErrors, m_RepError.Count.ToString(CultureInfo.CurrentCulture));

            if (null == m_RepError)
            {
                return;
            }

            foreach (Report r in m_RepError)
            {
                r.Serialize(ref writer);
            }
        }
        #endregion

    }
}
