<!--***********************************************************************
  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      
***************************************************************************
  This program is protected by German copyright law and international      
  treaties. The use of this software including but not limited to its      
  Source Code is subject to restrictions as agreed in the license          
  agreement between you and Siemens.                                       
  according to your license agreement with Siemens.                        
  Copying or distribution is not allowed unless expressly permitted        
***************************************************************************
                                                                           
  P r o j e c t         Basic Components                            
                                                                           
  P a c k a g e         PROFINET Configuration Library      
                                                                           
  C o m p o n e n t     Examples                          
                                                                           
  F i l e               ListOfNodes.xml                 
                                                                           
***************************************************************************-->

<!--************************************************************************************
    *** Example For a Basic Configuration of a Central Device and a Decentral Device ***
    ************************************************************************************
______________________________________ Description _____________________________________
- A central device and a decentral device are configured.
- PNConfigLib supports any PROFINET controller as an IO controller. The device version is specified in the "DeviceVersion" XML attribute, where the variant is specified in the "InterfaceType" XML attribute.
- PNConfigLib supports decentral devices which can be modeled with GSDML. The GSDML path is specified in the "GSDPath" XML attribute, where the related DeviceAccessPoint's ID defined in the GSDML is referred in the "GSDRefID" XML attribute.
- PNConfigLib does not create the interfaces automatically. For this reason, they must be introduced.
- The IDs (ListOfNodesID, DeviceID, InterfaceID) defined in this ListOfNodes file are referred in the Configuration and in the optional Topology files.
-->
<ListOfNodes schemaVersion="1.0"
             ListOfNodesID="ListOfNodesID"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes"
             xsi:schemaLocation="http://www.siemens.com/Automation/PNConfigLib/ListOfNodes ListOfNodes.xsd">
    <PNDriver DeviceID="PN_Driver_1"
			  DeviceVersion="v3.1"
              DeviceName="PROFINET Driver">
        <Interface InterfaceID="PN_Driver_1_Interface"
                   InterfaceType="Linux Native"
                   InterfaceName="PN_Driver_1_Interface" />
    </PNDriver>
    <DecentralDevice DeviceID="ET200SP_1"
                     GSDPath="../GSDMLs/GSDML-V2.33-Siemens-ET200SP-20171130.xml"
                     GSDRefID="DIM ST V3.1"
                     DeviceName="ET200SP_1">
        <Interface InterfaceID="ET200SP_1_Interface"
                   InterfaceName="ET200SP_1_Interface" />
    </DecentralDevice>
</ListOfNodes>