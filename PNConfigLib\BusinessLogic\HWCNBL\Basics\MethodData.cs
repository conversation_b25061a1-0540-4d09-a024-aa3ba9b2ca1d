/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MethodData.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Basics
{
    /// <summary>
    /// The class MethodData implements interface IMethodData, which provides access to the data
    /// of a generic method via IBaseActions.CallMethod().
    /// </summary>
    internal class MethodData : IMethodData
    {
        //########################################################################################

        #region Fields

        /// <summary>
        /// The collection of all arguments, where each argument is referenced by name (string)
        /// and of type object.
        /// </summary>
        private NameObjectCollection m_Arguments;

        /// <summary>
        /// Name of the method.
        /// </summary>
        private string m_Name;

        /// <summary>
        /// Return value of the method.
        /// </summary>
        private object m_ReturnValue;

        #endregion

        //########################################################################################

        #region IMethodData Members

        /// <summary>
        /// The collection of all arguments, where each argument
        /// is referenced by name (string) and of type object.
        /// </summary>
        public NameObjectCollection Arguments
        {
            get
            {
                if (this.m_Arguments == null)
                {
                    this.m_Arguments = new NameObjectCollection();
                }
                return this.m_Arguments;
            }
        }

        /// <summary>
        /// The name of the method, which is registered at the config object;
        /// it’s not necessarily the same as the name of the corresponding callback
        /// (but a similarity is highly recommended).
        /// </summary>
        public string Name
        {
            get { return this.m_Name; }
            set { this.m_Name = value; }
        }

        /// <summary>
        /// The return value of the method; may be null in case of a void-method.
        /// </summary>
        public object ReturnValue
        {
            get { return this.m_ReturnValue; }
            set { this.m_ReturnValue = value; }
        }

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        #endregion
    }
}