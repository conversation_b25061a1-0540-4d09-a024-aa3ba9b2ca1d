/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: MAUTypeItem.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The MAUTypeItem element contains information about a single MAUTypeItem.
    /// It could be used to specify the characteristic of MAU types
    /// of a port submodule.
    /// </summary>
    public class MauTypeItem :
        GsdObject
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the MAUTypeItem if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public MauTypeItem()
        {
            m_AdjustSupported = false;
            m_Value = 0;
            m_Extension = 0;
            m_SupportedFeatures = null;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private bool m_AdjustSupported;
        private UInt32 m_Value;
        private UInt32 m_Extension;
        private ArrayList m_SupportedFeatures;   // V2.4
        private UInt32 m_MaxTransferTimeTx;   // V2.42
        private UInt32 m_MaxTransferTimeRx;   // V2.42

        private AplPortClassification m_AplPortClassification;   // V2.43

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the AdjustSupported property, which specifies whether the 
		/// engineering system should display the data item in a bit representation,
		/// or in normal octet representation.
		/// </summary>
		/// <remarks>Instead of addressing an octet only completely (e.g. with one number 5),
		/// each bit of the octet can be addressed separately (e.g. with 5.0 to 5.7)</remarks>
        public bool IsAdjustSupported => this.m_AdjustSupported;

        /// <summary>
        /// Accesses the length of the octets, specified with this data item.
        /// </summary>
        public UInt32 Value => this.m_Value;

        /// <summary>
        /// Accesses the length of the octets, specified with this data item.
        /// </summary>
        public UInt32 Extension => this.m_Extension;

        /// <summary>
        /// </summary>
        /// <remarks></remarks>
        public Array SupportedFeatures =>
            (null != this.m_SupportedFeatures) ?
                m_SupportedFeatures.ToArray() :
                null;

        /// <summary>
        /// Accesses the list of max transfer time TX, specified with this MAU type item.
        /// </summary>
        public UInt32 MaxTransferTimeTX => this.m_MaxTransferTimeTx;

        /// <summary>
        /// Accesses the list of max transfer time RX, specified with this MAU type item.
        /// </summary>
        public UInt32 MaxTransferTimeRX => this.m_MaxTransferTimeRx;

        public AplPortClassification APLPortClassification => this.m_AplPortClassification;


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldAdjustSupported;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    m_AdjustSupported = (bool)hash[member];

                member = Models.s_FieldValue;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_Value = (UInt32)hash[member];

                member = Models.s_FieldExtension;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_Extension = (UInt32)hash[member];

                member = Models.s_FieldSupportedFeatures;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    m_SupportedFeatures = hash[member] as ArrayList;

                member = Models.s_FieldMaxTransferTimeTX;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_MaxTransferTimeTx = (UInt32)hash[member];

                member = Models.s_FieldMaxTransferTimeRX;
                if (hash.ContainsKey(member) && hash[member] is UInt32)
                    m_MaxTransferTimeRx = (UInt32)hash[member];

                member = Models.s_FieldAplPortClassification;
                if (hash.ContainsKey(member) && hash[member] is AplPortClassification)
                    this.m_AplPortClassification = hash[member] as AplPortClassification;

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectMauTypeItem);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteBooleanProperty(ref writer, Models.s_FieldAdjustSupported, this.m_AdjustSupported);
            Export.WriteUint32Property(ref writer, Models.s_FieldValue, this.m_Value);
            Export.WriteUint32Property(ref writer, Models.s_FieldExtension, this.m_Extension);
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedFeatures, m_SupportedFeatures, true);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxTransferTimeTX, this.m_MaxTransferTimeTx);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxTransferTimeRX, this.m_MaxTransferTimeRx);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldAplPortClassification, this.m_AplPortClassification);


            return true;
        }

        #endregion
    }
}
