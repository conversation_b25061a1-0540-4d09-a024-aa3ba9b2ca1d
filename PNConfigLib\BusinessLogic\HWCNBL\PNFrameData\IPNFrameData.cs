/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPNFrameData.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

#endregion

namespace PNConfigLib.HWCNBL
{
    /// <summary>
    /// Summary description for IPNFrameData.
    /// </summary>
    public interface IPNFrameData
    {
        //########################################################################################

        #region Methods

        /// <summary>
        /// Gets the total frame length by adding the header and the trailer.
        /// Parts of the frame considered:
        /// IFG(12 Byte) + Safety (in RTC3) (2 Byte) + Preamble (1 or 7 Byte) + Start Frame Delimiter (1 Byte) +
        /// Ethernet Header (14 Byte) + VLAN Header (in RTC 1&2) (4 Byte) + PROFINET Header (2 Byte) + Datalength +
        /// PROFINET Trailer (4 Byte) + Frame Check Sequence (4 Byte)
        /// Datalength must be minimum 40 Bytes. If the actual data length is smaller than 40 bytes, a payload is added
        /// to make it 40 Bytes.
        /// </summary>
        /// <param name="useShortPreambleForRTC3">
        /// If true, 1-Byte preamble will be used in RTC3 Frames
        /// instead of 7.
        /// </param>
        long GetTotalFrameLength(bool useShortPreambleForRTC3);

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// List of core ids of io-devices which have this frame.
        /// </summary>
        IList<int> CoreIds { get; }

        /// <summary>
        /// Core id of the io-device which has the frame. Use CoreIds in multicast or dfp cases.
        /// </summary>
        int CoreId { get; }

        string Version { get; }

        long FrameID { get; set; }

        long RedundantFrameID { get; set; }

        int StationNumber { get; set; }

        long FrameType { get; set; }

        byte FrameDirection { get; set; }

        long FrameClass { get; set; }

        /// <summary>
        /// Data Length of the frame. Data length is the payload length without any padding.
        /// </summary>
        long DataLength { get; set; }

        /// <summary>
        /// Data Length of the theoretical bandwidth reservation frame which incluudes the payload of ALL shared
        /// submodules.
        /// </summary>
        long SharedDataLength { get; set; }

        long DeviceLocalReductionRatio { get; set; }

        int ReductionGranularity { get; }

        long SendClockFactor { get; set; }

        long MinFrameIntervall { get; set; }

        uint NumberOfARs { get; set; }

        bool HasNotAssignedSubmodule { get; set; }

        IList<long> SuppRRPow { get; }

        IList<long> SuppRRNonPow { get; }

        IList<long> SuppSendClockFactors { get; }

        byte UpdateTimeMode { get; set; }

        byte IoSyncRole { get; set; }

        bool HasUsingData { get; set; }

        int FrameMultiplier { get; set; }

        long FixedPhaseNumber { get; set; }

        int ProxyNumber { get; set; }

        int MinAutomaticUnsyncUpdateTime { get; }

        int SlotNumber { get; set; }

        int SubSlotNumber { get; set; }

        IList<long> PossibleReductionRatios { get; }

        void SetPossibleReductionRatios(IEnumerable<long> list);

        long ControllerLocalReductionRatio { get; set; }

        long DeviceLocalPhase { get; set; }

        long ControllerLocalPhase { get; set; }

        long Sequence { get; set; }

        long DataHoldFactor { get; set; }

        long WatchdogFactor { get; set; }

        int GroupNo { get; set; }

        int IOCRReferenceNo { get; set; }

        //Used for updating FrameIDs in the BL after PNPlanner call.
        bool FrameIdSet { get; set; }

        #endregion
    }
}