<Page x:Class="PNConfigTool.Views.Pages.ModuleConfigPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="模块配置"
      Loaded="Page_Loaded">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" Text="该页可配置所选模块的每个子模块" FontSize="18" FontWeight="Bold" Margin="10" HorizontalAlignment="Left"/>
        
        <!-- 主内容区域 - 表单 -->
        <Grid Grid.Row="1" Margin="20,10,20,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 简短标识 -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="简短标识" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox Grid.Row="0" Grid.Column="1" x:Name="ShortNameTextBox" 
                     Text="Standard, MRP, S2 redundancy, DR" 
                     Margin="0,5,0,5" Height="25"/>
            
            <!-- 说明 -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="说明" VerticalAlignment="Top" Margin="0,5,10,5"/>
            <TextBox Grid.Row="1" Grid.Column="1" x:Name="DescriptionTextBox" 
                     Text="ERTEC 200P Evaluation Kit, standard, RT, IRT, IsoM, MRP, Shared Device, S2, DR" 
                     TextWrapping="Wrap" AcceptsReturn="True" Height="50" Margin="0,5,0,5"/>
            
            <!-- 订货号 -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="订货号" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox Grid.Row="2" Grid.Column="1" x:Name="OrderNumberTextBox" 
                     Text="6ES7 195-3BE00-0YA1" 
                     Margin="0,5,0,5" Height="25"/>
                        
            <!-- GSDML 路径 -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="GSDML 路径" VerticalAlignment="Center" Margin="0,5,10,5"/>
            <TextBox Grid.Row="4" Grid.Column="1" x:Name="GSDMLPathTextBox" 
                     Text="C:\Users\<USER>\Documents\Siemens\STEP 7-Micro\WIN SMART\GSDML\GSDML-V2.43-Siemens-ERTEC200pEvalkit-20230301.xml" 
                     TextWrapping="Wrap" Height="60" Margin="0,5,0,5"/>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="2" Background="#F0F0F0">
            <!-- 左侧按钮 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="10">
                <Button x:Name="PreviousButton" Content="&lt; 上一步" Width="100" Padding="10,5" Margin="0,0,10,0" Click="PreviousButton_Click"/>
                <Button x:Name="NextButton" Content="下一步 >" Width="100" Padding="10,5" Click="NextButton_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page> 