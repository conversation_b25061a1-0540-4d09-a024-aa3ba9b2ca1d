/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigUtility.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Mrp;
using PNConfigLib.HWCNBL.MultipleMrp;
using PNConfigLib.HWCNBL.Tailor.AddressTailoring;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal enum SortOrder
    {
        APDU = 0,

        IO = 1,

        OI = 2
    }

    internal static class ConfigUtility
    {
        internal const int m_IdPortAutomaticSettings = 8;

        [Flags]
        public enum PDSubBlocks
        {
            CheckPeersSubBlock = 1,

            CheckLineDelaySubBlock = 2,

            CheckMauTypeSubBlock = 4,

            CheckLinkStateSubBlock = 8,

            CheckSyncDiffSubBlock = 16,

            CheckMauTypeDiffSubBlock = 32
        }

        private enum SortDirection
        {
            Ascending = 1,

            Descending = -1
        }
        /// <summary>
        /// </summary>
        /// <param name="actualConfiguration">the "bit array"</param>
        /// <param name="maxBit">the maximal 1 based bit to set</param>
        /// <param name="bitNr">the number of the bit to set</param>
        /// <param name="value">true or false</param>
        internal static void SetBit(ref int actualConfiguration, int maxBit, int bitNr, bool value)
        {
            if ((bitNr < maxBit) && (bitNr >= 0))
            {
                actualConfiguration |= (value ? 0x01 : 0x00) << bitNr;
            }
            else
            {
                throw new InvalidOperationException(string.Format(CultureInfo.InvariantCulture, "Not supported bit number {0} ! (bitNr = (min)1 - (max){1})", bitNr, maxBit));
            }
        }
        /// <summary>
        /// Generate CheckPeers-0x020A SubBlock
        /// </summary>
        /// <param name="port"></param>
        /// <param name="pdPortData">Port informations</param>
        /// <param name="connectedPorts">All the ports connected to the current port</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateCheckPeersBlock(
            DataModel.PCLObjects.Port port,
            PDPortData pdPortData,
            List<DataModel.PCLObjects.Port> connectedPorts)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckPeersSubBlock) == 0)
            {
                return new byte[0];
            }

            //Check if port is a programmable peer. If so peer port and station names must be empty.
            if (MachineTailorUtility.IsProgrammablePeerEnabled(port))
            {
                return GenerateEmptyCheckPeersBlock();
            }

            // only 1 partner port is allowed
            if (connectedPorts.Count != 1)
            {
                return new byte[0];
            }

            string peerPortID = null;
            string nameOfStation = null;

            foreach (DataModel.PCLObjects.Port connectedPort in connectedPorts)
            {
                peerPortID = GetPeerPortID(connectedPort);

                Interface connectedInterfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(connectedPort);
                if (connectedInterfaceSubmodule != null)
                {
                    try
                    {
                        GetPNNameOfStationWithAddressTailorCheck(port, connectedInterfaceSubmodule, out nameOfStation);
                    }
                    catch (Exception e)
                    {
                        Debug.Assert(false, e.Message + "\r\n" + e.StackTrace);
                        throw;
                    }
                }
            }

            CheckPeersSubBlockStruct checkPeersSubBlock = new CheckPeersSubBlockStruct();
            //There is not more than 1 peer port connected to this port (this is not a "Tool-changer" port)
            checkPeersSubBlock.NumberOfPeerPorts = connectedPorts.Count;

            if ((nameOfStation != null)
                && (peerPortID != null))
            {
                List<byte> data = new List<byte>();

                byte lengthPeerPortID = (byte)Encoding.ASCII.GetByteCount(peerPortID);
                data.Add(lengthPeerPortID);
                data.AddRange(Encoding.ASCII.GetBytes(peerPortID));

                byte lengthPeerChassisID = (byte)Encoding.ASCII.GetByteCount(nameOfStation);
                data.Add(lengthPeerChassisID);
                if (!string.IsNullOrEmpty(nameOfStation))
                {
                    data.AddRange(Encoding.ASCII.GetBytes(nameOfStation));
                }
                //append peerIDs to CheckPeers-0x020A SubBlock
                checkPeersSubBlock.AddSubblock(data.ToArray());
                int alignmentLen = BufferManager.Alignment(checkPeersSubBlock.ParaBlockLength, 4);
                if (alignmentLen != 0)
                {
                    byte[] alignment = new byte[alignmentLen];
                    //append alignment
                    checkPeersSubBlock.AddSubblock(alignment);
                }

                return checkPeersSubBlock.ToByteArray;
            }

            return new byte[0];
        }

        /// <summary>
        /// Generate CheckLineDelay-0x020B SubBlock
        /// </summary>
        /// <param name="pdPortData">Port informations</param>
        /// <param name="portModule">The current port</param>
        /// <param name="connectedPorts">All the ports connected to the current port</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateLineDelayBlock(
            ref PDPortData pdPortData,
            ref DataModel.PCLObjects.Port portModule,
            ref List<DataModel.PCLObjects.Port> connectedPorts)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckLineDelaySubBlock) == 0)
            {
                return new byte[0];
            }

            CheckLineDelaySubBlockStruct checkLineDelaySubBlock = new CheckLineDelaySubBlockStruct();

            uint irtSignalDelayTime =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtSignalDelayTime,
                    new AttributeAccessCode(),
                    0);

            // get local Port LocalRXPhyDelayInNs
            uint localPortRxDelay =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortRxDelay,
                    new AttributeAccessCode(),
                    0);

            // only 1 partner port is allowed
            uint remotePortTxDelay = 0;
            if (connectedPorts.Count == 1)
            {
                remotePortTxDelay =
                    connectedPorts[0].AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtPortTxDelay,
                        new AttributeAccessCode(),
                        0);
            }

            const int JitterReserved = 50;
            checkLineDelaySubBlock.LineDelay = irtSignalDelayTime + remotePortTxDelay + localPortRxDelay
                                               + JitterReserved;

            return checkLineDelaySubBlock.ToByteArray;
        }

        /// <summary>
        /// Generate CheckLinkState-0x021C SubBlock
        /// </summary>
        /// <param name="pdPortData">Port informations</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateLinkStateBlock(ref PDPortData pdPortData)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckLinkStateSubBlock) == 0)
            {
                return new byte[0];
            }

            CheckMauTypeSubBlockStruct checkLinkStateSubBlock = new CheckMauTypeSubBlockStruct();
            checkLinkStateSubBlock.ParaBlockType = 0x021C;
            checkLinkStateSubBlock.MAUType = 1;

            return checkLinkStateSubBlock.ToByteArray;
        }

        /// <summary>
        /// Generate CheckMAUType-0x020C SubBlock
        /// </summary>
        /// <param name="pdPortData">Port informations</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateMauTypeBlock(ref PDPortData pdPortData)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckMauTypeSubBlock) == 0)
            {
                return new byte[0];
            }

            CheckMauTypeSubBlockStruct checkMauTypeSubBlock = new CheckMauTypeSubBlockStruct();
            checkMauTypeSubBlock.ParaBlockType = 0x020C;
            checkMauTypeSubBlock.MAUType = pdPortData.MauType;

            return checkMauTypeSubBlock.ToByteArray;
        }

        /// <summary>
        /// Generate CheckMAUTypeDifference-0x021F SubBlock
        /// </summary>
        /// <param name="pdPortData">Port informations</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateMauTypeDifferenceBlock(ref PDPortData pdPortData)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckMauTypeDiffSubBlock) == 0)
            {
                return new byte[0];
            }

            CheckMauTypeSubBlockStruct checkMauTypeDifferenceSubBlock = new CheckMauTypeSubBlockStruct();
            checkMauTypeDifferenceSubBlock.ParaBlockType = 0x021F;
            checkMauTypeDifferenceSubBlock.MAUType = 1;

            return checkMauTypeDifferenceSubBlock.ToByteArray;
        }

        /// <summary>
        /// Generate CheckSyncDifference-0x021E SubBlock if necessary
        /// </summary>
        /// <param name="pdPortData">Port informations</param>
        /// <returns>The data block in a simple byte array format (default value is an empty array)</returns>
        internal static byte[] CreateSyncDifferenceBlock(ref PDPortData pdPortData)
        {
            if ((pdPortData.SubBlockToGenerate & PDSubBlocks.CheckSyncDiffSubBlock) == 0)
            {
                return new byte[0];
            }

            CheckSyncDifferenceSubBlockStruct checkSyncDifferenceSubBlock = new CheckSyncDifferenceSubBlockStruct();
            checkSyncDifferenceSubBlock.SetCableDelay(pdPortData.CableDelay);
            checkSyncDifferenceSubBlock.SetSyncMaster(pdPortData.SyncMaster);

            return checkSyncDifferenceSubBlock.ToByteArray;
        }

        internal static PdIrBeginEndData GeneratePdirBeginEndData(Interface interfaceSubmodule)
        {
            PdIrBeginEndData pdirBeginEndData = new PdIrBeginEndData();

            //Get StartOfReqFrameID and EndOfReqFrameID
            int startOfReqFrameID;
            int endOfReqFrameID;
            GetStartEndOfReqFrameIDsList(interfaceSubmodule, out startOfReqFrameID, out endOfReqFrameID);

            List<DataModel.PCLObjects.Port> ports = (List<DataModel.PCLObjects.Port>)interfaceSubmodule.GetPorts();
            pdirBeginEndData.SetStartOfReqFrameID(startOfReqFrameID);
            pdirBeginEndData.SetEndOfReqFrameID(endOfReqFrameID);
            pdirBeginEndData.SetPortCount((uint)ports.Count);

            List<DataModel.PCLObjects.Port> orderedPorts = (List<DataModel.PCLObjects.Port>)interfaceSubmodule.GetPortModulesSorted();

            foreach (DataModel.PCLObjects.Port port in orderedPorts)
            {
                List<PdIrBeginEndAssignment> beginEndAssignments = GetPdirBeginEndAssignments(interfaceSubmodule, port);

                bool dummyAssignmentNeeded;
                List<PdIrPhaseAssignment> phaseAssignments = GetPdirPhaseAssignments(
                    interfaceSubmodule,
                    port,
                    out dummyAssignmentNeeded,
                    beginEndAssignments.Count);

                if (dummyAssignmentNeeded)
                {
                    beginEndAssignments.Add(GetEmptyBeginEndAssignment());
                }

                // add number of assignments
                pdirBeginEndData.SetAssignmentCount((uint)beginEndAssignments.Count);

                // add begin end assignments
                foreach (PdIrBeginEndAssignment assignment in beginEndAssignments)
                {
                    pdirBeginEndData.Add(assignment.ToByteArray);
                }

                // add number of phases
                pdirBeginEndData.SetPhaseCount((uint)phaseAssignments.Count);
                // add phase assignments
                foreach (PdIrPhaseAssignment phase in phaseAssignments)
                {
                    pdirBeginEndData.Add(phase.ToByteArray);
                }
            }
            return pdirBeginEndData;
        }

        internal static ParameterDSStruct GeneratePdirFrameData(Interface interfaceSubmodule, int version)
        {
            ParameterDSStruct pdirFrameData = new ParameterDSStruct();
            pdirFrameData.ParaBlockType = 0x0207;
            pdirFrameData.ParaBlockVersion = version;

            AttributeAccessCode ac = new AttributeAccessCode();
            Enumerated pnIrtForwardingMode = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtForwardingMode, ac, null);
            PNIrtForwardingMode currForwardingMode = ac.IsOkay
                                     ? (PNIrtForwardingMode)pnIrtForwardingMode.DefaultValue
                                     : PNIrtForwardingMode.None;
            bool ieHasRelativeForwarding = currForwardingMode == PNIrtForwardingMode.Relative;
            if (version == 0x101)
            {
                // add frameDataProperties
                PdIrFrameDataProperties frameDataProperties = new PdIrFrameDataProperties();
                frameDataProperties.ForwardingMode = ieHasRelativeForwarding ? 1 : 0;

                SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
                if (syncDomain == null)
                {
                    Debug.Fail("SyncDomain of the interface cannot be found");
                    return null;
                }

                SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
                if (syncDomainBl == null)
                {
                    Debug.Fail("SyncDomainBusinessLogic cannot be retrieved.");
                    return null;
                }

                if (syncDomainBl.IsFFWActive)
                {
                    frameDataProperties.FastForwardingMulticastMACAdd = 2;
                }
                else if (syncDomainBl.IsAdvancedStartupModeActive)
                {
                    frameDataProperties.FastForwardingMulticastMACAdd = 1;
                }
                else
                {
                    frameDataProperties.FastForwardingMulticastMACAdd = 0;
                }

                if (!syncDomainBl.IsFragmentationActive)
                {
                    frameDataProperties.FragmentationMode = 0;
                }
                else if ((syncDomainBl.SendClockFactor == 1)
                         || (syncDomainBl.SendClockFactor == 2))
                {
                    frameDataProperties.FragmentationMode = 1;
                }
                else
                {
                    frameDataProperties.FragmentationMode = 2;
                }

                pdirFrameData.AddSubblock(frameDataProperties.ToByteArray);
            }
            List<PdIrFrameDataMain> listFrames = GetPdirFrameDataMain(interfaceSubmodule, ieHasRelativeForwarding);
            if (listFrames != null)
            {
                foreach (PdIrFrameDataMain pdirFrameDataMain in listFrames)
                {
                    // add frames
                    pdirFrameData.AddSubblock(pdirFrameDataMain.ToByteArray);
                }
            }
            return pdirFrameData;
        }

        internal static PdIrGlobalData GeneratePdirGlobalData(Interface interfaceSubmodule, int version)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            PdIrGlobalData globalData = new PdIrGlobalData();
            globalData.ParaBlockVersion = version;

            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (syncDomain == null)
            {
                Debug.Fail("SyncDomain of the interface cannot be found");
                return null;
            }

            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBl == null)
            {
                Debug.Fail("AcfSyncDomain cannot be retrieved.");
                return null;
            }

            globalData.IRDataID = GetIRDataIDOfInterface(interfaceSubmodule);

            // Actual Bridging Delay calculated by PNPlanner Preplanner (PNIrtSwitchActualBridgingDelay) should be 
            // used instead of PNIrtSwitchBridgingDelay
            globalData.BridgeDelay =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtSwitchActualBridgingDelay,
                    ac.GetNew(),
                    0);

            //Debug.Assert(globalData.BridgeDelay != 0, "globalData.BridgeDelay is 0");

            List<DataModel.PCLObjects.Port> ports = (List<DataModel.PCLObjects.Port>)interfaceSubmodule.GetPortModulesSorted();
            if (ports == null)
            {
                return globalData;
            }

            // Get the yellow time and use it globally in all ports of the interface.
            uint yellowVal = DomainManagementUtility.GetYellowTime(syncDomainBl);
            foreach (DataModel.PCLObjects.Port port in ports)
            {
                uint maxPortTxDelay = port.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortTxDelay,
                    ac.GetNew(),
                    0);
                uint maxPortRxDelay = port.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortRxDelay,
                    ac.GetNew(),
                    0);
                if (version == 0x0102)
                {
                    int portNr = port.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnPortNumber,
                        ac.GetNew(),
                        0);
                    int maxLineRxDelay = syncDomainBl.GetConfig2008MaxLineRxDelays(interfaceSubmodule, portNr);
                    globalData.AddPortData(maxPortTxDelay, maxPortRxDelay, (uint)maxLineRxDelay, yellowVal);
                }
                else
                {
                    globalData.AddPortData(maxPortTxDelay, maxPortRxDelay);
                }
            }

            return globalData;
        }

        internal static bool GetAllConnectedInterfacesSupportLldpNoD(
            Interface interfaceSubmodule,
            ref List<Interface> allInterfaces)
        {
            if (interfaceSubmodule == null)
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            Subnet subnet = interfaceSubmodule.Node.Subnet;
            //Check if interface does not connected to a subnet
            if (subnet == null)
            {
                if (allInterfaces.Contains(interfaceSubmodule))
                {
                    return true;
                }

                List<Interface> siblings = new List<Interface> { interfaceSubmodule };
                //Check if interface has any siblings
                foreach (Interface sibling in siblings)
                {
                    string typeName = sibling.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.TypeName,
                        ac,
                        string.Empty);
                    if (!typeName.Contains("PROFINET")
                        && !typeName.Contains("IE General"))
                    {
                        continue;
                    }

                    if (allInterfaces.Contains(sibling))
                    {
                        continue;
                    }
                    //Do not search siblings of a already searched siblings
                    allInterfaces.Add(sibling);
                    if (
                        !sibling.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnLLDPNoDSupported,
                            ac.GetNew(),
                            false)
                        || sibling.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnLLDPLegacyEnforce,
                            ac.GetNew(),
                            false))
                    {
                        return false;
                    }

                    if (!GetAllConnectedInterfacesSupportLldpNoD(sibling, ref allInterfaces))
                    {
                        return false;
                    }
                }
                if (!allInterfaces.Contains(interfaceSubmodule))
                {
                    allInterfaces.Add(interfaceSubmodule);
                }
                return true;
            }

            List<Interface> interfaceSubmodulesOfSubnet = NavigationUtilities.GetInterfacesOfSubnet(subnet);
            foreach (Interface interfaceSub in interfaceSubmodulesOfSubnet)
            {
                if (allInterfaces.Contains(interfaceSub))
                {
                    continue;
                }
                allInterfaces.Add(interfaceSub);
                if (
                    !interfaceSub.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnLLDPNoDSupported,
                        ac.GetNew(),
                        false)
                    || interfaceSub.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnLLDPLegacyEnforce,
                        ac.GetNew(),
                        false))
                {
                    return false;
                }

                List<Interface> siblings = new List<Interface> { interfaceSubmodule };

                foreach (Interface sibling in siblings)
                {
                    string typeName = sibling.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.TypeName,
                        ac,
                        string.Empty);
                    if (!typeName.Contains("PROFINET")
                        && !typeName.Contains("IE General"))
                    {
                        continue;
                    }

                    if (allInterfaces.Contains(sibling))
                    {
                        continue;
                    }
                    //Do not search siblings of a already searched siblings
                    allInterfaces.Add(sibling);
                    if (
                        !sibling.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnLLDPNoDSupported,
                            ac.GetNew(),
                            false)
                        || sibling.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnLLDPLegacyEnforce,
                            ac.GetNew(),
                            false))
                    {
                        return false;
                    }

                    if (!GetAllConnectedInterfacesSupportLldpNoD(sibling, ref allInterfaces))
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// Assigns the currently projected application phases.
        /// </summary>
        /// <param name="PNIORatio">the interface's current IO ratio</param>
        /// <param name="ioApplicationPhases">currently projected IO application phases</param>
        /// <param name="cbaApplicationPhases">currently projected CBA application phases</param>
        internal static void GetApplicationPhases(
            PNIORatio pnIORatio,
            out int[] ioApplicationPhases,
            out int[] cbaApplicationPhases)
        {
            switch (pnIORatio)
            {
                case PNIORatio.OneToZero:
                    ioApplicationPhases = new int[] { 1, 2, 3, 4, 5, 6, 7, 8 };
                    cbaApplicationPhases = null;
                    break;

                default:
                    ioApplicationPhases = null;
                    cbaApplicationPhases = new int[] { 1, 2, 3, 4, 5, 6, 7, 8 };
                    break;
            }
        }

        internal static byte[] GetBlockIdentifications(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return null;
            }

            int vendorID, deviceID;

            AttributeAccessCode ac = new AttributeAccessCode();
            if ((interfaceSubmodule.ParentObject != null)
                && interfaceSubmodule.ParentObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnCustomizationEnabled,
                    ac,
                    false))
            {
                vendorID =
                    interfaceSubmodule.ParentObject.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnVendorIdCustomized,
                        ac.GetNew(),
                        0);
                deviceID =
                    interfaceSubmodule.ParentObject.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnDeviceIdCustomized,
                        ac.GetNew(),
                        0);
            }
            else
            {
                vendorID = interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnVendorId,
                    ac.GetNew(),
                    0);
                deviceID = interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnDeviceId,
                    ac.GetNew(),
                    0);
            }
            int instanceID = PNInstanceIdUtilities.GetInstanceIdForPNIdentificationBlock(interfaceSubmodule);

            IdentificationBlock identificationBlock = new IdentificationBlock();
            identificationBlock.VendorID = vendorID;
            identificationBlock.DeviceID = deviceID;
            identificationBlock.InstanceID = instanceID;
            // create block

            return identificationBlock.ToByteArray;
        }

        /// <summary>
        /// Gets the Supported Synchronization Protocols of this submodule and the Controller
        /// Interface Submodule and returns the Common Protocols
        /// </summary>
        /// <returns>The list of the Common Protocols</returns>
        internal static List<PNIRTSupportedSyncProtocols> GetCommonSyncProtocols(Interface interfaceSubmodule)
        {
            List<PNIRTSupportedSyncProtocols> commonSyncProtocols = new List<PNIRTSupportedSyncProtocols>();

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);

            if (controllerInterfaceSubmodule != null)
            {
                // Get the related Composite Attribute
                AttributeAccessCode accessCode = new AttributeAccessCode();
                Enumerated controllerSyncProtocols =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSupportedSyncProtocols,
                        accessCode,
                        null);

                if (accessCode.IsOkay)
                {
                    foreach (int syncPrtocol in controllerSyncProtocols.List)
                    {
                        // Get the value of the enum.
                        PNIRTSupportedSyncProtocols syncProtocol = (PNIRTSupportedSyncProtocols)Enum.Parse(
                            typeof(PNIRTSupportedSyncProtocols),
                            syncPrtocol.ToString(CultureInfo.InvariantCulture),
                            true);

                        // Check whether both Submodules support the Protocol
                        if (GetSuppSyncProtocols(interfaceSubmodule).Contains(syncProtocol))
                        {
                            // Add the class to the global list.
                            commonSyncProtocols.Add(syncProtocol);
                        }
                    }
                }
            }

            return commonSyncProtocols;
        }

        /// <summary>
        /// Returns the IRDataID of a device interface submodule. The method returns null if the device doesn't have
        /// any RTC3 frames.
        /// </summary>
        internal static byte[] GetIRDataIDOfDevice(Interface deviceInterfaceSubmodule)
        {
            List<IPNFrameData> pnFrameDataList =
                NavigationUtilities.GetPNFrameDataListOfIODevice(deviceInterfaceSubmodule.PNIOD, false);
            if ((null == pnFrameDataList)
                || (pnFrameDataList.Count <= 0)
                || (pnFrameDataList[0].FrameClass != (long)PNIOFrameClass.Class3Frame))
            {
                return null;
            }

            return GetIRDataIDOfInterface(deviceInterfaceSubmodule);
        }

        internal static byte[] GetIrInfoBlockPlus(Interface interfaceSubmodule, Interface controllerInterfaceSubmodule)
        {
            IRInfoBlockStructPlus irInfoBlockStructPlus = new IRInfoBlockStructPlus();
            irInfoBlockStructPlus.irInfoStructBody.IRDataUUID = GetIRDataIDOfDevice(interfaceSubmodule);

            GetDFPIOCREntries(irInfoBlockStructPlus.irInfoStructBody, controllerInterfaceSubmodule, interfaceSubmodule);
            irInfoBlockStructPlus.UpdateBlockLength();

            return irInfoBlockStructPlus.ToByteArray;
        }

        internal static void GetDFPIOCREntries(IRInfoBlockStructBody irInfoBlockStructBody,
           Interface controllerInterfaceSubmodule, Interface interfaceSubmodule)
        {
            List<IPNFrameData> frames =
                (List<IPNFrameData>)controllerInterfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.GetFramesOfInterface(interfaceSubmodule);
            if (frames != null)
            {
                foreach (IPNFrameData frame in frames)
                {
                    IPNDfpFrameData dfpFrame = frame as IPNDfpFrameData;
                    if (dfpFrame == null)
                    {
                        continue;
                    }

                    // Create a DfpIOCREntry for the related subframe of the dfp frame
                    IPNSubframeData subframe = Utility.GetSubframeOfInterface(interfaceSubmodule, dfpFrame);
                    DfpIocrEntryStruct dfpIocrEntry = new DfpIocrEntryStruct();
                    dfpIocrEntry.IOCRReference = dfpFrame.FrameDirection ==
                        (byte)PNPlannerFrameDirection.InputFrame ? (UInt16)1 : (UInt16)2;
                    dfpIocrEntry.SubframeOffset = GetSubframeOffset(dfpFrame, subframe.SubframeId);

                    // Fill the subframe data
                    dfpIocrEntry.SubFrameData.Position = subframe.SubframeId;
                    dfpIocrEntry.SubFrameData.DataLength = subframe.SubframeLength;

                    irInfoBlockStructBody.AddDfpIocrEntry(dfpIocrEntry);
                }
            }
        }

        /// <summary>
        /// Calculates the subframe offset (start of the data) for a specific subframe of the dfp frame.
        /// This equation is used: 
        /// subframeOffset = (NumPrevSubframes + 1) * subframeappendix + PreviousDataLength + 
        ///                  padding at the beginning of the subframe
        /// NumPrevSubframes = in inbound direction, number of the subframes which have smaller subframe id.
        ///                    in outbound direction, number of the subframes which have larger subframe id.
        /// PreviousDataLength = in inbound direction, data length of the all subframes which have smaller subframe id.
        ///                      in outbound direction, data length of the all subframes which have larger subframe id.
        /// </summary>
        private static UInt16 GetSubframeOffset(IPNDfpFrameData dfpFrame, int subframeId)
        {
            int prevDataLength = 0;
            int numPrevSubframes = 0;
            switch (dfpFrame.FrameDirection)
            {
                case (byte)PNPlannerFrameDirection.InputFrame:
                    for (int i = 1; i < subframeId; i++)
                    {
                        Debug.Assert(dfpFrame.Subframes[i] != null, "Subframes are built incorrectly.");
                        if (dfpFrame.Subframes[i] == null)
                        {
                            continue;
                        }
                        numPrevSubframes++;
                        prevDataLength += dfpFrame.Subframes[i].SubframeLength;
                    }
                    break;
                case (byte)PNPlannerFrameDirection.OutputFrame:
                    int largestSubframeId = dfpFrame.Subframes.Keys[dfpFrame.Subframes.Count - 1];
                    for (int i = largestSubframeId; i > subframeId; i--)
                    {
                        Debug.Assert(dfpFrame.Subframes[i] != null, "Subframes are built incorrectly.");
                        if (dfpFrame.Subframes[i] == null)
                        {
                            continue;
                        }
                        numPrevSubframes++;
                        prevDataLength += dfpFrame.Subframes[i].SubframeLength;
                    }
                    break;
            }

            int sfBeginNumPaddingBytes = 0;
            return (UInt16)((numPrevSubframes + 1) * 6 + prevDataLength + sfBeginNumPaddingBytes);
        }

        /// <summary>
        /// Fills the PDInterfaceAdjust of the given interface submodule if it is supported.
        /// </summary>
        internal static byte[] GetPDInterfaceAdjustBlock(Interface interfaceSubmodule, out bool returnValue)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            returnValue = false;

            // If the interface submodule doesn't support lldp nod, don't fill the block.
            if (
                !interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnLLDPNoDSupported,
                    ac,
                    false))
            {
                return null;
            }

            // Create the PDInterfaceAdjust with the info read.
            PDInterfaceAdjustStruct pdInterfaceAdjust = new PDInterfaceAdjustStruct();
            List<Interface> allInterfaces = new List<Interface>();

            bool multiInterfaceModeNameOfDevice = GetAllConnectedInterfacesSupportLldpNoD(
                interfaceSubmodule,
                ref allInterfaces);

            pdInterfaceAdjust.MultiInterfaceModeNameOfDevice = multiInterfaceModeNameOfDevice ? 1 : 0;
            returnValue = true;
            return pdInterfaceAdjust.ToByteArray;
        }

        /// <summary>
        /// The method fills the parameter data block PDInterfaceMrpDataAdjust for Config2002 and Config2003.
        /// </summary>
        /// <param name="interfaceSubmodule">The owner of the Dataset</param>
        /// <param name="returnValueOk">if the prm block is to be generated</param>
        /// <returns>
        /// PDInterfaceMrpDataCheck 0x0213 Parameter Block if it is to be generated,
        /// otherwise an empty byte array
        /// </returns>
        internal static byte[] GetPDInterfaceMrpDataAdjust(Interface interfaceSubmodule, out bool returnValueOk)
        {
            returnValueOk = false;
            if (interfaceSubmodule == null)
            {
                return new byte[0];
            }

            PNMrpRole PNMrpRole = PNMrpRole.NotInRing;

            //Check if the PDInterfaceMrpDataAdjust parameter block is to be generated
            returnValueOk = HasPDInterfaceMrpDataAdjust(interfaceSubmodule, ref PNMrpRole);

            if (returnValueOk)
            {
                //If multiple MRP or norm manager auto is available then new MRP blocks are generated
                bool isMrpBlocksV11 = MultipleMrpUtilities.IsMrpBlocksV11(interfaceSubmodule);

                if (isMrpBlocksV11)
                {
                    return MultipleMrpConfigUtility.GetPDInterfaceMultipleMrpDataAdjust(interfaceSubmodule);
                }

                AttributeAccessCode ac = new AttributeAccessCode();
                PDInterfaceMrpDataAdjustStruct mrpDataAdjustBlock = new PDInterfaceMrpDataAdjustStruct();
                mrpDataAdjustBlock.ParaBlockType = 0x0211;

                mrpDataAdjustBlock.MrpDomainUUID = MultipleMrpConfigUtility.GenerateMrpUuid(interfaceSubmodule, false);
                MrpDomainInstance mediaRedDomain = null;
                if ((interfaceSubmodule.MrpDomainInstances != null) && (interfaceSubmodule.MrpDomainInstances.Count > 0))
                {
                    mediaRedDomain = interfaceSubmodule.MrpDomainInstances.First();
                }
                if (mediaRedDomain == null)
                {
                    mrpDataAdjustBlock.MrpDomainName = MultipleMrpConfigUtility.s_MrpDomainDefaultName;
                }
                else
                {
                    bool allowLeadingDigits = interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnAllowLeadingNumInNoI,
                                                    new AttributeAccessCode(), false);
                    string validatedName;
                    if (PNNameOfStationConverter.EncodePNNameOfStation(
                        AttributeUtilities.GetName(mediaRedDomain), null, null, out validatedName, allowLeadingDigits)
                        != PNNameOfStationConverter.DnsNameConvertStatus.NoError)
                    {
                        Debug.Fail("unconvertible Domain Name.");
                    }

                    mrpDataAdjustBlock.MrpDomainName =
                        (string.IsNullOrEmpty(validatedName)) ? MultipleMrpConfigUtility.s_MrpDomainDefaultName : validatedName;
                }

                #region SubBlocks

                switch (PNMrpRole)
                {
                    case PNMrpRole.NotInRing:
                        mrpDataAdjustBlock.MrpRole = 0;
                        break;

                    case PNMrpRole.Client:
                        mrpDataAdjustBlock.MrpRole = 1;

                        //generate MrpClientParams-0x0217 SubBlock
                        MrpClientParamsSubBlockStruct clientParams = new MrpClientParamsSubBlockStruct();
                        clientParams.MrpLNKdownT = 20;
                        clientParams.MrpLNKupT = 20;
                        clientParams.MrpLNKNRmax = 4;
                        //append MrpClientParams-0x0217 SubBlock
                        mrpDataAdjustBlock.AddSubblock(clientParams.ToByteArray);

                        //don't a generate MrpRTModeClientData-0x021D SubBlock (MRRT not supported)
                        break;

                    case PNMrpRole.Manager:
                        mrpDataAdjustBlock.MrpRole = 2;

                        //generate MrpManagerParams-0x0216 SubBlock
                        MrpManagerParamsSubBlockStruct managerParams = new MrpManagerParamsSubBlockStruct();
                        managerParams.MrpPrio =
                            interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnMrpManagerPriority,
                                ac.GetNew(),
                                0x8000);
                        if (!ac.IsOkay)
                        {
                            int vendorId =
                                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                    InternalAttributeNames.PnVendorId,
                                    new AttributeAccessCode(),
                                    0x2A);
                            if (vendorId == 0x2A)
                            {
                                ac.Reset();
                                uint operatingMode =
                                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                                        InternalAttributeNames.PnIoOperatingMode,
                                        ac,
                                        0);
                                if (ac.IsOkay)
                                {
                                    if (operatingMode == 2)
                                    {
                                        managerParams.MrpPrio = 0xA000;
                                    }
                                    else
                                    {
                                        managerParams.MrpPrio = 0x9000;
                                    }
                                }
                            }
                        }

                        managerParams.MrpTOPchgT = 1;
                        managerParams.MrpTOPNRmax = 3;
                        managerParams.MrpTSTshortT = 10;
                        managerParams.MrpTSTdefaultT = 20;
                        managerParams.MrpTSTNRmax = 3;
                        //append MrpManagerParams-0x0216 SubBlock
                        mrpDataAdjustBlock.AddSubblock(managerParams.ToByteArray);

                        //Don't generate a MrpRTModeManagerData-0x0218 SubBlock (MRRT not supported)
                        break;

                    case PNMrpRole.NormManagerAuto:
                        mrpDataAdjustBlock.MrpRole = 3;

                        //generate MrpManagerParams-0x0216 SubBlock
                        MrpManagerParamsSubBlockStruct managerAutoParams = new MrpManagerParamsSubBlockStruct();
                        managerAutoParams.MrpPrio =
                            interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.PnMrpManagerPriority,
                                ac.GetNew(),
                                0x8000);
                        if (!ac.IsOkay)
                        {
                            int vendorId =
                                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                    InternalAttributeNames.PnVendorId,
                                    new AttributeAccessCode(),
                                    0x2A);
                            if (vendorId == 0x2A)
                            {
                                ac.Reset();
                                uint operatingMode =
                                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                                        InternalAttributeNames.PnIoOperatingMode,
                                        ac,
                                        0);
                                if (ac.IsOkay)
                                {
                                    if (operatingMode == 2)
                                    {
                                        managerAutoParams.MrpPrio = 0xA000;
                                    }
                                    else
                                    {
                                        managerAutoParams.MrpPrio = 0x9000;
                                    }
                                }
                            }
                        }

                        managerAutoParams.MrpTOPchgT = 1;
                        managerAutoParams.MrpTOPNRmax = 3;
                        managerAutoParams.MrpTSTshortT = 10;
                        managerAutoParams.MrpTSTdefaultT = 20;
                        managerAutoParams.MrpTSTNRmax = 3;
                        //append MrpManagerParams-0x0216 SubBlock
                        mrpDataAdjustBlock.AddSubblock(managerAutoParams.ToByteArray);

                        //Don't generate a MrpRTModeManagerData-0x0218 SubBlock (MRRT not supported)
                        break;

                    default:
                        return new byte[0];
                }

                #endregion

                return mrpDataAdjustBlock.ToByteArray;
            }
            return new byte[0];
        }

        /// <summary>
        /// The method fills the parameter data block PDInterfaceMrpDataCheck for
        /// Config2002 and Config2003.
        /// </summary>
        /// <param name="interfaceSubmodule">The owner of the Dataset</param>
        /// <param name="returnValueOk">if the prm block is to be generated</param>
        /// <returns>
        /// PDInterfaceMrpDataCheck 0x0213 Parameter Block if it is to be generated,
        /// otherwise an empty byte array
        /// </returns>
        internal static byte[] GetPDInterfaceMrpDataCheck(Interface interfaceSubmodule, out bool returnValueOk)
        {
            PNMrpRole PNMrpRole = PNMrpRole.NotInRing;
            bool mrpDiagnosis = false;

            //Check if the PDInterfaceMrpDataCheck parameter block is to be generated
            returnValueOk = HasPDInterfaceMrpDataCheck(interfaceSubmodule, ref PNMrpRole, ref mrpDiagnosis);

            if (returnValueOk)
            {
                //generate PDInterfaceMrpDataCheck PrmBlock 0x0213

                //If multiple MRP is available then new MRP blocks are generated
                bool isMrpBlocksV11 = MultipleMrpUtilities.IsMrpBlocksV11(interfaceSubmodule);

                if (isMrpBlocksV11)
                {
                    return MultipleMrpConfigUtility.GetPDInterfaceMultipleMrpDataCheck(interfaceSubmodule, mrpDiagnosis);
                }

                PDInterfaceMrpDataCheckStruct mrpDataCheckBlock = new PDInterfaceMrpDataCheckStruct();
                mrpDataCheckBlock.ParaBlockType = 0x0213;
                mrpDataCheckBlock.MrpDomainUUID = MultipleMrpConfigUtility.GenerateMrpUuid(interfaceSubmodule, false);

                switch (PNMrpRole)
                {
                    case PNMrpRole.NotInRing:
                        mrpDataCheckBlock.MrpCheckMediaRedundancyManager = false;
                        mrpDataCheckBlock.MrpCheckMrpDomainUUID = false;
                        break;
                    case PNMrpRole.Client:
                        mrpDataCheckBlock.MrpCheckMediaRedundancyManager = false;
                        mrpDataCheckBlock.MrpCheckMrpDomainUUID = mrpDiagnosis;
                        break;
                    case PNMrpRole.Manager:
                    case PNMrpRole.NormManagerAuto:
                        mrpDataCheckBlock.MrpCheckMediaRedundancyManager = mrpDiagnosis;
                        mrpDataCheckBlock.MrpCheckMrpDomainUUID = mrpDiagnosis;
                        break;
                    default:
                        return new byte[0];
                }

                return mrpDataCheckBlock.ToByteArray;
            }
            return new byte[0];
        }

        /// <summary>
        /// Fills the PDIRSubframeData of the given interface submodule.
        /// </summary>
        /// <returns>
        /// PDIRSubframeData as byte[].
        /// If the interface submodule does not have any dfp frames, the method returns null.
        /// </returns>
        internal static byte[] GetPDIRSubframeData(Interface interfaceSubmodule)
        {
            PdirSubframeData pdirSubframeData = new PdirSubframeData();

            // Get the controller interface submodule
            bool isControllerInterfaceSubmodule = interfaceSubmodule.ParentObject is CentralDevice;
            Interface controllerInterfaceSubmodule = isControllerInterfaceSubmodule
                                                         ? interfaceSubmodule
                                                         : NavigationUtilities.GetControllerOfDevice(
                                                             interfaceSubmodule);
            if (controllerInterfaceSubmodule == null)
            {
                return null;
            }

            List<IPNFrameData> frames;

            // Get all frames of the interface submodule

            if (!isControllerInterfaceSubmodule)
            {
                frames = (List<IPNFrameData>)interfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.GetFramesOfInterface(
                    interfaceSubmodule);
            }
            else
            {
                frames = (List<IPNFrameData>)
                    controllerInterfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.GetFramesOfController(
                        controllerInterfaceSubmodule);
            }


            if (frames == null)
            {
                return null;
            }

            FillOutputBlocks(interfaceSubmodule, pdirSubframeData, frames);
            return pdirSubframeData.NumberOfSubframeBlocks > 0 ? pdirSubframeData.ToByteArray : null;
        }

        private static void FillOutputBlocks(
            Interface interfaceSubmodule,
            PdirSubframeData pdirSubframeData,
            List<IPNFrameData> frames)
        {
            // Get all dfp frames and fill the blocks
            foreach (IPNFrameData frame in frames)
            {
                IPNDfpFrameData dfpFrame = frame as IPNDfpFrameData;
                if (dfpFrame == null)
                {
                    continue;
                }

                // A dfp frame is found. Create a new subframe block and fill it with the values of the frame.
                SubframeBlock subframeBlock = new SubframeBlock { FrameID = (int)dfpFrame.FrameID };
                SfiocrProperties props = subframeBlock.Properties;
                props.DistributedWatchDogFactor = (int)dfpFrame.DistributedWdFactor;
                props.RestartFactorForDistributedWD = (int)dfpFrame.DistributedRestartFactor;

                IPNSubframeData pnSubframeData = Utility.GetSubframeOfInterface(interfaceSubmodule, dfpFrame);
                // If found, get the subframe id, else use 0 (means that it is an end node).
                props.DFPMode = pnSubframeData == null ? 0 : pnSubframeData.SubframeId;

                props.DFPRedundantPathLayout = dfpFrame.DfpRedundantPathLayout ? 1 : 0;
                props.DFPType = dfpFrame.FrameDirection == (byte)PNPlannerFrameDirection.InputFrame ? 0 : 1;
                props.SFCRC16 = Convert.ToInt32(dfpFrame.SfCrc16, CultureInfo.InvariantCulture);

                // Check the frame direction and sort the subframe list according to the subframe ids.
                IList<IPNSubframeData> listToEnumerate = dfpFrame.Subframes.Values;
                if (dfpFrame.FrameDirection == (byte)PNPlannerFrameDirection.OutputFrame)
                {
                    listToEnumerate = new List<IPNSubframeData>();
                    for (int i = dfpFrame.Subframes.Values.Count - 1; i >= 0; i--)
                    {
                        listToEnumerate.Add(dfpFrame.Subframes.Values[i]);
                    }
                }

                // Create a subframedata for each subframe)
                foreach (IPNSubframeData subframe in listToEnumerate)
                {
                    SubframeData subframeData = new SubframeData
                    {
                        Position = subframe.SubframeId,
                        DataLength = subframe.SubframeLength
                    };
                    subframeBlock.AddSubframeData(subframeData);
                }

                pdirSubframeData.AddSubframeBlock(subframeBlock);
            }
        }

        /// <summary>
        /// Fills the PDPortMrpData adjust block of Config 2002 and 2003.
        /// Only filled for the ports which are part of the ring. False is returned with
        /// returnValueOk when the method is called with a port which does not support 
        /// mrp or currently is not part of the ring.
        /// </summary>
        /// <param name="portSubmodule"></param>
        /// <param name="returnValueOk">Output parameter. True, if the block can be filled 
        /// successfully.</param>
        /// <returns>Byte array which contains the Config info</returns>
        internal static byte[] GetPDPortMrpDataAdjust(DataModel.PCLObjects.Port portSubmodule,
            out bool returnValueOk)
        {
            // Does the interface supports ring configuration
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);

            returnValueOk = HasPDPortMrpDataAdjust(portSubmodule, interfaceSubmodule);
            if (returnValueOk)
            {
                bool isMrpBlocksV11 = MultipleMrpUtilities.IsMrpBlocksV11(interfaceSubmodule);


                if (isMrpBlocksV11)
                {
                    return MultipleMrpConfigUtility.GetPDPortMrpDataAdjust(portSubmodule, out returnValueOk);
                }

                //generate PDPortMrpDataAdjust PrmBlock
                PDPortMrpDataAdjustStruct mrpDataAdjustBlock = new PDPortMrpDataAdjustStruct();

                mrpDataAdjustBlock.ParaBlockVersion = 0x0100;
                mrpDataAdjustBlock.MrpDomainUUID = MultipleMrpConfigUtility.GenerateMrpUuid(interfaceSubmodule, false);
                return mrpDataAdjustBlock.ToByteArray;

            }
            return new byte[0];
        }

        /// <summary>
        /// function checks whether the port has PDPortMrpDataAdjust block
        /// as this is a very costly method call, it uses compile caching for future calls
        /// </summary>
        internal static bool HasPDPortMrpDataAdjust(DataModel.PCLObjects.Port portSubmodule, Interface interfaceSubmodule)
        {
            bool returnValueOk = false;

            AttributeAccessCode ac = new AttributeAccessCode();
            // Does the interface supports ring configuration
            if (interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnMrpSupported, ac, false))
            {
                // Is the mrp for the interface activated
                if (interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                        InternalAttributeNames.PnMrpRole, ac.GetNew(), (UInt32)PNMrpRole.Client)
                    != (UInt32)PNMrpRole.NotInRing)
                {
                    // Does the port support mrp
                    if (portSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnMrpIsDefaultRingPort, ac, false) ||
                        portSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnMrpSupportsRingConfig, ac, false))
                    {
                        // Is the port currently part of the ring
                        IMethodData methodData = new MethodData();
                        methodData.Name = GetSelectedRingPorts.Name;
                        interfaceSubmodule.BaseActions.CallMethod(methodData);
                        List<DataModel.PCLObjects.Port> selectedRingPorts = (List<DataModel.PCLObjects.Port>)
                            methodData.Arguments[GetSelectedRingPorts.SelectedRingPorts];
                        if ((selectedRingPorts != null) && selectedRingPorts.Contains(portSubmodule))
                        {
                            returnValueOk = true;
                        }
                    }
                }
                else if (MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule))
                {
                    int instanceNumber = MultipleMrpUtilities.GetInstanceOfPort(portSubmodule);

                    if ((instanceNumber > 0) && (MultipleMrpUtilities.GetMultipleRole(
                        interfaceSubmodule, instanceNumber) != PNMrpRole.NotInRing))
                    {
                        returnValueOk = true;
                    }
                }
            }

            return returnValueOk;
        }

        /// <summary>
        /// Get port name in the following format: "partnerPort-001.NameOfStation".
        /// Required for StationNameAlias table
        /// </summary>
        /// <param name="port">port</param>
        /// <param name="partnerPort">partner</param>
        /// <returns>formatted name of port</returns>
        internal static string GetPortName(DataModel.PCLObjects.Port port, DataModel.PCLObjects.Port partnerPort)
        {
            StringBuilder sb = new StringBuilder("port-");

            int portNumber = partnerPort.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnPortNumber,
                new AttributeAccessCode(),
                1);
            sb.Append(portNumber.ToString("000", CultureInfo.InvariantCulture));

            if (partnerPort.ParentObject is Module)
            {
                int posNumber = partnerPort.ParentObject.AttributeAccess.GetAnyAttribute<Int32>(
                    InternalAttributeNames.PositionNumber,
                    new AttributeAccessCode(),
                    0);
                sb.Append("-");
                sb.Append(posNumber.ToString("00000", CultureInfo.InvariantCulture));
            }

            PclObject headModuleOfPartnerPort = partnerPort.GetDevice();
            if (IsModularScalance(headModuleOfPartnerPort)
                && partnerPort.ParentObject is Interface)
            {
                int posNumber = headModuleOfPartnerPort.AttributeAccess.GetAnyAttribute<Int32>(
                    InternalAttributeNames.PositionNumber,
                    new AttributeAccessCode(),
                    0);
                sb.Append("-");
                sb.Append(posNumber.ToString("00000", CultureInfo.InvariantCulture));
            }

            Interface partnerInterfaceSubmodule = partnerPort.GetInterface();
            string nameOfStation;

            GetPNNameOfStationWithAddressTailorCheck(port, partnerInterfaceSubmodule, out nameOfStation);
            if (!string.IsNullOrEmpty(nameOfStation))
            {
                sb.Append(@".");
                sb.Append(nameOfStation);
            }
            return sb.ToString();
        }

        /// <summary>
        /// RtClass from pnFrameDataList
        /// </summary>
        internal static PNRTClass GetRtClassfromFrameDataList(Interface interfaceSubmodule)
        {
            //RT
            if (!IsSynchronized(interfaceSubmodule, PNInterfaceType.IODevice))
            {
                return PNRTClass.Rt;
            }

            List<IPNFrameData> pnFrameDataList =
                NavigationUtilities.GetPNFrameDataListOfIODevice(interfaceSubmodule.PNIOD, false);
            if ((pnFrameDataList == null)
                || (pnFrameDataList.Count <= 0))
            {
                throw new PNFunctionsException(
                    "Could not found any PNFrameData objects aggregated on the device interface submodule.");
            }
            return pnFrameDataList[0].FrameClass == (long)PNIOFrameClass.Class3Frame
                       ? PNRTClass.IrtTop
                       : PNRTClass.IrtFlex;
        }

        internal static byte[] GetConfig2002Pdev1ABlockDs802C(Interface ieInterface, out bool ok)
        {
            ok = IsControllerIrtTop(ieInterface);
            if (!ok)
            {
                return null; // block is solely created in case of an IRT top configuration
            }

            SyncDomain syncDomain = ieInterface.SyncDomain;
            if (syncDomain == null)
            {
                Debug.Fail("SyncDomain of the interface cannot be found");
                ok = false;
                return null;
            }
            if (syncDomain.SyncDomainBusinessLogic == null)
            {
                Debug.Fail("SyncDomainBL cannot be loaded");
                ok = false;
                return null;
            }

            // Don't generate the block if the interface doesn't have any frames (sent, received or forwarded)
            List<IPNPlannerOutputFrame> frameBlocks = (List<IPNPlannerOutputFrame>)syncDomain.SyncDomainBusinessLogic.GetConfig2008FrameBlocks(ieInterface);
            if (frameBlocks.Count == 0)
            {
                ok = false;
                return null;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            // Version of blocks are determined with startup mode and forwarding mode
            // If advanced startup mode is supported or forwarding mode is not none then new versions should be used
            Enumerated pnIrtForwardingMode = ieInterface.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIrtForwardingMode, ac, null);
            PNIrtForwardingMode currForwardingMode;
            if (ac.IsOkay)
            {
                currForwardingMode = (PNIrtForwardingMode)pnIrtForwardingMode.DefaultValue;
            }
            else
            {
                currForwardingMode = PNIrtForwardingMode.None;
            }


            bool pdirxxxDataNewVersion = currForwardingMode != PNIrtForwardingMode.None;

            if (!pdirxxxDataNewVersion)
            {
                List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(ieInterface);
                //if advanced startup mode is supported then never version should also be used
                pdirxxxDataNewVersion = startupModes.Contains(PNIrtArStartupMode.Advanced);
            }

            // Generate PDIRGlobalData
            int pdirGlobalDataVersion = pdirxxxDataNewVersion ? 0x102 : 0x101;
            PdIrGlobalData globalData = GeneratePdirGlobalData(ieInterface, pdirGlobalDataVersion);

            // Generate PDIRFrameData
            int pdirFrameDataVersion = pdirxxxDataNewVersion ? 0x101 : 0x100;
            ParameterDSStruct frameData = GeneratePdirFrameData(ieInterface, pdirFrameDataVersion);

            //Generate PDIRBeginEndData
            PdIrBeginEndData beginEndData = GeneratePdirBeginEndData(ieInterface);

            #region complete block

            //**************************************************************************************
            PdIrData pdIrData = new PdIrData();
            // insert slot number
            PclObject headModule = ieInterface.GetDevice();
            Debug.Assert(headModule != null, "Head module of the interface cannot be retrieved.");
            string deviceVersion = headModule.AttributeAccess.GetAnyAttribute<string>(
              InternalAttributeNames.FwVersion,
              new AttributeAccessCode(),
              null);
            bool isDeviceVersion31orUpper = CheckDeviceVersion(deviceVersion);
           
            if (isDeviceVersion31orUpper)
            {
                pdIrData.SlotNumber = 0;
            }
            else
            {
                pdIrData.SlotNumber = headModule == null
                                      ? 0
                                      : headModule.AttributeAccess.GetAnyAttribute<int>(
                                          InternalAttributeNames.PositionNumber,
                                          ac.GetNew(),
                                          0);
                if (headModule != null)
                {
                    int cpuRtSlotNumber = headModule.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.CpuRtSlotNumber, ac.GetNew(), 0);
                    if (ac.IsOkay)
                    {
                        pdIrData.SlotNumber = cpuRtSlotNumber;
                    }
                }
            }
           
            // insert PN subslot number
            pdIrData.SubSlotNumber =
                ieInterface.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnSubslotNumber, ac.GetNew(), 0);
            // append global data block
            if (globalData != null)
            {
                pdIrData.AddRecordData(globalData.ToByteArray);
            }
            // append frame data block
            if (frameData != null)
            {
                pdIrData.AddRecordData(frameData.ToByteArray);
            }
            // append begin end data block
            if (beginEndData != null)
            {
                pdIrData.AddRecordData(beginEndData.ToByteArray);
            }

            #endregion

            // return complete PDIR block
            return pdIrData.ToByteArray;
        }

        internal static bool CheckDeviceVersion(string deviceVersion)
        {
            FwVersion fwVersion = HWCNBL.Utilities.AttributeUtilities.MapVersion(deviceVersion);
            return fwVersion.CompareTo(FwVersion.V3_1) >= 0;
        }
        internal static byte[] GetConfig2002Pdev1ABlockDs8070(Interface ieInterface, out bool ok)
        {
            string attribute = InternalAttributeNames.PnNetworkComponentDiagnosisSupported;
            AttributeAccessCode acode = new AttributeAccessCode();
            ok = ieInterface.AttributeAccess.GetAnyAttribute<bool>(attribute, acode, false);

            if (!ok)
            {
                return null;
            }

            PdncDataCheckStruct pdncDataCheck = new PdncDataCheckStruct();

            pdncDataCheck.MaintenanceRequiredDropBudget = 0x80000003;
            pdncDataCheck.MaintenanceDemandedDropBudget = 0x8000000A;
            pdncDataCheck.ErrorDropBudget = 0x00000000;

            return pdncDataCheck.ToByteArray;
        }

        internal static int GetSlotNumberOfPort(
            DataModel.PCLObjects.Port portSubmodule)
        {
            // get InterfaceSubmodule to PortSubmodule
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portSubmodule);
            if (interfaceSubmodule == null)
            {
                Debug.Fail("Interfacesubmodule can not be reached from Port:" + portSubmodule);
                return -1;
            }
            PclObject module = NavigationUtilities.GetContainer(interfaceSubmodule);
            if (module == null)
            {
                Debug.Fail("Container can not be reached from Interfacesubmodule:" + interfaceSubmodule);
                return -1;
            }

            int slotNumber = Utility.GetSlotNumber(module);
            // Check if the InterfaceSubmodule of the Port is a Device InterfaceSubmodule
            if (Utility.IsProfinetDeviceInterfaceSubmodule(interfaceSubmodule))
            {
                PclObject mediaModule = PNNavigationUtility.GetMediaModuleOfPort(portSubmodule);
                if (mediaModule != null)
                {
                    slotNumber = mediaModule.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PositionNumber,
                        new AttributeAccessCode(),
                        -1);
                }
                else
                {
                    slotNumber =
                        module.AttributeAccess.GetAnyAttribute<int>(
                            "PNIoSlotNumberForDecentralPDevDatasets",
                            new AttributeAccessCode(),
                            slotNumber);
                }
            }
            return slotNumber;
        }

        internal static void GetStartEndOfReqFrameIDsList(
            Interface interfaceSubmodule,
            out int startOfReqFrameID,
            out int endOfReqFrameID)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            startOfReqFrameID = ushort.MaxValue;
            endOfReqFrameID = 0;
            // Get all frames independent from the absolute or relative time mode
            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (syncDomain == null)
            {
                return;
            }
            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBl == null)
            {
                return;
            }
            List<IPNPlannerOutputFrame> frameBlocks = (List<IPNPlannerOutputFrame>)syncDomainBl.GetConfig2008FrameBlocks(interfaceSubmodule);

            foreach (IPNPlannerOutputFrame frame in frameBlocks)
            {
                if (frame.FrameID < startOfReqFrameID)
                {
                    startOfReqFrameID = frame.FrameID;
                }
                if (frame.FrameID > endOfReqFrameID)
                {
                    endOfReqFrameID = frame.FrameID;
                }
            }
        }

        /// <summary>
        /// Gets the Supported Synchronization Protocols of the Interface Submodule
        /// </summary>
        /// <returns>The list of the Supported Protocols</returns>
        internal static List<PNIRTSupportedSyncProtocols> GetSuppSyncProtocols(PclObject pclObj)
        {
            List<PNIRTSupportedSyncProtocols> suppSyncProtocols = new List<PNIRTSupportedSyncProtocols>();
            // Get the related Enumerated
            AttributeAccessCode accessCode = new AttributeAccessCode();
            Enumerated syncProtocols =
                pclObj.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtSupportedSyncProtocols,
                    accessCode,
                    null);

            if (accessCode.IsOkay)
            {
                foreach (object syncProtocol in syncProtocols.List)
                {
                    PNIRTSupportedSyncProtocols result;
                    if (Enum.TryParse(syncProtocol.ToString(), out result))
                    {
                        suppSyncProtocols.Add(result);
                    }
                }
            }
            return suppSyncProtocols;
        }

        internal static ushort GetSwappedUInt16(byte[] value, int offset)
        {
            byte[] swapped = new byte[2];
            swapped[0] = value[offset + 1];
            swapped[1] = value[offset + 0];

            return BitConverter.ToUInt16(swapped, 0);
        }

        #region Config

        internal static bool HasModuleInOutOrDiagAddress(PclObject addressee)
        {
            if (null == addressee)
            {
                return false;
            }

            foreach (DataAddress address in addressee.DataAddresses)
            {
                int bitAddress = address.GetBitOffset(0);
                if (bitAddress > 0)
                {
                    // Ignore packed modules.
                    continue;
                }

                IoTypes io = address.IoType;
                if (io == IoTypes.Output)
                {
                    return true;
                }
                if (io == IoTypes.Input)
                {
                    return true;
                }
                if (io == IoTypes.Diagnosis)
                {
                    return true;
                }
            }
            return false;
        }

        #endregion

        /// <summary>
        /// </summary>
        /// <returns> 'true' if AdjustDomainBoundary block has to be generated for this port</returns>
        internal static bool IsAdjustDomainBoundaryToGenerate(DataModel.PCLObjects.Port portModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            Interface interfaceOfPort = NavigationUtilities.GetInterfaceOfPort(portModule);
            int numberOfPorts = interfaceOfPort.GetPorts().Count;
            bool isPortDeactivated =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortDeactivated,
                    ac.GetNew(),
                    false);

            bool isFrameClass2Supported = false;
            bool isPNIrtSyncRoleExists;

            AttributeAccessCode accessCode = new AttributeAccessCode();
            interfaceOfPort.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIrtSupportedSyncProtocols,
                accessCode,
                null);
            isPNIrtSyncRoleExists = accessCode.IsOkay;

            // Get the Composite Attribute of supported Frame Classes
            accessCode.Reset();
            Enumerated suppFrameClasses =
                interfaceOfPort.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    ac,
                    null);
            if (accessCode.IsOkay)
            {
                foreach (object pnPlannerFrameClass in suppFrameClasses.List)
                {
                    PNIOFrameClass frameClass;
                    if (Enum.TryParse(pnPlannerFrameClass.ToString(), out frameClass)
                        && (frameClass == PNIOFrameClass.Class2Frame))
                    {
                        isFrameClass2Supported = true;
                        break;
                    }
                }
            }

            // PNIrtSupportedSyncProtocols
            bool isPNIrtSupportedSyncProtocolsExist =
                GetSuppSyncProtocols(interfaceOfPort).Contains(PNIRTSupportedSyncProtocols.RTSync);

            bool isIrtPortSyncDomainBoundarySet = (numberOfPorts > 1) && !isPortDeactivated && isPNIrtSyncRoleExists
                                                  && isFrameClass2Supported && isPNIrtSupportedSyncProtocolsExist;

            return isIrtPortSyncDomainBoundarySet;
        }

        /// <summary>
        /// Checks the conditions for the AdjustMauType subblock's generation.
        /// </summary>
        /// <param name="portSubmodule">The port, which should be checked.</param>
        /// <returns>True, if the subblock must be generated. Else false.</returns>
        internal static bool IsAdjustMauTypeSubBlockToGenerate(DataModel.PCLObjects.Port portSubmodule)
        {
            if (portSubmodule == null)
            {
                throw new ArgumentNullException(nameof(portSubmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            uint portEthernetMediumDuplex =
                portSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnEthernetMediumDuplex,
                    ac,
                    0);
            bool isConcreteMauTypeSet = ac.IsOkay && (portEthernetMediumDuplex != m_IdPortAutomaticSettings);

            bool isPortAutoNegotiationDisabled =
                !portSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortAutoNegotiation,
                    ac.GetNew(),
                    true);

            bool isPortDeactivated =
                portSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortDeactivated,
                    ac.GetNew(),
                    false);

            bool isSelectedMauTypeSupportsAdjustMauType = IsSelectedMauTypeSupportsAdjustMauType(
                portSubmodule, portEthernetMediumDuplex);

            bool isAdjustMauTypeSubBlockToGenerate = isConcreteMauTypeSet && isPortAutoNegotiationDisabled
                                                                          && !isPortDeactivated && isSelectedMauTypeSupportsAdjustMauType;

            return isAdjustMauTypeSubBlockToGenerate;
        }

        /// <summary>
        /// Checks the conditions for the CheckMauType subblock's generation.
        /// </summary>
        /// <param name="portSubmodule">The port, which should be checked.</param>
        /// <param name="isPdev11"></param>
        /// <param name="portEthernetMediumDuplex"></param>
        /// <returns>True, if the subblock must be generated. Else false.</returns>
        private static bool IsCheckMauTypeSubBlockToBeGenerated(
            DataModel.PCLObjects.Port portSubmodule,
            bool isPdev11,
            out uint portEthernetMediumDuplex)
        {
            AttributeAccessCode acPNCheckMauTypeRecordSupported = new AttributeAccessCode();
            AttributeAccessCode acPortEthernetMediumDuplex = new AttributeAccessCode();

            bool checkMauTypeRecordSupported = portSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnCheckMAUTypeRecordSupported,
                acPNCheckMauTypeRecordSupported,
                false);
            bool portDeactivationSupported = PortUtility.GetPortDeactivationCapability(portSubmodule);
            bool linkStateDiagnosis = portSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(
                InternalAttributeNames.PnLinkStateDiagnosis,
                new AttributeAccessCode(),
                false);
            bool isConcreteMauTypeSelected = false;
            portEthernetMediumDuplex = portSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnEthernetMediumDuplex,
                acPortEthernetMediumDuplex,
                0);

            if (acPortEthernetMediumDuplex.IsOkay
                && (portEthernetMediumDuplex != m_IdPortAutomaticSettings))
            {
                isConcreteMauTypeSelected = true;
            }

            if (acPNCheckMauTypeRecordSupported.IsOkay)
            {
                if (isConcreteMauTypeSelected
                    && linkStateDiagnosis
                    && (checkMauTypeRecordSupported
                        || (!acPNCheckMauTypeRecordSupported.IsOkay && portDeactivationSupported)))
                {
                    return true;
                }
            }
            else
            {
                if ((isConcreteMauTypeSelected && linkStateDiagnosis)
                    || (isPdev11 && IsCheckPeersSubBlockToGenerate(portSubmodule)))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Utility method for the determination whether the selected MauType requires AdjustMauType block.
        /// </summary>
        /// <param name="portSubmodule"></param>
        /// <param name="selectedMauType"></param>
        /// <returns></returns>
        private static bool IsSelectedMauTypeSupportsAdjustMauType(DataModel.PCLObjects.Port portSubmodule, UInt32 selectedMauType)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            Enumerated generateAttribute =
                portSubmodule.PCLCatalogObject?.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnDontGenerateAdjustMAUTypeBlock,
                    ac,
                    null);

            if (ac.IsOkay && generateAttribute != null)
            {
                // Get all elements
                foreach (var mediumDuplex in generateAttribute.List)
                {
                    UInt32 mediumDuplexValue = Convert.ToUInt32(mediumDuplex, CultureInfo.InvariantCulture);

                    if (mediumDuplexValue == selectedMauType)
                    {
                        // because the PnDontGenerateAdjustMAUTypeBlock attribute contains the selected MauType, return false
                        return false;
                    }
                }
            }

            return true;
        }


        /// <summary>
        /// The function returns true, if any of the preconditions is complied for generating
        /// PDPortDataCheck.CheckLineDelay parameter dataset sub-block.
        /// </summary>
        /// <returns>'true' if PDPortDataCheck.CheckLineDelay parameter dataset sub-block has to be generated for this port</returns>
        private static bool IsCheckLineDelaySubBlockToGenerate(DataModel.PCLObjects.Port portModule)
        {
            IList<DataModel.PCLObjects.Port> connectedPorts = portModule.GetPartnerPorts();

            if ((connectedPorts != null)
                && (connectedPorts.Count == 1)
                && !PortUtility.IsPortDeactivated(connectedPorts[0]))
            {
                if (PortUtility.LineLengthSupportedBetweenPorts(portModule, connectedPorts[0]))
                {
                    if (portModule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtSignalDelayTime,
                        new AttributeAccessCode(),
                        0) != 0)
                    {
                        return true;
                    }
                    AttributeAccessCode aCode = new AttributeAccessCode();
                    uint selection =
                        portModule.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIrtLineDelaySelection,
                            aCode,
                            0);
                    if (aCode.IsOkay
                        && (selection == 1))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// The function returns true, if any of the preconditions is complied for generating
        /// PDPortDataCheck.CheckPeers parameter dataset sub-block.
        /// </summary>
        /// <returns>'true' if PDPortDataCheck.CheckPeers parameter dataset sub-block has to be generated for this port</returns>
        private static bool IsCheckPeersSubBlockToGenerate(DataModel.PCLObjects.Port portModule)
        {
            List<DataModel.PCLObjects.Port> interconnectedPorts = (List<DataModel.PCLObjects.Port>)portModule.GetPartnerPorts();
            bool isProgrammablePeerEnabled = MachineTailorUtility.IsProgrammablePeerEnabled(portModule);

            // check if there is not more than 1 peer port connected to this port (this is not a "Tool-changer" Docking-Port)
            if ((null == interconnectedPorts)
                || (interconnectedPorts.Count != 1)
                || PortUtility.IsPortDeactivated(interconnectedPorts[0]))
            {
                //Generate the block if the port is programmable peer.
                if (isProgrammablePeerEnabled)
                {
                    return true;
                }
                return false;
            }

            Interface currentInterface = NavigationUtilities.GetInterfaceOfPort(portModule);

            if (!Utility.IsPdevInterface(currentInterface))
            {
                return false;
            }

            Interface connectedInterface = NavigationUtilities.GetInterfaceOfPort(interconnectedPorts[0]);
            PclObject interfaceSubmoduleNode = connectedInterface.Node;

            bool noSViaOtherPath = false;

            if (interfaceSubmoduleNode != null)
            {
                noSViaOtherPath =
                    interfaceSubmoduleNode.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPnNoSViaOtherPath,
                        new AttributeAccessCode(),
                        false);
            }

            bool addressTailoringIsActivated =
                AddressTailorUtility.IsAddressTailoringEnabledforInterface(currentInterface);

            return Utility.IsPdevInterface(connectedInterface)
                   && (!noSViaOtherPath || addressTailoringIsActivated || isProgrammablePeerEnabled);
        }

        /// <summary>
        /// Determines whether a Controller interface submodule's configuration is IRT top
        /// </summary>
        /// <param name="interfaceSubmodule">current PNIO Controller interface</param>
        /// <returns>true in case of an IRT top configuration; otherwise false</returns>
        internal static bool IsControllerIrtTop(Interface interfaceSubmodule)
        {
            PNRTClass rtClass =
                (PNRTClass)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtHighestSyncClass,
                    new AttributeAccessCode(),
                    (uint)PNRTClass.Rt);
            return rtClass == PNRTClass.IrtTop;
        }

        /// <summary>
        /// Check whether a port is a FO port and its activation status.
        /// </summary>
        /// <returns>'true' if the port is a FO port and it is activated, otherwise 'false'.</returns>
        internal static bool IsFoPortActivated(DataModel.PCLObjects.Port portModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            portModule.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnFiberOptic,
                ac,
                0);
            //is it a fiber optic port
            if (ac.IsOkay)
            {
                AttributeAccessCode acPortDeactivated = new AttributeAccessCode();
                bool isPortDeactivated =
                    portModule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPortDeactivated,
                        acPortDeactivated,
                        false);
                // if the attribute does not exist, than this FO Port is activated
                if (!acPortDeactivated.IsOkay)
                {
                    return true;
                }
                // if the attribute exists and has a false value, than this FO Port is not de activated
                if (!isPortDeactivated)
                {
                    return true;
                }
                return false;
            }
            // it is not a FO Port
            return false;
        }

        internal static bool IsModularScalance(PclObject headModule)
        {
            if (headModule != null)
            {
                if ((headModule.AttributeAccess.GetAnyAttribute<ulong>(
                    InternalAttributeNames.ScalanceDeviceCapabilities,
                    new AttributeAccessCode(),
                    0) & (ulong)ScalanceDeviceCapabilities.Modular) == 1)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// </summary>
        /// <remarks>
        /// PNConfigLib comment: here isCPU check is done. It should return false for PNDriver. 
        /// But because pnPdevSupportedModel is larger then 3 for PNDriver, it also returns false. 
        /// </remarks>
        private static bool IsPdev11(PclObject pnInterface)
        {
            PclObject container = NavigationUtilities.GetContainer(pnInterface);
            if ((container != null)
                && container is CentralDevice)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                uint pnPdevSupportedModel =
                    pnInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnPdevSupportedModel,
                        new AttributeAccessCode(),
                        0);
                return ac.IsOkay && (pnPdevSupportedModel <= 3);
            }
            return false;
        }

        /// <summary>
        /// The function returns true, if any of the preconditions is complied for generating PDPortDataAdjust parameter dataset
        /// block.
        /// </summary>
        /// <returns>'true' if PDPortDataAdjust parameter dataset block has to be generated for this port</returns>
        internal static bool IsPDPortDataAdjustToGenerate(DataModel.PCLObjects.Port portModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool isPortDeactivated =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortDeactivated,
                    ac.GetNew(),
                    false);

            bool isAdjustMauTypeSubBlockToGenerate = IsAdjustMauTypeSubBlockToGenerate(portModule);

            //Boundaries
            uint valueIrtPortSyncDomainBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtPortSyncDomainBoundary,
                    new AttributeAccessCode(),
                    0);
            bool isIrtPortSyncDomainBoundarySet = (valueIrtPortSyncDomainBoundary > 0)
                                                  && IsAdjustDomainBoundaryToGenerate(portModule);

            uint valuePNDcpBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnDCPBoundary,
                    new AttributeAccessCode(),
                    0);
            bool isDcpBoundarySet = valuePNDcpBoundary > 0;

            uint valuePNPTPBoundary =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPTPBoundary,
                    new AttributeAccessCode(),
                    0);
            bool isPeerToPeerBoundarySet = valuePNPTPBoundary > 0;

            bool generateAdjustPreambleLength = true;

            generateAdjustPreambleLength &=
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIrtGenerateAdjustPreambleLengthV14,
                    ac.GetNew(),
                    false);

            if (isPortDeactivated
                || isAdjustMauTypeSubBlockToGenerate
                || isIrtPortSyncDomainBoundarySet
                || isPeerToPeerBoundarySet
                || isDcpBoundarySet
                || generateAdjustPreambleLength)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// The function returns true, if any of the preconditions is complied for generating PDPortDataCheck parameter dataset
        /// block.
        /// </summary>
        /// <returns>'true' if PDPortDataCheck parameter dataset block has to be generated for this port</returns>
        internal static bool IsPDPortDataCheckToGenerate(
            DataModel.PCLObjects.Port portModule,
            PNInterfaceType interfaceType,
            PDPortData pdPortData)
        {
            // If the port is deactivated, than PDPortDataCheck is not generated
            if (PortUtility.IsPortDeactivated(portModule))
            {
                return false;
            }

            // Condition for CheckPeers-0x020A SubBlock ///////////////////////////////////////////////////////////

            if (IsCheckPeersSubBlockToGenerate(portModule))
            {
                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckPeersSubBlock;
            }

            ///////////////////////////////////////////////////////////////////////////////////////////////////////

            // Condition for CheckLineDelay-0x020B SubBlock ///////////////////////////////////////////////////////

            if (IsCheckLineDelaySubBlockToGenerate(portModule))
            {
                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckLineDelaySubBlock;
            }

            ///////////////////////////////////////////////////////////////////////////////////////////////////////

            AttributeAccessCode ac = new AttributeAccessCode();
            AttributeAccessCode linkStateDiagSupp2 = new AttributeAccessCode();

            bool linkStateDiagnosis =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnLinkStateDiagnosis,
                    linkStateDiagSupp2,
                    false);

            // Get the Composite Attribute of supported Frame Classes
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(portModule);

            Enumerated suppFrameClasses =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    ac,
                    null);

            // Condition for CheckMAUType-0x020C SubBlock
            uint selectedMauType;
            bool isPdev11 = IsPdev11(interfaceSubmodule);
            if (IsCheckMauTypeSubBlockToBeGenerated(portModule, isPdev11, out selectedMauType))
            {
                //AP01142264
                pdPortData.MauType = isPdev11 ? (int)PNEthernetMediumDuplex.Fullduplex100Mbit : Convert.ToInt32(selectedMauType, CultureInfo.InvariantCulture);
                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckMauTypeSubBlock;
            }

            AttributeAccessCode linkStateDiagSupp = new AttributeAccessCode();
            portModule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnLinkStateDiagnosisCapability,
                linkStateDiagSupp,
                0);

            // Condition for CheckLinkState-0x021C SubBlock

            if (linkStateDiagSupp.IsOkay && linkStateDiagnosis)
            {
                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckLinkStateSubBlock;
            }

            // Condition for CheckSyncDifference-0x021E SubBlock
            bool isFrameClass2Supported;
            bool endOfTopologyDiscovery;
            PclObject ioConnector;
            CheckSyncDifference(
                portModule,
                pdPortData,
                ac.IsOkay,
                linkStateDiagnosis,
                interfaceSubmodule,
                suppFrameClasses,
                out isFrameClass2Supported,
                out endOfTopologyDiscovery,
                out ioConnector);

            // Condition for CheckMAUTypeDifference-0x021F

            bool isPortDeactivationSupported = PortUtility.GetPortDeactivationCapability(portModule);
            bool isCheckMauTypeDifferenceSupported =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnCheckMAUTypeDifferenceSupported,
                    ac.GetNew(),
                    true);

            if (linkStateDiagnosis
                && !endOfTopologyDiscovery
                && (ac.IsOkay ? isCheckMauTypeDifferenceSupported : isPortDeactivationSupported))
            {
                //isPortDeactivationSupported is only relevant, if the attribute PNCheckMAUTypeDifferenceSupported is not available
                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckMauTypeDiffSubBlock;
            }

            // In case of "Automatic settings (monitor)" is selected
            if (LinkStateCheck(
                portModule,
                ioConnector,
                linkStateDiagnosis,
                isPortDeactivationSupported,
                isFrameClass2Supported))
            {
                return true;
            }

            return pdPortData.SubBlockToGenerate > 0;
        }

        /// <summary>
        /// The function returns true, if the preconditions are complied for generating PDPortFODataCheck parameter dataset block.
        /// </summary>
        /// <returns>'true' if PDPortFODataCheck parameter dataset block has to be generated for this port</returns>
        internal static bool IsPDPortFoDataCheckToGenerate(DataModel.PCLObjects.Port portModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool powerBudgetControlSupported =
                portModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPowerBudgetControlSupported,
                    ac,
                    false);

            ac.Reset();
            //check the medium settings, whether "Automatic settings (monitor)" is selected
            bool linkStateDiagnosis =
                portModule.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnLinkStateDiagnosis, ac, false);

            if (powerBudgetControlSupported && linkStateDiagnosis)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// </summary>
        internal static bool IsRTSync(Interface interfaceSubmodule, PNInterfaceType interfaceType)
        {
            // check if the actual synchronization Class > 0
            if (IsSynchronized(interfaceSubmodule, interfaceType))
            {
                if (interfaceType == PNInterfaceType.IODevice)
                {
                    if (GetCommonSyncProtocols(interfaceSubmodule).Contains(PNIRTSupportedSyncProtocols.RTSync))
                    {
                        return true;
                    }
                    return false;
                }
                if (GetSuppSyncProtocols(interfaceSubmodule).Contains(PNIRTSupportedSyncProtocols.RTSync))
                {
                    return true;
                }
                return false;
            }
            return false;
        }

        /// <summary>
        /// </summary>
        internal static bool IsSynchronized(Interface interfaceSubmodule, PNInterfaceType interfaceType)
        {
            //Get the appropriate IOConnector of DeviceInterfaceSubmodule
            PclObject ioConnector;
            if (interfaceType == PNInterfaceType.IOController)
            {
                ioConnector = interfaceSubmodule.PNIOC;
            }
            else if (interfaceType == PNInterfaceType.IODevice)
            {
                ioConnector = NavigationUtilities.GetIODevice(interfaceSubmodule, null);
            }
            else
            {
                // Debug.Assert(false, "PNInterfaceType is None. Required PN IO-System connector (IOController or IODevice) can't be determined!");
                return false;
            }
            if (ioConnector != null)
            {
                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);
                if (syncRole != PNIRTSyncRole.NotSynchronized)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Check whether all participant Devices are PDEV.
        /// </summary>
        internal static bool PdevCheckForPDPortData(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule != null)
            {
                // DEVICE
                if (!Utility.IsPdevInterface(interfaceSubmodule))
                {
                    return false;
                }

                Interface controllerInterface = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);
                if (controllerInterface == null)
                {
                    return true;
                }
                return
                    controllerInterface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnPdevSupportedModel,
                        new AttributeAccessCode(),
                        0) > 0;
            }
            // CONTROLLER
            return interfaceSubmodule != null
                   && (interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                       InternalAttributeNames.PnPdevSupportedModel,
                       new AttributeAccessCode(),
                       0) > 0);
        }

        internal static byte[] SwapBytes(int val)
        {
            Debug.Assert((val & 0xFFFF0000) == 0);

            byte[] bval = BitConverter.GetBytes(val);
            byte[] swapped = new byte[2];
            Array.Copy(bval, swapped, 2);

            return swapped;
        }

        private static void CheckSyncDifference(
            DataModel.PCLObjects.Port portModule,
            PDPortData pdPortData,
            bool isOkay,
            bool linkStateDiagnosis,
            Interface interfaceSubmodule,
            Enumerated suppFrameClasses,
            out bool isFrameClass2Supported,
            out bool endOfTopologyDiscovery,
            out PclObject ioConnector)
        {
            isFrameClass2Supported =
                suppFrameClasses.List.Any(
                    suppFrameClass => Convert.ToInt32(suppFrameClass, CultureInfo.InvariantCulture) == (int)PNIOFrameClass.Class2Frame);

            // endOfTopologyDiscovery  changed :
            endOfTopologyDiscovery =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPTPBoundary,
                    new AttributeAccessCode(),
                    0) > 0;

            ioConnector = NavigationUtilities.GetIOConnector(interfaceSubmodule);
            if ((suppFrameClasses.List != null)
                && (suppFrameClasses.List.Count > 0))
            {
                Enumerated supportedSyncProtocols =
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSupportedSyncProtocols,
                        new AttributeAccessCode(),
                        null);

                bool rtSyncSupported = (supportedSyncProtocols != null) && (supportedSyncProtocols.List != null)
                                       && (supportedSyncProtocols.List.Count > 0)
                                       && supportedSyncProtocols.List.Any(
                                           supportedSyncProtocol =>
                                           Convert.ToInt32(supportedSyncProtocol, CultureInfo.InvariantCulture)
                                           == (int)PNIRTSupportedSyncProtocols.RTSync);

                if (isFrameClass2Supported && !rtSyncSupported)
                {
                    if (Utility.IsIrtTopDevice(interfaceSubmodule)
                        && IsSyncronized(ioConnector))
                    {
                        pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckSyncDiffSubBlock;
                    }
                }
                else
                {
                    int cableDelay = 0;
                    if (!endOfTopologyDiscovery && linkStateDiagnosis)
                    {
                        if (isFrameClass2Supported && isOkay)
                        {
                            Enumerated syncRolesEnumerated =
                                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                                    InternalAttributeNames.PnIrtSyncRoleSupp,
                                    new AttributeAccessCode(),
                                    null);

                            if ((syncRolesEnumerated != null)
                                && (syncRolesEnumerated.List != null)
                                && (syncRolesEnumerated.List.Count > 0))
                            {
                                pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckSyncDiffSubBlock;
                            }
                        }

                        cableDelay = rtSyncSupported ? 1 : cableDelay;
                    }

                    // SyncMaster is set to 1 if
                    // The interface is synchronized AND end of sync-domain is false.

                    // syncMaster changed :
                    int syncMaster = IsSyncronized(ioConnector) && !endOfTopologyDiscovery ? 1 : 0;

                    // The subblock is generated if one of its fields is set to 1
                    if ((cableDelay == 1)
                        || (syncMaster == 1))
                    {
                        pdPortData.CableDelay = cableDelay;
                        pdPortData.SyncMaster = syncMaster;
                        pdPortData.SubBlockToGenerate |= PDSubBlocks.CheckSyncDiffSubBlock;
                    }
                }
            }
        }

        /// <summary>
        /// This Method is called for creation of CheckPeers block for programmable peers.
        /// </summary>
        /// o
        private static byte[] GenerateEmptyCheckPeersBlock()
        {
            CheckPeersSubBlockStruct checkProgrammablePeersSubBlock = new CheckPeersSubBlockStruct();
            checkProgrammablePeersSubBlock.NumberOfPeerPorts = 1;

            List<byte> data = new List<byte>();

            byte lengthPeerPortID = 0;
            data.Add(lengthPeerPortID);
            data.AddRange(Encoding.ASCII.GetBytes(string.Empty));

            byte lengthPeerChassisID = 0;
            data.Add(lengthPeerChassisID);
            data.AddRange(Encoding.ASCII.GetBytes(string.Empty));

            //append peerIDs to CheckPeers-0x020A SubBlock
            checkProgrammablePeersSubBlock.AddSubblock(data.ToArray());
            int alignmentLen = BufferManager.Alignment(checkProgrammablePeersSubBlock.ParaBlockLength, 4);
            if (alignmentLen != 0)
            {
                byte[] alignment = new byte[alignmentLen];
                //append alignment
                checkProgrammablePeersSubBlock.AddSubblock(alignment);
            }
            return checkProgrammablePeersSubBlock.ToByteArray;
        }

        /// <summary>
        /// Creates an empty begin end assignment.
        /// </summary>
        /// <returns>Created empty begin end assignment</returns>
        private static PdIrBeginEndAssignment GetEmptyBeginEndAssignment()
        {
            PdIrBeginEndAssignment pDirBeginEndAssignment = new PdIrBeginEndAssignment();

            pDirBeginEndAssignment.RX_RedPeriodBegin = 0;
            pDirBeginEndAssignment.RX_OrangePeriodBegin = 0;
            pDirBeginEndAssignment.RX_RedOrangePeriodEnd = 0;
            pDirBeginEndAssignment.TX_RedPeriodBegin = 0;
            pDirBeginEndAssignment.TX_OrangePeriodBegin = 0;
            pDirBeginEndAssignment.TX_RedOrangePeriodEnd = 0;
            return pDirBeginEndAssignment;
        }

        /// <summary>
        /// Creates an empty phase assignment.
        /// </summary>
        /// <param name="assignmentIndex">
        /// ReservedBegin, OrangeBegin and ReservedEnd fields will be
        /// initialized with this number
        /// </param>
        /// <returns>Created empty phase assignment</returns>
        private static PdIrPhaseAssignment GetEmptyPhaseAssignment(int assignmentIndex)
        {
            return new PdIrPhaseAssignment
            {
                RX_Phase = 0,
                RX_ReservedEnd = assignmentIndex,
                RX_OrangeBegin = assignmentIndex,
                RX_ReservedBegin = assignmentIndex,
                TX_Phase = 0,
                TX_ReservedEnd = assignmentIndex,
                TX_OrangeBegin = assignmentIndex,
                TX_ReservedBegin = assignmentIndex
            };
        }

        /// <summary>
        /// Returns the IRDataID of an interface submodule. Use GetIRDataIDOfDevice method for device interface
        /// submodules which must be checked against being an irttop device.
        /// </summary>
        private static byte[] GetIRDataIDOfInterface(Interface interfaceSubmodule)
        {
            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (syncDomain == null)
            {
                return null;
            }

            SyncDomainBusinessLogic syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBusinessLogic == null)
            {
                return null;
            }
            byte[] irDataID = new byte[16];
            string pnPlannerFingerPrints = syncDomainBusinessLogic.PNPlannerResults.PNPlannerOutputFingerprint;

            Debug.Assert(
                (null == pnPlannerFingerPrints) || (pnPlannerFingerPrints.Length == 32),
                "IRDataId should be 32 bytes long");

            if ((pnPlannerFingerPrints != null)
                && (pnPlannerFingerPrints.Length == 32))
            {
                for (int i = 0; i < 16; i++)
                {
                    if (pnPlannerFingerPrints.Length >= i * 2)
                    {
                        irDataID[i] = Convert.ToByte(pnPlannerFingerPrints.Substring(16 * 2 - 2 - i * 2, 2), 16);
                    }
                }
            }
            return irDataID;
        }

        private static int GetNumberOfTxPortGroups(Interface interfaceSubmodule)
        {
            int numberOfPorts = interfaceSubmodule.GetPorts().Count;

            return numberOfPorts < 8 ? 1 : (numberOfPorts - 7) / 8 + 2;
        }

        /// <summary>
        /// Returns the Config2008 redshift beginendassignments of the port.
        /// If it is not an IrtTop device, returns null.
        /// </summary>
        private static List<PdIrBeginEndAssignment> GetPdirBeginEndAssignments(
            Interface interfaceSubmodule,
            DataModel.PCLObjects.Port port)
        {
            List<PdIrBeginEndAssignment> config2008BeginEndAssignments = new List<PdIrBeginEndAssignment>();

            int portNumber = port.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnPortNumber,
                new AttributeAccessCode(),
                0);
            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (null != syncDomain)
            {
                SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
                if (null != syncDomainBl)
                {
                    List<IPNPlannerOutputBeginEndAssignment> beginEndAssignments =
                        (List<IPNPlannerOutputBeginEndAssignment>)syncDomainBl.GetConfig2008BeginEndAssignmentBlocks(interfaceSubmodule, portNumber);
                    if (null != beginEndAssignments)
                    {
                        foreach (IPNPlannerOutputBeginEndAssignment beginEndAssignment in beginEndAssignments)
                        {
                            PdIrBeginEndAssignment pDirBeginEndAssignment = new PdIrBeginEndAssignment();

                            pDirBeginEndAssignment.RX_RedPeriodBegin = beginEndAssignment.RX_RedPeriodBegin;
                            pDirBeginEndAssignment.RX_OrangePeriodBegin = beginEndAssignment.RX_OrangePeriodBegin;
                            pDirBeginEndAssignment.RX_RedOrangePeriodEnd = beginEndAssignment.RX_RedOrangePeriodEnd;
                            pDirBeginEndAssignment.TX_RedPeriodBegin = beginEndAssignment.TX_RedPeriodBegin;
                            pDirBeginEndAssignment.TX_OrangePeriodBegin = beginEndAssignment.TX_OrangePeriodBegin;
                            pDirBeginEndAssignment.TX_RedOrangePeriodEnd = beginEndAssignment.TX_RedOrangePeriodEnd;

                            config2008BeginEndAssignments.Add(pDirBeginEndAssignment);
                        }
                    }
                    else
                    {
                        config2008BeginEndAssignments.Add(GetEmptyBeginEndAssignment());
                    }
                }
            }
            return config2008BeginEndAssignments;
        }

        /// <summary>
        /// Returns the Config2008 frame blocks of the device interface.
        /// If it is not an IrtTop device, returns null.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="relativeTimeMode">
        /// If true, only frames of the interface which have
        /// rxport = 0 or local = 1 are returned. Else, all frames are returned.
        /// </param>
        /// <returns></returns>
        private static List<PdIrFrameDataMain> GetPdirFrameDataMain(Interface interfaceSubmodule, bool relativeTimeMode)
        {
            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (null == syncDomain)
            {
                return null;
            }
            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (null == syncDomainBl)
            {
                return null;
            }
            List<IPNPlannerOutputFrame> frameBlocks = (List<IPNPlannerOutputFrame>)syncDomainBl.GetConfig2008FrameBlocks(interfaceSubmodule);
            if (frameBlocks.Count == 0)
            {
                // The interface doesn't have any frames, return.
                return null;
            }

            // Get MediaRedundancyWatchdog value (Not supported in PNConfigLib)
            int mrtWatchdog = 0;

            List<PdIrFrameDataMain> config2008FrameBlocks = new List<PdIrFrameDataMain>();
            int numberOfTxPortGroups = GetNumberOfTxPortGroups(interfaceSubmodule);
            foreach (IPNPlannerOutputFrame frameBlock in frameBlocks)
            {
                // Don't add the forwarded frames in relative time mode
                if (relativeTimeMode
                    && (frameBlock.RxPort != 0)
                    && (frameBlock.Local != 1))
                {
                    continue;
                }
                PdIrFrameDataMain pdIrFrameData = new PdIrFrameDataMain();
                pdIrFrameData.FrameSendOffset = frameBlock.FrameSendOffset;
                // PDIRFrameData requires the padded data length, in which case only the length of the frame block.
                pdIrFrameData.DataLength = frameBlock.Length;
                pdIrFrameData.ReductionRatio = frameBlock.ReductionRatio;
                pdIrFrameData.Phase = frameBlock.Phase;
                pdIrFrameData.FrameID = frameBlock.FrameID == PNRTFrameIdLimits.SecondarySyncFrameId
                                            ? PNRTFrameIdLimits.SyncFrameId
                                            : frameBlock.FrameID;
                pdIrFrameData.EtherType = frameBlock.EtherType;
                pdIrFrameData.RxPort = frameBlock.RxPort;
                pdIrFrameData.SyncFrame = frameBlock.SyncFrame;
                // Set mrt watchdog value only for local mrpd frames
                if ((frameBlock.FrameID >= (int)PNRTFrameIdLimits.RTClass3FrameId.RedundantMin)
                    && (frameBlock.FrameID <= (int)PNRTFrameIdLimits.RTClass3FrameId.RedundantMax)
                    && (frameBlock.Local == 1))
                {
                    pdIrFrameData.MediaRedundancyWatchdog = mrtWatchdog;
                }
                else
                {
                    pdIrFrameData.MediaRedundancyWatchdog = 0;
                }
                pdIrFrameData.MeaningFrameSendOffset = frameBlock.MeaningFrameSendOffset;
                pdIrFrameData.NumTxPortGroups = numberOfTxPortGroups;

                List<List<int>> portGroups = SplitPortsToTxPortGroups(frameBlock.Ports, numberOfTxPortGroups);
                for (int i = 0; i < portGroups.Count; i++)
                {
                    PdIrFrameTxPortGroups portGroupData = new PdIrFrameTxPortGroups();
                    portGroupData.SetPorts(portGroups[i]);
                    if (i == 0)
                    {
                        portGroupData.Local = frameBlock.Local;
                    }

                    pdIrFrameData.AddPortData(portGroupData);
                }

                config2008FrameBlocks.Add(pdIrFrameData);
            }

            return config2008FrameBlocks;
        }

        /// <summary>
        /// Returns the config2008 redshift phaseassignments of the port.
        /// If it is not an IrtTop device, returns null.
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="port"></param>
        /// <param name="dummyAssignmentNeeded">
        /// If there is a phase assignment for the given port but
        /// information for all phases with phase number up to maximum reduction ratio is not found,
        /// the method creates a dummy phase assignment for non existing assignments and returns true.
        /// Othwerwise, false is returned.
        /// </param>
        /// <param name="dummyAssignmentIndex">Index for the dummy begin end assignment in case of it is needed.</param>
        /// <returns></returns>
        private static List<PdIrPhaseAssignment> GetPdirPhaseAssignments(
            Interface interfaceSubmodule,
            DataModel.PCLObjects.Port port,
            out bool dummyAssignmentNeeded,
            int dummyAssignmentIndex)
        {
            dummyAssignmentNeeded = false;
            List<PdIrPhaseAssignment> config2008PhaseAssignments = new List<PdIrPhaseAssignment>();

            SyncDomain syncDomain = interfaceSubmodule.SyncDomain;
            if (syncDomain == null)
            {
                return config2008PhaseAssignments;
            }
            if (syncDomain.SyncDomainBusinessLogic == null)
            {
                return config2008PhaseAssignments;
            }
            int portNumber = port.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnPortNumber,
                new AttributeAccessCode(),
                0);
            List<IPNPlannerOutputPhaseAssignment> phaseAssignments =
                (List<IPNPlannerOutputPhaseAssignment>)syncDomain.SyncDomainBusinessLogic.GetConfig2008PhaseAssignmentBlocks(interfaceSubmodule, portNumber);

            if (phaseAssignments == null)
            {
                config2008PhaseAssignments.Add(GetEmptyPhaseAssignment(0));
            }
            else
            {
                phaseAssignments.Sort((assignment1, assignment2) => assignment1.Phase.CompareTo(assignment2.Phase));

                // Get the maximum reduction ratio and determine if dummy phases needed
                List<int> existingPhases = new List<int>(phaseAssignments.Count);
                foreach (IPNPlannerOutputPhaseAssignment assignment in phaseAssignments)
                {
                    existingPhases.Add(assignment.Phase);
                }
                for (int currPhase = 1; currPhase <= syncDomain.SyncDomainBusinessLogic.PNPlannerResults.MaxReductionRatio; currPhase++)
                {
                    // phaseAssignments are already sorted according to the phase, so
                    // binary search can be done without another sort operation.
                    int phaseIndex = existingPhases.BinarySearch(currPhase);
                    if (phaseIndex >= 0) // found
                    {
                        IPNPlannerOutputPhaseAssignment phaseAssignment = phaseAssignments[phaseIndex];
                        PdIrPhaseAssignment pdirPhaseAssignment = new PdIrPhaseAssignment
                        {
                            RX_Phase = 0,
                            RX_ReservedEnd =
                                                                              phaseAssignment
                                                                              .RX_ReservedEnd,
                            RX_OrangeBegin =
                                                                              phaseAssignment
                                                                              .RX_OrangeBegin,
                            RX_ReservedBegin =
                                                                              phaseAssignment
                                                                              .RX_ReservedBegin,
                            TX_Phase = 0,
                            TX_ReservedEnd =
                                                                              phaseAssignment
                                                                              .TX_ReservedEnd,
                            TX_OrangeBegin =
                                                                              phaseAssignment
                                                                              .TX_OrangeBegin,
                            TX_ReservedBegin =
                                                                              phaseAssignment
                                                                              .TX_ReservedBegin
                        };

                        config2008PhaseAssignments.Add(pdirPhaseAssignment);

                        // Check if dummy assignment is referenced before 
                        // because of PNPlannerOutputReader.ReferenceDummyAssignments method, only one side of 
                        // the Rx or Tx part can have dummy assignment references
                        if ((pdirPhaseAssignment.RX_ReservedEnd == dummyAssignmentIndex)
                            || (pdirPhaseAssignment.TX_ReservedEnd == dummyAssignmentIndex))
                        {
                            dummyAssignmentNeeded = true;
                        }
                    }
                    else
                    {
                        dummyAssignmentNeeded = true;
                        config2008PhaseAssignments.Add(GetEmptyPhaseAssignment(dummyAssignmentIndex));
                    }
                }
            }
            return config2008PhaseAssignments;
        }

        /// <summary>
        /// The function returns the PeerPortID (short form of LLDP_PortID="port-xyz", 8 octets) for the actual portModule.
        /// </summary>
        /// <returns>PeerPortID</returns>
        private static string GetPeerPortID(DataModel.PCLObjects.Port connectedPortModule)
        {
            if (connectedPortModule == null)
            {
                return string.Empty;
            }

            StringBuilder sbPortID = new StringBuilder("port-");
            int positionNumber = 0;
            // PNPortNumber is in the range of "001-255"
            byte pnPortNumber =
                (byte)
                connectedPortModule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnPortNumber,
                    new AttributeAccessCode(),
                    1);

            sbPortID.Append(pnPortNumber.ToString("000", CultureInfo.InvariantCulture));

            DecentralDevice headModule = connectedPortModule.ParentObject.ParentObject as DecentralDevice;
            if (IsModularScalance(headModule))
            {
                //we have a modular device -> long form of LLDP_PortID="port-xyz-rstuv"

                PclObject container = NavigationUtilities.GetContainer(connectedPortModule);
                if (container == null)
                {
                    //short form of LLDP_PortID="port-xyz
                    return sbPortID.ToString();
                }
                if (container is Module)
                {
                    //matches if partnerport is on a mediamodule
                    positionNumber = container.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PositionNumber,
                        new AttributeAccessCode(),
                        0);
                }
                else if (container is Interface)
                {
                    //matches if partnerport is is aggregated to interface but the port is a sfp like comboport of XM400
                    if (headModule != null)
                    {
                        positionNumber = headModule.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.PositionNumber,
                            new AttributeAccessCode(),
                            0);
                    }
                }

                sbPortID.Append("-");
                sbPortID.Append(positionNumber.ToString("00000", CultureInfo.InvariantCulture));
            }

            // in other cases: short form of LLDP_PortID="port-xyz"
            return sbPortID.ToString();
        }

        private static void GetPNNameOfStationWithAddressTailorCheck(
            DataModel.PCLObjects.Port port,
            Interface connectedInterfaceSubmodule,
            out string nameOfStation)
        {
            nameOfStation = String.Empty;

            PartnerPortBlocksAddressTailorLogic partnerPortBlocksAddressTalilorLogic =
                            new PartnerPortBlocksAddressTailorLogic(port, connectedInterfaceSubmodule);
            if (!partnerPortBlocksAddressTalilorLogic.HasToTailorPartnerPortBlock())
            {
                nameOfStation =
                    connectedInterfaceSubmodule.Node.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.PnNameOfStation, new AttributeAccessCode(), string.Empty);
            }

        }
        /// <summary>
        /// Check if the PDInterfaceMrpDataAdjust parameter block is to be generated
        /// </summary>
        internal static bool HasPDInterfaceMrpDataAdjust(Interface interfaceSubmodule)
        {
            PNMrpRole PNMrpRole = PNMrpRole.NotInRing;
            return HasPDInterfaceMrpDataAdjust(interfaceSubmodule, ref PNMrpRole);
        }
        private static bool HasPDInterfaceMrpDataAdjust(
            Interface interfaceSubmodule,
            ref PNMrpRole PNMrpRole)
        {
            bool returnValueOk = false;
            //Check if the PDInterfaceMrpDataAdjust parameter block is to be generated
            AttributeAccessCode ac = new AttributeAccessCode();
            // Does the interface support mrp
            if (interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnMrpSupported,
                ac,
                false))
            {
                PNMrpRole = (PNMrpRole)interfaceSubmodule.AttributeAccess.
                    GetAnyAttribute<UInt32>(InternalAttributeNames.PnMrpRole, ac.GetNew(),
                        (UInt32)PNMrpRole.NotInRing);
                if (ac.IsOkay)
                {
                    returnValueOk = true;
                }
            }
            return returnValueOk;
        }

        /// <summary>
        /// Check if the PDInterfaceMrpDataCheck parameter block is to be generated
        /// </summary>
        internal static bool HasPDInterfaceMrpDataCheck(Interface interfaceSubmodule)
        {
            PNMrpRole PNMrpRole = PNMrpRole.NotInRing;
            Boolean mrpDiagnosis = false;
            return HasPDInterfaceMrpDataCheck(interfaceSubmodule, ref PNMrpRole, ref mrpDiagnosis);
        }

        private static bool HasPDInterfaceMrpDataCheck(
            Interface interfaceSubmodule,
            ref PNMrpRole PNMrpRole, ref Boolean mrpDiagnosis)
        {
            //Check if the PDInterfaceMrpDataCheck parameter block is to be generated
            bool returnValueOk = HasPDInterfaceMrpDataAdjust(interfaceSubmodule, ref PNMrpRole);

            if (returnValueOk)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                mrpDiagnosis = interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnMrpDiagnosis, ac, false);
                if (!ac.IsOkay)
                {
                    returnValueOk = false;
                }
            }
            return returnValueOk;
        }

        private static bool IsSyncronized(PclObject ioConnector)
        {
            PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);
            if (syncRole != PNIRTSyncRole.NotSynchronized)
            {
                return true;
            }
            return false;
        }

        private static bool LinkStateCheck(
            DataModel.PCLObjects.Port portModule,
            PclObject ioConnector,
            bool linkStateDiagnosis,
            bool isPortDeactivationSupported,
            bool isFrameClass2Supported)
        {
            if (!linkStateDiagnosis)
            {
                return false;
            }
            if (isPortDeactivationSupported)
            {
                return true;
            }

            if ((ioConnector != null) && isFrameClass2Supported)
            {
                AttributeAccessCode accessCode = new AttributeAccessCode();
                ioConnector.AttributeAccess.GetAnyAttribute<byte>(InternalAttributeNames.PnIrtSyncRole, accessCode, 0);
                if (accessCode.IsOkay)
                {
                    return true;
                }
            }

            // Option1: "LinkStateDiagnosisCapability" exists and is true in the GSD/Metafile,
            // Option1 is complied, if the attribute PNLinkStateDiagnosisCapability > 0 (Condition for SubBlock 0x021C)
            uint linkStateDiagnosisCapability =
                portModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnLinkStateDiagnosisCapability,
                    new AttributeAccessCode(),
                    0);
            // Option4: "PortDeactivationSupported" exists and is true in the GSD/Metafile (Condition for SubBlock 0x021F)                

            // if any of the options (1, 4, 5) is true, and the port is not deactivated, than PDPortDataCheck is must be generated                
            return linkStateDiagnosisCapability > 0;
        }

        /// <summary>
        /// Splits the general ports list to smaller lists for each txportgroup.
        /// Empty lists are generated for the port groups which don't have any tx assignments
        /// </summary>
        /// <param name="ports">List of all ports</param>
        /// <param name="numberOfTxPortGroups"></param>
        private static List<List<int>> SplitPortsToTxPortGroups(IList<int> ports, int numberOfTxPortGroups)
        {
            List<List<int>> txPortGroups = new List<List<int>>();
            for (int i = 0; i < numberOfTxPortGroups; i++)
            {
                List<int> nextTxPortGroup = new List<int>();
                txPortGroups.Add(nextTxPortGroup);
            }

            foreach (int port in ports)
            {
                if (port < 8)
                {
                    txPortGroups[0].Add(port);
                }
                else
                {
                    int portGroupIndex = (port - 7) / 8 + 1;
                    Debug.Assert(numberOfTxPortGroups > portGroupIndex);
                    txPortGroups[portGroupIndex].Add(port);
                }
            }
            return txPortGroups;
        }

        /// <summary>
        /// Summary description for PDPortData.
        /// </summary>
        internal class PDPortData
        {
            public int CableDelay
            {
                get; set;
            }

            public int MauType
            {
                get; set;
            }

            public PDSubBlocks SubBlockToGenerate
            {
                get; set;
            }

            public int SyncMaster
            {
                get; set;
            }
        }

        #region Address functions with IPropertyContainer

        private static List<DataAddress> GetAddressObjectsSorted(
            PclObject module,
            SortOrder sortOrder,
            Func<IoTypes, SortOrder, SortOrder> sortingChecker)
        {
            List<DataAddress> addr = new List<DataAddress>();

            // check if a diagnostic address is required IoType == 0x40
            IoTypes io =
                (IoTypes)module.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.IoType, new AttributeAccessCode(), 0);

            if ((io == IoTypes.None)
                && (module is Interface || module is DataModel.PCLObjects.Port))
            {
                io = IoTypes.None;
            }

            if (module.DataAddresses.Count == 0)
            {
                return addr;
            }
            addr.AddRange(module.DataAddresses.Where(item => item.GetBitOffset(0) == 0));

            switch (sortingChecker(io, sortOrder))
            {
                case SortOrder.APDU:
                    addr.Sort((a, b) => ApduComparer(a, b, SortDirection.Ascending));
                    break;
                case SortOrder.IO:
                    addr.Sort((a, b) => IoComparer(a, b, SortDirection.Ascending));
                    break;
                case SortOrder.OI:
                    addr.Sort((a, b) => IoComparer(a, b, SortDirection.Descending));
                    break;
            }

            return addr;
        }

        /// <summary>
        /// This method implements a complex sort algorithm
        /// 1st sort order logical address
        /// 2nd for io modules input before output address
        /// </summary>
        /// <param name="objA">Object on the left side</param>
        /// <param name="objB">Object on the right side</param>
        /// <param name="direction">This enum determinates if the sorting is ascending or descending</param>
        /// <returns>The result of the compare</returns>
        private static int ApduComparer(DataAddress objA, DataAddress objB, SortDirection direction)
        {
            if (objA == null)
            {
                return 0;
            }
            if (objB == null)
            {
                return 0;
            }
            if (objA == objB)
            {
                return 0;
            }

            int addrA = objA.StartAddress;
            int addrB = objB.StartAddress;

            if (addrA != addrB)
            {
                return addrA > addrB ? 1 * (int)direction : -1 * (int)direction;
            }

            return IoComparer(objA, objB, direction);
        }

        /// <summary>
        /// This method implements ascending sort algorithm
        /// </summary>
        /// <param name="objA">Object on the left side</param>
        /// <param name="objB">Object on the right side</param>
        /// <param name="direction">This enum determinates if the sorting is ascending or descending</param>
        /// <returns>The result of the compare</returns>
        private static int IoComparer(DataAddress objA, DataAddress objB, SortDirection direction)
        {
            if (objA == null)
            {
                return 0;
            }
            if (objB == null)
            {
                return 0;
            }
            if (objA == objB)
            {
                return 0;
            }

            IoTypes ioTypeA = objA.IoType;
            IoTypes ioTypeB = objB.IoType;

            if ((ioTypeA == IoTypes.Input)
                && (ioTypeB == IoTypes.Output))
            {
                return -1 * (int)direction;
            }
            if ((ioTypeA == IoTypes.Output)
                && (ioTypeB == IoTypes.Input))
            {
                return 1 * (int)direction;
            }

            Debug.Assert(ioTypeA != ioTypeB, "Invalid IoTypes");
            return 0;
        }

        /// <summary>
        /// Returns address objects of module sorted by io type (without address)
        /// </summary>
        /// <param name="module"></param>
        /// <param name="sortOrder">true: sorted by io type (without address), false: default sort (address and iotype)</param>
        internal static List<DataAddress> GetAddressObjectsSorted(
            PclObject module,
            SortOrder sortOrder)
        {
            return GetAddressObjectsSorted(
                module,
                sortOrder,
                (io, order) =>
                {
                    return order;
                });
        }

        /// <summary>
        /// Returns address objects of module sorted by io type (without address)
        /// </summary>
        /// <param name="module"></param>
        /// <param name="sortOrder">true: sorted by io type (without address), false: default sort (address and iotype)</param>
        internal static List<DataAddress> GetAddressObjectsSortedSharedDevice(
            PclObject module,
            SortOrder sortOrder)
        {
            return GetAddressObjectsSorted(
                module,
                sortOrder,
                (io, order) =>
                    {
                        // in case of mixed modules starts with the Address with lowest number --> sort new the addresses
                        // mixmodul
                        if ((((int)io & (int)IoTypes.Input) == (int)IoTypes.Input)
                            && (((int)io & (int)IoTypes.Output) == (int)IoTypes.Output))
                        {
                            return SortOrder.APDU;
                        }
                        return order;
                    });
        }

        #endregion
    }
}