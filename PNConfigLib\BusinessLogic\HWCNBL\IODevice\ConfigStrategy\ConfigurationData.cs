/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConfigurationData.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Addresses;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    /// <summary>
    /// ConfigurationData block related part of the PNDeviceConfigStrategy class.
    /// </summary>
    internal partial class PNDeviceConfigStrategy
    {
        //########################################################################################

        #region Fields

        /// <summary>
        /// The dictionary that contains the configuration data.
        /// </summary>
        private IDictionary<string, List<ARConfigSubslotStruct>> m_ConfigData;

        #endregion

        //########################################################################################

        #region Properties

        /// <summary>
        /// The accessor for m_ConfigData.
        /// </summary>
        protected virtual IDictionary<string, List<ARConfigSubslotStruct>> ConfigData
        {
            set { m_ConfigData = value; }
            get { return m_ConfigData; }
        }

        #endregion

        //########################################################################################

        #region Nested Classes

        #endregion

        //########################################################################################

        #region Constants and Enums

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion

        //########################################################################################

        #region Public Methodspublic 

        /// <summary>
        /// Initializes the configuration data.
        /// </summary>
        private void InitializeConfigData()
        {
            if (ConfigData == null)
            {
                ConfigData = new Dictionary<string, List<ARConfigSubslotStruct>>();
            }
            else
            {
                ConfigData.Clear();
            }

            bool isApduController = IsApduController();

            foreach (PclObject module in Modules)
            {
                List<ARConfigSubslotStruct> slotConfiguration = new List<ARConfigSubslotStruct>();

                // check whether the module has Input, Output or Diagnostic address
                bool hasInOrOutOrDiagAddr = ConfigUtility.HasModuleInOutOrDiagAddress(module);

                // if a physical module has Input, Output or Diagnostic address(es) it must have subslot entry
                if (hasInOrOutOrDiagAddr)
                {
                    ARConfigSubslotStruct subslotConfiguration = CreateSubslotData(module, false, isApduController);
                    if (subslotConfiguration != null)
                    {
                        slotConfiguration.Add(subslotConfiguration);
                    }
                }

                IList<PclObject> submodules = GetSubmodulesInternal(module);
                // no submodule found => create dummy-submodule entry for each module, but only if module has not got address
                if ((submodules.Count == 0)
                    && !hasInOrOutOrDiagAddr)
                {
                    ARConfigSubslotStruct subslotConfiguration = CreateSubslotData(module, true, isApduController);
                    AddSubSlotConfiguration(subslotConfiguration, slotConfiguration);
                }
                else
                {
                    // create submodule entries
                    foreach (PclObject submodule in submodules)
                    {
                        ARConfigSubslotStruct subslotConfiguration =
                            CreateSubslotData(submodule, false, isApduController);
                        AddSubSlotConfiguration(subslotConfiguration, slotConfiguration);
                    }
                }

                ConfigData.Add(module.Id, slotConfiguration);
            }
        }

        /// <summary>
        /// Add subslot configuration to slot configuration list
        /// </summary>
        /// <param name="subslotConfiguration"></param>
        /// <param name="slotConfiguration"></param>
        private static void AddSubSlotConfiguration(
            ARConfigSubslotStruct subslotConfiguration,
            ICollection<ARConfigSubslotStruct> slotConfiguration)
        {
            if (subslotConfiguration != null)
            {
                slotConfiguration.Add(subslotConfiguration);
            }
        }

        /// <summary>
        /// Gets the configuration subslot data.
        /// </summary>
        /// <param name="module">The module whose subslot data will be retrieved.</param>
        /// <param name="submodule">The submodule whose subslot data will be retrieved.</param>
        /// <param name="api">The API number for the subslot data to be retrieved.</param>
        /// <returns></returns>
        public virtual byte[] GetConfigurationSubslotData(
            PclObject module,
            PclObject submodule,
            long api)
        {
            int subslotNumber = GetSubmodulesPNSubslotNumber(submodule);

            if (ConfigData.ContainsKey(module.Id))
            {
                //get slot configuration of given module
                List<ARConfigSubslotStruct> slotConfiguration = ConfigData[module.Id];

                foreach (ARConfigSubslotStruct subslotConfiguration in slotConfiguration)
                {
                    //filter relevant subslots
                    if ((subslotConfiguration.SubslotNumber == subslotNumber)
                        && (subslotConfiguration.api == api))
                    {
                        return subslotConfiguration.ToByteArray;
                    }
                }
            }

            return new byte[0];
        }

        /// <summary>
        /// Gets the subslotnumber from configuration subslot data
        /// </summary>
        /// <param name="module">The module whose data will be retrieved.</param>
        /// <param name="submodule">The submodule whose data will be retrieved</param>
        /// <param name="api">The API number.</param>
        /// <returns></returns>
        public virtual int GetSubslotNumber(PclObject module, PclObject submodule, long api)
        {
            int subslotNumber = GetSubmodulesPNSubslotNumber(submodule);

            if (ConfigData.ContainsKey(module.Id))
            {
                //get slot configuration of given module
                List<ARConfigSubslotStruct> slotConfiguration = ConfigData[module.Id];

                bool isSubslotNumberValid =
                    slotConfiguration.Any(p => (p.SubslotNumber == subslotNumber) && (p.api == api));

                if (isSubslotNumberValid)
                {
                    return subslotNumber;
                }
            }

            subslotNumber = 1;
            return subslotNumber;
        }

        /// <summary>
        /// Provides the complete configuration data block, without header.
        /// </summary>
        /// <returns>The byte array containing the data block.</returns>
        private byte[] GetConfigDataBlock()
        {
            ICollection<List<ARConfigSubslotStruct>> cfgSlot = ConfigData.Values;

            List<byte> list = new List<byte>();

            foreach (List<ARConfigSubslotStruct> slot in cfgSlot)
            {
                foreach (ARConfigSubslotStruct cfgSubSlot in slot)
                {
                    list.AddRange(cfgSubSlot.ToByteArray);
                }
            }

            return list.ToArray();
        }

        #endregion

        //########################################################################################

        #region Private Implementation

        /// <summary>
        /// Gets the submodule ident number of a submodule.
        /// </summary>
        /// <param name="submodule">The submodule whose ident number will be retrieved.</param>
        /// <returns></returns>
        protected virtual uint GetPNSubmoduleIdentNumber(PclObject submodule)
        {
            return submodule.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnSubmoduleIdentNumber,
                new AttributeAccessCode(),
                0);
        }

        /// <summary>
        /// Creates the subslot data block.
        /// </summary>
        /// <param name="submodule">The submodule whose data block will be generated.</param>
        /// <param name="dummySubmodule">Whether the submodule is a dummy submodule.</param>
        /// <param name="isApduController">Whether the controller is an APDU controller.</param>
        /// <returns></returns>
        protected virtual ARConfigSubslotStruct CreateSubslotData(
            PclObject submodule,
            bool dummySubmodule,
            bool isApduController)
        {
            ARConfigSubslotStruct subslot = new ARConfigSubslotStruct();
            AttributeAccessCode ac = new AttributeAccessCode();

            subslot.SubslotNumber = dummySubmodule ? 1 : GetSubmodulesPNSubslotNumber(submodule);

            subslot.SubmoduleIdentNr = GetPNSubmoduleIdentNumber(submodule);

            ac.Reset();
            subslot.api = submodule.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnAPI, ac, 0);

            ac.Reset();
            IoTypes ioType = (IoTypes)submodule.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.IoType, ac, 0);

            ARConfigSubslotDataStruct subslotData = new ARConfigSubslotDataStruct();

            if (AddressUtility.IsPackedModule(submodule))
            {
                ioType |= IoTypes.Diagnosis;
            }

            switch (ioType)
            {
                case IoTypes.Diagnosis:
                    {
                        if (!Utility.GetIOXSRequired(this.Interface))
                        {
                            subslot.SetDiscardIOXS(1);
                        }

                        subslot.SubmoduleType = (int)SubmoduleType.NoData;
                        subslotData.SetDataDescriptionType((int)DataType.Input);
                        subslotData.SetDataLength(0);

                        subslot.AddSubmoduleData(subslotData);

                        break;
                    }
                case IoTypes.Output:
                    {
                        subslot.SubmoduleType = (int)SubmoduleType.OutputData;
                        subslotData.SetDataDescriptionType((int)DataType.Output);
                        subslotData.SetDataLength(
                            submodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.OutAddressRange,
                                null,
                                0));

                        subslot.AddSubmoduleData(subslotData);

                        break;
                    }
                case IoTypes.Input:
                    {
                        subslot.SubmoduleType = (int)SubmoduleType.InputData;
                        subslotData.SetDataDescriptionType((int)DataType.Input);
                        int inAddressRange =
                            submodule.AttributeAccess.GetAnyAttribute<int>(
                                InternalAttributeNames.InAddressRange,
                                null,
                                0);
                        subslotData.SetDataLength(inAddressRange);

                        if (inAddressRange == 0)
                        {
                            subslot.SubmoduleType = (int)SubmoduleType.NoData;
                        }
                        subslot.AddSubmoduleData(subslotData);

                        break;
                    }
                case IoTypes.Input | IoTypes.Output:
                    {
                        subslot.SubmoduleType = (int)SubmoduleType.InputOutputData;

                        SortOrder sortOrder = isApduController ? SortOrder.APDU : SortOrder.IO;
                        List<DataAddress> addressObjects = ConfigUtility.GetAddressObjectsSorted(
                            submodule,
                            sortOrder);
                        foreach (DataAddress addressObj in addressObjects)
                        {
                            subslotData = new ARConfigSubslotDataStruct();

                            IoTypes io = addressObj.IoType;

                            if (io == IoTypes.Input)
                            {
                                subslotData.SetDataDescriptionType((int)DataType.Input);
                                subslotData.SetDataLength(
                                    submodule.AttributeAccess.GetAnyAttribute<int>(
                                        InternalAttributeNames.InAddressRange,
                                        null,
                                        0));
                            }
                            else if (io == IoTypes.Output)
                            {
                                subslotData.SetDataDescriptionType((int)DataType.Output);
                                subslotData.SetDataLength(
                                    submodule.AttributeAccess.GetAnyAttribute<int>(
                                        InternalAttributeNames.OutAddressRange,
                                        null,
                                        0));
                            }

                            subslot.AddSubmoduleData(subslotData);
                        }

                        break;
                    }
                case IoTypes.Input | IoTypes.Diagnosis:
                case IoTypes.Output | IoTypes.Diagnosis:
                case IoTypes.Input | IoTypes.Output | IoTypes.Diagnosis:
                    {
                        subslot.SubmoduleType = (int)SubmoduleType.NoData;
                        subslotData.SetDataDescriptionType((int)DataType.Input);
                        subslotData.SetDataLength(0);

                        subslot.AddSubmoduleData(subslotData);

                        break;
                    }
                default:
                    {
                        // Don't create Subslotdata if there is no valid address (e.g. Non-PDEV Interface)
                        return null;
                    }
            }

            return subslot;
        }

        #endregion
    }
}