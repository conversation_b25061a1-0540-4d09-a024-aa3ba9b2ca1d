﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_041.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml;
using System.Xml.Linq;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.41 and is based on GSD(ML) versions 2.4 and lower.
    ///		
    /// </summary>
    internal class CheckerV02041 : CheckerV0204
    {
        #region Fields

        #endregion


        #region Properties

        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:CertificationInfo/@SecurityClass";
                return (xp);
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList;
                return (xp);
            }
        }

        #endregion


        #region CheckerObject Members


        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02041;
        }
        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02041;
        }


        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00041000);
            Checks.Add(Constants.s_Cn_0X00041001);
            Checks.Add(Constants.s_Cn_0X00041002);

            return succeeded;
        }

        override protected bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                //Checks.Remove(Constants.CN_0x000XXXXX);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion




        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.41.
        /// </summary>
        public CheckerV02041()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version241);
        }

        #endregion


        #region Methods

        /// <summary>
        /// Checks the MAUType against MAUTypeExtension for allowed combinations.
        /// 
        /// Note: The only known combinations in GSDML V2.41 is MAUType = 18 with MAUTypeExtension = 256 for POF
        ///       and MAUType = 141 with MAUTypeExtension = 512 for APL.
        ///       For all other MAUTypes only 0 is allowed for attribute 'Extension' as before.
        /// </summary>
        /// <param></param>
        /// <returns></returns>
        protected override void CheckMAUTypeExtensionCombinations(XElement mauTypeItem)
        {
            UInt16 mauType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Value));
            var extensionStr = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_Extension);
            if (string.IsNullOrEmpty(extensionStr))
                return;

            bool extensionCorrect = true;
            UInt16 extension = XmlConvert.ToUInt16(extensionStr);
            if (extension <= 0)
                return;

            switch (mauType)  // be prepared to check many extension - MAUType combinations
            {
                case 18:
                    if (extension != 0x100)
                        extensionCorrect = false;
                    break;
                case 141:
                    if (extension != 0x200)
                        extensionCorrect = false;
                    break;
                default:
                    extensionCorrect = false;
                    break;
            }
            if (!extensionCorrect)
            {
                if (Help.CheckSchemaVersion(mauTypeItem, SupportedGsdmlVersion))
                {
                    // "The given combination of 'MAUTypeItem/@Extension' and 'MAUTypeItem/@Value' is not allowed."
                    string msg = Help.GetMessageString("M_0x00032005_4");
                    string xpath = Help.GetXPath(mauTypeItem);
                    IXmlLineInfo xli = mauTypeItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00032005_4");
                }
            }
        }

        /// <summary>
        /// 'ProfileProcessAutomation/@PAProfileVersion' shall contain "4.01".
        /// </summary>
        /// <param></param>
        /// <returns></returns>
        protected virtual void CheckPaProfileVersion(XElement profileProcessAutomation)
        {
            string paProfileVersionStr = Help.GetAttributeValueFromXElement(profileProcessAutomation, Attributes.s_PaProfileVersion);
            if (string.IsNullOrEmpty(paProfileVersionStr))
            {
                return;
            }

            double paProfileVersion = GetPNioVersion(paProfileVersionStr);
            if (paProfileVersion == 4.01)
            {
                return;
            }

            // "The 'PAProfileVersion' attribute must contain the value "V4.01"."
            string msg = Help.GetMessageString("M_0x00041002_2");
            string xpath = Help.GetXPath(profileProcessAutomation.Attribute(Attributes.s_PaProfileVersion));
            IXmlLineInfo xli = profileProcessAutomation.Attribute(Attributes.s_PaProfileVersion);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00041002_2");
            }
        }


        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();
        }


        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            IList<string> tokens1 = new List<string>();
            tokens1.Add("1");
            tokens1.Add("2");
            tokens1.Add("3");
            AttributeTokenDictionary.Add("SecurityClass", tokens1);

            var tokens2 = AttributeTokenDictionary["IsochroneModeInRT_Classes"];
            tokens2.Remove("RT_CLASS_STREAM_LO");
        }

        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// The attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// As of GSDML V2.4:
        /// - New values 0x004F - 0x0066 for MAUTypes
        /// As of GSDML V2.41
        /// - New values 103..113, 141, 144 for MAUTypes
        /// 
        /// Partly the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs into which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            AttributeValueListDictionary.Remove("MAUTypes-v2.35");
            string values1 = "-v2.4 0 5 10..13 15..18 21..26 29..102";              // gilt für pnio_version > 2.35
            AttributeValueListDictionary.Add("MAUTypes-v2.35", values1);
            string values2 = "0 5 10..13 15..18 21..26 29..102 103..113 141 144";   // gilt für pnio_version > 2.4
            AttributeValueListDictionary.Add("MAUTypes-v2.4", values2);
        }

        #endregion


        #region Overrides

        #endregion


        /// <summary>
        /// Check number: CN_0x00041000
        /// 
        /// Ensure that all elements under 'ds:Signature' are schema checked.
        /// This means that all these elements must be defined in one of the namespaces
        /// "http://www.w3.org/2000/09/xmldsig#" or "http://uri.etsi.org/01903/v1.3.2#".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00041000()
        {
            XNamespace ns = Nsmgr.LookupNamespace("gsdsig");
            var sig = Gsd.Descendants(NamespaceGsdDef + Elements.s_Iso15745Profile)
                         .Elements(ns + Elements.s_Signature).FirstOrDefault();

            if (sig == null)
                return true;

            var nl = sig.Descendants();
            foreach (var el in nl)
            {
                string namespaceName = el.Name.NamespaceName;
                if (namespaceName != "http://www.w3.org/2000/09/xmldsig#" &&
                    namespaceName != "http://uri.etsi.org/01903/v1.3.2#")
                {
                    // "Elements under 'ds:Signature' must be defined in one of the namespaces
                    // "http://www.w3.org/2000/09/xmldsig#" or "http://uri.etsi.org/01903/v1.3.2#"."
                    string msg = Help.GetMessageString("M_0x00041000_1");
                    string xpath = Help.GetXPath(el);
                    var xli = (IXmlLineInfo)el;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00041000_1");                
                }
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00041001
        /// 
        /// If at least one attribute 'DeviceAccessPointItem/CertificationInfo/SecurityClass' exists
        /// the elements 'ds:Signature' must be available.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00041001()
        {
            bool signatureNeeded = false;
            XAttribute securityClass = null;
            var certificationInfoList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo);
            certificationInfoList = Help.TryRemoveXElementsUnderXsAny(certificationInfoList, Nsmgr, Gsd);
            foreach (var certificationInfo in certificationInfoList)
            {
                securityClass = certificationInfo.Attribute(Attributes.s_SecurityClass);
                if (securityClass != null)
                {
                    signatureNeeded = true;
                    break;
                }
            }

            if (!signatureNeeded)
                return true;

            XNamespace ns = Nsmgr.LookupNamespace("gsdsig");
            var sig = Gsd.Descendants(NamespaceGsdDef + Elements.s_Iso15745Profile)
                         .Elements(ns + Elements.s_Signature).FirstOrDefault();

            if (sig == null)
            {
                // "At least one attribute 'SecurityClass' exists, but
                // the element 'ds:Signature' is not available."
                string msg = Help.GetMessageString("M_0x00041001_1");
                string xpath = Help.GetXPath(securityClass);
                var xli = (IXmlLineInfo)securityClass;
                Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00041001_1");
            }

            return true;
        }


        /// <summary>
        /// Check number: CN_0x00041002
        /// 
        /// Checks against element 'ProfileProcessAutomation' and its attributes.
        /// 
        /// (1) The element 'CertificationInfo/ProfileProcessAutomation' shall be present
        ///     if 'DeviceAccessPointItem/@PNIO_Version' >= V2.41 and the attribute 
        ///     'ApplicationClass' contains the token ProcessAutomation.
        /// (2) 'ProfileProcessAutomation/@PAProfileVersion' shall contain "4.01".
        /// (3) 'ProfileProcessAutomation/@PAProfileDeviceID' shall be in the range from 0xB000 to 0xBFFF.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00041002()
        {
            // Find all CertificationInfo elements
            var certificationInfoList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_CertificationInfo);
            certificationInfoList = Help.TryRemoveXElementsUnderXsAny(certificationInfoList, Nsmgr, Gsd);
            foreach (var certificationInfo in certificationInfoList)
            {
                var dap = certificationInfo.Parent;
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                IList<string> applicationClasses = GetCombinedApplicationClasses(dap);

                if (!applicationClasses.Contains("ProcessAutomation") || pnioVersion < 2.41)    // Nothing to do
                    return true;

                // (1)
                var profileProcessAutomation = certificationInfo.Element(NamespaceGsdDef + Elements.s_ProfileProcessAutomation);
                if (profileProcessAutomation == null)
                {
                    // "If 'PNIO_Version' >= V2.41 and the attribute 'ApplicationClass' contains the token ProcessAutomation",
                    // the element 'CertificationInfo/ProfileProcessAutomation' must be present."
                    string msg = Help.GetMessageString("M_0x00041002_1");
                    string xpath = Help.GetXPath(certificationInfo);
                    IXmlLineInfo xli = certificationInfo;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00041002_1");
                    return true;
                }

                // (2)
                CheckPaProfileVersion(profileProcessAutomation);

                // (3)
                string paProfileDeviceIDStr = Help.GetAttributeValueFromXElement(profileProcessAutomation, Attributes.s_PaProfileDeviceId);
                if (string.IsNullOrEmpty(paProfileDeviceIDStr))
                {
                    continue;
                }

                {
                    UInt32 paProfileDeviceId = UInt32.Parse(
                        paProfileDeviceIDStr.Substring(2),
                        NumberStyles.HexNumber,
                        CultureInfo.InvariantCulture);
                    if (paProfileDeviceId >= 0xb000
                        && paProfileDeviceId <= 0xbfff)
                    {
                        continue;
                    }

                    // "The value of 'PAProfileDeviceID' must be between "0xB000" and "0xBFFF"."
                    string msg = Help.GetMessageString("M_0x00041002_3");
                    string xpath = Help.GetXPath(profileProcessAutomation.Attribute(Attributes.s_PaProfileDeviceId));
                    IXmlLineInfo xli = profileProcessAutomation.Attribute(Attributes.s_PaProfileDeviceId);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00041002_3");
                    }
                }
            }

            return true;
        }

        #endregion
    }
}
