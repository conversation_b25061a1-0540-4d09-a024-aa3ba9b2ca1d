/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ModulePlugData.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// Specifies the slots where the module can be plugged and also 
    /// the slots where the module is plugged by default.
    /// </summary>
    public class ModulePlugData :
        GsdObject,
        GSDI.IModulePlugData
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ModulePlugData()
        {
            // Initialize the properties
            m_SubmodulePlugDataModel = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private List<uint> m_AllowedInSlots;
        private List<uint> m_UsedInSlots;
        private List<uint> m_FixedInSlots;

        private String m_AllowedInSlotsString;
        private String m_UsedInSlotsString;
        private String m_FixedInSlotsString;

        private bool m_SubmodulePlugDataModel;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of slot numbers, on which the module can be plugged
        /// by the configurator.
        /// </summary>
        public virtual Array AllowedInSlots
        {

            get
            {
                if (m_AllowedInSlots != null)
                {
                    return new ArrayList(m_AllowedInSlots).ToArray();
                }
                else if (!String.IsNullOrEmpty(m_AllowedInSlotsString))
                {
                    m_AllowedInSlots = ValueListHelper.SeparateUnsignedValueList(m_AllowedInSlotsString);
                    return new ArrayList(m_AllowedInSlots).ToArray();
                }
                return null;
            }

        }

        /// <summary>
        /// Accesses a list of slot numbers, on which the module should be plugged
        /// by default. If desired, the module can be removed from this slot numbers
        /// from the configuration.
        /// </summary>
        public virtual Array UsedInSlots
        {
            get
            {
                if (m_UsedInSlots != null)
                {
                    return new ArrayList(m_UsedInSlots).ToArray();
                }
                else if (!String.IsNullOrEmpty(m_UsedInSlotsString))
                {
                    m_UsedInSlots = ValueListHelper.SeparateUnsignedValueList(m_UsedInSlotsString);
                    return new ArrayList(m_UsedInSlots).ToArray();
                }
                return null;
            }
        }

        /// <summary>
        /// Accesses a list of slot numbers, on which the module should be plugged
        /// by default. Unlike to the slot numbers in UsedInSlots, the module can't 
        /// be removed from this slot numbers from the configuration.
        /// </summary>
        public virtual Array FixedInSlots
        {
            get
            {
                if (m_FixedInSlots != null)
                {
                    return new ArrayList(m_FixedInSlots).ToArray();
                }
                else if (!String.IsNullOrEmpty(m_FixedInSlotsString))
                {
                    m_FixedInSlots = ValueListHelper.SeparateUnsignedValueList(m_FixedInSlotsString);
                    return new ArrayList(m_FixedInSlots).ToArray();
                }
                return null;
            }
        }

        protected bool IsAllowedInSlot(object vSlot)
        {
            System.UInt32 slot = System.Convert.ToUInt32(vSlot, CultureInfo.InvariantCulture);

            if (m_SubmodulePlugDataModel)
            {
                if (String.IsNullOrEmpty(m_AllowedInSlotsString))
                {
                    // For pluggable submodules, a missing AllowedInSubslots attribute means
                    // that the submodule can be plugged on any subslot.
                    return true;
                }
            }

            if (!String.IsNullOrEmpty(m_AllowedInSlotsString))
            {
                m_AllowedInSlots = ValueListHelper.SeparateUnsignedValueList(m_AllowedInSlotsString);
            }

            if (null != this.m_AllowedInSlots)
            {
                return this.m_AllowedInSlots.Contains(slot);
            }

            // NOTE: If the allowed slots are not explicitly specified,
            //       NO slots are allowed here.
            return false;

        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;
            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                string member = Models.s_FieldAllowedInSlots;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_AllowedInSlots = hash[member] as List<uint>;

                member = Models.s_FieldUsedInSlots;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_UsedInSlots = hash[member] as List<uint>;

                member = Models.s_FieldFixedInSlots;
                if (hash.ContainsKey(member) && hash[member] is List<uint>)
                    this.m_FixedInSlots = hash[member] as List<uint>;

                member = Models.s_FieldAllowedInSlotsString;
                if (hash.ContainsKey(member) && hash[member] is String)
                    this.m_AllowedInSlotsString = hash[member] as String;

                member = Models.s_FieldUsedInSlotsString;
                if (hash.ContainsKey(member) && hash[member] is String)
                    this.m_UsedInSlotsString = hash[member] as String;

                member = Models.s_FieldFixedInSlotsString;
                if (hash.ContainsKey(member) && hash[member] is String)
                    this.m_FixedInSlotsString = hash[member] as String;

                member = Models.s_FieldSubmodulePlugDataModel;
                if (hash.ContainsKey(member) && hash[member] is bool)
                    this.m_SubmodulePlugDataModel = (bool)hash[member];


            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;

        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectModulePlugData);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            if (AllowedInSlots != null)
                Export.WriteArrayUint32Property(ref writer, Models.s_FieldAllowedInSlots, new ArrayList(this.m_AllowedInSlots), true);

            if (UsedInSlots != null)
                Export.WriteArrayUint32Property(ref writer, Models.s_FieldUsedInSlots, new ArrayList(this.m_UsedInSlots), true);

            if (FixedInSlots != null)
                Export.WriteArrayUint32Property(ref writer, Models.s_FieldFixedInSlots, new ArrayList(this.m_FixedInSlots), true);

            Export.WriteStringProperty(ref writer, Models.s_FieldAllowedInSlotsString, this.m_AllowedInSlotsString);
            Export.WriteStringProperty(ref writer, Models.s_FieldUsedInSlotsString, this.m_UsedInSlotsString);
            Export.WriteStringProperty(ref writer, Models.s_FieldFixedInSlotsString, this.m_FixedInSlotsString);

            return true;
        }

        #endregion
    }
}


