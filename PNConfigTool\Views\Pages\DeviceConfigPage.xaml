<Page x:Class="PNConfigTool.Views.Pages.DeviceConfigPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Loaded="Page_Loaded"
      Title="设备配置">

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="80"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock x:Name="PageTitle" Grid.Row="0" Text="设备配置" FontSize="18" FontWeight="Bold" Margin="10,10,10,15"/>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧模块配置表格 -->
            <Grid Grid.Column="0" Margin="10,0,5,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 模块配置表格 -->
                <DataGrid x:Name="ModulesDataGrid" Grid.Row="0" AutoGenerateColumns="False"
                          CanUserAddRows="False" CanUserDeleteRows="False"
                          HeadersVisibility="Column"
                          BorderBrush="#CCCCCC" BorderThickness="1"
                          ToolTip="模块配置表格"
                          LoadingRow="ModulesDataGrid_LoadingRow"
                          BeginningEdit="ModulesDataGrid_BeginningEdit"
                          CellEditEnding="ModulesDataGrid_CellEditEnding"
                          AllowDrop="True"
                          DragOver="ModulesDataGrid_DragOver"
                          Drop="ModulesDataGrid_Drop">
                    <DataGrid.RowStyle>
                        <Style TargetType="DataGridRow">
                            <Style.Triggers>
                                <!-- 内置模块行样式 -->
                                <DataTrigger Binding="{Binding IsBuiltIn}" Value="True">
                                    <Setter Property="Background" Value="#F5F5F5"/>
                                    <Setter Property="Foreground" Value="#666666"/>
                                    <Setter Property="IsEnabled" Value="False"/>
                                    <Setter Property="ToolTip" Value="此为DAP内置模块，不可编辑"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.RowStyle>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="" Binding="{Binding Index}" Width="50" IsReadOnly="True"/>
                        <DataGridCheckBoxColumn Binding="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Width="40">
                            <DataGridCheckBoxColumn.Header>
                                <CheckBox x:Name="SelectAllCheckBox"
                                          ToolTip="全选/取消全选"
                                          Checked="SelectAllCheckBox_Checked"
                                          Unchecked="SelectAllCheckBox_Unchecked"/>
                            </DataGridCheckBoxColumn.Header>
                            <DataGridCheckBoxColumn.ElementStyle>
                                <Style TargetType="CheckBox">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsBuiltIn}" Value="True">
                                            <Setter Property="IsEnabled" Value="False"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGridCheckBoxColumn.ElementStyle>
                        </DataGridCheckBoxColumn>
                        <DataGridTextColumn Header="序号" Binding="{Binding Position}" Width="50" IsReadOnly="True"/>
                        <DataGridTextColumn Header="模块名" Binding="{Binding ModuleName}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="子模块名" Binding="{Binding SubmoduleName}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="插槽_子插槽" Binding="{Binding SlotSubslot}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="PNI起始地址" Binding="{Binding PNIStartAddress}" Width="100"/>
                        <DataGridTextColumn Header="输入长度（字节）" Binding="{Binding InputLength}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="PNQ起始地址" Binding="{Binding PNQStartAddress}" Width="100"/>
                        <DataGridTextColumn Header="输出长度（字节）" Binding="{Binding OutputLength}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="固件版本" Binding="{Binding FirmwareVersion}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="注释" Binding="{Binding Comment}" Width="100" IsReadOnly="True"/>
                    </DataGrid.Columns>
                </DataGrid>
                
                <!-- 按钮区域 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5,0,0">
                    <Button x:Name="AddModuleButton" Content="添加" Width="80" Margin="0,0,5,0" Click="AddModuleButton_Click"/>
                    <Button x:Name="RemoveModuleButton" Content="删除选中" Width="80" Margin="5,0,5,0" Click="RemoveModuleButton_Click"/>
                    <Button x:Name="IOStatusButton" Content="IO使用状态" Width="80" Margin="5,0,0,0" Click="IOStatusButton_Click" ToolTip="查看当前设备的输入/输出长度使用状态"/>

                    <!-- 新增设置项 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="30,0,0,0">
                        <TextBlock Text="更新时间 (ms)" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox x:Name="UpdateTimeComboBox" Width="70" SelectedValue="{Binding UpdateTimeValue}" SelectedValuePath="Tag" Margin="0,0,20,0">
                            <!-- 动态填充内容，由代码后台根据GSDML文件中的ReductionRatio值生成 -->
                        </ComboBox>
                        <TextBlock Text="数据保持" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox x:Name="AcceptedUpdateCyclesWithoutIODataComboBox"
                                  Width="60"
                                  MaxDropDownHeight="200"
                                  SelectedValue="{Binding AcceptedUpdateCyclesWithoutIOData}"
                                  SelectedValuePath="Tag"
                                  SelectionChanged="OnAcceptedUpdateCyclesChanged">
                            <!-- 动态填充内容，由代码后台生成3-255的完整范围 -->
                        </ComboBox>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <!-- 分隔条 -->
            <GridSplitter Grid.Column="1" 
                          Width="5"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Stretch"
                          Background="#E0E0E0"
                          ShowsPreview="True"/>

            <!-- 右侧模块树 -->
            <Grid Grid.Column="2" Margin="5,0,10,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 模块选择说明 -->
                <TextBlock Grid.Row="0" Text="选择要添加的模块" FontWeight="SemiBold" Margin="0,0,0,5"/>
                
                <!-- 模块树 -->
                <TreeView x:Name="ModulesTreeView" Grid.Row="1" BorderBrush="#CCCCCC" BorderThickness="1"
                          MouseMove="ModulesTreeView_MouseMove"
                          MouseLeftButtonDown="ModulesTreeView_MouseLeftButtonDown">
                    <TreeViewItem Header="IOD-Linux Native" IsExpanded="True">
                        <TreeViewItem Header="主模块" IsExpanded="True">
                            <TreeViewItem Header="IOD-Linux Native"/>
                        </TreeViewItem>
                        <TreeViewItem Header="模块" IsExpanded="True">
                            <TreeViewItem Header="1 byte I"/>
                            <TreeViewItem Header="1 byte IO"/>
                            <TreeViewItem Header="1 byte O"/>
                            <TreeViewItem Header="64 bytes I"/>
                            <TreeViewItem Header="64 bytes IO"/>
                            <TreeViewItem Header="64 bytes O"/>
                            <TreeViewItem Header="250 bytes I"/>
                            <TreeViewItem Header="250 bytes IO"/>
                            <TreeViewItem Header="250 bytes O"/>
                            <TreeViewItem Header="Multi subslots I1 I1 IO IO"/>
                        </TreeViewItem>
                        <TreeViewItem Header="子模块"/>
                    </TreeViewItem>
                </TreeView>
            </Grid>
        </Grid>

        <!-- Media redundancy 区域 -->
        <Grid Grid.Row="2" Margin="10,5,10,5">
            <GroupBox Header="Media redundancy" Padding="10">
                <Grid>
                    <!-- MRP控件区域 -->
                    <StackPanel x:Name="MRPControlsPanel" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Media redundancy role -->
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                            <TextBlock Text="Media redundancy role:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <ComboBox x:Name="MediaRedundancyRoleComboBox" Width="180" VerticalAlignment="Center" SelectionChanged="MediaRedundancyRoleComboBox_SelectionChanged">
                                <ComboBoxItem Content="Not device in the ring" IsSelected="True"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Ring port 1 -->
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                            <TextBlock Text="Ring port 1:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <ComboBox x:Name="RingPort1ComboBox" Width="120" VerticalAlignment="Center" SelectionChanged="RingPort1ComboBox_SelectionChanged">
                                <ComboBoxItem Content="Port1"/>
                                <ComboBoxItem Content="Port2"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Ring port 2 -->
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="Ring port 2:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <ComboBox x:Name="RingPort2ComboBox" Width="120" VerticalAlignment="Center" SelectionChanged="RingPort2ComboBox_SelectionChanged">
                                <ComboBoxItem Content="Port1"/>
                                <ComboBoxItem Content="Port2"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>

                    <!-- MRP不支持提示 -->
                    <TextBlock x:Name="MRPNotSupportedTextBlock"
                               Text="该设备不支持Media redundancy"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               FontSize="14"
                               Foreground="Gray"
                               Visibility="Collapsed"/>
                </Grid>
            </GroupBox>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="3" Background="#F0F0F0">
            <!-- 左侧按钮 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="10">
                <Button x:Name="PreviousButton" Content="&lt; 上一步" Width="100" Padding="10,5" Margin="0,0,10,0" Click="PreviousButton_Click"/>
                <Button x:Name="NextButton" Content="下一步 >" Width="100" Padding="10,5" Click="NextButton_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page> 