/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ValueItem.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The ValueItem defines a concrete value with his value and his 
    /// assigned text. It is needed to specify a concrete value within 
    /// the RefData objects.
    /// </summary>
    public class ValueItem :
        GsdObject,
        GSDI.IValueItem
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public ValueItem()
        {
            m_Text = String.Empty;
            m_TextID = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        private object m_Value;
        private string m_Text;
        private string m_TextID;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the concrete value.
        /// </summary>
        public object Value => this.m_Value;

        /// <summary>
        /// Accesses the associated text for the concrete value.
        /// </summary>
        public string Text => this.m_Text;

        /// <summary>
        /// Accesses the text id of the associated text.
        /// </summary>
        public string TextID => this.m_TextID;
        protected string ValueAsString
        {
            get
            {
                if (null != this.m_Value)
                    return this.m_Value.ToString();
                return String.Empty;
            }
        }


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldValue;
                if (hash.ContainsKey(member))
                    this.m_Value = hash[member];

                member = Models.s_FieldText;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Text = hash[member] as string;

                member = Models.s_FieldTextId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_TextID = hash[member] as string;
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectValueItem);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            string val = String.Empty;
            if (null != this.m_Value)   // Normally object, not string!
                val = this.m_Value.ToString();
            Export.WriteStringProperty(ref writer, Models.s_FieldValue, val);
            Export.WriteStringProperty(ref writer, Models.s_FieldText, this.m_Text);
            Export.WriteStringProperty(ref writer, Models.s_FieldTextId, this.m_TextID);

            return true;
        }


        #endregion

        //########################################################################################
        #region Object members

        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(ValueItem))
                return false;

            ValueItem value1 = this;
            ValueItem value2 = obj as ValueItem;

            if (value2 == null)
            {
                return false;
            }

            if (!value1.Value.Equals(value2.Value))
                return false;

            if (!value1.Text.Equals(value2.Text, StringComparison.Ordinal))
                return false;

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 17;

            hash = hash * 23 + Value.GetHashCode();
            hash = hash * 23 + Text.GetHashCode();

            return hash;
        }

        #endregion

    }
}


