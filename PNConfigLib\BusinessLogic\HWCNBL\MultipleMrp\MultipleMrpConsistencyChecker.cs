/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: MultipleMrpConsistencyChecker.cs          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

namespace PNConfigLib.HWCNBL.MultipleMrp
{
    internal static class MultipleMrpConsistencyChecker
    {
        /// <summary>
        /// This method checks if multiple MRP Ring Ports are configured correctly
        /// If one ring port is configured in multiple instances, a CC is shown
        /// </summary>
        internal static void CheckMultipleMRPRingPorts(Interface interfaceSubmodule)
        {
            List<MrpDomainInstance> mrpDomainInstancesOfInterface = interfaceSubmodule.MrpDomainInstances;
            if ((mrpDomainInstancesOfInterface == null) || (mrpDomainInstancesOfInterface.Count < 1))
            {
                return;
            }
            List<string> ringPortsOfInterface = new List<string>();
            List<string> multiUsedPorts = new List<string>();

            // First we collect each ports which are used multiple times on the interface
            foreach (MrpDomainInstance mrpDomainInstance in mrpDomainInstancesOfInterface)
            {
                foreach (DataModel.PCLObjects.Port ringPort in mrpDomainInstance.RingPorts)
                {
                    if (ringPort == null)
                    {
                        continue;
                    }

                    if (ringPortsOfInterface.Contains(ringPort.Id))
                    {
                        if (!multiUsedPorts.Contains(ringPort.Id))
                        {
                            multiUsedPorts.Add(ringPort.Id);
                        }
                    }
                    else
                    {
                        ringPortsOfInterface.Add(ringPort.Id);
                    }
                }
            }

            // If we found ports, which are used multiple time, we create an error message for each of them
            if (multiUsedPorts.Count <= 0)
            {
                return;
            }

            foreach (string portName in multiUsedPorts)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.MrpRingPortUsedInDifferentInstances,
                    portName);
            }
        }

        /// <summary>
        /// This method checks if multiple MRP Roles are configured correctly. It only runs when multiple mrp rings feature is active.
        /// It also checks whether or not a domain is attributed to multiple instances.
        /// </summary>
        internal static void CheckMultipleMRPRolesAndDomains(Interface interfaceSubmodule)
        {
            List<MrpDomainInstance> mrpDomainInstancesOfInterface = interfaceSubmodule.MrpDomainInstances;
            if ((mrpDomainInstancesOfInterface == null) || (mrpDomainInstancesOfInterface.Count < 1))
            {
                return;
            }

            Enumerated supportedMultipleMrpRolesAttribute = interfaceSubmodule.PCLCatalogObject.AttributeAccess.
                GetAnyAttribute<Enumerated>(InternalAttributeNames.PnMrpSupportedMultipleRole
                                            , new AttributeAccessCode(), null);
            //-----------------------------   Utility method getsupported multiple mrp roles from interface
            List<PNMrpRole> supportedMultipleMrpRoles = new List<PNMrpRole>();
            foreach (int child in supportedMultipleMrpRolesAttribute.List)
            {
                PNMrpRole currRole = (PNMrpRole)Enum.Parse(
                    typeof(PNMrpRole),
                    child.ToString(CultureInfo.InvariantCulture),
                    true);
                supportedMultipleMrpRoles.Add(currRole);
            }
            //-----------------------------

            Dictionary<MrpDomain, int> domains = new Dictionary<MrpDomain, int>();
            foreach (MrpDomainInstance mrpDomainInstance in mrpDomainInstancesOfInterface)
            {
                int instanceRole = mrpDomainInstance.AttributeAccess
                    .GetAnyAttribute<int>(InternalAttributeNames.PnInstanceMrpRole, new AttributeAccessCode(), 0);
                PNMrpRole tempRole = (PNMrpRole)instanceRole;

                if (tempRole != PNMrpRole.NotInRing)
                {
                    Int32 instanceNumber = mrpDomainInstance.AttributeAccess
                        .GetAnyAttribute<Int32>(InternalAttributeNames.PnInstanceNumber, new AttributeAccessCode(), 0);
                    if (!supportedMultipleMrpRoles.Contains(tempRole))
                    {
                        ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.MultipleMrpRoleNotSupported,
                                MultipleMrpUtilities.RoleToText(tempRole), instanceNumber, Utility.GetNameWithContainer(interfaceSubmodule));
                    }

                    MrpDomain tempDomain = interfaceSubmodule.Node.Subnet
                        .DomainList.Where(dmn => dmn is MrpDomain).OfType<MrpDomain>()
                        .FirstOrDefault(mrpDomain => mrpDomain.Id == mrpDomainInstance.MrpDomainId);
                    if (tempDomain != null)
                    {
                        if (domains.ContainsKey(tempDomain))
                        {
                            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.MrpDomainUsedInDifferentInstances
                                    , domains[tempDomain], instanceNumber
                                    , Utility.GetNameWithContainer(interfaceSubmodule)
                                    , AttributeUtilities.GetName(tempDomain));
                        }
                        else
                        {
                            domains.Add(tempDomain, instanceNumber);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// This method checks if Single MRP Roles are configured correctly in multiple MRP supporting device
        /// </summary>
        internal static void CheckSingleMRPRoles(Interface interfaceSubmodule)
        {
            //This CC is run when multiple MRP is not active
            PNMrpRole mrpRoleType = (PNMrpRole)interfaceSubmodule.AttributeAccess.
                GetAnyAttribute<UInt32>(InternalAttributeNames.PnMrpRole, new AttributeAccessCode(),
                    (UInt32)PNMrpRole.NotInRing);

            // Get the related Composite Attribute
            Enumerated supportedMrpRolesAttribute = interfaceSubmodule.PCLCatalogObject.AttributeAccess.
                GetAnyAttribute<Enumerated>(InternalAttributeNames.PnMrpRole, new AttributeAccessCode(), null);

            if (!supportedMrpRolesAttribute.List.OfType<PNMrpRole>().Contains(mrpRoleType))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, interfaceSubmodule, ConsistencyConstants.MrpRoleIsNotSupported,
                        MultipleMrpUtilities.RoleToText(mrpRoleType), Utility.GetNameWithContainer(interfaceSubmodule));
            }
        }
    }
}
