using Microsoft.Win32;
using PNConfigTool.Common;
using PNConfigTool.Services;
using PNConfigTool.Models;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

using System.Windows.Input;
using System.Windows;
using System.IO;
using System.Windows.Data;
using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using System.Diagnostics;
using System.Threading;
using PNConfigLib.Gsd.Interpreter;
using PNConfigLib.Gsd.Interpreter.Structure;
using GSDI;

namespace PNConfigTool.ViewModels
{
    public class DeviceCatalogEntry : ViewModelBase
    {
        public string GsdKey { get; }
        public string Name { get; set; } = string.Empty;
        public string VendorName { get; set; } = string.Empty;
        public bool IsExpanded { get; set; }

        public ObservableCollection<InterfaceCatalogEntry> Interfaces { get; } = new();

        public DeviceCatalogEntry(string gsdKey, string name = "未命名设备", string vendorName = "未知厂商")
        {
            GsdKey = gsdKey;
            Name = name;
            VendorName = vendorName;
        }


    }



    public class InterfaceCatalogEntry : ViewModelBase
    {
        public string GsdKey { get; }
        public string Name { get; set; } = string.Empty;
        public bool IsExpanded { get; set; }
        public ObservableCollection<PortCatalogEntry> Ports { get; } = new();

        public InterfaceCatalogEntry(string gsdKey, string name = "接口")
        {
            GsdKey = gsdKey;
            Name = name;
        }
    }



    public class PortCatalogEntry : ViewModelBase
    {
        public uint PortNumber { get; }
        public string Name { get; set; } = string.Empty;

        public PortCatalogEntry(uint portNumber, dynamic catalog)
        {
            PortNumber = portNumber;
            Name = $"端口 {portNumber}";
        }
    }

    /// <summary>
    /// GSDML目录查看器的ViewModel
    /// </summary>
    public class GSDMLCatalogViewModel : ViewModelBase, IDisposable
    {
        #region 字段

        private readonly IGSDMLService _gsdmlService;
        private bool _isLoading;
        private string _statusMessage = string.Empty;
        private int _gsdmlFilesCount;
        private int _processedFilesCount;

        // 添加信号量以防止并发刷新操作
        private readonly SemaphoreSlim _refreshSemaphore = new SemaphoreSlim(1, 1);

        #endregion

        #region 属性

        /// <summary>
        /// 设备列表
        /// </summary>
        public ObservableCollection<GSDMLDeviceInfo> Devices { get; } = new ObservableCollection<GSDMLDeviceInfo>();



        /// <summary>
        /// 接口列表
        /// </summary>
        public ObservableCollection<GSDMLInterfaceInfo> Interfaces { get; } = new ObservableCollection<GSDMLInterfaceInfo>();

        /// <summary>
        /// 制造商列表
        /// </summary>
        public ObservableCollection<GSDMLManufacturerNode> Manufacturers { get; } = new ObservableCollection<GSDMLManufacturerNode>();

        /// <summary>
        /// 主产品家族列表 - 新的组织结构
        /// </summary>
        public ObservableCollection<MainFamilyNode> MainFamilies { get; } = new ObservableCollection<MainFamilyNode>();

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// GSDML文件总数
        /// </summary>
        public int GSDMLFilesCount
        {
            get => _gsdmlFilesCount;
            set => SetProperty(ref _gsdmlFilesCount, value);
        }

        /// <summary>
        /// 已处理文件数量
        /// </summary>
        public int ProcessedFilesCount
        {
            get => _processedFilesCount;
            set => SetProperty(ref _processedFilesCount, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 刷新目录命令
        /// </summary>
        public ICommand RefreshCatalogCommand { get; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="gsdmlService">GSDML服务实例</param>
        public GSDMLCatalogViewModel(IGSDMLService gsdmlService)
        {
            _gsdmlService = gsdmlService ?? throw new ArgumentNullException(nameof(gsdmlService));
            
            // 初始化命令
            RefreshCatalogCommand = new RelayCommand(async () => await RefreshCatalogAsync());
            
            // 初始化时加载目录
            Task.Run(async () => await LoadGSDMLCatalogAsync());
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 刷新GSDML目录 - 防止并发刷新操作
        /// </summary>
        private async Task RefreshCatalogAsync()
        {
            // 使用信号量防止并发刷新操作
            if (!await _refreshSemaphore.WaitAsync(100)) // 100ms超时
            {
                StatusMessage = "刷新操作正在进行中，请稍候...";
                return;
            }

            try
            {
                await LoadGSDMLCatalogAsync();
            }
            finally
            {
                _refreshSemaphore.Release();
            }
        }

        /// <summary>
        /// Helper structure to return data from Interpreter parsing.
        /// </summary>
        private struct InterpreterParseResult
        {
            public string? VendorName { get; set; }
            public string? MainFamily { get; set; }
            public string? ProductFamily { get; set; }
            public string? Category { get; set; }
            public string? InfoText { get; set; }
            
            // 修改为集合以支持多个DAP
            public List<DAPInfo> DAPInfoList { get; set; }
            
            // 为了向后兼容，保留单一DAP属性
            public string? DAPName { 
                get => DAPInfoList.Count > 0 ? DAPInfoList[0].Name : "N/A"; 
                set { 
                    if (DAPInfoList.Count == 0) 
                        DAPInfoList.Add(new DAPInfo { Name = value }); 
                    else 
                        DAPInfoList[0].Name = value; 
                } 
            }
            public string? DAPId { 
                get => DAPInfoList.Count > 0 ? DAPInfoList[0].Id : "N/A"; 
                set { 
                    if (DAPInfoList.Count == 0) 
                        DAPInfoList.Add(new DAPInfo { Id = value }); 
                    else 
                        DAPInfoList[0].Id = value; 
                } 
            }
            
            public InterpreterParseResult()
            {
                VendorName = "N/A (Interpreter)";
                MainFamily = "N/A (Interpreter)";
                ProductFamily = "N/A (Interpreter)";
                Category = "N/A (Interpreter)";
                InfoText = "N/A (Interpreter)";
                DAPInfoList = new List<DAPInfo>();
            }
        }
        
        /// <summary>
        /// Helper structure to store DAP information
        /// </summary>
        private class DAPInfo
        {
            public string? Name { get; set; }
            public string? Id { get; set; }
            
            public override string ToString() => Name ?? "Unknown DAP";
        }

        /// <summary>
        /// Parses a GSDML file using PNConfigLib.Gsd.Interpreter to get device information.
        /// </summary>
        private async Task<InterpreterParseResult> GetDeviceDataFromInterpreterAsync(string gsdmlFilePath)
        {
            return await Task.Run(() =>
            {
                var result = new InterpreterParseResult { 
                    VendorName = "N/A (Interpreter)", 
                    MainFamily = "N/A (Interpreter)",
                    ProductFamily = "N/A (Interpreter)",
                    Category = "N/A (Interpreter)",
                    InfoText = "N/A (Interpreter)",
                    DAPInfoList = new List<DAPInfo>()
                };
                
                if (string.IsNullOrEmpty(gsdmlFilePath) || !File.Exists(gsdmlFilePath))
                {
                    Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] File not found or path is null/empty: {gsdmlFilePath}");
                    return result;
                }

                var gsdInterpreter = GSDMLCacheService.GetCachedInterpreter(gsdmlFilePath, GSDI.ModelOptions.GSDStructure);
                try
                {
                    if (gsdInterpreter != null)
                    {
                        DeviceStructureElement? mainDevice = gsdInterpreter.GetDeviceStructureElement();
                        if (mainDevice != null)
                        {
                            // Properties we know we can access directly
                            result.VendorName = mainDevice.VendorName ?? "N/A";
                            result.MainFamily = mainDevice.MainFamily ?? "N/A";
                            result.ProductFamily = mainDevice.ProductFamily ?? "N/A";
                            result.InfoText = mainDevice.InfoText ?? "N/A";
                            
                            // Use MainFamily as Category (no reflection needed)
                            result.Category = mainDevice.MainFamily ?? "N/A";
                            Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Using MainFamily as Category: {result.Category}");
                            
                            // Get DAP information directly from cached interpreter
                            try
                            {
                                // 直接使用缓存的Interpreter获取DAP信息
                                var deviceAccessPoints = mainDevice.DeviceAccessPoints;
                                if (deviceAccessPoints != null && deviceAccessPoints.Length > 0)
                                {
                                    Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Found {deviceAccessPoints.Length} DAPs in mainDevice");

                                    // 处理所有的DAP
                                    for (int i = 0; i < deviceAccessPoints.Length; i++)
                                    {
                                        var accessPoint = deviceAccessPoints.GetValue(i) as AccessPointStructureElement;
                                        if (accessPoint == null) continue;

                                        var dapInfo = new DAPInfo();

                                        // 直接获取DAP名称和ID（无反射）
                                        dapInfo.Name = !string.IsNullOrEmpty(accessPoint.Name) ? accessPoint.Name : $"DAP {i+1}";
                                        dapInfo.Id = !string.IsNullOrEmpty(accessPoint.GsdID) ? accessPoint.GsdID : "N/A";

                                        Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Direct access - DAP Name: {dapInfo.Name}, ID: {dapInfo.Id}");

                                        // 添加到结果列表
                                        result.DAPInfoList.Add(dapInfo);
                                    }
                                }
                                else
                                {
                                    Debug.WriteLine("[GetDeviceDataFromInterpreterAsync] No DAPs found in mainDevice");
                                }
                            }
                            catch (Exception ex_dap)
                            {
                                Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Error getting DAP info: {ex_dap.Message}");

                                // 至少添加一个默认DAP
                                if (result.DAPInfoList.Count == 0)
                                {
                                    result.DAPInfoList.Add(new DAPInfo {
                                        Name = "N/A (Error)",
                                        Id = "N/A (Error)"
                                    });
                                }
                            }

                            return result;
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Interpreter.AssignGsd failed for: {gsdmlFilePath}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[GetDeviceDataFromInterpreterAsync] Error parsing {gsdmlFilePath} with Interpreter: {ex.Message} | StackTrace: {ex.StackTrace}");
                }
                return result;
            });
        }

        /// <summary>
        /// 加载GSDML目录
        /// </summary>
        private async Task LoadGSDMLCatalogAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在加载GSDML文件...";

                await Task.Run(async () =>
                {
                    Application.Current.Dispatcher.Invoke(() => 
                    {
                        Manufacturers.Clear();
                        MainFamilies.Clear(); // 清除主产品家族列表
                    });

                    var gsdmlDirectory = _gsdmlService.GetGSDMLDirectory();
                    if (!Directory.Exists(gsdmlDirectory))
                    {
                        StatusMessage = $"GSDML目录不存在: {gsdmlDirectory}";
                        return;
                    }

                    // 使用服务的缓存方法而不是直接扫描目录
                    var gsdmlFiles = await _gsdmlService.GetGSDMLFilesAsync();
                    if (gsdmlFiles.Count == 0)
                    {
                        StatusMessage = "未找到GSDML文件";
                        return;
                    }

                    // 确保目录中的所有GSDML文件都已导入到Catalog
                    // 这是修复的关键部分 - 检查文件是否已导入，如果没有，则导入它们
                    int newlyImportedCount = 0;
                    foreach (var gsdmlFile in gsdmlFiles)
                    {
                        string fileName = Path.GetFileName(gsdmlFile);
                        
                        // 如果文件尚未导入，则导入它
                        if (!PNConfigLib.DataModel.Catalog.ImportedGSDMLList.Contains(fileName.ToUpperInvariant()))
                        {
                            try
                            {
                                // 创建一个一致性管理器
                                var consistencyManager = new PNConfigLib.Consistency.ConsistencyManager();
                                
                                // 提取GSDML版本信息
                                string gsdmlVersion = "2.3"; // 默认版本
                                if (fileName.StartsWith("GSDML-V", StringComparison.OrdinalIgnoreCase))
                                {
                                    int hyphenIndex = fileName.IndexOf('-', 7);
                                    if (hyphenIndex > 7)
                                    {
                                        gsdmlVersion = fileName.Substring(7, hyphenIndex - 7);
                                    }
                                }
                                
                                // 验证GSDML文件
                                bool validationResult = consistencyManager.ValidateGSDML(gsdmlVersion, gsdmlFile);
                                if (!validationResult)
                                {
                                    Debug.WriteLine($"GSDML验证失败: {gsdmlFile}");
                                    continue;
                                }
                                
                                // 导入GSDML文件到Catalog
                                bool importResult = PNConfigLib.GSDImport.Converter.ImportGSDML(gsdmlFile, consistencyManager);
                                if (importResult)
                                {
                                    newlyImportedCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"导入GSDML文件时出错: {ex.Message}");
                            }
                        }
                    }

                    // 新的按照 MainFamily > VendorName > ProductFamily 组织的结构
                    Dictionary<
                        string, 
                        Dictionary<
                            string, 
                            Dictionary<
                                string, 
                                List<KeyValuePair<string, DecentralDeviceCatalog>>
                            >
                        >
                    > devicesByMainFamily = new();

                    // 从Catalog获取已导入的设备
                    foreach (var deviceEntry in PNConfigLib.DataModel.Catalog.DeviceList)
                    {
                        var device = deviceEntry.Value;
                        // 直接从设备键中提取文件名，不使用AttributeAccess
                        string deviceFileName = deviceEntry.Key.Contains("\\") ? deviceEntry.Key.Split('\\')[0] : deviceEntry.Key;
                        var deviceFilePath = deviceFileName;

                        // 使用Interpreter获取设备信息
                        if (!string.IsNullOrEmpty(deviceFilePath))
                        {
                            string fullPath = Path.Combine(gsdmlDirectory, deviceFilePath);
                            var interpreterData = await GetDeviceDataFromInterpreterAsync(fullPath);

                            // 获取分类信息，完全使用Interpreter数据
                            string mainFamily = interpreterData.MainFamily ?? "未分类";
                            string vendorName = interpreterData.VendorName ?? "未知厂商";
                            string productFamily = interpreterData.ProductFamily ?? "未分类产品";
                            
                            // 确保字典中有相应的键
                            if (!devicesByMainFamily.ContainsKey(mainFamily))
                            {
                                devicesByMainFamily[mainFamily] = new Dictionary<string, Dictionary<string, List<KeyValuePair<string, DecentralDeviceCatalog>>>>();
                            }
                            
                            if (!devicesByMainFamily[mainFamily].ContainsKey(vendorName))
                            {
                                devicesByMainFamily[mainFamily][vendorName] = new Dictionary<string, List<KeyValuePair<string, DecentralDeviceCatalog>>>();
                            }
                            
                            if (!devicesByMainFamily[mainFamily][vendorName].ContainsKey(productFamily))
                            {
                                devicesByMainFamily[mainFamily][vendorName][productFamily] = new List<KeyValuePair<string, DecentralDeviceCatalog>>();
                            }
                            
                            // 添加设备到列表
                            devicesByMainFamily[mainFamily][vendorName][productFamily].Add(deviceEntry);
                        }
                        else
                        {
                            // 对于没有文件路径的设备，使用默认分类
                            string vendorName = "未知厂商"; // 不再使用AttributeAccess
                            
                            if (!devicesByMainFamily.ContainsKey("未分类"))
                            {
                                devicesByMainFamily["未分类"] = new Dictionary<string, Dictionary<string, List<KeyValuePair<string, DecentralDeviceCatalog>>>>();
                            }
                            
                            if (!devicesByMainFamily["未分类"].ContainsKey(vendorName))
                            {
                                devicesByMainFamily["未分类"][vendorName] = new Dictionary<string, List<KeyValuePair<string, DecentralDeviceCatalog>>>();
                            }
                            
                            if (!devicesByMainFamily["未分类"][vendorName].ContainsKey("未分类产品"))
                            {
                                devicesByMainFamily["未分类"][vendorName]["未分类产品"] = new List<KeyValuePair<string, DecentralDeviceCatalog>>();
                            }
                            
                            devicesByMainFamily["未分类"][vendorName]["未分类产品"].Add(deviceEntry);
                        }
                    }

                    // 构建树形结构
                    var mainFamilies = new ObservableCollection<MainFamilyNode>();
                    
                    // 按MainFamily名称排序
                    var sortedMainFamilies = devicesByMainFamily.Keys.OrderBy(k => k).ToList();
                    
                    foreach (var mainFamilyName in sortedMainFamilies)
                    {
                        var mainFamilyNode = new MainFamilyNode { 
                            Name = mainFamilyName,
                            IsExpanded = true  // 自动展开顶层节点
                        };
                        
                        // 按VendorName排序
                        var sortedVendors = devicesByMainFamily[mainFamilyName].Keys.OrderBy(k => k).ToList();
                        
                        foreach (var vendorName in sortedVendors)
                        {
                            var vendorNode = new VendorFamilyNode { 
                                Name = vendorName,
                                IsExpanded = sortedVendors.Count <= 3  // 当厂商数量较少时自动展开
                            };
                            
                            // 按ProductFamily排序
                            var sortedProductFamilies = devicesByMainFamily[mainFamilyName][vendorName].Keys.OrderBy(k => k).ToList();
                            
                            foreach (var productFamilyName in sortedProductFamilies)
                            {
                                var productFamilyNode = new ProductFamilyNode { Name = productFamilyName };
                                
                                // 添加所有设备
                                foreach (var deviceEntry in devicesByMainFamily[mainFamilyName][vendorName][productFamilyName])
                                {
                                    var deviceKey = deviceEntry.Key;
                                    var deviceCatalog = deviceEntry.Value;
                                    
                                    // 从设备键中提取设备名称，不使用AttributeAccess
                                    string deviceFileName = deviceKey.Contains("\\") ? deviceKey.Split('\\')[0] : deviceKey;
                                    var deviceName = Path.GetFileNameWithoutExtension(deviceFileName);

                                    // 预先计算 DisplayName 以便查找
                                    string tempDisplayName = deviceName;
                                    var tempInterpreterData = new InterpreterParseResult(); // 用于临时获取InfoText
                                    string tempDeviceFilePath = deviceFileName;

                                    if (!string.IsNullOrEmpty(tempDeviceFilePath))
                                    {
                                        tempInterpreterData = await GetDeviceDataFromInterpreterAsync(Path.Combine(gsdmlDirectory, tempDeviceFilePath));
                                        if (!string.IsNullOrEmpty(tempInterpreterData.InfoText) && tempInterpreterData.InfoText != "N/A" && tempInterpreterData.InfoText != "N/A (Interpreter)")
                                        {
                                            tempDisplayName = $"{deviceName} - {tempInterpreterData.InfoText}";
                                        }
                                    }

                                    // 尝试查找已存在的设备节点
                                    var deviceNode = productFamilyNode.Devices.FirstOrDefault(d => d.DisplayName == tempDisplayName);
                                    bool isNewDevice = deviceNode == null;

                                    if (isNewDevice)
                                    {
                                        deviceNode = new GSDMLDeviceNode
                                        {
                                            Name = deviceName,
                                            DeviceId = deviceKey, // 使用第一个遇到的GSD的Key
                                            FilePath = tempDeviceFilePath, // 使用第一个遇到的GSD的FilePath
                                            DisplayName = tempDisplayName
                                        };
                                    }
                                    // 如果是现有设备，我们可能仍需要使用当前deviceEntry的FilePath来加载其特定的模块/DAP，
                                    // 但最终目标是合并显示。这里我们假设所有具有相同DisplayName的设备共享相似的顶级属性，
                                    // 并且第一个被处理的GSD文件足以代表该设备节点。
                                    // 对于模块和DAP，如果需要基于每个单独GSD文件精确加载，则此合并逻辑需要更复杂处理。
                                    // 目前，DAP和模块将基于首次创建此deviceNode时使用的FilePath加载。

                                    // 获取解释器数据 (如果之前未获取或需要刷新)
                                    // 对于新设备，tempInterpreterData 已有数据。对于现有设备，不再重新获取。
                                    var interpreterData = isNewDevice ? tempInterpreterData : await GetDeviceDataFromInterpreterAsync(Path.Combine(gsdmlDirectory, deviceNode.FilePath)); 

                                    if (isNewDevice) // 仅为新设备设置一次这些属性
                                    {
                                        deviceNode.VendorNameFromInterpreter = interpreterData.VendorName;
                                        deviceNode.MainFamilyFromInterpreter = interpreterData.MainFamily;
                                        deviceNode.ProductFamilyFromInterpreter = interpreterData.ProductFamily;
                                        deviceNode.CategoryFromInterpreter = interpreterData.Category;
                                        deviceNode.InfoTextFromInterpreter = interpreterData.InfoText;
                                        deviceNode.DAPNameFromInterpreter = interpreterData.DAPName; // 向后兼容
                                        deviceNode.DAPIdFromInterpreter = interpreterData.DAPId;     // 向后兼容
                                    }
                                    
                                    // 添加所有DAP信息到集合 (即使是现有设备，也可能需要聚合DAP，但当前逻辑会覆盖)
                                    // 为了简化，我们假设DAP信息在具有相同DisplayName的设备之间是一致的，或者首次加载的为准
                                    if (isNewDevice || deviceNode.DAPsFromInterpreter.Count == 0) // 只为新设备或尚未填充DAP的设备加载DAP
                                    {
                                        deviceNode.DAPsFromInterpreter.Clear();
                                        if (interpreterData.DAPInfoList != null && interpreterData.DAPInfoList.Count > 0)
                                        {
                                            var existingDapIds = new HashSet<string>();
                                            foreach (var dapInfo in interpreterData.DAPInfoList)
                                            {
                                                if (dapInfo == null) continue;
                                                
                                                string name = dapInfo?.Name ?? "未命名DAP";
                                                string id = dapInfo?.Id ?? "N/A";
                                                string uniqueKey = $"{name}_{id}";
                                                if (!existingDapIds.Contains(uniqueKey))
                                                {
                                                    deviceNode.DAPsFromInterpreter.Add(new DAPNode { Name = name, Id = id });
                                                    existingDapIds.Add(uniqueKey);
                                                }
                                            }
                                            // 使用安全的方式处理排序和添加
                                            if (deviceNode.DAPsFromInterpreter != null && deviceNode.DAPsFromInterpreter.Any())
                                            {
                                                var sortedDaps = deviceNode.DAPsFromInterpreter
                                                    .Where(d => d != null)
                                                    .OrderBy(d => d.Name)
                                                    .ThenBy(d => d.Id)
                                                    .ToList();
                                                    
                                                deviceNode.DAPsFromInterpreter.Clear();
                                                
                                                foreach (var dap in sortedDaps)
                                                {
                                                    deviceNode.DAPsFromInterpreter.Add(dap);
                                                }
                                            }
                                        }
                                    }
                                    
                                    // 移除模块加载逻辑 - 只显示DAP信息

                                    if (isNewDevice)
                                    {
                                        productFamilyNode.Devices.Add(deviceNode);
                                    }
                                }
                                
                                vendorNode.ProductFamilies.Add(productFamilyNode);
                            }
                            
                            mainFamilyNode.Vendors.Add(vendorNode);
                        }
                        
                        mainFamilies.Add(mainFamilyNode);
                    }
                    
                    // 使用原有的逻辑也生成厂商层次结构 (为了保持向后兼容性)
                    var manufacturers = new ObservableCollection<GSDMLManufacturerNode>();
                    
                    // 按照制造商分组组织设备信息
                    var devicesByManufacturer = new Dictionary<string, List<KeyValuePair<string, DecentralDeviceCatalog>>>();

                    // 从Catalog获取已导入的设备
                    foreach (var deviceEntry in PNConfigLib.DataModel.Catalog.DeviceList)
                    {
                        var device = deviceEntry.Value;
                        string vendorName = "未知厂商"; // 不再使用AttributeAccess，使用默认值
                        
                        if (!devicesByManufacturer.ContainsKey(vendorName))
                        {
                            devicesByManufacturer[vendorName] = new List<KeyValuePair<string, DecentralDeviceCatalog>>();
                        }
                        
                        devicesByManufacturer[vendorName].Add(deviceEntry);
                    }
                    
                    foreach (var manufacturerGroup in devicesByManufacturer)
                    {
                        var manufacturerName = manufacturerGroup.Key;
                        var devices = manufacturerGroup.Value;
                        
                        // 创建制造商节点
                        var manufacturer = new GSDMLManufacturerNode { Name = manufacturerName };
                        
                        foreach (var deviceEntry in devices)
                        {
                            // 原有逻辑 - 略
                        }
                        
                        manufacturers.Add(manufacturer);
                    }
                    
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        // 清空并更新制造商列表
                        Manufacturers.Clear();
                        foreach (var manufacturer in manufacturers)
                        {
                            Manufacturers.Add(manufacturer);
                        }
                        
                        // 清空并更新主产品家族列表
                        MainFamilies.Clear();
                        foreach (var mainFamily in mainFamilies)
                        {
                            MainFamilies.Add(mainFamily);
                        }
                    });

                    // 更新状态消息，包含新导入的文件信息
                    if (newlyImportedCount > 0)
                    {
                        StatusMessage = $"已加载 {PNConfigLib.DataModel.Catalog.ImportedGSDMLList.Count} 个GSDML文件 (新导入 {newlyImportedCount} 个)";
                    }
                    else
                    {
                        StatusMessage = $"已加载 {PNConfigLib.DataModel.Catalog.ImportedGSDMLList.Count} 个GSDML文件";
                    }
                });
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载GSDML目录时出错: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }



        #region IDisposable Implementation

        private bool _disposed = false;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    _refreshSemaphore?.Dispose();
                }

                _disposed = true;
            }
        }

        #endregion

        #endregion
    }

    #region 模型类

    // 添加树形结构的节点类
    public class FamilyTreeNode : ViewModelBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }
        
        private bool _isExpanded;
        public bool IsExpanded
        {
            get => _isExpanded;
            set => SetProperty(ref _isExpanded, value);
        }
        
        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }
    }
    
    /// <summary>
    /// 主产品家族节点，用于组织设备的顶层结构
    /// </summary>
    public class MainFamilyNode : FamilyTreeNode
    {
        public ObservableCollection<VendorFamilyNode> Vendors { get; } = new ObservableCollection<VendorFamilyNode>();
    }
    
    /// <summary>
    /// 厂商节点，用于组织同一厂商的产品家族
    /// </summary>
    public class VendorFamilyNode : FamilyTreeNode
    {
        public ObservableCollection<ProductFamilyNode> ProductFamilies { get; } = new ObservableCollection<ProductFamilyNode>();
    }
    
    /// <summary>
    /// 产品家族节点，用于组织同一产品家族下的设备
    /// </summary>
    public class ProductFamilyNode : FamilyTreeNode
    {
        public ObservableCollection<GSDMLDeviceNode> Devices { get; } = new ObservableCollection<GSDMLDeviceNode>();
    }

    /// <summary>
    /// GSDML设备信息
    /// </summary>
    public class GSDMLDeviceInfo
    {
        public string DeviceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Vendor { get; set; } = string.Empty;
    }



    /// <summary>
    /// GSDML接口信息
    /// </summary>
    public class GSDMLInterfaceInfo
    {
        public string InterfaceId { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    // Node classes for the TreeView structure - Assuming these are defined elsewhere or implicitly.
    // If not, they need to be defined. For this example, I'll add their definitions here
    // based on their usage in LoadGSDMLCatalogAsync.
    // Ensure these classes implement INotifyPropertyChanged if UI needs to update dynamically (e.g., inherit ViewModelBase).

    public class GSDMLInterfaceNode : ViewModelBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        private int _size;
        public int Size
        {
            get => _size;
            set => SetProperty(ref _size, value);
        }
    }





    public class GSDMLDeviceNode : ViewModelBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }
        
        private string _displayName = string.Empty;
        public string DisplayName
        {
            get => _displayName;
            set => SetProperty(ref _displayName, value);
        }

        private string _deviceId = string.Empty;
        public string DeviceId
        {
            get => _deviceId;
            set => SetProperty(ref _deviceId, value);
        }

        private string _filePath = string.Empty;
        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        private string? _vendorNameFromInterpreter;
        public string? VendorNameFromInterpreter
        {
            get => _vendorNameFromInterpreter;
            set => SetProperty(ref _vendorNameFromInterpreter, value);
        }

        private string? _mainFamilyFromInterpreter;
        public string? MainFamilyFromInterpreter
        {
            get => _mainFamilyFromInterpreter;
            set => SetProperty(ref _mainFamilyFromInterpreter, value);
        }

        private string? _productFamilyFromInterpreter;
        public string? ProductFamilyFromInterpreter
        {
            get => _productFamilyFromInterpreter;
            set => SetProperty(ref _productFamilyFromInterpreter, value);
        }

        private string? _categoryFromInterpreter;
        public string? CategoryFromInterpreter
        {
            get => _categoryFromInterpreter;
            set => SetProperty(ref _categoryFromInterpreter, value);
        }

        private string? _infoTextFromInterpreter;
        public string? InfoTextFromInterpreter
        {
            get => _infoTextFromInterpreter;
            set => SetProperty(ref _infoTextFromInterpreter, value);
        }

        // 为保持向后兼容，保留原来的单一DAP属性
        private string? _dapNameFromInterpreter;
        public string? DAPNameFromInterpreter
        {
            get => _dapNameFromInterpreter;
            set => SetProperty(ref _dapNameFromInterpreter, value);
        }

        private string? _dapIdFromInterpreter;
        public string? DAPIdFromInterpreter
        {
            get => _dapIdFromInterpreter;
            set => SetProperty(ref _dapIdFromInterpreter, value);
        }
        
        // 添加DAP集合
        private ObservableCollection<DAPNode> _dapsFromInterpreter = new ObservableCollection<DAPNode>();
        public ObservableCollection<DAPNode> DAPsFromInterpreter
        {
            get => _dapsFromInterpreter;
            set => SetProperty(ref _dapsFromInterpreter, value);
        }
        
        // 只显示DAP信息，不显示模块
        public ObservableCollection<object> AllChildren
        {
            get
            {
                var result = new ObservableCollection<object>();

                // 只添加DAP信息
                foreach (var dap in DAPsFromInterpreter)
                {
                    result.Add(dap);
                }

                return result;
            }
        }
    }

    public class DAPNode : ViewModelBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }
        
        private string _id = string.Empty;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }
        
        private bool _isExpanded;
        public bool IsExpanded
        {
            get => _isExpanded;
            set => SetProperty(ref _isExpanded, value);
        }
        
        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }
        
        // 空的子集合，使其在树中显示为叶节点
        public ObservableCollection<object> Children { get; } = new ObservableCollection<object>();
        
        // 用于UI显示的完整名称 - 修改为不显示ID
        public string DisplayName => Name;
        
        // 仅供内部使用的完整显示名称（包含ID）
        public string FullDisplayName => $"{Name} (ID: {Id})";
        
        public override string ToString() => DisplayName;
    }

    public class GSDMLManufacturerNode : ViewModelBase
    {
        private string _name = string.Empty;
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }
        public ObservableCollection<GSDMLDeviceNode> Devices { get; } = new ObservableCollection<GSDMLDeviceNode>();
    }

    // 添加一个用于分组标题的类
    public class HeaderNode : ViewModelBase
    {
        private string _title = string.Empty;
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }
        
        // 空的子集合，确保在树中显示为叶节点
        public ObservableCollection<object> Children { get; } = new ObservableCollection<object>();
    }

    #endregion
} 