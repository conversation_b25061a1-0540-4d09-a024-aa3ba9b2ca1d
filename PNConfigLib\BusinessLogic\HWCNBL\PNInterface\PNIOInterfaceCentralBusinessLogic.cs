/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIOInterfaceCentralBusinessLogic.cs      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using PNConfigLib.BusinessLogic.HWCNBL;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.PNFrameGeneration;
using PNConfigLib.HWCNBL.Tailor.AddressTailoring.Options;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

using Transformator = PNConfigLib.HWCNBL.ConfigParser.Transformator;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIOInterfaceCentralBusinessLogic : IFDecorator
    {
        /// <summary>
        /// Constant used for converting between SendClock and SendClockFactor.
        /// </summary>
        private const float s_PNTimeBaseNs = 31.25f;

        /// <summary>
        /// The frame generator that generates the frames for PNPlanner.
        /// </summary>
        private FrameGeneration m_FrameGenerator;

        /// <summary>
        /// The default constructor for PNIoInterfaceCentralBusinessLogic, used with decorator pattern.
        /// </summary>
        /// <param name="decoratedInterface">The interface to be decorated.</param>
        public PNIOInterfaceCentralBusinessLogic(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        /// <summary>
        /// The sync domain this Interface is in.
        /// </summary>
        public SyncDomain SyncDomain
        {
            get { return Interface.SyncDomain; }
            set { Interface.SyncDomain = value; }
        }

        /// <summary>
        /// Getter for the frame generator.
        /// </summary>
        private FrameGeneration FrameGenerator
        {
            get
            {
                if (m_FrameGenerator == null)
                {
                    m_FrameGenerator = new FrameGeneration(Interface);
                }
                return m_FrameGenerator;
            }
        }

        /// <summary>
        /// Initialize the Actions and Attributes.
        /// Register event handlers and register attribute handlers.
        /// </summary>
        public override void InitBL()
        {
            InitActions();
            InitAttributes();
        }

        public override void Configure(
            IConfigInterface xmlDeviceInterface,
            SyncDomainType syncDomainType)
        {
            CentralDeviceTypeCentralDeviceInterface xmlCentralDeviceInterface =
                xmlDeviceInterface as CentralDeviceTypeCentralDeviceInterface;
            base.Configure(xmlDeviceInterface, syncDomainType);
            #region Attributes kept in Interface

            FillGeneralAttributes(Interface, xmlCentralDeviceInterface.General, xmlCentralDeviceInterface.InterfaceRefID);

            Interface.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnIoExchangeWithoutMMC,
                Convert.ToUInt32(
                    xmlCentralDeviceInterface.AdvancedOptions.InterfaceOptions
                        .SupportDeviceReplacementWithoutExchangeableMedium,
                    CultureInfo.InvariantCulture));

            if (
                xmlCentralDeviceInterface.AdvancedOptions.InterfaceOptions
                    .PermitOverwritingOfDeviceNamesOfAllAssignedIODevices)
            {
                Interface.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnAllowOverwriteNoIActive,
                    xmlCentralDeviceInterface.AdvancedOptions.InterfaceOptions
                        .PermitOverwritingOfDeviceNamesOfAllAssignedIODevices);
            }

            Interface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnLLDPLegacyEnforce,
                xmlCentralDeviceInterface.AdvancedOptions.InterfaceOptions.UseIECV22LLDPMode);

            Interface.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnIoKeepARAtErrorActivated,
                xmlCentralDeviceInterface.AdvancedOptions.InterfaceOptions.KeepApplicationRelationAtCommunicationError);

            if (xmlCentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization.SynchronizationRole
                != SyncRole.Unsynchronized)
            {
                Interface.AttributeAccess.SetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtHighestSyncClass,
                    (uint)PNRTClass.IrtTop);

                long sendClockFactor = Convert.ToInt64(syncDomainType.SendClock * 32, CultureInfo.InvariantCulture);
                Interface.AttributeAccess.SetAnyAttribute<long>(
                        InternalAttributeNames.PnIoSendClockFactor, sendClockFactor);
                Interface.AttributeAccess.SetAnyAttribute<long>(
                    InternalAttributeNames.PnSendClockFactorTransient,
                      sendClockFactor);
            }
            else
            {
                long sendClockFactor = 0;
                if (xmlCentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClockSpecified)
                {
                    float sendClock = xmlCentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCommunication.SendClock;
                    sendClockFactor = Convert.ToInt64(sendClock * 32, CultureInfo.InvariantCulture);
                }
                else if(syncDomainType.SendClockSpecified)
                {
                    sendClockFactor = Convert.ToInt64(syncDomainType.SendClock * 32, CultureInfo.InvariantCulture);
                }
                Interface.AttributeAccess.SetAnyAttribute<long>(
                    InternalAttributeNames.PnIoSendClockFactor,
                        sendClockFactor);
                Interface.AttributeAccess.SetAnyAttribute<long>(
                    InternalAttributeNames.PnSendClockFactorTransient,
                        sendClockFactor);
            }

            #endregion

            #region Attributes kept in IOConnector
            SyncRole syncRole = xmlCentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization.SynchronizationRole;
            PNIRTSyncRole mappedSyncRole = AttributeUtilities.MapSyncRoleEnum(syncRole);
            if (mappedSyncRole == PNIRTSyncRole.PrimarySyncMaster)
            {
                Interface.SyncDomain.SyncDomainBusinessLogic.AssignAsSyncMaster(Interface, PNInterfaceType.IOController);
            }
            else
            {
                Interface.PNIOC.AttributeAccess.SetAnyAttribute<byte>(InternalAttributeNames.PnIrtSyncRole, (byte)mappedSyncRole);
            }

            Interface.PNIOC.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PnStationNumber, 0);

            #endregion

            Interface.Node.NodeBusinessLogic.Configure(xmlCentralDeviceInterface);

            #region Mrp 
            // Although PN Driver does not support MRP PN feature, it will be assigned to a MRP domain. 
            //Its MRP role will be set to �Not device in the Ring� and this value cannot be changed.
            List<MrpRingType> xmlMrpRings = xmlCentralDeviceInterface.AdvancedOptions.MediaRedundancy;
            if ((xmlMrpRings != null)
                && (xmlMrpRings.Count > 0))
            {
                MrpRingType mrpRing = xmlMrpRings.FirstOrDefault(); // PNDriver's instance count = 1
                Interface.MrpDomainInstances.Add(
                    new MrpDomainInstance(mrpRing?.MrpDomainRefID,
                            mrpRing?.MrpDomainRefID,
                            PNMrpRole.NotInRing,
                            (List<DataModel.PCLObjects.Port>)Interface.GetPorts()));
            }

            #endregion
        }

        private byte[] CreateIosysParamConfig()
        {
            byte[] result = new byte[20];
            IIosysParamConfigHelper helper = new IosysParamConfigHelper(Interface.AttributeAccess);
            Transformator.WriteHeader(result, 0x3040, 16, helper.GetBlockVersionHigh, helper.GetBlockVersionLow);
            AttributeAccessCode ac = new AttributeAccessCode();
            if ((Interface.ParentObject != null)
                && Interface.ParentObject.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnCustomizationEnabled,
                    ac,
                    false))
            {
                int pnVendorIdCustomized =
                    Interface.ParentObject.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnVendorIdCustomized,
                        ac.GetNew(),
                        0);
                Transformator.Write16(result, 8, (ushort)pnVendorIdCustomized);
                int pnDeviceIdCustomized =
                    Interface.ParentObject.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnDeviceIdCustomized,
                        ac.GetNew(),
                        0);
                Transformator.Write16(result, 10, (ushort)pnDeviceIdCustomized);
            }
            else
            {
                //VendorID
                Transformator.Write16(result, 8, 0x2a);
                // DeviceID
                int pnDeviceId = Interface.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnDeviceId,
                    null,
                    0);
                Transformator.Write16(result, 10, (ushort)pnDeviceId);
            }

                // InstanceID (byte 12-13)
            ushort instanceId = PNInstanceIdUtilities.GetInstanceIdForDecentralIoSystem(Interface);
            Transformator.Write16(result, 12, instanceId);
            // Constant value
            Transformator.Write16(result, 14, 600);
            // Constant value
            Transformator.Write16(result, 16, 300);
            //HighNetload value
            if (helper.GetBlockVersionHigh >= 1 && helper.GetBlockVersionLow > 0)
            {
                // Properties at the end of the block
                Transformator.Write16(result, 18, helper.GetProperties);
            }
            return result;
        }
       
        /// <summary>
        /// Consistency check.
        /// </summary>
        private void GenericMethodCheckConsistency()
        {
            string nameOfController = AttributeUtilities.GetName(Interface.ParentObject);
            // 1. Check if we have a multi deployable system, and consistent if so

            AddressTailorOptionsCentral addressTailoringUtility = new AddressTailorOptionsCentral(null, Interface);
            if (addressTailoringUtility.AddressTailoringEnabled)
            {
                addressTailoringUtility.IoSystemController.CheckConsistency();
            }

            // 2. Check if IOController has at least one common RtStartupMode with devices.
            Interface deviceInterface = Utility.ControllerHasCommonIoStartupMode(Interface);
            if (deviceInterface != null)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.NoCommonStartupFound,
                    nameOfController,
                    AttributeUtilities.GetName(deviceInterface.ParentObject));
            }

            // 3. Check Maximum number of supported Fast Startup enabled devices.
            if (FastStartupUtility.FastStartupMaxCountExceeded(Interface) > 0)
            {
                uint maxFastStartupSupported =
                    Interface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoFastStartupMaxCount,
                        new AttributeAccessCode(),
                        0);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.FastStartupMaxCountExceeded,
                    nameOfController,
                    maxFastStartupSupported);
            }

            // 4. Check the validity of sendclock for iDevices and PNDriver.
            PNIRTSyncRole syncRole =
                (PNIRTSyncRole)
                Interface.PNIOC.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtSyncRole,
                    new AttributeAccessCode(),
                    (byte)PNIRTSyncRole.NotSynchronized);

            long transientSendClockFactor = AttributeUtilities.GetTransientPNSendClockFactor(Interface, 32);
            bool isControllerSendclockUneven = false;

            bool pnPdevParametrizationDecentral =
                Convert.ToBoolean(
                    Interface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnPdevParametrizationDecentral,
                        new AttributeAccessCode(),
                        0),
                    CultureInfo.InvariantCulture);

            if (transientSendClockFactor != -1)
            {
                isControllerSendclockUneven = Utility.IsUnevenSendclock(Interface, transientSendClockFactor);
            }

            if (isControllerSendclockUneven
                && !pnPdevParametrizationDecentral
                && (syncRole == PNIRTSyncRole.NotSynchronized))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.IDeviceSendclockNotValid);
            }

            if ((transientSendClockFactor == -1)
                && (syncRole == PNIRTSyncRole.NotSynchronized))
            {
                long sendclockFactor =
                    Interface.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoSendClockFactor,
                        new AttributeAccessCode(),
                        0);

                float updateTime = sendclockFactor * (s_PNTimeBaseNs / 1000);
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.SendclockNotValid,
                    updateTime,
                    nameOfController);
            }


            CheckConsistencyUtility.ConsistencyCheck_CentralDataLengthSubmoduleLength(Interface);
            //Check the cyclic bandwidth 
            CheckConsistencyUtility.ConsistencyCheck_CyclicBandwidth(
                Interface,
                SyncDomain.SyncDomainBusinessLogic,
                SyncDomain.SyncDomainBusinessLogic.PNPlannerResults.CalculatedPartRt,
                SyncDomain.SyncDomainBusinessLogic.PNPlannerResults.CalculatedPartIrt);

            ConsistencyCheckPrioritizedStartup(Interface);

            IsPnIoKeepARAtErrorConfigurable();
        }

        private void IsPnIoKeepARAtErrorConfigurable()
        {
            bool pnIoKeepARAtErrorActivated = Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoKeepARAtErrorActivated, new AttributeAccessCode(), false);

            bool pnIoKeepARAtError = Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoKeepARAtError, new AttributeAccessCode(), false);
            if (pnIoKeepARAtErrorActivated && !pnIoKeepARAtError)
            {
                ConsistencyLogger.Log(
                   ConsistencyType.PN,
                   LogSeverity.Error,
                   Interface,
                   ConsistencyConstants.KeepApplicationRelationAtCommunicationErrorNotConfigurable,
                   Interface.ParentObject.Id,
                   Interface.Id);
            }
        }

        private void GenericMethodGetIosysParamConfig(IMethodData methodData)
        {
            methodData.ReturnValue = CreateIosysParamConfig();
        }

        /// <summary>
        /// This method gets the state of the IO system.
        /// </summary>
        /// <param name="methodData">The method data.</param>
        /// <remarks>
        /// If the IO controller have devices connected to it,  i.e. the IO system has both
        /// an IOC and IODs, then the return value of the method data is 1, otherwise it is 0.
        /// </remarks>
        private void GenericMethodGetIoSysUsage(IMethodData methodData)
        {
            int mode = 0;

            if (Interface == null)
            {
                throw new ArgumentException("Interface submodule is null.");
            }

            //Check if IO-Controller
            PNIOC ioController = Interface.PNIOC;
            List<PNIOD> remIoDevicesOfIoController = NavigationUtilities.GetIoDevicesOfIoController(ioController);
            if (remIoDevicesOfIoController.Count > 0)
            {
                mode |= 1;
            }

            methodData.ReturnValue = mode;
        }

        /// <summary>
        /// This method fills the PDSyncDataStruct with the attributes of the Sync-Domain
        /// and the synchronization parameters of the Interface Submodule.
        /// </summary>
        /// <param name="methodData">The method data.</param>
        /// <remarks>
        /// If the record includes header; return value of the method data will be true or false
        /// depending on the success of the operation, and PdSyncDataStructEntry argument will contain
        /// the created Config block as a byte array.
        /// If the record is without header; return value of the method data will contain the Config block as a byte array.
        /// </remarks>
        private void GenericMethodGetPdSyncDataStruct(IMethodData methodData)
        {
            SyncDomain syncDomain = SyncDomain;
            //init ReturnValue
            methodData.ReturnValue = false;
            bool isRecordwithoutHeader = false;
            for (int i = 0; i < methodData.Arguments.Count; i++)
            {
                if (methodData.Arguments.AllKeys[i] == RecordwithoutHeader.isRecordwithoutHeader)
                {
                    isRecordwithoutHeader = true;
                }
            }

            if (syncDomain != null)
            {
                PNInterfaceType interfaceType = PNAttributeUtility.IsPNIoControllerOperatingModeActive(Interface)
                                                    ? PNInterfaceType.IOController
                                                    : PNInterfaceType.IODevice;
                if (ConfigUtility.IsRTSync(Interface, interfaceType))
                {
                    if (CompileUtility.FillPdSyncDataStruct(methodData, Interface, syncDomain, interfaceType))
                    {
                        // Adding the DS header.
                        byte[] syncData = methodData.Arguments[GetPdSyncDataParameters.PdSyncDataStructEntry] as byte[];
                        if (syncData != null)
                        {
                            if (isRecordwithoutHeader || (interfaceType == PNInterfaceType.IODevice))
                            {
                                // assign the output parameter of the method
                                methodData.ReturnValue = syncData;
                                return;
                            }
                            ParameterDatasetStruct pdSyncData = new ParameterDatasetStruct();
                            pdSyncData.ParaDSNumber = 0x802D;
                            pdSyncData.AddParaBlock(syncData);

                            methodData.Arguments[GetPdSyncDataParameters.PdSyncDataStructEntry] = pdSyncData.ToByteArray;

                            methodData.ReturnValue = true;
                        }
                    }
                    else
                    {
                        throw new PNFunctionsException(
                            "PdSyncData block (Config2002 0x802D) could not be generated at interfaceSubmodule: "
                            + AttributeUtilities.GetName(Interface));
                    }
                }
            }
        }

        /// <summary>
        /// Creates the Profinet IO related parts for PDEV 1A block of Config 2002.
        /// Supported datasets: 0x802C, 0x8070.
        /// </summary>
        /// <param name="methodData">The method data.</param>
        /// <remarks>
        /// The return value of the method data will be true or false depending on the success of the operation.
        /// The generated block will be in ConfigBlock argument of the method data as a byte array.
        /// </remarks>
        private void GenericMethodGetConfig2002Pdev1ABlock(IMethodData methodData)
        {
            if (methodData == null)
            {
                return;
            }

            int dataSet = Convert.ToInt32(methodData.Arguments[GetConfig2002Pdev1ABlock.SelectedDataset], CultureInfo.InvariantCulture);
            bool isRecordwithoutHeader = false;
            for (int i = 0; i < methodData.Arguments.Count; i++)
            {
                if (methodData.Arguments.AllKeys[i] == RecordwithoutHeader.isRecordwithoutHeader)
                {
                    isRecordwithoutHeader = true;
                }
            }

            byte[] prmBlock = null;
            bool returnValue = false;
            try
            {
                switch (dataSet)
                {
                    case 0x802C:
                        prmBlock = ConfigUtility.GetConfig2002Pdev1ABlockDs802C(Interface, out returnValue);
                        methodData.ReturnValue = returnValue;
                        break;
                    case 0x8070:
                        prmBlock = ConfigUtility.GetConfig2002Pdev1ABlockDs8070(Interface, out returnValue);
                        methodData.ReturnValue = returnValue;
                        break;
                }
            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetConfig2002Pdev1ABlock", e);
            }

            if (returnValue)
            {
                if (isRecordwithoutHeader)
                {
                    // assign the output parameter of the method
                    methodData.ReturnValue = prmBlock;
                    return;
                }

                ParameterDatasetStruct block = new ParameterDatasetStruct();
                block.ParaDSNumber = dataSet;
                block.ParaDSIdentifier = 0;
                // add parameter block into DS Struct
                block.AddParaBlock(prmBlock);
                // assign the output parameter of the method
                methodData.Arguments[GetConfig2002Pdev1ABlock.ConfigBlock] = block.ToByteArray;
            }
            else
            {
                if (isRecordwithoutHeader)
                {
                    methodData.ReturnValue = null;
                }
            }
        }

        /// <summary>
        /// This function creates the resource partition block of Config2002.
        /// </summary>
        /// <param name="methodData">The method data.</param>
        /// <remarks>
        /// The return value of the method data will be true or false depending on the success of the operation.
        /// The generated block will be in ConfigBlock argument of the method data as a byte array.
        /// </remarks>
        private void GenericMethodGetConfig2002ResourcePartitioningBlock(IMethodData methodData)
        {
            if (!IsResourcePartitioningBlockRequired())
            {
                methodData.ReturnValue = false;
                return;
            }

            DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(Interface);

            AttributeAccessCode ac = new AttributeAccessCode();

            // get ControllerInterfaceSubmodule's PNSendClockFactorTransient
            int pnIoSendClockFactor = (int)AttributeUtilities.GetTransientPNSendClockFactor(Interface, 0);

            // check sync role and send clock factor in case there's no io subsystem
            if (ioSystem == null)
            {
                if (pnIoSendClockFactor == 0)
                {
                    // don't create the subblock in case the send clock factor equals 0
                    methodData.ReturnValue = false;
                    return;
                }

                // get ControllerInterfaceSubmodule's PNIrtSyncRole
                ac.Reset();
                PNIOC ioController = Interface.PNIOC;
                if (ioController == null)
                {
                    methodData.ReturnValue = false;
                    return;
                }
            }

            // get application phases
            int[] ioApplicationPhases, cbaApplicationPhases;
            ConfigUtility.GetApplicationPhases(PNIORatio.OneToZero, out ioApplicationPhases, out cbaApplicationPhases);

            ResourcePartitioningStruct resourcePartitioningStruct = new ResourcePartitioningStruct();
            resourcePartitioningStruct.SetParameterBlockId(0x1042);
            resourcePartitioningStruct.SetBlockLength(28);
            if (pnIoSendClockFactor % 4 == 0) // Send clock is integer
            {
                resourcePartitioningStruct.SendClockFormat = 0;
                resourcePartitioningStruct.SendClock = (int)(pnIoSendClockFactor * s_PNTimeBaseNs);
            }
            else
            {
                // Send clock is not an integer, change the sendclockformat and 
                // use sendclockfactor instead.
                resourcePartitioningStruct.SendClockFormat = 1;
                resourcePartitioningStruct.SendClock = pnIoSendClockFactor;
            }
            resourcePartitioningStruct.SetNumberOfApplications(2);
            resourcePartitioningStruct.SetIOApplicationId(2);
            resourcePartitioningStruct.IOApplicationPhases = ioApplicationPhases;
            resourcePartitioningStruct.SetCbaApplicationId(1);
            resourcePartitioningStruct.SetCbaApplicationPhases(cbaApplicationPhases);

            methodData.Arguments[GetConfig2002ResourcePartitioningBlock.ConfigBlock] = resourcePartitioningStruct.ToByteArray;
            methodData.ReturnValue = true;
        }

        /// <summary>
        /// Creates the Profinet IO SendClock Data Record.
        /// Supported datasets: 0x10000
        /// </summary>
        /// <param name="methodData">The method data.</param>
        /// <remarks>
        /// The return value of the method data will contain the generated block as a byte array.
        /// </remarks>
        private void GenericMethodGetSendClockParameterBlocks(IMethodData methodData)
        {
            try
            {
                long sendClockFactor;
                SyncDomain syncDomain = Interface.SyncDomain;
                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(Interface.GetIOConnector());

                if ((syncDomain != null)
                    && (syncRole != PNIRTSyncRole.NotSynchronized))
                {
                    SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
                    if (syncDomainBl != null)
                    {
                        sendClockFactor = syncDomainBl.SendClockFactor;
                    }
                    else
                    {
                        sendClockFactor =
                            Interface.AttributeAccess.GetAnyAttribute<long>(
                                InternalAttributeNames.PnIoSendClockFactor,
                                new AttributeAccessCode(),
                                32);
                    }
                }
                else
                {
                    sendClockFactor =
                        Interface.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            new AttributeAccessCode(),
                            32);
                }
                SendClock sendClock = new SendClock();
                sendClock.SendClockFactor = Convert.ToInt32(sendClockFactor, CultureInfo.InvariantCulture);

                // If Pdev parametrization is decentral (= 1) (and therefore IDevice) and there is no assigned IO Device or MC multicast to it
                // (therefore only the two IDevice FrameData exist or nothing), SendClockProperties is 1, else 3                
                IList<IPNFrameData> pnFrameDataList = SyncDomain.SyncDomainBusinessLogic.GetFramesOfInterface(Interface);
                int notIDeviceFrameCount = pnFrameDataList == null
                                               ? 0
                                               : pnFrameDataList.Count(
                                                   t => t.FrameType != (int)PNPlannerFrameType.IDeviceFrame);
                uint pnPdevParametrizationDecentral =
                    Interface.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnPdevParametrizationDecentral,
                        new AttributeAccessCode(),
                        0u);
                sendClock.SendClockProperties = (pnPdevParametrizationDecentral == 1) && (notIDeviceFrameCount == 0)
                                                    ? 0x0001
                                                    : 0x0003;

                sendClock.ExtraDataLength = 0;
                sendClock.ParaBlockLength = 8;
                // assign the output parameter of the method
                methodData.ReturnValue = sendClock.ToByteArray;
            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetSendClockParameterBlocks", e);
            }
        }

        /// <summary>
        /// This method makes prepares PNPlanner run by generating frames.
        /// </summary>
        /// <param name="methodData">The method data.</param>
        private void GenericMethodPrepareForPNPlanner(IMethodData methodData)
        {
            bool isCompile = (bool)methodData.Arguments[PNConstants.IsCompile];

            FrameGenerator.MethodPrepareForPNPlannerScf(isCompile);

            methodData.Arguments[PrepareForPNPlanner.FrameLengthColl] = FrameGenerator.FrameLengthColl;
            methodData.Arguments[PrepareForPNPlanner.PNFrameDataList] = FrameGenerator.PNFrameDataList;
        }

        /// <summary>
        /// Registers the generic methods with their corresponding callbacks.
        /// </summary>
        private void InitActions()
        {
            PclObject.Actions actions = Interface.BaseActions;

            actions.RegisterMethod(PrepareForPNPlanner.Name, GenericMethodPrepareForPNPlanner);
            actions.RegisterMethod(
                GetConfig2002ResourcePartitioningBlock.Name,
                GenericMethodGetConfig2002ResourcePartitioningBlock);
            actions.RegisterMethod(GetIOsysUsage.Name, GenericMethodGetIoSysUsage);
            actions.RegisterMethod(GetPdSyncDataParameters.Name, GenericMethodGetPdSyncDataStruct);
            actions.RegisterMethod(GetConfig2002Pdev1ABlock.Name, GenericMethodGetConfig2002Pdev1ABlock);
            actions.RegisterMethod(GetSendClockParameterBlocks.Name, GenericMethodGetSendClockParameterBlocks);
            actions.RegisterMethod(GetIosysParamConfig.Name, GenericMethodGetIosysParamConfig);

            //Consistency Check
            ConsistencyManager.RegisterConsistencyCheck(Interface, GenericMethodCheckConsistency);
        }

        private void InitAttributes()
        {
            Interface.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnIoOperatingMode,
                (uint)PNIOOperatingModes.IOController);
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIoExchangeWithoutMMC, 1);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnAllowOverwriteNoIActive, false);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnLLDPLegacyEnforce, false);
            Interface.AttributeAccess.AddAnyAttribute<uint>(
                InternalAttributeNames.PnIrtHighestSyncClass,
                (uint)PNRTClass.Rt);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnIoSendClockFactor, 1024);
            Interface.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnSendClockFactorTransient, 1024);
            Interface.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIoKeepARAtErrorActivated, false);
            bool pnIoKeepARAtErrorSupported = Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoKeepARAtError,
                new AttributeAccessCode(),
                false);
            if (pnIoKeepARAtErrorSupported)
            {
                Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIoControllerPropertiesBlockVersion, 257);
            }          
        }

        /// <summary>
        /// Checks if the resource partitioning block needs to be generated.
        /// </summary>
        /// <remarks>
        /// </remarks>
        /// <returns>True if block needs to be generated; false otherwise.</returns>
        private bool IsResourcePartitioningBlockRequired()
        {
            DataModel.PCLObjects.IOSystem ioSystem = NavigationUtilities.GetIoSystem(Interface);

            if (ioSystem != null)
            {
                // Project with Profinet IO-Controller and projected IO-System
                return true;
            }
            if (AttributeUtilities.IsIDevice(Interface))
            {
                return true;
            }

            PNIOC ioController = Interface.PNIOC;
            if (ioController != null)
            {
                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioController);
                if (syncRole != PNIRTSyncRole.NotSynchronized)
                {
                    return true;
                }
            }
            return false;
        }

        #region PNDriver Specific Consistencies

        /// <summary>
        /// Gets all prioritized startup supported devices in the IOSystem and
        /// checks if any of them is topologically connected or connectable to the PNDriver.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        private void ConsistencyCheckPrioritizedStartup(Interface controllerInterfaceSubmodule)
        {
            PNSuppControllerStartupMode controllerStartupMode =
                (PNSuppControllerStartupMode)
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoSuppStartupModes,
                    new AttributeAccessCode(),
                    0);

            bool isCheckPrioritizedStartupActivated =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoPrioritizedStartupConsistencyCheckRequired,
                    new AttributeAccessCode(),
                    false);

            if (!isCheckPrioritizedStartupActivated
                || (controllerStartupMode == PNSuppControllerStartupMode.NSU))
            {
                return;
            }

            List<Interface> devicesOfController =
                NavigationUtilities.GetDevicesOfController(controllerInterfaceSubmodule);

            foreach (Interface device in devicesOfController)
            {
                bool isFsuActivated =
                    device.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoDeviceFSUPriority,
                        new AttributeAccessCode(),
                        false);
                if (!isFsuActivated)
                {
                    continue;
                }
                if (CheckInterfacesTopologicalConnectable(controllerInterfaceSubmodule, device))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Warning,
                        Interface,
                        ConsistencyConstants.PNDriverPrioStartupNotAllowedWarning,
                        controllerInterfaceSubmodule.Id);
                    return;
                }

                if (!CheckInterfacesTopologicalConnected(controllerInterfaceSubmodule, device))
                {
                    continue;
                }
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Interface,
                    ConsistencyConstants.PNDriverPrioStartupNotAllowedError);
                return;
            }
        }

        private static bool CheckInterfacesTopologicalConnected(
            Interface controllerInterface,
            Interface deviceInterface)
        {
            List<DataModel.PCLObjects.Port> portsOfController = NavigationUtilities.GetPortModules(controllerInterface);
            List<DataModel.PCLObjects.Port> portsOfDevice = NavigationUtilities.GetPortModules(deviceInterface);

            if ((portsOfController == null)
                || (portsOfDevice == null))
            {
                return false;
            }

            foreach (DataModel.PCLObjects.Port port in portsOfController)
            {
                List<DataModel.PCLObjects.Port> partnerPorts = NavigationUtilities.GetPartnerPorts(port);

                foreach (DataModel.PCLObjects.Port partner in partnerPorts)
                {
                    Interface partnerInterface = NavigationUtilities.GetInterfaceOfPort(partner);
                    if (partnerInterface == deviceInterface)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private static bool CheckInterfacesTopologicalConnectable(
            Interface controllerInterface,
            Interface deviceInterface)
        {
            List<DataModel.PCLObjects.Port> portsOfController = NavigationUtilities.GetPortModules(controllerInterface);
            List<DataModel.PCLObjects.Port> portsOfDevice = NavigationUtilities.GetPortModules(deviceInterface);

            bool hasControllerUnconnectedPort = false;
            bool hasDeviceUnconnectedPort = false;

            foreach (DataModel.PCLObjects.Port port in portsOfController)
            {
                List<DataModel.PCLObjects.Port> partnerPorts = NavigationUtilities.GetPartnerPorts(port);
                if (partnerPorts.Count != 0)
                {
                    continue;
                }
                hasControllerUnconnectedPort = true;
                break;
            }

            if (!hasControllerUnconnectedPort)
            {
                return false;
            }

            foreach (DataModel.PCLObjects.Port port in portsOfDevice)
            {
                List<DataModel.PCLObjects.Port> partnerPorts = NavigationUtilities.GetPartnerPorts(port);
                if (partnerPorts.Count != 0)
                {
                    continue;
                }
                hasDeviceUnconnectedPort = true;
                break;
            }

            return hasDeviceUnconnectedPort;
        }

        #endregion
    }
}