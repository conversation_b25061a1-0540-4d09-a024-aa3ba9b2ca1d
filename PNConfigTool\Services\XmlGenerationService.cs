using System;
using System.IO;
using System.Xml.Serialization;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using PNConfigTool.Models;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel;
using PNConfigLib.Importer.GSDImport.Helper;

namespace PNConfigTool.Services
{
    /// <summary>
    /// XML生成服务，负责将项目配置转换为XML文件
    /// </summary>
    public class XmlGenerationService
    {
        /// <summary>
        /// 生成XML文件的结果
        /// </summary>
        public class GenerationResult
        {
            public bool Success { get; set; }
            public string Message { get; set; } = string.Empty;
            public string ListOfNodesPath { get; set; } = string.Empty;
            public string ConfigurationPath { get; set; } = string.Empty;
        }

        /// <summary>
        /// 根据项目配置生成ListOfNodes.xml和Configuration.xml文件
        /// </summary>
        /// <param name="projectConfig">项目配置</param>
        /// <returns>生成结果</returns>
        public GenerationResult GenerateXmlFiles(ProjectConfig projectConfig)
        {
            var result = new GenerationResult();

            try
            {
                // 验证项目配置
                if (projectConfig == null)
                {
                    result.Message = "项目配置为空";
                    return result;
                }

                // 确保输出目录存在
                if (!Directory.Exists(projectConfig.OutputConfiguration.OutputDirectory))
                {
                    Directory.CreateDirectory(projectConfig.OutputConfiguration.OutputDirectory);
                }

                // 生成ListOfNodes.xml
                var listOfNodesResult = GenerateListOfNodes(projectConfig);
                if (!listOfNodesResult.Success)
                {
                    result.Message = $"生成ListOfNodes.xml失败: {listOfNodesResult.Message}";
                    return result;
                }

                // 生成Configuration.xml
                var configurationResult = GenerateConfiguration(projectConfig);
                if (!configurationResult.Success)
                {
                    result.Message = $"生成Configuration.xml失败: {configurationResult.Message}";
                    return result;
                }

                result.Success = true;
                result.Message = "XML文件生成成功";
                result.ListOfNodesPath = listOfNodesResult.ListOfNodesPath;
                result.ConfigurationPath = configurationResult.ConfigurationPath;

                // 更新项目配置中的文件路径（转换为相对路径）
                var projectManager = PNConfigTool.Models.ProjectManager.Instance;
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(projectManager.CurrentProjectFilePath ?? "");

                projectConfig.OutputConfiguration.ListOfNodesXmlPath = PNConfigTool.Utilities.PathHelper.ToRelativePath(result.ListOfNodesPath, projectDirectory);
                projectConfig.OutputConfiguration.ConfigurationXmlPath = PNConfigTool.Utilities.PathHelper.ToRelativePath(result.ConfigurationPath, projectDirectory);
                projectConfig.OutputConfiguration.GenerationStatus = "已生成";

                return result;
            }
            catch (Exception ex)
            {
                result.Message = $"生成XML文件时发生异常: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 生成ListOfNodes.xml文件
        /// </summary>
        /// <param name="projectConfig">项目配置</param>
        /// <returns>生成结果</returns>
        private GenerationResult GenerateListOfNodes(ProjectConfig projectConfig)
        {
            var result = new GenerationResult();

            try
            {
                // 创建ListOfNodes对象
                var listOfNodes = new ListOfNodes
                {
                    ListOfNodesID = projectConfig.ListOfNodesConfiguration.ListOfNodesID,
                    schemaVersion = "1.0"
                };

                // 添加PNDriver（中央设备/主站控制器）
                // 使用新的配置结构
                var pnDriver = new PNDriverType
                {
                    DeviceID = projectConfig.ListOfNodesConfiguration.PNDriver.DeviceID,
                    DeviceName = projectConfig.ListOfNodesConfiguration.PNDriver.DeviceName,
                    DeviceVersion = projectConfig.ListOfNodesConfiguration.PNDriver.DeviceVersion,
                    Interface = new PNDriverInterfaceType
                    {
                        InterfaceID = projectConfig.ListOfNodesConfiguration.PNDriver.Interface.InterfaceID,
                        InterfaceName = projectConfig.ListOfNodesConfiguration.PNDriver.Interface.InterfaceName,
                        InterfaceType = GetPNDriverInterfaceType(projectConfig.ListOfNodesConfiguration.PNDriver.Interface.InterfaceType)
                    }
                };

                // 如果是自定义接口类型，设置自定义路径
                if (pnDriver.Interface.InterfaceType == PNDriverInterfaceEnum.Custom)
                {
                    // 计算从output目录到GSDML文件的相对路径
                    string customInterfacePath = projectConfig.ProjectSpecificExtensions.MasterCustomInterfacePath;
                    if (!string.IsNullOrEmpty(customInterfacePath) && !Path.IsPathRooted(customInterfacePath))
                    {
                        // 如果是相对路径，需要从output目录的角度重新计算
                        customInterfacePath = "../" + customInterfacePath;
                    }

                    pnDriver.Interface.CustomInterfacePath = customInterfacePath;
                }

                listOfNodes.PNDriver.Add(pnDriver);

                // 添加分布式设备
                // 使用新的配置结构
                foreach (var device in projectConfig.ListOfNodesConfiguration.DecentralDevices)
                {
                    // 计算从output目录到GSDML文件的相对路径
                    // 因为ListOfNodes.xml在output/目录中，而GSDML文件在GSDMLs/目录中
                    // 所以需要使用 "../GSDMLs/filename.xml" 格式
                    string gsdPathForXml = device.GSDPath;
                    if (!string.IsNullOrEmpty(gsdPathForXml) && !Path.IsPathRooted(gsdPathForXml))
                    {
                        // 如果是相对路径，需要从output目录的角度重新计算
                        gsdPathForXml = "../" + gsdPathForXml;
                    }

                    var decentralDevice = new PNConfigLib.ConfigReader.ListOfNodes.DecentralDeviceType
                    {
                        DeviceID = device.DeviceID,
                        DeviceName = device.DeviceName,
                        GSDPath = gsdPathForXml, // 使用从output目录的相对路径
                        GSDRefID = device.GSDRefID,
                        Interface = new PNConfigLib.ConfigReader.ListOfNodes.DecentralDeviceInterfaceType
                        {
                            InterfaceID = device.Interface.InterfaceID,
                            InterfaceName = device.Interface.InterfaceName
                        }
                    };

                    listOfNodes.DecentralDevice.Add(decentralDevice);
                }

                // 序列化到XML文件
                string filePath = Path.Combine(projectConfig.OutputConfiguration.OutputDirectory, "ListOfNodes.xml");
                SerializeToXml(listOfNodes, filePath);

                result.Success = true;
                result.ListOfNodesPath = filePath;
                result.Message = "ListOfNodes.xml生成成功";

                return result;
            }
            catch (Exception ex)
            {
                result.Message = $"生成ListOfNodes.xml时发生异常: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 生成Configuration.xml文件
        /// </summary>
        /// <param name="projectConfig">项目配置</param>
        /// <returns>生成结果</returns>
        private GenerationResult GenerateConfiguration(ProjectConfig projectConfig)
        {
            var result = new GenerationResult();

            try
            {
                // 创建Configuration对象
                var configuration = new PNConfigLib.ConfigReader.Configuration.Configuration
                {
                    ConfigurationID = projectConfig.ConfigurationSettings.ConfigurationID,
                    ConfigurationName = projectConfig.ConfigurationSettings.ConfigurationName,
                    ConfigurationDescription = string.Empty,
                    ListOfNodesRefID = projectConfig.ConfigurationSettings.ListOfNodesRefID,
                    schemaVersion = "1.0"
                };

                // 添加中央设备（使用新的配置结构）
                var centralDevice = new CentralDeviceType
                {
                    DeviceRefID = projectConfig.ConfigurationSettings.CentralDevice.DeviceRefID
                };

                // 配置中央设备接口
                centralDevice.CentralDeviceInterface = new CentralDeviceTypeCentralDeviceInterface
                {
                    InterfaceRefID = projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.InterfaceRefID
                };

                // 配置中央设备的以太网地址（如果有的话）
                if (projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses != null)
                {
                    centralDevice.CentralDeviceInterface.EthernetAddresses = new PNConfigLib.ConfigReader.Configuration.CentralDeviceEthernetAddressesType
                    {
                        // 添加IOSystemRefID和SubnetRefID
                        IOSystemRefID = "PROFINETIO-System_100",
                        SubnetRefID = "PNIE_1"
                    };

                    // 配置IP协议
                    if (projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol?.SetInTheProject != null)
                    {
                        var ipConfig = projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject;
                        centralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol = new PNConfigLib.ConfigReader.Configuration.CentralIPProtocolType
                        {
                            Item = new PNConfigLib.ConfigReader.Configuration.CentralIPProtocolTypeSetInTheProject
                            {
                                IPAddress = ipConfig.IPAddress,
                                SubnetMask = ipConfig.SubnetMask,
                                RouterAddress = string.IsNullOrEmpty(ipConfig.RouterAddress) ? "0.0.0.0" : ipConfig.RouterAddress
                            }
                        };
                    }

                    // 配置PROFINET设备名称
                    if (projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.PROFINETDeviceName != null)
                    {
                        centralDevice.CentralDeviceInterface.EthernetAddresses.PROFINETDeviceName = new PNConfigLib.ConfigReader.Configuration.CentralDeviceNameType
                        {
                            Item = projectConfig.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName
                        };
                    }

                    // 配置高级选项（AdvancedOptions）- 包含必需的 IsochronousMode 和 RealTimeSettings
                    centralDevice.CentralDeviceInterface.AdvancedOptions = new PNConfigLib.ConfigReader.Configuration.CentralAdvancedOptionsType
                    {
                        IsochronousMode = new PNConfigLib.ConfigReader.Configuration.CentralAdvancedOptionsTypeIsochronousMode
                        {
                            Item = "" // AutomaticSetting - 空字符串表示自动设置
                        }
                    };

                    // 配置实时设置（RealTimeSettings）- 包含 IOCommunication 和 SendClock
                    // 确保始终创建RealTimeSettings，即使IOCommunication为null
                    double sendClockValue = 1.0; // 默认值
                    if (projectConfig.ConfigurationSettings.CentralDevice.AdvancedConfiguration?.RealTimeSettings?.IOCommunication != null)
                    {
                        sendClockValue = projectConfig.ConfigurationSettings.CentralDevice.AdvancedConfiguration.RealTimeSettings.IOCommunication.SendClock;
                        System.Diagnostics.Debug.WriteLine($"XML生成: 从项目配置读取SendClock值: {sendClockValue} ms");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"XML生成: IOCommunication配置为null，使用默认SendClock值: {sendClockValue} ms");
                    }

                    centralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings = new PNConfigLib.ConfigReader.Configuration.CentralAdvancedOptionsTypeRealTimeSettings
                    {
                        IOCommunication = new PNConfigLib.ConfigReader.Configuration.CentralAdvancedOptionsTypeRealTimeSettingsIOCommunication
                        {
                            SendClock = (float)sendClockValue,
                            SendClockSpecified = true // 确保 SendClock 属性被序列化
                        },
                        // 默认添加Synchronization元素，SyncDomainRefID为"Sync-Domain_1"
                        Synchronization = new PNConfigLib.ConfigReader.Configuration.CentralAdvancedOptionsTypeRealTimeSettingsSynchronization
                        {
                            SynchronizationRole = PNConfigLib.ConfigReader.Configuration.SyncRole.Unsynchronized,
                            SyncDomainRefID = "Sync-Domain_1"
                        }
                    };

                    // 添加主站MediaRedundancy配置（如果存在）
                    if (!string.IsNullOrEmpty(projectConfig.ProjectSpecificExtensions.MasterMrpRole) &&
                        projectConfig.ProjectSpecificExtensions.MasterMrpRole != "Not device in the ring")
                    {
                        var mediaRedundancyList = new List<PNConfigLib.ConfigReader.Configuration.MrpRingType>();
                        var xmlMrpRing = new PNConfigLib.ConfigReader.Configuration.MrpRingType
                        {
                            MrpDomainRefID = "mrpDomain1",
                            MrpRole = ConvertMrpRoleToXmlEnum(projectConfig.ProjectSpecificExtensions.MasterMrpRole)
                        };

                        // 添加RingPort配置
                        var ringPorts = new List<PNConfigLib.ConfigReader.Configuration.MrpRingTypeRingPort>();

                        // 处理Ring Port 1
                        if (!string.IsNullOrEmpty(projectConfig.ProjectSpecificExtensions.RingPort1))
                        {
                            string portNumber1 = ExtractPortNumber(projectConfig.ProjectSpecificExtensions.RingPort1);
                            if (!string.IsNullOrEmpty(portNumber1) && byte.TryParse(portNumber1, out byte port1))
                            {
                                ringPorts.Add(new PNConfigLib.ConfigReader.Configuration.MrpRingTypeRingPort
                                {
                                    PortNumber = port1
                                });
                                System.Diagnostics.Debug.WriteLine($"XML生成: 添加Ring Port 1: {port1}");
                            }
                        }

                        // 处理Ring Port 2
                        if (!string.IsNullOrEmpty(projectConfig.ProjectSpecificExtensions.RingPort2))
                        {
                            string portNumber2 = ExtractPortNumber(projectConfig.ProjectSpecificExtensions.RingPort2);
                            if (!string.IsNullOrEmpty(portNumber2) && byte.TryParse(portNumber2, out byte port2))
                            {
                                ringPorts.Add(new PNConfigLib.ConfigReader.Configuration.MrpRingTypeRingPort
                                {
                                    PortNumber = port2
                                });
                                System.Diagnostics.Debug.WriteLine($"XML生成: 添加Ring Port 2: {port2}");
                            }
                        }

                        if (ringPorts.Count > 0)
                        {
                            xmlMrpRing.RingPort = ringPorts;
                            mediaRedundancyList.Add(xmlMrpRing);
                            centralDevice.CentralDeviceInterface.AdvancedOptions.MediaRedundancy = mediaRedundancyList;
                            System.Diagnostics.Debug.WriteLine($"XML生成: 添加主站MediaRedundancy配置 - Role: {projectConfig.ProjectSpecificExtensions.MasterMrpRole}, Ports: {ringPorts.Count}");
                        }
                    }
                }

                configuration.Devices.CentralDevice.Add(centralDevice);

                // 添加分布式设备配置（使用新的配置结构）
                foreach (var device in projectConfig.ConfigurationSettings.DecentralDevices)
                {
                    var decentralDevice = new PNConfigLib.ConfigReader.Configuration.DecentralDeviceType
                    {
                        DeviceRefID = device.DeviceRefID
                    };

                    // 配置分布式设备接口
                    if (device.DecentralDeviceInterface != null)
                    {
                        decentralDevice.DecentralDeviceInterface = new PNConfigLib.ConfigReader.Configuration.DecentralDeviceTypeDecentralDeviceInterface
                        {
                            InterfaceRefID = device.DecentralDeviceInterface.InterfaceRefID
                        };

                        // 配置以太网地址
                        if (device.DecentralDeviceInterface.EthernetAddresses != null)
                        {
                            decentralDevice.DecentralDeviceInterface.EthernetAddresses = new PNConfigLib.ConfigReader.Configuration.DecentralDeviceEthernetAddressesType();

                            // 配置IP协议
                            if (device.DecentralDeviceInterface.EthernetAddresses.IPProtocol?.SetInTheProject != null)
                            {
                                var ipConfig = device.DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject;
                                decentralDevice.DecentralDeviceInterface.EthernetAddresses.IPProtocol = new PNConfigLib.ConfigReader.Configuration.DecentralIPProtocolType
                                {
                                    Item = new PNConfigLib.ConfigReader.Configuration.DecentralIPProtocolTypeSetInTheProject
                                    {
                                        IPAddress = ipConfig.IPAddress,
                                        SubnetMask = ipConfig.SubnetMask,
                                        RouterAddress = string.IsNullOrEmpty(ipConfig.RouterAddress) ? "0.0.0.0" : ipConfig.RouterAddress,
                                        SynchronizeRouterSettingsWithIOController = ipConfig.SynchronizeRouterSettingsWithIOController
                                    },
                                    ItemElementName = PNConfigLib.ConfigReader.Configuration.ItemChoiceType.SetInTheProject
                                };
                            }

                            // 配置PROFINET设备名称
                            if (device.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName != null)
                            {
                                decentralDevice.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName = new PNConfigLib.ConfigReader.Configuration.DecentralDeviceEthernetAddressesTypePROFINETDeviceName
                                {
                                    Item = device.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName,
                                    DeviceNumber = (uint)device.DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.DeviceNumber
                                };
                            }

                            // 配置子网和IO系统引用（默认设置为标准值）
                            decentralDevice.DecentralDeviceInterface.EthernetAddresses.SubnetRefID =
                                !string.IsNullOrEmpty(device.DecentralDeviceInterface.EthernetAddresses.SubnetRefID)
                                ? device.DecentralDeviceInterface.EthernetAddresses.SubnetRefID
                                : "PNIE_1";

                            decentralDevice.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID =
                                !string.IsNullOrEmpty(device.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID)
                                ? device.DecentralDeviceInterface.EthernetAddresses.IOSystemRefID
                                : "PROFINETIO-System_100";
                        }

                        // 配置同步模式（IsochronousMode）- 必需的元素
                        decentralDevice.DecentralDeviceInterface.IsochronousMode = new PNConfigLib.ConfigReader.Configuration.IsochronousModeType
                        {
                            IsoModeEnabled = false, // 默认禁用同步模式
                            TiToValues = new PNConfigLib.ConfigReader.Configuration.IsochronousModeTypeTiToValues
                            {
                                Item = "" // AutomaticMinimum - 空字符串表示自动最小值
                            }
                        };

                        // 配置高级选项（AdvancedOptions）- 包含实时设置
                        if (device.DecentralDeviceInterface.AdvancedConfiguration?.RealTimeSettings != null)
                        {
                            var realTimeSettings = device.DecentralDeviceInterface.AdvancedConfiguration.RealTimeSettings;

                            var realTimeSettingsXml = new PNConfigLib.ConfigReader.Configuration.DecentralAdvancedOptionsTypeRealTimeSettings
                            {
                                IOCycle = new PNConfigLib.ConfigReader.Configuration.DecentralAdvancedOptionsTypeRealTimeSettingsIOCycle
                                {
                                    UpdateTime = new PNConfigLib.ConfigReader.Configuration.IOCycleTypeUpdateTime
                                    {
                                        Mode = realTimeSettings.IOCycle.UpdateTime.Mode == Models.UpdateTimeMode.Manual
                                            ? PNConfigLib.ConfigReader.Configuration.IOCycleTypeUpdateTimeMode.Manual
                                            : PNConfigLib.ConfigReader.Configuration.IOCycleTypeUpdateTimeMode.Automatic,
                                        ModeSpecified = true,
                                        Value = realTimeSettings.IOCycle.UpdateTime.Value,
                                        ValueSpecified = true
                                    },
                                    AcceptedUpdateCyclesWithoutIOData = realTimeSettings.IOCycle.AcceptedUpdateCyclesWithoutIOData
                                }
                            };

                            // 默认生成Synchronization元素，SyncDomainRefID默认为"Sync-Domain_1"
                            var syncDomainRefID = string.IsNullOrEmpty(realTimeSettings.Synchronization.SyncDomainRefID)
                                ? "Sync-Domain_1"
                                : realTimeSettings.Synchronization.SyncDomainRefID;

                            realTimeSettingsXml.Synchronization = new PNConfigLib.ConfigReader.Configuration.DecentralAdvancedOptionsTypeRealTimeSettingsSynchronization
                            {
                                SynchronizationRole = realTimeSettings.Synchronization.SynchronizationRole == Models.SyncRole.SyncSlave
                                    ? PNConfigLib.ConfigReader.Configuration.SyncRole.SyncSlave
                                    : realTimeSettings.Synchronization.SynchronizationRole == Models.SyncRole.SyncMaster
                                        ? PNConfigLib.ConfigReader.Configuration.SyncRole.SyncMaster
                                        : PNConfigLib.ConfigReader.Configuration.SyncRole.Unsynchronized,
                                SyncDomainRefID = syncDomainRefID
                            };

                            var advancedOptions = new PNConfigLib.ConfigReader.Configuration.DecentralAdvancedOptionsType
                            {
                                RealTimeSettings = realTimeSettingsXml
                            };

                            // 添加MediaRedundancy配置（如果存在）
                            if (device.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy?.MrpRings != null &&
                                device.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy.MrpRings.Count > 0)
                            {
                                var mediaRedundancyList = new List<PNConfigLib.ConfigReader.Configuration.MrpRingType>();

                                foreach (var mrpRing in device.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy.MrpRings)
                                {
                                    var xmlMrpRing = new PNConfigLib.ConfigReader.Configuration.MrpRingType
                                    {
                                        MrpDomainRefID = "mrpDomain1",
                                        MrpRole = ConvertMrpRoleToXmlEnum(mrpRing.MrpRole)
                                    };

                                    // 添加Ring Port配置
                                    if (mrpRing.RingPorts != null && mrpRing.RingPorts.Count > 0)
                                    {
                                        var ringPorts = new List<PNConfigLib.ConfigReader.Configuration.MrpRingTypeRingPort>();
                                        foreach (var ringPort in mrpRing.RingPorts)
                                        {
                                            // 将string类型的PortNumber转换为byte类型
                                            if (byte.TryParse(ringPort.PortNumber, out byte portNumber))
                                            {
                                                ringPorts.Add(new PNConfigLib.ConfigReader.Configuration.MrpRingTypeRingPort
                                                {
                                                    PortNumber = portNumber
                                                });
                                                System.Diagnostics.Debug.WriteLine($"XML生成: 添加分布式设备Ring Port: {portNumber}");
                                            }
                                            else
                                            {
                                                System.Diagnostics.Debug.WriteLine($"XML生成: 无效的端口号格式: {ringPort.PortNumber}");
                                            }
                                        }
                                        xmlMrpRing.RingPort = ringPorts;
                                    }

                                    mediaRedundancyList.Add(xmlMrpRing);
                                }

                                advancedOptions.MediaRedundancy = mediaRedundancyList;
                                System.Diagnostics.Debug.WriteLine($"XML生成: 添加分布式设备MediaRedundancy配置 - Device: {device.DeviceRefID}, MRP环数量: {mediaRedundancyList.Count}");
                            }

                            decentralDevice.DecentralDeviceInterface.AdvancedOptions = advancedOptions;
                        }
                    }

                    // 添加模块配置（如果存在）
                    if (device.Modules != null && device.Modules.Count > 0)
                    {
                        decentralDevice.Module = new List<PNConfigLib.ConfigReader.Configuration.ModuleType>();

                        foreach (var module in device.Modules)
                        {
                            var xmlModule = new PNConfigLib.ConfigReader.Configuration.ModuleType
                            {
                                ModuleID = module.ModuleRefID, // 使用正确的字段名
                                SlotNumber = (ushort)module.SlotNumber, // 使用正确的数据类型
                                GSDRefID = !string.IsNullOrEmpty(module.GSDRefID) ? module.GSDRefID : module.ModuleRefID // 优先使用GSDRefID
                            };

                            // 添加子模块配置（如果存在）
                            if (module.Submodules != null && module.Submodules.Count > 0)
                            {
                                xmlModule.Submodule = new List<PNConfigLib.ConfigReader.Configuration.ModuleTypeSubmodule>();

                                foreach (var submodule in module.Submodules)
                                {
                                    var xmlSubmodule = new PNConfigLib.ConfigReader.Configuration.ModuleTypeSubmodule
                                    {
                                        SubmoduleID = GenerateSubmoduleID(module, submodule), // 生成有意义的子模块ID
                                        SubslotNumber = 1, // 默认子槽号
                                        GSDRefID = ExtractSubmoduleGSDRefID(module.GSDRefID) // 从模块的GSDRefID提取子模块的GSDRefID
                                    };

                                    // 配置IO地址（如果存在地址配置）
                                    if (submodule.AddressConfiguration != null)
                                    {
                                        bool hasInputAddress = !string.IsNullOrEmpty(submodule.AddressConfiguration.InputStartAddress) &&
                                                             uint.TryParse(submodule.AddressConfiguration.InputStartAddress, out _);
                                        bool hasOutputAddress = !string.IsNullOrEmpty(submodule.AddressConfiguration.OutputStartAddress) &&
                                                              uint.TryParse(submodule.AddressConfiguration.OutputStartAddress, out _);

                                        if (hasInputAddress || hasOutputAddress)
                                        {
                                            // IOAddresses是List<object>类型，需要添加具体的地址对象
                                            if (hasInputAddress && uint.TryParse(submodule.AddressConfiguration.InputStartAddress, out uint inputAddr))
                                            {
                                                var inputAddresses = new PNConfigLib.ConfigReader.Configuration.ModuleTypeInputAddresses
                                                {
                                                    StartAddress = inputAddr
                                                };
                                                xmlSubmodule.IOAddresses.Add(inputAddresses);
                                            }

                                            if (hasOutputAddress && uint.TryParse(submodule.AddressConfiguration.OutputStartAddress, out uint outputAddr))
                                            {
                                                var outputAddresses = new PNConfigLib.ConfigReader.Configuration.ModuleTypeOutputAddresses
                                                {
                                                    StartAddress = outputAddr
                                                };
                                                xmlSubmodule.IOAddresses.Add(outputAddresses);
                                            }
                                        }
                                    }

                                    // 注意：不添加空的ParameterRecordDataItems元素
                                    // 只有在确实有参数记录数据时才添加
                                    // 这样避免XSD验证错误

                                    xmlModule.Submodule.Add(xmlSubmodule);
                                }
                            }

                            // 注意：不添加空的ParameterRecordDataItems元素
                            // 只有在确实有参数记录数据时才添加
                            // 这样避免XSD验证错误

                            decentralDevice.Module.Add(xmlModule);
                        }
                    }

                    // 清除空的ParameterRecordDataItems列表，避免XSD验证错误
                    if (decentralDevice.ParameterRecordDataItems != null && decentralDevice.ParameterRecordDataItems.Count == 0)
                    {
                        decentralDevice.ParameterRecordDataItems = null;
                    }

                    // 清除设备接口的空ParameterRecordDataItems
                    if (decentralDevice.DecentralDeviceInterface?.ParameterRecordDataItems != null &&
                        decentralDevice.DecentralDeviceInterface.ParameterRecordDataItems.Count == 0)
                    {
                        decentralDevice.DecentralDeviceInterface.ParameterRecordDataItems = null;
                    }

                    // 清除模块和子模块的空ParameterRecordDataItems
                    if (decentralDevice.Module != null)
                    {
                        foreach (var module in decentralDevice.Module)
                        {
                            if (module.ParameterRecordDataItems != null && module.ParameterRecordDataItems.Count == 0)
                            {
                                module.ParameterRecordDataItems = null;
                            }

                            if (module.Submodule != null)
                            {
                                foreach (var submodule in module.Submodule)
                                {
                                    if (submodule.ParameterRecordDataItems != null && submodule.ParameterRecordDataItems.Count == 0)
                                    {
                                        submodule.ParameterRecordDataItems = null;
                                    }
                                }
                            }

                            // 清除端口的空ParameterRecordDataItems
                            if (module.Port != null)
                            {
                                foreach (var port in module.Port)
                                {
                                    if (port.ParameterRecordDataItems != null && port.ParameterRecordDataItems.Count == 0)
                                    {
                                        port.ParameterRecordDataItems = null;
                                    }
                                }
                            }
                        }
                    }



                    configuration.Devices.DecentralDevice.Add(decentralDevice);
                }

                // 添加默认Subnet配置
                var domainManagement = new PNConfigLib.ConfigReader.Configuration.SubnetDomainManagement
                {
                    SyncDomains = new List<PNConfigLib.ConfigReader.Configuration.SyncDomainType>
                    {
                        new PNConfigLib.ConfigReader.Configuration.SyncDomainType
                        {
                            SyncDomainID = "Sync-Domain_1",
                            SyncDomainName = "Sync-Domain_1"
                        }
                    }
                };

                // 检查MasterMrpRole配置和分布式设备MediaRedundancy配置，决定是否生成MrpDomains
                var masterMrpRole = projectConfig.ProjectSpecificExtensions?.MasterMrpRole ?? "Not device in the ring";

                // 检查是否有分布式设备配置了MediaRedundancy
                bool hasDecentralDeviceMediaRedundancy = projectConfig.ConfigurationSettings?.DecentralDevices?.Any(device =>
                    device.DecentralDeviceInterface?.AdvancedConfiguration?.MediaRedundancy?.MrpRings != null &&
                    device.DecentralDeviceInterface.AdvancedConfiguration.MediaRedundancy.MrpRings.Count > 0) ?? false;

                if (masterMrpRole != "Not device in the ring" || hasDecentralDeviceMediaRedundancy)
                {
                    // 当主站配置了MRP或任何分布式设备配置了MediaRedundancy时才添加MrpDomains
                    domainManagement.MrpDomains = new List<PNConfigLib.ConfigReader.Configuration.MrpDomainType>
                    {
                        new PNConfigLib.ConfigReader.Configuration.MrpDomainType
                        {
                            MrpDomainID = "mrpDomain1",
                            MrpDomainName = projectConfig.ProjectSpecificExtensions?.MrpDomain ?? "mrpdomain-1"
                        }
                    };
                }
                else
                {
                    // 当主站MrpRole是"Not device in the ring"且没有分布式设备配置MediaRedundancy时，将MrpDomains设置为null以避免生成空元素
                    domainManagement.MrpDomains = null;
                }

                var defaultSubnet = new PNConfigLib.ConfigReader.Configuration.Subnet
                {
                    SubnetID = "PNIE_1",
                    IOSystem = new List<PNConfigLib.ConfigReader.Configuration.SubnetIOSystem>
                    {
                        new PNConfigLib.ConfigReader.Configuration.SubnetIOSystem
                        {
                            IOSystemID = "PROFINETIO-System_100",
                            General = new PNConfigLib.ConfigReader.Configuration.SubnetIOSystemGeneral
                            {
                                IOSystemNumber = 100,
                                IOSystemNumberSpecified = true,
                                IOSystemName = "PROFINET IO-System"
                            }
                        }
                    },
                    DomainManagement = domainManagement
                };

                configuration.Subnet.Add(defaultSubnet);

                // 序列化到XML文件
                string filePath = Path.Combine(projectConfig.OutputConfiguration.OutputDirectory, "Configuration.xml");
                SerializeToXml(configuration, filePath);

                result.Success = true;
                result.ConfigurationPath = filePath;
                result.Message = "Configuration.xml生成成功";

                return result;
            }
            catch (Exception ex)
            {
                result.Message = $"生成Configuration.xml时发生异常: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 将对象序列化为XML文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        private void SerializeToXml<T>(T obj, string filePath)
        {
            var serializer = new XmlSerializer(typeof(T));
            using (var writer = new StreamWriter(filePath))
            {
                serializer.Serialize(writer, obj);
            }
        }

        /// <summary>
        /// 将字符串转换为PNDriverInterfaceEnum
        /// </summary>
        /// <param name="interfaceType">接口类型字符串</param>
        /// <returns>PNDriverInterfaceEnum枚举值</returns>
        private PNDriverInterfaceEnum GetPNDriverInterfaceType(string interfaceType)
        {
            if (string.IsNullOrEmpty(interfaceType))
                return PNDriverInterfaceEnum.LinuxNative;

            return interfaceType.ToLower() switch
            {
                "cp1625stand-alone" => PNDriverInterfaceEnum.CP1625Standalone,
                "cp1625host" => PNDriverInterfaceEnum.CP1625Host,
                "linux" => PNDriverInterfaceEnum.Linux,
                "linux native" => PNDriverInterfaceEnum.LinuxNative,
                "iot20x0" => PNDriverInterfaceEnum.IoT20x0,
                "windows" => PNDriverInterfaceEnum.Windows,
                "custom" => PNDriverInterfaceEnum.Custom,
                _ => PNDriverInterfaceEnum.Windows
            };
        }

        /// <summary>
        /// 生成有意义的子模块ID
        /// </summary>
        private string GenerateSubmoduleID(ModuleConfig module, SubmoduleConfig submodule)
        {
            try
            {
                // 根据模块的IO类型生成有意义的子模块名称
                if (submodule.AddressConfiguration != null)
                {
                    bool hasInput = !string.IsNullOrEmpty(submodule.AddressConfiguration.InputStartAddress) &&
                                   uint.TryParse(submodule.AddressConfiguration.InputStartAddress, out _);
                    bool hasOutput = !string.IsNullOrEmpty(submodule.AddressConfiguration.OutputStartAddress) &&
                                    uint.TryParse(submodule.AddressConfiguration.OutputStartAddress, out _);

                    if (hasInput && hasOutput)
                    {
                        return "Submodulewithbothinputandoutput";
                    }
                    else if (hasInput)
                    {
                        return "Submodulewith64bytesinput"; // 可以根据实际长度调整
                    }
                    else if (hasOutput)
                    {
                        return "Submodulewith64bytesoutput"; // 可以根据实际长度调整
                    }
                }

                // 默认子模块名称
                return $"Submodule_{module.ModuleRefID}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"生成子模块ID时出错: {ex.Message}");
                return $"Submodule_{module.ModuleRefID}";
            }
        }

        /// <summary>
        /// 从模块的GSDRefID动态提取子模块的GSDRefID
        /// </summary>
        private string ExtractSubmoduleGSDRefID(string moduleGSDRefID)
        {
            try
            {
                if (string.IsNullOrEmpty(moduleGSDRefID))
                    return "1";

                Debug.WriteLine($"开始为模块 {moduleGSDRefID} 动态提取子模块GSDRefID");

                // 尝试从当前项目的GSDML文件中动态获取模块的子模块信息
                string submoduleGSDRefID = GetSubmoduleGSDRefIDFromGSDML(moduleGSDRefID);
                if (!string.IsNullOrEmpty(submoduleGSDRefID))
                {
                    Debug.WriteLine($"从GSDML动态提取到模块 {moduleGSDRefID} 的子模块GSDRefID: {submoduleGSDRefID}");
                    return submoduleGSDRefID;
                }

                // 如果动态提取失败，使用后备方案
                Debug.WriteLine($"动态提取失败，使用后备方案为模块 {moduleGSDRefID} 提取子模块GSDRefID");

                // 对于ID_Mod_XX格式，提取XX部分作为子模块GSDRefID
                if (moduleGSDRefID.StartsWith("ID_Mod_"))
                {
                    string numberPart = moduleGSDRefID.Substring("ID_Mod_".Length);
                    Debug.WriteLine($"从模块 {moduleGSDRefID} 提取子模块GSDRefID: {numberPart}");
                    return numberPart;
                }

                // 最后的默认值
                Debug.WriteLine($"无法为模块 {moduleGSDRefID} 确定子模块GSDRefID，使用默认值: 1");
                return "1";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取子模块GSDRefID时出错: {ex.Message}");
                return "1"; // 默认值
            }
        }

        /// <summary>
        /// 从GSDML文件中动态获取模块的子模块GSDRefID
        /// </summary>
        private string GetSubmoduleGSDRefIDFromGSDML(string moduleGSDRefID)
        {
            try
            {
                Debug.WriteLine($"开始使用ModuleCatalogHelper查找模块 {moduleGSDRefID} 的子模块信息");

                // 尝试通过不同的GSDML文件名获取模块目录
                ModuleCatalog moduleCatalog = null;

                // 尝试常见的GSDML文件名
                var commonGSDMLFiles = new[]
                {
                    "GSDML-V2.45-Siemens-PROFINETDriver-IOD-20250213.xml",
                    "GSDML-V2.43-Siemens-ERTEC200pEvalkit-20230301.xml",
                    "GSDML-V2.4-Siemens-ET200SP-20190301.xml"
                };

                foreach (var gsdmlFile in commonGSDMLFiles)
                {
                    try
                    {
                        moduleCatalog = ModuleCatalogHelper.GetModuleCatalogWithGsdName(gsdmlFile, moduleGSDRefID);
                        if (moduleCatalog != null)
                        {
                            Debug.WriteLine($"通过GSDML文件 {gsdmlFile} 找到模块 {moduleGSDRefID}");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"尝试GSDML文件 {gsdmlFile} 时出错: {ex.Message}");
                    }
                }

                // 如果标准方法失败，回退到直接搜索Catalog
                if (moduleCatalog == null)
                {
                    Debug.WriteLine($"标准方法失败，回退到直接搜索Catalog");
                    foreach (var moduleEntry in Catalog.ModuleList)
                    {
                        if (moduleEntry.Key.EndsWith($"\\{moduleGSDRefID}", StringComparison.OrdinalIgnoreCase))
                        {
                            moduleCatalog = moduleEntry.Value;
                            Debug.WriteLine($"通过直接搜索找到模块: {moduleEntry.Key}");
                            break;
                        }
                    }
                }

                if (moduleCatalog == null)
                {
                    Debug.WriteLine($"未找到模块 {moduleGSDRefID} 的ModuleCatalog");
                    return null;
                }

                // 检查虚拟子模块列表
                if (moduleCatalog.VirtualSubmoduleList != null && moduleCatalog.VirtualSubmoduleList.Count > 0)
                {
                    var firstVirtualSubmodule = moduleCatalog.VirtualSubmoduleList.First();
                    if (firstVirtualSubmodule.AttributeAccess?.AttributeList?.ContainsKey("GsdId") == true)
                    {
                        string gsdId = firstVirtualSubmodule.AttributeAccess.AttributeList["GsdId"] as string;
                        Debug.WriteLine($"从虚拟子模块列表获取到GSDRefID: {gsdId}");
                        return gsdId;
                    }
                }

                // 检查可插拔子模块列表
                if (moduleCatalog.PluggableSubmoduleList != null && moduleCatalog.PluggableSubmoduleList.Count > 0)
                {
                    var firstPluggableSubmodule = moduleCatalog.PluggableSubmoduleList.Keys.FirstOrDefault();
                    if (!string.IsNullOrEmpty(firstPluggableSubmodule))
                    {
                        Debug.WriteLine($"从可插拔子模块列表获取到GSDRefID: {firstPluggableSubmodule}");
                        return firstPluggableSubmodule;
                    }
                }

                Debug.WriteLine($"模块 {moduleGSDRefID} 没有找到子模块信息");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从GSDML获取子模块GSDRefID时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 转换MRP角色到XML枚举
        /// </summary>
        private PNConfigLib.ConfigReader.Configuration.MrpRole ConvertMrpRoleToXmlEnum(string mrpRole)
        {
            return mrpRole switch
            {
                "MrpManager" => PNConfigLib.ConfigReader.Configuration.MrpRole.MrpManager,
                "MrpAutoManager" => PNConfigLib.ConfigReader.Configuration.MrpRole.MrpAutoManager,
                "MrpClient" => PNConfigLib.ConfigReader.Configuration.MrpRole.MrpClient,
                _ => PNConfigLib.ConfigReader.Configuration.MrpRole.MrpClient // 默认为Client
            };
        }

        /// <summary>
        /// 从端口名称中提取纯数字
        /// </summary>
        private string ExtractPortNumber(string portName)
        {
            if (string.IsNullOrEmpty(portName))
                return string.Empty;

            // 使用正则表达式提取数字
            var match = System.Text.RegularExpressions.Regex.Match(portName, @"\d+");
            return match.Success ? match.Value : string.Empty;
        }

    }
}
