/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Export.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Xml;
using C = PNConfigLib.Gsd.Interpreter.Common;
using S = PNConfigLib.Gsd.Interpreter.Structure;

using System.Collections.Generic;
#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all element names of the export specification and corresponding settings.
    /// </summary>
    internal static class Export
    {
        //########################################################################################
        #region Vocabulary
        // Contains all element names as constants

        #region Elements

        /// <summary>name of the gsdexport element</summary>
		public const string s_ElementExport = @"gsdexport";
        /// <summary>name of the gsddevice element</summary>
        public const string s_ElementDevice = @"gsddevice";
        /// <summary>name of the gsdstructuremodel element</summary>
        public const string s_ElementStructureModel = @"gsdstructuremodel";
        /// <summary>name of the gsdcommonmodel element</summary>
        public const string s_ElementCommonModel = @"gsdcommonmodel";
        /// <summary>name of the gsdcollection element</summary>
		public const string s_ElementCollection = @"gsdcollection";
        /// <summary>name of the gsdobj element</summary>
        public const string s_ElementObject = @"gsdobj";
        /// <summary>name of the gsdprop element</summary>
        public const string s_ElementProperty = @"gsdprop";
        /// <summary>name of the gsdref element</summary>
		public const string s_ElementReference = @"gsdref";
        /// <summary>name of the gsddict element</summary>
        public const string s_ElementDictionary = @"gsddict";
        /// <summary>name of the gsdreportexport element</summary>
        public const string s_ElementReportExport = @"gsdreportexport";
        /// <summary>name of the gsdreport element</summary>
        public const string s_ElementReport = @"gsdreport";
        /// <summary>name of the gsdreports element</summary>
        public const string s_ElementReports = @"gsdreports";

        #endregion

        #region Attributes

        /// <summary>name of the name attribute</summary>
        public const string s_AttributeName = @"name";
        /// <summary>name of the type attribute</summary>
        public const string s_AttributeType = @"type";
        /// <summary>name of the subtype attribute</summary>
        public const string s_AttributeSubtype = @"subtype";
        /// <summary>name of the target attribute</summary>
		public const string s_AttributeTarget = @"target";
        /// <summary>name of the targetcollection attribute</summary>
        public const string s_AttributeTargetCollection = @"targetcollection";
        /// <summary>name of the key attribute</summary>
        public const string s_AttributeKey = @"key";
        /// <summary>name of the errors attribute</summary>
        public const string s_AttributeErrors = @"errors";
        /// <summary>name of the minor errors attribute</summary>
        public const string s_AttributeMinorErrors = @"minor errors";
        /// <summary>name of the warnings attribute</summary>
        public const string s_AttributeWarnings = @"warnings";
        /// <summary>name of the informations attribute</summary>
        public const string s_AttributeInformations = @"informations";

        #endregion

        #endregion

        //########################################################################################
        #region Contents
        // Contains all content values as constants

        /// <summary>indentation for export</summary>
        public const int s_Indentation = 1;
        /// <summary>indentation character for export</summary>
        public const char s_IndentationCharacter = (char)0x9; // TAB

        #region Special Types

        /// <summary>collection type for export</summary>
        public const string s_TypeCollection = @"collection";
        /// <summary>string type for export</summary>
        public const string s_TypeString = @"string";
        /// <summary>unsigned integer 32 type for export</summary>
        public const string s_TypeUint = @"uint32";
        /// <summary>unsigned integer 32 type for export</summary>
        public const string s_TypeInt = @"int32";
        /// <summary>enum type for export</summary>
        public const string s_TypeEnum = @"enum";
        /// <summary>boolean type for export</summary>
        public const string s_TypeBoolean = @"boolean";
        /// <summary>single type for export</summary>
        public const string s_TypeSingle = @"Single";
        /// <summary>object type for export</summary>
        public const string s_TypeObject = @"object";
        /// <summary>array type for export</summary>
        public const string s_TypeArray = @"array";
        /// <summary>dictionary type for export</summary>
        public const string s_TypeDictionary = @"dictionary";

        /// <summary>MainFamilies enum sub type for export</summary>
        public const string s_SubtypeMainFamilies = @"MainFamilies";
        /// <summary>DataItemTypes enum sub type for export</summary>
        public const string s_SubtypeDataItemTypes = @"DataItemTypes";
        /// <summary>DataTypes enum sub type for export</summary>
        public const string s_SubtypeDataTypes = @"DataTypes";
        /// <summary>ValueTypes enum sub type for export</summary>
        public const string s_SubtypeValueTypes = @"ValueTypes";
        /// <summary>GraphicTypes enum sub type for export</summary>
        public const string s_SubtypeGraphicTypes = @"GraphicTypes";
        /// <summary>IOConsistencies enum sub type for export</summary>
        public const string s_SubtypeIoConsistencies = @"IOConsistencies";
        /// <summary>RTClasses enum sub type for export</summary>
        public const string s_SubtypeRtClasses = @"RTClasses";
        /// <summary>SynchronisationRoles enum sub type for export</summary>
        public const string s_SubtypeSynchronisationRoles = @"SynchronisationRoles";
        /// <summary>PortTypes enum sub type for export</summary>
        public const string s_SubtypeMauTypes = @"MAUTypes";
		/// <summary>FParameters enum sub type for export</summary>
		public const string s_SubtypeFParameters = @"FParameters";
        /// <summary>ReportTypes enum sub type for export</summary>
        public const string s_SubtypeReportTypes = @"ReportTypes";
        /// <summary>LinkStateDiagnosisTypes enum sub type for export</summary>
        public const string s_SubtypeLinkStateDiagnosisTypes = @"LinkStateDiagnosisTypes";
        /// <summary>FragmentationTypes enum sub type for export</summary>
        public const string s_SubtypeFragmentationTypes = @"FragmentationTypes";
        /// <summary>EntityClass enum sub type for export</summary>
        public const string s_SubtypeEntityClasses = @"EntityClasses";
        /// <summary>EntitySubclass enum sub type for export</summary>
        public const string s_SubtypeEntitySubclasses = @"EntitySubclasses";
        /// <summary>ObserverTypes enum sub type for export</summary>
        public const string s_SubtypeObserverTypes = @"ObserverTypes";

        public const string s_SubtypeSegmentClasses = @"SegmentClasses";
        public const string s_SubtypePortClasses = @"PortClasses";
        public const string s_SubtypePowerClasses = @"PowerClasses";
        public const string s_SubtypeProtectionClasses = @"ProtectionClasses";
        public const string s_SubtypeLinkSpeeds = @"LinkSpeeds";
        public const string s_SubtypePADeviceClasses = @"PADeviceClasses";

        #endregion

        #region Collection Names

        /// <summary>name of the collection for AccessPointStructureElement objects</summary>
        public const string s_CollNameDeviceAccessPointStructureElements = @"DeviceAccessPointStructureElements";
        /// <summary>name of the collection for ModuleStructureElement objects</summary>
        public const string s_CollNameModuleStructureElements = @"ModuleStructureElements";
        /// <summary>name of the collection for SubmoduleStructureElement objects</summary>
        public const string s_CollNameSubmoduleStructureElements = @"SubmoduleStructureElements";
        /// <summary>name of the collection for RecordDataStructureElement objects</summary>
        public const string s_CollNameRecordDataStructureElements = @"RecordDataStructureElements";
        /// <summary>name of the collection for DeviceAccessPoint objects</summary>
        public const string s_CollNameDeviceAccessPoints = @"DeviceAccessPoints";
        /// <summary>name of the collection for Module objects</summary>
        public const string s_CollNameModules = @"Modules";
        /// <summary>name of the collection for VirtualSubmodule objects</summary>
        public const string s_CollNameVirtualSubmodules = @"VirtualSubmodules";
        /// <summary>name of the collection for PhysicalSubmodule objects</summary>
        public const string s_CollNamePhysicalSubmodules = @"PhysicalSubmodules";
        /// <summary>name of the collection for RecordDataItem objects</summary>
        public const string s_CollNameRecordDataItems = @"RecordDataItems";
        /// <summary>name of the collection for CommunicationInterfaceItem objects</summary>
        public const string s_CollNameCommunicationInterfaceItems = @"CommunicationInterfaceItems";

        #endregion

        #endregion

        //########################################################################################
        #region Writer Methods

        #region Property Writer

        /// <summary>
        /// Writes the elements and attributes needed for the export of an string property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteStringProperty(ref XmlTextWriter writer, string name, string val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));

            // Value (val) can be empty string. It's ok!
            #endregion

            writer.WriteStartElement(Export.s_ElementProperty);
            writer.WriteAttributeString(Export.s_AttributeName, name);
            writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeString);
            string propval = String.Empty;
            if (null != val)    // If null, write empty string value!
                propval = val;
            writer.WriteString(propval);
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an unsigned integer 32
        /// property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteUint32Property(ref XmlTextWriter writer, string name, uint val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));

            // Value (val) can be empty string. It's ok!
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeUint);
            writer.WriteString(XmlConvert.ToString(val));
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an integer 32
        /// property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteInt32Property(ref XmlTextWriter writer, string name, int val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));

            // Value (val) can be empty string. It's ok!
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeInt);
            writer.WriteString(XmlConvert.ToString(val));
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an boolean
        /// property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteBooleanProperty(ref XmlTextWriter writer, string name, bool val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));

            // Value (val) can be empty string. It's ok!
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeBoolean);
            writer.WriteString(XmlConvert.ToString(val));
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an single
        /// property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteSingleProperty(ref XmlTextWriter writer, string name, Single val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));

            // Value (val) can be empty string. It's ok!
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeSingle);
            writer.WriteString(XmlConvert.ToString(val));
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an enum
        /// property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Value of the property to export.</param>
        /// <param name="type">Type of the enum property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value, val or type string</exception>
        public static void WriteEnumProperty(ref XmlTextWriter writer, string name, string val, string type)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (val == null)
                throw new ArgumentNullException(nameof(val));
            if (type == null)
                throw new ArgumentNullException(nameof(type));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            if (val.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(val));
            if (type.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(type));
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeEnum);
            writer.WriteAttributeString(s_AttributeSubtype, type);
            writer.WriteString(val);
            writer.WriteEndElement();
        }


        /// <summary>
        /// Writes the elements and attributes needed for the export of an string property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="values">List of unsigned integer 32 properties to export.</param>
        /// <param name="shortexport">Specifies whether the array should be exported with sub type,
        /// or with separate property export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteArrayUint32Property(ref XmlTextWriter writer, string name, ArrayList values, bool shortexport)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeArray);

            if (shortexport)
            {
                writer.WriteAttributeString(s_AttributeSubtype, s_TypeUint);

                if (null != values) // If null write only empty element!
                {
                    bool first = true;
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    foreach (object obj in values)
                    {
                        if (first == false)
                            sb.Append(Constants.s_Space);
                        sb.Append(obj);
                        first = false;
                    }
                    writer.WriteString(sb.ToString());
                }
            }
            else
            {
                if (null != values) // If null write only empty element!
                {
                    foreach (object obj in values)
                    {
                        writer.WriteStartElement(Export.s_ElementProperty);
                        writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeUint);
                        writer.WriteString(obj.ToString());
                        writer.WriteEndElement();
                    }
                }
            }
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an string property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="values">List of unsigned integer 32 properties to export.</param>
        /// <param name="shortexport">Specifies whether the array should be exported with sub type,
        /// or with separate property export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteArrayUint32Property(ref XmlTextWriter writer, string name, List<uint> values, bool shortexport)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeArray);

            if (shortexport)
            {
                writer.WriteAttributeString(s_AttributeSubtype, s_TypeUint);

                if (null != values)	// If null write only empty element!
                {
                    bool first = true;
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    foreach (object obj in values)
                    {
                        if (first == false)
                            sb.Append(Constants.s_Space);
                        sb.Append(obj);
                        first = false;
                    }
                    writer.WriteString(sb.ToString());
                }
            }
            else
            {
                if (null != values)	// If null write only empty element!
                {
                    foreach (object obj in values)
                    {
                        writer.WriteStartElement(s_ElementProperty);
                        writer.WriteAttributeString(s_AttributeType, s_TypeUint);
                        writer.WriteString(obj.ToString());
                        writer.WriteEndElement();
                    }
                }
            }
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an string property value.
        /// </summary>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="values">List of unsigned integer 32 properties to export.</param>
        /// <param name="shortexport">Specifies whether the array should be exported with sub type,
        /// or with separate property export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        public static void WriteArrayStringProperty(ref XmlTextWriter writer, string name, ArrayList values, bool shortexport)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeArray);

            if (shortexport)
            {
                writer.WriteAttributeString(s_AttributeSubtype, s_TypeString);

                if (null != values) // If null write only empty element!
                {
                    bool first = true;
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    foreach (object obj in values)
                    {
                        if (first == false)
                            sb.Append(Constants.s_Semicolon);
                        sb.Append(obj);
                        first = false;
                    }
                    writer.WriteString(sb.ToString());
                }
            }
            else
            {
                if (null != values) // If null write only empty element!
                {
                    foreach (object obj in values)
                    {
                        writer.WriteStartElement(s_ElementProperty);
                        writer.WriteAttributeString(s_AttributeType, s_TypeString);
                        writer.WriteString(obj.ToString());
                        writer.WriteEndElement();
                    }
                }
            }
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of an GsdObject 
        /// property.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">GsdObject of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        /// <exception cref="SerializationException">is thrown, in case of an failed object serialization</exception>
        public static void WriteSimpleObjectProperty(C.SerializeOptions option, ref XmlTextWriter writer, string name, C.GsdObject val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(Export.s_ElementProperty);
            writer.WriteAttributeString(Export.s_AttributeName, name);
            writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeObject);
            if (null != val)    // If null write only empty element!
            {
                bool succeeded = val.Serialize(option, ref writer);
                if (!succeeded)
                    throw new SerializationException("Couldn't serialize " + val.GetType().Name + "!");
            }
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of a list of GsdObjects
        /// in a property.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">List of GsdObjects of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        /// <exception cref="SerializationException">is thrown, in case of an failed object serialization</exception>
        public static void WriteSimpleObjectListProperty(C.SerializeOptions option, ref XmlTextWriter writer, string name, ArrayList val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(Export.s_ElementProperty);
            writer.WriteAttributeString(Export.s_AttributeName, name);
            writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeCollection);
            if (null != val)    // If null write only empty element!
            {
                // Serialize the found objects.
                foreach (C.GsdObject obj in val)
                {
                    bool succeeded = obj.Serialize(option, ref writer);
                    if (!succeeded)
                        throw new SerializationException("Couldn't serialize " + obj.GetType().Name + "!");
                }
            }
            writer.WriteEndElement();
        }

        /// <summary>
        /// Writes the elements and attributes needed for the export of a list of ModuleObjects
        /// in a property, whereby the objects must be saved directly or as reference,
        /// depending on the serialize option.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">List of GsdObjects of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name or collection name 
        /// value string</exception>
        /// <exception cref="SerializationException">is thrown, in case of an failed object serialization</exception>
        public static void WriteComplexObjectListProperty(C.SerializeOptions option, ref XmlTextWriter writer, string name, ArrayList val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(Export.s_ElementProperty);
            writer.WriteAttributeString(Export.s_AttributeName, name);
            writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeCollection);
            if (null != val)    // If null write only empty element!
            {
                if (option == C.SerializeOptions.Deep)
                {
                    // Serialize the found objects.
                    foreach (C.GsdObject obj in val)
                    {
                        bool succeeded = obj.Serialize(option, ref writer);
                        if (!succeeded)
                            throw new SerializationException("Couldn't serialize " + obj.GetType().Name + "!");
                    }
                }
                else if (option == C.SerializeOptions.Export || option == C.SerializeOptions.Flat)
                {
                    // Serialize only reference to object.
                    string collname = String.Empty;
                    foreach (C.GsdObject obj in val)
                    {
                        if (String.IsNullOrEmpty(collname))
                            collname = Export.GetCollectionName(obj);
                        writer.WriteStartElement(Export.s_ElementReference);
                        writer.WriteAttributeString(Export.s_AttributeTarget, Export.GetGsdID(obj));
                        if (option == C.SerializeOptions.Export)
                            writer.WriteAttributeString(Export.s_AttributeTargetCollection, collname);
                        writer.WriteEndElement();
                    }
                }
            }
            writer.WriteEndElement();
        }

        #region WriteComplexObjectListProperty Helper
        /// <summary>
        /// Returns the correct collection name for the given referenceable GsdObject.
        /// Known referenceable objects are DeviceAccessPoints, Modules, VirtualSubmodules,
        /// AccessPointStructureElements and ModuleStructureElements.
        /// </summary>
        /// <param name="obj">Referenceable GsdObject, for which the collection name
        /// should be returned.</param>
        /// <returns>The correct collection name or an empty string if an unknown
        /// referenceable object is given.</returns>
        private static string GetCollectionName(C.GsdObject obj)
        {
            if (obj is C.DeviceAccessPoint)
                return s_CollNameDeviceAccessPoints;                   // ---------->
            if (obj is C.Module)
                return s_CollNameModules;                              // ---------->
            if (obj is C.VirtualSubmodule)
                return s_CollNameVirtualSubmodules;                    // ---------->

            if (obj is S.AccessPointStructureElement)
                return s_CollNameDeviceAccessPointStructureElements;   // ---------->
            if (obj is S.ModuleStructureElement)
                return s_CollNameModuleStructureElements;              // ---------->
            if (obj is S.SubmoduleStructureElement)
                return s_CollNameSubmoduleStructureElements;

            return String.Empty;
        }

        /// <summary>
		/// Returns the GsdID of the given referenceable GsdObject. Known referenceable
		/// objects are all ModuleObject and StructureElement GsdObjects.
		/// </summary>
		/// <param name="obj">Referenceable GsdObject, for which the GsdID should be 
		/// returned.</param>
		/// <exception cref="ArgumentException">is thrown, in case of an unknown GsdObject</exception>
		/// <returns></returns>
		private static string GetGsdID(C.GsdObject obj)
        {
            if (obj is C.ModuleObject)
                return (obj as C.ModuleObject).GsdID;                     // ---------->

            if (obj is S.StructureElement)
                return (obj as S.StructureElement).GsdID;                 // ---------->

            throw new ArgumentException("Couldn't serialize " + obj.GetType().Name + "here, must be 'ModuleObject...' or 'StructureElement...'!");
        }
        #endregion

        
        /// <summary>
        /// Writes the elements and attributes needed for the export of a dictionary in a property
        /// with GsdObjects as value.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="name">Name of the property to export.</param>
        /// <param name="val">Dictionary list of keys (strings) and values (GsdObjects) of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        /// <exception cref="SerializationException">is thrown, in case of an failed object serialization</exception>
        public static void WriteDictionaryListProperty(C.SerializeOptions option, ref XmlTextWriter writer, string name, IDictionary val)
        {
            #region Argument Preconditions
            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (name == null)
                throw new ArgumentNullException(nameof(name));
            if (name.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(name));
            #endregion

            writer.WriteStartElement(s_ElementProperty);
            writer.WriteAttributeString(s_AttributeName, name);
            writer.WriteAttributeString(s_AttributeType, s_TypeDictionary);
            if (null != val)    // If null write only empty element!
            {
                IDictionaryEnumerator enumerator = val.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    writer.WriteStartElement(s_ElementDictionary);
                    if (enumerator.Key != null)
                    {
                        writer.WriteAttributeString(s_AttributeKey, enumerator.Key.ToString());
                    }

                    // Serialize the found objects.
                    bool succeeded = ((C.GsdObject)(enumerator.Value)).Serialize(option, ref writer);
                    if (!succeeded)
                        throw new SerializationException("Couldn't serialize " + enumerator.Value.GetType().Name + "!");
                    writer.WriteEndElement();
                }
            }
            writer.WriteEndElement();
        }

        #endregion

        #region Collection Writer

        /// <summary>
        /// Writes the elements and attributes needed for the export of a dictionary cache
        /// with GsdObjects as value.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <param name="collname">Name of the collection which should be exported.</param>
        /// <param name="val">Dictionary list of keys (strings) and values (GsdObjects) of the property to export.</param>
        /// <exception cref="ArgumentNullException">if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an empty name value string</exception>
        /// <exception cref="SerializationException">is thrown, in case of an failed object serialization</exception>
        public static void WriteDictionaryList(
            C.SerializeOptions option,
            ref System.Xml.XmlTextWriter writer,
            string collname,
            IDictionary val)
        {
            #region Argument Preconditions

            if (writer == null)
                throw new ArgumentNullException(nameof(writer));
            if (collname == null)
                throw new ArgumentNullException(nameof(collname));
            if (collname.Length == 0) // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(collname));

            #endregion

            if (null == val
                || val.Count == 0) // If null write no empty element!
            {
                return;
            }

            writer.WriteStartElement(Export.s_ElementCollection);
            writer.WriteAttributeString(Export.s_AttributeName, collname);

            IDictionaryEnumerator enumerator = val.GetEnumerator();
            while (enumerator.MoveNext())
            {
                bool succeeded = ((C.GsdObject)(enumerator.Value)).Serialize(option, ref writer);
                if (!succeeded)
                    throw new SerializationException("Couldn't serialize " + enumerator.Value.GetType().Name + "!");
            }

            writer.WriteEndElement();


        }


        #endregion

        #endregion

    }
}


