/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IoConfigDataStruct.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.IODevParamConfig
{
    /// <summary>
    /// The data record object for IoConfigData.
    /// </summary>
    internal class IoConfigDataStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for IoConfigDataStruct.
        /// </summary>
        public IoConfigDataStruct()
        {
            BlockType = DataRecords.BlockTypes.iPNIODIOConfigData;
            BlockLength = 0x0015;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[17];
        }

        /// <summary>
        /// MaxDataLength part of the data record.
        /// </summary>
        public int MaxDataLength
        {
            set { Transformator.Write16(Data, 4, value); }
            get { return Transformator.Read16(Data, 4); }
        }

        /// <summary>
        /// MaxInputLength part of the data record.
        /// </summary>
        public int MaxInputLength
        {
            set { Transformator.Write16(Data, 0, value); }
            get { return Transformator.Read16(Data, 0); }
        }

        /// <summary>
        /// MaxOutputLength part of the data record.
        /// </summary>
        public int MaxOutputLength
        {
            set { Transformator.Write16(Data, 2, value); }
            get { return Transformator.Read16(Data, 2); }
        }

        /// <summary>
        /// NumberOfAdditionalInputCR part of the data record.
        /// </summary>
        public int NumberOfAdditionalInputCR
        {
            set { Transformator.Write16(Data, 8, value); }
            get { return Transformator.Read16(Data, 8); }
        }

        /// <summary>
        /// NumberOfAdditionalMulticastProviderCR part of the data record.
        /// </summary>
        public int NumberOfAdditionalMulticastProviderCR
        {
            set { Transformator.Write16(Data, 12, value); }
            get { return Transformator.Read16(Data, 12); }
        }

        /// <summary>
        /// NumberOfAdditionalOutputCR part of the data record.
        /// </summary>
        public int NumberOfAdditionalOutputCR
        {
            set { Transformator.Write16(Data, 10, value); }
            get { return Transformator.Read16(Data, 10); }
        }

        /// <summary>
        /// NumberOfAR part of the data record.
        /// </summary>
        public int NumberOfAR
        {
            set { Transformator.Write16(Data, 6, value); }
            get { return Transformator.Read16(Data, 6); }
        }

        /// <summary>
        /// NumberOfMulticastConsumerCR part of the data record.
        /// </summary>
        public int NumberOfMulticastConsumerCR
        {
            set { Transformator.Write16(Data, 14, value); }
            get { return Transformator.Read16(Data, 14); }
        }
    }
}