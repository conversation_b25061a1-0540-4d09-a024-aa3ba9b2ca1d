/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNBasePortBL.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Reflection;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using CableLength = PNConfigLib.ConfigReader.Topology.CableLength;
using FiberOpticType = PNConfigLib.ConfigReader.Topology.FiberOpticType;
using TransmissionRate = PNConfigLib.ConfigReader.Configuration.TransmissionRate;

#endregion

namespace PNConfigLib.HWCNBL.Port
{
    internal class PNBasePortBL : HwcnBusinessLogic, IPortBL
    {
        internal PNBasePortBL(DataModel.PCLObjects.Port port)
        {
            Port = port;
            InitBL();
        }

        public DataModel.PCLObjects.Port Port { get; }
        private const int IdPortAutomaticSettings = 8;

        internal static IPortBL PortBLCreator(DataModel.PCLObjects.Port port)
        {
            IPortBL portBL = new PNBasePortBL(port);

            foreach (Type decorator in ((PortCatalog)port.PCLCatalogObject).DecorationList)
            {
                ConstructorInfo constructorInfo = decorator.GetConstructor(new Type[] { typeof(IPortBL) });
                if (constructorInfo != null)
                {
                    portBL = (IPortBL)constructorInfo.Invoke(
                        BindingFlags.Default,
                        null,
                        new object[] { portBL },
                        CultureInfo.InvariantCulture);
                }
            }

            return portBL;
        }

        public void Configure(PortType xmlPort, TopologyTypePortInterconnection xmlPortInterconnection, int numberOfPorts, bool isLocal)
        {
            FillGeneralAttributes(Port, xmlPort.General, string.Format(CultureInfo.InvariantCulture, "Port {0}", xmlPort.PortNumber));
            Port.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PnPortNumber, xmlPort.PortNumber);
            Port.AttributeAccess.SetAnyAttribute<int>(InternalAttributeNames.PositionNumber, xmlPort.PortNumber);

            if (!xmlPort.PortOptions.ActivateThisPortForUse)
            {
                if (PortUtility.GetPortDeactivationCapability(Port))
                {
                    Port.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, true);
                }
                else
                {
                    Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, true);
                }
                if (IsDisabledFieldsConfigured(xmlPort.PortOptions))
                {
                    ConsistencyLogger.Log(ConsistencyType.XML, LogSeverity.Error, Port, ConsistencyConstants.XML_InvalidPortSettingsForDeactivatedPort, PortUtility.GetPortName(Port));
                }
            }
            else
            {
                Port.AttributeAccess.SetAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, false);

                ConfigurePortOptions(xmlPort, numberOfPorts);
                Dictionary<uint, byte[]> parameterRecordDataItems = FillParameterRecordDataItems(Port,
                    xmlPort.ParameterRecordDataItems, 
                    string.Format(CultureInfo.InvariantCulture, "Port number {0}", xmlPort.PortNumber));
                Port.SetParameterRecordDataItems(parameterRecordDataItems);
            }
            ConfigurePortInterconnections(xmlPortInterconnection);
            ConfigureFiberOpticTypes(xmlPortInterconnection, isLocal);

        }

        private void ConfigurePortOptions(PortType xmlPort, int numberOfPorts)
        {
            ConfigureConnection(xmlPort);
            ConfigureBoundaries(xmlPort, numberOfPorts);
        }

        private void ConfigurePortInterconnections(TopologyTypePortInterconnection xmlPortInterconnection)
        {
            if (xmlPortInterconnection == null
                || xmlPortInterconnection.Properties.Item == null)
            {
                CableData cableData = PortUtility.GetDefaultCableDataForPort(Port);
                Port.AttributeAccess.SetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtLineDelaySelection,
                    cableData.LineDelaySelection);
                Port.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnIrtLineLength, cableData.LineLength);
                Port.AttributeAccess.SetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtSignalDelayTime,
                    cableData.SignalDelayTime);
            }
            else
            {
                if (xmlPortInterconnection.Properties.Item != null
                    && xmlPortInterconnection.Properties.Item.GetType() == typeof(CableLength))
                {
                    Port.AttributeAccess.SetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtLineDelaySelection,
                        (uint)CableDataEditMode.LineLength);

                    CableLength cableLength = (CableLength)xmlPortInterconnection.Properties.Item;

                    PNCableLength mappedCableLength = AttributeUtilities.MapCableLength(cableLength);
                    Port.AttributeAccess.SetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtLineLength,
                        (uint)mappedCableLength);
                    Port.AttributeAccess.SetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtSignalDelayTime,
                        (uint)mappedCableLength);
                }
                else
                {
                    Port.AttributeAccess.SetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtLineDelaySelection,
                        (uint)CableDataEditMode.SignalDelayTime);

                    long pnIrtSignalDelayTime;
                    long.TryParse(
                        ((float)xmlPortInterconnection.Properties.Item * 1000).ToString(CultureInfo.InvariantCulture),
                        NumberStyles.Float,
                        CultureInfo.InvariantCulture,
                        out pnIrtSignalDelayTime);

                    Port.AttributeAccess.SetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtSignalDelayTime,
                        (uint)pnIrtSignalDelayTime);
                }
            }
        }
        private void ConfigureConnection(PortType xmlPort)
        {
            ConfigureTransmissionRate(xmlPort);
            Port.AttributeAccess.SetAnyAttribute<bool>(
                InternalAttributeNames.PnLinkStateDiagnosis,
                xmlPort.PortOptions.Monitor);
            // If the device is not fiber optic and the transmission rate is automatic, autonegotiation option cannot be set to false.
            if (xmlPort.PortOptions.TransmissionRate == TransmissionRate.Automatic
                && !xmlPort.PortOptions.EnableAutonegotiation
                && xmlPort.PortOptions.ActivateThisPortForUse
                && !PortUtility.IsFiberOptic(Port))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Port,
                    ConsistencyConstants.PortWrongValueForAutoNegotiation,
                    PortUtility.GetPortName(Port));
            }
            else
            {
                Port.AttributeAccess.SetAnyAttribute<bool>(
                    InternalAttributeNames.PnPortAutoNegotiation,
                    xmlPort.PortOptions.EnableAutonegotiation);
            }
        }
        private void ConfigureBoundaries(PortType xmlPort, int numberOfPorts)
        {
            if (!CheckBoundaries(xmlPort.PortOptions, numberOfPorts))
            {
                return;
            }
            Port.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnDCPBoundary,
                Convert.ToUInt32(xmlPort.PortOptions.EndOfDetectionOfAccessibleDevices, CultureInfo.InvariantCulture));

            Port.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnPTPBoundary,
                Convert.ToUInt32(xmlPort.PortOptions.EndOfTopologyDiscovery, CultureInfo.InvariantCulture));

            Port.AttributeAccess.SetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtPortSyncDomainBoundary,
                Convert.ToUInt32(xmlPort.PortOptions.EndOfTheSyncDomain, CultureInfo.InvariantCulture));
        }
        private bool IsDisabledFieldsConfigured(PortTypePortOptions portOptions)
        {
            return IsConnectionFieldsActivated(portOptions) || IsBoundaryFieldsActivated(portOptions);
        }
        private bool IsConnectionFieldsActivated(PortTypePortOptions portOptions)
        {
            if (portOptions.Monitor
                || !portOptions.EnableAutonegotiation)
            {
                return true;
            }
            return false;
        }
        private bool IsBoundaryFieldsActivated(PortTypePortOptions portOptions)
        {
            if (portOptions.EndOfDetectionOfAccessibleDevices
                || portOptions.EndOfTopologyDiscovery
                || portOptions.EndOfTheSyncDomain)
            {
                return true;
            }
            return false;
        }
        private bool CheckBoundaries(PortTypePortOptions portOptions, int numberOfPorts)
        {
            if (!IsBoundaryFieldsActivated(portOptions))
            {
                return true;
            }
            bool isValid = true;
            // The “boundaries” is enabled if the device port support one possible boundary and the interface support PDEV.
            if (!Utility.IsPdevInterface(NavigationUtilities.GetInterfaceOfPort(Port)))
            {
                ConsistencyLogger.Log(ConsistencyType.XML, LogSeverity.Error, Port, ConsistencyConstants.XML_PortBoundariesSetForNonPDEVInterface, PortUtility.GetPortName(Port));
                isValid = false;
            }
            if (!PortUtility.IsPNDCPBoundarySupported(Port) && portOptions.EndOfDetectionOfAccessibleDevices)
            {
                ConsistencyLogger.Log(ConsistencyType.XML, LogSeverity.Error, Port, ConsistencyConstants.XML_EndOfDetectionOfAccessibleDevicesIsNotValid, PortUtility.GetPortName(Port));
                isValid = false;
            }
            if (!PortUtility.IsPNPTPBoundarySupported(Port) && portOptions.EndOfTopologyDiscovery)
            {
                ConsistencyLogger.Log(ConsistencyType.XML, LogSeverity.Error, Port, ConsistencyConstants.XML_EndOfTopologyDiscoveryIsNotValid, PortUtility.GetPortName(Port));
                isValid = false;
            }
            if (!PortUtility.IsPNIrtPortSyncDomainBoundarySupported(Port, numberOfPorts) && portOptions.EndOfTheSyncDomain)
            {
                ConsistencyLogger.Log(ConsistencyType.XML, LogSeverity.Error, Port, ConsistencyConstants.XML_EndOfTheSyncDomainIsNotValid, PortUtility.GetPortName(Port));
                isValid = false;
            }
            return isValid;
        }

        private void ConfigureFiberOpticTypes(TopologyTypePortInterconnection xmlPortInterconnection, bool isLocal)
        {
            Enumerated supportedTypes = Port.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnFiberOptic,
                new AttributeAccessCode(),
                null);

            if ((supportedTypes != null)
                && (supportedTypes.List.Count > 0))
            {
                Port.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnFiberOptic); // Remove the enumerated type

                long defaultType = (long)supportedTypes.DefaultValue;
                if (xmlPortInterconnection != null)
                {
                    FiberOpticType specifiedCableType;
                    if (isLocal)
                    {
                        specifiedCableType = xmlPortInterconnection.LocalPort.CableType;
                    }
                    else
                    {
                        specifiedCableType = xmlPortInterconnection.PartnerPort.CableType;
                    }
                    // Fiber optic is supported
                    if (specifiedCableType == FiberOpticType.NotSpecified)
                    {
                        // Type is not specified, used the first supported type.
                        Port.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnFiberOptic, defaultType);
                    }
                    else
                    {
                        long mappedType = AttributeUtilities.MapFiberOpticType(specifiedCableType);
                        Port.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnFiberOptic, mappedType);
                    }
                }
                else
                {
                    // Type is not specified, used the first supported type.
                    Port.AttributeAccess.AddAnyAttribute<long>(InternalAttributeNames.PnFiberOptic, defaultType);
                }
            }
        }

        private void ConfigureTransmissionRate(PortType xmlPort)
        {
            TransmissionRate transmissionRate = xmlPort.PortOptions.TransmissionRate;
            PNTransmissionRate mappedTransmissionRate = AttributeUtilities.MapTransmissionRateEnum(transmissionRate);
            AttributeAccessCode ac = new AttributeAccessCode();
            Enumerated pnEthernetMediumDuplexSupportedValues =
                Port.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnEthernetMediumDuplex,
                    ac,
                    null);
            if (ac.IsOkay)
            {
                if (pnEthernetMediumDuplexSupportedValues.List.Contains((uint)mappedTransmissionRate))
                {
                    Port.AttributeAccess.RemoveAttribute(InternalAttributeNames.PnEthernetMediumDuplex);
                    Port.AttributeAccess.AddAnyAttribute<uint>(
                        InternalAttributeNames.PnEthernetMediumDuplex,
                        (uint)mappedTransmissionRate);
                }
                else
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        Port,
                        ConsistencyConstants.PortTransferRateNotSupported,
                        PortUtility.GetPortName(Port));
                }
            }
        }

        public void InitBL()
        {
            InitAttributes();
            AddAttributesForAlternativePortInterconnnection(Port);
            InitActions();
        }

        #region BaseAction callbacks

        private void AddAttributesForAlternativePortInterconnnection(DataModel.PCLObjects.Port port)
        {
            AttributeAccessCode ac1 = new AttributeAccessCode();
            port.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsMultipleConnectionEnabled, ac1, false);
            if (!ac1.IsOkay)
            {
                port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIsMultipleConnectionEnabled, false);
            }
        }

        #endregion

        #region PortInterconnection

        /// <summary>
        /// Function creates an undo capable Port interconnection between the current port
        /// and the port given in argument if this interconnection is possible.
        /// a checkonly parameter can steer if only the a check should be made
        /// whether the given interconnection is posssible.
        /// </summary>
        private void GenericMethodCreatePortInterconnection(IMethodData data)
        {
            object tmp = data.Arguments[CreatePortInterconnection.PartnerPort];
            DataModel.PCLObjects.Port partnerPort = tmp != null ? tmp as DataModel.PCLObjects.Port : null;
            if (partnerPort != null)
            {
                Port.SetPartnerPort(partnerPort);

                // deactivate the "end of topology discovery", because the ports are interconnected
                Port.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnPTPBoundary, 0);
                partnerPort.AttributeAccess.SetAnyAttribute<uint>(InternalAttributeNames.PnPTPBoundary, 0);

                data.Arguments[CreatePortInterconnection.RetValue] = true;
                data.ReturnValue = true;
            }
        }

        // Contains the private implementation of the class

        #endregion

        #region DataBlock

        /// <summary>
        /// Function creates the Port Parameterblocks
        /// </summary>
        private void GenericMethodGetPortParameterBlocks(IMethodData data)
        {
            if (data == null)
            {
                return;
            }
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(Port);
            if (data.Arguments["central"] != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                if ((Port != null)
                    && interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPdevParametrizationDecentral,
                        ac,
                        false))
                {
                    return;
                }
            }

            try
            {
                // init return value
                data.ReturnValue = false;

                Debug.Assert(
                    data.Arguments[GetPortParameterBlocks.SelectedDataset] != null,
                    "One of the Port Dataset must be set to be generated!");

                int dataset = Convert.ToInt32(
                    data.Arguments[GetPortParameterBlocks.SelectedDataset],
                    CultureInfo.InvariantCulture);

                PNInterfaceType interfaceType = (PNInterfaceType)Convert.ToInt32(
                    data.Arguments[GetPortParameterBlocks.InterfaceType],
                    CultureInfo.InvariantCulture);

                switch (dataset)
                {
                    case 0x802F:
                        CompileUtility.GetPDPortDataAdjust(
                            data,
                            Port,
                            interfaceType);
                        break;

                    case 0x802B:
                        CompileUtility.GetPDPortDataCheck(
                            data,
                            Port,
                            interfaceSubmodule,
                            interfaceType);
                        break;

                    case 0x8062:
                        CompileUtility.GetPDPortFoDataAdjust(data, Port, interfaceSubmodule);
                        break;

                    case 0x8061:
                        CompileUtility.GetPDPortFoDataCheck(data, Port);
                        break;

                    default:
                        Debug.Assert(
                            data.Arguments[GetPortParameterBlocks.SelectedDataset] != null,
                            "One of the Port Dataset must be set to be generated!");
                        break;
                }
            }
            catch (Exception e)
            {
                throw new PNFunctionsException("GenericMethodGetPortParameterBlocks", e);
            }
        }

        #endregion

        #region Overrides and Overridables

        private void InitAttributes()
        {
            Port.AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PnPortNumber, (int)Port.PortNumber);

            Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnLinkStateDiagnosis, false);

                Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPortAutoNegotiation, true);

            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtPortSyncDomainBoundary, 0);

            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnDCPBoundary, 0);

            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnPTPBoundary, 0);

            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtLineDelaySelection, 0);
            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtLineLength, 600);
            Port.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtSignalDelayTime, 600);

            Port.AttributeAccess.AddAnyAttribute<UInt32>(InternalAttributeNames.SharedIoAssignment,
                (UInt32)SharedIoAssignment.None);

            bool pnPortDeactivationSupported = Port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPortDeactivationSupported,
                new AttributeAccessCode(),
                false);

            if (pnPortDeactivationSupported)
            {
                Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnPortDeactivated, false);
            }
        }

        private void InitActions()
        {
            Port.BaseActions.RegisterMethod(GetPortParameterBlocks.Name, GenericMethodGetPortParameterBlocks);
            Port.BaseActions.RegisterMethod(CreatePortInterconnection.Name, GenericMethodCreatePortInterconnection);
            ConsistencyManager.RegisterConsistencyCheck(Port, CheckConsistency);
        }

        #endregion

        #region Consistency Checks

        private void CheckConsistency()
        {
            ConsistencyCheck_PortDeactivation();

            ConsistencyCheck_PortInterconnection_MediumDifferent();
            ConsistencyCheck_PortOptions_PartnerDeactivated();
            ConsistencyCheck_PortOptions_TransferRateDifferent();
            ConsistencyCheck_PortOptions_DifferentAutonegotiation();
        }

        private void ConsistencyCheck_PortDeactivation()
        {
            bool isDeactivationSupported = Port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPortDeactivationSupported,
                new AttributeAccessCode(),
                false);

            bool isPortDeactivated = Port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPortDeactivated,
                new AttributeAccessCode(),
                false);

            if (isPortDeactivated && !isDeactivationSupported)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.PortDeactivationNotValid,
                    PortUtility.GetPortName(Port));
            }
        }

        private void ConsistencyCheck_PortInterconnection_MediumDifferent()
        {
            IList<DataModel.PCLObjects.Port> partnerPorts = Port.GetPartnerPorts();
            if (partnerPorts == null)
            {
                return;
            }

            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                if (PortUtility.CheckMediumIdentity(Port, partnerPort))
                {
                    continue;
                }
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, Port, ConsistencyConstants.PortMediumDifferent,
                    PortUtility.GetPortName(Port),
                    PortUtility.GetPortName(partnerPort));
            }
        }

        private void ConsistencyCheck_PortOptions_PartnerDeactivated()
        {
            IList<DataModel.PCLObjects.Port> partnerPorts = Port.GetPartnerPorts();
            if ((partnerPorts == null)
                || !partnerPorts.Any())
            {
                return;
            }

            bool portDeactivated = Port.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnPortDeactivated,
                new AttributeAccessCode(),
                false);

            if (!portDeactivated)
            {
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Warning, Port, ConsistencyConstants.InterconnectedPortDeactivated,
                    AttributeUtilities.GetName(Port));
        }

        private void ConsistencyCheck_PortOptions_TransferRateDifferent()
        {
            //if port is deactivated, we don't have to check the partnerport's transferrate, an other cosistency check does this
            IList<DataModel.PCLObjects.Port> partnerPorts = Port.GetPartnerPorts();

            AttributeAccessCode accessCode = new AttributeAccessCode();
            bool portDeactivated = PortUtility.IsPortDeactivated(Port);

            if ((partnerPorts == null) || portDeactivated)
            {
                return;
            }

            Interface currInterface = Port.GetInterface();
            bool pdevModelSupported =
                currInterface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPdevModelSupportedLocal,
                    accessCode.GetNew(),
                    true);

            if (!pdevModelSupported)
            {
                return;
            }

            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                //if a partnerport is deactivated, we don't have carry about it, an other cosistency check does this
                if (PortUtility.IsPortDeactivated(partnerPort))
                {
                    continue;
                }

                Interface partnerInterface = partnerPort.GetInterface();

                if (partnerInterface == null)
                {
                    continue;
                }

                pdevModelSupported =
                    currInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPdevModelSupportedLocal,
                        accessCode.GetNew(),
                        true);

                if (!pdevModelSupported)
                {
                    continue;
                }

                uint localPortMauType =
                    Port.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnEthernetMediumDuplex,
                        new AttributeAccessCode(),
                        0);

                uint partnerPortMauType =
                    partnerPort.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnEthernetMediumDuplex,
                        new AttributeAccessCode(),
                        0);

                //a difference exists in the mautypes,
                if (localPortMauType == partnerPortMauType)
                {
                    continue;
                }

                //we have to check if we already reported the problem on the partnerport
                List<ConsistencyLog> currentLogs = ConsistencyLogger.ConsistencyLogs;
                string possibleConsistencyMessage = ConsistencyLogger.RetrieveConsistencyMessage(ConsistencyConstants.PortTransferRateIsDifferent, PortUtility.GetPortName(Port),
                             PortUtility.GetPortName(partnerPort));
                string possibleConsistencyMessage_2 = ConsistencyLogger.RetrieveConsistencyMessage(ConsistencyConstants.PortTransferRateIsDifferent, PortUtility.GetPortName(partnerPort),
                           PortUtility.GetPortName(Port));
                if (currentLogs.Any(x => x.Message.Contains(possibleConsistencyMessage) || x.Message.Contains(possibleConsistencyMessage_2)))
                {                   
                    continue;
                }
                             

                if (localPortMauType != IdPortAutomaticSettings &&
                       partnerPortMauType != IdPortAutomaticSettings)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.PortTransferRateIsDifferent,
                            PortUtility.GetPortName(Port),
                            PortUtility.GetPortName(partnerPort));
                }

                else
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.PortTransferRateIsDifferentError,
                           PortUtility.GetPortName(Port),
                           PortUtility.GetPortName(partnerPort));
                }
            }
        }

        private void ConsistencyCheck_PortOptions_DifferentAutonegotiation()
        {
            IList<DataModel.PCLObjects.Port> partnerPorts = Port.GetPartnerPorts();
            if ((partnerPorts == null)
                || !partnerPorts.Any()
                || PortUtility.IsPortDeactivated(Port))
            {
                return;
            }

            Interface currInterface = Port.GetInterface();

            if (!Utility.IsPdevInterface(currInterface))
            {
                return;
            }

            //if the port is fiber optic, it can be connected only to fiber optic port, and at FO autonegotiation is always false   

            if (PortUtility.GetMediumTypeOfPort(Port) != PortUtility.MediumType.Cu)
            {
                return;
            }

            foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
            {
                if (PortUtility.IsPortDeactivated(partnerPort))
                {
                    continue;
                }

                Interface partnerInterface = partnerPort.GetInterface();

                if ((partnerInterface == null)
                    || !Utility.IsPdevInterface(partnerInterface)
                    || !Utility.IsPdevInterface(partnerInterface))
                {
                    // if the partner is located in a different subnet, we could not use cache, we dont generate error, an other cons.check will be generated
                    // (error: ports are interconnected, but located in different subnet)
                    continue;
                }

                AttributeAccessCode accessCode = new AttributeAccessCode();

                bool pnLocalPortAutoNegotiation =
                    Port.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPortAutoNegotiation,
                        accessCode,
                        false);

                bool pnPartnerPortAutoNegotiation =
                    partnerPort.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnPortAutoNegotiation,
                        accessCode,
                        false);

                if (pnLocalPortAutoNegotiation != pnPartnerPortAutoNegotiation)
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.PortDifferentAutoNegotiation,
                            AttributeUtilities.GetName(Port),
                            AttributeUtilities.GetName(partnerPort));
                }
            }
        }

        #endregion
    }
}