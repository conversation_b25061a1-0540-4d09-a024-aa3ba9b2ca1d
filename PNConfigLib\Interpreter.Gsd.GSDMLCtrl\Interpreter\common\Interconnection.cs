/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Interconnection.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The Interconnection object defines the timing behavior for 
    /// sending cyclic IO data.
    /// </summary>
    //[ComVisible(true), Guid("38B6CEC9-BDC1-4f05-9BC2-33992F83BC0F")] 
    public class Interconnection : 
		GsdObject,
		IInterconnection
	{
		//########################################################################################
		#region Initialization & Termination

		/// <summary>
		/// Initializes the Interconnection if it is instantiated.
		/// The properties of the object are all initialized to empty 
		/// or with abstract default values.
		/// </summary>
		/// <remarks>The real content can only be written to the object by the Fill
		/// method, which is only available within this assembly.</remarks>
        public Interconnection()
		{
			//m_SendClock = null;
			//m_ReductionRatio = null;
		}

		#endregion

		//########################################################################################
		#region Fields

		// Declaration of the properties
        private ArrayList m_SupportedMRP_InterconnRole;
        private uint m_MaxMRP_InterconnInstances;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses a list of SupportedMRP_InterconnRoles.
        /// </summary>
        /// <remarks></remarks>
        public virtual Array SupportedMRP_InterconnRole =>
            (null != this.m_SupportedMRP_InterconnRole) ?
                new ArrayList(this.m_SupportedMRP_InterconnRole).ToArray() :
                null;

        /// <summary>
        /// ...
        /// </summary>
        public UInt32 MaxMRP_InterconnInstances => this.m_MaxMRP_InterconnInstances;

        #region COM Interface Members Only

        // ONLY for the COM interface.
        //object GSDI.IInterconnection.MaxMRP_InterconnInstances
        //{
        //    get { return this.MaxMRP_InterconnInstances; }
        //}

        #endregion

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter.
				if (null == hash)
					throw new FillException("The input hash table parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldSupportedMrpInterconnRole;
                if (hash.ContainsKey(member) && hash[member] is ArrayList)
                    this.m_SupportedMRP_InterconnRole = hash[member] as ArrayList;

                member = Models.s_FieldMaxMrpInterconnInstances;
                if (hash.ContainsKey(member) && hash[member] is uint)
                    m_MaxMRP_InterconnInstances = (uint)hash[member];


                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectInterconnection);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successful, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// ----------------------------------------------
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSupportedMrpInterconnRole, this.m_SupportedMRP_InterconnRole, true);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxMrpInterconnInstances, this.m_MaxMRP_InterconnInstances);

			return true; 
		}


		#endregion

	}
}
