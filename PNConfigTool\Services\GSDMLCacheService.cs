using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using PNConfigLib.Gsd.Interpreter;
using GSDI;

namespace PNConfigTool.Services
{
    /// <summary>
    /// GSDML解析缓存条目
    /// </summary>
    public class GSDMLCacheEntry
    {
        public PNConfigLib.Gsd.Interpreter.Interpreter Interpreter { get; set; }
        public DateTime LastModified { get; set; }
        public string FilePath { get; set; }
        public bool IsLoaded { get; set; }
        public GSDI.ModelOptions LoadedOptions { get; set; }
        
        public GSDMLCacheEntry(string filePath)
        {
            FilePath = filePath;
            Interpreter = new PNConfigLib.Gsd.Interpreter.Interpreter();
            LastModified = File.GetLastWriteTime(filePath);
            IsLoaded = false;
            LoadedOptions = GSDI.ModelOptions.GSDStructure;
        }
        
        public bool IsValid()
        {
            if (!File.Exists(FilePath))
                return false;
                
            var currentModified = File.GetLastWriteTime(FilePath);
            return currentModified == LastModified && IsLoaded;
        }
        
        public bool LoadGSDML(GSDI.ModelOptions options = GSDI.ModelOptions.GSDStructure)
        {
            try
            {
                // 检查是否需要重新加载
                bool needReload = !IsLoaded || !IsValid();

                // 检查ModelOptions兼容性
                if (IsLoaded && IsValid())
                {
                    // ModelOptions不是标志枚举，需要检查兼容性
                    bool isCompatible = false;

                    if (LoadedOptions == options)
                    {
                        // 完全匹配
                        isCompatible = true;
                        Debug.WriteLine($"缓存选项完全匹配: {LoadedOptions}");
                    }
                    else if (LoadedOptions == GSDI.ModelOptions.GSDCommonAndStructure)
                    {
                        // GSDCommonAndStructure包含所有其他选项
                        isCompatible = (options == GSDI.ModelOptions.GSDCommon ||
                                      options == GSDI.ModelOptions.GSDStructure ||
                                      options == GSDI.ModelOptions.GSDCommonAndStructure);
                        Debug.WriteLine($"缓存为GSDCommonAndStructure，请求{options}，兼容性: {isCompatible}");
                    }
                    else if (options == GSDI.ModelOptions.GSDCommonAndStructure)
                    {
                        // 请求GSDCommonAndStructure，需要重新加载
                        needReload = true;
                        Debug.WriteLine($"请求GSDCommonAndStructure，当前缓存{LoadedOptions}，需要重新加载");
                    }
                    else
                    {
                        // 其他情况不兼容，需要重新加载
                        needReload = true;
                        Debug.WriteLine($"缓存选项{LoadedOptions}与请求选项{options}不兼容，需要重新加载");
                    }

                    if (isCompatible)
                    {
                        return true;
                    }
                }

                if (needReload)
                {
                    Debug.WriteLine($"重新加载GSDML文件，选项: {options}");
                    bool loadResult = Interpreter.AssignGsd(FilePath, options, "en", false);
                    if (loadResult)
                    {
                        IsLoaded = true;
                        LastModified = File.GetLastWriteTime(FilePath);
                        LoadedOptions = options;
                        Debug.WriteLine($"GSDML文件加载成功，已加载选项: {LoadedOptions}");
                    }
                    else
                    {
                        Debug.WriteLine($"GSDML文件加载失败: {FilePath}");
                    }
                    return loadResult;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载GSDML文件到缓存失败: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                IsLoaded = false;
                return false;
            }
        }
    }

    /// <summary>
    /// GSDML解析缓存服务 - 全局共享的缓存管理器
    /// </summary>
    public static class GSDMLCacheService
    {
        private static readonly Dictionary<string, GSDMLCacheEntry> _gsdmlCache = new Dictionary<string, GSDMLCacheEntry>();
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 获取缓存的GSDML Interpreter实例，如果不存在则创建并缓存
        /// </summary>
        public static PNConfigLib.Gsd.Interpreter.Interpreter? GetCachedInterpreter(string gsdPath, GSDI.ModelOptions options = GSDI.ModelOptions.GSDStructure)
        {
            if (string.IsNullOrEmpty(gsdPath) || !File.Exists(gsdPath))
            {
                Debug.WriteLine($"GSDML文件不存在: {gsdPath}");
                return null;
            }

            lock (_cacheLock)
            {
                if (_gsdmlCache.TryGetValue(gsdPath, out var cacheEntry))
                {
                    Debug.WriteLine($"找到GSDML缓存条目: {gsdPath}");
                    Debug.WriteLine($"缓存状态 - IsValid: {cacheEntry.IsValid()}, LoadedOptions: {cacheEntry.LoadedOptions}, 请求Options: {options}");

                    if (cacheEntry.LoadGSDML(options))
                    {
                        Debug.WriteLine($"使用缓存的GSDML解析器: {gsdPath}");
                        return cacheEntry.Interpreter;
                    }
                    else
                    {
                        Debug.WriteLine($"GSDML缓存加载失败，移除缓存条目: {gsdPath}");
                        _gsdmlCache.Remove(gsdPath);
                        return null;
                    }
                }
                else
                {
                    Debug.WriteLine($"创建新的GSDML缓存条目: {gsdPath}");
                    var newCacheEntry = new GSDMLCacheEntry(gsdPath);
                    if (newCacheEntry.LoadGSDML(options))
                    {
                        _gsdmlCache[gsdPath] = newCacheEntry;
                        Debug.WriteLine($"新缓存条目创建成功: {gsdPath}");
                        return newCacheEntry.Interpreter;
                    }
                    else
                    {
                        Debug.WriteLine($"无法加载GSDML文件: {gsdPath}");
                        return null;
                    }
                }
            }
        }

        /// <summary>
        /// 清理GSDML缓存
        /// </summary>
        public static void ClearCache()
        {
            lock (_cacheLock)
            {
                Debug.WriteLine($"清理GSDML缓存，共 {_gsdmlCache.Count} 个条目");
                _gsdmlCache.Clear();
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public static (int Count, long MemoryUsageEstimate) GetCacheStats()
        {
            lock (_cacheLock)
            {
                long memoryEstimate = _gsdmlCache.Count * 1024 * 1024;
                return (_gsdmlCache.Count, memoryEstimate);
            }
        }
    }
}
