/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: CompileOutputSerializer.cs                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Xml.Linq;

using PNConfigLib.Compiler.DataTypes;
using PNConfigLib.Compiler.DataTypes.Interfaces;
using PNConfigLib.Compiler.DataTypes.Variable;

#endregion

namespace PNConfigLib.Compiler
{
    internal static class CompileOutputSerializer
    {
        private const string s_NodeVariable = "Variable";

        private const string s_NodeVariableAID = "AID";

        private const string s_NodeVariableKey = "Key";

        private const string s_NodeVariableLength = "Length";

        private const string s_NodeVariableLink = "Link";

        private const string s_NodeVariableName = "Name";

        private const string s_NodeVariableRID = "RID";

        private const string s_NodeVariableTargetRID = "TargetRID";

        private const string s_NodeVariableValue = "Value";

        private const string s_NodeVariableValueDataType = "Datatype";

        private const string s_NodeVariableValueElement = "Element";

        private const string s_NodeVariableValueField = "Field";

        private const string s_NodeVariableValueType = "Valuetype";

        private static XDocument s_Doc;

        internal static Dictionary<string, XDocument> Serialize(List<HwConfiguration> ioSystems)
        {
            Dictionary<string, XDocument> retval = new Dictionary<string, XDocument>();
            if (ioSystems != null)
            {
                foreach (HwConfiguration ioSystem in ioSystems)
                {
                    s_Doc = new XDocument();
                    FillOutputXml(ioSystem);

                    if (ioSystem.Controller != null
                        && ioSystem.Controller.CentralDevice != null)
                    {
                        retval.Add(ioSystem.Controller.CentralDevice.Id, s_Doc);
                    }
                }
            }
            return retval;
        }

        private static XElement CreateXElement(
            XElement parent,
            string Tag,
            object TagValue = null,
            Dictionary<string, object> attributeLookup = null)
        {
            XElement retval = new XElement(Tag);
            if (attributeLookup != null)
            {
                foreach (string key in attributeLookup.Keys.ToList())
                {
                    Type enumType = typeof(CompilerConstants.AttributeId);
                    if (attributeLookup[key].GetType() == enumType)
                    {
                        MemberInfo[] memberInfos = enumType.GetMember(attributeLookup[key].ToString());
                        MemberInfo enumValueMemberInfo = memberInfos.FirstOrDefault(m => m.DeclaringType == enumType);
                        object [] valueAttributes =
                            enumValueMemberInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
                        if (valueAttributes.Length != 0)
                        {
                            string attributeName = ((DescriptionAttribute)valueAttributes[0]).Description;
                            attributeLookup[key] = attributeName;
                        }
                    }
                }
                attributeLookup.ToList().ForEach(x => retval.SetAttributeValue(x.Key, x.Value));
            }

            if (TagValue != null)
            {
                if (TagValue is byte[])
                {
                    retval.Value = BitConverter.ToString((byte[])TagValue).Replace("-", string.Empty);
                }
                else
                {
                    retval.Value = TagValue is bool
                                       ? TagValue.ToString().ToLowerInvariant()
                                       : TagValue.ToString();
                }
            }
            if (parent == null)
            {
                s_Doc.Add(retval);
            }
            else
            {
                parent.Add(retval);
            }
            return retval;
        }

        private static void FillOutputXml(object cfg, XElement parentXE = null)
        {
            Dictionary<string, object> tagAttributesLookup = new Dictionary<string, object>();
            string xElementTag = string.Empty;
            string tagValue = null;
            Dictionary<SerializationPropertiesAttribute, object> spAttrLookup =
                new Dictionary<SerializationPropertiesAttribute, object>();
            Dictionary<SerializationPropertiesAttribute, object> parentSpAttrLookup =
                new Dictionary<SerializationPropertiesAttribute, object>();
            List<IVariable> variables = new List<IVariable>();

            Type memberInfo = cfg.GetType().BaseType;
            if (memberInfo != null)
            {
                Attribute[] customAttributes = Attribute.GetCustomAttributes(memberInfo);
                if (customAttributes.Any())
                {
                    Attribute spAttribute = customAttributes.ToList().First(x => x is SerializationPropertiesAttribute);
                    if (spAttribute != null)
                    {
                        xElementTag = ((SerializationPropertiesAttribute)spAttribute).Tag;
                    }
                }
            }

            PropertyInfo[] props = cfg.GetType().GetProperties();
            foreach (PropertyInfo prop in props)
            {
                object[] attrs = prop.GetCustomAttributes(true);
                if ((attrs != null)
                    && attrs.Any())
                {
                    object tagAttribute = attrs.ToList().FirstOrDefault(x => x is TagAttributeAttribute);
                    if (tagAttribute != null)
                    {
                        tagAttributesLookup.Add(
                            ((TagAttributeAttribute)tagAttribute).TagAttributeName,
                            prop.GetValue(cfg, BindingFlags.Default, null, null, CultureInfo.InvariantCulture)
                                .ToString());
                    }
                    object tempTagValue = attrs.ToList().FirstOrDefault(x => x is TagValueAttribute);
                    if (tempTagValue != null)
                    {
                        object propValue = prop.GetValue(
                            cfg,
                            BindingFlags.Default,
                            null,
                            null,
                            CultureInfo.InvariantCulture);

                        if (propValue != null)
                        {
                            tagValue = propValue.ToString();
                        }
                    }
                    SerializationPropertiesAttribute spAttr =
                        attrs.ToList()
                                .FirstOrDefault(x => x is SerializationPropertiesAttribute) as
                            SerializationPropertiesAttribute;
                    if (spAttr != null)
                    {
                        if (spAttr.IsParent)
                        {
                            parentSpAttrLookup.Add(
                                spAttr,
                                prop.GetValue(cfg, BindingFlags.Default, null, null, CultureInfo.InvariantCulture));
                        }
                        else
                        {
                            object propValueObj = prop.GetValue(
                                cfg,
                                BindingFlags.Default,
                                null,
                                null,
                                CultureInfo.InvariantCulture);

                            string propValue = propValueObj.GetType().IsEnum
                                                   ? ((int)propValueObj).ToString(CultureInfo.InvariantCulture)
                                                   : propValueObj.ToString();
                            spAttrLookup.Add(spAttr, propValue);
                        }
                    }
                    VariableAttribute variableAttribute =
                        attrs.ToList().FirstOrDefault(x => x is VariableAttribute) as VariableAttribute;
                    if (variableAttribute != null)
                    {
                        variables.AddRange(
                            (List<IVariable>)prop.GetValue(
                                cfg,
                                BindingFlags.Default,
                                null,
                                null,
                                CultureInfo.InvariantCulture));
                    }
                }
            }

            XElement parent = CreateXElement(parentXE, xElementTag, tagValue, tagAttributesLookup);
            if (variables.Any(v => v is Rid))
            {
                Rid ridVariable = variables.FirstOrDefault(v => v is Rid) as Rid;
                if (ridVariable != null)
                {
                    CreateXElement(parent, s_NodeVariableRID, ridVariable.Value);
                }
            }
            if (spAttrLookup.Count > 0)
            {
                spAttrLookup.Keys.ToList()
                    .ForEach(spa => CreateXElement(parent, spa.Tag, spAttrLookup[spa].ToString()));
            }
            if (variables.Count > 0)
            {
                ProcessNodeVariables(variables.Where(x => !(x is Rid)).ToList(), parent);
            }
            if (parentSpAttrLookup.Count > 0)
            {
                foreach (SerializationPropertiesAttribute spAttr in parentSpAttrLookup.Keys)
                {
                    if (parentSpAttrLookup[spAttr].GetType().IsGenericType
                        && parentSpAttrLookup[spAttr].GetType().GetGenericTypeDefinition()
                            .IsAssignableFrom(typeof(List<>)))
                    {
                        foreach (object attrObj in (IEnumerable<object>)parentSpAttrLookup[spAttr])
                        {
                            FillOutputXml(attrObj, parent);
                        }
                    }
                    else
                    {
                        FillOutputXml(parentSpAttrLookup[spAttr], parent);
                    }
                }
            }
        }

        private static void ProcessNodeVariables(List<IVariable> variables, XElement parentXE)
        {
            XElement variableParentElement;
            foreach (IVariable variable in variables)
            {
                if (variable.GetType() == typeof(Key))
                {
                    Key keyVariable = variable as Key;
                    if (keyVariable != null)
                    {
                        CreateXElement(
                            parentXE,
                            s_NodeVariableKey,
                            keyVariable.Value,
                            new Dictionary<string, object> { { s_NodeVariableAID, keyVariable.KeyAid } });
                    }
                }
                else if (variable.GetType() == typeof(Link))
                {
                    Link linkVariable = (Link)variable;
                    XElement linkXe = CreateXElement(parentXE, s_NodeVariableLink);
                    CreateXElement(linkXe, s_NodeVariableAID, (int)linkVariable.AttributeId);
                    CreateXElement(linkXe, s_NodeVariableTargetRID, linkVariable.TargetRID);
                }
                else
                {
                    variableParentElement = CreateXElement(
                        parentXE,
                        s_NodeVariable,
                        null,
                        new Dictionary<string, object> { { s_NodeVariableName, variable.AttributeId } });

                    CreateXElement(variableParentElement, s_NodeVariableAID, (int)variable.AttributeId);
                    if (variable.GetType() == typeof(CompositeVariable))
                    {
                        CompositeVariable compositeVariable = variable as CompositeVariable;
                        if (compositeVariable != null)
                        {
                            Dictionary<string, object> attributeLookup =
                                new Dictionary<string, object>
                                    {
                                        {
                                            s_NodeVariableValueDataType,
                                            compositeVariable.DataType
                                        },
                                        {
                                            s_NodeVariableValueType,
                                            compositeVariable.ValueType
                                        }
                                    };

                            if ((compositeVariable.AttributeId == CompilerConstants.AttributeId.DataRecordsTransferSequence)
                                || (compositeVariable.Length != 0))
                            {
                                attributeLookup.Add(s_NodeVariableLength, compositeVariable.Length);
                            }

                            variableParentElement = CreateXElement(
                                variableParentElement,
                                s_NodeVariableValue,
                                compositeVariable.Value,
                                attributeLookup);

                            string compositeVariableValue;
                            foreach (Field variableField in compositeVariable.Fields)
                            {
                                if ((variableField.Value != null)
                                    && (variableField.Length > 0))
                                {
                                    if (compositeVariable.HasHexadecimalValues)
                                    {
                                        compositeVariableValue =
                                            BitConverter.ToString(variableField.Value).Replace("-", string.Empty);
                                    }
                                    else
                                    {
                                        StringBuilder sb = new StringBuilder();
                                        variableField.Value.ToList().ForEach(b => sb.Append(b));
                                        compositeVariableValue = sb.ToString();
                                    }
                                    CreateXElement(
                                        variableParentElement,
                                        s_NodeVariableValueField,
                                        compositeVariableValue,
                                        new Dictionary<string, object>
                                            {
                                                {
                                                    nameof(variableField.Key),
                                                    variableField.Key
                                                },
                                                {
                                                    nameof(variableField.Length),
                                                    variableField.Length
                                                }
                                            });
                                }
                            }

                            foreach (Element variableElement in compositeVariable.Elements)
                            {
                                if (variableElement.Value != null)
                                {
                                    CreateXElement(
                                        variableParentElement,
                                        s_NodeVariableValueElement,
                                        variableElement.Value,
                                        new Dictionary<string, object>
                                            {
                                                {
                                                    s_NodeVariableAID,
                                                    (int)variableElement.AttributeId
                                                },
                                                {
                                                    s_NodeVariableValueDataType,
                                                    variableElement.DataType
                                                },
                                                {
                                                    s_NodeVariableValueType,
                                                    variableElement.ValueType
                                                }
                                            });
                                }
                            }
                        }
                    }
                    else
                    {
                        PropertyInfo[] variableProperities = variable.GetType().GetProperties();
                        if (variableProperities != null)
                        {
                            PropertyInfo firstOrDefault = variableProperities.ToList()
                                .FirstOrDefault(x => x.Name == s_NodeVariableValue);
                            if (firstOrDefault != null)
                            {
                                object variableValue = firstOrDefault.GetValue(
                                    variable,
                                    BindingFlags.Default,
                                    null,
                                    null,
                                    CultureInfo.InvariantCulture);
                                CreateXElement(
                                    variableParentElement,
                                    s_NodeVariableValue,
                                    variableValue,
                                    new Dictionary<string, object>
                                        {
                                            {
                                                s_NodeVariableValueDataType,
                                                variable.DataType.ToString()
                                            },
                                            {
                                                s_NodeVariableValueType,
                                                variable.ValueType.ToString()
                                            }
                                        });
                            }
                        }
                    }
                }
            }
        }
    }
}