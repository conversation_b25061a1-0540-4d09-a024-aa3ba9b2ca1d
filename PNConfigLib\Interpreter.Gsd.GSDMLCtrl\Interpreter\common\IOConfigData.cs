/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: IOConfigData.cs                           :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The IOConfigData object contains information about the quantity of 
    /// input and output (IO) data.
    /// </summary>
    public class IOConfigData :
        GsdObject,
        GSDI.IIOConfigData,
        GSDI.IIOConfigData2
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the IOConfigData if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public IOConfigData()
        {
            m_MaxInputLength = 0;
            m_MaxOutputLength = 0;
            m_MaxDataLength = 0;
            m_MaxTotalDataLength = 0;
            m_MaxApplicationInputLength = 0;
            m_MaxApplicationOutputLength = 0;
            m_MaxApplicationDataLength = 0;
            m_IsMaxApplicationInputLength = false;
            m_IsMaxApplicationOutputLength = false;
            m_IsMaxApplicationDataLength = false;

            // GSDML V2.34
            m_ApplicationLengthIncludesIOxS = false;

        }

        #endregion

        //########################################################################################
        #region Fields

        private uint m_MaxInputLength;
        private uint m_MaxOutputLength;
        private uint m_MaxDataLength;
        private uint m_MaxTotalDataLength;

        private uint m_MaxApplicationInputLength;
        private uint m_MaxApplicationOutputLength;
        private uint m_MaxApplicationDataLength;
        private bool m_IsMaxApplicationInputLength;
        private bool m_IsMaxApplicationOutputLength;
        private bool m_IsMaxApplicationDataLength;

        // GSDML V2.34
        private bool m_ApplicationLengthIncludesIOxS;

        // GSDML V2.42
        private uint m_MaxApplicationARs;
        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the maximum length of the data in octets which can be 
        /// transferred from the IO Device to the IO Controller. 
        /// </summary>
        /// <remarks>This length is defined by the sum of the input data of 
        /// all used submodules, the corresponding IO producer status and the 
        /// IO consumer status of the used output submodules.</remarks>
        public System.UInt32 MaxInputLength => this.m_MaxInputLength;

        /// <summary>
        /// Contains the maximum length of the data in octets which can be 
        /// transferred from the IO Controller to the IO Device. 
        /// </summary>
        /// <remarks>This length is defined by the sum of the output data of 
        /// all used submodules, the corresponding IO producer status and the 
        /// IO consumer status of the used input submodules.</remarks>
        public System.UInt32 MaxOutputLength => this.m_MaxOutputLength;

        /// <summary>
        /// Accesses the maximum length of the output and input data in octets. 
        /// </summary>
        /// <remarks>MaxDataLength shall not be less than the highest value of 
        /// MaxInputLength or MaxOutputLength.
        /// It shall not be greater than the sum of MaxInputLength and MaxOutputLength. 
        /// If this keyword is not provided, the maximum length is the sum of 
        /// MaxInputLength and MaxOutputLength.</remarks>
        public System.UInt32 MaxDataLength => this.m_MaxDataLength;

        #region IIOConfigData2 Members

        public System.UInt32 MaxTotalDataLength => this.m_MaxTotalDataLength;

        /// <summary>
        /// Accesses the maximum length of the used data in octets which can be 
        /// transferred from the IO Device to the IO Controller. 
        /// </summary>
        /// <remarks>This length is defined by the sum of the used input data of 
        /// all used submodules, the corresponding IO producer status and the 
        /// IO consumer status of the used output submodules.</remarks>
        public System.UInt32 MaxApplicationInputLength => this.m_MaxApplicationInputLength;

        /// <summary>
        /// Indicates if MaxApplicationInputLength is given in GSD file
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMaxApplicationInputLength => m_IsMaxApplicationInputLength;

        /// <summary>
        /// Contains the maximum length of the used data in octets which can be 
        /// transferred from the IO Controller to the IO Device. 
        /// </summary>
        /// <remarks>This length is defined by the sum of the used output data of 
        /// all used submodules, the corresponding IO producer status and the 
        /// IO consumer status of the used input submodules.</remarks>
        public System.UInt32 MaxApplicationOutputLength => this.m_MaxApplicationOutputLength;

        /// <summary>
        /// Indicates if MaxApplicationOutputLength is given in GSD file
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsMaxApplicationOutputLength => m_IsMaxApplicationOutputLength;

        /// <summary>
        /// Accesses the maximum length of the used output and input data in octets. 
        /// </summary>
        /// <remarks>MaxApplicationDataLength shall not be less than the highest value of 
        /// MaxApplicationInputLength or MaxApplicationOutputLength.
        /// It shall not be greater than the sum of MaxApplicationInputLength and
        /// MaxApplicationOutputLength.</remarks>
        public System.UInt32 MaxApplicationDataLength => this.m_MaxApplicationDataLength;

        /// <summary>
        /// Indicates if MaxApplicationDataLength is given in GSD file
        /// </summary>
        /// <remarks>....</remarks>m_ApplicationLengthIncludesIOxS
        public bool IsMaxApplicationDataLength => m_IsMaxApplicationDataLength;

        /// <summary>
        /// Indicates if ApplicationLength includes IOxS
        /// </summary>
        /// <remarks>....</remarks>
        public bool ApplicationLengthIncludesIOxS => m_ApplicationLengthIncludesIOxS;

        /// <summary>
        /// Accesses the maximum application ARs. 
        /// </summary>
        /// <remarks>...</remarks>
        public System.UInt32 MaxApplicationARs => this.m_MaxApplicationARs;

        #endregion

        #endregion
        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                FillFieldMaxInputLength(hash);

                FillFieldMaxOutputLength(hash);

                FillFieldMaxDataLength(hash);

                FillFieldMaxTotalDataLength(hash);

                FillFieldMaxApplicationInputLength(hash);

                FillFieldIsMaxApplicationInputLength(hash);

                FillFieldMaxApplicationOutputLength(hash);

                FillFieldIsMaxApplicationOutputLength(hash);

                FillFieldMaxApplicationDataLength(hash);

                FillFieldIsMaxApplicationDataLength(hash);

                FillFieldApplicationLengthIncludesIOxS(hash);

                FillFieldMaxApplicationARs(hash);
            }

            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        private void FillFieldMaxApplicationARs(Hashtable hash)
        {
            string member = Models.s_FieldMaxApplicationARs;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxApplicationARs = (uint)hash[member];
        }

        private void FillFieldApplicationLengthIncludesIOxS(Hashtable hash)
        {
            string member = Models.s_FieldApplicationLengthIncludesIoxS;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ApplicationLengthIncludesIOxS = (bool)hash[member];
        }

        private void FillFieldIsMaxApplicationDataLength(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxApplicationDataLength;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxApplicationDataLength = (bool)hash[member];
        }

        private void FillFieldMaxApplicationDataLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxApplicationDataLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxApplicationDataLength = (uint)hash[member];
        }

        private void FillFieldIsMaxApplicationOutputLength(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxApplicationOutputLength;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxApplicationOutputLength = (bool)hash[member];
        }

        private void FillFieldMaxApplicationOutputLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxApplicationOutputLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxApplicationOutputLength = (uint)hash[member];
        }

        private void FillFieldIsMaxApplicationInputLength(Hashtable hash)
        {
            string member = Models.s_FieldIsMaxApplicationInputLength;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMaxApplicationInputLength = (bool)hash[member];
        }

        private void FillFieldMaxApplicationInputLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxApplicationInputLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxApplicationInputLength = (uint)hash[member];
        }

        private void FillFieldMaxTotalDataLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxTotalDataLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
            {
                this.m_MaxTotalDataLength = (uint)hash[member];
            }
            else
            {
                // Default value for maximal total data length is 
                // the maximal data length
                this.m_MaxTotalDataLength = this.m_MaxDataLength;
            }
        }

        private void FillFieldMaxDataLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxDataLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
            {
                this.m_MaxDataLength = (uint)hash[member];
            }
            else
            {
                // Maximal data length is maximal input data length plus maximal 
                // output data length, if no special number is given.
                this.m_MaxDataLength = this.m_MaxInputLength + this.m_MaxOutputLength;
            }
        }

        private void FillFieldMaxOutputLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxOutputLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxOutputLength = (uint)hash[member];
        }

        private void FillFieldMaxInputLength(Hashtable hash)
        {
            string member = Models.s_FieldMaxInputLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_MaxInputLength = (uint)hash[member];
        }
        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectIoConfigData);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxInputLength, this.m_MaxInputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxOutputLength, this.m_MaxOutputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxDataLength, this.m_MaxDataLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxTotalDataLength, this.m_MaxDataLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxApplicationInputLength, this.m_MaxApplicationInputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxApplicationOutputLength, this.m_MaxApplicationOutputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxApplicationDataLength, this.m_MaxApplicationDataLength);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxApplicationInputLength, this.m_IsMaxApplicationInputLength);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxApplicationOutputLength, this.m_IsMaxApplicationOutputLength);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsMaxApplicationDataLength, this.m_IsMaxApplicationDataLength);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldApplicationLengthIncludesIoxS, this.m_ApplicationLengthIncludesIOxS);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxApplicationARs, this.m_MaxApplicationARs);

            return true;
        }

        #endregion
    }
}


