/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Link.cs                                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Globalization;
using System.Text;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.PNPlannerAdapter.DataClasses
{
    /// <summary>
    /// The PNPlanner class that represents a topological connection.
    /// </summary>
    internal class Link
    {
        //####################################################################################

        #region Construction/Destruction/Initialisation

        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        /// <summary>
        /// The construtor for the class.
        /// </summary>
        /// <param name="fromPort">Starting port of the link.</param>
        /// <param name="toPort">Ending port of the link.</param>
        public Link(DataModel.PCLObjects.Port fromPort, DataModel.PCLObjects.Port toPort)
        {
            if ((fromPort != null)
                && (toPort != null))
            {
                // Keep the "From" port.
                m_FromPort = fromPort;
                m_From = m_FromPort.GetInterface();

                // Get the LinkDelay from the attribute.
                m_LinkDelay =
                    Convert.ToInt64(
                        fromPort.AttributeAccess.GetAnyAttribute<uint>(
                            InternalAttributeNames.PnIrtSignalDelayTime,
                            new AttributeAccessCode(),
                            0),
                        CultureInfo.InvariantCulture);

                // Keep the "To" port.
                m_ToPort = toPort;
                m_To = m_ToPort.GetInterface();
            }
        }

        #endregion

        //####################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        /// <summary>
        /// The delay of link.
        /// </summary>
        private readonly long m_LinkDelay;

        /// <summary>
        /// Starting interface submodule.
        /// </summary>
        private readonly Interface m_From;

        /// <summary>
        /// Ending interface submodule.
        /// </summary>
        private readonly Interface m_To;

        /// <summary>
        /// Starting port.
        /// </summary>
        private readonly DataModel.PCLObjects.Port m_FromPort;

        /// <summary>
        /// Ending port.
        /// </summary>
        private readonly DataModel.PCLObjects.Port m_ToPort;

        #endregion

        //####################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        /// <summary>
        /// Gets the delay of the link.
        /// </summary>
        public long LinkDelay => m_LinkDelay;

        /// <summary>
        /// Gets the start Interface Submodule of the link.
        /// </summary>
        public Interface From => m_From;

        /// <summary>
        /// Gets the end Interface Submodule of the link.
        /// </summary>
        public Interface To => m_To;

        /// <summary>
        /// Gets the starting port of the link.
        /// </summary>
        private PclObject FromPort => m_FromPort;

        /// <summary>
        /// Gets the ending port of the link.
        /// </summary>
        private PclObject ToPort => m_ToPort;

        /// <summary>
        ///   Gets the complete string for the starting Interface Submodule.
        /// </summary>
        public String FromSwitchText
        {
            get
            {
                if (m_From == null)
                    return "";

                // Get the name of the device item with thesstation name.
                StringBuilder fromText = new StringBuilder(
                    AttributeUtilities.GetSubmoduleNameWithContainerAndStation(From));

                return fromText.ToString();
            }
        }

        /// <summary>
        ///   Gets the string for the starting port of the Interface Submodule.
        /// </summary>
        public String FromPortText
        {
            get
            {
                if (m_FromPort == null)
                    return "";

                // Get the number of the port.
                Int32 portNumber = m_FromPort.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.UniquePortNumber,
                    new AttributeAccessCode(),
                    0);

                return portNumber.ToString(CultureInfo.InvariantCulture);
            }
        }

        /// <summary>
        ///   Gets the complete string for the end Interface Submodule.
        /// </summary>
        public String ToSwitchText
        {
            get
            {
                if (m_To == null)
                    return "";

                // Get the name of the device item with thesstation name.
                StringBuilder toText = new StringBuilder(
                    AttributeUtilities.GetSubmoduleNameWithContainerAndStation(To));

                return toText.ToString();
            }
        }

        /// <summary>
        ///   Gets the complete string for the end port of the Interface Submodule.
        /// </summary>
        public String ToPortText
        {
            get
            {
                if (m_ToPort == null)
                    return "";

                // Get the number of the port.
                Int32 portNumber = m_ToPort.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.UniquePortNumber,
                        new AttributeAccessCode(),
                        0);

                return portNumber.ToString(CultureInfo.InvariantCulture);
            }
        }

        #endregion

        //####################################################################################

        #region Public Methods

        // Contains all public methods of the class	
        /// <summary>
        /// Returns the hash code for this instance.
        /// </summary>
        /// <returns>A 32-bit signed integer hash code.</returns>
        public override int GetHashCode()
        {
            return 0;
        }

        /// <summary>
        /// Compares the given Link with the owner.
        /// </summary>
        /// <param name="obj">Provided Link.</param>
        /// <returns>True if the given Link is equal to the owner; false otherwise.</returns>
        public override bool Equals(object obj)
        {
            Link linkToCompare = obj as Link;

            if (linkToCompare == null)
            {
                return false;
            }

            // Compare the properties of two links.
            // The links may completely be equal or the direction can also vary. 
            // The direction does not cause the links to become different links.
            return ((linkToCompare.From == From) && (linkToCompare.To == To) && (linkToCompare.FromPort == FromPort)
                    && (linkToCompare.ToPort == ToPort))
                   || ((linkToCompare.From == To) && (linkToCompare.To == From) && (linkToCompare.FromPort == ToPort)
                       && (linkToCompare.ToPort == FromPort));
        }

        #endregion
    }
}