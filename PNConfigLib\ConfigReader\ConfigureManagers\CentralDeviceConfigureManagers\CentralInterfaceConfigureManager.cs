﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: CentralInterfaceConfigureManager.cs       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.IOController;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.CentralDeviceConfigureManagers
{
    internal class CentralInterfaceConfigureManager
    {
        private Project m_Project;

        private Configuration.Configuration m_Configuration
        {
            get;
        }

        private Topology.Topology m_Topology
        {
            get;
        }

        internal CentralInterfaceConfigureManager(Project project, Configuration.Configuration configuration, Topology.Topology topology) 
        {
            m_Project = project;
            m_Configuration = configuration;
            m_Topology = topology;
        }

        internal Interface Configure(CentralDeviceType xmlCentralDevice,
            CentralDeviceCatalog pndCatalog,
            PNDriverType lonPnDriver,
            CentralDevice centralDevice) 
        {
            Interface interfaceSubmodule = new Interface(xmlCentralDevice.CentralDeviceInterface.InterfaceRefID);
            interfaceSubmodule.PCLCatalogObject = pndCatalog.Interface;

            if (lonPnDriver.Interface.InterfaceType == PNDriverInterfaceEnum.Custom)
            {
                AttributeUtilities.SetCustomVariantSubmoduleIdentNumber(interfaceSubmodule, lonPnDriver.DeviceVersion);
                AttributeUtilities.SetCustomVariantPositionNumber(centralDevice, lonPnDriver.DeviceVersion);
            }
            centralDevice.AddInterface(interfaceSubmodule);

            IInterfaceBusinessLogic interfaceBL = PNBaseInterfaceBL.InterfaceBLCreator(interfaceSubmodule);
            m_Project.BusinessLogicList.Add(interfaceBL);

            CentralPortConfigureManager portConfigureManager = new CentralPortConfigureManager(m_Project, m_Topology);
            portConfigureManager.Configure(pndCatalog, lonPnDriver, interfaceSubmodule, xmlCentralDevice);

            ProjectManagerUtilities.FindSyncDomainById(
                xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                    .SyncDomainRefID,
                m_Project.BusinessLogicList).AddInterface(interfaceSubmodule);

            interfaceSubmodule.Node.Subnet =
                ProjectManagerUtilities.FindSubnetById(
                    xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID,
                    m_Project.BusinessLogicList);

            PNIOC pnIOC = new PNIOC();
            interfaceSubmodule.PNIOC = pnIOC;
            pnIOC.Id = AttributeUtilities.GetName(interfaceSubmodule) + "_PnIOC";
            PNIOCBusinessLogic pnIocBusinessLogic = new PNIOCBusinessLogic(pnIOC);

            if (
                !string.IsNullOrEmpty(
                    xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID))
            {
                IOSystem ioSystem =
                    ProjectManagerUtilities.FindIOSystemById(
                        xmlCentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID,
                        m_Project.BusinessLogicList);
                ioSystem.SetPNIOC(pnIOC);
            }

            m_Project.BusinessLogicList.Add(pnIocBusinessLogic);

            // Interface is configured after PnIoc is created, because some attributes need to be set in PnIoc object.
            interfaceBL.Configure(
                xmlCentralDevice.CentralDeviceInterface,
                ProjectManagerUtilities.FindSyncDomainXmlById(
                    xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                        .SyncDomainRefID,
                    m_Configuration));

            return interfaceSubmodule;
        }
    }
}
