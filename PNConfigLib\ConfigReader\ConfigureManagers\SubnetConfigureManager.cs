﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: SubnetConfigureManager.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.IOSystem;
using PNConfigLib.HWCNBL.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers
{
    internal class SubnetConfigureManager
    {
        private Project m_Project;

        internal SubnetConfigureManager(Project project)
        {
            m_Project = project;
        }

        internal void Configure(List<Configuration.Subnet> subnets) 
        {
            foreach (Configuration.Subnet xmlSubnet in subnets)
            {
                DataModel.PCLObjects.Subnet subnet = new DataModel.PCLObjects.Subnet(xmlSubnet.SubnetID);
                NetIeBusinessLogic subnetBL = new NetIeBusinessLogic(subnet);
                m_Project.BusinessLogicList.Add(subnetBL);
                subnetBL.Configure(xmlSubnet);

                ConfigureSyncDomain(xmlSubnet.DomainManagement.SyncDomains, subnet);
                ConfigureMrpDomain(xmlSubnet.DomainManagement.MrpDomains, subnet);
                ConfigureIOSystem(xmlSubnet.IOSystem, subnet);

                m_Project.SubnetList.Add(subnet);
            }
        }

        private void ConfigureSyncDomain(List<SyncDomainType> syncDomains, DataModel.PCLObjects.Subnet subnet) 
        {
            foreach (SyncDomainType xmlSyncDomain in syncDomains)
            {
                SyncDomain syncDomain = new SyncDomain(xmlSyncDomain.SyncDomainID);
                subnet.DomainList.Add(syncDomain);

                SyncDomainBusinessLogic syncDomainBusinessLogic = new SyncDomainBusinessLogic(syncDomain);
                m_Project.BusinessLogicList.Add(syncDomainBusinessLogic);
                syncDomainBusinessLogic.Configure(xmlSyncDomain);
            }
        }

        private void ConfigureIOSystem(List<SubnetIOSystem> ioSystems, DataModel.PCLObjects.Subnet subnet) 
        {
            foreach (SubnetIOSystem xmlIoSystem in ioSystems)
            {
                IOSystem ioSystem = new IOSystem(xmlIoSystem.IOSystemID);
                IoSystemBusinessLogic ioSystemBusinessLogic = new IoSystemBusinessLogic(ioSystem);
                m_Project.BusinessLogicList.Add(ioSystemBusinessLogic);
                ioSystemBusinessLogic.Configure(xmlIoSystem);
                subnet.AddIOSystem(ioSystem);
            }
        }

        private void ConfigureMrpDomain(List<MrpDomainType> mrpDomains, DataModel.PCLObjects.Subnet subnet)
        {
            foreach (MrpDomainType xmlMrpDomain in mrpDomains)
            {
                MrpDomain mrpDomain = new MrpDomain(xmlMrpDomain.MrpDomainID);
                subnet.DomainList.Add(mrpDomain);

                MrpDomainBusinessLogic mrpDomainBusinessLogic = new MrpDomainBusinessLogic(mrpDomain);
                m_Project.BusinessLogicList.Add(mrpDomainBusinessLogic);
                mrpDomainBusinessLogic.Configure(xmlMrpDomain);
            }
        }
    }
}
