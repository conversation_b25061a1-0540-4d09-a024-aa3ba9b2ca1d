/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: UtilityNodeIe.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Net;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.AddressTailor;
using PNConfigLib.HWCNBL.Utilities.Network;
using PNConfigLib.HWCNBL.Utilities.Network._Enums;

#endregion

namespace PNConfigLib.HWCNBL.Networks.Ethernet
{
    internal class UtilityNodeIe
    {
        private readonly NodeIeBusinessLogic m_NodeIeBusinessLogic;

        private bool SupernetingSupported =>
            m_NodeIeBusinessLogic.CanSuperneting == NodeIeBusinessLogic.Supernet.Supported;

        public UtilityNodeIe(NodeIeBusinessLogic bl)
        {
            m_NodeIeBusinessLogic = bl;
        }

        /// <summary> Checks the uniqueness of the IP address. </summary>
        /// <param name="nodesData"> The data of the consistency check for all Ethernet nodes. </param>
        internal void CheckIPAddressWithinNet(ConsistencyCheckDataNodesIE nodesData)
        {
            if (m_NodeIeBusinessLogic.IsIPConfiguredInProject)
            {
                IDictionary<long, IList<DataModel.PCLObjects.Node>> multipleIPAddresses = nodesData.MultipleIPAddresses;
                long address = m_NodeIeBusinessLogic.IPAddress.AsInt64;
                if (!multipleIPAddresses.ContainsKey(address))
                {
                    return;
                }

                IList<DataModel.PCLObjects.Node> nodesInConflict = multipleIPAddresses[address];
                foreach (DataModel.PCLObjects.Node node in nodesInConflict)
                {
                    if ((node != null)
                        && !node.Equals(m_NodeIeBusinessLogic.Node))
                    {
                        SetCheckConsistencyMessage(m_NodeIeBusinessLogic.IPAddress, IPAddressErrorCodes.AddressNotUnique);
                    }
                }
            }
        }

        /// <summary> Check all IP Protocol properties </summary>
        internal void CheckIPProtocol()
        {
            Interface curInterface = m_NodeIeBusinessLogic.Node.ParentObject as Interface;
            if (m_NodeIeBusinessLogic.IPProtocolUsed
                && (!m_NodeIeBusinessLogic.IsPNPNIpConfigModeSupported || !m_NodeIeBusinessLogic.PNPNIpSuiteViaOtherPath)
                && ((!m_NodeIeBusinessLogic.IsPNIoIpConfigModeSupported
                     && !AddressTailorUtility.IsAddressTailoringEnabledIoControllerInterfaceStartObject(curInterface))
                    || !m_NodeIeBusinessLogic.IsPNIoIpSuiteViaOtherPathActive))
            {
                //IP Protocol is in use
                if (m_NodeIeBusinessLogic.IPConfiguration == NodeIPConfiguration.Project)
                {
                    IPAddress address = m_NodeIeBusinessLogic.IPAddress;
                    IPSubnetMask mask = m_NodeIeBusinessLogic.IPSubnetMask;
                    IPDefaultRouterAddress router = m_NodeIeBusinessLogic.IPDefaultRouterAddress;

                    //Check ip address.
                    IPAddressErrorCodes addressError = address.Check();
                    if (addressError != IPAddressErrorCodes.None)
                    {
                        SetCheckConsistencyMessage(address, addressError);
                    }


                    // check the mask: it is not neccessery to check mask if 
                    // the controller gets it's IP address via other path
                    IPAddressErrorCodes maskError = IPAddressErrorCodes.None;
                    if (m_NodeIeBusinessLogic.IsControllerNode)
                    {
                        maskError = mask.Check();
                    }
                    else
                    {
                        Interface master = NavigationUtilities.GetControllerOfDevice(curInterface);
                        DataModel.PCLObjects.Node masterNodeIe = master?.Node;

                        if ((master != null)
                            && (masterNodeIe != null))
                        {
                            bool pnIPSuiteViaOtherPathOnMaster =
                                masterNodeIe.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                                    new AttributeAccessCode(),
                                    false);
                            bool pnIoIPSuiteViaOtherPathOnMaster =
                                masterNodeIe.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                                    new AttributeAccessCode(),
                                    false);

                            bool ipProtocolUsed =
                                masterNodeIe.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.NodeIPProtocolUsed,
                                    new AttributeAccessCode(),
                                    false);
                            NodeIPConfiguration ipConfiguration = AttributeUtilities.GetNodeIPConfiguration(
                                masterNodeIe);

                            bool isIpConfiguredOnMaster = ipProtocolUsed
                                                          && (ipConfiguration == NodeIPConfiguration.Project)
                                                          && !pnIPSuiteViaOtherPathOnMaster
                                                          && !pnIoIPSuiteViaOtherPathOnMaster;

                            if (isIpConfiguredOnMaster)
                            {
                                maskError = mask.Check();
                            }
                        }
                    }
                    SetCheckConsistencyMessage(mask, maskError);
                    //Check if the IP Address fits to the mask
                    if ((addressError | maskError) == IPAddressErrorCodes.None)
                    {
                        //Check full address (mask and address only if both mask and address are valid
                        SetCheckConsistencyMessage(address, mask.CheckAgainstAddress(address, SupernetingSupported));
                    }

                    //check the router address
                    if (m_NodeIeBusinessLogic.IPDefaultRouterAddressUsed)
                    {
                        //Check router address
                        IPAddressErrorCodes routerError = router.Check();
                        SetCheckConsistencyMessage(router, routerError);
                        //Check if the IP Address of the router fits to the mask
                        if ((routerError | maskError) == IPAddressErrorCodes.None)
                        {
                            SetCheckConsistencyMessage(router, mask.CheckAgainstAddress(router, SupernetingSupported));
                        }
                        //Check if the IP Address fits to the default router
                        if ((routerError | maskError | addressError) == IPAddressErrorCodes.None)
                        {
                            //Check full address (mask, address and default router address only if all of them are valid
                            IPAddressErrorCodes errorCode = router.CheckAgainstAddresses(address, mask);
                            if (errorCode == IPAddressErrorCodes.DefaultRoutersNetOtherThenAddressesNet)
                            {
                                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, curInterface, ConsistencyConstants.NodeIpRouterOtherThanIpAddress,
                                        AttributeUtilities.GetName(curInterface),
                                        address.AsString,
                                        router.AsString,
                                        mask.AsString);
                            }
                            else if (errorCode == IPAddressErrorCodes.DefaultRouterSameAsAddress)
                            {
                                var isIoDevice = (m_NodeIeBusinessLogic.PNIoOperatingMode & PNIOOperatingModes.IODevice) == PNIOOperatingModes.IODevice;
                                if (!isIoDevice || !m_NodeIeBusinessLogic.IPDefaultRouterIodSync)
                                {
                                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, curInterface, ConsistencyConstants.RouterIpDifferentFromIeIp,
                                        m_NodeIeBusinessLogic.IPDefaultRouterAddress.AsString,
                                        m_NodeIeBusinessLogic.IPAddress.AsString,
                                            router.AsString);
                                }
                            }
                        }
                    }
                }
            }
        }

        internal void CheckPNNameOfStation(ConsistencyCheckDataNodesIE nodesData)
        {
            string pnNameOfStation;
            // check if name of station is available
            if (m_NodeIeBusinessLogic.Node?.ParentObject != null)
            {
                pnNameOfStation = GetPNNameOfStation(m_NodeIeBusinessLogic.Node);
                if (string.IsNullOrEmpty(pnNameOfStation))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_NodeIeBusinessLogic.Node.ParentObject, ConsistencyConstants.NodePNNoSInvalid,
                        AttributeUtilities.GetName(m_NodeIeBusinessLogic.Node.Subnet));
                }
                if ((pnNameOfStation != null) && nodesData.MultipleDeviceNames.ContainsKey(pnNameOfStation))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_NodeIeBusinessLogic.Node.ParentObject, ConsistencyConstants.NodePNNoSNotUnique,
                        pnNameOfStation);
                }
            }
            else
            {
                if (m_NodeIeBusinessLogic.Node == null)
                {
                    return;
                }

                Interface interfaceSubmodule = (Interface)m_NodeIeBusinessLogic.Node.ParentObject;

                // check if name of station is available
                if (interfaceSubmodule == null)
                {
                    return;
                }

                pnNameOfStation = m_NodeIeBusinessLogic.GetPNNameOfStation();
                if (string.IsNullOrEmpty(pnNameOfStation))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_NodeIeBusinessLogic.Node.ParentObject, ConsistencyConstants.NodePNNoSInvalid,
                        AttributeUtilities.GetName(m_NodeIeBusinessLogic.Node.Subnet));
                }
                if ((pnNameOfStation != null) && nodesData.MultipleDeviceNames.ContainsKey(pnNameOfStation))
                {
                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, m_NodeIeBusinessLogic.Node.ParentObject, ConsistencyConstants.NodePNNoSNotUnique,
                        pnNameOfStation);
                }
            }
        }

        /// <summary>
        /// Set the IP address and decide if it is nesseccary also to set the
        /// NodeGetsAddressAutomatically attribute.
        /// </summary>
        /// <param name="value">new IP address</param>
        /// <param name="setNodeGetsAddressAutomatically">true if the attribute should be set, false otherwise.</param>
        internal void InternalSetIPAddress(IPAddress value, bool setNodeGetsAddressAutomatically)
        {

            if (setNodeGetsAddressAutomatically)
            {
                m_NodeIeBusinessLogic.NodeGetsAddressAutomatically = false;
            }

            //set ip address for the node 
            value.WriteToNode(m_NodeIeBusinessLogic.Node); // set the attribute
        }

        /// <summary> Set the router address. </summary>
        /// <param name="value">new router address</param>
        internal void InternalSetIPDefaultRouterAddress(IPDefaultRouterAddress value)
        {
            value.WriteToNode(m_NodeIeBusinessLogic.Node);
            // set flag to false
            m_NodeIeBusinessLogic.NodeGetsAddressAutomatically = false;
        }

        /// <summary>
        /// Set the subnet mask and decide if it is nesseccary also to set the
        /// NodeGetsAddressAutomatically attribute.
        /// </summary>
        /// <param name="value">new subnet mask</param>
        /// <param name="setNodeGetsAddressAutomatically">
        /// true if the attribute should be set,
        /// false otherwise.
        /// </param>
        internal void InternalSetIPSubnetMask(IPSubnetMask value, bool setNodeGetsAddressAutomatically)
        {
            //set default router address for the node 
            value.WriteToNode(m_NodeIeBusinessLogic.Node);

            if (setNodeGetsAddressAutomatically)
            {
                m_NodeIeBusinessLogic.NodeGetsAddressAutomatically = false;
            }
        }

        /// <summary> set default ip address (see also SetDefaultAddress) </summary>
        /// <param name="subnetIeBusinessLogic"></param>
        /// <returns>true if succeeded</returns>
        internal bool SetDefaultIPAddress(INetIeBusinessLogic subnetIeBusinessLogic)
        {
            // do nothing if IPv4 address is already unique
            IDictionary usedAddresses = subnetIeBusinessLogic.GetUsedIPAddresses(m_NodeIeBusinessLogic.Node);
            List<long> usedAddressesList = ((Dictionary<long, long>)usedAddresses).Keys.ToList();
            if (!usedAddressesList.Contains(m_NodeIeBusinessLogic.IPAddress.AsInt64))
            {
                return true;
            }

            Interface ioControllerInterface = m_NodeIeBusinessLogic.IOControllerInterface;
            if (ioControllerInterface != null)
            {
                //Since there are more logical nets (actual subnets) we have to shrink our scope 
                //to the one that this node is assigned to.
                DataModel.PCLObjects.Node ioControllerNode = ioControllerInterface.Node;
                IPAddress controllerIpAddress = new IPAddress(ioControllerNode);
                IPSubnetMask ipSubnetMaskController = new IPSubnetMask(ioControllerNode);
                //Set the IP address and the subnet mask to the to one of the controller.
                //The host number will be adjusted later.
                m_NodeIeBusinessLogic.IPAddress = controllerIpAddress;
                m_NodeIeBusinessLogic.IPSubnetMask = ipSubnetMaskController;
            }
            IPAddress addressForLastUsed = m_NodeIeBusinessLogic.IPAddress;
            IPSubnetMask subnetmaskForLastUsed = m_NodeIeBusinessLogic.IPSubnetMask;

            //set subnet mask and dirty flag
            InternalSetIPSubnetMask(subnetmaskForLastUsed, true);

            //local copy of subnet mask
            IPSubnetMask ipSubnetMask = m_NodeIeBusinessLogic.IPSubnetMask;

            long maxLoop = ipSubnetMask.GetHostAddress(new IPAddress(0xFFFFFFFF)); //max loop to find a host address

            long hostAddress = ipSubnetMask.GetHostAddress(addressForLastUsed);
            long netAddress = ipSubnetMask.GetNetAddress(addressForLastUsed);

            // Lookup for free host addresses
            for (long i = hostAddress; i < maxLoop; i++)
            {
                hostAddress++;
                hostAddress &= ~ipSubnetMask.AsInt64; // mask out host address overflow

                IPAddress ipAddress = new IPAddress(netAddress | hostAddress);
                IPAddressErrorCodes errorCode = ipSubnetMask.CheckAgainstAddress(ipAddress, SupernetingSupported);
                if (errorCode != IPAddressErrorCodes.None || 
                    (errorCode == IPAddressErrorCodes.AddressClassInvalid && SupernetingSupported))
                {
                    continue; // IP address is not valid
                }

                if (usedAddresses.Contains(ipAddress.AsInt64))
                {
                    continue; // IP address already exists
                }

                //Ip address is valid
                m_NodeIeBusinessLogic.IPAddress = ipAddress; //set new ip address
                return true; // end the lookup
            }

            Debug.Assert(false, "No valid IP address found");
            return false;
        }

        /// <summary> Set Check Consistency message from IPAddressErrorCode.
        /// (i.e. wrong ip address class (subnet mask and ip address involved) </summary>
        /// <param name="address">ip, default router address, subnet mask</param>
        /// <param name="errorCode">error code</param>
        internal void SetCheckConsistencyMessage(IPAddressBase address, IPAddressErrorCodes errorCode)
        {
            if (errorCode == IPAddressErrorCodes.None
                || errorCode == IPAddressErrorCodes.AddressClassInvalid && SupernetingSupported)
            {
                return; //everything is ok.
            }

            List<object> parameters = new List<object>();
            string interfaceName = AttributeUtilities.GetName(m_NodeIeBusinessLogic.Node.GetInterface());
            string subnetMask = m_NodeIeBusinessLogic.IPSubnetMask.AsString;
            string errorStringId = GetErrorStringId(address, errorCode);
            if (errorStringId == null)
                return;
            switch (errorCode)
            {
                case IPAddressErrorCodes.NotAllowedAddressRange:
                case IPAddressErrorCodes.AddressTooLong:
                case IPAddressErrorCodes.LoopbackAddress:
                    {
                        parameters.Add(address.AsString);
                        parameters.Add(interfaceName);
                        break;
                    }
                case IPAddressErrorCodes.HostAdresseNull:
                case IPAddressErrorCodes.AddressClassInvalid:
                case IPAddressErrorCodes.BroadcastAddress:
                case IPAddressErrorCodes.DefaultRouteAddress:
                    {
                        parameters.Add(address.AsString);
                        parameters.Add(subnetMask);
                        parameters.Add(interfaceName);
                        break;
                    }
                case IPAddressErrorCodes.AddressNotUnique:
                    {
                        parameters.Add(address.AsString);
                        parameters.Add(interfaceName);
                        parameters.AddRange(GetAddressConflictPartner());
                        break;
                    }
                case IPAddressErrorCodes.EmptyAddress:
                    {
                        parameters.Add(interfaceName);
                        break;
                    }
                case IPAddressErrorCodes.NetAddressNull:
                case IPAddressErrorCodes.WrongSubnetMask:
                    {
                        parameters.Add(subnetMask);
                        parameters.Add(interfaceName);
                        break;
                    }
                case IPAddressErrorCodes.WrongAddress:
                    {
                        return;
                    }
            }

            ConsistencyLogger.Log(ConsistencyType.PN, 
                LogSeverity.Error, 
                m_NodeIeBusinessLogic.Node.ParentObject, 
                errorStringId,
                parameters.ToArray());
        }

        /// <summary> Set Check Consistency message from IPAddressErrorCode.
        /// (i.e. wrong ip address class (subnet mask and ip address involved) </summary>
        /// <param name="address">ip, default router address, subnet mask</param>
        /// <param name="errorCode">error code</param>
        /// <param name="addressValue"></param>
        internal void SetCheckConsistencyMessage(IPAddressBase address, IPAddressErrorCodes errorCode, string addressValue)
        {
            if (errorCode == IPAddressErrorCodes.None)
                return; //everything is ok.

            List<object> parameters = new List<object>();
            string interfaceName = AttributeUtilities.GetName(m_NodeIeBusinessLogic.Node.GetInterface());
            string errorStringId = GetErrorStringId(address, errorCode);

            if (errorStringId == null)
                return;

            if ((errorCode == IPAddressErrorCodes.WrongAddress)
                || (errorCode == IPAddressErrorCodes.WrongSubnetMask))
            {
                parameters.Add(addressValue);
                parameters.Add(interfaceName);
            }

            ConsistencyLogger.Log(ConsistencyType.PN,
                LogSeverity.Error,
                m_NodeIeBusinessLogic.Node.ParentObject,
                errorStringId,
                parameters.ToArray());
        }
        private string GetErrorStringId(IPAddressBase address, IPAddressErrorCodes errorCode)
        {
            string errorStringId = null;
            if (address is IPAddress)
            {
                if (!MessagesIe.IpAddressCompile.TryGetValue(errorCode, out errorStringId))
                    Debug.Fail("Unknown error code");
            }
            else if (address is IPDefaultRouterAddress)
            {
                if (!MessagesIe.IpRouterCompile.TryGetValue(errorCode, out errorStringId))
                    Debug.Fail("Unknown error code");
            }
            else if (address is IPSubnetMask)
            {
                if (!MessagesIe.IpSubnetMaskCompile.TryGetValue(errorCode, out errorStringId))
                    Debug.Fail("Unknown error code");
            }
            else
            {
                Debug.Fail("Unknown ip address type.");
            }
            return errorStringId;
        }
        private List<string> GetAddressConflictPartner()
        {
            DataModel.PCLObjects.Node node = m_NodeIeBusinessLogic.Node;
            PclObject interfaceSubmodule = node.GetInterface();
            PclObject device = node.GetDevice();
            var nameAndType = new List<string>();

            if (device == null)
                return nameAndType;

            nameAndType.Add(device.Id);
            nameAndType.Add(interfaceSubmodule.Id);

            return nameAndType;
        }
        private string GetPNNameOfStation(PclObject node)
        {
            bool isPNPNNoSAutoGenerate =
                node.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnPnNoSAutoGenerate,
                    new AttributeAccessCode(),
                    false);
            string nameOfStation;
            if (isPNPNNoSAutoGenerate || (node.GetDevice() is PNIOC && m_NodeIeBusinessLogic.AddressTailoringEnabled))
            {
                nameOfStation =
                    node.AttributeAccess.GetAnyAttribute<string>(
                        InternalAttributeNames.PnNameOfStationVirtual,
                        new AttributeAccessCode(),
                        string.Empty);
            }
            else
            {
                nameOfStation = node.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.PnNameOfStation,
                    new AttributeAccessCode(),
                    string.Empty);
            }

            return nameOfStation;
        }
    }
}