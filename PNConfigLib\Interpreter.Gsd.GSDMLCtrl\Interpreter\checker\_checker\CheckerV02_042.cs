﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_042.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Linq;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.42 and is based on GSD(ML) versions 2.41 and lower.
    ///		
    /// </summary>
    internal class CheckerV02042 : CheckerV02041
    {
        #region Fields

        #endregion


        #region Properties

        protected override string Msg_0X000121052 => "0x00012105_5";


        protected override string AttributesWithTokenList
        {
            get
            {
                string xp = base.AttributesWithTokenList +
                            " | .//gsddef:ProfileProcessAutomation/@PADeviceClass" +
                            " | .//gsddef:InterfaceSubmoduleItem/@SupportedDelayMeasurements";
                return (xp);
            }
        }

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList;
                return (xp);
            }
        }

        #endregion


        #region CheckerObject Members


        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02042;
        }

        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02042;
        }


        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();

            if (Checks == null)
                Checks = new List<string>();


            Checks.Add(Constants.s_Cn_0X00042000);
            Checks.Add(Constants.s_Cn_0X00042001);
            Checks.Add(Constants.s_Cn_0X00042002);
            Checks.Add(Constants.s_Cn_0X00042003);
            Checks.Add(Constants.s_Cn_0X00042004);
            Checks.Add(Constants.s_Cn_0X00042005);
            //Checks.Add(Constants.s_Cn_0X00042006); // TFS #466060: According to GSDML V2.43 the check for natural alignment must be removed

            return succeeded;
        }

        override protected bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();

            try
            {
                //Checks.Remove(Constants.CN_0x000XXXXX);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        #endregion




        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.42.
        /// </summary>
        public CheckerV02042()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version242);
        }

        #endregion


        #region Methods

        // Check for each 'DataItem' if its natural alignment match regarding its data type.
        // TFS #466060: According to GSDML V2.43 the check for natural alignment must be removed.
        //protected virtual void CheckDataItemsForAlignment(IList<XElement> dataItems)
        //{
        //    if (dataItems == null || dataItems.Count <= 0)
        //        return;

        //    int allDataTypeLen = 0;
        //    foreach (var dataItem in dataItems)
        //    {
        //        // Get the data type and length of item
        //        string dataType = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_DataType);
        //        int dataTypeLen = GetDataTypeChannelSize(dataType);

        //        switch (dataType)
        //        {
        //            case Enums.s_VisibleString:
        //            case Enums.s_OctetString:
        //            case Enums.s_OctetStringS:
        //            case Enums.s_UnicodeString8:
        //            case Enums.s_String61131:
        //            case Enums.s_Wstring61131:
        //                {
        //                    string length = Help.GetAttributeValueFromXElement(dataItem, Attributes.s_Length);
        //                    if (!string.IsNullOrEmpty(length))
        //                        dataTypeLen = XmlConvert.ToUInt16(length);
        //                    break;
        //                }
        //            case Enums.s_Integer16:
        //            case Enums.s_Unsigned16:
        //            case Enums.s_Unsigned16S:
        //            case Enums.s_Integer16S:
        //            case Enums.s_N2:
        //            case Enums.s_V2:
        //            case Enums.s_L2:
        //            case Enums.s_R2:
        //            case Enums.s_T2:
        //            case Enums.s_D2:
        //            case Enums.s_E2:
        //            case Enums.s_X2:
        //            case Enums.Unipolar2_16:
        //                {
        //                    if (allDataTypeLen % 2 != 0)
        //                    {
        //                        // "The offset of the 'DataItem' does not match the natural alignment regarding its data type."
        //                        string msg = Help.GetMessageString("M_0x00042006_1");
        //                        string xpath = Help.GetXPath(dataItem.Attribute(Attributes.s_DataType));
        //                        IXmlLineInfo xli = (IXmlLineInfo)dataItem.Attribute(Attributes.s_DataType);
        //                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
        //                                                      ReportCategories.TypeSpecific, "0x00042006_1");
        //                    }
        //                    break;
        //                }
        //            case Enums.s_Integer32:
        //            case Enums.s_Unsigned32:
        //            case Enums.s_Float32:
        //            case Enums.s_Float32Unsigned8:
        //            case Enums.s_Float32Status8:
        //            case Enums.s_N4:
        //            case Enums.s_T4:
        //            case Enums.s_C4:
        //            case Enums.s_X4:
        //                {
        //                    if (allDataTypeLen % 4 != 0)
        //                    {
        //                        // "The offset of the 'DataItem' does not match the natural alignment regarding its data type."
        //                        string msg = Help.GetMessageString("M_0x00042006_1");
        //                        string xpath = Help.GetXPath(dataItem.Attribute(Attributes.s_DataType));
        //                        IXmlLineInfo xli = (IXmlLineInfo)dataItem.Attribute(Attributes.s_DataType);
        //                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
        //                                                      ReportCategories.TypeSpecific, "0x00042006_1");
        //                    }
        //                    break;
        //                }
        //            case Enums.s_Integer64:
        //            case Enums.s_Unsigned64:
        //            case Enums.s_Float64:
        //                {
        //                    if (allDataTypeLen % 8 != 0)
        //                    {
        //                        // "The offset of the 'DataItem' does not match the natural alignment regarding its data type."
        //                        string msg = Help.GetMessageString("M_0x00042006_1");
        //                        string xpath = Help.GetXPath(dataItem.Attribute(Attributes.s_DataType));
        //                        IXmlLineInfo xli = (IXmlLineInfo)dataItem.Attribute(Attributes.s_DataType);
        //                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
        //                                                      ReportCategories.TypeSpecific, "0x00042006_1");
        //                    }
        //                    break;
        //                }
        //        }
        //        allDataTypeLen = allDataTypeLen + dataTypeLen;
        //    }
        //}

        #endregion


        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();
        }


        protected override void DefineAttributeTokenDescriptions()
        {
            base.DefineAttributeTokenDescriptions();

            var tokens1 = AttributeTokenDictionary["IsochroneModeInRT_Classes"];
            tokens1.Remove("RT_CLASS_2");

            IList<string> tokens2 = new List<string>();
            tokens2.Add("ProcessControlDevice");
            tokens2.Add("General");
            AttributeTokenDictionary.Add("PADeviceClass", tokens2);

            var tokens3 = AttributeTokenDictionary["SupportedFeatures"];
            tokens3.Add("LD_NotSupported");

            IList<string> tokens4 = new List<string>();
            tokens4.Add("disable");
            tokens4.Add("PTCP");
            tokens4.Add("CMLDS");
            AttributeTokenDictionary.Add("SupportedDelayMeasurements", tokens4);

            var tokens5 = AttributeTokenDictionary["SupportedRT_Classes"];
            tokens5.Add("RT_CLASS_STREAM_RT");
        }

        /// Die Menge der erlaubten/bekannten Attributwerte ist hier von der PNIO_Version abhängig.
        /// 
        /// Zu prüfen sind die Attribute:
        /// 
        /// Ab GSDML V2.1:
        /// - MAUTypes am Portsubmodul
        /// - FiberOpticTypes am Portsubmodul
        /// Ab GSDML V2.25:
        /// - FieldbusType am Modul sowie am Element SlotCluster am Submodul
        /// - SupportedSubstitutionModes am Submodul
        /// Ab GSDML V2.3:
        /// - ResetToFactoryModes am DAP
        /// Ab GSDML V2.4:
        /// - Neue Werte 0x004F - 0x0066 für MAUTypes
        /// Ab GSDML V2.41
        /// - Neue Werte 103..113, 141, 144 für MAUTypes
        /// 
        /// Teilweise sind die zu prüfenden Werte abhängig von der PNIO Version am DAP oder
        /// von der kleinsten PNIO Version an den DAPs, in die das (Port)Submodul gesteckt werden kann.
        /// In diesen Fällen wird das durch eine spezielle Codierung in den betreffenden Wertebereichen
        /// des Attributs gekennzeichnet. Ein '-x.xx' am Anfang des Wertebereichs bedeutet, dass dieser
        /// Wertebereich nur bis einschließlich dieser PNIO Version gültig ist. Ihm folgt ein
        /// 'Attributname-x.xx' Eintrag, der den Wertebereich für PNIO Versionen > x.xx enthält.
        /// 
        /// Nicht auf diese Weise überprüft werden sollen die Attribute:
        /// - API, APStructureIdentifier und ErrorType weil sich Profile schneller ändern können
        ///   als die PN IO Norm / GSDML Spec / der GSDML Checker
        ///   - Die PROFIsafe-Attribute F_IO_StructureDescVersion sowie DefaultValue und AllowedValues
        ///     an F_Block_ID und F_Par_Version, da bei unbekannten Werten an diesen Attributen das
        ///     Engineering Tool und der GSDML Checker nichts mehr damit anfangen können.
        ///     Hier bleibt es bei den vorhandenen harten Checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();
        }

        #endregion


        #region Overrides

        protected override void CheckIsochroneModeForMarketingRules(XElement dap, XElement interfaceSubmoduleItem, IList<string> applicationClasses)
        {
            var lineInfo = (IXmlLineInfo)dap;

            string isochroneModeInRT_Classes = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeInRTClasses);
            var isoRT_Classes = new List<string>(isochroneModeInRT_Classes.Split(Constants.s_Semicolon.ToCharArray()));
            string isochroneModeSupportedStr = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_IsochroneModeSupported);
            bool isochroneModeSupported = false;
            if (!string.IsNullOrEmpty(isochroneModeSupportedStr))
                isochroneModeSupported = XmlConvert.ToBoolean(isochroneModeSupportedStr);
            if (applicationClasses.Contains("Isochronous"))
            {
                if (!string.IsNullOrEmpty(isochroneModeSupportedStr) && !isochroneModeSupported || !isoRT_Classes.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' contains the
                    // token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported'
                    // shall be "true" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'
                    // shall contain "RT_CLASS_STREAM_HI"."
                    string msg = Help.GetMessageString("M_0x00020020_19");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00020020_19");
                }
            }
            else
            {
                if (isochroneModeSupported ||
                    isoRT_Classes.Contains("RT_CLASS_1") || isoRT_Classes.Contains("RT_CLASS_3"))
                {
                    // "If 'PNIO_Version' >= "V2.31" and the attribute 'DeviceAccessPointItem/CertificationInfo/@ApplicationClass' does not contain the
                    // token "Isochronous", the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported'
                    // shall be "false" if present and the attribute 'DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes'
                    // shall be missing."
                    string msg = Help.GetMessageString("M_0x00020020_20");
                    string xpath = Help.GetXPath(dap);
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00020020_20");
                }
            }
        }

        /// <summary>
        /// CheckSupportedRTClassesContainsStreamCategory
        /// 
        /// <Description>
        /// InterfaceSubmoduleItem/@SupportedRT_Classes new value "RT_CLASS_STREAM_RT".
        /// 
        /// The following check is implemented:
        /// - check, if 'InterfaceSubmoduleItem/@SupportedRT_Classes' contains
        ///   at least one of the stream categories
        ///   "RT_CLASS_STREAM_LO",
        ///   "RT_CLASS_STREAM_HI",
        ///   "RT_CLASS_STREAM_RT".
        /// </Description>
        /// 
        /// </summary>
        /// <returns> </returns>
        protected override void CheckSupportedRTClassesContainsStreamCategory(XElement interfaceSubmoduleItem, XElement certificationInfoExt)
        {
            if (interfaceSubmoduleItem != null)
            {
                string SupportedRT_ClassesValues = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedRTClasses);
                IList<string> SupportedRT_ClassesList = SupportedRT_ClassesValues.Split(Constants.s_Semicolon.ToCharArray());
                if (!SupportedRT_ClassesList.Contains("RT_CLASS_STREAM_LO") &&
                    !SupportedRT_ClassesList.Contains("RT_CLASS_STREAM_HI") &&
                    !SupportedRT_ClassesList.Contains("RT_CLASS_STREAM_RT"))
                {
                    // "When 'CertificationInfoExt/@ConformanceClass'="D", then SupportedRT_Classes must contain
                    //  "RT_CLASS_STREAM_LO", RT_CLASS_STREAM_HI and/or "RT_CLASS_STREAM_RT"."
                    string msg = Help.GetMessageString("M_0x00040005_8");
                    string xpath = Help.GetXPath(certificationInfoExt);
                    var xli = (IXmlLineInfo)certificationInfoExt;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00040005_8");
                }
            }
        }

        #endregion


        /// <summary>
        /// Check number: CN_0x00042000
        /// PortSubmoduleItem/MAUTypeList/MAUTypeItem: New attributes @MaxTransferTimeTX and @MaxTransferTimeRX.
        /// 
        /// The following checks are implemented:
        /// MAUTypeList/MAUTypeItem/@MaxTransferTimeTX and @MaxTransferTimeRX:
        /// (1) may not be present when PNIO_Version less than V2.42
        /// (2) shall be present when
        ///     (a) MAUTypeList/MAUTypeItem/@SupportedFeatures contains "TSN" and
        ///     (b) PNIO_Version greater or equal than V2.42 and
        ///     (c) InterfaceSubmoduleItem/@IsochroneModeInRT_Classes contains "RT_CLASS_STREAM_HI"
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042000()
        {
            // Find all MAUTypeItem nodes with attribute MaxTransferTimeTX
            var mauTypeItemsTX = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                .Where(x => x.Attribute(Attributes.s_MaxTransferTimeTx) != null);
            mauTypeItemsTX = Help.TryRemoveXElementsUnderXsAny(mauTypeItemsTX, Nsmgr, Gsd);
            CreateReport0x00042000_1(mauTypeItemsTX);

            // Find all MAUTypeItem nodes with attribute MaxTransferTimeRX
            var mauTypeItemsRx = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                .Where(x => x.Attribute(Attributes.s_MaxTransferTimeTx) != null);
            mauTypeItemsRx = Help.TryRemoveXElementsUnderXsAny(mauTypeItemsRx, Nsmgr, Gsd);
            CreateReport0x00042000_2(mauTypeItemsRx);

            // Find all MAUTypeItem nodes with attribute SupportedFeatures
            var mauTypeItems = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_MauTypeItem)
                .Where(x => x.Attribute(Attributes.s_SupportedFeatures) != null);
            mauTypeItemsRx = Help.TryRemoveXElementsUnderXsAny(mauTypeItemsRx, Nsmgr, Gsd);
            foreach (var mauTypeItem in mauTypeItems)
            {
                // (2a)
                string strSupportedFeatures = Help.GetAttributeValueFromXElement(mauTypeItem, Attributes.s_SupportedFeatures);
                IList<string> supportedFeaturesList = strSupportedFeatures.Split(Constants.s_Semicolon.ToCharArray());
                if (!supportedFeaturesList.Contains("TSN"))
                {
                    continue;
                }

                if (mauTypeItem.Parent == null)
                {
                    continue;
                }

                var portSubmoduleItem = mauTypeItem.Parent.Parent;

                if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
                    continue; // Port is defined but not referenced

                CreateReport0x00042000_3(dapsOfPort, mauTypeItemsTX, mauTypeItem, mauTypeItemsRx);
            }

            return true;
        }

        private void CreateReport0x00042000_3(
            IList<XElement> dapsOfPort,
            IEnumerable<XElement> mauTypeItemsTx,
            XElement mauTypeItem,
            IEnumerable<XElement> mauTypeItemsRx)
        {
            foreach (var dap in dapsOfPort)
            {
                double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                // (2b)
                if (pnioVersion < 2.42)
                {
                    continue;
                }

                // (2c)
                var interfaceSubmoduleItem =
                    dap.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem).FirstOrDefault();
                if (interfaceSubmoduleItem != null)
                {
                    string strIsochroneModeInRTClasses = Help.GetAttributeValueFromXElement(
                        interfaceSubmoduleItem,
                        Attributes.s_IsochroneModeInRTClasses);
                    IList<string> isochroneModeInRTClassesList =
                        strIsochroneModeInRTClasses.Split(Constants.s_Semicolon.ToCharArray());
                    if (!isochroneModeInRTClassesList.Contains("RT_CLASS_STREAM_HI"))
                    {
                        continue;
                    }
                }

                if (mauTypeItemsTx.Contains(mauTypeItem)
                    && mauTypeItemsRx.Contains(mauTypeItem))
                {
                    continue;
                }

                // "'MAUTypeItem/@MaxTransferTimeTX' and '@MaxTransferTimeRX' may be present when '@PNIO_Version' >= V2.42,
                //  and the associated InterfaceSubmoduleItem/@IsochroneModeInRT_Classes contains the token "RT_CLASS_STREAM_HI",
                //  and this port and MAUType support the given stream class (@SupportedFeatures contains "TSN")."
                string msg = Help.GetMessageString("M_0x00042000_3");
                string xpath = Help.GetXPath(mauTypeItem);
                var xli = (IXmlLineInfo)mauTypeItem;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00042000_3");
                break;
            }
        }

        private void CreateReport0x00042000_2(IEnumerable<XElement> mauTypeItemsRx)
        {
            foreach (var mauTypeItem in mauTypeItemsRx)
            {
                // (1)
                if (mauTypeItem.Parent == null)
                {
                    continue;
                }

                var portSubmoduleItem = mauTypeItem.Parent.Parent;

                if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
                    continue; // Port is defined but not referenced

                foreach (var dap in dapsOfPort)
                {
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (!(pnioVersion < 2.4))
                    {
                        continue;
                    }

                    // "'MAUTypeList/MAUTypeItem/@MaxTransferTimeRX'
                    //  may not be present when '@PNIO_Version' < V2.42."
                    string msg = Help.GetMessageString("M_0x00042000_2");
                    string xpath = Help.GetXPath(mauTypeItem);
                    var xli = (IXmlLineInfo)mauTypeItem;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Warning,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00042000_2");
                    break;
                }
            }
        }

        private void CreateReport0x00042000_1(IEnumerable<XElement> mauTypeItemsTx)
        {
            foreach (var mauTypeItem in mauTypeItemsTx)
            {
                // (1)
                if (mauTypeItem.Parent == null)
                {
                    continue;
                }

                var portSubmoduleItem = mauTypeItem.Parent.Parent;

                if (!PortToDapDictionary.TryGetValue(portSubmoduleItem, out IList<XElement> dapsOfPort))
                    continue; // Port is defined but not referenced

                foreach (var dap in dapsOfPort)
                {
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));
                    if (!(pnioVersion < 2.4))
                    {
                        continue;
                    }

                    // "'MAUTypeList/MAUTypeItem/@MaxTransferTimeTX'
                    //  may not be present when '@PNIO_Version' < V2.42."
                    string msg = Help.GetMessageString("M_0x00042000_1");
                    string xpath = Help.GetXPath(mauTypeItem);
                    var xli = (IXmlLineInfo)mauTypeItem;
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Warning,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00042000_1");
                    break;
                }
            }
        }
        /// <summary>
        /// Check number: CN_0x00042001
        /// 
        /// Checks against element 'ProfileProcessAutomation' and its attributes.
        /// 
        /// Element 'ProfileProcessAutomation' has a new attribute '@PADeviceClass' with the allowed values "General" / "ProcessControlDevice".
        /// (1) "ProcessControlDevice": default value, the attributes '@PAProfileVersion', '@PAProfileDeviceID' and '@PAProfileDeviceDAP_ID' shall be present.
        /// (2) "General":              the attributes '@PAProfileVersion', '@PAProfileDeviceID' and '@PAProfileDeviceDAP_ID' shall not exist."
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042001()
        {
            // Find all ProfileProcessAutomation elements
            var profileProcessAutomationList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ProfileProcessAutomation);
            profileProcessAutomationList = Help.TryRemoveXElementsUnderXsAny(profileProcessAutomationList, Nsmgr, Gsd);
            foreach (var profileProcessAutomation in profileProcessAutomationList)
            {
                // Find the value of PADeviceClass
                string paPADeviceClassStr = Help.GetAttributeValueFromXElement(profileProcessAutomation, Attributes.s_PaDeviceClass);
                if (string.IsNullOrEmpty(paPADeviceClassStr))
                {
                    paPADeviceClassStr = "ProcessControlDevice";
                }

                var paProfileVersion = profileProcessAutomation.Attribute(Attributes.s_PaProfileVersion);
                var paProfileDeviceID = profileProcessAutomation.Attribute(Attributes.s_PaProfileDeviceId);
                var paProfileDeviceDAP_ID = profileProcessAutomation.Attribute(Attributes.s_PaProfileDeviceDapId);
                // (1)
                if (paPADeviceClassStr == "ProcessControlDevice" && (paProfileVersion == null || paProfileDeviceID == null || paProfileDeviceDAP_ID == null))
                {
                    // "When 'ProfileProcessAutomation/@PADeviceClass' is "ProcessControlDevice", the attributes
                    //  '@PAProfileVersion', '@PAProfileDeviceID' and '@PAProfileDeviceDAP_ID' must be present."
                    string msg = Help.GetMessageString("M_0x00042001_1");
                    string xpath = Help.GetXPath(profileProcessAutomation);
                    IXmlLineInfo xli = profileProcessAutomation;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00042001_1");
                }

                // (2)
                if (paPADeviceClassStr == "General" && (paProfileVersion != null || paProfileDeviceID != null || paProfileDeviceDAP_ID != null))
                {
                    // "When 'ProfileProcessAutomation/@PADeviceClass' is "General", the attributes
                    //  '@PAProfileVersion', '@PAProfileDeviceID' and '@PAProfileDeviceDAP_ID' must not be present."
                    string msg = Help.GetMessageString("M_0x00042001_2");
                    string xpath = Help.GetXPath(profileProcessAutomation.Attribute(Attributes.s_PaProfileVersion));
                    IXmlLineInfo xli = profileProcessAutomation.Attribute(Attributes.s_PaProfileVersion);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00042001_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00042002
        /// 
        /// <Description>
        /// For PNIO_Version >= "V2.42" and
        /// at least one submodule which is configurable with this DAP
        /// contains the PROFIenergy/@ProfileVersion attribute with the value >= "V1.3"
        /// DeviceAccessPointItem/@DeviceAccessSupported shall be true.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042002()
        {
            var profienergyList =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ProfIenergy)
                    .Where(
                        x =>
                            (x.Attribute(Attributes.s_ProfileVersion) != null));
            profienergyList = Help.TryRemoveXElementsUnderXsAny(profienergyList, Nsmgr, Gsd);

            List<XElement> dapErrorRaised = new List<XElement>();
            foreach (var profienergy in profienergyList)
            {
                double profileVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(profienergy, Attributes.s_ProfileVersion));
                if (profileVersion < 1.3)
                    continue;

                var submodule = profienergy.Parent;

                if (!SubmoduleToDapDictionary.TryGetValue(submodule, out IList<XElement> dapsOfSubmodule))
                    continue; // Submodule is defined but not referenced

                foreach (var dap in dapsOfSubmodule)
                {
                    double pnioVersion = GetPNioVersion(Help.GetAttributeValueFromXElement(dap, Attributes.s_PNioVersion));

                    if (pnioVersion < 2.42)
                    {
                        continue;
                    }

                    string deviceAccessSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_DeviceAccessSupported);    // must
                    bool deviceAccessSupported = XmlConvert.ToBoolean(deviceAccessSupportedStr);
                    if (deviceAccessSupported || dapErrorRaised.Contains(dap))
                    {
                        continue;
                    }

                    // "If PNIO_Version >= V2.42, and at least one submodule configurable with the DAP contains 'PROFIenergy/@ProfileVersion' >= "V1.3",
                    //  the attribute 'DeviceAccessSupported' must be "true"."
                    string msg = Help.GetMessageString("M_0x00042002_1");
                    string xpath = Help.GetXPath(dap.Attribute(Attributes.s_DeviceAccessSupported));
                    IXmlLineInfo xli = dap.Attribute(Attributes.s_DeviceAccessSupported);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00042002_1");
                    }

                    dapErrorRaised.Add(dap);
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00042003
        /// 
        /// <Description>
        /// DeviceAccessPointItem/IOConfigData: New attribute MaxApplicationARs.
        /// 
        /// The following checks are implemented:
        /// If this attribute is present, 
        /// the value shall be 1, if: 
        /// - the attribute DeviceAccessPointItem/@SharedDeviceSupported is not present or "false" 
        /// - and the attribute DeviceAccessPointItem/@IO_SupervisorSupported is not present or "false". 
        /// otherwise the value shall be > 1. 
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042003()
        {
            var ioConfigDataList =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData)
                    .Where(
                        x =>
                            (x.Attribute(Attributes.s_MaxApplicationARs) != null));
            ioConfigDataList = Help.TryRemoveXElementsUnderXsAny(ioConfigDataList, Nsmgr, Gsd);

            foreach (var ioConfigData in ioConfigDataList)
            {
                UInt16 maxApplicationARs = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(ioConfigData, Attributes.s_MaxApplicationARs));

                var dap = ioConfigData.Parent;

                bool sharedDeviceSupported = false;
                string sharedDeviceSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_SharedDeviceSupported);    // must
                if (!string.IsNullOrEmpty(sharedDeviceSupportedStr))
                    sharedDeviceSupported = XmlConvert.ToBoolean(sharedDeviceSupportedStr);

                bool io_SupervisorSupported = false;
                string io_SupervisorSupportedStr = Help.GetAttributeValueFromXElement(dap, Attributes.s_IOSupervisorSupported);    // must
                if (!string.IsNullOrEmpty(io_SupervisorSupportedStr))
                    io_SupervisorSupported = XmlConvert.ToBoolean(io_SupervisorSupportedStr);

                if (!sharedDeviceSupported && !io_SupervisorSupported && (maxApplicationARs != 1))
                {
                    // "If '@SharedDeviceSupported' is not present or "false" and '@IO_SupervisorSupported' is not present or "false",
                    //  the attribute 'IOConfigData/@MaxApplicationARs' must be "1"."
                    string msg = Help.GetMessageString("M_0x00042003_1");
                    string xpath = Help.GetXPath(ioConfigData.Attribute(Attributes.s_MaxApplicationARs));
                    IXmlLineInfo xli = ioConfigData.Attribute(Attributes.s_MaxApplicationARs);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00042003_1");
                    }
                }

                if ((sharedDeviceSupported || io_SupervisorSupported) && (maxApplicationARs <= 1))
                {
                    // "If '@SharedDeviceSupported' is "true" or '@IO_SupervisorSupported' is "true",
                    //  the attribute 'IOConfigData/@MaxApplicationARs' must be greater than "1"."
                    string msg = Help.GetMessageString("M_0x00042003_2");
                    string xpath = Help.GetXPath(ioConfigData.Attribute(Attributes.s_MaxApplicationARs));
                    IXmlLineInfo xli = ioConfigData.Attribute(Attributes.s_MaxApplicationARs);
                    if (xli != null)
                    {
                        Store.CreateAndAnnounceReport(
                            GSDI.ReportTypes.GSD_RT_Error,
                            xli.LineNumber,
                            xli.LinePosition,
                            msg,
                            xpath,
                            ReportCategories.TypeSpecific,
                            "0x00042003_2");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00042004
        /// 
        /// <Description>
        /// InterfaceSubmoduleItem: New attribute SupportedDelayMeasurements.
        /// 
        /// The following checks are implemented:
        /// This attribute shall contain the token "PTCP", 
        /// (1) when the attribute 'DelayMeasurementSupported' is present and set to "true" 
        /// (2) when the attribute 'SynchronisationMode/@SupportedSyncProtocols' contains the protocol type "PTCP".
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042004()
        {
            var interfaceSubmoduleItems =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem)
                    .Where(
                        x =>
                            (x.Attribute(Attributes.s_DelayMeasurementSupported) != null));
            interfaceSubmoduleItems = Help.TryRemoveXElementsUnderXsAny(interfaceSubmoduleItems, Nsmgr, Gsd);

            foreach (var interfaceSubmoduleItem in interfaceSubmoduleItems)
            {
                bool delayMeasurementSupported = XmlConvert.ToBoolean(Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_DelayMeasurementSupported));

                if (!delayMeasurementSupported)
                    continue;

                string strSupportedDelayMeasurements = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedDelayMeasurements);
                IList<string> supportedDelayMeasurements = strSupportedDelayMeasurements.Split(Constants.s_Semicolon.ToCharArray());
                if (!supportedDelayMeasurements.Contains("PTCP"))
                {
                    // "When 'InterfaceSubmoduleItem/@DelayMeasurementSupported' is present and "true",
                    //  'InterfaceSubmoduleItem/@SupportedDelayMeasurements' must contain the value "PTCP"."
                    string msg = Help.GetMessageString("M_0x00042004_1");
                    string xpath = Help.GetXPath(interfaceSubmoduleItem);
                    IXmlLineInfo xli = interfaceSubmoduleItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00042004_1");
                }
            }

            var synchronisationModes =
                GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SynchronisationMode)
                    .Where(
                        x =>
                            (x.Attribute(Attributes.s_SupportedSyncProtocols) != null));
            synchronisationModes = Help.TryRemoveXElementsUnderXsAny(synchronisationModes, Nsmgr, Gsd);

            foreach (var synchronisationMode in synchronisationModes)
            {
                string strSupportedSyncProtocols = Help.GetAttributeValueFromXElement(synchronisationMode, Attributes.s_SupportedSyncProtocols);
                IList<string> supportedSyncProtocols = strSupportedSyncProtocols.Split(Constants.s_Semicolon.ToCharArray());
                if (!supportedSyncProtocols.Contains("PTCP"))
                    continue;

                var interfaceSubmoduleItem = synchronisationMode.Parent;

                string strSupportedDelayMeasurements = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_SupportedDelayMeasurements);
                IList<string> supportedDelayMeasurements = strSupportedDelayMeasurements.Split(Constants.s_Semicolon.ToCharArray());
                if (supportedDelayMeasurements.Contains("PTCP"))
                {
                    continue;
                }
                // "When 'InterfaceSubmoduleItem/SynchronisationMode/@SupportedSyncProtocols' contains the protocol type "PTCP",
                //  'InterfaceSubmoduleItem/@SupportedDelayMeasurements' must contain the value "PTCP", too."
                string msg = Help.GetMessageString("M_0x00042004_2");
                string xpath = Help.GetXPath(interfaceSubmoduleItem);
                IXmlLineInfo xli = interfaceSubmoduleItem;
                if (xli != null)
                {
                    Store.CreateAndAnnounceReport(
                        GSDI.ReportTypes.GSD_RT_Error,
                        xli.LineNumber,
                        xli.LinePosition,
                        msg,
                        xpath,
                        ReportCategories.TypeSpecific,
                        "0x00042004_2");
                }

            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00042005
        /// Check Graphic file name, which must conform to a strict naming convention.
        /// 
        /// The recommended scheme for the filename is composed of the four fields below in the following order:
        /// 1) "GSDML"
        /// 2) The VendorID as defined in chapter 8.8 without "0x" prefix (exactly 4 hex digits).
        /// 3) The DeviceID as defined in chapter 8.8 without "0x" prefix (exactly 4 hex digits).
        /// 4) A vendor-specific extension
        /// 
        /// The dash character "-" (ASCII 45 decimal) is used as a delimiter between the fields.
        /// 
        /// Example: GSDML-002A-0301-ET200S-01
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x00042005()
        {
            // Eventually the name is not given for checking.
            //string FileName = String.Empty;

            var graphicFiles = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_GraphicItem).Attributes(Attributes.s_GraphicFile);
            graphicFiles = Help.TryRemoveXAttributesUnderXsAny(graphicFiles, Nsmgr, Gsd);

            foreach (var graphicFile in graphicFiles)
            {
                string xpath = Help.GetXPath(graphicFile);
                IXmlLineInfo xli = graphicFile;

                string graphicFileName = graphicFile.Value;

                if (CreateReport0x00042005_1(graphicFileName, xli, xpath))
                {
                    continue;
                }

                string[] parts = graphicFileName.Split("-".ToCharArray());
                int numberOfParts = parts.Length;

                // Empty fields do not count to the number of parts
                for (int i = 0; i < parts.Length; i++)
                {
                    if (string.IsNullOrEmpty(parts[i]))
                        numberOfParts--;
                }

                if (numberOfParts < 4)
                {
                    CreateReport0x00042005_2(xli, xpath);
                }
                else
                {
                    // field 1: "GSDML"
                    if (!parts[0].Equals("GSDML", StringComparison.CurrentCultureIgnoreCase))
                    {
                        CreateReport0x00042005_3(xli, xpath);
                    }
                    else
                    {
                        CreateReport0x00042005_4(parts, xli, xpath);
                    }

                    CreateReport0x00042005_5(parts, xli, xpath);
                }
            }

            return true;
        }

        private void CreateReport0x00042005_5(IReadOnlyList<string> parts, IXmlLineInfo xli, string xpath)
        {
            // field 2: VenorID
            // Check, if it has the required format
            Regex rxId = new("^[A-Fa-f0-9]{4}$");
            String sId = parts[1];
            Match mId = rxId.Match(sId);
            if (!mId.Success)
            {
                // "<VendorID> in graphics file name must be an unsigned16 hex value with exactly 4 hex digits."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00042005_5"), "VenorID");
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.General,
                    "0x00042005_5");
            }

            // field 3: DeviceID
            // Check, if it has the required format
            sId = parts[2];
            mId = rxId.Match(sId);
            if (mId.Success)
            {
                return;
            }

            {
                // "<DeviceID> in graphics file name must be an unsigned16 hex value with exactly 4 hex digits."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00042005_5"), "DeviceID");
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.General,
                    "0x00042005_5");
            }
        }

        private void CreateReport0x00042005_4(IReadOnlyList<string> parts, IXmlLineInfo xli, string xpath)
        {
            if (parts[0] == "GSDML")
            {
                return;
            }

            // ""GSDML-" in graphics file name must be upper case."
            string msg = Help.GetMessageString("M_0x00042005_4");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.General,
                "0x00042005_4");
        }

        private void CreateReport0x00042005_3(IXmlLineInfo xli, string xpath)
        {
            // "A graphics file name must begin with "GSDML-"."
            string msg = Help.GetMessageString("M_0x00042005_3");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.General,
                "0x00042005_3");
        }

        private void CreateReport0x00042005_2(IXmlLineInfo xli, string xpath)
        {
            // "A graphics file name must consist of at least 4 fields, separated by "-"."
            string msg = Help.GetMessageString("M_0x00042005_2");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.General,
                "0x00042005_2");
        }

        private bool CreateReport0x00042005_1(string graphicFileName, IXmlLineInfo xli, string xpath)
        {
            if (graphicFileName.Length != 0)
            {
                return false;
            }

            // "A graphics file name must be according to the scheme "GSDML-<VendorID>-<DeviceID>-<vendor-specific extension>"."
            string msg = Help.GetMessageString("M_0x00042005_1");
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Warning,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.General,
                "0x00042005_1");
            return true;

        }
        /// <summary>
        /// Check number: CN_0x00042006
        /// 
        /// <Description>
        /// For all DataItems under (Virtual)SubmoduleItem elements:
        /// Issue a warning if the offset of the current DataItem does not match the natural alignment regarding its data type.
        /// /// 
        /// TFS #466060: According to GSDML V2.43 the check for natural alignment must be removed.
        /// </Description>
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        //protected virtual bool CheckCN_0x00042006()
        //{
        //    bool succeeded = true;

        //    var allSubmodules =
        //        GsdProfileBody.Descendants()
        //            .Where(
        //                x =>
        //                    x.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
        //                    x.Name.LocalName == Elements.s_SubmoduleItem);
        //    Help.RemoveXElementsUnderXsAny(ref allSubmodules, Nsmgr, Gsd);

        //    foreach (var submodule in allSubmodules)
        //    {
        //        var inputDataItems = GetInputDataItems(submodule);
        //        var outputDataItems = GetOutputDataItems(submodule);

        //        CheckDataItemsForAlignment(inputDataItems);
        //        CheckDataItemsForAlignment(outputDataItems);
        //    }

        //    return succeeded;
        //}

        #endregion
    }
}