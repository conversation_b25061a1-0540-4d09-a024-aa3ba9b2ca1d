/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNNameOfStationConverter.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class PNNameOfStationConverter
    {
        private const char c_DnsLabelEscapeChar = 'x';

        private const int c_DnsNameMaxLength = 240;

        private const string c_Dot = ".";

        private static Dictionary<string, string> s_DnsLabelEscapeTable;

        private static UTF8Encoding s_Utf8Encoder;

        /// <summary>
        /// Converting result
        /// </summary>
        public enum DnsNameConvertStatus
        {
            //
            // Summary:
            //     The conversion is completed without errors
            NoError = 0,
            //
            // Summary:
            //     CPU name could not be converted (Unexpected unicode character)
            InvalidCpuName = 1,
            //
            // Summary:
            //     CPU name too long (violates DNS restrictions)
            CpuNameLengthExceeded = 2,
            //
            // Summary:
            //     Interface name could not be converted (Unexpected unicode character)
            InvalidInterfaceName = 3,
            //
            // Summary:
            //     Interface name too long (violates DNS restrictions)
            InterfaceNameLengthExceeded = 4,
            //
            // Summary:
            //     IO-System name could not be converted (Unexpected unicode character)
            InvalidIOSystemName = 5,
            //
            // Summary:
            //     IO-System name too long (violates DNS restrictions)
            IOSystemNameLengthExceeded = 6,
            //
            // Summary:
            //     Total length violates DNS restrictions
            TotalLengthExceeded = 7,
            //
            // Summary:
            //     The input string does not conform to IDN specification
            InvalidIdn = 8,
            //
            // Summary:
            //     The input string has an invalid escape sequence
            InvalidEscapeSequence = 9,
            //
            // Summary:
            //     The CRC code is invalid or missing
            InvalidCrc = 10,
            //
            // Summary:
            //     The encoding of the Unicode character is invalid
            InvalidUnicodeString = 11
        }

        /// <summary>
        /// Generates the encoded (escaped + Punycoded) DNS-conform name
        /// for the specified name elements
        /// </summary>
        /// <param name="cpuOrHeadName">Name of CPU, Headmodule or CP (not null)</param>
        /// <param name="interfaceName">Name of PN Interface (may be null)</param>
        /// <param name="ioSystemName">Name of PN-IOSystem (may be null)</param>
        /// <param name="output">The resulting DNS name</param>
        /// <returns>Conversion status</returns>
        public static DnsNameConvertStatus EncodePNNameOfStation(
            string cpuOrHeadName,
            string interfaceName,
            string ioSystemName,
            out string output)
        {
            if (cpuOrHeadName == null)
            {
                throw new ArgumentNullException(nameof(cpuOrHeadName));
            }

            bool cpuNameEscapingNeeded;

            string escapedCpuOrHeadName;
            string lowerCpuOrHeadName = cpuOrHeadName.ToLower(CultureInfo.CurrentCulture);

            DnsNameConvertStatus labelConvertStatus = EscapeDnsName(
                lowerCpuOrHeadName,
                out escapedCpuOrHeadName,
                out cpuNameEscapingNeeded,
                true);

            if (labelConvertStatus != DnsNameConvertStatus.NoError)
            {
                output = null;
                return DnsNameConvertStatus.InvalidCpuName;
            }

            bool interfaceNameEscapingNeeded = false;

            string escapedInterfaceName = null;
            string lowerInterfaceName = null;

            if (interfaceName != null)
            {
                lowerInterfaceName = interfaceName.ToLower(CultureInfo.CurrentCulture);
                labelConvertStatus = EscapeDnsName(
                    lowerInterfaceName,
                    out escapedInterfaceName,
                    out interfaceNameEscapingNeeded,
                    false);
                if (labelConvertStatus != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return DnsNameConvertStatus.InvalidInterfaceName;
                }
            }

            bool ioSystemNameEscapingNeeded = false;

            string escapedIOSystemName = null;
            string lowerIoSystemName = null;

            if (ioSystemName != null)
            {
                lowerIoSystemName = ioSystemName.ToLower(CultureInfo.CurrentCulture);
                labelConvertStatus = EscapeDnsName(
                    lowerIoSystemName,
                    out escapedIOSystemName,
                    out ioSystemNameEscapingNeeded,
                    false);

                if (labelConvertStatus != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return DnsNameConvertStatus.InvalidIOSystemName;
                }
            }

            string cpuNameToUse;
            string interfaceNameToUse;
            string ioSystemNameToUse;

            if (cpuNameEscapingNeeded
                || interfaceNameEscapingNeeded
                || ioSystemNameEscapingNeeded)
            {
                cpuNameToUse = escapedCpuOrHeadName + CalculateCrc(escapedCpuOrHeadName);
                interfaceNameToUse = escapedInterfaceName;
                ioSystemNameToUse = escapedIOSystemName;
                if (!string.IsNullOrEmpty(escapedIOSystemName))
                {
                    ioSystemNameToUse += CalculateCrc(escapedIOSystemName);
                }
            }
            else
            {
                cpuNameToUse = lowerCpuOrHeadName;
                interfaceNameToUse = lowerInterfaceName;
                ioSystemNameToUse = lowerIoSystemName;
            }

            //Check if the max allowed length is already exceeded
            if (cpuNameToUse.Length > c_DnsNameMaxLength)
            {
                output = null;
                return DnsNameConvertStatus.TotalLengthExceeded;
            }

            StringBuilder pnNameOfStation = new StringBuilder();

            IdnMapping mapper = new IdnMapping();

            string encodedCpuOrHeadName;

            try
            {
                //Last part of the IDN must be checked separately, because the GetAscii() method do not 
                //report error, when length of the last part of the IDN is greater than 63 character.
                string[] splittedName = cpuNameToUse.Split(c_Dot.ToCharArray());

                if (splittedName.Length > 0)
                {
                    mapper.GetAscii(splittedName[splittedName.Length - 1]);
                }
                encodedCpuOrHeadName = mapper.GetAscii(cpuNameToUse);
            }
            catch (ArgumentException)
            {
                output = null;
                return DnsNameConvertStatus.CpuNameLengthExceeded;
            }

            pnNameOfStation.Append(encodedCpuOrHeadName);

            if (interfaceNameToUse != null)
            {
                string encodedInterfaceName;

                try
                {
                    encodedInterfaceName = mapper.GetAscii(interfaceNameToUse);
                }
                catch (ArgumentException)
                {
                    output = null;
                    return DnsNameConvertStatus.InterfaceNameLengthExceeded;
                }

                pnNameOfStation.Append(c_Dot);
                pnNameOfStation.Append(encodedInterfaceName);
            }

            if (ioSystemNameToUse != null)
            {
                string encodedIOSystemName;

                try
                {
                    encodedIOSystemName = mapper.GetAscii(ioSystemNameToUse);
                }
                catch (ArgumentException)
                {
                    output = null;
                    return DnsNameConvertStatus.IOSystemNameLengthExceeded;
                }

                pnNameOfStation.Append(c_Dot);
                pnNameOfStation.Append(encodedIOSystemName);
            }

            if (pnNameOfStation.Length > c_DnsNameMaxLength)
            {
                output = null;
                return DnsNameConvertStatus.TotalLengthExceeded;
            }

            output = pnNameOfStation.ToString();
            return DnsNameConvertStatus.NoError;
        }

        /// <summary>
        /// Generates the encoded (escaped + Punycoded) DNS-conform name
        /// for the specified name elements
        /// </summary>
        /// <param name="cpuOrHeadName">Name of CPU, Headmodule or CP (not null)</param>
        /// <param name="interfaceName">Name of PN Interface (may be null)</param>
        /// <param name="ioSystemName">Name of PN-IOSystem (may be null)</param>
        /// <param name="output">The resulting DNS name</param>
        /// <param name="allowLabelStartWithDigit">true if label of PNNameOfStation can start with digit</param>
        /// <returns>Conversion status</returns>
        public static DnsNameConvertStatus EncodePNNameOfStation(
            string cpuOrHeadName, string interfaceName, string ioSystemName,
            out string output,
            bool allowLabelStartWithDigit = false)
        {
            if (cpuOrHeadName == null)
            {
                throw new ArgumentNullException(nameof(cpuOrHeadName));
            }

            bool cpuNameEscapingNeeded;

            string escapedCpuOrHeadName;
            string lowerCpuOrHeadName = cpuOrHeadName.ToLower(
                CultureInfo.CurrentCulture);

            DnsNameConvertStatus labelConvertStatus = EscapeDnsName(
                lowerCpuOrHeadName,
                out escapedCpuOrHeadName,
                out cpuNameEscapingNeeded,
                true,
                allowLabelStartWithDigit);

            if (labelConvertStatus != DnsNameConvertStatus.NoError)
            {
                output = null;
                return DnsNameConvertStatus.InvalidCpuName;
            }

            bool interfaceNameEscapingNeeded = false;

            string escapedInterfaceName = null;
            string lowerInterfaceName = null;

            if (interfaceName != null)
            {
                lowerInterfaceName = interfaceName.ToLower(CultureInfo.CurrentCulture);
                labelConvertStatus = EscapeDnsName(lowerInterfaceName,
                    out escapedInterfaceName,
                    out interfaceNameEscapingNeeded,
                    false,
                    allowLabelStartWithDigit);
                if (labelConvertStatus != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return DnsNameConvertStatus.InvalidInterfaceName;
                }
            }

            bool ioSystemNameEscapingNeeded = false;

            string escapedIOSystemName = null;
            string lowerIoSystemName = null;

            if (ioSystemName != null)
            {
                lowerIoSystemName = ioSystemName.ToLower(CultureInfo.CurrentCulture);
                labelConvertStatus = EscapeDnsName(lowerIoSystemName,
                    out escapedIOSystemName,
                    out ioSystemNameEscapingNeeded,
                    false,
                    allowLabelStartWithDigit);

                if (labelConvertStatus != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return DnsNameConvertStatus.InvalidIOSystemName;
                }
            }

            string cpuNameToUse;
            string interfaceNameToUse;
            string ioSystemNameToUse;


            if (cpuNameEscapingNeeded || interfaceNameEscapingNeeded
                || ioSystemNameEscapingNeeded)
            {
                cpuNameToUse = escapedCpuOrHeadName + CalculateCrc(escapedCpuOrHeadName);
                interfaceNameToUse = escapedInterfaceName;
                ioSystemNameToUse = escapedIOSystemName;
                if (!string.IsNullOrEmpty(escapedIOSystemName))
                {
                    ioSystemNameToUse += CalculateCrc(escapedIOSystemName);
                }
            }
            else
            {
                cpuNameToUse = lowerCpuOrHeadName;
                interfaceNameToUse = lowerInterfaceName;
                ioSystemNameToUse = lowerIoSystemName;
            }

            //Check if the max allowed length is already exceeded
            if (cpuNameToUse.Length > c_DnsNameMaxLength)
            {
                output = null;
                return DnsNameConvertStatus.TotalLengthExceeded;
            }

            StringBuilder pnNameOfStation = new StringBuilder();

            IdnMapping mapper = new IdnMapping();

            string encodedCpuOrHeadName;

            try
            {
                #region AP01356875

                //Last part of the IDN must be checked separately, because the GetAscii() method do not 
                //report error, when length of the last part of the IDN is greater than 63 character.
                string[] splittedName = cpuNameToUse.Split(c_Dot.ToCharArray());

                if (splittedName.Length > 0)
                {
                    mapper.GetAscii(splittedName[splittedName.Length - 1]);
                }

                #endregion

                encodedCpuOrHeadName = mapper.GetAscii(cpuNameToUse);
            }
            catch (ArgumentException)
            {
                output = null;
                return DnsNameConvertStatus.CpuNameLengthExceeded;
            }

            pnNameOfStation.Append(encodedCpuOrHeadName);

            if (interfaceNameToUse != null)
            {
                string encodedInterfaceName;

                try
                {
                    encodedInterfaceName = mapper.GetAscii(interfaceNameToUse);
                }
                catch (ArgumentException)
                {
                    output = null;
                    return DnsNameConvertStatus.InterfaceNameLengthExceeded;
                }

                pnNameOfStation.Append(c_Dot);
                pnNameOfStation.Append(encodedInterfaceName);
            }

            if (ioSystemNameToUse != null)
            {
                string encodedIOSystemName;

                try
                {
                    encodedIOSystemName = mapper.GetAscii(ioSystemNameToUse);
                }
                catch (ArgumentException)
                {
                    output = null;
                    return DnsNameConvertStatus.IOSystemNameLengthExceeded;
                }

                pnNameOfStation.Append(c_Dot);
                pnNameOfStation.Append(encodedIOSystemName);
            }

            if (pnNameOfStation.Length > c_DnsNameMaxLength)
            {
                output = null;
                return DnsNameConvertStatus.TotalLengthExceeded;
            }

            output = pnNameOfStation.ToString();
            return DnsNameConvertStatus.NoError;
        }

        /// <summary>
        /// Escape DNS name
        /// </summary>
        /// <param name="input">The name to escape</param>
        /// <param name="output">The result</param>
        /// <param name="escapingNeeded">true if input is not valid PROFINET DNS label</param>
        /// <param name="firstNamePart">Is this the beginning of the name? 
        /// (special rules apply, see port-abc)</param>
        /// <param name="allowLabelStartWithDigit">true if labels of PNNameOfStation can start with digit</param>
        /// <returns>Conversion status</returns>
        private static DnsNameConvertStatus EscapeDnsName(string input, out string output,
            out bool escapingNeeded, bool firstNamePart, bool allowLabelStartWithDigit)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }
            string[] labels = input.Split('.');

            StringBuilder escapedName = new StringBuilder();
            escapingNeeded = false;
            for (int curLabel = 0; curLabel < labels.Length; curLabel++)
            {
                string escapedLabel;
                bool actualEscapingNeeded;

                DnsNameConvertStatus status = EscapeDnsLabel(labels[curLabel], out escapedLabel,
                    out actualEscapingNeeded, firstNamePart && (curLabel == 0), allowLabelStartWithDigit);

                if (status != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return status;
                }

                if (actualEscapingNeeded)
                {
                    escapingNeeded = true;
                }

                escapedName.Append(escapedLabel);
                if (curLabel < labels.Length - 1)
                {
                    escapedName.Append('.');
                }
            }
            output = escapedName.ToString();
            return DnsNameConvertStatus.NoError;
        }


        /// <summary>
        /// Escape one DNS label
        /// </summary>
        /// <param name="input">The label to escape</param>
        /// <param name="output">The result</param>
        /// <param name="escapingNeeded">true if input is not valid PROFINET DNS label</param>
        /// <param name="firstLabel">Is this the first label? 
        /// (special rules apply, see port-abc)</param>
        /// <param name="allowLabelStartWithDigit"></param>
        /// <returns>Conversion status</returns>
        private static DnsNameConvertStatus EscapeDnsLabel(string input, out string output,
            out bool escapingNeeded, bool firstLabel, bool allowLabelStartWithDigit)
        {
            Initialize();

            StringBuilder result = new StringBuilder();

            // will be set to true if escaping can not be avoided
            // (original name is an invalid DNS name)
            escapingNeeded = false;

            if (input.Length == 0)
            {
                // empty string label - between two consecutive dots
                string replaceString;
                escapingNeeded = true;
                s_DnsLabelEscapeTable.TryGetValue("", out replaceString);
                result.Append(c_DnsLabelEscapeChar);
                result.Append(replaceString);
            }

            int i = 0;

            while (i < input.Length)
            {
                int increment = 1;
                string replaceString;

                if (s_DnsLabelEscapeTable.TryGetValue(
                    input[i].ToString(CultureInfo.InvariantCulture),
                    out replaceString))
                {
                    if (
                        // do not escape dash if not the first or last char
                        (input[i] == '-' && (i != 0 && i != input.Length - 1))
                        ||
                        // do not escape digit if not the first char (--> obsolete!!)
                        // digits now are allowed
                        (Char.IsDigit(input[i]) && (i != 0 || allowLabelStartWithDigit))
                        ||
                        // do not escape p if not first label or
                        // (not first char or does not begin with port-abc)
                        // where a, b, c are digits
                        // (the digits can be added by CRC later, so
                        // here "port-" prefix must be prohibited)
                        (input[i] == 'p'
                         && (!firstLabel || i != 0 || !Regex.Match(input, "^port-(\\d*$|\\d{3})").Success)))
                    {
                        result.Append(input[i]);
                    }
                    else
                    {
                        result.Append(c_DnsLabelEscapeChar);
                        result.Append(replaceString);

                        if (replaceString != c_DnsLabelEscapeChar.ToString(CultureInfo.InvariantCulture))
                        {
                            escapingNeeded = true;
                        }
                    }
                }
                else if (!Char.IsLetterOrDigit(input[i])
                         && input[i] != '-' && input[i] != '.')
                {
                    escapingNeeded = true;

                    // substituting with UTF-8 encoded value
                    // in hexadecimal digits

                    if (Char.IsHighSurrogate(input[i]))
                    {
                        if (i + 1 >= input.Length
                            ||
                            !Char.IsLowSurrogate(input[i + 1]))
                        {
                            output = null;
                            return DnsNameConvertStatus.InvalidUnicodeString;
                        }

                        increment = 2;
                    }

                    byte[] encoded = new byte[4];

                    int numBytes = s_Utf8Encoder.GetBytes(input, i, increment, encoded, 0);

                    result.Append(c_DnsLabelEscapeChar);
                    result.Append('h');
                    for (int b = 0; b < numBytes; b++)
                    {
                        result.Append($"{encoded[b]:x2}");
                    }
                }
                else
                {
                    result.Append(input[i]);
                }

                i += increment;
            }

            output = result.ToString();
            return DnsNameConvertStatus.NoError;
        }

        /// <summary>
        /// Calculates CRC on the UTF-8 encoded text
        /// The result is in hexadecimal notation
        /// (LS Byte first)
        /// </summary>
        /// <param name="input">The input text</param>
        /// <returns>CRC in hexadecimal notation</returns>
        private static string CalculateCrc(string input)
        {
            Initialize();

            byte[] utf8EncodedText = s_Utf8Encoder.GetBytes(input);

            ulong crc = CrcUtilities.CalculateCrc(utf8EncodedText);

            string result = string.Format(CultureInfo.InvariantCulture, "{0:x4}", crc);

            return result;
        }

        /// <summary>
        /// Escape one DNS label
        /// </summary>
        /// <param name="input">The label to escape</param>
        /// <param name="output">The result</param>
        /// <param name="escapingNeeded">true if input is not valid PROFINET DNS label</param>
        /// <param name="firstLabel">
        /// Is this the first label?
        /// (special rules apply, see port-abc)
        /// </param>
        /// <returns>Conversion status</returns>
        private static DnsNameConvertStatus EscapeDnsLabel(
            string input,
            out string output,
            out bool escapingNeeded,
            bool firstLabel)
        {
            Initialize();

            StringBuilder result = new StringBuilder();

            // will be set to true if escaping can not be avoided
            // (original name is an invalid DNS name)
            escapingNeeded = false;

            if (input.Length == 0)
            {
                // empty string label - between two consecutive dots
                string replaceString;
                escapingNeeded = true;
                s_DnsLabelEscapeTable.TryGetValue("", out replaceString);
                result.Append(c_DnsLabelEscapeChar);
                result.Append(replaceString);
            }

            int i = 0;

            while (i < input.Length)
            {
                int increment = 1;
                string replaceString;

                if (s_DnsLabelEscapeTable.TryGetValue(
                    input[i].ToString(CultureInfo.InvariantCulture),
                    out replaceString))
                {
                    if (
                        // do not escape dash if not the first or last char
                        ((input[i] == '-') && (i != 0) && (i != input.Length - 1))
                        ||
                        // do not escape digit if not the first char (--> obsolete!!)
                        (char.IsDigit(input[i]) && (i != 0))
                        ||
                        // do not escape p if not first label or
                        // (not first char or does not begin with port-abc)
                        // where a, b, c are digits
                        // (the digits can be added by CRC later, so
                        // here "port-" prefix must be prohibited)
                        ((input[i] == 'p') && (!firstLabel || (i != 0)
                                                           || !Regex.Match(input, "^port-(\\d*$|\\d{3})").Success)))
                    {
                        result.Append(input[i]);
                    }
                    else
                    {
                        result.Append(c_DnsLabelEscapeChar);
                        result.Append(replaceString);

                        if (replaceString != c_DnsLabelEscapeChar.ToString(CultureInfo.InvariantCulture))
                        {
                            escapingNeeded = true;
                        }
                    }
                }
                else if (!char.IsLetterOrDigit(input[i])
                         && (input[i] != '-')
                         && (input[i] != '.'))
                {
                    escapingNeeded = true;

                    // substituting with UTF-8 encoded value
                    // in hexadecimal digits

                    if (char.IsHighSurrogate(input[i]))
                    {
                        if ((i + 1 >= input.Length)
                            || !char.IsLowSurrogate(input[i + 1]))
                        {
                            output = null;
                            return DnsNameConvertStatus.InvalidUnicodeString;
                        }

                        increment = 2;
                    }

                    byte[] encoded = new byte[4];

                    int numBytes = s_Utf8Encoder.GetBytes(input, i, increment, encoded, 0);

                    result.Append(c_DnsLabelEscapeChar);
                    result.Append('h');
                    for (int b = 0; b < numBytes; b++)
                    {
                        result.Append(string.Format(CultureInfo.InvariantCulture, "{0:x2}", encoded[b]));
                    }
                }
                else
                {
                    result.Append(input[i]);
                }

                i += increment;
            }

            output = result.ToString();
            return DnsNameConvertStatus.NoError;
        }

        /// <summary>
        /// Escape DNS name
        /// </summary>
        /// <param name="input">The name to escape</param>
        /// <param name="output">The result</param>
        /// <param name="escapingNeeded">true if input is not valid PROFINET DNS label</param>
        /// <param name="firstNamePart">
        /// Is this the beginning of the name?
        /// (special rules apply, see port-abc)
        /// </param>
        /// <returns>Conversion status</returns>
        private static DnsNameConvertStatus EscapeDnsName(
            string input,
            out string output,
            out bool escapingNeeded,
            bool firstNamePart)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }
            string[] labels = input.Split('.');

            StringBuilder escapedName = new StringBuilder();
            escapingNeeded = false;
            for (int curLabel = 0; curLabel < labels.Length; curLabel++)
            {
                string escapedLabel;
                bool actualEscapingNeeded;

                DnsNameConvertStatus status = EscapeDnsLabel(
                    labels[curLabel],
                    out escapedLabel,
                    out actualEscapingNeeded,
                    firstNamePart && (curLabel == 0));

                if (status != DnsNameConvertStatus.NoError)
                {
                    output = null;
                    return status;
                }

                if (actualEscapingNeeded)
                {
                    escapingNeeded = true;
                }

                escapedName.Append(escapedLabel);
                if (curLabel < labels.Length - 1)
                {
                    escapedName.Append('.');
                }
            }
            output = escapedName.ToString();
            return DnsNameConvertStatus.NoError;
        }

        /// <summary>
        /// This function initializes the mapping table
        /// (part of Singleton design pattern)
        /// </summary>
        /// <returns></returns>
        private static void Initialize()
        {
            if (s_Utf8Encoder == null)
            {
                s_Utf8Encoder = new UTF8Encoding();
            }

            if (s_DnsLabelEscapeTable == null)
            {
                s_DnsLabelEscapeTable = new Dictionary<string, string>();

                s_DnsLabelEscapeTable.Add("", "3"); // (empty label between two dots)
                s_DnsLabelEscapeTable.Add(" ", "a"); // ANSI 32
                s_DnsLabelEscapeTable.Add("!", "1"); // ANSI 33
                s_DnsLabelEscapeTable.Add("\"", "c"); // ANSI 34
                // the letter d is reservet for beginning numbers
                s_DnsLabelEscapeTable.Add("#", "e"); // ANSI 35
                s_DnsLabelEscapeTable.Add("$", "f"); // ANSI 36
                s_DnsLabelEscapeTable.Add("%", "g"); // ANSI 37
                s_DnsLabelEscapeTable.Add("&", "i"); // ANSI 38
                // the letter h is reserved for hexa notation
                s_DnsLabelEscapeTable.Add("\'", "j"); // ANSI 39
                s_DnsLabelEscapeTable.Add("(", "k"); // ANSI 40
                s_DnsLabelEscapeTable.Add(")", "l"); // ANSI 41
                s_DnsLabelEscapeTable.Add("*", "m"); // ANSI 42
                s_DnsLabelEscapeTable.Add("+", "n"); // ANSI 43
                s_DnsLabelEscapeTable.Add(",", "o"); // ANSI 44
                s_DnsLabelEscapeTable.Add("-", "q"); // ANSI 45
                // ANSI 46: "."
                // the letter p is reserved due to the port-abc problem
                s_DnsLabelEscapeTable.Add("/", "r"); // ANSI 47
                s_DnsLabelEscapeTable.Add("0", "d0"); // ANSI 48
                s_DnsLabelEscapeTable.Add("1", "d1"); // ANSI 49
                s_DnsLabelEscapeTable.Add("2", "d2"); // ANSI 50
                s_DnsLabelEscapeTable.Add("3", "d3"); // ANSI 51
                s_DnsLabelEscapeTable.Add("4", "d4"); // ANSI 52
                s_DnsLabelEscapeTable.Add("5", "d5"); // ANSI 53
                s_DnsLabelEscapeTable.Add("6", "d6"); // ANSI 54
                s_DnsLabelEscapeTable.Add("7", "d7"); // ANSI 55
                s_DnsLabelEscapeTable.Add("8", "d8"); // ANSI 56
                s_DnsLabelEscapeTable.Add("9", "d9"); // ANSI 57
                s_DnsLabelEscapeTable.Add(":", "s"); // ANSI 58
                s_DnsLabelEscapeTable.Add(";", "t"); // ANSI 59
                s_DnsLabelEscapeTable.Add("<", "4"); // ANSI 60
                s_DnsLabelEscapeTable.Add("=", "v"); // ANSI 61
                s_DnsLabelEscapeTable.Add(">", "w"); // ANSI 62
                // letter x: escape character
                s_DnsLabelEscapeTable.Add("?", "y"); // ANSI 63
                s_DnsLabelEscapeTable.Add("@", "z"); // ANSI 64
                // 65-90: letters A-Z
                s_DnsLabelEscapeTable.Add("[", "2"); // ANSI 91
                s_DnsLabelEscapeTable.Add("\\", "u"); // ANSI 92
                s_DnsLabelEscapeTable.Add("]", "5"); // ANSI 93
                //s_DnsLabelEscapeTable.Add("^",  ""); // ANSI 94 - converted with hexa notation
                s_DnsLabelEscapeTable.Add("_", "b"); // ANSI 95
                s_DnsLabelEscapeTable.Add("`", "6"); // ANSI 96
                // 97-111: letters a-o
                // letter p: due to "port-123" problem
                s_DnsLabelEscapeTable.Add("p", "p"); // ANSI 112
                // 113-119: letters q-w
                // letter x: escape character
                s_DnsLabelEscapeTable.Add("x", "x"); // ANSI 120
                // 121-122: letters y-z
                s_DnsLabelEscapeTable.Add("{", "7"); // ANSI 123
                s_DnsLabelEscapeTable.Add("|", "8"); // ANSI 124
                s_DnsLabelEscapeTable.Add("}", "9"); // ANSI 125

                // letters above 126 will be converted with the hexa notation
            }
        }
    }
}