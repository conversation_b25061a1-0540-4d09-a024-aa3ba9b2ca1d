/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIrtIFCentralBL.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIrtIFCentralBL : IFDecorator
    {
        public PNIrtIFCentralBL(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        /// <summary>
        /// Initialize the Actions and Attributes.
        /// Register event handlers and register attribute handlers.
        /// </summary>
        public override void InitBL()
        {
            InitAttributes();
            InitActions();
        }

        private void InitActions()
        {
            Interface.BaseActions.RegisterMethod(GetPdirSubframeData.Name, GenericMethodGetPDIRSubframeData);
            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodConsistencyCheck);
        }

        private void InitAttributes()
        {
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtHighestSyncClass, 0);
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtSwitchActualBridgingDelay, 0);
        }

        #region Private Implementation

        private void MethodConsistencyCheck()
        {
            if (PNAttributeUtility.IsPNIoControllerOperatingModeActive(Interface))
            {
                ConsistencyCheckIrtIFCentral(Interface.SyncDomain);
            }
        }

        private void ConsistencyCheckIrtIFCentral(SyncDomain syncDomain)
        {
            SyncDomainBusinessLogic syncDomainBusinessLogic = null;
            //Consistency Check K1:
            //FS PN-IRT Chapter "Synchronization settings on the PN interface of CPU/CP"

            // Get the SyncRole of the Interface Submodule.
            PNIOC ioController = Interface.PNIOC;
            if (null != ioController)
            {
                PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioController);
                // If the submodule is synchronized as SyncSlave, the Send Clock Factor 
                // of the Sync-Domain should be supported.
                if (syncRole == PNIRTSyncRole.SyncSlave)
                {
                    if (syncDomain != null)
                    {
                        syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
                        Dictionary<Interface, List<long>> possibleSendClocFactors =
                            syncDomainBusinessLogic.PossibleSendClockFactors;
                        // Check whether the Send Clock Factor of the Sync Domain is supported.
                        if ((possibleSendClocFactors != null) 
                            && ((possibleSendClocFactors.Count == 0)
                                || !possibleSendClocFactors[Interface].Contains(syncDomainBusinessLogic.SendClockFactor)))
                        {
                            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Interface, ConsistencyConstants.SendclockNotApplicable);
                        }
                    }
                }

                if (syncRole == PNIRTSyncRole.NotSynchronized)
                {
                    if (syncDomain != null)
                    {
                        // Check whether synchronized IO Data is established on the controller submodule. 
                        List<IPNFrameData> frameDataObjects =
                            NavigationUtilities.GetAllPNFrameDataOfController(Interface);

                        if (frameDataObjects != null)
                        {
                            foreach (IPNFrameData frameData in frameDataObjects)
                            {
                                if (frameData.IoSyncRole == (byte)PNIRTSyncRole.NotSynchronized)
                                {
                                    continue;
                                }
                                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Interface, ConsistencyConstants.ControllerNotSynchronized,
                                        AttributeUtilities.GetName(Interface));
                                break;
                            }
                        }
                    }
                }

                if (syncRole == PNIRTSyncRole.SyncMaster)
                {
                    List<IPNFrameData> frameDataObjects = NavigationUtilities.GetAllPNFrameDataOfController(Interface);

                    if ((frameDataObjects != null)
                        && (frameDataObjects.Count > 0)
                         &&
                        !Utilities.AddressTailor.AddressTailorUtility.IsAddressTailoringEnabledIoControllerInterfaceStartObject(Interface))
                    {
                        //Is IRTTop
                        if (frameDataObjects[0].FrameClass == (long)PNIOFrameClass.Class3Frame)
                        {
                            DataModel.PCLObjects.Node node = Interface.Node;
                            if (node != null)
                            {
                                bool isPNPNNoSViaOtherPath =
                                    node.AttributeAccess.GetAnyAttribute<bool>(
                                        InternalAttributeNames.PnPnNoSViaOtherPath,
                                        null,
                                        false);
                                bool isPNIpSuiteViaOtherPath =
                                    node.AttributeAccess.GetAnyAttribute<bool>(
                                        InternalAttributeNames.PnPnIpSuiteViaOtherPath,
                                        null,
                                        false);

                                if (isPNPNNoSViaOtherPath && isPNIpSuiteViaOtherPath)
                                {
                                    ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Interface, ConsistencyConstants.NoSViaOtherPathActiveWithIRTTop);

                                }
                            }
                        }
                    }
                }
            }
            //Consistency check for syncdomain boundaries
            foreach (DataModel.PCLObjects.Port port in 
                DomainManagementUtility.CheckSlavesOutsideOfTheBoundariesOfSyncDomain(Interface))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, 
                    Interface, 
                    ConsistencyConstants.PortMayNotBeBoundaryError,
                    Utility.GetNameWithContainer(port));
            }

            if (syncDomain == null)
            {
                return;
            }
            if (syncDomainBusinessLogic == null)
            {
                syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
            }
            if (syncDomainBusinessLogic == null)
            {
                return;
            }
            CheckConsistencyUtility.CheckMaxNoIrFrameData(Interface, syncDomainBusinessLogic);
        }

        
        private void GenericMethodGetPDIRSubframeData(IMethodData methodData)
        {
            byte[] pdirSubframeData = ConfigUtility.GetPDIRSubframeData(Interface);
            //Check if the PDInterfaceMrpDataCheck parameter block is to be generated
            if (pdirSubframeData == null)
            {
                methodData.ReturnValue = null;
                return;
            }

            // Create dataset with Intel header format
            ParameterDatasetStruct pdirSubframeDataStruct = new ParameterDatasetStruct();
            pdirSubframeDataStruct.ParaDSNumber = 0x8020;
            // add parameter block into DS Struct
            pdirSubframeDataStruct.AddParaBlock(pdirSubframeData);
            methodData.ReturnValue = pdirSubframeData;
        }

        #endregion
    }
}