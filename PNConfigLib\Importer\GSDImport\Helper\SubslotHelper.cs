/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: SubslotHelper.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;

using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.GSDImport.Helper
{
    internal class SubslotHelper
    {
        //########################################################################################
        #region Fields
        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        

        #endregion


        //########################################################################################
        #region Properties
        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)


        /// <summary>
        /// submodule plug datas of the module
        /// </summary>
        private IDictionary<string, ModulePlugData> VirtualSubmodulesPlugData
        {
            get;
        }


        private IDictionary<string, VirtualSubmodule> VirtualSubmodules
        {
            get;
        }

        /// <summary>
        /// System defined submodules (PDEV) of the module
        /// </summary>
        private IList<SystemDefinedSubmoduleObject> SystemDefinedSubmodules
        {
            get;
        }

        /// <summary>
        /// Physical submodule plu datas of the module
        /// </summary>
        private Dictionary<string, ModulePlugData> PhysicalSubmodulesPlugData
        {
            get;
        }

        private IDictionary<string, ModuleObject> PhysicalSubmodules
        {
            get;
        }

        /// <summary>
        /// Physical subslots of the module.
        /// </summary>
        private IList<uint> PhysicalSubslots
        {
            get;
        }

        /// <summary>
        /// Physical subslots of the module.
        /// </summary>
        public List<uint> SystemDefinedSubslots
        {
            get;
        }

        /// <summary>
        /// Subslot descriptions
        /// </summary>
        private List<Subslot> SubslotDescriptions
        {
            get;
        }

        #endregion

        //########################################################################################
        #region Construction/Destruction/Initialisation
        // Contains all constructors (class constructors as well as static constructors),
        // All initialisation methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        

        public SubslotHelper()
        {
            VirtualSubmodulesPlugData = new Dictionary<string, ModulePlugData>();
            PhysicalSubmodulesPlugData = new Dictionary<string, ModulePlugData>();
            VirtualSubmodules = new Dictionary<string, VirtualSubmodule>();
            PhysicalSubmodules = new Dictionary<string, ModuleObject>();
            PhysicalSubslots = new List<uint>();
            SystemDefinedSubmodules = new List<SystemDefinedSubmoduleObject>();
            SystemDefinedSubslots = new List<uint>();
            SubslotDescriptions = new List<Subslot>();
        }

        #endregion
        //########################################################################################
        public void AddVirtualSubmodule(string gsdId, VirtualSubmodule submodule)
        {
            VirtualSubmodules.Add(gsdId, submodule);
        }

        public void AddVirtualSubmodulePlugData(string gsdId, ModulePlugData submodulePlugData)
        {
            VirtualSubmodulesPlugData.Add(gsdId, submodulePlugData);
        }

        public void AddPhysicalSubmodulePlugData(string gsdId, ModulePlugData submodulePlugData)
        {
            PhysicalSubmodulesPlugData.Add(gsdId, submodulePlugData);
        }

        public void AddPhysicalSubmodule(string gsdId, ModuleObject submodule)
        {
            PhysicalSubmodules.Add(gsdId, submodule);
        }

        public void AddAllPhysicalSubslots(IEnumerable<uint> physicalSubslots)
        {
            foreach (uint subslot in physicalSubslots)
            {
                PhysicalSubslots.Add(subslot);
            }
        }

        public void AddSystemDefinedSubmodule(SystemDefinedSubmoduleObject systemDefinedSubmodule)
        {
            SystemDefinedSubmodules.Add(systemDefinedSubmodule);
        }

        public void AddSubslotDescription(Subslot subslotDescription)
        {
            SubslotDescriptions.Add(subslotDescription);
        }

        //########################################################################################
        #region Private Implementation
        // Contains the private implementation of the class

        #endregion
    }
}
