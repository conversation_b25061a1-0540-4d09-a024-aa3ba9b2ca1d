/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNFrameData.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;

using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL
{
    internal class PNFrameData : IPNFrameData
    {
        //########################################################################################

        #region Fields

        private readonly List<int> m_CoreIds;

        #endregion

        //########################################################################################

        #region Public Methods

        /// <summary>
        /// Gets the total frame length by adding the header and the trailer.
        /// Parts of the frame considered:
        /// IFG(12 Byte) + Safety (in RTC3) (2 Byte) + Preamble (1 or 7 Byte) + Start Frame Delimiter (1 Byte) +
        /// Ethernet Header (14 Byte) + VLAN Header (in RTC 1&2) (4 Byte) + PROFINET Header (2 Byte) + Datalength +
        /// PROFINET Trailer (4 Byte) + Frame Check Sequence (4 Byte)
        /// Datalength must be minimum 40 Bytes. If the actual data length is smaller than 40 bytes, a payload is added
        /// to make it 40 Bytes.
        /// </summary>
        /// <param name="useShortPreambleForRTC3">
        /// If true, 1-Byte preamble will be used in RTC3 Frames
        /// instead of 7.
        /// </param>
        public long GetTotalFrameLength(bool useShortPreambleForRTC3)
        {
            //Minimum Frame lengths
            const int class12Header = 48;
            int class3Header = useShortPreambleForRTC3 ? 40 : 46;

            long totalFrameLength = Math.Max(DataLength, PNConstants.MinFramePayloadLength);
            if ((FrameClass == (long)PNIOFrameClass.Class1Frame)
                || (FrameClass == (long)PNIOFrameClass.Class2Frame))
            {
                totalFrameLength += class12Header;
            }
            else // Frame class 3
            {
                totalFrameLength += class3Header;
            }
            return totalFrameLength;
        }

        #endregion

        //########################################################################################

        #region Nested Clases

        #endregion

        //########################################################################################

        #region Constants and Enums

        #endregion

        //########################################################################################

        #region Properties

        #region IPNFrameData Members

        public long ControllerLocalPhase { get; set; }

        public void SetPossibleReductionRatios(IEnumerable<long> list)
        {
            PossibleReductionRatios.Clear();
            ((List<long>)PossibleReductionRatios).AddRange(list);
        }

        public long ControllerLocalReductionRatio { get; set; }

        public int CoreId
        {
            get
            {
                Debug.Assert(m_CoreIds.Count == 1, "More than one io-device have this frame. Use CoreIds instead.");
                return m_CoreIds[0];
            }
        }

        public IList<int> CoreIds =>  m_CoreIds; 

        public long DataHoldFactor { get; set; }

        public long DeviceLocalPhase { get; set; }

        public long DeviceLocalReductionRatio { get; set; }

        public long FixedPhaseNumber { get; set; }

        public long FrameClass { get; set; }

        public byte FrameDirection { get; set; }

        public long FrameID { get; set; }

        public long DataLength { get; set; }

        public long SharedDataLength { get; set; }

        public int FrameMultiplier { get; set; }

        public long FrameType { get; set; }

        public bool HasUsingData { get; set; }

        public byte IoSyncRole { get; set; }

        public int MinAutomaticUnsyncUpdateTime { get; set; }

        public long MinFrameIntervall { get; set; }

        public IList<long> PossibleReductionRatios { get; set; }

        public int ProxyNumber { get; set; }

        public int MinAutomaticUnsyncRR { get; set; }

        public uint NumberOfARs { get; set; }

        public bool HasNotAssignedSubmodule { get; set; }

        public int ReductionGranularity => 0; 

        public long RedundantFrameID { get; set; }

        public long SendClockFactor { get; set; }

        public long Sequence { get; set; }

        public int SlotNumber { get; set; }

        public int StationNumber { get; set; }

        public int SubSlotNumber { get; set; }

        public IList<long> SuppRRNonPow { get; set; }

        public IList<long> SuppRRPow { get; set; }

        public IList<long> SuppSendClockFactors { get; set; }

        public byte UpdateTimeMode { get; set; }

        public string Version { get; }

        public long WatchdogFactor { get; set; }

        public int GroupNo { get; set; }

        public int IOCRReferenceNo { get; set; }

        public bool FrameIdSet { get; set; }

        #endregion

        #endregion

        //########################################################################################

        #region Delegates and Events

        // Contains all delegate and events

        #endregion

        //########################################################################################

        #region Construction/Destruction/Initialisation

        public PNFrameData(int coreId)
        {
            m_CoreIds = new List<int> { coreId };
            PossibleReductionRatios = new List<long>();
            SuppRRNonPow = new List<long>();
            SuppRRPow = new List<long>();
            SuppSendClockFactors = new List<long>();
            Version = string.Empty;
            FrameIdSet = false;
        }

        /// <summary>
        /// Copy of the original instance
        /// </summary>
        /// <param name="PNFrameData"></param>
        public PNFrameData(PNFrameData pnFrameData)
        {
            ControllerLocalPhase = pnFrameData.ControllerLocalPhase;
            ControllerLocalReductionRatio = pnFrameData.ControllerLocalReductionRatio;

            // As the list is filled with original frame's list below, no nee dto initialize this list here.
            m_CoreIds = new List<int>();

            DataHoldFactor = pnFrameData.DataHoldFactor;
            DataLength = pnFrameData.DataLength;
            DeviceLocalPhase = pnFrameData.DeviceLocalPhase;
            DeviceLocalReductionRatio = pnFrameData.DeviceLocalReductionRatio;
            FixedPhaseNumber = pnFrameData.FixedPhaseNumber;
            FrameClass = pnFrameData.FrameClass;
            FrameDirection = pnFrameData.FrameDirection;
            FrameID = pnFrameData.FrameID;
            FrameMultiplier = pnFrameData.FrameMultiplier;
            FrameType = pnFrameData.FrameType;
            HasUsingData = pnFrameData.HasUsingData;
            IoSyncRole = pnFrameData.IoSyncRole;
            m_CoreIds = pnFrameData.m_CoreIds;
            MinAutomaticUnsyncRR = pnFrameData.MinAutomaticUnsyncRR;
            MinAutomaticUnsyncUpdateTime = pnFrameData.MinAutomaticUnsyncUpdateTime;
            MinFrameIntervall = pnFrameData.MinFrameIntervall;
            NumberOfARs = pnFrameData.NumberOfARs;
            HasNotAssignedSubmodule = pnFrameData.HasNotAssignedSubmodule;
            PossibleReductionRatios = pnFrameData.PossibleReductionRatios;
            ProxyNumber = pnFrameData.ProxyNumber;
            RedundantFrameID = pnFrameData.RedundantFrameID;
            SendClockFactor = pnFrameData.SendClockFactor;
            Sequence = pnFrameData.Sequence;
            SlotNumber = pnFrameData.SlotNumber;
            StationNumber = pnFrameData.StationNumber;
            SubSlotNumber = pnFrameData.SubSlotNumber;
            SuppRRNonPow = pnFrameData.SuppRRNonPow;
            SuppRRPow = pnFrameData.SuppRRPow;
            SuppSendClockFactors = pnFrameData.SuppSendClockFactors;
            UpdateTimeMode = pnFrameData.UpdateTimeMode;
            Version = pnFrameData.Version;
            WatchdogFactor = pnFrameData.WatchdogFactor;
        }

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        #endregion

        //########################################################################################

        #region Protected Methods

        #endregion

        //########################################################################################

        #region	Private	Implementation

        #endregion
    }
}