/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: NameObjectCollection.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections;
using System.Collections.Specialized;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.Serialization;

#endregion

namespace PNConfigLib.HWCNBL
{
    /// <inheritdoc />
    /// <summary>
    /// A standard implementation for the NameObjectColletionBase, which mapps a string to an object.
    /// </summary>
    [Serializable]
    public sealed class NameObjectCollection : NameObjectCollectionBase
    {

        #region Fields

        #endregion

        #region Private implementation

        #endregion

        #region Constructors

        /// <summary>
        /// Creates an empty string-object collection.
        /// </summary>
        public NameObjectCollection()
        {

        }

        private NameObjectCollection(SerializationInfo info, StreamingContext context) : base(info, context)
        {

        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets or sets the value associated with the specified string.
        /// </summary>
        public object this[string key]
        {
            get
            {
                return BaseGet(key);
            }
            set
            {
                BaseSet(key, value);
            }
        }

        /// <summary>
        /// Gets a string array which contains all the keys in the collection.
        /// </summary>
        public string[] AllKeys => BaseGetAllKeys();

        /// <summary>
        /// Adds an entry to the collection.
        /// </summary>
        /// <param name="key">The key which the object should be added to the collection with.</param>
        /// <param name="value">The object which should be added.</param>
        public void Add(string key, object value)
        {
            BaseAdd(key, value);
        }

        #endregion
    }
}