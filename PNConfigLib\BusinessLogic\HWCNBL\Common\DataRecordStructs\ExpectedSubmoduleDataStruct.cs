/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ExpectedSubmoduleDataStruct.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs
{
    /// <summary>
    /// The data record object for ExpectedSubmoduleData.
    /// </summary>
    internal class ExpectedSubmoduleDataStruct : DataRecordStruct
    {
        /// <summary>
        /// Default constructor for ExpectedSubmoduleDataStruct.
        /// </summary>
        public ExpectedSubmoduleDataStruct()
        {
            Header = new byte[10];
            BlockType = DataRecords.BlockTypes.ExpectedSubmoduleData;
            BlockLength = 0x6; // BV, Reserved, Number of API
            BlockVersion = 0x0100;
            Reserved = 0;
        }

        /// <summary>
        /// APICount part of the data record.
        /// </summary>
        public int APICount
        {
            set { Transformator.Write16(Header, 8, (ushort)value); }
            get { return Transformator.Read16(Header, 8); }
        }
    }
}