using Microsoft.Win32;
using PNConfigTool.Common;
using PNConfigTool.Models;
using PNConfigTool.Services;
using PNConfigTool.Views.Windows;
using System;
using System.Windows;
using System.Windows.Input;
using System.IO;
using System.Threading.Tasks;

namespace PNConfigTool.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        private readonly IProjectService _projectService;
        private readonly INavigationService _navigationService;
        private readonly IGSDMLService _gsdmlService;
        private string _projectName = string.Empty;
        private bool _isProjectOpen;
        private string _statusMessage = "就绪";
        private bool _isBusy;

        public string ProjectName
        {
            get => _projectName;
            set
            {
                if (SetProperty(ref _projectName, value))
                {
                    OnPropertyChanged(nameof(WindowTitle));
                }
            }
        }

        public string WindowTitle
        {
            get => string.IsNullOrEmpty(ProjectName) ? "PROFINET 组态工具" : $"PROFINET 组态工具 - {ProjectName}";
        }

        public bool IsProjectOpen
        {
            get => _isProjectOpen;
            set => SetProperty(ref _isProjectOpen, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool IsBusy
        {
            get => _isBusy;
            private set => SetProperty(ref _isBusy, value);
        }

        public ICommand NewProjectCommand { get; }
        public ICommand OpenProjectCommand { get; }
        public ICommand SaveProjectCommand { get; }
        public ICommand SaveProjectAsCommand { get; }
        public ICommand CloseProjectCommand { get; }
        public ICommand NavigateToNetworkCommand { get; }
        public ICommand NavigateToControllerCommand { get; }
        public ICommand NavigateToDeviceCatalogCommand { get; }
        public ICommand ManageGSDMLCommand { get; }
        public ICommand ViewGSDMLCatalogCommand { get; }
        public ICommand AboutCommand { get; }
        public ICommand ExitCommand { get; }

        public MainWindowViewModel(
            IProjectService projectService, 
            INavigationService navigationService,
            IGSDMLService gsdmlService)
        {
            _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
            _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            _gsdmlService = gsdmlService ?? throw new ArgumentNullException(nameof(gsdmlService));
            
            // 订阅项目改变事件
            _projectService.ProjectChanged += OnProjectChanged;
            
            // 初始化命令
            NewProjectCommand = new RelayCommand(async _ => await ExecuteNewProject());
            OpenProjectCommand = new RelayCommand(async _ => await ExecuteOpenProject());
            SaveProjectCommand = new RelayCommand(
                async _ => await ExecuteSaveProject(), 
                CanExecuteProjectOperation);
            SaveProjectAsCommand = new RelayCommand(
                async _ => await ExecuteSaveProjectAs(), 
                CanExecuteProjectOperation);
            CloseProjectCommand = new RelayCommand(
                async _ => await ExecuteCloseProject(), 
                CanExecuteProjectOperation);
            
            NavigateToNetworkCommand = new RelayCommand(
                _ => _navigationService.Navigate("NetworkConfigPage"));
            NavigateToControllerCommand = new RelayCommand(
                _ => _navigationService.Navigate("ControllerConfigPage"),
                CanExecuteProjectOperation);
            NavigateToDeviceCatalogCommand = new RelayCommand(
                ExecuteNavigateToDeviceCatalog);
            
            ManageGSDMLCommand = new RelayCommand(ExecuteManageGSDML);
            ViewGSDMLCatalogCommand = new RelayCommand(ExecuteViewGSDMLCatalog);
            AboutCommand = new RelayCommand(ExecuteAbout);

            ExitCommand = new RelayCommand(_ => Application.Current.Shutdown());
        }

        private void OnProjectChanged(object? sender, Models.ProjectConfig? project)
        {
            IsProjectOpen = project != null;
            ProjectName = project?.ProjectMetadata.ProjectName ?? "无项目";
            StatusMessage = IsProjectOpen ? $"项目: {ProjectName}" : "无项目";
        }

        private async Task ExecuteNewProject()
        {
            try
            {
                IsBusy = true;
                
                var dialog = new SaveFileDialog
                {
                    Filter = "Profinet 项目 (*.json)|*.json",
                    Title = "创建新项目"
                };

                if (dialog.ShowDialog() == true)
                {
                    string projectName = Path.GetFileNameWithoutExtension(dialog.FileName);
                    string projectFilePath = dialog.FileName;

                    bool result = await _projectService.CreateNewProject(projectName);
                    
                    if (result)
                    {
                        // 设置项目文件路径并保存项目
                        ProjectManager.Instance.CurrentProjectFilePath = projectFilePath;
                        bool saveResult = await _projectService.SaveProject();
                        
                        if (saveResult)
                        {
                            _navigationService.Navigate("NetworkConfigPage");
                            StatusMessage = $"项目已创建: {projectName}";
                        }
                        else
                        {
                            StatusMessage = "创建项目文件夹失败";
                        }
                    }
                    else
                    {
                        StatusMessage = "创建项目失败";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"创建项目时出错: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExecuteOpenProject()
        {
            try
            {
                IsBusy = true;
                
                bool result = await _projectService.OpenProject();
                
                if (result)
                {
                    _navigationService.Navigate("NetworkConfigPage");
                    StatusMessage = $"项目已打开: {ProjectName}";
                }
                else
                {
                    StatusMessage = "打开项目失败";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"打开项目时出错: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExecuteSaveProject()
        {
            try
            {
                IsBusy = true;
                
                bool result = await _projectService.SaveProject();
                StatusMessage = result ? $"项目已保存: {ProjectName}" : "保存项目失败";
            }
            catch (Exception ex)
            {
                StatusMessage = $"保存项目时出错: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExecuteSaveProjectAs()
        {
            try
            {
                IsBusy = true;
                
                bool result = await _projectService.SaveProjectAs();
                StatusMessage = result ? $"项目已另存为: {ProjectName}" : "保存项目失败";
            }
            catch (Exception ex)
            {
                StatusMessage = $"另存项目时出错: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task ExecuteCloseProject()
        {
            try
            {
                IsBusy = true;
                
                bool result = await _projectService.CloseProject();
                if (result)
                {
                    StatusMessage = "项目已关闭";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"关闭项目时出错: {ex.Message}";
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ExecuteManageGSDML(object? parameter)
        {
            try
            {
                // 直接打开GSDML管理窗口，不需要检查项目状态
                var gsdmlWindow = new GSDMLManagementWindow(_gsdmlService);
                gsdmlWindow.Owner = Application.Current.MainWindow;
                gsdmlWindow.ShowDialog();
                
                // 如果项目已打开，刷新项目状态
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null)
                {
                    StatusMessage = "GSDML文件已更新";
                }
                else
                {
                    StatusMessage = "GSDML文件已管理";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"管理GSDML文件时出错: {ex.Message}";
            }
        }

        private void ExecuteViewGSDMLCatalog(object? parameter)
        {
            try
            {
                // 打开GSDML目录查看窗口
                var catalogWindow = new GSDMLCatalogWindow(_gsdmlService);
                catalogWindow.Owner = Application.Current.MainWindow;
                catalogWindow.ShowDialog();
                
                StatusMessage = "已查看GSDML目录";
            }
            catch (Exception ex)
            {
                StatusMessage = $"查看GSDML目录时出错: {ex.Message}";
            }
        }

        private void ExecuteNavigateToDeviceCatalog(object? parameter)
        {
            try
            {
                // 由于已移除设备目录步骤，改为导航到网络配置页面
                _navigationService.Navigate("NetworkConfigPage");
                
                if (!IsProjectOpen)
                {
                    StatusMessage = "PROFINET网络配置已打开 (无项目模式)";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"导航到PROFINET网络配置时出错: {ex.Message}";
            }
        }

        private void ExecuteAbout(object? parameter)
        {
            try
            {
                // 创建并显示关于对话框
                var aboutWindow = new Views.Windows.AboutWindow();
                aboutWindow.Owner = Application.Current.MainWindow;
                aboutWindow.ShowDialog();

                StatusMessage = "已显示关于信息";
            }
            catch (Exception ex)
            {
                StatusMessage = $"显示关于对话框时出错: {ex.Message}";
            }
        }

        private bool CanExecuteProjectOperation(object? parameter)
        {
            // 仅在项目打开且系统不忙时允许执行项目相关操作
            // 注意：NetworkConfigPage可在无项目时导航，此限制不应用于NetworkConfigCommand
            return IsProjectOpen && !IsBusy;
        }
    }
}