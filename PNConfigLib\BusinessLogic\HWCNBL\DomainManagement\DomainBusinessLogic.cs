/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DomainBusinessLogic.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.HWCNBL.DomainManagement
{
    /// <summary>
    /// The business logic class for a Domain object.
    /// </summary>
    /// <remarks>
    /// The Domain object is currently only used for SyncDomain classes. As MRP domains
    /// are added, this base class will have more meaning.
    /// </remarks>
   public class DomainBusinessLogic : IBusinessLogic
    {
        /// <summary>
        /// The list of all interfaces in the domain.
        /// </summary>
        /// <remarks>
        /// This list is used for determining whether the lists for controller and device interfaces should be refreshed.
        /// </remarks>
        private IList<Interface> m_AllInterfaces;

        /// <summary>
        /// The list of controller interfaces in the domain.
        /// </summary>
        private IList<Interface> m_IOControllerInterfaces;

        /// <summary>
        /// The list of device interfaces in the domain.
        /// </summary>
        private IList<Interface> m_IODeviceInterfaces;

        /// <summary>
        /// Default constructor for the DomainBL class.
        /// </summary>
        protected DomainBusinessLogic()
        {
            m_AllInterfaces = new List<Interface>();
            m_IOControllerInterfaces = new List<Interface>();
            m_IODeviceInterfaces = new List<Interface>();
        }

        /// <summary>
        /// Gets the name of the domain.
        /// </summary>
        public string DomainName
        {
            get
            {
                return this.PCLObject.AttributeAccess.GetAnyAttribute<string>(
                    InternalAttributeNames.Name,
                    new AttributeAccessCode(),
                    string.Empty);
            }
        }

        /// <summary>
        /// Getter for the list of controller interfaces in the domain.
        /// </summary>
        public IList<Interface> IOControllerInterfaces
        {
            get
            {
                if (InterfaceInitializationNeeded())
                {
                    InitializeInterfaces();
                }

                return m_IOControllerInterfaces;
            }
        }

        /// <summary>
        /// Getter for the list of device interfaces in the domain.
        /// </summary>
        public IList<Interface> IODeviceInterfaces
        {
            get
            {
                if (InterfaceInitializationNeeded())
                {
                    InitializeInterfaces();
                }

                return m_IODeviceInterfaces;
            }
        }

        /// <summary>
        /// The data model object.
        /// </summary>
        internal Domain PCLObject { get; set; }

        /// <summary>
        /// Refreshes the lists for controller and device interfaces.
        /// </summary>
        private void InitializeInterfaces()
        {
            SyncDomain syncDomain = PCLObject as SyncDomain;
            if (syncDomain == null)
            {
                throw new InvalidOperationException("PclObject of BL class is null.");
            }

            m_AllInterfaces = new List<Interface>();
            m_IOControllerInterfaces = new List<Interface>();
            m_IODeviceInterfaces = new List<Interface>();

            foreach (Interface interfaceSubmodule in syncDomain.GetInterfaces())
            {
                m_AllInterfaces.Add(interfaceSubmodule);

                if (interfaceSubmodule.PNIOC != null)
                {
                    m_IOControllerInterfaces.Add(interfaceSubmodule);
                }
                else if (interfaceSubmodule.PNIOD != null)
                {
                    m_IODeviceInterfaces.Add(interfaceSubmodule);
                }
            }
        }

        /// <summary>
        /// Checks whether interfaces need to be refreshed,
        /// i.e. any interfaces are added after the last list update.
        /// </summary>
        /// <returns>True if interface lists should be refreshed; false otherwise.</returns>
        private bool InterfaceInitializationNeeded()
        {
            SyncDomain syncDomain = PCLObject as SyncDomain;
            if (syncDomain == null)
            {
                throw new InvalidOperationException("PclObject of BL class is null.");
            }

            return syncDomain.GetInterfaces().Count != m_AllInterfaces.Count;
        }
    }
}