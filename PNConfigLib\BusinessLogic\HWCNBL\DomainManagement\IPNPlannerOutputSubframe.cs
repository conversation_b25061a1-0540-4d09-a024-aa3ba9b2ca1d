/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IPNPlannerOutputSubframe.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.DomainManagement
{
    /// <summary>
    /// Interface for the PNPlanner output dfp Frame. The corresponding PNPlanner output element looks like the 
    /// following
    /// DfpFrame SFId="1" Length="139" SFLength="39" Padding="0" Duration="13040"
    /// </summary>
    public interface IPNPlannerOutputSubframe
    {
        //########################################################################################
        #region Properties

        /// <summary>
        /// ID of the subframe
        /// </summary>
        int SubframeId { get; }

        /// <summary>
        /// Length of the complete frame with this subframe
        /// </summary>
        int Length { get; }

        /// <summary>
        /// Padding bytes required to fill minimum RTC3 payload (40 bytes) for the complete frame.
        /// </summary>
        int Padding { get; }

        int MsgId { get; }

        int SFLength {get;}
        #endregion
    }
}
