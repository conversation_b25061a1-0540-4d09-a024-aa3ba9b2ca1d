<Project Sdk="Microsoft.NET.Sdk" ToolsVersion="Current">
  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Platforms>x64</Platforms>
    <ProjectGuid>{8A04E2DF-E86F-42C0-A74D-F1E341C87155}</ProjectGuid>
  </PropertyGroup>
  <PropertyGroup>
    <OutputPath>$(ProjectDir)..\..\bin\$(Configuration)\$(Platform)\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
    <PackageId>PNConfigLib</PackageId>
    <Authors>PNConfigLib</Authors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebugType>portable</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebugType>none</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Interpreter.Gsd.GSDMLCtrl\**" />
    <Compile Remove="Xsd\tools\**" />
    <EmbeddedResource Remove="Interpreter.Gsd.GSDMLCtrl\**" />
    <EmbeddedResource Remove="Xsd\tools\**" />
    <None Remove="Interpreter.Gsd.GSDMLCtrl\**" />
    <None Remove="Xsd\tools\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v2.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v2.2.xml" />
	<None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v3.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Standalone_v2.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Standalone_v2.2.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\IoT20x0_v2.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\IoT20x0_v2.2.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\LinuxNative_v2.2.xml" />
	<None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\LinuxNative_v3.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Linux_v2.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Linux_v2.2.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v2.1.xml" />
    <None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v2.2.xml" />
	<None Remove="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v3.1.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v2.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v2.2.xml" />
	<EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Host_v3.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Standalone_v2.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\CP1625Standalone_v2.2.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\IoT20x0_v2.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\IoT20x0_v2.2.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\LinuxNative_v2.2.xml" />
	<EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\LinuxNative_v3.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Linux_v2.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Linux_v2.2.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v2.1.xml" />
    <EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v2.2.xml" />
	<EmbeddedResource Include="Importer\CentralDeviceImport\CentralDeviceCatalogTemplates\Windows_v3.1.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Interpreter.Gsd.GSDMLCtrl\Interpreter.Gsd.GSDMLCtrl.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Xsd\PNConfigLib_Xsd\*.xsd">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>XSD\%(FileName)%(Extension)</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="BusinessLogic\HWCNBL\Networks\SNMP\" />
  </ItemGroup>
</Project>