/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIrtIFDecentralBL.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.PNInterface
{
    internal class PNIrtIFDecentralBL : IFDecorator
    {
        public PNIrtIFDecentralBL(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        public override void InitBL()
        {
            InitAttributes();
            InitActions();
        }

        private void InitActions()
        {
            ConsistencyManager.RegisterConsistencyCheck(Interface, MethodConsistencyCheck);
        }

        private void InitAttributes()
        {
            Interface.AttributeAccess.AddAnyAttribute<uint>(InternalAttributeNames.PnIrtSwitchActualBridgingDelay, 0);
        }

        private void MethodConsistencyCheck()
        {
            //consystency check for syncdomain boundaries
            foreach (DataModel.PCLObjects.Port port in
                DomainManagementUtility.CheckSlavesOutsideOfTheBoundariesOfSyncDomain(Interface))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,Interface, ConsistencyConstants.PortMayNotBeBoundaryError,
                        Utility.GetNameWithContainer(port));
            }
            

            SyncDomain syncDomain = Interface.SyncDomain;
            if (syncDomain == null)
            {
                return;
            }
            SyncDomainBusinessLogic syncDomainBl = syncDomain.SyncDomainBusinessLogic;
            if (syncDomainBl != null)
            {
               CheckConsistencyUtility.CheckMaxNoIrFrameData(Interface, syncDomainBl);

            }
            SharedDeviceUtility.ConsistencyCheckIRTAndDeviceWithSharedInterface(Interface);
        }
    }
}