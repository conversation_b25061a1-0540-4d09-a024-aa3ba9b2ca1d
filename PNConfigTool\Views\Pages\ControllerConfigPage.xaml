<Page x:Class="PNConfigTool.Views.Pages.ControllerConfigPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:PNConfigTool.Views.Pages"
      xmlns:controls="clr-namespace:PNConfigTool.Views.Controls"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="1000"
      Title="PROFINET 配置向导">

    <Grid Background="White" Margin="10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="300" />
        </Grid.ColumnDefinitions>
        
        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 页面标题 -->
            <TextBlock Grid.Row="0" Text="PROFINET 控制器设置" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>

            <!-- 内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="0,10">
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        设备表列出了此PROFINET网络当前组态的所有设备。
                    </TextBlock>
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,10">
                        可从右侧设备目录树添加设备或将DAP节点拖放到设备表中。
                    </TextBlock>
                    
                    <!-- 设备表 -->
                    <GroupBox Header="设备表" Margin="0,10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <DataGrid x:Name="DevicesDataGrid" Grid.Row="0" AutoGenerateColumns="False" 
                                      CanUserAddRows="False" CanUserDeleteRows="False" Margin="0,5" 
                                      AllowDrop="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="设备号" Binding="{Binding DeviceNumber}" Width="60"/>
                                    <DataGridTextColumn Header="类型" Binding="{Binding DeviceType}" Width="100"/>
                                    <DataGridTextColumn Header="设备名" Binding="{Binding DeviceName}" Width="150"/>
                                    <DataGridTextColumn Header="IP 设置" Binding="{Binding IPSettings}" Width="100"/>
                                    <DataGridTemplateColumn Header="IP 地址" Width="200">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <controls:IPAddressInput IPAddress="{Binding IPAddress, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTextColumn Header="注释" Binding="{Binding Comment}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                            
         
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- 底部按钮区域 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="PreviousButton" Content="&lt; 上一步" Width="100" Padding="10,5" Margin="5" Click="PreviousButton_Click"/>
                <Button x:Name="NextButton" Content="下一步 >" Width="100" Padding="10,5" Margin="5" Click="NextButton_Click"/>
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,5">
                    <Button x:Name="AddDeviceButton" Content="添加" Width="100" Margin="0,0,5,0" Click="AddDeviceButton_Click"/>
                    <Button x:Name="DeleteDeviceButton" Content="删除" Width="100" Margin="5,0,0,0" Click="DeleteDeviceButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- 分隔条 -->
        <GridSplitter Grid.Column="1" 
                    Width="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Stretch"
                    Background="{StaticResource SecondaryColor}"
                    ShowsPreview="True"/>
        
        <!-- 右侧设备目录区域 -->
        <Grid Grid.Column="2" Margin="10,0,0,0">
          <Frame x:Name="DeviceCatalogFrame" NavigationUIVisibility="Hidden">
            <Frame.Resources>
                <Style TargetType="TextBlock">
                    <Setter Property="TextWrapping" Value="Wrap"/>
                </Style>
            </Frame.Resources>
          </Frame>
        </Grid>
    </Grid>
</Page>