using System;
using System.IO;

namespace PNConfigTool.Utilities
{
    /// <summary>
    /// 路径处理工具类，用于处理相对路径和绝对路径的转换
    /// </summary>
    public static class PathHelper
    {
        /// <summary>
        /// 将绝对路径转换为相对于项目目录的相对路径
        /// </summary>
        /// <param name="absolutePath">绝对路径</param>
        /// <param name="projectDirectory">项目目录路径</param>
        /// <returns>相对路径，如果转换失败则返回原路径</returns>
        public static string ToRelativePath(string absolutePath, string projectDirectory)
        {
            try
            {
                if (string.IsNullOrEmpty(absolutePath) || string.IsNullOrEmpty(projectDirectory))
                {
                    return absolutePath;
                }

                // 确保路径都是完整的绝对路径
                string fullAbsolutePath = Path.GetFullPath(absolutePath);
                string fullProjectDirectory = Path.GetFullPath(projectDirectory);

                // 检查文件是否在项目目录下
                if (!fullAbsolutePath.StartsWith(fullProjectDirectory, StringComparison.OrdinalIgnoreCase))
                {
                    // 如果文件不在项目目录下，返回原路径
                    return absolutePath;
                }

                // 计算相对路径
                Uri projectUri = new Uri(fullProjectDirectory + Path.DirectorySeparatorChar);
                Uri fileUri = new Uri(fullAbsolutePath);
                Uri relativeUri = projectUri.MakeRelativeUri(fileUri);
                
                // 将URI转换为本地路径格式
                string relativePath = Uri.UnescapeDataString(relativeUri.ToString());
                relativePath = relativePath.Replace('/', Path.DirectorySeparatorChar);

                System.Diagnostics.Debug.WriteLine($"路径转换: {absolutePath} -> {relativePath}");
                return relativePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径转换失败: {ex.Message}");
                return absolutePath;
            }
        }

        /// <summary>
        /// 将相对路径转换为绝对路径
        /// </summary>
        /// <param name="relativePath">相对路径</param>
        /// <param name="projectDirectory">项目目录路径</param>
        /// <returns>绝对路径</returns>
        public static string ToAbsolutePath(string relativePath, string projectDirectory)
        {
            try
            {
                if (string.IsNullOrEmpty(relativePath) || string.IsNullOrEmpty(projectDirectory))
                {
                    return relativePath;
                }

                // 如果已经是绝对路径，直接返回
                if (Path.IsPathRooted(relativePath))
                {
                    return relativePath;
                }

                // 组合项目目录和相对路径
                string absolutePath = Path.Combine(projectDirectory, relativePath);
                string fullAbsolutePath = Path.GetFullPath(absolutePath);

                System.Diagnostics.Debug.WriteLine($"路径解析: {relativePath} -> {fullAbsolutePath}");
                return fullAbsolutePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"路径解析失败: {ex.Message}");
                return relativePath;
            }
        }

        /// <summary>
        /// 获取项目目录路径
        /// </summary>
        /// <param name="projectFilePath">项目文件路径</param>
        /// <returns>项目目录路径</returns>
        public static string GetProjectDirectory(string projectFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(projectFilePath))
                {
                    return string.Empty;
                }

                return Path.GetDirectoryName(projectFilePath) ?? string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取项目目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查路径是否为相对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>如果是相对路径返回true，否则返回false</returns>
        public static bool IsRelativePath(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return false;
            }

            return !Path.IsPathRooted(path);
        }

        /// <summary>
        /// 规范化路径分隔符
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>规范化后的路径</returns>
        public static string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return path;
            }

            // 将所有路径分隔符统一为当前系统的分隔符
            return path.Replace('/', Path.DirectorySeparatorChar)
                      .Replace('\\', Path.DirectorySeparatorChar);
        }

        /// <summary>
        /// 确保文件存在，如果不存在则尝试相对路径解析
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="projectDirectory">项目目录</param>
        /// <returns>存在的文件路径，如果都不存在则返回原路径</returns>
        public static string EnsureFileExists(string filePath, string projectDirectory)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    return filePath;
                }

                // 如果文件已存在，直接返回
                if (File.Exists(filePath))
                {
                    return filePath;
                }

                // 如果是相对路径，尝试解析为绝对路径
                if (IsRelativePath(filePath))
                {
                    string absolutePath = ToAbsolutePath(filePath, projectDirectory);
                    if (File.Exists(absolutePath))
                    {
                        return absolutePath;
                    }
                }

                // 如果是绝对路径但文件不存在，尝试转换为相对路径再解析
                if (!IsRelativePath(filePath))
                {
                    string relativePath = ToRelativePath(filePath, projectDirectory);
                    if (relativePath != filePath) // 确实转换了
                    {
                        string newAbsolutePath = ToAbsolutePath(relativePath, projectDirectory);
                        if (File.Exists(newAbsolutePath))
                        {
                            return newAbsolutePath;
                        }
                    }
                }

                // 都不存在，返回原路径
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保文件存在失败: {ex.Message}");
                return filePath;
            }
        }
    }
}
