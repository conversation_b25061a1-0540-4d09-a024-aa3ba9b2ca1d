using Microsoft.Extensions.DependencyInjection;
using PNConfigTool.ViewModels;
using System;

namespace PNConfigTool.Services
{
    public class ServiceLocator
    {
        private static ServiceProvider? _serviceProvider;
        private static readonly ServiceCollection _services = new ServiceCollection();
        private static bool _isBuilt = false;

        static ServiceLocator()
        {
            ConfigureServices(_services);
            BuildServiceProvider();
        }

        private static void ConfigureServices(IServiceCollection services)
        {
            // Register services
            services.AddSingleton<IProjectService, ProjectService>();
            services.AddSingleton<IGSDMLService, GSDMLService>();
            
            // Register view models
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<NetworkConfigPageViewModel>();
            services.AddTransient<ControllerConfigPageViewModel>();
            services.AddTransient<DeviceCatalogPageViewModel>();
            services.AddTransient<GSDMLManagementViewModel>();
            
            // Navigation service requires a content control reference, which we can't provide at startup
            // Will be registered separately
        }
        
        /// <summary>
        /// 重新构建服务提供者，应用新注册的服务
        /// </summary>
        public static void BuildServiceProvider()
        {
            _serviceProvider = _services.BuildServiceProvider();
            _isBuilt = true;
            System.Diagnostics.Debug.WriteLine("ServiceLocator: 服务提供者已构建");
        }
        
        /// <summary>
        /// 注册单例服务
        /// </summary>
        public static void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            _services.AddSingleton<TService, TImplementation>();
            _isBuilt = false;
        }
        
        /// <summary>
        /// 注册已存在的服务实例
        /// </summary>
        public static void RegisterSingleton<TService>(TService instance)
            where TService : class
        {
            _services.AddSingleton(instance);
            _isBuilt = false;
            System.Diagnostics.Debug.WriteLine($"ServiceLocator: 已注册{typeof(TService).Name}服务实例");
        }

        public static T? GetService<T>() where T : class
        {
            if (!_isBuilt)
            {
                BuildServiceProvider();
            }
            
            if (_serviceProvider == null)
            {
                System.Diagnostics.Debug.WriteLine("ServiceLocator: 服务提供者为空");
                return null;
            }
            
            return _serviceProvider.GetService<T>();
        }

        public static IServiceScope CreateScope()
        {
            if (!_isBuilt)
            {
                BuildServiceProvider();
            }
            
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("ServiceLocator: 服务提供者尚未初始化");
            }
            
            return _serviceProvider.CreateScope();
        }
    }

    // Placeholder ViewModels until we implement them
    public class ControllerConfigPageViewModel : ViewModelBase
    {
        private readonly IProjectService _projectService;

        public ControllerConfigPageViewModel(IProjectService projectService)
        {
            _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
        }
    }

    public class DeviceCatalogPageViewModel : ViewModelBase
    {
        private readonly IProjectService _projectService;
        private readonly IGSDMLService _gsdmlService;

        public DeviceCatalogPageViewModel(IProjectService projectService, IGSDMLService gsdmlService)
        {
            _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
            _gsdmlService = gsdmlService ?? throw new ArgumentNullException(nameof(gsdmlService));
        }
    }
} 