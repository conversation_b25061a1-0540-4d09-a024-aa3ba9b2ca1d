//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.1026
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PNConfigLib.Gsd.Interpreter.checker.resources {
    using System;
    
    
    /// <summary>
    ///   Eine stark typisierte Ressourcenklasse zum Suchen von lokalisierten Zeichenfolgen usw.
    /// </summary>
    // Diese K<PERSON>e wur<PERSON> von der StronglyTypedResourceBuilder automatisch generiert
    // -Klasse über ein Tool wie ResGen oder Visual Studio automatisch generiert.
    // Um einen Member hinzuzufügen oder zu entfernen, bearbeiten Sie die .ResX-Datei und führen dann ResGen
    // mit der /str-Option erneut aus, oder Sie erstellen Ihr VS-Projekt neu.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Gibt die zwischengespeicherte ResourceManager-Instanz zurück, die von dieser Klasse verwendet wird.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PNConfigLib.Gsd.Interpreter.checker.resources.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Überschreibt die CurrentUICulture-Eigenschaft des aktuellen Threads für alle
        ///   Ressourcenzuordnungen, die diese stark typisierte Ressourcenklasse verwenden.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Checker, which supports GSD(ML) version &quot;{0}&quot;, is used for GSD(ML) with version &quot;{1}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00001001_1 {
            get {
                return ResourceManager.GetString("M_0x00001001_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Checker couldn&apos;t check GSD file (&quot;{0}&quot;) correctly! Please note that the mentioned GSD file is not completely checked. ähnelt.
        /// </summary>
        internal static string M_0x00001002_1 {
            get {
                return ResourceManager.GetString("M_0x00001002_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die !!!   Internal error !!!! Please note that the GSD file (&quot;{0}&quot;) is not completely checked. ähnelt.
        /// </summary>
        internal static string M_0x00001002_2 {
            get {
                return ResourceManager.GetString("M_0x00001002_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Could not find the appropriate checker version. Please note that the GSD file is not checked. ähnelt.
        /// </summary>
        internal static string M_0x00001002_3 {
            get {
                return ResourceManager.GetString("M_0x00001002_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The GSD file must have the extension &quot;.xml&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010002_1 {
            get {
                return ResourceManager.GetString("M_0x00010002_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Name of GSD must consist of at least 5 fields, separated by &quot;-&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010002_2 {
            get {
                return ResourceManager.GetString("M_0x00010002_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die GSD file name must begin with &quot;GSDML-&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010002_3 {
            get {
                return ResourceManager.GetString("M_0x00010002_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &quot;GSDML-&quot; in GSD name must be upper case. ähnelt.
        /// </summary>
        internal static string M_0x00010002_4 {
            get {
                return ResourceManager.GetString("M_0x00010002_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die GSD version field in GSD name must match &quot;Vx.y&quot; whereby &quot;x&quot; and &quot;y&quot; are unsigned numbers. ähnelt.
        /// </summary>
        internal static string M_0x00010002_5 {
            get {
                return ResourceManager.GetString("M_0x00010002_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &quot;V&quot; from the version field in GSD name must be upper case. ähnelt.
        /// </summary>
        internal static string M_0x00010002_6 {
            get {
                return ResourceManager.GetString("M_0x00010002_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Sixth field of GSD name (release time): hour must be within 0-23. ähnelt.
        /// </summary>
        internal static string M_0x00010002_7 {
            get {
                return ResourceManager.GetString("M_0x00010002_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Sixth field of GSD name (release time): minute must be within 0-59. ähnelt.
        /// </summary>
        internal static string M_0x00010002_8 {
            get {
                return ResourceManager.GetString("M_0x00010002_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Sixth field of GSD name (release time): second must be within 0-59. ähnelt.
        /// </summary>
        internal static string M_0x00010002_9 {
            get {
                return ResourceManager.GetString("M_0x00010002_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die After the vendor and device family name in GSD name, only a release date matching \&quot;yyyymmdd\&quot; and optionally a release time matching \&quot;hhmmss\&quot; may follow. ähnelt.
        /// </summary>
        internal static string M_0x00010002_a {
            get {
                return ResourceManager.GetString("M_0x00010002_a", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Release date of GSD name: year should be within 2000-2099. ähnelt.
        /// </summary>
        internal static string M_0x00010002_b {
            get {
                return ResourceManager.GetString("M_0x00010002_b", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Release date of GSD name: month must be within 1-12. ähnelt.
        /// </summary>
        internal static string M_0x00010002_c {
            get {
                return ResourceManager.GetString("M_0x00010002_c", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Release date of GSD name: day must be within 1-31. ähnelt.
        /// </summary>
        internal static string M_0x00010002_d {
            get {
                return ResourceManager.GetString("M_0x00010002_d", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Release date of GSD name: day exceeds length of month. ähnelt.
        /// </summary>
        internal static string M_0x00010002_e {
            get {
                return ResourceManager.GetString("M_0x00010002_e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem/@ID&apos; must be unique over all &apos;DeviceAccessPointItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010003_1 {
            get {
                return ResourceManager.GetString("M_0x00010003_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleItem/@ID&apos; must be unique over all &apos;ModuleItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010004_1 {
            get {
                return ResourceManager.GetString("M_0x00010004_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleItemRef/@ModuleItemTarget&apos; attribute must reference an existing module &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010004_2 {
            get {
                return ResourceManager.GetString("M_0x00010004_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ValueItem/@ID&apos; must be unique over all &apos;ValueItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010005_1 {
            get {
                return ResourceManager.GetString("M_0x00010005_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Ref/@ValueItemTarget&apos; attribute must reference an existing ValueItem &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010005_2 {
            get {
                return ResourceManager.GetString("M_0x00010005_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;GraphicItem/@ID&apos; must be unique over all &apos;GraphicItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010006_1 {
            get {
                return ResourceManager.GetString("M_0x00010006_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;GraphicItemRef/@GraphicItemTarget&apos; attribute must reference an existing GraphicItem &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010006_2 {
            get {
                return ResourceManager.GetString("M_0x00010006_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;CategoryItem/@ID&apos; must be unique over all &apos;CategoryItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010007_1 {
            get {
                return ResourceManager.GetString("M_0x00010007_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleInfo/@CategoryRef&apos; attribute must reference an existing CategoryItem &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010007_2 {
            get {
                return ResourceManager.GetString("M_0x00010007_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleInfo/@SubCategory1Ref&apos; attribute must reference an existing CategoryItem &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010007_3 {
            get {
                return ResourceManager.GetString("M_0x00010007_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;VirtualSubmoduleItem/@ID&apos; must be unique over all &apos;VirtualSubmoduleItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00010008_1 {
            get {
                return ResourceManager.GetString("M_0x00010008_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Text/@TextId&apos; attribute must be unique for all text IDs of the primary language. ähnelt.
        /// </summary>
        internal static string M_0x00010009_1 {
            get {
                return ResourceManager.GetString("M_0x00010009_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;xml:lang&apos; attribute must be unique in the &apos;ExternalTextList&apos;. The language &quot;{0}&quot; is more than one times defined. ähnelt.
        /// </summary>
        internal static string M_0x00010009_2 {
            get {
                return ResourceManager.GetString("M_0x00010009_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The language code &quot;en&quot; is not valid, because the PrimaryLanguage already contains the English texts. ähnelt.
        /// </summary>
        internal static string M_0x00010009_3 {
            get {
                return ResourceManager.GetString("M_0x00010009_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The language code is not a valid value as defined in ISO 639-1. ähnelt.
        /// </summary>
        internal static string M_0x00010009_4 {
            get {
                return ResourceManager.GetString("M_0x00010009_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Text/@TextId&apos; attribute must be unique for all text IDs of the language &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010009_5 {
            get {
                return ResourceManager.GetString("M_0x00010009_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;TextId&apos; reference attribute must reference an existing primary language text. ähnelt.
        /// </summary>
        internal static string M_0x00010009_6 {
            get {
                return ResourceManager.GetString("M_0x00010009_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;TextId&apos; reference attribute must reference an existing text of the language &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010009_7 {
            get {
                return ResourceManager.GetString("M_0x00010009_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The maximal data length is greater than the sum of maximal input and output length. ähnelt.
        /// </summary>
        internal static string M_0x0001000A_1 {
            get {
                return ResourceManager.GetString("M_0x0001000A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The maximal data length is lower than the highest value of maximal input or output length. ähnelt.
        /// </summary>
        internal static string M_0x0001000B_1 {
            get {
                return ResourceManager.GetString("M_0x0001000B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;@Length&apos; must be used, if the data type is &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_1 {
            get {
                return ResourceManager.GetString("M_0x0001000C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;@Length&apos; must be unequal to 0, if the data type is &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_2 {
            get {
                return ResourceManager.GetString("M_0x0001000C_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;DataItem/@Length(= {0})&apos; must match to &apos;DataItem/@DataType&apos; (length= {1}). ähnelt.
        /// </summary>
        internal static string M_0x0001000C_3 {
            get {
                return ResourceManager.GetString("M_0x0001000C_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;@Length&apos; must be greater or equal 3, if the data type is &quot;61131_STRING&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_4 {
            get {
                return ResourceManager.GetString("M_0x0001000C_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;@Length&apos; must be greater or equal 6 and an even number, if the data type is &quot;61131_WSTRING&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_5 {
            get {
                return ResourceManager.GetString("M_0x0001000C_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;@Length&apos; must be greater or equal 3 and &apos;@Length&apos; modulo 3 equal 0, if the data type is &quot;OctetString_S&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_6 {
            get {
                return ResourceManager.GetString("M_0x0001000C_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;DataItem/@Length&apos; is only allowed on (Octet)String data types with variable size. ähnelt.
        /// </summary>
        internal static string M_0x0001000C_7 {
            get {
                return ResourceManager.GetString("M_0x0001000C_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The length of all input &apos;DataItem&apos; or output &apos;DataItem&apos; elements within a &apos;IOData&apos; element plus the IOPS/IOCS length must not be greater than 1440 bytes. ähnelt.
        /// </summary>
        internal static string M_0x0001000D_1 {
            get {
                return ResourceManager.GetString("M_0x0001000D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of the &apos;Assign/@Content&apos; attribute must be unique within each &apos;ValueItem/Assignments&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x0001000E_1 {
            get {
                return ResourceManager.GetString("M_0x0001000E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of the &apos;Assign/@TextId&apos; attribute must be unique within each &apos;ValueItem/Assignments&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x0001000F_1 {
            get {
                return ResourceManager.GetString("M_0x0001000F_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;RecordDataList/*/@Index&apos; attribute value must be unique within each &apos;VirtualSubmoduleItem&apos; or &apos;SubmoduleItem&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x00010010_1 {
            get {
                return ResourceManager.GetString("M_0x00010010_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the transfer sequence is specified for the record data objects, the &apos;TransferSequence&apos; attribute must be specified and the values must be unequal to 0. ähnelt.
        /// </summary>
        internal static string M_0x00010011_1 {
            get {
                return ResourceManager.GetString("M_0x00010011_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the transfer sequence is specified for the record data objects, the &apos;TransferSequence&apos; attribute values must be unique within each &apos;VirtualSubmoduleItem&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x00010011_2 {
            get {
                return ResourceManager.GetString("M_0x00010011_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the transfer sequence is specified for the record data objects, the &apos;TransferSequence&apos; attribute values must be ascending and starting with 1. ähnelt.
        /// </summary>
        internal static string M_0x00010011_3 {
            get {
                return ResourceManager.GetString("M_0x00010011_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Ref/@BitOffset&apos; attribute can only be used in conjunction with data type &quot;Bit&quot; or &quot;BitArea&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010012_1 {
            get {
                return ResourceManager.GetString("M_0x00010012_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Ref/@BitLength&apos; attribute must be in the range of 1 to 15 for the data type &quot;BitArea&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010013_1 {
            get {
                return ResourceManager.GetString("M_0x00010013_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Ref/@BitLength&apos; attribute can only be used in conjunction with data type &quot;BitArea&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010013_2 {
            get {
                return ResourceManager.GetString("M_0x00010013_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleIdentNumber&apos; must not be 0x00000000. ähnelt.
        /// </summary>
        internal static string M_0x00010015_1 {
            get {
                return ResourceManager.GetString("M_0x00010015_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slots defined in &apos;{0}&apos; are not available in &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010016_1 {
            get {
                return ResourceManager.GetString("M_0x00010016_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Duplicate &apos;ModuleItemRef&apos; elements found (it means, they have the same target). ähnelt.
        /// </summary>
        internal static string M_0x00010016_2 {
            get {
                return ResourceManager.GetString("M_0x00010016_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slot {0} is reserved from another module. ähnelt.
        /// </summary>
        internal static string M_0x00010016_3 {
            get {
                return ResourceManager.GetString("M_0x00010016_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slots defined in &apos;{0}&apos; must not be available in &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010016_4 {
            get {
                return ResourceManager.GetString("M_0x00010016_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Without system redundancy, &apos;AllowedInSlots&apos; should not be used. ähnelt.
        /// </summary>
        internal static string M_0x00010016_5 {
            get {
                return ResourceManager.GetString("M_0x00010016_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For a non redundant DAP only one slot number is allowed in the list of &apos;FixedInSlots&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010016_6 {
            get {
                return ResourceManager.GetString("M_0x00010016_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Type&apos; (&quot;{0}&quot;) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010017_1 {
            get {
                return ResourceManager.GetString("M_0x00010017_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ErrorType&apos; ({0}) is not in the allowed range between {1} and {2}. ähnelt.
        /// </summary>
        internal static string M_0x00010018_1 {
            get {
                return ResourceManager.GetString("M_0x00010018_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ErrorType&apos; ({0}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010018_2 {
            get {
                return ResourceManager.GetString("M_0x00010018_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The combination &apos;ErrorType&apos; ({0}) and &apos;API&apos; ({1}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010018_3 {
            get {
                return ResourceManager.GetString("M_0x00010018_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;UserStructureIdentifier&apos; ({0}) is not in the range of {1} to {2}. ähnelt.
        /// </summary>
        internal static string M_0x00010019_1 {
            get {
                return ResourceManager.GetString("M_0x00010019_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;UserStructureIdentifier&apos; ({0}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010019_2 {
            get {
                return ResourceManager.GetString("M_0x00010019_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The combination &apos;UserStructureIdentifier&apos; ({0}) and &apos;API&apos; ({1}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010019_3 {
            get {
                return ResourceManager.GetString("M_0x00010019_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The offset combination &apos;ByteOffset&apos; ({0}) plus &apos;BitOffset&apos; ({1}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010020_1 {
            get {
                return ResourceManager.GetString("M_0x00010020_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Ref&apos; data entries overlap. ähnelt.
        /// </summary>
        internal static string M_0x00010020_2 {
            get {
                return ResourceManager.GetString("M_0x00010020_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Length&apos; of the data items ({0}) is lower than the &apos;Ref&apos; length ({1}). ähnelt.
        /// </summary>
        internal static string M_0x00010020_3 {
            get {
                return ResourceManager.GetString("M_0x00010020_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ByteOffset&apos; ({0}) is a duplicate. ähnelt.
        /// </summary>
        internal static string M_0x00010021_1 {
            get {
                return ResourceManager.GetString("M_0x00010021_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Const&apos; data entries are overlapped. ähnelt.
        /// </summary>
        internal static string M_0x00010021_2 {
            get {
                return ResourceManager.GetString("M_0x00010021_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Length&apos; of the data items ({0}) is lower than the &apos;Const&apos; length ({1}). ähnelt.
        /// </summary>
        internal static string M_0x00010021_3 {
            get {
                return ResourceManager.GetString("M_0x00010021_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Ref is changeable but not visible. ähnelt.
        /// </summary>
        internal static string M_0x00010022_1 {
            get {
                return ResourceManager.GetString("M_0x00010022_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DefaultValue&apos; ({0}) does not comply with the &apos;DataType&apos; (&quot;{1}&quot; with bit length {2}). ähnelt.
        /// </summary>
        internal static string M_0x00010022_2 {
            get {
                return ResourceManager.GetString("M_0x00010022_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DefaultValue&apos; ({0}) does not comply with the &apos;DataType&apos; (&quot;{1}&quot;). ähnelt.
        /// </summary>
        internal static string M_0x00010022_3 {
            get {
                return ResourceManager.GetString("M_0x00010022_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value ({0}) specified by &apos;AllowedValues&apos; does not comply with the &apos;DataType&apos; (&quot;{1}&quot; with bit length {2}). ähnelt.
        /// </summary>
        internal static string M_0x00010022_4 {
            get {
                return ResourceManager.GetString("M_0x00010022_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value ({0}) specified by &apos;AllowedValues&apos; does not comply with the &apos;DataType&apos; (&quot;{1}&quot;). ähnelt.
        /// </summary>
        internal static string M_0x00010022_5 {
            get {
                return ResourceManager.GetString("M_0x00010022_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ValueItem&apos; (ID &quot;{0}&quot;) assignments &apos;Content&apos; value ({1}) does not comply with the &apos;DataType&apos; (&quot;{2}&quot; with bit length {3}). ähnelt.
        /// </summary>
        internal static string M_0x00010022_6 {
            get {
                return ResourceManager.GetString("M_0x00010022_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ValueItem&apos; (ID &quot;{0}&quot;) assignments &apos;Content&apos; value ({1}) does not comply with the &apos;DataType&apos; (&quot;{2}&quot;). ähnelt.
        /// </summary>
        internal static string M_0x00010022_7 {
            get {
                return ResourceManager.GetString("M_0x00010022_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DefaultValue&apos; ({0}) is not available from &apos;ValueItem&apos; assignments &apos;Content&apos; attribute. ähnelt.
        /// </summary>
        internal static string M_0x00010022_8 {
            get {
                return ResourceManager.GetString("M_0x00010022_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value ({0}) specified by &apos;AllowedValues&apos; isn&apos;t available from &apos;ValueItem&apos; assignments &apos;Content&apos; attribute. ähnelt.
        /// </summary>
        internal static string M_0x00010022_9 {
            get {
                return ResourceManager.GetString("M_0x00010022_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DefaultValue&apos; ({0}) is not available from the &apos;AllowedValues&apos; ({1}). ähnelt.
        /// </summary>
        internal static string M_0x00010022_a {
            get {
                return ResourceManager.GetString("M_0x00010022_a", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;DataType&apos; = &quot;Float32&quot; or &apos;DataType&apos; = &quot;Float64&quot; &apos;AllowedValues&apos; must not contain a range: {0}..{1}. ähnelt.
        /// </summary>
        internal static string M_0x00010022_b {
            get {
                return ResourceManager.GetString("M_0x00010022_b", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;DataType&apos; (&quot;{0}&quot;) no AllowedValues must be given. ähnelt.
        /// </summary>
        internal static string M_0x00010022_c {
            get {
                return ResourceManager.GetString("M_0x00010022_c", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;DataType&apos; (&quot;{0}&quot;) no ValueItemTarget must be given. ähnelt.
        /// </summary>
        internal static string M_0x00010022_d {
            get {
                return ResourceManager.GetString("M_0x00010022_d", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;DataType&apos; (&quot;{0}&quot;) the DefaultValue does not fit. ähnelt.
        /// </summary>
        internal static string M_0x00010022_e {
            get {
                return ResourceManager.GetString("M_0x00010022_e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die At least one value ({0}) specified by the range: {1}..{2} in &apos;AllowedValues&apos; isn&apos;t available from &apos;ValueItem&apos; assignments &apos;Content&apos; attribute. ähnelt.
        /// </summary>
        internal static string M_0x00010022_f {
            get {
                return ResourceManager.GetString("M_0x00010022_f", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SendClock&apos; attribute must contain the mandatory value 32. ähnelt.
        /// </summary>
        internal static string M_0x00010023_1 {
            get {
                return ResourceManager.GetString("M_0x00010023_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/ApplicationProcess&apos; is missing. It must be given just one-time. ähnelt.
        /// </summary>
        internal static string M_0x00010024_1 {
            get {
                return ResourceManager.GetString("M_0x00010024_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceID&apos; must not be 0. ähnelt.
        /// </summary>
        internal static string M_0x00010025_1 {
            get {
                return ResourceManager.GetString("M_0x00010025_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Error in value of &apos;DeviceID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010025_2 {
            get {
                return ResourceManager.GetString("M_0x00010025_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;schemaLocation&apos; attribute must be given. ähnelt.
        /// </summary>
        internal static string M_0x00010026_1 {
            get {
                return ResourceManager.GetString("M_0x00010026_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;schemaLocation&apos; attribute does not contain the appropriate number of two entries. ähnelt.
        /// </summary>
        internal static string M_0x00010026_2 {
            get {
                return ResourceManager.GetString("M_0x00010026_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The first entry of the &apos;schemaLocation&apos; attribute must contain either the namespace: &quot;http://www.profibus.com/GSDML/2003/11/DeviceProfile&quot; or &quot;http://www.profibus.com/Common/2003/11/Primitives&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010026_3 {
            get {
                return ResourceManager.GetString("M_0x00010026_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The first entry of the &apos;schemaLocation&apos; attribute must contain either the namespace: &quot;http://www.profibus.com/GSDML/2003/11/DeviceProfile&quot; or &quot;http://www.profibus.com/GSDML/2003/11/Primitives&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010026_4 {
            get {
                return ResourceManager.GetString("M_0x00010026_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The second entry of the &apos;schemaLocation&apos; attribute must contain the device profile schema: &quot;..\xsd\GSDML-DeviceProfile-vX.Y.xsd&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010026_5 {
            get {
                return ResourceManager.GetString("M_0x00010026_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The second entry of the &apos;schemaLocation&apos; attribute must contain the primitoves schema: &quot;..\..\xsd\Common-Primitives-v1.0.xsd&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010026_6 {
            get {
                return ResourceManager.GetString("M_0x00010026_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The second entry of the &apos;schemaLocation&apos; attribute must contain the primitives schema: &quot;..\..\xsd\GSDML-Primitives-vX.Y.xsd&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010026_7 {
            get {
                return ResourceManager.GetString("M_0x00010026_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;schemaLocation&apos; attribute does not reference the appropriate schema file. ähnelt.
        /// </summary>
        internal static string M_0x00010026_8 {
            get {
                return ResourceManager.GetString("M_0x00010026_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Ref/@DataType&apos; = &quot;VisibleString&quot; or &apos;Ref/@DataType&apos; = &quot;OctetString&quot; not allowed, because &apos;Ref/@Length&apos; not available. &apos;Ref/@Length&apos; is available from GSDML V2.1 on. ähnelt.
        /// </summary>
        internal static string M_0x00010027_1 {
            get {
                return ResourceManager.GetString("M_0x00010027_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;Ref/@DefaultValue&apos; contains {0} octets, but does not contain the appropriate number of {1} octets. ähnelt.
        /// </summary>
        internal static string M_0x00010027_5 {
            get {
                return ResourceManager.GetString("M_0x00010027_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;Ref/@DefaultValue&apos; contains {0} characters, but must not contain more than {1} characters. ähnelt.
        /// </summary>
        internal static string M_0x00010027_6 {
            get {
                return ResourceManager.GetString("M_0x00010027_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The Element &apos;ProfileBody/DeviceIdentity&apos; is missing. It must be given just one-time. ähnelt.
        /// </summary>
        internal static string M_0x00010028_1 {
            get {
                return ResourceManager.GetString("M_0x00010028_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/DeviceIdentity&apos; is more than one-time given. It must be given just one-time. ähnelt.
        /// </summary>
        internal static string M_0x00010028_2 {
            get {
                return ResourceManager.GetString("M_0x00010028_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/DeviceManager&apos; is superfluous. ähnelt.
        /// </summary>
        internal static string M_0x00010029_1 {
            get {
                return ResourceManager.GetString("M_0x00010029_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/ExternalProfileHandle&apos; is superfluous. ähnelt.
        /// </summary>
        internal static string M_0x00010029_2 {
            get {
                return ResourceManager.GetString("M_0x00010029_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/DeviceFunction&apos; must be given just one-time. Superfluous definitions will be ignored. ähnelt.
        /// </summary>
        internal static string M_0x0001002A_1 {
            get {
                return ResourceManager.GetString("M_0x0001002A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileBody/ApplicationProcess&apos; must be given just one-time. Superfluous definitions will be ignored. ähnelt.
        /// </summary>
        internal static string M_0x0001002B_1 {
            get {
                return ResourceManager.GetString("M_0x0001002B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ProfileIdentification&apos; must have the value &quot;PROFINET Device Profile&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_1 {
            get {
                return ResourceManager.GetString("M_0x0001002C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ProfileRevision&apos; must have the value &quot;1.00&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_2 {
            get {
                return ResourceManager.GetString("M_0x0001002C_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ProfileName&apos; must have the value &quot;Device Profile for PROFINET Devices&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_3 {
            get {
                return ResourceManager.GetString("M_0x0001002C_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ProfileSource&apos; must have the value &quot;PROFIBUS Nutzerorganisation e. V. (PNO)&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_4 {
            get {
                return ResourceManager.GetString("M_0x0001002C_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ProfileClassID&apos; must have the value &quot;Device&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_5 {
            get {
                return ResourceManager.GetString("M_0x0001002C_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die  The element &apos;ProfileHeader/ISO15745Reference/ISO15745Part&apos; must have the value 4. You have given {0}. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_6 {
            get {
                return ResourceManager.GetString("M_0x0001002C_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ISO15745Reference/ISO15745Edition&apos; must have the value 1. You have given {0}. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_7 {
            get {
                return ResourceManager.GetString("M_0x0001002C_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;ProfileHeader/ISO15745Reference/ProfileTechnology&apos; must have the value &quot;GSDML&quot;. You have given &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001002C_8 {
            get {
                return ResourceManager.GetString("M_0x0001002C_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die As prefix for the schema instance namespace, http://www.w3.org/2001/XMLSchema-instance, &quot;xsi&quot; shall be used in the GSDML. ähnelt.
        /// </summary>
        internal static string M_0x0001002D_1 {
            get {
                return ResourceManager.GetString("M_0x0001002D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ParameterRecordDataItem/@Index&apos; attribute value (= {0}) must be in a range between 0..32767 for &apos;API&apos; == 0 (default) or in a range between 0..32767 or 0xB000..0xBFFF for &apos;API&apos; &lt;&gt; 0. ähnelt.
        /// </summary>
        internal static string M_0x0001002E_1 {
            get {
                return ResourceManager.GetString("M_0x0001002E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Profile specific records (&apos;ParameterRecordDataItem/@Index&apos; in a range between 0xB000 (45056) and 0xBFFF (49151)) may only be used if the corresponding submodule is defined in an &apos;API&apos; &lt;&gt; 0. ähnelt.
        /// </summary>
        internal static string M_0x0001002E_2 {
            get {
                return ResourceManager.GetString("M_0x0001002E_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;F_ParameterRecordDataItem/@Index&apos; attribute value (= {0}) must be in a range between 0..32767. ähnelt.
        /// </summary>
        internal static string M_0x0001002E_3 {
            get {
                return ResourceManager.GetString("M_0x0001002E_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;ApplicationProcess/ExternalTextList/Language/@xml:lang&apos; is mandatory. ähnelt.
        /// </summary>
        internal static string M_0x0001002F_1 {
            get {
                return ResourceManager.GetString("M_0x0001002F_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values contained within the &apos;(RT_Class3)TimingProperties/@ReductionRatio&apos; attribute must be lower or equal than 512 and higher or equal than 1. ähnelt.
        /// </summary>
        internal static string M_0x00010030_1 {
            get {
                return ResourceManager.GetString("M_0x00010030_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value {0} contained within the &apos;(RT_Class3)TimingProperties/@ReductionRatio&apos; attribute is not a power of 2. ähnelt.
        /// </summary>
        internal static string M_0x00010030_2 {
            get {
                return ResourceManager.GetString("M_0x00010030_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die At least one of the &apos;ReductionRatio&apos;, &apos;ReductionRatioPow2&apos;, &apos;ReductionRatioNonPow2&apos; attributes at the &apos;RT_Class3TimingProperties&apos; element must contain the mandatory values 1 2 4 8 16. ähnelt.
        /// </summary>
        internal static string M_0x00010030_3 {
            get {
                return ResourceManager.GetString("M_0x00010030_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to At least one of the &apos;ReductionRatio&apos;, &apos;ReductionRatioPow2&apos; attributes at the &apos;RT_Class3TimingProperties&apos; element must contain the mandatory values 1 2 4 8 16..
        /// </summary>
        internal static string M_0x00010030_3a
        {
            get
            {
                return ResourceManager.GetString("M_0x00010030_3a", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die At least one of the &apos;ReductionRatio&apos;, &apos;ReductionRatioPow2&apos;, &apos;ReductionRatioNonPow2&apos; attributes at the &apos;TimingProperties&apos; element must contain the mandatory values 1 2 4 8 16 32 64 128 256 512. ähnelt.
        /// </summary>
        internal static string M_0x00010030_4 {
            get {
                return ResourceManager.GetString("M_0x00010030_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to At least one of the &apos;ReductionRatio&apos;, &apos;ReductionRatioPow2&apos; attributes at the &apos;TimingProperties&apos; element must contain the mandatory values 1 2 4 8 16 32 64 128 256 512..
        /// </summary>
        internal static string M_0x00010030_4a
        {
            get
            {
                return ResourceManager.GetString("M_0x00010030_4a", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;IOPS_Length&apos; and &apos;IOCS_Length&apos; must always be 1. ähnelt.
        /// </summary>
        internal static string M_0x00010031_1 {
            get {
                return ResourceManager.GetString("M_0x00010031_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;RecordDataList/ParameterRecordDataItem&apos; must contain either a &apos;Const&apos; or a &apos;Ref&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x00010033_1 {
            get {
                return ResourceManager.GetString("M_0x00010033_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem/ARVendorBlock/Request&apos; must contain either a &apos;Const&apos; or a &apos;Ref&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x00010033_2 {
            get {
                return ResourceManager.GetString("M_0x00010033_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;ValueList/ValueItem&apos; must contain a &apos;Help&apos; and/or &apos;Assignments&apos; element. ähnelt.
        /// </summary>
        internal static string M_0x00010034_1 {
            get {
                return ResourceManager.GetString("M_0x00010034_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slot {0} can&apos;t be used for this module, because it is used permanently by &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010035_1 {
            get {
                return ResourceManager.GetString("M_0x00010035_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slot {0} can&apos;t be used twice. It is already used by &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010035_2 {
            get {
                return ResourceManager.GetString("M_0x00010035_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Could not find Schema: &quot;{0}\{1}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001003E_1 {
            get {
                return ResourceManager.GetString("M_0x0001003E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die {0}: File is not a GSD file. ähnelt.
        /// </summary>
        internal static string M_0x0001003E_2 {
            get {
                return ResourceManager.GetString("M_0x0001003E_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Neither from GSD file name nor from &apos;SchemaLocation&apos; a correct GSDML version could be extracted. ähnelt.
        /// </summary>
        internal static string M_0x0001003E_3 {
            get {
                return ResourceManager.GetString("M_0x0001003E_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Problem during validating GSDML file or creating XML document object. ähnelt.
        /// </summary>
        internal static string M_0x0001003E_4 {
            get {
                return ResourceManager.GetString("M_0x0001003E_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The subslot 0 is not allowed. ähnelt.
        /// </summary>
        internal static string M_0x00010043_1 {
            get {
                return ResourceManager.GetString("M_0x00010043_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The subslot 0 is only allowed if the pull module alarm is supported by the interface. ähnelt.
        /// </summary>
        internal static string M_0x00010043_2 {
            get {
                return ResourceManager.GetString("M_0x00010043_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Slot numbers must be in a range between 0 and 0x7FFF (32767). ähnelt.
        /// </summary>
        internal static string M_0x00010043_3 {
            get {
                return ResourceManager.GetString("M_0x00010043_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values for &apos;PhysicalSubslots&apos; must be in a range between 0 and 0x8FFF (36863). ähnelt.
        /// </summary>
        internal static string M_0x00010043_4 {
            get {
                return ResourceManager.GetString("M_0x00010043_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslot numbers that refer to a &apos;PortSubmoduleItem&apos; must be in a range between 0x8000 (32768) and 0x8FFF (36863). ähnelt.
        /// </summary>
        internal static string M_0x00010043_5 {
            get {
                return ResourceManager.GetString("M_0x00010043_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslot numbers that refer to a &quot;normal&quot; &apos;SubmoduleItem&apos; (not &apos;PortSubmoduleItem&apos;) must be in a range between 0 and 0x7FFF (32767). ähnelt.
        /// </summary>
        internal static string M_0x00010043_6 {
            get {
                return ResourceManager.GetString("M_0x00010043_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslot 0 in combination with PullModuleAlarmSupported=&quot;true&quot; is allowed, but might not work with all controllers. ähnelt.
        /// </summary>
        internal static string M_0x00010043_7 {
            get {
                return ResourceManager.GetString("M_0x00010043_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;RecordDataList/ParameterRecordDataItem/Const/@Data&apos;. Format should be like &quot;0x00,0x0a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010044_1 {
            get {
                return ResourceManager.GetString("M_0x00010044_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;ProfileBody/DeviceIdentity/@VendorID&apos;. Format should be like &quot;0x000a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010045_1 {
            get {
                return ResourceManager.GetString("M_0x00010045_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;ProfileBody/DeviceIdentity/@DeviceID&apos;. Format should be like &quot;0x000a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010046_1 {
            get {
                return ResourceManager.GetString("M_0x00010046_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;DeviceAccessPointList/DeviceAccessPointItem/@ModuleIdentNumber&apos;. Format should be like &quot;0x0000000a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010047_1 {
            get {
                return ResourceManager.GetString("M_0x00010047_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;DeviceAccessPointList/DeviceAccessPointItem/@DNS_CompatibleName&apos;. Format should be like &quot;Pno-Example-Dap&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010048_1 {
            get {
                return ResourceManager.GetString("M_0x00010048_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The total length of attribute &apos;DNS_CompatibleName&apos; must be &lt;= 240. ähnelt.
        /// </summary>
        internal static string M_0x00010048_2 {
            get {
                return ResourceManager.GetString("M_0x00010048_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The first label of attribute &apos;DNS_CompatibleName&apos; must not have the form &quot;port-xyz&quot; or &quot;port-xyz-abcde&quot; with a, b, c, d, e, x, y, z = 0..9. ähnelt.
        /// </summary>
        internal static string M_0x00010048_3 {
            get {
                return ResourceManager.GetString("M_0x00010048_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;DNS_CompatibleName&apos; must not have the form &quot;a.b.c.d&quot; with a, b, c, d = 0..999. ähnelt.
        /// </summary>
        internal static string M_0x00010048_4 {
            get {
                return ResourceManager.GetString("M_0x00010048_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;ModuleList/ModuleItem/@ModuleIdentNumber&apos;. Format should be like &quot;0x0000000a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010049_1 {
            get {
                return ResourceManager.GetString("M_0x00010049_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong format at attribute &apos;(Virtual)SubmoduleItem/@SubmoduleIdentNumber&apos;. Format should be like &quot;0x0000000a&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001004A_1 {
            get {
                return ResourceManager.GetString("M_0x0001004A_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;GraphicFile&apos; on &apos;GraphicsList/GraphicItem&apos; must contain the filename without file extension..
        /// </summary>
        internal static string M_0x0001004B_1 {
            get {
                return ResourceManager.GetString("M_0x0001004B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For the attribute &apos;AllowedValues&apos; it is useless to specify more than one value when the attribute &apos;Changeable&apos; =false..
        /// </summary>
        internal static string M_0x0001004C_1 {
            get {
                return ResourceManager.GetString("M_0x0001004C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;ResetToFactoryModes&apos; exceeds the maximum value of &quot;32767&quot;..
        /// </summary>
        internal static string M_0x0001004D_1 {
            get {
                return ResourceManager.GetString("M_0x0001004D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;SlotGroups/SlotGroup/SlotList&apos; exceeds the maximum value of &quot;32767&quot;..
        /// </summary>
        internal static string M_0x0001004D_2 {
            get {
                return ResourceManager.GetString("M_0x0001004D_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;SupportedSubstitutionModes&apos; exceeds the maximum value of Unsigned16..
        /// </summary>
        internal static string M_0x0001004D_3 {
            get {
                return ResourceManager.GetString("M_0x0001004D_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;MAUTypes&apos; exceeds the maximum value of Unsigned16..
        /// </summary>
        internal static string M_0x0001004D_4 {
            get {
                return ResourceManager.GetString("M_0x0001004D_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; uses at least one &apos;(Virtual)SubmoduleItem&apos; which requires IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_1 {
            get {
                return ResourceManager.GetString("M_0x00010102_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; uses a &apos;ModuleItem&apos; which has at least one &apos;VirtualSubmoduleItem&apos; which requires IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_2 {
            get {
                return ResourceManager.GetString("M_0x00010102_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem&apos; does not support IsochroneMode but has a submodule which supports IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_3 {
            get {
                return ResourceManager.GetString("M_0x00010102_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem&apos; does not support IsochroneMode but can be used together with modules which support IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_4 {
            get {
                return ResourceManager.GetString("M_0x00010102_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; does not support IsochroneMode but it can be used together with a &apos;SubmoduleItem&apos; which requires IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_5 {
            get {
                return ResourceManager.GetString("M_0x00010102_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; does not support IsochroneMode but it can be used together with a &apos;SubmoduleItem&apos; which supports IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010102_6 {
            get {
                return ResourceManager.GetString("M_0x00010102_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports IsochroneMode, but the attribute &apos;SupportedRT_Class&apos; is not set to &quot;Class2&quot; respectively &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010103_1 {
            get {
                return ResourceManager.GetString("M_0x00010103_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports IsochroneMode, but the attribute &apos;SupportedRT_Class&apos; is not set to &quot;Class2&quot; respectively &quot;Class3&quot; or &apos;SupportedRT_Classes&apos; does not contain &quot;RT_CLASS_2&quot; respectively &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010103_2 {
            get {
                return ResourceManager.GetString("M_0x00010103_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports &quot;RT_CLASS_2&quot; or &quot;RT_CLASS_3&quot;, but the element &apos;SynchronisationMode&apos; is not set. ähnelt.
        /// </summary>
        internal static string M_0x00010104_1 {
            get {
                return ResourceManager.GetString("M_0x00010104_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;SynchronisationMode&apos; is set, but the &apos;InterfaceSubmoduleItem&apos; does not support &quot;RT_CLASS_2&quot; or &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010104_2 {
            get {
                return ResourceManager.GetString("M_0x00010104_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports &quot;RT_CLASS_3&quot;, but the element &apos;RT_Class3Properties&apos; is not set. ähnelt.
        /// </summary>
        internal static string M_0x00010105_1 {
            get {
                return ResourceManager.GetString("M_0x00010105_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;RT_Class3Properties&apos; is set, but the &apos;InterfaceSubmoduleItem&apos; does not support &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010105_2 {
            get {
                return ResourceManager.GetString("M_0x00010105_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports &quot;RT_CLASS_3&quot;, but the related &apos;PortSubmoduleItem&apos; element does not have the needed attributes &apos;MaxPortRxDelay&apos; respectively &apos;MaxPortTxDelay&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010106_1 {
            get {
                return ResourceManager.GetString("M_0x00010106_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports &quot;RT_CLASS_3&quot;, but the &apos;PortSubmoduleItem&apos; element from the referenced &apos;ModuleItem&apos; does not have the needed attributes &apos;MaxPortRxDelay&apos; respectively &apos;MaxPortTxDelay&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010106_2 {
            get {
                return ResourceManager.GetString("M_0x00010106_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;T_DC_Min&apos; must be lower or equal than &apos;T_DC_Max&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010107_1 {
            get {
                return ResourceManager.GetString("M_0x00010107_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of &apos;T_DC_Min&apos; * &apos;T_DC_Base&apos; must be lower or equal than 1024. ähnelt.
        /// </summary>
        internal static string M_0x00010108_1 {
            get {
                return ResourceManager.GetString("M_0x00010108_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of &apos;T_DC_Max&apos; * &apos;T_DC_Base&apos; must be lower or equal than 1024. ähnelt.
        /// </summary>
        internal static string M_0x00010109_1 {
            get {
                return ResourceManager.GetString("M_0x00010109_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;T_IO_Base&apos; * &apos;T_IO_InputMin&apos; must be lower or equal than 32000000. ähnelt.
        /// </summary>
        internal static string M_0x0001010A_1 {
            get {
                return ResourceManager.GetString("M_0x0001010A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;T_IO_Base&apos; * &apos;T_IO_OutputMin&apos; must be lower or equal than 32000000. ähnelt.
        /// </summary>
        internal static string M_0x0001010B_1 {
            get {
                return ResourceManager.GetString("M_0x0001010B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem/ApplicationRelations&apos; is ignored because the DAP is not built according to PROFINET IO V1.0. &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/ApplicationRelations&apos; is evaluated instead. ähnelt.
        /// </summary>
        internal static string M_0x0001010D_1 {
            get {
                return ResourceManager.GetString("M_0x0001010D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DataItem/@UseAsBits&apos; attribute must be set to &apos;true&apos;, if the child element &apos;BitDataItem&apos; is specified. ähnelt.
        /// </summary>
        internal static string M_0x0001010E_1 {
            get {
                return ResourceManager.GetString("M_0x0001010E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;BitDataItem/@BitOffset&apos; attribute value is higher than allowed with the specified &apos;DataItem/@DataType&apos; and its bit length. ähnelt.
        /// </summary>
        internal static string M_0x0001010F_1 {
            get {
                return ResourceManager.GetString("M_0x0001010F_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DataItem/@DataType&apos; attribute values &quot;F_MessageTrailer4Byte&quot; and &quot;F_MessageTrailer5Byte&quot; can only be used, if the attribute &apos;PROFIsafeSupported&apos; of the &apos;(Virtual)SubmoduleItem&apos; element is set to &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010110_1 {
            get {
                return ResourceManager.GetString("M_0x00010110_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;IOData&apos; attributes &apos;F_IO_StructureDescVersion&apos; and &apos;F_IO_StructureDescCRC&apos; can only be used, if the attribute &apos;PROFIsafeSupported&apos; of the &apos;(Virtual)SubmoduleItem&apos; element is set to &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010110_2 {
            get {
                return ResourceManager.GetString("M_0x00010110_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SubslotNumber&apos; attribute value must be unique for each &apos;PortSubmoduleItem&apos; and &apos;InterfaceSubmoduleItem&apos; element within a &apos;ModuleItem&apos; or &apos;DeviceAccessPointItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010112_1 {
            get {
                return ResourceManager.GetString("M_0x00010112_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the &apos;(Virtual)SubmoduleItem/@PROFIsafeSupported&apos; attribute is set to &quot;true&quot;, the element &apos;RecordDataList/F_ParameterRecordDataItem&apos; must be specified and the &apos;IOData/@F_IO_StructureDescCRC&apos; attribute must be available. ähnelt.
        /// </summary>
        internal static string M_0x00010113_1 {
            get {
                return ResourceManager.GetString("M_0x00010113_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the &apos;(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem&apos; element is available, the attribute &apos;PROFIsafeSupported&apos; must be set to &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010114_1 {
            get {
                return ResourceManager.GetString("M_0x00010114_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If more than one &apos;VirtualSubmoduleItem&apos; element within a &apos;VirtualSubmoduleList&apos; is specified, the attribute &apos;FixedInSubslots&apos; must be used for all these elements. ähnelt.
        /// </summary>
        internal static string M_0x00010116_1 {
            get {
                return ResourceManager.GetString("M_0x00010116_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The specified subslot number (&apos;SubslotItem/@SubslotNumber&apos;) must be also available as a real subslot number of a &apos;InterfaceSubmoduleItem/@SubslotNumber&apos; or &apos;PortSubmoduleItem/@SubslotNumber&apos; or &apos;@PhysicalSubslots&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010117_1 {
            get {
                return ResourceManager.GetString("M_0x00010117_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The specified subslot number (&apos;SubslotItem/@SubslotNumber&apos;) must be also available as a real subslot number of a Submodule (&apos;VirtualSubmoduleItem/@FixedInSubslots&apos;, &apos;@PhysicalSubslots&apos;). ähnelt.
        /// </summary>
        internal static string M_0x00010117_2 {
            get {
                return ResourceManager.GetString("M_0x00010117_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values contained within the &apos;VirtualSubmoduleItem/@FixedInSubslots&apos; attribute must not be duplicates. ähnelt.
        /// </summary>
        internal static string M_0x00010118_1 {
            get {
                return ResourceManager.GetString("M_0x00010118_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values contained within the &apos;VirtualSubmoduleItem/@FixedInSubslots&apos; attribute must be lower than 32768 (0x8000). ähnelt.
        /// </summary>
        internal static string M_0x00010118_2 {
            get {
                return ResourceManager.GetString("M_0x00010118_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values contained within the &apos;(RT_Class3)TimingProperties/@SendClock&apos; attribute must be higher or equal than {0} and lower or equal than {1}. ähnelt.
        /// </summary>
        internal static string M_0x00010119_1 {
            get {
                return ResourceManager.GetString("M_0x00010119_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SendClock&apos; attribute must contain the mandatory value 32. ähnelt.
        /// </summary>
        internal static string M_0x00010119_2 {
            get {
                return ResourceManager.GetString("M_0x00010119_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values for &apos;F_Source_Add/@AllowedValues&apos; or &apos;F_Dest_Add/@AllowedValues&apos; attribute must be in the range of 1 to 65534. ähnelt.
        /// </summary>
        internal static string M_0x0001011B_1 {
            get {
                return ResourceManager.GetString("M_0x0001011B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values for &apos;F_WD_Time/@AllowedValues&apos; attribute must be in the range of 1 to 65535. ähnelt.
        /// </summary>
        internal static string M_0x0001011C_1 {
            get {
                return ResourceManager.GetString("M_0x0001011C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;RequiredSchemaVersion&apos; is higher than the GSDML version of this GSD file. ähnelt.
        /// </summary>
        internal static string M_0x0001011F_1 {
            get {
                return ResourceManager.GetString("M_0x0001011F_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;RequiredSchemaVersion&apos; attribute references a non-existing GSDML version. ähnelt.
        /// </summary>
        internal static string M_0x0001011F_2 {
            get {
                return ResourceManager.GetString("M_0x0001011F_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;ApplicationProcess/SubmoduleList/{0}/@RequiredSchemaVersion&apos; must be &gt;= {1}, because prior to GSDML version V{1} no pluggable (port)submodules existed. ähnelt.
        /// </summary>
        internal static string M_0x0001011F_3 {
            get {
                return ResourceManager.GetString("M_0x0001011F_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The &apos;RequiredSchemaVersion&apos; is not given in a valid format..
        /// </summary>
        internal static string M_0x0001011F_4
        {
            get
            {
                return ResourceManager.GetString("M_0x0001011F_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No gaps allowed in &apos;AllowedValues&apos; attribute. ähnelt.
        /// </summary>
        internal static string M_0x00010120_1 {
            get {
                return ResourceManager.GetString("M_0x00010120_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; supports IsochroneMode, but none of its modules can use IsochroneMode. ähnelt.
        /// </summary>
        internal static string M_0x00010121_1 {
            get {
                return ResourceManager.GetString("M_0x00010121_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules: For a &apos;DataType&apos; other than &quot;Unsigned8&quot;, &quot;Unsigned16&quot; or &quot;Unsigned32&quot; &apos;UseAsBits&apos; shall not be set. ähnelt.
        /// </summary>
        internal static string M_0x00010122_1 {
            get {
                return ResourceManager.GetString("M_0x00010122_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules: The DataType &quot;Unsigned8&quot;, &quot;Unsigned16&quot; or &quot;Unsigned32&quot; requires &apos;UseAsBits&apos; = &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010122_2 {
            get {
                return ResourceManager.GetString("M_0x00010122_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;IOData/Input&apos; must be available. ähnelt.
        /// </summary>
        internal static string M_0x00010122_3 {
            get {
                return ResourceManager.GetString("M_0x00010122_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;IOData/Output&apos; must be available. ähnelt.
        /// </summary>
        internal static string M_0x00010122_4 {
            get {
                return ResourceManager.GetString("M_0x00010122_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;IOData&apos; at least one &apos;DataItem&apos; with &apos;DataType&apos; equal &quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot; must be available for &apos;Input&apos; and &apos;Output&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010122_5 {
            get {
                return ResourceManager.GetString("M_0x00010122_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For packing boolean channels, it is strongly advised to use only DataItems with DataType=&quot;Unsigned8&quot;. This warning points to the first occurrence, but it might be other ones under this PROFIsafe submodule. ähnelt.
        /// </summary>
        internal static string M_0x00010122_6 {
            get {
                return ResourceManager.GetString("M_0x00010122_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules in &apos;IOData&apos; a message trailer (&quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot;) must be defined for &apos;Input&apos; and &apos;Output&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010122_8 {
            get {
                return ResourceManager.GetString("M_0x00010122_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The same message trailer must be used for &apos;IOData/Input&apos; and &apos;IOData/Output&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010122_9 {
            get {
                return ResourceManager.GetString("M_0x00010122_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The given &apos;Length&apos; = {0} doesn&apos;t match with &apos;DataType&apos; = &quot;{1}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010122_b {
            get {
                return ResourceManager.GetString("M_0x00010122_b", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;IOData&apos; the message trailer bytes for &apos;Input&apos; (&quot;{0}&quot;) and &apos;Output&apos; (&quot;{1}&quot;) must be identical. ähnelt.
        /// </summary>
        internal static string M_0x00010122_c {
            get {
                return ResourceManager.GetString("M_0x00010122_c", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;Consistency&apos;=&quot;All items consistency&quot; must be given for &apos;Input&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010123_1 {
            get {
                return ResourceManager.GetString("M_0x00010123_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules &apos;Consistency&apos;=&quot;All items consistency&quot; must be given for &apos;Output&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010124_1 {
            get {
                return ResourceManager.GetString("M_0x00010124_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_CRC_Length&apos;: &quot;2-Byte-CRC&quot; is allowed in V1-mode only, which is not applicable with PROFINET and must not be used for &apos;DefaultValue&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010125_1 {
            get {
                return ResourceManager.GetString("M_0x00010125_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_CRC_Length&apos;: &quot;2-Byte-CRC&quot; is allowed in V1-mode only, which is not applicable with PROFINET and must not be used for &apos;AllowedValues&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010125_2 {
            get {
                return ResourceManager.GetString("M_0x00010125_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_CRC_Length&apos;: The default value (= {0}) is not contained in allowed values (= {1}). ähnelt.
        /// </summary>
        internal static string M_0x00010125_3 {
            get {
                return ResourceManager.GetString("M_0x00010125_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &quot;4-Byte-CRC&quot; must be used when using &apos;Input&apos; or &apos;Output&apos; data with more than {0} bytes. ähnelt.
        /// </summary>
        internal static string M_0x00010125_4 {
            get {
                return ResourceManager.GetString("M_0x00010125_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &quot;F_MessageTrailer5Byte&quot; is necessary when using a &quot;4-Byte-CRC&quot; as default value. ähnelt.
        /// </summary>
        internal static string M_0x00010125_5 {
            get {
                return ResourceManager.GetString("M_0x00010125_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &quot;F_MessageTrailer4Byte&quot; is necessary when using a &quot;3-Byte-CRC&quot; as default value. ähnelt.
        /// </summary>
        internal static string M_0x00010125_6 {
            get {
                return ResourceManager.GetString("M_0x00010125_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue&apos;: &quot;4-Byte-CRC&quot; is mandatory for PROFIsafe V2.6.1. ähnelt.
        /// </summary>
        internal static string M_0x00010125_7 {
            get {
                return ResourceManager.GetString("M_0x00010125_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_CRC_Length/@AllowedValues&apos;: &quot;4-Byte-CRC&quot; is mandatory for PROFIsafe V2.6.1. ähnelt.
        /// </summary>
        internal static string M_0x00010125_8 {
            get {
                return ResourceManager.GetString("M_0x00010125_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &quot;4-Byte-CRC&quot; a maximum of {0} bytes &apos;Input&apos; or &apos;Output&apos; is defined. You use {1} bytes. ähnelt.
        /// </summary>
        internal static string M_0x00010125_9 {
            get {
                return ResourceManager.GetString("M_0x00010125_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &quot;4-Byte-CRC&quot; {0} bytes &apos;Input&apos; or &apos;Output&apos; are guaranteed. You use {1} bytes. This may not work with all PROFIsafe hosts. ähnelt.
        /// </summary>
        internal static string M_0x00010125_a {
            get {
                return ResourceManager.GetString("M_0x00010125_a", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;F_ParameterRecordDataItem/F_CRC_Length/@DefaultValue&apos;: &quot;4-Byte-CRC&quot; should not be used for PROFIsafe V2.4..
        /// </summary>
        internal static string M_0x00010125_b
        {
            get
            {
                return ResourceManager.GetString("M_0x00010125_b", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_SIL&apos;: The default value (= {0}) is not contained in allowed values (= {0}). ähnelt.
        /// </summary>
        internal static string M_0x00010126_1 {
            get {
                return ResourceManager.GetString("M_0x00010126_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_WD_Time&apos;: The default value (= {0}) is not contained in allowed values (= {1}..{2}). ähnelt.
        /// </summary>
        internal static string M_0x00010127_1 {
            get {
                return ResourceManager.GetString("M_0x00010127_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/@F_Par_CRC&apos;: The CRC over all default values is {0}, but should be {1}. ähnelt.
        /// </summary>
        internal static string M_0x00010128_1 {
            get {
                return ResourceManager.GetString("M_0x00010128_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/@F_ParamDescCRC&apos;: The CRC over all F-parameters is {0}, but should be {1}. ähnelt.
        /// </summary>
        internal static string M_0x00010128_2 {
            get {
                return ResourceManager.GetString("M_0x00010128_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &quot;&apos;(Virtual)SubmoduleItem/IOData/@F_IO_StructureDescCRC&apos;: The CRC over all IOData is {0}, but should be {1}. ähnelt.
        /// </summary>
        internal static string M_0x00010129_1 {
            get {
                return ResourceManager.GetString("M_0x00010129_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules, the &apos;Input/DataItem&apos; elements have to be ordered according to their &apos;DataType&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010131_1 {
            get {
                return ResourceManager.GetString("M_0x00010131_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules, the &apos;Output/DataItem&apos; elements have to be ordered according to their &apos;DataType&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00010131_2 {
            get {
                return ResourceManager.GetString("M_0x00010131_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The PROFIsafe specification requires hosts to support at least 64 boolean input channels. This submodule exceeds this number and thus may not work with all PROFIsafe hosts. ähnelt.
        /// </summary>
        internal static string M_0x00010131_3 {
            get {
                return ResourceManager.GetString("M_0x00010131_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The PROFIsafe specification requires hosts to support at least 64 boolean output channels. This submodule exceeds this number and thus may not work with all PROFIsafe hosts. ähnelt.
        /// </summary>
        internal static string M_0x00010131_4 {
            get {
                return ResourceManager.GetString("M_0x00010131_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules in &apos;IOData&apos; a &apos;DataItem&apos; with &apos;DataType&apos; equal &quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot; must be the last one, and not &quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00010131_5 {
            get {
                return ResourceManager.GetString("M_0x00010131_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules in &apos;IOData&apos; the &apos;DataType&apos; &quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot; must not be double defined. ähnelt.
        /// </summary>
        internal static string M_0x00010131_6 {
            get {
                return ResourceManager.GetString("M_0x00010131_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DataType&apos; &quot;{0}&quot; is not allowed for PROFIsafe. ähnelt.
        /// </summary>
        internal static string M_0x00010131_7 {
            get {
                return ResourceManager.GetString("M_0x00010131_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules, the list of boolean channels on &apos;Input/DataItem&apos; elements with DataType=&quot;UnsignedXX&quot; and UseAsBits=&quot;true&quot;, may not have holes in it. ähnelt.
        /// </summary>
        internal static string M_0x00010131_8 {
            get {
                return ResourceManager.GetString("M_0x00010131_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For F-Submodules, the list of boolean channels on &apos;Output/DataItem&apos; elements with DataType=&quot;UnsignedXX&quot; and UseAsBits=&quot;true&quot;, may not have holes in it. ähnelt.
        /// </summary>
        internal static string M_0x00010131_9 {
            get {
                return ResourceManager.GetString("M_0x00010131_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Visible&apos;=&quot;false&quot; does not only hide the F-parameter &apos;{0}&apos;, but also sets its value to &quot;{1}&quot;, which is not the same as the DefaultValue. This is probably not intended. ähnelt.
        /// </summary>
        internal static string M_0x00010132_1 {
            get {
                return ResourceManager.GetString("M_0x00010132_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;SupportedRT_Class&apos; is &quot;Class3&quot; and/or &apos;SupportedRT_Classes&apos; contains &quot;RT_CLASS_3&quot;, the modules pluggable with this DAP (ID = &quot;{0}&quot;) shall not carry PortSubmodules. ähnelt.
        /// </summary>
        internal static string M_0x00010133_1 {
            get {
                return ResourceManager.GetString("M_0x00010133_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For a &apos;DataType&apos; other than &quot;Unsigned8&quot;, &quot;Unsigned16&quot; or &quot;Unsigned32&quot; or &quot;Unsigned64&quot; or &quot;OctetString&quot; or &quot;V2&quot; &apos;UseAsBits&apos; shall not be set. ähnelt.
        /// </summary>
        internal static string M_0x00010134_1 {
            get {
                return ResourceManager.GetString("M_0x00010134_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No PortSubmoduleItem is configurable with this DAP. ähnelt.
        /// </summary>
        internal static string M_0x00010135_1 {
            get {
                return ResourceManager.GetString("M_0x00010135_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A DAP with PROFINET Version = 1.0 must not have any configurable PortSubmoduleItem..
        /// </summary>
        internal static string M_0x00010135_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00010135_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For GSDML V2.0 &apos;InterfaceSubmoduleItem/@SubslotNumber&apos; must have a fixed (correct) value of 32768..
        /// </summary>
        internal static string M_0x00010136_1 {
            get {
                return ResourceManager.GetString("M_0x00010136_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From GSDML V2.1 on &apos;InterfaceSubmoduleItem/@SubslotNumber&apos; must have one of the following values: 32768 (0x8000 – Interface 1), 33024 (0x8100 – Interface 2) to 36608 (0x8F00 – Interface 16)..
        /// </summary>
        internal static string M_0x00010136_2 {
            get {
                return ResourceManager.GetString("M_0x00010136_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_Block_ID&apos; with &apos;DefaultValue&apos; 1, 3, 5 or 7 but no &apos;F_iPar_CRC&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011101_1 {
            get {
                return ResourceManager.GetString("M_0x00011101_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; must have either a &apos;VirtualSubmoduleList&apos;, a &apos;SystemDefinedSubmoduleList&apos; or a &apos;UseableSubmodules&apos; list. ähnelt.
        /// </summary>
        internal static string M_0x00011104_1 {
            get {
                return ResourceManager.GetString("M_0x00011104_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ModuleItem&apos; must have either a &apos;VirtualSubmoduleList&apos;, a &apos;SystemDefinedSubmoduleList&apos; or a &apos;UseableSubmodules&apos; list. ähnelt.
        /// </summary>
        internal static string M_0x00011104_2 {
            get {
                return ResourceManager.GetString("M_0x00011104_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DeviceAccessPointItem&apos; or &apos;ModuleItem&apos; has an &apos;UseableSubmodules&apos; element but there are no &apos;PhysicalSubslots&apos; defined. ähnelt.
        /// </summary>
        internal static string M_0x00011106_1 {
            get {
                return ResourceManager.GetString("M_0x00011106_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Physical subslot {0} is also used for &apos;VirtualSubmoduleItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011106_4 {
            get {
                return ResourceManager.GetString("M_0x00011106_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Duplicate &apos;SubmoduleItemRef&apos; elements found (it means, they have the same target). ähnelt.
        /// </summary>
        internal static string M_0x00011106_5 {
            get {
                return ResourceManager.GetString("M_0x00011106_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslots defined in &apos;{0}&apos; aren&apos;t available in &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011106_6 {
            get {
                return ResourceManager.GetString("M_0x00011106_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslots defined in &apos;{0}&apos; must not be available in &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011106_7 {
            get {
                return ResourceManager.GetString("M_0x00011106_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Physical subslot {0} is also used for &apos;PortSubmoduleItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011106_8 {
            get {
                return ResourceManager.GetString("M_0x00011106_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Physical subslot {0} is also used for &apos;InterfaceSubmoduleItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011106_9 {
            get {
                return ResourceManager.GetString("M_0x00011106_9", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to More than 1000 physical subslots are given. Check 0x00011106 will be stopped for performance reasons..
        /// </summary>
        internal static string M_0x00011106_a
        {
            get
            {
                return ResourceManager.GetString("M_0x00011106_a", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SubmoduleItem/@ID&apos; must be unique for all &apos;SubmoduleItem&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x00011107_1 {
            get {
                return ResourceManager.GetString("M_0x00011107_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SubmoduleItemRef/@SubmoduleItemTarget&apos; attribute must reference an existing submodule &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011107_2 {
            get {
                return ResourceManager.GetString("M_0x00011107_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Invalid value in &apos;Writeable_IM_Records&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011108_1 {
            get {
                return ResourceManager.GetString("M_0x00011108_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Writeable_IM_Records&apos; must not contain 5. ähnelt.
        /// </summary>
        internal static string M_0x00011108_2 {
            get {
                return ResourceManager.GetString("M_0x00011108_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The mandatory value &quot;RT_CLASS_1&quot; is missing. ähnelt.
        /// </summary>
        internal static string M_0x00011109_1 {
            get {
                return ResourceManager.GetString("M_0x00011109_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Supporting &quot;RT_CLASS_2&quot; without &quot;RT_CLASS_3&quot; is not recommended. ähnelt.
        /// </summary>
        internal static string M_0x00011109_2 {
            get {
                return ResourceManager.GetString("M_0x00011109_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Mismatch between attributes &apos;SupportedRT_Class&apos; and &apos;SupportedRT_Classes&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00011109_3 {
            get {
                return ResourceManager.GetString("M_0x00011109_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;FiberOpticTypes&apos; is not given, attribute &apos;PowerBudgetControlSupported&apos; shall not be present. ähnelt.
        /// </summary>
        internal static string M_0x0001119A_1 {
            get {
                return ResourceManager.GetString("M_0x0001119A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DataItem/@DataType&apos;=&quot;{0}&quot; is not allowed, because &apos;DataItem/@Length&apos; not available. &apos;DataItem/@Length&apos; is available from GSDML V2.2 on. ähnelt.
        /// </summary>
        internal static string M_0x0001119B_1 {
            get {
                return ResourceManager.GetString("M_0x0001119B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;DataItem/@Length&apos; must be used, if &apos;DataItem/@DataType&apos;=&quot;{0}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001119B_2 {
            get {
                return ResourceManager.GetString("M_0x0001119B_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;DataItem/@Length&apos;(={0}) must match to &apos;DataItem/@DataType&apos; (length={1}). ähnelt.
        /// </summary>
        internal static string M_0x0001119B_3 {
            get {
                return ResourceManager.GetString("M_0x0001119B_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The sum of all &apos;DataItem&apos; elements in a &apos;ExtChannelAddValue&apos; shall describe exactly 4 octets, you have {0} octets. ähnelt.
        /// </summary>
        internal static string M_0x0001119C_1 {
            get {
                return ResourceManager.GetString("M_0x0001119C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The combination &apos;ChannelDiagItem/ExtChannelDiagList/ProfileExtChannelDiagItem&apos; is not allowed from GSDML V2.2 on. ähnelt.
        /// </summary>
        internal static string M_0x0001119D_2 {
            get {
                return ResourceManager.GetString("M_0x0001119D_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die In the text for text id &quot;{0}&quot; the used id (= {1}) is not defined as an id in the corresponding data items. ähnelt.
        /// </summary>
        internal static string M_0x0001119E_1 {
            get {
                return ResourceManager.GetString("M_0x0001119E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die In the text for text id &quot;{0}&quot; the format &quot;({1}:{2})&quot; does not match to the data type &quot;{3}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0001119E_2 {
            get {
                return ResourceManager.GetString("M_0x0001119E_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to In the text &quot;{0}&quot; the format does not match the following expression: &quot;{1}&quot;..
        /// </summary>
        internal static string M_0x0001119E_3
        {
            get
            {
                return ResourceManager.GetString("M_0x0001119E_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The DAP (ID = &quot;{0}&quot;) has no fixed Submodule where &apos;Writeable_IM_Records&apos; contains the values 1-3. ähnelt.
        /// </summary>
        internal static string M_0x000111A0_1 {
            get {
                return ResourceManager.GetString("M_0x000111A0_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Double defined tokens in token list. ähnelt.
        /// </summary>
        internal static string M_0x000111A1_1 {
            get {
                return ResourceManager.GetString("M_0x000111A1_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The token &quot;{0}&quot; is not a known token for attribute &apos;{1}&apos; in this GSDML version. ähnelt.
        /// </summary>
        internal static string M_0x000111A1_2 {
            get {
                return ResourceManager.GetString("M_0x000111A1_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No known token in token list. ähnelt.
        /// </summary>
        internal static string M_0x000111A1_3 {
            get {
                return ResourceManager.GetString("M_0x000111A1_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;ProfileExtChannelDiagItem/@API&apos; must have the same value as the attribute &apos;API&apos; of the overlying &apos;ProfileChannelDiagItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x000111A2_1 {
            get {
                return ResourceManager.GetString("M_0x000111A2_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;API&apos; must be greater than 0. ähnelt.
        /// </summary>
        internal static string M_0x000111A3_1 {
            get {
                return ResourceManager.GetString("M_0x000111A3_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No known value in value list. ähnelt.
        /// </summary>
        internal static string M_0x000111A4_1 {
            get {
                return ResourceManager.GetString("M_0x000111A4_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value {0} is not a known value for attribute &apos;{1}&apos; in this GSDML version. ähnelt.
        /// </summary>
        internal static string M_0x000111A4_2 {
            get {
                return ResourceManager.GetString("M_0x000111A4_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The MAU type in attribute &apos;MAUType&apos; should also be available in attribute &apos;MAUTypes&apos;. ähnelt.
        /// </summary>
        internal static string M_0x000111A5_1 {
            get {
                return ResourceManager.GetString("M_0x000111A5_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslots defined in &apos;VirtualSubmoduleList/VirtualSubmoduleItem/{0}&apos; must not be available in &apos;{1}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x000111A6_1 {
            get {
                return ResourceManager.GetString("M_0x000111A6_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If only one VirtualSubmoduleItem is given, the default value for FixedInSubslots is 1. Thus 1 must not be available in &apos;{0}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x000111A6_2 {
            get {
                return ResourceManager.GetString("M_0x000111A6_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Subslots defined in &apos;SystemDefinedSubmoduleList/*SubmoduleItem/{0}&apos; must not be available in &apos;{1}&apos; ähnelt.
        /// </summary>
        internal static string M_0x000111A6_3 {
            get {
                return ResourceManager.GetString("M_0x000111A6_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If for &apos;SystemDefinedSubmoduleList/InterfaceSubmoduleItem&apos; the attribute SubslotNumber is missing, the default value is 32768. Thus 32768 must not be available in &apos;{0}&apos;. ähnelt.
        /// </summary>
        internal static string M_0x000111A6_4 {
            get {
                return ResourceManager.GetString("M_0x000111A6_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;ChannelDiagList/(Profile)ChannelDiagItem/@MaintenanceAlarmState&apos; = &quot;QD&quot; is only allowed with extended channel diagnosis. ähnelt.
        /// </summary>
        internal static string M_0x000111A7_1 {
            get {
                return ResourceManager.GetString("M_0x000111A7_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Attributes of type Tokenlist must not contain a &quot;[&quot;, &quot;]&quot;, &quot;\&quot;, &quot;^&quot; or &quot;&apos;&quot; in the value string..
        /// </summary>
        internal static string M_0x000111A8_1 {
            get {
                return ResourceManager.GetString("M_0x000111A8_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attributes of type Tokenlist must not start or end with &quot;;&quot;..
        /// </summary>
        internal static string M_0x000111A8_2 {
            get {
                return ResourceManager.GetString("M_0x000111A8_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attributes of type Tokenlist must not contain &quot;;;&quot; in the value string..
        /// </summary>
        internal static string M_0x000111A8_3 {
            get {
                return ResourceManager.GetString("M_0x000111A8_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of &apos;F_IO_StructureDescCRC&apos; requires a &apos;F_IO_StructureDescVersion&apos; with a value greater or equal than 2. ähnelt.
        /// </summary>
        internal static string M_0x00012101_1 {
            get {
                return ResourceManager.GetString("M_0x00012101_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Value contained within the &apos;DeviceAccessPointItem/@MaxSupportedRecordSize&apos; attribute must be at least {0}. ähnelt.
        /// </summary>
        internal static string M_0x00012104_1 {
            get {
                return ResourceManager.GetString("M_0x00012104_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Attribute &apos;InterfaceSubmoduleItem/@SupportedRT_Classes&apos; is not present, while using &apos;InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012105_1 {
            get {
                return ResourceManager.GetString("M_0x00012105_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Class type &quot;{0}&quot; contained within the &apos;InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; attribute is not supported. Each class shall be separated by a semicolon. One or more of the following class types can be used: &quot;RT_CLASS_2&quot; and or &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00012105_2 {
            get {
                return ResourceManager.GetString("M_0x00012105_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Class type within the &apos;InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; attribute is not supported in &apos;InterfaceSubmoduleItem/@SupportedRT_Classes&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012105_3 {
            get {
                return ResourceManager.GetString("M_0x00012105_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Class type within the &apos;InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; attribute is not supported in &apos;InterfaceSubmoduleItem/@SupportedRT_Class&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012105_4 {
            get {
                return ResourceManager.GetString("M_0x00012105_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Class type &quot;{0}&quot; contained within the &apos;InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; attribute is not supported. Each class shall be separated by a semicolon. One or more of the following class types can be used: &quot;RT_CLASS_3&quot; and or &quot;RT_CLASS_STREAM_HI&quot;..
        /// </summary>
        internal static string M_0x00012105_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00012105_5", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die A &apos;DataItem&apos; with the &apos;DataType&apos; &quot;Integer32&quot; or &quot;Unsigned8+Unsigned8&quot; is used. That requires a &apos;F_IO_StructureDescVersion&apos; with a value greater or equal than 2. ähnelt.
        /// </summary>
        internal static string M_0x00012107_1 {
            get {
                return ResourceManager.GetString("M_0x00012107_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Block_ID&apos;: The default value (= {0}) is not contained in allowed values (= {1}..{2}). ähnelt.
        /// </summary>
        internal static string M_0x00012108_1 {
            get {
                return ResourceManager.GetString("M_0x00012108_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Block_ID&apos;: If none of the values 1, 3, 5 or 7 is contained in &apos;AllowedValues&apos;, &apos;F_iPar_CRC&apos; must not be given. ähnelt.
        /// </summary>
        internal static string M_0x00012108_2 {
            get {
                return ResourceManager.GetString("M_0x00012108_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Check_iPar&apos;: The default value (= {0}) is not contained in allowed values (= 1}). ähnelt.
        /// </summary>
        internal static string M_0x00012109_1 {
            get {
                return ResourceManager.GetString("M_0x00012109_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die F_ParameterRecordDataItem/F_Source_Add: The default value (= {0}) is not contained in allowed values (= {1}..{2}). ähnelt.
        /// </summary>
        internal static string M_0x00012110_1 {
            get {
                return ResourceManager.GetString("M_0x00012110_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die F_ParameterRecordDataItem/F_Dest_Add: The default value (= {0}) is not contained in allowed values (= {1}..{2}). ähnelt.
        /// </summary>
        internal static string M_0x00012111_1 {
            get {
                return ResourceManager.GetString("M_0x00012111_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die F_ParameterRecordDataItem/F_Par_CRC: The default value (= {0}) is not contained in allowed values (= {1}..{2}). ähnelt.
        /// </summary>
        internal static string M_0x00012112_1 {
            get {
                return ResourceManager.GetString("M_0x00012112_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;InterfaceSubmoduleItem/@DCP_HelloSupported&apos; is available and &quot;true&quot;, &apos;PowerOnToCommReady&apos; on the DAP must be &gt;0. ähnelt.
        /// </summary>
        internal static string M_0x00012113_1 {
            get {
                return ResourceManager.GetString("M_0x00012113_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die FSU (Fast Startup) can only work if autonegotiation can be turned off. That means, when &apos;InterfaceSubmoduleItem/@DCP_HelloSupported&apos; is available and &quot;true&quot;, for all &apos;PortSubmoduleItem&apos; elements pluggable on the DAP (ID = &quot;{0}&quot;) &apos;MAUTypes&apos; must be given, so that PDPortDataAdjust.AdjustMAUType is supported and autonegotiation can be switched off. ähnelt.
        /// </summary>
        internal static string M_0x00012113_2 {
            get {
                return ResourceManager.GetString("M_0x00012113_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die FSU (Fast Startup) can only work if autonegotiation can be turned off. That means, when &apos;InterfaceSubmoduleItem/@DCP_HelloSupported&apos; is available and &quot;true&quot;, for all &apos;PortSubmoduleItem&apos; elements pluggable on the DAP (ID = &quot;{0}&quot;) at least one of the MAUTypeItem elements shall have the attribute &apos;AdjustSupported&apos; present and &quot;true&quot; so that autonegotiation can be switched off. ähnelt.
        /// </summary>
        internal static string M_0x00012113_3 {
            get {
                return ResourceManager.GetString("M_0x00012113_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Par_Version/@DefaultValue&apos; must be 1 for v2-mode (PROFINET). ähnelt.
        /// </summary>
        internal static string M_0x00012114_1 {
            get {
                return ResourceManager.GetString("M_0x00012114_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Par_Version/@AllowedValues&apos; must be 1 for v2-mode (PROFINET). ähnelt.
        /// </summary>
        internal static string M_0x00012114_2 {
            get {
                return ResourceManager.GetString("M_0x00012114_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values for &apos;F_Block_ID/@AllowedValues&apos; attribute must be in the range of 0 to 7. ähnelt.
        /// </summary>
        internal static string M_0x00012115_1 {
            get {
                return ResourceManager.GetString("M_0x00012115_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The length of the ParameterRecordDataItem must not exceed the MaxSupportedRecordSize of {0}..
        /// </summary>
        internal static string M_0x00012116_1 {
            get {
                return ResourceManager.GetString("M_0x00012116_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;F_ParamDescCRC&apos; is missing. ähnelt.
        /// </summary>
        internal static string M_0x0001211D_1 {
            get {
                return ResourceManager.GetString("M_0x0001211D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of &apos;T_IO_InputMin&apos; * &apos;T_IO_Base&apos; must be lower or equal than &apos;T_DC_Max&apos; * &apos;T_DC_Base&apos; * 31250. ähnelt.
        /// </summary>
        internal static string M_0x00012202_1 {
            get {
                return ResourceManager.GetString("M_0x00012202_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of &apos;T_IO_OutputMin&apos; * &apos;T_IO_Base&apos; must be lower or equal than &apos;T_DC_Max&apos; * &apos;T_DC_Base&apos; * 31250. ähnelt.
        /// </summary>
        internal static string M_0x00012203_1 {
            get {
                return ResourceManager.GetString("M_0x00012203_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;ModuleItem/@FieldbusType&apos; is present, &apos;VirtualSubmoduleItem/SlotCluster&apos; must not be given. ähnelt.
        /// </summary>
        internal static string M_0x00012204_1 {
            get {
                return ResourceManager.GetString("M_0x00012204_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;ModuleItem/@FieldbusType&apos; is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget must not reference a SubmoduleItem which works as a FAP (&apos;@API&apos; = 17920). ähnelt.
        /// </summary>
        internal static string M_0x00012204_2 {
            get {
                return ResourceManager.GetString("M_0x00012204_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;ModuleItem/@FieldbusType&apos; is present, &apos;SystemDefinedSubmoduleList&apos; must not be given. ähnelt.
        /// </summary>
        internal static string M_0x00012204_3 {
            get {
                return ResourceManager.GetString("M_0x00012204_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;ModuleItem/@FieldbusType&apos; is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget must not reference a PortSubmoduleItem. ähnelt.
        /// </summary>
        internal static string M_0x00012204_4 {
            get {
                return ResourceManager.GetString("M_0x00012204_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;DeviceAccessPointItem/@SharedInputSupported&apos; is present and &quot;true&quot;, &apos;DeviceAccessPointItem/@SharedDeviceSupported&apos; must be present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00012205_1 {
            get {
                return ResourceManager.GetString("M_0x00012205_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When a FAP using slot cluster can be operated with the DAP, the element &apos;DeviceAccessPointItem/FieldbusIntegrationSlots must be given&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012206_1 {
            get {
                return ResourceManager.GetString("M_0x00012206_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;FieldbusIntegrationSlots/@MaxSupported&apos; must be greater or equal &apos;SlotCluster/@Count&apos; of every operable FAP. ähnelt.
        /// </summary>
        internal static string M_0x00012206_2 {
            get {
                return ResourceManager.GetString("M_0x00012206_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;FieldbusIntegrationSlots/@Range&apos; must be a correct range x..y with x&lt;y. ähnelt.
        /// </summary>
        internal static string M_0x00012206_3 {
            get {
                return ResourceManager.GetString("M_0x00012206_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;FieldbusIntegrationSlots/@Range&apos; must be in the range 0..32767. ähnelt.
        /// </summary>
        internal static string M_0x00012206_4 {
            get {
                return ResourceManager.GetString("M_0x00012206_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Values of attribute &apos;FieldbusIntegrationSlots/@Range&apos; overlap with &apos;DeviceAccessPointItem/@PhysicalSlots&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012206_5 {
            get {
                return ResourceManager.GetString("M_0x00012206_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The width of &apos;FieldbusIntegrationSlots/@Range&apos; is lower than &apos;FieldbusIntegrationSlots/@MaxSupported&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00012206_6 {
            get {
                return ResourceManager.GetString("M_0x00012206_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For FBI module with field bus type (= {0}) no matching FAP is available. ähnelt.
        /// </summary>
        internal static string M_0x00012206_7 {
            get {
                return ResourceManager.GetString("M_0x00012206_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;SlotCluster&apos; may only appear on a FAP (detected by API = 17920), but not on other submodules. ähnelt.
        /// </summary>
        internal static string M_0x00012207_1 {
            get {
                return ResourceManager.GetString("M_0x00012207_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;SupportedSubstitutionModes&apos; must only be present if the &apos;(Virtual)SubmoduleItem/IOData/Output&apos; is given. ähnelt.
        /// </summary>
        internal static string M_0x00012208_1 {
            get {
                return ResourceManager.GetString("M_0x00012208_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute ParameterizationDisallowed on DAP has not the same value as on PortSubmoduleItem which can be configured with this DAP. ähnelt.
        /// </summary>
        internal static string M_0x00012209_1 {
            get {
                return ResourceManager.GetString("M_0x00012209_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die @FieldbusType = &quot;0&quot; is not allowed, because the value &quot;0&quot; corresponds to API &quot;0x4600&quot; which is reserved for the FAP. ähnelt.
        /// </summary>
        internal static string M_0x0001220A_1 {
            get {
                return ResourceManager.GetString("M_0x0001220A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;SlotCluster/@Count&apos; = &quot;0&quot; is not allowed, because the value &quot;0&quot; would mean, that no Slots are required for the FAP. ähnelt.
        /// </summary>
        internal static string M_0x0001220B_1 {
            get {
                return ResourceManager.GetString("M_0x0001220B_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute MaxDataLength should be given, because using the default value is only good for one AR with a single input/output CR, but this device may be configured using more CRs/ARs..
        /// </summary>
        internal static string M_0x0001220C_1
        {
            get
            {
                return ResourceManager.GetString("M_0x0001220C_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;SupportedRT_Classes&apos; is necessary if the attribute &apos;StartupMode&apos; contains the token &quot;Advanced&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020002_1 {
            get {
                return ResourceManager.GetString("M_0x00020002_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die FrameID range 7 is only allowed for legacy devices. ähnelt.
        /// </summary>
        internal static string M_0x00020002_2 {
            get {
                return ResourceManager.GetString("M_0x00020002_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem&apos; supports &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; but does not support &apos;StartupMode&apos; &quot;Advanced&quot; for &quot;RT_CLASS_1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020003_1 {
            get {
                return ResourceManager.GetString("M_0x00020003_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem&apos; supports &apos;SystemRedundancy&apos; but does not support &apos;StartupMode&apos; &quot;Advanced&quot; for &quot;RT_CLASS_1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020003_2 {
            get {
                return ResourceManager.GetString("M_0x00020003_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem&apos; supports CiR but does not support &apos;StartupMode&apos; &quot;Advanced&quot; for &quot;RT_CLASS_1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020003_3 {
            get {
                return ResourceManager.GetString("M_0x00020003_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &quot;RT_CLASS_2&quot; is supported, the attribute &apos;ApplicationRelations/@StartupMode&apos; shall be either missing or contain the token &quot;Legacy&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020003_4 {
            get {
                return ResourceManager.GetString("M_0x00020003_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When using menus, all &apos;Ref&apos; elements must have an &apos;ID&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00020008_1 {
            get {
                return ResourceManager.GetString("M_0x00020008_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;Name/@TextId&apos; of the very first &apos;MenuItem&apos; (= &quot;{0}&quot;) in the &apos;MenuList&apos; is different to the &apos;Name/@TextId&apos; of the parameter record (= &quot;{1}&quot;). ähnelt.
        /// </summary>
        internal static string M_0x00020008_2 {
            get {
                return ResourceManager.GetString("M_0x00020008_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The parameter &quot;{0}&quot; is referenced more than once. ähnelt.
        /// </summary>
        internal static string M_0x00020008_3 {
            get {
                return ResourceManager.GetString("M_0x00020008_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The menu item &quot;{0}&quot; is referenced more than once. ähnelt.
        /// </summary>
        internal static string M_0x00020008_4 {
            get {
                return ResourceManager.GetString("M_0x00020008_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The parameter &quot;{0}&quot; is not referenced. ähnelt.
        /// </summary>
        internal static string M_0x00020008_5 {
            get {
                return ResourceManager.GetString("M_0x00020008_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The menu item &quot;{0}&quot; is not referenced. ähnelt.
        /// </summary>
        internal static string M_0x00020008_6 {
            get {
                return ResourceManager.GetString("M_0x00020008_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The menu hierarchy has exceeded the nesting level of three (root menu -&gt; menu -&gt; submenu -&gt; sub-submenu). ähnelt.
        /// </summary>
        internal static string M_0x00020008_7 {
            get {
                return ResourceManager.GetString("M_0x00020008_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;SystemRedundancy/@DeviceType&apos;=&quot;R2&quot; (or &quot;R1&quot; from V2.32 on) &apos;SystemRedundancy/@PrimaryAR_OnBothNAPsSupported&apos; must be given. ähnelt.
        /// </summary>
        internal static string M_0x00020010_1 {
            get {
                return ResourceManager.GetString("M_0x00020010_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;SystemRedundancy/@PrimaryAR_OnBothNAPsSupported&apos; is given &apos;SystemRedundancy/@DeviceType&apos; must be equal &quot;R2&quot; (or &quot;R1&quot; from V2.32 on). ähnelt.
        /// </summary>
        internal static string M_0x00020010_2 {
            get {
                return ResourceManager.GetString("M_0x00020010_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;SystemRedundancy/@DeviceType&apos;=&quot;R2&quot; (or &quot;R1&quot; from V2.32 on) &apos;FixedInSlots&apos; must contain one or two slots. ähnelt.
        /// </summary>
        internal static string M_0x00020010_3 {
            get {
                return ResourceManager.GetString("M_0x00020010_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If for &apos;SystemRedundancy/@DeviceType&apos;=&quot;R2&quot; (or &quot;R1&quot; from V2.32 on), &apos;AllowedInSlots&apos; must be available and contain two slots. ähnelt.
        /// </summary>
        internal static string M_0x00020010_4 {
            get {
                return ResourceManager.GetString("M_0x00020010_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If for &apos;SystemRedundancy/@DeviceType&apos;=&quot;R2&quot; (or &quot;R1&quot; from V2.32 on) &apos;FixedInSlots&apos; contains one slot, &apos;AllowedInSlots&apos; must be available and contain the slot from &apos;FixedInSlots&apos; plus one higher slot number. ähnelt.
        /// </summary>
        internal static string M_0x00020010_5 {
            get {
                return ResourceManager.GetString("M_0x00020010_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If for &apos;SystemRedundancy/@DeviceType&apos;=&quot;R2&quot; (or &quot;R1&quot; from V2.32 on) &apos;FixedInSlots&apos; contains two slots, &apos;AllowedInSlots&apos; must be available and contain those two slots. ähnelt.
        /// </summary>
        internal static string M_0x00020010_6 {
            get {
                return ResourceManager.GetString("M_0x00020010_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The maximum application data length is greater than the sum of maximum application data input and output length. ähnelt.
        /// </summary>
        internal static string M_0x00020012_1 {
            get {
                return ResourceManager.GetString("M_0x00020012_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The maximum application data length is lower than the highest value of maximum application data input or output length. ähnelt.
        /// </summary>
        internal static string M_0x00020013_1 {
            get {
                return ResourceManager.GetString("M_0x00020013_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For Status = {0} LogBookEntryItem/ErrorCode2Value must be given. ähnelt.
        /// </summary>
        internal static string M_0x00020014_1 {
            get {
                return ResourceManager.GetString("M_0x00020014_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For Status = {0} LogBookEntryItem/ErrorCode2List must be given. ähnelt.
        /// </summary>
        internal static string M_0x00020014_2 {
            get {
                return ResourceManager.GetString("M_0x00020014_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For Status = {0} ErrorCode2 must be in the range 0xC9..0xFF. ErrorCode2 = {1} is not valid here. ähnelt.
        /// </summary>
        internal static string M_0x00020014_3 {
            get {
                return ResourceManager.GetString("M_0x00020014_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of Status = {0} is not valid according to table &quot;Vendor specific PNIOStatus&quot; of the GSDML Specification. ähnelt.
        /// </summary>
        internal static string M_0x00020014_4 {
            get {
                return ResourceManager.GetString("M_0x00020014_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &quot;F_iPar_CRC&quot; is defined as token in &apos;F_SupportedParameters&apos;, &apos;F_iPar_CRC&apos; shall be present. ähnelt.
        /// </summary>
        internal static string M_0x00020016_1 {
            get {
                return ResourceManager.GetString("M_0x00020016_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;F_SupportedParameters&apos; is given, &apos;F_iPar_CRC&apos; shall only be present when it is defined as token in &apos;F_SupportedParameters&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00020016_2 {
            get {
                return ResourceManager.GetString("M_0x00020016_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die SendClock = &quot;{0}&quot; cannot be used, because the product of SendClock and highest value of &apos;ReductionRatio&apos; applicable (from attribute &apos;ReductionRatio&apos;, or from attribute &apos;ReductionRatioPow2&apos;/&apos;ReductionRatioNonPow2&apos;) is less than MinDeviceInterval. ähnelt.
        /// </summary>
        internal static string M_0x00020017_1 {
            get {
                return ResourceManager.GetString("M_0x00020017_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The PNIO version is higher than the GSDML version of the GSD file. ähnelt.
        /// </summary>
        internal static string M_0x00020018_1 {
            get {
                return ResourceManager.GetString("M_0x00020018_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; the element &apos;CertificationInfo&apos; is mandatory at the DAP. ähnelt.
        /// </summary>
        internal static string M_0x00020020_0 {
            get {
                return ResourceManager.GetString("M_0x00020020_0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@PTP_BoundarySupported&apos; shall be present and &quot;true&quot; if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_10 {
            get {
                return ResourceManager.GetString("M_0x00020020_10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@DCP_BoundarySupported&apos; shall be present and &quot;true&quot; if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_11 {
            get {
                return ResourceManager.GetString("M_0x00020020_11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@MulticastBoundarySupported&apos; shall be missing or &quot;false&quot; if &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;A&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_12 {
            get {
                return ResourceManager.GetString("M_0x00020020_12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@MulticastBoundarySupported&apos; shall be present and &quot;true&quot; if &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;B&quot; or &quot;C&quot; and if &apos;NumberOfAdditionalMulticastProviderCR&apos; &gt; 0 or &apos;NumberOfMulticastConsumerCR&apos; &gt; 0. ähnelt.
        /// </summary>
        internal static string M_0x00020020_13 {
            get {
                return ResourceManager.GetString("M_0x00020020_13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;InterfaceSubmoduleItem/MediaRedundancy&apos; shall be present if &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;ProcessAutomation&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_15 {
            get {
                return ResourceManager.GetString("M_0x00020020_15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die There shall be at least one combination of &apos;SendClock&apos; and &apos;ReductionRatio&apos; / &apos;ReductionRatioPow2&apos; / &apos;ReductionRatioNonPow2&apos; that exactly equals the value of &apos;MinDeviceInterval&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_16 {
            get {
                return ResourceManager.GetString("M_0x00020020_16", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The element &apos;SystemRedundancy&apos; must be present at the DAP when &apos;PNIO_Version&apos; &gt;= &quot;V2.35&quot; and &apos;CertificationInfo/@ApplicationClass&apos; contains &quot;HighAvailability&quot;..
        /// </summary>
        internal static string M_0x00020020_17
        {
            get
            {
                return ResourceManager.GetString("M_0x00020020_17", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The element &apos;InterfaceSubmoduleItem/MediaRedundancy&apos; must be present if there are at least two port submodules assigned to the interface submodule and &apos;DeviceAccessPointItem/@PNIO_Version &gt;= &quot;V2.35&quot;  and &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighAvailability&quot; or &quot;ProcessAutomation&quot;..
        /// </summary>
        internal static string M_0x00020020_18
        {
            get
            {
                return ResourceManager.GetString("M_0x00020020_18", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains the token &quot;Isochronous&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported&apos; shall be &quot;true&quot; if present and the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; shall contain &quot;RT_CLASS_STREAM_HI&quot;..
        /// </summary>
        internal static string M_0x00020020_19
        {
            get
            {
                return ResourceManager.GetString("M_0x00020020_19", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; does not contain the token &quot;Isochronous&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported&apos; shall be &quot;false&quot; if present and the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; shall be missing..
        /// </summary>
        internal static string M_0x00020020_20
        {
            get
            {
                return ResourceManager.GetString("M_0x00020020_20", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;MultipleWriteSupported&apos; shall be present and &quot;true&quot; at the DAP if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_3 {
            get {
                return ResourceManager.GetString("M_0x00020020_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;RemoteApplicationTimeout&apos; is present its value shall be &lt;= 300 at the DAP if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_4 {
            get {
                return ResourceManager.GetString("M_0x00020020_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;LLDP_NoD_Supported&apos; shall be present and &quot;true&quot; at the DAP if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_5 {
            get {
                return ResourceManager.GetString("M_0x00020020_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;ResetToFactoryModes&apos; shall be present and contain the value 2 at the DAP if the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_6 {
            get {
                return ResourceManager.GetString("M_0x00020020_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;SystemRedundancy&apos; shall be present at the DAP when &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;CertificationInfo/@ApplicationClass&apos; contains &quot;ProcessAutomation&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_7 {
            get {
                return ResourceManager.GetString("M_0x00020020_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If present at the DAP, the attribute &apos;SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class&apos; shall be set to &quot;Class3&quot; when &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;C&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_8 {
            get {
                return ResourceManager.GetString("M_0x00020020_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If present at the DAP, the attribute &apos;SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Class&apos; shall be set to &quot;Class1&quot; when &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;A&quot; or &quot;B&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_9 {
            get {
                return ResourceManager.GetString("M_0x00020020_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes&apos; shall be present. ähnelt.
        /// </summary>
        internal static string M_0x00020020_a {
            get {
                return ResourceManager.GetString("M_0x00020020_a", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes&apos; shall contain &quot;RT_CLASS_1&quot; except when &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;C&quot; and some submodule which is fixed plugged with this DAP has the attribute &apos;IsochroneModeRequired&apos; = &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_b {
            get {
                return ResourceManager.GetString("M_0x00020020_b", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes&apos; shall contain &quot;RT_CLASS_3&quot; when &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;C&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_c {
            get {
                return ResourceManager.GetString("M_0x00020020_c", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@SupportedRT_Classes&apos; shall not contain &quot;RT_CLASS_2&quot; or &quot;RT_CLASS_3&quot; when &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;A&quot; or &quot;B&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_d {
            get {
                return ResourceManager.GetString("M_0x00020020_d", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains the token &quot;Isochronous&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported&apos; shall be &quot;true&quot; if present and the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; shall contain &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_e {
            get {
                return ResourceManager.GetString("M_0x00020020_e", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; does not contain the token &quot;Isochronous&quot;, the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeSupported&apos; shall be &quot;false&quot; if present and the attribute &apos;DeviceAccessPointItem/SystemDefinedSubmoduleList/InterfaceSubmoduleItem/@IsochroneModeInRT_Classes&apos; shall either be missing or contain at most &quot;RT_CLASS_2&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020020_f {
            get {
                return ResourceManager.GetString("M_0x00020020_f", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PNIO_Version&apos; attribute references a non-existing GSDML version. ähnelt.
        /// </summary>
        internal static string M_0x00020021_1 {
            get {
                return ResourceManager.GetString("M_0x00020021_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;NetloadClass&apos; is mandatory at &apos;DeviceAccessPointItem/CertificationInfo&apos; for &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020021_2 {
            get {
                return ResourceManager.GetString("M_0x00020021_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, for &apos;ApplicationClass&apos; the tokens &quot;Isochronous&quot; and &quot;HighPerformance&quot; are only allowed if &apos;ConformanceClass&apos; is &quot;C&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020021_3 {
            get {
                return ResourceManager.GetString("M_0x00020021_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ApplicationClass&apos; values &quot;ProcessAutomation&quot; and &quot;HighPerformance&quot; require &apos;PNIO_Version&apos; &gt;= &quot;V2.3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020021_4 {
            get {
                return ResourceManager.GetString("M_0x00020021_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;SystemDefinedSubmoduleList&apos; element shall only be present at the DAP if the attribute &apos;PNIO_Version&apos; &gt;= &quot;V2.0&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020022_1 {
            get {
                return ResourceManager.GetString("M_0x00020022_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;ApplicationRelations&apos; element shall only be present at the DAP if the attribute &apos;PNIO_Version&apos; = &quot;V1.0&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020023_1 {
            get {
                return ResourceManager.GetString("M_0x00020023_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@ForwardingMode&apos; is mandatory if &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020024_1 {
            get {
                return ResourceManager.GetString("M_0x00020024_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if &apos;DeviceAccessPointItem/@PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &quot;RT_CLASS_3&quot; is supported. ähnelt.
        /// </summary>
        internal static string M_0x00020024_2 {
            get {
                return ResourceManager.GetString("M_0x00020024_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if fast forwarding is supported: &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW&apos; is present. ähnelt.
        /// </summary>
        internal static string M_0x00020024_3 {
            get {
                return ResourceManager.GetString("M_0x00020024_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if small send clock factors are supported: &apos;InterfaceSubmoduleItem/ApplicationRelations/RT_Class3TimingProperties/@SendClock&apos; is present and contains at least one value less than 8. ähnelt.
        /// </summary>
        internal static string M_0x00020024_4 {
            get {
                return ResourceManager.GetString("M_0x00020024_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if fragmentation is supported: &apos;InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType&apos; is present. ähnelt.
        /// </summary>
        internal static string M_0x00020024_5 {
            get {
                return ResourceManager.GetString("M_0x00020024_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if dynamic frame packing is supported: &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames&apos; is present and &gt; 0. ähnelt.
        /// </summary>
        internal static string M_0x00020024_6 {
            get {
                return ResourceManager.GetString("M_0x00020024_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if MRPD is supported: &apos;InterfaceSubmoduleItem/MediaRedundancy/@MRPD_Supported&apos; is present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020024_7 {
            get {
                return ResourceManager.GetString("M_0x00020024_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; if Short preamble is supported: &apos;PortSubmoduleItem/@ShortPreamble100MBitSupported&apos; is present and &quot;true&quot; on at least one port submodule which is configurable with this DAP. ähnelt.
        /// </summary>
        internal static string M_0x00020024_8 {
            get {
                return ResourceManager.GetString("M_0x00020024_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; = &quot;{0}&quot; is supported, &apos;InterfaceSubmoduleItem/@SupportedProtocols&apos; shall contain the value &quot;SNMP&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020025_1 {
            get {
                return ResourceManager.GetString("M_0x00020025_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, &apos;InterfaceSubmoduleItem/@SupportedProtocols&apos; shall contain the value &quot;LLDP&quot; except when &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;A&quot; and all port submodules which can be configured with this DAP have &apos;MAUTypes&apos; = 0 (wireless). ähnelt.
        /// </summary>
        internal static string M_0x00020025_2 {
            get {
                return ResourceManager.GetString("M_0x00020025_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die When &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, &apos;InterfaceSubmoduleItem/@SupportedProtocols&apos; shall contain the value &quot;LLDP&quot; except when &apos;DeviceAccessPointItem/CertificationInfo/@ConformanceClass&apos; is &quot;A&quot; and all port submodules which can be configured with this DAP only have a single element MAUTypeList/MAUTypeItem and this element has attribute Value =0 (wireless/radio communication). ähnelt.
        /// </summary>
        internal static string M_0x00020025_3 {
            get {
                return ResourceManager.GetString("M_0x00020025_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; with &apos;MAUTypes&apos; = 0 is pluggable with &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;) and &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, but &apos;DeviceAccessPointItem/.../@ConformanceClass&apos; is not &quot;A&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_1 {
            get {
                return ResourceManager.GetString("M_0x00020026_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@MAUTypes&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x00020026_2 {
            get {
                return ResourceManager.GetString("M_0x00020026_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@PortDeactivationSupported&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x00020026_3 {
            get {
                return ResourceManager.GetString("M_0x00020026_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@LinkStateDiagnosisCapability&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x00020026_4 {
            get {
                return ResourceManager.GetString("M_0x00020026_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@CheckMAUTypeSupported&apos; is not present or not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_5 {
            get {
                return ResourceManager.GetString("M_0x00020026_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; is not &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@ShortPreamble100MBitSupported&apos; is &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_6 {
            get {
                return ResourceManager.GetString("M_0x00020026_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ApplicationClass&apos; &quot;HighPerformance&quot;, but the attribute &apos;PortSubmoduleItem/@ShortPreamble100MBitSupported&apos; is not present or not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_7 {
            get {
                return ResourceManager.GetString("M_0x00020026_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is wireless (&apos;MAUTypeList/MAUTypeItem/Value&apos; = 0 and/or &apos;@MAUTypes = 0’) and pluggable with &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;) and &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot;, but &apos;DeviceAccessPointItem/.../@ConformanceClass&apos; is not &quot;A&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_8 {
            get {
                return ResourceManager.GetString("M_0x00020026_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is pluggable into &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/MAUTypeList/MAUTypeItem/@AdjustSupported&apos; is not present or not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020026_9 {
            get {
                return ResourceManager.GetString("M_0x00020026_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW&apos; is not present. This attribute shall be present and its value shall be &lt;= 2000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_1 {
            get {
                return ResourceManager.GetString("M_0x00020027_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxBridgeDelayFFW&apos; is {0}. This attribute shall be present and its value shall be &lt;= 2000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_2 {
            get {
                return ResourceManager.GetString("M_0x00020027_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames&apos; is not present. This attribute shall be present and &gt;0 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_3 {
            get {
                return ResourceManager.GetString("M_0x00020027_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Frames&apos; is {0}. This attribute shall be present and &gt;0 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_4 {
            get {
                return ResourceManager.GetString("M_0x00020027_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@FragmentationType&apos; is not present. This attribute shall be present if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_5 {
            get {
                return ResourceManager.GetString("M_0x00020027_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO&apos; is not present. This attribute shall be present and its value shall be &lt;= 3500 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_6 {
            get {
                return ResourceManager.GetString("M_0x00020027_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MinFSO&apos; is {0}. This attribute shall be present and its value shall be &lt;= 3500 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_7 {
            get {
                return ResourceManager.GetString("M_0x00020027_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed&apos; is not present. This attribute shall be present and its value shall be &lt;= 1000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_8 {
            get {
                return ResourceManager.GetString("M_0x00020027_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MaxDFP_Feed&apos; is {0}. This attribute shall be present and its value shall be &lt;= 1000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020027_9 {
            get {
                return ResourceManager.GetString("M_0x00020027_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter&apos; is not present. This attribute shall be present and its value shall be &lt;= 250 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020028_1 {
            get {
                return ResourceManager.GetString("M_0x00020028_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/SynchronisationMode/@PeerToPeerJitter&apos; is {0}. This attribute shall be present and its value shall be &lt;= 250 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020028_2 {
            get {
                return ResourceManager.GetString("M_0x00020028_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock&apos; is not present. This attribute shall be present and shall contain value(s) &lt; 8 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020029_1 {
            get {
                return ResourceManager.GetString("M_0x00020029_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3TimingProperties/@SendClock&apos; is {0}. This attribute shall be present and shall contain value(s) &lt; 8 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020029_2 {
            get {
                return ResourceManager.GetString("M_0x00020029_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem/SystemRedundancy&apos; is present, but the attribute &apos;DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported&apos; is not present or not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0002002A_1 {
            get {
                return ResourceManager.GetString("M_0x0002002A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;ModuleItem/SystemDefinedSubmoduleList&apos; is present, but if system redundancy is supported, the modules pluggable with this DAP (&apos;ID&apos; = &quot;{0}&quot;) shall not carry &apos;PortSubmodule&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x0002002A_2 {
            get {
                return ResourceManager.GetString("M_0x0002002A_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;SubmoduleItemRef/@SubmoduleItemTarget&apos; references a &apos;PortSubmoduleItem&apos;, but if system redundancy is supported, the modules pluggable with this DAP (&apos;ID&apos; = &quot;{0}&quot;) shall not carry &apos;PortSubmodule&apos; elements. ähnelt.
        /// </summary>
        internal static string M_0x0002002A_3 {
            get {
                return ResourceManager.GetString("M_0x0002002A_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;SystemRedundancy&apos; is present but &apos;PrmBeginPrmEndSequenceSupported&apos; is missing or &quot;false&quot; this DAP supports an interim version of system redundancy which is unsupported by most controllers..
        /// </summary>
        internal static string M_0x0002002A_4
        {
            get
            {
                return ResourceManager.GetString("M_0x0002002A_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;MaxDFP_Feed&apos; shall only be present if DFP is supported, i.e. &apos;MaxDFP_Frames&apos; &lt;&gt;0. ähnelt.
        /// </summary>
        internal static string M_0x0002002B_1 {
            get {
                return ResourceManager.GetString("M_0x0002002B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;AlignDFP_Subframes&apos; shall only be present if DFP is supported, i.e. &apos;MaxDFP_Frames&apos; &lt;&gt;0. ähnelt.
        /// </summary>
        internal static string M_0x0002002B_2 {
            get {
                return ResourceManager.GetString("M_0x0002002B_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;DeviceAccessPointItem/@PrmBeginPrmEndSequenceSupported&apos; is present and &quot;true&quot;, but the element &apos;SystemRedundancy&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x0002002C_1 {
            get {
                return ResourceManager.GetString("M_0x0002002C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@SupportedRT_Classes&apos; is present and &apos;MediaRedundancy/@MRPD_Supported&apos; is &quot;true&quot;, but the &apos;SupportedRT_Classes&apos; does not contain &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0002002D_1 {
            get {
                return ResourceManager.GetString("M_0x0002002D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;InterfaceSubmoduleItem/@SupportedRT_Class&apos; is present and legal and &apos;MediaRedundancy/@MRPD_Supported&apos; is &quot;true&quot;, but the &apos;SupportedRT_Class&apos; is not equal to &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0002002D_2 {
            get {
                return ResourceManager.GetString("M_0x0002002D_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;MediaRedundancy/@MRT_Supported&apos; is present and &quot;true&quot;, &apos;MediaRedundancy/@MRPD_Supported&apos; must be present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0002002E_1 {
            get {
                return ResourceManager.GetString("M_0x0002002E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports RT_CLASS_3, but the attribute &apos;SupportedSyncProtocols&apos; does not contain the token &quot;PTCP&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00020030_1 {
            get {
                return ResourceManager.GetString("M_0x00020030_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;RT_Class3Properties/@MaxDFP_Frames&apos; ({0}) shall not exceed &apos;ApplicationRelations/@NumberOfAR&apos; ({1}). ähnelt.
        /// </summary>
        internal static string M_0x00020031_1 {
            get {
                return ResourceManager.GetString("M_0x00020031_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;DeviceAccessPointItem/@SharedDeviceSupported&apos; is present and &quot;true&quot;, &apos;InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR&apos; must be present and &gt;1. ähnelt.
        /// </summary>
        internal static string M_0x00020032_1 {
            get {
                return ResourceManager.GetString("M_0x00020032_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;DeviceAccessPointItem/SystemRedundancy&apos; is present, &apos;InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR&apos; must be present and greater than the value of the attribute NumberOfAR_Sets, multiplied with the number of ARs which constitute an SR-AR set. ähnelt.
        /// </summary>
        internal static string M_0x00020032_2 {
            get {
                return ResourceManager.GetString("M_0x00020032_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;DeviceAccessPointItem/@IO_SupervisorSupported&apos; is present and &quot;true&quot;, &apos;InterfaceSubmoduleItem/ApplicationRelations/@NumberOfAR&apos; must be present and &gt;1. ähnelt.
        /// </summary>
        internal static string M_0x00020032_3 {
            get {
                return ResourceManager.GetString("M_0x00020032_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to NumberOfAR is too high. When neither shared device nor system redundancy is supported, only one or two ARs may be established (depending on whether IO supervisor is supported)..
        /// </summary>
        internal static string M_0x00020032_4 {
            get {
                return ResourceManager.GetString("M_0x00020032_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;SupportedMultipleRole&apos; must only be present if the device supports more than one MRP-instance (attribute &apos;MaxMRP_Instances&apos;). ähnelt.
        /// </summary>
        internal static string M_0x00030003_1 {
            get {
                return ResourceManager.GetString("M_0x00030003_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PDEV_CombinedObjectSupported&apos; shall be set to &quot;true&quot; if DeviceAccessPointItem/@PNIO_Version &gt;= &quot;V2.31&quot; and DeviceAccessPointItem/CertificationInfo/@ApplicationClass contains the token &quot;ProcessAutomation&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030004_1 {
            get {
                return ResourceManager.GetString("M_0x00030004_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PDEV_CombinedObjectSupported&apos; shall be set to &quot;true&quot; if &apos;DeviceAccessPointItem/@CIR_Supported&apos; is present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030004_2 {
            get {
                return ResourceManager.GetString("M_0x00030004_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PDEV_CombinedObjectSupported&apos; shall be set to &quot;true&quot; if &apos;DeviceAccessPointItem/SystemRedundancy/@DeviceType&apos; is &quot;R2&quot; (or &quot;R1&quot; from V2.32 on). ähnelt.
        /// </summary>
        internal static string M_0x00030004_3 {
            get {
                return ResourceManager.GetString("M_0x00030004_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If PNIO_Version &gt;= V2.35, &apos;PDEV_CombinedObjectSupported&apos; must be set to &quot;true&quot; if &apos;DeviceAccessPointItem/SystemRedundancy&apos; is present..
        /// </summary>
        internal static string M_0x00030004_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00030004_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;PDEV_CombinedObjectSupported&apos; shall be set to &quot;true&quot; if &apos;DeviceAccessPointItem/@PNIO_Version &gt;= &quot;V2.35&quot; and DeviceAccessPointItem/CertificationInfo[/CertificationInfoExt]/@ApplicationClass contains the token &quot;HighAvailability&quot;..
        /// </summary>
        internal static string M_0x00030004_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00030004_5", resourceCulture);
            }
        }


        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value &apos;MaxReductionRatioIsochroneMode&apos; shall be one of the values listed in &apos;ReductionRatio&apos;, &apos;ReductionRatioPow2&apos; or &apos;ReductionRatioNonPow2&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00030005_1 {
            get {
                return ResourceManager.GetString("M_0x00030005_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die PROFIsafe V2.6.1 is used but not all mandatory PROFIsafe V2.6.1 elements or attributes are present. ähnelt.
        /// </summary>
        internal static string M_0x00030007_1 {
            get {
                return ResourceManager.GetString("M_0x00030007_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Submodules with RIOforFA (&apos;Input/DataItem&apos; with &apos;Subordinate&apos;=&quot;true&quot;) and PROFIsafe must use PROFIsafe Version 2.6.1. &apos;F_Passivation&apos; must be set to &quot;Channel&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030008_1 {
            get {
                return ResourceManager.GetString("M_0x00030008_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_Passivation&apos; with value &quot;Channel&quot; is only allowed in combination with RIOforFA (&apos;Input/DataItem&apos; with &apos;Subordinate&apos;=&quot;true&quot;). ähnelt.
        /// </summary>
        internal static string M_0x00030008_2 {
            get {
                return ResourceManager.GetString("M_0x00030008_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;F_ParameterRecordDataItem/F_Passivation&apos;: The default value (= {0}) is not contained in allowed values (= {1}). ähnelt.
        /// </summary>
        internal static string M_0x00030009_1 {
            get {
                return ResourceManager.GetString("M_0x00030009_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Number of Q bits does not match number of IO channels. ähnelt.
        /// </summary>
        internal static string M_0x00030011_1 {
            get {
                return ResourceManager.GetString("M_0x00030011_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;DataType&apos; &quot;{0}&quot; must not be used in combination with RIOforFA. ähnelt.
        /// </summary>
        internal static string M_0x00030012_1 {
            get {
                return ResourceManager.GetString("M_0x00030012_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wrong order of &apos;DataItem&apos; elements used in combination with RIOforFA. ähnelt.
        /// </summary>
        internal static string M_0x00030012_2 {
            get {
                return ResourceManager.GetString("M_0x00030012_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die RIOforFA: For &apos;IOData/Output&apos; no Qualifier bits must be given. ähnelt.
        /// </summary>
        internal static string M_0x00030012_3 {
            get {
                return ResourceManager.GetString("M_0x00030012_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/@MaxFrameStartTime&apos; is not given. This attribute shall be present and its value shall be &lt;= 3500 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030013_1 {
            get {
                return ResourceManager.GetString("M_0x00030013_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/@MaxFrameStartTime&apos; is {0}. This attribute shall be present and its value shall be &lt;= 3500 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030013_2 {
            get {
                return ResourceManager.GetString("M_0x00030013_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/@MinNRT_Gap&apos; is not given. This attribute shall be present and its value shall be &lt;= 1600 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030013_3 {
            get {
                return ResourceManager.GetString("M_0x00030013_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/@MinNRT_Gap&apos; is {0}. This attribute shall be present and its value shall be &lt;= 1600 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030013_4 {
            get {
                return ResourceManager.GetString("M_0x00030013_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap&apos; is not given. This attribute shall be present and its value shall be &lt;= 1600 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030014_1 {
            get {
                return ResourceManager.GetString("M_0x00030014_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@MinRTC3_Gap&apos; is {0}. This attribute shall be present and its value shall be &lt;= 1600 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030014_2 {
            get {
                return ResourceManager.GetString("M_0x00030014_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin&apos; is not given. This attribute shall be present and its value shall be &lt;= 1000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030014_3 {
            get {
                return ResourceManager.GetString("M_0x00030014_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;InterfaceSubmoduleItem/RT_Class3Properties/@YellowSafetyMargin&apos; is {0}. This attribute shall be present and its value shall be &lt;= 1000 if &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and the attribute &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; contains &quot;HighPerformance&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00030014_4 {
            get {
                return ResourceManager.GetString("M_0x00030014_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The RIOforFA common profile requires BitDataItems for each of the DI, DO and Q bits. ähnelt.
        /// </summary>
        internal static string M_0x00030015_1 {
            get {
                return ResourceManager.GetString("M_0x00030015_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Referenced texts must not be an empty string or only consist of white spaces (blank, tabulator). ähnelt.
        /// </summary>
        internal static string M_0x00030020_1 {
            get {
                return ResourceManager.GetString("M_0x00030020_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;InterfaceSubmoduleItem&apos; supports RT_CLASS_3, but the element &apos;RT_Class3TimingProperties&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x0003002A_1 {
            get {
                return ResourceManager.GetString("M_0x0003002A_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The element &apos;RT_Class3TimingProperties&apos; is present, but the &apos;InterfaceSubmoduleItem&apos; does not support &quot;RT_CLASS_3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0003002A_2 {
            get {
                return ResourceManager.GetString("M_0x0003002A_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;API&apos; = 0, &apos;APStructureIdentifier&apos; must be less than 0x8000 (32768). ähnelt.
        /// </summary>
        internal static string M_0x0003002B_1 {
            get {
                return ResourceManager.GetString("M_0x0003002B_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If the attribute &apos;RT_Class3Properties/@ForwardingMode&apos; contains the token &quot;Absolute&quot; and the attribute &apos;PNIO_Version&apos; is &gt;= &quot;V2.31&quot;, the value of the attribute &apos;RT_Class3Properties/@MaxNumberIR_FrameData&apos; shall be &gt;= 128. ähnelt.
        /// </summary>
        internal static string M_0x0003002C_1 {
            get {
                return ResourceManager.GetString("M_0x0003002C_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die A DAP with &apos;ApplicationClass&apos; &quot;FunctionalSafety&quot; must have at least one PROFIsafe submodule. ähnelt.
        /// </summary>
        internal static string M_0x0003002D_1 {
            get {
                return ResourceManager.GetString("M_0x0003002D_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The submodule (&apos;ID&apos; = &quot;{0}&quot;) with &apos;PROFIsafeSupported&apos; = &quot;true&quot; is pluggable in a DAP with ApplicationClass &quot;FunctionalSafety&quot;. Here I&amp;M4 must not be writable. ähnelt.
        /// </summary>
        internal static string M_0x0003002D_2 {
            get {
                return ResourceManager.GetString("M_0x0003002D_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;ApplicationRelations/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; when PDEV_CombinedObjectSupported is present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0003002E_1 {
            get {
                return ResourceManager.GetString("M_0x0003002E_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;RT_Class3Properties/@StartupMode&apos; shall be present and contain the token &quot;Advanced&quot; when PDEV_CombinedObjectSupported is present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x0003002E_2 {
            get {
                return ResourceManager.GetString("M_0x0003002E_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For RIOforFA &apos;Consistency&apos;=&quot;All items consistency&quot; must be given for Input. ähnelt.
        /// </summary>
        internal static string M_0x0003002F_1 {
            get {
                return ResourceManager.GetString("M_0x0003002F_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For RIOforFA &apos;Consistency&apos;=&quot;All items consistency&quot; must be given for Output. ähnelt.
        /// </summary>
        internal static string M_0x0003002F_2 {
            get {
                return ResourceManager.GetString("M_0x0003002F_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The ErrorType must be unique across SystemDefinedChannelDiagItems and ChannelDiagItems. ähnelt.
        /// </summary>
        internal static string M_0x00030030_1 {
            get {
                return ResourceManager.GetString("M_0x00030030_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die @Subordinate=&quot;true&quot; must not appear on &apos;IOData/Output/DataItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00030031_1 {
            get {
                return ResourceManager.GetString("M_0x00030031_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;DeviceAccessPointItem/SystemRedundancy/@MinRDHT&apos; must not be lower than &apos;@MaxSwitchOverTime&apos;..
        /// </summary>
        internal static string M_0x00030032_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00030032_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; is present and contains the token &quot;EnergySaving&quot; the attribute &apos;PROFIenergyASE_Supported&apos; shall be present and &quot;true&quot; at the DAP. ähnelt.
        /// </summary>
        internal static string M_0x00032000_1 {
            get {
                return ResourceManager.GetString("M_0x00032000_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;DeviceAccessPointItem/CertificationInfo/@ApplicationClass&apos; is present and contains the token &quot;EnergySaving&quot; there shall be at least one submodule with element &apos;PROFIenergy&apos; and attribute &apos;PESAP_uses_PROFIenergyASE&apos; present and &quot;true&quot; which can be configured with this DAP. ähnelt.
        /// </summary>
        internal static string M_0x00032000_2 {
            get {
                return ResourceManager.GetString("M_0x00032000_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Data/@BitOffset&apos; and &apos;Data/@BitLength&apos; overlap. &apos;BitOffset&apos; = {0} is defined twice. ähnelt.
        /// </summary>
        internal static string M_0x00032001_1 {
            get {
                return ResourceManager.GetString("M_0x00032001_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Data/@BitOffset&apos; and &apos;Data/@BitLength&apos; overlap. The referenced IO data area before must not reach the area starting with &apos;BitOffset&apos; = {0}. ähnelt.
        /// </summary>
        internal static string M_0x00032001_2 {
            get {
                return ResourceManager.GetString("M_0x00032001_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Data/@BitOffset&apos; and &apos;Data/@BitLength&apos; overlap. The referenced IO data area after must not be reached by the area starting with &apos;BitOffset&apos; = {0}. ähnelt.
        /// </summary>
        internal static string M_0x00032001_3 {
            get {
                return ResourceManager.GetString("M_0x00032001_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Quality/@BitOffset&apos; and &apos;Quality/@Format&apos; overlap. &apos;BitOffset&apos; = {0} is defined twice. ähnelt.
        /// </summary>
        internal static string M_0x00032001_4 {
            get {
                return ResourceManager.GetString("M_0x00032001_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Quality/@BitOffset&apos; and &apos;Quality/@Format&apos; overlap. The referenced IO data area before must not reach the area starting with &apos;BitOffset&apos; = {0}. ähnelt.
        /// </summary>
        internal static string M_0x00032001_5 {
            get {
                return ResourceManager.GetString("M_0x00032001_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Parts of the IO data referenced by &apos;Quality/@BitOffset&apos; and &apos;Quality/@Format&apos; overlap. The referenced IO data area after must not be reached by the area starting with &apos;BitOffset&apos; = {0}. ähnelt.
        /// </summary>
        internal static string M_0x00032001_6 {
            get {
                return ResourceManager.GetString("M_0x00032001_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Channel/Data&apos; or &apos;Channel/Quality&apos; points outside the available IO data. ähnelt.
        /// </summary>
        internal static string M_0x00032001_7 {
            get {
                return ResourceManager.GetString("M_0x00032001_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Channel/Data&apos; or &apos;Channel/Quality&apos; must point either to a part of one DataItem or to one or more complete DataItems. ähnelt.
        /// </summary>
        internal static string M_0x00032001_8 {
            get {
                return ResourceManager.GetString("M_0x00032001_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Channel/Data&apos; or &apos;Channel/Quality&apos; which is only one bit wide and which is in a &apos;DataItem&apos; of &apos;DataType&apos; &quot;UnsignedXX&quot;, &quot;OctetString&quot; or &quot;V2&quot; must be described by a &apos;BitDataItem&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00032001_9 {
            get {
                return ResourceManager.GetString("M_0x00032001_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die All &apos;DataItem&apos; elements except for those with &apos;DataType&apos; &quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot; must be at least partly used by &apos;Channel/Data&apos; and/or &apos;Channel/Quality&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00032001_a {
            get {
                return ResourceManager.GetString("M_0x00032001_a", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die All &apos;DataItem&apos; elements with &apos;DataType&apos; &quot;F_MessageTrailer4Byte&quot; or &quot;F_MessageTrailer5Byte&quot; must not be referenced by &apos;Channel/Data&apos; and/or &apos;Channel/Quality&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00032001_b {
            get {
                return ResourceManager.GetString("M_0x00032001_b", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die On &apos;DataItem&apos; elements with &apos;BitDataItem&apos; elements, all &apos;BitDataItem&apos; elements must be used. ähnelt.
        /// </summary>
        internal static string M_0x00032001_c {
            get {
                return ResourceManager.GetString("M_0x00032001_c", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;SystemRedundancy/@{0}&apos; shall only be present if &apos;RT_InputOnBackupAR_Supported&apos; is present and &quot;true&quot; and &apos;DeviceType&apos; is &quot;{1}&quot; or &quot;{2}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032002_2 {
            get {
                return ResourceManager.GetString("M_0x00032002_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;RT_InputOnBackupAR_Supported&apos; is present and &quot;true&quot; &apos;SystemRedundancy/@{0}&apos; shall also be present if &apos;DeviceType&apos; is &quot;{1}&quot; or &quot;{2}&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032002_3 {
            get {
                return ResourceManager.GetString("M_0x00032002_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;MAUTypeItem/@Value=0&apos; is present it must be the only one in the &apos;MAUTypeList&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00032003_1 {
            get {
                return ResourceManager.GetString("M_0x00032003_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;MAUTypeItem/@Value=0&apos; is present the attribute &apos;AdjustSupported&apos; shall be missing or &quot;false&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032003_2 {
            get {
                return ResourceManager.GetString("M_0x00032003_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &apos;MAUTypeItem/@Extension!=0&apos; is present the attribute &apos;MAUTypeList/@ExtensionSupported&apos; shall be present and &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032004_1 {
            get {
                return ResourceManager.GetString("M_0x00032004_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUTypes&apos; value = {0} no &apos;MAUTypeItem&apos; with this value in attribute &apos;Value&apos; found. ähnelt.
        /// </summary>
        internal static string M_0x00032005_1 {
            get {
                return ResourceManager.GetString("M_0x00032005_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUTypes&apos; value = {0} a &apos;MAUTypeItem&apos; with this value in attribute &apos;Value&apos; found, but attribute &apos;Extension&apos; is not missing or set to 0. ähnelt.
        /// </summary>
        internal static string M_0x00032005_2 {
            get {
                return ResourceManager.GetString("M_0x00032005_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUTypes&apos; value = {0} a &apos;MAUTypeItem&apos; with this value in attribute &apos;Value&apos; found, but attribute &apos;AdjustSupported&apos; is not set to &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032005_3 {
            get {
                return ResourceManager.GetString("M_0x00032005_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The given combination of &apos;MAUTypeItem/@Extension&apos; and &apos;MAUTypeItem/@Value&apos; is not allowed..
        /// </summary>
        internal static string M_0x00032005_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00032005_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUType&apos; value = {0} no &apos;MAUTypeItem&apos; with the mapped value in attribute &apos;Value&apos; found. ähnelt.
        /// </summary>
        internal static string M_0x00032006_1 {
            get {
                return ResourceManager.GetString("M_0x00032006_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUType&apos; value = {0} a &apos;MAUTypeItem&apos; with the mapped value in attribute &apos;Value&apos; found, but attribute &apos;Extension&apos; is not missing or set to 0. ähnelt.
        /// </summary>
        internal static string M_0x00032006_2 {
            get {
                return ResourceManager.GetString("M_0x00032006_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die For &apos;PortSubmoduleItem/@MAUType&apos; value = {0} a &apos;MAUTypeItem&apos; with this mapped value in attribute &apos;Value&apos; found, but attribute &apos;AdjustSupported&apos; is not missing or set to &quot;false&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032006_3 {
            get {
                return ResourceManager.GetString("M_0x00032006_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;RT_Class3Properties/@MaxRetentionTime&apos; shall not be present if &apos;@ForwardingMode&apos; only contains the token &quot;Absolute&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032007_1 {
            get {
                return ResourceManager.GetString("M_0x00032007_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The &apos;PortSubmoduleItem&apos; is configurable with &apos;DeviceAccessPointItem&apos; (&apos;ID&apos; = &quot;{0}&quot;), &apos;PNIO_Version&apos; &gt;= &quot;V2.31&quot; and &apos;ConformanceClass&apos; &quot;C&quot;, but the attribute &apos;PortSubmoduleItem/@CheckMAUTypeDifferenceSupported&apos; is not present or not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00032008_1 {
            get {
                return ResourceManager.GetString("M_0x00032008_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The length of the string in the attribute DefaultValue according to its DataType does not match to the Length attribute. ähnelt.
        /// </summary>
        internal static string M_0x00032009_1 {
            get {
                return ResourceManager.GetString("M_0x00032009_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The string in the attribute DefaultValue does not match its DataType. ähnelt.
        /// </summary>
        internal static string M_0x00032009_2 {
            get {
                return ResourceManager.GetString("M_0x00032009_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Channel description is mandatory when the submodule follows the RIOforFA profile..
        /// </summary>
        internal static string M_0x0003200A_1
        {
            get
            {
                return ResourceManager.GetString("M_0x0003200A_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &quot;Off&quot; is present at &apos;MediaRedundancy/@SupportedRole&apos;, it must be the first token in the list. ähnelt.
        /// </summary>
        internal static string M_0x00033000_1 {
            get {
                return ResourceManager.GetString("M_0x00033000_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die If &quot;Off&quot; is present at &apos;MediaRedundancy/@SupportedRole&apos;, there must be at least one other token in the list. ähnelt.
        /// </summary>
        internal static string M_0x00033000_2 {
            get {
                return ResourceManager.GetString("M_0x00033000_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die On &apos;InterfaceSubmoduleItem&apos; the attribute &apos;SynchronisationMode/@SupportedSyncProtocols&apos; contains the token &quot;PTCP&quot;, but the attribute &apos;DelayMeasurementSupported&apos; is not set to &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033001_1 {
            get {
                return ResourceManager.GetString("M_0x00033001_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;@DeviceAccessSupported&apos; is present and &quot;true&quot; on &apos;DeviceAccessPointItem&apos;, but &apos;@NumberOfDeviceAccessAR&apos; is not present. ähnelt.
        /// </summary>
        internal static string M_0x00033002_1 {
            get {
                return ResourceManager.GetString("M_0x00033002_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;@NumberOfDeviceAccessAR&apos; is present on &apos;DeviceAccessPointItem&apos;, but &apos;@DeviceAccessSupported&apos; is not &quot;true&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033002_2 {
            get {
                return ResourceManager.GetString("M_0x00033002_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die A DAP with &apos;.../InterfaceSubmoduleItem/ReportingSystem&apos; must have at least one submodule which is configurable with this DAP which contains the &apos;ReportingSystemEvents&apos;. ähnelt.
        /// </summary>
        internal static string M_0x00033003_1 {
            get {
                return ResourceManager.GetString("M_0x00033003_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/@EntityClass&apos; must be present if &apos;@ProfileVersion&apos; is &gt;= &quot;V1.1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_1 {
            get {
                return ResourceManager.GetString("M_0x00033005_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;MeasurementItem/@Number&apos; must not be present if &apos;@ProfileVersion&apos; is &lt; &quot;V1.1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_10 {
            get {
                return ResourceManager.GetString("M_0x00033005_10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;MeasurementList&apos; must only contain one &apos;MeasurementItem&apos; if &apos;@ProfileVersion&apos; is &lt; &quot;V1.1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_11 {
            get {
                return ResourceManager.GetString("M_0x00033005_11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/@EntityClass&apos; must not be present if &apos;@ProfileVersion&apos; is &lt; &quot;V1.1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_2 {
            get {
                return ResourceManager.GetString("M_0x00033005_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/@EntitySubclass&apos; must be present if &apos;@ProfileVersion&apos; is &gt;= &quot;V1.2&quot; and &apos;@EntityClass&apos; is &quot;Class1&quot; or &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_3 {
            get {
                return ResourceManager.GetString("M_0x00033005_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/@EntitySubclass&apos; must not be present if &apos;@ProfileVersion&apos; is &lt; &quot;V1.2&quot; or &apos;@EntityClass&apos; is neither &quot;Class1&quot; nor &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_4 {
            get {
                return ResourceManager.GetString("M_0x00033005_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/EnergySavingModeList&apos; must be present if &apos;@EntityClass&apos; is &quot;Class1&quot; or &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_5 {
            get {
                return ResourceManager.GetString("M_0x00033005_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/EnergySavingModeList&apos; must not be present if &apos;@EntityClass&apos; is &quot;Class2&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_6 {
            get {
                return ResourceManager.GetString("M_0x00033005_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/MeasurementList&apos; must be present if &apos;@EntityClass&apos; is &quot;Class2&quot; or &quot;Class3&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_7 {
            get {
                return ResourceManager.GetString("M_0x00033005_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;PROFIenergy/MeasurementList&apos; must not be present if &apos;@EntityClass&apos; is &quot;Class1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_8 {
            get {
                return ResourceManager.GetString("M_0x00033005_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;MeasurementItem/@Number&apos; must be present if &apos;@ProfileVersion&apos; is &gt;= &quot;V1.1&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033005_9 {
            get {
                return ResourceManager.GetString("M_0x00033005_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value given for &apos;EnergySavingModeItem/@ID&apos; is not allowed according to PROFIenergy profile Table 2. ähnelt.
        /// </summary>
        internal static string M_0x00033006_1 {
            get {
                return ResourceManager.GetString("M_0x00033006_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value given for &apos;MeasurementItem/@ID&apos; is not allowed according to PROFIenergy profile Annex A.1. ähnelt.
        /// </summary>
        internal static string M_0x00033006_2 {
            get {
                return ResourceManager.GetString("M_0x00033006_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value given for &apos;MeasurementItem/@AccuracyDomain&apos; is not allowed according to PROFIenergy profile Table 5. ähnelt.
        /// </summary>
        internal static string M_0x00033006_3 {
            get {
                return ResourceManager.GetString("M_0x00033006_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value given for &apos;MeasurementItem/@AccuracyClass&apos; is not allowed according to PROFIenergy profile Table 6-8. ähnelt.
        /// </summary>
        internal static string M_0x00033006_4 {
            get {
                return ResourceManager.GetString("M_0x00033006_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Channel description on input side is mandatory when &apos;Observer/@Type&apos; is &quot;DigitalInput&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033007_1 {
            get {
                return ResourceManager.GetString("M_0x00033007_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Channel description on input side must contain at least one Channel with bit BitLength=1 when &apos;Observer/@Type&apos; is &quot;DigitalInput&quot;. ähnelt.
        /// </summary>
        internal static string M_0x00033007_2 {
            get {
                return ResourceManager.GetString("M_0x00033007_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &quot;Off&quot; is present at &apos;MediaRedundancy/@SupportedRole&apos;, so the PortSubmoduleItem (ID = &apos;{0}&apos;), which has &apos;@IsDefaultRingport&apos; = &quot;true&quot;, must not be configurable with this DAP..
        /// </summary>
        internal static string M_0x00033008_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00033008_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to SubmoduleIdentNumber 0 should not be used manufacturer specific..
        /// </summary>
        internal static string M_0x00033009_1 {
            get {
                return ResourceManager.GetString("M_0x00033009_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;PROFIenergy/@ProfileVersion&apos; is &gt;= V1.3 the attribute &apos;PESAP_uses_PROFIenergyASE&apos; must be present and &quot;true&quot;..
        /// </summary>
        internal static string M_0x00035000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00035000_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For &apos;MediaRedundancy/Interconnection&apos; either both attributes &apos;SupportedMRP_InterconnRole&apos; and &apos;MaxMRP_InterconnInstances&apos; must be present or none at all..
        /// </summary>
        internal static string M_0x00035001_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00035001_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For &apos;MediaRedundancy/Interconnection&apos; if both attributes &apos;SupportedMRP_InterconnRole&apos; and &apos;MaxMRP_InterconnInstances&apos; are present, at least for one of the PortSubmoduleItems configurable with this DAP the attribute SupportsMRP_InterconnPortConfig must be present and &quot;true&quot;..
        /// </summary>
        internal static string M_0x00035001_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00035001_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The sum of the lengths of all &apos;DataItem&apos; elements in a &apos;ProcessAlarmReasonAddValue&apos; must not exceed 127 byte, you have {0} bytes..
        /// </summary>
        internal static string M_0x00035002_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00035002_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to In the text for text id &quot;{0}&quot; the used id (= {1}) is not defined as an id in the corresponding data items..
        /// </summary>
        internal static string M_0x00035002_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00035002_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to In the text for text id &quot;{0}&quot; the format &quot;({1}:{2})&quot; does not match to the data type &quot;{3}&quot;..
        /// </summary>
        internal static string M_0x00035002_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00035002_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to In the text &quot;{0}&quot; the format does not match the following expression: &quot;{1}&quot;..
        /// </summary>
        internal static string M_0x00035002_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00035002_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The ApplicationClass &quot;HighAvailability&quot; must only appear on DAPs with PNIO_Version &gt;= V2.35..
        /// </summary>
        internal static string M_0x00035003_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00035003_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If the attribute ApplicationClass contains the token &quot;ProcessAutomation&quot;, it must not contain the token &quot;HighAvailability&quot;..
        /// </summary>
        internal static string M_0x00035003_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00035003_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If PNIO_Version &gt;= V2.35, and if the attribute ApplicationClass contains the token &quot;ProcessAutomation&quot;, the attribute DeviceAccessSupported must be present and &quot;true&quot;..
        /// </summary>
        internal static string M_0x00035003_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00035003_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If PNIO_Version &gt;= V2.35, the attribute &apos;MediaRedundancy/@AdditionalForwardingRulesSupported&apos; must be present and &quot;true&quot; if the attribute ApplicationClass contains one of the tokens &quot;ProcessAutomation&quot; and &quot;HighAvailability&quot;..
        /// </summary>
        internal static string M_0x00035003_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00035003_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If the attribute ApplicationClass contains the token &quot;HighAvailability&quot;, the attribute &quot;CIR_Supported&quot; must be present and &quot;true&quot;..
        /// </summary>
        internal static string M_0x00035003_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00035003_5", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;(Virtual)SubmoduleItem/@PROFIsafePIR_Supported&apos; must only be present and true when &apos;(Virtual)SubmoduleItem/@PROFIsafeSupported&apos; is also present and &quot;true&quot;..
        /// </summary>
        internal static string M_0x00040000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040000_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;(Virtual)SubmoduleItem/@PROFIsafePIR_Supported&apos; must only be present and &quot;true&quot; when PROFIsafe is &gt;= 2.6 (&apos;F_ParameterRecordDataItem/F_CRC_Seed&apos; and &apos;../F_Passivation&apos; are present)..
        /// </summary>
        internal static string M_0x00040000_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040000_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;@PrmBeginPrmEndSequenceSupported&apos; must be present and &quot;true&quot; on each DAP where a (Virtual)SubmoduleItem with &apos;@PROFIsafePIR_Supported&apos; present and &quot;true&quot; can be plugged..
        /// </summary>
        internal static string M_0x00040000_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00040000_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;InterfaceSubmoduleItem/@SupportedServiceProtocols&apos; with &quot;RSI&quot; must only be present when PNIO_Version &gt;= &quot;V2.4&quot;..
        /// </summary>
        internal static string M_0x00040001_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040001_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;DeviceAccessPointItem/CertificationInfo/CertificationInfoExt&apos; is present with ConformanceClass=&quot;D&quot;, &apos;InterfaceSubmoduleItem/@SupportedServiceProtocols&apos; shall contain the value &quot;RSI&quot;..
        /// </summary>
        internal static string M_0x00040001_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040001_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For this DAP the attribute SFPDiagnosisSupported exists, but no port submodule configurable with this DAP. There shall be at least one port submodule configurable with this DAP where the attribute &apos;@SFPDiagnosisMonitoring&apos; is present and which contains the tokens from &apos;@SFPDiagnosisSupported&apos;..
        /// </summary>
        internal static string M_0x00040002_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040002_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For this DAP the attribute SFPDiagnosisSupported exists with the token &quot;{0}&quot;, but there is no port submodule configurable with this DAP where the attribute &apos;@SFPDiagnosisMonitoring&apos; is present and contains this token..
        /// </summary>
        internal static string M_0x00040002_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040002_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The value of DeviceType = &quot;{0}&quot; must also be present as token in DeviceTypes..
        /// </summary>
        internal static string M_0x00040003_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040003_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If the attribute &apos;SystemRedundancy/@DeviceType&apos; is missing, the attribute &apos;@RequiredSchemaVersion&apos; must be present at the DAP with a value &gt;= V2.4..
        /// </summary>
        internal static string M_0x00040003_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040003_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;SystemRedundancy/@DeviceTypes&apos; contains &quot;R1&quot; and/or &quot;R2&quot; there must be provisions for a second DAP. The provision depends on the presence of the token &quot;S2&quot;: if present, the second DAP must be optionally pluggable what means &apos;@FixedInSlots&apos; must contain one and &apos;@AllowedInSlots&apos; two entries..
        /// </summary>
        internal static string M_0x00040003_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00040003_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;@PNIO_Version&apos; is &lt; V2.4, &apos;CertificationInfo/@ConformanceClass&apos; and &apos;@ApplicationClass&apos; must be present, and &apos;CertificationInfoExt&apos; must not be present..
        /// </summary>
        internal static string M_0x00040004_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040004_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;@PNIO_Version&apos; &gt;= V2.4, &apos;CertificationInfo/@ConformanceClass&apos; and &apos;@ApplicationClass&apos; must both be present or both be missing..
        /// </summary>
        internal static string M_0x00040004_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040004_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfo/@ConformanceClass&apos; and &apos;@ApplicationClass&apos; both are missing, &apos;@RequiredSchemaVersion&apos; must be present on the DAP with a value &gt;= V2.4..
        /// </summary>
        internal static string M_0x00040004_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00040004_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then the neighboring attribute &apos;@ApplicationClass&apos; shall not contain the token &quot;HighPerformance&quot;..
        /// </summary>
        internal static string M_0x00040005_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then SupportedRT_Classes must contain &quot;RT_CLASS_STREAM_LO&quot; and/or &quot;RT_CLASS_STREAM_HI&quot;..
        /// </summary>
        internal static string M_0x00040005_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then TimeSynchronisation/WorkingClock must be present..
        /// </summary>
        internal static string M_0x00040005_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then there must be at least one PortSubmoduleItem pluggable with MAUTypeList/MAUTypeItem/@SupportedFeatures is present and contains &quot;TSN&quot;..
        /// </summary>
        internal static string M_0x00040005_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then &apos;DeviceAccessPointItem/@MinDeviceInterval&apos; must be &lt;= 128 ms..
        /// </summary>
        internal static string M_0x00040005_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_5", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;@ConformanceClass&apos; and &apos;@ApplicationClass&apos; are missing, and &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot; (a D-only device), &apos;InterfaceSubmoduleItem/@MulticastBoundarySupported&apos; must be missing or false..
        /// </summary>
        internal static string M_0x00040005_6
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_6", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;@ConformanceClass&apos; and &apos;@ApplicationClass&apos; are missing, and &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot; (a D-only device), the token &quot;RT_CLASS_1&quot; must not be contained in &apos;InterfaceSubmoduleItem/@SupportedRT_Classes&apos;..
        /// </summary>
        internal static string M_0x00040005_7
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_7", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CertificationInfoExt/@ConformanceClass&apos;=&quot;D&quot;, then SupportedRT_Classes must contain &quot;RT_CLASS_STREAM_LO&quot;, &quot;RT_CLASS_STREAM_HI and/or &quot;RT_CLASS_STREAM_RT&quot;..
        /// </summary>
        internal static string M_0x00040005_8
        {
            get
            {
                return ResourceManager.GetString("M_0x00040005_8", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;MAUTypeList/MAUTypeItem/@SupportedFeatures&apos; may not be present when &apos;@PNIO_Version&apos; &lt; V2.4..
        /// </summary>
        internal static string M_0x00040006_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00040006_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;MAUTypeList/MAUTypeItem/@SupportedFeatures&apos; contains token &quot;TSN_TAS&quot; and/or &quot;TSN_Preemption&quot; but not required token &quot;TSN&quot;..
        /// </summary>
        internal static string M_0x00040006_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00040006_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;MAUTypeList/MAUTypeItem/@SupportedFeatures&apos; with token &quot;TSN&quot; and &apos;MAUTypeItem/@Value&apos; &lt;= 100 Mbit/s is given &apos;@SupportedFeatures&apos; must also contain &quot;TSN_TAS&quot;..
        /// </summary>
        internal static string M_0x00040006_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00040006_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;MAUTypeList/MAUTypeItem/@SupportedFeatures&apos; with token &quot;TSN&quot; and &apos;MAUTypeItem/@Value&apos; &lt;= 1 Gbit/s is given &apos;@SupportedFeatures&apos; must also contain &quot;TSN_Preemption&quot;..
        /// </summary>
        internal static string M_0x00040006_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00040006_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Elements under &apos;ds:Signature&apos; must be defined in one of the namespaces &quot;http://www.w3.org/2000/09/xmldsig#&quot; or &quot;http://uri.etsi.org/01903/v1.3.2#&quot;..
        /// </summary>
        internal static string M_0x00041000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;SigningCertificateV2/Cert/IssuerSerialV2&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_10
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_10", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the attribute &apos;Id&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML &apos;CanonicalizationMethod/@Algorithm&apos; must be &quot;http://www.w3.org/2001/10/xml-exc-c14n#&quot;..
        /// </summary>
        internal static string M_0x00041000_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML &apos;SignatureMethod/@Algorithm&apos; must be &quot;http://www.w3.org/2001/04/xmldsig-more#rsa-sha256&quot;..
        /// </summary>
        internal static string M_0x00041000_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML &apos;Transform/@Algorithm&apos; must be &quot;http://www.w3.org/2001/10/xml-exc-c14n#&quot; or &quot;http://www.w3.org/2001/04/xmlenc#sha256&quot;..
        /// </summary>
        internal static string M_0x00041000_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_5", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML &apos;DigestMethod/@Algorithm&apos; must be &quot;http://www.w3.org/2001/04/xmlenc#sha256&quot;..
        /// </summary>
        internal static string M_0x00041000_6
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_6", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;KeyInfo&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_7
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_7", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;KeyInfo/x509Data&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_8
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_8", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to At least three X509Certificates must be given..
        /// </summary>
        internal static string M_0x00041000_9
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_9", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to At least one &apos;Signature/Object&apos; must be given..
        /// </summary>
        internal static string M_0x00041000_a
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_a", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;Object/QualifyingProperties&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_b
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_b", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;Object/QualifyingProperties/SignedProperties&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_c
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_c", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;SignedProperties/SignedSignatureProperties&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_d
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_d", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;SignedProperties/SignedSignatureProperties/SigningTime&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_e
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_e", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to For a signed GSDML the element &apos;SignedProperties/SignedSignatureProperties/SigningCertificateV2&apos; is mandatory..
        /// </summary>
        internal static string M_0x00041000_f
        {
            get
            {
                return ResourceManager.GetString("M_0x00041000_f", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to At least one attribute &apos;SecurityClass&apos; exists, but the element &apos;ds:Signature&apos; is not available..
        /// </summary>
        internal static string M_0x00041001_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00041001_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;PNIO_Version&apos; &gt;= V2.41 and the attribute &apos;ApplicationClass&apos; contains the token ProcessAutomation&quot;, the element &apos;CertificationInfo/ProfileProcessAutomation&apos; must be present..
        /// </summary>

        internal static string M_0x00041002_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00041002_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The &apos;PAProfileVersion&apos; attribute must contain the value &quot;V4.01&quot;..
        /// </summary>

        internal static string M_0x00041002_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00041002_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The value of &apos;PAProfileDeviceID&apos; must be between &quot;0xB000&quot; and &quot;0xBFFF&quot;..
        /// </summary>

        internal static string M_0x00041002_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00041002_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;MAUTypeList/MAUTypeItem/@MaxTransferTimeTX&apos; may not be present when &apos;@PNIO_Version&apos; &lt; V2.42..
        /// </summary>
        internal static string M_0x00042000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042000_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;MAUTypeList/MAUTypeItem/@MaxTransferTimeRX&apos; may not be present when &apos;@PNIO_Version&apos; &lt; V2.42..
        /// </summary>
        internal static string M_0x00042000_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00042000_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &apos;MAUTypeItem/@MaxTransferTimeTX&apos; and &apos;@MaxTransferTimeRX&apos; may be present when &apos;@PNIO_Version&apos; &gt;= V2.42, and the associated InterfaceSubmoduleItem/@SupportedRT_Classes contains the token &quot;RT_CLASS_STREAM_HI&quot;, and this port and MAUType support the given stream class (@SupportedFeatures contains &quot;TSN&quot;)..
        /// </summary>
        internal static string M_0x00042000_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00042000_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;ProfileProcessAutomation/@PADeviceClass&apos; is &quot;ProcessControlDevice&quot;, the attributes &apos;@PAProfileVersion&apos;, &apos;@PAProfileDeviceID&apos; and &apos;@PAProfileDeviceDAP_ID&apos; must be present..
        /// </summary>
        internal static string M_0x00042001_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042001_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;ProfileProcessAutomation/@PADeviceClass&apos; is &quot;General&quot;, the attributes &apos;@PAProfileVersion&apos;, &apos;@PAProfileDeviceID&apos; and &apos;@PAProfileDeviceDAP_ID&apos; must not be present..
        /// </summary>
        internal static string M_0x00042001_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00042001_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If PNIO_Version &gt;= V2.42, and at least one submodule configurable with the DAP contains &apos;PROFIenergy/@ProfileVersion&apos; &gt;= &quot;V1.3&quot;, the attribute &apos;DeviceAccessSupported&apos; must be &quot;true&quot;..
        /// </summary>
        internal static string M_0x00042002_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042002_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;@SharedDeviceSupported&apos; is not present or &quot;false&quot; and &apos;@IO_SupervisorSupported&apos; is not present or &quot;false&quot;, the attribute &apos;IOConfigData/@MaxApplicationARs&apos; must be &quot;1&quot;..
        /// </summary>
        internal static string M_0x00042003_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042003_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;@SharedDeviceSupported&apos; is &quot;true&quot; or &apos;@IO_SupervisorSupported&apos; is &quot;true&quot;, the attribute &apos;IOConfigData/@MaxApplicationARs&apos; must be greater than &quot;1&quot;..
        /// </summary>

        internal static string M_0x00042003_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00042003_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;InterfaceSubmoduleItem/@DelayMeasurementSupported&apos; is present and &quot;true&quot;, &apos;InterfaceSubmoduleItem/@SupportedDelayMeasurements&apos; must contain the value &quot;PTCP&quot;..
        /// </summary>
        internal static string M_0x00042004_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042004_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;InterfaceSubmoduleItem/SynchronisationMode/@SupportedSyncProtocols&apos; contains the protocol type &quot;PTCP&quot;, &apos;InterfaceSubmoduleItem/@SupportedDelayMeasurements&apos; must contain the value &quot;PTCP&quot;, too..
        /// </summary>
        internal static string M_0x00042004_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00042004_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A graphics file name must be according to the scheme &quot;GSDML-&lt;VendorID&gt;-&lt;DeviceID&gt;-&lt;vendor-specific extension&gt;&quot;..
        /// </summary>
        internal static string M_0x00042005_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042005_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A graphics file name must consist of at least 4 fields, separated by &quot;-&quot;..
        /// </summary>
        internal static string M_0x00042005_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00042005_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A graphics file name must begin with &quot;GSDML-&quot;..
        /// </summary>
        internal static string M_0x00042005_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00042005_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &quot;GSDML-&quot; in graphics file name must be upper case..
        /// </summary>
        internal static string M_0x00042005_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00042005_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to &lt;{0}&gt; in graphics file name must be an unsigned16 hex value with exactly 4 hex digits..
        /// </summary>
        internal static string M_0x00042005_5
        {
            get
            {
                return ResourceManager.GetString("M_0x00042005_5", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The offset of the &apos;DataItem&apos; does not match the natural alignment regarding its data type..
        /// </summary>
        internal static string M_0x00042006_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00042006_1", resourceCulture);
            }
        }
        /// <summary>
        ///   Looks up a localized string similar to When &apos;PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification&apos; exists, the attribute &apos;InterfaceSubmoduleItem/@DCP_FeaturesSupported&apos; must exist and must contain the token &quot;PRUNING&quot;.
        /// </summary>
        internal static string M_0x00043000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043000_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to According to Ethernet-APL Port Profile Specification (ETH-APL-2021), the combination of &apos;APLPortClassification/@SegmentClass&apos; = &quot;{0}&quot;, &apos;APLPortClassification/@PortClass&apos; = &quot;{1}&quot; and &apos;APLPortClassification/@PowerClass&apos; = &quot;{2}&quot; is not allowed..
        /// </summary>
        internal static string M_0x00043000_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00043000_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The element &apos;PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification&apos; must exist only, if  &apos;MAUTypeItem/@Value&apos; equals to 10BaseT1L = &quot;141&quot; and MAUTypeItem/@Extension equals to APL = &quot;512&quot;..
        /// </summary>
        internal static string M_0x00043000_3
        {
            get
            {
                return ResourceManager.GetString("M_0x00043000_3", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If &apos;MAUTypeItem/@Value&apos; equals to 10BaseT1L = &quot;141&quot; and MAUTypeItem/@Extension equals to APL = &quot;512&quot;, the element &apos;PortSubmoduleItem/MAUTypeList/MAUTypeItem/APLPortClassification&apos; must exist, too..
        /// </summary>
        internal static string M_0x00043000_4
        {
            get
            {
                return ResourceManager.GetString("M_0x00043000_4", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The element &apos;(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem/F_WD_TIME_2&apos; shall exist if &apos;@PROFIsafePIR_Supported&apos; is present and &quot;true&quot; for this submodule..
        /// </summary>
        internal static string M_0x00043001_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043001_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The element &apos;(Virtual)SubmoduleItem/RecordDataList/F_ParameterRecordDataItem/F_WD_TIME_2&apos; shall not exist if &apos;@PROFIsafePIR_Supported&apos; is not present or not &quot;true&quot; for this submodule..
        /// </summary>
        internal static string M_0x00043001_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00043001_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When &apos;CommunicationInterfaceItem/CIM_SupportedRecords&apos; exists, the element &apos;CommunicationInterfaceItem/CIM_Interface&apos; must exist, too..
        /// </summary>
        internal static string M_0x00043002_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043002_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If PNIO_Version &gt;= &quot;V2.43&quot; and if the DAP describes a ConformanceClass &quot;D&quot; device, the element &apos;DeviceAccessPointItem/CommunicationInterfaceModule&apos; must be present..
        /// </summary>
        internal static string M_0x00043003_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043003_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If the DAP describes a SecurityClass &quot;2&quot; or &quot;3&quot; device, the element &apos;DeviceAccessPointItem/CommunicationInterfaceModule&apos; must be present..
        /// </summary>
        internal static string M_0x00043003_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00043003_2", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to If the element &apos;{0}&apos; is present, then one of the attributes &apos;@AuthnOnly&apos; or &apos;@AuthnEnc&apos; must be present..
        /// </summary>
        internal static string M_0x00043004_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043004_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The &apos;InterfaceSubmoduleItem/TimingProperties/@SendClock&apos; attribute must contain the value 32 if all ports only support a link speed less than 100 Mbits/s..
        /// </summary>
        internal static string M_0x00043005_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00043005_1", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;DeviceAccessPointItem/@MinDeviceInterval&apos; shall be &gt;= 256 (8 ms) if all ports only support a link speed less than 100 Mbits/s..
        /// </summary>
        internal static string M_0x00043005_2
        {
            get
            {
                return ResourceManager.GetString("M_0x00043005_2", resourceCulture);
            }
        }
        /// <summary>
        ///   Looks up a localized string similar to The attribute &apos;DeviceAccessPointItem/@MinDeviceInterval&apos; shall be &gt;= 256 (8 ms) if all ports only support a link speed less than 100 Mbits/s..
        /// </summary>
        internal static string M_0x00044000_1
        {
            get
            {
                return ResourceManager.GetString("M_0x00044000_1", resourceCulture);
            }
        }



        /// <summary>
        ///   Looks up a localized string similar to Decimal digits must only contains the characters &quot;0&quot; to &quot;9&quot;..
        /// </summary>
        internal static string M_DecimalDigits
        {
            get
            {
                return ResourceManager.GetString("M_DecimalDigits", resourceCulture);
            }
        }

        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die General ähnelt.
        /// </summary>
        internal static string M_General {
            get {
                return ResourceManager.GetString("M_General", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;{0}&apos; = &quot;{1}&quot; contains a range with a minimum value greater than the maximum value. ähnelt.
        /// </summary>
        internal static string M_InvalidRange {
            get {
                return ResourceManager.GetString("M_InvalidRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die KeyKeyref ähnelt.
        /// </summary>
        internal static string M_KeyKeyref {
            get {
                return ResourceManager.GetString("M_KeyKeyref", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The value of the attribute &apos;{0}&apos; = &quot;{1}&quot; cannot be converted into the given data type (too many digits?). ähnelt.
        /// </summary>
        internal static string M_NumericOverflow {
            get {
                return ResourceManager.GetString("M_NumericOverflow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The attribute &apos;{0}&apos; = &quot;{1}&quot; contains values and/or value ranges which overlap. ähnelt.
        /// </summary>
        internal static string M_Overlap {
            get {
                return ResourceManager.GetString("M_Overlap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die PlugRules ähnelt.
        /// </summary>
        internal static string M_PlugRules {
            get {
                return ResourceManager.GetString("M_PlugRules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Signature ähnelt.
        /// </summary>
        internal static string M_Signature {
            get {
                return ResourceManager.GetString("M_Signature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die TypeSpecific ähnelt.
        /// </summary>
        internal static string M_TypeSpecific {
            get {
                return ResourceManager.GetString("M_TypeSpecific", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The DeviceAccessPoints (DAPs) {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
        ///Reason: {2} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_DAP_1 {
            get {
                return ResourceManager.GetString("M_UniqueIds_DAP_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The DAP {0} and the Module {1} are incompatible and thus need different ModuleIdentNumbers.
        ///The conflict arises e.g. when the Module is plugged with the DAP {2}.
        ///Reason: {3} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_MOD_1 {
            get {
                return ResourceManager.GetString("M_UniqueIds_MOD_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The Module {0} and the DAP {1} are incompatible and thus need different ModuleIdentNumbers.
        ///The conflict arises e.g. when the Module is plugged with the DAP {2}.
        ///Reason: {3} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_MOD_2 {
            get {
                return ResourceManager.GetString("M_UniqueIds_MOD_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The Modules {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
        ///The conflict arises e.g. when both Modules are plugged with the DAP {2}.
        ///Reason: {3} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_MOD_3 {
            get {
                return ResourceManager.GetString("M_UniqueIds_MOD_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The Modules {0} and {1} are incompatible and thus need different ModuleIdentNumbers.
        ///The conflict arises e.g. when the first Module is plugged with the DAP {2} and the second Module is plugged with the DAP {3}.
        ///Reason: {4} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_MOD_4 {
            get {
                return ResourceManager.GetString("M_UniqueIds_MOD_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when both Submodules are plugged in the DAP {4}.
        /// Reason: {5} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_1 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when both Submodules are plugged in the Module {4}.
        /// Reason: {5} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_2 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when the first Submodule is plugged in the DAP {4} and the second Submodule is plugged in the DAP {5}.
        ///Reason: {6} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_3 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when the first Submodule is plugged in the DAP {4} and the second Submodule is plugged in the Module {5} which is plugged with the DAP {6}.
        ///Reason: {7} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_4 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when the first Submodule is plugged in the Module &quot;{4}&quot; which is plugged with the DAP {5} and the second Submodule is plugged in the DAP {6}.
        ///Reason: {7} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_5 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when the first Submodule is plugged in the Module {4} and the second Submodule is plugged in the Module {5} which are both plugged with the DAP {6}.
        ///Reason: {7} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_6 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} {1} and the {2} {3} are incompatible and thus need different SubmoduleIdentNumbers.
        ///The conflict arises e.g. when the first Submodule is plugged in the Module {4} which is plugged with the DAP {5} and the second Submodule is plugged in the Module {6} which is plugged with the DAP {7}.
        ///Reason: {8} ähnelt.
        /// </summary>
        internal static string M_UniqueIds_SUBMOD_7 {
            get {
                return ResourceManager.GetString("M_UniqueIds_SUBMOD_7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Validation ähnelt.
        /// </summary>
        internal static string M_Validation {
            get {
                return ResourceManager.GetString("M_Validation", resourceCulture);
            }
        }
    }
}
