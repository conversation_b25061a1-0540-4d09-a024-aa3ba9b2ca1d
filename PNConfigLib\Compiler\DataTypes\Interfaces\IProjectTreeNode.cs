/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: PnDriverCompiler                          :C&  */
/*                                                                           */
/*  F i l e               &F: IProjectTreeNode.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.PCLObjects;

#endregion

namespace PNConfigLib.Compiler.DataTypes.Interfaces
{
    /// <summary>
    /// This interface is used for representing the nodes that constitutes the Project Tree. Each node
    /// (e.g. CPU, CPU interface, Decentral Device, it's interface submodule and ports) must implement this interface
    /// in order to generate the Project Tree.
    /// </summary>
    internal interface IProjectTreeNode
    {
        #region Properties

        string Name { get; }

        CompilerConstants.ClassRid ClassRID { get; }

        PclObject PCLObject { get; }

        List<IProjectTreeNode> ChildNodes { get; }

        List<IVariable> Variables { get; }

        #endregion

        #region Methods

        void GenerateChildren();

        void Compile();

        #endregion
    }
}