using System;

namespace PNConfigTool.Models
{
    /// <summary>
    /// 模块导航参数类，用于在页面间传递模块信息
    /// </summary>
    public class NavigationModuleParams
    {
        public string DeviceName { get; set; } = string.Empty;
        public string ModuleType { get; set; } = string.Empty;
        public string ModuleName { get; set; } = string.Empty;
        public int ModuleIndex { get; set; } = -1;
        
        public NavigationModuleParams(string deviceName, string moduleType, string moduleName, int moduleIndex)
        {
            DeviceName = deviceName;
            ModuleType = moduleType;
            ModuleName = moduleName;
            ModuleIndex = moduleIndex;
        }
    }
} 