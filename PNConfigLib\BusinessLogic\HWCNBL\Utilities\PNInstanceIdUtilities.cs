/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNInstanceIdUtilities.cs                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class PNInstanceIdUtilities
    {
        /// <summary>
        /// Generates instance id
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <returns></returns>
        public static ushort GetInstanceIdForDecentralIoSystem(Interface controllerInterfaceSubmodule)
        {
            return GetInstanceIdForControllerInterface(controllerInterfaceSubmodule);
        }

        /// <summary>
        /// Generates instance id for PNIdentification block. Note that the block is only generated for controller
        /// interface submodules.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <returns></returns>
        public static int GetInstanceIdForPNIdentificationBlock(Interface controllerInterfaceSubmodule)
        {
            return GetInstanceIdForDecentralIoSystem(controllerInterfaceSubmodule);
        }

        /// <summary>
        /// Generates instance id
        /// All IO Devices except the following are counted as "normal" IO Devices:
        /// - Integrated iDevice
        /// - MC CC-QVK Shadowdevices (DxDevice)
        /// - IE-PB Link (itself and DP-Slaves behind it)
        /// For these exceptions, other methods exist in this class and they should be used.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule"></param>
        /// <returns></returns>
        public static ushort GetInstanceIdForRemoteIoDevice(Interface deviceInterfaceSubmodule)
        {
            if (deviceInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(deviceInterfaceSubmodule));
            }

            return
                (ushort)
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIoObjectUUIDLocalIndex,
                    new AttributeAccessCode(),
                    1);
        }

        /// <summary>
        /// Generates instance id to be used at IOControllerProperties and decentralIOsystem blocks.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <returns></returns>
        private static ushort GetInstanceIdForControllerInterface(
            Interface controllerInterfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool pnIoObjectUuidV23Supported =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoObjectUuidV23Supported,
                    ac.GetNew(),
                    false);

            int instanceId = 0;

            switch (pnIoObjectUuidV23Supported)
            {
                case true:
                    int pnSubslotNumber =
                        controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.PnSubslotNumber,
                            ac.GetNew(),
                            0);
                    int interfaceNumber = (pnSubslotNumber >> 8) & 0xF;

                    bool checkDeviceIdSupported =
                        controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnIoCheckDeviceIDSupported,
                            ac,
                            false);
                    switch (checkDeviceIdSupported)
                    {
                        case true:
                            int objectUuidLocalIndex =
                                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                                    InternalAttributeNames.PnIoObjectUUIDLocalIndex,
                                    ac.GetNew(),
                                    100);

                            instanceId = (objectUuidLocalIndex & 0x0FFF) + (interfaceNumber << 12);
                            break;
                        case false:
                            DataModel.PCLObjects.IOSystem ioSystem =
                                NavigationUtilities.GetIoSystem(controllerInterfaceSubmodule);
                            if (ioSystem != null)
                            {
                                int ioSystemNumber = IOSystemUtility.GetIOSystemPositionNumber(ioSystem);
                                instanceId = (ioSystemNumber & 0x0FFF) + (interfaceNumber << 12);
                            }
                            break;
                    }
                    break;

                case false:
                    DataModel.PCLObjects.IOSystem ioSys =
                        NavigationUtilities.GetIoSystem(controllerInterfaceSubmodule);
                    if (ioSys != null)
                    {
                        instanceId = IOSystemUtility.GetIOSystemPositionNumber(ioSys);
                    }
                    break;
            }

            return (ushort)instanceId;
        }
    }
}