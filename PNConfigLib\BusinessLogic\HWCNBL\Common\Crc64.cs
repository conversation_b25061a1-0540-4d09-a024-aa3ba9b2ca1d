/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Crc64.cs                                  :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Security.Cryptography;

#endregion

namespace PNConfigLib.HWCNBL.Common
{
    /// <summary>
    /// Contains methods for making CRC calculations.
    /// </summary>
    internal class Crc64 : HashAlgorithm
    {
        #region Private implementation

        /// <summary>
        /// Creates a hash table for 64bit CRC calculation based on a polynomial.
        /// </summary>
        /// <param name="polynomial">64 bit value to determine the degree and value of the CRC.</param>
        /// <returns>The CRC hash table.</returns>
        private ulong[] createTable(ulong polynomial)
        {
            ulong[] createTable = new ulong[256];
            for (int i = 0; i < 256; ++i)
            {
                ulong entry = (ulong)i;
                for (int j = 0; j < 8; ++j)
                {
                    if ((entry & 1) == 1)
                    {
                        entry = (entry >> 1) ^ polynomial;
                    }
                    else
                    {
                        entry = entry >> 1;
                    }
                }
                createTable[i] = entry;
            }
            return createTable;
        }

        #endregion

        #region Constants and variables

        /// <summary>
        /// Default seed.
        /// </summary>
        private const ulong defaultSeed = 1;

        /// <summary>
        /// Default seed for config 2003.
        /// </summary>
        private const ulong config2003DefaultSeed = 0x42F0E1EBA9EA3693;

        /// <summary>
        /// The hash table used while calculating CRC.
        /// </summary>
        private readonly ulong[] table;

        /// <summary>
        /// The seed value used while calculating CRC.
        /// </summary>
        private readonly ulong seed;

        /// <summary>
        /// CRC value.
        /// </summary>
        private ulong crcValue;

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for Crc64, using default polynomial and seed values.
        /// </summary>
        public Crc64() : this(config2003DefaultSeed, defaultSeed)
        {
        }

        /// <summary>
        /// Constructor for Crc64, using default seed with a given polynomial value.
        /// </summary>
        /// <param name="polynomial">Polynomial value.</param>
        public Crc64(ulong polynomial) : this(polynomial, defaultSeed)
        {
        }

        /// <summary>
        /// Constructor for Crc64 with given polynomial and seed values.
        /// </summary>
        /// <param name="polynomial">Polynomial value.</param>
        /// <param name="seed">Seed value.</param>
        public Crc64(ulong polynomial, ulong seed)
        {
            table = createTable(polynomial);
            this.seed = crcValue = seed;
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Initializes the Crc64.
        /// </summary>
        public override void Initialize()
        {
            crcValue = seed;
        }

        /// <summary>
        /// Base algorithm to calculate a 64bit CRC based on a hash table.
        /// </summary>
        /// <param name="array">The input byte stream for the CRC.</param>
        /// <param name="ibStart">Index for the input byte stream from which the CRC is to be generated.</param>
        /// <param name="cbSize">Length of the byte stream in the input stream to consider for the CRC.</param>
        protected override void HashCore(byte[] array, int ibStart, int cbSize)
        {
            ulong crc = seed;

            for (int i = ibStart; i < cbSize; i++)
            {
                unchecked
                {
                    crc = (crc >> 8) ^ table[(array[i] ^ crc) & 0xff];
                }
            }

            crcValue = crc;
        }

        /// <summary>
        /// Stores and returns the result of the CRC calculation and adjusts the result if it is 0.
        /// </summary>
        /// <returns>The calculated 64 bit CRC.</returns>
        protected override byte[] HashFinal()
        {
            if (crcValue == 0)
            {
                crcValue = 1;
            }
            HashValue = BitConverter.GetBytes(crcValue);
            return HashValue;
        }

        #endregion
    }
}