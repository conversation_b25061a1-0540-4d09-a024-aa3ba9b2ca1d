/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Node.cs                                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Globalization;

using PNConfigLib.HWCNBL.Node;

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// This class represents a Node, which is used for connecting to a Subnet.
    /// </summary>
    public class Node : PclObject
    {
        /// <summary>
        /// The subnet that this Node is connected to.
        /// </summary>
        private Subnet m_Subnet;

        private const string s_NodeIdSuffix = "_Node";

        public INodeBusinessLogic NodeBusinessLogic { get; }

        public Node(Interface interfaceSubmodule)
        {
            ParentObject = interfaceSubmodule;
            Id = string.Format(
                CultureInfo.InvariantCulture,
                "{0}{1}",
                HWCNBL.Utilities.AttributeUtilities.GetName(interfaceSubmodule),
                s_NodeIdSuffix);
            NodeBusinessLogic = new NodeIeBusinessLogic(this);
        }

        /// <summary>
        /// The property for the Subnet.
        /// </summary>
        public Subnet Subnet
        {
            get { return m_Subnet; }
            set
            {
                m_Subnet = value;
                m_Subnet.Nodes.Add(this);
            }
        }

        /// <summary>
        /// Gets the list of Interface objects connected to this Node.
        /// </summary>
        /// <returns>The list of interfaces connected to this Node.</returns>
        internal override Interface GetInterface()
        {
            return GetDevice().GetInterface();
        }
    }
}