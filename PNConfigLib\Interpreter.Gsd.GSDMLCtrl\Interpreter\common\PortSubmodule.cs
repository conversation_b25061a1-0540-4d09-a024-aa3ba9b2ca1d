/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: PortSubmodule.cs                          :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

using GSDI;
using System.Collections.Generic;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The PortSubmodule ...
    /// </summary>
    public class PortSubmodule :
        SystemDefinedSubmoduleObject,
        GSDI.IPortSubmodule,
        IPortSubmodule2,
        IPortSubmodule3,
        IPortSubmodule4,
        IPortSubmodule5,
        ICompatibility
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the PortSubmodule if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public PortSubmodule()
        {
            m_MauType = GSDI.MauTypes.GSDMauNone;
            m_Name = String.Empty;
            m_NameTextID = String.Empty;    // only private!
            m_MaxPortTxDelay = 0;
            m_MaxPortRxDelay = 0;
            m_Info = null;

            m_PowerBudgetControlSupported = false;
            m_LinkStateDiagnosisCapability = GSDI.LinkStateDiagnosisCapabilities.GSDLsdcNone;

            m_SupportsRingportConfig = false;
            m_IsDefaultRingport = false;

            m_ParameterizationDisallowed = false;
            m_CheckMauTypeSupported = false;
            m_CheckMauTypeSupportedAttributePresent = false;

            m_WriteableImRecords = null;
            m_IsTransferSequenceDefined = false;

            m_ShortPreamble100MBitSupported = Attributes.s_DefaultShortPreamble100MbitSupported;
            m_IsShortPreamble100MBitSupported = false;

            m_IsIm5Supported = false;
            m_CheckMauTypeDifferenceSupported = false;
            m_CheckMauTypeDifferenceSupportedAttributePresent = false;

            m_SupportsMRP_InterconnPortConfig = false;
            m_IsMrpSupported = false;

            // GSDML V2.4
            m_SFPDiagnosisMonitoring = null;
            m_IsPROFIsafePIR_Supported = false;

            // GSDML V2.45
            m_IsProfisafeFscpTestModeSupported = false;
        }

        #endregion

        //########################################################################################
        #region Fields

        // V2.0
        private GSDI.MauTypes m_MauType;
        private string m_Name;
        private string m_NameTextID;
        private ArrayList m_ParameterRecordData;
        private uint m_MaxPortTxDelay;
        private uint m_MaxPortRxDelay;

        // V2.1
        private List<uint> m_FiberOpticTypes;
        private List<uint> m_MauTypes;
        private bool m_PowerBudgetControlSupported;
        private GSDI.LinkStateDiagnosisCapabilities m_LinkStateDiagnosisCapability;
        private bool m_PortDeactivationSupported;
        private ModuleInfo m_Info;
        private bool m_IsMrpSupported;
        private bool m_IsDefaultRingport;
        private bool m_SupportsRingportConfig;

        // V2.25
        private bool m_ParameterizationDisallowed;
        private bool m_CheckMauTypeSupported;
        private bool m_CheckMauTypeSupportedAttributePresent;
        private List<uint> m_WriteableImRecords;
        private bool m_IsTransferSequenceDefined;

        // V2.3
        private bool m_ShortPreamble100MBitSupported;
        private bool m_IsShortPreamble100MBitSupported;

        // V2.32
        private MauTypeList m_MauTypeList;
        private bool m_IsIm5Supported;
        private bool m_CheckMauTypeDifferenceSupported;
        private bool m_CheckMauTypeDifferenceSupportedAttributePresent;


        // V2.35
        private bool m_SupportsMRP_InterconnPortConfig;

        // GSDML V2.4
        private ArrayList m_SFPDiagnosisMonitoring;
        private bool m_IsPROFIsafePIR_Supported;

        // GSDML V2.45
        private bool m_IsProfisafeFscpTestModeSupported;

        #endregion

        //########################################################################################
        #region Properties


        /// <summary>
        /// ...
        /// </summary>
        public virtual MauTypes MAUType => this.m_MauType;


        /// <summary>
        /// ...
        /// </summary>
        public virtual string Name
        {
            get
            {
                if (Info != null)
                {
                    return Info.Name;
                }
                else
                {
                    return this.m_Name;
                }
            }
        }

        public override ModuleInfo Info => this.m_Info;


        /// <summary>
        /// ...
        /// </summary>
        public virtual string NameTextID
        {
            get
            {
                if (Info != null)
                {
                    return Info.NameTextID;
                }
                else
                {
                    return this.m_NameTextID;
                }
            }
        }

        /// <summary>
        /// Accesses a list of parameter record data objects, which contains
        /// information about the possible parametrization of the submodule.
        /// </summary>
        public virtual Array ParameterRecordData =>
            this.m_ParameterRecordData?.ToArray();

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 MaxPortTxDelay => this.m_MaxPortTxDelay;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 MaxPortRxDelay => this.m_MaxPortRxDelay;

        /// <summary>
		/// Accesses a list of ....
		/// </summary>
		public virtual Array FiberOpticTypes =>
            (null != this.m_FiberOpticTypes) ?
                new ArrayList(this.m_FiberOpticTypes).ToArray() :
                null;

        /// <summary>
		/// Accesses a list of ....
		/// </summary>
		public virtual Array MAUTypes =>
            (null != this.m_MauTypes) ?
                new ArrayList(this.m_MauTypes).ToArray() :
                null;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsPowerBudgetControlSupported => this.m_PowerBudgetControlSupported;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsPortDeactivationSupported => this.m_PortDeactivationSupported;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public LinkStateDiagnosisCapabilities LinkStateDiagnosisCapability => this.m_LinkStateDiagnosisCapability;

        public bool IsMrpSupported => m_IsMrpSupported;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool IsDefaultRingport => this.m_IsDefaultRingport;

        /// <summary>
		/// Accesses, ....
		/// </summary>
		/// <remarks>....</remarks>
		public bool SupportsRingportConfig => this.m_SupportsRingportConfig;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsParameterizationDisallowed => this.m_ParameterizationDisallowed;

        /// <summary>
        /// Accesses, ....
        /// </summary>
        /// <remarks>....</remarks>
        public bool IsCheckMAUTypeSupported => this.m_CheckMauTypeSupported;

        public bool IsCheckMAUTypeSupportedAttributePresent => m_CheckMauTypeSupportedAttributePresent;

        public virtual Array Writeable_IM_Records =>
            (null != this.m_WriteableImRecords) ?
                new ArrayList(this.m_WriteableImRecords).ToArray() :

                null;

        /// <summary>
        /// Accesses whether the transfer sequence for the parameter record
        /// data objects is defined.
        /// </summary>
        /// <remarks>There is so, if the transfer sequence of any parameter
        /// record data object is unequal to 0.</remarks>
        public virtual bool IsTransferSequenceDefined()
        {
            return this.m_IsTransferSequenceDefined;
        }


        public bool IsShortPreamble100MBitSupportedExt => m_ShortPreamble100MBitSupported;

        public bool IsShortPreamble100MBitSupported => m_IsShortPreamble100MBitSupported;

        public MauTypeList MAUTypeList => this.m_MauTypeList;

        public bool IsIM5_Supported => this.m_IsIm5Supported;

        public bool IsCheckMauTypeDifferenceSupported => this.m_CheckMauTypeDifferenceSupported;

        public bool IsCheckMauTypeDifferenceSupportedAttributePresent => m_CheckMauTypeDifferenceSupportedAttributePresent;

        public bool SupportsMRP_InterconnPortConfig => m_SupportsMRP_InterconnPortConfig;

        public virtual Array SFPDiagnosisMonitoring =>
            (null != this.m_SFPDiagnosisMonitoring) ?
                m_SFPDiagnosisMonitoring.ToArray() :
                null;

        public bool IsPROFIsafePIR_Supported => this.m_IsPROFIsafePIR_Supported;

        protected new object SubslotNumber => ((SystemDefinedSubmoduleObject)this).SubslotNumber;

        protected new object IdentNumber => ((ModuleObject)this).IdentNumber;

        public virtual bool IsPROFIsafeFSCP_TestMode_Supported => this.m_IsProfisafeFscpTestModeSupported;



        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                FillFieldMAUType(hash);

                FillFieldName(hash);

                FillFieldNameTextID(hash);

                FillFieldParameterRecordData(hash);

                FillFieldMaxPortTxDelay(hash);

                FillFieldMaxPortRxDelay(hash);

                FillFieldInfo(hash);

                FillFieldFiberOpticTypes(hash);

                FillFieldMAUTypes(hash);

                FillFieldIsPowerBudgetControlSupported(hash);

                FillFieldLinkStateDiagnosisCapability(hash);

                FillFieldIsPortDeactivationSupported(hash);

                FillFieldIsDefaultRingport(hash);

                FillFieldSupportsRingportConfig(hash);

                FillFieldIsParameterizationDisallowed(hash);

                FillFieldIsCheckMAUTypeSupported(hash);

                FillFieldWriteable_IM_Records(hash);

                FillFieldShortPreamble100MbitSupported(hash);

                FillFieldIsShortPreamble100MbitSupported(hash);

                FillFieldMAUTypeList(hash);

                FillFieldIM5_Supported(hash);

                FillFieldCheckMAUTypeDifferenceSupported(hash);

                FillFieldSupportsMRP_InterconnPortConfig(hash);

                FillFieldSFPDiagnosisMonitoring(hash);

                FillFieldPROFIsafePIR_Supported(hash);

                FillFieldIsMrpSupported(hash);

                FillFieldPROFIsafeFSCP_TestMode_supported(hash);
                // Base data.
                succeeded = base.Fill(hash);

                if (null != this.m_ParameterRecordData)
                {
                    if (this.m_ParameterRecordData.Count != 0)
                    {
                        // Get TransferSequence information.
                        this.m_IsTransferSequenceDefined = (((ParameterRecordData)this.m_ParameterRecordData[0]).TransferSequence != 0);

                        // Sort parameter records by index.
                        SortedList list = new SortedList();
                        foreach (RecordData o in this.m_ParameterRecordData)
                            list.Add(o.Index, o);
                        this.m_ParameterRecordData = new ArrayList(list.Values);
                    }
                }

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        private void FillFieldPROFIsafePIR_Supported(Hashtable hash)
        {
            string member = Models.s_FieldProfIsafePirSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsPROFIsafePIR_Supported = (bool)hash[member];
        }

        private void FillFieldSFPDiagnosisMonitoring(Hashtable hash)
        {
            string member = Models.s_FieldSfpDiagnosisMonitoring;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_SFPDiagnosisMonitoring = hash[member] as ArrayList;
        }

        private void FillFieldSupportsMRP_InterconnPortConfig(Hashtable hash)
        {
            string member = Models.s_FieldSupportsMrpInterconnPortConfig;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_SupportsMRP_InterconnPortConfig = (bool)hash[member];
        }

        private void FillFieldCheckMAUTypeDifferenceSupported(Hashtable hash)
        {
            string member = Models.s_FieldCheckMauTypeDifferenceSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
            {
                m_CheckMauTypeDifferenceSupportedAttributePresent = true;
                this.m_CheckMauTypeDifferenceSupported = (bool)hash[member];
            }
        }

        private void FillFieldIM5_Supported(Hashtable hash)
        {
            string member = Models.s_FieldIm5Supported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsIm5Supported = (bool)hash[member];
        }

        private void FillFieldMAUTypeList(Hashtable hash)
        {
            string member = Models.s_FieldMauTypeList;
            if (hash.ContainsKey(member)
                && hash[member] is MauTypeList)
                this.m_MauTypeList = hash[member] as MauTypeList;
        }

        private void FillFieldIsShortPreamble100MbitSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsShortPreamble100MbitSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsShortPreamble100MBitSupported = (bool)hash[member];
        }

        private void FillFieldShortPreamble100MbitSupported(Hashtable hash)
        {
            string member = Models.s_FieldShortPreamble100MbitSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ShortPreamble100MBitSupported = (bool)hash[member];
        }

        private void FillFieldWriteable_IM_Records(Hashtable hash)
        {
            string member = Models.s_FieldWriteableImRecords;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_WriteableImRecords = hash[member] as List<uint>;
        }

        private void FillFieldIsCheckMAUTypeSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsCheckMauTypeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
            {
                m_CheckMauTypeSupportedAttributePresent = true;
                m_CheckMauTypeSupported = (bool)hash[member];
            }
        }

        private void FillFieldIsParameterizationDisallowed(Hashtable hash)
        {
            string member = Models.s_FieldIsParameterizationDisallowed;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_ParameterizationDisallowed = (bool)hash[member];
        }

        private void FillFieldSupportsRingportConfig(Hashtable hash)
        {
            string member = Models.s_FieldSupportsRingportConfig;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_SupportsRingportConfig = (bool)hash[member];
        }

        private void FillFieldIsDefaultRingport(Hashtable hash)
        {
            string member = Models.s_FieldIsDefaultRingport;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsDefaultRingport = (bool)hash[member];
        }

        private void FillFieldIsPortDeactivationSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsPortDeactivationSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_PortDeactivationSupported = (bool)hash[member];
        }

        private void FillFieldLinkStateDiagnosisCapability(Hashtable hash)
        {
            string member = Models.s_FieldLinkStateDiagnosisCapability;
            if (hash.ContainsKey(member)
                && hash[member] is LinkStateDiagnosisCapabilities)
                m_LinkStateDiagnosisCapability = (LinkStateDiagnosisCapabilities)hash[member];
        }

        private void FillFieldIsPowerBudgetControlSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsPowerBudgetControlSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_PowerBudgetControlSupported = (bool)hash[member];
        }

        private void FillFieldMAUTypes(Hashtable hash)
        {
            string member = Models.s_FieldMauTypes;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_MauTypes = hash[member] as List<uint>;
        }

        private void FillFieldFiberOpticTypes(Hashtable hash)
        {
            string member = Models.s_FieldFiberOpticTypes;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                m_FiberOpticTypes = hash[member] as List<uint>;
        }

        private void FillFieldInfo(Hashtable hash)
        {
            string member = Models.s_FieldInfo;
            if (hash.ContainsKey(member)
                && hash[member] is ModuleInfo)
                this.m_Info = hash[member] as ModuleInfo;
        }

        private void FillFieldMaxPortRxDelay(Hashtable hash)
        {
            string member = Models.s_FieldMaxPortRxDelay;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxPortRxDelay = (uint)hash[member];
        }

        private void FillFieldMaxPortTxDelay(Hashtable hash)
        {
            string member = Models.s_FieldMaxPortTxDelay;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                m_MaxPortTxDelay = (uint)hash[member];
        }

        private void FillFieldParameterRecordData(Hashtable hash)
        {
            string member = Models.s_FieldParameterRecordData;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                m_ParameterRecordData = hash[member] as ArrayList;
        }

        private void FillFieldNameTextID(Hashtable hash)
        {
            string member = Models.s_FieldNameTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_NameTextID = hash[member] as string;
        }

        private void FillFieldName(Hashtable hash)
        {
            string member = Models.s_FieldName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                m_Name = hash[member] as string;
        }

        private void FillFieldMAUType(Hashtable hash)
        {
            string member = Models.s_FieldMauType;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.MauTypes)
                m_MauType = (GSDI.MauTypes)hash[member];
        }

        private void FillFieldIsMrpSupported(Hashtable hash)
        {
            string member = Models.s_FieldIsMrpSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                m_IsMrpSupported = (bool)hash[member];
        }

        private void FillFieldPROFIsafeFSCP_TestMode_supported(Hashtable hash)
        {
            string member = Models.s_FieldProfIsafeFscpTestModeSupported;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsProfisafeFscpTestModeSupported = (bool)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            bool succeeded = true;

            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectPortSubmodule);

            // ----------------------------------------------
            succeeded = this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return succeeded;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            bool succeeded = true;


            // ----------------------------------------------
            // From base class
            succeeded = base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldMauType, this.m_MauType.ToString(), Export.s_SubtypeMauTypes);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, this.m_NameTextID);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxPortTxDelay, this.m_MaxPortTxDelay);
            Export.WriteUint32Property(ref writer, Models.s_FieldMaxPortRxDelay, this.m_MaxPortRxDelay);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldParameterRecordData, this.m_ParameterRecordData);
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldInfo, this.m_Info);


            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsPowerBudgetControlSupported, this.m_PowerBudgetControlSupported);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldFiberOpticTypes, this.m_FiberOpticTypes, true);
            Export.WriteEnumProperty(ref writer, Models.s_FieldLinkStateDiagnosisCapability, this.m_LinkStateDiagnosisCapability.ToString(), Export.s_SubtypeLinkStateDiagnosisTypes);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldMauTypes, this.m_MauTypes, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsPortDeactivationSupported, this.m_PortDeactivationSupported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsDefaultRingport, this.m_IsDefaultRingport);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldSupportsRingportConfig, this.m_SupportsRingportConfig);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldTransferSequence, this.m_IsTransferSequenceDefined);
            Export.WriteArrayUint32Property(ref writer, Models.s_FieldWriteableImRecords, this.m_WriteableImRecords, true);

            
            Export.WriteSimpleObjectProperty(option, ref writer, Models.s_FieldMauTypeList, this.m_MauTypeList);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIm5Supported, m_IsIm5Supported);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldCheckMauTypeDifferenceSupported, m_CheckMauTypeDifferenceSupported);

            Export.WriteBooleanProperty(ref writer, Models.s_FieldSupportsMrpInterconnPortConfig, m_SupportsMRP_InterconnPortConfig);

            // V2.4
            Export.WriteArrayStringProperty(ref writer, Models.s_FieldSfpDiagnosisMonitoring, m_SFPDiagnosisMonitoring, true);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafePirSupported, m_IsPROFIsafePIR_Supported);

            // V2.45
            Export.WriteBooleanProperty(ref writer, Models.s_FieldProfIsafeFscpTestModeSupported, m_IsProfisafeFscpTestModeSupported);

            return succeeded;
        }

        #endregion

    }
}


