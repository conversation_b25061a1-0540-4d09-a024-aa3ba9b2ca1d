/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: TailorConfigLogicDecentral.cs             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.Config
{
    /// <summary>
    /// Summary description for TailorConfigLogicDecentral.
    /// </summary>
    internal class TailorConfigLogicDecentral : TailorConfigLogicBase
    {
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private DataModel.PCLObjects.Interface m_IoControllerInterface;

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public TailorConfigLogicDecentral(
            DataModel.PCLObjects.Interface ioControllerInterface,
            DataModel.PCLObjects.Interface ioDeviceInterface) : base(ioDeviceInterface)
        {
            m_IoControllerInterface = ioControllerInterface;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Overrides and overridables
        // Contains all public and protected overrides as well as overridables of the class
        public override void InitTailorProperties()
        {
            base.InitTailorProperties();

            bool tailor = AddressTailoringEnabled;
            bool optional = Utilities.MachineTailor.MachineTailorUtility.IsOptionalDeviceEnabled(InterfaceSubmodule);
            //for the prototype we set ipv4suite tailoring of iodevice to true if the iosystem is set to multiple deployable
            //and IP via Other Path is not selected

            DataModel.PCLObjects.Node node = InterfaceSubmodule.Node;
            bool isPNIoIpSuiteViaOtherPath = false;
            bool isPNPNIpSuiteViaOtherPath = node.AttributeAccess.GetAnyAttribute<Boolean>(
                InternalAttributeNames.PnPnIpSuiteViaOtherPath, new AttributeAccessCode(), false);
            if (!isPNPNIpSuiteViaOtherPath)
            {
                isPNIoIpSuiteViaOtherPath = node.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnIoIpSuiteViaOtherPath, new AttributeAccessCode(), false);
            }

            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.DeviceMode, optional);
            //For iodevice use ipv4suite tailoring if the user selected "set ip address using address tailoring"
            //For iDevice the set via other path is selected by default, thus has to be considered here as well
            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.IPv4Suite,
                                                   tailor &&
                                                   ((!(isPNPNIpSuiteViaOtherPath || isPNIoIpSuiteViaOtherPath)) ||
                                                    AttributeUtilities.IsIDevice(InterfaceSubmodule)));
            DeviceTailorProperties.SetBlockElement(DeviceTailorPropertiesEnum.TailorPDIRFrameData,
                CalculateTailorPDIRData()); 
        }
        public override bool HasPDIRData()
        {
            // only for machine tailoring must be activated
            return ConfigUtility.IsSynchronized(InterfaceSubmodule, PNInterfaceType);
        }

        #endregion

        public override PNInterfaceType PNInterfaceType =>  PNInterfaceType.IODevice;

        ///////////////////////////////////////////////////////////////////////////////////////////
        #region Protected methods
        // Contains all protected (non overridables) methods of the class
        protected override DataModel.PCLObjects.Interface IoControllerInterface => m_IoControllerInterface;

        new protected bool CalculateTailorPDIRData()
        {
            AttributeAccessCode accessCode = new AttributeAccessCode();
            PNIrtForwardingMode forwardingMode = (PNIrtForwardingMode)InterfaceSubmodule.AttributeAccess.GetAnyAttribute
                <Enumerated>(InternalAttributeNames.PnIrtForwardingMode, accessCode, null).DefaultValue;
            if (base.CalculateTailorPDIRData() && forwardingMode != PNIrtForwardingMode.Relative && accessCode.IsOkay)
            {
                return true;
            }
            return false;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Delegates and events

        // Contains all delegate and events

        #endregion
    }
}