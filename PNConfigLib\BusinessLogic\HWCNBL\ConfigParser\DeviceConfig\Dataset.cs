/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Dataset.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.ConfigParser.DeviceConfig
{
    internal class Dataset : ConfigBlock, IDataset
    {
        public Dataset(byte[] data, int offset, int length) : base(data, offset, length)
        {
        }

        public Dataset()
        {
        }

        #region interface IDataset

        /// <summary>
        /// Length of the nettodata
        /// </summary>
        public ushort ParaDS_Len
        {
            get { return Read16(2); }
        }

        /// <summary>
        /// Dataset number.
        /// Record index is extended to support data record indices > 0xffff
        /// </summary>
        public uint ParaDS_Nr =>  Read16(4) + (uint)(Read16(6) << 16);

        /// <summary>
        /// The netto dataset.
        /// </summary>
        public byte[] GetDatasetNetto()
        {
            return ReadByteblock(8, ParaDS_Len);
        }

        /// <summary>
        /// The result is the complete datablock from the config.
        /// such a block starts with the field "ParaDS_BlockLen".
        /// </summary>
        public byte[] GetCompleteDataBlock()
        {
            ushort length = Read16(0);
            byte[] result = ReadByteblock(0, length);
            return result;
        }

        #endregion
    }
}