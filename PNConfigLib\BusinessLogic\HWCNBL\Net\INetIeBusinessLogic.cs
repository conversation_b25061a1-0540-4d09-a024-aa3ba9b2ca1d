/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: INetIeBusinessLogic.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections;
using System.Collections.Generic;

using PNConfigLib.HWCNBL.Utilities.Network;

#endregion

namespace PNConfigLib.HWCNBL.Net
{
    /// <summary>
    /// Industrial ethernet related business class interface for Subnet.
    /// </summary>
    public interface INetIeBusinessLogic : INetBL
    {
        /// <summary>
        /// Gets all used addresses on this subnet.
        /// </summary>
        /// <returns>A dictionary containing the node hashcodes and corresponding IP addresses.</returns>
        IDictionary GetUsedIPAddresses();

        /// <summary>
        /// Gets all used addresses on this subnet, except a given node.
        /// </summary>
        /// <remarks>
        /// Own node can be specified as the excluded node, in order to get all
        /// forbidden addresses for a given node.
        /// </remarks>
        /// <param name="excludedNode">The node to be excluded.</param>
        /// <returns>A dictionary containing the node hashcodes and corresponding IP addresses.</returns>
        IDictionary GetUsedIPAddresses(DataModel.PCLObjects.Node excludedNode);
    }
}