/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: RefData.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The RefData objects are used to reference a section in a record 
    /// data block and to change parameter settings on this sections.
    /// </summary>
    public class RefData :
        GsdObject,
        GSDI.IRefData,
        GSDI.IRefData2
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public RefData()
        {
            m_Name = String.Empty;
            m_NameTextID = String.Empty;
            m_IsChangeable = true;
            m_IsVisible = true;
            m_ByteOffset = 0;
            m_BitOffset = 0;
            m_BitLength = 1;
            m_DataType = GSDI.DataTypes.GSDBit;
            m_ValueGsdID = String.Empty;
            m_ValueType = GSDI.ValueTypes.GSDConcrete;
            m_HelpTextId = String.Empty;
            m_ParamID = String.Empty;

        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_Name;
        private string m_NameTextID;
        private bool m_IsChangeable;
        private bool m_IsVisible;
        private uint m_ByteOffset;
        private uint m_BitOffset;
        private uint m_BitLength;
        private GSDI.DataTypes m_DataType;
        private uint m_Length;
        private string m_ValueGsdID;    // only private
        private object m_DefaultValue;
        private ArrayList m_Values;
        private GSDI.ValueTypes m_ValueType;
        private string m_Help;
        private string m_HelpTextId;
        private string m_ParamID;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
		/// Accesses the human readable, language specific name of the ref data
		/// object.
		/// </summary>
		public string Identifier
        {
            get; set;
        }

        /// <summary>
        /// Accesses the human readable, language specific name of the ref data
        /// object.
        /// </summary>
        public string Name => this.m_Name;

        /// <summary>
		/// Accesses the language independent text id of the name of the ref data
		/// object.
		/// </summary>
		public string NameTextID => this.m_NameTextID;

        // RQ ********** begin
        /// <summary>
        /// Accesses the language independent parameter id of the name of the ref data
        /// object.
        /// </summary>
        public string ParamID => this.m_ParamID;
        // RQ ********** end

        /// <summary>
        /// Accesses whether this parameter is changeable or not.
        /// </summary>
        public bool IsChangeable => this.m_IsChangeable;

        /// <summary>
		/// Accesses whether this parameter should be visible within the engineering
		/// tool.
		/// </summary>
		public bool IsVisible => this.m_IsVisible;

        /// <summary>
		/// Accesses the offset in octets of the referenced data from the beginning 
		/// of the record data object.
		/// </summary>
		public UInt32 ByteOffset => this.m_ByteOffset;

        /// <summary>
		/// Accesses the offset in bits of the referenced data from the beginning of 
		/// the referenced octet.
		/// </summary>
		/// <remarks>Can only be used in conjunction with data type Bit or BitArea 
		/// and the valid range is 0 to 7.</remarks>
		public UInt32 BitOffset => this.m_BitOffset;

        /// <summary>
		/// Accesses the length of this field in bit.
		/// </summary>
		public UInt32 BitLength => this.m_BitLength;

        /// <summary>
		/// Accesses the type of the data for this field.
		/// </summary>
		public virtual GSDI.DataTypes DataType => this.m_DataType;

        /// <summary>
		/// Accesses the default value for this parameter.
		/// </summary>
		/// <remarks>Which type is returned, could be evaluated with the Type 
		/// property.</remarks>
		public object DefaultValue => this.m_DefaultValue;

        /// <summary>
		/// Returns a list of value item objects or area item objects, which 
		/// are possible for the parameter.
		/// </summary>
		/// <remarks>Which type is returned from this object properties, could be 
		/// evaluated with the Type property.</remarks>
		public virtual Array Values =>
            this.m_Values?.ToArray();

        /// <summary>
		/// Accesses which type of values (objects) are available from the Values
		/// list. Possible are value item or area item objects.
		/// </summary>
		public virtual GSDI.ValueTypes ValueType => this.m_ValueType;

        /// <summary>
        /// Accesses the length of the parameter
        /// </summary>
        public virtual UInt32 Length => this.m_Length;

        /// <summary>
		/// Accesses the language specific help text for this parameter.
		/// </summary>
		public string Help => this.m_Help;

        /// <summary>
		/// Accesses the help text for this parameter.. Not accessible via COM
		/// </summary>
		public string HelpTextId => this.m_HelpTextId;

        protected string DefaultValueAsString =>
            (null != this.DefaultValue) ?
                this.DefaultValue.ToString() :
                String.Empty;


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");
                FillFieldValueGsdID(hash);

                FillFieldName(hash);

                FillFieldNameTextID(hash);

                // RQ ********** begin
                FillFieldParamID(hash);
                // RQ ********** end

                FillFieldIsChangeable(hash);

                FillFieldIsVisible(hash);

                FillFieldByteOffset(hash);

                FillFieldBitOffset(hash);

                FillFieldBitLength(hash);

                FillFieldDataType(hash);

                FillFieldDefaultValue(hash);

                FillFieldValues(hash);

                FillFieldValueType(hash);

                FillFieldLength(hash);

                FillFieldHelp(hash);

                FillFieldHelpTextId(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        private void FillFieldHelpTextId(Hashtable hash)
        {
            string member = Models.s_FieldHelpTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_HelpTextId = hash[member] as string;
        }

        private void FillFieldHelp(Hashtable hash)
        {
            string member = Models.s_FieldHelp;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_Help = hash[member] as string;
        }

        private void FillFieldLength(Hashtable hash)
        {
            string member = Models.s_FieldLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_Length = (uint)hash[member];
        }

        private void FillFieldValueType(Hashtable hash)
        {
            string member = Models.s_FieldValueType;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.ValueTypes)
                this.m_ValueType = (GSDI.ValueTypes)hash[member];
        }

        private void FillFieldValues(Hashtable hash)
        {
            string member = Models.s_FieldValues;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_Values = hash[member] as ArrayList;
        }

        private void FillFieldDefaultValue(Hashtable hash)
        {
            string member = Models.s_FieldDefaultValue;
            if (hash.ContainsKey(member))
                this.m_DefaultValue = hash[member];
        }

        private void FillFieldDataType(Hashtable hash)
        {
            string member = Models.s_FieldDataType;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.DataTypes)
                this.m_DataType = (GSDI.DataTypes)hash[member];
        }

        private void FillFieldBitLength(Hashtable hash)
        {
            string member = Models.s_FieldBitLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_BitLength = (uint)hash[member];
        }

        private void FillFieldBitOffset(Hashtable hash)
        {
            string member = Models.s_FieldBitOffset;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_BitOffset = (uint)hash[member];
        }

        private void FillFieldByteOffset(Hashtable hash)
        {
            string member = Models.s_FieldByteOffset;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_ByteOffset = (uint)hash[member];
        }

        private void FillFieldIsVisible(Hashtable hash)
        {
            string member = Models.s_FieldIsVisible;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsVisible = (bool)hash[member];
        }

        private void FillFieldIsChangeable(Hashtable hash)
        {
            string member = Models.s_FieldIsChangeable;
            if (hash.ContainsKey(member)
                && hash[member] is bool)
                this.m_IsChangeable = (bool)hash[member];
        }

        private void FillFieldParamID(Hashtable hash)
        {
            string member = Models.s_FieldParamId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_ParamID = hash[member] as string;
        }

        private void FillFieldNameTextID(Hashtable hash)
        {
            string member = Models.s_FieldNameTextId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_NameTextID = hash[member] as string;
        }

        private void FillFieldName(Hashtable hash)
        {
            string member = Models.s_FieldName;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_Name = hash[member] as string;
        }

        private void FillFieldValueGsdID(Hashtable hash)
        {
            string member = Models.s_FieldValueGsdId;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_ValueGsdID = hash[member] as string;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectRefData);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);
            Export.WriteStringProperty(ref writer, Models.s_FieldNameTextId, this.m_NameTextID);
            Export.WriteStringProperty(ref writer, Models.s_FieldParamId, this.m_ParamID);
            Export.WriteStringProperty(ref writer, Models.s_FieldHelp, this.m_Help);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsChangeable, this.m_IsChangeable);
            Export.WriteBooleanProperty(ref writer, Models.s_FieldIsVisible, this.m_IsVisible);
            Export.WriteUint32Property(ref writer, Models.s_FieldByteOffset, this.m_ByteOffset);
            Export.WriteUint32Property(ref writer, Models.s_FieldBitOffset, this.m_BitOffset);
            Export.WriteUint32Property(ref writer, Models.s_FieldBitLength, this.m_BitLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldLength, this.m_Length);
            Export.WriteEnumProperty(ref writer, Models.s_FieldDataType, this.m_DataType.ToString(), Export.s_SubtypeDataTypes);
            Export.WriteStringProperty(ref writer, Models.s_FieldValueGsdId, this.m_ValueGsdID);
            Export.WriteEnumProperty(ref writer, Models.s_FieldValueType, this.m_ValueType.ToString(), Export.s_SubtypeValueTypes);
            string val = String.Empty;
            if (null != this.m_DefaultValue)    // Normally object, not string!
                val = this.m_DefaultValue.ToString();
            Export.WriteStringProperty(ref writer, Models.s_FieldDefaultValue, val);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldValues, this.m_Values);

            return true;
        }

        #endregion

        //########################################################################################
        #region Object members
        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(RefData))
                return false;

            RefData ref1 = this;
            RefData ref2 = obj as RefData;

            if (ref2 == null)
            {
                return false;
            }

            if (ref1.BitLength != ref2.BitLength)
                return false;

            if (ref1.BitOffset != ref2.BitOffset)
                return false;

            if (ref1.ByteOffset != ref2.ByteOffset)
                return false;

            if (ref1.DataType != ref2.DataType)
                return false;

            if (!ref1.DefaultValue.Equals(ref2.DefaultValue))
                return false;

            if (ref1.IsChangeable != ref2.IsChangeable)
                return false;

            if (ref1.IsVisible != ref2.IsVisible)
                return false;

            if (ref1.ValueType != ref2.ValueType)
                return false;

            if (ref1.Values == null && ref2.Values != null)
                return false;

            if (ref1.Values == null
                || ref2.Values == null)
            {
                return true;
            }

            if (ref1.Values.Length != ref2.Values.Length)
                return false;

            for (int i = 0; i < Values.Length; i++)
            {
                if (!ref1.Values.GetValue(i).Equals(ref2.Values.GetValue(i)))
                    return false;
            }

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 23;

            hash = hash * 17 + BitLength.GetHashCode();
            hash = hash * 17 + BitOffset.GetHashCode();
            hash = hash * 17 + ByteOffset.GetHashCode();
            hash = hash * 17 + DataType.GetHashCode();
            hash = hash * 17 + DefaultValue.GetHashCode();
            hash = hash * 17 + IsChangeable.GetHashCode();
            hash = hash * 17 + IsVisible.GetHashCode();
            hash = hash * 17 + ValueType.GetHashCode();

            if (Values != null)
            {
                foreach (Object obj in Values)
                {
                    hash = hash * 17 + obj.GetHashCode();
                }
            }

            return hash;
        }
        #endregion

    }
}


