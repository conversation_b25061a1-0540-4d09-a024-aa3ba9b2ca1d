/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: HwcnBusinessLogic.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.HWCNBL
{
    public class HwcnBusinessLogic : IBusinessLogic
    {
        internal Dictionary<uint, byte[]> FillParameterRecordDataItems(PclObject pclObject, 
            List<ParameterRecordDataItemsTypeParameterRecordDataItem> customParameterRecordDataItems, string configuratonObjectId)
        {
            Dictionary<uint, byte[]> retval = new Dictionary<uint, byte[]>();
            if (pclObject.PCLCatalogObject.ParameterRecordDataList != null)
            {
                foreach (ParameterRecordData parameterRecordData in pclObject.PCLCatalogObject.ParameterRecordDataList)
                {
                    byte[] parameterRecordDataBytes = new byte[parameterRecordData.Length];
                    ParameterRecordDataUtilities.FillBytes(parameterRecordData, parameterRecordDataBytes);
                    ParameterRecordDataItemsTypeParameterRecordDataItem customParameterRecordDataItem =
                        customParameterRecordDataItems.SingleOrDefault(
                            e => e.GSDRefIndex == parameterRecordData.Index);

                    if (customParameterRecordDataItem != null)
                    {
                        ParameterRecordDataUtilities.FillCustomRefs(
                            customParameterRecordDataItem,
                            parameterRecordData,
                            parameterRecordDataBytes);
                        customParameterRecordDataItems.Remove(customParameterRecordDataItem);
                    }
                    retval.Add(parameterRecordData.Index, parameterRecordDataBytes);
                }
                if (customParameterRecordDataItems.Count > 0)
                {
                    string incorrectIndexes = string.Empty;
                    foreach (ParameterRecordDataItemsTypeParameterRecordDataItem incorrectParameterRecordDataItem in
                        customParameterRecordDataItems)
                    {
                        incorrectIndexes = incorrectIndexes + ", " + incorrectParameterRecordDataItem.GSDRefIndex;
                    }
                    incorrectIndexes = incorrectIndexes.Remove(0, 2);

                    throw new InvalidOperationException(
                        string.Format(CultureInfo.InvariantCulture, 
                            "Incorrect parameter record data item indexes ({0}) in configuration object: {1}",
                            incorrectIndexes, configuratonObjectId));
                }
            }
            return retval;
        }

        #region GeneralAttribute
        internal void FillGeneralAttributes(PclObject pclObject, GeneralType generalAttr,
            string pnDeviceName = null, string lonDeviceName = null, string lonDeviceId = null)
        {
            generalAttr.Name = !string.IsNullOrEmpty(pnDeviceName)
                        ? pnDeviceName
                        : (
                            !string.IsNullOrEmpty(lonDeviceName)
                                ? lonDeviceName : lonDeviceId
                        );
            FillGeneralAttributes(pclObject, generalAttr);
        }

        internal void FillGeneralAttributes(PclObject pclObject, GeneralType generalAttr, string objectId)
        {
            if(string.IsNullOrEmpty(generalAttr.Name))
            generalAttr.Name = !string.IsNullOrEmpty(generalAttr.Name) ? generalAttr.Name : objectId;
            FillGeneralAttributes(pclObject, generalAttr);
        }

        private void FillGeneralAttributes(PclObject pclObject, GeneralType generalAttr)
        {
            pclObject.AttributeAccess.SetAnyAttribute<string>(InternalAttributeNames.Name, generalAttr.Name);
            pclObject.AttributeAccess.SetAnyAttribute<string>(
                InternalAttributeNames.Author, generalAttr.Author ?? "PNConfigLib");
            pclObject.AttributeAccess.SetAnyAttribute<string>(
                InternalAttributeNames.Comment, generalAttr.Comment ?? string.Empty);
        }
        #endregion
    }
}

