/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: NetloadClasses.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// This element contains the definition of the netload class according to the link speed.
    /// </summary>
    //[ComVisible(true), Guid("0022C67A-2373-4AB8-AD13-168810EA2C9D")] 
    public class NetloadClasses : 
		GsdObject,
		GSDI.INetloadClasses

    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the NetloadClasses if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public NetloadClasses()
		{
            m_NetloadClass = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private GSDI.LinkSpeeds m_LinkSpeed;
        private string m_LinkSpeedAsString;
        private string m_NetloadClass;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the SegmentClass.
        /// </summary>
        public GSDI.LinkSpeeds LinkSpeed => this.m_LinkSpeed;

        /// <summary>
        /// Accesses the SegmentClass.
        /// </summary>
        public string LinkSpeedAsString => this.m_LinkSpeedAsString;

        /// <summary>
        /// Accesses the PortClass.
        /// </summary>
        public string NetloadClass => this.m_NetloadClass;

#if !S7PLUS
        #region COM Interface Members Only

        #endregion
#endif

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        override internal bool Fill(System.Collections.Hashtable hash)
		{
			bool succeeded = true;

			try
			{
				// Check parameter
				if (null == hash)
					throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldLinkSpeed;
                if (hash.ContainsKey(member) && hash[member] is GSDI.LinkSpeeds)
                    m_LinkSpeed = (GSDI.LinkSpeeds)hash[member];

                member = Models.s_FieldLinkSpeedAsString;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_LinkSpeedAsString = (string)hash[member];

                member = Models.s_FieldNetloadClass;
                if (hash.ContainsKey(member) && hash[member] is string)
                    m_NetloadClass = (string)hash[member];

                // Base data.
                //succeeded = base.Fill(hash);
            }
            catch (FillException)
			{
				succeeded = false;
			}

			return succeeded;
		}

		/// <summary>
		/// Serializes the object itself and his properties to a fixed XML 
		/// structure.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successfull, else false.</returns>
		override internal bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{ 
			// Object begin.
			writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectNetloadClasses);

			// ----------------------------------------------
			this.SerializeMembers(option, ref writer);

			// Object end.
			writer.WriteEndElement();

			return true; 
		}

		/// <summary>
		/// Serializes only the properties of the object to a fixed XML 
		/// structure, without any enclosing element for the object itself.
		/// </summary>
		/// <param name="option">This option specifies, whether the object should
		/// be serialized flat, deep and so on.</param>
		/// <param name="writer">It's an XmlTextWriter, which is used to
		/// write out all relevant xml tags and the needed content data.</param>
		/// <returns>True, if serializing was successful, else false.</returns>
		override internal bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer) 
		{
            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldLinkSpeed, this.m_LinkSpeed.ToString(), Export.s_SubtypeLinkSpeeds);
            Export.WriteStringProperty(ref writer, Models.s_FieldNetloadClass, this.m_NetloadClass);

            return true; 
		}


		#endregion
	}
}
