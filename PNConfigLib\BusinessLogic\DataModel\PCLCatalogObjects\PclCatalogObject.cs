/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PclCatalogObject.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;

using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;

#endregion

namespace PNConfigLib.DataModel.PCLCatalogObjects
{
    /// <summary>
    /// The base catalog object that contains hardware specific information.
    /// </summary>
    /// <remarks>
    /// PclCatalogObject and PclObject have a class - instance like relation. Catalog objects are the
    /// "type" of the hardware; attributes and features that will be within all instances of that hardware
    /// are contained within PclCatalogObject, whereas settings that can be changed by the user are contained
    /// within PclObject.
    /// </remarks>
    public class PclCatalogObject : IPclObject
    {
        /// <summary>
        /// The member used for accessing attributes within the catalog object.
        /// </summary>
        public AttributeAccess AttributeAccess { get; set; }

        /// <summary>
        /// The default constructor for PclCatalogObject.
        /// </summary>
        public PclCatalogObject()
        {
            AttributeAccess = new AttributeAccess(this);
        }

        /// <summary>
        /// An array that keeps the ParameterRecordDataItem objects, which are custom settings on the hardware
        /// that can be defined by the user in GSDML.
        /// </summary>
        /// <remarks>
        /// Only interfaces, ports and submodules (including virtual submodules) can contain parameter record data items.
        /// </remarks>
        public Array ParameterRecordDataList { get; set; }
    }
}