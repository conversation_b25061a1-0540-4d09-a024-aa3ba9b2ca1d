/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: CheckerV02_025.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml.Linq;
using System.Xml.XPath;

namespace PNConfigLib.Gsd.Interpreter.Checker
{
    /// <summary>
    /// Internal checker object, which is implemented for the GSD(ML)
    /// version 2.25 and is based on GSD(ML) versions 2.2 and lower.
    ///		
    /// </summary>
    internal class CheckerV02025 : CheckerV0202
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Creates an instance of this class, which especially implements checks for GSD(ML) version 2.25.
        /// </summary>
        public CheckerV02025()
        {
            SetSupportedGsdmlVersion(Constants.s_Version225);
        }

        #endregion

        #region Properties

        protected virtual GSDI.ReportTypes ReportType_0X00012205 => GSDI.ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X000122062 => GSDI.ReportTypes.GSD_RT_MinorError;

        protected virtual GSDI.ReportTypes ReportType_0X00012208 => GSDI.ReportTypes.GSD_RT_Warning;

        protected override GSDI.ReportTypes ReportType_0X00010020 => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X0001002D => GSDI.ReportTypes.GSD_RT_Error;

        protected override GSDI.ReportTypes ReportType_0X000100302 => GSDI.ReportTypes.GSD_RT_Error;

        protected override string AttributesWithValueList
        {
            get
            {
                string xp = base.AttributesWithValueList +
                            " | .//gsddef:VirtualSubmoduleItem/@SupportedSubstitutionModes" +
                            " | .//gsddef:SubmoduleItem/@SupportedSubstitutionModes" +
                            " | .//gsddef:ModuleItem/@FieldbusType" +
                            " | .//gsddef:SlotCluster/@FieldbusType";
                return (xp);
            }
        }

        #endregion

        #region CheckerObject Members

        protected override string GetSchemaFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlDeviceProfileNameV02025;
        }
        protected override string GetPrimitivesFileName()
        {
            return PNConfigLib.Gsd.Interpreter.Namespaces.s_SchemaGsdmlPrimitivesNameV02025;
        }

        protected override bool RegisterChecks()
        {
            bool succeeded = base.RegisterChecks();


            if (Checks == null)
                Checks = new List<string>();

            Checks.Add(Constants.s_Cn_0X00012202);
            Checks.Add(Constants.s_Cn_0X00012203);
            Checks.Add(Constants.s_Cn_0X00012204);
            Checks.Add(Constants.s_Cn_0X00012205);
            Checks.Add(Constants.s_Cn_0X00012206);
            Checks.Add(Constants.s_Cn_0X00012207);
            Checks.Add(Constants.s_Cn_0X00012208);
            Checks.Add(Constants.s_Cn_0X00012209);
            Checks.Add(Constants.s_Cn_0X0001220A);
            Checks.Add(Constants.s_Cn_0X0001220B);
            Checks.Add(Constants.s_Cn_0x0001220C);
            return succeeded;
        }

        protected override bool DeregisterObsoleteChecks()
        {
            bool succeeded = base.DeregisterObsoleteChecks();


            try
            {
                Checks.Remove(Constants.s_Cn_0X0001000A);
                Checks.Remove(Constants.s_Cn_0X0001211D);
            }
            catch (NotSupportedException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Check whether a FAP can be operated at the DAP.
        /// 
        /// </summary>
        /// <returns>True, if a FAP can be operated at the DAP.</returns>
        protected bool CanFapBeOperatedAtDAP(XElement dap, out UInt16 maxCount, out bool fapWithoutSlotClusterFound, SortedList<UInt16, UInt16> usedFieldbusTypes)
        {
            fapWithoutSlotClusterFound = false;
            maxCount = 0;

            if (dap.Name.LocalName != Elements.s_DeviceAccessPointItem)
            {
                return false;
            }

            // (1) Check all VirtualSubmoduleItems at the DAP
            bool fapWithSlotClusterFound = CheckAllVirtualSubmoduleItemsAtTheDAP(dap, ref maxCount, ref fapWithoutSlotClusterFound, usedFieldbusTypes, false);


            // (2) Check all UseableSubmodules at the DAP
            fapWithSlotClusterFound = CheckAllUseableSubmodulesAtTheDAP(dap, ref maxCount, ref fapWithoutSlotClusterFound, usedFieldbusTypes, fapWithSlotClusterFound);


            // (3) Check all VirtualSubmoduleItem at the module
            // (4) Check all UseableSubmodules at the module
            string xp = "./gsddef:UseableModules/gsddef:ModuleItemRef";
            var moduleItemRefs = dap.XPathSelectElements(xp, Nsmgr);
            foreach (var moduleItemRef in moduleItemRefs)
            {
                string moduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(moduleItemRef, Attributes.s_ModuleItemTarget));
                PluggableModuleItems.TryGetValue(moduleTarget, out XElement module);

                if (null == module)
                    continue;

                // (3) Check all VirtualSubmoduleItem of fixed modules at the DAP
                fapWithSlotClusterFound = CheckAllVirtualSubmoduleItemOfFixedModulesAtTheDAP(ref maxCount, ref fapWithoutSlotClusterFound, usedFieldbusTypes, module, fapWithSlotClusterFound);


                // (4) Check all UseableSubmodules of fixed modules at the DAP
                fapWithSlotClusterFound = CheckAllUseableSubmodulesOfFixedModulesAtTheDAP(ref maxCount, ref fapWithoutSlotClusterFound, usedFieldbusTypes, module, fapWithSlotClusterFound);

            }

            return fapWithSlotClusterFound;

        }

        private bool CheckAllVirtualSubmoduleItemOfFixedModulesAtTheDAP(
            ref ushort maxCount,
            ref bool fapWithoutSlotClusterFound,
            IDictionary<ushort, ushort> usedFieldbusTypes,
            XNode module,
            bool fapWithSlotClusterFound)
        {
            string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem";
            var virtualSubmoduleItemsModule = module.XPathSelectElements(xp, Nsmgr);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItemsModule)
            {

                uint api = 0;
                if (virtualSubmoduleItem.Attribute(Attributes.s_Api) != null)
                    api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(virtualSubmoduleItem, Attributes.s_Api));
                var slotCluster = virtualSubmoduleItem.Element(NamespaceGsdDef + Elements.s_SlotCluster);
                if (slotCluster != null)
                {
                    fapWithSlotClusterFound = true;
                    UInt16 fieldbusType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_FieldbusType));
                    if (!usedFieldbusTypes.ContainsKey(fieldbusType))
                        usedFieldbusTypes.Add(fieldbusType, fieldbusType);
                    UInt16 count = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_Count)); // required attribute
                    if (count > maxCount)
                        maxCount = count;
                }
                else if (api == 17920)
                    fapWithoutSlotClusterFound = true;

            }
            return fapWithSlotClusterFound;
        }

        private bool CheckAllUseableSubmodulesOfFixedModulesAtTheDAP(
            ref ushort maxCount,
            ref bool fapWithoutSlotClusterFound,
            IDictionary<ushort, ushort> usedFieldbusTypes,
            XNode module,
            bool fapWithSlotClusterFound)
        {
            string xp = "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef";
            var submoduleItemRefsModule = module.XPathSelectElements(xp, Nsmgr);
            foreach (var submoduleItemRef in submoduleItemRefsModule)
            {
                string submoduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);
                if (null == submodule)
                    continue;

                uint api = 0;
                if (submodule.Attribute(Attributes.s_Api) != null)
                    api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(submodule, Attributes.s_Api));
                var slotCluster = submodule.Element(NamespaceGsdDef + Elements.s_SlotCluster);
                if (slotCluster != null)
                {
                    fapWithSlotClusterFound = true;
                    UInt16 fieldbusType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_FieldbusType));
                    if (!usedFieldbusTypes.ContainsKey(fieldbusType))
                        usedFieldbusTypes.Add(fieldbusType, fieldbusType);
                    UInt16 count = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_Count)); // required attribute
                    if (count > maxCount)
                        maxCount = count;
                }
                else if (api == 17920)
                    fapWithoutSlotClusterFound = true;
            }
            return fapWithSlotClusterFound;
        }



        private bool CheckAllUseableSubmodulesAtTheDAP(
            XNode dap,
             ref ushort maxCount,
             ref bool fapWithoutSlotClusterFound,
             IDictionary<ushort, ushort> usedFieldbusTypes,
             bool fapWithSlotClusterFound)
        {
            string xp = "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef";
            var submoduleItemRefs = dap.XPathSelectElements(xp, Nsmgr);
            foreach (var submoduleItemRef in submoduleItemRefs)
            {
                string submoduleTarget = Help.CollapseWhitespace(Help.GetAttributeValueFromXElement(submoduleItemRef, Attributes.s_SubmoduleItemTarget));
                PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                if (null == submodule)
                    continue;

                uint api = 0;
                if (submodule.Attribute(Attributes.s_Api) != null)
                    api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(submodule, Attributes.s_Api));
                var slotCluster = submodule.Element(NamespaceGsdDef + Elements.s_SlotCluster);
                if (slotCluster != null)
                {
                    fapWithSlotClusterFound = true;
                    UInt16 fieldbusType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_FieldbusType));
                    if (!usedFieldbusTypes.ContainsKey(fieldbusType))
                        usedFieldbusTypes.Add(fieldbusType, fieldbusType);
                    UInt16 count = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_Count)); // required attribute
                    if (count > maxCount)
                        maxCount = count;
                }
                else if (api == 17920)
                    fapWithoutSlotClusterFound = true;
            }
            return fapWithSlotClusterFound;

        }

        private bool CheckAllVirtualSubmoduleItemsAtTheDAP(
            XNode dap,
           ref ushort maxCount,
           ref bool fapWithoutSlotClusterFound,
           IDictionary<ushort, ushort> usedFieldbusTypes,
           bool fapWithSlotClusterFound)
        {
            string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem";
            var virtualSubmoduleItems = dap.XPathSelectElements(xp, Nsmgr);
            foreach (var virtualSubmoduleItem in virtualSubmoduleItems)
            {
                uint api = 0;
                if (virtualSubmoduleItem.Attribute(Attributes.s_Api) != null)
                    api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(virtualSubmoduleItem, Attributes.s_Api));
                var slotCluster = virtualSubmoduleItem.Element(NamespaceGsdDef + Elements.s_SlotCluster);
                if (slotCluster != null)
                {
                    fapWithSlotClusterFound = true;
                    UInt16 fieldbusType = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_FieldbusType));
                    if (!usedFieldbusTypes.ContainsKey(fieldbusType))
                        usedFieldbusTypes.Add(fieldbusType, fieldbusType);
                    UInt16 count = XmlConvert.ToUInt16(Help.GetAttributeValueFromXElement(slotCluster, Attributes.s_Count)); // required attribute
                    if (count > maxCount)
                        maxCount = count;
                }
                else if (api == 17920)
                    fapWithoutSlotClusterFound = true;
            }
            return fapWithSlotClusterFound;
        }

        #endregion

        #region All Checks

        #region Category : Validation

        protected override void DefineElementDescriptions()
        {
            base.DefineElementDescriptions();

            ElementDescriptions.Add("PortSubmoduleItem", "ID");
        }

        /// The set of allowed/known attribute values here depends on the PNIO_Version.
        /// 
        /// The attributes to be checked are:
        /// 
        /// As of GSDML V2.1:
        /// - MAUTypes on port submodule
        /// - FiberOpticTypes on port submodule
        /// As of GSDML V2.25:
        /// - FieldbusType at the module as well as at the SlotCluster element at the submodule
        /// - SupportedSubstitutionModes at the submodule
        /// As of GSDML V2.3:
        /// - ResetToFactoryModes at the DAP
        /// 
        /// Partly the values to be checked depend on the PNIO version at the DAP or
        /// on the smallest PNIO version at the DAPs into which the (port) submodule can be plugged.
        /// In these cases this is indicated by a special coding in the respective value ranges
        /// of the attribute. A '-x.xx' at the beginning of the value range means that this
        /// value range is only valid up to and including this PNIO version. It is followed by an
        /// 'Attribute name-x.xx' entry containing the value range for PNIO versions > x.xx.
        /// 
        /// Not to be checked in this way are the attributes:
        /// - API, APStructureIdentifier and ErrorType because profiles can change faster
        ///     than the PN IO standard / GSDML Spec / the GSDML Checker
        /// - The PROFIsafe attributes F_IO_StructureDescVersion as well as DefaultValue and AllowedValues
        ///     to F_Block_ID and F_Par_Version, because with unknown values at these attributes the
        ///     Engineering Tool and the GSDML Checker cannot do anything with them.
        ///     Here it remains with the existing hard checks.
        protected override void DefineAttributeValueListDescriptions()
        {
            base.DefineAttributeValueListDescriptions();

            string values1 = "0..2 256..511";
            AttributeValueListDictionary.Add("SupportedSubstitutionModes", values1);

            string values2 = "0..9";
            AttributeValueListDictionary.Add("FieldbusType", values2);
        }

        #endregion

        /// <summary>
        /// Check number: CN_0x00012202
        /// The value of the attribute "IsochroneMode/@T_IO_InputMin" multiplied by
        /// by the value of the attribute "IsochroneMode/@T_IO_Base "must not be 
        /// be greater than IsochroneMode/@T_DC_Max * IsochroneMode/@T_DC_Base * 31250.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012202()
        {
            const string Xp = ".//gsddef:IsochroneMode[(number(@T_IO_InputMin) * number(@T_IO_Base)) > (number(@T_DC_Max) * number(@T_DC_Base) * 31250)]";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The value of 'T_IO_InputMin' * 'T_IO_Base' must be lower or equal than 'T_DC_Max' * 'T_DC_Base' * 31250."
                    string msg = Help.GetMessageString("M_0x00012202_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                    ReportCategories.TypeSpecific, "0x00012202_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00012203
        /// The value of 'T_IO_OutputMin' * 'T_IO_Base' must be lower or equal than 'T_DC_Max' * 'T_DC_Base' * 31250.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012203()
        {
            const string Xp = ".//gsddef:IsochroneMode[(number(@T_IO_OutputMin) * number(@T_IO_Base)) > (number(@T_DC_Max) * number(@T_DC_Base) * 31250)]";
            var nl = GsdProfileBody.XPathSelectElements(Xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var en in nl)
            {
                if (Help.CheckSchemaVersion(en, SupportedGsdmlVersion))
                {
                    // "The value of 'T_IO_OutputMin' * 'T_IO_Base' must be lower or equal than 'T_DC_Max' * 'T_DC_Base' * 31250."
                    string msg = Help.GetMessageString("M_0x00012203_1");
                    string xpath = Help.GetXPath(en);
                    var xli = (IXmlLineInfo)en;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                    ReportCategories.TypeSpecific, "0x00012203_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x00012204
        /// (1) Fieldbus integration modules (those which have attribute FieldbusType) shall not have VirtualSubmoduleItem
        ///     with SlotCluster given and shall not have UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget
        ///     which references a SubmoduleItem with SlotCluster given.
        /// (2) Fieldbus integration modules shall not have SystemDefinedSubmoduleList and shall not have
        ///     UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget which references a PortSubmoduleItem.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012204()
        {
            string xp = ".//gsddef:ModuleItem[@FieldbusType]";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var fieldbusIntegrationModule in nl)
            {
                // (1)
                CreateReport0x00012204_1(fieldbusIntegrationModule);

                CreateReport0x00012204_3(fieldbusIntegrationModule);


                // Check UseableSubmodule
                xp = "./gsddef:UseableSubmodules/gsddef:SubmoduleItemRef/@SubmoduleItemTarget";
                var submoduleTargetNodes = (IEnumerable)fieldbusIntegrationModule.XPathEvaluate(xp, Nsmgr);
                foreach (XAttribute submoduleTargetNode in submoduleTargetNodes)
                {
                    var lineInfo = (IXmlLineInfo)submoduleTargetNode;

                    string submoduleTarget = Help.CollapseWhitespace(submoduleTargetNode.Value);

                    PluggableSubmoduleItems.TryGetValue(submoduleTarget, out XElement submodule);

                    if (submodule != null)
                    {
                        CreateReport0x00012204_2(submodule, submoduleTargetNode, lineInfo);

                    }
                    else
                    {
                        CreateReport0x00012204_4(submoduleTarget, submoduleTargetNode, lineInfo);

                    }
                }
            }

            return true;
        }

        private void CreateReport0x00012204_4(string submoduleTarget, XObject submoduleTargetNode, IXmlLineInfo lineInfo)
        {
            PluggablePortSubmoduleItems.TryGetValue(submoduleTarget, out XElement portSubmodule);

            if (portSubmodule == null)
                return;

            if (!Help.CheckSchemaVersion(submoduleTargetNode, SupportedGsdmlVersion))
            {
                return;
            }
            // "If the attribute 'ModuleItem/@FieldbusType' is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget
            // must not reference a PortSubmoduleItem."
            string msg = Help.GetMessageString("M_0x00012204_4");
            string xpath = Help.GetXPath(submoduleTargetNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012204_4");
        }
        private void CreateReport0x00012204_2(XElement submodule, XObject submoduleTargetNode, IXmlLineInfo lineInfo)
        {
            uint api = 0;
            if (submodule.Attribute(Attributes.s_Api) != null)
                api = XmlConvert.ToUInt32(Help.GetAttributeValueFromXElement(submodule, Attributes.s_Api));
            if (api != 17920)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(submoduleTargetNode, SupportedGsdmlVersion))
            {
                return;
            }
            // "If the attribute 'ModuleItem/@FieldbusType' is present, UseableSubmodules/SubmoduleItemRef/@SubmoduleItemTarget
            // must not reference a SubmoduleItem which works as a FAP ('@API' = 17920)."
            string msg = Help.GetMessageString("M_0x00012204_2");
            string xpath = Help.GetXPath(submoduleTargetNode);
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                lineInfo.LineNumber,
                lineInfo.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012204_2");

        }

        private void CreateReport0x00012204_3(XNode fieldbusIntegrationModule)
        {
            string xp = "./gsddef:SystemDefinedSubmoduleList";
            var systemDefinedSubmoduleList = fieldbusIntegrationModule.XPathSelectElement(xp, Nsmgr);
            if (systemDefinedSubmoduleList == null)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(systemDefinedSubmoduleList, SupportedGsdmlVersion))
            {
                return;
            }
            // "If the attribute 'ModuleItem/@FieldbusType' is present, 'SystemDefinedSubmoduleList' must not be given."
            string msg = Help.GetMessageString("M_0x00012204_3");
            string xpath = Help.GetXPath(systemDefinedSubmoduleList);
            IXmlLineInfo xli = systemDefinedSubmoduleList;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012204_3");

        }

        private void CreateReport0x00012204_1(XNode fieldbusIntegrationModule)
        {
            string xp = "./gsddef:VirtualSubmoduleList/gsddef:VirtualSubmoduleItem/gsddef:SlotCluster";
            var slotClusterAtVsi = fieldbusIntegrationModule.XPathSelectElement(xp, Nsmgr);
            if (slotClusterAtVsi == null)
            {
                return;
            }
            if (!Help.CheckSchemaVersion(slotClusterAtVsi, SupportedGsdmlVersion))
            {
                return;
            }
            // "If the attribute 'ModuleItem/@FieldbusType' is present, 'VirtualSubmoduleItem/SlotCluster' must not be given."
            string msg = Help.GetMessageString("M_0x00012204_1");
            string xpath = Help.GetXPath(slotClusterAtVsi);
            IXmlLineInfo xli = slotClusterAtVsi;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012204_1");

        }

        /// <summary>
        /// Check number: CN_0x00012205
        /// Check for attribute DeviceAccessPointItem/@SharedInputSupported in dependence to DeviceAccessPointItem/@SharedDeviceSupported.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012205()
        {
            // Find all Daps with SharedInputSupported = "true"
            var daps = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_DeviceAccessPointItem);
            daps = Help.TryRemoveXElementsUnderXsAny(daps, Nsmgr, Gsd);
            foreach (var dap in daps)
            {
                string strSharedInputSupported =
                    Help.GetAttributeValueFromXElement(dap, Attributes.s_SharedInputSupported);
                bool bSharedInputSupported = false;
                if (!string.IsNullOrEmpty(strSharedInputSupported))
                    bSharedInputSupported = XmlConvert.ToBoolean(strSharedInputSupported);
                if (!bSharedInputSupported)
                {
                    continue;
                }
                // Check the attribute SharedDeviceSupported
                string strSharedDeviceSupported =
                    Help.GetAttributeValueFromXElement(dap, Attributes.s_SharedDeviceSupported);
                bool bSharedDeviceSupported = false;
                if (!string.IsNullOrEmpty(strSharedDeviceSupported))
                    bSharedDeviceSupported = XmlConvert.ToBoolean(strSharedDeviceSupported);
                if (bSharedDeviceSupported)
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
                {
                    continue;
                }
                // "If the attribute 'DeviceAccessPointItem/@SharedInputSupported' is present and "true",
                //  'DeviceAccessPointItem/@SharedDeviceSupported' must be present and "true"."
                string msg = Help.GetMessageString("M_0x00012205_1");
                string xpath = Help.GetXPath(dap);
                IXmlLineInfo xli = dap;
                Store.CreateAndAnnounceReport(ReportType_0X00012205, xli.LineNumber, xli.LinePosition, msg,
                    xpath,
                    ReportCategories.TypeSpecific, "0x00012205_1");
            }



            return true;
        }

        /// <summary>
        /// Check number: CN_0x00012206
        /// Checks for Element DeviceAccessPointItem/FieldbusIntegrationSlots:
        /// 
        /// 1. The element FieldbusIntegrationSlots must be present if a FAP (Fieldbus Access Point) which has the element
        ///    SlotCluster can be used with this DAP. A FAP is a (Virtual)SubmoduleItem, which has the API 17920.
        ///     
        /// 2. The area reserved for SlotCluster must be big enough to use each of the FAPs (individually, not all FAPs at once).
        ///    I.e. the attribute FieldbusIntegrationSlots/@MaxSupported must be >= SlotCluster/@Count of each FAP tha
        ///    t can be used with this DAP. Otherwise warning (because FAP can't be used).
        ///    
        /// 3. FieldbusIntegrationSlots/@Range must be an actual range
        ///    I.e. the number in front of ".." must be smaller or equal to the number after it. Else error.
        ///    
        /// 4. The FieldbusIntegrationSlots must be in the range 0..32767 and may not overlap with the PhysicalSlots.
        ///    I.e. FieldbusIntegrationSlots/@Range must be in the range 0..32767 and may not be smaller
        ///    than the highest value in PhysicalSlots at the DAP. Else error.
        ///    
        /// 5. The range must be big enough
        ///    The range is either given in the attribute Range, or the default for this attribute, which is
        ///    PhysicalSlots plus 1 .. 32767. (Attention: the highest value in PhysicalSlots could legally
        ///    be 32767 (or even more in case of error), then the range is invalid!).
        ///    I.e. better calculate the width of the range instead of the actual range, because the width
        ///    may be 0 in case of error. This width must be >= MaxSupported, else error.
        ///    
        /// 6. Checks for FBI modules (FieldbusIntegration modules):
        ///    FBI module are those which have the attribute FieldbusType.
        ///    For each FBI module a corresponding FAP must exist. I.e. for each value of FieldbusType
        ///    there must exist a FAP with this FieldbusType, else warning (because FBI module can't be used).
        ///    (This can only be checked if SlotCluster are supported,
        ///    i.e. the FAP has the element SlotCluster with the attribute FieldbusType.)
        ///    
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012206()
        {
            bool fapWithoutSlotClusterFound = false;
            SortedList<UInt16, UInt16> usedFieldbusTypes = new SortedList<UInt16, UInt16>();
            string xp = ".//gsddef:DeviceAccessPointItem";
            var nl = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl = Help.TryRemoveXElementsUnderXsAny(nl, Nsmgr, Gsd);
            foreach (var dap in nl)
            {
                if (CheckCN_0x00012206_CheckSingleDap(dap, usedFieldbusTypes, out fapWithoutSlotClusterFound))
                {
                    continue;
                }

                break;
            }

            // (6)
            CreateReport0x00012206_7(fapWithoutSlotClusterFound, usedFieldbusTypes);

            return true;

        }

        private bool CheckCN_0x00012206_CheckSingleDap(
            XElement dap,
            SortedList<ushort, ushort> usedFieldbusTypes,
            out bool fapWithoutSlotClusterFound)
        {
            var fieldbusIntegrationSlots = dap.XPathSelectElement("./gsddef:FieldbusIntegrationSlots", Nsmgr);
            UInt16 maxSupported = 0;
            if (fieldbusIntegrationSlots != null)
                maxSupported = XmlConvert.ToUInt16(
                    Help.GetAttributeValueFromXElement(
                        fieldbusIntegrationSlots,
                        Attributes.s_MaxSupported)); // required attribute

            if (CanFapBeOperatedAtDAP(dap, out UInt16 maxCount, out fapWithoutSlotClusterFound, usedFieldbusTypes))
            {
                // (1)
                if (fieldbusIntegrationSlots == null)
                {
                    CreateReport0x00012206_1(dap);
                }
                else
                {
                    // (2)
                    CreateReport0x00012206_2(maxCount, maxSupported, dap, fieldbusIntegrationSlots);
                }
            }

            if (fieldbusIntegrationSlots == null)
                return true;

            // (3)
            string range = Help.GetAttributeValueFromXElement(fieldbusIntegrationSlots, Attributes.s_Range);
            uint rangeFrom, rangeTo = 0, rangeWidth;
            List<ValueListHelper.ValueRangeT> physicalSlots =
                ValueListHelper.NormalizeValueList(dap.Attribute(Attributes.s_PhysicalSlots), Store);
            if (!string.IsNullOrEmpty(range))
            {
                // Range
                if (CreateReport0x00012206_3(range, dap, fieldbusIntegrationSlots, out rangeFrom, ref rangeTo))
                {
                    return true;
                }

                rangeWidth = rangeTo - rangeFrom + 1;

                // (4)
                CreateReport0x00012206_4(rangeTo, dap, fieldbusIntegrationSlots);
                if (CreateReport0x00012206_5(physicalSlots, rangeFrom, dap, fieldbusIntegrationSlots))
                {
                    return false;
                }
            }
            else
            {
                // Default value
                rangeWidth = maxSupported;
                if (physicalSlots.Count > 0)
                {
                    rangeFrom = physicalSlots[physicalSlots.Count - 1].To + 1;
                    rangeTo = 32767;
                    if (rangeFrom <= 32767)
                    {
                        rangeWidth = rangeTo - rangeFrom + 1;
                    }
                }
            }

            // (5)
            if (CreateReport0x00012206_6(rangeWidth, maxSupported, dap, fieldbusIntegrationSlots))
            {
                return false;
            }

            return false;
        }

        private void CreateReport0x00012206_7(bool fapWithoutSlotClusterFound, IReadOnlyDictionary<ushort, ushort> usedFieldbusTypes)
        {
            if (fapWithoutSlotClusterFound)
            {
                return;
            }

            var nlFieldbusType = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_ModuleItem)
                .Attributes(Attributes.s_FieldbusType);
            nlFieldbusType = Help.TryRemoveXAttributesUnderXsAny(nlFieldbusType, Nsmgr, Gsd);
            foreach (XAttribute fieldbusType in nlFieldbusType)
            {
                if (usedFieldbusTypes.ContainsKey(XmlConvert.ToUInt16(fieldbusType.Value)))
                {
                    continue;
                }

                if (!Help.CheckSchemaVersion(fieldbusType, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "For FBI module with field bus type (= {0}) no matching FAP is available."
                string msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_0x00012206_7"), fieldbusType.Value);
                string xpath = Help.GetXPath(fieldbusType);
                var xli = (IXmlLineInfo)fieldbusType;
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Warning,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00012206_7");
            }
        }

        private bool CreateReport0x00012206_6(
            uint rangeWidth,
            ushort maxSupported,
            XObject dap,
            XElement fieldbusIntegrationSlots)
        {
            if (rangeWidth >= maxSupported)
            {
                return false;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return false;
            }

            // "The width of 'FieldbusIntegrationSlots/@Range' is lower than 'FieldbusIntegrationSlots/@MaxSupported'."
            string msg = Help.GetMessageString("M_0x00012206_6");
            string xpath = Help.GetXPath(fieldbusIntegrationSlots);
            var xli = (IXmlLineInfo)fieldbusIntegrationSlots;
            var rangeNode = fieldbusIntegrationSlots.Attribute(Attributes.s_Range);
            if (rangeNode != null)
            {
                xpath = Help.GetXPath(rangeNode);
                xli = rangeNode;
            }

            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012206_6");
            return true;

        }

        private bool CreateReport0x00012206_5(
            IReadOnlyList<ValueListHelper.ValueRangeT> physicalSlots,
            uint rangeFrom,
            XObject dap,
            XElement fieldbusIntegrationSlots)
        {
            if (physicalSlots.Count <= 0
                || physicalSlots[physicalSlots.Count - 1].To < rangeFrom)
            {
                return false;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return false;
            }

            // "Values of attribute 'FieldbusIntegrationSlots/@Range' overlap with 'DeviceAccessPointItem/@PhysicalSlots'."
            string msg = Help.GetMessageString("M_0x00012206_5");
            string xpath = Help.GetXPath(fieldbusIntegrationSlots.Attribute(Attributes.s_Range));
            IXmlLineInfo xli = fieldbusIntegrationSlots.Attribute(Attributes.s_Range);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00012206_5");
            }

            return true;

        }

        private void CreateReport0x00012206_4(uint rangeTo, XObject dap, XElement fieldbusIntegrationSlots)
        {
            if (rangeTo <= 32767)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return;
            }

            // "The attribute 'FieldbusIntegrationSlots/@Range' must be in the range 0..32767."
            string msg = Help.GetMessageString("M_0x00012206_4");
            string xpath = Help.GetXPath(fieldbusIntegrationSlots.Attribute(Attributes.s_Range));
            IXmlLineInfo xli = fieldbusIntegrationSlots.Attribute(Attributes.s_Range);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00012206_4");
            }
        }

        private bool CreateReport0x00012206_3(
            string range,
            XObject dap,
            XElement fieldbusIntegrationSlots,
            out uint rangeFrom,
            ref uint rangeTo)
        {
            string[] values = range.Split(new string[] { ".." }, StringSplitOptions.None);
            if (!uint.TryParse(values[0], NumberStyles.Any, CultureInfo.InvariantCulture, out rangeFrom)
                || !uint.TryParse(values[1], NumberStyles.Any, CultureInfo.InvariantCulture, out rangeTo))
            {
                return false;
            }

            if (rangeFrom <= rangeTo)
            {
                return false;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return false;
            }

            // "The attribute 'FieldbusIntegrationSlots/@Range' must be a correct range x..y with x<y."
            string msg = Help.GetMessageString("M_0x00012206_3");
            string xpath = Help.GetXPath(fieldbusIntegrationSlots.Attribute(Attributes.s_Range));
            IXmlLineInfo xli = fieldbusIntegrationSlots.Attribute(Attributes.s_Range);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    GSDI.ReportTypes.GSD_RT_Error,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00012206_3");
            }

            return true;

        }

        private void CreateReport0x00012206_2(
            ushort maxCount,
            ushort maxSupported,
            XObject dap,
            XElement fieldbusIntegrationSlots)
        {
            if (maxCount <= maxSupported)
            {
                return;
            }

            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return;
            }

            // "The attribute 'FieldbusIntegrationSlots/@MaxSupported' must be greater or equal 'SlotCluster/@Count' of every operable FAP."
            string msg = Help.GetMessageString("M_0x00012206_2");
            string xpath = Help.GetXPath(fieldbusIntegrationSlots.Attribute(Attributes.s_MaxSupported));
            var xli = (IXmlLineInfo)fieldbusIntegrationSlots.Attribute(Attributes.s_MaxSupported);
            if (xli != null)
            {
                Store.CreateAndAnnounceReport(
                    ReportType_0X000122062,
                    xli.LineNumber,
                    xli.LinePosition,
                    msg,
                    xpath,
                    ReportCategories.TypeSpecific,
                    "0x00012206_2");
            }
        }

        private void CreateReport0x00012206_1(XObject dap)
        {
            if (!Help.CheckSchemaVersion(dap, SupportedGsdmlVersion))
            {
                return;
            }

            // "When a FAP using slot cluster can be operated with the DAP, the element 'DeviceAccessPointItem/FieldbusIntegrationSlots' must be given."
            string msg = Help.GetMessageString("M_0x00012206_1");
            string xpath = Help.GetXPath(dap);
            IXmlLineInfo xli = dap;
            Store.CreateAndAnnounceReport(
                GSDI.ReportTypes.GSD_RT_Error,
                xli.LineNumber,
                xli.LinePosition,
                msg,
                xpath,
                ReportCategories.TypeSpecific,
                "0x00012206_1");
        }

        /// <summary>
        /// Check number: CN_0x00012207
        /// The element SlotCluster may only appear on a FAP (detected by API = 17920), but not on other submodules.
        ///    
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X00012207()
        {
            string xp = "(.//gsddef:VirtualSubmoduleItem | .//gsddef:SubmoduleItem)[not(@API=17920) and " +
                        "./gsddef:SlotCluster]";
            var nl1 = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            nl1 = Help.TryRemoveXElementsUnderXsAny(nl1, Nsmgr, Gsd);
            foreach (var submoduleItem1 in nl1)
            {
                if (Help.CheckSchemaVersion(submoduleItem1, SupportedGsdmlVersion))
                {
                    // "The element 'SlotCluster' may only appear on a FAP (detected by API = 17920), but not on other submodules
                    string msg = Help.GetMessageString("M_0x00012207_1");
                    string xpath = Help.GetXPath(submoduleItem1);
                    IXmlLineInfo xli = submoduleItem1;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_MinorError, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00012207_1");
                }
            }

            return true;
        }

        /// <summary>
        /// Check for attribute SupportedSubstitutionModes if (Virtual)SubmoduleItem/IOData/Output is defined.
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00012208()
        {
            var nl =
                GsdProfileBody.Descendants().Attributes(Attributes.s_SupportedSubstitutionModes)
                    .Where(
                        x =>
                            x.Parent != null && (x.Parent.Name.LocalName == Elements.s_VirtualSubmoduleItem ||
                                                 x.Parent.Name.LocalName == Elements.s_SubmoduleItem));
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                // Call Help.NormalizeValueList only to test if the values are ok
                ValueListHelper.NormalizeValueList(an, Store);

                XElement submoduleItem = an.Parent;

                if (submoduleItem == null)
                {
                    continue;
                }

                var output = submoduleItem.Descendants(NamespaceGsdDef + Elements.s_Output).FirstOrDefault();
                if (output != null)
                {
                    continue;
                }
                if (!Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                {
                    continue;
                }

                // "The attribute 'SupportedSubstitutionModes' must only be present if the '(Virtual)SubmoduleItem/IOData/Output' is given."
                string msg = Help.GetMessageString("M_0x00012208_1");
                string xpath = Help.GetXPath(an);
                IXmlLineInfo xli = an;
                Store.CreateAndAnnounceReport(ReportType_0X00012208, xli.LineNumber, xli.LinePosition, msg, xpath,
                                              ReportCategories.TypeSpecific, "0x00012208_1");


            }

            return true;
        }

        /// <summary>
        /// The attribute ParameterizationDisallowed must have the same boolean value on the InterfaceSubmoduleItem
        /// than on all PortSubmoduleItems which can be configured with this Interface (i.e. DAP).
        /// 
        /// </summary>
        protected virtual bool CheckCn_0X00012209()
        {
            string xp = ".//gsddef:DeviceAccessPointItem";
            var daPs = GsdProfileBody.XPathSelectElements(xp, Nsmgr);
            daPs = Help.TryRemoveXElementsUnderXsAny(daPs, Nsmgr, Gsd);
            foreach (var dap in daPs)
            {
                bool parameterizationDisallowedDap = false;
                var interfaceSubmoduleItem = dap.XPathSelectElement("./gsddef:SystemDefinedSubmoduleList/gsddef:InterfaceSubmoduleItem", Nsmgr);

                if (interfaceSubmoduleItem == null)
                    continue;

                string strParameterizationDisallowedDap = Help.GetAttributeValueFromXElement(interfaceSubmoduleItem, Attributes.s_ParameterizationDisallowed);
                if (!string.IsNullOrEmpty(strParameterizationDisallowedDap))
                    parameterizationDisallowedDap = XmlConvert.ToBoolean(strParameterizationDisallowedDap);

                if (!DapToPortDictionary.TryGetValue(dap, out IList<XElement> portSubmoduleItems))
                {
                    continue;
                }
                foreach (var portSubmoduleItem in portSubmoduleItems)
                {
                    bool parameterizationDisallowedPort = false;
                    string strParameterizationDisallowedPort = Help.GetAttributeValueFromXElement(portSubmoduleItem, Attributes.s_ParameterizationDisallowed);
                    if (!string.IsNullOrEmpty(strParameterizationDisallowedPort))
                        parameterizationDisallowedPort = XmlConvert.ToBoolean(strParameterizationDisallowedPort);

                    if (parameterizationDisallowedDap == parameterizationDisallowedPort)
                    {
                        continue;
                    }
                    if (!Help.CheckSchemaVersion(portSubmoduleItem, SupportedGsdmlVersion))
                    {
                        continue;
                    }
                    // "The attribute ParameterizationDisallowed on DAP has not the same value as on PortSubmoduleItem which can be configured with this DAP."
                    string msg = Help.GetMessageString("M_0x00012209_1");
                    string xpath = Help.GetXPath(portSubmoduleItem);
                    var lineInfo = (IXmlLineInfo)portSubmoduleItem;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x00012209_1");
                }


            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001220A
        /// Check that @FieldbusType = "0" is not used, because the value "0" corresponds
        /// to API "0x4600" which is reserved for the FAP.".
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001220A()
        {
            var nl = GsdProfileBody.Descendants().Attributes(Attributes.s_FieldbusType);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                UInt16 fieldbusType = XmlConvert.ToUInt16(an.Value);
                if (fieldbusType == 0)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "@FieldbusType = "0" is not allowed, because the value "0" corresponds
                        // to API "0x4600" which is reserved for the FAP."
                        string msg = Help.GetMessageString("M_0x0001220A_1");
                        string xpath = Help.GetXPath(an);
                        var lineInfo = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                      msg, xpath, ReportCategories.TypeSpecific, "0x0001220A_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001220B
        /// Check that SlotCluster/@Count' is not "0", because the value "0" would
        /// mean, that no Slots are required for the FAP.
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCn_0X0001220B()
        {
            var nl = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_SlotCluster).Attributes(Attributes.s_Count);
            nl = Help.TryRemoveXAttributesUnderXsAny(nl, Nsmgr, Gsd);
            foreach (XAttribute an in nl)
            {
                UInt16 slotClusterCount = XmlConvert.ToUInt16(an.Value);
                if (slotClusterCount == 0)
                {
                    if (Help.CheckSchemaVersion(an, SupportedGsdmlVersion))
                    {
                        // "'SlotCluster/@Count' = "0" is not allowed, because the value "0" would
                        // mean, that no Slots are required for the FAP."
                        string msg = Help.GetMessageString("M_0x0001220B_1");
                        string xpath = Help.GetXPath(an);
                        var lineInfo = (IXmlLineInfo)an;
                        Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition,
                                                      msg, xpath, ReportCategories.TypeSpecific, "0x0001220B_1");
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Check number: CN_0x0001220C
        /// 
        /// Verify the existence of MaxDataLength with more than one AR and/or more than a single input/output CR:
        /// 
        /// Issue a warning if MaxDataLength is missing and
        /// - the attribute NumberOfAR is present and >1 (this includes the cases SystemRedundancy, SharedDevice and/or Supervisor) and/or
        /// - the attribute NumberOfAdditionalInputCR is present and >0 and/or
        /// - the attribute NumberOfAdditionalOutputCR is present and >0
        /// 
        /// </summary>
        /// <returns>True, if no runtime problem occurred.</returns>
        protected virtual bool CheckCN_0x0001220C()
        {
            // Find all IOConfigData elements
            var ioConfigDataList = GsdProfileBody.Descendants(NamespaceGsdDef + Elements.s_IoConfigData);
            ioConfigDataList = Help.TryRemoveXElementsUnderXsAny(ioConfigDataList, Nsmgr, Gsd);
            foreach (var ioConfigData in ioConfigDataList)
            {
                string strMaxDataLength = Help.GetAttributeValueFromXElement(ioConfigData, Attributes.s_MaxDataLength);

                if (!string.IsNullOrEmpty(strMaxDataLength))
                    continue;

                var dap = ioConfigData.Parent;
                if (dap == null)
                {
                    continue;
                }
                var systemDefinedSubmoduleList = dap.Element(NamespaceGsdDef + Elements.s_SystemDefinedSubmoduleList);
                if (systemDefinedSubmoduleList == null)
                    continue;
                var applicationRelations = systemDefinedSubmoduleList.Element(NamespaceGsdDef + Elements.s_InterfaceSubmoduleItem)
                    .Element(NamespaceGsdDef + Elements.s_ApplicationRelations);
                if (applicationRelations == null)
                    // The default values take effect -> no warning
                    continue;

                XAttribute numberOfAR =
                    applicationRelations.Attributes(Attributes.s_NumberOfAr)
                        .FirstOrDefault();
                UInt16 noOfAR = 1;
                if (numberOfAR != null)
                    noOfAR = XmlConvert.ToUInt16(numberOfAR.Value);

                XAttribute numberOfAdditionalInputCR =
                    applicationRelations.Attributes(Attributes.s_NumberOfAdditionalInputCr)
                        .FirstOrDefault();
                UInt16 noOfAdditionalInputCR = 0;
                if (numberOfAdditionalInputCR != null)
                    noOfAdditionalInputCR = XmlConvert.ToUInt16(numberOfAdditionalInputCR.Value);

                XAttribute numberOfAdditionalOutputCR =
                    applicationRelations.Attributes(Attributes.s_NumberOfAdditionalOutputCr)
                        .FirstOrDefault();
                UInt16 noOfAdditionalOutputCR = 0;
                if (numberOfAdditionalOutputCR != null)
                    noOfAdditionalOutputCR = XmlConvert.ToUInt16(numberOfAdditionalOutputCR.Value);

                if (noOfAR > 1 || noOfAdditionalInputCR > 0 || noOfAdditionalOutputCR > 0)
                {
                    // "The attribute MaxDataLength should be given, because using the default value is only good for
                    //  one AR with a single input/output CR, but this device may be configured using more CRs/ARs."
                    string msg = Help.GetMessageString("M_0x0001220C_1");
                    string xpath = Help.GetXPath(ioConfigData);
                    IXmlLineInfo xli = ioConfigData;
                    Store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Warning, xli.LineNumber, xli.LinePosition, msg, xpath,
                                                  ReportCategories.TypeSpecific, "0x0001220C_1");
                }
            }

            return true;
        }

        #endregion
    }
}


