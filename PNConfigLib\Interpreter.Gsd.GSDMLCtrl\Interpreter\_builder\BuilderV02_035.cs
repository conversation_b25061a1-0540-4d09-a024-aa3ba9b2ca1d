/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_035.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections;
using System.Globalization;
using System.Xml;
using System.Xml.XPath;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02035 :
        BuilderV02034
    {

        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02035()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version235);
        }

        #endregion


        //########################################################################################
        #region Methods

        protected override bool InitExpressions()
        {
            bool succeeded = true;

            try
            {
                succeeded = base.InitExpressions();
                if (!succeeded)
                {
                    return false;
                }

                // Create document navigator.
                XPathNavigator nav = Gsd.CreateNavigator();

                if (nav == null)
                {
                    return false;
                }

                // Create the NamespaceManager and add all XML Namespaces to it.
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                // ChannelProcessAlarmItem
                XPathExpression expr = nav.Compile(XPathes.AllChannelProcessAlarmItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_ChannelProcessAlarmItem, expr);

                // SystemDefinedChannelProcessAlarmItem
                expr = nav.Compile(XPathes.AllSystemDefinedChannelProcessAlarmItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_SystemDefinedChannelProcessAlarmItem, expr);

                // ProfileChannelProcessAlarmItem
                expr = nav.Compile(XPathes.AllProfileChannelProcessAlarmItems);
                expr.SetContext(nsmgr);
                this.Expressions.Add(Elements.s_ProfileChannelProcessAlarmItem, expr);
            }
            catch (XPathException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectChannelProcessAlarm:
                        {
                            // NOTE: Navigator must point to ChannelProcessAlarmItem.
                            this.PrepareChannelProcessAlarm(nav, ref hash);
                            obj = new C.ChannelProcessAlarm();

                            break;
                        }
                    case Models.s_ObjectSystemDefinedChannelProcessAlarm:
                        {
                            // NOTE: Navigator must point to SystemDefinedChannelProcessAlarmItem.
                            this.PrepareSystemDefinedChannelProcessAlarm(nav, ref hash);
                            obj = new C.SystemDefinedChannelProcessAlarm();

                            break;
                        }
                    case Models.s_ObjectProfileChannelProcessAlarm:
                        {
                            // NOTE: Navigator must point to ProfileChannelProcessAlarmItem.
                            this.PrepareProfileChannelProcessAlarm(nav, ref hash);
                            obj = new C.ProfileChannelProcessAlarm();

                            break;
                        }
                    case Models.s_ObjectExtChannelProcessAlarm:
                        {
                            // NOTE: Navigator must point to ExtChannelProcessAlarmItem.
                            this.PrepareExtChannelProcessAlarm(nav, ref hash);
                            obj = new C.ChannelProcessAlarm();

                            break;
                        }
                    case Models.s_ObjectProfileExtChannelProcessAlarm:
                        {
                            // NOTE: Navigator must point to ProfileExtChannelProcessAlarmItem.
                            this.PrepareProfileExtChannelProcessAlarm(nav, ref hash);
                            obj = new C.ProfileChannelProcessAlarm();

                            break;
                        }
                    case Models.s_ObjectInterconnection:
                        // NOTE: Navigator must point to Interconnection.
                        this.PrepareInterconnection(nav, ref hash);
                        obj = new C.Interconnection();

                        break;
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion


        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldNumberOfImplicitAr, null);

            base.PrepareDeviceAccessPoint(nav, ref hash);

            // Attribute NumberOfImplicitAR, optional
            hash[Models.s_FieldNumberOfImplicitAr] = 1;
            string attr = nav.GetAttribute(Attributes.s_NumberOfImplicitAr, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32 value = 0;
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldNumberOfImplicitAr] = value;
            }
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSupportsMrpInterconnPortConfig, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_SupportsMrpInterconnPortConfig, String.Empty);
            hash[Models.s_FieldSupportsMrpInterconnPortConfig] = Help.GetBool(attr, Attributes.s_DefaultSupportsMrpInterconnPortConfig);
        }

        protected override void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldInterconnection, null);

            // Call base class method first.
            base.PrepareMediaRedundancy(nav, ref hash);


            // Navigate to Interconnection. Optional.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Interconnection, Namespaces.s_GsdmlDeviceProfile);
            // Create element.
            if (nodes.MoveNext())
            {
                object obj = CreateGsdObject(Models.s_ObjectInterconnection, nodes.Current);

                if (null == obj)
                    throw new PreparationException("Couldn't create Interconnection!");

                // Set hash variable.
                hash[Models.s_FieldInterconnection] = obj;
            }
        }

        protected virtual void PrepareInterconnection(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldSupportedMrpInterconnRole, null);
            hash.Add(Models.s_FieldMaxMrpInterconnInstances, null);


            // Get SupportedMRP_InterconnRole attribute. Optional.
            string attr = nav.GetAttribute(Attributes.s_SupportedMrpInterconnRole, String.Empty);
            ArrayList listMRRoles = new ArrayList();
            if (!String.IsNullOrEmpty(attr))
            {
                ArrayList attrList = Help.SeparateTokenList(attr);
                foreach (string role in attrList)
                {
                    if (Enums.IsMRSupportedRolesEnumValueConvertable(role))
                        listMRRoles.Add(Enums.ConvertMRSupportedRolesEnum(role));
                }
                hash[Models.s_FieldSupportedMrpInterconnRole] = listMRRoles;
            }

            // Get MaxMRP_InterconnInstances attribute. Optional.
            attr = nav.GetAttribute(Attributes.s_MaxMrpInterconnInstances, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxMrpInterconnInstances] = XmlConvert.ToUInt32(attr);
            }
        }


        protected virtual void PrepareChannelProcessAlarm(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelProcessAlarm data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldReason, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldExtChannelProcessAlarms, null);

            // Get reason attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Reason, String.Empty);
            hash[Models.s_FieldReason] = System.Xml.XmlConvert.ToUInt32(attr);

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = this.GetText(nodes.Current);
            if (nodes.Current == null)
            {
                return;
            }
            hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = this.GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Navigate to ExtChannelProcessAlarmItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ExtChannelProcessAlarmItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = this.CreateGsdObject(Models.s_ObjectExtChannelProcessAlarm, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtChannelProcessAlarm + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtChannelProcessAlarms] = list;
        }

        protected virtual void PrepareSystemDefinedChannelProcessAlarm(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare SystemDefinedChannelProcessAlarm data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldReason, null);
            hash.Add(Models.s_FieldExtChannelProcessAlarms, null);
            hash.Add(Models.s_FieldProfileExtChannelProcessAlarms, null);

            // Get reason attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Reason, String.Empty);
            hash[Models.s_FieldReason] = System.Xml.XmlConvert.ToUInt32(attr);

            // Navigate to ExtChannelProcessAlarmItem elements and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ExtChannelProcessAlarmItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectExtChannelProcessAlarm, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtChannelProcessAlarm + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtChannelProcessAlarms] = list;

            // Navigate to ProfileExtChannelProcessAlarmItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ProfileExtChannelProcessAlarmItem, Namespaces.s_GsdmlDeviceProfile, false);
            list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectProfileExtChannelProcessAlarm, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileExtChannelProcessAlarm + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfileExtChannelProcessAlarms] = list;
        }

        protected virtual void PrepareProfileChannelProcessAlarm(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ProfileChannelProcessAlarm data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApi, null);
            hash.Add(Models.s_FieldReason, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldExtChannelProcessAlarms, null);
            hash.Add(Models.s_FieldProfileExtChannelProcessAlarms, null);

            // Get error API attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Api, String.Empty);
            UInt32 value = 0;
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldApi] = value;
            }

            // Get reason attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Reason, String.Empty);
            hash[Models.s_FieldReason] = System.Xml.XmlConvert.ToUInt32(attr);

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = this.GetText(nodes.Current);
            if (nodes.Current == null)
            {
                return;
            }
            hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = this.GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Navigate to ExtChannelProcessAlarmItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ExtChannelProcessAlarmItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectExtChannelProcessAlarm, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtChannelProcessAlarm + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtChannelProcessAlarms] = list;

            // Navigate to ProfileExtChannelProcessAlarmItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ProfileExtChannelProcessAlarmItem, Namespaces.s_GsdmlDeviceProfile, false);
            list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectProfileExtChannelProcessAlarm, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileExtChannelProcessAlarm + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfileExtChannelProcessAlarms] = list;
        }

        protected virtual void PrepareExtChannelProcessAlarm(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ExtChannelProcessAlarm data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldReason, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldExtChanProcAlReasonAddValues, null);

            // Get reason attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Reason, String.Empty);
            hash[Models.s_FieldReason] = System.Xml.XmlConvert.ToUInt32(attr);

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldName] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Navigate to ProcessAlarmReasonAddValueDataItem elements and create them. Optional.
            nodes = nav.SelectDescendants(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = this.CreateGsdObject(Models.s_ObjectAddValueDataItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAddValueDataItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtChanProcAlReasonAddValues] = list;
        }

        protected virtual void PrepareProfileExtChannelProcessAlarm(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ProfileExtChannelProcessAlarm data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldApi, null);
            hash.Add(Models.s_FieldReason, null);
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);
            hash.Add(Models.s_FieldProfExtChanProcAlReasonAddValues, null);

            // Get error API attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_Api, String.Empty);
            UInt32 value = 0;
            if (!String.IsNullOrEmpty(attr))
            {
                UInt32.TryParse(attr, NumberStyles.Any, CultureInfo.InvariantCulture, out value);
                hash[Models.s_FieldApi] = value;
            }

            // Get reason attribute. Required.
            attr = nav.GetAttribute(Attributes.s_Reason, String.Empty);
            hash[Models.s_FieldReason] = System.Xml.XmlConvert.ToUInt32(attr);

            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldName] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                hash[Models.s_FieldHelp] = GetText(nodes.Current);
                if (nodes.Current != null)
                {
                    hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
                }
            }

            // Navigate to ProcessAlarmReasonAddValueDataItems elements and create them. Optional.
            nodes = nav.SelectDescendants(Elements.s_DataItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = this.CreateGsdObject(Models.s_ObjectAddValueDataItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectAddValueDataItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfExtChanProcAlReasonAddValues] = list;
        }

        protected override void PrepareDevice(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Device data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldChannelProcessAlarms, null);
            hash.Add(Models.s_FieldSystemDefinedChannelProcessAlarms, null);
            hash.Add(Models.s_FieldProfileChannelProcessAlarms, null);

            // Call base class method first.
            base.PrepareDevice(nav, ref hash);

            // Navigate to channel process alarms and create it. Optional.
            object obj = null;
            ArrayList list = null;
            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_ChannelProcessAlarmItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectChannelProcessAlarm, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectChannelProcessAlarm + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldChannelProcessAlarms] = list;

            // Navigate to system defined channel process alarms and create it. Optional.
            list = null;
            nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_SystemDefinedChannelProcessAlarmItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectSystemDefinedChannelProcessAlarm, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSystemDefinedChannelProcessAlarm + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldSystemDefinedChannelProcessAlarms] = list;

            // Navigate to profile channel process alarms and create it. Optional.
            list = null;
            nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_ProfileChannelProcessAlarmItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectProfileChannelProcessAlarm, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileChannelProcessAlarm + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldProfileChannelProcessAlarms] = list;
        }

        #endregion

        #endregion

    }
}
