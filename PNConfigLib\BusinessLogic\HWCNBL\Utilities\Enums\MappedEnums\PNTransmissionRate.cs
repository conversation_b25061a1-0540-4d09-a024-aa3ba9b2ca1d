/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNTransmissionRate.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/


namespace PNConfigLib.HWCNBL.Utilities.Enums
{
    public enum PNTransmissionRate
    {
        TP10MbpsHalfDuplex = 10,
        TP10MbpsFullDuplex = 11,
        AsyncFiber10MbpsHalfDuplex = 12,
        AsyncFiber10MbpsFullDuplex = 13,
        TP100MbpsHalfDuplex = 15,
        TP100MbpsFullDuplex = 16,
        POFPCF100MbpsFullDuplex = 54,
        FO100MbpsFullDuplex = 18,
        X1000MbpsFullDuplex = 22,
        FO1000MbpsFullDuplex = 26,
        FO1000MbpsFullDuplexLD = 24,
        TP1000MbpsFullDuplex = 30,
        FO10000MbpsFullDuplex = 31,
        FO100MbpsFullDuplexLD = 46,
        Automatic = 8
    }
}
