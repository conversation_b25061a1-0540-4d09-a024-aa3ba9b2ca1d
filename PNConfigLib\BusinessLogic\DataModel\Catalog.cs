/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Catalog.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Globalization;
using PNConfigLib.BusinessLogic.HWCNBL.Utilities;
using PNConfigLib.CentralDeviceImport;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel.PCLCatalogObjects;

#endregion

namespace PNConfigLib.DataModel
{
    /// <summary>
    /// The main Catalog that contains the hardware types (devices, interfaces, modules, ports and submodules)
    /// that can be used within the project.
    /// </summary>
    /// <remarks>
    /// These hardware items are imported from the GSDML files specified in the ListOfNodes file of the project.
    /// CentralDeviceCatalog objects are not imported; these PNDriver catalogs are hard-coded.
    /// </remarks>
    public static class Catalog
    {
        /// <summary>
        /// The catalog objects for decentral devices.
        /// </summary>
        public static Dictionary<string, DecentralDeviceCatalog> DeviceList { get; set; } =
            new Dictionary<string, DecentralDeviceCatalog>();

        /// <summary>
        /// The list containing the paths of GSDML files imported to the project.
        /// </summary>
        public static List<string> ImportedGSDMLList { get; set; } = new List<string>();

        /// <summary>
        /// The catalog objects for interfaces.
        /// </summary>
        /// <remarks>
        /// InterfaceCatalog objects are also stored in their corresponding device catalog
        /// objects and can be used from there.
        /// </remarks>
        public static Dictionary<string, InterfaceCatalog> InterfaceList { get; set; } =
            new Dictionary<string, InterfaceCatalog>();

        /// <summary>
        /// The catalog objects for modules.
        /// </summary>
        public static Dictionary<string, ModuleCatalog> ModuleList { get; set; } =
            new Dictionary<string, ModuleCatalog>();

        ///// <summary>
        ///// Gets the catalog objects for PNDriver variants.
        ///// </summary>
        public static CentralDeviceCatalog GetCentralDeviceCatalog(
            PNDriverInterfaceEnum interfaceType,
            string customInterfacePath,
            string version,
            string listOfNodesPath)
        {
            string catalogKey = string.Format(
                CultureInfo.InvariantCulture,
                "{0}_{1}_{2}",
                interfaceType,
                version,
                customInterfacePath);

            if (customInterfacePath != null)
            {
                customInterfacePath = FileOperations.GetFullDirectoryPath(customInterfacePath, listOfNodesPath);
            }

            if (!s_CentralDeviceCatalog.ContainsKey(catalogKey))
            {
                CentralDeviceCatalogObjectReader centralDeviceCatalogReader = new CentralDeviceCatalogObjectReader();
                CentralDeviceCatalog catalog =
                    centralDeviceCatalogReader.FillCentralDeviceCatalog(interfaceType, customInterfacePath, version);
                s_CentralDeviceCatalog.Add(catalogKey, catalog);
            }
            return s_CentralDeviceCatalog[catalogKey];
        }

        private static Dictionary<string, CentralDeviceCatalog> s_CentralDeviceCatalog =
            new Dictionary<string, CentralDeviceCatalog>();

        /// <summary>
        /// The catalog objects for ports.
        /// </summary>
        /// <remarks>
        /// PortCatalog objects are also stored in ther corresponding interface or module
        /// catalog objects and can be used from there.
        /// </remarks>
        public static Dictionary<string, PortCatalog> PortList { get; set; } = new Dictionary<string, PortCatalog>();

        /// <summary>
        /// The catalog objects for submodules.
        /// </summary>
        public static Dictionary<string, SubmoduleCatalog> SubmoduleList { get; set; } =
            new Dictionary<string, SubmoduleCatalog>();

        /// <summary>
        /// Clears the contents of the Catalog.
        /// </summary>
        public static void Reset()
        {
            DeviceList.Clear();
            InterfaceList.Clear();
            ModuleList.Clear();
            PortList.Clear();
            SubmoduleList.Clear();
            ImportedGSDMLList.Clear();
            s_CentralDeviceCatalog.Clear();
        }
    }
}