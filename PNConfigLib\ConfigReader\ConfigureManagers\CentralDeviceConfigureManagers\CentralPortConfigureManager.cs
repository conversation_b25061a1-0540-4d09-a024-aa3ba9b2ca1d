﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: CentralPortConfigureManager.cs            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.CentralDeviceConfigureManagers
{
    internal class CentralPortConfigureManager
    {
        private Project m_Project;

        private Topology.Topology m_Topology
        {
            get;
        }

        internal CentralPortConfigureManager(Project project, Topology.Topology topology)
        {
            m_Project = project;
            m_Topology = topology;
        }

        internal void Configure(
            CentralDeviceCatalog pndCatalog,
            PNDriverType lonPnDriver,
            Interface interfaceSubmodule,
            CentralDeviceType xmlCentralDevice)
        {
            foreach (uint portCursor in pndCatalog.Interface.PortList.Keys)
            {
                byte portNumber = (byte)pndCatalog.Interface.PortList[portCursor].AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnPortNumber,
                    new AttributeAccessCode(),
                    0);

                Port port = new Port(portNumber);
                port.PCLCatalogObject = pndCatalog.Interface.PortList[portCursor];

                if (lonPnDriver.Interface.InterfaceType == PNDriverInterfaceEnum.Custom)
                {
                    AttributeUtilities.SetCustomVariantSubmoduleIdentNumber(port, lonPnDriver.DeviceVersion);
                }

                interfaceSubmodule.AddPort(port);
                IPortBL portBL = PNBasePortBL.PortBLCreator(port);
                PortType xmlPort = new PortType { PortNumber = portNumber };

                if ((xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.Ports != null)
                    && xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.Ports.Any(
                        p => p.PortNumber == portNumber))
                {
                    xmlPort = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.Ports.First(
                        p => p.PortNumber == portNumber);
                }

                bool isLocal;
                portBL.Configure(
                    xmlPort,
                    ProjectManagerUtilities.FindPortInterconnectionByInterfaceAndPortNumber(
                        xmlCentralDevice.CentralDeviceInterface.InterfaceRefID,
                        xmlPort.PortNumber,
                        m_Topology,
                        out isLocal),
                    pndCatalog.Interface.PortList.Keys.Count,
                    isLocal);
                m_Project.BusinessLogicList.Add(portBL);
            }
        }
    }
}
