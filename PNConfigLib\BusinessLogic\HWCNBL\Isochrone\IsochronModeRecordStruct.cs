/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: IsochronModeRecordStruct.cs               :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Isochrone
{
    internal class IsochronModeRecordStruct : DataRecordStruct
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="IsochronModeRecordStruct" /> class.
        /// </summary>
        public IsochronModeRecordStruct()
        {
            BlockType = 0xF005;
            BlockLength = 0x0010;
            BlockVersion = 0x0100;
            Reserved = 0;
            Data = new byte[12];
        }

        /// <summary>
        /// Gets or sets the CACF.
        /// </summary>
        public int Cacf
        {
            get { return BufferManager.Read16(Data, 0); }
            set { BufferManager.Write16(Data, 0, value); }
        }

        /// <summary>
        /// Gets or sets the Reserved End
        /// </summary>
        public int ReservedEnd
        {
            get { return BufferManager.Read16(Data, 10); }
            set { BufferManager.Write16(Data, 10, value); }
        }

        /// <summary>
        /// Gets or sets the TCA_Start.
        /// </summary>
        public uint TcaStart
        {
            get { return BufferManager.Read32(Data, 2); }
            set { BufferManager.Write32(Data, 2, value); }
        }

        /// <summary>
        /// Gets or sets the TCA_Valid.
        /// </summary>
        public uint TcaValid
        {
            get { return BufferManager.Read32(Data, 6); }
            set { BufferManager.Write32(Data, 6, value); }
        }
    }
}