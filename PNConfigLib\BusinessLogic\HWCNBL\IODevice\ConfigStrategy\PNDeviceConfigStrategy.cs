/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNDeviceConfigStrategy.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.BusinessLogic.HWCNBL;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.PNFunctions._Interfaces;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Tailor.Config;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;
using PNConfigLib.HWCNBL.Utilities.MachineTailor;
using PNConfigLib.PNPlannerAdapter;

#endregion

namespace PNConfigLib.HWCNBL.IODevice.ConfigStrategy
{
    internal partial class PNDeviceConfigStrategy : IFDecorator, IPNDeviceConfigStrategy
    {
        //########################################################################################

        #region Construction/Destruction/Initialisation

        public void InitActions()
        {
        }

        #endregion

        //########################################################################################

        #region Fields

        private Interface m_ItfController;

        private PNIOD m_IODevice;

        private IList<PclObject> m_Modules;

        private ISet<uint> m_Apis;

        private enum SubmoduleType
        {
            NoData = 0,

            InputData = 1,

            OutputData = 2,

            InputOutputData = 3
        }

        private enum DataType
        {
            Input = 1,

            Output = 0
        }

        #endregion

        //########################################################################################

        #region Overrides and Overridables

        public PNDeviceConfigStrategy(IInterfaceBusinessLogic decoratedInterface) : base(decoratedInterface)
        {
            InitBL();
        }

        public override void InitBL()
        {
            InitActions();
        }

        #endregion

        //########################################################################################

        #region Properties

        protected Interface ControllerInterfaceSubmodule
        {
            get
            {
                //in Plus CPU case we cannot rely any more on the cached value, as it may have been dereferenced in a previous GM call
                return m_ItfController ?? NavigationUtilities.GetControllerOfDevice(this.Interface);
            }
            set { m_ItfController = value; }
        }

        protected PNIOD IODevice
        {
            get
            {
                if (m_IODevice == null)
                {
                    m_IODevice = NavigationUtilities.GetIODevice(
                        this.Interface,
                        NavigationUtilities.GetIoSystem(ControllerInterfaceSubmodule));
                }
                return m_IODevice;
            }
        }

        protected IList<PclObject> Modules
        {
            get
            {
                if (m_Modules == null)
                {
                    m_Modules = PNNavigationUtility.GetModulesSortedCore(Interface);
                }
                return m_Modules;
            }
            set { m_Modules = value; }
        }

        /// <summary>
        /// </summary>
        private int GetSubmodulesPNSubslotNumber(PclObject submodule)
        {
            int pnSubslotNumber = submodule.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PnSubslotNumber,
                new AttributeAccessCode(),
                1);
            return pnSubslotNumber;
        }

        protected ISet<uint> Apis
        {
            get { return m_Apis; }
            set { m_Apis = value; }
        }

        #endregion

        //########################################################################################

        #region Public Methods IPNDeviceConfigStrategy

        #region IODeviceDataBlock

        /// <summary>
        /// </summary>
        public ushort GetDeviceId()
        {
            bool customizationEnabled =
                Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnCustomizationEnabled,
                    new AttributeAccessCode(),
                    false);

            if (customizationEnabled)
            {
                return
                    (ushort)
                    Interface.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.PnDeviceIdCustomized,
                        new AttributeAccessCode(),
                        0);
            }
            return
                (ushort)
                this.Interface.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnDeviceId,
                    new AttributeAccessCode(),
                    0x0301);
        }

        /// <summary>
        /// </summary>
        /// <returns></returns>
        public bool GetCheckDeviceId()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            if (
                !this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoCheckDeviceIDAllowed,
                    ac,
                    false))
            {
                return false;
            }

            return
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoCheckDeviceIDSupported,
                    ac.GetNew(),
                    false);
        }

        public bool IsAllowNameOfStationOverwriteSupported()
        {
            bool isPNAllowOverwriteNoISupported = ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnAllowOverwriteNoISupported,
                new AttributeAccessCode(),
                false);

            
            return isPNAllowOverwriteNoISupported;
        }

        public bool IsAllowNameOfStationOverwriteActive()
        {
                bool isAllowNameOfStationActive = ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnAllowOverwriteNoIActive,
                new AttributeAccessCode(),
                false);

            return isAllowNameOfStationActive;
        }

        /// <summary>
        /// </summary>
        public ushort GetVendorId()
        {
            return
                (ushort)
                this.Interface.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnVendorId,
                    new AttributeAccessCode(),
                    0x002A);
        }

        /// <summary>
        /// Gets the InstanceID for the IO-Device.
        /// </summary>
        /// <returns></returns>
        /// <remarks>
        /// what you are doing.
        /// </remarks>
        public virtual ushort GetDeviceInstance()
        {
            return PNInstanceIdUtilities.GetInstanceIdForRemoteIoDevice(this.Interface);
        }

        /// <summary>
        /// </summary>
        public virtual uint GetDeviceAddressMode()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            uint pnIoAddressModeFlags = this.Interface.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIoAddressModeFlags,
                ac,
                0);
            if (ac.IsOkay)
            {
                return pnIoAddressModeFlags;
            }
            DataModel.PCLObjects.Node node = this.Interface.Node;
            pnIoAddressModeFlags =
                (uint)
                (node.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoIpSuiteViaOtherPath,
                    ac.GetNew(),
                    false)
                     ? 5
                     : 0);

            ushort startupMode = GetStartupMode();
            // fast startup settings
            switch ((PNStartupMode)startupMode)
            {
                case PNStartupMode.NSU:
                    break;
                case PNStartupMode.ASU:
                case PNStartupMode.FSU:
                    pnIoAddressModeFlags = pnIoAddressModeFlags | 0x10;
                    break;
            }
            return pnIoAddressModeFlags;
        }

        /// <summary>
        /// Liefert die MultibleWrite Unterstützung des Controllers und des Devices
        /// PN_MULTIPLE_WRITE_SUPPORTED am HeadSubmodule
        /// CAPABLE_MULTIPLEWRITE am Controller
        /// </summary>
        /// <returns>true wenn Controller und Device multibleWrite unterstützen, sonst false</returns>
        public bool IsMultipleWriteSupported()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool deviceMultiWrite =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoMultipleWriteSupported,
                    ac,
                    false);
            if (!deviceMultiWrite)
            {
                return false;
            }

            ac.Reset();
            bool controllerMultiWrite =
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoMultipleWriteSupported,
                    ac,
                    false);
            return controllerMultiWrite;
        }

        /// <summary>
        /// </summary>
        public bool IsApduController()
        {
            // This attribute exists only for certain controllers and is set at MDD level.
            // Since PNDriver does not have it, this is always false.
            AttributeAccessCode ac = new AttributeAccessCode();
            return
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIncludeAPDU,
                    ac,
                    false);
        }

        /// <summary>
        /// If Device supports PDEV and the controller supports fsu/asu, ar record is supported
        /// </summary>
        /// <returns></returns>
        public virtual bool IsARRecordSupported()
        {          
            if (!GeneralUtilities.IsPDEVDevice(this.Interface)
                && !Utility.HasDeviceModuleOrSubmoduleWithMoreApIs(this.Interface))
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            bool arRecordSupported =
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoArRecordSupported,
                    ac,
                    true);

            if (arRecordSupported)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// If AR Record is supported and ParamSpeedup is supported AR Record is active
        /// </summary>
        /// <returns></returns>
        public virtual bool IsArRecordActive()
        {
            bool isArRecordActive = IsARRecordSupported() && IsParamSpeedupSupported();
            return isArRecordActive;
        }

        /// <summary>
        /// If Device supports Parametrizing speedup ArRecord table will be filled (if supported bei controller)
        /// </summary>
        /// <returns></returns>
        public bool IsParamSpeedupSupported()
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool paramSpeedupSupported =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoParameterizationSpeedupSupported,
                    ac,
                    false);

            return paramSpeedupSupported;
        }

        public ushort GetStartupMode()
        {
            PNStartupMode startupMode = PNStartupMode.NSU;

            AttributeAccessCode ac = new AttributeAccessCode();

            if (ControllerInterfaceSubmodule == null)
            {
                ControllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(this.Interface);
            }

            PNSuppControllerStartupMode suppControllerStartupMode = 0;

            if (ControllerInterfaceSubmodule != null)
            {
                suppControllerStartupMode =
                    (PNSuppControllerStartupMode)
                    ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIoSuppStartupModes,
                        ac,
                        0);
            }

            bool devicePNDcpHelloSupported =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDCPHelloSupported,
                    ac.GetNew(),
                    false);

            bool deviceStartupMode =
                this.Interface.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDeviceFSUPriority,
                    ac.GetNew(),
                    false);
            if (!deviceStartupMode)
            {
                return (ushort)startupMode;
            }

            switch (suppControllerStartupMode)
            {
                case PNSuppControllerStartupMode.ASUANDFSU:
                    if (devicePNDcpHelloSupported)
                    {
                        startupMode = PNStartupMode.FSU;
                    }
                    else
                    {
                        PNIOOperatingModes operatingMode = AttributeUtilities.GetOperatingMode(this.Interface);
                        if ((operatingMode == PNIOOperatingModes.IOControllerAndIODevice)
                            || (operatingMode == PNIOOperatingModes.IODevice))
                        {
                            startupMode = PNStartupMode.ASU;
                        }
                    }
                    break;
                case PNSuppControllerStartupMode.ASU:
                    startupMode = PNStartupMode.ASU;
                    break;
                default:
                    startupMode = PNStartupMode.NSU;
                    break;
            }

            return (ushort)startupMode;
        }

        /// <summary>
        /// Returns the RtClass of the Device
        /// </summary>
        public ushort GetIrtMode()
        {
            PNRTClass rtClass = ConfigUtility.GetRtClassfromFrameDataList(this.Interface);

            return (ushort)rtClass;
        }

        public byte[] GetPDIRData(int slotNumber, int subSlotNumber)
        {
            PdIrData pdIRData = new PdIrData();
            pdIRData.SlotNumber = slotNumber;
            pdIRData.SubSlotNumber = subSlotNumber;

            // Version of blocks are determined with startup mode and forwarding mode
            // If advanced startup mode is supported or forwarding mode is not none then new versions should be used
            AttributeAccessCode ac = new AttributeAccessCode();
            Enumerated pnIrtForwardingMode = Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                InternalAttributeNames.PnIrtForwardingMode, ac, null);
            PNIrtForwardingMode currForwardingMode = ac.IsOkay
                                     ? (PNIrtForwardingMode)pnIrtForwardingMode.DefaultValue
                                     : PNIrtForwardingMode.None;
            bool pdirxxxDataNewVersion = currForwardingMode != PNIrtForwardingMode.None;

            if (!pdirxxxDataNewVersion)
            {
                List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(this.Interface);
                //if advanced startup mode is supported then never version should also be used
                pdirxxxDataNewVersion = startupModes.Contains(PNIrtArStartupMode.Advanced);
            }

            // generate global data 
            int pdirGlobalDataVersion = pdirxxxDataNewVersion ? 0x102 : 0x101;
            PdIrGlobalData globalData = ConfigUtility.GeneratePdirGlobalData(this.Interface, pdirGlobalDataVersion);
            pdIRData.AddRecordData(globalData.ToByteArray);

            //generate frame data
            int pdirFrameDataVersion = pdirxxxDataNewVersion ? 0x101 : 0x100;
            ParameterDSStruct pdirFrameData = ConfigUtility.GeneratePdirFrameData(this.Interface, pdirFrameDataVersion);
            pdIRData.AddRecordData(pdirFrameData.ToByteArray);

            // generate begin end data
            PdIrBeginEndData dsBeginEndData = ConfigUtility.GeneratePdirBeginEndData(this.Interface);
            pdIRData.AddRecordData(dsBeginEndData.ToByteArray);

            return pdIRData.ToByteArray;
        }

        public bool IsDcpReadOnlyEnabled()
        {
            IDcpReadOnlyHelper helper = new DcpReadOnlyHelper(this.Interface);

            return helper.IsDcpEnableReadOnly;
        }

        #endregion

        #region ARTableBlock

        /// <summary>
        /// This method has to be called before accessing any Config- Address- or PrmData
        /// </summary>
        public void InitializeArData(Interface controllerInterfaceSubmodule)
        {
            if (controllerInterfaceSubmodule != null)
            {
                ControllerInterfaceSubmodule = controllerInterfaceSubmodule;
            }

            InitializeIOEntries();
            InitializeConfigData();
            InitializePrmData();
            InitializeSubmoduleProperties();
        }

        public Guid GetArUuid()
        {
            return Interface.AttributeAccess.GetAnyAttribute<Guid>(
                InternalAttributeNames.PnIoArUuid,
                new AttributeAccessCode(),
                Guid.Empty);
        }

        /// <summary>
        /// </summary>
        public virtual ushort GetArType()
        {
            PNRTClass rtClass = ConfigUtility.GetRtClassfromFrameDataList(this.Interface);

            // IOC_AR_Single with RtClass3
            if (rtClass == PNRTClass.IrtTop)
            {
                return 0x10;
            }
            return 0x01;
        }

        /// <summary>
        /// </summary>
        public uint GetArProperties()
        {
            uint arProperties = 0x0011;

            // Check the used frame type from generated frame types

            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(
                IODevice,
                false,
                ControllerInterfaceSubmodule);
            if ((pnFrameDataList == null)
                || (pnFrameDataList.Count == 0))
            {
                IMethodData methodData = new MethodData();
                methodData.Name = PrepareForPNPlanner.Name;
                methodData.Arguments[PNConstants.IsCompile] = true;
                ControllerInterfaceSubmodule.BaseActions.CallMethod(methodData);

                pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(
                    IODevice,
                    false,
                    ControllerInterfaceSubmodule);
            }
            if ((pnFrameDataList.Count > 0) && (pnFrameDataList[0].FrameClass == (long)PNIOFrameClass.Class3Frame))
            {
                // Check the startup mode of the sync-domain and set bit 30 if it is advanced.
                SyncDomain syncDomain = Interface.SyncDomain;
                if (syncDomain == null)
                {
                    return arProperties;
                }

                SyncDomainBusinessLogic syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
                if (syncDomainBusinessLogic == null)
                {
                    return arProperties;
                }

                if (syncDomainBusinessLogic.IsAdvancedStartupModeActive)
                {
                    // Bit 30 must also be set.
                    arProperties = 0x40000011;
                }
            }
            else
            {
                // Check the startup mode of the controller and the device interface submodule and set bit 30 if
                // both of them support advanced startup mode.
                AttributeAccessCode ac = new AttributeAccessCode();
                Enumerated pnIoArStartupModeEnumerated =
                    Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoArStartupMode,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    bool advancedSupported = false;

                    foreach (object startupModeObject in pnIoArStartupModeEnumerated.List)
                    {
                        PNIOARStartupMode startupMode =
                            (PNIOARStartupMode)Enum.Parse(typeof (PNIOARStartupMode), startupModeObject.ToString());

                        if (startupMode == PNIOARStartupMode.Advanced)
                        {
                            advancedSupported = true;
                            break;
                        }
                    }

                    if (!advancedSupported)
                    {
                        return arProperties;
                    }
                }
                else
                {
                    return arProperties;
                }

                ac.Reset();
                Enumerated controllerSuppStartupModesEnumerated =
                    ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoArStartupMode,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    foreach (object startupModeObject in controllerSuppStartupModesEnumerated.List)
                    {
                        PNIOARStartupMode startupMode =
                            (PNIOARStartupMode)Enum.Parse(typeof (PNIOARStartupMode), startupModeObject.ToString());

                        if (startupMode == PNIOARStartupMode.Advanced)
                        {
                            arProperties = 0x40000011;
                            break;
                        }
                    }
                }
            }
            return arProperties;
        }

        /// <summary>
        /// </summary>
        public ushort GetCmiActivityTimeout()
        {
            // Fixed 600 unless the sync domain uses irttop with advanced startup mode. 
            const ushort TimeoutForLegacyMode = 600;
            const ushort TimeoutForAdvancedMode = 200;

            // Check the RT Class of the frame from generated frames
            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(IODevice, false);
            if (pnFrameDataList[0].FrameClass == (long)PNIOFrameClass.Class3Frame)
            {
                SyncDomain syncDomain = Interface.SyncDomain;
                if (syncDomain == null)
                {
                    return TimeoutForLegacyMode;
                }

                SyncDomainBusinessLogic syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
                if (syncDomainBusinessLogic == null)
                {
                    return TimeoutForLegacyMode;
                }

                if (syncDomainBusinessLogic.IsAdvancedStartupModeActive)
                {
                    return TimeoutForAdvancedMode;
                }
            }
            else
            {
                // Check the startup mode of the controller and the device interface submodule

                AttributeAccessCode ac = new AttributeAccessCode();
                Enumerated pnIoArStartupModeEnumerated =
                    Interface.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoArStartupMode,
                        ac,
                        null);

                if (pnIoArStartupModeEnumerated == null)
                {
                    return TimeoutForLegacyMode;
                }

                bool returnLegacy = true;

                foreach (object startupModeObject in pnIoArStartupModeEnumerated.List)
                {
                    PNIOARStartupMode startupMode =
                        (PNIOARStartupMode)Enum.Parse(typeof (PNIOARStartupMode), startupModeObject.ToString());

                    if (startupMode == PNIOARStartupMode.Advanced)
                    {
                        returnLegacy = false;
                        break;
                    }
                }

                if (returnLegacy)
                {
                    return TimeoutForLegacyMode;
                }

                ac.Reset();
                Enumerated controllerSuppStartupModesEnumerated =
                    ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoArStartupMode,
                        ac,
                        null);

                if (ac.IsOkay)
                {
                    foreach (object startupModeObject in controllerSuppStartupModesEnumerated.List)
                    {
                        PNIOARStartupMode startupMode =
                            (PNIOARStartupMode)Enum.Parse(typeof (PNIOARStartupMode), startupModeObject.ToString());

                        if (startupMode == PNIOARStartupMode.Advanced)
                        {
                            return TimeoutForAdvancedMode;
                        }
                    }
                }
            }
            return TimeoutForLegacyMode;
        }

        /// <summary>
        /// This method gets the value of the Config2003BlockVersion attribute of the controler Interface-Submodule.
        /// </summary>
        /// <param></param>
        /// <param name="blocktype"></param>
        /// <returns>Config2003BlockVersion</returns>
        public ushort GetBlockVersion(int blocktype)
        {
            return PNConstants.Config2003BlockVersion;
        }

        /// <summary>
        /// Returns the Int32 attribute of the InterfaceSubmodule with given attributeName. defaultValue will be returned if query
        /// fails.
        /// </summary>
        /// <param name="attributeName"></param>
        /// <param name="defaultValue"></param>
        /// <returns>attributeValue of InterfaceSubmodule</returns>
        public virtual T GetAttributeValue<T>(string attributeName, T defaultValue)
        {
            return this.Interface.AttributeAccess.GetAnyAttribute<T>(
                attributeName,
                new AttributeAccessCode(),
                defaultValue);
        }

        /// <summary>
        /// Returns an array of APIs.
        /// </summary>
        public virtual uint[] GetAPIs()
        {
            Apis = new HashSet<uint>();

            foreach (PclObject module in Modules)
            {
                //IConfigBase moduleCfgBase = this.Interface.ConfigObject.HwcnBasicsFacade.GetConfigObject(module);
                IList<PclObject> submodules = GetSubmodulesInternal(module);
                AttributeAccessCode ac = new AttributeAccessCode();

                foreach (PclObject submodule in submodules)
                {
                    uint api = submodule.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnAPI, ac, 0);

                    Apis.Add(api);
                }

                if (submodules.Count == 0)
                {
                    //Check whether PNAPI is accessible at the module itself. If it is, then consider it for the generation!
                    uint api = module.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnAPI,
                        ac.GetNew(),
                        0);

                    Apis.Add(api);
                }
            }

            return Apis.ToArray();
        }

        /// <summary>
        /// Gets a list of slots for the given device.
        /// </summary>
        /// <returns>List of slots of the given device</returns>
        public IList<PclObject> GetModules()
        {
            return Modules;
        }

        /// <summary>
        /// Gets a list of subslots filled with submodules of given module.
        /// If ther isn't a real submodule this function returns the module as single submodule!!
        /// </summary>
        /// <param name="module"></param>
        /// <returns>array of subslots filled with submodules</returns>
        public IList<PclObject> GetSubmodules(PclObject module)
        {
            List<PclObject> list = new List<PclObject>(GetSubmodulesInternal(module));

            if (list.Count == 0)
            {
                list.Add(module);
            }

            return list;
        }

        /// <summary>
        /// Generates RTARetries.
        /// Value of the RTAEntries is dependant to WatchdogFaktor for old PDEV less than 0x0102:
        ///      WatchdogFaktor less than 15: RTAEntries = WatchdogFaktor
        ///      WatchdogFaktor >= 15: RTAEntries = 15
        /// From PDEV 0x0102, the value is always 3.
        /// </summary>
        public ushort GetRTARetries()
        {
            if (GetBlockVersion(0) < 0x0102)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                long watchdogFactor =
                    IODevice.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIoWatchdogFactor, ac, 0);

                if (!ac.IsOkay)
                {
                    return
                        (ushort)
                        this.Interface.AttributeAccess.GetAnyAttribute<int>(
                            InternalAttributeNames.PnConfigAlarmRetries,
                            ac,
                            3);
                }

                return (ushort)(watchdogFactor < 15 ? watchdogFactor : 15);
            }
            
            SyncDomain syncDomain = Interface.SyncDomain;
            if (null == syncDomain)
            {
                return 3;
            }
            SyncDomainBusinessLogic syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
            if (null == syncDomainBusinessLogic)
            {
                return 3;
            }
            List<IPNPlannerOutputFrame> frameBlocks = (List<IPNPlannerOutputFrame>)syncDomainBusinessLogic.GetConfig2008FrameBlocks(Interface);
            if (frameBlocks.Count == 0)
            {
                // The interface doesn't have any frames, return.
                return 3;
            }

            foreach (IPNPlannerOutputFrame frameBlock in frameBlocks)
            {
                if ((frameBlock.FrameID >= (int)PNRTFrameIdLimits.RTClass3FrameId.RedundantMin)
                    && (frameBlock.FrameID <= (int)PNRTFrameIdLimits.RTClass3FrameId.RedundantMax))
                {
                    return 6;
                }
            }

            return 3;
        }

        public ushort GetRTATimeoutFactor()
        {
            if (GetBlockVersion(0) < 0x0102)
            {
                return Interface.AttributeAccess.GetAnyAttribute<ushort>(
                    InternalAttributeNames.RTATimeoutFactor,
                    new AttributeAccessCode(),
                    1);
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(IODevice, false);
            long scf;
            if ((pnFrameDataList == null)
                || (pnFrameDataList.Count == 0))
            {
                scf = PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor;
            }
            else
            {
                scf = pnFrameDataList[0].SendClockFactor;
            }
            long rr = 0;
            if (pnFrameDataList != null)
            {
                rr = pnFrameDataList[0].DeviceLocalReductionRatio;
            }
            long wdf = IODevice.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoWatchdogFactor,
                ac.GetNew(),
                3);
            Debug.Assert(ac.IsOkay, "Device PNIoWatchdogFactor cannot be read.");

            if ((scf == 0)
                || (rr == 0)
                || (wdf == 0))
            {
                return Interface.AttributeAccess.GetAnyAttribute<ushort>(
                    InternalAttributeNames.RTATimeoutFactor,
                    new AttributeAccessCode(),
                    1);
            }

            return (ushort)Math.Ceiling(Math.Min(scf * rr * wdf * 0.03125, 1920.0) / 100.0);
        }

        /// <summary>
        /// </summary>
        public IList<byte[]> GetARRecordData()
        {
            IList<byte[]> arRecordTable = new List<byte[]>();

            //construct new Dataset-header
            ParameterDatasetStruct arfsuDataAdjustDs = new ParameterDatasetStruct();
            arfsuDataAdjustDs.ParaDSNumber = 0xE050;

            ParameterDSStruct arfsuDataAdjustBlock = new ParameterDSStruct();
            arfsuDataAdjustBlock.ParaBlockType = 0x0609;
            arfsuDataAdjustBlock.ParaBlockVersion = 0x0100;

            //generate FSUDataAdjustheader
            FSParameterBlockStruct fsParameterBlock = new FSParameterBlockStruct();
            fsParameterBlock.ParaBlockType = 0x0601;

            fsParameterBlock.FSParameterMode = 1;

            fsParameterBlock.FSParameterUUID = GetFsuParameterUUID();

            //add Block to DS
            arfsuDataAdjustBlock.AddSubblock(fsParameterBlock.ToByteArray);

            //add DS in Dataset struct
            arfsuDataAdjustDs.AddParaBlock(arfsuDataAdjustBlock.ToByteArray);

            arRecordTable.Add(arfsuDataAdjustDs.ToByteArray);

            return arRecordTable;
        }

        /// <summary>
        /// Calculate unique identifier for FSU
        /// calculated by md5 hash code function from PrmData, DonfigData and IRDataID (in case of IRTtop)
        /// </summary>
        /// <returns></returns>
        private byte[] GetFsuParameterUUID()
        {
            // compute md5 hash
            byte[] uuid;

            List<byte> data = new List<byte>();
            data.AddRange(GetPrmDataBlock());
            data.AddRange(GetConfigDataBlock());
            if (ConfigUtility.GetRtClassfromFrameDataList(this.Interface) == PNRTClass.IrtTop)
            {
                byte[] irDataUUID = ConfigUtility.GetIRDataIDOfDevice(this.Interface);

                //use IRDataID 3 times to eliminate md5 failure 
                data.AddRange(irDataUUID);
                data.AddRange(irDataUUID);
                data.AddRange(irDataUUID);
            }

            MD5Encryptor.Encrypt(data.ToArray(), out uuid);
            return uuid;
        }

        /// <summary>
        /// Returns true if IRInfoBlock is required. It is only required if the rt class of the device frame is 3 and
        /// advanced startup mode (one provider mode) is active at the sync-domain.
        /// </summary>
        public bool IsIRInfoBlockRequired()
        {
            // Check IrtTop project from generated frame types
            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(IODevice, false);
            if (pnFrameDataList[0].FrameClass == (long)PNIOFrameClass.Class3Frame)
            {
                SyncDomain syncDomain = Interface.SyncDomain;
                if (syncDomain == null)
                {
                    return false;
                }

                SyncDomainBusinessLogic syncDomainBusinessLogic = syncDomain.SyncDomainBusinessLogic;
                if (syncDomainBusinessLogic == null)
                {
                    return false;
                }
                if (syncDomainBusinessLogic.IsAdvancedStartupModeActive)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Calculates the subframe offset (start of the data) for a specific subframe of the dfp frame.
        /// This equation is used:
        /// subframeOffset = (NumPrevSubframes + 1) * subframeappendix + PreviousDataLength +
        ///                  padding at the beginning of the subframe
        /// NumPrevSubframes = in inbound direction, number of the subframes which have smaller subframe id.
        ///                    in outbound direction, number of the subframes which have larger subframe id.
        /// PreviousDataLength = in inbound direction, data length of the all subframes which have smaller subframe id.
        ///                      in outbound direction, data length of the all subframes which have larger subframe id.
        /// </summary>
        private ushort GetSubframeOffset(IPNDfpFrameData dfpFrame, int subframeId)
        {
            int prevDataLength = 0;
            int numPrevSubframes = 0;
            switch (dfpFrame.FrameDirection)
            {
                case (byte)PNPlannerFrameDirection.InputFrame:
                    for (int i = 1; i < subframeId; i++)
                    {
                        Debug.Assert(dfpFrame.Subframes[i] != null, "Subframes are built incorrectly.");
                        if (dfpFrame.Subframes[i] == null)
                        {
                            continue;
                        }
                        numPrevSubframes++;
                        prevDataLength += dfpFrame.Subframes[i].SubframeLength;
                    }
                    break;
                case (byte)PNPlannerFrameDirection.OutputFrame:
                    int largestSubframeId = dfpFrame.Subframes.Keys[dfpFrame.Subframes.Count - 1];
                    for (int i = largestSubframeId; i > subframeId; i--)
                    {
                        Debug.Assert(dfpFrame.Subframes[i] != null, "Subframes are built incorrectly.");
                        if (dfpFrame.Subframes[i] == null)
                        {
                            continue;
                        }
                        numPrevSubframes++;
                        prevDataLength += dfpFrame.Subframes[i].SubframeLength;
                    }
                    break;
            }

            // Check the padding at the beginning of subframes (for now it can only be given with settings)
            int sfBeginNumPaddingBytes = 0;
            return
                (ushort)
                ((numPrevSubframes + 1) * PNPlannerConstants.m_SubframeAppendix + prevDataLength
                 + sfBeginNumPaddingBytes);
        }

        #endregion

        #region StationNameAlias

        /// <summary>
        /// Checks attribute PNIoExchangeWithoutMMC of controller interface submodule and available linked ports.
        /// </summary>
        public virtual bool IsExchangeWithoutMMCSupported()
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool mmcsupported =
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnExchangeWithoutMmcSupported,
                    ac,
                    false);
            if (ac.IsOkay
                && (mmcsupported == false))
            {
                return false;
            }

            uint exchangeWithoutMmc =
                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoExchangeWithoutMMC,
                    ac.GetNew(),
                    0);
            if (!Convert.ToBoolean(exchangeWithoutMmc, CultureInfo.InvariantCulture))
            {
                return false;
            }

            // get ports of current interface
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(this.Interface);
            if (ports.Count != 0)
            {
                foreach (DataModel.PCLObjects.Port port in ports)
                {
                    // get connected ports
                    IList<DataModel.PCLObjects.Port> connectedPorts = port.GetPartnerPorts();
                    if ((null != connectedPorts))
                    {
                        uint value =
                            this.Interface.AttributeAccess.GetAnyAttribute(
                                InternalAttributeNames.PnPdevParametrizationDecentral,
                                ac,
                                0u);

                        if (ac.IsOkay
                            && (value == 0))
                        {
                            uint pdev =
                                ControllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                                    InternalAttributeNames.PnPdevSupportedModel,
                                    ac.GetNew(),
                                    0);
                            if (ac.IsOkay
                                && (pdev > 0))
                            {
                                return false;
                            }
                        }
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// </summary>
        public IList<byte[]> GetStationNameAliases()
        {
            IList<byte[]> stationNameAliasTable = new List<byte[]>();

            // get ports of current interface
            List<DataModel.PCLObjects.Port> ports = NavigationUtilities.GetPortModules(this.Interface);
            if (ports.Count != 0)
            {
                foreach (DataModel.PCLObjects.Port port in NavigationUtilities.SortPortObjects(ports))
                {

                    //Add an empty entry if port is programmable peer
                    if (MachineTailorUtility.IsProgrammablePeerEnabled(port))
                    {
                        StationNameAliasEntryStruct entry = new StationNameAliasEntryStruct();
                        entry.AliasName = string.Empty;
                        stationNameAliasTable.Add(entry.ToByteArray);
                    }

                    // get connected ports
                    IList<DataModel.PCLObjects.Port> connectedPorts = port.GetPartnerPorts();
                    if ((null != connectedPorts)
                        && (connectedPorts.Count != 0))
                    {
                        foreach (DataModel.PCLObjects.Port connectedPort in connectedPorts)
                        {
                            // create StationNameAlias struct
                            StationNameAliasEntryStruct entry = new StationNameAliasEntryStruct();

                            entry.AliasName = ConfigUtility.GetPortName(port, connectedPort);

                            //add entry into list of entries
                            stationNameAliasTable.Add(entry.ToByteArray);
                        }
                    }
                }
            }

            return stationNameAliasTable;
        }

        #endregion

        public byte[] GetDataRecords(PclObject module)
        {
            if (this.Parameters == null)
            {
                return null;
            }

            PclObject configObject = module;

            if (!this.Parameters.ContainsKey(configObject))
            {
                return null;
            }

            ParameterDataBlockStruct pdb = this.Parameters[configObject];

            return pdb.ToByteArray;
        }

        /// <summary>
        /// Delete properties which are initialized during compile. This method is called
        /// on the end of the compile.
        /// </summary>
        public virtual void ReleaseCaches()
        {
            if (Parameters != null)
            {
                Parameters.Clear();
            }
            if (ConfigData != null)
            {
                ConfigData.Clear();
            }
            m_ItfController = null;
            m_Modules = null;
            m_IODevice = null;
            if (m_Apis != null)
            {
                m_Apis.Clear();
            }
        }
        #endregion

        //########################################################################################

        #region Generic methods

        #endregion

        //########################################################################################

        #region helpers

        /// <summary>
        /// Gets a list of subslots filled with submodules of given module.
        /// </summary>
        /// <returns>array of subslots filled with submodules</returns>
        protected virtual IList<PclObject> GetSubmodulesInternal(PclObject module)
        {
            if (module is DecentralDevice)
            {
                return PNNavigationUtility.GetHeadSubmodulesSorted(module as DecentralDevice);
            }
            return PNNavigationUtility.GetSubmodulesSorted(module);
        }

        protected virtual bool IsSharedSubmoduleExist(PclObject module)
        {
            return false;
        }
        #endregion

        //########################################################################################

        #region Other private methods

        /// <summary>
        /// function adds the PdMasterTailorData dataset to the list of datasets
        /// </summary>
        /// <param name="controllersInterfaceSubmodule"></param>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="addDataSetCallback">callback method to add the dataset</param>
        private void AddPdMasterTailorData(
            Interface controllersInterfaceSubmodule,
            Interface interfaceSubmodule,
            Action<ParameterDatasetStruct> addDataSetCallback)
        {
            if (controllersInterfaceSubmodule == null)
            {
                controllersInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);
            }
            if (controllersInterfaceSubmodule != null)
            {
                TailorConfigLogicDecentral tailorConfigLogicDecentral =
                    new TailorConfigLogicDecentral(controllersInterfaceSubmodule, interfaceSubmodule);
                ParameterDatasetStruct pdMasterTailorDataRecord =
                    tailorConfigLogicDecentral.GetPDMasterTailorDataDatasetStruct();
                if ((pdMasterTailorDataRecord != null)
                    && (addDataSetCallback != null))
                {
                    addDataSetCallback(pdMasterTailorDataRecord);
                }
            }
        }

        #endregion

        public bool IsDeviceDefaultRouterIndividual()
        {
            // if Controller is null then no need to set this field to TRUE.
            if (ControllerInterfaceSubmodule == null)
            {
                return false;
            }

            // if controller gets its IP address via other path, field must be false
            if (GetNodeIPConfigurationFromInterface(ControllerInterfaceSubmodule) == NodeIPConfiguration.Other)
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            var controllerNode = ControllerInterfaceSubmodule.Node;
            var nodeIPConfig = controllerNode.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.NodeIPConfiguration, ac, (int)NodeIPConfiguration.Project);

            // if IO-Controller gets its IP address via other path, field must be false.
            if (nodeIPConfig == (int)NodeIPConfiguration.Other)
            {
                return false;
            }

            DataModel.PCLObjects.Node node = Interface.Node;
            bool individualRouterSettings = !node.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.NodeIPDefaultRouterIODSync,
                    ac.GetNew(),
                    true);

            return individualRouterSettings && ac.IsOkay;
        }
        private static NodeIPConfiguration GetNodeIPConfigurationFromInterface(Interface interfaceSubmodule)
        {
            int nodeIPConfig = 0;
            var node = interfaceSubmodule.Node;
            if (node != null)
            {
                node.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.NodeIPConfiguration, new AttributeAccessCode(), 0);
            }
            return (NodeIPConfiguration)nodeIPConfig;
        }
    }
}