using PNConfigTool.Common;
using PNConfigTool.Models;
using PNConfigTool.Services;
using System;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace PNConfigTool.ViewModels
{
    public class NetworkConfigPageViewModel : ViewModelBase
    {
        private readonly IProjectService _projectService;
        private ProjectConfig? _project;
        private string _masterName = string.Empty;
        private string _masterIPAddress = string.Empty;
        private string _masterSubnetMask = string.Empty;
        private string _masterRouterAddress = string.Empty;

        public string MasterName
        {
            get => _masterName;
            set => SetProperty(ref _masterName, value);
        }

        public string MasterIPAddress
        {
            get => _masterIPAddress;
            set => SetProperty(ref _masterIPAddress, value);
        }

        public string MasterSubnetMask
        {
            get => _masterSubnetMask;
            set => SetProperty(ref _masterSubnetMask, value);
        }

        public string MasterRouterAddress
        {
            get => _masterRouterAddress;
            set => SetProperty(ref _masterRouterAddress, value);
        }

        public ObservableCollection<object> NetworkDevices { get; } = new ObservableCollection<object>();

        public ICommand SaveNetworkSettingsCommand { get; }
        public ICommand AddDeviceCommand { get; }
        public ICommand RemoveDeviceCommand { get; }

        public NetworkConfigPageViewModel(IProjectService projectService)
        {
            _projectService = projectService ?? throw new ArgumentNullException(nameof(projectService));
            
            SaveNetworkSettingsCommand = new RelayCommand(ExecuteSaveNetworkSettings);
            AddDeviceCommand = new RelayCommand(ExecuteAddDevice);
            RemoveDeviceCommand = new RelayCommand(ExecuteRemoveDevice, CanExecuteRemoveDevice);

            _projectService.ProjectChanged += OnProjectChanged;
            
            // Initialize with current project if available
            _project = _projectService.CurrentProject;
            if (_project != null)
            {
                LoadProjectData();
            }
        }

        private void OnProjectChanged(object? sender, ProjectConfig? project)
        {
            _project = project;
            if (_project != null)
            {
                LoadProjectData();
            }
            else
            {
                ClearData();
            }
        }

        private void LoadProjectData()
        {
            if (_project == null) return;

            // Load network settings from project using the new configuration structure
            MasterName = _project.ListOfNodesConfiguration.PNDriver.DeviceName;
            MasterIPAddress = _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress;
            MasterSubnetMask = _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask;
            MasterRouterAddress = _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress;

            // Load devices
            NetworkDevices.Clear();
            if (_project.ConfigurationSettings.DecentralDevices != null)
            {
                foreach (var device in _project.ConfigurationSettings.DecentralDevices)
                {
                    NetworkDevices.Add(device);
                }
            }
        }

        private void ClearData()
        {
            MasterName = string.Empty;
            MasterIPAddress = string.Empty;
            MasterSubnetMask = string.Empty;
            MasterRouterAddress = string.Empty;
            NetworkDevices.Clear();
        }

        private void ExecuteSaveNetworkSettings(object? parameter)
        {
            if (_project != null)
            {
                // Update project settings using the new configuration structure
                _project.ListOfNodesConfiguration.PNDriver.DeviceName = MasterName;
                _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = MasterIPAddress;
                _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask = MasterSubnetMask;
                _project.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress = MasterRouterAddress;

                // Save project
                _projectService.SaveProject();
            }
        }

        private void ExecuteAddDevice(object? parameter)
        {
            // This would open a device selection dialog
            // For now, just add a placeholder object
            if (_project != null)
            {
                var deviceCount = _project.ConfigurationSettings.DecentralDevices.Count;
                var deviceConfig = DeviceConfigFactory.CreateDecentralDeviceConfig(
                    "DeviceID-" + DateTime.Now.Ticks.ToString(),
                    "New Device",
                    "192.168.1." + (deviceCount + 2),
                    "*************",
                    deviceCount + 1,
                    "device" + (deviceCount + 1)
                );

                NetworkDevices.Add(deviceConfig);

                // Update project devices
                _project.ConfigurationSettings.DecentralDevices.Add(deviceConfig);
            }
        }

        private void ExecuteRemoveDevice(object? parameter)
        {
            if (_project == null || parameter == null) return;

            if (parameter is DecentralDeviceConfig device && NetworkDevices.Contains(device))
            {
                NetworkDevices.Remove(device);

                // Update project devices
                _project.ConfigurationSettings.DecentralDevices.Remove(device);
            }
        }

        private bool CanExecuteRemoveDevice(object? parameter)
        {
            return parameter != null && NetworkDevices.Contains(parameter);
        }
    }
} 