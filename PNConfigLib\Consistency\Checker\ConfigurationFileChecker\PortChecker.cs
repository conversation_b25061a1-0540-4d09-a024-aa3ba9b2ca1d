/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: PortChecker.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.IO;
using System.Linq;
using PNConfigLib.ConfigReader;
using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;
using PNConfigLib.PNProjectManager;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class PortChecker : IConsistencyChecker
    {
        private List<DecentralDeviceType> m_DecentralDevices;

        private readonly ConfigReader.ListOfNodes.ListOfNodes m_ListOfNodes;

        internal PortChecker(List<DecentralDeviceType> decentralDevices, ConfigReader.ListOfNodes.ListOfNodes lon)
        {
            m_DecentralDevices = decentralDevices;
            m_ListOfNodes = lon;
        }
        public void Check()
        {
            CheckPort();
        }
        private void CheckPort()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_DecentralDevices)
            {
                ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);

                string gsdFileName = Path.GetFileName(lonDecentralDevice.GSDPath);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdName(
                        gsdFileName,
                        lonDecentralDevice.GSDRefID);

                Dictionary<int, PortCatalog> portsOfDeviceLookup =
                    PortConfigurator.FillPortCatalogValues(decentralDeviceCatalog, gsdFileName);
                ModuleConfigurator.FillModuleValues(
                    xmlDecentralDevice,
                    decentralDeviceCatalog,
                    gsdFileName);

                ArePortGsdIdAndSubslotNumbersValid(xmlDecentralDevice, decentralDeviceCatalog, gsdFileName);

                FillXmlPortsAndCheckConsistency(
                        portsOfDeviceLookup,
                        xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports,
                        decentralDeviceCatalog.PhysicalSubslotList,
                        decentralDeviceCatalog.Interface.PortList);
            }
        }

        private void FillXmlPortsAndCheckConsistency(
            Dictionary<int, PortCatalog> systemDefinedPortLookup,
            List<PortType> xmlPorts,
            ICollection<uint> physicalSubSlots,
            SortedList<uint, PortCatalog> portCatalog = null)
        {
            Dictionary<uint, PortCatalog> catalogLookup = new Dictionary<uint, PortCatalog>();
            PortConfigurator.FillSystemDefinedValuesToPort(systemDefinedPortLookup, portCatalog, catalogLookup);
            List<PortType> allPorts = xmlPorts;

            IsSubslotNumberConfigurationWrong(catalogLookup, xmlPorts);
            IsPortNumberUsedMultipleTimes(allPorts);
            CheckPhysicalSubslot(allPorts, portCatalog, physicalSubSlots);

            // A temporary list is created in order to add the ports in the right order according to their port number 
            SortAllPorts(allPorts);
        }

        private void IsSubslotNumberConfigurationWrong(Dictionary<uint, PortCatalog> catalogLookup, List<PortType> xmlPorts)
        {
            byte iPortNumberCursor = 0;
            foreach (uint slotNumber in catalogLookup.Keys)
            {
                ++iPortNumberCursor;
                if (slotNumber != xmlPorts.Find(p => p.PortNumber == iPortNumberCursor).SubslotNumber)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SubslotNumberConfigurationIsWrong,
                        slotNumber);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsPortNumberUsedMultipleTimes(List<PortType> allPorts)
        {
            List<uint> allPortsNumbers = new List<uint>();
            foreach (PortType checkPort in allPorts)
            {
                if (allPortsNumbers.Contains(checkPort.PortNumber))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_PortNumberUsedMultipleTimes,
                        checkPort.PortNumber);
                    throw new ConsistencyCheckException();
                }
                allPortsNumbers.Add(checkPort.PortNumber);
            }
        }

        private void CheckPhysicalSubslot(List<PortType> allPorts, SortedList<uint, PortCatalog> portCatalog, ICollection<uint> physicalSubSlots)
        {
            List<int> usedPhysicalSubSlots = new List<int>();
            foreach (PortType xmlPort in allPorts)
            {
                Port port = new Port(xmlPort.PortNumber, xmlPort.SubslotNumber);
                int subSlotNumber = port.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    new AttributeAccessCode(),
                    0);
                if (subSlotNumber != 0)
                {
                    // Submodule in SystemDefinedSubmoduleList or VirtualSubmoduleList does not need to be in physical subslots.
                    if ((portCatalog != null)
                        && !(physicalSubSlots.Contains((uint)subSlotNumber)
                             || portCatalog.ContainsKey((uint)subSlotNumber)))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_PortSubmoduleNotHavePhysicalSubslot,
                            subSlotNumber,
                            port.Id);
                        throw new ConsistencyCheckException();

                    }

                    if (!usedPhysicalSubSlots.Contains(subSlotNumber))
                    {
                        usedPhysicalSubSlots.Add(subSlotNumber);
                    }
                    else
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_CollisionForPhysicalSubslot,
                            subSlotNumber);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }

        private void SortAllPorts(List<PortType> allPorts)
        {
            List<PortType> portList = new List<PortType>(allPorts);
            allPorts.Clear();
            allPorts.AddRange(portList.OrderBy(portNum => portNum.PortNumber).ToList());
        }

        /// <summary>
        /// Validate device's/module's port gsd id and subslotnumber
        /// </summary>
        /// <param name="xmlDecentralDevice"></param>
        /// <param name="decentralDeviceCatalog"></param>
        /// <param name="gsdFileName"></param>
        /// <returns></returns>
        private void ArePortGsdIdAndSubslotNumbersValid(
            DecentralDeviceType xmlDecentralDevice,
            DecentralDeviceCatalog decentralDeviceCatalog,
            string gsdFileName)
        {
            IsDevicePortGsdIdAndSubslotNumberValid(
                    xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.Ports,
                    decentralDeviceCatalog,
                    xmlDecentralDevice.DeviceRefID);

            if (xmlDecentralDevice.Module == null)
            {
                return;
            }

            foreach (ModuleType xmlModule in xmlDecentralDevice.Module)
            {
                ModuleCatalog moduleCatalog =
                    ModuleCatalogHelper.GetModuleCatalogWithGsdName(gsdFileName, xmlModule.GSDRefID);

                if (moduleCatalog == null)
                {
                    continue;
                }

                IsModulePortGsdIdAndSubslotNumberValid(
                        xmlModule.Port,
                        moduleCatalog,
                        xmlDecentralDevice.DeviceRefID);
            }
        }

        /// <summary>
        /// Validates port gsd id and subslot number
        /// </summary>
        /// <param name="xmlPorts"></param>
        /// <param name="moduleCatalog"></param>
        /// <param name="deviceId"></param>
        /// <returns></returns>
        private void IsModulePortGsdIdAndSubslotNumberValid(
            IEnumerable<PortType> xmlPorts,
            ModuleCatalog moduleCatalog,
            string deviceId)
        {
            foreach (PortType xmlPort in xmlPorts)
            {
                if (string.IsNullOrEmpty(xmlPort.GSDRefID))
                {
                    continue;
                }

                if (!moduleCatalog.PluggableSubmoduleList.ContainsKey(xmlPort.GSDRefID)
                    && moduleCatalog.SystemDefinedPorts.Values.All(
                        sysDef => sysDef.GsdId != xmlPort.GSDRefID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_NotValidPortGsdId,
                        xmlPort.GSDRefID,
                        deviceId);
                    throw new ConsistencyCheckException();
                }

                if (!xmlPort.SubslotNumberSpecified)
                {
                    continue;
                }

                bool isPluggableSubslotNrValid = true;
                if (moduleCatalog.PluggableSubmoduleList.ContainsKey(xmlPort.GSDRefID))
                {
                    isPluggableSubslotNrValid = HasPluggableSubmoduleValidSubslotNr(
                        moduleCatalog.PluggableSubmoduleList[xmlPort.GSDRefID],
                        null,
                        xmlPort.SubslotNumber);
                }

                bool isSysDefinedAndSubslotNrInvalid = moduleCatalog.SystemDefinedPorts.Any(
                    sysDef => sysDef.Value.GsdId == xmlPort.GSDRefID && xmlPort.SubslotNumber != sysDef.Key);

                if (isPluggableSubslotNrValid && !isSysDefinedAndSubslotNrInvalid)
                {
                    continue;
                }

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotValidPortSubslotNumber,
                    xmlPort.SubslotNumber,
                    deviceId,
                    xmlPort.GSDRefID);
                throw new ConsistencyCheckException();
            }
        }

        /// <summary>
        /// Validates port gsd id and subslot number
        /// </summary>
        /// <param name="xmlPorts"></param>
        /// <param name="deviceCatalog"></param>
        /// <param name="deviceId"></param>
        /// <returns></returns>
        private void IsDevicePortGsdIdAndSubslotNumberValid(
            IEnumerable<PortType> xmlPorts,
            DecentralDeviceCatalog deviceCatalog,
            string deviceId)
        {
            foreach (PortType xmlPort in xmlPorts)
            {
                if (string.IsNullOrEmpty(xmlPort.GSDRefID))
                {
                    continue;
                }

                if (!deviceCatalog.PluggableSubmoduleList.ContainsKey(xmlPort.GSDRefID)
                    && deviceCatalog.SystemDefinedSubmoduleList.All(sysDef => sysDef.GsdID != xmlPort.GSDRefID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_NotValidPortGsdId,
                        xmlPort.GSDRefID,
                        deviceId);
                    throw new ConsistencyCheckException();
                }

                if (!xmlPort.SubslotNumberSpecified)
                {
                    continue;
                }

                bool isPluggableSubslotNrValid = true;
                if (deviceCatalog.PluggableSubmoduleList.ContainsKey(xmlPort.GSDRefID))
                {
                    isPluggableSubslotNrValid = HasPluggableSubmoduleValidSubslotNr(
                        deviceCatalog.PluggableSubmoduleList[xmlPort.GSDRefID],
                        deviceCatalog.PhysicalSubslotList,
                        xmlPort.SubslotNumber);
                }

                bool isSysDefinedAndSubslotNrInvalid = deviceCatalog.SystemDefinedSubmoduleList.Any(
                    sysDef => sysDef.GsdID == xmlPort.GSDRefID && sysDef.SubslotNumber != xmlPort.SubslotNumber);

                if (isPluggableSubslotNrValid && !isSysDefinedAndSubslotNrInvalid)
                {
                    continue;
                }

                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_NotValidPortSubslotNumber,
                    xmlPort.SubslotNumber,
                    deviceId,
                    xmlPort.GSDRefID);
                throw new ConsistencyCheckException();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pluggableSubmoduleSlotRelation"></param>
        /// <param name="pyhsicalSubSlotList"></param>
        /// <param name="subslotNr"></param>
        /// <returns></returns>
        private static bool HasPluggableSubmoduleValidSubslotNr(
            SlotRelation pluggableSubmoduleSlotRelation,
            ICollection<uint> pyhsicalSubSlotList,
            uint subslotNr)
        {
            bool isPluggableSubslotNrValid = true;
            if (pluggableSubmoduleSlotRelation.AllowedInSlots != null)
            {
                isPluggableSubslotNrValid = pluggableSubmoduleSlotRelation.AllowedInSlots.OfType<uint>()
                    .Contains(subslotNr);
            }
            else
            {
                if (pluggableSubmoduleSlotRelation.FixedInSlots != null)
                {
                    isPluggableSubslotNrValid =
                        pluggableSubmoduleSlotRelation.FixedInSlots.OfType<uint>().Contains(subslotNr)
                        || pyhsicalSubSlotList != null && pyhsicalSubSlotList.Contains(subslotNr);
                }
                else if (pluggableSubmoduleSlotRelation.UsedInSlots != null)
                {
                    isPluggableSubslotNrValid =
                        pluggableSubmoduleSlotRelation.UsedInSlots.OfType<uint>().Contains(subslotNr)
                        || pyhsicalSubSlotList != null && pyhsicalSubSlotList.Contains(subslotNr);
                }
            }
            return isPluggableSubslotNrValid;
        }

        internal static bool IsPortParameterizationValid(IEnumerable<DecentralDeviceTypeDecentralDeviceInterface> xmlInterfaces, IEnumerable<Interface> interfaceSubmodules)
        {
            foreach (DecentralDeviceTypeDecentralDeviceInterface xmlInterface in xmlInterfaces)
            {
                Interface interfaceSubmodule =
                    interfaceSubmodules.FirstOrDefault(i => i.Id == xmlInterface.InterfaceRefID);

                List<PortType> ports = xmlInterface.AdvancedOptions.Ports;
                foreach (PortType port in ports)
                {
                    PortTypePortOptions portOptions = port.PortOptions;
                    bool portParametrizationDisallowed = interfaceSubmodule.GetPortByPortNumber(port.PortNumber)
                        .AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnParameterizationDisallowed,
                            new AttributeAccessCode(),
                            false);
                    if (portParametrizationDisallowed && (portOptions.TransmissionRate != TransmissionRate.Automatic
                                                          || portOptions.Monitor || !portOptions.EnableAutonegotiation
                                                          || !portOptions.ActivateThisPortForUse
                                                          || portOptions.EndOfDetectionOfAccessibleDevices
                                                          || portOptions.EndOfTheSyncDomain
                                                          || portOptions.EndOfTopologyDiscovery))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_ParameterizationDisallowed_InterfacePort,
                            port.PortNumber,
                            interfaceSubmodule.ParentObject.Id);
                        return false;
                    }
                }
            }

            return true;
        }
    }
}
