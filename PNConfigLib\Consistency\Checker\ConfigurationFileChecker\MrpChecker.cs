/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: MrpChecker.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;
using System.Text;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;

using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class MrpChecker : IConsistencyChecker
    {

        private List<CentralDeviceType> m_CentralDevices;

        private List<DecentralDeviceType> m_DecentralDevices;

        private List<MrpDomainType> m_MrpDomains;

        private readonly ListOfNodes m_ListOfNodes;

        internal MrpChecker(
            List<CentralDeviceType> centralDevices,
            List<DecentralDeviceType> decentralDevices,
            List<MrpDomainType> mrpDomains,
            ListOfNodes lon)
        {
            m_CentralDevices = centralDevices;
            m_DecentralDevices = decentralDevices;
            m_MrpDomains = mrpDomains;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            AreCentralDeviceMrpRefIdsValid();
            CheckMrpDomain();
        }

        private void AreCentralDeviceMrpRefIdsValid()
        {
            foreach (CentralDeviceType xmlCentralDevice in m_CentralDevices)
            {
                List<MrpRingType> mrpRings = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.MediaRedundancy;
                SyncRole syncRole = xmlCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;
                IsMrpdConfigured(mrpRings, syncRole, xmlCentralDevice.DeviceRefID);

                foreach (MrpRingType mrpRing in mrpRings)
                {
                    IsMrpDomainRefIdValid(mrpRing.MrpDomainRefID, xmlCentralDevice.DeviceRefID);
                }
            }
        }

        private void IsMrpdConfigured(List<MrpRingType> mrpRings, SyncRole syncRole, string deviceId)
        {
            if (syncRole != SyncRole.Unsynchronized
                && mrpRings.Any(r => r.MrpRole != MrpRole.Notdeviceinthering))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MrpdNotSupported,
                    deviceId,
                    syncRole);
                throw new ConsistencyCheckException();
            }
        }

        private bool CheckMrpDomain()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_DecentralDevices)
            {
                List<MrpRingType> mrpRings = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions
                    .MediaRedundancy;
                SyncRole syncRole = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole;

                IsMrpdConfigured(mrpRings, syncRole, xmlDecentralDevice.DeviceRefID);

                if (mrpRings == null)
                {
                    continue;
                }

                ConfigReader.ListOfNodes.DecentralDeviceType decentralDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);

                uint pnMrpMaxInstances = 0;

                if (decentralDevice != null)
                {
                    DecentralDeviceCatalog deviceCatalog =
                        DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                            decentralDevice.GSDPath,
                            decentralDevice.GSDRefID);

                    pnMrpMaxInstances = deviceCatalog.Interface.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnMrpMaxInstances,
                        new AttributeAccessCode(),
                        0);
                    if ((pnMrpMaxInstances == 0)
                        && deviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                            InternalAttributeNames.PnMrpSupported,
                            new AttributeAccessCode(),
                            false))
                    {
                        pnMrpMaxInstances = 1;
                    }
                }
                CheckMrpRingInstances(mrpRings, xmlDecentralDevice.DeviceRefID, pnMrpMaxInstances);

                foreach (MrpRingType mrpRing in mrpRings)
                {
                    if (mrpRings.Count > 1
                        || (mrpRings.Count == 1 && mrpRing.InstanceNumberSpecified))
                    {
                        if (!CheckMrpInstanceNumber(
                                mrpRing.InstanceNumber,
                                pnMrpMaxInstances,
                                xmlDecentralDevice.DeviceRefID,
                                mrpRing.MrpDomainRefID))
                        {
                            return false;
                        }
                    }

                    IsMrpDomainRefIdValid(mrpRing.MrpDomainRefID, xmlDecentralDevice.DeviceRefID);
                }
            }
            return true;
        }

        private static void CheckMrpRingInstances(
            IReadOnlyCollection<MrpRingType> mrpRings,
            string deviceId,
            uint maxInstanceCount)
        {
            if (maxInstanceCount > 0
                && mrpRings.Count > 0
                && maxInstanceCount != mrpRings.Count)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MrpRingCount,
                    deviceId,
                    maxInstanceCount,
                    mrpRings.Count);
                throw new ConsistencyCheckException();
            }

            if (mrpRings.Count <= 1)
            {
                return;
            }

            if (!mrpRings.All(ring => ring.InstanceNumberSpecified))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MrpRingInstanceNumberIsSpecified,
                    deviceId);
                throw new ConsistencyCheckException();
            }

            if (mrpRings.Select(ring => ring.InstanceNumber).Distinct().Count() != mrpRings.Count)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MrpInstanceNumberUniqueness,
                    deviceId);
                throw new ConsistencyCheckException();
            }
        }

        private static bool CheckMrpInstanceNumber(
            int instanceNumber,
            uint maxInstancesNumber,
            string deviceId,
            string mrpDomainId)
        {
            if (1 <= instanceNumber
                && instanceNumber <= maxInstancesNumber)
            {
                return true;
            }

            StringBuilder availableInstanceNumbers = new StringBuilder();
            for (int instanceNumberCursor = 1; instanceNumberCursor <= maxInstancesNumber; instanceNumberCursor++)
            {
                availableInstanceNumbers.Append(instanceNumberCursor);
                if (instanceNumberCursor != maxInstancesNumber)
                {
                    availableInstanceNumbers.Append(", ");
                }
            }

            ConsistencyLogger.Log(
                ConsistencyType.XML,
                LogSeverity.Error,
                string.Empty,
                ConsistencyConstants.XML_MrpInstanceNumberConsistency,
                availableInstanceNumbers,
                deviceId,
                mrpDomainId);
            throw new ConsistencyCheckException();
        }

        private void IsMrpDomainRefIdValid(string mrpDomainRefId, string deviceId)
        {
            if (m_MrpDomains.All(domain => domain.MrpDomainID != mrpDomainRefId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MrpDomainRefIdConsistency,
                    mrpDomainRefId,
                    deviceId);
                throw new ConsistencyCheckException();
            }
        }
    }
}
