/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNRTFrameLimits.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Diagnostics.CodeAnalysis;

#endregion

namespace PNConfigLib.HWCNBL.Constants.AttributeValues
{
    /// <summary>
    /// Container class for ID limits of RT frames.
    /// </summary>
    internal class PNRTFrameIdLimits
    {
        public const int SecondarySyncFrameId = 0xA0;

        public const int SyncFrameId = 0x80;

        /// <summary>
        /// Frame ID range 7 table, first part.
        /// </summary>
        public enum RTClass1FrameId
        {
            None = 0x0000,

            Min = 0xC000,

            Max = 0xF7FF
        }

        /// <summary>
        /// Frame ID range 6 table, first part (unicast).
        /// </summary>
        public enum RTClass2FrameId
        {
            None = 0x0000,

            Min = 0x8000,

            Max = 0xBBFF
        }

        /// <summary>
        /// Frame ID range 3 table (without the reserved part).
        /// </summary>
        public enum RTClass3FrameId
        {
            None = 0x0000,

            NonRedundantMin = 0x0100,

            NonRedundantMax = 0x06FF,

            RedundantMin = 0x0700,

            RedundantMax = 0x0FFF
        }
    }
}