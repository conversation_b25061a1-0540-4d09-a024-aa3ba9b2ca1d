﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConfigurationReader                       :C&  */
/*                                                                           */
/*  F i l e               &F: IOAddressConfigureManager.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.PNProjectManager;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;

namespace PNConfigLib.ConfigReader.ConfigureManagers.DecentralDeviceConfigureManagers
{
    internal class IOAddressConfigureManager
    {
        private ListOfNodes.ListOfNodes m_ListOfNodes
        {
            get;
        }

        internal IOAddressConfigureManager(ListOfNodes.ListOfNodes listOfNodes) 
        {
            m_ListOfNodes = listOfNodes;
        }

        internal void Configure(List<Configuration.DecentralDeviceType> cfgDecentralDevices,
           IOAddressManager ioAddressManager)
        {
            ReserveIOAddressesFromXmlValues(cfgDecentralDevices, ioAddressManager);
        }

        private void ReserveIOAddressesFromXmlValues(
           List<Configuration.DecentralDeviceType> configurationDevicesDecentralDevice,
           IOAddressManager ioAddressManager)
        {
            foreach (Configuration.DecentralDeviceType xmlDecentralDevice in configurationDevicesDecentralDevice)
            {
                ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    ProjectManagerUtilities.GetDecentralDeviceFromListOfNodes(
                        m_ListOfNodes,
                        xmlDecentralDevice.DeviceRefID);
                string gsdPath = Path.GetFileName(lonDecentralDevice.GSDPath);
                ReserveIOAddressForModule(ioAddressManager, xmlDecentralDevice, gsdPath);
            }
        }

        private static void ReserveIOAddressForModule(
           IOAddressManager ioAddressManager,
           Configuration.DecentralDeviceType xmlDecentralDevice,
           string gsdPath)
        {
            foreach (ModuleType module in xmlDecentralDevice.Module)
            {
                if (module.IOAddresses != null
                    && module.IOAddresses.Count > 0)
                {
                    foreach (var ioAddress in module.IOAddresses)
                    {
                        string moduleName = string.Format(
                            CultureInfo.InvariantCulture,
                            "{0}\\{1}",
                            gsdPath,
                            module.GSDRefID);
                        SubmoduleCatalog submoduleCatalog;

                        if (Catalog.ModuleList.ContainsKey(moduleName))
                        {
                            ModuleCatalog moduleCatalog = Catalog.ModuleList[moduleName];
                            submoduleCatalog = moduleCatalog.VirtualSubmoduleList.Find(
                                w => w.IOData.InputDataItems != null || w.IOData.OutputDataItems != null);
                        }
                        else
                        {
                            DecentralDeviceCatalog decentralDeviceCatalog = Catalog.DeviceList[moduleName];
                            submoduleCatalog = decentralDeviceCatalog.VirtualSubmoduleList.Find(
                                w => w.IOData.InputDataItems != null || w.IOData.OutputDataItems != null);
                        }

                        AttributeAccessCode ac = new AttributeAccessCode();
                        if (ioAddress is ModuleTypeInputAddresses)
                        {
                            SetIOData(
                                submoduleCatalog,
                                ac,
                                module.ModuleID,
                                InternalAttributeNames.InAddressRange,
                                IoTypes.Input,
                                ioAddress,
                                ioAddressManager);
                        }
                        else if (ioAddress is ModuleTypeOutputAddresses)
                        {
                            SetIOData(
                                submoduleCatalog,
                                ac,
                                module.ModuleID,
                                InternalAttributeNames.OutAddressRange,
                                IoTypes.Output,
                                ioAddress,
                                ioAddressManager);
                        }
                    }
                }

                ReserveIOAddressForSubmodule(ioAddressManager, gsdPath, module);
            }
        }

        private static void ReserveIOAddressForSubmodule(IOAddressManager ioAddressManager, string gsdPath, ModuleType module)
        {
            foreach (ModuleTypeSubmodule submodule in module.Submodule)
            {
                if (submodule.IOAddresses != null && submodule.IOAddresses.Count > 0)
                {
                    foreach (var ioAddress in submodule.IOAddresses)
                    {
                        SubmoduleCatalog submoduleCatalog = Catalog.SubmoduleList[string.Format(
                            CultureInfo.InvariantCulture,
                            "{0}\\{1}",
                            gsdPath,
                            submodule.GSDRefID)];

                        AttributeAccessCode ac = new AttributeAccessCode();
                        if (ioAddress is ModuleTypeInputAddresses)
                        {
                            SetIOData(
                                submoduleCatalog,
                                ac,
                                submodule.SubmoduleID,
                                InternalAttributeNames.InAddressRange,
                                IoTypes.Input,
                                ioAddress,
                                ioAddressManager);
                        }
                        else if (ioAddress is ModuleTypeOutputAddresses)
                        {
                            SetIOData(
                                submoduleCatalog,
                                ac,
                                submodule.SubmoduleID,
                                InternalAttributeNames.OutAddressRange,
                                IoTypes.Output,
                                ioAddress,
                                ioAddressManager);
                        }
                    }
                }
            }
        }

        private static void SetIOData(
            SubmoduleCatalog submoduleCatalog,
            AttributeAccessCode ac,
            string moduleId,
            string addreesRange,
            IoTypes ioType,
            object ioAddress,
            IOAddressManager ioAddressManager)
        {
            int addressRange = submoduleCatalog.AttributeAccess.GetAnyAttribute<int>(addreesRange, ac.GetNew(), -1);

            if (!ac.IsOkay)
            {
                throw new PNFunctionsException(
                    string.Format(
                        CultureInfo.InvariantCulture,
                        "{0} address length not given for module: {1}",
                        ioType.ToString(),
                        moduleId));
            }

            int startAddress = (int)(ioType == IoTypes.Input
                                         ? ((ModuleTypeInputAddresses)ioAddress).StartAddress
                                         : ((ModuleTypeOutputAddresses)ioAddress).StartAddress);
            ioAddressManager.GetFreeAddresses(
                addressRange,
                ioType,
                true,
                startAddress);
        }
    }
}
