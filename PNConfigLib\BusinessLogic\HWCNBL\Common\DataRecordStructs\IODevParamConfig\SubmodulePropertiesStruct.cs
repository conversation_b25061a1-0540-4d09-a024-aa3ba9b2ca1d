/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: SubmodulePropertiesStruct.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.HWCNBL.AdapterFramework.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Common.DataRecordStructs.IODevParamConfig
{
    /// <summary>
    /// The data record object for SubmoduleProperties.
    /// </summary>
    internal class SubmodulePropertiesStruct
    {
        /// <summary>
        /// Content of the data record.
        /// </summary>
        protected byte[] data = new byte[0];

        /// <summary>
        /// Header data of the data record.
        /// </summary>
        protected byte[] header;

        /// <summary>
        /// List of the subblocks in the data record.
        /// </summary>
        private IList<byte[]> listSubBlocks = new List<byte[]>();

        /// <summary>
        /// Default constructor of SubmodulePropertiesStruct.
        /// </summary>
        public SubmodulePropertiesStruct()
        {
            header = new byte[8];
            BlockType = DataRecords.BlockTypes.SubmoduleProperties;
            BlockLength = 0x4; // BlockVersion, Number of APIs
            BlockVersion = 0x0100;
        }

        /// <summary>
        /// APICount part of the data record.
        /// </summary>
        public int APICount
        {
            set { Transformator.Write16(header, 6, (ushort)value); }
            get { return Transformator.Read16(header, 6); }
        }

        /// <summary>
        /// BlockLength part of the data record.
        /// </summary>
        public int BlockLength
        {
            set { Transformator.Write16(header, 2, (ushort)value); }
            get { return Transformator.Read16(header, 2); }
        }

        /// <summary>
        /// BlockType part of the data record.
        /// </summary>
        public int BlockType
        {
            set { Transformator.Write16(header, 0, (ushort)value); }
            get { return Transformator.Read16(header, 0); }
        }

        /// <summary>
        /// BlockVersion part of the data record.
        /// </summary>
        public int BlockVersion
        {
            set { Transformator.Write16(header, 4, (ushort)value); }
            get { return Transformator.Read16(header, 4); }
        }

        /// <summary>
        /// Gets the data record as a byte array.
        /// </summary>
        public virtual byte[] ToByteArray
        {
            get
            {
                List<byte> block = new List<byte>();
                if (header != null)
                {
                    block.AddRange(header);
                }
                block.AddRange(data);

                foreach (byte[] subblock in listSubBlocks)
                {
                    block.AddRange(subblock);
                }

                return block.ToArray();
            }
        }

        /// <summary>
        /// Adds a subblock to the data record.
        /// </summary>
        /// <param name="subblock">The subblock to be added.</param>
        public void AddSubBlock(byte[] subblock)
        {
            listSubBlocks.Add(subblock);
            BlockLength += subblock.Length;
        }
    }
}