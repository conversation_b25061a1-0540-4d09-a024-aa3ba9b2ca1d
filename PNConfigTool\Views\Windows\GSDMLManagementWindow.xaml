<Window x:Class="PNConfigTool.Views.Windows.GSDMLManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PNConfigTool.Views.Windows"
        xmlns:vm="clr-namespace:PNConfigTool.ViewModels"
        mc:Ignorable="d"
        Title="GSDML 管理" Height="500" Width="830"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVis" />
    </Window.Resources>
    
    <Grid Margin="8">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 介绍文本 -->
        <GroupBox Grid.Row="0" Header="简介" Margin="0,0,0,10">
            <TextBlock Margin="5" TextWrapping="Wrap">
                可用"GSDML 管理"来为PROFINET安装和删除GSDML 文件。所有GSDML文件均保存在全局库中，可供所有项目使用。
            </TextBlock>
        </GroupBox>
        
        <!-- 已导入的GSDML文件列表 -->
        <TextBlock Grid.Row="1" Text="导入的 GSDML 文件" Margin="5,5,0,2" FontWeight="SemiBold"/>
        <ListView Grid.Row="2" 
                  ItemsSource="{Binding GSDMLFiles}" 
                  SelectedItem="{Binding SelectedGSDML}"
                  Margin="5,0,5,15"
                  Height="200"
                  SelectionMode="Extended">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="#" Width="30">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected, Mode=TwoWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="文件名" Width="450" DisplayMemberBinding="{Binding FileName}"/>
                    <GridViewColumn Header="安装日期" Width="150" DisplayMemberBinding="{Binding InstallDate}"/>
                    <GridViewColumn Header="状态" Width="60" DisplayMemberBinding="{Binding Status}"/>
                </GridView>
            </ListView.View>
        </ListView>
        
        <!-- 导入新的GSDML文件 -->
        <TextBlock Grid.Row="3" Text="导入新的 GSDML" Margin="5,5,0,2" FontWeight="SemiBold"/>
        <Grid Grid.Row="4" Margin="5,0,5,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBox Grid.Column="0" 
                     Text="{Binding GSDMLImportPath}" 
                     IsReadOnly="True" 
                     Padding="5,3"
                     Margin="0,0,5,0"/>
            <Button Grid.Column="1" 
                    Content="浏览..." 
                    Command="{Binding BrowseGSDMLCommand}"
                    Padding="10,5" 
                    Margin="0,0,5,0"/>
            <Button Grid.Column="2" 
                    Content="删除" 
                    Command="{Binding RemoveSelectedGSDMLCommand}"
                    Padding="10,5"/>
        </Grid>
        
        <!-- 状态栏和确定按钮 -->
        <Grid Grid.Row="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 状态栏 -->
            <TextBlock Grid.Column="0" 
                       Text="{Binding StatusMessage}" 
                       VerticalAlignment="Center" 
                       Margin="5,0,0,0" 
                       Foreground="{Binding StatusColor}"
                       Visibility="{Binding HasStatusMessage, Converter={StaticResource BoolToVis}}"/>
            
            <!-- 放置按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="确认" 
                        Command="{Binding CloseWindowCommand}"
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        HorizontalAlignment="Right" 
                        VerticalAlignment="Center"
                        Padding="15,3" 
                        MinWidth="70"
                        Margin="0,0,5,5"
                        IsCancel="True"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>