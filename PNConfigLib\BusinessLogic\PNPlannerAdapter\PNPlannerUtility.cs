/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNPlannerUtility.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.PNPlannerAdapter
{
    /// <summary>
    /// Contains methods and utilities used for PNPlanner input xml creation.
    /// </summary>
    internal static class PNPlannerUtility
    {
        /// <summary>
        /// Evaluates whether fast forwarding is supported for a given interface submodule.
        /// </summary>
        /// <remarks>
        /// Fast Forwarding is supported if PNIrtSwitchBridgingDelayFFW is set and > 0
        /// AND if it is a controller, FFWMode = "All" OR  have just one activated
        /// port and have FFWMode = "OnePort".
        /// </remarks>
        /// <param name="interfaceSubmodule">The interface submodule to be checked.</param>
        /// <returns>True if fast forwarding is supported; false otherwise.</returns>
        internal static bool IsFFWSupported(Interface interfaceSubmodule)
        {
            // Check the advanced startup mode first since it is a prerequisite of this feature.
            List<PNIrtArStartupMode> startupModes = Utility.GetSupportedIrtArStartupModes(interfaceSubmodule);
            if ((startupModes == null)
                || (startupModes.Count == 0)
                || !startupModes.Contains(PNIrtArStartupMode.Advanced))
            {
                return false;
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            // Check if ffw supported by existence and the value of 
            // PNIrtSwitchBridgingDelayFFW
            if (AttributeUtilities.IsIDevice(interfaceSubmodule)
                || !Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
            {
                if (
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtSwitchBridgingDelayFFW,
                        ac.GetNew(),
                        0) == 0)
                {
                    return false;
                }
                return true;
            }
            if (Utility.IsProfinetControllerInterfaceSubmodule(interfaceSubmodule))
            {
                if (
                    interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnIrtSwitchBridgingDelayFFWIOC,
                        ac.GetNew(),
                        0) == 0)
                {
                    return false;
                }
            }

            // Check FFW mode if the interface is a controller interface
            PNIrtFfwMode ffwMode =
                (PNIrtFfwMode)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtFFWMode,
                    ac.GetNew(),
                    (uint)PNIrtFfwMode.None);
            switch (ffwMode)
            {
                case PNIrtFfwMode.None:
                    // If ffwmode is not supported, ffw cannot be activated.
                    return false;
                case PNIrtFfwMode.OnePort:
                    // Check if only one port is active.
                    IList<Port> ports = interfaceSubmodule.GetPorts();
                    int numActivePorts = 0;
                    foreach (Port port in ports)
                    {
                        if (
                            !port.AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnPortDeactivated,
                                ac.GetNew(),
                                false))
                        {
                            numActivePorts++;
                        }
                        if (numActivePorts > 1)
                        {
                            return false;
                        }
                    }
                    break;
                case PNIrtFfwMode.All:
                    break;
            }
            return true;
        }
        internal static bool IsPNPlannerSuccess(Interface interfaceSubmodule)
        {
            return interfaceSubmodule.SyncDomain.SyncDomainBusinessLogic.IsPNPlannerSuccessful;
        }
    }
}