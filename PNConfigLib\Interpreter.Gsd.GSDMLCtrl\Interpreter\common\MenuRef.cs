/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: MenuRef.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The MenuRef objects are referenced in MenuData objects.
    /// </summary>
    public class MenuRef :
        GsdObject,
        GSDI.IMenuRef
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the AccessPoint if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public MenuRef()
        {
            m_MenuRefID = String.Empty;
            m_Name = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_MenuRefID;
        private string m_Name;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the human readable, language specific id of the menu data
        /// object.
        /// </summary>
        public string MenuRefID
        {
            get => this.m_MenuRefID;
            set => m_MenuRefID = value;
        }

        /// <summary>
        /// Accesses the human readable, language specific name of the menu data
        /// object.
        /// </summary>
        public string Name
        {
            get => this.m_Name;
            set => m_Name = value;
        }

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successful, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.
                string member = Models.s_FieldMenuRefId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_MenuRefID = hash[member] as string;

                member = Models.s_FieldName;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Name = hash[member] as string;

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectMenuRef);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successful, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldMenuRefId, this.m_MenuRefID);
            Export.WriteStringProperty(ref writer, Models.s_FieldName, this.m_Name);

            return true;
        }


        #endregion

        //########################################################################################
        #region Object members
        public override bool Equals(Object obj)
        {
            if (obj == null)
                return false;

            if (obj.GetType() != typeof(MenuRef))
                return false;

            MenuRef menu1 = this;
            MenuRef menu2 = obj as MenuRef;

            if (menu2 == null)
            {
                return false;
            }
            if (menu1.MenuRefID != menu2.MenuRefID)
                return false;

            if (menu1.Name != menu2.Name)
                return false;

            return true;
        }

        public override int GetHashCode()
        {
            int hash = 23;

            hash = hash * 17 + MenuRefID.GetHashCode();
            hash = hash * 17 + Name.GetHashCode();

            return hash;
        }
        #endregion

    }
}
