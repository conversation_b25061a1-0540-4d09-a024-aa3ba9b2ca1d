/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: ValueListHelper.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Xml.Linq;

using GSDI;

using PNConfigLib.Gsd.Interpreter.Checker;
using System.Linq;

#endregion

namespace PNConfigLib.Gsd.Interpreter
{
    internal class ValueListHelper
    {

        //########################################################################################
        #region Nested Classes
        // Contains all non-public nested classes and locally scoped interface definitions


        /// <summary>
        /// Struct for value ranges
        /// </summary>
        public struct ValueRangeT : IEquatable<ValueRangeT>
        {
            public uint From;
            public uint To;

            public bool Equals(ValueRangeT other)
            {
                return From == other.From &&
                       To == other.To;
            }
        }

        public struct Int64ValueRangeT : IEquatable<Int64ValueRangeT>
        {
            public Int64 From;
            public Int64 To;

            public bool Equals(Int64ValueRangeT other)
            {
                return From == other.From &&
                       To == other.To;
            }
        }

        public struct UInt64ValueRangeT : IEquatable<UInt64ValueRangeT>
        {
            public UInt64 From;
            public UInt64 To;
            public bool Equals(UInt64ValueRangeT other)
            {
                return From == other.From &&
                              To == other.To;
            }
        }


        public struct Float32ValueRangeT : IEquatable<Float32ValueRangeT>
        {
            public float From;
            public float To;
            public bool Equals(Float32ValueRangeT other)
            {
                return From == other.From &&
                       To == other.To;
            }
        }

        public struct Float64ValueRangeT : IEquatable<Float64ValueRangeT>
        {
            public double From;
            public double To;

            public bool Equals(Float64ValueRangeT other)
            {
                return From == other.From &&
                       To == other.To;
            }

        }

        public class ValueRangeComparer : IComparer<ValueRangeT>
        {
            public int Compare(ValueRangeT x, ValueRangeT y)
            {
                if (x.From < y.From) return -1;
                if (x.From > y.From) return 1;
                if (x.To < y.To) return -1;
                if (x.To > y.To) return 1;
                return 0;
            }
        }

        public class ValueRangeComparerInt64 : IComparer<Int64ValueRangeT>
        {
            public int Compare(Int64ValueRangeT x, Int64ValueRangeT y)
            {
                if (x.From < y.From) return -1;
                if (x.From > y.From) return 1;
                if (x.To < y.To) return -1;
                if (x.To > y.To) return 1;
                return 0;
            }
        }

        public class ValueRangeComparerUInt64 : IComparer<UInt64ValueRangeT>
        {
            public int Compare(UInt64ValueRangeT x, UInt64ValueRangeT y)
            {
                if (x.From < y.From) return -1;
                if (x.From > y.From) return 1;
                if (x.To < y.To) return -1;
                if (x.To > y.To) return 1;
                return 0;
            }
        }

        public class ValueRangeComparerFloat32 : IComparer<Float32ValueRangeT>
        {
            public int Compare(Float32ValueRangeT x, Float32ValueRangeT y)
            {
                if (x.From < y.From) return -1;
                if (x.From > y.From) return 1;
                if (x.To < y.To) return -1;
                if (x.To > y.To) return 1;
                return 0;
            }
        }

        public class ValueRangeComparerFloat64 : IComparer<Float64ValueRangeT>
        {
            public int Compare(Float64ValueRangeT x, Float64ValueRangeT y)
            {
                if (x.From < y.From) return -1;
                if (x.From > y.From) return 1;
                if (x.To < y.To) return -1;
                if (x.To > y.To) return 1;
                return 0;
            }
        }

        public enum NormalizeResult
        {
            OK,                 // everything is fine
            NumericOverflow,    // too many decimal digits
            InvalidRange,       // min value > max value
            Overlap             // values and/or value ranges overlap
        }

        #endregion

        //########################################################################################
        #region Fields

        protected List<XAttribute> NodesWithError2 = new();

        /// <summary>
        /// Regex object for unsigned value list (e.g. "1..5 7 10 12..20")
        /// </summary>
        public static readonly Regex RegularExpressionUnsignedValueList = new(Constants.s_RegExUnsignedValueList, RegexOptions.IgnoreCase);

        #endregion

        //########################################################################################
        #region Properties

        protected List<XAttribute> NodesWithError => NodesWithError2;

        #endregion

        public static NormalizeResult NormalizeValueListInt64(string valueList, out List<Int64ValueRangeT> valueRanges)
        {
            valueRanges = new List<Int64ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return NormalizeResult.OK;
            }
            String pattern = "^((-?[0-9]+\\.\\.-?[0-9]+)|(-?[0-9]+))(( -?[0-9]+\\.\\.-?[0-9]+)|( -?[0-9]+))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return NormalizeResult.NumericOverflow;
            }

            if (NormalizeValueListInt64ValueOrRange(valueList, ref valueRanges, out NormalizeResult normalizeValueListInt64))
            {
                return normalizeValueListInt64;
            }

            valueRanges.Sort(new ValueRangeComparerInt64());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges = null;
                    return NormalizeResult.Overlap;
                }
                // immediately following?
                if (valueRanges[currentRange].To + 1 == valueRanges[currentRange + 1].From)
                {
                    // join the two ranges into one
                    Int64ValueRangeT vr = valueRanges[currentRange];
                    vr.To = valueRanges[currentRange + 1].To;
                    valueRanges[currentRange] = vr;
                    valueRanges.RemoveAt(currentRange + 1);
                }
                else currentRange++;
            }
            return NormalizeResult.OK;
        }

        private static bool NormalizeValueListInt64ValueOrRange(
            string valueList,
            ref List<Int64ValueRangeT> valueRanges,
            out NormalizeResult normalizeValueListInt64)
        {
            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                Int64ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    if (!Int64.TryParse(values[0], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From)
                        || !Int64.TryParse(values[1], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.To))
                    {
                        valueRanges = null;
                        {
                            normalizeValueListInt64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListInt64 = NormalizeResult.InvalidRange;
                            return true;
                        }
                    }
                }
                else
                {
                    // Single value
                    if (!Int64.TryParse(valueOrRange, NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From))
                    {
                        valueRanges = null;
                        {
                            normalizeValueListInt64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    vr.To = vr.From;
                }

                valueRanges.Add(vr);
            }
            normalizeValueListInt64 = NormalizeResult.OK;
            return false;
        }

        public static NormalizeResult NormalizeValueListUInt64(string valueList, out List<UInt64ValueRangeT> valueRanges)
        {
            valueRanges = new List<UInt64ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return NormalizeResult.OK;
            }
            // ((\d+\.\.\d+)|(\d+))(( \d+\.\.\d+)|( \d+))*

            String pattern = "^(([0-9]+\\.\\.[0-9]+)|([0-9]+))(( [0-9]+\\.\\.[0-9]+)|( [0-9]+))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return NormalizeResult.NumericOverflow;
            }

            if (NormalizeValueListUInt64ValueOrRange(valueList, ref valueRanges, out NormalizeResult normalizeValueListUInt64))
            {
                return normalizeValueListUInt64;
            }

            valueRanges.Sort(new ValueRangeComparerUInt64());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges = null;
                    return NormalizeResult.Overlap;
                }
                // immediately following?
                if (valueRanges[currentRange].To + 1 == valueRanges[currentRange + 1].From)
                {
                    // join the two ranges into one
                    UInt64ValueRangeT vr = valueRanges[currentRange];
                    vr.To = valueRanges[currentRange + 1].To;
                    valueRanges[currentRange] = vr;
                    valueRanges.RemoveAt(currentRange + 1);
                }
                else currentRange++;
            }
            return NormalizeResult.OK;
        }

        private static bool NormalizeValueListUInt64ValueOrRange(
            string valueList,
            ref List<UInt64ValueRangeT> valueRanges,
            out NormalizeResult normalizeValueListUInt64)
        {
            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                UInt64ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    if (!UInt64.TryParse(values[0], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From)
                        || !UInt64.TryParse(values[1], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.To))
                    {
                        valueRanges = null;
                        {
                            normalizeValueListUInt64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListUInt64 = NormalizeResult.InvalidRange;
                            return true;
                        }
                    }
                }
                else
                {
                    // Single value
                    if (!UInt64.TryParse(valueOrRange, NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From))
                    {
                        valueRanges = null;
                        {
                            normalizeValueListUInt64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    vr.To = vr.From;
                }

                valueRanges.Add(vr);
            }

            normalizeValueListUInt64 = NormalizeResult.OK;
            return false;
        }

        public static NormalizeResult NormalizeValueListFloat32(string valueList, out List<Float32ValueRangeT> valueRanges)
        {
            valueRanges = new List<Float32ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return NormalizeResult.OK;
            }
            // "((-?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|(-?\d+(\.\d+)?([eE]-?\d+)?))(( -?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|( -?\d+(\.\d+)?([eE]-?\d+)?))*"
            String pattern = "^((-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|(-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))(( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return NormalizeResult.NumericOverflow;
            }

            if (NormalizeValueListFloat32ValuesOrRanges(valueList, ref valueRanges, out NormalizeResult normalizeValueListFloat32))
            {
                return normalizeValueListFloat32;
            }

            valueRanges.Sort(new ValueRangeComparerFloat32());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges = null;
                    return NormalizeResult.Overlap;
                }
                else currentRange++;
            }
            return NormalizeResult.OK;
        }

        private static bool NormalizeValueListFloat32ValuesOrRanges(
            string valueList,
            ref List<Float32ValueRangeT> valueRanges,
            out NormalizeResult normalizeValueListFloat32)
        {
            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                Float32ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    try
                    {
                        vr.From = XmlConvert.ToSingle(values[0]);
                        vr.To = XmlConvert.ToSingle(values[1]);

                        ThrowExceptionIfValueRangeFloat32IsInfinity(vr.From);
                        ThrowExceptionIfValueRangeFloat32IsInfinity(vr.To);

                    }
                    catch (OverflowException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat32 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }
                    catch (FormatException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat32 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat32 = NormalizeResult.InvalidRange;
                            return true;
                        }
                    }
                }
                else
                {
                    // Single value
                    try
                    {
                        vr.From = XmlConvert.ToSingle(valueOrRange);

                        ThrowExceptionIfValueRangeFloat32IsInfinity(vr.From);

                    }
                    catch (OverflowException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat32 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }
                    catch (FormatException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat32 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    vr.To = vr.From;
                }

                valueRanges.Add(vr);
            }

            normalizeValueListFloat32 = NormalizeResult.OK;
            return false;
        }

        private static void ThrowExceptionIfValueRangeFloat32IsInfinity(float vr)
        {
            if (float.IsInfinity(vr))
                throw new OverflowException();
        }

        public static NormalizeResult NormalizeValueListFloat64(string valueList, out List<Float64ValueRangeT> valueRanges)
        {
            valueRanges = new List<Float64ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return NormalizeResult.OK;
            }
            // "((-?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|(-?\d+(\.\d+)?([eE]-?\d+)?))(( -?\d+(\.\d+)?([eE]-?\d+)?\.\.-?\d+(\.\d+)?([eE]-?\d+)?)|( -?\d+(\.\d+)?([eE]-?\d+)?))*"
            String pattern = "^((-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|(-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))(( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?\\.\\.-?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?)|( -?[0-9]+(\\.[0-9]+)?([eE]-?[0-9]+)?))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return NormalizeResult.NumericOverflow;
            }

            if (NormalizeValueListFloat64ValuesOrRanges(valueList, ref valueRanges, out NormalizeResult normalizeValueListFloat64))
            {
                return normalizeValueListFloat64;
            }

            valueRanges.Sort(new ValueRangeComparerFloat64());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges = null;
                    return NormalizeResult.Overlap;
                }
                else currentRange++;
            }
            return NormalizeResult.OK;
        }

        private static bool NormalizeValueListFloat64ValuesOrRanges(
            string valueList,
            ref List<Float64ValueRangeT> valueRanges,
            out NormalizeResult normalizeValueListFloat64)
        {
            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                Float64ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    try
                    {
                        vr.From = XmlConvert.ToDouble(values[0]);
                        vr.To = XmlConvert.ToDouble(values[1]);

                        ThrowExceptionIfValueRangeDoubleIsInfinity(vr.From);
                        ThrowExceptionIfValueRangeDoubleIsInfinity(vr.To);

                    }
                    catch (OverflowException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }
                    catch (FormatException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat64 = NormalizeResult.InvalidRange;
                            return true;
                        }
                    }
                }
                else
                {
                    // Double value
                    try
                    {
                        vr.From = XmlConvert.ToDouble(valueOrRange);

                        ThrowExceptionIfValueRangeDoubleIsInfinity(vr.From);

                    }
                    catch (OverflowException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }
                    catch (FormatException)
                    {
                        valueRanges = null;
                        {
                            normalizeValueListFloat64 = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    vr.To = vr.From;
                }

                valueRanges.Add(vr);
            }

            normalizeValueListFloat64 = NormalizeResult.OK;
            return false;
        }

        private static void ThrowExceptionIfValueRangeDoubleIsInfinity(double vr)
        {
            if (double.IsInfinity(vr))
                throw new OverflowException();
        }

        public ValueListHelper()
        {
            NodesWithError2 = new List<XAttribute>();
        }

        public List<ValueRangeT> NormalizeValueList(XAttribute node, ReportStore store)
        {
            if (null == node)
                return new List<ValueRangeT>();

            string msg;
            string xpath;

            var lineInfo = (IXmlLineInfo)node;

            NormalizeResult res = NormalizeValueList(node.Value, out List<ValueRangeT> valueRanges);
            switch (res)
            {
                case NormalizeResult.NumericOverflow:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The value of the attribute '{0} = {1}' cannot be converted into the given data type (too many digits?)."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_NumericOverflow"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "NumericOverflow");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<ValueRangeT>();
                    break;
                case NormalizeResult.InvalidRange:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains to a range with a minimum value greater than the maximum value."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_InvalidRange"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "InvalidRange");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<ValueRangeT>();
                    break;
                case NormalizeResult.Overlap:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains values and/or value ranges which overlap."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_Overlap"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "Overlap");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<ValueRangeT>();
                    break;
            }

            return valueRanges;
        }

        public List<Int64ValueRangeT> NormalizeValueListInt64(XAttribute node, ReportStore store)
        {
            if (null == node)
                return new List<Int64ValueRangeT>();

            string msg;
            string xpath;

            var lineInfo = (IXmlLineInfo)node;

            NormalizeResult res = NormalizeValueListInt64(node.Value, out List<Int64ValueRangeT> valueRanges);
            switch (res)
            {
                case NormalizeResult.NumericOverflow:
                    if (!NodesWithError.Contains(node))  // Has an error already been reported for the XmlAttribute?
                    {
                        // "The value of the attribute '{0} = {1}' cannot be converted into the given data type (too many digits?)."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_NumericOverflow"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "NumericOverflow");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Int64ValueRangeT>();
                    break;
                case NormalizeResult.InvalidRange:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains to a range with a minimum value greater than the maximum value."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_InvalidRange"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "InvalidRange");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Int64ValueRangeT>();
                    break;
                case NormalizeResult.Overlap:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains values and/or value ranges which overlap."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_Overlap"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                            ReportCategories.TypeSpecific, "Overlap");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Int64ValueRangeT>();
                    break;
            }

            return valueRanges;
        }

        public List<UInt64ValueRangeT> NormalizeValueListUInt64(XAttribute node, ReportStore store)
        {
            if (null == node)
                return new List<UInt64ValueRangeT>();

            string msg;
            string xpath;

            var lineInfo = (IXmlLineInfo)node;

            NormalizeResult res = NormalizeValueListUInt64(node.Value, out List<UInt64ValueRangeT> valueRanges);
            switch (res)
            {
                case NormalizeResult.NumericOverflow:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The value of the attribute '{0} = {1}' cannot be converted into the given data type (too many digits?)."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_NumericOverflow"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "NumericOverflow");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<UInt64ValueRangeT>();
                    break;
                case NormalizeResult.InvalidRange:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains to a range with a minimum value greater than the maximum value."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_InvalidRange"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "InvalidRange");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<UInt64ValueRangeT>();
                    break;
                case NormalizeResult.Overlap:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains values and/or value ranges which overlap."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_Overlap"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "Overlap");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<UInt64ValueRangeT>();
                    break;
            }

            return valueRanges;
        }

        public List<Float32ValueRangeT> NormalizeValueListFloat32(XAttribute node, ReportStore store)
        {
            if (null == node)
                return new List<Float32ValueRangeT>();

            string msg;
            string xpath;

            var lineInfo = (IXmlLineInfo)node;

            NormalizeResult res = NormalizeValueListFloat32(node.Value, out List<Float32ValueRangeT> valueRanges);
            switch (res)
            {
                case NormalizeResult.NumericOverflow:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The value of the attribute '{0} = {1}' cannot be converted into the given data type (too many digits?)."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_NumericOverflow"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "NumericOverflow");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float32ValueRangeT>();
                    break;
                case NormalizeResult.InvalidRange:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains to a range with a minimum value greater than the maximum value."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_InvalidRange"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "InvalidRange");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float32ValueRangeT>();
                    break;
                case NormalizeResult.Overlap:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains values and/or value ranges which overlap."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_Overlap"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "Overlap");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float32ValueRangeT>();
                    break;
            }

            return valueRanges;
        }


        public List<Float64ValueRangeT> NormalizeValueListFloat64(XAttribute node, ReportStore store)
        {
            if (null == node)
                return new List<Float64ValueRangeT>();

            string msg;
            string xpath;

            var lineInfo = (IXmlLineInfo)node;

            NormalizeResult res = NormalizeValueListFloat64(node.Value, out List<Float64ValueRangeT> valueRanges);
            switch (res)
            {
                case NormalizeResult.NumericOverflow:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The value of the attribute '{0} = {1}' cannot be converted into the given data type (too many digits?)."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_NumericOverflow"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "NumericOverflow");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float64ValueRangeT>();
                    break;
                case NormalizeResult.InvalidRange:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains to a range with a minimum value greater than the maximum value."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_InvalidRange"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "InvalidRange");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float64ValueRangeT>();
                    break;
                case NormalizeResult.Overlap:
                    if (!NodesWithError.Contains(node))  // Has an error been reported for the XAttribute yet?
                    {
                        // "The attribute '{0} = {1}' contains values and/or value ranges which overlap."
                        msg = String.Format(CultureInfo.CurrentCulture, Help.GetMessageString("M_Overlap"), node.Name.LocalName, node.Value);
                        xpath = Help.GetXPath(node);
                        store.CreateAndAnnounceReport(GSDI.ReportTypes.GSD_RT_Error, lineInfo.LineNumber, lineInfo.LinePosition, msg, xpath,
                                                      ReportCategories.TypeSpecific, "Overlap");
                        NodesWithError.Add(node);
                    }
                    valueRanges = new List<Float64ValueRangeT>();
                    break;
            }

            return valueRanges;
        }

        public static NormalizeResult NormalizeValueList(string valueList, out List<ValueRangeT> valueRanges)
        {
            valueRanges = new List<ValueRangeT>();
            // XmlElement.GetAttribute() returns empty string if attribute not present
            if (String.IsNullOrEmpty(valueList))
            {
                // return empty array in this case for convenience
                return NormalizeResult.OK;
            }
            // ((\d+\.\.\d+)|(\d+))(( \d+\.\.\d+)|( \d+))*

            String pattern = "^(([0-9]+\\.\\.[0-9]+)|([0-9]+))(( [0-9]+\\.\\.[0-9]+)|( [0-9]+))*$";
            Match digitMatch = Regex.Match(valueList, pattern);
            if (!digitMatch.Success)
            {
                return NormalizeResult.NumericOverflow;
            }

            if (NormalizeValueListValuesOrRanges(valueList, ref valueRanges, out NormalizeResult normalizeValueList))
            {
                return normalizeValueList;
            }

            valueRanges.Sort(new ValueRangeComparer());
            int currentRange = 0;
            while ((currentRange + 1) < valueRanges.Count)
            {
                if (valueRanges[currentRange].To >= valueRanges[currentRange + 1].From)
                {
                    // This check also guarantees that ValueRange[CurrentRange].to is _smaller_ than
                    // uint.MaxValue, so ValueRange[CurrentRange].to + 1 in the next if statement can't overflow!
                    valueRanges = null;
                    return NormalizeResult.Overlap;
                }
                // immediately following?
                if (valueRanges[currentRange].To + 1 == valueRanges[currentRange + 1].From)
                {
                    // join the two ranges into one
                    ValueRangeT vr = valueRanges[currentRange];
                    vr.To = valueRanges[currentRange + 1].To;
                    valueRanges[currentRange] = vr;
                    valueRanges.RemoveAt(currentRange + 1);
                }
                else currentRange++;
            }
            return NormalizeResult.OK;
        }

        private static bool NormalizeValueListValuesOrRanges(
            string valueList,
            ref List<ValueRangeT> valueRanges,
            out NormalizeResult normalizeValueList)
        {
            string[] valuesOrRanges = valueList.Split(" ".ToCharArray());
            foreach (string valueOrRange in valuesOrRanges)
            {
                ValueRangeT vr;
                if (valueOrRange.Contains(".."))
                {
                    // Range
                    string[] values = valueOrRange.Split(new string[] { ".." }, StringSplitOptions.None);
                    if (!uint.TryParse(values[0], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From)
                        || !uint.TryParse(values[1], NumberStyles.Any, CultureInfo.InvariantCulture, out vr.To))
                    {
                        valueRanges = null;
                        {
                            normalizeValueList = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    if (vr.From > vr.To)
                    {
                        valueRanges = null;
                        {
                            normalizeValueList = NormalizeResult.InvalidRange;
                            return true;
                        }
                    }
                }
                else
                {
                    // Single value
                    if (!uint.TryParse(valueOrRange, NumberStyles.Any, CultureInfo.InvariantCulture, out vr.From))
                    {
                        valueRanges = null;
                        {
                            normalizeValueList = NormalizeResult.NumericOverflow;
                            return true;
                        }
                    }

                    vr.To = vr.From;
                }

                valueRanges.Add(vr);
            }

            normalizeValueList = NormalizeResult.OK;
            return false;
        }

        /// <summary>
        /// Separates a string of combined unsigned values to a list of valid numbers.
        /// </summary>
        /// <exception cref="ArgumentNullException">is thrown if parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an invalid or empty value list string</exception>
        /// <returns>List of valid unsigned (32 bit) numbers.</returns>
        public List<uint> SeparateUnsignedValueList(XAttribute an, ReportStore store)
        {
            #region Argument Preconditions

            if (an == null)
                throw new ArgumentNullException(nameof(an));
            if (store == null)
                throw new ArgumentNullException(nameof(store));

            #endregion

            // Split incoming string to individual numbers in a list.
            List<ValueRangeT> splitlist = NormalizeValueList(an, store);
            List<uint> valuelist = new();

            for (int currentRange = 0; currentRange < splitlist.Count; currentRange++)
            {
                for (uint ui = splitlist[currentRange].From; ui <= splitlist[currentRange].To; ui++)
                {
                    valuelist.Add(ui);
                }
            }

            return valuelist;
        }

        /// <summary>
        /// Separates a string of combined unsigned values to a list of valid numbers.
        /// </summary>
        /// <param name="values">String of combined unsigned values.</param>
        /// <returns>List of valid unsigned (32 bit) numbers.</returns>
        public static List<uint> SeparateUnsignedValueList(string values)
        {
            #region Argument Preconditions

            SeparateUnsignedValueListValidataValues(values);

            #endregion

            List<string> splitlist =
                // Split incoming string to individual numbers in a list.
                new(values.Split(Constants.s_Space.ToCharArray()));
            List<uint> valuelist = new();

            foreach (string s in splitlist)
            {
                int index = s.IndexOf(Constants.s_DoubleDot, StringComparison.Ordinal);
                if (index != -1)
                {
                    SeparateAndAddValues(s, index, valuelist);
                }
                else
                {
                    if (!UInt32.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out UInt32 val))
                    {
                        continue;
                    }

                    if (!valuelist.Contains(val))
                    {
                        valuelist.Add(val);
                    }
                }
            }
            valuelist.TrimExcess();
            valuelist.Sort();


            return valuelist;
        }

        private static void SeparateAndAddValues(string s, int index, ICollection<uint> valuelist)
        {
            // Area, separate area and add values.
            string ss1 = s.Substring(0, index);
            string ss2 = s.Substring(index + 2);
            if (!UInt32.TryParse(ss1, NumberStyles.Any, CultureInfo.InvariantCulture, out uint v1)
                || !UInt32.TryParse(ss2, NumberStyles.Any, CultureInfo.InvariantCulture, out uint v2))
            {
                return;
            }

            if (v1 > v2)
            {
                uint vtemp = v1;
                v1 = v2;
                v2 = vtemp;
            }

            for (uint i = v1; i <= v2; i++)
            {
                if (!valuelist.Contains(i))
                {
                    valuelist.Add(i);
                }
            }
        }

        private static void SeparateUnsignedValueListValidataValues(string values)
        {
            if (values == null)
                throw new ArgumentNullException(nameof(values));
            if (values.Length == 0) // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(values));

            // Check for unsigned value list (e.g. "1..5 7 10 12..20")
            if (!RegularExpressionUnsignedValueList.IsMatch(values))
                throw new ArgumentException("Input parameter is invalid unsigned value list string.", nameof(values));
        }

        /// <summary>
        /// Separates a string of combined unsigned values to a list of valid numbers. Additionally, given
        /// default values are also added to the result list.
        /// </summary>
        /// <param name="values">String of combined unsigned values.</param>
        /// <param name="defaultvalues">List of default values, which must be contained in the result list.</param>
        /// <exception cref="ArgumentNullException">is thrown if values parameter is a null reference</exception>
        /// <exception cref="ArgumentException">is thrown, in case of an invalid or empty value list string</exception>
        /// <returns>List of valid unsigned (32 bit) numbers.</returns>
        public static List<uint> SeparateUnsignedValueList(string values, uint[] defaultvalues)
        {
            #region Argument Preconditions

            if (values == null)
                throw new ArgumentNullException(nameof(values));
            if (values.Length == 0)  // -> empty string
                throw new ArgumentException("The parameter can not be empty.", nameof(values));

            // Check for unsigned value list (e.g. "1..5 7 10 12..20")
            if (!RegularExpressionUnsignedValueList.IsMatch(values))
                throw new ArgumentException("Input parameter is invalid unsigned value list string.", nameof(values));

            #endregion

            // Split incoming string to individual numbers in a list.
            List<uint>  resultlist = SeparateUnsignedValueList(values);

            // Add given default values, if they aren't contained.
            if (defaultvalues == null)
                return resultlist;	// ---------->

            foreach (uint v in defaultvalues)
            {
                if (!resultlist.Contains(v))
                    resultlist.Add(v);
            }
            resultlist.TrimExcess();
            resultlist.Sort();

            return resultlist;
        }

        /// <summary>
        /// Checks if an uint value is contained in a list of ranges.
        /// </summary>
        /// <param name="subslot"></param>
        /// <param name="slotrange"></param>
        /// <returns>true if the value is contained in list of value ranges.</returns>
        public static bool IsValueInValueList(uint value, IList<ValueRangeT> lattr)
        {
            List<ValueRangeT> valueList = (List<ValueRangeT>)lattr;

            valueList.Sort(new ValueRangeComparer());

            for (int currentRange = 0; currentRange < valueList.Count; currentRange++)
            {
                if (value < valueList[currentRange].From)

                    // The entry is greater than subslot and cannot be found any more, because the list is sorted.

                    break;

                if (value <= valueList[currentRange].To)

                    // Found! Subslot is in the range.

                    return true;
            }

            return false;
        }

        /// <summary>
        /// Checks if the values contained in a list of value ranges does not exceed a maximum value.
        /// </summary>
        /// <returns>true if the list of value ranges exceeds a maximum value.</returns>
        public static bool DoesValueListExceedsMaximum(IList<ValueRangeT> lattr, uint maxValue)
        {
            if (lattr == null || lattr.Count == 0)
                return false;

            List<ValueRangeT> valueList = (List<ValueRangeT>)lattr;

            valueList.Sort(new ValueRangeComparer());

            if (maxValue < valueList[valueList.Count - 1].To)

                // Found! Range exceeds maximum value.

                return true;

            return false;
        }

    }
}
