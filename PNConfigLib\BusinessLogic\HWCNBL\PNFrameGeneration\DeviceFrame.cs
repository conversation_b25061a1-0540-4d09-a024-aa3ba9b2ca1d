/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DeviceFrame.cs                            :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.PNFrameGeneration
{
    internal class ControllerFrameDataList
    {
        public ControllerFrameDataList(Dictionary<int, List<IPNFrameData>> pnFrameDataList)
        {
            PNFrameDataList = pnFrameDataList;
        }

        public Dictionary<int, List<IPNFrameData>> PNFrameDataList { get; }
    }

    internal class DeviceFrame
    {
        /// <summary>
        /// Dafault Value for IOCS Length
        /// </summary>
        private const int m_DefaultIOCSLength = 1;

        /// <summary>
        /// Dafault Value for IOPS Length
        /// </summary>
        private const int m_DefaultIOPSLength = 1;

        public DeviceFrame(PNIOD pnIOD)
        {
            PNIOD = pnIOD;
        }

        private PNIOD PNIOD { get; }

        public virtual void RefreshPNPlannerInputDataDevice(IMethodData methodData)
        {
            Interface controllerInterfaceSubmodule = null;

            int coreId = PNIOD.GetHashCode();
            int controllerCoreId;

            PNIOC ioController = methodData.Arguments[RefreshPNPlannerInputData.IOController] as PNIOC;

            if (ioController != null)
            {
                controllerCoreId = ioController.GetHashCode();

                controllerInterfaceSubmodule = NavigationUtilities.GetInterfaceFromConnector(ioController);
            }
            else
            {
                controllerCoreId = coreId;
            }

            Dictionary<int, List<IPNFrameData>> pnFrameDataList =
                methodData.Arguments[RefreshPNPlannerInputData.PNFrameDataList] as Dictionary<int, List<IPNFrameData>>;

            if (pnFrameDataList == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            Interface deviceInterfaceSubmodule = NavigationUtilities.GetInterfaceFromConnector(PNIOD);

            if (deviceInterfaceSubmodule == null)
            {
                return;
            }

            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput =
                ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();
            ParamObjectRefreshPNPlannerInput paramObjectRefreshSharedPNPlannerInput =
                ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();

            bool sharedDeviceSupported = false;
            if (methodData.Arguments[RefreshPNPlannerInputData.CtrlSharedDeviceAssignmentSupp] != null)
            {
                if ((bool)methodData.Arguments[RefreshPNPlannerInputData.CtrlSharedDeviceAssignmentSupp])
                {
                    sharedDeviceSupported =
                        deviceInterfaceSubmodule.GetDevice()
                            .AttributeAccess.GetAnyAttribute<bool>(
                                InternalAttributeNames.PnIoSharedDeviceSupported,
                                ac,
                                false);
                }
            }

            if (sharedDeviceSupported)
            {
                List<PclObject> sharedModules = SharedDeviceUtility.GetSharedSubmodules(deviceInterfaceSubmodule);

                foreach (PclObject module in sharedModules)
                {
                    Utility.GetAddressLengthOfDeviceItem(module, paramObjectRefreshSharedPNPlannerInput, 1, 1);
                }
            }

            paramObjectRefreshPNPlannerInput.SharedDeviceSupported = sharedDeviceSupported;

            GetFrameLengthsOfInterfaceSubmodule(deviceInterfaceSubmodule, paramObjectRefreshPNPlannerInput);

            GenerateFrameDataList(
                methodData,
                paramObjectRefreshPNPlannerInput,
                controllerInterfaceSubmodule,
                deviceInterfaceSubmodule,
                controllerCoreId,
                new ControllerFrameDataList(pnFrameDataList),
                paramObjectRefreshSharedPNPlannerInput);
        }

        /// <summary>
        /// Gets the send clock factor for a given controller.
        /// </summary>
        /// <param name="ioDevice">The IO device that is connected to the controller interface.</param>
        /// <param name="controllerInterfaceSubmodule">The controller interface whose send clock factor will be retrieved.</param>
        /// <param name="isExternalSendClock">Whether the send clock is external.</param>
        /// <returns>The send clock factor of controller.</returns>
        private static long GetControllerSendClockFactor(
            PNIOD ioDevice,
            Interface controllerInterfaceSubmodule,
            out bool isExternalSendClock)
        {
            isExternalSendClock = false;

            Interface deviceInterface = NavigationUtilities.GetInterfaceFromConnector(ioDevice);
            long sendClockFactor;

            AttributeAccessCode ac = new AttributeAccessCode();

            if ((SharedDeviceUtility.GetSharedAccess(deviceInterface) == SharedIoAssignment.NotAssigned)
                && !SharedDeviceUtility.IsParameterizationDisallowed(deviceInterface))
            {
                isExternalSendClock = true;
                sendClockFactor =
                    deviceInterface.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoExternalSendClockFactor,
                        ac,
                        32);
            }
            else
            {
                sendClockFactor = AttributeUtilities.GetTransientPNSendClockFactor(
                    controllerInterfaceSubmodule,
                    PNFunctionsDefaultAttributeValues.DefaultPNSendClockFactor);
            }

            return sendClockFactor;
        }

        /// <summary>
        /// Generates frame list related to multiple IOCR feature.
        /// </summary>
        /// <param name="md">MethodData object that keeps all the inputs, outputs and other related data used in this method.</param>
        /// <param name="paramObjectRefreshPNPlannerInput">The parameter object used in frame generation.</param>
        /// <param name="controllerInterfaceSubmodule">The controller that this IO device is connected to.</param>
        /// <param name="deviceInterfaceSubmodule">The IO device whose frames will be generated.</param>
        /// <param name="controllerCoreId">Core ID of the controller.</param>
        /// <param name="pnFrameDataList">The list that stores the PNFrameData objects.</param>
        /// <param name="paramObjectRefreshSharedPNPlannerInput">The parameter object used in shared device frame generation.</param>
        private void GenerateFrameDataList(
            IMethodData md,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput,
            Interface controllerInterfaceSubmodule,
            Interface deviceInterfaceSubmodule,
            int controllerCoreId,
            ControllerFrameDataList pnFrameDataList,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshSharedPNPlannerInput)
        {
            #region Collect specific parameters for creating PNFrameData objects

            Dictionary<string, ParamObjectRefreshPNPlannerInput> frameLengthColl;
            object tmp = md.Arguments[PrepareForPNPlanner.FrameLengthColl];
            if (tmp != null)
            {
                frameLengthColl = tmp as Dictionary<string, ParamObjectRefreshPNPlannerInput>;
            }
            else
            {
                frameLengthColl = new Dictionary<string, ParamObjectRefreshPNPlannerInput>();
                md.Arguments[PrepareForPNPlanner.FrameLengthColl] = frameLengthColl;
            }

            if (frameLengthColl != null)
            {
                frameLengthColl[PNIOD.GetHashCode().ToString(CultureInfo.InvariantCulture)] = paramObjectRefreshPNPlannerInput;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            if (PNIOD == null)
            {
                throw new PNFunctionsException("IODevice is null.");
            }
            
            int coreId = PNIOD.GetHashCode();

            PNIOFrameClass frameClass =
                (PNIOFrameClass)
                PNIOD.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIoFrameClass, ac, 0);

            ac.Reset();
            byte ioSyncRole = (byte)PNAttributeUtility.GetAdjustedSyncRole(PNIOD);
            long sendClockFactor;
            byte updateTimeMode;
            bool isExternalSendClock;
            long localReductionRatio = 1;

            GetUpdateTimeAttributes(
                PNIOD,
                controllerInterfaceSubmodule,
                frameClass,
                ref localReductionRatio,
                out updateTimeMode,
                out sendClockFactor,
                out isExternalSendClock);

            PNPlannerFrameType frameType = PNPlannerFrameType.DeviceFrame;

            // Get PNIoWatchdogFactor from IODevice
            ac.Reset();
            long watchdogFactor = PNIOD.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoWatchdogFactor,
                ac,
                0);

            // Get StationNumber of IODevice
            int stationNumber = AttributeUtilities.GetFirstPNStationNumber(PNIOD);

            // Get PNIoMinFrameIntFactor of deviceInterfaceSubmodule
            ac.Reset();
            long minFrameIntFactor =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoMinFrameIntFactor,
                    ac,
                    (int)PNFunctionsDefaultAttributeValues.DefaultPNMinFrameIntFactor);

            // Get the number of ARs (in this context, the number of external controllers) 
            // if shared device functionality is supported and IO Controller has full access to the Interface
            uint numberOfARs = SharedDeviceUtility.GetNumberOfARs(
                controllerInterfaceSubmodule,
                deviceInterfaceSubmodule);
            bool hasNotAssignedSubmodule =
                SharedDeviceUtility.HasNotAssignedSubmodule(deviceInterfaceSubmodule.GetDevice() as DecentralDevice);

            //Get SuppRRPow from deviceInterfaceSubmodule
            //If FrameClass = 1 or 2, get PNIoSuppRR12Pow2 of deviceInterfaceSubmodule
            //If FrameClass = 3, get PNIoSuppRR3Pow2 of deviceInterfaceSubmodule
            List<long> suppRRPow = PNAttributeUtility.GetSuppRRPowList(deviceInterfaceSubmodule, frameClass);

            //Get SuppRRNonPow from deviceInterfaceSubmodule
            //If FrameClass = 1 or 2, get PNIoSuppRR12NonPow2 of deviceInterfaceSubmodule
            //If FrameClass = 3, get PNIoSuppRR3NonPow2 of deviceInterfaceSubmodule
            List<long> suppRRNonPow = PNAttributeUtility.GetSuppRRNonPowList(deviceInterfaceSubmodule, frameClass);

            // Get PNFrameMultiplier of deviceInterfaceSubmodule
            ac.Reset();
            int frameMultiplier =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnFrameMultiplier,
                    ac,
                    PNFunctionsDefaultAttributeValues.DefaultPNFrameMultiplier);

            // Get ProxyNumber of the deviceInterfaceSubmodule 
            // If deviceInterfaceSubmodule is a proxy submodule, get its StationNumber
            // 0 otherwise
            int proxyNumber = 0;
            ac.Reset();
            bool isProxy =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsProxy,
                    ac,
                    false);
            if (isProxy)
            {
                proxyNumber = stationNumber;
            }

            ac.Reset();
            // Get SupportedSendClockFactors from deviceInterfaceSubmodule
            // If FrameClass = 1 or 2, get PNIoSuppSCF12 of deviceInterfaceSubmodule
            // If FrameClass = 3, get PNIoSuppSCF3 of deviceInterfaceSubmodule

            List<long> suppSCF = new List<long>();
            PNIOOperatingModes deviceOperatingMode =
                (PNIOOperatingModes)
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoOperatingMode,
                    ac,
                    (uint)PNIOOperatingModes.None);

            if (isExternalSendClock)
            {
                suppSCF.Add(
                    deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoExternalSendClockFactor,
                        ac.GetNew(),
                        PNConstants.DefaultSendClockFactor));
            }
            else if (IDeviceUtility.CanDeviceSupportMultipleSCF(deviceInterfaceSubmodule))
            {
                suppSCF = PNAttributeUtility.GetSupportedSendClockFactorList(deviceInterfaceSubmodule, frameClass);
            }
            else if ((deviceOperatingMode & PNIOOperatingModes.IOController) == PNIOOperatingModes.IOController)
            {
                // Needed workaround for CPUs which have to be synchronized with their own IDevice items
                suppSCF.Add(
                    deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoSendClockFactor,
                        ac.GetNew(),
                        PNConstants.DefaultSendClockFactor));
            }
            else
            {
                suppSCF.Add(sendClockFactor);
            }

            ac.Reset();
            int slotNo = deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.PositionNumber,
                ac,
                0);

            ac.Reset();
            int subslotNo =
                deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    ac,
                    0);

            // Default reduction ratio for unsynchronized frames
            int defaultRR = Utility.GetDefaultRRForUnsyncFrames(controllerInterfaceSubmodule);

            #endregion

            #region Create input PNFrameData object

            PNFrameData inputFrameData = new PNFrameData(coreId);
            inputFrameData.DataLength = paramObjectRefreshPNPlannerInput.InputGrossFrameLength;
            if (hasNotAssignedSubmodule && (paramObjectRefreshSharedPNPlannerInput != null))
            {
                inputFrameData.SharedDataLength = paramObjectRefreshSharedPNPlannerInput.InputGrossFrameLength;
            }
            else
            {
                inputFrameData.SharedDataLength = 0;
            }
            inputFrameData.FrameDirection = (byte)PNPlannerFrameDirection.InputFrame;
            inputFrameData.FrameClass = (long)frameClass;
            // Set PNPlannerFrameType = DeviceFrame or IDeviceFrame
            inputFrameData.FrameType = (long)frameType;
            inputFrameData.WatchdogFactor = watchdogFactor;
            // Set PNPlannerDataHoldFactor = PNIoWatchdogFactor
            inputFrameData.DataHoldFactor = watchdogFactor;
            inputFrameData.StationNumber = stationNumber;
            inputFrameData.MinFrameIntervall = minFrameIntFactor;
            inputFrameData.NumberOfARs = numberOfARs;
            inputFrameData.HasNotAssignedSubmodule = hasNotAssignedSubmodule;
            inputFrameData.SuppRRPow = suppRRPow;
            inputFrameData.SuppRRNonPow = suppRRNonPow;
            inputFrameData.HasUsingData = paramObjectRefreshPNPlannerInput.HasUsingData;
            inputFrameData.FrameMultiplier = frameMultiplier;
            // Set PNPlannerProxyNumber = the proxy number of deviceInterfaceSubmodule, if deviceInterfaceSubmodule is proxy
            inputFrameData.ProxyNumber = proxyNumber;
            inputFrameData.DeviceLocalReductionRatio = localReductionRatio;
            inputFrameData.IoSyncRole = ioSyncRole;
            inputFrameData.UpdateTimeMode = updateTimeMode;
            inputFrameData.SendClockFactor = sendClockFactor;
            inputFrameData.SuppSendClockFactors = suppSCF;
            inputFrameData.SlotNumber = slotNo;
            inputFrameData.SubSlotNumber = subslotNo;
            // MinAutomaticUnsyncUpdateTime is intentionally filled with 
            // defaultRR (not with defaultRR * sendClockFactor). It is wrong but Classic implementation is so.
            inputFrameData.MinAutomaticUnsyncUpdateTime = defaultRR;

            #endregion

            #region Create output PNFrameData object

            PNFrameData outputFrameData = new PNFrameData(coreId);
            outputFrameData.DataLength = paramObjectRefreshPNPlannerInput.OutputGrossFrameLength;
            if (hasNotAssignedSubmodule && (paramObjectRefreshSharedPNPlannerInput != null))
            {
                outputFrameData.SharedDataLength = paramObjectRefreshSharedPNPlannerInput.OutputGrossFrameLength;
            }
            else
            {
                outputFrameData.SharedDataLength = 0;
            }
            outputFrameData.FrameDirection = (byte)PNPlannerFrameDirection.OutputFrame;
            outputFrameData.FrameClass = (long)frameClass;
            // Set PNPlannerFrameType = DeviceFrame or IDeviceFrame                
            outputFrameData.FrameType = (long)frameType;
            outputFrameData.WatchdogFactor = watchdogFactor;
            // Set PNPlannerDataHoldFactor = PNIoWatchdogFactor
            outputFrameData.DataHoldFactor = watchdogFactor;
            outputFrameData.StationNumber = stationNumber;
            outputFrameData.MinFrameIntervall = minFrameIntFactor;
            outputFrameData.NumberOfARs = numberOfARs;
            outputFrameData.HasNotAssignedSubmodule = hasNotAssignedSubmodule;
            outputFrameData.SuppRRPow = suppRRPow;
            outputFrameData.SuppRRNonPow = suppRRNonPow;
            outputFrameData.HasUsingData = paramObjectRefreshPNPlannerInput.HasUsingData;
            outputFrameData.FrameMultiplier = frameMultiplier;
            // Set PNPlannerProxyNumber = the proxy number of deviceInterfaceSubmodule, if deviceInterfaceSubmodule is proxy
            outputFrameData.ProxyNumber = proxyNumber;
            outputFrameData.DeviceLocalReductionRatio = localReductionRatio;
            outputFrameData.IoSyncRole = ioSyncRole;
            outputFrameData.UpdateTimeMode = updateTimeMode;
            outputFrameData.SendClockFactor = sendClockFactor;
            outputFrameData.SuppSendClockFactors = suppSCF;
            outputFrameData.SlotNumber = slotNo;
            outputFrameData.SubSlotNumber = subslotNo;
            // MinAutomaticUnsyncUpdateTime is intentionally filled with 
            // defaultRR (not with defaultRR * sendClockFactor). It is wrong but Classic implementation is so.
            outputFrameData.MinAutomaticUnsyncUpdateTime = defaultRR;

            #endregion

            if (!pnFrameDataList.PNFrameDataList.ContainsKey(controllerCoreId))
            {
                pnFrameDataList.PNFrameDataList.Add(controllerCoreId, new List<IPNFrameData>());
            }

            pnFrameDataList.PNFrameDataList[controllerCoreId].Add(inputFrameData);
            pnFrameDataList.PNFrameDataList[controllerCoreId].Add(outputFrameData);
        }

        /// <summary>
        /// Gets the total input and output datalengths of a given device interface submodule.
        /// </summary>
        /// <remarks>
        /// This method finds all the address objects which are attached to modules and submodules of
        /// deviceInterfaceSubmodule, then calculates from address objects the total input and output data lengths.
        /// </remarks>
        /// <param name="decentralDeviceInterfaceSubmodule">The device inteface hose frame lengths will be retrieved.</param>
        /// <param name="paramObjectRefreshPNPlannerInput">The parameter object used in frame generation.</param>
        private void GetFrameLengthsOfInterfaceSubmodule(
            Interface decentralDeviceInterfaceSubmodule,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput)
        {
            paramObjectRefreshPNPlannerInput.DiscardIOXS = !Utility.GetIOXSRequired(decentralDeviceInterfaceSubmodule);

            AttributeAccessCode ac = new AttributeAccessCode();

            List<PclObject> modules =
                PNNavigationUtility.GetModulesFromInterfaceSubmodule(decentralDeviceInterfaceSubmodule);

            foreach (PclObject module in modules)
            {
                int iocsLengthOfModule = module.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIOCSLength,
                    ac.GetNew(),
                    m_DefaultIOCSLength);

                int iopsLengthOfModule = module.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIOPSLength,
                    ac.GetNew(),
                    m_DefaultIOPSLength);

                Utility.GetAddressLengthOfDeviceItem(
                    module,
                    paramObjectRefreshPNPlannerInput,
                    iocsLengthOfModule,
                    iopsLengthOfModule);
            }
        }

        /// <summary>
        /// Gets and/or calculates attributes related to update time for a given IO device.
        /// </summary>
        /// <param name="ioDevice">The IO device whose update time attributes will be retrieved.</param>
        /// <param name="controllerInterfaceSubmodule">The controller interface that ioDevice is connected to.</param>
        /// <param name="frameClass">Class of the frame.</param>
        /// <param name="localReductionRatio">Local reduction ratio.</param>
        /// <param name="updateTimeMode">Update time mode of IO device.</param>
        /// <param name="deviceSendClockFactor">The send clock factor of IO device.</param>
        /// <param name="isExternalSendClock">Whether the send clock is external.</param>
        private void GetUpdateTimeAttributes(
            PNIOD ioDevice,
            Interface controllerInterfaceSubmodule,
            PNIOFrameClass frameClass,
            ref long localReductionRatio,
            out byte updateTimeMode,
            out long deviceSendClockFactor,
            out bool isExternalSendClock)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            long controllerSendClockFactor = GetControllerSendClockFactor(
                ioDevice,
                controllerInterfaceSubmodule,
                out isExternalSendClock);

            deviceSendClockFactor = controllerSendClockFactor;

            // Get updateTimeMode
            // PNIO-TT #730: RTC3 Frames cannot have Automatic Time Mode. Display the time mode still as Automatic 
            // to user, but use FixedReduction with RR = 1
            byte origUpdateTimeMode =
                ioDevice.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnUpdateTimeMode,
                    ac.GetNew(),
                    PNFunctionsDefaultAttributeValues.DefaultPNUpdateTimeMode);

            updateTimeMode = frameClass == PNIOFrameClass.Class3Frame
                                 ? (byte)PNUpdateTimeMode.FixedReduction
                                 : origUpdateTimeMode;

            if (origUpdateTimeMode == (byte)PNUpdateTimeMode.FixedReduction)
            {
                localReductionRatio =
                    ioDevice.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                        ac.GetNew(),
                        1);
            }

            if (origUpdateTimeMode == (byte)PNUpdateTimeMode.FixedTime)
            {
                AttributeAccessCode acUpdateTime = new AttributeAccessCode();
                long userAdjustedUpdateTime =
                    ioDevice.AttributeAccess.GetAnyAttribute<long>(
                        InternalAttributeNames.PnIoUserAdjustedUpdTime,
                        acUpdateTime,
                        32);

                // If External Send Clock exists and UpdateTime mode is FixedTime then deviceSendClockFactor should be 
                // equal to external scf and deviceLocalRR should be calculated according to selected update time and deviceSCF.
                if (isExternalSendClock
                    && (userAdjustedUpdateTime > deviceSendClockFactor)
                    && (deviceSendClockFactor > 0))
                {
                    localReductionRatio = userAdjustedUpdateTime / deviceSendClockFactor;
                }
                else
                {
                    localReductionRatio =
                        ioDevice.AttributeAccess.GetAnyAttribute<long>(
                            InternalAttributeNames.PnIoDeviceLocalReductionRatio,
                            ac.GetNew(),
                            1);

                    if ((userAdjustedUpdateTime > localReductionRatio)
                        && (localReductionRatio > 0))
                    {
                        deviceSendClockFactor = userAdjustedUpdateTime / localReductionRatio;
                    }
                }
            }
        }
    }
}