/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PartnerPortBlocksAddressTailorLogic.cs    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.Tailor.Interface;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Tailor.AddressTailoring
{
    /// <summary>
    /// Summary description for PartnerPortBlocksAddressTailorLogic.
    /// </summary>
    internal class PartnerPortBlocksAddressTailorLogic : TailorLogicBase
    {
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)
        private DataModel.PCLObjects.Interface m_InterfaceSubmodule;
        private DataModel.PCLObjects.Interface m_PartnerInterfaceSubmodule;

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Construction/destruction/initialization

        // Contains all constructors (class constructors as well as static constructors),
        // All initialization methods that need to be called before using the class
        // And finally all finalization code (Finalizers, IDisposable implementation, ...)
        public PartnerPortBlocksAddressTailorLogic(
            DataModel.PCLObjects.Port port,
            DataModel.PCLObjects.Interface partnerInterface)
        {
            m_InterfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(port);
            m_PartnerInterfaceSubmodule = partnerInterface;
        }

        #endregion

        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Public methods

        // Contains all public methods of the class
        public bool HasToTailorPartnerPortBlock()
        {
            bool retValue = false;
            if ((m_PartnerInterfaceSubmodule.PNIOC != null)
                && AddressTailoringEnabled
                && !AttributeUtilities.IsIDevice(m_PartnerInterfaceSubmodule))
            {
                retValue = true;
            }
            return retValue;
        }

        #endregion
        ///////////////////////////////////////////////////////////////////////////////////////////

        #region Overrides and overridables

        // Contains all public and protected overrides as well as overridables of the class
        protected override DataModel.PCLObjects.Interface IoControllerInterface => NavigationUtilities
            .GetControllerOfDevice(m_InterfaceSubmodule);

        #endregion
    }
}