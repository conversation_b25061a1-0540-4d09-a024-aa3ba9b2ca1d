/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: DeviceConverter.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;

using PNConfigLib.Gsd.Interpreter.Common;

#endregion

namespace PNConfigLib.GSDImport.ComponentConverters
{
    internal class DeviceConverter
    {
        private DeviceAccessPoint m_DAP;

        private DecentralDeviceCatalog m_Device;

        public DeviceConverter(DeviceAccessPoint dap)
        {
            m_DAP = dap;
        }

        public DecentralDeviceCatalog Convert(string fileName)
        {
            m_Device = new DecentralDeviceCatalog();
            m_Device.AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.GSDFileName, fileName);

            AddDeviceGeneralAttributes();

            return m_Device;
        }

        private void AddDeviceGeneralAttributes()
        {
            if (m_DAP.PlugData != null && m_DAP.PlugData.FixedInSlots != null)
            {
                foreach (uint fixedInSlot in m_DAP.PlugData.FixedInSlots)
                {
                    m_Device.AttributeAccess.AddAnyAttribute<int>(
                        InternalAttributeNames.PositionNumber,
                        (int)fixedInSlot);
                    break;
                }
            }
        }
    }
}