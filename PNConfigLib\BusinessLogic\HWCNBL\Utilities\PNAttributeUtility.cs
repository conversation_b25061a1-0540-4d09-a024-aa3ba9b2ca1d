/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNAttributeUtility.cs                     :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Node;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    /// <summary>
    /// Contains methods and utilities used for accessing PROFINET attributes and relevant values.
    /// </summary>
    internal static class PNAttributeUtility
    {
        #region Combined Object

        /// <summary>
        /// Check if IODevice (and superordinated IOController) support CombinedObject
        /// </summary>
        /// <param name="interfaceSubmoduleOfIODevice"></param>
        /// <returns>true, if IOC and IOD is supported</returns>
        public static bool IsCombinedObjectSupportedForIOD(Interface interfaceSubmoduleOfIODevice)
        {
            if (interfaceSubmoduleOfIODevice == null)
            {
                return false;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            // Check if IOD IF supports the feature
            bool isCombinedObjectSupported =
                interfaceSubmoduleOfIODevice.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoPdevCombinedObjectSupported,
                    ac,
                    false);

            if (!isCombinedObjectSupported)
            {
                return false;
            }

            // Get the IOC IF and check if it also supports the feature
            Interface interfaceSubmoduleOfIOController =
                interfaceSubmoduleOfIODevice.PNIOD.AssignedController.ParentObject as Interface;

            if (interfaceSubmoduleOfIOController != null)
            {
                isCombinedObjectSupported &=
                    interfaceSubmoduleOfIOController.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoControllerPdevCombinedObjectSupported,
                        ac.GetNew(),
                        false);
            }

            return isCombinedObjectSupported;
        }

        #endregion

        /// <summary>
        /// Gets the sync role of a given IO connector.
        /// </summary>
        /// <param name="ioConnector">The IO connector whose sync role will be retrieved.</param>
        /// <returns>
        /// PNIrtSyncRole enum value based on the PNIrtSyncRole attribute value of the IO connector;
        /// PNIrtSyncRole.NotSynchronized if ioConnector is null or the attribute can not be retrieved.
        /// </returns>
        internal static PNIRTSyncRole GetAdjustedSyncRole(PclObject ioConnector)
        {
            if (ioConnector == null)
            {
                return PNIRTSyncRole.NotSynchronized;
            }
            AttributeAccessCode ac = new AttributeAccessCode();

            PNIRTSyncRole syncRole =
                (PNIRTSyncRole)
                ioConnector.AttributeAccess.GetAnyAttribute<byte>(
                    InternalAttributeNames.PnIrtSyncRole,
                    ac,
                    (byte)PNIRTSyncRole.NotSynchronized);

            if (!ac.IsOkay)
            {
                syncRole = PNIRTSyncRole.NotSynchronized;
            }

            return syncRole;
        }

        /// <summary>
        /// Gets the supported Frame Classes of the Interface Submodule.
        /// </summary>
        /// <returns>The list of the supported Frame Classes</returns>
        internal static List<PNIOFrameClass> GetSuppFrameClasses(Interface interfaceSubmodule)
        {
            List<PNIOFrameClass> suppFrameClasses = new List<PNIOFrameClass>();

            // Get the related Composite Attribute
            AttributeAccessCode accessCode = new AttributeAccessCode();

            Enumerated frameClassesEnumerated =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppFrameClass,
                    accessCode,
                    null);

            if (accessCode.IsOkay)
            {
                foreach (Enumerated child in frameClassesEnumerated.List)
                {
                    // Get the value of the enum.
                    PNIOFrameClass frameClass =
                        (PNIOFrameClass)Enum.Parse(typeof (PNIOFrameClass), (string)child.Value, true);

                    // Add the class to the global list.
                    suppFrameClasses.Add(frameClass);
                }
            }
            return suppFrameClasses;
        }

        /// <summary>
        /// Gets the supported send clock factors of a device interface submodule depending on the frame class.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface whose supported send clock factors will be retrieved.</param>
        /// <param name="frameClass">The frame class that determines whether to get RT or IRT send clock factors.</param>
        /// <returns>
        /// If frameClass is 1 or 2, returns the PNIoSuppSCF12 attribute value of deviceInterfaceSubmodule.
        /// If frameClass is 3, returns the PNIoSuppSCF3 attribute value of deviceInterfaceSubmodule.
        /// Returns empty list if deviceInterfaceSubmodule is null.
        /// Returns default values if related attributes can not be retrieved.
        /// </returns>
        internal static List<long> GetSupportedSendClockFactorList(
            BusinessLogic.DataModel.IPclObject deviceInterfaceSubmodule,
            PNIOFrameClass frameClass)
        {
            List<long> suppSCF = new List<long>();

            if (deviceInterfaceSubmodule == null)
            {
                return suppSCF;
            }

            // Get the attribute value from deviceInterfaceSubmodule
            Enumerated pnSuppSCF = null;
            AttributeAccessCode ac = new AttributeAccessCode();

            if ((frameClass == PNIOFrameClass.Class1Frame)
                || (frameClass == PNIOFrameClass.Class2Frame))
            {
                // FrameClass is 1 or 2
                pnSuppSCF =
                    deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIoSuppSCF12,
                        new AttributeAccessCode(),
                        null);
            }
            else if (frameClass == PNIOFrameClass.Class3Frame)
            {
                GetSupportedSendClockFactors3(deviceInterfaceSubmodule, out pnSuppSCF);
            }

            if (ac.IsOkay
                && (pnSuppSCF != null))
            {
                foreach (object child in pnSuppSCF.List)
                {
                    suppSCF.Add(Convert.ToInt64(child, CultureInfo.InvariantCulture));
                }
            }
            else
            {
                foreach (int supportedSendClockFactor in PNFunctionsDefaultAttributeValues.DefaultPNRTSupportedSendClockFactors)
                {
                    suppSCF.Add(supportedSendClockFactor);
                }
            }

            return suppSCF;
        }

        /// <summary>
        /// Gets supported send clock factors of an interface for frame class 3.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface whose supported send clock factors will be retrieved.</param>
        /// <param name="pnIoSuppScf3Enumerated">
        /// The list that will contain supported send clock factor values.
        /// It is set to null if PNIoSuppSCF3 attribute can not be retrieved.
        /// </param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        private static void GetSupportedSendClockFactors3(
            BusinessLogic.DataModel.IPclObject interfaceSubmodule,
            out Enumerated pnIoSuppScf3Enumerated)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }

            // Skipped IDevice related code here.
            pnIoSuppScf3Enumerated =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppSCF3,
                    new AttributeAccessCode(),
                    null);
        }

        /// <summary>
        /// Gets the supported reduction ratios of a device interface submodule which are not power of two.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface whose supported reduction ratios will be retrieved.</param>
        /// <param name="frameClass">The frame class that determines whether to get RT or IRT reduction ratios.</param>
        /// <returns>
        /// If frameClass is 1 or 2, returns the PNIoSuppRR12NonPow2 attribute value of deviceInterfaceSubmodule.
        /// If frameClass is 3, returns the PNIoSuppRR3NonPow2 attribute value of deviceInterfaceSubmodule.
        /// Returns empty list if deviceInterfaceSubmodule is null.
        /// Returns default values if related attributes can not be retrieved.
        /// </returns>
        internal static List<long> GetSuppRRNonPowList(PclObject deviceInterfaceSubmodule, PNIOFrameClass frameClass)
        {
            List<long> pnPlannerSuppRRNonPow = new List<long>();

            if (deviceInterfaceSubmodule == null)
            {
                return pnPlannerSuppRRNonPow;
            }

            string attributeName;
            IEnumerable values;
            switch (frameClass)
            {
                case PNIOFrameClass.Class1Frame:
                case PNIOFrameClass.Class2Frame:
                    attributeName = InternalAttributeNames.PnIoSuppRR12NonPow2;
                    values = PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow12;
                    break;
                case PNIOFrameClass.Class3Frame:
                    attributeName = InternalAttributeNames.PnIoSuppRR3NonPow2;
                    values = PNFunctionsDefaultAttributeValues.DefaultPNSuppRRNonPow3;
                    break;
                default:
                    return pnPlannerSuppRRNonPow;
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            Enumerated pnSuppRRNonPow = deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(attributeName, ac, null);

            Func<object, long> valueGetter = value => { return Convert.ToInt64((int)value, CultureInfo.InvariantCulture); };

            if (ac.IsOkay)
            {
                values = pnSuppRRNonPow.List;
                valueGetter = value => { return Convert.ToInt64(value, CultureInfo.InvariantCulture); };
            }

            foreach (object value in values)
            {
                pnPlannerSuppRRNonPow.Add(valueGetter(value));
            }

            return pnPlannerSuppRRNonPow;
        }

        /// <summary>
        /// Gets the supported reduction ratios of a device interface submodule which are power of two.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The interface whose supported reduction ratios will be retrieved.</param>
        /// <param name="frameClass">The frame class that determines whether to get RT or IRT reduction ratios.</param>
        /// <returns>
        /// If frameClass is 1 or 2, returns the PNIoSuppRR12Pow2 attribute value of deviceInterfaceSubmodule.
        /// If frameClass is 3, returns the PNIoSuppRR3Pow2 attribute value of deviceInterfaceSubmodule.
        /// Returns empty list if deviceInterfaceSubmodule is null.
        /// Returns default reduction ratio values if related attributes can not be retrieved.
        /// </returns>
        internal static List<long> GetSuppRRPowList(PclObject deviceInterfaceSubmodule, PNIOFrameClass frameClass)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            List<long> pnPlannerSuppRRPow = new List<long>();

            if (deviceInterfaceSubmodule == null)
            {
                return pnPlannerSuppRRPow;
            }

            // Get the attribute value from deviceInterfaceSubmodule
            Enumerated pnSuppRRPow = null;
            if ((frameClass == PNIOFrameClass.Class1Frame)
                || (frameClass == PNIOFrameClass.Class2Frame))
            {
                // FrameClass is 1 or 2
                pnSuppRRPow = deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppRR12Pow2, ac, null);
            }
            else if (frameClass == PNIOFrameClass.Class3Frame)
            {
                // FrameClass is 3
                pnSuppRRPow = deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppRR3Pow2, ac, null);
            }

            if (ac.IsOkay
                && (pnSuppRRPow != null))
            {
                foreach (object child in pnSuppRRPow.List)
                {
                    pnPlannerSuppRRPow.Add(Convert.ToInt64(child, CultureInfo.InvariantCulture));
                }
            }
            else
            {
                // The attribute does not exist at deviceInterfaceSubmodule,
                // use default values.
                if ((frameClass == PNIOFrameClass.Class1Frame)
                    || (frameClass == PNIOFrameClass.Class2Frame))
                {
                    // FrameClass is 1 or 2
                    foreach (int suppRRPow12 in PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow12)
                    {
                        pnPlannerSuppRRPow.Add(suppRRPow12);
                    }
                }
                else if (frameClass == PNIOFrameClass.Class3Frame)
                {
                    // FrameClass is 3
                    foreach (int suppRRPow3 in PNFunctionsDefaultAttributeValues.DefaultPNSuppRRPow3)
                    {
                        pnPlannerSuppRRPow.Add(suppRRPow3);
                    }
                }
            }
            return pnPlannerSuppRRPow;
        }

        internal static bool IsPNIoControllerOperatingModeActive(Interface interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                return false;
            }
            PNIOOperatingModes currentOperatingMode = AttributeUtilities.GetOperatingMode(interfaceSubmodule);
            if ((currentOperatingMode == PNIOOperatingModes.IOController)
                || (currentOperatingMode == PNIOOperatingModes.IOControllerAndIODevice))
            {
                return true;
            }
            return false;
        }

        internal static NodeIeBusinessLogic.PNIoIpConfigModeSupported GetPNIoIpConfigModeSupported(Interface interfaceSubmodule)
        {
            return GetPNIoIpConfigModeSupported(interfaceSubmodule.PCLCatalogObject);
        }

        internal static NodeIeBusinessLogic.PNIoIpConfigModeSupported GetPNIoIpConfigModeSupported(PclCatalogObject interfaceCatalog)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            NodeIeBusinessLogic.PNIoIpConfigModeSupported pnIoIpConfigModeSupported =
                NodeIeBusinessLogic.PNIoIpConfigModeSupported.NotSupported;

            if (interfaceCatalog != null)
            {
                pnIoIpConfigModeSupported =
                    (NodeIeBusinessLogic.PNIoIpConfigModeSupported)interfaceCatalog.AttributeAccess.GetAnyAttribute(
                        InternalAttributeNames.PnIoIpConfigModeSupported,
                        ac,
                        0);
            }

            return ac.IsOkay ? pnIoIpConfigModeSupported : NodeIeBusinessLogic.PNIoIpConfigModeSupported.NotSupported;
        }
    }
}