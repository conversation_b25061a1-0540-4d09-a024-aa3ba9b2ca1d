/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: InternalAttributeNames.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// Attribute names used in PNConfigLib.
    /// </summary>
    internal static class InternalAttributeNames
    {
        internal const string PnIoMachineTailoringTopologyRequired = "PnIoMachineTailoringTopologyRequired";

        internal const string NodeIPConfigurationDefault = "DefaultIPConfiguration";

        internal const string NodeDefaultIPAddress = "NodeDefaultIPAddress";

        internal const string NodeIPSubnetMaskDefault = "DefaultIPSubnetMask";

        internal const string NodeIPDefaultRouterAddressDefault = "DefaultIPDefaultRouterAddress";

        internal const string NodeIPDefaultRouterIODSync = "NodeIPDefaultRouterIODSync";

        internal const string NodeIPSubnetMaskUnrestricted = "NodeIPSubnetMaskUnrestricted";

        internal const string Name = "Name";

        internal const string Author = "Author";

        internal const string ClockSyncMode = "ClockSyncMode";

        internal const string PnIsochroneModeRequired = "PnIsochroneModeRequired";

        internal const string IsClockSyncModeSelected = "IsClockSyncModeSelected";

        internal const string InputStartAddress = "InputStartAddress";

        internal const string OutputStartAddress = "OutputStartAddress";

        internal const string IoType = "IoType";

        internal const string DataRecordTransferSequence = "DataRecordTransferSequence";

        internal const string PnIoDataRecordsTransferSeqSupp = "PnIoDataRecordsTransferSeqSupp";

        internal const string Description = "Description";

        internal const string DeviceAccessSupported = "DeviceAccessSupported";

        internal const string DeviceFunctionStatus = "DeviceFunctionStatus";

        internal const string PnIoIODDefaultRouterSupported = "PnIoIODDefaultRouterSupported";

        internal const string SignalType = "SignalType";

        internal const string GSDFileName = "GSDFileName";

        internal const string IEPBLinkOperatingMode = "IePbLinkOperatingMode";

        internal const string InAddressRange = "InAddressRange";

        internal const string IsAssignDeviceActivated = "IsAssignDeviceActivated";

        internal const string IsMasterType = "IsMasterType";

        internal const string FwVersion = "FwVersion";

        internal const string  InterfaceType = "InterfaceType";

        internal const string HwVersion = "HwVersion";

        internal const string IsReceiver = "IsReceiver";

        internal const string IsSender = "IsSender";

        internal const string IsSlaveType = "IsSlaveType";

        internal const string IsSubstituteConfigObjectType = "IsSubstituteConfigObjectType";

        internal const string MaxMastersystemNumber = "MaxMastersystemNumber";

        internal const string MinMastersystemNumber = "MinMastersystemNumber";

        internal const string ModelName = "ModelName";

        internal const string NetType = "NetType";

        internal const string NodeFeatures = "NodeFeatures";

        internal const string NodeGetsAddressAutomatically = "NodeGetsAddressAutomatically";

        internal const string NodeIECheckProtocolPresence = "NodeIECheckProtocolPresence";

        internal const string NodeIPAddress = "NodeIPAddress";

        internal const string NodeIPAddresseSetByUser = "NodeIPAddresseSetByUser";

        internal const string NodeIPBackplaneRouting = "NodeIPBackplaneRouting";

        internal const string NodeIPBackplaneRoutingSupported = "NodeIPBackplaneRoutingSupported";

        internal const string NodeIPConfiguration = "NodeIPConfiguration";

        internal const string NodeIPDefaultRouterAddress = "NodeIPDefaultRouterAddress";

        internal const string NodeIPDefaultRouterAddressUsed = "NodeIPDefaultRouterAddressUsed";

        internal const string NodeIPProtocolSupported = "NodeIPProtocolSupported";

        internal const string NodeIPProtocolUsed = "NodeIPProtocolUsed";

        internal const string NodeIPSubnetMask = "NodeIPSubnetMask";

        internal const string OB6xMinTDPDistance = "OB6xMinTDPDistance";

        internal const string GsdId = "GsdId";

        internal const string AlternativeTypeId = "AlternativeTypeId";

        internal const string OrderNumber = "OrderNumber";

        internal const string InvariantTypeName = "InvariantTypeName";

        internal const string OutAddressRange = "OutAddressRange";

        internal const string PnAddMediaRedundancyProtSupported = "PnAddMediaRedundancyProtSupported";

        internal const string PnAllowLeadingNumInNoI = "PnAllowLeadingNumInNoI";

        internal const string PnAllowOverwriteNoIActive = "PnAllowOverwriteNoIActive";

        internal const string PnAllowOverwriteNoISupported = "PnAllowOverwriteNoISupported";

        internal const string PnAPI = "PnAPI";

        internal const string PnBasicPackageVersion = "PnBasicPackageVersion";

        internal const string PnPortDeactivationSupported = "PnPortDeactivationSupported";

        #region GSDML V2.32

        //GSDML: CheckMAUTypeDifferenceSupported
        internal const string PnCheckMAUTypeDifferenceSupported = "PnCheckMAUTypeDifferenceSupported";

        #endregion

        #region GSDML V2.33

        internal const string PnAdditionalForwardingRulesSupported = "PnAdditionalForwardingRulesSupported"; //GSDML: AdditionalForwardingRulesSupported 
        internal const string PnIoNumberOfDeviceAccessAR = "PnIoNumberOfDeviceAccessAR"; //GSDML: NumberOfDeviceAccessAR

        #endregion

        #region GSDML V2.34

        internal const string PnIoNettoDataLengthIncludesIOxS = "PnIoNettoDataLengthIncludesIOxS"; //GSDML: ApplicationLengthIncludesIOxS 
        internal const string PnIoNumberOfSubmodules = "PnIoNumberOfSubmodules"; //GSDML: NumberOfSubmodules        

        #endregion

        internal const string PnCheckMAUTypeRecordSupported = "PnCheckMAUTypeRecordSupported";

        internal const string PnCheckMAUTypeSupported = "PnCheckMAUTypeSupported"; //GSDML: CheckMAUTypeSupported

        internal const string PnContainsSynchronizedItems = "PnContainsSynchronizedItems";

        internal const string PnCustomizationEnabled = "PnCustomizationEnabled";

        internal const string PnDCPBoundary = "PnDCPBoundary";

        internal const string PnDCPBoundarySupported = "PnDCPBoundarySupported";

        internal const string PnDelayMeasurementSupported = "PnDelayMeasurementSupported";

        public const string PnDontGenerateAdjustMAUTypeBlock = "PnDontGenerateAdjustMAUTypeBlock";

        //GSDML: DelayMeasurementSupported
        internal const string PnDeviceId = "PnDeviceId";

        internal const string PnDeviceIdCustomized = "PnDeviceIdCustomized";

        internal const string PnEthernetMediumDuplex = "PnEthernetMediumDuplex";

        internal const string PnExchangeWithoutMmcSupported = "PnExchangeWithoutMmcSupported";

        internal const string PnFastIoSupported = "PnFastIoSupported";

        internal const string PnFiberOptic = "PnFiberOptic";

        internal const string PnFrameMultiplier = "PnFrameMultiplier";

        internal const string PnIncludeAPDU = "PnIncludeAPDU";

        internal const string PnIoAddressTailoring = "PnIoAddressTailoring";

        internal const string PnIoAddressTailoringEnabled = "PnIoAddressTailoringEnabled";

        internal const string PnIoTailoringExt = "PnIoTailoringExt";

        internal const string PnIoAllowSlot0WithoutSubmodule1 = "PnIoAllowSlot0WithoutSubmodule1";

        internal const string PnIoArRecordSupported = "PnIoArRecordSupported";

        internal const string PnIoArUuid = "PnIoArUuid";

        internal const string PnIoConfigAlarmCrDataSupported = "PnIoConfigAlarmCrDataSupported";

        internal const string PnIoControllerPdevCombinedObjectSupported = "PnIoControllerPdevCombinedObjectSupported";

        internal const string PnIOCSLength = "PnIOCSLength";

        internal const string PnIoDCPHelloSupported = "PnIoDCPHelloSupported";

        internal const string PnIoDeviceFSUPriority = "PnIoDeviceFSUPriority";

        internal const string PnIoDeviceLocalReductionRatio = "PnIoDeviceLocalReductionRatio";

        internal const string PnIoExchangeWithoutMMC = "PnIoExchangeWithoutMMC";

        internal const string PnIoExternalSendClockFactor = "PnIoExternalSendClockFactor";

        internal const string PnIDeviceMultipleSendClock = "PnIDeviceMultipleSendClock";

        internal const string PnIoFastStartupMaxCount = "PnIoFastStartupMaxCount";

        internal const string PnIoFrameClass = "PnIoFrameClass";

        internal const string PnIoGenerateIOConfigDataIDevice = "PnIoGenerateIOConfigDataIDevice";

        internal const string PnIoGenerateConfig2003BlocksV1_1 = "PnIoGenerateConfig2003BlocksV1_1";

        internal const string PnIoGsdLegacyDataLengthConsistencyCheck = "PnIoGsdLegacyDataLengthConsistencyCheck";

        internal const string PnIoIdentificationBlockRequired = "PnIoIdentificationBlockRequired";

        internal const string PnIoInterfaceIsOptional = "PnIoInterfaceIsOptional";

        internal const string PnIoIpConfigModeSupported = "PnIoIpConfigModeSupported";

        internal const string PnIoIpSuiteViaOtherPath = "PnIoIpSuiteViaOtherPath";

        internal const string PnIoMachineTailoring = "PnIoMachineTailoring";

        internal const string PnIoMaxControllerInputDataLength = "PnIoMaxControllerInputDataLength";

        internal const string PnIoMaxControllerOutputDataLength = "PnIoMaxControllerOutputDataLength";

        internal const string PnIoMaxDeviceDataLength = "PnIoMaxDeviceDataLength";

        internal const string PnIoMaxDeviceInputDataLength = "PnIoMaxDeviceInputDataLength";

        internal const string PnIoMaxDeviceInputNettoDataLength = "PnIoMaxDeviceInputNettoDataLength";

        internal const string PnIoMaxDeviceNettoDataLength = "PnIoMaxDeviceNettoDataLength";

        internal const string PnIoMaxDeviceOutputDataLength = "PnIoMaxDeviceOutputDataLength";

        internal const string PnIoMaxDeviceOutputNettoDataLength = "PnIoMaxDeviceOutputNettoDataLength";

        internal const string PnIoMaxDevices = "PnIoMaxDevices";

        internal const string PnIoMaxDeviceSubmoduleDataLength = "PnIoMaxDeviceSubmoduleDataLength";

        internal const string PnIoMaxDeviceSubmodules = "PnIoMaxDeviceSubmodules";

        internal const string PnIoMaxIrtDevices = "PnIoMaxIrtDevices";

        internal const string PnIoMaxNumberOfAR = "PnIoMaxNumberOfAR"; //GSDML: NumberOfAR

        internal const string PnIoMaxRecordSupported = "PnIoMaxRecordSupported";

        internal const string PnIoMaxStationNumber = "PnIoMaxStationNumber";

        internal const string PnIoMinFrameIntFactor = "PnIoMinFrameIntFactor";

        internal const string PnIoMinMaxWatchdogFactor = "PnIoMinMaxWatchdogFactor";

        internal const string PnIoMinWatchdogFactorDevice = "PnIoMinWatchdogFactorDevice";

        internal const string PnIoMaxWatchdogFactorDevice = "PnIoMaxWatchdogFactorDevice";

        internal const string PnIoMultipleWriteSupported = "PnIoMultipleWriteSupported";

        internal const string PnIoNameOfStationNotTransferable = "PnIoNameOfStationNotTransferable";

        internal const string PnIoNumberOfAdditionalInputCR = "PnIoNumberOfAdditionalInputCR";

        //GSDML: NumberOfAdditionalInputCR
        internal const string PnIoNumberOfAdditionalOutputCR = "PnIoNumberOfAdditionalOutputCR";

        //GSDML: NumberOfAdditionalOutputCR
        internal const string PnIoNumberOfAR = "PnIoNumberOfAR"; //User adjusted value of PnIoMaxNumberOfAR

        internal const string PnIoObjectUUIDLocalIndex = "PnIoObjectUUIDLocalIndex";

        internal const string PnIoObjectUuidV23Supported = "PnIoObjectUuidV23Supported";

        internal const string PnIoOperatingMode = "PnIoOperatingMode";

        internal const string PnIoParameterizationSpeedupSupported = "PnIoParameterizationSpeedupSupported";

        internal const string PnIoPdevCombinedObjectSupported = "PnIoPdevCombinedObjectSupported";

        internal const string PnIoPowerOnToCommReady = "PnIoPowerOnToCommReady";

        internal const string PnIoPrioritizedStartupConsistencyCheckRequired =
            "PnIoPrioritizedStartupConsistencyCheckRequired";

        internal const string PnIoProgrammablePeer = "PnIoProgrammablePeer";

        internal const string PnIOPSLength = "PnIOPSLength";

        internal const string PnIoScfAdaptionNonPow2Supported = "PnIoScfAdaptionNonPow2Supported";

        internal const string PnIoScfAdaptionSupported = "PnIoScfAdaptionSupported";

        internal const string PnIoSendClockFactor = "PnIoSendClockFactor";

        internal const string PnIoSharedDeviceAssignmentSupp = "PnIoSharedDeviceAssignmentSupp";

        //GSDML V2.25 extention V11-------------------------------------------------------------------------------
        internal const string PnIoSharedDeviceSupported = "PnIoSharedDeviceSupported"; //GSDML: SharedDeviceSupported

        internal const string PnIoPnPlannerDistributionMode = "PnIoPnPlannerDistributionMode";

        internal const string PnIoPnPlannerMaxBytesPerMs = "PnIoPnPlannerMaxBytesPerMs";

        internal const string PnIoPnPlannerMaxFramesPerMs = "PnIoPnPlannerMaxFramesPerMs";

        internal const string PnIoPnPlannerMaxRTC12BytesPerMs = "PnIoPnPlannerMaxRTC12BytesPerMs";

        internal const string PnIoSubmoduleModelSupp = "PnIoSubmoduleModelSupp";

        internal const string PnIoSuppFrameClass = "PnIoSuppFrameClass";

        internal const string PnIoSuppRR12NonPow2 = "PnIoSuppRR12NonPow2";

        internal const string PnIoSuppRR12Pow2 = "PnIoSuppRR12Pow2";

        internal const string PnIoSuppRR3NonPow2 = "PnIoSuppRR3NonPow2";

        internal const string PnIoSuppRR3Pow2 = "PnIoSuppRR3Pow2";

        internal const string PnIoSuppSCF12 = "PnIoSuppSCF12";

        internal const string PnIoSuppSCF3 = "PnIoSuppSCF3";

        internal const string PnIoSuppStartupModes = "PnIoSuppStartupModes";

        internal const string PnIoSystemNumber = "PnIoSystemNumber";

        internal const string PnIoUserAdjustedUpdTime = "PnIoUserAdjustedUpdTime";

        internal const string PnIoWatchdogFactor = "PnIoWatchdogFactor";

        internal const string PnIrtHighestSyncClass = "PnIrtHighestSyncClass";

        internal const string PnIrtLineDelaySelection = "PnIrtLineDelaySelection";

        internal const string PnIrtLineLength = "PnIrtLineLength";

        internal const string PnIrtMaxNoIrFrameData = "PnIrtMaxNoIrFrameData";

        internal const string PnIrtPllWindow = "PnIrtPllWindow";

        internal const string PnIrtPortRxDelay = "PnIrtPortRxDelay";

        internal const string PnIrtPortSyncDomainBoundary = "PnIrtPortSyncDomainBoundary";

        internal const string PnIrtPortTxDelay = "PnIrtPortTxDelay";

        internal const string PnIrtSignalDelayTime = "PnIrtSignalDelayTime";

        internal const string PnIrtSupportedSyncProtocols = "PnIrtSupportedSyncProtocols";

        internal const string PnIrtSwitchBridgingDelay = "PnIrtSwitchBridgingDelay";

        internal const string PnIrtSwitchMaxBufferTime = "PnIrtSwitchMaxBufferTime";

        internal const string PnIrtSyncRole = "PnIrtSyncRole";

        internal const string PnIrtSyncRoleSupp = "PnIrtSyncRoleSupp";

        internal const string PnIsMultipleConnectionEnabled = "PnIsMultipleConnectionEnabled";

        internal const string PnIsoDataModel = "PnIsoDataModel";

        internal const string PnIsoMaxDelayTime = "PnIsoMaxDelayTime";

        internal const string PnIsoMinDelayTime = "PnIsoMinDelayTime";

        internal const string PnIsoRecordNotSupported = "PnIsoRecordNotSupported";

        internal const string PnIsoTcaValid = "PnIsoTcaValid";

        internal const string PnIsProxy = "PnIsProxy";

        internal const string PnPnPlannerAdditionalLinkDelay = "PnPnPlannerAdditionalLinkDelay";

        internal const string PnPnPlannerByteLengthFactor = "PnPnPlannerByteLengthFactor";

        internal const string PnPnPlannerMaxFrameLength = "PnPnPlannerMaxFrameLength";

        internal const string PnPnPlannerMinFrameLength = "PnPnPlannerMinFrameLength";

        internal const string PnPnPlannerMinInterLsduGap = "PnPnPlannerMinInterLsduGap";

        internal const string PnPnPlannerStartAlignedBorder = "PnPnPlannerStartAlignedBorder";

        internal const string PnLinkStateDiagnosis = "PnLinkStateDiagnosis";

        internal const string PnLinkStateDiagnosisCapability = "PnLinkStateDiagnosisCapability";

        internal const string PnLLDPLegacyEnforce = "PnLLDPLegacyEnforce";

        internal const string PnLLDPSupported = "PnLLDPSupported";

        internal const string PnMaxIoCyclicBandwidth = "PnMaxIoCyclicBandwidth";

        internal const string PnModuleIdentNumber = "PnModuleIdentNumber";

        internal const string PnMrpDomainNameService = "PnMrpDomainNameService";

        internal const string PnMrpIsDefaultRingPort = "PnMrpIsDefaultRingPort";

        internal const string PnMrpManagerPriority = "PnMrpManagerPriority";

        internal const string PnMrpMaxInstances = "PnMrpMaxInstances";

        internal const string PnMrpIsSelectedRingPort = "PnMrpIsSelectedRingPort";

        internal const string PnMrpRole = "PnMrpRole";

        internal const string ManagerAuto = "ManagerAuto";

        internal const string PnMrpSupported = "PnMrpSupported";

        internal const string PnMrpSupportedMultipleRole = "PnMrpSupportedMultipleRole";

        internal const string PnMrpSupportsRingConfig = "PnMrpSupportsRingConfig";

        internal const string PnMrpDiagnosis = "PnMrpDiagnosis";

        internal const string PnInstanceNumber = "PnInstanceNumber";

        internal const string PnInstanceMrpRole = "PnInstanceMrpRole";

        internal const string PnInstanceDiagnostics = "PnInstanceDiagnostics";

        internal const string PnNameOfStation = "PnNameOfStation";

        internal const string PnNameOfStationVirtual = "PnNameOfStationVirtual";

        internal const string PnNetworkComponentDiagnosisSupported = "PnNetworkComponentDiagnosisSupported";

        internal const string PnParameterizationDisallowed = "PnParameterizationDisallowed";

        //GSDML: ParameterizationDisallowed
        internal const string PnPdevModelSupportedLocal = "PnPdevModelSupportedLocal";

        internal const string PnPdevParametrizationDecentral = "PnPdevParametrizationDecentral";

        internal const string PnPdevSupportedModel = "PnPdevSupportedModel";

        internal const string PnPnIpConfigModeSupported = "PnPnIpConfigModeSupported";

        internal const string PnPnIpSuiteViaOtherPath = "PnPnIpSuiteViaOtherPath";

        internal const string PnPnNoSAutoGenerate = "PnPnNoSAutoGenerate";

        internal const string PnPnNoSViaOtherPath = "PnPnNoSViaOtherPath";

        internal const string PnPortAutoNegotiation = "PnPortAutoNegotiation";

        internal const string PnPortDeactivated = "PnPortDeactivated";

        internal const string PnPortNumber = "PnPortNumber";

        internal const string UniquePortNumber = "UniquePortNumber";

        internal const string PnPowerBudgetControlSupported = "PnPowerBudgetControlSupported";

        internal const string PnPTPBoundary = "PnPTPBoundary";

        internal const string PnPTPBoundarySupported = "PnPTPBoundarySupported";

        internal const string PnResSyncCyclicBandwidth = "PnResSyncCyclicBandwidth";

        internal const string PnResSyncCyclicRatio = "PnResSyncCyclicRatio";

        internal const string PnConfigAlarmRetries = "PnConfigAlarmRetries";

        internal const string PnSendClockFactorTransient = "PnSendClockFactorTransient";

        internal const string PnStationNumber = "PnStationNumber";

        internal const string PnSubmoduleIdentNumber = "PnSubmoduleIdentNumber";

        internal const string PnSubslotNumber = "PnSubslotNumber";

        internal const string PnSyncDomainConfiguration = "PnSyncDomainConfiguration";

        internal const string PnSyncDomainUuid = "PnSyncDomainUuid";

        internal const string PnUpdateTimeMode = "PnUpdateTimeMode";

        internal const string PnVendorId = "PnVendorId";

        internal const string PnVendorIdCustomized = "PnVendorIdCustomized";

        internal const string PositionNumber = "PositionNumber";

        internal const string CpuRtSlotNumber = "CpuRtSlotNumber";

        internal const string RTATimeoutFactor = "RTATimeoutFactor";

        internal const string PnIoAddressModeFlags = "PnIoAddressModeFlags";

        internal const string ScalanceDeviceCapabilities = "ScalanceDeviceCapabilities";

        internal const string SharedInputSupported = "SharedInputSupported"; //GSDML: SharedInputSupported

        internal const string SharedIoAssignment = "SharedIoAssignment";

        internal const string TimeSyncInterval = "TimeSyncInterval";

        internal const string TimeSyncRole = "TimeSyncRole";

        internal const string TypeName = "TypeName";

        internal const string UseNameInDeviceController = "UseNameInDeviceController";

        internal const string AsNtpServerAddress1 = "AsNtpServerAddress1";

        internal const string AsNtpServerAddress2 = "AsNtpServerAddress2";

        internal const string AsNtpServerAddress3 = "AsNtpServerAddress3";

        internal const string AsNtpServerAddress4 = "AsNtpServerAddress4";

        internal const string ClockSyncTi = "ClockSyncTi";

        internal const string ClockSyncTo = "ClockSyncTo";

        internal const string ClockSyncTiValid = "ClockSyncTiValid";

        internal const string ClockSyncToValid = "ClockSyncToValid";

        internal const string Comment = "Comment";

        internal const string InputDataLength = "InputDataLength";

        internal const string OutputDataLength = "OutputDataLength";

        internal const string AssignedPipNumbers = "AssignedPipNumbers";
        
        internal const string PnIoKeepARAtError = "PnIoKeepARAtError";

        internal const string PnIoKeepARAtErrorActivated = "PnIoKeepARAtErrorActivated";

        public const string PnIoControllerPropertiesBlockVersion = "PnIoControllerPropertiesBlockVersion";

        public const string PnDCPReadOnlySupported = "PnDCPReadOnlySupported";

        public const string ActivateDcpReadOnly = "ActivateDcpReadOnly";

        public const string DcpEnableReadOnly = "DcpEnableReadOnly";

        #region GSDML V2.3

        #region IRT+ Attributes

        internal const string PnIrtArStartupMode = "PnIrtArStartupMode";

        internal const string PnIrtForwardingMode = "PnIrtForwardingMode";

        internal const string PnIrtMediaRedTimerSupported = "PnIrtMediaRedTimerSupported";

        internal const string PnIrtSwitchBridgingDelayFFW = "PnIrtSwitchBridgingDelayFFW";

        internal const string PnIrtSwitchBridgingDelayFFWIOC = "PnIrtSwitchBridgingDelayFFWIOC";

        internal const string PnIrtShortPreamble100MBitSupported = "PnIrtShortPreamble100MBitSupported";

        internal const string PnIrtGenerateAdjustPreambleLengthV14 = "PnIrtGenerateAdjustPreambleLengthV14";

        internal const string PnIrtMaxDfpFeed = "PnIrtMaxDfpFeed";

        internal const string PnIrtAlignDfpSubframes = "PnIrtAlignDfpSubframes";

        internal const string PnIrtDfpOutboundTruncationSupported = "PnIrtDfpOutboundTruncationSupported";

        internal const string PnIrtMaxRangeIRFrameID = "PnIrtMaxRangeIRFrameID";

        internal const string PnIrtMaxRedPeriodLength = "PnIrtMaxRedPeriodLength";

        internal const string PnIrtPeerToPeerJitter = "PnIrtPeerToPeerJitter";

        internal const string PnIrtFragmentationType = "PnIrtFragmentationType";

        internal const string PnIrtMinFrameSendOffset = "PnIrtMinFrameSendOffset";

        internal const string PnIrtGeneratePdevInConfig2008 = "PnIrtGeneratePdevInConfig2008";

        #endregion

        #region #Other GSDML V2.3 Attributes

        internal const string PnIoArStartupMode = "PnIoArStartupMode";

        internal const string PnIoIOXSRequired = "PnIoIOXSRequired";

        internal const string PnLLDPNoDSupported = "PnLLDPNoDSupported";

        internal const string PnIoCheckDeviceIDAllowed = "PnIoCheckDeviceIDAllowed";

        internal const string PnIoCheckDeviceIDSupported = "PnIoCheckDeviceIDSupported";

        #endregion

        internal const string PnIrtMinRTC3Gap = "PnIrtMinRTC3Gap";

        internal const string PnIoMaxFrameStartTime = "PnIoMaxFrameStartTime";

        internal const string PnIoMinNRTGap = "PnIoMinNRTGap";

        internal const string PnIrtMinYellowTime = "PnIrtMinYellowTime";

        internal const string PnIrtMinYellowTimeIOC = "PnIrtMinYellowTimeIOC";

        internal const string PnIrtYellowSafetyMargin = "PnIrtYellowSafetyMargin";

        internal const string PnIrtMaxRetentionTime = "PnIrtMaxRetentionTime";

        #endregion

        internal const string PnIoMaxNumberOfInputCR = "PnIoMaxNumberOfInputCR"; //GSDML: MaxNumberOfInputCR
        internal const string PnIoMaxNumberOfOutputCR = "PnIoMaxNumberOfOutputCR"; //GSDML: MaxNumberOfOutputCR

        #region Other IRT+ related Attributes

        internal const string PnIrtMrpdDiagnosis = "PnIrtMrpdDiagnosis";

        internal const string PnIrtSyncDomainExpertMode = "PnIrtSyncDomainExpertMode";

        internal const string PnIrtFFWMode = "PnIrtFFWMode";

        internal const string PnIrtSyncDomainFFW = "PnIrtSyncDomainFFW";

        internal const string PnIrtSwitchActualBridgingDelay = "PnIrtSwitchActualBridgingDelay";

        internal const string PnIrtRestartFactorForDistributedWD = "PnIrtRestartFactorForDistributedWD";

        internal const string PnIrtSubframeCRCSupported = "PnIrtSubframeCRCSupported";

        internal const string PnIrtFragmentationMode = "PnIrtFragmentationMode";

        internal const string PnIrtMaxDfpFrames = "PnIrtMaxDfpFrames";

        internal const string PnIrtMaxDfpFramesIOC = "PnIrtMaxDfpFramesIOC";

        internal const string PnIrtBandwidthLevel = "PnIrtBandwidthLevel";

        internal const string PnIrtMaxSuppBwLevel = "PnIrtMaxSuppBwLevel";

        internal const string PnIoSuppSCF3IDevice = "PnIoSuppSCF3IDevice";

        internal const string PnIrtUsingShortPreamble = "PnIrtUsingShortPreamble";

        #endregion

        #region Isochron

        internal const string PnIsoTDCConsistencyCheckAsWarning = "PnIsoTDCConsistencyCheckAsWarning";

        internal const string PnIsoTpaMaxInCopyLen = "PnIsoTpaMaxInCopyLen";

        internal const string PnIsoTpaMaxOutCopyLen = "PnIsoTpaMaxOutCopyLen";

        internal const string PnIsoInterruptLatency = "PnIsoInterruptLatency";

        internal const string PnIsoCopyTimeInputBase = "PnIsoCopyTimeInputBase";

        internal const string PnIsoCopyTimeInputByte = "PnIsoCopyTimeInputByte";

        internal const string PnIsoCopyTimeOutputBase = "PnIsoCopyTimeOutputBase";

        internal const string PnIsoCopyTimeOutputByte = "PnIsoCopyTimeOutputByte";

        internal const string PnIsoProvisionTime = "PnIsoProvisionTime";

        internal const string PnIsoCopyTimeInputSubmodule = "PnIsoCopyTimeInputSubmodule";

        internal const string PnIsoCopyTimeOutputSubmodule = "PnIsoCopyTimeOutputSubmodule";

        internal const string IsoModeDecentralSupported = "IsoModeDecentralSupported";

        internal const string DelayTimeOB61 = "DelayTimeOB61";

        internal const string PnIsoTi = "PnIsoTi";

        internal const string PnIsoTo = "PnIsoTo";

        internal const string PnIsoT_IO_InputValid = "PnIsoT_IO_InputValid";

        internal const string PnIsoT_IO_OutputValid = "PnIsoT_IO_OutputValid";

        internal const string PnIsoUseDeviceLocalOutputValid = "PnIsoUseDeviceLocalOutputValid";

        internal const string PnIsoT_DC_MIN = "PnIsoT_DC_MIN";

        internal const string PnIsoT_DC_MAX = "PnIsoT_DC_MAX";

        internal const string PnIsoT_IO_BASE = "PnIsoT_IO_BASE";

        internal const string PnIsoT_IO_InputMin = "PnIsoT_IO_InputMin";

        internal const string PnIsoT_IO_OutputMin = "PnIsoT_IO_OutputMin";

        internal const string PnIsoT_DC_BASE = "PnIsoT_DC_BASE";

        internal const string IsoTcaValid = "IsoTcaValid";

        internal const string PnIsoCacf = "PnIsoCacf";

        internal const string PnIsoCacfSupported = "PnIsoCacfSupported";

        internal const string PnIsochron = "PnIsochron";

        internal const string PnIsoTiToCalcMode = "PnIsoTiToCalcMode";

        internal const string PnIsoTiMinCalculated = "PnIsoTiMinCalculated";

        internal const string PnIsoToMinCalculated = "PnIsoToMinCalculated";

        internal const string PnIsoTiRanges = "PnIsoTiRanges";

        internal const string PnIsoToRanges = "PnIsoToRanges";

        internal const string DelayTimeAutomaticOb61 = "DelayTimeAutomaticOB61";

        internal const string PnIsoUserAdjustedAppCycle = "PnIsoUserAdjustedAppCycle";

        //DPMasterSystemIdOB6x
        internal const string DPMasterSystemIdOB61 = "DPMasterSystemIdOB61";
        internal const string DPMasterSystemIdOB62 = "DPMasterSystemIdOB62";
        internal const string DPMasterSystemIdOB63 = "DPMasterSystemIdOB63";
        internal const string DPMasterSystemIdOB64 = "DPMasterSystemIdOB64";


        //CoupledDistributedIo
        internal const string CoupledDistributedIoOB61 = "CoupledDistributedIoOB61";
        internal const string CoupledDistributedIoOB62 = "CoupledDistributedIoOB62";
        internal const string CoupledDistributedIoOB63 = "CoupledDistributedIoOB63";
        internal const string CoupledDistributedIoOB64 = "CoupledDistributedIoOB64";
        #endregion

        #region Alternative Redundancy
        internal const string PnAlternativeRedundancy = "PnAlternativeRedundancy";

        #endregion

        #region AlarmCRData

        internal const string AlarmCRVersion = "AlarmCRVersion";

        internal const string AlarmCRType = "AlarmCRType";

        internal const string Ethertype = "Ethertype";

        internal const string AlarmTransport = "AlarmTransport";

        internal const string AlarmPriority = "AlarmPriority";

        internal const string RTARetries = "RTARetries";

        internal const string AlarmCRUserPriorityOfAlarmCRTagHeaderHigh = "AlarmCRUserPriorityOfAlarmCRTagHeaderHigh";

        internal const string AlarmCRVLANIDOfAlarmCRTagHeaderHigh = "AlarmCRVLANIDOfAlarmCRTagHeaderHigh";

        internal const string AlarmCRUserPriority = "AlarmCRUserPriority";

        internal const string AlarmCRVLANID = "AlarmCRVLANID";

        internal const string AlarmCRProperties = "AlarmCRProperties";

        internal const string AlarmCRTagHeaderHigh = "AlarmCRTagHeaderHigh";

        internal const string AlarmCRTagHeaderLow = "AlarmCRTagHeaderLow";

        #endregion

        #region SNMP

        internal const string SnmpEnabled = "SNMPEnabled";

        internal const string SnmpEnableReadOnly = "SNMPEnableReadOnly";

        internal const string SnmpReadOnlyCommunityName = "SNMPReadOnlyCommunityName";

        internal const string SnmpReadWriteCommunityName = "SNMPReadWriteCommunityName";

        #endregion
    }
}