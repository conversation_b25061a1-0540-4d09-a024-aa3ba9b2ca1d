/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: Importer                                  :C&  */
/*                                                                           */
/*  F i l e               &F: CentralDeviceCatalogObjectReader.cs       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Xml.Serialization;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Port;
using PNConfigLib.HWCNBL.Tailor.AddressTailoring.Logic;
using PNConfigLib.HWCNBL.Utilities.Enums;

namespace PNConfigLib.CentralDeviceImport
{
    internal class CentralDeviceCatalogObjectReader
    {
        internal static readonly string CentralDeviceInterfaceFormat // {0}: InterfaceType, {1}: Version
            = "PNConfigLib.Importer.CentralDeviceImport.CentralDeviceCatalogTemplates.{0}_{1}.xml";

        /// <summary>
        /// Reads controller variant
        /// </summary>
        /// <param name="pnDriverInterfaceType"></param>
        /// <param name="customInterfacePath"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        private CentralDeviceCatalogObject ReadCatalog(
            PNDriverInterfaceEnum pnDriverInterfaceType,
            string customInterfacePath,
            string version)
        {
            CentralDeviceCatalogObject retval = pnDriverInterfaceType == PNDriverInterfaceEnum.Custom
                                                    ? ReadCustomCatalog(customInterfacePath)
                                                    : ReadEmbeddedCatalog(pnDriverInterfaceType, version);

            return retval;
        }

        /// <summary>
        /// Reads embedded variant
        /// </summary>
        /// <param name="pnDriverInterfaceType"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        private CentralDeviceCatalogObject ReadEmbeddedCatalog(
            PNDriverInterfaceEnum pnDriverInterfaceType,
            string version)
        {
            CentralDeviceCatalogObject retval;

            string resourceName = string.Format(
                CultureInfo.InvariantCulture,
                CentralDeviceInterfaceFormat,
                pnDriverInterfaceType,
                version);
            XmlSerializer serializer = new XmlSerializer(typeof(CentralDeviceCatalogObject));
            Assembly assembly = Assembly.GetExecutingAssembly();
            using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            {
                retval = (CentralDeviceCatalogObject)serializer.Deserialize(stream);
            }

            return retval;
        }

        /// <summary>
        /// Reads custom variant
        /// </summary>
        /// <param name="customInterfacePath"></param>
        /// <returns></returns>
        private CentralDeviceCatalogObject ReadCustomCatalog(string customInterfacePath)
        {
            CentralDeviceCatalogObject retval =
                ConfigReader.ConfigReader.DeserializeInput<CentralDeviceCatalogObject>(customInterfacePath);
            return retval;
        }

        /// <summary>
        /// Fills central device catalog. Adds attributes and childs.
        /// </summary>
        /// <param name="pnDriverInterfaceType"></param>
        /// <param name="customInterfacePath"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        internal CentralDeviceCatalog FillCentralDeviceCatalog(
            PNDriverInterfaceEnum pnDriverInterfaceType,
            string customInterfacePath,
            string version)
        {
            CentralDeviceCatalog retval = new CentralDeviceCatalog();

            CentralDeviceCatalogObject catalogObject = ReadCatalog(pnDriverInterfaceType, customInterfacePath, version);
            catalogObject.AttributeLookup.ToList()
                .ForEach(ca => retval.AttributeAccess.AddAnyAttribute(ca.Key, ca.Value));
            if (catalogObject.InterfaceCatalogItems != null
                && catalogObject.InterfaceCatalogItems.Count > 0)
            {
                retval.Interface = FillInterfaceCatalog(catalogObject.InterfaceCatalogItems[0]);
                if (catalogObject.InterfaceCatalogItems[0].PortCatalogItems != null)
                {
                    FillPortList(retval.Interface, catalogObject.InterfaceCatalogItems[0]);
                }
            }

            return retval;
        }

        /// <summary>
        /// Fills interface catalog form the catalog item. Adds attributes and decoration items.
        /// </summary>
        /// <param name="interfaceCatalog"></param>
        /// <returns></returns>
        private InterfaceCatalog FillInterfaceCatalog(InterfaceCatalogItem interfaceCatalog)
        {
            InterfaceCatalog retval = new InterfaceCatalog();

            CatalogAttribute isoModeSupported =
                interfaceCatalog.AttributeLookup.FirstOrDefault(
                    a => a.Key == InternalAttributeNames.IsoModeDecentralSupported);

            if (isoModeSupported != null)
            {
                FillIsochronDecorationItems(retval.DecorationList);
            }
            else
            {
                CatalogAttribute pnIoSuppFrameClass =
                    interfaceCatalog.AttributeLookup.FirstOrDefault(
                        a => a.Key == InternalAttributeNames.PnIoSuppFrameClass);

                if (pnIoSuppFrameClass != null
                    && pnIoSuppFrameClass.Value is Enumerated)
                {
                    if (((Enumerated)pnIoSuppFrameClass.Value).List.Any(
                        v => (PNIOFrameClass)Enum.Parse(typeof(PNIOFrameClass), v.ToString(), true)
                             == PNIOFrameClass.Class3Frame))
                    {
                        FillIrtDecorationItems(retval.DecorationList);
                    }
                    else
                    {
                        FillRtDecorationItems(retval.DecorationList);
                    }
                }
            }

            interfaceCatalog.AttributeLookup.ToList()
                .ForEach(ca => retval.AttributeAccess.AddAnyAttribute(ca.Key, ca.Value));

            return retval;
        }

        /// <summary>
        /// Adds RT related items to decorationList
        /// </summary>
        /// <returns></returns>
        private void FillRtDecorationItems(ICollection<Type> decorationList)
        {
            decorationList.Add(typeof(PNIOInterfaceBusinessLogic));
            decorationList.Add(typeof(PNIOInterfaceCentralBusinessLogic));
            decorationList.Add(typeof(AdressTailorCentralBL));
        }

        /// <summary>
        /// Adds IRT related items to decorationList
        /// </summary>
        /// <returns></returns>
        private void FillIrtDecorationItems(ICollection<Type> decorationList)
        {
            FillRtDecorationItems(decorationList);
            decorationList.Add(typeof(PNIrtIFCentralBL));
        }

        /// <summary>
        /// Adds Isochron related items to decorationList
        /// </summary>
        /// <returns></returns>
        private void FillIsochronDecorationItems(ICollection<Type> decorationList)
        {
            FillIrtDecorationItems(decorationList);
            decorationList.Add(typeof(PNIsochronIFCentralBL));
        }

        /// <summary>
        /// Fills ports of an interface catalog.
        /// </summary>
        /// <param name="interfaceCatalog"></param>
        /// <param name="interfaceCatalogItem"></param>
        private void FillPortList(InterfaceCatalog interfaceCatalog, InterfaceCatalogItem interfaceCatalogItem)
        {
            interfaceCatalog.SetPortList(new SortedList<uint, PortCatalog>());
            for (int portConfigurationItemCursor = 0;
                 portConfigurationItemCursor < interfaceCatalogItem.PortCatalogItems.Count;
                 portConfigurationItemCursor++)
            {
                interfaceCatalog.PortList.Add((uint)portConfigurationItemCursor, new PortCatalog());

                if (interfaceCatalog.DecorationList.Any(d => d == typeof(PNIOInterfaceBusinessLogic)))
                {
                    interfaceCatalog.PortList[(uint)portConfigurationItemCursor].DecorationList
                        .Add(typeof(PNIoPortBusinessLogic));
                }

                if (interfaceCatalog.DecorationList.Any(d => d == typeof(PNIrtIFCentralBL)))
                {
                    interfaceCatalog.PortList[(uint)portConfigurationItemCursor].DecorationList
                        .Add(typeof(PNIrtPortBusinessLogic));
                }

                foreach (CatalogAttribute ca in interfaceCatalogItem.PortCatalogItems[portConfigurationItemCursor]
                    .AttributeLookup)
                {
                    interfaceCatalog.PortList[(uint)portConfigurationItemCursor].AttributeAccess
                        .AddAnyAttribute(ca.Key, ca.Value);
                }
            }
        }
    }
}
