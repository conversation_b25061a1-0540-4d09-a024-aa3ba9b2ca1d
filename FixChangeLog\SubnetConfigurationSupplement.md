# Subnet Configuration 补充说明

## 问题识别

在初始的配置架构中，我们遗漏了几个关键的Subnet配置字段，这些字段对于完整的PROFINET配置至关重要：

1. **SubnetRefID** 和 **IOSystemRefID** - 用于建立设备与子网和IO系统的引用关系
2. **SyncDomains** - 同步域配置，包含带宽使用等重要设置
3. **SyncDomain Details** - 同步域的详细配置，特别是BandwidthUse属性

## 补充的配置字段

### 1. EthernetAddressesConfig 更新

**新增字段：**
```csharp
public class EthernetAddressesConfig
{
    // 原有字段...
    
    /// <summary>
    /// 子网引用ID - 引用Configuration中定义的Subnet
    /// </summary>
    public string SubnetRefID { get; set; } = string.Empty;

    /// <summary>
    /// IO系统引用ID - 引用Configuration中定义的IOSystem
    /// </summary>
    public string IOSystemRefID { get; set; } = string.Empty;
}
```

**XML映射：**
```xml
<EthernetAddresses SubnetRefID="PNIE_1" IOSystemRefID="IOSystem1">
    <IPProtocol>
        <SetInTheProject IPAddress="***********" SubnetMask="*************" RouterAddress="***********00" />
    </IPProtocol>
    <PROFINETDeviceName>
        <PNDeviceName>profinetdriver</PNDeviceName>
    </PROFINETDeviceName>
</EthernetAddresses>
```

### 2. DomainManagementConfig 更新

**新增SyncDomains支持：**
```csharp
public class DomainManagementConfig
{
    /// <summary>
    /// 同步域配置列表
    /// </summary>
    public List<SyncDomainConfig> SyncDomains { get; set; } = new List<SyncDomainConfig>();

    /// <summary>
    /// MRP域配置列表
    /// </summary>
    public List<MrpDomainConfig> MrpDomains { get; set; } = new List<MrpDomainConfig>();
}
```

### 3. 新增SyncDomainConfig类

```csharp
/// <summary>
/// 同步域配置
/// </summary>
public class SyncDomainConfig
{
    /// <summary>
    /// 同步域ID
    /// </summary>
    public string SyncDomainID { get; set; } = string.Empty;

    /// <summary>
    /// 同步域名称
    /// </summary>
    public string SyncDomainName { get; set; } = string.Empty;

    /// <summary>
    /// 同步域详细配置
    /// </summary>
    public SyncDomainDetailsConfig Details { get; set; } = new SyncDomainDetailsConfig();
}

/// <summary>
/// 同步域详细配置
/// </summary>
public class SyncDomainDetailsConfig
{
    /// <summary>
    /// 带宽使用配置
    /// </summary>
    public string BandwidthUse { get; set; } = "Maximum 50% cyclic IO data. Balanced proportion";
}
```

**XML映射：**
```xml
<DomainManagement>
    <SyncDomains>
        <SyncDomain SyncDomainID="Sync-Domain1" SyncDomainName="同步域1">
            <Details BandwidthUse="Maximum 50% cyclic IO data. Balanced proportion" />
        </SyncDomain>
    </SyncDomains>
    <MrpDomains>
        <MrpDomain MrpDomainID="mrpdomain-1" MrpDomainName="MRP域1" />
    </MrpDomains>
</DomainManagement>
```

## 完整的Subnet配置示例

### 配置代码示例：
```csharp
var subnet = new SubnetConfig
{
    SubnetID = "PNIE_1",
    IOSystems = new List<IOSystemConfig>
    {
        new IOSystemConfig
        {
            IOSystemID = "IOSystem1",
            General = new IOSystemGeneralConfig
            {
                IOSystemNumber = 100,
                IOSystemName = "PROFINET IO-System"
            }
        }
    },
    DomainManagement = new DomainManagementConfig
    {
        SyncDomains = new List<SyncDomainConfig>
        {
            new SyncDomainConfig
            {
                SyncDomainID = "Sync-Domain1",
                SyncDomainName = "同步域1",
                Details = new SyncDomainDetailsConfig
                {
                    BandwidthUse = "Maximum 50% cyclic IO data. Balanced proportion"
                }
            }
        },
        MrpDomains = new List<MrpDomainConfig>
        {
            new MrpDomainConfig
            {
                MrpDomainID = "mrpdomain-1",
                MrpDomainName = "MRP域1"
            }
        }
    }
};
```

### 对应的XML输出：
```xml
<Subnet SubnetID="PNIE_1">
    <IOSystem IOSystemID="IOSystem1">
        <General IOSystemName="PROFINET IO-System" IOSystemNumber="100" />
    </IOSystem>
    <DomainManagement>
        <SyncDomains>
            <SyncDomain SyncDomainID="Sync-Domain1" SyncDomainName="同步域1">
                <Details BandwidthUse="Maximum 50% cyclic IO data. Balanced proportion" />
            </SyncDomain>
        </SyncDomains>
        <MrpDomains>
            <MrpDomain MrpDomainID="mrpdomain-1" MrpDomainName="MRP域1" />
        </MrpDomains>
    </DomainManagement>
</Subnet>
```

## 引用关系说明

### 1. SubnetRefID 和 IOSystemRefID 的作用
- **SubnetRefID**: 在EthernetAddresses中引用Subnet的SubnetID
- **IOSystemRefID**: 在EthernetAddresses中引用IOSystem的IOSystemID
- 这些引用建立了设备与网络拓扑的关联关系

### 2. 引用关系示例
```
CentralDevice.EthernetAddresses.SubnetRefID = "PNIE_1"
    ↓ 引用
Subnet.SubnetID = "PNIE_1"

CentralDevice.EthernetAddresses.IOSystemRefID = "IOSystem1"
    ↓ 引用
Subnet.IOSystems[0].IOSystemID = "IOSystem1"
```

## BandwidthUse 可选值

根据XSD定义，BandwidthUse支持以下值：
- "Maximum 25% cyclic IO data. Balanced proportion"
- "Maximum 50% cyclic IO data. Balanced proportion"
- "Maximum 75% cyclic IO data. Balanced proportion"
- "Maximum 90% cyclic IO data. Balanced proportion"

## 更新后的字段映射

### 新增映射关系：
```
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses@SubnetRefID

ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses@IOSystemRefID

ConfigurationSettings.Subnets[].DomainManagement.SyncDomains[].SyncDomainID → Configuration/Subnet/DomainManagement/SyncDomains/SyncDomain@SyncDomainID

ConfigurationSettings.Subnets[].DomainManagement.SyncDomains[].Details.BandwidthUse → Configuration/Subnet/DomainManagement/SyncDomains/SyncDomain/Details@BandwidthUse
```

## 总结

通过这些补充，我们的配置架构现在完全支持：
1. ✅ 完整的Subnet配置
2. ✅ SyncDomain配置，包括带宽使用设置
3. ✅ 正确的SubnetRefID和IOSystemRefID引用关系
4. ✅ 符合XSD标准的完整Configuration结构

这些更新确保了配置文件能够生成完全符合PNConfigLib要求的XML输出。
