using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PNConfigTool.Models;
using PNConfigTool.Views.Windows;
using System.Linq;
using PNConfigTool.ViewModels;
using PNConfigTool.Services;

namespace PNConfigTool.Views.Pages
{
    /// <summary>
    /// NetworkConfigPage.xaml 的交互逻辑
    /// </summary>
    public partial class NetworkConfigPage : Page, INavigationAwareLeaving
    {
        private ProjectManager _projectManager;
        private bool _isModified = false;
        private bool _isInitialized = false;

        public NetworkConfigPage()
        {
            InitializeComponent();

            // 初始化项目管理器
            _projectManager = ProjectManager.Instance;

            // 设置按钮状态
            NextButton.IsEnabled = false;
            PreviousStepButton.IsEnabled = true;

            // 确保Ring Port ComboBox有默认选择（在UI加载完成后）
            this.Loaded += (s, e) => {
                if (RingPort1ComboBox.SelectedIndex == -1 || RingPort2ComboBox.SelectedIndex == -1)
                {
                    EnsureRingPortsHaveDifferentDefaults();
                }
            };

            // 初始化时检查是否有项目
            if (_projectManager.CurrentProject != null)
            {
                LoadProjectData();
            }
            else
            {
                ShowNoProjectView();
            }
        }

        /// <summary>
        /// 项目加载事件处理
        /// </summary>
        private void OnProjectLoaded(object sender, EventArgs e)
        {
            LoadProjectData();
        }

        /// <summary>
        /// 项目关闭事件处理
        /// </summary>
        private void OnProjectClosed(object sender, EventArgs e)
        {
            ShowNoProjectView();
        }

        /// <summary>
        /// 加载项目数据
        /// </summary>
        private void LoadProjectData()
        {
            // 如果有项目打开，显示配置区域
            NoProjectPanel.Visibility = Visibility.Collapsed;
            ConfigScrollViewer.Visibility = Visibility.Visible;
            
            // 加载现有配置
            LoadNetworkConfig();
            
            // 重置修改状态，因为刚加载完成
            _isModified = false;
            
            System.Diagnostics.Debug.WriteLine("已加载网络配置");
        }

        /// <summary>
        /// 显示无项目视图
        /// </summary>
        private void ShowNoProjectView()
        {
            // 无项目打开时，显示提示面板
            NoProjectPanel.Visibility = Visibility.Visible;
            ConfigScrollViewer.Visibility = Visibility.Collapsed;
            
            // 禁用所有配置区域
            UpdateConfigurationAreaState(false);
            
            // 禁用下一步按钮
            NextButton.IsEnabled = false;
            PreviousStepButton.IsEnabled = false;
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 如果页面尚未初始化，则初始化控件并加载配置
                if (!_isInitialized)
                {
                    InitializeControls();
                    
                    // 检查是否有项目打开
                    if (_projectManager.CurrentProject == null)
                    {
                        // 无项目打开时，显示提示面板
                        NoProjectPanel.Visibility = Visibility.Visible;
                        ConfigScrollViewer.Visibility = Visibility.Collapsed;
                        
                        // 禁用所有配置区域
                        UpdateConfigurationAreaState(false);
                        
                        // 禁用下一步按钮
                        NextButton.IsEnabled = false;
                        PreviousStepButton.IsEnabled = false;
                    }
                    else
                    {
                        // 如果有项目打开，显示配置区域
                        NoProjectPanel.Visibility = Visibility.Collapsed;
                        ConfigScrollViewer.Visibility = Visibility.Visible;
                        
                        // 加载现有配置
                        LoadNetworkConfig();
                        
                        // 重置修改状态，因为刚加载完成
                        _isModified = false;
                        
                        System.Diagnostics.Debug.WriteLine("已加载网络配置");
                    }
                    
                    _isInitialized = true;
                }
                else
                {
                    // 每次页面加载时刷新配置，确保显示最新数据
                    if (_projectManager.CurrentProject != null)
                    {
                        LoadNetworkConfig();
                        _isModified = false;
                        System.Diagnostics.Debug.WriteLine("已刷新网络配置");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"页面加载出错: {ex.Message}");
                MessageBox.Show($"加载页面时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 注册文本变更事件以跟踪修改状态并实时保存
        /// </summary>
        private void RegisterTextChangedEvents()
        {
            // IP地址变更 - 添加实时保存
            IPAddressBox1.TextChanged += OnIPAddressChanged;
            IPAddressBox2.TextChanged += OnIPAddressChanged;
            IPAddressBox3.TextChanged += OnIPAddressChanged;
            IPAddressBox4.TextChanged += OnIPAddressChanged;

            // 子网掩码变更 - 添加实时保存
            SubnetMaskBox1.TextChanged += OnSubnetMaskChanged;
            SubnetMaskBox2.TextChanged += OnSubnetMaskChanged;
            SubnetMaskBox3.TextChanged += OnSubnetMaskChanged;
            SubnetMaskBox4.TextChanged += OnSubnetMaskChanged;

            // 网关变更 - 添加实时保存
            GatewayBox1.TextChanged += OnGatewayChanged;
            GatewayBox2.TextChanged += OnGatewayChanged;
            GatewayBox3.TextChanged += OnGatewayChanged;
            GatewayBox4.TextChanged += OnGatewayChanged;

            // 站点名称变更 - 添加实时保存
            StationNameTextBox.TextChanged += OnStationNameChanged;

            // MRP域名变更 - 添加实时保存
            MrpDomainTextBox.TextChanged += OnMrpDomainChanged;

            // 角色选择变更
            ControllerCheckBox.Checked += (s, e) => { _isModified = true; SaveNetworkConfigIfProjectExists(); };
            IntelligentDeviceCheckBox.Checked += (s, e) => { _isModified = true; SaveNetworkConfigIfProjectExists(); };

            // MRP角色变更 - 添加实时保存
            MrpRoleComboBox.SelectionChanged += OnMrpRoleChanged;

            // 发送时间变更 - 添加实时保存
            SendTimeComboBox.SelectionChanged += OnSendTimeChanged;
        }

        /// <summary>
        /// 初始化控件状态
        /// </summary>
        private void InitializeControls()
        {
            // 注册文本变更事件以跟踪修改状态
            RegisterTextChangedEvents();

            // 初始状态下的角色选项设置
            if (_projectManager.CurrentProject != null && string.IsNullOrEmpty(_projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole))
            {
                // 新建项目默认选中控制器角色
                ControllerCheckBox.IsChecked = true;
                // 触发选中事件，自动配置相关联的UI
                RoleCheckBox_Checked(ControllerCheckBox, new RoutedEventArgs());
            }
            else
            {
                // 如果不是新项目或无项目，确保初始状态下两个角色选项都未选中
                ControllerCheckBox.IsChecked = false;
                IntelligentDeviceCheckBox.IsChecked = false;
            }

            // 确保Ring Port ComboBox有默认选择
            EnsureRingPortsHaveDifferentDefaults();

            // 禁用配置区域
            UpdateConfigurationAreaState(false);
        }

        /// <summary>
        /// 更新配置区域的启用状态
        /// </summary>
        private void UpdateConfigurationAreaState(bool isEnabled)
        {
            EthernetPortGroup.IsEnabled = isEnabled;
            MediaRedundancyGroup.IsEnabled = isEnabled;
            CommunicationGroup.IsEnabled = isEnabled;
            NextButton.IsEnabled = isEnabled && _projectManager.CurrentProject != null;
        }

        /// <summary>
        /// IP地址变更事件处理
        /// </summary>
        private void OnIPAddressChanged(object sender, TextChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// 子网掩码变更事件处理
        /// </summary>
        private void OnSubnetMaskChanged(object sender, TextChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// 网关变更事件处理
        /// </summary>
        private void OnGatewayChanged(object sender, TextChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// 站点名称变更事件处理
        /// </summary>
        private void OnStationNameChanged(object sender, TextChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// MRP域名变更事件处理
        /// </summary>
        private void OnMrpDomainChanged(object sender, TextChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// MRP角色变更事件处理
        /// </summary>
        private void OnMrpRoleChanged(object sender, SelectionChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// 发送时间变更事件处理
        /// </summary>
        private void OnSendTimeChanged(object sender, SelectionChangedEventArgs e)
        {
            _isModified = true;
            SaveNetworkConfigIfProjectExists();
        }

        /// <summary>
        /// 如果项目存在则保存网络配置
        /// </summary>
        private void SaveNetworkConfigIfProjectExists()
        {
            if (_projectManager?.CurrentProject != null && _isInitialized)
            {
                try
                {
                    SaveNetworkConfig();
                    System.Diagnostics.Debug.WriteLine("网络配置已实时保存");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"实时保存网络配置时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 角色选择变更事件处理
        /// </summary>
        private void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void IPTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // 移除非数字字符
                string text = new string(textBox.Text.Where(c => char.IsDigit(c)).ToArray());
                int value;
                if (int.TryParse(text, out value))
                {
                    // 确保值在0-255范围内
                    if (value > 255)
                    {
                        textBox.Text = "255";
                        textBox.SelectionStart = textBox.Text.Length;
                    }
                    else
                    {
                        textBox.Text = value.ToString();
                    }

                    // 如果输入了3位数字，自动跳转到下一个输入框
                    if (textBox.Text.Length == 3)
                    {
                        if (textBox == IPAddressBox1) IPAddressBox2.Focus();
                        else if (textBox == IPAddressBox2) IPAddressBox3.Focus();
                        else if (textBox == IPAddressBox3) IPAddressBox4.Focus();
                    }
                }
                else if (string.IsNullOrEmpty(text))
                {
                    textBox.Text = "";
                }
            }
        }

        private void SubnetMaskTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                string text = new string(textBox.Text.Where(c => char.IsDigit(c)).ToArray());
                int value;
                if (int.TryParse(text, out value))
                {
                    if (value > 255)
                    {
                        textBox.Text = "255";
                        textBox.SelectionStart = textBox.Text.Length;
                    }
                    else
                    {
                        textBox.Text = value.ToString();
                    }

                    if (textBox.Text.Length == 3)
                    {
                        if (textBox == SubnetMaskBox1) SubnetMaskBox2.Focus();
                        else if (textBox == SubnetMaskBox2) SubnetMaskBox3.Focus();
                        else if (textBox == SubnetMaskBox3) SubnetMaskBox4.Focus();
                    }
                }
                else if (string.IsNullOrEmpty(text))
                {
                    textBox.Text = "";
                }
            }
        }

        private void GatewayTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                string text = new string(textBox.Text.Where(c => char.IsDigit(c)).ToArray());
                int value;
                if (int.TryParse(text, out value))
                {
                    if (value > 255)
                    {
                        textBox.Text = "255";
                        textBox.SelectionStart = textBox.Text.Length;
                    }
                    else
                    {
                        textBox.Text = value.ToString();
                    }

                    if (textBox.Text.Length == 3)
                    {
                        if (textBox == GatewayBox1) GatewayBox2.Focus();
                        else if (textBox == GatewayBox2) GatewayBox3.Focus();
                        else if (textBox == GatewayBox3) GatewayBox4.Focus();
                    }
                }
                else if (string.IsNullOrEmpty(text))
                {
                    textBox.Text = "";
                }
            }
        }

        public void RoleCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            UpdateConfigurationAreaState(true);
            
            // 获取MainWindow实例
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                if (mainWindow.FindName("ControllerNavItem") is ListBoxItem controllerNavItem)
                {
                    // 如果选择了控制器角色，隐藏DHCP选项，显示MRP设置，显示控制器导航项
                    if (sender == ControllerCheckBox && ControllerCheckBox.IsChecked == true)
                    {
                        DHCPRadioButton.Visibility = Visibility.Collapsed;
                        FixedIPRadioButton.IsChecked = true;
                        MediaRedundancyGroup.Visibility = Visibility.Visible;
                        
                        // 显示控制器导航项
                        controllerNavItem.Visibility = Visibility.Visible;
                        
                        // 自动填写IP设置信息
                        AutoFillIPSettings();
                    }
                    else if (sender == IntelligentDeviceCheckBox && IntelligentDeviceCheckBox.IsChecked == true)
                    {
                        DHCPRadioButton.Visibility = Visibility.Visible;
                        MediaRedundancyGroup.Visibility = Visibility.Collapsed;
                        
                        // 隐藏控制器导航项
                        controllerNavItem.Visibility = Visibility.Collapsed;
                    }
                }
            }
        }
        
        /// <summary>
        /// 当选择控制器角色时，自动填写IP设置信息
        /// </summary>
        private void AutoFillIPSettings()
        {
            // 设置默认IP地址
            IPAddressBox1.Text = "192";
            IPAddressBox2.Text = "168";
            IPAddressBox3.Text = "0";
            IPAddressBox4.Text = "1";
            
            // 设置默认子网掩码（*************）
            SubnetMaskBox1.Text = "255";
            SubnetMaskBox2.Text = "255";
            SubnetMaskBox3.Text = "255";
            SubnetMaskBox4.Text = "0";
            
            // 设置默认网关（*************）
            GatewayBox1.Text = "192";
            GatewayBox2.Text = "168";
            GatewayBox3.Text = "0";
            GatewayBox4.Text = "100";
            
            // 设置默认站名（PROFINET Driver）
            StationNameTextBox.Text = "PROFINET Driver";
            
            // 如果当前项目已有设置，则不覆盖
            if (_projectManager.CurrentProject != null)
            {
                // 如果项目中没有配置，则将当前填写的值更新到项目中
                var masterIPAddress = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress;
                if (string.IsNullOrEmpty(masterIPAddress))
                {
                    _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = $"{IPAddressBox1.Text}.{IPAddressBox2.Text}.{IPAddressBox3.Text}.{IPAddressBox4.Text}";
                }

                var masterSubnetMask = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask;
                if (string.IsNullOrEmpty(masterSubnetMask))
                {
                    _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask = $"{SubnetMaskBox1.Text}.{SubnetMaskBox2.Text}.{SubnetMaskBox3.Text}.{SubnetMaskBox4.Text}";
                }

                var masterRouterAddress = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress;
                if (string.IsNullOrEmpty(masterRouterAddress))
                {
                    _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress = $"{GatewayBox1.Text}.{GatewayBox2.Text}.{GatewayBox3.Text}.{GatewayBox4.Text}";
                }

                // General配置已移除，直接使用PNDriver的DeviceName
                var masterName = _projectManager.CurrentProject.ListOfNodesConfiguration.PNDriver.DeviceName;
                if (string.IsNullOrEmpty(masterName))
                {
                    _projectManager.CurrentProject.ListOfNodesConfiguration.PNDriver.DeviceName = StationNameTextBox.Text;
                }

                // 设置主站角色
                _projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole = "Controller";
                
                // 标记项目已修改
                _projectManager.IsProjectModified = true;
            }
        }

        /// <summary>
        /// 加载网络配置
        /// </summary>
        private void LoadNetworkConfig()
        {
            if (_projectManager.CurrentProject != null)
            {
                // 临时禁用实时保存，避免在加载时触发保存
                bool wasInitialized = _isInitialized;
                _isInitialized = false;

                try
                {
                    // 新建项目时默认设置控制器角色
                    bool isNewProject = string.IsNullOrEmpty(_projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole);
                    if (isNewProject)
                    {
                        // 设置默认角色为控制器
                        _projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole = "Controller";
                        ControllerCheckBox.IsChecked = true;
                        IntelligentDeviceCheckBox.IsChecked = false;
                        DHCPRadioButton.Visibility = Visibility.Collapsed;
                        MediaRedundancyGroup.Visibility = Visibility.Visible;

                        // 自动填写默认的IP设置
                        AutoFillIPSettings();

                        // 启用配置区域
                        UpdateConfigurationAreaState(true);

                        // 恢复初始化状态并返回
                        _isInitialized = wasInitialized;
                        return; // 自动填充默认设置后直接返回，不再继续加载
                    }

                // 加载已有项目的设置（非新建项目）
                // 设置角色复选框
                if (_projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole == "Controller")
                {
                    ControllerCheckBox.IsChecked = true;
                    IntelligentDeviceCheckBox.IsChecked = false;
                    DHCPRadioButton.Visibility = Visibility.Collapsed;
                    MediaRedundancyGroup.Visibility = Visibility.Visible;
                }
                else if (_projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole == "IntelligentDevice")
                {
                    IntelligentDeviceCheckBox.IsChecked = true;
                    ControllerCheckBox.IsChecked = false;
                    DHCPRadioButton.Visibility = Visibility.Visible;
                    MediaRedundancyGroup.Visibility = Visibility.Collapsed;
                }
                
                // 启用配置区域
                UpdateConfigurationAreaState(true);
                
                // 如果项目中已有配置，则加载配置
                var masterIPAddress = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress;
                if (!string.IsNullOrEmpty(masterIPAddress))
                {
                    string[] ipParts = masterIPAddress.Split('.');
                    if (ipParts.Length == 4)
                    {
                        IPAddressBox1.Text = ipParts[0];
                        IPAddressBox2.Text = ipParts[1];
                        IPAddressBox3.Text = ipParts[2];
                        IPAddressBox4.Text = ipParts[3];
                    }
                }

                // 使用新的配置结构获取主站名称
                var masterName = _projectManager.CurrentProject.ListOfNodesConfiguration.PNDriver.DeviceName;
                if (!string.IsNullOrEmpty(masterName))
                {
                    StationNameTextBox.Text = masterName;
                }

                // 加载子网掩码
                var masterSubnetMask = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask;
                if (!string.IsNullOrEmpty(masterSubnetMask))
                {
                    string[] subnetParts = masterSubnetMask.Split('.');
                    if (subnetParts.Length == 4)
                    {
                        SubnetMaskBox1.Text = subnetParts[0];
                        SubnetMaskBox2.Text = subnetParts[1];
                        SubnetMaskBox3.Text = subnetParts[2];
                        SubnetMaskBox4.Text = subnetParts[3];
                    }
                }

                // 加载默认网关
                var masterRouterAddress = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress;
                if (!string.IsNullOrEmpty(masterRouterAddress))
                {
                    string[] gatewayParts = masterRouterAddress.Split('.');
                    if (gatewayParts.Length == 4)
                    {
                        GatewayBox1.Text = gatewayParts[0];
                        GatewayBox2.Text = gatewayParts[1];
                        GatewayBox3.Text = gatewayParts[2];
                        GatewayBox4.Text = gatewayParts[3];
                    }
                }
                
                // 加载MRP域名
                var mrpDomain = _projectManager.CurrentProject.ProjectSpecificExtensions.MrpDomain;
                if (!string.IsNullOrEmpty(mrpDomain))
                {
                    MrpDomainTextBox.Text = mrpDomain;
                }

                // 加载MRP角色
                var masterMrpRole = _projectManager.CurrentProject.ProjectSpecificExtensions.MasterMrpRole;
                if (!string.IsNullOrEmpty(masterMrpRole))
                {
                    foreach (ComboBoxItem item in MrpRoleComboBox.Items)
                    {
                        if (item.Content.ToString() == masterMrpRole)
                        {
                            MrpRoleComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }

                // 加载环端口1
                var ringPort1 = _projectManager.CurrentProject.ProjectSpecificExtensions.RingPort1;
                if (!string.IsNullOrEmpty(ringPort1))
                {
                    foreach (ComboBoxItem item in RingPort1ComboBox.Items)
                    {
                        if (item.Content.ToString() == ringPort1)
                        {
                            RingPort1ComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }

                // 加载环端口2
                var ringPort2 = _projectManager.CurrentProject.ProjectSpecificExtensions.RingPort2;
                if (!string.IsNullOrEmpty(ringPort2))
                {
                    foreach (ComboBoxItem item in RingPort2ComboBox.Items)
                    {
                        if (item.Content.ToString() == ringPort2)
                        {
                            RingPort2ComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }

                // 加载发送时间
                var sendClock = _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.AdvancedConfiguration.RealTimeSettings.IOCommunication.SendClock;
                System.Diagnostics.Debug.WriteLine($"加载发送时间: {sendClock} ms");
                foreach (ComboBoxItem item in SendTimeComboBox.Items)
                {
                    if (double.TryParse(item.Content.ToString(), out double itemValue) && Math.Abs(itemValue - sendClock) < 0.001)
                    {
                        SendTimeComboBox.SelectedItem = item;
                        System.Diagnostics.Debug.WriteLine($"设置ComboBox选中项: {item.Content}");
                        break;
                    }
                }

                // 确保Ring Port ComboBox有不同的默认选择
                // 只在没有从项目加载到Ring Port配置时调用
                if (string.IsNullOrEmpty(ringPort1) && string.IsNullOrEmpty(ringPort2))
                {
                    EnsureRingPortsHaveDifferentDefaults();
                }
                }
                finally
                {
                    // 恢复初始化状态
                    _isInitialized = wasInitialized;
                }
            }
        }

        /// <summary>
        /// 实现INavigationAwareLeaving接口，在页面离开前保存更改
        /// </summary>
        public bool OnNavigatedFrom()
        {
            // 如果有修改，询问用户是否保存
            if (_isModified && _projectManager.CurrentProject != null)
            {
                MessageBoxResult result = MessageBox.Show(
                    "您对网络配置进行了修改，是否保存这些更改？",
                    "保存更改",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // 保存更改
                    SaveNetworkConfig();
                    System.Diagnostics.Debug.WriteLine("网络配置已保存到项目文件");
                    return true;
                }
                else if (result == MessageBoxResult.No)
                {
                    // 不保存，继续导航
                    return true;
                }
                else
                {
                    // 取消导航
                    return false;
                }
            }
            
            // 如果没有修改，允许导航
            return true;
        }

        /// <summary>
        /// 保存网络配置
        /// </summary>
        private void SaveNetworkConfig()
        {
            if (_projectManager.CurrentProject != null)
            {
                // 保存主站名称到新的配置结构
                _projectManager.CurrentProject.ListOfNodesConfiguration.PNDriver.DeviceName = StationNameTextBox.Text;
                // General配置已移除，不再需要更新

                // 保存IP地址
                _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress = $"{IPAddressBox1.Text}.{IPAddressBox2.Text}.{IPAddressBox3.Text}.{IPAddressBox4.Text}";

                // 保存子网掩码
                _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask = $"{SubnetMaskBox1.Text}.{SubnetMaskBox2.Text}.{SubnetMaskBox3.Text}.{SubnetMaskBox4.Text}";

                // 保存默认网关
                _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress = $"{GatewayBox1.Text}.{GatewayBox2.Text}.{GatewayBox3.Text}.{GatewayBox4.Text}";

                // 保存角色
                if (ControllerCheckBox.IsChecked == true)
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole = "Controller";
                }
                else if (IntelligentDeviceCheckBox.IsChecked == true)
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole = "IntelligentDevice";
                }
                else
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.MasterRole = string.Empty;
                }

                // 保存MRP角色
                if (MrpRoleComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.MasterMrpRole = selectedItem.Content.ToString();
                }

                // 保存MRP域名
                string mrpDomain = MrpDomainTextBox.Text;
                _projectManager.CurrentProject.ProjectSpecificExtensions.MrpDomain = string.IsNullOrEmpty(mrpDomain) ? string.Empty : mrpDomain;

                // 保存Ring Port 1
                if (RingPort1ComboBox.SelectedItem is ComboBoxItem selectedRingPort1Item)
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.RingPort1 = selectedRingPort1Item.Content?.ToString() ?? string.Empty;
                    System.Diagnostics.Debug.WriteLine($"保存Ring Port 1: {selectedRingPort1Item.Content}");
                }

                // 保存Ring Port 2
                if (RingPort2ComboBox.SelectedItem is ComboBoxItem selectedRingPort2Item)
                {
                    _projectManager.CurrentProject.ProjectSpecificExtensions.RingPort2 = selectedRingPort2Item.Content?.ToString() ?? string.Empty;
                    System.Diagnostics.Debug.WriteLine($"保存Ring Port 2: {selectedRingPort2Item.Content}");
                }

                // 保存发送时间
                if (SendTimeComboBox.SelectedItem is ComboBoxItem selectedSendTimeItem)
                {
                    if (double.TryParse(selectedSendTimeItem.Content.ToString(), out double sendTime))
                    {
                        _projectManager.CurrentProject.ConfigurationSettings.CentralDevice.AdvancedConfiguration.RealTimeSettings.IOCommunication.SendClock = sendTime;
                        System.Diagnostics.Debug.WriteLine($"保存发送时间: {sendTime} ms");
                    }
                }
                
                // 标记项目已修改
                _projectManager.IsProjectModified = true;
                
                // 将更改保存到磁盘
                _projectManager.SaveProject();
                
                // 重置修改状态
                _isModified = false;
            }
        }

        /// <summary>
        /// 上一步按钮点击事件
        /// </summary>
        private void PreviousStepButton_Click(object sender, RoutedEventArgs e)
        {
            // NetworkConfigPage is the first page in the wizard
            // Check if we need to save changes before navigating
            if (_isModified && _projectManager.CurrentProject != null)
            {
                MessageBoxResult result = MessageBox.Show(
                    "您对网络配置进行了修改，是否保存这些更改？",
                    "保存更改",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // 保存更改
                    SaveNetworkConfig();
                    System.Diagnostics.Debug.WriteLine("网络配置已保存");
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    // 取消导航
                    return;
                }
                // 如果选择No，则不保存继续导航
            }

            // 获取导航服务
            var navigationService = ServiceLocator.GetService<INavigationService>();
            if (navigationService == null)
            {
                MessageBox.Show("无法获取导航服务", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 由于NetworkConfigPage是第一个页面，所以没有上一页可以返回
            // 可以返回到主页面或显示消息
            MessageBox.Show("这是第一个配置步骤", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 备用上一步按钮点击事件（当前保持隐藏）
        /// </summary>
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            // 网络配置现在是第一步，所以没有上一步可以回退
            // 可以选择隐藏按钮或者显示消息
            MessageBox.Show("这是第一个配置步骤", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 下一步按钮点击事件
        /// </summary>
        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 保存网络配置
                SaveNetworkConfig();
                
                // 显示保存成功消息
                System.Diagnostics.Debug.WriteLine("网络配置已保存到项目文件");

                // 获取导航服务
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService == null)
                {
                    MessageBox.Show("无法获取导航服务", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                
                // 始终导航到控制器配置页面，遵循导航栏的顺序
                navigationService.Navigate("ControllerConfigPage");
                System.Diagnostics.Debug.WriteLine("导航到控制器配置页面");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"下一步按钮点击事件处理出错: {ex.Message}");
                MessageBox.Show($"导航时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建新项目
        /// </summary>
        private void CreateNewProject_Click(object sender, RoutedEventArgs e)
        {
            // 获取主窗口ViewModel
            if (Application.Current.MainWindow.DataContext is MainWindowViewModel viewModel)
            {
                // 使用MainWindowViewModel的CreateNewProject方法
                viewModel.NewProjectCommand.Execute(null);
            }
        }

        /// <summary>
        /// 打开项目
        /// </summary>
        private void OpenProject_Click(object sender, RoutedEventArgs e)
        {
            // 获取主窗口ViewModel
            if (Application.Current.MainWindow.DataContext is MainWindowViewModel viewModel)
            {
                // 使用MainWindowViewModel的OpenProject方法
                viewModel.OpenProjectCommand.Execute(null);
            }
        }

        /// <summary>
        /// Ring Port 1 ComboBox选择变更事件处理程序
        /// </summary>
        private void RingPort1ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedValue = selectedItem.Content?.ToString() ?? "";
                if (!ValidateRingPortSelection(comboBox, RingPort2ComboBox, selectedValue))
                {
                    // 如果选择无效，恢复到之前的选择
                    if (e.RemovedItems.Count > 0 && e.RemovedItems[0] is ComboBoxItem previousItem)
                    {
                        comboBox.SelectionChanged -= RingPort1ComboBox_SelectionChanged;
                        comboBox.SelectedItem = previousItem;
                        comboBox.SelectionChanged += RingPort1ComboBox_SelectionChanged;
                    }
                }
            }
        }

        /// <summary>
        /// Ring Port 2 ComboBox选择变更事件处理程序
        /// </summary>
        private void RingPort2ComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedValue = selectedItem.Content?.ToString() ?? "";
                if (!ValidateRingPortSelection(comboBox, RingPort1ComboBox, selectedValue))
                {
                    // 如果选择无效，恢复到之前的选择
                    if (e.RemovedItems.Count > 0 && e.RemovedItems[0] is ComboBoxItem previousItem)
                    {
                        comboBox.SelectionChanged -= RingPort2ComboBox_SelectionChanged;
                        comboBox.SelectedItem = previousItem;
                        comboBox.SelectionChanged += RingPort2ComboBox_SelectionChanged;
                    }
                }
            }
        }

        /// <summary>
        /// 验证Ring Port选择是否有效（不能与另一个ComboBox选择相同的值）
        /// </summary>
        private bool ValidateRingPortSelection(ComboBox currentComboBox, ComboBox otherComboBox, string selectedValue)
        {
            if (otherComboBox?.SelectedItem is ComboBoxItem otherSelectedItem)
            {
                string otherSelectedValue = otherSelectedItem.Content?.ToString() ?? "";
                if (selectedValue == otherSelectedValue)
                {
                    System.Diagnostics.Debug.WriteLine($"Ring Port选择冲突: 尝试选择 {selectedValue}，但另一个ComboBox已选择相同值");
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 确保Ring Port ComboBox初始化时选择不同的值
        /// </summary>
        private void EnsureRingPortsHaveDifferentDefaults()
        {
            try
            {
                // 临时移除事件处理程序
                RingPort1ComboBox.SelectionChanged -= RingPort1ComboBox_SelectionChanged;
                RingPort2ComboBox.SelectionChanged -= RingPort2ComboBox_SelectionChanged;

                // 确保有足够的选项
                if (RingPort1ComboBox.Items.Count >= 2 && RingPort2ComboBox.Items.Count >= 2)
                {
                    // 设置Ring Port 1为第一个选项
                    RingPort1ComboBox.SelectedIndex = 0;
                    // 设置Ring Port 2为第二个选项
                    RingPort2ComboBox.SelectedIndex = 1;

                    System.Diagnostics.Debug.WriteLine("Ring Port ComboBox默认值设置完成：Port1 -> 第一个选项，Port2 -> 第二个选项");
                }
            }
            finally
            {
                // 重新添加事件处理程序
                RingPort1ComboBox.SelectionChanged += RingPort1ComboBox_SelectionChanged;
                RingPort2ComboBox.SelectionChanged += RingPort2ComboBox_SelectionChanged;
            }
        }
    }
}