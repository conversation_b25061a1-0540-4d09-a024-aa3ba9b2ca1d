/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Graphic.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using GSDI;
using System;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// A Graphic carries the information for the symbolic representation of 
    /// a Device, AccessPoint, Module or Submodule.
    /// </summary>
    public class Graphic :
        GsdObject,
        GSDI.IGraphic
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Graphic if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public Graphic()
        {
            m_GraphicGsdID = String.Empty;
            m_File = String.Empty;
            m_Svg = String.Empty;
            m_Type = GSDI.GraphicTypes.GSDDeviceSymbol;
        }

        #endregion

        //########################################################################################
        #region Fields

        // Declaration of the properties
        private string m_GraphicGsdID;  // only private
        private string m_File;
        private string m_Svg;
        private GSDI.GraphicTypes m_Type;

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
		/// Accesses the  file name without file extension. The format depends on 
		/// the type of the Graphic.
		/// </summary>
        public string File => this.m_File;

        /// <summary>
        /// Accesses the SVG graphic. It is used to describe the graphical information 
        /// inside the GSDML based file in SVG format. 
        /// </summary>
        /// <remarks>Scaleable Vector Graphics (SVG) is a language for describing 
        /// two-dimensional vector and mixed vector/raster graphics in XML.</remarks>
        public string SVG => this.m_Svg;

        /// <summary>
        /// Accesses the type of the graphic.
        /// </summary>
        /// <remarks>If a DeviceSymbol type is used, the assigned Graphic shall be a 
        /// bitmap file in the DIB Format (70*40 pixels (width*height) 16 colors).
        /// If a DeviceIcon type is used, the assigned Graphic shall be a file in 
        /// icon format.</remarks>
        public GSDI.GraphicTypes Type => this.m_Type;

        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;
            string member = String.Empty;

            try
            {
                // Check parameter
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                member = Models.s_FieldFile;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_File = hash[member] as string;

                member = Models.s_FieldSvg;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_Svg = hash[member] as string;

                member = Models.s_FieldType;
                if (hash.ContainsKey(member) && hash[member] is GSDI.GraphicTypes)
                    this.m_Type = (GSDI.GraphicTypes)hash[member];

                member = Models.s_FieldGraphicGsdId;
                if (hash.ContainsKey(member) && hash[member] is string)
                    this.m_GraphicGsdID = hash[member] as string;


            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectGraphic);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteStringProperty(ref writer, Models.s_FieldGraphicGsdId, this.m_GraphicGsdID);
            Export.WriteStringProperty(ref writer, Models.s_FieldFile, this.m_File);

            // ----------------------------------------------
            writer.WriteStartElement(Export.s_ElementProperty);
            writer.WriteAttributeString(Export.s_AttributeName, Models.s_FieldSvg);
            writer.WriteAttributeString(Export.s_AttributeType, Export.s_TypeString);
            writer.WriteCData(this.m_Svg);
            writer.WriteEndElement();

            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldType, this.m_Type.ToString(), Export.s_SubtypeGraphicTypes);

            return true;
        }

        #endregion
    }
}