using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;

namespace PNConfigTool.Services
{
    /// <summary>
    /// 缓存管理器 - 提供统一的缓存管理功能
    /// </summary>
    public static class CacheManager
    {
        /// <summary>
        /// 清除所有缓存
        /// </summary>
        public static void ClearAllCaches()
        {
            try
            {
                Debug.WriteLine("开始清除所有缓存...");
                
                // 清除GSDMLService缓存
                var gsdmlService = ServiceLocator.GetService<IGSDMLService>();
                if (gsdmlService is GSDMLService service)
                {
                    service.ClearAllCaches();
                    Debug.WriteLine("GSDMLService缓存已清除");
                }
                
                // 清除GSDML解析缓存
                GSDMLCacheService.ClearCache();
                Debug.WriteLine("GSDML解析缓存已清除");
                
                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                Debug.WriteLine("所有缓存清除完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清除缓存时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取所有缓存统计信息
        /// </summary>
        public static CacheStatistics GetCacheStatistics()
        {
            try
            {
                var gsdmlService = ServiceLocator.GetService<IGSDMLService>();
                if (gsdmlService is GSDMLService service)
                {
                    var stats = service.GetCacheStatistics();
                    return new CacheStatistics
                    {
                        FileListCacheCount = stats.FileListCacheCount,
                        FileExistsCacheCount = stats.FileExistsCacheCount,
                        GSDMLCacheCount = stats.GSDMLCacheCount,
                        GSDMLMemoryEstimate = stats.GSDMLMemoryEstimate,
                        TotalCacheItems = stats.FileListCacheCount + stats.FileExistsCacheCount + stats.GSDMLCacheCount
                    };
                }
                
                return new CacheStatistics();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取缓存统计信息时出错: {ex.Message}");
                return new CacheStatistics();
            }
        }
        
        /// <summary>
        /// 预热缓存 - 在应用启动时预加载常用数据
        /// </summary>
        public static async Task WarmupCaches()
        {
            try
            {
                Debug.WriteLine("开始预热缓存...");
                
                var gsdmlService = ServiceLocator.GetService<IGSDMLService>();
                
                // 预加载GSDML文件列表
                var files = await gsdmlService.GetGSDMLFilesAsync();
                Debug.WriteLine($"预加载了 {files.Count} 个GSDML文件到缓存");
                
                // 预加载设备列表
                var devices = await gsdmlService.GetAvailableDevices();
                Debug.WriteLine($"预加载了 {devices.Count} 个设备到缓存");
                
                Debug.WriteLine("缓存预热完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"预热缓存时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 监控缓存使用情况
        /// </summary>
        public static void MonitorCacheUsage()
        {
            try
            {
                var stats = GetCacheStatistics();
                
                Debug.WriteLine("=== 缓存使用情况监控 ===");
                Debug.WriteLine($"文件列表缓存: {stats.FileListCacheCount} 项");
                Debug.WriteLine($"文件存在性缓存: {stats.FileExistsCacheCount} 项");
                Debug.WriteLine($"GSDML解析缓存: {stats.GSDMLCacheCount} 项");
                Debug.WriteLine($"总缓存项数: {stats.TotalCacheItems}");
                Debug.WriteLine($"预估内存使用: {stats.GSDMLMemoryEstimate / 1024 / 1024:F1} MB");
                Debug.WriteLine("========================");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"监控缓存使用情况时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查缓存健康状态
        /// </summary>
        public static CacheHealthStatus CheckCacheHealth()
        {
            try
            {
                var stats = GetCacheStatistics();
                var status = new CacheHealthStatus();
                
                // 检查缓存项数量
                if (stats.TotalCacheItems > 1000)
                {
                    status.IsHealthy = false;
                    status.Issues.Add("缓存项数量过多，可能影响性能");
                }
                
                // 检查内存使用
                if (stats.GSDMLMemoryEstimate > 500 * 1024 * 1024) // 500MB
                {
                    status.IsHealthy = false;
                    status.Issues.Add("GSDML缓存内存使用过高");
                }
                
                // 如果没有问题，标记为健康
                if (status.Issues.Count == 0)
                {
                    status.IsHealthy = true;
                    status.Issues.Add("缓存状态良好");
                }
                
                return status;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查缓存健康状态时出错: {ex.Message}");
                return new CacheHealthStatus
                {
                    IsHealthy = false,
                    Issues = { $"检查失败: {ex.Message}" }
                };
            }
        }
    }
    
    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        public int FileListCacheCount { get; set; }
        public int FileExistsCacheCount { get; set; }
        public int GSDMLCacheCount { get; set; }
        public long GSDMLMemoryEstimate { get; set; }
        public int TotalCacheItems { get; set; }
    }
    
    /// <summary>
    /// 缓存健康状态
    /// </summary>
    public class CacheHealthStatus
    {
        public bool IsHealthy { get; set; } = true;
        public List<string> Issues { get; set; } = new List<string>();
    }
}
