/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_032.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml.XPath;
using System.Collections;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02032 :
        BuilderV02031
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02032()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version232);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectMauTypeList:
                        {
                            // NOTE: Navigator must point to MAUTypeList.
                            this.PrepareMauTypeList(nav, ref hash);
                            obj = new C.MauTypeList();

                            break;
                        }
                    case Models.s_ObjectMauTypeItem:
                        {
                            // NOTE: Navigator must point to MAUTypeItem.
                            this.PrepareMauTypeItem(nav, ref hash);
                            obj = new C.MauTypeItem();

                            break;
                        }
                    case Models.s_ObjectChannel:
                        {
                            // NOTE: Navigator must point to Channel.
                            this.PrepareChannel(nav, ref hash);
                            obj = new C.Channel();

                            break;
                        }
                    case Models.s_ObjectData:
                        {
                            // NOTE: Navigator must point to Data.
                            this.PrepareData(nav, ref hash);
                            obj = new C.GsdData();

                            break;
                        }
                    case Models.s_ObjectQuality:
                        {
                            // NOTE: Navigator must point to Quality.
                            this.PrepareQuality(nav, ref hash);
                            obj = new C.Quality();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.
            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldIsProfIenergySupported, null);

            string attr = nav.GetAttribute(Attributes.s_ProfIenergyAseSupported, String.Empty);
            hash[Models.s_FieldIsProfIenergySupported] = Help.GetBool(attr, Attributes.s_DefaultProfIenergyAseSupported);
        }

        protected override void PrepareVirtualSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIm5Supported, null);

            // Call base class method first.
            base.PrepareVirtualSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_Im5Supported, String.Empty);
            hash[Models.s_FieldIm5Supported] = Help.GetBool(attr, Attributes.s_DefaultIm5Supported);

            attr = nav.GetAttribute(Attributes.s_MayIssueProcessAlarm, String.Empty);
            hash[Models.s_FieldMayIssueProcessAlarm] = Help.GetBool(attr, false);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ProfIenergy, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            if (nodes.Current != null)
            {
                attr = nodes.Current.GetAttribute(Attributes.s_PesaPusesProfIenergyAse, String.Empty);
            }

            hash[Models.s_FieldIsProfIenergySupported] = Help.GetBool(attr, Attributes.s_DefaultPesaPusesProfIenergyAse);
        }

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIm5Supported, null);
            hash.Add(Models.s_FieldIsProfIenergySupported, null);
            hash.Add(Models.s_FieldUsesStaticArpCacheEntries, null);
            hash.Add(Models.s_FieldDFPOutboundTruncationSupported, null);
            hash.Add(Models.s_FieldMaxRetentionTime, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_Im5Supported, String.Empty);
            hash[Models.s_FieldIm5Supported] = Help.GetBool(attr, Attributes.s_DefaultIm5Supported);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_ProfIenergy, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    attr = nodes.Current.GetAttribute(Attributes.s_PesaPusesProfIenergyAse, String.Empty);
                }

                hash[Models.s_FieldIsProfIenergySupported] = Help.GetBool(attr, Attributes.s_DefaultPesaPusesProfIenergyAse);
            }

            attr = nav.GetAttribute(Attributes.s_UsesStaticArpCacheEntries, String.Empty);
            hash[Models.s_FieldUsesStaticArpCacheEntries] = Help.GetBool(attr, Attributes.s_DefaultUsesStaticArpCacheEntries);

            // Get RTClass3Properties, optional
            nodes = nav.SelectChildren(Elements.s_RTClass3Properties, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            if (nodes.Current != null)
            {
                attr = nodes.Current.GetAttribute(Attributes.s_DFPOutboundTruncationSupported, String.Empty);
                hash[Models.s_FieldDFPOutboundTruncationSupported] = Help.GetBool(attr, Attributes.s_DefaultDFPOutboundTruncationSupported);

                attr = nodes.Current.GetAttribute(Attributes.s_MaxRetentionTime, String.Empty);
                
            }

            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldMaxRetentionTime] = System.Xml.XmlConvert.ToUInt32(attr);
            }

        }

        protected override void PrepareSystemRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldDataInvalidOnBackupArSupported, null);
            hash.Add(Models.s_FieldS2MaxInputOnBackupDelay, null);
            hash.Add(Models.s_FieldR2MaxInputOnBackupDelay, null);

            // Call base class method first.
            base.PrepareSystemRedundancy(nav, ref hash);

            string attr = nav.GetAttribute(Attributes.s_DataInvalidOnBackupArSupported, String.Empty);
            hash[Models.s_FieldDataInvalidOnBackupArSupported] = Help.GetBool(attr, Attributes.s_DefaultDataInvalidOnBackupArSupported);

            attr = nav.GetAttribute(Attributes.s_S2MaxInputOnBackupDelay, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldS2MaxInputOnBackupDelay] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_R2MaxInputOnBackupDelay, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldR2MaxInputOnBackupDelay] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        protected override void PreparePortSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMauTypeList, null);
            hash.Add(Models.s_FieldIm5Supported, null);

            // Call base class method first.
            base.PreparePortSubmodule(nav, ref hash);

            // Navigate to MAUTypeList and create it. Required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_MauTypeList, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectMauTypeList, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMauTypeList + "' couldn't be created!");
            }
            hash[Models.s_FieldMauTypeList] = obj;

            string attr = nav.GetAttribute(Attributes.s_Im5Supported, String.Empty);
            hash[Models.s_FieldIm5Supported] = Help.GetBool(attr, Attributes.s_DefaultIm5Supported);

            attr = nav.GetAttribute(Attributes.s_CheckMauTypeDifferenceSupported, String.Empty);
            if (!string.IsNullOrEmpty(attr))
                hash[Models.s_FieldCheckMauTypeDifferenceSupported] = Help.GetBool(attr, false);

        }

        protected virtual void PrepareMauTypeList(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare MAUTypeList data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldExtensionSupported, null);
            hash.Add(Models.s_FieldMauTypeItems, null);

            // Get ExtensionSupported attribute. Conditional.
            string attr = nav.GetAttribute(Attributes.s_ExtensionSupported, String.Empty);
            hash[Models.s_FieldExtensionSupported] = Help.GetBool(attr, Attributes.s_DefaultExtensionSupported);

            // Navigate to MAUTypeItem elements and create it. One or more.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_MauTypeItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = CreateGsdObject(Models.s_ObjectMauTypeItem, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectMauTypeItem + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldMauTypeItems] = list;
        }

        protected virtual void PrepareMauTypeItem(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldAdjustSupported, null);
            hash.Add(Models.s_FieldValue, null);
            hash.Add(Models.s_FieldExtension, null);

            string attr = nav.GetAttribute(Attributes.s_AdjustSupported, String.Empty);
            hash[Models.s_FieldAdjustSupported] = Help.GetBool(attr, Attributes.s_DefaultAdjustSupported);

            attr = nav.GetAttribute(Attributes.s_Value, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldValue] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_Extension, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldExtension] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        protected override void PrepareIOData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare IOData data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldInputChannels, null);
            hash.Add(Models.s_FieldOutputChannels, null);

            // Call base class method first.
            base.PrepareIOData(nav, ref hash);

            // Navigate to data items and create them.
            // Select input data items.
            object obj = null;
            ArrayList list = null;
            XPathNavigator tempnav = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Input, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                tempnav = nodes.Current;
                if (tempnav != null)
                {
                    nodes = tempnav.SelectChildren(Elements.s_Channel, Namespaces.s_GsdmlDeviceProfile);
                }

                // Create each found data item. Optional.
                while (nodes.MoveNext())
                {
                    // Create list.
                    if (null == list)
                        list = new ArrayList(nodes.Count);

                    // Create data record itself.
                    obj = this.CreateGsdObject(Models.s_ObjectChannel, nodes.Current);
                    if (null == obj)
                        throw new CreationException("Object '" + Models.s_ObjectChannel + "' couldn't be created!");

                    // Add it to the list.
                    list.Add(obj);
                }

                hash[Models.s_FieldInputChannels] = list;
            }

            // Select output data items.
            list = null;
            nodes = nav.SelectChildren(Elements.s_Output, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            tempnav = nodes.Current;

            if (tempnav != null)
            {

                nodes = tempnav.SelectChildren(Elements.s_Channel, Namespaces.s_GsdmlDeviceProfile);
            }

            // Create each found data item. Optional.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data record itself.
                obj = this.CreateGsdObject(Models.s_ObjectChannel, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectChannel + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }

            hash[Models.s_FieldOutputChannels] = list;

        }

        protected virtual void PrepareChannel(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for Channel object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldChannelNumber, null);
            hash.Add(Models.s_FieldData, null);
            hash.Add(Models.s_FieldQuality, null);


            string attr = nav.GetAttribute(Attributes.s_Number, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldChannelNumber] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // --------------------------------------------

            // Navigate to Data and create it. Required.
            object obj = null;
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Data, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectData, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectData + "' couldn't be created!");
            }
            hash[Models.s_FieldData] = obj;

            // Navigate to Quality and create it. Required.
            nodes = nav.SelectChildren(Elements.s_Quality, Namespaces.s_GsdmlDeviceProfile);
            if (nodes.MoveNext())
            {
                obj = this.CreateGsdObject(Models.s_ObjectQuality, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectQuality + "' couldn't be created!");
            }
            hash[Models.s_FieldQuality] = obj;
        }

        protected virtual void PrepareData(XPathNavigator nav, ref Hashtable hash)
        {
            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldBitOffset, null);
            hash.Add(Models.s_FieldBitLength, null);


            string attr = nav.GetAttribute(Attributes.s_BitOffset, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldBitOffset] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_BitLength, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldBitLength] = System.Xml.XmlConvert.ToUInt32(attr);
            }
        }

        protected virtual void PrepareQuality(XPathNavigator nav, ref Hashtable hash)
        {
            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldBitOffset, null);
            hash.Add(Models.s_FieldFormat, null);
            hash.Add(Models.s_FieldOppositeDirection, null);


            string attr = nav.GetAttribute(Attributes.s_BitOffset, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldBitOffset] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            attr = nav.GetAttribute(Attributes.s_Format, String.Empty);
            hash[Models.s_FieldFormat] = attr;

            attr = nav.GetAttribute(Attributes.s_OppositeDirection, String.Empty);
            hash[Models.s_FieldOppositeDirection] = Help.GetBool(attr, Attributes.s_DefaultOppositeDirection);
        }

        protected override void PrepareUnitDiagnosticType(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare UnitDiagnosticType data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldName, null);
            hash.Add(Models.s_FieldNameTextId, null);
            hash.Add(Models.s_FieldHelp, null);
            hash.Add(Models.s_FieldHelpTextId, null);

            // Call base class method first.
            base.PrepareUnitDiagnosticType(nav, ref hash);

            // --------------------------------------------------------------
            // Get Name. Required.
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_Name, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
                throw new PreparationException("Element '" + Elements.s_Name + "' isn't available from the '" + nav.LocalName + "' element!");
            hash[Models.s_FieldName] = this.GetText(nodes.Current);
            if (nodes.Current == null)
            {
                return;
            }
            hash[Models.s_FieldNameTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);

            // --------------------------------------------------------------
            // Get Help. Optional.
            nodes = nav.SelectChildren(Elements.s_Help, Namespaces.s_GsdmlDeviceProfile);
            if (!nodes.MoveNext())
            {
                return;
            }

            hash[Models.s_FieldHelp] = this.GetText(nodes.Current);

            if (nodes.Current != null)
            {
                hash[Models.s_FieldHelpTextId] = nodes.Current.GetAttribute(Attributes.s_TextId, String.Empty);
            }
        }

        #endregion

        #endregion

    }
}
