/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Port.cs                                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;

namespace PNConfigLib.PNPlannerAdapter.DataClasses
{
    /// <summary>
    /// PNPlanner struct that represents a Port.
    /// </summary>
    internal struct Port: IEquatable<Port>
    {
        /// <summary>
        /// Whether port is fiber optic.
        /// </summary>
        public bool IsFiberOptic;

        /// <summary>
        /// The port number.
        /// </summary>
        public int Number;

        /// <summary>
        /// Delay of receiver part of the port in nanoseconds.
        /// </summary>
        public long RxDelay;

        /// <summary>
        /// Whether port supports short preamble.
        /// </summary>
        public bool ShortPreamble100MBitSupported;

        /// <summary>
        /// Delay of sender part of the port in nanoseconds.
        /// </summary>
        public long TxDelay;

        #region Implementation of IEquatable<Port>

        /// <summary>
        /// Indicates whether the current object is equal to another object of the same type.
        /// </summary>
        /// <returns>
        /// true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
        /// </returns>
        /// <param name="other">An object to compare with this object.</param>
        public bool Equals(Port other)
        {
            if ((other.IsFiberOptic == IsFiberOptic)
                && (other.ShortPreamble100MBitSupported == ShortPreamble100MBitSupported)
                && (other.Number == Number)
                && (other.RxDelay == RxDelay)
                && (other.TxDelay == TxDelay))
            {
                return true;
            }
            return false;
        }

        #endregion
    }
}