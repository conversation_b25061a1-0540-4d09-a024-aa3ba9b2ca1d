﻿/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: DcpReadOnlyHelper.cs                      :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Utilities;
using System;
using System.Collections.Generic;
using System.Text;

namespace PNConfigLib.BusinessLogic.HWCNBL
{
    internal class DcpReadOnlyHelper : IDcpReadOnlyHelper
    {
        #region Fields
        private readonly bool m_DcpEnableReadOnly;
        #endregion

        #region Properties
        public bool IsDcpEnableReadOnly
        {
            get
            {
                return m_DcpEnableReadOnly;
            }
        }

        #endregion
        #region Construction
        public DcpReadOnlyHelper(Interface interfaceSubmodule)
        {
            m_DcpEnableReadOnly = IsControllerCompatible(interfaceSubmodule)
                                  && interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                                      InternalAttributeNames.ActivateDcpReadOnly,
                                      new AttributeAccessCode(),
                                      false);

        }
        #endregion

        #region Private Methods
        private bool IsControllerCompatible(Interface interfaceSubmodule)
        {
            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);

            if (controllerInterfaceSubmodule != null && IsPnSecurityClassSupportedOnController(controllerInterfaceSubmodule))
            {
                return true;
            }

            return false;
        }

        private bool IsPnSecurityClassSupportedOnController(Interface controllerInterfaceSubmodule)
        {
            return controllerInterfaceSubmodule.AttributeAccess
                .GetAnyAttribute(InternalAttributeNames.PnDCPReadOnlySupported, new AttributeAccessCode(), false);
        }
        #endregion
    }
}