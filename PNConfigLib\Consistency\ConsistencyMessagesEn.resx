<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllPortsDeactivated" xml:space="preserve">
    <value>At least one port must be activated on device '{0}'.</value>
  </data>
  <data name="CalculatedBandwidthExceededMaximum" xml:space="preserve">
    <value>{0}: Calculated bandwidth exceeds maximum bandwidth for cyclical IO data for at least one IO cycle.</value>
  </data>
  <data name="CalculatedTotalBandwidthExceededMaximum" xml:space="preserve">
    <value>{0}: The calculated bandwidth for cyclic IO data exceeds the maximum possible bandwidth for cyclic IO data.</value>
  </data>
  <data name="IoDNotAssignedToController" xml:space="preserve">
    <value>{0} is not assigned to an IO controller.</value>
  </data>
  <data name="XML_CentralDeviceVariantsNotSupported" xml:space="preserve">
    <value>PNConfigLib doesn't support interface : {0} for current version.</value>
  </data>
  <data name="Clocksync_IOSystemIsNotAssignedToOB6x" xml:space="preserve">
    <value>An isochronous mode interrupt must be configured on the CPU for the IO system '{0}'.</value>
  </data>
  <data name="XML_CollisionForPhysicalSubslot" xml:space="preserve">
    <value>There is a collision for the physical subslot {0}.</value>
  </data>
  <data name="XML_CollisionInIOAddress" xml:space="preserve">
    <value>Collision in {0} IO addresses, check IO addresses which have start values {1} and {2} for IO system : {3}</value>
  </data>
  <data name="ControllerErrorMaxIAddressWithinAString" xml:space="preserve">
    <value>The max. input address range ({0} bytes) was exceeded. {1} bytes are configured.</value>
  </data>
  <data name="ControllerErrorMaxOAddressWithinAString" xml:space="preserve">
    <value>The max. output address range ({0} bytes) was exceeded. {1} bytes are configured.</value>
  </data>
  <data name="ControllerErrorMaxSubmoduleCount" xml:space="preserve">
    <value>The maximum number of submodules ({0}) was exceeded on the PN interface of an IO controller. {1} submodules too many were configured.</value>
  </data>
  <data name="ControllerErrorMaxSubmoduleDataLength" xml:space="preserve">
    <value>The maximum size of a submodule for this IO controller ({0} bytes) was exceeded by {1} ({2} bytes).</value>
  </data>
  <data name="ControllerNotSynchronized" xml:space="preserve">
    <value>Synchronized IO data is established on the central PN interface '{0}' (for example synchronized IO devices) and the central PN interface is unsynchronized.</value>
  </data>
  <data name="DeviceExchangeWithoutMMC" xml:space="preserve">
    <value>The device replacement without exchangeable medium function cannot be used for device '{0}' without configuration of topology.</value>
  </data>
  <data name="DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET" xml:space="preserve">
    <value>Isochronous mode is enabled on device '{0}' although IRT (high performance) is not set. Change the RT class and reselect the OB6x assignment.</value>
  </data>
  <data name="DifferentSlotModels" xml:space="preserve">
    <value>The IO device cannot be operated with the firmware version of the IO controller.</value>
  </data>
  <data name="DisabledPortUsed" xml:space="preserve">
    <value>Inactive port is used in ring connection. Please remove the port from the ring.</value>
  </data>
  <data name="ErrorIE_IPRouteAddressDifferent" xml:space="preserve">
    <value>The router address differs from router address of {0}.</value>
  </data>
  <data name="ERROR_INVALID_TITO_RANGE" xml:space="preserve">
    <value>The CPU {0} cannot supply the process values for {1}.</value>
  </data>
  <data name="FalsePIPAssignment" xml:space="preserve">
    <value>The submodule/module '{0}' in slot = {1} has an incorrect process image assignment or none at all.</value>
  </data>
  <data name="FastStartupDeviceWithoutIOSystem" xml:space="preserve">
    <value>The 'prioritized startup' option is enabled on IO device {0} although the IO device is not connected to any IO system.</value>
  </data>
  <data name="FastStartupMaxCountExceeded" xml:space="preserve">
    <value>A maximum of {1} IO devices may be prioritized on IO controller {0}.</value>
  </data>
  <data name="IDeviceSendclockNotValid" xml:space="preserve">
    <value>If the device is configured as an I-device, the setting of the cycle time is only permitted if the I-device was configured without a subordinate IO device and the parameters are assigned by the higher-level IO controller.</value>
  </data>
  <data name="IncorrectReductionRatioWithSmallSC" xml:space="preserve">
    <value>The ratio of update time to send clock must be 1 if the send clock is shorter than 250 µs.</value>
  </data>
  <data name="InputGrossFrameLengthExceeded" xml:space="preserve">
    <value>The gross amount of input data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes.</value>
  </data>
  <data name="InputNetFrameLengthExceeded" xml:space="preserve">
    <value>The net amount of input data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes.</value>
  </data>
  <data name="InterconnectedPortDeactivated" xml:space="preserve">
    <value>Port '{0}' is interconnected but disabled.</value>
  </data>
  <data name="InterfaceSubmodulePortsDeactivated" xml:space="preserve">
    <value>At least one port must be activated on device '{0}'.</value>
  </data>
  <data name="InvalidIOSystemNumber" xml:space="preserve">
    <value>The number of the IO system must be between {0} and {1}.</value>
  </data>
  <data name="InvalidWatchdogFactor" xml:space="preserve">
    <value>The watchdog factor '{0}' cannot be used.</value>
  </data>
  <data name="IOControllerDoesNotSupportIODevice" xml:space="preserve">
    <value>The selected IO device requires the IO controller to support advanced PROFINET diagnostics. The configured IO controller, however, does not support advanced PROFINET diagnostics.</value>
  </data>
  <data name="XML_IoIpConfigMode" xml:space="preserve">
    <value>AddressAssignment is LOCAL in the device {0}'s gsdml file. IPProtocol must be configured as IPAddressIsSetDirectlyAtTheDevice in the configuration xml file.</value>
  </data>
  <data name="XML_InterfaceDoesNotExist" xml:space="preserve">
    <value>Device: {0} has interface: {1} in the Configuration.xml file but this device cannot have an interface according to its GSD file.</value>
  </data>
  <data name="XML_InterfaceParameterizationDisallowed" xml:space="preserve">
    <value>Device: {0} has interface: {1} in the Configuration.xml file but parametrization disallowed on this device.</value>
  </data>
  <data name="IoSystemPNIpConfigIPAddressViaOtherPath" xml:space="preserve">
    <value>The option 'IP address via other method' should not be set if a PNIO system is used on this interface.</value>
  </data>
  <data name="Irt_With_Deactivated_Port" xml:space="preserve">
    <value>The interconnected port within an IRT line may not be deactivated.</value>
  </data>
  <data name="IsoCacfNotValid" xml:space="preserve">
    <value>CACF value of the clock synchronization of the IO system is not valid. Check the value of the application cycle of {0}.</value>
  </data>
  <data name="ISOCHRONOUS_DISABLED_AT_INTERFACE" xml:space="preserve">
    <value>Isochronous mode must be enabled for the PN interface because there are submodules operating in isochronous mode.</value>
  </data>
  <data name="ISOCHRONOUS_REQUIRED_AT_SUBMODUL" xml:space="preserve">
    <value>Isochronous mode must be enabled on submodule, because the submodule is operating in isochronous mode.</value>
  </data>
  <data name="IsochronPIPHasModulesFromDifferentMastersystems" xml:space="preserve">
    <value>The isochronous PIP {0} may not have modules from {1}.</value>
  </data>
  <data name="IsochronPIPRequiresIsochronSubmodule" xml:space="preserve">
    <value>The isochronous process image partition PIP {0} must have at least one isochronous IO submodule.</value>
  </data>
  <data name="IsoCouplingNotSupported" xml:space="preserve">
    <value>Isochronous mode is activated on device {0} even though the assigned controller {1} does not support isochronous OB coupling.</value>
  </data>
  <data name="PNPlannerMultipleIslands" xml:space="preserve">
    <value>There is more than one IRT top island in the sync domain. Possibly missing Port interconnection between Sync Master and Redundant Sync Master.</value>
  </data>
  <data name="PNPlannerToolChangerError" xml:space="preserve">
    <value>There are alternative partners configured on port {1}. No port with IRT and routing may be connected downstream from this port.</value>
  </data>
  <data name="MaxNoIrFrameDataExceeded" xml:space="preserve">
    <value>The maximum number of forwarded frames for '{0}' has been exceeded. The interface of the submodule only supports up to {1} frames for forwarding.</value>
  </data>
  <data name="MaxRangeIRFrameIDOutOfRange" xml:space="preserve">
    <value>The ID value range of the IRT frames by the interface {0} exceeds the maximum value supported by this interface.</value>
  </data>
  <data name="MaxStationNumberExceeded" xml:space="preserve">
    <value>The device number {0} exceeds the max. possible device number {1}.</value>
  </data>
  <data name="MaxWatchdog" xml:space="preserve">
    <value>The watchdog time on IO device {0} must not exceed {1} ms.</value>
  </data>
  <data name="Max_SyncSlaveNumber_Overstepped" xml:space="preserve">
    <value>There are {0} IRT devices connected to the {1} device but it can only control {2} IRT devices in the current configuration.</value>
  </data>
  <data name="ControllerErrorMaxSharedRTC3Provider" xml:space="preserve">
    <value>There are {0} IRT Provider Communication Relations genereted in the {1} interface device but it can only control {2} Provider Communication Relations.</value>
  </data>
  <data name="ControllerErrorMaxSharedRTC3Consumer" xml:space="preserve">
    <value>There are {0} IRT Consumer Communication Relations genereted in the {1} device, but it can only control {2} Consumer Communication Relations.</value>
  </data>
  <data name="ControllerErrorMaxSharedRTC1Provider" xml:space="preserve">
    <value>There are {0} RT Provider Communication Relations genereted in the {1} device, but it can only control {2} RT Provider Communication Relations.</value>
  </data>
  <data name="ControllerErrorMaxSharedRTCXProvider" xml:space="preserve">
    <value>There are {0} Provider Communication Relations genereted in the {1} device, but it can only control {2} Provider Communication Relations.</value>
  </data>
  <data name="ControllerErrorMaxSharedRTCXConsumer" xml:space="preserve">
    <value>There are {0} Consumer Communication Relations genereted in the {1} device, but it can only control {2} Consumer Communication Relations.</value>
  </data>
  <data name="MixedOperation_IRTTop_IRTFlex_at_SyncDomain" xml:space="preserve">
    <value>Prohibited mixed operation by devices with IRT option
high flexibility and IRT option high performance
in sync domain {0} on subnet
{1}.</value>
  </data>
  <data name="XML_ModuleIsNotPluggableForDevice" xml:space="preserve">
    <value>The module '{0}' is not a pluggable for decentral device {1}, and module id is {2}.</value>
  </data>
  <data name="ModuleWithoutSubmodule" xml:space="preserve">
    <value>Module '{0}' requries at least one submodule.</value>
  </data>
  <data name="XML_MrpDomainRefIdConsistency" xml:space="preserve">
    <value>MrpDomainRefID: {0} of Device: {1}, couldn't found in MrpDomains.</value>
  </data>
  <data name="MrpDomainUsedInDifferentInstances" xml:space="preserve">
    <value>Instances '{0}' and '{1}' from '{2}' cannot belong to the same domain '{3}'.</value>
  </data>
  <data name="MrpFastStartupNotAllowed" xml:space="preserve">
    <value>Both media redundancy and 'prioritized startup' are enabled on module '{0}'. This combination can result in longer startup times.</value>
  </data>
  <data name="XML_MrpInstanceNumberConsistency" xml:space="preserve">
    <value>Available InstanceNumber value(s) : {0} for the device {1} in mrp ring {2}</value>
  </data>
  <data name="XML_MrpInstanceNumberUniqueness" xml:space="preserve">
    <value>Mrp ring InstanceNumber values must be unique for device : {0}</value>
  </data>
  <data name="MrpManagerAndManagerAuto" xml:space="preserve">
    <value>MRP domain cannot contain devices with the 'Manager' role and 'Manager(auto)' role at the same time.</value>
  </data>
  <data name="MrpMaxSizeExceeded" xml:space="preserve">
    <value>The maximum permitted number of interfaces in the ring is exceeded in the MRP domain.</value>
  </data>
  <data name="MrpMoreThanOneManager" xml:space="preserve">
    <value>MRP domain can only contain one device with the 'Manager' role.</value>
  </data>
  <data name="MrpMoreThanOneRing" xml:space="preserve">
    <value>More than one MRP ring is fully or partially configured in the MRP domain {0}. This is not supported.</value>
  </data>
  <data name="MrpNoManager" xml:space="preserve">
    <value>A manager must be configured in an MRP domain.</value>
  </data>
  <data name="MrpNoNetwork" xml:space="preserve">
    <value>Device '{0}' must be connected to a network for MRP.</value>
  </data>
  <data name="MrpPortEndOfTopologyDiscovery" xml:space="preserve">
    <value>The 'End of topology discovery' option is set for ring port '{0}'. This is not permitted for MRP managers.</value>
  </data>
  <data name="MrpPortInterconnDifferentSubnet" xml:space="preserve">
    <value>Ring ports '{0}' and '{1}' are interconnected and are located in different subnets</value>
  </data>
  <data name="MrpPortInterconnNonRingPort" xml:space="preserve">
    <value>Ring port '{0}' is connected to port '{1}', which is not a ring port.</value>
  </data>
  <data name="MrpPortShouldBeSelected" xml:space="preserve">
    <value>Port '{0}' is a non-configurable default ring port. It must be selected as one of the ring ports.</value>
  </data>
  <data name="MrpPortTransferRateNotAllowed" xml:space="preserve">
    <value>Please make sure that in active MRP mode, the interconnected ring port {0} is configured with the automatic setting or at least 100 Mbps full duplex.</value>
  </data>
  <data name="XML_MrpRingCount" xml:space="preserve">
    <value>Device: {0}, has {1} instance(s) but {2} of them defined.</value>
  </data>
  <data name="XML_MrpRingInstanceNumberIsSpecified" xml:space="preserve">
    <value>All the instance numbers must be deifned for the device {0} which have multiple mrp instances</value>
  </data>
  <data name="MrpRingPortUsedInDifferentInstances" xml:space="preserve">
    <value>Ring port '{0}' is used in more than one instance</value>
  </data>
  <data name="MrpRoleIsNotSupported" xml:space="preserve">
    <value>Selected MRP role '{0}' is not supported in simple MRP operation '{1}'.</value>
  </data>
  <data name="MrpRoleOutsideTheRing" xml:space="preserve">
    <value>{0} is not part of the ring, but the MRP role is configured.</value>
  </data>
  <data name="MrpSyncNotAllowed" xml:space="preserve">
    <value>Synchronization and media redundancy are enabled on module '{0}'. This is not permitted.</value>
  </data>
  <data name="MrpWrongPortNumber" xml:space="preserve">
    <value>In '{0}', exactly 2 ports must be selected as ring ports.</value>
  </data>
  <data name="MultipleMrpRoleNotSupported" xml:space="preserve">
    <value>The selected multiple MRP role '{0}' in instance '{1}' from '{2}' is not supported in multi-MRP operation.</value>
  </data>
  <data name="NoCommonStartupFound" xml:space="preserve">
    <value>The controller {0} does not have a mode for normal startup of the device {1}.</value>
  </data>
  <data name="NodeIeNoProtocol" xml:space="preserve">
    <value>No protocol selected for Ethernet interface {0}.</value>
  </data>
  <data name="NodeIpRouterOtherThanIpAddress" xml:space="preserve">
    <value>IP address {1} and default router IP address {2} of interface {0} are in different subnets. (Subnet mask: {3})</value>
  </data>
  <data name="NodePNNoSInvalid" xml:space="preserve">
    <value>The converted name of the device in network {0} could not be generated.</value>
  </data>
  <data name="NodePNNoSNotUnique" xml:space="preserve">
    <value>PROFINET device name {0} is not unique.</value>
  </data>
  <data name="NoSActiveButIPSuiteNotActive" xml:space="preserve">
    <value>The 'PROFINET device name is set directly at the device' option must be used along with the 'IP address is set directly at the device' option.</value>
  </data>
  <data name="NoSViaOtherPathActiveWithIRTTop" xml:space="preserve">
    <value>If the 'PROFINET device name is set directly at the device' option is enabled, IRTtop data exchange cannot be established on this interface.</value>
  </data>
  <data name="NoUpdateTime" xml:space="preserve">
    <value>No update time can be calculated for IO device '{0}'.</value>
  </data>
  <data name="ObControllerAppCycleTooHigh" xml:space="preserve">
    <value>The value set on OB {0}!d! for the controller application cycle = {1} µs is too high. The value is limited by /PROFIdrive/ to a maximum of 32000 µs.</value>
  </data>
  <data name="OutputGrossFrameLengthExceeded" xml:space="preserve">
    <value>The gross amount of output data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes.</value>
  </data>
  <data name="OutputNetFrameLengthExceeded" xml:space="preserve">
    <value>The net amount of output data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes.</value>
  </data>
  <data name="XML_ParameterizationDisallowed_InterfaceOptions" xml:space="preserve">
    <value>The given interface options are not valid for the device : {0} because parametrization disallowed in the gsdml file.</value>
  </data>
  <data name="XML_InterfaceNotSupported_InterfaceOptions" xml:space="preserve">
    <value>The given interface options are not valid for the device : {0} because it does not have an interface submodule in the gsdml file.</value>
  </data>
  <data name="XML_ParameterizationDisallowed_SyncRole" xml:space="preserve">
    <value>The given synchronization role is not valid for the device : {0} because parametrization disallowed in the gsdml file.</value>
  </data>
  <data name="XML_InterfaceNotSupported_SyncRole" xml:space="preserve">
    <value>The given synchronization role is not valid for the device : {0} because it does not have an interface submodule in the gsdml file.</value>
  </data>
  <data name="PIPAssignmentWithIsochronActivated" xml:space="preserve">
    <value>The assignment of the submodules/modules {0} in slot = {1} to the process image partition = {2} is only permitted if isochronous mode is activated on the submodules/modules.</value>
  </data>
  <data name="PNCONTROLLER_ERROR_MAX_DEVICES" xml:space="preserve">
    <value>The maximum permitted number of IO devices ({0}) has been exceeded by {1}.</value>
  </data>
  <data name="PNDriverPrioStartupNotAllowedError" xml:space="preserve">
    <value>PROFINET Driver does not support port interconnections with IO device interfaces when prioritized startup is enabled.</value>
  </data>
  <data name="PNDriverPrioStartupNotAllowedWarning" xml:space="preserve">
    <value>{0} may contain port interconnections to IO device interfaces with enabled prioritized startup. Interconnecting these ports can result in problems during runtime.</value>
  </data>
  <data name="PortDifferentAutoNegotiation" xml:space="preserve">
    <value>The autonegotiation option setting must be identical on port {0} and port {1}.</value>
  </data>
  <data name="XML_PortForModuleDoesNotExist" xml:space="preserve">
    <value>The port {0} does not exist in the context.</value>
  </data>
  <data name="PortIrtLineLengthNotSpecified" xml:space="preserve">
    <value>The signal runtime for port '{0}' cannot be 0.</value>
  </data>
  <data name="XML_PortIsUsedMoreThanOneInTopology" xml:space="preserve">
    <value>The port {0} is defined as LocalPort for multiple times in topology.</value>
  </data>
  <data name="PortMauNotSetOrAutonegotiationActivated" xml:space="preserve">
    <value>If prioritized startup is enabled, it is a good idea to enter a specific transfer rate / Duplex and to disable autonegotiation at port {0}/{1}.</value>
  </data>
  <data name="PortMayNotBeBoundaryError" xml:space="preserve">
    <value>The 'end of synchronization domain' option must not be enabled on port {0}.</value>
  </data>
  <data name="PortMediumDifferent" xml:space="preserve">
    <value>The attachment type of the local port {0} is different from the attachment type of the partner port {1}.</value>
  </data>
  <data name="XML_PortNumberUsedMultipleTimes" xml:space="preserve">
    <value>The port number {0} is used multiple times.</value>
  </data>
  <data name="XML_PortSubmoduleNotHavePhysicalSubslot" xml:space="preserve">
    <value>The physical subslot {0} is not available for the port {1}.</value>
  </data>
  <data name="PortTransferRateIsDifferent" xml:space="preserve">
    <value>The same transmission rate/duplex must be set on port {0} and port {1}.</value>
  </data>
  <data name="PortTransferRateIsDifferentError" xml:space="preserve">
    <value>The same transmission rate/duplex setting should be made on port {0} and port {1}.</value>
  </data>
  <data name="RingWithMrpSelectedPortMismatch" xml:space="preserve">
    <value>{0} is configured in a ring network, but its selected ring ports don't match with the ring ports of the ring network. This is not allowed.</value>
  </data>
  <data name="RingWithoutActiveMrp" xml:space="preserve">
    <value>{0} is configured in a ring network, but the role 'Not device in the ring' is selected in its media redundancy settings. This is not allowed.</value>
  </data>
  <data name="RingWithoutMediaRedundancy" xml:space="preserve">
    <value>{0} is configured in a ring network but it does not support any media redundancy protocol. This is not allowed.</value>
  </data>
  <data name="RingWithUnknownMediaRedundancy" xml:space="preserve">
    <value>{0} belongs to a ring structure that might not be allowed.</value>
  </data>
  <data name="SendclockInvalidInSyncDomain" xml:space="preserve">
    <value>The selected send clock is invalid in the sync domain.</value>
  </data>
  <data name="SendclockNotApplicable" xml:space="preserve">
    <value>If the IO controller is synchronized, the IO controller must support the send clock of the synchronization domain.</value>
  </data>
  <data name="SendclockNotValid" xml:space="preserve">
    <value>The setting '{0}' ms is not possible for the send clock at IO controller '{1}'.</value>
  </data>
  <data name="SharedDevice_IsoModuleNotPossibleWithSharedIF" xml:space="preserve">
    <value>Isochronous mode is not possible on module {0} because access is set to 'not assigned' for the {1}\{2}.</value>
  </data>
  <data name="SharedDevice_IsoNotPossibleWithSharedModule" xml:space="preserve">
    <value>Isochronous mode is not possible on module {0} because access is set to 'not assigned'.</value>
  </data>
  <data name="SignalDelayTimeLessThanMinValue" xml:space="preserve">
    <value>Minimum value for the signal delay is {0} µs!</value>
  </data>
  <data name="SignalDelayTimeMoreThanMaxValue" xml:space="preserve">
    <value>Maximum value for the signal delay is {1} µs for '{0}'!</value>
  </data>
  <data name="XML_SlotIsNotAvailableForModule" xml:space="preserve">
    <value>Slot number {0} is not available for module {1}.</value>
  </data>
  <data name="XML_SlotNumberIsUsedByMoreThanOneModule" xml:space="preserve">
    <value>Slot number {0} is used from more than one module.</value>
  </data>
  <data name="XSD_ValidationError" xml:space="preserve">
    <value>{0} file failed XSD validation: {1}</value>
  </data>
  <data name="XML_DefaultIOSystemForMultipleIOC" xml:space="preserve">
    <value>Can not create default IO systems for multiple central devices.</value>
  </data>
  <data name="XML_DeviceRefIDWithoutCentralDevice" xml:space="preserve">
    <value>DeviceRefID of central device not found in list of nodes: {0}</value>
  </data>
  <data name="XML_DeviceRefIDWithoutDecentralDevice" xml:space="preserve">
    <value>DeviceRefID of decentral device not found in list of nodes: {0}</value>
  </data>
  <data name="XML_EmptyProject" xml:space="preserve">
    <value>No devices connected to any subnet; can not compile empty project.</value>
  </data>
  <data name="XML_IncorrectGSDRefIDDevice" xml:space="preserve">
    <value>Incorrect GSDRefID '{0}' for decentral device '{1}'</value>
  </data>
  <data name="XML_IncorrectGSDRefIDModule" xml:space="preserve">
    <value>Incorrect GSDRefID '{0}' for decentral device '{1}' module '{2}'</value>
  </data>
  <data name="XML_IncorrectGSDRefIDPort" xml:space="preserve">
    <value>Incorrect GSDRefID '{0}' for decentral device '{1}' port '{2}'</value>
  </data>
  <data name="XML_IncorrectGSDRefIDSubmodule" xml:space="preserve">
    <value>Incorrect GSDRefID '{0}' for decentral device '{1}' module '{2}' submodule '{3}'</value>
  </data>
  <data name="XML_IncorrectInterfaceRefID" xml:space="preserve">
    <value>Incorrect InterfaceRefID in port interconnection: {0}</value>
  </data>
  <data name="XML_IncorrectListOfNodesRefIDInConfig" xml:space="preserve">
    <value>Incorrect ListOfNodesRefID in Configuration xml.</value>
  </data>
  <data name="XML_IncorrectListOfNodesRefIDInTopology" xml:space="preserve">
    <value>Incorrect ListOfNodesRefID in Topology xml.</value>
  </data>
  <data name="XML_IncorrectTopologyRefIDInConfig" xml:space="preserve">
    <value>Incorrect TopologyRefID in Configuration xml.</value>
  </data>
  <data name="XML_InvalidNamePort" xml:space="preserve">
    <value>The PROFINET device name {0} set wrongly for the {1}. The first label cannot start with 'port-xyz' or 'port-xyz-abcde' with a,b,c,d,e, x, y, z = 0...9.</value>
  </data>
  <data name="XML_IODNotConnectedToSubnetButToIOSystem" xml:space="preserve">
    <value>Decentral device '{0}' is not connected to Subnet '{1}' but it is connected to IO System '{2}'</value>
  </data>
  <data name="XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain" xml:space="preserve">
    <value>Decentral device '{0}' is not connected to Subnet '{1}' but it is connected to IO System '{2}' and Sync Domain '{3}'</value>
  </data>
  <data name="XML_IODNotConnectedToSubnetButToSyncDomain" xml:space="preserve">
    <value>Decentral device '{0}' is not connected to Subnet '{1}' but it is connected to Sync Domain '{2}'</value>
  </data>
  <data name="XML_IoSystemNotFounfForIOC" xml:space="preserve">
    <value>IOSystem '{0}' not found for central device: {1} - interface {2}</value>
  </data>
  <data name="XML_IoSystemNotFoundForIOD" xml:space="preserve">
    <value>IOSystem '{0}' not found for decentral device: {1} - interface {2}</value>
  </data>
  <data name="XML_IoSystemNotFounfForIOD" xml:space="preserve">
    <value>IOSystem '{0}' not found for decentral device: {1} - interface {2}</value>
  </data>
  <data name="XML_IOSystemRefIDWithoutSubnet" xml:space="preserve">
    <value>IOSystemRefID given without defining a Subnet.</value>
  </data>
  <data name="XML_IoSystemWithoutIOC" xml:space="preserve">
    <value>IO system does not have a controller: {0}</value>
  </data>
  <data name="XML_IRTProjectWithoutTopology" xml:space="preserve">
    <value>No topology given in an IRT project.</value>
  </data>
  <data name="XML_MaxIOSystemNumber" xml:space="preserve">
    <value>Maximum 16 IO system are supported, but there are {0} IO systems in subnet: {1}</value>
  </data>
  <data name="XML_ModuleIdUsedMoreThanOnce" xml:space="preserve">
    <value>ModuleID {0} on device {1} is used more than once.</value>
  </data>
  <data name="XML_ModuleRefIDWithoutModuleUnderIsochronousModule" xml:space="preserve">
    <value>ModuleRefID {0} given under isochronous submodule not found in the list of modules for device: {1}</value>
  </data>
  <data name="XML_ModuleRefIDWithoutModuleUnderSharedDevice" xml:space="preserve">
    <value>ModuleRefID {0} given under shared device not found in the list of modules for device: {1}</value>
  </data>
  <data name="XML_NumberOfPortsExceeded" xml:space="preserve">
    <value>Number of ports are more than allowed for the device: {0}.</value>
  </data>
  <data name="XML_SubmoduleIdUsedMoreThanOnce" xml:space="preserve">
    <value>SubModuleID {0} under module {1} on device {2} is used more than once. </value>
  </data>
  <data name="XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule" xml:space="preserve">
    <value>SubModuleRefID {0} under isochronous module not found in the list of submodules for device: {1} module: {2}</value>
  </data>
  <data name="XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice" xml:space="preserve">
    <value>SubModuleRefID {0} under shared device not found in the list of submodules for device: {1} module: {2}</value>
  </data>
  <data name="XML_MultipleIOControllersInIOSystem" xml:space="preserve">
    <value>Multiple IO controllers in IO system '{0}': device: {1} - interface {2}</value>
  </data>
  <data name="XML_MultipleUseOfIOSystemNumber" xml:space="preserve">
    <value>IO system number {0} is used more than once for IOSystemID: {1}</value>
  </data>
  <data name="XML_NotExistConfigXML" xml:space="preserve">
    <value>Configuration.xml file not found.</value>
  </data>
  <data name="XML_NotExistListOfNodesXML" xml:space="preserve">
    <value>ListOfNodes.xml file not found.</value>
  </data>
  <data name="XML_NotExistTopologyXML" xml:space="preserve">
    <value>Topology.xml file not found.</value>
  </data>
  <data name="XML_NullOrEmptyConfigXML" xml:space="preserve">
    <value>Configuration.xml file path is not specified.</value>
  </data>
  <data name="XML_NullOrEmptyListOfNodesXML" xml:space="preserve">
    <value>ListOfNodes.xml file path is not specified.</value>
  </data>
  <data name="XML_SpecifiedSyncDomainNotFound" xml:space="preserve">
    <value>SyncDomain '{0}' not found for device: {1} - interface {2}</value>
  </data>
  <data name="XML_SubnetNotFound" xml:space="preserve">
    <value>Subnet '{0}' not found for device: {1} - interface {2}</value>
  </data>
  <data name="XML_SubnetNotFoundForIOC" xml:space="preserve">
    <value>Subnet '{0}' not found for central device: {1} - interface {2}</value>
  </data>
  <data name="XML_SubnetNotFounfForIOC" xml:space="preserve">
    <value>Subnet '{0}' not found for central device: {1} - interface {2}</value>
  </data>
  <data name="XML_SubnetNotFoundForIOD" xml:space="preserve">
    <value>Subnet '{0}' not found for decentral device: {1} - interface {2}</value>
  </data>
  <data name="XML_SubnetNotFounfForIOD" xml:space="preserve">
    <value>Subnet '{0}' not found for decentral device: {1} - interface {2}</value>
  </data>
  <data name="XML_SyncDomainNotFounfForIOD" xml:space="preserve">
    <value>Sync domain not found for decentral device: {0} - interface {1}</value>
  </data>
  <data name="XML_SyncDomainRefIDWithoutSubnet" xml:space="preserve">
    <value>SyncDomainRefID given without defining a Subnet.</value>
  </data>
  <data name="XML_TopologyRefIDWithoutTopology" xml:space="preserve">
    <value>TopologyRefID given in configuration xml without a topology xml file.</value>
  </data>
  <data name="XML_UnmatchedInterfaceRefIDForCentralDevice" xml:space="preserve">
    <value>Incorrect InterfaceRefID for central device: {0} - interface: {1}</value>
  </data>
  <data name="XML_UnmatchedInterfaceRefIDForDecentralDevice" xml:space="preserve">
    <value>Incorrect InterfaceRefID for decentral device: {0} - interface: {1}</value>
  </data>
  <data name="XML_UpdateTimeMustBeSetInManualMode" xml:space="preserve">
    <value>Update time mode is manual but value is not set for the decentral device {0}.</value>
  </data>
  <data name="XML_UpdateTimeMustNotBeSetInAutomaticMode" xml:space="preserve">
    <value>Update time mode is automatic but value is set for the decentral device {0}.</value>
  </data>
  <data name="XML_WithoutSubnet" xml:space="preserve">
    <value>IOSystem or SyncDomain set without specifying a subnet. This is not supported.</value>
  </data>
  <data name="SubnetDuplicateName" xml:space="preserve">
    <value>The subnet name {0} is not unique.</value>
  </data>
  <data name="SubnetEmptyName" xml:space="preserve">
    <value>The name of the subnet is empty.</value>
  </data>
  <data name="SubnetNameTooLong" xml:space="preserve">
    <value>The subnet name is too long. Only 24 characters are allowed.</value>
  </data>
  <data name="XML_SubslotIsNotAvailableForModule" xml:space="preserve">
    <value>Subslot number {0} is not available for submodule {1} of the module {2}.</value>
  </data>
  <data name="XML_SubslotNumberConfigurationIsWrong" xml:space="preserve">
    <value>The subslot {0} is configured wrong in configuration file.</value>
  </data>
  <data name="XML_SubslotNumberForPortNotSuitable" xml:space="preserve">
    <value>The subslot number is not suitable for the port {0}.</value>
  </data>
  <data name="XML_SubslotNumberIsUsedByMoreThanOneModule" xml:space="preserve">
    <value>The subslot number {0} is used by multiple modules.</value>
  </data>
  <data name="XML_SubslotNumberUsedMultipleTimesForPort" xml:space="preserve">
    <value>The subslot {0} is used multiple times for port.</value>
  </data>
  <data name="SyncDomainBwLevelExceeded" xml:space="preserve">
    <value>Selected bandwidth use {0} exceeds the maximum possible bandwidth use {1}, for the sync domain '{2}'.</value>
  </data>
  <data name="SyncDomainEmptyName" xml:space="preserve">
    <value>The sync domain must be given a name.</value>
  </data>
  <data name="SyncDomainMaxRedBwExceeded" xml:space="preserve">
    <value>Calculated IRT bandwidth exceeds the maximum bandwidth permitted by the sync domain.</value>
  </data>
  <data name="SyncDomainNameTooLong" xml:space="preserve">
    <value>The name of a sync domain cannot be longer than 64 characters.</value>
  </data>
  <data name="SyncDomainNoCommonStartupMode" xml:space="preserve">
    <value>Cannot find an operating mode for normal startup for the synchronized interfaces of sync domain '{0}'.</value>
  </data>
  <data name="SyncDomainNoConnection" xml:space="preserve">
    <value>Topology error. There is no connection possibility between the sync master {0} and the sync slave {1}.</value>
  </data>
  <data name="SyncDomainNoRtcyFragModeWithRtcyDeviceExists" xml:space="preserve">
    <value>{0} only supports fragmentation if the sync domain {1} does not include RT devices, but {2} is an RT device.</value>
  </data>
  <data name="SyncDomainNoSecSyncMasterWithIrtFlex" xml:space="preserve">
    <value>A redundant sync master is not permitted in an IRT Flex domain.</value>
  </data>
  <data name="SyncDomainNoSyncMaster" xml:space="preserve">
    <value>One sync master must be defined in synchronization domain '{0}'.</value>
  </data>
  <data name="SyncDomainNotAllowedPortInterconnection" xml:space="preserve">
    <value>In sync domain '{0}' there is a prohibited port interconnection with device '{1}' which is outside of the limits of the sync domain '{0}'.</value>
  </data>
  <data name="SyncDomainOnePortFragModeWithMultiplePortsActive" xml:space="preserve">
    <value>{1} only supports fragmentation if there is one active port, but more than one port is active.</value>
  </data>
  <data name="SyncDomainResBandwidthExceeded" xml:space="preserve">
    <value>{0}: The calculated IRT bandwidth exceeds the reserved IRT bandwidth.</value>
  </data>
  <data name="SyncDomainSecondarySyncMasterCount" xml:space="preserve">
    <value>Only one secondary sync master may be defined in synchronization domain '{0}'.</value>
  </data>
  <data name="SyncDomainSmallSendClocksNotSupported" xml:space="preserve">
    <value>Sync domain does not currently support send clocks less than 250 µs.</value>
  </data>
  <data name="SyncDomainSyncMasterCount" xml:space="preserve">
    <value>Only one sync master may be defined in synchronization domain '{0}'.</value>
  </data>
  <data name="SyncDomainSyncMasterIsNotComprised" xml:space="preserve">
    <value>The current IRT top island does not include the sync master: '{0}'.</value>
  </data>
  <data name="SyncMasterInvalidDistance" xml:space="preserve">
    <value>Topology error. A maximum of two sync slaves are allowed between the primary sync master '{0}' and the secondary sync master '{1}'.</value>
  </data>
  <data name="TiToCannotBeCalculated" xml:space="preserve">
    <value>The IO device {0} cannot supply the process values with the update time of {1} ms</value>
  </data>
  <data name="TiToGreaterThanAppCycle" xml:space="preserve">
    <value>The specified Ti/To value is greater than the application cycle value</value>
  </data>
  <data name="TotalGrossFrameLengthExceeded" xml:space="preserve">
    <value>The input / output length, including user data qualifier is {0} bytes and exceeds the maximum permitted data length of {1} bytes.</value>
  </data>
  <data name="TotalNetFrameLengthExceeded" xml:space="preserve">
    <value>The input / output length, excluding user data qualifier is {0} bytes and exceeds the maximum permitted data length of {1} bytes.</value>
  </data>
  <data name="UpdateTimeChangedAutomatically" xml:space="preserve">
    <value>On IO device {0}, the update time has changed from {1} ms to {2} ms.</value>
  </data>
  <data name="UpdateTimeNotApplicable" xml:space="preserve">
    <value>IO device '{0}' cannot be operated with the set update time.</value>
  </data>
  <data name="UpdateTimeNotEqualSendClock" xml:space="preserve">
    <value>The configured update time must match the send clock (isochronous mode enabled).</value>
  </data>
  <data name="ValidationErrorInvalidTi" xml:space="preserve">
    <value>The specified Ti value is invalid.</value>
  </data>
  <data name="ValidationErrorInvalidTo" xml:space="preserve">
    <value>The specified To value is invalid.</value>
  </data>
  <data name="ValidationErrorTiNotMultiplesOfRaster" xml:space="preserve">
    <value>The given Ti value ({0}) is invalid, as it must be a multiple of the interval ({1})</value>
  </data>
  <data name="ValidationExpertModeSupportedBwLevel" xml:space="preserve">
    <value>The selected bandwidth use {0} is only supported in high performance mode for the sync domain '{1}'.</value>
  </data>
  <data name="ValidationExpertModeSupportedSC" xml:space="preserve">
    <value>The selected send clock pulse is only supported when the 'high performance' option is enabled.</value>
  </data>
  <data name="ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE" xml:space="preserve">
    <value>There is no isochronous submodule configured in the device {0} operating in isochronous mode.</value>
  </data>
  <data name="XML_ParameterizationDisallowed_MediaRedundancy" xml:space="preserve">
    <value>The given media redundancy configuration is not valid for the device : {0} because parametrization disallowed in the gsdml file.</value>
  </data>
  <data name="XML_InterfaceNotSupported_MediaRedundancy" xml:space="preserve">
    <value>The given media redundancy configuration is not valid for the device : {0} because it does not have an interface submodule in the gsdml file.</value>
  </data>
  <data name="SharedDeviceAllSubmodulesShared" xml:space="preserve">
    <value>At least one submodule in the shared device '{0}' in subsystem '{1}' must be assigned to the IO controller.</value>
  </data>
  <data name="SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned" xml:space="preserve">
    <value>IRT is not possible on device {0} because access is set to 'not assigned' for the interface {1}.</value>
  </data>
  <data name="SharedDeviceIsoNotPossibleWithSharedIF" xml:space="preserve">
    <value>Isochronous mode is not possible on device {0} because access is set to 'not assigned' for the interface {1}.</value>
  </data>
  <data name="SharedDevicePrioritizedStartupNotPossible" xml:space="preserve">
    <value>Prioritized startup is not possible with IO device '{0}' because the IO controller '{1}' does not have full access to the PROFINET interface.</value>
  </data>
  <data name="SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules" xml:space="preserve">
    <value>Device {0} was configured as shared device. Use of the send clock {1} ms is not possible.</value>
  </data>
  <data name="XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice" xml:space="preserve">
    <value>The device {0} must have at least one virtual submodule.</value>
  </data>
  <data name="XML_DeviceHasOverCapacityForSharedDevice" xml:space="preserve">
    <value>The device {0} has more shared part than it can support.</value>
  </data>
  <data name="XML_HeadModuleCannotBeUsedMoreThanOne" xml:space="preserve">
    <value>Head module cannot be used more than one, check the device {0}.</value>
  </data>
  <data name="XML_InterfaceCannotBeUsedForSharedDevice" xml:space="preserve">
    <value>The device {0} cannot use its interface.</value>
  </data>
  <data name="XML_IOControllerDoesNotExistForSharedDevice" xml:space="preserve">
    <value>IOController {0} does not exist.</value>
  </data>
  <data name="XML_InterfaceDoesNotExistForSharedDevice" xml:space="preserve">
    <value>Interface {0} does not exist.</value>
  </data>
  <data name="XML_IOControllersOfIODeviceHasDifferentSendClock" xml:space="preserve">
    <value>Assigned IO controllers of shared device {0} have different send clock values which will be adapted from PDEV owner controller if it is possible.</value>
  </data>
  <data name="XML_IODeviceSendClockForSharedIsNotRequired" xml:space="preserve">
    <value>Send clock value is not required for device {0}, the value is updated.</value>
  </data>
  <data name="XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice" xml:space="preserve">
    <value>The module {0} must have at least one submodule.</value>
  </data>
  <data name="XML_PDevCannotBeUsedMoreThanOne" xml:space="preserve">
    <value>PDev cannot be used more than one, check the device {0}.</value>
  </data>
  <data name="XML_SendClockForSharedDeviceIsNotSet" xml:space="preserve">
    <value>Send clock for device {0} is not set.</value>
  </data>
  <data name="XML_SharedDeviceIsNotSupportedForDevice" xml:space="preserve">
    <value>The device {0} does not support shared device feature.</value>
  </data>
  <data name="XML_SharedModuleUsedMoreThanOne" xml:space="preserve">
    <value>Shared module {0} is used more than one.</value>
  </data>
  <data name="XML_SharedSubmoduleUsedMoreThanOne" xml:space="preserve">
    <value>Shared submodule {0} is used more than one.</value>
  </data>
  <data name="PNIpConfig_DeviceNameUsingDifferentMethod" xml:space="preserve">
    <value>For multiple use IO systems, the device name must be obtained by a different service on the IO controller.</value>
  </data>
  <data name="CoupledIDeviceOnlyOneIOSystemShouldBeTailored" xml:space="preserve">
    <value>I-devices with a lower-level IO system at the same Ethernet interface cannot support standard machine projects in higher-level and lower-level IO systems.</value>
  </data>
  <data name="DomainManagement_OtherIOSystemMemberInMrpDomain" xml:space="preserve">
    <value>For multiple use IO systems or standard machine projects, IO devices can only be part of an MRP domain if they include only elements of their IO system.</value>
  </data>
  <data name="DomainManagement_OtherIOSystemMemberInSyncDomain" xml:space="preserve">
    <value>For multiple use IO systems or standard machine projects, IO devices can only be part of a sync domain if they include only elements of their IO system.</value>
  </data>
  <data name="IDeviceControllerCentralPdevAssigned" xml:space="preserve">
    <value>If the IO controller is used as I-device at the same interface for multiple use IO systems or standard machine projects, the PDEV must be assigned centrally.</value>
  </data>
  <data name="InterfaceOptions_DeviceReplacementWithoutExchangableMedium" xml:space="preserve">
    <value>For multiple use IO systems, device replacement without exchangeable medium must be set on the IO controller.</value>
  </data>
  <data name="IOSystem_MachineTailoringEnabled_IOC" xml:space="preserve">
    <value>A standard machine project should contain an IO controller '{0}' with activated optional IO device.</value>
  </data>
  <data name="Mrp_PortInterconn_NonRingPort" xml:space="preserve">
    <value>Ring port '{0}' is connected to port '{1}', which is not a ring port.</value>
  </data>
  <data name="MultiDeployable_NoSharedDevice" xml:space="preserve">
    <value>A multiple use IO system or standard machine project may not include a shared device.</value>
  </data>
  <data name="Optional_Device_Incompatible_Mrp_Role" xml:space="preserve">
    <value>MRP roles from {0} cannot be 'Manager' or 'Manager (auto)', if the device is being used optionally.</value>
  </data>
  <data name="Optional_Device_Incompatible_Sync_Role" xml:space="preserve">
    <value>{0} cannot be a sync master if the device is optional.</value>
  </data>
  <data name="Optional_Device_Interconnected_With_Tool_Changer_Port" xml:space="preserve">
    <value>Port {0} cannot be interconnected with a tool changer port if the device is optional.</value>
  </data>
  <data name="Optional_Device_Iod_Pdev_Assigned_Iocontroller" xml:space="preserve">
    <value>{0} has to support the PDEV model and its PDEV has to be assigned to the same controller because it is interconnected with an optional device.</value>
  </data>
  <data name="Optional_Device_Mau_Type_Warning" xml:space="preserve">
    <value>Port {0} is contained in an invalid MAU type with topology.</value>
  </data>
  <data name="Optional_Device_Max_Fixed_Peer_Limit_Exceeded" xml:space="preserve">
    <value>The optional device {0} can be used for a maximum of 2 fixed port connections.</value>
  </data>
  <data name="Optional_Device_Mrp_Ring_Port_Error" xml:space="preserve">
    <value>The optional device {0} can either have 2 or 0 interconnected ringports.</value>
  </data>
  <data name="Optional_Device_With_Set_Boundaries" xml:space="preserve">
    <value>{1} cannot be set for port {0} because it belongs to an optional device.</value>
  </data>
  <data name="Optional_Device_With_Tool_Changer_Port" xml:space="preserve">
    <value>Port {0} cannot be a tool changer port if the device is optional.</value>
  </data>
  <data name="Optional_IDevice_With_No_SuperOrdinate" xml:space="preserve">
    <value>{0} is an I-device and an optional IO device, which means it must have a higher-level IO system.</value>
  </data>
  <data name="OverwriteProfinetDeviceName" xml:space="preserve">
    <value>For multiple use IO systems, permission for overwriting of PROFINET device names for IO controllers must be set.</value>
  </data>
  <data name="PdevAssignedToController" xml:space="preserve">
    <value>When „Partner port” is set to "Set Partner by user program" or "Optional IO-Device" is activated on a device of an IO-System, then all IO devices in the same IO system be must be parameterizable by the IO controller.</value>
  </data>
  <data name="PNIpConfig_IPAddressUsingDifferentMethod" xml:space="preserve">
    <value>For multiple use IO systems, the IP address must be obtained by a different service on the IO controller.</value>
  </data>
  <data name="Port_ProgrammablePeer_Not_In_Ring" xml:space="preserve">
    <value>'{0}' is a ring port and 'Set partner through user program' therefore cannot be selected for the partner.</value>
  </data>
  <data name="Port_ProgrammablePeer_PdevAssignedToController" xml:space="preserve">
    <value>'{0}' is selected as 'Partner is set by the user program' and for this reason for its interface '{1}' the PDEV has to be assigned to the controller.</value>
  </data>
  <data name="PortInterconnection_PartnerPortFromSameIoSystem" xml:space="preserve">
    <value>For multiple use IO systems or standard machine projects, all specified partner ports are limited to partner ports of their own IO system.</value>
  </data>
  <data name="PortInterconnection_TopologyReachableFromController" xml:space="preserve">
    <value>IO device {0} cannot be topologically reached by IO controller {1}. All devices in a standard machine project must be topologically accessible.</value>
  </data>
  <data name="ProgrammablePeerPortDeactivated" xml:space="preserve">
    <value>Port {0} is deactivated even though 'Set partner through user program' was selected. Activate the port if you want to use the option.</value>
  </data>
  <data name="XML_WrongDefaultRouterSettingInIOD" xml:space="preserve">
    <value>The IO device {0} with Interface {1} cannot have an individual router address because its router settings are in synchronization with its IO controller.</value>
  </data>
  <data name="XML_IOSystemAndSyncDomainNotDeclaredForIod" xml:space="preserve">
    <value>If an IO device ({0}) has topology connection and not connected to an IOSystem, SyncDomain should be defined.</value>
  </data>
  <data name="XML_SharedDeviceIOSystemCheck" xml:space="preserve">
    <value>IOSystem can't be defined for shared device {0}.</value>
  </data>
  <data name="XML_SharedDeviceSyncDomainCheck" xml:space="preserve">
    <value>SyncDomain can't be defined for shared device {0}.</value>
  </data>
  <data name="XML_SharedDeviceAssignedControllerUniqueness" xml:space="preserve">
    <value>Assigned controllers have to be unique for shared device {0}.</value>
  </data>
  <data name="XML_IOAddressRangeExceeded" xml:space="preserve">
    <value>IO address value cannot be bigger than 32767.</value>
  </data>
  <data name="XML_IOAddressTypeConsistency" xml:space="preserve">
    <value>{0} IO address type must be defined for {1}.</value>
  </data>
  <data name="XML_IOModuleNotExist" xml:space="preserve">
    <value>{0} has IO addresses and must be defined in the configuration.</value>
  </data>
  <data name="XML_NotIOAddressTypedModule" xml:space="preserve">
    <value>{0} is not an IO typed but IO address is defined.</value>
  </data>
  <data name="XML_GSDMLExistence" xml:space="preserve">
    <value>GSDML cannot be found: {0}.</value>
  </data>
  <data name="StationNumberDownLimit" xml:space="preserve">
    <value>The PROFINET device number cannot be less than {0}, but the value {1} is used in IO system {2}.</value>
  </data>
  <data name="StationNumberDuplication" xml:space="preserve">
    <value>The PROFINET device number {0} is already used in the IO system {1}. The next available number is {2}.</value>
  </data>
  <data name="StationNumberDuplicationAndNotAvailablePosition" xml:space="preserve">
    <value>The PROFINET device number {0} is already used in the IO system {1}.</value>
  </data>
  <data name="PortDeactivationNotValid" xml:space="preserve">
    <value>Deactivation is not supported for port {0}.</value>
  </data>
  <data name="XML_MultipleUseIOSystemIsNotSelected" xml:space="preserve">
    <value>If MultipleUseIOSystem is not activated, SetIPAddressByTheIOController cannot be selected.</value>
  </data>
  <data name="XML_InvalidPortSettingsForDeactivatedPort" xml:space="preserve">
    <value>Invalid port settings for port '{0}', because it is deactivated.</value>
  </data>
  <data name="XML_PortBoundariesSetForNonPDEVInterface" xml:space="preserve">
    <value>'Boundaries' cannot be set for the port '{0}', because its interface does not support PDEV.</value>
  </data>
  <data name="XML_EndOfTopologyDiscoveryIsNotValid" xml:space="preserve">
    <value>'EndOfTopologyDiscovery' port option cannot be set for the port '{0}', because it does not support this function.</value>
  </data>
  <data name="XML_EndOfTheSyncDomainIsNotValid" xml:space="preserve">
    <value>'EndOfTheSyncDomain' port option cannot be set for the port '{0}', because it does not support this function.</value>
  </data>
  <data name="XML_EndOfDetectionOfAccessibleDevicesIsNotValid" xml:space="preserve">
    <value>'EndOfDetectionOfAccessibleDevices' port option cannot be set for the port '{0}', because it does not support this function.</value>
  </data>
  <data name="XML_CentralDeviceIsNotUsedInConfiguration" xml:space="preserve">
    <value>Central device {0} is not used in configuration file.</value>
  </data>
  <data name="XML_DecentralDeviceIsNotUsedInConfiguration" xml:space="preserve">
    <value>Decentral device {0} is not used in configuration file.</value>
  </data>
  <data name="XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement" xml:space="preserve">
    <value>PermitOverwritingOfDeviceNamesOfAllAssignedIODevices attribute is not available without SupportDeviceReplacementWithoutExchangeableMedium. Device {0} violates this rule.</value>
  </data>
  <data name="XML_InterconnectedPortOwnerDeleted" xml:space="preserve">
    <value>An interconnected port located in slot {0} but the module in this slot of device {1} is deleted in the configuration.xml.</value>
  </data>
  <data name="XML_TopologyDeviceSlotCheck" xml:space="preserve">
    <value>Device {0} does not have any port owner module which is located in slot {1}</value>
  </data>
  <data name="XML_TopologyInvalidPortNumber" xml:space="preserve">
    <value>Invalid port number {0} for device {1} in the topology.</value>
  </data>
  <data name="XML_PDEVOwnerNotExistButSyncRoleDefined" xml:space="preserve">
    <value>PDEV owner device of shared device {0} does not exist in the project but SyncRole defined.</value>
  </data>
  <data name="XML_SharedDeviceWrongSyncRole" xml:space="preserve">
    <value>Assigned IO controller is unsynchronized so SyncRole of the shared device {0} must be unsynchronized too.</value>
  </data>
  <data name="XML_SyncDomainOfPDEV" xml:space="preserve">
    <value>SyncDomainRefID of {0} must not be different from assigned controller because parametrization is disallowed for the device.</value>
  </data>
  <data name="XML_SendClockAlreadyGivenInSyncDomain" xml:space="preserve">
    <value>Send clock value is already given in sync domain, send clock value of IO controller {0} is ignored.</value>
  </data>
  <data name="XML_SendClockNotGivenInSyncDomaion" xml:space="preserve">
    <value>The sync domain {0} must have a send clock value because it has IO controller that has different syncronization role from unsyncronized.</value>
  </data>
  <data name="XML_NonExistingInterfaceRefIDInTopology" xml:space="preserve">
    <value>The given Interface RefID {0} in topology does not exist according to listofnodes.</value>
  </data>
  <data name="XML_NonExistingDeviceRefIDInTopology" xml:space="preserve">
    <value>The given Device RefID {0} in topology does not exist according to listofnodes.</value>
  </data>
  <data name="XML_UnmatchedInterfaceRefIDInTopology" xml:space="preserve">
    <value>The given Interface RefID {0} in topology does not match the given deviceID according to listofnodes.</value>
  </data>
  <data name="XML_NoPartnerPortAndItIsNotSetByUserProgram" xml:space="preserve">
    <value>Partner port type must be SetByUserProgram if no partner ports are set. Interface id: {0} - Port number: {1}</value>
  </data>
  <data name="XML_ParameterRecordDataIndexNotExist" xml:space="preserve">
    <value>Given ParameterRecordDataItem index: {0} for {1} does not exist according to GSDML.</value>
  </data>
  <data name="XML_ParameterRecordDataNotAllowedValue" xml:space="preserve">
    <value>The value given for the ParameterRecordDataItem index: {0}, byte offset: {1}, bit offset: {2} for {3} is not one of the allowed values.</value>
  </data>
  <data name="XML_ParameterRecordDataItemIndexNotUnique" xml:space="preserve">
    <value>ParameterRecordDataItem indexes are not unique for {0}.</value>
  </data>
  <data name="XML_ParameterRecordDataRefIDNotExist" xml:space="preserve">
    <value>The byte offset {0}, bit offset {1} value for ParameterRecordDataItem with index: {2} does not exist for {3}.</value>
  </data>
  <data name="XML_IOAddressesDefinitionOfMultipleVsm" xml:space="preserve">
    <value>Module {0} has multiple IO address owner virtual submodules so IO addresses must be defined under the submodule element.</value>
  </data>
  <data name="XML_IOAddressOfVsmNotExist" xml:space="preserve">
    <value>IO address of module {0}/virtual submodule {1} must be defined.</value>
  </data>
  <data name="XML_VsmIOAddressesDefinedTwice" xml:space="preserve">
    <value>IO address of module {0}/virtual submodule {1} defined twice. Choose one of the IO address definitions, under the module or under the submodule.</value>
  </data>
  <data name="XML_WrongSubslotNumberForVsm" xml:space="preserve">
    <value>Subslot number {0} is not valid for virtual submodule : {1}. The virtual submodule is fixed in subslot {2} in the GSDML definition.</value>
  </data>
  <data name="XML_IRTNotSupportedForIOControllerInterface" xml:space="preserve">
    <value>Central device {1}'s interface {0} does not support IRT.</value>
  </data>
  <data name="IfWithAdditionalRedundancy" xml:space="preserve">
    <value>{0} belongs to a ring structure without MRP settings.</value>
  </data>
  <data name="IPMultipleAddress" xml:space="preserve">
    <value>Address {0} of the interface {1} is not unique. The device {2}[{3}] has the same address.</value>
  </data>
  <data name="RouterIpDifferentFromIeIp" xml:space="preserve">
    <value>Default router IP address {0} must differ from the IP address of interface {1}.</value>
  </data>
  <data name="IPRouterAddressClassInvalid" xml:space="preserve">
    <value>Default router IP address {0} with subnet mask {1} of interface {2} is invalid.</value>
  </data>
  <data name="IPTooLongaddress" xml:space="preserve">
    <value>IP address {0} of interface {1} is too long.</value>
  </data>
  <data name="IPRouterAddressTooLong" xml:space="preserve">
    <value>Default router IP address {0} of interface {1} is too long.</value>
  </data>
  <data name="IPRouterBroadcastAddress" xml:space="preserve">
    <value>Default router IP address {0} with subnet mask {1} of interface {2} is invalid (this is a broadcast address).</value>
  </data>
  <data name="IPRouterDefaultRouteAddress" xml:space="preserve">
    <value>Default router IP address {0} with subnet mask {1} of interface {2} is invalid (this is a default network address).</value>
  </data>
  <data name="IPEmptyAddress" xml:space="preserve">
    <value>IP address of interface {0} may not be empty.</value>
  </data>
  <data name="IPRouterEmptyAddress" xml:space="preserve">
    <value>Default router IP address of interface {0} may not be empty.</value>
  </data>
  <data name="IPLoopbackAddress" xml:space="preserve">
    <value>IP address {0} of interface {1} is invalid (it is a loopback address).</value>
  </data>
  <data name="IPRouterLoopbackAddress" xml:space="preserve">
    <value>Default router IP address {0} of interface {1} is invalid (it is a loopback address).</value>
  </data>
  <data name="IPAddressRestrictedSubnet" xml:space="preserve">
    <value>The IP address {0} of interface {1} is located in a permitted subnet range.</value>
  </data>
  <data name="IPWrongAddress" xml:space="preserve">
    <value>IP address {0} of interface {1} is invalid.</value>
  </data>
  <data name="IPRouterWrongAddress" xml:space="preserve">
    <value>Default router IP address {0} of interface {1} is invalid.</value>
  </data>
  <data name="IPWrongSubnetMask" xml:space="preserve">
    <value>IP subnet mask {0} of interface {1} is invalid.</value>
  </data>
  <data name="IPBroadcastAddress" xml:space="preserve">
    <value>IP address {0} with subnet mask {1} of interface {2} is invalid (this is a broadcast address).</value>
  </data>
  <data name="IPDefaultRouteAddress" xml:space="preserve">
    <value>IP address {0} with subnet mask {1} of interface {2} is invalid (this  is a default router address).</value>
  </data>
  <data name="IPHostAddressNull" xml:space="preserve">
    <value>IP address {0} with subnet mask {1} of interface {2} is invalid (the host address is 0).</value>
  </data>
  <data name="IPUnrestrictedSubnetMaskNotSupported" xml:space="preserve">
    <value>IP address {0} with subnet mask {1} of interface {2} is invalid.</value>
  </data>
  <data name="IPAddressDifferentSubnet" xml:space="preserve">
    <value>The network part of the IO device's IP address {0} is different to the network part of the IO controller.</value>
  </data>
  <data name="DelayTimeOutOfRange" xml:space="preserve">
    <value>Delay time value {0:f3} ms is not within the value range {1:f3} ms to {2:f3} ms.</value>
  </data>
  <data name="InputPipSizeAgainstController" xml:space="preserve">
    <value>The maximum permitted input data volume has been exceeded for the isochronous mode.</value>
  </data>
  <data name="OutputPipSizeAgainstController" xml:space="preserve">
    <value>The maximum permitted output data volume has been exceeded for the isochronous mode.</value>
  </data>
  <data name="TdcAgainstTdcminCheck" xml:space="preserve">
    <value>The configured send clock = {1} µs is less than the required time {0} µs!</value>
  </data>
  <data name="XML_ParameterRecordLengthIsOutOfLimit" xml:space="preserve">
    <value>The parameter record data value given of the byte : {0}, has length that is out of limit of the ref ID : {1}.</value>
  </data>
  <data name="XML_BothIODeviceAndSubmoduleHasParameterRecord" xml:space="preserve">
    <value>Both IO device and its submodule has ParameterRecordDataItem field for {0}, one of them can have this field.</value>
  </data>
  <data name="XML_BothModuleAndSubmoduleHasParameterRecord" xml:space="preserve">
    <value>Both module and submodule has ParameterRecordDataItem field for {0}, one of them can have this field.</value>
  </data>
  <data name="XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord" xml:space="preserve">
    <value>The IO device {0} has multiple virtual submodule, therefore, the IO device cannot have parameter record data item.</value>
  </data>
  <data name="XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord" xml:space="preserve">
    <value>The module {0} has multiple virtual submodule, therefore, the module cannot have parameter record data item.</value>
  </data>
  <data name="PortTransferRateNotSupported" xml:space="preserve">
    <value>The transmission rate value is not supported in Port {0}</value>
  </data>
  <data name="PortWrongValueForAutoNegotiation" xml:space="preserve">
    <value>If the transmission rate of the device is automatic and the device is not fiber optic, then the value of the autonegotiation option at port {0} can not be set to false.</value>
  </data>
  <data name="SyncDomainNameNotUnique" xml:space="preserve">
    <value>SyncDomain names must be unique in subnet {0}.</value>
  </data>
  <data name="XML_GSDMLPathUniqueness" xml:space="preserve">
    <value>In the ListOfNodes, the GSDML {0} is provided from different paths. The gsdml located in the first path will be imported. Others are ignored.</value>
  </data>
  <data name="XML_SlotIsNotAvailableForRemovingModule" xml:space="preserve">
    <value>The used-in module {0} is unplugged by introducing the module with an empty GSDRefId (''). But its slot number {1} is invalid.</value>
  </data>
  <data name="XML_SendClockMustBeUnderSyncDomain" xml:space="preserve">
    <value>Send clock must be defined under the sync domain : {0} for the IRT configured device : {1}.</value>
  </data>
  <data name="XML_SendClockNotSet" xml:space="preserve">
    <value>Send clock must be set for the central device {0}.</value>
  </data>
  <data name="XML_SendClockOfRtDeviceWrongLocation" xml:space="preserve">
    <value>Send clock of the RT configured central device {0}, must be set in the device configuration not in the sync domain.</value>
  </data>
  <data name="XML_SendClockAlreadySetInDevice" xml:space="preserve">
    <value>The send clock set in the device configuration and also in the sync domain for the central device {0}. The send clock in the sync domain will be ignored.</value>
  </data>
  <data name="XML_SendClockRtAndIrtInSameSyncDomain" xml:space="preserve">
    <value>In the sync domain {0}, IRT configured device(s) are exist and send clock is set so the send clock configuration for the central device {1} cannot be set inside device definition.</value>
  </data>
  <data name="XML_InvalidSendClock" xml:space="preserve">
    <value>Send Clock {0} is invalid for the given device: {1}</value>
  </data>
  <data name="XML_ParameterRecordDataIsNotChangeable" xml:space="preserve">
    <value>Parameter record data of byte offset {0} and bit offset {1} is not changeable for the IO device {2}.</value>
  </data>
  <data name="XML_ParameterRecordFormatIsNotValid" xml:space="preserve">
    <value>Parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in 'Value1,0xValue2' format.</value>
  </data>
  <data name="XML_InvalidNameIpFormat" xml:space="preserve">
    <value>The PROFINET device name {0} set wrongly for the {1}. Device name cannot have the form n.n.n.n, n = 0...999</value>
  </data>
  <data name="XML_ParameterRecordOctetUnsignedFormatIsNotValid" xml:space="preserve">
    <value>Parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in '0xValue1, 0xValue2, 0xValue3' format.</value>
  </data>
  <data name="XML_ParameterRecordDateFormatIsNotValid" xml:space="preserve">
    <value>Date type parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in  format yyyy-MM-dd or yyyy-MM-dd HH:mm:ss or yyyy-MM-dd HH:mm:ss.TTT which TTT represents 1 to 3 length millisecond part.</value>
  </data>
  <data name="XML_RestrictedVersionForExpertMode" xml:space="preserve">
    <value>High performance mode on PN Driver {0} {1} is not supported.</value>
  </data>
  <data name="XML_InvalidSyncRole" xml:space="preserve">
    <value>SyncRole is set as {0} but it does not supported for {1}</value>
  </data>
  <data name="ExpertModeRequiredForFastForwarding" xml:space="preserve">
    <value>High Performance needs to be activated in order to use Fast Forwarding.</value>
  </data>
  <data name="FastForwardingWithIpv6NotSupported" xml:space="preserve">
    <value>'Fast forwarding' cannot be used in combination with IPv6 devices in the same network.</value>
  </data>
  <data name="XML_ParameterRecordOctetStringLengthExceeded" xml:space="preserve">
    <value>Octet string type parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} exceeded its length for the IO device {3}.</value>
  </data>
  <data name="XML_CustomInterfaceFileNotExist" xml:space="preserve">
    <value>Custom interface file does not exist on path {0} for device : {1}</value>
  </data>
  <data name="XML_CustomInterfacePathNull" xml:space="preserve">
    <value>Interface type is selected as Custom but CustomInterfacePath is not set for device {0}</value>
  </data>
  <data name="XML_CustomInterfacePathNotNull" xml:space="preserve">
    <value>Interface type is not selected as Custom but CustomInterfacePath is also set. CustomInterfacePath can be used only with Custom interface type so it is ignored.</value>
  </data>
  <data name="XML_NotValid" xml:space="preserve">
    <value>{0} is not a valid XML file. {1}</value>
  </data>
  <data name="XML_TypeOfIOAddressNotExist" xml:space="preserve">
    <value>{0} IO address could not be used for the module {1}.</value>
  </data>
  <data name="IOAddressDataExceeded" xml:space="preserve">
    <value>The maximum {0} address range ({1} bytes) was exceeded. {2} bytes are configured.</value>
  </data>
  <data name="IoAddressScheduleIsNotPossible" xml:space="preserve">
    <value>{0} type IO address scheduling is not possible.</value>
  </data>
  <data name="XML_InvalidCentralDevicePortCount" xml:space="preserve">
    <value>Central device interface : {0} supports {1} port(s) but configured with {2} port(s).</value>
  </data>
  <data name="XML_CentralDeviceIsMoreThanOnceInConfiguration" xml:space="preserve">
    <value>Central device {0} is defined more than once in configuration file.</value>
  </data>
  <data name="XML_DecentralDeviceIsMoreThanOnceInConfiguration" xml:space="preserve">
    <value>Decentral device {0} is defined more than once in configuration file.</value>
  </data>
  <data name="XML_AddressTailoringActivatedButNotSupported" xml:space="preserve">
    <value>Multiple Use IO System is activated for the IO System : {0} but central device : {1} of this IO System does not support the feature.</value>
  </data>
  <data name="XML_PortWithGivenSubslotNumberDoesNotExist" xml:space="preserve">
    <value>Subslot Number '{0}' is not available for Port '{1}' on Module '{2}' on Interface '{3}'.</value>
  </data>
  <data name="XML_ParameterizationDisallowed_InterfacePort" xml:space="preserve">
    <value>The given port options are not valid for the port number {0} of the device : {1} because parametrization disallowed in the gsdml file.</value>
  </data>
  <data name="SubnetMaskDifferenceInIOSystem" xml:space="preserve">
    <value>Subnet Mask should be same for the devices in the same IO-System. Subnet mask '{0}' for decentral device '{1}' does not match Subnet mask '{2}' for central device '{3}' in IO-System '{4}'</value>
  </data>
  <data name="XML_MrpdNotSupported" xml:space="preserve">
    <value>PNConfigLib does not support MRPD feature. {0} is inside an MRP ring and its SyncRole is defined as {1}.</value>
  </data>
  <data name="XML_DeviceVersionNull" xml:space="preserve">
    <value>DeviceVersion of {0} cannot be null.</value>
  </data>
  <data name="XSD_NamespaceNotValid" xml:space="preserve">
    <value>xmlns : '{0}' must be same with targetNamespace : {1} in {2}</value>
  </data>
  <data name="XML_InvalidCentralDeviceAttributeType" xml:space="preserve">
    <value>{0} attribute has invalid value type in the catalog file {1} of {2}</value>
  </data>
  <data name="XML_NotValidPortGsdId" xml:space="preserve">
    <value>Port GsdId : {0} of {1} is not valid according to its GSDML file.</value>
  </data>
  <data name="XML_NotValidPortSubslotNumber" xml:space="preserve">
    <value>Port subslot number : {0} of {1}\{2} is not valid according to its GSDML file.</value>
  </data>
  <data name="XML_DnsConvert_PNDeviceNameLengthExceeded" xml:space="preserve">
    <value>The PROFINET device name {0} set wrongly for the {1}. The converted name is too long and violates DNS restrictions. International domain name (IDN) labels must contain fewer than 64 characters between the two points.</value>
  </data>
  <data name="XML_IpSetDirectlyAtTheDeviceNotSupported" xml:space="preserve">
    <value>{0} does not support IP address SetDirectlyAtTheDevice</value>
  </data>
  <data name="XML_IPAddressSetWithAddressTailoring" xml:space="preserve">
    <value>IPAddress cannot be set for {0} because MultipleUseIOSystem is configured for its IO system : {1}</value>
  </data>
  <data name="XML_IoSystemNotFound" xml:space="preserve">
    <value>IOSystem '{0}' not found for device: {1} - interface {2}</value>
  </data>
  <data name="XML_InvalidSNMPEnableReadOnly" xml:space="preserve">
    <value>When SNMP function is disabled, the read-only access also must be disabled on {0}.</value>
  </data>
  <data name="XML_EmptyCommunityName" xml:space="preserve">
    <value>The community name(s) on the {0} mustn't be empty.</value>
  </data>
  <data name="XML_MaxCharSizeCommunityName" xml:space="preserve">
    <value>The community name(s) on the {0} must be shorter or equal to 240 characters.</value>
  </data>
  <data name="XML_InvalidCharCommunityName" xml:space="preserve">
    <value>The community name(s) on the {0} can contain only upper / lowercase letters, numbers, dots and hyphens, and the string cannot start / end with dot or hyphen.</value>
  </data>
  <data name="KeepARAtErrorNotConfigurable" xml:space="preserve">
    <value>{0}.{1} does not support KeepARAtError</value>
  </data>
  <data name="KeepApplicationRelationAtCommunicationErrorNotConfigurable" xml:space="preserve">
    <value>{0}.{1} does not support KeepApplicationRelationAtCommunicationError</value>
  </data>
  <data name="ActivateDcpReadOnlyConfigurable" xml:space="preserve">
    <value>Decentral Device: {0}.{1}'s associated Pn Driver {2} does not support Dcp Read Only.</value>
  </data>
  <data name="DcpEnableReadOnlyConfigurable" xml:space="preserve">
    <value>Gsdml {0} does not support Dcp Read Only.</value>
  </data>
  <data name="XML_DeviceVersionIsNotValid" xml:space="preserve">
    <value>PNConfigLib doesn't support device version : {0} .</value>
  </data>
</root>