/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: InputRelationChecker.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;


using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

namespace PNConfigLib.Consistency.Checker
{
    internal class InputRelationChecker
    {
        private readonly ListOfNodes m_ListOfNodes;

        private readonly Configuration m_Configuration;

        private readonly Topology m_Topology;

        internal InputRelationChecker(Configuration cfg, ListOfNodes lon, Topology topo)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
            m_Topology = topo;
        }

        /// <summary>
        /// Start point for the relation checks
        /// </summary>
        /// <returns></returns>
        internal void Check()
        {
            IsDeviceVersionValidInListOfNodes();
            IsListOfNodesRefIdInConfigurationValid();
            IsTopologyRefIdInConfigurationValid();
            AreCentralDeviceRefIdsInConfigurationExistInListOfNodes();
            AreDecentralDeviceRefIdsInConfigurationExistInListOfNodes();
            IsCentralDeviceNotUsedInConfiguratıonOrMoreThanOnce();
            IsDecentralDeviceNotUsedInConfiguratıonOrMoreThanOnce();
            IsListOfNodesRefIdInTopologyValid();
        }

        private void IsCentralDeviceNotUsedInConfiguratıonOrMoreThanOnce()
        {
            foreach (PNDriverType pnDriver in m_ListOfNodes.PNDriver)
            {
                if (!m_Configuration.Devices.CentralDevice.Exists(w => w.DeviceRefID == pnDriver.DeviceID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Warning,
                        string.Empty,
                        ConsistencyConstants.XML_CentralDeviceIsNotUsedInConfiguration,
                        pnDriver.DeviceID);
                }

                if (m_Configuration.Devices.CentralDevice.FindAll(w => w.DeviceRefID == pnDriver.DeviceID).Count > 1)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_CentralDeviceIsMoreThanOnceInConfiguration,
                        pnDriver.DeviceID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsDecentralDeviceNotUsedInConfiguratıonOrMoreThanOnce()
        {
            foreach (ConfigReader.ListOfNodes.DecentralDeviceType decentralDevice in m_ListOfNodes.DecentralDevice)
            {
                if (!m_Configuration.Devices.DecentralDevice.Exists(w => w.DeviceRefID == decentralDevice.DeviceID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Warning,
                        string.Empty,
                        ConsistencyConstants.XML_DecentralDeviceIsNotUsedInConfiguration,
                        decentralDevice.DeviceID);
                }

                if (m_Configuration.Devices.DecentralDevice.FindAll(w => w.DeviceRefID == decentralDevice.DeviceID).Count
                    > 1)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_DecentralDeviceIsMoreThanOnceInConfiguration,
                        decentralDevice.DeviceID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsListOfNodesRefIdInConfigurationValid() 
        {
            if (m_Configuration.ListOfNodesRefID != m_ListOfNodes.ListOfNodesID)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IncorrectListOfNodesRefIDInConfig);
                throw new ConsistencyCheckException();
            }
        }

        private void IsTopologyRefIdInConfigurationValid() 
        {
            if (m_Topology != null)
            {
                if (m_Configuration.TopologyRefID != m_Topology.TopologyID)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IncorrectTopologyRefIDInConfig);
                    throw new ConsistencyCheckException();
                }
            }
            else if (!string.IsNullOrEmpty(m_Configuration.TopologyRefID))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_TopologyRefIDWithoutTopology);
                throw new ConsistencyCheckException();
            }
        }

        /// <summary>
        /// Checks if central devices and their interfaces in configuration file exist in ListOfNodes.
        /// </summary>
        private void AreCentralDeviceRefIdsInConfigurationExistInListOfNodes()
        {
            foreach (CentralDeviceType cfgCentralDevice in m_Configuration.Devices.CentralDevice)
            {
                PNDriverType lonCentralDevice = m_ListOfNodes.PNDriver.SingleOrDefault(e => e.DeviceID == cfgCentralDevice.DeviceRefID);
                if (lonCentralDevice == null)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_DeviceRefIDWithoutCentralDevice,
                        cfgCentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }

                if (cfgCentralDevice.CentralDeviceInterface.InterfaceRefID != lonCentralDevice.Interface.InterfaceID)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_UnmatchedInterfaceRefIDForCentralDevice,
                        cfgCentralDevice.DeviceRefID,
                        cfgCentralDevice.CentralDeviceInterface.InterfaceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void AreDecentralDeviceRefIdsInConfigurationExistInListOfNodes()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_Configuration.Devices.DecentralDevice)
            {
                string deviceRefId = xmlDecentralDevice.DeviceRefID;
                string interfaceRefId = xmlDecentralDevice.DecentralDeviceInterface.InterfaceRefID;

                ConfigReader.ListOfNodes.DecentralDeviceType lonDecentralDevice =
                    m_ListOfNodes.DecentralDevice.SingleOrDefault(e => e.DeviceID == deviceRefId);
                if (lonDecentralDevice == null)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_DeviceRefIDWithoutDecentralDevice,
                        xmlDecentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }

                // Check InterfaceRefID.
                if (interfaceRefId != lonDecentralDevice.Interface.InterfaceID)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_UnmatchedInterfaceRefIDForDecentralDevice,
                        deviceRefId,
                        interfaceRefId);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsListOfNodesRefIdInTopologyValid()
        {
            if (m_Topology != null)
            {
                if (m_Topology.ListOfNodesRefID != m_ListOfNodes.ListOfNodesID)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_IncorrectListOfNodesRefIDInTopology);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsDeviceVersionValidInListOfNodes()
        {
            foreach (PNDriverType pnd in m_ListOfNodes.PNDriver)
            {
                if (pnd.Interface?.InterfaceType != PNDriverInterfaceEnum.Custom)
                {
                    if (string.IsNullOrEmpty(pnd.DeviceVersion))
                    {
                        string interfaceIdPath =
                            StringOperations.CombineParametersWithBackSlash(pnd.DeviceID, pnd?.Interface?.InterfaceID);
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_DeviceVersionNull,
                            interfaceIdPath);
                        throw new ConsistencyCheckException();
                    }
                }
            }
        }
    }
}