/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIsochronUtilities.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.PNInterface;
using PNConfigLib.HWCNBL.Utilities;
using PNConfigLib.PNPlannerAdapter;

#endregion

namespace PNConfigLib.HWCNBL.Isochrone
{
    internal class PipAssignedModules
    {
        public PipAssignedModules(Dictionary<int, List<PclObject>> pipModuleDictionary)
        {
            PipModuleDictionary = pipModuleDictionary;
        }

        public Dictionary<int, List<PclObject>> PipModuleDictionary { get; set; }
    }

    internal static class PNIsochronUtilities
    {
        private const string s_DpMasterSystemId = "DPMasterSystemId";

        private const string s_PartialProcessImageOb = "PartialProcessImage";

        /// <summary>
        /// Time base for send clock
        /// </summary>
        private const decimal PNTimeBase = 0.03125M;
        private const float s_PNTimeBase = 31.25f;
        /// <summary>
        /// Conversion value to convert a millisecond to nanoseconds
        /// </summary>
        private const decimal MillisecToNanosec = 1000000M;
        public const int MillisecToNanosecInt = 1000000;

        /// <summary>
        /// Assigncheck 4 (Consistency check): Check if PIP refers to other DP-Mastersystem or other IO-System:
        /// If a PB mastersystem has PIP assignments from other IO-system,
        /// or a PN IO-system has PIP assignments from other IO-system or PB mastersystem
        /// </summary>
        /// <param name="mastersystem">DP Mastersystem or IO System</param>
        public static void CheckPiPsOfDifferentMastersystems(DataModel.PCLObjects.IOSystem mastersystem)
        {
            if (mastersystem == null)
            {
                return;
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            PNIOC cpu = mastersystem.PNIOC;

            if (cpu == null)
            {
                return;
            }
            Dictionary<int, List<int>> obPipsOfMastersystem = GetIsochronousObPipSettings(cpu);
            int mastersystemNumber =
                mastersystem.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PositionNumber,
                    ac.GetNew(),
                    0);
            List<int> assignedPiPs = GetAssignedPiPs(mastersystem);

            foreach (KeyValuePair<int, List<int>> ioSystemPipNumberPairs in obPipsOfMastersystem)
            {
                if (ioSystemPipNumberPairs.Key == mastersystemNumber)
                {
                    continue;
                }

                foreach (int pipNumber in ioSystemPipNumberPairs.Value)
                {
                    //If a PB mastersystem has PIP assignments from other IO-system or a PN IO-system has PIP assignments from other IO-system or PB mastersystem
                    if (assignedPiPs.Contains(pipNumber))
                    {
                        object[] errorParameters = new object[2];
                        errorParameters[0] = pipNumber;
                        errorParameters[1] = mastersystem.AttributeAccess.GetAnyAttribute<string>(
                            InternalAttributeNames.Name,
                            ac.GetNew(),
                            string.Empty);
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            cpu,
                            ConsistencyConstants.IsochronPIPHasModulesFromDifferentMastersystems,
                            errorParameters);
                    }
                }
            }
        }

        /// <summary>
        /// Returns the List of PIP numbers that are assigned to the modules of the given Mastersystem
        /// </summary>
        /// <param name="mastersystem">PN IO-System</param>
        /// <returns>List of PIP numbers</returns>
        private static List<int> GetAssignedPiPs(PclObject mastersystem)
        {
            Dictionary<int, UInt32> map = mastersystem.AttributeAccess.GetAnyAttribute<Dictionary<int, UInt32>>(
                InternalAttributeNames.AssignedPipNumbers, new AttributeAccessCode(), null);
            List<int> pips = new List<int>();

            if (map == null)
            {
                //Debug.Assert(map != null, string.Format(CultureInfo.InvariantCulture, "{0} not found in {1}", AttributeNames.AssignedPipNumbers, mastersystem));
                return pips;
            }
            for (int i = 0; i <= map.Count; i++)
            {
                UInt32 pip;
                if (map.TryGetValue(i, out pip))
                {
                    pips.Add(Convert.ToInt32(pip, CultureInfo.InvariantCulture));
                }
            }
            return pips;
        }

        /// <summary>
        /// Check the isochron settings of the PN-interface, and the sub-modules. Returns false if:
        /// 
        /// 1) If one or more submodules are isochron and the interface is not isochron.
        /// 
        /// 2) If no submodules are isochron and the interface is isochron.
        /// 
        /// 3) If the interface and one or more submodules are isochron and in the PLC is no OB6x assigned.
        /// </summary>
        public static bool CheckIsochronSubmodulesInIoDevice(Interface ifSubmodule)
        {
            if (ifSubmodule == null)
            {
                throw new ArgumentNullException(nameof(ifSubmodule));
            }
            AttributeAccessCode ac = new AttributeAccessCode();
            bool deviceValid = true;
            bool isochronEnabled =
                ifSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(InternalAttributeNames.PnIsochron, ac, false);
            DecentralDevice headModule = NavigationUtilities.GetHeadmodule(ifSubmodule);
            bool isSharedSupported = false;
            if (headModule != null)
            {
                isSharedSupported = headModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSharedDeviceSupported,
                    new AttributeAccessCode(),
                    false);
            }
            int nrOfIsochronSubmodules = 0;
            List<PclObject> modules = PNNavigationUtility.GetModulesFromInterfaceSubmodule(ifSubmodule).ToList();
            List<Submodule> submodules = modules.Where(m => m is Module).SelectMany(m => ((Module)m).GetSubmodules())
                .ToList();
            List<Submodule> virtualSubmodules = modules.Where(m => m is Module)
                .SelectMany(m => ((Module)m).GetVirtualSubmodules()).ToList();
            submodules.AddRange(virtualSubmodules);
            foreach (Submodule submodule in submodules)
            {
                bool isochronSubmodule = submodule.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.ClockSyncMode,
                    ac.GetNew(),
                    false);
                bool isochronRequired = submodule.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnIsochroneModeRequired,
                    ac.GetNew(),
                    false);
                bool isAssigned = true;
                if (isSharedSupported)
                {
                    isAssigned = SharedDeviceUtility.GetSharedAccess(submodule) == SharedIoAssignment.None;
                }
                if (isochronRequired && !isochronSubmodule)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        submodule,
                        ConsistencyConstants.ISOCHRONOUS_REQUIRED_AT_SUBMODUL);
                    deviceValid = false;
                }

                if (isochronSubmodule && isAssigned)
                {
                    nrOfIsochronSubmodules++;
                }

                PNIsochronIFDecentralBL.ClockSyncModeBlpCheck validationResult = ValidateIsochronActivatedBlp(ifSubmodule, submodule, GetClockSyncModeBlp(submodule));

                switch (validationResult)
                {
                    case PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.SharedModule:
                        {
                            ConsistencyLogger.Log(
                                ConsistencyType.PN,
                                LogSeverity.Error,
                                submodule,
                                ConsistencyConstants.SharedDevice_IsoNotPossibleWithSharedModule,
                                AttributeUtilities.GetName(submodule));
                            deviceValid = false;

                            break;
                        }
                    case PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.SharedIsochronousInterface:
                        {
                            DecentralDevice headmodule = NavigationUtilities.GetHeadmodule(ifSubmodule);
                            ConsistencyLogger.Log(
                                ConsistencyType.PN,
                                LogSeverity.Error,
                                submodule,
                                ConsistencyConstants.SharedDevice_IsoModuleNotPossibleWithSharedIF,
                                AttributeUtilities.GetName(submodule),
                                AttributeUtilities.GetName(headmodule),
                                AttributeUtilities.GetName(ifSubmodule));
                            deviceValid = false;

                            break;
                        }
                }
            }

            // 1) If one or more submodules are isochron and the interface is not isochron.
            if (!isochronEnabled
                && (nrOfIsochronSubmodules > 0))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    ifSubmodule,
                    ConsistencyConstants.ISOCHRONOUS_DISABLED_AT_INTERFACE);
                deviceValid = false;
            }

            // 2) If no submodules are isochron and the interface is isochron.
            if (isochronEnabled && (nrOfIsochronSubmodules == 0))
            {
                string deviceName = AttributeUtilities.GetName(NavigationUtilities.GetContainer(ifSubmodule));
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    ifSubmodule,
                    ConsistencyConstants.ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE,
                    deviceName);
                deviceValid = false;
            }
            // This check is only usefull, if at least one of the attributes "DPMasterSystemIdOB6x" exists.

            if (isochronEnabled && (nrOfIsochronSubmodules > 0))
            {
                Interface controllerInterface =
                    NavigationUtilities.GetControllerInterfaceOfDeviceInterface(ifSubmodule);

                if (controllerInterface == null)
                {
                    return deviceValid;
                }

                PNIOC cpu = controllerInterface.PNIOC;

                if (cpu == null)
                {
                    return deviceValid;
                }

                PclObject ioSystem =
                    NavigationUtilities.GetIoSystem(ifSubmodule);

                if (ioSystem == null)
                {
                    return deviceValid;
                }

                int ioSystemNumberOfCurrentDevice = AttributeUtilities.GetPositionNumber(ioSystem);

                string[] attributeIDs =
                    {
                        InternalAttributeNames.DPMasterSystemIdOB61, InternalAttributeNames.DPMasterSystemIdOB62,
                        InternalAttributeNames.DPMasterSystemIdOB63, InternalAttributeNames.DPMasterSystemIdOB64,
                        InternalAttributeNames.CoupledDistributedIoOB61,
                        InternalAttributeNames.CoupledDistributedIoOB62,
                        InternalAttributeNames.CoupledDistributedIoOB63, InternalAttributeNames.CoupledDistributedIoOB64
                    };

                bool isIoSystemAssigned = false;
                bool atLeastOneAttributeExists = false;

                foreach (string attId in attributeIDs)
                {
                    UInt32 assignedIoSystemId = cpu.AttributeAccess.GetAnyAttribute<UInt32>(attId, ac.GetNew(), 0);

                    if (ac.IsOkay)
                        atLeastOneAttributeExists = true;

                    if (ac.IsOkay
                        && (assignedIoSystemId == ioSystemNumberOfCurrentDevice))
                    {
                        isIoSystemAssigned = true;
                    }
                }

                if (atLeastOneAttributeExists && !isIoSystemAssigned)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.PN,
                        LogSeverity.Error,
                        ifSubmodule,
                        ConsistencyConstants.Clocksync_IOSystemIsNotAssignedToOB6x,
                        AttributeUtilities.GetName(ioSystem));
                    deviceValid = false;
                }
            }
            return deviceValid;
        }

        private static bool GetClockSyncModeBlp(PclObject submodule)
        {
            return GetClockSyncMode(submodule);
        }

        private static bool GetClockSyncMode(PclObject submodule)
        {
            bool clockSyncMode = submodule.AttributeAccess.GetAnyAttribute<bool>(
               InternalAttributeNames.ClockSyncMode,
               new AttributeAccessCode(),
               false);
            return clockSyncMode;
        }
        private static bool GetIsochronousModeRequiredBlp(PclObject submodule)
        {
            return GetIsochronousModeRequired(submodule);
        }
        private static bool GetIsochronousModeRequired(PclObject submodule)
        {
            return submodule.AttributeAccess.GetAnyAttribute<Boolean>(
                InternalAttributeNames.PnIsochroneModeRequired, new AttributeAccessCode(), false);
        }
        private static PNIsochronIFDecentralBL.ClockSyncModeBlpCheck ValidateIsochronActivatedBlp(PclObject ifSubmodule, PclObject submodule, bool value)
        {
            if (!value)
            {
                return PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.Valid;
            }

            bool isochronRequiredBlp = GetIsochronousModeRequiredBlp(submodule);

            if (isochronRequiredBlp)
            {
                return PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.Valid;
            }
            SharedIoAssignment assignmentValue = SharedDeviceUtility.GetSharedAccess(submodule);
            if (assignmentValue == SharedIoAssignment.NotAssigned)
            {
                return PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.SharedModule;
            }
            assignmentValue = SharedDeviceUtility.GetSharedAccess(ifSubmodule);

            // Check assignment
            if ((assignmentValue == SharedIoAssignment.NotAssigned))
            {
                return PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.SharedIsochronousInterface;
            }

            return PNIsochronIFDecentralBL.ClockSyncModeBlpCheck.Valid;
        }

        public static bool CheckIfIsoCouplingSupported(Interface ifSubmodule)
        {
            bool isIsochronActivated = GetIsochronActivatedBlp(ifSubmodule);
            bool isValid = true;
            if (isIsochronActivated)
            {
                PNIsochronIFDecentralBL.IsochronActivatedBlpCheck validationResult =
                    ValidateIsochronActivatedBlp(ifSubmodule, isIsochronActivated);
                switch (validationResult)
                {
                    case PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.IsochronousCouplingNotSupported:
                        {
                            string deviceName = AttributeUtilities.GetName(ifSubmodule);
                            string controllerName = AttributeUtilities.GetName(ifSubmodule.PNIOC);
                            ConsistencyLogger.Log(
                                ConsistencyType.PN,
                                LogSeverity.Error,
                                ifSubmodule,
                                ConsistencyConstants.IsoCouplingNotSupported,
                                deviceName,
                                controllerName);
                            isValid = false;
                            break;
                        }
                }
            }
            return isValid;
        }

        private static PNIsochronIFDecentralBL.IsochronActivatedBlpCheck ValidateIsochronActivatedBlp(Interface pnSubmodule, bool value)
        {
            if (!value)
            {
                return PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.Valid;
            }
            PNIOC controller = pnSubmodule.PNIOC;

            if (controller == null)
            {
                return PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.Valid;
            }

            SharedIoAssignment assignmentValue = SharedDeviceUtility.GetSharedAccess(pnSubmodule);

            // Check assignment
            if ((assignmentValue == SharedIoAssignment.NotAssigned))
            {
                return PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.SharedIsochronousInterface;
            }

            Interface centralIfSubmodule = NavigationUtilities.GetControllerInterfaceOfDeviceInterface(pnSubmodule);
            bool isomodeDecentralSupported = centralIfSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.IsoModeDecentralSupported,
                new AttributeAccessCode(),
                false);

            if (!isomodeDecentralSupported)
            {
                return PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.IsochronousCouplingNotSupported;
            }
            return PNIsochronIFDecentralBL.IsochronActivatedBlpCheck.Valid;
        }

        /// <summary>
        /// Check if the send clock (which is equals to the update time of the isochronous device) is greater than the Ti/To value
        /// </summary>
        /// <param name="pnInterface">PN Interface</param>
        /// <returns>True if the send clock is greater than the TI/To value, and false, if it is lower.</returns>
        public static bool CheckUpdateTimeGreaterThanTiToValue(Interface pnInterface)
        {
            if (pnInterface == null)
            {
                throw new ArgumentNullException(nameof(pnInterface));
            }
            bool isValid = true;
            if (!PNPlannerUtility.IsPNPlannerSuccess(pnInterface))
            {
                return false;
            }
            decimal sendClock = GetSendclockBlp(pnInterface);
            if (!ValidateSendclockBlp(pnInterface, sendClock))
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, pnInterface,
                     ConsistencyConstants.TiToCannotBeCalculated,
                        AttributeUtilities.GetName(pnInterface),
                        sendClock.ToString("0.000", CultureInfo.InvariantCulture));
                isValid = false;
            }
            return isValid;
        }
        private static decimal GetSendclockBlp(Interface pnInterface)
        {
            int scf;
            AttributeAccessCode code = new AttributeAccessCode();
            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(pnInterface);
            decimal scfms = 0;

            if (controllerInterfaceSubmodule != null)
            {
                scf = (int)controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int64>(
                    InternalAttributeNames.PnIoSendClockFactor, code, 0);

                scfms = scf * PNTimeBase;
            }

            return scfms;
        }
        private static decimal GetTiToBaseBlp(Interface pnInterface)
        {
            long tiToTimeBase = GeneralUtilities.GetTiToTimeBase(pnInterface);

            return tiToTimeBase / MillisecToNanosec;
        }

        private static decimal GetTDcMinBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long tDcBase = pnInterface.AttributeAccess.
                                GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_BASE, code, 1);
            long tDcMin = pnInterface.AttributeAccess.
                                GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_MIN, code, 1);

            return tDcBase * tDcMin * PNTimeBase;
        }

        private static decimal GetTDcMaxBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long tDcBase = pnInterface.AttributeAccess.
                                GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_BASE, code, 1);
            long tDcMax = pnInterface.AttributeAccess.
                                GetAnyAttribute<long>(InternalAttributeNames.PnIsoT_DC_MAX, code, 1);

            return tDcBase * tDcMax * PNTimeBase;
        }


        private static uint GetTiToModeBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            return pnInterface.AttributeAccess.
                                GetAnyAttribute<uint>(InternalAttributeNames.PnIsoTiToCalcMode, code, 0);
        }

        private static bool GetIsochronActivatedBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            bool isochronActivated = pnInterface.AttributeAccess.
                GetAnyAttribute<bool>(InternalAttributeNames.PnIsochron, code, false);

            return isochronActivated;
        }
        private static decimal GetTiBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            uint titoModeBlp = GetTiToModeBlp(pnInterface);
            decimal currentTi;

            if ((PNIsoTiToCalcMode)titoModeBlp == PNIsoTiToCalcMode.AutomaticMinimum)
            {
                currentTi = GetTiMinBlp(pnInterface);
            }
            else
            {
                long tiValue = pnInterface.AttributeAccess.
                            GetAnyAttribute<long>(InternalAttributeNames.PnIsoTi, code, 0);

                currentTi = Convert.ToDecimal(tiValue, CultureInfo.InvariantCulture) / MillisecToNanosec;
            }

            return currentTi;
        }

        private static decimal GetToBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            uint titoModeBlp = GetTiToModeBlp(pnInterface);
            decimal currentTo;

            if ((PNIsoTiToCalcMode)titoModeBlp == PNIsoTiToCalcMode.AutomaticMinimum)
            {
                currentTo = GetToMinBlp(pnInterface);
            }
            else
            {
                long toValue = pnInterface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoTo, code, 0);

                currentTo = Convert.ToDecimal(toValue, CultureInfo.InvariantCulture) / MillisecToNanosec;
            }

            return currentTo;
        }

        private static decimal GetTiMinBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long tiMinCalculated = pnInterface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoTiMinCalculated, code, 0);

            return Convert.ToDecimal(tiMinCalculated, CultureInfo.InvariantCulture) / MillisecToNanosec;
        }

        private static decimal GetToMinBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long toMinCalculated = pnInterface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoToMinCalculated, code, 0);

            return Convert.ToDecimal(toMinCalculated, CultureInfo.InvariantCulture) / MillisecToNanosec;
        }

        private static decimal GetTiMaxBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long tiMaxCalculated = pnInterface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoTiRanges, code, 0);

            return Convert.ToDecimal(tiMaxCalculated, CultureInfo.InvariantCulture) / MillisecToNanosec;
        }

        private static decimal GetToMaxBlp(PclObject pnInterface)
        {
            AttributeAccessCode code = new AttributeAccessCode();
            long toMaxCalculated = pnInterface.AttributeAccess.GetAnyAttribute<long>(InternalAttributeNames.PnIsoToRanges, code, 0);

            return Convert.ToDecimal(toMaxCalculated, CultureInfo.InvariantCulture) / MillisecToNanosec;
        }
        private static bool ValidateSendclockBlp(PclObject pnInterface, decimal sendClock)
        {
            //Check if IsoChronMode active
            if (!GetIsochronActivatedBlp(pnInterface))
            {
                return true;
            }
            decimal tiMin = GetTiMinBlp(pnInterface);
            decimal tiMax = GetTiMaxBlp(pnInterface);
            decimal toMin = GetToMinBlp(pnInterface);
            decimal toMax = GetToMaxBlp(pnInterface);
            decimal tDcMin = GetTDcMinBlp(pnInterface);
            decimal tDcMax = GetTDcMaxBlp(pnInterface);

            if (tiMax > sendClock || toMax > sendClock
                || tiMin > tiMax || toMin > toMax
                || tDcMin > sendClock || tDcMax < 0 || tDcMax < sendClock
                )
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Check if the Ti/To values are in the allowed range
        /// </summary>
        /// <param name="pnInterface">PN interface</param>
        /// <returns>True if the Ti/To values are in the allowed range, false, if they are outside of the range.</returns>
        public static void CheckIsochronTiToValueRange(Interface pnInterface)
        {
            if (pnInterface == null)
            {
                throw new ArgumentNullException(nameof(pnInterface));
            }
            decimal ti = GetTiBlp(pnInterface);
            decimal tiBase = GetTiToBaseBlp(pnInterface);
            PNIsochronIFDecentralBL.TiBlpCheck tiCheckResult = ValidateTiBlp(pnInterface, ti);
            switch (tiCheckResult)
            {
                case PNIsochronIFDecentralBL.TiBlpCheck.TitoGreaterThanAppCycle:
                case PNIsochronIFDecentralBL.TiBlpCheck.InvalidTi:
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            pnInterface,
                            ConsistencyConstants.ValidationErrorInvalidTi);
                        break;
                    }

                case PNIsochronIFDecentralBL.TiBlpCheck.NotInRaster:
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            pnInterface,
                            ConsistencyConstants.ValidationErrorTiNotMultiplesOfRaster,
                            ti.ToString("0.000", CultureInfo.InvariantCulture),
                            tiBase.ToString("0.000", CultureInfo.InvariantCulture));
                        break;
                    }
            }
            decimal to = GetToBlp(pnInterface);
            decimal toBase = GetTiToBaseBlp(pnInterface);
            PNIsochronIFDecentralBL.ToBlpCheck toCheckResult = ValidateToBlp(pnInterface, to);
            switch (toCheckResult)
            {
                case PNIsochronIFDecentralBL.ToBlpCheck.TitoGreaterThanAppCycle:
                case PNIsochronIFDecentralBL.ToBlpCheck.InvalidTo:
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            pnInterface,
                            ConsistencyConstants.ValidationErrorInvalidTo);
                        break;
                    }
                case PNIsochronIFDecentralBL.ToBlpCheck.NotInRaster:
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.PN,
                            LogSeverity.Error,
                            pnInterface,
                            ConsistencyConstants.ValidationErrorToNotMultiplesOfRaster,
                            to.ToString("0.000", CultureInfo.InvariantCulture),
                            toBase.ToString("0.000", CultureInfo.InvariantCulture));
                        break;
                    }
            }

            if ((tiCheckResult == PNIsochronIFDecentralBL.TiBlpCheck.TitoGreaterThanAppCycle)
                || (toCheckResult == PNIsochronIFDecentralBL.ToBlpCheck.TitoGreaterThanAppCycle))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    pnInterface,
                    ConsistencyConstants.TiToGreaterThanAppCycle);
            }
        }

        private static PNIsochronIFDecentralBL.TiBlpCheck ValidateTiBlp(Interface pnInterface, decimal value)
        {
            //Check if IsoChronMode active
            if (!GetIsochronActivatedBlp(pnInterface))
            {
                return PNIsochronIFDecentralBL.TiBlpCheck.Valid;
            }

            decimal tiMin = GetTiMinBlp(pnInterface);
            decimal tiMax = GetTiMaxBlp(pnInterface);
            float appCycle = GetAppCycleFromDeviceInterface(pnInterface);

            if (tiMax < tiMin)
            {
                return PNIsochronIFDecentralBL.TiBlpCheck.TiToCannotBeCalculated;
            }

            bool isPossible = (value >= tiMin) && (value <= tiMax);

            if (!isPossible)
            {
                if ((decimal)appCycle < value)
                {
                    return PNIsochronIFDecentralBL.TiBlpCheck.TitoGreaterThanAppCycle;
                }
                return PNIsochronIFDecentralBL.TiBlpCheck.InvalidTi;
            }

            decimal raster = GetTiToBaseBlp(pnInterface);
            long ti = (long)(value * MillisecToNanosec);
            long rasterValue = (long)(raster * MillisecToNanosec);

            if (ti % rasterValue != 0)
            {
                return PNIsochronIFDecentralBL.TiBlpCheck.NotInRaster;
            }

            return PNIsochronIFDecentralBL.TiBlpCheck.Valid;
        }
        private static PNIsochronIFDecentralBL.ToBlpCheck ValidateToBlp(Interface pnInterface, decimal value)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool isIsochronActivated =
                pnInterface.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIsochron, ac.GetNew(),
                     false);
            //Check if IsoChronMode active
            if (!isIsochronActivated)
            {
                return PNIsochronIFDecentralBL.ToBlpCheck.Valid;
            }

            decimal toMin = GetToMinBlp(pnInterface);
            decimal toMax = GetToMaxBlp(pnInterface);
            float appCycle = GetAppCycleFromDeviceInterface(pnInterface);

            if (toMax < toMin)
            {
                return PNIsochronIFDecentralBL.ToBlpCheck.TiToCannotBeCalculated;
            }
            bool isPossible = (value >= toMin) && (value <= toMax);

            if (!isPossible)
            {
                if ((decimal)appCycle < value)
                {
                    return PNIsochronIFDecentralBL.ToBlpCheck.TitoGreaterThanAppCycle;
                }
                return PNIsochronIFDecentralBL.ToBlpCheck.InvalidTo;
            }

            decimal raster = GetTiToBaseBlp(pnInterface);
            long to = (long)(value * MillisecToNanosec);
            long rasterValue = (long)(raster * MillisecToNanosec);

            if (to % rasterValue != 0)
            {
                return PNIsochronIFDecentralBL.ToBlpCheck.NotInRaster;
            }

            return PNIsochronIFDecentralBL.ToBlpCheck.Valid;
        }
        /// <summary>
        /// Returns the Application cycle of the connected controller
        /// </summary>
        /// <param name="deviceInterface">Device interface submodule</param>
        /// <returns>Application cycle</returns>
        private static float GetAppCycleFromDeviceInterface(Interface deviceInterface)
        {
            if (deviceInterface == null)
            {
                throw new ArgumentNullException(nameof(deviceInterface));
            }

            //get the sendclockfactor from the IO-Controller
            AttributeAccessCode ac = new AttributeAccessCode();
            int cacf = 1;
            int scf = 0;
            Interface controllerInterfaceSubmodule =
                NavigationUtilities.GetControllerInterfaceOfDeviceInterface(deviceInterface);

            if (controllerInterfaceSubmodule != null)
            {
                scf = (int)controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int64>(
                            InternalAttributeNames.PnIoSendClockFactor,
                            ac, 0);
            }

            PclObject ioSystem =
                NavigationUtilities.GetIoSystem(deviceInterface);

            if (ioSystem != null)
            {
                cacf = ioSystem.AttributeAccess.GetAnyAttribute<Int32>(InternalAttributeNames.PnIsoCacf,
                    ac.GetNew(), 1);
            }

            float appCycle = scf * cacf * PNConstants.PNTimeBase;

            return appCycle;
        }
        /// <summary>
        /// If IO Device is in isochron mode IRTtop (not flex) has to be activated at the IO device.
        /// </summary>
        /// <param name="device">the IO Device</param>
        /// <returns>false if the IO device is in iscochron mode and the RT class is not IRTtop</returns>
        public static bool CheckIRTtopAndIsochronMode(Interface device)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool valid = true;
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }
            bool isochronEnabled = device.AttributeAccess.GetAnyAttribute<Boolean>(InternalAttributeNames.PnIsochron, ac,
                false);
            if (isochronEnabled)
            {
                PNRTClass rtClass = ConfigUtility.GetRtClassfromFrameDataList(device);

                if (rtClass != PNRTClass.IrtTop)
                {
                    valid = false;
                    string deviceName = AttributeUtilities.GetName(device);
                    ConsistencyLogger.Log(ConsistencyType.PN,
                        LogSeverity.Error,
                        device,
                        ConsistencyConstants.DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET,
                        deviceName);
                }
            }
            return valid;
        }

        /// <summary>
        /// Check controller application cycle time against max. norm value
        /// </summary>
        /// <param name="ioSystem">assigned IO-System</param>
        /// <param name="cacf"> the user adjusted setting of controller application cycle for OB</param>
        /// <param name="obNumberStr">string containing the ob number</param>
        /// <returns>true if cacf is smaller than or equal to max. norm value (= 32 000) </returns>
        public static bool CheckMaxControllerAppTime(
            DataModel.PCLObjects.IOSystem ioSystem,
            long cacf,
            string obNumberStr)
        {
            if (ioSystem == null)
            {
                return true;
            }

            PNIOC ioController = ioSystem.PNIOC;
            if (ioController == null)
            {
                return true;
            }
            Interface controllerInterfaceSubmodule = (Interface)ioController.ParentObject;
            if (controllerInterfaceSubmodule == null)
            {
                return true;
            }

            long sendClockFactor =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<long>(
                    InternalAttributeNames.PnIoSendClockFactor,
                    new AttributeAccessCode(),
                    0);
            double appCycleTime = cacf * sendClockFactor * s_PNTimeBase;
            if (appCycleTime <= PNConstants.MaxControllerAppCycleTime)
            {
                return true;
            }

            ConsistencyLogger.Log(ConsistencyType.PN,
                LogSeverity.Error,
                controllerInterfaceSubmodule,
                ConsistencyConstants.ObControllerAppCycleTooHigh,
                obNumberStr,
                appCycleTime);
            return false;
        }

        /// <summary>
        /// Returns the OB-PIP Settings of the CPU
        /// </summary>
        /// <param name="cpu">CPU object</param>
        /// <returns>
        /// Returns with a Dictionary object. The key of the dictionary is the IO-System or DP Mastersystem ID, the values
        /// are the lists of PIPs
        /// </returns>
        private static Dictionary<int, List<int>> GetIsochronousObPipSettings(PclObject cpu)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            Dictionary<int, List<int>> isochronousObSettings = new Dictionary<int, List<int>>();

            if (cpu == null)
            {
                return isochronousObSettings;
            }

            foreach (string isochronOb in PNConstants.IsochronousOBs)
            {
                uint masterSystemID =
                    cpu.AttributeAccess.GetAnyAttribute<uint>(
                        string.Format(CultureInfo.InvariantCulture, "{0}{1}", s_DpMasterSystemId, isochronOb),
                        ac.GetNew(),
                        0);

                if (masterSystemID != 0)
                {
                    string obpipNumber =
                        cpu.AttributeAccess.GetAnyAttribute<string>(
                            string.Format(CultureInfo.InvariantCulture, "{0}{1}", s_PartialProcessImageOb, isochronOb),
                            ac.GetNew(),
                            string.Empty);
                    List<int> obpipNumbers = SplitNumberList(obpipNumber);

                    isochronousObSettings[(int)masterSystemID] = obpipNumbers;
                }
            }

            return isochronousObSettings;
        }

        /// <summary>
        /// Split the given string parameter to a list of integers
        /// </summary>
        /// <param name="pip">String parameter (the string can contains commas and intervals, e.g.: 1,2-4,8)</param>
        /// <returns>List of integers</returns>
        private static List<int> SplitNumberList(string pip)
        {
            List<int> resultList = new List<int>();

            if (string.IsNullOrEmpty(pip))
            {
                return resultList;
            }

            string pattern = @"^\d\d?(-\d\d?)?(,\d\d?(-\d\d?)?)*$";
            if (!Regex.IsMatch(pip, pattern))
            {
                return resultList;
            }

            string[] parts = pip.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string part in parts)
            {
                string[] subparts = part.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);
                int min = 0;
                int max = 0;
                if (subparts.Length > 0)
                {
                    if (!int.TryParse(subparts[0], NumberStyles.Integer, CultureInfo.InvariantCulture, out min))
                    {
                        return resultList;
                    }

                    max = min;
                }
                if (subparts.Length > 1)
                {
                    if (!int.TryParse(subparts[1], NumberStyles.Integer, CultureInfo.InvariantCulture, out max))
                    {
                        return resultList;
                    }
                }

                for (int number = min; number <= max; number++)
                {
                    resultList.Add(number);
                }
            }

            return resultList;
        }
    }
}