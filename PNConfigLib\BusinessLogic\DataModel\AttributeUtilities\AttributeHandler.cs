/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: AttributeHandler.cs                       :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.DataModel.AttributeUtilities
{
    /// <summary>
    /// The class that contains get and set handlers for attribute access.
    /// </summary>
    /// <typeparam name="T">Type of the attribute.</typeparam>
    internal class AttributeHandler<T>
    {
        /// <summary>
        /// The property for get handler.
        /// </summary>
        public GetHandler AttributeGetter;

        /// <summary>
        /// The property for set handler.
        /// </summary>
        public SetHandler AttributeSetter;

        /// <summary>
        /// The value used during set operation.
        /// </summary>
        public T Value;

        /// <summary>
        /// The delegate for get handler.
        /// </summary>
        /// <returns>The calculated attribute value.</returns>
        public delegate T GetHandler();

        /// <summary>
        /// The delegate for set handler.
        /// </summary>
        /// <param name="value">The value to be set.</param>
        public delegate void SetHandler(T value);
    }
}