/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Utility.cs                                :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using PNConfigLib.BusinessLogic.HWCNBL.Constants.Methods;
using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.AdapterFramework.Utilities;
using PNConfigLib.HWCNBL.Basics;
using PNConfigLib.HWCNBL.Common;
using PNConfigLib.HWCNBL.Common.DataRecordStructs;
using PNConfigLib.HWCNBL.Common.DataRecordStructs.DataRecordsConf;
using PNConfigLib.HWCNBL.Common.DataRecordStructs.IODevParamConfig;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Constants.Methods;
using PNConfigLib.HWCNBL.DomainManagement;
using PNConfigLib.HWCNBL.Isochrone;
using PNConfigLib.HWCNBL.MultipleMrp;
using PNConfigLib.HWCNBL.PNFunctions._Interfaces;
using PNConfigLib.HWCNBL.Tailor.Config;
using PNConfigLib.HWCNBL.Utilities.Addresses;
using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Utilities
{
    internal static class Utility
    {
        /// <summary>
        /// Calculate the local watchdog factor for LocalSCFAdaptionEntry and UpdateTimeWatchDog consistency check
        /// </summary>
        /// <param name="frameData">PNFrameData</param>
        /// <param name="controllerSendClockFactor">Transient value of SendClockFactor of the controller Interface Submodule</param>
        internal static int CalcLocalWatchDogFactor(IPNFrameData frameData, long controllerSendClockFactor)
        {
            int wdf;
            int[] wdfArray = new int[3];

            #region ceil( (SC-IOD * RR-IOD * WDF-IOD) / (SC-IOC * RR-IOC) )

            wdf =
                (int)
                Math.Ceiling(
                    frameData.SendClockFactor * frameData.DeviceLocalReductionRatio * frameData.WatchdogFactor
                    / (double)(controllerSendClockFactor * frameData.ControllerLocalReductionRatio));
            wdfArray[0] = wdf;

            #endregion

            #region floor( 1920000 µs / (SC-IOC * RR-IOC) )

            wdf =
                (int)
                Math.Floor(
                    PNConstants.MaxAllowedWatchdogTime
                    / (double)
                    (controllerSendClockFactor * frameData.ControllerLocalReductionRatio * PNConstants.PNTimeBase));
            wdfArray[1] = wdf;

            #endregion

            #region MaxAllowedWatchdogFactor = 255

            wdfArray[2] = (int)PNConstants.MaxAllowedWatchdogFactor;

            #endregion

            // WDF-IOC = MIN( ceil(...), floor(...), 255 )
            return wdfArray.Min();
        }
        internal static void LogAddressesErrors(List<long> frameLengths, Interface controllerInterfaceSubmodule)
        {
            AttributeAccessCode acOutputDataLength = new AttributeAccessCode();
            AttributeAccessCode acInputDataLength = new AttributeAccessCode();
            Int64 pnIoMaxControllerOutputDataLength = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int64>(
                InternalAttributeNames.PnIoMaxControllerOutputDataLength, acOutputDataLength, 8192);
            Int64 pnIoMaxControllerInputDataLength = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int64>(
                InternalAttributeNames.PnIoMaxControllerInputDataLength, acInputDataLength, 8192);
            
            if (acInputDataLength.IsOkay && (pnIoMaxControllerInputDataLength < frameLengths[0]))
            {
                object[] errorParameters = new object[2];
                errorParameters[0] = pnIoMaxControllerInputDataLength;
                errorParameters[1] = frameLengths[0];
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, 
                    controllerInterfaceSubmodule, 
                    ConsistencyConstants.ControllerErrorMaxIAddressWithinAString,
                    errorParameters);
            }

            if (acOutputDataLength.IsOkay && (pnIoMaxControllerOutputDataLength < frameLengths[1]))
            {
                object[] errorParameters = new object[2];
                errorParameters[0] = pnIoMaxControllerOutputDataLength;
                errorParameters[1] = frameLengths[1];
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    controllerInterfaceSubmodule,
                    ConsistencyConstants.ControllerErrorMaxOAddressWithinAString,
                    errorParameters);
            }
        }
        /// <summary>
        /// Gets the address length of the given deviceitem and its elements.
        /// </summary>
        /// <param name="deviceItem">The device item whose address length will be retrieved.</param>
        /// <param name="paramObjectRefreshSlcInput">The parameter object used in frame generation.</param>
        /// <param name="ioCsLength">Output length value.</param>
        /// <param name="ioPsLength">Input length value.</param>
        internal static void GetAddressLengthOfDeviceItem(
            PclObject deviceItem,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput,
            int ioCsLength,
            int ioPsLength)
        {
            if (deviceItem == null)
            {
                throw new ArgumentNullException(nameof(deviceItem));
            }

            AttributeAccessCode ac = new AttributeAccessCode();

            bool isAssigned = true;
            if (paramObjectRefreshPNPlannerInput.SharedDeviceSupported)
            {
                uint assignment;
                assignment = deviceItem.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.SharedIoAssignment,
                    ac,
                    0);
                isAssigned = (SharedIoAssignment)assignment == SharedIoAssignment.None;
            }

            List<PclObject> elements = (List<PclObject>)deviceItem.GetElements();
            if (isAssigned)
            {
                ioCsLength = deviceItem.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIOCSLength,
                    ac.GetNew(),
                    ioCsLength);

                ioPsLength = deviceItem.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIOPSLength,
                    ac.GetNew(),
                    ioPsLength);

                AddFrameLengths(deviceItem, paramObjectRefreshPNPlannerInput, ioCsLength, ioPsLength);
            }

            foreach (PclObject element in elements)
            {
                GetAddressLengthOfDeviceItem(element, paramObjectRefreshPNPlannerInput, ioCsLength, ioPsLength);
            }
        }

        /// <summary>
        /// Checks for each IOD if they have a common IO startup mode with IOC. If not returns that IOD
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <returns></returns>
        internal static Interface ControllerHasCommonIoStartupMode(Interface controllerInterfaceSubmodule)
        {
            IEnumerable<Interface> devices = controllerInterfaceSubmodule.PNIOC.IOSystem.GetParticipants()
                .Select(s => s.GetInterface());

            Enumerated controllerPNIoArStartupModeEnumerated =
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoArStartupMode,
                    new AttributeAccessCode(),
                    null);
            if (controllerPNIoArStartupModeEnumerated != null)
            {
                IEnumerable<PNIOARStartupMode> iocSupportedIoArStartupModes =
                    controllerPNIoArStartupModeEnumerated.List.Cast<PNIOARStartupMode>();

                foreach (Interface deviceInterface in devices)
                {
                    if (IsIrtTopDevice(deviceInterface))
                    {
                        continue;
                    }

                    Enumerated devicePNIoArStartupModeEnumerated =
                        deviceInterface.AttributeAccess.GetAnyAttribute<Enumerated>(
                            InternalAttributeNames.PnIoArStartupMode,
                            new AttributeAccessCode(),
                            null);
                    if (devicePNIoArStartupModeEnumerated != null)
                    {
                        IEnumerable<PNIOARStartupMode> iodSupportedIoArStartupModes =
                            devicePNIoArStartupModeEnumerated.List.Cast<PNIOARStartupMode>();

                        if (!iocSupportedIoArStartupModes.Intersect(iodSupportedIoArStartupModes).Any())
                        {
                            return deviceInterface;
                        }
                    }
                }
            }
            return null;
        }

        #region ALARMCR_DATA

        internal static byte[] GetAlarmCrData(IPNDeviceConfigStrategy configStrategy)
        {
            AlarmCRDataRecordStruct alarmCr = new AlarmCRDataRecordStruct();

            alarmCr.AlarmCRBlockVersion = 0x0100;
            alarmCr.AlarmCRType = 1;
            alarmCr.RTATimeoutFactor = configStrategy.GetRTATimeoutFactor();
            alarmCr.RTARetries = configStrategy.GetRTARetries();
            alarmCr.AlarmCRProperties =
                configStrategy.GetAttributeValue<uint>(InternalAttributeNames.AlarmCRProperties, 0);
            alarmCr.AlarmCRTagHeaderHigh =
                configStrategy.GetAttributeValue<int>(InternalAttributeNames.AlarmCRTagHeaderHigh, 0xC000);
            alarmCr.AlarmCRTagHeaderLow =
                configStrategy.GetAttributeValue<int>(InternalAttributeNames.AlarmCRTagHeaderLow, 0xA000);

            alarmCr.Ethertype = 0x8892;

            return alarmCr.ToByteArray;
        }

        #endregion

        #region AR_COMMUNICATION_DATA

        /// <summary>
        /// </summary>
        /// <returns>AR-Table Communication-Block als ByteArray</returns>
        private static byte[] GetArCommunicationDataPlus(
            IPNDeviceConfigStrategy configStrategy,
            Interface ioControllerInterface)
        {
            byte[] communicationData;

            IEnumerable<PNIOARStartupMode> supportedIoArStartupModes =
                GetSupportedIoArStartupModes(ioControllerInterface);
            List<PNIrtArStartupMode> supportedIrtArStartupModes = GetSupportedIrtArStartupModes(ioControllerInterface);

            bool isNewBlockRequired = supportedIoArStartupModes.Contains(PNIOARStartupMode.Advanced)
                                      || supportedIrtArStartupModes.Contains(PNIrtArStartupMode.Advanced);

            Guid arUuid = configStrategy.GetArUuid();

            if (!isNewBlockRequired)
            {
                ArCommunicationDataStruct communication = new ArCommunicationDataStruct();
                communication.ARBlockVersion = 0x0100;
                communication.ARType = configStrategy.GetArType();
                communication.ARUuid = arUuid.ToByteArray();
                communication.ARProperties = configStrategy.GetArProperties();
                return communication.ToByteArray;
            }

            ArCommunicationDataStructV11 communication64 = new ArCommunicationDataStructV11();
            communication64.ARBlockVersion = 0x0100;

            communication64.ARType = configStrategy.GetArType();
            communication64.ARUuid = arUuid.ToByteArray();
            communication64.ARProperties = configStrategy.GetArProperties();
            communication64.RejectDcpSetRequests = configStrategy.IsDcpReadOnlyEnabled();
            communication64.CMIActivityTimeout = configStrategy.GetCmiActivityTimeout();

            communicationData = communication64.ToByteArray;

            return communicationData;
        }

        #endregion

        #region AR_RECORD_DATA

        private static byte[] GetArRecordTablePlus(IPNDeviceConfigStrategy configStrategy)
        {
            int recordCountCount = 0;
            ArRecordDataStruct arRecordData = new ArRecordDataStruct();

            if (configStrategy.IsARRecordSupported())
            {
                if (configStrategy.IsArRecordActive())
                {
                    IList<byte[]> arRecords = configStrategy.GetARRecordData();

                    foreach (byte[] arRecord in arRecords)
                    {
                        int alignment = BufferManager.Alignment(arRecord.Length, 8);

                        if (alignment != 0)
                        {
                            byte[] narRecord = new byte[arRecord.Length + alignment];
                            Array.Copy(arRecord, 0, narRecord, 0, arRecord.Length);
                            arRecordData.AddSubBlock(narRecord);
                        }
                        else
                        {
                            arRecordData.AddSubBlock(arRecord);
                        }
                        recordCountCount++;
                    }
                }

                arRecordData.RecordCount = recordCountCount;

                return arRecordData.RecordCount > 0 ? arRecordData.ToByteArray : null;
            }

            // fast startup isn't supported by device
            return null;
        }

        #endregion

        /// <summary>
        /// Gets the default reduction ratio value for unsynchronized frames of a given controller interface.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">
        /// The controller interface whose default redaction ratio value will be
        /// retrieved.
        /// </param>
        /// <returns>
        /// 2 if controller supports PNIrtSupportedSyncProtocols.RTSync and the PNIoAllowSlot0WithoutSubmodule1 attribute value is
        /// true.
        /// 1 otherwise or if controllerInterfaceSubmodule is null.
        /// </returns>
        internal static int GetDefaultRRForUnsyncFrames(PclObject controllerInterfaceSubmodule)
        {
            int defaultRR = 1;
            AttributeAccessCode ac = new AttributeAccessCode();
            if (controllerInterfaceSubmodule != null)
            {
                bool pnIoAllowSlot0WithoutSubmodule1 =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoAllowSlot0WithoutSubmodule1,
                        ac,
                        false);

                Enumerated suppSyncProtocols =
                    controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                        InternalAttributeNames.PnIrtSupportedSyncProtocols,
                        new AttributeAccessCode(),
                        new Enumerated());

                List<PNIRTSupportedSyncProtocols> supportedSyncProtocols = new List<PNIRTSupportedSyncProtocols>();
                foreach (object syncProtocol in suppSyncProtocols.List)
                {
                    supportedSyncProtocols.Add((PNIRTSupportedSyncProtocols)syncProtocol);
                }

                if (supportedSyncProtocols.Contains(PNIRTSupportedSyncProtocols.RTSync)
                    || pnIoAllowSlot0WithoutSubmodule1)
                {
                    defaultRR = 2;
                }
            }

            return defaultRR;
        }

        /// <summary>
        /// This function returns the IO device data block
        /// </summary>
        private static byte[] GetDeviceDataBlockPlus(IPNDeviceConfigStrategy configStrategy)
        {
            IoDevicePropertiesStruct ioDeviceData = new IoDevicePropertiesStruct();

            ioDeviceData.VendorID = configStrategy.GetVendorId();
            ioDeviceData.DeviceIdentNumber = configStrategy.GetDeviceId();
            ioDeviceData.InstanceId = configStrategy.GetDeviceInstance();

            ioDeviceData.MaxRecordSize =
                (int)configStrategy.GetAttributeValue<uint>(InternalAttributeNames.PnIoMaxRecordSupported, 4068);

            ioDeviceData.MultipleWriteSupported = configStrategy.IsMultipleWriteSupported();

            ioDeviceData.CheckDeviceID = configStrategy.GetCheckDeviceId();

            if (configStrategy.IsAllowNameOfStationOverwriteSupported())
            {
                ioDeviceData.AllowNameOfStationOverwrite = configStrategy.IsAllowNameOfStationOverwriteActive();
            }

            ioDeviceData.UseIndividualDefaultRouter = configStrategy.IsDeviceDefaultRouterIndividual();
            
            // fast startup settings
            PNStartupMode startupMode = (PNStartupMode)configStrategy.GetStartupMode();
            if (startupMode == PNStartupMode.NSU)
            {
                ioDeviceData.HighPrioScanCycleIn10ms = 0;
                ioDeviceData.FastStartupProcedure = false;
                ioDeviceData.AcceleratedStartupProcedure = false;
            }
            else if (startupMode == PNStartupMode.ASU)
            {
                ioDeviceData.HighPrioScanCycleIn10ms = 50;
                ioDeviceData.FastStartupProcedure = false;
                ioDeviceData.AcceleratedStartupProcedure = true;
            }
            else if (startupMode == PNStartupMode.FSU)
            {
                ioDeviceData.HighPrioScanCycleIn10ms = 50;
                ioDeviceData.FastStartupProcedure = true;
                ioDeviceData.AcceleratedStartupProcedure = true;
            }

            return ioDeviceData.ToByteArray;
        }

        internal static List<Interface> GetInterfacesOnShortestPath(
            Interface sourceInterface,
            Interface targetInterface)
        {
            List<Interface> path = new List<Interface>();
            List<int> breadCumbs = new List<int>();

            path.Add(sourceInterface);
            breadCumbs.Add(-1);

            int index = 0;
            Interface current = null;
            while ((index < path.Count) && ((current = path[index]) != targetInterface))
            {
                List<Interface> interconnectedInterfaces = NavigationUtilities.GetConnectedInterfaces(current);
                if (null == interconnectedInterfaces)
                {
                    continue;
                }

                foreach (Interface currentInterface in interconnectedInterfaces)
                {
                    if (path.Contains(currentInterface))
                    {
                        continue;
                    }

                    path.Add(currentInterface);
                    breadCumbs.Add(index);
                }

                index++;
            }

            if (current != targetInterface)
            {
                return new List<Interface>();
            }

            index--;
            Stack<Interface> result = new Stack<Interface>();
            while (index != -1)
            {
                result.Push(path[index]);
                index = breadCumbs[index];
            }

            return new List<Interface>(result);
        }

        internal static void GetIocrEntry(IMethodData methodData, Interface deviceInterfaceSubmodule)
        {
            IPNFrameData frameData =
                (IPNFrameData)methodData.Arguments[Constants.Methods.GetIocrEntry.PNFrameData];

            if (null == frameData)
            {
                methodData.ReturnValue = false;
                return;
            }

            methodData.ReturnValue = true;

            List<IPNPlannerOutputFrame> frameBlocks = null;

            methodData.Arguments[Constants.Methods.GetIocrEntry.IOCREntry] =
                CreateConfigAriocrEntryStruct(deviceInterfaceSubmodule, frameData, ref frameBlocks).ToByteArray;
            frameData =
                (IPNFrameData)
                methodData.Arguments[Constants.Methods.GetIocrEntry.PNFrameData2];
            if (frameData != null)
            {
                methodData.Arguments[Constants.Methods.GetIocrEntry.IOCREntry2] =
                    CreateConfigAriocrEntryStruct(deviceInterfaceSubmodule, frameData, ref frameBlocks).ToByteArray;
            }
        }

        internal static ParameterDataBlockStruct GetIoDevParamData(
            IPNDeviceConfigStrategy configStrategy,
            Interface interfaceSubmodule,
            Interface ioControllerInterface,
            bool isLocaliDevice)
        {
            ParameterDataBlockStruct paramDataBlock = new ParameterDataBlockStruct(false);

            configStrategy.InitializeArData(ioControllerInterface);

            IList<PclObject> modules = configStrategy.GetModules();

            //PNIODProperties 
            byte[] ioDevPropertiesData = GetDeviceDataBlockPlus(configStrategy);
            if (ioDevPropertiesData != null)
            {
                ParameterDatasetStruct ioDevPropertiesDataRecord = new ParameterDatasetStruct();
                ioDevPropertiesDataRecord.ParaDSNumber = (int)DataRecords.Indexes.PNIODProperties;
                ioDevPropertiesDataRecord.AddParaBlock(ioDevPropertiesData);
                paramDataBlock.AddParamDSBlock(ioDevPropertiesDataRecord);
            }

            //ArConfigurationData
            ExpectedSubmoduleDataStruct arConfigData = GetArConfigurationDataPlus(configStrategy, modules);
            if (arConfigData != null)
            {
                ParameterDatasetStruct arConfigDataRecord = new ParameterDatasetStruct();
                if (isLocaliDevice)
                {
                    arConfigData.BlockType = (int)DataRecords.Indexes.iPNIODSubmoduleDefinition;
                    arConfigDataRecord.ParaDSNumber = (int)DataRecords.Indexes.iPNIODSubmoduleDefinition;
                }
                else
                {
                    arConfigDataRecord.ParaDSNumber = (int)DataRecords.Indexes.ExpectedSubmoduleData;
                }
                arConfigDataRecord.AddParaBlock(arConfigData.ToByteArray);
                paramDataBlock.AddParamDSBlock(arConfigDataRecord);
            }

            //iPNIOD_IOCONFIGDATA
            bool generateIOConfigData =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoGenerateIOConfigDataIDevice,
                    new AttributeAccessCode(),
                    false);
            if (generateIOConfigData)
            {
                byte[] ioDevConfigData = GetIOConfigDataPlus(interfaceSubmodule);
                if (ioDevConfigData != null)
                {
                    ParameterDatasetStruct ioDevConfigDataRecord = new ParameterDatasetStruct();
                    ioDevConfigDataRecord.ParaDSNumber = (int)DataRecords.Indexes.iPNIODIOConfigData;
                    ioDevConfigDataRecord.AddParaBlock(ioDevConfigData);
                    paramDataBlock.AddParamDSBlock(ioDevConfigDataRecord);
                }
            }
            // not iDevice
            if (!isLocaliDevice)
            {
                //AR_COMMUNICATION_DATA
                byte[] arCommuniData = GetArCommunicationDataPlus(configStrategy, ioControllerInterface);
                if (arCommuniData != null)
                {
                    ParameterDatasetStruct arCommuniDataRecord = new ParameterDatasetStruct();
                    arCommuniDataRecord.ParaDSNumber = (int)DataRecords.Indexes.ArCommunicationData;
                    arCommuniDataRecord.AddParaBlock(arCommuniData);
                    paramDataBlock.AddParamDSBlock(arCommuniDataRecord);
                }

                //IOCR_DATA
                byte[] ioCrData = GetIocrDataPlus(configStrategy);
                if (ioCrData != null)
                {
                    ParameterDatasetStruct ioCrDataRecord = new ParameterDatasetStruct();
                    ioCrDataRecord.ParaDSNumber = (int)DataRecords.Indexes.IocrData;
                    ioCrDataRecord.AddParaBlock(ioCrData);
                    paramDataBlock.AddParamDSBlock(ioCrDataRecord);
                }

                //LOCAL_SCF_ADAPTION
                byte[] scfData = GetLocalSCFAdaptionDataPlus(
                    interfaceSubmodule,
                    NavigationUtilities.GetIODevice(
                        interfaceSubmodule,
                        NavigationUtilities.GetIoSystem(ioControllerInterface)));
                if (scfData != null)
                {
                    ParameterDatasetStruct scfDataRecord = new ParameterDatasetStruct();
                    scfDataRecord.ParaDSNumber = (int)DataRecords.Indexes.LocalScfAdaption;
                    scfDataRecord.AddParaBlock(scfData);
                    paramDataBlock.AddParamDSBlock(scfDataRecord);
                }

                //AR_RECORD_DATA
                byte[] arRecordData = GetArRecordTablePlus(configStrategy);
                if (arRecordData != null)
                {
                    ParameterDatasetStruct arRecordDataRecord = new ParameterDatasetStruct();
                    arRecordDataRecord.ParaDSNumber = (int)DataRecords.Indexes.ArRecordData;
                    arRecordDataRecord.AddParaBlock(arRecordData);
                    paramDataBlock.AddParamDSBlock(arRecordDataRecord);
                }

                if (configStrategy.IsIRInfoBlockRequired())
                {
                    byte[] irInfoBlock = ConfigUtility.GetIrInfoBlockPlus(interfaceSubmodule, ioControllerInterface);
                    {
                        ParameterDatasetStruct irInfoDataRecord = new ParameterDatasetStruct();
                        irInfoDataRecord.ParaDSNumber = (int)DataRecords.Indexes.IrInfoBlock;
                        irInfoDataRecord.AddParaBlock(irInfoBlock);
                        paramDataBlock.AddParamDSBlock(irInfoDataRecord);
                    }
                }
                //ALARMCR_DATA
                bool createAlarmCrData =
                    ioControllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.PnIoConfigAlarmCrDataSupported,
                        new AttributeAccessCode(),
                        false);
                if (createAlarmCrData)
                {
                    byte[] alarmCrRecordData = GetAlarmCrData(configStrategy);
                    if (alarmCrRecordData != null)
                    {
                        ParameterDatasetStruct alarmCrRecordDataRecord = new ParameterDatasetStruct();
                        alarmCrRecordDataRecord.ParaDSNumber = DataRecords.Indexes.AlarmCrData;
                        alarmCrRecordDataRecord.AddParaBlock(alarmCrRecordData);
                        paramDataBlock.AddParamDSBlock(alarmCrRecordDataRecord);
                    }
                }
                //SubmoduleProperties                
                if (configStrategy.IsSubmodulePropertiesRequired())
                {
                    SubmodulePropertiesStruct submoduleProperties = GetSubmodulePropertiesPlus(configStrategy, modules);
                    if (submoduleProperties != null)
                    {
                        ParameterDatasetStruct submodulePropertiesRecord = new ParameterDatasetStruct();
                        submodulePropertiesRecord.ParaDSNumber = DataRecords.Indexes.SubmoduleProperties;
                        submodulePropertiesRecord.AddParaBlock(submoduleProperties.ToByteArray);
                        paramDataBlock.AddParamDSBlock(submodulePropertiesRecord);
                    }
                }
            }

            return paramDataBlock;
        }

        /// <summary>
        /// Checks if IOxS is required for the given device interface submodule.
        /// </summary>
        /// <param name="deviceInterfaceSubmodule">The device interface to be checked.</param>
        /// <exception cref="ArgumentNullException">if deviceInterfaceSubmodule is null.</exception>
        /// <returns>
        /// If PNIOXSRequired attribute of the device is false, checks the PNIOXSRequired attribute of the controller;
        /// if that is false too, returns false. Returns true in all other cases.
        /// </returns>
        internal static bool GetIOXSRequired(Interface deviceInterfaceSubmodule)
        {
            if (deviceInterfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(deviceInterfaceSubmodule));
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            if (deviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnIoIOXSRequired,
                ac,
                true))
            {
                return true;
            }

            Interface centralDeviceInterfaceSubmodule =
                deviceInterfaceSubmodule.PNIOD.AssignedController.ParentObject as Interface;

            if (centralDeviceInterfaceSubmodule == null)
            {
                return true;
            }

            return
                centralDeviceInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoIOXSRequired,
                    ac.GetNew(),
                    true);
        }

        /// <summary>
        /// Gets the maximum cyclic IO bandwidth (RT + IRT bandwidth) in nanoseconds.
        /// </summary>
        /// <param name="sendClockFactor">Used sendclock factor.</param>
        /// <param name="syncDomainBl">The sync domain object.</param>
        /// <returns>Maximum cyclic IO bandwidth in nanoseconds.</returns>
        internal static long GetMaxIoCyclicBandwidth(long sendClockFactor, SyncDomainBusinessLogic syncDomainBl)
        {
            // Boundary values in nanoseconds
            const int SendClockLowClassLimit = 250000;
            const int SendClockMiddleClassLimit = 500000;
            const int DefaultMaxCyclicBandwidth = 500000;
            const int MaximumBandwithBase = 100000;

            int maximumBandwith = 0;

            // Get the actual Send Clock.
            long sendClock = (long)(PNConstants.PNTimeBase * 1000000 * sendClockFactor);

            if (sendClock <= 0)
            {
                return DefaultMaxCyclicBandwidth;
            }

            if (syncDomainBl != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                byte previousLevel =
                    syncDomainBl.PCLObject.AttributeAccess.GetAnyAttribute<byte>(
                        InternalAttributeNames.PnIrtBandwidthLevel,
                        ac,
                        0);

                PNIRTBandwidthLevel currentLevel;

                //Handle uninitilized bandwithlevel case
                if (previousLevel == 0)
                {
                    PNIRTBandwidthLevel maxAvailableBanwidthLevel = syncDomainBl.GetMaxAvailableBandwidthLevel();
                    currentLevel = maxAvailableBanwidthLevel < PNIRTBandwidthLevel.Fair
                                       ? maxAvailableBanwidthLevel
                                       : PNIRTBandwidthLevel.Fair;
                }
                else
                {
                    currentLevel = (PNIRTBandwidthLevel)previousLevel;
                }

                switch (currentLevel)
                {
                    case PNIRTBandwidthLevel.MaximumNRT:
                        if (sendClock < SendClockLowClassLimit)
                        {
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.2 * sendClock, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(0.5 * Math.Min(0.4 * sendClock, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else if (sendClock < SendClockMiddleClassLimit)
                        {
                            maximumBandwith =
                                Convert.ToInt32(0.6 * (sendClock - SendClockLowClassLimit) + MaximumBandwithBase, CultureInfo.InvariantCulture);
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.5 * maximumBandwith, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(Math.Min(0.5 * maximumBandwith, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            maximumBandwith = Convert.ToInt32(Math.Min(0.5 * sendClock, SendClockMiddleClassLimit), CultureInfo.InvariantCulture);
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.5 * maximumBandwith, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(Math.Min(0.5 * maximumBandwith, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        break;
                    case PNIRTBandwidthLevel.MoreNRT:
                        if (sendClock < SendClockLowClassLimit)
                        {
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.3 * sendClock, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(0.75 * Math.Min(0.4 * sendClock, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else if (sendClock < SendClockMiddleClassLimit)
                        {
                            maximumBandwith =
                                Convert.ToInt32(0.6 * (sendClock - SendClockLowClassLimit) + MaximumBandwithBase, CultureInfo.InvariantCulture);
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.75 * maximumBandwith, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(Math.Min(0.75 * maximumBandwith, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            maximumBandwith = Convert.ToInt32(Math.Min(0.5 * sendClock, SendClockMiddleClassLimit), CultureInfo.InvariantCulture);
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.75 * maximumBandwith, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(Math.Min(0.75 * maximumBandwith, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        break;
                    case PNIRTBandwidthLevel.Fair:
                        if (sendClock < SendClockLowClassLimit)
                        {
                            maximumBandwith = syncDomainBl.IsFragmentationPossible
                                                  ? Convert.ToInt32(0.4 * sendClock, CultureInfo.InvariantCulture)
                                                  : Convert.ToInt32(
                                                      Math.Max(Math.Min(0.4 * sendClock, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else if (sendClock < SendClockMiddleClassLimit)
                        {
                            maximumBandwith =
                                Convert.ToInt32(0.6 * (sendClock - SendClockLowClassLimit) + MaximumBandwithBase, CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            maximumBandwith = Convert.ToInt32(Math.Min(0.5 * sendClock, SendClockMiddleClassLimit), CultureInfo.InvariantCulture);
                        }
                        break;
                    case PNIRTBandwidthLevel.MoreIO:
                        if (sendClock < SendClockLowClassLimit)
                        {
                            maximumBandwith = syncDomainBl.IsFragmentationPossible ?
                                Convert.ToInt32(0.5 * sendClock, CultureInfo.InvariantCulture) :
                                Convert.ToInt32(Math.Max(Math.Min(0.5 * sendClock, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            maximumBandwith = Convert.ToInt32(Math.Min(0.9 * sendClock, sendClock - 125000), CultureInfo.InvariantCulture);
                        }
                        break;
                    case PNIRTBandwidthLevel.MaximumIO:
                        if (syncDomainBl.IsFragmentationPossible)
                        {
                            maximumBandwith = Convert.ToInt32(0.9 * sendClock, CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            if (sendClock < SendClockLowClassLimit)
                            {
                                maximumBandwith = Convert.ToInt32(Math.Max(
                                    Math.Min(0.5 * sendClock, sendClock - 125000), 0), CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                maximumBandwith = Convert.ToInt32(Math.Min(0.9 * sendClock, sendClock - 125000), CultureInfo.InvariantCulture);
                            }

                        }
                        break;
                }
            }
            else if (sendClock < SendClockLowClassLimit)
            {
                Debug.Fail("SyncDomainBL is not created");
                return 0;
            }

            return maximumBandwith;
        }

        /// <summary>
        /// The method builds a string for the Interface Submodule
        /// that also contains the container name.
        /// </summary>
        internal static string GetNameWithContainer(Interface interfaceSubmodule)
        {
            return
                new StringBuilder(
                    AttributeUtilities.GetName(interfaceSubmodule.GetDevice()) + "\\"
                    + AttributeUtilities.GetName(interfaceSubmodule)).ToString();
        }

        /// <summary>
        /// The method builds a string for the Interface Submodule
        /// that also contains the container name.
        /// </summary>
        internal static string GetNameWithContainer(PclObject pclObject)
        {
            StringBuilder sbNameWithContainer = new StringBuilder();
            List<PclObject> pclObjects = new List<PclObject> { pclObject };

            PclObject parentObject = pclObject.ParentObject;
            while (parentObject != null)
            {
                pclObjects.Add(parentObject);
                parentObject = parentObject.ParentObject;
            }
            pclObjects.Reverse();
            for (int iPclObjectCursor = 0; iPclObjectCursor < pclObjects.Count; iPclObjectCursor++)
            {
                sbNameWithContainer.Append(AttributeUtilities.GetName(pclObjects[iPclObjectCursor]));
                if (iPclObjectCursor + 1 != pclObjects.Count)
                {
                    sbNameWithContainer.Append("\\");
                }
            }
            return sbNameWithContainer.ToString();
        }

        #region Norm Block 0x8090 - PDInterfaceFSUDataAdjust

        internal static ParameterDatasetStruct GetPdfsuDataAdjust(Interface interfaceSubmodule)
        {
            ParameterDatasetStruct paramDs = new ParameterDatasetStruct();
            paramDs.ParaDSNumber = 0x8090;

            //generate FSUDataAdjustheader
            PdFsuDataAdjustStruct fsuDataAdjust = new PdFsuDataAdjustStruct();

            //get FSHelloblock 
            IMethodData md = new MethodData();
            md.Name = GetFSHelloBlock.Name;

            interfaceSubmodule.BaseActions.CallMethod(md);
            if (!(bool)md.ReturnValue)
            {
                throw new PNFunctionsException("GenericMethod: " + GetFSHelloBlock.Name + "failed.");
            }

            byte[] fsHelloBlock = md.Arguments[GetFSHelloBlock.FSHelloBlock] as byte[];

            //append FSHelloBlock
            fsuDataAdjust.AddSubblock(fsHelloBlock);

            //add Dataset in Dataset struct
            paramDs.AddParaBlock(fsuDataAdjust.ToByteArray);
            return paramDs;
        }

        #endregion

        #region PDSyncData
        private static byte[] GetPdSyncDataPlus(Interface interfaceSubmodule)
        {
            if (ConfigUtility.IsRTSync(interfaceSubmodule, PNInterfaceType.IODevice) == false)
                return new byte[0];

            if (interfaceSubmodule.SyncDomain != null)
            {
                // Generate the related Data Block.
                return GetPdSyncData(interfaceSubmodule.SyncDomain, PNInterfaceType.IODevice, interfaceSubmodule);
            }
            return new byte[0];
        }

        internal static byte[] GetPdSyncData(
            SyncDomain syncDomain,
            PNInterfaceType interfaceType,
            Interface interfaceSubmodule)
        {
            if (syncDomain == null)
            {
                // The Device Submodule has not been assigned to a Sync-Domain yet.
                return new byte[0];
            }
            
            if (syncDomain.SyncDomainBusinessLogic == null)
            {
                Debug.Fail("SyncDomainBusinessLogic cannot be loaded.");
                return new byte[0];
            }

            const int TakeoverTimeoutFactorSyncMaster = 3; // For sync-masters always 3.
            // For sync-slaves always 2 
            const int TakeoverTimeoutFactorSyncSlave = 2;

            // Create the PDSyncDataStruct to assign the related parameters.
            PDSyncDataStruct syncDataStruct = new PDSyncDataStruct();

            // Get the related parameters from the Sync-Domain 
            // and assign the corresponding property.
            syncDataStruct.PTCPSubdomainID = syncDomain.SyncDomainBusinessLogic.GetSyncDomainUuid();
            syncDataStruct.ReservedIntervalBegin = syncDomain.SyncDomainBusinessLogic.ReservedIntervalBegin;
            syncDataStruct.ReservedIntervalEnd = syncDomain.SyncDomainBusinessLogic.ReservedIntervalEnd;
            syncDataStruct.PLLWindow = syncDomain.SyncDomainBusinessLogic.PllWindow;
            syncDataStruct.SyncSendFactor = syncDomain.SyncDomainBusinessLogic.SyncSendFactor;
            syncDataStruct.SendClockFactor = syncDomain.SyncDomainBusinessLogic.SendClockFactor;
            syncDataStruct.PTCPTimeoutFactor = syncDomain.SyncDomainBusinessLogic.PtcpTimeoutFactor;

            // Get the Sync-Role from the Interface Submodule.
            // Get the appropriate IOConnector of DeviceInterfaceSubmodule
            PclObject ioConnector;

            if (interfaceType == PNInterfaceType.IOController)
            {
                ioConnector = interfaceSubmodule.PNIOC;
            }
            else
            {
                ioConnector = NavigationUtilities.GetIODevice(interfaceSubmodule, null);
            }

            if (ioConnector == null)
            {
                Debug.Fail(
                    "IO-System connector object (IOController or IODevice) could not be get from interface submodule: "
                    + interfaceSubmodule);
                return new byte[0];
            }
            PNIRTSyncRole syncRole = PNAttributeUtility.GetAdjustedSyncRole(ioConnector);

            switch (syncRole)
            {
                case PNIRTSyncRole.PrimarySyncMaster:

                    syncDataStruct.PTCPMasterStartupTime = syncDomain.SyncDomainBusinessLogic.SecondarySyncMaster != null
                                                               ? s_MasterStartupTimeRedundant
                                                               : s_MasterStartupTimeNone;
                    syncDataStruct.SyncID = s_SyncId;
                    syncDataStruct.SyncRole = s_SyncPropertiesRoleMaster;
                    syncDataStruct.PTCPMasterPriority1 = s_MasterPriorityPrimary;
                    syncDataStruct.PTCPTakeoverTimeoutFactor = TakeoverTimeoutFactorSyncMaster;
                    break;

                case PNIRTSyncRole.SyncMaster:
                    // If the SyncDomain has a secondary syncmaster then the startuptime is 60 sec.
                    syncDataStruct.PTCPMasterStartupTime = syncDomain.SyncDomainBusinessLogic.SecondarySyncMaster != null
                                                               ? s_MasterStartupTimeRedundant
                                                               : s_MasterStartupTimeNone;
                    syncDataStruct.SyncID = s_SyncId;
                    syncDataStruct.SyncRole = s_SyncPropertiesRoleMaster;
                    syncDataStruct.PTCPMasterPriority1 = s_MasterPriorityPrimary;
                    syncDataStruct.PTCPTakeoverTimeoutFactor = TakeoverTimeoutFactorSyncMaster;
                    break;

                case PNIRTSyncRole.SyncSlave:
                    syncDataStruct.PTCPMasterStartupTime = s_MasterStartupTimeNone;
                    syncDataStruct.SyncID = s_SyncId;
                    syncDataStruct.SyncRole = s_SyncPropertiesRoleSlave;
                    syncDataStruct.PTCPMasterPriority1 = s_MasterPriorityNone;
                    syncDataStruct.PTCPTakeoverTimeoutFactor = TakeoverTimeoutFactorSyncSlave;
                    break;

                case PNIRTSyncRole.SecondarySyncMaster:
                    syncDataStruct.PTCPMasterStartupTime = s_MasterStartupTimeRedundant;
                    syncDataStruct.SyncID = s_SyncId;
                    syncDataStruct.SyncRole = s_SyncPropertiesRoleMaster;
                    syncDataStruct.PTCPMasterPriority1 = s_MasterPrioritySecondary;
                    syncDataStruct.PTCPTakeoverTimeoutFactor = TakeoverTimeoutFactorSyncMaster;
                    break;
            }

            syncDataStruct.PTCPMasterPriority2 = s_MasterPriority2;

            string domainName;
            PNNameOfStationConverter.EncodePNNameOfStation(
                AttributeUtilities.GetName(syncDomain),
                null,
                null,
                out domainName);
            syncDataStruct.SyncDomainNameLength = domainName.Length;
            syncDataStruct.SyncDomainName = domainName;
            return syncDataStruct.ToByteArray;
        }
        #endregion
        #region PortDataAdjust

        private static byte[] GetPortDataAdjustPlus(
            DataModel.PCLObjects.Port port,
            PNInterfaceType interfaceType)
        {
            //Check if the PDPortDataAdjust parameter block is to be generated
            if (!ConfigUtility.IsPDPortDataAdjustToGenerate(port))
            {
                return null;
            }

            return CompileUtility.GetTlvSubBlockForPortDataAdjust(port, interfaceType);
        }

        #endregion

        #region PortDataCheck

        private static byte[] GetPortDataCheckPlus(
            DataModel.PCLObjects.Port port,
            Interface interfaceSubmodule,
            PNInterfaceType interfaceType)
        {
            //Check if PDEV
            if (IsPdevInterface(interfaceSubmodule) == false)
            {
                return null;
            }

            //Check if the PDPortDataCheck parameter block is to be generated
            ConfigUtility.PDPortData pdPortData = new ConfigUtility.PDPortData();
            if (!ConfigUtility.IsPDPortDataCheckToGenerate(port, interfaceType, pdPortData))
            {
                return null;
            }
            return CompileUtility.GetTlvSubBlockForPortDataCheck(port, interfaceType, pdPortData);
        }

        #endregion

        private static List<PNIOARStartupMode> GetSupportedIoArStartupModes(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            List<PNIOARStartupMode> irtArStartupModes = new List<PNIOARStartupMode>(2);
            AttributeAccessCode ac = new AttributeAccessCode();

            Enumerated startupModesEnumerated =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoArStartupMode,
                    ac,
                    null);
            if (!ac.IsOkay)
            {
                irtArStartupModes.Add(PNIOARStartupMode.Legacy);
                return irtArStartupModes;
            }

            foreach (PNIOARStartupMode startupMode in startupModesEnumerated.List)
            {
                irtArStartupModes.Add(startupMode);
            }
            return irtArStartupModes;
        }

        /// <summary>
        /// Gets the supported irt ar startup modes (PNIrtArStartupMode) of the interface.
        /// </summary>
        /// <param name="interfaceSubmodule">The interface submodule whose supported irt ar startup modes will be retrieved.</param>
        /// <exception cref="ArgumentNullException">if interfaceSubmodule is null.</exception>
        /// <returns>List of supported irt ar startup modes of the interface.</returns>
        internal static List<PNIrtArStartupMode> GetSupportedIrtArStartupModes(PclObject interfaceSubmodule)
        {
            if (interfaceSubmodule == null)
            {
                throw new ArgumentNullException(nameof(interfaceSubmodule));
            }
            List<PNIrtArStartupMode> irtArStartupModes = new List<PNIrtArStartupMode>(2);
            AttributeAccessCode ac = new AttributeAccessCode();

            Enumerated startupModesEnumerated =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtArStartupMode,
                    ac,
                    null);
            if (!ac.IsOkay)
            {
                irtArStartupModes.Add(PNIrtArStartupMode.Legacy);
                return irtArStartupModes;
            }

            foreach (PNIrtArStartupMode startupMode in startupModesEnumerated.List)
            {
                irtArStartupModes.Add(startupMode);
            }
            return irtArStartupModes;
        }

        /// <summary>
        /// Get the supported sendclock factors for RT class 1 and Rt class 2 of PN interface submodule
        /// </summary>
        /// <param name="interfaceSubmodule">Interface submodule</param>
        /// <param name="suppSCF12">Output list with the supported sendclock factors</param>
        /// <returns>Attribute access result</returns>
        private static bool GetSupportedSendClockFactors12(Interface interfaceSubmodule, out List<long> suppSCF12)
        {
            suppSCF12 = new List<long>();
            AttributeAccessCode ac = new AttributeAccessCode();

            Enumerated pnIoSuppSCF12 =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIoSuppSCF12,
                    ac,
                    null);

            if (!ac.IsOkay)
            {
                return false;
            }

            foreach (object suppSCF in pnIoSuppSCF12.List)
            {
                suppSCF12.Add(long.Parse(suppSCF.ToString(), CultureInfo.InvariantCulture));
            }

            return true;
        }

        /// <summary>
        /// Gets the supported sendclock factors for RT class 3 of a given PN interface submodule.
        /// </summary>
        /// <param name="interfaceSubmodule">
        /// The interface submodule whose supported RT class 3 sendclock factors will be
        /// retrieved.
        /// </param>
        /// <param name="pnIoSuppSCF3Enumerated">Output attribute with the supported sendclock factors.</param>
        /// <returns>Attribute access result.</returns>
        internal static bool GetSupportedSendClockFactors3(PclObject interfaceSubmodule, out Enumerated pnIoSuppSCF3Enumerated)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            if (AttributeUtilities.IsIDevice(interfaceSubmodule))
            {
                pnIoSuppSCF3Enumerated = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF3IDevice, ac, null);

                if (!ac.IsOkay)
                {
                    ac.Reset();
                    pnIoSuppSCF3Enumerated = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF3, ac, null);
                }
            }
            else
            {
                pnIoSuppSCF3Enumerated = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoSuppSCF3, ac, null);
            }

            return ac.IsOkay;
        }

        /// <summary>
        /// Get the supported sendclock factors for RT class 3 of PN interface submodule
        /// </summary>
        /// <param name="interfaceSubmodule">Interface submodule</param>
        /// <param name="suppSCF3">Output list with the supported sendclock factors</param>
        /// <returns>Attribute access result</returns>
        private static bool GetSupportedSendClockFactors3(PclObject interfaceSubmodule, out List<long> suppSCF3)
        {
            suppSCF3 = new List<long>();

            Enumerated pnIoSuppSCF3;
            if (!GetSupportedSendClockFactors3(interfaceSubmodule, out pnIoSuppSCF3))
            {
                return false;
            }

            foreach (object suppSCF in pnIoSuppSCF3.List)
            {
                suppSCF3.Add(long.Parse(suppSCF.ToString(), CultureInfo.InvariantCulture));
            }

            return true;
        }

        internal static float GetUpdateTimeOfIODevice(PNIOD ioDevice)
        {
            IPNFrameData frameData = CheckConsistencyUtility.GetPNPlannerFrameDataOfIODevice(ioDevice);

            if (frameData != null)
            {
                return Convert.ToSingle(
                    frameData.SendClockFactor * frameData.DeviceLocalReductionRatio * PNConstants.PNTimeBase,
                    CultureInfo.InvariantCulture);
            }

            return 0;
        }

        internal static List<long> GetWatchdogFactors(Interface interfaceSubmodule, PNIOD ioDevice= null, float updateTime = -1f)
        {
            const int MinWatchDogFactorDefault = 3;
            const int MaxWatchDogFactorDefault = 3;

            // Create a list, fill it and assign to the property controller
            List<long> watchDogFactors = new List<long>();
            long minWatchDogFactor = MinWatchDogFactorDefault;
            long maxWatchDogFactor = MaxWatchDogFactorDefault;

            Interface controllerInterfaceSubmodule = interfaceSubmodule.PNIOD.AssignedController.ParentObject as Interface;

            if (controllerInterfaceSubmodule != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();
                Enumerated minMaxWatchdogFactors = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(InternalAttributeNames.PnIoMinMaxWatchdogFactor, ac, null);

                if (ac.IsOkay
                    && (minMaxWatchdogFactors.List.Count == 2))
                {
                    minWatchDogFactor = long.Parse(minMaxWatchdogFactors.List[0].ToString(), CultureInfo.InvariantCulture);
                    maxWatchDogFactor = long.Parse(minMaxWatchdogFactors.List[1].ToString(), CultureInfo.InvariantCulture);
                }
            }

            int minWatchDogFactorDevice =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIoMinWatchdogFactorDevice,
                    new AttributeAccessCode(),
                    -1);
            if (minWatchDogFactorDevice >= 0)
            {
                minWatchDogFactor = minWatchDogFactorDevice;
            }

            int maxWatchDogFactorDevice =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIoMaxWatchdogFactorDevice,
                    new AttributeAccessCode(),
                    -1);
            if (maxWatchDogFactorDevice >= 0)
            {
                maxWatchDogFactor = maxWatchDogFactorDevice;
            }
            
            if (updateTime <= 0) 
            { 
                    updateTime = (GetUpdateTimeOfIODevice(ioDevice ?? NavigationUtilities.GetIODevice(interfaceSubmodule, null)));
            }

            if (updateTime > 0)
            {
                    maxWatchDogFactor = Math.Min(maxWatchDogFactor, (int)(PNConstants.MaxAllowedWatchdogTime / updateTime));
            }
            
            for (long watchdogFactor = minWatchDogFactor; watchdogFactor <= maxWatchDogFactor; watchdogFactor++)
            {
                watchDogFactors.Add(watchdogFactor);
            }

            return watchDogFactors;
        }

        /// <summary>
        /// If the interface has decentral IO-devices, takes part in CCDX, or is itself an I-Device,
        /// it has IO-Communication
        /// </summary>
        /// <param name="controllerInterfaceSubmodule">central interface submodule</param>
        /// <returns>true if the central IF has IO communication</returns>
        internal static bool HasCentralInterfaceIoCommunication(Interface controllerInterfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            return (NavigationUtilities.GetIODevice(controllerInterfaceSubmodule, null) != null)
                   || (NavigationUtilities.GetDevicesOfController(controllerInterfaceSubmodule).Count > 0)
                   || controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                       InternalAttributeNames.IsSender,
                       ac.GetNew(),
                       false)
                   || controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                       InternalAttributeNames.IsReceiver,
                       ac.GetNew(),
                       false);
        }

        /// <summary>
        /// Returns true, if the IO Device has a module or submodule with az API value other than 0
        /// </summary>
        internal static bool HasDeviceModuleOrSubmoduleWithMoreApIs(Interface deviceInterfaceSubmodule)
        {
            if (deviceInterfaceSubmodule == null)
            {
                return false;
            }

            foreach (PclObject module in PNNavigationUtility.GetModulesSorted(deviceInterfaceSubmodule))
            {
                if (module.AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnAPI,
                        new AttributeAccessCode(),
                        0) > 0)
                {
                    return true;
                }

                IList<PclObject> submodules = PNNavigationUtility.GetSubmodulesSorted(module);
                if (submodules.Count != 1)
                {
                    continue;
                }

                if (submodules[0].AttributeAccess.GetAnyAttribute<uint>(
                        InternalAttributeNames.PnAPI,
                        new AttributeAccessCode(),
                        0) > 0)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// check if the given device synchronized or not
        /// </summary>
        /// <returns></returns>
        internal static bool IsDeviceSynchronized(Interface interfaceSubmodule)
        {
            PclObject connector = interfaceSubmodule.GetIOConnector();

            if (connector == null)
            {
                return false;
            }
            // Check whether the interface submodule is synchronized.
            if (PNAttributeUtility.GetAdjustedSyncRole(connector) == PNIRTSyncRole.NotSynchronized)
            {
                return false;
            }
            return true;
        }


        /// <summary>
        /// Checks if the given virtualModule CoreObject is synchronized.
        /// </summary>
        internal static bool IsInterfaceSynchronized(Interface interfaceCoreObject)
        {
            List<PclObject> ioConnectors = NavigationUtilities.GetIOConnectors(interfaceCoreObject);
            if ((ioConnectors == null) || (ioConnectors.Count == 0))
            {
                return true;
            }
            bool isSynchronized = false;
            foreach (PclObject ioConnector in ioConnectors)
            {
                if (PNAttributeUtility.GetAdjustedSyncRole(ioConnector) != PNIRTSyncRole.NotSynchronized)
                {
                    isSynchronized = true;
                    break;
                }
            }
            if (!isSynchronized)
            {
                return true;
            }
            return false;
        }
        internal static bool IsFastStartupActivated(Interface interfaceSubmodule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            bool pnIoDeviceFsuPriority =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDeviceFSUPriority,
                    ac,
                    false);
            bool pnIoDcpHelloSupported =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoDCPHelloSupported,
                    ac,
                    false);

            bool isActivated = pnIoDeviceFsuPriority && pnIoDcpHelloSupported;
            if (ac.IsOkay)
            {
                // IDevice doesn't support FSU, when Prioritized Startup is enabled, ASU mode must be used, no Hello block needed
                return AttributeUtilities.GetOperatingMode(interfaceSubmodule)
                       == PNIOOperatingModes.IOControllerAndIODevice
                           ? false
                           : isActivated;
            }

            return false;
        }

        /// <summary>
        /// Checks if the given device interface submodule currently uses irtTop.
        /// </summary>
        internal static bool IsIrtTopDevice(Interface deviceInterfaceSubmodule)
        {
            if (deviceInterfaceSubmodule == null)
            {
                return false;
            }

            PNIOD ioDevice = deviceInterfaceSubmodule.PNIOD;

            if (ioDevice == null)
            {
                return false;
            }

            long frameClass = ioDevice.AttributeAccess.GetAnyAttribute<long>(
                InternalAttributeNames.PnIoFrameClass,
                new AttributeAccessCode(),
                0);
            // Check whether the actual Frame Class is Class3Frame.
            return (PNIOFrameClass)frameClass == PNIOFrameClass.Class3Frame;
        }

        internal static bool IsPdevInterface(Interface interfaceSubmodule)
        {
            bool retValue = false;
            if (interfaceSubmodule == null)
            {
                Debug.Assert(false, "virtualModule is null!");
                return retValue;
            }

            AttributeAccessCode aCode = new AttributeAccessCode();
            uint pdev =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnPdevSupportedModel,
                    aCode,
                    0);

            if (aCode.IsOkay
                && (pdev > 0))
            {
                return true;
            }

            aCode.Reset();
            retValue =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoSubmoduleModelSupp,
                    aCode,
                    false);
            if (!aCode.IsOkay)
            {
                return false;
            }

            return retValue;
        }

        /// <summary>
        /// This method returns true, if cfgBase is an active Profinet interface submodule,
        /// which agregates an IOController connector object.
        /// </summary>
        internal static bool IsProfinetControllerInterfaceSubmodule(Interface interfaceSubmodule)
        {
            if ((interfaceSubmodule != null)
                && (interfaceSubmodule.PNIOC != null))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// This method returns true, if cfgBase is an active Profinet interface submodule,
        /// which agregates an IODevice connector object.
        /// </summary>
        internal static bool IsProfinetDeviceInterfaceSubmodule(Interface interfaceSubmodule)
        {
            if ((interfaceSubmodule != null)
                && (interfaceSubmodule.PNIOD != null))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Check is the send clock uneven
        /// </summary>
        /// <param name="interfaceSubmodule">Interface required to retrive the SuppSCFs</param>
        /// <param name="sendClock">Send clock</param>
        /// <returns>True if the send clock uneven</returns>
        internal static bool IsUnevenSendclock(Interface interfaceSubmodule, long sendClock)
        {
            List<long> suppSCF12, suppSCF3;

            if (!GetSupportedSendClockFactors12(interfaceSubmodule, out suppSCF12)
                || !GetSupportedSendClockFactors3(interfaceSubmodule, out suppSCF3))
            {
                return false;
            }
            if (!suppSCF12.Contains(sendClock)
                && suppSCF3.Contains(sendClock))
            {
                //if the send clock factor is not a power of two
                if ((sendClock & (sendClock - 1)) > 0)
                {
                    return true;
                }

                return false;
            }
            return false;
        }

        /// <summary>
        /// This function calculates the input and output address lengths of a deviceitem
        /// and adds them to the given input and output address lengths.
        /// </summary>
        /// <param name="deviceItem">The deviceItem whose address length will be updated.</param>
        /// <param name="paramObjectRefreshPNPlannerInput">The parameter object used in frame generation.</param>
        /// <param name="ioCsLength">Output length value.</param>
        /// <param name="ioPsLength">Input length value.</param>
        /// <exception cref="ArgumentNullException">if deviceItem is null.</exception>
        private static void AddFrameLengths(
            PclObject deviceItem,
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput,
            int ioCsLength,
            int ioPsLength)
        {
            if (deviceItem == null)
            {
                throw new ArgumentNullException(nameof(deviceItem));
            }

            DataAddress inAddress;
            DataAddress outAddress;
            bool isDiagAddress;
            bool hasAdr = AddressUtility.GetAddressObjectsOfModule(
                deviceItem,
                out inAddress,
                out outAddress,
                out isDiagAddress);

            if (!paramObjectRefreshPNPlannerInput.HasUsingData)
            {
                if (!isDiagAddress
                    && ((inAddress != null) || (outAddress != null)))
                {
                    paramObjectRefreshPNPlannerInput.HasUsingData = true;
                }
            }

            // Device is probably connected with an Plus CPU -> no Diagnostic addresses
            if (!hasAdr)
            {
                isDiagAddress = IsDiagAddress(deviceItem);
            }

            if (isDiagAddress)
            {
                if (!paramObjectRefreshPNPlannerInput.DiscardIOXS)
                {
                    paramObjectRefreshPNPlannerInput.InputGrossFrameLength += ioPsLength;
                    paramObjectRefreshPNPlannerInput.OutputGrossFrameLength += ioCsLength;
                }
                return;
            }

            if (!hasAdr)
            {
                return;
            }

            long oldInputGrossLength = paramObjectRefreshPNPlannerInput.InputGrossFrameLength;
            long oldOutputGrossLength = paramObjectRefreshPNPlannerInput.OutputGrossFrameLength;

            if (inAddress == null)
            {
                inAddress = new DataAddress(IoTypes.Input, 0, 0);
            }
            if (outAddress == null)
            {
                outAddress = new DataAddress(IoTypes.Output, 0, 0);
            }

            paramObjectRefreshPNPlannerInput.InputGrossFrameLength += inAddress.Length;
            paramObjectRefreshPNPlannerInput.InputNetFrameLength += inAddress.Length;

            paramObjectRefreshPNPlannerInput.OutputGrossFrameLength += outAddress.Length;
            paramObjectRefreshPNPlannerInput.OutputNetFrameLength += outAddress.Length;

            if (paramObjectRefreshPNPlannerInput.DiscardIOXS
                && (oldInputGrossLength == paramObjectRefreshPNPlannerInput.InputGrossFrameLength)
                && (oldOutputGrossLength == paramObjectRefreshPNPlannerInput.OutputGrossFrameLength))
            {
                ioCsLength = ioPsLength = 0;
            }

            if ((inAddress.Length != 0)
                && (outAddress.Length != 0))
            {
                paramObjectRefreshPNPlannerInput.InputGrossFrameLength += ioCsLength;
                paramObjectRefreshPNPlannerInput.OutputGrossFrameLength += ioPsLength;
            }

            paramObjectRefreshPNPlannerInput.InputGrossFrameLength += ioPsLength;
            paramObjectRefreshPNPlannerInput.OutputGrossFrameLength += ioCsLength;
        }

        private static bool IsDiagAddress(PclObject deviceItem)
        {
            bool isDiagAdr = false;

            IoTypes ioType = (IoTypes)deviceItem.AttributeAccess.GetAnyAttribute<int>(
                InternalAttributeNames.IoType,
                new AttributeAccessCode(),
                0);

            if (ioType == IoTypes.Diagnosis)
            {
                isDiagAdr = true;
            }

            return isDiagAdr;
        }

        internal static long GetTdc(Interface controllerInterfaceSubmodule)
        {
            long tdc = 0;
            if (controllerInterfaceSubmodule != null)
            {
                tdc = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int64>(
                    InternalAttributeNames.PnIoSendClockFactor,
                    new AttributeAccessCode(),
                    0);
                tdc = (long)(tdc * PNConstants.PNTimeBase * 1000000);
            }
            return tdc;
        }

        #region Constants

        private const int s_DefaultPNMaxDeviceInputDataLength = 1440;

        private const int s_DefaultPNMaxDeviceOutputDataLength = 1440;

        #endregion

        #region PDSyncData

        // Constants for PDSyncData.
        private const int s_MasterStartupTimeNone = 0;

        private const int s_MasterStartupTimeRedundant = 60;

        private const int s_SyncId = 0x00;

        private const int s_SyncPropertiesRoleSlave = 0x01;

        private const int s_SyncPropertiesRoleMaster = 0x02;

        private const int s_MasterPriorityNone = 0x00;

        private const int s_MasterPriorityPrimary = 0x01;

        private const int s_MasterPrioritySecondary = 0x02;

        private const int s_MasterPriority2 = 0xFF;

        #endregion

        #region iPNIOD_IOCONFIGDATA

        /// <summary>
        /// This function returns the IOConfigData block 
        /// </summary>
        private static byte[] GetIOConfigDataPlus(Interface interfaceSubmodule)
        {
            int maxInputLength;
            int maxOutputLength;
            int maxDataLength;
            AttributeAccessCode attributeAccessCode = new AttributeAccessCode();
            IoConfigDataStruct ioConfigData = new IoConfigDataStruct();

            int numberOfAr =
                (int)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIoNumberOfAR,
                    attributeAccessCode.GetNew(),
                    1);

            GetDataLengthsforIDevice(interfaceSubmodule, out maxInputLength, out maxOutputLength, out maxDataLength);

            ioConfigData.MaxInputLength = maxInputLength;
            ioConfigData.MaxOutputLength = maxOutputLength;
            ioConfigData.MaxDataLength = maxDataLength;
            ioConfigData.NumberOfAR = numberOfAr;
            ioConfigData.NumberOfAdditionalMulticastProviderCR = 0;
            ioConfigData.NumberOfMulticastConsumerCR = 0;
            ioConfigData.NumberOfAdditionalInputCR =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoNumberOfAdditionalInputCR,
                    attributeAccessCode.GetNew(),
                    0);
            ioConfigData.NumberOfAdditionalOutputCR =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoNumberOfAdditionalOutputCR,
                    attributeAccessCode.GetNew(),
                    0);

            return ioConfigData.ToByteArray;
        }

        private static void GetDataLengthsforIDevice(
            Interface interfaceSubmodule,
            out int maxInputLength,
            out int maxOutputLength,
            out int maxDataLength)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            ParamObjectRefreshPNPlannerInput paramObjectRefreshPNPlannerInput =
                ParamObjectRefreshPNPlannerInput.CreateParamObjectRefreshPNPlannerInput();

            // input and output have to be switched, 
            // because input transfer area becomes output device-module, and so on

            maxInputLength = (int)paramObjectRefreshPNPlannerInput.OutputGrossFrameLength;
            maxOutputLength = (int)paramObjectRefreshPNPlannerInput.InputGrossFrameLength;

            int numberOfAr =
                (int)
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<uint>(InternalAttributeNames.PnIoNumberOfAR, ac, 1);

            ac.Reset();
            int pnMaxDeviceInputGrossDataLength =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIoMaxDeviceInputDataLength,
                    ac,
                    s_DefaultPNMaxDeviceInputDataLength);

            ac.Reset();
            int pnMaxDeviceOutputGrossDataLength =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnIoMaxDeviceOutputDataLength,
                    ac,
                    s_DefaultPNMaxDeviceOutputDataLength);

            ac.Reset();
            int maxDeviceDataLength =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnIoMaxDeviceDataLength,
                    new AttributeAccessCode(),
                    0);

            maxInputLength = Math.Min(maxInputLength, pnMaxDeviceInputGrossDataLength);
            maxOutputLength = Math.Min(maxOutputLength, pnMaxDeviceOutputGrossDataLength);
            maxDataLength = Math.Min(numberOfAr * (maxInputLength + maxOutputLength), maxDeviceDataLength);
        }

        #endregion

        #region EXPECTED_SUBMODULE_DATA

        private static ExpectedSubmoduleDataStruct GetArConfigurationDataPlus(
            IPNDeviceConfigStrategy configStrategy,
            IList<PclObject> modules)
        {
            ExpectedSubmoduleDataStruct arConfig = new ExpectedSubmoduleDataStruct();
            int blockVersion = configStrategy.GetBlockVersion(1);

            switch (blockVersion)
            {
                case 0x0100:
                    foreach (PclObject module in modules)
                    {
                        ARConfigSlotStruct slot = GetConfigurationSlotData(module, 0, configStrategy);

                        arConfig.AddSubBlock(slot.ToByteArray);
                        arConfig.BlockLength += slot.SlotBlockLength;
                    }
                    break;
                case 0x0102:
                    uint[] apIs = configStrategy.GetAPIs();
                    int apiCount = 0;
                    foreach (uint API in apIs)
                    {
                        ArConfigApiStruct api = GetConfigurationApiData(API, configStrategy, modules);

                        api.API = API;
                        arConfig.AddSubBlock(api.ToByteArray);
                        apiCount++;
                    }
                    arConfig.APICount = apiCount;
                    break;

                default:
                    Debug.Assert(false);
                    break;
            }

            int alignment = BufferManager.Alignment(arConfig.ToByteArray.Length, 16);

            if (alignment != 0)
            {
                arConfig.AddSubBlock(new byte[alignment]);
            }
            return arConfig.APICount > 0 ? arConfig : null;
        }

        private static ArConfigApiStruct GetConfigurationApiData(
            long api,
            IPNDeviceConfigStrategy configStrategy,
            IList<PclObject> modules)
        {
            ArConfigApiStruct API = new ArConfigApiStruct();
            //get slots of module

            foreach (PclObject module in modules)
            {
                ARConfigSlotStruct slot = GetConfigurationSlotData(module, api, configStrategy);

                if (slot.SubmoduleCount > 0)
                {
                    API.AddSubblock(slot.ToByteArray);
                }
            }
            return API;
        }

        private static ARConfigSlotStruct GetConfigurationSlotData(
            PclObject module,
            long api,
            IPNDeviceConfigStrategy configStrategy)
        {
            ARConfigSlotStruct slot = new ARConfigSlotStruct();

            slot.SetSubmoduleDataBlockVersion(0x0100);

            slot.SlotNumber = GetSlotNumber(module);

            slot.ModuleIdentNr = module.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnModuleIdentNumber,
                new AttributeAccessCode(),
                0);

            IList<PclObject> submodules = configStrategy.GetSubmodules(module);
            foreach (PclObject submodule in submodules)
            {
                int submoduleNr;

                byte[] subslotData = configStrategy.GetConfigurationSubslotData(module, submodule, api);
                submoduleNr = configStrategy.GetSubslotNumber(module, submodule, api);
                slot.AddSubmodule(subslotData);

                if (submoduleNr > slot.MaxSubmoduleNr)
                {
                    slot.MaxSubmoduleNr = submoduleNr;
                }
            }

            return slot;
        }

        #endregion

        #region Other Records

        internal static ParameterDataBlockStruct GetParamDataBlock(
            PclObject module,
            Interface iodeviceInterface,
            Interface controllerInterface)
        {
            byte[] parameter = GetSlotBlockNative(module);

            if (parameter == null)
            {
                return null;
            }

            if (parameter.Length < 4)
            {
                return null;
            }

            int datasets = parameter[3]; 
            int run = 6; 

            // Check if the parameter data must be generated with PrmID.
            AttributeAccessCode ac = new AttributeAccessCode();
            bool hasPrmId = controllerInterface == null
                                ? false
                                : controllerInterface.AttributeAccess.GetAnyAttribute<bool>(
                                    InternalAttributeNames.PnIoGenerateConfig2003BlocksV1_1,
                                    ac,
                                    false);

            ushort datasetOffset;
            bool isGsdDataSetType = false;

            if (parameter.Length > run)
            {
                // The structure of the parameter array:
                // ( Config header) ( n * record header ) ( dataset_1 dataset_2 ... dataset_n       )
                // (  6 bytes  ) (   n * 6/8 bytes   ) ( DSLen_1 + DSLen_2 + ... + DSLen_n bytes )
                // ( 0  ... 5  ) ( 6     ...         ) ( dataOffset_1                            )
                // the isGsdDataSetType decides between 6/8
                datasetOffset = ConfigUtility.GetSwappedUInt16(parameter, run);
                datasetOffset &= 0x7FFF;
                isGsdDataSetType = (datasetOffset - run) / datasets % 8 == 0;
            }

            ParameterDataBlockStruct paramDataBlock = new ParameterDataBlockStruct(hasPrmId);
            for (int i = 0; i < datasets; i++)
            {
                ParameterDatasetStruct paramDs = new ParameterDatasetStruct();

                ushort nDsNum;
                ushort nDsLen;
                int nAddOffset = 6;

                if (isGsdDataSetType)
                {
                    // GSD devices belong here and some MDD devices as well
                    // DSNum and DSLength are both in 2-2 Bytes delivered, the length of the dataset descriptors are 8 byte                    
                    nDsNum = ConfigUtility.GetSwappedUInt16(parameter, run + 2);
                    nDsLen = ConfigUtility.GetSwappedUInt16(parameter, run + 4);
                    nAddOffset = 8;
                }
                else
                {
                    // DSNum and DSLength are both in 1-1 Byte delivered
                    nDsNum = parameter[run + 2];
                    nDsLen = parameter[run + 3];
                }

                // ET200M Rule

                int alignLen = nDsLen + BufferManager.Alignment(nDsLen, 4);
                datasetOffset = ConfigUtility.GetSwappedUInt16(parameter, run);
                datasetOffset &= 0x7FFF;

                byte[] dataset = new byte[alignLen];

                //Diagnostic Parameterization in the headmodule
                IMethodData md = new MethodData();
                md.Name = PNConstants.s_InitializeDiagnosticAlarmBlock;
                md.Arguments[PNConstants.s_ParameterBlock] = dataset;
                module.BaseActions.CallMethod(md);

                if (datasetOffset + nDsLen <= parameter.Length)
                {
                    Array.Copy(parameter, datasetOffset, dataset, 0, nDsLen);
                }

                paramDs.AddParaBlock(dataset);
                paramDs.ParaDSNumber = nDsNum;
                paramDs.ParaDSLength = nDsLen;

                paramDataBlock.AddParamDSBlock(paramDs);

                run += nAddOffset;
            }

            int mod4 = paramDataBlock.ToByteArray.Length % 4;
            if (mod4 != 0)
            {
                paramDataBlock.BlockLength += mod4;
            }

            // For block version 101 a PrmID (64 bit CRC) must be generated
            if (hasPrmId)
            {
                Crc64 crc = new Crc64();
                paramDataBlock.PrmID = crc.ComputeHash(paramDataBlock.ToByteArrayNetto);
            }

            return paramDataBlock;
        }

        private static byte[] GetSlotBlockNative(PclObject module)
        {
            IMethodData methodData = new MethodData();
            methodData.Name = GetSlotBlock.Name;
            methodData.Arguments[GetSlotBlock.ConfigVersion] = 3;
            methodData.Arguments[GetSlotBlock.GetDatasetsAlways] = 1;

            if (module.BaseActions == null)
            {
                return null;
            }

            if (module.BaseActions.CallMethod(methodData) == false)
            {
                //The method GetSlotBlock is not implemented.
                return null;
            }
            if (methodData.ReturnValue == null)
            {
                // Runtime error in method GetSlotBlock
                Debug.Assert(false, "Error in GetSlotBlock of Module " + module);
                return null;
            }

            byte[] parameter = (byte[])methodData.ReturnValue;

            return parameter;
        }

        #endregion

        #region get DataRecordsConf

        /// <summary>
        /// Generate DataRecordsConf block for ports and interfaces.
        /// (The equivalent dara record generation for Config is in the specific Config Strategy such as PNDeviceConfigStrategy.)
        /// </summary>
        /// <param name="module">port or interface</param>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="ioControllerInterface">superordinate controller interface</param>
        /// <param name="configStrategy"></param>
        /// <returns></returns>
        internal static byte[] GetDataRecordsConf(
            PclObject module,
            Interface interfaceSubmodule,
            Interface ioControllerInterface,
            IPNDeviceConfigStrategy configStrategy)
        {
            ParameterDataBlockStruct recordsData = null;

            if ((module != null)
                && (ioControllerInterface != null))
            {
                recordsData = GetParamDataBlock(module, interfaceSubmodule, ioControllerInterface)
                              ?? new ParameterDataBlockStruct();

                ParameterDatasetStruct isochronModeDataRecord = GetIsochronBlock(module, interfaceSubmodule);

                if (isochronModeDataRecord != null)
                {
                    recordsData.AddParamDSBlock(isochronModeDataRecord);
                }

                //########################################################################################
                //Interface
                //########################################################################################
                if (module is Interface)
                {
                    if (configStrategy != null)
                    {
                        Interface currentModule = module as Interface;
                        //pdIRData
                        byte[] pdIRData = GetPdirDataPlus(currentModule, configStrategy, configStrategy.GetModules());
                        if ((pdIRData != null)
                            && (pdIRData.Length > 0))
                        {
                            ParameterDatasetStruct pdIRDataRecord = new ParameterDatasetStruct();
                            pdIRDataRecord.ParaDSNumber = (int)DataRecords.Indexes.PDIRData;
                            pdIRDataRecord.AddParaBlock(pdIRData);
                            recordsData.AddParamDSBlock(pdIRDataRecord);
                        }

                        bool returnValue;

                        //mrpDataCheck
                        byte[] mrpDataCheck = ConfigUtility.GetPDInterfaceMrpDataCheck(currentModule, out returnValue);
                        if ((mrpDataCheck != null)
                            && (mrpDataCheck.Length > 0))
                        {
                            ParameterDatasetStruct mrpDataCheckRecord = new ParameterDatasetStruct();
                            mrpDataCheckRecord.ParaDSNumber = (int)DataRecords.Indexes.PDInterfaceMrpDataCheck;
                            mrpDataCheckRecord.AddParaBlock(mrpDataCheck);
                            recordsData.AddParamDSBlock(mrpDataCheckRecord);
                        }
                        //MrpDataAdjust
                        byte[] mrpDataAdjust = ConfigUtility.GetPDInterfaceMrpDataAdjust(currentModule, out returnValue);
                        if ((mrpDataCheck != null)
                            && (mrpDataCheck.Length > 0))
                        {
                            ParameterDatasetStruct mrpDataAdjustRecord = new ParameterDatasetStruct();
                            mrpDataAdjustRecord.ParaDSNumber = (int)DataRecords.Indexes.PDInterfaceMrpDataAdjust;
                            mrpDataAdjustRecord.AddParaBlock(mrpDataAdjust);
                            recordsData.AddParamDSBlock(mrpDataAdjustRecord);
                        }

                        ////ExpectedPDSyncData
                        byte[] pdSyncData = GetPdSyncDataPlus(currentModule);
                        if ((pdSyncData != null) && (pdSyncData.Length > 0))
                        {
                            ParameterDatasetStruct pdSyncDataRecord = new ParameterDatasetStruct();
                            pdSyncDataRecord.ParaDSNumber = (int)DataRecords.Indexes.ExpectedPDSyncData;
                            pdSyncDataRecord.AddParaBlock(pdSyncData);
                            recordsData.AddParamDSBlock(pdSyncDataRecord);
                        }

                        byte[] pdIRSubframeData = ConfigUtility.GetPDIRSubframeData(currentModule);
                        if (pdIRSubframeData != null)
                        {
                            ParameterDatasetStruct pdIRSubframeDataRecord = new ParameterDatasetStruct();
                            pdIRSubframeDataRecord.ParaDSNumber = (int)DataRecords.Indexes.PdirSubframeData;
                            pdIRSubframeDataRecord.AddParaBlock(pdIRSubframeData);
                            recordsData.AddParamDSBlock(pdIRSubframeDataRecord);
                        }

                        bool iAdjustReturnValue;
                        byte[] pdInterfaceAdjust = ConfigUtility.GetPDInterfaceAdjustBlock(
                            currentModule,
                            out iAdjustReturnValue);
                        if (pdInterfaceAdjust != null)
                        {
                            ParameterDatasetStruct pdInterfaceAdjustRecord = new ParameterDatasetStruct();
                            pdInterfaceAdjustRecord.ParaDSNumber = (int)DataRecords.Indexes.PDInterfaceAdjust;
                            pdInterfaceAdjustRecord.AddParaBlock(pdInterfaceAdjust);
                            recordsData.AddParamDSBlock(pdInterfaceAdjustRecord);
                        }

                        //CIMSNMPAdjust
                        byte[] cimSNMPAdjust = GetCIMSNMPAdjustBlock(currentModule);
                        if (cimSNMPAdjust != null && cimSNMPAdjust.Length > 0)
                        {
                            ParameterDatasetStruct cimSnmpAdjust_record = new ParameterDatasetStruct();
                            cimSnmpAdjust_record.ParaDSNumber = (int)DataRecords.Indexes.CIMSNMPAdjust;
                            cimSnmpAdjust_record.AddParaBlock(cimSNMPAdjust);
                            recordsData.AddParamDSBlock(cimSnmpAdjust_record);
                        }

                        // Block 0x8090 - PDInterfaceFSUDataAdjust
                        if (IsFastStartupActivated(currentModule))
                        {
                            ParameterDatasetStruct pdFsuDataAdjustRecord = GetPdfsuDataAdjust(interfaceSubmodule);
                            if (pdFsuDataAdjustRecord != null)
                            {
                                recordsData.AddParamDSBlock(pdFsuDataAdjustRecord);
                            }
                        }

                        #region address tailor data block

                        TailorConfigLogicDecentral tailorConfigLogicDecentral =
                            new TailorConfigLogicDecentral(ioControllerInterface, interfaceSubmodule);
                        ParameterDatasetStruct pdMasterTailorDataRecord = tailorConfigLogicDecentral.GetPDMasterTailorDataDatasetStruct();
                        if (pdMasterTailorDataRecord != null)
                        {
                            recordsData.AddParamDSBlock(pdMasterTailorDataRecord);
                        }

                        #endregion
                    }
                }
                else if (module is DataModel.PCLObjects.Port)
                {
                    GetDatarecordsConfPort(recordsData, module as DataModel.PCLObjects.Port, interfaceSubmodule);
                }

                if (module.ParameterRecordDataItems.Count > 0)
                {
                    foreach (KeyValuePair<uint, byte[]> parameterRecordDataItem in module.ParameterRecordDataItems)
                    {
                        ParameterDatasetStruct parameterDatasetStruct = new ParameterDatasetStruct();

                        parameterDatasetStruct.ParaDSNumber = (int)parameterRecordDataItem.Key;
                        parameterDatasetStruct.AddParaBlock(parameterRecordDataItem.Value);

                        recordsData.AddParamDSBlock(parameterDatasetStruct);
                    }
                }
            }

            return recordsData != null ? recordsData.ToByteArray : new byte[0];
        }

        private static ParameterDatasetStruct GetIsochronBlock(PclObject pclObject, Interface interfaceSubModule)
        {
            AttributeAccessCode ac = new AttributeAccessCode();
            bool generateBlock = false;

            if (pclObject == null
                || interfaceSubModule == null)
            {
                return null;
            }

            bool interfaceIsIsochron =
                interfaceSubModule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIsochron,
                    ac.GetNew(),
                    false);
            PNIsoDataModel isoDataModel = PNIsoDataModel.Model1;

            if (interfaceIsIsochron)
            {
                isoDataModel = (PNIsoDataModel)interfaceSubModule.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIsoDataModel,
                    ac.GetNew(),
                    (uint)PNIsoDataModel.Model1);
            }

            Module module = pclObject as Module;
            if (module != null)
            {
                int submoduleCount = (module.GetSubmodules()).Count;

                if ((submoduleCount == 0)
                    && interfaceIsIsochron
                    && module.AttributeAccess.GetAnyAttribute<Boolean>(
                        InternalAttributeNames.ClockSyncMode,
                        ac.GetNew(),
                        false)
                    && !module.AttributeAccess.GetAnyAttribute<Boolean>(
                        InternalAttributeNames.PnIsoRecordNotSupported,
                        ac.GetNew(),
                        false))
                {
                    generateBlock = true;
                }
            }
            else if (pclObject is Submodule && interfaceIsIsochron)
            {
                if (pclObject.AttributeAccess.GetAnyAttribute<bool>(
                        InternalAttributeNames.ClockSyncMode,
                        ac.GetNew(),
                        false)
                    && !pclObject.AttributeAccess.GetAnyAttribute<Boolean>(
                        InternalAttributeNames.PnIsoRecordNotSupported,
                        ac.GetNew(),
                        false))
                {
                    generateBlock = true;
                }
            }

            if (!generateBlock)
            {
                return null;
            }

            bool returnVal;
            byte[] isochronModeData =
                IsochronConfigUtility.GetIsochronousModeData(
                    interfaceSubModule,
                    pclObject,
                    out returnVal,
                    isoDataModel);

            ParameterDatasetStruct isochronModeDataRecord = null;
            if (returnVal && isochronModeData != null)
            {
                isochronModeDataRecord = new ParameterDatasetStruct();
                isochronModeDataRecord.ParaDSNumber = (int)DataRecords.Indexes.IsoMParameter;
                isochronModeDataRecord.AddParaBlock(isochronModeData);
            }
            return isochronModeDataRecord;
        }

        private static void GetDatarecordsConfPort(
            ParameterDataBlockStruct recordsData,
            DataModel.PCLObjects.Port module,
            Interface interfaceSubmodule)
        {
            //PDPortMrpDataAdjust
            byte[] pdPortMRPDataAdjust = GetPDPortMrpDataAdjustPlus(interfaceSubmodule, module);
            if (pdPortMRPDataAdjust != null)
            {
                ParameterDatasetStruct pdPortMRPDataAdjustRecord = new ParameterDatasetStruct();
                pdPortMRPDataAdjustRecord.ParaDSNumber = (int)DataRecords.Indexes.PDPortMrpDataAdjust;
                pdPortMRPDataAdjustRecord.AddParaBlock(pdPortMRPDataAdjust);
                recordsData.AddParamDSBlock(pdPortMRPDataAdjustRecord);
            }

            //PDPortDataAdjust
            byte[] pdPortDataAdjust = GetPortDataAdjustPlus(module, PNInterfaceType.IODevice);
            if (pdPortDataAdjust != null)
            {
                ParameterDatasetStruct pdPortDataAdjustRecord = new ParameterDatasetStruct();
                pdPortDataAdjustRecord.ParaDSNumber = (int)DataRecords.Indexes.PDPortDataAdjust;
                pdPortDataAdjustRecord.AddParaBlock(pdPortDataAdjust);
                recordsData.AddParamDSBlock(pdPortDataAdjustRecord);
            }

            //PDPortDatacheck
            byte[] pdPortDataCheck = GetPortDataCheckPlus(module, interfaceSubmodule, PNInterfaceType.IODevice);
            if (pdPortDataCheck != null)
            {
                ParameterDatasetStruct pdPortDataCheckRecord = new ParameterDatasetStruct();
                pdPortDataCheckRecord.ParaDSNumber = (int)DataRecords.Indexes.PDPortDataCheck;
                pdPortDataCheckRecord.AddParaBlock(pdPortDataCheck);
                recordsData.AddParamDSBlock(pdPortDataCheckRecord);
            }

            if (ConfigUtility.IsFoPortActivated(module))
            {
                //PDPortFODataAdjust
                byte[] pdPortFoDataAdjust = CompileUtility.GetPdPortFoDataAdjust(module);
                if (pdPortFoDataAdjust != null)
                {
                    ParameterDatasetStruct pdPortFoDataAdjustRecord = new ParameterDatasetStruct();
                    pdPortFoDataAdjustRecord.ParaDSNumber = (int)DataRecords.Indexes.PDPortFODataAdjust;
                    pdPortFoDataAdjustRecord.AddParaBlock(pdPortFoDataAdjust);
                    recordsData.AddParamDSBlock(pdPortFoDataAdjustRecord);
                }
                if (ConfigUtility.IsPDPortFoDataCheckToGenerate(module))
                {
                    //PDPortDatacheck
                    byte[] pdPortFoDataCheck = CompileUtility.GetPdPortFoDataCheck();
                    if (pdPortFoDataCheck != null)
                    {
                        ParameterDatasetStruct pdPortFoDataCheckRecord = new ParameterDatasetStruct();
                        pdPortFoDataCheckRecord.ParaDSNumber = (int)DataRecords.Indexes.PDPortFODataCheck;
                        pdPortFoDataCheckRecord.AddParaBlock(pdPortFoDataCheck);
                        recordsData.AddParamDSBlock(pdPortFoDataCheckRecord);
                    }
                }
            }
        }

        private static byte[] GetCIMSNMPAdjustBlock(Interface interfaceSubModule)
        {
            IMethodData methodData = new MethodData();
            //Check if PLC is compatible!
            methodData.Name = GetCimSnmpAdjustRecord.Name;
            methodData.Arguments.Add(GetCimSnmpAdjustRecord.ConnectedInterface, interfaceSubModule);

            if (interfaceSubModule.BaseActions == null)
                return null;

            if (interfaceSubModule.BaseActions.CallMethod(methodData) == false)
            {
                //The method GetSlotBlock is not implemented.
                return null;
            }
            if (methodData.ReturnValue == null)
            {
                // Runtime error in method GetSlotBlock
                return null;
            }

            byte[] parameter = (byte[])methodData.ReturnValue;

            return parameter;
        }

        #endregion

        #region PDIRData

        internal static byte[] GetPdirDataPlus(
            Interface interfaceSubmodule,
            IPNDeviceConfigStrategy configStrategy,
            IList<PclObject> modules)
        {
            if ((configStrategy == null)
                || ((PNRTClass)configStrategy.GetIrtMode() != PNRTClass.IrtTop))
            {
                //no need to generate Config2008
                return new byte[0];
            }

            int slotNumber = GetSlotNumber(interfaceSubmodule, modules);

            AttributeAccessCode ac = new AttributeAccessCode();
            int subSlotNumber =
                interfaceSubmodule.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.PnSubslotNumber,
                    ac.GetNew(),
                    0);

            // Generate PDIRData
            byte[] pdIRData = configStrategy.GetPDIRData(slotNumber, subSlotNumber);

            return pdIRData;
        }

        private static int GetSlotNumber(Interface deviceItfSubmodule, IList<PclObject> modules)
        {
            PclObject interfaceContainer = NavigationUtilities.GetContainer(deviceItfSubmodule);
            int slotNumber = 0;
            //precondition, there must be at least 1 Module item and the first one must be always the physical Headmodule or virtual Headmodule(Remote-VirtualPNDevice)
            if (modules.Count > 0)
            {
                if (interfaceContainer != null)
                {
                    if (!SharedDeviceUtility.IsHeadModuleShared(deviceItfSubmodule))
                    {
                        slotNumber = GetSlotNumber(interfaceContainer);
                    }
                    else
                    {
                        PclObject firstNotSharedModule = SharedDeviceUtility.GetFirstNotSharedModule(modules);
                        if (firstNotSharedModule != null)
                            // Shared device case: slotnumber come from the first not shared module, by default it is the headmodule
                        {
                            slotNumber = AttributeUtilities.GetPositionNumber(firstNotSharedModule);
                        }
                    }
                }
            }
            return slotNumber;
        }

        internal static int GetSlotNumber(PclObject module)
        {
            string deviceVersion = module.AttributeAccess.GetAnyAttribute<string>(
               InternalAttributeNames.FwVersion,
               new AttributeAccessCode(),
               null);
            bool isDeviceVersion31orUpper = CheckDeviceVersion(deviceVersion);
            if (module == null)
            {
                throw new ArgumentNullException(nameof(module), "This parameter should not be null");
            }

            AttributeAccessCode ac = new AttributeAccessCode();
            int slotNumber;
            if (!isDeviceVersion31orUpper)
            {
                slotNumber = module.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PositionNumber, ac, 0);
            }
            else
            {              
                    slotNumber = 0;             
            }
            return slotNumber;
        }

        #endregion

        internal static bool CheckDeviceVersion(string deviceVersion)
        {
            FwVersion fwVersion = HWCNBL.Utilities.AttributeUtilities.MapVersion(deviceVersion);
            return fwVersion.CompareTo(FwVersion.V3_1) >= 0;
        }

        #region PDPortMrpDataAdjust
        private static byte[] GetPDPortMrpDataAdjustPlus(Interface interfaceSubmodule, DataModel.PCLObjects.Port port)
        {

            if (!interfaceSubmodule.AttributeAccess.GetAnyAttribute<Boolean>(
                    InternalAttributeNames.PnMrpSupported, new AttributeAccessCode(), false))
            {
                return null;
            }
            if ((interfaceSubmodule.AttributeAccess.GetAnyAttribute<UInt32>(
                InternalAttributeNames.PnMrpRole, new AttributeAccessCode(), (int)PNMrpRole.NotInRing) ==
                 (int)PNMrpRole.NotInRing) && !MultipleMrpUtilities.IsMultipleInstancesActive(interfaceSubmodule))
            {
                return null;
            }

            bool returnVal;
            return ConfigUtility.GetPDPortMrpDataAdjust(port, out returnVal);
        }
        #endregion

        #region IOCR_DATA

        private static byte[] GetIocrDataPlus(IPNDeviceConfigStrategy configStrategy)
        {
            IocrDataStruct ioCrData = new IocrDataStruct();

            int blockVersion = configStrategy.GetBlockVersion(0);
            List<byte[]> ioCrIns = new List<byte[]>();
            List<byte[]> ioCrOuts = new List<byte[]>();
            int ioCrCount = 0;

            switch (blockVersion)
            {
                case 0x0100: //V1.00 ohne API
                    GetIocrDataV100(ioCrIns, ioCrOuts, configStrategy);
                    if (ioCrIns != null)
                    {
                        if (ioCrIns.Count != 0)
                        {
                            foreach (byte[] ioCrIn in ioCrIns)
                            {
                                ioCrData.AddSubBlock(ioCrIn);
                                ioCrCount++;
                            }
                        }
                    }
                    if (ioCrOuts != null)
                    {
                        if (ioCrOuts.Count != 0)
                        {
                            foreach (byte[] ioCrOut in ioCrOuts)
                            {
                                ioCrData.AddSubBlock(ioCrOut);
                                ioCrCount++;
                            }
                        }
                    }
                    ioCrData.IOCRCount = ioCrCount;
                    break;

                case 0x0101:
                    Debug.Assert(false, "Not supported!");
                    break;

                case 0x0102: //V1.02 mit API

                    GetIocrDataV102(ioCrIns, ioCrOuts, configStrategy);
                    if (ioCrIns != null)
                    {
                        if (ioCrIns.Count != 0)
                        {
                            foreach (byte[] ioCrIn in ioCrIns)
                            {
                                ioCrData.AddSubBlock(ioCrIn);
                                ioCrCount++;
                            }
                        }
                    }
                    if (ioCrOuts != null)
                    {
                        if (ioCrOuts.Count != 0)
                        {
                            foreach (byte[] ioCrOut in ioCrOuts)
                            {
                                ioCrData.AddSubBlock(ioCrOut);
                                ioCrCount++;
                            }
                        }
                    }
                    ioCrData.IOCRCount = ioCrCount;
                    break;

                default:
                    Debug.Assert(false);
                    break;
            }
            int alignment = BufferManager.Alignment(ioCrData.ToByteArray.Length, 16);

            if (alignment != 0)
            {
                ioCrData.AddSubBlock(new byte[alignment]);
            }
            return ioCrData.IOCRCount > 0 ? ioCrData.ToByteArray : null;
        }

        private static void GetIocrDataV100(
            List<byte[]> inputCrEntries,
            List<byte[]> outputCrEntries,
            IPNDeviceConfigStrategy configStrategy)
        {
            configStrategy.GetCREntry(inputCrEntries, outputCrEntries, false);
        }

        private static void GetIocrDataV102(
            List<byte[]> inputCrEntries,
            List<byte[]> outputCrEntries,
            IPNDeviceConfigStrategy configStrategy)
        {
            configStrategy.GetCREntry(inputCrEntries, outputCrEntries, true);
        }

        private static IocrEntryStruct CreateConfigAriocrEntryStruct(
            Interface deviceInterfaceSubmodule,
            IPNFrameData frameData,
            ref List<IPNPlannerOutputFrame> frameBlocks)
        {
            IocrEntryStruct configAriocrEntryStruct = new IocrEntryStruct();

            //PNPlannerFrameDirection
            if (frameData.FrameDirection == (long)PNPlannerFrameDirection.InputFrame)
            {
                configAriocrEntryStruct.IOCRType = 1;
            }
            else if (frameData.FrameDirection == (long)PNPlannerFrameDirection.OutputFrame)
            {
                configAriocrEntryStruct.IOCRType = 2;
            }

            //PNPlannerFrameClass
            configAriocrEntryStruct.RTClass = (int)frameData.FrameClass;

            //PNPlannerFrameLength
            int dataLength;
            if ((frameData.FrameClass == (long)PNIOFrameClass.Class3Frame)
                && (frameData.DataLength < PNConstants.MinFramePayloadLength))
            {
                dataLength = PNConstants.MinFramePayloadLength;
            }
            else
            {
                dataLength = (int)frameData.DataLength;
            }
            configAriocrEntryStruct.DataLength = dataLength;

            int frameID = (int)frameData.FrameID;
            configAriocrEntryStruct.FrameID = frameID;

            //PNPlannerSendClockFactor
            configAriocrEntryStruct.SendClockFactor = (int)frameData.SendClockFactor;

            //PNPlannerDeviceLocalReductionRatio
            configAriocrEntryStruct.ReductionRatio = (int)frameData.DeviceLocalReductionRatio;

            //PNPlannerDeviceLocalPhase
            configAriocrEntryStruct.Phase = (int)frameData.DeviceLocalPhase;

            //PNPlannerSequence
            configAriocrEntryStruct.Sequence = (int)frameData.Sequence;

            //PNIoWatchdogFactor
            configAriocrEntryStruct.WatchDogFactor = (int)frameData.WatchdogFactor;

            //PNPlannerDataHoldFactor
            configAriocrEntryStruct.DataHoldFactor = (int)frameData.DataHoldFactor;

            //FrameSendOffset
            if (frameData.FrameClass == (long)PNIOFrameClass.Class3Frame)
            {
                if (frameBlocks == null)
                {
                    SyncDomain syncDomain = deviceInterfaceSubmodule.SyncDomain;
                    if (null != syncDomain)
                    {
                        if (null != syncDomain.SyncDomainBusinessLogic)
                        {
                            frameBlocks = (List<IPNPlannerOutputFrame>)syncDomain.SyncDomainBusinessLogic.GetConfig2008FrameBlocks(deviceInterfaceSubmodule);
                        }
                    }
                }
                if (frameBlocks != null)
                {
                    foreach (IPNPlannerOutputFrame frameBlock in frameBlocks)
                    {
                        if (frameBlock.FrameID == frameID)
                        {
                            //In DFP frames, If frame is in inbound direction then 
                            //tranceived frame send offset should be used otherwise 
                            //received frame send offset should be used
                            IPNDfpFrameData dfpFrameData = frameData as IPNDfpFrameData;
                            if (dfpFrameData != null)
                            {
                                //The Local attribute is not used in txlocal frame definitions so that 
                                //they are false as default. 
                                //If frame is RX then frameBlock.Local = 1 Otherwise 0.

                                //In Inbound direction TX frame should be used
                                if ((frameData.FrameDirection == 0)
                                    && (frameBlock.Local == 1))
                                {
                                    continue;
                                }

                                //In Outbound direction RX should be used
                                if ((frameData.FrameDirection == 1)
                                    && (frameBlock.Local == 0))
                                {
                                    continue;
                                }
                            }
                            configAriocrEntryStruct.SetFrameSendOffset(frameBlock.FrameSendOffset);
                            break;
                        }
                    }
                }
            }
            else
            {
                configAriocrEntryStruct.SetFrameSendOffset(0xFFFFFFFF);
            }

            return configAriocrEntryStruct;
        }

        /// <summary>
        /// Calculates IOXS length from parameters
        /// </summary>
        /// <param name="interfaceSubmodule"></param>
        /// <param name="ioType"></param>
        /// <param name="defaultLength"></param>
        /// <returns></returns>
        internal static int GetIOXSLength(Interface interfaceSubmodule, IoTypes ioType, int defaultLength)
        {
            return (((int)ioType & (int)IoTypes.Input) != (int)IoTypes.Input)
                   && (((int)ioType & (int)IoTypes.Output) != (int)IoTypes.Output) && !GetIOXSRequired(interfaceSubmodule)
                       ? 0
                       : defaultLength;
        }

        #endregion

        #region LOCAL_SCF_ADAPTION

        private static byte[] GetLocalSCFAdaptionDataPlus(Interface interfaceSubmodule, PNIOD ioDeviceObject)
        {
            byte[] block;
            int scfEntrieCount = 0;
            LocalScfAdaptionDataStruct scfData = new LocalScfAdaptionDataStruct();

            // get IOCR block from PNFrameData object
            List<IPNFrameData> pnFrameDataList = NavigationUtilities.GetPNFrameDataListOfIODevice(ioDeviceObject, false);

            if ((null == pnFrameDataList)
                || (pnFrameDataList.Count < 2))
            {
                throw new PNFunctionsException(
                    "Could not found any PNFrameData objects aggregated on the device interface submodule.");
            }

            foreach (IPNFrameData frameData in pnFrameDataList)
            {
                block = null == frameData ? null : GetLocalScfAdaptionEntry(interfaceSubmodule, frameData);

                if ((block != null)
                    && (block.Length != 0))
                {
                    scfData.AddSubBlock(block);
                    scfEntrieCount++;
                }
            }
            scfData.SCFEntrieCount = scfEntrieCount;
            int alignment = BufferManager.Alignment(scfData.ToByteArray.Length, 32);

            if (alignment != 0)
            {
                scfData.AddSubBlock(new byte[alignment]);
            }
            return scfData.SCFEntrieCount > 0 ? scfData.ToByteArray : null;
        }

        /// <summary>
        /// This method fills the LocalScfAdaptionEntryStruct with the attributes of PNPlannerDataObject.
        /// </summary>
        internal static byte[] GetLocalScfAdaptionEntry(Interface interfaceSubmodule, IPNFrameData frameData)
        {
            AttributeAccessCode ac = new AttributeAccessCode();

            Interface controllerInterfaceSubmodule = NavigationUtilities.GetControllerOfDevice(interfaceSubmodule);

            if (null == controllerInterfaceSubmodule)
            {
                return null;
            }

            ac.Reset();
            long controllerSendClockFactor =
                AttributeUtilities.GetTransientPNSendClockFactor(controllerInterfaceSubmodule, 0);

            if (controllerSendClockFactor == frameData.SendClockFactor)
            {
                return new byte[0];
            }

            LocalScfAdaptionEntryStruct configArLocalSCFAdaptionEntryStruct = new LocalScfAdaptionEntryStruct();

            //IOCRReference
            if (frameData.IOCRReferenceNo != 0)
            {
                configArLocalSCFAdaptionEntryStruct.IOCRReference = frameData.IOCRReferenceNo;
            }
            else
            {
                configArLocalSCFAdaptionEntryStruct.IOCRReference = frameData.FrameDirection
                                                                 == (int)PNPlannerFrameDirection.InputFrame
                                                                     ? 1
                                                                     : 2;
            }

            //PNPlannerSendClockFactor
            configArLocalSCFAdaptionEntryStruct.LocalSendClockFactor = (int)controllerSendClockFactor;

            //PNPlannerControllerLocalReductionRatio
            configArLocalSCFAdaptionEntryStruct.LocalReductionRatio = (int)frameData.ControllerLocalReductionRatio;

            //PNPlannerControllerLocalPhase
            configArLocalSCFAdaptionEntryStruct.LocalPhase = (int)frameData.ControllerLocalPhase;

            //PNPlannerSequence
            configArLocalSCFAdaptionEntryStruct.LocalSequence = (int)frameData.Sequence;

            //LocalFrameSendOffset
            configArLocalSCFAdaptionEntryStruct.LocalFrameSendOffset = 0xFFFFFFFF;

            // Fill local watchdog and datahold factor if the controller interface supports uneven sendclock adaptation
            if (
                controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<bool>(
                    InternalAttributeNames.PnIoScfAdaptionNonPow2Supported,
                    ac.GetNew(),
                    false))
            {
                int wdfCalc = CalcLocalWatchDogFactor(frameData, controllerSendClockFactor);
                //Watchdogfactor
                configArLocalSCFAdaptionEntryStruct.LocalWatchdogFactor = wdfCalc;
                //Dataholdfactor
                configArLocalSCFAdaptionEntryStruct.LocalDataHoldFactor = wdfCalc;
            }
            return configArLocalSCFAdaptionEntryStruct.ToByteArray;
        }

        #endregion

        #region SUBMODULEPROPERTIES

        /// <summary>
        /// Returns the address length of the given deviceitem and its elements
        /// Additionally validates the size of each submodule against an IO-Controller specific limit.
        /// </summary>
        internal static void GetSubmoduleCountOfDeviceItem(
            PclObject deviceItem,
            ref int submoduleCount,
            ref int proxyCount,
            bool sharedDeviceSupported,
            Dictionary<PclObject, int> sseList = null,
            int pnIoMaxDeviceSubmoduleDataLength = 0)
        {
            Debug.Assert(deviceItem != null);
            if (deviceItem == null)
            {
                return;
            }

            bool isAssigned = true;
            if (sharedDeviceSupported)
            {
                UInt32 assignment = deviceItem.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.SharedIoAssignment,
                    new AttributeAccessCode(),
                    0);

                isAssigned = (SharedIoAssignment)assignment == SharedIoAssignment.None;
            }

            bool pnParametrizationDisallowed =  deviceItem.AttributeAccess.GetAnyAttribute(
                    InternalAttributeNames.PnParameterizationDisallowed,
                    new AttributeAccessCode(),
                    false);

            if (deviceItem is Submodule) //Don't count not assigned submodules
            {
                submoduleCount++;
                CheckSubmoduleDataLength(deviceItem, pnIoMaxDeviceSubmoduleDataLength, sseList);
            }

            List<PclObject> elements = (List<PclObject>)deviceItem.GetElements();
            if (null != elements)
            {
                if ((elements.Count == 0)
                    && deviceItem is Module
                    && isAssigned
                    && !pnParametrizationDisallowed) //Don't count not assigned submodules
                {
                    submoduleCount++;
                    CheckSubmoduleDataLength(deviceItem, pnIoMaxDeviceSubmoduleDataLength, sseList);
                }

                if ((elements.Count > 1)
                    && deviceItem is Module)
                {
                    proxyCount++;
                }

                foreach (PclObject element in elements)
                {
                    GetSubmoduleCountOfDeviceItem(
                        element,
                        ref submoduleCount,
                        ref proxyCount,
                        sharedDeviceSupported,
                        sseList,
                        pnIoMaxDeviceSubmoduleDataLength);
                }
            }
        }

        /// <summary>
        /// All submodules of an IO-Device must not be greater in length than a device specific value for the IO-Controller.
        /// </summary>
        /// <param name="coreObject"></param>
        /// <param name="limit"></param>
        /// <param name="sseList"></param>
        private static void CheckSubmoduleDataLength(PclObject coreObject, int limit, Dictionary<PclObject, int> sseList)
        {
            if ((limit != 0) && (sseList != null))
            {
                int inputLength = coreObject.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.InAddressRange,
                    new AttributeAccessCode(),
                    -1);
                int outputLength = coreObject.AttributeAccess.GetAnyAttribute<int>(
                    InternalAttributeNames.OutAddressRange,
                    new AttributeAccessCode(),
                    -1);

                if ((Math.Max(inputLength, outputLength) > limit) && !sseList.ContainsKey(coreObject))
                {
                    sseList.Add(coreObject, Math.Max(inputLength, outputLength));
                }
            }
        }
        internal static void CountLocalVirtualDevices(Interface controllerInterfaceSubmodule,
                                                                AttributeAccessCode acMaxDevices,
                                                                int pnIoMaxDevices, int devicePNStationNumberCount)
        {
            int countOfLocalVirtualDevices = 0;
            if (devicePNStationNumberCount == 0)
            {
                return;
            }
            if (acMaxDevices.IsOkay && ((devicePNStationNumberCount + countOfLocalVirtualDevices) > pnIoMaxDevices))
            {
                int withHowMany = devicePNStationNumberCount + countOfLocalVirtualDevices - pnIoMaxDevices;
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, 
                    controllerInterfaceSubmodule, 
                    ConsistencyConstants.PNCONTROLLER_ERROR_MAX_DEVICES, 
                    pnIoMaxDevices, 
                    withHowMany);
            }
        }
        /// <summary>
        /// This method checks the maximum number of IRT devices which are connected to the controller interface.
        /// If the allowed number is exceeded, consistency check error is thrown.
        /// This method is modified since it is first implemented but the name remained same but 
        /// it doesn't cover the main purpose.
        /// </summary>
        /// <param name="controllerInterfaceSubmodule"></param>
        /// <param name="syncSlaveCounter"></param>
        internal static void CheckSyncSlaveNumber(Interface controllerInterfaceSubmodule,
                                                            long syncSlaveCounter)
        {
            Int32 maxSyncSlaveCount = controllerInterfaceSubmodule.AttributeAccess.GetAnyAttribute<Int32>(
                InternalAttributeNames.PnIoMaxIrtDevices, new AttributeAccessCode(), -1);

            if ((maxSyncSlaveCount >= 0) && (maxSyncSlaveCount < syncSlaveCounter))
            {
                CentralDevice centralDevice = controllerInterfaceSubmodule.ParentObject as CentralDevice;
                string devicenName = centralDevice.AttributeAccess.GetAnyAttribute<string>(InternalAttributeNames.Name,
                    new AttributeAccessCode(), string.Empty);
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error,
                    centralDevice,
                    ConsistencyConstants.Max_SyncSlaveNumber_Overstepped, 
                    syncSlaveCounter, 
                    devicenName, 
                    maxSyncSlaveCount);
            }
        }

        private static SubmodulePropertiesStruct GetSubmodulePropertiesPlus(
            IPNDeviceConfigStrategy configStrategy,
            IList<PclObject> modules)
        {
            SubmodulePropertiesStruct dataStruct = new SubmodulePropertiesStruct();

            uint[] apIs = configStrategy.GetAPIs();
            int apiCount = 0;

            foreach (uint API in apIs)
            {
                SubmodulePropertiesApiStruct api = GetSubmodulePropertiesApiData(API, configStrategy, modules);
                if (api.BlockCount > 0)
                {
                    api.API = API;
                    dataStruct.AddSubBlock(api.ToByteArray);
                    apiCount++;
                }
            }

            dataStruct.APICount = apiCount;

            return dataStruct.APICount > 0 ? dataStruct : null;
        }

        private static SubmodulePropertiesApiStruct GetSubmodulePropertiesApiData(
            long api,
            IPNDeviceConfigStrategy configStrategy,
            IList<PclObject> modules)
        {
            SubmodulePropertiesApiStruct API = new SubmodulePropertiesApiStruct();
            //get slots of module

            foreach (PclObject module in modules)
            {
                SubmodulePropertiesSlotStruct slot = GetSubmodulePropertiesSlotData(module, api, configStrategy);

                if (slot.SubmoduleCount > 0)
                {
                    API.AddSubblock(slot.ToByteArray);
                }
            }
            return API;
        }

        private static SubmodulePropertiesSlotStruct GetSubmodulePropertiesSlotData(
            PclObject module,
            long api,
            IPNDeviceConfigStrategy configStrategy)
        {
            SubmodulePropertiesSlotStruct slot = new SubmodulePropertiesSlotStruct();

            slot.SlotNumber = GetSlotNumber(module);

            IList<PclObject> submodules = configStrategy.GetSubmodules(module);
            foreach (PclObject submodule in submodules)
            {
                byte[] subslotData = configStrategy.GetSubmodulePropertiesSubslotData(module, submodule, api);
                slot.AddSubmodule(subslotData);
            }

            return slot;
        }

        #endregion

        #region IRT+ Related Utility Methods

        /// <summary>
        /// Gets the related subframe of a device interface submodule from its dfp frame.
        /// </summary>
        internal static IPNSubframeData GetSubframeOfInterface(Interface deviceInterfaceSubmodule,
                                                               IPNDfpFrameData dfpFrame)
        {
            List<PclObject> ioConnectors = NavigationUtilities.GetIOConnectors(deviceInterfaceSubmodule);
            if ((ioConnectors == null) || (ioConnectors.Count == 0))
            {
                Debug.Fail("Io connector cannot be found.");
                return null;
            }

            int coreId = ioConnectors[0].GetHashCode();
            foreach (IPNSubframeData subframe in dfpFrame.Subframes.Values)
            {
                if (coreId == subframe.CoreId)
                {
                    return subframe;
                }
            }

            return null;
        }
        #endregion

        #region Compare Functions

        /// <summary>
        /// Compares two port objects by their slot and port number.
        /// </summary>
        internal static int ComparePortsBySlotAndPortNumber(
            DataModel.PCLObjects.Port port1,
            DataModel.PCLObjects.Port port2)
        {
            Debug.Assert((port1 != null) && (port2 != null), "Call compare ports with not-null port objects.");

            if ((port1 == null)
                && (port2 == null))
            {
                return 0;
            }
            if (port1 == null)
            {
                return 1;
            }
            if (port2 == null)
            {
                return -1;
            }

            // Compare by position number of the container (it can be interface or another module)
            PclObject containerObj1 = NavigationUtilities.GetContainer(port1);
            PclObject containerObj2 = NavigationUtilities.GetContainer(port2);

            if (containerObj1 == null)
            {
                containerObj1 = port1.GetInterface();
            }

            if (containerObj2 == null)
            {
                containerObj2 = port1.GetInterface();
            }

            int positionNumber1 = AttributeUtilities.GetPositionNumber(containerObj1);
            int positionNumber2 = AttributeUtilities.GetPositionNumber(containerObj2);
            int compareResult = positionNumber1.CompareTo(positionNumber2);
            if (compareResult != 0)
            {
                return compareResult;
            }

            // Compare by port number
            AttributeAccessCode ac = new AttributeAccessCode();
            int portNo1 = port1.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnPortNumber, ac, 0);
            if (!ac.IsOkay)
            {
                return 1;
            }
            int portNo2 =
                port2.AttributeAccess.GetAnyAttribute<int>(InternalAttributeNames.PnPortNumber, ac.GetNew(), 0);
            if (!ac.IsOkay)
            {
                return -1;
            }
            return portNo1.CompareTo(portNo2);
        }

        #endregion
    }
}