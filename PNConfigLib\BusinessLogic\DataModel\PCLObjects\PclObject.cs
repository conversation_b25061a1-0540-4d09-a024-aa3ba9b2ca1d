/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PclObject.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;

using PNConfigLib.BusinessLogic.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.DataAddresses;
using PNConfigLib.DataModel.LAddresses;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL;
using PNConfigLib.HWCNBL.Basics;

#endregion

namespace PNConfigLib.DataModel.PCLObjects
{
    /// <summary>
    /// The base object for data classes of PNConfigLib.
    /// </summary>
    /// <remarks>
    /// Most of data objects are derived from this class. Contains attribute access and navigation mechanisms.
    /// </remarks>
    public class PclObject : IPclObject
    {
        /// <summary>
        /// The attribute access mechanism used for accessing an objects attribute list.
        /// </summary>
        public AttributeAccess AttributeAccess { get; set; }

        internal Actions BaseActions = new Actions();

        internal List<DataAddress> DataAddresses = new List<DataAddress>();

        /// <summary>
        /// The parent object of this PclObject; that is, the object that this PclObject is connected to.
        /// </summary>
        private PclObject m_ParentObject;

        /// <summary>
        /// The default constructor of PclObject, initiates the attribute access
        /// mechanism and adds several general attributes.
        /// </summary>
        public PclObject()
        {
            AttributeAccess = new AttributeAccess(this);

            AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Name, string.Empty);
            AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Author, "PNConfigLib");
            AttributeAccess.AddAnyAttribute<string>(InternalAttributeNames.Comment, string.Empty);
            AttributeAccess.AddAnyAttribute<int>(InternalAttributeNames.PositionNumber, 0);
        }

        
        public string Id { get; set; }

        /// <summary>
        /// The address of this PclObject.
        /// </summary>
        internal LAddress LAddress { get; set; }

        public Dictionary<uint, byte[]> ParameterRecordDataItems { get; } = new Dictionary<uint, byte[]>();

        public void SetParameterRecordDataItems(IDictionary<uint, byte[]> dictionary)
        {
            if (dictionary == null)
            {
                throw new ArgumentNullException(nameof(dictionary));
            }

            ParameterRecordDataItems.Clear();
            foreach (uint dictionaryKey in dictionary.Keys)
            {
                ParameterRecordDataItems.Add(dictionaryKey, dictionary[dictionaryKey]);
            }
        }

        /// <summary>
        /// The property that allows access to the parent object.
        /// </summary>
        /// <exception cref="PNFunctionsException">if parent object is set more than once.</exception>
        public PclObject ParentObject
        {
            get { return m_ParentObject; }
            set
            {
                if (m_ParentObject != null)
                {
                    throw new PNFunctionsException("Can not change the parent of an object.");
                }

                m_ParentObject = value;
            }
        }

        /// <summary>
        /// The catalog object of this PclObject.
        /// </summary>
        /// <remarks>
        /// PclCatalogObject contains attributes and properties that are not set in runtime;
        /// they are the "base" objects from the PNConfigLib project point of view. For example,
        /// a PclCatalogObject would define the characteristics of "ERTEC 200P" device, whereas
        /// a PclObject would define an "ERTEC 200P" device instance added to the project.
        /// </remarks>
        internal PclCatalogObject PCLCatalogObject { get; set; }

        /// <summary>
        /// Gets the device that this PclObject is connected to.
        /// </summary>
        /// <remarks>
        /// This recursive method is used for navigating through a device's hierarchy; such as,
        /// getting the CentralDevice or DecentralDevice object that a Port or a Submodule is connected to.
        /// </remarks>
        /// <returns>
        /// The DecentralDevice or CentralDevice object that this PCL object is connected to;
        /// or null if it is not connected.
        /// </returns>
        public PclObject GetDevice()
        {
            if (this is DecentralDevice)
            {
                return this;
            }

            if (ParentObject == null)
            {
                return null;
            }

            if (ParentObject is CentralDevice
                || ParentObject is DecentralDevice)
            {
                return ParentObject;
            }

            return ParentObject.GetDevice();
        }

        /// <summary>
        /// Gets the objects connected to this PclObject with Element relation.
        /// </summary>
        /// <remarks>This method is overridden in related derived classes.</remarks>
        /// <returns>An emptly list.</returns>
        public virtual IList<PclObject> GetElements()
        {
            return new List<PclObject>();
        }

        /// <summary>
        /// Gets the list of Interface objects connected to this PclObject.
        /// </summary>
        /// <returns>The list of interfaces connected to this PclObject.</returns>
        internal virtual Interface GetInterface()
        {
            return null;
        }

        /// <summary>
        /// Registers a PclObject with the address manager of its device.
        /// </summary>
        /// <remarks>
        /// This method is used for registering a PclObject from within a device's hierarchy;
        /// such as when adding a Port to an Interface or adding a Submodule to a Module.
        /// </remarks>
        /// <param name="pclObject">The PclObject to be registered.</param>
        /// <exception cref="InvalidOperationException">if pclObject is not connected to a device.</exception>
        public void RegisterWithAddressManager(PclObject pclObject)
        {
            if (pclObject is IOSystem)
            {
                IOSystem ioSystem = pclObject as IOSystem;

                CentralDevice centralDevice = ioSystem.PNIOC.GetDevice() as CentralDevice;

                if (centralDevice == null)
                {
                    throw new PNFunctionsException("Could not get device of IO controller assigned to IO system.");
                }

                centralDevice.GetCentralAddressManager().RegisterPCLObject(pclObject, LAddressType.PCLObject);
            }
            else
            {
                PclObject parentDevice = GetDevice();

                if (parentDevice == null)
                {
                    throw new InvalidOperationException("The PclObject is not connected to a device.");
                }

                if (parentDevice is CentralDevice)
                {
                    (parentDevice as CentralDevice).GetCentralAddressManager()
                        .RegisterPCLObject(pclObject, LAddressType.PCLObject);
                }
                else if (parentDevice is DecentralDevice)
                {
                    (parentDevice as DecentralDevice).GetAddressManager()
                        .RegisterPCLObject(pclObject, LAddressType.PCLObject);
                }
            }
        }

        /// <summary>
        /// Checks if snmp is supported.
        /// </summary>
        public bool IsSnmpSupported()
        {
            AttributeAccessCode acc = new AttributeAccessCode();

            AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.SnmpEnabled,
                acc,
                false);

            if (!acc.IsOkay)
            {
                return false;
            }

            return true;
        }

        internal class Actions
        {
            private Dictionary<string, MethodCallback> m_Methods = new Dictionary<string, MethodCallback>();

            public delegate void MethodConsistencyCallback();

            public delegate void MethodCallback(IMethodData methodData);

            private List<MethodConsistencyCallback> m_ConsistencyCheckMethods = new List<MethodConsistencyCallback>();


            /// <summary>
            /// Calls the method callback, which is registered with name methodData.Name;
            /// Find the return value of the method as property of the method data.
            /// </summary>
            /// <param name="methodData">Data of the method call</param>
            /// <returns>True, when such a method exists, otherwise false.</returns>
            public bool CallMethod(IMethodData methodData)
            {
                if (methodData == null)
                {
                    throw new ArgumentNullException(nameof(methodData));
                }
                bool result = false;

                if (m_Methods.ContainsKey(methodData.Name)
                    && (m_Methods[methodData.Name] != null))
                {
                    m_Methods[methodData.Name].Invoke(methodData);
                    result = true;
                }
                return result;
            }

            /// <summary>
            /// Calls the next consistency check in the "Queue of execution"
            /// </summary>
            /// <returns>
            /// True if the end of the queue is not yet reached.
            ///          False if the end of the queue is reached.
            /// </returns>
            public void CallAllConsistencyChecks()
            {
                foreach (MethodConsistencyCallback methodConsistencyCallback in m_ConsistencyCheckMethods)
                {
                    methodConsistencyCallback.Invoke();
                }
            }

            /// <summary>
            /// Registers a method with the specified method name.
            /// </summary>
            /// <param name="methodName">Name of the method to register</param>
            /// <param name="methodCallback">Method callback</param>
            public void RegisterMethod(string methodName, MethodCallback methodCallback)
            {
                if (methodName == null)
                {
                    throw new ArgumentNullException(nameof(methodName));
                }
                if (methodCallback == null)
                {
                    throw new ArgumentNullException(nameof(methodCallback));
                }
                if (!m_Methods.ContainsKey(methodName))
                {
                    m_Methods.Add(methodName, methodCallback);
                }
                else
                {
                    throw new ConfigException("Already registered method detected!");
                }
            }

            /// <summary>
            /// Registers a Consistency Check method into Consistency Manager to be run in future.
            /// </summary>
            /// <param name="methodCallback"></param>
            public void RegisterConsistencyMethod(MethodConsistencyCallback methodCallback)
            {
                {
                    if (methodCallback == null)
                    {
                        throw new ArgumentNullException(nameof(methodCallback));
                    }
                    m_ConsistencyCheckMethods.Add(methodCallback);
                }
            }
        }
    }
}