/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Constants.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all common constants which are necessary for the Interpreter.
    /// </summary>
    internal static class Constants
    {
        //########################################################################################
        #region Constants
        // Contains all common constants

        #region Misc

        /// <summary>defines the format of the date/time strings</summary>
        public const string s_DateTimeFormat = @"yyyy-MM-dd_HH-mm-ss";

        /// <summary>defines the format of the date/time strings for tracing</summary>
        public const string s_DateTimeFormatTrace = @"yyyy-MM-dd_HH:mm:ss.fff";
        /// <summary>defines the 'Value' string</summary>
        public const string s_Value = @"Value";
        /// <summary>defines the 'Text' string</summary>
        public const string s_Text = @"Text";
        /// <summary>defines the 'TextID' string</summary>
        public const string s_TextId = @"TextID";

        /// <summary>defines the 'true' string</summary>
        public const string s_True = @"true";

        /// <summary>defines the 'false' string</summary>
        public const string s_False = @"false";

        /// <summary>defines the file type of an XML file, with dot</summary>
        public const string s_XmlFileType = @".xml";

        /// <summary>defines the extension for external language files</summary>
        public const string s_ExternalLanguageFileExtension = @"-text-";

        /// <summary>defines the name of the FParameter record data</summary>
        public const string s_FParameterRecordDataName = @"FParameter";

        /// <summary>defines the '123456789ABCDEFabcdef' string</summary>
        public const string s_Expression123456789AbcdeFabcdef = @"123456789ABCDEFabcdef";

        /// <summary>defines the 'YYYYYYYYYYYYYYYYYYYYY' string</summary>
        public const string s_ExpressionYyyyyyyyyyyyyyyyyyyyy = @"YYYYYYYYYYYYYYYYYYYYY";

        /// <summary>defines the 'A' character string</summary>
        public const string s_CharacterA = @"A";

        /// <summary>defines the 'B' character string</summary>
        public const string s_CharacterB = @"B";

        /// <summary>defines the 'C' character string</summary>
        public const string s_CharacterC = @"C";

        /// <summary>defines the 'D' character string</summary>
        public const string s_CharacterD = @"D";

        /// <summary>defines the 'Y' character string</summary>
        public const string s_CharacterY = @"Y";




        #endregion

        #region Number Strings

        /// <summary>defines the '0' string</summary>
        public const string s_Number0 = @"0";

        /// <summary>defines the '1' string</summary>
        public const string s_Number1 = @"1";
        /// <summary>defines the '2' string</summary>
        public const string s_Number2 = @"2";
        /// <summary>defines the '4' string</summary>
        public const string s_Number4 = @"4";

        /// <summary>defines the '2' string</summary>
        public const string s_Number7 = @"7";

        /// <summary>defines the '8' string</summary>
        public const string s_Number8 = @"8";

        /// <summary>defines the '15' string</summary>
        public const string s_Number15 = @"15";

        /// <summary>defines the '16' string</summary>
        public const string s_Number16 = @"16";

        /// <summary>defines the '32' string</summary>
        public const string s_Number32 = @"32";

        /// <summary>defines the '40' string</summary>
        public const string s_Number40 = @"40";

        /// <summary>defines the '48' string</summary>
        public const string s_Number48 = @"48";

        /// <summary>defines the '56' string</summary>
        public const string s_Number56 = @"56";

        /// <summary>defines the '64' string</summary>
        public const string s_Number64 = @"64";

        /// <summary>defines the '127' string</summary>
        public const string s_Number127 = @"127";

        /// <summary>defines the '128' string</summary>
        public const string s_Number128 = @"128";

        /// <summary>defines the '255' string</summary>
        public const string s_Number255 = @"255";

        /// <summary>defines the '1024' string</summary>
        public const string s_Number1024 = @"1024";

        /// <summary>defines the '32767' string</summary>
        public const string s_Number32767 = @"32767";

        /// <summary>defines the '32768' string</summary>
        public const string s_Number32768 = @"32768";

        /// <summary>defines the '65535' string</summary>
        public const string s_Number65535 = @"65535";

        /// <summary>defines the '32000000' string</summary>
        public const string s_Number32000000 = @"32000000";

        /// <summary>defines the '2147483647' string</summary>
        public const string s_Number2147483647 = @"2147483647";

        /// <summary>defines the '2147483648' string</summary>
        public const string s_Number2147483648 = @"2147483648";

        /// <summary>defines the '4294967295' string</summary>
        public const string s_Number4294967295 = @"4294967295";

        /// <summary>defines the '9223372036854775807' string</summary>
        public const string s_Number9223372036854775807 = @"9223372036854775807";

        /// <summary>defines the '9223372036854775808' string</summary>
        public const string s_Number9223372036854775808 = @"9223372036854775808";

        /// <summary>defines the '18446744073709551615' string</summary>
        public const string s_Number18446744073709551615 = @"18446744073709551615";


        #endregion

        #region XPath Specific Strings

        /// <summary>defines the 'or' string</summary>
        public const string s_Or = @"or";

        /// <summary>defines the 'and' string</summary>
        public const string s_And = @"and";

        /// <summary>defines the 'not' string</summary>
        public const string s_Not = @"not";

        /// <summary>defines the 'number' string</summary>
        public const string s_Number = @"number";

        /// <summary>defines the 'preceding-sibling' string</summary>
        public const string s_Precedingsibling = @"preceding-sibling";

        /// <summary>defines the 'following-sibling' string</summary>
        public const string s_Followingsibling = @"following-sibling";

        /// <summary>defines the 'count' string</summary>
        public const string s_Count = @"count";

        /// <summary>defines the 'local-name' string</summary>
        public const string s_Localname = @"local-name";

        /// <summary>defines a '+' (plus) string</summary>
        public const string s_Plus = @"+";

        /// <summary>defines a '-' (minus) string</summary>
        public const string s_Minus = @"-";

        /// <summary>defines a 'contains' string</summary>
        public const string s_Contains = @"contains";

        /// <summary>defines a 'translate' string</summary>
        public const string s_Translate = @"translate";

        /// <summary>defines a 'mod' string</summary>
        public const string s_Mod = @"mod";



        #endregion
        #region Version Strings

        /// <summary>defines the V1.0.0.0 version string</summary>
        public const string s_Version1000 = @"V1.0.0.0";

        /// <summary>defines the V1.1.0.0 version string</summary>
        public const string s_Version1100 = @"V1.1.0.0";

        /// <summary>defines the V2.0.0.0 version string</summary>
        public const string s_Version2000 = @"V2.0.0.0";

        /// <summary>defines the V2.1.0.0 version string</summary>
        public const string s_Version2100 = @"V2.1.0.0";

        /// <summary>defines the V2.2.0.0 version string</summary>
        public const string s_Version2200 = @"V2.2.0.0";

        /// <summary>defines the V2.2.5.0 version string</summary>
        public const string s_Version2250 = @"V2.2.5.0";

        /// <summary>defines the actual interpreter version string</summary>
        /// <summary>defines the V2.3.0.0 version string</summary>
        public const string s_Version2300 = @"V2.3.0.0";
        /// <summary>defines the actual interpreter version string</summary>
        public const string s_CurrentInterpreterVersion = s_Version2300;
        /// <summary>defines the actual checker version string</summary>
        public const string s_CurrentCheckerVersion = s_Version2300;

        /// <summary>defines the V1.0 version string</summary>
        public const string s_Version10 = @"V1.0";
        /// <summary>defines the V2.0 version string</summary>
        public const string s_Version20 = @"V2.0";
        /// <summary>defines the V2.1 version string</summary>
        public const string s_Version21 = @"V2.1";
        /// <summary>defines the V2.2 version string</summary>
        public const string s_Version22 = @"V2.2";
        /// <summary>defines the V2.25 version string</summary>
        public const string s_Version225 = @"V2.25";
        /// <summary>defines the V2.3 version string</summary>
        public const string s_Version23 = @"V2.3";
        /// <summary>defines the V2.31 version string</summary>
        public const string s_Version231 = @"V2.31";
        /// <summary>defines the V2.32 version string</summary>
        public const string s_Version232 = @"V2.32";
        /// <summary>defines the V2.33 version string</summary>
        public const string s_Version233 = @"V2.33";
        /// <summary>defines the V2.34 version string</summary>
        public const string s_Version234 = @"V2.34";
        /// <summary>defines the V2.35 version string</summary>
        public const string s_Version235 = @"V2.35";
        /// <summary>defines the V2.4 version string</summary>
        public const string s_Version24 = @"V2.4";
        /// <summary>defines the V2.41 version string</summary>
        public const string s_Version241 = @"V2.41";
        /// <summary>defines the V2.42 version string</summary>
        public const string s_Version242 = @"V2.42";
        /// <summary>defines the V2.43 version string</summary>
        public const string s_Version243 = @"V2.43";
        /// <summary>defines the V2.44 version string</summary>
        public const string s_Version244 = @"V2.44";
        /// <summary>defines the V2.45 version string</summary>
        public const string s_Version245 = @"V2.45";
        /// <summary>defines the current GSDML version string</summary>
        public const string s_CurrentGsdmlVersion = s_Version245;

        #endregion

        #region Language Strings
        // Contains all language string constants

        /// <summary>defines the german language string</summary>
        public const string s_De = @"de";
        /// <summary>defines the english language string</summary>
        public const string s_En = @"en";
        /// <summary>defines the french language string</summary>
        public const string s_Fr = @"fr";
        /// <summary>defines the italian language string</summary>
        public const string s_It = @"it";
        /// <summary>defines the spanish language string</summary>
        public const string s_Es = @"es";
        /// <summary>defines the japanese language string</summary>
        public const string s_Ja = @"ja";
        /// <summary>defines the chinese language string</summary>
        public const string s_Ch = @"ch";

        #endregion

        #region Regular Expressions
        // Contains all regular expression constants

        /// <summary>defines the regular expression for a version string</summary>
        public const string s_RegExVersion = @"^(V){1}([0-9]+\.[0-9]+)$";
        /// <summary>defines the regular expression for the name of a GSDML file</summary>
        /// public const string RegExGsdmlName = @"(GSDML-V){1}\d+\.\d+-.+(-\d{8})(-\d{6}){0,1}\.XML";

        public const string s_RegExGsdmlName = @"^(GSDML-V){1}[0-9]+\.[0-9]+-.+-.+(-[0-9]{8})(-[0-9]{6}){0,1}\.XML$";
        /// <summary>defines the regular expression for a value list with unsigned numbers</summary>
        public const string s_RegExUnsignedValueList = @"^(([0-9]+\.\.[0-9]+)|([0-9]+))(( [0-9]+\.\.[0-9]+)|( [0-9]+))*$";
        
        #endregion

        #region Special Character Strings
        // Contains all special character string constants

        /// <summary>defines a space ' ' string</summary>
        public const string s_Space = @" ";
        /// <summary>defines a space '  ' (two spaces) string</summary>
        public const string s_DoubleSpace = @"  ";
        /// <summary>defines a '\' (back slash) string</summary>
        public const string s_BackSlash = @"\";
        /// <summary>defines a '\\' (two back slashes) string</summary>
        public const string s_DoubleBackSlash = @"\\";
        /// <summary>defines a '/' (slash) string</summary>
        public const string s_Slash = @"/";
        /// <summary>defines a '//' (two slashes) string</summary>
        public const string s_DoubleSlash = @"//";
        /// <summary>defines a ':' (colon) string</summary>
        public const string s_Colon = @":";
        /// <summary>defines a '::' (two colons) string</summary>
        public const string s_DoubleColon = @"::";
        /// <summary>defines a '.' (dot) string</summary>
        public const string s_Dot = @".";
        /// <summary>defines a '..' (two dot) string</summary>
        public const string s_DoubleDot = @"..";
        /// <summary>defines a '&amp;' (ampersand) string</summary>
        public const string s_Ampersand = @"&";
        /// <summary>defines a '&amp;&amp;' (two ampersand) string</summary>
        public const string s_DoubleAmpersand = @"&&";
        /// <summary>defines a ',' (comma) string</summary>
        public const string s_Comma = @",";
        /// <summary>defines a '_' (underscore) string</summary>
        public const string s_Underscore = @"_";
        /// <summary>defines a '-' (hyphen) string</summary>
        public const string s_Hyphen = @"-";
        /// <summary>defines a '@' (commercial at) string</summary>
        public const string s_CommercialAt = @"@";
        /// <summary>defines a '[' (square bracket left) string</summary>
        public const string s_SquareBracketLeft = @"[";
        /// <summary>defines a ']' (square bracket right) string</summary>
        public const string s_SquareBracketRight = @"]";
        /// <summary>defines a '(' (parenthesis left) string</summary>
        public const string s_ParenthesisLeft = @"(";
        /// <summary>defines a ')' (parenthesis right) string</summary>
        public const string s_ParenthesisRight = @")";
        /// <summary>defines a '{' (curly bracket left) string</summary>
        public const string s_CurlyBracketLeft = @"{";
        /// <summary>defines a '}' (curly bracket left) string</summary>
        public const string s_CurlyBracketRight = @"}";
        /// <summary>defines a '=' (equal sign) string</summary>
        public const string s_EqualSign = @"=";
        /// <summary>defines a '!=' (unequal) string</summary>
        public const string s_Unequal = @"!=";
        /// <summary>defines a ''' (apostrophe) string</summary>
        public const string s_Apostrophe = @"'";
        /// <summary>defines a '&gt;' (greater) string</summary>
        public const string s_Greater = @">";
        /// <summary>defines a '&lt;' (lower) string</summary>
        public const string s_Lower = @"<";
        /// <summary>defines a '&gt;=' (greater or equal) string</summary>
        public const string s_GreaterOrEqual = @">=";
        /// <summary>defines a '&lt;=' (lower or equal) string</summary>
        public const string s_LowerOrEqual = @"<=";
        /// <summary>defines a '*' (asterisk) string</summary>
        public const string s_Asterisk = @"*";
        /// <summary>defines a '?' (question mark) string</summary>
        public const string s_QuestionMark = @"?";
        /// <summary>defines a '??' (two question marks) string</summary>
        public const string s_DoubleQuestionMark = @"??";
        /// <summary>defines a ';' (semicolon) string</summary>
        public const string s_Semicolon = @";";
        /// <summary>defines a '<CR>' (carriage return) string</summary>
        public const string s_Cr = @"\\r\\n";

        #endregion

        #region Boundary Values
        // Contains all boundary value constants

        /// <summary>defines the index of the minimal value within the vector</summary>
        public const uint s_MinIndex = 0;
        /// <summary>defines the index of the maximal value within the vector</summary>
        public const uint s_MaxIndex = 1;
        /// <summary>defines the index of the initial value within the vector</summary>
        public const uint s_InitialIndex = 2;
        /// <summary>defines the index of the default value within the vector</summary>
        public const uint s_DefaultIndex = 3;

        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of a BitArea</summary>
        public static readonly uint[] BitArea  = { 1, 15, 1, 1 };
        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of the BitOffset</summary>
        public static readonly uint[] BitOffset  = { 0, 7, 0, 0 };
        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of the UserStructureIdentifier</summary>
        public static readonly uint[] UserStructureIdentifier  = { 0, 32767, 0, 0 };
        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of the SendClock</summary>
        public static readonly uint[] SendClock = { 1, 128, 32, 32 };
        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of the MinDeviceInterval</summary>
        public static readonly uint[] MinDeviceInterval = { 1, 256, 32, 32 };
        /// <summary>list of maximum values for the bit area data type and the respective bit length</summary>
        public static readonly int[] BitAreaMaximumValues  = { 0, 1, 3, 7, 15, 31, 63, 127, 255, 511, 1023, 2047, 4095, 8191, 16383, 32767 };

        #endregion

        #region Custom Arguments

        public const string s_IgnoredChecks = "IgnoredChecks";

        #endregion

        #endregion

        //########################################################################################
        #region Special Checker Constants
        // Contains all common constants

        /// <summary>defines the 'Check_CN_' string</summary>
        public const string s_CheckCn = @"CheckCN_";

        #region Report Fields

        /// <summary>defines the LineNumber field name of the report</summary>
        public const string s_ReportLineNumber = @"LineNumber";
        /// <summary>defines the LinePosition field name of the report</summary>
        public const string s_ReportLinePosition = @"LinePosition";
        /// <summary>defines the CheckNumber field name of the report</summary>
        public const string s_ReportCheckNumber = @"CheckNumber";
        /// <summary>defines the Message field name of the report</summary>
        public const string s_ReportMessage = @"Message";
        /// <summary>defines the SourceXPath field name of the report</summary>
        public const string s_ReportSourceXPath = @"SourceXPath";
        /// <summary>defines the Category field name of the report</summary>
        public const string s_ReportCategory = @"Category";
        /// <summary>defines the Type field name of the report</summary>
        public const string s_ReportType = @"Type";

        #endregion

        #region Checker Check Numbers
        // Contains all check numbers for the Checker as string constants

        // V1.0 checks ----------

        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010002 = @"0x00010002";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010003 = @"0x00010003";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010004 = @"0x00010004";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010005 = @"0x00010005";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010006 = @"0x00010006";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010007 = @"0x00010007";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010008 = @"0x00010008";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010009 = @"0x00010009";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000A = @"0x0001000A";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000B = @"0x0001000B";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000C = @"0x0001000C";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000D = @"0x0001000D";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000E = @"0x0001000E";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001000F = @"0x0001000F";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010010 = @"0x00010010";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010011 = @"0x00010011";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010012 = @"0x00010012";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010013 = @"0x00010013";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010015 = @"0x00010015";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010016 = @"0x00010016";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010017 = @"0x00010017";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010018 = @"0x00010018";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010019 = @"0x00010019";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010020 = @"0x00010020";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010021 = @"0x00010021";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010022 = @"0x00010022";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010023 = @"0x00010023";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010024 = @"0x00010024";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010025 = @"0x00010025";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010026 = @"0x00010026";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010027 = @"0x00010027";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010028 = @"0x00010028";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010029 = @"0x00010029";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001002A = @"0x0001002A";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X0001002B = @"0x0001002B";
        public const string s_Cn_0X0001002C = @"0x0001002C";
        public const string s_Cn_0X0001002D = @"0x0001002D";
        public const string s_Cn_0X0001002E = @"0x0001002E";
        public const string s_Cn_0X0001002F = @"0x0001002F";
        public const string s_Cn_0X00010030 = @"0x00010030";
        public const string s_Cn_0X00010031 = @"0x00010031";
        public const string s_Cn_0X00010032 = @"0x00010032";
        public const string s_Cn_0X00010033 = @"0x00010033";
        public const string s_Cn_0X00010034 = @"0x00010034";
        public const string s_Cn_0X00010035 = @"0x00010035";
        /// <summary>defines check number for GSDML V1.0 check</summary>
        public const string s_Cn_0X00010043 = @"0x00010043";
        public const string s_Cn_0X00010044 = @"0x00010044";
        public const string s_Cn_0X00010045 = @"0x00010045";
        public const string s_Cn_0X00010046 = @"0x00010046";
        public const string s_Cn_0X00010047 = @"0x00010047";
        public const string s_Cn_0X00010048 = @"0x00010048";
        public const string s_Cn_0X00010049 = @"0x00010049";
        public const string s_Cn_0X0001004A = @"0x0001004A";
        public const string s_Cn_0X0001004B = @"0x0001004B";
        public const string s_Cn_0X0001004C = @"0x0001004C";
        public const string s_Cn_0X0001004D = @"0x0001004D";

        public const string s_CnUniqueIds = @"UniqueIds";
        // V2.0 checks ----------

        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010102 = @"0x00010102";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010103 = @"0x00010103";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010104 = @"0x00010104";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010105 = @"0x00010105";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010106 = @"0x00010106";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010107 = @"0x00010107";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010108 = @"0x00010108";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010109 = @"0x00010109";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001010A = @"0x0001010A";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001010B = @"0x0001010B";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001010D = @"0x0001010D";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001010E = @"0x0001010E";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001010F = @"0x0001010F";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010110 = @"0x00010110";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X000101101 = @"0x00010110_1";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010112 = @"0x00010112";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010113 = @"0x00010113";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010114 = @"0x00010114";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010116 = @"0x00010116";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010117 = @"0x00010117";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010118 = @"0x00010118";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010119 = @"0x00010119";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001011B = @"0x0001011B";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001011C = @"0x0001011C";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001011D = @"0x0001011D";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X0001011F = @"0x0001011F";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010120 = @"0x00010120";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010121 = @"0x00010121";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010122 = @"0x00010122";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010123 = @"0x00010123";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010124 = @"0x00010124";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010125 = @"0x00010125";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010126 = @"0x00010126";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010127 = @"0x00010127";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X000101281 = @"0x00010128_1";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X000101282 = @"0x00010128_2";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010129 = @"0x00010129";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0x00010130 = @"0x00010130";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010131 = @"0x00010131";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010132 = @"0x00010132";
        /// <summary>defines check number for GSDML V2.0 check</summary>
        public const string s_Cn_0X00010133 = @"0x00010133";
        public const string s_Cn_0X00010134 = @"0x00010134";
        public const string s_Cn_0X00010135 = @"0x00010135";
        public const string s_Cn_0X00010136 = @"0x00010136";

        // V2.1 checks ----------
        /// <summary>defines check number for GSDML V2.1 check</summary>
        public const string s_Cn_0X00011101 = @"0x00011101";
        public const string s_Cn_0X00011104 = @"0x00011104";
        public const string s_Cn_0X00011106 = @"0x00011106";
        public const string s_Cn_0X00011107 = @"0x00011107";
        public const string s_Cn_0X00011108 = @"0x00011108";
        public const string s_Cn_0X00011109 = @"0x00011109";
        public const string s_Cn_0X0001119A = @"0x0001119A";
        public const string s_Cn_0X0001119B = @"0x0001119B";
        public const string s_Cn_0X0001119C = @"0x0001119C";
        public const string s_Cn_0X0001119D = @"0x0001119D";
        public const string s_Cn_0X0001119E = @"0x0001119E";
        public const string s_Cn_0X000111A0 = @"0x000111A0";
        public const string s_Cn_0X000111A1 = @"0x000111A1";
        public const string s_Cn_0X000111A2 = @"0x000111A2";
        public const string s_Cn_0X000111A3 = @"0x000111A3";
        public const string s_Cn_0X000111A4 = @"0x000111A4";
        public const string s_Cn_0X000111A5 = @"0x000111A5";
        public const string s_Cn_0X000111A6 = @"0x000111A6";
        public const string s_Cn_0X000111A7 = @"0x000111A7";
        public const string s_Cn_0X000111A8 = @"0x000111A8";

        // V2.2 checks ----------
        public const string s_Cn_0X00012101 = @"0x00012101";
        public const string s_Cn_0X00012104 = @"0x00012104";
        public const string s_Cn_0X00012105 = @"0x00012105";
        public const string s_Cn_0X00012105_2 = @"0x00012105_2";
        public const string s_Cn_0X00012107 = @"0x00012107";
        public const string s_Cn_0X00012108 = @"0x00012108";
        public const string s_Cn_0X00012109 = @"0x00012109";
        public const string s_Cn_0X00012110 = @"0x00012110";
        public const string s_Cn_0X0001211D = @"0x0001211D";
        public const string s_Cn_0X00012111 = @"0x00012111";
        public const string s_Cn_0X00012112 = @"0x00012112";
        public const string s_Cn_0X00012113 = @"0x00012113";
        public const string s_Cn_0X00012114 = @"0x00012114";
        public const string s_Cn_0X00012115 = @"0x00012115";
        public const string s_Cn_0X00012116 = @"0x00012116";

        // V2.25 checks ----------
        public const string s_Cn_0X00012202 = @"0x00012202";
        public const string s_Cn_0X00012203 = @"0x00012203";
        public const string s_Cn_0X00012204 = @"0x00012204";
        public const string s_Cn_0X00012205 = @"0x00012205";
        public const string s_Cn_0X00012206 = @"0x00012206";
        public const string s_Cn_0X00012207 = @"0x00012207";
        public const string s_Cn_0X00012208 = @"0x00012208";
        public const string s_Cn_0X00012209 = @"0x00012209";
        public const string s_Cn_0X0001220A = @"0x0001220A";
        public const string s_Cn_0X0001220B = @"0x0001220B";
        public const string s_Cn_0x0001220C = @"0x0001220C";

        // V2.3 Checks
        public const string s_Cn_0X00020002 = @"0x00020002";
        public const string s_Cn_0X00020003 = @"0x00020003";
        public const string s_Cn_0X00020008 = @"0x00020008";
        public const string s_Cn_0X00020010 = @"0x00020010";
        public const string s_Cn_0X00020012 = @"0x00020012";
        public const string s_Cn_0X00020013 = @"0x00020013";
        public const string s_Cn_0X00020014 = @"0x00020014";
        public const string s_Cn_0X00020016 = @"0x00020016";
        public const string s_Cn_0X00020017 = @"0x00020017";
        public const string s_Cn_0X00020018 = @"0x00020018";
        public const string s_Cn_0X00020020 = @"0x00020020";
        public const string s_Cn_0X00020021 = @"0x00020021";
        public const string s_Cn_0X00020022 = @"0x00020022";
        public const string s_Cn_0X00020023 = @"0x00020023";
        public const string s_Cn_0X00020024 = @"0x00020024";
        public const string s_Cn_0X00020025 = @"0x00020025";
        public const string s_Cn_0X00020026 = @"0x00020026";
        public const string s_Cn_0X00020027 = @"0x00020027";
        public const string s_Cn_0X00020028 = @"0x00020028";
        public const string s_Cn_0X00020029 = @"0x00020029";
        public const string s_Cn_0X0002002A = @"0x0002002A";
        public const string s_Cn_0X0002002B = @"0x0002002B";
        public const string s_Cn_0X0002002C = @"0x0002002C";
        public const string s_Cn_0X0002002D = @"0x0002002D";
        public const string s_Cn_0X0002002E = @"0x0002002E";
        public const string s_Cn_0X00020030 = @"0x00020030";
        public const string s_Cn_0X00020031 = @"0x00020031";
        public const string s_Cn_0X00020032 = @"0x00020032";

        // V2.31 Checks
        public const string s_Cn_0X00030002 = @"0x00030002";
        public const string s_Cn_0X00030003 = @"0x00030003";
        public const string s_Cn_0X00030004 = @"0x00030004";
        public const string s_Cn_0X00030005 = @"0x00030005";
        public const string s_Cn_0X00030007 = @"0x00030007";
        public const string s_Cn_0X00030008 = @"0x00030008";
        public const string s_Cn_0X00030009 = @"0x00030009";
        public const string s_Cn_0X00030011 = @"0x00030011";
        public const string s_Cn_0X00030012 = @"0x00030012";
        public const string s_Cn_0X00030013 = @"0x00030013";
        public const string s_Cn_0X00030014 = @"0x00030014";
        public const string s_Cn_0X00030015 = @"0x00030015";
        public const string s_Cn_0X00030020 = @"0x00030020";
        public const string s_Cn_0X0003002A = @"0x0003002A";
        public const string s_Cn_0X0003002B = @"0x0003002B";
        public const string s_Cn_0X0003002C = @"0x0003002C";
        public const string s_Cn_0X0003002D = @"0x0003002D";
        public const string s_Cn_0X0003002E = @"0x0003002E";
        public const string s_Cn_0X0003002F = @"0x0003002F";
        public const string s_Cn_0X00030030 = @"0x00030030";
        public const string s_Cn_0X00030031 = @"0x00030031";
        public const string s_Cn_0X00030032 = @"0x00030032";

        // V2.32 Checks
        public const string s_Cn_0X00032000 = @"0x00032000";
        public const string s_Cn_0X00032001 = @"0x00032001";
        public const string s_Cn_0X00032002 = @"0x00032002";
        public const string s_Cn_0X00032003 = @"0x00032003";
        public const string s_Cn_0X00032004 = @"0x00032004";
        public const string s_Cn_0X00032005 = @"0x00032005";
        public const string s_Cn_0X00032006 = @"0x00032006";
        public const string s_Cn_0X00032007 = @"0x00032007";
        public const string s_Cn_0X00032008 = @"0x00032008";
        public const string s_Cn_0X00032009 = @"0x00032009";
        public const string s_Cn_0X0003200A = @"0x0003200A";
        public const string s_Cn_0X0003200B = @"0x0003200B";

        // V2.33 Checks
        public const string s_Cn_0X00033000 = @"0x00033000";
        public const string s_Cn_0X00033001 = @"0x00033001";
        public const string s_Cn_0X00033002 = @"0x00033002";
        public const string s_Cn_0X00033003 = @"0x00033003";
        public const string s_Cn_0X00033005 = @"0x00033005";
        public const string s_Cn_0X00033006 = @"0x00033006";
        public const string s_Cn_0X00033007 = @"0x00033007";
        public const string s_Cn_0X00033008 = @"0x00033008";
        public const string s_Cn_0X00033009 = @"0x00033009";

        // V2.35 Checks
        public const string s_Cn_0X00035000 = @"0x00035000";
        public const string s_Cn_0X00035001 = @"0x00035001";
        public const string s_Cn_0X00035002 = @"0x00035002";
        public const string s_Cn_0X00035003 = @"0x00035003";

        // V2.4 Checks
        public const string s_Cn_0X00040000 = @"0x00040000";
        public const string s_Cn_0X00040001 = @"0x00040001";
        public const string s_Cn_0X00040002 = @"0x00040002";
        public const string s_Cn_0X00040003 = @"0x00040003";
        public const string s_Cn_0X00040004 = @"0x00040004";
        public const string s_Cn_0X00040005 = @"0x00040005";
        public const string s_Cn_0X00040006 = @"0x00040006";
        // V2.41 Checks
        public const string s_Cn_0X00041000 = @"0x00041000";
        public const string s_Cn_0X00041001 = @"0x00041001";
        public const string s_Cn_0X00041002 = @"0x00041002";

        // V2.42 Checks
        public const string s_Cn_0X00042000 = @"0x00042000";
        public const string s_Cn_0X00042001 = @"0x00042001";
        public const string s_Cn_0X00042002 = @"0x00042002";
        public const string s_Cn_0X00042003 = @"0x00042003";
        public const string s_Cn_0X00042004 = @"0x00042004";
        public const string s_Cn_0X00042005 = @"0x00042005";
        public const string s_Cn_0X00042006 = @"0x00042006";

        // V2.43 Checks
        public const string s_Cn_0X00043000 = @"0x00043000";
        public const string s_Cn_0X00043001 = @"0x00043001";
        public const string s_Cn_0X00043002 = @"0x00043002";
        public const string s_Cn_0X00043003 = @"0x00043003";
        public const string s_Cn_0X00043004 = @"0x00043004";
        public const string s_Cn_0X00043005 = @"0x00043005";
        public const string s_Cn_0X00043006 = @"0x00043006";
        public const string s_Cn_0X00043007 = @"0x00043007";
        public const string s_Cn_0X00043008 = @"0x00043008";
        public const string s_Cn_0X00043009 = @"0x00043009";
        public const string s_Cn_0X00043010 = @"0x00043010";


        // V2.44 Checks
        public const string s_Cn_0X00044000 = @"0x00044000";
        public const string s_Cn_0X00044001 = @"0x00044001";
        public const string s_Cn_0X00044002 = @"0x00044002";
        public const string s_Cn_0X00044003 = @"0x00044003";

        // V2.45 Checks
        public const string s_Cn_0X00045000 = @"0x00045000";
        public const string s_Cn_0X00045001 = @"0x00045001";
        public const string s_Cn_0X00045002 = @"0x00045002";
        public const string s_Cn_0X00045003 = @"0x00045003";
        public const string s_Cn_0X00045004 = @"0x00045004";
        public const string s_Cn_0X00045005 = @"0x00045005";

        #endregion
        #region Boundary Values
        // Contains all boundary value constants for the checker

        /// <summary>defines the minimal value for the subslot number of system defined submodules</summary>
        public const uint s_SystemDefinedSubmoduleSubslotNumberMin = 32768;

        /// <summary>defines the minimal value (index 0), maximal value (index 1), initial value (index 2) 
        /// and the default value (index 3) of the F_Source_Add or F_Dest_Add</summary>
        public static readonly uint[] FSourceDestinationAddress = { 1, 65534, 1, 1 };
        /// <summary>defines the maximal number for error/warning messages for the same issue for the same XElement/XAttribute</summary>
        public const uint s_MaxNumberErrorMessages = 10;

        #endregion

        #endregion

    }
}


