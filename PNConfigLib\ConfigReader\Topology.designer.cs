// ------------------------------------------------------------------------------
//  <auto-generated>
//    Generated by Xsd2Code. Version 3.4.0.32989
//    <NameSpace>PNConfigLib.ConfigReader.Topology</NameSpace><Collection>List</Collection><codeType>CSharp</codeType><EnableDataBinding>False</EnableDataBinding><EnableLazyLoading>False</EnableLazyLoading><TrackingChangesEnable>False</TrackingChangesEnable><GenTrackingClasses>False</GenTrackingClasses><HidePrivateFieldInIDE>False</HidePrivateFieldInIDE><EnableSummaryComment>False</EnableSummaryComment><VirtualProp>False</VirtualProp><IncludeSerializeMethod>False</IncludeSerializeMethod><UseBaseClass>False</UseBaseClass><GenBaseClass>False</GenBaseClass><GenerateCloneMethod>False</GenerateCloneMethod><GenerateDataContracts>False</GenerateDataContracts><CodeBaseTag>Net20</CodeBaseTag><SerializeMethodName>Serialize</SerializeMethodName><DeserializeMethodName>Deserialize</DeserializeMethodName><SaveToFileMethodName>SaveToFile</SaveToFileMethodName><LoadFromFileMethodName>LoadFromFile</LoadFromFileMethodName><GenerateXMLAttributes>True</GenerateXMLAttributes><OrderXMLAttrib>False</OrderXMLAttrib><EnableEncoding>False</EnableEncoding><AutomaticProperties>False</AutomaticProperties><GenerateShouldSerialize>False</GenerateShouldSerialize><DisableDebug>False</DisableDebug><ProPNameSpecified>Default</ProPNameSpecified><Encoder>UTF8</Encoder><CustomUsings></CustomUsings><ExcludeIncludedTypes>False</ExcludeIncludedTypes><EnableInitializeFields>True</EnableInitializeFields>
//  </auto-generated>
// ------------------------------------------------------------------------------
namespace PNConfigLib.ConfigReader.Topology {
    using System;
    using System.Diagnostics;
    using System.Xml.Serialization;
    using System.Collections;
    using System.Xml.Schema;
    using System.ComponentModel;
    using System.Collections.Generic;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    [System.Xml.Serialization.XmlRootAttribute("Topology", Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology", IsNullable=false)]
    public partial class Topology {
        
        private List<TopologyTypePortInterconnection> portInterconnectionField;
        
        private string topologyIDField;
        
        private string topologyNameField;
        
        private string topologyDescriptionField;
        
        private string listOfNodesRefIDField;
        
        private string schemaVersionField;
        
        public Topology() {
            this.portInterconnectionField = new List<TopologyTypePortInterconnection>();
            this.schemaVersionField = "1.0";
        }
        
        [System.Xml.Serialization.XmlElementAttribute("PortInterconnection")]
        public List<TopologyTypePortInterconnection> PortInterconnection {
            get {
                return this.portInterconnectionField;
            }
            set {
                this.portInterconnectionField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string TopologyID {
            get {
                return this.topologyIDField;
            }
            set {
                this.topologyIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TopologyName {
            get {
                return this.topologyNameField;
            }
            set {
                this.topologyNameField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TopologyDescription {
            get {
                return this.topologyDescriptionField;
            }
            set {
                this.topologyDescriptionField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ListOfNodesRefID {
            get {
                return this.listOfNodesRefIDField;
            }
            set {
                this.listOfNodesRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string schemaVersion {
            get {
                return this.schemaVersionField;
            }
            set {
                this.schemaVersionField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    public partial class TopologyTypePortInterconnection {
        
        private TopologyTypePortInterconnectionLocalPort localPortField;
        
        private TopologyTypePortInterconnectionPartnerPort partnerPortField;
        
        private PropertiesType propertiesField;
        
        public TopologyTypePortInterconnection() {
            this.propertiesField = new PropertiesType();
            this.partnerPortField = new TopologyTypePortInterconnectionPartnerPort();
            this.localPortField = new TopologyTypePortInterconnectionLocalPort();
        }
        
        public TopologyTypePortInterconnectionLocalPort LocalPort {
            get {
                return this.localPortField;
            }
            set {
                this.localPortField = value;
            }
        }
        
        public TopologyTypePortInterconnectionPartnerPort PartnerPort {
            get {
                return this.partnerPortField;
            }
            set {
                this.partnerPortField = value;
            }
        }
        
        public PropertiesType Properties {
            get {
                return this.propertiesField;
            }
            set {
                this.propertiesField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    public partial class TopologyTypePortInterconnectionLocalPort {
        
        private string deviceRefIDField;
        
        private string interfaceRefIDField;
        
        private byte portNumberField;
        
        private ushort slotNumberField;
        
        private bool slotNumberFieldSpecified;
        
        private FiberOpticType cableTypeField;
        
        private PartnerPortType partnerPortTypeField;
        
        public TopologyTypePortInterconnectionLocalPort() {
            this.cableTypeField = FiberOpticType.NotSpecified;
            this.partnerPortTypeField = PartnerPortType.SinglePartner;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceRefID {
            get {
                return this.deviceRefIDField;
            }
            set {
                this.deviceRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceRefID {
            get {
                return this.interfaceRefIDField;
            }
            set {
                this.interfaceRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public byte PortNumber {
            get {
                return this.portNumberField;
            }
            set {
                this.portNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SlotNumber {
            get {
                return this.slotNumberField;
            }
            set {
                this.slotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SlotNumberSpecified {
            get {
                return this.slotNumberFieldSpecified;
            }
            set {
                this.slotNumberFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(FiberOpticType.NotSpecified)]
        public FiberOpticType CableType {
            get {
                return this.cableTypeField;
            }
            set {
                this.cableTypeField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(PartnerPortType.SinglePartner)]
        public PartnerPortType PartnerPortType {
            get {
                return this.partnerPortTypeField;
            }
            set {
                this.partnerPortTypeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology", IsNullable=false)]
    public enum FiberOpticType {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO standard cable GP (50 µm)")]
        FOstandardcableGP50µm,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO trailing cable / GP")]
        FOtrailingcableGP,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO ground cable")]
        FOgroundcable,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("FO standard cable (62.5 µm)")]
        FOstandardcable625µm,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Flexible FO cable")]
        FlexibleFOcable,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("POF standard cable GP")]
        POFstandardcableGP,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("POF trailing cable")]
        POFtrailingcable,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("PCF standard cable GP")]
        PCFstandardcableGP,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("PCF trailing cable / GP")]
        PCFtrailingcableGP,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("GI-PCF standard cable")]
        GIPCFstandardcable,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("GI-PCF trailing cable")]
        GIPCFtrailingcable,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Not Specified")]
        NotSpecified,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology", IsNullable=false)]
    public enum PartnerPortType {
        
        /// <remarks/>
        SinglePartner,
        
        /// <remarks/>
        SetByUserProgram,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology", IsNullable=true)]
    public partial class PropertiesType {
        
        private object itemField;
        
        [System.Xml.Serialization.XmlElementAttribute("CableLength", typeof(CableLength))]
        [System.Xml.Serialization.XmlElementAttribute("SignalDelay", typeof(float))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology", IsNullable=false)]
    public enum CableLength {
        
        /// <remarks/>
        max20m,
        
        /// <remarks/>
        max50m,
        
        /// <remarks/>
        max100m,
        
        /// <remarks/>
        max500m,
        
        /// <remarks/>
        max1000m,
        
        /// <remarks/>
        max3000m,
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("Not Specified")]
        NotSpecified,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Xsd2Code", "3.4.0.32990")]
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.siemens.com/Automation/PNConfigLib/Topology")]
    public partial class TopologyTypePortInterconnectionPartnerPort {
        
        private string deviceRefIDField;
        
        private string interfaceRefIDField;
        
        private byte portNumberField;
        
        private ushort slotNumberField;
        
        private bool slotNumberFieldSpecified;
        
        private FiberOpticType cableTypeField;
        
        public TopologyTypePortInterconnectionPartnerPort() {
            this.cableTypeField = FiberOpticType.NotSpecified;
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string DeviceRefID {
            get {
                return this.deviceRefIDField;
            }
            set {
                this.deviceRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string InterfaceRefID {
            get {
                return this.interfaceRefIDField;
            }
            set {
                this.interfaceRefIDField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public byte PortNumber {
            get {
                return this.portNumberField;
            }
            set {
                this.portNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public ushort SlotNumber {
            get {
                return this.slotNumberField;
            }
            set {
                this.slotNumberField = value;
            }
        }
        
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool SlotNumberSpecified {
            get {
                return this.slotNumberFieldSpecified;
            }
            set {
                this.slotNumberFieldSpecified = value;
            }
        }
        
        [System.Xml.Serialization.XmlAttributeAttribute()]
        [System.ComponentModel.DefaultValueAttribute(FiberOpticType.NotSpecified)]
        public FiberOpticType CableType {
            get {
                return this.cableTypeField;
            }
            set {
                this.cableTypeField = value;
            }
        }
    }
}
