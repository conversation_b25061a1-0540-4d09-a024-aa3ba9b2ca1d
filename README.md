# PNConfigTool

## Overview

PNConfigTool is a visual configuration software designed to facilitate the configuration of PROFINET master stations and devices. It allows users to generate configuration files such as `Configuration.xml` and `ListOfNodes.xml` for use with the PNConfigLib software. Additionally, it utilizes PNConfigLibRunner to generate configuration files recognizable by PNDriver-IOC.

## Features

- Visual configuration interface for PROFINET master stations and devices.
- Generation of `Configuration.xml` and `ListOfNodes.xml` files.
- Integration with PNConfigLibRunner for generating PNDriver-IOC compatible configuration files.

## Requirements

- .NET SDK

## Installation

1. Clone the repository:
   ```bash
   git clone PNBuilder.git
   ```

2. Navigate to the project directory:
   ```bash
   cd PNBuilder
   ```

## Running the Project

To run the project, execute the following command:

```bash
dotnet run --project PNConfigTool/PNConfigTool.csproj
```

## Usage

1. Launch the application using the command above.
2. Use the visual interface to configure PROFINET master stations and devices.
3. Save the configuration to generate the necessary XML files.
4. Utilize PNConfigLibRunner to produce PNDriver-IOC compatible files.


