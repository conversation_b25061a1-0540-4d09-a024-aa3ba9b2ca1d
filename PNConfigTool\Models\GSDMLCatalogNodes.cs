using System.Collections.ObjectModel;

namespace PNConfigTool.Models
{
    /// <summary>
    /// GSDML树节点基类
    /// </summary>
    public abstract class GSDMLNodeBase
    {
        /// <summary>
        /// 节点名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 是否展开
        /// </summary>
        public bool IsExpanded { get; set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected { get; set; }
    }

    /// <summary>
    /// GSDML制造商节点
    /// </summary>
    public class GSDMLManufacturerNode : GSDMLNodeBase
    {
        /// <summary>
        /// 设备列表
        /// </summary>
        public ObservableCollection<GSDMLDeviceNode> Devices { get; } = new ObservableCollection<GSDMLDeviceNode>();
    }

    /// <summary>
    /// GSDML设备节点
    /// </summary>
    public class GSDMLDeviceNode : GSDMLNodeBase
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// GSDML文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 模块列表
        /// </summary>
        public ObservableCollection<GSDMLModuleNode> Modules { get; } = new ObservableCollection<GSDMLModuleNode>();
    }

    /// <summary>
    /// GSDML模块节点
    /// </summary>
    public class GSDMLModuleNode : GSDMLNodeBase
    {
        /// <summary>
        /// 模块ID
        /// </summary>
        public string ModuleId { get; set; } = string.Empty;

        /// <summary>
        /// 子模块列表
        /// </summary>
        public ObservableCollection<GSDMLSubmoduleNode> Submodules { get; } = new ObservableCollection<GSDMLSubmoduleNode>();
    }

    /// <summary>
    /// GSDML子模块节点
    /// </summary>
    public class GSDMLSubmoduleNode : GSDMLNodeBase
    {
        /// <summary>
        /// 子模块ID
        /// </summary>
        public string SubmoduleId { get; set; } = string.Empty;

        /// <summary>
        /// 接口列表
        /// </summary>
        public ObservableCollection<GSDMLInterfaceNode> Interfaces { get; } = new ObservableCollection<GSDMLInterfaceNode>();
    }

    /// <summary>
    /// GSDML接口节点
    /// </summary>
    public class GSDMLInterfaceNode : GSDMLNodeBase
    {
        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public int Size { get; set; }
    }
} 