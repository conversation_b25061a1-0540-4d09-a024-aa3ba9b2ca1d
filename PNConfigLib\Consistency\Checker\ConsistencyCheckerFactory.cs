/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyCheckerFactory.cs              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.ConfigReader.Topology;
using PNConfigLib.Consistency.Checker.ConfigurationFileChecker;

namespace PNConfigLib.Consistency.Checker
{
    internal class ConsistencyCheckerFactory
    {
        private readonly Configuration m_Configuration;

        private readonly ListOfNodes m_ListOfNodes;

        private readonly Topology m_Topology;
        private readonly string m_ListOfNodesPath;
        internal ConsistencyCheckerFactory(Configuration cfg, ListOfNodes lon, string listOfNodesPath, Topology topo = null)
        {
            m_Configuration = cfg;
            m_ListOfNodes = lon;
            m_Topology = topo;
            m_ListOfNodesPath = listOfNodesPath;
        }

        internal IConsistencyChecker GetCentralDeviceChecker()
        {
            return new CentralDeviceChecker(m_Configuration.Devices.CentralDevice, m_Configuration.Subnet, m_ListOfNodes, m_ListOfNodesPath);
        }

        internal IConsistencyChecker GetDecentralDeviceChecker()
        {
            return new DecentralDeviceChecker(m_Configuration.Devices.DecentralDevice, m_Configuration.Subnet, m_ListOfNodes, m_Topology);
        }

        internal IConsistencyChecker GetGsdReferenceChecker()
        {
            return new GsdReferenceChecker(m_Configuration.Devices.DecentralDevice, m_ListOfNodes);
        }

        internal IConsistencyChecker GetIOAddressChecker()
        {
            return new IOAddressChecker(m_Configuration, m_ListOfNodes);
        }

        internal IConsistencyChecker GetIsochronChecker()
        {
            return new IsochronChecker(m_Configuration.Devices, m_ListOfNodes);
        }

        internal IConsistencyChecker GetMrpChecker()
        {
            return new MrpChecker(m_Configuration.Devices.CentralDevice,
                                                      m_Configuration.Devices.DecentralDevice,
                                                      m_Configuration.Subnet.SelectMany(s => s.DomainManagement.MrpDomains).ToList(),
                                                      m_ListOfNodes);
        }

        internal IConsistencyChecker GetParameterRecordDataChecker()
        {
            return new ParameterRecordDataChecker(m_Configuration.Devices, m_ListOfNodes);
        }

        internal IConsistencyChecker GetPortChecker()
        {
            return new PortChecker(m_Configuration.Devices.DecentralDevice, m_ListOfNodes);
        }

        internal IConsistencyChecker GetSharedDeviceChecker()
        {
            return new SharedDeviceChecker(m_Configuration.Devices, m_Configuration.Subnet, m_ListOfNodes);
        }

        internal IConsistencyChecker GetSubnetChecker()
        {
            return new SubnetChecker(m_Configuration.Subnet, m_Configuration.Devices);
        }

        internal IConsistencyChecker GetIPConfigurationChecker()
        {
            return new IPConfigurationChecker(m_Configuration);
        }

        internal IConsistencyChecker GetSyncDomainChecker()
        {
            return new SyncDomainChecker(m_Configuration, m_ListOfNodes);
        }

        internal IConsistencyChecker GetIOSystemChecker()
        {
            return new IOSystemChecker(m_Configuration);
        }
        
    }
}
