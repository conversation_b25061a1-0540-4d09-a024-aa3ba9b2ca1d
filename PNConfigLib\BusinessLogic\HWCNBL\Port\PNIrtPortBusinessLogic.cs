/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: PNIrtPortBusinessLogic.cs                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

using PNConfigLib.Consistency;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.HWCNBL.Constants.AttributeValues;
using PNConfigLib.HWCNBL.Utilities;

#endregion

namespace PNConfigLib.HWCNBL.Port
{
    internal class PNIrtPortBusinessLogic : PortDecorator
    {
        public PNIrtPortBusinessLogic(IPortBL decoratedPort) : base(decoratedPort)
        {
            InitBL();
        }
        public override void InitBL()
        {
            InitAttributes();
            InitActions();
        }

        protected void InitAttributes()
        {
            Port.AttributeAccess.AddAnyAttribute<bool>(InternalAttributeNames.PnIrtUsingShortPreamble, false);
        }

        /// <summary></summary>
        private void ConsistencyCheck_SignalDelayTimeLimits()
        {
            AttributeAccessCode aCode = new AttributeAccessCode();
            UInt32 selection = Port.AttributeAccess.GetAnyAttribute<UInt32>(
                    InternalAttributeNames.PnIrtLineDelaySelection, aCode, 0);
            if (!aCode.IsOkay || (selection != 1))
            {
                return;
            }

            // Signal delay is selected in the port interconnection

            // Check min value
            aCode.Reset();

            uint signalDelayTimeAttribute = Port.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIrtSignalDelayTime, aCode, 0);

            uint minValue = Convert.ToUInt32(PNConstants.PNIrtSignalMinDelayTime, CultureInfo.InvariantCulture);

            if (aCode.IsOkay
                && signalDelayTimeAttribute < minValue)
            {
                ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.SignalDelayTimeLessThanMinValue, AttributeUtilities.GetName(Port));
                return;
            }

            // Check max value
            uint maxValue = Convert.ToUInt32(PNConstants.PNIrtSignalMaxDelayTime, CultureInfo.InvariantCulture);

            // Check relative forwarder case. If the interface submodule of the port is a relative forwarder, 
            // max. value can be at most 18 �s, independent from the mdd value. 
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(Port);

            if (interfaceSubmodule != null)
            {
                AttributeAccessCode ac = new AttributeAccessCode();

                Enumerated pnIrtForwardingMode = interfaceSubmodule.AttributeAccess.GetAnyAttribute<Enumerated>(
                    InternalAttributeNames.PnIrtForwardingMode, 
                    ac, 
                    null);
                PNIrtForwardingMode currForwardingMode = ac.IsOkay
                                         ? (PNIrtForwardingMode)pnIrtForwardingMode.DefaultValue
                                         : PNIrtForwardingMode.None;
                if (currForwardingMode == PNIrtForwardingMode.Relative)
                {
                    maxValue = Math.Min(maxValue, PNConstants.RelativeForwarderMaxSignalDelay);
                }
            }

            if (maxValue != UInt32.MaxValue && signalDelayTimeAttribute > maxValue)
            {
                float maxValueMicro = (float)maxValue / 1000;
                ConsistencyLogger.Log(
                    ConsistencyType.PN,
                    LogSeverity.Error,
                    Port,
                    ConsistencyConstants.SignalDelayTimeMoreThanMaxValue,
                    Utility.GetNameWithContainer(Port),
                    maxValueMicro);
            }
        }
        /// <summary>
        /// A port in IRT-using must have a valid Signal delay time (not 0)
        /// </summary>
        private void ConsistencyCheck_SignalDelayTimeWithIrt()
        {
            IList<DataModel.PCLObjects.Port> partnerPorts = Port.GetPartnerPorts();
            if ((partnerPorts != null)
                && !partnerPorts.Any())
            {
                return;
            }

            bool isAnyPartnerSynchronized = false;

            if (partnerPorts != null)
            {
                foreach (DataModel.PCLObjects.Port partnerPort in partnerPorts)
                {
                    Interface partnerInterface = NavigationUtilities.GetInterfaceOfPort(partnerPort);
                    if (Utility.IsDeviceSynchronized(partnerInterface))
                    {
                        isAnyPartnerSynchronized = true;
                        break;
                    }
                }
            }

            // IRT case: check if the signal delay has a valid value, if not give a compile error
            Interface interfaceSubmodule = NavigationUtilities.GetInterfaceOfPort(Port);
            uint signalDelayTimeAttribute =
                Port.AttributeAccess.GetAnyAttribute<uint>(
                    InternalAttributeNames.PnIrtSignalDelayTime,
                    new AttributeAccessCode(),
                    0);

            if ((signalDelayTimeAttribute != 0)
                || !Utility.IsDeviceSynchronized(interfaceSubmodule)
                || !isAnyPartnerSynchronized)
            {
                return;
            }
            ConsistencyLogger.Log(ConsistencyType.PN, LogSeverity.Error, Port, ConsistencyConstants.PortIrtLineLengthNotSpecified,
                    AttributeUtilities.GetName(Port));
        }

        private void InitActions()
        {
            ConsistencyManager.RegisterConsistencyCheck(Port, MethodCheckConsistency);
        }

        private void MethodCheckConsistency()
        {
            ConsistencyCheck_SignalDelayTimeLimits();
            ConsistencyCheck_SignalDelayTimeWithIrt();
        }
    }
}