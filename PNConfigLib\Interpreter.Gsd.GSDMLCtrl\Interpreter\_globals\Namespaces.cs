/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Namespaces.cs                             :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.Gsd.Interpreter
{
    /// <summary>
    /// Collects all namespace names of the GSDML specification prefix names and corresponding 
    /// values, like schema names.
    /// </summary>
    internal static class Namespaces
    {
        //########################################################################################
        #region GSDML Namespaces
        // Contains all namespace names as constants

        /// <summary>GSDML device profile namespace</summary>
        public const string s_GsdmlDeviceProfile = @"http://www.profibus.com/GSDML/2003/11/DeviceProfile";
        /// <summary>GSDML primitives namespace</summary>
        public const string s_GsdmlPrimitives = @"http://www.profibus.com/GSDML/2003/11/Primitives";
        /// <summary>common primitives namespace</summary>
        public const string s_CommonPrimitives = @"http://www.profibus.com/Common/2003/11/Primitives";
        /// <summary>XML schema instance namespace</summary>
        public const string s_XmlSchemaInstance = @"http://www.w3.org/2001/XMLSchema-instance";
        /// <summary>XML schema namespace</summary>
        public const string s_XmlSchema = @"http://www.w3.org/2001/XMLSchema";
        /// <summary>XML signature namespace</summary>
        public const string s_XmlSignature = @"http://www.w3.org/2000/09/xmldsig#";

        #endregion

        //########################################################################################
        #region GSDML Namespace Prefixes
        // Contains all namespace prefix names as constants

        /// <summary>prefix for GSDML device profile namespace</summary>
        public const string s_PrefixGsdmlDeviceProfile = @"gsddef";
        /// <summary>prefix for GSDML primitives namespace</summary>
        public const string s_PrefixGsdmlPrimitives = @"gsdbase";
        /// <summary>prefix for common primitives namespace</summary>
        public const string s_PrefixCommonPrimitives = @"gsdprim";
        /// <summary>prefix for XML schema instance namespace</summary>
        public const string s_PrefixXmlSchemaInstance = @"gsdxsi";
        /// <summary>prefix for XML schema namespace</summary>
        public const string s_PrefixXmlSchema = @"gsdxsd";
        /// <summary>prefix for XML signature namespace</summary>
        public const string s_PrefixXmlSignature = @"gsdsig";

        #endregion

        //########################################################################################
        #region GSDML Schema Names
        // Contains all namespace prefix names as constants
        /// <summary>name of the GSDML device profile schema for version 2.45</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02045 = @"GSDML-DeviceProfile-V2.45.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.44</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02044 = @"GSDML-DeviceProfile-V2.44.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.43</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02043 = @"GSDML-DeviceProfile-V2.43.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.42</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02042 = @"GSDML-DeviceProfile-V2.42.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.41</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02041 = @"GSDML-DeviceProfile-V2.41.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.4</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV0204 = @"GSDML-DeviceProfile-V2.4.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.35</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02035 = @"GSDML-DeviceProfile-V2.35.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.34</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02034 = @"GSDML-DeviceProfile-V2.34.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.33</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02033 = @"GSDML-DeviceProfile-V2.33.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.32</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02032 = @"GSDML-DeviceProfile-V2.32.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.31</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02031 = @"GSDML-DeviceProfile-V2.31.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.3</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV0203 = @"GSDML-DeviceProfile-V2.3.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.25</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV02025 = @"GSDML-DeviceProfile-V2.25.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.2</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV0202 = @"GSDML-DeviceProfile-V2.2.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.1</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV0201 = @"GSDML-DeviceProfile-V2.1.xsd";
        /// <summary>name of the GSDML device profile schema for version 2.0</summary>
        public const string s_SchemaGsdmlDeviceProfileNameV0200 = @"GSDML-DeviceProfile-V2.0.xsd";
        /// <summary>name of the GSDML device profile schema for version 1.0</summary>
        public const string s_SchemaGsdmlDeviceProfileName = @"GSDML-DeviceProfile-V1.0.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.32</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV02032 = @"gsdml-commnetworkprofile-v2.32.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.31</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV02031 = @"gsdml-commnetworkprofile-v2.31.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.3</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV0203 = @"gsdml-commnetworkprofile-v2.3.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.25</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV02025 = @"gsdml-commnetworkprofile-v2.25.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.2</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV0202 = @"gsdml-commnetworkprofile-v2.2.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.1</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV0201 = @"gsdml-commnetworkprofile-v2.1.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 2.0</summary>
        public const string s_SchemaGsdmlCommNetworkProfileNameV0200 = @"gsdml-commnetworkprofile-v2.0.xsd";
        /// <summary>name of the GSDML communication network profile schema for version 1.0</summary>
        public const string s_SchemaGsdmlCommNetworkProfileName = @"gsdml-commnetworkprofile-v1.0.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.45</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02045 = @"GSDML-Primitives-v2.45.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.44</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02044 = @"GSDML-Primitives-v2.44.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.43</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02043 = @"GSDML-Primitives-v2.43.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.42</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02042 = @"GSDML-Primitives-v2.42.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.41</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02041 = @"GSDML-Primitives-v2.41.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.4</summary>
        public const string s_SchemaGsdmlPrimitivesNameV0204 = @"GSDML-Primitives-v2.4.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.35</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02035 = @"GSDML-Primitives-v2.35.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.34</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02034 = @"GSDML-Primitives-v2.34.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.33</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02033 = @"GSDML-Primitives-v2.33.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.32</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02032 = @"GSDML-Primitives-v2.32.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.31</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02031 = @"GSDML-Primitives-v2.31.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.3</summary>
        public const string s_SchemaGsdmlPrimitivesNameV0203 = @"GSDML-Primitives-v2.3.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.25</summary>
        public const string s_SchemaGsdmlPrimitivesNameV02025 = @"GSDML-Primitives-v2.25.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.2</summary>
        public const string s_SchemaGsdmlPrimitivesNameV0202 = @"GSDML-Primitives-v2.2.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.1</summary>
        public const string s_SchemaGsdmlPrimitivesNameV0201 = @"GSDML-Primitives-v2.1.xsd";
        /// <summary>name of the GSDML primitives schema for version 2.0</summary>
        public const string s_SchemaGsdmlPrimitivesNameV0200 = @"GSDML-Primitives-v2.0.xsd";
        /// <summary>name of the GSDML primitives schema for version 1.0</summary>
        public const string s_SchemaGsdmlPrimitivesName = @"GSDML-Primitives-v1.0.xsd";
        /// <summary>name of the common primitives schema</summary>
        public const string s_SchemaCommonPrimitivesName = @"common-primitives-v1.0.xsd";
        /// <summary>name of the XML schema schema</summary>
        public const string s_SchemaXmlSchemaName = @"xml.xsd";
        /// <summary>name of the XML signature schema</summary>
        public const string s_SchemaXmlSignatureName = @"xmldsig-core-schema.xsd";

        #endregion
    }
}


