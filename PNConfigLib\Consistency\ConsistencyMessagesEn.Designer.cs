//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace PNConfigLib.Consistency {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ConsistencyMessagesEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ConsistencyMessagesEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("PNConfigLib.Consistency.ConsistencyMessagesEn", typeof(ConsistencyMessagesEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least one port must be activated on device &apos;{0}&apos;..
        /// </summary>
        internal static string AllPortsDeactivated {
            get {
                return ResourceManager.GetString("AllPortsDeactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: Calculated bandwidth exceeds maximum bandwidth for cyclical IO data for at least one IO cycle..
        /// </summary>
        internal static string CalculatedBandwidthExceededMaximum {
            get {
                return ResourceManager.GetString("CalculatedBandwidthExceededMaximum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: The calculated bandwidth for cyclic IO data exceeds the maximum possible bandwidth for cyclic IO data..
        /// </summary>
        internal static string CalculatedTotalBandwidthExceededMaximum {
            get {
                return ResourceManager.GetString("CalculatedTotalBandwidthExceededMaximum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An isochronous mode interrupt must be configured on the CPU for the IO system &apos;{0}&apos;..
        /// </summary>
        internal static string Clocksync_IOSystemIsNotAssignedToOB6x {
            get {
                return ResourceManager.GetString("Clocksync_IOSystemIsNotAssignedToOB6x", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The max. input address range ({0} bytes) was exceeded. {1} bytes are configured..
        /// </summary>
        internal static string ControllerErrorMaxIAddressWithinAString {
            get {
                return ResourceManager.GetString("ControllerErrorMaxIAddressWithinAString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The max. output address range ({0} bytes) was exceeded. {1} bytes are configured..
        /// </summary>
        internal static string ControllerErrorMaxOAddressWithinAString {
            get {
                return ResourceManager.GetString("ControllerErrorMaxOAddressWithinAString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are {0} RT Provider Communication Relations genereted in the {1} device, but it can only control {2} RT Provider Communication Relations..
        /// </summary>
        internal static string ControllerErrorMaxSharedRTC1Provider {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSharedRTC1Provider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are {0} IRT Consumer Communication Relations genereted in the {1} device, but it can only control {2} Consumer Communication Relations..
        /// </summary>
        internal static string ControllerErrorMaxSharedRTC3Consumer {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSharedRTC3Consumer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are {0} IRT Provider Communication Relations genereted in the {1} interface device but it can only control {2} Provider Communication Relations..
        /// </summary>
        internal static string ControllerErrorMaxSharedRTC3Provider {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSharedRTC3Provider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are {0} Consumer Communication Relations genereted in the {1} device, but it can only control {2} Consumer Communication Relations..
        /// </summary>
        internal static string ControllerErrorMaxSharedRTCXConsumer {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSharedRTCXConsumer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are {0} Provider Communication Relations genereted in the {1} device, but it can only control {2} Provider Communication Relations..
        /// </summary>
        internal static string ControllerErrorMaxSharedRTCXProvider {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSharedRTCXProvider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum number of submodules ({0}) was exceeded on the PN interface of an IO controller. {1} submodules too many were configured..
        /// </summary>
        internal static string ControllerErrorMaxSubmoduleCount {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSubmoduleCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum size of a submodule for this IO controller ({0} bytes) was exceeded by {1} ({2} bytes)..
        /// </summary>
        internal static string ControllerErrorMaxSubmoduleDataLength {
            get {
                return ResourceManager.GetString("ControllerErrorMaxSubmoduleDataLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Synchronized IO data is established on the central PN interface &apos;{0}&apos; (for example synchronized IO devices) and the central PN interface is unsynchronized..
        /// </summary>
        internal static string ControllerNotSynchronized {
            get {
                return ResourceManager.GetString("ControllerNotSynchronized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I-devices with a lower-level IO system at the same Ethernet interface cannot support standard machine projects in higher-level and lower-level IO systems..
        /// </summary>
        internal static string CoupledIDeviceOnlyOneIOSystemShouldBeTailored {
            get {
                return ResourceManager.GetString("CoupledIDeviceOnlyOneIOSystemShouldBeTailored", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delay time value {0:f3} ms is not within the value range {1:f3} ms to {2:f3} ms..
        /// </summary>
        internal static string DelayTimeOutOfRange {
            get {
                return ResourceManager.GetString("DelayTimeOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode is enabled on device &apos;{0}&apos; although IRT (high performance) is not set. Change the RT class and reselect the OB6x assignment..
        /// </summary>
        internal static string DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET {
            get {
                return ResourceManager.GetString("DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device replacement without exchangeable medium function cannot be used for device &apos;{0}&apos; without configuration of topology..
        /// </summary>
        internal static string DeviceExchangeWithoutMMC {
            get {
                return ResourceManager.GetString("DeviceExchangeWithoutMMC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The IO device cannot be operated with the firmware version of the IO controller..
        /// </summary>
        internal static string DifferentSlotModels {
            get {
                return ResourceManager.GetString("DifferentSlotModels", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive port is used in ring connection. Please remove the port from the ring..
        /// </summary>
        internal static string DisabledPortUsed {
            get {
                return ResourceManager.GetString("DisabledPortUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems or standard machine projects, IO devices can only be part of an MRP domain if they include only elements of their IO system..
        /// </summary>
        internal static string DomainManagement_OtherIOSystemMemberInMrpDomain {
            get {
                return ResourceManager.GetString("DomainManagement_OtherIOSystemMemberInMrpDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems or standard machine projects, IO devices can only be part of a sync domain if they include only elements of their IO system..
        /// </summary>
        internal static string DomainManagement_OtherIOSystemMemberInSyncDomain {
            get {
                return ResourceManager.GetString("DomainManagement_OtherIOSystemMemberInSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The CPU {0} cannot supply the process values for {1}..
        /// </summary>
        internal static string ERROR_INVALID_TITO_RANGE {
            get {
                return ResourceManager.GetString("ERROR_INVALID_TITO_RANGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The router address differs from router address of {0}..
        /// </summary>
        internal static string ErrorIE_IPRouteAddressDifferent {
            get {
                return ResourceManager.GetString("ErrorIE_IPRouteAddressDifferent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High Performance needs to be activated in order to use Fast Forwarding..
        /// </summary>
        internal static string ExpertModeRequiredForFastForwarding {
            get {
                return ResourceManager.GetString("ExpertModeRequiredForFastForwarding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The submodule/module &apos;{0}&apos; in slot = {1} has an incorrect process image assignment or none at all..
        /// </summary>
        internal static string FalsePIPAssignment {
            get {
                return ResourceManager.GetString("FalsePIPAssignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;Fast forwarding&apos; cannot be used in combination with IPv6 devices in the same network..
        /// </summary>
        internal static string FastForwardingWithIpv6NotSupported {
            get {
                return ResourceManager.GetString("FastForwardingWithIpv6NotSupported", resourceCulture);
            }
        }
        /// <summary>
        ///   Looks up a localized string similar to &apos;Expert mode fast forwarding &apos; is not possible for the central device..
        /// </summary>

        /// <summary>
        ///   Looks up a localized string similar to The &apos;prioritized startup&apos; option is enabled on IO device {0} although the IO device is not connected to any IO system..
        /// </summary>
        internal static string FastStartupDeviceWithoutIOSystem {
            get {
                return ResourceManager.GetString("FastStartupDeviceWithoutIOSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A maximum of {1} IO devices may be prioritized on IO controller {0}..
        /// </summary>
        internal static string FastStartupMaxCountExceeded {
            get {
                return ResourceManager.GetString("FastStartupMaxCountExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If the IO controller is used as I-device at the same interface for multiple use IO systems or standard machine projects, the PDEV must be assigned centrally..
        /// </summary>
        internal static string IDeviceControllerCentralPdevAssigned {
            get {
                return ResourceManager.GetString("IDeviceControllerCentralPdevAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If the device is configured as an I-device, the setting of the cycle time is only permitted if the I-device was configured without a subordinate IO device and the parameters are assigned by the higher-level IO controller..
        /// </summary>
        internal static string IDeviceSendclockNotValid {
            get {
                return ResourceManager.GetString("IDeviceSendclockNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} belongs to a ring structure without MRP settings..
        /// </summary>
        internal static string IfWithAdditionalRedundancy {
            get {
                return ResourceManager.GetString("IfWithAdditionalRedundancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ratio of update time to send clock must be 1 if the send clock is shorter than 250 µs..
        /// </summary>
        internal static string IncorrectReductionRatioWithSmallSC {
            get {
                return ResourceManager.GetString("IncorrectReductionRatioWithSmallSC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The gross amount of input data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes..
        /// </summary>
        internal static string InputGrossFrameLengthExceeded {
            get {
                return ResourceManager.GetString("InputGrossFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The net amount of input data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes..
        /// </summary>
        internal static string InputNetFrameLengthExceeded {
            get {
                return ResourceManager.GetString("InputNetFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum permitted input data volume has been exceeded for the isochronous mode..
        /// </summary>
        internal static string InputPipSizeAgainstController {
            get {
                return ResourceManager.GetString("InputPipSizeAgainstController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port &apos;{0}&apos; is interconnected but disabled..
        /// </summary>
        internal static string InterconnectedPortDeactivated {
            get {
                return ResourceManager.GetString("InterconnectedPortDeactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems, device replacement without exchangeable medium must be set on the IO controller..
        /// </summary>
        internal static string InterfaceOptions_DeviceReplacementWithoutExchangableMedium {
            get {
                return ResourceManager.GetString("InterfaceOptions_DeviceReplacementWithoutExchangableMedium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least one port must be activated on device &apos;{0}&apos;..
        /// </summary>
        internal static string InterfaceSubmodulePortsDeactivated {
            get {
                return ResourceManager.GetString("InterfaceSubmodulePortsDeactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The number of the IO system must be between {0} and {1}..
        /// </summary>
        internal static string InvalidIOSystemNumber {
            get {
                return ResourceManager.GetString("InvalidIOSystemNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The watchdog factor &apos;{0}&apos; cannot be used..
        /// </summary>
        internal static string InvalidWatchdogFactor {
            get {
                return ResourceManager.GetString("InvalidWatchdogFactor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum {0} address range ({1} bytes) was exceeded. {2} bytes are configured..
        /// </summary>
        internal static string IOAddressDataExceeded {
            get {
                return ResourceManager.GetString("IOAddressDataExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} type IO address scheduling is not possible..
        /// </summary>
        internal static string IoAddressScheduleIsNotPossible {
            get {
                return ResourceManager.GetString("IoAddressScheduleIsNotPossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected IO device requires the IO controller to support advanced PROFINET diagnostics. The configured IO controller, however, does not support advanced PROFINET diagnostics..
        /// </summary>
        internal static string IOControllerDoesNotSupportIODevice {
            get {
                return ResourceManager.GetString("IOControllerDoesNotSupportIODevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is not assigned to an IO controller..
        /// </summary>
        internal static string IoDNotAssignedToController {
            get {
                return ResourceManager.GetString("IoDNotAssignedToController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A standard machine project should contain an IO controller &apos;{0}&apos; with activated optional IO device..
        /// </summary>
        internal static string IOSystem_MachineTailoringEnabled_IOC {
            get {
                return ResourceManager.GetString("IOSystem_MachineTailoringEnabled_IOC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The option &apos;IP address via other method&apos; should not be set if a PNIO system is used on this interface..
        /// </summary>
        internal static string IoSystemPNIpConfigIPAddressViaOtherPath {
            get {
                return ResourceManager.GetString("IoSystemPNIpConfigIPAddressViaOtherPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The network part of the IO device&apos;s IP address {0} is different to the network part of the IO controller..
        /// </summary>
        internal static string IPAddressDifferentSubnet {
            get {
                return ResourceManager.GetString("IPAddressDifferentSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The IP address {0} of interface {1} is located in a permitted subnet range..
        /// </summary>
        internal static string IPAddressRestrictedSubnet {
            get {
                return ResourceManager.GetString("IPAddressRestrictedSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} with subnet mask {1} of interface {2} is invalid (this is a broadcast address)..
        /// </summary>
        internal static string IPBroadcastAddress {
            get {
                return ResourceManager.GetString("IPBroadcastAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} with subnet mask {1} of interface {2} is invalid (this  is a default router address)..
        /// </summary>
        internal static string IPDefaultRouteAddress {
            get {
                return ResourceManager.GetString("IPDefaultRouteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address of interface {0} may not be empty..
        /// </summary>
        internal static string IPEmptyAddress {
            get {
                return ResourceManager.GetString("IPEmptyAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} with subnet mask {1} of interface {2} is invalid (the host address is 0)..
        /// </summary>
        internal static string IPHostAddressNull {
            get {
                return ResourceManager.GetString("IPHostAddressNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} of interface {1} is invalid (it is a loopback address)..
        /// </summary>
        internal static string IPLoopbackAddress {
            get {
                return ResourceManager.GetString("IPLoopbackAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address {0} of the interface {1} is not unique. The device {2}[{3}] has the same address..
        /// </summary>
        internal static string IPMultipleAddress {
            get {
                return ResourceManager.GetString("IPMultipleAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} with subnet mask {1} of interface {2} is invalid..
        /// </summary>
        internal static string IPRouterAddressClassInvalid {
            get {
                return ResourceManager.GetString("IPRouterAddressClassInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} of interface {1} is too long..
        /// </summary>
        internal static string IPRouterAddressTooLong {
            get {
                return ResourceManager.GetString("IPRouterAddressTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} with subnet mask {1} of interface {2} is invalid (this is a broadcast address)..
        /// </summary>
        internal static string IPRouterBroadcastAddress {
            get {
                return ResourceManager.GetString("IPRouterBroadcastAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} with subnet mask {1} of interface {2} is invalid (this is a default network address)..
        /// </summary>
        internal static string IPRouterDefaultRouteAddress {
            get {
                return ResourceManager.GetString("IPRouterDefaultRouteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address of interface {0} may not be empty..
        /// </summary>
        internal static string IPRouterEmptyAddress {
            get {
                return ResourceManager.GetString("IPRouterEmptyAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} of interface {1} is invalid (it is a loopback address)..
        /// </summary>
        internal static string IPRouterLoopbackAddress {
            get {
                return ResourceManager.GetString("IPRouterLoopbackAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} of interface {1} is invalid..
        /// </summary>
        internal static string IPRouterWrongAddress {
            get {
                return ResourceManager.GetString("IPRouterWrongAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} of interface {1} is too long..
        /// </summary>
        internal static string IPTooLongaddress {
            get {
                return ResourceManager.GetString("IPTooLongaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} with subnet mask {1} of interface {2} is invalid..
        /// </summary>
        internal static string IPUnrestrictedSubnetMaskNotSupported {
            get {
                return ResourceManager.GetString("IPUnrestrictedSubnetMaskNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {0} of interface {1} is invalid..
        /// </summary>
        internal static string IPWrongAddress {
            get {
                return ResourceManager.GetString("IPWrongAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP subnet mask {0} of interface {1} is invalid..
        /// </summary>
        internal static string IPWrongSubnetMask {
            get {
                return ResourceManager.GetString("IPWrongSubnetMask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The interconnected port within an IRT line may not be deactivated..
        /// </summary>
        internal static string Irt_With_Deactivated_Port {
            get {
                return ResourceManager.GetString("Irt_With_Deactivated_Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CACF value of the clock synchronization of the IO system is not valid. Check the value of the application cycle of {0}..
        /// </summary>
        internal static string IsoCacfNotValid {
            get {
                return ResourceManager.GetString("IsoCacfNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode must be enabled for the PN interface because there are submodules operating in isochronous mode..
        /// </summary>
        internal static string ISOCHRONOUS_DISABLED_AT_INTERFACE {
            get {
                return ResourceManager.GetString("ISOCHRONOUS_DISABLED_AT_INTERFACE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode must be enabled on submodule, because the submodule is operating in isochronous mode..
        /// </summary>
        internal static string ISOCHRONOUS_REQUIRED_AT_SUBMODUL {
            get {
                return ResourceManager.GetString("ISOCHRONOUS_REQUIRED_AT_SUBMODUL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no isochronous submodule configured in the device {0} operating in isochronous mode..
        /// </summary>
        internal static string ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE {
            get {
                return ResourceManager.GetString("ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The isochronous PIP {0} may not have modules from {1}..
        /// </summary>
        internal static string IsochronPIPHasModulesFromDifferentMastersystems {
            get {
                return ResourceManager.GetString("IsochronPIPHasModulesFromDifferentMastersystems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The isochronous process image partition PIP {0} must have at least one isochronous IO submodule..
        /// </summary>
        internal static string IsochronPIPRequiresIsochronSubmodule {
            get {
                return ResourceManager.GetString("IsochronPIPRequiresIsochronSubmodule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode is activated on device {0} even though the assigned controller {1} does not support isochronous OB coupling..
        /// </summary>
        internal static string IsoCouplingNotSupported {
            get {
                return ResourceManager.GetString("IsoCouplingNotSupported", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0}.{1} does not support KeepApplicationRelationAtCommunicationError.
        /// </summary>
        internal static string KeepApplicationRelationAtCommunicationErrorNotConfigurable
        {
            get
            {
                return ResourceManager.GetString("KeepApplicationRelationAtCommunicationErrorNotConfigurable", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to There are {0} IRT devices connected to the {1} device but it can only control {2} IRT devices in the current configuration..
        /// </summary>
        internal static string Max_SyncSlaveNumber_Overstepped {
            get {
                return ResourceManager.GetString("Max_SyncSlaveNumber_Overstepped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum number of forwarded frames for &apos;{0}&apos; has been exceeded. The interface of the submodule only supports up to {1} frames for forwarding..
        /// </summary>
        internal static string MaxNoIrFrameDataExceeded {
            get {
                return ResourceManager.GetString("MaxNoIrFrameDataExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The ID value range of the IRT frames by the interface {0} exceeds the maximum value supported by this interface..
        /// </summary>
        internal static string MaxRangeIRFrameIDOutOfRange {
            get {
                return ResourceManager.GetString("MaxRangeIRFrameIDOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device number {0} exceeds the max. possible device number {1}..
        /// </summary>
        internal static string MaxStationNumberExceeded {
            get {
                return ResourceManager.GetString("MaxStationNumberExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The watchdog time on IO device {0} must not exceed {1} ms..
        /// </summary>
        internal static string MaxWatchdog {
            get {
                return ResourceManager.GetString("MaxWatchdog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prohibited mixed operation by devices with IRT option
        ///high flexibility and IRT option high performance
        ///in sync domain {0} on subnet
        ///{1}..
        /// </summary>
        internal static string MixedOperation_IRTTop_IRTFlex_at_SyncDomain {
            get {
                return ResourceManager.GetString("MixedOperation_IRTTop_IRTFlex_at_SyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Module &apos;{0}&apos; requries at least one submodule..
        /// </summary>
        internal static string ModuleWithoutSubmodule {
            get {
                return ResourceManager.GetString("ModuleWithoutSubmodule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ring port &apos;{0}&apos; is connected to port &apos;{1}&apos;, which is not a ring port..
        /// </summary>
        internal static string Mrp_PortInterconn_NonRingPort {
            get {
                return ResourceManager.GetString("Mrp_PortInterconn_NonRingPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instances &apos;{0}&apos; and &apos;{1}&apos; from &apos;{2}&apos; cannot belong to the same domain &apos;{3}&apos;..
        /// </summary>
        internal static string MrpDomainUsedInDifferentInstances {
            get {
                return ResourceManager.GetString("MrpDomainUsedInDifferentInstances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both media redundancy and &apos;prioritized startup&apos; are enabled on module &apos;{0}&apos;. This combination can result in longer startup times..
        /// </summary>
        internal static string MrpFastStartupNotAllowed {
            get {
                return ResourceManager.GetString("MrpFastStartupNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MRP domain cannot contain devices with the &apos;Manager&apos; role and &apos;Manager(auto)&apos; role at the same time..
        /// </summary>
        internal static string MrpManagerAndManagerAuto {
            get {
                return ResourceManager.GetString("MrpManagerAndManagerAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum permitted number of interfaces in the ring is exceeded in the MRP domain..
        /// </summary>
        internal static string MrpMaxSizeExceeded {
            get {
                return ResourceManager.GetString("MrpMaxSizeExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MRP domain can only contain one device with the &apos;Manager&apos; role..
        /// </summary>
        internal static string MrpMoreThanOneManager {
            get {
                return ResourceManager.GetString("MrpMoreThanOneManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More than one MRP ring is fully or partially configured in the MRP domain {0}. This is not supported..
        /// </summary>
        internal static string MrpMoreThanOneRing {
            get {
                return ResourceManager.GetString("MrpMoreThanOneRing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A manager must be configured in an MRP domain..
        /// </summary>
        internal static string MrpNoManager {
            get {
                return ResourceManager.GetString("MrpNoManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device &apos;{0}&apos; must be connected to a network for MRP..
        /// </summary>
        internal static string MrpNoNetwork {
            get {
                return ResourceManager.GetString("MrpNoNetwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The &apos;End of topology discovery&apos; option is set for ring port &apos;{0}&apos;. This is not permitted for MRP managers..
        /// </summary>
        internal static string MrpPortEndOfTopologyDiscovery {
            get {
                return ResourceManager.GetString("MrpPortEndOfTopologyDiscovery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ring ports &apos;{0}&apos; and &apos;{1}&apos; are interconnected and are located in different subnets.
        /// </summary>
        internal static string MrpPortInterconnDifferentSubnet {
            get {
                return ResourceManager.GetString("MrpPortInterconnDifferentSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ring port &apos;{0}&apos; is connected to port &apos;{1}&apos;, which is not a ring port..
        /// </summary>
        internal static string MrpPortInterconnNonRingPort {
            get {
                return ResourceManager.GetString("MrpPortInterconnNonRingPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port &apos;{0}&apos; is a non-configurable default ring port. It must be selected as one of the ring ports..
        /// </summary>
        internal static string MrpPortShouldBeSelected {
            get {
                return ResourceManager.GetString("MrpPortShouldBeSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make sure that in active MRP mode, the interconnected ring port {0} is configured with the automatic setting or at least 100 Mbps full duplex..
        /// </summary>
        internal static string MrpPortTransferRateNotAllowed {
            get {
                return ResourceManager.GetString("MrpPortTransferRateNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ring port &apos;{0}&apos; is used in more than one instance.
        /// </summary>
        internal static string MrpRingPortUsedInDifferentInstances {
            get {
                return ResourceManager.GetString("MrpRingPortUsedInDifferentInstances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected MRP role &apos;{0}&apos; is not supported in simple MRP operation &apos;{1}&apos;..
        /// </summary>
        internal static string MrpRoleIsNotSupported {
            get {
                return ResourceManager.GetString("MrpRoleIsNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is not part of the ring, but the MRP role is configured..
        /// </summary>
        internal static string MrpRoleOutsideTheRing {
            get {
                return ResourceManager.GetString("MrpRoleOutsideTheRing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Synchronization and media redundancy are enabled on module &apos;{0}&apos;. This is not permitted..
        /// </summary>
        internal static string MrpSyncNotAllowed {
            get {
                return ResourceManager.GetString("MrpSyncNotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In &apos;{0}&apos;, exactly 2 ports must be selected as ring ports..
        /// </summary>
        internal static string MrpWrongPortNumber {
            get {
                return ResourceManager.GetString("MrpWrongPortNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A multiple use IO system or standard machine project may not include a shared device..
        /// </summary>
        internal static string MultiDeployable_NoSharedDevice {
            get {
                return ResourceManager.GetString("MultiDeployable_NoSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected multiple MRP role &apos;{0}&apos; in instance &apos;{1}&apos; from &apos;{2}&apos; is not supported in multi-MRP operation..
        /// </summary>
        internal static string MultipleMrpRoleNotSupported {
            get {
                return ResourceManager.GetString("MultipleMrpRoleNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The controller {0} does not have a mode for normal startup of the device {1}..
        /// </summary>
        internal static string NoCommonStartupFound {
            get {
                return ResourceManager.GetString("NoCommonStartupFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No protocol selected for Ethernet interface {0}..
        /// </summary>
        internal static string NodeIeNoProtocol {
            get {
                return ResourceManager.GetString("NodeIeNoProtocol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP address {1} and default router IP address {2} of interface {0} are in different subnets. (Subnet mask: {3}).
        /// </summary>
        internal static string NodeIpRouterOtherThanIpAddress {
            get {
                return ResourceManager.GetString("NodeIpRouterOtherThanIpAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The converted name of the device in network {0} could not be generated..
        /// </summary>
        internal static string NodePNNoSInvalid {
            get {
                return ResourceManager.GetString("NodePNNoSInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PROFINET device name {0} is not unique..
        /// </summary>
        internal static string NodePNNoSNotUnique {
            get {
                return ResourceManager.GetString("NodePNNoSNotUnique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The &apos;PROFINET device name is set directly at the device&apos; option must be used along with the &apos;IP address is set directly at the device&apos; option..
        /// </summary>
        internal static string NoSActiveButIPSuiteNotActive {
            get {
                return ResourceManager.GetString("NoSActiveButIPSuiteNotActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If the &apos;PROFINET device name is set directly at the device&apos; option is enabled, IRTtop data exchange cannot be established on this interface..
        /// </summary>
        internal static string NoSViaOtherPathActiveWithIRTTop {
            get {
                return ResourceManager.GetString("NoSViaOtherPathActiveWithIRTTop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No update time can be calculated for IO device &apos;{0}&apos;..
        /// </summary>
        internal static string NoUpdateTime {
            get {
                return ResourceManager.GetString("NoUpdateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The value set on OB {0}!d! for the controller application cycle = {1} µs is too high. The value is limited by /PROFIdrive/ to a maximum of 32000 µs..
        /// </summary>
        internal static string ObControllerAppCycleTooHigh {
            get {
                return ResourceManager.GetString("ObControllerAppCycleTooHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MRP roles from {0} cannot be &apos;Manager&apos; or &apos;Manager (auto)&apos;, if the device is being used optionally..
        /// </summary>
        internal static string Optional_Device_Incompatible_Mrp_Role {
            get {
                return ResourceManager.GetString("Optional_Device_Incompatible_Mrp_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} cannot be a sync master if the device is optional..
        /// </summary>
        internal static string Optional_Device_Incompatible_Sync_Role {
            get {
                return ResourceManager.GetString("Optional_Device_Incompatible_Sync_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port {0} cannot be interconnected with a tool changer port if the device is optional..
        /// </summary>
        internal static string Optional_Device_Interconnected_With_Tool_Changer_Port {
            get {
                return ResourceManager.GetString("Optional_Device_Interconnected_With_Tool_Changer_Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} has to support the PDEV model and its PDEV has to be assigned to the same controller because it is interconnected with an optional device..
        /// </summary>
        internal static string Optional_Device_Iod_Pdev_Assigned_Iocontroller {
            get {
                return ResourceManager.GetString("Optional_Device_Iod_Pdev_Assigned_Iocontroller", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port {0} is contained in an invalid MAU type with topology..
        /// </summary>
        internal static string Optional_Device_Mau_Type_Warning {
            get {
                return ResourceManager.GetString("Optional_Device_Mau_Type_Warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The optional device {0} can be used for a maximum of 2 fixed port connections..
        /// </summary>
        internal static string Optional_Device_Max_Fixed_Peer_Limit_Exceeded {
            get {
                return ResourceManager.GetString("Optional_Device_Max_Fixed_Peer_Limit_Exceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The optional device {0} can either have 2 or 0 interconnected ringports..
        /// </summary>
        internal static string Optional_Device_Mrp_Ring_Port_Error {
            get {
                return ResourceManager.GetString("Optional_Device_Mrp_Ring_Port_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {1} cannot be set for port {0} because it belongs to an optional device..
        /// </summary>
        internal static string Optional_Device_With_Set_Boundaries {
            get {
                return ResourceManager.GetString("Optional_Device_With_Set_Boundaries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port {0} cannot be a tool changer port if the device is optional..
        /// </summary>
        internal static string Optional_Device_With_Tool_Changer_Port {
            get {
                return ResourceManager.GetString("Optional_Device_With_Tool_Changer_Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is an I-device and an optional IO device, which means it must have a higher-level IO system..
        /// </summary>
        internal static string Optional_IDevice_With_No_SuperOrdinate {
            get {
                return ResourceManager.GetString("Optional_IDevice_With_No_SuperOrdinate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The gross amount of output data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes..
        /// </summary>
        internal static string OutputGrossFrameLengthExceeded {
            get {
                return ResourceManager.GetString("OutputGrossFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The net amount of output data of {0} bytes exceeds the maximum permitted amount of data of {1} bytes..
        /// </summary>
        internal static string OutputNetFrameLengthExceeded {
            get {
                return ResourceManager.GetString("OutputNetFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum permitted output data volume has been exceeded for the isochronous mode..
        /// </summary>
        internal static string OutputPipSizeAgainstController {
            get {
                return ResourceManager.GetString("OutputPipSizeAgainstController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems, permission for overwriting of PROFINET device names for IO controllers must be set..
        /// </summary>
        internal static string OverwriteProfinetDeviceName {
            get {
                return ResourceManager.GetString("OverwriteProfinetDeviceName", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to When "Partner port" is set to "Set Partner by user program" or "Optional IO-Device" is activated on a device of an IO-System, then all IO devices in the same IO system be must be parameterizable by the IO controller..
        /// </summary>
        internal static string PdevAssignedToController {
            get {
                return ResourceManager.GetString("PdevAssignedToController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The assignment of the submodules/modules {0} in slot = {1} to the process image partition = {2} is only permitted if isochronous mode is activated on the submodules/modules..
        /// </summary>
        internal static string PIPAssignmentWithIsochronActivated {
            get {
                return ResourceManager.GetString("PIPAssignmentWithIsochronActivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The maximum permitted number of IO devices ({0}) has been exceeded by {1}..
        /// </summary>
        internal static string PNCONTROLLER_ERROR_MAX_DEVICES {
            get {
                return ResourceManager.GetString("PNCONTROLLER_ERROR_MAX_DEVICES", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PROFINET Driver does not support port interconnections with IO device interfaces when prioritized startup is enabled..
        /// </summary>
        internal static string PNDriverPrioStartupNotAllowedError {
            get {
                return ResourceManager.GetString("PNDriverPrioStartupNotAllowedError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} may contain port interconnections to IO device interfaces with enabled prioritized startup. Interconnecting these ports can result in problems during runtime..
        /// </summary>
        internal static string PNDriverPrioStartupNotAllowedWarning {
            get {
                return ResourceManager.GetString("PNDriverPrioStartupNotAllowedWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems, the device name must be obtained by a different service on the IO controller..
        /// </summary>
        internal static string PNIpConfig_DeviceNameUsingDifferentMethod {
            get {
                return ResourceManager.GetString("PNIpConfig_DeviceNameUsingDifferentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems, the IP address must be obtained by a different service on the IO controller..
        /// </summary>
        internal static string PNIpConfig_IPAddressUsingDifferentMethod {
            get {
                return ResourceManager.GetString("PNIpConfig_IPAddressUsingDifferentMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is more than one IRT top island in the sync domain. Possibly missing Port interconnection between Sync Master and Redundant Sync Master..
        /// </summary>
        internal static string PNPlannerMultipleIslands {
            get {
                return ResourceManager.GetString("PNPlannerMultipleIslands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are alternative partners configured on port {1}. No port with IRT and routing may be connected downstream from this port..
        /// </summary>
        internal static string PNPlannerToolChangerError {
            get {
                return ResourceManager.GetString("PNPlannerToolChangerError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;{0}&apos; is a ring port and &apos;Set partner through user program&apos; therefore cannot be selected for the partner..
        /// </summary>
        internal static string Port_ProgrammablePeer_Not_In_Ring {
            get {
                return ResourceManager.GetString("Port_ProgrammablePeer_Not_In_Ring", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;{0}&apos; is selected as &apos;Partner is set by the user program&apos; and for this reason for its interface &apos;{1}&apos; the PDEV has to be assigned to the controller..
        /// </summary>
        internal static string Port_ProgrammablePeer_PdevAssignedToController {
            get {
                return ResourceManager.GetString("Port_ProgrammablePeer_PdevAssignedToController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deactivation is not supported for port {0}..
        /// </summary>
        internal static string PortDeactivationNotValid {
            get {
                return ResourceManager.GetString("PortDeactivationNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The autonegotiation option setting must be identical on port {0} and port {1}..
        /// </summary>
        internal static string PortDifferentAutoNegotiation {
            get {
                return ResourceManager.GetString("PortDifferentAutoNegotiation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For multiple use IO systems or standard machine projects, all specified partner ports are limited to partner ports of their own IO system..
        /// </summary>
        internal static string PortInterconnection_PartnerPortFromSameIoSystem {
            get {
                return ResourceManager.GetString("PortInterconnection_PartnerPortFromSameIoSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO device {0} cannot be topologically reached by IO controller {1}. All devices in a standard machine project must be topologically accessible..
        /// </summary>
        internal static string PortInterconnection_TopologyReachableFromController {
            get {
                return ResourceManager.GetString("PortInterconnection_TopologyReachableFromController", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The signal runtime for port &apos;{0}&apos; cannot be 0..
        /// </summary>
        internal static string PortIrtLineLengthNotSpecified {
            get {
                return ResourceManager.GetString("PortIrtLineLengthNotSpecified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If prioritized startup is enabled, it is a good idea to enter a specific transfer rate / Duplex and to disable autonegotiation at port {0}/{1}..
        /// </summary>
        internal static string PortMauNotSetOrAutonegotiationActivated {
            get {
                return ResourceManager.GetString("PortMauNotSetOrAutonegotiationActivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The &apos;end of synchronization domain&apos; option must not be enabled on port {0}..
        /// </summary>
        internal static string PortMayNotBeBoundaryError {
            get {
                return ResourceManager.GetString("PortMayNotBeBoundaryError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The attachment type of the local port {0} is different from the attachment type of the partner port {1}..
        /// </summary>
        internal static string PortMediumDifferent {
            get {
                return ResourceManager.GetString("PortMediumDifferent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The same transmission rate/duplex must be set on port {0} and port {1}..
        /// </summary>
        internal static string PortTransferRateIsDifferent {
            get {
                return ResourceManager.GetString("PortTransferRateIsDifferent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The same transmission rate/duplex setting should be made on port {0} and port {1}..
        /// </summary>
        internal static string PortTransferRateIsDifferentError {
            get {
                return ResourceManager.GetString("PortTransferRateIsDifferentError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The transmission rate value is not supported in Port {0}.
        /// </summary>
        internal static string PortTransferRateNotSupported {
            get {
                return ResourceManager.GetString("PortTransferRateNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If the transmission rate of the device is automatic and the device is not fiber optic, then the value of the autonegotiation option at port {0} can not be set to false..
        /// </summary>
        internal static string PortWrongValueForAutoNegotiation {
            get {
                return ResourceManager.GetString("PortWrongValueForAutoNegotiation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port {0} is deactivated even though &apos;Set partner through user program&apos; was selected. Activate the port if you want to use the option..
        /// </summary>
        internal static string ProgrammablePeerPortDeactivated {
            get {
                return ResourceManager.GetString("ProgrammablePeerPortDeactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is configured in a ring network, but its selected ring ports don&apos;t match with the ring ports of the ring network. This is not allowed..
        /// </summary>
        internal static string RingWithMrpSelectedPortMismatch {
            get {
                return ResourceManager.GetString("RingWithMrpSelectedPortMismatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is configured in a ring network, but the role &apos;Not device in the ring&apos; is selected in its media redundancy settings. This is not allowed..
        /// </summary>
        internal static string RingWithoutActiveMrp {
            get {
                return ResourceManager.GetString("RingWithoutActiveMrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is configured in a ring network but it does not support any media redundancy protocol. This is not allowed..
        /// </summary>
        internal static string RingWithoutMediaRedundancy {
            get {
                return ResourceManager.GetString("RingWithoutMediaRedundancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} belongs to a ring structure that might not be allowed..
        /// </summary>
        internal static string RingWithUnknownMediaRedundancy {
            get {
                return ResourceManager.GetString("RingWithUnknownMediaRedundancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default router IP address {0} must differ from the IP address of interface {1}..
        /// </summary>
        internal static string RouterIpDifferentFromIeIp {
            get {
                return ResourceManager.GetString("RouterIpDifferentFromIeIp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected send clock is invalid in the sync domain..
        /// </summary>
        internal static string SendclockInvalidInSyncDomain {
            get {
                return ResourceManager.GetString("SendclockInvalidInSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If the IO controller is synchronized, the IO controller must support the send clock of the synchronization domain..
        /// </summary>
        internal static string SendclockNotApplicable {
            get {
                return ResourceManager.GetString("SendclockNotApplicable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The setting &apos;{0}&apos; ms is not possible for the send clock at IO controller &apos;{1}&apos;..
        /// </summary>
        internal static string SendclockNotValid {
            get {
                return ResourceManager.GetString("SendclockNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode is not possible on module {0} because access is set to &apos;not assigned&apos; for the {1}\{2}..
        /// </summary>
        internal static string SharedDevice_IsoModuleNotPossibleWithSharedIF {
            get {
                return ResourceManager.GetString("SharedDevice_IsoModuleNotPossibleWithSharedIF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode is not possible on module {0} because access is set to &apos;not assigned&apos;..
        /// </summary>
        internal static string SharedDevice_IsoNotPossibleWithSharedModule {
            get {
                return ResourceManager.GetString("SharedDevice_IsoNotPossibleWithSharedModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least one submodule in the shared device &apos;{0}&apos; in subsystem &apos;{1}&apos; must be assigned to the IO controller..
        /// </summary>
        internal static string SharedDeviceAllSubmodulesShared {
            get {
                return ResourceManager.GetString("SharedDeviceAllSubmodulesShared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IRT is not possible on device {0} because access is set to &apos;not assigned&apos; for the interface {1}..
        /// </summary>
        internal static string SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned {
            get {
                return ResourceManager.GetString("SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isochronous mode is not possible on device {0} because access is set to &apos;not assigned&apos; for the interface {1}..
        /// </summary>
        internal static string SharedDeviceIsoNotPossibleWithSharedIF {
            get {
                return ResourceManager.GetString("SharedDeviceIsoNotPossibleWithSharedIF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prioritized startup is not possible with IO device &apos;{0}&apos; because the IO controller &apos;{1}&apos; does not have full access to the PROFINET interface..
        /// </summary>
        internal static string SharedDevicePrioritizedStartupNotPossible {
            get {
                return ResourceManager.GetString("SharedDevicePrioritizedStartupNotPossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device {0} was configured as shared device. Use of the send clock {1} ms is not possible..
        /// </summary>
        internal static string SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules {
            get {
                return ResourceManager.GetString("SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum value for the signal delay is {0} µs!.
        /// </summary>
        internal static string SignalDelayTimeLessThanMinValue {
            get {
                return ResourceManager.GetString("SignalDelayTimeLessThanMinValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum value for the signal delay is {1} µs for &apos;{0}&apos;!.
        /// </summary>
        internal static string SignalDelayTimeMoreThanMaxValue {
            get {
                return ResourceManager.GetString("SignalDelayTimeMoreThanMaxValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device number cannot be less than {0}, but the value {1} is used in IO system {2}..
        /// </summary>
        internal static string StationNumberDownLimit {
            get {
                return ResourceManager.GetString("StationNumberDownLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device number {0} is already used in the IO system {1}. The next available number is {2}..
        /// </summary>
        internal static string StationNumberDuplication {
            get {
                return ResourceManager.GetString("StationNumberDuplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device number {0} is already used in the IO system {1}..
        /// </summary>
        internal static string StationNumberDuplicationAndNotAvailablePosition {
            get {
                return ResourceManager.GetString("StationNumberDuplicationAndNotAvailablePosition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subnet name {0} is not unique..
        /// </summary>
        internal static string SubnetDuplicateName {
            get {
                return ResourceManager.GetString("SubnetDuplicateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name of the subnet is empty..
        /// </summary>
        internal static string SubnetEmptyName {
            get {
                return ResourceManager.GetString("SubnetEmptyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subnet Mask should be same for the devices in the same IO-System. Subnet mask &apos;{0}&apos; for decentral device &apos;{1}&apos; does not match Subnet mask &apos;{2}&apos; for central device &apos;{3}&apos; in IO-System &apos;{4}&apos;.
        /// </summary>
        internal static string SubnetMaskDifferenceInIOSystem {
            get {
                return ResourceManager.GetString("SubnetMaskDifferenceInIOSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subnet name is too long. Only 24 characters are allowed..
        /// </summary>
        internal static string SubnetNameTooLong {
            get {
                return ResourceManager.GetString("SubnetNameTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected bandwidth use {0} exceeds the maximum possible bandwidth use {1}, for the sync domain &apos;{2}&apos;..
        /// </summary>
        internal static string SyncDomainBwLevelExceeded {
            get {
                return ResourceManager.GetString("SyncDomainBwLevelExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The sync domain must be given a name..
        /// </summary>
        internal static string SyncDomainEmptyName {
            get {
                return ResourceManager.GetString("SyncDomainEmptyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculated IRT bandwidth exceeds the maximum bandwidth permitted by the sync domain..
        /// </summary>
        internal static string SyncDomainMaxRedBwExceeded {
            get {
                return ResourceManager.GetString("SyncDomainMaxRedBwExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncDomain names must be unique in subnet {0}..
        /// </summary>
        internal static string SyncDomainNameNotUnique {
            get {
                return ResourceManager.GetString("SyncDomainNameNotUnique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The name of a sync domain cannot be longer than 64 characters..
        /// </summary>
        internal static string SyncDomainNameTooLong {
            get {
                return ResourceManager.GetString("SyncDomainNameTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot find an operating mode for normal startup for the synchronized interfaces of sync domain &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainNoCommonStartupMode {
            get {
                return ResourceManager.GetString("SyncDomainNoCommonStartupMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Topology error. There is no connection possibility between the sync master {0} and the sync slave {1}..
        /// </summary>
        internal static string SyncDomainNoConnection {
            get {
                return ResourceManager.GetString("SyncDomainNoConnection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} only supports fragmentation if the sync domain {1} does not include RT devices, but {2} is an RT device..
        /// </summary>
        internal static string SyncDomainNoRtcyFragModeWithRtcyDeviceExists {
            get {
                return ResourceManager.GetString("SyncDomainNoRtcyFragModeWithRtcyDeviceExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A redundant sync master is not permitted in an IRT Flex domain..
        /// </summary>
        internal static string SyncDomainNoSecSyncMasterWithIrtFlex {
            get {
                return ResourceManager.GetString("SyncDomainNoSecSyncMasterWithIrtFlex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One sync master must be defined in synchronization domain &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainNoSyncMaster {
            get {
                return ResourceManager.GetString("SyncDomainNoSyncMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In sync domain &apos;{0}&apos; there is a prohibited port interconnection with device &apos;{1}&apos; which is outside of the limits of the sync domain &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainNotAllowedPortInterconnection {
            get {
                return ResourceManager.GetString("SyncDomainNotAllowedPortInterconnection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {1} only supports fragmentation if there is one active port, but more than one port is active..
        /// </summary>
        internal static string SyncDomainOnePortFragModeWithMultiplePortsActive {
            get {
                return ResourceManager.GetString("SyncDomainOnePortFragModeWithMultiplePortsActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}: The calculated IRT bandwidth exceeds the reserved IRT bandwidth..
        /// </summary>
        internal static string SyncDomainResBandwidthExceeded {
            get {
                return ResourceManager.GetString("SyncDomainResBandwidthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only one secondary sync master may be defined in synchronization domain &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainSecondarySyncMasterCount {
            get {
                return ResourceManager.GetString("SyncDomainSecondarySyncMasterCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sync domain does not currently support send clocks less than 250 µs..
        /// </summary>
        internal static string SyncDomainSmallSendClocksNotSupported {
            get {
                return ResourceManager.GetString("SyncDomainSmallSendClocksNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only one sync master may be defined in synchronization domain &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainSyncMasterCount {
            get {
                return ResourceManager.GetString("SyncDomainSyncMasterCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The current IRT top island does not include the sync master: &apos;{0}&apos;..
        /// </summary>
        internal static string SyncDomainSyncMasterIsNotComprised {
            get {
                return ResourceManager.GetString("SyncDomainSyncMasterIsNotComprised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Topology error. A maximum of two sync slaves are allowed between the primary sync master &apos;{0}&apos; and the secondary sync master &apos;{1}&apos;..
        /// </summary>
        internal static string SyncMasterInvalidDistance {
            get {
                return ResourceManager.GetString("SyncMasterInvalidDistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The configured send clock = {1} µs is less than the required time {0} µs!.
        /// </summary>
        internal static string TdcAgainstTdcminCheck {
            get {
                return ResourceManager.GetString("TdcAgainstTdcminCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The IO device {0} cannot supply the process values with the update time of {1} ms.
        /// </summary>
        internal static string TiToCannotBeCalculated {
            get {
                return ResourceManager.GetString("TiToCannotBeCalculated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified Ti/To value is greater than the application cycle value.
        /// </summary>
        internal static string TiToGreaterThanAppCycle {
            get {
                return ResourceManager.GetString("TiToGreaterThanAppCycle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The input / output length, including user data qualifier is {0} bytes and exceeds the maximum permitted data length of {1} bytes..
        /// </summary>
        internal static string TotalGrossFrameLengthExceeded {
            get {
                return ResourceManager.GetString("TotalGrossFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The input / output length, excluding user data qualifier is {0} bytes and exceeds the maximum permitted data length of {1} bytes..
        /// </summary>
        internal static string TotalNetFrameLengthExceeded {
            get {
                return ResourceManager.GetString("TotalNetFrameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On IO device {0}, the update time has changed from {1} ms to {2} ms..
        /// </summary>
        internal static string UpdateTimeChangedAutomatically {
            get {
                return ResourceManager.GetString("UpdateTimeChangedAutomatically", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO device &apos;{0}&apos; cannot be operated with the set update time..
        /// </summary>
        internal static string UpdateTimeNotApplicable {
            get {
                return ResourceManager.GetString("UpdateTimeNotApplicable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The configured update time must match the send clock (isochronous mode enabled)..
        /// </summary>
        internal static string UpdateTimeNotEqualSendClock {
            get {
                return ResourceManager.GetString("UpdateTimeNotEqualSendClock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified Ti value is invalid..
        /// </summary>
        internal static string ValidationErrorInvalidTi {
            get {
                return ResourceManager.GetString("ValidationErrorInvalidTi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The specified To value is invalid..
        /// </summary>
        internal static string ValidationErrorInvalidTo {
            get {
                return ResourceManager.GetString("ValidationErrorInvalidTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Ti value ({0}) is invalid, as it must be a multiple of the interval ({1}).
        /// </summary>
        internal static string ValidationErrorTiNotMultiplesOfRaster {
            get {
                return ResourceManager.GetString("ValidationErrorTiNotMultiplesOfRaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected bandwidth use {0} is only supported in high performance mode for the sync domain &apos;{1}&apos;..
        /// </summary>
        internal static string ValidationExpertModeSupportedBwLevel {
            get {
                return ResourceManager.GetString("ValidationExpertModeSupportedBwLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected send clock pulse is only supported when the &apos;high performance&apos; option is enabled..
        /// </summary>
        internal static string ValidationExpertModeSupportedSC {
            get {
                return ResourceManager.GetString("ValidationExpertModeSupportedSC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multiple Use IO System is activated for the IO System : {0} but central device : {1} of this IO System does not support the feature..
        /// </summary>
        internal static string XML_AddressTailoringActivatedButNotSupported {
            get {
                return ResourceManager.GetString("XML_AddressTailoringActivatedButNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device {0} must have at least one virtual submodule..
        /// </summary>
        internal static string XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice {
            get {
                return ResourceManager.GetString("XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both IO device and its submodule has ParameterRecordDataItem field for {0}, one of them can have this field..
        /// </summary>
        internal static string XML_BothIODeviceAndSubmoduleHasParameterRecord {
            get {
                return ResourceManager.GetString("XML_BothIODeviceAndSubmoduleHasParameterRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both module and submodule has ParameterRecordDataItem field for {0}, one of them can have this field..
        /// </summary>
        internal static string XML_BothModuleAndSubmoduleHasParameterRecord {
            get {
                return ResourceManager.GetString("XML_BothModuleAndSubmoduleHasParameterRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Central device {0} is defined more than once in configuration file..
        /// </summary>
        internal static string XML_CentralDeviceIsMoreThanOnceInConfiguration {
            get {
                return ResourceManager.GetString("XML_CentralDeviceIsMoreThanOnceInConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Central device {0} is not used in configuration file..
        /// </summary>
        internal static string XML_CentralDeviceIsNotUsedInConfiguration {
            get {
                return ResourceManager.GetString("XML_CentralDeviceIsNotUsedInConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PNConfigLib doesn&apos;t support interface : {0} for current version..
        /// </summary>
        internal static string XML_CentralDeviceVariantsNotSupported {
            get {
                return ResourceManager.GetString("XML_CentralDeviceVariantsNotSupported", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to PNConfigLib doesn&apos;t device version : {0} ..
        /// </summary>
        internal static string XML_DeviceVersionIsNotValid
        {
            get
            {
                return ResourceManager.GetString("XML_DeviceVersionIsNotValid", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to There is a collision for the physical subslot {0}..
        /// </summary>
        internal static string XML_CollisionForPhysicalSubslot {
            get {
                return ResourceManager.GetString("XML_CollisionForPhysicalSubslot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collision in {0} IO addresses, check IO addresses which have start values {1} and {2} for IO system : {3}.
        /// </summary>
        internal static string XML_CollisionInIOAddress {
            get {
                return ResourceManager.GetString("XML_CollisionInIOAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom interface file does not exist on path {0} for device : {1}.
        /// </summary>
        internal static string XML_CustomInterfaceFileNotExist {
            get {
                return ResourceManager.GetString("XML_CustomInterfaceFileNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interface type is not selected as Custom but CustomInterfacePath is also set. CustomInterfacePath can be used only with Custom interface type so it is ignored..
        /// </summary>
        internal static string XML_CustomInterfacePathNotNull {
            get {
                return ResourceManager.GetString("XML_CustomInterfacePathNotNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interface type is selected as Custom but CustomInterfacePath is not set for device {0}.
        /// </summary>
        internal static string XML_CustomInterfacePathNull {
            get {
                return ResourceManager.GetString("XML_CustomInterfacePathNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decentral device {0} is defined more than once in configuration file..
        /// </summary>
        internal static string XML_DecentralDeviceIsMoreThanOnceInConfiguration {
            get {
                return ResourceManager.GetString("XML_DecentralDeviceIsMoreThanOnceInConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decentral device {0} is not used in configuration file..
        /// </summary>
        internal static string XML_DecentralDeviceIsNotUsedInConfiguration {
            get {
                return ResourceManager.GetString("XML_DecentralDeviceIsNotUsedInConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not create default IO systems for multiple central devices..
        /// </summary>
        internal static string XML_DefaultIOSystemForMultipleIOC {
            get {
                return ResourceManager.GetString("XML_DefaultIOSystemForMultipleIOC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device {0} has more shared part than it can support..
        /// </summary>
        internal static string XML_DeviceHasOverCapacityForSharedDevice {
            get {
                return ResourceManager.GetString("XML_DeviceHasOverCapacityForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeviceRefID of central device not found in list of nodes: {0}.
        /// </summary>
        internal static string XML_DeviceRefIDWithoutCentralDevice {
            get {
                return ResourceManager.GetString("XML_DeviceRefIDWithoutCentralDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeviceRefID of decentral device not found in list of nodes: {0}.
        /// </summary>
        internal static string XML_DeviceRefIDWithoutDecentralDevice {
            get {
                return ResourceManager.GetString("XML_DeviceRefIDWithoutDecentralDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeviceVersion of {0} cannot be null..
        /// </summary>
        internal static string XML_DeviceVersionNull {
            get {
                return ResourceManager.GetString("XML_DeviceVersionNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device name {0} set wrongly for the {1}. The converted name is too long and violates DNS restrictions. International domain name (IDN) labels must contain fewer than 64 characters between the two points..
        /// </summary>
        internal static string XML_DnsConvert_PNDeviceNameLengthExceeded {
            get {
                return ResourceManager.GetString("XML_DnsConvert_PNDeviceNameLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No devices connected to any subnet; can not compile empty project..
        /// </summary>
        internal static string XML_EmptyProject {
            get {
                return ResourceManager.GetString("XML_EmptyProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;EndOfDetectionOfAccessibleDevices&apos; port option cannot be set for the port &apos;{0}&apos;, because it does not support this function..
        /// </summary>
        internal static string XML_EndOfDetectionOfAccessibleDevicesIsNotValid {
            get {
                return ResourceManager.GetString("XML_EndOfDetectionOfAccessibleDevicesIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;EndOfTheSyncDomain&apos; port option cannot be set for the port &apos;{0}&apos;, because it does not support this function..
        /// </summary>
        internal static string XML_EndOfTheSyncDomainIsNotValid {
            get {
                return ResourceManager.GetString("XML_EndOfTheSyncDomainIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;EndOfTopologyDiscovery&apos; port option cannot be set for the port &apos;{0}&apos;, because it does not support this function..
        /// </summary>
        internal static string XML_EndOfTopologyDiscoveryIsNotValid {
            get {
                return ResourceManager.GetString("XML_EndOfTopologyDiscoveryIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GSDML cannot be found: {0}..
        /// </summary>
        internal static string XML_GSDMLExistence {
            get {
                return ResourceManager.GetString("XML_GSDMLExistence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In the ListOfNodes, the GSDML {0} is provided from different paths. The gsdml located in the first path will be imported. Others are ignored..
        /// </summary>
        internal static string XML_GSDMLPathUniqueness {
            get {
                return ResourceManager.GetString("XML_GSDMLPathUniqueness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Head module cannot be used more than one, check the device {0}..
        /// </summary>
        internal static string XML_HeadModuleCannotBeUsedMoreThanOne {
            get {
                return ResourceManager.GetString("XML_HeadModuleCannotBeUsedMoreThanOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect GSDRefID &apos;{0}&apos; for decentral device &apos;{1}&apos;.
        /// </summary>
        internal static string XML_IncorrectGSDRefIDDevice {
            get {
                return ResourceManager.GetString("XML_IncorrectGSDRefIDDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect GSDRefID &apos;{0}&apos; for decentral device &apos;{1}&apos; module &apos;{2}&apos;.
        /// </summary>
        internal static string XML_IncorrectGSDRefIDModule {
            get {
                return ResourceManager.GetString("XML_IncorrectGSDRefIDModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect GSDRefID &apos;{0}&apos; for decentral device &apos;{1}&apos; port &apos;{2}&apos;.
        /// </summary>
        internal static string XML_IncorrectGSDRefIDPort {
            get {
                return ResourceManager.GetString("XML_IncorrectGSDRefIDPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect GSDRefID &apos;{0}&apos; for decentral device &apos;{1}&apos; module &apos;{2}&apos; submodule &apos;{3}&apos;.
        /// </summary>
        internal static string XML_IncorrectGSDRefIDSubmodule {
            get {
                return ResourceManager.GetString("XML_IncorrectGSDRefIDSubmodule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect InterfaceRefID in port interconnection: {0}.
        /// </summary>
        internal static string XML_IncorrectInterfaceRefID {
            get {
                return ResourceManager.GetString("XML_IncorrectInterfaceRefID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect ListOfNodesRefID in Configuration xml..
        /// </summary>
        internal static string XML_IncorrectListOfNodesRefIDInConfig {
            get {
                return ResourceManager.GetString("XML_IncorrectListOfNodesRefIDInConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect ListOfNodesRefID in Topology xml..
        /// </summary>
        internal static string XML_IncorrectListOfNodesRefIDInTopology {
            get {
                return ResourceManager.GetString("XML_IncorrectListOfNodesRefIDInTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect TopologyRefID in Configuration xml..
        /// </summary>
        internal static string XML_IncorrectTopologyRefIDInConfig {
            get {
                return ResourceManager.GetString("XML_IncorrectTopologyRefIDInConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An interconnected port located in slot {0} but the module in this slot of device {1} is deleted in the configuration.xml..
        /// </summary>
        internal static string XML_InterconnectedPortOwnerDeleted {
            get {
                return ResourceManager.GetString("XML_InterconnectedPortOwnerDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device {0} cannot use its interface..
        /// </summary>
        internal static string XML_InterfaceCannotBeUsedForSharedDevice {
            get {
                return ResourceManager.GetString("XML_InterfaceCannotBeUsedForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interface {0} does not exist..
        /// </summary>
        internal static string XML_InterfaceDoesNotExistForSharedDevice {
            get {
                return ResourceManager.GetString("XML_InterfaceDoesNotExistForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given interface options are not valid for the device : {0} because it does not have an interface submodule in the gsdml file..
        /// </summary>
        internal static string XML_InterfaceNotSupported_InterfaceOptions {
            get {
                return ResourceManager.GetString("XML_InterfaceNotSupported_InterfaceOptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given media redundancy configuration is not valid for the device : {0} because it does not have an interface submodule in the gsdml file..
        /// </summary>
        internal static string XML_InterfaceNotSupported_MediaRedundancy {
            get {
                return ResourceManager.GetString("XML_InterfaceNotSupported_MediaRedundancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given synchronization role is not valid for the device : {0} because it does not have an interface submodule in the gsdml file..
        /// </summary>
        internal static string XML_InterfaceNotSupported_SyncRole {
            get {
                return ResourceManager.GetString("XML_InterfaceNotSupported_SyncRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} attribute has invalid value type in the catalog file {1} of {2}.
        /// </summary>
        internal static string XML_InvalidCentralDeviceAttributeType {
            get {
                return ResourceManager.GetString("XML_InvalidCentralDeviceAttributeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Central device interface : {0} supports {1} port(s) but configured with {2} port(s)..
        /// </summary>
        internal static string XML_InvalidCentralDevicePortCount {
            get {
                return ResourceManager.GetString("XML_InvalidCentralDevicePortCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device name {0} set wrongly for the {1}. Device name cannot have the form n.n.n.n, n = 0...999.
        /// </summary>
        internal static string XML_InvalidNameIpFormat {
            get {
                return ResourceManager.GetString("XML_InvalidNameIpFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The PROFINET device name {0} set wrongly for the {1}. The first label cannot start with &apos;port-xyz&apos; or &apos;port-xyz-abcde&apos; with a,b,c,d,e, x, y, z = 0...9..
        /// </summary>
        internal static string XML_InvalidNamePort {
            get {
                return ResourceManager.GetString("XML_InvalidNamePort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid port settings for port &apos;{0}&apos;, because it is deactivated..
        /// </summary>
        internal static string XML_InvalidPortSettingsForDeactivatedPort {
            get {
                return ResourceManager.GetString("XML_InvalidPortSettingsForDeactivatedPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncRole is set as {0} but it does not supported for {1}.
        /// </summary>
        internal static string XML_InvalidSyncRole {
            get {
                return ResourceManager.GetString("XML_InvalidSyncRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Module {0} has multiple IO address owner virtual submodules so IO addresses must be defined under the submodule element..
        /// </summary>
        internal static string XML_IOAddressesDefinitionOfMultipleVsm {
            get {
                return ResourceManager.GetString("XML_IOAddressesDefinitionOfMultipleVsm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO address of module {0}/virtual submodule {1} must be defined..
        /// </summary>
        internal static string XML_IOAddressOfVsmNotExist {
            get {
                return ResourceManager.GetString("XML_IOAddressOfVsmNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO address value cannot be bigger than 32767..
        /// </summary>
        internal static string XML_IOAddressRangeExceeded {
            get {
                return ResourceManager.GetString("XML_IOAddressRangeExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} IO address type must be defined for {1}..
        /// </summary>
        internal static string XML_IOAddressTypeConsistency {
            get {
                return ResourceManager.GetString("XML_IOAddressTypeConsistency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IOController {0} does not exist..
        /// </summary>
        internal static string XML_IOControllerDoesNotExistForSharedDevice {
            get {
                return ResourceManager.GetString("XML_IOControllerDoesNotExistForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned IO controllers of shared device {0} have different send clock values which will be adapted from PDEV owner controller if it is possible..
        /// </summary>
        internal static string XML_IOControllersOfIODeviceHasDifferentSendClock {
            get {
                return ResourceManager.GetString("XML_IOControllersOfIODeviceHasDifferentSendClock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The IO device {0} has multiple virtual submodule, therefore, the IO device cannot have parameter record data item..
        /// </summary>
        internal static string XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord {
            get {
                return ResourceManager.GetString("XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock value is not required for device {0}, the value is updated..
        /// </summary>
        internal static string XML_IODeviceSendClockForSharedIsNotRequired {
            get {
                return ResourceManager.GetString("XML_IODeviceSendClockForSharedIsNotRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decentral device &apos;{0}&apos; is not connected to Subnet &apos;{1}&apos; but it is connected to IO System &apos;{2}&apos;.
        /// </summary>
        internal static string XML_IODNotConnectedToSubnetButToIOSystem {
            get {
                return ResourceManager.GetString("XML_IODNotConnectedToSubnetButToIOSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decentral device &apos;{0}&apos; is not connected to Subnet &apos;{1}&apos; but it is connected to IO System &apos;{2}&apos; and Sync Domain &apos;{3}&apos;.
        /// </summary>
        internal static string XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain {
            get {
                return ResourceManager.GetString("XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decentral device &apos;{0}&apos; is not connected to Subnet &apos;{1}&apos; but it is connected to Sync Domain &apos;{2}&apos;.
        /// </summary>
        internal static string XML_IODNotConnectedToSubnetButToSyncDomain {
            get {
                return ResourceManager.GetString("XML_IODNotConnectedToSubnetButToSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddressAssignment is LOCAL in the device {0}&apos;s gsdml file. IPProtocol must be configured as IPAddressIsSetDirectlyAtTheDevice in the configuration xml file..
        /// </summary>
        internal static string XML_IoIpConfigMode {
            get {
                return ResourceManager.GetString("XML_IoIpConfigMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} has IO addresses and must be defined in the configuration..
        /// </summary>
        internal static string XML_IOModuleNotExist {
            get {
                return ResourceManager.GetString("XML_IOModuleNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If an IO device ({0}) has topology connection and not connected to an IOSystem, SyncDomain should be defined..
        /// </summary>
        internal static string XML_IOSystemAndSyncDomainNotDeclaredForIod {
            get {
                return ResourceManager.GetString("XML_IOSystemAndSyncDomainNotDeclaredForIod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IOSystem &apos;{0}&apos; not found for central device: {1} - interface {2}.
        /// </summary>
        internal static string XML_IoSystemNotFounfForIOC {
            get {
                return ResourceManager.GetString("XML_IoSystemNotFounfForIOC", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to IOSystem &apos;{0}&apos; not found for decentral device: {1} - interface {2}.
        /// </summary>
        internal static string XML_IoSystemNotFoundForIOD
        {
            get
            {
                return ResourceManager.GetString("XML_IoSystemNotFoundForIOD", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to IOSystem &apos;{0}&apos; not found for decentral device: {1} - interface {2}.
        /// </summary>
        internal static string XML_IoSystemNotFounfForIOD {
            get {
                return ResourceManager.GetString("XML_IoSystemNotFounfForIOD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IOSystemRefID given without defining a Subnet..
        /// </summary>
        internal static string XML_IOSystemRefIDWithoutSubnet {
            get {
                return ResourceManager.GetString("XML_IOSystemRefIDWithoutSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO system does not have a controller: {0}.
        /// </summary>
        internal static string XML_IoSystemWithoutIOC {
            get {
                return ResourceManager.GetString("XML_IoSystemWithoutIOC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} does not support IP address SetDirectlyAtTheDevice.
        /// </summary>
        internal static string XML_IpSetDirectlyAtTheDeviceNotSupported {
            get {
                return ResourceManager.GetString("XML_IpSetDirectlyAtTheDeviceNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Central device {1}&apos;s interface {0} does not support IRT..
        /// </summary>
        internal static string XML_IRTNotSupportedForIOControllerInterface {
            get {
                return ResourceManager.GetString("XML_IRTNotSupportedForIOControllerInterface", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No topology given in an IRT project..
        /// </summary>
        internal static string XML_IRTProjectWithoutTopology {
            get {
                return ResourceManager.GetString("XML_IRTProjectWithoutTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum 16 IO system are supported, but there are {0} IO systems in subnet: {1}.
        /// </summary>
        internal static string XML_MaxIOSystemNumber {
            get {
                return ResourceManager.GetString("XML_MaxIOSystemNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The module {0} has multiple virtual submodule, therefore, the module cannot have parameter record data item..
        /// </summary>
        internal static string XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord {
            get {
                return ResourceManager.GetString("XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The module {0} must have at least one submodule..
        /// </summary>
        internal static string XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice {
            get {
                return ResourceManager.GetString("XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ModuleID {0} on device {1} is used more than once..
        /// </summary>
        internal static string XML_ModuleIdUsedMoreThanOnce {
            get {
                return ResourceManager.GetString("XML_ModuleIdUsedMoreThanOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The module &apos;{0}&apos; is not a pluggable for decentral device {1}, and module id is {2}..
        /// </summary>
        internal static string XML_ModuleIsNotPluggableForDevice {
            get {
                return ResourceManager.GetString("XML_ModuleIsNotPluggableForDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ModuleRefID {0} given under isochronous submodule not found in the list of modules for device: {1}.
        /// </summary>
        internal static string XML_ModuleRefIDWithoutModuleUnderIsochronousModule {
            get {
                return ResourceManager.GetString("XML_ModuleRefIDWithoutModuleUnderIsochronousModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ModuleRefID {0} given under shared device not found in the list of modules for device: {1}.
        /// </summary>
        internal static string XML_ModuleRefIDWithoutModuleUnderSharedDevice {
            get {
                return ResourceManager.GetString("XML_ModuleRefIDWithoutModuleUnderSharedDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PNConfigLib does not support MRPD feature. {0} is inside an MRP ring and its SyncRole is defined as {1}..
        /// </summary>
        internal static string XML_MrpdNotSupported {
            get {
                return ResourceManager.GetString("XML_MrpdNotSupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MrpDomainRefID: {0} of Device: {1}, couldn&apos;t found in MrpDomains..
        /// </summary>
        internal static string XML_MrpDomainRefIdConsistency {
            get {
                return ResourceManager.GetString("XML_MrpDomainRefIdConsistency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available InstanceNumber value(s) : {0} for the device {1} in mrp ring {2}.
        /// </summary>
        internal static string XML_MrpInstanceNumberConsistency {
            get {
                return ResourceManager.GetString("XML_MrpInstanceNumberConsistency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mrp ring InstanceNumber values must be unique for device : {0}.
        /// </summary>
        internal static string XML_MrpInstanceNumberUniqueness {
            get {
                return ResourceManager.GetString("XML_MrpInstanceNumberUniqueness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device: {0}, has {1} instance(s) but {2} of them defined..
        /// </summary>
        internal static string XML_MrpRingCount {
            get {
                return ResourceManager.GetString("XML_MrpRingCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All the instance numbers must be deifned for the device {0} which have multiple mrp instances.
        /// </summary>
        internal static string XML_MrpRingInstanceNumberIsSpecified {
            get {
                return ResourceManager.GetString("XML_MrpRingInstanceNumberIsSpecified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multiple IO controllers in IO system &apos;{0}&apos;: device: {1} - interface {2}.
        /// </summary>
        internal static string XML_MultipleIOControllersInIOSystem {
            get {
                return ResourceManager.GetString("XML_MultipleIOControllersInIOSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If MultipleUseIOSystem is not activated, SetIPAddressByTheIOController cannot be selected..
        /// </summary>
        internal static string XML_MultipleUseIOSystemIsNotSelected {
            get {
                return ResourceManager.GetString("XML_MultipleUseIOSystemIsNotSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO system number {0} is used more than once for IOSystemID: {1}.
        /// </summary>
        internal static string XML_MultipleUseOfIOSystemNumber {
            get {
                return ResourceManager.GetString("XML_MultipleUseOfIOSystemNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Device RefID {0} in topology does not exist according to listofnodes..
        /// </summary>
        internal static string XML_NonExistingDeviceRefIDInTopology {
            get {
                return ResourceManager.GetString("XML_NonExistingDeviceRefIDInTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Interface RefID {0} in topology does not exist according to listofnodes..
        /// </summary>
        internal static string XML_NonExistingInterfaceRefIDInTopology {
            get {
                return ResourceManager.GetString("XML_NonExistingInterfaceRefIDInTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partner port type must be SetByUserProgram if no partner ports are set. Interface id: {0} - Port number: {1}.
        /// </summary>
        internal static string XML_NoPartnerPortAndItIsNotSetByUserProgram {
            get {
                return ResourceManager.GetString("XML_NoPartnerPortAndItIsNotSetByUserProgram", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration.xml file not found..
        /// </summary>
        internal static string XML_NotExistConfigXML {
            get {
                return ResourceManager.GetString("XML_NotExistConfigXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ListOfNodes.xml file not found..
        /// </summary>
        internal static string XML_NotExistListOfNodesXML {
            get {
                return ResourceManager.GetString("XML_NotExistListOfNodesXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Topology.xml file not found..
        /// </summary>
        internal static string XML_NotExistTopologyXML {
            get {
                return ResourceManager.GetString("XML_NotExistTopologyXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is not an IO typed but IO address is defined..
        /// </summary>
        internal static string XML_NotIOAddressTypedModule {
            get {
                return ResourceManager.GetString("XML_NotIOAddressTypedModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is not a valid XML file. {1}.
        /// </summary>
        internal static string XML_NotValid {
            get {
                return ResourceManager.GetString("XML_NotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port GsdId : {0} of {1} is not valid according to its GSDML file..
        /// </summary>
        internal static string XML_NotValidPortGsdId {
            get {
                return ResourceManager.GetString("XML_NotValidPortGsdId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Port subslot number : {0} of {1}\{2} is not valid according to its GSDML file..
        /// </summary>
        internal static string XML_NotValidPortSubslotNumber {
            get {
                return ResourceManager.GetString("XML_NotValidPortSubslotNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration.xml file path is not specified..
        /// </summary>
        internal static string XML_NullOrEmptyConfigXML {
            get {
                return ResourceManager.GetString("XML_NullOrEmptyConfigXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ListOfNodes.xml file path is not specified..
        /// </summary>
        internal static string XML_NullOrEmptyListOfNodesXML {
            get {
                return ResourceManager.GetString("XML_NullOrEmptyListOfNodesXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of ports are more than allowed for the device: {0}..
        /// </summary>
        internal static string XML_NumberOfPortsExceeded {
            get {
                return ResourceManager.GetString("XML_NumberOfPortsExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given interface options are not valid for the device : {0} because parametrization disallowed in the gsdml file..
        /// </summary>
        internal static string XML_ParameterizationDisallowed_InterfaceOptions {
            get {
                return ResourceManager.GetString("XML_ParameterizationDisallowed_InterfaceOptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given port options are not valid for the port number {0} of the device : {1} because parametrization disallowed in the gsdml file..
        /// </summary>
        internal static string XML_ParameterizationDisallowed_InterfacePort {
            get {
                return ResourceManager.GetString("XML_ParameterizationDisallowed_InterfacePort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given media redundancy configuration is not valid for the device : {0} because parametrization disallowed in the gsdml file..
        /// </summary>
        internal static string XML_ParameterizationDisallowed_MediaRedundancy {
            get {
                return ResourceManager.GetString("XML_ParameterizationDisallowed_MediaRedundancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given synchronization role is not valid for the device : {0} because parametrization disallowed in the gsdml file..
        /// </summary>
        internal static string XML_ParameterizationDisallowed_SyncRole {
            get {
                return ResourceManager.GetString("XML_ParameterizationDisallowed_SyncRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Given ParameterRecordDataItem index: {0} for {1} does not exist according to GSDML..
        /// </summary>
        internal static string XML_ParameterRecordDataIndexNotExist {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDataIndexNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter record data of byte offset {0} and bit offset {1} is not changeable for the IO device {2}..
        /// </summary>
        internal static string XML_ParameterRecordDataIsNotChangeable {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDataIsNotChangeable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ParameterRecordDataItem indexes are not unique for {0}..
        /// </summary>
        internal static string XML_ParameterRecordDataItemIndexNotUnique {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDataItemIndexNotUnique", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The value given for the ParameterRecordDataItem index: {0}, byte offset: {1}, bit offset: {2} for {3} is not one of the allowed values..
        /// </summary>
        internal static string XML_ParameterRecordDataNotAllowedValue {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDataNotAllowedValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The byte offset {0}, bit offset {1} value for ParameterRecordDataItem with index: {2} does not exist for {3}..
        /// </summary>
        internal static string XML_ParameterRecordDataRefIDNotExist {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDataRefIDNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date type parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in  format yyyy-MM-dd or yyyy-MM-dd HH:mm:ss or yyyy-MM-dd HH:mm:ss.TTT which TTT represents 1 to 3 length millisecond part..
        /// </summary>
        internal static string XML_ParameterRecordDateFormatIsNotValid {
            get {
                return ResourceManager.GetString("XML_ParameterRecordDateFormatIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in &apos;Value1,0xValue2&apos; format..
        /// </summary>
        internal static string XML_ParameterRecordFormatIsNotValid {
            get {
                return ResourceManager.GetString("XML_ParameterRecordFormatIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The parameter record data value given of the byte : {0}, has length that is out of limit of the ref ID : {1}..
        /// </summary>
        internal static string XML_ParameterRecordLengthIsOutOfLimit {
            get {
                return ResourceManager.GetString("XML_ParameterRecordLengthIsOutOfLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Octet string type parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} exceeded its length for the IO device {3}..
        /// </summary>
        internal static string XML_ParameterRecordOctetStringLengthExceeded {
            get {
                return ResourceManager.GetString("XML_ParameterRecordOctetStringLengthExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter record data of gsd reference index {0} byte offset {1} and bit offset {2} is not in valid format for the IO device {3}. It must be in &apos;0xValue1, 0xValue2, 0xValue3&apos; format..
        /// </summary>
        internal static string XML_ParameterRecordOctetUnsignedFormatIsNotValid {
            get {
                return ResourceManager.GetString("XML_ParameterRecordOctetUnsignedFormatIsNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDev cannot be used more than one, check the device {0}..
        /// </summary>
        internal static string XML_PDevCannotBeUsedMoreThanOne {
            get {
                return ResourceManager.GetString("XML_PDevCannotBeUsedMoreThanOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDEV owner device of shared device {0} does not exist in the project but SyncRole defined..
        /// </summary>
        internal static string XML_PDEVOwnerNotExistButSyncRoleDefined {
            get {
                return ResourceManager.GetString("XML_PDEVOwnerNotExistButSyncRoleDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PermitOverwritingOfDeviceNamesOfAllAssignedIODevices attribute is not available without SupportDeviceReplacementWithoutExchangeableMedium. Device {0} violates this rule..
        /// </summary>
        internal static string XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement {
            get {
                return ResourceManager.GetString("XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &apos;Boundaries&apos; cannot be set for the port &apos;{0}&apos;, because its interface does not support PDEV..
        /// </summary>
        internal static string XML_PortBoundariesSetForNonPDEVInterface {
            get {
                return ResourceManager.GetString("XML_PortBoundariesSetForNonPDEVInterface", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The port {0} does not exist in the context..
        /// </summary>
        internal static string XML_PortForModuleDoesNotExist {
            get {
                return ResourceManager.GetString("XML_PortForModuleDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The port {0} is defined as LocalPort for multiple times in topology..
        /// </summary>
        internal static string XML_PortIsUsedMoreThanOneInTopology {
            get {
                return ResourceManager.GetString("XML_PortIsUsedMoreThanOneInTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The port number {0} is used multiple times..
        /// </summary>
        internal static string XML_PortNumberUsedMultipleTimes {
            get {
                return ResourceManager.GetString("XML_PortNumberUsedMultipleTimes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The physical subslot {0} is not available for the port {1}..
        /// </summary>
        internal static string XML_PortSubmoduleNotHavePhysicalSubslot {
            get {
                return ResourceManager.GetString("XML_PortSubmoduleNotHavePhysicalSubslot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subslot Number &apos;{0}&apos; is not available for Port &apos;{1}&apos; on Module &apos;{2}&apos; on Interface &apos;{3}&apos;..
        /// </summary>
        internal static string XML_PortWithGivenSubslotNumberDoesNotExist {
            get {
                return ResourceManager.GetString("XML_PortWithGivenSubslotNumberDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High performance mode on PN Driver version {0} is not supported..
        /// </summary>
        internal static string XML_RestrictedVersionForExpertMode {
            get {
                return ResourceManager.GetString("XML_RestrictedVersionForExpertMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock value is already given in sync domain, send clock value of IO controller {0} is ignored..
        /// </summary>
        internal static string XML_SendClockAlreadyGivenInSyncDomain {
            get {
                return ResourceManager.GetString("XML_SendClockAlreadyGivenInSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The send clock set in the device configuration and also in the sync domain for the central device {0}. The send clock in the sync domain will be ignored..
        /// </summary>
        internal static string XML_SendClockAlreadySetInDevice {
            get {
                return ResourceManager.GetString("XML_SendClockAlreadySetInDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock for device {0} is not set..
        /// </summary>
        internal static string XML_SendClockForSharedDeviceIsNotSet {
            get {
                return ResourceManager.GetString("XML_SendClockForSharedDeviceIsNotSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock must be defined under the sync domain : {0} for the IRT configured device : {1}..
        /// </summary>
        internal static string XML_SendClockMustBeUnderSyncDomain {
            get {
                return ResourceManager.GetString("XML_SendClockMustBeUnderSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The sync domain {0} must have a send clock value because it has IO controller that has different syncronization role from unsyncronized..
        /// </summary>
        internal static string XML_SendClockNotGivenInSyncDomaion {
            get {
                return ResourceManager.GetString("XML_SendClockNotGivenInSyncDomaion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock must be set for the central device {0}..
        /// </summary>
        internal static string XML_SendClockNotSet {
            get {
                return ResourceManager.GetString("XML_SendClockNotSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send clock of the RT configured central device {0}, must be set in the device configuration not in the sync domain..
        /// </summary>
        internal static string XML_SendClockOfRtDeviceWrongLocation {
            get {
                return ResourceManager.GetString("XML_SendClockOfRtDeviceWrongLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In the sync domain {0}, IRT configured device(s) are exist and send clock is set so the send clock configuration for the central device {1} cannot be set inside device definition..
        /// </summary>
        internal static string XML_SendClockRtAndIrtInSameSyncDomain {
            get {
                return ResourceManager.GetString("XML_SendClockRtAndIrtInSameSyncDomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned controllers have to be unique for shared device {0}..
        /// </summary>
        internal static string XML_SharedDeviceAssignedControllerUniqueness {
            get {
                return ResourceManager.GetString("XML_SharedDeviceAssignedControllerUniqueness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IOSystem can&apos;t be defined for shared device {0}..
        /// </summary>
        internal static string XML_SharedDeviceIOSystemCheck {
            get {
                return ResourceManager.GetString("XML_SharedDeviceIOSystemCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The device {0} does not support shared device feature..
        /// </summary>
        internal static string XML_SharedDeviceIsNotSupportedForDevice {
            get {
                return ResourceManager.GetString("XML_SharedDeviceIsNotSupportedForDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncDomain can&apos;t be defined for shared device {0}..
        /// </summary>
        internal static string XML_SharedDeviceSyncDomainCheck {
            get {
                return ResourceManager.GetString("XML_SharedDeviceSyncDomainCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned IO controller is unsynchronized so SyncRole of the shared device {0} must be unsynchronized too..
        /// </summary>
        internal static string XML_SharedDeviceWrongSyncRole {
            get {
                return ResourceManager.GetString("XML_SharedDeviceWrongSyncRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shared module {0} is used more than one..
        /// </summary>
        internal static string XML_SharedModuleUsedMoreThanOne {
            get {
                return ResourceManager.GetString("XML_SharedModuleUsedMoreThanOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shared submodule {0} is used more than one..
        /// </summary>
        internal static string XML_SharedSubmoduleUsedMoreThanOne {
            get {
                return ResourceManager.GetString("XML_SharedSubmoduleUsedMoreThanOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot number {0} is not available for module {1}..
        /// </summary>
        internal static string XML_SlotIsNotAvailableForModule {
            get {
                return ResourceManager.GetString("XML_SlotIsNotAvailableForModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The used-in module {0} is unplugged by introducing the module with an empty GSDRefId (&apos;&apos;). But its slot number {1} is invalid..
        /// </summary>
        internal static string XML_SlotIsNotAvailableForRemovingModule {
            get {
                return ResourceManager.GetString("XML_SlotIsNotAvailableForRemovingModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slot number {0} is used from more than one module..
        /// </summary>
        internal static string XML_SlotNumberIsUsedByMoreThanOneModule {
            get {
                return ResourceManager.GetString("XML_SlotNumberIsUsedByMoreThanOneModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncDomain &apos;{0}&apos; not found for device: {1} - interface {2}.
        /// </summary>
        internal static string XML_SpecifiedSyncDomainNotFound {
            get {
                return ResourceManager.GetString("XML_SpecifiedSyncDomainNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SubModuleID {0} under module {1} on device {2} is used more than once. .
        /// </summary>
        internal static string XML_SubmoduleIdUsedMoreThanOnce {
            get {
                return ResourceManager.GetString("XML_SubmoduleIdUsedMoreThanOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SubModuleRefID {0} under isochronous module not found in the list of submodules for device: {1} module: {2}.
        /// </summary>
        internal static string XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule {
            get {
                return ResourceManager.GetString("XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SubModuleRefID {0} under shared device not found in the list of submodules for device: {1} module: {2}.
        /// </summary>
        internal static string XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice {
            get {
                return ResourceManager.GetString("XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Subnet &apos;{0}&apos; not found for central device: {1} - interface {2}.
        /// </summary>
        internal static string XML_SubnetNotFoundForIOC
        {
            get
            {
                return ResourceManager.GetString("XML_SubnetNotFoundForIOC", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Subnet &apos;{0}&apos; not found for central device: {1} - interface {2}.
        /// </summary>
        internal static string XML_SubnetNotFounfForIOC {
            get {
                return ResourceManager.GetString("XML_SubnetNotFounfForIOC", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Subnet &apos;{0}&apos; not found for decentral device: {1} - interface {2}.
        /// </summary>
        internal static string XML_SubnetNotFoundForIOD
        {
            get
            {
                return ResourceManager.GetString("XML_SubnetNotFoundForIOD", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Subnet &apos;{0}&apos; not found for decentral device: {1} - interface {2}.
        /// </summary>
        internal static string XML_SubnetNotFounfForIOD {
            get {
                return ResourceManager.GetString("XML_SubnetNotFounfForIOD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subslot number {0} is not available for submodule {1} of the module {2}..
        /// </summary>
        internal static string XML_SubslotIsNotAvailableForModule {
            get {
                return ResourceManager.GetString("XML_SubslotIsNotAvailableForModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subslot {0} is configured wrong in configuration file..
        /// </summary>
        internal static string XML_SubslotNumberConfigurationIsWrong {
            get {
                return ResourceManager.GetString("XML_SubslotNumberConfigurationIsWrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subslot number is not suitable for the port {0}..
        /// </summary>
        internal static string XML_SubslotNumberForPortNotSuitable {
            get {
                return ResourceManager.GetString("XML_SubslotNumberForPortNotSuitable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subslot number {0} is used by multiple modules..
        /// </summary>
        internal static string XML_SubslotNumberIsUsedByMoreThanOneModule {
            get {
                return ResourceManager.GetString("XML_SubslotNumberIsUsedByMoreThanOneModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The subslot {0} is used multiple times for port..
        /// </summary>
        internal static string XML_SubslotNumberUsedMultipleTimesForPort {
            get {
                return ResourceManager.GetString("XML_SubslotNumberUsedMultipleTimesForPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sync domain not found for decentral device: {0} - interface {1}.
        /// </summary>
        internal static string XML_SyncDomainNotFounfForIOD {
            get {
                return ResourceManager.GetString("XML_SyncDomainNotFounfForIOD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncDomainRefID of {0} must not be different from assigned controller because parametrization is disallowed for the device..
        /// </summary>
        internal static string XML_SyncDomainOfPDEV {
            get {
                return ResourceManager.GetString("XML_SyncDomainOfPDEV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SyncDomainRefID given without defining a Subnet..
        /// </summary>
        internal static string XML_SyncDomainRefIDWithoutSubnet {
            get {
                return ResourceManager.GetString("XML_SyncDomainRefIDWithoutSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device {0} does not have any port owner module which is located in slot {1}.
        /// </summary>
        internal static string XML_TopologyDeviceSlotCheck {
            get {
                return ResourceManager.GetString("XML_TopologyDeviceSlotCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid port number {0} for device {1} in the topology..
        /// </summary>
        internal static string XML_TopologyInvalidPortNumber {
            get {
                return ResourceManager.GetString("XML_TopologyInvalidPortNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TopologyRefID given in configuration xml without a topology xml file..
        /// </summary>
        internal static string XML_TopologyRefIDWithoutTopology {
            get {
                return ResourceManager.GetString("XML_TopologyRefIDWithoutTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} IO address could not be used for the module {1}..
        /// </summary>
        internal static string XML_TypeOfIOAddressNotExist {
            get {
                return ResourceManager.GetString("XML_TypeOfIOAddressNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect InterfaceRefID for central device: {0} - interface: {1}.
        /// </summary>
        internal static string XML_UnmatchedInterfaceRefIDForCentralDevice {
            get {
                return ResourceManager.GetString("XML_UnmatchedInterfaceRefIDForCentralDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect InterfaceRefID for decentral device: {0} - interface: {1}.
        /// </summary>
        internal static string XML_UnmatchedInterfaceRefIDForDecentralDevice {
            get {
                return ResourceManager.GetString("XML_UnmatchedInterfaceRefIDForDecentralDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Interface RefID {0} in topology does not match the given deviceID according to listofnodes..
        /// </summary>
        internal static string XML_UnmatchedInterfaceRefIDInTopology {
            get {
                return ResourceManager.GetString("XML_UnmatchedInterfaceRefIDInTopology", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update time mode is manual but value is not set for the decentral device {0}..
        /// </summary>
        internal static string XML_UpdateTimeMustBeSetInManualMode {
            get {
                return ResourceManager.GetString("XML_UpdateTimeMustBeSetInManualMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update time mode is automatic but value is set for the decentral device {0}..
        /// </summary>
        internal static string XML_UpdateTimeMustNotBeSetInAutomaticMode {
            get {
                return ResourceManager.GetString("XML_UpdateTimeMustNotBeSetInAutomaticMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO address of module {0}/virtual submodule {1} defined twice. Choose one of the IO address definitions, under the module or under the submodule..
        /// </summary>
        internal static string XML_VsmIOAddressesDefinedTwice {
            get {
                return ResourceManager.GetString("XML_VsmIOAddressesDefinedTwice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IOSystem or SyncDomain set without specifying a subnet. This is not supported..
        /// </summary>
        internal static string XML_WithoutSubnet {
            get {
                return ResourceManager.GetString("XML_WithoutSubnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The IO device {0} with Interface {1} cannot have an individual router address because its router settings are in synchronization with its IO controller..
        /// </summary>
        internal static string XML_WrongDefaultRouterSettingInIOD {
            get {
                return ResourceManager.GetString("XML_WrongDefaultRouterSettingInIOD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subslot number {0} is not valid for virtual submodule : {1}. The virtual submodule is fixed in subslot {2} in the GSDML definition..
        /// </summary>
        internal static string XML_WrongSubslotNumberForVsm {
            get {
                return ResourceManager.GetString("XML_WrongSubslotNumberForVsm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to xmlns : &apos;{0}&apos; must be same with targetNamespace : {1} in {2}.
        /// </summary>
        internal static string XSD_NamespaceNotValid {
            get {
                return ResourceManager.GetString("XSD_NamespaceNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} file failed XSD validation: {1}.
        /// </summary>
        internal static string XSD_ValidationError {
            get {
                return ResourceManager.GetString("XSD_ValidationError", resourceCulture);
            }
        }

        internal static string XML_InvalidSNMPEnableReadOnly
        {
            get
            {
                return ResourceManager.GetString("XML_InvalidSNMPEnableReadOnly", resourceCulture);
            }
        }

        internal static string XML_EmptyCommunityName
        {
            get
            {
                return ResourceManager.GetString("XML_EmptyCommunityName", resourceCulture);
            }
            
        }

        internal static string XML_MaxCharSizeCommunityName
        {
            get
            {
                return ResourceManager.GetString("XML_MaxCharSizeCommunityName", resourceCulture);
            }

        }

        internal static string XML_InvalidCharCommunityName
        {
            get
            {
                return ResourceManager.GetString("XML_InvalidCharCommunityName", resourceCulture);
            }

        }

        
    }
}
