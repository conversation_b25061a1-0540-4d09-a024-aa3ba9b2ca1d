using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using PNConfigTool.Models;
using PNConfigTool.Services;
using PNConfigTool.Views.Windows;
using PNConfigLib.Gsd.Interpreter;

namespace PNConfigTool.Views.Pages
{
    /// <summary>
    /// ModuleConfigPage.xaml 的交互逻辑，用于配置设备模块的详细参数
    /// </summary>
    public partial class ModuleConfigPage : Page, INavigationAware, INavigationAwareLeaving
    {
        private ProjectManager? _projectManager;
        private string _deviceName = string.Empty;
        private string _deviceType = string.Empty;
        private string _moduleName = string.Empty;
        private string _moduleType = string.Empty;
        private int _moduleIndex = -1;
        private DecentralDeviceConfig? _currentDeviceConfig;
        private ModuleConfig? _currentModule;
        private bool _isLoaded = false;
        private bool _isModified = false;

        public ModuleConfigPage()
        {
            try
            {
                InitializeComponent();
                _projectManager = ProjectManager.Instance;
                
                Debug.WriteLine("ModuleConfigPage initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing ModuleConfigPage: {ex.Message}");
                MessageBox.Show($"初始化模块配置页面时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 页面加载事件
        /// </summary>
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            if (!_isLoaded && string.IsNullOrEmpty(_deviceName))
            {
                LoadFromNavigation();
            }
        }
        
        /// <summary>
        /// 实现INavigationAware接口，处理导航参数
        /// </summary>
        public void OnNavigatedTo(object? parameter)
        {
            Debug.WriteLine($"=== ModuleConfigPage.OnNavigatedTo 开始 ===");
            Debug.WriteLine($"参数类型: {parameter?.GetType().Name ?? "null"}");
            Debug.WriteLine($"参数值: {parameter}");

            // 首先显示当前项目中的所有设备信息
            if (_projectManager?.CurrentProject != null)
            {
                Debug.WriteLine($"=== 当前项目中的所有设备 ===");
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    Debug.WriteLine($"设备: DeviceRefID='{device.DeviceRefID}', DeviceType='{device.DeviceType}', 模块数量={device.Modules?.Count ?? 0}");
                }
            }

            try
            {
                if (parameter is NavigationModuleParams moduleParams)
                {
                    // 从导航参数中获取设备和模块信息
                    _deviceName = moduleParams.DeviceName;
                    _moduleType = moduleParams.ModuleType;
                    _moduleName = moduleParams.ModuleName;
                    _moduleIndex = moduleParams.ModuleIndex;

                    Debug.WriteLine($"接收到模块参数 - 设备: '{_deviceName}', 模块类型: '{_moduleType}', 模块名: '{_moduleName}', 索引: {_moduleIndex}");
                    LoadModuleData();
                }
                else if (parameter is string deviceName)
                {
                    // 仅接收设备名称，但没有模块信息
                    _deviceName = deviceName;
                    Debug.WriteLine($"仅接收到设备名称: '{_deviceName}'");
                    // 尝试加载设备的第一个模块信息
                    LoadFirstModuleForDevice();
                }
                else
                {
                    // 没有接收到参数，尝试从导航信息解析
                    Debug.WriteLine("没有接收到有效参数，尝试从导航信息解析");
                    LoadFromNavigation();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"OnNavigatedTo 错误: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"加载模块信息时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 实现INavigationAwareLeaving接口，在页面离开前保存更改
        /// </summary>
        public bool OnNavigatedFrom()
        {
            return CheckUnsavedChanges();
        }
        
        /// <summary>
        /// 尝试从导航参数中获取设备和模块信息
        /// </summary>
        private void LoadFromNavigation()
        {
            try
            {
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService != null && navigationService.CurrentPage != null)
                {
                    string pageName = navigationService.CurrentPage;
                    Debug.WriteLine($"Current page name: {pageName}");
                    
                    // 尝试从导航参数获取信息
                    if (navigationService.NavigationParameter is NavigationModuleParams moduleParams)
                    {
                        _deviceName = moduleParams.DeviceName;
                        _moduleType = moduleParams.ModuleType;
                        _moduleName = moduleParams.ModuleName;
                        _moduleIndex = moduleParams.ModuleIndex;

                        Debug.WriteLine($"从导航参数提取模块信息: 设备={_deviceName}, 模块类型={_moduleType}, 模块名={_moduleName}, 索引={_moduleIndex}");
                        LoadModuleData();
                        return;
                    }
                    else if (navigationService.NavigationParameter is string deviceName)
                    {
                        _deviceName = deviceName;
                        Debug.WriteLine($"从导航参数提取设备名: {_deviceName}");
                        LoadFirstModuleForDevice();
                        return;
                    }
                    
                    // 如果导航参数为空，则尝试从页面名称解析
                    if (pageName.StartsWith("ModuleConfig_"))
                    {
                        Debug.WriteLine($"解析ModuleConfig页面名称: '{pageName}'");

                        // 尝试新的格式: ModuleConfig_{deviceName}_{slotNumber}
                        // 由于设备名可能包含下划线，我们需要更智能的解析
                        string remainder = pageName.Substring("ModuleConfig_".Length);
                        Debug.WriteLine($"页面名称去除前缀后: '{remainder}'");

                        // 从末尾开始查找最后一个下划线，这应该是插槽号分隔符
                        int lastUnderscoreIndex = remainder.LastIndexOf('_');
                        if (lastUnderscoreIndex > 0)
                        {
                            string deviceName = remainder.Substring(0, lastUnderscoreIndex);
                            string slotNumber = remainder.Substring(lastUnderscoreIndex + 1);
                            Debug.WriteLine($"从页面名称提取设备名和插槽号: 设备='{deviceName}', 插槽='{slotNumber}'");

                            // 根据设备名和插槽号查找对应的模块
                            if (FindDeviceAndModuleBySlot(deviceName, slotNumber))
                            {
                                Debug.WriteLine($"成功找到模块: 设备='{_deviceName}', 插槽='{slotNumber}'");
                                // 直接更新UI，因为_currentModule已经在FindDeviceAndModuleBySlot中设置
                                UpdateUIWithModuleInfo();
                                _isLoaded = true;
                                return;
                            }
                            else
                            {
                                Debug.WriteLine($"无法找到设备和插槽对应的模块: 设备='{deviceName}', 插槽='{slotNumber}'");
                            }
                        }
                        else
                        {
                            // 回退到旧的格式: ModuleConfig_{ModuleID}
                            string moduleId = pageName.Substring("ModuleConfig_".Length);
                            Debug.WriteLine($"从页面名称提取模块ID: '{moduleId}'");

                            // 根据ModuleID查找对应的设备和模块
                            if (FindDeviceAndModuleByModuleId(moduleId))
                            {
                                Debug.WriteLine($"成功找到模块: 设备='{_deviceName}', 模块ID='{moduleId}'");
                                // 直接更新UI，因为_currentModule已经在FindDeviceAndModuleByModuleId中设置
                                UpdateUIWithModuleInfo();
                                _isLoaded = true;
                                return;
                            }
                            else
                            {
                                Debug.WriteLine($"无法找到模块ID对应的设备和模块: '{moduleId}'");
                            }
                        }
                    }
                    
                    Debug.WriteLine($"无法从页面名称提取设备或模块信息: {pageName}");
                    MessageBox.Show("无法确定要配置的设备或模块。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                else
                {
                    Debug.WriteLine("导航服务或当前页面为空");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading from navigation: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 根据设备名和插槽号查找对应的模块
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <param name="slotNumber">插槽号</param>
        /// <returns>如果找到返回true，否则返回false</returns>
        private bool FindDeviceAndModuleBySlot(string deviceName, string slotNumber)
        {
            try
            {
                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("项目管理器或当前项目为空");
                    return false;
                }

                Debug.WriteLine($"查找设备和模块: 设备={deviceName}, 插槽={slotNumber}");
                Debug.WriteLine($"项目中的设备列表:");

                // 查找设备
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    Debug.WriteLine($"  - DeviceRefID: {device.DeviceRefID}, DeviceType: {device.DeviceType}, 模块数量: {device.Modules?.Count ?? 0}");

                    if (device.DeviceRefID == deviceName)
                    {
                        _deviceName = deviceName;
                        Debug.WriteLine($"找到匹配的设备: {device.DeviceRefID}");

                        // 查找指定插槽的模块
                        if (device.Modules != null)
                        {
                            Debug.WriteLine($"设备 {deviceName} 的模块列表:");
                            foreach (var module in device.Modules)
                            {
                                Debug.WriteLine($"    - 模块: {module.ModuleRefID}, 插槽: {module.SlotNumber}, GSDRefID: {module.GSDRefID}");
                                if (module.SlotNumber.ToString() == slotNumber)
                                {
                                    // 找到匹配的模块，设置模块信息
                                    _moduleIndex = device.Modules.IndexOf(module);
                                    _moduleType = module.GSDRefID ?? "";
                                    _moduleName = module.ModuleRefID ?? "";
                                    _currentModule = module;  // 关键修复：设置当前模块引用
                                    _currentDeviceConfig = device;  // 同时设置当前设备配置

                                    Debug.WriteLine($"找到匹配的模块: 索引={_moduleIndex}, 类型={_moduleType}, 名称={_moduleName}");
                                    return true;
                                }
                            }
                        }

                        Debug.WriteLine($"在设备 {deviceName} 中未找到插槽 {slotNumber} 的模块");
                        return false;
                    }
                }

                Debug.WriteLine($"未找到设备: {deviceName}");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找设备和模块时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据模块ID查找对应的设备和模块
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <returns>如果找到返回true，否则返回false</returns>
        private bool FindDeviceAndModuleByModuleId(string moduleId)
        {
            try
            {
                Debug.WriteLine($"=== FindDeviceAndModuleByModuleId 开始查找模块ID: {moduleId} ===");

                if (_projectManager?.CurrentProject == null)
                {
                    Debug.WriteLine("ProjectManager或CurrentProject为null");
                    return false;
                }

                Debug.WriteLine($"项目中的设备数量: {_projectManager.CurrentProject.ConfigurationSettings.DecentralDevices.Count}");

                // 遍历所有设备，查找包含指定模块ID的设备
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    Debug.WriteLine($"检查设备: {device.DeviceRefID}, 模块数量: {device.Modules?.Count ?? 0}");

                    if (device.Modules != null)
                    {
                        foreach (var module in device.Modules)
                        {
                            Debug.WriteLine($"  检查模块: {module.ModuleRefID}");

                            if (module.ModuleRefID == moduleId)
                            {
                                _deviceName = device.DeviceRefID;
                                _deviceType = device.DeviceType;
                                _moduleIndex = device.Modules.IndexOf(module);
                                _currentModule = module;  // 关键修复：设置当前模块引用
                                _currentDeviceConfig = device;  // 同时设置当前设备配置
                                Debug.WriteLine($"找到模块: 设备={_deviceName}, 模块ID={moduleId}, 索引={_moduleIndex}");
                                Debug.WriteLine($"=== FindDeviceAndModuleByModuleId 成功完成 ===");
                                return true;
                            }
                        }
                    }
                }

                Debug.WriteLine($"未找到模块ID: {moduleId}");
                Debug.WriteLine($"=== FindDeviceAndModuleByModuleId 未找到模块 ===");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找模块时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 判断当前模块是否为DAP主模块
        /// DAP主模块是设备的根模块，不应该被添加到Modules配置中
        /// </summary>
        /// <param name="moduleIndex">模块索引</param>
        /// <param name="slotNumber">槽位号</param>
        /// <returns>如果是DAP主模块返回true，否则返回false</returns>
        private bool IsDAPMainModule(int moduleIndex, int slotNumber)
        {
            // DAP主模块的特征：
            // 1. 通过DeviceDetail_设备名称导航进来的（表示要显示DAP主模块）
            // 2. 或者模块索引为-1（专门用于标识DAP主模块）
            // 3. 或者模块索引为0且槽位号为0且设备没有模块配置

            // 检查是否通过DeviceDetail导航进来
            var navigationService = ServiceLocator.GetService<INavigationService>();
            bool isDeviceDetailNavigation = navigationService?.CurrentPage?.StartsWith("DeviceDetail_") == true;

            // 如果是通过DeviceDetail导航进来，则认为是DAP主模块
            if (isDeviceDetailNavigation)
            {
                return true;
            }

            // 如果模块索引为-1，则认为是DAP主模块（专门用于标识DAP主模块）
            if (moduleIndex == -1)
            {
                return true;
            }

            // 传统的判断方式：模块索引为0且槽位号为0且设备没有模块配置
            if (moduleIndex == 0 && slotNumber == 0)
            {
                if (_currentDeviceConfig?.Modules == null ||
                    _currentDeviceConfig.Modules.Count == 0)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 加载设备的第一个模块信息
        /// </summary>
        private void LoadFirstModuleForDevice()
        {
            if (string.IsNullOrEmpty(_deviceName)) return;

            try
            {
                if (_projectManager == null)
                {
                    _projectManager = ProjectManager.Instance;
                    if (_projectManager == null)
                    {
                        Debug.WriteLine("Error: ProjectManager.Instance is null");
                        MessageBox.Show("无法获取项目管理器实例", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                if (_projectManager.CurrentProject == null)
                {
                    Debug.WriteLine("Error: CurrentProject is null");
                    MessageBox.Show("当前没有打开的项目", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 查找对应的设备配置
                DecentralDeviceConfig? deviceConfig = null;
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    if (device.DeviceRefID == _deviceName)
                    {
                        deviceConfig = device;
                        _deviceType = device.DeviceRefID;
                        break;
                    }
                }

                if (deviceConfig == null)
                {
                    Debug.WriteLine($"Device not found: {_deviceName}");
                    MessageBox.Show($"未找到设备: {_deviceName}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    NavigateBack();
                    return;
                }

                _currentDeviceConfig = deviceConfig;

                // 检查设备是否有模块列表
                if (deviceConfig.Modules == null)
                {
                    deviceConfig.Modules = new List<ModuleConfig>();
                }

                // 检查是否通过DeviceDetail导航进来（表示要显示DAP主模块）
                var navigationService = ServiceLocator.GetService<INavigationService>();
                bool isDeviceDetailNavigation = navigationService?.CurrentPage?.StartsWith("DeviceDetail_") == true;

                // 如果通过DeviceDetail导航进来，强制显示DAP主模块（无论设备是否有普通模块）
                if (isDeviceDetailNavigation)
                {
                    Debug.WriteLine($"设备 {_deviceName} 通过DeviceDetail导航，强制显示DAP主模块 (模块数量: {deviceConfig.Modules.Count})");

                    // 创建默认DAP主模块（仅用于UI显示，不保存到配置）
                    var defaultModule = DeviceConfigFactory.CreateModuleConfig(
                        $"{_deviceType}_DAP",
                        _deviceType,
                        0,
                        "DAP主模块"
                    );

                    // 创建默认子模块
                    var defaultSubmodule = DeviceConfigFactory.CreateSubmoduleConfig(
                        $"{_deviceType}_DAP_Submodule",
                        "DAP主模块",
                        0,
                        "0",
                        "0",
                        "0",
                        "DAP主模块"
                    );

                    defaultModule.Submodules.Add(defaultSubmodule);

                    // 注意：DAP主模块不添加到deviceConfig.Modules中，仅用于UI显示
                    _moduleIndex = -1; // 使用-1表示这是DAP主模块，不是普通模块索引
                    _currentModule = defaultModule;
                    _moduleType = defaultModule.ModuleRefID;
                    _moduleName = defaultSubmodule.SubmoduleRefID;

                    Debug.WriteLine($"已为设备 {_deviceName} 创建DAP主模块用于UI显示（未添加到配置）");

                    // 更新UI并直接返回，不再执行后续的普通模块加载逻辑
                    UpdateUIWithModuleInfo();
                    _isLoaded = true;
                    return;
                }
                // 如果设备没有模块，也创建DAP主模块用于UI显示
                else if (deviceConfig.Modules.Count == 0)
                {
                    Debug.WriteLine($"设备 {_deviceName} 没有模块，创建默认DAP主模块用于UI显示");

                    // 创建默认DAP主模块（仅用于UI显示，不保存到配置）
                    var defaultModule = DeviceConfigFactory.CreateModuleConfig(
                        $"{_deviceType}_DAP",
                        _deviceType,
                        0,
                        "DAP主模块"
                    );

                    // 创建默认子模块
                    var defaultSubmodule = DeviceConfigFactory.CreateSubmoduleConfig(
                        $"{_deviceType}_DAP_Submodule",
                        "DAP主模块",
                        0,
                        "0",
                        "0",
                        "0",
                        "DAP主模块"
                    );

                    defaultModule.Submodules.Add(defaultSubmodule);

                    // 注意：DAP主模块不添加到deviceConfig.Modules中，仅用于UI显示
                    _moduleIndex = 0;
                    _currentModule = defaultModule;
                    _moduleType = defaultModule.ModuleRefID;
                    _moduleName = defaultSubmodule.SubmoduleRefID;

                    Debug.WriteLine($"已为设备 {_deviceName} 创建DAP主模块用于UI显示（未添加到配置）");
                }
                else
                {
                    // 使用第一个模块
                    _currentModule = deviceConfig.Modules[0];
                    _moduleIndex = 0;
                    _moduleType = _currentModule.ModuleRefID;
                    _moduleName = _currentModule.Submodules.Count > 0 ? _currentModule.Submodules[0].SubmoduleRefID : "主模块";
                    Debug.WriteLine($"加载设备 {_deviceName} 的第一个模块: {_moduleType}");
                }

                // 更新UI
                UpdateUIWithModuleInfo();
                _isLoaded = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载第一个模块出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"加载模块信息时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NavigateBack();
            }
        }
        
        /// <summary>
        /// 根据设备名和模块索引加载模块数据
        /// </summary>
        private void LoadModuleData()
        {
            Debug.WriteLine($"=== LoadModuleData 开始 ===");
            Debug.WriteLine($"设备名: {_deviceName}");
            Debug.WriteLine($"模块名: {_moduleName}");
            Debug.WriteLine($"模块类型: {_moduleType}");
            Debug.WriteLine($"模块索引: {_moduleIndex}");

            if (string.IsNullOrEmpty(_deviceName))
            {
                Debug.WriteLine("错误: 设备名称为空，无法加载模块数据");
                return;
            }

            try
            {
                if (_projectManager == null)
                {
                    _projectManager = ProjectManager.Instance;
                    if (_projectManager == null)
                    {
                        Debug.WriteLine("错误: ProjectManager.Instance 为空");
                        MessageBox.Show("无法获取项目管理器实例", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                if (_projectManager.CurrentProject == null)
                {
                    Debug.WriteLine("错误: CurrentProject 为空");
                    MessageBox.Show("当前没有打开的项目", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 查找对应的设备配置
                DecentralDeviceConfig? deviceConfig = null;
                Debug.WriteLine($"查找设备: {_deviceName}");
                Debug.WriteLine($"项目中的设备列表:");
                foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                {
                    Debug.WriteLine($"  - DeviceRefID: {device.DeviceRefID}, DeviceType: {device.DeviceType}");
                    if (device.DeviceRefID == _deviceName)
                    {
                        deviceConfig = device;
                        _deviceType = device.DeviceType; // 使用DeviceType而不是DeviceRefID
                        Debug.WriteLine($"找到匹配的设备: {device.DeviceRefID}");
                        break;
                    }
                }

                if (deviceConfig == null)
                {
                    Debug.WriteLine($"找不到设备: {_deviceName}");
                    MessageBox.Show($"未找到设备: {_deviceName}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    NavigateBack();
                    return;
                }

                _currentDeviceConfig = deviceConfig;

                // 检查设备是否有模块列表
                if (deviceConfig.Modules == null)
                {
                    deviceConfig.Modules = new List<ModuleConfig>();
                }

                // 检查是否为DAP主模块（索引0，槽位0）
                bool isDAPMainModule = IsDAPMainModule(_moduleIndex, 0);

                // 如果设备没有模块，或者模块索引超出范围，则创建一个默认模块
                if (deviceConfig.Modules.Count == 0 || _moduleIndex < 0 || _moduleIndex >= deviceConfig.Modules.Count)
                {
                    if (isDAPMainModule)
                    {
                        Debug.WriteLine($"设备 {_deviceName} 没有模块或索引 {_moduleIndex} 超出范围，将创建DAP主模块用于UI显示");

                        // 创建DAP主模块（仅用于UI显示，不保存到配置）
                        var defaultModule = DeviceConfigFactory.CreateModuleConfig(
                            $"{_deviceType}_DAP",
                            _deviceType,
                            0,
                            "DAP主模块"
                        );

                        // 创建默认子模块
                        var defaultSubmodule = DeviceConfigFactory.CreateSubmoduleConfig(
                            $"{_deviceType}_DAP_Submodule",
                            "DAP主模块",
                            0,
                            "0",
                            "0",
                            "0",
                            "DAP主模块"
                        );

                        defaultModule.Submodules.Add(defaultSubmodule);

                        // 注意：DAP主模块不添加到deviceConfig.Modules中，仅用于UI显示
                        _moduleIndex = 0; // 设置为第一个模块
                        _currentModule = defaultModule;

                        // 更新模块相关属性
                        _moduleType = defaultModule.ModuleRefID;
                        _moduleName = defaultSubmodule.SubmoduleRefID;

                        Debug.WriteLine($"已为设备 {_deviceName} 创建DAP主模块用于UI显示（未添加到配置）");
                    }
                    else
                    {
                        Debug.WriteLine($"设备 {_deviceName} 的模块索引 {_moduleIndex} 超出范围");
                        Debug.WriteLine("模块配置页面应该通过ModuleID导航，而不是索引");

                        // 显示错误信息并导航回设备配置页面
                        MessageBox.Show($"模块索引 {_moduleIndex} 不存在。请从设备配置页面正确导航到模块配置页面。",
                                      "模块不存在", MessageBoxButton.OK, MessageBoxImage.Warning);

                        NavigateBack();
                        return;
                    }

                    // 更新UI
                    UpdateUIWithModuleInfo();
                    _isLoaded = true;
                }
                else
                {
                    // 使用指定的模块索引
                    _currentModule = deviceConfig.Modules[_moduleIndex];

                    // 更新模块相关属性
                    _moduleType = _currentModule.ModuleRefID;
                    _moduleName = _currentModule.Submodules.Count > 0 ? _currentModule.Submodules[0].SubmoduleRefID : "主模块";

                    Debug.WriteLine($"已加载设备 {_deviceName} 的第 {_moduleIndex} 个模块: {_moduleType}");

                    // 更新UI
                    UpdateUIWithModuleInfo();
                    _isLoaded = true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载模块数据出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"加载模块数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NavigateBack();
            }
        }
        
        /// <summary>
        /// 更新UI显示模块信息
        /// </summary>
        private void UpdateUIWithModuleInfo()
        {
            try
            {
                // 更新表单字段
                if (_currentModule != null)
                {
                    // 获取GSDML路径和模块GSDRefID
                    string? gsdPath = GetCurrentGSDMLPath();
                    string? moduleGSDRefID = GetCurrentModuleGSDRefID();

                    // 默认值
                    string shortName = "未知模块类型";
                    string orderNumber = "未指定";
                    string info = "无描述信息";
                    string gsdmlPath = "未找到GSDML文件";

                    // 检查当前模块是否为DAP主模块
                    bool isDAPMainModule = IsDAPMainModule(_moduleIndex, _currentModule.SlotNumber);

                    // 如果有GSDML路径，则动态提取信息
                    if (!string.IsNullOrEmpty(gsdPath))
                    {
                        gsdmlPath = gsdPath;

                        if (isDAPMainModule)
                        {
                            // 对于DAP主模块，使用设备的GSDRefID和原来的DAP信息提取方法
                            string? deviceGSDRefID = GetCurrentGSDRefID();
                            if (!string.IsNullOrEmpty(deviceGSDRefID))
                            {
                                Debug.WriteLine("当前模块是DAP主模块，使用DAP信息提取方法");
                                var dapInfo = ExtractDAPInfoFromGSDML(gsdPath, deviceGSDRefID);
                                if (dapInfo != null)
                                {
                                    // 获取设备类型作为简短标识（与导航栏菜单一致）
                                    if (_currentDeviceConfig != null)
                                    {
                                        shortName = !string.IsNullOrEmpty(_currentDeviceConfig.DeviceType) ?
                                                   _currentDeviceConfig.DeviceType :
                                                   _currentDeviceConfig.DeviceRefID;
                                    }
                                    orderNumber = dapInfo.OrderNumber ?? orderNumber;
                                    info = dapInfo.Info ?? info;

                                    Debug.WriteLine($"成功从GSDML提取DAP信息:");
                                    Debug.WriteLine($"  简明标识: {shortName}");
                                    Debug.WriteLine($"  订货号: {orderNumber}");
                                    Debug.WriteLine($"  描述: {info}");
                                }
                                else
                                {
                                    Debug.WriteLine("无法从GSDML文件提取DAP信息，使用默认值");
                                }
                            }
                        }
                        else if (!string.IsNullOrEmpty(moduleGSDRefID))
                        {
                            // 对于普通模块，使用模块的GSDRefID和模块信息提取方法
                            Debug.WriteLine("当前模块是普通模块，使用模块信息提取方法");
                            var moduleInfo = ExtractModuleInfoFromGSDML(gsdPath, moduleGSDRefID);
                            if (moduleInfo != null)
                            {
                                shortName = moduleInfo.ShortName ?? shortName;
                                orderNumber = moduleInfo.OrderNumber ?? orderNumber;
                                info = moduleInfo.Info ?? info;

                                Debug.WriteLine($"成功从GSDML提取模块信息:");
                                Debug.WriteLine($"  简明标识: {shortName}");
                                Debug.WriteLine($"  订货号: {orderNumber}");
                                Debug.WriteLine($"  描述: {info}");
                            }
                            else
                            {
                                Debug.WriteLine("无法从GSDML文件提取模块信息，使用默认值");

                                // 如果无法提取模块信息，尝试使用模块的基本信息
                                if (!string.IsNullOrEmpty(_currentModule.ModuleRefID))
                                {
                                    shortName = _currentModule.ModuleRefID;
                                    Debug.WriteLine($"使用模块RefID作为简明标识: {shortName}");
                                }
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine("缺少GSDML路径或模块GSDRefID，使用默认值");
                        Debug.WriteLine($"  GSDML路径: {gsdPath ?? "null"}");
                        Debug.WriteLine($"  模块GSDRefID: {moduleGSDRefID ?? "null"}");

                        // 如果没有GSDML信息，至少使用模块的基本信息
                        if (!string.IsNullOrEmpty(_currentModule.ModuleRefID))
                        {
                            shortName = _currentModule.ModuleRefID;
                            Debug.WriteLine($"使用模块RefID作为简明标识: {shortName}");
                        }
                    }

                    // 更新UI控件
                    // 简短标识 = 设备类型（与导航栏菜单一致）
                    ShortNameTextBox.Text = shortName;

                    // 说明 = dapInfo.Info
                    DescriptionTextBox.Text = info;

                    // 订货号 = dapInfo.OrderNumber
                    OrderNumberTextBox.Text = orderNumber;

                    // GSDML路径
                    GSDMLPathTextBox.Text = gsdmlPath;
                }

                Debug.WriteLine("UI updated with module information");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating UI: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 根据模块类型配置选项卡显示
        /// </summary>
        private void ConfigureTabsForModuleType()
        {
            // 在新的UI设计中，我们不再使用选项卡，所以这个方法可以简化
            Debug.WriteLine("ConfigureTabsForModuleType called - no tabs in new UI design");
        }
        
        /// <summary>
        /// 加载模块特定的配置设置
        /// </summary>
        private void LoadModuleSpecificSettings()
        {
            if (_currentModule == null) return;

            try
            {
                // 设置基本信息
                if (_currentDeviceConfig != null)
                {
                    // 调用UpdateUIWithModuleInfo来动态获取和设置信息
                    UpdateUIWithModuleInfo();
                }

                // 防止修改状态在初始化时被触发
                _isModified = false;

                Debug.WriteLine("Module specific settings loaded");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading module settings: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 保存模块配置
        /// </summary>
        private bool SaveModuleConfiguration()
        {
            if (_currentModule == null || _currentDeviceConfig == null || _projectManager == null) return false;

            try
            {
                // 保存设备信息
                // 在新的UI设计中，我们不再使用IPAddressTextBox，所以这里不需要保存IP地址
                
                // 检查当前模块是否为DAP主模块
                bool isDAPMainModule = IsDAPMainModule(_moduleIndex, _currentModule.SlotNumber);

                if (isDAPMainModule)
                {
                    Debug.WriteLine("当前模块是DAP主模块，仅保存设备级别配置，不保存到Modules列表");
                    // DAP主模块的配置保存在设备级别，不需要添加到Modules列表中
                }
                else
                {
                    Debug.WriteLine("当前模块是普通模块，确保已添加到Modules列表");
                    // 确保普通模块已添加到设备的Modules列表中
                    if (!_currentDeviceConfig.Modules.Contains(_currentModule))
                    {
                        _currentDeviceConfig.Modules.Add(_currentModule);
                        Debug.WriteLine($"将模块 {_currentModule.ModuleRefID} 添加到设备配置中");
                    }
                }

                // 这里可以添加更多模块特定参数的保存

                // 更新项目修改状态
                _projectManager.IsProjectModified = true;
                _isModified = false;

                Debug.WriteLine("Module configuration saved");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving module configuration: {ex.Message}");
                MessageBox.Show($"保存模块配置时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
        
        /// <summary>
        /// 导航回设备配置页面
        /// </summary>
        private void NavigateBack()
        {
            var navigationService = ServiceLocator.GetService<INavigationService>();
            if (navigationService != null)
            {
                // 导航回设备配置页面
                string deviceConfigPageKey = $"DeviceConfig_{_deviceName}";
                try
                {
                    navigationService.Navigate(deviceConfigPageKey, _deviceName);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error navigating back: {ex.Message}");
                    // 如果导航到设备页面失败，尝试导航到控制器配置页面
                    try
                    {
                        navigationService.Navigate("ControllerConfigPage");
                    }
                    catch
                    {
                        // 如果都失败，显示错误信息
                        MessageBox.Show("导航失败，请手动选择页面。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
        
        /// <summary>
        /// 检查是否有未保存的更改
        /// </summary>
        private bool CheckUnsavedChanges()
        {
            if (_isModified)
            {
                MessageBoxResult result = MessageBox.Show(
                    "当前有未保存的更改，是否保存？",
                    "提示",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    return SaveModuleConfiguration();
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return false;
                }
            }
            
            return true;
        }
        
        #region 按钮事件处理
        
        /// <summary>
        /// 上一步按钮点击事件
        /// </summary>
        private void PreviousButton_Click(object sender, RoutedEventArgs e)
        {
            if (CheckUnsavedChanges())
            {
                NavigateBack();
            }
        }
        
        /// <summary>
        /// 下一步按钮点击事件
        /// </summary>
        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!SaveModuleConfiguration())
                {
                    Debug.WriteLine("保存模块配置失败，中止导航");
                    return;
                }
                
                // 获取导航服务
                var navigationService = ServiceLocator.GetService<INavigationService>();
                if (navigationService == null)
                {
                    Debug.WriteLine("无法获取导航服务");
                    MessageBox.Show("无法获取导航服务", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                
                // 获取主窗口中的导航ListBox
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Debug.WriteLine("无法获取主窗口");
                    return;
                }
                
                var navigationListBox = mainWindow.FindName("NavigationListBox") as ListBox;
                if (navigationListBox == null)
                {
                    Debug.WriteLine("无法获取导航ListBox");
                    return;
                }
                
                // 查找当前模块在导航列表中的位置
                int currentIndex = -1;
                string currentModuleTag = navigationService.CurrentPage ?? "";
                Debug.WriteLine($"当前页面标识: {currentModuleTag}");

                // 首先尝试精确匹配
                for (int i = 0; i < navigationListBox.Items.Count; i++)
                {
                    if (navigationListBox.Items[i] is ListBoxItem item && item.Tag?.ToString() == currentModuleTag)
                    {
                        currentIndex = i;
                        Debug.WriteLine($"找到当前页面在导航列表中的位置: {i}, Tag: {currentModuleTag}");
                        break;
                    }
                }

                // 如果精确匹配失败，尝试模糊匹配（针对ModuleConfig页面）
                if (currentIndex == -1 && currentModuleTag.StartsWith("ModuleConfig_"))
                {
                    Debug.WriteLine("精确匹配失败，尝试模糊匹配ModuleConfig页面");
                    for (int i = 0; i < navigationListBox.Items.Count; i++)
                    {
                        if (navigationListBox.Items[i] is ListBoxItem item)
                        {
                            string itemTag = item.Tag?.ToString() ?? "";
                            Debug.WriteLine($"检查导航项 {i}: Tag={itemTag}");

                            // 检查是否为同一设备的ModuleConfig页面
                            if (itemTag.StartsWith("ModuleConfig_") && itemTag.Contains(_deviceName))
                            {
                                currentIndex = i;
                                Debug.WriteLine($"通过模糊匹配找到当前页面位置: {i}, Tag: {itemTag}");
                                break;
                            }
                        }
                    }
                }
                
                if (currentIndex >= 0 && currentIndex < navigationListBox.Items.Count - 1)
                {
                    // 直接获取下一个导航项，无论它是什么类型
                    var nextItem = navigationListBox.Items[currentIndex + 1] as ListBoxItem;
                    if (nextItem != null && nextItem.Tag != null)
                    {
                        string nextTag = nextItem.Tag.ToString()!;
                        Debug.WriteLine($"下一个导航项: {nextTag}");
                        
                        try
                        {
                            // 根据页面类型进行不同的导航
                            if (nextTag.StartsWith("DeviceConfig_"))
                            {
                                // 设备配置页面
                                string nextDeviceName = nextTag.Substring("DeviceConfig_".Length);
                                Debug.WriteLine($"导航到设备配置页面: {nextTag}, 参数: {nextDeviceName}");
                                navigationService.Navigate(nextTag, nextDeviceName);
                                return;
                            }
                            else if (nextTag.StartsWith("DeviceDetail_"))
                            {
                                // 模块配置页面
                                string deviceName = nextTag.Substring("DeviceDetail_".Length);
                                Debug.WriteLine($"导航到模块配置页面: {nextTag}, 设备: {deviceName}");

                                // 查找该设备的类型
                                string deviceType = "";
                                if (_projectManager?.CurrentProject != null)
                                {
                                    foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                                    {
                                        if (device.DeviceRefID == deviceName)
                                        {
                                            deviceType = device.DeviceType; // 使用DeviceType而不是DeviceRefID
                                            break;
                                        }
                                    }
                                }

                                // 创建导航参数，指向设备的第一个模块
                                var moduleParams = new NavigationModuleParams(
                                    deviceName,
                                    deviceType,
                                    "", // 默认无子模块名
                                    0   // 默认第一个模块
                                );

                                navigationService.Navigate(nextTag, moduleParams);
                                return;
                            }
                            else if (nextTag.StartsWith("ModuleConfig_"))
                            {
                                // 另一个模块配置页面
                                Debug.WriteLine($"导航到另一个模块配置页面: {nextTag}");

                                // 解析页面键以获取设备名和插槽号
                                string remainder = nextTag.Substring("ModuleConfig_".Length);
                                int lastUnderscoreIndex = remainder.LastIndexOf('_');
                                if (lastUnderscoreIndex > 0)
                                {
                                    string deviceName = remainder.Substring(0, lastUnderscoreIndex);
                                    string slotNumber = remainder.Substring(lastUnderscoreIndex + 1);

                                    // 查找该设备的类型
                                    string deviceType = "";
                                    if (_projectManager?.CurrentProject != null)
                                    {
                                        foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                                        {
                                            if (device.DeviceRefID == deviceName)
                                            {
                                                deviceType = device.DeviceType;
                                                break;
                                            }
                                        }
                                    }

                                    // 查找模块索引（基于插槽号）
                                    int moduleIndex = 0;
                                    if (_projectManager?.CurrentProject != null)
                                    {
                                        foreach (var device in _projectManager.CurrentProject.ConfigurationSettings.DecentralDevices)
                                        {
                                            if (device.DeviceRefID == deviceName && device.Modules != null)
                                            {
                                                for (int i = 0; i < device.Modules.Count; i++)
                                                {
                                                    if (device.Modules[i].SlotNumber.ToString() == slotNumber)
                                                    {
                                                        moduleIndex = i;
                                                        break;
                                                    }
                                                }
                                                break;
                                            }
                                        }
                                    }

                                    // 创建导航参数
                                    var moduleParams = new NavigationModuleParams(
                                        deviceName,
                                        deviceType,
                                        "", // 默认无子模块名
                                        moduleIndex   // 使用找到的模块索引
                                    );

                                    navigationService.Navigate(nextTag, moduleParams);
                                    return;
                                }
                                else
                                {
                                    Debug.WriteLine($"无法解析模块配置页面键: {nextTag}");
                                    // 直接导航，让目标页面自己处理
                                    navigationService.Navigate(nextTag);
                                    return;
                                }
                            }
                            else if (nextTag == "GlobalCompletion")
                            {
                                // 全局完成页面
                                Debug.WriteLine($"导航到全局完成页面: {nextTag}");
                                navigationService.Navigate(nextTag);
                                return;
                            }
                            else
                            {
                                // 其他类型的页面
                                Debug.WriteLine($"导航到其他类型页面: {nextTag}");
                                navigationService.Navigate(nextTag);
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"导航到下一个页面出错: {ex.Message}");
                            MessageBox.Show($"导航到下一个页面时出错: {ex.Message}", "导航错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("当前页面索引无效或已是最后一个页面");
                }
                
                // 如果没有找到下一个导航项或者出错，则返回设备配置页面
                string deviceConfigPageKey = $"DeviceConfig_{_deviceName}";
                Debug.WriteLine($"返回设备配置页面: {deviceConfigPageKey}");
                
                try
                {
                    navigationService.Navigate(deviceConfigPageKey, _deviceName);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"导航回设备配置页面出错: {ex.Message}");
                    // 如果导航失败，尝试返回控制器配置页面
                    navigationService.Navigate("ControllerConfigPage");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"下一步按钮点击处理出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"处理下一步操作时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        #endregion

        #region GSDML信息提取方法

        /// <summary>
        /// 从GSDML文件中提取DAP信息
        /// </summary>
        private DAPInfo? ExtractDAPInfoFromGSDML(string gsdPath, string gsdRefId)
        {
            try
            {
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"GSD引用ID: {gsdRefId}");

                // 如果gsdPath只是文件名，需要查找完整路径
                string fullGsdPath = FindFullGSDMLPath(gsdPath);
                if (string.IsNullOrEmpty(fullGsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}");
                    return null;
                }

                Debug.WriteLine($"找到完整GSDML路径: {fullGsdPath}");

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(fullGsdPath, GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {fullGsdPath}");
                    return null;
                }

                // 获取设备结构
                var deviceStructure = interpreter.GetDeviceStructureElement();
                if (deviceStructure == null)
                {
                    Debug.WriteLine("无法获取设备结构");
                    return null;
                }

                Debug.WriteLine($"设备结构获取成功，DAP数量: {deviceStructure?.DeviceAccessPoints?.Length ?? 0}");

                // 获取设备访问点
                var deviceAccessPoints = deviceStructure?.DeviceAccessPoints;
                if (deviceAccessPoints == null || deviceAccessPoints.Length == 0)
                {
                    Debug.WriteLine("设备没有访问点");
                    return null;
                }

                // 查找匹配的DAP（根据用户选择的特定DAP）
                PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement? targetDAP = null;

                // 首先尝试根据GSDRefID匹配
                foreach (var dap in deviceAccessPoints)
                {
                    if (dap is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement accessPoint)
                    {
                        Debug.WriteLine($"检查DAP: Name={accessPoint.Name}, GsdID={accessPoint.GsdID}");

                        // 匹配GsdID或Name
                        if (!string.IsNullOrEmpty(accessPoint.GsdID) && accessPoint.GsdID.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过GsdID): {accessPoint.GsdID}");
                            break;
                        }
                        else if (!string.IsNullOrEmpty(accessPoint.Name) && accessPoint.Name.Equals(gsdRefId, StringComparison.OrdinalIgnoreCase))
                        {
                            targetDAP = accessPoint;
                            Debug.WriteLine($"找到匹配的DAP (通过Name): {accessPoint.Name}");
                            break;
                        }
                    }
                }

                // 如果没有找到匹配的DAP，使用第一个DAP
                if (targetDAP == null && deviceAccessPoints.Length > 0)
                {
                    if (deviceAccessPoints.GetValue(0) is PNConfigLib.Gsd.Interpreter.Structure.AccessPointStructureElement firstDAP)
                    {
                        targetDAP = firstDAP;
                        Debug.WriteLine($"使用第一个DAP: {firstDAP.Name ?? firstDAP.GsdID}");
                    }
                }

                if (targetDAP == null)
                {
                    Debug.WriteLine("无法找到有效的DAP");
                    return null;
                }

                // 提取DAP信息
                var dapInfo = new DAPInfo();

                // 提取Category信息
                dapInfo.Category = !string.IsNullOrEmpty(targetDAP.Category) ? targetDAP.Category :
                                  (!string.IsNullOrEmpty(targetDAP.SubCategory1) ? targetDAP.SubCategory1 :
                                  (!string.IsNullOrEmpty(targetDAP.MainFamily) ? targetDAP.MainFamily : "未知"));

                // 提取OrderNumber信息
                dapInfo.OrderNumber = !string.IsNullOrEmpty(targetDAP.OrderNumber) ? targetDAP.OrderNumber : "未指定";

                // 提取Info信息
                dapInfo.Info = !string.IsNullOrEmpty(targetDAP.InfoText) ? targetDAP.InfoText : "无描述信息";

                Debug.WriteLine($"成功提取DAP信息:");
                Debug.WriteLine($"  Category: {dapInfo.Category}");
                Debug.WriteLine($"  OrderNumber: {dapInfo.OrderNumber}");
                Debug.WriteLine($"  Info: {dapInfo.Info}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 成功完成 ===");

                return dapInfo;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取DAP信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== ExtractDAPInfoFromGSDML 异常结束 ===");
                return null;
            }
        }

        /// <summary>
        /// 获取当前设备的GSDML路径（绝对路径）
        /// </summary>
        private string? GetCurrentGSDMLPath()
        {
            try
            {
                if (_projectManager?.CurrentProject == null || string.IsNullOrEmpty(_deviceName))
                {
                    return null;
                }

                // 从ListOfNodesConfiguration中获取设备节点信息
                var deviceNode = _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices
                    .FirstOrDefault(d => d.DeviceID == _deviceName);

                if (deviceNode == null)
                {
                    return null;
                }

                // 获取项目目录
                string projectDirectory = PNConfigTool.Utilities.PathHelper.GetProjectDirectory(_projectManager.CurrentProjectFilePath ?? "");

                // 将相对路径转换为绝对路径用于文件操作
                string relativePath = deviceNode.GSDPath ?? string.Empty;
                string absolutePath = PNConfigTool.Utilities.PathHelper.ToAbsolutePath(relativePath, projectDirectory);

                Debug.WriteLine($"ModuleConfigPage GSDML路径转换: {relativePath} -> {absolutePath}");
                return absolutePath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取GSDML路径时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查找GSDML文件的完整路径
        /// </summary>
        private string? FindFullGSDMLPath(string gsdFileName)
        {
            try
            {
                Debug.WriteLine($"开始查找GSDML文件: {gsdFileName}");

                // 如果已经是完整路径且文件存在，直接返回
                if (Path.IsPathRooted(gsdFileName) && File.Exists(gsdFileName))
                {
                    Debug.WriteLine($"文件已存在: {gsdFileName}");
                    return gsdFileName;
                }

                // 定义可能的搜索路径
                var searchPaths = new List<string>();

                // 1. 示例GSDML文件夹
                string exampleGsdmlPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GSDML_Example");
                if (Directory.Exists(exampleGsdmlPath))
                {
                    searchPaths.AddRange(Directory.GetDirectories(exampleGsdmlPath, "*", SearchOption.AllDirectories));
                    searchPaths.Add(exampleGsdmlPath);
                }

                // 2. 应用程序数据目录
                string appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PNConfigTool", "GSDML");
                if (Directory.Exists(appDataPath))
                {
                    searchPaths.Add(appDataPath);
                }

                // 3. 项目GSDMLs文件夹（如果有当前项目）
                var projectManager = ProjectManager.Instance;
                if (projectManager.CurrentProject != null && !string.IsNullOrEmpty(projectManager.CurrentProjectFilePath))
                {
                    string projectDirectory = Path.GetDirectoryName(projectManager.CurrentProjectFilePath) ?? "";
                    string projectGsdmlPath = Path.Combine(projectDirectory, "GSDMLs");
                    if (Directory.Exists(projectGsdmlPath))
                    {
                        searchPaths.Add(projectGsdmlPath);
                    }
                }

                // 在所有路径中搜索文件
                foreach (string searchPath in searchPaths)
                {
                    if (!Directory.Exists(searchPath)) continue;

                    // 直接匹配文件名
                    string fullPath = Path.Combine(searchPath, gsdFileName);
                    if (File.Exists(fullPath))
                    {
                        Debug.WriteLine($"找到GSDML文件: {fullPath}");
                        return fullPath;
                    }

                    // 尝试不区分大小写的搜索
                    var files = Directory.GetFiles(searchPath, "*.xml", SearchOption.TopDirectoryOnly);
                    foreach (string file in files)
                    {
                        if (string.Equals(Path.GetFileName(file), gsdFileName, StringComparison.OrdinalIgnoreCase))
                        {
                            Debug.WriteLine($"找到GSDML文件（不区分大小写）: {file}");
                            return file;
                        }
                    }
                }

                Debug.WriteLine($"未找到GSDML文件: {gsdFileName}");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找GSDML文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前设备的GSDRefID
        /// </summary>
        private string? GetCurrentGSDRefID()
        {
            try
            {
                if (_projectManager?.CurrentProject == null || string.IsNullOrEmpty(_deviceName))
                {
                    return null;
                }

                // 从ListOfNodesConfiguration中获取设备节点信息
                var deviceNode = _projectManager.CurrentProject.ListOfNodesConfiguration.DecentralDevices
                    .FirstOrDefault(d => d.DeviceID == _deviceName);

                return deviceNode?.GSDRefID;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取GSDRefID时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前模块的GSDRefID
        /// </summary>
        private string? GetCurrentModuleGSDRefID()
        {
            try
            {
                if (_currentModule == null)
                {
                    Debug.WriteLine("当前模块为空，无法获取模块GSDRefID");
                    return null;
                }

                // 返回当前模块的GSDRefID
                string? moduleGSDRefID = _currentModule.GSDRefID;
                Debug.WriteLine($"当前模块GSDRefID: {moduleGSDRefID ?? "null"}");

                return !string.IsNullOrEmpty(moduleGSDRefID) ? moduleGSDRefID : null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取模块GSDRefID时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从GSDML文件中提取模块信息
        /// </summary>
        private ModuleInfo? ExtractModuleInfoFromGSDML(string gsdPath, string moduleGSDRefID)
        {
            try
            {
                Debug.WriteLine($"=== ExtractModuleInfoFromGSDML 开始 ===");
                Debug.WriteLine($"GSDML路径: {gsdPath}");
                Debug.WriteLine($"模块GSD引用ID: {moduleGSDRefID}");

                // 如果gsdPath只是文件名，需要查找完整路径
                string fullGsdPath = FindFullGSDMLPath(gsdPath);
                if (string.IsNullOrEmpty(fullGsdPath))
                {
                    Debug.WriteLine($"GSDML文件不存在: {gsdPath}");
                    return null;
                }

                Debug.WriteLine($"找到完整GSDML路径: {fullGsdPath}");

                if (string.IsNullOrEmpty(moduleGSDRefID))
                {
                    Debug.WriteLine("模块GSDRefID为空");
                    return null;
                }

                // 使用缓存的Interpreter来解析GSDML文件
                var interpreter = GSDMLCacheService.GetCachedInterpreter(fullGsdPath, GSDI.ModelOptions.GSDCommon | GSDI.ModelOptions.GSDStructure);
                if (interpreter == null)
                {
                    Debug.WriteLine($"无法获取GSDML解析器: {fullGsdPath}");
                    return null;
                }

                // 首先尝试从Structure模型获取模块结构元素
                var moduleStructureElement = interpreter.GetModuleStructureElement(moduleGSDRefID);
                if (moduleStructureElement != null)
                {
                    Debug.WriteLine($"从Structure模型找到模块: {moduleGSDRefID}");

                    // 提取模块信息
                    var moduleInfo = new ModuleInfo();

                    // 提取简明标识（使用模块名称）
                    moduleInfo.ShortName = !string.IsNullOrEmpty(moduleStructureElement.Name) ? moduleStructureElement.Name : moduleGSDRefID;


                    // 提取订货号信息
                    moduleInfo.OrderNumber = !string.IsNullOrEmpty(moduleStructureElement.OrderNumber) ? moduleStructureElement.OrderNumber : "未指定";

                    // 提取描述信息
                    moduleInfo.Info = !string.IsNullOrEmpty(moduleStructureElement.InfoText) ? moduleStructureElement.InfoText : "无描述信息";

                    Debug.WriteLine($"成功提取模块信息:");
                    Debug.WriteLine($"  简明标识: {moduleInfo.ShortName}");
                    Debug.WriteLine($"  订货号: {moduleInfo.OrderNumber}");
                    Debug.WriteLine($"  描述: {moduleInfo.Info}");
                    Debug.WriteLine($"=== ExtractModuleInfoFromGSDML 成功完成 ===");

                    return moduleInfo;
                }

                // 如果Structure模型中没有找到，尝试从Common模型获取模块
                var commonModule = interpreter.GetModule(moduleGSDRefID);
                if (commonModule != null)
                {
                    Debug.WriteLine($"从Common模型找到模块: {moduleGSDRefID}");

                    // 提取模块信息
                    var moduleInfo = new ModuleInfo();

                    // 从模块信息中提取名称、订货号和描述
                    if (commonModule.Info != null && commonModule.Info is PNConfigLib.Gsd.Interpreter.Common.ModuleInfo commonModuleInfo)
                    {
                        // 提取简明标识（使用模块名称）
                        moduleInfo.ShortName = !string.IsNullOrEmpty(commonModuleInfo.Name) ? commonModuleInfo.Name : moduleGSDRefID;

                        // 提取订货号
                        moduleInfo.OrderNumber = !string.IsNullOrEmpty(commonModuleInfo.OrderNumber) ? commonModuleInfo.OrderNumber : "未指定";

                        // 提取描述信息
                        moduleInfo.Info = !string.IsNullOrEmpty(commonModuleInfo.InfoText) ? commonModuleInfo.InfoText : "无描述信息";
                    }
                    else
                    {
                        moduleInfo.ShortName = moduleGSDRefID;
                        moduleInfo.OrderNumber = "未指定";
                        moduleInfo.Info = "无描述信息";
                    }

                    Debug.WriteLine($"成功提取模块信息:");
                    Debug.WriteLine($"  简明标识: {moduleInfo.ShortName}");
                    Debug.WriteLine($"  订货号: {moduleInfo.OrderNumber}");
                    Debug.WriteLine($"  描述: {moduleInfo.Info}");
                    Debug.WriteLine($"=== ExtractModuleInfoFromGSDML 成功完成 ===");

                    return moduleInfo;
                }

                Debug.WriteLine($"无法找到匹配的模块: {moduleGSDRefID}");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"提取模块信息时出错: {ex.Message}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                Debug.WriteLine($"=== ExtractModuleInfoFromGSDML 异常结束 ===");
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// 模块信息结构
    /// </summary>
    public class ModuleInfo
    {
        public string? ShortName { get; set; }
        public string? OrderNumber { get; set; }
        public string? Info { get; set; }
    }
}