/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: BuilderV02_031.cs                         :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System;
using System.Xml;
using System.Xml.XPath;
using System.Collections;
using C = PNConfigLib.Gsd.Interpreter.Common;

namespace PNConfigLib.Gsd.Interpreter
{
    internal class BuilderV02031 :
        BuilderV0203
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the builder if it is instantiated.
        /// </summary>
        public BuilderV02031()
        {
            // Initialize the properties
            SetSupportedGsdmlVersion(Constants.s_Version231);
        }

        #endregion

        //########################################################################################
        #region Methods

        protected override bool InitExpressions()
        {
            bool succeeded = true;
            try
            {
                succeeded = base.InitExpressions();
                if (!succeeded)
                {
                    return false;
                }

                // Create document navigator.
                XPathNavigator nav = Gsd.CreateNavigator();

                if (nav == null)
                {
                    return false;
                }

                // Create the NamespaceManager and add all XML Namespaces to it.
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(nav.NameTable);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlDeviceProfile, Namespaces.s_GsdmlDeviceProfile);
                nsmgr.AddNamespace(Namespaces.s_PrefixGsdmlPrimitives, Namespaces.s_GsdmlPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixCommonPrimitives, Namespaces.s_CommonPrimitives);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchema, Namespaces.s_XmlSchema);
                nsmgr.AddNamespace(Namespaces.s_PrefixXmlSchemaInstance, Namespaces.s_XmlSchemaInstance);

                // pluggable port submodules
                XPathExpression expr = nav.Compile(XPathes.AllSystemDefinedChannelDiagItems);
                expr.SetContext(nsmgr);
                Expressions.Add(Elements.s_SystemDefinedChannelDiagItem, expr);
            }
            catch (XPathException)
            {
                succeeded = false;
            }

            return succeeded;
        }

        protected override object CreateGsdObject(string name, XPathNavigator nav)
        {
            Hashtable hash = new Hashtable();
            C.GsdObject obj = null;

            try
            {
                switch (name)
                {
                    case Models.s_ObjectSystemDefinedChannelDiagnostic:
                        {
                            // NOTE: Navigator must point to SystemDefinedChannelDiagItem.
                            this.PrepareSystemDefinedChannelDiagnostic(nav, ref hash);
                            obj = new C.SystemDefinedChannelDiagnostic();

                            break;
                        }
                }

                // Fill object with data.
                if (null != obj)
                {
                    bool succeeded = obj.Fill(hash);
                    if (!succeeded)
                        throw new PreparationException(name + " couldn't be filled with data!");
                }
                else
                    return base.CreateGsdObject(name, nav);	// Call base class creator.

            }
            catch (ArgumentException)
            {
                obj = null;
            }
            catch (PreparationException)
            {
                obj = null;
            }

            return obj;
        }

        #endregion

        //########################################################################################
        #region Common Model Methods

        #region Preparation

        protected override void PrepareInterfaceSubmodule(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMinRtc3Gap, null);
            hash.Add(Models.s_FieldMaxFrameStartTime, null);
            hash.Add(Models.s_FieldMinNrtGap, null);
            hash.Add(Models.s_FieldMinYellowTime, null);
            hash.Add(Models.s_FieldYellowSafetyMargin, null);
            hash.Add(Models.s_FieldIsMinRtc3Gap, null);
            hash.Add(Models.s_FieldIsMaxFrameStartTime, null);
            hash.Add(Models.s_FieldIsMinNrtGap, null);
            hash.Add(Models.s_FieldIsMinYellowTime, null);
            hash.Add(Models.s_FieldIsYellowSafetyMargin, null);
            hash.Add(Models.s_FieldPdevCombinedObjectSupported, null);

            // Call base class method first.
            base.PrepareInterfaceSubmodule(nav, ref hash);

            // MaxFrameStartTime attribute, optional
            hash[Models.s_FieldIsMaxFrameStartTime] = false;
            string attribute = nav.GetAttribute(Attributes.s_MaxFrameStartTime, String.Empty);
            if (attribute.Length != 0)
            {
                hash[Models.s_FieldIsMaxFrameStartTime] = true;
                hash[Models.s_FieldMaxFrameStartTime] = System.Xml.XmlConvert.ToUInt32(attribute);
            }

            // MinNRTGap attribute, optional
            hash[Models.s_FieldIsMinNrtGap] = false;
            attribute = nav.GetAttribute(Attributes.s_MinNrtGap, String.Empty);
            if (attribute.Length != 0)
            {
                hash[Models.s_FieldIsMinNrtGap] = true;
                hash[Models.s_FieldMinNrtGap] = System.Xml.XmlConvert.ToUInt32(attribute);
            }

            // MinNRTGap attribute, optional
            attribute = nav.GetAttribute(Attributes.s_PdevCombinedObjectSupported, String.Empty);
            hash[Models.s_FieldPdevCombinedObjectSupported] = Help.GetBool(
                attribute,
                Attributes.s_DefaultDelayMeasurementSupported);

            // Get RTClass3Properties info, optional
            XPathNodeIterator nodes = nav.SelectChildren(
                Elements.s_RTClass3Properties,
                Namespaces.s_GsdmlDeviceProfile);

            if (!nodes.MoveNext())
            {
                return;
            }

            // MinRTC3Gap attribute, optional
            hash[Models.s_FieldIsMinRtc3Gap] = false;
            if (nodes.Current == null)
            {
                return;
            }
            string attr = nodes.Current.GetAttribute(Attributes.s_MinRtc3Gap, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldIsMinRtc3Gap] = true;
                hash[Models.s_FieldMinRtc3Gap] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // MinYellowTime attribute, optional
            hash[Models.s_FieldIsMinYellowTime] = false;
            attr = nodes.Current.GetAttribute(Attributes.s_MinYellowTime, String.Empty);
            if (attr.Length != 0)
            {
                hash[Models.s_FieldIsMinYellowTime] = true;
                hash[Models.s_FieldMinYellowTime] = System.Xml.XmlConvert.ToUInt32(attr);
            }

            // YellowSafetyMargin attribute, optional
            hash[Models.s_FieldIsYellowSafetyMargin] = false;
            attr = nodes.Current.GetAttribute(Attributes.s_YellowSafetyMargin, String.Empty);
            if (attr.Length == 0)
            {
                return;
            }

            hash[Models.s_FieldIsYellowSafetyMargin] = true;
            hash[Models.s_FieldYellowSafetyMargin] = System.Xml.XmlConvert.ToUInt32(attr);
        }

        protected override void PrepareMediaRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldIsSupportedMultipleRoles, null);
            hash.Add(Models.s_FieldSupportedMultipleRoles, null);
            hash.Add(Models.s_FieldIsMaxMrpInstances, null);
            hash.Add(Models.s_FieldMaxMrpInstances, null);

            // Call base class method first.
            base.PrepareMediaRedundancy(nav, ref hash);

            // Get SupportedMultipleRole attribute. Optional.
            hash[Models.s_FieldIsSupportedMultipleRoles] = false;
            string attr = nav.GetAttribute(Attributes.s_SupportedMultipleRoles, String.Empty);
            ArrayList listMRRoles = new ArrayList();
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIsSupportedMultipleRoles] = true;
                ArrayList attrList = Help.SeparateTokenList(attr);
                foreach (string role in attrList)
                {
                    if (Enums.IsMRSupportedRolesEnumValueConvertable(role))
                        listMRRoles.Add(Enums.ConvertMRSupportedRolesEnum(role));
                }
            }
            hash[Models.s_FieldSupportedMultipleRoles] = listMRRoles;

            // Get MaxMRPInstances attribute. Optional.
            hash[Models.s_FieldIsMaxMrpInstances] = false;
            attr = nav.GetAttribute(Attributes.s_MaxMrpInstances, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldIsMaxMrpInstances] = true;
                hash[Models.s_FieldMaxMrpInstances] = XmlConvert.ToUInt32(attr);
            }
        }

        protected override void PrepareSystemRedundancy(XPathNavigator nav, ref Hashtable hash)
        {
            hash.Add(Models.s_FieldMinRdht, null);
            hash.Add(Models.s_FieldExistsMinRdht, null);
            // Call base class method first.
            base.PrepareSystemRedundancy(nav, ref hash);

            hash[Models.s_FieldExistsMinRdht] = false;

            string attr = nav.GetAttribute(Attributes.s_MinRdht, String.Empty);
            if (!String.IsNullOrEmpty(attr))
            {
                hash[Models.s_FieldExistsMinRdht] = true;
                hash[Models.s_FieldMinRdht] = XmlConvert.ToUInt32(attr);
            }
        }

        protected virtual void PrepareSystemDefinedChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldErrorType, null);
            hash.Add(Models.s_FieldProfileExtendedChannelDiagnostics, null);
            hash.Add(Models.s_FieldExtendedChannelDiagnostics, null);

            // Get error type attribute. Required.
            string attr = nav.GetAttribute(Attributes.s_ErrorType, String.Empty);
            hash[Models.s_FieldErrorType] = XmlConvert.ToUInt32(attr);

            // Navigate to ProfileExtChannelDiagItem elements and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ProfileExtChannelDiagItem, Namespaces.s_GsdmlDeviceProfile, false);
            object obj = null;
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = CreateGsdObject(Models.s_ObjectProfileExtendedChannelDiagnostic, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectProfileExtendedChannelDiagnostic + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldProfileExtendedChannelDiagnostics] = list;

            // Navigate to ExtChannelDiagItem elements and create it. Optional.
            nodes = nav.SelectDescendants(Elements.s_ExtChannelDiagItem, Namespaces.s_GsdmlDeviceProfile, false);
            list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectExtendedChannelDiagnostic, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtendedChannelDiagnostic + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtendedChannelDiagnostics] = list;
        }


        protected override void PrepareDevice(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare Device data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldSystemDefinedChannelDiagnostics, null);

            // Call base class method first.
            base.PrepareDevice(nav, ref hash);

            // Navigate to channel diagnostics and create it. Optional.
            object obj = null;
            ArrayList list = null;
            XPathNodeIterator nodes = nav.Select((XPathExpression)this.Expressions[Elements.s_SystemDefinedChannelDiagItem]);

            // Create each found item.
            while (nodes.MoveNext())
            {
                // Create list.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                obj = this.CreateGsdObject(Models.s_ObjectSystemDefinedChannelDiagnostic, nodes.Current);
                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectSystemDefinedChannelDiagnostic + "' couldn't be created!");

                // Add it to the list.
                list.Add(obj);
            }
            // Set hash variable.
            hash[Models.s_FieldSystemDefinedChannelDiagnostics] = list;
        }

        protected override void PrepareProfileChannelDiagnostic(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare ProfileChannelDiagnostic data with all sub components.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldExtendedChannelDiagnostics, null);

            // Call base class method first.
            base.PrepareProfileChannelDiagnostic(nav, ref hash);

            // Navigate to ExtChannelDiagItem elements and create it. Optional.
            XPathNodeIterator nodes = nav.SelectDescendants(Elements.s_ExtChannelDiagItem, Namespaces.s_GsdmlDeviceProfile, false);
            ArrayList list = null;

            // Create each found data object.
            while (nodes.MoveNext())
            {
                // Create list object at first time.
                if (null == list)
                    list = new ArrayList(nodes.Count);

                // Create data object.
                object obj = this.CreateGsdObject(Models.s_ObjectExtendedChannelDiagnostic, nodes.Current);

                if (null == obj)
                    throw new CreationException("Object '" + Models.s_ObjectExtendedChannelDiagnostic + "' couldn't be created!");

                list.Add(obj);	// Add it to the list.
            }

            // Set hash variable.
            hash[Models.s_FieldExtendedChannelDiagnostics] = list;
        }

        protected override void PrepareRTClass3TimingProperties(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for TimingProperties object.

            // Add all needed entries as null references to avoid later exceptions.
            hash.Add(Models.s_FieldMaxReductionRatioIsochroneMode, null);

            // Call base class method first.
            base.PrepareRTClass3TimingProperties(nav, ref hash);

            if (nav != null)
            {
                // Get MaxReductionRatioIsochroneMode attribute. Optional.
                string attr = nav.GetAttribute(Attributes.s_MaxReductionRatioIsochroneMode, String.Empty);
                if (attr.Length != 0)
                {
                    hash[Models.s_FieldMaxReductionRatioIsochroneMode] = System.Xml.XmlConvert.ToUInt32(attr);
                }
                else
                {
                    hash[Models.s_FieldMaxReductionRatioIsochroneMode] = Attributes.s_DefaultMaxReductionRatioIsochroneMode;
                }
            }
        }

        protected override void PrepareDeviceAccessPoint(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDeviceAccessPoint(nav, ref hash);

            hash.Add(Models.s_FieldIsCheckDeviceIdAllowed, null);

            string attr = nav.GetAttribute(Attributes.s_CheckDeviceIdAllowed, String.Empty);
            hash[Models.s_FieldIsCheckDeviceIdAllowed] = Help.GetBool(attr, Attributes.s_DefaultCheckDeviceIdAllowed);
        }

        protected override void PrepareDataItem(XPathNavigator nav, ref Hashtable hash)
        {
            base.PrepareDataItem(nav, ref hash);

            hash.Add(Models.s_FieldSubordinate, null);

            string attr = nav.GetAttribute(Attributes.s_Subordinate, String.Empty);
            hash[Models.s_FieldSubordinate] = Help.GetBool(attr, Attributes.s_DefaultSubordinate);
        }


        /// <summary>
        /// The F-parameter record data (with F_iPar_CRC) is a fixed 14 byte block with configuration data. It is
        /// organized as follows:
        /// 0	F_Prm_Flag1		(Unsigned8)		0x08 (8)
        ///			Bit 0:	xxx	(F_Check_SeqNr)		0
        ///			Bit 1:	F_Check_iPar			0 "No check"
        ///			Bit 2:	F_SIL					0 "SIL3"
        ///			Bit 3:							1
        ///			Bit 4:	F_CRC_Length			0 "3 Byte CRC"
        ///			Bit 5:							0
        ///			Bit 6:	F_CRC_Seed			    0
        ///			Bit 7:	xxx						0 "reserved"
        /// 1	F_Prm_Flag2		(Unsigned8)		0x40 (64)
        ///			Bit 0:	F_Passivation		    0
        ///			Bit 1:	xxx						0 "reserved"
        ///			Bit 2:	xxx						0 "reserved"
        ///			Bit 3:	F_Block_ID				0 "F-Host/F-Device relationship"
        ///			Bit 4:							0
        ///			Bit 5:							0
        ///			Bit 6:	F_Par_Version			1 "PROFIsafe V2.0"
        ///			Bit 7:							0
        /// 2	F_Source_Add	(Unsigned16)	0x00 (0)
        /// 3									0x01 (1)
        /// 4	F_Dest_Add		(Unsigned16)	0x00 (0)
        /// 5									0x01 (1)
        /// 6	F_WD_Time		(Unsigned16)	0x00 (0)
        /// 7									0x96 (150)
        /// 8   F_iPar_CRC		(Unsigned32)
        /// 12  F_Par_CRC		(Unsigned16)	0xBB (187)
        /// 
        /// If there is no F_iPar_CRC the F-parameter record data is a fixed 10 byte block with configuration data
        /// (see BuilderV02_00.cs).
        /// </summary>
        /// <returns>Const object with default binary settings with byte length 10.</returns>
        protected override void PrepareFParameterRecordData(XPathNavigator nav, ref Hashtable hash)
        {
            // Prepare data for FParameterRecordData object.
            base.PrepareFParameterRecordData(nav, ref hash);

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FIParCrc, Namespaces.s_GsdmlDeviceProfile);
            bool isiParCrc = false;
            if (nodes.MoveNext())
            {
                // If there is a F_iPar_CRC, use a FParameterRecord with 14 Bytes
                isiParCrc = true;
                hash[Models.s_FieldLength] = Attributes.s_ExtFixedFParameterRecordDataLength;
            }
            else
            {
                hash[Models.s_FieldLength] = Attributes.s_FixedFParameterRecordDataLength;
            }

            // Set Refs property.
            Hashtable h = new Hashtable();
            h.Add(Models.s_FieldDataType, null);
            h.Add(Models.s_FieldByteOffset, null);
            h.Add(Models.s_FieldBitOffset, null);
            h.Add(Models.s_FieldBitLength, null);
            h.Add(Models.s_FieldIsChangeable, null);
            h.Add(Models.s_FieldIsVisible, null);
            h.Add(Models.s_FieldName, null);
            h.Add(Models.s_FieldNameTextId, null);
            h.Add(Models.s_FieldHelp, null);
            h.Add(Models.s_FieldValueGsdId, null);
            h.Add(Models.s_FieldDefaultValue, null);
            h.Add(Models.s_FieldValues, null);
            h.Add(Models.s_FieldValueType, null);
            ArrayList refs = new ArrayList();
            object obj = PrepareFParameterRecordDataCreateRefFCheckIPar(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSil(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcLength(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFBlockID(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFParVersion(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFSourceAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFDestAdd(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFWdTime(nav, ref h);
            refs.Add(obj);
            if (isiParCrc)
            {
                // there is a F_iPar_CRC 
                obj = PrepareFParameterRecordDataCreateRefFiParCrc(nav, ref h);
                refs.Add(obj);
            }
            obj = PrepareFParameterRecordDataCreateRefFParCrc(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFCrcSeed(nav, ref h);
            refs.Add(obj);
            obj = PrepareFParameterRecordDataCreateRefFPassivation(nav, ref h);
            refs.Add(obj);

            hash[Models.s_FieldRefs] = refs;
        }

        /// <summary>
        /// F_CRC_Seed
        /// 
        /// Data type:		Bit
        /// Byte offset:	0
        /// Bit offset:		6
        /// Bit length:		1
        /// 
        ///	///	Parameter values:
        ///		0 = CRC_Seed16" (default)
        ///		1 = "CRC_Seed24/32"
        /// </summary>
        /// <returns>Ref object for the F_CRC_Seed parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFCrcSeed(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FCrcSeed;
            hash[Models.s_FieldNameTextId] = Elements.s_FCrcSeed;
            hash[Models.s_FieldDataType] = GSDI.DataTypes.GSDBit;
            hash[Models.s_FieldByteOffset] = (uint)0;
            hash[Models.s_FieldBitOffset] = (uint)6;
            hash[Models.s_FieldBitLength] = (uint)1;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = GSDI.ValueTypes.GSDConcrete;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Constants.s_Value, null);
            valuehash.Add(Constants.s_Text, null);
            valuehash.Add(Constants.s_TextId, null);

            ArrayList list = new ArrayList();

            bool isChangeable = false;  // Is initially set with default value valid for PROFIsafe < V2.6.1 and PROFIsafe >= V2.6.1!
            bool isVisible = false; // Element might be missing in GSD file (PROFIsafe < V2.6.1). In this case use visible=false.

            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FCrcSeed, Namespaces.s_GsdmlDeviceProfile);
            XPathNodeIterator nodesPassivation = nav.SelectChildren(Elements.s_FPassivation, Namespaces.s_GsdmlDeviceProfile);

            bool isCrcSeedAvailable = false;
            bool isPassivationAvailable = false;
            if (nodes.MoveNext())
                isCrcSeedAvailable = true;
            if (nodesPassivation.MoveNext())
                isPassivationAvailable = true;

            byte defaultValue = Enums.s_CrcSeed16Value;   // Is initially set with default value for PROFIsafe < V2.6.1!
            string allowedValues = Enums.s_CrcSeed16;     // Is initially set with default value for PROFIsafe < V2.6.1!
            if (isCrcSeedAvailable || isPassivationAvailable)
            {
                // If F_CRC_Seed or F_Passivation available, PROFIsafe >= V2.6.1 is implied!
                defaultValue = Enums.s_CrcSeed2432Value;      // Default value for PROFIsafe >= V2.6.1!
                allowedValues = Enums.s_CrcSeed2432;          // Default value for PROFIsafe >= V2.6.1!
            }

            if (isCrcSeedAvailable)
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_Changeable, String.Empty);
                    isChangeable = Help.GetBool(attr, false);

                    attr = nodes.Current.GetAttribute(Attributes.s_Visible, String.Empty);
                    isVisible = Help.GetBool(attr, true);

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Equals(Enums.s_CrcSeed16, StringComparison.Ordinal))
                        defaultValue = Enums.s_CrcSeed16Value;

                    attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                    if (!string.IsNullOrEmpty(attr))
                        allowedValues = attr;
                }
            }


            // CRCSeed16 value ------------------------------------
            C.GsdObject obj = null;
            if (allowedValues.IndexOf(Enums.s_CrcSeed16, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_CrcSeed16Value;
                valuehash[Constants.s_Text] = Enums.s_CrcSeed16;
                valuehash[Constants.s_TextId] = Enums.s_CrcSeed16;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // CRCSeed24/32 value --------------------------------------
            if (allowedValues.IndexOf(Enums.s_CrcSeed2432, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_CrcSeed2432Value;
                valuehash[Constants.s_Text] = Enums.s_CrcSeed2432;
                valuehash[Constants.s_TextId] = Enums.s_CrcSeed2432;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }

            hash[Models.s_FieldValues] = list;

            hash[Models.s_FieldIsChangeable] = isChangeable;
            hash[Models.s_FieldIsVisible] = isVisible;
            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }


        /// <summary>
        /// F_Passivation
        /// 
        /// Data type:		Bit
        /// Byte offset:	1
        /// Bit offset:		0
        /// Bit length:		1
        /// 
        ///	///	Parameter values:
        ///		0 = "Device/Module" (default)
        ///		1 = "Channel"
        /// </summary>
        /// <returns>Ref object for the F_Passivation parameter.</returns>
        protected virtual object PrepareFParameterRecordDataCreateRefFPassivation(XPathNavigator nav, ref Hashtable hash)
        {
            bool succeeded = true;

            // Set hash values.
            hash[Models.s_FieldName] = Elements.s_FPassivation;
            hash[Models.s_FieldNameTextId] = Elements.s_FPassivation;
            hash[Models.s_FieldDataType] = GSDI.DataTypes.GSDBit;
            hash[Models.s_FieldByteOffset] = (uint)1;
            hash[Models.s_FieldBitOffset] = (uint)0;
            hash[Models.s_FieldBitLength] = (uint)1;
            hash[Models.s_FieldHelp] = String.Empty;

            hash[Models.s_FieldValueGsdId] = String.Empty;
            hash[Models.s_FieldValueType] = GSDI.ValueTypes.GSDConcrete;

            // Build values.
            Hashtable valuehash = new Hashtable();
            valuehash.Add(Constants.s_Value, null);
            valuehash.Add(Constants.s_Text, null);
            valuehash.Add(Constants.s_TextId, null);

            string allowedValues = String.Empty;
            ArrayList list = new ArrayList();

            bool isChangeable = false; // Is initially set with default value!
            bool isVisible = false; // Element might be missing in GSD file (PROFIsafe < V2.6.1). In this case use visible=false.
            byte defaultValue = Enums.s_DeviceModuleValue; // Is initially set with default value!
            XPathNodeIterator nodes = nav.SelectChildren(Elements.s_FPassivation, Namespaces.s_GsdmlDeviceProfile);

            if (nodes.MoveNext())
            {
                if (nodes.Current != null)
                {
                    string attr = nodes.Current.GetAttribute(Attributes.s_Changeable, String.Empty);
                    isChangeable = Help.GetBool(attr, false);

                    attr = nodes.Current.GetAttribute(Attributes.s_Visible, String.Empty);
                    isVisible = Help.GetBool(attr, true);

                    attr = nodes.Current.GetAttribute(Attributes.s_DefaultValue, String.Empty);
                    if (attr.Equals(Enums.s_Channel, StringComparison.Ordinal))
                        defaultValue = 1;

                    attr = nodes.Current.GetAttribute(Attributes.s_AllowedValues, String.Empty);
                    allowedValues = attr;
                }
            }

            // Device/Module value ------------------------------------
            C.GsdObject obj = null;
            if (allowedValues.Length == 0 || allowedValues.IndexOf(Enums.s_DeviceModule, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_DeviceModuleValue;
                valuehash[Constants.s_Text] = Enums.s_DeviceModule;
                valuehash[Constants.s_TextId] = Enums.s_DeviceModule;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }
            // Channel value --------------------------------------
            if (allowedValues.IndexOf(Enums.s_Channel, StringComparison.Ordinal) != -1)
            {
                obj = new C.ValueItem();
                valuehash[Constants.s_Value] = Enums.s_ChannelValue;
                valuehash[Constants.s_Text] = Enums.s_Channel;
                valuehash[Constants.s_TextId] = Enums.s_Channel;
                succeeded = obj.Fill(valuehash);
                if (!succeeded)
                    throw new PreparationException(Models.s_ObjectValueItem + " couldn't be filled with data!");
                list.Add(obj);
            }

            hash[Models.s_FieldValues] = list;

            hash[Models.s_FieldIsChangeable] = isChangeable;
            hash[Models.s_FieldIsVisible] = isVisible;
            hash[Models.s_FieldDefaultValue] = defaultValue;

            // Create and fill RefData object.
            obj = new C.RefData();
            succeeded = obj.Fill(hash);
            if (!succeeded)
                throw new PreparationException(Models.s_ObjectRefData + " couldn't be filled with data!");

            return obj;	// ---------->
        }

        #endregion

        #endregion

    }
}
