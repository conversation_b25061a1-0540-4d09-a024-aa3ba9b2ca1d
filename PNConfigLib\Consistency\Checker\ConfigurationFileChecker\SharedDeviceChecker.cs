/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: SharedDeviceChecker.cs                    :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;
using System.Linq;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.ConfigReader.ListOfNodes;
using PNConfigLib.DataModel;
using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLCatalogObjects;
using PNConfigLib.HWCNBL.Constants;
using PNConfigLib.Importer.GSDImport.Helper;

using DecentralDeviceType = PNConfigLib.ConfigReader.Configuration.DecentralDeviceType;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    internal class SharedDeviceChecker : IConsistencyChecker
    {
        private Devices m_Devices;

        private List<Subnet> m_Subnets;

        private readonly ListOfNodes m_ListOfNodes;

        internal SharedDeviceChecker(Devices devices, List<Subnet> subnets, ListOfNodes lon)
        {
            m_Devices = devices;
            m_Subnets = subnets;
            m_ListOfNodes = lon;
        }

        public void Check()
        {
            AreSharedDeviceRefIDsValid();

            CheckSharedDevice();
        }

        private void AreSharedDeviceRefIDsValid()
        {
            foreach (DecentralDeviceType decentralDevice in m_Devices.DecentralDevice)
            {
                string gsdFileName = ListOfNodesChecker.GetGsdFileName(decentralDevice.DeviceRefID, m_ListOfNodes);

                foreach (SharedDeviceTypeAssignedIOController sharedIOController in decentralDevice.SharedDevice)
                {
                    IsAssignedIOControllerOfSharedDeviceExist(sharedIOController);

                    IsSharedDeviceInterfaceExist(sharedIOController.InterfaceRefID);

                    AreSharedDeviceModulesExist(decentralDevice, sharedIOController.SharedModule, gsdFileName);
                }
            }
        }

        private void AreSharedDeviceModulesExist(
            DecentralDeviceType decentralDevice,
            IEnumerable<SharedDeviceTypeAssignedIOControllerSharedModule> sharedModules,
            string gsdFileName)
        {
            foreach (var sharedModule in sharedModules)
            {
                if (decentralDevice.DeviceRefID != sharedModule.ModuleRefID
                    && !decentralDevice.Module.Exists(m => m.ModuleID == sharedModule.ModuleRefID))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_ModuleRefIDWithoutModuleUnderSharedDevice,
                        sharedModule.ModuleRefID,
                        decentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }

                AreSharedDeviceSubmodulesExist(decentralDevice, gsdFileName, sharedModule);
            }
        }

        private void AreSharedDeviceSubmodulesExist(
            DecentralDeviceType decentralDevice,
            string gsdFileName,
            SharedDeviceTypeAssignedIOControllerSharedModule sharedModule)
        {
            if (sharedModule.SharedSubmodule != null
                && sharedModule.SharedSubmodule.Count > 0)
            {
                List<string> virtualSubmoduleCatalogIds = InputCheckerHelper.GetAllVirtualSubmoduleCatalogIds(
                    decentralDevice,
                    gsdFileName,
                    sharedModule.ModuleRefID,
                    m_ListOfNodes);

                IsSharedSubmoduleExist(sharedModule, decentralDevice, virtualSubmoduleCatalogIds);
            }
        }

        private void IsSharedSubmoduleExist(
            SharedDeviceTypeAssignedIOControllerSharedModule sharedModule,
            DecentralDeviceType decentralDevice,
            IEnumerable<string> virtualSubmoduleCatalogIds)
        {
            foreach (var sharedSm in sharedModule.SharedSubmodule)
            {
                ModuleType currentXmlModule =
                    decentralDevice.Module.FirstOrDefault(m => m.ModuleID == sharedModule.ModuleRefID);

                bool xmlSubmoduleNotExistInConfiguration =
                    currentXmlModule != null
                    && !currentXmlModule.Submodule.Exists(sm => sm.SubmoduleID == sharedSm.SubmoduleRefID);

                if ((xmlSubmoduleNotExistInConfiguration && virtualSubmoduleCatalogIds == null)
                    || (xmlSubmoduleNotExistInConfiguration
                        && !virtualSubmoduleCatalogIds.Contains(sharedSm.SubmoduleRefID)))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice,
                        sharedSm.SubmoduleRefID,
                        decentralDevice.DeviceRefID,
                        sharedModule.ModuleRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsSharedDeviceInterfaceExist(string interfaceId)
        {
            if (!m_Devices.CentralDevice.Exists(
                    iod => iod.CentralDeviceInterface.InterfaceRefID == interfaceId))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_InterfaceDoesNotExistForSharedDevice,
                    interfaceId);
                throw new ConsistencyCheckException();
            }
        }

        private void IsAssignedIOControllerOfSharedDeviceExist(SharedDeviceTypeAssignedIOController sharedIOController)
        {
            if (!m_Devices.CentralDevice.Exists(iod => iod.DeviceRefID == sharedIOController.DeviceRefID))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IOControllerDoesNotExistForSharedDevice,
                    sharedIOController.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void DoesDeviceSupportSharedDevice(DecentralDeviceCatalog decentralDeviceCatalog, DecentralDeviceType xmlDecentralDevice)
        {
            bool sharedDeviceSupported =
                decentralDeviceCatalog.AttributeAccess.AttributeList.ContainsKey(
                    InternalAttributeNames.PnIoSharedDeviceSupported)
                && (bool)decentralDeviceCatalog.AttributeAccess.AttributeList[InternalAttributeNames
                    .PnIoSharedDeviceSupported];

            if (!sharedDeviceSupported
                && xmlDecentralDevice.SharedDevice.Count > 0)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SharedDeviceIsNotSupportedForDevice,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSharedDeviceVirtualSubmoduleCountValid(
            SharedDeviceTypeAssignedIOController sharedIOController,
            DecentralDeviceType xmlDecentralDevice,
            DecentralDeviceCatalog decentralDeviceCatalog)
        {
            if (sharedIOController.SharedModule.Exists(sm => sm.ModuleRefID == xmlDecentralDevice.DeviceRefID))
            {
                SharedDeviceTypeAssignedIOControllerSharedModule headModule =
                    sharedIOController.SharedModule.FirstOrDefault(
                        sm => sm.ModuleRefID == xmlDecentralDevice.DeviceRefID);

                if (decentralDeviceCatalog.VirtualSubmoduleList.Exists(
                        vsm => vsm.AttributeAccess.AttributeList.ContainsKey(InternalAttributeNames.PnSubslotNumber))
                    && (headModule.SharedSubmodule.Count == 0))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice,
                        xmlDecentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void IsSharedDeviceSubmoduleCountValid(
            SharedDeviceTypeAssignedIOController sharedIOController,
            DecentralDeviceType xmlDecentralDevice,
            ConfigReader.ListOfNodes.DecentralDeviceType lonDevice)
        {
            foreach (var sharedModule in sharedIOController.SharedModule)
            {
                if (sharedModule.ModuleRefID == xmlDecentralDevice.DeviceRefID)
                {
                    continue;
                }

                string moduleGsdId = xmlDecentralDevice.Module
                    .FirstOrDefault(sbm => sbm.ModuleID == sharedModule.ModuleRefID).GSDRefID;

                ModuleCatalog moduleCatalog =
                    ModuleCatalogHelper.GetModuleCatalogWithGsdPath(lonDevice.GSDPath, moduleGsdId);

                Dictionary<string, SlotRelation> pluggableSubSlots = moduleCatalog.PluggableSubmoduleList;

                if (pluggableSubSlots != null
                    && pluggableSubSlots.Count > 0
                    && sharedModule.SharedSubmodule.Count == 0)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice,
                        sharedModule.ModuleRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CheckSharedDeviceTag(
            DecentralDeviceCatalog decentralDeviceCatalog,
            DecentralDeviceType xmlDecentralDevice,
            ConfigReader.ListOfNodes.DecentralDeviceType lonDevice)
        {
            foreach (SharedDeviceTypeAssignedIOController sharedIOController in xmlDecentralDevice.SharedDevice)
            {
                IsSharedDeviceVirtualSubmoduleCountValid(sharedIOController, xmlDecentralDevice, decentralDeviceCatalog);

                IsSharedDeviceSubmoduleCountValid(sharedIOController, xmlDecentralDevice, lonDevice);
            }
        }

        private void IsInterfaceAvailableToShare(DecentralDeviceCatalog decentralDeviceCatalog, DecentralDeviceType xmlDecentralDevice)
        {
            AttributeAccessCode aac = new AttributeAccessCode();
            bool parametrizationDisallowed = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnParameterizationDisallowed,
                aac,
                false);

            if (aac.IsOkay && parametrizationDisallowed) // parametrization dis.
            {
                if (xmlDecentralDevice.SharedDevice.Exists(sharedIO => sharedIO.IsPDEVShared))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_InterfaceCannotBeUsedForSharedDevice,
                        xmlDecentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void HasDeviceCapatiyToShare(DecentralDeviceCatalog decentralDeviceCatalog, DecentralDeviceType xmlDecentralDevice)
        {
            uint numberOfAr = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<uint>(
                InternalAttributeNames.PnIoMaxNumberOfAR,
                new AttributeAccessCode(),
                1);

            uint iocOutsideProjectWithAccess = xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions
                .RealTimeSettings.IOCycle.SharedDevicePart.IOControllerOutsideProjectWithAccessToThisIODevice;

            if (numberOfAr < xmlDecentralDevice.SharedDevice.Count + iocOutsideProjectWithAccess)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_DeviceHasOverCapacityForSharedDevice,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void ValidatendClockAndSyncRole(DecentralDeviceType xmlDecentralDevice)
        {
            if (xmlDecentralDevice.SharedDevice.Exists(sd => sd.IsPDEVShared))
            {
                if (xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCycle
                        .SharedDevicePart.IODeviceSendClock > 0f)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Warning,
                        string.Empty,
                        ConsistencyConstants.XML_IODeviceSendClockForSharedIsNotRequired,
                        xmlDecentralDevice.DeviceRefID);
                }
            }
            else
            {
                if (xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                        .SynchronizationRole != SyncRole.Unsynchronized)
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_PDEVOwnerNotExistButSyncRoleDefined,
                        xmlDecentralDevice.DeviceRefID);
                    throw new ConsistencyCheckException();
                }
            }
        }

        private void CanSendClockBeSetForSharedDevice(DecentralDeviceType xmlDecentralDevice, DecentralDeviceCatalog decentralDeviceCatalog)
        {
            bool parametrizationDisallowed = decentralDeviceCatalog.Interface.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.PnParameterizationDisallowed,
                new AttributeAccessCode(),
                false);
            if (!xmlDecentralDevice.SharedDevice.Exists(sd => sd.IsPDEVShared)
                && !parametrizationDisallowed
                && xmlDecentralDevice.SharedDevice.Count > 0
                && xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings.IOCycle
                    .SharedDevicePart.IODeviceSendClock.Equals(0f))
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SendClockForSharedDeviceIsNotSet,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void AreHeadModuleAndPDEVCountValid(DecentralDeviceType xmlDecentralDevice)
        {
            int pdevCount = 0;
            int headModuleCount = 0;
            foreach (SharedDeviceTypeAssignedIOController sharedIOController in xmlDecentralDevice.SharedDevice)
            {
                if (sharedIOController.SharedModule.Exists(
                    sm => sm.ModuleRefID == xmlDecentralDevice.DeviceRefID))
                {
                    headModuleCount++;
                }

                if (sharedIOController.IsPDEVShared)
                {
                    pdevCount++;
                }
            }

            if (pdevCount > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_PDevCannotBeUsedMoreThanOne,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }

            if (headModuleCount > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_HeadModuleCannotBeUsedMoreThanOne,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void IsSharedDeviceSyncRoleValid(SharedDeviceTypeAssignedIOController sharedIOController, DecentralDeviceType xmlDecentralDevice)
        {
            if (sharedIOController.IsPDEVShared
                && m_Devices.CentralDevice
                    ?.FirstOrDefault(c => c.DeviceRefID == sharedIOController.DeviceRefID)
                    .CentralDeviceInterface.AdvancedOptions.RealTimeSettings.Synchronization
                    .SynchronizationRole == SyncRole.Unsynchronized
                && xmlDecentralDevice.DecentralDeviceInterface.AdvancedOptions.RealTimeSettings
                    .Synchronization.SynchronizationRole != SyncRole.Unsynchronized)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_SharedDeviceWrongSyncRole,
                    xmlDecentralDevice.DeviceRefID);
                throw new ConsistencyCheckException();
            }
        }

        private void FillIODSendClocks(SharedDeviceTypeAssignedIOController sharedIOController, List<float> iodSendClocks)
        {
            CentralDeviceType assignedCentralDevice =
                m_Devices.CentralDevice?.FirstOrDefault(
                    iod => iod.DeviceRefID == sharedIOController.DeviceRefID);

            if (assignedCentralDevice != null)
            {
                float iodSendClock;
                if (assignedCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                        .IOCommunication.SendClockSpecified
                    && !assignedCentralDevice.CentralDeviceInterface.AdvancedOptions.RealTimeSettings
                        .IOCommunication.SendClock.Equals(0f))
                {
                    iodSendClock = assignedCentralDevice.CentralDeviceInterface.AdvancedOptions
                        .RealTimeSettings.IOCommunication.SendClock;

                    if (!iodSendClocks.Contains(iodSendClock))
                    {
                        iodSendClocks.Add(iodSendClock);
                    }
                }
                else
                {
                    iodSendClock = m_Subnets.SelectMany(s => s.DomainManagement.SyncDomains)
                        .FirstOrDefault(
                            s => s.SyncDomainID
                                 == assignedCentralDevice.CentralDeviceInterface.AdvancedOptions
                                     .RealTimeSettings.Synchronization.SyncDomainRefID).SendClock;

                    if (!iodSendClock.Equals(0f))
                    {
                        iodSendClocks.Add(iodSendClock);
                    }
                }
            }
        }

        private void ValidateSharedModuleAndSubmoduleCount(
            SharedDeviceTypeAssignedIOController sharedIOController,
            List<string> sharedModules,
            List<string> sharedSubmodules)
        {
            foreach (SharedDeviceTypeAssignedIOControllerSharedModule sharedModule in sharedIOController.SharedModule)
            {
                if (sharedModule.SharedSubmodule.Count == 0)
                {
                    if (sharedModules.Contains(sharedModule.ModuleRefID))
                    {
                        ConsistencyLogger.Log(
                            ConsistencyType.XML,
                            LogSeverity.Error,
                            string.Empty,
                            ConsistencyConstants.XML_SharedModuleUsedMoreThanOne,
                            sharedModule.ModuleRefID);
                        throw new ConsistencyCheckException();
                    }

                    sharedModules.Add(sharedModule.ModuleRefID);
                }
                else
                {
                    foreach (SharedDeviceTypeAssignedIOControllerSharedModuleSharedSubmodule sharedSubmodule in
                        sharedModule.SharedSubmodule)
                    {
                        if (sharedSubmodules.Contains(sharedSubmodule.SubmoduleRefID))
                        {
                            ConsistencyLogger.Log(
                                ConsistencyType.XML,
                                LogSeverity.Error,
                                string.Empty,
                                ConsistencyConstants.XML_SharedSubmoduleUsedMoreThanOne,
                                sharedSubmodule.SubmoduleRefID);
                            throw new ConsistencyCheckException();
                        }

                        sharedSubmodules.Add(sharedSubmodule.SubmoduleRefID);
                    }
                }
            }
        }

        private void IsAssignedIOControllerValid(DecentralDeviceType xmlDecentralDevice)
        {
            List<string> sharedModules = new List<string>();
            List<string> sharedSubmodules = new List<string>();
            List<float> iodSendClocks = new List<float>();

            foreach (SharedDeviceTypeAssignedIOController sharedIOController in xmlDecentralDevice.SharedDevice)
            {
                IsSharedDeviceSyncRoleValid(sharedIOController, xmlDecentralDevice);

                FillIODSendClocks(sharedIOController, iodSendClocks);

                ValidateSharedModuleAndSubmoduleCount(sharedIOController, sharedModules, sharedSubmodules);
            }

            if (iodSendClocks.Count > 1)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Warning,
                    string.Empty,
                    ConsistencyConstants.XML_IOControllersOfIODeviceHasDifferentSendClock,
                    xmlDecentralDevice.DeviceRefID);
            }
        }

        private void CheckSharedDevice()
        {
            foreach (DecentralDeviceType xmlDecentralDevice in m_Devices.DecentralDevice)
            {
                if (xmlDecentralDevice.SharedDevice == null
                    || xmlDecentralDevice.SharedDevice.Count <= 0)
                {
                    continue;
                }

                ConfigReader.ListOfNodes.DecentralDeviceType lonDevice =
                    ListOfNodesChecker.GetListOfNodesDeviceById(xmlDecentralDevice.DeviceRefID, m_ListOfNodes);

                DecentralDeviceCatalog decentralDeviceCatalog =
                    DecentralDeviceCatalogHelper.GetDecentralDeviceCatalogWithGsdPath(
                        lonDevice.GSDPath,
                        lonDevice.GSDRefID);

                DoesDeviceSupportSharedDevice(decentralDeviceCatalog, xmlDecentralDevice);

                CheckSharedDeviceTag(decentralDeviceCatalog, xmlDecentralDevice, lonDevice);

                IsInterfaceAvailableToShare(decentralDeviceCatalog, xmlDecentralDevice);

                HasDeviceCapatiyToShare(decentralDeviceCatalog, xmlDecentralDevice);

                ValidatendClockAndSyncRole(xmlDecentralDevice);

                CanSendClockBeSetForSharedDevice(xmlDecentralDevice, decentralDeviceCatalog);

                AreHeadModuleAndPDEVCountValid(xmlDecentralDevice);

                IsAssignedIOControllerValid(xmlDecentralDevice);
            }
        }
    }
}
