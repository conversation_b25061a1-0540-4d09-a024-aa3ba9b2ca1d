/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: Module.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{

    /// <summary>
    /// The Module extends the abstract ModuleObject. It describes the 
    /// characterisitic of a Module of a PROFINET Device. In principle
    /// it is a container for the Submodules, which are the owner of the
    /// relevant input, output and parameter data.
    /// </summary>
    public class Module :
        ModuleObject,
        GSDI.IModule,
        GSDI.IModule2,
        GSDI.IModule3,
        GSDI.IModule4,
        GSDI.ICompatibility
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the Module if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public Module()
        {
            m_RequiredSchemaVersion = String.Empty;
        }

        #endregion

        //########################################################################################
        #region Fields

        // V1.0
        private ArrayList m_VirtualSubmodules;

        // V2.0
        private ArrayList m_SystemDefinedSubmodules;
        private ArrayList m_Subslots;
        private string m_RequiredSchemaVersion;

        // V2.1
        private List<uint> m_PhysicalSubslots;
        private ArrayList m_PhysicalSubmodules;
        private IGsdObjectDictionary m_SubmodulePlugData;
        private IGsdObjectDictionary m_SubmoduleQuickFinder;  // Only private!

        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the list of VirtualSubmodules available from this Module.
        /// </summary>
        public virtual Array VirtualSubmodules =>
            this.m_VirtualSubmodules?.ToArray();

        /// <summary>
        /// Accesses the list of VirtualSubmodules available from this Module.
        /// </summary>
        public virtual Array PhysicalSubmodules =>
            this.m_PhysicalSubmodules?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array SystemDefinedSubmodulesArray =>
            this.m_SystemDefinedSubmodules?.ToArray();

        /// <summary>
        /// ...
        /// </summary>
        public virtual Array Subslots =>
            this.m_Subslots?.ToArray();

        public virtual Array PhysicalSubslots =>
            (null != this.m_PhysicalSubslots) ?
                new ArrayList(this.m_PhysicalSubslots).ToArray() :
                null;

        /// <summary>
        /// Accesses the key value list of ModulePlugData, whereby each 
        /// plug data corresponds to a Module available from the Modules list.
        /// </summary>
        /// <remarks>Key is the GsdID of the Module and the corresponding value
        /// is the ModulePlugData object.</remarks>
        public virtual IDictionary SubmodulePlugData => m_SubmodulePlugData.CastToNonGenericDictionary();

        //		/// <summary>
        //		/// ...
        //		/// </summary>
        //		public bool IsPROFIsafeSupported
        //		{
        //			get { return this.m_IsPROFIsafeSupported; }
        //		}
        /// <summary>
        /// ...
        /// </summary>
        public string RequiredSchemaVersion => this.m_RequiredSchemaVersion;

        protected new object IdentNumber => ((ModuleObject)this).IdentNumber;

        public new GSDI.IModuleInfo Info => ((ModuleObject)this).Info;

        protected new GSDI.IGraphic GetGraphic(GSDI.GraphicTypes lType)
        {
            return ((ModuleObject)this).GetGraphic(lType);
        }

        protected GSDI.IModuleObject GetSubmoduleExt(string bstrGsdId)
        {
            return this.GetSubmodule(bstrGsdId);
        }

        #endregion

        //########################################################################################
        #region Methods

        // V2.0
        /// <summary>
        /// ...
        /// </summary>
        public virtual bool HasSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes lType)
        {
            // Check whether system defined submodules exist.
            if (null == this.SystemDefinedSubmodulesArray)
                return false;   // ---------->

            foreach (SystemDefinedSubmoduleObject pso in this.SystemDefinedSubmodulesArray)
            {
                // Check for Interface system defined submodule.
                //if (lType == GSDI.SystemDefinedSubmoduleTypes.GSD_All && 
                //	lType == GSDI.SystemDefinedSubmoduleTypes.GSD_Interface && pso is InterfaceSubmodule)
                //	return true;	// ---------->

                // Check for Port system defined submodule.
                if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDAll ||
                    lType == GSDI.SystemDefinedSubmoduleTypes.GSDPort || pso is PortSubmodule)
                    return true;    // ---------->
            }

            return false;
        }
        /// <summary>
        /// ...
        /// </summary>
        public virtual Array GetSystemDefinedSubmodules(GSDI.SystemDefinedSubmoduleTypes lType)
        {
            // Check whether system defined submodules exist.
            if (null == this.SystemDefinedSubmodulesArray)
                return null;    // ---------->

            // Check for all type.
            if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDAll)
                return this.SystemDefinedSubmodulesArray;   // ---------->

            // Fill array for returning.
            ArrayList list = new ArrayList();
            foreach (SystemDefinedSubmoduleObject pso in this.SystemDefinedSubmodulesArray)
            {
                // Check for Interface system defined submodule.
                //if (lType == GSDI.SystemDefinedSubmoduleTypes.GSD_Interface && pso is InterfaceSubmodule)
                //	list.Add(pso);

                // Check for Port system defined submodule.
                if (lType == GSDI.SystemDefinedSubmoduleTypes.GSDPort && pso is PortSubmodule)
                    list.Add(pso);
            }

            if (list.Count > 0)
                return list.ToArray();  // ---------->

            return null;
        }

        /// <summary>
        /// ...
        /// </summary>
        public virtual SystemDefinedSubmoduleObject GetSystemDefinedSubmodule(System.UInt32 lSubslot)
        {
            // Check whether system defined submodules exist.
            if (null == this.SystemDefinedSubmodulesArray)
                return null;    // ---------->

            // Search for system defined submodule with given slot number.
            foreach (SystemDefinedSubmoduleObject pso in this.SystemDefinedSubmodulesArray)
            {

                // Check slot number.
                if (pso.SubslotNumber == lSubslot)
                    return pso; // ---------->
            }
            return null;
        }
        /// <summary>
        /// ...
        /// </summary>
        public virtual Subslot GetSubslot(System.UInt32 lSubslot)
        {
            // Check whether subslots exist.
            if (null == this.Subslots)
                return null;    // ---------->

            // Search for subslot with given slot number.
            foreach (Subslot ssl in this.Subslots)
            {

                // Check slot number.
                if (ssl.SubslotNumber == lSubslot)
                    return ssl; // ---------->
            }
            return null;
        }


        /// <summary>
        /// Returns the ModulePlugData corresponding to the Module specified 
        /// with the given GsdID.
        /// </summary>
        /// <param name="bstrModuleGsdID">Is the GsdID of the Module for
        /// which the ModulePlugData should be accessed.</param>
        public virtual ModulePlugData GetSubmodulePlugData(string bstrModuleGsdID)
        {
            if (null != this.m_SubmodulePlugData)
            {
                if (this.m_SubmodulePlugData.ContainsKey(bstrModuleGsdID))
                    return this.m_SubmodulePlugData[bstrModuleGsdID] as ModulePlugData;
            }

            // Return null if no ModulePlugData is available for this GsdID.
            return null;

        }

        /// <summary>
        /// Returns the Submodule object from the Submodules list having the 
        /// specified GsdID.
        /// </summary>
        /// <param name="bstrGsdID">Is the GsdID of the Module, which should
        /// be accessed.</param>
        public virtual ModuleObject GetSubmodule(string bstrGsdID)
        {
            if (null != this.m_SubmoduleQuickFinder)
            {
                if (this.m_SubmoduleQuickFinder.ContainsKey(bstrGsdID))
                    return this.m_SubmoduleQuickFinder[bstrGsdID] as ModuleObject;
            }

            // If no Module or this Module didn't exist, return nothing.
            return null;
        }


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(System.Collections.Hashtable hash)
        {
            bool succeeded = true;

            try
            {
                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                FillFieldVirtualSubmodules(hash);

                // V2.0
                FillFieldSystemDefinedSubmodules(hash);

                FillFieldSubslots(hash);

                //				member = Models.FieldIsPROFIsafeSupported;
                //				if (hash.ContainsKey(member) && hash[member] is bool)
                //					this.m_IsPROFIsafeSupported = (bool) hash[member];

                FillFieldRequiredSchemaVersion(hash);

                FillFieldPhysicalSubslots(hash);

                FillFieldSubmodules(hash);

                FillFieldSubmodulePlugData(hash);

                // For better handling of Submodules in relation to the GsdID create private hashtable.
                if (null != m_PhysicalSubmodules)
                {
                    foreach (ModuleObject m in m_PhysicalSubmodules)
                    {
                        // If graphics exist, create hashtable.
                        if (null == m_SubmoduleQuickFinder)
                            m_SubmoduleQuickFinder = GsdObjectDictionaryFactory.CreateDictionary(true);

                        // Add graphic to quick finder hashtable.
                        m_SubmoduleQuickFinder.Add(m.GsdID, m);
                    }
                    if (null != m_SubmoduleQuickFinder)
                    {
                        m_PhysicalSubmodules = new ArrayList(m_SubmoduleQuickFinder.Values.ToArray());
                    }
                }


                // Base data.
                succeeded = base.Fill(hash);
            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }
        private void FillFieldSubmodulePlugData(Hashtable hash)
        {
            string member = Models.s_FieldSubmodulePlugData;
            if (hash.ContainsKey(member)
                && hash[member] is IGsdObjectDictionary)
                this.m_SubmodulePlugData = hash[member] as IGsdObjectDictionary;
        }

        private void FillFieldSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_PhysicalSubmodules = hash[member] as ArrayList;
        }

        private void FillFieldPhysicalSubslots(Hashtable hash)
        {
            string member = Models.s_FieldPhysicalSubslots;
            if (hash.ContainsKey(member)
                && hash[member] is List<uint>)
                this.m_PhysicalSubslots = hash[member] as List<uint>;
        }

        private void FillFieldRequiredSchemaVersion(Hashtable hash)
        {
            string member = Models.s_FieldRequiredSchemaVersion;
            if (hash.ContainsKey(member)
                && hash[member] is string)
                this.m_RequiredSchemaVersion = hash[member] as string;
        }

        private void FillFieldSubslots(Hashtable hash)
        {
            string member = Models.s_FieldSubslots;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_Subslots = hash[member] as ArrayList;
        }

        private void FillFieldSystemDefinedSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldSystemDefinedSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_SystemDefinedSubmodules = hash[member] as ArrayList;
        }

        private void FillFieldVirtualSubmodules(Hashtable hash)
        {
            string member = Models.s_FieldVirtualSubmodules;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_VirtualSubmodules = hash[member] as ArrayList;
        }



        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectModule);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {

            // ----------------------------------------------
            // From base class
            base.SerializeMembers(option, ref writer);

            // ----------------------------------------------
            Export.WriteComplexObjectListProperty(option, ref writer, Models.s_FieldVirtualSubmodules, this.m_VirtualSubmodules);

            // V2.0
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSystemDefinedSubmodules, this.m_SystemDefinedSubmodules);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldSubslots, this.m_Subslots);
            Export.WriteStringProperty(ref writer, Models.s_FieldRequiredSchemaVersion, this.m_RequiredSchemaVersion);

            // V2.1
            Export.WriteDictionaryListProperty(option, ref writer, Models.s_FieldSubmodulePlugData, m_SubmodulePlugData.CastToNonGenericDictionary());

            return true;
        }


        #endregion

    }
}


