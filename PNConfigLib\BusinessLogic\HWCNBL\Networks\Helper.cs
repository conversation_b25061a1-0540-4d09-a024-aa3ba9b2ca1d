/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: Helper.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.DataModel.AttributeUtilities;
using PNConfigLib.DataModel.PCLObjects;
using PNConfigLib.HWCNBL.Constants.AttributeValues;

#endregion

namespace PNConfigLib.HWCNBL.Networks
{
    /// <summary>
    /// Class for all static Routines not belonging to a special class
    /// </summary>
    internal static class Helper
    {
        //########################################################################################

        #region Fields

        // Contains all private fields (and also public fields in case there is a really
        // good reason to have some)

        #endregion

        //########################################################################################

        #region Properties

        // Contains all properties, that are not part of an interface implementation
        // those go into "Implementation of I..."
        // regardless of the access modifier (public, private, protected)
        #endregion

        //########################################################################################

        #region Public Methods

        #region methods for IE Nodes

        public static bool IsIPConfiguredInProject(PclObject node)
        {
            AttributeAccessCode aac = new AttributeAccessCode();
            bool ipProtocolUsed = node.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.NodeIPProtocolUsed,
                aac,
                false);

            if (!aac.IsOkay
                || !ipProtocolUsed)
            {
                return false;
            }

            NodeIPConfiguration nodeIPConfig = GetIPConfiguration(node);

            if (nodeIPConfig != NodeIPConfiguration.Project)
            {
                return false;
            }

            aac.Reset();
            bool pnIPSuiteViaOtherPath =
                node.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnPnIpSuiteViaOtherPath, aac, false);

            if (!aac.IsOkay || pnIPSuiteViaOtherPath)
            {
                return false;
            }

            aac.Reset();
            //Attribute at PROFINET devices
            bool pnIoIPSuiteViaOtherPath =
                node.AttributeAccess.GetAnyAttribute<bool>(InternalAttributeNames.PnIoIpSuiteViaOtherPath, aac, true);

            if (!aac.IsOkay || pnIoIPSuiteViaOtherPath)
            {
                return false;
            }

            return true;
        }

        public static bool IsIPProtocolUsed(PclObject node)
        {
            AttributeAccessCode aac = new AttributeAccessCode();
            bool isIPProtocolUsed = node.AttributeAccess.GetAnyAttribute<bool>(
                InternalAttributeNames.NodeIPProtocolUsed,
                new AttributeAccessCode(),
                false);
            if (aac.IsOkay)
            {
                return isIPProtocolUsed;
            }

            return false;
        }

        public static NodeIPConfiguration GetIPConfiguration(PclObject node)
        {
            if (IsIPProtocolUsed(node))
            {
                return
                   (NodeIPConfiguration) node.AttributeAccess.GetAnyAttribute<int>(
                        InternalAttributeNames.NodeIPConfiguration,
                        new AttributeAccessCode(),
                        (int)NodeIPConfiguration.None);
            }
            return NodeIPConfiguration.None;
        }

        #endregion

        #endregion
    }
}