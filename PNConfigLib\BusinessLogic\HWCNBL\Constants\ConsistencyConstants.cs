/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: ConsistencyConstants.cs                   :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

namespace PNConfigLib.HWCNBL.Constants
{
    /// <summary>
    /// The constants that are used for consistency check messages.
    /// </summary>
    internal static class ConsistencyConstants
    {
        #region  IP & Router & Subnet

        #region IP Address

        internal const string NodeIpRouterOtherThanIpAddress = "NodeIpRouterOtherThanIpAddress";
        internal const string IPAddressRestrictedSubnet = "IPAddressRestrictedSubnet";
        internal const string IPUnrestrictedSubnetMaskNotSupported = "IPUnrestrictedSubnetMaskNotSupported";
        internal const string IPMultipleAddress = "IPMultipleAddress";
        internal const string IPTooLongaddress = "IPTooLongaddress";
        internal const string IPBroadcastAddress = "IPBroadcastAddress";
        internal const string IPDefaultRouteAddress = "IPDefaultRouteAddress";
        internal const string IPEmptyAddress = "IPEmptyAddress";
        internal const string IPHostAddressNull = "IPHostAddressNull";
        internal const string IPLoopbackAddress = "IPLoopbackAddress";
        internal const string IPWrongAddress = "IPWrongAddress";

        #endregion

        #region Router Address

        internal const string IPRouterAddressClassInvalid = "IPRouterAddressClassInvalid";
        internal const string IPRouterAddressTooLong = "IPRouterAddressTooLong";
        internal const string IPRouterBroadcastAddress = "IPRouterBroadcastAddress";
        internal const string IPRouterDefaultRouteAddress = "IPRouterDefaultRouteAddress";
        internal const string IPRouterEmptyAddress = "IPRouterEmptyAddress";
        internal const string IPRouterWrongAddress = "IPRouterWrongAddress";
        internal const string IPRouterLoopbackAddress = "IPRouterLoopbackAddress";

        #region Default Router

        internal const string ErrorIE_IPRouteAddressDifferent = "ErrorIE_IPRouteAddressDifferent";
        internal const string XML_WrongDefaultRouterSettingInIOD = "XML_WrongDefaultRouterSettingInIOD";
        internal const string XML_DefaultIOSystemForMultipleIOC = "XML_DefaultIOSystemForMultipleIOC";

        #endregion

        #endregion

        #region Subnet Mask

        internal const string IPAddressDifferentSubnet = "IPAddressDifferentSubnet";
        internal const string IPWrongSubnetMask = "IPWrongSubnetMask";
        internal const string RouterIpDifferentFromIeIp = "RouterIpDifferentFromIeIp";
        internal const string SubnetMaskDifferenceInIOSystem = "SubnetMaskDifferenceInIOSystem";

        #endregion


        #endregion

        internal const string ErrorTooManySubmodules = "ErrorTooManySubmodules";

        internal const string AllPortsDeactivated = "AllPortsDeactivated";

        internal const string CalculatedBandwidthExceededMaximum = "CalculatedBandwidthExceededMaximum";

        internal const string CalculatedTotalBandwidthExceededMaximum = "CalculatedTotalBandwidthExceededMaximum";

        internal const string DeviceExchangeWithoutMMC = "DeviceExchangeWithoutMMC";

        internal const string DifferentSlotModels = "DifferentSlotModels";

        internal const string FastStartupDeviceWithoutIOSystem = "FastStartupDeviceWithoutIOSystem";

        internal const string NoCommonStartupFound = "NoCommonStartupFound";

        internal const string FastStartupMaxCountExceeded = "FastStartupMaxCountExceeded";

        internal const string IDeviceSendclockNotValid = "IDeviceSendclockNotValid";

        internal const string InputGrossFrameLengthExceeded = "InputGrossFrameLengthExceeded";

        internal const string InputNetFrameLengthExceeded = "InputNetFrameLengthExceeded";

        internal const string InterconnectedPortDeactivated = "InterconnectedPortDeactivated";

        internal const string PortDeactivationNotValid = "PortDeactivationNotValid";

        internal const string InterfaceSubmodulePortsDeactivated = "InterfaceSubmodulePortsDeactivated";

        internal const string InvalidIOSystemNumber = "InvalidIOSystemNumber";

        internal const string InvalidWatchdogFactor = "InvalidWatchdogFactor";

        internal const string IoAddressScheduleIsNotPossible = "IoAddressScheduleIsNotPossible";

        internal const string IOControllerDoesNotSupportIODevice = "IOControllerDoesNotSupportIODevice";

        internal const string IoSystemPNIpConfigIPAddressViaOtherPath = "IoSystemPNIpConfigIPAddressViaOtherPath";

        internal const string MaxStationNumberExceeded = "MaxStationNumberExceeded";

        internal const string MaxWatchdog = "MaxWatchdog";

        internal const string ModuleWithoutSubmodule = "ModuleWithoutSubmodule";

        internal const string NodeIeNoProtocol = "NodeIeNoProtocol";

        internal const string NodePNNoSInvalid = "NodePNNoSInvalid";

        internal const string NodePNNoSNotUnique = "NodePNNoSNotUnique";

        internal const string NoSActiveButIPSuiteNotActive = "NoSActiveButIPSuiteNotActive";

        internal const string NoUpdateTime = "NoUpdateTime";

        internal const string OutputGrossFrameLengthExceeded = "OutputGrossFrameLengthExceeded";

        internal const string OutputNetFrameLengthExceeded = "OutputNetFrameLengthExceeded";

        internal const string PNDriverPrioStartupNotAllowedError = "PNDriverPrioStartupNotAllowedError";

        internal const string PNDriverPrioStartupNotAllowedWarning = "PNDriverPrioStartupNotAllowedWarning";

        internal const string PortDifferentAutoNegotiation = "PortDifferentAutoNegotiation";

        internal const string PortMauNotSetOrAutonegotiationActivated = "PortMauNotSetOrAutonegotiationActivated";

        internal const string PortMediumDifferent = "PortMediumDifferent";

        internal const string PortTransferRateIsDifferent = "PortTransferRateIsDifferent";

        internal const string PortTransferRateNotSupported = "PortTransferRateNotSupported";

        internal const string PortTransferRateIsDifferentError = "PortTransferRateIsDifferentError";

        internal const string PortWrongValueForAutoNegotiation = "PortWrongValueForAutoNegotiation";

        internal const string SendclockNotValid = "SendclockNotValid";

        internal const string Msg_NoPortForInterface = "Msg_NoPortForInterface";

        internal const string SyncDomainEmptyName = "SyncDomainEmptyName";

        internal const string SyncDomainNameTooLong = "SyncDomainNameTooLong";

        internal const string SyncDomainNameNotUnique = "SyncDomainNameNotUnique";

        internal const string TotalGrossFrameLengthExceeded = "TotalGrossFrameLengthExceeded";

        internal const string TotalNetFrameLengthExceeded = "TotalNetFrameLengthExceeded";

        internal const string UpdateTimeNotApplicable = "UpdateTimeNotApplicable";

        internal const string StationNumberDuplication = "StationNumberDuplication";

        internal const string StationNumberDuplicationAndNotAvailablePosition =
            "StationNumberDuplicationAndNotAvailablePosition";

        internal const string StationNumberDownLimit = "StationNumberDownLimit";

        internal const string IoDNotAssignedToController = "IoDNotAssignedToController";

        internal const string IOAddressDataExceeded = "IOAddressDataExceeded";

        internal const string XML_PortWithGivenSubslotNumberDoesNotExist = "XML_PortWithGivenSubslotNumberDoesNotExist";

        internal const string XML_NotValid = "XML_NotValid";

        internal const string XML_NonExistingInterfaceRefIDInTopology = "XML_NonExistingInterfaceRefIDInTopology";

        internal const string XML_UnmatchedInterfaceRefIDInTopology = "XML_UnmatchedInterfaceRefIDInTopology";

        internal const string XML_ModuleIdUsedMoreThanOnce = "XML_ModuleIdUsedMoreThanOnce";

        internal const string XML_NonExistingDeviceRefIDInTopology = "XML_NonExistingDeviceRefIDInTopology";

        internal const string XML_NoPartnerPortAndItIsNotSetByUserProgram = "XML_NoPartnerPortAndItIsNotSetByUserProgram";

        internal const string XML_GSDMLPathUniqueness = "XML_GSDMLPathUniqueness";

        internal const string XML_SlotIsNotAvailableForRemovingModule = "XML_SlotIsNotAvailableForRemovingModule";

        internal const string XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice = "XML_SubModuleRefIDWithoutSubModuleUnderSharedDevice";

        internal const string XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule = "XML_SubModuleRefIDWithoutSubModuleUnderIsochronousModule";

        internal const string XML_SubmoduleIdUsedMoreThanOnce = "XML_SubmoduleIdUsedMoreThanOnce";

        internal const string XML_ModuleRefIDWithoutModuleUnderIsochronousModule = "XML_ModuleRefIDWithoutModuleUnderIsochronousModule";

        internal const string XML_ModuleRefIDWithoutModuleUnderSharedDevice = "XML_ModuleRefIDWithoutModuleUnderSharedDevice";

        internal const string XML_SendClockNotSet = "XML_SendClockNotSet";

        internal const string XML_SendClockMustBeUnderSyncDomain = "XML_SendClockMustBeUnderSyncDomain";

        internal const string XML_SendClockOfRtDeviceWrongLocation = "XML_SendClockOfRtDeviceWrongLocation";

        internal const string XML_SendClockAlreadySetInDevice = "XML_SendClockAlreadySetInDevice";

        internal const string XML_SendClockRtAndIrtInSameSyncDomain = "XML_SendClockRtAndIrtInSameSyncDomain";

        internal const string XML_InvalidSendClock = "XML_InvalidSendClock";

        internal const string XML_TypeOfIOAddressNotExist = "XML_TypeOfIOAddressNotExist";

        internal const string XML_InvalidCentralDevicePortCount = "XML_InvalidCentralDevicePortCount";

        internal const string XML_AddressTailoringActivatedButNotSupported = "XML_AddressTailoringActivatedButNotSupported";

        internal const string XML_IpSetDirectlyAtTheDeviceNotSupported = "XML_IpSetDirectlyAtTheDeviceNotSupported";

        internal const string XML_IPAddressSetWithAddressTailoring = "XML_IPAddressSetWithAddressTailoring";

        internal const string KeepApplicationRelationAtCommunicationErrorNotConfigurable = "KeepApplicationRelationAtCommunicationErrorNotConfigurable";

        internal const string ActivateDcpReadOnlyConfigurable = "ActivateDcpReadOnlyConfigurable";

        internal const string DcpEnableReadOnlyConfigurable = "DcpEnableReadOnlyConfigurable";
        #region IRT

        internal const string SendclockNotApplicable = "SendclockNotApplicable";

        internal const string ControllerNotSynchronized = "ControllerNotSynchronized";

        internal const string NoSViaOtherPathActiveWithIRTTop = "NoSViaOtherPathActiveWithIRTTop";

        internal const string MaxNoIrFrameDataExceeded = "MaxNoIrFrameDataExceeded";

        internal const string PortIrtLineLengthNotSpecified = "PortIrtLineLengthNotSpecified";

        internal const string PortMayNotBeBoundaryError = "PortMayNotBeBoundaryError";

        internal const string SyncDomainSyncMasterCount = "SyncDomainSyncMasterCount";

        internal const string SyncDomainSyncMasterIsNotComprised = "SyncDomainSyncMasterIsNotComprised";

        internal const string SyncDomainSecondarySyncMasterCount = "SyncDomainSecondarySyncMasterCount";

        internal const string SyncDomainNoCommonStartupMode = "SyncDomainNoCommonStartupMode";

        internal const string SyncMasterInvalidDistance = "SyncMasterInvalidDistance";

        internal const string SyncDomainBwLevelExceeded = "SyncDomainBwLevelExceeded";

        internal const string IncorrectReductionRatioWithSmallSC = "IncorrectReductionRatioWithSmallSC";

        internal const string SyncDomainMaxRedBwExceeded = "SyncDomainMaxRedBwExceeded";

        internal const string MaxRangeIRFrameIDOutOfRange = "MaxRangeIRFrameIDOutOfRange";

        internal const string SendclockInvalidInSyncDomain = "SendclockInvalidInSyncDomain";

        internal const string SyncDomainSmallSendClocksNotSupported = "SyncDomainSmallSendClocksNotSupported";

        internal const string SignalDelayTimeLessThanMinValue = "SignalDelayTimeLessThanMinValue";

        internal const string SignalDelayTimeMoreThanMaxValue = "SignalDelayTimeMoreThanMaxValue";

        internal const string SyncDomainNoRtcyFragModeWithRtcyDeviceExists = "SyncDomainNoRtcyFragModeWithRtcyDeviceExists";

        internal const string SyncDomainNoSyncMaster = "SyncDomainNoSyncMaster";

        internal const string SyncDomainNoSecSyncMasterWithIrtFlex = "SyncDomainNoSecSyncMasterWithIrtFlex";

        internal const string SyncDomainOnePortFragModeWithMultiplePortsActive =
            "SyncDomainOnePortFragModeWithMultiplePortsActive";

        internal const string ValidationExpertModeSupportedBwLevel = "ValidationExpertModeSupportedBwLevel";

        internal const string ValidationExpertModeSupportedSC = "ValidationExpertModeSupportedSC";

        internal const string MixedOperation_IRTTop_IRTFlex_at_SyncDomain =
            "MixedOperation_IRTTop_IRTFlex_at_SyncDomain";

        internal const string SyncDomainResBandwidthExceeded = "SyncDomainResBandwidthExceeded";

        internal const string Irt_With_Deactivated_Port = "Irt_With_Deactivated_Port";

        internal const string SyncDomainNoConnection = "SyncDomainNoConnection";

        internal const string SyncDomainNotAllowedPortInterconnection = "SyncDomainNotAllowedPortInterconnection";

        internal const string PNPlannerMultipleIslands = "PNPlannerMultipleIslands";

        internal const string PNPlannerToolChangerError = "PNPlannerToolChangerError";

        internal const string ControllerErrorMaxSubmoduleCount = "ControllerErrorMaxSubmoduleCount";

        internal const string ControllerErrorMaxSubmoduleDataLength = "ControllerErrorMaxSubmoduleDataLength";

        internal const string PNCONTROLLER_ERROR_MAX_DEVICES = "PNCONTROLLER_ERROR_MAX_DEVICES";

        internal const string Max_SyncSlaveNumber_Overstepped = "Max_SyncSlaveNumber_Overstepped";

        internal const string ExpertModeRequiredForFastForwarding = "ExpertModeRequiredForFastForwarding";

        internal const string FastForwardingWithIpv6NotSupported = "FastForwardingWithIpv6NotSupported";

        #endregion

        #region Isochron

        internal const string DelayTimeOutOfRange = "DelayTimeOutOfRange";

        internal const string InputPipSizeAgainstController = "InputPipSizeAgainstController";

        internal const string OutputPipSizeAgainstController = "OutputPipSizeAgainstController";

        internal const string TdcAgainstTdcminCheck = "TdcAgainstTdcminCheck";

        internal const string IsoCacfNotValid = "IsoCacfNotValid";

        internal const string ObControllerAppCycleTooHigh = "ObControllerAppCycleTooHigh";

        internal const string IsochronPIPRequiresIsochronSubmodule = "IsochronPIPRequiresIsochronSubmodule";

        internal const string UpdateTimeNotEqualSendClock = "UpdateTimeNotEqualSendClock";

        internal const string TiToCannotBeCalculated = "TiToCannotBeCalculated";

        internal const string ValidationErrorTiNotMultiplesOfRaster = "ValidationErrorTiNotMultiplesOfRaster";

        internal const string ValidationErrorToNotMultiplesOfRaster = "ValidationErrorToNotMultiplesOfRaster";

        internal const string ValidationErrorInvalidTi = "ValidationErrorInvalidTi";

        internal const string ValidationErrorInvalidTo = "ValidationErrorInvalidTo";

        internal const string TiToGreaterThanAppCycle = "TiToGreaterThanAppCycle";

        internal const string DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET =
            "DEVICE_ISOCHRONOUS_MODE_ACTIVATED_IRT_NOT_SET";

        internal const string IsoCouplingNotSupported = "IsoCouplingNotSupported";

        internal const string ISOCHRONOUS_REQUIRED_AT_SUBMODUL = "ISOCHRONOUS_REQUIRED_AT_SUBMODUL";

        internal const string SharedDevice_IsoNotPossibleWithSharedModule =
            "SharedDevice_IsoNotPossibleWithSharedModule";

        internal const string SharedDevice_IsoModuleNotPossibleWithSharedIF =
            "SharedDevice_IsoModuleNotPossibleWithSharedIF";

        internal const string ISOCHRONOUS_DISABLED_AT_INTERFACE = "ISOCHRONOUS_DISABLED_AT_INTERFACE";

        internal const string ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE =
            "ISOCHRONOUS_SUBMODUL_NOT_EXISTS_AT_ISOCHRONOUS_DEVICE";

        internal const string Clocksync_IOSystemIsNotAssignedToOB6x = "Clocksync_IOSystemIsNotAssignedToOB6x";

        internal const string IsochronPIPHasModulesFromDifferentMastersystems =
            "IsochronPIPHasModulesFromDifferentMastersystems";

        internal const string ControllerErrorMaxIAddressWithinAString = "ControllerErrorMaxIAddressWithinAString";

        internal const string ControllerErrorMaxOAddressWithinAString = "ControllerErrorMaxOAddressWithinAString";

        #endregion

        #region Structural Consistency Messages

        internal const string XML_InvalidSyncRole = "XML_InvalidSyncRole";

        internal const string XML_GSDMLExistence = "XML_GSDMLExistence";

        internal const string XML_InvalidNamePort = "XML_InvalidNamePort";

        internal const string XML_InvalidNameIpFormat = "XML_InvalidNameIpFormat";

        internal const string XML_EndOfTopologyDiscoveryIsNotValid = "XML_EndOfTopologyDiscoveryIsNotValid";

        internal const string XML_EndOfDetectionOfAccessibleDevicesIsNotValid = "XML_EndOfDetectionOfAccessibleDevicesIsNotValid";

        internal const string XML_EndOfTheSyncDomainIsNotValid = "XML_EndOfTheSyncDomainIsNotValid";

        internal const string XML_PortBoundariesSetForNonPDEVInterface = "XML_PortBoundariesSetForNonPDEVInterface";

        internal const string XML_InvalidPortSettingsForDeactivatedPort = "XML_InvalidPortSettingsForDeactivatedPort";

        internal const string XML_ParameterizationDisallowedInterfaceOptions = "XML_ParameterizationDisallowed_InterfaceOptions";

        internal const string XML_InterfaceNotSupported_InterfaceOptions = "XML_InterfaceNotSupported_InterfaceOptions";

        internal const string XML_InterfaceNotSupported_SyncRole = "XML_InterfaceNotSupported_SyncRole";

        internal const string XML_InterfaceNotSupported_MediaRedundancy = "XML_InterfaceNotSupported_MediaRedundancy";

        internal const string XML_ParameterizationDisallowed_SyncRole = "XML_ParameterizationDisallowed_SyncRole";

        internal const string XML_SharedDeviceWrongSyncRole = "XML_SharedDeviceWrongSyncRole";

        internal const string XML_PDEVOwnerNotExistButSyncRoleDefined = "XML_PDEVOwnerNotExistButSyncRoleDefined";

        internal const string XML_ParameterizationDisallowed_MediaRedundancy = "XML_ParameterizationDisallowed_MediaRedundancy";

        internal const string XML_ParameterRecordDataRefIDNotExist = "XML_ParameterRecordDataRefIDNotExist";

        internal const string XML_IoIpConfigMode = "XML_IoIpConfigMode";

        internal const string XML_InterfaceDoesNotExist = "XML_InterfaceDoesNotExist";

        internal const string XML_InterfaceParameterizationDisallowed = "XML_InterfaceParameterizationDisallowed";

        internal const string XML_WithoutSubnet = "XML_WithoutSubnet";

        internal const string XML_EmptyProject = "XML_EmptyProject";

        internal const string XML_SyncDomainRefIDWithoutSubnet = "XML_SyncDomainRefIDWithoutSubnet";

        internal const string XML_SyncDomainOfPDEV = "XML_SyncDomainOfPDEV";

        internal const string XML_IOSystemRefIDWithoutSubnet = "XML_IOSystemRefIDWithoutSubnet";

        internal const string XML_NullOrEmptyConfigXML = "XML_NullOrEmptyConfigXML";

        internal const string XML_NotExistConfigXML = "XML_NotExistConfigXML";

        internal const string XML_NotExistTopologyXML = "XML_NotExistTopologyXML";

        internal const string XML_NullOrEmptyListOfNodesXML = "XML_NullOrEmptyListOfNodesXML";

        internal const string XML_NumberOfPortsExceeded = "XML_NumberOfPortsExceeded";

        internal const string XML_NotExistListOfNodesXML = "XML_NotExistListOfNodesXML";

        internal const string XSD_ValidationError = "XSD_ValidationError";

        internal const string XSD_NamespaceNotValid = "XSD_NamespaceNotValid";

        internal const string XML_IncorrectGSDRefIDDevice = "XML_IncorrectGSDRefIDDevice";

        internal const string XML_IncorrectGSDRefIDPort = "XML_IncorrectGSDRefIDPort";

        internal const string XML_IncorrectGSDRefIDModule = "XML_IncorrectGSDRefIDModule";

        internal const string XML_IncorrectGSDRefIDSubmodule = "XML_IncorrectGSDRefIDSubmodule";

        internal const string XML_IncorrectListOfNodesRefIDInConfig = "XML_IncorrectListOfNodesRefIDInConfig";

        internal const string XML_IncorrectTopologyRefIDInConfig = "XML_IncorrectTopologyRefIDInConfig";

        internal const string XML_TopologyRefIDWithoutTopology = "XML_TopologyRefIDWithoutTopology";

        internal const string XML_DeviceRefIDWithoutCentralDevice = "XML_DeviceRefIDWithoutCentralDevice";

        internal const string XML_UnmatchedInterfaceRefIDForCentralDevice = "XML_UnmatchedInterfaceRefIDForCentralDevice";

        internal const string XML_SubnetNotFound = "XML_SubnetNotFound";

        internal const string XML_SubnetNotFoundForIOC = "XML_SubnetNotFoundForIOC";

        internal const string XML_SubnetNotFounfForIOC = "XML_SubnetNotFounfForIOC";

        internal const string XML_IoSystemNotFound = "XML_IoSystemNotFound";

        internal const string XML_IoSystemNotFounfForIOC = "XML_IoSystemNotFounfForIOC";

        internal const string XML_MultipleIOControllersInIOSystem = "XML_MultipleIOControllersInIOSystem";

        internal const string XML_SpecifiedSyncDomainNotFound = "XML_SpecifiedSyncDomainNotFound";

        internal const string XML_DeviceRefIDWithoutDecentralDevice = "XML_DeviceRefIDWithoutDecentralDevice";

        internal const string XML_UnmatchedInterfaceRefIDForDecentralDevice = "XML_UnmatchedInterfaceRefIDForDecentralDevice";

        internal const string XML_ParameterRecordDataIndexNotExist = "XML_ParameterRecordDataIndexNotExist";

        internal const string XML_ParameterRecordDataItemIndexNotUnique = "XML_ParameterRecordDataItemIndexNotUnique";

        internal const string XML_ParameterRecordDataNotAllowedValue = "XML_ParameterRecordDataNotAllowedValue";

        internal const string XML_ParameterRecordLengthIsOutOfLimit = "XML_ParameterRecordLengthIsOutOfLimit";

        internal const string XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain = "XML_IODNotConnectedToSubnetButToIOSystemAndSyncDomain";

        internal const string XML_IODNotConnectedToSubnetButToIOSystem = "XML_IODNotConnectedToSubnetButToIOSystem";

        internal const string XML_IODNotConnectedToSubnetButToSyncDomain = "XML_IODNotConnectedToSubnetButToSyncDomain";

        internal const string XML_SubnetNotFoundForIOD = "XML_SubnetNotFoundForIOD";

        internal const string XML_SubnetNotFounfForIOD = "XML_SubnetNotFounfForIOD";

        internal const string XML_IoSystemNotFoundForIOD = "XML_IoSystemNotFoundForIOD";

        internal const string XML_IoSystemNotFounfForIOD = "XML_IoSystemNotFounfForIOD";

        internal const string XML_SyncDomainNotFounfForIOD = "XML_SyncDomainNotFounfForIOD";

        internal const string XML_MaxIOSystemNumber = "XML_MaxIOSystemNumber";

        internal const string XML_IoSystemWithoutIOC = "XML_IoSystemWithoutIOC";

        internal const string XML_MultipleUseOfIOSystemNumber = "XML_MultipleUseOfIOSystemNumber";

        internal const string XML_IRTProjectWithoutTopology = "XML_IRTProjectWithoutTopology";

        internal const string XML_IncorrectListOfNodesRefIDInTopology = "XML_IncorrectListOfNodesRefIDInTopology";

        internal const string XML_IncorrectInterfaceRefID = "XML_IncorrectInterfaceRefID";

        internal const string XML_ModuleIsNotPluggableForDevice = "XML_ModuleIsNotPluggableForDevice";

        internal const string XML_SlotNumberIsUsedByMoreThanOneModule = "XML_SlotNumberIsUsedByMoreThanOneModule";

        internal const string XML_SlotIsNotAvailableForModule = "XML_SlotIsNotAvailableForModule";

        internal const string XML_CollisionInIOAddress = "XML_CollisionInIOAddress";

        internal const string XML_IOAddressRangeExceeded = "XML_IOAddressRangeExceeded";

        internal const string XML_IOAddressTypeConsistency = "XML_IOAddressTypeConsistency";

        internal const string XML_NotIOAddressTypedModule = "XML_NotIOAddressTypedModule";

        internal const string XML_IOModuleNotExist = "XML_IOModuleNotExist";

        internal const string XML_IOAddressOfVsmNotExist = "XML_IOAddressOfVsmNotExist";

        internal const string XML_VsmIOAddressesDefinedTwice = "XML_VsmIOAddressesDefinedTwice";

        internal const string XML_IOAddressesDefinitionOfMultipleVsm = "XML_IOAddressesDefinitionOfMultipleVsm";

        internal const string XML_SubslotIsNotAvailableForModule = "XML_SubslotIsNotAvailableForModule";

        internal const string XML_WrongSubslotNumberForVsm = "XML_WrongSubslotNumberForVsm";

        internal const string XML_SubslotNumberIsUsedByMoreThanOneModule = "XML_SubslotNumberIsUsedByMoreThanOneModule";

        internal const string XML_PortIsUsedMoreThanOneInTopology = "XML_PortIsUsedMoreThanOneInTopology";

        internal const string XML_PortSubmoduleNotHavePhysicalSubslot = "XML_PortSubmoduleNotHavePhysicalSubslot";

        internal const string XML_CollisionForPhysicalSubslot = "XML_CollisionForPhysicalSubslot";

        internal const string XML_NotValidPortGsdId = "XML_NotValidPortGsdId";

        internal const string XML_NotValidPortSubslotNumber = "XML_NotValidPortSubslotNumber";

        internal const string XML_SubslotNumberConfigurationIsWrong = "XML_SubslotNumberConfigurationIsWrong";

        internal const string XML_PortForModuleDoesNotExist = "XML_PortForModuleDoesNotExist";

        internal const string XML_SubslotNumberUsedMultipleTimesForPort = "XML_SubslotNumberUsedMultipleTimesForPort";

        internal const string XML_SubslotNumberForPortNotSuitable = "XML_SubslotNumberForPortNotSuitable";

        internal const string XML_PortNumberUsedMultipleTimes = "XML_PortNumberUsedMultipleTimes";

        internal const string XML_CentralDeviceVariantsNotSupported = "XML_CentralDeviceVariantsNotSupported";

        internal const string XML_DeviceVersionIsNotValid = "XML_DeviceVersionIsNotValid";

        internal const string XML_CustomInterfacePathNull = "XML_CustomInterfacePathNull";

        internal const string XML_CustomInterfacePathNotNull = "XML_CustomInterfacePathNotNull";

        internal const string XML_DeviceVersionNull = "XML_DeviceVersionNull";

        internal const string XML_InvalidCentralDeviceAttributeType = "XML_InvalidCentralDeviceAttributeType";

        internal const string XML_CustomInterfaceFileNotExist = "XML_CustomInterfaceFileNotExist";

        internal const string XML_SharedDeviceIsNotSupportedForDevice = "XML_SharedDeviceIsNotSupportedForDevice";

        internal const string XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice = "XML_AtLeastOneVirtualSubmoduleNeedForSharedDevice";

        internal const string XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice = "XML_ModuleHasToHaveAtLeastOneSubmoduleForSharedDevice";

        internal const string XML_InterfaceCannotBeUsedForSharedDevice = "XML_InterfaceCannotBeUsedForSharedDevice";

        internal const string XML_DeviceHasOverCapacityForSharedDevice = "XML_DeviceHasOverCapacityForSharedDevice";

        internal const string XML_IODeviceSendClockForSharedIsNotRequired = "XML_IODeviceSendClockForSharedIsNotRequired";

        internal const string XML_SendClockForSharedDeviceIsNotSet = "XML_SendClockForSharedDeviceIsNotSet";

        internal const string XML_IOControllerDoesNotExistForSharedDevice = "XML_IOControllerDoesNotExistForSharedDevice";

        internal const string XML_InterfaceDoesNotExistForSharedDevice = "XML_InterfaceDoesNotExistForSharedDevice";

        internal const string XML_IOControllersOfIODeviceHasDifferentSendClock = "XML_IOControllersOfIODeviceHasDifferentSendClock";

        internal const string XML_PDevCannotBeUsedMoreThanOne = "XML_PDevCannotBeUsedMoreThanOne";

        internal const string XML_HeadModuleCannotBeUsedMoreThanOne = "XML_HeadModuleCannotBeUsedMoreThanOne";

        internal const string XML_SharedModuleUsedMoreThanOne = "XML_SharedModuleUsedMoreThanOne";

        internal const string XML_SharedSubmoduleUsedMoreThanOne = "XML_SharedSubmoduleUsedMoreThanOne";

        internal const string XML_IOSystemAndSyncDomainNotDeclaredForIod = "XML_IOSystemAndSyncDomainNotDeclaredForIod";

        internal const string XML_SharedDeviceIOSystemCheck = "XML_SharedDeviceIOSystemCheck";

        internal const string XML_SharedDeviceSyncDomainCheck = "XML_SharedDeviceSyncDomainCheck";

        internal const string XML_SharedDeviceAssignedControllerUniqueness = "XML_SharedDeviceAssignedControllerUniqueness";

        internal const string XML_MultipleUseIOSystemIsNotSelected =
            "XML_MultipleUseIOSystemIsNotSelected";

        internal const string XML_DecentralDeviceIsNotUsedInConfiguration = "XML_DecentralDeviceIsNotUsedInConfiguration";

        internal const string XML_CentralDeviceIsNotUsedInConfiguration = "XML_CentralDeviceIsNotUsedInConfiguration";

        internal const string XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement = "XML_PermitOverwritingNotPossibleWithoutSupportDeviceReplacement";

        internal const string XML_InterconnectedPortOwnerDeleted = "XML_InterconnectedPortOwnerDeleted";

        internal const string XML_TopologyDeviceSlotCheck = "XML_TopologyDeviceSlotCheck";

        internal const string XML_TopologyInvalidPortNumber = "XML_TopologyInvalidPortNumber";

        internal const string XML_SendClockAlreadyGivenInSyncDomain = "XML_SendClockAlreadyGivenInSyncDomain";

        internal const string XML_IRTNotSupportedForIOControllerInterface = "XML_IRTNotSupportedForIOControllerInterface";

        internal const string XML_BothModuleAndSubmoduleHasParameterRecord = "XML_BothModuleAndSubmoduleHasParameterRecord";

        internal const string XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord = "XML_ModuleHasMultipleVirtualSubmoduleCannotHaveParameterRecord";

        internal const string XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord = "XML_IODeviceHasMultipleVirtualSubmoduleCannotHaveParameterRecord";

        internal const string XML_BothIODeviceAndSubmoduleHasParameterRecord = "XML_BothIODeviceAndSubmoduleHasParameterRecord";

        internal const string XML_ParameterRecordDataIsNotChangeable = "XML_ParameterRecordDataIsNotChangeable";

        internal const string XML_ParameterRecordFormatIsNotValid = "XML_ParameterRecordFormatIsNotValid";

        internal const string XML_ParameterRecordOctetUnsignedFormatIsNotValid = "XML_ParameterRecordOctetUnsignedFormatIsNotValid";

        internal const string XML_ParameterRecordDateFormatIsNotValid = "XML_ParameterRecordDateFormatIsNotValid";

        internal const string XML_ParameterRecordOctetStringLengthExceeded = "XML_ParameterRecordOctetStringLengthExceeded";

        internal const string XML_RestrictedVersionForExpertMode = "XML_RestrictedVersionForExpertMode";

        internal const string XML_DecentralDeviceIsMoreThanOnceInConfiguration =
            "XML_DecentralDeviceIsMoreThanOnceInConfiguration";

        internal const string XML_CentralDeviceIsMoreThanOnceInConfiguration =
            "XML_CentralDeviceIsMoreThanOnceInConfiguration";

        internal const string XML_ParameterizationDisallowed_InterfacePort = "XML_ParameterizationDisallowed_InterfacePort";

        internal const string XML_DnsConvert_PNDeviceNameLengthExceeded = "XML_DnsConvert_PNDeviceNameLengthExceeded";

        #region MRP

        internal const string XML_MrpInstanceNumberConsistency = "XML_MrpInstanceNumberConsistency";

        internal const string XML_MrpInstanceNumberUniqueness = "XML_MrpInstanceNumberUniqueness";

        internal const string XML_MrpRingCount = "XML_MrpRingCount";

        internal const string XML_MrpRingInstanceNumberIsSpecified = "XML_MrpRingInstanceNumberIsSpecified";

        internal const string XML_MrpDomainRefIdConsistency = "XML_MrpDomainRefIdConsistency";

        internal const string XML_MrpdNotSupported = "XML_MrpdNotSupported";

        internal const string XML_UpdateTimeMustBeSetInManualMode = "XML_UpdateTimeMustBeSetInManualMode";

        internal const string XML_UpdateTimeMustNotBeSetInAutomaticMode = "XML_UpdateTimeMustNotBeSetInAutomaticMode";

        #endregion

        #endregion

        #region Mrp Messages

        internal const string MultipleMrpRoleNotSupported = "MultipleMrpRoleNotSupported";

        internal const string MrpDomainUsedInDifferentInstances = "MrpDomainUsedInDifferentInstances";

        internal const string MrpRoleIsNotSupported = "MrpRoleIsNotSupported";

        internal const string MrpRingPortUsedInDifferentInstances = "MrpRingPortUsedInDifferentInstances";

        internal const string MrpNoNetwork = "MrpNoNetwork";

        internal const string MrpFastStartupNotAllowed = "MrpFastStartupNotAllowed";

        internal const string MrpSyncNotAllowed = "MrpSyncNotAllowed";

        internal const string MrpWrongPortNumber = "MrpWrongPortNumber";

        internal const string MrpPortInterconnDifferentSubnet = "MrpPortInterconnDifferentSubnet";

        internal const string MrpPortInterconnNonRingPort = "MrpPortInterconnNonRingPort";

        internal const string MrpPortTransferRateNotAllowed = "MrpPortTransferRateNotAllowed";

        internal const string MrpPortEndOfTopologyDiscovery = "MrpPortEndOfTopologyDiscovery";

        internal const string DisabledPortUsed = "DisabledPortUsed";

        internal const string MrpPortShouldBeSelected = "MrpPortShouldBeSelected";

        internal const string MrpMoreThanOneManager = "MrpMoreThanOneManager";

        internal const string MrpManagerAndManagerAuto = "MrpManagerAndManagerAuto";

        internal const string MrpNoManager = "MrpNoManager";

        internal const string MrpMaxSizeExceeded = "MrpMaxSizeExceeded";

        internal const string MrpMoreThanOneRing = "MrpMoreThanOneRing";

        internal const string MrpRoleOutsideTheRing = "MrpRoleOutsideTheRing";

        internal const string RingWithUnknownMediaRedundancy = "RingWithUnknownMediaRedundancy";

        internal const string RingWithoutMediaRedundancy = "RingWithoutMediaRedundancy";

        internal const string RingWithoutActiveMrp = "RingWithoutActiveMrp";

        internal const string RingWithMrpSelectedPortMismatch = "RingWithMrpSelectedPortMismatch";

        #endregion

        #region SharedDevice

        internal const string SharedDeviceAllSubmodulesShared = "SharedDeviceAllSubmodulesShared";

        internal const string SharedDevicePrioritizedStartupNotPossible = "SharedDevicePrioritizedStartupNotPossible";

        internal const string SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned = "SharedDeviceIRTNotPossibleWhenInterfaceNotAssigned";

        internal const string SharedDeviceIsoNotPossibleWithSharedIF = "SharedDeviceIsoNotPossibleWithSharedIF";

        internal const string SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules = "SharedDeviceUnevenSendclockNotPossibleWithNotAssignedSubmodules";

        #endregion

        #region Tailoring
        internal const string PNIpConfig_DeviceNameUsingDifferentMethod = "PNIpConfig_DeviceNameUsingDifferentMethod";
        internal const string CoupledIDeviceOnlyOneIOSystemShouldBeTailored = "CoupledIDeviceOnlyOneIOSystemShouldBeTailored";
        internal const string DomainManagement_OtherIOSystemMemberInMrpDomain = "DomainManagement_OtherIOSystemMemberInMrpDomain";
        internal const string DomainManagement_OtherIOSystemMemberInSyncDomain = "DomainManagement_OtherIOSystemMemberInSyncDomain";
        internal const string IDeviceControllerCentralPdevAssigned = "IDeviceControllerCentralPdevAssigned";
        internal const string InterfaceOptions_DeviceReplacementWithoutExchangableMedium = "InterfaceOptions_DeviceReplacementWithoutExchangableMedium";
        internal const string IOSystem_MachineTailoringEnabled_IOC = "IOSystem_MachineTailoringEnabled_IOC";
        internal const string Mrp_PortInterconn_NonRingPort = "Mrp_PortInterconn_NonRingPort";
        internal const string MultiDeployable_NoSharedDevice = "MultiDeployable_NoSharedDevice";
        internal const string Optional_Device_Incompatible_Mrp_Role = "Optional_Device_Incompatible_Mrp_Role";
        internal const string Optional_Device_Incompatible_Sync_Role = "Optional_Device_Incompatible_Sync_Role";
        internal const string Optional_Device_Interconnected_With_Tool_Changer_Port = "Optional_Device_Interconnected_With_Tool_Changer_Port";
        internal const string Optional_Device_Iod_Pdev_Assigned_Iocontroller = "Optional_Device_Iod_Pdev_Assigned_Iocontroller";
        internal const string Optional_Device_Mau_Type_Warning = "Optional_Device_Mau_Type_Warning";
        internal const string Optional_Device_Max_Fixed_Peer_Limit_Exceeded =   "Optional_Device_Max_Fixed_Peer_Limit_Exceeded";
        internal const string Optional_Device_Mrp_Ring_Port_Error = "Optional_Device_Mrp_Ring_Port_Error";
        internal const string Optional_Device_With_Set_Boundaries = "Optional_Device_With_Set_Boundaries";
        internal const string Optional_Device_With_Tool_Changer_Port = "Optional_Device_With_Tool_Changer_Port";
        internal const string Optional_IDevice_With_No_SuperOrdinate = "Optional_IDevice_With_No_SuperOrdinate";
        internal const string OverwriteProfinetDeviceName = "OverwriteProfinetDeviceName";
        internal const string PdevAssignedToController = "PdevAssignedToController";
        internal const string PNIpConfig_IPAddressUsingDifferentMethod = "PNIpConfig_IPAddressUsingDifferentMethod";
        internal const string Port_ProgrammablePeer_Not_In_Ring = "Port_ProgrammablePeer_Not_In_Ring";
        internal const string Port_ProgrammablePeer_PdevAssignedToController = "Port_ProgrammablePeer_PdevAssignedToController";
        internal const string PortInterconnection_PartnerPortFromSameIoSystem = "PortInterconnection_PartnerPortFromSameIoSystem";
        internal const string PortInterconnection_TopologyReachableFromController = "PortInterconnection_TopologyReachableFromController";
        internal const string ProgrammablePeerPortDeactivated = "ProgrammablePeerPortDeactivated";

        internal const string IfWithAdditionalRedundancy = "IfWithAdditionalRedundancy";
        internal const string ControllerErrorMaxSharedRTCXConsumer = "ControllerErrorMaxSharedRTCXConsumer";
        internal const string ControllerErrorMaxSharedRTCXProvider = "ControllerErrorMaxSharedRTCXProvider";
        internal const string ControllerErrorMaxSharedRTC3Consumer = "ControllerErrorMaxSharedRTC3Consumer";
        internal const string ControllerErrorMaxSharedRTC3Provider = "ControllerErrorMaxSharedRTC3Provider";
        internal const string ControllerErrorMaxSharedRTC1Consumer = "ControllerErrorMaxSharedRTC1Consumer";
        internal const string ControllerErrorMaxSharedRTC1Provider = "ControllerErrorMaxSharedRTC1Provider";


        #endregion

        #region Snmp
        internal const string XML_InvalidSNMPEnableReadOnly = "XML_InvalidSNMPEnableReadOnly";

        internal const string XML_EmptyCommunityName = "XML_EmptyCommunityName";

        internal const string XML_MaxCharSizeCommunityName = "XML_MaxCharSizeCommunityName";

        internal const string XML_InvalidCharCommunityName = "XML_InvalidCharCommunityName";
        #endregion
    }
}