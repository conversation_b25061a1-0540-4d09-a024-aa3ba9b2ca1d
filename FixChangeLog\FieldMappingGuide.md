# 新配置文件架构字段映射指南

## 概述
新的配置文件架构将原有的混合结构分离为清晰的层次结构，每个部分都能直接映射到对应的XML输出文件。

## 1. ListOfNodes映射

### 1.1 根节点映射
```
新配置文件路径 → ListOfNodes.xml路径
ListOfNodesConfiguration.ListOfNodesID → ListOfNodes@ListOfNodesID
ListOfNodesConfiguration.SchemaVersion → ListOfNodes@schemaVersion
```

### 1.2 PNDriver映射
```
ListOfNodesConfiguration.PNDriver.DeviceID → PNDriver@DeviceID
ListOfNodesConfiguration.PNDriver.DeviceName → PNDriver@DeviceName
ListOfNodesConfiguration.PNDriver.DeviceVersion → PNDriver@DeviceVersion
ListOfNodesConfiguration.PNDriver.Interface.InterfaceID → PNDriver/Interface@InterfaceID
ListOfNodesConfiguration.PNDriver.Interface.InterfaceName → PNDriver/Interface@InterfaceName
ListOfNodesConfiguration.PNDriver.Interface.InterfaceType → PNDriver/Interface@InterfaceType
ListOfNodesConfiguration.PNDriver.Interface.CustomInterfacePath → PNDriver/Interface@CustomInterfacePath
```

### 1.3 DecentralDevice映射
```
ListOfNodesConfiguration.DecentralDevices[].DeviceID → DecentralDevice@DeviceID
ListOfNodesConfiguration.DecentralDevices[].DeviceName → DecentralDevice@DeviceName
ListOfNodesConfiguration.DecentralDevices[].GSDPath → DecentralDevice@GSDPath
ListOfNodesConfiguration.DecentralDevices[].GSDRefID → DecentralDevice@GSDRefID
ListOfNodesConfiguration.DecentralDevices[].Interface.InterfaceID → DecentralDevice/Interface@InterfaceID
ListOfNodesConfiguration.DecentralDevices[].Interface.InterfaceName → DecentralDevice/Interface@InterfaceName
```

## 2. Configuration映射

### 2.1 根节点映射
```
ConfigurationSettings.ConfigurationID → Configuration@ConfigurationID
ConfigurationSettings.ConfigurationName → Configuration@ConfigurationName
ConfigurationSettings.ListOfNodesRefID → Configuration@ListOfNodesRefID
ConfigurationSettings.SchemaVersion → Configuration@schemaVersion
```

### 2.2 General映射
```
ConfigurationSettings.General.Author → Configuration/General<AUTHOR> → Configuration/General@Comment
ConfigurationSettings.General.Name → Configuration/General@Name
```

### 2.3 CentralDevice映射
```
ConfigurationSettings.CentralDevice.DeviceRefID → Configuration/Devices/CentralDevice@DeviceRefID
ConfigurationSettings.CentralDevice.General.Name → Configuration/Devices/CentralDevice/General@Name
ConfigurationSettings.CentralDevice.General.Comment → Configuration/Devices/CentralDevice/General@Comment
ConfigurationSettings.CentralDevice.CentralDeviceInterface.InterfaceRefID → Configuration/Devices/CentralDevice/CentralDeviceInterface@InterfaceRefID
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses/IPProtocol/SetInTheProject@IPAddress
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses/IPProtocol/SetInTheProject@SubnetMask
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.RouterAddress → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses/IPProtocol/SetInTheProject@RouterAddress
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses/PROFINETDeviceName/PNDeviceName
ConfigurationSettings.CentralDevice.AdvancedConfiguration.RealTimeSettings.IOCommunication.SendClock → Configuration/Devices/CentralDevice/AdvancedConfiguration/RealTimeSettings/IOCommunication@SendClock
```

### 2.4 DecentralDevice映射
```
ConfigurationSettings.DecentralDevices[].DeviceRefID → Configuration/Devices/DecentralDevice@DeviceRefID
ConfigurationSettings.DecentralDevices[].General.Name → Configuration/Devices/DecentralDevice/General@Name
ConfigurationSettings.DecentralDevices[].General.Comment → Configuration/Devices/DecentralDevice/General@Comment
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.InterfaceRefID → Configuration/Devices/DecentralDevice/DecentralDeviceInterface@InterfaceRefID
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.IPAddress → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses/IPProtocol/SetInTheProject@IPAddress
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.IPProtocol.SetInTheProject.SubnetMask → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses/IPProtocol/SetInTheProject@SubnetMask
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.PNDeviceName → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses/PROFINETDeviceName/PNDeviceName
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.PROFINETDeviceName.DeviceNumber → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses/PROFINETDeviceName@DeviceNumber
```

### 2.5 Module和Submodule映射
```
ConfigurationSettings.DecentralDevices[].Modules[].ModuleRefID → Configuration/Devices/DecentralDevice/Module@ModuleRefID
ConfigurationSettings.DecentralDevices[].Modules[].SlotNumber → Configuration/Devices/DecentralDevice/Module@SlotNumber
ConfigurationSettings.DecentralDevices[].Modules[].General.Name → Configuration/Devices/DecentralDevice/Module/General@Name
ConfigurationSettings.DecentralDevices[].Modules[].General.Comment → Configuration/Devices/DecentralDevice/Module/General@Comment
ConfigurationSettings.DecentralDevices[].Modules[].Submodules[].SubmoduleRefID → Configuration/Devices/DecentralDevice/Module/Submodule@SubmoduleRefID
ConfigurationSettings.DecentralDevices[].Modules[].Submodules[].General.Name → Configuration/Devices/DecentralDevice/Module/Submodule/General@Name
ConfigurationSettings.DecentralDevices[].Modules[].Submodules[].General.Comment → Configuration/Devices/DecentralDevice/Module/Submodule/General@Comment
```

### 2.6 EthernetAddresses引用映射（新增）
```
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.SubnetRefID → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses@SubnetRefID
ConfigurationSettings.CentralDevice.CentralDeviceInterface.EthernetAddresses.IOSystemRefID → Configuration/Devices/CentralDevice/CentralDeviceInterface/EthernetAddresses@IOSystemRefID
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.SubnetRefID → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses@SubnetRefID
ConfigurationSettings.DecentralDevices[].DecentralDeviceInterface.EthernetAddresses.IOSystemRefID → Configuration/Devices/DecentralDevice/DecentralDeviceInterface/EthernetAddresses@IOSystemRefID
```

### 2.7 Subnet映射
```
ConfigurationSettings.Subnets[].SubnetID → Configuration/Subnet@SubnetID
ConfigurationSettings.Subnets[].IOSystems[].IOSystemID → Configuration/Subnet/IOSystem@IOSystemID
ConfigurationSettings.Subnets[].IOSystems[].General.IOSystemNumber → Configuration/Subnet/IOSystem/General@IOSystemNumber
ConfigurationSettings.Subnets[].IOSystems[].General.IOSystemName → Configuration/Subnet/IOSystem/General@IOSystemName
ConfigurationSettings.Subnets[].DomainManagement.SyncDomains[].SyncDomainID → Configuration/Subnet/DomainManagement/SyncDomains/SyncDomain@SyncDomainID
ConfigurationSettings.Subnets[].DomainManagement.SyncDomains[].SyncDomainName → Configuration/Subnet/DomainManagement/SyncDomains/SyncDomain@SyncDomainName
ConfigurationSettings.Subnets[].DomainManagement.SyncDomains[].Details.BandwidthUse → Configuration/Subnet/DomainManagement/SyncDomains/SyncDomain/Details@BandwidthUse
ConfigurationSettings.Subnets[].DomainManagement.MrpDomains[].MrpDomainID → Configuration/Subnet/DomainManagement/MrpDomains/MrpDomain@MrpDomainID
ConfigurationSettings.Subnets[].DomainManagement.MrpDomains[].MrpDomainName → Configuration/Subnet/DomainManagement/MrpDomains/MrpDomain@MrpDomainName
```

## 3. 保留字段说明

### 3.1 UI相关字段（保留在新架构中）
- `UIProperties.Index` - 界面显示索引
- `UIProperties.IsSelected` - 选择状态
- `UIProperties.Position` - 位置信息

### 3.2 地址配置字段（保留在新架构中）
- `AddressConfiguration.InputStartAddress` - 输入起始地址
- `AddressConfiguration.OutputStartAddress` - 输出起始地址
- `AddressConfiguration.InputAddress` - 输入地址
- `AddressConfiguration.OutputAddress` - 输出地址

### 3.3 项目特定扩展字段
这些字段保留在 `ProjectSpecificExtensions` 节中，不直接映射到标准XML，但可用于项目特定的处理逻辑。

## 4. 命名规范说明

### 4.1 ID命名规范
- 所有ID字段使用标准XSD命名：`DeviceID`, `InterfaceID`, `ModuleRefID`, `SubmoduleRefID`
- 引用字段使用`RefID`后缀：`DeviceRefID`, `InterfaceRefID`, `ListOfNodesRefID`

### 4.2 结构命名规范
- 顶级节点使用描述性名称：`ListOfNodesConfiguration`, `ConfigurationSettings`
- 子节点严格遵循XSD元素名称：`CentralDevice`, `DecentralDevice`, `EthernetAddresses`

### 4.3 属性命名规范
- 网络配置使用标准名称：`IPAddress`, `SubnetMask`, `RouterAddress`
- 设备名称使用标准格式：`PNDeviceName`, `DeviceName`
- 引用字段使用标准后缀：`SubnetRefID`, `IOSystemRefID`, `SyncDomainRefID`

## 5. 新增字段说明（补充）

### 5.1 SubnetRefID 和 IOSystemRefID
这两个字段建立了设备与网络拓扑的引用关系：
- `SubnetRefID`: 引用Configuration中定义的Subnet的SubnetID
- `IOSystemRefID`: 引用Configuration中定义的IOSystem的IOSystemID

### 5.2 SyncDomain配置
同步域配置包含以下关键字段：
- `SyncDomainID`: 同步域的唯一标识符
- `SyncDomainName`: 同步域的显示名称
- `Details.BandwidthUse`: 带宽使用配置，支持25%、50%、75%、90%等选项

### 5.3 引用关系示例
```
设备配置中的引用：
EthernetAddresses.SubnetRefID = "PNIE_1"
EthernetAddresses.IOSystemRefID = "IOSystem1"

对应的Subnet配置：
Subnet.SubnetID = "PNIE_1"
Subnet.IOSystems[0].IOSystemID = "IOSystem1"
```
