/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: InterpreterGsdGsdml                       :C&  */
/*                                                                           */
/*  F i l e               &F: IOData.cs                                 :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Namespaces
using System;
using System.Collections;
#endregion

namespace PNConfigLib.Gsd.Interpreter.Common
{
    /// <summary>
    /// The IOData object defines characteristics of the IO data of 
    /// a submodule.
    /// </summary>
    public class IOData :
        GsdObject,
        GSDI.IIOData
    {
        //########################################################################################
        #region Initialization & Termination

        /// <summary>
        /// Initializes the IOData if it is instantiated.
        /// The properties of the object are all initialized to empty 
        /// or with abstract default values.
        /// </summary>
        /// <remarks>The real content can only be written to the object by the Fill
        /// method, which is only available within this assembly.</remarks>
        public IOData()
        {
            m_InputLength = 0;
            m_OutputLength = 0;
            m_InputConsistency = GSDI.IOConsistencies.GSDType;
            m_OutputConsistency = GSDI.IOConsistencies.GSDType;
            m_IopsLength = 1;
            m_IocsLength = 1;
            m_FioStructureDescCrc = 0;
            m_FioStructureDescVersion = 1;
        }

        #endregion

        //########################################################################################
        #region Fields

        // 
        private uint m_InputLength;
        private uint m_OutputLength;
        private GSDI.IOConsistencies m_InputConsistency;
        private GSDI.IOConsistencies m_OutputConsistency;
        private ArrayList m_InputDataItems;
        private ArrayList m_OutputDataItems;
        private uint m_IopsLength;
        private uint m_IocsLength;

        // 
        private uint m_FioStructureDescCrc;
        private uint m_FioStructureDescVersion;

        private ArrayList m_InputChannels;
        private ArrayList m_OutputChannels;


        #endregion

        //########################################################################################
        #region Properties

        /// <summary>
        /// Accesses the length of the input data items as bit.
        /// </summary>
        public UInt32 InputLength => this.m_InputLength;

        /// <summary>
		/// Accesses the length of the output data items as bit.
		/// </summary>
		public UInt32 OutputLength => this.m_OutputLength;

        /// <summary>
		/// Accesses the consistency of the input data.
		/// </summary>
		/// <remarks>If this attribute is set to GSD_Type, the consistency is 
		/// only given within a input DataItem.
		/// If this attribute is set to GSD_Complete, the submodule provides 
		/// consistency for all input data items.</remarks>
		public GSDI.IOConsistencies InputConsistency => this.m_InputConsistency;

        /// <summary>
		/// Accesses the consistency of the output data.
		/// </summary>
		/// <remarks>If this attribute is set to GSD_Type, the consistency is 
		/// only given within a output DataItem.
		/// If this attribute is set to GSD_Complete, the submodule provides 
		/// consistency for all output data items.</remarks>
		public GSDI.IOConsistencies OutputConsistency => this.m_OutputConsistency;

        /// <summary>
		/// Accesses the list of data items available for the input data.
		/// </summary>
		public virtual Array InputDataItems =>
            this.m_InputDataItems?.ToArray();

        /// <summary>
		/// Accesses the list of data items available for the output data.
		/// </summary>
		public virtual Array OutputDataItems =>
            this.m_OutputDataItems?.ToArray();

        /// <summary>
		/// Accesses the Length of the IO producer status in octets as part 
		/// of an IO data object.
		/// </summary>
		public UInt32 IOPSLength => this.m_IopsLength;

        /// <summary>
		/// Accesses the Length of the IO consumer status in octets as part 
		/// of an IO data object.
		/// </summary>
		public UInt32 IOCSLength => this.m_IocsLength;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 FIOStructureDescCRC => this.m_FioStructureDescCrc;

        /// <summary>
		/// ...
		/// </summary>
		public UInt32 FIOStructureDescVersion => this.m_FioStructureDescVersion;

        /// <summary>
        /// Accesses the list of channels available for the input data.
        /// </summary>
        public virtual Array InputChannels =>
            this.m_InputChannels?.ToArray();

        /// <summary>
        /// Accesses the list of channels available for the output data.
        /// </summary>
        public virtual Array OutputChannels =>
            this.m_OutputChannels?.ToArray();

        #endregion

        //########################################################################################
        #region Methods

        /// <summary>
        /// Calculates the length of the DataItems contained in the list
        /// given as parameter.
        /// </summary>
        /// <param name="dataitems">List of DataItem objects.</param>
        /// <returns>Bit length of all DataItems.</returns>
        protected virtual uint GetBitLength(ArrayList dataitems)
        {
            if (dataitems == null)
            {
                return 0;
            }

            // Get length of all items and add its results.
            uint res = 0;

          
                foreach (DataItem obj in dataitems)
                    res += obj.DataLength;
            

            return res;
        }


        #endregion

        //########################################################################################
        #region GsdObject Members

        /// <summary>
        /// Fills the created object with all relevant data, which is needed
        /// for the properties.
        /// </summary>
        /// <param name="hash">Hashtable which contains name value pairs for
        /// all properties available from this object.</param>
        /// <returns>True, if filling was successfull, else false.</returns>
        internal override bool Fill(Hashtable hash)
        {
            bool succeeded = true;

            try
            {

                // Check parameter.
                if (null == hash)
                    throw new FillException("The input hashtable parameter couldn't be 'null'!");

                // Own data.

                FillFieldInputConsistency(hash);

                FillFieldOutputConsistency(hash);

                FillFieldInputDataItems(hash);

                FillFieldOutputDataItems(hash);

                FillFieldIOPSLength(hash);

                FillFieldIOCSLength(hash);

                FillFieldFIOStructureDescCRC(hash);

                FillFieldFIOStructureDescVersion(hash);

                FillFieldInputChannels(hash);

                FillFieldOutputChannels(hash);

                

                // Calculate length for input and output data.
                if (null != this.m_InputDataItems)
                    this.m_InputLength = this.GetBitLength(this.m_InputDataItems);
                if (null != this.m_OutputDataItems)
                    this.m_OutputLength = this.GetBitLength(this.m_OutputDataItems);

            }
            catch (FillException)
            {
                succeeded = false;
            }

            return succeeded;
        }


        private void FillFieldOutputChannels(Hashtable hash)
        {
            string member = Models.s_FieldOutputChannels;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_OutputChannels = hash[member] as ArrayList;
        }

        private void FillFieldInputChannels(Hashtable hash)
        {
            string member = Models.s_FieldInputChannels;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_InputChannels = hash[member] as ArrayList;
        }

        private void FillFieldFIOStructureDescVersion(Hashtable hash)
        {
            string member = Models.s_FieldFioStructureDescVersion;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_FioStructureDescVersion = (uint)hash[member];
        }

        private void FillFieldFIOStructureDescCRC(Hashtable hash)
        {
            string member = Models.s_FieldFioStructureDescCrc;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_FioStructureDescCrc = (uint)hash[member];
        }

        private void FillFieldIOCSLength(Hashtable hash)
        {
            string member = Models.s_FieldIocsLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_IocsLength = (uint)hash[member];
        }

        private void FillFieldIOPSLength(Hashtable hash)
        {
            string member = Models.s_FieldIopsLength;
            if (hash.ContainsKey(member)
                && hash[member] is uint)
                this.m_IopsLength = (uint)hash[member];
        }

        private void FillFieldOutputDataItems(Hashtable hash)
        {
            string member = Models.s_FieldOutputDataItems;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_OutputDataItems = hash[member] as ArrayList;
        }

        private void FillFieldInputDataItems(Hashtable hash)
        {
            string member = Models.s_FieldInputDataItems;
            if (hash.ContainsKey(member)
                && hash[member] is ArrayList)
                this.m_InputDataItems = hash[member] as ArrayList;
        }

        private void FillFieldOutputConsistency(Hashtable hash)
        {
            string member = Models.s_FieldOutputConsistency;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.IOConsistencies)
                this.m_OutputConsistency = (GSDI.IOConsistencies)hash[member];
        }

        private void FillFieldInputConsistency(Hashtable hash)
        {
            string member = Models.s_FieldInputConsistency;
            if (hash.ContainsKey(member)
                && hash[member] is GSDI.IOConsistencies)
                this.m_InputConsistency = (GSDI.IOConsistencies)hash[member];
        }

        /// <summary>
        /// Serializes the object itself and his properties to a fixed XML 
        /// structure.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool Serialize(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // Object begin.
            writer.WriteStartElement(Export.s_ElementObject);
            writer.WriteAttributeString(Export.s_AttributeType, Models.s_ObjectIoData);

            // ----------------------------------------------
            this.SerializeMembers(option, ref writer);

            // Object end.
            writer.WriteEndElement();

            return true;
        }

        /// <summary>
        /// Serializes only the properties of the object to a fixed XML 
        /// structure, without any enclosing element for the object itself.
        /// </summary>
        /// <param name="option">This option specifies, whether the object should
        /// be serialized flat, deep and so on.</param>
        /// <param name="writer">It's an XmlTextWriter, which is used to
        /// write out all relevant xml tags and the needed content data.</param>
        /// <returns>True, if serializing was successfull, else false.</returns>
        internal override bool SerializeMembers(SerializeOptions option, ref System.Xml.XmlTextWriter writer)
        {
            // ----------------------------------------------
            Export.WriteEnumProperty(ref writer, Models.s_FieldInputConsistency, this.m_InputConsistency.ToString(), Export.s_SubtypeIoConsistencies);
            Export.WriteEnumProperty(ref writer, Models.s_FieldOutputConsistency, this.m_OutputConsistency.ToString(), Export.s_SubtypeIoConsistencies);
            Export.WriteUint32Property(ref writer, Models.s_FieldInputLength, this.m_InputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldOutputLength, this.m_OutputLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldIopsLength, this.m_IopsLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldIocsLength, this.m_IocsLength);
            Export.WriteUint32Property(ref writer, Models.s_FieldFioStructureDescCrc, this.m_FioStructureDescCrc);
            Export.WriteUint32Property(ref writer, Models.s_FieldFioStructureDescVersion, this.m_FioStructureDescVersion);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldInputDataItems, this.m_InputDataItems);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldOutputDataItems, this.m_OutputDataItems);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldInputChannels, this.m_InputChannels);
            Export.WriteSimpleObjectListProperty(option, ref writer, Models.s_FieldOutputChannels, this.m_OutputChannels);


            return true;
        }

        #endregion

    }
}


