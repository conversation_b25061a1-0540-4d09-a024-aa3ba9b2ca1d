/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: BusinessLogic                             :C&  */
/*                                                                           */
/*  F i l e               &F: CableData.cs                              :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

#region Used Namespaces

using PNConfigLib.HWCNBL.Utilities.Enums;

#endregion

namespace PNConfigLib.HWCNBL.Common
{
    /// <summary>
    /// Holds the cabledata for a port.
    /// The properties LineLength, SignalDelayTime, LineDelaySelection can be written directly into
    /// to port attributes.
    /// </summary>
    internal class CableData
    {
        /// <summary>
        /// Constructor for CableData that specifies the line length,
        /// signal delay time and cable data edit mode.
        /// </summary>
        /// <param name="lineLength">Line length.</param>
        /// <param name="signalDelayTime">Signal delay time.</param>
        /// <param name="editMode">Cable data edit mode.</param>
        internal CableData(uint lineLength, uint signalDelayTime, CableDataEditMode editMode)
        {
            LineLength = lineLength;
            SignalDelayTime = signalDelayTime;
            CableDataEditMode = editMode;
        }

        /// <summary>
        /// Cable data edit mode.
        /// </summary>
        private CableDataEditMode CableDataEditMode { get; }

        /// <summary>
        /// Cable data edit mode as uint.
        /// </summary>
        public uint LineDelaySelection =>  (uint)CableDataEditMode; 

        /// <summary>
        /// Line length.
        /// </summary>
        public uint LineLength { get; private set; }

        /// <summary>
        /// Signal delay time.
        /// </summary>
        public uint SignalDelayTime { get; private set; }
    }
}