/*****************************************************************************/
/*  Copyright (C) 2024 Siemens Aktiengesellschaft. All rights reserved.      */
/*****************************************************************************/
/*  This program is protected by German copyright law and international      */
/*  treaties. The use of this software including but not limited to its      */
/*  Source Code is subject to restrictions as agreed in the license          */
/*  agreement between you and Siemens.                                       */
/*  Copying or distribution is not allowed unless expressly permitted        */
/*  according to your license agreement with Siemens.                        */
/*****************************************************************************/
/*                                                                           */
/*  P r o j e c t         &P: Basic Components                          :P&  */
/*                                                                           */
/*  P a c k a g e         &W: PROFINET Configuration Library            :W&  */
/*                                                                           */
/*  C o m p o n e n t     &C: ConsistencyChecker                        :C&  */
/*                                                                           */
/*  F i l e               &F: IOSystemChecker.cs                        :F&  */
/*                                                                           */
/*  V e r s i o n         &V: BC_PNConfigLib_P01.05.00.00_00.01.06.57   :V&  */
/*                                                                           */
/*  D a t e  (YYYY-MM-DD) &D: 2024-10-15                                :D&  */
/*                                                                           */
/*****************************************************************************/

using System.Collections.Generic;

using PNConfigLib.ConfigReader.Configuration;
using PNConfigLib.HWCNBL.Constants;

namespace PNConfigLib.Consistency.Checker.ConfigurationFileChecker
{
    class IOSystemChecker : IConsistencyChecker
    {

        private readonly Configuration m_Configuration;

        public IOSystemChecker(Configuration cfg)
        {
            m_Configuration = cfg;
        }

        /// <summary>
        /// Keeps IOSystemIDs that are provided by CentralDeviceChecker.cs
        /// </summary>
        private static List<string> ioSystemIDs = new List<string>();

        public void Check()
        {
            CheckIOSystem();
        }

        private void CheckIOSystem()
        {
            foreach (Subnet subnet in m_Configuration.Subnet)
            {
                List<uint> usedIoSystemNumbers = new List<uint>();

                CheckIOSystemNumbersInSubnet(subnet);

                CheckSubnetIOSystem(subnet, usedIoSystemNumbers);
            }
        }

        private void CheckIOSystemNumbersInSubnet(Subnet subnet)
        {
            if (subnet.IOSystem.Count > 16)
            {
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_MaxIOSystemNumber,
                    subnet.IOSystem.Count,
                    subnet.SubnetID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckSubnetIOSystem(Subnet subnet, List<uint> usedIoSystemNumbers)
        {
            foreach (SubnetIOSystem ioSystem in subnet.IOSystem)
            {
                CheckIOSystemWithController(ioSystem);

                CheckIfMultipleIOSystemUsed(ioSystem, usedIoSystemNumbers);

                if (ioSystem.General.MultipleUseIOSystem)
                {
                    continue;
                }

                CheckIpProtocolForDecentralDevices();
            }
        }

        private void CheckIOSystemWithController(SubnetIOSystem ioSystem)
        {
            if (!ioSystemIDs.Contains(ioSystem.IOSystemID))
            {
                // Check there are no IO systems without an IO controller
                ConsistencyLogger.Log(
                    ConsistencyType.XML,
                    LogSeverity.Error,
                    string.Empty,
                    ConsistencyConstants.XML_IoSystemWithoutIOC,
                    ioSystem.IOSystemID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckIfMultipleIOSystemUsed(SubnetIOSystem ioSystem, List<uint> usedIoSystemNumbers)
        {
            if (ioSystem.General.IOSystemNumberSpecified && usedIoSystemNumbers.Contains(ioSystem.General.IOSystemNumber))
            {
                // Check there are no IO systems with the same IOSystemNumber
                ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_MultipleUseOfIOSystemNumber,
                        ioSystem.General.IOSystemNumber,
                        ioSystem.IOSystemID);
                throw new ConsistencyCheckException();
            }
        }

        private void CheckIpProtocolForDecentralDevices()
        {
            foreach (var device in m_Configuration.Devices.DecentralDevice)
            {
                DecentralIPProtocolType ipProtocol =
                    device.DecentralDeviceInterface.EthernetAddresses.IPProtocol;
                if ((ipProtocol.Item != null)
                    && (ipProtocol.ItemElementName == ItemChoiceType.SetByTheIOController))
                {
                    ConsistencyLogger.Log(
                        ConsistencyType.XML,
                        LogSeverity.Error,
                        string.Empty,
                        ConsistencyConstants.XML_MultipleUseIOSystemIsNotSelected);
                    throw new ConsistencyCheckException();
                }
            }
        }

        internal static List<string> GetIOSystemIDs()
        {
            return ioSystemIDs;
        }
    }
}
